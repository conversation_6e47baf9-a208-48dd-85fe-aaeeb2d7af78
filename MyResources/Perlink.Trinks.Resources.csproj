﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{FC46ABA3-0CB2-48CE-84F7-D9FFD53C1D68}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Perlink.Trinks.Resources</RootNamespace>
    <AssemblyName>Perlink.Trinks.Resources</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Windows Service|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Windows Service\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Tests|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Tests\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugTodosProjetos|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\DebugTodosProjetos\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Areas\AreaPerlink\Controllers\ConsultaDeClientesController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ConsultaDeClientesController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\AreaPerlink\Models\ClienteEstabelecimentoViewModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClienteEstabelecimentoViewModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\AreaPerlink\Views\ConsultaDeClientes\FiltroClienteEstabelecimento.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FiltroClienteEstabelecimento.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\AreaPerlink\Views\Shared\ParteCabecalho1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteCabecalho.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Controllers\MinhaAgendaController.Designer.cs">
      <DependentUpon>MinhaAgendaController.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Models\MinhaAgenda\MinhaAgendaModel.Designer.cs">
      <DependentUpon>MinhaAgendaModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Models\MinhaAgenda\MinhaAgendaFiltrosModel.Designer.cs">
      <DependentUpon>MinhaAgendaFiltrosModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Models\MinhaAgenda\ItemMinhaAgendaModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMinhaAgendaModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Views\MinhaAgenda\ParteTabelaAgendamento.Designer.cs">
      <DependentUpon>ParteTabelaAgendamento.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Views\MinhaAgenda\Index.Designer.cs">
      <DependentUpon>Index.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\AreaProfissional\Views\Shared\ParteCabecalho.Designer.cs">
      <DependentUpon>ParteCabecalho.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\ClienteController.Designer.cs">
      <DependentUpon>ClienteController.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\RelatoriosController.Designer.cs">
      <DependentUpon>RelatoriosController.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\ManterCadastroController.Designer.cs">
      <DependentUpon>ManterCadastroController.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\ProfissionalController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ProfissionalController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\RecorrenciaController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RecorrenciaController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\ServicosController.Designer.cs">
      <DependentUpon>ServicosController.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\TutorialController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TutorialController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ExtensionMethods\ResultadoRelatorioComissoesModelExtension.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ResultadoRelatorioComissoesModelExtension.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ExtensionMethods\ResultadoRelatorioFinanceiroModelExtension.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ResultadoRelatorioFinanceiroModelExtension.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ModelAdapters\CancelarAgendamentoModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CancelarAgendamentoModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ModelAdapters\ManterProfissionalModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ManterProfissionalModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ModelAdapters\Relatorios\RelatorioComissoesModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RelatorioComissoesModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ModelAdapters\Relatorios\RelatorioFinanceiroModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RelatorioFinanceiroModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\ModelAdapters\ServicoModelAdapter.Designer.cs">
      <DependentUpon>ServicoModelAdapter.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\ModelAdapters\CategoriaModelAdapter.Designer.cs">
      <DependentUpon>CategoriaModelAdapter.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\AgendamentoClienteModel.Designer.cs">
      <DependentUpon>AgendamentoClienteModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\AgendamentoModel.Designer.cs">
      <DependentUpon>AgendamentoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\FiltroRelatoriosModel.Designer.cs">
      <DependentUpon>FiltroRelatoriosModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\NotificacaoConfirmacaoModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>NotificacaoConfirmacaoModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\RecorrenciaModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RecorrenciaModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\ServicoModel.Designer.cs">
      <DependentUpon>ServicoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\FotoModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FotoModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\ManterClienteModel.Designer.cs">
      <DependentUpon>ManterClienteModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\ClienteModel.Designer.cs">
      <DependentUpon>ClienteModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\ConfiguracaoModel.Designer.cs">
      <DependentUpon>ConfiguracaoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\DadosGeraisModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DadosGeraisModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\ManterCadastro\Profissional\ServicosModel.Designer.cs">
      <DependentUpon>ServicosModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\ManterCadastro\Profissional\DadosGeraisModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DadosGeraisModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Models\PersonalizacaoModel.Designer.cs">
      <DependentUpon>PersonalizacaoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Agenda\CancelarAgendamentoPopup.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CancelarAgendamentoPopup.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Agenda\ConsultarAgendamentos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ConsultarAgendamentos.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Agenda\VerFechamentoPopup.Designer.cs">
      <DependentUpon>VerFechamentoPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Agenda\HorarioHistoricoPopup.Designer.cs">
      <DependentUpon>HorarioHistoricoPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Profissional\ParteCopiarServicosDeProfissionais.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteCopiarServicosDeProfissionais.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Recorrencia\CancelarRecorrenciaPopup.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CancelarRecorrenciaPopup.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Cliente\ClientesWebPopup.Designer.cs">
      <DependentUpon>ClientesWebPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Configuracao\ExcessoesExibicaoPrecoPopup.Designer.cs">
      <DependentUpon>ExcessoesExibicaoPrecoPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Servicos\EdicaoRapidaPopup.Designer.cs">
      <DependentUpon>EdicaoRapidaPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Servicos\ModalAdicionarServico.Designer.cs" />
    <Compile Include="Areas\BackOffice\Views\Cliente\ListaDeClientes.Designer.cs">
      <DependentUpon>ListaDeClientes.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Cliente\ManterClientePopup.Designer.cs">
      <DependentUpon>ManterClientePopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Agenda\ManterAgendamentoPopup.Designer.cs">
      <DependentUpon>ManterAgendamentoPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Agenda\Index.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Index.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Configuracao\ExcessoesAgendamentoPopup.Designer.cs">
      <DependentUpon>ExcessoesAgendamentoPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\HorariosEspeciais\ListaHorariosEspeciais.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ListaHorariosEspeciais.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\HorariosEspeciais\ManterHorarioEspecialPopup.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ManterHorarioEspecialPopup.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\ManterCadastro\FotosEstabelecimento.Designer.cs">
      <DependentUpon>FotosEstabelecimento.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Foto\ManterFotoPopup.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ManterFotoPopup.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\ManterCadastro\ParteDadosGeraisEstabelecimento.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteDadosGeraisEstabelecimento.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\ManterCadastro\ClienteOffline.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClienteOffline.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Profissional\ParteFoto.Designer.cs">
      <DependentUpon>ParteFoto.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Profissional\ParteManterServicos.Designer.cs">
      <DependentUpon>ParteManterServicos.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Profissional\ParteManterHorarioTrabalho.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteManterHorarioTrabalho.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Relatorios\Financeiro.Designer.cs">
      <DependentUpon>Financeiro.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Relatorios\ResultadoRelatorioFinanceiro.Designer.cs">
      <DependentUpon>ResultadoRelatorioFinanceiro.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Servicos\ModalAdicionarCategoria.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModalAdicionarCategoria.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Servicos\ModalManterCategoria.Designer.cs">
      <DependentUpon>ModalManterCategoria.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Servicos\ModalManterServico.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModalManterServico.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Servicos\ListaDeCategoriasEServicos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ListaDeCategoriasEServicos.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Shared\ParteLogo.Designer.cs">
      <DependentUpon>ParteLogo.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Shared\ParteListaProfissionais.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteListaProfissionais.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Controllers\ComponenteBuscaController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ComponenteBuscaController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\ModelAdapters\ComponenteCaracteristicasFacilidadesModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ComponenteCaracteristicasFacilidadesModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\ModelAdapters\ComponenteHorarioFuncionamentoModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ComponenteHorarioFuncionamentoModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\BuscarModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>BuscarModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ConfirmarAgendamentoModel.Designer.cs">
      <DependentUpon>ConfirmarAgendamentoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuAgendeHorario.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMenuAgendeHorario.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuHome.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMenuHome.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuPacotesPromocionais.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMenuPacotesPromocionais.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuProfissionais.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMenuProfissionais.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuServicos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMenuServicos.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuSobreNos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ItemMenuSobreNos.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteBusca\ConfirmarAgendamento.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ConfirmarAgendamento.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Hotsite\ModelAdapters\BuscarModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>BuscarModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteBusca\ParteBusca.Designer.cs">
      <DependentUpon>ParteBusca.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteCaracteristicasFacilidades\ParteCaracteristicasFacilidades.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteCaracteristicasFacilidades.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteFiltroBusca\ParteFiltroBusca.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteFiltroBusca.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteHorarioFuncionamento\ParteHorarioFuncionamento.Designer.cs">
      <DependentUpon>ParteHorarioFuncionamento.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteQuemSomos\ParteQuemSomos.Designer.cs">
      <DependentUpon>ParteQuemSomos.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteTelefone\ParteTelefone.Designer.cs">
      <DependentUpon>ParteTelefone.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteInstrucoesEspeciais\ParteInstrucoesEspeciais.Designer.cs">
      <DependentUpon>ParteInstrucoesEspeciais.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponenteVitrineServicos\ParteVitrineServicos.Designer.cs">
      <DependentUpon>ParteVitrineServicos.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\ComponentePoliticaCancelamento\PartePoliticaCancelamento.Designer.cs">
      <DependentUpon>PartePoliticaCancelamento.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\Shared\ParteCabecalho.Designer.cs">
      <DependentUpon>ParteCabecalho.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\Shared\Template\0001\index.Designer.cs">
      <DependentUpon>index.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\HotSite\Views\Shared\Template\0001\ParteMenu.Designer.cs">
      <DependentUpon>ParteMenu.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\MinhaArea\Controllers\MeuCadastroController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MeuCadastroController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\MinhaArea\Controllers\MeusCompromissosController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MeusCompromissosController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\MinhaArea\ModelAdapters\FavoritosModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FavoritosModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\MinhaArea\Models\CompromissoModel.Designer.cs">
      <DependentUpon>CompromissoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\MinhaArea\Models\MeuCadastroModel.Designer.cs">
      <DependentUpon>MeuCadastroModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\MinhaArea\Views\Favoritos\Index.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Index.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Views\CentralAtendimento\Breadcrumb.Designer.cs">
      <DependentUpon>Breadcrumb.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\MinhaArea\Views\MeuCadastro\Index.Designer.cs">
      <DependentUpon>Index.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\MinhaArea\Views\Shared\ParteRodape.Designer.cs">
      <DependentUpon>ParteRodape.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\MinhaArea\Views\Shared\ParteCabecalho.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteCabecalho.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Controllers\ClienteController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClienteController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Models\CadastroCliente\ManterClienteModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ManterClienteModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Models\CadastroEstabelecimento\EnderecoEstabelecimentoModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EnderecoEstabelecimentoModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Models\CentralAtendimento\FaleConoscoModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FaleConoscoModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Views\CentralAtendimento\FaleConosco.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FaleConosco.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Views\Cliente\ManterClientePopUp.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ManterClientePopUp.resx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\LoginController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LoginController.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Controllers\AgendaController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AgendaController.resx</DependentUpon>
    </Compile>
    <Compile Include="Disponibilidade\ControleConcorrenciaAgendamento.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ControleConcorrenciaAgendamento.resx</DependentUpon>
    </Compile>
    <Compile Include="ExtensionMethods\StringExtension.Designer.cs">
      <DependentUpon>StringExtension.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="ExtensionMethods\DecimalExtension.Designer.cs">
      <DependentUpon>DecimalExtension.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="MascarasEFormatos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MascarasEFormatos.resx</DependentUpon>
    </Compile>
    <Compile Include="Mensagens.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Mensagens.resx</DependentUpon>
    </Compile>
    <Compile Include="Models\LoginAdministrativoModel.Designer.cs">
      <DependentUpon>LoginAdministrativoModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Models\RecuperacaoSenhaModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RecuperacaoSenhaModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Pessoas\Enums\Sexo.Designer.cs">
      <DependentUpon>Sexo.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Pessoas\Services\ContaService.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ContaService.resx</DependentUpon>
    </Compile>
    <Compile Include="Pessoas\Services\ClienteEstabelecimentoService.Designer.cs">
      <DependentUpon>ClienteEstabelecimentoService.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Pessoas\Services\EstabelecimentoHorarioEspecialFuncionamentoService.Designer.cs">
      <DependentUpon>EstabelecimentoHorarioEspecialFuncionamentoService.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Pessoas\Services\EnvioEmailService.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EnvioEmailService.resx</DependentUpon>
    </Compile>
    <Compile Include="Pessoas\Services\EstabelecimentoService.Designer.cs">
      <DependentUpon>EstabelecimentoService.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Pessoas\Services\FotoService.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FotoService.resx</DependentUpon>
    </Compile>
    <Compile Include="Textos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Textos.resx</DependentUpon>
    </Compile>
    <Compile Include="TratamentoErro\ExceptionHandler.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ExceptionHandler.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Cliente\HistoricoClienteItem.Designer.cs">
      <DependentUpon>HistoricoClienteItem.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Cliente\HistoricoClientePopup.Designer.cs">
      <DependentUpon>HistoricoClientePopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Login\Administrativo.Designer.cs">
      <DependentUpon>Administrativo.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Login\LoginPopup.Designer.cs">
      <DependentUpon>LoginPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Login\IdentifiquesePopup.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>IdentifiquesePopup.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Login\RecuperacaoSenhaPopup1.Designer.cs">
      <DependentUpon>RecuperacaoSenhaPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Login\RecuperacaoSenha.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RecuperacaoSenha.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Shared\Administrativo.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Administrativo.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Shared\Aguarde.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Aguarde.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Shared\ParteCabecalhoLinkAreas.Designer.cs">
      <DependentUpon>ParteCabecalhoLinkAreas.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteCabecalhoLinkAreaProfissional.Designer.cs">
      <DependentUpon>ParteCabecalhoLinkAreaProfissional.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteHorarioFuncionario.Designer.cs">
      <DependentUpon>ParteHorarioFuncionario.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\Paginador.Designer.cs">
      <DependentUpon>Paginador.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\LayoutGeral.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LayoutGeral.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Shared\NotFound.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>NotFound.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Shared\ParteCabecalhoLinkBackoffice.Designer.cs" />
    <Compile Include="Views\Shared\ParteCabecalhoLinkMinhaArea.Designer.cs">
      <DependentUpon>ParteCabecalhoLinkMinhaArea.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteLogin.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteLogin.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\BackOffice\Views\Shared\ParteRodape.Designer.cs">
      <DependentUpon>ParteRodape.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Controllers\CadastroEstabelecimentoControllers.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CadastroEstabelecimentoControllers.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\ModelAdapters\IndexModelAdapter.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>IndexModelAdapter.resx</DependentUpon>
    </Compile>
    <Compile Include="Areas\Portal\Models\CadastroEstabelecimento\ConfirmeModel.Designer.cs">
      <DependentUpon>ConfirmeModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Models\CadastroEstabelecimento\IndexModel.Designer.cs">
      <DependentUpon>IndexModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Views\CadastroEstabelecimento\Sucesso.Designer.cs">
      <DependentUpon>Sucesso.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Views\CadastroEstabelecimento\PlanosAssinaturaPopup.Designer.cs">
      <DependentUpon>PlanosAssinaturaPopup.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Views\CadastroEstabelecimento\ParteRedeFiliais.Designer.cs">
      <DependentUpon>ParteRedeFiliais.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Views\CadastroEstabelecimento\ParteEnderecoEstabelecimento.Designer.cs">
      <DependentUpon>ParteEnderecoEstabelecimento.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Content\js\js\Funcoes.Designer.cs">
      <DependentUpon>Funcoes.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Models\ErrorModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ErrorModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Models\PessoaJuridicaModel.Designer.cs">
      <DependentUpon>PessoaJuridicaModel.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Areas\Portal\Models\CadastroEstabelecimento\ResponsavelEstabelecimentoModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ResponsavelEstabelecimentoModel.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Views\Email\ConfirmacaoCadastro.Designer.cs">
      <DependentUpon>ConfirmacaoCadastro.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteTelefone.Designer.cs">
      <DependentUpon>ParteTelefone.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\CultureManager.Designer.cs">
      <DependentUpon>CultureManager.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\Error.Designer.cs">
      <DependentUpon>Error.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ExibicaoDeErro.Designer.cs">
      <DependentUpon>ExibicaoDeErro.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ExibicaoDeErroEmModal.Designer.cs">
      <DependentUpon>ExibicaoDeErroEmModal.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteCabecalhoPadrao.Designer.cs">
      <DependentUpon>ParteCabecalhoPadrao.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteHorarioFuncionamento.Designer.cs">
      <DependentUpon>ParteHorarioFuncionamento.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Views\Shared\ParteListaTelefone.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteListaTelefone.resx</DependentUpon>
    </Compile>
    <Compile Include="Views\Shared\ParteRodapePadrao.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ParteRodapePadrao.resx</DependentUpon>
    </Compile>
    <Compile Include="WebSupport\Areas\Portal\Controllers\ClienteBaseController.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClienteBaseController.resx</DependentUpon>
    </Compile>
    <Compile Include="WebSupport\Filters\AutorizacaoAcessoAreaPerlink.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AutorizacaoAcessoAreaPerlink.resx</DependentUpon>
    </Compile>
    <Compile Include="WebSupport\Filters\AutorizacaoResponsavelEstabelecimento.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AutorizacaoResponsavelEstabelecimento.resx</DependentUpon>
    </Compile>
    <Compile Include="WebSupport\Filters\AutorizacaoUsuarioEstabelecimento.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AutorizacaoUsuarioEstabelecimento.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Areas\AreaPerlink\Controllers\ConsultaDeClientesController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConsultaDeClientesController.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaPerlink\Models\ClienteEstabelecimentoViewModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteEstabelecimentoViewModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaPerlink\Views\ConsultaDeClientes\FiltroClienteEstabelecimento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FiltroClienteEstabelecimento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaPerlink\Views\Shared\ParteCabecalho.resx">
      <LastGenOutput>ParteCabecalho1.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Controllers\MinhaAgendaController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MinhaAgendaController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Models\MinhaAgenda\MinhaAgendaModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MinhaAgendaModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Models\MinhaAgenda\MinhaAgendaFiltrosModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MinhaAgendaFiltrosModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Models\MinhaAgenda\ItemMinhaAgendaModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMinhaAgendaModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Views\MinhaAgenda\ParteTabelaAgendamento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteTabelaAgendamento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Views\MinhaAgenda\Index.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Index.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\AreaProfissional\Views\Shared\ParteCabecalho.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalho.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\ClienteController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\RelatoriosController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RelatoriosController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\ManterCadastroController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterCadastroController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\ProfissionalController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ProfissionalController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\RecorrenciaController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RecorrenciaController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\ServicosController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ServicosController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\TutorialController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>TutorialController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ExtensionMethods\ResultadoRelatorioComissoesModelExtension.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResultadoRelatorioComissoesModelExtension.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ExtensionMethods\ResultadoRelatorioFinanceiroModelExtension.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResultadoRelatorioFinanceiroModelExtension.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ModelAdapters\CancelarAgendamentoModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CancelarAgendamentoModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ModelAdapters\ManterProfissionalModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterProfissionalModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ModelAdapters\Relatorios\RelatorioComissoesModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RelatorioComissoesModelAdapter.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ModelAdapters\Relatorios\RelatorioFinanceiroModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RelatorioFinanceiroModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ModelAdapters\ServicoModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ServicoModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\ModelAdapters\CategoriaModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CategoriaModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\AgendamentoClienteModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AgendamentoClienteModel.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\AgendamentoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AgendamentoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\FiltroRelatoriosModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FiltroRelatoriosModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\NotificacaoConfirmacaoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>NotificacaoConfirmacaoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\RecorrenciaModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RecorrenciaModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\ServicoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ServicoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\FotoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FotoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\ManterClienteModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterClienteModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\ClienteModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\ConfiguracaoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracaoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\DadosGeraisModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DadosGeraisModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\ManterCadastro\Profissional\ServicosModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ServicosModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\ManterCadastro\Profissional\DadosGeraisModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DadosGeraisModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Models\PersonalizacaoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PersonalizacaoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Agenda\CancelarAgendamentoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CancelarAgendamentoPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Agenda\ConsultarAgendamentos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConsultarAgendamentos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Agenda\VerFechamentoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>VerFechamentoPopup.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Agenda\HorarioHistoricoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>HorarioHistoricoPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Profissional\ParteCopiarServicosDeProfissionais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCopiarServicosDeProfissionais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Recorrencia\CancelarRecorrenciaPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CancelarRecorrenciaPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Cliente\ClientesWebPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClientesWebPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Configuracao\ExcessoesExibicaoPrecoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ExcessoesExibicaoPrecoPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Servicos\EdicaoRapidaPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EdicaoRapidaPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Cliente\ListaDeClientes.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ListaDeClientes.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Cliente\ManterClientePopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterClientePopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Agenda\ManterAgendamentoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterAgendamentoPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Agenda\Index.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Index.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Configuracao\ExcessoesAgendamentoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ExcessoesAgendamentoPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\HorariosEspeciais\ListaHorariosEspeciais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ListaHorariosEspeciais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\HorariosEspeciais\ManterHorarioEspecialPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterHorarioEspecialPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\ManterCadastro\FotosEstabelecimento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FotosEstabelecimento.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Foto\ManterFotoPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterFotoPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\ManterCadastro\ParteDadosGeraisEstabelecimento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteDadosGeraisEstabelecimento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\ManterCadastro\ClienteOffline.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteOffline.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Profissional\ParteFoto.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteFoto.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Profissional\ParteManterServicos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteManterServicos.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Profissional\ParteManterHorarioTrabalho.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteManterHorarioTrabalho.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Relatorios\Financeiro.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Financeiro.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Relatorios\ResultadoRelatorioFinanceiro.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResultadoRelatorioFinanceiro.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Servicos\ModalAdicionarCategoria.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ModalAdicionarCategoria.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Servicos\ModalManterCategoria.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ModalManterCategoria.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Servicos\ModalManterServico.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ModalManterServico.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Servicos\ListaDeCategoriasEServicos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ListaDeCategoriasEServicos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Shared\ParteLogo.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteLogo.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Shared\ParteListaProfissionais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteListaProfissionais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Controllers\ComponenteBuscaController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ComponenteBuscaController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\ModelAdapters\ComponenteCaracteristicasFacilidadesModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ComponenteCaracteristicasFacilidadesModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\ModelAdapters\ComponenteHorarioFuncionamentoModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ComponenteHorarioFuncionamentoModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\BuscarModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>BuscarModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ConfirmarAgendamentoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfirmarAgendamentoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuAgendeHorario.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMenuAgendeHorario.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuHome.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMenuHome.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuPacotesPromocionais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMenuPacotesPromocionais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuProfissionais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMenuProfissionais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuServicos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMenuServicos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Models\ItemMenuPadrao\ItemMenuSobreNos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ItemMenuSobreNos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteBusca\ConfirmarAgendamento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfirmarAgendamento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Hotsite\ModelAdapters\BuscarModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>BuscarModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteBusca\ParteBusca.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteBusca.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteCaracteristicasFacilidades\ParteCaracteristicasFacilidades.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCaracteristicasFacilidades.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteFiltroBusca\ParteFiltroBusca.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteFiltroBusca.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteHorarioFuncionamento\ParteHorarioFuncionamento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteHorarioFuncionamento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteQuemSomos\ParteQuemSomos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteQuemSomos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteTelefone\ParteTelefone.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteTelefone.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteInstrucoesEspeciais\ParteInstrucoesEspeciais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteInstrucoesEspeciais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponenteVitrineServicos\ParteVitrineServicos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteVitrineServicos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\ComponentePoliticaCancelamento\PartePoliticaCancelamento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PartePoliticaCancelamento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\Shared\ParteCabecalho.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalho.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\Shared\Template\0001\index.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>index.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\HotSite\Views\Shared\Template\0001\ParteMenu.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteMenu.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Controllers\MeuCadastroController.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>MeuCadastroController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Controllers\MeusCompromissosController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MeusCompromissosController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\ModelAdapters\FavoritosModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FavoritosModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Models\CompromissoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CompromissoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Models\MeuCadastroModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MeuCadastroModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Views\Favoritos\Index.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Index.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\CentralAtendimento\Breadcrumb.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Breadcrumb.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Views\MeuCadastro\Index.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Index.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Views\Shared\ParteRodape.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteRodape.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\MinhaArea\Views\Shared\ParteCabecalho.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalho.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Controllers\ClienteController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Models\CadastroCliente\ManterClienteModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterClienteModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Models\CadastroEstabelecimento\EnderecoEstabelecimentoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EnderecoEstabelecimentoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Models\CentralAtendimento\FaleConoscoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FaleConoscoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\CentralAtendimento\FaleConosco.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FaleConosco.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\Cliente\ManterClientePopUp.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ManterClientePopUp.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Controllers\LoginController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Disponibilidade\ControleConcorrenciaAgendamento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ControleConcorrenciaAgendamento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionMethods\StringExtension.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>StringExtension.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ExtensionMethods\DecimalExtension.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DecimalExtension.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="MascarasEFormatos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MascarasEFormatos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Mensagens.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Mensagens.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Models\LoginAdministrativoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginAdministrativoModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Models\RecuperacaoSenhaModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RecuperacaoSenhaModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Pessoas\Enums\Sexo.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Sexo.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Pessoas\Services\ContaService.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContaService.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Pessoas\Services\ClienteEstabelecimentoService.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteEstabelecimentoService.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Pessoas\Services\EnvioEmailService.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EnvioEmailService.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Pessoas\Services\EstabelecimentoHorarioEspecialFuncionamentoService.resx" />
    <EmbeddedResource Include="Pessoas\Services\EstabelecimentoService.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>EstabelecimentoService.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Pessoas\Services\FotoService.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FotoService.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="TemplatesEmail\AgendamentoCancelado.cshtml" />
    <EmbeddedResource Include="Textos.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Textos.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="TratamentoErro\ExceptionHandler.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ExceptionHandler.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Cliente\HistoricoClienteItem.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>HistoricoClienteItem.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Cliente\HistoricoClientePopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>HistoricoClientePopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Login\Administrativo.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Administrativo.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Login\LoginPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Login\IdentifiquesePopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>IdentifiquesePopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Login\RecuperacaoSenhaPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RecuperacaoSenhaPopup1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Login\RecuperacaoSenha.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RecuperacaoSenha.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\Administrativo.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Administrativo.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\Aguarde.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Aguarde.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteCabecalhoLinkAreas.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalhoLinkAreas.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteCabecalhoLinkAreaProfissional.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalhoLinkAreaProfissional.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteHorarioFuncionario.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteHorarioFuncionario.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\Paginador.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Paginador.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\LayoutGeral.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LayoutGeral.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\NotFound.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>NotFound.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteCabecalhoLinkBackoffice.resx" />
    <EmbeddedResource Include="Views\Shared\ParteCabecalhoLinkMinhaArea.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalhoLinkMinhaArea.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteLogin.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteLogin.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Controllers\AgendaController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AgendaController.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\BackOffice\Views\Shared\ParteRodape.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteRodape.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Controllers\CadastroEstabelecimentoControllers.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CadastroEstabelecimentoControllers.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\ModelAdapters\IndexModelAdapter.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>IndexModelAdapter.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Models\CadastroEstabelecimento\ConfirmeModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfirmeModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Models\CadastroEstabelecimento\IndexModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>IndexModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\CadastroEstabelecimento\Sucesso.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Sucesso.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\CadastroEstabelecimento\PlanosAssinaturaPopup.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PlanosAssinaturaPopup.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\CadastroEstabelecimento\ParteRedeFiliais.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteRedeFiliais.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Views\CadastroEstabelecimento\ParteEnderecoEstabelecimento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteEnderecoEstabelecimento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Content\js\js\Funcoes.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Funcoes.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Models\ErrorModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ErrorModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Models\PessoaJuridicaModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PessoaJuridicaModel.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Areas\Portal\Models\CadastroEstabelecimento\ResponsavelEstabelecimentoModel.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResponsavelEstabelecimentoModel.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources.en-us.resx" />
    <EmbeddedResource Include="Views\Email\ConfirmacaoCadastro.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfirmacaoCadastro.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteTelefone.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteTelefone.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\CultureManager.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>CultureManager.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\Error.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Error.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ExibicaoDeErro.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ExibicaoDeErro.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ExibicaoDeErroEmModal.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ExibicaoDeErroEmModal.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteCabecalhoPadrao.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteCabecalhoPadrao.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteHorarioFuncionamento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteHorarioFuncionamento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteListaTelefone.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteListaTelefone.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Shared\ParteRodapePadrao.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ParteRodapePadrao.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="WebSupport\Areas\Portal\Controllers\ClienteBaseController.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ClienteBaseController.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="WebSupport\Filters\AutorizacaoAcessoAreaPerlink.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AutorizacaoAcessoAreaPerlink.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="WebSupport\Filters\AutorizacaoResponsavelEstabelecimento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AutorizacaoResponsavelEstabelecimento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="WebSupport\Filters\AutorizacaoUsuarioEstabelecimento.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AutorizacaoUsuarioEstabelecimento.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Views\Agenda\" />
    <Folder Include="Views\Home\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\AgendamentoClienteFaltou.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AgendamentoConfirmado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AgendamentoFinalizado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AgendamentoRecorrencia.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AlteracaoDadosAgendamento.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AlteracaoNomeServicoCategoriaPadrao.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AlteracaoStatusAgendamento.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\AssinaturaCancelada.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\CadastroProfissionalRealizado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\CancelamentoRecorrencia.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\CheckoutRealizado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ClienteBalcaoCadastrado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ClientesComAgendamentoNoDiaSeguinte.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ClienteWebBoasVindas.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ConfirmacaoCadastro.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ContaBloqueada.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ContaFinanceiraAExpirar.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ContaFinanceiraAExpirarUltimoDia.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ContaFinanceiraNaoAssinanteExpiradaA10Dias.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\EstabelecimentoIndicado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\EstatisticasSobreUsoDoSistema.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\FaleConoscoCliente.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\FaleConoscoTrinks.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\InformacoesAgendamento.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ItemAgendamentoMarcado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\LembreteAgendamentosMarcados.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ManterAgendamentoEmLote.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\NovoEstabelecimentoCadastrado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\NovoServicoOuCategoriaNaoPadrao.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\PagamentoDaFaturaAprovado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\PagamentoDaFaturaNaoAutorizado.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ParteServicosRealizados.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\ReativacaoConta.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\RecuperacaoSenha.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\SenhaAlterada.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\SenhaAlteradaAdministrativo.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\SolicitacaoEstabelecimentoAparecerNaBuscaPortal.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\SolicitacaoEstabelecimentoAparecerNaBuscaPortalParaCliente.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\AtivacaoCadastroCliente.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\AtivacaoCadastroClientePorAgendamentoHotsite.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\AgendamentosDeAmanha.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ModeloRelatorio\FechamentoMensal01.xslt">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\CEPSemBairro.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\DespesasDoEstabelecimento.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\CancelamentoDeAgendamentos.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EstabelecimentoPreCadastro.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PagamentoAguardandoConfirmacao.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PagamentoFaturaNaoRecebido.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\TabelaAlteracaoDadosAgendamento.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailNFC.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ParteInformacoesCliente.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ParteServicosImpressaoNFC.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailCancelamentoNFC.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\RPSLoteOperacao.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\MarketingCampanhaNaoEnviadaPorFaltaDeCredito.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailMarketing.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailBrindeSMSEmailMarketing.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\MarketingCampanhaNaoEnviadaPorSituacaoFinanceiraIrregular.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailMarketingSemLogo.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\AtivacaoCadastroNovo.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\InformePagamentoBoletoSegundaVezParaFinanceiro.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailAvisoCartaoProximoAExpirar.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\FaturaGerada.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ProgramaDeFidelidadePontosExpirandoNoPrazo.html" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ProgramaDeFidelidadePontosExpirandoNoDia.html" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ProgramaDeFidelidadePontosGanhos.html" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ConteudoEmailMarketingProgramaDeFidelidade.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailErroEmissaoNFC.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ResumoDosTotaisDosAgendamentos.html" />
    <EmbeddedResource Include="TemplatesEmail\ResumoFechamento.html" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ResumoFechamentoAgendamentosFuturos.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\MarketingCampanhaNaoEnviadaPorProblemaNoEnvio.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\AlertaFaltaDeCreditoParaEnvioConvitedeRetorno.cshtml" />
    <EmbeddedResource Include="TemplatesEmail\MarketingConviteRetornoNaoEnviadaPorFaltaDeCredito.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ConviteAvaliacaoEstabelecimento.html" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailControleDespesa.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\_ParteEmailControleEstoque.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ComprovantePagamentoBelezinha.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EnvioDePedidoDeProdutoParaFranqueador.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\CancelamentoDePedidoDeProdutoParaFranqueador.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ParteResumoFinanceiroDoCaixa.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PagamentoAntecipado\PagamentoEfetuado.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ParteImprimirProfissional.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PlanilhaDePagamentoParceiro.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PromotoresDoTrinks\IndicadoQueAssinou.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PromotoresDoTrinks\IndicadoQueSeCadastrou.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PromotoresDoTrinks\Profissional_IndicadoQueAssinou.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\PromotoresDoTrinks\Profissional_IndicadoQueSeCadastrou.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\FormulariosDinamicos\CopiaFormularioRespondidoParaCliente.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\Shared\LayoutPadraoParaClienteEstabelecimento.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\FormulariosDinamicos\SolicitacaoDeAssinatura.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="TemplatesEmail\img\btn-assinar-email.png" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ImportacaoDeDados\ImportacaoFinalizada.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\Shared\LayoutPadraoParaProfissionalEstabelecimento.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ComunidadeTrinks\AgradecimentoEnvioDeSugestao.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\Shared\LayoutPadraoParaUsuarioWeb.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\VerificacaoDeIdentidade\CodigoDeVerificacao.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\LinkPagamento\ComprovantePagamento.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\LinkPagamento\ComprovanteDePagamentoDoClubeDeAssinaturas.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\EmailControleEstoque.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\LinkPagamento\ComprovanteDePagamentoVendaPacoteHotsite.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\LinkPagamento\NoficacoVendaPacoteHotsiteParaRecebedor.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\RotinaMensagensWhatsApp\CompraNaoAutorizada.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\RotinaMensagensWhatsApp\CompraAutorizada.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\Whatsapp\AvisoSaldo.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ClubeDeAssinaturas\ContratoDeAdesaoPendente.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TemplatesEmail\ClubeDeAssinaturas\AssinaturaEditada.cshtml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>