﻿@using Perlink.Trinks.ExtensionMethods
@using Perlink.Trinks.Resources
@model Perlink.Trinks.Financeiro.DTO.RelatorioComissaoProfissionalDTO

@*@{
            Layout = "~/Areas/BackOffice/Views/Shared/LayoutBackOfficeRelatorio.cshtml";
    }*@

@*@if (Model.EstabelecimentoAutenticado.Logo().FoiAtribuidoUmaImagem()) {
        <div id="logo-estabelecimento" style="position: relative;float: left;margin: -10px 0;height: 170px;">
            <img src="@Model.LogoCaminhoWeb" />
        </div>
    }*@



<div style="text-align: center;">

    <span style="margin:20px 0 40px;font-size: 10px;font-weight:bold;">@Model.NomeEstabelecimento</span><br /><br />
    <span style="font-size:10px;font-weight:bold;">RESUMO FINANCEIRO</span><br />
    <span style="margin: 1mm 0;font-size:10px;font-weight:bold;">@Model.NomeProfissional</span><br />
    <span style="font-size:10px;">@(Model.TipoData == 1 ? "Período de Atendimento/Venda" : Model.TipoData == 5 ? "Período de Liberação do Valor Profissional" : "Período de Pagamento"): @Model.DataInicio.ToShortDateString() a @Model.DataFim.ToShortDateString()</span>
    <br />
    <br />
</div>

@{
    var possuiComissaoServico = Model.ComissaoServicosLista.Any();
    var possuiComissaoProduto = Model.ComissaoProdutosLista.Any();
    var possuiComissaoPacote = Model.ComissaoPacotesLista.Any();
    var possuiGorjeta = Model.GorjetasLista.Any();
    var exibeGorjeta = Model.ExibeGorjeta;
    var exibirCampoAssinaturaProfissional = Model.ExibirCampoAssinaturaProfissional;
    var impressaoEhAgrupado = Model.TipoDeImpressao == Perlink.Trinks.Financeiro.Enums.TipoImpressaoComissaoEnum.Agrupados;
    ViewBag.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit = Model.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit;
    ViewBag.PossuiCustoOperacionalHabilitado = Model.PodeExibirColunaRateio;
    var ehDetalhado = Model.TipoDeImpressao == Perlink.Trinks.Financeiro.Enums.TipoImpressaoComissaoEnum.Detalhados;

}
@if (possuiComissaoServico || possuiComissaoProduto) {
    <span style="font-weight:bold;font-size:10px;">DESCRITIVO DAS RECEITAS VARIÁVEIS NO PERÍODO</span>
    <br />
    <br />
}

@if (possuiComissaoServico) {
    if (impressaoEhAgrupado) {
        <h1 style="font-size:10px;"><i>Sobre Serviços</i></h1>
        <br />
        <table width="100%" border="1">
            <tr style="font-size:10px;font-weight:bold;text-align:center;" bgcolor="#ddd">
                <th>@Textos.Servico</th>
                <th>@Textos.Quantidade</th>
                @if (Model.ComissaoServicosLista.FirstOrDefault().PodeExibirValoresDeServicoEhProduto) {
                    <th>Valor em Serviços R$</th>
                }
                <th>Valor Profissional R$</th>
            </tr>
            @foreach (var i in Model.ComissaoServicosLista) {
                <tr style="margin:5px;font-size:10px;">
                    <td>@i.Nome</td>
                    <td style="text-align:center;">@i.Quantidade</td>
                    @if (i.PodeExibirValoresDeServicoEhProduto) {
                        <td style="text-align:right;">@i.ValorPago.ValorDecimal()</td>
                    }
                    <td style="text-align:right;">@i.Comissao.ValorDecimal()</td>
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                <td style="text-align:right;">Total</td>
                <td style="text-align:center;">@Model.ComissaoServicosLista.Sum(f => f.Quantidade)</td>
                @if (Model.ComissaoServicosLista.FirstOrDefault().PodeExibirValoresDeServicoEhProduto) {
                    <td style="text-align:right;">@Model.ComissaoServicosLista.Sum(f => f.ValorPago).ValorDecimal()</td>
                }
                <td style="text-align:right;">@Model.ComissaoServicosLista.Sum(f => f.Comissao).ValorDecimal()</td>
            </tr>
        </table>
    }
    else {
        <h1 style="font-size:10px;">
            <i>Sobre Serviços</i>
        </h1>
        <br />
        <table border="1">
            <tr style="font-size:10px;font-weight:bold;text-align:center;" bgcolor="#ddd">
                <th>Data do Atendimento</th>
                <th>Data do Pagamento</th>
                <th>Liberação do Valor Profissional</th>
                <th>Cliente</th>
                <th>@Textos.Servico</th>
                @if (Model.ComissaoServicosLista.FirstOrDefault().PodeExibirValoresDeServicoEhProduto) {
                    <th>Valor R$</th>
                }
                <th>Forma de Pagamento</th>
                @if (ViewBag.PossuiCustoOperacionalHabilitado) {
                    <th>Rateio %</th>
                }
                <th>Valor Profissional R$</th>
                @if (ViewBag.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit) {
                    <th>Split</th>
                }

            </tr>
            @foreach (var i in Model.ComissaoServicosLista) {
                <tr style="font-size:10px;">
                    <td style="text-align:center;">@i.DataAtendimento.ToShortDateString()</td>
                    <td style="text-align:center;">@i.DataPagamento.ToShortDateString()</td>
                    <td style="text-align:center;">@i.DataLiberacaoPagamento.ToShortDateString()</td>
                    <td>@i.NomeCliente</td>
                    <td width="700">
                        @i.Nome
                        @if (i.ComoAssistente) {
                            <span>(como assistente)</span>
                        }
                        @if (i.TeveAssistente && !i.ComoAssistente) {
                            <br />
                            <span>Assistente: @i.ApelidoOuNomeAssistente</span>
                        }
                    </td>
                    @if (Model.ComissaoServicosLista.FirstOrDefault().PodeExibirValoresDeServicoEhProduto) {
                        <td>
                            @i.ValorPago.ValorDecimal()


                            @if (i.PossuiComissaoParcelada) {
                                <br /><span>Proporcional<br />@i.ValorProporcional.ValorDecimal()</span>
                            }
                        </td>
                    }
                    <td style="margin:5px;font-size:10px;">@i.FormaDePagamento</td>
                    @if (ViewBag.PossuiCustoOperacionalHabilitado) {
                        <td style="font-size:10px;text-align:right;">@i.Rateio</td>
                    }
                    <td style="text-align:right;">@i.Comissao.ValorDecimal()</td>
                    @if (ViewBag.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit) {
                        <td style="font-size:10px;text-align:right;">
                            @if (i.EhComissaoComSplit) {
                                <span>Sim</span>
                            } 
                            else {
                                <span>Não</span>
                            }
                        </td>
                    }
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                @if (Model.ComissaoServicosLista.FirstOrDefault().PodeExibirValoresDeServicoEhProduto) {
                    <td colspan="7" style="text-align:right;">Total</td>
                } else {
                    <td colspan="6" style="text-align:right;">Total</td>
                }
                @if (Model.PodeExibirColunaRateio) {
                    <td></td>
                }
                <td style="text-align:right;">@Model.ComissaoServicosLista.Sum(f => f.Comissao).ValorDecimal()</td>
                @if (ViewBag.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit) {
                    <td></td>
                }
            </tr>
        </table>


    }
}
<br />
<br />
<br />
@if (possuiComissaoProduto) {
    <h4 style="font-size:10px;"><i>Sobre Produtos Vendidos</i></h4>
    <table class="grid" width="100%" border="1">
        <tr style="font-size:10px;font-weight:bold;text-align: center; " bgcolor="#ddd">
            <th>@Textos.Produto</th>
            <th>@Textos.Quantidade</th>
            @if (Model.EstabelecimentoAutenticado.PodeExibirValoresDeServicoEhProduto()) {
                <th>Valor em Produtos R$</th>
            }
            @if (ehDetalhado && ViewBag.PossuiCustoOperacionalHabilitado) {
                <th>Rateio %</th>
            }
            <th>Valor Profissional R$</th>
            @if (ehDetalhado) {
                <th>Data da venda</th>
                <th>Nome do cliente</th>
            }

        </tr>
        @foreach (var i in Model.ComissaoProdutosLista) {
            <tr style="font-size:10px;">
                <td>@i.Nome</td>
                <td style="text-align:center;">@i.TextoQuantidade</td>
                @if (Model.EstabelecimentoAutenticado.PodeExibirValoresDeServicoEhProduto()) {
                    <td style="text-align:right;">@i.ValorPago.ValorDecimal()</td>
                }
                @if (ehDetalhado && ViewBag.PossuiCustoOperacionalHabilitado) {
                    <td style="text-align:right;">@i.Rateio</td>
                }
                <td style="text-align:right;">@i.Comissao.ValorDecimal()</td>
                @if (ehDetalhado) {
                    <td style="text-align:center;">@i.DataVenda.ToShortDateString()</td>
                    <td>@i.NomeCliente</td>
                }

            </tr>
        }
        <tr style="font-size:10px;font-weight:bold;">
            <td style="text-align:right;">Total</td>
            <td style="text-align: center;">@Model.TextoTotalComissaoProdutos</td>
            @if (Model.EstabelecimentoAutenticado.PodeExibirValoresDeServicoEhProduto()) {
                <td style="text-align:right;">@Model.ComissaoProdutosLista.Sum(f => f.ValorPago).ValorDecimal()</td>
            }
            @if (ehDetalhado && ViewBag.PossuiCustoOperacionalHabilitado) {
                <td></td>
            }
            <td style="text-align:right;">@Model.ComissaoProdutosLista.Sum(f => f.Comissao).ValorDecimal()</td>
            @if (ehDetalhado) {
                <td></td>
                <td></td>
            }

        </tr>
    </table>
}

<br />
<br />
<br />
@if (possuiComissaoPacote) {
    <h4 style="font-size:10px;"><i>Sobre Pacotes Vendidos</i></h4>
    <table class="grid" width="100%" border="1">
        <tr style="font-size:10px;font-weight:bold;text-align: center; " bgcolor="#ddd">
            <th>@Textos.Pacote</th>
            @if (!ehDetalhado) {
                <th>@Textos.Quantidade</th>
            }
            @if (Model.EstabelecimentoAutenticado.PodeExibirValoresDeServicoEhProduto()) {
                <th>Valor em Pacote R$</th>
            }
            @if (ehDetalhado && ViewBag.PossuiCustoOperacionalHabilitado) {
                <th>Rateio %</th>
            }
            <th>Valor Profissional R$</th>
            @if (ehDetalhado) {
                <th>Data da venda</th>
                <th>Nome do cliente</th>
            }

        </tr>
        @foreach (var i in Model.ComissaoPacotesLista) {
            <tr style="font-size:10px;">
                <td>@i.Nome</td>
                @if (!ehDetalhado) {
                    <td style="text-align:center;">@i.Quantidade</td>
                }
                @if (Model.EstabelecimentoAutenticado.PodeExibirValoresDeServicoEhProduto()) {
                    <td style="text-align:right;">@i.ValorPago.ValorDecimal()</td>
                }
                @if (ehDetalhado && ViewBag.PossuiCustoOperacionalHabilitado) {
                    <td style="text-align:right;">@i.Rateio</td>
                }
                <td style="text-align:right;">@i.Comissao.ValorDecimal()</td>
                @if (ehDetalhado) {
                    <td style="text-align:center;">@i.DataVenda.ToShortDateString()</td>
                    <td>@i.NomeCliente</td>
                }

            </tr>
        }
        <tr style="font-size:10px;font-weight:bold;">
            <td style="text-align:right;">Total</td>
            @if (!ehDetalhado) {
                <td style="text-align: center;">@Model.ComissaoPacotesLista.Sum(f => f.Quantidade)</td>
            }
            @if (Model.EstabelecimentoAutenticado.PodeExibirValoresDeServicoEhProduto()) {
                <td style="text-align:right;">@Model.ComissaoPacotesLista.Sum(f => f.ValorPago).ValorDecimal()</td>
            }
            @if (ehDetalhado && ViewBag.PossuiCustoOperacionalHabilitado) {
                <td></td>
            }
            <td style="text-align:right;">@Model.ComissaoPacotesLista.Sum(f => f.Comissao).ValorDecimal()</td>
            @if (ehDetalhado) {
                <td></td>
                <td></td>
            }

        </tr>
    </table>
}
<br />
<br />
<br />
@if (exibeGorjeta) {
    if (possuiGorjeta) {
        <h4 style="font-size:10px;"><i>Gorjetas</i></h4>
        <table class="grid" width="100%" border="1">
            <tr style="font-size:10px;font-weight:bold;text-align: center; " bgcolor="#ddd">
                <th>Data de atendimento</th>
                @if (ehDetalhado) {
                    <th>Cliente</th>
                    <th>Data de pagamento</th>
                    <th>Status</th>
                }
                <th>Valor</th>
            </tr>
            @foreach (var g in Model.GorjetasLista) {
                <tr style="font-size:10px;">
                    <td>@g.Data.ToShortDateString()</td>
                    @if (ehDetalhado) {
                        <td>@g.Cliente.Cliente.PessoaFisica.NomeOuApelido()</td>
                        if (@g.DataPagamento.ToBrazilianShortDateString() != "") {
                            <td>@g.DataPagamento.ToBrazilianShortDateString()</td>
                        }
                        else {
                            <td>-</td>
                        }
                        if (g.StatusGorjeta == 0) {
                            <td>Não Pago</td>
                        }
                        else {
                            <td>@g.StatusGorjeta.ToString()</td>
                        }
                    }
                    <td class="valorGrande" style="text-align:right">@g.Valor.ValorDecimal()</td>
                </tr>
            }

        </table>
    }
}
<br />
<br />
<br />

@{
    var possuiVale = Model.ValesLista.Any();
    var possuiCompraProduto = Model.CompraProdutoLista.Any();
    var possuiBonificacoes = Model.BonificacoesLista.Any();
    var habilitouDespesasPersonalizadas = Model.HabilitouDespesasPersonalizadas;
}
@if (possuiVale || possuiCompraProduto || possuiBonificacoes) {
    <span style="font-size:10px;font-weight:bold;">DESCRITIVO DOS DESCONTOS VARIÁVEIS NO PERÍODO</span>

    if (possuiVale && habilitouDespesasPersonalizadas)
    {
        <br />
        <br />
        <br />
        <h4 style="font-size:10px;"><i>Abatimentos</i></h4>
        <table width="100%" border="1">
            <tr style="font-size:10px;font-weight:bold;text-align:center;" bgcolor="#ddd">
                <th>Data</th>
                <th>Tipo de Abatimento</th>
                <th>Descrição/Observação</th>
                <th class="valorGrande" style="text-wrap:nowrap;padding-block:16px">
                    Valor a Descontar R$
                </th>
            </tr>
            @foreach (var v in Model.ValesLista) {
                <tr style="font-size:10px;">
                    <td style="text-align:center;">@v.Data.ToShortDateString()</td>
                    <td>@v.TipoNome</td>
                    <td>@v.Descricao</td>
                    <td style="text-align:right;">@v.Valor.ValorDecimal()</td>
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                <td></td>
                <td style="text-align:right;" colspan="2">Total</td>
                <td style="text-align:right;">@Model.ValesLista.Sum(f => f.Valor).ValorDecimal()</td>
            </tr>
        </table>
    }

    if (possuiVale && !habilitouDespesasPersonalizadas)
    {
        <br />
        <br />
        <br />
        <h4 style="font-size:10px;"><i>Vales / Adiantamento</i></h4>
        <table width="100%" border="1">
            <tr style="font-size:10px;font-weight:bold;text-align:center;" bgcolor="#ddd">
                <th>Data</th>
                <th>Valor do<br />Vale R$</th>
            </tr>
            @foreach (var v in Model.ValesLista)
            {
                <tr style="font-size:10px;">
                    @if (v.Descricao != "-")
                    {
                        <td style="text-align:center;">@v.Data.ToShortDateString() - @v.Descricao</td>
                    }
                    else
                    {
                        <td style="text-align:center;">@v.Data.ToShortDateString()</td>
                    }
                    <td style="text-align:right;">@v.Valor.ValorDecimal()</td>
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                <td style="text-align:right;">Total</td>
                <td style="text-align:right;">@Model.ValesLista.Sum(f => f.Valor).ValorDecimal()</td>
            </tr>
        </table>
    }

    if (possuiCompraProduto) {
        <br />
        <br />
        <br />
        <h4 style="font-size:10px"><i>Produtos Comprados/Usados</i></h4>
        <table width="100%" border="1">
            <tr style="text-align:center;font-size:10px;font-weight:bold;" bgcolor="#ddd">
                <th>@Textos.Produto</th>
                <th>@Textos.Quantidade</th>
                <th>Valor a Descontar R$</th>
                @if (ehDetalhado) {
                    <th>Data de Consumo</th>
                }

            </tr>
            @foreach (var i in Model.CompraProdutoLista) {
                <tr style="font-size:10px;">
                    <td>@i.Nome</td>
                    <td style="text-align:center;">@i.TextoQuantidade</td>
                    <td style="text-align:right;">@i.ValorPago.ValorDecimal()</td>
                    @if (ehDetalhado) {
                        <td style="text-align:center;">@i.DataVenda.ToShortDateString()</td>
                    }
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                <td style="text-align:right;">Total</td>
                <td style="text-align:center;">@Model.TextoTotalCompraProdutos</td>
                <td style="text-align:right;">@Model.CompraProdutoLista.Sum(f => f.ValorPago).ValorDecimal()</td>
                @if (ehDetalhado) {
                    <td></td>
                }
            </tr>
        </table>
    }

    if (possuiBonificacoes && habilitouDespesasPersonalizadas)
    {
        <br />
        <br />
        <br />
        <h4 style="font-size:10px;"><i>Recebíveis</i></h4>
        <table width="100%" border="1">
            <tr style="font-size:10px;font-weight:bold;text-align:center;" bgcolor="#ddd">
                <th>Data</th>
                <th>Tipo de Recebível</th>
                <th>Descrição/Observação</th>
                <th class="valorGrande" style="text-wrap:nowrap;padding-block:16px">
                    Valor do Recebível R$
                </th>
            </tr>
            @foreach (var v in Model.BonificacoesLista) {
                <tr style="font-size:10px;">
                    <td style="text-align:center;">@v.Data.ToShortDateString()</td>
                    <td>@v.TipoNome</td>
                    <td>@v.Descricao</td>
                    <td style="text-align:right;">@v.Valor.ValorDecimal()</td>
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                <td></td>
                <td style="text-align:right;" colspan="2">Total</td>
                <td style="text-align:right;">@Model.BonificacoesLista.Sum(f => f.Valor).ValorDecimal()</td>
            </tr>
        </table>
    }

    if (possuiBonificacoes && !habilitouDespesasPersonalizadas)
    {
        <br />
        <br />
        <br />
        <h4 style="font-size:10px;"><i>Bonificações</i></h4>
        <table width="100%" border="1">
            <tr style="font-size:10px;font-weight:bold;text-align:center;" bgcolor="#ddd">
                <th>Data</th>
                <th>Valor do<br />Bônus R$</th>
            </tr>
            @foreach (var v in Model.BonificacoesLista)
            {
                <tr style="font-size:10px;">
                    @if (v.Descricao != "-")
                    {
                        <td style="text-align:center;">@v.Data.ToShortDateString() - @v.Descricao</td>
                    }
                    else
                    {
                        <td style="text-align:center;">@v.Data.ToShortDateString()</td>
                    }   
                    <td style="text-align:right;">@v.Valor.ValorDecimal()</td>
                </tr>
            }
            <tr style="font-size:10px;font-weight:bold;">
                <td style="text-align:right;">Total</td>
                <td style="text-align:right;">@Model.BonificacoesLista.Sum(f => f.Valor).ValorDecimal()</td>
            </tr>
        </table>
    }
}
<br />
<br />
<br />

<h3 style="font-size:10px;font-weight:bold;" cellspacing="0" cellpadding="0">RESUMO</h3>
<br />
<table border="1" style="font-size:10px;" width="100%">
    <tr bgcolor="#ddd" style="font-weight:bold;text-align: center;">
        <th>@Textos.Recebimentos</th>
        <th>@Textos.Descontos</th>
    </tr>
    <tr style="font-size:10px;">
        <td valign="top">
            <table border="0" width="100%">
                <tr>
                    <td>@Textos.ComissaoServicos</td>
                    <td style="text-align:right;">@Model.ComissaoServicos.ValorDecimal()</td>
                </tr>
                <tr>
                    <td>@Textos.ComissaoProdutos</td>
                    <td style="text-align:right;">@Model.ComissaoProdutos.ValorDecimal()</td>
                </tr>
                <tr>
                    <td>Sobre Pacote</td>
                    <td style="text-align:right;">@Model.ComissaoPacotes.ValorDecimal()</td>
                </tr>
                <tr>
                    <td>
                        @if (habilitouDespesasPersonalizadas)
                        {
                            <span>Recebíveis</span>
                        }
                        else
                        {
                            <span>Bonificações</span>
                        }
                    </td>
                    <td style="text-align:right;">@Model.Bonificacoes.ValorDecimal()</td>
                </tr>
                <tr>
                    @if (exibeGorjeta) {
                        <td>Gorjetas</td>
                        <td style="text-align:right;">@Model.Gorjetas.ValorDecimal()</td>
                    }
                </tr>
            </table>
        </td>
        <td valign="top">
            <table border="0" width="100%">
                <tr>
                    <td>
                        @if (habilitouDespesasPersonalizadas)
                        {
                            <span>Abatimentos</span>
                        }
                        else
                        {
                            <span>@Textos.Vales / @Textos.Adiantamento</span>
                        }
                    </td>
                    <td style="text-align:right;">@Model.Vales.ValorDecimal()</td>
                </tr>
                <tr>
                    <td>@Textos.CompraProdutos</td>
                    <td style="text-align:right;">@Model.CompraProduto.ValorDecimal()</td>
                </tr>
                @if (Model.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit) {
                    <tr>
                        <td>Pagamento de Split</td>
                        <td style="text-align:right;">@Model.Splits.ValorDecimal()</td>
                    </tr>
                }
            </table>
        </td>
    </tr>
    <tr style="font-weight: bold;font-size:10px;">
        <td>
            <table border="0" width="100%">
                <tr>
                    <td>Total de Recebimentos</td>
                    <td style="text-align:right;">@Model.TotalRecebimentos.ValorDecimal()</td>
                </tr>
            </table>
        </td>
        <td>
            <table border="0" width="100%">
                <tr>
                    <td>Total de Descontos</td>
                    <td style="text-align:right;">@Model.TotalDescontos.ValorDecimal()</td>
                </tr>
            </table>
        </td>
    </tr>
    <tr style="font-weight: bold;font-size:10px;">
        <td></td>
        <td>
            <table border="0" width="100%">
                <tr>
                    <td>Total</td>
                    <td style="text-align:right;">@Model.ValorAReceber.ValorDecimal()</td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<br />
<br />
<br />

@if (exibirCampoAssinaturaProfissional) {
    <div style="margin-top: 1cm; white-space: pre;">
        <br />
        -------------------------------------------------------------------<br />
        @Model.NomeProfissional
    </div>
}
