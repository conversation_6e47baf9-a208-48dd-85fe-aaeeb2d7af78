﻿<?xml version="1.0" ?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

    <xsl:decimal-format decimal-separator="," grouping-separator="." />
    <xsl:param name="idEstabelecimentoProfissional" select="0"></xsl:param>
    <xsl:param name="detalhes" select="1"></xsl:param>
    <xsl:param name="estabelecimentoUtilizaEstoqueFracionado" select="2"></xsl:param>
    <xsl:param name="nomeEstabelecimento"></xsl:param>
    <xsl:param name="estabelecimentoPossuiLogo" select="1"></xsl:param>
    <xsl:param name="urlLogoEstabelecimento"></xsl:param>
    <xsl:param name="habilitouDespesasPersonalizadas" select="0"></xsl:param>

    <xsl:template name="break">
        <xsl:param name="text" select="." />
        <xsl:choose>
            <xsl:when test="contains($text, '&#xa;')">
                <xsl:value-of select="substring-before($text, '&#xa;')" />
                <br />
                <xsl:call-template name="break">
                    <xsl:with-param
					  name="text"
					  select="substring-after($text, '&#xa;')" />
                </xsl:call-template>
            </xsl:when>
            <xsl:otherwise>
                <xsl:value-of select="$text" />
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>

    <xsl:template match="/">
        <html>
            <head>
                <style type="text/css">
                    body { font-family: sans-serif; }
                    body, table, h1, h2, h3 { font-size: 10pt; }
                    h1 { margin: 0; }
                    h2 { margin: 0; font-weight: normal; }
                    h3 { font-weight: bold; margin-top: 1cm; margin-bottom: 3mm; border-bottom: 0px solid #000; padding-bottom: 1mm; text-transform: uppercase }
                    h4 { font-style: italic; font-size: 1em; margin-top: 0.5cm; margin-bottom: 0; font-weight: normal; }
                    .observacao { text-align: right; font-style: italic; font-size: 0.8em; }
                    .totais td { font-weight: bold; border-top: 1.5px solid #000!important; }
                    .totais td:first-child { text-align: right; }
                    .grid { border-collapse: collapse; }
                    .grid td, .grid th { border: 1px solid #000; padding: 0.1cm 0.2cm; }
                    .grid td table td { border: none; }
                    .grid th { border-bottom: 1px solid #000; background-color: #DDD }
                    td.valorGrande { text-align: right; }
                    .valorGrande { width: 7em; }
                    .valorQuantidade { width: 5em; text-align: center; }

                    @media print {
                    .break { page-break-before: always; }
                    }

                    @media screen {
                    .break { border-top: 1px dashed #AAA; margin: 2cm 0; }
                    }
                </style>
            </head>
            <body>
                <xsl:if test="$estabelecimentoPossuiLogo = 1">
                    <div id="logo-estabelecimento" style="position: relative;float: left;margin: -10px 0;height: 170px;">
                        <img>
                            <xsl:attribute name="src">
                                <xsl:value-of select="$urlLogoEstabelecimento" />
                            </xsl:attribute>
                        </img>
                    </div>
                </xsl:if>
                <xsl:for-each select="FechamentoMesModel/FechamentosProfissionais/FechamentoMesProfissionalModel">
                    <xsl:if test="$idEstabelecimentoProfissional = 0 or IdEstabelecimentoProfissional = $idEstabelecimentoProfissional">
                        <div style="text-align: center;margin-bottom:75px;">
                            <h1 style="margin:20px 0 40px;font-size: 12pt;">
                                <xsl:value-of select="$nomeEstabelecimento" />
                            </h1>
                            <h1>PAGAMENTO DE PROFISSIONAL</h1>
                            <h1 style="margin: 1mm 0">
                                <xsl:value-of select="NomeProfissional" />
                            </h1>
                            <h2>
                                Mês de Referência: <span style="text-transform: capitalize">
                                    <xsl:choose>
                                        <xsl:when test="../../Mes = 1">Janeiro</xsl:when>
                                        <xsl:when test="../../Mes = 2">Fevereiro</xsl:when>
                                        <xsl:when test="../../Mes = 3">Março</xsl:when>
                                        <xsl:when test="../../Mes = 4">Abril</xsl:when>
                                        <xsl:when test="../../Mes = 5">Maio</xsl:when>
                                        <xsl:when test="../../Mes = 6">Junho</xsl:when>
                                        <xsl:when test="../../Mes = 7">Julho</xsl:when>
                                        <xsl:when test="../../Mes = 8">Agosto</xsl:when>
                                        <xsl:when test="../../Mes = 9">Setembro</xsl:when>
                                        <xsl:when test="../../Mes = 10">Outubro</xsl:when>
                                        <xsl:when test="../../Mes = 11">Novembro</xsl:when>
                                        <xsl:when test="../../Mes = 12">Dezembro</xsl:when>
                                    </xsl:choose>/<xsl:value-of select="../../Ano" />
                                </span>
                            </h2>
                        </div>
                        <div class="observacao">
                            Fechado em
                            <xsl:value-of select="concat(
                      substring(../../DataUltimaAlteracao, 9, 2),
                      '/',
                      substring(../../DataUltimaAlteracao, 6, 2),
                      '/',
                      substring(../../DataUltimaAlteracao, 1, 4)
                      )" />
                            às
                            <xsl:value-of select="substring(../../DataUltimaAlteracao, 12, 8)" />
                        </div>
                        <xsl:if test="(count(ComissaoProdutosLista/FechamentoMesComissaoModel)>0 or count(ComissaoServicosLista/FechamentoMesComissaoModel)>0 or count(ComissaoPacotesLista/FechamentoMesComissaoModel)>0) and $detalhes = 1">
                            <h3>Descritivo das Receitas Variáveis no Período</h3>
                            <xsl:if test="count(ComissaoServicosLista/FechamentoMesComissaoModel)>0">
                                <h4>Comissão sobre Serviços</h4>
                                <table class="grid" width="100%">
                                    <tr>
                                        <th>Serviço</th>
                                        <th class="valorQuantidade">Quantidade</th>
                                        <th class="valorGrande">Valor em Serviços R$</th>
                                        <th class="valorGrande">Valor da Comissão R$</th>
                                    </tr>
                                    <xsl:for-each select="ComissaoServicosLista/FechamentoMesComissaoModel">
                                        <tr>
                                            <td>
                                                <xsl:value-of select="Nome" />
                                            </td>
                                            <td class="valorQuantidade">
                                                <xsl:value-of select="Quantidade" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(ValorPago, '#.##0,00')" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(Comissao, '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </xsl:for-each>
                                    <tr class="totais">
                                        <td>Total</td>
                                        <td class="valorQuantidade">
                                            <xsl:value-of select="sum(ComissaoServicosLista/FechamentoMesComissaoModel/Quantidade)" />
                                        </td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(ComissaoServicosLista/FechamentoMesComissaoModel/ValorPago), '#.##0,00')" />
                                        </td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(ComissaoServicosLista/FechamentoMesComissaoModel/Comissao), '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </xsl:if>

                            <xsl:if test="count(ComissaoProdutosLista/FechamentoMesComissaoModel)>0">
                                <h4>Comissão sobre Produtos Vendidos</h4>
                                <table class="grid" width="100%">
                                    <tr>
                                        <th>Produto</th>
                                        <th class="valorQuantidade">Quantidade</th>
                                        <th class="valorGrande">Valor em Produtos R$</th>
                                        <th class="valorGrande">Valor da Comissão R$</th>
                                    </tr>
                                    <xsl:for-each select="ComissaoProdutosLista/FechamentoMesComissaoModel">
                                        <tr>
                                            <td>
                                                <xsl:value-of select="Nome" />
                                            </td>
                                            <td class="valorQuantidade">
                                                <xsl:value-of select="TextoQuantidade" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(ValorPago, '#.##0,00')" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(Comissao, '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </xsl:for-each>
                                    <tr class="totais">
                                        <td>Total</td>
                                        <td class="valorGrande" style="text-align: center;">
                                            <xsl:value-of select="TextoTotalComissaoProdutos" />
                                        </td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(ComissaoProdutosLista/FechamentoMesComissaoModel/ValorPago), '#.##0,00')" />
                                        </td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(ComissaoProdutosLista/FechamentoMesComissaoModel/Comissao), '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </xsl:if>

                            <xsl:if test="count(ComissaoPacotesLista/FechamentoMesComissaoModel)>0">
                                <h4>Comissão sobre Pacotes Vendidos</h4>
                                <table class="grid" width="100%">
                                    <tr>
                                        <th>Pacote</th>
                                        <th class="valorGrande">Valor em Pacotes R$</th>
                                        <th class="valorGrande">Valor da Comissão R$</th>
                                    </tr>
                                    <xsl:for-each select="ComissaoPacotesLista/FechamentoMesComissaoModel">
                                        <tr>
                                            <td>
                                                <xsl:value-of select="Nome" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(ValorPago, '#.##0,00')" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(Comissao, '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </xsl:for-each>
                                    <tr class="totais">
                                        <td>Total</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(ComissaoPacotesLista/FechamentoMesComissaoModel/ValorPago), '#.##0,00')" />
                                        </td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(ComissaoPacotesLista/FechamentoMesComissaoModel/Comissao), '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </xsl:if>
                        </xsl:if>

                        <xsl:if test="(count(ValesLista/FechamentoMesValeModel)>0 or count(CompraProdutoLista/FechamentoMesComissaoModel)>0 or count(BonificacoesLista/FechamentoMesBonificacaoModel)>0) and $detalhes = 1">
                            <h3>Descritivo dos Descontos Variáveis no Período</h3>

                            <xsl:if test="count(ValesLista/FechamentoMesValeModel)>0">
                                <xsl:if test="$habilitouDespesasPersonalizadas = 1">
                                    <h4>Abatimentos</h4>
                                    <table class="grid" width="100%">
                                        <tr>
                                            <th>Data</th>
                                            <th>Tipo de Abatimento</th>
                                            <th>Descrição/Observação</th>
                                            <th class="valorGrande" style="text-wrap:nowrap;padding-block:16px">
                                                Valor a Descontar R$
                                            </th>
                                        </tr>
                                        <xsl:for-each select="ValesLista/FechamentoMesValeModel">
                                            <tr>
                                                <td>
                                                    <xsl:value-of select="concat(
                                                      substring(Data, 9, 2),
                                                      '/',
                                                      substring(Data, 6, 2),
                                                      '/',
                                                      substring(Data, 1, 4)
                                                )" />
                                                </td>
                                                <td>
                                                    <xsl:value-of select="TipoNome" />
                                                </td>
                                                <td>
                                                    <xsl:value-of select="Descricao" />
                                                </td>
                                                <td class="valorGrande">
                                                    <xsl:value-of select="format-number(Valor, '#.##0,00')" />
                                                </td>
                                            </tr>
                                        </xsl:for-each>
                                        <tr class="totais">
                                            <td></td>
                                            <td class="valorGrande" colspan="2">Total</td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(sum(ValesLista/FechamentoMesValeModel/Valor), '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </table>
                                </xsl:if>
                                <xsl:if test="$habilitouDespesasPersonalizadas = 0">
                                    <h4>Vales</h4>
                                    <table class="grid" width="100%">
                                        <tr>
                                            <th>Data</th>
                                            <th class="valorGrande">
                                                Valor do<br />Vale R$
                                            </th>
                                        </tr>
                                        <xsl:for-each select="ValesLista/FechamentoMesValeModel">
                                            <tr>
                                                <td>
                                                    <xsl:choose>
                                                        <xsl:when test="Descricao != '-'">
                                                            <xsl:value-of select="concat(
                              substring(Data, 9, 2),
                              '/',
                              substring(Data, 6, 2),
                              '/',
                              substring(Data, 1, 4),
                                ' - ',
                              Descricao
                              )" />
                                                        </xsl:when>
                                                        <xsl:otherwise>
                                                            <xsl:value-of select="concat(
                              substring(Data, 9, 2),
                              '/',
                              substring(Data, 6, 2),
                              '/',
                              substring(Data, 1, 4)
                              )" />
                                                        </xsl:otherwise>
                                                    </xsl:choose>
                                                </td>
                                                <td class="valorGrande">
                                                    <xsl:value-of select="format-number(Valor, '#.##0,00')" />
                                                </td>
                                            </tr>
                                        </xsl:for-each>
                                        <tr class="totais">
                                            <td>Total</td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(sum(ValesLista/FechamentoMesValeModel/Valor), '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </table>
                                </xsl:if>
                            </xsl:if>

                            <xsl:if test="count(CompraProdutoLista/FechamentoMesComissaoModel)>0">
                                <h4>Produtos Comprados/Usados</h4>
                                <table class="grid" width="100%">
                                    <tr>
                                        <th>Produto</th>
                                        <th class="valorQuantidade">Quantidade</th>
                                        <th class="valorGrande">Valor a Descontar R$</th>
                                    </tr>
                                    <xsl:for-each select="CompraProdutoLista/FechamentoMesComissaoModel">
                                        <tr>
                                            <td>
                                                <xsl:value-of select="Nome" />
                                            </td>
                                            <td class="valorQuantidade">
                                                <xsl:value-of select="TextoQuantidade" />
                                            </td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(ValorPago, '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </xsl:for-each>
                                    <tr class="totais">
                                        <td>Total</td>
                                        <td class="valorGrande" style="text-align: center;">
                                            <xsl:value-of select="TextoTotalCompraProdutos" />
                                        </td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(sum(CompraProdutoLista/FechamentoMesComissaoModel/ValorPago), '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </xsl:if>

                            <xsl:if test="count(BonificacoesLista/FechamentoMesBonificacaoModel)>0">
                                <xsl:if test="$habilitouDespesasPersonalizadas = 1">
                                    <h4>Recebíveis</h4>
                                    <table class="grid" width="100%">
                                        <tr>
                                            <th>Data</th>
                                            <th>Tipo de Recebível</th>
                                            <th>Descrição/Observação</th>
                                            <th class="valorGrande" style="text-wrap:nowrap;padding-block:16px">
                                                Valor do Recebível R$
                                            </th>
                                        </tr>
                                        <xsl:for-each select="BonificacoesLista/FechamentoMesBonificacaoModel">
                                            <tr>
                                                <td>
                                                    <xsl:value-of select="concat(
                                                      substring(Data, 9, 2),
                                                      '/',
                                                      substring(Data, 6, 2),
                                                      '/',
                                                      substring(Data, 1, 4)
                                                )" />
                                                </td>
                                                <td>
                                                    <xsl:value-of select="TipoNome" />
                                                </td>
                                                <td>
                                                    <xsl:value-of select="Descricao" />
                                                </td>
                                                <td class="valorGrande">
                                                    <xsl:value-of select="format-number(Valor, '#.##0,00')" />
                                                </td>
                                            </tr>
                                        </xsl:for-each>
                                        <tr class="totais">
                                            <td></td>
                                            <td class="valorGrande" colspan="2">Total</td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(sum(BonificacoesLista/FechamentoMesBonificacaoModel/Valor), '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </table>
                                </xsl:if>
                                <xsl:if test="$habilitouDespesasPersonalizadas = 0">
                                    <h4>Bonificações</h4>
                                    <table class="grid" width="100%">
                                        <tr>
                                            <th>Data</th>
                                            <th class="valorGrande">
                                                Valor do<br />Bônus R$
                                            </th>
                                        </tr>
                                        <xsl:for-each select="BonificacoesLista/FechamentoMesBonificacaoModel">
                                            <tr>
                                                <td>
                                                    <xsl:choose>
                                                        <xsl:when test="Descricao != '-'">
                                                            <xsl:value-of select="concat(
                              substring(Data, 9, 2),
                              '/',
                              substring(Data, 6, 2),
                              '/',
                              substring(Data, 1, 4),
                                    ' - ',
                              Descricao
                              )" />
                                                        </xsl:when>
                                                        <xsl:otherwise>
                                                            <xsl:value-of select="concat(
                              substring(Data, 9, 2),
                              '/',
                              substring(Data, 6, 2),
                              '/',
                              substring(Data, 1, 4)
                              )" />
                                                        </xsl:otherwise>
                                                    </xsl:choose>
                                                </td>
                                                <td class="valorGrande">
                                                    <xsl:value-of select="format-number(Valor, '#.##0,00')" />
                                                </td>
                                            </tr>
                                        </xsl:for-each>
                                        <tr class="totais">
                                            <td>Total</td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(sum(BonificacoesLista/FechamentoMesBonificacaoModel/Valor), '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </table>
                                </xsl:if>

                            </xsl:if>
                        </xsl:if>

                    </xsl:if>

                    <xsl:variable name="TotalDeBonificacoes">
                        <xsl:choose>
                            <xsl:when test="not(Bonificacoes)">
                                <xsl:copy-of select="0"/>
                            </xsl:when>
                            <xsl:otherwise>
                                <xsl:copy-of select="Bonificacoes"/>
                            </xsl:otherwise>
                        </xsl:choose>
                    </xsl:variable>

                    <xsl:variable name="TotalComissaoPacotes">
                        <xsl:choose>
                            <xsl:when test="not(ComissaoPacotes)">
                                <xsl:copy-of select="0"/>
                            </xsl:when>
                            <xsl:otherwise>
                                <xsl:copy-of select="ComissaoPacotes"/>
                            </xsl:otherwise>
                        </xsl:choose>
                    </xsl:variable>

                    <xsl:variable name="TotalDescontos" select="(INSS + FaltasAtrasos + Vales + CompraProduto + ValeTransporte + Alimentacao + OutrosDescontos + SplitsPagamento)" />
                    <xsl:variable name="TotalRecebimentos" select="Salario + ComissaoServicos + ComissaoProdutos + $TotalComissaoPacotes + $TotalDeBonificacoes + HoraExtra + SalarioFamilia + Ferias + DecimoTerceiro + OutrosRecebimentos" />

                    <h3>Resumo</h3>
                    <table class="grid" width="100%">
                        <tr>
                            <th>Recebimentos</th>
                            <th>Descontos</th>
                        </tr>
                        <tr>
                            <td valign="top">
                                <table border="0" width="100%">
                                    <tr>
                                        <td>Salário</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(Salario, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Comissão sobre Serviços</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(ComissaoServicos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Comissão sobre Produtos Vendidos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(ComissaoProdutos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Comissão sobre Pacotes Vendidos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number($TotalComissaoPacotes, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <xsl:if test="$habilitouDespesasPersonalizadas = 1">
                                            <td>Recebíveis</td>
                                        </xsl:if>
                                        <xsl:if test="$habilitouDespesasPersonalizadas = 0">
                                            <td>Bonificações</td>
                                        </xsl:if>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number($TotalDeBonificacoes, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Hora Extra</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(HoraExtra, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Salário Familia</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(SalarioFamilia, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Férias</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(Ferias, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>13º Salário</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(DecimoTerceiro, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Outros Recebimentos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(OutrosRecebimentos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td valign="top">
                                <table border="0" width="100%">
                                    <tr>
                                        <td>INSS</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(INSS, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Faltas / Atrasos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(FaltasAtrasos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <xsl:if test="$habilitouDespesasPersonalizadas = 1">
                                            <td>Abatimentos</td>
                                        </xsl:if>
                                        <xsl:if test="$habilitouDespesasPersonalizadas = 0">
                                            <td>Vales</td>
                                        </xsl:if>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(Vales, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Vale Transporte</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(ValeTransporte, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Alimentação</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(Alimentacao, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Compra/Uso de Produtos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(CompraProduto, '#.##0,00')" />
                                        </td>
                                    </tr>
                                    <xsl:if test="EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit = 1 and string(number(EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit)) != 'NaN'">
                                        <tr>
                                            <td>Pagamento de Split</td>
                                            <td class="valorGrande">
                                                <xsl:value-of select="format-number(SplitsPagamento, '#.##0,00')" />
                                            </td>
                                        </tr>
                                    </xsl:if>
                                    <tr>
                                        <td>Outros Descontos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number(OutrosDescontos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table border="0" width="100%">
                                    <tr style="font-weight: bold">
                                        <td>Total de Recebimentos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number($TotalRecebimentos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td>
                                <table border="0" width="100%">
                                    <tr style="font-weight: bold">
                                        <td>Total de Descontos</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number($TotalDescontos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td style="border: none">&#160;</td>
                            <td>
                                <table border="0" width="100%">
                                    <tr style="font-weight: bold">
                                        <td>Total a Receber</td>
                                        <td class="valorGrande">
                                            <xsl:value-of select="format-number($TotalRecebimentos - $TotalDescontos, '#.##0,00')" />
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>

                    <xsl:if test="count(Observacoes)">
                        <div>
                            <h3>Observações</h3>
                            <xsl:call-template name="break">
                                <xsl:with-param name="text" select="Observacoes" />
                            </xsl:call-template>
                        </div>
                    </xsl:if>

                    <div style="margin-top: 1cm">
                        De acordo,<br />
                        <br />
                        -------------------------------------------------------------------<br />
                        <xsl:value-of select="NomeProfissional" />
                    </div>

                    <xsl:if test="last() > position() and $idEstabelecimentoProfissional = 0">
                        <div class="break">&#160;</div>
                    </xsl:if>
                </xsl:for-each>
            </body>
        </html>
    </xsl:template>
</xsl:stylesheet>