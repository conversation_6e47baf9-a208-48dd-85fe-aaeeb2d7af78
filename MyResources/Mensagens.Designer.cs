﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Perlink.Trinks.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Mensagens {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Mensagens() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Perlink.Trinks.Resources.Mensagens", typeof(Mensagens).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A aba Informações Adicionais é opcional, você só precisa preencher se quiser..
        /// </summary>
        public static string AbaInformacoesAdicionaisEhOpcional {
            get {
                return ResourceManager.GetString("AbaInformacoesAdicionaisEhOpcional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acessar Meu Plano.
        /// </summary>
        public static string AcessarMinhaConta {
            get {
                return ResourceManager.GetString("AcessarMinhaConta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acesso para a agenda do estabelecimento e fechamento de conta - usado normalmente para recepcionista.
        /// </summary>
        public static string AcessoAgendaDoEstabelecimentoEFechamentoDeConta {
            get {
                return ResourceManager.GetString("AcessoAgendaDoEstabelecimentoEFechamentoDeConta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profissional I – acesso para visualizar à própria agenda, recomendado para profissionais que executam serviços.
        /// </summary>
        public static string AcessoApenasParaVisualizarPropriaAgenda {
            get {
                return ResourceManager.GetString("AcessoApenasParaVisualizarPropriaAgenda", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acesso à consulta de clientes.
        /// </summary>
        public static string AcessoConsultaDeClientes {
            get {
                return ResourceManager.GetString("AcessoConsultaDeClientes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acesso ao controle de estoque, inventário, cadastro e pré-venda de produto.
        /// </summary>
        public static string AcessoEstoquista {
            get {
                return ResourceManager.GetString("AcessoEstoquista", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recepção.
        /// </summary>
        public static string AcessoParaAgendaDoEstabelecimentoEFechamentoDeConta {
            get {
                return ResourceManager.GetString("AcessoParaAgendaDoEstabelecimentoEFechamentoDeConta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acesso apenas para visualizar a própria agenda – usado normalmente para profissionais que executam serviços.
        /// </summary>
        public static string AcessoParaVisualizarPropriaAgenda {
            get {
                return ResourceManager.GetString("AcessoParaVisualizarPropriaAgenda", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profissional II - acesso à própria agenda, lançamento de serviços e comissões.
        /// </summary>
        public static string AcessoPropriaAgendaEPainelAtendimento {
            get {
                return ResourceManager.GetString("AcessoPropriaAgendaEPainelAtendimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acesso total ao sistema – usado normalmente por gerentes/sócios.
        /// </summary>
        public static string AcessoTotal {
            get {
                return ResourceManager.GetString("AcessoTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Administrador.
        /// </summary>
        public static string AcessoTotalAoSistema {
            get {
                return ResourceManager.GetString("AcessoTotalAoSistema", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acompanhe o Trinks no.
        /// </summary>
        public static string AcompanheOTrinksNo {
            get {
                return ResourceManager.GetString("AcompanheOTrinksNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acompanhe sua assinatura em Meu plano &gt; Ver Histórico de Pagamentos..
        /// </summary>
        public static string AcompanheSuaAssinatura {
            get {
                return ResourceManager.GetString("AcompanheSuaAssinatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A data do seu computador deve estar dentro do período de vencimento da fatura mais antiga pendente de pagamento !.
        /// </summary>
        public static string ADataDoSeuComputadorDeveEstarDentroDoPeriodoDeVencimentoDaFaturaMaisAntigaPendenteDePagamento {
            get {
                return ResourceManager.GetString("ADataDoSeuComputadorDeveEstarDentroDoPeriodoDeVencimentoDaFaturaMaisAntigaPendent" +
                        "eDePagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A data informada não pode ser anterior a hoje..
        /// </summary>
        public static string ADataInformadaNaoPodeSerAnteriorAHoje {
            get {
                return ResourceManager.GetString("ADataInformadaNaoPodeSerAnteriorAHoje", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A despesa só pode ser relacionada a um profissional ou a um fornecedor.
        /// </summary>
        public static string ADespesaSoPodeSerRelacionadoAUmProfissionalOuFornecedor {
            get {
                return ResourceManager.GetString("ADespesaSoPodeSerRelacionadoAUmProfissionalOuFornecedor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O programa de fidelidade do seu estabelecimento não está configurado. Para incluir itens no programa, vá em &quot;Programa de Fidelidade &gt; Configuração&quot;..
        /// </summary>
        public static string AdicionarItensSemProgramaDeFidelidadeConfigurado {
            get {
                return ResourceManager.GetString("AdicionarItensSemProgramaDeFidelidadeConfigurado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adicionar os serviços e as categorias que não estão cadastrados.
        /// </summary>
        public static string AdicionarServicosECategoriasNaoCadastrados {
            get {
                return ResourceManager.GetString("AdicionarServicosECategoriasNaoCadastrados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adicione os dados do profissional. 
        ///.
        /// </summary>
        public static string AdicioneDadosDoProfissional {
            get {
                return ResourceManager.GetString("AdicioneDadosDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adicione os horários de trabalho do profissional..
        /// </summary>
        public static string AdicioneHorariosDeTrabalhoDoProfissional {
            get {
                return ResourceManager.GetString("AdicioneHorariosDeTrabalhoDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A duração máxima de uma despesa é de até um ano!.
        /// </summary>
        public static string ADuracaoMaximaDeUmaDespesaApenasUmAno {
            get {
                return ResourceManager.GetString("ADuracaoMaximaDeUmaDespesaApenasUmAno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A agenda e seu site foram criados!.
        /// </summary>
        public static string AgendaESiteCriadosComSucesso {
            get {
                return ResourceManager.GetString("AgendaESiteCriadosComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agendamento NÃO REALIZADO. Você não está autorizado a marcar hora neste estabelecimento. Favor entrar em contato para mais detalhes..
        /// </summary>
        public static string AgendamentoNaoRealizadoVoceNaoEstaAutorizadoMarcarHoraNoEstabelecimento {
            get {
                return ResourceManager.GetString("AgendamentoNaoRealizadoVoceNaoEstaAutorizadoMarcarHoraNoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agendamento Online.
        /// </summary>
        public static string AgendamentoOnline {
            get {
                return ResourceManager.GetString("AgendamentoOnline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agendamento realizado com sucesso.
        /// </summary>
        public static string AgendamentoRealizadoComSucesso {
            get {
                return ResourceManager.GetString("AgendamentoRealizadoComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agendamentos somente poderão ser realizados com data a partir do dia {0:d}.
        /// </summary>
        public static string AgendamentosAPartirDe {
            get {
                return ResourceManager.GetString("AgendamentosAPartirDe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AGENDE PELO.
        /// </summary>
        public static string AgendePelo {
            get {
                return ResourceManager.GetString("AgendePelo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A imagem adicionada aqui será a logomarca do seu site. Ela é importante pois o seu estabelecimento só aparecerá na busca do Portal Trinks se você adicionar a logomarca. Esta imagem pode ser alterada posteriormente..
        /// </summary>
        public static string AImagemAdicionadaAquiSeraALogomarcaDoSeuSite {
            get {
                return ResourceManager.GetString("AImagemAdicionadaAquiSeraALogomarcaDoSeuSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O percentual de Alíquota ISS deve estar entre 0% e 100%..
        /// </summary>
        public static string AliquotaInvalida {
            get {
                return ResourceManager.GetString("AliquotaInvalida", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A logo do seu estabelecimento.
        /// </summary>
        public static string ALogoDoEstabelecimento {
            get {
                return ResourceManager.GetString("ALogoDoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A mensagem precisa ser definida para que o envio de SMS para os aniversariantes seja ativado!.
        /// </summary>
        public static string AMensagemPrecisaSerDefinidaParaAtivarSmsAniversario {
            get {
                return ResourceManager.GetString("AMensagemPrecisaSerDefinidaParaAtivarSmsAniversario", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A observação do fornecedor não pode ultrapassar 400 caracteres!.
        /// </summary>
        public static string AObservacaoDoFornecedorNaoPodeUltrapassar400Caracteres {
            get {
                return ResourceManager.GetString("AObservacaoDoFornecedorNaoPodeUltrapassar400Caracteres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ao cadastrar a duração de um serviço, coloque o tempo de duração médio. Se um profissional demorar menos ou mais tempo, basta alterar a duração editando o agendamento!.
        /// </summary>
        public static string AoCadastrarDuracaoDoServicoColoqueTempoDuracaoMedio {
            get {
                return ResourceManager.GetString("AoCadastrarDuracaoDoServicoColoqueTempoDuracaoMedio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ao cancelar você perde &lt;span&gt;imediatamente&lt;/span&gt; o acesso a sua agenda..
        /// </summary>
        public static string AoCancelarVocePerdeAcessoASuaAgenda {
            get {
                return ResourceManager.GetString("AoCancelarVocePerdeAcessoASuaAgenda", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ao salvar será enviado para o profissional um e-mail referente à senha.
        /// </summary>
        public static string AoSalvarSeraEnviadoEmailProfissionalReferenteASenha {
            get {
                return ResourceManager.GetString("AoSalvarSeraEnviadoEmailProfissionalReferenteASenha", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acesso apenas ao cadastro de clientes.
        /// </summary>
        public static string ApenasCadastroDeCliente {
            get {
                return ResourceManager.GetString("ApenasCadastroDeCliente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to APROVA.
        /// </summary>
        public static string Aprova {
            get {
                return ResourceManager.GetString("Aprova", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aproveite os benefícios que o Trinks traz pro seu estabelecimento.
        /// </summary>
        public static string AproveiteOsBeneficiosDoTrinksParaSeuEstabelecimento {
            get {
                return ResourceManager.GetString("AproveiteOsBeneficiosDoTrinksParaSeuEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aqui você pode colocar suas observações, como o tempo de tolerância para atrasos ou algum aviso para os seus clientes, por exemplo: Não nos responsabilizamos por produtos trazidos pelos clientes..
        /// </summary>
        public static string AquiVocePodeColocarSuasObservacoes {
            get {
                return ResourceManager.GetString("AquiVocePodeColocarSuasObservacoes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As informações não ficarão disponíveis para os seus clientes. Eles só irão ver o nome e os serviços que o profissional realiza..
        /// </summary>
        public static string AsInformacoesNaoFicaraoDisponiveisParaSeusClientes {
            get {
                return ResourceManager.GetString("AsInformacoesNaoFicaraoDisponiveisParaSeusClientes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As observações sobre o fornecedor não pode ultrapassar os 400 caracteres..
        /// </summary>
        public static string AsObervacoesSobreOFornecedorNaoPodeUltrapassar400Caracteres {
            get {
                return ResourceManager.GetString("AsObervacoesSobreOFornecedorNaoPodeUltrapassar400Caracteres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plano de assinatura mensal..
        /// </summary>
        public static string AssinaturaMensalDebitadoCartaoCredito {
            get {
                return ResourceManager.GetString("AssinaturaMensalDebitadoCartaoCredito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assine aqui.
        /// </summary>
        public static string AssineAqui {
            get {
                return ResourceManager.GetString("AssineAqui", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Associação de profissionais ao serviço feita com sucesso!.
        /// </summary>
        public static string AssociacaoDeProfissionaisAoServicoFeitaComSucesso {
            get {
                return ResourceManager.GetString("AssociacaoDeProfissionaisAoServicoFeitaComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Associe pelo menos um profissional ao serviço!.
        /// </summary>
        public static string AssociePeloMenosUmProfissionalAoServico {
            get {
                return ResourceManager.GetString("AssociePeloMenosUmProfissionalAoServico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Associe sua conta ao Facebook.
        /// </summary>
        public static string AssocieSuaContaAoFacebook {
            get {
                return ResourceManager.GetString("AssocieSuaContaAoFacebook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As taxas de Assinatura são faturadas no início de cada período.
        /// </summary>
        public static string AsTaxasSaoFaturadasACadaPeriodo {
            get {
                return ResourceManager.GetString("AsTaxasSaoFaturadasACadaPeriodo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baixar boleto.
        /// </summary>
        public static string BaixarBoleto {
            get {
                return ResourceManager.GetString("BaixarBoleto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baixe o boleto e realize o pagamento para garantir a continuidade de seu acesso!.
        /// </summary>
        public static string BaixeOBoletoERealizeOPagamento {
            get {
                return ResourceManager.GetString("BaixeOBoletoERealizeOPagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sua fatura já está disponível para pagamento e sua assinatura foi.
        /// </summary>
        public static string BoletoLiberadoParaPagamento {
            get {
                return ResourceManager.GetString("BoletoLiberadoParaPagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O boleto para o seu próximo pagamento já encontra-se disponível..
        /// </summary>
        public static string BoletoParaProximoPagamentoEstaDisponivel {
            get {
                return ResourceManager.GetString("BoletoParaProximoPagamentoEstaDisponivel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não identificamos seu pagamento até o momento..
        /// </summary>
        public static string BoletoPendenteDePagamento {
            get {
                return ResourceManager.GetString("BoletoPendenteDePagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A cada 30 dias será gerado uma nova fatura para pagamento, de acordo com a faixa em que você se encontrar..
        /// </summary>
        public static string BoletoSeraGeradoACada30Dias {
            get {
                return ResourceManager.GetString("BoletoSeraGeradoACada30Dias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não iremos funcionar no dia informado.
        /// </summary>
        public static string Busca_EstabelecimentoNaoFuncionara {
            get {
                return ResourceManager.GetString("Busca_EstabelecimentoNaoFuncionara", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este estabelecimento não permite a consulta aos seus horários através do hotsite. Para marcar uma hora, entre em contato através do(s) telefone(s).
        /// </summary>
        public static string Busca_EstabelecimentoNaoPermite {
            get {
                return ResourceManager.GetString("Busca_EstabelecimentoNaoPermite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não encontramos nenhum horário disponível para hoje. Que tal procurar outro dia?.
        /// </summary>
        public static string Busca_SemRegistros {
            get {
                return ResourceManager.GetString("Busca_SemRegistros", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Busca do Portal e do Aplicativo.
        /// </summary>
        public static string BuscaPortalEAplicativo {
            get {
                return ResourceManager.GetString("BuscaPortalEAplicativo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cadastrando o seu Estabelecimento no Trinks.
        /// </summary>
        public static string CadastrandoSeuEstabelecimentoNoTrinks {
            get {
                return ResourceManager.GetString("CadastrandoSeuEstabelecimentoNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Os campos de horário de almoço e intervalo são opcionais..
        /// </summary>
        public static string CamposAlmocoEIntervaloSaoOpcionais {
            get {
                return ResourceManager.GetString("CamposAlmocoEIntervaloSaoOpcionais", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Por padrão, os campos de horário do profissional são preenchidos iguais ao horário de funcionamento do salão, assim, para sócios e outros administradores, você não precisa alterar o horário..
        /// </summary>
        public static string CamposDeHorarioDoProfissionalSaoPreenchidosPorPadraoSeguindoHorarioDeFuncionamentoDoSalao {
            get {
                return ResourceManager.GetString("CamposDeHorarioDoProfissionalSaoPreenchidosPorPadraoSeguindoHorarioDeFuncionament" +
                        "oDoSalao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O cancelamento entrará em vigor imediatamente..
        /// </summary>
        public static string CancelamentoEntraraEmVigorImediatamente {
            get {
                return ResourceManager.GetString("CancelamentoEntraraEmVigorImediatamente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancelar Plano de Assinatura.
        /// </summary>
        public static string CancelarPlanoAssinatura {
            get {
                return ResourceManager.GetString("CancelarPlanoAssinatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Caso deseje remover este serviço, favor desassociá-lo antes de todos os profissionais que o realizam..
        /// </summary>
        public static string CasoDesejeRemoverEsteServicoFavorDesassociarTodosOsProfissionais {
            get {
                return ResourceManager.GetString("CasoDesejeRemoverEsteServicoFavorDesassociarTodosOsProfissionais", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clicando em.
        /// </summary>
        public static string ClicandoEm {
            get {
                return ResourceManager.GetString("ClicandoEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clicar em Edição Rápida para alterar os valores e as durações de todos os serviços.
        /// </summary>
        public static string ClicarEmEdicaoRapidaParaAlterarValoresEDuracoesDeServicos {
            get {
                return ResourceManager.GetString("ClicarEmEdicaoRapidaParaAlterarValoresEDuracoesDeServicos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clique aqui.
        /// </summary>
        public static string CliqueAqui {
            get {
                return ResourceManager.GetString("CliqueAqui", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clique aqui e Assine já!.
        /// </summary>
        public static string CliqueAquiEAssineJa {
            get {
                return ResourceManager.GetString("CliqueAquiEAssineJa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clique aqui para acessar o Hotsite do Estabelecimento.
        /// </summary>
        public static string CliqueAquiParaAcessarHotsiteDoEstabelecimento {
            get {
                return ResourceManager.GetString("CliqueAquiParaAcessarHotsiteDoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clique aqui para assinar o Trinks!.
        /// </summary>
        public static string CliqueAquiParaAssinarOTrinks {
            get {
                return ResourceManager.GetString("CliqueAquiParaAssinarOTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clique aqui para baixá-lo..
        /// </summary>
        public static string CliqueAquiParaBaixarOBoleto {
            get {
                return ResourceManager.GetString("CliqueAquiParaBaixarOBoleto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clique aqui para resolver..
        /// </summary>
        public static string CliqueAquiParaResolver {
            get {
                return ResourceManager.GetString("CliqueAquiParaResolver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Código de propriedade do profissional.
        /// </summary>
        public static string CodigoDePropriedadeDoProfissional {
            get {
                return ResourceManager.GetString("CodigoDePropriedadeDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Foi encontrado serviço com mesmo Código informado..
        /// </summary>
        public static string CodigoServicoEmDuplicidade {
            get {
                return ResourceManager.GetString("CodigoServicoEmDuplicidade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se um serviço tiver diferentes preços, variando de acordo com o tamanho do cabelo, por exemplo, coloque sempre o menor valor e a opção A partir de. Dessa forma você não vai correr o risco de um cliente agendar um serviço querendo pagar o valor de outro!.
        /// </summary>
        public static string ColoqueSempreMenorValorParaServicosDeDiferentesPrecos {
            get {
                return ResourceManager.GetString("ColoqueSempreMenorValorParaServicosDeDiferentesPrecos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Com as informações abaixo, conseguiremos montar a estrutura da sua agenda e também poderemos contar para os seus clientes mais sobre você..
        /// </summary>
        public static string ComInformacoesAbaixoConseguiremosMontarAEstruturaDaAgenda {
            get {
                return ResourceManager.GetString("ComInformacoesAbaixoConseguiremosMontarAEstruturaDaAgenda", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Com o Trinks você.
        /// </summary>
        public static string ComOTrinksVoce {
            get {
                return ResourceManager.GetString("ComOTrinksVoce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Os e-mails informados não coincidem.
        /// </summary>
        public static string Confere_Email {
            get {
                return ResourceManager.GetString("Confere_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirma a exclusão dessa foto?.
        /// </summary>
        public static string ConfirmaExclusaoFoto {
            get {
                return ResourceManager.GetString("ConfirmaExclusaoFoto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Já existe outro agendamento neste horário para este profissional..
        /// </summary>
        public static string ConfirmarAgendamentoComConflitoDeHorario {
            get {
                return ResourceManager.GetString("ConfirmarAgendamentoComConflitoDeHorario", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O profissional está marcado como ausente neste horário..
        /// </summary>
        public static string ConfirmarAgendamentoEmPeriodoDeAusencia {
            get {
                return ResourceManager.GetString("ConfirmarAgendamentoEmPeriodoDeAusencia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoje o estabelecimento funciona em um horário especial e você está marcando uma hora fora deste horário..
        /// </summary>
        public static string ConfirmarAgendamentoForaHorarioEspecial {
            get {
                return ResourceManager.GetString("ConfirmarAgendamentoForaHorarioEspecial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você está tentando marcar uma hora fora do horário de funcionamento do estabelecimento..
        /// </summary>
        public static string ConfirmarAgendamentoForaHorarioTrabalho {
            get {
                return ResourceManager.GetString("ConfirmarAgendamentoForaHorarioTrabalho", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você está tentando marcar uma hora fora do horário de trabalho do profissional..
        /// </summary>
        public static string ConfirmarAgendamentoForaHorarioTrabalhoProfissional {
            get {
                return ResourceManager.GetString("ConfirmarAgendamentoForaHorarioTrabalhoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CONFIRME SEU CADASTRO NO TRINKS.
        /// </summary>
        public static string ConfirmeSeuEnderecoDeEmail {
            get {
                return ResourceManager.GetString("ConfirmeSeuEnderecoDeEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to conforme impresso no cartão.
        /// </summary>
        public static string ConformeImpressoNoCartao {
            get {
                return ResourceManager.GetString("ConformeImpressoNoCartao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consulta Apenas Relatório Financeiro Caixa.
        /// </summary>
        public static string ConsultaApenasRelatórioFinanceiroCaixa {
            get {
                return ResourceManager.GetString("ConsultaApenasRelatórioFinanceiroCaixa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consulta os estabelecimentos onde você já é cliente para novos agendamentos.
        /// </summary>
        public static string ConsultaEstabelecimentosOndeVoceJaEhCliente {
            get {
                return ResourceManager.GetString("ConsultaEstabelecimentosOndeVoceJaEhCliente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consulta seus serviços agendados e realizados.
        /// </summary>
        public static string ConsultaSeusServicosAgendadosERealizados {
            get {
                return ResourceManager.GetString("ConsultaSeusServicosAgendadosERealizados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copiar o profissional nos emails para clientes sobre seus agendamentos.
        /// </summary>
        public static string CopiarProfissionalNosEmailsParaClientesSobreSeusAgendamentos {
            get {
                return ResourceManager.GetString("CopiarProfissionalNosEmailsParaClientesSobreSeusAgendamentos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O cpf ou cnpj é inválido.
        /// </summary>
        public static string CpfCnpjInvalido {
            get {
                return ResourceManager.GetString("CpfCnpjInvalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O cpf é inválido.
        /// </summary>
        public static string CpfInvalido {
            get {
                return ResourceManager.GetString("CpfInvalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este CPF corresponde a um profissional que já foi funcionário do seu estabelecimento. Deseja aproveitar os dados do antigo cadastro?.
        /// </summary>
        public static string CPFJaAssociadoAProfissional {
            get {
                return ResourceManager.GetString("CPFJaAssociadoAProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Criar Conta.
        /// </summary>
        public static string CriarConta {
            get {
                return ResourceManager.GetString("CriarConta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dados atualizados com sucesso!.
        /// </summary>
        public static string DadosAtualizadosComSucesso {
            get {
                return ResourceManager.GetString("DadosAtualizadosComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atenção! Data final deve ser menor ou igual a {0}. O tempo total permitido para recorrência é de {1} dias..
        /// </summary>
        public static string DataLimiteRecorrencia {
            get {
                return ResourceManager.GetString("DataLimiteRecorrencia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A data da movimentação não pode ser maior que a data atual..
        /// </summary>
        public static string DataMovimentacaoNaoPodeMAiorHoje {
            get {
                return ResourceManager.GetString("DataMovimentacaoNaoPodeMAiorHoje", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deseja continuar?.
        /// </summary>
        public static string DesejaContinuar {
            get {
                return ResourceManager.GetString("DesejaContinuar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deseja imprimir o comprovante do vale/adiantamento do profissional agora?.
        /// </summary>
        public static string DesejaImprimirValeProfissional {
            get {
                return ResourceManager.GetString("DesejaImprimirValeProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deseja realmente excluir esta campanha?.
        /// </summary>
        public static string DesejaRealmenteExcluirEstaCampanha {
            get {
                return ResourceManager.GetString("DesejaRealmenteExcluirEstaCampanha", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deseja realmente redefinir a senha do usuário?.
        /// </summary>
        public static string DesejaRedefinirSenhaDoUsuario {
            get {
                return ResourceManager.GetString("DesejaRedefinirSenhaDoUsuario", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to de uso grátis!.
        /// </summary>
        public static string DeUsoGratis {
            get {
                return ResourceManager.GetString("DeUsoGratis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deve ser definido pelo menos um dia de trabalho..
        /// </summary>
        public static string DeveSerDefinidoPeloMenosUmDiaDeTrabalho {
            get {
                return ResourceManager.GetString("DeveSerDefinidoPeloMenosUmDiaDeTrabalho", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dia de acesso ao Trinks.
        /// </summary>
        public static string DiaDeAcessoAoTrinks {
            get {
                return ResourceManager.GetString("DiaDeAcessoAoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to dia de uso grátis do Trinks.com!.
        /// </summary>
        public static string DiaDeUsoGratisDoTrinks {
            get {
                return ResourceManager.GetString("DiaDeUsoGratisDoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to dias.
        /// </summary>
        public static string Dias {
            get {
                return ResourceManager.GetString("Dias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digite aqui o código de confirmação recebido por e-mail para concluir o cadastro.
        /// </summary>
        public static string DigiteAquiCodigoConfirmacaoRecebidoPorEmailParaConcluirCadastro {
            get {
                return ResourceManager.GetString("DigiteAquiCodigoConfirmacaoRecebidoPorEmailParaConcluirCadastro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digitei o e-mail errado.
        /// </summary>
        public static string DigiteiOEmailErrado {
            get {
                return ResourceManager.GetString("DigiteiOEmailErrado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diminua o número de profissionais selecionados.
        ///O recálculo não pode impactar em mais de {0} serviços de profissionais.
        ///Atualmente está  impactando em {1}..
        /// </summary>
        public static string DiminuaProfissionaisSelecionados {
            get {
                return ResourceManager.GetString("DiminuaProfissionaisSelecionados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to É necessário ler e aceitar os Termos de Uso para concluir o cadastro..
        /// </summary>
        public static string EhNecessarioLerEAceitarOsTermosDeUsoParaConcluirOCadastro {
            get {
                return ResourceManager.GetString("EhNecessarioLerEAceitarOsTermosDeUsoParaConcluirOCadastro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to e informe o cpf do profissional..
        /// </summary>
        public static string EInformeCpfDoProfissional {
            get {
                return ResourceManager.GetString("EInformeCpfDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-mail inválido.
        /// </summary>
        public static string Email_Invalido {
            get {
                return ResourceManager.GetString("Email_Invalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O e-mail deve ser preenchido corretamente.
        /// </summary>
        public static string EmailDeveSerPreenchidoCorretamente {
            get {
                return ResourceManager.GetString("EmailDeveSerPreenchidoCorretamente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-mail inválido.
        /// </summary>
        public static string EmailInvalido {
            get {
                return ResourceManager.GetString("EmailInvalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O e-mail não confere com as informações cadastradas no Trinks..
        /// </summary>
        public static string EmailNaoPossuiContaNoTrinks {
            get {
                return ResourceManager.GetString("EmailNaoPossuiContaNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O e-mail e/ou a senha não conferem com as informações cadastradas no Trinks..
        /// </summary>
        public static string EmailSenhaInvalidos {
            get {
                return ResourceManager.GetString("EmailSenhaInvalidos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Em Edição Rápida você pode editar a duração e o valor de todos os serviços de uma só vez. No valor do serviço, você pode escolher entre Preço Fixo, A partir de ou Valor Promocional..
        /// </summary>
        public static string EmEdicaoRapidaVocePodeEditarDuracaoEValorDeServicosDeUmaSoVez {
            get {
                return ResourceManager.GetString("EmEdicaoRapidaVocePodeEditarDuracaoEValorDeServicosDeUmaSoVez", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Em que parte do meu site estas informações vão aparecer?.
        /// </summary>
        public static string EmQueParteDoMeuSiteAsInformacoesVaoAparecer {
            get {
                return ResourceManager.GetString("EmQueParteDoMeuSiteAsInformacoesVaoAparecer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encontra estabelecimentos numa localidade (e vê as informações e fotos dos mesmos).
        /// </summary>
        public static string EncontraEstabelecimentosNumaLocalidade {
            get {
                return ResourceManager.GetString("EncontraEstabelecimentosNumaLocalidade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encontra estabelecimentos que realizam um 
        ///determinado serviço numa localidade e que funcionem numa data selecionada 
        ///(e vê as informações e fotos dos mesmos).
        /// </summary>
        public static string EncontraEstabelecimentosQueRealizamUmDeterminadoServico {
            get {
                return ResourceManager.GetString("EncontraEstabelecimentosQueRealizamUmDeterminadoServico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encontre os melhores salões, clínicas de estética, SPAs e Barbearias do Brasil.
        /// </summary>
        public static string EncontreOsMelhoresSaloesClinicasSpasDoBrasil {
            get {
                return ResourceManager.GetString("EncontreOsMelhoresSaloesClinicasSpasDoBrasil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encontre um horário livre e marque sua hora.
        /// </summary>
        public static string EncontreUmHorarioLivreEMarqueSuaHora {
            get {
                return ResourceManager.GetString("EncontreUmHorarioLivreEMarqueSuaHora", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entre em contato conosco.
        /// </summary>
        public static string EntreEmContatoConosco {
            get {
                return ResourceManager.GetString("EntreEmContatoConosco", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Um e-mail de recuperação de senha será enviado para o endereço {0}.
        /// Deseja prosseguir?.
        /// </summary>
        public static string EnviarEmailRecuperacaoDeSenha {
            get {
                return ResourceManager.GetString("EnviarEmailRecuperacaoDeSenha", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enviaremos um e-mail de confirmação do seu cancelamento..
        /// </summary>
        public static string EnviaremosEmailConfirmacaoCancelamento {
            get {
                return ResourceManager.GetString("EnviaremosEmailConfirmacaoCancelamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enviar senha para o e-mail do profissional.
        /// </summary>
        public static string EnviarSenhaParaEmailDoProfissional {
            get {
                return ResourceManager.GetString("EnviarSenhaParaEmailDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Erro ao salvar o agendamento!.
        /// </summary>
        public static string Erro_Salvar_Agendamento {
            get {
                return ResourceManager.GetString("Erro_Salvar_Agendamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Erro ao salvar o registro de ausência!.
        /// </summary>
        public static string Erro_Salvar_Registro_Ausencia {
            get {
                return ResourceManager.GetString("Erro_Salvar_Registro_Ausencia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ocorreu um erro na página. Tente novamente mais tarde..
        /// </summary>
        public static string ErroNaPagina {
            get {
                return ResourceManager.GetString("ErroNaPagina", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esqueci minha senha.
        /// </summary>
        public static string EsqueciMinhaSenha {
            get {
                return ResourceManager.GetString("EsqueciMinhaSenha", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esses valores poderão ser alterados a qualquer momento..
        /// </summary>
        public static string EssesValoresPoderaoSerAlteradosAQualquerMomento {
            get {
                return ResourceManager.GetString("EssesValoresPoderaoSerAlteradosAQualquerMomento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to estamos muito felizes por sua equipe fazer parte da nossa família.
        /// </summary>
        public static string EstamosMuitoFelizes {
            get {
                return ResourceManager.GetString("EstamosMuitoFelizes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estas mensagens são cortesia do Trinks.com, ou seja, não abatem o saldo do seu pacote de SMS adquirido para campanhas de SMS Marketing (promoção por tempo indeterminado)..
        /// </summary>
        public static string EstasMensagensSaoCortesiaDoTrinks {
            get {
                return ResourceManager.GetString("EstasMensagensSaoCortesiaDoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estes e-mails são cortesia do Trinks.com, ou seja, não abatem do saldo do pacote mensal de e-mails e nem de pacotes adquiridos para campanhas de e-mail Marketing (promoção por tempo indeterminado)..
        /// </summary>
        public static string EstasMensagensSaoCortesiaDoTrinks_Email {
            get {
                return ResourceManager.GetString("EstasMensagensSaoCortesiaDoTrinks_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este e-mail será o seu login no Trinks.
        /// </summary>
        public static string EsteEmailSeraSeuLoginNoTrinks {
            get {
                return ResourceManager.GetString("EsteEmailSeraSeuLoginNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este é o texto da mensagem (que poderá ser alterado a qualquer momento) que será enviada para os seus clientes por SMS quando eles fizerem aniversário..
        /// </summary>
        public static string EsteEOTextoQueSeraEnviadoParaSeusClientes {
            get {
                return ResourceManager.GetString("EsteEOTextoQueSeraEnviadoParaSeusClientes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este é o texto do e-mail (que poderá ser alterado a qualquer momento) que será enviado para os seus clientes quando eles fizerem aniversário..
        /// </summary>
        public static string EsteEOTextoQueSeraEnviadoParaSeusClientes_Email {
            get {
                return ResourceManager.GetString("EsteEOTextoQueSeraEnviadoParaSeusClientes_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este horário não se encontra mais disponível.
        /// </summary>
        public static string EsteHorarioNaoSeEncontraMaisDisponivel {
            get {
                return ResourceManager.GetString("EsteHorarioNaoSeEncontraMaisDisponivel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este é o nome que aparecerá no site do estabelecimento.
        /// </summary>
        public static string EsteNomeApareceraNoSiteDoEstabelecimento {
            get {
                return ResourceManager.GetString("EsteNomeApareceraNoSiteDoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este será o endereço do site que o Trinks cria para o seu negócio. Você deve divulgar para os seus clientes, para que eles comecem a marcar sua hora pela Internet..
        /// </summary>
        public static string EsteSeraOEnderecoDoSite {
            get {
                return ResourceManager.GetString("EsteSeraOEnderecoDoSite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Excluir os serviços que o seu estabelecimento não realiza.
        /// </summary>
        public static string ExcluirServicosQueSeuEstabelecimentoNaoRealiza {
            get {
                return ResourceManager.GetString("ExcluirServicosQueSeuEstabelecimentoNaoRealiza", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existem alguns serviços com profissionais associados. Para realizar a ação, você deve desassociar os profissionais dos seguintes serviços:.
        /// </summary>
        public static string ExistemAlgunsServicosComProfissionaisAssociados {
            get {
                return ResourceManager.GetString("ExistemAlgunsServicosComProfissionaisAssociados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existem comissões não informadas ou com valor zero. Deseja prosseguir?.
        /// </summary>
        public static string ExistemComissoesNaoInformadasOuComValorZero {
            get {
                return ResourceManager.GetString("ExistemComissoesNaoInformadasOuComValorZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As faixas acima são para profissionais cujas agendas serão controladas pelo Trinks. A quantidade de profissionais administrativos cadastrados é ilimitada..
        /// </summary>
        public static string FaixasParaProfissionaisAgendaControladasPeloTrinks {
            get {
                return ResourceManager.GetString("FaixasParaProfissionaisAgendaControladasPeloTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fale sobre o seu estabelecimento. O que você quer contar aos seus clientes? Olhe esse exemplo de texto: O estabelecimento Trinks foi inaugurado em 2011 e tem como objetivo levar aos nossos clientes sempre os melhores resultados e a satisfação garantida. Não perca tempo! Marque sua hora pela Internet e fique nos Trinks..
        /// </summary>
        public static string FaleSobreSeuEstabelecimento {
            get {
                return ResourceManager.GetString("FaleSobreSeuEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neste fechamento estão sendo considerados R$ {0} em comissão de serviços e R$ {1} em comissão de produtos do período {2} referente a registros com data de liberação da comissão para {3} e pagos após o fechamento deste período ou cadastrados retroativamente. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores..
        /// </summary>
        public static string FechamentoComResiduosComissoesDoMesAnterior {
            get {
                return ResourceManager.GetString("FechamentoComResiduosComissoesDoMesAnterior", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neste fechamento estão sendo considerados R$ {0} em comissão de serviços e R$ {1} em comissão de produtos do período {2} referente a registros com data de liberação da comissão para {3} e R$ {4} em comissão de serviços e R$ {5} em comissão de produtos com  data de liberação da comissão em {6} estão considerados no Fechamento Mensal de {7}, pois existem registros que foram pagos após o fechamento do mês ou cadastrados retroativamente. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em [rest of string was truncated]&quot;;.
        /// </summary>
        public static string FechamentoComResiduosComissoesEmDiferentesMeses {
            get {
                return ResourceManager.GetString("FechamentoComResiduosComissoesEmDiferentesMeses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R$ {0} em comissão de serviços e R$ {1} em comissão de produtos com data de liberação da comissão em {2} estão considerados no Fechamento Mensal de {3}, pois existem registros que foram pagos após o fechamento deste período ou cadastrados retroativamente. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores..
        /// </summary>
        public static string FechamentoComResiduosComissoesNoProximoMes {
            get {
                return ResourceManager.GetString("FechamentoComResiduosComissoesNoProximoMes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fechamento de conta realizado com sucesso!.
        /// </summary>
        public static string FechamentoContaRealizadoComSucesso {
            get {
                return ResourceManager.GetString("FechamentoContaRealizadoComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Foto(s) do estabelecimento.
        /// </summary>
        public static string FotosDoEstabelecimento {
            get {
                return ResourceManager.GetString("FotosDoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerenciador do seu Estabelecimento.
        /// </summary>
        public static string GerenciadorEstabelecimento {
            get {
                return ResourceManager.GetString("GerenciadorEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to gratuito por {0} dias!.
        /// </summary>
        public static string GratuitoPorXDias {
            get {
                return ResourceManager.GetString("GratuitoPorXDias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoje é o último dia para enviar seu comprovante para.
        /// </summary>
        public static string HojeEhOUltimoDiaParaEnviarSeuComprovantePara {
            get {
                return ResourceManager.GetString("HojeEhOUltimoDiaParaEnviarSeuComprovantePara", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoje é seu.
        /// </summary>
        public static string HojeEhSeu {
            get {
                return ResourceManager.GetString("HojeEhSeu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoje seria seu.
        /// </summary>
        public static string HojeSeriaSeu {
            get {
                return ResourceManager.GetString("HojeSeriaSeu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Houve um problema no seu pagamento..
        /// </summary>
        public static string HouveUmProblemaNoSeuPagamento {
            get {
                return ResourceManager.GetString("HouveUmProblemaNoSeuPagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A imagem selecionada não atende aos requisitos do sistema.\nO arquivo deve ter entre 0MB e 4MB e estar no formato PNG..
        /// </summary>
        public static string ImagemNaoAtendeRequisitosSistema {
            get {
                return ResourceManager.GetString("ImagemNaoAtendeRequisitosSistema", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to É preciso adicionar pelo menos um serviço para realizar o check out!.
        /// </summary>
        public static string IncluirServicoParaCheckOut {
            get {
                return ResourceManager.GetString("IncluirServicoParaCheckOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicação realizada com sucesso!.
        /// </summary>
        public static string IndicacaoRealizadaComSucesso {
            get {
                return ResourceManager.GetString("IndicacaoRealizadaComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicar caso seja fornecedor de produtos utilizados pelo estabelecimento para realização de serviços e/ou venda para clientes e profissionais. Fornecedores de produtos com controle de estoque..
        /// </summary>
        public static string IndicarCasoSejaFornecedorDeProdutosUtilizadosPeloEstabelecimento {
            get {
                return ResourceManager.GetString("IndicarCasoSejaFornecedorDeProdutosUtilizadosPeloEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As configurações de e-mail de aniversariantes foram alteradas. Caso saia desta páginas as alterações serão perdidas..
        /// </summary>
        public static string InformacaoConfiguracoesEmailAniversariantesAlteradas {
            get {
                return ResourceManager.GetString("InformacaoConfiguracoesEmailAniversariantesAlteradas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As configurações de SMS de aniversariantes foram alteradas. Caso saia desta páginas as alterações serão perdidas..
        /// </summary>
        public static string InformacaoConfiguracoesSmsAniversariantesAlteradas {
            get {
                return ResourceManager.GetString("InformacaoConfiguracoesSmsAniversariantesAlteradas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você está com saldo de {0} e-mails para envio..
        /// </summary>
        public static string InformacaoSaldoEmailMarketing {
            get {
                return ResourceManager.GetString("InformacaoSaldoEmailMarketing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você está com saldo de {0} SMS para disparo..
        /// </summary>
        public static string InformacaoSaldoSmsMarketing {
            get {
                return ResourceManager.GetString("InformacaoSaldoSmsMarketing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você não possui saldo de e-mail para disparo..
        /// </summary>
        public static string InformacaoSaldoZeroEmailMarketing {
            get {
                return ResourceManager.GetString("InformacaoSaldoZeroEmailMarketing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você não possui saldo de SMS para disparo..
        /// </summary>
        public static string InformacaoSaldoZeroSmsMarketing {
            get {
                return ResourceManager.GetString("InformacaoSaldoZeroSmsMarketing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O Horário de Início deverá ser a hora que o estabelecimento é aberto e o Horário Final deverá ser a hora que ele é fechado e não o último horário de marcação. Exemplo: Se o seu estabelecimento fecha às 20h, o último horário disponível para o cliente agendar pela Internet será de 19:30h, para um serviço que dura 30 minutos, 19h para um serviço de 60 minutos e assim por diante. Mas se mesmo assim você quiser marcar um agendamento para um horário além do informado, não tem problema! Somente os Agendamentos pel [rest of string was truncated]&quot;;.
        /// </summary>
        public static string InformacoesSobreHorariosDoEstabelecimento {
            get {
                return ResourceManager.GetString("InformacoesSobreHorariosDoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selecione “Sim” para profissionais que realizam serviços e que vão poder incluir fotos relacionadas aos mesmos..
        /// </summary>
        public static string InformacoesSobreInclusaoDeFotosNosServicosParaProfissionais {
            get {
                return ResourceManager.GetString("InformacoesSobreInclusaoDeFotosNosServicosParaProfissionais", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selecione “Sim” para profissionais que realizam serviços e que vão poder incluir fotos relacionadas aos mesmos. Este acesso é apenas para profissionais que utilizam o PAT e o Painel de Atendimento. Recepcionistas e Administradores sempre poderão incluir fotos de clientes através de outras telas..
        /// </summary>
        public static string InformacoesSobreInclusaoDeFotosNosServicosParaProfissionaisQueUtilizamPATePainel {
            get {
                return ResourceManager.GetString("InformacoesSobreInclusaoDeFotosNosServicosParaProfissionaisQueUtilizamPATePainel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Informe ao profissional!.
        /// </summary>
        public static string InformeAoProfissional {
            get {
                return ResourceManager.GetString("InformeAoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Informe aos seus clientes como eles deverão fazer os seus cancelamentos, como: Cancelamentos deverão ser realizados com, no mínimo, 30 minutos de antecedência, com o objetivo de não prejudicar a agenda do Profissional..
        /// </summary>
        public static string InformeAosSeusClientesComoDevemFazerCancelamentos {
            get {
                return ResourceManager.GetString("InformeAosSeusClientesComoDevemFazerCancelamentos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você precisa informar uma categoria para o tipo de despesa.
        /// </summary>
        public static string InformeCategoriaGrupo {
            get {
                return ResourceManager.GetString("InformeCategoriaGrupo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Informe o número de dias de antecedência para envio do SMS..
        /// </summary>
        public static string InformeONumeroDeDiasDeAntecedencia {
            get {
                return ResourceManager.GetString("InformeONumeroDeDiasDeAntecedencia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Informe o pagamento do seu boleto para desbloqueio imediato.
        /// </summary>
        public static string InformePagamentoDoBoletoParaDesbloqueio {
            get {
                return ResourceManager.GetString("InformePagamentoDoBoletoParaDesbloqueio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para incluir créditos para os clientes é necessário habilitar o &quot;Crédito de Cliente&quot; como forma de pagamento em Configurações &gt;&gt; Configurações do Sistema &gt;&gt; Forma de pagamento..
        /// </summary>
        public static string InstrucaoDeAtivacaoCreditoCliente {
            get {
                return ResourceManager.GetString("InstrucaoDeAtivacaoCreditoCliente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para incluir créditos para os clientes utilizando link de pagamento é necessário habilitar em Configurações &gt; Configurações do Sistema &gt; Formas de Pagamento marque a opção &quot;Pagamento Online por Link&quot;..
        /// </summary>
        public static string InstrucaoDeAtivacaoPagamentoPorLinkDePagamento {
            get {
                return ResourceManager.GetString("InstrucaoDeAtivacaoPagamentoPorLinkDePagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para incluir vales-presente é necessário utilizar a forma de pagamento &quot;Vale-Presente&quot;. Caso deseje utilizá-la, acesse Visualizar/Editar &gt; Site e Estabelecimento &gt; Dados do Estabelecimento e em Forma de Pagamento marque a opção &quot;Vale-Presente&quot;..
        /// </summary>
        public static string InstrucaoDeAtivacaoVendaValePresente {
            get {
                return ResourceManager.GetString("InstrucaoDeAtivacaoVendaValePresente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Já existe um estabelecimento com o nome fantasia informado..
        /// </summary>
        public static string JaExisteEstabelecimentoComNomeFantasiaInformado {
            get {
                return ResourceManager.GetString("JaExisteEstabelecimentoComNomeFantasiaInformado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Já existe um fabricante com esse nome..
        /// </summary>
        public static string JaExisteUmFabricanteComEsseNome {
            get {
                return ResourceManager.GetString("JaExisteUmFabricanteComEsseNome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Já existe um fornecedor com esse nome fantasia..
        /// </summary>
        public static string JaExisteUmFornecedorComEsseNomeFantasia {
            get {
                return ResourceManager.GetString("JaExisteUmFornecedorComEsseNomeFantasia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Já existe um tipo de despesa com a descrição informada!.
        /// </summary>
        public static string JaExisteUmTipoDeDespesaComADescricaoInformada {
            get {
                return ResourceManager.GetString("JaExisteUmTipoDeDespesaComADescricaoInformada", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eu já tenho cadastro no Trinks.
        /// </summary>
        public static string JaTenhoUmaContaNoTrinks {
            get {
                return ResourceManager.GetString("JaTenhoUmaContaNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O valor da comissão do profissional é calculado de acordo com o valor pago pelo cliente por cada serviço e produto vendido. Se o serviço/produto for uma cortesia ou possuir algum tipo de desconto que não deve influenciar na comissão do profissional, usuários com acesso total ao sistema poderão definir manualmente o valor da comissão no Relatório de Comissões..
        /// </summary>
        public static string LembreteAlteracaoDeComissao {
            get {
                return ResourceManager.GetString("LembreteAlteracaoDeComissao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Li e aceito os.
        /// </summary>
        public static string LiEAceito {
            get {
                return ResourceManager.GetString("LiEAceito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você já atingiu o limite de profissionais ativos com agenda de serviços permitidos pelo seu plano atual. Para adicionar novos profissionais com agenda de serviços entre em contato com o atendimento do Trinks para alterar a sua faixa de profissionais do plano. As alterações não serão salvas a menos que você indique que este profissional não realiza serviços..
        /// </summary>
        public static string LimiteDeFaixaAtingidoParaAssinaturaComBoletos {
            get {
                return ResourceManager.GetString("LimiteDeFaixaAtingidoParaAssinaturaComBoletos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ao ativar a agenda de serviços para este profissional, você estará ultrapassando o limite da faixa atual de &lt;br /&gt;profissionais e, portanto, a próxima cobrança no seu cartão de crédito será de {0}. Deseja continuar?.
        /// </summary>
        public static string LimiteDeFaixaAtingidoParaAssinaturaComCartaoDeCredito {
            get {
                return ResourceManager.GetString("LimiteDeFaixaAtingidoParaAssinaturaComCartaoDeCredito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to De {0} a {1} profissionais ......................... {2} mensais.
        /// </summary>
        public static string LinhaInvestimentoComLimiteInferiorESuperior {
            get {
                return ResourceManager.GetString("LinhaInvestimentoComLimiteInferiorESuperior", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Até {0} profissionais ......................... {1} mensais.
        /// </summary>
        public static string LinhaInvestimentoSemLimiteInferior {
            get {
                return ResourceManager.GetString("LinhaInvestimentoSemLimiteInferior", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} ou mais profissionais ......................... {1} mensais.
        /// </summary>
        public static string LinhaInvestimentoSemLimiteSuperior {
            get {
                return ResourceManager.GetString("LinhaInvestimentoSemLimiteSuperior", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No momento do envio, foi verificado que a situação financeira do estabelecimento não estava regularizada. Se já está tudo ok, edite a campanha e programe novo envio!.
        /// </summary>
        public static string MensagemAjudaCampanhaSmsEnvioSuspenso {
            get {
                return ResourceManager.GetString("MensagemAjudaCampanhaSmsEnvioSuspenso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não houve saldo de disparos suficiente para envio da mensagem para todos os clientes. Clique na lupa para verificar e se desejar, solicite o disparo depois de comprar crédito de SMS..
        /// </summary>
        public static string MensagemAjudaCampanhaSmsPendenteCredito {
            get {
                return ResourceManager.GetString("MensagemAjudaCampanhaSmsPendenteCredito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existem intervalos que não estão sendo cobertos pelas faixas de cobrança informadas. 
        ///Favor ajustar de maneira que as faixas compreendam todo o universo dos números naturais..
        /// </summary>
        public static string MensagemDeFaixasInvalidas {
            get {
                return ResourceManager.GetString("MensagemDeFaixasInvalidas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este plano está sendo assinado por {0} pessoa(s), sendo {1} assinaturas ativas e {2} assinaturas inativas..
        /// </summary>
        public static string MensagemDePlanoUtilizado {
            get {
                return ResourceManager.GetString("MensagemDePlanoUtilizado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deve haver pelo menos um serviço básico nos serviços inclusos neste plano de assinatura..
        /// </summary>
        public static string MensagemDeServicoBasico {
            get {
                return ResourceManager.GetString("MensagemDeServicoBasico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deve haver pelo menos um plano de assinatura ativo..
        /// </summary>
        public static string MensagemDeTodosPlanosInativos {
            get {
                return ResourceManager.GetString("MensagemDeTodosPlanosInativos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Todos os valores devem ser preenchidos..
        /// </summary>
        public static string MensagemDeValoresNaoPreenchidos {
            get {
                return ResourceManager.GetString("MensagemDeValoresNaoPreenchidos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Há informações não preenchidas corretamente..
        /// </summary>
        public static string MensagemErroPadrao {
            get {
                return ResourceManager.GetString("MensagemErroPadrao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nenhum resultado encontrado.
        /// </summary>
        public static string MensagemNenhumEncontrado {
            get {
                return ResourceManager.GetString("MensagemNenhumEncontrado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deve ser definido pelo menos um dia de trabalho..
        /// </summary>
        public static string MensagemSelecioneUmHorario {
            get {
                return ResourceManager.GetString("MensagemSelecioneUmHorario", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mês/ano.
        /// </summary>
        public static string MesAno {
            get {
                return ResourceManager.GetString("MesAno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mínimo de {0} caracteres.
        /// </summary>
        public static string MinimoDeXCaracteres {
            get {
                return ResourceManager.GetString("MinimoDeXCaracteres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Já existe um serviço cadastrado com esse nome!.
        /// </summary>
        public static string MsgJaExisteServicoComNome {
            get {
                return ResourceManager.GetString("MsgJaExisteServicoComNome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mude os dados do Cartão de Crédito.
        /// </summary>
        public static string MudeDadosDoCartaoCredito {
            get {
                return ResourceManager.GetString("MudeDadosDoCartaoCredito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Muitas vezes um ou mais profissionais realizam os mesmos serviços, com os mesmos percentuais de comissão, por exemplo: cabeleireiros e manicures.
        ///Se isso também acontece no seu estabelecimento, adicione um profissional e, nos seguintes, selecione a opção “Copiar lista de outro profissional”, na aba de Serviços. Isso vai deixar tudo ainda mais rápido!.
        /// </summary>
        public static string MuitasVezesProfissionaisRealizamMesmoServicoComMesmoPercentualDeComissao {
            get {
                return ResourceManager.GetString("MuitasVezesProfissionaisRealizamMesmoServicoComMesmoPercentualDeComissao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não é permitido informar letras ou caracteres especiais no número do endereço..
        /// </summary>
        public static string NaoEhPermitidoInformarLetrasOuCaracteresEspeciaisNoNumeroDoEndereco {
            get {
                return ResourceManager.GetString("NaoEhPermitidoInformarLetrasOuCaracteresEspeciaisNoNumeroDoEndereco", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não é possível alterar o acesso e as permissões do seu próprio usuário..
        /// </summary>
        public static string NaoEhPossivelAlterarAcessoAAgendaDoSeuProprioUsuario {
            get {
                return ResourceManager.GetString("NaoEhPossivelAlterarAcessoAAgendaDoSeuProprioUsuario", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não é possível realizar agendamento em recorrência para agendamentos que foram pagos antecipadamente.
        /// </summary>
        public static string NaoEhPossivelAlterarParaRecorrenteUmAgendamentoQueFoiPagoAntecipadamente {
            get {
                return ResourceManager.GetString("NaoEhPossivelAlterarParaRecorrenteUmAgendamentoQueFoiPagoAntecipadamente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não é possível excluir, pois é necessário que pelo menos um profissional tenha acesso à agenda!.
        /// </summary>
        public static string NaoEhPossivelExcluirPoisEhNecessarioUmProfissionalComAcessoAAgenda {
            get {
                return ResourceManager.GetString("NaoEhPossivelExcluirPoisEhNecessarioUmProfissionalComAcessoAAgenda", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este fechamento já foi realizado..
        /// </summary>
        public static string NaoEhPossivelRealizarCheckoutNovamente {
            get {
                return ResourceManager.GetString("NaoEhPossivelRealizarCheckoutNovamente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O pagamento deste atendimento já foi realizado. Por isso, não é possível realizar alterações neste horário. Atualize a página para ver os dados recentes..
        /// </summary>
        public static string NaoEhPossivelRealizarPagamentosDeServicosJaPagos {
            get {
                return ResourceManager.GetString("NaoEhPossivelRealizarPagamentosDeServicosJaPagos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não foram encontrados agendamentos para os filtros aplicados..
        /// </summary>
        public static string NaoEncontrouAgendamentos {
            get {
                return ResourceManager.GetString("NaoEncontrouAgendamentos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não encontrou o local onde já é cliente e gostaria de agendar?.
        /// </summary>
        public static string NaoEncontrouLocalOndeEhClienteEGostariaDeAgendar {
            get {
                return ResourceManager.GetString("NaoEncontrouLocalOndeEhClienteEGostariaDeAgendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não encontrou o estabelecimento que procurava? Clique aqui e nos ajude a deixá-lo nos.
        /// </summary>
        public static string NaoEncontrouOEstabelecimentoQueProcurava {
            get {
                return ResourceManager.GetString("NaoEncontrouOEstabelecimentoQueProcurava", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O pagamento deste atendimento já foi realizado. Por isso, não é possível realizar alterações neste horário. Atualize a página para ver os dados recentes..
        /// </summary>
        public static string NaoEPossivelRealizarAlteracoesEmUmAgendamentoQueJaFoiPago {
            get {
                return ResourceManager.GetString("NaoEPossivelRealizarAlteracoesEmUmAgendamentoQueJaFoiPago", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não é possivel remover todos os serviços do estabelecimento, para remover esse serviço cadastre primeiro um novo serviço..
        /// </summary>
        public static string NaoEPossivelRemoverTodosOsServicosDoEstabelecimento {
            get {
                return ResourceManager.GetString("NaoEPossivelRemoverTodosOsServicosDoEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não é possível visualizar as avaliações deste estabelecimento..
        /// </summary>
        public static string NaoEPossivelVerAvaliacoesDesteEstabelecimento {
            get {
                return ResourceManager.GetString("NaoEPossivelVerAvaliacoesDesteEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não foram encontrados clientes para os filtros aplicados..
        /// </summary>
        public static string NaoForamEncontradosClientesParaOsFiltrosAplicados {
            get {
                return ResourceManager.GetString("NaoForamEncontradosClientesParaOsFiltrosAplicados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não foram encontrados produtos para os filtros aplicados..
        /// </summary>
        public static string NaoForamEncontradosProdutosParaOsFiltrosAplicados {
            get {
                return ResourceManager.GetString("NaoForamEncontradosProdutosParaOsFiltrosAplicados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não foram encontrados profissionais para os filtros aplicados..
        /// </summary>
        public static string NaoForamEncontradosProfissionaisParaOsFiltrosAplicados {
            get {
                return ResourceManager.GetString("NaoForamEncontradosProfissionaisParaOsFiltrosAplicados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não foram encontrados serviços para os filtros aplicados.
        /// </summary>
        public static string NaoForamEncontradosServicosParaOsFiltrosAplicados {
            get {
                return ResourceManager.GetString("NaoForamEncontradosServicosParaOsFiltrosAplicados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não há categorias padrões cadastradas.
        /// </summary>
        public static string NaoHaCategoriasPadroes {
            get {
                return ResourceManager.GetString("NaoHaCategoriasPadroes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não há clientes em atendimento.
        /// </summary>
        public static string NaoHaClientesEmAtendimento {
            get {
                return ResourceManager.GetString("NaoHaClientesEmAtendimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não há serviços padrões cadastrados.
        /// </summary>
        public static string NaoHaServicosPadroes {
            get {
                return ResourceManager.GetString("NaoHaServicosPadroes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não foi identificado o pagamento do seu boleto..
        /// </summary>
        public static string NaoIdentificadoPagamentoBoleto {
            get {
                return ResourceManager.GetString("NaoIdentificadoPagamentoBoleto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não perca os benefícios que o Trinks traz pro seu Estabelecimento!.
        /// </summary>
        public static string NaoPercaBeneficiosTrinksParaSeuEstabelecimento {
            get {
                return ResourceManager.GetString("NaoPercaBeneficiosTrinksParaSeuEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não possui cartão de crédito?.
        /// </summary>
        public static string NaoPossuiCartaoDeCredito {
            get {
                return ResourceManager.GetString("NaoPossuiCartaoDeCredito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não recebi o código de confirmação.
        /// </summary>
        public static string NaoRecebiCodigoConfirmacao {
            get {
                return ResourceManager.GetString("NaoRecebiCodigoConfirmacao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Não será possível realizar a ação, pois é necessário ter pelo menos um profissional associado a um serviço ativo do estabelecimento..
        /// </summary>
        public static string NaoSeraPossivelRealizarAAcaoPoisENecessarioTerPeloMenosUmProfissionalAssociadoAServico {
            get {
                return ResourceManager.GetString("NaoSeraPossivelRealizarAAcaoPoisENecessarioTerPeloMenosUmProfissionalAssociadoASe" +
                        "rvico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eu não tenho cadastro no Trinks.
        /// </summary>
        public static string NaoTenhoUmaContaNoTrinks {
            get {
                return ResourceManager.GetString("NaoTenhoUmaContaNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to É necessário informar um valor superior a zero para todos os itens com resgate ativado..
        /// </summary>
        public static string NecessarioInformarValorResgateParaTodosOsItens {
            get {
                return ResourceManager.GetString("NecessarioInformarValorResgateParaTodosOsItens", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nenhum resultado encontrado..
        /// </summary>
        public static string NenhumRegistroEncontrado {
            get {
                return ResourceManager.GetString("NenhumRegistroEncontrado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nenhum resultado encontrado.
        /// </summary>
        public static string NenhumResultadoEncontrado {
            get {
                return ResourceManager.GetString("NenhumResultadoEncontrado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hoje você tem {1}
        ///{0}: {2} ou {3}.
        /// </summary>
        public static string NotificacaoLembreteAgendamento {
            get {
                return ResourceManager.GetString("NotificacaoLembreteAgendamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} às {1} {2}.
        /// </summary>
        public static string NotificacaoLembreteAgendamentoItem {
            get {
                return ResourceManager.GetString("NotificacaoLembreteAgendamentoItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nova senha gerada para o cliente.
        /// </summary>
        public static string NovaSenhaGeradaParaCliente {
            get {
                return ResourceManager.GetString("NovaSenhaGeradaParaCliente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b class=&quot;destaque&quot;&gt;Novo!&lt;/b&gt; Agora você pode ver o seu fluxo financeiro por forma de pagamento. Mas não é só isso! Você também consegue consultar:&lt;/p&gt;&lt;p&gt;&lt;b&gt;Valor a ser recebido&lt;/b&gt;: é o valor final que a operadora de cartão (de crédito ou de débito) vai depositar na sua conta, já descontando o percentual cobrado pela operadora.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Data prevista de recebimento&lt;/b&gt;:  é a data prevista para você receber da operadora o valor desta transação, na sua conta.&lt;/p&gt;&lt;p&gt;Para desfrutar completamente deste relat [rest of string was truncated]&quot;;.
        /// </summary>
        public static string NovoFluxoFinanceiroPorFormaPagamento {
            get {
                return ResourceManager.GetString("NovoFluxoFinanceiroPorFormaPagamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;p&gt;&lt;b class=&quot;destaque&quot;&gt;Novo!&lt;/b&gt;  Para cada forma de pagamento, você agora pode informar:&lt;/p&gt;&lt;p&gt;&lt;b&gt;% cobrado pela operadora&lt;/b&gt;: é o valor percentual que a operadora do cartão (de crédito ou de débito) cobra por transação realizada. &lt;/p&gt;&lt;p&gt;&lt;b&gt;Nº de dias para recebimento&lt;/b&gt;: é o número de dias que a operadora do cartão leva para depositar o dinheiro na sua conta.&lt;/p&gt;&lt;p&gt;Preencha esses novos campos e confira nosso novo relatório: &quot;Fluxo Financeiro por Forma de Pagamento&quot;&lt;/p&gt;.
        /// </summary>
        public static string NovoParaCadaFormaPagamentoAgoraVocePodeInformar {
            get {
                return ResourceManager.GetString("NovoParaCadaFormaPagamentoAgoraVocePodeInformar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Obrigada por se cadastrar no Trinks! Enviamos um e-mail de confirmação para.
        /// </summary>
        public static string ObrigadaPorSeCadastrarEnviamosEmailDeConfirmacao {
            get {
                return ResourceManager.GetString("ObrigadaPorSeCadastrarEnviamosEmailDeConfirmacao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo &quot;e-mail&quot; é obrigatório!.
        /// </summary>
        public static string OCampoEmailEObrigatorio {
            get {
                return ResourceManager.GetString("OCampoEmailEObrigatorio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo Onde Procurar é obrigatório! É preciso preenchê-lo para realizar a busca..
        /// </summary>
        public static string OCampoOndeProcurarEhObrigatorio {
            get {
                return ResourceManager.GetString("OCampoOndeProcurarEhObrigatorio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O CNPJ indicado já está cadastrado no sistema..
        /// </summary>
        public static string OCnpjIndicadoJaEstaCadastradoNoSistema {
            get {
                return ResourceManager.GetString("OCnpjIndicadoJaEstaCadastradoNoSistema", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O cpf é obrigatório pois é ele que tornará o profissional único no sistema..
        /// </summary>
        public static string OCpfEhObrigatorioParaTornarOProfissionalUnicoNoSistema {
            get {
                return ResourceManager.GetString("OCpfEhObrigatorioParaTornarOProfissionalUnicoNoSistema", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O envio dos {0} está inativo. Deseja ativá-lo em outro momento?.
        /// </summary>
        public static string OEnvioDosSmsEstaInativo {
            get {
                return ResourceManager.GetString("OEnvioDosSmsEstaInativo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome do tipo de despesa é obrigatório!.
        /// </summary>
        public static string ONomeDoTipoDespesaEhObrigatorio {
            get {
                return ResourceManager.GetString("ONomeDoTipoDespesaEhObrigatorio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome do tipo de despesa não pode ultrapassar 200 caracteres.
        /// </summary>
        public static string ONomeDoTipoDespesaNaoPodeUltrapassar200Caracteres {
            get {
                return ResourceManager.GetString("ONomeDoTipoDespesaNaoPodeUltrapassar200Caracteres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome do fabricante é obrigatório!.
        /// </summary>
        public static string ONomeFabricanteEhObrigatorio {
            get {
                return ResourceManager.GetString("ONomeFabricanteEhObrigatorio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome do fabricante não pode ultrapassar 55 caracteres..
        /// </summary>
        public static string ONomeFabricanteNaoPodeUltrapassar55Caracteres {
            get {
                return ResourceManager.GetString("ONomeFabricanteNaoPodeUltrapassar55Caracteres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome fantasia é obrigatório.
        /// </summary>
        public static string ONomeFantasiaEObrigatorio {
            get {
                return ResourceManager.GetString("ONomeFantasiaEObrigatorio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome fantasia do fornecedor é obrigatório!.
        /// </summary>
        public static string ONomeFantasiaFornecedorEhObrigatorio {
            get {
                return ResourceManager.GetString("ONomeFantasiaFornecedorEhObrigatorio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome fantasia não pode ultrapassar 55 caracteres.
        /// </summary>
        public static string ONomeFantasiaNaoPodeUltrapassar55Caracteres {
            get {
                return ResourceManager.GetString("ONomeFantasiaNaoPodeUltrapassar55Caracteres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O PAT é o painel de atendimento onde os profissionais lançam e atualizam serviços realizados. O acesso é através de um código próprio do profissional e este não precisa necessariamente ter acesso ao sistema através de seu email. Apenas profissionais que executam serviços têm acesso ao PAT..
        /// </summary>
        public static string OPatEhOPainelDeAtendimentoOndeOsProfissionaisLancamServicosRealizados {
            get {
                return ResourceManager.GetString("OPatEhOPainelDeAtendimentoOndeOsProfissionaisLancamServicosRealizados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Opção restrita para usuários com acesso total..
        /// </summary>
        public static string OpcaoRestritaParaUsuariosComAcessoTotal {
            get {
                return ResourceManager.GetString("OpcaoRestritaParaUsuariosComAcessoTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Opcional.
        /// </summary>
        public static string Opcional {
            get {
                return ResourceManager.GetString("Opcional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Essa operação não poderá ser desfeita, confima a operação?.
        /// </summary>
        public static string OperacaoNaoPoderaSerDesfeita {
            get {
                return ResourceManager.GetString("OperacaoNaoPoderaSerDesfeita", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O percentual da comissão deve estar entre 0% e 100%..
        /// </summary>
        public static string OPercentualDasComissoesDevemEstarEntre0E100 {
            get {
                return ResourceManager.GetString("OPercentualDasComissoesDevemEstarEntre0E100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Os dados foram salvos com sucesso!.
        /// </summary>
        public static string OsDadosForamSalvosComSucesso {
            get {
                return ResourceManager.GetString("OsDadosForamSalvosComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O campo serviço é obrigatório para salvar o agendamento.
        /// </summary>
        public static string OServicoEhObrigatorioParaSalvarOAgendamento {
            get {
                return ResourceManager.GetString("OServicoEhObrigatorioParaSalvarOAgendamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O valor devido é de: R$.
        /// </summary>
        public static string OValorDevidoEhDeRS {
            get {
                return ResourceManager.GetString("OValorDevidoEhDeRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pacote com itens já consumidos em outro fechamento. Antes de estornar esta conta, 
        ///é necessário estornar os fechamentos que possuem consumo deste pacote. O estorno não poderá ser realizado..
        /// </summary>
        public static string PacoteComItensJaConsumidosEmOutroFechamento {
            get {
                return ResourceManager.GetString("PacoteComItensJaConsumidosEmOutroFechamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pagamento não confirmado pelo banco.
        /// </summary>
        public static string PagamentoNaoConfirmadoPeloBanco {
            get {
                return ResourceManager.GetString("PagamentoNaoConfirmadoPeloBanco", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O pagamento do plano de assinatura foi.
        /// </summary>
        public static string PagamentoPlanoAssinaturaFoi {
            get {
                return ResourceManager.GetString("PagamentoPlanoAssinaturaFoi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para acessar o sistema via login, é necessário que o profissional tenha e-mail.
        /// </summary>
        public static string ParaAcessarSistemaNecessarioEmailDoProfissional {
            get {
                return ResourceManager.GetString("ParaAcessarSistemaNecessarioEmailDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to para adicionar um novo serviço, escolhendo um dos itens da nossa lista padrão ou cadastrando um Novo Serviço..
        /// </summary>
        public static string ParaAdicionarUmNovoServicoEscolhendoItensDaListaPadraoOuCadastrandoNovoServico {
            get {
                return ResourceManager.GetString("ParaAdicionarUmNovoServicoEscolhendoItensDaListaPadraoOuCadastrandoNovoServico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para adicionar um profissional, clique em.
        /// </summary>
        public static string ParaAdicionarUmProfissionalCliqueEm {
            get {
                return ResourceManager.GetString("ParaAdicionarUmProfissionalCliqueEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para que seu site apareça no Portal Trinks e fique disponível para seus atuais e novos clientes, você precisa adicionar a logomarca e pelo menos uma foto do seu estabelecimento..
        /// </summary>
        public static string ParaAparecerNoPortalTrinksAdicioneLogoEFotos {
            get {
                return ResourceManager.GetString("ParaAparecerNoPortalTrinksAdicioneLogoEFotos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para baixar o boleto para pagamento clique aqui.
        /// </summary>
        public static string ParaBaixarBoletoParaPagamentoCliqueAqui {
            get {
                return ResourceManager.GetString("ParaBaixarBoletoParaPagamentoCliqueAqui", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parabéns por escolher o Trinks.
        /// </summary>
        public static string ParabensPorEscolherOTrinks {
            get {
                return ResourceManager.GetString("ParabensPorEscolherOTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to para copiar o valor de comissão para todos os serviços..
        /// </summary>
        public static string ParaCopiarValorComissaoParaTodosServicos {
            get {
                return ResourceManager.GetString("ParaCopiarValorComissaoParaTodosServicos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para editar um serviço clique sobre ele ou em Editar.
        /// </summary>
        public static string ParaEditarServicoCliqueSobreEleOuEmEditar {
            get {
                return ResourceManager.GetString("ParaEditarServicoCliqueSobreEleOuEmEditar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para enviar uma nova senha para o e-mail do profissional, clique em “Enviar senha para o e-mail do Profissional”, localizado embaixo do campo de e-mail..
        /// </summary>
        public static string ParaEnviarNovaSenhaParaEmailDoProfissionalCliqueEm {
            get {
                return ResourceManager.GetString("ParaEnviarNovaSenhaParaEmailDoProfissionalCliqueEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para evitar que os clientes marquem uma hora com muita antecedência, você pode limitar qual tempo futuro o cliente terá para agendar no seu estabelecimento. Por exemplo: Se você escolher 1 semana, o cliente só poderá marcar uma hora pelo site de hoje até daqui a sete dias, e assim por diante..
        /// </summary>
        public static string ParaEvitarQueClientesMarquemHoraComMuitaAntecedencia {
            get {
                return ResourceManager.GetString("ParaEvitarQueClientesMarquemHoraComMuitaAntecedencia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para evitar que isso ocorra, regularize o pagamento da fatura:.
        /// </summary>
        public static string ParaEvitarQueIssoOcorraRegularizeFatura {
            get {
                return ResourceManager.GetString("ParaEvitarQueIssoOcorraRegularizeFatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to e para excluí-lo, basta clicar em Excluir.
        /// </summary>
        public static string ParaExcluirBastaClicarEmExcluir {
            get {
                return ResourceManager.GetString("ParaExcluirBastaClicarEmExcluir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para pesquisar outro CPF você perderá as informações deste cadastro. Deseja continuar?.
        /// </summary>
        public static string ParaPesquisarNovoCPFVocePerderaInformacoesDesteCadastro {
            get {
                return ResourceManager.GetString("ParaPesquisarNovoCPFVocePerderaInformacoesDesteCadastro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para que o cadastro dos serviços fique ainda mais rápido sugerimos.
        /// </summary>
        public static string ParaQueCadastroDeServicosFiqueAindaMaisRapido {
            get {
                return ResourceManager.GetString("ParaQueCadastroDeServicosFiqueAindaMaisRapido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para que o seu site apareça na busca do portal Trinks.com e no aplicativo mobile é necessário que as seguintes informações estejam devidamente preenchidas:.
        /// </summary>
        public static string ParaQueSeuSiteAparecaNaBuscaEhNecessarioAsSeguintesInformacoesPreenchidas {
            get {
                return ResourceManager.GetString("ParaQueSeuSiteAparecaNaBuscaEhNecessarioAsSeguintesInformacoesPreenchidas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to para que sua conta não seja bloqueada..
        /// </summary>
        public static string ParaQueSuaContaNaoSejaBloqueada {
            get {
                return ResourceManager.GetString("ParaQueSuaContaNaoSejaBloqueada", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to para realizar o seu pagamento no cartão.
        /// </summary>
        public static string ParaRealizarOSeuPagamentoNoCartao {
            get {
                return ResourceManager.GetString("ParaRealizarOSeuPagamentoNoCartao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para reativar seu acesso, regularize o pagamento da fatura.
        /// </summary>
        public static string ParaReativarAcessoRegularizeFatura {
            get {
                return ResourceManager.GetString("ParaReativarAcessoRegularizeFatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para replicar uma despesa é necessário que o mês/ano seja de uma data superior à Data de Vencimento!.
        /// </summary>
        public static string ParaReplicarUmaDespesaEhNecessarioQueOMesAnoSejaDataSuperiorDataVencimento {
            get {
                return ResourceManager.GetString("ParaReplicarUmaDespesaEhNecessarioQueOMesAnoSejaDataSuperiorDataVencimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para aparecer na.
        /// </summary>
        public static string ParaSeuQueSeuSiteAparecaNa {
            get {
                return ResourceManager.GetString("ParaSeuQueSeuSiteAparecaNa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to do Trinks.com é necessario cadastrar.
        /// </summary>
        public static string ParaSeuQueSeuSiteAparecaNaParte2 {
            get {
                return ResourceManager.GetString("ParaSeuQueSeuSiteAparecaNaParte2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pedido de agendamento realizado!.
        /// </summary>
        public static string PedidoDeAgendamentoRealizado {
            get {
                return ResourceManager.GetString("PedidoDeAgendamentoRealizado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Movimentações com {0} entre {1:dd/MM/yyyy} e {2:dd/MM/yyyy}.
        /// </summary>
        public static string PeriodoMovimentacao {
            get {
                return ResourceManager.GetString("PeriodoMovimentacao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permitir que o profissional acesse a agenda com seus horários marcados.
        /// </summary>
        public static string PermitirProfissionalAcessarAgendaComSeusHorariosMarcados {
            get {
                return ResourceManager.GetString("PermitirProfissionalAcessarAgendaComSeusHorariosMarcados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to pesquisar cpf.
        /// </summary>
        public static string PesquisarCPF {
            get {
                return ResourceManager.GetString("PesquisarCPF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to pesquisar novo cpf.
        /// </summary>
        public static string PesquisarNovoCPF {
            get {
                return ResourceManager.GetString("PesquisarNovoCPF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plano de Assinatura criado com sucesso!.
        /// </summary>
        public static string PlanoDeAssinaturaCriadoComSucesso {
            get {
                return ResourceManager.GetString("PlanoDeAssinaturaCriadoComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to pode fazer por você.
        /// </summary>
        public static string PodeFazerPorVoce {
            get {
                return ResourceManager.GetString("PodeFazerPorVoce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to política de privacidade.
        /// </summary>
        public static string PoliticaDePrivacidade {
            get {
                return ResourceManager.GetString("PoliticaDePrivacidade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Por favor, envie o comprovante para.
        /// </summary>
        public static string PorFavorEnvieComprovantePara {
            get {
                return ResourceManager.GetString("PorFavorEnvieComprovantePara", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Por que você deseja encerrar sua assinatura no Trinks?.
        /// </summary>
        public static string PorqueVoceDesejaCancelarAssinatura {
            get {
                return ResourceManager.GetString("PorqueVoceDesejaCancelarAssinatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O preço devido é de.
        /// </summary>
        public static string PrecoDevidolEh {
            get {
                return ResourceManager.GetString("PrecoDevidolEh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preencha a confirmação do e-mail.
        /// </summary>
        public static string Preencha_Confirmacao_Email {
            get {
                return ResourceManager.GetString("Preencha_Confirmacao_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preencha E-mail.
        /// </summary>
        public static string Preencha_Email {
            get {
                return ResourceManager.GetString("Preencha_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to « Preencha a URL do Hotsite.
        /// </summary>
        public static string Preencha_Url_Hotsite {
            get {
                return ResourceManager.GetString("Preencha_Url_Hotsite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O programa de fidelidade do seu estabelecimento não está configurado. Para configurá-lo, vá em &quot;Programa de Fidelidade &gt; Configuração&quot;..
        /// </summary>
        public static string ProgramaDeFidelidadeNaoConfigurado {
            get {
                return ResourceManager.GetString("ProgramaDeFidelidadeNaoConfigurado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A quantidade de caracteres do SMS aniversariante é maior que o permitido.
        /// </summary>
        public static string QuantidadeCaracteresSmsAniversarianteExcedido {
            get {
                return ResourceManager.GetString("QuantidadeCaracteresSmsAniversarianteExcedido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QUEM JÁ ESTÁ NOS.
        /// </summary>
        public static string QuemJaEstaNos {
            get {
                return ResourceManager.GetString("QuemJaEstaNos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QUEM USA O.
        /// </summary>
        public static string QuemUsaO {
            get {
                return ResourceManager.GetString("QuemUsaO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quer falar algo sobre o salão ou mandar uma mensagem para ele?.
        /// </summary>
        public static string QuerNosFalarAlgumaCoisaSobreOEstabelecimento {
            get {
                return ResourceManager.GetString("QuerNosFalarAlgumaCoisaSobreOEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quero aparecer no Aplicativo e no Portal do Trinks.com.
        /// </summary>
        public static string QueroAparecerNoTrinks {
            get {
                return ResourceManager.GetString("QueroAparecerNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quero receber newsletters com notícias, eventos e informações do Trinks e seus parceiros.
        /// </summary>
        public static string QueroReceberNewsletters {
            get {
                return ResourceManager.GetString("QueroReceberNewsletters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realizada com sucesso.
        /// </summary>
        public static string RealizadaSucesso {
            get {
                return ResourceManager.GetString("RealizadaSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reative sua assinatura fazendo um novo plano de assinatura!.
        /// </summary>
        public static string ReativeSuaAssinatura {
            get {
                return ResourceManager.GetString("ReativeSuaAssinatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deseja realmente recalcular todas as comissões de atendimentos e vendas de pacotes e produtos realizados a partir do dia {0} dos profissionais selecionados?.
        /// </summary>
        public static string RecalculoComissaoConfirmacao1 {
            get {
                return ResourceManager.GetString("RecalculoComissaoConfirmacao1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TODAS AS COMISSÕES DE ATENDIMENTOS E VENDAS DE PRODUTOS REALIZADOS A PARTIR DO DIA {0} DOS PROFISSIONAIS 
        ///SELECIONADOS, EXCETO AS COMISSÕES COM VALORES INFORMADOS NO RELATÓRIO DE COMISSÕES,  \\nSERÃO RECALCULADAS COM OS PERCENTUAIS DE COMISSÃO E VALORES DE DESCARTÁVEIS ATUAIS.\\nDeseja prosseguir?.
        /// </summary>
        public static string RecalculoComissaoConfirmacao2 {
            get {
                return ResourceManager.GetString("RecalculoComissaoConfirmacao2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esta funcionalidade recalcula todas as comissões atuais dos profissionais selecionados a partir da data informada com base nos valores..
        /// </summary>
        public static string RecalculoComissaoMensagem {
            get {
                return ResourceManager.GetString("RecalculoComissaoMensagem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recálculo das comissões realizado com sucesso..
        /// </summary>
        public static string RecalculoComissaoSucesso {
            get {
                return ResourceManager.GetString("RecalculoComissaoSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reinicie seu plano de assinatura quando quiser. Os dados do estabelecimento ficarão salvos por 1 ano..
        /// </summary>
        public static string ReiniciaPlanoAssinatura {
            get {
                return ResourceManager.GetString("ReiniciaPlanoAssinatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existem {0} de comissões com data de liberação neste período que tiveram seu atendimento no mês anterior. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores..
        /// </summary>
        public static string RelatorioComissaoComResiduosDoPeriodoAnterior {
            get {
                return ResourceManager.GetString("RelatorioComissaoComResiduosDoPeriodoAnterior", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existem {0} de comissões com data de liberação neste período que tiveram seu atendimento no mês anterior e {1} de atendimento deste período com data de liberação para o próximo mês. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores..
        /// </summary>
        public static string RelatorioComissaoComResiduosNoPeriodoAnteriorENoProximoPeriodo {
            get {
                return ResourceManager.GetString("RelatorioComissaoComResiduosNoPeriodoAnteriorENoProximoPeriodo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existem {0} de comissões com data de atendimento deste período com data de liberação para o próximo mês. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores..
        /// </summary>
        public static string RelatorioComissaoComResiduosParaOProximoPeriodo {
            get {
                return ResourceManager.GetString("RelatorioComissaoComResiduosParaOProximoPeriodo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resolveu o problema com a financeira/banco? Para reprocessar o pagamento da fatura pendente, clique aqui.
        /// </summary>
        public static string ResolveuPendenciasAtualizeDados {
            get {
                return ResourceManager.GetString("ResolveuPendenciasAtualizeDados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Opa, a gente agenda por aqui com até {0} dias de antecedência.
        ///Visita a gente antes, vai! É só escolher uma data nova. :p.
        /// </summary>
        public static string RestricaoAgendamento {
            get {
                return ResourceManager.GetString("RestricaoAgendamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restrinja seus filtros para o recálculo aumentando a data ou diminuindo o número de profissionais.
        ///A quantidade de itens a serem recalculados é de {0}, porém o máximo é {1}..
        /// </summary>
        public static string RestrinjaFiltrosRecalculo {
            get {
                return ResourceManager.GetString("RestrinjaFiltrosRecalculo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to São os 4 primeiros dígitos do CPF do profissional. Deverá ser alterado no próximo acesso ao PAT..
        /// </summary>
        public static string SaoOs4PrimeirosDigitosDoCPFDoProfissional {
            get {
                return ResourceManager.GetString("SaoOs4PrimeirosDigitosDoCPFDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se desejar que o nome do cliente saia no {0}, coloque o texto [cliente] (entre colchetes) na posição desejada. No momento do envio, o primeiro e segundo nomes substituirão o texto [cliente]..
        /// </summary>
        public static string SeDesejarQueONomeDoClienteSaiaNoSms {
            get {
                return ResourceManager.GetString("SeDesejarQueONomeDoClienteSaiaNoSms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se você já efetuou o pagamento do seu boleto clique aqui para desbloqueio imediato.
        /// </summary>
        public static string SeJaEfetuouPagamentoDoSeuBoletoCliqueAquiParaDesbloqueioImediato {
            get {
                return ResourceManager.GetString("SeJaEfetuouPagamentoDoSeuBoletoCliqueAquiParaDesbloqueioImediato", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seleciona e agenda onde vai realizar o serviço, com que profissional, e em qual dia e horário.
        /// </summary>
        public static string SelecionaEAgendaOndeVaiRealizarOServico {
            get {
                return ResourceManager.GetString("SelecionaEAgendaOndeVaiRealizarOServico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selecionamos abaixo os serviços mais comuns oferecidos pelo seu tipo de estabelecimento..
        /// </summary>
        public static string SelecionamosAbaixoServicosMaisComunsOferecidosPeloSeuTipoDeEstabelecimento {
            get {
                return ResourceManager.GetString("SelecionamosAbaixoServicosMaisComunsOferecidosPeloSeuTipoDeEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selecione abaixo o tipo de acesso deste profissional. Para que o profissional possa ter acesso, é necessário informar um e-mail para ele. Este e-mail será o login de acesso deste profissional e automaticamente uma senha será enviada para ele..
        /// </summary>
        public static string SelecioneAbaixoTipoDeAcessoDoProfissional {
            get {
                return ResourceManager.GetString("SelecioneAbaixoTipoDeAcessoDoProfissional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marque pelo menos uma opção.
        /// </summary>
        public static string SelecionePeloMenosUmaOpcao {
            get {
                return ResourceManager.GetString("SelecionePeloMenosUmaOpcao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to « Selecione Sexo.
        /// </summary>
        public static string SelecioneSexo {
            get {
                return ResourceManager.GetString("SelecioneSexo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deve ser definido pelo menos um dia de trabalho para o profissional..
        /// </summary>
        public static string SelecioneUmHorario {
            get {
                return ResourceManager.GetString("SelecioneUmHorario", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deve ser selecionado pelo menos um serviço..
        /// </summary>
        public static string SelecioneUmServico {
            get {
                return ResourceManager.GetString("SelecioneUmServico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sem acesso ao sistema através de login (e-mail).
        /// </summary>
        public static string SemAcessoAoSistema {
            get {
                return ResourceManager.GetString("SemAcessoAoSistema", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A senha do profissional foi enviada com sucesso!.
        /// </summary>
        public static string SenhaDoProfissionalEnviadaComSucesso {
            get {
                return ResourceManager.GetString("SenhaDoProfissionalEnviadaComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Senha enviada com sucesso.
        /// </summary>
        public static string SenhaEnviadaComSucesso {
            get {
                return ResourceManager.GetString("SenhaEnviadaComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se o profissional realizar serviços selecione-os e informe o percentual de comissão..
        /// </summary>
        public static string SeProfissionalRealizaServicosInformePercentualDeComissao {
            get {
                return ResourceManager.GetString("SeProfissionalRealizaServicosInformePercentualDeComissao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se o profissional tiver e-mail, você poderá permitir que ele receba um e-mail toda vez que um agendamento for marcado para ele, clicando em “Copiar o profissional nos emails para clientes sobre seus agendamentos”..
        /// </summary>
        public static string SeProfissionalTiverEmailPodeReceberEmailAgendamento {
            get {
                return ResourceManager.GetString("SeProfissionalTiverEmailPodeReceberEmailAgendamento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serviço(s) associado(s) com sucesso!.
        /// </summary>
        public static string ServicoAssociado {
            get {
                return ResourceManager.GetString("ServicoAssociado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Este serviço não pode ser editado pois está associado a um ou mais planos de assinatura..
        /// </summary>
        public static string ServicoAssociadoAPlanoDeAssinatura {
            get {
                return ResourceManager.GetString("ServicoAssociadoAPlanoDeAssinatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serviço criado com sucesso!.
        /// </summary>
        public static string ServicoCriadoComSucesso {
            get {
                return ResourceManager.GetString("ServicoCriadoComSucesso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serviço(s) já associado(s) ao estabelecimento!.
        /// </summary>
        public static string ServicoJaAssociado {
            get {
                return ResourceManager.GetString("ServicoJaAssociado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu acesso ao Trinks está liberado.
        /// </summary>
        public static string SeuAcessoAoTrinksEstaLiberado {
            get {
                return ResourceManager.GetString("SeuAcessoAoTrinksEstaLiberado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu acesso foi encerrado dia.
        /// </summary>
        public static string SeuAcessoFoiEncerradoDia {
            get {
                return ResourceManager.GetString("SeuAcessoFoiEncerradoDia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu acesso será encerrado dia.
        /// </summary>
        public static string SeuAcessoSeraEncerradoDia {
            get {
                return ResourceManager.GetString("SeuAcessoSeraEncerradoDia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu estabelecimento está cadastrado no Trinks. Para configurar a agenda e criar o seu site, complete os 4 passos a seguir..
        /// </summary>
        public static string SeuEstabelecimentoEstaCadastradoNoTrinks {
            get {
                return ResourceManager.GetString("SeuEstabelecimentoEstaCadastradoNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu período atual termina em.
        /// </summary>
        public static string SeuPeriodoAtualTerminaEm {
            get {
                return ResourceManager.GetString("SeuPeriodoAtualTerminaEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seus dias de acesso ao Trinks terminaram.
        /// </summary>
        public static string SeusDiasDeAcessoAoTrinks {
            get {
                return ResourceManager.GetString("SeusDiasDeAcessoAoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seus dias de gratuidade.
        /// </summary>
        public static string SeusDiasDeGratuidade {
            get {
                return ResourceManager.GetString("SeusDiasDeGratuidade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu site está aparecendo na busca do portal Trinks.com e no aplicativo mobile desde {0}.
        /// </summary>
        public static string SeuSiteEstaAaparecendoNaBuscaDoPortalDesde {
            get {
                return ResourceManager.GetString("SeuSiteEstaAaparecendoNaBuscaDoPortalDesde", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu Site na Internet.
        /// </summary>
        public static string SeuSiteNaInternet {
            get {
                return ResourceManager.GetString("SeuSiteNaInternet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string SeusProfissionais {
            get {
                return ResourceManager.GetString("SeusProfissionais", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seus profissionais (recomendamos que possuam foto).
        /// </summary>
        public static string SeusProfissionaisRecomendamentosQuePossuamFoto {
            get {
                return ResourceManager.GetString("SeusProfissionaisRecomendamentosQuePossuamFoto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seus serviços e preços.
        /// </summary>
        public static string SeusServicosEPrecos {
            get {
                return ResourceManager.GetString("SeusServicosEPrecos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to se você não encontrou o que procurava na lista acima.
        /// </summary>
        public static string SeVoceNaoEncontrouOQueProcuravaNaListaAcima {
            get {
                return ResourceManager.GetString("SeVoceNaoEncontrouOQueProcuravaNaListaAcima", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Se você já possui um site e gostaria de incluir um link para ele no site que o Trinks vai criar para você, digite aqui o endereço.
        /// </summary>
        public static string SeVocePossuiUmSiteEGostariaDeIncluirLink {
            get {
                return ResourceManager.GetString("SeVocePossuiUmSiteEGostariaDeIncluirLink", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMARTPHONE E TABLET.
        /// </summary>
        public static string SmartPhoneETablet {
            get {
                return ResourceManager.GetString("SmartPhoneETablet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Só é possível carregar valores a partir de {0:MMMM} de {0:yyyy}.
        /// </summary>
        public static string SoEhPossivelCarregarValoresAPartirDe {
            get {
                return ResourceManager.GetString("SoEhPossivelCarregarValoresAPartirDe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Solicitação de não exibição no Portal feita por {0} em {1}..
        /// </summary>
        public static string SolicitacaoDeNaoExibicaoNoPortalFeitaPor {
            get {
                return ResourceManager.GetString("SolicitacaoDeNaoExibicaoNoPortalFeitaPor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Solicitação realizada por {0} em {1}..
        /// </summary>
        public static string SolicitacaoRealizadaPorEm {
            get {
                return ResourceManager.GetString("SolicitacaoRealizadaPorEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Somente é possivel registrar vendas com data a partir de {0:d}.
        /// </summary>
        public static string SomenteEhPossivelRegistrarVendaAPartirDe {
            get {
                return ResourceManager.GetString("SomenteEhPossivelRegistrarVendaAPartirDe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sua assinatura foi cancelada em .
        /// </summary>
        public static string SuaAssinaturaFoiCanceladaEm {
            get {
                return ResourceManager.GetString("SuaAssinaturaFoiCanceladaEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sua assinatura poderá ser cancelada normalmente, pelo próprio Trinks, em Minha Conta &gt; Cancelar Plano de Assinatura.
        /// </summary>
        public static string SuaAssinaturaPoderaSerCanceladaNormalmente {
            get {
                return ResourceManager.GetString("SuaAssinaturaPoderaSerCanceladaNormalmente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUA BELEZA A UM CLIQUE.
        /// </summary>
        public static string SuaBelezaAUmCliqueDeVoce {
            get {
                return ResourceManager.GetString("SuaBelezaAUmCliqueDeVoce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sua conta será automaticamente bloqueada em 1 dia..
        /// </summary>
        public static string SuaContaSeraAutomaticamenteBloqueadaEm1Dia {
            get {
                return ResourceManager.GetString("SuaContaSeraAutomaticamenteBloqueadaEm1Dia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sua conta será automaticamente bloqueada em {0} dias..
        /// </summary>
        public static string SuaContaSeraAutomaticamenteBloqueadaEmXDias {
            get {
                return ResourceManager.GetString("SuaContaSeraAutomaticamenteBloqueadaEmXDias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to As taxas de Assinatura são faturadas no início de cada período.
        /// </summary>
        public static string TaxasSaoRecalculadasPorPeriodo {
            get {
                return ResourceManager.GetString("TaxasSaoRecalculadasPorPeriodo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to termos de uso.
        /// </summary>
        public static string TermosDeUso {
            get {
                return ResourceManager.GetString("TermosDeUso", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Além disso, você pode definir qual será a primeira foto que será exibida na capa para seus  clientes quando eles acessarem seu site selecionando a foto principal..
        /// </summary>
        public static string TextoDefinirFotoComoPrincipal {
            get {
                return ResourceManager.GetString("TextoDefinirFotoComoPrincipal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;div&gt;&lt;strong&gt;Ol&amp;aacute;, [cliente],&lt;/strong&gt;&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt;Parab&amp;eacute;ns pelo seu anivers&amp;aacute;rio!&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt;A equipe do {0} deseja tudo de bom e aguarda a sua visita!&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt;Agende em {1}&lt;/div&gt;.
        /// </summary>
        public static string TextoEmailAniversarioPadrao {
            get {
                return ResourceManager.GetString("TextoEmailAniversarioPadrao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O cartão de crédito registrado na sua conta expirou! Para garantir serviço ininterrupto, atualize a data de validade do seu cartão de crédito assim que possível. Você também pode usar um cartão diferente, se desejar. Visite a página Mudar dados do Cartão em Meu Plano..
        /// </summary>
        public static string TextoSeuCartaoExpirou {
            get {
                return ResourceManager.GetString("TextoSeuCartaoExpirou", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu cartão de crédito expira em breve! O cartão de crédito registrado na sua conta vai expirar. Para garantir serviço ininterrupto, atualize a data de validade do seu cartão de crédito assim que possível. Você também pode usar um cartão diferente, se desejar. Visite a página Mudar dados do Cartão em Meu Plano..
        /// </summary>
        public static string TextoSeuCartaoVaiExpirar {
            get {
                return ResourceManager.GetString("TextoSeuCartaoVaiExpirar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feliz aniversário, [cliente], tudo de bom!.
        /// </summary>
        public static string TextoSmsAniversarioPadrao {
            get {
                return ResourceManager.GetString("TextoSmsAniversarioPadrao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu cartão de crédito expirou!.
        /// </summary>
        public static string TituloSeuCartaoExpirou {
            get {
                return ResourceManager.GetString("TituloSeuCartaoExpirou", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu cartão de crédito expira em breve!.
        /// </summary>
        public static string TituloSeuCartaoVaiExpirar {
            get {
                return ResourceManager.GetString("TituloSeuCartaoVaiExpirar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ® 2017, Trinks - CNPJ 73.499.238/0001-87 - Todos os direitos reservados..
        /// </summary>
        public static string TodosDireitosReservados {
            get {
                return ResourceManager.GetString("TodosDireitosReservados", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O Trinks oferece aos seus clientes acesso inicial.
        /// </summary>
        public static string TrinksOfereceAcessoInicialGratuito {
            get {
                return ResourceManager.GetString("TrinksOfereceAcessoInicialGratuito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tudo usando seu computador, tablet ou smartphone.
        /// </summary>
        public static string TudoUsandoSeuComputadorTabletOuSmartPhone {
            get {
                return ResourceManager.GetString("TudoUsandoSeuComputadorTabletOuSmartPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Último período pago terminou em.
        /// </summary>
        public static string UltimoPeriodoPagoTerminouEm {
            get {
                return ResourceManager.GetString("UltimoPeriodoPagoTerminouEm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Um novo e-mail foi enviado para.
        /// </summary>
        public static string UmNovoEmailFoiEnviadoPara {
            get {
                return ResourceManager.GetString("UmNovoEmailFoiEnviadoPara", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O nome do site é o endereço que você deve divulgar aos seus clientes, assim, é muito importante que esteja correto, pois é definitivo. 
        ///Por favor, certifique-se de que foi informado corretamente, pois após ser salvo, este endereço não poderá mais ser alterado, a não ser que 
        ///você entre em contato com a equipe do Trinks. Vale ressaltar que o endereço anterior, uma vez alterado pela equipe Trinks, não será mais válido..
        /// </summary>
        public static string Url_Hotsite_Confirmacao {
            get {
                return ResourceManager.GetString("Url_Hotsite_Confirmacao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use o botão.
        /// </summary>
        public static string UseOBotao {
            get {
                return ResourceManager.GetString("UseOBotao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O usuário do facebook informado não pôde ser utilizado devido a um erro na requisição..
        /// </summary>
        public static string UsuarioFacebookInvalido {
            get {
                return ResourceManager.GetString("UsuarioFacebookInvalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seu usuário já possui uma conta associada ao Trinks..
        /// </summary>
        public static string UsuarioFacebookJaPossuiContaNoTrinks {
            get {
                return ResourceManager.GetString("UsuarioFacebookJaPossuiContaNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utilize outro cartão para pagamento de sua fatura.
        /// </summary>
        public static string UtilizeOutroCartaoParaPagamentoDeSuaFatura {
            get {
                return ResourceManager.GetString("UtilizeOutroCartaoParaPagamentoDeSuaFatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A cada 30 dias, o valor da assinatura será debitado em seu cartão de crédito, de acordo com a faixa em que você se encontrar..
        /// </summary>
        public static string ValorAssinaturaSeraDebitadoACada30DiasDoSeuCartaoDeCredito {
            get {
                return ResourceManager.GetString("ValorAssinaturaSeraDebitadoACada30DiasDoSeuCartaoDeCredito", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valor cobrado a cada 30 dias..
        /// </summary>
        public static string ValorCobradoACada30Dias {
            get {
                return ResourceManager.GetString("ValorCobradoACada30Dias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O valor é cobrado mensalmente de acordo com a faixa de profissionais com agenda que seu estabelecimento se encontra.
        /// </summary>
        public static string ValorCobradoMensalmente {
            get {
                return ResourceManager.GetString("ValorCobradoMensalmente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O valor pago deve ser maior que o valor a pagar!.
        /// </summary>
        public static string ValorNaoQuitado {
            get {
                return ResourceManager.GetString("ValorNaoQuitado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O valor da mensalidade será referente a faixa de profissionais contratada, ou de acordo com a faixa acima considerando o número de profissionais máximos com agenda do estabelecimento..
        /// </summary>
        public static string ValorVariaDeAcordoComQtdProfissionaisNoMomentoDaGeracao {
            get {
                return ResourceManager.GetString("ValorVariaDeAcordoComQtdProfissionaisNoMomentoDaGeracao", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to O valor da mensalidade será referente a faixa de profissionais contratada, ou de acordo com a faixa acima considerando o número de profissionais máximos com agenda do estabelecimento..
        /// </summary>
        public static string ValorVariaDeAcordoQuantidadeMaximaProfissionaisPeriodoAnterior {
            get {
                return ResourceManager.GetString("ValorVariaDeAcordoQuantidadeMaximaProfissionaisPeriodoAnterior", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vamos convidá-lo a entrar no Trinks!.
        /// </summary>
        public static string VamosConvidaloEntrarNoTrinks {
            get {
                return ResourceManager.GetString("VamosConvidaloEntrarNoTrinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ver Detalhes da Fatura.
        /// </summary>
        public static string VerDetalhesFatura {
            get {
                return ResourceManager.GetString("VerDetalhesFatura", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ver Histórico....
        /// </summary>
        public static string VerHistorico {
            get {
                return ResourceManager.GetString("VerHistorico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verifique se os dados de pagamento informados estão corretos..
        /// </summary>
        public static string VerifiqueSeDadosPagamentoEstaoCorretos {
            get {
                return ResourceManager.GetString("VerifiqueSeDadosPagamentoEstaoCorretos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você ainda possui.
        /// </summary>
        public static string VoceAindaPossui {
            get {
                return ResourceManager.GetString("VoceAindaPossui", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você ainda possui 1 dia de uso grátis!.
        /// </summary>
        public static string VoceAindaPossuiUmDiaDeUsoGratis {
            get {
                return ResourceManager.GetString("VoceAindaPossuiUmDiaDeUsoGratis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você ainda teria {0} dia(s) de acesso para usar o Trinks!.
        /// </summary>
        public static string VoceAindaPossuiXDias {
            get {
                return ResourceManager.GetString("VoceAindaPossuiXDias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você foi cadastrado como profissional responsável pelo estabelecimento e está associado a um serviço.
        /// Se você não realiza nenhum serviço, adicione pelo menos um profissional que realize serviços e depois edite o seu cadastro, informando que você não possui Agenda, na tela de Serviços do cadastro de profissional.
        ///Caso você realize serviços, edite o seu cadastro, informando quais serviços você realiza..
        /// </summary>
        public static string VoceFoiCadastradoComoProfissionalResponsavelPeloEstabelecimento {
            get {
                return ResourceManager.GetString("VoceFoiCadastradoComoProfissionalResponsavelPeloEstabelecimento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você pode adicionar diferentes níveis de acesso para seus profissionais.
        /// </summary>
        public static string VocePodeAdicionarDiferentesNiveisDeAcessoAosProfissionais {
            get {
                return ResourceManager.GetString("VocePodeAdicionarDiferentesNiveisDeAcessoAosProfissionais", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você pode adicionar até 30 fotos do estabelecimento, a qualquer momento! Use este espaço para divulgar seus serviços e novidades!.
        /// </summary>
        public static string VocePodeAdicionarQuantasFotosDoEstabelecimentoDesejarAQualquerMomento {
            get {
                return ResourceManager.GetString("VocePodeAdicionarQuantasFotosDoEstabelecimentoDesejarAQualquerMomento", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você também pode criar suas próprias categorias para organizar da forma que desejar a sua lista de serviços, clicando em.
        /// </summary>
        public static string VocePodeCriarSuasPropriasCategorias {
            get {
                return ResourceManager.GetString("VocePodeCriarSuasPropriasCategorias", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você pode customizar os serviços, alterando a descrição, o preço, a duração e a foto clicando em Editar.
        /// </summary>
        public static string VocePodeCustomizarServicosClicandoEmEditar {
            get {
                return ResourceManager.GetString("VocePodeCustomizarServicosClicandoEmEditar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você poderá assinar a partir do dia .
        /// </summary>
        public static string VocePoderaAssinarApartirDoDia {
            get {
                return ResourceManager.GetString("VocePoderaAssinarApartirDoDia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você pode utilizar o botão de replicar.
        /// </summary>
        public static string VocePodeUtilizarBotaoReplicar {
            get {
                return ResourceManager.GetString("VocePodeUtilizarBotaoReplicar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você será avisado se, durante o uso do sistema, mudar de faixa..
        /// </summary>
        public static string VoceSeraAvisadoMudancaDeFaixa {
            get {
                return ResourceManager.GetString("VoceSeraAvisadoMudancaDeFaixa", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tem certeza que deseja cancelar?.
        /// </summary>
        public static string VoceTemCertezaQueQuerCancelar {
            get {
                return ResourceManager.GetString("VoceTemCertezaQueQuerCancelar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Você tem um estabelecimento de estética e beleza? Veja tudo que o.
        /// </summary>
        public static string VoceTemUmEstabelecimentoDeEsteticaEBeleza {
            get {
                return ResourceManager.GetString("VoceTemUmEstabelecimentoDeEsteticaEBeleza", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to você verá todos os profissionais que realizam o serviço e poderá associar ou desassociar profissionais para esse serviço, sem precisar ir até a página de profissionais!.
        /// </summary>
        public static string VoceVeraTodosOsProfissionaisQueRealizamServicoEPodeAssociarEDesassociar {
            get {
                return ResourceManager.GetString("VoceVeraTodosOsProfissionaisQueRealizamServicoEPodeAssociarEDesassociar", resourceCulture);
            }
        }
    }
}
