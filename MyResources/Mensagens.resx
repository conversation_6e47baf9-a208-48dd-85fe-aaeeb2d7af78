﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AoCancelarVocePerdeAcessoASuaAgenda" xml:space="preserve">
    <value>Ao cancelar você perde &lt;span&gt;imediatamente&lt;/span&gt; o acesso a sua agenda.</value>
  </data>
  <data name="ConfirmarAgendamentoComConflitoDeHorario" xml:space="preserve">
    <value>Já existe outro agendamento neste horário para este profissional.</value>
  </data>
  <data name="DesejaContinuar" xml:space="preserve">
    <value>Deseja continuar?</value>
  </data>
  <data name="ErroNaPagina" xml:space="preserve">
    <value>Ocorreu um erro na página. Tente novamente mais tarde.</value>
  </data>
  <data name="AgendamentoOnline" xml:space="preserve">
    <value>Agendamento Online</value>
  </data>
  <data name="AssinaturaMensalDebitadoCartaoCredito" xml:space="preserve">
    <value>Plano de assinatura mensal.</value>
  </data>
  <data name="AssineAqui" xml:space="preserve">
    <value>Assine aqui</value>
  </data>
  <data name="AsTaxasSaoFaturadasACadaPeriodo" xml:space="preserve">
    <value>As taxas de Assinatura são faturadas no início de cada período</value>
  </data>
  <data name="CliqueAquiEAssineJa" xml:space="preserve">
    <value>Clique aqui e Assine já!</value>
  </data>
  <data name="CancelamentoEntraraEmVigorImediatamente" xml:space="preserve">
    <value>O cancelamento entrará em vigor imediatamente.</value>
  </data>
  <data name="CancelarPlanoAssinatura" xml:space="preserve">
    <value>Cancelar Plano de Assinatura</value>
  </data>
  <data name="EntreEmContatoConosco" xml:space="preserve">
    <value>Entre em contato conosco</value>
  </data>
  <data name="FaixasParaProfissionaisAgendaControladasPeloTrinks" xml:space="preserve">
    <value>As faixas acima são para profissionais cujas agendas serão controladas pelo Trinks. A quantidade de profissionais administrativos cadastrados é ilimitada.</value>
  </data>
  <data name="GerenciadorEstabelecimento" xml:space="preserve">
    <value>Gerenciador do seu Estabelecimento</value>
  </data>
  <data name="EnviaremosEmailConfirmacaoCancelamento" xml:space="preserve">
    <value>Enviaremos um e-mail de confirmação do seu cancelamento.</value>
  </data>
  <data name="LinhaInvestimentoComLimiteInferiorESuperior" xml:space="preserve">
    <value>De {0} a {1} profissionais ......................... {2} mensais</value>
  </data>
  <data name="LinhaInvestimentoSemLimiteInferior" xml:space="preserve">
    <value>Até {0} profissionais ......................... {1} mensais</value>
  </data>
  <data name="LinhaInvestimentoSemLimiteSuperior" xml:space="preserve">
    <value>{0} ou mais profissionais ......................... {1} mensais</value>
  </data>
  <data name="MinimoDeXCaracteres" xml:space="preserve">
    <value>mínimo de {0} caracteres</value>
  </data>
  <data name="MudeDadosDoCartaoCredito" xml:space="preserve">
    <value>Mude os dados do Cartão de Crédito</value>
  </data>
  <data name="NaoPercaBeneficiosTrinksParaSeuEstabelecimento" xml:space="preserve">
    <value>Não perca os benefícios que o Trinks traz pro seu Estabelecimento!</value>
  </data>
  <data name="OValorDevidoEhDeRS" xml:space="preserve">
    <value>O valor devido é de: R$</value>
  </data>
  <data name="ParaEvitarQueIssoOcorraRegularizeFatura" xml:space="preserve">
    <value>Para evitar que isso ocorra, regularize o pagamento da fatura:</value>
  </data>
  <data name="ParaReativarAcessoRegularizeFatura" xml:space="preserve">
    <value>Para reativar seu acesso, regularize o pagamento da fatura</value>
  </data>
  <data name="PorqueVoceDesejaCancelarAssinatura" xml:space="preserve">
    <value>Por que você deseja encerrar sua assinatura no Trinks?</value>
  </data>
  <data name="PrecoDevidolEh" xml:space="preserve">
    <value>O preço devido é de</value>
  </data>
  <data name="ReativeSuaAssinatura" xml:space="preserve">
    <value>Reative sua assinatura fazendo um novo plano de assinatura!</value>
  </data>
  <data name="RecalculoComissaoConfirmacao1" xml:space="preserve">
    <value>Deseja realmente recalcular todas as comissões de atendimentos e vendas de pacotes e produtos realizados a partir do dia {0} dos profissionais selecionados?</value>
  </data>
  <data name="RecalculoComissaoConfirmacao2" xml:space="preserve">
    <value>TODAS AS COMISSÕES DE ATENDIMENTOS E VENDAS DE PRODUTOS REALIZADOS A PARTIR DO DIA {0} DOS PROFISSIONAIS 
SELECIONADOS, EXCETO AS COMISSÕES COM VALORES INFORMADOS NO RELATÓRIO DE COMISSÕES,  \\nSERÃO RECALCULADAS COM OS PERCENTUAIS DE COMISSÃO E VALORES DE DESCARTÁVEIS ATUAIS.\\nDeseja prosseguir?</value>
  </data>
  <data name="RecalculoComissaoMensagem" xml:space="preserve">
    <value>Esta funcionalidade recalcula todas as comissões atuais dos profissionais selecionados a partir da data informada com base nos valores.</value>
  </data>
  <data name="RecalculoComissaoSucesso" xml:space="preserve">
    <value>Recálculo das comissões realizado com sucesso.</value>
  </data>
  <data name="ReiniciaPlanoAssinatura" xml:space="preserve">
    <value>Reinicie seu plano de assinatura quando quiser. Os dados do estabelecimento ficarão salvos por 1 ano.</value>
  </data>
  <data name="ResolveuPendenciasAtualizeDados" xml:space="preserve">
    <value>Resolveu o problema com a financeira/banco? Para reprocessar o pagamento da fatura pendente, clique aqui</value>
  </data>
  <data name="SeuAcessoFoiEncerradoDia" xml:space="preserve">
    <value>Seu acesso foi encerrado dia</value>
  </data>
  <data name="SeuAcessoSeraEncerradoDia" xml:space="preserve">
    <value>Seu acesso será encerrado dia</value>
  </data>
  <data name="SeuPeriodoAtualTerminaEm" xml:space="preserve">
    <value>Seu período atual termina em</value>
  </data>
  <data name="SeuSiteNaInternet" xml:space="preserve">
    <value>Seu Site na Internet</value>
  </data>
  <data name="SuaAssinaturaFoiCanceladaEm" xml:space="preserve">
    <value>Sua assinatura foi cancelada em </value>
  </data>
  <data name="TaxasSaoRecalculadasPorPeriodo" xml:space="preserve">
    <value>As taxas de Assinatura são faturadas no início de cada período</value>
  </data>
  <data name="TrinksOfereceAcessoInicialGratuito" xml:space="preserve">
    <value>O Trinks oferece aos seus clientes acesso inicial</value>
  </data>
  <data name="UltimoPeriodoPagoTerminouEm" xml:space="preserve">
    <value>Último período pago terminou em</value>
  </data>
  <data name="PagamentoNaoConfirmadoPeloBanco" xml:space="preserve">
    <value>Pagamento não confirmado pelo banco</value>
  </data>
  <data name="ParaBaixarBoletoParaPagamentoCliqueAqui" xml:space="preserve">
    <value>Para baixar o boleto para pagamento clique aqui</value>
  </data>
  <data name="ValorVariaDeAcordoQuantidadeMaximaProfissionaisPeriodoAnterior" xml:space="preserve">
    <value>O valor da mensalidade será referente a faixa de profissionais contratada, ou de acordo com a faixa acima considerando o número de profissionais máximos com agenda do estabelecimento.</value>
  </data>
  <data name="VoceAindaPossui" xml:space="preserve">
    <value>Você ainda possui</value>
  </data>
  <data name="VocePoderaAssinarApartirDoDia" xml:space="preserve">
    <value>Você poderá assinar a partir do dia </value>
  </data>
  <data name="VoceSeraAvisadoMudancaDeFaixa" xml:space="preserve">
    <value>Você será avisado se, durante o uso do sistema, mudar de faixa.</value>
  </data>
  <data name="ValorCobradoACada30Dias" xml:space="preserve">
    <value>Valor cobrado a cada 30 dias.</value>
  </data>
  <data name="ConformeImpressoNoCartao" xml:space="preserve">
    <value>conforme impresso no cartão</value>
  </data>
  <data name="MesAno" xml:space="preserve">
    <value>mês/ano</value>
  </data>
  <data name="VoceAindaPossuiXDias" xml:space="preserve">
    <value>Você ainda teria {0} dia(s) de acesso para usar o Trinks!</value>
  </data>
  <data name="VoceTemCertezaQueQuerCancelar" xml:space="preserve">
    <value>Tem certeza que deseja cancelar?</value>
  </data>
  <data name="AcessarMinhaConta" xml:space="preserve">
    <value>Acessar Meu Plano</value>
  </data>
  <data name="EstamosMuitoFelizes" xml:space="preserve">
    <value>estamos muito felizes por sua equipe fazer parte da nossa família</value>
  </data>
  <data name="PagamentoPlanoAssinaturaFoi" xml:space="preserve">
    <value>O pagamento do plano de assinatura foi</value>
  </data>
  <data name="ParabensPorEscolherOTrinks" xml:space="preserve">
    <value>Parabéns por escolher o Trinks</value>
  </data>
  <data name="SeuAcessoAoTrinksEstaLiberado" xml:space="preserve">
    <value>Seu acesso ao Trinks está liberado</value>
  </data>
  <data name="VerDetalhesFatura" xml:space="preserve">
    <value>Ver Detalhes da Fatura</value>
  </data>
  <data name="AcompanheSuaAssinatura" xml:space="preserve">
    <value>Acompanhe sua assinatura em Meu plano &gt; Ver Histórico de Pagamentos.</value>
  </data>
  <data name="SuaAssinaturaPoderaSerCanceladaNormalmente" xml:space="preserve">
    <value>Sua assinatura poderá ser cancelada normalmente, pelo próprio Trinks, em Minha Conta &gt; Cancelar Plano de Assinatura</value>
  </data>
  <data name="ValorAssinaturaSeraDebitadoACada30DiasDoSeuCartaoDeCredito" xml:space="preserve">
    <value>A cada 30 dias, o valor da assinatura será debitado em seu cartão de crédito, de acordo com a faixa em que você se encontrar.</value>
  </data>
  <data name="VerifiqueSeDadosPagamentoEstaoCorretos" xml:space="preserve">
    <value>Verifique se os dados de pagamento informados estão corretos.</value>
  </data>
  <data name="CliqueAquiParaAssinarOTrinks" xml:space="preserve">
    <value>Clique aqui para assinar o Trinks!</value>
  </data>
  <data name="CliqueAquiParaBaixarOBoleto" xml:space="preserve">
    <value>Clique aqui para baixá-lo.</value>
  </data>
  <data name="BoletoParaProximoPagamentoEstaDisponivel" xml:space="preserve">
    <value>O boleto para o seu próximo pagamento já encontra-se disponível.</value>
  </data>
  <data name="CliqueAquiParaResolver" xml:space="preserve">
    <value>Clique aqui para resolver.</value>
  </data>
  <data name="HouveUmProblemaNoSeuPagamento" xml:space="preserve">
    <value>Houve um problema no seu pagamento.</value>
  </data>
  <data name="TextoSeuCartaoExpirou" xml:space="preserve">
    <value>O cartão de crédito registrado na sua conta expirou! Para garantir serviço ininterrupto, atualize a data de validade do seu cartão de crédito assim que possível. Você também pode usar um cartão diferente, se desejar. Visite a página Mudar dados do Cartão em Meu Plano.</value>
  </data>
  <data name="TextoSeuCartaoVaiExpirar" xml:space="preserve">
    <value>Seu cartão de crédito expira em breve! O cartão de crédito registrado na sua conta vai expirar. Para garantir serviço ininterrupto, atualize a data de validade do seu cartão de crédito assim que possível. Você também pode usar um cartão diferente, se desejar. Visite a página Mudar dados do Cartão em Meu Plano.</value>
  </data>
  <data name="TituloSeuCartaoExpirou" xml:space="preserve">
    <value>Seu cartão de crédito expirou!</value>
  </data>
  <data name="TituloSeuCartaoVaiExpirar" xml:space="preserve">
    <value>Seu cartão de crédito expira em breve!</value>
  </data>
  <data name="DadosAtualizadosComSucesso" xml:space="preserve">
    <value>Dados atualizados com sucesso!</value>
  </data>
  <data name="MensagemDeFaixasInvalidas" xml:space="preserve">
    <value>Existem intervalos que não estão sendo cobertos pelas faixas de cobrança informadas. 
Favor ajustar de maneira que as faixas compreendam todo o universo dos números naturais.</value>
  </data>
  <data name="MensagemDePlanoUtilizado" xml:space="preserve">
    <value>Este plano está sendo assinado por {0} pessoa(s), sendo {1} assinaturas ativas e {2} assinaturas inativas.</value>
  </data>
  <data name="MensagemDeServicoBasico" xml:space="preserve">
    <value>Deve haver pelo menos um serviço básico nos serviços inclusos neste plano de assinatura.</value>
  </data>
  <data name="MensagemDeTodosPlanosInativos" xml:space="preserve">
    <value>Deve haver pelo menos um plano de assinatura ativo.</value>
  </data>
  <data name="MensagemDeValoresNaoPreenchidos" xml:space="preserve">
    <value>Todos os valores devem ser preenchidos.</value>
  </data>
  <data name="PlanoDeAssinaturaCriadoComSucesso" xml:space="preserve">
    <value>Plano de Assinatura criado com sucesso!</value>
  </data>
  <data name="CodigoServicoEmDuplicidade" xml:space="preserve">
    <value>Foi encontrado serviço com mesmo Código informado.</value>
  </data>
  <data name="ServicoAssociadoAPlanoDeAssinatura" xml:space="preserve">
    <value>Este serviço não pode ser editado pois está associado a um ou mais planos de assinatura.</value>
  </data>
  <data name="ServicoCriadoComSucesso" xml:space="preserve">
    <value>Serviço criado com sucesso!</value>
  </data>
  <data name="Dias" xml:space="preserve">
    <value>dias</value>
  </data>
  <data name="OperacaoNaoPoderaSerDesfeita" xml:space="preserve">
    <value>Essa operação não poderá ser desfeita, confima a operação?</value>
  </data>
  <data name="Erro_Salvar_Agendamento" xml:space="preserve">
    <value>Erro ao salvar o agendamento!</value>
  </data>
  <data name="NaoHaCategoriasPadroes" xml:space="preserve">
    <value>Não há categorias padrões cadastradas</value>
  </data>
  <data name="NaoHaServicosPadroes" xml:space="preserve">
    <value>Não há serviços padrões cadastrados</value>
  </data>
  <data name="MsgJaExisteServicoComNome" xml:space="preserve">
    <value>Já existe um serviço cadastrado com esse nome!</value>
  </data>
  <data name="AcessoApenasParaVisualizarPropriaAgenda" xml:space="preserve">
    <value>Profissional I – acesso para visualizar à própria agenda, recomendado para profissionais que executam serviços</value>
  </data>
  <data name="AcessoParaAgendaDoEstabelecimentoEFechamentoDeConta" xml:space="preserve">
    <value>Recepção</value>
  </data>
  <data name="AcessoTotalAoSistema" xml:space="preserve">
    <value>Administrador</value>
  </data>
  <data name="AoSalvarSeraEnviadoEmailProfissionalReferenteASenha" xml:space="preserve">
    <value>Ao salvar será enviado para o profissional um e-mail referente à senha</value>
  </data>
  <data name="ConfirmaExclusaoFoto" xml:space="preserve">
    <value>Confirma a exclusão dessa foto?</value>
  </data>
  <data name="CopiarProfissionalNosEmailsParaClientesSobreSeusAgendamentos" xml:space="preserve">
    <value>Copiar o profissional nos emails para clientes sobre seus agendamentos</value>
  </data>
  <data name="CPFJaAssociadoAProfissional" xml:space="preserve">
    <value>Este CPF corresponde a um profissional que já foi funcionário do seu estabelecimento. Deseja aproveitar os dados do antigo cadastro?</value>
  </data>
  <data name="EnviarEmailRecuperacaoDeSenha" xml:space="preserve">
    <value>Um e-mail de recuperação de senha será enviado para o endereço {0}.
 Deseja prosseguir?</value>
  </data>
  <data name="EnviarSenhaParaEmailDoProfissional" xml:space="preserve">
    <value>Enviar senha para o e-mail do profissional</value>
  </data>
  <data name="EsteNomeApareceraNoSiteDoEstabelecimento" xml:space="preserve">
    <value>Este é o nome que aparecerá no site do estabelecimento</value>
  </data>
  <data name="NaoEhPossivelAlterarAcessoAAgendaDoSeuProprioUsuario" xml:space="preserve">
    <value>Não é possível alterar o acesso e as permissões do seu próprio usuário.</value>
  </data>
  <data name="ParaAcessarSistemaNecessarioEmailDoProfissional" xml:space="preserve">
    <value>Para acessar o sistema via login, é necessário que o profissional tenha e-mail</value>
  </data>
  <data name="ParaPesquisarNovoCPFVocePerderaInformacoesDesteCadastro" xml:space="preserve">
    <value>Para pesquisar outro CPF você perderá as informações deste cadastro. Deseja continuar?</value>
  </data>
  <data name="PermitirProfissionalAcessarAgendaComSeusHorariosMarcados" xml:space="preserve">
    <value>Permitir que o profissional acesse a agenda com seus horários marcados</value>
  </data>
  <data name="PesquisarCPF" xml:space="preserve">
    <value>pesquisar cpf</value>
  </data>
  <data name="PesquisarNovoCPF" xml:space="preserve">
    <value>pesquisar novo cpf</value>
  </data>
  <data name="SelecioneSexo" xml:space="preserve">
    <value>« Selecione Sexo</value>
  </data>
  <data name="SelecioneUmHorario" xml:space="preserve">
    <value>Deve ser definido pelo menos um dia de trabalho para o profissional.</value>
  </data>
  <data name="SelecioneUmServico" xml:space="preserve">
    <value>Deve ser selecionado pelo menos um serviço.</value>
  </data>
  <data name="SemAcessoAoSistema" xml:space="preserve">
    <value>Sem acesso ao sistema através de login (e-mail)</value>
  </data>
  <data name="SenhaDoProfissionalEnviadaComSucesso" xml:space="preserve">
    <value>A senha do profissional foi enviada com sucesso!</value>
  </data>
  <data name="CadastrandoSeuEstabelecimentoNoTrinks" xml:space="preserve">
    <value>Cadastrando o seu Estabelecimento no Trinks</value>
  </data>
  <data name="CriarConta" xml:space="preserve">
    <value>Criar Conta</value>
  </data>
  <data name="EhNecessarioLerEAceitarOsTermosDeUsoParaConcluirOCadastro" xml:space="preserve">
    <value>É necessário ler e aceitar os Termos de Uso para concluir o cadastro.</value>
  </data>
  <data name="EsqueciMinhaSenha" xml:space="preserve">
    <value>Esqueci minha senha</value>
  </data>
  <data name="JaTenhoUmaContaNoTrinks" xml:space="preserve">
    <value>Eu já tenho cadastro no Trinks</value>
  </data>
  <data name="LiEAceito" xml:space="preserve">
    <value>Li e aceito os</value>
  </data>
  <data name="NaoTenhoUmaContaNoTrinks" xml:space="preserve">
    <value>Eu não tenho cadastro no Trinks</value>
  </data>
  <data name="PoliticaDePrivacidade" xml:space="preserve">
    <value>política de privacidade</value>
  </data>
  <data name="QueroReceberNewsletters" xml:space="preserve">
    <value>Quero receber newsletters com notícias, eventos e informações do Trinks e seus parceiros</value>
  </data>
  <data name="SenhaEnviadaComSucesso" xml:space="preserve">
    <value>Senha enviada com sucesso</value>
  </data>
  <data name="TermosDeUso" xml:space="preserve">
    <value>termos de uso</value>
  </data>
  <data name="ConfirmeSeuEnderecoDeEmail" xml:space="preserve">
    <value>CONFIRME SEU CADASTRO NO TRINKS</value>
  </data>
  <data name="DigiteAquiCodigoConfirmacaoRecebidoPorEmailParaConcluirCadastro" xml:space="preserve">
    <value>Digite aqui o código de confirmação recebido por e-mail para concluir o cadastro</value>
  </data>
  <data name="ObrigadaPorSeCadastrarEnviamosEmailDeConfirmacao" xml:space="preserve">
    <value>Obrigada por se cadastrar no Trinks! Enviamos um e-mail de confirmação para</value>
  </data>
  <data name="MensagemErroPadrao" xml:space="preserve">
    <value>Há informações não preenchidas corretamente.</value>
  </data>
  <data name="NaoHaClientesEmAtendimento" xml:space="preserve">
    <value>Não há clientes em atendimento</value>
  </data>
  <data name="NenhumResultadoEncontrado" xml:space="preserve">
    <value>Nenhum resultado encontrado</value>
  </data>
  <data name="OPercentualDasComissoesDevemEstarEntre0E100" xml:space="preserve">
    <value>O percentual da comissão deve estar entre 0% e 100%.</value>
  </data>
  <data name="DiminuaProfissionaisSelecionados" xml:space="preserve">
    <value>Diminua o número de profissionais selecionados.
O recálculo não pode impactar em mais de {0} serviços de profissionais.
Atualmente está  impactando em {1}.</value>
  </data>
  <data name="RestrinjaFiltrosRecalculo" xml:space="preserve">
    <value>Restrinja seus filtros para o recálculo aumentando a data ou diminuindo o número de profissionais.
A quantidade de itens a serem recalculados é de {0}, porém o máximo é {1}.</value>
  </data>
  <data name="JaExisteEstabelecimentoComNomeFantasiaInformado" xml:space="preserve">
    <value>Já existe um estabelecimento com o nome fantasia informado.</value>
  </data>
  <data name="AdicionarServicosECategoriasNaoCadastrados" xml:space="preserve">
    <value>Adicionar os serviços e as categorias que não estão cadastrados</value>
  </data>
  <data name="AoCadastrarDuracaoDoServicoColoqueTempoDuracaoMedio" xml:space="preserve">
    <value>Ao cadastrar a duração de um serviço, coloque o tempo de duração médio. Se um profissional demorar menos ou mais tempo, basta alterar a duração editando o agendamento!</value>
  </data>
  <data name="ClicarEmEdicaoRapidaParaAlterarValoresEDuracoesDeServicos" xml:space="preserve">
    <value>Clicar em Edição Rápida para alterar os valores e as durações de todos os serviços</value>
  </data>
  <data name="ColoqueSempreMenorValorParaServicosDeDiferentesPrecos" xml:space="preserve">
    <value>Se um serviço tiver diferentes preços, variando de acordo com o tamanho do cabelo, por exemplo, coloque sempre o menor valor e a opção A partir de. Dessa forma você não vai correr o risco de um cliente agendar um serviço querendo pagar o valor de outro!</value>
  </data>
  <data name="ExcluirServicosQueSeuEstabelecimentoNaoRealiza" xml:space="preserve">
    <value>Excluir os serviços que o seu estabelecimento não realiza</value>
  </data>
  <data name="ParaQueCadastroDeServicosFiqueAindaMaisRapido" xml:space="preserve">
    <value>Para que o cadastro dos serviços fique ainda mais rápido sugerimos</value>
  </data>
  <data name="ParaAdicionarUmNovoServicoEscolhendoItensDaListaPadraoOuCadastrandoNovoServico" xml:space="preserve">
    <value>para adicionar um novo serviço, escolhendo um dos itens da nossa lista padrão ou cadastrando um Novo Serviço.</value>
  </data>
  <data name="EmEdicaoRapidaVocePodeEditarDuracaoEValorDeServicosDeUmaSoVez" xml:space="preserve">
    <value>Em Edição Rápida você pode editar a duração e o valor de todos os serviços de uma só vez. No valor do serviço, você pode escolher entre Preço Fixo, A partir de ou Valor Promocional.</value>
  </data>
  <data name="MensagemSelecioneUmHorario" xml:space="preserve">
    <value>Deve ser definido pelo menos um dia de trabalho.</value>
  </data>
  <data name="Url_Hotsite_Confirmacao" xml:space="preserve">
    <value>O nome do site é o endereço que você deve divulgar aos seus clientes, assim, é muito importante que esteja correto, pois é definitivo. 
Por favor, certifique-se de que foi informado corretamente, pois após ser salvo, este endereço não poderá mais ser alterado, a não ser que 
você entre em contato com a equipe do Trinks. Vale ressaltar que o endereço anterior, uma vez alterado pela equipe Trinks, não será mais válido.</value>
  </data>
  <data name="ParaEditarServicoCliqueSobreEleOuEmEditar" xml:space="preserve">
    <value>Para editar um serviço clique sobre ele ou em Editar</value>
  </data>
  <data name="ParaExcluirBastaClicarEmExcluir" xml:space="preserve">
    <value>e para excluí-lo, basta clicar em Excluir</value>
  </data>
  <data name="UseOBotao" xml:space="preserve">
    <value>Use o botão</value>
  </data>
  <data name="VocePodeCriarSuasPropriasCategorias" xml:space="preserve">
    <value>Você também pode criar suas próprias categorias para organizar da forma que desejar a sua lista de serviços, clicando em</value>
  </data>
  <data name="Confere_Email" xml:space="preserve">
    <value>Os e-mails informados não coincidem</value>
  </data>
  <data name="DigiteiOEmailErrado" xml:space="preserve">
    <value>Digitei o e-mail errado</value>
  </data>
  <data name="Email_Invalido" xml:space="preserve">
    <value>E-mail inválido</value>
  </data>
  <data name="EsteEmailSeraSeuLoginNoTrinks" xml:space="preserve">
    <value>Este e-mail será o seu login no Trinks</value>
  </data>
  <data name="NaoRecebiCodigoConfirmacao" xml:space="preserve">
    <value>Não recebi o código de confirmação</value>
  </data>
  <data name="Preencha_Confirmacao_Email" xml:space="preserve">
    <value>Preencha a confirmação do e-mail</value>
  </data>
  <data name="Preencha_Email" xml:space="preserve">
    <value>Preencha E-mail</value>
  </data>
  <data name="UmNovoEmailFoiEnviadoPara" xml:space="preserve">
    <value>Um novo e-mail foi enviado para</value>
  </data>
  <data name="AbaInformacoesAdicionaisEhOpcional" xml:space="preserve">
    <value>A aba Informações Adicionais é opcional, você só precisa preencher se quiser.</value>
  </data>
  <data name="AcessoAgendaDoEstabelecimentoEFechamentoDeConta" xml:space="preserve">
    <value>Acesso para a agenda do estabelecimento e fechamento de conta - usado normalmente para recepcionista</value>
  </data>
  <data name="AcessoParaVisualizarPropriaAgenda" xml:space="preserve">
    <value>Acesso apenas para visualizar a própria agenda – usado normalmente para profissionais que executam serviços</value>
  </data>
  <data name="AcessoTotal" xml:space="preserve">
    <value>Acesso total ao sistema – usado normalmente por gerentes/sócios</value>
  </data>
  <data name="AdicioneDadosDoProfissional" xml:space="preserve">
    <value>Adicione os dados do profissional. 
</value>
  </data>
  <data name="AdicioneHorariosDeTrabalhoDoProfissional" xml:space="preserve">
    <value>Adicione os horários de trabalho do profissional.</value>
  </data>
  <data name="AsInformacoesNaoFicaraoDisponiveisParaSeusClientes" xml:space="preserve">
    <value>As informações não ficarão disponíveis para os seus clientes. Eles só irão ver o nome e os serviços que o profissional realiza.</value>
  </data>
  <data name="CamposAlmocoEIntervaloSaoOpcionais" xml:space="preserve">
    <value>Os campos de horário de almoço e intervalo são opcionais.</value>
  </data>
  <data name="CamposDeHorarioDoProfissionalSaoPreenchidosPorPadraoSeguindoHorarioDeFuncionamentoDoSalao" xml:space="preserve">
    <value>Por padrão, os campos de horário do profissional são preenchidos iguais ao horário de funcionamento do salão, assim, para sócios e outros administradores, você não precisa alterar o horário.</value>
  </data>
  <data name="EInformeCpfDoProfissional" xml:space="preserve">
    <value>e informe o cpf do profissional.</value>
  </data>
  <data name="EssesValoresPoderaoSerAlteradosAQualquerMomento" xml:space="preserve">
    <value>Esses valores poderão ser alterados a qualquer momento.</value>
  </data>
  <data name="MuitasVezesProfissionaisRealizamMesmoServicoComMesmoPercentualDeComissao" xml:space="preserve">
    <value>Muitas vezes um ou mais profissionais realizam os mesmos serviços, com os mesmos percentuais de comissão, por exemplo: cabeleireiros e manicures.
Se isso também acontece no seu estabelecimento, adicione um profissional e, nos seguintes, selecione a opção “Copiar lista de outro profissional”, na aba de Serviços. Isso vai deixar tudo ainda mais rápido!</value>
  </data>
  <data name="OCpfEhObrigatorioParaTornarOProfissionalUnicoNoSistema" xml:space="preserve">
    <value>O cpf é obrigatório pois é ele que tornará o profissional único no sistema.</value>
  </data>
  <data name="ParaAdicionarUmProfissionalCliqueEm" xml:space="preserve">
    <value>Para adicionar um profissional, clique em</value>
  </data>
  <data name="ParaCopiarValorComissaoParaTodosServicos" xml:space="preserve">
    <value>para copiar o valor de comissão para todos os serviços.</value>
  </data>
  <data name="ParaEnviarNovaSenhaParaEmailDoProfissionalCliqueEm" xml:space="preserve">
    <value>Para enviar uma nova senha para o e-mail do profissional, clique em “Enviar senha para o e-mail do Profissional”, localizado embaixo do campo de e-mail.</value>
  </data>
  <data name="SeProfissionalRealizaServicosInformePercentualDeComissao" xml:space="preserve">
    <value>Se o profissional realizar serviços selecione-os e informe o percentual de comissão.</value>
  </data>
  <data name="SeProfissionalTiverEmailPodeReceberEmailAgendamento" xml:space="preserve">
    <value>Se o profissional tiver e-mail, você poderá permitir que ele receba um e-mail toda vez que um agendamento for marcado para ele, clicando em “Copiar o profissional nos emails para clientes sobre seus agendamentos”.</value>
  </data>
  <data name="VoceFoiCadastradoComoProfissionalResponsavelPeloEstabelecimento" xml:space="preserve">
    <value>Você foi cadastrado como profissional responsável pelo estabelecimento e está associado a um serviço.
 Se você não realiza nenhum serviço, adicione pelo menos um profissional que realize serviços e depois edite o seu cadastro, informando que você não possui Agenda, na tela de Serviços do cadastro de profissional.
Caso você realize serviços, edite o seu cadastro, informando quais serviços você realiza.</value>
  </data>
  <data name="VocePodeAdicionarDiferentesNiveisDeAcessoAosProfissionais" xml:space="preserve">
    <value>Você pode adicionar diferentes níveis de acesso para seus profissionais</value>
  </data>
  <data name="VocePodeUtilizarBotaoReplicar" xml:space="preserve">
    <value>Você pode utilizar o botão de replicar</value>
  </data>
  <data name="AgendaESiteCriadosComSucesso" xml:space="preserve">
    <value>A agenda e seu site foram criados!</value>
  </data>
  <data name="ParaAparecerNoPortalTrinksAdicioneLogoEFotos" xml:space="preserve">
    <value>Para que seu site apareça no Portal Trinks e fique disponível para seus atuais e novos clientes, você precisa adicionar a logomarca e pelo menos uma foto do seu estabelecimento.</value>
  </data>
  <data name="ComInformacoesAbaixoConseguiremosMontarAEstruturaDaAgenda" xml:space="preserve">
    <value>Com as informações abaixo, conseguiremos montar a estrutura da sua agenda e também poderemos contar para os seus clientes mais sobre você.</value>
  </data>
  <data name="SelecionamosAbaixoServicosMaisComunsOferecidosPeloSeuTipoDeEstabelecimento" xml:space="preserve">
    <value>Selecionamos abaixo os serviços mais comuns oferecidos pelo seu tipo de estabelecimento.</value>
  </data>
  <data name="SeuEstabelecimentoEstaCadastradoNoTrinks" xml:space="preserve">
    <value>Seu estabelecimento está cadastrado no Trinks. Para configurar a agenda e criar o seu site, complete os 4 passos a seguir.</value>
  </data>
  <data name="ClicandoEm" xml:space="preserve">
    <value>Clicando em</value>
  </data>
  <data name="VoceVeraTodosOsProfissionaisQueRealizamServicoEPodeAssociarEDesassociar" xml:space="preserve">
    <value>você verá todos os profissionais que realizam o serviço e poderá associar ou desassociar profissionais para esse serviço, sem precisar ir até a página de profissionais!</value>
  </data>
  <data name="ParaEvitarQueClientesMarquemHoraComMuitaAntecedencia" xml:space="preserve">
    <value>Para evitar que os clientes marquem uma hora com muita antecedência, você pode limitar qual tempo futuro o cliente terá para agendar no seu estabelecimento. Por exemplo: Se você escolher 1 semana, o cliente só poderá marcar uma hora pelo site de hoje até daqui a sete dias, e assim por diante.</value>
  </data>
  <data name="ALogoDoEstabelecimento" xml:space="preserve">
    <value>A logo do seu estabelecimento</value>
  </data>
  <data name="FotosDoEstabelecimento" xml:space="preserve">
    <value>Foto(s) do estabelecimento</value>
  </data>
  <data name="ParaQueSeuSiteAparecaNaBuscaEhNecessarioAsSeguintesInformacoesPreenchidas" xml:space="preserve">
    <value>Para que o seu site apareça na busca do portal Trinks.com e no aplicativo mobile é necessário que as seguintes informações estejam devidamente preenchidas:</value>
  </data>
  <data name="SeusProfissionaisRecomendamentosQuePossuamFoto" xml:space="preserve">
    <value>Seus profissionais (recomendamos que possuam foto)</value>
  </data>
  <data name="SeusServicosEPrecos" xml:space="preserve">
    <value>Seus serviços e preços</value>
  </data>
  <data name="SeuSiteEstaAaparecendoNaBuscaDoPortalDesde" xml:space="preserve">
    <value>Seu site está aparecendo na busca do portal Trinks.com e no aplicativo mobile desde {0}</value>
  </data>
  <data name="SolicitacaoRealizadaPorEm" xml:space="preserve">
    <value>Solicitação realizada por {0} em {1}.</value>
  </data>
  <data name="CliqueAquiParaAcessarHotsiteDoEstabelecimento" xml:space="preserve">
    <value>Clique aqui para acessar o Hotsite do Estabelecimento</value>
  </data>
  <data name="OsDadosForamSalvosComSucesso" xml:space="preserve">
    <value>Os dados foram salvos com sucesso!</value>
  </data>
  <data name="SolicitacaoDeNaoExibicaoNoPortalFeitaPor" xml:space="preserve">
    <value>Solicitação de não exibição no Portal feita por {0} em {1}.</value>
  </data>
  <data name="AssocieSuaContaAoFacebook" xml:space="preserve">
    <value>Associe sua conta ao Facebook</value>
  </data>
  <data name="DeveSerDefinidoPeloMenosUmDiaDeTrabalho" xml:space="preserve">
    <value>Deve ser definido pelo menos um dia de trabalho.</value>
  </data>
  <data name="SelecioneAbaixoTipoDeAcessoDoProfissional" xml:space="preserve">
    <value>Selecione abaixo o tipo de acesso deste profissional. Para que o profissional possa ter acesso, é necessário informar um e-mail para ele. Este e-mail será o login de acesso deste profissional e automaticamente uma senha será enviada para ele.</value>
  </data>
  <data name="AquiVocePodeColocarSuasObservacoes" xml:space="preserve">
    <value>Aqui você pode colocar suas observações, como o tempo de tolerância para atrasos ou algum aviso para os seus clientes, por exemplo: Não nos responsabilizamos por produtos trazidos pelos clientes.</value>
  </data>
  <data name="EmQueParteDoMeuSiteAsInformacoesVaoAparecer" xml:space="preserve">
    <value>Em que parte do meu site estas informações vão aparecer?</value>
  </data>
  <data name="EsteSeraOEnderecoDoSite" xml:space="preserve">
    <value>Este será o endereço do site que o Trinks cria para o seu negócio. Você deve divulgar para os seus clientes, para que eles comecem a marcar sua hora pela Internet.</value>
  </data>
  <data name="FaleSobreSeuEstabelecimento" xml:space="preserve">
    <value>Fale sobre o seu estabelecimento. O que você quer contar aos seus clientes? Olhe esse exemplo de texto: O estabelecimento Trinks foi inaugurado em 2011 e tem como objetivo levar aos nossos clientes sempre os melhores resultados e a satisfação garantida. Não perca tempo! Marque sua hora pela Internet e fique nos Trinks.</value>
  </data>
  <data name="InformacoesSobreHorariosDoEstabelecimento" xml:space="preserve">
    <value>O Horário de Início deverá ser a hora que o estabelecimento é aberto e o Horário Final deverá ser a hora que ele é fechado e não o último horário de marcação. Exemplo: Se o seu estabelecimento fecha às 20h, o último horário disponível para o cliente agendar pela Internet será de 19:30h, para um serviço que dura 30 minutos, 19h para um serviço de 60 minutos e assim por diante. Mas se mesmo assim você quiser marcar um agendamento para um horário além do informado, não tem problema! Somente os Agendamentos pela Internet se encerrarão de acordo com o horário final.</value>
  </data>
  <data name="InformeAosSeusClientesComoDevemFazerCancelamentos" xml:space="preserve">
    <value>Informe aos seus clientes como eles deverão fazer os seus cancelamentos, como: Cancelamentos deverão ser realizados com, no mínimo, 30 minutos de antecedência, com o objetivo de não prejudicar a agenda do Profissional.</value>
  </data>
  <data name="NaoEncontrouLocalOndeEhClienteEGostariaDeAgendar" xml:space="preserve">
    <value>Não encontrou o local onde já é cliente e gostaria de agendar?</value>
  </data>
  <data name="VamosConvidaloEntrarNoTrinks" xml:space="preserve">
    <value>Vamos convidá-lo a entrar no Trinks!</value>
  </data>
  <data name="IndicacaoRealizadaComSucesso" xml:space="preserve">
    <value>Indicação realizada com sucesso!</value>
  </data>
  <data name="QuerNosFalarAlgumaCoisaSobreOEstabelecimento" xml:space="preserve">
    <value>Quer falar algo sobre o salão ou mandar uma mensagem para ele?</value>
  </data>
  <data name="AImagemAdicionadaAquiSeraALogomarcaDoSeuSite" xml:space="preserve">
    <value>A imagem adicionada aqui será a logomarca do seu site. Ela é importante pois o seu estabelecimento só aparecerá na busca do Portal Trinks se você adicionar a logomarca. Esta imagem pode ser alterada posteriormente.</value>
  </data>
  <data name="VocePodeAdicionarQuantasFotosDoEstabelecimentoDesejarAQualquerMomento" xml:space="preserve">
    <value>Você pode adicionar até 30 fotos do estabelecimento, a qualquer momento! Use este espaço para divulgar seus serviços e novidades!</value>
  </data>
  <data name="ComOTrinksVoce" xml:space="preserve">
    <value>Com o Trinks você</value>
  </data>
  <data name="ConsultaEstabelecimentosOndeVoceJaEhCliente" xml:space="preserve">
    <value>Consulta os estabelecimentos onde você já é cliente para novos agendamentos</value>
  </data>
  <data name="ConsultaSeusServicosAgendadosERealizados" xml:space="preserve">
    <value>Consulta seus serviços agendados e realizados</value>
  </data>
  <data name="EncontraEstabelecimentosNumaLocalidade" xml:space="preserve">
    <value>Encontra estabelecimentos numa localidade (e vê as informações e fotos dos mesmos)</value>
  </data>
  <data name="EncontraEstabelecimentosQueRealizamUmDeterminadoServico" xml:space="preserve">
    <value>Encontra estabelecimentos que realizam um 
determinado serviço numa localidade e que funcionem numa data selecionada 
(e vê as informações e fotos dos mesmos)</value>
  </data>
  <data name="EncontreUmHorarioLivreEMarqueSuaHora" xml:space="preserve">
    <value>Encontre um horário livre e marque sua hora</value>
  </data>
  <data name="SelecionaEAgendaOndeVaiRealizarOServico" xml:space="preserve">
    <value>Seleciona e agenda onde vai realizar o serviço, com que profissional, e em qual dia e horário</value>
  </data>
  <data name="SuaBelezaAUmCliqueDeVoce" xml:space="preserve">
    <value>SUA BELEZA A UM CLIQUE</value>
  </data>
  <data name="TudoUsandoSeuComputadorTabletOuSmartPhone" xml:space="preserve">
    <value>Tudo usando seu computador, tablet ou smartphone</value>
  </data>
  <data name="AgendePelo" xml:space="preserve">
    <value>AGENDE PELO</value>
  </data>
  <data name="Aprova" xml:space="preserve">
    <value>APROVA</value>
  </data>
  <data name="NaoEncontrouOEstabelecimentoQueProcurava" xml:space="preserve">
    <value>Não encontrou o estabelecimento que procurava? Clique aqui e nos ajude a deixá-lo nos</value>
  </data>
  <data name="Opcional" xml:space="preserve">
    <value>Opcional</value>
  </data>
  <data name="PodeFazerPorVoce" xml:space="preserve">
    <value>pode fazer por você</value>
  </data>
  <data name="QuemJaEstaNos" xml:space="preserve">
    <value>QUEM JÁ ESTÁ NOS</value>
  </data>
  <data name="QuemUsaO" xml:space="preserve">
    <value>QUEM USA O</value>
  </data>
  <data name="SmartPhoneETablet" xml:space="preserve">
    <value>SMARTPHONE E TABLET</value>
  </data>
  <data name="VoceTemUmEstabelecimentoDeEsteticaEBeleza" xml:space="preserve">
    <value>Você tem um estabelecimento de estética e beleza? Veja tudo que o</value>
  </data>
  <data name="AcompanheOTrinksNo" xml:space="preserve">
    <value>Acompanhe o Trinks no</value>
  </data>
  <data name="TodosDireitosReservados" xml:space="preserve">
    <value>® 2017, Trinks - CNPJ 73.499.238/0001-87 - Todos os direitos reservados.</value>
  </data>
  <data name="ADataInformadaNaoPodeSerAnteriorAHoje" xml:space="preserve">
    <value>A data informada não pode ser anterior a hoje.</value>
  </data>
  <data name="OCampoOndeProcurarEhObrigatorio" xml:space="preserve">
    <value>O campo Onde Procurar é obrigatório! É preciso preenchê-lo para realizar a busca.</value>
  </data>
  <data name="CliqueAqui" xml:space="preserve">
    <value>Clique aqui</value>
  </data>
  <data name="QueroAparecerNoTrinks" xml:space="preserve">
    <value>Quero aparecer no Aplicativo e no Portal do Trinks.com</value>
  </data>
  <data name="OpcaoRestritaParaUsuariosComAcessoTotal" xml:space="preserve">
    <value>Opção restrita para usuários com acesso total.</value>
  </data>
  <data name="VocePodeCustomizarServicosClicandoEmEditar" xml:space="preserve">
    <value>Você pode customizar os serviços, alterando a descrição, o preço, a duração e a foto clicando em Editar</value>
  </data>
  <data name="AcessoPropriaAgendaEPainelAtendimento" xml:space="preserve">
    <value>Profissional II - acesso à própria agenda, lançamento de serviços e comissões</value>
  </data>
  <data name="CasoDesejeRemoverEsteServicoFavorDesassociarTodosOsProfissionais" xml:space="preserve">
    <value>Caso deseje remover este serviço, favor desassociá-lo antes de todos os profissionais que o realizam.</value>
  </data>
  <data name="NaoEhPossivelRealizarPagamentosDeServicosJaPagos" xml:space="preserve">
    <value>O pagamento deste atendimento já foi realizado. Por isso, não é possível realizar alterações neste horário. Atualize a página para ver os dados recentes.</value>
  </data>
  <data name="NaoEPossivelRemoverTodosOsServicosDoEstabelecimento" xml:space="preserve">
    <value>Não é possivel remover todos os serviços do estabelecimento, para remover esse serviço cadastre primeiro um novo serviço.</value>
  </data>
  <data name="NaoSeraPossivelRealizarAAcaoPoisENecessarioTerPeloMenosUmProfissionalAssociadoAServico" xml:space="preserve">
    <value>Não será possível realizar a ação, pois é necessário ter pelo menos um profissional associado a um serviço ativo do estabelecimento.</value>
  </data>
  <data name="Busca_EstabelecimentoNaoFuncionara" xml:space="preserve">
    <value>Não iremos funcionar no dia informado</value>
  </data>
  <data name="Busca_EstabelecimentoNaoPermite" xml:space="preserve">
    <value>Este estabelecimento não permite a consulta aos seus horários através do hotsite. Para marcar uma hora, entre em contato através do(s) telefone(s)</value>
  </data>
  <data name="Busca_SemRegistros" xml:space="preserve">
    <value>Não encontramos nenhum horário disponível para hoje. Que tal procurar outro dia?</value>
  </data>
  <data name="NaoEPossivelRealizarAlteracoesEmUmAgendamentoQueJaFoiPago" xml:space="preserve">
    <value>O pagamento deste atendimento já foi realizado. Por isso, não é possível realizar alterações neste horário. Atualize a página para ver os dados recentes.</value>
  </data>
  <data name="PedidoDeAgendamentoRealizado" xml:space="preserve">
    <value>Pedido de agendamento realizado!</value>
  </data>
  <data name="AgendamentoRealizadoComSucesso" xml:space="preserve">
    <value>Agendamento realizado com sucesso</value>
  </data>
  <data name="ExistemAlgunsServicosComProfissionaisAssociados" xml:space="preserve">
    <value>Existem alguns serviços com profissionais associados. Para realizar a ação, você deve desassociar os profissionais dos seguintes serviços:</value>
  </data>
  <data name="ServicoAssociado" xml:space="preserve">
    <value>Serviço(s) associado(s) com sucesso!</value>
  </data>
  <data name="ServicoJaAssociado" xml:space="preserve">
    <value>Serviço(s) já associado(s) ao estabelecimento!</value>
  </data>
  <data name="SelecionePeloMenosUmaOpcao" xml:space="preserve">
    <value>Marque pelo menos uma opção</value>
  </data>
  <data name="SeVocePossuiUmSiteEGostariaDeIncluirLink" xml:space="preserve">
    <value>Se você já possui um site e gostaria de incluir um link para ele no site que o Trinks vai criar para você, digite aqui o endereço</value>
  </data>
  <data name="NenhumRegistroEncontrado" xml:space="preserve">
    <value>Nenhum resultado encontrado.</value>
  </data>
  <data name="NaoEhPossivelExcluirPoisEhNecessarioUmProfissionalComAcessoAAgenda" xml:space="preserve">
    <value>Não é possível excluir, pois é necessário que pelo menos um profissional tenha acesso à agenda!</value>
  </data>
  <data name="EmailDeveSerPreenchidoCorretamente" xml:space="preserve">
    <value>O e-mail deve ser preenchido corretamente</value>
  </data>
  <data name="DeUsoGratis" xml:space="preserve">
    <value>de uso grátis!</value>
  </data>
  <data name="GratuitoPorXDias" xml:space="preserve">
    <value>gratuito por {0} dias!</value>
  </data>
  <data name="AssociacaoDeProfissionaisAoServicoFeitaComSucesso" xml:space="preserve">
    <value>Associação de profissionais ao serviço feita com sucesso!</value>
  </data>
  <data name="AssociePeloMenosUmProfissionalAoServico" xml:space="preserve">
    <value>Associe pelo menos um profissional ao serviço!</value>
  </data>
  <data name="OServicoEhObrigatorioParaSalvarOAgendamento" xml:space="preserve">
    <value>O campo serviço é obrigatório para salvar o agendamento</value>
  </data>
  <data name="ExistemComissoesNaoInformadasOuComValorZero" xml:space="preserve">
    <value>Existem comissões não informadas ou com valor zero. Deseja prosseguir?</value>
  </data>
  <data name="ConfirmarAgendamentoEmPeriodoDeAusencia" xml:space="preserve">
    <value>O profissional está marcado como ausente neste horário.</value>
  </data>
  <data name="ConfirmarAgendamentoForaHorarioEspecial" xml:space="preserve">
    <value>Hoje o estabelecimento funciona em um horário especial e você está marcando uma hora fora deste horário.</value>
  </data>
  <data name="ConfirmarAgendamentoForaHorarioTrabalho" xml:space="preserve">
    <value>Você está tentando marcar uma hora fora do horário de funcionamento do estabelecimento.</value>
  </data>
  <data name="ConfirmarAgendamentoForaHorarioTrabalhoProfissional" xml:space="preserve">
    <value>Você está tentando marcar uma hora fora do horário de trabalho do profissional.</value>
  </data>
  <data name="DataLimiteRecorrencia" xml:space="preserve">
    <value>Atenção! Data final deve ser menor ou igual a {0}. O tempo total permitido para recorrência é de {1} dias.</value>
  </data>
  <data name="Erro_Salvar_Registro_Ausencia" xml:space="preserve">
    <value>Erro ao salvar o registro de ausência!</value>
  </data>
  <data name="FechamentoContaRealizadoComSucesso" xml:space="preserve">
    <value>Fechamento de conta realizado com sucesso!</value>
  </data>
  <data name="IncluirServicoParaCheckOut" xml:space="preserve">
    <value>É preciso adicionar pelo menos um serviço para realizar o check out!</value>
  </data>
  <data name="NaoEncontrouAgendamentos" xml:space="preserve">
    <value>Não foram encontrados agendamentos para os filtros aplicados.</value>
  </data>
  <data name="ValorNaoQuitado" xml:space="preserve">
    <value>O valor pago deve ser maior que o valor a pagar!</value>
  </data>
  <data name="RestricaoAgendamento" xml:space="preserve">
    <value>Opa, a gente agenda por aqui com até {0} dias de antecedência.
Visita a gente antes, vai! É só escolher uma data nova. :p</value>
  </data>
  <data name="SeVoceNaoEncontrouOQueProcuravaNaListaAcima" xml:space="preserve">
    <value>se você não encontrou o que procurava na lista acima</value>
  </data>
  <data name="DesejaRedefinirSenhaDoUsuario" xml:space="preserve">
    <value>Deseja realmente redefinir a senha do usuário?</value>
  </data>
  <data name="NovaSenhaGeradaParaCliente" xml:space="preserve">
    <value>Nova senha gerada para o cliente</value>
  </data>
  <data name="CpfInvalido" xml:space="preserve">
    <value>O cpf é inválido</value>
  </data>
  <data name="AproveiteOsBeneficiosDoTrinksParaSeuEstabelecimento" xml:space="preserve">
    <value>Aproveite os benefícios que o Trinks traz pro seu estabelecimento</value>
  </data>
  <data name="DiaDeUsoGratisDoTrinks" xml:space="preserve">
    <value>dia de uso grátis do Trinks.com!</value>
  </data>
  <data name="HojeEhSeu" xml:space="preserve">
    <value>Hoje é seu</value>
  </data>
  <data name="SeusDiasDeGratuidade" xml:space="preserve">
    <value>Seus dias de gratuidade</value>
  </data>
  <data name="VoceAindaPossuiUmDiaDeUsoGratis" xml:space="preserve">
    <value>Você ainda possui 1 dia de uso grátis!</value>
  </data>
  <data name="ADataDoSeuComputadorDeveEstarDentroDoPeriodoDeVencimentoDaFaturaMaisAntigaPendenteDePagamento" xml:space="preserve">
    <value>A data do seu computador deve estar dentro do período de vencimento da fatura mais antiga pendente de pagamento !</value>
  </data>
  <data name="NaoEhPermitidoInformarLetrasOuCaracteresEspeciaisNoNumeroDoEndereco" xml:space="preserve">
    <value>Não é permitido informar letras ou caracteres especiais no número do endereço.</value>
  </data>
  <data name="NaoForamEncontradosClientesParaOsFiltrosAplicados" xml:space="preserve">
    <value>Não foram encontrados clientes para os filtros aplicados.</value>
  </data>
  <data name="AObservacaoDoFornecedorNaoPodeUltrapassar400Caracteres" xml:space="preserve">
    <value>A observação do fornecedor não pode ultrapassar 400 caracteres!</value>
  </data>
  <data name="ONomeFantasiaFornecedorEhObrigatorio" xml:space="preserve">
    <value>O nome fantasia do fornecedor é obrigatório!</value>
  </data>
  <data name="ONomeDoTipoDespesaEhObrigatorio" xml:space="preserve">
    <value>O nome do tipo de despesa é obrigatório!</value>
  </data>
  <data name="ONomeDoTipoDespesaNaoPodeUltrapassar200Caracteres" xml:space="preserve">
    <value>O nome do tipo de despesa não pode ultrapassar 200 caracteres</value>
  </data>
  <data name="JaExisteUmTipoDeDespesaComADescricaoInformada" xml:space="preserve">
    <value>Já existe um tipo de despesa com a descrição informada!</value>
  </data>
  <data name="AsObervacoesSobreOFornecedorNaoPodeUltrapassar400Caracteres" xml:space="preserve">
    <value>As observações sobre o fornecedor não pode ultrapassar os 400 caracteres.</value>
  </data>
  <data name="JaExisteUmFornecedorComEsseNomeFantasia" xml:space="preserve">
    <value>Já existe um fornecedor com esse nome fantasia.</value>
  </data>
  <data name="ONomeFantasiaEObrigatorio" xml:space="preserve">
    <value>O nome fantasia é obrigatório</value>
  </data>
  <data name="ONomeFantasiaNaoPodeUltrapassar55Caracteres" xml:space="preserve">
    <value>O nome fantasia não pode ultrapassar 55 caracteres</value>
  </data>
  <data name="ADespesaSoPodeSerRelacionadoAUmProfissionalOuFornecedor" xml:space="preserve">
    <value>A despesa só pode ser relacionada a um profissional ou a um fornecedor</value>
  </data>
  <data name="ADuracaoMaximaDeUmaDespesaApenasUmAno" xml:space="preserve">
    <value>A duração máxima de uma despesa é de até um ano!</value>
  </data>
  <data name="ParaReplicarUmaDespesaEhNecessarioQueOMesAnoSejaDataSuperiorDataVencimento" xml:space="preserve">
    <value>Para replicar uma despesa é necessário que o mês/ano seja de uma data superior à Data de Vencimento!</value>
  </data>
  <data name="EmailInvalido" xml:space="preserve">
    <value>E-mail inválido</value>
  </data>
  <data name="DiaDeAcessoAoTrinks" xml:space="preserve">
    <value>Dia de acesso ao Trinks</value>
  </data>
  <data name="HojeSeriaSeu" xml:space="preserve">
    <value>Hoje seria seu</value>
  </data>
  <data name="SeusDiasDeAcessoAoTrinks" xml:space="preserve">
    <value>Seus dias de acesso ao Trinks terminaram</value>
  </data>
  <data name="LembreteAlteracaoDeComissao" xml:space="preserve">
    <value>O valor da comissão do profissional é calculado de acordo com o valor pago pelo cliente por cada serviço e produto vendido. Se o serviço/produto for uma cortesia ou possuir algum tipo de desconto que não deve influenciar na comissão do profissional, usuários com acesso total ao sistema poderão definir manualmente o valor da comissão no Relatório de Comissões.</value>
  </data>
  <data name="NovoFluxoFinanceiroPorFormaPagamento" xml:space="preserve">
    <value>&lt;p&gt;&lt;b class="destaque"&gt;Novo!&lt;/b&gt; Agora você pode ver o seu fluxo financeiro por forma de pagamento. Mas não é só isso! Você também consegue consultar:&lt;/p&gt;&lt;p&gt;&lt;b&gt;Valor a ser recebido&lt;/b&gt;: é o valor final que a operadora de cartão (de crédito ou de débito) vai depositar na sua conta, já descontando o percentual cobrado pela operadora.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Data prevista de recebimento&lt;/b&gt;:  é a data prevista para você receber da operadora o valor desta transação, na sua conta.&lt;/p&gt;&lt;p&gt;Para desfrutar completamente deste relatório, preencha os valores "% cobrado pela operadora" e "Nº de dias para recebimento" na tela Visualizar/Editar &gt; Site e Estabelecimento &gt; Formas de Pagamento".&lt;/p&gt;</value>
  </data>
  <data name="NovoParaCadaFormaPagamentoAgoraVocePodeInformar" xml:space="preserve">
    <value>&lt;p&gt;&lt;b class="destaque"&gt;Novo!&lt;/b&gt;  Para cada forma de pagamento, você agora pode informar:&lt;/p&gt;&lt;p&gt;&lt;b&gt;% cobrado pela operadora&lt;/b&gt;: é o valor percentual que a operadora do cartão (de crédito ou de débito) cobra por transação realizada. &lt;/p&gt;&lt;p&gt;&lt;b&gt;Nº de dias para recebimento&lt;/b&gt;: é o número de dias que a operadora do cartão leva para depositar o dinheiro na sua conta.&lt;/p&gt;&lt;p&gt;Preencha esses novos campos e confira nosso novo relatório: "Fluxo Financeiro por Forma de Pagamento"&lt;/p&gt;</value>
  </data>
  <data name="DataMovimentacaoNaoPodeMAiorHoje" xml:space="preserve">
    <value>A data da movimentação não pode ser maior que a data atual.</value>
  </data>
  <data name="AgendamentoNaoRealizadoVoceNaoEstaAutorizadoMarcarHoraNoEstabelecimento" xml:space="preserve">
    <value>Agendamento NÃO REALIZADO. Você não está autorizado a marcar hora neste estabelecimento. Favor entrar em contato para mais detalhes.</value>
  </data>
  <data name="IndicarCasoSejaFornecedorDeProdutosUtilizadosPeloEstabelecimento" xml:space="preserve">
    <value>Indicar caso seja fornecedor de produtos utilizados pelo estabelecimento para realização de serviços e/ou venda para clientes e profissionais. Fornecedores de produtos com controle de estoque.</value>
  </data>
  <data name="MensagemNenhumEncontrado" xml:space="preserve">
    <value>Nenhum resultado encontrado</value>
  </data>
  <data name="JaExisteUmFabricanteComEsseNome" xml:space="preserve">
    <value>Já existe um fabricante com esse nome.</value>
  </data>
  <data name="ONomeFabricanteEhObrigatorio" xml:space="preserve">
    <value>O nome do fabricante é obrigatório!</value>
  </data>
  <data name="ONomeFabricanteNaoPodeUltrapassar55Caracteres" xml:space="preserve">
    <value>O nome do fabricante não pode ultrapassar 55 caracteres.</value>
  </data>
  <data name="PacoteComItensJaConsumidosEmOutroFechamento" xml:space="preserve">
    <value>Pacote com itens já consumidos em outro fechamento. Antes de estornar esta conta, 
é necessário estornar os fechamentos que possuem consumo deste pacote. O estorno não poderá ser realizado.</value>
  </data>
  <data name="CodigoDePropriedadeDoProfissional" xml:space="preserve">
    <value>Código de propriedade do profissional</value>
  </data>
  <data name="OPatEhOPainelDeAtendimentoOndeOsProfissionaisLancamServicosRealizados" xml:space="preserve">
    <value>O PAT é o painel de atendimento onde os profissionais lançam e atualizam serviços realizados. O acesso é através de um código próprio do profissional e este não precisa necessariamente ter acesso ao sistema através de seu email. Apenas profissionais que executam serviços têm acesso ao PAT.</value>
  </data>
  <data name="SaoOs4PrimeirosDigitosDoCPFDoProfissional" xml:space="preserve">
    <value>São os 4 primeiros dígitos do CPF do profissional. Deverá ser alterado no próximo acesso ao PAT.</value>
  </data>
  <data name="InformeAoProfissional" xml:space="preserve">
    <value>Informe ao profissional!</value>
  </data>
  <data name="NotificacaoLembreteAgendamento" xml:space="preserve">
    <value>Hoje você tem {1}
{0}: {2} ou {3}</value>
  </data>
  <data name="NotificacaoLembreteAgendamentoItem" xml:space="preserve">
    <value>{0} às {1} {2}</value>
  </data>
  <data name="AgendamentosAPartirDe" xml:space="preserve">
    <value>Agendamentos somente poderão ser realizados com data a partir do dia {0:d}</value>
  </data>
  <data name="SoEhPossivelCarregarValoresAPartirDe" xml:space="preserve">
    <value>Só é possível carregar valores a partir de {0:MMMM} de {0:yyyy}</value>
  </data>
  <data name="SomenteEhPossivelRegistrarVendaAPartirDe" xml:space="preserve">
    <value>Somente é possivel registrar vendas com data a partir de {0:d}</value>
  </data>
  <data name="AliquotaInvalida" xml:space="preserve">
    <value>O percentual de Alíquota ISS deve estar entre 0% e 100%.</value>
  </data>
  <data name="PeriodoMovimentacao" xml:space="preserve">
    <value>Movimentações com {0} entre {1:dd/MM/yyyy} e {2:dd/MM/yyyy}</value>
  </data>
  <data name="NaoIdentificadoPagamentoBoleto" xml:space="preserve">
    <value>Não foi identificado o pagamento do seu boleto.</value>
  </data>
  <data name="ParaRealizarOSeuPagamentoNoCartao" xml:space="preserve">
    <value>para realizar o seu pagamento no cartão</value>
  </data>
  <data name="PorFavorEnvieComprovantePara" xml:space="preserve">
    <value>Por favor, envie o comprovante para</value>
  </data>
  <data name="SuaContaSeraAutomaticamenteBloqueadaEm1Dia" xml:space="preserve">
    <value>Sua conta será automaticamente bloqueada em 1 dia.</value>
  </data>
  <data name="SuaContaSeraAutomaticamenteBloqueadaEmXDias" xml:space="preserve">
    <value>Sua conta será automaticamente bloqueada em {0} dias.</value>
  </data>
  <data name="HojeEhOUltimoDiaParaEnviarSeuComprovantePara" xml:space="preserve">
    <value>Hoje é o último dia para enviar seu comprovante para</value>
  </data>
  <data name="ParaQueSuaContaNaoSejaBloqueada" xml:space="preserve">
    <value>para que sua conta não seja bloqueada.</value>
  </data>
  <data name="Preencha_Url_Hotsite" xml:space="preserve">
    <value>« Preencha a URL do Hotsite</value>
  </data>
  <data name="EsteHorarioNaoSeEncontraMaisDisponivel" xml:space="preserve">
    <value>Este horário não se encontra mais disponível</value>
  </data>
  <data name="InstrucaoDeAtivacaoCreditoCliente" xml:space="preserve">
    <value>Para incluir créditos para os clientes é necessário habilitar o "Crédito de Cliente" como forma de pagamento em Configurações >> Configurações do Sistema >> Forma de pagamento.</value>
  </data>
  <data name="InstrucaoDeAtivacaoVendaValePresente" xml:space="preserve">
    <value>Para incluir vales-presente é necessário utilizar a forma de pagamento "Vale-Presente". Caso deseje utilizá-la, acesse Visualizar/Editar &gt; Site e Estabelecimento &gt; Dados do Estabelecimento e em Forma de Pagamento marque a opção "Vale-Presente".</value>
  </data>
  <data name="OCnpjIndicadoJaEstaCadastradoNoSistema" xml:space="preserve">
    <value>O CNPJ indicado já está cadastrado no sistema.</value>
  </data>
  <data name="DesejaImprimirValeProfissional" xml:space="preserve">
    <value>Deseja imprimir o comprovante do vale/adiantamento do profissional agora?</value>
  </data>
  <data name="BoletoPendenteDePagamento" xml:space="preserve">
    <value>Não identificamos seu pagamento até o momento.</value>
  </data>
  <data name="NaoPossuiCartaoDeCredito" xml:space="preserve">
    <value>Não possui cartão de crédito?</value>
  </data>
  <data name="BoletoSeraGeradoACada30Dias" xml:space="preserve">
    <value>A cada 30 dias será gerado uma nova fatura para pagamento, de acordo com a faixa em que você se encontrar.</value>
  </data>
  <data name="BaixeOBoletoERealizeOPagamento" xml:space="preserve">
    <value>Baixe o boleto e realize o pagamento para garantir a continuidade de seu acesso!</value>
  </data>
  <data name="BaixarBoleto" xml:space="preserve">
    <value>Baixar boleto</value>
  </data>
  <data name="ValorCobradoMensalmente" xml:space="preserve">
    <value>O valor é cobrado mensalmente de acordo com a faixa de profissionais com agenda que seu estabelecimento se encontra</value>
  </data>
  <data name="ValorVariaDeAcordoComQtdProfissionaisNoMomentoDaGeracao" xml:space="preserve">
    <value>O valor da mensalidade será referente a faixa de profissionais contratada, ou de acordo com a faixa acima considerando o número de profissionais máximos com agenda do estabelecimento.</value>
  </data>
  <data name="InformacoesSobreInclusaoDeFotosNosServicosParaProfissionaisQueUtilizamPATePainel" xml:space="preserve">
    <value>Selecione “Sim” para profissionais que realizam serviços e que vão poder incluir fotos relacionadas aos mesmos. Este acesso é apenas para profissionais que utilizam o PAT e o Painel de Atendimento. Recepcionistas e Administradores sempre poderão incluir fotos de clientes através de outras telas.</value>
  </data>
  <data name="BuscaPortalEAplicativo" xml:space="preserve">
    <value>Busca do Portal e do Aplicativo</value>
  </data>
  <data name="ParaSeuQueSeuSiteAparecaNa" xml:space="preserve">
    <value>Para aparecer na</value>
  </data>
  <data name="ParaSeuQueSeuSiteAparecaNaParte2" xml:space="preserve">
    <value>do Trinks.com é necessario cadastrar</value>
  </data>
  <data name="SeusProfissionais" xml:space="preserve">
    <value />
  </data>
  <data name="EstasMensagensSaoCortesiaDoTrinks" xml:space="preserve">
    <value>Estas mensagens são cortesia do Trinks.com, ou seja, não abatem o saldo do seu pacote de SMS adquirido para campanhas de SMS Marketing (promoção por tempo indeterminado).</value>
  </data>
  <data name="EsteEOTextoQueSeraEnviadoParaSeusClientes" xml:space="preserve">
    <value>Este é o texto da mensagem (que poderá ser alterado a qualquer momento) que será enviada para os seus clientes por SMS quando eles fizerem aniversário.</value>
  </data>
  <data name="TextoSmsAniversarioPadrao" xml:space="preserve">
    <value>Feliz aniversário, [cliente], tudo de bom!</value>
    <comment>Utilizado em conjunto com String.Format. Deve ser passado o nome de exibição do Estabelecimento e a URL do mesmo no Trinks.</comment>
  </data>
  <data name="AMensagemPrecisaSerDefinidaParaAtivarSmsAniversario" xml:space="preserve">
    <value>A mensagem precisa ser definida para que o envio de SMS para os aniversariantes seja ativado!</value>
  </data>
  <data name="SeDesejarQueONomeDoClienteSaiaNoSms" xml:space="preserve">
    <value>Se desejar que o nome do cliente saia no {0}, coloque o texto [cliente] (entre colchetes) na posição desejada. No momento do envio, o primeiro e segundo nomes substituirão o texto [cliente].</value>
  </data>
  <data name="InformeONumeroDeDiasDeAntecedencia" xml:space="preserve">
    <value>Informe o número de dias de antecedência para envio do SMS.</value>
  </data>
  <data name="OEnvioDosSmsEstaInativo" xml:space="preserve">
    <value>O envio dos {0} está inativo. Deseja ativá-lo em outro momento?</value>
  </data>
  <data name="VerHistorico" xml:space="preserve">
    <value>Ver Histórico...</value>
  </data>
  <data name="MensagemAjudaCampanhaSmsEnvioSuspenso" xml:space="preserve">
    <value>No momento do envio, foi verificado que a situação financeira do estabelecimento não estava regularizada. Se já está tudo ok, edite a campanha e programe novo envio!</value>
  </data>
  <data name="MensagemAjudaCampanhaSmsPendenteCredito" xml:space="preserve">
    <value>Não houve saldo de disparos suficiente para envio da mensagem para todos os clientes. Clique na lupa para verificar e se desejar, solicite o disparo depois de comprar crédito de SMS.</value>
  </data>
  <data name="DesejaRealmenteExcluirEstaCampanha" xml:space="preserve">
    <value>Deseja realmente excluir esta campanha?</value>
  </data>
  <data name="InformacaoSaldoSmsMarketing" xml:space="preserve">
    <value>Você está com saldo de {0} SMS para disparo.</value>
  </data>
  <data name="InformacaoSaldoEmailMarketing" xml:space="preserve">
    <value>Você está com saldo de {0} e-mails para envio.</value>
  </data>
  <data name="InformacaoSaldoZeroSmsMarketing" xml:space="preserve">
    <value>Você não possui saldo de SMS para disparo.</value>
  </data>
  <data name="InformacaoSaldoZeroEmailMarketing" xml:space="preserve">
    <value>Você não possui saldo de e-mail para disparo.</value>
  </data>
  <data name="InformacaoConfiguracoesEmailAniversariantesAlteradas" xml:space="preserve">
    <value>As configurações de e-mail de aniversariantes foram alteradas. Caso saia desta páginas as alterações serão perdidas.</value>
  </data>
  <data name="InformacaoConfiguracoesSmsAniversariantesAlteradas" xml:space="preserve">
    <value>As configurações de SMS de aniversariantes foram alteradas. Caso saia desta páginas as alterações serão perdidas.</value>
  </data>
  <data name="EstasMensagensSaoCortesiaDoTrinks_Email" xml:space="preserve">
    <value>Estes e-mails são cortesia do Trinks.com, ou seja, não abatem do saldo do pacote mensal de e-mails e nem de pacotes adquiridos para campanhas de e-mail Marketing (promoção por tempo indeterminado).</value>
  </data>
  <data name="EsteEOTextoQueSeraEnviadoParaSeusClientes_Email" xml:space="preserve">
    <value>Este é o texto do e-mail (que poderá ser alterado a qualquer momento) que será enviado para os seus clientes quando eles fizerem aniversário.</value>
  </data>
  <data name="TextoEmailAniversarioPadrao" xml:space="preserve">
    <value>&lt;div&gt;&lt;strong&gt;Ol&amp;aacute;, [cliente],&lt;/strong&gt;&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt;Parab&amp;eacute;ns pelo seu anivers&amp;aacute;rio!&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt;A equipe do {0} deseja tudo de bom e aguarda a sua visita!&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt;Agende em {1}&lt;/div&gt;</value>
  </data>
  <data name="NaoForamEncontradosProfissionaisParaOsFiltrosAplicados" xml:space="preserve">
    <value>Não foram encontrados profissionais para os filtros aplicados.</value>
  </data>
  <data name="NaoForamEncontradosServicosParaOsFiltrosAplicados" xml:space="preserve">
    <value>Não foram encontrados serviços para os filtros aplicados</value>
  </data>
  <data name="NaoForamEncontradosProdutosParaOsFiltrosAplicados" xml:space="preserve">
    <value>Não foram encontrados produtos para os filtros aplicados.</value>
  </data>
  <data name="EncontreOsMelhoresSaloesClinicasSpasDoBrasil" xml:space="preserve">
    <value>Encontre os melhores salões, clínicas de estética, SPAs e Barbearias do Brasil</value>
  </data>
  <data name="InformeCategoriaGrupo" xml:space="preserve">
    <value>Você precisa informar uma categoria para o tipo de despesa</value>
  </data>
  <data name="NaoEhPossivelRealizarCheckoutNovamente" xml:space="preserve">
    <value>Este fechamento já foi realizado.</value>
  </data>
  <data name="UtilizeOutroCartaoParaPagamentoDeSuaFatura" xml:space="preserve">
    <value>Utilize outro cartão para pagamento de sua fatura</value>
  </data>
  <data name="InformePagamentoDoBoletoParaDesbloqueio" xml:space="preserve">
    <value>Informe o pagamento do seu boleto para desbloqueio imediato</value>
  </data>
  <data name="ApenasCadastroDeCliente" xml:space="preserve">
    <value>Acesso apenas ao cadastro de clientes</value>
  </data>
  <data name="AcessoConsultaDeClientes" xml:space="preserve">
    <value>Acesso à consulta de clientes</value>
  </data>
  <data name="LimiteDeFaixaAtingidoParaAssinaturaComBoletos" xml:space="preserve">
    <value>Você já atingiu o limite de profissionais ativos com agenda de serviços permitidos pelo seu plano atual. Para adicionar novos profissionais com agenda de serviços entre em contato com o atendimento do Trinks para alterar a sua faixa de profissionais do plano. As alterações não serão salvas a menos que você indique que este profissional não realiza serviços.</value>
  </data>
  <data name="LimiteDeFaixaAtingidoParaAssinaturaComCartaoDeCredito" xml:space="preserve">
    <value>Ao ativar a agenda de serviços para este profissional, você estará ultrapassando o limite da faixa atual de &lt;br /&gt;profissionais e, portanto, a próxima cobrança no seu cartão de crédito será de {0}. Deseja continuar?</value>
  </data>
  <data name="NecessarioInformarValorResgateParaTodosOsItens" xml:space="preserve">
    <value>É necessário informar um valor superior a zero para todos os itens com resgate ativado.</value>
  </data>
  <data name="ProgramaDeFidelidadeNaoConfigurado" xml:space="preserve">
    <value>O programa de fidelidade do seu estabelecimento não está configurado. Para configurá-lo, vá em "Programa de Fidelidade &gt; Configuração".</value>
  </data>
  <data name="AdicionarItensSemProgramaDeFidelidadeConfigurado" xml:space="preserve">
    <value>O programa de fidelidade do seu estabelecimento não está configurado. Para incluir itens no programa, vá em "Programa de Fidelidade &gt; Configuração".</value>
  </data>
  <data name="BoletoLiberadoParaPagamento" xml:space="preserve">
    <value>Sua fatura já está disponível para pagamento e sua assinatura foi</value>
  </data>
  <data name="RealizadaSucesso" xml:space="preserve">
    <value>Realizada com sucesso</value>
  </data>
  <data name="FechamentoComResiduosComissoesDoMesAnterior" xml:space="preserve">
    <value>Neste fechamento estão sendo considerados R$ {0} em comissão de serviços e R$ {1} em comissão de produtos do período {2} referente a registros com data de liberação da comissão para {3} e pagos após o fechamento deste período ou cadastrados retroativamente. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores.</value>
  </data>
  <data name="FechamentoComResiduosComissoesEmDiferentesMeses" xml:space="preserve">
    <value>Neste fechamento estão sendo considerados R$ {0} em comissão de serviços e R$ {1} em comissão de produtos do período {2} referente a registros com data de liberação da comissão para {3} e R$ {4} em comissão de serviços e R$ {5} em comissão de produtos com  data de liberação da comissão em {6} estão considerados no Fechamento Mensal de {7}, pois existem registros que foram pagos após o fechamento do mês ou cadastrados retroativamente. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores.</value>
  </data>
  <data name="FechamentoComResiduosComissoesNoProximoMes" xml:space="preserve">
    <value>R$ {0} em comissão de serviços e R$ {1} em comissão de produtos com data de liberação da comissão em {2} estão considerados no Fechamento Mensal de {3}, pois existem registros que foram pagos após o fechamento deste período ou cadastrados retroativamente. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores.</value>
  </data>
  <data name="RelatorioComissaoComResiduosDoPeriodoAnterior" xml:space="preserve">
    <value>Existem {0} de comissões com data de liberação neste período que tiveram seu atendimento no mês anterior. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores.</value>
  </data>
  <data name="RelatorioComissaoComResiduosNoPeriodoAnteriorENoProximoPeriodo" xml:space="preserve">
    <value>Existem {0} de comissões com data de liberação neste período que tiveram seu atendimento no mês anterior e {1} de atendimento deste período com data de liberação para o próximo mês. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores.</value>
  </data>
  <data name="RelatorioComissaoComResiduosParaOProximoPeriodo" xml:space="preserve">
    <value>Existem {0} de comissões com data de atendimento deste período com data de liberação para o próximo mês. Ao comparar o Fechamento Mensal atual com o Relatório de Comissão levar em consideração estes valores.</value>
  </data>
  <data name="QuantidadeCaracteresSmsAniversarianteExcedido" xml:space="preserve">
    <value>A quantidade de caracteres do SMS aniversariante é maior que o permitido</value>
  </data>
  <data name="ImagemNaoAtendeRequisitosSistema" xml:space="preserve">
    <value>A imagem selecionada não atende aos requisitos do sistema.\nO arquivo deve ter entre 0MB e 4MB e estar no formato PNG.</value>
  </data>
  <data name="EmailNaoPossuiContaNoTrinks" xml:space="preserve">
    <value>O e-mail não confere com as informações cadastradas no Trinks.</value>
  </data>
  <data name="EmailSenhaInvalidos" xml:space="preserve">
    <value>O e-mail e/ou a senha não conferem com as informações cadastradas no Trinks.</value>
  </data>
  <data name="OCampoEmailEObrigatorio" xml:space="preserve">
    <value>O campo "e-mail" é obrigatório!</value>
  </data>
  <data name="UsuarioFacebookInvalido" xml:space="preserve">
    <value>O usuário do facebook informado não pôde ser utilizado devido a um erro na requisição.</value>
  </data>
  <data name="UsuarioFacebookJaPossuiContaNoTrinks" xml:space="preserve">
    <value>Seu usuário já possui uma conta associada ao Trinks.</value>
  </data>
  <data name="NaoEPossivelVerAvaliacoesDesteEstabelecimento" xml:space="preserve">
    <value>Não é possível visualizar as avaliações deste estabelecimento.</value>
  </data>
  <data name="SeJaEfetuouPagamentoDoSeuBoletoCliqueAquiParaDesbloqueioImediato" xml:space="preserve">
    <value>Se você já efetuou o pagamento do seu boleto clique aqui para desbloqueio imediato</value>
  </data>
  <data name="NaoEhPossivelAlterarParaRecorrenteUmAgendamentoQueFoiPagoAntecipadamente" xml:space="preserve">
    <value>Não é possível realizar agendamento em recorrência para agendamentos que foram pagos antecipadamente</value>
  </data>
  <data name="AcessoEstoquista" xml:space="preserve">
    <value>Acesso ao controle de estoque, inventário, cadastro e pré-venda de produto</value>
  </data>
  <data name="InformacoesSobreInclusaoDeFotosNosServicosParaProfissionais" xml:space="preserve">
    <value>Selecione “Sim” para profissionais que realizam serviços e que vão poder incluir fotos relacionadas aos mesmos.</value>
  </data>
  <data name="ConsultaApenasRelatórioFinanceiroCaixa" xml:space="preserve">
    <value>Consulta Apenas Relatório Financeiro Caixa</value>
  </data>
  <data name="TextoDefinirFotoComoPrincipal" xml:space="preserve">
    <value>Além disso, você pode definir qual será a primeira foto que será exibida na capa para seus  clientes quando eles acessarem seu site selecionando a foto principal.</value>
  </data>
  <data name="InstrucaoDeAtivacaoPagamentoPorLinkDePagamento" xml:space="preserve">
    <value>Para incluir créditos para os clientes utilizando link de pagamento é necessário habilitar em Configurações &gt; Configurações do Sistema &gt; Formas de Pagamento marque a opção "Pagamento Online por Link".</value>
  </data>
  <data name="CpfCnpjInvalido" xml:space="preserve">
      <value>O cpf ou cnpj é inválido</value>
  </data>
</root>