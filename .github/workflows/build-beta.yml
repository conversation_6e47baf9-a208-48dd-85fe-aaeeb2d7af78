name: Atualizar ambiente Beta

on:
  release:
    types:
      - published

concurrency:
      group: prioridade-1
      cancel-in-progress: false

defaults:
  run:
    working-directory: J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master

jobs:
  build-copy:
    runs-on: [self-hosted, windows, x64, build]
    if: github.ref == 'refs/heads/master' || github.event_name == 'release'
    steps:
      - name: Setup .NET Core SDK
        uses: actions/setup-dotnet@v4.0.0
        with:
          source-url: https://nuget.pkg.github.com/trinks-com/index.json
          owner: trinks-com
        env:
          NUGET_AUTH_TOKEN: ${{ secrets.NUGET_PUBLISH_TOKEN }}

      - uses: actions/checkout@v4
        with:
          path: master
          clean: false

      - name: Restore NuGet packages
        run: dotnet restore ${{ vars.SOLUTION_FILE_PATH }}

      - uses: nuget/setup-nuget@v2.0.0
        with:
          nuget-version: "5.x"
      - run: nuget restore ${{ vars.SOLUTION_FILE_PATH }}

      - name: Add MSBuild to PATH
        uses: microsoft/setup-msbuild@v2

      - name: Build
        run: msbuild ${{vars.SOLUTION_FILE_PATH}} /t:Build /m /nologo /p:IncrementalBuild=true /p:RestorePackages=false /p:Configuration=${{vars.BUILD_CONFIGURATION}} /maxcpucount:4 /p:BuildInParallel=true /verbosity:minimal /consoleLoggerParameters:ErrorsOnly

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Copy Files Web
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Perlink.Trinks.Web' 's3://trinks-web-prd' --delete --exact-timestamps --exclude 'web.config' --exclude 'appspec.yml' --exclude '*.cs' --exclude '*.csproj' --exclude 'Api/*' --exclude 'ProApi/*' --exclude 'Services/*' --exclude 'obj/*' --exclude '*.htm' --exclude '*.bat' --exclude '*.txt' --exclude '*.appcache' --exclude 'web.*.config' --only-show-errors
      
      - name: Copy Files Web Bin
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Perlink.Trinks.Web\Bin' 's3://trinks-web-prd/bin' --delete --exact-timestamps --exclude 'web.config' --exclude 'appspec.yml' --exclude '*.cs' --exclude '*.csproj' --exclude 'Api/*' --exclude 'ProApi/*' --exclude 'Services/*' --exclude 'obj/*' --exclude '*.htm' --exclude '*.bat' --exclude '*.txt' --exclude '*.appcache' --exclude 'web.*.config' --only-show-errors
      
      - name: Copy Files Api
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Perlink.Trinks.Api' 's3://trinks-web-prd/Api' --delete --exact-timestamps --exclude 'web.config' --exclude '*.cs' --exclude '*.csproj' --exclude 'obj/*' --exclude 'Properties/*' --exclude 'App_Readme/*' --exclude 'web.*.config' --exclude '*.json' --only-show-errors
      
      - name: Copy Files Api Bin
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Perlink.Trinks.Api\bin' 's3://trinks-web-prd/Api/bin' --delete --exact-timestamps --only-show-errors

      - name: Copy Files ProApi
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Trinks.Pro.Api' 's3://trinks-web-prd/ProApi' --delete --exact-timestamps --exclude 'web.config' --exclude '*.cs' --exclude '*.csproj' --exclude 'obj/*' --exclude 'Properties/*' --exclude 'App_Readme/*' --exclude 'web.*.config' --only-show-errors
      
      - name: Copy Files ProApi Bin
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Trinks.Pro.Api\bin' 's3://trinks-web-prd/ProApi/bin' --delete --exact-timestamps --only-show-errors

      - name: Copy Files Services
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Perlink.Trinks.WebApi' 's3://trinks-web-prd/Services' --delete --exact-timestamps --exclude 'web.config' --exclude '*.cs' --exclude '*.csproj' --exclude 'obj/*' --exclude 'web.*.config' --only-show-errors

      - name: Copy Files Services Bin
        run: |
          & "C:\Program Files\Amazon\AWSCLIV2\aws.exe" s3 sync 'J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master\Perlink.Trinks.WebApi\bin' 's3://trinks-web-prd/Services/bin' --delete --exact-timestamps --only-show-errors

  Packet-Generator:
    if: github.ref == 'refs/heads/master' || github.event_name == 'release'
    needs: build-copy
    runs-on: [self-hosted, windows, x64, hmg02]

    steps:
      - name: Packet Generator
        working-directory: C:\Sites\GeradorDePacote\
        run: C:\Sites\GeradorDePacote\GeradorDePacotes.exe

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4.0.2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Copy Package to S3
        working-directory: C:\Sites\Trinks.Web_master\
        run: |
          aws s3 cp C:\Sites\Trinks.Web_master\ver.xml s3://trinks-web-prd
