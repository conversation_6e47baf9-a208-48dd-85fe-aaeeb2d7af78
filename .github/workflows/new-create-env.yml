name: <PERSON><PERSON>r/Atualizar ambiente HMG

on:
  workflow_dispatch:
    inputs:
      database:
        description: 'Informe o ambiente desejado: TrinksP ou TrinksH.'
        required: true
        default: TrinksH
        type: choice
        options:
          - TrinksH
          - TrinksP

concurrency:
  group: prioridade-1
  cancel-in-progress: false

jobs:

  setup-build:
    if: github.ref != 'refs/heads/master'
    runs-on: [self-hosted, windows, x64, build]
    steps:
      - name: Setup .NET Core SDK
        uses: actions/setup-dotnet@v4
        with:
          source-url: https://nuget.pkg.github.com/trinks-com/index.json
          owner: trinks-com
        env:
          NUGET_AUTH_TOKEN: ${{ secrets.NUGET_PUBLISH_TOKEN }}

      - uses: actions/checkout@v4
        with:
          clean: false

      - name: Setup NuGet packages
        uses: nuget/setup-nuget@v2
        with:
          nuget-version: '5.x'

      - name: Restore NuGet packages
        run: |
          dotnet restore "${{ vars.SOLUTION_FILE_PATH }}" --verbosity quiet
          nuget restore "${{ vars.SOLUTION_FILE_PATH }}"

      - name: Add MSBuild to PATH
        uses: microsoft/setup-msbuild@v2

      - name: Build
        working-directory: ${{ env.GITHUB_WORKSPACE }}
        run: |
          msbuild "${{ vars.SOLUTION_FILE_PATH }}" /t:Build /m /nologo /p:RestorePackages=false /p:IncrementalBuild=true /p:Configuration=${{ vars.BUILD_CONFIGURATION }} /p:OutDir=J:\PublishBuild /maxcpucount:4 /p:BuildInParallel=true /verbosity:minimal /consoleLoggerParameters:ErrorsOnly

  copy-master:
    runs-on: [self-hosted, windows, x64, hmg02]
    if: github.event_name != 'pull_request' || github.ref != 'refs/heads/master'
    outputs:
      FOLDER_EXISTS: ${{ steps.folder.outputs.FOLDER_EXISTS }}

    steps:
    - name: Check if directory exists
      continue-on-error: true
      id: folder
      run: |
        $folder = "C:\Sites\Branches\${{ github.ref_name }}"
        $dir = "C:\Sites\Branches\${{ github.ref_name }}"
        if (Test-Path $dir) {
          Write-Host "Directory exists."
          echo "::set-output name=FOLDER_EXISTS::true"
          exit 0
        } else {
          Write-Host "Directory does not exist."
          robocopy C:\Sites\Branches\master-hmg C:\Sites\Branches\${{ github.ref_name }} /E /MT:4
          echo "::set-output name=FOLDER_EXISTS::false"
          exit 0
        }

  copy:
    needs: [setup-build, copy-master]
    if: github.event_name != 'pull_request' || github.ref != 'refs/heads/master'
    runs-on: [self-hosted, windows, x64, build]
    env:
      DEST_BASE: \\trinkshmg\\Sites\\Branches\\${{ github.ref_name }}

    steps:
      - name: Copy Files Web
        continue-on-error: true
        run: |
          robocopy "${{ vars.FOLDER_PATH_BUILD }}\\Perlink.Trinks.Web" "${{ env.DEST_BASE }}" *.* /MT:4 /S /XA:H /XO /PURGE /NDL /NC /NS /NP
          exit 0
          Remove-Item -Path "${{ vars.FOLDER_PATH_BUILD }}\\Perlink.Trinks.Web" -Recurse -Force

      - name: Copy Files Api
        continue-on-error: true
        run: |
          robocopy "${{ vars.FOLDER_PATH_BUILD }}\\Perlink.Trinks.Api" "${{ env.DEST_BASE }}\\Api" *.* /MT:4 /S /XA:H /XO /PURGE /NDL /NC /NS /NP
          exit 0
          Remove-Item -Path "${{ vars.FOLDER_PATH_BUILD }}\\Perlink.Trinks.Api" -Recurse -Force

      - name: Copy Files ProApi
        continue-on-error: true
        run: |
          robocopy "${{ vars.FOLDER_PATH_BUILD }}\\Trinks.Pro.Api" "${{ env.DEST_BASE }}\\ProApi" *.* /MT:4 /S /XA:H /XO /PURGE /NDL /NC /NS /NP
          exit 0
          Remove-Item -Path "${{ vars.FOLDER_PATH_BUILD }}\\Trinks.Pro.Api" -Recurse -Force

      - name: Copy Files Services
        continue-on-error: true
        run: |
          robocopy "${{ vars.FOLDER_PATH_BUILD }}\\Perlink.Trinks.WebApi" "${{ env.DEST_BASE }}\\Services" *.* /MT:4 /S /XA:H /XO /PURGE /NDL /NC /NS /NP
          exit 0
          Remove-Item -Path "${{ vars.FOLDER_PATH_BUILD }}\\Perlink.Trinks.WebApi" -Recurse -Force

  Create-iis-App:
    needs: [copy-master, copy]
    if: github.event_name != 'pull_request'
    runs-on: [self-hosted, windows, x64, hmg02]

    steps:
      - name: Executar deploy IIS
        shell: powershell
        working-directory: C:\Sites
        env:
          WEBSITE_NAME: ${{ vars.WEBSITE_NAME }}
          BRANCH_NAME: ${{ github.ref_name }}
        run: |
          .\deploy_iis.ps1 -siteName "${{ env.WEBSITE_NAME }}" -branch "${{ env.BRANCH_NAME }}" -baseDir "C:\Sites\Branches"

    
  change-to-trinksp:
    if: github.event.inputs.database == 'TrinksP'
    needs: copy
    runs-on: [self-hosted, windows, x64, hmg02]
    steps:
      - name: Change to TrinksP
        shell: cmd
        run: copy C:\Sites\Branches\master-prdi\web.config C:\Sites\Branches\${{ github.ref_name }}\web.config /Y
