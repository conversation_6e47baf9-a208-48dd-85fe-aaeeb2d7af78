name: Deploy Windows Service

on:
  workflow_dispatch:
    inputs:
      data-copy:
        type: string
        description: Realizar copia de arquivos de quantos dias atrás?
        required: true
concurrency:
  group: prioridade-1
  cancel-in-progress: false

env:
  DESTINATION_DIRECTORY: '\\trinkshmg\Sites\Pacotes\Trinks-Master-Actions-WindowsService'
  SITE_ORIGIN_DIRECTORY: 'J:\BuildWinService'

defaults:
  run:
    working-directory: .\Perlink.Trinks.WindowsService

jobs:
  data:
    if: github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    outputs:
      data: ${{ steps.data.outputs.data }}
    steps:
      - name: Set Current Date
        id: data
        working-directory: ${{env.GITHUB_WORKSPACE}}
        run: |
          echo "::set-output name=data::$(date +'%Y%m%d')"

  build:
    runs-on: [self-hosted, windows, x64, build]
    needs: data
    if: github.ref == 'refs/heads/master'
    steps:
      - name: Setup .NET Core SDK
        uses: actions/setup-dotnet@v1
        with:
          source-url: https://nuget.pkg.github.com/trinks-com/index.json
          owner: trinks-com
        env:
          NUGET_AUTH_TOKEN: ${{ secrets.NUGET_PUBLISH_TOKEN }}

      - uses: actions/checkout@v3
        with:
          clean: false

      - name: Restore NuGet packages
        run: dotnet restore J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\Trinks.sln

      - name: Add MSBuild to PATH
        uses: microsoft/setup-msbuild@v1.0.2
      
      - name: Clean build output
        run: Remove-Item -Path "J:\BuildWinService\*" -Recurse -Force
        shell: powershell

      - name: Build
        run: msbuild Perlink.Trinks.WindowsService.csproj /t:ReBuild /m /nologo /p:RestorePackages=false /p:Configuration=${{vars.BUILD_CONFIGURATION}} /p:OutDir=J:\BuildWinService

      - name: Copy
        run: |
          robocopy ${{env.SITE_ORIGIN_DIRECTORY}} ${{env.DESTINATION_DIRECTORY}}\${{ needs.data.outputs.data }}
          exit 0

      - name: Delete Archives
        shell: cmd
        run: del /f ${{env.DESTINATION_DIRECTORY}}\${{ needs.data.outputs.data }}\Perlink.Trinks.WindowsService.exe.config /S /F
      
      - name: Delete Empy Folder 
        shell: PowerShell
        run: |
          do {
              $emptyFolders = Get-ChildItem -Path "${{env.DESTINATION_DIRECTORY}}\${{ needs.data.outputs.data }}" -Recurse -Directory | Where-Object { (Get-ChildItem $_.FullName -Force).Count -eq 0 }
              if ($emptyFolders) {
                 $emptyFolders | Remove-Item -Force
              }
           } while ($emptyFolders)
  Copy:
    needs: [data,build]
    if: github.ref == 'refs/heads/master'
    runs-on: [self-hosted, windows, x64, hmg02]

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Copy to S3
        working-directory: C:\Sites\Pacotes\Trinks-Master-Actions-WindowsService\
        run: aws s3 cp C:\Sites\Pacotes\Trinks-Master-Actions-WindowsService\${{ needs.data.outputs.data }} s3://windows-service-deploy/${{ needs.data.outputs.data }} --recursive --only-show-errors

  Deploy:
    needs: [Copy,data]
    if: github.ref == 'refs/heads/master'
    runs-on: [self-hosted, windows, x64, SRV07]

    steps:

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Copy S3 to SRV07
        working-directory: D:\PacotesWinService
        run: |
          aws s3 cp s3://windows-service-deploy/${{ needs.data.outputs.data }} D:\PacotesWinService\${{ needs.data.outputs.data }} --recursive --only-show-errors
