name: Atualizar Master HMG

on: 
    workflow_run:
        workflows: [Atualizar ambiente Beta]
        types: [completed]
    workflow_dispatch:

concurrency:
  group: prioridade-1
  cancel-in-progress: false

jobs:

  build-copy:
    if: github.ref == 'refs/heads/master'
    defaults:
      run:
        working-directory: J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master-hmg
    runs-on: [self-hosted, windows, x64, build]
    steps:   

    - name: Setup .NET Core SDK
      uses: actions/setup-dotnet@v4
      with:
        source-url: https://nuget.pkg.github.com/trinks-com/index.json
        owner: trinks-com
      env:
        NUGET_AUTH_TOKEN: ${{ secrets.NUGET_PUBLISH_TOKEN }}
  
    - uses: actions/checkout@v4
      with:
        clean: false
        path: master-hmg
        
    - name: Setup NuGet packages
      uses: nuget/setup-nuget@v2
      with:
        nuget-version: '5.x'

    - name: Restore NuGet packages
      run: |
        dotnet restore ${{ vars.SOLUTION_FILE_PATH }} --verbosity quiet
        nuget restore ${{ vars.SOLUTION_FILE_PATH }}
  
    - name: Add MSBuild to PATH
      uses: microsoft/setup-msbuild@v2
      
    - name: Build
      working-directory: J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master-hmg
      run: msbuild ${{ vars.SOLUTION_FILE_PATH }} /t:ReBuild /m /nologo /p:RestorePackages=false /p:IncrementalBuild=true /p:Configuration=${{ vars.BUILD_CONFIGURATION }} /p:OutDir=J:\PublishBuild-master /maxcpucount:4 /p:BuildInParallel=true /verbosity:minimal /consoleLoggerParameters:ErrorsOnly

    - name: Copy Files Web
      continue-on-error: true
      run: |
        robocopy J:\PublishBuild-master\_PublishedWebsites\Perlink.Trinks.Web \\trinkshmg\Sites\Branches\${{ github.ref_name }}-hmg *.* /MT:4 /MIR
        exit 0
    - name: Copy Files Api
      continue-on-error: true
      run: |
        robocopy J:\PublishBuild-master\_PublishedWebsites\Perlink.Trinks.Api \\trinkshmg\Sites\Branches\${{ github.ref_name }}-hmg\Api *.* /MT:4 /MIR
        exit 0
    - name: Copy Files ProApi
      continue-on-error: true
      run: |
        robocopy J:\PublishBuild-master\_PublishedWebsites\Trinks.Pro.Api \\trinkshmg\Sites\Branches\${{ github.ref_name }}-hmg\ProApi *.* /MT:4 /MIR
        exit 0
    - name: Copy Files Services
      continue-on-error: true
      run: |
        robocopy J:\PublishBuild-master\_PublishedWebsites\Perlink.Trinks.WebApi \\trinkshmg\Sites\Branches\${{ github.ref_name }}-hmg\Services *.* /MT:4 /MIR
        exit 0
  
    - name: Generate Versioning
      run: J:\actions-runner-trinks-web\GerarVersionamentoGit\GerarVersionamentoGit.exe J:\actions-runner-trinks-web\_work\Trinks.Web\Trinks.Web\master-hmg \\trinkshmg\Sites\Branches\master\ver.xml

  update-master-prdi:
      if: github.ref == 'refs/heads/master'
      runs-on: [self-hosted, windows, x64, hmg02]
      needs: build-copy
      steps:
      
      - name: Copy Files HMG to PRDI
        continue-on-error: true
        run: |
          robocopy C:\Sites\Branches\${{ github.ref_name }}-hmg C:\Sites\Branches\${{ github.ref_name }}-prdi *.* /MT:4 /MIR
          exit 0
  
      - name: Convert HMG to PRDI
        run: C:\Sites\ConvertHMGtoPRDI.ps1
          
