> # Instruções
> Olá! Por favor, preencha os campos abaixo conforme necessário. Apague esse bloco quando for submeter o PR para revisão.
> Os PRs no Github suportam a sintaxe de Markdown. Utilize a aba "Preview" acima para visualizar como ficará o PR.
>
> Sempre que possível, adicione links para facilitar a leitura e obtenção de informações sobre o PR.
> Você pode adicionar links ao seu PR dando um título e a referência para onde o link aponta. Exemplo:
> [Clique para visualizar](https://www.youtube.com/watch?v=dQw4w9WgXcQ)
>
> May the force be with you

### 🃏 Quais cards estão associados a essa tarefa? (se possível, utilize links)
Preencha com referências aos cards no Jira. *Utilize links para os cards*

### 📚 Documentação técnica associada
Preencha com a documentação técnica associada a esse PR. A documentação técnica deve ser comporsta por um PR com uma ou mais ADRs no repositório https://github.com/trinks-com/documentacao-tecnica. Lembre-se também de atualizar o Guia da funcionalidade associada quando necessário. Caso não haja decisões técnicas relevantes, preencha essa seção indicando isso e explicando sucintamente porque não há.

### 🔗 Pull Requests Associados
Prencha com referências a outros PRs que devem ser implantados juntamente com esse. Se não houver, apague essa seção. *Utilize links para os PRs*

### ✔️ Checklist Técnico
Preencha as informações necessárias para deploy adicionando um ✔️ onde a resposta for "sim" (você pode copiar e colar o ícone ou usar o markdown `:heavy_check_mark:`)
- Tem deploy do Web? ✔️
- Possui script de banco?
- Tem deploy de Lambda?
- Tem deploy de WindowsService?
- Existe alteração no Web.Config?
- Tem JOB novo no Quartz?
- Tem deploy para lojas (google e apple) do App Pro?
- Tem deploy do no front-end do Link de Pagamento?
- Tem deploy da API do APP novo?
- Tem deploy da API do APP antigo?
- Tem deploy da API B2B?
- Tem deploy do trinks-express?
- Tem deploy do nova-api?
  
---
### 🙏 Tarefas de deploy
Não se esqueça de realizar as tarefas abaixo. Preencha as checkboxes com "x" e preencha os campos abaixo das checkboxes com as informações solicitadas após concluir cada tarefa. *Não apague essa seção*
- [ ] 📚 Preencha a seção "Documentação técnica". Se for necessário, [crie um PR no repositório de documentação técnica.](https://github.com/trinks-com/documentacao-tecnica)
- [ ] 📅 Preencha o campo de previsão de data de finalização da revisão abaixo. No mínimo, considere 50 arquivos por dia. Ex.: se o seu PR tem 160 arquivos, serão necessários no mínimo 4 dias para revisão.
- [ ] 📧 Lembre-se de mandar um e-mail para o endereço [<EMAIL>](mailto:<EMAIL>) referenciando esse PR e as informações acima.
- [ ] 📝 Lembre-se de criar um card no board [Delivery](https://perlink.atlassian.net/jira/core/projects/DEPLOY/board) referenciando esse PR e as informações acima.
- [ ] ✔️ Você deve obter 3 aprovações de PR - 2 tech leads que não sejam do seu squad e 1 sênior (Patrick ou Lira)

Previsão para finalização de revisão do(s) PR(s): DD/MM/YY

Título (subject) do e-mail de deploy:

Link do card no delivery:

