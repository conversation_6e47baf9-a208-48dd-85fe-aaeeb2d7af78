﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Notificacoes.DTO;
using Perlink.Trinks.Notificacoes.Enums;
using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.Notificacoes.Services
{

    public class DispositivoService : BaseService, IDispositivoService
    {
        public void RegistrarOuAtualizarDispositivo(DispositivoDTO dadosDoDispositivo, int? idFranquia)
        {

            ValidarDadosDoDispositivo(dadosDoDispositivo);

            if (!ValidationHelper.Instance.IsValid)
                return;

            Dispositivo dispositivo = Domain.Notificacoes.DispositivoRepository
                .Queryable()
                .FirstOrDefault(x => x.UUID == dadosDoDispositivo.UUID &&
                    x.Plataforma == (TipoPlataformaEnum)dadosDoDispositivo.Plataforma &&
                    x.Aplicativo.IdFranquia == idFranquia);

            if (dispositivo == null)
            {
                dispositivo = new Dispositivo()
                {
                    DataHoraRegistro = Calendario.Agora(),
                    UUID = dadosDoDispositivo.UUID,
                    Plataforma = dadosDoDispositivo.Plataforma,
                    Aplicativo = Domain.TrinksApps.AplicativoDeAgendamentoService.ObterAppDaFranquiaOuAppDoTrinks(idFranquia),
                    Ativo = true
                };
            };

            dispositivo.DataHoraUltimaAtualizacao = Calendario.Agora();
            dispositivo.DeviceToken = Regex.Replace(dadosDoDispositivo.DeviceToken, "\\s+", "");
            dispositivo.VersaoPlataforma = dadosDoDispositivo.VersaoPlataforma;
            dispositivo.Conta = Domain.WebContext.IdContaAutenticada.HasValue ?
                Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value) :
                null;

            dispositivo.VersaoDoAplicativo = dadosDoDispositivo.VersaoApp;

            if (dispositivo.IdDispositivo != 0)
                Domain.Notificacoes.DispositivoRepository.Update(dispositivo);
            else
                Domain.Notificacoes.DispositivoRepository.SaveNew(dispositivo);
        }

        private void ValidarDadosDoDispositivo(DispositivoDTO dadosDoDispositivo)
        {
            if (String.IsNullOrWhiteSpace(dadosDoDispositivo.UUID))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Identificador único do dispositivo não informado.");
                return;
            }

            if ((TipoPlataformaEnum)dadosDoDispositivo.Plataforma == TipoPlataformaEnum.Desconhecido)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Plataforma do dispositivo não indicada.");
                return;
            }

            if (String.IsNullOrWhiteSpace(dadosDoDispositivo.DeviceToken))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Token do dispositivo não informado.");
                return;
            }
        }

        public bool VersaoEhMaiorOuIgual(string versaoDoApp, string versaoMinima)
        {
            int numeroVersaoApp = ObterComponenteChave(versaoDoApp);
            int numeroVersaoMinima = ObterComponenteChave(versaoMinima);

            return numeroVersaoApp > 0 && numeroVersaoMinima > 0 && numeroVersaoApp >= numeroVersaoMinima;
        }

        private int ObterComponenteChave(string versao)
        {
            if (string.IsNullOrEmpty(versao)) return 0;

            var somenteVersao = versao.Split(' ').FirstOrDefault();
            var partes = somenteVersao?.Split('.') ?? new string[0];

            if (partes.Length < 2)
                return 0;

            // Combina os elementos do segundo em diante para formar um número inteiro
            var componenteChave = string.Join("", partes.Skip(1));

            if (int.TryParse(componenteChave, out var numero))
            {
                return numero;
            }

            return 0;
        }
    }
}