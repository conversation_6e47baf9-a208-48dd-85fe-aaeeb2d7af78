﻿using Castle.ActiveRecord;
using Elmah;
using Newtonsoft.Json;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Notificacao.SMSGateway;
using Perlink.Notificacao.SMSGateway.Services;
using Perlink.Shared.Exceptions;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.EnvioMensagem;
using Perlink.Trinks.EnvioMensagem.Services;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Notificacoes.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Pessoas.Services;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Text;

namespace Perlink.Trinks.Notificacoes.Services
{

    public class RegistroNotificacaoService : BaseService, IRegistroNotificacaoService
    {

        public void AtualizarStatusMensagens(List<NotificacaoSMSDTO> dados)
        {
            foreach (var item in dados)
            {
                var n = Domain.Notificacoes.RegistroNotificacaoRepository.Load(item.Id);
                n.StatusEnvio = item.StatusMensagemGateway.Left(50);
            }
            Domain.Notificacoes.RegistroNotificacaoRepository.Flush();
        }

        public void AtualizarStatusMensagens()
        {
            string retorno;
            var urlBase = ConfiguracoesTrinksWrapper.Instancia.UrlBase();

            var urlStatusPendentesDeAtualizacao = urlBase + "/WebService/Notificacoes/MensagensPendentesAtualizacaoDeStatus";

            LogService<RegistroNotificacaoService>.Info("============ Obtendo mensagens pela URL " + urlStatusPendentesDeAtualizacao + " ================");
            using (var client = new WebClient())
            {
                ServicePointManager.Expect100Continue = false;
                client.Encoding = Encoding.UTF8;
                retorno = client.DownloadString(urlStatusPendentesDeAtualizacao);
            }
            var envios = JsonConvert.DeserializeObject<List<NotificacaoSMSDTO>>(retorno);

            LogService<RegistroNotificacaoService>.Info("============ " + envios.Count + " SMS pendentes de atualização de status ================");

            if (!envios.Any())
            {
                return;
            }

            try
            {
                foreach (var envio in envios)
                {
                    var status = new NotificacaoSMSGatewayService().ObterStatusNotificacao(new NotificacaoSMSGateway
                    {
                        IdMensagem = envio.IdMensagem,
                        SMSGatewayUrlSendMessage = envio.UrlServicoSMS + "/request-status-update",
                    });
                    envio.StatusMensagemGateway = status;
                }
                var urlAtualizarStatusMensagens = urlBase + "/WebService/Notificacoes/AtualizarStatusMensagens";
                using (var client = new WebClient())
                {
                    ServicePointManager.Expect100Continue = false;
                    client.Encoding = Encoding.UTF8;
                    string json = JsonConvert.SerializeObject(envios);
                    client.UploadValues(urlAtualizarStatusMensagens, new NameValueCollection { { "Dados", json } });
                }
            }
            catch (Exception e)
            {
                LogService<RegistroNotificacaoService>.Error(e.ToString());
            }
        }

        public int EnviarNotificacoesParaDisparo(int idEstabelecimento = 0)
        {
            var disparos = 0;

            var mensagens = new List<MensagemSMS>();
            var idsClienteEstabelecimentoQueJaReceberamNotificacaoPush = new List<int>();
            var clientesEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterPendentesDeNotificacao(idEstabelecimento: idEstabelecimento);
            LogService<RegistroNotificacaoService>.Debug(clientesEstabelecimento.Count() + " clientes para notificar");

            var notificacoes = GerarNotificaoesHorarioDTO(clientesEstabelecimento);

            if (notificacoes.Any())
            {
                disparos = notificacoes.Count;
                LogService<RegistroNotificacaoService>.Info(disparos + " lembretes SMS para enviar");

                var qtdHoraLimiteParaEnvio = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.qtd_horas_limite_para_envio_sms_lembrete);

                var dataDeEmissao = Calendario.Agora();

                IList<int> idsEstabelecimentos = notificacoes.Select(n => n.IdEstabelecimento).Distinct().ToList();

                IList<SaldoDeSMSLembreteDoEstabelecimento> saldosDeLimitacaoSMSLembrete
                    = Domain.Pessoas.SaldoDeSMSLembreteDoEstabelecimentoRepository.ListarPorEstabelecimentos(idsEstabelecimentos);

                var notificacoesGroup = notificacoes.ChunkBy(10);

                var scope = new TransactionScope(TransactionMode.New);
                try
                {
                    using (scope)
                    {
                        foreach (var ns in notificacoesGroup)
                        {
                            foreach (var notificacao in ns)
                            {
                                ClienteEstabelecimento clienteEstabelecimento = null;

                                foreach (var horarioId in notificacao.IdHorarios)
                                {
                                    var horario = Domain.Pessoas.HorarioRepository.Load(horarioId);
                                    horario.DataDeEmissaoFilaParaEnvio = dataDeEmissao;

                                    Domain.Pessoas.HorarioRepository.UpdateNoFlush(horario);

                                    if (clienteEstabelecimento == null)
                                        clienteEstabelecimento = horario.ClienteEstabelecimento;
                                }

                                try
                                {

                                    bool enviaPush = clienteEstabelecimento.Cliente.TipoCliente == TipoClienteEnum.Web
                                        && clienteEstabelecimento.Cliente.PessoaFisica.PrimeiraConta != null;
                                    bool notificacaoPushFoiEnviada = false;

                                    if (enviaPush)
                                    {
                                        var idContaCliente = clienteEstabelecimento.Cliente.PessoaFisica.PrimeiraConta.IdConta;
                                        var dispositivo = Domain.Notificacoes.DispositivoRepository.Queryable().FirstOrDefault(p => p.Conta.IdConta == idContaCliente);

                                        enviaPush = dispositivo != null;
                                    }

                                    if (enviaPush)
                                    {
                                        if (!idsClienteEstabelecimentoQueJaReceberamNotificacaoPush.Any(p => p == clienteEstabelecimento.Codigo))
                                        {
                                            var notificacaoPush = Domain.Notificacoes.NotificacaoPushService.ProgramarEnvioPushParaLembreteDeAgendamento(notificacao.IdHorarios, clienteEstabelecimento);
                                            notificacaoPushFoiEnviada = notificacaoPush != null;

                                            if (notificacaoPushFoiEnviada)
                                            {
                                                idsClienteEstabelecimentoQueJaReceberamNotificacaoPush.Add(clienteEstabelecimento.Codigo);
                                                var registro = new RegistroNotificacao
                                                {
                                                    ClienteEstabelecimento = clienteEstabelecimento,
                                                    Conteudo = notificacaoPush.TextoNotificacao,
                                                    //ServicoSMS = servicoSMS,
                                                    TelefoneDDD = notificacao.TelefoneDDD,
                                                    TelefoneNumero = notificacao.TelefoneNumero,
                                                    TipoNotificacao = TipoNotificacaoEnum.PUSH,
                                                    DataEnvio = Calendario.Agora(),
                                                    StatusEnvio = "Em progresso"
                                                };
                                                Domain.Notificacoes.RegistroNotificacaoRepository.SaveNewNoFlush(registro);
                                            }
                                            else
                                            {
                                                LogService<RegistroNotificacaoService>.Error(String.Format("O Push de lembrete não foi enviado para o ClienteEstabelecimento com Id: {0}", clienteEstabelecimento.Codigo));
                                            }
                                        }
                                    }
                                    else if (notificacao.PermiteEnvioViaSMS && EstabelecimentoTemSaldoParaEnviarSMSLembrete(notificacao.IdEstabelecimento, saldosDeLimitacaoSMSLembrete, notificacao.StatusContaFinanceira))
                                    {

                                        var servicoSMS = Domain.Notificacoes.ServicoSMSRepository.Load(notificacao.IdServicoSMS);
                                        var registro = new RegistroNotificacao
                                        {
                                            ClienteEstabelecimento = clienteEstabelecimento,
                                            Conteudo = notificacao.Conteudo,
                                            ServicoSMS = servicoSMS,
                                            TelefoneDDD = notificacao.TelefoneDDD,
                                            TelefoneNumero = notificacao.TelefoneNumero,
                                            TipoNotificacao = TipoNotificacaoEnum.SMS,
                                            DataEnvio = Calendario.Agora(),
                                            StatusEnvio = "Em progresso"
                                        };

                                        Domain.Notificacoes.RegistroNotificacaoRepository.SaveNewNoFlush(registro);

                                        var mensagem = new MensagemSMS
                                        {
                                            Conteudo = notificacao.Conteudo,
                                            Origem = EnvioMensagem.Enums.OrigemMensagemEnum.LembreteDeAgendamento,
                                            Telefone = Int32.Parse(notificacao.TelefoneNumero.SomenteNumeros()),
                                            Id = registro.Id.ToString(),
                                            DDD = Int32.Parse(notificacao.TelefoneDDD.SomenteNumeros()),
                                            GatewayUrl = notificacao.UrlServicoSMS,
                                            DataLimite = Calendario.Agora().AddHours(Double.Parse(qtdHoraLimiteParaEnvio.Valor)),
                                            DiscriminadorDeEnvio = notificacao.IdEstabelecimento.ToString()
                                        };

                                        mensagens.Add(mensagem);
                                        ContabilizarEnvioDeSMSLembreteSeNecessario(notificacao.IdEstabelecimento, saldosDeLimitacaoSMSLembrete, notificacao.StatusContaFinanceira);
                                    }

                                }
                                catch (Exception e)
                                {
                                    LogService<RegistroNotificacaoService>.Error("Cliente: " + clienteEstabelecimento.Codigo + " - " + e.Formatada());
                                }
                            }

                            Domain.Pessoas.HorarioRepository.Flush();
                            scope.VoteCommit();
                        }
                    }

                    Domain.Pessoas.EstabelecimentoRepository.Clear();
                }
                catch (Exception ex)
                {
                    scope.VoteRollBack();
                    throw new Exception(ex.Message, ex.InnerException);
                }

                if (mensagens.Any())
                {
                    var mq = new MensagensSMSServiceMQSQS();
                    if (mq != null)
                        mq.SaveNewMessage(mensagens);
                }
                Domain.Pessoas.HorarioRepository.Flush();
            }

            return disparos;
        }

        private List<NotificacaoHorarioDTO> GerarNotificaoesHorarioDTO(List<ClienteEstabelecimento> clientesEstabelecimento)
        {
            int[] statusQuePermitemEnvio = new int[] {
                (int)StatusContaFinanceira.Adimplente,
                (int)StatusContaFinanceira.CobrancaManual,
                (int)StatusContaFinanceira.InadimplenteEmTolerancia,
                (int)StatusContaFinanceira.PeriodoGratis
            };

            List<int> idsPessoasDosEstabelecimentosComLembretePremium = ObterIdsPessoasDosEstabelecimentosComLembretePremium(statusQuePermitemEnvio);

            var notificacoes = new List<NotificacaoHorarioDTO>();

            var contasFinanceiras = Domain.Cobranca.ContaFinanceiraRepository.Queryable(true).Where(f => f.Ativo && !f.DataCancelamento.HasValue);

            var agrupadosPorEstabelecimento = clientesEstabelecimento.GroupBy(f => f.Estabelecimento);
            foreach (var e in agrupadosPorEstabelecimento)
            {
                var statusConta = contasFinanceiras.Where(f => f.Pessoa == e.Key.PessoaJuridica).Select(f => f.Status).FirstOrDefault();
                foreach (var ce in e)
                {
                    if (statusConta != null && statusQuePermitemEnvio.Contains(statusConta.IdStatus))
                    {
                        var notificacoesCliente = NotificacaoHorarioDTO.BuildList(ce, statusConta, idsPessoasDosEstabelecimentosComLembretePremium);
                        notificacoes.AddRange(notificacoesCliente);
                    }
                }
            }

            return notificacoes;
        }

        private List<int> ObterIdsPessoasDosEstabelecimentosComLembretePremium(int[] statusQuePermitemEnvio)
        {

            var ids = Domain.Cobranca.AdicionalNaAssinaturaRepository.Queryable()
                .Where(aa => aa.Ativo && aa.Assinatura.Ativo && aa.Servico.IdServico == (int)ServicoAdicionalEnum.LembretePremium && statusQuePermitemEnvio.Contains(aa.Assinatura.ContaFinanceira.Status.IdStatus))
                .Select(aa => aa.Assinatura.ContaFinanceira.Pessoa.IdPessoa).ToList();

            return ids;
        }

        private bool EstabelecimentoTemSaldoParaEnviarSMSLembrete(int idEstabelecimento, IList<SaldoDeSMSLembreteDoEstabelecimento> saldosDeLimitacaoSMSLembrete, StatusContaFinanceira statusContaFinanceira)
        {
            bool podeEnviar = false;

            var saldoLimitador = saldosDeLimitacaoSMSLembrete.FirstOrDefault(s => s.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (saldoLimitador == null)
            {
                podeEnviar = true;
            }
            else
            {
                saldoLimitador.RenovarQuantidadeDeEnviosSeNecessario(statusContaFinanceira);
                bool possuiSaldo = saldoLimitador.ObterSaldoAtual(statusContaFinanceira) > 0;
                podeEnviar = possuiSaldo;
            }

            return podeEnviar;
        }

        private void ContabilizarEnvioDeSMSLembreteSeNecessario(int idEstabelecimento, IList<SaldoDeSMSLembreteDoEstabelecimento> saldosDeLimitacaoSMSLembrete, StatusContaFinanceira statusContaFinanceira)
        {
            var saldoLimitador = saldosDeLimitacaoSMSLembrete.FirstOrDefault(s => s.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            if (saldoLimitador != null)
            {
                saldoLimitador.QuantidadeDeEnviadosNoPeriodo += 1;
                Domain.Pessoas.SaldoDeSMSLembreteDoEstabelecimentoRepository.UpdateNoFlush(saldoLimitador);
            }
        }

        public string ObterStatusNotificacao(RegistroNotificacao registro)
        {
            String status = String.Empty;

            try
            {
                status = new NotificacaoSMSGatewayService().ObterStatusNotificacao(new NotificacaoSMSGateway
                {
                    IdMensagem = registro.IdMensagem,
                    SMSGatewayUrlSendMessage = registro.ServicoSMS != null ? registro.ServicoSMS.Url + "/request-status-update" : "",
                });
            }
            catch (Exception e)
            {
                if (ErrorSignal.FromCurrentContext() != null)
                    ErrorSignal.FromCurrentContext().Raise(new Exception("Erro ao tentar obter status do SMS", e));
                LogService<RegistroNotificacaoService>.Error(e.ToString());
            }

            return status;
        }

        public void RegistrarIdMensagem(int idRegistroNotificacao, string idMensagem)
        {
            var registro = Domain.Notificacoes.RegistroNotificacaoRepository.Load(idRegistroNotificacao);
            registro.IdMensagem = idMensagem;
        }

        [TransactionInitRequired]
        public void RegistrarIdMensagens(IEnumerable<RetornoMensagem> mensagensLembrete)
        {
            foreach (var item in mensagensLembrete)
            {
                RegistrarIdMensagem(int.Parse(item.IdMensagem), item.IdEnvio);
            }
            Domain.Notificacoes.RegistroNotificacaoRepository.Flush();
        }

        [TransactionInitRequired]
        public void RegistrarNotificacao(RegistroNotificacao registro, List<int> idsHorarios)
        {
            Domain.Notificacoes.RegistroNotificacaoRepository.SaveNew(registro);

            foreach (var id in idsHorarios)
            {
                var horario = Domain.Pessoas.HorarioRepository.Load(id);
                horario.DataHoraNotificacaoClienteEnviada = registro.DataEnvio;
                Domain.Pessoas.HorarioRepository.Update(horario);
            }
        }

        public class NotificacaoHorarioDTO
        {
            public string Conteudo { get; set; }
            public int Id { get; set; }
            public int IdClienteEstabelecimento { get; set; }
            public List<int> IdHorarios { get; set; }
            public string IdMensagem { get; set; }
            public int IdServicoSMS { get; set; }
            public string TelefoneDDD { get; set; }
            public string TelefoneNumero { get; set; }
            public TipoNotificacaoEnum TipoNotificacao { get; set; }
            public string UrlServicoSMS { get; set; }
            public int IdEstabelecimento { get; set; }
            public StatusContaFinanceira StatusContaFinanceira { get; set; }
            public bool PermiteEnvioViaSMS { get; set; }

            public static List<NotificacaoHorarioDTO> BuildList(ClienteEstabelecimento clienteEstabelecimento, StatusContaFinanceira statusContaFinanceira, List<int> idsPessoasDosEstabelecimentosComLembretePremium)
            {
                var retorno = new List<NotificacaoHorarioDTO>();

                var conteudoSMSDoDia = String.Empty;
                var conteudoSMSFuturos = String.Empty;
                IEnumerable<Horario> horariosDoDia = new List<Horario>();
                IEnumerable<Horario> horariosFuturos = new List<Horario>();

                var temLembretePremiumContratado = idsPessoasDosEstabelecimentosComLembretePremium.Contains(clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa);
                var permiteEnvioViaSMS = temLembretePremiumContratado || clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.PermiteEnvioDeLembreteSmsGratuito;

                var antecedenciaNotificacao = clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.AntecedenciaDeNotificacaoEmMinutos;
                if (antecedenciaNotificacao == 1440)
                {
                    horariosFuturos = Domain.Pessoas.HorarioRepository.ObterNotificacoesPendentesDoDiaSeguinte(clienteEstabelecimento);
                    if (horariosFuturos != null && horariosFuturos.Count() > 0)
                    {
                        conteudoSMSFuturos = ObterConteudoSMSFuturo(clienteEstabelecimento, temLembretePremiumContratado, horariosFuturos);
                    }
                }

                horariosDoDia = Domain.Pessoas.HorarioRepository.ObterNotificacoesPendentesDeHoje(clienteEstabelecimento);
                if (horariosDoDia != null && horariosDoDia.Count() > 0)
                {
                    conteudoSMSDoDia = ObterConteudoSMSDoDia(clienteEstabelecimento, temLembretePremiumContratado, horariosDoDia);
                }
                //conteudoSMSFuturos = clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.TemplateLembreteSelecionado.Valor;
                //conteudoSMSDoDia = ObterConteudoTemplateDoTipoSMSRandomizado(clienteEstabelecimento, horariosDoDia, TipoTemplateEnum.SMS_Lembrete_De_Agendamentos);

                var telefones =
                    Domain.Pessoas.TelefoneRepository.ObterTelefonesCelularesDaPessoa(clienteEstabelecimento).Distinct();

                //LogService<RegistroNotificacaoService>.Debug("Cliente " + clienteEstabelecimento.Codigo + ": " + telefones.Count() + " telefones - IdHorarios: " + string.Join(", ", horariosDoDia.Select(f => f.Codigo)));

                foreach (Telefone n in telefones)
                {
                    ServicoSMS servico = null;
                    String urlServicoSMS = ConfiguracoesTrinks.SMSGateway.HttpRequestSendMessage;
                    //if (n.Operadora != null) {
                    //    bool utilizandoServicoComGarantiaDeEntrega;
                    //    servico = ObterServicoSMSParaDisparoDeLembrete(clienteEstabelecimento, n, idsPessoasDosEstabelecimentosComLembretePremium, out utilizandoServicoComGarantiaDeEntrega);

                    //    urlServicoSMS = servico.Url;

                    //    if (ConfiguracoesTrinks.Geral.SinalizaSeLembreteEhShortOuLongNaMensagem) {
                    //        conteudoSMSDoDia = (utilizandoServicoComGarantiaDeEntrega ? "." : ",") + conteudoSMSDoDia;
                    //        conteudoSMSFuturos = (utilizandoServicoComGarantiaDeEntrega ? "." : ",") + conteudoSMSFuturos;
                    //    }

                    //    PreencherNotificacoesDoDia(clienteEstabelecimento, retorno, conteudoSMSDoDia, horariosDoDia, n, servico, urlServicoSMS, statusContaFinanceira, permiteEnvioViaSMS);
                    //    PreencherNotificacoesFuturas(clienteEstabelecimento, retorno, conteudoSMSFuturos, horariosFuturos, n, servico, urlServicoSMS, statusContaFinanceira, permiteEnvioViaSMS);
                    //}
                    //else {
                    //    servico = Domain.Notificacoes.ServicoSMSRepository.Queryable().FirstOrDefault(p => p.EhPadrao);
                    //    urlServicoSMS = servico.Url;
                    //}

                    if(n.Ddi != DdiConstants.Brasil || n.DDD.Length != 2 || (n.Numero.Length != 8 && n.Numero.Length != 9))
                    {
                        continue;
                    }

                    if (temLembretePremiumContratado)
                    {
                        servico = Domain.Notificacoes.NotificacaoDoTrinksService.ObterServicoSMSParaLembreteComGarantiaDeEntrega(n.Operadora);
                    }
                    else
                    {
                        if (n.Operadora != null && n.Operadora.Id > 0)
                        {
                            servico = Domain.Notificacoes.OperadoraServicoSMSRepository.ObterPorOperadora(n.Operadora.Id).ServicoSMS;
                        }
                        else
                        {
                            servico = Domain.Notificacoes.ServicoSMSRepository.Queryable().FirstOrDefault(p => p.EhPadrao);
                        }
                    }

                    urlServicoSMS = servico.Url;

                    if (ConfiguracoesTrinks.Geral.SinalizaSeLembreteEhShortOuLongNaMensagem)
                    {
                        conteudoSMSDoDia = (temLembretePremiumContratado ? "." : ",") + conteudoSMSDoDia;
                        conteudoSMSFuturos = (temLembretePremiumContratado ? "." : ",") + conteudoSMSFuturos;
                    }

                    PreencherNotificacoesDoDia(clienteEstabelecimento, retorno, conteudoSMSDoDia, horariosDoDia, n, servico, urlServicoSMS, statusContaFinanceira, permiteEnvioViaSMS);
                    PreencherNotificacoesFuturas(clienteEstabelecimento, retorno, conteudoSMSFuturos, horariosFuturos, n, servico, urlServicoSMS, statusContaFinanceira, permiteEnvioViaSMS);
                }

                return retorno;
            }

            private static string ObterConteudoSMSDoDia(ClienteEstabelecimento clienteEstabelecimento, bool temLembretePremiumContratado, IEnumerable<Horario> horariosDoDia)
            {
                string conteudoSMSDoDia;
                if (temLembretePremiumContratado)
                {
                    if (clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.TemplateLembreteSelecionado != null)
                        conteudoSMSDoDia = PreencherDadosTemplateSMS(clienteEstabelecimento, clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.TemplateLembreteSelecionado, horariosDoDia);
                    else
                        conteudoSMSDoDia = ObterConteudoTemplateDoTipoSMSDefault(clienteEstabelecimento, horariosDoDia);
                }
                else
                    conteudoSMSDoDia = ObterConteudoTemplateDoTipoSMSRandomizado(clienteEstabelecimento, horariosDoDia, TipoTemplateEnum.SMS_Lembrete_De_Agendamentos);
                return conteudoSMSDoDia;
            }

            private static string ObterConteudoSMSFuturo(ClienteEstabelecimento clienteEstabelecimento, bool temLembretePremiumContratado, IEnumerable<Horario> horariosFuturos)
            {
                string conteudoSMSFuturos;
                if (temLembretePremiumContratado)
                {
                    if (clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.TemplateLembreteSelecionado != null)
                        conteudoSMSFuturos = PreencherDadosTemplateSMS(clienteEstabelecimento, clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.TemplateLembreteSelecionado, horariosFuturos);
                    else
                        conteudoSMSFuturos = ObterConteudoTemplateDoTipoSMSDefault(clienteEstabelecimento, horariosFuturos);
                }
                else
                    conteudoSMSFuturos = ObterConteudoTemplateDoTipoSMSRandomizado(clienteEstabelecimento, horariosFuturos, TipoTemplateEnum.SMS_Lembrete_De_Agendamentos_Futuro);
                return conteudoSMSFuturos;
            }

            private static ServicoSMS ObterServicoSMSParaDisparoDeLembrete(ClienteEstabelecimento clienteEstabelecimento, Telefone celular, List<int> idsPessoasDosEstabelecimentosComLembretePremium, out bool utilizandoServicoComGarantiaDeEntrega)
            {
                ServicoSMS servico;

                utilizandoServicoComGarantiaDeEntrega = false;
                if (idsPessoasDosEstabelecimentosComLembretePremium.Contains(clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa))
                {
                    servico = Domain.Notificacoes.NotificacaoDoTrinksService.ObterServicoSMSParaLembreteComGarantiaDeEntrega(celular.Operadora);
                    utilizandoServicoComGarantiaDeEntrega = true;
                }
                else
                    servico = Domain.Notificacoes.OperadoraServicoSMSRepository.ObterPorOperadora(celular.Operadora.Id).ServicoSMS;

                return servico;
            }

            private static void PreencherNotificacoesFuturas(ClienteEstabelecimento clienteEstabelecimento, List<NotificacaoHorarioDTO> retorno, string conteudoSMSFuturos, IEnumerable<Horario> horariosFuturos, Telefone n, ServicoSMS servico, String urlServicoSMS, StatusContaFinanceira statusContaFinanceira, bool permiteEnvioViaSMS)
            {
                if (horariosFuturos.Any())
                {
                    retorno.Add(new NotificacaoHorarioDTO
                    {
                        IdHorarios = horariosFuturos.Select(f => f.Codigo.Value).ToList(),
                        IdClienteEstabelecimento = clienteEstabelecimento.Codigo,
                        Conteudo = conteudoSMSFuturos,
                        TelefoneDDD = n.DDD,
                        TelefoneNumero = n.Numero,
                        IdServicoSMS = servico != null ? servico.Id : 0,
                        UrlServicoSMS = urlServicoSMS,
                        TipoNotificacao = TipoNotificacaoEnum.SMS,
                        IdEstabelecimento = horariosFuturos.FirstOrDefault().Estabelecimento.IdEstabelecimento,
                        StatusContaFinanceira = statusContaFinanceira,
                        PermiteEnvioViaSMS = permiteEnvioViaSMS
                    });
                }
            }

            private static void PreencherNotificacoesDoDia(ClienteEstabelecimento clienteEstabelecimento, List<NotificacaoHorarioDTO> retorno, string conteudoSMSDoDia, IEnumerable<Horario> horariosDoDia, Telefone telefone, ServicoSMS servico, String urlServicoSMS, StatusContaFinanceira statusContaFinanceira, bool permiteEnvioViaSMS)
            {
                if (clienteEstabelecimento == null || horariosDoDia == null || !horariosDoDia.Any() || telefone == null)
                    return;

                retorno.Add(new NotificacaoHorarioDTO
                {
                    IdHorarios = horariosDoDia.Where(f => f.Codigo.HasValue).Select(f => f.Codigo.Value).ToList(),
                    IdClienteEstabelecimento = clienteEstabelecimento.Codigo,
                    Conteudo = conteudoSMSDoDia,
                    TelefoneDDD = telefone.DDD,
                    TelefoneNumero = telefone.Numero,
                    IdServicoSMS = servico != null ? servico.Id : 0,
                    UrlServicoSMS = urlServicoSMS,
                    TipoNotificacao = TipoNotificacaoEnum.SMS,
                    IdEstabelecimento = horariosDoDia.FirstOrDefault().Estabelecimento.IdEstabelecimento,
                    StatusContaFinanceira = statusContaFinanceira,
                    PermiteEnvioViaSMS = permiteEnvioViaSMS
                });
            }

            private static String ObterConteudoTemplateDoTipoSMSDefault(ClienteEstabelecimento clienteEstabelecimento, IEnumerable<Horario> horarios)
            {
                //bool exibirNomeProfissional = clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
                //TODO: Caso exista a necessidade de um SMS com o nome do profissional e a variável acima for false, o template não pode ter essa informação.
                var template = Domain.Pessoas.TemplateRepository.ObterListaDeTemplatesPorTipo(TipoTemplateEnum.SMS_Lembrete_De_Agendamentos_Premium).First();

                return PreencherDadosTemplateSMS(clienteEstabelecimento, template, horarios);
            }

            private static String ObterConteudoTemplateDoTipoSMSSelecionado(ClienteEstabelecimento clienteEstabelecimento, IEnumerable<Horario> horarios, int idTemplate)
            {
                //bool exibirNomeProfissional = clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
                //TODO: Caso exista a necessidade de um SMS com o nome do profissional e a variável acima for false, o template não pode ter essa informação.
                var template = Domain.Pessoas.TemplateRepository.ObterTemplatePorId(idTemplate);

                return PreencherDadosTemplateSMS(clienteEstabelecimento, template, horarios);
            }

            private static String ObterConteudoTemplateDoTipoSMSRandomizado(ClienteEstabelecimento clienteEstabelecimento, IEnumerable<Horario> horarios, TipoTemplateEnum tipoTemplate)
            {
                //bool exibirNomeProfissional = clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
                //TODO: Caso exista a necessidade de um SMS com o nome do profissional e a variável acima for false, o template não pode ter essa informação.
                var template = Domain.Pessoas.TemplateRepository.ObterTemplateDoTipoSmsRandomizado(tipoTemplate);

                return PreencherDadosTemplateSMS(clienteEstabelecimento, template, horarios);
            }

            private static string PreencherDadosTemplateSMS(ClienteEstabelecimento clienteEstabelecimento, Template template, IEnumerable<Horario> conteudoHorarios)
            {
                String conteudo = template.Valor;

                Horario agendamento = conteudoHorarios.FirstOrDefault();

                if (agendamento == null) return null;

                conteudo = PreencherDadosTemplateSMS(conteudo, agendamento.DataInicio.ObterHoraFormatada(), clienteEstabelecimento.Estabelecimento,
                    Domain.Agenda.MeusAgendamentosService.CriarLinkComCriptografia(agendamento, Encurtador.Enums.TipoEncurtadorDeDadosEnum.IdHorarioParaSmsLembrete), clienteEstabelecimento.Cliente.PessoaFisica.PrimeiroNome());

                return conteudo;
            }

            public static string PreencherDadosTemplateSMS(string template, string primeiroHorario, Estabelecimento estabelecimento, string linkIdAgendamentoCriptografado, string nomeCliente)
            {
                String conteudo = template;

                //Replace em variaveis do template
                conteudo = conteudo.Replace("{primeiro_horario}", primeiroHorario);
                conteudo = conteudo.Replace("{nome_estabelecimento}", estabelecimento.ToString());
                conteudo = conteudo.Replace("{Link_IdAgendamento_Criptografado}", linkIdAgendamentoCriptografado);
                conteudo = conteudo.Replace("{nome_cliente}", nomeCliente);

                conteudo = conteudo.Replace("//", "/");
                conteudo = conteudo.Replace("https:/", "https://");

                conteudo = conteudo.RemoverAcentos();
                return conteudo;
            }
        }
    }
}