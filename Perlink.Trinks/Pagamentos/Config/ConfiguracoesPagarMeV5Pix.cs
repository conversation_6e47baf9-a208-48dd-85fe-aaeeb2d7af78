﻿using Perlink.Pagamentos.Gateways.Config;
using Perlink.Trinks.Pagamentos.Providers;
using System;

namespace Perlink.Trinks.Pagamentos.Config
{
    public class ConfiguracoesPagarMeV5Pix : IConfiguracoesGateway
    {
        public IConfigurationProvider _configuracao => new PagamentoOnlineConfigurationProvider();
        public virtual int IdRecebedorTrinks => _configuracao.GetConfiguration<int>("pgtoOnline_pagarmev5_recebedor_trinks");
        public decimal PercentualTrinks => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarmev5_percentual_trinks_pix");
        public decimal PercentualTrinksPrimeiroPagamento => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarme_percentual_trinks_primeiro_pagamento_default");
        public decimal ValorFixoTrinks => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarme_valor_fixo_trinks_default");
        public decimal ValorFixoTrinksPrimeiroPagamento => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarme_valor_fixo_trinks_primeiro_pagamento_default");
        public decimal ValorFixoOperadora => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarmev5_valor_fixo_operadora_pix");
        public decimal ValorPorTransferencia => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarme_valor_fixo_por_transferencia_default");
        public int DiasParaReceber => _configuracao.GetConfiguration<int>("pgtoOnline_pagarmev5_dias_para_receber_pix");
        public bool RequerUploadDeDocumentos => false;

        public decimal ObterPercentualOperadoraDeAcordoComParcelas(int numeroParcelas)
            => _configuracao.GetConfiguration<decimal>("pgtoOnline_pagarmev5_percentual_operadora_pix");
    }
}