﻿using Castle.ActiveRecord;
using Perlink.Pagamentos.Gateways.Enums;
using System;

namespace Perlink.Trinks.Pagamentos
{
    [ActiveRecord("Gateway", Schema = "Pagamentos", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class Gateway : ActiveRecordBase<Gateway>
    {
        [PrimaryKey(PrimaryKeyType.Native, "id_gateway", ColumnType = "Int32")]
        public virtual int IdGateway { get; set; }

        [Property("nome", NotNull = true)]
        public virtual string Nome { get; set; }

        public virtual GatewayEnum AsEnum() => (GatewayEnum)IdGateway;
    }
}
