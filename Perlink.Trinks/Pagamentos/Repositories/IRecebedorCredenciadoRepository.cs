﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.Pagamentos.Repositories
{
    public partial interface IRecebedorCredenciadoRepository
    {
        RecebedorCredenciado ObterRecebedorCredenciadoNoGateway(int idRecebedor);
        RecebedorCredenciado ObterRecebedorCredenciadoNoGateway(int idRecebedor, GatewayEnum gateway);
        RecebedorCredenciado ObterPorIdRecebedorNoGateway(string idRecebedorNoGateway, GatewayEnum gateway);
        List<RecebedorCredenciado> ObterRecebedoresCredenciadoNoGateway(IEnumerable<int> idsDosRecebedores);
        List<RecebedorCredenciado> ObterRecebedoresCredenciadoNoGateway(IEnumerable<int> idsDosRecebedores, GatewayEnum gateway);
        bool VerificarSeExisteRecebedorNoGateway(IEnumerable<int> idsDosRecebedores, GatewayEnum gateway);
        bool VerificarSeGatewayDoRecebedorEstahAtivo(int idRecebedorCredenciado);
        string ObterIdRecebedorNoGatewayPorIdRecebedor(int idRecebedor);
        StatusKycEGatewayDTO ObterStatusKycERecebedorPorIdEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway);
        int ObterIdGatewayDoIdRecebedorCredenciado(int idRecebedorCredenciado);
    }
}
