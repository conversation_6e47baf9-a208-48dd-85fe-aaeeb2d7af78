﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pagamentos.Repositories
{
    public partial class PagamentoRepository : IPagamentoRepository
    {

        public int ObterQuantidadeDeParcelasDoPagamento(int idPagamento)
        {
            var quantidadeParcelas = Queryable().Where(p => p.IdPagamento == idPagamento).Select(p => p.QuantidadeParcelas).FirstOrDefault();

            return quantidadeParcelas;
        }

        public IQueryable<DadosDosRecebiveisDTO> ListarDadosDosRecebiveisDtos(List<string> idsTransacoesNoGateway)
        {
            var pagamentos = Queryable();
            var pagamentoItem = Domain.Pagamentos.ItemPagamentoRepository.Queryable();
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Queryable();
            var transacaoFormaPagamento = Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable();
            var transacao = Domain.Financeiro.TransacaoRepository.Queryable();
            var compradorGatewayCartao = Domain.Pagamentos.CartaoDeCompradorGatewayRepository.Queryable();
            var transacaoItem = Domain.Financeiro.TransacaoItemRepository.Queryable();

            var retorno = (from pgts in pagamentos
                           join pi in pagamentoItem on pgts.IdPagamento equals pi.Pagamento.IdPagamento
                           join pgont in pagamentoOnlineNoTrinks on pgts.IdPagamento equals pgont.IdPagamentoOnline
                           join tr in transacao on pgont.Transacao.Id equals tr.Id
                           join tfp in transacaoFormaPagamento on tr.Id equals tfp.Transacao.Id
                           join cgc in compradorGatewayCartao on pgts.IdCompradorGatewayCartao equals cgc.IdCompradorGatewayCartao
                           where idsTransacoesNoGateway.Contains(pgts.IdTransacaoGateway) &&
                                 tfp.FormaPagamento.Id != (int)FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite
                           select new DadosDosRecebiveisDTO()
                           {
                               IdTransacaoGateway = pgts.IdTransacaoGateway,
                               NomeCliente = cgc.CompradorGateway.Comprador.PrimeiroNome + " " + cgc.CompradorGateway.Comprador.UltimoNome,
                               NomeItem = pi.Nome ?? "-",
                               DataRecebimento = pgts.DataPrevisaoRecebimento,
                               IdFormaPagamento = tfp.FormaPagamento.Id,
                               
                               Tipo = (from ti in transacaoItem
                                   where ti.Transacao.Id == tr.Id
                                   select ti.Tipo).FirstOrDefault()
                           });

            return retorno;
        }

        public IQueryable<Pagamento> ObterPagamentoPorIdCobrancaNoGateway(string idCobrancaNoGateway)
            => Queryable().Where(p => p.IdCobrancaGateway == idCobrancaNoGateway);

        public MetodoDePagamentoNoGatewayEnum? ObterMetodoPorIdPagamentos(int idPagamento)
            => Queryable()
                .Where(p => p.IdPagamento == idPagamento)
                .Select(p => p.MetodoPagamento)
                .FirstOrDefault();
    }
}