﻿using NHibernate.Linq;
using System.Linq;

namespace Perlink.Trinks.Pagamentos.Repositories
{
    public partial class RecebedorRepository : IRecebedorRepository
    {
        public Recebedor ObterRecebedorPorCNPJ(string cnpj)
        {
            return Queryable().SingleOrDefault(r => r.CNPJ == cnpj);
        }

        public Recebedor ObterRecebedorPorCNPJEGateway(string cnpj, int gateway)
        {
            var query = Queryable();
            var recebedorCredenciadoQuery = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            return (from recebedor in query
                    join recebedorCredenciado in recebedorCredenciadoQuery on recebedor.IdRecebedor equals recebedorCredenciado.Recebedor.IdRecebedor
                    where recebedorCredenciado.Gateway.IdGateway == gateway && recebedor.CNPJ == cnpj
                    select recebedor)
                    .FirstOrDefault();
        }

        public bool RecebedorPossuiAntecipacaoHabilitada(int idEstabelecimento)
        {
            var estabelecimentoRecebedorQuery = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable();

            return (from recebedor in Queryable()
                    join estabelecimentoRecebedor in estabelecimentoRecebedorQuery on recebedor.IdRecebedor equals
                        estabelecimentoRecebedor.IdRecebedor
                    where estabelecimentoRecebedor.IdEstabelecimento == idEstabelecimento && estabelecimentoRecebedor.Ativo
                    select recebedor.PermiteAntecipacao ?? false).FirstOrDefault();
        }

        public Recebedor ObterRecebedorComCredenciamentosPorIdEstabelecimento(int idEstabelecimento)
        {
            var estabelecimentoRecebedorQuery = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable();

            return Queryable()
                .FetchMany(r => r.RecebedoresCredenciados)
                .Join(estabelecimentoRecebedorQuery, r => r.IdRecebedor, er => er.IdRecebedor, (r, er) => new
                {
                    Recebedor = r,
                    IdEstabelecimento = er.IdEstabelecimento
                })
                .Where(r => r.IdEstabelecimento == idEstabelecimento)
                .Select(r => r.Recebedor)
                .FirstOrDefault();
        }
    }
}
