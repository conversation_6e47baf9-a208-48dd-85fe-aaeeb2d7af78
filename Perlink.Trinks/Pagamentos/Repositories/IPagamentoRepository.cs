﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pagamentos.Repositories
{
    public partial interface IPagamentoRepository
    {
        int ObterQuantidadeDeParcelasDoPagamento(int idPagamento);
        IQueryable<DadosDosRecebiveisDTO> ListarDadosDosRecebiveisDtos(List<string> idsTransacoesNoGateway);
        IQueryable<Pagamento> ObterPagamentoPorIdCobrancaNoGateway(string idCobrancaNoGateway);
        MetodoDePagamentoNoGatewayEnum? ObterMetodoPorIdPagamentos(int idPagamento);
    }
}