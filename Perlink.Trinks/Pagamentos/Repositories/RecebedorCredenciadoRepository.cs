﻿using NHibernate.Linq;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pagamentos.Repositories
{
    public partial class RecebedorCredenciadoRepository : IRecebedorCredenciadoRepository
    {

        public RecebedorCredenciado ObterRecebedorCredenciadoNoGateway(int idRecebedor)
        {
            return Queryable().SingleOrDefault(r => r.Recebedor.IdRecebedor == idRecebedor);
        }

        public RecebedorCredenciado ObterRecebedorCredenciadoNoGateway(int idRecebedor, GatewayEnum gateway)
        {
            return Queryable().SingleOrDefault(r => r.Recebedor.IdRecebedor == idRecebedor && r.Gateway.IdGateway == (int)gateway);
        }

        public RecebedorCredenciado ObterPorIdRecebedorNoGateway(string idRecebedorNoGateway, GatewayEnum gateway)
        {
            return Queryable().SingleOrDefault(r => r.IdRecebedorNoGateway == idRecebedorNoGateway && r.Gateway.IdGateway == (int)gateway);
        }

        public List<RecebedorCredenciado> ObterRecebedoresCredenciadoNoGateway(IEnumerable<int> idsDosRecebedores)
        {
            return Queryable()
                .Where(r => idsDosRecebedores.Contains(r.Recebedor.IdRecebedor))
                .Fetch(r => r.Recebedor)
                .ToList();
        }

        public List<RecebedorCredenciado> ObterRecebedoresCredenciadoNoGateway(IEnumerable<int> idsDosRecebedores, GatewayEnum gateway)
        {
            return Queryable()
                .Where(r => idsDosRecebedores.Contains(r.Recebedor.IdRecebedor) && r.Gateway.IdGateway == (int)gateway)
                .Fetch(r => r.Recebedor)
                .ToList();
        }

        public bool VerificarSeExisteRecebedorNoGateway(IEnumerable<int> idsDosRecebedores, GatewayEnum gateway)
        {
            return Queryable()
                .Any(r => idsDosRecebedores.Contains(r.Recebedor.IdRecebedor) && r.Gateway.IdGateway == (int)gateway);
        }

        public bool VerificarSeGatewayDoRecebedorEstahAtivo(int idRecebedorCredenciado)
        {
            var recebedorCredenciado = Queryable();
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable();

            var gatewayDoRecebedorEstahAtivo = (
                    from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where rc.IdRecebedorCredenciado == idRecebedorCredenciado
                    select er.Ativo
                ).SingleOrDefault();

            return gatewayDoRecebedorEstahAtivo;
        }

        public string ObterIdRecebedorNoGatewayPorIdRecebedor(int idRecebedor)
        {
            return Queryable()
                .Where(rc => rc.Recebedor.IdRecebedor.Equals(idRecebedor))
                .Select(rc => rc.IdRecebedorNoGateway)
                .SingleOrDefault();
        }

        public StatusKycEGatewayDTO ObterStatusKycERecebedorPorIdEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable();
            var recebedorCredenciado = Queryable();

            return (
                    from recebCred in recebedorCredenciado
                    join estabReceb in estabelecimentoRecebedor on recebCred.Recebedor.IdRecebedor equals estabReceb.IdRecebedor
                    where estabReceb.IdEstabelecimento == idEstabelecimento && recebCred.Gateway.IdGateway == (int)gateway
                    select new StatusKycEGatewayDTO(recebCred.StatusKyc, recebCred.MotivoStatusKyc, recebCred.StatusGateway)
                ).FirstOrDefault();
        }

        public int ObterIdGatewayDoIdRecebedorCredenciado(int idRecebedorCredenciado)
            => Queryable().Where(rc => rc.IdRecebedorCredenciado == idRecebedorCredenciado)
                .Select(rc => rc.Gateway.IdGateway).FirstOrDefault();
    }
}
