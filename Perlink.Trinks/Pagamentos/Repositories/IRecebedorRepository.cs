﻿namespace Perlink.Trinks.Pagamentos.Repositories
{
    public partial interface IRecebedorRepository
    {
        Recebedor ObterRecebedorPorCNPJ(string cnpj);
        Recebedor ObterRecebedorPorCNPJEGateway(string cnpj, int gateway);
        bool RecebedorPossuiAntecipacaoHabilitada(int idEstabelecimento);
        Recebedor ObterRecebedorComCredenciamentosPorIdEstabelecimento(int idEstabelecimento);
    }
}
