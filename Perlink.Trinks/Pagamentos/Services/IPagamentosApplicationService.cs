﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Pagamentos.Gateways;
using Perlink.Trinks.Pagamentos.DTO;
using System.Threading.Tasks;

namespace Perlink.Trinks.Pagamentos.Services
{
    public interface IPagamentosApplicationService : IService
    {
        Task<Resultado<NovoPagamentoNoGateway>> RealizarPagamento(NovoPagamentoDTO novoPagamento);
        Task<Resultado<NovoPagamentoNoGateway>> CriarPedidoPix(NovoPedidoPixDTO pedido);
        Task<Resultado<bool>> EstornarPagamento(int idPagamento);
    }
}
