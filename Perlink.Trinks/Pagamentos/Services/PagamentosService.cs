﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.Pagamentos.Factories;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pagamentos.Services
{
    public class PagamentosService : BaseService, IPagamentosService
    {

        public GatewayEnum GatewayAtivoDoRecebedor(int idRecebedor)
        {
            var recebedor = Domain.Pagamentos.RecebedorCredenciadoRepository.ObterRecebedorCredenciadoNoGateway(idRecebedor);

            return (GatewayEnum)recebedor.Gateway.IdGateway;
        }

        public GatewayEnum ObterGatewayPadraoParaCredenciamento(OrigemDeCadastroEnum origem)
        {
            switch (origem)
            {
                case OrigemDeCadastroEnum.Zoop:
                    var gateway = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgtoOnline_gateway).ObterValor();
                    return StringToGateway(gateway);
                case OrigemDeCadastroEnum.Pagarme:
                    return GatewayEnum.PagarMe;
                case OrigemDeCadastroEnum.PagarMeV5:
                    return GatewayEnum.PagarMeV5;
                default:
                    throw new NotImplementedException();
            }
        }

        public string ObterUrlPostBack(GatewayEnum gateway)
        {

            string urlPostBack;

            switch (gateway)
            {
                case GatewayEnum.PagarMe:
                    urlPostBack = new ParametrosTrinks<string>(ParametrosTrinksEnum.postback_url_mudanca_status_recebedor_pagarme).ObterValor();
                    break;
                case GatewayEnum.Zoop:
                    urlPostBack = null;
                    break;
                case GatewayEnum.PagarMeV5:
                    urlPostBack = null;
                    break;
                default:
                    throw new NotImplementedException();
            }

            return urlPostBack;
        }

        public GatewayEnum ObterGatewayPadraoParaCadastroDeCartao()
        {
            return GatewayEnum.Zoop;
        }

        public bool GatewayRequerUploadDeDocumentos(int idEstabelecimento, OrigemDeCadastroEnum origem)
        {
            // TODO: Implementar no gateway flag dizendo se ele requer envio de documentos

            return ObterGatewayDoEstabelecimentoOuGatewayPadrao(idEstabelecimento, origem) == GatewayEnum.Zoop;
        }

        public bool GatewayAtivoDoRecebedorRequerUploadDeDocumentos(int idEstabelecimento)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idEstabelecimento);

            if (estabelecimentoRecebedor == null)
                return false;

            var gatewayRecebedor = GatewayAtivoDoRecebedor(estabelecimentoRecebedor.IdRecebedor);

            var configGateway = ConfiguracaoGatewayFactory.ObterConfiguracoesDeAcordoComOGateway(gatewayRecebedor);

            return configGateway.RequerUploadDeDocumentos;
        }

        public bool EstahCredenciadoNoGateway(int idRecebedor, GatewayEnum gateway)
        {
            return Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable().Any(rc =>
                rc.Recebedor.IdRecebedor == idRecebedor && rc.Gateway.IdGateway == (int)gateway);
        }

        public bool GatewayPermiteAntiFraude(int idEstabelecimento, OrigemDeCadastroEnum origem)
        {
            // TODO: Implementar no gateway flag dizendo se ele possui antifraude
            var gateway = ObterGatewayDoEstabelecimentoOuGatewayPadrao(idEstabelecimento, origem);
            return gateway == GatewayEnum.PagarMe || gateway == GatewayEnum.PagarMeV5;
        }

        public bool EstabelecimentoTemCadastroAtivoNaPagarMe(int idEstabelecimento)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idEstabelecimento);

            if (estabelecimentoRecebedor == null)
                return false;

            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.ObterRecebedorCredenciadoNoGateway(estabelecimentoRecebedor.IdRecebedor);
            var idGateway = recebedorCredenciado.Gateway.IdGateway;

            return idGateway == (int)GatewayEnum.PagarMe || idGateway == (int)GatewayEnum.PagarMeV5;
        }
        public GatewayEnum ObterGatewayDoEstabelecimento(int idEstabelecimento)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idEstabelecimento);
            var recebedorCredenciado =
                Domain.Pagamentos.RecebedorCredenciadoRepository.ObterRecebedorCredenciadoNoGateway(
                    estabelecimentoRecebedor.IdRecebedor);

            return (GatewayEnum)recebedorCredenciado.Gateway.IdGateway;
        }

        public List<GatewayEnum> ListarGatewaysParaLinkDePagamento()
            => new ParametrosTrinks<string>(ParametrosTrinksEnum.pgtoPorLink_gateways).ObterValor()
                .Split(',')
                .Select(gs => gs.Trim())
                .Select(gs => StringToGateway(gs))
                .ToList();

        public GatewayEnum ObterGatewayPadraoParaPagamentoAntecipado()
        {
            var gatewayPagamentoAntecipado = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgtoOnline_gateway).ObterValor();
            return StringToGateway(gatewayPagamentoAntecipado);
        }

        public GatewayEnum ObterGatewayDoEstabelecimentoOuGatewayPadrao(int idEstabelecimento, OrigemDeCadastroEnum origem)
        {
            var (estabelecimentoRecebedor, gateway) = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ObterAtivoComGatewayPorEstabelecimento(idEstabelecimento);

            if (estabelecimentoRecebedor == null)
            {
                return ObterGatewayPadraoParaCredenciamento(origem);
            }

            return gateway.Value;
        }

        private GatewayEnum StringToGateway(string gateway, string nomeDaConfig = null)
        {
            if (Enum.TryParse(gateway, out GatewayEnum gatewaySelecionado))
                return gatewaySelecionado;
            
            if (!string.IsNullOrEmpty(nomeDaConfig))
            {
                throw new InvalidOperationException($"Configure o parâmetro '{nomeDaConfig}' com um dos nomes válidos. O nome '{gateway}' configurado é inválido.");
            }

            throw new InvalidOperationException($"O nome do gateway configurado ('{gateway}') é inválido.");
        }

        public Pagamento ObterPorIdDaTransacaoNoGateway(string idTransacaoNoGateway)
        {
            return Domain.Pagamentos.PagamentoRepository.Queryable().FirstOrDefault(f => f.IdTransacaoGateway == idTransacaoNoGateway);
        }

        public Pagamento SalvarEntidadePagamento(NovoPagamentoDTO novoPagamento, CartaoDeCompradorGateway cartao, List<RecebedorCredenciado> recebedores, RecebedorCredenciado recebedorCredenciado)
        {
            var pagamento = new Pagamento()
            {
                MetodoPagamento = novoPagamento.MetodoDePagamento,
                IdRecebedorCredenciado = recebedorCredenciado.IdRecebedorCredenciado,
                IdCompradorGatewayCartao = cartao.IdCompradorGatewayCartao,
                Valor = novoPagamento.Valor,
                StatusPagamento = Enums.StatusPagamentoEnum.AguardandoConfirmacaoDePagamento,
                PagamentoRealizado = false,
                DataCriacao = Calendario.Agora(),
            };

            pagamento.Itens = novoPagamento.Itens.Select(i => new ItemPagamento()
            {
                Nome = i.Nome,
                Valor = i.Valor,
                Pagamento = pagamento
            }).ToList();

            if (novoPagamento.RegrasDeSplit.Any())
            {
                pagamento.RegrasDeSplit = novoPagamento.RegrasDeSplit.Select(r =>
                {
                    RecebedorCredenciado recebedorDoSplit = recebedores.SingleOrDefault(x => x.Recebedor.IdRecebedor == r.IdRecebedor);

                    return new SplitPagamento()
                    {
                        Pagamento = pagamento,
                        IdRecebedorCredenciado = recebedorDoSplit.IdRecebedorCredenciado,
                        ResponsavelPelasTaxasDaOperacao = r.ResponsavelPelasTaxasDaOperacao,
                        ResponsavelPeloChargeback = r.ResponsavelPeloChargeback,
                        ResponsavelPeloRestoDaDivisaoDasTaxas = r.ResponsavelPeloRestoDaDivisaoDeTaxas,
                        Valor = r.Valor
                    };
                }).ToList();
            }

            Domain.Pagamentos.PagamentoRepository.SaveNew(pagamento);
            return pagamento;
        }
    }
}