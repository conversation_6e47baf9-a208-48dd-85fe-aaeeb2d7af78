﻿using Elmah;
using Perlink.DomainInfrastructure.Caching;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways;
using Perlink.Pagamentos.Gateways.Config;
using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Auditing;
using Perlink.Shared.Text;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.Pagamentos.Calculos;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.Pagamentos.Exceptions;
using Perlink.Trinks.Pagamentos.Providers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Perlink.Trinks.Pagamentos.Factories;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Interface;
using Perlink.Trinks.Financeiro;

namespace Perlink.Trinks.Pagamentos.Services
{
    public class PagamentosApplicationService : BaseService, IPagamentosApplicationService
    {
        private readonly IConfigurationProvider _pagamentoOnlineConfigurationProvider;
        private readonly ICache _cache;

        public PagamentosApplicationService(ICache cache)
        {
            _pagamentoOnlineConfigurationProvider = new PagamentoOnlineConfigurationProvider();
            _cache = cache;
        }

        public async Task<Resultado<NovoPagamentoNoGateway>> RealizarPagamento(NovoPagamentoDTO novoPagamento)
        {
            var ehRecorrencia = novoPagamento.OrigemDePagamento == OrigemDePagamentoEnum.RecorrenciaDaAssinatura;

            var recebedorTemLimiteDisponivel = RecebedorTemLimiteDisponivel(novoPagamento.IdRecebedor, novoPagamento.Valor, ehRecorrencia);

            if (!recebedorTemLimiteDisponivel)
                return Resultado.Falhar<NovoPagamentoNoGateway>("O recebedor não possui limite disponível para essa transação");

            var retorno = await RealizarNovoPagamento(novoPagamento);

            if ((retorno.Falha) || (retorno.Valor is null))
                return retorno;


            if (!ehRecorrencia && retorno.Valor.PagamentoFoiRealizado)
            {
                IncluirNaFilaProcessamentoLimite(novoPagamento, retorno);
            }

            return retorno;
        }
        
        public async Task<Resultado<NovoPagamentoNoGateway>> CriarPedidoPix(NovoPedidoPixDTO pedido)
        {
            var novoPagamento = pedido.DadosDoPagamento;
            var recebedorTemLimiteDisponivel = RecebedorTemLimiteDisponivel(novoPagamento.IdRecebedor, novoPagamento.Valor, false);

            if (!recebedorTemLimiteDisponivel)
                return Resultado.Falhar<NovoPagamentoNoGateway>("O recebedor não possui limite disponível para essa transação");

            return await CriarNovoPedidoPix(pedido);
        }

        private void ValidarPrecisoes(NovoPagamentoDTO novoPagamento)
        {
            ValidarPrecisao(novoPagamento.Valor, "novoPagamento.Valor");

            for (int i = 0; i < novoPagamento.Itens.Count; i++)
                ValidarPrecisao(novoPagamento.Itens[i].Valor, $"novoPagamento.Itens[{i}].Valor");

            for (int i = 0; i < novoPagamento.RegrasDeSplit.Count; i++)
                ValidarPrecisao(novoPagamento.RegrasDeSplit[i].Valor, $"novoPagamento.RegrasDeSplit[{i}].Valor");
        }

        public static string ObterTipoDeCliente(string documento)
        {
            if (string.IsNullOrWhiteSpace(documento))
                throw new ArgumentException("O documento não pode ser nulo ou vazio.", nameof(documento));

            documento = Regex.Replace(documento, @"\D", "");

            if (documento.Length == 11)
                return ClienteTipo.Individual;

            if (documento.Length == 14)
                return ClienteTipo.Empresa;

            throw new InvalidOperationException("Não foi possível determinar o tipo de cliente. Verifique o documento fornecido.");
        }

        private async Task<Resultado<NovoPagamentoNoGateway>> CriarNovoPedidoPix(NovoPedidoPixDTO pedido)
        {
            var novoPagamento = pedido.DadosDoPagamento;
            var pixGatewayService = ObterGatewayParaPagamentoPix(novoPagamento.IdRecebedor);
            ValidarPrecisoes(novoPagamento);

            var recebedores = ObterRecebedoresCredenciados(novoPagamento);
            ValidarRecebedores(recebedores, novoPagamento);

            var pagamento = CriarPagamento(novoPagamento, recebedores);

            var novoPagamentoNoGateway = CriarNovoPagamentoComPixNoGatewayDTO(pedido, pagamento, recebedores);

            var retornoDoGateway = await pixGatewayService.CriarPedidoPix(novoPagamentoNoGateway, pedido.TempoDeExpiracao);
            VerificarErroGateway(retornoDoGateway);

            return ProcessarResultadoGateway(retornoDoGateway, pagamento);
        }

        private IGatewayPix ObterGatewayParaPagamentoPix(int idRecebedor)
        {
            var pixGatewayService = Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedor(idRecebedor);
            return PixGatewayFactory.ObterGatewayParaPagamentoPix(pixGatewayService, _pagamentoOnlineConfigurationProvider);
        }

        private List<RecebedorCredenciado> ObterRecebedoresCredenciados(NovoPagamentoDTO novoPagamento)
        {
            var idsDosRecebedores = novoPagamento.RegrasDeSplit.Select(x => x.IdRecebedor).Append(novoPagamento.IdRecebedor).Distinct().ToList();
            return Domain.Pagamentos.RecebedorCredenciadoRepository.ObterRecebedoresCredenciadoNoGateway(idsDosRecebedores, Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedor(novoPagamento.IdRecebedor));
        }

        private void ValidarRecebedores(List<RecebedorCredenciado> recebedores, NovoPagamentoDTO novoPagamento)
        {

            if (recebedores.Count != novoPagamento.RegrasDeSplit.Select(r => r.IdRecebedor).Distinct().Count())
            {
                var ids = novoPagamento.RegrasDeSplit.Select(r => r.IdRecebedor).Distinct().ToList();
                var idsString = string.Join(", ", ids);
                var mensagem = $"Um ou mais recebedores não foram encontrados no gateway ativo. IDs: {idsString}";
                LogService<PagamentosApplicationService>.Error(mensagem);
                Elmah.ErrorLog.GetDefault(null).Log(new Error(new BusinessException(mensagem)));
                throw new RecebedorNaoEncontradoNoGatewayException();
            }

            if (novoPagamento.Valor != novoPagamento.Itens.Sum(i => i.Valor))
                throw new ErroNoCalculoDoTotalDoPagamentoException();

            var diff = novoPagamento.Valor - novoPagamento.RegrasDeSplit.Sum(r => r.Valor);
            if (diff > 0.01m)
                throw new ErroNoCalculoDoSplitDoPagamentoException();

            if (novoPagamento.RegrasDeSplit.Any(r => r.Valor <= 0))
            {
                if (novoPagamento.Valor == 1m)
                {
                    // Se o valor total do pagamento for 1, é possível que as taxas sejam menores que 1 centavo
                    return;
                }

                throw new ErroNoCalculoDoSplitDoPagamentoException();
            }
        }

        private Pagamento CriarPagamento(NovoPagamentoDTO novoPagamento, List<RecebedorCredenciado> recebedores)
        {
            var recebedorCredenciado = recebedores.SingleOrDefault(r => r.Recebedor.IdRecebedor == novoPagamento.IdRecebedor);

            var pagamento = new Pagamento
            {
                MetodoPagamento = novoPagamento.MetodoDePagamento,
                IdRecebedorCredenciado = recebedorCredenciado.IdRecebedorCredenciado,
                ReferenciaDeOrigem = novoPagamento.ReferenciaDeOrigem,
                Valor = novoPagamento.Valor,
                QuantidadeParcelas = 1,
                StatusPagamento = StatusPagamentoEnum.AguardandoConfirmacaoDePagamento,
                PagamentoRealizado = false,
                DataCriacao = Calendario.Agora(),
            };

            pagamento.Itens = novoPagamento.Itens.Select(i => new ItemPagamento
            {
                Nome = i.Nome,
                Valor = i.Valor,
                Pagamento = pagamento
            }).ToList();
            
            if (novoPagamento.RegrasDeSplit.Any())
            {
                pagamento.RegrasDeSplit = CriarRegrasDeSplit(novoPagamento, recebedores, pagamento);
            }

            Domain.Pagamentos.PagamentoRepository.SaveNew(pagamento);
            return pagamento;
        }

        private List<SplitPagamento> CriarRegrasDeSplit(NovoPagamentoDTO novoPagamento, List<RecebedorCredenciado> recebedores, Pagamento pagamento)
        {
            if (!novoPagamento.RegrasDeSplit.Any()) return null;

            return novoPagamento.RegrasDeSplit.Select(r =>
            {
                var recebedorDoSplit = recebedores.Single(x => x.Recebedor.IdRecebedor == r.IdRecebedor);
                return new SplitPagamento
                {
                    Pagamento = pagamento,
                    IdRecebedorCredenciado = recebedorDoSplit.IdRecebedorCredenciado,
                    ResponsavelPelasTaxasDaOperacao = r.ResponsavelPelasTaxasDaOperacao,
                    ResponsavelPeloChargeback = r.ResponsavelPeloChargeback,
                    ResponsavelPeloRestoDaDivisaoDasTaxas = r.ResponsavelPeloRestoDaDivisaoDeTaxas,
                    Valor = r.Valor
                };
            }).ToList();
        }

        private NovoPagamentoNoGatewayDTO CriarNovoPagamentoComPixNoGatewayDTO(NovoPedidoPixDTO pedido, Pagamento pagamento, List<RecebedorCredenciado> recebedores)
        {
            var novoPagamento = pedido.DadosDoPagamento;
            var recebedorCredenciado = recebedores.Single(r => r.Recebedor.IdRecebedor == novoPagamento.IdRecebedor);

            return new NovoPagamentoNoGatewayDTO
            {
                IdRecebedorNoGateway = recebedorCredenciado.IdRecebedorNoGateway,
                Comprador = new CompradorDTO
                {
                    Nome = novoPagamento.Comprador.Nome,
                    CpfCnpj = novoPagamento.Comprador.CpfCnpj,
                    Email = novoPagamento.Comprador.Email,
                    Telefone = novoPagamento.Comprador.Telefone,
                    Tipo = ObterTipoDeCliente(novoPagamento.Comprador.CpfCnpj)
                },
                EnderecoDeCobranca = new EnderecoParaCadastrarDTO
                {
                    Logradouro = novoPagamento.EnderecoDeCobranca.Logradouro,
                    Bairro = novoPagamento.EnderecoDeCobranca.Bairro,
                    CEP = novoPagamento.EnderecoDeCobranca.CEP,
                    Cidade = novoPagamento.EnderecoDeCobranca.Cidade,
                    Complemento = novoPagamento.EnderecoDeCobranca.Complemento,
                    Estado = novoPagamento.EnderecoDeCobranca.Estado,
                    Numero = novoPagamento.EnderecoDeCobranca.Numero
                },
                Descricao = $"Serviços agendados em '{recebedorCredenciado.Recebedor.Nome}'",
                NomeDeExibicaoNaFatura = GerarNomeDeExibicaoNaFatura(recebedorCredenciado.Recebedor.Nome),
                Valor = novoPagamento.Valor,
                RegrasDeSplit = CriarSplitPagamentoNoGatewayDTO(novoPagamento, recebedores),
                Itens = pagamento.Itens.Select(i => new ItemPagamentoNoGatewayDTO
                {
                    IdItemPagamento = i.IdItemPagamento,
                    EhTangivel = false,
                    NomeItem = i.Nome,
                    Quantidade = 1,
                    ValorOriginal = i.Valor
                }).ToList()
            };
        }

        private List<SplitPagamentoNoGatewayDTO> CriarSplitPagamentoNoGatewayDTO(NovoPagamentoDTO novoPagamento, List<RecebedorCredenciado> recebedores)
        {
            return novoPagamento.RegrasDeSplit.Select(s =>
            {
                var recebedorParaSplit = recebedores.Single(r => r.Recebedor.IdRecebedor == s.IdRecebedor);
                return new SplitPagamentoNoGatewayDTO
                {
                    IdRecebedorNoGateway = recebedorParaSplit.IdRecebedorNoGateway,
                    ResponsavelPelasTaxasDaOperacao = s.ResponsavelPelasTaxasDaOperacao,
                    ResponsavelPeloChargeback = s.ResponsavelPeloChargeback,
                    ResponsavelPeloRestoDaDivisaoDeTaxas = s.ResponsavelPeloRestoDaDivisaoDeTaxas,
                    Valor = s.Valor
                };
            }).ToList();
        }

        private void VerificarErroGateway(Resultado<PagamentoPixCriadoNoGatewayDTO> retornoDoGateway)
        {
            if (!string.IsNullOrWhiteSpace(retornoDoGateway?.Erro))
            {
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new BusinessException("[PgtoOnline] Exceção reportada ao realizar pagamento online. " + retornoDoGateway.Erro)));
                LogService<PagamentosApplicationService>.Error($"Exceção ao tentar realizar pagamento online. Dados do erro: {retornoDoGateway.Erro}");
            }

            if (retornoDoGateway.Falha)
                throw new Exception("Erro ao processar o pagamento na pagarme");
        }

        private Resultado<NovoPagamentoNoGateway> ProcessarResultadoGateway(Resultado<PagamentoPixCriadoNoGatewayDTO> retornoDoGateway, Pagamento pagamento)
        {
            AtualizarDadosDoPagamento(retornoDoGateway, pagamento);

            if (retornoDoGateway.Sucesso)
            {
                return Resultado.Ok(new NovoPagamentoNoGateway
                {
                    IdPagamento = pagamento.IdPagamento,
                    DataPagamento = pagamento.DataPagamento,
                    ValorPago = pagamento.Valor,
                    PagamentoFoiRealizado = false,
                    RetornoPixDTO = new RetornoPixDTO
                    {
                        LinkPix = retornoDoGateway.Valor.QrCode,
                        UrlQrCode = retornoDoGateway.Valor.QrCodeUrl,
                        IdTransacaoNoGateway = retornoDoGateway.Valor.IdTransacaoNoGateway,
                        IdCobrancaNoGateway = retornoDoGateway.Valor.IdCobrancaNoGateway,
                    }
                });
            }
            return Resultado.Falhar<NovoPagamentoNoGateway>("Erro ao processar o pagamento");
        }

        private void AtualizarDadosDoPagamento(Resultado<PagamentoPixCriadoNoGatewayDTO> retornoDoGateway, Pagamento pagamento)
        {
            try
            {
                pagamento.RegistrarDadosDaTransacao(
                    pagamentoFoiRealizado: retornoDoGateway.Valor.PagamentoFoiRealizado,
                    idTransacaoGateway: retornoDoGateway.Valor.IdTransacaoNoGateway,
                    nomeDeExibicaoNaFatura: retornoDoGateway.Valor.NomeDeExibicaoNaFatura,
                    custoTransacao: retornoDoGateway.Valor.CustoTransacao,
                    motivoRejeicaoGateway: String.Empty,
                    motivoStatusGateway: String.Empty,
                    statusGateway: retornoDoGateway.Valor.Status,
                    dataHoraPagamento: retornoDoGateway.Valor.PagamentoFoiRealizado ? Calendario.Agora() : ((DateTime?)null),
                    dataPrevisaoRecebimento: retornoDoGateway.Valor.DataEsperadaDeRecebimento,
                    idCobrancaGateway: retornoDoGateway.Valor.IdCobrancaNoGateway
                );

                Domain.Pagamentos.PagamentoRepository.Update(pagamento);
            }
            catch (Exception ex)
            {
                LogService<PagamentosApplicationService>.Error("Falha ao registrar retorno do pagamento online - " + ex.Message + " - " + ex.StackTrace);
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new BusinessException("[Pagamentos] Falha ao registrar retorno do pagamento online.", ex)));
            }
        }

        private async Task<Resultado<NovoPagamentoNoGateway>> RealizarNovoPagamento(NovoPagamentoDTO novoPagamento)
        {
            var gatewayAtivo = Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedor(novoPagamento.IdRecebedor);
            var gateway = Domain.Pagamentos.GatewayRepository.ObterPorId(gatewayAtivo);
            var gatewayService = GatewayPagamentoOnlineFactory.Create(gatewayAtivo, _pagamentoOnlineConfigurationProvider);

            ValidarPrecisao(novoPagamento.Valor, "novoPagamento.Valor");

            for (int i = 0; i < novoPagamento.Itens.Count; i++)
                ValidarPrecisao(novoPagamento.Itens[i].Valor, $"novoPagamento.Itens[{i}].Valor");

            for (int i = 0; i < novoPagamento.RegrasDeSplit.Count; i++)
                ValidarPrecisao(novoPagamento.RegrasDeSplit[i].Valor, $"novoPagamento.RegrasDeSplit[{i}].Valor");

            CartaoDeCompradorGateway cartao = null;

            Comprador customer = null;

            if (novoPagamento.Comprador == null)
            {
                customer = Domain.Pagamentos.CartaoDeCompradorRepository.ObterCompradorPorIdCartaoDeComprador(novoPagamento.IdCartaoDeComprador);
                if (customer == null)
                {
                    LogService<PagamentosApplicationService>.Error("Solicitado pagamento de usuário que não está cadastrado como comprador");
                    return Resultado.Falhar<NovoPagamentoNoGateway>("Usuário não cadastrado como comprador");
                }

                cartao = Domain.Pagamentos.CartaoDeCompradorGatewayRepository.ObterCartaoNoGateway(customer.IdComprador, novoPagamento.IdCartaoDeComprador, gatewayAtivo);
                if (cartao == null)
                {
                    LogService<PagamentosApplicationService>.Error("O cartão selecionado para pagamento não foi encontrado");
                    return Resultado.Falhar<NovoPagamentoNoGateway>("O cartão selecionado para pagamento não foi encontrado");
                }
            }

            var idsDosRecebedores = new List<int> { novoPagamento.IdRecebedor };

            if (novoPagamento.RegrasDeSplit.Any())
                idsDosRecebedores.AddRange(novoPagamento.RegrasDeSplit.Select(x => x.IdRecebedor));

            idsDosRecebedores = idsDosRecebedores.Distinct().ToList();

            var recebedores = Domain.Pagamentos.RecebedorCredenciadoRepository.ObterRecebedoresCredenciadoNoGateway(idsDosRecebedores, gatewayAtivo);
            if (recebedores.Count != idsDosRecebedores.Count)
            {
                LogService<PagamentosApplicationService>.Error("Um ou mais recebedores não foram encontrados no gateway ativo");
                throw new RecebedorNaoEncontradoNoGatewayException();
            }

            var recebedorCredenciado = recebedores.SingleOrDefault(r => r.Recebedor.IdRecebedor == novoPagamento.IdRecebedor);

            if (novoPagamento.Valor != novoPagamento.Itens.Sum(i => i.Valor))
                throw new ErroNoCalculoDoTotalDoPagamentoException();

            if (novoPagamento.Valor - novoPagamento.RegrasDeSplit.Sum(r => r.Valor) > 0.01m || novoPagamento.RegrasDeSplit.Any(r => r.Valor <= 0))
                throw new ErroNoCalculoDoSplitDoPagamentoException();

            var quantidadeParcelas = novoPagamento.QuantidadeParcelas ?? 1;

            var pagamento = new Pagamento()
            {
                MetodoPagamento = novoPagamento.MetodoDePagamento,
                IdRecebedorCredenciado = recebedorCredenciado.IdRecebedorCredenciado,
                ReferenciaDeOrigem = novoPagamento.ReferenciaDeOrigem,
                IdCompradorGatewayCartao = cartao?.IdCompradorGatewayCartao,
                Valor = novoPagamento.Valor,
                QuantidadeParcelas = quantidadeParcelas,
                StatusPagamento = StatusPagamentoEnum.AguardandoConfirmacaoDePagamento,
                PagamentoRealizado = false,
                DataCriacao = Calendario.Agora(),
            };

            pagamento.Itens = novoPagamento.Itens.Select(i => new ItemPagamento()
            {
                Nome = i.Nome,
                Valor = i.Valor,
                Pagamento = pagamento
            }).ToList();

            if (novoPagamento.RegrasDeSplit.Any())
            {
                pagamento.RegrasDeSplit = novoPagamento.RegrasDeSplit.Select(r =>
                {
                    var recebedorDoSplit = recebedores.SingleOrDefault(x => x.Recebedor.IdRecebedor == r.IdRecebedor);

                    return new SplitPagamento()
                    {
                        Pagamento = pagamento,
                        IdRecebedorCredenciado = recebedorDoSplit.IdRecebedorCredenciado,
                        ResponsavelPelasTaxasDaOperacao = r.ResponsavelPelasTaxasDaOperacao,
                        ResponsavelPeloChargeback = r.ResponsavelPeloChargeback,
                        ResponsavelPeloRestoDaDivisaoDasTaxas = r.ResponsavelPeloRestoDaDivisaoDeTaxas,
                        Valor = r.Valor
                    };
                }).ToList();
            }

            Domain.Pagamentos.PagamentoRepository.SaveNew(pagamento);

            var novoPagamentoNoGateway = new NovoPagamentoNoGatewayDTO
            {
                IdRecebedorNoGateway = recebedorCredenciado.IdRecebedorNoGateway,
                IdCartaoNoGateway = cartao?.IdNoGateway ?? string.Empty,
                IdCompradorNoGateway = cartao?.CompradorGateway.IdNoGateway ?? string.Empty,
                Descricao = $"Serviços agendados em '{recebedorCredenciado.Recebedor.Nome}'",
                NomeDeExibicaoNaFatura = GerarNomeDeExibicaoNaFatura(recebedorCredenciado.Recebedor.Nome),
                Valor = novoPagamento.Valor,
                QuantidadeParcelas = quantidadeParcelas,
                RegrasDeSplit = novoPagamento.RegrasDeSplit.Select(s =>
                {
                    var recebedorParaSplit = recebedores.SingleOrDefault(r => r.Recebedor.IdRecebedor == s.IdRecebedor);
                    return new SplitPagamentoNoGatewayDTO
                    {
                        IdRecebedorNoGateway = recebedorParaSplit.IdRecebedorNoGateway,
                        ResponsavelPelasTaxasDaOperacao = s.ResponsavelPelasTaxasDaOperacao,
                        ResponsavelPeloChargeback = s.ResponsavelPeloChargeback,
                        ResponsavelPeloRestoDaDivisaoDeTaxas = s.ResponsavelPeloRestoDaDivisaoDeTaxas,
                        Valor = s.Valor
                    };
                }).ToList(),
                Itens = pagamento.Itens.Select(i => new ItemPagamentoNoGatewayDTO
                {
                    IdItemPagamento = i.IdItemPagamento,
                    EhTangivel = false,
                    NomeItem = i.Nome,
                    Quantidade = 1,
                    ValorOriginal = i.Valor
                }).ToList()
            };

            var cartaoAindaNaoCadastradoNoGateway = cartao == null;

            ObterDadosAdicionaisParaRealizarPagamento(novoPagamento, cartaoAindaNaoCadastradoNoGateway, customer, novoPagamentoNoGateway);

            Resultado<PagamentoRealizadoNoGatewayDTO> retornoDoGateway;

            try
            {
                retornoDoGateway = await gatewayService.RealizarPagamentoOnline(novoPagamentoNoGateway);
            } catch (Exception ex)
            {
                var inner = new Exception($"Dados do erro: {ex.Message} - Stack trace: {ex.StackTrace}");
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new BusinessException($"[PgtoOnline] Exceção ao tentar realizar pagamento online.", inner)));
                LogService<PagamentosApplicationService>.Error($"[PgtoOnline] Exceção ao tentar realizar pagamento online.");
                throw;
            }

            var erro = retornoDoGateway?.Valor?.InformacoesDeErro;
            if (!string.IsNullOrEmpty(retornoDoGateway?.Erro))
            {
                erro += $"Erro: {retornoDoGateway?.Erro}";
            }

            if (!string.IsNullOrWhiteSpace(erro))
            {
                var inner = new Exception($"Dados do erro: {erro}");
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new BusinessException($"[PgtoOnline] Erro ao tentar realizar pagamento online.", inner)));
                LogService<PagamentosApplicationService>.Error($"[PgtoOnline] Erro ao tentar realizar pagamento online.");
            }

            if (retornoDoGateway.Falha)
                return Resultado.Falhar<NovoPagamentoNoGateway>("Erro ao processar o pagamento na pagarme");

            try
            {
                pagamento.RegistrarDadosDaTransacao(
                    pagamentoFoiRealizado: retornoDoGateway.Valor.PagamentoFoiRealizado,
                    idTransacaoGateway: retornoDoGateway.Valor.IdTransacaoNoGateway,
                    nomeDeExibicaoNaFatura: retornoDoGateway.Valor.NomeDeExibicaoNaFatura,
                    custoTransacao: retornoDoGateway.Valor.CustoTransacao,
                    motivoRejeicaoGateway: String.Empty,
                    motivoStatusGateway: String.Empty,
                    statusGateway: retornoDoGateway.Valor.Status,
                    dataHoraPagamento: retornoDoGateway.Valor.PagamentoFoiRealizado ? Calendario.Agora() : ((DateTime?)null),
                    dataPrevisaoRecebimento: retornoDoGateway.Valor.DataEsperadaDeRecebimento,
                    idCobrancaGateway: retornoDoGateway.Valor.IdCobrancaNoGateway
                );

                if (novoPagamento.Comprador != null && novoPagamento.Cartao != null)
                {
                    var comprador = RegistrarDadosDoComprador(novoPagamento.Comprador);
                    var compradorNoGateway = RegistrarDadosDoCompradorNoGateway(gateway, comprador, retornoDoGateway.Valor);
                    var cartaoDeComprador = RegistrarDadosDoCartaoDoComprador(novoPagamento.Cartao, comprador, retornoDoGateway.Valor);
                    var cartaoDeCompradorGateway = RegistrarDadosDoCartaoDeCompradorNoGateway(compradorNoGateway, retornoDoGateway.Valor, cartaoDeComprador);

                    if (novoPagamento.EnderecoDeCobranca != null)
                        RegistrarDadosDoEnderecoDeCobranca(cartaoDeComprador.IdCartaoDeComprador, novoPagamento.EnderecoDeCobranca);

                    pagamento.IdCompradorGatewayCartao = cartaoDeCompradorGateway.IdCompradorGatewayCartao;
                }

                Domain.Pagamentos.PagamentoRepository.Update(pagamento);
            }
            catch (Exception ex)
            {
                LogService<PagamentosApplicationService>.Error("Falha ao registrar retorno do pagamento online - " + ex.Message + " - " + ex.StackTrace);
                Elmah.ErrorSignal.FromCurrentContext().Raise(new BusinessException("[Pagamentos] Falha ao registrar retorno do pagamento online."));
            }

            if (retornoDoGateway != null && retornoDoGateway.Sucesso)
            {
                return Resultado.Ok(new NovoPagamentoNoGateway()
                {
                    IdPagamento = pagamento.IdPagamento,
                    DataPagamento = pagamento.DataPagamento,
                    ValorPago = pagamento.Valor,
                    PagamentoFoiRealizado = retornoDoGateway.Valor.PagamentoFoiRealizado
                });
            }
            else
            {
                return Resultado.Falhar<NovoPagamentoNoGateway>("Erro ao processar o pagamento");
            }
        }

        private void ObterDadosAdicionaisParaRealizarPagamento(NovoPagamentoDTO novoPagamento, bool cartaoAindaNaoCadastradoNoGateway, Comprador customer, NovoPagamentoNoGatewayDTO novoPagamentoNoGateway)
        {

            if (cartaoAindaNaoCadastradoNoGateway)
            {
                PreencherDadosAdicionais(novoPagamento, novoPagamentoNoGateway);

            }
            else
            {
                ObterDadadosAdicionaisJaCadastrados(customer, novoPagamentoNoGateway);
            }

        }

        private void ObterDadadosAdicionaisJaCadastrados(Comprador comprador, NovoPagamentoNoGatewayDTO novoPagamentoNoGateway)
        {

            var cpf = Domain.Pessoas.PessoaFisicaRepository.ObterCpfPorEmail(comprador.Email);

            novoPagamentoNoGateway.Comprador = new CompradorDTO
            {
                IdComprador = comprador.IdComprador,
                Nome = $"{comprador.PrimeiroNome} {comprador.UltimoNome}",
                CpfCnpj = cpf,
                Email = comprador.Email,
                Telefone = comprador.Telefone,
            };

            var enderecoDeCobranca = Domain.Pagamentos.EnderecoDeCobrancaRepository.ObterEnderecoDeCobrancaPeloIdCartaoNoGateway(novoPagamentoNoGateway.IdCartaoNoGateway);

            if (enderecoDeCobranca == null)
                return;

            var endereco = enderecoDeCobranca.Endereco;

            novoPagamentoNoGateway.EnderecoDeCobranca = new EnderecoParaCadastrarDTO()
            {
                Bairro = endereco.Bairro,
                CEP = endereco.CEP.ToString(),
                Cidade = endereco.Cidade,
                Estado = endereco.Estado,
                Logradouro = endereco.Logradouro,
                Numero = endereco.Numero
            };
        }

        private void PreencherDadosAdicionais(NovoPagamentoDTO novoPagamento, NovoPagamentoNoGatewayDTO novoPagamentoNoGateway)
        {

            var comprador = novoPagamento.Comprador;
            var enderecoDeCobranca = novoPagamento.EnderecoDeCobranca;
            var cartao = novoPagamento.Cartao;


            novoPagamentoNoGateway.Cartao = new DadosDoCartaoDTO
            {
                Nome = cartao.Nome,
                CVV = cartao.CVV,
                Numero = cartao.Numero,
                Validade = cartao.Validade
            };

            novoPagamentoNoGateway.Comprador = new CompradorDTO
            {
                IdComprador = comprador.IdComprador,
                Nome = comprador.Nome,
                CpfCnpj = comprador.CpfCnpj,
                Email = comprador.Email,
                Telefone = comprador.Telefone,
            };

            if (enderecoDeCobranca != null)
            {
                novoPagamentoNoGateway.EnderecoDeCobranca = new EnderecoParaCadastrarDTO
                {
                    Logradouro = enderecoDeCobranca.Logradouro,
                    Bairro = enderecoDeCobranca.Bairro,
                    CEP = enderecoDeCobranca.CEP,
                    Cidade = enderecoDeCobranca.Cidade,
                    Complemento = enderecoDeCobranca.Complemento,
                    Estado = enderecoDeCobranca.Estado,
                    Numero = enderecoDeCobranca.Numero
                };
            }
        }

        private CartaoDeCompradorGateway RegistrarDadosDoCartaoDeCompradorNoGateway(CompradorGateway compradorGateway, PagamentoRealizadoNoGatewayDTO valor,
            CartaoDeComprador cartaoDeComprador)
        {
            CartaoCadastradoNoGatewayDTO cartaoCadastradoNoGatewayDTO = new CartaoCadastradoNoGatewayDTO()
            {
                Bandeira = valor.Cartao.Bandeira,
                IdDoCartaoNoGateway = valor.Cartao.IdDoCartaoNoGateway,
                Fingerprint = valor.Cartao.Fingerprint,
                Valido = valor.Cartao.Valido
            };

            return Domain.Pagamentos.CartaoApplicationService
                .GerarEntidadeCartaoDeCompradorGateway(compradorGateway, cartaoCadastradoNoGatewayDTO, cartaoDeComprador);
        }

        private CartaoDeComprador RegistrarDadosDoCartaoDoComprador(CartaoDTO cartao, Comprador comprador,
            PagamentoRealizadoNoGatewayDTO pagamento)
        {

            return Domain.Pagamentos.CartaoApplicationService.GerarEntidadeCartaoDeComprador(cartao, comprador,
                 cartao.Validade.Split('/'), pagamento.Cartao.Bandeira);
        }

        private EnderecoDeCobranca RegistrarDadosDoEnderecoDeCobranca(int idCartao, EnderecoDeCobrancaDTO endereco)
        {
            var enderecoDTO = new EnderecoDTO()
            {
                Logradouro = endereco.Logradouro,
                Bairro = endereco.Bairro,
                CEP = endereco.CEP,
                Cidade = endereco.Cidade,
                Estado = endereco.Estado,
                Numero = endereco.Numero
            };

            var enderecoDeCobrancaDTO = new NovoEnderecoCobrancaDTO(idCartao, enderecoDTO);

            return Domain.Pagamentos.CartaoApplicationService.GerarEntidadeEnderecoDeCobranca(enderecoDeCobrancaDTO);
        }

        private CompradorGateway RegistrarDadosDoCompradorNoGateway(Gateway gateway, Comprador comprador, PagamentoRealizadoNoGatewayDTO pagamento)
        {
            return Domain.Pagamentos.CompradorApplicationService.GerarEntidadeCompradorNoGateway(comprador, gateway, pagamento.Comprador.IdNoGateway);
        }

        private Comprador RegistrarDadosDoComprador(DadosDoCompradorDTO comprador)
        {
            return Domain.Pagamentos.CompradorApplicationService.GerarEntidadeComprador(comprador);
        }

        public async Task<Resultado<bool>> EstornarPagamento(int idPagamento)
        {
            Pagamento pagamento = Domain.Pagamentos.PagamentoRepository
                    .Queryable()
                    .FirstOrDefault(p => p.IdPagamento == idPagamento);

            if (pagamento == null || pagamento.StatusPagamento != StatusPagamentoEnum.Pago)
                throw new BusinessException("O pagamento informado não pode ser estornado.");

            int idDoGatewayDoPagamento = Domain.Pagamentos.RecebedorCredenciadoRepository.ObterIdGatewayDoIdRecebedorCredenciado(pagamento.IdRecebedorCredenciado);

            bool gatewayOriginalDoRecebedorEstahAtivo = Domain.Pagamentos.RecebedorCredenciadoRepository.VerificarSeGatewayDoRecebedorEstahAtivo(pagamento.IdRecebedorCredenciado);

            if (!gatewayOriginalDoRecebedorEstahAtivo)
            {
                if (idDoGatewayDoPagamento == (int)GatewayEnum.Zoop)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Não será possível realizar o estorno de transações feitas por meio do Pagamento Online com essa funcionalidade desativada.");
                }
                else
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Não será possível realizar o estorno de transações feitas por meio do Link de Pagamento com essa funcionalidade desativada.");
                }

                return Resultado.Falhar<bool>("Falha no estorno devido a troca de gateways.");
            }

            RecebedorCredenciado recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Load(pagamento.IdRecebedorCredenciado);

            GatewayEnum gatewayDoPagamento = (GatewayEnum)idDoGatewayDoPagamento;
            IGatewayPagamentoOnline gatewayService = GatewayPagamentoOnlineFactory.Create(gatewayDoPagamento, _pagamentoOnlineConfigurationProvider);

            var retornoDoGateway = await gatewayService.EstornarTransacao(new EstornarPagamentoDTO()
            {
                IdTransacaoNoGateway = pagamento.IdTransacaoGateway,
                ValorTotalDoPagamento = pagamento.Valor,
                IdDoRecebedorNoGateway = recebedorCredenciado.IdRecebedorNoGateway,
                IdCobrancaNoGateway = pagamento.IdCobrancaGateway,
            });

            if (!retornoDoGateway.Sucesso)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Ocorreu um erro ao estornar o fechamento de conta. Fale conosco através do WhatsApp pelo número +55 (21) 99748-6902.");
                LogService<PagamentosApplicationService>.Error($"[Pagamentos] Erro ao estornar transação no Gateway - {retornoDoGateway.Erro}");
                ErrorSignal.FromCurrentContext().Raise(new BusinessException($"[Pagamentos] Erro ao estornar transação no Gateway - {retornoDoGateway.Erro}"));
            }
            else
            {
                pagamento.DataUltimaAtualizacao = Calendario.Agora();
                pagamento.StatusPagamento = StatusPagamentoEnum.Estornado;
                pagamento.StatusGateway = retornoDoGateway.Valor;
                Domain.Pagamentos.PagamentoRepository.Update(pagamento);
            }

            return Resultado.Ok(retornoDoGateway.Sucesso);
        }

        private const int NumeroMaximoDeCaracteresDoTextoDeExibicaoNaFatura = 13;
        private string GerarNomeDeExibicaoNaFatura(string nomeDoRecebedor)
        {
            return nomeDoRecebedor.ToUpper().RemoverAcentos().SomenteLetrasENumeros()
                .Replace(" ", String.Empty)
                .Left(NumeroMaximoDeCaracteresDoTextoDeExibicaoNaFatura);
        }

        private void ValidarPrecisao(decimal valor, string nomeDoValor)
        {
            if (valor % 0.01m > 0)
            {
                LogService<PagamentosApplicationService>.Error($"O valor '{nomeDoValor}' tem precisão maior que duas casas decimais");
                throw new PrecisaoDoValorInvalidaException();
            }
        }

        private bool RecebedorTemLimiteDisponivel(int idRecebedor, decimal valorPagamento, bool ehRecorrencia)
        {
            if (ehRecorrencia) return true;

            var valorPagamentoEmCentavos = ConversaoMonetaria.ConverterReaisParaCentavos(valorPagamento);

            var valorUsoDiario = _cache.CacheGet(ChaveCacheTipo.ObterChaveLimiteDiarioPagamento(idRecebedor));
            var valorUsoMensal = _cache.CacheGet(ChaveCacheTipo.ObterChaveLimiteMensalPagamento(idRecebedor));

            var limitePagamentoRecebedor = Domain.Pagamentos.LimitePagamentoRecebedorRepository.ObterPorId(idRecebedor);

            var temLimitePagamentoDiario = RecebedorTemLimiteDiarioDisponivel(idRecebedor, valorUsoDiario, valorPagamentoEmCentavos, limitePagamentoRecebedor.Limite.LimitePagamentoDiarioEmCentavos);
            var temLimitePagamentoMensal = RecebedorTemLimiteMensalDisponivel(idRecebedor, valorUsoMensal, valorPagamentoEmCentavos, limitePagamentoRecebedor.Limite.LimitePagamentoMensalEmCentavos);

            return temLimitePagamentoDiario && temLimitePagamentoMensal;
        }

        private bool RecebedorTemLimiteDiarioDisponivel(int idRecebedor, string valorUsoDiario, int valorPagamentoEmCentavos, int limiteDiarioEmCentavos)
        {
            var temValorEmCache = !string.IsNullOrEmpty(valorUsoDiario);

            var valorUsado = temValorEmCache ? int.Parse(valorUsoDiario) : 0;

            if (!temValorEmCache)
            {
                var usoDiario = Domain.Pagamentos.UsoLimitePagamentoDiarioRecebedorRepository.ObterPorIdRecebedorEReferencia(idRecebedor, Calendario.Agora());

                valorUsado = usoDiario != null ? usoDiario.ValorDiarioUsadoEmCentavos : valorUsado;
            }

            var temLimiteDiario = CalculosLimite.TemLimite(valorPagamentoEmCentavos, valorUsado, limiteDiarioEmCentavos);

            return temLimiteDiario;
        }

        private bool RecebedorTemLimiteMensalDisponivel(int idRecebedor, string valorUsoMensal, int valorPagamentoEmCentavos, int limiteMensalEmCentavos)
        {
            var temValorEmCache = !string.IsNullOrEmpty(valorUsoMensal);

            var valorUsado = temValorEmCache ? int.Parse(valorUsoMensal) : 0;

            if (!temValorEmCache)
            {
                var usoMensal = Domain.Pagamentos.UsoLimitePagamentoMensalRecebedorRepository.ObterPorIdRecebedorEReferencia(idRecebedor, Calendario.Agora());

                valorUsado = usoMensal != null ? usoMensal.ValorMensalUsadoEmCentavos : valorUsado;
            }

            var temLimiteDiario = CalculosLimite.TemLimite(valorPagamentoEmCentavos, valorUsado, limiteMensalEmCentavos);

            return temLimiteDiario;
        }

        private void IncluirNaFilaProcessamentoLimite(NovoPagamentoDTO novoPagamento, Resultado<NovoPagamentoNoGateway> retorno)
        {
            var valorPago = ConversaoMonetaria.ConverterReaisParaCentavos(retorno.Valor.ValorPago);
            var dataPagamento = retorno.Valor.DataPagamento ?? Calendario.Agora();
            Domain.Pagamentos.DisparoMensagensConsumoLimitePagamentoService.IncluirNaFila(novoPagamento.IdRecebedor, retorno.Valor.IdPagamento, valorPago, dataPagamento);
        }
    }
}