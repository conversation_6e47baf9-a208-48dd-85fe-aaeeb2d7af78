using Castle.ActiveRecord;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pagamentos
{
    [ActiveRecord("Pagamento", Schema = "Pagamentos", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public partial class Pagamento : ActiveRecordBase<Pagamento>
    {

        public Pagamento()
        {
            Itens = new List<ItemPagamento>();
            RegrasDeSplit = new List<SplitPagamento>();
            StatusPagamento = StatusPagamentoEnum.AguardandoConfirmacaoDePagamento;
            DataCriacao = Calendario.Agora();
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_pagamento", ColumnType = "Int32")]
        public virtual int IdPagamento { get; set; }

        [Property("id_recebedor_credenciado")]
        public virtual int IdRecebedorCredenciado { get; set; }

        [Property("id_comprador_gateway_cartao")]
        public virtual int? IdCompradorGatewayCartao { get; set; }

        [Property("valor")]
        public virtual decimal Valor { get; set; }

        [Property("quantidade_parcelas")]
        public virtual int QuantidadeParcelas { get; set; }

        [Property("status_pagamento")]
        public virtual StatusPagamentoEnum StatusPagamento { get; set; }

        [Property("dt_pagamento", NotNull = false)]
        public virtual DateTime? DataPagamento { get; set; }

        [Property("nome_exibicao_fatura", NotNull = false)]
        public virtual string NomeDeExibicaoNaFatura { get; set; }

        [Property("pagamento_realizado", NotNull = false)]
        public virtual bool PagamentoRealizado { get; set; }

        [Property("status_gateway", NotNull = false)]
        public virtual string StatusGateway { get; set; }

        [Property("motivo_status_gateway", NotNull = false)]
        public virtual string MotivoStatusGateway { get; set; }

        [Property("motivo_rejeicao_gateway", NotNull = false)]
        public virtual string MotivoRejeicaoGateway { get; set; }

        [Property("id_transacao_gateway", NotNull = false)]
        public virtual string IdTransacaoGateway { get; set; }

        [Property("custo_transacao_gateway", NotNull = false)]
        public virtual decimal? CustoTransacaoGateway { get; set; }

        [Property("dt_previsao_recebimento")]
        public virtual DateTime? DataPrevisaoRecebimento { get; set; }

        [Property("dt_criacao")]
        public virtual DateTime DataCriacao { get; set; }

        [Property("dt_ultima_atualizacao", NotNull = false)]
        public virtual DateTime? DataUltimaAtualizacao { get; set; }

        [HasMany(typeof(ItemPagamento), ColumnKey = "id_pagamento", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan,
            Schema = "Pagamentos", Table = "Pagamento_Item", Inverse = true, Lazy = true)]
        public virtual IList<ItemPagamento> Itens { get; set; }

        [HasMany(typeof(SplitPagamento), ColumnKey = "id_pagamento", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan,
            Schema = "Pagamentos", Table = "Pagamento_Split", Inverse = true, Lazy = true)]
        public virtual IList<SplitPagamento> RegrasDeSplit { get; set; }
        
        [Property("id_cobranca_gateway")]
        public virtual string IdCobrancaGateway { get; set; }

        /// <summary>
        /// Código utilizado para referenciar, a partir de um pagamento, quem é o responsável por ele.
        /// Exemplo: código de referência ao link de pagamento que originou o pagamento.
        /// Esse campo se assemelha ao campo "code" do pedido na API do Pagar.me.
        /// Quem define como esse campo será utilizado é quem está gerando o pagamento. 
        /// Não devem ser enforçadas regras de negócio sobre esse campo.
        /// </summary>
        [Property("referencia_origem")]
        public virtual string ReferenciaDeOrigem { get; set; }

        [Property("metodo_pagamento")]
        public virtual MetodoDePagamentoNoGatewayEnum? MetodoPagamento { get; set; }

        #region Métodos públicos
        public virtual void RegistrarDadosDaTransacao(bool pagamentoFoiRealizado, string idTransacaoGateway,
            string nomeDeExibicaoNaFatura, string statusGateway, string motivoStatusGateway,
            string motivoRejeicaoGateway, decimal? custoTransacao, DateTime? dataHoraPagamento,
            DateTime? dataPrevisaoRecebimento, string idCobrancaGateway)
        {

            this.PagamentoRealizado = pagamentoFoiRealizado;
            this.IdTransacaoGateway = idTransacaoGateway;
            this.NomeDeExibicaoNaFatura = nomeDeExibicaoNaFatura;
            this.StatusGateway = statusGateway;
            this.MotivoStatusGateway = motivoStatusGateway;
            this.MotivoRejeicaoGateway = motivoRejeicaoGateway;
            this.CustoTransacaoGateway = custoTransacao;
            this.DataPagamento = dataHoraPagamento;
            this.DataPrevisaoRecebimento = dataPrevisaoRecebimento;

            this.StatusPagamento = pagamentoFoiRealizado ? StatusPagamentoEnum.Pago : StatusPagamentoEnum.Negado;
            this.DataUltimaAtualizacao = Calendario.Agora();

            this.IdCobrancaGateway = idCobrancaGateway;
        }

        public virtual void RegistrarDadosDaTransacao(string idTransacaoGateway,
          string statusGateway, string motivoStatusGateway,
         string motivoRejeicaoGateway, decimal? custoTransacao, DateTime? dataHoraPagamento,
         DateTime? dataPrevisaoRecebimento)
        {

            this.IdTransacaoGateway = idTransacaoGateway;
            this.StatusGateway = statusGateway;
            this.MotivoStatusGateway = motivoStatusGateway;
            this.MotivoRejeicaoGateway = motivoRejeicaoGateway;
            this.CustoTransacaoGateway = custoTransacao;
            this.DataPagamento = dataHoraPagamento;
            this.DataPrevisaoRecebimento = dataPrevisaoRecebimento;
            this.DataUltimaAtualizacao = Calendario.Agora();

        }

        public virtual void RegistrarConfirmacacaoDePagamento(DadosDePagamentoNoGateway dadosNoGateway)
        {
            PagamentoRealizado = true;
            StatusGateway = dadosNoGateway.Status;
            StatusPagamento = StatusPagamentoEnum.Pago;
            DataUltimaAtualizacao = Calendario.Agora();

            DataPagamento = dadosNoGateway.DataDoPagamento;
            CustoTransacaoGateway = dadosNoGateway.ValorEmCentavos / 100m;
        }

        public virtual void RegistrarCancelamento(DadosDePagamentoNoGateway dadosNoGateway)
        {
            PagamentoRealizado = false;
            StatusGateway = dadosNoGateway.Status;
            StatusPagamento = StatusPagamentoEnum.Cancelado;
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual bool FoiPago()
        {
            return StatusPagamento == StatusPagamentoEnum.Pago;
        }

        #endregion
    }
}