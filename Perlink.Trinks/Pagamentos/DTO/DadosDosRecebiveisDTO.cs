﻿using Perlink.Pagamentos.Gateways.DTO;
using System;

namespace Perlink.Trinks.Pagamentos.DTO
{
    public class DadosDosRecebiveisDTO
    {
        public DadosDosRecebiveisDTO() { }

        public DadosDosRecebiveisDTO(RecebivelDTO recebivel, DadosDosRecebiveisDTO dados)
        {
            DataRecebimento = recebivel.DataDePagamento;
            NomeItem = dados.NomeItem;
            NomeCliente = dados.NomeCliente;
            ValorAReceber = recebivel.Valor;
            IdAntecipacao = recebivel.IdAntecipacao;
            Status = recebivel.Status;
            IdFormaPagamento = dados.IdFormaPagamento;
            Tipo = dados.Tipo;
        }

        public string IdTransacaoGateway { get; set; }
        public string IdAntecipacao { get; set; }
        public string NomeCliente { get; set; }
        public string NomeItem { get; set; }
        public decimal ValorAReceber { get; set; }
        public DateTime? DataRecebimento { get; set; }
        public string Status { get; set; }
        public int IdFormaPagamento { get; set; }
        public string Tipo { get; set; }
    }
}