﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.Pagamentos.Enums;
using System.Collections.Generic;

namespace Perlink.Trinks.Pagamentos.DTO
{
    public sealed class NovoPagamentoDTO
    {
        public NovoPagamentoDTO()
        {
            Itens = new List<ItemNovoPagamentoDTO>();
            RegrasDeSplit = new List<SplitPagamentoDTO>();
        }

        public MetodoDePagamentoNoGatewayEnum MetodoDePagamento { get; set; }
        public OrigemDePagamentoEnum OrigemDePagamento { get; set; }
        public int IdRecebedor { get; set; }
        public string ReferenciaDeOrigem { get; set; }
        public int IdCartaoDeComprador { get; set; }
        public decimal Valor { get; set; }
        public int? QuantidadeParcelas { get; set; }
        public List<ItemNovoPagamentoDTO> Itens { get; set; }
        public List<SplitPagamentoDTO> RegrasDeSplit { get; set; }
        public CartaoDTO Cartao { get; internal set; }
        public DadosDoCompradorDTO Comprador { get; internal set; }
        public int IdEstabelecimento { get; internal set; }
        public EnderecoDeCobrancaDTO EnderecoDeCobranca { get; set; }
    }

    public class NovoPedidoPixDTO
    {
        public int TempoDeExpiracao { get; set; }
        public NovoPagamentoDTO DadosDoPagamento { get; set; }
    }

    public sealed class ItemNovoPagamentoDTO
    {
        public string Nome { get; set; }
        public decimal Valor { get; set; }
    }

    public sealed class SplitPagamentoDTO
    {
        public int IdRecebedor { get; set; }
        public bool ResponsavelPelasTaxasDaOperacao { get; set; }
        public bool ResponsavelPeloChargeback { get; set; }
        public bool ResponsavelPeloRestoDaDivisaoDeTaxas { get; set; }
        public decimal Valor { get; set; }
    }

    public class PixDTO
    {
        public PixDTO(int tempoDeExpiracaoSegs)
        {
            TempoDeExpiracaoSegs = tempoDeExpiracaoSegs;
        }

        public int TempoDeExpiracaoSegs { get; set; }
    }

    public class EnderecoDeCobrancaDTO
    {
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string CEP { get; set; }
        public string Pais { get; set; }
        public string Estado { get; set; }
        public string Cidade { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
    }
}