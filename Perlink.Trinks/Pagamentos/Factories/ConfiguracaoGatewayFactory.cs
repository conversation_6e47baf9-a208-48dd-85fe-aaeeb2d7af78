﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.Config;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pagamentos.Factories
{
    public static class ConfiguracaoGatewayFactory
    {
        public static IConfiguracoesGateway ObterConfiguracoesDeAcordoComOGateway(GatewayEnum gateway, MetodoDePagamentoNoGatewayEnum metodoDePagamento = MetodoDePagamentoNoGatewayEnum.CartaoDeCredito)
        {
            if (_configuracoesFactories.TryGetValue((gateway, metodoDePagamento), out Func<IConfiguracoesGateway> factory))
            {
                return factory();
            }

            throw new NotImplementedException($"Combinação de gateway e método de pagamento não suportada: {gateway}, {metodoDePagamento}");
        }
        
        private static readonly Dictionary<(GatewayEnum, MetodoDePagamentoNoGatewayEnum), Func<IConfiguracoesGateway>> _configuracoesFactories =
            new Dictionary<(GatewayEnum, MetodoDePagamentoNoGatewayEnum), Func<IConfiguracoesGateway>>
            {
                { (GatewayEnum.PagarMe, MetodoDePagamentoNoGatewayEnum.CartaoDeCredito), () => new ConfiguracoesPagarme() },
                { (GatewayEnum.PagarMeV5, MetodoDePagamentoNoGatewayEnum.CartaoDeCredito), () => new ConfiguracoesPagarMeV5Credito() },
                { (GatewayEnum.PagarMeV5, MetodoDePagamentoNoGatewayEnum.Pix), () => new ConfiguracoesPagarMeV5Pix() },
                { (GatewayEnum.Zoop, MetodoDePagamentoNoGatewayEnum.CartaoDeCredito), () => new ConfiguracoesZoop() },
            };
    }
}