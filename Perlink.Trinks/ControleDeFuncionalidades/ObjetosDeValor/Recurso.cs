using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor
{

    public class Recurso
    {

        public Recurso(RecursoEnum recursoEnum, ServicoAdicionalEnum? servicoAdicional)
        {
            IdRecurso = (int)recursoEnum;
            ServicoAdicional = servicoAdicional;
        }

        public int IdRecurso { get; private set; }
        public ServicoAdicionalEnum? ServicoAdicional { get; private set; }

        public bool EhUmRecursoQueEhUmServicoAdicional()
        {
            return ServicoAdicional != null;
        }

        private static Recurso lembretePremium;
        public static Recurso LembretePremium { get { return lembretePremium ?? (lembretePremium = new Recurso(RecursoEnum.Lembrete_Premium, ServicoAdicionalEnum.LembretePremium)); } }


        public static Recurso PesquisaComboClienteComTodasAsMascarasDeTelefone => new Recurso(RecursoEnum.Pesquisa_ComboCliente_Com_Todas_As_Mascaras_De_Telefone, null);

        private static Recurso pagamentoOnlineAntecipado;
        public static Recurso PagamentoOnlineAntecipado { get { return pagamentoOnlineAntecipado ?? (pagamentoOnlineAntecipado = new Recurso(RecursoEnum.Pagamento_Online_Antecipado, null)); } }

        private static Recurso realizarPagamentoOnlineAntecipado;
        public static Recurso RealizarPagamentoOnlineAntecipado { get { return realizarPagamentoOnlineAntecipado ?? (realizarPagamentoOnlineAntecipado = new Recurso(RecursoEnum.Realizar_Pagamento_Online_Antecipado, null)); } }

        private static Recurso ehNovoFrameBusca;
        public static Recurso EhNovoFrameBusca { get { return ehNovoFrameBusca ?? (ehNovoFrameBusca = new Recurso(RecursoEnum.Novo_Frame_Busca, null)); } }

        private static Recurso novoVisualBackoffice;
        public static Recurso EhNovoVisualBackoffice { get { return novoVisualBackoffice ?? (novoVisualBackoffice = new Recurso(RecursoEnum.Novo_Visual_Backoffice, null)); } }

        private static Recurso baixaAutomaticaEstoque;
        public static Recurso BaixaAutomaticaEstoque { get { return baixaAutomaticaEstoque ?? (baixaAutomaticaEstoque = new Recurso(RecursoEnum.Baixa_Automatica_Estoque, null)); } }

        private static Recurso debitoParcial;
        public static Recurso DebitoParcial { get { return debitoParcial ?? (debitoParcial = new Recurso(RecursoEnum.Debito_Parcial, null)); } }

        private static Recurso novoDesignFechamentoConta;
        public static Recurso NovoDesignFechamentoConta { get { return novoDesignFechamentoConta ?? (novoDesignFechamentoConta = new Recurso(RecursoEnum.Novo_Design_Fechamento_Conta, null)); } }

        private static Recurso formularioAnamnese;
        public static Recurso FormularioAnamnese { get { return formularioAnamnese ?? (formularioAnamnese = new Recurso(RecursoEnum.Formulario_Anamnese, null)); } }

        private static Recurso relatorioSimplificado;
        public static Recurso RelatorioSimplificado { get { return relatorioSimplificado ?? (relatorioSimplificado = new Recurso(RecursoEnum.Relatorio_Simplificado, null)); } }

        private static Recurso fichaDoCliente;
        public static Recurso FichaDoCliente { get { return fichaDoCliente ?? (fichaDoCliente = new Recurso(RecursoEnum.Ficha_do_Cliente, null)); } }

        private static Recurso acessoAoPainelDeAtendimentoPeloAppTrinksProfissional;
        public static Recurso AcessoAoPainelDeAtendimentoPeloAppTrinksProfissional { get { return acessoAoPainelDeAtendimentoPeloAppTrinksProfissional ?? (acessoAoPainelDeAtendimentoPeloAppTrinksProfissional = new Recurso(RecursoEnum.Acesso_Ao_Painel_De_Atendimento_Pelo_App_Trinks_Profissional, null)); } }

        private static Recurso administrarAppProfissional;
        public static Recurso AdministrarAppProfissional { get { return administrarAppProfissional ?? (administrarAppProfissional = new Recurso(RecursoEnum.AdministrarAppProfissinal, null)); } }

        private static Recurso gorjeta;
        public static Recurso Gorjeta { get { return gorjeta ?? (gorjeta = new Recurso(RecursoEnum.Gorjeta, null)); } }

        private static Recurso fecharContaAppTrinksProfissional;
        public static Recurso FecharContaAppTrinksProfiossional { get { return fecharContaAppTrinksProfissional ?? (fecharContaAppTrinksProfissional = new Recurso(RecursoEnum.Fechar_Conta_App_Trinks_Profissional, null)); } }

        private static Recurso conciliacaoSiclos;
        public static Recurso ConciliacaoSiclos { get { return conciliacaoSiclos ?? (conciliacaoSiclos = new Recurso(RecursoEnum.ConciliacaoSiclos, null)); } }

        private static Recurso utilizarBelezinhaFechamentoTrinksProfissional;
        public static Recurso UtilizarBelezinhaFechamentoTrinksProfissional { get { return utilizarBelezinhaFechamentoTrinksProfissional ?? (utilizarBelezinhaFechamentoTrinksProfissional = new Recurso(RecursoEnum.Utilizar_Belezinha_Fechamento_Trinks_Profissional, null)); } }

        private static Recurso assinaturaDigital;
        public static Recurso AssinaturaDigital { get { return assinaturaDigital ?? (assinaturaDigital = new Recurso(RecursoEnum.Assinatura_Digital, null)); } }

        private static Recurso botaoNovoMenu;
        public static Recurso BotaoNovoMenu { get { return botaoNovoMenu ?? (botaoNovoMenu = new Recurso(RecursoEnum.Botao_Novo_Menu, null)); } }

        private static Recurso zoomAgenda;
        public static Recurso ZoomAgenda { get { return zoomAgenda ?? (zoomAgenda = new Recurso(RecursoEnum.Zoom_Na_Agenda, null)); } }

        private static Recurso utilizarCanalDeAjudaTrinksProfissional;
        public static Recurso UtilizarCanalDeAjudaTrinksProfissional { get { return utilizarCanalDeAjudaTrinksProfissional ?? (utilizarCanalDeAjudaTrinksProfissional = new Recurso(RecursoEnum.Utilizar_Canal_De_Ajuda_Trinks_Profissional, null)); } }

        private static Recurso surveyAppTrinksProfissional;
        public static Recurso SurveyAppTrinksProfissional { get { return surveyAppTrinksProfissional ?? (surveyAppTrinksProfissional = new Recurso(RecursoEnum.Survey_App_Trinks_Profissional, null)); } }

        private static Recurso rankingPacotes;
        public static Recurso RankingPacotes { get { return rankingPacotes ?? (rankingPacotes = new Recurso(RecursoEnum.Ranking_Pacotes, null)); } }

        private static Recurso appExclusivo;
        public static Recurso AppExclusivo { get { return appExclusivo ?? (appExclusivo = new Recurso(RecursoEnum.App_Exclusivo, null)); } }

        private static Recurso ativacaoAutomaticaDoProgramaDeFidelidade;
        public static Recurso AtivacaoAutomaticaDoProgramaDeFidelidade { get { return ativacaoAutomaticaDoProgramaDeFidelidade ?? (ativacaoAutomaticaDoProgramaDeFidelidade = new Recurso(RecursoEnum.AtivacaoAutomaticaDoProgramaDeFidelidade, null)); } }

        private static Recurso surveyTrinksMobile;
        public static Recurso SurveyTrinksMobile { get { return surveyTrinksMobile ?? (surveyTrinksMobile = new Recurso(RecursoEnum.Survey_Trinks_Mobile, null)); } }

        private static Recurso telaComRelatoriosPrincipais;
        public static Recurso TelaComRelatoriosPrincipais { get { return telaComRelatoriosPrincipais ?? (telaComRelatoriosPrincipais = new Recurso(RecursoEnum.TelaComRelatoriosPrincipais, null)); } }

        private static Recurso configuracoesBasicas;
        public static Recurso ConfiguracoesBasicas { get { return configuracoesBasicas ?? (configuracoesBasicas = new Recurso(RecursoEnum.Configuracoes_Basicas, null)); } }

        private static Recurso home;
        public static Recurso Home { get { return home ?? (home = new Recurso(RecursoEnum.Home, null)); } }
        private static Recurso assinaturaRecorrente;
        public static Recurso AssinaturaRecorrente { get { return assinaturaRecorrente ?? (assinaturaRecorrente = new Recurso(RecursoEnum.Assinatura_Recorrente, null)); } }

        private static Recurso melhoriasConsumoPacote;
        public static Recurso MelhoriasConsumoPacote { get { return melhoriasConsumoPacote ?? (melhoriasConsumoPacote = new Recurso(RecursoEnum.Melhorias_Consumo_Pacote, null)); } }



        private static Recurso contatoPorWhatsappNoProcessamentoDaBelezinha;
        public static Recurso ContatoPorWhatsappNoProcessamentoDaBelezinha { get { return contatoPorWhatsappNoProcessamentoDaBelezinha ?? (contatoPorWhatsappNoProcessamentoDaBelezinha = new Recurso(RecursoEnum.Contato_Por_Whatsapp_No_Processamento_Da_Belezinha, null)); } }

        private static Recurso contatoPorWhatsappNoSaibaMaisDoIncentivoAIntegracaoDaBelezinha;
        public static Recurso ContatoPorWhatsappNoSaibaMaisDoIncentivoAIntegracaoDaBelezinha { get { return contatoPorWhatsappNoSaibaMaisDoIncentivoAIntegracaoDaBelezinha ?? (contatoPorWhatsappNoSaibaMaisDoIncentivoAIntegracaoDaBelezinha = new Recurso(RecursoEnum.Contato_Por_Whatsapp_No_Saiba_Mais_Do_Incentivo_A_Integracao_Da_Belezinha, null)); } }

        private static Recurso contatoPorWhatsappAoSelecionarBelezinhaNoFechamentoDeConta;
        public static Recurso ContatoPorWhatsappAoSelecionarBelezinhaNoFechamentoDeConta { get { return contatoPorWhatsappAoSelecionarBelezinhaNoFechamentoDeConta ?? (contatoPorWhatsappAoSelecionarBelezinhaNoFechamentoDeConta = new Recurso(RecursoEnum.Contato_Por_Whatsapp_Ao_Selecionar_Belezinha_No_Fechamento_De_Conta, null)); } }

        private static Recurso questionarioParaEntenderInteressePeloAdicionalEstaAtivo;
        public static Recurso QuestionarioParaEntenderInteressePeloAdicionalEstaAtivo { get { return questionarioParaEntenderInteressePeloAdicionalEstaAtivo ?? (questionarioParaEntenderInteressePeloAdicionalEstaAtivo = new Recurso(RecursoEnum.Questionario_Para_Entender_Interesse_Pelo_Adicional_Esta_Ativo, null)); } }


        #region CTA's Belezinha

        private static Recurso ctaBelezinhaNuncaUsouIntegradoFechamentoDeConta;
        public static Recurso CtaBelezinhaNuncaUsouIntegradoFechamentoDeConta { get { return ctaBelezinhaNuncaUsouIntegradoFechamentoDeConta ?? (ctaBelezinhaNuncaUsouIntegradoFechamentoDeConta = new Recurso(RecursoEnum.CTA_Belezinha_Nunca_Usou_Integrado_Fechamento_De_Conta, null)); } }


        private static Recurso ctaBelezinhaParouDeUsarIntegradoFechamentoDeConta;
        public static Recurso CTADoPorqueNaoUsaABelezinhaNoFechamentoDeConta { get { return ctaBelezinhaParouDeUsarIntegradoFechamentoDeConta ?? (ctaBelezinhaParouDeUsarIntegradoFechamentoDeConta = new Recurso(RecursoEnum.CTA_Belezinha_Parou_De_Usar_Integrado_Fechamento_De_Conta, null)); } }


        private static Recurso ctaBelezinhaNuncaUsouIntegradoRelatorioFinanceiro;
        public static Recurso CtaBelezinhaNuncaUsouIntegradoRelatorioFinanceiro { get { return ctaBelezinhaNuncaUsouIntegradoRelatorioFinanceiro ?? (ctaBelezinhaNuncaUsouIntegradoRelatorioFinanceiro = new Recurso(RecursoEnum.CTA_Belezinha_Nunca_Usou_Integrado_Relatorio_Financeiro, null)); } }


        private static Recurso ctaBelezinhaParouDeUsarIntegradoRelatorioFinanceiro;
        public static Recurso CTADoPorqueNaoUsaABelezinhaNoRelatorioFinanceiro { get { return ctaBelezinhaParouDeUsarIntegradoRelatorioFinanceiro ?? (ctaBelezinhaParouDeUsarIntegradoRelatorioFinanceiro = new Recurso(RecursoEnum.CTA_Belezinha_Parou_De_Usar_Integrado_Relatorio_Financeiro, null)); } }


        private static Recurso ctaBelezinhaNuncaUsouIntegradoAgenda;
        public static Recurso CtaBelezinhaNuncaUsouIntegradoAgenda { get { return ctaBelezinhaNuncaUsouIntegradoAgenda ?? (ctaBelezinhaNuncaUsouIntegradoAgenda = new Recurso(RecursoEnum.CTA_Belezinha_Nunca_Usou_Integrado_Agenda, null)); } }


        private static Recurso ctaBelezinhaParouDeUsarIntegradoAgenda;
        public static Recurso CTADoPorqueNaoUsaABelezinhaNoAgenda { get { return ctaBelezinhaParouDeUsarIntegradoAgenda ?? (ctaBelezinhaParouDeUsarIntegradoAgenda = new Recurso(RecursoEnum.CTA_Belezinha_Parou_De_Usar_Integrado_Agenda, null)); } }



        private static Recurso ctaBelezinhaNuncaUsouIntegradoRelatorioComissoes;
        public static Recurso CtaBelezinhaNuncaUsouIntegradoRelatorioComissoes { get { return ctaBelezinhaNuncaUsouIntegradoRelatorioComissoes ?? (ctaBelezinhaNuncaUsouIntegradoRelatorioComissoes = new Recurso(RecursoEnum.CTA_Belezinha_Nunca_Usou_Integrado_Relatorio_De_Comissoes, null)); } }


        private static Recurso ctaBelezinhaParouDeUsarIntegradoRelatorioComissoes;
        public static Recurso CTADoPorqueNaoUsaABelezinhaNoRelatorioComissoes { get { return ctaBelezinhaParouDeUsarIntegradoRelatorioComissoes ?? (ctaBelezinhaParouDeUsarIntegradoRelatorioComissoes = new Recurso(RecursoEnum.CTA_Belezinha_Parou_De_Usar_Integrado_Relatorio_De_Comissoes, null)); } }

        private static Recurso ctaBelezinhaNuncaUsouIntegradoHome;
        public static Recurso CtaBelezinhaNuncaUsouIntegradoHome { get { return ctaBelezinhaNuncaUsouIntegradoHome ?? (ctaBelezinhaNuncaUsouIntegradoHome = new Recurso(RecursoEnum.CTA_Belezinha_Nunca_Usou_Integrado_Home, null)); } }


        private static Recurso ctaBelezinhaParouDeUsarIntegradoHome;
        public static Recurso CTADoPorqueNaoUsaABelezinhaNoHome { get { return ctaBelezinhaParouDeUsarIntegradoHome ?? (ctaBelezinhaParouDeUsarIntegradoHome = new Recurso(RecursoEnum.CTA_Belezinha_Parou_De_Usar_Integrado_Home, null)); } }


        private static Recurso ctaBelezinhaNuncaUsouIntegradoTrinksProHome;
        public static Recurso CtaBelezinhaNuncaUsouIntegradoTrinksProHome { get { return ctaBelezinhaNuncaUsouIntegradoTrinksProHome ?? (ctaBelezinhaNuncaUsouIntegradoTrinksProHome = new Recurso(RecursoEnum.CTA_Belezinha_Nunca_Usou_Integrado_TrinksPro_Home, null)); } }


        private static Recurso ctaBelezinhaParouDeUsarIntegradoTrinksProHome;
        public static Recurso CTADoPorqueNaoUsaABelezinhaNoTrinksProHome { get { return ctaBelezinhaParouDeUsarIntegradoTrinksProHome ?? (ctaBelezinhaParouDeUsarIntegradoTrinksProHome = new Recurso(RecursoEnum.CTA_Belezinha_Parou_De_Usar_Integrado_TrinksPro_Home, null)); } }


        #endregion


        private static Recurso formularioPMFAoFecharContaComBelezinha;
        public static Recurso FormularioPMFAoFecharContaComBelezinha { get { return formularioPMFAoFecharContaComBelezinha ?? (formularioPMFAoFecharContaComBelezinha = new Recurso(RecursoEnum.Formulario_PMF_Ao_Fechar_Conta_Com_Belezinha, null)); } }

        private static Recurso mapaDeCalor;
        public static Recurso MapaDeCalor { get { return mapaDeCalor ?? (mapaDeCalor = new Recurso(RecursoEnum.Mapa_de_Calor, null)); } }

        private static Recurso motivoDeCadastroAssertivo;
        public static Recurso MotivoDeCadastroAssertivo { get { return motivoDeCadastroAssertivo ?? (motivoDeCadastroAssertivo = new Recurso(RecursoEnum.Motivo_De_Cadastro_Assertivo, null)); } }

        private static Recurso controleDeEntradaESaida;
        public static Recurso ControleDeEntradaESaida { get { return controleDeEntradaESaida ?? (controleDeEntradaESaida = new Recurso(RecursoEnum.Controle_De_Entrada_E_Saida, null)); } }

        private static Recurso pagamentoFaturaPix;
        public static Recurso PagamentoFaturaPix { get { return pagamentoFaturaPix ?? (pagamentoFaturaPix = new Recurso(RecursoEnum.Pagamento_Fatura_Pix, null)); } }

        private static Recurso importadorDeDadosNoBackoffice;
        public static Recurso ImportadorDeDadosNoBackoffice { get { return importadorDeDadosNoBackoffice ?? (importadorDeDadosNoBackoffice = new Recurso(RecursoEnum.Importador_de_Dados_No_Backoffice, null)); } }


        private static Recurso integracaoFacebookBusiness;
        public static Recurso IntegracaoFacebookBusiness { get { return integracaoFacebookBusiness ?? (integracaoFacebookBusiness = new Recurso(RecursoEnum.Integracao_Facebook_Business, null)); } }

        private static Recurso redirecionarParaAHomeAoAcessarOBackoffice;
        public static Recurso RedirecionarParaAHomeAoAcessarOBackoffice { get { return redirecionarParaAHomeAoAcessarOBackoffice ?? (redirecionarParaAHomeAoAcessarOBackoffice = new Recurso(RecursoEnum.Redirecionar_Para_A_Home_Ao_Acessar_O_Backoffice, null)); } }

        private static Recurso retornoDeClientes;
        public static Recurso RetornoDeClientes { get { return retornoDeClientes ?? (retornoDeClientes = new Recurso(RecursoEnum.Retorno_de_Clientes, null)); } }

        private static Recurso linkDePagamento;
        public static Recurso LinkDePagamento { get { return linkDePagamento ?? (linkDePagamento = new Recurso(RecursoEnum.Link_de_Pagamento, null)); } }

        private static Recurso novasTelasDeRanking;
        public static Recurso NovasTelasRanking { get { return novasTelasDeRanking ?? (novasTelasDeRanking = new Recurso(RecursoEnum.Novas_Telas_Ranking, null)); } }

        private static Recurso gyraMaisIntegracao;
        public static Recurso GyraMaisIntegracao { get { return gyraMaisIntegracao ?? (gyraMaisIntegracao = new Recurso(RecursoEnum.Gyra_Mais_Integracao, null)); } }

        private static Recurso gyraMaisOferta;
        public static Recurso GyraMaisOferta { get { return gyraMaisOferta ?? (gyraMaisOferta = new Recurso(RecursoEnum.Gyra_Mais_Oferta, null)); } }

        private static Recurso storiesDeNovidades;
        public static Recurso StoriesDeNovidades { get { return storiesDeNovidades ?? (storiesDeNovidades = new Recurso(RecursoEnum.Stories_De_Novidades, null)); } }

        private static Recurso usarAutomaticamentePacoteDoClienteNoFechamentoDeConta;
        public static Recurso UsarAutomaticamentePacoteDoClienteNoFechamentoDeConta { get { return usarAutomaticamentePacoteDoClienteNoFechamentoDeConta ?? (usarAutomaticamentePacoteDoClienteNoFechamentoDeConta = new Recurso(RecursoEnum.Usar_Automaticamente_Pacote_Do_Cliente_No_Fechamento_De_Conta, null)); } }

        private static Recurso comunidadeTrinksTelaSugestao;
        public static Recurso ComunidadeTrinksTelaSugestao { get { return comunidadeTrinksTelaSugestao ?? (comunidadeTrinksTelaSugestao = new Recurso(RecursoEnum.Comunidade_Trinks_Tela_Sugestao, null)); } }

        private static Recurso todosOsPacotes;
        public static Recurso TodosOsPacotes { get { return todosOsPacotes ?? (todosOsPacotes = new Recurso(RecursoEnum.Novo_layout_Responsivo_Tela_Todos_Os_Pacotes, null)); } }
        private static Recurso contatoPorWhatsAppNoPopUpDeExperimentacaoDaNFS;
        public static Recurso ContatoPorWhatsAppNoPopUpDeExperimentacaoDaNFS { get { return contatoPorWhatsAppNoPopUpDeExperimentacaoDaNFS ?? (contatoPorWhatsAppNoPopUpDeExperimentacaoDaNFS = new Recurso(RecursoEnum.Contato_Por_Whatsapp_Popup_Aceitacao_Experimentacao_NFS, null)); } }
        private static Recurso cupom;
        public static Recurso Cupom { get { return cupom ?? (cupom = new Recurso(RecursoEnum.Cupom, null)); } }
        private static Recurso experimentacaoDaNFS;
        public static Recurso ExperimentacaoDaNFS { get { return experimentacaoDaNFS ?? (experimentacaoDaNFS = new Recurso(RecursoEnum.Experimentacao_NFS, null)); } }

        private static Recurso mapearPerfilDoEstabelecimento;
        public static Recurso MapearPerfilDoEstabelecimento { get { return mapearPerfilDoEstabelecimento ?? (mapearPerfilDoEstabelecimento = new Recurso(RecursoEnum.Mapear_Perfil_Do_Estabelecimento, null)); } }

        private static Recurso selecaoDeTrilha;
        public static Recurso SelecaoDeTrilha { get { return selecaoDeTrilha ?? (selecaoDeTrilha = new Recurso(RecursoEnum.Selecao_De_Trilha, null)); } }

        private static Recurso novaJornadaBelezinha;
        public static Recurso NovaJornadaBelezinha { get { return novaJornadaBelezinha ?? (novaJornadaBelezinha = new Recurso(RecursoEnum.Nova_Jornada_Belezinha, null)); } }

        private static Recurso modalProdutosUsadosNosServicos;
        public static Recurso ModalProdutosUsadosNosServicos { get { return modalProdutosUsadosNosServicos ?? (modalProdutosUsadosNosServicos = new Recurso(RecursoEnum.Modal_Produtos_Usados_Nos_Servicos, null)); } }

        private static Recurso cadastroDeServicoSimplificadoTrinksPro;
        public static Recurso CadastroDeServicoSimplificadoTrinksPro { get { return cadastroDeServicoSimplificadoTrinksPro ?? (cadastroDeServicoSimplificadoTrinksPro = new Recurso(RecursoEnum.Cadastro_De_Servico_Simplificado_TrinksPro, null)); } }

        private static Recurso novoLayoutDoCadastroDeDespesa;
        public static Recurso NovoLayoutDoCadastroDeDespesa { get { return novoLayoutDoCadastroDeDespesa ?? (novoLayoutDoCadastroDeDespesa = new Recurso(RecursoEnum.Novo_Layout_Do_Cadastro_De_Despesa, null)); } }

        private static Recurso aporte;
        public static Recurso Aporte { get { return aporte ?? (aporte = new Recurso(RecursoEnum.Aporte, null)); } }

        private static Recurso cardsAcoesFinanceirasMenuAcoesTrinksPro;
        public static Recurso CardsAcoesFinanceirasMenuAcoesTrinksPro { get { return cardsAcoesFinanceirasMenuAcoesTrinksPro ?? (cardsAcoesFinanceirasMenuAcoesTrinksPro = new Recurso(RecursoEnum.Cards_Acoes_Financeiras_Menu_Acoes_TrinksPro, null)); } }

        private static Recurso botaoFinanceiroNoMenuDeAcessoRapido;
        public static Recurso BotaoFinanceiroNoMenuDeAcessoRapido { get { return botaoFinanceiroNoMenuDeAcessoRapido ?? (botaoFinanceiroNoMenuDeAcessoRapido = new Recurso(RecursoEnum.Botao_Financeiro_No_Menu_De_Acesso_Rapido, null)); } }


        private static Recurso botaoFinanceiroNoMenuDeAcessoRapidoTrinksPro;
        public static Recurso BotaoFinanceiroNoMenuDeAcessoRapidoTrinksPro { get { return botaoFinanceiroNoMenuDeAcessoRapidoTrinksPro ?? (botaoFinanceiroNoMenuDeAcessoRapidoTrinksPro = new Recurso(RecursoEnum.Botao_Financeiro_No_Menu_De_Acesso_Rapido_TrinksPro, null)); } }

        private static Recurso despesasPessoais;
        public static Recurso DespesasPessoais { get { return despesasPessoais ?? (despesasPessoais = new Recurso(RecursoEnum.Despesas_Pessoais, null)); } }

        private static Recurso formularioPMFAoResgatarPontosFidelidade;
        public static Recurso FormularioPMFAoResgatarPontosFidelidade { get { return formularioPMFAoResgatarPontosFidelidade ?? (formularioPMFAoResgatarPontosFidelidade = new Recurso(RecursoEnum.Formulario_PMF_Ao_Fechar_Conta_Com_Resgate_Pontos_Fidelidade, null)); } }

        private static Recurso programaFidelidadeCTAEngajamento;
        public static Recurso ProgramaFidelidadeCTAEngajamento { get { return programaFidelidadeCTAEngajamento ?? (formularioPMFAoResgatarPontosFidelidade = new Recurso(RecursoEnum.Programa_Fidelidade_CTA_Engajamento, null)); } }

        private static Recurso bubble;
        public static Recurso Bubble { get { return bubble ?? (bubble = new Recurso(RecursoEnum.FloatingBubble, null)); } }

        private static Recurso solicitarCancelamentoDaAssinaturaPeloMeuPlano;
        public static Recurso SolicitarCancelamentoDaAssinaturaPeloMeuPlano { get { return solicitarCancelamentoDaAssinaturaPeloMeuPlano ?? (solicitarCancelamentoDaAssinaturaPeloMeuPlano = new Recurso(RecursoEnum.Solicitar_Cancelamento_Da_Assinatura_Pelo_Meu_Plano, null)); } }

        private static Recurso botaoDeAcessoAoMeuPlanoPeloTrinksPro;
        public static Recurso BotaoDeAcessoAoMeuPlanoPeloTrinksPro { get { return botaoDeAcessoAoMeuPlanoPeloTrinksPro ?? (botaoDeAcessoAoMeuPlanoPeloTrinksPro = new Recurso(RecursoEnum.Botao_De_Acesso_Ao_Meu_Plano_Pelo_TrinksPro, null)); } }

        private static Recurso ultimaVersaoCategoriasEGruposDeDespesas;
        public static Recurso UltimaVersaoCategoriasEGruposDeDespesas { get { return ultimaVersaoCategoriasEGruposDeDespesas ?? (ultimaVersaoCategoriasEGruposDeDespesas = new Recurso(RecursoEnum.Ultima_Versao_Categorias_E_Grupos_De_Despesas, null)); } }

        private static Recurso opcoesDeCancelamentoNoMenuAjuda;
        public static Recurso OpcoesDeCancelamentoNoMenuAjuda { get { return opcoesDeCancelamentoNoMenuAjuda ?? (opcoesDeCancelamentoNoMenuAjuda = new Recurso(RecursoEnum.Opcoes_De_Cancelamento_No_Menu_Ajuda, null)); } }

        private static Recurso separarFaixas1e2NoCadastroDeEstabelecimento;
        public static Recurso SepararFaixas1e2NoCadastroDeEstabelecimento { get { return separarFaixas1e2NoCadastroDeEstabelecimento ?? (separarFaixas1e2NoCadastroDeEstabelecimento = new Recurso(RecursoEnum.Separar_Faixas_1_e_2_No_Cadastro_De_Estabelecimento, null)); } }

        private static Recurso minhaContaChavesAcesso;
        public static Recurso MinhaContaChavesAcesso { get { return minhaContaChavesAcesso ?? (minhaContaChavesAcesso = new Recurso(RecursoEnum.Minha_Conta_Chaves_Acesso, null)); } }

        private static Recurso clubeDeAssinaturas;
        public static Recurso ClubeDeAssinaturas { get { return clubeDeAssinaturas ?? (clubeDeAssinaturas = new Recurso(RecursoEnum.Clube_De_Assinaturas, null)); } }

        private static Recurso avisoDeTerminoPeriodoGaratisAdicional;
        public static Recurso AvisoDeTerminoPeriodoGaratisAdicional
        {
            get { return avisoDeTerminoPeriodoGaratisAdicional ?? (avisoDeTerminoPeriodoGaratisAdicional = new Recurso(RecursoEnum.AvisoDeTerminoPeriodoGaratisAdicional, null)); }
        }

        private static Recurso tarefasOnboarding;
        public static Recurso TarefasOnboarding
        {
            get { return tarefasOnboarding ?? (tarefasOnboarding = new Recurso(RecursoEnum.Tarefas_Onboarding, null)); }
        }

        private static Recurso avisoDeTarefasDoOnboarding;
        public static Recurso AvisoDeTarefasDoOnboarding
        {
            get { return avisoDeTarefasDoOnboarding ?? (avisoDeTarefasDoOnboarding = new Recurso(RecursoEnum.Aviso_De_Tarefas_Onboarding, null)); }
        }

        private static Recurso novoMenuConfiguracoes;
        public static Recurso NovoMenuConfiguracoes
        {
            get { return novoMenuConfiguracoes ?? (novoMenuConfiguracoes = new Recurso(RecursoEnum.Novo_Menu_Configuracoes, null)); }
        }

        private static Recurso faceliftPopupAgendamento;
        public static Recurso FaceliftPopupAgendamento
        {
            get { return faceliftPopupAgendamento ?? (faceliftPopupAgendamento = new Recurso(RecursoEnum.Facelift_Popup_Agendamento, null)); }
        }

        private static Recurso historicoClienteImportado;
        public static Recurso HistoricoClienteImportado
        {
            get { return historicoClienteImportado ?? (historicoClienteImportado = new Recurso(RecursoEnum.Histrorico_De_Clientes_Importados, null)); }
        }

        private static Recurso cadastroV2;
        public static Recurso CadastroV2
        {
            get { return cadastroV2 ?? (cadastroV2 = new Recurso(RecursoEnum.CadastroV2, null)); }
        }

        private static Recurso backdoorParaEnderecoNoCadastro;
        public static Recurso BackdoorParaEnderecoNoCadastro
        {
            get { return backdoorParaEnderecoNoCadastro ?? (backdoorParaEnderecoNoCadastro = new Recurso(RecursoEnum.BackdoorParaEnderecoNoCadastro, null)); }
        }

        private static Recurso landingPageIntegracaoWhatsApp;
        public static Recurso LandingPageIntegracaoWhatsApp
        {
            get { return landingPageIntegracaoWhatsApp ?? (landingPageIntegracaoWhatsApp = new Recurso(RecursoEnum.Lading_Page_De_Integracao_Com_WhatsApp, null)); }
        }

        private static Recurso precoLandingPageIntegracaoWhatsApp;
        public static Recurso PrecoLandingPageIntegracaoWhatsApp
        {
            get { return precoLandingPageIntegracaoWhatsApp ?? (precoLandingPageIntegracaoWhatsApp = new Recurso(RecursoEnum.Preco_Lading_Page_De_Integracao_Com_WhatsApp, null)); }
        }

        private static Recurso unificacaoDeProfissionalPorEmail;
        public static Recurso UnificacaoDeProfissionalPorEmail { get { return unificacaoDeProfissionalPorEmail ?? (unificacaoDeProfissionalPorEmail = new Recurso(RecursoEnum.unificacao_De_Profissional_Por_Email, null)); } }

        private static Recurso pularSegundaValidacaoEmailFluxoCadastroCelulares;
        public static Recurso PularSegundaValidacaoEmailFluxoCadastroCelulares
        {
            get { return pularSegundaValidacaoEmailFluxoCadastroCelulares ?? (pularSegundaValidacaoEmailFluxoCadastroCelulares = new Recurso(RecursoEnum.PularSegundaValidacaoEmailFluxoCadastroCelulares, null)); }
        }

        private static Recurso ctaComissaoCadastroDeProfissional;
        public static Recurso CtaComissaoCadastroDeProfissional
        {
            get { return ctaComissaoCadastroDeProfissional ?? (ctaComissaoCadastroDeProfissional = new Recurso(RecursoEnum.CTA_Comissao_Cadastro_Profissional, null)); }
        }

        private static Recurso podeACessarIndicaTrinks;
        public static Recurso PodeAcessarIndicaTrinks
        {
            get { return podeACessarIndicaTrinks ?? (podeACessarIndicaTrinks = new Recurso(RecursoEnum.Pode_Acessar_O_Indica_Trinks, null)); }
        }


        private static Recurso testeABWhyTrinksEstaAtivo;
        public static Recurso TesteABWhyTrinksEstaAtivo
        {
            get { return testeABWhyTrinksEstaAtivo ?? (testeABWhyTrinksEstaAtivo = new Recurso(RecursoEnum.Teste_AB_Whytrinks_ativo, null)); }
        }

        private static Recurso permiteAcessoAoCadastroClienteResponsivo;
        public static Recurso PermiteAcessoAoCadastroClienteResponsivo
        {
            get { return permiteAcessoAoCadastroClienteResponsivo ?? (permiteAcessoAoCadastroClienteResponsivo = new Recurso(RecursoEnum.Permite_Acessar_Modal_De_Cadastro_Cliente_Responsiva, null)); }
        }

        private static Recurso podeAcessarConviteRetorno;

        public static Recurso PodeAcessarConviteRetorno
        {
            get
            {
                return podeAcessarConviteRetorno ?? (
                    podeAcessarConviteRetorno = new Recurso(RecursoEnum.Pode_Acessar_Convite_Retorno, null)
                );
            }
        }

        private static Recurso compartilhamentoDePacotes;
        public static Recurso CompartilhamentoDePacotes
        {
            get { return compartilhamentoDePacotes ?? (compartilhamentoDePacotes = new Recurso(RecursoEnum.Compartilhamento_De_Pacotes, null)); }
        }

        private static Recurso podeConfigurarEnvioIndependenteFluxoRotinaMensagensWpp;
        public static Recurso PodeConfigurarEnvioIndependenteFluxoRotinaMensagensWpp
        {
            get
            {
                return podeConfigurarEnvioIndependenteFluxoRotinaMensagensWpp ?? (
                    podeConfigurarEnvioIndependenteFluxoRotinaMensagensWpp = new Recurso(RecursoEnum.Pode_Configurar_Envio_Independente_Fluxo_Rotina_Mensagens_Wpp, null)
                );
            }
        }

        private static Recurso exibirColetaFeedbackRotinaMensagens;
        public static Recurso ExibirColetaFeedbackRotinaMensagens
        {
            get
            {
                return exibirColetaFeedbackRotinaMensagens ?? (
                    exibirColetaFeedbackRotinaMensagens = new Recurso(RecursoEnum.Exibir_Coleta_Feedback_Rotina_Mensagens, null)
                );
            }
        }

        private static Recurso usarCnpjClienteNfce;
        public static Recurso UsarCnpjClienteNfce
        {
            get { return usarCnpjClienteNfce ?? (usarCnpjClienteNfce = new Recurso(RecursoEnum.Pode_Usar_Cnpj_Do_Cliente_Em_Emissao_Nfce, null)); }
        }

        private static Recurso usarCnpjClienteNfse;
        public static Recurso UsarCnpjClienteNfse
        {
            get { return usarCnpjClienteNfse ?? (usarCnpjClienteNfse = new Recurso(RecursoEnum.Pode_Usar_Cnpj_Do_Cliente_Em_Emissao_Nfse, null)); }
        }


        private static Recurso liberarEmissaoNfcPagamentoOnline;
        public static Recurso LiberarEmissaoNfcPagamentoOnline
        {
            get { return liberarEmissaoNfcPagamentoOnline ?? (liberarEmissaoNfcPagamentoOnline = new Recurso(RecursoEnum.Liberar_Emissao_Nfc_Pagamento_Online, null)); }
        }


        private static Recurso rejeitarLoteRPS;
        public static Recurso RejeitarLoteRPS
        {
            get { return rejeitarLoteRPS ?? (rejeitarLoteRPS = new Recurso(RecursoEnum.Rejeitar_Lote_RPS, null)); }
        }

        private static Recurso reenviarLoteRejeitadoAutomaticamente;
        public static Recurso ReenviarLoteRejeitadoAutomaticamente
        {
            get { return reenviarLoteRejeitadoAutomaticamente ?? (reenviarLoteRejeitadoAutomaticamente = new Recurso(RecursoEnum.Reenviar_Lote_Rejeitado_Automaticamente, null)); }
        }

        private static Recurso dividirLoteNfse;
        public static Recurso DividirLoteNfse
        {
            get { return dividirLoteNfse ?? (dividirLoteNfse = new Recurso(RecursoEnum.Dividir_Lote_Nfse, null)); }
        }

        private static Recurso permiteCadastrarTelefoneInternacionalNoCadastroCliente;
        public static Recurso PermiteCadastrarTelefoneInternacionalNoCadastroCliente
        {
            get { return permiteCadastrarTelefoneInternacionalNoCadastroCliente ?? (permiteCadastrarTelefoneInternacionalNoCadastroCliente = new Recurso(RecursoEnum.Permite_Cadastrar_Telefone_Internacional_No_Cadastro_Cliente, null)); }
        }

        private static Recurso lancamentoDePagamentoDeProfissionais;
        public static Recurso LancamentoDePagamentoDeProfissionais
        {
            get
            {
                return lancamentoDePagamentoDeProfissionais ?? (
                    lancamentoDePagamentoDeProfissionais = new Recurso(RecursoEnum.Lancamento_De_Pagamento_De_Profissionais, null)
                );
            }
        }

        private static Recurso cancelamentoAutomaticoViaRotinaMensagens;
        public static Recurso CancelamentoAutomaticoViaRotinaMensagens
        {
            get
            {
                return cancelamentoAutomaticoViaRotinaMensagens ?? (
                    cancelamentoAutomaticoViaRotinaMensagens = new Recurso(RecursoEnum.Cancelamento_Automatico_Via_Rotina_Mensagens, null)
                );
            }
        }

        private static Recurso podeConfigurarTipoEnvioConfirmacoesWpp;
        public static Recurso PodeConfigurarTipoEnvioConfirmacoesWpp
        {
            get
            {
                return podeConfigurarTipoEnvioConfirmacoesWpp ?? (
                    podeConfigurarTipoEnvioConfirmacoesWpp = new Recurso(RecursoEnum.Pode_Configurar_Tipo_Envio_Confirmacoes_Wpp, null)
                );
            }
        }

        private static Recurso compraRecorrenteWhatsApp;
        public static Recurso CompraRecorrenteWhatsApp
        {
            get
            {
                return compraRecorrenteWhatsApp ?? (
                    compraRecorrenteWhatsApp = new Recurso(RecursoEnum.Compra_Recorrente_WhatsApp, null)
                );
            }
        }

        private static Recurso exibirColetaFeedbackConviteRetorno;
        public static Recurso ExibirColetaFeedbackConviteRetorno
        {
            get
            {
                return exibirColetaFeedbackConviteRetorno ?? (
                    exibirColetaFeedbackConviteRetorno = new Recurso(RecursoEnum.Exibir_Coleta_Feedback_Convite_Retorno, null)
                );
            }
        }

        private static Recurso relatoriosRetorno;
        public static Recurso RelatoriosRetorno
        {
            get
            {
                return relatoriosRetorno ?? (
                    relatoriosRetorno = new Recurso(RecursoEnum.Relatorios_Retorno, null)
                );
            }
        }

        private static Recurso observacoesAgendamentoAMostraNaAgendaPorCliente;
        public static Recurso ObservacoesAgendamentoAMostraNaAgendaPorCliente
        {
            get
            {
                return observacoesAgendamentoAMostraNaAgendaPorCliente ?? (
                    observacoesAgendamentoAMostraNaAgendaPorCliente = new Recurso(RecursoEnum.Observacoes_Agendamento_A_Mostra_Na_Agenda_Por_Cliente, null)
                );
            }
        }

        private static Recurso descontosPersonalizados;

        public static Recurso DescontosPersonalizados
        {
            get
            {
                return descontosPersonalizados ?? (
                    descontosPersonalizados = new Recurso(RecursoEnum.Descontos_Personalizados, null)
                    );
            }
        }

        private static Recurso contaDigital;
        public static Recurso ContaDigital
        {
            get
            {
                return contaDigital ?? (
                    contaDigital = new Recurso(RecursoEnum.ContaDigital, null)
                );
            }
        }

        private static Recurso relatorioConsumoDePacotes;
        public static Recurso RelatorioDeConsumoDePacotes
        {
            get { return relatorioConsumoDePacotes ?? (relatorioConsumoDePacotes = new Recurso(RecursoEnum.Relatorio_Consumo_De_Pacotes, null)); }
        }

        private static Recurso esconderAbaClientesEmAtendimento;
        public static Recurso EsconderAbaClientesEmAtendimento
        {
            get
            {
                return esconderAbaClientesEmAtendimento ?? (
                    esconderAbaClientesEmAtendimento = new Recurso(RecursoEnum.Esconder_Aba_Clientes_Em_Atendimento, null)
                );
            }
        }

        private static Recurso regularizacaoPagamentoAssinaturaAnualSemestral;
        public static Recurso RegularizacaoPagamentoAssinaturaAnualSemestral
        {
            get
            {
                return regularizacaoPagamentoAssinaturaAnualSemestral ??
                    (regularizacaoPagamentoAssinaturaAnualSemestral =
                    new Recurso(RecursoEnum.Regularizacao_Pagamento_Assinatura_Anual_Semestral, null));
            }
        }
        private static Recurso _removerOpcaoProfissionalUmDoCadastroDeProfissionais;
        public static Recurso RemoverOpcaoProfissionalUmDoCadastroDeProfissionais =>
            _removerOpcaoProfissionalUmDoCadastroDeProfissionais ?? (
                _removerOpcaoProfissionalUmDoCadastroDeProfissionais = new Recurso(RecursoEnum.Remover_Opcao_Profissional_Um_Do_Cadastro_De_Profissionais, null)
            );

        private static Recurso configurar_Emissao_De_Nota_Pacote;
        public static Recurso ConfigurarEmissaoDeNotaPacote => configurar_Emissao_De_Nota_Pacote ?? (configurar_Emissao_De_Nota_Pacote = new Recurso(RecursoEnum.Configurar_Emissao_De_Nota_Pacote, null)
            );

        private static Recurso consultarRodizioAoAdicionarServico;
        public static Recurso ConsultarRodizioAoAdicionarServico
        {
            get
            {
                return consultarRodizioAoAdicionarServico ?? (
                    consultarRodizioAoAdicionarServico = new Recurso(RecursoEnum.Consultar_Rodizio_Ao_Adicionar_Servico, null)
                );
            }
        }

        private static Recurso pacotesPersonalizados;
        public static Recurso PacotesPersonalizados
        {
            get
            {
                return pacotesPersonalizados ??
                         (pacotesPersonalizados =
                             new Recurso(RecursoEnum.Pacotes_Personalizados, null));
            }
        }

        private static Recurso editarTaxaDeAntecipacaoNasFormasDePagamento;
        public static Recurso EditarTaxaDeAntecipacaoNasFormasDePagamento
        {
            get { return editarTaxaDeAntecipacaoNasFormasDePagamento ?? (editarTaxaDeAntecipacaoNasFormasDePagamento = new Recurso(RecursoEnum.Editar_Taxa_De_Antecipacao_Nas_Formas_De_Pagamento, null)); }
        }

        private static Recurso exigirAceitePoliticaRenovacaoECancelamentoAssinatura;
        public static Recurso ExigirAceitePoliticaRenovacaoECancelamentoAssinatura
        {
            get { return exigirAceitePoliticaRenovacaoECancelamentoAssinatura ?? (exigirAceitePoliticaRenovacaoECancelamentoAssinatura = new Recurso(RecursoEnum.Exigir_Aceite_Politica_Renovacao_E_Cancelamento_Assinatura, null)); }
        }
        
        private static Recurso vendaDeProdutoParceladoParaProfissional;
        public static Recurso VendaDeProdutoParceladoParaProfissional
        {
            get { return vendaDeProdutoParceladoParaProfissional ?? (vendaDeProdutoParceladoParaProfissional = new Recurso(RecursoEnum.Venda_De_Produto_Parcelado_Para_Profissional, null)); }
        }

        private static Recurso contaDigitalConfiguracoesOperador;
        public static Recurso ContaDigitalConfiguracoesOperador
            => contaDigitalConfiguracoesOperador ?? (contaDigitalConfiguracoesOperador = new Recurso(RecursoEnum.ContaDigital_Configuracao_Operador, null));


        private static Recurso contratacaoAutoatendimento;
        public static Recurso ContratacaoAutoatendimento
        {
            get { return contratacaoAutoatendimento ?? (contratacaoAutoatendimento = new Recurso(RecursoEnum.Contratacao_Autoatendimento, null)); }
        }

        private static Recurso permiteAcessoExtratoAgendaRecebiveisBackoffice;
        public static Recurso PermiteAcessoExtratoAgendaRecebiveisBackoffice
        {
            get { return permiteAcessoExtratoAgendaRecebiveisBackoffice ?? (permiteAcessoExtratoAgendaRecebiveisBackoffice = new Recurso(RecursoEnum.Permite_Acesso_Extrato_Agenda_Recebiveis_Backoffice, null)); }
        }

        private static Recurso permiteAcessoAntecipacaoAgendaRecebiveisBackoffice;
        public static Recurso PermiteAcessoAntecipacaoAgendaRecebiveisBackoffice
        {
            get { return permiteAcessoAntecipacaoAgendaRecebiveisBackoffice ?? (permiteAcessoAntecipacaoAgendaRecebiveisBackoffice = new Recurso(RecursoEnum.Permite_Acesso_Antecipacao_Agenda_Recebiveis_Backoffice, null)); }
        }

        private static Recurso permiteAcessoLiquidacaoAgendaRecebiveisBackoffice;
        public static Recurso PermiteAcessoLiquidacaoAgendaRecebiveisBackoffice
        {
            get { return permiteAcessoLiquidacaoAgendaRecebiveisBackoffice ?? (permiteAcessoLiquidacaoAgendaRecebiveisBackoffice = new Recurso(RecursoEnum.Permite_Acesso_Liquidacao_Agenda_Recebiveis_Backoffice, null)); }
        }

        private static Recurso permiteAcessoConfiguracoesAgendaRecebiveisBackoffice;
        public static Recurso PermiteAcessoConfiguracoesAgendaRecebiveisBackoffice
        {
            get { return permiteAcessoConfiguracoesAgendaRecebiveisBackoffice ?? (permiteAcessoConfiguracoesAgendaRecebiveisBackoffice = new Recurso(RecursoEnum.Permite_Acesso_Configuracoes_Agenda_Recebiveis_Backoffice, null)); }
        }

        private static Recurso permiteAcessoExtratoAgendaRecebiveisProfissional;

        public static Recurso PermiteAcessoExtratoAgendaRecebiveisProfissional
        {
            get { return permiteAcessoExtratoAgendaRecebiveisProfissional ?? (permiteAcessoExtratoAgendaRecebiveisProfissional = new Recurso(RecursoEnum.Permite_Acesso_Extrato_Agenda_Recebiveis_Profissional, null)); }
        }

        private static Recurso permiteAcessoAntecipacaoAgendaRecebiveisProfissional;
        public static Recurso PermiteAcessoAntecipacaoAgendaRecebiveisProfissional
        {
            get { return permiteAcessoAntecipacaoAgendaRecebiveisProfissional ?? (permiteAcessoAntecipacaoAgendaRecebiveisProfissional = new Recurso(RecursoEnum.Permite_Acesso_Antecipacao_Agenda_Recebiveis_Profissional, null)); }
        }

        private static Recurso permiteAcessoLiquidacaoAgendaRecebiveisProfissional;
        public static Recurso PermiteAcessoLiquidacaoAgendaRecebiveisProfissional
        {
            get { return permiteAcessoLiquidacaoAgendaRecebiveisProfissional ?? (permiteAcessoLiquidacaoAgendaRecebiveisProfissional = new Recurso(RecursoEnum.Permite_Acesso_Liquidacao_Agenda_Recebiveis_Profissional, null)); }
        }

        private static Recurso permiteAcessoConfiguracoesAgendaRecebiveisProfissional;
        public static Recurso PermiteAcessoConfiguracoesAgendaRecebiveisProfissional
        {
            get { return permiteAcessoConfiguracoesAgendaRecebiveisProfissional ?? (permiteAcessoConfiguracoesAgendaRecebiveisProfissional = new Recurso(RecursoEnum.Permite_Acesso_Configuracoes_Agenda_Recebiveis_Profissional, null)); }
        }

        private static Recurso antecipacaoPagamentoOnline;
        public static Recurso AntecipacaoPagamentoOnline
        {
            get { return antecipacaoPagamentoOnline ?? (antecipacaoPagamentoOnline = new Recurso(RecursoEnum.Antecipacao_Pagamento_Online, null)); }
        }

        private static Recurso reutilizarComandasAbertasEmDiasDiferentes;
        public static Recurso ReutilizarComandasAbertasEmDiasDiferentes
        {
            get { return reutilizarComandasAbertasEmDiasDiferentes ?? (reutilizarComandasAbertasEmDiasDiferentes = new Recurso(RecursoEnum.Reutilizar_Comandas_Abertas_Em_Dias_Diferentes, null)); }
        }

        private static Recurso exibirColunasIdNosRelatorios;

        public static Recurso ExibirColunasIdNosRelatorios
        {
            get { return exibirColunasIdNosRelatorios ?? (exibirColunasIdNosRelatorios = new Recurso(RecursoEnum.Exibir_colunas_de_ids_nos_relatorios, null)); }
        }


        private static Recurso exibirNomeDoProfissionalDoAgendamentoQueTeveBaixaAutomaticaNaExportacaoDeEstoque;

        public static Recurso ExibirNomeDoProfissionalDoAgendamentoQueTeveBaixaAutomaticaNaExportacaoDeEstoque
        {
            get { return exibirNomeDoProfissionalDoAgendamentoQueTeveBaixaAutomaticaNaExportacaoDeEstoque ?? (exibirNomeDoProfissionalDoAgendamentoQueTeveBaixaAutomaticaNaExportacaoDeEstoque = new Recurso(RecursoEnum.Exibir_Profissional_Vinculado_A_Horario_Com_Baixa_Automatica, null)); }
        }

        private static Recurso adicionalCompraMensalLandingPageWhatsApp;
        public static Recurso AdicionalCompraMensalLandingPageWhatsApp
        {
            get { return adicionalCompraMensalLandingPageWhatsApp ?? (adicionalCompraMensalLandingPageWhatsApp = new Recurso(RecursoEnum.Adicional_Compra_Mensal_LandingPage_WhatsApp, null)); }
        }

        private static Recurso exibirComoNosConheceuRankingDeClientes;

        public static Recurso ExibirComoNosConheceuRankingDeClientes
        {
            get { return exibirComoNosConheceuRankingDeClientes ?? (exibirComoNosConheceuRankingDeClientes = new Recurso(RecursoEnum.Exibir_Como_nos_conheceu_ranking_de_clientes, null)); }
        }

        private static Recurso exibirOpcaoDePagamentoComPixIntegradoPelaBelezinha;
        public static Recurso ExibirOpcaoDePagamentoComPixIntegradoPelaBelezinha
        {
            get
            {
                return exibirOpcaoDePagamentoComPixIntegradoPelaBelezinha ?? (exibirOpcaoDePagamentoComPixIntegradoPelaBelezinha =
                new Recurso(RecursoEnum.Exibir_opcao_de_pagamento_com_pix_integrado_pela_belezinha, null));
            }
        }

        private static Recurso cashbackCrmBonus;

        public static Recurso CashbackCrmBonus
        {
            get { return cashbackCrmBonus ?? (cashbackCrmBonus = new Recurso(RecursoEnum.Cashback_Crm_Bonus, null)); }
        }
        
        private static Recurso pagamentoAntecipadoDeServicosNoHotsite;
        public static Recurso PagamentoAntecipadoDeServicosNoHotsite
        {
            get { return pagamentoAntecipadoDeServicosNoHotsite ?? (pagamentoAntecipadoDeServicosNoHotsite = new Recurso(RecursoEnum.Pagamento_Antecipado_De_Servicos_No_Hotsite, null)); }
        }

        private static Recurso exibirResumoLancamentoPagamento;

        public static Recurso ExibirResumoLancamentoPagamento
        {
            get { return exibirResumoLancamentoPagamento ?? (exibirResumoLancamentoPagamento = new Recurso(RecursoEnum.Exibir_Resumo_No_Lancamento_De_Pagamento_De_Profissionais, null)); }
        }

        private static Recurso ehVipParaAtendimentoPersonalizado;

        public static Recurso EhVipParaAtendimentoPersonalizado
        {
            get { return ehVipParaAtendimentoPersonalizado ?? (ehVipParaAtendimentoPersonalizado = new Recurso(RecursoEnum.Eh_Vip_Para_Atendimento_Personalizado, null)); }
        }

        private static Recurso podeAdicionarContratoDeAdesaoNoClube;

        public static Recurso PodeAdicionarContratoDeAdesaoNoClube
        {
            get { return podeAdicionarContratoDeAdesaoNoClube ?? (podeAdicionarContratoDeAdesaoNoClube = new Recurso(RecursoEnum.Pode_Adicionar_Contrato_De_Adesao_No_Clube, null)); }
        }

        private static Recurso connectPagarmeAtualizarTaxaMDRDoProfissionalAutomaticamente;
        public static Recurso ConnectPagarmeAtualizarTaxaMDRDoProfissionalAutomaticamente
        {
            get { return connectPagarmeAtualizarTaxaMDRDoProfissionalAutomaticamente ?? (connectPagarmeAtualizarTaxaMDRDoProfissionalAutomaticamente = new Recurso(RecursoEnum.Connect_Pagarme_Atualizar_Taxa_MDR_Do_Profissional_Automaticamente, null)); }
        }

        private static Recurso connectPagarmeAtualizarTaxaMDRDoEstabelecimentoAutomaticamente;
        public static Recurso ConnectPagarmeAtualizarTaxaMDRDoEstabelecimentoAutomaticamente
        {
            get { return connectPagarmeAtualizarTaxaMDRDoEstabelecimentoAutomaticamente ?? (connectPagarmeAtualizarTaxaMDRDoEstabelecimentoAutomaticamente = new Recurso(RecursoEnum.Connect_Pagarme_Atualizar_Taxa_MDR_Do_Estabelecimento_Automaticamente, null)); }
        }

        private static Recurso contaDigitalAprovadorStone;

        public static Recurso ContaDigitalAprovadorStone
        {
            get { return contaDigitalAprovadorStone ?? (contaDigitalAprovadorStone = new Recurso(RecursoEnum.ContaDigital_Aprovador_Stone, null)); }
        }

        private static Recurso contaDigitalRotinaCancelamentoTransaces;

        public static Recurso ContaDigitalRotinaCancelamentoTransacoes
        {
            get { return contaDigitalRotinaCancelamentoTransaces ?? (contaDigitalRotinaCancelamentoTransaces = new Recurso(RecursoEnum.ContaDigital_Rotina_De_Cancelamento_De_Transacoes, null)); }
        }
        
        private static Recurso redirecionarAcessoComunidade;

        public static Recurso RedirecionarAcessoComunidade
        {
            get { return redirecionarAcessoComunidade ?? (redirecionarAcessoComunidade = new Recurso(RecursoEnum.Redirecionar_Acesso_Comunidade, null)); }
        }

        private static Recurso habilitarManterOrdemDoRodizioPeloDiaAnterior;
        public static Recurso HabilitarManterOrdemDoRodizioPeloDiaAnterior
        {
            get { return habilitarManterOrdemDoRodizioPeloDiaAnterior ?? (habilitarManterOrdemDoRodizioPeloDiaAnterior = new Recurso(RecursoEnum.Habilitar_Manter_Ordem_Do_Rodizio_Pelo_Dia_Anterior, null)); }
        }

        private static Recurso clubeDeAssinaturasEstaDisponivelComoAdicional;
        public static Recurso ClubeDeAssinaturasEstaDisponivelComoAdicional
        {
            get { return clubeDeAssinaturasEstaDisponivelComoAdicional ?? (clubeDeAssinaturasEstaDisponivelComoAdicional = new Recurso(RecursoEnum.Clube_De_Assinaturas_Esta_Disponivel_Como_Adicional, null)); }
        }

        private static Recurso bloqueioDeAgendamentoParaClientesComClubeInadimplente;

        public static Recurso BloqueioDeAgendamentoParaClientesComClubeInadimplente
        {
            get { return bloqueioDeAgendamentoParaClientesComClubeInadimplente ?? (bloqueioDeAgendamentoParaClientesComClubeInadimplente = new Recurso(RecursoEnum.Bloqueio_De_Agendamento_Para_Clientes_Com_Clube_Inadimplente, null)); }
        }
        
        private static Recurso permitePaginacaoTelaRPS;

        public static Recurso PermitePaginacaoTelaRPS
        {
            get { return permitePaginacaoTelaRPS ?? (permitePaginacaoTelaRPS = new Recurso(RecursoEnum.Permite_Paginacao_RPS, null)); }
        }
        
        private static Recurso permiteEmisaoSequencialRPS;

        public static Recurso PermiteEmisasoSequencialRPS
        {
            get { return permiteEmisaoSequencialRPS ?? (permiteEmisaoSequencialRPS = new Recurso(RecursoEnum.Permite_Emisao_Sequencial_RPS, null)); }
        }

        private static Recurso habilitadoParaEmissaoDeParceiroUnificado;
        public static Recurso HabilitadoParaEmissaoDeParceiroUnificado
        {
            get { return habilitadoParaEmissaoDeParceiroUnificado ?? (habilitadoParaEmissaoDeParceiroUnificado = new Recurso(RecursoEnum.Habilitado_Para_Emissao_De_Parceiro_Unificado, null)); }
        }

        private static Lazy<Recurso> lazyHabilitarPixNoLinkDePagamento = LazyLoad(RecursoEnum.Habilitar_Pix_No_Link_De_Pagamento);
        public static Recurso HabilitarPixNoLinkDePagamento => lazyHabilitarPixNoLinkDePagamento.Value;

        private static Recurso despesasPersonalizadas;
        public static Recurso DespesasPersonalizadas
        {
            get { return despesasPersonalizadas ?? (despesasPersonalizadas = new Recurso(RecursoEnum.Despesas_Personalizadas, null)); }
        }

        private static Lazy<Recurso> promocaoOnline = LazyLoad(RecursoEnum.Promocao_Online);
        public static Recurso PromocaoOnline => promocaoOnline.Value;
        
        private static Lazy<Recurso> promocaoOnlineDisponibilidadeDeCadeiras = LazyLoad(RecursoEnum.Promocao_Online_Disponibilidade_De_Cadeiras);
        public static Recurso PromocaoOnlineDisponibilidadeDeCadeiras => promocaoOnlineDisponibilidadeDeCadeiras.Value;
        
        private static Lazy<Recurso> pagamentoFaturaTrinksPagarmeV5 = LazyLoad(RecursoEnum.Pagamento_Fatura_Trinks_PagarMe_V5);
        public static Recurso PagamentoFaturaTrinksPagarmeV5 => pagamentoFaturaTrinksPagarmeV5.Value;

        #region Thread-safe lazy load for caching
        private static Lazy<Recurso> LazyLoad(RecursoEnum recurso)
            => new Lazy<Recurso>(() => new Recurso(recurso, null));
        #endregion

        private static Recurso exibeNumeroFechamentoNaImpressaoDoFechamentoEOcultaNumeroComanda;

        public static Recurso ExibeNumeroFechamentoNaImpressaoDoFechamentoEOcultaNumeroComanda
        {
            get { return exibeNumeroFechamentoNaImpressaoDoFechamentoEOcultaNumeroComanda ?? (exibeNumeroFechamentoNaImpressaoDoFechamentoEOcultaNumeroComanda = new Recurso(RecursoEnum.Exibe_Numero_Fechamento_Na_Impressao_Do_Fechamento_E_Oculta_Numero_Comanda, null)); }
        }

        private static Recurso visualizarTelefonesInternacionaisAppB2B;
        public static Recurso VisualizarTelefonesInternacionaisAppB2B
        {
            get { return visualizarTelefonesInternacionaisAppB2B ?? (visualizarTelefonesInternacionaisAppB2B = new Recurso(RecursoEnum.Visualizar_Telefones_Internacionais_App_B2B, null)); }
        }

        private static Recurso visualizarTelefonesInternacionaisAppB2C;
        public static Recurso VisualizarTelefonesInternacionaisAppB2C
        {
            get { return visualizarTelefonesInternacionaisAppB2C ?? (visualizarTelefonesInternacionaisAppB2C = new Recurso(RecursoEnum.Visualizar_Telefones_Internacionais_App_B2C, null)); }
        }
    }
}
