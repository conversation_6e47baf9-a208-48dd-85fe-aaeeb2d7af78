using System.ComponentModel;

namespace Perlink.Trinks.ControleDeFuncionalidades.Enums
{
    public enum RecursoEnum
    {
        [Description("Serviço padrão")]
        Servico_Padrao = 1,

        [Description("Lembrete Premium")]
        Lembrete_Premium = 2,

        [Description("Pagamento online antecipado")]
        Pagamento_Online_Antecipado = 3,

        [Description("Realizar pagamento online antecipado")]
        Realizar_Pagamento_Online_Antecipado = 4,

        [Description("Novo frame busca")]
        Novo_Frame_Busca = 5,

        [Description("Novo visual do backoffice")]
        Novo_Visual_Backoffice = 6,

        [Description("Baixa automatica de estoque")]
        Baixa_Automatica_Estoque = 7,

        [Description("Debito parcial")]
        Debito_Parcial = 8,

        [Description("Novo design do fechamento de conta")]
        Novo_Design_Fechamento_Conta = 9,

        [Description("Formulário de anamnese")]
        Formulario_Anamnese = 10,

        [Description("Relatório simplificado")]
        Relatorio_Simplificado = 11,

        [Description("Ficha do cliente")]
        Ficha_do_Cliente = 12,

        [Description("Administrar App TrinksPro")]
        AdministrarAppProfissinal = 13,

        [Description("Acesso ao painel de atendimento pelo TrinksPro")]
        Acesso_Ao_Painel_De_Atendimento_Pelo_App_Trinks_Profissional = 14,

        [Description("Gorjeta")]
        Gorjeta = 15,

        [Description("Fechar conta pelo TrinksPro")]
        Fechar_Conta_App_Trinks_Profissional = 16,

        [Description("Conciliação Siclos")]
        ConciliacaoSiclos = 17,

        [Description("Utilizar belezinha no fechamento de conta do TrinksPro")]
        Utilizar_Belezinha_Fechamento_Trinks_Profissional = 18,

        [Description("Assinatura Digital")]
        Assinatura_Digital = 19,

        [Description("Utilizar canal de ajuda no TrinksPro")]
        Utilizar_Canal_De_Ajuda_Trinks_Profissional = 20,

        [Description("Survey no TrinksPro")]
        Survey_App_Trinks_Profissional = 21,

        [Description("Ranking de pacotes")]
        Ranking_Pacotes = 22,

        [Description("Botão novo menu")]
        Botao_Novo_Menu = 23,

        [Description("App exclusivo")]
        App_Exclusivo = 24,

        [Description("Zoom na agenda")]
        Zoom_Na_Agenda = 25,

        [Description("Ativação automatica do programa de fidelidade")]
        AtivacaoAutomaticaDoProgramaDeFidelidade = 26,

        [Description("Survey Trinks(Mobile)")]
        Survey_Trinks_Mobile = 27,

        [Description("Tela com relatórios principais")]
        TelaComRelatoriosPrincipais = 28,

        [Description("Configurações básicas")]
        Configuracoes_Basicas = 29,

        [Description("Home")]
        Home = 30,

        [Description("Melhorias de consumo de pacote")]
        Melhorias_Consumo_Pacote = 31,

        [Description("CTA da belezinha - Nunca usou integrado - Fechamento de conta")]
        CTA_Belezinha_Nunca_Usou_Integrado_Fechamento_De_Conta = 32,

        [Description("Contato por WPP no processamento da belezinha")]
        Contato_Por_Whatsapp_No_Processamento_Da_Belezinha = 33,

        [Description("Contato por WPP no saiba mais do incentivo a integração da belezinha")]
        Contato_Por_Whatsapp_No_Saiba_Mais_Do_Incentivo_A_Integracao_Da_Belezinha = 34,

        [Description("CTA da belezinha - Parou de usar integrado - Fechamento de conta")]
        CTA_Belezinha_Parou_De_Usar_Integrado_Fechamento_De_Conta = 35,

        [Description("Contato por WPP ao selecionar belezinha no fechamento de conta")]
        Contato_Por_Whatsapp_Ao_Selecionar_Belezinha_No_Fechamento_De_Conta = 36,

        [Description("CTA da belezinha - Nunca usou integrado - Relatório financeiro")]
        CTA_Belezinha_Nunca_Usou_Integrado_Relatorio_Financeiro = 37,

        [Description("CTA da belezinha - Parou de usar integrado - Relatorio financeiro")]
        CTA_Belezinha_Parou_De_Usar_Integrado_Relatorio_Financeiro = 38,

        [Description("Questionário para entender o interesse pelo adicional")]
        Questionario_Para_Entender_Interesse_Pelo_Adicional_Esta_Ativo = 39,

        [Description("CTA da belezinha - Nunca usou integrado - Agenda")]
        CTA_Belezinha_Nunca_Usou_Integrado_Agenda = 40,

        [Description("CTA da belezinha - Parou de usar integrado - Agenda")]
        CTA_Belezinha_Parou_De_Usar_Integrado_Agenda = 41,

        [Description("CTA da belezinha - Nunca usou integrado - Relatório de comissões")]
        CTA_Belezinha_Nunca_Usou_Integrado_Relatorio_De_Comissoes = 42,

        [Description("CTA da belezinha - Parou de usar integrado - Relatório de comissões")]
        CTA_Belezinha_Parou_De_Usar_Integrado_Relatorio_De_Comissoes = 43,

        [Description("CTA da belezinha - Nunca usou integrado - Home")]
        CTA_Belezinha_Nunca_Usou_Integrado_Home = 44,

        [Description("CTA da belezinha - Parou de usar integrado - Home")]
        CTA_Belezinha_Parou_De_Usar_Integrado_Home = 45,

        [Description("Pesquisa - Combo de clientes com todas as máscaras de telefone")]
        Pesquisa_ComboCliente_Com_Todas_As_Mascaras_De_Telefone = 46,

        [Description("Tela de controle de entrada e saída")]
        Controle_De_Entrada_E_Saida = 47,

        [Description("Formulário de PMF ao fechar conta usando a belezinha")]
        Formulario_PMF_Ao_Fechar_Conta_Com_Belezinha = 48,

        [Description("CTA da belezinha - Nunca usou integrado - TrinksPro - Home")]
        CTA_Belezinha_Nunca_Usou_Integrado_TrinksPro_Home = 49,

        [Description("CTA da belezinha - Parou de usar integrado - TrinksPro - Home")]
        CTA_Belezinha_Parou_De_Usar_Integrado_TrinksPro_Home = 50,

        [Description("Mapa de calor")]
        Mapa_de_Calor = 51,

        [Description("Motivo de cadastro assertivo")]
        Motivo_De_Cadastro_Assertivo = 52,

        [Description("Pagamento de fatura utilizando PIX")]
        Pagamento_Fatura_Pix = 53,

        [Description("Importador de dados no backoffice")]
        Importador_de_Dados_No_Backoffice = 54,

        [Description("Integração com o Facebook Business")]
        Integracao_Facebook_Business = 55,

        [Description("Redirecionar para a home ao acessar o backoffice")]
        Redirecionar_Para_A_Home_Ao_Acessar_O_Backoffice = 56,

        [Description("Retorno de clientes")]
        Retorno_de_Clientes = 57,

        [Description("Link de pagamento")]
        Link_de_Pagamento = 58,

        [Description("Novas telas de ranking")]
        Novas_Telas_Ranking = 59,

        [Description("Stories de novidades")]
        Stories_De_Novidades = 60,

        [Description("CTA anual fiscal - Home")]
        CTA_Anual_Fiscal_Inicio = 61,

        [Description("CTA anual fiscal - Relatório Financeiro")]
        CTA_Anual_Fiscal_Relatorio_Financeiro = 62,

        [Description("CTA anual fiscal - Relatório financeiro por forma de pagamento")]
        CTA_Anual_Fiscal_Relatorio_Financeiro_Por_Forma_Pagamento = 63,

        [Description("CTA anual fiscal - Serviços")]
        CTA_Anual_Fiscal_Servicos = 64,

        [Description("CTA anual fiscal - Produtos")]
        CTA_Anual_Fiscal_Produtos = 65,

        [Description("CTA anual fiscal - Comissão")]
        CTA_Anual_Fiscal_Comissao = 66,

        [Description("Saiba mais com vídeo na NFS")]
        Saiba_Mais_NFS_Video = 67,

        [Description("Gyra mais - Integração")]
        Gyra_Mais_Integracao = 68,

        [Description("Gyra mais - Oferta")]
        Gyra_Mais_Oferta = 69,

        [Description("Popup de experimentação da NFS")]
        Experimentacao_NFS = 70,

        [Description("Contato por WPP - No popup de aceitação da experimentação da NFS")]
        Contato_Por_Whatsapp_Popup_Aceitacao_Experimentacao_NFS = 71,

        [Description("Assinatura recorrente")]
        Assinatura_Recorrente = 72,

        [Description("Comunidade Trinks - Tela sugestão")]
        Comunidade_Trinks_Tela_Sugestao = 73,

        [Description("Usar automaticamente o pacote do cliente no fechamento de conta")]
        Usar_Automaticamente_Pacote_Do_Cliente_No_Fechamento_De_Conta = 74,

        [Description("Novo layout responsivo da tela de pacotes")]
        Novo_layout_Responsivo_Tela_Todos_Os_Pacotes = 75,

        [Description("Cupom - Extensão do programa de fidelidade")]
        Cupom = 76,

        [Description("Onboarding - Seleção de trilha")]
        Selecao_De_Trilha = 77,

        [Description("Onboarding - Mapear perfil do estabelecimento")]
        Mapear_Perfil_Do_Estabelecimento = 78,

        [Description("Onboarding - Jornada da Belezinha")]
        Nova_Jornada_Belezinha = 79,

        [Description("Cadastro de serviço simplificado - TrinksPro")]
        Cadastro_De_Servico_Simplificado_TrinksPro = 80,

        [Description("Controle de estoque no fechamento")]
        Modal_Produtos_Usados_Nos_Servicos = 81,

        [Description("Novo layout do cadastro de despesa")]
        Novo_Layout_Do_Cadastro_De_Despesa = 82,

        [Description("Solicitar cancelamento da assinatura pelo meu plano")]
        Solicitar_Cancelamento_Da_Assinatura_Pelo_Meu_Plano = 83,

        [Description("Cards de ações financeiras no menu ações - TrinksPro")]
        Cards_Acoes_Financeiras_Menu_Acoes_TrinksPro = 84,

        [Description("Aporte")]
        Aporte = 85,

        [Description("Novo Botão de financeiro no menu de acesso rápido")]
        Botao_Financeiro_No_Menu_De_Acesso_Rapido = 86,

        [Description("Novo botão financeiro no menu acesso rápido - TrinksPro")]
        Botao_Financeiro_No_Menu_De_Acesso_Rapido_TrinksPro = 87,

        [Description("Despesas Pessoais")]
        Despesas_Pessoais = 88,

        [Description("Formulário de PMF ao fechar conta com resgate de pontos de fidelidade")]
        Formulario_PMF_Ao_Fechar_Conta_Com_Resgate_Pontos_Fidelidade = 89,

        [Description("CTA para quem tem programa de fidelidade contratado chegar até o painel")]
        Programa_Fidelidade_CTA_Engajamento = 90,

        [Description("Ícone flutuante para agendamento rápido - TrinksPro")]
        FloatingBubble = 91,

        [Description("Toggle utilizar a última versão das categorias e grupos de despesas")]
        Ultima_Versao_Categorias_E_Grupos_De_Despesas = 92,

        [Description("Retencao - Botão de acesso ao Meu Plano em mais ações - TrinksPro")]
        Botao_De_Acesso_Ao_Meu_Plano_Pelo_TrinksPro = 93,

        [Description("Opções de cancelamento no menu ajuda")]
        Opcoes_De_Cancelamento_No_Menu_Ajuda = 94,

        [Description("Separar faixas 1 e 2 no cadastro de estabelecimento")]
        Separar_Faixas_1_e_2_No_Cadastro_De_Estabelecimento = 95,

        [Description("Minha conta - Chaves de acesso")]
        Minha_Conta_Chaves_Acesso = 96,

        [Description("Clube de Assinaturas")]
        Clube_De_Assinaturas = 97,

        [Description("Estabelecimento consumir API pública")]
        Estabelecimento_Consumir_API_Publica = 98,

        [Description("CTA de aviso de termino de periodo gratis do adicional")]
        AvisoDeTerminoPeriodoGaratisAdicional = 99,

        [Description("Tarefas do onboarding")]
        Tarefas_Onboarding = 100,

        [Description("Aviso de tarefas do onboarding")]
        Aviso_De_Tarefas_Onboarding = 101,

        [Description("Nova exibição das configurações")]
        Novo_Menu_Configuracoes = 102,

        [Description("Facelift popup agendamneto")]
        Facelift_Popup_Agendamento = 103,

        [Description("Histórico de cliente importado de sistema externo")]
        Histrorico_De_Clientes_Importados = 104,

        [Description("Tela de Cadastro V2")]
        CadastroV2 = 105,

        [Description("Backdoor para o endereço no fluxo de cadastro")]
        BackdoorParaEnderecoNoCadastro = 106,

        [Description("Lading Page de Integração com o WhatsApp")]
        Lading_Page_De_Integracao_Com_WhatsApp = 107,

        [Description("Preço da Lading Page de Integração com o WhatsApp")]
        Preco_Lading_Page_De_Integracao_Com_WhatsApp = 109,

        [Description("Permite utilizar unificação de profissional por e-mail")]
        unificacao_De_Profissional_Por_Email = 110,

        [Description("Pular segunda validação de email no fluxo de cadastro para celulares")]
        PularSegundaValidacaoEmailFluxoCadastroCelulares = 111,

        [Description("CTA para cadastro de profissional na tela de comissão")]
        CTA_Comissao_Cadastro_Profissional = 112,

        [Description("Permissão para acessar o Indica trinks")]
        Pode_Acessar_O_Indica_Trinks = 113,

        [Description("Teste AB do WhyTrinks está ativo")]
        Teste_AB_Whytrinks_ativo = 114,

        [Description("Pode acessar Convite Retorno")]
        Pode_Acessar_Convite_Retorno = 115,

        [Description("Permite acesso modal de cadastro cliente responsiva")]
        Permite_Acessar_Modal_De_Cadastro_Cliente_Responsiva = 116,

        [Description("Compartilhamento de pacotes")]
        Compartilhamento_De_Pacotes = 118,

        [Description("Permite configurar os envios independnete do fluxo na Rotina de Mensagens (Wpp)")]
        Pode_Configurar_Envio_Independente_Fluxo_Rotina_Mensagens_Wpp = 120,

        [Description("Exibir coleta de feedback da funcionalidade Rotina de Mensagens")]
        Exibir_Coleta_Feedback_Rotina_Mensagens = 121,

        [Description("Estabelecimento pode usar CNPJ do cliente para emitir NFC-e")]
        Pode_Usar_Cnpj_Do_Cliente_Em_Emissao_Nfce = 122,

        [Description("Estabelecimento pode usar CNPJ do cliente para emitir NFS-e")]
        Pode_Usar_Cnpj_Do_Cliente_Em_Emissao_Nfse = 123,

        [Description("Lançamento de Pagamento de Profissionais")]
        Lancamento_De_Pagamento_De_Profissionais = 125,

        [Description("Habilitar telefone internacional no cadastro Cliente")]
        Permite_Cadastrar_Telefone_Internacional_No_Cadastro_Cliente = 126,

        [Description("Permite configurar tipo de envio das confirmações pelo whatsapp")]
        Pode_Configurar_Tipo_Envio_Confirmacoes_Wpp = 127,

        [Description("Permitir estabelecimento cancelar automaticamente pela Rotina de Mensagens (Não irei mais)")]
        Cancelamento_Automatico_Via_Rotina_Mensagens = 128,

        [Description("Compra recorrente de pacotes do WPP (Rotina de Mensagens)")]
        Compra_Recorrente_WhatsApp = 129,

        [Description("Conta Digital")]
        ContaDigital = 130,

        [Description("Relatório de Consumo de Pacotes")]
        Relatorio_Consumo_De_Pacotes = 131,

        [Description("Exibir coleta de feedback da funcionalidade Convite de Retorno")]
        Exibir_Coleta_Feedback_Convite_Retorno = 132,

        [Description("Permitir acesso a nova tela de Relatórios de retorno de clientes")]
        Relatorios_Retorno = 134,

        [Description("Observações do agendamento a mostra na Agenda por Cliente abaixo do serviço para ser mais visível")]
        Observacoes_Agendamento_A_Mostra_Na_Agenda_Por_Cliente = 137,

        [Description("Descontos personalizados")]
        Descontos_Personalizados = 138,

        [Description("Esconder aba no menu de clientes em atendimento do autoatendimento")]
        Esconder_Aba_Clientes_Em_Atendimento = 139,

        [Description("Remover opção 'Profissional 1' do cadastro de profissionais")]
        Remover_Opcao_Profissional_Um_Do_Cadastro_De_Profissionais = 140,

        [Description("Configurar emissão de nota de pacote")]
        Configurar_Emissao_De_Nota_Pacote = 141,

        [Description("Regularização de pagamento do sistema para clientes do anual/semestral")]
        Regularizacao_Pagamento_Assinatura_Anual_Semestral = 142,

        [Description("Consultar rodízio ao adicionar serviços na comanda")]
        Consultar_Rodizio_Ao_Adicionar_Servico = 143,

        [Description("Permite utilizar Pacotes Personalizados")]
        Pacotes_Personalizados = 144,

        [Description("Editar taxa de antecipação nas formas de pagamento")]
        Editar_Taxa_De_Antecipacao_Nas_Formas_De_Pagamento = 145,

        [Description("Exigir aceite sobre política de renovação e de cancelamento na assinatura")]
        Exigir_Aceite_Politica_Renovacao_E_Cancelamento_Assinatura = 146,

        [Description("Parcelar venda de produtos para profissional")]
        Venda_De_Produto_Parcelado_Para_Profissional = 150,

        [Description("Conta Digital - Configuração Operador")]
        ContaDigital_Configuracao_Operador = 147,

        [Description("Contratação e LP do Autoatendimento")]
        Contratacao_Autoatendimento = 149,

        [Description("Permite acesso a área de Extrato da Agenda de Recebíveis Backoffice")]
        Permite_Acesso_Extrato_Agenda_Recebiveis_Backoffice = 151,

        [Description("Permite acesso a área de Antecipação da Agenda de Recebíveis Backoffice")]
        Permite_Acesso_Antecipacao_Agenda_Recebiveis_Backoffice = 152,

        [Description("Permite acesso a área de Liquidacao da Agenda de Recebíveis Backoffice")]
        Permite_Acesso_Liquidacao_Agenda_Recebiveis_Backoffice = 153,

        [Description("Permite acesso a área de Configurações da Agenda de Recebíveis Backoffice")]
        Permite_Acesso_Configuracoes_Agenda_Recebiveis_Backoffice = 154,

        [Description("Permite acesso a área de Extrato da Agenda de Recebíveis Profissional")]
        Permite_Acesso_Extrato_Agenda_Recebiveis_Profissional = 155,

        [Description("Permite acesso a área de Antecipação da Agenda de Recebíveis Profissional")]
        Permite_Acesso_Antecipacao_Agenda_Recebiveis_Profissional = 156,

        [Description("Permite acesso a área de Liquidacao da Agenda de Recebíveis Profissional")]
        Permite_Acesso_Liquidacao_Agenda_Recebiveis_Profissional = 157,

        [Description("Permite acesso a área de Configurações da Agenda de Recebíveis Profissional")]
        Permite_Acesso_Configuracoes_Agenda_Recebiveis_Profissional = 158,

        [Description("Antecipação de pagamento online")]
        Antecipacao_Pagamento_Online = 159,

        [Description("Reutilizar comandas abertas em dias diferentes")]
        Reutilizar_Comandas_Abertas_Em_Dias_Diferentes = 160,

        [Description("DividirLoteNfse")]
        Dividir_Lote_Nfse = 161,

        [Description("Habilitar Ids nos relatorios de exportações")]
        Exibir_colunas_de_ids_nos_relatorios = 162,

        [Description("Liberar Emissão de nfce para fechamentos com crédito ou consumo de pagamento online")]
        Liberar_Emissao_Nfc_Pagamento_Online = 163,

        [Description("Rejeitar lote RPS e não desfazer")]
        Rejeitar_Lote_RPS = 165,

        [Description("Exibir opção de pagamento com PIX integrado pela belezinha")]
        Exibir_opcao_de_pagamento_com_pix_integrado_pela_belezinha = 166,

        [Description("Habilitar nome do profissional vinculados ao atendimento com baixa automatica na exportacao de movimentacao de estoque")]
        Exibir_Profissional_Vinculado_A_Horario_Com_Baixa_Automatica = 167,

        [Description("Habilitar a exibição do campo Como nos conheceu no ranking de clientes")]
        Exibir_Como_nos_conheceu_ranking_de_clientes = 168,

        [Description("Adicional compra mensal landing page pacote whatsapp")]
        Adicional_Compra_Mensal_LandingPage_WhatsApp = 169,

        [Description("Habilitar cashback da CRM Bônus")]
        Cashback_Crm_Bonus = 170,

        [Description("Pagamento Antecipado de Serviços no Hotsite")]
        Pagamento_Antecipado_De_Servicos_No_Hotsite = 171,

        [Description("Estabelecimento é VIP para atendimento personalizado")]
        Eh_Vip_Para_Atendimento_Personalizado = 172,

        [Description("Habilitar Pix no link de pagamento")]
        Habilitar_Pix_No_Link_De_Pagamento = 173,
        
        [Description("Exibir resumo no lançamento de profissionais")]
        Exibir_Resumo_No_Lancamento_De_Pagamento_De_Profissionais = 175,

        [Description("Habilitar reenvio automático de rps rejeitado pela rotina de nfs-e")]
        Reenviar_Lote_Rejeitado_Automaticamente = 176,

        [Description("Estabelecimento pode adicionar contrato de adesão ao Clube de Assinaturas")]
        Pode_Adicionar_Contrato_De_Adesao_No_Clube = 177,

        [Description("Conta Digital - Aprovador Stone")]
        ContaDigital_Aprovador_Stone = 178,

        [Description("Connect Pagarme - Atualizar taxa MDR do profissional automaticamente")]
        Connect_Pagarme_Atualizar_Taxa_MDR_Do_Profissional_Automaticamente = 179,

        [Description("Connect Pagarme - Atualizar taxa MDR do estabelecimento automaticamente")]
        Connect_Pagarme_Atualizar_Taxa_MDR_Do_Estabelecimento_Automaticamente = 180,

        [Description("Habilitar redirecionamento da página de comunidade para outro sub-domínio")]
        Redirecionar_Acesso_Comunidade = 181,

        [Description("Habilitar manter ordem do rodízio pelo dia anterior")]
        Habilitar_Manter_Ordem_Do_Rodizio_Pelo_Dia_Anterior = 182,

        [Description("Conta Digital - Permitir rotina de cancelamento de transações aguardando aprovação")]
        ContaDigital_Rotina_De_Cancelamento_De_Transacoes = 183,

        [Description("Clube de Assinaturas disponível como adicional")]
        Clube_De_Assinaturas_Esta_Disponivel_Como_Adicional = 185,

        [Description("Habilitar promoção online")]
        Promocao_Online = 187,
        
        [Description("Promocão Online - Disponibilidade de Cadeiras")]
        Promocao_Online_Disponibilidade_De_Cadeiras = 188,

        [Description("Bloqueia o agendamento online para clientes com clubes inadimplentes")]
        Bloqueio_De_Agendamento_Para_Clientes_Com_Clube_Inadimplente = 189,
        
        [Description("Habilitado Para Emissao De Parceiro Unificado")]
        Habilitado_Para_Emissao_De_Parceiro_Unificado = 190,
        
        [Description("Permitir a emissão com envio sequencial do RPS")]
        Permite_Emisao_Sequencial_RPS = 191,

        [Description("Exibe o número do fechamento na impressão do fechamento e oculta o número da comanda")]
        Exibe_Numero_Fechamento_Na_Impressao_Do_Fechamento_E_Oculta_Numero_Comanda = 192,
        
        [Description("Permite uso de paginaçao na tela de RPS")]
        Permite_Paginacao_RPS = 193,

        [Description("APP B2B - Permite visualizar telefones internacionais")]
        Visualizar_Telefones_Internacionais_App_B2B = 194,

        [Description("APP B2C - Permite visualizar telefones internacionais")]
        Visualizar_Telefones_Internacionais_App_B2C = 195,

        [Description("Despesas personalizadas")]
        Despesas_Personalizadas = 197,
        
        [Description("Habilitar o uso da versão v5 da API da Pagarme para pagamento de faturas Trinks")]
        Pagamento_Fatura_Trinks_PagarMe_V5 = 198,
    }
}
