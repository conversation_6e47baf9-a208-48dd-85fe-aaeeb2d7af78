﻿using System.ComponentModel;

namespace Perlink.Trinks.ControleDeFuncionalidades.Enums
{
    public enum ServicoAdicionalEnum
    {

        [Description("Serviço Padrão")]
        ServicoPadrao = 1,

        [Description("Lembrete Premium")]
        LembretePremium = 2,

        [Description("Programa de Fidelidade")]
        ProgramaDeFidelidade = 3,

        [Description("Nota Fiscal do Consumidor")]
        NFCe = 4,

        [Description("Nota Fiscal de Serviço")]
        NFSe = 5,

        [Description("Profissional Parceiro")]
        ProfissionalParceiro = 6,

        [Description("Belezinha")]
        BelezinhaSemSplit = 7,

        [Description("Trinks Pay")]
        BelezinhaComSplit = 8,

        [Description("Aplicativo Exclusivo")]
        AppWhiteLabelExclusivo = 9,

        [Description("Aplicativo Exclusivo Novo")]
        AppExclusivoNovo = 10,

        [Description("Pacote de 2000 SMS Marketing")]
        Pacote2000SMSMarketing = 11,

        [Description("Integração com API")]
        IntegracaoComApi = 12,

        [Description("Belezinha Ton")]
        BelezinhaTon = 13,

        [Description("Autoatendimento")]
        Autoatendimento = 14,

        [Description("Webhook")]
        Webhook = 15,

        [Description("Rotina de Mensagem WhatsApp")]
        RotinaMensagem = 16,

        [Description("Cloudia 200")]
        Cloudia200 = 17,

        [Description("Cloudia 600")]
        Cloudia600 = 18,

        [Description("Clube de Assinaturas")]
        ClubeDeAssinaturas = 19,

        [Description("Conector para BI - até 5 lojas")]
        ConectorBI5Lojas = 20,

        [Description("Conector para BI - 6 a 50 lojas")]
        ConectorBI6Ate50Lojas = 21,

        [Description("Conector para BI - até 5 lojas")]
        ConectorBIAcima50Lojas = 22,

        [Description("Conector para BI - Especial")]
        ConectorBIEspecial = 23,

        [Description("Cloudia 900")]
        Cloudia900 = 24,

        [Description("Cloudia 1200")]
        Cloudia1200 = 25,

        [Description("Cloudia 1600")]
        Cloudia1600 = 26,

        [Description("Cloudia 2200")]
        Cloudia2200 = 27,
    }
}