﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Dashboard.DTOs;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Dashboard.Services
{

    public class ReceitaDiariaPessoaJuridicaService : BaseService, IReceitaDiariaPessoaJuridicaService
    {

        public DadosDashboardPorDataDTO<decimal> ListarFiltrado(DashboardFiltro filtro)
        {
            var transacoes = Domain.Financeiro.TransacaoRepository.Queryable(true);
            transacoes = FiltrarSomentePagamentosNaoEstornados(transacoes);
            transacoes = FiltrarSomentePagamentosNaoDescontadosDeProfissional(transacoes);

            if (filtro.IdPessoa.HasValue)
                transacoes = FiltrarPelaPessoaJuridica(transacoes, filtro.IdPessoa.Value);

            if (filtro.IdFranquia.HasValue)
                transacoes = FiltrarPelaFranquia(transacoes, filtro.IdFranquia.Value);

            if (filtro.DataInicio.HasValue)
                transacoes = FiltrarDataInicio(transacoes, filtro.DataInicio.Value);

            if (filtro.DataFim.HasValue)
                transacoes = FiltrarDataFim(transacoes, filtro.DataFim.Value);

            if (filtro.Periodicidade == PeriodicidadeEnum.Diario)
            {
                var retorno = transacoes.GroupBy(f => f.DataHora.Date).Select(f => new KeyValuePair<DateTime, decimal>(f.Key,
                    f.Sum(g => g.TotalPagar) ?? 0));

                return new DadosDashboardPorDataDTO<decimal>(retorno);
            }
            else if (filtro.Periodicidade == PeriodicidadeEnum.Mensal)
            {
                var retorno = transacoes.GroupBy(f => new { f.DataHora.Month, f.DataHora.Year })
                    .Select(f => new
                    {
                        f.Key,
                        Valor = f.Sum(g => g.TotalPagar) ?? 0
                    })
                    .ToList()
                    .Select(f => new KeyValuePair<DateTime, decimal>(new DateTime(f.Key.Year, f.Key.Month, 1), f.Valor));

                return new DadosDashboardPorDataDTO<decimal>(retorno);
            }
            else if (filtro.Periodicidade == PeriodicidadeEnum.PeriodoTodo)
            {
                var retorno = new List<KeyValuePair<DateTime, decimal>> {
                    new KeyValuePair<DateTime, decimal>(filtro.DataInicio ?? transacoes.Min(f=>f.DataHora),transacoes.Sum(f=>f.TotalPagar) ?? 0)
                };

                return new DadosDashboardPorDataDTO<decimal>(retorno);
            }

            throw new NotImplementedException();
        }

        private static IQueryable<Transacao> FiltrarDataFim(IQueryable<Transacao> transacoes, DateTime dataFim)
        {
            transacoes = transacoes.Where(f => f.DataHora < dataFim.AddDays(1).Date);
            return transacoes;
        }

        private static IQueryable<Transacao> FiltrarDataInicio(IQueryable<Transacao> transacoes, DateTime dataInicio)
        {
            transacoes = transacoes.Where(f => f.DataHora >= dataInicio.Date);
            return transacoes;
        }

        private static IQueryable<Transacao> FiltrarPelaFranquia(IQueryable<Transacao> transacoes, int idFranquia)
        {
            var idsPJ = Domain.Pessoas.EstabelecimentoRepository.Queryable()
                .Where(f => f.FranquiaEstabelecimento != null && f.FranquiaEstabelecimento.Ativo && f.FranquiaEstabelecimento.Franquia.Id == idFranquia)
                .Select(f => f.PessoaJuridica.IdPessoa);
            transacoes = transacoes.Where(f => idsPJ.Contains(f.PessoaQueRecebeu.IdPessoa));
            return transacoes;
        }

        private static IQueryable<Transacao> FiltrarPelaPessoaJuridica(IQueryable<Transacao> transacoes, int idPessoaJuridica)
        {
            transacoes = transacoes.Where(f => f.PessoaQueRecebeu.IdPessoa == idPessoaJuridica);
            return transacoes;
        }

        private static IQueryable<Transacao> FiltrarSomentePagamentosNaoEstornados(IQueryable<Transacao> transacoes)
        {
            transacoes = transacoes.Where(f => f.TipoTransacao.Id == 1 && f.TransacaoQueEstounouEsta == null);
            return transacoes;
        }

        private static IQueryable<Transacao> FiltrarSomentePagamentosNaoDescontadosDeProfissional(IQueryable<Transacao> transacoes)
        {
            transacoes = transacoes.Where(f => !Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable()
                                                .Any(g => g.Transacao.Id == f.Id && g.FormaPagamento.Id == (int)FormaPagamentoEnum.DescontoDeProfissional));

            return transacoes;
        }
    }
}