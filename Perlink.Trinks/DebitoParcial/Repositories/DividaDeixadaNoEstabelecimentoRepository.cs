﻿using Perlink.Trinks.DebitoParcial.Filtros;
using Perlink.Trinks.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.DebitoParcial.Repositories
{

    public partial class DividaDeixadaNoEstabelecimentoRepository : IDividaDeixadaNoEstabelecimentoRepository
    {

        public decimal ObterValorTotalFaltantePorCliente(int idPessoaEstabelecimento, int idPessoaCliente)
        {
            var valorFaltante = Queryable()
                .Where(d => d.IdPessoaDoEstabelecimento == idPessoaEstabelecimento
                        && d.IdPessoaDoCliente == idPessoaCliente
                        && d.EstaPendenteDePagamento)
                .Sum(d => (decimal?)d.ValorTotalFaltante);

            return valorFaltante ?? 0;
        }

        public decimal ObterValorTotalFaltantePorClienteAnterioresAData(int idPessoaEstabelecimento, int idPessoaCliente, DateTime dataLimite)
        {
            var valorFaltante = Queryable()
                .Where(d => d.IdPessoaDoEstabelecimento == idPessoaEstabelecimento
                        && d.IdPessoaDoCliente == idPessoaCliente
                        && d.EstaPendenteDePagamento
                        && d.DataQueDeixouDivida < dataLimite)
                .Sum(d => (decimal?)d.ValorTotalFaltante);

            return valorFaltante ?? 0;
        }

        public List<DividaDeixadaNoEstabelecimento> ListarPendentesDePagamentoDoCliente(int idPessoaEstabelecimento, int idPessoaCliente)
        {
            return Queryable()
                .Where(d => d.IdPessoaDoEstabelecimento == idPessoaEstabelecimento
                         && d.IdPessoaDoCliente == idPessoaCliente
                         && d.EstaPendenteDePagamento)
                .OrderBy(d => d.DataQueDeixouDivida)
                .ToList();
        }

        public DividaDeixadaNoEstabelecimento ObterPorTransacao(int idPessoaEstabelecimento, int idTransacao)
        {
            return Queryable()
                .FirstOrDefault(d => d.IdPessoaDoEstabelecimento == idPessoaEstabelecimento
                                    && d.IdTransacao == idTransacao);
        }

        public List<int> ListarIdsTransacaoComDividasAssociadasNoPeriodo(int idPessoaEstabelecimento, DateTime? dataInicio, DateTime? dataFim)
        {
            var query = Queryable().Where(d => d.IdPessoaDoEstabelecimento == idPessoaEstabelecimento);

            if (dataInicio.HasValue)
                query = query.Where(d => d.DataQueDeixouDivida >= dataInicio.Value.Date);

            if (dataFim.HasValue)
                query = query.Where(d => d.DataQueDeixouDivida < dataFim.Value.AddDays(1).Date);

            return query.Select(d => d.IdTransacao).ToList();
        }

        public List<DividaDeixadaNoEstabelecimento> ObterDividasPeloIdTransacaoFormaPagamento(int idTransacaoFormaPagamento)
        {
            return Queryable().Where(p => p.IdTransacaoFormaPagamento == idTransacaoFormaPagamento).ToList();
        }

        #region Filtros Queryable

        public IList<ClientesEmDebito> ListarClientesEmDebito(FiltroDivida filtro, bool incluiHoje = false)
        {
            var dividas = Queryable();
            var clientes = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(filtro.IdEstabelecimento);

            if (!String.IsNullOrWhiteSpace(filtro.TextoBuscaCliente))
            {
                if (filtro.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.Nome)
                {
                    clientes = clientes.Where(c => c.Cliente.PessoaFisica.NomeCompleto.Contains(filtro.TextoBuscaCliente));
                }
                else if (filtro.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.Email)
                {
                    clientes = clientes.Where(c => c.Cliente.PessoaFisica.Email == filtro.TextoBuscaCliente.ToLower());
                }
                else if (filtro.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.CPF)
                {
                    clientes = clientes.Where(
                        c =>
                            c.Cliente.PessoaFisica.Email == filtro.TextoBuscaCliente.ToLower() ||
                            c.Cliente.PessoaFisica.Contas.Any(
                                g => g.Email == filtro.TextoBuscaCliente.ToLower())
                    );
                }
                else if (filtro.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.Telefone)
                {
                    clientes = clientes.Where(c => c.Cliente.PessoaFisica.Telefones.Any(g => (g.Ddi + g.DDD + g.Numero).Contains(filtro.TextoBuscaCliente)));
                }
            }

            var clientesComDividas = (from ce in clientes
                                      join d in dividas on new { IdPessoaDoEstabelecimento = ce.Estabelecimento.PessoaJuridica.IdPessoa, IdPessoaDoCliente = ce.Cliente.PessoaFisica.IdPessoa }
                                                         equals new { d.IdPessoaDoEstabelecimento, d.IdPessoaDoCliente }
                                      where d.IdPessoaDoEstabelecimento == estabelecimento.PessoaJuridica.IdPessoa
                                                                   && (d.Status == DebitoParcial.StatusDaDivida.NenhumPagamentoRealizado || d.Status == DebitoParcial.StatusDaDivida.PagoParcialmente)
                                      group d by new { ClienteEstabelecimento = ce } into g
                                      select g).ToList();

            return (clientesComDividas.Select(g => new ClientesEmDebito
            {
                ClienteEstabelecimento = g.Key.ClienteEstabelecimento,
                Valor = g.Sum(kv => kv.ValorTotalFaltante),
                TotalCredito = g.Key.ClienteEstabelecimento.ValorCredito,
                SaldoDoCliente = (g.Key.ClienteEstabelecimento.ValorCredito - g.Sum(kv => kv.ValorTotalFaltante))
            }).ToList());
        }

        public IList<ClientesEmDebito> ListarClientesEmDebito(ClienteEstabelecimento clienteEstabelecimento)
        {
            if (clienteEstabelecimento == null)
                return new List<ClientesEmDebito>();

            var dividas = Queryable();
            var clientes = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();

            var clientesComDividas = (from d in dividas
                                      join ce in clientes on new { d.IdPessoaDoEstabelecimento, d.IdPessoaDoCliente }
                                                         equals new { IdPessoaDoEstabelecimento = ce.Estabelecimento.PessoaJuridica.IdPessoa, IdPessoaDoCliente = ce.Cliente.PessoaFisica.IdPessoa }
                                      where (d.Status == DebitoParcial.StatusDaDivida.NenhumPagamentoRealizado || d.Status == DebitoParcial.StatusDaDivida.PagoParcialmente)
                                         && ce.Codigo == clienteEstabelecimento.Codigo
                                      group d by new { d.IdPessoaDoCliente } into g
                                      select new ClientesEmDebito
                                      {
                                          ClienteEstabelecimento = clienteEstabelecimento,
                                          Valor = g.Sum(kv => kv.ValorTotalFaltante),
                                          Data = Calendario.Hoje(),
                                          EhDebitoParcial = true
                                          //TotalCredito = g.Key.ClienteEstabelecimento.ValorCredito,
                                          //SaldoDoCliente = (g.Key.ClienteEstabelecimento.ValorCredito - g.Sum(kv => kv.ValorTotalFaltante))
                                      }).ToList();

            return clientesComDividas;
        }

        #endregion Filtros Queryable

        public List<DividaDeixadaNoEstabelecimento> ListarTodasDoCliente(int idPessoaCliente)
        {
            return Queryable().Where(p => p.IdPessoaDoCliente == idPessoaCliente).ToList();
        }
    }
}