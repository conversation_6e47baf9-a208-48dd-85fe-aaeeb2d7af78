﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ControleDeFuncionalidades;
using Perlink.Trinks.ControleDeSatisfacao.DTO;
using Perlink.Trinks.ControleDeSatisfacao.Enums;
using Perlink.Trinks.Estabelecimentos.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Estabelecimentos.Services
{
    public class GerarPesquisaDeSatisfacaocaoService : BaseService, IGerarPesquisaDeSatisfacaoService
    {

        public void GerarPesquisaDeSatisfacao(Transacao transacao, Estabelecimento estabelecimento)
        {

            if (!EhParaGerarPesquisaDeSatisfacao(transacao, estabelecimento))
                return;

            var configuracaoDeAvaliacao = Domain.Estabelecimentos.ConfiguracaoDeAvaliacaoDeSatisfacaoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var pedidoDeAvaliacaoDeSatisfacaoDTO = new PedidoDeAvaliacaoDeSatisfacaoDTO();

            pedidoDeAvaliacaoDeSatisfacaoDTO.IdDono = transacao.PessoaQueRecebeu.IdPessoa;
            pedidoDeAvaliacaoDeSatisfacaoDTO.CanalDeComunicacao = CanalDeComunicacaoEnum.SMS;
            pedidoDeAvaliacaoDeSatisfacaoDTO.ContextoDaAvaliacao = (int)ContextoDaAvaliacaoEnum.ServicoPrestadoPeloEstabelecimento;
            pedidoDeAvaliacaoDeSatisfacaoDTO.PessoaAvaliador = transacao.PessoaQuePagou;
            pedidoDeAvaliacaoDeSatisfacaoDTO.TextoDaPergunta = ObterTextoDaPerguntaDeAvaliacaoDeSatisfacao(estabelecimento, transacao.PessoaQuePagou.NomeCompleto, transacao.DataHora, transacao.DataReferencia);
            pedidoDeAvaliacaoDeSatisfacaoDTO.DataHoraDoFechamentoDeContas = transacao.DataHora;

            List<string> descricoesDosServicos = new List<string>();

            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                if (ServicoEstaConfiguradoParaSerAvaliado(configuracaoDeAvaliacao, horarioTransacao))
                {
                    var servicoParaAvaliar = new ItemParaAvaliarDTO
                    {
                        TipoDoObjetoParaAvaliar = (int)TipoDeItemParaAvaliarEnum.ServicoEstabelecimento,
                        IdObjetoParaAvaliar = horarioTransacao.Horario.ServicoEstabelecimento.IdServicoEstabelecimento,
                        DescricaoDoObjeto = horarioTransacao.Horario.ServicoEstabelecimento.Nome
                    };
                    pedidoDeAvaliacaoDeSatisfacaoDTO.ItensParaAvaliar.Add(servicoParaAvaliar);

                    var profissional = horarioTransacao.Horario.Profissional;
                    if (profissional.PessoaFisica == null)
                    {
                        profissional = Domain.Pessoas.ProfissionalRepository.Load(profissional.IdProfissional);
                    }

                    string descricaoServicoPrestado = string.Format("{0} com {1} às {2}",
                        horarioTransacao.Horario.ServicoEstabelecimento.Nome,
                        profissional.PessoaFisica.ObterApelidoOuNome(),
                        horarioTransacao.Horario.DataInicio.ToString("H:mm").Replace(":00", "h"));

                    var horarioParaAvaliar = new ItemParaAvaliarDTO
                    {
                        TipoDoObjetoParaAvaliar = (int)TipoDeItemParaAvaliarEnum.Horario,
                        IdObjetoParaAvaliar = horarioTransacao.Horario.Id,
                        DescricaoDoObjeto = descricaoServicoPrestado
                    };
                    pedidoDeAvaliacaoDeSatisfacaoDTO.ItensParaAvaliar.Add(horarioParaAvaliar);

                    descricoesDosServicos.Add(descricaoServicoPrestado);

                    if (!ProfissionalJaEstaIncluidoNoPedidoDeAvaliacao(pedidoDeAvaliacaoDeSatisfacaoDTO, profissional))
                    {
                        var profissionalParaAvaliar = new ItemParaAvaliarDTO
                        {
                            TipoDoObjetoParaAvaliar = (int)TipoDeItemParaAvaliarEnum.Profissional,
                            IdObjetoParaAvaliar = profissional.IdProfissional,
                            DescricaoDoObjeto = profissional.PessoaFisica.ObterApelidoOuNome()
                        };

                        pedidoDeAvaliacaoDeSatisfacaoDTO.ItensParaAvaliar.Add(profissionalParaAvaliar);
                    }
                }
            }

            var fechamentoDeConta = new ItemParaAvaliarDTO
            {
                TipoDoObjetoParaAvaliar = (int)TipoDeItemParaAvaliarEnum.Transacao,
                IdObjetoParaAvaliar = transacao.Id,
                DescricaoDoObjeto = string.Join(", ", descricoesDosServicos)
            };

            pedidoDeAvaliacaoDeSatisfacaoDTO.ItemQueOriginouAvaliacao = fechamentoDeConta;

            pedidoDeAvaliacaoDeSatisfacaoDTO.Contatos = ObterTelefonesCelularesParaEnvioDeSmsDeAvaliacaoDeSatisfacao(transacao);

            Domain.ControleDeSatisfacao.AvaliacaoDeSatisfacaoService.SalvarNovaAvaliacaoDeSatisfacaoASerEnviada(pedidoDeAvaliacaoDeSatisfacaoDTO);
        }

        private static bool ProfissionalJaEstaIncluidoNoPedidoDeAvaliacao(PedidoDeAvaliacaoDeSatisfacaoDTO pedidoDeAvaliacaoDeSatisfacaoDTO, Profissional profissional)
        {
            return pedidoDeAvaliacaoDeSatisfacaoDTO.ItensParaAvaliar.Any(ipa => ipa.IdObjetoParaAvaliar == profissional.IdProfissional && ipa.TipoDoObjetoParaAvaliar == (int)TipoDeItemParaAvaliarEnum.Profissional);
        }

        private bool ServicoEstaConfiguradoParaSerAvaliado(ConfiguracaoDeAvaliacaoDeSatisfacao configuracaoDeAvaliacao, HorarioTransacao horarioTransacao)
        {
            return configuracaoDeAvaliacao.ItensParaAvaliar
                .Any(ipa => ipa.PermiteAvaliar
                         && ipa.ObterTipo() == TipoDeItemParaAvaliarEnum.ServicoEstabelecimento
                         && ipa.ObterIdDoItem() == horarioTransacao.Horario.ServicoEstabelecimento.IdServicoEstabelecimento);
        }

        public string ObterTextoDaPerguntaDeAvaliacaoDeSatisfacao(Estabelecimento estabelecimento, string nomeCliente, DateTime dataHoraPagamento, DateTime dataReferencia)
        {
            var previewMensagem = new Configuracao<string>(ControleDeFuncionalidades.Enums.ConfiguracaoEnum.mensagem_pesquisa_satisfacao).ObterValor(estabelecimento);
            return MensagemTratada(estabelecimento, nomeCliente, dataHoraPagamento, dataReferencia, previewMensagem);
        }

        public string MensagemTratada(Estabelecimento estabelecimento, string nomeCliente, DateTime dataHoraPagamento, DateTime dataReferencia, string previewMensagem)
        {
            if (estabelecimento.EhUmEstabelecimentoFranqueadoAtivo())
                previewMensagem = previewMensagem.Replace("[estabelecimento/franquia]", estabelecimento.FranquiaEstabelecimento.Franquia.Nome);
            else
                previewMensagem = previewMensagem.Replace("[estabelecimento/franquia]", estabelecimento.NomeDeExibicaoNoPortal);

            if (previewMensagem.Contains("[estabelecimento]"))
                previewMensagem = previewMensagem.Replace("[estabelecimento]", estabelecimento.NomeDeExibicaoNoPortal);

            previewMensagem = previewMensagem
                .Replace("[ em dd/MM]", dataHoraPagamento.Date == dataReferencia.Date ? "" : string.Format(" em {0}", dataReferencia.ToString("dd/MM")))
                .Replace("[cliente]", nomeCliente.ObterPrimeiroNome())
                .RemoverAcentos(); // deixar sempre por último

            var perguntaComExplicacaoDasNotas = previewMensagem.Replace("[1 a 5]", "1 (muito insatisfeito) a 5 (muito satisfeito)");
            if (perguntaComExplicacaoDasNotas.Length <= 160)
                previewMensagem = perguntaComExplicacaoDasNotas;

            return previewMensagem;
        }

        private bool EhParaGerarPesquisaDeSatisfacao(Transacao transacao, Estabelecimento estabelecimento)
        {
            bool ehParaGerarPesquisa = false;

            if (estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitarPesquisaDeSatisfacao)
            {
                var configuracaoDeAvaliacao = Domain.Estabelecimentos.ConfiguracaoDeAvaliacaoDeSatisfacaoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);

                if (configuracaoDeAvaliacao != null && configuracaoDeAvaliacao.PermiteQueClientesAvaliemOServico)
                {

                    bool clientePossuiTelefoneCelular = ObterTelefonesCelularesParaEnvioDeSmsDeAvaliacaoDeSatisfacao(transacao).Any();

                    if (clientePossuiTelefoneCelular && ClienteRecebeAvaliacaoDeSatisfacao(transacao, estabelecimento))
                    {

                        if (!TransacaoJaTevePesquisaDeSatisfacaoGerada(transacao))
                        {
                            ehParaGerarPesquisa = transacao.HorariosTransacoes.Any(ht => ServicoEstaConfiguradoParaSerAvaliado(configuracaoDeAvaliacao, ht) && !EhAtendimentoFuturo(ht));
                        }
                    }
                }
            }

            return ehParaGerarPesquisa;
        }

        private bool TransacaoJaTevePesquisaDeSatisfacaoGerada(Transacao transacao)
        {
            int[] idsHorariosDaTransacao = transacao.HorariosTransacoes.Select(ht =>
                                            ht.Horario.Id != 0 ? ht.Horario.Id :
                                            ht.Horario.Codigo.HasValue ? ht.Horario.Codigo.Value : 0).ToArray();
            var JaFoiRealizadoOEnvioParaOClienteHoje = Domain.ControleDeSatisfacao.AvaliacaoDeSatisfacaoRepository.Queryable()
                .Any(ipa => ipa.IdDono == transacao.PessoaQueRecebeu.IdPessoa
                         && ipa.PessoaAvaliador.IdPessoa == transacao.PessoaQuePagou.IdPessoa
                         && ipa.DataHoraQueFoiCriado.Date == transacao.DataHora.Date
                          );

            var jaExisteHorarioDaTransacaoParaAvaliar = Domain.ControleDeSatisfacao.ItemParaAvaliarRepository.Queryable()
                .Any(ipa => ipa.IdDono == transacao.PessoaQueRecebeu.IdPessoa
                         && ipa.ContextoDaAvaliacao == (int)ContextoDaAvaliacaoEnum.ServicoPrestadoPeloEstabelecimento
                         && ipa.TipoDoObjetoParaAvaliar == (int)TipoDeItemParaAvaliarEnum.Horario
                         && idsHorariosDaTransacao.Contains(ipa.IdDoObjetoParaAvaliar));

            return jaExisteHorarioDaTransacaoParaAvaliar || JaFoiRealizadoOEnvioParaOClienteHoje;
        }

        private bool EhAtendimentoFuturo(HorarioTransacao ht)
        {
            return ht.DataHoraInicioHorario > Calendario.Hoje().AddDays(1);
        }

        private bool ClienteRecebeAvaliacaoDeSatisfacao(Transacao transacao, Estabelecimento estabelecimento)
        {
            return Domain.Pessoas.ClienteEstabelecimentoRepository.ClienteRecebeSmsMarketing(transacao.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);
        }

        private List<ContatoDTO> ObterTelefonesCelularesParaEnvioDeSmsDeAvaliacaoDeSatisfacao(Transacao transacao)
        {
            var telefones = new List<Telefone>();

            var celularesCadastradosPeloEstabelecimento = transacao.PessoaQuePagou.Telefones
                .FiltrarPorDono(transacao.PessoaQueRecebeu.IdPessoa)
                .IdentificadosComoCelular()
                .TelefonesNacionais()
                .Ativos();

            var celularesCadastradosPeloClienteWeb = transacao.PessoaQuePagou.Telefones
                .FiltrarPorDono(transacao.PessoaQuePagou.IdPessoa)
                .IdentificadosComoCelular()
                .TelefonesNacionais()
                .Ativos();

            telefones.AddRange(celularesCadastradosPeloEstabelecimento);
            telefones.AddRange(celularesCadastradosPeloClienteWeb);

            var contatos = telefones
                .Where(t => t.Ddi == DdiConstants.Brasil)
                .Where(t => t.DDD.Length == 2 && t.Numero.EhUmTelefoneCelular())
                .Select(t => t.DDD + ";" + t.Numero)
                .Distinct()
                .Select(t => new ContatoDTO(t.Split(';')[0], t.Split(';')[1], TipoDeContatoEnum.TelefoneCelular))
                .ToList();

            return contatos;
        }
    }
}
