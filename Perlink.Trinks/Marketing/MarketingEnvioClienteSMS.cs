using Castle.ActiveRecord;
using Perlink.Trinks.Notificacoes;

namespace Perlink.Trinks.Marketing
{

    [ActiveRecord(DiscriminatorValue = "SMS")]
    public class MarketingEnvioClienteSMS : MarketingEnvioCliente
    {

        public MarketingEnvioClienteSMS()
        {
        }

        [Property("ddi_telefone")]
        public virtual string DdiTelefone { get; set; }

        [Property("ddd_telefone")]
        public virtual string DDDTelefone { get; set; }

        [Property("id_mensagem_gateway")]
        public string IdMensagemGateway { get; set; }

        [Property("status_gateway")]
        public string StatusGateway { get; set; }

        [Property("numero_telefone")]
        public virtual string NumeroTelefone { get; set; }

        [BelongsTo("id_operadora")]
        public virtual Operadora Operadora { get; set; }

        public override bool Equals(object obj)
        {
            var objTipado = obj as MarketingEnvioClienteSMS;
            if (objTipado == null) return false;

            return DDDTelefone == objTipado.DDDTelefone
                   && NumeroTelefone == objTipado.NumeroTelefone
                   && base.Equals(objTipado);
        }
    }
}