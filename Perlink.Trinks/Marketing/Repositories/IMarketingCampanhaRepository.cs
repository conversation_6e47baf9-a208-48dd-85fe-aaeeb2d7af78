﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Marketing.Enums;
using Perlink.Trinks.Marketing.Filtro;
using Perlink.Trinks.Marketing.Filtros;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Marketing.Repositories
{

    public partial interface IMarketingCampanhaRepository
    {

        DateTime? DataUltimaPendenciaResolvidaENaoEnviada(int idMarketingCampanha);

        bool ExisteCampanhaComMesmoNome(String nomeCampanha, int idEstabelecimento, MarketingCampanha marketingCampanha, int idCampanhaDiferente = 0);

        List<MarketingCampanha> Listar(FiltroCampanha filtro);

        List<string> ListarEmailsDeClientesParaDisparoDaCampanha(MarketingCampanha campanha);

        List<int> ListarTelefonesDeClientesParaDisparoDaCampanha(MarketingCampanha campanha);

        List<MarketingCampanha> ObterCampanhasPendentesDeEnvio(DateTime dataReferencia);

        IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingCampanha campanha);

        IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingCampanhaPublicoAlvo publicoAlvo, TipoCampanhaMarketingEnum tipoCampanha, int idEstabelecimento);

        IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingCampanhaPublicoAlvo publicoAlvo, TipoCampanhaMarketingEnum tipoCampanha, int idEstabelecimento, DateTime data);

        IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingEnvio marketingEnvio);

        IQueryable<ClienteEstabelecimento> ObterClientesQuePodemReceberEmail(List<int> idsClientes, int idEstabelecimento);

        IQueryable<ClienteEstabelecimento> ObterClientesQuePodemReceberSMS(List<int> idsClientes, int idEstabelecimento);

        IQueryable<ClienteEstabelecimento> AplicarFiltroPersonalizadoDeClientes(IQueryable<ClienteEstabelecimento> clientes, FiltroPersonalizadoDeClientes filtro);

        MarketingCampanha ObterPorEstabelecimentoECampanhaDoTrinks(int idEstabelecimento, MarketingCampanhaDoTrinksEnum campanhaTrinks);

        IQueryable<ResultadoPublicoAlvoDTO> FiltrarClientesEstabelecimentoPersonalizado(MarketingCampanha campanha, MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento);

        IQueryable<ResultadoPublicoAlvoDTO> FiltrarClientesEstabelecimentoPersonalizado(MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento, int idEstabelecimento);

        IQueryable<ClienteEstabelecimento> FiltrarPorClientesSelecionados(MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento);
    }
}
