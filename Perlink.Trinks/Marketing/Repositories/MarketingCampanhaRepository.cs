﻿using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Marketing.Enums;
using Perlink.Trinks.Marketing.Factories;
using Perlink.Trinks.Marketing.Filtro;
using Perlink.Trinks.Marketing.Filtros;
using Perlink.Trinks.Marketing.Strategies;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Pessoas.VO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Marketing.Repositories
{

    public partial class MarketingCampanhaRepository
    {

        public DateTime? DataUltimaPendenciaResolvidaENaoEnviada(int idMarketingCampanha)
        {
            var ultimaReprogramacao =
                Domain.Marketing.MarketingEnvioRepository.Queryable()
                    .FirstOrDefault(
                        f =>
                            f.MarketingCampanha.IdMarketingCampanha == idMarketingCampanha
                            && f.EnvioOriginal != null
                            && (f.EnvioOriginal.StatusMarketingEnvio == StatusMarketingEnvioEnum.PendenteCredito || f.EnvioOriginal.StatusMarketingEnvio == StatusMarketingEnvioEnum.EnvioSuspenso)
                            && f.DataHoraEnvio == null);

            if (ultimaReprogramacao == null)
                return null;

            return ultimaReprogramacao.EnvioOriginal.DataHoraProgramada;
        }

        public bool ExisteCampanhaComMesmoNome(String nomeCampanha, int idEstabelecimento, MarketingCampanha marketingCampanha, int idCampanhaDiferente = 0)
        {
            return Domain.Marketing.MarketingCampanhaRepository.Queryable()
                    .Any(p => p.NomeCampanha.ToUpper().Trim() == nomeCampanha.ToUpper().Trim()
                              && p.IdMarketingCampanha != idCampanhaDiferente
                              && p.Estabelecimento.IdEstabelecimento == idEstabelecimento
                              && ((p is MarketingCampanhaSMS && marketingCampanha is MarketingCampanhaSMS) ||
                              (p is MarketingCampanhaEmail && marketingCampanha is MarketingCampanhaEmail) ||
                              (p is MarketingCampanhaWhatsApp && marketingCampanha is MarketingCampanhaWhatsApp)));
        }

        public List<MarketingCampanha> Listar(FiltroCampanha filtro)
        {
            var retorno = Queryable()
                .PorEstabelecimento(filtro.IdEstabelecimento)
                .ApenasPendentesDeDisparo(filtro.ApenasPendentesDeDisparo)
                .PorDataDeEnvios(filtro.DataInicio, filtro.DataFim)
                .PorTextoNoNomeOuConteudo(filtro.CampoPesquisa);

            // Caso seja necessário ordenar por mais de dois campos, substituir o trecho abaixo pela verificação por um enum, strategy ou similares.
            if (filtro.OrdenacaoPadrao)
                retorno = retorno.OrderByDescending(x => x.DataCriacao);
            else
                retorno = retorno.OrderBy(x => x.NomeCampanha);

            return retorno.Paginado(filtro).ToList();
        }

        public List<string> ListarEmailsDeClientesParaDisparoDaCampanha(MarketingCampanha campanha)
        {
            var emailsDeClientesparaDisparo = Domain.Marketing.MarketingEnvioClienteRepository.Queryable().
                Where(f => f.MarketingEnvio.MarketingCampanha == campanha).
                Select(g => ((MarketingEnvioClienteEmail)g).EmailClienteEstabelecimento).ToList();

            if (emailsDeClientesparaDisparo.Any())
                return emailsDeClientesparaDisparo;

            return null;
        }

        public List<int> ListarTelefonesDeClientesParaDisparoDaCampanha(MarketingCampanha campanha)
        {
            var listaDeTelefonesDeClientes = Domain.Marketing.MarketingEnvioClienteRepository.Queryable().
                Where(f => f.MarketingEnvio.MarketingCampanha == campanha).
                Select(g => ((MarketingEnvioClienteSMS)g).NumeroTelefone).ToList();

            if (listaDeTelefonesDeClientes.Any())
            {
                var listaDeTelefonesDeClientesValidoParaDisparo = new List<int>();

                listaDeTelefonesDeClientes.ForEach(i =>
                {
                    int j;
                    if (Int32.TryParse(i.Substring(0, 1), out j))
                    {
                        if (j >= 6)
                        {
                            int z;
                            if (Int32.TryParse(i, out z) && (i.Length >= 8 && i.Length <= 9))
                                listaDeTelefonesDeClientesValidoParaDisparo.Add(z);
                        }
                    }
                });

                if (listaDeTelefonesDeClientesValidoParaDisparo.Any())
                    return listaDeTelefonesDeClientesValidoParaDisparo;
            }

            return null;
        }

        public List<MarketingCampanha> ObterCampanhasPendentesDeEnvio(DateTime dataReferencia)
        {
            return Queryable().Where(f => f.ProximoEnvio.HasValue &&
                                          f.ProximoEnvio.Value <= dataReferencia &&
                                          f.StatusCampanha != StatusCampanhaEnum.EnvioFinalizado &&
                                          f.StatusCampanha != StatusCampanhaEnum.EnvioEmCurso).ToList();
        }

        public IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingCampanha campanha)
        {
            var publicoAlvo = campanha.MarketingCampanhaPublicoAlvo;

            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimentoQueryable(
                    campanha.Estabelecimento.IdEstabelecimento);

            if (campanha is MarketingCampanhaSMS)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoQuePodemReceberSMS(clientesEstabelecimento);
            else if (campanha is MarketingCampanhaEmail)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoQuePodemReceberEmail(clientesEstabelecimento);
            else if (campanha is MarketingCampanhaWhatsApp)
                Console.WriteLine("valor");
            else
                throw new Exception("Campanha não é do tipo WPP, nem SMS e nem e-mail");

            IFiltroPublicoAlvoStrategy filtroPublicoAlvoStrategy = PublicoAlvoFactory.Criar(publicoAlvo.TipoPublicoAlvo);
            return filtroPublicoAlvoStrategy.FiltrarPublicoAlvo(clientesEstabelecimento, campanha);
        }

        public IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingCampanhaPublicoAlvo publicoAlvo,
            TipoCampanhaMarketingEnum tipoCampanha, int idEstabelecimento)
        {
            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimentoQueryable(idEstabelecimento);

            if (tipoCampanha == TipoCampanhaMarketingEnum.SMS)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoQuePodemReceberSMS(clientesEstabelecimento);
            else if (tipoCampanha == TipoCampanhaMarketingEnum.Email)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoQuePodemReceberEmail(clientesEstabelecimento);
            else if (tipoCampanha == TipoCampanhaMarketingEnum.WhatsApp)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoComNumeroNacionalValido(clientesEstabelecimento);
            else
                throw new Exception("Campanha não é do tipo SMS nem e-mail");

            IFiltroPublicoAlvoStrategy filtroPublicoAlvoStrategy = PublicoAlvoFactory.Criar(publicoAlvo.TipoPublicoAlvo);
            return filtroPublicoAlvoStrategy.FiltrarPublicoAlvo(clientesEstabelecimento, publicoAlvo, idEstabelecimento);
        }

        public IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingCampanhaPublicoAlvo publicoAlvo,
                    TipoCampanhaMarketingEnum tipoCampanha, int idEstabelecimento, DateTime data)
        {
            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimentoQueryable(idEstabelecimento);

            if (tipoCampanha == TipoCampanhaMarketingEnum.WhatsApp)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoComNumeroNacionalValido(clientesEstabelecimento);
            else
                throw new Exception("Campanha não é do tipo SMS nem e-mail");

            return FiltrarClientesEstabelecimentoPersonalizado(publicoAlvo, clientesEstabelecimento, idEstabelecimento, data);
        }

        public IQueryable<ResultadoPublicoAlvoDTO> ListarResultadoDoPublicoAlvo(MarketingEnvio marketingEnvio)
        {
            var campanha = marketingEnvio.MarketingCampanha;
            var publicoAlvo = campanha.MarketingCampanhaPublicoAlvo;

            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimentoQueryable(
                    campanha.Estabelecimento.IdEstabelecimento);

            if (campanha is MarketingCampanhaSMS)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoQuePodemReceberSMS(clientesEstabelecimento);
            else if (campanha is MarketingCampanhaEmail)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoQuePodemReceberEmail(clientesEstabelecimento);
            else if (campanha is MarketingCampanhaWhatsApp)
                clientesEstabelecimento = FiltrarClientesEstabelecimentoComNumeroNacionalValido(clientesEstabelecimento);
            else
                throw new Exception("Campanha não é do tipo WPP, nem SMS e nem e-mail");

            IFiltroPublicoAlvoStrategy filtroPublicoAlvoStrategy = PublicoAlvoFactory.Criar(publicoAlvo.TipoPublicoAlvo);
            return filtroPublicoAlvoStrategy.FiltrarPublicoAlvo(clientesEstabelecimento, campanha);
        }

        public IQueryable<ClienteEstabelecimento> ObterClientesQuePodemReceberEmail(List<int> idsClientes, int idEstabelecimento)
        {
            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimentoQueryable(
                    idEstabelecimento).Where(c => idsClientes.Contains(c.Codigo));

            return FiltrarClientesEstabelecimentoQuePodemReceberEmail(clientesEstabelecimento);
        }

        public IQueryable<ClienteEstabelecimento> ObterClientesQuePodemReceberSMS(List<int> idsClientes, int idEstabelecimento)
        {
            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimentoQueryable(
                    idEstabelecimento).Where(c => idsClientes.Contains(c.Codigo));

            return FiltrarClientesEstabelecimentoQuePodemReceberSMS(clientesEstabelecimento);
        }

        public IQueryable<ClienteEstabelecimento> FiltrarPorClientesSelecionados(MarketingCampanhaPublicoAlvo publicoAlvo,
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var listaClientesEstabelecimentoIndicados =
                Domain.Marketing.MarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository.Queryable()
                    .Where(
                        p =>
                            p.MarketingCampanhaPublicoAlvo.IdMarketingCampanhaPublicoAlvo ==
                            publicoAlvo.IdMarketingCampanhaPublicoAlvo)
                    .Select(p => p.ClienteEstabelecimento);
            clientesEstabelecimento =
                clientesEstabelecimento.Where(p => listaClientesEstabelecimentoIndicados.Contains(p));
            return clientesEstabelecimento;
        }

        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarClientesEstabelecimentoPersonalizado(
            MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento,
            int idEstabelecimento)
        {

            var filtroPersonalizado = new FiltroPersonalizadoDeClientes
            {
                IdEstabelecimento = idEstabelecimento,
                IdadeMinima = publicoAlvo.IdadeMinima,
                IdadeMaxima = publicoAlvo.IdadeMaxima,
                GenerosSelecionados = GeneroHelper.ObterListaDeGenerosPorString(publicoAlvo.Sexo),
                TipoPublicoAlvoServico = publicoAlvo.TipoPublicoAlvoServico,
                DiasAntecedenciaServicoFeito = publicoAlvo.DiasAntecedenciaServicoFeito,
                DataInicioServicoRealizado = publicoAlvo.DataInicioServicoRealizado,
                DataFimServicoRealizado = publicoAlvo.DataFimServicoRealizado,
                TipoDataServico = publicoAlvo.TipoDataServico,
                IdsServicosRealizados = publicoAlvo.ServicosRealizados.Select(f => f.ServicoEstabelecimento.IdServicoEstabelecimento).ToList(),
                IdsServicosSemAgendamentoFuturo = publicoAlvo.ServicosSemAgendamentoFuturo.Select(s => s.ServicoEstabelecimento.IdServicoEstabelecimento).ToList(),
                IdsProfissionais = publicoAlvo.MarketingCampanhaPublicoAlvoProfissional.Select(p => p.Profissional.IdProfissional).ToList(),
                PeriodoUltimaVisitaInicio = publicoAlvo.PeriodoUltimaVisitaInicio,
                PeriodoUltimaVisitaFim = publicoAlvo.PeriodoUltimaVisitaFim
            };


            var ehConviteDeRetorno = publicoAlvo.TipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueTenhamFeitoServicoEspecifico && publicoAlvo.TipoDataServico == PublicoAlvoTipoDataServicoEnum.HaXDias;

            if (ehConviteDeRetorno)
            {

                if (publicoAlvo.SemAgendamentoFuturoDoServico)
                {
                    clientesEstabelecimento = FiltrarSemAgendamentoFuturoDoServico(filtroPersonalizado, clientesEstabelecimento);
                }

                return FiltrarQueTenhaFeitoServicoEspecificoHaXDias(publicoAlvo, clientesEstabelecimento, idEstabelecimento).AsQueryable();
            }

            clientesEstabelecimento = AplicarFiltroPersonalizadoDeClientes(clientesEstabelecimento, filtroPersonalizado);

            return clientesEstabelecimento.Select(f => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = f });
        }

        private IQueryable<ResultadoPublicoAlvoDTO> FiltrarClientesEstabelecimentoPersonalizado(
                    MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento,
                    int idEstabelecimento, DateTime data)
        {

            var filtroPersonalizado = new FiltroPersonalizadoDeClientes
            {
                IdEstabelecimento = idEstabelecimento,
                IdadeMinima = publicoAlvo.IdadeMinima,
                IdadeMaxima = publicoAlvo.IdadeMaxima,
                GenerosSelecionados = GeneroHelper.ObterListaDeGenerosPorString(publicoAlvo.Sexo),
                TipoPublicoAlvoServico = publicoAlvo.TipoPublicoAlvoServico,
                DiasAntecedenciaServicoFeito = publicoAlvo.DiasAntecedenciaServicoFeito,
                DataInicioServicoRealizado = publicoAlvo.DataInicioServicoRealizado,
                DataFimServicoRealizado = publicoAlvo.DataFimServicoRealizado,
                TipoDataServico = publicoAlvo.TipoDataServico,
                IdsServicosRealizados = publicoAlvo.ServicosRealizados.Select(f => f.ServicoEstabelecimento.IdServicoEstabelecimento).ToList(),
                IdsServicosSemAgendamentoFuturo = publicoAlvo.ServicosSemAgendamentoFuturo.Select(s => s.ServicoEstabelecimento.IdServicoEstabelecimento).ToList(),
                IdsProfissionais = publicoAlvo.MarketingCampanhaPublicoAlvoProfissional.Select(p => p.Profissional.IdProfissional).ToList(),
                PeriodoUltimaVisitaInicio = publicoAlvo.PeriodoUltimaVisitaInicio,
                PeriodoUltimaVisitaFim = publicoAlvo.PeriodoUltimaVisitaFim
            };

            clientesEstabelecimento = FiltrarSemAgendamentoFuturoDoServico(filtroPersonalizado, clientesEstabelecimento, data);

            return FiltrarQueTenhaFeitoServicoEspecificoHaXDias(publicoAlvo, clientesEstabelecimento, idEstabelecimento, data).AsQueryable();
        }


        #region Filtros personalizados
        public IQueryable<ClienteEstabelecimento> AplicarFiltroPersonalizadoDeClientes(IQueryable<ClienteEstabelecimento> clientes, FiltroPersonalizadoDeClientes filtro)
        {

            if (filtro.TipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueTenhamFeitoServicoEspecifico && filtro.TipoDataServico == PublicoAlvoTipoDataServicoEnum.HaXDias)
                throw new ArgumentException("Este método não possui suporte a esse tipo de consulta. Utilizar: FiltrarQueTenhaFeitoServicoEspecificoHaXDias");

            if (filtro.IdadeMinima > 0)
            {
                var dataNascimentoMaxima = Calendario.Hoje().AddYears(-filtro.IdadeMinima.Value);
                clientes = clientes.Where(f => f.Cliente.PessoaFisica.DataNascimento <= dataNascimentoMaxima.Date);
            }

            if (filtro.IdadeMaxima > 0 && filtro.IdadeMaxima < 99)
            {
                var dataNascimentoMinima = Calendario.Hoje().AddYears(-(filtro.IdadeMaxima.Value + 1)).AddDays(+1);
                clientes = clientes.Where(f => f.Cliente.PessoaFisica.DataNascimento >= dataNascimentoMinima.Date);
            }

            if (filtro.GenerosSelecionados != null && filtro.GenerosSelecionados.Any())
            {
                clientes = clientes.FiltrarPorGeneros(filtro.GenerosSelecionados);
            }

            if (filtro.IdsServicosSemAgendamentoFuturo != null && filtro.IdsServicosSemAgendamentoFuturo.Any())
            {
                clientes = FiltrarSemAgendamentoFuturoDoServico(filtro, clientes);
            }

            if (filtro.PeriodoUltimaVisitaInicio.HasValue && filtro.PeriodoUltimaVisitaFim.HasValue)
            {
                clientes = FiltrarQueTenhamFeitoUltimaVisitaEmPeriodoEspecifico(clientes, filtro);
            }

            var tipoPublicoAlvoServico = filtro.TipoPublicoAlvoServico;

            if (tipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueTenhamFeitoAlgumServico)
            {
                clientes = FiltrarQueTenhamFeitoAlgumServico(filtro, clientes);
            }
            else if (tipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueTenhamFeitoServicoEspecifico)
            {
                clientes = FiltrarQueTenhamFeitoServicoEspecifico(filtro, clientes);
            }
            else if (tipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueNaoTenhamFeitoNenhumServico)
            {
                clientes = FiltrarQueNaoTenhamFeitoNenhumServico(filtro, clientes);
            }
            else if (tipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueNaoTenhamFeitoServicoEspecifico)
            {
                clientes = FiltrarQueNaoTenhamFeitoServicoEspecifico(filtro, clientes);
            }
            else if (tipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueTenhamFeitoServicoComProfissional)
            {
                clientes = FiltrarQueTenhamOuNaoFeitoServicoComProfissional(filtro, clientes, true);
            }
            else if (tipoPublicoAlvoServico == TipoPublicoAlvoServicoEnum.QueNaoTenhamFeitoServicoComProfissional)
            {
                clientes = FiltrarQueTenhamOuNaoFeitoServicoComProfissional(filtro, clientes, false);
            }

            return clientes;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarQueTenhamFeitoUltimaVisitaEmPeriodoEspecifico(IQueryable<ClienteEstabelecimento> clientes, FiltroPersonalizadoDeClientes filtro)
        {
            var fechamentos = Domain.Financeiro.TransacaoRepository.Queryable();

            clientes = from f in clientes
                       let ultimaVisita = fechamentos
                                .Where(fech => fech.PessoaQueRecebeu.IdPessoa == f.Estabelecimento.PessoaJuridica.IdPessoa
                                            && fech.PessoaQuePagou.IdPessoa == f.Cliente.PessoaFisica.IdPessoa
                                            && fech.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento
                                            && fech.TransacaoQueEstounouEsta == null)
                                .Max(fech => fech.DataReferencia)
                       where ultimaVisita >= filtro.PeriodoUltimaVisitaInicio.Value.Date
                          && ultimaVisita < filtro.PeriodoUltimaVisitaFim.Value.AddDays(1).Date
                       select f;

            return clientes;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarQueTenhamOuNaoFeitoServicoComProfissional(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento, bool fezServicoComProfissional)
        {
            var idProfissionaisPublicoAlvo = filtro.IdsProfissionais;

            var horarios = Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(f => f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento
                        && idProfissionaisPublicoAlvo.Contains(f.Profissional.IdProfissional));

            if (filtro.TipoDataServico == PublicoAlvoTipoDataServicoEnum.HaXDias)
                throw new Exception("Este método não dá suporte a PublicoAlvoTipoDataServicoEnum.HaXDias");

            horarios = FiltrarPorTipoDataServico(filtro, horarios);

            if (fezServicoComProfissional)
                clientesEstabelecimento = ClientesContidosNosHorarios(clientesEstabelecimento, horarios);
            else
                clientesEstabelecimento = ClientesNaoContidosNosHorarios(clientesEstabelecimento, horarios);

            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarSemAgendamentoFuturoDoServico(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var idsServicosEstabelecimento = filtro.IdsServicosSemAgendamentoFuturo;

            var horarios =
                Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(
                        f =>
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            idsServicosEstabelecimento.Contains(f.ServicoEstabelecimento.IdServicoEstabelecimento));

            horarios = horarios.Where(f => f.DataInicio > Calendario.Hoje().AddDays(1));

            clientesEstabelecimento = ClientesNaoContidosNosHorarios(clientesEstabelecimento, horarios);
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarSemAgendamentoFuturoDoServico(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento, DateTime data)
        {
            var idsServicosEstabelecimento = filtro.IdsServicosSemAgendamentoFuturo;

            var horarios =
                Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(
                        f =>
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            idsServicosEstabelecimento.Contains(f.ServicoEstabelecimento.IdServicoEstabelecimento));

            horarios = horarios.Where(f => f.DataInicio > data.AddDays(1));

            clientesEstabelecimento = ClientesNaoContidosNosHorarios(clientesEstabelecimento, horarios);
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarQueNaoTenhamFeitoServicoEspecifico(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var idsServicosEstabelecimento = filtro.IdsServicosRealizados;

            var horarios =
                Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(
                        f =>
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            idsServicosEstabelecimento.Contains(
                                f.ServicoEstabelecimento.IdServicoEstabelecimento));

            if (filtro.TipoDataServico == PublicoAlvoTipoDataServicoEnum.HaXDias)
                throw new Exception("Este método não dá suporte a PublicoAlvoTipoDataServicoEnum.HaXDias");

            horarios = FiltrarPorTipoDataServico(filtro, horarios);
            clientesEstabelecimento = ClientesNaoContidosNosHorarios(clientesEstabelecimento, horarios);

            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarQueNaoTenhamFeitoNenhumServico(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var horarios =
                                Domain.Pessoas.HorarioRepository.Queryable()
                                    .Where(f => f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento);
            horarios = FiltrarPorTipoDataServico(filtro, horarios);

            clientesEstabelecimento = ClientesNaoContidosNosHorarios(clientesEstabelecimento, horarios);
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarQueTenhamFeitoServicoEspecifico(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var idsServicosEstabelecimento = filtro.IdsServicosRealizados;

            var horarios =
                Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(
                        f =>
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            idsServicosEstabelecimento.Contains(f.ServicoEstabelecimento.IdServicoEstabelecimento) &&
                            (f.Status.Codigo != (int)StatusHorarioEnum.Cancelado && f.Status.Codigo != (int)StatusHorarioEnum.Cliente_Faltou));

            horarios = FiltrarPorTipoDataServico(filtro, horarios);

            clientesEstabelecimento = ClientesContidosNosHorarios(clientesEstabelecimento, horarios);
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarQueTenhamFeitoAlgumServico(FiltroPersonalizadoDeClientes filtro, IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var horarios = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento && (f.Status.Codigo != (int)StatusHorarioEnum.Cancelado && f.Status.Codigo != (int)StatusHorarioEnum.Cliente_Faltou));
            horarios = FiltrarPorTipoDataServico(filtro, horarios);

            clientesEstabelecimento = ClientesContidosNosHorarios(clientesEstabelecimento, horarios);
            return clientesEstabelecimento;
        }

        private static IQueryable<Horario> FiltrarPorTipoDataServico(FiltroPersonalizadoDeClientes filtro,
            IQueryable<Horario> horarios)
        {
            switch (filtro.TipoDataServico)
            {
                case PublicoAlvoTipoDataServicoEnum.DentroDoPeriodo:
                    horarios = HorariosDentroDoPeriodo(horarios, filtro.DataInicioServicoRealizado, filtro.DataFimServicoRealizado);
                    break;

                case PublicoAlvoTipoDataServicoEnum.NosUltimosXDias:
                    horarios = HorariosNosUltimosDias(horarios, filtro.DiasAntecedenciaServicoFeito);
                    break;
            }
            return horarios;
        }

        #endregion

        public static List<ResultadoPublicoAlvoDTO> FiltrarQueTenhaFeitoServicoEspecificoHaXDias(
            MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento,
            int idEstabelecimento)
        {
            if (publicoAlvo.TipoPublicoAlvoServico != TipoPublicoAlvoServicoEnum.QueTenhamFeitoServicoEspecifico)
                throw new Exception("Este método somente dá suporte a TipoPublicoAlvoServicoEnum.QueNaoTenhamFeitoServicoEspecifico");

            if (publicoAlvo.TipoDataServico != PublicoAlvoTipoDataServicoEnum.HaXDias)
                throw new Exception("Este método somente dá suporte a PublicoAlvoTipoDataServicoEnum.HaXDias");

            var retorno = new List<ResultadoPublicoAlvoDTO>();

            // Pegando somente os agendamentos NÃO CANCELADOS do estabelecimento
            var horariosQualquerServico = Domain.Pessoas.HorarioRepository.Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Status != StatusHorarioEnum.Cancelado && f.Status != StatusHorarioEnum.Cliente_Faltou);

            foreach (var s in publicoAlvo.ServicosRealizados)
            {
                var data = Calendario.Hoje().AddDays(-s.DiasAtrasClienteFezOServico ?? 0);

                // Pegando somente os agendamentos do serviço
                var horariosDoServico = horariosQualquerServico.Where(f => f.ServicoEstabelecimento == s.ServicoEstabelecimento);

                var clientesFiltrados = clientesEstabelecimento;
                clientesFiltrados = FitrarClientesComServicoFinalizadoNodia(data, horariosDoServico, clientesFiltrados);

                if (s.QueNaoTenhaFeitoOutroServico)
                {
                    clientesFiltrados = FiltrarClientesQueNaoTenhamFinalizadoServicosAposAData(horariosQualquerServico, data, clientesFiltrados);
                    clientesFiltrados = FiltrarClientesSemAgendamentoFuturo(horariosQualquerServico, clientesFiltrados);
                }
                else
                {
                    clientesFiltrados = FiltrarClientesSemAgendamentoFuturo(horariosDoServico, clientesFiltrados);
                }

                retorno.AddRange(clientesFiltrados.Select(ce => new ResultadoPublicoAlvoDTO
                {
                    ClienteEstabelecimento = ce,
                    Variaveis = new
                    {
                        servico = s.ServicoEstabelecimento.Nome,
                        dias = s.DiasAtrasClienteFezOServico,
                        queNaoTenhaFeitoOutroServico = s.QueNaoTenhaFeitoOutroServico ? 1 : 0,
                        idPublicoAlvoServicoEstabelecimento = s.Id,
                        idServicoEstabelecimento = s.ServicoEstabelecimento.IdServicoEstabelecimento
                    }
                }));
            }

            return retorno;
        }

        private static List<ResultadoPublicoAlvoDTO> FiltrarQueTenhaFeitoServicoEspecificoHaXDias(
            MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento,
            int idEstabelecimento, DateTime date)
        {
            if (publicoAlvo.TipoPublicoAlvoServico != TipoPublicoAlvoServicoEnum.QueTenhamFeitoServicoEspecifico)
                throw new Exception("Este método somente dá suporte a TipoPublicoAlvoServicoEnum.QueNaoTenhamFeitoServicoEspecifico");

            if (publicoAlvo.TipoDataServico != PublicoAlvoTipoDataServicoEnum.HaXDias)
                throw new Exception("Este método somente dá suporte a PublicoAlvoTipoDataServicoEnum.HaXDias");

            var retorno = new List<ResultadoPublicoAlvoDTO>();

            // Pegando somente os agendamentos NÃO CANCELADOS do estabelecimento
            var horariosQualquerServico = Domain.Pessoas.HorarioRepository.Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Status != StatusHorarioEnum.Cancelado && f.Status != StatusHorarioEnum.Cliente_Faltou);

            foreach (var s in publicoAlvo.ServicosRealizados)
            {
                var data = date.AddDays(-s.DiasAtrasClienteFezOServico ?? 0);

                // Pegando somente os agendamentos do serviço
                var horariosDoServico = horariosQualquerServico.Where(f => f.ServicoEstabelecimento == s.ServicoEstabelecimento);

                var clientesFiltrados = clientesEstabelecimento;
                clientesFiltrados = FitrarClientesComServicoFinalizadoNodia(data, horariosDoServico, clientesFiltrados);

                if (s.QueNaoTenhaFeitoOutroServico)
                {
                    clientesFiltrados = FiltrarClientesQueNaoTenhamFinalizadoServicosAposAData(horariosQualquerServico, data, clientesFiltrados);
                    clientesFiltrados = FiltrarClientesSemAgendamentoFuturo(horariosQualquerServico, clientesFiltrados, date);
                }
                else
                {
                    clientesFiltrados = FiltrarClientesSemAgendamentoFuturo(horariosDoServico, clientesFiltrados, date);
                }

                retorno.AddRange(clientesFiltrados.Select(ce => new ResultadoPublicoAlvoDTO
                {
                    ClienteEstabelecimento = ce,
                    Variaveis = new { servico = s.ServicoEstabelecimento.Nome, dias = s.DiasAtrasClienteFezOServico }
                }));
            }

            return retorno;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesQueNaoTenhamFinalizadoServicosAposAData(IQueryable<Horario> horarios, DateTime data, IQueryable<ClienteEstabelecimento> filtrados)
        {
            // Filtrando somente clientes em que o último agendamento finalizado tenha sido no dia específico
            filtrados = filtrados.Where(ce => horarios.Where(h => h.Cliente == ce.Cliente && h.Status == StatusHorarioEnum.Finalizado).Max(f => f.DataInicio) < data.AddDays(1));
            return filtrados;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesSemAgendamentoFuturo(IQueryable<Horario> horariosDoServico, IQueryable<ClienteEstabelecimento> filtrados)
        {
            // Filtrando somente os clientes que não tenham ainda agendado o retorno para este serviço no futuro
            filtrados = filtrados.Where(ce =>
                        !horariosDoServico.Any(h => h.Cliente == ce.Cliente && h.DataInicio >= Calendario.Hoje()));
            return filtrados;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesSemAgendamentoFuturo(IQueryable<Horario> horariosDoServico, IQueryable<ClienteEstabelecimento> filtrados, DateTime data)
        {
            // Filtrando somente os clientes que não tenham ainda agendado o retorno para este serviço no futuro
            filtrados = filtrados.Where(ce =>
                        !horariosDoServico.Any(h => h.Cliente == ce.Cliente && h.DataInicio >= data));
            return filtrados;
        }

        private static IQueryable<ClienteEstabelecimento> FitrarClientesComServicoFinalizadoNodia(DateTime data, IQueryable<Horario> horariosDoServico, IQueryable<ClienteEstabelecimento> filtrados)
        {
            // Filtrando somente os clientes que tenham agendamento do serviço FINALIZADO pela última vez no dia específico
            filtrados = filtrados.Where(ce =>
                       horariosDoServico.Where(h => h.Cliente == ce.Cliente && h.Status == StatusHorarioEnum.Finalizado).Max(f => f.DataInicio) >= data.Date
                       && horariosDoServico.Where(h => h.Cliente == ce.Cliente && h.Status == StatusHorarioEnum.Finalizado).Max(f => f.DataInicio) < data.Date.AddDays(1));
            return filtrados;
        }

        private static IQueryable<ClienteEstabelecimento> ClientesContidosNosHorarios(
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento, IQueryable<Horario> horarios)
        {
            clientesEstabelecimento =
                clientesEstabelecimento.Where(f => horarios.Select(g => g.ClienteEstabelecimento).Any(g => g.Codigo == f.Codigo));
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> ClientesNaoContidosNosHorarios(
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento, IQueryable<Horario> horarios)
        {
            clientesEstabelecimento =
                clientesEstabelecimento
                .Where(f => !horarios.Where(g => g.Status.Codigo != (int)StatusHorarioEnum.Cancelado && g.Status.Codigo != (int)StatusHorarioEnum.Cliente_Faltou)
                    .Any(g => g.Cliente == f.Cliente));

            return clientesEstabelecimento;
        }

        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarClientesEstabelecimentoPersonalizado(
            MarketingCampanha campanha,
            MarketingCampanhaPublicoAlvo publicoAlvo, IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            return FiltrarClientesEstabelecimentoPersonalizado(publicoAlvo, clientesEstabelecimento,
                campanha.Estabelecimento.IdEstabelecimento);
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesEstabelecimentoPorSexo(
            TipoPublicoAlvoEnum tipoPublicoAlvoEnum,
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            if (tipoPublicoAlvoEnum == TipoPublicoAlvoEnum.Mulheres)
            {
                clientesEstabelecimento = clientesEstabelecimento.Where(f => f.Cliente.PessoaFisica.Genero.Equals(Genero.Feminino));
            }
            if (tipoPublicoAlvoEnum == TipoPublicoAlvoEnum.Homens)
            {
                clientesEstabelecimento = clientesEstabelecimento.Where(f => f.Cliente.PessoaFisica.Genero.Equals(Genero.Masculino));
            }
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesEstabelecimentoQuePodemReceberEmail(
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            var queryableEmailsRejeitados = Domain.Pessoas.EmailRejeitadoAmazonRepository.QueryableEmailsBloqueados();
            clientesEstabelecimento = clientesEstabelecimento.Where(f => f.Cliente.PessoaFisica.Email != null &&
                                                                         !queryableEmailsRejeitados.Contains(f.Cliente.PessoaFisica.Email) &&
                                                                         f.RecebeEmailMarketing);
            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesEstabelecimentoQuePodemReceberSMS(
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            clientesEstabelecimento = FiltrarClientesEstabelecimentoComNumeroNacionalValido(clientesEstabelecimento);
            clientesEstabelecimento = clientesEstabelecimento.Where(f => f.RecebeSMSMarketing);

            return clientesEstabelecimento;
        }

        private static IQueryable<ClienteEstabelecimento> FiltrarClientesEstabelecimentoComNumeroNacionalValido(
            IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            clientesEstabelecimento = clientesEstabelecimento.Where(f =>
                f.Cliente.PessoaFisica.Telefones.Any(
                    g => (g.Dono == g.Pessoa ||
                          g.Dono == f.Estabelecimento.PessoaJuridica) &&
                          g.Ativo
                          && g.Ddi == DdiConstants.Brasil
                          && (g.Numero.StartsWith("6") || g.Numero.StartsWith("7") || g.Numero.StartsWith("8") || g.Numero.StartsWith("9"))));
            return clientesEstabelecimento;
        }

        private static IQueryable<Horario> HorariosDentroDoPeriodo(IQueryable<Horario> horarios, DateTime? dataInicioServicoRealizado, DateTime? dataFimServicoRealizado)
        {
            if (dataInicioServicoRealizado.HasValue)
                horarios = horarios.Where(f => f.DataInicio >= dataInicioServicoRealizado.Value);

            if (dataFimServicoRealizado.HasValue)
            {
                var dtFim = dataFimServicoRealizado.Value.Date;
                horarios = horarios.Where(f => f.DataInicio < dtFim.AddDays(1));
            }

            return horarios;
        }

        private static IQueryable<Horario> HorariosNosUltimosDias(IQueryable<Horario> horarios, int? diasAntecedenciaServicoFeito)
        {
            if (diasAntecedenciaServicoFeito.HasValue)
                horarios = horarios.Where(f => f.DataInicio >= Calendario.Hoje().AddDays(-diasAntecedenciaServicoFeito.Value) && f.DataInicio < Calendario.Hoje().AddDays(1));
            return horarios;
        }

        private IQueryable<MarketingCampanha> FiltrarComStatus(List<StatusCampanhaEnum> listaDeStatus,
            IQueryable<MarketingCampanha> retorno)
        {
            retorno = retorno.Where(f => listaDeStatus.Contains(f.StatusCampanha));
            return retorno;
        }

        public MarketingCampanha ObterPorEstabelecimentoECampanhaDoTrinks(int idEstabelecimento, MarketingCampanhaDoTrinksEnum campanhaTrinks)
        {
            return Queryable().FirstOrDefault(c => c.Estabelecimento.IdEstabelecimento == idEstabelecimento && c.MarketingCampanhaDoTrinks == campanhaTrinks);
        }
    }
}
