﻿using Castle.ActiveRecord;

namespace Perlink.Trinks.Marketing
{

    [ActiveRecord(DiscriminatorValue = "WPP")]
    public class MarketingEnvioClienteWhatsApp : MarketingEnvioCliente
    {
        public MarketingEnvioClienteWhatsApp()
        {
        }

        [Property("ddi_telefone")]
        public virtual string DdiTelefone { get; set; }

        [Property("ddd_telefone")]
        public virtual string DDDTelefone { get; set; }

        [Property("id_mensagem_gateway")]
        public string IdMensagemGateway { get; set; }

        [Property("status_gateway")]
        public string StatusGateway { get; set; }

        [Property("numero_telefone")]
        public virtual string NumeroTelefone { get; set; }

        public override bool Equals(object obj)
        {
            var objTipado = obj as MarketingEnvioClienteWhatsApp;
            if (objTipado == null) return false;

            return DDDTelefone == objTipado.DDDTelefone
                   && NumeroTelefone == objTipado.NumeroTelefone
                   && base.Equals(objTipado);
        }
    }
}
