﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Marketing
{

    public class GeraMarketingEnvioClienteSMSStrategy : IGeraMarketingEnvioClienteStrategy<MarketingEnvioSMS, MarketingEnvioClienteSMS>
    {

        public MarketingEnvioSMS ObterEnvio(Estabelecimento estabelecimento, int horaMin = 9, int horaMax = 11)
        {
            // Horário sorteado entre 9 e 11h
            var dataHora = SorteiaHora(Calendario.Hoje(), horaMin, horaMax);

            return new MarketingEnvioSMS()
            {
                DataHoraProgramada = dataHora,
                Estabelecimento = estabelecimento,
                StatusMarketingEnvio = Enums.StatusMarketingEnvioEnum.AguardandoConsolidacao
            };
        }

        public IEnumerable<MarketingEnvioClienteSMS> ObterEnvioClientes(MarketingEnvioSMS envio,
            IEnumerable<DestinatarioDTO> destinatarios)
        {
            var retorno = new List<MarketingEnvioClienteSMS>();

            //var lista = clientesEstabelecimento.Select(f => new {
            //    IdPessoa = f.Cliente.PessoaFisica.IdPessoa,
            //    IdPessoaEstabelecimento = f.Estabelecimento.PessoaJuridica.IdPessoa,
            //    IdClienteEstabelecimento = f.Codigo
            //}).ToList();

            var idsPessoa = destinatarios.Select(f => f.IdPessoa)
                .Take(2000) // É imporvável que um estabelecimento possua mais de 2000 aniversariantes em um mês. Impete o erro de mais de 2000 parâmetros no IN
                .ToList();

            var telefones = Domain.Pessoas.TelefoneRepository.ObterTelefonesCelularesDaPessoaQueryable(idsPessoa).ToList();

            foreach (var registro in destinatarios)
            {
                var telefone = telefones.FirstOrDefault(f => f.IdPessoa == registro.IdPessoa && (f.Dono.IdPessoa == registro.IdPessoaJuridica || f.IdPessoa == f.Dono.IdPessoa));

                if (telefone != null && !String.IsNullOrWhiteSpace(telefone.Numero) && !String.IsNullOrWhiteSpace(telefone.DDD))
                {
                    retorno.Add(new MarketingEnvioClienteSMS()
                    {
                        ClienteEstabelecimento = new ClienteEstabelecimento { Codigo = registro.IdClienteEstabelecimento },
                        DdiTelefone = telefone.Ddi,
                        DDDTelefone = telefone.DDD,
                        NumeroTelefone = telefone.Numero,
                        MarketingEnvio = envio,
                        Operadora = telefone.Operadora
                    });
                }
            }

            return retorno;
        }

        private static DateTime SorteiaHora(DateTime data, int horaMinima, int horaMaxima)
        {
            Random rnd = new Random();
            var quantidadesDeMinutos = (horaMaxima - horaMinima) * 60;
            var minutosDesvio = rnd.Next(0, quantidadesDeMinutos);

            return data.Date.AddHours(horaMinima).AddMinutes(minutosDesvio);
        }

        public bool JaHouveEnvioDeAniversarianteNoMesQueNaoFalhou(ConfiguracoesEstabelecimentoMarketing config)
        {
            return Domain.Marketing.MarketingEnvioSMSRepository.Queryable().Any(f =>
                f.MarketingCampanha == null &&
                f.Estabelecimento == config.Estabelecimento &&
                f.StatusMarketingEnvio != Enums.StatusMarketingEnvioEnum.ProblemasNoEnvio &&
                f.DataHoraProgramada.Month == Calendario.Hoje().Month &&
                f.DataHoraProgramada.Year == Calendario.Hoje().Year);
        }
    }
}