﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using System.Linq;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class FiltroPublicoAlvoAniversariantesStrategy : IFiltroPublicoAlvoStrategy
    {
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanha campanha)
        {
            return clientesEstabelecimento.Select(c => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = c });
        }
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanhaPublicoAlvo publicoAlvo, int idEstabelecimento)
        {
            return clientesEstabelecimento.Select(c => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = c });
        }
    }
}
