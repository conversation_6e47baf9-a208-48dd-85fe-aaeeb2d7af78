﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Web.Script.Serialization;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class ConsolidacaoMarketingWhatsAppStrategy : ConsolidacaoMarketingStrategy<MarketingEnvioWhatsApp, MarketingEnvioClienteWhatsApp>
    {

        public override List<MarketingEnvioClienteWhatsApp> ObterMarketingEnvioCliente(MarketingEnvioWhatsApp envio, ResultadoPublicoAlvoDTO destinatarioVariaveisDTO)
        {
            var celularesDoClienteEstabelecimento = Domain.Pessoas.TelefoneRepository.ObterTelefonesCelularesDaPessoaQueryable(destinatarioVariaveisDTO.ClienteEstabelecimento);

            var destinatarios = new List<MarketingEnvioClienteWhatsApp>();
            var conteudo = new JavaScriptSerializer().Serialize(destinatarioVariaveisDTO.Variaveis);
            foreach (var celular in celularesDoClienteEstabelecimento)
            {
                var marketingEnvio = new MarketingEnvioClienteWhatsApp()
                {
                    ClienteEstabelecimento = destinatarioVariaveisDTO.ClienteEstabelecimento,
                    DdiTelefone = celular.Ddi,
                    DDDTelefone = celular.DDD,
                    MarketingEnvio = envio,
                    NumeroTelefone = celular.Numero
                };
                var parametroEnvio = new MarketingEnvioClienteParametroEnvio()
                {
                    IdPublicoAlvoServicoEstabelecimento = Convert.ToInt32(ValorVariavel(conteudo, "idPublicoAlvoServicoEstabelecimento")),
                    IdServicoEstabelecimento = Convert.ToInt32(ValorVariavel(conteudo, "idServicoEstabelecimento")),
                    DiasAtras = Convert.ToInt32(ValorVariavel(conteudo, "dias")),
                    QueNaoTenhaFeitoOutroServico = Convert.ToInt32(ValorVariavel(conteudo, "queNaoTenhaFeitoOutroServico")) == 1
                };
                parametroEnvio.MarketingEnvioCliente = marketingEnvio;
                marketingEnvio.ParametroEnvio = parametroEnvio;

                destinatarios.Add(marketingEnvio);
            }
            //verificar uso dessas variáveis
            if (destinatarioVariaveisDTO.Variaveis != null)
            {
                var variaveis = new Conteudo.ConteudoTexto
                {
                    Conteudo = conteudo,
                    TipoConteudo = Conteudo.TipoConteudoEnum.VariaveisCampanhaConviteRetorno
                };

                destinatarios.ForEach(f => f.Variaveis = variaveis);
            }

            return destinatarios;
        }

        public string ValorVariavel(string conteudoVariaveis, string key)
        {
            var data = (JObject)JsonConvert.DeserializeObject(conteudoVariaveis);
            return data[key].ToString();
        }
    }
}
