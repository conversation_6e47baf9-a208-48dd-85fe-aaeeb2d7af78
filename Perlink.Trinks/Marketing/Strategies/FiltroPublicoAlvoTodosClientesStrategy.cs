﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class FiltroPublicoAlvoTodosClientesStrategy : IFiltroPublicoAlvoStrategy
    {
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanha campanha)
        {
            return clientesEstabelecimento.Select(c => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = c });
        }
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanhaPublicoAlvo publicoAlvo, int idEstabelecimento)
        {
            return clientesEstabelecimento.Select(c => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = c });
        }
    }
}
