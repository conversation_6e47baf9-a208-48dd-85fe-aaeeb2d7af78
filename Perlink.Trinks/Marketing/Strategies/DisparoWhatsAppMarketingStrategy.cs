﻿using Perlink.Shared.Exceptions;
using Perlink.Trinks.EnvioMensagem;
using Perlink.Trinks.Marketing.Services;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Pessoas.Services;
using Perlink.Trinks.WhatsApp;
using Perlink.Trinks.WhatsApp.Enums;
using Perlink.Trinks.WhatsApp.Factories;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class DisparoWhatsAppMarketingStrategy : DisparoMarketingStrategy<MarketingEnvioWhatsApp, MarketingEnvioClienteWhatsApp, MarketingCampanhaWhatsApp, MensagemWhatsApp>
    {

        private static readonly IMessageFactory MessageFactory = new MessageFactory();

        public override MensagemWhatsApp ObterMensagem(MarketingEnvioClienteWhatsApp envioCliente)
        {

            var marketingCampanha = (MarketingCampanhaWhatsApp)envioCliente.MarketingEnvio.MarketingCampanha;

            var conteudo = marketingCampanha.ConteudoCampanha.Conteudo;
            return new MensagemWhatsApp
            {
                Ddi = int.Parse(envioCliente.DdiTelefone ?? DdiConstants.Brasil),
                DDD = int.Parse(envioCliente.DDDTelefone),
                Telefone = int.Parse(envioCliente.NumeroTelefone),
                Conteudo = conteudo,
                Id = envioCliente.IdMarketingEnvioCliente.ToString(),
                Origem = EnvioMensagem.Enums.OrigemMensagemEnum.Marketing,
            };
        }

        public MessageSns ObterMensagemInterno(MarketingEnvioClienteWhatsApp envioCliente)
        {
            var establishmentName = envioCliente.ClienteEstabelecimento.Estabelecimento.NomeDeExibicaoNoPortal;
            var establishmentId = envioCliente.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento;
            var serviceName = ValorVariavel(envioCliente.Variaveis.Conteudo, "servico");

            Telefone telefone;

            telefone = Domain.Pessoas.TelefoneService.ObterTelefonesComWhatAppAtivosDoEstabelecimentoLogado(establishmentId).FirstOrDefault();

            if (telefone == null)
            {
                telefone = Domain.Pessoas.TelefoneService.ObterTelefonesAtivosDoEstabelecimentoLogado(establishmentId).FirstOrDefault();

                var telefoneFormatado = telefone?.ToString();

                return MessageFactory.ReturnInvitationPhoneContactMessage(
                    envioCliente.DdiTelefone ?? DdiConstants.Brasil,
                    envioCliente.DDDTelefone,
                    envioCliente.NumeroTelefone,
                    establishmentName,
                    serviceName,
                    telefoneFormatado);
            }

            var telefoneWhatsAppformatado = $"{telefone.Ddi}{telefone.DDD}{telefone.Numero}";
            string urlBase = ConfiguracoesTrinks.WhatsApp.UrlBase;

            if (urlBase.EndsWith("/"))
                urlBase = urlBase.Substring(0, urlBase.Length - 1);

            var urlResult = urlBase + "/S/RedirectRegister?n=" + telefoneWhatsAppformatado;

            return MessageFactory.ReturnInvitationWhatsAppContactMessage(
                envioCliente.DdiTelefone ?? DdiConstants.Brasil,
                envioCliente.DDDTelefone,
                envioCliente.NumeroTelefone,
                establishmentName,
                serviceName,
                urlResult);
        }

        protected override int EnvioParaDisparo(List<MarketingEnvioClienteWhatsApp> marketingEnvioClientes, MarketingEnvioWhatsApp marketingEnvio)
        {
            var count = 0;

            foreach (var marketingEnvioCliente in marketingEnvioClientes)
            {
                try
                {
                    var mensagem = ObterMensagemInterno(marketingEnvioCliente);

                    var sessionOptionalData = new SessionOptionalData()
                    {
                        IdEstabelecimento = marketingEnvioCliente.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento,
                    };

                    var result = Domain.WhatsApp.SendService.Send(mensagem, null, sessionOptionalData,
                        FluxoDeComunicacaoWhatsApp.ConviteRetorno, 0);

                    if (!Guid.TryParse(result, out var _)) continue;

                    marketingEnvioCliente.DataHoraEnvio = Calendario.Agora();
                    marketingEnvioCliente.DataEmissao = Calendario.Agora();
                    count++;

                }
                catch (Exception e)
                {
                    LogService<MarketingCampanhaService>.Error("============ Erro ao disparar mensagem através do whatsapp para o cliente " + marketingEnvioCliente.ClienteEstabelecimento.Cliente.PessoaFisica.NomeCompleto + " " + Calendario.Agora() + " ================");
                    LogService<MarketingCampanhaService>.Error(e.Formatada());
                    LogService<MarketingCampanhaService>.Error("============================");
                }
            }

            Domain.Marketing.MarketingEnvioClienteWhatsAppRepository.Flush();
            return count;
        }

        protected override IEnumerable<MarketingEnvioWhatsApp> ListarEnviosPendentesCampanha(DateTime data)
        {
            var listaCampanha = Domain.Marketing.MarketingEnvioWhatsAppRepository.ListarEnviosPendentesCampanha(data).ToList();
            return listaCampanha;
        }

        internal override int TamanhoDoLoteDeMensagem()
        {
            throw new NotImplementedException();
        }
    }
}
