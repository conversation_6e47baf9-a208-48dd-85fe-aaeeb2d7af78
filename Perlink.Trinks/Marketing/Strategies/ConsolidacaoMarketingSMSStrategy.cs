﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas.Helpers;
using System.Collections.Generic;
using System.Web.Script.Serialization;

namespace Perlink.Trinks.Marketing.Strategies
{

    public class ConsolidacaoMarketingSMSStrategy : ConsolidacaoMarketingStrategy<MarketingEnvioSMS, MarketingEnvioClienteSMS>
    {

        public override List<MarketingEnvioClienteSMS> ObterMarketingEnvioCliente(MarketingEnvioSMS envio, ResultadoPublicoAlvoDTO destinatarioVariaveisDTO)
        {
            var celularesDoClienteEstabelecimento = Domain.Pessoas.TelefoneRepository.ObterTelefonesCelularesDaPessoaQueryable(destinatarioVariaveisDTO.ClienteEstabelecimento);

            var destinatarios = new List<MarketingEnvioClienteSMS>();
            foreach (var celular in celularesDoClienteEstabelecimento)
            {
                destinatarios.Add(new MarketingEnvioClienteSMS()
                {
                    ClienteEstabelecimento = destinatarioVariaveisDTO.ClienteEstabelecimento,
                    DdiTelefone = celular.Ddi,
                    DDDTelefone = celular.DDD,
                    MarketingEnvio = envio,
                    NumeroTelefone = celular.Numero,
                    Operadora = celular.Operadora
                });
            }

            if (destinatarioVariaveisDTO.Variaveis != null)
            {
                var variaveis = new Conteudo.ConteudoTexto
                {
                    Conteudo = new JavaScriptSerializer().Serialize(destinatarioVariaveisDTO.Variaveis),
                    TipoConteudo = Conteudo.TipoConteudoEnum.VariaveisCampanhaConviteRetorno
                };

                destinatarios.ForEach(f => f.Variaveis = variaveis);
            }

            return destinatarios;
        }
    }
}