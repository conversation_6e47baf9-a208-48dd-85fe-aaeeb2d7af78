﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Perlink.Trinks.Marketing.Strategies
{
    public interface IFiltroPublicoAlvoStrategy
    {
        IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanha campanha);
        IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanhaPublicoAlvo publicoAlvo, int idEstabelecimento);
    }

}
