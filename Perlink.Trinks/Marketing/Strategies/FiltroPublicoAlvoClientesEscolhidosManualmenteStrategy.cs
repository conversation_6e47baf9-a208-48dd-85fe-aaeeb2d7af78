﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Linq;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class FiltroPublicoAlvoClientesEscolhidosManualmenteStrategy : IFiltroPublicoAlvoStrategy
    {
        public FiltroPublicoAlvoClientesEscolhidosManualmenteStrategy()
        {

        }

        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanha campanha)
        {
            return Domain.Marketing.MarketingCampanhaRepository
                .FiltrarPorClientesSelecionados(campanha.MarketingCampanhaPublicoAlvo, clientesEstabelecimento)
                .Select(p => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = p});
        }
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanhaPublicoAlvo publicoAlvo, int idEstabelecimento)
        {
            return Domain.Marketing.MarketingCampanhaRepository
                .FiltrarPorClientesSelecionados(publicoAlvo, clientesEstabelecimento)
                .Select(p => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = p });
        }
    }
}
