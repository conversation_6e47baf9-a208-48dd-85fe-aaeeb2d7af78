﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Linq;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class FiltroPublicoAlvoPersonalizadoStrategy : IFiltroPublicoAlvoStrategy
    {
        public FiltroPublicoAlvoPersonalizadoStrategy()
        {
        }

        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanha campanha)
        {
            return Domain.Marketing.MarketingCampanhaRepository
                 .FiltrarClientesEstabelecimentoPersonalizado(campanha, campanha.MarketingCampanhaPublicoAlvo, clientesEstabelecimento);
        }
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanhaPublicoAlvo publicoAlvo, int idEstabelecimento)
        {
            return Domain.Marketing.MarketingCampanhaRepository
                .FiltrarClientesEstabelecimentoPersonalizado(publicoAlvo, clientesEstabelecimento, idEstabelecimento);
        }
    }
}
