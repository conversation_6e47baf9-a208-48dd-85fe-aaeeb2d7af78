﻿using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.VO;
using System.Linq;

namespace Perlink.Trinks.Marketing.Strategies
{
    public class FiltroGeneroPublicoAlvoStrategy : IFiltroPublicoAlvoStrategy
    {
        private readonly Genero _genero;

        public FiltroGeneroPublicoAlvoStrategy(Genero genero)
        {
            _genero = genero;
        }

        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanha campanha)
        {
            return clientesEstabelecimento.Where(f => f.Cliente.PessoaFisica.Genero == _genero).Select(f => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = f });
        }
        public IQueryable<ResultadoPublicoAlvoDTO> FiltrarPublicoAlvo(IQueryable<ClienteEstabelecimento> clientesEstabelecimento, MarketingCampanhaPublicoAlvo publicoAlvo, int idEstabelecimento)
        {
            return clientesEstabelecimento.Where(f => f.Cliente.PessoaFisica.Genero == _genero).Select(f => new ResultadoPublicoAlvoDTO { ClienteEstabelecimento = f });
        }
    }


}
