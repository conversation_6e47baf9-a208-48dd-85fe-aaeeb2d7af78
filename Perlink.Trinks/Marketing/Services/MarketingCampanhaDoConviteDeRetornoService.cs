﻿using Perlink.DomainInfrastructure.Facilities;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Auditing;
using Perlink.Shared.Exceptions;
using Perlink.Trinks.Marketing.DTO;
using Perlink.Trinks.Marketing.Enums;
using Perlink.Trinks.WhatsApp.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Marketing.Services
{

    public class MarketingCampanhaDoConviteDeRetornoService : BaseService, IMarketingCampanhaDoConviteDeRetornoService
    {

        public void GerarRegistrosMarketingEnvioDosConvitesDeRetorno(DateTime agora)
        {
            try
            {
                GerarMarketingEnvioWppDosConvitesDeRetorno(agora);
            }
            catch (Exception e)
            {
                LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
            }

            try
            {
                GerarMarketingEnvioSmsDosConvitesDeRetorno(agora);
            }
            catch (Exception e)
            {
                LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
            }

            try
            {
                GerarMarketingEnvioEmailDosConvitesDeRetorno(agora);
            }
            catch (Exception e)
            {
                LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
            }
        }

        private void GerarMarketingEnvioWppDosConvitesDeRetorno(DateTime agora)
        {
            var listaDeCampanhas = ObterCampanhaWpp();

            foreach (var campanhaDto in listaDeCampanhas)
            {
                var session = NHibernateFacility.GetSession<MarketingCampanha>();

                using (var tx = session.BeginTransaction())
                {
                    try
                    {
                        Domain.Marketing.MarketingCampanhaDoConviteDeRetornoService.GerarMarketingEnvioDuranteRotinaDiaria(campanhaDto.MarketingCampanha, campanhaDto.SaldoDisponivel, campanhaDto.EstabelecimentoEstaAdimplente, agora);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        LogService<MarketingCampanhaDoConviteDeRetornoService>.Error("Erro ao gerar o envio da campanha: " + campanhaDto.MarketingCampanha.NomeCampanha + " - Estabelecimento: #" + campanhaDto.MarketingCampanha.Estabelecimento.IdEstabelecimento + " " + campanhaDto.MarketingCampanha.Estabelecimento.NomeDeExibicaoNoPortal);
                        LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
                    }
                }
            }
        }

        private void GerarMarketingEnvioEmailDosConvitesDeRetorno(DateTime agora)
        {
            var listaDeCampanhas = ObterCampanhasEmails();

            foreach (var campanhaDto in listaDeCampanhas)
            {
                var session = NHibernateFacility.GetSession<MarketingCampanha>();

                using (var tx = session.BeginTransaction())
                {
                    try
                    {
                        Domain.Marketing.MarketingCampanhaDoConviteDeRetornoService.GerarMarketingEnvioDuranteRotinaDiaria(campanhaDto.MarketingCampanha, campanhaDto.SaldoDisponivel, campanhaDto.EstabelecimentoEstaAdimplente, agora);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        LogService<MarketingCampanhaDoConviteDeRetornoService>.Error("Erro ao gerar o envio da campanha: " + campanhaDto.MarketingCampanha.NomeCampanha + " - Estabelecimento: #" + campanhaDto.MarketingCampanha.Estabelecimento.IdEstabelecimento + " " + campanhaDto.MarketingCampanha.Estabelecimento.NomeDeExibicaoNoPortal);
                        LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
                    }
                }
            }
        }

        private void GerarMarketingEnvioSmsDosConvitesDeRetorno(DateTime agora)
        {
            var listaDeCampanhas = ObterCampanhaSms();

            foreach (var campanhaDto in listaDeCampanhas)
            {
                var session = NHibernateFacility.GetSession<MarketingCampanha>();

                using (var tx = session.BeginTransaction())
                {
                    try
                    {
                        Domain.Marketing.MarketingCampanhaDoConviteDeRetornoService.GerarMarketingEnvioDuranteRotinaDiaria(campanhaDto.MarketingCampanha, campanhaDto.SaldoDisponivel, campanhaDto.EstabelecimentoEstaAdimplente, agora);
                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();
                        LogService<MarketingCampanhaDoConviteDeRetornoService>.Error("Erro ao gerar o envio da campanha: " + campanhaDto.MarketingCampanha.NomeCampanha + " - Estabelecimento: #" + campanhaDto.MarketingCampanha.Estabelecimento.IdEstabelecimento + " " + campanhaDto.MarketingCampanha.Estabelecimento.NomeDeExibicaoNoPortal);
                        LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
                    }
                }
            }
        }

        private IList<MarketingCampanhaDeConviteRetornoDTO> ObterCampanhaWpp(int idEstabelecimento = 0)
        {
            var queryMarketingSmsCampanha = Domain.Marketing.MarketingCampanhaWhatsAppRepository.Queryable();
            var queryConviteWpp = Domain.Marketing.ConviteDeRetornoWhatsAppRepository.Queryable();
            var queryContaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            var querySaldo = Domain.WhatsApp.SaldoEstabelecimentoRepository.Queryable();

            int[] statusQuePermitemEnvioConviteDeRetorno = new[] { 1, 3, 4, 9 };

            var campanhasFiltradas = from mark in queryMarketingSmsCampanha
                                     join conf in querySaldo on new { mark.Estabelecimento.IdEstabelecimento, Tipo = TipoSaldo.ConviteRetorno } equals new { conf.IdEstabelecimento, conf.Tipo }
                                     join conv in queryConviteWpp on mark.IdMarketingCampanha equals conv.MarketingCampanha.IdMarketingCampanha
                                     where conv.Ativo && !mark.EnvioSuspenso && mark.StatusCampanha != StatusCampanhaEnum.Excluida &&
                                     mark.ProximoEnvio != null && (idEstabelecimento > 0 ? mark.Estabelecimento.IdEstabelecimento == idEstabelecimento : true)
                                     select new MarketingCampanhaDeConviteRetornoDTO
                                     {
                                         MarketingCampanha = mark,
                                         SaldoDisponivel = conf != null ? (conf.LimiteDeEnvios + conf.LimiteDeEnviosCortesia) - (conf.QuantidadeDeEnviadosNoPeriodo + conf.QuantidadeDeEnviosCortesiaNoPeriodo) : 0,
                                         EstabelecimentoEstaAdimplente = queryContaFinanceira
                                             .Where(fin => fin.Pessoa.IdPessoa == mark.Estabelecimento.PessoaJuridica.IdPessoa)
                                             .Any(fin => statusQuePermitemEnvioConviteDeRetorno.Contains(fin.Status.IdStatus))
                                     };

            return campanhasFiltradas.ToList();
        }

        private IList<MarketingCampanhaDeConviteRetornoDTO> ObterCampanhasEmails()
        {
            var queryMarketingEmailCampanha = Domain.Marketing.MarketingCampanhaEmailRepository.Queryable();
            var queryConviteEmail = Domain.Marketing.ConviteDeRetornoEmailRepository.Queryable();
            var queryConfiguracoes = Domain.Marketing.ConfiguracoesEstabelecimentoMarketingRepository.Queryable();
            var queryContaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();

            int[] statusQuePermitemEnvioConviteDeRetorno = new[] { 1, 3, 4, 9 };

            var campanhasFiltradas = from mark in queryMarketingEmailCampanha
                                     join conv in queryConviteEmail on mark.IdMarketingCampanha equals conv.MarketingCampanha.IdMarketingCampanha
                                     join conf in queryConfiguracoes on mark.Estabelecimento.IdEstabelecimento equals conf.Estabelecimento.IdEstabelecimento
                                     where conv.Ativo && !mark.EnvioSuspenso && mark.StatusCampanha != StatusCampanhaEnum.Excluida && mark.ProximoEnvio != null
                                     select new MarketingCampanhaDeConviteRetornoDTO
                                     {
                                         MarketingCampanha = mark,
                                         SaldoDisponivel = conf.SaldoMarketingEmail + conf.SaldoMarketingEmailBrinde,
                                         EstabelecimentoEstaAdimplente = queryContaFinanceira
                                             .Where(fin => fin.Pessoa.IdPessoa == mark.Estabelecimento.PessoaJuridica.IdPessoa)
                                             .Any(fin => statusQuePermitemEnvioConviteDeRetorno.Contains(fin.Status.IdStatus))
                                     };

            return campanhasFiltradas.ToList();
        }

        private IList<MarketingCampanhaDeConviteRetornoDTO> ObterCampanhaSms()
        {
            var queryMarketingSmsCampanha = Domain.Marketing.MarketingCampanhaSMSRepository.Queryable();
            var queryConviteSms = Domain.Marketing.ConviteDeRetornoSMSRepository.Queryable();
            var queryConfiguracoes = Domain.Marketing.ConfiguracoesEstabelecimentoMarketingRepository.Queryable();
            var queryContaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();

            int[] statusQuePermitemEnvioConviteDeRetorno = new[] { 1, 3, 4, 9 };

            var campanhasFiltradas = from mark in queryMarketingSmsCampanha
                                     join conv in queryConviteSms on mark.IdMarketingCampanha equals conv.MarketingCampanha.IdMarketingCampanha
                                     join conf in queryConfiguracoes on mark.Estabelecimento.IdEstabelecimento equals conf.Estabelecimento.IdEstabelecimento
                                     where conv.Ativo && !mark.EnvioSuspenso && mark.StatusCampanha != StatusCampanhaEnum.Excluida && mark.ProximoEnvio != null
                                     select new MarketingCampanhaDeConviteRetornoDTO
                                     {
                                         MarketingCampanha = mark,
                                         SaldoDisponivel = conf.SaldoMarketingSMS,
                                         EstabelecimentoEstaAdimplente = queryContaFinanceira
                                             .Where(fin => fin.Pessoa.IdPessoa == mark.Estabelecimento.PessoaJuridica.IdPessoa)
                                             .Any(fin => statusQuePermitemEnvioConviteDeRetorno.Contains(fin.Status.IdStatus))
                                     };

            return campanhasFiltradas.ToList();
        }

        public void GerarMarketingEnvioAoAtivarConviteDeRetorno(MarketingCampanha campanha)
        {
            if (campanha.StatusCampanha == StatusCampanhaEnum.Excluida) return;

            MarketingEnvio ultimoEnvioDaCampanha = Domain.Marketing.MarketingEnvioRepository.ObterUltimoMarketingEnvioDaCampanha(campanha.IdMarketingCampanha);
            bool ultimoEnvioEstaAguardandoConsolidacao = ultimoEnvioDaCampanha != null && ultimoEnvioDaCampanha.StatusMarketingEnvio == StatusMarketingEnvioEnum.AguardandoConsolidacao;
            DateTime dataHoraProximoEnvio = ObterDataHoraProximoEnvio(campanha, Calendario.Agora());
            bool proximoEnvioOcorreHoje = dataHoraProximoEnvio.Date == Calendario.Hoje();

            if (ultimoEnvioEstaAguardandoConsolidacao)
            {
                ReprogramarMarketingEnvioAindaNaoEnviadoDaCampanha(ultimoEnvioDaCampanha, dataHoraProximoEnvio);
            }
            else
            {
                if (proximoEnvioOcorreHoje)
                    CriarNovoMarketingEnvioParaConsolidacao(campanha, Calendario.Agora());
                else
                    ProgramarCampanha(campanha, dataHoraProximoEnvio);
            }
        }

        private DateTime ObterDataHoraProximoEnvio(MarketingCampanha campanha, DateTime dataHoraReferencia)
        {
            var dataHoraProximoEnvio = dataHoraReferencia.Date;
            dataHoraProximoEnvio = dataHoraProximoEnvio
                                    .AddHours(campanha.ProximoEnvio.Value.Hour)
                                    .AddMinutes(campanha.ProximoEnvio.Value.Minute);

            MarketingEnvio ultimoEnvioDaCampanha = Domain.Marketing.MarketingEnvioRepository.ObterUltimoMarketingEnvioDaCampanha(campanha.IdMarketingCampanha);
            bool ultimoEnvioFoiRealizadoHoje = ultimoEnvioDaCampanha != null && ultimoEnvioDaCampanha.StatusMarketingEnvio != StatusMarketingEnvioEnum.AguardandoConsolidacao && ultimoEnvioDaCampanha.DataHoraProgramada.Date == dataHoraReferencia.Date;
            bool dataHoraProximoEnvioJaPassou = dataHoraProximoEnvio < dataHoraReferencia;

            if (ultimoEnvioFoiRealizadoHoje)
            {
                dataHoraProximoEnvio = dataHoraProximoEnvio.AddDays(1);
            }
            else if (dataHoraProximoEnvioJaPassou)
            {
                dataHoraProximoEnvio = dataHoraProximoEnvio.AddDays(1);
            }

            return dataHoraProximoEnvio;
        }

        public void GerarMarketingEnvioDuranteRotinaDiaria(MarketingCampanha campanha, int saldoDisponivel, bool estabelecimentoEmSituacaoRegular, DateTime agora)
        {
            if (estabelecimentoEmSituacaoRegular)
            {
                try
                {
                    MarketingEnvio ultimoEnvioDaCampanha = Domain.Marketing.MarketingEnvioRepository.ObterUltimoMarketingEnvioDaCampanha(campanha.IdMarketingCampanha);
                    bool ultimoEnvioFoiEnviado = ultimoEnvioDaCampanha != null && ultimoEnvioDaCampanha.StatusMarketingEnvio != StatusMarketingEnvioEnum.AguardandoConsolidacao;

                    if (ultimoEnvioDaCampanha == null || ultimoEnvioFoiEnviado)
                    {
                        var tipoCampanha = campanha is MarketingCampanhaWhatsApp ? TipoCampanhaMarketingEnum.WhatsApp : (campanha is MarketingCampanhaSMS ? TipoCampanhaMarketingEnum.SMS : TipoCampanhaMarketingEnum.Email);
                        int quantidadeNecessariaDeCreditos = Domain.Marketing.MarketingCampanhaRepository.ListarResultadoDoPublicoAlvo(campanha.MarketingCampanhaPublicoAlvo, tipoCampanha, campanha.Estabelecimento.IdEstabelecimento).Count();
                        bool possuiPublicoNaData = quantidadeNecessariaDeCreditos > 0;
                        bool possuiSaldoSuficiente = saldoDisponivel >= quantidadeNecessariaDeCreditos;

                        if (possuiPublicoNaData)
                        {
                            LogService<MarketingCampanhaDoConviteDeRetornoService>.Debug("Criando envio da campanha: " + campanha.NomeCampanha + " - Estabelecimento: #" + campanha.Estabelecimento.IdEstabelecimento + " " + campanha.Estabelecimento.NomeDeExibicaoNoPortal);
                            CriarNovoMarketingEnvioParaConsolidacao(campanha, agora);

                            if (!possuiSaldoSuficiente)
                            {
                                Domain.Pessoas.EnvioEmailService.EnviarEmailMarketingConviteRetornoSemCredito(campanha, quantidadeNecessariaDeCreditos);
                            }
                        }
                        else
                        {
                            DateTime dataHoraProximoEnvio = campanha.ProximoEnvio.Value.AddDays(1);
                            ProgramarCampanha(campanha, dataHoraProximoEnvio);
                            Domain.Marketing.MarketingCampanhaHistoricoService.RegistrarHistoricoAlteracaoProximoEnvioSemDestinatario(campanha);
                        }
                    }
                }
                catch (Exception e)
                {
                    LogService<MarketingCampanhaDoConviteDeRetornoService>.Error(e.Formatada());
                }
            }
        }

        private void CriarNovoMarketingEnvioParaConsolidacao(MarketingCampanha campanha, DateTime agora)
        {
            DateTime dataHoraProgramadaProximoEnvio = ObterDataHoraProximoEnvio(campanha, agora);

            campanha.ProximoEnvio = dataHoraProgramadaProximoEnvio;
            campanha.StatusCampanha = StatusCampanhaEnum.AguardandoMomentoEnvio;
            //Domain.Marketing.MarketingCampanhaRepository.Update(campanha);

            var marketingEnvio = MontarEntidadeEnvio(campanha, dataHoraProgramadaProximoEnvio, StatusMarketingEnvioEnum.AguardandoConsolidacao);
            Domain.Marketing.MarketingEnvioRepository.SaveNew(marketingEnvio);
        }

        private void ProgramarCampanha(MarketingCampanha campanha, DateTime dataProgramada)
        {
            campanha.ProximoEnvio = dataProgramada;
            campanha.StatusCampanha = StatusCampanhaEnum.AguardandoCriacaoProximoEnvio;
            Domain.Marketing.MarketingCampanhaRepository.Update(campanha);
        }

        private void ReprogramarMarketingEnvioAindaNaoEnviadoDaCampanha(MarketingEnvio envio, DateTime dataHoraProgramadaProximoEnvio)
        {
            var campanha = envio.MarketingCampanha;

            campanha.ProximoEnvio = dataHoraProgramadaProximoEnvio;
            campanha.StatusCampanha = StatusCampanhaEnum.AguardandoMomentoEnvio;
            Domain.Marketing.MarketingCampanhaRepository.Update(campanha);

            envio.StatusMarketingEnvio = StatusMarketingEnvioEnum.AguardandoConsolidacao;
            envio.DataHoraProgramada = dataHoraProgramadaProximoEnvio;
            Domain.Marketing.MarketingEnvioRepository.Update(envio);
        }

        private static MarketingEnvio MontarEntidadeEnvio(MarketingCampanha marketingCampanha, DateTime dataHoraProgramada, StatusMarketingEnvioEnum statusEnvio)
        {
            var marketingEnvio = new MarketingEnvio();
            if (marketingCampanha is MarketingCampanhaEmail)
                marketingEnvio = new MarketingEnvioEmail();
            else if (marketingCampanha is MarketingCampanhaSMS)
                marketingEnvio = new MarketingEnvioSMS();
            else if (marketingCampanha is MarketingCampanhaWhatsApp)
                marketingEnvio = new MarketingEnvioWhatsApp();

            marketingEnvio.DataHoraProgramada = dataHoraProgramada;
            marketingEnvio.MarketingCampanha = marketingCampanha;
            marketingEnvio.StatusMarketingEnvio = statusEnvio;
            marketingEnvio.Estabelecimento = marketingCampanha.Estabelecimento;
            marketingEnvio.EhPrimeiroEnvio = true;

            return marketingEnvio;
        }

        public void ExcluirCampanha(MarketingCampanha campanha)
        {
            if (campanha == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Campanha não localizada!");
                return;
            }
            else if (!Domain.Marketing.MarketingCampanhaService.CampanhaPodeSerExcluida(campanha))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A campanha selecionada não pode ser excluída!");
                return;
            }

            campanha.StatusCampanha = StatusCampanhaEnum.Excluida;

            var marketingEnvio = Domain.Marketing.MarketingEnvioRepository.ObterUltimoMarketingEnvioProgramadoNaoDisparado(campanha.IdMarketingCampanha);
            if (marketingEnvio != null && marketingEnvio.StatusMarketingEnvio != StatusMarketingEnvioEnum.EnvioFinalizado)
            {
                marketingEnvio.StatusMarketingEnvio = StatusMarketingEnvioEnum.Excluida;
                Domain.Marketing.MarketingEnvioRepository.Update(marketingEnvio);
            }

            var historico = new MarketingCampanhaHistorico()
            {
                DataHora = Calendario.Agora(),
                Descricao = "Campanha excluída",
                MarketingCampanha = campanha,
                TipoCampanhaHistoricoEnum = TipoCampanhaHistoricoEnum.CampanhaExcluida,
                TipoEnvio = campanha is MarketingCampanhaSMS ? TipoEnvioEnum.WhatsApp : (campanha is MarketingCampanhaSMS ? TipoEnvioEnum.SMS : TipoEnvioEnum.Email)
            };

            Domain.Marketing.MarketingCampanhaRepository.Update(campanha);
            Domain.Marketing.MarketingCampanhaHistoricoRepository.SaveNew(historico);
        }

        public void ReprogramarCampanhaAposTentativaDeEnvio(MarketingEnvio envio)
        {
            if (envio != null && envio.MarketingCampanha != null &&
                envio.MarketingCampanha.MarketingCampanhaDoTrinks != MarketingCampanhaDoTrinksEnum.ConviteDeRetorno)
                return;

            envio.MarketingCampanha.ProximoEnvio = envio.DataHoraProgramada.AddDays(1); ;
        }

        public List<ResultadoPublicoAlvoDTO> ObterClientesProximaCampanhaConviteDeRetornoWpp(int idEstabelecimento)
        {
            var listaResultadoPublicoAlvo = new List<ResultadoPublicoAlvoDTO>();
            var marketingCampanha = ObterCampanhaWpp(idEstabelecimento).FirstOrDefault();

            if (marketingCampanha != null)
                listaResultadoPublicoAlvo = Domain.Marketing.MarketingCampanhaRepository.ListarResultadoDoPublicoAlvo(
                    marketingCampanha.MarketingCampanha.MarketingCampanhaPublicoAlvo,
                    TipoCampanhaMarketingEnum.WhatsApp,
                    idEstabelecimento,
                    (DateTime)marketingCampanha.MarketingCampanha.ProximoEnvio
                ).ToList();

            return listaResultadoPublicoAlvo;
        }

    }

    public class MarketingCampanhaDeConviteRetornoDTO
    {
        public MarketingCampanha MarketingCampanha { get; set; }
        public int SaldoDisponivel { get; set; }
        public bool EstabelecimentoEstaAdimplente { get; set; }
    }
}
