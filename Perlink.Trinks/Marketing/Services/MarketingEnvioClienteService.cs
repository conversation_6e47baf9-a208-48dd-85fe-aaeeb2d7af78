﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas.Helpers;

namespace Perlink.Trinks.Marketing.Services
{

    public class MarketingEnvioClienteService : BaseService, IMarketingEnvioClienteService
    {

        public void CopiarListaDeEnviosCliente(MarketingEnvio origem, MarketingEnvio destino)
        {
            var listaEnviosClienteOrigem = Domain.Marketing.MarketingEnvioClienteRepository.ListarPorMarketingEnvio(origem);
            foreach (var envioClienteOrigem in listaEnviosClienteOrigem)
            {
                if (envioClienteOrigem is MarketingEnvioClienteEmail)
                {
                    var marketingEnvioClienteDestinoEmail = new MarketingEnvioClienteEmail()
                    {
                        ClienteEstabelecimento = envioClienteOrigem.ClienteEstabelecimento,
                        MarketingEnvio = destino,
                        EmailClienteEstabelecimento = envioClienteOrigem.ClienteEstabelecimento.Cliente.PessoaFisica.Email
                    };

                    Domain.Marketing.MarketingEnvioClienteEmailRepository.SaveNewNoFlush(marketingEnvioClienteDestinoEmail);
                }
                else if (envioClienteOrigem is MarketingEnvioClienteSMS)
                {
                    var marketingEnvioClienteDestinoSMS = new MarketingEnvioClienteSMS()
                    {
                        ClienteEstabelecimento = envioClienteOrigem.ClienteEstabelecimento,
                        DdiTelefone = ((MarketingEnvioClienteSMS)envioClienteOrigem).DdiTelefone ?? DdiConstants.Brasil,
                        DDDTelefone = ((MarketingEnvioClienteSMS)envioClienteOrigem).DDDTelefone,
                        MarketingEnvio = destino,
                        NumeroTelefone = ((MarketingEnvioClienteSMS)envioClienteOrigem).NumeroTelefone
                    };

                    Domain.Marketing.MarketingEnvioClienteSMSRepository.SaveNewNoFlush(marketingEnvioClienteDestinoSMS);
                }
            }

            Domain.Marketing.MarketingEnvioClienteSMSRepository.Flush();
        }
    }
}