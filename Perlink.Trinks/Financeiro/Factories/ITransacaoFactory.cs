﻿using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.LinksDePagamentoNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Pessoas;

namespace Perlink.Trinks.Financeiro.Factories
{
    public partial interface ITransacaoFactory
    {
        Transacao CreateParaPagementosDoClub(TransicaoParaClubeAssinaturaDTO filtro);
        Transacao CreateParaVendaPacoteHotsite(VendaHotsiteDTO filtro, PessoaFisica pessoaQuePagou, PessoaJuridica pessoaQueRecebeu);
        Transacao CreateParaPagamentoAntecipadoHotsite(PagamentoAntecipadoHotsiteDto filtro);
    }
}
