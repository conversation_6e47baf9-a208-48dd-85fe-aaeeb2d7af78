using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.LinksDePagamentoNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Factories
{
    public partial class TransacaoFactory : ITransacaoFactory
    {
        public Transacao CreateParaPagementosDoClub(TransicaoParaClubeAssinaturaDTO filtro)
        {
            var transacao = new Transacao();
            var venda = new Venda(transacao);
            transacao.Vendas = new List<Venda>();
            transacao.Vendas.Add(venda);

            EstabelecimentoFormaPagamento estabelecimentoFormaPagamento;

            if (filtro.EhAssinaturaPorLink)
            {
                Domain.ClubeDeAssinaturas.MontagemDePlanoService.ConfigurarEstabelecimentoParaUtilizarClubeSeNecessario(filtro.IdEstabelecimento);

                estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                    .ObterEstabelecimentoFormaDePagamento(filtro.IdEstabelecimento, (int)FormaPagamentoEnum.ClubeDeAssinaturaPorLink);
            }
            else
            {
                estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
               .ObterEstabelecimentoFormaDePagamento(filtro.IdEstabelecimento, (int)FormaPagamentoEnum.ClubeDeAssinatura);
            }

            var numeroDeParcelas = 1;
            var troco = 0;

            var motivoDesconto = filtro.IdMotivoDesconto == null ? null : Domain.Financeiro.MotivoDescontoRepository.Load(filtro.IdMotivoDesconto.Value);

            transacao.DataHora = Calendario.Agora();
            transacao.DataReferencia = filtro.DataDoPagamento;
            transacao.PessoaQueRealizou = filtro.PessoaQueRealizou;
            transacao.TipoTransacao = new TipoTransacao((int)TipoTransacaoEnum.Pagamento);
            transacao.PessoaQueRecebeu = filtro.PessoaQueRecebeu;
            transacao.PessoaQuePagou = filtro.PessoaQuePagou;
            transacao.TotalPagar = filtro.ValorPago;
            transacao.TotalClubeDeAssinaturas = filtro.ValorPago;
            transacao.TotalPagoEmDinheiro = filtro.ValorPago;
            transacao.TotalPago = filtro.ValorPago;
            transacao.Troco = troco;

            transacao.AdicionarTransacaoFormaPagamento(estabelecimentoFormaPagamento.FormaPagamento, numeroDeParcelas, filtro.ValorPago, filtro.Taxas);
            transacao.AdicionarTransacaoItem(motivoDesconto, filtro.ValorDesconto, filtro.Nome, filtro.Tipo, filtro.IdObjetoReferencia);

            return transacao;
        }

        public Transacao CreateParaVendaPacoteHotsite(VendaHotsiteDTO filtro, PessoaFisica pessoaQuePagou, PessoaJuridica pessoaQueRecebeu)
        {
            var transacao = new Transacao();

            var venda = Domain.Financeiro.VendasPeloHotsiteService.CriarVendaItensHotsite(filtro, pessoaQuePagou.IdPessoa);

            transacao.Vendas = new List<Venda> {
                venda
            };

            var valorTotalDaVenda = venda.ItensVenda.Sum(l => l.SubTotal);

            transacao.DataHora = Calendario.Agora();
            transacao.DataReferencia = Calendario.Agora();
            transacao.TipoTransacao = new TipoTransacao((int)TipoTransacaoEnum.Pagamento);
            transacao.PessoaQueRecebeu = pessoaQueRecebeu;
            transacao.PessoaQueRealizou = pessoaQuePagou;
            transacao.PessoaQuePagou = pessoaQuePagou;
            transacao.TotalPagar = valorTotalDaVenda;
            transacao.TotalPago = valorTotalDaVenda;

            EstabelecimentoFormaPagamento ep;
            switch (filtro.Pagamento.MetodoPagamento)
            {
                case MetodoDePagamentoNoGatewayEnum.CartaoDeCredito:
                case null:
                    transacao.TotalPagoEmCredito = valorTotalDaVenda;
                    ep = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                        .ObterEstabelecimentoFormaDePagamento(filtro.IdEstabelecimento, (int)FormaPagamentoEnum.PagarmeCredito);
                    break;
                case MetodoDePagamentoNoGatewayEnum.Pix:
                    transacao.TotalPagoEmOutros = valorTotalDaVenda;
                    ep = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                        .ObterEstabelecimentoFormaDePagamento(filtro.IdEstabelecimento, (int)FormaPagamentoEnum.PagarmePix);
                    break;
                default:
                    throw new NotImplementedException();
            }
            transacao.Troco = 0;
            
            var taxas = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService
                .ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(filtro.IdEstabelecimento, ep.FormaPagamento.ToMetodoDePagamentoNoGatewayEnum(), filtro.NumeroParcelas);
            var taxasDTO = new TaxasDTO(taxas.ObterPercentualTotalPorTransacao(), taxas.ObterValorFixoTotalPorTransacao());

            var idReferencia = filtro.Itens.Select(i => i.IdReferenciaItem).FirstOrDefault();

            transacao.AdicionarTransacaoFormaPagamento(ep.FormaPagamento, filtro.NumeroParcelas, valorTotalDaVenda, taxasDTO);
            transacao.AdicionarTransacaoItem(filtro.Nome, filtro.Tipo, idReferencia);
            
            venda.Transacao = transacao;

            return transacao;
        }

        public Transacao CreateParaPagamentoAntecipadoHotsite(PagamentoAntecipadoHotsiteDto filtro)
        {
            var dataHora = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterDataHoraTransacaoCorreta(filtro.Pagamento.DataPagamento);
            
            var transacao = new Transacao();
            var venda = new Venda(transacao);
            transacao.Vendas = new List<Venda>();
            transacao.Vendas.Add(venda);

            EstabelecimentoFormaPagamento ep;

            switch (filtro.Pagamento.MetodoPagamento)
            {
                case MetodoDePagamentoNoGatewayEnum.CartaoDeCredito:
                case null:
                    transacao.TotalPagoEmCredito = filtro.ValorPago;
                    ep = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                        .ObterEstabelecimentoFormaDePagamento(filtro.IdEstabelecimento, (int)FormaPagamentoEnum.PagarmeCredito);
                    break;
                case MetodoDePagamentoNoGatewayEnum.Pix:
                    transacao.TotalPagoEmOutros = filtro.ValorPago;
                    ep = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                        .ObterEstabelecimentoFormaDePagamento(filtro.IdEstabelecimento, (int)FormaPagamentoEnum.PagarmePix);
                    break;
                default:
                    throw new NotImplementedException();
            }

            transacao.DataHora = dataHora;
            transacao.DataReferencia = Calendario.Agora();
            transacao.TipoTransacao = new TipoTransacao((int)TipoTransacaoEnum.Pagamento);
            transacao.PessoaQueRecebeu = filtro.PessoaQueRecebeu;
            transacao.PessoaQuePagou = filtro.PessoaQuePagou;
            transacao.PessoaQueRealizou = filtro.PessoaQuePagou;
            transacao.TotalPagar = filtro.ValorPago;
            transacao.TotalPago = filtro.ValorPago;
            
            var taxas = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService
                .ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(filtro.IdEstabelecimento, ep.FormaPagamento.ToMetodoDePagamentoNoGatewayEnum(), filtro.NumeroParcelas);
            var taxasDTO = new TaxasDTO(taxas.ObterPercentualTotalPorTransacao(), taxas.ObterValorFixoTotalPorTransacao());
            
            transacao.AdicionarTransacaoFormaPagamento(ep.FormaPagamento, filtro.NumeroParcelas, filtro.ValorPago, taxasDTO);
            transacao.AdicionarTransacaoItem(filtro.Nome, filtro.Tipo, filtro.IdReferencia);
            
            Domain.Financeiro.TransacaoService
                .ColocarCompraDeCreditoNaTransacao(transacao: venda.Transacao, formaPagamentoPrePago: FormaPagamentoPrePagoEnum.CreditoDePagamentoOnlineHotsite, 
                    valorDoCredito: filtro.ValorPago);

            return transacao;
        }
    }
}
