﻿using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.DTO.Connect.Pagarme;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using Trinks.Integracoes.Pagarme.Connect.V2.Dtos.Response;

namespace Perlink.Trinks.Financeiro.Interfaces
{
    public interface IIntegracaoConnectPagarmeService
    {
        RetornoPagamentoPosDto RealizarPagamentoPos(RealizarPagamentoDto realizarPagamentoDto);
        bool CancelarOrdem(string secretKey, string codigoPreTransacao);
        OrderResponseDto ObterOrdem(string secretKey, string codigoTransacao);
        void TratarWebhook(WebhookConnectPagarmeDto webhook);
        bool CapturarTransacaoComSplit(int idEstabelecimento, SplitDTO split, string secretKey);
        string RealizarCadastroRecebedor(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto);
        RetornoSaldoRecebedorDto ObterSaldoRecebedor(string secretKey, string idRecebedor);
        RetornoListaDeRecebedoresDto ObterListaDeRecebedores(string secretKey, int page, int size);
        List<PayableResponseDto> ConsultarPagamentosAsync(EstabelecimentoConfiguracaoPOS configuracaoPos, DateTime dataDasTransacoes);
        List<ReceivableResponseDto> ConsultarPagamentosV5Async(EstabelecimentoConfiguracaoPOS configuracaoPos, DateTime dataDasTransacoes);
        List<TransactionResponseDto> ConsultarTransacoesAsync(EstabelecimentoConfiguracaoPOS configuracaoPos, DateTime dataDasTransacoes);
        List<TaxasTransacaoRecebedorDto> ObterTaxasDaTransacaoPorRecebedor(EstabelecimentoConfiguracaoPOS configuracaoPos, string chargeId);
    }
}