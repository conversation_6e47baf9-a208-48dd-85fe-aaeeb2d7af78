﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Text;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Stories
{
    public class ExportacaoComissoesStory : BaseStory, IExportacaoComissoesStory
    {

        public MemoryStream ExportarLancamentosDescontosEBonificacoesProfissional(ParametrosFiltrosRelatorio parametros)
        {

            var resultado = new List<ExportacaoLancamentoProfissionalDTO>();

            resultado.AddRange(ListarLancamentosDeValesAdiantamentoParaProfissional(parametros));
            resultado.AddRange(ListarLancamentosDescontoSplitParaParaProfissional(parametros));
            resultado.AddRange(ListarLancamentosBonificacoesParaProfissional(parametros));
            resultado.AddRange(ListarLancamentosDeVendaProdutoParaProfissional(parametros));

            resultado = resultado.OrderBy(i => i.DataLancamento).ToList();

            if (resultado == null || !resultado.Any())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não foram encontrados lançamentos para os filtros aplicados");
                return null;
            }

            return GerarArquivoCsv(parametros, resultado);
        }

        private List<ExportacaoLancamentoProfissionalDTO> ListarLancamentosDeValesAdiantamentoParaProfissional(ParametrosFiltrosRelatorio parametros)
        {
            var lancamentos = Domain.Despesas.LancamentoRepository.ListarVales(parametros);
            return ListarLancamentosDeDespesasDoProfissional(lancamentos, valorPositivo: false);
        }

        private List<ExportacaoLancamentoProfissionalDTO> ListarLancamentosDescontoSplitParaParaProfissional(ParametrosFiltrosRelatorio parametros)
        {
            var lancamentos = Domain.Despesas.LancamentoRepository.ListarSplitsDePagamento(parametros);
            return ListarLancamentosDeDespesasDoProfissional(lancamentos, valorPositivo: false);
        }

        private List<ExportacaoLancamentoProfissionalDTO> ListarLancamentosBonificacoesParaProfissional(ParametrosFiltrosRelatorio parametros)
        {
            var lancamentos = Domain.Despesas.LancamentoRepository.ListarBonificacoes(parametros);
            return ListarLancamentosDeDespesasDoProfissional(lancamentos, valorPositivo: true);
        }

        private List<ExportacaoLancamentoProfissionalDTO> ListarLancamentosDeDespesasDoProfissional(IQueryable<Despesas.Lancamento> lancamentos, bool valorPositivo)
        {
            var pessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var lista = (from lanc in lancamentos
                         join prof in pessoaFisica on lanc.PessoaQueRecebeuOuPagou.IdPessoa equals prof.IdPessoa
                         join criador in pessoaFisica on lanc.PessoaQueCriou.IdPessoa equals criador.IdPessoa
                         select new ExportacaoLancamentoProfissionalDTO
                         {
                             DataLancamento = lanc.DataVencimento,
                             DataPagamento = lanc.DataPagamento,
                             NomeTipo = lanc.LancamentoCategoria.Nome,
                             Descricao = lanc.Descricao,
                             NomeProfissional = prof.NomeCompleto,
                             NomeFormaPagamento = lanc.FormaPagamento.Nome,
                             Valor = lanc.Valor,
                             NomePessoaQueRegistrou = criador.NomeCompleto
                         }).ToList();

            foreach (var lancamento in lista)
            {
                lancamento.Valor = valorPositivo ? lancamento.Valor.ToValorPositivo() : lancamento.Valor.ToValorNegativo();
            }

            return lista;
        }

        private List<ExportacaoLancamentoProfissionalDTO> ListarLancamentosDeVendaProdutoParaProfissional(ParametrosFiltrosRelatorio parametros)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ListarComDescontoProfissional(parametros)
                .Select(f => new ExportacaoLancamentoProfissionalDTO
                {
                    DataLancamento = f.TransacaoFormaPagamento.Transacao.DataReferencia,
                    DataPagamento = f.DataPagamento,
                    NomeTipo = "Venda de produto para profissional",
                    Descricao = string.Empty,
                    NomeProfissional = f.TransacaoFormaPagamento.Transacao.PessoaQuePagou.NomeCompleto,
                    NomeFormaPagamento = f.TransacaoFormaPagamento.FormaPagamento.Nome,
                    Valor = f.Valor.ToValorNegativo(),
                    NomePessoaQueRegistrou = f.TransacaoFormaPagamento.Transacao.PessoaQueRealizou.NomeCompleto
                }).ToList();
        }

        private MemoryStream GerarArquivoCsv(ParametrosFiltrosRelatorio parametros, List<ExportacaoLancamentoProfissionalDTO> resultado)
        {
            var csv = new Csv();

            csv.AdicionarLinha("Data início: " + parametros.DataInicial.Formatar());
            csv.AdicionarLinha("Data fim: " + parametros.DataFinal.Formatar());
            csv.AdicionarLinha("Relatório gerado em " + Calendario.Agora().ToBrazilianLongDateTimeString());
            csv.AdicionarLinha("");

            var cabecalho = new Csv.Linha();
            cabecalho.AdicionarCelula("Data do lançamento");
            cabecalho.AdicionarCelula("Data de pagamento");
            cabecalho.AdicionarCelula("Tipo");
            cabecalho.AdicionarCelula("Descrição");
            cabecalho.AdicionarCelula("Profissional");
            cabecalho.AdicionarCelula("Forma de pagamento");
            cabecalho.AdicionarCelula("Valor (R$)");
            cabecalho.AdicionarCelula("Quem registrou");
            csv.AdicionarLinha(cabecalho);

            foreach (var lancamento in resultado)
            {
                var linha = new Csv.Linha();
                linha.AdicionarCelula(lancamento.DataLancamento.Formatar());
                linha.AdicionarCelula(lancamento.DataPagamento.Formatar());
                linha.AdicionarCelula(lancamento.NomeTipo);
                linha.AdicionarCelula(lancamento.Descricao);
                linha.AdicionarCelula(lancamento.NomeProfissional);
                linha.AdicionarCelula(lancamento.NomeFormaPagamento);
                linha.AdicionarCelula(lancamento.Valor.ValorDecimal());
                linha.AdicionarCelula(lancamento.NomePessoaQueRegistrou);
                csv.AdicionarLinha(linha);
            }

            return csv.ObterArquivo();
        }
    }
}
