﻿using Perlink.Trinks.Pagamentos;
using Perlink.Trinks.Pessoas;
using System;

namespace Perlink.Trinks.Financeiro.DTO
{
    public class TransicaoParaClubeAssinaturaDTO
    {
        public PessoaFisica PessoaQuePagou { get; set; }
        public PessoaJuridica PessoaQueRecebeu { get; set; }
        public PessoaFisica PessoaQueRealizou { get; set; }
        public decimal ValorPago { get; set; }
        public decimal? ValorDesconto { get; set; }
        public int? IdMotivoDesconto { get; set; }
        public string Nome { get; set; }
        public string Tipo { get; set; }
        public int IdEstabelecimento { get; set; }
        public int IdObjetoReferencia { get; set; }
        public DateTime DataDoPagamento { get; set; }
        public bool EhAssinaturaPorLink { get; set; }
        public TaxasDTO Taxas { get; set; }
        public bool EhTransacaoDeMultaDeCancelamento { get; set; }
    }
}
