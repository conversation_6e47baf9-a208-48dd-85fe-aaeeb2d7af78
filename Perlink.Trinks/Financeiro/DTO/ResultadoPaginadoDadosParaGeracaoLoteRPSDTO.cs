﻿using Perlink.Shared.NHibernate.Paginacao;
using System.Collections.Generic;

namespace Perlink.Trinks.Financeiro.DTO
{
    public class ResultadoPaginadoDadosParaGeracaoLoteRPSDTO : ResultadoPaginado<DadosParaGeracaoLoteRPSDTO>
    {
        public ResultadoPaginadoDadosParaGeracaoLoteRPSDTO() : base() {}

        public ResultadoPaginadoDadosParaGeracaoLoteRPSDTO(List<DadosParaGeracaoLoteRPSDTO> listaPaginadaDeDadosParaGeracaoLoteRPS, ParametrosPaginacao parametrosPaginacao, decimal valorTotalGeralDosItens)
            :base(listaPaginadaDeDadosParaGeracaoLoteRPS, parametrosPaginacao)

        {
            ValorTotalGeralDosItens = valorTotalGeralDosItens;
        }

        public decimal ValorTotalGeralDosItens { get; set; }
    }
}
