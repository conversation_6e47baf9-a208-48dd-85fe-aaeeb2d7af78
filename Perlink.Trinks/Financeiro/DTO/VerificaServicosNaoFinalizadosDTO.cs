﻿using System;

namespace Perlink.Trinks.Financeiro.DTO
{
    public class VerificaServicosNaoFinalizadosDTO
    {
        public VerificaServicosNaoFinalizadosDTO(bool temConfiguracaoServicosNaoFinalizadosHabilitada, DateTime data, 
            int? idClienteEstabelecimento, int? origemDoFechamento, bool escolheuProsseguirComFechamento, int idEstabelecimento)
        {
            TemConfiguracaoServicosNaoFinalizadosHabilitada = temConfiguracaoServicosNaoFinalizadosHabilitada;
            Data = data;
            IdClienteEstabelecimento = idClienteEstabelecimento;
            OrigemDoFechamento = origemDoFechamento;
            EscolheuProsseguirComFechamento = escolheuProsseguirComFechamento;
            IdEstabelecimento = idEstabelecimento;
        }

        public bool TemConfiguracaoServicosNaoFinalizadosHabilitada { get; set; }
        public DateTime Data { get; set; } 
        public int? IdClienteEstabelecimento { get; set; }
        public int? OrigemDoFechamento { get; set; } 
        public bool EscolheuProsseguirComFechamento { get; set; }
        public int IdEstabelecimento { get; set; }
    }
}
