﻿using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Financeiro.DTO
{
    public class RelatorioComissaoProfissionalDTO
    {
        public List<FechamentoMesComissaoDTO> ComissaoProdutosLista { get; set; }
        public List<FechamentoMesComissaoDTO> ComissaoServicosLista { get; set; }
        public List<FechamentoMesComissaoDTO> ComissaoPacotesLista { get; set; }
        public List<FechamentoMesLancamentoDTO> ValesLista { get; set; }
        public List<FechamentoMesLancamentoDTO> SplitsLista { get; set; }
        public List<FechamentoMesLancamentoDTO> BonificacoesLista { get; set; }
        public List<FechamentoMesComissaoDTO> CompraProdutoLista { get; set; }
        public List<FechamentoMesGorjetaDTO> GorjetasLista { get; set; }

        public string NomeProfissional { get; set; }

        public string MensagemImpressaoRelatorioComissoes { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }

        public decimal ComissaoServicos { get; set; }
        public decimal ComissaoProdutos { get; set; }
        public decimal ComissaoPacotes { get; set; }
        public decimal TotalRecebimentos { get; set; }
        public decimal Vales { get; set; }
        public decimal Splits { get; set; }
        public decimal Bonificacoes { get; set; }
        public decimal Gorjetas { get; set; }
        public decimal CompraProduto { get; set; }
        public decimal TotalDescontos { get; set; }

        public decimal ValorAReceber { get; set; }
        public int TipoData { get; set; }
        public string TextoTotalComissaoProdutos { get; set; }
        public string TextoTotalCompraProdutos { get; set; }
        public bool ExibeGorjeta { get; set; }
        public bool ExibirCampoAssinaturaProfissional { get; set; }
        public TipoImpressaoComissaoEnum TipoDeImpressao { get; set; }
        public bool EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit { get; set; }
        public bool PodeExibirColunaProdutoRateio { get; set; }
        public bool PodeExibirColunaRateio { get; set; }
        public string NomeCliente { get; set; }
        public string NomeEstabelecimento { get; set; }

        public Estabelecimento EstabelecimentoAutenticado { get; set; }
        public string LogoCaminhoWeb { get; set; }
        public bool HabilitouDespesasPersonalizadas { get; set; }

        //public bool EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit { get { return Domain.Financeiro.ComissaoRepository.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit(EstabelecimentoAutenticado.PessoaJuridica.IdPessoa); } }
        //public bool PodeExibirColunaProdutoRateio { get { return EstabelecimentoAutenticado.PossuiCustoOperacionalProdutoHabilitado(); } }
        //public bool PodeExibirColunaRateio { get { return EstabelecimentoAutenticado.PossuiCustoOperacionalHabilitado(); } }
    }
}
