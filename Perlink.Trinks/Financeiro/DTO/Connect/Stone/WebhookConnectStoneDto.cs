﻿using Newtonsoft.Json;
using System;

namespace Perlink.Trinks.Financeiro.DTO.Connect.Stone
{

    public class WebhookConnectStoneDto
    {

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("account")]
        public Account Account { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("data")]
        public Data Data { get; set; }
    }

    public class Account
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }
    }

    public class Card
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("first_six_digits")]
        public string FirstSixDigits { get; set; }

        [JsonProperty("last_four_digits")]
        public string LastFourDigits { get; set; }

        [JsonProperty("brand")]
        public string Brand { get; set; }

        [JsonProperty("exp_month")]
        public int ExpMonth { get; set; }

        [JsonProperty("exp_year")]
        public int ExpYear { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updated_at")]
        public DateTime UpdatedAt { get; set; }
    }

    public class Customer
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("delinquent")]
        public bool Delinquent { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updated_at")]
        public DateTime UpdatedAt { get; set; }
    }

    public class Data
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("gateway_id")]
        public string GatewayId { get; set; }

        [JsonProperty("amount")]
        public int Amount { get; set; }

        [JsonProperty("status")]
        public StoneChargeEnum Status { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("payment_method")]
        public string PaymentMethod { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [JsonProperty("pending_cancellation")]
        public bool PendingCancellation { get; set; }

        [JsonProperty("customer")]
        public Customer Customer { get; set; }

        [JsonProperty("order")]
        public Order Order { get; set; }

        [JsonProperty("last_transaction")]
        public LastTransaction LastTransaction { get; set; }
    }


    public class LastTransaction
    {
        [JsonProperty("transaction_type")]
        public string TransactionType { get; set; }

        [JsonProperty("acquirer_tid")]
        public string AcquirerTid { get; set; }

        [JsonProperty("acquirer_nsu")]
        public string AcquirerNsu { get; set; }

        [JsonProperty("acquirer_auth_code")]
        public string AcquirerAuthCode { get; set; }

        [JsonProperty("acquirer_return_code")]
        public string AcquirerReturnCode { get; set; }

        [JsonProperty("operation_type")]
        public string OperationType { get; set; }

        [JsonProperty("card")]
        public Card Card { get; set; }

        [JsonProperty("funding_source")]
        public string FundingSource { get; set; }

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("gateway_id")]
        public string GatewayId { get; set; }

        [JsonProperty("amount")]
        public int Amount { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [JsonProperty("entry_mode")]
        public string EntryMode { get; set; }

        [JsonProperty("initiator_date")]
        public DateTime InitiatorDate { get; set; }

        [JsonProperty("initiator_reference")]
        public string InitiatorReference { get; set; }

        [JsonProperty("initiator_transaction_key")]
        public string InitiatorTransactionKey { get; set; }

        [JsonProperty("device_serial_number")]
        public string DeviceSerialNumber { get; set; }
    }

    public class Order
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("amount")]
        public int Amount { get; set; }

        [JsonProperty("closed")]
        public bool Closed { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("customer_id")]
        public string CustomerId { get; set; }
    }
}
