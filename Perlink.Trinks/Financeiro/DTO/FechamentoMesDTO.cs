﻿using Perlink.Trinks.Despesas;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;

namespace Perlink.Trinks.Financeiro.DTO
{

    public class FechamentoMesCompraProdutoDTO : FechamentoMesComissaoDTO
    {

        [XmlIgnore]
        public int MedidasPorUnidade { get; set; }

        public string TextoQuantidade { get; set; }
        //[XmlIgnore]
        //public EstabelecimentoProduto EstabelecimentoProduto { get; set; }

        [XmlIgnore]
        public TipoDeQuantidadeDeProduto TipoDeQuantidade { get; set; }

        [XmlIgnore]
        public UnidadeMedida UnidadeMedida { get; set; }
    }

    [Serializable]
    public class FechamentoMesComissaoDeProdutoDTO : FechamentoMesComissaoDTO
    {

        [XmlIgnore]
        public int MedidasPorUnidade { get; set; }

        public string TextoQuantidade { get; set; }

        [XmlIgnore]
        public TipoDeQuantidadeDeProduto TipoDeQuantidade { get; set; }

        [XmlIgnore]
        public UnidadeMedida UnidadeMedida { get; set; }
    }

    [Serializable]
    public class FechamentoMesComissaoDeServicoDTO : FechamentoMesComissaoDTO
    {
    }

    [Serializable]
    public class FechamentoMesComissaoDePacoteDTO : FechamentoMesComissaoDTO
    {
    }

    [Serializable]
    public class FechamentoMesComissaoDTO
    {
        public decimal Comissao { get; set; }
        public int IdPessoaComissionada { get; set; }
        public string Nome { get; set; }
        public String Assistente { get; set; }
        public int Quantidade { get; set; }
        public decimal ValorPago { get; set; }
        public decimal Rateio { get; set; }
        public bool PodeExibirValoresDeServicoEhProduto { get; set; }
        public DateTime DataPagamento { get; set; }

        [XmlIgnore]
        public bool PossuiComissaoParcelada { get; internal set; }

        public bool EhComissaoComSplit { get; set; }
        public string TextoQuantidade { get; set; }

        public decimal ValorProporcional { get; set; }

        public DateTime DataAtendimento { get; set; }

        public DateTime DataLiberacaoPagamento { get; set; }
        public DateTime DataVenda { get; set; }

        public string NomeCliente { get; set; }

        public bool EhItemPacoteCliente { get; set; }

        public bool EhPacoteDaRede { get; set; }

        public bool UsouPontosDeFidelidade { get; set; }

        [XmlIgnore]
        public string FormaDePagamento { get; set; }

        [XmlIgnore]
        public List<TransacaoFormaPagamento> FormasDePagamento { get; set; }

        public bool ComoAssistente { get; set; }

        public bool TeveAssistente { get; set; }

        public string ApelidoOuNomeAssistente { get; set; }
        public decimal? CustoDescartavel { get; set; }

        public static IEnumerable<FechamentoMesComissaoDeProdutoDTO> ToFechamentoMesComissao(IEnumerable<ItemVendaProduto> obj)
        {
            if (obj == null)
                return null;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            var produtoFracionadoEstaAtivo = estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo;

            return (from i in obj
                    group i by new { i.EstabelecimentoProduto, i.Comissao.PessoaComissionada.IdPessoa } into g
                    select new FechamentoMesComissaoDeProdutoDTO
                    {
                        Comissao = g.Sum(f => f.Comissao != null ? f.Comissao.ComissaoParaPagar : 0) ?? 0,
                        Nome = g.Key.EstabelecimentoProduto.Descricao,
                        Quantidade = g.Sum(f => f.Quantidade),
                        ValorPago = g.Sum(f => f.Comissao != null ? f.Comissao.ValorBruto + f.Comissao.DescontoCliente : f.ValorFinal()),
                        TextoQuantidade = produtoFracionadoEstaAtivo ?
                                        Domain.Pessoas.EstabelecimentoProdutoService.ObterTextoDeQuantidadeComUnidadeDeMedida(g.Sum(p => p.Quantidade), g.Key.EstabelecimentoProduto.UnidadeMedida.Simbolo, g.Key.EstabelecimentoProduto.MedidasPorUnidade, false, false, true, true) :
                                        g.Sum(f => f.Quantidade).ToString(),
                        TipoDeQuantidade = g.Select(f => f.TipoDeQuantidade).First(),
                        UnidadeMedida = g.Key.EstabelecimentoProduto.UnidadeMedida,
                        MedidasPorUnidade = g.Key.EstabelecimentoProduto.MedidasPorUnidade,
                        IdPessoaComissionada = g.Key.IdPessoa
                    })
                    .OrderBy(f => f.Nome);
        }

        public static IEnumerable<FechamentoMesCompraProdutoDTO> ToFechamentoMesComprasProduto(List<ItemVendaProduto> obj, 
            List<TransacaoFormaPagamentoParcela> parcelas)
        {
            if (obj == null)
                return null;

            var dados = (from i in obj
                        join p in parcelas on i.Venda.Transacao.Id equals p.TransacaoFormaPagamento.Transacao.Id
                        select new
                        {
                            EstabelecimentoProduto = new
                            {
                                i.EstabelecimentoProduto.Id,
                                i.EstabelecimentoProduto.Descricao,
                                i.EstabelecimentoProduto.UnidadeMedida,
                                i.EstabelecimentoProduto.MedidasPorUnidade,
                                IdPessoaComissionada = i.Venda.Transacao.PessoaQuePagou.IdPessoa
                            },
                            SubTotal = (i.SubTotal + i.Desconto) / p.TransacaoFormaPagamento.NumeroParcelas,
                            i.Quantidade,
                            i.TipoDeQuantidade
                        })
                .ToList();

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            var produtoFracionadoEstaAtivo = estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo;

            return (from i in dados
                    group i by i.EstabelecimentoProduto into g
                    select new FechamentoMesCompraProdutoDTO
                    {
                        Comissao = g.Sum(f => f.SubTotal),
                        Nome = g.Key.Descricao,
                        Quantidade = g.Sum(f => f.Quantidade),
                        ValorPago = g.Sum(f => f.SubTotal),
                        IdPessoaComissionada = g.Key.IdPessoaComissionada,
                        TextoQuantidade =
                                produtoFracionadoEstaAtivo ?
                                        Domain.Pessoas.EstabelecimentoProdutoService.ObterTextoDeQuantidadeComUnidadeDeMedida(g.Sum(f => f.Quantidade), g.Key.UnidadeMedida.Simbolo, g.Key.MedidasPorUnidade, false, false, true, true) :
                                        g.Sum(f => f.Quantidade).ToString(),
                        TipoDeQuantidade = g.Select(f => f.TipoDeQuantidade).First(),
                        UnidadeMedida = g.Key.UnidadeMedida,
                        MedidasPorUnidade = g.Key.MedidasPorUnidade
                    }).OrderBy(f => f.Nome)
                   .ToList();
        }
    }

    [Serializable]
    public class FechamentoMesGorjetaDTO
    {
        public DateTime Data { get; set; }
        public ClienteEstabelecimento Cliente { get; set; }
        public DateTime? DataPagamento { get; set; }
        public StatusGorjetaEnum StatusGorjeta { get; set; }
        public decimal Valor { get; set; }

    }

    [Serializable]
    [XmlRoot("FechamentoMesModel")]
    public class FechamentoMesDTO
    {

        public FechamentoMesDTO()
        {
            FechamentosProfissionais = new List<FechamentoMesProfissionalDTO>();

            var dataInicial = Calendario.Hoje().AddMonths(-1);
            Mes = dataInicial.Month;
            Ano = dataInicial.Year;
        }

        public FechamentoMesDTO(FechamentoFolhaMes objeto, List<FechamentoFolhaMesProfissional> fechamentosProfissionais = null, FechamentoMesDTO model = null)
        {
            IdFechamentoMes = objeto.Id;
            Ano = objeto.ReferenciaInicio.Year;
            Mes = objeto.ReferenciaInicio.Month;
            MesFechado = objeto.MesFechado;
            PessoaQueFechou = objeto.PessoaUltimaAlteracao == null ? null : objeto.PessoaUltimaAlteracao.NomeCompleto;
            DataUltimaAlteracao = objeto.DataUltimaAlteracao;

            if (fechamentosProfissionais != null)
            {
                FechamentosProfissionais = fechamentosProfissionais.Select(f => new FechamentoMesProfissionalDTO(f)).ToList();
                DataUltimaAtuzalicaoDeValores = fechamentosProfissionais.Any() ? fechamentosProfissionais.Min(f => f.DataUltimaAlteracao) : Calendario.Agora();
            }

            if (model != null)
            {
                NomeProfissional = model.NomeProfissional;
            }

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit = Domain.Financeiro.ComissaoRepository.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit(estabelecimento.PessoaJuridica.IdPessoa) ? 1 : 0;
        }

        public int Ano { get; set; }
        public bool DadosCarregados { get { return FechamentosProfissionais.Any(); } }

        [XmlArrayItem("FechamentoMesProfissionalModel")]
        public List<FechamentoMesProfissionalDTO> FechamentosProfissionais { get; set; }

        public int Mes { get; set; }

        public string MesAno
        {
            get
            {
                if (Mes < 1 || Mes > 12 || Ano < 2012 || Ano > Calendario.Hoje().Year)
                    return null;
                return string.Format("{0:00}/{1:0000}", Mes, Ano);
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                {
                    Mes = 0;
                    Ano = 0;
                    return;
                }
                var partes = value.Split('/');
                if (partes.Count() != 2)
                    return;

                Mes = int.Parse(partes[0]);
                Ano = int.Parse(partes[1]);
            }
        }

        public bool MesFechado { get; set; }
        public String NomeProfissional { get; set; }

        #region Totais

        public decimal Alimentacao { get { return FechamentosProfissionais.Sum(f => f.Alimentacao); } }
        public decimal ComissaoProdutos { get { return FechamentosProfissionais.Sum(f => f.ComissaoProdutos); } }
        public decimal ComissaoServicos { get { return FechamentosProfissionais.Sum(f => f.ComissaoServicos); } }
        public decimal ComissaoPacotes { get { return FechamentosProfissionais.Sum(f => f.ComissaoPacotes); } }
        public decimal Bonificacoes { get { return FechamentosProfissionais.Sum(f => f.Bonificacoes); } }
        public decimal CompraProduto { get { return FechamentosProfissionais.Sum(f => f.CompraProduto); } }
        public DateTime DataUltimaAlteracao { get; set; }
        public DateTime DataUltimaAtuzalicaoDeValores { get; set; }
        public decimal DecimoTerceiro { get { return FechamentosProfissionais.Sum(f => f.DecimoTerceiro); } }
        public decimal FaltasAtrasos { get { return FechamentosProfissionais.Sum(f => f.FaltasAtrasos); } }
        public decimal Ferias { get { return FechamentosProfissionais.Sum(f => f.Ferias); } }
        public decimal HoraExtra { get { return FechamentosProfissionais.Sum(f => f.HoraExtra); } }
        public int IdFechamentoMes { get; set; }
        public decimal INSS { get { return FechamentosProfissionais.Sum(f => f.INSS); } }
        public decimal OutrosDescontos { get { return FechamentosProfissionais.Sum(f => f.OutrosDescontos); } }
        public decimal OutrosRecebimentos { get { return FechamentosProfissionais.Sum(f => f.OutrosRecebimentos); } }
        public string PessoaQueFechou { get; set; }
        public decimal Salario { get { return FechamentosProfissionais.Sum(f => f.Salario); } }
        public decimal SalarioFamilia { get { return FechamentosProfissionais.Sum(f => f.SalarioFamilia); } }

        public decimal TotalDescontos
        {
            get
            {
                return INSS +
                    FaltasAtrasos +
                    Vales +
                    SplitsPagamento +
                    CompraProduto +
                    ValeTransporte +
                    Alimentacao +
                    OutrosDescontos;
            }
        }

        public decimal TotalRecebimentos
        {
            get
            {
                return Salario +
                    ComissaoServicos +
                    ComissaoProdutos +
                    ComissaoPacotes +
                    Bonificacoes +
                    HoraExtra +
                    SalarioFamilia +
                    Ferias +
                    DecimoTerceiro +
                    OutrosRecebimentos;
            }
        }

        public decimal SplitsPagamento { get { return FechamentosProfissionais.Sum(f => f.SplitsPagamento); } }
        public decimal Vales { get { return FechamentosProfissionais.Sum(f => f.Vales); } }
        public decimal ValeTransporte { get { return FechamentosProfissionais.Sum(f => f.ValeTransporte); } }

        public int EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit { get; set; }

        public decimal ValorAReceber
        {
            get { return TotalRecebimentos - TotalDescontos; }
        }

        #endregion Totais
    }

    [Serializable]
    public class FechamentoMesProfissionalDTO
    {

        public FechamentoMesProfissionalDTO()
        {
        }

        public FechamentoMesProfissionalDTO(FechamentoFolhaMesProfissional fechamentoProfissional)
        {
            if (fechamentoProfissional.ComissaoProdutosLista != null && fechamentoProfissional.ComissaoProdutosLista.Any())
            {
                ComissaoProdutosLista = fechamentoProfissional.ComissaoProdutosLista;
            }

            if (fechamentoProfissional.ComissaoServicosLista != null)
                ComissaoServicosLista = fechamentoProfissional.ComissaoServicosLista;

            if (fechamentoProfissional.ComissaoPacotesLista != null)
                ComissaoPacotesLista = fechamentoProfissional.ComissaoPacotesLista;

            if (fechamentoProfissional.ValesLista != null)
                ValesLista = fechamentoProfissional.ValesLista.Select(f => new FechamentoMesLancamentoDTO(f)).ToList();

            if (fechamentoProfissional.BonificacoesLista != null && fechamentoProfissional.BonificacoesLista.Any())
                BonificacoesLista = fechamentoProfissional.BonificacoesLista.Select(f => new FechamentoMesLancamentoDTO(f)).ToList();

            if (fechamentoProfissional.CompraProdutoLista != null && fechamentoProfissional.CompraProdutoLista.Any())
            {
                CompraProdutoLista = fechamentoProfissional.CompraProdutoLista;
            }

            if (fechamentoProfissional.FechamentoFolhaMes != null)
            {
                ReferenciaInicio = fechamentoProfissional.FechamentoFolhaMes.ReferenciaInicio;
                ReferenciaFim = fechamentoProfissional.FechamentoFolhaMes.ReferenciaFim;
                MesFechado = fechamentoProfissional.FechamentoFolhaMes.MesFechado;
            }

            IdEstabelecimentoProfissional = fechamentoProfissional.EstabelecimentoProfissional.Codigo;
            NomeProfissional = fechamentoProfissional.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto;
            Salario = fechamentoProfissional.Salario;
            ComissaoServicos = fechamentoProfissional.ComissaoServicos;
            ComissaoProdutos = fechamentoProfissional.ComissaoProdutos;
            ComissaoPacotes = fechamentoProfissional.ComissaoPacotes;
            Bonificacoes = fechamentoProfissional.Bonificacoes;
            HoraExtra = fechamentoProfissional.HoraExtra;
            SalarioFamilia = fechamentoProfissional.SalarioFamilia;
            Ferias = fechamentoProfissional.Ferias;
            DecimoTerceiro = fechamentoProfissional.DecimoTerceiro;
            OutrosRecebimentos = fechamentoProfissional.OutrosRecebimentos;
            INSS = fechamentoProfissional.INSS;
            FaltasAtrasos = fechamentoProfissional.FaltasAtrasos;
            Vales = fechamentoProfissional.Vales;
            SplitsPagamento = fechamentoProfissional.SplitsPagamento;
            ValeTransporte = fechamentoProfissional.ValeTransporte;
            Alimentacao = fechamentoProfissional.Alimentacao;
            CompraProduto = fechamentoProfissional.CompraProduto;
            OutrosDescontos = fechamentoProfissional.OutrosDescontos;
            Observacoes = fechamentoProfissional.Observacoes;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit = Domain.Financeiro.ComissaoRepository.EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit(estabelecimento.PessoaJuridica.IdPessoa) ? 1 : 0;

            if (ComissaoProdutosLista != null && ComissaoProdutosLista.Any())
                ComissaoProdutos =
                    ComissaoProdutosLista.Sum(c => c.Comissao);

            if (ComissaoServicosLista != null && ComissaoServicosLista.Any())
                ComissaoServicos =
                    ComissaoServicosLista.Sum(c => c.Comissao);

            if (ComissaoPacotesLista != null && ComissaoPacotesLista.Any())
                ComissaoPacotes =
                    ComissaoPacotesLista.Sum(c => c.Comissao);
        }

        public decimal Alimentacao { get; set; }

        public int Ano { get { return ReferenciaInicio.Year; } }

        public decimal ComissaoProdutos { get; set; }

        [XmlArrayItem("FechamentoMesComissaoModel")]
        public List<FechamentoMesComissaoDeProdutoDTO> ComissaoProdutosLista { get; set; }

        public decimal ComissaoServicos { get; set; }

        [XmlArrayItem("FechamentoMesComissaoModel")]
        public List<FechamentoMesComissaoDeServicoDTO> ComissaoServicosLista { get; set; }

        public decimal ComissaoPacotes { get; set; }

        [XmlArrayItem("FechamentoMesComissaoModel")]
        public List<FechamentoMesComissaoDePacoteDTO> ComissaoPacotesLista { get; set; }

        public decimal CompraProduto { get; set; }

        [XmlArrayItem("FechamentoMesComissaoModel")]
        public List<FechamentoMesCompraProdutoDTO> CompraProdutoLista { get; set; }

        public decimal DecimoTerceiro { get; set; }

        public decimal FaltasAtrasos { get; set; }

        public decimal Ferias { get; set; }

        public decimal HoraExtra { get; set; }

        public int Id { get; set; }

        public int IdEstabelecimentoProfissional { get; set; }

        public decimal INSS { get; set; }

        public int Mes { get { return ReferenciaInicio.Month; } }

        public bool MesFechado { get; set; }

        public string NomeProfissional { get; set; }

        public string Observacoes { get; set; }

        public decimal OutrosDescontos { get; set; }

        public decimal OutrosRecebimentos { get; set; }

        public DateTime ReferenciaFim { get; set; }

        public DateTime ReferenciaInicio { get; set; }

        public decimal Salario { get; set; }

        public decimal SalarioFamilia { get; set; }

        public string TextoTotalComissaoProdutos
        {
            get
            {
                return ObterTextoTotalUnidade(ComissaoProdutosLista);
            }
            set { }
        }

        public string TextoTotalCompraProdutos
        {
            get
            {
                return ObterTextoTotalUnidade(CompraProdutoLista);
            }
            set { }
        }

        public decimal Bonificacoes { get; set; }

        [XmlArrayItem("FechamentoMesBonificacaoModel")]
        public List<FechamentoMesLancamentoDTO> BonificacoesLista { get; set; }

        public decimal TotalDescontos
        {
            get
            {
                return INSS +
                    FaltasAtrasos +
                    Vales +
                    SplitsPagamento +
                    CompraProduto +
                    ValeTransporte +
                    Alimentacao +
                    OutrosDescontos;
            }
            set { }
        }

        public decimal TotalRecebimentos
        {
            get
            {
                return Salario +
                    ComissaoServicos +
                    ComissaoProdutos +
                    ComissaoPacotes +
                    Bonificacoes +
                    HoraExtra +
                    SalarioFamilia +
                    Ferias +
                    DecimoTerceiro +
                    OutrosRecebimentos;
            }
            set { }
        }

        public decimal Vales { get; set; }

        public int EstabelecimentoPossuiAlgumRegistroDeComissaoComSplit { get; set; }

        public decimal SplitsPagamento { get; set; }

        [XmlArrayItem("FechamentoMesValeModel")]
        public List<FechamentoMesLancamentoDTO> ValesLista { get; set; }

        public decimal ValeTransporte { get; set; }

        public decimal ValorAReceber
        {
            get { return TotalRecebimentos - TotalDescontos; }
            set { }
        }

        private static string ObterTextoTotalUnidade(List<FechamentoMesComissaoDeProdutoDTO> lista)
        {
            if (lista == null)
                return "";

            var todosOsProdutosEstaoNaMesmaQuantidade = lista.Select(p => p.TipoDeQuantidade).Distinct().Count() > 1 ? false : true;
            var todosOsProdutosEstaoNaMesmaUnidadeDeMedida = lista.Select(p => p.UnidadeMedida.Id).Distinct().Count() > 1 ? false : true;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            if (estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo)
            {
                if (todosOsProdutosEstaoNaMesmaQuantidade && todosOsProdutosEstaoNaMesmaUnidadeDeMedida)
                {
                    var simboloUnidadeMedida = lista.Select(p => p.UnidadeMedida.Simbolo).FirstOrDefault();
                    var medidasPorUnidade = lista.Select(p => p.MedidasPorUnidade).FirstOrDefault();
                    var tipoQuantidade = lista.Select(p => p.TipoDeQuantidade).FirstOrDefault();
                    var quantidadeTotal = lista.Sum(p => p.Quantidade);
                    return Domain.Pessoas.EstabelecimentoProdutoService.ObterTextoDeQuantidadeComUnidadeDeMedida(quantidadeTotal, simboloUnidadeMedida, medidasPorUnidade, false, adicionaTotalEmFracaoEntreParenteses: true);
                }
            }
            else
            {
                return lista.Sum(p => p.Quantidade).ToString();
            }

            return string.Empty;
        }

        private static string ObterTextoTotalUnidade(List<FechamentoMesCompraProdutoDTO> lista)
        {
            if (lista == null)
                return "";

            var todosOsProdutosEstaoNaMesmaQuantidade = lista.Select(p => p.TipoDeQuantidade).Distinct().Count() > 1 ? false : true;
            var todosOsProdutosEstaoNaMesmaUnidadeDeMedida = lista.Select(p => p.UnidadeMedida.Id).Distinct().Count() > 1 ? false : true;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            if (estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo)
            {
                if (todosOsProdutosEstaoNaMesmaQuantidade && todosOsProdutosEstaoNaMesmaUnidadeDeMedida)
                {
                    var simboloUnidadeMedida = lista.Select(p => p.UnidadeMedida.Simbolo).FirstOrDefault();
                    var medidasPorUnidade = lista.Select(p => p.MedidasPorUnidade).FirstOrDefault();
                    var tipoQuantidade = lista.Select(p => p.TipoDeQuantidade).FirstOrDefault();
                    var quantidadeTotal = lista.Sum(p => p.Quantidade);
                    return Domain.Pessoas.EstabelecimentoProdutoService.ObterTextoDeQuantidadeComUnidadeDeMedida(quantidadeTotal, simboloUnidadeMedida, medidasPorUnidade, false, adicionaTotalEmFracaoEntreParenteses: true);
                }
            }
            else
            {
                return lista.Sum(p => p.Quantidade).ToString();
            }

            return string.Empty;
        }
    }

    [Serializable]
    public class FechamentoMesLancamentoDTO
    {
        public FechamentoMesLancamentoDTO()
        {
        }

        public FechamentoMesLancamentoDTO(Lancamento obj)
        {
            Data = obj.DataPagamento ?? new DateTime();
            Valor = obj.Valor;
            Descricao = string.IsNullOrWhiteSpace(obj?.Descricao) ? "-" : obj.Descricao;
            TipoNome = string.IsNullOrWhiteSpace(obj?.LancamentoCategoria?.Nome) ? "-" : obj.LancamentoCategoria.Nome;
        }

        public DateTime Data { get; set; }
        public String Descricao { get; set; }
        public decimal Valor { get; set; }
        public string TipoNome { get; set; }
    }
}