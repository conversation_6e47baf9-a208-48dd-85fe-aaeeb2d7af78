﻿using System;

namespace Perlink.Trinks.Financeiro.DTO
{
    public class PreencherItensPendentesDTO
    {
        public PreencherItensPendentesDTO(bool temConfiguracaoServicosAindaPendentesHabilitada, DateTime data, 
            int idEstabelecimentoProfissional, int idPessoaComprador, int idEstabelecimento)
        {
            TemConfiguracaoServicosAindaPendentesHabilitada = temConfiguracaoServicosAindaPendentesHabilitada;
            Data = data;
            IdEstabelecimentoProfissional = idEstabelecimentoProfissional;
            IdPessoaComprador = idPessoaComprador;
            IdEstabelecimento = idEstabelecimento;
        }

        public bool TemConfiguracaoServicosAindaPendentesHabilitada { get; set; } 
        public DateTime Data { get; set; } 
        public int IdEstabelecimentoProfissional { get; set; }
        public int IdPessoaComprador { get; set; }
        public int IdEstabelecimento { get; set; }
    }
}
