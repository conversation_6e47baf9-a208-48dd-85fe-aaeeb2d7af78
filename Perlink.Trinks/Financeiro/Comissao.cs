﻿using Castle.ActiveRecord;
using Perlink.Trinks.Cashback;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro
{

    [ActiveRecord("Comissao", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class Comissao : ActiveRecordBase<Comissao>
    {

        //[Obsolete("Utilizar o Service para gerar")]
        public Comissao()
        {
            ValoresAReceber = new List<ValorDeComissaoAReceber>();
        }

        //[Obsolete("Utilizar o Service para gerar")]
        //public Comissao(int idPessoaEstabelecimento) : this() {
        //    IdPessoaEstabelecimento = idPessoaEstabelecimento;
        //}

        [PrimaryKey(PrimaryKeyType.Native, "id_comissao")]
        public virtual int Id { get; set; }

        [BelongsTo("id_transacao", Lazy = FetchWhen.OnInvoke)]
        public virtual Transacao Transacao { get; set; }

        [BelongsTo("id_pessoa_comissionada", Lazy = FetchWhen.OnInvoke)]
        public virtual PessoaFisica PessoaComissionada { get; set; }

        [Property("valor_comissao")]
        public virtual Decimal ValorComissao { get; set; }

        [Property("valor_base")]
        public virtual Decimal ValorBase { get; set; }

        [BelongsTo("id_comissao_tipo")]
        public virtual TipoComissao TipoComissao { get; set; }

        [Property("valor_comissao_a_pagar")]
        public virtual Decimal? ComissaoParaPagar { get; set; }

        [Property("comissao_esta_alterada_manualmente")]
        public virtual bool ComissaoAlteradaManualmente { get; set; }

        [Property("valor_comissao_original")]
        public virtual Decimal? PercentualDeComissaoOriginal { get; set; }

        [Property("valor_comissao_a_pagar_original")]
        public virtual Decimal? ValorComissaoParaPagarOriginal { get; set; }

        [Property("dt_hora_ultima_alteracao_manual_comissao")]
        public virtual DateTime? DataHoraUltimaAlteracaoManualComissao { get; set; }

        [BelongsTo("id_pessoa_quem_fez_ultima_alteracao_manual_comissao", Lazy = FetchWhen.OnInvoke)]
        public virtual PessoaFisica ResponsavelPelaUltimaAlteracaoDeComissao { get; set; }

        [Property("desconto_operadora")]
        public virtual Decimal DescontoOperadora { get; set; }

        [Property("desconto_extra")]
        public virtual Decimal DescontoExtra { get; set; }

        [Property("desconto_extra_no_valor_base")]
        public virtual bool DescontoExtraNoValorBase { get; set; }

        [Property("valor_bruto")]
        public virtual decimal ValorBruto { get; set; }

        [Property("ind_consumo_pacote")]
        public virtual bool ConsumoDePacote { get; set; }

        [Property("desconto_cliente")]
        public virtual decimal DescontoCliente { get; set; }

        [Property("tipo_origem_comissao")]
        public virtual TipoOrigemComissaoEnum TipoOrigemComissao { get; set; }

        [Property("id_pessoa_estabelecimento", NotNull = true)]
        public virtual int IdPessoaEstabelecimento { get; set; }

        [Property("eh_comissao_split")]
        public virtual bool EhComissaoComSplit { get; set; }

        [HasMany(typeof(ValorDeComissaoAReceber), ColumnKey = "id_comissao",
            Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO", Table = "Comissao_Valor_A_Receber",
            Inverse = true, Lazy = true)]
        public virtual IList<ValorDeComissaoAReceber> ValoresAReceber { get; set; }

        public virtual IList<ValorDeComissaoAReceber> ValoresAReceberAtivos()
        {
            return ValoresAReceber.Where(v => v.Ativo).ToList();
        }

        #region Cashback
        private CashbackComissao _cashback = null;
        public virtual void AtribuirCashbackSemPersistencia(CashbackComissao cashback)
        {
            _cashback = cashback;
        }

        public virtual bool PossuiCashbackNaoPersistido()
        {
            return _cashback != null;
        }

        public virtual CashbackComissao ObterCashbackNaoPersistido()
        {
            return _cashback;
        }
        #endregion

        public virtual void CalcularValoresAReceber()
        {
            bool existeAlgumValorQueEstahEmAlgumFechamentoMensal = PossuiValorEmUmFechamentoMensal();

            if (existeAlgumValorQueEstahEmAlgumFechamentoMensal)
                return;

            bool recebeComissaoNaDataPrevistaDeRecebimento = Domain.Financeiro.TransacaoService.PessoaRecebeComissaoNaDataPrevistaDeRecebimento(PessoaComissionada, Transacao.PessoaQueRecebeu.PessoaJuridica.Estabelecimento);
            bool valoresJaForamCalculados = ValoresAReceberAtivos().Count > 0;
            bool existeValorPorDataDeRecebimento = ValoresAReceberAtivos().Any(v => v.TransacaoFormaPagamentoParcela != null);
            bool houveTrocaNaConfiguracaoDeDataDeLiberacao = (recebeComissaoNaDataPrevistaDeRecebimento && !existeValorPorDataDeRecebimento)
                                                             || (!recebeComissaoNaDataPrevistaDeRecebimento && existeValorPorDataDeRecebimento);
            if (recebeComissaoNaDataPrevistaDeRecebimento
                && !ConsumoDePacote
                && Transacao.FormasPagamento.Count > 0)
            {
                if (houveTrocaNaConfiguracaoDeDataDeLiberacao)
                    DesativarValoresAReceber();
                foreach (var formaDePagamento in Transacao.FormasPagamento)
                {
                    foreach (var parcela in formaDePagamento.Parcelas.Where(p => p.Valor > 0))
                    {
                        var ehFormaPagamentoPOSComSplitHabilitado = formaDePagamento.FormaPagamento.TipoPOS != null && formaDePagamento.FormaPagamento.TipoPOS.HabilitaSplit;
                        var valorAReceber = GerarValorAReceberDaParcela(houveTrocaNaConfiguracaoDeDataDeLiberacao, parcela, ehFormaPagamentoPOSComSplitHabilitado);

                        if (valorAReceber.Id == 0)
                            ValoresAReceber.Add(valorAReceber);
                    }
                }
                AcertarUltimoValorAReceberCasoPercentualQuebreCentavos();
            }
            else
            {
                if (existeValorPorDataDeRecebimento)
                    DesativarValoresAReceber();

                var valorAReceber = GerarValorAReceberSemParcela();

                if (valorAReceber.Id == 0)
                    ValoresAReceber.Add(valorAReceber);
            }
        }

        private ValorDeComissaoAReceber GerarValorAReceberSemParcela()
        {
            var valorAReceber = ValoresAReceberAtivos().FirstOrDefault() ?? new ValorDeComissaoAReceber(IdPessoaEstabelecimento);
            valorAReceber.Comissao = this;
            valorAReceber.TransacaoFormaPagamentoParcela = null;

            if (valorAReceber.DataDaComissaoAReceberAlteradaManualmente)
                valorAReceber.DataDaComissaoAReceberOriginal = Transacao.DataHora.Date;
            else
                valorAReceber.DataDaComissaoAReceber = Transacao.DataHora.Date;

            if (EhComissaoComSplit)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(IdPessoaEstabelecimento);
                var configuracoesPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

                valorAReceber.DataDaComissaoAReceber = valorAReceber.DataDaComissaoAReceber.AddDays(configuracoesPOS.TipoPOS.NumeroMinimoDiasRecebimento);

                if (configuracoesPOS != null && configuracoesPOS.TipoPOS.Id == (int)SubadquirenteEnum.Stone && configuracoesPOS.TipoPOS.DiasRecebimentoOperadora.HasValue)
                    valorAReceber.DataDaComissaoAReceber = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametroPorSubadquirente(Calendario.Agora().AddDays(1));

                if (valorAReceber.DataDaComissaoAReceberOriginal.HasValue)
                {
                    valorAReceber.DataDaComissaoAReceberOriginal = valorAReceber.DataDaComissaoAReceberOriginal.Value.AddDays(configuracoesPOS.TipoPOS.NumeroMinimoDiasRecebimento);

                    if (configuracoesPOS != null && configuracoesPOS.TipoPOS.Id == (int)SubadquirenteEnum.Stone && configuracoesPOS.TipoPOS.DiasRecebimentoOperadora.HasValue)
                        valorAReceber.DataDaComissaoAReceberOriginal = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametroPorSubadquirente(Calendario.Agora().AddDays(1));
                }
            }

            valorAReceber.ValorOriginal = ValorComissaoParaPagarOriginal;
            valorAReceber.Valor = ComissaoParaPagar ?? 0;

            #region proporcionais

            valorAReceber.ValorBaseProporcional = ValorBase;
            valorAReceber.ValorBrutoProporcional = ValorBruto;
            valorAReceber.DescontoOperadoraProporcional = DescontoOperadora;
            valorAReceber.DescontoExtraProporcional = DescontoExtra;
            valorAReceber.DescontoClienteProporcional = DescontoCliente;

            #endregion proporcionais

            return valorAReceber;
        }

        private ValorDeComissaoAReceber GerarValorAReceberDaParcela(bool houveTrocaNaConfiguracaoDeDataDeLiberacao, TransacaoFormaPagamentoParcela parcela, bool ehFormaPagamentoPOSComSplitHabilitado)
        {
            ValorDeComissaoAReceber valorAReceber = ValoresAReceberAtivos().FirstOrDefault(v => v.TransacaoFormaPagamentoParcela.Id == parcela.Id && parcela.Id > 0)
                ?? new ValorDeComissaoAReceber(IdPessoaEstabelecimento);

            var totalPago = Transacao.FormasPagamento.Sum(f => f.ValorPago);
            var percentualValorParcela = totalPago > 0 ? parcela.Valor / totalPago : 0;
            var valorComissaoDaParcela = percentualValorParcela > 0 ? ((ComissaoParaPagar * percentualValorParcela) ?? 0) : 0;

            valorComissaoDaParcela = Math.Truncate(valorComissaoDaParcela * 100) / 100;

            valorAReceber.Comissao = this;
            valorAReceber.TransacaoFormaPagamentoParcela = parcela;

            if (valorAReceber.DataDaComissaoAReceberAlteradaManualmente)
                valorAReceber.DataDaComissaoAReceberOriginal = parcela.DataRecebimento.Date;
            else
                valorAReceber.DataDaComissaoAReceber = parcela.DataRecebimento.Date;

            if (EhComissaoComSplit && ehFormaPagamentoPOSComSplitHabilitado)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(IdPessoaEstabelecimento);
                var configuracoesPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

                var dataPrevistoAhReceberComSplit = Transacao.DataHora.Date.AddDays(configuracoesPOS.TipoPOS.NumeroMinimoDiasRecebimento);

                if (configuracoesPOS != null && configuracoesPOS.TipoPOS.Id == (int)SubadquirenteEnum.Stone && configuracoesPOS.TipoPOS.DiasRecebimentoOperadora.HasValue)
                    dataPrevistoAhReceberComSplit = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametroPorSubadquirente(Calendario.Agora().AddDays(1));

                valorAReceber.DataDaComissaoAReceber = dataPrevistoAhReceberComSplit;
                if (valorAReceber.DataDaComissaoAReceberOriginal.HasValue)
                    valorAReceber.DataDaComissaoAReceberOriginal = dataPrevistoAhReceberComSplit;
            }

            if (valorAReceber.Comissao.ComissaoAlteradaManualmente && !houveTrocaNaConfiguracaoDeDataDeLiberacao)
                valorAReceber.ValorOriginal = valorComissaoDaParcela;
            else
                valorAReceber.Valor = valorComissaoDaParcela;

            #region proporcionais

            valorAReceber.ValorBaseProporcional = ValorBase * percentualValorParcela;
            valorAReceber.ValorBrutoProporcional = ValorBruto * percentualValorParcela;
            valorAReceber.DescontoOperadoraProporcional = DescontoOperadora * percentualValorParcela;
            valorAReceber.DescontoExtraProporcional = DescontoExtra * percentualValorParcela;
            valorAReceber.DescontoClienteProporcional = DescontoCliente * percentualValorParcela;

            #endregion proporcionais

            return valorAReceber;
        }

        private void AcertarUltimoValorAReceberCasoPercentualQuebreCentavos()
        {
            var ultimoValorAReceber = ValoresAReceberAtivos().Last();

            decimal diferencaDeCentavos = (ComissaoParaPagar ?? 0) - ValoresAReceberAtivos().Sum(v => v.Valor);
            ultimoValorAReceber.Valor = ultimoValorAReceber.Valor + diferencaDeCentavos;

            diferencaDeCentavos = DescontoCliente - ValoresAReceberAtivos().Sum(v => v.DescontoClienteProporcional);
            ultimoValorAReceber.DescontoClienteProporcional = ultimoValorAReceber.DescontoClienteProporcional + diferencaDeCentavos;

            diferencaDeCentavos = DescontoExtra - ValoresAReceberAtivos().Sum(v => v.DescontoExtraProporcional);
            ultimoValorAReceber.DescontoExtraProporcional = ultimoValorAReceber.DescontoExtraProporcional + diferencaDeCentavos;

            diferencaDeCentavos = ValorBruto - ValoresAReceberAtivos().Sum(v => v.ValorBrutoProporcional);
            ultimoValorAReceber.ValorBrutoProporcional = ultimoValorAReceber.ValorBrutoProporcional + diferencaDeCentavos;

            diferencaDeCentavos = ValorBase - ValoresAReceberAtivos().Sum(v => v.ValorBaseProporcional);
            ultimoValorAReceber.ValorBaseProporcional = ultimoValorAReceber.ValorBaseProporcional + diferencaDeCentavos;

            diferencaDeCentavos = DescontoOperadora - ValoresAReceberAtivos().Sum(v => v.DescontoOperadoraProporcional);
            ultimoValorAReceber.DescontoOperadoraProporcional = ultimoValorAReceber.DescontoOperadoraProporcional + diferencaDeCentavos;

            //var ultimoValorAReceberComDescontoOperadora = ValoresAReceberAtivos().LastOrDefault(v=> v.DescontoOperadoraProporcional != 0);
            //if (ultimoValorAReceberComDescontoOperadora != null) {
            //    diferencaDeCentavos = DescontoOperadora - ValoresAReceberAtivos().Sum(v => v.DescontoOperadoraProporcional);
            //    ultimoValorAReceberComDescontoOperadora.DescontoOperadoraProporcional = ultimoValorAReceberComDescontoOperadora.DescontoOperadoraProporcional + diferencaDeCentavos ?? 0;
            //}
        }

        private void DesativarValoresAReceber()
        {
            foreach (var v in ValoresAReceberAtivos())
            {
                v.Ativo = false;
            }
        }

        public virtual bool PossuiValorEmUmFechamentoMensal()
        {
            return ValoresAReceberAtivos().Any(v => v.FechamentoProfisisonal != null);
        }
    }
}