﻿using Castle.ActiveRecord;
using Perlink.Trinks.Cashback;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.RPS;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro
{

    [ActiveRecord("Transacao", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class Transacao : ActiveRecordBase<Transacao>
    {
        private ClienteEstabelecimento _clienteEstabelecimento;

        private bool? descontarProfissional;

        public Transacao()
        {
            HorariosTransacoes = new List<HorarioTransacao>();
            FormasPagamento = new List<TransacaoFormaPagamento>();
            TransacaoItens = new List<TransacaoItem>();
            DataHora = Calendario.Agora();
            DataReferencia = Calendario.Hoje();
            Ativo = true;
            TipoTransacao = new TipoTransacao((int)TipoTransacaoEnum.Pagamento);
            Gorjetas = new List<Gorjeta>();
        }

        public Transacao(int idTransacao)
        {
            Id = idTransacao;
            Refresh();
        }

        [Property("ativo", ColumnType = "Boolean")]
        public virtual bool Ativo { get; set; }

        [BelongsTo("id_comanda", Lazy = FetchWhen.OnInvoke)]
        public virtual Comanda Comanda { get; set; }

        [Property("comentario_estorno", ColumnType = "String")]
        public virtual String ComentarioEstorno { get; set; }

        [Property("comentario_fechamento_conta", ColumnType = "String")]
        public virtual String ComentarioFechamentoConta { get; set; }

        public virtual DadosRPSTransacao DadosRPS(PessoaJuridica pessoaJuridica)
        {
            return Domain.RPS.DadosRPSTransacaoRepository.ObterPorIdTransacaoEIdPessoaJuridica(Id, pessoaJuridica.IdPessoa);
        }

        [Property("data_hora_transacao", ColumnType = "DateTime", NotNull = false)]
        public virtual DateTime DataHora { get; set; }

        [Property("data_horarios_que_essa_transacao_paga", ColumnType = "DateTime")]
        public virtual DateTime DataReferencia { get; set; }

        [Property("venda_para_profissional")]
        public virtual bool FoiVendaParaProfissional { get; set; }

        public virtual Boolean GetDescontarProfissional()
        {
            if (descontarProfissional != null) return descontarProfissional.Value;
            if (Id == 0) return false;

            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(f => f.Transacao.Id == Id && f.FormaPagamento.Id == (int)FormaPagamentoEnum.DescontoDeProfissional);
        }

        public virtual void SetDescontarProfissional(bool value)
        {
            descontarProfissional = value;
        }

        [Property("total_descontos", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? Descontos { get; set; }

        [Property("total_descontos_pacotes")]
        public virtual decimal? DescontosPacotes { get; set; }

        [Property("total_descontos_produtos")]
        public virtual decimal? DescontosProdutos { get; set; }

        [Property("total_descontos_servicos")]
        public virtual decimal? DescontosServicos { get; set; }

        [Property("total_descontos_vale_presente")]
        public virtual decimal? DescontosValePresente { get; set; }

        [HasMany(typeof(TransacaoFormaPagamento), ColumnKey = "id_transacao",
            Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO", Table = "Transacao__Forma_Pagamento",
            Inverse = true, Lazy = true)]
        public virtual IList<TransacaoFormaPagamento> FormasPagamento { get; set; }

        [HasMany(typeof(HorarioTransacao), ColumnKey = "id_transacao",
            Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO", Table = "Horario_Transacao",
            Inverse = true, Lazy = true)]
        public virtual IList<HorarioTransacao> HorariosTransacoes { get; set; }

        [HasMany(ColumnKey = "id_transacao", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true, Lazy = true)]
        public virtual IList<TransacaoItem> TransacaoItens { get; set; }

        [PrimaryKey(PrimaryKeyType.Native, "id_transacao", ColumnType = "Int32")]
        public virtual Int32 Id { get; set; }

        [Property("numero_pre_venda")]
        public virtual int? NumeroDaPreVenda { get; set; }

        public virtual Boolean PagamentoJaEstornado
        {
            get { return IdTransacaoQueEstounouEsta.HasValue; }
            set { }
        }

        [Property("percentual_medio_operadoras")]
        public virtual decimal PercentualMedioDescontoOperadoras { get; set; }

        [BelongsTo("id_pessoa_pagou", Lazy = FetchWhen.OnInvoke)]
        public virtual PessoaFisica PessoaQuePagou { get; set; }

        [BelongsTo("id_pessoa_que_realizou", Lazy = FetchWhen.OnInvoke, NotNull = true)]
        public virtual PessoaFisica PessoaQueRealizou { get; set; }

        [BelongsTo("id_pessoa_recebeu", Lazy = FetchWhen.OnInvoke, NotNull = true)]
        public virtual PessoaJuridica PessoaQueRecebeu { get; set; }

        public virtual StatusRpsEnum StatusRPS(PessoaJuridica pessoaJuridica)
        {
            var dadosRPS = DadosRPS(pessoaJuridica);
            return dadosRPS == null ? StatusRpsEnum.NaoEmitido : dadosRPS.StatusRPS;
        }

        [BelongsTo("id_transacao_tipo", Lazy = FetchWhen.OnInvoke, NotNull = true)]
        public virtual TipoTransacao TipoTransacao { get; set; }

        [Property("sub_total_credito_cliente")]
        public virtual decimal TotalCreditoCliente { get; set; }

        [Property("total_custo_descartaveis")]
        public virtual Decimal? TotalDescartaveis { get; set; }

        [Property("total_desconto_operadoras")]
        public virtual decimal TotalDescontoOperadoras { get; set; }

        [Property("sub_total_pacotes")]
        public virtual decimal? TotalPacotes { get; set; }

        [Property("sub_total_clube_de_assinaturas")]
        public virtual decimal? TotalClubeDeAssinaturas { get; set; }

        [Property("total_a_pagar", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? TotalPagar { get; set; }

        [Property("total_pago", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? TotalPago { get; set; }

        [Property("total_pago_credito")]
        public virtual Decimal? TotalPagoEmCredito { get; set; }

        [Property("total_pago_debito")]
        public virtual Decimal? TotalPagoEmDebito { get; set; }

        [Property("total_pago_dinheiro")]
        public virtual Decimal? TotalPagoEmDinheiro { get; set; }

        [Property("total_pago_outros")]
        public virtual Decimal? TotalPagoEmOutros { get; set; }

        [Property("total_pago_prepago")]
        public virtual decimal TotalPagoEmPrePago { get; set; }

        [Property("sub_total_produtos")]
        public virtual Decimal? TotalProdutos { get; set; }

        [Property("sub_total_servicos", NotNull = false)]
        public virtual Decimal? TotalServicos { get; set; }

        [Property("sub_total_vale_presente")]
        public virtual decimal TotalValePresente { get; set; }

        [BelongsTo("id_transacao_que_estornou_esta", Lazy = FetchWhen.OnInvoke, NotNull = false)]
        public virtual Transacao TransacaoQueEstounouEsta { get; set; }

        [Property("id_transacao_que_estornou_esta", Update = false, Insert = false)]
        public virtual int? IdTransacaoQueEstounouEsta { get; set; }

        [Property("troco", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? Troco { get; set; }

        [HasMany(typeof(Gorjeta), ColumnKey = "id_transacao", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true, Lazy = true)]
        public virtual IList<Gorjeta> Gorjetas { get; set; }

        [HasMany(typeof(Venda), ColumnKey = "id_transacao", Inverse = true, Lazy = true)]
        public virtual IList<Venda> Vendas { get; set; }

        [Property("saldo_pontos_fidelidade_apos_transacao")]
        public virtual int SaldoDePontosDeFidelidadeDoClienteAposEfetuarTransacao { get; set; }

        [BelongsTo(Column = "id_transacao", Fetch = FetchEnum.Join, Cascade = CascadeEnum.None, Lazy = FetchWhen.OnInvoke, Update = false, NotFoundBehaviour = NotFoundBehaviour.Ignore)]
        public virtual BonusTransacao BonusTransacao { get; set; }

        #region Cashback
        private CashbackTransacao _cashback = null;
        public virtual void AtribuirCashbackSemPersistencia(CashbackTransacao cashback)
        {
            _cashback = cashback;
        }

        public virtual bool PossuiCashbackNaoPersistido()
        {
            return _cashback != null;
        }

        public virtual CashbackTransacao ObterCashbackNaoPersistido()
        {
            return _cashback;
        }
        #endregion

        public virtual ClienteEstabelecimento ClienteEstabelecimento()
        {
            if (_clienteEstabelecimento == null && PessoaQuePagou != null)
            {
                var estabelecimento =
                    Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                        PessoaQueRecebeu.PessoaJuridica.IdPessoaJuridica);

                _clienteEstabelecimento =
                    Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(
                        PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);
            }

            return _clienteEstabelecimento;
        }

        public virtual HorarioHistorico ObterHorarioHistorico()
        {
            var historicos = HorariosTransacoes.First().Horario.Historicos.Where(f => f.DataHoraAlteracao <= DataHora).ToList();

            if (!historicos.Any())
                return null;
            var dataUltimoHistorico = historicos.Max(f => f.DataHoraAlteracao);
            return historicos.First(f => f.DataHoraAlteracao == dataUltimoHistorico);
        }

        public virtual void RecalcularTotaisDeOperadoras()
        {
            this.TotalDescontoOperadoras = this.FormasPagamento.Sum(f => f.ValorDescontoOperadora());

            if (this.TotalPagar.HasValue && this.TotalPagar.Value != 0)
            {
                this.PercentualMedioDescontoOperadoras = this.TotalDescontoOperadoras / this.TotalPagar.Value * 100;
            }
            else
                this.PercentualMedioDescontoOperadoras = 0;
        }

        public virtual bool ProgramaDeFidelidadeParticipouDaTransacao(Venda venda)
        {
            return ObterItensQueMovimentaramPontosDeFidelidade(venda).Count > 0;
        }

        private List<IMovimentaPontosDeFidelidade> ObterItensQueMovimentaramPontosDeFidelidade(Venda venda)
        {
            List<IMovimentaPontosDeFidelidade> itens = new List<IMovimentaPontosDeFidelidade>();
            itens.AddRange(HorariosTransacoes);
            itens.AddRange(venda.ItensVenda);
            var agendamentosOnlineQueDeramPontos = Domain.Fidelidade.AgendamentoOnlineQueGerouPontosRepository.ListarPorTransacao(Id);
            itens.AddRange(agendamentosOnlineQueDeramPontos);

            return itens.Where(i => i.PontosDeFidelidade != 0).ToList();
        }

        public virtual int PontosDeFidelidadeGanhos(Venda venda)
        {
            return ObterItensQueMovimentaramPontosDeFidelidade(venda).Where(i => i.PontosDeFidelidade > 0).Sum(i => i.PontosDeFidelidade);
        }

        public virtual int PontosDeFidelidadeUsados(Venda venda)
        {
            return ObterItensQueMovimentaramPontosDeFidelidade(venda).Where(i => i.PontosDeFidelidade < 0).Sum(i => i.PontosDeFidelidade);
        }

        public virtual HorarioTransacao ObterPrimeiroHorarioTransacaoAgendadoOnline()
        {
            return HorariosTransacoes.FirstOrDefault(ht => !ht.FoiAgendadoNoBalcao());
        }

        public virtual string ObterFormasDePagamentoTexto()
        {
            var formasDePagamento = "";
            var contadorDoFimDaLista = 0;
            foreach (var formaPagamento in FormasPagamento)
            {
                var nomeFormaDePagamento = formaPagamento.FormaPagamento.Nome;
                var tipoFormaDePagamento = formaPagamento.FormaPagamento.Tipo.Nome;
                var parcelas = formaPagamento.NumeroParcelas;
                contadorDoFimDaLista++;
                formasDePagamento += nomeFormaDePagamento + " - " + tipoFormaDePagamento;
                if (formaPagamento.NumeroParcelas > 1)
                {
                    formasDePagamento += String.Format(" {0}x", formaPagamento.NumeroParcelas);
                }
                if (FormasPagamento.Count > 1 && contadorDoFimDaLista < FormasPagamento.Count)
                    formasDePagamento += " / ";
            }

            return formasDePagamento;
        }

        public virtual List<TransacaoFormaPagamento> ListarLancamentosDeDividas()
        {
            return FormasPagamento.Where(tfp => tfp.FormaPagamento == FormaPagamentoEnum.DeixarFaltaComoDivida && tfp.ValorPago > 0).ToList();
        }

        public virtual List<TransacaoFormaPagamento> ListarPagamentosDeDividas()
        {
            return FormasPagamento.Where(tfp => tfp.FormaPagamento == FormaPagamentoEnum.DeixarFaltaComoDivida && tfp.ValorPago < 0).ToList();
        }

        public virtual decimal ObterValorTotalPagoEmDividas()
        {
            return ListarPagamentosDeDividas().Sum(tfp => tfp.ValorPago);
        }

        public virtual decimal ObterValorDeixadoParaPagarDepois()
        {
            return ListarLancamentosDeDividas().Sum(tfp => tfp.ValorPago);
        }

        public virtual decimal ObterValorRestanteAPagar()
        {
            var totalAPagar = TotalPagar ?? 0;
            var totalPago = TotalPago ?? 0;

            if (totalPago >= totalAPagar)
                return 0;

            return totalAPagar - totalPago;
        }

        public virtual bool TeveLancamentoDeDivida()
        {
            return ListarLancamentosDeDividas().Any();
        }

        public virtual bool TevePagamentoDeDivida()
        {
            return ListarPagamentosDeDividas().Any();
        }

        public virtual bool TemClienteIdentificado()
        {
            return PessoaQuePagou != null;
        }

        public virtual void RecalcularTroco()
        {
            if (GetDescontarProfissional())
            {
                Troco = 0;
            }
            else
            {
                var quantoPagou = TotalPago ?? 0;
                var quantoDeveriaPagar = TotalPagar ?? 0;
                var gorjeta = Gorjetas != null && Gorjetas.Any(g => g.Ativo) ? Gorjetas.Where(g => g.Ativo).Sum(g => g.Valor) : 0;

                Troco = (quantoPagou - quantoDeveriaPagar - gorjeta) * -1;
            }
        }

        public virtual bool PossuiCompraDeCreditoCliente()
        {
            return TotalCreditoCliente > 0;
        }

        public virtual void AdicionarTransacaoFormaPagamento(FormaPagamento formaPagamento, int numeroDeParcelas, decimal valorPago, TaxasDTO taxas)
        {
            var transacaoFormaPagamento = new TransacaoFormaPagamento()
            {
                FormaPagamento = formaPagamento,
                Transacao = this,
                NumeroParcelas = numeroDeParcelas,
                PessoaQuePagou = PessoaQuePagou,
                ValorPago = valorPago,
                PercentualCobradoPelaOperadora = taxas.PercentualCobradoPelaOperadora,
                ValorFixoCobradoPelaOperadora = taxas.ValorFixoCobradoPelaOperadora,
                DiasParaReceberDaOperadora = formaPagamento.DiasParaReceberDaOperadora,
            };

            var valorRestante = valorPago;

            for (int i = 0; i < numeroDeParcelas; i++)
            {
                decimal valorParcelaAtual;

                valorParcelaAtual = Decimal.Round(valorRestante / (numeroDeParcelas - i), 2);
                valorRestante -= valorParcelaAtual;

                transacaoFormaPagamento.AdicionarParcela(i + 1, valorParcelaAtual, DataReferencia.AddMonths(i), DataReferencia.AddMonths(i).AddDays(formaPagamento.DiasParaReceberDaOperadora));
            }

            FormasPagamento.Add(transacaoFormaPagamento);
        }

        public virtual void AdicionarTransacaoItem(MotivoDesconto motivoDesconto, decimal? valorDesconto, string nome, string tipo, int idObjetoReferencia)
        {
            var valorPago = TotalPago ?? 0;
            TransacaoItens.Add(new TransacaoItem(this, motivoDesconto, valorPago, valorDesconto, nome, tipo, idObjetoReferencia));
        }
        
        public virtual void AdicionarTransacaoItem(string nome, string tipo, int idObjetoReferencia)
        {
            var valorPago = TotalPago ?? 0;
            TransacaoItens.Add(new TransacaoItem(this, motivoDesconto: null, valorPago, valorDesconto: null, nome, tipo, idObjetoReferencia));
        }
    }
}