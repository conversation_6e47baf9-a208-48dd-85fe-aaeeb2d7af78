﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Vendas;
using System.Linq;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Financeiro.Services
{
    public class PagamentoComissaoComDescontosService : BaseService, IPagamentoComissaoComDescontosService
    {

        public void PagarComissoesComDescontos(FiltroBaixaComissaoComDescontoDTO dto)
        {
            PagarComissao(dto);
            PagarBonificacao(dto);
            PagarVale(dto);
            PagarSplit(dto);
            PagarVendaDeProdutos(dto);
        }

        private static void PagarBonificacao(FiltroBaixaComissaoComDescontoDTO dto)
        {
            var bonificacoesAPagar = Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesAbertas(dto.IdEstabelecimento, dto.DataPeriodoInicio, dto.DataPeriodoFim, dto.IdPessoaDoProfissionalEstabelecimento);

            decimal totalBonificacoes = 0;

            foreach (var bonificacao in bonificacoesAPagar)
            {
                totalBonificacoes += bonificacao.Valor;

                var folhaPagamentoItemBonificacao = new FolhaPagamentoItemBonificacao(dto.FolhaMesProfissional, bonificacao);
                var folhaPagamentoLancamento = new FolhaPagamentoLancamento(dto.FolhaMesProfissional, bonificacao.IdLancamento, dto.PessoaLogada.IdPessoaFisica);

                Domain.Financeiro.FolhaPagamentoItemBonificacaoRepository.SaveNewNoFlush(folhaPagamentoItemBonificacao);
                Domain.Financeiro.FolhaPagamentoLancamentoRepository.SaveNewNoFlush(folhaPagamentoLancamento);
            }

            dto.FolhaMesProfissional.Bonificacoes = totalBonificacoes;

            Domain.Despesas.LancamentoRepository.Flush();
            Domain.Financeiro.FolhaPagamentoItemBonificacaoRepository.Flush();
        }

        private static void PagarVale(FiltroBaixaComissaoComDescontoDTO dto)
        {
            var valesAPagar = Domain.Despesas.LancamentoRepository
                .ObterQueryValesAbertos(dto.IdEstabelecimento, dto.DataPeriodoInicio, dto.DataPeriodoFim, dto.IdPessoaDoProfissionalEstabelecimento);

            decimal totalVales = 0;

            foreach (var vale in valesAPagar)
            {
                totalVales += vale.Valor;

                var folhaPagamentoItemVale = new FolhaPagamentoItemVale(dto.FolhaMesProfissional, vale);
                var folhaPagamentoLancamento = new FolhaPagamentoLancamento(dto.FolhaMesProfissional, vale.IdLancamento, dto.PessoaLogada.IdPessoaFisica);

                Domain.Financeiro.FolhaPagamentoItemValeRepository.SaveNewNoFlush(folhaPagamentoItemVale);
                Domain.Financeiro.FolhaPagamentoLancamentoRepository.SaveNewNoFlush(folhaPagamentoLancamento);
            }

            dto.FolhaMesProfissional.Vales = totalVales;

            Domain.Despesas.LancamentoRepository.Flush();
            Domain.Financeiro.FolhaPagamentoItemValeRepository.Flush();
        }

        private static void PagarSplit(FiltroBaixaComissaoComDescontoDTO dto)
        {
            var splitsAPagar = Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsAbertas(dto.IdEstabelecimento, dto.DataPeriodoInicio, dto.DataPeriodoFim,
                    dto.IdPessoaDoProfissionalEstabelecimento);

            decimal totalSplits = 0;

            foreach (var split in splitsAPagar)
            {
                totalSplits += split.Valor;

                var folhaPagamentoItemSplit = new FolhaPagamentoItemSplit(dto.FolhaMesProfissional, split);
                var folhaPagamentoLancamento = new FolhaPagamentoLancamento(dto.FolhaMesProfissional, split.IdLancamento, dto.PessoaLogada.IdPessoaFisica);

                Domain.Financeiro.FolhaPagamentoItemSplitRepository.SaveNewNoFlush(folhaPagamentoItemSplit);
                Domain.Financeiro.FolhaPagamentoLancamentoRepository.SaveNewNoFlush(folhaPagamentoLancamento);
            }

            dto.FolhaMesProfissional.SplitsPagamento = totalSplits;

            Domain.Financeiro.FolhaPagamentoItemSplitRepository.Flush();
        }

        private static void PagarVendaDeProdutos(FiltroBaixaComissaoComDescontoDTO dto)
        {
            var parcelasAPagar = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository
                .ObterQueryVendaDeProdutosAbertas(dto.IdEstabelecimento, dto.DataPeriodoInicio, dto.DataPeriodoFim, dto.IdPessoaEstabelecimento, dto.FolhaMesProfissional.EstabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa).ToList();

            decimal totalVendaDeProdutos = 0;

            foreach (var parcela in parcelasAPagar)
            {
                totalVendaDeProdutos += parcela.Valor;

                var folhaPagamentoItemVendaDeProduto = new FolhaPagamentoCompraProduto(dto.FolhaMesProfissional, parcela);
                Domain.Financeiro.FolhaPagamentoCompraProdutoRepository.SaveNewNoFlush(folhaPagamentoItemVendaDeProduto);
            }

            dto.FolhaMesProfissional.CompraProduto = totalVendaDeProdutos;

            Domain.Financeiro.FolhaPagamentoCompraProdutoRepository.Flush();
        }

        private static void PagarComissao(FiltroBaixaComissaoComDescontoDTO dto)
        {
            var comissoesAPagar = Domain.Financeiro.ValorDeComissaoAReceberRepository
                .ObterQueryComissoesAbertas(dto.IdPessoaEstabelecimento, dto.DataPeriodoInicio, dto.DataPeriodoFim, dto.IdPessoaDoProfissionalEstabelecimento)
                .ToList();

            dto.FolhaMesProfissional.ComissaoProdutos = 0;
            dto.FolhaMesProfissional.ComissaoServicos = 0;
            dto.FolhaMesProfissional.ComissaoPacotes = 0;

            foreach (var comissao in comissoesAPagar)
            {
                AtualizarComissao(dto.FolhaMesProfissional, comissao.Comissao.TipoOrigemComissao, comissao.Valor);

                comissao.FechamentoProfisisonal = dto.FolhaMesProfissional;

                Domain.Financeiro.ValorDeComissaoAReceberRepository.UpdateNoFlush(comissao);
            }

            Domain.Financeiro.ValorDeComissaoAReceberRepository.Flush();
        }

        private static void AtualizarComissao(FechamentoFolhaMesProfissional folhaProfissional, TipoOrigemComissaoEnum tipo, decimal valor)
        {
            switch (tipo)
            {
                case TipoOrigemComissaoEnum.Produto:
                    folhaProfissional.ComissaoProdutos += valor;
                    break;
                case TipoOrigemComissaoEnum.Servico:
                    folhaProfissional.ComissaoServicos += valor;
                    break;
                case TipoOrigemComissaoEnum.Pacote:
                    folhaProfissional.ComissaoPacotes += valor;
                    break;
            }
        }
    }
}
