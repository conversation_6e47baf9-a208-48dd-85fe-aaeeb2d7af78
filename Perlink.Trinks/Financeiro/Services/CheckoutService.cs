﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.IntegracaoSplit.Models;
using Perlink.Shared.Auditing;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.DTO.Checkout;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Resources;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.Financeiro.Services
{

    public class CheckoutService : BaseService, ICheckoutService
    {
        //TODO: Testar consumo de pontos de fidelidade
        //TODO: Testar ganho de pontos de fidelidade
        //TODO: Testar emissão de NFCe
        //TODO: Testar pagamento POS
        //TODO: Testar Split
        //TODO: Testar Consumo de pacote comprado anteriormente
        //TODO: Testar Consumo de pacote comprado no próprio fechamento
        //TODO: Testar valor quitado
        //TODO: Testar pré-venda
        //TODO: Testar comanda

        public List<ItemCheckoutDetalheServicoDTO> HorariosParaPagamento(DateTime data, int? idClienteEstabelecimento = null)
        {
            var idEstabelecimento = Domain.WebContext.IdEstabelecimentoAutenticado.Value;

            var horarios = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento
                && f.DataInicio >= data.Date && f.DataInicio < data.Date.AddDays(1)
                && f.Profissional != null
                && !f.FoiPago
                && f.Status != StatusHorarioEnum.Cancelado);

            if (idClienteEstabelecimento.HasValue)
                horarios = horarios.Where(f => f.ClienteEstabelecimento.Codigo == idClienteEstabelecimento.Value);

            return horarios.Select(h => ToItemCheckoutServicoDTO(h)).ToList();
        }

        public List<MotivoDescontoCheckoutDTO> ObterMotivosDeDesconto()
        {
            return Domain.Financeiro.MotivoDescontoRepository
                .Listar(Domain.WebContext.IdEstabelecimentoAutenticado.Value, null, true)
                .Select(f => new MotivoDescontoCheckoutDTO
                {
                    Id = f.Id,
                    Ativo = f.Ativo,
                    DescontoRefleteNaComissao = f.DescontoRefleteNaComissao,
                    Descricao = f.Descricao,
                    MotivoDeDescontoDoTrinks = f.MotivoDeDescontoDoTrinks,
                    PermiteEdicao = f.PermiteEdicao()
                })
                .ToList();
        }

        [TransactionInitRequired]
        public async Task<CheckoutRealizadoDTO> RealizarCheckout(DadosParaCheckoutDTO dto)
        {
            var retorno = new CheckoutRealizadoDTO();
            var estaValido = ValidarDadosCheckoutDTO(dto);

            if (estaValido)
            {
                var idsHorariosIncluidosNaTransacao = dto.ItensServico.Where(h => h.IdHorario > 0).Select(h => h.IdHorario).ToList();
                var idsHorariosQueJaEstavamFinalizados = Domain.Pessoas.HorarioRepository.ListarIdsDosHorariosQueEstaoFinalizados(Domain.WebContext.IdEstabelecimentoAutenticado.Value, idsHorariosIncluidosNaTransacao);

                var venda = Domain.Vendas.VendaRepository.Factory.CreateVenda(dto);

                var pessoaLogada = Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value).Pessoa.PessoaFisica;

                var emitirNFCe = false;
                var idTransacaoPOS = dto.IdTransacaoPos;
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);
                var estabelecimentoConfiguracaoGeral = estabelecimento.EstabelecimentoConfiguracaoGeral;

                foreach (var ht in venda.Transacao.HorariosTransacoes)
                {
                    if (ht.Horario.Id == 0)
                        Domain.Pessoas.HorarioService.ManterHorario(ht.Horario);
                }

                var checkoutDTO = new Financeiro.DTO.CheckoutDTO(venda.Transacao, pessoaLogada)
                {
                    Venda = venda,
                    EmitirNFC = emitirNFCe,
                    IdTransacaoPOS = idTransacaoPOS,
                    JaTeveCaixaPorProfissionalAberto = estabelecimentoConfiguracaoGeral.JaTeveCaixaPorProfissionalAberto,
                    ManterStatusDosAgendamentos = dto.ManterStatusDosAgendamentos
                };

                var resultadoCheckOut = await Domain.Financeiro.TransacaoService.RealizarCheckOut(checkoutDTO);
                var transacao = resultadoCheckOut.Transacao;

                var nfcNota = transacao.Id > 0
                    ? Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(transacao)
                    : null;
                var numeroNFC = nfcNota != null ? nfcNota.NumeroNota : 0;

                var exibeNumeroDeFechamento = estabelecimentoConfiguracaoGeral.ExibeNumeroDeFechamento;
                int numeroDoFechamento = exibeNumeroDeFechamento ? (transacao.NumeroDaPreVenda ?? 0) : 0;

                var idsHorariosFinalizadosNaTransacao = transacao.HorariosTransacoes
                    .Where(ht => ht.Horario.Codigo.HasValue && !idsHorariosQueJaEstavamFinalizados.Contains(ht.Horario.Codigo.Value))
                    .Select(ht => ht.Horario.Codigo.Value)
                    .ToList();

                var produtosUsadosAoFinalizarServicos = Domain.EstoqueComBaixaAutomatica.FinalizacaoDoUsoDeProdutosStory
                        .ObterDetalhesDosProdutosUsadosNosHorariosFinalizados(estabelecimento, idsHorariosFinalizadosNaTransacao, fechouConta: true);

                retorno.NumeroFechamento = numeroDoFechamento;
                retorno.IdTransacao = transacao.Id;

                Domain.Financeiro.SplitService.GerarRegistroDeSplitDePagamentoSeNecessario(transacao);
            }

            return retorno;
        }

        public void CapturarCobrancaCasoSejaNecessario(CheckoutRealizadoDTO checkoutRealizado)
        {
            try
            {
                var idEstabelecimento = Domain.WebContext.IdEstabelecimentoAutenticado.Value;
                var configPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);
                if (configPOS.TipoPOS.Id != (int)SubadquirenteEnum.ConnectPagarme)
                    return;

                var estabelecimentoConfig = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.ObterPorIdEstabelecimento(idEstabelecimento);
                var estabelecimentoRealizaSplit = estabelecimentoConfig.HabilitaSplitPagamento;

                if (!estabelecimentoRealizaSplit)
                    return;

                var transacaoPOS = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorIdTransacao(checkoutRealizado.IdTransacao);
                if (transacaoPOS == null)
                    return;

                var transacao = transacaoPOS.Transacao;

                var dadosSplit = Domain.Financeiro.SubadquirenteService.GerarDadosParaRegistroDeSplitSeNecessario(new TransacaoPOS(), transacao, null, configPOS).Result;
                dadosSplit.AgruparSplitsPorRecipient();

                var splitDto = CriarSplitDto(configPOS, idEstabelecimento, dadosSplit, transacao.TotalPagar.Value);
                splitDto.IdExternoDaTransacao = transacaoPOS.InitiatorTransactionKey;
                Domain.Financeiro.TransacaoService.CapturarTransacaoComSplit(configPOS.Estabelecimento, splitDto);
            }
            catch (Exception ex)
            {
                LogService<CheckoutService>.Error(string.Concat("Erro ao capturar cobrança: ", ex.Message));
            }
        }

        private SplitDTO CriarSplitDto(EstabelecimentoConfiguracaoPOS configuracoesPOS, int idEstabelecimento, RequestSplitModel dadosSplit, decimal totalPagar)
        {
            var valorTotalDaTransacao = totalPagar * 100;
            var consideraDescontoDaOperadoraNaComissao = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoConsideraDescontoDaOperadoraNaComissao(idEstabelecimento);

            if (consideraDescontoDaOperadoraNaComissao && dadosSplit != null && dadosSplit.Splits.Count > 0)
            {
                var listaProfissionaisExcessaoDescontoOperadora = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarFiltrandoPelaConfiguracaoDescontoOperadoraProfissional(idEstabelecimento, true, true);
                var profissionaisDoSplitComExcessao = listaProfissionaisExcessaoDescontoOperadora
                    .Where(p =>
                        dadosSplit.Splits.Any(split => split.IdExternoBeneficiario == p.IdExternosDeBeneficiario))
                    .ToList();

                if (profissionaisDoSplitComExcessao.Count == dadosSplit.Splits.Count)
                    consideraDescontoDaOperadoraNaComissao = false;
            }

            var splitDto = new SplitDTO
            {
                Valor = (int)valorTotalDaTransacao,
                IdExternoDaTransacao = dadosSplit.IdExternoTransacao,
                Regras = dadosSplit.Splits.Select(split => new RegraDeSplit
                {
                    Valor = (int)(split.Valor * 100),
                    IdRecebedor = split.IdExternoBeneficiario,
                    Tipo = "flat",
                    Opcoes = new OpcoesDeSplit
                    {
                        CobrarTaxaDeProcessamento = split.CobrarTaxaDoProfissional,
                        RecebeORestanteDaDivisao = false,
                        Responsavel = false
                    }
                }).ToList()
            };

            var valorSplitEstabelecimento = dadosSplit.ValorTransacao - dadosSplit.Splits.Sum(s => s.Valor);
            AdicionarSplitDoEstabelecimento(splitDto, valorSplitEstabelecimento, configuracoesPOS.IdRecebedor, consideraDescontoDaOperadoraNaComissao);

            return splitDto;
        }

        private static void AdicionarSplitDoEstabelecimento(SplitDTO splitDTO, decimal valorSplitEstabelecimento, string idRecebedorDoEstabelecimento, bool consideraDescontoDaOperadoraNaComissao)
        {
            splitDTO.Regras.Add(new RegraDeSplit
            {
                Valor = (int)(valorSplitEstabelecimento * 100),
                IdRecebedor = idRecebedorDoEstabelecimento,
                Tipo = "flat",
                Opcoes = new OpcoesDeSplit
                {
                    CobrarTaxaDeProcessamento = true,
                    RecebeORestanteDaDivisao = true,
                    Responsavel = true
                }
            });
        }

        public bool ControleDeCaixaHabilitadoEOperadorComCaixaFechado()
        {
            var pessoaLogada = Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value).Pessoa.PessoaFisica;
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            if (estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaControleDeCaixaPorProfissional)
            {
                var profissionalLogadoPossuiCaixaAberto =
                    Domain.Financeiro.ControleDeCaixaService.ValidarSeProfissionalLogadoTemCaixaAberto(estabelecimento.PessoaJuridica, pessoaLogada);
                if (!profissionalLogadoPossuiCaixaAberto)
                    return true;
            }

            return false;
        }

        private static List<ItemCheckoutDTO> TodosOsItens(DadosParaCheckoutDTO dto)
        {
            var retorno = new List<ItemCheckoutDTO>();

            retorno.AddRange(dto.ItensPacote);
            retorno.AddRange(dto.ItensProduto);
            retorno.AddRange(dto.ItensServico);
            retorno.AddRange(dto.ItensValePresente);

            return retorno;
        }

        public List<TipoFormaPagamentoDTO> ListarFormasDePagamentoParaFechamentoDeConta()
        {
            var estabelecimentoFormasPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoService
                .ListarFormasDePagamentoParaFechamentoDeContas();

            RemoverFormasDePagamentoAindaNaoImplementadasNoB2B(estabelecimentoFormasPagamento);

            var formasAgrupadas = estabelecimentoFormasPagamento.GroupBy(f => f.FormaPagamento.Tipo);

            var tiposFormaPagamento =
                formasAgrupadas
                .Select(t => new TipoFormaPagamentoDTO
                {
                    Id = t.Key.Id,
                    Nome = t.Key.Nome,
                    FormasPagamento = t.Select(f => new DTO.Checkout.FormaPagamentoDTO
                    {
                        Id = f.FormaPagamento.Id,
                        Nome = f.FormaPagamento.Nome,
                        Parcelas = f.Parcelamento.Where(p => p.Ativo).Select(p => p.NumeroParcela).ToList(),
                        IdTipoPOS = f.FormaPagamento.TipoPOS?.Id,
                        TipoPos = f.FormaPagamento.TipoPOS != null ? new TipoPosDto()
                        {
                            Id = f.FormaPagamento.TipoPOS.Id,
                            Nome = f.FormaPagamento.TipoPOS.NomeExibicao,
                            TerminaisBelezinhas = ObterTerminaisBelezinhas(f),
                        } : null,

                    }).ToList()
                }).ToList();

            RemoverBelezinhaDeAcordoComAsConfiguracoesDoEstabelecimento(tiposFormaPagamento);

            return tiposFormaPagamento;
        }

        private static List<TerminalBelezinhaDto> ObterTerminaisBelezinhas(EstabelecimentoFormaPagamento estabelecimentoFormaPagamento)
        {
            var idsDosTiposDePosQuePrecisamDeTerminaisCadastrados = new List<int>()
            {
                (int)SubadquirenteEnum.ConnectStone,
                (int)SubadquirenteEnum.ConnectPagarme
            };

            if (!idsDosTiposDePosQuePrecisamDeTerminaisCadastrados.Contains(estabelecimentoFormaPagamento.FormaPagamento
                    .TipoPOS.Id))
                return new List<TerminalBelezinhaDto>();

            var terminaisBelezinhas = Domain.Belezinha.EstabelecimentoTerminalPosService
                .ObterTerminaisDoEstabelecimentoPorIdEstabelecimentoEIdTipoPos(
                    estabelecimentoFormaPagamento.Estabelecimento.IdEstabelecimento,
                    estabelecimentoFormaPagamento.FormaPagamento.TipoPOS.Id
                );

            return terminaisBelezinhas
                .Select(terminalPos => new TerminalBelezinhaDto()
                {
                    Id = terminalPos.Id,
                    Nome = terminalPos.Nome,
                    SerialNumber = terminalPos.SerialNumber,
                    DataUltimaTransacao = terminalPos.DataUltimaTransacao,
                    VinculadaAoAutoatendimento = terminalPos.VinculadaAoAutoatendimento,
                }).ToList();
        }

        private static void RemoverBelezinhaDeAcordoComAsConfiguracoesDoEstabelecimento(List<TipoFormaPagamentoDTO> tiposFormaPagamento)
        {
            var configPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);
            var belezinhaEstaDisponivel =
                ObterDisponibilidadeDaUtilizacaoDaBelezinhaNoFechamento(Domain.WebContext.IdEstabelecimentoAutenticado
                    .Value).EstaDisponivel;
            tiposFormaPagamento.ForEach(t =>
            {
                // Deve manter somente a primeira forma de pagamento belezinha
                var formaBelezinha = t.FormasPagamento.FirstOrDefault(f => f.IdTipoPOS == configPos?.TipoPOS.Id);

                t.FormasPagamento.RemoveAll(f => f.IdTipoPOS > 0);

                if ((configPos?.ConcluiuConfiguracao ?? false) && formaBelezinha != null && belezinhaEstaDisponivel)
                {
                    formaBelezinha.Nome = formaBelezinha.Nome.Split(' ')[0].Trim(); // Deixa somente o primeiro nome da forma de pagamento
                    t.FormasPagamento.Add(formaBelezinha);
                    t.FormasPagamento = t.FormasPagamento.OrderBy(f => f.Nome).ToList();
                }
            });
        }

        private static DisponibilidadeDoRecurso ObterDisponibilidadeDaUtilizacaoDaBelezinhaNoFechamento(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            return Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.UtilizarBelezinhaFechamentoTrinksProfissional);
        }

        private static void RemoverFormasDePagamentoAindaNaoImplementadasNoB2B(List<EstabelecimentoFormaPagamento> estabelecimentoFormasPagamento)
        {
            var tiposPosImplementados = new List<SubadquirenteEnum>()
            {
                SubadquirenteEnum.StoneSiclos,
                SubadquirenteEnum.ConnectPagarme,
                SubadquirenteEnum.ConnectStone
            };

            estabelecimentoFormasPagamento.RemoveAll(f => f.FormaPagamento.TipoPOS != null
                                                          && !tiposPosImplementados.Contains((SubadquirenteEnum)f.FormaPagamento.TipoPOS.Id));
            estabelecimentoFormasPagamento.RemoveAll(f => f.FormaPagamento == FormaPagamentoEnum.DeixarFaltaComoDivida);
            estabelecimentoFormasPagamento.RemoveAll(f => f.FormaPagamento == FormaPagamentoEnum.CreditoCliente);
            estabelecimentoFormasPagamento.RemoveAll(f => f.FormaPagamento == FormaPagamentoEnum.ValePresente);
        }

        #region Cálculos Checkout

        private static decimal TotalAPagar(DadosParaCheckoutDTO dto)
        {
            return ValorTotalServicos(dto) + ValorTotalProdutos(dto) + ValorTotalPacotes(dto) + ValorAPagarDeVendaValesPresente(dto) + dto.TotalPagoEmDividas;
        }

        private static decimal TotalPago(DadosParaCheckoutDTO dto)
        {
            return dto.FormasPagamento.Sum(f => f.Valor);
        }

        private static decimal ValorAPagarDeVendaValesPresente(DadosParaCheckoutDTO dto)
        {
            return dto.ItensValePresente.Sum(f => ValorTotal(f));
        }

        private static bool ValorFoiQuitado(DadosParaCheckoutDTO dto)
        {
            if (dto.TipoComprador == TipoCompradorEnum.Profissional)
                return true;

            var valorPago = TotalPago(dto);
            var valorAPagar = TotalAPagar(dto);

            return (valorPago >= valorAPagar) && valorAPagar >= 0;
        }

        private static decimal ValorTotal(ItemCheckoutDTO dto)
        {
            return dto.ValorUnitario * dto.Quantidade - dto.Desconto;
        }

        private static decimal ValorTotalPacotes(DadosParaCheckoutDTO dto)
        {
            return dto.ItensPacote.Sum(f => ValorTotal(f));
        }

        private static decimal ValorTotalProdutos(DadosParaCheckoutDTO dto)
        {
            return dto.ItensProduto.Sum(f => ValorTotal(f));
        }

        private static decimal ValorTotalServicos(DadosParaCheckoutDTO dto)
        {
            return dto.ItensServico.Sum(f => ValorTotal(f));
        }

        #endregion Cálculos Checkout

        #region Adapter

        private static ItemCheckoutDetalheServicoDTO ToItemCheckoutServicoDTO(Horario horario)
        {
            return new ItemCheckoutDetalheServicoDTO
            {
                DataHora = horario.DataInicio,
                Duracao = horario.Duracao,
                IdProfissionalAssistente = horario.EstabelecimentoProfissionalAssistente?.Profissional.IdProfissional,
                IdHorario = horario.Id,
                IdProfissional = horario.Profissional.IdProfissional,
                IdServicoEstabelecimento = horario.ServicoEstabelecimento.IdServicoEstabelecimento,
                IdPessoaQuePagou = horario.ClienteEstabelecimento.Cliente.PessoaFisica.IdPessoa,
                ValorUnitario = horario.Valor + horario.ObterCustoDosProdutosAssociadosAoHorarioParaCliente(),
                NomeAssistente = horario.EstabelecimentoProfissionalAssistente?.Profissional.PessoaFisica.NomeOuApelido(),
                NomeProfissional = horario.Profissional.PessoaFisica.NomeOuApelido(),
                NomeServico = horario.ServicoEstabelecimento.Nome,
                Status = new AgendamentoStatusDTO
                {
                    Cor = horario.Status.Cor,
                    Id = horario.Status.Codigo,
                    Nome = horario.Status.Nome
                }
            };
        }

        #endregion Adapter

        #region Validação

        private static bool ValidarDadosCheckoutDTO(DadosParaCheckoutDTO dto)
        {
            var itensServico = dto.ItensServico;
            if (itensServico.Any())
            {
                var idsHorario = itensServico.Where(f => f.IdHorario > 0).Select(p => p.IdHorario).ToList();

                for (int i = 0; i < idsHorario.Count; i++)
                {
                    var horario = Domain.Pessoas.HorarioRepository.Load(idsHorario[i]);
                    if (horario.FoiPago)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("Já existe um agendamento já pago neste fechamento.");
                        break;
                    }
                }
            }

            if (ValorFoiQuitado(dto) && dto.TotalDeixadoEmDividas != 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(Mensagens.MensagemErroPadrao);
            }
            if (!dto.ItensServico.Any()
                && !dto.ItensPacote.Any()
                && !dto.ItensProduto.Any()
                && !dto.ItensValePresente.Any()
                && !dto.EhCompraCredito
                && dto.TotalPagoEmDividas != 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O fechamento de conta deve conter pelo menos um item.");
            }
            if (dto.TipoComprador == TipoCompradorEnum.Cliente &&
                TotalPago(dto) < TotalAPagar(dto) && dto.TotalDeixadoEmDividas == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O total pago deve ser igual ou maior que o total a pagar.");
            }
            if (itensServico.Any(f => f.IdProfissional == 0))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Existe serviço em fila de espera. Favor definir o profissional.");
            }
            if (itensServico.Any() && itensServico.Select(f => f.DataHora.Date).Distinct().Count() > 1)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Existem agendamentos em mais de uma data.\nSó é permitido fechar conta de agendamentos de um dia por vez.");
            }

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);
            if (!Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.Ignora_Valor_Maximo_Desconto))
            {
                var percentualMaximoDesconto = estabelecimento.EstabelecimentoConfiguracaoGeral.PercentualDeDescontoMaximo;
                if (percentualMaximoDesconto.HasValue)
                {
                    if (percentualMaximoDesconto.Value > 0)
                    {
                        if (TodosOsItens(dto).Any(f => f.Desconto > (f.ValorUnitario * f.Quantidade * percentualMaximoDesconto / 100)))
                        {
                            ValidationHelper.Instance.AdicionarItemValidacao("O percentual máximo de desconto configurado pelo sistema é " + percentualMaximoDesconto + "%");
                        }
                    }
                    else
                    {
                        if (TodosOsItens(dto).Any(f => f.Desconto > 0))
                        {
                            ValidationHelper.Instance.AdicionarItemValidacao("O percentual máximo de desconto configurado pelo sistema é " + percentualMaximoDesconto + "%");
                        }
                    }
                }
            }

            return ValidationHelper.Instance.IsValid;
        }
        #endregion Validação    

        public DadosCheckoutRealizadoDTO DetalhesDoFechamento(int IdTransacao, bool considerarDadosDePacote)
        {
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            var transacao = Domain.Financeiro.TransacaoRepository.Queryable()
                .FirstOrDefault(f => f.Id == IdTransacao && f.Ativo && f.PessoaQueRecebeu == estabelecimento.PessoaJuridica);

            if (transacao == null)
                return null;

            var venda = Domain.Vendas.VendaRepository.Queryable().FirstOrDefault(f => f.Transacao.Id == IdTransacao);

            var retorno = new DadosCheckoutRealizadoDTO
            {
                IdTransacao = transacao.Id,
                NomeCliente = transacao.PessoaQuePagou != null ? transacao.PessoaQuePagou.NomeCompleto : "",
                NomeProfissional = transacao.PessoaQueRealizou != null ? transacao.PessoaQueRealizou.NomeCompleto : "",
                Comentario = transacao.ComentarioFechamentoConta,
                DataPagamento = transacao.DataHora,
                DataReferencia = transacao.DataReferencia,
                TotalPagar = transacao.TotalPagar ?? 0,
                Recebido = transacao.TotalPago ?? 0,
                Troco = transacao.Troco ?? 0
            };

            retorno.TiposFormasPagamento = ToFormasPagamentoCheckoutDto(transacao.FormasPagamento);

            retorno.ItensServico = ToItensCheckoutDetalheServicoDto(transacao.HorariosTransacoes);

            retorno.ItensProduto = ToItensCheckoutProdutoDto(venda.ItensVenda.Where(f => f is ItemVendaProduto).ToList());

            if (considerarDadosDePacote)
            {
                retorno.ItensPacote = ToItensCheckoutDetalhePacoteDTO(venda.ItensVenda.Where(f => f is ItemVendaPacote).ToList());
            }

            if (transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno)
            {
                retorno.IdTransacao = Domain.Financeiro.TransacaoRepository.ObterPorTransacaoQueEstornou(transacao.Id).Id;
            }

            return retorno;
        }

        public List<CheckoutsPorDiaDTO> ListarFechamentos(DateTime? dataInicio, DateTime? dataFim)
        {

            IQueryable<Transacao> queryableTransacao = Domain.Financeiro.TransacaoRepository.Queryable();
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            queryableTransacao = queryableTransacao.Where(t =>
                t.Ativo && t.PessoaQueRecebeu == estabelecimento.PessoaJuridica && t.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && t.TransacaoQueEstounouEsta == null);
            queryableTransacao = AdicionarFiltroPorData(dataInicio, dataFim, queryableTransacao);
            var listaDeFechamentos = queryableTransacao.Select(t =>
                                            new
                                            {
                                                IdTransacao = t.Id,
                                                NomePessoaQuePagou = t.PessoaQuePagou != null ? t.PessoaQuePagou.NomeCompleto : "Cliente não identificado",
                                                NomePessoaQueRecebeu = t.PessoaQueRealizou != null ? t.PessoaQueRealizou.NomeCompleto : "",
                                                DataPagamento = t.DataHora,
                                                DataReferencia = t.DataReferencia,
                                                IdTipoTransacao = t.TipoTransacao.Id,
                                                TotalPagar = t.TotalPagar ?? 0
                                            });


            var retorno = new List<CheckoutsPorDiaDTO>();
            foreach (var item in listaDeFechamentos.OrderBy(f => f.DataPagamento))
            {
                var checkout = new DadosBasicosCheckoutRealizadoParaListagemDTO
                {
                    IdTransacao = item.IdTransacao,
                    NomeCliente = item.NomePessoaQuePagou,
                    NomeProfissional = item.NomePessoaQueRecebeu,
                    DataPagamento = item.DataPagamento,
                    DataReferencia = item.DataReferencia,
                    TotalPagar = item.TotalPagar
                };

                if (!retorno.Any(f => f.Data.Date == checkout.DataPagamento.Date))
                {
                    retorno.Add(new CheckoutsPorDiaDTO()
                    {
                        Data = checkout.DataPagamento.Date
                    });
                }

                retorno.FirstOrDefault(f => f.Data.Date == checkout.DataPagamento.Date).Checkouts.Add(checkout);
            }
            return retorno;
        }

        private static IQueryable<Transacao> AdicionarFiltroPorData(DateTime? dataInicio, DateTime? dataFim, IQueryable<Transacao> queryableTransacao)
        {
            if (dataInicio.HasValue && !dataFim.HasValue)
            {
                var dtInicio = new DateTime(dataInicio.Value.Year, dataInicio.Value.Month, dataInicio.Value.Day, 0, 0, 0);
                queryableTransacao = queryableTransacao.Where(f => f.DataHora >= dtInicio);
            }
            else if (!dataInicio.HasValue && dataFim.HasValue)
            {
                var dtFim = new DateTime(dataFim.Value.Year, dataFim.Value.Month,
                    dataFim.Value.Day, 23, 59, 59);
                queryableTransacao = queryableTransacao.Where(f => f.DataHora <= dtFim);
            }
            else if (dataInicio.HasValue && dataFim.HasValue)
            {
                var dtInicio = new DateTime(dataInicio.Value.Year, dataInicio.Value.Month,
                    dataInicio.Value.Day, 0, 0, 0);
                var dtFim = new DateTime(dataFim.Value.Year, dataFim.Value.Month,
                    dataFim.Value.Day, 23, 59, 59);
                queryableTransacao = queryableTransacao.Where(f => f.DataHora >= dtInicio && f.DataHora <= dtFim);
            }

            return queryableTransacao;
        }

        private List<ItemCheckoutDetalheProdutoDTO> ToItensCheckoutProdutoDto(IList<ItemVenda> itensVenda)
        {
            List<ItemCheckoutDetalheProdutoDTO> ItensCheckoutProduto = new List<ItemCheckoutDetalheProdutoDTO>();
            foreach (ItemVendaProduto itemVenda in itensVenda)
            {
                ItensCheckoutProduto.Add(new ItemCheckoutDetalheProdutoDTO()
                {
                    IdEstabelecimentoProduto = itemVenda.EstabelecimentoProduto.Id,
                    NomeProduto = itemVenda.EstabelecimentoProduto.Descricao,
                    MedidaPorUnidade = itemVenda.EstabelecimentoProduto.MedidasPorUnidade,
                    NomeProfissional = itemVenda.PessoaComissionada != null ? itemVenda.PessoaComissionada.NomeCompleto : "",
                    Desconto = itemVenda.Desconto,
                    IdClienteEstabelecimentoQueUsou = itemVenda.ClienteEstabelecimentoQueUsou?.Codigo,
                    IdMotivoDesconto = itemVenda.MotivoDesconto?.Id,
                    IdPessoaComissionada = itemVenda.PessoaComissionada?.IdPessoa,
                    Quantidade = itemVenda.Quantidade,
                    TipoDeQuantidade = itemVenda.TipoDeQuantidade,
                    ValorUnitario = itemVenda.ValorUnitario
                });
            }
            return ItensCheckoutProduto;
        }

        private List<ItemCheckoutDetalheServicoDTO> ToItensCheckoutDetalheServicoDto(IList<HorarioTransacao> horariosTransacao)
        {
            List<ItemCheckoutDetalheServicoDTO> ItemCheckoutServico = new List<ItemCheckoutDetalheServicoDTO>();
            foreach (var horarioTransacao in horariosTransacao)
            {
                var horario = horarioTransacao.Horario;
                ItemCheckoutServico.Add(new ItemCheckoutDetalheServicoDTO()
                {
                    IdHorario = horario.Id,

                    IdServicoEstabelecimento = horario.ServicoEstabelecimento.IdServicoEstabelecimento,
                    NomeServico = horario.ServicoEstabelecimento.Nome,
                    ValorUnitario = horarioTransacao.Preco ?? horario.ServicoEstabelecimento.Preco,
                    DataHora = horario.DataInicio,
                    Duracao = horario.Duracao,

                    IdProfissional = horario.Profissional.IdProfissional,
                    NomeProfissional = horario.Profissional.PessoaFisica.NomeCompleto,
                    IdProfissionalAssistente = horario.EstabelecimentoProfissionalAssistente?.Profissional.IdProfissional ?? 0,
                    NomeAssistente = horario.EstabelecimentoProfissionalAssistente?.Profissional.PessoaFisica.NomeCompleto ?? "",

                    IdMotivoDesconto = horarioTransacao.MotivoDesconto?.Id ?? 0,
                    Desconto = horarioTransacao.Desconto ?? 0,

                });
            }
            return ItemCheckoutServico;
        }

        private List<ItemCheckoutDetalhePacoteDTO> ToItensCheckoutDetalhePacoteDTO(IList<ItemVenda> itensVendaPacote)
        {
            List<ItemCheckoutDetalhePacoteDTO> ItensCheckoutPacote = new List<ItemCheckoutDetalhePacoteDTO>();

            foreach (ItemVendaPacote itemVendaPacote in itensVendaPacote)
            {
                ItensCheckoutPacote.Add(new ItemCheckoutDetalhePacoteDTO()
                {
                    IdPacote = itemVendaPacote.PacoteCliente.PacoteOriginal.Id,
                    NomePacote = itemVendaPacote.PacoteCliente.Nome,
                    ValorUnitario = itemVendaPacote.ValorUnitario,
                    IdPessoaComissionada = itemVendaPacote.PessoaComissionada?.IdPessoa,
                    NomeProfissional = itemVendaPacote.PessoaComissionada != null ? itemVendaPacote.PessoaComissionada.NomeCompleto : "",
                    IdMotivoDesconto = itemVendaPacote.MotivoDesconto?.Id,
                    Desconto = itemVendaPacote.Desconto
                });
            }

            return ItensCheckoutPacote;
        }

        private List<TipoFormaPagamentoCheckoutDTO> ToFormasPagamentoCheckoutDto(IList<TransacaoFormaPagamento> transacaoFormaPagamento)
        {


            var formasAgrupadas = transacaoFormaPagamento.GroupBy(f => f.FormaPagamento.Tipo);

            var tiposFormaPagamento =
                formasAgrupadas
                .Select(t => new TipoFormaPagamentoCheckoutDTO
                {
                    Id = t.Key.Id,
                    Nome = t.Key.Nome,
                    FormasPagamento = t.Select(f => new FormaPagamentoCheckoutDTO
                    {
                        IdFormaPagamento = f.FormaPagamento.Id,
                        Nome = f.FormaPagamento.Nome,
                        Parcelas = f.Parcelas.Where(p => p.TransacaoFormaPagamento.Ativo).Select(parcela => new FormaPagamentoParcelaCheckoutDTO()
                        {
                            Data = parcela.DataPagamento,
                            NumeroParcela = parcela.NumeroParcela,
                            Valor = parcela.TransacaoFormaPagamento.ValorPago
                        }).ToList(),
                        IdTipoPOS = f.FormaPagamento.TipoPOS?.Id,
                        Valor = f.ValorPago
                    }).ToList()
                }).ToList();

            return tiposFormaPagamento;
        }


        private List<FormaPagamentoCheckoutDTO> ToFormasPagamentoCheckoutDTO(Transacao transacao)
        {
            return transacao.FormasPagamento.Select(f => new FormaPagamentoCheckoutDTO()
            {
                IdFormaPagamento = f.FormaPagamento.Id,
                IdPessoaQuePagou = f.PessoaQuePagou.IdPessoa,
                IdTipoPOS = f.FormaPagamento.TipoPOS?.Id,
                Nome = f.FormaPagamento.Nome,
                Valor = f.ValorPago,
                Parcelas = f.Parcelas.Select(p => new FormaPagamentoParcelaCheckoutDTO()
                {
                    Data = p.DataPagamento,
                    NumeroParcela = p.NumeroParcela,
                    Valor = p.Valor
                }).ToList()
            }).ToList();
        }

        private List<ItemCheckoutPacoteDTO> ToItensCheckoutPacoteDTO(Venda venda)
        {
            return venda.ItensVenda.Where(iv => iv is ItemVendaPacote).Select(p => new ItemCheckoutPacoteDTO()
            {
                Desconto = p.Desconto,
                IdMotivoDesconto = p.MotivoDesconto?.Id,
                IdPacote = ((ItemVendaPacote)p).PacoteCliente.PacoteOriginal.Id,
                Quantidade = p.Quantidade,
                ValorUnitario = p.ValorUnitario
            }).ToList();
        }

        private List<ItemCheckoutProdutoDTO> ToItensCheckoutProdutoDTO(Venda venda)
        {
            return venda.ItensVenda.Where(iv => iv is ItemVendaProduto).Select(p => new ItemCheckoutProdutoDTO()
            {
                Desconto = p.Desconto,
                IdMotivoDesconto = p.MotivoDesconto.Id,
                Quantidade = p.Quantidade,
                ValorUnitario = p.ValorUnitario,
                IdPessoaComissionada = p.PessoaComissionada.IdPessoa,
                IdClienteEstabelecimentoQueUsou = ((ItemVendaProduto)p).ClienteEstabelecimentoQueUsou.Codigo,
                IdEstabelecimentoProduto = ((ItemVendaProduto)p).EstabelecimentoProduto.Id,
                TipoDeQuantidade = ((ItemVendaProduto)p).TipoDeQuantidade
            }).ToList();
        }

        private List<ItemCheckoutServicoDTO> ToItensCheckoutServicoDTO(Venda venda)
        {
            return venda.Transacao.HorariosTransacoes.Select(ht =>
             new ItemCheckoutServicoDTO
             {
                 DataHora = ht.Horario.DataInicio,
                 Duracao = ht.Horario.Duracao,
                 IdProfissionalAssistente = ht.Horario.EstabelecimentoProfissionalAssistente?.Profissional.IdProfissional,
                 IdHorario = ht.Horario.Id,
                 IdProfissional = ht.Horario.Profissional.IdProfissional,
                 IdServicoEstabelecimento = ht.Horario.ServicoEstabelecimento.IdServicoEstabelecimento,
                 ValorUnitario = ht.Horario.Valor,
                 Desconto = ht.Desconto ?? 0,
                 IdMotivoDesconto = ht.MotivoDesconto.Id,
                 Quantidade = ht.ObterQuantidade()
             }).ToList();
        }

        private List<ItemCheckoutValePresenteDTO> ToItensCheckoutValePresente(Venda venda)
        {
            return venda.ItensVenda.Where(iv => iv is ItemVendaValePresente).Select(p => new ItemCheckoutValePresenteDTO()
            {
                Desconto = p.Desconto,
                IdMotivoDesconto = p.MotivoDesconto.Id,
                Quantidade = p.Quantidade,
                ValorUnitario = p.ValorUnitario,
                NumeroDoVale = ((ItemVendaValePresente)p).ValePresente.Numero,
                Validade = ((ItemVendaValePresente)p).ValePresente.Validade
            }).ToList();
        }

        private TipoCompradorEnum ObterTipoDeCompradorPelaVenda(Venda venda)
        {
            return venda.Transacao.FoiVendaParaProfissional ? TipoCompradorEnum.Profissional : TipoCompradorEnum.Cliente;
        }

        public DadosParaCheckoutDTO ObterDadosParaCheckoutBaseadoEmTransacao(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);

            return new DadosParaCheckoutDTO()
            {
                Comentario = transacao.ComentarioFechamentoConta,
                FormasPagamento = ToFormasPagamentoCheckoutDTO(transacao),
                IdPessoaQuePagou = transacao.PessoaQuePagou.IdPessoa,
                ItensPacote = ToItensCheckoutPacoteDTO(venda),
                ItensProduto = ToItensCheckoutProdutoDTO(venda),
                ItensServico = ToItensCheckoutServicoDTO(venda),
                ItensValePresente = ToItensCheckoutValePresente(venda),
                TotalPagoEmDividas = transacao.ObterValorTotalPagoEmDividas(),
                TotalDeixadoEmDividas = transacao.ObterValorDeixadoParaPagarDepois(),
                TipoComprador = ObterTipoDeCompradorPelaVenda(venda),
                IdTransacaoPos = null,
                EhCompraCredito = false,
                DataPagamento = Calendario.Agora(),
            };
        }
    }
}
