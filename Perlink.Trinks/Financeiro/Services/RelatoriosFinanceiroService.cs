﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Services
{
    public class RelatoriosFinanceiroService : BaseService, IRelatoriosFinanceiroService
    {
        public List<KeyValuePair<int, string>> ListarKeyValueComOpcoesDeFiltroDeFormasDePagamento(int idEstabelecimento)
        {

            var configPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);

            var estFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .ListarAtivasPorEstabelecimentoEhTipoPOS(idEstabelecimento, configPOS != null ? configPOS.TipoPOS.Id : 0)
                .Select(f => new KeyValuePair<int, string>(f.FormaPagamento.Id, f.FormaPagamento.Tipo.Nome + " - " + f.FormaPagamento.Nome))
                .ToList();

            estFormaPagamento.RemoveAll(fp => fp.Key == (int)FormaPagamentoEnum.DeixarFaltaComoDivida);
            estFormaPagamento.RemoveAll(fp => fp.Key == (int)FormaPagamentoEnum.CreditoDePagamentoOnline);
            estFormaPagamento.RemoveAll(fp => fp.Key == (int)FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite);
            estFormaPagamento.RemoveAll(fp => fp.Key == (int)FormaPagamentoEnum.DescontoDeProfissional);

            return estFormaPagamento;
        }
    }
}
