﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Services
{
    public class RelatorioComissaoService : BaseService, IRelatorioComissaoService
    {
        #region Agrupado por mês

        public IEnumerable<RelatorioComissao> ObterAgrupadoPorMes(ParametrosFiltrosRelatorio parametros)
        {
            IEnumerable<RelatorioComissao> retorno = new List<RelatorioComissao>();

            bool naoConsiderarAssistentesNosTotais = DesconsiderarAssistenteNosTotais(parametros);

            if (parametros.TipoData == TipoDataRelatorio.DataDaComissao)
            {
                retorno = ObterAgrupadoPorMesPorDataDaComissao(parametros, naoConsiderarAssistentesNosTotais);//rever este método após análise de desempenho
            }
            else
            {
                retorno = ObterAgrupadoPorMesPorDataDeAtendimentoOuPagamento(parametros, naoConsiderarAssistentesNosTotais);//rever este método após análise de desempenho
            }
            return retorno;
        }

        private static void AdicionarOutrosValoresPorMes(ParametrosFiltrosRelatorio parametros, List<RelatorioComissao> retorno)
        {

            #region Desconto Profissional Vale

            var descontoVale = Domain.Despesas.LancamentoRepository.ListarVales(parametros);
            var valesAgrupados = descontoVale.GroupBy(f => new { f.DataPagamento.Value.Month, f.DataPagamento.Value.Year });
            foreach (var mes in valesAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, 1)
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorVale += mes.Sum(f => f.Valor);
            }

            #endregion Desconto Profissional Vale

            #region Desconto Pagamento Split

            var descontoSplit = Domain.Despesas.LancamentoRepository.ListarSplitsDePagamento(parametros);
            var splitsAgrupados = descontoSplit.GroupBy(f => new { f.DataPagamento.Value.Month, f.DataPagamento.Value.Year });
            foreach (var mes in splitsAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, 1)
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorSplit += mes.Sum(f => f.Valor);
            }

            #endregion Desconto Pagamento Split

            #region Bonificação

            var bonificacoesAgrupados = Domain.Despesas.LancamentoRepository
                .ListarBonificacoes(parametros)
                .GroupBy(f => new { f.DataPagamento.Value.Month, f.DataPagamento.Value.Year });

            foreach (var mes in bonificacoesAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, 1)
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorBonificacao += mes.Sum(f => f.Valor);
            }

            #endregion Bonificação

            #region Desconto Profissional Venda Produto

            var descontoVendaProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ListarComDescontoProfissional(parametros);

            var vendaProdutoAgrupados = descontoVendaProduto
                .GroupBy(f => new { f.DataPagamento.Month, f.DataPagamento.Year })
                .Select(f => new { f.Key.Month, f.Key.Year, Total = f.Sum(g => (decimal)g.Valor) });
            foreach (var mes in vendaProdutoAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Month && d.DataReferencia.Year == mes.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Year, mes.Month, 1)
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorVale += mes.Total;
            }

            #endregion Desconto Profissional Venda Produto
        }

        private IEnumerable<RelatorioComissao> ObterAgrupadoPorMesPorDataDaComissao(ParametrosFiltrosRelatorio parametros, bool naoConsiderarAssistentesNosTotais)
        {
            var buscaDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Listar(parametros);

            var agrupado = buscaDeValores
                .GroupBy(f => new { f.DataDaComissaoAReceber.Month, f.DataDaComissaoAReceber.Year });

            var dados = agrupado.Select(f => new
            {
                DataReferencia = new DateTime(f.Key.Year, f.Key.Month, 1),
                TotalBaseComissao = f.Sum(g => g.ValorBaseProporcional),
                TotalComissao = f.Sum(g => g.Valor),
                TotalDescartaveis = f.Sum(g => g.DescontoExtraProporcional),
                TotalDescontoOperadoras = f.Sum(g => g.DescontoOperadoraProporcional),
                TotalDescontos = f.Sum(g => g.DescontoClienteProporcional),
                TotalServicos = f.Sum(g => g.ValorBrutoProporcional)
            }).ToList();

            var retorno = dados.Select(f => new RelatorioComissao
            {
                DataReferencia = f.DataReferencia,
                TotalBaseComissao = f.TotalBaseComissao,
                TotalComissao = f.TotalComissao,
                TotalDescartaveis = f.TotalDescartaveis,
                TotalDescontoOperadoras = f.TotalDescontoOperadoras,
                TotalDescontos = f.TotalDescontos,
                TotalServicos = f.TotalServicos
            }).ToList();

            AdicionarOutrosValoresPorMes(parametros, retorno);

            return retorno.OrderBy(f => f.DataReferencia);
        }

        private IEnumerable<RelatorioComissao> ObterAgrupadoPorMesPorDataDeAtendimentoOuPagamento(ParametrosFiltrosRelatorio parametros, bool naoConsiderarAssistentesNosTotais)
        {
            var buscaDeValores = Domain.Financeiro.ComissaoRepository.Listar(parametros);

            var agrupado = parametros.TipoData == TipoDataRelatorio.DataAtendimento
                ? buscaDeValores.GroupBy(f => new { f.Transacao.DataReferencia.Month, f.Transacao.DataReferencia.Year })
                : buscaDeValores.GroupBy(f => new { f.Transacao.DataHora.Month, f.Transacao.DataHora.Year });

            var dados = agrupado.Select(f => new
            {
                DataReferencia = new DateTime(f.Key.Year, f.Key.Month, 1),
                TotalBaseComissao = f.Sum(g => g.ValorBase),
                TotalComissao = f.Sum(g => g.ComissaoParaPagar),
                TotalDescartaveis = f.Sum(g => g.DescontoExtra),
                TotalDescontoOperadoras = f.Sum(g => g.DescontoOperadora),
                TotalDescontos = f.Sum(g => g.DescontoCliente),
                TotalServicos = f.Sum(g => g.ValorBruto)
            }).ToList();

            var retorno = dados.Select(f => new RelatorioComissao
            {
                DataReferencia = f.DataReferencia,
                TotalBaseComissao = f.TotalBaseComissao,
                TotalComissao = f.TotalComissao,
                TotalDescartaveis = f.TotalDescartaveis,
                TotalDescontoOperadoras = f.TotalDescontoOperadoras,
                TotalDescontos = f.TotalDescontos,
                TotalServicos = f.TotalServicos
            }).ToList();

            AdicionarOutrosValoresPorMes(parametros, retorno);

            return retorno.OrderBy(f => f.DataReferencia);
        }

        #endregion Agrupado por mês

        #region Agrupado por profissional

        public IEnumerable<RelatorioComissao> ObterAgrupadoPorProfissional(ParametrosFiltrosRelatorio parametros)
        {
            IEnumerable<RelatorioComissao> retorno = new List<RelatorioComissao>();

            if (parametros.TipoData == TipoDataRelatorio.DataDaComissao)
            {
                retorno = ObterAgrupadoPorProfissionalPorDataDaComissao(parametros);
            }
            else
            {
                retorno = ObterAgrupadoPorProfissionalPorDataAtendimentoOuPagamento(parametros);
            }

            return ObterApenasProfissionais(retorno);
        }

        public IEnumerable<RelatorioComissao> ObterApenasProfissionais(IEnumerable<RelatorioComissao> dados)
        {
            return dados.Where(p => Domain.Pessoas.ProfissionalRepository.ObterPorPessoaFisica(p.IdPessoaProfissional) != null);
        }

        private static void AdicionarOutrosValoresPorProfissional(ParametrosFiltrosRelatorio parametros, List<RelatorioComissao> retorno)
        {

            #region Desconto Profissional Vale

            var descontoVale = Domain.Despesas.LancamentoRepository.ListarVales(parametros);
            var valesAgrupados = descontoVale.Where(f => f.PessoaQueRecebeuOuPagou != null).GroupBy(f => new { f.DataPagamento.Value.Month, f.DataPagamento.Value.Year, f.PessoaQueRecebeuOuPagou.IdPessoa });
            foreach (var mes in valesAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year && d.IdPessoaProfissional == mes.Key.IdPessoa);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, 1),
                        IdPessoaProfissional = mes.Key.IdPessoa
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorVale += mes.Sum(f => f.Valor);
            }

            #endregion Desconto Profissional Vale

            #region Desconto Pagamento Split

            var descontoSplit = Domain.Despesas.LancamentoRepository.ListarSplitsDePagamento(parametros);
            var splitsAgrupados = descontoSplit.GroupBy(f => new { f.DataPagamento.Value.Month, f.DataPagamento.Value.Year, f.PessoaQueRecebeuOuPagou.IdPessoa });
            foreach (var mes in splitsAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year && d.IdPessoaProfissional == mes.Key.IdPessoa);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, 1),
                        IdPessoaProfissional = mes.Key.IdPessoa
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorSplit += mes.Sum(f => f.Valor);
            }

            #endregion Desconto Pagamento Split

            #region Bonificação

            var bonificiacoesAgrupadas = Domain.Despesas.LancamentoRepository.ListarBonificacoes(parametros)
                .Where(f => f.PessoaQueRecebeuOuPagou != null)
                .GroupBy(f => new { f.DataPagamento.Value.Month, f.DataPagamento.Value.Year, f.PessoaQueRecebeuOuPagou.IdPessoa });

            foreach (var mes in bonificiacoesAgrupadas)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year && d.IdPessoaProfissional == mes.Key.IdPessoa);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, 1),
                        IdPessoaProfissional = mes.Key.IdPessoa
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorBonificacao += mes.Sum(f => f.Valor);
            }

            #endregion Bonificação

            #region Desconto Profissional Venda Produto

            var descontoVendaProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ListarComDescontoProfissional(parametros);

            var vendaProdutoAgrupados = descontoVendaProduto
                .GroupBy(f => new { f.DataPagamento.Month, f.DataPagamento.Year, f.TransacaoFormaPagamento.Transacao.PessoaQuePagou.IdPessoa })
                .Select(f => new { f.Key.Month, f.Key.Year, Total = f.Sum(g => (decimal)g.Valor), f.Key.IdPessoa });
            foreach (var mes in vendaProdutoAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Month == mes.Month && d.DataReferencia.Year == mes.Year && d.IdPessoaProfissional == mes.IdPessoa);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Year, mes.Month, 1),
                        IdPessoaProfissional = mes.IdPessoa
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorVale += mes.Total;
            }

            #endregion Desconto Profissional Venda Produto

            var idsPessoasProfissionais = retorno.Select(f => f.IdPessoaProfissional).Distinct();
            foreach (var id in idsPessoasProfissionais)
            {
                var profissional = Domain.Pessoas.ProfissionalRepository.ObterPorPessoaFisica(id);

                foreach (var item in retorno.Where(f => f.IdPessoaProfissional == id))
                {
                    if (profissional != null)
                        item.IdProfissionalComissionado = profissional.IdProfissional;
                }
            }
        }

        private IEnumerable<RelatorioComissao> ObterAgrupadoPorProfissionalPorDataAtendimentoOuPagamento(ParametrosFiltrosRelatorio parametros)
        {
            var buscaDeValores = Domain.Financeiro.ComissaoRepository.Listar(parametros);

            var agrupado = parametros.TipoData == TipoDataRelatorio.DataAtendimento
                ? buscaDeValores.GroupBy(f => new { f.Transacao.DataReferencia.Month, f.Transacao.DataReferencia.Year, f.PessoaComissionada.IdPessoa })
                : buscaDeValores.GroupBy(f => new { f.Transacao.DataHora.Month, f.Transacao.DataHora.Year, f.PessoaComissionada.IdPessoa });

            var dados = agrupado.Select(f => new
            {
                DataReferencia = new DateTime(f.Key.Year, f.Key.Month, 1),
                TotalBaseComissao = f.Sum(g => g.ValorBase),
                TotalComissao = f.Sum(g => g.ComissaoParaPagar),
                TotalDescartaveis = f.Sum(g => g.DescontoExtra),
                TotalDescontoOperadoras = f.Sum(g => g.DescontoOperadora),
                TotalDescontos = f.Sum(g => g.DescontoCliente),
                TotalServicos = f.Sum(g => g.ValorBruto),
                IdPessoaProfissional = f.Key.IdPessoa
            }).ToList();

            var retorno = dados.Select(f => new RelatorioComissao
            {
                DataReferencia = f.DataReferencia,
                TotalBaseComissao = f.TotalBaseComissao,
                TotalComissao = f.TotalComissao,
                TotalDescartaveis = f.TotalDescartaveis,
                TotalDescontoOperadoras = f.TotalDescontoOperadoras,
                TotalDescontos = f.TotalDescontos,
                TotalServicos = f.TotalServicos,
                IdPessoaProfissional = f.IdPessoaProfissional
            }).ToList();

            AdicionarOutrosValoresPorProfissional(parametros, retorno);

            return retorno.OrderBy(f => f.DataReferencia);
        }

        private IEnumerable<RelatorioComissao> ObterAgrupadoPorProfissionalPorDataDaComissao(ParametrosFiltrosRelatorio parametros)
        {
            var buscaDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Listar(parametros);

            var agrupado = buscaDeValores
                .GroupBy(f => new { f.DataDaComissaoAReceber.Month, f.DataDaComissaoAReceber.Year, f.Comissao.PessoaComissionada.IdPessoa });

            var dados = agrupado.Select(f => new
            {
                DataReferencia = new DateTime(f.Key.Year, f.Key.Month, 1),
                TotalBaseComissao = f.Sum(g => g.ValorBaseProporcional),
                TotalComissao = f.Sum(g => g.Valor),
                TotalDescartaveis = f.Sum(g => g.DescontoExtraProporcional),
                TotalDescontoOperadoras = f.Sum(g => g.DescontoOperadoraProporcional),
                TotalDescontos = f.Sum(g => g.DescontoClienteProporcional),
                TotalServicos = f.Sum(g => g.ValorBrutoProporcional),
                IdPessoaProfissional = f.Key.IdPessoa
            }).ToList();

            var retorno = dados.Select(f => new RelatorioComissao
            {
                DataReferencia = f.DataReferencia,
                TotalBaseComissao = f.TotalBaseComissao,
                TotalComissao = f.TotalComissao,
                TotalDescartaveis = f.TotalDescartaveis,
                TotalDescontoOperadoras = f.TotalDescontoOperadoras,
                TotalDescontos = f.TotalDescontos,
                TotalServicos = f.TotalServicos,
                IdPessoaProfissional = f.IdPessoaProfissional
            }).ToList();

            AdicionarOutrosValoresPorProfissional(parametros, retorno);

            return retorno.OrderBy(f => f.DataReferencia);
        }

        #endregion Agrupado por profissional

        #region Agrupado por dia

        public IEnumerable<RelatorioComissao> ObterAgrupadoPorDia(ParametrosFiltrosRelatorio parametros)
        {
            IEnumerable<RelatorioComissao> retorno = new List<RelatorioComissao>();
            if (parametros.TipoData == Pessoas.Enums.TipoDataRelatorio.DataDaComissao)
            {
                retorno = ObterAgrupadoPorDiaPorDataDaComissao(parametros);//rever este método após análise de desempenho
            }
            else
            {
                retorno = ObterAgrupadoPorDiaPorDataAtendimentoOuPagamento(parametros);
            }

            return retorno;
        }

        private static void AdicionarOutrosValoresPorProfissionalAgrupadoPorDia(ParametrosFiltrosRelatorio parametros, List<RelatorioComissao> retorno)
        {

            #region Desconto Profissional Vale

            var descontoVale = Domain.Despesas.LancamentoRepository.ListarVales(parametros);
            var valesAgrupados = descontoVale.GroupBy(f => new { f.DataPagamento.Value.Day, f.DataPagamento.Value.Month, f.DataPagamento.Value.Year });
            foreach (var mes in valesAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Day == mes.Key.Day && d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, mes.Key.Day),
                        DataTransacao = new DateTime(mes.Key.Year, mes.Key.Month, mes.Key.Day),
                        IdProfissionalComissionado = parametros.IdProfissional
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorVale += mes.Sum(f => f.Valor);
            }

            #endregion Desconto Profissional Vale

            #region Desconto Pagamento Split

            var descontoSplit = Domain.Despesas.LancamentoRepository.ListarSplitsDePagamento(parametros);
            var splitsAgrupados = descontoSplit.GroupBy(f => new { f.DataPagamento.Value.Day, f.DataPagamento.Value.Month, f.DataPagamento.Value.Year });
            foreach (var mes in splitsAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Day == mes.Key.Day && d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, mes.Key.Day),
                        DataTransacao = new DateTime(mes.Key.Year, mes.Key.Month, mes.Key.Day),
                        IdProfissionalComissionado = parametros.IdProfissional
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorSplit += mes.Sum(f => f.Valor);
            }

            #endregion Desconto Pagamento Split

            #region Bonificação

            var bonificacoesAgrupadas = Domain.Despesas.LancamentoRepository
                .ListarBonificacoes(parametros)
                .GroupBy(f => new { f.DataPagamento.Value.Day, f.DataPagamento.Value.Month, f.DataPagamento.Value.Year });

            foreach (var mes in bonificacoesAgrupadas)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Day == mes.Key.Day && d.DataReferencia.Month == mes.Key.Month && d.DataReferencia.Year == mes.Key.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Key.Year, mes.Key.Month, mes.Key.Day),
                        DataTransacao = new DateTime(mes.Key.Year, mes.Key.Month, mes.Key.Day),
                        IdProfissionalComissionado = parametros.IdProfissional
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorBonificacao += mes.Sum(f => f.Valor);
            }

            #endregion Bonificação

            #region Desconto Profissional Venda Produto

            var descontoVendaProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ListarComDescontoProfissional(parametros);

            //if (parametros.ExibirEstornos) {
            //    descontoVendaProduto = descontoVendaProduto.Where(f => f.Transacao.);
            //        }

            var vendaProdutoAgrupados = descontoVendaProduto
                .GroupBy(f => new { f.DataPagamento.Day, f.DataPagamento.Month, f.DataPagamento.Year })
                .Select(f => new { f.Key.Day, f.Key.Month, f.Key.Year, Total = f.Sum(g => (decimal)g.Valor) });
            foreach (var mes in vendaProdutoAgrupados)
            {
                var retornoMes = retorno.FirstOrDefault(d => d.DataReferencia.Day == mes.Day && d.DataReferencia.Month == mes.Month && d.DataReferencia.Year == mes.Year);

                if (retornoMes == null)
                {
                    retornoMes = new RelatorioComissao
                    {
                        DataReferencia = new DateTime(mes.Year, mes.Month, mes.Day),
                        DataTransacao = new DateTime(mes.Year, mes.Month, mes.Day),
                        IdProfissionalComissionado = parametros.IdProfissional
                    };
                    retorno.Add(retornoMes);
                }

                retornoMes.ValorVale += mes.Total;
            }

            #endregion Desconto Profissional Venda Produto
        }

        private IEnumerable<RelatorioComissao> ObterAgrupadoPorDiaPorDataAtendimentoOuPagamento(ParametrosFiltrosRelatorio parametros)
        {
            var buscaDeValores = Domain.Financeiro.ComissaoRepository.Listar(parametros);

            var agrupado = parametros.TipoData == TipoDataRelatorio.DataAtendimento
                ? buscaDeValores.GroupBy(f => new { f.Transacao.DataReferencia.Day, f.Transacao.DataReferencia.Month, f.Transacao.DataReferencia.Year, f.PessoaComissionada.IdPessoa })
                : buscaDeValores.GroupBy(f => new { f.Transacao.DataHora.Day, f.Transacao.DataHora.Month, f.Transacao.DataHora.Year, f.PessoaComissionada.IdPessoa });

            var dados = agrupado.Select(f => new
            {
                DataReferencia = new DateTime(f.Key.Year, f.Key.Month, f.Key.Day),
                TotalBaseComissao = f.Sum(g => g.ValorBase),
                TotalComissao = f.Sum(g => g.ComissaoParaPagar),
                TotalDescartaveis = f.Sum(g => g.DescontoExtra),
                TotalDescontoOperadoras = f.Sum(g => g.DescontoOperadora),
                TotalDescontos = f.Sum(g => g.DescontoCliente),
                TotalServicos = f.Sum(g => g.ValorBruto)
            }).ToList();

            var retorno = dados.Select(f => new RelatorioComissao
            {
                DataReferencia = f.DataReferencia,
                DataTransacao = f.DataReferencia,
                TotalBaseComissao = f.TotalBaseComissao,
                TotalComissao = f.TotalComissao,
                TotalDescartaveis = f.TotalDescartaveis,
                TotalDescontoOperadoras = f.TotalDescontoOperadoras,
                TotalDescontos = f.TotalDescontos,
                TotalServicos = f.TotalServicos,
                IdProfissionalComissionado = parametros.IdProfissional
            }).ToList();

            AdicionarOutrosValoresPorProfissionalAgrupadoPorDia(parametros, retorno);

            return retorno.OrderBy(f => f.DataReferencia);
        }

        private IEnumerable<RelatorioComissao> ObterAgrupadoPorDiaPorDataDaComissao(ParametrosFiltrosRelatorio parametros)
        {
            var buscaDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Listar(parametros);

            var agrupado = buscaDeValores
                .GroupBy(f => new { f.DataDaComissaoAReceber.Day, f.DataDaComissaoAReceber.Month, f.DataDaComissaoAReceber.Year });

            var dados = agrupado.Select(f => new
            {
                DataReferencia = new DateTime(f.Key.Year, f.Key.Month, f.Key.Day),
                TotalBaseComissao = f.Sum(g => g.ValorBaseProporcional),
                TotalComissao = f.Sum(g => g.Valor),
                TotalDescartaveis = f.Sum(g => g.DescontoExtraProporcional),
                TotalDescontoOperadoras = f.Sum(g => g.DescontoOperadoraProporcional),
                TotalDescontos = f.Sum(g => g.DescontoClienteProporcional),
                TotalServicos = f.Sum(g => g.ValorBrutoProporcional)
            }).ToList();

            var retorno = dados.Select(f => new RelatorioComissao
            {
                DataReferencia = f.DataReferencia,
                DataTransacao = f.DataReferencia,
                TotalBaseComissao = f.TotalBaseComissao,
                TotalComissao = f.TotalComissao,
                TotalDescartaveis = f.TotalDescartaveis,
                TotalDescontoOperadoras = f.TotalDescontoOperadoras,
                TotalDescontos = f.TotalDescontos,
                TotalServicos = f.TotalServicos,
                IdProfissionalComissionado = parametros.IdProfissional
            }).ToList();

            AdicionarOutrosValoresPorProfissionalAgrupadoPorDia(parametros, retorno);

            return retorno.OrderBy(f => f.DataReferencia);
        }

        #endregion Agrupado por dia

        #region Sem agrupamento

        public IList<RelatorioComissaoValorPorParcelaDTO> ListarComissoes(ParametrosFiltrosRelatorio parametros, bool incluiDadosNF)
        {
            var resultado = new List<RelatorioComissaoValorPorParcelaDTO>();

            if (parametros.TipoData == TipoDataRelatorio.DataDaComissao)
            {
                resultado = ListarComissoesComValoresPorParcela(parametros, incluiDadosNF);
            }
            else
            {
                resultado = ListarComissoesDesagrupados(parametros, incluiDadosNF);
            }

            return resultado;
        }

        private static List<RelatorioComissaoValorPorParcelaDTO> ListarComissoesComValoresPorParcela(ParametrosFiltrosRelatorio parametros, bool incluiDadosNF)
        {
            var idEstabelecimentoAutenticado = parametros.Estabelecimento.IdEstabelecimento;
            var estabelecimento = parametros.Estabelecimento;
            var buscaDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Listar(parametros);

            var horarioTransacao = Domain.Pessoas.HorarioTransacaoRepository.Queryable();

            var comissoesServico = from v in buscaDeValores
                                   let comissao = v.Comissao
                                   join ht2 in horarioTransacao on comissao equals ht2.Comissao
                                   let transacao = comissao.Transacao
                                   let horario = ht2.Horario
                                   where ht2.Horario.Estabelecimento.IdEstabelecimento == parametros.IdEstabelecimento
                                   select new RelatorioComissaoValorPorParcelaDTO
                                   {
                                       IdTransacao = transacao.Id,
                                       IdComissao = comissao.Id,
                                       IdValorDeComissaoAReceber = v.Id,
                                       EhConsumoPacote = ht2.ItemPacoteCliente != null,
                                       EhConsumoPacoteNaRede = ht2.ItemPacoteCliente != null && ht2.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != ht2.Horario.Estabelecimento.PessoaJuridica.IdPessoa,
                                       ComissaoFoiAlteradaManualmente = comissao.ComissaoAlteradaManualmente,
                                       ComissaoAReceberFoiAlteradaManualmente = v.ValorAlteradoManualmente,
                                       DataComissaoAReceberFoiAlteradaManualmente = v.DataDaComissaoAReceberAlteradaManualmente,
                                       PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                       EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                       EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                       DataHoraUltimaAlteracaoManualComissao = v.DataHoraUltimaAlteracaoManualDeValor,
                                       DataHoraUltimaAlteracaoManualDataComissaoAReceber = v.DataHoraUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                       ResponsavelPelaUltimaAlteracaoDaDataAReceber = v.ResponsavelPelaUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                       DataHorario = horario.DataInicio,
                                       DataMovimentacao = transacao.DataHora,
                                       HoraMovimentacao = transacao.DataHora,
                                       PessoaCliente = transacao.PessoaQuePagou,
                                       ApelidoProfissional = comissao.PessoaComissionada.Apelido,
                                       NomeCompletoProfissional = comissao.PessoaComissionada.NomeCompleto,
                                       ApelidoProfissionalAssistente = ht2.ComissaoAssistente.PessoaComissionada.Apelido,
                                       NomeCompletoProfissionalAssistente = ht2.ComissaoAssistente.PessoaComissionada.NomeCompleto,
                                       NomeServico = horario.ServicoEstabelecimento.Nome,
                                       UsouPontosDeFidelidade = ht2.UsouPontosDeFidelidade,
                                       MotivoDescontos = ht2.MotivoDesconto != null && ht2.MotivoDesconto.DescontoRefleteNaComissao ? ht2.MotivoDesconto.Descricao : null,
                                       DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                       DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                       TotalComissao = v.Valor,
                                       ValorComissaoParaPagar = v.Valor,
                                       ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                       PossuiComissaoParcelada = comissao.ValorBruto != v.ValorBrutoProporcional,
                                       RelatorioComissao = new RelatorioComissao
                                       {
                                           ResponsavelPelaUltimaAlteracao = (PessoaFisica)v.ResponsavelPelaUltimaAlteracaoManualDeValor ?? comissao.Transacao.PessoaQueRealizou,
                                           TotalServicos = v.ValorBrutoProporcional,
                                           TotalDescontos = v.DescontoClienteProporcional,
                                           TotalDescontoOperadoras = v.DescontoOperadoraProporcional,
                                           TotalServicosNaoParcelado = comissao.ValorBruto,
                                           TotalComissaoNaoParcelado = comissao.ComissaoParaPagar ?? 0,
                                           TotalDescartaveis = v.DescontoExtraProporcional,
                                           TotalPercentualComissao = comissao.ValorComissao,
                                           TotalBaseComissao = v.ValorBaseProporcional,
                                           TotalComissao = v.Valor,
                                           Categoria = horario.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Descricao,
                                           IdTransacao = transacao.Id,
                                           ValorSplit = comissao.EhComissaoComSplit ? comissao.ValorComissao : 0
                                       }
                                   };
            var comissoesServicoAssistente = from v in buscaDeValores
                                             let comissao = v.Comissao
                                             join ht3 in horarioTransacao on comissao equals ht3.ComissaoAssistente
                                             let transacao = comissao.Transacao
                                             let horario = ht3.Horario
                                             where ht3.Horario.Estabelecimento.IdEstabelecimento == parametros.IdEstabelecimento
                                             select new RelatorioComissaoValorPorParcelaDTO
                                             {
                                                 IdTransacao = transacao.Id,
                                                 IdComissao = comissao.Id,
                                                 IdValorDeComissaoAReceber = v.Id,
                                                 EhConsumoPacote = ht3.ItemPacoteCliente != null,
                                                 EhConsumoPacoteNaRede = ht3.ItemPacoteCliente != null && ht3.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != ht3.Horario.Estabelecimento.PessoaJuridica.IdPessoa,
                                                 ComissaoFoiAlteradaManualmente = comissao.ComissaoAlteradaManualmente,
                                                 ComissaoAReceberFoiAlteradaManualmente = v.ValorAlteradoManualmente,
                                                 DataComissaoAReceberFoiAlteradaManualmente = v.DataDaComissaoAReceberAlteradaManualmente,
                                                 PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                                 EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                                 EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                                 DataHoraUltimaAlteracaoManualComissao = v.DataHoraUltimaAlteracaoManualDeValor,
                                                 DataHoraUltimaAlteracaoManualDataComissaoAReceber = v.DataHoraUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                                 ResponsavelPelaUltimaAlteracaoDaDataAReceber = v.ResponsavelPelaUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                                 DataHorario = horario.DataInicio,
                                                 DataMovimentacao = transacao.DataHora,
                                                 HoraMovimentacao = transacao.DataHora,
                                                 PessoaCliente = transacao.PessoaQuePagou,
                                                 ApelidoProfissional = horario.Profissional.PessoaFisica.Apelido,
                                                 NomeCompletoProfissional = horario.Profissional.PessoaFisica.NomeCompleto,
                                                 ApelidoProfissionalAssistente = comissao.PessoaComissionada.Apelido,
                                                 NomeCompletoProfissionalAssistente = comissao.PessoaComissionada.NomeCompleto,
                                                 NomeServico = horario.ServicoEstabelecimento.Nome,
                                                 UsouPontosDeFidelidade = ht3.UsouPontosDeFidelidade,
                                                 MotivoDescontos = ht3.MotivoDesconto != null && ht3.MotivoDesconto.DescontoRefleteNaComissao ? ht3.MotivoDesconto.Descricao : null,
                                                 DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                                 DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                                 TotalComissao = v.Valor,
                                                 ValorComissaoParaPagar = v.Valor,
                                                 ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                                 PossuiComissaoParcelada = comissao.ValorBruto != v.ValorBrutoProporcional,
                                                 RelatorioComissao = new RelatorioComissao
                                                 {
                                                     ResponsavelPelaUltimaAlteracao = (PessoaFisica)v.ResponsavelPelaUltimaAlteracaoManualDeValor ?? comissao.Transacao.PessoaQueRealizou,
                                                     TotalServicos = v.ValorBrutoProporcional,
                                                     TotalDescontos = v.DescontoClienteProporcional,
                                                     TotalDescontoOperadoras = v.DescontoOperadoraProporcional,
                                                     TotalDescartaveis = v.DescontoExtraProporcional,
                                                     TotalPercentualComissao = comissao.ValorComissao,
                                                     TotalServicosNaoParcelado = comissao.ValorBruto,
                                                     TotalComissaoNaoParcelado = comissao.ComissaoParaPagar ?? 0,
                                                     TotalBaseComissao = v.ValorBaseProporcional,
                                                     TotalComissao = v.Valor,
                                                     Categoria = horario.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Descricao,
                                                     IdTransacao = transacao.Id,
                                                     ValorSplit = comissao.EhComissaoComSplit ? comissao.ValorComissao : 0
                                                 },
                                                 EhAssistenteComissao = true
                                             };

            var itemVendaProduto = Domain.Vendas.ItemVendaProdutoRepository.Queryable();
            var comissoesProduto = (from v in buscaDeValores
                                    let comissao = v.Comissao
                                    join iv in itemVendaProduto on comissao equals iv.Comissao
                                    let transacao = comissao.Transacao
                                    where iv.EstabelecimentoProduto.Estabelecimento.IdEstabelecimento == parametros.IdEstabelecimento
                                    select new RelatorioComissaoValorPorParcelaDTO
                                    {
                                        IdTransacao = transacao.Id,
                                        IdComissao = comissao.Id,
                                        IdValorDeComissaoAReceber = v.Id,
                                        EhConsumoPacote = iv.ItemPacoteCliente != null,
                                        EhConsumoPacoteNaRede = iv.ItemPacoteCliente != null && iv.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != comissao.IdPessoaEstabelecimento,
                                        ComissaoFoiAlteradaManualmente = comissao.ComissaoAlteradaManualmente,
                                        ComissaoAReceberFoiAlteradaManualmente = v.ValorAlteradoManualmente,
                                        DataComissaoAReceberFoiAlteradaManualmente = v.DataDaComissaoAReceberAlteradaManualmente,
                                        PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                        EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                        EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                        DataHoraUltimaAlteracaoManualComissao = v.DataHoraUltimaAlteracaoManualDeValor,
                                        DataHoraUltimaAlteracaoManualDataComissaoAReceber = v.DataHoraUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                        ResponsavelPelaUltimaAlteracaoDaDataAReceber = v.ResponsavelPelaUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                        DataHorario = iv.Venda.Data,
                                        DataMovimentacao = transacao.DataHora,
                                        HoraMovimentacao = transacao.DataHora,
                                        PessoaCliente = transacao.PessoaQuePagou,
                                        ApelidoProfissional = comissao.PessoaComissionada.Apelido,
                                        NomeCompletoProfissional = comissao.PessoaComissionada.NomeCompleto,
                                        NomeServico = iv.EstabelecimentoProduto.Descricao,
                                        UsouPontosDeFidelidade = iv.UsouPontosDeFidelidade,
                                        MotivoDescontos = iv.MotivoDesconto != null && iv.MotivoDesconto.DescontoRefleteNaComissao ? iv.MotivoDesconto.Descricao : null,
                                        DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                        DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                        TotalComissao = v.Valor,
                                        ValorComissaoParaPagar = v.Valor,
                                        ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                        PossuiComissaoParcelada = comissao.ValorBruto != v.ValorBrutoProporcional,
                                        RelatorioComissao = new RelatorioComissao
                                        {
                                            ResponsavelPelaUltimaAlteracao = v.ResponsavelPelaUltimaAlteracaoManualDeValor ?? comissao.Transacao.PessoaQueRealizou,
                                            TotalServicos = v.ValorBrutoProporcional,
                                            TotalServicosNaoParcelado = comissao.ValorBruto,
                                            TotalComissaoNaoParcelado = comissao.ComissaoParaPagar ?? 0,
                                            TotalDescontos = v.DescontoClienteProporcional,
                                            TotalDescontoOperadoras = v.DescontoOperadoraProporcional,
                                            TotalDescartaveis = v.DescontoExtraProporcional,
                                            TotalPercentualComissao = comissao.ValorComissao,
                                            TotalBaseComissao = v.ValorBaseProporcional,
                                            TotalComissao = v.Valor,
                                            Categoria = iv.EstabelecimentoProduto.EstabelecimentoProdutoCategoria.Nome,
                                            IdTransacao = transacao.Id,
                                            ValorSplit = comissao.EhComissaoComSplit ? comissao.ValorComissao : 0
                                        }
                                    });

            var itemVendaPacote = Domain.Vendas.ItemVendaPacoteRepository.Queryable();
            var comissoesPacote = (from v in buscaDeValores
                                   let comissao = v.Comissao
                                   join iv in itemVendaPacote on comissao equals iv.Comissao
                                   let transacao = comissao.Transacao
                                   select new RelatorioComissaoValorPorParcelaDTO
                                   {
                                       IdTransacao = transacao.Id,
                                       IdComissao = comissao.Id,
                                       IdValorDeComissaoAReceber = v.Id,
                                       EhConsumoPacote = iv.ItemPacoteCliente != null,
                                       EhConsumoPacoteNaRede = iv.ItemPacoteCliente != null && iv.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != comissao.IdPessoaEstabelecimento,
                                       ComissaoFoiAlteradaManualmente = comissao.ComissaoAlteradaManualmente,
                                       ComissaoAReceberFoiAlteradaManualmente = v.ValorAlteradoManualmente,
                                       DataComissaoAReceberFoiAlteradaManualmente = v.DataDaComissaoAReceberAlteradaManualmente,
                                       PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                       EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                       EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                       DataHoraUltimaAlteracaoManualComissao = v.DataHoraUltimaAlteracaoManualDeValor,
                                       DataHoraUltimaAlteracaoManualDataComissaoAReceber = v.DataHoraUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                       ResponsavelPelaUltimaAlteracaoDaDataAReceber = v.ResponsavelPelaUltimaAlteracaoManualDeDataDaComissaoAReceber,
                                       DataHorario = iv.Venda.Data,
                                       DataMovimentacao = transacao.DataHora,
                                       HoraMovimentacao = transacao.DataHora,
                                       PessoaCliente = transacao.PessoaQuePagou,
                                       ApelidoProfissional = comissao.PessoaComissionada.Apelido,
                                       NomeCompletoProfissional = comissao.PessoaComissionada.NomeCompleto,
                                       NomeServico = iv.PacoteCliente.Nome,
                                       UsouPontosDeFidelidade = iv.UsouPontosDeFidelidade,
                                       MotivoDescontos = iv.MotivoDesconto != null && iv.MotivoDesconto.DescontoRefleteNaComissao ? iv.MotivoDesconto.Descricao : null,
                                       DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                       DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                       TotalComissao = v.Valor,
                                       ValorComissaoParaPagar = v.Valor,
                                       ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                       PossuiComissaoParcelada = comissao.ValorBruto != v.ValorBrutoProporcional,
                                       RelatorioComissao = new RelatorioComissao
                                       {
                                           ResponsavelPelaUltimaAlteracao = v.ResponsavelPelaUltimaAlteracaoManualDeValor ?? comissao.Transacao.PessoaQueRealizou,
                                           TotalServicos = v.ValorBrutoProporcional,
                                           TotalServicosNaoParcelado = comissao.ValorBruto,
                                           TotalComissaoNaoParcelado = comissao.ComissaoParaPagar ?? 0,
                                           TotalDescontos = v.DescontoClienteProporcional,
                                           TotalDescontoOperadoras = v.DescontoOperadoraProporcional,
                                           TotalDescartaveis = v.DescontoExtraProporcional,
                                           TotalPercentualComissao = comissao.ValorComissao,
                                           TotalBaseComissao = v.ValorBaseProporcional,
                                           TotalComissao = v.Valor,
                                           Categoria = "Pacotes",
                                           IdTransacao = transacao.Id,
                                           ValorSplit = comissao.EhComissaoComSplit ? comissao.ValorComissao : 0
                                       }
                                   });

            if (incluiDadosNF)
            {
                var idsTransacao = buscaDeValores.Select(c => c.Comissao.Transacao.Id).ToList().Distinct().ChunkBy(10);
                var lotesRPS = Domain.RPS.LoteRPSRepository.Queryable().Where(f => f.PessoaEmitente.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa);
                var temRPS = lotesRPS.Select(f => (int?)f.Id).Take(1).FirstOrDefault() != null;
                if (temRPS)
                {
                    var emissoesRPS = idsTransacao.SelectMany(ids =>
                       Domain.RPS.EmissaoRPSRepository.Queryable().Where(l =>
                           ids.Contains(l.Transacao.Id))
                       .Select(e => new
                       {
                           e.Numero,
                           IdTransacao = e.Transacao.Id,
                           e.DataEmissao,
                           e.DadosRPSTransacao.StatusRPS
                       })
                   );

                    foreach (var e in emissoesRPS)
                    {
                        var comissoesServicoDaTransacao = comissoesServico.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoDaTransacao)
                        {
                            ct.NumeroRPS = e.Numero;
                            ct.DataEmissaoRPS = e.DataEmissao;
                            ct.StatusRPS = e.StatusRPS;
                        }

                        var comissoesServicoAssistenteDaTransacao = comissoesServicoAssistente.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoAssistenteDaTransacao)
                        {
                            ct.NumeroRPS = e.Numero;
                            ct.DataEmissaoRPS = e.DataEmissao;
                            ct.StatusRPS = e.StatusRPS;
                        }

                        var comissoesProdutoDaTransacao = comissoesProduto.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesProdutoDaTransacao)
                        {
                            ct.NumeroRPS = e.Numero;
                            ct.DataEmissaoRPS = e.DataEmissao;
                            ct.StatusRPS = e.StatusRPS;
                        }
                    }
                }

                var emissoesNFCEstabelecimento = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == parametros.Estabelecimento.IdEstabelecimento);
                var temNFC = emissoesNFCEstabelecimento.Select(f => (int?)f.IdNotaNFC).Take(1).FirstOrDefault() != null;
                if (temNFC)
                {
                    var emissoesNFC = idsTransacao.SelectMany(ids =>
                        emissoesNFCEstabelecimento.Where(l =>
                            ids.Contains(l.Transacao.Id))
                        .Select(e => new
                        {
                            e.NumeroNota,
                            IdTransacao = e.Transacao.Id,
                            e.DataEmissao,
                            e.StatusNota.Status
                        })
                    );

                    foreach (var e in emissoesNFC)
                    {
                        var comissoesServicoDaTransacao = comissoesServico.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoDaTransacao)
                        {
                            ct.NumeroNFC = e.NumeroNota;
                            ct.DataEmissaoNFC = e.DataEmissao;
                            ct.StatusNFC = e.Status;
                        }

                        var comissoesServicoAssistenteDaTransacao = comissoesServicoAssistente.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoAssistenteDaTransacao)
                        {
                            ct.NumeroNFC = e.NumeroNota;
                            ct.DataEmissaoNFC = e.DataEmissao;
                            ct.StatusNFC = e.Status;
                        }

                        var comissoesProdutoDaTransacao = comissoesProduto.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesProdutoDaTransacao)
                        {
                            ct.NumeroNFC = e.NumeroNota;
                            ct.DataEmissaoNFC = e.DataEmissao;
                            ct.StatusNFC = e.Status;
                        }
                    }
                }
            }

            var retorno = new List<RelatorioComissaoValorPorParcelaDTO>();

            retorno.AddRange(comissoesServico);
            retorno.AddRange(comissoesServicoAssistente);
            retorno.AddRange(comissoesProduto);
            retorno.AddRange(comissoesPacote);

            var exibirColunasDeId = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.ExibirColunasIdNosRelatorios);

            if (exibirColunasDeId)
                retorno = PreencherInformacoesDeIdsProfissionaisEClientes(retorno, idEstabelecimentoAutenticado);

            return retorno.OrderBy(f => f.DataMovimentacao).ToList();
        }

        private List<RelatorioComissaoValorPorParcelaDTO> ListarComissoesDesagrupados(ParametrosFiltrosRelatorio parametros, bool incluiDadosNF)
        {
            var idEstabelecimentoAutenticado = parametros.Estabelecimento.IdEstabelecimento;
            var estabelecimento = parametros.Estabelecimento;
            var comissoes = Domain.Financeiro.ComissaoRepository.Listar(parametros);
            var valoresAPagar = Domain.Financeiro.ValorDeComissaoAReceberRepository.Queryable();

            var horarioTransacao = Domain.Pessoas.HorarioTransacaoRepository.Queryable();

            var comissoesServico = (from c in comissoes
                                    join v in valoresAPagar on c equals v.Comissao
                                    join ht2 in horarioTransacao on c equals ht2.Comissao
                                    let transacao = c.Transacao
                                    let horario = ht2.Horario
                                    select new RelatorioComissaoValorPorParcelaDTO
                                    {
                                        IdTransacao = transacao.Id,
                                        IdComissao = c.Id,
                                        IdValorDeComissaoAReceber = v.Id,
                                        EhConsumoPacote = ht2.ItemPacoteCliente != null,
                                        EhConsumoPacoteNaRede = ht2.ItemPacoteCliente != null && ht2.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != ht2.Horario.Estabelecimento.PessoaJuridica.IdPessoa,
                                        ComissaoFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                        ComissaoAReceberFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                        DataComissaoAReceberFoiAlteradaManualmente = c.DataHoraUltimaAlteracaoManualComissao != null,
                                        PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                        EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                        EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                        DataHoraUltimaAlteracaoManualComissao = c.DataHoraUltimaAlteracaoManualComissao,
                                        DataHoraUltimaAlteracaoManualDataComissaoAReceber = c.DataHoraUltimaAlteracaoManualComissao,
                                        ResponsavelPelaUltimaAlteracaoDaDataAReceber = c.ResponsavelPelaUltimaAlteracaoDeComissao,
                                        DataHorario = horario.DataInicio,
                                        DataMovimentacao = transacao.DataHora,
                                        HoraMovimentacao = transacao.DataHora,
                                        PessoaCliente = transacao.PessoaQuePagou,
                                        ApelidoProfissional = c.PessoaComissionada.Apelido,
                                        NomeCompletoProfissional = c.PessoaComissionada.NomeCompleto,
                                        ApelidoProfissionalAssistente = ht2.ComissaoAssistente.PessoaComissionada.Apelido,
                                        NomeCompletoProfissionalAssistente = ht2.ComissaoAssistente.PessoaComissionada.NomeCompleto,
                                        NomeServico = horario.ServicoEstabelecimento.Nome,
                                        UsouPontosDeFidelidade = ht2.UsouPontosDeFidelidade,
                                        MotivoDescontos = ht2.MotivoDesconto != null && ht2.MotivoDesconto.DescontoRefleteNaComissao ? ht2.MotivoDesconto.Descricao : null,
                                        DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                        DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                        TotalComissao = c.ComissaoParaPagar,
                                        ValorComissaoParaPagar = v.Valor,
                                        ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                        PossuiComissaoParcelada = v.Comissao.ValorBruto != v.ValorBrutoProporcional,
                                        RelatorioComissao = new RelatorioComissao
                                        {
                                            ResponsavelPelaUltimaAlteracao = c.ResponsavelPelaUltimaAlteracaoDeComissao ?? c.Transacao.PessoaQueRealizou,
                                            TotalServicos = c.ValorBruto,
                                            TotalDescontos = c.DescontoCliente,
                                            TotalDescontoOperadoras = c.DescontoOperadora,
                                            TotalServicosNaoParcelado = c.ValorBruto,
                                            TotalComissaoNaoParcelado = c.ComissaoParaPagar ?? 0,
                                            TotalDescartaveis = v.DescontoExtraProporcional,
                                            TotalPercentualComissao = c.ValorComissao,
                                            TotalBaseComissao = c.ValorBase,
                                            TotalComissao = c.ComissaoParaPagar,
                                            Categoria = horario.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Descricao,
                                            IdTransacao = transacao.Id,
                                            ValorSplit = c.EhComissaoComSplit ? c.ValorComissao : 0
                                        }
                                    }).ToList();

            var comissoesServicoAssistente = (from c in comissoes
                                              join v in valoresAPagar on c equals v.Comissao
                                              join ht3 in horarioTransacao on c equals ht3.ComissaoAssistente
                                              let transacao = c.Transacao
                                              let horario = ht3.Horario
                                              select new RelatorioComissaoValorPorParcelaDTO
                                              {
                                                  IdTransacao = transacao.Id,
                                                  IdComissao = c.Id,
                                                  IdValorDeComissaoAReceber = v.Id,
                                                  EhConsumoPacote = ht3.ItemPacoteCliente != null,
                                                  EhConsumoPacoteNaRede = ht3.ItemPacoteCliente != null && ht3.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != ht3.Horario.Estabelecimento.PessoaJuridica.IdPessoa,
                                                  ComissaoFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                                  ComissaoAReceberFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                                  DataComissaoAReceberFoiAlteradaManualmente = c.DataHoraUltimaAlteracaoManualComissao != null,
                                                  PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                                  EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                                  EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                                  DataHoraUltimaAlteracaoManualComissao = c.DataHoraUltimaAlteracaoManualComissao,
                                                  DataHoraUltimaAlteracaoManualDataComissaoAReceber = c.DataHoraUltimaAlteracaoManualComissao,
                                                  ResponsavelPelaUltimaAlteracaoDaDataAReceber = c.ResponsavelPelaUltimaAlteracaoDeComissao,
                                                  DataHorario = horario.DataInicio,
                                                  DataMovimentacao = transacao.DataHora,
                                                  HoraMovimentacao = transacao.DataHora,
                                                  PessoaCliente = transacao.PessoaQuePagou,
                                                  ApelidoProfissional = horario.Profissional.PessoaFisica.Apelido,
                                                  NomeCompletoProfissional = horario.Profissional.PessoaFisica.NomeCompleto,
                                                  ApelidoProfissionalAssistente = c.PessoaComissionada.Apelido,
                                                  NomeCompletoProfissionalAssistente = c.PessoaComissionada.NomeCompleto,
                                                  NomeServico = horario.ServicoEstabelecimento.Nome,
                                                  UsouPontosDeFidelidade = ht3.UsouPontosDeFidelidade,
                                                  MotivoDescontos = ht3.MotivoDesconto != null && ht3.MotivoDesconto.DescontoRefleteNaComissao ? ht3.MotivoDesconto.Descricao : null,
                                                  DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                                  DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                                  TotalComissao = c.ComissaoParaPagar,
                                                  ValorComissaoParaPagar = v.Valor,
                                                  ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                                  PossuiComissaoParcelada = v.Comissao.ValorBruto != v.ValorBrutoProporcional,
                                                  RelatorioComissao = new RelatorioComissao
                                                  {
                                                      ResponsavelPelaUltimaAlteracao = c.ResponsavelPelaUltimaAlteracaoDeComissao ?? c.Transacao.PessoaQueRealizou,
                                                      TotalServicos = c.ValorBruto,
                                                      TotalDescontos = c.DescontoCliente,
                                                      TotalDescontoOperadoras = c.DescontoOperadora,
                                                      TotalServicosNaoParcelado = c.ValorBruto,
                                                      TotalComissaoNaoParcelado = c.ComissaoParaPagar ?? 0,
                                                      TotalDescartaveis = v.DescontoExtraProporcional,
                                                      TotalPercentualComissao = c.ValorComissao,
                                                      TotalBaseComissao = c.ValorBase,
                                                      TotalComissao = c.ComissaoParaPagar,
                                                      Categoria = horario.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Descricao,
                                                      IdTransacao = transacao.Id,
                                                      ValorSplit = c.EhComissaoComSplit ? c.ValorComissao : 0
                                                  },
                                                  EhAssistenteComissao = true
                                              }).ToList();

            var itemVendaProduto = Domain.Vendas.ItemVendaProdutoRepository.Queryable();
            var comissoesProduto = (from c in comissoes
                                    join v in valoresAPagar on c equals v.Comissao
                                    join iv in itemVendaProduto on c equals iv.Comissao
                                    let transacao = iv.Venda.Transacao
                                    where c.TipoOrigemComissao == TipoOrigemComissaoEnum.Produto && v.Ativo
                                    select new RelatorioComissaoValorPorParcelaDTO
                                    {
                                        IdTransacao = transacao.Id,
                                        IdComissao = c.Id,
                                        IdValorDeComissaoAReceber = c.ValoresAReceber.Min(f => f.Id),
                                        EhConsumoPacote = iv.ItemPacoteCliente != null,
                                        EhConsumoPacoteNaRede = iv.ItemPacoteCliente != null && iv.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != c.IdPessoaEstabelecimento,
                                        ComissaoFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                        ComissaoAReceberFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                        DataComissaoAReceberFoiAlteradaManualmente = c.DataHoraUltimaAlteracaoManualComissao != null,
                                        PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                        EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                        EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                        DataHoraUltimaAlteracaoManualComissao = c.DataHoraUltimaAlteracaoManualComissao,
                                        DataHoraUltimaAlteracaoManualDataComissaoAReceber = c.DataHoraUltimaAlteracaoManualComissao,
                                        ResponsavelPelaUltimaAlteracaoDaDataAReceber = c.ResponsavelPelaUltimaAlteracaoDeComissao,
                                        DataHorario = iv.Venda.Data,
                                        DataMovimentacao = transacao.DataHora,
                                        HoraMovimentacao = transacao.DataHora,
                                        PessoaCliente = transacao.PessoaQuePagou,
                                        ApelidoProfissional = c.PessoaComissionada.Apelido,
                                        NomeCompletoProfissional = c.PessoaComissionada.NomeCompleto,
                                        NomeServico = iv.EstabelecimentoProduto.Descricao,
                                        UsouPontosDeFidelidade = iv.UsouPontosDeFidelidade,
                                        MotivoDescontos = iv.MotivoDesconto != null && iv.MotivoDesconto.DescontoRefleteNaComissao ? iv.MotivoDesconto.Descricao : null,
                                        DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                        DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                        TotalComissao = c.ComissaoParaPagar,
                                        ValorComissaoParaPagar = v.Valor,
                                        ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                        PossuiComissaoParcelada = v.Comissao.ValorBruto != v.ValorBrutoProporcional,
                                        RelatorioComissao = new RelatorioComissao
                                        {
                                            ResponsavelPelaUltimaAlteracao = c.ResponsavelPelaUltimaAlteracaoDeComissao ?? c.Transacao.PessoaQueRealizou,
                                            TotalServicos = c.ValorBruto,
                                            TotalDescontos = c.DescontoCliente,
                                            TotalDescontoOperadoras = c.DescontoOperadora,
                                            TotalServicosNaoParcelado = c.ValorBruto,
                                            TotalComissaoNaoParcelado = c.ComissaoParaPagar ?? 0,
                                            TotalDescartaveis = v.DescontoExtraProporcional,
                                            TotalPercentualComissao = c.ValorComissao,
                                            TotalBaseComissao = c.ValorBase,
                                            TotalComissao = c.ComissaoParaPagar,
                                            Categoria = iv.EstabelecimentoProduto.EstabelecimentoProdutoCategoria.Nome,
                                            IdTransacao = transacao.Id,
                                            ValorSplit = c.EhComissaoComSplit ? c.ValorComissao : 0
                                        }
                                    });

            var itemVendaPacote = Domain.Vendas.ItemVendaPacoteRepository.Queryable();
            var comissoesPacote = (from c in comissoes
                                   join v in valoresAPagar on c equals v.Comissao
                                   join iv in itemVendaPacote on c equals iv.Comissao
                                   let transacao = iv.Venda.Transacao
                                   where c.TipoOrigemComissao == TipoOrigemComissaoEnum.Pacote && v.Ativo
                                   select new RelatorioComissaoValorPorParcelaDTO
                                   {
                                       IdTransacao = transacao.Id,
                                       IdComissao = c.Id,
                                       IdValorDeComissaoAReceber = c.ValoresAReceber.Min(f => f.Id),
                                       EhConsumoPacote = iv.ItemPacoteCliente != null,
                                       EhConsumoPacoteNaRede = iv.ItemPacoteCliente != null && iv.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != c.IdPessoaEstabelecimento,
                                       ComissaoFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                       ComissaoAReceberFoiAlteradaManualmente = c.ComissaoAlteradaManualmente,
                                       DataComissaoAReceberFoiAlteradaManualmente = c.DataHoraUltimaAlteracaoManualComissao != null,
                                       PermiteAlteracaoDaComissao = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && !transacao.IdTransacaoQueEstounouEsta.HasValue,
                                       EhEstorno = transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno,
                                       EhPagamentoEstornado = transacao.IdTransacaoQueEstounouEsta.HasValue,
                                       DataHoraUltimaAlteracaoManualComissao = c.DataHoraUltimaAlteracaoManualComissao,
                                       DataHoraUltimaAlteracaoManualDataComissaoAReceber = c.DataHoraUltimaAlteracaoManualComissao,
                                       ResponsavelPelaUltimaAlteracaoDaDataAReceber = c.ResponsavelPelaUltimaAlteracaoDeComissao,
                                       DataHorario = iv.Venda.Data,
                                       DataMovimentacao = transacao.DataHora,
                                       HoraMovimentacao = transacao.DataHora,
                                       PessoaCliente = transacao.PessoaQuePagou,
                                       ApelidoProfissional = c.PessoaComissionada.Apelido,
                                       NomeCompletoProfissional = c.PessoaComissionada.NomeCompleto,
                                       NomeServico = iv.PacoteCliente.Nome,
                                       UsouPontosDeFidelidade = iv.UsouPontosDeFidelidade,
                                       MotivoDescontos = iv.MotivoDesconto != null && iv.MotivoDesconto.DescontoRefleteNaComissao ? iv.MotivoDesconto.Descricao : null,
                                       DataAPagarOProfissional = v.DataDaComissaoAReceber,
                                       DataAPagarOProfissionalOriginal = v.DataDaComissaoAReceberOriginal,
                                       TotalComissao = c.ComissaoParaPagar,
                                       ValorComissaoParaPagar = v.Valor,
                                       ValorComissaoParaPagarOriginal = v.ValorOriginal,
                                       PossuiComissaoParcelada = v.Comissao.ValorBruto != v.ValorBrutoProporcional,
                                       RelatorioComissao = new RelatorioComissao
                                       {
                                           ResponsavelPelaUltimaAlteracao = c.ResponsavelPelaUltimaAlteracaoDeComissao ?? c.Transacao.PessoaQueRealizou,
                                           TotalServicos = c.ValorBruto,
                                           TotalDescontos = c.DescontoCliente,
                                           TotalDescontoOperadoras = c.DescontoOperadora,
                                           TotalServicosNaoParcelado = c.ValorBruto,
                                           TotalComissaoNaoParcelado = c.ComissaoParaPagar ?? 0,
                                           TotalDescartaveis = v.DescontoExtraProporcional,
                                           TotalPercentualComissao = c.ValorComissao,
                                           TotalBaseComissao = c.ValorBase,
                                           TotalComissao = c.ComissaoParaPagar,
                                           Categoria = "Pacotes",
                                           IdTransacao = transacao.Id,
                                           ValorSplit = c.EhComissaoComSplit ? c.ValorComissao : 0
                                       }
                                   });

            if (incluiDadosNF)
            {
                var idsTransacao = comissoes.Select(c => c.Transacao.Id).ToList().Distinct().ChunkBy(10);
                var lotesRPS = Domain.RPS.LoteRPSRepository.Queryable().Where(f => f.PessoaEmitente.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa);
                var temRPS = lotesRPS.Select(f => (int?)f.Id).Take(1).FirstOrDefault() != null;
                if (temRPS)
                {
                    var emissoesRPS = idsTransacao.SelectMany(ids =>
                        Domain.RPS.EmissaoRPSRepository.Queryable().Where(l =>
                            ids.Contains(l.Transacao.Id))
                        .Select(e => new
                        {
                            e.Numero,
                            IdTransacao = e.Transacao.Id,
                            e.DataEmissao,
                            e.DadosRPSTransacao.StatusRPS
                        })
                    );

                    foreach (var e in emissoesRPS)
                    {
                        var comissoesServicoDaTransacao = comissoesServico.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoDaTransacao)
                        {
                            ct.NumeroRPS = e.Numero;
                            ct.DataEmissaoRPS = e.DataEmissao;
                            ct.StatusRPS = e.StatusRPS;
                        }

                        var comissoesServicoAssistenteDaTransacao = comissoesServicoAssistente.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoAssistenteDaTransacao)
                        {
                            ct.NumeroRPS = e.Numero;
                            ct.DataEmissaoRPS = e.DataEmissao;
                            ct.StatusRPS = e.StatusRPS;
                        }

                        var comissoesProdutoDaTransacao = comissoesProduto.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesProdutoDaTransacao)
                        {
                            ct.NumeroRPS = e.Numero;
                            ct.DataEmissaoRPS = e.DataEmissao;
                            ct.StatusRPS = e.StatusRPS;
                        }
                    }
                }

                var emissoesNFCEstabelecimento = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == parametros.Estabelecimento.IdEstabelecimento);
                var temNFC = emissoesNFCEstabelecimento.Select(f => (int?)f.IdNotaNFC).Take(1).FirstOrDefault() != null;
                if (temNFC)
                {
                    var emissoesNFC = idsTransacao.SelectMany(ids =>
                        emissoesNFCEstabelecimento.Where(l =>
                            ids.Contains(l.Transacao.Id))
                        .Select(e => new
                        {
                            e.NumeroNota,
                            IdTransacao = e.Transacao.Id,
                            e.DataEmissao,
                            e.StatusNota.Status
                        })
                    );

                    foreach (var e in emissoesNFC)
                    {
                        var comissoesServicoDaTransacao = comissoesServico.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoDaTransacao)
                        {
                            ct.NumeroNFC = e.NumeroNota;
                            ct.DataEmissaoNFC = e.DataEmissao;
                            ct.StatusNFC = e.Status;
                        }

                        var comissoesServicoAssistenteDaTransacao = comissoesServicoAssistente.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesServicoAssistenteDaTransacao)
                        {
                            ct.NumeroNFC = e.NumeroNota;
                            ct.DataEmissaoNFC = e.DataEmissao;
                            ct.StatusNFC = e.Status;
                        }

                        var comissoesProdutoDaTransacao = comissoesProduto.Where(c => c.IdTransacao == e.IdTransacao);
                        foreach (var ct in comissoesProdutoDaTransacao)
                        {
                            ct.NumeroNFC = e.NumeroNota;
                            ct.DataEmissaoNFC = e.DataEmissao;
                            ct.StatusNFC = e.Status;
                        }
                    }
                }
            }

            var retorno = new List<RelatorioComissaoValorPorParcelaDTO>();


            retorno.AddRange(comissoesServico);
            retorno.AddRange(comissoesServicoAssistente);
            retorno.AddRange(comissoesProduto);
            retorno.AddRange(comissoesPacote);

            var exibirColunasDeId = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.ExibirColunasIdNosRelatorios);

            if (exibirColunasDeId)
                retorno = PreencherInformacoesDeIdsProfissionaisEClientes(retorno, idEstabelecimentoAutenticado);

            return retorno.OrderBy(f => f.DataMovimentacao).ToList();
        }

        #endregion Sem agrupamento

        public bool DesconsiderarAssistenteNosTotais(ParametrosFiltrosRelatorio parametros)
        {
            return parametros.IdEstabelecimentoProfissional == 0;
        }

        public decimal? ObterTotalDeDescontosDoProfissionalEValeNoPeriodo(DateTime dataInicial, DateTime dataFinal, int idProfissionalComissionado, int idPessoaEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(idPessoaEstabelecimento);
            var profissional = Domain.Pessoas.ProfissionalRepository.Load(idProfissionalComissionado);

            decimal? vale = ObterValorVale(dataInicial, dataFinal, estabelecimento.IdEstabelecimento, profissional.PessoaFisica.IdPessoa);

            // OBS: Foi adicionado o dia anterior a data inicial (.AddDays(-1)) no código abaixo pois a consulta que é feita, pega o dia seguinte a data inicial, mas
            // ao mesmo tempo essa consulta é utilizada em outros lugares além do PAT. Então por isso o ajuste da data inicial está aqui e não na consulta em si,
            // por conta do impacto em outras partes do código.
            decimal? valorDescontoProfissional = ObterValorDescontoProfissionalNoPeriodo(dataInicial.AddDays(-1), dataFinal, idPessoaEstabelecimento, profissional.PessoaFisica.IdPessoa);

            return (vale ?? 0) + (valorDescontoProfissional ?? 0);
        }

        private decimal? ObterValorVale(DateTime dataInicial, DateTime dataFinal, int idEstabelecimento, int idPessoaDoProfissional)
        {
            return Domain.Despesas.LancamentoRepository.Queryable()
                            .Where(l =>
                                l.Ativo
                                && l.LancamentoCategoria.LancamentoCategoriaPadrao == Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.ValeProfissional)
                                && l.DataPagamento >= dataInicial && l.DataPagamento < dataFinal
                                && l.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaDoProfissional
                                && l.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                            .Sum(l => (decimal?)(l.Valor));
        }

        private decimal? ObterValorDescontoProfissionalNoPeriodo(DateTime dataInicial, DateTime dataFinal, int idPessoaEstabelecimento, int idPessoaDoProfissional)
        {
            var filtro = new FiltroDescontoCompraProdutoDTO(idPessoaEstabelecimento, idPessoaDoProfissional, dataInicial, dataFinal);
            var valorDescontoProfissionalCompraProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ObterValorDescontoProfissionalVendaProdutoNoPeriodo(filtro);

            return valorDescontoProfissionalCompraProduto;
        }

        public decimal? ObterTotalDeDescontosProfissionalNoPeriodo(DateTime dataInicial, DateTime dataFinal, int idPessoaDoProfissional, int idPessoaEstabelecimento)
        {
            var valorDescontoProfissional = ObterValorDescontoProfissionalNoPeriodo(dataInicial, dataFinal, idPessoaEstabelecimento, idPessoaDoProfissional);
            return valorDescontoProfissional;
        }

        public List<ComissaoDoFechamentoPorProfissionaDTO> ObterComissaoDoFechamento(List<ValorDeComissaoAReceber> listaDeValores)
        {

            var retorno = listaDeValores.GroupBy(vc => vc.Comissao)
                .Select(c => new ComissaoDoFechamentoPorProfissionaDTO()
                {
                    ValorTotalComissao = c.Key.ComissaoParaPagar.ValorDecimal(),
                    ValorTotalServicoOuVenda = (c.Key.ValorBruto - c.Key.DescontoCliente).ValorDecimal(),
                    NomeProfissional = c.Key.PessoaComissionada.NomeOuApelido(),
                    ItensComissoes = c.Select(vc => new ItemComissaoDoFechamentoPorProfissionaDTO(vc)).ToList(),
                    NomeServicoOuVenda = ObterNomeNomeServicoOuVenda(c.Key)
                }).ToList();

            return retorno;
        }

        private string ObterNomeNomeServicoOuVenda(Comissao comissao)
        {

            if ((int)comissao.TipoOrigemComissao == (int)TipoOrigemComissaoEnum.Produto)
            {
                var ivp = Domain.Vendas.ItemVendaProdutoRepository.Queryable()
                    .Where(f => f.Comissao.Id == comissao.Id)
                    .Select(f => new
                    {
                        Descricao = f.Quantidade + "x " + f.EstabelecimentoProduto.Descricao,
                    })
                    .FirstOrDefault();

                return ivp.Descricao;

            }
            else if ((int)comissao.TipoOrigemComissao == (int)TipoOrigemComissaoEnum.Servico)
            {
                var ht = Domain.Pessoas.HorarioTransacaoRepository.Queryable()
                    .Where(f => f.Transacao.Id == comissao.Transacao.Id &&
                            (f.Comissao.Id == comissao.Id || f.ComissaoAssistente.Id == comissao.Id))
                    .Select(f => new
                    {
                        f.Horario.ServicoEstabelecimento.Nome,
                    })
                    .FirstOrDefault();

                return ht.Nome;

            }
            else if ((int)comissao.TipoOrigemComissao == (int)TipoOrigemComissaoEnum.Pacote)
            {
                var ivp = Domain.Vendas.ItemVendaPacoteRepository.Queryable()
                    .Where(f => f.Comissao.Id == comissao.Id)
                    .Select(f => new
                    {
                        f.PacoteCliente.Nome
                    })
                    .FirstOrDefault();

                return ivp.Nome;
            }

            return String.Empty;
        }

        private static List<RelatorioComissaoValorPorParcelaDTO> PreencherInformacoesDeIdsProfissionaisEClientes(List<RelatorioComissaoValorPorParcelaDTO> relatorios, int idEstabelecimento)
        {
            var transacoes = ObterTransacoes(relatorios);
            var idsPessoaPagas = ObterIdsPessoasQuePagaram(transacoes);
            var estabelecimentosClienteDict = ObterDicionarioClientes(idsPessoaPagas, idEstabelecimento);

            var comissoes = ObterComissoes(relatorios);
            var idPessoasComissionadas = ObterIdsPessoasComissionadas(comissoes);
            var estabelecimentosProfissionalDict = ObterDicionarioProfissionais(idPessoasComissionadas, idEstabelecimento);

            var transacoesDict = transacoes.ToDictionary(t => t.Id, t => t.PessoaQuePagou?.IdPessoa ?? 0);
            var comissoesDict = comissoes.ToDictionary(c => c.Id, c => c.PessoaComissionada?.IdPessoa ?? 0);

            return PreencherIdEstabelecimentoClienteEIDEstabelecimentoProfissional(relatorios, transacoesDict, estabelecimentosClienteDict, comissoesDict, estabelecimentosProfissionalDict);
        }

        private static List<Transacao> ObterTransacoes(List<RelatorioComissaoValorPorParcelaDTO> relatorios)
        {
            var transacoesIds = relatorios
                .Where(r => r.IdTransacao.HasValue)
                .Select(r => r.IdTransacao.Value)
                .Distinct()
                .ToList();

            return Domain.Financeiro.TransacaoRepository.ListarPorIds(transacoesIds);
        }

        private static List<int> ObterIdsPessoasQuePagaram(List<Transacao> transacoes)
        {
            return transacoes
                .Where(t => t.PessoaQuePagou?.IdPessoa != null)
                .Select(t => t.PessoaQuePagou.IdPessoa)
                .Distinct()
                .ToList();
        }


        private static Dictionary<int, int> ObterDicionarioClientes(List<int> idsPessoaPagas, int idEstabelecimento)
        {
            var clientes = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPelaPessoa(idsPessoaPagas, idEstabelecimento);
            return clientes.GroupBy(c => c.Cliente.PessoaFisica.IdPessoa).ToDictionary(g => g.Key, g => g.First().Codigo);

        }

        private static List<Comissao> ObterComissoes(List<RelatorioComissaoValorPorParcelaDTO> relatorios)
        {
            var comissoesIds = relatorios
                .Where(r => r.IdComissao != 0)
                .Select(r => r.IdComissao)
                .Distinct()
                .ToList();

            return Domain.Financeiro.ComissaoRepository.ListarPorIds(comissoesIds);
        }

        private static List<int> ObterIdsPessoasComissionadas(List<Comissao> comissoes)
        {
            return comissoes
                .Where(c => c.PessoaComissionada?.IdPessoa != null)
                .Select(c => c.PessoaComissionada.IdPessoa)
                .Distinct()
                .ToList();
        }

        private static Dictionary<int, int> ObterDicionarioProfissionais(List<int> idPessoasComissionadas, int idEstabelecimento)
        {
            var profissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterProfissionaisPelasComissoes(idPessoasComissionadas, idEstabelecimento);
            return profissionais.GroupBy(p => p.Profissional.PessoaFisica.IdPessoa).ToDictionary(g => g.Key, g => g.First().Codigo);

        }

        private static List<RelatorioComissaoValorPorParcelaDTO> PreencherIdEstabelecimentoClienteEIDEstabelecimentoProfissional(
            List<RelatorioComissaoValorPorParcelaDTO> relatorios,
            Dictionary<int, int> transacoesDict,
            Dictionary<int, int> estabelecimentosClienteDict,
            Dictionary<int, int> comissoesDict,
            Dictionary<int, int> estabelecimentosProfissionalDict)
        {
            foreach (var relatorio in relatorios)
            {
                if (relatorio.IdTransacao.HasValue &&
                    transacoesDict.TryGetValue(relatorio.IdTransacao.Value, out var idPessoa) &&
                    estabelecimentosClienteDict.TryGetValue(idPessoa, out var codigoCliente))
                {
                    relatorio.IdEstabelecimentoCliente = codigoCliente;
                }

                if (relatorio.IdComissao != 0 &&
                    comissoesDict.TryGetValue(relatorio.IdComissao, out var idPessoaComissionada) &&
                    estabelecimentosProfissionalDict.TryGetValue(idPessoaComissionada, out var codigoProfissional))
                {
                    relatorio.IdEstabelecimentoProfissional = codigoProfissional;
                }
            }

            return relatorios;
        }
    }
}
