﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro.Calculos;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Services
{
    public class DetalhamentoDePagamentosAoProfissionalService : BaseService, IDetalhamentoDePagamentosAoProfissionalService
    {
        public DetalhesDePagamentoDoProfissionalDTO ObterDetalhesDePagamentoDoProfissional(DetalhesDePagamentoDoProfissionalFiltroDTO filtro)
        {
            var itens = new List<ItemPagamentoProfissionalDTO>();

            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .ObterPorProfissionalEEstabelecimento(filtro.IdProfissional, filtro.Estabelecimento.IdEstabelecimento);

            var idPessoaProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa;
            var idPessoaEstabelecimento = filtro.Estabelecimento.PessoaJuridica.IdPessoa;
            var idEstabelecimento = filtro.Estabelecimento.IdEstabelecimento;


            var gorjetasPagas = ObterValorGorjetasPagas(filtro, idEstabelecimento);

            var gorjetasAbertas = ObterValorGorjetasAbertas(filtro, idEstabelecimento);

            var dtoFiltroComissao = new FiltroComissaoComDescontos(filtro.DataInicio, filtro.DataFim, idEstabelecimento, idPessoaProfissional, idPessoaEstabelecimento);
            var comissoesAbertasComDescontos = ObterValorComissoesAbertasComDescontos(dtoFiltroComissao);
            var comissoesPagasComDescontos = ObterValorComissoesPagasComDescontos(dtoFiltroComissao);

            itens.Add(new ItemPagamentoProfissionalDTO { Tipo = ItensAPagarEnum.Comissao.ToString(), Valor = comissoesAbertasComDescontos, ValorPago = comissoesPagasComDescontos });
            itens.Add(new ItemPagamentoProfissionalDTO { Tipo = ItensAPagarEnum.Gorjeta.ToString(), Valor = gorjetasAbertas, ValorPago = gorjetasPagas });

            var dto = new DetalhesDePagamentoDoProfissionalDTO
            {
                IdProfissional = filtro.IdProfissional,
                NomeProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                TotalAPagar = itens.Sum(i => i.ValorPago + i.Valor),
                TotalPago = itens.Sum(i => i.ValorPago),
                TotalRestante = itens.Sum(i => i.Valor),
                Itens = itens
            };

            return dto;
        }

        public decimal ObterValorComissaoAPagar(FiltroComissaoComDescontos filtro)
        {
            var comissoesAbertasComDescontos = ObterValorComissoesAbertasComDescontos(filtro);

            return comissoesAbertasComDescontos;
        }

        private decimal ObterValorGorjetasPagas(DetalhesDePagamentoDoProfissionalFiltroDTO filtro, int idEstabelecimento)
        {
            var valorGorjetasPagas = Domain.Financeiro.GorjetaRepository
                .ObterQueryGorjetasPagas(idEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdProfissional).Sum(p => (decimal?)p.Valor);

            return valorGorjetasPagas ?? 0;
        }

        private decimal ObterValorGorjetasAbertas(DetalhesDePagamentoDoProfissionalFiltroDTO filtro, int idEstabelecimento)
        {
            var valorGorjetasAbertas = Domain.Financeiro.GorjetaRepository
                .ObterQueryGorjetasAbertas(idEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdProfissional).Sum(p => (decimal?)p.Valor);

            return valorGorjetasAbertas ?? 0;
        }

        private decimal ObterValorComissoesAbertasComDescontos(FiltroComissaoComDescontos filtro)
        {
            var comissoesAbertas = Domain.Financeiro.ValorDeComissaoAReceberRepository
            .ObterQueryComissoesAbertas(filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var bonificacoesAbertas = Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var valesAbertos = Domain.Despesas.LancamentoRepository
                .ObterQueryValesAbertos(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var vendasProdutoAbertas = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository
                .ObterQueryVendaDeProdutosAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim,
                    filtro.IdPessoaEstabelecimento, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var splitsAbertas = Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);


            var comissoesAbertasComDescontos = CalculoValorComissaoComLancamentos.ObterValorComissaoComDespesas(comissoesAbertas, bonificacoesAbertas, valesAbertos, vendasProdutoAbertas, splitsAbertas);

            return comissoesAbertasComDescontos;
        }

        private decimal ObterValorComissoesPagasComDescontos(FiltroComissaoComDescontos filtro)
        {
            var comissoesPagas = Domain.Financeiro.ValorDeComissaoAReceberRepository
                .ObterQueryComissoesPagas(filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var bonificacoesPagas = Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var valesPagos = Domain.Despesas.LancamentoRepository
                .ObterQueryValesPagos(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var vendasProdutoPagas = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository
                .ObterQueryVendaDeProdutosPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim,
                    filtro.IdPessoaEstabelecimento, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var splitsPagas = Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaProfissional).Sum(p => (decimal?)p.Valor);

            var comissoesPagasComDescontos = CalculoValorComissaoComLancamentos.ObterValorComissaoComDespesas(comissoesPagas, bonificacoesPagas, valesPagos, vendasProdutoPagas, splitsPagas);

            return comissoesPagasComDescontos;
        }

    }
}
