﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.Financeiro.DTO.POS;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Financeiro.Services
{

    public class TransacaoPOSService : BaseService, ITransacaoPOSService
    {

        public void IncluirTransacaoPOS(TransacaoPOS transacaoPOS)
        {
            var estabelecimentoAutenticado = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento ?? 0);

            transacaoPOS.Estabelecimento = estabelecimentoAutenticado;
            transacaoPOS.Data = Calendario.Agora();

            Domain.Financeiro.TransacaoPOSRepository.SaveNew(transacaoPOS);
        }

        public void AssociarComFechamento(Int32 idTransacaoPOS, Int32 idTransacao)
        {
            var transacaoPos = Domain.Financeiro.TransacaoPOSRepository.Load(idTransacaoPOS);

            if (transacaoPos != null && transacaoPos.Transacao == null)
            {
                transacaoPos.Transacao = new Transacao { Id = idTransacao };

                Domain.Financeiro.TransacaoPOSRepository.Update(transacaoPos);
            }
        }

        public ResultadoRegistroPagamentoPOSDTO RegistrarPagamentoPOS(RegistroPagamentoPOSDTO dto)
        {
            var idConta = Domain.WebContext.IdContaAutenticada.Value;
            var pessoa = Domain.Pessoas.ContaRepository.Load(idConta).Pessoa;

            var idEstabelecimento = Domain.WebContext.IdEstabelecimentoAutenticado.Value;
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var venda = Domain.Vendas.VendaRepository.Factory.CreateVenda(dto.DadosCheckout);

            var dadosParaCalculoComissao = Domain.Financeiro.CalculoComissaoService.ColetarDadosTransacao(venda.Transacao);
            var calculos = Domain.Financeiro.CalculoComissaoService.CalcularComissao(dadosParaCalculoComissao);
            Domain.Financeiro.CalculoComissaoService.DefinirComissaoDaTransacao(venda.Transacao, calculos);

            var formaPagamento = Domain.Financeiro.FormaPagamentoRepository.Load(dto.DadosPagamento.IdFormaPagamento);

            var numeroParcelas = dto.DadosPagamento.Parcelas.Count;
            var serialNumberDoTerminalSelecionado = dto.DadosPagamento.TerminalBelezinha?.SerialNumber;
            var pagamento = Domain.Financeiro.TransacaoService
                .RealizarPagamentoPOS(
                    pessoa.PessoaFisica,
                    estabelecimento,
                    venda.Transacao,
                    venda,
                    dto.DadosPagamento.Valor,
                    formaPagamento.Tipo,
                    TipoParcelamentoEnum.SemJuros,
                    numeroParcelas,
                    serialNumberDoTerminalSelecionado);

            if (!pagamento.Sucesso)
                throw new Exception(pagamento.MensagemDeErro);

            var estabelecimentoConfigPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(dto.DadosPagamento.IdPessoaQuePagou);

            var transacaoPosWebhookRequest = new TransacaoPosWebhookRequest
            {
                PessoaJuridica = estabelecimento.PessoaJuridica,
                Cliente = cliente,
                Estabelecimento = estabelecimento,
                ChaveDiariaTransacao = pagamento.NumeroDaComanda,
                CNPJ = estabelecimento.PessoaJuridica.CNPJ,
                ValorFechamento = dto.DadosPagamento.Valor,
                DataCriacao = DateTime.Now,
                FormaPagamentoPos = formaPagamento.Tipo.Id, // 1 (cred) ou 2 (deb)
                ModoParcelamento = 0,
                NumeroParcelas = numeroParcelas,
                CodigoTransacao = pagamento.IdTransacao,
                TipoPos = estabelecimentoConfigPOS.TipoPOS
            };

            if (formaPagamento.Tipo.Id == 1)
                transacaoPosWebhookRequest.ModoPagamentoPos = numeroParcelas == 1 ? 1 : 2; // 1 a vista ou 2 parcelado

            Domain.Financeiro.TransacaoPosWebhookRequestService.IncluirTransacaoPosWebhookRequest(transacaoPosWebhookRequest);

            return new ResultadoRegistroPagamentoPOSDTO
            {
                ReferenciaPagamento = pagamento.NumeroDaComanda,
                IdCliente = cliente.IdCliente
            };
        }

        [TransactionInitRequired]
        public ResultadoConsultaPagamentoPOSDTO ConsultaERegistrarPagamentoPOS(int referenciaPagamento, int idCliente)
        {
            var posWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
               .ObterTransacaoAbertaPosWebhookRequestPorChaveDiariaTransacao(Domain.WebContext.IdEstabelecimentoAutenticado.Value, idCliente, referenciaPagamento);

            if (posWebhookRequest == null) return null;

            if (posWebhookRequest.FoiPago && posWebhookRequest.TransacaoPos == null)
            {
                posWebhookRequest.TransacaoPos = GravarPOS(posWebhookRequest);
                Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(posWebhookRequest);
            }

            return new ResultadoConsultaPagamentoPOSDTO
            {
                IdTransacaoPOS = posWebhookRequest.TransacaoPos?.Id,
                Valor = posWebhookRequest.ValorFechamento,
                FoiPago = posWebhookRequest.FoiPago,
                OperacaoCancelada = posWebhookRequest.OperacaoCancelada
            };
        }

        [TransactionInitRequired]
        public ResultadoCancelarTransacaoPosWebhookRequestDto CancelarTransacaoPosWebhookRequest(
            int referenciaPagamento, int idCliente)
        {
            var posWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoAbertaPosWebhookRequestPorChaveDiariaTransacao(
                    Domain.WebContext.IdEstabelecimentoAutenticado.Value,
                    idCliente,
                    referenciaPagamento);

            if (posWebhookRequest == null) return null;

            posWebhookRequest.OperacaoCancelada = true;
            if (Domain.Financeiro.TransacaoService.CancelarPreTransacaoPOS(posWebhookRequest.CodigoTransacao))
            {
                Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(posWebhookRequest);
            }

            return new ResultadoCancelarTransacaoPosWebhookRequestDto
            {
                OperacaoCancelada = posWebhookRequest.OperacaoCancelada
            };
        }

        [TransactionInitRequired]
        public TransacaoPOS GravarPOS(TransacaoPosWebhookRequest posWebhookRequest)
        {
            // TODO: implementar transacaoPOS.TransacaoPOSEstornada
            // TODO: implementar transacaoPOS.RegistradoPeloTrinks para outros TipoPOS
            // TODO: implementar transacaoPOS.BrandId;
            // TODO: implementar transacaoPOS.CardHolderName;
            // TODO: implementar transacaoPOS.MaskedAccountNumber;
            // TODO: implementar transacaoPOS.TransactionType
            // TODO: implementar transacaoPOS.ExpirationDate
            // TODO: implementar transacaoPOS.IdentificadorEntreAplicacoes

            var tiposPosRegistradosPeloTrinks = new List<SubadquirenteEnum>() { 
                SubadquirenteEnum.StoneSiclos,
                SubadquirenteEnum.ConnectStone, 
                SubadquirenteEnum.ConnectPagarme 
            };

            var idEstabelecimento = Domain.WebContext.IdEstabelecimentoAutenticado.Value;
            var transacaoPOS = new TransacaoPOS
            {
                Estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento),
                TipoPOS = posWebhookRequest.TipoPos,
                Valor = posWebhookRequest.ValorFechamento,
                CodigoAutorizacaoTransacaoPDV = posWebhookRequest.CodigoTransacao,

                Transacao = posWebhookRequest.Transacao,
                RegistradoPeloTrinks = tiposPosRegistradosPeloTrinks.Contains((SubadquirenteEnum)posWebhookRequest.TipoPos.Id),

                NomeBandeira = posWebhookRequest.CodigoBandeiraCartao,
                BrandId = 0,
                Data = DateTime.Now,

                AcquirerTransactionKey = posWebhookRequest.StoneTransactionId,
                InitiatorTransactionKey = posWebhookRequest.CodigoTransacao
            };

            Domain.Financeiro.TransacaoPOSRepository.SaveNew(transacaoPOS);

            return transacaoPOS;
        }
    }
}