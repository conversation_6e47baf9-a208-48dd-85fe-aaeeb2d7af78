﻿using Newtonsoft.Json;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.DTO.Connect.Stone;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.Interfaces;
using Perlink.Trinks.NotificacoesApps;
using Perlink.Trinks.NotificacoesApps.Strategies;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web;
using Trinks.Integracoes.Stone.Connect.V2;
using Trinks.Integracoes.Stone.Connect.V2.Dtos;
using Trinks.Integracoes.Stone.Connect.V2.Dtos.Requests;
using Trinks.Integracoes.Stone.Connect.V2.Dtos.Response;
using Trinks.Logging.V2;

namespace Perlink.Trinks.Financeiro.Services
{
    public class IntegracaoConnectStoneService : IIntegracaoConnectStoneService
    {
        private INotificacaoStrategy _notificacaoStrategy;

        public RetornoPagamentoPosDto RealizarPagamentoPos(RealizarPagamentoDto realizarPagamentoDto)
        {
            var connectStoneApi = new ConnectStoneApi(realizarPagamentoDto.EstabelecimentoConfigPos.SecretKey);
            var ordemDto = CriarOrdemDto(realizarPagamentoDto.TransacaoFicticia,
                        realizarPagamentoDto.TipoPagamento,
                        realizarPagamentoDto.NumerosDeParcelas,
                        realizarPagamentoDto.TipoParcelamento,
                        realizarPagamentoDto.ValorAPagar,
                        realizarPagamentoDto.ChaveDiariaTransacao,
            realizarPagamentoDto.Terminal);

            Logger.TrackEvent("[ConnectStone] - Criar pedido", ordemDto);
            var ordemStone = connectStoneApi.OrderService.CreateOrder(ordemDto);
            Logger.TrackEvent("[ConnectStone] - Pedido criado", ordemStone);

            return CriarRetornoPagamentoPosDto(ordemStone, realizarPagamentoDto.ChaveDiariaTransacao);
        }

        public bool CancelarOrdem(string secretKey, string codigoPreTransacao)
        {
            var connectStoneApi = new ConnectStoneApi(secretKey);

            Logger.TrackEvent("[ConnectStone] - Cancelar pedido", codigoPreTransacao);
            var response = connectStoneApi.OrderService.CancelOrder(codigoPreTransacao);
            Logger.TrackEvent("[ConnectStone] - Pedido cancelado", response);

            return response.Success;
        }

        public OrderResponseDto ObterOrdem(string secretKey, string codigoTransacao)
        {
            var connectStoneApi = new ConnectStoneApi(secretKey);

            Logger.TrackEvent("[ConnectStone] - Obter pedido", codigoTransacao);
            var response = connectStoneApi.OrderService.GetOrder(codigoTransacao);
            Logger.TrackEvent("[ConnectStone] - Pedido obtido", codigoTransacao);

            return response.Data;
        }

        public async Task TratarWebhook(WebhookConnectStoneDto webhook)
        {
            if (webhook?.Data?.Order?.Id == null)
                return;

            var transacaoAbertaPos = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(webhook.Data.Order.Id);

            if (transacaoAbertaPos == null)
                return;

            var config =
                Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(
                    transacaoAbertaPos.Estabelecimento.IdEstabelecimento);
            var connectStoneApi = new ConnectStoneApi(config.SecretKey);

            Logger.TrackEvent("[ConnectStone] - Obter cobrança", webhook.Data.Id);
            var cobrancaStone = connectStoneApi.ChargeService.GetCharge(webhook.Data.Id);
            Logger.TrackEvent("[ConnectStone] - Cobrança obtida", cobrancaStone);

            if (!cobrancaStone.Success)
                return;

            var statusUltimaTransacao = cobrancaStone.Data.LastTransaction.Status;
            var status = cobrancaStone.Data.Status;
            switch (status)
            {
                case ChargeStatusEnum.Pending
                    when statusUltimaTransacao == LastTransactionStatus.AuthorizedPendingCapture:
                    AtualizarInformacoesDeTransacaoCapturadaPeloIdOrdem(cobrancaStone.Data);
                    break;
                case ChargeStatusEnum.Canceled:
                    AtualizarInformacoesDeTransacaoCanceladaPeloIdOrdem(cobrancaStone.Data);
                    TratarTransacoesEstornadas(cobrancaStone.Data, webhook.Type);
                    break;
                case ChargeStatusEnum.Paid:
                    transacaoAbertaPos.FoiPago = true;
                    transacaoAbertaPos.Serial = cobrancaStone.Data.Metadata?.TerminalSerialNumber ??
                                                cobrancaStone.Data.LastTransaction.DeviceSerialNumber;
                    transacaoAbertaPos.ExternalTransactionId = cobrancaStone.Data.Id;
                    transacaoAbertaPos.StoneTransactionId = cobrancaStone.Data.Code;
                    transacaoAbertaPos.EhSplit = false;
                    transacaoAbertaPos.NumeroParcelas = cobrancaStone.Data.Metadata != null ? int.Parse(cobrancaStone.Data.Metadata.InstallmentQuantity) : 1;
                    transacaoAbertaPos.CodigoBandeiraCartao = ObterCodigoDaBandeira(cobrancaStone);

                    Logger.TrackEvent("[ConnectStone] - Fechar pedido", webhook.Data.Order.Id);
                    var response = connectStoneApi.OrderService.CloseOrder(webhook.Data.Order.Id);
                    Logger.TrackEvent("[ConnectStone] - Pedido fechado", response);
                    break;
            }
        }

        #region Metodos privados

        private static string ObterCodigoDaBandeira(ResponseDto<ChargeResponseDto> cobrancaStone)
        {
            if (cobrancaStone.Data.Metadata == null)
                return string.Empty;

            return cobrancaStone.Data.Metadata.SchemeName.ToLower() == "americanexpress" ? "Amex" : cobrancaStone.Data.Metadata.SchemeName.ToCapitalizacaoDeTitulos();
        }

        private RetornoPagamentoPosDto CriarRetornoPagamentoPosDto(ResponseDto<OrderResponseDto> responseDto,
            int chaveDiariaTransacao)
        {
            if (responseDto.Success)
            {
                return new RetornoPagamentoPosDto()
                {
                    Sucesso = true,
                    IdTransacao = responseDto.Data.Id,
                    NumeroDaComanda = chaveDiariaTransacao
                };
            }
            return new RetornoPagamentoPosDto()
            {
                Sucesso = false,
                MensagemDeErro = string.Empty
            };
        }

        private CreateOrderDto CriarOrdemDto(Transacao transacao,
            TipoPagamentoEnum tipoPagamento,
            int? numParcelas,
            TipoParcelamentoEnum? tipoDeParcelamento, decimal valorAPagar, int identificador, string terminal)
        {
            return new CreateOrderDto()
            {
                Closed = false,
                Customer = new CustomerRequestDto()
                {
                    Name = transacao.PessoaQuePagou.PrimeiroNome(),
                    Email = transacao.PessoaQuePagou.Email
                },
                Items = CriarItemsDto(transacao, valorAPagar),
                PoiPaymentSettings = CriarPoiPaymentSettingsDto(transacao, tipoPagamento, numParcelas, tipoDeParcelamento, identificador, terminal)
            };
        }

        private PoiPaymentSettingsRequestDto CriarPoiPaymentSettingsDto(Transacao transacao,
            TipoPagamentoEnum tipoPagamento,
            int? numParcelas,
            TipoParcelamentoEnum? tipoDeParcelamento, int identificador, string terminal)
        {
            return new PoiPaymentSettingsRequestDto()
            {
                Visible = true,
                DisplayName = "Fechamento " + identificador,
                PaymentSetup = new PaymentSetupRequestDto()
                {
                    Type = ObterPaymentSetupType(tipoPagamento),
                    Installments = (numParcelas ?? 1) == 0 ? 1 : numParcelas.Value,
                    InstallmentType = tipoDeParcelamento.HasValue && tipoDeParcelamento.Value == TipoParcelamentoEnum.SemJuros ? "merchant" : "issuer",
                },
                PrintOrderReceipt = false,
                DevicesSerialNumber = new List<string>()
                {
                    terminal
                }
            };
        }

        private string ObterPaymentSetupType(TipoPagamentoEnum tipoPagamento)
        {
            switch (tipoPagamento)
            {
                case TipoPagamentoEnum.Credito:
                    return "credit";
                case TipoPagamentoEnum.Debito:
                    return "debit";
                case TipoPagamentoEnum.Voucher:
                    return "voucher";
                case TipoPagamentoEnum.Pix:
                    return "pix";
            }

            throw new NotImplementedException("Tipo de pagamento não implementado no Connect Stone V2");
        }

        private List<ItemRequestDto> CriarItemsDto(Transacao transacao, decimal valorAPagar)
        {
            return new List<ItemRequestDto>()
            {
                new ItemRequestDto()
                {
                    Amount = (int)(valorAPagar * 100),
                    Description = "Item 1",
                    Quantity = 1
                }
            };
        }

        private void AtualizarInformacoesDeTransacaoCapturadaPeloIdOrdem(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            transacaoPosWebhookRequest.ExternalTransactionId = chargeResponse.Id;
            transacaoPosWebhookRequest.CodigoBandeiraCartao = chargeResponse.LastTransaction.Card.Brand;
            transacaoPosWebhookRequest.FormaPagamentoPos = (int)chargeResponse.LastTransaction.Card.Type;
            transacaoPosWebhookRequest.EhSplit = false;
            transacaoPosWebhookRequest.StatusPreTransacao = "processed";
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        private void AtualizarInformacoesDeTransacaoCanceladaPeloIdOrdem(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            transacaoPosWebhookRequest.OperacaoCancelada = true;
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        private void TratarTransacoesEstornadas(ChargeResponseDto chargeResponse, string webhookType)
        {
            if (webhookType == "charge.refunded")
            {
                AtualizarInformacoesDeTransacaoEstornadaPeloIdOrdem(chargeResponse);
                GerarNotificacoesDeCancelamentoNaSubadquirente(chargeResponse);
            }
        }

        private void AtualizarInformacoesDeTransacaoEstornadaPeloIdOrdem(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            transacaoPosWebhookRequest.PagamentoEstornado = true;
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        private void GerarNotificacoesDeCancelamentoNaSubadquirente(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            EnviarEmailAvisandoEstabelecimentoDoCancelamentoDaTransacao(transacaoPosWebhookRequest);
            NotificarEstabelecimentoDoCancelamentoNaAgenda(transacaoPosWebhookRequest);
            NotificarEstabelecimentoDoCancelamentoNoAppPro(transacaoPosWebhookRequest);
        }

        private void NotificarEstabelecimentoDoCancelamentoNoAppPro(TransacaoPosWebhookRequest transacaoPosWebhookRequest)
        {
            TipoDeNotificacao tipo = Domain.NotificacoesApps.TipoDeNotificacaoRepository
                .Queryable().FirstOrDefault(f => f.Id == (int)TipoDeMensagemDeNotificacaoEnum.AgendamentoCanceladoQueNaoSejaPeloProprioProfissional && f.Ativo == true);

            CanalDaNotificacao canal = Domain.NotificacoesApps.CanalDaNotificacaoRepository.Queryable()
                .FirstOrDefault(f => f.Ativo == true && f.Id == (int)CanalDaNotificacaoEnum.Sininho);

            var transacaoPos = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorCodigoAutorizacaoTransacaoPdv(transacaoPosWebhookRequest.CodigoTransacao);
            var texto = $"A transação {transacaoPosWebhookRequest.CodigoTransacao} realizada em {transacaoPos.Data.ToString("dd/MM/yyyy")} na maquininha de cartão foi cancelada pela Stone. Toque e confira detalhes.";
            var horario = transacaoPos.Transacao.HorariosTransacoes.FirstOrDefault(ht => ht.Horario.Id > 0).Horario;
            var conta = transacaoPosWebhookRequest.Estabelecimento.ObterResponsavel().Contas.FirstOrDefault();
            _notificacaoStrategy = new NotificacaoAgendamentoCanceladoQueNaoSejaPeloProprioProfissionalStrategy();
            var parametros = _notificacaoStrategy.CarregarParametros(horario, canal);
            var textosDaNotificacao = _notificacaoStrategy.CarregarModeloDeTextos(horario, canal);
            textosDaNotificacao["Titulo"] = texto;

            EventoDeNotificacao eventoDeNotificacao = new EventoDeNotificacao
            {
                IdEstabelecimento = horario.Estabelecimento.IdEstabelecimento,
                DataHoraProgramado = Calendario.Agora(),
                Tipo = tipo,
                JaFoiLida = false,
                Parametros = JsonConvert.SerializeObject(parametros),
                QuantidadeDeEnviosRealizados = 0
            };

            eventoDeNotificacao.IdPessoa = conta.Pessoa.IdPessoa;
            Domain.NotificacoesApps.CanalDaNotificaoAppProService.ProgramarEnvioDeNotificacaoPorEsseCanal(eventoDeNotificacao, canal, textosDaNotificacao);
        }

        private void NotificarEstabelecimentoDoCancelamentoNaAgenda(TransacaoPosWebhookRequest transacaoPosWebhookRequest)
        {
            var transacaoPos = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorCodigoAutorizacaoTransacaoPdv(transacaoPosWebhookRequest.CodigoTransacao);
            var texto = $"A transação {transacaoPosWebhookRequest.CodigoTransacao} realizada em {transacaoPos.Data.ToString("dd/MM/yyyy")} na maquininha de cartão foi cancelada pela Stone. Toque e confira detalhes.";
            var horario = transacaoPos.Transacao.HorariosTransacoes.FirstOrDefault(ht => ht.Horario.Id > 0).Horario;
            var conta = transacaoPosWebhookRequest.Estabelecimento.ObterResponsavel().Contas.FirstOrDefault();

            var notificacaoEstabelecimento = new NotificacaoEstabelecimento
            {
                Estabelecimento = transacaoPosWebhookRequest.Estabelecimento,
                DataHoraReferencia = DateTime.Now,
                DataHoraRegistroNotificacao = DateTime.Now,
                Visualizada = false,
                Texto = texto,
                TipoNotificacao = 6,
                Conta = conta,
                Horario = horario
            };

            Domain.Pessoas.NotificacaoEstabelecimentoRepository.SaveNew(notificacaoEstabelecimento);
        }

        private void EnviarEmailAvisandoEstabelecimentoDoCancelamentoDaTransacao(TransacaoPosWebhookRequest transacaoPosWebhookRequest)
        {
            var assunto = $"Cancelamento de Transação na Maquininha de Cartão - Stone";
            var remetente = Pessoas.Services.EnvioEmailService.ObterRemetentePadrao();
            var pathQuery = HttpContext.Current.Request.Url.PathAndQuery;
            var url = HttpContext.Current.Request.Url.AbsoluteUri.Replace(pathQuery, "/");
            var paragrafos = new List<string>();

            paragrafos.Add($"<b>Prezado(a) {transacaoPosWebhookRequest.Estabelecimento.NomeDeExibicaoNoPortal}</b>");
            paragrafos.Add($"Informamos que o valor referente à transação {transacaoPosWebhookRequest.CodigoTransacao} realizada em {transacaoPosWebhookRequest.DataOperacao?.ToString("dd/MM/yyyy")} na maquininha de cartão foi cancelado pela Stone.");
            paragrafos.Add($"Para mais informações, entre em contato com a Stone através de um dos seus canais de atendimento.");
            paragrafos.Add($"Capitais e regiões metropolitanas: 3004 9680<br />Demais regiões: 0800 326 0506<br />Email: <EMAIL>");
            paragrafos.Add($"Atenciosamente,");

            var corpoEmail = string.Join("<br /><br />", paragrafos);

            Domain.Pessoas.EnvioEmailService.DispararEmail(assunto, corpoEmail, remetente, new MailAddress(transacaoPosWebhookRequest.Estabelecimento.EmailEstabelecimento()), null);
        }
        #endregion
    }
}