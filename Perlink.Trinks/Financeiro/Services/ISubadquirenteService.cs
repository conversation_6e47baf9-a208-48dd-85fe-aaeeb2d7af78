﻿using Perlink.DomainInfrastructure.Services;
using Perlink.IntegracaoSplit;
using Perlink.IntegracaoSplit.Models;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Threading.Tasks;

namespace Perlink.Trinks.Financeiro.Services
{

    public interface ISubadquirenteService : IService
    {

        Task<bool> RealizarSplitSeNecessario(int idTransacao);
        Task<bool> CancelarSplit(int idTransacao);
        RetornoPagamentoPosDto RealizarPagamentoPOS(Transacao transacaoFicticia,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfigPOS, decimal valorAPagar,
            TipoPagamentoEnum tipoPagamento, TipoParcelamentoEnum? tipoParcelamento = null, int? numParcelas = null,
            string terminalSelecionado = null);
        Task<string> CancelarTransferencia(string internalTransferSecurityKey, string originMerchantAffiliationKey, string transferOrderKey);
        Task AtualizarStatusPosSplits();
        Task<TransferInternaStatusEnum> ObterStatusTransferencia(string transferOrderKey, string internalTransferSecurityKey);

        Task<RequestSplitModel> GerarDadosParaRegistroDeSplitSeNecessario(TransacaoPOS transacaoPOS, Transacao transacao, IIntegracaoSplit integracaoTranferencia, EstabelecimentoConfiguracaoPOS config);

        void GravarLogPOS(string AuthorizationReportValue, int IdCliente);

        Task ObterStatusSplitEAtualizarTransacaoPOS(IIntegracaoSplit integracaoTransferencia, TransacaoPOSSplit splitPos, EstabelecimentoConfiguracaoPOS config);

        RetornoCadastroDeRecebedorDTO AssociarProfissionalComoRecebedorPOS(EstabelecimentoProfissional ep);

        bool CancelarCadastroProfissionalRecebedor(EstabelecimentoProfissional ep);

        string RealizarCadastroRecebedor(DadosParaCadastrarRecebedorDto dadosDoRecebedorDto);

        RetornoListaDeRecebedoresDto ObterListaDeRecebedores(string secretKey, int page, int size);

        void CancelarOrdemConnect(string idOrdem, int idEstabelecimento);

        bool CapturarPedidoComSplit(int idEstabelecimento, SplitDTO split);
    }
}