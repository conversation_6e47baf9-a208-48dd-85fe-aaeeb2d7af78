﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro.DTO.CalculoComissao;
using Perlink.Trinks.Financeiro.Factories;
using Perlink.Trinks.Loggers;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using static Perlink.Trinks.Financeiro.DTO.CalculoComissao.CalculosDeComissaoDTO;

namespace Perlink.Trinks.Financeiro.Services
{

    public class CalculoComissaoService : BaseService, ICalculoComissaoService
    {

        private readonly CloudWatchLogger loggerComissao;

        public static string LoggerCorrelationId;

        private readonly IDescontoPersonalizadoService _descontoPersonalizadoService;

        public CalculoComissaoService()
        {
            _descontoPersonalizadoService = new DescontoPersonalizadoService();
            loggerComissao = new CloudWatchLogger(ConfiguracoesTrinks.AWS.ComissaoLogGroupName);
        }

        public CalculosDeComissaoDTO CalcularComissao(DadosParaCalculoComissaoDTO dados)
        {
            LoggerCorrelationId = Guid.NewGuid().ToString();

            var retorno = new CalculosDeComissaoDTO();

            ValidarDadosParaCalculoComissao(dados);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(dados.IdPessoaEstabelecimento);
            var configuracoesPosEstabelecimentos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var estabelecimentoPermiteComissaoProfissional = estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao;
            var estabelecimentoPermiteComissaoProfissionalAssistente = estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissaoDeAssistentes;

            loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Estabelecimento obtido com ID: {estabelecimento.IdEstabelecimento}");

            var configuracaoPOSPermiteDescontoTaxaDaoperadora = true;
            if (configuracoesPosEstabelecimentos != null && configuracoesPosEstabelecimentos.TipoPOS != null)
            {
                configuracaoPOSPermiteDescontoTaxaDaoperadora = configuracoesPosEstabelecimentos.TipoPOS.DescontaTaxaOperadoraNaComissao;
            }

            loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Configuração POS permite desconto taxa da operadora: {configuracaoPOSPermiteDescontoTaxaDaoperadora}");

            var subtrairPercentualDoProfissionalAPartirDoAssistente = new ControleDeFuncionalidades.Configuracao<bool>(ControleDeFuncionalidades.Enums.ConfiguracaoEnum.subtrair_percentual_assistente_do_percentual_profissional).ObterValor(estabelecimento);

            foreach (var item in dados.Itens)
            {
                var itemServico = item as DadosParaCalculoComissaoDTO.ItemServicoDTO;
                var possuiAssistente = itemServico != null && itemServico.PossuiAssistente;
                var possuiProfissional = item.IdPessoaComissionada > 0 && dados.Profissionais.Any(f => f.IdPessoa == item.IdPessoaComissionada);

                if (possuiProfissional)
                {
                    var profissional = ObterProfissionalDTO(dados, item.IdPessoaComissionada);
                    var deveDescontarTaxaOperadora = profissional.DescontaTaxaOperadora;
                    var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(profissional.IdPessoa, estabelecimento.IdEstabelecimento);

                    if (!configuracaoPOSPermiteDescontoTaxaDaoperadora && item.EhComissaoComSplit.HasValue && item.EhComissaoComSplit.Value)
                        deveDescontarTaxaOperadora = configuracaoPOSPermiteDescontoTaxaDaoperadora;

                    if (ValidarComissaoMaquininhaResgistro(itemServico))
                    {
                        if (estabelecimentoPermiteComissaoProfissional && !estabelecimentoProfissional.EhExcecaoDescontoTaxaOperadora)
                        {
                            deveDescontarTaxaOperadora = true;
                            item.EhComissaoComSplit = false;
                        }
                    }

                    loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Deve descontar taxa operadora: {deveDescontarTaxaOperadora}, Comissao Split: {item.EhComissaoComSplit}");

                    var percentualComissao = item.PercentualComissao ?? 0;

                    loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Percentual comissao: {percentualComissao}, Comissao Split: {item.EhComissaoComSplit}");

                    if (subtrairPercentualDoProfissionalAPartirDoAssistente && itemServico != null)
                        percentualComissao = SubtrairPercentualAssistenteDoPercentualProfissional(itemServico.PercentualComissaoAssistente, percentualComissao);

                    loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Percentual comissao: {percentualComissao}, subtrairPercentualDoProfissionalAPartirDoAssistente: {subtrairPercentualDoProfissionalAPartirDoAssistente}");

                    var comissaoCalculadaDTO = GerarComissaoCalculadaDTO(dados, item, percentualComissao, profissional, deveDescontarTaxaOperadora);
                    retorno.Comissoes.Add(comissaoCalculadaDTO);

                    loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Cálculo para profissional: {item.IdPessoaComissionada}, valores {comissaoCalculadaDTO.ToString()}");
                }

                if (possuiAssistente)
                {
                    var assistente = ObterProfissionalDTO(dados, itemServico.IdPessoaAssistenteComissionada.Value);
                    var deveDescontarTaxaOperadora = assistente.DescontaTaxaOperadoraAssistente;
                    var estabelecimentoProfissionalAssistente = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(assistente.IdPessoa, estabelecimento.IdEstabelecimento);


                    if (!configuracaoPOSPermiteDescontoTaxaDaoperadora && item.EhComissaoComSplit.HasValue && item.EhComissaoComSplit.Value)
                        deveDescontarTaxaOperadora = configuracaoPOSPermiteDescontoTaxaDaoperadora;

                    if (ValidarComissaoMaquininhaResgistro(itemServico))
                    {
                        if (estabelecimentoPermiteComissaoProfissionalAssistente && !estabelecimentoProfissionalAssistente.EhExcecaoDescontoTaxaOperadora)
                        {
                            deveDescontarTaxaOperadora = true;
                            item.EhComissaoComSplit = false;
                        }
                    }

                    var comissaoAssistenteCalculadaDTO = GerarComissaoCalculadaDTO(dados, item, itemServico.PercentualComissaoAssistente.Value, assistente, deveDescontarTaxaOperadora, true);
                    retorno.Comissoes.Add(comissaoAssistenteCalculadaDTO);

                    loggerComissao.Log(LoggerCorrelationId, $"({nameof(CalcularComissao)}) Cálculo para assistente: {item.IdPessoaComissionada}, valores {comissaoAssistenteCalculadaDTO.ToString()}");
                }
            }

            return retorno;
        }

        private decimal SubtrairPercentualAssistenteDoPercentualProfissional(decimal? percentualComissaoAssistente, decimal percentualComissao)
        {
            if (percentualComissaoAssistente.HasValue)
                percentualComissao = percentualComissao - percentualComissaoAssistente.Value;

            return percentualComissao;
        }

        public void DefinirComissaoDaTransacao(Transacao transacao, CalculosDeComissaoDTO calculos, bool atualizarComissao = false)
        {
            var comissoesServico = calculos.Comissoes
                .Where(f => f is CalculosDeComissaoDTO.ComissaoCalculadaServicoDTO)
                .Select(f => f as CalculosDeComissaoDTO.ComissaoCalculadaServicoDTO)
                .ToList();

            foreach (var c in comissoesServico)
            {
                if (c.HorarioTransacao.Comissao?.ComissaoAlteradaManualmente == true)
                    continue;

                if (c.HorarioTransacao.Comissao != null && atualizarComissao)
                    c.HorarioTransacao.Comissao = AtualizarComissao(c.HorarioTransacao.Comissao, c, transacao);
                else
                    c.HorarioTransacao.Comissao = GerarComissao(c, transacao);
            }

            var comissoesServicoAssistente = calculos.Comissoes
                .Where(f => f is CalculosDeComissaoDTO.ComissaoCalculadaServicoAssistenteDTO)
                .Select(f => f as CalculosDeComissaoDTO.ComissaoCalculadaServicoAssistenteDTO)
                .ToList();

            foreach (var c in comissoesServicoAssistente)
            {
                if (c.HorarioTransacao.ComissaoAssistente?.ComissaoAlteradaManualmente == true)
                    continue;

                if (c.HorarioTransacao.ComissaoAssistente != null && atualizarComissao)
                    c.HorarioTransacao.ComissaoAssistente = AtualizarComissao(c.HorarioTransacao.ComissaoAssistente, c, transacao);
                else
                    c.HorarioTransacao.ComissaoAssistente = GerarComissao(c, transacao);
            }

            var comissoesProduto = calculos.Comissoes
                .Where(f => f is CalculosDeComissaoDTO.ComissaoCalculadaProdutoDTO)
                .Select(f => f as CalculosDeComissaoDTO.ComissaoCalculadaProdutoDTO)
                .ToList();

            foreach (var c in comissoesProduto)
            {
                if (c.ItemVenda.Comissao?.ComissaoAlteradaManualmente == true)
                    continue;

                if (c.ItemVenda.Comissao != null && atualizarComissao)
                    c.ItemVenda.Comissao = AtualizarComissao(c.ItemVenda.Comissao, c, transacao);
                else
                    c.ItemVenda.Comissao = GerarComissao(c, transacao);
            }

            var comissoesPacote = calculos.Comissoes
                .Where(f => f is CalculosDeComissaoDTO.ComissaoCalculadaPacoteDTO)
                .Select(f => f as CalculosDeComissaoDTO.ComissaoCalculadaPacoteDTO)
                .ToList();

            foreach (var c in comissoesPacote)
            {
                if (c.ItemVenda.Comissao?.ComissaoAlteradaManualmente == true)
                    continue;

                if (c.ItemVenda.Comissao != null && atualizarComissao)
                    c.ItemVenda.Comissao = AtualizarComissao(c.ItemVenda.Comissao, c, transacao);
                else
                    c.ItemVenda.Comissao = GerarComissao(c, transacao);
            }
        }

        public void DefinirComissaoDaTransacao(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            //var comissoes = Domain.Financeiro.ComissaoRepository.ObterComissoesDaTransacao(transacao);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            var subtrairPercentualAssistenteDoPercentualProfissional = new ControleDeFuncionalidades.Configuracao<bool>(ControleDeFuncionalidades.Enums.ConfiguracaoEnum.subtrair_percentual_assistente_do_percentual_profissional).ObterValor(estabelecimento);
            var estabelecimentoProfissionalServicoComissoes = ComissoesNosServicosDeUmaTransacao(transacao);
            var estabelecimentoAssistenteServicos = EstabelecimentoAssistenteServicosDaTransacao(transacao);
            var estabelecimentoProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterEstabelecimentoProfissionaisEnvolvidosNaTransacao(transacao.Id);
            var estabelecimentoFormasPagamento = EstabelecimentoFormasPagamentoDaTransacao(transacao);
            var subtrairDescontoCashback = Domain.Pessoas.EstabelecimentoConfiguracaoComissaoRepository.ObterDescontaCashbackComissaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            var dadosParaCalculoComissao = new DadosParaCalculoComissaoDTOFactory().ConstruirDadosParaCalculoComissaoDTO(transacao,
                estabelecimentoProfissionais, estabelecimentoProfissionalServicoComissoes, estabelecimentoAssistenteServicos,
                estabelecimentoFormasPagamento, estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                subtrairPercentualAssistenteDoPercentualProfissional: subtrairPercentualAssistenteDoPercentualProfissional,
                considerarDescontoCashbackNosDescontosDoCliente: subtrairDescontoCashback);

            var calculos = new CalculoComissaoService().CalcularComissao(dadosParaCalculoComissao);

            DefinirComissaoDaTransacao(transacao, calculos);
        }

        private static List<EstabelecimentoFormaPagamento> EstabelecimentoFormasPagamentoDaTransacao(Transacao transacao)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            var retorno = new List<EstabelecimentoFormaPagamento>();

            foreach (var tfp in transacao.FormasPagamento)
            {
                var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(estabelecimento.IdEstabelecimento, tfp.FormaPagamento.Id);
                if (estabelecimentoFormaPagamento != null)
                    retorno.Add(estabelecimentoFormaPagamento);
            }

            return retorno;
        }

        private static List<EstabelecimentoAssistenteServico> EstabelecimentoAssistenteServicosDaTransacao(Transacao transacao)
        {
            var retorno = new List<EstabelecimentoAssistenteServico>();
            var estabelecimentoProfissionalAssistenteIds = transacao.HorariosTransacoes.Select(ht => ht.Horario.EstabelecimentoProfissionalAssistente?.Codigo).Where(id => id != null).Distinct();
            var servicoEstabelecimentoIds = transacao.HorariosTransacoes.Select(ht => ht.Horario.ServicoEstabelecimento.IdServicoEstabelecimento).Distinct();

            foreach (var estabelecimentoProfissionalAssistenteId in estabelecimentoProfissionalAssistenteIds)
            {
                foreach (var servicoEstabelecimentoId in servicoEstabelecimentoIds)
                {
                    var assistenteServico = Domain.Pessoas.EstabelecimentoAssistenteServicoRepository.Obter(estabelecimentoProfissionalAssistenteId.Value, servicoEstabelecimentoId);
                    if (assistenteServico != null)
                        retorno.Add(assistenteServico);
                }
            }

            return retorno;
        }

        public List<EstabelecimentoProfissionalServicoComissao> ComissoesNosServicosDeUmaTransacao(Transacao transacao)
        {
            var retorno = new List<EstabelecimentoProfissionalServicoComissao>();

            foreach (var ht in transacao.HorariosTransacoes)
            {
                var horario = ht.Horario;
                var servicoComissao = Domain.Pessoas.EstabelecimentoProfissionalServicoComissaoRepository.ObterComissaoProfissional(horario.Profissional, horario.ServicoEstabelecimento, transacao.DataHora);
                if (servicoComissao != null)
                    retorno.Add(servicoComissao);

                if (horario.EstabelecimentoProfissionalAssistente != null)
                {
                    var servicoComissaoAssistente = Domain.Pessoas.EstabelecimentoProfissionalServicoComissaoRepository.ObterComissaoProfissional(horario.EstabelecimentoProfissionalAssistente.Profissional, horario.ServicoEstabelecimento, transacao.DataHora);
                    if (servicoComissaoAssistente != null)
                        retorno.Add(servicoComissaoAssistente);
                }
            }

            return retorno;
        }

        public DadosParaCalculoComissaoDTO ColetarDadosTransacao(Transacao transacao)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);

            var subtrairPercentualAssistenteDoPercentualProfissional = new ControleDeFuncionalidades.Configuracao<bool>(ControleDeFuncionalidades.Enums.ConfiguracaoEnum.subtrair_percentual_assistente_do_percentual_profissional).ObterValor(estabelecimento);

            var estabelecimentoProfissionalServicoComissoes = Domain.Financeiro.CalculoComissaoService.ComissoesNosServicosDeUmaTransacao(transacao);

            var estabelecimentoProfissionalServicosQuery = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable().Where(f => f.Ativo && f.EstabelecimentoProfissional.Estabelecimento == estabelecimento).ToList();

            var estabelecimentoAssistenteServicos = Domain.Pessoas.EstabelecimentoAssistenteServicoRepository.Queryable().Where(f => f.EstabelecimentoProfissional.Estabelecimento == estabelecimento).ToList();
            var estabelecimentoProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(f => f.Estabelecimento == estabelecimento).ToList();
            var estabelecimentoFormasPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ListarAtivasPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var subtrairDescontoCashback = Domain.Pessoas.EstabelecimentoConfiguracaoComissaoRepository.ObterDescontaCashbackComissaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            return new DadosParaCalculoComissaoDTOFactory().ConstruirDadosParaCalculoComissaoDTO(transacao,
                estabelecimentoProfissionais, estabelecimentoProfissionalServicoComissoes, estabelecimentoAssistenteServicos,
                estabelecimentoFormasPagamento, estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                subtrairPercentualAssistenteDoPercentualProfissional: subtrairPercentualAssistenteDoPercentualProfissional,
                considerarDescontoCashbackNosDescontosDoCliente: subtrairDescontoCashback);
        }

        private static decimal CalcularTaxaMedia(decimal valorParcela1, decimal percentualParcela1, decimal valorParcela2, decimal percentualParcela2)
        {
            var valorTotal = (double)valorParcela2 + (double)valorParcela1;

            if (valorTotal == 0)
                return 0;

            var valorTaxaOperadora = (double)valorParcela2 * (double)percentualParcela2 + (double)valorParcela1 * (double)percentualParcela1;
            var percentualDescontoOperadoraNaComissaoNova = (double)valorTaxaOperadora / (double)valorTotal;
            return (decimal)percentualDescontoOperadoraNaComissaoNova;
        }

        private static CalculosDeComissaoDTO.ComissaoCalculadaDTO GerarComissaoCalculada(DadosParaCalculoComissaoDTO.ItemDTO item, DadosParaCalculoComissaoDTO.ProfissionalDTO profissional)
        {
            CalculosDeComissaoDTO.ComissaoCalculadaDTO comissaoCalculada;
            if (item is DadosParaCalculoComissaoDTO.ItemProdutoDTO)
            {
                comissaoCalculada = new CalculosDeComissaoDTO.ComissaoCalculadaProdutoDTO
                {
                    ItemVenda = (item as DadosParaCalculoComissaoDTO.ItemProdutoDTO).ItemVenda,
                    DescontoOperadoraNaComissao = profissional.DescontaTaxaOperadora
                };
            }
            else if (item is DadosParaCalculoComissaoDTO.ItemPacoteDTO)
            {
                comissaoCalculada = new CalculosDeComissaoDTO.ComissaoCalculadaPacoteDTO
                {
                    ItemVenda = (item as DadosParaCalculoComissaoDTO.ItemPacoteDTO).ItemVenda,
                    DescontoOperadoraNaComissao = profissional.DescontaTaxaOperadora
                };
            }
            else
            {
                var itemServico = item as DadosParaCalculoComissaoDTO.ItemServicoDTO;
                if (itemServico.IdPessoaComissionada == profissional.IdPessoa)
                    comissaoCalculada = new CalculosDeComissaoDTO.ComissaoCalculadaServicoDTO
                    {
                        HorarioTransacao = itemServico.HorarioTransacao,
                        DescontoOperadoraNaComissao = profissional.DescontaTaxaOperadora
                    };
                else
                    comissaoCalculada = new CalculosDeComissaoDTO.ComissaoCalculadaServicoAssistenteDTO
                    {
                        HorarioTransacao = itemServico.HorarioTransacao,
                        DescontoOperadoraNaComissao = profissional.DescontaTaxaOperadoraAssistente
                    };
            }

            return comissaoCalculada;
        }

        private CalculosDeComissaoDTO.ComissaoCalculadaDTO GerarComissaoCalculadaDTO(DadosParaCalculoComissaoDTO dados, DadosParaCalculoComissaoDTO.ItemDTO item, decimal percentualComissao, DadosParaCalculoComissaoDTO.ProfissionalDTO profissional, bool descontaTaxaOperadora, bool ehAssistente = false)
        {

            var comissaoComDescontos = _descontoPersonalizadoService.AplicarDescontoPersonalizadoQuandoConfigurado(dados, item, ehAssistente);

            bool pagarComissaoNaDataDaLiquidacao = profissional.PagarComissaoNaDataDaLiquidacao;

            var valorTotalPago = dados.FormasPagamento.Where(f => f.Valor > 0).Sum(f => f.Valor);

            var rateioValorBaseCalculoItem = valorTotalPago > 0
                ? (double)item.ValorBaseCalculo / (double)valorTotalPago
                : 1;

            var rateioValorLiquidoItem = valorTotalPago > 0
                ? (double)item.ValorLiquido / (double)valorTotalPago
                : 1;

            var parcelas = new List<CalculosDeComissaoDTO.Parcela>();
            foreach (var fp in dados.FormasPagamento.Where(f => f.Valor > 0))
            {
                var parcelasFormaPagamento = new List<CalculosDeComissaoDTO.Parcela>();

                decimal percentualDescontoOperadoraNaComissao = descontaTaxaOperadora &&
                                                        (!item.EhComissaoComSplit.HasValue || !item.EhComissaoComSplit.Value)
                                                        ? (item.ConsumoDePacote
                                                            ? item.PercentualTaxaOperadoraPacote
                                                            : -fp.DescontoOperadora / 100)
                                                        : 0;

                if (profissional.PagarComissaoNaDataDaLiquidacao)
                {
                    foreach (var fpp in fp.Parcelas)
                    {
                        var valorBaseParaCalculoComissao = (decimal)(rateioValorBaseCalculoItem * (double)fpp.Valor);
                        var valorPagoRateado = (decimal)(rateioValorLiquidoItem * (double)fpp.Valor);

                        var rateioParcelaNaFormaPagamento = ((double)fpp.Valor / (double)fp.Valor);

                        var valorLiquidoDoItem = (double)item.ValorBruto + (double)item.DescontoCliente;

                        var percentualValorParcelaSobreTotalPago = (double)fpp.Valor / (double)valorTotalPago;

                        var parcelaNova = new CalculosDeComissaoDTO.Parcela(
                            comissaoComDescontos.PercentualDescontoExtraNoValorBase,
                            comissaoComDescontos.PercentualDescontoExtraNoValorFinal,
                           percentualDescontoOperadoraNaComissao,
                           percentualComissao,
                           pagarComissaoNaDataDaLiquidacao,
                           valorBaseParaCalculoComissao,
                           valorPagoRateado,
                           profissional.PagarComissaoNaDataDaLiquidacao ? fpp.Data.Date : dados.DataFechamentoConta.Date,
                           fp.DiasParaLiquidar,
                           rateioParcelaNaFormaPagamento,
                           percentualValorParcelaSobreTotalPago,
                           fp.IdFormaPagamento,
                           fpp.IdParcela);

                        var parcelaExistente = parcelasFormaPagamento.FirstOrDefault(p => p.DataPagamentoParcela.Date == parcelaNova.DataPagamentoParcela.Date);

                        if (parcelaExistente == null)
                        {
                            parcelasFormaPagamento.Add(parcelaNova);
                            loggerComissao.Log(LoggerCorrelationId, $"({nameof(GerarComissaoCalculadaDTO)}) Gerar comissão para pessoa: {item.IdPessoaComissionada}, Parcela Nova: {parcelaNova.ToString()}");
                        }
                        else
                        {
                            parcelaExistente.ValorParaCalculoParcela += parcelaNova.ValorParaCalculoParcela;
                            loggerComissao.Log(LoggerCorrelationId, $"({nameof(GerarComissaoCalculadaDTO)}) Gerar comissão para pessoa: {item.IdPessoaComissionada}, Parcela Existente: {parcelaExistente.ToString()}");

                        }
                    }

                    parcelas.AddRange(parcelasFormaPagamento);
                }
                else
                {
                    var valorbaseParaCalculoComissao = (decimal)(rateioValorBaseCalculoItem * (double)fp.Valor);
                    var valorPagoRateado = (decimal)(rateioValorLiquidoItem * (double)fp.Valor);
                    var parcela = new CalculosDeComissaoDTO.Parcela(
                        comissaoComDescontos.PercentualDescontoExtraNoValorBase,
                        comissaoComDescontos.PercentualDescontoExtraNoValorFinal,
                        percentualDescontoOperadoraNaComissao,
                        percentualComissao,
                        pagarComissaoNaDataDaLiquidacao,
                        valorbaseParaCalculoComissao,
                        valorPagoRateado,
                        dados.DataFechamentoConta.Date,
                        fp.DiasParaLiquidar,
                        1, 1,
                        fp.IdFormaPagamento,
                        null);

                    var parcelaExistente = parcelas.SingleOrDefault();// f=>f.IdFormaPagamento == fp.IdFormaPagamento);

                    if (parcelaExistente == null)
                    {
                        parcelas.Add(parcela);

                        loggerComissao.Log(LoggerCorrelationId, $"({nameof(GerarComissaoCalculadaDTO)}) Gerar comissão para pessoa: {item.IdPessoaComissionada}, Parcela: {parcela.ToString()}");
                    }
                    else
                    {
                        if (!profissional.PagarComissaoNaDataDaLiquidacao)
                            parcelaExistente.PercentualDescontoOperadora = CalcularTaxaMedia(parcela.ValorParaCalculoParcela, parcela.PercentualDescontoOperadora, parcelaExistente.ValorParaCalculoParcela, parcelaExistente.PercentualDescontoOperadora);

                        parcelaExistente.ValorPagoRateadoParcela += parcela.ValorPagoRateadoParcela;
                        parcelaExistente.ValorParaCalculoParcela += parcela.ValorParaCalculoParcela;

                        loggerComissao.Log(LoggerCorrelationId, $"({nameof(GerarComissaoCalculadaDTO)}) Gerar comissão para pessoa: {item.IdPessoaComissionada}, Parcela: {parcelaExistente.ToString()}");
                    }
                }
            }

            if (!dados.FormasPagamento.Any(f => f.Valor != 0))
            {
                decimal percentualOperadora = 0;
                if (item.ConsumoDePacote && descontaTaxaOperadora)
                    percentualOperadora = item.PercentualTaxaOperadoraPacote;

                var pacela = new Parcela(
                        comissaoComDescontos.PercentualDescontoExtraNoValorBase,
                        comissaoComDescontos.PercentualDescontoExtraNoValorFinal,
                        percentualOperadora,
                        percentualComissao,
                        pagarComissaoNaDataDaLiquidacao,
                        item.ValorBaseCalculo,
                        item.ValorLiquido,
                        dados.DataFechamentoConta.Date,
                        0,
                        1, 1,
                        null,
                        null);

                parcelas.Add(pacela);

                loggerComissao.Log(LoggerCorrelationId, $"({nameof(GerarComissaoCalculadaDTO)}) Gerar comissão para pessoa: {item.IdPessoaComissionada}, valores {pacela.ToString()}");
            }

            var comissaoCalculadaDTO = GerarComissaoCalculada(item, profissional);

            comissaoCalculadaDTO.Parcelas = parcelas;
            comissaoCalculadaDTO.IdPessoaComissionada = profissional.IdPessoa;
            comissaoCalculadaDTO.DescontoCliente = item.DescontoCliente;
            comissaoCalculadaDTO.DescontoClienteNaComissao = item.DescontoClienteNaComissao;
            comissaoCalculadaDTO.ValorBruto = item.ValorBruto;
            comissaoCalculadaDTO.DescontoExtra = !dados.EhParaDescontarDescontoExtraNoValorBase && ehAssistente ? 0 : comissaoComDescontos.DescontoExtraReal;
            comissaoCalculadaDTO.DescontoExtraDescontadoNoValorBase = dados.EhParaDescontarDescontoExtraNoValorBase;
            comissaoCalculadaDTO.PercentualComissao = percentualComissao;
            comissaoCalculadaDTO.EhComissaoComSplit = item.EhComissaoComSplit;
            comissaoCalculadaDTO.ConsumoDePacote = item.ConsumoDePacote;
            comissaoCalculadaDTO.DescontoCashback = item.DescontoCashback;

            return comissaoCalculadaDTO;
        }

        private static ValorDeComissaoAReceber GerarValorDeComissaoAReceber(Comissao comissao, CalculosDeComissaoDTO.Parcela parcelaComissao)
        {
            var percentualValorItem = (decimal)parcelaComissao.RateioParcelaNoItem;

            var valorBruto = comissao.ValorBruto * percentualValorItem;

            var valorComissaoAReceber = new ValorDeComissaoAReceber
            {
                Comissao = comissao,
                DataDaComissaoAReceber = parcelaComissao.DataPagamentoComissao,
                DataDeCadastro = Calendario.Agora(),
                DescontoClienteProporcional = Math.Round(comissao.DescontoCliente * percentualValorItem, 2),
                DescontoExtraProporcional = Math.Round(parcelaComissao.DescontoExtraNoValorFinalProporcional + parcelaComissao.DescontoExtraNoValorBaseProporcional, 2),
                DescontoOperadoraProporcional = Math.Round(parcelaComissao.DescontoOperadoraProporcional, 2),
                IdPessoaEstabelecimento = comissao.IdPessoaEstabelecimento,
                Valor = Math.Round(parcelaComissao.ValorComissao, 2),
                ValorBaseProporcional = Math.Round(parcelaComissao.ValorBaseCalculoParcela, 2),
                ValorBrutoProporcional = Math.Round(valorBruto, 2),
                TransacaoFormaPagamentoParcela = parcelaComissao.IdParcela > 0 ? Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.Load(parcelaComissao.IdParcela.Value) : null,
            };

            if (comissao.PossuiCashbackNaoPersistido())
            {
                valorComissaoAReceber.AtribuirCashbackSemPersistencia(new Cashback.CashbackComissaoValorAReceber(Math.Round(comissao.ObterCashbackNaoPersistido().Valor * percentualValorItem, 2)));
            }

            return valorComissaoAReceber;
        }

        private static List<ValorDeComissaoAReceber> GerarValoresDeComissaoAReceber(CalculosDeComissaoDTO.ComissaoCalculadaDTO comissaoCalculada, Comissao comissao)
        {
            var retorno = new List<ValorDeComissaoAReceber>();

            foreach (var p in comissaoCalculada.Parcelas)
            {
                var parcelaNova = GerarValorDeComissaoAReceber(comissao, p);
                retorno.Add(parcelaNova);
            }

            return retorno;
        }

        private static DadosParaCalculoComissaoDTO.ProfissionalDTO ObterProfissionalDTO(DadosParaCalculoComissaoDTO dados, int idPessoaComissionada)
        {
            DadosParaCalculoComissaoDTO.ProfissionalDTO profissional;
            try
            {
                profissional = dados.Profissionais.Single(f => f.IdPessoa == idPessoaComissionada);
            }
            catch (Exception)
            {
                throw new ArgumentException("A pessoa comissionada não tem definições como profissional");
            }

            return profissional;
        }

        private Comissao GerarComissao(CalculosDeComissaoDTO.ComissaoCalculadaDTO comissaoCalculada, Transacao transacao)
        {
            var comissao = new Comissao();
            AtualizarComissao(comissao, comissaoCalculada, transacao);

            return comissao;
        }

        private Comissao AtualizarComissao(Comissao comissao, CalculosDeComissaoDTO.ComissaoCalculadaDTO comissaoCalculada, Transacao transacao)
        {
            comissao.ComissaoParaPagar = Math.Round(comissaoCalculada.ValorComissaoTotal, 2);
            comissao.DescontoCliente = comissaoCalculada.DescontoClienteNaComissao ? Math.Round(comissaoCalculada.DescontoCliente, 2) : 0;
            comissao.DescontoExtra = Math.Round(comissaoCalculada.DescontoExtra, 2);
            comissao.DescontoExtraNoValorBase = comissaoCalculada.DescontoExtraDescontadoNoValorBase;
            comissao.PessoaComissionada = new Pessoas.PessoaFisica { IdPessoa = comissaoCalculada.IdPessoaComissionada };
            comissao.TipoComissao = TipoComissaoEnum.Percentual;
            comissao.Transacao = transacao;
            comissao.TipoOrigemComissao = comissaoCalculada.TipoOrigemComissao;
            comissao.ValorBase = Math.Round(comissaoCalculada.ValorBase, 2);
            comissao.ValorBruto = Math.Round(comissaoCalculada.ValorBruto, 2);
            comissao.ValorComissao = Math.Round(comissaoCalculada.PercentualComissao * 100, 2);
            comissao.IdPessoaEstabelecimento = transacao.PessoaQueRecebeu.IdPessoa;
            comissao.ConsumoDePacote = comissaoCalculada.ConsumoDePacote;

            //if (comissaoCalculada.EhComissaoComSplit.HasValue)
            //    comissao.EhComissaoComSplit = comissaoCalculada.EhComissaoComSplit.Value;

            comissao.DescontoOperadora = Math.Round(comissaoCalculada.DescontoOperadora, 2);

            if ((!comissao.PossuiCashbackNaoPersistido()) && comissaoCalculada.DescontoCashback != 0)
            {
                comissao.AtribuirCashbackSemPersistencia(new Cashback.CashbackComissao(comissaoCalculada.DescontoCashback));
            }
            else if (comissao.PossuiCashbackNaoPersistido())
            {
                comissao.ObterCashbackNaoPersistido().Valor = comissaoCalculada.DescontoCashback;
            }

            var parcelas = GerarValoresDeComissaoAReceber(comissaoCalculada, comissao);

            comissao.ValoresAReceber.Clear();
            parcelas.ForEach(comissao.ValoresAReceber.Add);

            return comissao;
        }

        private void ValidarDadosParaCalculoComissao(DadosParaCalculoComissaoDTO dados)
        {
            //var valorPago = dados.FormasPagamento.Sum(f => f.Valor);
            //var valorDevido = dados.Itens.Sum(f => f.ValorLiquido);
            //if (valorPago < valorDevido)
            //    throw new ArgumentException($"Valor pago não paga o valor devido: {valorPago} < {valorDevido}");
        }

        public void RecalcularComissoesDoPeriodo(List<int> idPessoasComissionadas, int idPessoaEstabelecimento, DateTime? dataInicio = null, DateTime? dataFim = null)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(idPessoaEstabelecimento);

            var comissoesARecalcular = idPessoasComissionadas.SelectMany(id => Domain.Financeiro.ComissaoRepository.ObterComissoesRecalculaveisNoPeriodo(id, idPessoaEstabelecimento, dataInicio, dataFim).ToList()).ToList();
            var porTransacao = RemoverTransacoesQueTenhaComissaoComSplit(comissoesARecalcular);

            List<EstabelecimentoAssistenteServico> estabelecimentoAssistenteServicos;
            List<EstabelecimentoProfissional> estabelecimentoProfissionais;
            List<EstabelecimentoProfissionalServicoComissao> estabelecimentoProfissionalServicoComissoes = new List<EstabelecimentoProfissionalServicoComissao>(); ;

            var subtrairPercentualAssistenteDoPercentualProfissional = new ControleDeFuncionalidades.Configuracao<bool>(ControleDeFuncionalidades.Enums.ConfiguracaoEnum.subtrair_percentual_assistente_do_percentual_profissional).ObterValor(estabelecimento);

            estabelecimentoProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .Where(f => idPessoasComissionadas.Contains(f.Profissional.PessoaFisica.IdPessoa) && f.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento)
                .ToList();

            estabelecimentoAssistenteServicos = Domain.Pessoas.EstabelecimentoAssistenteServicoRepository.Queryable()
                .Where(f => f.Ativo && estabelecimentoProfissionais.Contains(f.EstabelecimentoProfissional))
                .ToList();

            estabelecimentoProfissionalServicoComissoes = Domain.Pessoas.EstabelecimentoProfissionalServicoComissaoRepository.Queryable()
                .Where(f => estabelecimentoProfissionais.Contains(f.EstabelecimentoProfissionalServico.EstabelecimentoProfissional) &&
                    (f.InicioVigencia == null || f.InicioVigencia <= Calendario.Agora()) &&
                    (f.FimVigencia == null || f.FimVigencia >= Calendario.Agora())
                ).ToList();

            var estabelecimentoFormasPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ListarAtivasPorEstabelecimento(estabelecimento.IdEstabelecimento);

            foreach (var t in porTransacao)
            {
                int idTransacao = t.Key.Id;

                var estabelecimentoProfissionaisDaTransacao = estabelecimentoProfissionais.ToList();
                var subtrairDescontoCashback = Domain.Pessoas.EstabelecimentoConfiguracaoComissaoRepository.ObterDescontaCashbackComissaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

                var dadosParaCalculoComissao = new DadosParaCalculoComissaoDTOFactory()
                    .ConstruirDadosParaCalculoComissaoDTO(
                        transacao: t.Key,
                        estabelecimentoProfissionais: estabelecimentoProfissionaisDaTransacao,
                        estabelecimentoProfissionalServicoComissoes: estabelecimentoProfissionalServicoComissoes.ToList(),
                        estabelecimentoAssistenteServicos: estabelecimentoAssistenteServicos.ToList(),
                        estabelecimentoFormasPagamento: estabelecimentoFormasPagamento,
                        descontarDescartaveisDoValorPago: estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                        estabelecimentoTrabalhaComDescartaveis: estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                        pularProfissionaisEServicosNaoIncluidos: true,
                        subtrairPercentualAssistenteDoPercentualProfissional: subtrairPercentualAssistenteDoPercentualProfissional,
                        considerarDescontoCashbackNosDescontosDoCliente: subtrairDescontoCashback);

                var calculos = CalcularComissao(dadosParaCalculoComissao);
                DefinirComissaoDaTransacao(t.Key, calculos, true);
            }
        }

        private List<IGrouping<Transacao, Comissao>> RemoverTransacoesQueTenhaComissaoComSplit(List<Comissao> comissoesARecalcular)
        {
            return comissoesARecalcular.GroupBy(f => f.Transacao).Where(g => !g.Any(p => p.EhComissaoComSplit)).ToList();
        }

        private bool ValidarComissaoMaquininhaResgistro(DadosParaCalculoComissaoDTO.ItemServicoDTO itemServico)
        {
            if (itemServico != null && itemServico.HorarioTransacao != null && itemServico.HorarioTransacao.Transacao != null)
            {
                var transacaoPOS = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorIdTransacao(itemServico.HorarioTransacao.Transacao.Id);
                if (transacaoPOS != null && !transacaoPOS.RegistradoPeloTrinks)
                {
                    return true;
                }
            }
            return false;
        }
    }
}