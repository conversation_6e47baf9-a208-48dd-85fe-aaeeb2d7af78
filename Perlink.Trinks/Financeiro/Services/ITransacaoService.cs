using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.Financeiro.Services
{

    public interface ITransacaoService : IService
    {

        RetornoPagamentoPosDto RealizarPagamentoPOS(PessoaFisica cliente, Estabelecimento estabelecimento,
            Transacao transacaoFicticia, Venda vendaFicticia, decimal valorAPagar, TipoPagamentoEnum tipoPagamento,
            TipoParcelamentoEnum? tipoParcelamento, int? numParcelas, string terminalSelecionado = null);

        bool CancelarPreTransacaoPOS(string codigoPreTransacao);

        void Atualizar(Transacao transacao, PessoaFisica pessoaFisicaOperadorDeCaixa, bool jaTeveControleDeCaixaPorProfissional = false);

        int GerarEGravarNumeroDoFechamento(PessoaJuridica pessoaJuridica);

        Task<int> GerarEEmitirNotaFiscal(Transacao transacao, PessoaFisica pessoaQueEmitiu = null);

        Task<string> GerarNFCeParaDownload(Transacao transacao, PessoaFisica pessoaQueEmitiu = null);

        Task<ResultadoCheckOutDTO> RealizarCheckOut(CheckoutDTO checkoutDTO);

        void ColocarCompraDeCreditoNaTransacao(Transacao transacao, FormaPagamentoPrePagoEnum formaPagamentoPrePago, decimal valorDoCredito);

        Task<Transacao> RealizarEstorno(int idTransacao, DateTime dataHoraTrancao, String comentario, PessoaFisica pessoaFisicaAutenticada, Int32? idTransacaoPOS);

        void RecalcularCheckOut(IEnumerable<DadosParaRecalculoComissao> dadosParaRecalculoComissao);

        /// <summary>
        ///     Este recalculo é utilizado para redefinir os valores de comissoes originais de transacoes que tiveram seus dados
        ///     informados no
        ///     relatório de comissões
        /// </summary>
        /// <param name="dadosParaRecalculoComissao"></param>
        //void RecalcularComissoesOriginaisDeComissoesInformadasManualmente(
        //    IEnumerable<DadosParaRecalculoComissao> dadosParaRecalculoComissao);

        void RecalcularCreditoCliente(ClienteEstabelecimento clienteEstabelecimento);

        void RecalcularVendas(IList<ItemVendaProduto> itensVendas);

        bool PessoaRecebeComissaoNaDataPrevistaDeRecebimento(PessoaFisica pessoaComissionada, Estabelecimento estabelecimento);

        bool ConsiderarDescontoOperadoraNaComissaoParaPessoa(PessoaFisica pessoaComissionada, Estabelecimento estabelecimento, bool ehParaAssistente);

        string ObterUrlQRCodeDaNFC(NotaNFC notaNFC);

        byte[] ObterImagemQRCodeDaNFCe(NotaNFC notaNFC, Estabelecimento estabelecimento);

        void EnviarEventoDeTransacaoParaIntegracao(Transacao transacao, TipoDeEventoEnum tipoDeEventoEnum);

        bool TransacaoPertenceAoEstabelecimento(int idTransacao, int idEstabelecimento);

        TransacaoFormaPagamento MontarTransacaoFormaPagamento(Transacao transacao, int idFormaPagamento, int idEstabelecimento,
            decimal valorPago, List<ParcelaDePagamentoDTO> parcelas = null, int idTransacaoFormaPagamento = 0, int idPessoaQuePagou = 0, int idValePresente = 0);

        void ValidarEstorno(Transacao transacao);

        bool PodeEstornarTransacao(Transacao transacao);

        void ValidarEstornoDeFechamentoComMensagemNaTela(Estabelecimento estabelecimento, int idTransacao);

        void ValidarPermissaoParaAlterarDataDaTransacao(Estabelecimento estabelecimento, int idTransacao, DateTime dataMovimentacaoAposEdicao);

        Task<int> CriarTransacaoParaPagamentosDoClubeAsync(TransicaoParaClubeAssinaturaDTO filtro);

        bool FranquiaPermiteAlterarValoresDosServicosEProdutos(Estabelecimento estabelecimento);

        PermissoesNoFechamentoDeContasDTO ObterPermissoesParaFecharConta(Estabelecimento estabelecimento);

        Task<ResultadoCheckOutDTO> GerarTransacaoFinanceiraVendaPacoteNoHotsite(VendaHotsiteDTO dto, PessoaFisica pessoaQuePagou);

        void VerificarSePacoteJaFoiConsumido(Venda venda, Transacao transacao);

        TaxasDTO ObterTaxasParaTransacao(int? idTaxasPagamentoOnlineTrinks);
        bool CapturarTransacaoComSplit(Estabelecimento estabelecimento, SplitDTO split);

        Task<ResultadoCheckOutDTO> RealizarTransacaoPagamentoAntecipadoHotsite(PagamentoAntecipadoHotsiteDto filtro);

        void CriarOuAssociarFormaPagamentoSeNecessario(int idEstabelecimento);

        List<KeyValuePair<string, string>> PreencherItensPendentesCasoHouver(PreencherItensPendentesDTO preencherItensPendentes);

        bool VerificarSeHaServicosNaoFinalizados(VerificaServicosNaoFinalizadosDTO verificaServicosNaoFinalizados);
    }
}