﻿using Elmah;
using NHibernate.Proxy;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Cashback.DTO.CrmBonus;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Fidelidade;
using Perlink.Trinks.Fidelidade.Enums;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.Repositories;
using Perlink.Trinks.IntegracaoComOutrosSistemas;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor.Exceptions;
using Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF;
using Perlink.Trinks.Pacotes;
using Perlink.Trinks.PagamentosAntecipados;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Services;
using Perlink.Trinks.RecorrenciaDeAssinatura.DTO;
using Perlink.Trinks.Resources;
using Perlink.Trinks.RPS;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Vendas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.Financeiro.Services
{

    public class TransacaoService : BaseService, ITransacaoService
    {

        #region Propriedades de Apoio

        private static IAgendaService AgendaService
        {
            get { return Domain.Pessoas.AgendaService; }
        }

        private static ITransacaoRepository TransacaoRepository
        {
            get { return Domain.Financeiro.TransacaoRepository; }
        }

        #endregion Propriedades de Apoio

        #region Transacao

        [TransactionInitRequired]
        public void Atualizar(Transacao transacao, PessoaFisica pessoaFisicaOperadorDeCaixa, bool jaTeveControleDeCaixaPorProfissional = false)
        {
            ValidarCheckout(transacao, pessoaFisicaOperadorDeCaixa);
            ValidarAlteracaoDeDataDaTransacao(transacao);

            if (ValidationHelper.Instance.IsValid && !ValidationHelper.Instance.TemPendenciaDeConfirmacao())
            {
                var pessoaLogada = Domain.Pessoas.ContaService.ObterPessoaFisicaDaContaAutenticada();

                ManterHistoricoDaTransacao(transacao, pessoaFisicaOperadorDeCaixa, jaTeveControleDeCaixaPorProfissional);

                var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);

                RecalcularTotais(transacao, venda);
                Domain.Financeiro.TransacaoRepository.Update(transacao);

                if (venda != null && !transacao.HorariosTransacoes.Any())
                {
                    venda.Data = transacao.DataHora;
                    transacao.DataReferencia = transacao.DataHora;
                }

                AtualizarDataMovimentacaoDeEstoque(transacao);

                ValidarResultadoFinalCheckout(transacao);

                AtualizarLiberacoesDeComissoesDaTransacao(transacao);

                AlterarDataDaDividaAssociadaNaTransacao(transacao, pessoaLogada);

                if (jaTeveControleDeCaixaPorProfissional)
                    Domain.Financeiro.ControleDeCaixaService.ManterMovimentacaoDeTransacaoDePagamento(transacao, pessoaFisicaOperadorDeCaixa);
            }
            else
            {
                Domain.Financeiro.TransacaoRepository.Clear();
            }
        }

        private void AlterarDataDaDividaAssociadaNaTransacao(Transacao transacao, PessoaFisica pessoaLogada)
        {
            if (!transacao.TeveLancamentoDeDivida())
                return;

            Domain.DebitoParcial.RegistroDeDividaService.AlterarDataDaDividaPelaTransacao(
                new DebitoParcial.DTO.AlterarDataDaDividaDTO
                {
                    IdTransacao = transacao.Id,
                    DataHoraAlteracao = Calendario.Agora(),
                    NovaDataDaDivida = transacao.DataHora,
                    IdPessoaEstabelecimento = transacao.PessoaQueRecebeu.IdPessoa,
                    IdPessoaQueAlterou = pessoaLogada.IdPessoa
                });
        }

        private void AtualizarLiberacoesDeComissoesDaTransacao(Transacao transacao)
        {
            var comissoes = Domain.Financeiro.ComissaoRepository.ObterComissoesDaTransacao(transacao);

            //bool avisoDeDataEmFechamentoJaAdicionado = false;
            foreach (var comissao in comissoes)
            {
                //if (comissao.PossuiValorEmUmFechamentoMensal()) {
                //    if (!avisoDeDataEmFechamentoJaAdicionado) {
                //        ValidationHelper.Instance.AdicionarItemNotificacao("Não foi possível alterar as datas de liberação da comissão pois já existem valores da comissão que estão em um fechamento mensal.");
                //        avisoDeDataEmFechamentoJaAdicionado = true;
                //    }
                //}
                //else
                comissao.CalcularValoresAReceber();
            }
        }

        private void AtualizarDataMovimentacaoDeEstoque(Transacao transacao)
        {
            var movimentos = Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueRepository.Queryable()
                 .Where(f => f.ItemVenda.Venda.Transacao.Id == transacao.Id);

            var prevendas = Domain.Vendas.ItemVendaProdutoRepository.Queryable()
                .Where(f => f.PreVenda != null && f.Venda.Transacao == transacao).Select(f => f.PreVenda);
            foreach (var pv in prevendas)
            {
                var movimento = Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueRepository.Queryable()
                     .FirstOrDefault(f => f.PreVenda.Id == pv.Id);
                if (movimento != null && movimento.ItemVenda != null)
                {
                    movimento.DataMovimentacao = movimento.ItemVenda.Venda.Transacao.DataHora;
                    Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueService.Manter(movimento);
                }
            }

            foreach (var m in movimentos)
            {
                if (m.ItemVenda != null)
                {
                    m.DataMovimentacao = m.ItemVenda.Venda.Transacao.DataHora;
                    Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueService.Manter(m);
                }
            }
        }

        /*
            Na Stone Siclos a Forma de Pagamento pode ser alterada na maquininha,
            então temos que confirmar qual foi o método de pagamento de fato utilizado
        */

        public void AtualizarFormaDePagamentoSiclos(Transacao transacao, TransacaoPOS transacaoPos)
        {
            // consultar a tabela de TransacaoPosWebHook com o IdtransacaoPos
            var transacaoPosWebhook = Domain.Financeiro.TransacaoPosWebhookRequestRepository.ObterTransacaoPosWebhookRequestPorIdTransacaoPos(transacaoPos.Id);

            if (transacaoPosWebhook == null)
                return;

            // percorrer a lista de TransacaoFormaPagamento, procurando pelos que são belezinhas,
            const int TIPO_POS_SICLOS = 5;
            const int DEBITO_SICLOS = 1;

            string busca;
            for (int i = 0; i < transacao.FormasPagamento.Count(); i++)
            {
                if (transacao.FormasPagamento[i].FormaPagamento?.TipoPOS?.Id == TIPO_POS_SICLOS)
                {
                    busca = transacaoPosWebhook.CodigoBandeiraCartao;
                    if (transacaoPosWebhook.FormaPagamentoPos == DEBITO_SICLOS)
                    {
                        switch (transacaoPosWebhook.CodigoBandeiraCartao.ToLower())
                        {
                            case "mastercard":
                                busca = "Maestro";
                                break;

                            case "visa":
                                busca = "Electron";
                                break;

                            case "elo":
                                busca = "ELO D";
                                break;
                        }
                    }

                    var formaPagamento = Domain.Financeiro.FormaPagamentoRepository.ListarTodasBelezinhaAtivos().Where(p => p.Nome.Contains(busca) && p.TipoPOS.EhSplitSiclos).FirstOrDefault();

                    // sobrescrever as formas de pagamento
                    if (formaPagamento != null)
                    {
                        transacao.PercentualMedioDescontoOperadoras = formaPagamento.PercentualCobradoPeloOperadora;
                        transacao.FormasPagamento[i].FormaPagamento = formaPagamento;
                        //Domain.Financeiro.TransacaoRepository.Update(transacao);
                    }
                    transacao.TotalDescontoOperadoras = transacao.FormasPagamento.Sum(f => f.ValorDescontoOperadora());
                }
            }
        }

        public void AtualizarFormaDePagamentoConnectStone(Transacao transacao, TransacaoPOS transacaoPos)
        {
            var transacaoPosWebhook = Domain.Financeiro.TransacaoPosWebhookRequestRepository.ObterTransacaoPosWebhookRequestPorIdTransacaoPos(transacaoPos.Id);
            if (transacaoPosWebhook == null)
                return;

            string busca;
            const int DEBITO_CONNECT = 1;
            foreach (var formaPagamentoTransacao in transacao.FormasPagamento)
            {
                if (formaPagamentoTransacao.FormaPagamento?.TipoPOS?.Id != (int)SubadquirenteEnum.ConnectStone)
                    continue;

                busca = transacaoPosWebhook.CodigoBandeiraCartao.ToLower() == "mastercard" &&
                        transacaoPosWebhook.FormaPagamentoPos == DEBITO_CONNECT ?
                        "maestro" : transacaoPosWebhook.CodigoBandeiraCartao.ToLower();

                var formaPagamento = Domain.Financeiro.FormaPagamentoRepository.ListarTodasBelezinhaAtivos()
                    .FirstOrDefault(p => p.Nome.ToLower().Contains(busca) &&
                                         p.TipoPOS.Id == (int)SubadquirenteEnum.ConnectStone &&
                                         p.Tipo.Id == transacaoPosWebhook.FormaPagamentoPos);
                if (formaPagamento != null)
                {
                    transacao.PercentualMedioDescontoOperadoras = formaPagamento.PercentualCobradoPeloOperadora;
                    formaPagamentoTransacao.FormaPagamento = formaPagamento;
                }
                transacao.TotalDescontoOperadoras = transacao.FormasPagamento.Sum(f => f.ValorDescontoOperadora());
            }
        }

        public void AtualizarFormaDePagamentoConnectPagarme(Transacao transacao, TransacaoPOS transacaoPos)
        {
            var transacaoPosWebhook = Domain.Financeiro.TransacaoPosWebhookRequestRepository.ObterTransacaoPosWebhookRequestPorIdTransacaoPos(transacaoPos.Id);
            if (transacaoPosWebhook == null)
                return;

            string busca;
            const int DEBITO_CONNECT = 2;
            foreach (var formaPagamentoTransacao in transacao.FormasPagamento)
            {
                if (formaPagamentoTransacao.FormaPagamento?.TipoPOS?.Id != (int)SubadquirenteEnum.ConnectPagarme)
                    continue;

                busca = transacaoPosWebhook.CodigoBandeiraCartao.ToLower() == "mastercard" &&
                        transacaoPosWebhook.FormaPagamentoPos == DEBITO_CONNECT ?
                        "maestro" : transacaoPosWebhook.CodigoBandeiraCartao.ToLower();

                var formaPagamento = Domain.Financeiro.FormaPagamentoRepository.ListarTodasBelezinhaAtivos()
                    .FirstOrDefault(p => p.TipoPOS.Id == (int)SubadquirenteEnum.ConnectPagarme &&
                                         p.Tipo.Id == transacaoPosWebhook.FormaPagamentoPos &&
                                         p.Nome.ToLower().Contains(busca));
                if (formaPagamento != null)
                {
                    transacao.PercentualMedioDescontoOperadoras = formaPagamento.PercentualCobradoPeloOperadora;
                    formaPagamentoTransacao.FormaPagamento = formaPagamento;
                }
                transacao.TotalDescontoOperadoras = transacao.FormasPagamento.Sum(f => f.ValorDescontoOperadora());
            }
        }

        [TransactionInitRequired]
        public async Task<ResultadoCheckOutDTO> RealizarCheckOut(CheckoutDTO checkoutDTO)
        {
            var resultado = new ResultadoCheckOutDTO();
            var preVendasParaEfetivar = new List<PreVenda>();
            var numeroDoFechamentoGerado = 0;

            var transacao = checkoutDTO.Transacao;
            var quemMarcou = checkoutDTO.QuemMarcou;
            var venda = checkoutDTO.Venda;

            ValidarCheckout(transacao, transacao.PessoaQueRealizou, venda, checkoutDTO.ControlarCaixaSeNecessario);
            ValidarCheckoutDuplicado(transacao);
            var itensPacoteClienteAAtualizar = new List<ItemPacoteCliente>();

            if (ValidationHelper.Instance.IsValid && !ValidationHelper.Instance.TemPendenciaDeConfirmacao())
            {
                numeroDoFechamentoGerado = PreencherTransacaoComDadosDePagamentoEComissaoDoCheckout(transacao, quemMarcou, venda, preVendasParaEfetivar);

                List<ItemVendaPacote> itemVendaComPacoteNovo = null;
                if (venda != null)
                {
                    var itemVendaPacotes = venda.ItensVenda.Where(p => p is ItemVendaPacote).Cast<ItemVendaPacote>();
                    itemVendaComPacoteNovo = itemVendaPacotes.Where(f => f.PacoteCliente.Venda.Id == 0).ToList();

                    foreach (var i in itemVendaComPacoteNovo)
                    {
                        i.PacoteCliente.Venda = null;
                        Domain.Pacotes.PacoteClienteRepository.SaveNew(i.PacoteCliente);
                    }
                }
                var pacoteNaoSalvo =
                    transacao.HorariosTransacoes.Where(
                        f => f.ItemPacoteCliente != null && f.ItemPacoteCliente.PacoteCliente.Id == 0)
                        .Select(f => f.ItemPacoteCliente.PacoteCliente);
                foreach (var pacoteCliente in pacoteNaoSalvo)
                {
                    pacoteCliente.Venda = null;
                    Domain.Pacotes.PacoteClienteRepository.SaveNew(pacoteCliente);
                }

                // Atualizar FormaPagamento de transações
                if (checkoutDTO.IdTransacaoPOS > 0)
                {
                    var transacaoPos = Domain.Financeiro.TransacaoPOSRepository.Load(checkoutDTO.IdTransacaoPOS.Value);                  

                    if (transacaoPos != null && transacaoPos.TipoPOS.EhSplitSiclos)
                    {
                        AtualizarFormaDePagamentoSiclos(transacao, transacaoPos);
                    }

                    if (transacaoPos != null && transacaoPos.TipoPOS.EhConnectStone)
                    {
                        AtualizarFormaDePagamentoConnectStone(transacao, transacaoPos);
                    }
                    
                    if (transacaoPos != null && transacaoPos.TipoPOS.EhConnectPagarme)
                    {
                        AtualizarFormaDePagamentoConnectPagarme(transacao, transacaoPos);
                    }
                }

                var dadosCashback = Domain.Cashback.CashbackService.GerarDadosHorarioTransacaoEITemVendaCashback(transacao, venda);
                bool possuiCashback = dadosCashback.CashbackTransacao != null;

                Domain.Financeiro.TransacaoRepository.SaveNew(transacao);


                if (venda != null)
                {

                    var ehFluxoDaAssinaturaRecorrente = checkoutDTO.IdAssinaturaRecorrente > 0;

                    foreach (var i in itemVendaComPacoteNovo)
                    {
                        i.PacoteCliente.Venda = venda;
                    }

                    foreach (var pacoteCliente in pacoteNaoSalvo)
                    {
                        pacoteCliente.Venda = venda;
                    }

                    Domain.Vendas.VendaRepository.SaveNew(venda);
                    Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueService.RegistrarMovimentacoesProdutosDuranteVenda(
                        venda, quemMarcou);



                    List<PacoteCliente> pacotesDeAssinaturaNoFechamento = itemVendaComPacoteNovo.Select(p => p.PacoteCliente).Where(p => p.EhPacoteDeAssinatura()).ToList();
                    pacotesDeAssinaturaNoFechamento.AddRange(pacoteNaoSalvo.Where(p => p.EhPacoteDeAssinatura()).ToList());
                    pacotesDeAssinaturaNoFechamento = pacotesDeAssinaturaNoFechamento.Distinct().ToList();

                    if (ehFluxoDaAssinaturaRecorrente)
                    {
                        AtualizarDadosDaAssinaturaRecorrente(venda, checkoutDTO.IdAssinaturaRecorrente, ref pacotesDeAssinaturaNoFechamento);
                    }

                    foreach (var pacoteCliente in pacotesDeAssinaturaNoFechamento)
                    {
                        CriaAssinaturaRecorrente(transacao, venda, pacoteCliente);
                    }

                }

                itensPacoteClienteAAtualizar.AddRange(transacao.HorariosTransacoes.Where(item => item.ItemPacoteCliente != null).Select(item =>
                {
                    return item.ItemPacoteCliente;
                }));

                if (venda != null)
                {
                    var itens = venda.ItensVenda.Where(f => f is ItemVendaProduto);
                    itensPacoteClienteAAtualizar.AddRange(itens.Where(item => item.ItemPacoteCliente != null).Select(item =>
                    {
                        return item.ItemPacoteCliente;
                    }));
                }

                var formasPagamentoZeradas = transacao.FormasPagamento.Where(f => f.ValorPago == 0).ToList();
                foreach (var f in formasPagamentoZeradas)
                {
                    transacao.FormasPagamento.Remove(f);
                }

                ValidarResultadoFinalCheckout(transacao);

                Domain.Financeiro.TransacaoRepository.Flush();

                if (transacao.PessoaQuePagou != null)
                {
                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
                    RecalcularCreditoCliente(transacao.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);

                    var creditoCliente = transacao.FormasPagamento.FirstOrDefault(f => f.FormaPagamento == FormaPagamentoEnum.CreditoCliente);
                    if (creditoCliente != null && creditoCliente.PessoaQuePagou != transacao.PessoaQuePagou)
                        RecalcularCreditoCliente(creditoCliente.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);

                    var formaPagamentoValePresente = transacao.FormasPagamento.FirstOrDefault(p => p.ValePresente != null);
                    if (formaPagamentoValePresente != null)
                        RecalcularValePresente(formaPagamentoValePresente.ValePresente);
                }

                Domain.Pacotes.PacoteClienteService.AtualizarQuantidadeConsumida(itensPacoteClienteAAtualizar);

                Domain.Vendas.PreVendaService.EditarPreVendasSemTransaction(preVendasParaEfetivar, quemMarcou);
                Domain.Vendas.PreVendaService.EfetivarPreVendasSemTransaction(preVendasParaEfetivar, quemMarcou);

                AtualizarHorariosDoFechamentoConta(transacao, manterStatusAgendamentos: checkoutDTO.ManterStatusDosAgendamentos);

                if (checkoutDTO.IdTransacaoPOS > 0)
                {
                    Domain.Financeiro.TransacaoPOSService.AssociarComFechamento(checkoutDTO.IdTransacaoPOS.Value, transacao.Id);
                }

                if (venda != null)
                    foreach (var item in venda.ItensVenda)
                    {
                        var ivp = item as ItemVendaProduto;
                        if (ivp == null)
                            continue;

                        if (ivp.PreVenda != null && ivp.PreVenda is PreVendaProduto)
                        {
                            var validacao = Domain.Vendas.PreVendaRepository.ValidarEstoquePreVenda(ivp.PreVenda.Id);
                            if (!string.IsNullOrEmpty(validacao))
                                throw new Exception(validacao);
                        }
                    }

                RegistrarDividasDeixadasPeloClienteNoEstabelecimento(transacao);
                RealizarPagamentoDeDividaDoCliente(transacao);

                //--- Abre todo //TODO:duvida - henrique >> essa parte tem eventos que poderiam ser chamados em paralelo com controle de erros
                //>> tem que pelo menos colocar em um método separado antes de fazer o merge com baseline

                if (checkoutDTO.JaTeveCaixaPorProfissionalAberto && checkoutDTO.ControlarCaixaSeNecessario)
                    Domain.Financeiro.ControleDeCaixaService.ManterMovimentacaoDeTransacaoDePagamento(transacao, transacao.PessoaQueRealizou);

                int totalDePontosNaTransacao = 0;
                int totalDePontosGanhos = 0;
                int totalDePontosTransferidos = 0;
                bool recebeEmailProgramaFidelidade = false;
                GerarMovimentacoesDePontosDeFidelidadeNoCheckOut(transacao, venda, out recebeEmailProgramaFidelidade, out totalDePontosNaTransacao, out totalDePontosGanhos, out totalDePontosTransferidos, checkoutDTO.PagamentoAntecipado);

                if (transacao.Vendas == null && venda != null)
                    transacao.Vendas = new List<Venda> { venda };

                if (possuiCashback)
                {
                    Domain.Cashback.CashbackService.PersistirDadosCashbackVinculadosAoFechamento(dadosCashback);
                }

                Domain.Financeiro.CalculoComissaoService.DefinirComissaoDaTransacao(transacao.Id);

                Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificacaoAoFinalDoFechamentoDeConta(transacao, venda, recebeEmailProgramaFidelidade, totalDePontosNaTransacao, totalDePontosGanhos);

                foreach (var benefecioUsado in checkoutDTO.BeneficiosUsados)
                {
                    Domain.ClubeDeAssinaturas.ConsumoDeAssinaturaService.RegistrarBeneficioUsado(benefecioUsado);
                }

                resultado.TotalDePontosTransferidos = totalDePontosTransferidos;

                if (transacao.PessoaQuePagou != null)
                {
                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
                    if (Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.EstabelecimentoPossuiChaveParaIntegracaoComOutrosSistemas(estabelecimento))
                        EnviarEventoDeTransacaoParaIntegracao(transacao, TipoDeEventoEnum.FechamentoDeConta);

                    GerarPesquisaDeSatisfacao(transacao, estabelecimento);
                }

                if (checkoutDTO.ControlarPagamentoPOS)
                {
                    var configPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

                    if (configPOS != null && configPOS.TipoPOS != null && configPOS.TipoPOS.EhSplitStone && checkoutDTO.IdTransacaoPOS.HasValue)
                    {
                        var splitRealizado = await Domain.Financeiro.SubadquirenteService.RealizarSplitSeNecessario(transacao.Id);

                        //No caso da stone, "AtualizarValoresDeComissaoParaRefletirSplit" só será executado após confirmação de split processado, com o status da solicitação atualizado via webhook ou job
                        throw new NotImplementedException();
                        //if (splitRealizado && !configPOS.TipoPOS.EhSplitStone) {
                        //    Domain.Financeiro.SplitService.AtualizarValoresDeComissaoParaRefletirSplit(transacao);
                        //}

                        //if (configPOS.TipoPOS.EhSplitStone) {
                        //    Domain.Financeiro.SubadquirenteService.AtualizarStatusPosSplits();
                        //}
                    }
                }

                if (possuiCashback)
                {
                    var dadosCashbackComissao = Domain.Cashback.CashbackService.GerarDadosCashbackComissao(transacao, venda);
                    Domain.Cashback.CashbackService.PersistirDadosComissaoCashbackVinculadosAoFechamento(dadosCashbackComissao);
                }
                //---fecha todo

                checkoutDTO.EmitirNFC = checkoutDTO.EmitirNFC && !TransacaoUtilizaCreditoDePagamentoOnline(transacao);
                // NÃO COLOCAR NADA DEPOIS DESTE MÉTODO PARA NÃO TER RISCO DE ROLLBACK APÓS EMISSÃO DE NFC
                if (checkoutDTO.EmitirNFC)
                    await EmitirNFC(transacao, venda, quemMarcou);
                // NÃO COLOCAR NADA DEPOIS DESTE MÉTODO PARA NÃO TER RISCO DE ROLLBACK APÓS EMISSÃO DE NFC
            }
            else
            {
                if (transacao.Id > 0)
                    transacao.Refresh();
            }

            resultado.Transacao = transacao;

            if (checkoutDTO.IdCupom.HasValue)
                Domain.Cupom.CupomPessoaFisicaService.AssociarCupomATransacao(checkoutDTO.IdCupom.Value, transacao.Id);

            return resultado;
        }

        private static void CriaAssinaturaRecorrente(Transacao transacao, Venda venda, PacoteCliente pacoteCliente)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository
                .ObterClienteEstabelecimentoPorPF(transacao.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);

            AssinaturaRecorrenteDTO assinatura = new AssinaturaRecorrenteDTO()
            {
                IdClienteEstabelecimento = clienteEstabelecimento.Codigo,
                IdPacote = pacoteCliente.PacoteOriginal.Id,
                IdPacoteCliente = pacoteCliente.Id,
                DataDaUltimaRenovacao = venda.Data,
                UltimoValorPago = venda.ItensVenda.FirstOrDefault(i => i is ItemVendaPacote iv && iv.PacoteCliente.Id == pacoteCliente.Id).ObterValorPago(),
                StatusDaAssinatura = RecorrenciaDeAssinatura.Enums.StatusDaAssinaturaRecorrenteEnum.Ativa
            };

            Domain.RecorrenciaDeAssinatura.AssinaturaRecorrenteService.SaveOrUpdate(assinatura);
        }

        private static void AtualizarDadosDaAssinaturaRecorrente(Venda venda, int? idAssinaturaRecorrente, ref List<PacoteCliente> pacotesClientes)
        {
            var recorrencia = Domain.RecorrenciaDeAssinatura.AssinaturaRecorrenteRepository.Load(idAssinaturaRecorrente.Value);
            var pacoteCliente = pacotesClientes.FirstOrDefault(f => f.PacoteOriginal.Id == recorrencia.Pacote.Id);
            if (pacoteCliente == null)
                return;

            recorrencia.PacoteCliente = pacoteCliente;
            recorrencia.DataDaUltimaRenovacao = venda.Data;
            recorrencia.UltimoValorPago = venda.ItensVenda.FirstOrDefault(i => i is ItemVendaPacote iv && iv.PacoteCliente.Id == recorrencia.PacoteCliente.Id).ObterValorPago();
            recorrencia.StatusDaAssinatura = RecorrenciaDeAssinatura.Enums.StatusDaAssinaturaRecorrenteEnum.Ativa;
            Domain.RecorrenciaDeAssinatura.AssinaturaRecorrenteRepository.Update(recorrencia);

            pacotesClientes.Remove(pacoteCliente);
        }

        private void RegistrarDividasDeixadasPeloClienteNoEstabelecimento(Transacao transacao)
        {
            var transacaoComClienteIdentificado = transacao.PessoaQuePagou != null;
            if (!transacaoComClienteIdentificado)
                return;

            var tfpDeDividas = transacao.ListarLancamentosDeDividas();

            foreach (var tfpDeDivida in tfpDeDividas)
            {
                Domain.DebitoParcial.RegistroDeDividaService.CadastrarDividaDeixadaPeloClienteNoEstabelecimento(
                    new DebitoParcial.DTO.CadastrarDividaDTO
                    {
                        IdTransacao = transacao.Id,
                        IdTransacaoFormaPagamento = tfpDeDivida.IdTransacaoFormaPagamento,
                        IdPessoaDoCliente = transacao.PessoaQuePagou.IdPessoa,
                        IdPessoaDoEstabelecimento = transacao.PessoaQueRecebeu.IdPessoa,
                        IdPessoaQueRegistrou = transacao.PessoaQueRealizou.IdPessoa,
                        DataQueDeixouDivida = transacao.DataHora,
                        ValorInicialDaDivida = tfpDeDivida.ValorPago
                    });
            }
        }

        private void RealizarPagamentoDeDividaDoCliente(Transacao transacao)
        {
            if (!transacao.TemClienteIdentificado())
                return;

            var tfpsDePagamentoEmDividas = transacao.ListarPagamentosDeDividas();

            foreach (var tfpPagamentoDivida in tfpsDePagamentoEmDividas)
            {
                Domain.DebitoParcial.PagamentoDeDividaService.RealizarPagamentoDeDividaDoCliente(
                    new DebitoParcial.DTO.RealizarPagamentoDeDividaDTO
                    {
                        IdPessoaEstabelecimento = transacao.PessoaQueRecebeu.IdPessoa,
                        IdPessoaCliente = transacao.PessoaQuePagou.IdPessoa,
                        IdPessoaQueRegistrou = transacao.PessoaQueRealizou.IdPessoa,
                        DataHoraPagamento = transacao.DataHora,
                        ValorPago = tfpPagamentoDivida.ValorPago.ToValorPositivo(),
                        IdTransacao = transacao.Id,
                        IdTransacaoFormaPagamento = tfpPagamentoDivida.IdTransacaoFormaPagamento
                    });
            }
        }

        private void EstornarPagamentoDeDividaDoCliente(Transacao transacao, int idPessoaQueAlterou)
        {
            if (!transacao.TemClienteIdentificado())
                return;

            var tfpsDePagamentoEmDividas = transacao.ListarPagamentosDeDividas();
            foreach (var tfpPagamentoDivida in tfpsDePagamentoEmDividas)
            {
                Domain.DebitoParcial.PagamentoDeDividaService.EstornarPagamento(tfpPagamentoDivida.IdTransacaoFormaPagamento, idPessoaQueAlterou);
            }
        }

        private void ValidarEstornoDeDivida(Transacao transacao)
        {
            if (!transacao.TemClienteIdentificado())
                return;

            var tfpsDePagamentoEmDividas = transacao.ListarLancamentosDeDividas();
            foreach (var tfpPagamentoDivida in tfpsDePagamentoEmDividas)
            {
                if (!Domain.DebitoParcial.RegistroDeDividaService.ValidarEstornoDeDivida(tfpPagamentoDivida.IdTransacaoFormaPagamento))
                    ValidationHelper.Instance.AdicionarItemValidacao("Não é possível estornar dívidas que já tiveram pagamento feito.");
            }
        }

        private void EstornarDividaDoCliente(Transacao transacao, int idPessoaQueAlterou)
        {
            if (!transacao.TemClienteIdentificado())
                return;

            var tfpsDePagamentoEmDividas = transacao.ListarLancamentosDeDividas();
            foreach (var tfpPagamentoDivida in tfpsDePagamentoEmDividas)
            {
                Domain.DebitoParcial.RegistroDeDividaService.EstornarDivida(tfpPagamentoDivida.IdTransacaoFormaPagamento, idPessoaQueAlterou);
            }
        }

        public RetornoPagamentoPosDto RealizarPagamentoPOS(PessoaFisica quemFechou, Estabelecimento estabelecimento,
            Transacao transacaoFicticia, Venda vendaFicticia, decimal valorAPagar, TipoPagamentoEnum tipoPagamento,
            TipoParcelamentoEnum? tipoParcelamento, int? numParcelas, string terminalSelecionado = null)
        {
            var estabelecimentoConfigPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            PreencherTransacaoComDadosDePagamentoEComissaoDoCheckout(transacaoFicticia, quemFechou, vendaFicticia, new List<PreVenda>());

            if (estabelecimentoConfigPos.TipoPOS.Id == (int)TipoPosEnum.ConnectStone || estabelecimentoConfigPos.TipoPOS.Id == (int)TipoPosEnum.ConnectPagarme)
                PreencherDataUltimaTransacaoEmEstabelecimentoTerminalPos(estabelecimento.IdEstabelecimento, terminalSelecionado);

            return Domain.Financeiro.SubadquirenteService
                .RealizarPagamentoPOS(
                    transacaoFicticia,
                    estabelecimentoConfigPos,
                    valorAPagar,
                    tipoPagamento,
                    tipoParcelamento,
                    numParcelas,
                    terminalSelecionado);
        }

        public bool CancelarPreTransacaoPOS(string codigoPreTransacao)
        {
            var subadquirente = new SubadquirenteService();
            return subadquirente.CancelarPreTransacaoPos(codigoPreTransacao);
        }

        public bool CapturarTransacaoComSplit(Estabelecimento estabelecimento, SplitDTO split) {
            return Domain.Financeiro.SubadquirenteService.CapturarPedidoComSplit(estabelecimento.IdEstabelecimento, split);
        }

        public void PreencherDataUltimaTransacaoEmEstabelecimentoTerminalPos(int idEstabelecimento, string terminalSelecionado)
        {
            var terminalPos = Domain.Belezinha.EstabelecimentoTerminalPosRepository.Queryable().FirstOrDefault(etp => 
                    etp.EstabelecimentoConfigPos.Estabelecimento.IdEstabelecimento == idEstabelecimento && etp.SerialNumber == terminalSelecionado);

            if(terminalPos == null)
                return;

            terminalPos.SetDataDaUltimaTransacao(Calendario.Agora());
        }

        private int PreencherTransacaoComDadosDePagamentoEComissaoDoCheckout(Transacao transacao, PessoaFisica quemMarcou, Venda venda, List<PreVenda> preVendasParaEfetivar)
        {
            int numeroDoFechamentoGerado;
            AjustarDataReferencia(transacao);

            if (venda != null)
                preVendasParaEfetivar.AddRange(venda.ItensVenda.Where(f => f is ItemVendaProduto && ((ItemVendaProduto)f).VeioDeUmaPreVenda())
                                                                                .Select(p => ((ItemVendaProduto)p).PreVenda));

            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                var preVenda = Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(horarioTransacao.Horario.Id);
                if (preVenda != null && preVenda.Id > 0)
                    preVendasParaEfetivar.Add(preVenda);
            }

            numeroDoFechamentoGerado = Domain.Financeiro.TransacaoService.GerarEGravarNumeroDoFechamento(transacao.PessoaQueRecebeu);
            transacao.NumeroDaPreVenda = numeroDoFechamentoGerado;

            RecalcularTotais(transacao, venda);

            transacao.PessoaQueRealizou = quemMarcou;
            foreach (var f in transacao.FormasPagamento.Where(f => f.PessoaQuePagou == null))
            {
                f.PessoaQuePagou = transacao.PessoaQuePagou;
            }

            return numeroDoFechamentoGerado;
        }

        private bool TransacaoUtilizaCreditoDePagamentoOnline(Transacao transacao)
        {
            return transacao.FormasPagamento.Any(f => f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnline 
                                                      || f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite);
        }

        private void GerarPesquisaDeSatisfacao(Transacao transacao, Estabelecimento estabelecimento)
        {
            Domain.Estabelecimentos.GerarPesquisaDeSatisfacaoService.GerarPesquisaDeSatisfacao(transacao, estabelecimento);
        }

        private void GerarMovimentacoesDePontosDeFidelidadeNoCheckOut(Transacao transacao, Venda venda, out bool recebeEmailProgramaFidelidade, out int totalPontosMovimentados, out int totalDePontosGanhos, out int totalDePontosTransferidos, PagamentoAntecipado pagamentoAntecipado = null)
        {
            totalPontosMovimentados = 0;
            totalDePontosTransferidos = 0;
            totalDePontosGanhos = 0;

            recebeEmailProgramaFidelidade = false;

            var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento;
            if (!estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaProgramaDeFidelidade)
                return;

            var pessoaFisicaDoCliente = transacao.PessoaQuePagou;
            if (pessoaFisicaDoCliente == null)
                return;

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(pessoaFisicaDoCliente.IdPessoa, estabelecimento.IdEstabelecimento);
            if (clienteEstabelecimento != null)
            {
                var programaDeFidelidade = Domain.Fidelidade.ProgramaDeFidelidadeRepository.ObterUltimaConfiguracoesDoEstabelecimento(estabelecimento.IdEstabelecimento);
                if (programaDeFidelidade == null)
                    return;

                SalvarMovimentacoesDasVendasEServicos(transacao, venda, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, out totalPontosMovimentados, out totalDePontosGanhos, out totalDePontosTransferidos, pagamentoAntecipado);

                clienteEstabelecimento.SaldoDePontosDeFidelidade += totalPontosMovimentados;
                recebeEmailProgramaFidelidade = clienteEstabelecimento.RecebeEmailProgramaFidelidade;
                transacao.SaldoDePontosDeFidelidadeDoClienteAposEfetuarTransacao = clienteEstabelecimento.SaldoDePontosDeFidelidade;
            }
        }

        private void SalvarMovimentacoesDasVendasEServicos(Transacao transacao, Venda venda, Estabelecimento estabelecimento, ProgramaDeFidelidade programaDeFidelidade, PessoaFisica pessoaFisicaDoCliente, out int totalPontosMovimentados, out int totalDePontosGanhos, out int totalDePontosTransferidos, PagamentoAntecipado pagamentoAntecipado = null)
        {
            List<MovimentacaoDePontos> movimentacoesDePontosParaGerar = new List<MovimentacaoDePontos>();

            AdicionarMovimentacaoDeAgendamentoOnline(transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, movimentacoesDePontosParaGerar);
            AdicionarMovimentacoesDeServicos(transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, movimentacoesDePontosParaGerar);
            AdicionarMovimentacoesDeVendas(transacao, venda, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, movimentacoesDePontosParaGerar);
            AdicionarMovimentacaoDePagamentoOnline(transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, pagamentoAntecipado, movimentacoesDePontosParaGerar);

            Domain.Fidelidade.MovimentacaoDePontosSevice.RegistrarMovimentacoesDePontos(movimentacoesDePontosParaGerar, transacao, out totalPontosMovimentados, out totalDePontosGanhos, out totalDePontosTransferidos);

            Domain.Vendas.VendaRepository.Update(venda);
            Domain.Financeiro.TransacaoRepository.Update(transacao);
        }

        private void AdicionarMovimentacoesDeVendas(Transacao transacao, Venda venda, Estabelecimento estabelecimento, ProgramaDeFidelidade programaDeFidelidade, PessoaFisica pessoaFisicaDoCliente, List<MovimentacaoDePontos> movimentacoesDePontos)
        {
            foreach (var itemVenda in venda.ItensVenda)
            {
                if (itemVenda is ItemVendaPacote || itemVenda is ItemVendaProduto)
                {
                    bool foiPossivelMovimentarPontos = false;

                    var operacaoDaMovimentacao = itemVenda.UsouPontosDeFidelidade ? OperacaoDaMovimentacaoDePontosEnum.UsoDePontos : OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos;

                    var movimentacaoDePontos = Domain.Fidelidade.MovimentacaoDePontosSevice.CriarMovimentacaoDePontosParaSerRegistrada(operacaoDaMovimentacao, transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, (IMovimentaPontosDeFidelidade)itemVenda, itemVenda.ObterDescricaoDoItem(), out foiPossivelMovimentarPontos);

                    if (foiPossivelMovimentarPontos)
                    {
                        movimentacoesDePontos.Add(movimentacaoDePontos);
                        itemVenda.PontosDeFidelidade = movimentacaoDePontos.QuantidadeDePontos;
                    }
                }
            }
        }

        private void AdicionarMovimentacoesDeServicos(Transacao transacao, Estabelecimento estabelecimento, ProgramaDeFidelidade programaDeFidelidade, PessoaFisica pessoaFisicaDoCliente, List<MovimentacaoDePontos> movimentacoesDePontos)
        {
            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                bool foiPossivelMovimentarPontos = false;

                var operacaoDaMovimentacao = horarioTransacao.UsouPontosDeFidelidade ? OperacaoDaMovimentacaoDePontosEnum.UsoDePontos : OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos;

                var movimentacaoDePontos = Domain.Fidelidade.MovimentacaoDePontosSevice.CriarMovimentacaoDePontosParaSerRegistrada(operacaoDaMovimentacao, transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, (IMovimentaPontosDeFidelidade)horarioTransacao, horarioTransacao.Horario.ServicoEstabelecimento.Nome, out foiPossivelMovimentarPontos);

                if (foiPossivelMovimentarPontos)
                {
                    movimentacoesDePontos.Add(movimentacaoDePontos);
                    horarioTransacao.PontosDeFidelidade = movimentacaoDePontos.QuantidadeDePontos;
                }
            }
        }

        private void AdicionarMovimentacaoDeAgendamentoOnline(Transacao transacao, Estabelecimento estabelecimento, ProgramaDeFidelidade programaDeFidelidade, PessoaFisica pessoaFisicaDoCliente, List<MovimentacaoDePontos> movimentacoesDePontos)
        {
            HorarioTransacao horarioTransacaoAgendadoOnline = transacao.ObterPrimeiroHorarioTransacaoAgendadoOnline();
            if (horarioTransacaoAgendadoOnline != null)
            {
                bool foiPossivelMovimentarPontos = false;
                var nomeDoUsuario = transacao.PessoaQueRealizou.NomeCompleto;
                var agendamentoOnlineQueGerouPontos = new AgendamentoOnlineQueGerouPontos(horarioTransacaoAgendadoOnline.Horario, transacao.Id);

                var movimentacaoDePontosAgendamentoOnline = Domain.Fidelidade.MovimentacaoDePontosSevice
                    .CriarMovimentacaoDePontosParaSerRegistrada(OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos, transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, (IMovimentaPontosDeFidelidade)agendamentoOnlineQueGerouPontos, "Agendamento Online - conta fechada por " + nomeDoUsuario + " em " + transacao.DataHora.ToDataHoraString(), out foiPossivelMovimentarPontos);

                if (foiPossivelMovimentarPontos)
                {
                    movimentacoesDePontos.Add(movimentacaoDePontosAgendamentoOnline);
                    agendamentoOnlineQueGerouPontos.PontosDeFidelidade = movimentacaoDePontosAgendamentoOnline.QuantidadeDePontos;
                }
            }
        }

        private void AdicionarMovimentacaoDePagamentoOnline(Transacao transacao, Estabelecimento estabelecimento, ProgramaDeFidelidade programaDeFidelidade, PessoaFisica pessoaFisicaDoCliente, PagamentoAntecipado pagamentoAntecipado, List<MovimentacaoDePontos> movimentacoesDePontos)
        {
            if (pagamentoAntecipado == null)
                return;

            if (!TransacaoEhDePagamentoOnline(transacao))
                return;

            if (pagamentoAntecipado.Beneficios.ProgramaDeFidelidadePossuiBeneficiosConfigurados() && pagamentoAntecipado.Beneficios.DisponivelAoCliente)
            {
                bool movimentacaoDePontosFoiGerada = false;
                string nomeDoCliente = pessoaFisicaDoCliente.NomeCompleto;
                string descricaoDoItem = "Pagamento Online - realizado por " + nomeDoCliente + " em " + transacao.DataHora.ToDataHoraString();

                var pagamentoOnlineQueGerouPontos = new PagamentoAntecipadoQueGerouPontos(pagamentoAntecipado);

                var movimentacaoDePontosPagamentoOnline = Domain.Fidelidade.MovimentacaoDePontosSevice
                    .CriarMovimentacaoDePontosParaSerRegistrada(OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos, transacao, estabelecimento, programaDeFidelidade, pessoaFisicaDoCliente, pagamentoOnlineQueGerouPontos, descricaoDoItem, out movimentacaoDePontosFoiGerada);

                if (movimentacaoDePontosFoiGerada)
                {
                    movimentacoesDePontos.Add(movimentacaoDePontosPagamentoOnline);
                }
            }
        }

        private bool TransacaoEhDePagamentoOnline(Transacao transacao)
        {
            return Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhDePagamentoOnline(transacao.Id);
        }

        private async Task EmitirNFC(Transacao transacao, Venda venda, PessoaFisica pessoaQueEmitiu)
        {
            if (EhParaGerarNotaFiscalDoConsumidor(transacao))
            {
                Estabelecimento estabelecimentoTransacao = Domain.Financeiro.TransacaoRepository.ObterEstabelecimentoDaTransacao(transacao);

                if (estabelecimentoTransacao.ConfiguracaoDeNFC != null &&
                    estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.NotaFiscalEletronica)
                {
                    var certificadoDigitalEstaValido = Domain.Pessoas.PessoaJuridicaConfiguracaoNFeService.PossuiCertificadoDentroDaValidade(estabelecimentoTransacao.PessoaJuridica);
                    if (!certificadoDigitalEstaValido)
                        ValidationHelper.Instance.AdicionarItemValidacao("O certificado digital do estabelecimento está expirado.\nPara enviar o novo certificado, acesse Configurações > Nota Fiscal do Consumidor.\n");

                    if (String.IsNullOrEmpty(estabelecimentoTransacao.ConfiguracaoDeNFC.CSC) || String.IsNullOrEmpty(estabelecimentoTransacao.ConfiguracaoDeNFC.IdentificadorCSC))
                        ValidationHelper.Instance.AdicionarItemValidacao("Para emitir a NFCe é necessário informar os dados do Código de Segurança do Contribuinte (CSC) na aba Nota Fiscal do Consumidor em Configurações >> Configurações de adicionais >> Nota Fiscal de Consumidor\n");

                    //if (estabelecimentoTransacao.ConfiguracaoDeNFC.TipoIntegracaoPagamento == (int)TipoIntegracaoPagamentoEnum.PagamentoIntegradoComOSistemaDeAutomacao && (transacao.TotalPagoEmCredito > 0 || transacao.TotalPagoEmDebito > 0))
                    //ValidationHelper.Instance.AdicionarItemValidacao("Seu estabelecimento está configurado com pagamento integrado ao Trinks (TEF), mas a emissão da NFCe ainda não está preparada com esta configuração. A nota não poderá ser emitida. Favor entrar em contato com o nosso suporte.\n");

                    if (transacao.PessoaQuePagou != null)
                    {
                        var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(transacao.PessoaQuePagou.IdPessoaFisica, estabelecimentoTransacao.IdEstabelecimento);

                        var podeUsarCnpjNfce = false;
                        if (clienteEstabelecimento != null)
                        {
                            podeUsarCnpjNfce = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                            .ObterDisponibilidadeDeRecurso(clienteEstabelecimento.Estabelecimento, Recurso.UsarCnpjClienteNfce).EstaDisponivel;
                        }

                        var possuiCpfOuCnpj = !String.IsNullOrEmpty(transacao.PessoaQuePagou.PessoaFisica.Cpf)
                            || (podeUsarCnpjNfce && !String.IsNullOrEmpty(clienteEstabelecimento.CNPJ));

                        if (transacao.TotalPagar > new ParametrosTrinks<decimal>(ParametrosTrinksEnum.valor_maximo_emissao_nota).ObterValor() && (!possuiCpfOuCnpj || String.IsNullOrEmpty(transacao.PessoaQuePagou.PessoaFisica.NomeCompleto) || (clienteEstabelecimento != null && !clienteEstabelecimento.EnderecoTemAlgumaInformacao())))
                            ValidationHelper.Instance.AdicionarItemValidacao("Para a emissão de notas superiores a " + new ParametrosTrinks<decimal>(ParametrosTrinksEnum.valor_maximo_emissao_nota).ObterValor().ToString("C") + " é obrigatório o preenchimento do CPF, nome completo e endereço do cliente. Atualize as informações no cadastro deste cliente e solicite novamente a emissão da nota.");
                    }
                }

                if (ValidationHelper.Instance.IsValid)
                {
                    if (estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Autocom)
                    {
                        GerarDadosParaAIntegracaoComNotaFiscalDoConsumidor(transacao, desconsiderarSeTeveDescontoOuNao: true);
                    }
                    else
                    {
                        var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == estabelecimentoTransacao.PessoaJuridica.IdPessoa);
                        var serialCertificado = pessoaJuridicaCertificadoDigital != null ? pessoaJuridicaCertificadoDigital.SerialCertificado : null;

                        if (estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.NotaFiscalEletronica
                            && String.IsNullOrEmpty(serialCertificado))
                        {
                            transacao.NumeroDaPreVenda = -1;
                        }
                        else
                        {
                            try
                            {
                                transacao.Vendas = new List<Venda>();
                                transacao.Vendas.Add(venda);
                                var numeroNota = await GerarEEmitirNotaFiscal(transacao, pessoaQueEmitiu);
                            }
                            catch (NFCeException ex)
                            {
                                ValidationHelper.Instance.AdicionarItemAlerta(ex.Message);
                            }
                        }
                    }
                }
            }
        }

        private void AjustarDataReferencia(Transacao transacao)
        {
            transacao.DataReferencia = transacao.DataHora;
            if (transacao.HorariosTransacoes.Any())
            {
                var menorData = transacao.HorariosTransacoes.Min(f => f.DataHoraInicioHorario ?? Calendario.Agora()).Date;
                var maiorData = transacao.HorariosTransacoes.Max(f => f.DataHoraInicioHorario ?? Calendario.Agora()).Date;

                if (transacao.DataReferencia.Date < menorData || transacao.DataReferencia.Date > maiorData)
                {
                    transacao.DataReferencia = menorData;
                }
            }
        }

        private bool VerificarPermissaoEmissaoPagamentoOnlineCasoExista(Transacao transacao, Estabelecimento estabelecimentoTransacao)
        {
            var possuiPagamentoOnline = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhDePagamentoOnline(transacao.Id);
            var possuiConsumoDePagamentoOnline = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhConsumoDePagamentoOnline(transacao.Id);
            var liberarEmissaoNfcPagamentoOnline = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(estabelecimentoTransacao, Recurso.LiberarEmissaoNfcPagamentoOnline).EstaDisponivel;

            return liberarEmissaoNfcPagamentoOnline || (!possuiPagamentoOnline && !possuiConsumoDePagamentoOnline);
        }

        [TransactionInitRequired]
        public async Task<int> GerarEEmitirNotaFiscal(Transacao transacao, PessoaFisica pessoaQueEmitiu = null)
        {
            int numeroNFC = 0;
            Estabelecimento estabelecimentoTransacao = Domain.Financeiro.TransacaoRepository.ObterEstabelecimentoDaTransacao(transacao);

            var podeEmitirEssaNota = VerificarPermissaoEmissaoPagamentoOnlineCasoExista(transacao, estabelecimentoTransacao);

            if (!podeEmitirEssaNota)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível emitir nota fiscal para esta transação.");
                return 0;
            }

            var descontoPacotesEhTotal = (transacao.DescontosPacotes.HasValue && (transacao.DescontosPacotes + transacao.TotalPacotes == 0)) || !transacao.DescontosPacotes.HasValue;
            var descontoProdutoEhTotal = transacao.DescontosProdutos.HasValue && (transacao.DescontosProdutos + transacao.TotalProdutos == 0) && descontoPacotesEhTotal;
            var registros = Domain.RPS.DadosRPSService.ListarDadosParaGeracaoDeRPS(new ParametrosFiltrosLotesRPS
            {
                IdsTransacao = new List<int> { transacao.Id },
                IdPessoaDaPessoaJuridica = estabelecimentoTransacao.PessoaJuridica.IdPessoa,
                Estabelecimento = estabelecimentoTransacao
            });

            if (estabelecimentoTransacao.EstabelecimentoPossuiNFC() && !descontoProdutoEhTotal || estabelecimentoTransacao.ConfiguracaoDeNFC.IncluirServicosNaNotaConsiderandoLocalizacao())
            {
                //if (estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Sat) {
                //    ValidarEmissaoNotaFiscalConsumidorSAT(estabelecimentoTransacao);

                //    if (ValidationHelper.Instance.IsValid) {
                //        GerarDadosParaAIntegracaoComNotaFiscalDoConsumidor(transacao);
                //        Domain.NotaFiscalDoConsumidor.NotaNFCService.GerarNotaNFC(transacao, pessoaQueEmitiu, false, registros);
                //    }
                //}
                //else
                if (estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Autocom)
                {
                    GerarDadosParaAIntegracaoComNotaFiscalDoConsumidor(transacao);
                }
                else
                {
                    var pessoaJuridicaCertificado = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == estabelecimentoTransacao.PessoaJuridica.IdPessoa);
                    var serialCertificado = pessoaJuridicaCertificado != null ? pessoaJuridicaCertificado.SerialCertificado : null;
                    if (estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.NotaFiscalEletronica && String.IsNullOrEmpty(serialCertificado))
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao(
                            "Nota não pode ser emitida, pois não existe certificado digital associado a este estabelecimento. O upload do certificado digital precisa ser feito em Visualizar/Editar > Site e Estabelecimento > Nota Fiscal do Consumidor.");
                    }
                    if (String.IsNullOrEmpty(transacao.PessoaQueRecebeu.PessoaJuridica.CNPJ))
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("Para que a nota fiscal possa ser emitida para vendas com este produto, é necessário que o CNPJ do estabelecimento seja informado em Visualizar/Editar > Site e Estabelecimento  na aba Dados do Estabelecimento");
                    }

                    if (!ValidationHelper.Instance.IsValid)
                        return 0;

                    var geradorDeNF = GeradorDeDadosParaNotaFiscalFactory.ConstruirGeradorDeNF(null, transacao, estabelecimentoTransacao, registros);
                    numeroNFC = await geradorDeNF.EmitirNotaFiscal();
                }
            }
            else
            {
                if (estabelecimentoTransacao.EstabelecimentoPossuiNFC())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Não é possivel emitir nota fiscal de vendas com 100% de desconto");
                }
            }

            if (numeroNFC > 0)
                Domain.NotaFiscalDoConsumidor.NotaNFCService.IncrementarNumeracaoDaNFe(estabelecimentoTransacao.ConfiguracaoDeNFC);

            return numeroNFC;
        }

        [TransactionInitRequired]
        public async Task<string> GerarNFCeParaDownload(Transacao transacao, PessoaFisica pessoaQueEmitiu = null)
        {
            string xml = null;
            Estabelecimento estabelecimentoTransacao = Domain.Financeiro.TransacaoRepository.ObterEstabelecimentoDaTransacao(transacao);
            var descontoPacotesEhTotal = (transacao.DescontosPacotes.HasValue && (transacao.DescontosPacotes + transacao.TotalPacotes == 0)) || !transacao.DescontosPacotes.HasValue;
            var descontoProdutoEhTotal = transacao.DescontosProdutos.HasValue && (transacao.DescontosProdutos + transacao.TotalProdutos == 0) && descontoPacotesEhTotal;
            var registros = Domain.RPS.DadosRPSService.ListarDadosParaGeracaoDeRPS(new ParametrosFiltrosLotesRPS
            {
                IdsTransacao = new List<int> { transacao.Id },
                IdPessoaDaPessoaJuridica = estabelecimentoTransacao.PessoaJuridica.IdPessoa,
                Estabelecimento = estabelecimentoTransacao
            });

            if (estabelecimentoTransacao.EstabelecimentoPossuiNFC() && !descontoProdutoEhTotal || estabelecimentoTransacao.ConfiguracaoDeNFC.IncluirServicosNaNotaConsiderandoLocalizacao())
            {
                var pessoaJuridicaCertificado = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == estabelecimentoTransacao.PessoaJuridica.IdPessoa);
                var serialCertificado = pessoaJuridicaCertificado != null ? pessoaJuridicaCertificado.SerialCertificado : null;
                if (estabelecimentoTransacao.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.NotaFiscalEletronica && String.IsNullOrEmpty(serialCertificado))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "Nota não pode ser emitida, pois não existe certificado digital associado a este estabelecimento. O upload do certificado digital precisa ser feito em Visualizar/Editar > Site e Estabelecimento > Nota Fiscal do Consumidor.");
                }
                if (String.IsNullOrEmpty(transacao.PessoaQueRecebeu.PessoaJuridica.CNPJ))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Para que a nota fiscal possa ser emitida para vendas com este produto, é necessário que o CNPJ do estabelecimento seja informado em Visualizar/Editar > Site e Estabelecimento  na aba Dados do Estabelecimento");
                }

                if (!ValidationHelper.Instance.IsValid)
                    return null;

                var geradorDeNF = GeradorDeDadosParaNotaFiscalFactory.ConstruirGeradorDeNF(null, transacao, estabelecimentoTransacao, registros);
                xml = await geradorDeNF.GerarNFAssinadaDownload();
            }
            else
            {
                if (estabelecimentoTransacao.EstabelecimentoPossuiNFC())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Não é possivel emitir nota fiscal de vendas com 100% de desconto");
                }
            }

            return xml;
        }

        [TransactionInitRequired]
        public bool CancelarNFCe(Transacao transacao)
        {
            Estabelecimento estabelecimento = Domain.Financeiro.TransacaoRepository.ObterEstabelecimentoDaTransacao(transacao);
            if (estabelecimento != null && estabelecimento.EstabelecimentoPossuiNFC())
            {
                if (estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Autocom)
                {
                    //GerarDadosParaAIntegracaoComNotaFiscalDoConsumidor(transacao, out numeroDoFechamentoGerado);
                }
                else
                {
                    bool podeEmitirNota = true;

                    var numeroDoUltimoCFEmitido = estabelecimento.ConfiguracaoDeNFC.NumeroDoUltimoCFEmitido;
                    var nota = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(transacao);
                    //if (estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Sat)
                    //    podeEmitirNota = (nota != null && numeroDoUltimoCFEmitido == nota.NumeroNota && Calendario.Agora() <= nota.DataVenda.AddMinutes(30));

                    if (podeEmitirNota)
                    {
                        var geradorDeNF = GeradorDeDadosParaNotaFiscalFactory.ConstruirGeradorDeNF(null, transacao, estabelecimento);
                        geradorDeNF.CancelarNF(transacao.ComentarioEstorno);
                    }
                }
            }

            return true;
        }

        #region MontarTransacaoFormaPagaemnto

        public TransacaoFormaPagamento MontarTransacaoFormaPagamento(Transacao transacao, int idFormaPagamento, int idEstabelecimento,
            decimal valorPago, List<ParcelaDePagamentoDTO> parcelas = null, int idTransacaoFormaPagamento = 0, int idPessoaQuePagou = 0, int idValePresente = 0)
        {
            if (parcelas == null)
                parcelas = new List<ParcelaDePagamentoDTO> { new ParcelaDePagamentoDTO() { DataHora = transacao.DataHora, NumeroParcela = 1, Valor = valorPago } };

            var retorno = idTransacaoFormaPagamento == 0
            ? new TransacaoFormaPagamento { Transacao = transacao }
            : Domain.Financeiro.TransacaoFormaPagamentoRepository.Load(idTransacaoFormaPagamento);

            var estFormaPagamento =
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(
                    idEstabelecimento, idFormaPagamento);
            retorno.FormaPagamento = estFormaPagamento.FormaPagamento;

            retorno.DiasParaReceberDaOperadora = estFormaPagamento.DiasParaReceberDaOperadora;

            retorno.ValorPago = valorPago;
            retorno.ValorBaseCalculoPercentualDesconto = valorPago;
            retorno.PessoaQuePagou = idPessoaQuePagou == 0 && transacao != null
                ? transacao.PessoaQuePagou
                : Domain.Pessoas.PessoaRepository.Load(idPessoaQuePagou);

            retorno.ValePresente = (idValePresente == 0)
                ? null
                : Domain.Vendas.ValePresenteRepository.Load(idValePresente);

            retorno.PercentualCobradoPelaOperadora =
                Domain.Pessoas.EstabelecimentoFormaPagamentoService.ObterPercentualDescontoOperadora(estFormaPagamento,
                    retorno.NumeroParcelas);

            var usouAlgumTipoDePagamentoOnline =
                estFormaPagamento.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnline ||
                estFormaPagamento.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnlinePorLink ||
                estFormaPagamento.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnlineHotsite ||
                estFormaPagamento.FormaPagamento.Id == (int)FormaPagamentoEnum.PagarmePix ||
                estFormaPagamento.FormaPagamento.Id == (int)FormaPagamentoEnum.PagarmeCredito;
            
            if (usouAlgumTipoDePagamentoOnline)
            {
                var parametrosDaPagadoraParaPagamentoOnline = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterTaxasConfiguradasParaEstabelecimento(idEstabelecimento);
                retorno.DiasParaReceberDaOperadora = parametrosDaPagadoraParaPagamentoOnline.Parametros.DiasParaReceber ?? 0;
                retorno.PercentualCobradoPelaOperadora = parametrosDaPagadoraParaPagamentoOnline.Parametros.ObterPercentualTotalPorTransacao();
                retorno.ValorFixoCobradoPelaOperadora = parametrosDaPagadoraParaPagamentoOnline.Parametros.ObterValorFixoTotalPorTransacao();
            }

            AtualizarParcelasDaTransacaoFormaPagamento(idEstabelecimento, parcelas, retorno);

            retorno.NumeroParcelas = retorno.Parcelas.Count();

            return retorno;
        }

        private void AtualizarParcelasDaTransacaoFormaPagamento(int idEstabelecimento, List<ParcelaDePagamentoDTO> parcelas, TransacaoFormaPagamento transacaoFormaPagamento)
        {
            var novaLista = MontarParcelasDeTransacaoFormaPagamento(parcelas, transacaoFormaPagamento);
            var listaOriginal = transacaoFormaPagamento.Parcelas;

            // Diminuição do número de parcelas
            while (listaOriginal.Count() > novaLista.Count())
            {
                listaOriginal.RemoveAt(listaOriginal.Count() - 1);
            }

            AtualizarDataRecebimentoPorConfiguracaoPOS(idEstabelecimento, novaLista);
            //transacaoFormaPagamento.Parcelas.Clear();
            foreach (var item in novaLista.Where(f => f.Id == 0))
            {
                transacaoFormaPagamento.Parcelas.Add(item);
            }
        }

        private List<TransacaoFormaPagamentoParcela> MontarParcelasDeTransacaoFormaPagamento(List<ParcelaDePagamentoDTO> parcelas, TransacaoFormaPagamento transacaoFormaPagamento)
        {
            var retorno = new List<TransacaoFormaPagamentoParcela>();

            if (transacaoFormaPagamento.ValorPago != 0 && !parcelas.Any())
            {
                retorno = new List<TransacaoFormaPagamentoParcela> {
                    MontarParcelaDeTransacaoFormaPagamento(new ParcelaDePagamentoDTO {
                        DataHora = transacaoFormaPagamento.Transacao.DataHora,
                        NumeroParcela = 1,
                        Valor = transacaoFormaPagamento.ValorPago
                    },
                    transacaoFormaPagamento)
                };
            }
            else
            {
                foreach (var parcela in parcelas)
                {
                    retorno.Add(MontarParcelaDeTransacaoFormaPagamento(parcela, transacaoFormaPagamento));
                }
            }

            return retorno;
        }

        private TransacaoFormaPagamentoParcela MontarParcelaDeTransacaoFormaPagamento(ParcelaDePagamentoDTO parcelaDTO,
            TransacaoFormaPagamento transacaoFormaPagamento)
        {
            var parcela = transacaoFormaPagamento.Parcelas.FirstOrDefault(f => f.NumeroParcela == parcelaDTO.NumeroParcela)
                ?? new TransacaoFormaPagamentoParcela();

            parcela.NumeroParcela = parcelaDTO.NumeroParcela;
            parcela.TransacaoFormaPagamento = transacaoFormaPagamento;
            parcela.Valor = parcelaDTO.Valor;
            parcela.DataPagamento = parcelaDTO.DataHora;
            parcela.DataRecebimento = parcelaDTO.DataHora.AddDays(transacaoFormaPagamento.DiasParaReceberDaOperadora);

            return parcela;
        }

        private static void AtualizarDataRecebimentoPorConfiguracaoPOS(int idEstabelecimento, IEnumerable<TransacaoFormaPagamentoParcela> novaLista)
        {
            var configuracoesPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);
            if (configuracoesPOS != null && configuracoesPOS.TipoPOS.Id == (int)SubadquirenteEnum.Stone)
            {
                foreach (var transacaoFormaPagamentoParcela in novaLista)
                {
                    transacaoFormaPagamentoParcela.DataRecebimento =
                        Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametroPorSubadquirente(transacaoFormaPagamentoParcela.DataRecebimento);
                }
            }
        }

        #endregion MontarTransacaoFormaPagaemnto

        public void ColocarCompraDeCreditoNaTransacao(Transacao transacao, FormaPagamentoPrePagoEnum formaPagamentoPrePago, decimal valorDoCredito)
        {
            var transacaoFormaPagamento = new TransacaoFormaPagamento
            {
                FormaPagamento =
                            Domain.Financeiro.FormaPagamentoRepository.Load((int)formaPagamentoPrePago),
                Transacao = transacao,
                ValorPago = -valorDoCredito,
                ValorBaseCalculoPercentualDesconto = -valorDoCredito
            };
            transacaoFormaPagamento.Parcelas = new List<TransacaoFormaPagamentoParcela> {
                        new TransacaoFormaPagamentoParcela {
                            NumeroParcela = 1,
                            Valor = transacaoFormaPagamento.ValorPago,
                            DataPagamento = transacao.DataHora,
                            DataRecebimento = transacao.DataHora,
                            TransacaoFormaPagamento = transacaoFormaPagamento
                        }
                    };
            transacao.FormasPagamento.Add(transacaoFormaPagamento);
        }

        private bool EhParaGerarNotaFiscalDoConsumidor(Transacao transacao)
        {
            bool ehParaGerarNotaFiscalDoConsumidor = true;
            Estabelecimento estabelecimento = Domain.Financeiro.TransacaoRepository.ObterEstabelecimentoDaTransacao(transacao);

            ehParaGerarNotaFiscalDoConsumidor = estabelecimento.EstabelecimentoPossuiNFC()
                                && !transacao.GetDescontarProfissional();

            return ehParaGerarNotaFiscalDoConsumidor;
        }

        private static void GerarDadosParaAIntegracaoComNotaFiscalDoConsumidor(Transacao transacao, bool desconsiderarSeTeveDescontoOuNao = false)
        {
            Estabelecimento estabelecimento = Domain.Financeiro.TransacaoRepository.ObterEstabelecimentoDaTransacao(transacao);

            var descontoEhTotal = ValorEmProdutos(transacao) == 0;

            if (desconsiderarSeTeveDescontoOuNao || !descontoEhTotal)
            {
                Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.GerarPreImpressaoDeNF(transacao, estabelecimento);
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemAlerta("Não foi possivel gerar nota fiscal para este transacao: fechamento de produtos com 100% de desconto");
            }
            Domain.Financeiro.TransacaoRepository.Update(transacao);
        }

        private static decimal ValorEmProdutos(Transacao transacao)
        {
            var valor = (decimal)0;

            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);
            if (venda != null)
            {
                foreach (var itemVenda in venda.ItensVenda)
                {
                    if (itemVenda is ItemVendaPacote)
                    {
                        var itemVendaPacote = (ItemVendaPacote)itemVenda;

                        foreach (ItemPacoteCliente itemPacote in itemVendaPacote.PacoteCliente.ItensPacoteCliente)
                        {
                            if (itemPacote.GetRealType() == typeof(ItemPacoteClienteProduto))
                            {
                                ItemPacoteClienteProduto item = null;

                                if (itemPacote is INHibernateProxy)
                                {
                                    item = Domain.Pacotes.ItemPacoteClienteProdutoRepository.Load(itemPacote.Id);
                                }
                                else
                                    item = (ItemPacoteClienteProduto)itemPacote;

                                valor += ValorDoProdutoNoPacote(item, itemVendaPacote);
                            }
                        }
                    }

                    if (itemVenda is ItemVendaProduto && itemVenda.ValorFinal() > 0)
                    {
                        valor += itemVenda.ValorFinal();
                    }
                }
            }

            return valor;
        }

        private static decimal ValorDoProdutoNoPacote(ItemPacoteClienteProduto itemPacote, ItemVendaPacote itemVendaPacote)
        {
            var descontoNoPacote = itemVendaPacote.Desconto;
            var percentualDesconto = -descontoNoPacote / itemVendaPacote.SubTotal;
            return itemPacote.ValorUnitario + -itemPacote.ValorUnitario * percentualDesconto;
        }

        #region Métodos 'Private'

        private static void AtualizarHorariosDoFechamentoConta(Transacao transacao, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false, bool manterStatusAgendamentos = false)
        {
            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                AtualizarHorariosDoFechamentoConta(horarioTransacao, jaPassouPeloControleDeUsoDeProdutoAutomatico, manterStatusAgendamentos);
            }
        }

        private static void AtualizarHorariosDoFechamentoConta(HorarioTransacao horarioTransacao, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false, bool manterStatusAgendamentos = false)
        {
            var transacao = horarioTransacao.Transacao;
            var ehPagamento = transacao.TipoTransacao.Id == 1 && transacao.TransacaoQueEstounouEsta == null;
            var horario = horarioTransacao.Horario;

            if (horario.DataFim.Date <= transacao.DataHora.Date && ehPagamento && !manterStatusAgendamentos)
                horario.Status = new StatusHorario((int)StatusHorarioEnum.Finalizado);

            horario.FoiPago = ehPagamento;

            if (horarioTransacao.Preco.HasValue)
                horario.Valor = Math.Abs(horarioTransacao.Preco.Value);

            var transacaoParaHistorico = transacao.TransacaoQueEstounouEsta != null ? transacao.TransacaoQueEstounouEsta : transacao;
            AgendaService.ManterAgendamentoNoTransaction(horario, transacaoParaHistorico, false, true, false, forcarGeracaoHistorico: true, trataSplitDeComanda: false, jaPassouPeloControleDeUsoDeProdutoAutomatico: jaPassouPeloControleDeUsoDeProdutoAutomatico); //É necessário não dar commit para dar rollback junto com o fechamento de conta em caso de erro.
        }

        //public void GerarComissoesHorarioTransacao(Transacao transacao) {
        //    foreach (var horarioTransacao in transacao.HorariosTransacoes.Where(horarioTransacao => horarioTransacao.Comissao != null || horarioTransacao.ComissaoAssistente == null)) {
        //        if (horarioTransacao.Comissao == null)
        //            continue;
        //        horarioTransacao.Comissao.Transacao = transacao;
        //        horarioTransacao.Comissao.TipoOrigemComissao = Enums.TipoOrigemComissaoEnum.Servico;
        //        if (horarioTransacao.Horario.Profissional != null && horarioTransacao.Horario.Profissional.PessoaFisica == null) {
        //            if (horarioTransacao.Horario.Id > 0)
        //                horarioTransacao.Horario = Domain.Pessoas.HorarioRepository.Load(horarioTransacao.Horario.Id);
        //            else if (horarioTransacao.Horario.Profissional.IdProfissional > 0)
        //                horarioTransacao.Horario.Profissional = Domain.Pessoas.ProfissionalRepository.Load(horarioTransacao.Horario.Profissional.IdProfissional);
        //        }

        //        if (horarioTransacao.Horario.Profissional != null) {
        //            var profissional = Domain.Pessoas.ProfissionalRepository.Load(horarioTransacao.Horario.Profissional.IdProfissional);
        //            horarioTransacao.Comissao.PessoaComissionada = profissional.PessoaFisica;
        //        }
        //        if (horarioTransacao.ComissaoAssistente != null) {
        //            if (horarioTransacao.ComissaoAssistente == null)
        //                continue;
        //            horarioTransacao.ComissaoAssistente.Transacao = transacao;
        //            var profissional = Domain.Pessoas.ProfissionalRepository.Load(horarioTransacao.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional);
        //            horarioTransacao.ComissaoAssistente.PessoaComissionada = profissional.PessoaFisica;
        //        }

        //        Domain.Financeiro.ComissaoService.CalcularComissaoProfissionalServico(horarioTransacao);
        //    }
        //}

        //public void GerarComissoesItensVenda(Transacao transacao, Venda venda) {
        //    if (venda == null)
        //        return;
        //    foreach (var item in venda.ItensVenda.Where(item => item.Comissao != null)) {
        //        if (item.Comissao != null && item.Comissao.PessoaComissionada != null) {
        //            if (item is ItemVendaPacote)
        //                CalcularComissaoItemVendaPacote((ItemVendaPacote)item);
        //            else
        //                CalcularComissaoItemVendaProduto((ItemVendaProduto)item);

        //            item.Comissao.Transacao = transacao;
        //            item.Comissao.TipoOrigemComissao = Enums.TipoOrigemComissaoEnum.Produto;
        //        }
        //        else
        //            item.Comissao = null;
        //    }
        //}

        private void ValidarResultadoFinalCheckout(Transacao transacao)
        {
            ValidarResultadoFinalDescartaveis(transacao);
        }

        private void ValidarResultadoFinalDescartaveis(Transacao transacao)
        {
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            if (!estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis)
                return;

            var descartavelErrado =
                transacao.HorariosTransacoes.Any(
                    //f =>
                    //    Math.Abs(f.Descartaveis.Value) != (f.Horario.ServicoEstabelecimento.CustoDescartaveis + f.Horario.ObterCustoDosProdutosAssociadosAoHorario()) );
                    f =>
                        -f.Descartaveis != (f.Horario.ServicoEstabelecimento.CustoDescartaveis + f.Horario.ObterCustoDosProdutosAssociadosAoHorario()) &&
                        f.Descartaveis != (f.Horario.ServicoEstabelecimento.CustoDescartaveis + f.Horario.ObterCustoDosProdutosAssociadosAoHorario()));
            if (descartavelErrado)
                ErrorSignal.FromCurrentContext().Raise(new Exception("Erro ao descontar descartáveis"));
        }

        #endregion Métodos 'Private'

        #endregion Transacao

        #region Estorno

        [TransactionInitRequired]
        public async Task<Transacao> RealizarEstorno(int idTransacao, DateTime dataHoraTrancao, String comentario, PessoaFisica pessoaFisicaAutenticada, Int32? idTransacaoPOS)
        {
            if (idTransacao == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Não é possível estornar um pagamento que já foi estornado!");
                return null;
            }

            var itensPacoteClienteAAtualizar = new List<ItemPacoteCliente>();

            var transacao = TransacaoRepository.Load(idTransacao);
            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);

            var movimentacoesDePontosDeFidelidadeNestaTransacao = Domain.Fidelidade.MovimentacaoDePontosRepository.ObterMovimentacoesDePontosDestaTransacao(transacao);

            ValidarEstorno(transacao, movimentacoesDePontosDeFidelidadeNestaTransacao, venda);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var transacaoPossuiSplitNaoProcessado = false;
            var transacaoPossuiSplitDePagamento = VerificarSeTransacaoPossuiSplitDePagamento(transacao);

            if (idTransacaoPOS.HasValue && idTransacaoPOS.Value > 0)
                transacaoPossuiSplitNaoProcessado = Domain.Financeiro.TransacaoPOSSplitRepository.ListarSplitsPorTransacaoId(idTransacao).Any();

            if (transacaoPossuiSplitDePagamento || transacaoPossuiSplitNaoProcessado)
            {
                var lancamentosDeSplitDePagamentoParaTransacao = Domain.Despesas.LancamentoRepository.Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento && p.Transacao.Id == transacao.Id);
                foreach (var splitDePagamento in lancamentosDeSplitDePagamentoParaTransacao)
                {
                    Domain.Despesas.LancamentoService.Inativar(splitDePagamento, pessoaFisicaAutenticada);
                }

                var transacaoPOS = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorIdTransacao(idTransacao);
                if (transacaoPOS != null && transacaoPOS.TipoPOS.EhSplitStone)
                    await Domain.Financeiro.SubadquirenteService.CancelarSplit(transacao.Id);
            }

            var transacaoEstorno = ObterTransacaoEstorno(dataHoraTrancao, comentario, pessoaFisicaAutenticada, transacao);

            GerarEstornoDasFormasDePagamento(transacao, transacaoEstorno, pessoaFisicaAutenticada, out List<LancamentoDeAntecipacao> estornosDeAntecipacao);

            ColocarRPSsEmitidosDaTransacaoComoACancelar(transacao);

            TransacaoRepository.SaveNew(transacaoEstorno);

            foreach (var antecipacaoEstornada in estornosDeAntecipacao)
            {
                Domain.Financeiro.LancamentoDeAntecipacaoRepository.SaveNew(antecipacaoEstornada);
            }

            if (idTransacaoPOS.HasValue && idTransacaoPOS.Value > 0)
            {
                Domain.Financeiro.TransacaoPOSService.AssociarComFechamento(idTransacaoPOS.Value, transacaoEstorno.Id);
            }

            if (venda != null)
            {
                Venda vendaEstorno = null;
                vendaEstorno = ObterVendaEstorno(venda, transacaoEstorno, itensPacoteClienteAAtualizar);

                if (vendaEstorno != null)
                {
                    EstornarVenda(pessoaFisicaAutenticada, venda, vendaEstorno);
                }
            }

            transacao.TransacaoQueEstounouEsta = transacaoEstorno;

            Domain.Financeiro.TransacaoRepository.Flush();

            if (estabelecimento.EstabelecimentoConfiguracaoGeral.JaTeveCaixaPorProfissionalAberto)
                Domain.Financeiro.ControleDeCaixaService.EstornarMovimentacaoExistenteNoCaixa(transacao);

            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                EstornarHorarioTransacao(transacaoEstorno, horarioTransacao, itensPacoteClienteAAtualizar);
            }

            if (transacao.PessoaQuePagou != null)
            {
                RecalcularCreditoCliente(transacao.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);

                var creditoCliente = transacao.FormasPagamento.FirstOrDefault(f => f.FormaPagamento == FormaPagamentoEnum.CreditoCliente);
                if (creditoCliente != null && creditoCliente.PessoaQuePagou != transacao.PessoaQuePagou)
                    RecalcularCreditoCliente(creditoCliente.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);

                var formaPagamentoValePresente = transacao.FormasPagamento.FirstOrDefault(p => p.ValePresente != null);
                if (formaPagamentoValePresente != null)
                {
                    RecalcularValePresente(formaPagamentoValePresente.ValePresente);
                }
            }

            if (venda != null)
                foreach (var item in venda.ItensVenda)
                {
                    var ivp = item as ItemVendaProduto;
                    if (ivp == null)
                        continue;

                    if (ivp.PreVenda != null)
                    {
                        var validacao = Domain.Vendas.PreVendaRepository.ValidarEstoquePreVenda(ivp.PreVenda.Id);
                        if (!string.IsNullOrEmpty(validacao))
                            throw new Exception(validacao);
                    }
                }

            EnviarEmailCancelamentoNFCe(transacao);
            CancelarNFCe(transacao);

            DescontarCustosDeProdutosCobradosDosClientes(transacao.HorariosTransacoes);

            EstornarPontosDeFidelidade(movimentacoesDePontosDeFidelidadeNestaTransacao, transacao);

            EstornarPagamentoDeDividaDoCliente(transacao, pessoaFisicaAutenticada.IdPessoa);
            EstornarDividaDoCliente(transacao, pessoaFisicaAutenticada.IdPessoa);

            Domain.Pacotes.PacoteClienteService.AtualizarQuantidadeConsumida(itensPacoteClienteAAtualizar);

            Domain.ClubeDeAssinaturas.ConsumoDeAssinaturaService.EstornarBeneficio(idTransacao);
            Domain.ClubeDeAssinaturas.PagamentosService.EstornarPagamento(idTransacao, transacaoEstorno.DataHora);
            Domain.Financeiro.GorjetaService.EstornarGorjeta(idTransacao);

            var utilizaCashback = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.CashbackCrmBonus).EstaDisponivel;
            if (utilizaCashback)
            {
                if (transacao.BonusTransacao != null)
                {
                    var bonusTransacao = transacao.BonusTransacao;
                    try
                    {
                        Domain.Cashback.ProcessamentoBonusCrmBonusService.CancelarBonus(new CancelarBonusCrmBonusDto(bonusTransacao.CelularCliente,
                        transacao.Id, bonusTransacao.IdClienteCrmBonus, bonusTransacao.IdBonusCrmBonus));
                    }
                    catch (Exception ex)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao(ex.Message);
                    }
                }

                if (!ValidationHelper.Instance.IsValid)
                    return null;

                Domain.Cashback.EnvioAssincronoCrmBonusService.PublicarCancelamentoNaFila(transacao.Id);
            }

            if (Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks
                .NotificacaoDeEventoParaIntegracaoApartirDoTrinksService
                .EstabelecimentoPossuiChaveParaIntegracaoComOutrosSistemas(estabelecimento))
                EnviarEventoDeTransacaoParaIntegracao(transacao, TipoDeEventoEnum.EstornoDeFechamentoDeConta);

            return transacaoEstorno;
        }

        private void DescontarCustosDeProdutosCobradosDosClientes(IList<HorarioTransacao> ht)
        {
            foreach (var horarioTransacao in ht)
            {
                var valorDosProdutos = horarioTransacao.Horario.ObterCustoDosProdutosAssociadosAoHorarioParaCliente();
                var valorDoHorarioTransacaoSemCustoDeProduto = horarioTransacao.Preco - valorDosProdutos;
                var valorValido = valorDoHorarioTransacaoSemCustoDeProduto > 0 ? valorDoHorarioTransacaoSemCustoDeProduto : 0;
                horarioTransacao.Horario.Valor = valorValido.Value;
            }
        }

        private void EstornarPontosDeFidelidade(List<MovimentacaoDePontos> movimentacoesDePontosDeFidelidadeNestaTransacao, Transacao transacao)
        {
            if (transacao.PessoaQuePagou != null)
            {
                var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento;
                var pessoaFisicaDoCliente = transacao.PessoaQuePagou;

                var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(pessoaFisicaDoCliente.IdPessoa, estabelecimento.IdEstabelecimento);

                if (clienteEstabelecimento != null)
                    Domain.Fidelidade.MovimentacaoDePontosSevice.EstornarMovimentacoesDePontos(movimentacoesDePontosDeFidelidadeNestaTransacao, clienteEstabelecimento);
            }
        }

        private static void EnviarEmailCancelamentoNFCe(Transacao transacao)
        {
            NotaNFC notaDaTransacao = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(transacao);

            if (notaDaTransacao == null)
                return;

            if (notaDaTransacao.StatusNota.IdStatusNotaNFC == (int)StatusNotaNFCEnum.Emitida &&
                notaDaTransacao.StatusNota.InterfaceNFC == TipoDeInterfaceNFC.NotaFiscalEletronica &&
                !string.IsNullOrEmpty(notaDaTransacao.AutorizacaoSEFAZ))
                Domain.Pessoas.EnvioEmailService.EnviarEmailCancelamentoNFCE(transacao);
        }

        private void EstornarVenda(PessoaFisica pessoaFisicaAutenticada, Venda venda, Venda vendaEstorno)
        {
            foreach (var i in vendaEstorno.ItensVenda.Where(f => f is ItemVendaValePresente).Cast<ItemVendaValePresente>())
            {
                AjustarNomeValePresenteParaEstorno(i.ValePresente);
            }

            Domain.Vendas.VendaRepository.SaveNew(vendaEstorno);
            Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueService
                .RegistrarMovimentacoesProdutosDuranteEstornoVenda(venda, pessoaFisicaAutenticada);

            ReabrirPreVendasDeProdutoSeHouver(venda, pessoaFisicaAutenticada);
        }

        private static Venda ObterVendaEstorno(Venda venda, Transacao transacaoEstorno, List<ItemPacoteCliente> itensPacoteClienteAAtualizar)
        {
            Venda vendaEstorno = new Venda { Transacao = transacaoEstorno };
            foreach (var item in venda.ItensVenda)
            {
                var itemEstorno = ObterItemEstorno(item, vendaEstorno);
                vendaEstorno.ItensVenda.Add(itemEstorno);

                if (item.ItemPacoteCliente != null)
                    itensPacoteClienteAAtualizar.Add(item.ItemPacoteCliente);
            }

            return vendaEstorno;
        }

        private static Transacao ObterTransacaoEstorno(DateTime dataHoraTrancao, string comentario, PessoaFisica pessoaFisicaAutenticada, Transacao transacao)
        {
            return new Transacao
            {
                DataHora = dataHoraTrancao,
                DataReferencia = transacao.DataReferencia,
                Descontos = transacao.Descontos * -1,
                DescontosPacotes = transacao.DescontosPacotes * -1,
                DescontosProdutos = transacao.DescontosProdutos * -1,
                DescontosServicos = transacao.DescontosServicos * -1,
                DescontosValePresente = transacao.DescontosValePresente * -1,
                TotalServicos = transacao.TotalServicos * -1,
                TotalProdutos = transacao.TotalProdutos * -1,
                TotalPacotes = transacao.TotalPacotes * -1,
                TotalClubeDeAssinaturas = transacao.TotalClubeDeAssinaturas * -1,
                TotalCreditoCliente = transacao.TotalCreditoCliente * -1,
                TotalValePresente = transacao.TotalValePresente * -1,
                TipoTransacao = new TipoTransacao(2),
                TotalPagar = transacao.TotalPagar * -1,
                TotalPago = transacao.TotalPago * -1,
                TotalPagoEmCredito = transacao.TotalPagoEmCredito * -1,
                TotalPagoEmOutros = transacao.TotalPagoEmOutros * -1,
                TotalPagoEmDinheiro = transacao.TotalPagoEmDinheiro * -1,
                TotalPagoEmDebito = transacao.TotalPagoEmDebito * -1,
                TotalPagoEmPrePago = transacao.TotalPagoEmPrePago * -1,
                TotalDescartaveis = transacao.TotalDescartaveis * -1,
                TotalDescontoOperadoras = transacao.TotalDescontoOperadoras * -1,
                PercentualMedioDescontoOperadoras = transacao.PercentualMedioDescontoOperadoras,
                Troco = transacao.Troco * -1,
                Ativo = true,
                PessoaQueRealizou = pessoaFisicaAutenticada,
                PessoaQuePagou = transacao.PessoaQuePagou,
                PessoaQueRecebeu = transacao.PessoaQueRecebeu,
                ComentarioFechamentoConta = transacao.ComentarioFechamentoConta,
                ComentarioEstorno = comentario,
                FoiVendaParaProfissional = transacao.FoiVendaParaProfissional,
            };
        }

        private static void EstornarHorarioTransacao(Transacao transacaoEstorno, HorarioTransacao horarioTransacao, List<ItemPacoteCliente> itensPacoteClienteAAtualizar)
        {
            var htEstorno = ObterHtEstorno(transacaoEstorno, horarioTransacao);

            if (htEstorno.ItemPacoteCliente != null)
                itensPacoteClienteAAtualizar.Add(htEstorno.ItemPacoteCliente);
            transacaoEstorno.HorariosTransacoes.Add(htEstorno);

            var preVenda = htEstorno.Horario.PreVenda();

            if (preVenda != null && preVenda.Comanda != null)
            {
                EstornarComanda(preVenda);
            }

            AtualizarHorariosDoFechamentoConta(horarioTransacao);
        }

        private void GerarEstornoDasFormasDePagamento(Transacao transacaoPagamento, Transacao transacaoEstorno, PessoaFisica pessoaAutenticada, out List<LancamentoDeAntecipacao> estornosDeAntecipacao)
        {
            estornosDeAntecipacao = new List<LancamentoDeAntecipacao>();

            foreach (var transacaoFormaPagamento in transacaoPagamento.FormasPagamento)
            {
                var formaPagamento = ObterEstornoFormaPagamento(transacaoEstorno, transacaoFormaPagamento);

                foreach (var p in transacaoFormaPagamento.Parcelas)
                {
                    var parcela = ObterEstornoParcela(formaPagamento, p);
                    formaPagamento.Parcelas.Add(parcela);

                    var antecipacao = Domain.Financeiro.LancamentoDeAntecipacaoRepository.ObterPorTransacaoParcela(p.Id);
                    if (antecipacao != null)
                    {
                        var antecipacaoEstornada = new LancamentoDeAntecipacao(parcela, antecipacao.TaxaDesconto, pessoaAutenticada);
                        estornosDeAntecipacao.Add(antecipacaoEstornada);
                    }
                }

                transacaoEstorno.FormasPagamento.Add(formaPagamento);
            }
        }

        private static TransacaoFormaPagamentoParcela ObterEstornoParcela(TransacaoFormaPagamento formaPagamento, TransacaoFormaPagamentoParcela p)
        {
            return new TransacaoFormaPagamentoParcela
            {
                NumeroParcela = p.NumeroParcela,
                DataPagamento = p.DataPagamento,
                DataRecebimento = p.DataRecebimento,
                TransacaoFormaPagamento = formaPagamento,
                Valor = -p.Valor
            };
        }

        private static TransacaoFormaPagamento ObterEstornoFormaPagamento(Transacao transacaoEstorno, TransacaoFormaPagamento transacaoFormaPagamento)
        {
            return new TransacaoFormaPagamento
            {
                Transacao = transacaoEstorno,
                PessoaQuePagou = transacaoFormaPagamento.PessoaQuePagou,
                ValorPago = transacaoFormaPagamento.ValorPago * -1,
                ValorBaseCalculoPercentualDesconto = transacaoFormaPagamento.ValorPago * -1,
                FormaPagamento = transacaoFormaPagamento.FormaPagamento,
                PercentualCobradoPelaOperadora = transacaoFormaPagamento.PercentualCobradoPelaOperadora,
                DiasParaReceberDaOperadora = transacaoFormaPagamento.DiasParaReceberDaOperadora,
                ValePresente = transacaoFormaPagamento.ValePresente,
                NumeroParcelas = transacaoFormaPagamento.NumeroParcelas
            };
        }

        private static void EstornarComanda(PreVenda preVenda)
        {
            var existeComandaAberta = Domain.Vendas.ComandaRepository.ExisteComandaAbertaComMesmoNumero(preVenda.Comanda.Numero, preVenda.Estabelecimento.IdEstabelecimento, preVenda.Comanda.Id);

            if (existeComandaAberta)
            {
                throw new Exception("A pré-venda não pode ser reaberta pois já existe uma comanda aberta com esse número");
            }

            preVenda.Comanda.Fechada = false;
            preVenda.PreVendaStatus = StatusPreVendaEnum.EmAberto;
            //Domain.Vendas.PreVendaRepository.Update(preVenda);
            //Domain.Vendas.ComandaRepository.Update(preVenda.Comanda);
        }

        private static HorarioTransacao ObterHtEstorno(Transacao transacaoEstorno, HorarioTransacao horarioTransacao)
        {
            return new HorarioTransacao
            {
                Comissao = Domain.Financeiro.ComissaoService.GerarComissaoDeEstorno(horarioTransacao.Comissao, transacaoEstorno),
                ComissaoAssistente = Domain.Financeiro.ComissaoService.GerarComissaoDeEstorno(horarioTransacao.ComissaoAssistente, transacaoEstorno),
                DataHoraInicioHorario = horarioTransacao.DataHoraInicioHorario,
                Desconto = horarioTransacao.Desconto * -1,
                Preco = horarioTransacao.Preco * -1,
                Descartaveis = horarioTransacao.Descartaveis * -1,
                TipoDesconto = horarioTransacao.TipoDesconto,
                MotivoDesconto = horarioTransacao.MotivoDesconto,
                ItemPacoteCliente = horarioTransacao.ItemPacoteCliente,
                Horario = horarioTransacao.Horario,
                Transacao = transacaoEstorno,
                Ativo = horarioTransacao.Ativo,
                PontosDeFidelidade = horarioTransacao.PontosDeFidelidade * -1
            };
        }

        private void ColocarRPSsEmitidosDaTransacaoComoACancelar(Transacao transacao)
        {
            var listaDeDadosRPSEmitidosDaTransacao = Domain.RPS.DadosRPSTransacaoRepository.ObterPorIdTransacaoEStatus(transacao.Id, StatusRpsEnum.Emitido);

            foreach (var dadosRPS in listaDeDadosRPSEmitidosDaTransacao)
            {
                dadosRPS.StatusRPS = StatusRpsEnum.ACancelar;
            }
        }

        private void ReabrirPreVendasDeProdutoSeHouver(Venda vendaEstorno, PessoaFisica pessoaQueReabriu)
        {
            foreach (ItemVenda item in vendaEstorno.ItensVenda)
            {
                if (item.GetRealType() == typeof(ItemVendaProduto))
                {
                    ItemVendaProduto itemVendaProduto = null;

                    if (item is INHibernateProxy)
                        itemVendaProduto = Domain.Vendas.ItemVendaProdutoRepository.Load(item.Id);
                    else
                        itemVendaProduto = (ItemVendaProduto)item;

                    if (itemVendaProduto.VeioDeUmaPreVenda())
                    {
                        Domain.Vendas.PreVendaService.ReabrirPreVenda(itemVendaProduto.PreVenda, pessoaQueReabriu);
                    }
                }
            }
        }

        private static ItemVendaPacote ItemVendaPacoteEstorno(ItemVendaPacote item, Venda vendaEstorno)
        {
            var itemVendaPacote = new ItemVendaPacote
            {
                Comissao = Domain.Financeiro.ComissaoService.GerarComissaoDeEstorno(item.Comissao, vendaEstorno.Transacao),
                Desconto = item.Desconto * -1,
                MotivoDesconto = item.MotivoDesconto,
                Ativo = item.Ativo,
                PacoteCliente = item.PacoteCliente,
                Quantidade = -item.Quantidade,
                TipoDesconto = item.TipoDesconto,
                ValorUnitario = item.ValorUnitario,
                Venda = vendaEstorno
            };
            itemVendaPacote.PacoteCliente.Ativo = false;
            itemVendaPacote.CalcularEDefinirSubTotal();
            return itemVendaPacote;
        }

        private static ItemVendaProduto ItemVendaProdutoEstorno(ItemVendaProduto item, Venda vendaEstorno)
        {
            var itemVendaProduto = new ItemVendaProduto
            {
                Comissao = Domain.Financeiro.ComissaoService.GerarComissaoDeEstorno(item.Comissao, vendaEstorno.Transacao),
                Desconto = item.Desconto * -1,
                MotivoDesconto = item.MotivoDesconto,
                Quantidade = item.Quantidade * -1,
                Venda = vendaEstorno,
                Ativo = item.Ativo,
                ItemPacoteCliente = item.ItemPacoteCliente,
                TipoDesconto = item.TipoDesconto,
                ValorUnitario = item.ValorUnitario,
                EstabelecimentoProduto = item.EstabelecimentoProduto,
                TipoDeQuantidade = item.TipoDeQuantidade,
                PessoaComissionada = item.PessoaComissionada
            };

            itemVendaProduto.CalcularEDefinirSubTotal();

            return itemVendaProduto;
        }

        private static ItemVenda ItemVendaValePresenteEstorno(ItemVendaValePresente item, Venda vendaEstorno)
        {
            var itemVenda = new ItemVendaValePresente
            {
                Comissao = Domain.Financeiro.ComissaoService.GerarComissaoDeEstorno(item.Comissao, vendaEstorno.Transacao),
                Desconto = item.Desconto * -1,
                MotivoDesconto = item.MotivoDesconto,
                Quantidade = item.Quantidade * -1,
                Venda = vendaEstorno,
                Ativo = item.Ativo,
                ItemPacoteCliente = item.ItemPacoteCliente,
                TipoDesconto = item.TipoDesconto,
                ValorUnitario = item.ValorUnitario,
                ValePresente = item.ValePresente
            };

            itemVenda.CalcularEDefinirSubTotal();

            //if (item.ItemPacoteCliente != null)
            //    item.ItemPacoteCliente.QuantidadeConsumida -= 1;

            return itemVenda;
        }

        private static ItemVenda ObterItemEstorno(ItemVenda item, Venda vendaEstorno)
        {
            ItemVenda itemVendaEstorno = null;

            if (item.GetRealType() == typeof(ItemVendaProduto))
            {
                ItemVendaProduto itemVendaProduto = null;

                if (item is INHibernateProxy)
                    itemVendaProduto = Domain.Vendas.ItemVendaProdutoRepository.Load(item.Id);
                else
                    itemVendaProduto = (ItemVendaProduto)item;

                itemVendaEstorno = ItemVendaProdutoEstorno(itemVendaProduto, vendaEstorno);
            }

            if (item.GetRealType() == typeof(ItemVendaPacote))
            {
                ItemVendaPacote itemVendaPacote = null;

                if (item is INHibernateProxy)
                    itemVendaPacote = Domain.Vendas.ItemVendaPacoteRepository.Load(item.Id);
                else
                    itemVendaPacote = (ItemVendaPacote)item;

                itemVendaEstorno = ItemVendaPacoteEstorno(itemVendaPacote, vendaEstorno);
            }

            if (item.GetRealType() == typeof(ItemVendaValePresente))
            {
                ItemVendaValePresente itemVendaValePresente = null;

                if (item is INHibernateProxy)
                    itemVendaValePresente = Domain.Vendas.ItemVendaValePresenteRepository.Load(item.Id);
                else
                    itemVendaValePresente = (ItemVendaValePresente)item;

                itemVendaEstorno = ItemVendaValePresenteEstorno(itemVendaValePresente, vendaEstorno);
            }

            if (itemVendaEstorno != null)
            {
                itemVendaEstorno.PontosDeFidelidade = item.PontosDeFidelidade * -1;
            }

            return itemVendaEstorno;
        }

        private void AjustarNomeValePresenteParaEstorno(ValePresente valePresente)
        {
            var numero = valePresente.Numero;
            var count = 0;
            while (Domain.Vendas.ValePresenteRepository.ExisteValeComONumero(numero, valePresente.PessoaEstabelecimento.IdPessoa))
            {
                if (count == 0)
                    numero = "*" + valePresente.Numero;
                else
                    numero = count + "*" + valePresente.Numero;

                numero = numero.Left(10);
                count++;
            }

            valePresente.Numero = numero.Left(10);
        }

        public void EnviarEventoDeTransacaoParaIntegracao(Transacao transacao, TipoDeEventoEnum tipoDeEventoEnum)
        {
            if (transacao.FoiVendaParaProfissional)
                return;

            TipoDeAcaoEnum tipoAcao = Domain.IntegracaoComOutrosSistemas.NotificacaoDeEventoParaIntegracaoService.TipodeAcaoEnumRetorno(tipoDeEventoEnum);

            var clienteEstabelecimento = transacao.ClienteEstabelecimento();

            var telefones = Domain.Pessoas.TelefoneRepository.ObterTelefonesVisiveisParaAPessoaJuridica(clienteEstabelecimento);

            var enderecoCliente = clienteEstabelecimento.Endereco == null ? new IntegracaoComOutrosSistemas.Endereco() : new IntegracaoComOutrosSistemas.Endereco() { Logradouro = clienteEstabelecimento.Endereco.Logradouro, Bairro = clienteEstabelecimento.Endereco.Bairro, Cidade = clienteEstabelecimento.Endereco.Cidade, Numero = clienteEstabelecimento.Endereco.Numero, UF = clienteEstabelecimento.Endereco.UF == null ? "" : clienteEstabelecimento.Endereco.UF.Sigla.ToString(), EnderecoCompleto = clienteEstabelecimento.Endereco.ObterEnderecoCompleto() };

            var telefonesCliente = telefones.Select(p => new IntegracaoComOutrosSistemas.Telefone() { DDD = p.DDD, Numero = p.Numero, Operadora = p.Operadora != null ? p.Operadora.Nome : "", TelefoneCompleto = IntegracaoComOutrosSistemas.Telefone.TelefoneCompletoFormadado(p.DDD, p.Numero) }).ToList();

            var idEstabelecimento = transacao.PessoaQueRecebeu.Estabelecimento.IdEstabelecimento;

            var IdsDosProfissionaisEnvolvidos = Domain.Pessoas.EstabelecimentoProfissionalService.ObterProfissionaisEItensRelacionadosPorTransacao(transacao.Id);
            var pessoaQueFechouConta = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .Where(e => e.Profissional.PessoaFisica.IdPessoa == transacao.PessoaQueRealizou.IdPessoa && e.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(e => new { e.Profissional.PessoaFisica.NomeCompleto, e.Profissional.IdProfissional, e.Codigo })
                .FirstOrDefault();

            var dados = new DadosEventoDeFecharConta()
            {
                TipoDeEvento = tipoDeEventoEnum,
                Action = tipoAcao,
                IdDoEstabelecimento = idEstabelecimento,
                IdDoCliente = clienteEstabelecimento.Cliente.IdCliente,
                IdDoClienteNoEstabelecimento = clienteEstabelecimento.Codigo,
                IdPessoaDoProfissional = IdsDosProfissionaisEnvolvidos.Count != 0 ? IdsDosProfissionaisEnvolvidos.FirstOrDefault().IdPessoaDoProfissional : 0,
                IdDoProfissional = IdsDosProfissionaisEnvolvidos.Count != 0 ? IdsDosProfissionaisEnvolvidos.FirstOrDefault().IdDoProfissional : 0,
                IdDoProfissionalNoEstabelecimento = IdsDosProfissionaisEnvolvidos.Count != 0 ? IdsDosProfissionaisEnvolvidos.FirstOrDefault().IdDoProfissionalNoEstabelecimento : 0,
                IdsDosProfissionaisEnvolvidos = IdsDosProfissionaisEnvolvidos,
                IdDaTransacao = transacao.Id,
                ValorDaCompra = transacao.TotalPagar.ToString(),
                FormasPagamento = transacao.FormasPagamento?.Select(f => new FormaPagamentoItem { FormaPagamento = f.FormaPagamento?.Nome ?? "-", Valor = f.ValorPago }).ToList(),
                DataDoFechamento = transacao.DataHora.ToIntegracaoLongDateTimeString(),
                IdDoProfissionalQueFechouConta = pessoaQueFechouConta != null ? pessoaQueFechouConta.IdProfissional : 0,
                IdDoProfissionalNoEstabelecimentoQueFechouConta = pessoaQueFechouConta != null ? pessoaQueFechouConta.Codigo : 0,
                NomeDoProfissionalQueFechouConta = pessoaQueFechouConta != null ? pessoaQueFechouConta.NomeCompleto : string.Empty,
                NomeDoCliente = transacao.PessoaQuePagou.NomeCompleto,
                CPFDoCliente = transacao.PessoaQuePagou.Cpf,
                EmailDoCliente = transacao.PessoaQuePagou.Email,
                TelefoneDoCliente = telefonesCliente,
                DataDeNascimentoDoCliente = transacao.PessoaQuePagou.DataNascimento.ToIntegracaoLongDateTimeString(),
                SexoDoCliente = transacao.PessoaQuePagou.Genero,
                EnderecoDoCliente = enderecoCliente,
                DataDeInclusaoDoCliente = transacao.PessoaQuePagou.DataCadastro == null ? Calendario.Agora().ToIntegracaoLongDateTimeString() : transacao.PessoaQuePagou.DataCadastro.ToIntegracaoLongDateTimeString()
            };

            //try {
            dados.Itens.AddRange(transacao.HorariosTransacoes.Select(f => new DadosEventoDeFecharContaItem(f)));

            var venda = transacao.Vendas?.FirstOrDefault() ?? Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);
            dados.Itens.AddRange(venda.ItensVenda.Select(f => new DadosEventoDeFecharContaItem(f)));
            //}
            //catch (Exception e) {
            //    ErrorSignal.FromCurrentContext().Raise(e);
            //}

            var eventoDeFecharConta = new EventoDeFecharConta()
            {
                Dados = dados
            };
            eventoDeFecharConta.Tipo = tipoDeEventoEnum;

            Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.NotificarEventoParaIntegracaoComOutrosSistemas(transacao.PessoaQueRecebeu.Estabelecimento, eventoDeFecharConta);
        }

        #endregion Estorno

        #region Recalculo

        [TransactionInitRequired]
        public void RecalcularCheckOut(IEnumerable<DadosParaRecalculoComissao> dadosParaRecalculoComissao)
        {
            foreach (var item in dadosParaRecalculoComissao)
            {
                RecalcularCheckOut(item);
            }
        }

        [TransactionInitRequired]
        public void RecalcularVendas(IList<ItemVendaProduto> itensVendas)
        {
            foreach (var item in itensVendas)
            {
                RecalcularVenda(item);
            }
        }

        public void RecalcularCreditoCliente(ClienteEstabelecimento clienteEstabelecimento)
        {
            var creditos = Domain.Financeiro.TransacaoFormaPagamentoRepository.ObterCreditoCliente(clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa, clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa);

            if (creditos < 0 && (string.IsNullOrEmpty(clienteEstabelecimento.IdExterno) || clienteEstabelecimento.IdExterno != "CreditoNegativo"))
                if (creditos < 0 && (string.IsNullOrEmpty(clienteEstabelecimento.IdExterno) || clienteEstabelecimento.IdExterno != "CreditoNegativo"))
                    throw new Exception("Credito de clientes não pode ficar negativo.");

            clienteEstabelecimento.ValorCredito = creditos;
        }

        public void RecalcularCreditoCliente(int idPessoa, int idEstabelecimento)
        {
            var clienteEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(idPessoa,
                    idEstabelecimento);

            if (clienteEstabelecimento == null)
                return;
            var creditos = Domain.Financeiro.TransacaoFormaPagamentoRepository.ObterCreditoCliente(idPessoa, clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa);

            //TAREFA  http://jira.perlink.net/browse/TRINKS-9487
            //CODIGO TEMPORÁRIO CRIADO PARA CONTINGENCIAR CLIENTES COM CREDITO NEGATIVO
            //FOI CRIADA UMA CONSTRAINT NO BANCO DE DADOS PARA QUE CLIENTES QUE JÁ POSSUEM CREDITO NEGATIVO POSSAM REALIZAR ESTORNO E FECHAMENTO DE CONTA NORMALMENTE
            //A REGRA DESSA CONSTRAINT ESTÁ SENDO ADICIONADA NO CÓDIGO TEMPORÁRIAMENTE ATÉ TENHAMOS RESOLVIDO TODOS OS CASOS DE CLIENTES COM CRÉDITO NEGATIVO
            if (creditos < 0 && (string.IsNullOrEmpty(clienteEstabelecimento.IdExterno) || clienteEstabelecimento.IdExterno != "CreditoNegativo"))
                throw new Exception("Credito de clientes não pode ficar negativo.");

            clienteEstabelecimento.ValorCredito = creditos;
        }

        private void RecalcularValePresente(ValePresente valePresente)
        {
            valePresente.SaldoAtual = Domain.Vendas.ValePresenteRepository.ObterSaldoVale(valePresente);
        }

        #endregion Recalculo

        #region Métodos 'Private'

        private static void RecalcularTotais(Transacao transacao, Venda venda = null)
        {
            if (venda == null)
                venda = transacao.Vendas.FirstOrDefault();
            //var distintos = transacao.HorariosTransacoes.Select(f => f.Horario.Codigo).Distinct().Count();

            if (transacao.HorariosTransacoes.Any())
            {
                //if (distintos != transacao.HorariosTransacoes.Count())
                //    throw new Exception("Existem serviços repetidos no pagamento.");

                transacao.TotalServicos = transacao.HorariosTransacoes.Sum(f => f.Preco) ?? 0;
                transacao.DescontosServicos = transacao.HorariosTransacoes.Sum(f => f.Desconto) ?? 0;
            }

            if (venda != null && venda.ItensVenda.Any())
            {
                var itensVendaProduto = venda.ItensVenda.Where(f => f is ItemVendaProduto);
                if (itensVendaProduto.Any())
                {
                    transacao.TotalProdutos = itensVendaProduto.Sum(f => f.SubTotal);
                    transacao.DescontosProdutos = itensVendaProduto.Sum(f => f.Desconto);
                }

                var itensVendaPacote = venda.ItensVenda.Where(f => f is ItemVendaPacote);
                if (itensVendaPacote.Any())
                {
                    transacao.TotalPacotes = itensVendaPacote.Sum(f => f.ValorUnitario);
                    transacao.DescontosPacotes = itensVendaPacote.Sum(f => f.Desconto);
                }

                var itensVendaValePresente = venda.ItensVenda.Where(f => f is ItemVendaValePresente);
                if (itensVendaValePresente.Any())
                {
                    transacao.TotalValePresente = itensVendaValePresente.Sum(f => f.ValorUnitario);
                    transacao.DescontosValePresente = itensVendaValePresente.Sum(f => f.Desconto);
                }
            }
            else
            {
                transacao.TotalProdutos = 0;
                transacao.TotalPacotes = 0;
                transacao.TotalValePresente = 0;
            }

            var cashback = transacao.ObterCashbackNaoPersistido() ??
                Domain.Cashback.CashbackTransacaoRepository.ObterCashbackPelaTransacao(transacao.Id);

            transacao.Descontos = (transacao.DescontosServicos ?? 0) + (transacao.DescontosProdutos ?? 0) +
                                  (transacao.DescontosPacotes ?? 0) + (transacao.DescontosValePresente ?? 0) +
                                  (cashback?.Valor ?? 0);

            transacao.TotalPagar = (transacao.TotalServicos ?? 0) + (transacao.TotalProdutos ?? 0) +
                                   (transacao.TotalPacotes ?? 0) + (transacao.TotalClubeDeAssinaturas ?? 0) + (transacao.TotalValePresente) + (transacao.Descontos.Value) +
                                   ((transacao.FormasPagamento.Where(f => f.FormaPagamento.Tipo == FormaPagamentoTipoEnum.PrePago).Sum(f => f.ValorPago)) * -1) +
                                   ((transacao.FormasPagamento.Where(f => f.FormaPagamento.Tipo == FormaPagamentoTipoEnum.PagamentoEmDebito).Sum(f => f.ValorPago)) * -1);

            transacao.TotalDescartaveis = transacao.HorariosTransacoes.Sum(f => f.Descartaveis) ?? 0;

            transacao.TotalPago = transacao.FormasPagamento
                .Where(f => f.ValorPago > 0
                         && f.FormaPagamento.Tipo != FormaPagamentoTipoEnum.PrePago
                         && f.FormaPagamento.Tipo != FormaPagamentoTipoEnum.PagamentoEmDebito)
                .Sum(f => f.ValorPago);

            transacao.RecalcularTroco();

            var listaCredito =
                transacao.FormasPagamento.Where(f => f.FormaPagamento.Tipo == FormaPagamentoTipoEnum.Credito
                || f.FormaPagamento.Tipo == FormaPagamentoTipoEnum.PagamentoOnline).ToList();
            var listaDebito =
                transacao.FormasPagamento.Where(f => f.FormaPagamento.Tipo.Id == 2).ToList();

            List<TransacaoFormaPagamento> listaPrePago;
            List<TransacaoFormaPagamento> listaCreditoCliente;
            IEnumerable<TransacaoFormaPagamento> listaTroco;
            IEnumerable<TransacaoFormaPagamento> listaDinheiro;

            if (transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento)
            {
                listaDinheiro =
                    transacao.FormasPagamento.Where(
                        f =>
                            f.FormaPagamento.Id == 8 && f.ValorPago > 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento).ToList();

                listaTroco =
                    transacao.FormasPagamento.Where(
                        f =>
                            f.FormaPagamento.Id == 8 && f.ValorPago < 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento).ToList();

                listaPrePago =
                    transacao.FormasPagamento.Where(
                        f =>
                            (f.FormaPagamento.Tipo.Id == (int)FormaPagamentoTipoEnum.PrePago) && f.ValorPago > 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento).ToList();

                listaCreditoCliente =
                    transacao.FormasPagamento.Where(
                        f =>
                            (f.FormaPagamento == FormaPagamentoEnum.CreditoCliente || f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnline || f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite)
                            && f.ValorPago < 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento).ToList();
            }
            else
            {
                listaDinheiro =
                    transacao.FormasPagamento.Where(
                        f =>
                            f.FormaPagamento.Id == 8 && f.ValorPago < 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno).ToList();

                listaTroco =
                    transacao.FormasPagamento.Where(
                        f =>
                            f.FormaPagamento.Id == 8 && f.ValorPago > 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno).ToList();

                listaPrePago =
                    transacao.FormasPagamento.Where(
                        f =>
                            (f.FormaPagamento.Tipo.Id == (int)FormaPagamentoTipoEnum.PrePago) && f.ValorPago < 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno).ToList();

                listaCreditoCliente =
                    transacao.FormasPagamento.Where(
                        f =>
                            (f.FormaPagamento == FormaPagamentoEnum.CreditoCliente || f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnline || f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite)/*TODO.PO*/
                            && f.ValorPago > 0 &&
                            f.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Estorno).ToList();
            }

            var listaOutros = transacao.FormasPagamento.ToList();
            listaOutros.RemoveAll(f => listaDinheiro.Select(g => g.FormaPagamento).Contains(f.FormaPagamento));
            listaOutros.RemoveAll(f => listaTroco.Select(g => g.FormaPagamento).Contains(f.FormaPagamento));
            listaOutros.RemoveAll(f => listaCredito.Select(g => g.FormaPagamento).Contains(f.FormaPagamento));
            listaOutros.RemoveAll(f => listaDebito.Select(g => g.FormaPagamento).Contains(f.FormaPagamento));
            listaOutros.RemoveAll(f => listaPrePago.Select(g => g.FormaPagamento).Contains(f.FormaPagamento));
            listaOutros.RemoveAll(f => listaCreditoCliente.Select(g => g.FormaPagamento).Contains(f.FormaPagamento));
            listaOutros.RemoveAll(f => f.FormaPagamento.Tipo == FormaPagamentoTipoEnum.PagamentoEmDebito);

            transacao.TotalPagoEmDinheiro = listaDinheiro.Sum(f => f.ValorPago);
            transacao.TotalPagoEmCredito = listaCredito.Sum(f => f.ValorPago);
            transacao.TotalPagoEmDebito = listaDebito.Sum(f => f.ValorPago);
            transacao.TotalPagoEmPrePago = -listaPrePago.Sum(f => f.ValorPago);
            transacao.TotalPagoEmOutros = listaOutros.Sum(f => f.ValorPago);

            transacao.TotalCreditoCliente = -listaCreditoCliente.Sum(f => f.ValorPago);

            transacao.TotalDescontoOperadoras = transacao.FormasPagamento.Sum(f => f.ValorDescontoOperadora());

            if (transacao.TotalPagar.HasValue && transacao.TotalPagar.Value != 0)
            {
                transacao.PercentualMedioDescontoOperadoras = transacao.TotalDescontoOperadoras /
                                                              transacao.TotalPagar.Value * 100;
            }
            else
                transacao.PercentualMedioDescontoOperadoras = 0;

            if (!transacao.GetDescontarProfissional() && transacao.Troco != 0 && !listaTroco.Any())
            {
                var transacaoFormaPagamento = new TransacaoFormaPagamento
                {
                    FormaPagamento = Domain.Financeiro.FormaPagamentoRepository.Load(8),
                    Transacao = transacao,
                    PessoaQuePagou = transacao.PessoaQuePagou,
                    ValorPago = transacao.Troco ?? 0,
                    ValorBaseCalculoPercentualDesconto = transacao.Troco ?? 0
                };
                transacaoFormaPagamento.Parcelas = new List<TransacaoFormaPagamentoParcela> {
                    new TransacaoFormaPagamentoParcela {
                        DataPagamento = transacao.DataHora,
                        DataRecebimento = transacao.DataHora,
                        TransacaoFormaPagamento = transacaoFormaPagamento,
                        Valor = transacaoFormaPagamento.ValorPago
                    }
                };
                transacao.FormasPagamento.Add(transacaoFormaPagamento);
            }
        }

        private static void ValidarEmissaoNotaFiscalConsumidorSAT(Estabelecimento estabelecimento)
        {
            var validacoes = String.Empty;
            var msgValidacao = String.Empty;

            if (String.IsNullOrEmpty(estabelecimento.PessoaJuridica.InscricaoEstadual))
                validacoes += "\n - Inscrição Estadual";
            if (String.IsNullOrEmpty(estabelecimento.PessoaJuridica.InscricaoMunicipal))
                validacoes += "\n - Inscrição Municipal";
            if (String.IsNullOrEmpty(estabelecimento.PessoaJuridica.NomeFantasia))
                validacoes += "\n - Nome Fantasia";
            if (String.IsNullOrEmpty(estabelecimento.ConfiguracaoDeNFC.SAT.AssinaturaDigitalConjunta))
                validacoes += "\n - Assinatura Digital Conjunta";
            if (!estabelecimento.ConfiguracaoDeNFC.SAT.RegimeEspecialISSQ.HasValue)
                validacoes += "\n	- Regime Especial ISSQN";
            if (!estabelecimento.ConfiguracaoDeNFC.SAT.RateioDescontoISSQN)
                validacoes += "\n - Rateio Desconto ISSQN";

            if (!String.IsNullOrEmpty(validacoes))
            {
                msgValidacao = "\n\n Além da configuração completa dos produtos, é necessário que as seguintes informações estejam registradas para o seu estabelecimento:\n";
                msgValidacao += validacoes;

                ValidationHelper.Instance.AdicionarItemValidacao(msgValidacao);
            }
        }

        private static void ValidarCheckoutDuplicado(Transacao transacao)
        {
            if (transacao.Id > 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    Mensagens.NaoEhPossivelRealizarCheckoutNovamente);
            }
        }

        private void ValidarAlteracaoDeDataDaTransacao(Transacao transacao)
        {
            if (transacao.TeveLancamentoDeDivida())
            {
                var divida = Domain.DebitoParcial.DividaDeixadaNoEstabelecimentoRepository.ObterPorTransacao(transacao.PessoaQueRecebeu.IdPessoa, transacao.Id);

                if (divida != null && !divida.DataPodeSerAlterada())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Este fechamento de conta possui lançamento de uma dívida que já teve pagamentos realizados.\nPara continuar, é necessário estornar os pagamentos dessa dívida.");
                }
            }
        }

        private static void ValidarCheckout(Transacao transacao, PessoaFisica pessoaFisicaOperadorDeCaixa, Venda venda = null, bool validarControleDeCaixaSeNecessario = true)
        {
            if (validarControleDeCaixaSeNecessario && transacao.TotalClubeDeAssinaturas <= 0)
                Domain.Financeiro.ControleDeCaixaService.ValidarTransacao(transacao, pessoaFisicaOperadorDeCaixa);

            if (transacao.Id == 0 && transacao.HorariosTransacoes.Any(f => f.Horario.FoiPago))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    Mensagens.NaoEhPossivelRealizarPagamentosDeServicosJaPagos);
            }

            if (transacao.DataHora.Date > Calendario.Hoje())
                ValidationHelper.Instance.AdicionarItemValidacao(Mensagens.DataMovimentacaoNaoPodeMAiorHoje);

            var exibirDadosAPartirDe =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa)
                    .EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;
            if (exibirDadosAPartirDe.HasValue && transacao.DataHora < exibirDadosAPartirDe.Value)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    string.Format(Mensagens.SomenteEhPossivelRegistrarVendaAPartirDe, exibirDadosAPartirDe.Value));
            }

            if (venda != null)
            {
                var existemPreVendasDeProdutoExcluidas = venda.ItensVenda.Any(f => f is ItemVendaProduto &&
                                                                                    ((ItemVendaProduto)f).VeioDeUmaPreVenda() &&
                                                                                    (((ItemVendaProduto)f).PreVenda.PreVendaStatus == StatusPreVendaEnum.Excluido ||
                                                                                    ((ItemVendaProduto)f).PreVenda.PreVendaStatus == StatusPreVendaEnum.VendaEfetivada));

                if (existemPreVendasDeProdutoExcluidas)
                {
                    var listaPreVendasJaResolvidas = venda.ItensVenda.Where(f => f is ItemVendaProduto &&
                                                                                          ((ItemVendaProduto)f).VeioDeUmaPreVenda() &&
                                                                                          (((ItemVendaProduto)f).PreVenda.PreVendaStatus == StatusPreVendaEnum.Excluido ||
                                                                                          ((ItemVendaProduto)f).PreVenda.PreVendaStatus == StatusPreVendaEnum.VendaEfetivada));

                    if (listaPreVendasJaResolvidas.Count() > 1)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("Existem pré-vendas já finalizadas no fechamento de conta. Para continuar o fechamento exclua as pré-vendas já finalizadas do mesmo.");
                    }
                    else
                    {
                        var produtoComPreVendaJaResolvida = listaPreVendasJaResolvidas.FirstOrDefault();
                        ValidationHelper.Instance.AdicionarItemValidacao("A pré-venda para o produto " + ((ItemVendaProduto)produtoComPreVendaJaResolvida).EstabelecimentoProduto.Descricao + " está com o status " + ((ItemVendaProduto)produtoComPreVendaJaResolvida).PreVenda.PreVendaStatus.Nome + ". Para continuar o fechamento exclua o produto do mesmo.");
                    }
                }

                if (venda.ItensVenda.Any(p => p is ItemVendaValePresente))
                    if (Domain.Vendas.ValePresenteRepository.ExisteValeComONumero(venda.ItensVenda.Where(p => p is ItemVendaValePresente && p != null)
                        .Cast<ItemVendaValePresente>().First().ValePresente.Numero, transacao.PessoaQueRecebeu.IdPessoaJuridica))
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("Já existe um vale cadastrado com este número!");
                    }
            }

            var parcelamentoIrregular = transacao.FormasPagamento.FirstOrDefault(f => f.ValorPago != f.Parcelas.Sum(g => g.Valor));
            if (parcelamentoIrregular != null)
                throw new Exception("Valor em " + parcelamentoIrregular.FormaPagamento.Nome + " e soma das Parcelas está diferente.");

            var numeroParcelasInvalido = transacao.FormasPagamento.FirstOrDefault(tfp => tfp.NumeroParcelas != tfp.Parcelas.Count || tfp.NumeroParcelas == 0);
            if (numeroParcelasInvalido != null)
            {
                throw new Exception("Número incorreto de parcelas em " + numeroParcelasInvalido.FormaPagamento.Nome);
            }

            if (venda != null)
            {
                bool possuiItens = venda.ItensVenda.Any() || transacao.HorariosTransacoes.Any() || transacao.TransacaoItens.Any();
                var possuiPagamentoOnlineAntecipado = transacao.FormasPagamento.Any(f => f.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnline && f.ValorPago != 0);
                var possuiPagamentoDeDividas = transacao.TevePagamentoDeDivida();
                var possuiCompraCredito = transacao.FormasPagamento.Any(f => f.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoCliente && f.ValorPago != 0);
                var possuiPagamentoAntecipadoHotsite = transacao.FormasPagamento.Any(f => f.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite && f.ValorPago != 0);

                if (!possuiItens && !possuiCompraCredito && !possuiPagamentoOnlineAntecipado && !possuiPagamentoDeDividas && !possuiPagamentoAntecipadoHotsite)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Não é possível realizar um fechamento de conta vazio");
                }
            }

            bool jaExistemComissoesEmUmFechamentoMensal = JaExisteComissaoEmUmFechamentoMensal(transacao.Id);
            if (jaExistemComissoesEmUmFechamentoMensal)
                ValidationHelper.Instance.AdicionarItemValidacao("Não foi possível realizar a alteração pois já existem valores de comissão que estão em um fechamento mensal.");

            if (transacao.Id == 0 && transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento)
                VerificarEmCasoDePagamentoEmCreditoClienteSeClientePossuiCreditoSuficiente(transacao);

            //foreach (var ht in transacao.HorariosTransacoes) {
            //	Domain.Pessoas.AgendaService.ValidarManterAgendamento(ht.Horario);
            //}

            ValidarRegistrosDeDividasNaTransacao(transacao);
        }

        private static void ValidarRegistrosDeDividasNaTransacao(Transacao transacao)
        {
            var clienteDeixouDividas = transacao.TeveLancamentoDeDivida();
            var clientePagouDividas = transacao.TevePagamentoDeDivida();

            if (clienteDeixouDividas || clientePagouDividas)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
                var debitoParcial = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, ControleDeFuncionalidades.ObjetosDeValor.Recurso.DebitoParcial);
                if (!debitoParcial.EstaDisponivel)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("No momento, não é possível registrar ou pagar dívidas dos cliente. Entre em contato com nosso atendimento para verificar a liberação desta funcionalidade.");
                    return;
                }
            }

            if (clienteDeixouDividas && !transacao.TemClienteIdentificado())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Para deixar dívida é necessário que o cliente esteja identificado.");
            }

            if (clienteDeixouDividas && clientePagouDividas)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Você não pode deixar para pagar depois e realizar o pagamento de dívida ao mesmo tempo.\nPara continuar, é necessário fechar a conta individualmente.");
            }

            if (clienteDeixouDividas && transacao.PossuiCompraDeCreditoCliente())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é permitido deixar para pagar depois as compras de créditos cliente.");
            }

            if (clienteDeixouDividas && string.IsNullOrWhiteSpace(transacao.ComentarioFechamentoConta))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Comente o motivo do cliente estar deixando para pagar depois.");
            }

            var estaPagandoAgora = transacao.ListarPagamentosDeDividas().Any(p => p.IdTransacaoFormaPagamento == 0);
            if (transacao.TemClienteIdentificado() && clientePagouDividas && estaPagandoAgora)
                Domain.DebitoParcial.PagamentoDeDividaService.ValidarPagamentoDeDividaDoCliente(transacao.PessoaQueRecebeu.IdPessoa, transacao.PessoaQuePagou.IdPessoa, transacao.ObterValorTotalPagoEmDividas().ToValorPositivo());
        }

        private static void VerificarEmCasoDePagamentoEmCreditoClienteSeClientePossuiCreditoSuficiente(Transacao transacao)
        {
            var existePagamentoEmCreditoCliente =
                    transacao.FormasPagamento.Any(p => p.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoCliente &&
                                                       p.ValorPago > 0);
            if (existePagamentoEmCreditoCliente)
            {
                decimal valorPagoEmCredito = 0;

                var estabelecimento =
                    Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                        transacao.PessoaQueRecebeu.PessoaJuridica.IdPessoaJuridica);
                var pagamentosEmCreditoCliente =
                    transacao.FormasPagamento.Where(p => p.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoCliente);

                foreach (var pagamentoEmCreditoCliente in pagamentosEmCreditoCliente)
                {
                    var idPessoaFisicaQuePagou = transacao.PessoaQuePagou.IdPessoa;

                    if (pagamentoEmCreditoCliente.PessoaQuePagou != null)
                        idPessoaFisicaQuePagou = pagamentoEmCreditoCliente.PessoaQuePagou.IdPessoa;

                    var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(idPessoaFisicaQuePagou, estabelecimento.IdEstabelecimento);
                    valorPagoEmCredito += pagamentoEmCreditoCliente.ValorPago;

                    var valorFinalDeCreditos = clienteEstabelecimento.ValorCredito - valorPagoEmCredito;
                    //TAREFA  http://jira.perlink.net/browse/TRINKS-9487
                    //CODIGO TEMPORÁRIO CRIADO PARA CONTINGENCIAR CLIENTES COM CREDITO NEGATIVO
                    //FOI CRIADA UMA CONSTRAINT NO BANCO DE DADOS PARA QUE CLIENTES QUE JÁ POSSUEM CREDITO NEGATIVO POSSAM REALIZAR ESTORNO E FECHAMENTO DE CONTA NORMALMENTE
                    //A REGRA DESSA CONSTRAINT ESTÁ SENDO ADICIONADA NO CÓDIGO TEMPORÁRIAMENTE ATÉ TENHAMOS RESOLVIDO TODOS OS CASOS DE CLIENTES COM CRÉDITO NEGATIVO
                    if (valorFinalDeCreditos < 0 && (string.IsNullOrEmpty(clienteEstabelecimento.IdExterno) || clienteEstabelecimento.IdExterno != "CreditoNegativo"))
                        ValidationHelper.Instance.AdicionarItemValidacao("O cliente não possui crédito suficiente no seu estabelecimento para realizar este pagamento, por favor reveja as informações da tela anterior");
                }
            }
        }

        public static bool JaExisteComissaoEmUmFechamentoMensal(int idTransacao)
        {
            var comissoes = Domain.Financeiro.ComissaoRepository.Queryable().Where(c => c.Transacao.Id == idTransacao).ToList();
            bool jaExistemComissoesEmUmFechamentoMensal = comissoes.Any(c => c.PossuiValorEmUmFechamentoMensal());
            return jaExistemComissoesEmUmFechamentoMensal;
        }

        private void ValidarEstorno(Transacao transacao, List<MovimentacaoDePontos> movimentacoesDePontosDeFidelidadeNestaTransacao, Venda venda = null)
        {
            if (transacao.TipoTransacao.Id != 1)
                ValidationHelper.Instance.AdicionarItemValidacao("Somente é possivel estornar pagamentos!");
            else if (transacao.TipoTransacao.Id == 1 && transacao.PagamentoJaEstornado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Não é possível estornar um pagamento que já foi estornado!");
            }

            var valorEmCredito =
                -transacao.FormasPagamento.Where(f => f.FormaPagamento == FormaPagamentoEnum.CreditoCliente)
                    .Sum(f => f.ValorPago);
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            ClienteEstabelecimento clienteEstabelecimento = null;
            var gorjetaPagaNaTransacao = Domain.Financeiro.GorjetaService.ObterGorjetasAtivasEPagasPelaTransacao(transacao.Id);
            if (gorjetaPagaNaTransacao != null && gorjetaPagaNaTransacao.Any())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível realizar o estorno porque existe uma gorjeta ativa e paga. Para prosseguir, desative a gorjeta e tente novamente");
                return;
            }

            if (transacao.PessoaQuePagou != null)
                clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(
                    transacao.PessoaQuePagou.IdPessoa, estabelecimento.IdEstabelecimento);

            // Para o caso de ser um estorno no profissional, não existe cliente estabelecimento.
            if (clienteEstabelecimento != null)
            {
                //TAREFA  http://jira.perlink.net/browse/TRINKS-9487
                //CODIGO TEMPORÁRIO CRIADO PARA CONTINGENCIAR CLIENTES COM CREDITO NEGATIVO
                //FOI CRIADA UMA CONSTRAINT NO BANCO DE DADOS PARA QUE CLIENTES QUE JÁ POSSUEM CREDITO NEGATIVO POSSAM REALIZAR ESTORNO E FECHAMENTO DE CONTA NORMALMENTE
                //A REGRA DESSA CONSTRAINT ESTÁ SENDO ADICIONADA NO CÓDIGO TEMPORÁRIAMENTE ATÉ TENHAMOS RESOLVIDO TODOS OS CASOS DE CLIENTES COM CRÉDITO NEGATIVO
                if (valorEmCredito > clienteEstabelecimento.ValorCredito && (string.IsNullOrEmpty(clienteEstabelecimento.IdExterno) || clienteEstabelecimento.IdExterno != "CreditoNegativo"))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "Este estorno não poderá ser realizado pois fará com que o cliente fique com saldo de crédito negativo. Existiram outros fechamentos posteriores que consumiram este crédito. Por favor, consulte a movimentação deste cliente no relatório Vendas > Crédito de Cliente.");
                }

                //Código feito para solução temporária para estornos que podem gerar negativos TRINKS-10094. A solução foi não permitir o estorno de uso de pontos antigos (alinhado com a carina)
                DateTime dataQueFoiContigenciado = new DateTime(2017, 05, 10);
                bool usouPontosNaTransacao = movimentacoesDePontosDeFidelidadeNestaTransacao.Any(m => m.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.UsoDePontos);
                if (usouPontosNaTransacao && transacao.DataHora < dataQueFoiContigenciado)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Para efetuar o estorno desta transação entre em contato com a Equipe Trinks =)");
                    return;
                }

                bool existeMovimentacaoDeGanhoDePontosQueTemPontosUsados = movimentacoesDePontosDeFidelidadeNestaTransacao
                    .Any(m => m.PontoGanhoQueAMovimentacaoGerou != null && m.PontoGanhoQueAMovimentacaoGerou.QuantidadeDePontosUsados > 0
                    && !m.DescricaoDoItem.ToLower().Contains("automaticamente devido ao saldo negativo") && m.IdTransacao != transacao.Id);//teste com a descrição feito para solução temporária para estornos que geraram negativos TRINKS-10094. A solução foi aplicar saldos negativos para os clientes que estão com saldo positivo de forma errada e acabaram usando mais pontos do que tinham (alinhado com a carina)

                //Código feito para solução temporária para estornos que geraram negativos TRINKS-10094. A solução foi aplicar saldos negativos para os clientes que estão com saldo positivo de forma errada e acabaram usando mais pontos do que tinham (alinhado com a carina)
                if (!existeMovimentacaoDeGanhoDePontosQueTemPontosUsados)
                {
                    var movimentacaoQuePassouDeNegativoPraPositivo = movimentacoesDePontosDeFidelidadeNestaTransacao.FirstOrDefault(m => m.DescricaoDoItem.ToLower().Contains("pontos descontados automaticamente devido ao saldo negativo"));

                    if (movimentacaoQuePassouDeNegativoPraPositivo != null)
                    {
                        var transacoesQueImpedemEstorno = Domain.Fidelidade.MovimentacaoDePontosRepository.ObterTransacoesQueImpedemEstornoDeGanho(movimentacaoQuePassouDeNegativoPraPositivo.PontoGanhoQueAMovimentacaoGerou);
                        existeMovimentacaoDeGanhoDePontosQueTemPontosUsados = transacoesQueImpedemEstorno.Count > 0;
                    }
                }
                //-----//

                if (existeMovimentacaoDeGanhoDePontosQueTemPontosUsados)
                {
                    var mensagemDePontosUsados = ObterMensagemDePontosUsadosPrendendoOEstorno(movimentacoesDePontosDeFidelidadeNestaTransacao);
                    ValidationHelper.Instance.AdicionarItemValidacao(mensagemDePontosUsados);
                }
            }

            var idsComandas = new List<Int32>();

            if (venda != null)
            {

                VerificarSePacoteJaFoiConsumido(venda, transacao);

                var idsComandasProduto = venda.ItensVenda.Where(p => p is ItemVendaProduto).Cast<ItemVendaProduto>().Where(p => p.PreVenda != null && p.PreVenda.Comanda != null).Select(p => p.PreVenda.Comanda.Id).ToList();
                idsComandas.AddRange(idsComandasProduto);

                if (venda.ItensVenda.Where(p => p is ItemVendaValePresente).Cast<ItemVendaValePresente>().Any(itemValePresente => itemValePresente.ValePresente.SaldoAtual != itemValePresente.ValePresente.SaldoInicial))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Este vale-presente já foi utilizado. Para estorná-lo, é necessário que seja feito o estorno do fechamento com sua utilização. Consulte em Venda > Vale-Presente as informações sobre sua utilização.");
                }
            }

            var idsComandasServico = transacao.HorariosTransacoes.Where(p => p.Horario.PreVenda() != null && p.Horario.PreVenda().Comanda != null).Select(p => p.Horario.PreVenda().Comanda.Id).ToList();
            idsComandas.AddRange(idsComandasServico);

            if (idsComandas.Count > 0)
            {
                foreach (var idComanda in idsComandas)
                {
                    var comanda = Domain.Vendas.ComandaRepository.Load(idComanda);

                    if (comanda.Fechada)
                    {
                        var existeComandaAberta = Domain.Vendas.ComandaRepository.ExisteComandaAbertaComMesmoNumero(comanda.Numero, estabelecimento.IdEstabelecimento, comanda.Id);

                        if (existeComandaAberta)
                        {
                            ValidationHelper.Instance.AdicionarItemValidacao(String.Format("Este fechamento de conta não pode ser estornado, pois a comanda {0} se encontra aberta no momento. Antes de fazer este estorno, feche a conta da comanda {0}", comanda.Numero));
                            break;
                        }
                    }
                }
            }

            VerificarSeEhPossivelEstornarTransacaoComSplitDePagamento(transacao);
            ValidarEstornoDePagamentosOnline(transacao);
            ValidarEstornoDeDivida(transacao);

        }

        public void VerificarSePacoteJaFoiConsumido(Venda venda, Transacao transacao)
        {
            var existeConsumoDePacoteCompradoEmOutroCheckout = false;

            foreach (var itemPacoteCliente in venda.ItensVenda.Where(p => p is ItemVendaPacote).Cast<ItemVendaPacote>().SelectMany(itemPacote => itemPacote.PacoteCliente.ItensPacoteCliente))
            {
                ItemPacoteCliente itemDoPacote;

                if (itemPacoteCliente is INHibernateProxy)
                {
                    itemDoPacote = Domain.Pacotes.ItemPacoteClienteRepository.Load(itemPacoteCliente.Id);
                } else
                {
                    itemDoPacote = itemPacoteCliente;
                }
                

                if (itemDoPacote is ItemPacoteClienteServico)
                {
                    var item = itemDoPacote as ItemPacoteClienteServico;
                    var qntHorariosConsumidos =
                        transacao.HorariosTransacoes.Count(
                            p =>
                                p.ItemPacoteCliente != null && p.ItemPacoteCliente.Id == item.Id);
                    if (itemDoPacote.QuantidadeConsumida != qntHorariosConsumidos)
                        existeConsumoDePacoteCompradoEmOutroCheckout = true;
                }

                if (itemDoPacote is ItemPacoteClienteProduto)
                {
                    var item = itemDoPacote as ItemPacoteClienteProduto;
                    var qntProdutosConsumidos =
                        venda.ItensVenda.Count(
                            p =>
                                p is ItemVendaProduto && p.ItemPacoteCliente != null &&
                                p.ItemPacoteCliente.Id == item.Id);
                    if (itemDoPacote.QuantidadeConsumida != qntProdutosConsumidos)
                        existeConsumoDePacoteCompradoEmOutroCheckout = true;
                }
            }

            if (existeConsumoDePacoteCompradoEmOutroCheckout)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    Mensagens.PacoteComItensJaConsumidosEmOutroFechamento);
            }
        }

        private void ValidarEstornoDePagamentosOnline(Transacao transacao)
        {
            if (EhCompraDeCreditoGeradaPorEstornoDeCreditoDePagamentoOnline(transacao))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Créditos de clientes oriundos de pagamentos online não podem ser estornados.");
            }
        }

        private bool EhCompraDeCreditoGeradaPorEstornoDeCreditoDePagamentoOnline(Transacao transacao)
        {
            return transacao.TipoTransacao == TipoTransacaoEnum.Pagamento
                && TransacaoPossuiCompraDeCredito(transacao)
                && TransacaoUtilizaCreditoDePagamentoOnline(transacao);
        }

        private bool TransacaoPossuiCompraDeCredito(Transacao transacao)
        {
            return transacao.FormasPagamento.Any(fp => fp.FormaPagamento.Id == (int)FormaPagamentoPrePagoEnum.CreditoCliente);
        }

        private static void VerificarSeEhPossivelEstornarTransacaoComSplitDePagamento(Transacao transacao)
        {
            bool transacaoPossuiRegistrosDeSplit = VerificarSeTransacaoPossuiSplitDePagamento(transacao);
            if (transacaoPossuiRegistrosDeSplit)
            {
                if (transacao.DataHora.Date != Calendario.Hoje())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Para fechamentos de conta que tiveram split no pagamento, o estorno só pode ser realizado no mesmo dia do pagamento, a partir do dia seguinte o estorno do split deverá ser manual por fora do sistema");
                }
            }
        }

        private static bool VerificarSeTransacaoPossuiSplitDePagamento(Transacao transacao)
        {
            return transacao.HorariosTransacoes.Any(p => (p.Comissao != null && p.Comissao.EhComissaoComSplit) ||
                                                          (p.ComissaoAssistente != null && p.ComissaoAssistente.EhComissaoComSplit));
        }

        private string ObterMensagemDePontosUsadosPrendendoOEstorno(List<MovimentacaoDePontos> movimentacoesDePontosDeFidelidadeNestaTransacao)
        {
            string mensagem = "Este estorno não poderá ser realizado pois o cliente ganhou pontos no programa de fidelidade que já foram usados em outros fechamentos. Os fechamentos que consumiram os pontos ganhos foram nas datas:";
            foreach (var movimentacao in movimentacoesDePontosDeFidelidadeNestaTransacao)
            {
                if (movimentacao.PontoGanhoQueAMovimentacaoGerou != null)
                {
                    var transacoesQueImpedemEstorno = Domain.Fidelidade.MovimentacaoDePontosRepository.ObterTransacoesQueImpedemEstornoDeGanho(movimentacao.PontoGanhoQueAMovimentacaoGerou);
                    foreach (var t in transacoesQueImpedemEstorno)
                        mensagem = mensagem + "\n - " + t.DataHora;
                }
            }
            return mensagem;
        }

        private void RecalcularCheckOut(DadosParaRecalculoComissao item)
        {
            if (item.Comissao == null)
            {
                ValidationHelper.Instance.AdicionarItemNotificacao("Um ou mais serviços não foram recalculados pois o profisisonal não está mais associado a ele.");
                return;
            }

            var horarioTransacao = item.HorarioTransacao;

            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(horarioTransacao.Transacao);
            RecalcularTotais(horarioTransacao.Transacao, venda);

            horarioTransacao.Descartaveis = item.CustoDescartaveis;

            if (horarioTransacao.Comissao != null)
            {
                if (item.Comissao.EstabelecimentoProfissionalServico != null && item.Comissao.EstabelecimentoProfissionalServico.Ativo)
                {
                    horarioTransacao.Comissao.ValorComissao = Domain.Pessoas.HorarioService.DefinirComissaoDoProfissional(item.Comissao, item.HorarioTransacao.Horario);
                }
                else
                {
                    horarioTransacao.Comissao.ValorComissao = 0;
                }
            }
            if (horarioTransacao.ComissaoAssistente != null)
            {
                if (item.ComissaoAssistente.EstabelecimentoAssistenteServico != null && item.ComissaoAssistente.EstabelecimentoAssistenteServico.Ativo)
                {
                    horarioTransacao.ComissaoAssistente.ValorComissao = item.ComissaoAssistente.ValorComissao ?? 0;
                }
                else
                {
                    horarioTransacao.ComissaoAssistente.ValorComissao = 0;
                }
            }

            throw new NotImplementedException();
            //Domain.Financeiro.ComissaoService.CalcularComissaoProfissionalServico(horarioTransacao, item.Comissao, item.ComissaoAssistente);
        }

        private void RecalcularVenda(ItemVendaProduto item)
        {
            RecalcularTotais(item.Venda.Transacao, item.Venda);
            CalcularComissaoItemVendaProduto(item);
        }

        #endregion Métodos 'Private'

        #region Comissao

        [TransactionInitRequired]
        public void CalcularComissaoItemVendaPacote(ItemVendaPacote itemVenda)
        {
            var comissao = itemVenda.Comissao;
            var configuracoes = itemVenda.PacoteCliente.PacoteOriginal.Estabelecimento.EstabelecimentoConfiguracaoGeral;

            comissao.Transacao = itemVenda.Venda.Transacao;
            comissao.ValorBase = itemVenda.ValorFinal();

            decimal desconto = 0;
            if (itemVenda.MotivoDesconto == null || itemVenda.MotivoDesconto.DescontoRefleteNaComissao)
                desconto = itemVenda.Desconto;

            comissao.DescontoOperadora = 0;
            bool considerarDescontoOperadoraNaComissao = Domain.Financeiro.TransacaoService.ConsiderarDescontoOperadoraNaComissaoParaPessoa(comissao.PessoaComissionada, itemVenda.PacoteCliente.PacoteOriginal.Estabelecimento, ehParaAssistente: false);
            //Considerar O de N, rever desempenho
            if (considerarDescontoOperadoraNaComissao)
            {
                comissao.DescontoOperadora = -comissao.ValorBase *
                                             ((itemVenda.ItemPacoteCliente != null ?
                                             itemVenda.ItemPacoteCliente.PacoteCliente.Venda.Transacao.PercentualMedioDescontoOperadoras :
                                             itemVenda.Venda.Transacao.PercentualMedioDescontoOperadoras) / 100);
            }

            if (itemVenda.MotivoDesconto == null ||
                (itemVenda.MotivoDesconto != null && itemVenda.MotivoDesconto.DescontoRefleteNaComissao))
                comissao.DescontoCliente = desconto;

            comissao.ValorBase += comissao.DescontoOperadora;
            comissao.ValorComissao = itemVenda.PacoteCliente.Valor;
            comissao.DescontoExtraNoValorBase = configuracoes.DescontarDescartaveisDoValorPago;

            DefinirValorAPagarComissao(comissao);

            comissao.CalcularValoresAReceber();
        }

        [TransactionInitRequired]
        public void CalcularComissaoItemVendaProduto(ItemVendaProduto itemVenda)
        {
            var comissao = itemVenda.Comissao;
            itemVenda.EstabelecimentoProduto.Refresh();
            var configuracoes = itemVenda.EstabelecimentoProduto.Estabelecimento.EstabelecimentoConfiguracaoGeral;

            comissao.Transacao = itemVenda.Venda.Transacao;

            decimal desconto = 0;
            if (itemVenda.MotivoDesconto == null || itemVenda.MotivoDesconto.DescontoRefleteNaComissao)
            {
                desconto = itemVenda.Desconto;
                comissao.DescontoCliente = itemVenda.Desconto;
            }

            comissao.ValorBase = itemVenda.ItemPacoteCliente == null
                ? (itemVenda.SubTotal) + desconto
                : itemVenda.ItemPacoteCliente.ValorUnitario;

            comissao.DescontoOperadora = 0;
            bool considerarDescontoOperadoraNaComissao = Domain.Financeiro.TransacaoService.ConsiderarDescontoOperadoraNaComissaoParaPessoa(comissao.PessoaComissionada, itemVenda.EstabelecimentoProduto.Estabelecimento, ehParaAssistente: false);
            //Considerar O de N, rever desempenho
            if (considerarDescontoOperadoraNaComissao)
            {
                comissao.DescontoOperadora = -comissao.ValorBase *
                                             ((itemVenda.ItemPacoteCliente != null ?
                                              itemVenda.ItemPacoteCliente.PacoteCliente.Venda.Transacao.PercentualMedioDescontoOperadoras :
                                              itemVenda.Venda.Transacao.PercentualMedioDescontoOperadoras) / 100);
            }

            comissao.ValorBase += comissao.DescontoOperadora;
            comissao.ValorComissao = itemVenda.EstabelecimentoProduto.ValorComissaoRevenda;
            comissao.DescontoExtraNoValorBase = configuracoes.DescontarDescartaveisDoValorPago;

            DefinirValorAPagarComissao(comissao);

            if (itemVenda.ItemPacoteCliente != null)
            {
                comissao.ConsumoDePacote = true;
                comissao.ValorBruto = itemVenda.ItemPacoteCliente.ValorUnitario;
            }
            else
                comissao.ValorBruto = itemVenda.SubTotal;

            comissao.CalcularValoresAReceber();
        }

        //[TransactionInitRequired]
        //public void RecalcularComissoesOriginaisDeComissoesInformadasManualmente(
        //    IEnumerable<DadosParaRecalculoComissao> dadosParaRecalculoComissao) {
        //    foreach (var item in dadosParaRecalculoComissao) {
        //        RecalcularComissaoInformadasManualmente(item);
        //    }
        //}

        private static void DefinirValorAPagarComissao(Comissao comissao)
        {
            decimal? valor;
            var valorComissao = comissao.ComissaoAlteradaManualmente
                ? comissao.PercentualDeComissaoOriginal
                : comissao.ValorComissao;
            if (comissao.TipoComissao.Id == 2)
                valor = comissao.ValorBase * valorComissao / 100;
            else
                valor = valorComissao;

            if (!comissao.DescontoExtraNoValorBase)
                valor += comissao.DescontoExtra;

            if (comissao.ComissaoAlteradaManualmente)
                comissao.ValorComissaoParaPagarOriginal = valor > 0 ? valor : 0;
            else
                comissao.ComissaoParaPagar = valor > 0 ? valor : 0;
        }

        //private static void RecalcularComissaoInformadasManualmente(DadosParaRecalculoComissao item) {
        //    var horarioTransacao = item.HorarioTransacao;

        //    horarioTransacao.Descartaveis = item.CustoDescartaveis;
        //    //[TRINKS-4168] Erro causava que a comissão alterada manualmente voltasse pruma comissao após editá-la
        //    //horarioTransacao.Comissao.PercentualDeComissaoOriginal = item.Comissao.ValorComissao;
        //    Domain.Financeiro.ComissaoService.CalcularComissaoProfissionalServico(horarioTransacao, item.Comissao, item.ComissaoAssistente);
        //}

        public int GerarEGravarNumeroDoFechamento(PessoaJuridica pessoaJuridica)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoaJuridica.IdPessoa);
            return GerarNumeroDoFechamentoPelasTransacoes(estabelecimento);
        }

        private static int GerarNumeroDoFechamentoPelasTransacoes(Estabelecimento estabelecimento)
        {
            var ultimoNumeroDeFechamento = estabelecimento.EstabelecimentoConfiguracaoGeral.NumeroDoUltimoFechamento;
            int proximoNumero = 0;
            if (!ultimoNumeroDeFechamento.HasValue)
            {
                int ultimoGerado = Domain.Financeiro.TransacaoRepository.Queryable().Where(f => f.PessoaQueRecebeu == estabelecimento.PessoaJuridica).Max(f => f.NumeroDaPreVenda) ?? 0;
                proximoNumero = ultimoGerado + 1;
            }
            else
            {
                proximoNumero = ultimoNumeroDeFechamento.Value + 1;
            }

            estabelecimento.EstabelecimentoConfiguracaoGeral.NumeroDoUltimoFechamento = proximoNumero;

            return proximoNumero;
        }

        #endregion Comissao

        #region NFCe

        public string ObterUrlQRCodeDaNFC(NotaNFC notaNFC)
        {
            return notaNFC.UrlDoQRCode;
        }

        public byte[] ObterImagemQRCodeDaNFCe(NotaNFC notaNFC, Estabelecimento estabelecimento)
        {
            byte[] bytes = null;

            if (notaNFC != null || estabelecimento.PossuiInterfaceNfcComQRCode())
            {
                var urlDoQRCode = ObterUrlQRCodeDaNFC(notaNFC);

                QRCoder.QRCodeGenerator qrGenerator = new QRCoder.QRCodeGenerator();
                QRCoder.QRCodeGenerator.QRCode qrCode = qrGenerator.CreateQrCode(urlDoQRCode, QRCoder.QRCodeGenerator.ECCLevel.Q);
                var codigoGerado = qrCode.GetGraphic(20);

                System.Drawing.ImageConverter converter = new System.Drawing.ImageConverter();
                bytes = (byte[])converter.ConvertTo(codigoGerado, typeof(byte[]));
            }

            return bytes;
        }

        #endregion NFCe

        public bool PessoaRecebeComissaoNaDataPrevistaDeRecebimento(PessoaFisica pessoaComissionada, Estabelecimento estabelecimento)
        {
            bool recebeComissaoNaDataPrevistaDeRecebimento = false;
            if (pessoaComissionada != null)
            {
                var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(pessoaComissionada.IdPessoa, estabelecimento.IdEstabelecimento);
                if (estabelecimentoProfissional != null)
                    recebeComissaoNaDataPrevistaDeRecebimento = estabelecimentoProfissional.RecebeComissaoNaDataPrevistaDeRecebimento;
            }
            return recebeComissaoNaDataPrevistaDeRecebimento;
        }

        public bool ConsiderarDescontoOperadoraNaComissaoParaPessoa(PessoaFisica pessoaComissionada, Estabelecimento estabelecimento, bool ehParaAssistente)
        {
            bool descontaOperadora = false;
            if (pessoaComissionada != null)
            {
                var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(pessoaComissionada.IdPessoa, estabelecimento.IdEstabelecimento);
                if (estabelecimentoProfissional != null)
                    if (ehParaAssistente)
                        descontaOperadora = estabelecimentoProfissional.DescontaTaxaOperadoraNaComissaoAssistente;
                    else
                        descontaOperadora = estabelecimentoProfissional.DescontaTaxaOperadoraNaComissao;
            }
            return descontaOperadora;
        }

        public bool TransacaoPertenceAoEstabelecimento(int idTransacao, int idEstabelecimento)
        {
            var queryableEstabelecimetno = Domain.Pessoas.EstabelecimentoRepository.Queryable();
            var queryableTransacao = Domain.Financeiro.TransacaoRepository.Queryable();

            var mesmaPessoa = (from t in queryableTransacao
                               join e in queryableEstabelecimetno on t.PessoaQueRecebeu.IdPessoa equals e.PessoaJuridica.IdPessoa
                               where t.Id == idTransacao && e.IdEstabelecimento == idEstabelecimento
                               select t.Id).Any();

            return mesmaPessoa;
        }

        private void ManterHistoricoDaTransacao(Transacao transacao, PessoaFisica pessoaFisicaOperadorDeCaixa, bool jaTeveControleDeCaixaPorProfissional = false)
        {
            RegistroDeCaixaPorOperador registroCaixa = null;
            if (pessoaFisicaOperadorDeCaixa != null && jaTeveControleDeCaixaPorProfissional)
                registroCaixa = Domain.Financeiro.RegistroDeCaixaPorOperadorRepository.ObterPorData(transacao.PessoaQueRecebeu.IdPessoa, pessoaFisicaOperadorDeCaixa.IdPessoa, transacao.DataHora);

            var dadosDesatualizadosDaTransacao = ObterDadosParaTransacaoHistorico(transacao, jaTeveControleDeCaixaPorProfissional);

            if (TransacaoFoiAlterada(transacao, registroCaixa, dadosDesatualizadosDaTransacao))
            {
                var novoHistorico = Domain.Financeiro.TransacaoHistoricoRepository.Factory.CreateParaTransacaoAlterada(transacao, dadosDesatualizadosDaTransacao, registroCaixa);
                Domain.Financeiro.TransacaoHistoricoRepository.SaveNew(novoHistorico);
            }
        }

        private bool TransacaoFoiAlterada(Transacao transacao, RegistroDeCaixaPorOperador registroDeCaixa, DadosParaTransacaoHistoricoDTO dadosDesatualizadosDaTransacao)
        {
            return transacao.Id > 0
                   && (dadosDesatualizadosDaTransacao.DataHora != transacao.DataHora
                   || (registroDeCaixa != null && dadosDesatualizadosDaTransacao.IdRegistroDeCaixa == null)
                   || (registroDeCaixa == null && dadosDesatualizadosDaTransacao.IdRegistroDeCaixa != null)
                   || (registroDeCaixa != null && dadosDesatualizadosDaTransacao.IdRegistroDeCaixa != registroDeCaixa.Id));
        }

        private DadosParaTransacaoHistoricoDTO ObterDadosParaTransacaoHistorico(Transacao transacao, bool jaTeveControleDeCaixaPorProfissional = false)
        {
            var dadosTransacao = new DadosParaTransacaoHistoricoDTO();

            dadosTransacao.DataHora = Domain.Financeiro.TransacaoRepository.StatelessQueryable().Where(t => t.Id == transacao.Id).Select(t => t.DataHora).FirstOrDefault();

            if (jaTeveControleDeCaixaPorProfissional)
            {
                var movimentacaoDaTransacao = Domain.Financeiro.MovimentacaoNoCaixaPorOperadorTransacaoRepository.ObterAtivoPelaTransacao(transacao.Id);
                if (movimentacaoDaTransacao != null)
                {
                    dadosTransacao.IdPessoaFisicaOperadorCaixa = movimentacaoDaTransacao.IdPessoaFisicaOperador;
                    dadosTransacao.IdRegistroDeCaixa = movimentacaoDaTransacao.CaixaPorOperador.Id;
                }
            }

            return dadosTransacao;
        }

        public void ValidarEstorno(Transacao transacao)
        {
            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);
            var movimentacoesDePontosDeFidelidadeNestaTransacao = Domain.Fidelidade.MovimentacaoDePontosRepository.ObterMovimentacoesDePontosDestaTransacao(transacao);

            ValidarEstorno(transacao, movimentacoesDePontosDeFidelidadeNestaTransacao);
        }

        public bool PodeEstornarTransacao(Transacao transacao)
        {
            return Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.Fechamento_Estornar)
                && transacao.TipoTransacao == TipoTransacaoEnum.Pagamento
                && !transacao.PagamentoJaEstornado
                && !EhCompraDeCreditoGeradaPorEstornoDeCreditoDePagamentoOnline(transacao);
        }

        public void ValidarEstornoDeFechamentoComMensagemNaTela(Estabelecimento estabelecimento, int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.ObterPorIdTransacao(idTransacao);

            if (transacao == null || transacao.PessoaQueRecebeu.IdPessoa != estabelecimento.PessoaJuridica.IdPessoa)
                ValidationHelper.Instance.AdicionarItemValidacao("Este fechamento não pode ser estornado.");

            if (!PodeEstornarTransacao(transacao))
                ValidationHelper.Instance.AdicionarItemValidacao("Este fechamento não pode ser estornado.");

            if (EhEstabelecimentoDeFranquiaQueNaoPermiteQueEstornoSejaRealizado(estabelecimento, transacao))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Somente fechamentos de contas/vendas do mês atual podem ser estornados.");
            }

            if (ValidationHelper.Instance.IsValid)
            {
                Domain.Financeiro.ControleDeCaixaService.PedirConfirmacaoCasoEstejaEstornandoFechamentoAssociadoAUmCaixa(idTransacao, estabelecimento);
                Domain.Financeiro.PagamentoDeProfissionaisService.PedirConfirmacaoParaEstornoCasoSejaComissaoJaPaga(idTransacao, estabelecimento.PessoaJuridica.IdPessoa);
            }
        }

        private bool EhEstabelecimentoDeFranquiaQueNaoPermiteQueEstornoSejaRealizado(Estabelecimento estabelecimento, Transacao transacao)
        {
            var bloqueadoPelaFranquia = false;

            if (estabelecimento.EhUmEstabelecimentoFranqueado() &&
                estabelecimento.FranquiaEstabelecimento.Franquia.LimitaEstornoDosFechamentosContaParaOutrosMeses)
            {
                int mesDoFechamentoDeConta = transacao.DataHora.Month;
                int mesAtual = Calendario.Hoje().Month;

                bloqueadoPelaFranquia = mesDoFechamentoDeConta != mesAtual;
            }

            return bloqueadoPelaFranquia;
        }

        public void ValidarPermissaoParaAlterarDataDaTransacao(Estabelecimento estabelecimento, int idTransacao, DateTime dataMovimentacaoAposEdicao)
        {
            if (estabelecimento.EhUmEstabelecimentoFranqueado() &&
                estabelecimento.FranquiaEstabelecimento.Franquia.LimitaAlteracaDeDataDosFechamentosContaParaOutrosMeses)
            {
                DateTime dataOriginalDaTransacao = Domain.Financeiro.TransacaoRepository.ObterDataHoraPorIdTransacao(idTransacao);

                bool alterouDataDeMovimentacao = dataOriginalDaTransacao.DataComHora() != dataMovimentacaoAposEdicao.DataComHora();

                if (!alterouDataDeMovimentacao)
                    return;

                int mesOriginalDoFechamentoDeConta = dataOriginalDaTransacao.Month;
                int mesAposEdicaoDoFechamentoDeConta = dataMovimentacaoAposEdicao.Month;
                int mesAtual = Calendario.Hoje().Month;

                bool estaAlterandoFechamentoAnteriorAoMesAtual = mesOriginalDoFechamentoDeConta != mesAtual;
                bool estaAlterandoFechamentoParaOutroMes = mesOriginalDoFechamentoDeConta != mesAposEdicaoDoFechamentoDeConta;

                if (estaAlterandoFechamentoAnteriorAoMesAtual)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Somente fechamentos de contas/vendas do mês atual podem ter a data de movimentação alterada.");
                }
                else if (estaAlterandoFechamentoParaOutroMes)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("A data de movimentação somente pode ser alterada para uma dia do mês atual.");
                }
            }
        }

        public async Task<int> CriarTransacaoParaPagamentosDoClubeAsync(TransicaoParaClubeAssinaturaDTO filtro)
        {
            filtro.Tipo = filtro.EhTransacaoDeMultaDeCancelamento ?
                TransacaoItemTipo.PagamentoDeMultaDeCancelamento : TransacaoItemTipo.PagamentoDeAssinatura;

            var transacao = Domain.Financeiro.TransacaoRepository.Factory.CreateParaPagementosDoClub(filtro);
            var checkoutDTO = new CheckoutDTO(transacao, transacao.PessoaQueRealizou)
            {
                Venda = transacao.Vendas.First(),
                ControlarCaixaSeNecessario = !filtro.EhAssinaturaPorLink,
                JaTeveCaixaPorProfissionalAberto = filtro.PessoaQueRecebeu.Estabelecimento.EstabelecimentoConfiguracaoGeral.JaTeveCaixaPorProfissionalAberto,
                ControlarPagamentoPOS = false,
            };

            var result = await RealizarCheckOut(checkoutDTO);

            return result.Transacao.Id;
        }

        public async Task<ResultadoCheckOutDTO> GerarTransacaoFinanceiraVendaPacoteNoHotsite(VendaHotsiteDTO dto, PessoaFisica pessoaQuePagou)
        {
            var pessoaQueRecebeu = Domain.Pessoas.EstabelecimentoRepository.Load(dto.IdEstabelecimento).PessoaJuridica;
            var estabelecimentoTemNFC = pessoaQueRecebeu.Estabelecimento.EstabelecimentoPossuiNFC();

            CriarOuAssociarFormaPagamentoSeNecessario(dto.IdEstabelecimento);

            dto.Tipo = TransacaoItemTipo.VendaDePacoteHotsite;
            var transacao = Domain.Financeiro.TransacaoRepository.Factory.CreateParaVendaPacoteHotsite(dto, pessoaQuePagou, pessoaQueRecebeu);

            var checkoutDTO = new CheckoutDTO(transacao, transacao.PessoaQuePagou)
            {
                Venda = transacao.Vendas.First(),
                ControlarCaixaSeNecessario = false,
                ControlarPagamentoPOS = false,
                EmitirNFC = estabelecimentoTemNFC
            };

            return await RealizarCheckOut(checkoutDTO);
        }
        
        public async Task<ResultadoCheckOutDTO> RealizarTransacaoPagamentoAntecipadoHotsite(PagamentoAntecipadoHotsiteDto filtro)
        {
            CriarOuAssociarFormaPagamentoSeNecessario(filtro.IdEstabelecimento);

            filtro.Tipo = TransacaoItemTipo.PagamentoAntecipadoHotsite;
            var transacao = Domain.Financeiro.TransacaoRepository.Factory.CreateParaPagamentoAntecipadoHotsite(filtro);

            var checkoutDto = new CheckoutDTO(transacao, transacao.PessoaQuePagou)
            {
                Venda = transacao.Vendas.First(),
                EmitirNFC = false,
                ControlarCaixaSeNecessario = false,
                ControlarPagamentoPOS = false,
            };
            
            return await RealizarCheckOut(checkoutDto);
        }

        public void CriarOuAssociarFormaPagamentoSeNecessario(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
            
            var formaDePagamentoPagarmeCredito = Domain.Financeiro.FormaPagamentoRepository.ObterPorIdFormaPagamento((int)FormaPagamentoEnum.PagarmeCredito);
            var formaDePagamentoCreditoOnlineHotsite = Domain.Financeiro.FormaPagamentoRepository.ObterPorIdFormaPagamento((int)FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite);
            
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoPagarmeCredito, estabelecimento);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoCreditoOnlineHotsite, estabelecimento);

            var pixDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(idEstabelecimento, Recurso.HabilitarPixNoLinkDePagamento).EstaDisponivel;
            
            if (pixDisponivel)
            {
                var formaDePagamentoPagarmePix = Domain.Financeiro.FormaPagamentoRepository.ObterPorIdFormaPagamento((int)FormaPagamentoEnum.PagarmePix);
                Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoPagarmePix, estabelecimento);
            }
        }

        public bool FranquiaPermiteAlterarValoresDosServicosEProdutos(Estabelecimento estabelecimento)
        {

            if (!estabelecimento.EhUmEstabelecimentoFranqueadoAtivo())
                return true;

            if (estabelecimento.FranquiaEstabelecimento.PermiteAlteracaoDeValores)
                return true;

            var idConta = Domain.WebContext.IdContaAutenticada;

            bool ehContaVinculadaAFranquia = idConta > 0 &&
                Domain.Pessoas.ContaFranquiaRepository.EhContaVinculadaAFranquia(idConta.Value, estabelecimento.FranquiaEstabelecimento.Franquia.Id);

            return ehContaVinculadaAFranquia;
        }

        public PermissoesNoFechamentoDeContasDTO ObterPermissoesParaFecharConta(Estabelecimento estabelecimento)
        {

            var podeAlterarValores = FranquiaPermiteAlterarValoresDosServicosEProdutos(estabelecimento);

            return new PermissoesNoFechamentoDeContasDTO
            {
                AdicionarDescontosNosItens = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.FechamentoContas_AdicionarDescontosNosItens),
                RetirarItensDoFechamento = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.FechamentoContas_RetirarItensDoFechamento),
                AlterarPrecoDeVendaDosProdutos = podeAlterarValores && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.Produto_AlterarPrecoDeVenda) && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.FechamentoContas_AlterarPrecoDosItens),
                AlterarPrecoNoAgendamento = podeAlterarValores && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.Servicos_AlterarPrecoNoAgendamento) && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.FechamentoContas_AlterarPrecoDosItens),
            };
        }

        public TaxasDTO ObterTaxasParaTransacao(int? idTaxasPagamentoOnlineTrinks)
        {

            if (idTaxasPagamentoOnlineTrinks == null)
                return new TaxasDTO();

            var taxasPagamentoOnlineTrinks = Domain.PagamentosOnlineNoTrinks.TaxasDoPagamentoOnlineNoTrinksRepository.Load(idTaxasPagamentoOnlineTrinks.Value);


            var percentualCobradoPelaOperadora = taxasPagamentoOnlineTrinks.ParametrosDeRecebimento.ObterPercentualTotalPorTransacao();
            var valorFixoCobradoPelaOperadora = taxasPagamentoOnlineTrinks.ParametrosDeRecebimento.ObterValorFixoTotalPorOperadora();

            var taxas = new TaxasDTO(percentualCobradoPelaOperadora, valorFixoCobradoPelaOperadora);
            return taxas;
        }

        public List<KeyValuePair<string, string>> PreencherItensPendentesCasoHouver(PreencherItensPendentesDTO preencherItensPendentes)
        {
            var hoje = Calendario.Hoje();

            if (!preencherItensPendentes.TemConfiguracaoServicosAindaPendentesHabilitada 
                || preencherItensPendentes.Data == null
                || !preencherItensPendentes.Data.DentroPeriodo(hoje.Date, hoje.Date.AddDays(1)))
                return new List<KeyValuePair<string, string>>();

            var agendamentosRestantesCliente = Domain.Pessoas.HorarioRepository.AgendamentosAindaPendentesNaData(
                preencherItensPendentes.IdEstabelecimento, 
                hoje, 
                preencherItensPendentes.IdEstabelecimentoProfissional
            );

            var preVendasProdutoRestantesCliente = Domain.Vendas.PreVendaProdutoRepository
                .ObterAbertasPelaDataPorPessoaFisicaComprador(hoje, preencherItensPendentes.IdPessoaComprador)
                .ToList();

            if (!agendamentosRestantesCliente.Any() && !preVendasProdutoRestantesCliente.Any())
                return new List<KeyValuePair<string, string>>();

            var itensPendentesCliente = new List<KeyValuePair<string, string>>();

            itensPendentesCliente.AddRange(agendamentosRestantesCliente
                .Select(h => new KeyValuePair<string, string>(h.ServicoEstabelecimento.Nome, h.Profissional.PessoaFisica.NomeOuApelido()))
                .ToList());

            itensPendentesCliente.AddRange(preVendasProdutoRestantesCliente
                .Select(pv => new KeyValuePair<string, string>(pv.EstabelecimentoProduto.Descricao, pv.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeOuApelido()))
                .ToList());

            return itensPendentesCliente;
        }

        public bool VerificarSeHaServicosNaoFinalizados(VerificaServicosNaoFinalizadosDTO verificaServicosNaoFinalizados)
        {
            var hoje = Calendario.Hoje();

            if (!verificaServicosNaoFinalizados.TemConfiguracaoServicosNaoFinalizadosHabilitada 
                || verificaServicosNaoFinalizados.IdClienteEstabelecimento == null 
                || verificaServicosNaoFinalizados.OrigemDoFechamento == null 
                || verificaServicosNaoFinalizados.Data == null
                || !verificaServicosNaoFinalizados.Data.DentroPeriodo(hoje.Date, hoje.Date.AddDays(1)))
                return false;

            var origensPermitidas = new int[] {
                (int)OrigemFechamentoDeContaEnum.ClientesEmAtendimento,
                (int)OrigemFechamentoDeContaEnum.Agenda
            };

            if (origensPermitidas.Contains((int)verificaServicosNaoFinalizados.OrigemDoFechamento) 
                && !verificaServicosNaoFinalizados.EscolheuProsseguirComFechamento)
            {
                return Domain.Pessoas.HorarioRepository.ExisteAgendamentoPendenteNaoFinalizadoNaData(
                    verificaServicosNaoFinalizados.IdEstabelecimento, 
                    hoje, 
                    (int)verificaServicosNaoFinalizados.IdClienteEstabelecimento
                );
            }

            return false;
        }
    }
}
