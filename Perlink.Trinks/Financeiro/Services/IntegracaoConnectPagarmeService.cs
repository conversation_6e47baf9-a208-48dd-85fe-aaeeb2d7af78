﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.ConciliacaoBelezinha.Dtos;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.DTO.Connect.Pagarme;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.Interfaces;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Trinks.Integracoes.Pagarme.Connect.V2;
using Trinks.Integracoes.Pagarme.Connect.V2.Dtos;
using Trinks.Integracoes.Pagarme.Connect.V2.Dtos.Requests;
using Trinks.Integracoes.Pagarme.Connect.V2.Dtos.Response;
using EstabelecimentoConfiguracaoPOS = Perlink.Trinks.Pessoas.EstabelecimentoConfiguracaoPOS;
using Perlink.Trinks.NotificacoesApps.Strategies;
using Perlink.Trinks.NotificacoesApps;
using Newtonsoft.Json;
using System.Net.Mail;
using System.Web;
using Perlink.Trinks.Belezinha.Enums;
using Perlink.Trinks.Belezinha;
using Perlink.Trinks.Pessoas.DTO;
using Elmah;
using System.Text;
using Trinks.Logging.V2;
using Trinks.Integracoes.Stone.Connect.Dtos;

namespace Perlink.Trinks.Financeiro.Services
{
    public class IntegracaoConnectPagarmeService : IIntegracaoConnectPagarmeService
    {
        public RetornoPagamentoPosDto RealizarPagamentoPos(RealizarPagamentoDto realizarPagamentoDto)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(realizarPagamentoDto.EstabelecimentoConfigPos.SecretKey);
            var pedidoDto = CriarPedidoDto(realizarPagamentoDto.TransacaoFicticia,
                        realizarPagamentoDto.EstabelecimentoConfigPos,
                        realizarPagamentoDto.TipoPagamento,
                        realizarPagamentoDto.NumerosDeParcelas,
                        realizarPagamentoDto.TipoParcelamento,
                        realizarPagamentoDto.ValorAPagar,
                        realizarPagamentoDto.ChaveDiariaTransacao,
                        realizarPagamentoDto.Terminal);

            Logger.TrackEvent("[ConnectPagarme] - Criar pedido", pedidoDto);
            var pedidoPagarme = connectPagarmeApi.OrderService.CreateOrder(pedidoDto);
            Logger.TrackEvent("[ConnectPagarme] - Pedido criado", pedidoPagarme);

            return CriarRetornoPagamentoPosDto(pedidoPagarme, realizarPagamentoDto.ChaveDiariaTransacao);
        }

        public bool CancelarOrdem(string secretKey, string codigoPreTransacao)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(secretKey);

            Logger.TrackEvent("[ConnectPagarme] - Cancelar pedido", codigoPreTransacao);
            var response = connectPagarmeApi.OrderService.CancelOrder(codigoPreTransacao);
            Logger.TrackEvent("[ConnectPagarme] - Pedido cancelado", response);

            return response.Success;
        }

        public OrderResponseDto ObterOrdem(string secretKey, string codigoTransacao)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(secretKey);

            Logger.TrackEvent("[ConnectPagarme] - Obter pedido", codigoTransacao);
            var response = connectPagarmeApi.OrderService.GetOrder(codigoTransacao);
            Logger.TrackEvent("[ConnectPagarme] - Pedido obtido", response);

            return response.Data;
        }

        public void TratarWebhook(WebhookConnectPagarmeDto webhook)
        {
            var transacaoAbertaPos = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(webhook.Data.Order.Id);

            if (transacaoAbertaPos == null)
            {
                CapturarCobrancaAvulsa(webhook);
                return;
            }

            var config =
              Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(
                  transacaoAbertaPos.Estabelecimento.IdEstabelecimento);
            var connectPagarmeApi = new ConnectPagarmeApi(config.SecretKey);

            Logger.TrackEvent("[ConnectPagarme] - Obter cobrança", webhook.Data.Id);
            var cobrancaPagarme = connectPagarmeApi.ChargeService.GetCharge(webhook.Data.Id);
            Logger.TrackEvent("[ConnectPagarme] - Cobrança obtida", cobrancaPagarme);

            var estabelecimentoRealizaSplit = transacaoAbertaPos.Estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaSplitPagamento;

            if (!cobrancaPagarme.Success)
            {
                throw new Exception("Transação não encontrada na Pagar.me");
            }

            var statusUltimaTransacao = cobrancaPagarme.Data.LastTransaction.Status;
            var status = cobrancaPagarme.Data.Status;

            switch (status)
            {
                case ChargeStatusEnum.Pending
                    when statusUltimaTransacao == LastTransactionStatus.AuthorizedPendingCapture:
                    if (!estabelecimentoRealizaSplit)
                    {
                        Logger.TrackEvent("[ConnectPagarme] - Estabelecimento não utiliza split de pagamentos");

                        Logger.TrackEvent("[ConnectPagarme] - Capturar cobrança", webhook.Data.Id);
                        var cobrancaFoiCapturada = connectPagarmeApi.ChargeService.CaptureCharge(cobrancaPagarme.Data.Id, null);
                        Logger.TrackEvent("[ConnectPagarme] - Cobrança capturada", webhook.Data.Id);

                        if (!cobrancaFoiCapturada.Success)
                            throw new Exception("Erro ao capturar cobrança");
                    }
                    AtualizarInformacoesDeTransacaoCapturadaPeloIdOrdem(cobrancaPagarme.Data, estabelecimentoRealizaSplit);
                    break;
                case ChargeStatusEnum.Canceled:
                    AtualizarInformacoesDeTransacaoCanceladaPeloIdOrdem(cobrancaPagarme.Data);
                    TratarTransacoesEstornadas(cobrancaPagarme.Data, webhook.Type);
                    break;
                case ChargeStatusEnum.Paid:
                    transacaoAbertaPos.FoiPago = true;
                    transacaoAbertaPos.ExternalTransactionId = cobrancaPagarme.Data.Id;
                    transacaoAbertaPos.StoneTransactionId = cobrancaPagarme.Data.GatewayId;
                    transacaoAbertaPos.EhSplit = cobrancaPagarme.Data.LastTransaction.Split != null;
                    transacaoAbertaPos.Serial = cobrancaPagarme.Data.LastTransaction.DeviceSerialNumber;
                    transacaoAbertaPos.FormaPagamentoPos = cobrancaPagarme.Data.LastTransaction.TransactionType == "debit_card" ? 2 : 1;
                    transacaoAbertaPos.NumeroParcelas = cobrancaPagarme.Data.LastTransaction.Installments;
                    transacaoAbertaPos.CodigoBandeiraCartao = cobrancaPagarme.Data.LastTransaction.Card.Brand;

                    Logger.TrackEvent("[ConnectPagarme] - Fechar ordem", webhook.Data.Order.Id);
                    var response = connectPagarmeApi.OrderService.CloseOrder(webhook.Data.Order.Id);
                    Logger.TrackEvent("[ConnectPagarme] - Ordem fechada", response);
                    break;
            }
        }

        private void CapturarCobrancaAvulsa(WebhookConnectPagarmeDto webhook)
        {
            var transacaoAvulsaWebhook = CriarTransacaoAvulsaInicial(webhook);

            try
            {
                var estabelecimentoTerminalPos = BuscarEstabelecimentoTerminalPos(webhook, transacaoAvulsaWebhook);
                if (estabelecimentoTerminalPos == null)
                    return;

                var cobrancaPagarme = ConsultarCobrancaNaPagarme(webhook, transacaoAvulsaWebhook, estabelecimentoTerminalPos);
                if (cobrancaPagarme == null)
                    return;

                ProcessarCapturaDeCobranca(cobrancaPagarme, transacaoAvulsaWebhook, estabelecimentoTerminalPos.EstabelecimentoConfigPos);
            }
            catch (Exception ex)
            {
                TratarExcecaoCapturarCobrancaAvulsa(ex, transacaoAvulsaWebhook, webhook);
            }
        }

        private TransacaoAvulsaPOSWebhookRequest CriarTransacaoAvulsaInicial(WebhookConnectPagarmeDto webhook)
        {
            var transacaoAvulsaWebhook = new TransacaoAvulsaPOSWebhookRequest()
            {
                OrderId = webhook.Data.Order.Id,
                ChargeId = webhook.Data.Id,
                Valor = webhook.Data.Amount,
                StatusDaCaptura = StatusDaCapturaEnum.Nenhum
            };

            Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.SaveNew(transacaoAvulsaWebhook);
            return transacaoAvulsaWebhook;
        }

        private EstabelecimentoTerminalPos BuscarEstabelecimentoTerminalPos(
            WebhookConnectPagarmeDto webhook,
            TransacaoAvulsaPOSWebhookRequest transacaoAvulsaWebhook)
        {
            var estabelecimentoTerminalPos = Domain.Belezinha.EstabelecimentoTerminalPosRepository
                .ObterEstabelecimentoTerminalPosPorSerialNumber(webhook.Data.LastTransaction.DeviceSerialNumber);

            if (estabelecimentoTerminalPos == null)
            {
                transacaoAvulsaWebhook.StatusDaCaptura = StatusDaCapturaEnum.TerminalNaoEncontrado;
                Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.Update(transacaoAvulsaWebhook);

                var errorInformation = new Exception("[ConnectPagarme] - Estabelecimento Terminal POS não encontrado");
                errorInformation.Data.Add("Webhook.OrderId", webhook.Data.Order.Id);
                errorInformation.Data.Add("Webhook.ChargeId", webhook.Data.Id);
                errorInformation.Data.Add("Webhook.SerialNumber", webhook.Data.LastTransaction.DeviceSerialNumber);
                errorInformation.Data.Add("Webhook.Amount", webhook.Data.Amount);

                Elmah.ErrorSignal.FromCurrentContext().Raise(errorInformation);
            }

            transacaoAvulsaWebhook.EstabelecimentoTerminalPos = estabelecimentoTerminalPos;
            Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.Update(transacaoAvulsaWebhook);

            return estabelecimentoTerminalPos;
        }

        private ResponseDto<ChargeResponseDto> ConsultarCobrancaNaPagarme(
            WebhookConnectPagarmeDto webhook,
            TransacaoAvulsaPOSWebhookRequest transacaoAvulsaWebhook,
            EstabelecimentoTerminalPos estabelecimentoTerminalPos)
        {
            var estabelecimentoConfiguracaoPos = estabelecimentoTerminalPos.EstabelecimentoConfigPos;
            var connectPagarmeApi = new ConnectPagarmeApi(estabelecimentoConfiguracaoPos.SecretKey);

            Logger.TrackEvent("[ConnectPagarme] - Obter cobrança", webhook.Data.Id);
            var cobrancaPagarme = connectPagarmeApi.ChargeService.GetCharge(webhook.Data.Id);
            Logger.TrackEvent("[ConnectPagarme] - Cobrança obtida", cobrancaPagarme);

            if (!cobrancaPagarme.Success)
            {
                transacaoAvulsaWebhook.StatusDaCaptura = StatusDaCapturaEnum.TransacaoNaoEncontradaNaPagarme;
                Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.Update(transacaoAvulsaWebhook);

                var error = new
                {
                    WebhookOrderId = webhook.Data.Order.Id,
                    WebhookChargeId = webhook.Data.Id,
                    WebhookSerialNumber = webhook.Data.LastTransaction.DeviceSerialNumber,
                    WebhookAmount = webhook.Data.Amount,
                    TrinksEstabelecimentoId = estabelecimentoConfiguracaoPos.Estabelecimento.IdEstabelecimento
                };

                Logger.TrackEvent("[ConnectPagarme] - Erro ao obter a cobrança", cobrancaPagarme);

                return null;
            }

            return cobrancaPagarme;
        }

        private void ProcessarCapturaDeCobranca(
            ResponseDto<ChargeResponseDto> cobrancaPagarme,
            TransacaoAvulsaPOSWebhookRequest transacaoAvulsaWebhook,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPos)
        {
            var statusUltimaTransacao = cobrancaPagarme.Data.LastTransaction.Status;
            var status = cobrancaPagarme.Data.Status;
            var splitDto = AdicionarSplitDoEstabelecimento(transacaoAvulsaWebhook.Valor, estabelecimentoConfiguracaoPos.IdRecebedor);
            var splitPagarMeDto = CriarSplitDePagamentoDto(splitDto);

            if (status == ChargeStatusEnum.Pending && statusUltimaTransacao == LastTransactionStatus.AuthorizedPendingCapture)
            {
                TentarCapturarCobranca(cobrancaPagarme.Data.Id, transacaoAvulsaWebhook, estabelecimentoConfiguracaoPos, splitPagarMeDto);
                return;
            }

            transacaoAvulsaWebhook.StatusDaCaptura = StatusDaCapturaEnum.StatusInvalidoParaCaptura;
            Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.Update(transacaoAvulsaWebhook);
        }

        private void TentarCapturarCobranca(
            string chargeId,
            TransacaoAvulsaPOSWebhookRequest transacaoAvulsaWebhook,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPos,
            SplitRequestDto split)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(estabelecimentoConfiguracaoPos.SecretKey);

            Logger.TrackEvent("[ConnectPagarme] - Capturar cobrança", chargeId);
            var resultado = connectPagarmeApi.ChargeService.CaptureCharge(chargeId, split);
            Logger.TrackEvent("[ConnectPagarme] - Capturar cobrança", resultado);

            if (resultado.Success)
            {
                transacaoAvulsaWebhook.StatusDaCaptura = StatusDaCapturaEnum.Capturada;
            }
            else
            {
                transacaoAvulsaWebhook.StatusDaCaptura = StatusDaCapturaEnum.FalhaAoCapturar;
                throw new Exception("Erro ao capturar cobrança");
            }

            Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.Update(transacaoAvulsaWebhook);
        }

        private SplitDTO AdicionarSplitDoEstabelecimento(decimal valorTotalDaTransacao, string idRecebedorDoEstabelecimento)
        {
            return new SplitDTO
            {
                Valor = (int)valorTotalDaTransacao,
                Regras = new List<RegraDeSplit>() 
                { new RegraDeSplit
                    {
                        Valor = (int)valorTotalDaTransacao,
                        IdRecebedor = idRecebedorDoEstabelecimento,
                        Tipo = "flat",
                        Opcoes = new OpcoesDeSplit
                        {
                            CobrarTaxaDeProcessamento = true,
                            RecebeORestanteDaDivisao = true,
                            Responsavel = true
                        }
                    }
                }
            };
        }

        private void TratarExcecaoCapturarCobrancaAvulsa(
            Exception ex,
            TransacaoAvulsaPOSWebhookRequest transacaoAvulsaWebhook,
            WebhookConnectPagarmeDto webhook)
        {
            transacaoAvulsaWebhook.StatusDaCaptura = StatusDaCapturaEnum.ErroDesconhecido;
            Domain.Belezinha.TransacaoAvulsaPOSWebhookRequestRepository.Update(transacaoAvulsaWebhook);

            var errorInformation = new Exception("[ConnectPagarme] - Erro ao processar captura avulsa", ex);
            errorInformation.Data.Add("Webhook.ChargeId", webhook.Data.Id);
            errorInformation.Data.Add("Webhook.OrderId", webhook.Data.Order.Id);

            Elmah.ErrorSignal.FromCurrentContext().Raise(errorInformation);
            throw ex;
        }

        public bool CapturarTransacaoComSplit(int idEstabelecimento, SplitDTO split, string secretKey)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(secretKey);
            var splitPagarMeDto = CriarSplitDePagamentoDto(split);

            Logger.TrackEvent("[ConnectPagarme] - Obter pedido", split.IdExternoDaTransacao);
            var ordem = connectPagarmeApi.OrderService.GetOrder(split.IdExternoDaTransacao);
            Logger.TrackEvent("[ConnectPagarme] - Pedido obtido", split.IdExternoDaTransacao);

            if (!ordem.Success)
                throw new Exception("Erro ao obter ordem");

            var cobranca = ordem.Data.Charges.FirstOrDefault(c => c.Status == ChargeStatusEnum.Pending);
            if (cobranca == null || cobranca.LastTransaction.Status != LastTransactionStatus.AuthorizedPendingCapture)
                return false;

            Logger.TrackEvent("[ConnectPagarme] - Capturar cobrança", cobranca.Id);
            Logger.TrackEvent("[ConnectPagarme] - Capturar cobrança", splitPagarMeDto);
            var cobrancaFoiCapturada = connectPagarmeApi.ChargeService.CaptureCharge(cobranca.Id, splitPagarMeDto);
            Logger.TrackEvent("[ConnectPagarme] - Cobrança capturada ", cobrancaFoiCapturada);

            if (!cobrancaFoiCapturada.Success)
                throw new Exception("Erro ao capturar cobrança");

            return true;
        }

        public string RealizarCadastroRecebedor(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(dadosParaCadastrarRecebedorDto.SecretKey);

            var recipientDto = ToCreateRecipientDto(dadosParaCadastrarRecebedorDto);

            Logger.TrackEvent("[ConnectPagarme] - Criar recebedor", recipientDto);
            var response = connectPagarmeApi.RecipientService.CreateRecipient(recipientDto);
            Logger.TrackEvent("[ConnectPagarme] - Recebedor criado", response);

            if (!response.Success)
            {
                var sb = new StringBuilder();

                sb.Append("Pagarme message: ");
                sb.AppendLine(response.Message);

                var e = new Exception("[ConnectPagarme] Falha ao criar recebedor")
                {
                    Source = sb.ToString()
                };

                ErrorSignal.FromContext(HttpContext.Current).Raise(e);
                return string.Empty;
            }

            return response.Data.Id;
        }

        public RetornoSaldoRecebedorDto ObterSaldoRecebedor(string secretKey, string idRecebedor)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(secretKey);

            Logger.TrackEvent("[ConnectPagarme] - Obter saldo do recebedor", idRecebedor);
            var resultado = connectPagarmeApi.RecipientService.GetBalanceRecipient(idRecebedor);
            Logger.TrackEvent("[ConnectPagarme] - Saldo do recebedor obtido", resultado);

            return CriarRetornoSaldoRecebedorDto(resultado);
        }
        public RetornoListaDeRecebedoresDto ObterListaDeRecebedores(string secretKey, int page, int size)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(secretKey);

            Logger.TrackEvent("[ConnectPagarme] - Obter lista de recebedores", new { page, size });
            var listaRecebedores = connectPagarmeApi.RecipientService.GetRecipients(page, size);
            Logger.TrackEvent("[ConnectPagarme] - Lista de recebedores obtidas", listaRecebedores);

            return CriarRetornoListaDeRecebedoresDto(listaRecebedores);
        }

        public List<PayableResponseDto> ConsultarPagamentosAsync(EstabelecimentoConfiguracaoPOS configuracaoPos, DateTime dataDasTransacoes)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(configuracaoPos.SecretKey);
            var pagina = 1;
            var pagamentos = new List<PayableResponseDto>();
            while (true)
            {
                if (pagamentos.Count >= 10000)
                    break;

                var payablesRequestDto = new PayablesRequestDto()
                {
                    RecipientId = configuracaoPos.IdRecebedor,
                    PaymentDateSince = dataDasTransacoes,
                    PaymentDateUntil = dataDasTransacoes,
                    Statuses = new List<PayableStatusEnum> { PayableStatusEnum.Paid },
                    ItensPerPage = 1000,
                    Page = pagina
                };

                Logger.TrackEvent("[ConnectPagarme] - Obter pagamentos", payablesRequestDto);
                var retorno = connectPagarmeApi.PayablesService.GetPayables(payablesRequestDto);
                Logger.TrackEvent("[ConnectPagarme] - Pagamentos obtidos", retorno);

                if (retorno == null || retorno.Data.Count == 0)
                    break;

                pagamentos.AddRange(retorno.Data);
                pagina++;
            }
            return pagamentos;
        }

        public List<ReceivableResponseDto> ConsultarPagamentosV5Async(EstabelecimentoConfiguracaoPOS configuracaoPos, DateTime dataDasTransacoes)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(configuracaoPos.SecretKey);
            var pagina = 1;
            var pagamentos = new List<ReceivableResponseDto>();
            while (true)
            {
                if (pagamentos.Count >= 10000)
                    break;

                var payablesRequestDto = new PayablesRequestDto()
                {
                    RecipientId = configuracaoPos.IdRecebedor,
                    PaymentDateSince = dataDasTransacoes,
                    PaymentDateUntil = dataDasTransacoes,
                    Statuses = new List<PayableStatusEnum> {
                            PayableStatusEnum.Paid,
                            PayableStatusEnum.WaitingFunds,
                            PayableStatusEnum.Prepaid
                        },
                    ItensPerPage = 1000,
                    Page = pagina
                };

                Logger.TrackEvent("[ConnectPagarme] - Obter pagamentos V5", payablesRequestDto);
                var retorno = connectPagarmeApi.PayablesService.GetPayablesV5(payablesRequestDto);
                Logger.TrackEvent("[ConnectPagarme] - Pagmentos obtidos V5", retorno);

                if (retorno.Data == null || retorno.Data.Count == 0)
                    break;

                pagamentos.AddRange(retorno.Data);
                pagina++;
            }
            return pagamentos;
        }

        public List<TransactionResponseDto> ConsultarTransacoesAsync(EstabelecimentoConfiguracaoPOS configuracaoPos, DateTime dataDasTransacoes)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(configuracaoPos.SecretKey);
            var pagina = 1;
            var transacoes = new List<TransactionResponseDto>();
            while (true)
            {
                if (transacoes.Count >= 10000)
                    break;

                var transactionsRequestDto = new TransactionsRequestDto()
                {
                    TransactionDateSince = dataDasTransacoes,
                    TransactionDateUntil = dataDasTransacoes,
                    Statuses = new List<TransactionStatusEnum> {
                            TransactionStatusEnum.Paid,
                            TransactionStatusEnum.WaitingPayment,
                            TransactionStatusEnum.Processing,
                            TransactionStatusEnum.Authorized,
                        },
                    ItensPerPage = 1000,
                    Page = pagina
                };

                Logger.TrackEvent("[ConnectPagarme] - Obter transações", transactionsRequestDto);
                var retorno = connectPagarmeApi.TransactionService.GetTransactions(transactionsRequestDto);
                Logger.TrackEvent("[ConnectPagarme] - Transações obtidas", retorno);

                if (retorno == null || retorno.Data.Count == 0)
                    break;

                transacoes.AddRange(retorno.Data);
                pagina++;
            }
            return transacoes;
        }

        public List<TaxasTransacaoRecebedorDto> ObterTaxasDaTransacaoPorRecebedor(EstabelecimentoConfiguracaoPOS configuracaoPos, string chargeId)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(configuracaoPos.SecretKey);
            var payablesRequestDto = new PayablesRequestDto()
            {
                ChargeId = chargeId,
            };

            Logger.TrackEvent("[ConnectPagarme] - Obter pagamentos", payablesRequestDto);
            var retorno = connectPagarmeApi.PayablesService.GetPayablesV5(payablesRequestDto);
            Logger.TrackEvent("[ConnectPagarme] - Pagamentos obtidos", payablesRequestDto);

            if (!retorno.Success)
                throw new Exception(retorno.Message);

            var pagamentos = retorno.Data;

            return pagamentos.Select(p => new TaxasTransacaoRecebedorDto
            {
                IdRecebedor = p.RecipientId,
                ValorTaxas = -ObterValorDeTaxasPagasPeloRecebedor(p),
                ValorLiquido = (p.Amount / 100m) - ObterValorDeTaxasPagasPeloRecebedor(p)
            }).ToList();
        }

        public void FecharPedido(string secretKey, string orderId)
        {
            var connectPagarmeApi = new ConnectPagarmeApi(secretKey);

            Logger.TrackEvent("[ConnectPagarme] - Fechar pedido", orderId);
            connectPagarmeApi.OrderService.CloseOrder(orderId);
            Logger.TrackEvent("[ConnectPagarme] - Pedido fechado", orderId);
        }

        #region Métodos Privados
        private decimal ObterValorDeTaxasPagasPeloRecebedor(ReceivableResponseDto pagamento)
        {
            if (ObterTipoDaMovimentacao(pagamento.Type.ToString()) == TipoDeMovimentacaoEnum.Refund)
                return pagamento.Amount == 0
                    ? 0
                    : (pagamento.Amount - pagamento.Fee) / 100m;

            return pagamento.Amount == 0
                ? 0
                : (pagamento.AnticipationFee + pagamento.FraudCoverageFee + pagamento.Fee) / 100m;
        }

        private static TipoDeMovimentacaoEnum ObterTipoDaMovimentacao(string tipo)
        {
            return Enum.TryParse<TipoDeMovimentacaoEnum>(tipo, out var resultado) ? resultado : TipoDeMovimentacaoEnum.Others;
        }

        private List<TransacaoPosDto> ProcessarRetornoDaConsultaDeTransacoes(List<PayableResponseDto> payables)
        {
            return payables.Select(transaction =>
                new TransacaoPosDto()
                {
                    Id = transaction.Id.ToString(),
                    Modalidade = ObterModalidade(transaction.PaymentMethod),
                    Data = transaction.DateCreated,
                    DataPagamento = transaction.PaymentDate,
                    //Bandeira = transaction.Key.CardBrand,
                    //Parcela = transaction.Key.ActualInstallment,
                    TotalParcelas = transaction.Installment,
                    ValorTotal = transaction.Amount,
                    ValorTotalLiquido = transaction.Amount - transaction.Fee + transaction.AnticipationFee + transaction.FraudCoverageFee,
                    DescontoOperadora = transaction.Fee + transaction.AnticipationFee + transaction.FraudCoverageFee,
                    //ValorDoSplit = valorDoSplit,
                    ValorReceber = transaction.Amount - transaction.Fee + transaction.AnticipationFee + transaction.FraudCoverageFee
                }).ToList();
        }

        private string ObterModalidade(PaymentMethodEnum paymentMethod)
        {
            switch (paymentMethod)
            {
                case PaymentMethodEnum.CreditCard:
                    return "Crédito";
                case PaymentMethodEnum.DebitCard:
                    return "Débito";
                case PaymentMethodEnum.Voucher:
                    return "Voucher";
                default:
                    return "Desconhecida";
            }
        }

        private RetornoPagamentoPosDto CriarRetornoPagamentoPosDto(ResponseDto<OrderResponseDto> responseDto,
            int chaveDiariaTransacao)
        {
            if (responseDto.Success)
            {
                return new RetornoPagamentoPosDto()
                {
                    Sucesso = true,
                    IdTransacao = responseDto.Data.Id,
                    NumeroDaComanda = chaveDiariaTransacao
                };
            }
            return new RetornoPagamentoPosDto()
            {
                Sucesso = false,
                MensagemDeErro= string.Empty
            };
        }

        private CreateOrderDto CriarPedidoDto(Transacao transacao,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfigPOS,
            TipoPagamentoEnum tipoPagamento,
            int? numParcelas,
            TipoParcelamentoEnum? tipoDeParcelamento, decimal valorAPagar, int identificador,
            string terminal)
        {
            return new CreateOrderDto()
            {
                Closed = false,
                Customer = new CustomerRequestDto()
                {
                    Name = transacao.PessoaQuePagou.PrimeiroNome(),
                    Email = transacao.PessoaQuePagou.Email
                },
                Items = CriarItemsDto(transacao, valorAPagar),
                PoiPaymentSettings = CriarPoiPaymentSettingsDto(tipoPagamento, numParcelas, tipoDeParcelamento, identificador, terminal)
            };
        }

        private PoiPaymentSettingsRequestDto CriarPoiPaymentSettingsDto(
            TipoPagamentoEnum tipoPagamento,
            int? numParcelas,
            TipoParcelamentoEnum? tipoDeParcelamento, int identificador, string terminal)
        {
            return new PoiPaymentSettingsRequestDto()
            {
                Visible = true,
                DisplayName = "Fechamento " + identificador,
                PaymentSetup = new PaymentSetupRequestDto()
                {
                    Type = ObterPaymentSetupType(tipoPagamento),
                    Installments = (numParcelas ?? 1) == 0 ? 1 : numParcelas.Value,
                    InstallmentType = tipoDeParcelamento.HasValue && tipoDeParcelamento.Value == TipoParcelamentoEnum.SemJuros ? "merchant" : "issuer",
                },
                PrintOrderReceipt = false,
                DevicesSerialNumber = new List<string>()
                {
                    terminal
                }
            };
        }
        private static SplitRequestDto CriarSplitDePagamentoDto(SplitDTO split)
        {
            return new SplitRequestDto
            {
                Amount = split.Valor,
                Split = split.Regras.Select(r => new SplitRuleRequestDto
                {
                    Amount = r.Valor,
                    RecipientId = r.IdRecebedor,
                    Type = r.Tipo,
                    Options = new OptionsRequestDto()
                    {
                        ChargeProcessingFee = r.Opcoes.CobrarTaxaDeProcessamento,
                        ChargeRemainderFee = r.Opcoes.RecebeORestanteDaDivisao,
                        Liable = r.Opcoes.Responsavel
                    }
                }).ToList()
            };
        }
        private string ObterPaymentSetupType(TipoPagamentoEnum tipoPagamento)
        {
            switch (tipoPagamento)
            {
                case TipoPagamentoEnum.Credito:
                    return "credit";
                case TipoPagamentoEnum.Debito:
                    return "debit";
                case TipoPagamentoEnum.Voucher:
                    return "voucher";
            }

            throw new NotImplementedException("Tipo de pagamento não implementado no Connect Stone V2");
        }

        private List<ItemRequestDto> CriarItemsDto(Transacao transacao, decimal valorAPagar)
        {
            return new List<ItemRequestDto>()
            {
                new ItemRequestDto()
                {
                    Amount = (int)(valorAPagar * 100),
                    Description = "Item 1",
                    Quantity = 1
                }
            };
        }

        private void AtualizarInformacoesDeTransacaoCapturadaPeloIdOrdem(ChargeResponseDto chargeResponse, bool estabelecimentoRealizaSplit)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            transacaoPosWebhookRequest.FoiPago = true;
            transacaoPosWebhookRequest.ExternalTransactionId = chargeResponse.Id;
            transacaoPosWebhookRequest.CodigoBandeiraCartao = chargeResponse.LastTransaction.Card.Brand;
            transacaoPosWebhookRequest.FormaPagamentoPos = chargeResponse.LastTransaction.TransactionType == "debit_card" ? 2 : 1;
            transacaoPosWebhookRequest.EhSplit = estabelecimentoRealizaSplit;
            transacaoPosWebhookRequest.StatusPreTransacao = "processed";
            transacaoPosWebhookRequest.StoneTransactionId = chargeResponse.GatewayId;
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        private void AtualizarInformacoesDeTransacaoCanceladaPeloIdOrdem(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            transacaoPosWebhookRequest.OperacaoCancelada = true;
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        private CreateRecipientDto ToCreateRecipientDto(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto)
        {
            return new CreateRecipientDto()
            {
                Code = string.Concat("EP_", dadosParaCadastrarRecebedorDto.IdEstabelecimentoProfissional.ToString(), Guid.NewGuid().ToString()),
                RegisterInformation = ToRegisterInformationDto(dadosParaCadastrarRecebedorDto),
                DefaultBankAccount = ToDefaultBankAccountDto(dadosParaCadastrarRecebedorDto),
                TransferSettings = CriarTransferenciaDto()
            };
        }

        private RegisterInformationRequestDto ToRegisterInformationDto(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto)
        {
            var informacoesDoRegistro = new RegisterInformationRequestDto
            {
                Address = new AddressRequestDto
                {
                    Street = dadosParaCadastrarRecebedorDto.TipoLogradouro + " " + dadosParaCadastrarRecebedorDto.Logradouro,
                    StreetNumber = dadosParaCadastrarRecebedorDto.Numero,
                    Complementary = !string.IsNullOrEmpty(dadosParaCadastrarRecebedorDto.Complemento) ?
                                    dadosParaCadastrarRecebedorDto.Complemento : "Nenhum",
                    Neighborhood = dadosParaCadastrarRecebedorDto.Bairro,
                    City = dadosParaCadastrarRecebedorDto.Cidade,
                    State = dadosParaCadastrarRecebedorDto.Estado,
                    ZipCode = dadosParaCadastrarRecebedorDto.Cep,
                    ReferencePoint = !string.IsNullOrEmpty(dadosParaCadastrarRecebedorDto.PontoReferencia) ?
                     dadosParaCadastrarRecebedorDto.PontoReferencia : "Nenhum"
                },
                MainAddress = new AddressRequestDto
                {
                    Street = dadosParaCadastrarRecebedorDto.TipoLogradouro + " " + dadosParaCadastrarRecebedorDto.Logradouro,
                    StreetNumber = dadosParaCadastrarRecebedorDto.Numero,
                    Complementary = !string.IsNullOrEmpty(dadosParaCadastrarRecebedorDto.Complemento) ?
                                    dadosParaCadastrarRecebedorDto.Complemento : "Nenhum",
                    Neighborhood = dadosParaCadastrarRecebedorDto.Bairro,
                    City = dadosParaCadastrarRecebedorDto.Cidade,
                    State = dadosParaCadastrarRecebedorDto.Estado,
                    ZipCode = dadosParaCadastrarRecebedorDto.Cep,
                    ReferencePoint = !string.IsNullOrEmpty(dadosParaCadastrarRecebedorDto.PontoReferencia) ?
                     dadosParaCadastrarRecebedorDto.PontoReferencia : "Nenhum"
                },
                Name = dadosParaCadastrarRecebedorDto.Nome,
                Document = dadosParaCadastrarRecebedorDto.Documento,
                Type = dadosParaCadastrarRecebedorDto.TipoPessoa == TipoPessoaEnum.Fisica ? "individual" : "corporation",
                Email = dadosParaCadastrarRecebedorDto.TipoPessoa == TipoPessoaEnum.Fisica ?
                dadosParaCadastrarRecebedorDto.EmailPF : dadosParaCadastrarRecebedorDto.EmailPJ,

            };

            if (dadosParaCadastrarRecebedorDto.TipoPessoa == TipoPessoaEnum.Juridica)
            {
                informacoesDoRegistro.Email = dadosParaCadastrarRecebedorDto.EmailPJ;
                informacoesDoRegistro.CompanyName = dadosParaCadastrarRecebedorDto.RazaoSocial;
                informacoesDoRegistro.TradingName = dadosParaCadastrarRecebedorDto.NomeFantasia;
                informacoesDoRegistro.AnnualRevenue = dadosParaCadastrarRecebedorDto.RendaAnual;
                informacoesDoRegistro.PhoneNumbers = CriarTelefone(dadosParaCadastrarRecebedorDto.TelefonePJ);

                // Adiciona os sócios quando for pessoa jurídica
                if (dadosParaCadastrarRecebedorDto.ManagingPartners != null && dadosParaCadastrarRecebedorDto.ManagingPartners.Count > 0)
                {
                    informacoesDoRegistro.ManagingPartners = new List<ManagingPartnerRequestDto>();

                    foreach (var socio in dadosParaCadastrarRecebedorDto.ManagingPartners)
                    {
                        var managingPartner = new ManagingPartnerRequestDto
                        {
                            Name = socio.Nome,
                            Email = socio.Email,
                            Document = socio.Documento,
                            Type = "individual",
                            Birthdate = socio.DataNascimento,
                            MonthlyIncome = socio.RendaMensal,
                            ProfessionalOccupation = socio.OcupacaoProfissional,
                            PhoneNumbers = CriarTelefone(socio.Telefone),
                            Address = new AddressRequestDto
                            {
                                Street = informacoesDoRegistro.Address.Street,
                                StreetNumber = informacoesDoRegistro.Address.StreetNumber,
                                Complementary = informacoesDoRegistro.Address.Complementary,
                                Neighborhood = informacoesDoRegistro.Address.Neighborhood,
                                City = informacoesDoRegistro.Address.City,
                                State = informacoesDoRegistro.Address.State,
                                ZipCode = informacoesDoRegistro.Address.ZipCode,
                                ReferencePoint = informacoesDoRegistro.Address.ReferencePoint,
                            },
                            SelfDeclaredLegalRepresentative = socio.EhRepresentanteLegal
                        };

                        informacoesDoRegistro.ManagingPartners.Add(managingPartner);
                    }
                }

                return informacoesDoRegistro;
            }

            informacoesDoRegistro.Email = dadosParaCadastrarRecebedorDto.EmailPF;
            informacoesDoRegistro.Birthdate = dadosParaCadastrarRecebedorDto.DataNascimento.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            informacoesDoRegistro.MonthlyIncome = dadosParaCadastrarRecebedorDto.RendaMensal;
            informacoesDoRegistro.ProfessionalOcupation = dadosParaCadastrarRecebedorDto.OcupacaoProfissional;
            informacoesDoRegistro.PhoneNumbers = CriarTelefone(dadosParaCadastrarRecebedorDto.TelefonePF);

            // Pessoa física não tem sócios
            informacoesDoRegistro.ManagingPartners = null;

            return informacoesDoRegistro;
        }

        private List<PhoneRequestDto> CriarTelefone(string telefone)
        {
            return new List<PhoneRequestDto>
           {
               new PhoneRequestDto
               {
                   Type = "mobile",
                   DDD = telefone.Substring(1, 2),
                   Number = telefone.Substring(5, 5) + telefone.Substring(11, 4)
               }
           };
        }

        private DefaultBankAccountRequestDto ToDefaultBankAccountDto(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto)
        {
            return new DefaultBankAccountRequestDto
            {
                HolderName = dadosParaCadastrarRecebedorDto.NomeDoTitularDaContaBancaria,
                HolderType = dadosParaCadastrarRecebedorDto.TipoPessoa == TipoPessoaEnum.Juridica ? "company" : "individual",
                HolderDocument = dadosParaCadastrarRecebedorDto.DocumentoDaContaBancaria,
                Bank = dadosParaCadastrarRecebedorDto.CodigoDoBanco,
                BranchNumber = dadosParaCadastrarRecebedorDto.Agencia,
                BranchCheckDigit = dadosParaCadastrarRecebedorDto.DigitoVerificadorAgencia,
                AccountNumber = dadosParaCadastrarRecebedorDto.NumeroConta,
                AccountCheckDigit = dadosParaCadastrarRecebedorDto.DigitoVerificadorConta,
                Type = dadosParaCadastrarRecebedorDto.IdTipoConta == (int)TipoDeContaBancariaEnum.Corrente ? "checking" : "savings"
            };
        }

        private TransferSettingsRequestDto CriarTransferenciaDto()
        {
            return new TransferSettingsRequestDto
            {
                TransferInterval = "daily",
                TransferDay = 0,
                TransferEnabled = "true"
            };
        }

        private RetornoListaDeRecebedoresDto CriarRetornoListaDeRecebedoresDto(ResponseDto<RecipientsListResponseDto> responseDto)
        {
            if (responseDto.Success)
            {
                var recebedores = new List<RecebedorDto>();
                foreach (var recebedor in responseDto.Data.Data)
                {
                    recebedores.Add(new RecebedorDto
                    {
                        IdRecebedor = recebedor.Id,
                        Documento = recebedor.Document
                    });
                };

                return new RetornoListaDeRecebedoresDto()
                {
                    Sucesso = true,
                    Recebedores = recebedores,
                };
            }
            return new RetornoListaDeRecebedoresDto()
            {
                Sucesso = false,
                MensagemDeErro = responseDto.Message
            };
        }

        private RetornoSaldoRecebedorDto CriarRetornoSaldoRecebedorDto(ResponseDto<BalanceResponseDto> responseDto)
        {
            if (responseDto.Success)
            {
                return new RetornoSaldoRecebedorDto()
                {
                    Sucesso = true,
                    TotalAReceber = responseDto.Data.WaitingFundsAmount,
                    TotalATransferir = responseDto.Data.AvailableAmount,
                    TotalTransferido = responseDto.Data.TransferredAmount
                };
            }
            return new RetornoSaldoRecebedorDto()
            {
                Sucesso = false,
                MensagemDeErro = responseDto.Message
            };
        }

        private void TratarTransacoesEstornadas(ChargeResponseDto chargeResponse, string webhookType)
        {
            if (webhookType == "charge.refunded")
            {
                AtualizarInformacoesDeTransacaoEstornadaPeloIdOrdem(chargeResponse);
                GerarNotificacoesDeCancelamentoNaSubadquirente(chargeResponse);
            }
        }

        private void AtualizarInformacoesDeTransacaoEstornadaPeloIdOrdem(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            transacaoPosWebhookRequest.PagamentoEstornado = true;
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        private void GerarNotificacoesDeCancelamentoNaSubadquirente(ChargeResponseDto chargeResponse)
        {
            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(chargeResponse.Order.Id);

            EnviarEmailAvisandoEstabelecimentoDoCancelamentoDaTransacao(transacaoPosWebhookRequest);
            NotificarEstabelecimentoDoCancelamentoNaAgenda(transacaoPosWebhookRequest);
            NotificarEstabelecimentoDoCancelamentoNoAppPro(transacaoPosWebhookRequest);
        }

        private void EnviarEmailAvisandoEstabelecimentoDoCancelamentoDaTransacao(TransacaoPosWebhookRequest transacaoPosWebhookRequest)
        {
            try
            {
                var assunto = $"Cancelamento de Transação na Maquininha de Cartão - Stone";
                var remetente = Pessoas.Services.EnvioEmailService.ObterRemetentePadrao();
                var pathQuery = HttpContext.Current.Request.Url.PathAndQuery;
                var url = HttpContext.Current.Request.Url.AbsoluteUri.Replace(pathQuery, "/");
                var paragrafos = new List<string>
                {
                    $"<b>Prezado(a) {transacaoPosWebhookRequest.Estabelecimento.NomeDeExibicaoNoPortal}</b>",
                    $"Informamos que o valor referente à transação {transacaoPosWebhookRequest.CodigoTransacao} realizada em {transacaoPosWebhookRequest.DataOperacao?.ToString("dd/MM/yyyy")} na maquininha de cartão foi cancelado pela Stone.",
                    $"Para mais informações, entre em contato com a Stone através de um dos seus canais de atendimento.",
                    $"Capitais e regiões metropolitanas: 3004 9680<br />Demais regiões: 0800 326 0506<br />Email: <EMAIL>",
                    $"Atenciosamente,"
                };

                var corpoEmail = string.Join("<br /><br />", paragrafos);

                Domain.Pessoas.EnvioEmailService.DispararEmail(assunto, corpoEmail, remetente, new MailAddress(transacaoPosWebhookRequest.Estabelecimento.EmailEstabelecimento()), null);
            }
            catch (Exception ex)
            {
                return;
            }
        }

        private void NotificarEstabelecimentoDoCancelamentoNoAppPro(TransacaoPosWebhookRequest transacaoPosWebhookRequest)
        {
            TipoDeNotificacao tipo = Domain.NotificacoesApps.TipoDeNotificacaoRepository
                .Queryable().FirstOrDefault(f => f.Id == (int)TipoDeMensagemDeNotificacaoEnum.AgendamentoCanceladoQueNaoSejaPeloProprioProfissional && f.Ativo == true);

            CanalDaNotificacao canal = Domain.NotificacoesApps.CanalDaNotificacaoRepository.Queryable()
                .FirstOrDefault(f => f.Ativo == true && f.Id == (int)CanalDaNotificacaoEnum.Sininho);

            var transacaoPos = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorCodigoAutorizacaoTransacaoPdv(transacaoPosWebhookRequest.CodigoTransacao);
            var texto = $"A transação {transacaoPosWebhookRequest.CodigoTransacao} realizada em {transacaoPos.Data.ToString("dd/MM/yyyy")} na maquininha de cartão foi cancelada pela Stone. Toque e confira detalhes.";
            var horario = transacaoPos.Transacao.HorariosTransacoes.FirstOrDefault(ht => ht.Horario.Id > 0).Horario;
            var conta = transacaoPosWebhookRequest.Estabelecimento.ObterResponsavel().Contas.FirstOrDefault();
            var _strategy = new NotificacaoAgendamentoCanceladoQueNaoSejaPeloProprioProfissionalStrategy();
            var parametros = _strategy.CarregarParametros(horario, canal);
            var textosDaNotificacao = _strategy.CarregarModeloDeTextos(horario, canal);
            textosDaNotificacao["Titulo"] = texto;

            EventoDeNotificacao eventoDeNotificacao = new EventoDeNotificacao
            {
                IdEstabelecimento = horario.Estabelecimento.IdEstabelecimento,
                DataHoraProgramado = Calendario.Agora(),
                Tipo = tipo,
                JaFoiLida = false,
                Parametros = JsonConvert.SerializeObject(parametros),
                QuantidadeDeEnviosRealizados = 0
            };

            eventoDeNotificacao.IdPessoa = conta.Pessoa.IdPessoa;
            Domain.NotificacoesApps.CanalDaNotificaoAppProService.ProgramarEnvioDeNotificacaoPorEsseCanal(eventoDeNotificacao, canal, textosDaNotificacao);
        }

        private void NotificarEstabelecimentoDoCancelamentoNaAgenda(TransacaoPosWebhookRequest transacaoPosWebhookRequest)
        {
            var transacaoPos = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorCodigoAutorizacaoTransacaoPdv(transacaoPosWebhookRequest.CodigoTransacao);
            var texto = $"A transação {transacaoPosWebhookRequest.CodigoTransacao} realizada em {transacaoPos.Data.ToString("dd/MM/yyyy")} na maquininha de cartão foi cancelada pela Stone. Toque e confira detalhes.";
            var horario = transacaoPos.Transacao.HorariosTransacoes.FirstOrDefault(ht => ht.Horario.Id > 0).Horario;
            var conta = transacaoPosWebhookRequest.Estabelecimento.ObterResponsavel().Contas.FirstOrDefault();

            var notificacaoEstabelecimento = new NotificacaoEstabelecimento
            {
                Estabelecimento = transacaoPosWebhookRequest.Estabelecimento,
                DataHoraReferencia = DateTime.Now,
                DataHoraRegistroNotificacao = DateTime.Now,
                Visualizada = false,
                Texto = texto,
                TipoNotificacao = 6,
                Conta = conta,
                Horario = horario
            };

            Domain.Pessoas.NotificacaoEstabelecimentoRepository.SaveNew(notificacaoEstabelecimento);
        }
        #endregion
    }
}