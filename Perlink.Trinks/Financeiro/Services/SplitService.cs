﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Despesas;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Services
{

    public class SplitService : BaseService, ISplitService
    {
        /// <summary>
        /// Criar o tipo de lançamento para o Split no estabelecimento
        /// </summary>
        /// <param name="idEstabelecimento"></param>
        public void GerarRegistroDeSplitDePagamento(int idEstabelecimento)
        {
            var categoriaPadraoSplit = Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit);
            var lancamentoCategoriaPadraoSplit = Domain.Despesas.LancamentoCategoriaPadraoRepository.Load((int)categoriaPadraoSplit);
            var lancamentoCategoriaSplitPagamento =
                    Domain.Despesas.LancamentoCategoriaRepository.Queryable()
                        .FirstOrDefault(p => p.LancamentoCategoriaPadrao.IdLancamentoCategoria == lancamentoCategoriaPadraoSplit.IdLancamentoCategoria);

            if (lancamentoCategoriaSplitPagamento != null)
            {
                lancamentoCategoriaSplitPagamento.Ativo = true;
                Domain.Despesas.LancamentoCategoriaRepository.Update(lancamentoCategoriaSplitPagamento);
            }
            else
            {
                lancamentoCategoriaSplitPagamento = new LancamentoCategoria
                {
                    Ativo = true,
                    Estabelecimento = new Estabelecimento { IdEstabelecimento = idEstabelecimento },
                    LancamentoTipo = new LancamentoTipo { IdLancamentoTipo = 2 },
                    Nome = lancamentoCategoriaPadraoSplit.Nome,
                    LancamentoCategoriaPadrao = lancamentoCategoriaPadraoSplit,
                    LancamentoGrupo = Domain.Despesas.LancamentoGrupoService.ObterOuCriarLancamentoGrupoDoEstabelecimentoPorGrupoPadrao(idEstabelecimento, lancamentoCategoriaPadraoSplit.LancamentoGrupoPadrao.IdLancamentoGrupoPadrao),
                    NivelMinimoAcesso = 3,
                    NecessarioInformarFornecedor = false,
                    NecessarioInformarProfissional = true,
                    PermiteLancamentoManual = false,
                    PermiteEdicao = false,
                    PermiteExclusao = false
                };

                Domain.Despesas.LancamentoCategoriaRepository.SaveNew(lancamentoCategoriaSplitPagamento);
            }
        }

        public void GerarRegistroDeSplitDePagamentoSeNecessario(Transacao transacao)
        {
            if (transacao.HorariosTransacoes != null)
                foreach (var ht in transacao.HorariosTransacoes)
                {
                    GerarRegistroDeSplitDePagamentoSeNecessario(ht);
                }
        }

        public void GerarRegistroDeSplitDePagamentoSeNecessario(HorarioTransacao horarioTransacao)
        {
            var existeFormaDepagamentoPOSNaTransacao = horarioTransacao.Transacao.FormasPagamento.Any(p => p.FormaPagamento.TipoPOS != null && p.ValorPago > 0);

            if (existeFormaDepagamentoPOSNaTransacao && horarioTransacao.Transacao.FormasPagamento.Count <= 1)
            {
                var profissionalEstabelecimentoEstaUtilizandoSplitPagamento =
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(horarioTransacao.Horario.Profissional.IdProfissional, horarioTransacao.Horario.Estabelecimento.IdEstabelecimento);

                if (profissionalEstabelecimentoEstaUtilizandoSplitPagamento)
                {
                    horarioTransacao.Comissao.EhComissaoComSplit = true;
                    GerarLancamentoSplitPagamento(horarioTransacao, false);
                }

                var existeAssistenteNoAgendamento = horarioTransacao.Horario.EstabelecimentoProfissionalAssistente != null;
                if (existeAssistenteNoAgendamento)
                {
                    var assistenteEstaUtilizandoSplitPagamento =
                            Domain.Pessoas.EstabelecimentoProfissionalRepository.EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(horarioTransacao.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional, horarioTransacao.Horario.Estabelecimento.IdEstabelecimento);

                    if (assistenteEstaUtilizandoSplitPagamento)
                    {
                        horarioTransacao.ComissaoAssistente.EhComissaoComSplit = true;
                        GerarLancamentoSplitPagamento(horarioTransacao, true);
                    }
                }
            }
        }

        public decimal ObterTaxaDaSubAdquirente(decimal descontoOperadora, decimal valorPago, DateTime dataHoraVencimento, EstabelecimentoConfiguracaoPOS configuracaoPOS)
        {
            var valorDescontoOperadora = descontoOperadora;

            if (!configuracaoPOS.TipoPOS.PercentualDescontoOperadoraIncideSobreValorTotal)
                return 0;

            if (descontoOperadora < 0 && configuracaoPOS.TipoPOS.TaxaMDR.HasValue && configuracaoPOS.TipoPOS.TaxaRAVBase.HasValue)
            {
                var dataPrevistaRecebimentoSemAdiantamento = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametroPorSubadquirente(Calendario.Agora().AddDays(configuracaoPOS.TipoPOS.DiasRecebimentoOperadora.Value));
                var diasParaAntecipacaoDoPagamento = (dataPrevistaRecebimentoSemAdiantamento.Date - dataHoraVencimento.Date).Days;

                var taxaRAVPorDia = configuracaoPOS.TipoPOS.DiasRecebimentoOperadora.Value > 0 ? configuracaoPOS.TipoPOS.TaxaRAVBase.Value / configuracaoPOS.TipoPOS.DiasRecebimentoOperadora.Value : 0;
                var valorDescontoMDR = (valorPago * (configuracaoPOS.TipoPOS.TaxaMDR.Value / 100));
                var valorDescontoRAV = diasParaAntecipacaoDoPagamento * (valorPago - valorDescontoMDR) * (taxaRAVPorDia / 100);
                valorDescontoOperadora = (valorDescontoMDR + valorDescontoRAV) * -1;
            }

            return valorDescontoOperadora;
        }

        public decimal ObterValorSplitPorParcela(TransacaoFormaPagamento transacaoFormaPagamentoPOS, Comissao comissao)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(comissao.IdPessoaEstabelecimento);
            var configuracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            var totalPago = comissao.Transacao.FormasPagamento.Sum(f => f.ValorPago);
            var parcelaPagamentoPOS = transacaoFormaPagamentoPOS.Parcelas.FirstOrDefault();

            var descontoOperadoraOriginal = comissao.DescontoOperadora;
            var dataRecebimentoParcelaPagamento = parcelaPagamentoPOS.DataRecebimento;
            var descontarDescartaveisDoValorPago = estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago;
            var valorComissaoSemDescontoOperadora = (comissao.ValorBruto + comissao.DescontoCliente + (descontarDescartaveisDoValorPago ? comissao.DescontoExtra : 0));
            var percentualOperadoraEhApenasSobreTotalPago = comissao.DescontoOperadora < 0;

            decimal valorDescontoOperadora =
                ObterTaxaDaSubAdquirente(descontoOperadoraOriginal, valorComissaoSemDescontoOperadora, dataRecebimentoParcelaPagamento, configuracaoPOS);

            var percentualValorParcela = totalPago > 0 ? (parcelaPagamentoPOS.Valor) / totalPago : 0;
            if (percentualValorParcela > 1)
                percentualValorParcela = 1;

            var valorComissaoDaParcela =
                percentualValorParcela > 0 ? (((valorComissaoSemDescontoOperadora + valorDescontoOperadora) * (comissao.ValorComissao / 100)) * percentualValorParcela) : 0;
            var valorComissaoDaParcelaSemDesconto =
                percentualValorParcela > 0 ? (((valorComissaoSemDescontoOperadora) * (comissao.ValorComissao / 100)) * percentualValorParcela) : 0;

            if (!descontarDescartaveisDoValorPago)
            {
                var descontoParcela = percentualValorParcela > 0 ? comissao.DescontoExtra * percentualValorParcela : 0;
                valorComissaoDaParcela += descontoParcela;
            }

            valorComissaoDaParcela = Math.Round(valorComissaoDaParcela * 100) / 100;
            return valorComissaoDaParcela;
        }

        public decimal ObterValorSplit(TransacaoFormaPagamento transacaoFormaPagamentoPOS, Comissao comissao)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(comissao.IdPessoaEstabelecimento);
            var configuracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            var totalPago = comissao.Transacao.FormasPagamento.Sum(f => f.ValorPago);
            var parcelaPagamentoPOS = transacaoFormaPagamentoPOS.Parcelas.FirstOrDefault();

            var descontoOperadoraOriginal = comissao.DescontoOperadora;
            var dataRecebimentoParcelaPagamento = parcelaPagamentoPOS.DataRecebimento;
            var descontarDescartaveisDoValorPago = estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago;
            var valorComissaoSemDescontoOperadora = (comissao.ValorBruto + comissao.DescontoCliente + (descontarDescartaveisDoValorPago ? comissao.DescontoExtra : 0));
            var percentualOperadoraEhApenasSobreTotalPago = comissao.DescontoOperadora < 0;

            decimal valorDescontoOperadora =
                ObterTaxaDaSubAdquirente(descontoOperadoraOriginal, valorComissaoSemDescontoOperadora, dataRecebimentoParcelaPagamento, configuracaoPOS);

            var percentualValorParcela = totalPago > 0 ? totalPago : 0;
            if (percentualValorParcela > 1)
                percentualValorParcela = 1;

            var valorComissaoDaParcela =
                percentualValorParcela > 0 ? (((valorComissaoSemDescontoOperadora + valorDescontoOperadora) * (comissao.ValorComissao / 100))) : 0;
            var valorComissaoDaParcelaSemDesconto =
                percentualValorParcela > 0 ? (((valorComissaoSemDescontoOperadora) * (comissao.ValorComissao / 100))) : 0;

            if (!descontarDescartaveisDoValorPago)
            {
                var descontoParcela = comissao.DescontoExtra;
                valorComissaoDaParcela += descontoParcela;
            }

            valorComissaoDaParcela = Math.Round(valorComissaoDaParcela * 100) / 100;
            return valorComissaoDaParcela;
        }

        private static LancamentoCategoria ObterEGerarLancamentoCategoriaSplit(HorarioTransacao horarioTransacao)
        {
            var lancamentoCategoriaPadraoRepository = Domain.Despesas.LancamentoCategoriaPadraoRepository;
            var lancamentoCategoriaSplit =
                Domain.Despesas.LancamentoCategoriaRepository.Queryable()
                        .FirstOrDefault(p => p.Estabelecimento.IdEstabelecimento == horarioTransacao.Horario.Estabelecimento.IdEstabelecimento &&
                                             p.LancamentoCategoriaPadrao == lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit));

            if (lancamentoCategoriaSplit == null)
            {
                var lancamentoCategoriaSplitPadrao = Domain.Despesas.LancamentoCategoriaPadraoRepository.Queryable().FirstOrDefault(p => p.IdLancamentoCategoria == (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit));
                lancamentoCategoriaSplit = new LancamentoCategoria()
                {
                    Nome = lancamentoCategoriaSplitPadrao.Nome,
                    LancamentoCategoriaPadrao = lancamentoCategoriaSplitPadrao,
                    Estabelecimento = horarioTransacao.Horario.Estabelecimento,
                    LancamentoTipo = lancamentoCategoriaSplitPadrao.LancamentoTipo,
                    NecessarioInformarFornecedor = lancamentoCategoriaSplitPadrao.NecessarioInformarFornecedor,
                    NecessarioInformarProfissional = lancamentoCategoriaSplitPadrao.NecessarioInformarProfissional,
                    Ativo = true,
                    PermiteEdicao = false,
                    PermiteExclusao = false,
                    NivelMinimoAcesso = (int)lancamentoCategoriaSplitPadrao.NivelMinimoAcesso,
                    LancamentoGrupo = Domain.Despesas.LancamentoGrupoService.ObterOuCriarLancamentoGrupoDoEstabelecimentoPorGrupoPadrao(horarioTransacao.Horario.Estabelecimento.IdEstabelecimento, lancamentoCategoriaSplitPadrao.LancamentoGrupoPadrao.IdLancamentoGrupoPadrao)
                };

                Domain.Despesas.LancamentoCategoriaRepository.SaveNewNoFlush(lancamentoCategoriaSplit);
            }

            return lancamentoCategoriaSplit;
        }

        private void GerarLancamentoSplitPagamento(HorarioTransacao horarioTransacao, bool ehComissaoAssistente = false)
        {
            var transacaoFormaPagamento = horarioTransacao.Transacao.FormasPagamento.FirstOrDefault(p => p.FormaPagamento.TipoPOS != null);
            LancamentoCategoria lancamentoCategoriaSplit = ObterEGerarLancamentoCategoriaSplit(horarioTransacao);

            var profissional = ehComissaoAssistente ? horarioTransacao.Horario.EstabelecimentoProfissionalAssistente.Profissional : horarioTransacao.Horario.Profissional;
            var comissao = ehComissaoAssistente ? horarioTransacao.ComissaoAssistente : horarioTransacao.Comissao;
            var configuracoesPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(horarioTransacao.Horario.Estabelecimento.IdEstabelecimento);
            var dataPagamentoSplit = horarioTransacao.Transacao.DataHora.AddDays(configuracoesPOS.TipoPOS.NumeroMinimoDiasRecebimento);

            if (configuracoesPOS != null && configuracoesPOS.TipoPOS.Id == (int)SubadquirenteEnum.Stone && configuracoesPOS.TipoPOS.DiasRecebimentoOperadora.HasValue)
                dataPagamentoSplit = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametroPorSubadquirente(Calendario.Agora().AddDays(1));

            var valorLancamentoSplit = ObterValorSplit(transacaoFormaPagamento, comissao);
            var parcela = transacaoFormaPagamento.Parcelas.FirstOrDefault();

            var valorAReceber = comissao.ValoresAReceberAtivos().FirstOrDefault(p => p.TransacaoFormaPagamentoParcela != null &&
                                                                               p.TransacaoFormaPagamentoParcela.Id == parcela.Id);
            if (valorAReceber == null)
                valorAReceber = comissao.ValoresAReceberAtivos().FirstOrDefault();

            var lancamentoSplitPagamento = new Lancamento()
            {
                Ativo = true,
                DataCriacao = Calendario.Agora(),
                DataPagamento = dataPagamentoSplit.Date,
                DataUltimaAtualizacao = Calendario.Agora(),
                DataVencimento = dataPagamentoSplit.Date,
                Descricao = lancamentoCategoriaSplit.Nome,
                Estabelecimento = horarioTransacao.Horario.Estabelecimento,
                FormaPagamento = transacaoFormaPagamento.FormaPagamento,
                LancamentoCategoria = lancamentoCategoriaSplit,
                PessoaQueRecebeuOuPagou = profissional.PessoaFisica,
                Status = new LancamentoStatusPagamento() { IdLancamentoStatusPagamento = (int)LancamentoStatusPagamentoEnum.Pago },
                Valor = valorLancamentoSplit,
                PessoaQueCriou = horarioTransacao.Transacao.PessoaQueRealizou,
                Transacao = horarioTransacao.Transacao,
                ValorDeComissaoAReceber = valorAReceber
            };

            Domain.Despesas.LancamentoRepository.SaveNewNoFlush(lancamentoSplitPagamento);

            var transacaoPOSTransferencia = new TransacaoLancamentoFinanceiro()
            {
                Lancamento = lancamentoSplitPagamento,
                Transacao = horarioTransacao.Transacao
            };

            Domain.Financeiro.TransacaoLancamentoFinanceiroRepository.SaveNewNoFlush(transacaoPOSTransferencia);
        }

        public void RemoverTaxaOperadoraPosSplit(Transacao transacao)
        {
            if (transacao.HorariosTransacoes != null)
                foreach (var ht in transacao.HorariosTransacoes)
                {
                    RemoverTaxaOperadoraPosSplit(ht);
                }
        }

        private void RemoverTaxaOperadoraPosSplit(HorarioTransacao ht)
        {
            var comissao = ht.Comissao;

            if (comissao == null || !comissao.EhComissaoComSplit) return;

            var valorBase = (comissao.ValorBruto + (comissao.DescontoExtraNoValorBase ? comissao.DescontoExtra : 0) + comissao.DescontoCliente);
            comissao.ComissaoParaPagar = valorBase * (comissao.ValorComissao / 100);
            comissao.DescontoOperadora = 0;
            comissao.ValorBase = valorBase;

            foreach (var v in comissao.ValoresAReceber)
            {
                var valorBaseParcela = (v.ValorBrutoProporcional + (comissao.DescontoExtraNoValorBase ? v.DescontoExtraProporcional : 0) + v.DescontoClienteProporcional);
                v.Valor = valorBaseParcela * (comissao.ValorComissao / 100);
                v.DescontoOperadoraProporcional = 0;
                v.ValorBaseProporcional = valorBaseParcela;
            }
        }

        [TransactionInitRequired]
        public void DesfazerSplit(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);

            var lancamentos = Domain.Despesas.LancamentoRepository.Queryable().Where(l => l.Transacao == transacao && l.Estabelecimento == estabelecimento);

            foreach (var l in lancamentos)
            {
                l.Ativo = false;
                l.Status = new LancamentoStatusPagamento { IdLancamentoStatusPagamento = (int)LancamentoStatusPagamentoEnum.NaoPago };
                l.DataPagamento = null;
            }

            var comissoes = Domain.Financeiro.ComissaoRepository.Queryable().Where(c => c.Transacao.Id == idTransacao);

            foreach (var c in comissoes)
            {
                c.EhComissaoComSplit = false;
            }

            Domain.Financeiro.TransacaoRepository.Flush();
        }
    }
}