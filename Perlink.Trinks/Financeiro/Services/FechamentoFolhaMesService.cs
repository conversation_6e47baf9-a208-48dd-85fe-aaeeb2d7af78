﻿using Perlink.DomainInfrastructure.Facilities;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Shared.Serialization;
using Perlink.Trinks.Despesas;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Resources;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Services
{

    public class FechamentoFolhaMesService : BaseService, IFechamentoFolhaMesService
    {

        #region Fechamento

        public void AtualizarValores(FechamentoFolhaMes fechamento)
        {
            var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value);

            DefinirPeriodo(fechamento);

            CarregarValoresCalculaveis(fechamento);

            <PERSON><PERSON>(fechamento, contaAutenticada.Pessoa.PessoaFisica);
        }

        private void DefinirPeriodo(FechamentoFolhaMes fechamento)
        {
            var dataInicioNormal = new DateTime(fechamento.AnoReferencia, fechamento.MesReferencia, fechamento.Estabelecimento.EstabelecimentoConfiguracaoGeral.DiaInicioFechamentoMensal);
            if (fechamento.Estabelecimento.EstabelecimentoConfiguracaoGeral.DiaInicioFechamentoMensal > 15)
                dataInicioNormal = dataInicioNormal.AddMonths(-1);

            fechamento.ReferenciaInicio = dataInicioNormal;
            fechamento.ReferenciaInicio = AjustarDataInicioParaNaoDeixarBuracoAposFechamentoAnterior(fechamento);

            var dataFim = dataInicioNormal.AddMonths(1).AddDays(-1);
            var fechamentoCobrindoOFim = Domain.Financeiro.FechamentoFolhaMesRepository.Queryable().FirstOrDefault(f => dataFim.AddDays(15) >= f.ReferenciaInicio && dataFim.AddDays(15) <= f.ReferenciaFim && f.MesFechado && f.Estabelecimento == fechamento.Estabelecimento);
            if (fechamentoCobrindoOFim != null)
                dataFim = fechamentoCobrindoOFim.ReferenciaInicio.AddDays(-1);

            var fechamentoMesSeguinte = Domain.Financeiro.FechamentoFolhaMesRepository.ObterPorMes(fechamento.MesReferencia + 1, fechamento.AnoReferencia, fechamento.Estabelecimento);
            if (fechamentoMesSeguinte != null && fechamentoMesSeguinte.MesFechado && !fechamento.MesFechado)
            {
                dataFim = fechamentoMesSeguinte.ReferenciaInicio.AddDays(-1);
            }

            fechamento.ReferenciaFim = dataFim;
        }

        public FechamentoFolhaMes ObterFechamentoDoMes(int mes, int ano, Estabelecimento estabelecimento, PessoaFisica solicitante = null)
        {
            if (ano < 2012)
                ValidationHelper.Instance.AdicionarItemValidacao("Insira uma data após janeiro de 2012.");
            if (new DateTime(ano, mes, 1) > Calendario.Hoje())
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível carregar dados de meses futuros.");

            var exibirDadosAPartirDe = estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;
            if (exibirDadosAPartirDe.HasValue && new DateTime(ano, mes, 1) < exibirDadosAPartirDe.Value)
            {
                var dataValida = exibirDadosAPartirDe.Value.AddMonths(1);
                ValidationHelper.Instance.AdicionarItemValidacao(
                    string.Format(Mensagens.SoEhPossivelCarregarValoresAPartirDe, dataValida));
            }
            if (!ValidationHelper.Instance.IsValid)
                return null;

            var fechamento = Domain.Financeiro.FechamentoFolhaMesRepository.ObterPorMes(mes, ano, estabelecimento) ??
                             CriarFechamento(mes, ano, estabelecimento, solicitante);

            if (!fechamento.MesFechado)
                DefinirPeriodo(fechamento);

            return fechamento;
        }

        [TransactionInitRequired]
        public void ReabrirFolhaMes(FechamentoFolhaMes fechamento, PessoaFisica pessoaQueSolicitou)
        {
            if (!fechamento.MesFechado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível reabrir um mês já aberto.");
                return;
            }

            var proximoMes = new DateTime(fechamento.AnoReferencia, fechamento.MesReferencia, 1).AddMonths(1);
            var proximoFechamento = Domain.Financeiro.FechamentoFolhaMesRepository.ObterPorMes(proximoMes.Month, proximoMes.Year, fechamento.Estabelecimento);

            if (proximoFechamento != null && proximoFechamento.MesFechado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível reabrir um mês que contenha fechamento no mês posterior.\nPara realizar a ação será necessário reabrir o próximo mês.");
            }

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            fechamento.MesFechado = false;

            var fechamentosProfissionais = ListarFechamentosDoMesProfissional(fechamento, pessoaQueSolicitou);
            var idPessoaEstabelecimento = fechamento.Estabelecimento.PessoaJuridica.IdPessoa;

            foreach (var folhaProfissional in fechamentosProfissionais)
            {
                Domain.Financeiro.EstornoPagamentoDeProfissionaisService.EstornarPagamento(folhaProfissional.Id, idPessoaEstabelecimento, pessoaQueSolicitou, OrigemPedidoEstornoEnum.FechamentoMensal);
            }

            CarregarValoresCalculaveis(fechamento);

            fechamento.PessoaUltimaAlteracao = pessoaQueSolicitou;
            fechamento.DataUltimaAlteracao = Calendario.Agora();
            fechamento.XML = null;

            Salvar(fechamento, pessoaQueSolicitou);
            Domain.Financeiro.FechamentoFolhaMesRepository.Update(fechamento);
        }

        public void Salvar(FechamentoFolhaMes fechamento, PessoaFisica pessoaQueSolicitou)
        {
            if (fechamento.Id == 0)
            {
                fechamento.PessoaUltimaAlteracao = pessoaQueSolicitou;
                fechamento.DataUltimaAlteracao = Calendario.Agora();
                Domain.Financeiro.FechamentoFolhaMesRepository.SaveNew(fechamento);
            }

            var fechamentoFolhaMesProfissionals = ListarFechamentosDoMesProfissional(fechamento, pessoaQueSolicitou);

            foreach (var f in fechamentoFolhaMesProfissionals)
            {
                f.FechamentoFolhaMes = fechamento;
                f.PessoaUltimaAlteracao = pessoaQueSolicitou;
                f.DataUltimaAlteracao = Calendario.Agora();
            }

            foreach (var f in fechamentoFolhaMesProfissionals)
            {
                Salvar(f, pessoaQueSolicitou);
            }
        }

        public void Salvar(FechamentoFolhaMesProfissional fechamentoProfissional, PessoaFisica pessoaQueSolicitou)
        {
            fechamentoProfissional.PessoaUltimaAlteracao = pessoaQueSolicitou;
            fechamentoProfissional.DataUltimaAlteracao = Calendario.Agora();
            fechamentoProfissional.FechamentoFolhaMes.DataUltimaAlteracao = Calendario.Agora();

            fechamentoProfissional.FechamentoFolhaMes.PessoaUltimaAlteracao = pessoaQueSolicitou;

            if (fechamentoProfissional.Id == 0)
            {
                Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.SaveNew(fechamentoProfissional);
            }
            else
            {
                Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Update(fechamentoProfissional);
            }
        }

        private void FinalizarFolhaMes(FechamentoFolhaMes fechamento)
        {
            var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value);

            var pessoaLogada = contaAutenticada.Pessoa.PessoaFisica;

            fechamento.MesFechado = true;

            var xml = ObterXMLFechamento(fechamento);
            fechamento.XML = xml;

            var fechamentosProfissionais = ListarFechamentosDoMesProfissional(fechamento, pessoaLogada);

            var dataPagamento = Calendario.Agora();
            var dataInicio = fechamento.ReferenciaInicio;
            var dataFim = fechamento.ReferenciaFim;
            var idPessoaEstabelecimento = fechamento.Estabelecimento.PessoaJuridica.IdPessoa;
            var idEstabelecimento = fechamento.Estabelecimento.IdEstabelecimento;

            foreach (var folhaProfissional in fechamentosProfissionais)
            {
                var session = NHibernateFacility.GetSession<FechamentoFolhaMesProfissional>();
                using (var tx = session.BeginTransaction())
                {
                    try
                    {
                        var dto = new FiltroBaixaComissaoComDescontoDTO(
                            idEstabelecimento,
                            idPessoaEstabelecimento,
                            idContaFinanceira: 0,
                            folhaProfissional.EstabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa,
                            folhaProfissional,
                            pessoaLogada,
                            dataInicio,
                            dataFim,
                            dataPagamento
                        );

                        Domain.Financeiro.PagamentoComissaoComDescontosService.PagarComissoesComDescontos(dto);

                        tx.Commit();
                    }
                    catch (Exception e)
                    {
                        tx.Rollback();

                        Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new Exception(string.Format("[Fechamento Mensal] Erro ao fechar mês entre {0} e {1}. IdEstabelecimento: {2}, EstabelecimentoProfisisonal: {3}, Erro: {4}, InnerException : {5}", dataInicio.ToString(), dataFim.ToString(), idEstabelecimento, folhaProfissional.EstabelecimentoProfissional.Codigo, e.Message, e.InnerException))));

                        ValidationHelper.Instance.AdicionarItemValidacao("Erro ao fechar mês. Tente novamente");
                        break;
                    }
                }
            }

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            Salvar(fechamento, pessoaLogada);
        }

        [TransactionInitRequired]
        public void FecharFechamentoMes(FechamentoFolhaMes fechamento)
        {
            if (fechamento.ReferenciaFim >= Calendario.Hoje())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Este mês atual só poderá ser fechado a partir do próximo dia " + fechamento.Estabelecimento.EstabelecimentoConfiguracaoGeral.DiaInicioFechamentoMensal + ".");
                return;
            }

            if (fechamento.MesFechado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível fechar um mês já fechado.");
                return;
            }

            CarregarDetalhes(fechamento);
            FinalizarFolhaMes(fechamento);
        }

        #endregion Fechamento

        #region Fechamento Profissional

        public void CarregarDetalhes(FechamentoFolhaMes fechamento)
        {
            var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value);
            var fechamentosProfissionais = ListarFechamentosDoMesProfissional(fechamento, contaAutenticada.Pessoa.PessoaFisica);

            var parametrosFiltrosRelatorio = fechamento.ToParametrosFiltrosRelatorio();
            var dataInicio = parametrosFiltrosRelatorio.DataInicial.Value;
            var dataFim = parametrosFiltrosRelatorio.DataFinal.Value;
            var idEstabelecimento = parametrosFiltrosRelatorio.IdEstabelecimento;

            var valores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(parametrosFiltrosRelatorio, true);

            var comissoesServicoParcial = Domain.Pessoas.HorarioTransacaoRepository.ListarComissaoServicoProfissionalDTO(valores);
            comissoesServicoParcial.AddRange(Domain.Pessoas.HorarioTransacaoRepository.ListarComissaoServicoAssistenteDTO(valores));

            var comissoesServico = new List<FechamentoMesComissaoDeServicoDTO>();
            var comissoesServicoGroup = comissoesServicoParcial.GroupBy(p => new { IdPessoaComissionada = p.IdPessoaComissionada, Nome = p.Nome });

            foreach (var item in comissoesServicoGroup)
            {
                var fechamentoMesComissaoDeServicoDTO = new FechamentoMesComissaoDeServicoDTO();

                fechamentoMesComissaoDeServicoDTO.Nome = item.Key.Nome;
                fechamentoMesComissaoDeServicoDTO.IdPessoaComissionada = item.Key.IdPessoaComissionada;
                fechamentoMesComissaoDeServicoDTO.Quantidade = item.Sum(p => p.Quantidade);
                fechamentoMesComissaoDeServicoDTO.ValorPago = item.Sum(p => p.ValorPago);
                fechamentoMesComissaoDeServicoDTO.Comissao = item.Sum(p => p.Comissao);

                comissoesServico.Add(fechamentoMesComissaoDeServicoDTO);
            }

            var comissoesProduto = Domain.Vendas.ItemVendaProdutoRepository.ListarComissoesProdutoDTO(parametrosFiltrosRelatorio, true).ToList();

            var comissoesPacote = Domain.Vendas.ItemVendaPacoteRepository.ListarComissoesPacoteDTO(parametrosFiltrosRelatorio, true).ToList();

            var valesAbertos = Domain.Despesas.LancamentoRepository.ObterQueryValesAbertos(idEstabelecimento, dataInicio, dataFim).ToList();

            var bonificacoesAbertas = Domain.Despesas.LancamentoRepository.ObterQueryBonificacoesAbertas(idEstabelecimento, dataInicio, dataFim).ToList();

            var splitsPagamentoAbertos = Domain.Despesas.LancamentoRepository.ObterQuerySplitsAbertas(idEstabelecimento, dataInicio, dataFim).ToList();         

            var vendaDeProdutoAbertas = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ObterQueryVendaDeProdutosParaProfissionalAbertas(idEstabelecimento, dataInicio, dataFim, parametrosFiltrosRelatorio.Estabelecimento.PessoaJuridica.IdPessoa).ToList();

            var idsTransacoesDescontoProfissional = vendaDeProdutoAbertas.ConvertAll(p => p.TransacaoFormaPagamento.Transacao.Id);

            var descontosProfissional = Domain.Vendas.ItemVendaProdutoRepository.Queryable()
             .Where(d => idsTransacoesDescontoProfissional.Contains(d.Venda.Transacao.Id))
             .ToList();

            var vendasDeProduto = FechamentoMesComissaoDTO.ToFechamentoMesComprasProduto(descontosProfissional, vendaDeProdutoAbertas).ToList();

            foreach (var fp in fechamentosProfissionais)
            {
                CarregarDetalhes(fp, comissoesServico, comissoesPacote, comissoesProduto, valesAbertos, bonificacoesAbertas, vendasDeProduto, splitsPagamentoAbertos);
            }
        }
        public void CarregarValoresCalculaveis(FechamentoFolhaMes fechamento)
        {
            var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value);
            var fechamentosProfissionais = ListarFechamentosDoMesProfissional(fechamento, contaAutenticada.Pessoa.PessoaFisica);
            var idsPessoaProfissional = fechamentosProfissionais.Select(fp => fp.EstabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa).ToList();
            var filtro = fechamento.ToParametrosFiltrosRelatorio();

            var comissoesServicoValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, true)
                .GroupBy(f => f.Comissao.PessoaComissionada.IdPessoa)
                .Select(f => new KeyValuePair<int, decimal>(f.Key, f.Sum(g => g.Valor)))
                .ToList();

            var comissoesProdutoValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeProduto(filtro, true)
                .GroupBy(f => f.Comissao.PessoaComissionada.IdPessoa)
                .Select(f => new KeyValuePair<int, decimal>(f.Key, f.Sum(g => g.Valor)))
                .ToList();

            var comissoesPacoteValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDePacote(filtro, true)
                .GroupBy(f => f.Comissao.PessoaComissionada.IdPessoa)
                .Select(f => new KeyValuePair<int, decimal>(f.Key, f.Sum(g => g.Valor)))
                .ToList();

            var valesAbertosValores = Domain.Financeiro.RelatorioPagamentoProfissionalService.ListarValesAbertosAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicial.Value, filtro.DataFinal.Value, idsPessoaProfissional);

            var bonificacoesAbertasValores = Domain.Financeiro.RelatorioPagamentoProfissionalService.ListarBonificacoesAbertasAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicial.Value, filtro.DataFinal.Value, idsPessoaProfissional);

            var splitsAbertasPagamentoValores = Domain.Financeiro.RelatorioPagamentoProfissionalService.ListarSplitsAbertasAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicial.Value, filtro.DataFinal.Value, idsPessoaProfissional);

            var vendaDeProdutosAbertasValores = Domain.Financeiro.RelatorioPagamentoProfissionalService.ListarVendaDeProdutosAbertasAgrupadosPorProfissional(filtro.IdEstabelecimento, filtro.IdPessoaJuridica, filtro.DataInicial.Value, filtro.DataFinal.Value, idsPessoaProfissional);

            foreach (var fp in fechamentosProfissionais)
            {
                if (fp.FechamentoFolhaMes != null && fp.FechamentoFolhaMes.MesFechado)
                    continue;
                CarregarValoresCalculaveis(fp, comissoesServicoValores, comissoesProdutoValores, comissoesPacoteValores, valesAbertosValores, bonificacoesAbertasValores, vendaDeProdutosAbertasValores, splitsAbertasPagamentoValores);
            }
        }

        public List<FechamentoFolhaMesProfissional> ListarFechamentosDoMesProfissional(FechamentoFolhaMes fechamento, PessoaFisica solicitante, ParametrosPaginacao parametrosPaginacao = null, string filtro = null)
        {
            var parametrosFiltro = fechamento.ToParametrosFiltrosRelatorio(filtro, parametrosPaginacao);
            var fechamentosProfissionais = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.ListarPorFechamento(fechamento)
                .OrderBy(f => f.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto)
                .ToList();

            if (!fechamento.MesFechado)
            {
                var estabelecimentoProfissionais =
                    Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterParaFolhaDePagamento(parametrosFiltro)
                        .ToList();

                fechamentosProfissionais =
                    fechamentosProfissionais.Where(
                        f => estabelecimentoProfissionais.Contains(f.EstabelecimentoProfissional))
                        .ToList();

                foreach (var ep in estabelecimentoProfissionais)
                {
                    if (fechamentosProfissionais.Any(f => f.EstabelecimentoProfissional == ep))
                        continue;
                    var novoFechamentoProfissional = ObterFechamentoDoMesProfissional(ep, fechamento, solicitante);
                    fechamentosProfissionais.Add(novoFechamentoProfissional);
                }

                if (fechamentosProfissionais.Any(f => f.CarregarMesAnterior))
                {
                    var fechamentoAnterior =
                        Domain.Financeiro.FechamentoFolhaMesRepository.ObterUltimoFechamentoAte(
                            fechamento.ReferenciaInicio.Month, fechamento.ReferenciaInicio.Year,
                            fechamento.Estabelecimento);
                    if (fechamentoAnterior != null)
                    {
                        var fechamentosProfissionaisAnteriores =
                            Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.ListarPorFechamento(
                                fechamentoAnterior)
                                .ToList();

                        foreach (var fp in fechamentosProfissionais.Where(f => f.CarregarMesAnterior))
                        {
                            var fechamentoProfissionalAnterior =
                                fechamentosProfissionaisAnteriores.FirstOrDefault(
                                    f => f.EstabelecimentoProfissional == fp.EstabelecimentoProfissional);

                            if (fechamentoProfissionalAnterior != null)
                                CarregarFechamentoAnterior(fp, fechamentoProfissionalAnterior, solicitante);
                        }
                    }
                }
            }
            return fechamentosProfissionais;
        }

        public FechamentoFolhaMesProfissional ObterFechamentoDoMesProfissional(EstabelecimentoProfissional ep, FechamentoFolhaMes fechamento, PessoaFisica solicitante, FechamentoFolhaMes fechamentoAnterior = null)
        {
            var fechamentoFolhaMesProfissional =
                Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.ObterPorFechamento(fechamento, ep) ??
                CriarFechamentoProfissional(ep, fechamento, solicitante);

            return fechamentoFolhaMesProfissional;
        }

        public string ObterXMLFechamento(FechamentoFolhaMes fechamento)
        {
            var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value);
            var fechamentosProfissionais =
                ListarFechamentosDoMesProfissional(fechamento, contaAutenticada.Pessoa.PessoaFisica);
            var dto = new FechamentoMesDTO(fechamento, fechamentosProfissionais);
            return Serializer.Serialize(dto);
        }



        //public string GerarMensagemResiduosComissaoAExibirNoFechamentoMes(int mes, int ano, Estabelecimento estabelecimento, PessoaFisica pessoaQueSolicitou) {
        //    string mensagem = string.Empty;
        //    DateTime periodoAtual = new DateTime(ano, mes, 1);

        //    var residuosComisssoes = Domain.Financeiro.ValorDeComissaoAReceberRepository.ObterResiduosDeComissoesNoFechamentoMensal(mes, ano, estabelecimento, pessoaQueSolicitou);

        //    if (residuosComisssoes.ExibeResiduosEmAmbosMeses) {
        //        mensagem = String.Format(RESMensagens.FechamentoComResiduosComissoesEmDiferentesMeses,
        //                                 residuosComisssoes.ValorComissoesServicosDoMesAnterior.ValorDecimal(),
        //                                 residuosComisssoes.ValorComissoesProdutosDoMesAnterior.ValorDecimal(),
        //                                 periodoAtual.AddMonths(-1).PeriodoMesAnoFormatado(),
        //                                 periodoAtual.AddMonths(-1).PeriodoMesAnoFormatado(),
        //                                 residuosComisssoes.ValorComissoesServicosDesseMesLancadosNoProximoFechamento.ValorDecimal(),
        //                                 residuosComisssoes.ValorComissoesProdutosDesseMesLancadosNoProximoFechamento.ValorDecimal(),
        //                                 periodoAtual.PeriodoMesAnoFormatado(),
        //                                 periodoAtual.AddMonths(1).PeriodoMesAnoFormatado());
        //    }

        //    else if (residuosComisssoes.ExibeMensagemResiduosMesAnterior) {
        //        mensagem = String.Format(RESMensagens.FechamentoComResiduosComissoesDoMesAnterior,
        //                                 residuosComisssoes.ValorComissoesServicosDoMesAnterior.ValorDecimal(),
        //                                 residuosComisssoes.ValorComissoesProdutosDoMesAnterior.ValorDecimal(),
        //                                 periodoAtual.AddMonths(-1).PeriodoMesAnoFormatado(),
        //                                 periodoAtual.AddMonths(-1).PeriodoMesAnoFormatado());
        //    }

        //    else if (residuosComisssoes.ExibeMensagemResiduosDesseMesNoProximoFechamento) {
        //        mensagem = String.Format(RESMensagens.FechamentoComResiduosComissoesNoProximoMes,
        //                                residuosComisssoes.ValorComissoesServicosDesseMesLancadosNoProximoFechamento.ValorDecimal(),
        //                                residuosComisssoes.ValorComissoesProdutosDesseMesLancadosNoProximoFechamento.ValorDecimal(),
        //                                periodoAtual.PeriodoMesAnoFormatado(),
        //                                periodoAtual.AddMonths(1).PeriodoMesAnoFormatado());
        //    }

        //    return mensagem;
        //}

        private static DateTime AjustarDataInicioParaNaoDeixarBuracoAposFechamentoAnterior(FechamentoFolhaMes fechamento)
        {
            // Corrige o período em caso de alteração da data, para não deixar buraco entre 2 fechamentos

            var dataInicio = fechamento.ReferenciaInicio;
            var fechamentoAnterior = Domain.Financeiro.FechamentoFolhaMesRepository.ObterUltimoAntesDe(fechamento);
            if (fechamentoAnterior != null)
            {
                var diaDeFechamentoFoiAlterado = fechamento.ReferenciaInicio != fechamentoAnterior.ReferenciaInicio;
                var dataReferenciaAnterior = new DateTime(fechamentoAnterior.AnoReferencia, fechamentoAnterior.MesReferencia, 1);
                var dataReferenciaAtual = new DateTime(fechamento.AnoReferencia, fechamento.MesReferencia, 1);
                var ultimoFechamentoEhNoMesAnterior = dataReferenciaAnterior.AddMonths(1).Month == dataReferenciaAtual.Month
                    && dataReferenciaAnterior.AddMonths(1).Year == dataReferenciaAtual.Year;

                if (diaDeFechamentoFoiAlterado && ultimoFechamentoEhNoMesAnterior)
                    dataInicio = fechamentoAnterior.ReferenciaFim.AddDays(1);
            }

            return dataInicio;
        }

        private void CarregarDetalhes(FechamentoFolhaMesProfissional fechamentoProfissional, List<FechamentoMesComissaoDeServicoDTO> comissoesServico, List<FechamentoMesComissaoDePacoteDTO> comissoesPacote, List<FechamentoMesComissaoDeProdutoDTO> comissoesProduto, List<Lancamento> vales, List<Lancamento> bonificacoes, List<FechamentoMesCompraProdutoDTO> comprasProduto, List<Lancamento> splitsPagamento)
        {
            var parametrosFiltrosRelatorio = fechamentoProfissional.ToParametrosFiltrosRelatorio();
            var pessoaProfissional = parametrosFiltrosRelatorio.EstabelecimentoProfissional.Profissional.PessoaFisica;

            fechamentoProfissional.ComissaoServicosLista = comissoesServico.Where(f => f.IdPessoaComissionada == pessoaProfissional.IdPessoa).ToList();
            fechamentoProfissional.ComissaoProdutosLista = comissoesProduto.Where(f => f.IdPessoaComissionada == pessoaProfissional.IdPessoa).ToList();
            fechamentoProfissional.ComissaoPacotesLista = comissoesPacote.Where(f => f.IdPessoaComissionada == pessoaProfissional.IdPessoa).ToList();

            fechamentoProfissional.ValesLista = vales.Where(f => f.PessoaQueRecebeuOuPagou != null && f.PessoaQueRecebeuOuPagou.IdPessoa == pessoaProfissional.IdPessoa).ToList();

            fechamentoProfissional.BonificacoesLista = bonificacoes.Where(f => f.PessoaQueRecebeuOuPagou != null && f.PessoaQueRecebeuOuPagou.IdPessoa == pessoaProfissional.IdPessoa).ToList();

            fechamentoProfissional.SplitsPagamentoLista = splitsPagamento.Where(f => f.PessoaQueRecebeuOuPagou.IdPessoa == pessoaProfissional.IdPessoa).ToList();

            fechamentoProfissional.CompraProdutoLista = comprasProduto.Where(f => f.IdPessoaComissionada == pessoaProfissional.IdPessoa).ToList();

            fechamentoProfissional.AtualizarTotaisPelasListas();
        }

        private void CarregarFechamentoAnterior(FechamentoFolhaMesProfissional fechamentoProfissional, FechamentoFolhaMesProfissional fechamentoAnterior, PessoaFisica solicitante)
        {
            if (fechamentoProfissional.FechamentoFolhaMes != null && fechamentoProfissional.FechamentoFolhaMes.MesFechado)
                throw new Exception("Não é possível alterar um mês já fechado");
            //if (fechamentoProfissional.Salario > 0)
            //    throw new Exception("Fechamento carregado anteriormente");

            bool atualizado = fechamentoProfissional.Salario != fechamentoAnterior.Salario ||
                              fechamentoProfissional.SalarioFamilia != fechamentoAnterior.SalarioFamilia ||
                              fechamentoProfissional.INSS != fechamentoAnterior.INSS;

            fechamentoProfissional.Salario = fechamentoAnterior.Salario;
            fechamentoProfissional.SalarioFamilia = fechamentoAnterior.SalarioFamilia;
            fechamentoProfissional.INSS = fechamentoAnterior.INSS;

            if (atualizado)
                Salvar(fechamentoProfissional, solicitante);
        }

        private void CarregarValoresCalculaveis(FechamentoFolhaMesProfissional fechamento, List<KeyValuePair<int, decimal>> comissoesServico, List<KeyValuePair<int, decimal>> comissoesProduto, List<KeyValuePair<int, decimal>> comissoesPacote, List<KeyValuePair<int, decimal>> vales, List<KeyValuePair<int, decimal>> bonificacoes, List<KeyValuePair<int, decimal>> vendaDeProdutos, List<KeyValuePair<int, decimal>> splitsPagamento)
        {
            if (fechamento.FechamentoFolhaMes != null && fechamento.FechamentoFolhaMes.MesFechado)
                throw new Exception("Não é possível alterar um mês já fechado");

            var parametrosFiltrosRelatorio = fechamento.ToParametrosFiltrosRelatorio();
            var pessoaProfissional = parametrosFiltrosRelatorio.EstabelecimentoProfissional.Profissional.PessoaFisica;

            // ComissaoServicos
            fechamento.ComissaoServicos = comissoesServico.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);
            fechamento.ComissaoProdutos = comissoesProduto.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);
            fechamento.ComissaoPacotes = comissoesPacote.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);
            fechamento.Vales = vales.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);
            fechamento.Bonificacoes = bonificacoes.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);
            fechamento.CompraProduto = vendaDeProdutos.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);
            fechamento.SplitsPagamento = splitsPagamento.Where(f => f.Key == pessoaProfissional.IdPessoa).Sum(f => f.Value);

            if (!fechamento.ValoresSalvos)
            {
                fechamento.ValeTransporte = Domain.Despesas.LancamentoRepository.ValorEmValeTransporte(parametrosFiltrosRelatorio);

                fechamento.Alimentacao = Domain.Despesas.LancamentoRepository.ValorEmAlimentacao(parametrosFiltrosRelatorio);

                fechamento.OutrosDescontos = Domain.Despesas.LancamentoRepository.ValorEmOutrosDescontos(parametrosFiltrosRelatorio);
            }

            fechamento.DataUltimaAlteracao = Calendario.Agora();
        }

        [TransactionInitRequired]
        private FechamentoFolhaMes CriarFechamento(int mes, int ano, Estabelecimento estabelecimento, PessoaFisica solicitante = null)
        {
            var fechamento = new FechamentoFolhaMes
            {
                Estabelecimento = estabelecimento,
                MesReferencia = mes,
                AnoReferencia = ano
            };

            DefinirPeriodo(fechamento);

            Salvar(fechamento, solicitante);

            return fechamento;
        }

        private FechamentoFolhaMesProfissional CriarFechamentoProfissional(EstabelecimentoProfissional estabelecimentoProfissional, FechamentoFolhaMes fechamento, PessoaFisica solicitante)
        {
            var fechamentoProfissional = new FechamentoFolhaMesProfissional
            {
                EstabelecimentoProfissional = estabelecimentoProfissional,
                FechamentoFolhaMes = fechamento,
                CarregarMesAnterior = true
            };

            Salvar(fechamentoProfissional, solicitante);

            return fechamentoProfissional;
        }

        #endregion Fechamento Profissional
    }
}