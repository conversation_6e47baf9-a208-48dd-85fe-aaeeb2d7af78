﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro.DTO.Checkout;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.Financeiro.Services
{

    public interface ICheckoutService : IService
    {

        Task<CheckoutRealizadoDTO> RealizarCheckout(DadosParaCheckoutDTO dto);
        List<ItemCheckoutDetalheServicoDTO> HorariosParaPagamento(DateTime data, int? idClienteEstabelecimento = null);
        List<TipoFormaPagamentoDTO> ListarFormasDePagamentoParaFechamentoDeConta();
        List<MotivoDescontoCheckoutDTO> ObterMotivosDeDesconto();
        bool ControleDeCaixaHabilitadoEOperadorComCaixaFechado();
        List<CheckoutsPorDiaDTO> ListarFechamentos(DateTime? dataInicio, DateTime? dataFim);
        DadosCheckoutRealizadoDTO DetalhesDoFechamento(int IdTransacao, bool considerarDadosDePacote);
        DadosParaCheckoutDTO ObterDadosParaCheckoutBaseadoEmTransacao(int idTransacao);
        void CapturarCobrancaCasoSejaNecessario(CheckoutRealizadoDTO retorno);
    }
}