﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.IntegracaoSplit;
using Perlink.IntegracaoSplit.Models;
using Perlink.IntegracaoSplit.Models.Stone;
using Perlink.Shared.Auditing;
using Perlink.Shared.Exceptions;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.EnvioMensagem.Services;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.POS;
using Perlink.Trinks.IntegracaoSplit.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Statics;
using Perlink.Trinks.Wrapper;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ConnectStoneOrderStatusEnum = Trinks.Integracoes.Stone.Connect.V2.Dtos.Response.OrderStatusEnum;

namespace Perlink.Trinks.Financeiro.Services
{

    public class SubadquirenteService : BaseService, ISubadquirenteService
    {

        public async Task<bool> RealizarSplitSeNecessario(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            IIntegracaoSplit integracaoTransferencia;
            switch (config.TipoPOS.Id)
            {
                case (int)SubadquirenteEnum.Stone:
                    integracaoTransferencia = new IntegracaoSplitStone();
                    break;
                case (int)SubadquirenteEnum.GranitoPos:
                    integracaoTransferencia = new IntegracaoSplitGranito();
                    break;
                default:
                    throw new NotImplementedException("Integração de split não implementada: " + config.TipoPOS.Descricao);
            }

            var transacaoPOS = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorIdTransacao(transacao.Id);

            var dados = await GerarDadosParaRegistroDeSplitSeNecessario(transacaoPOS, transacao, integracaoTransferencia, config);

            if (!dados.Splits.Any() && config.TipoPOS.Id != (int)SubadquirenteEnum.GranitoPos)
                return false;

            dados.AgruparSplitsPorRecipient();

            string idExternoSplit = null;

            try
            {
                idExternoSplit = await integracaoTransferencia.RealizarSplit(config.MerchantKey, config.SecretKey, dados, ContextHelper.Instance.AmbienteProducao);
            }
            catch (Exception e)
            {
                ValidationHelper.Instance.AdicionarItemNotificacao("Não foi posssível realizar o split dessa transação. Verifique os dados de configuração fornecidos pela adquirente, tanto os do estabelecimento quanto dos profissionais participantes.");
                LogService<MensagemService>.Error("(Erro split POS Granito: (" + transacao.Id + ") " + (e.InnerException != null ? e.InnerException.Formatada() : e.Message));
            }

            if (idExternoSplit != null)
            {
                var splitKey = new TransacaoPOSSplit
                {
                    TransacaoPOS = transacaoPOS,
                    IdExternoSplit = idExternoSplit,
                    IdStatus = config.TipoPOS.Id != (int)SubadquirenteEnum.GranitoPos ? SplitStoneStatus.Criado.Id : SplitStoneStatus.Processado.Id
                };

                Domain.Financeiro.TransacaoPOSSplitRepository.SaveNew(splitKey);
            }

            if (config.TipoPOS.Id != (int)SubadquirenteEnum.GranitoPos || idExternoSplit == null) return true;

            foreach (var ht in transacao.HorariosTransacoes)
            {
                Domain.Financeiro.SplitService.GerarRegistroDeSplitDePagamentoSeNecessario(ht);
            }

            return true;
        }

        public RetornoPagamentoPosDto RealizarPagamentoPOS(Transacao transacaoFicticia,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfigPos, decimal valorAPagar,
            TipoPagamentoEnum tipoPagamento, TipoParcelamentoEnum? tipoParcelamento = null, int? numParcelas = null,
            string terminalSelecionado = null)
        {
            var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);
            switch (config.TipoPOS.Id)
            {
                case (int)SubadquirenteEnum.StoneSiclos:
                    return RealizarPagamentoPosNaSiclos(config, transacaoFicticia, estabelecimentoConfigPos, valorAPagar, tipoPagamento, tipoParcelamento, numParcelas);
                case (int)SubadquirenteEnum.ConnectPagarme:
                    return RealizarPagamentoPosNoConnectPagarme(transacaoFicticia, estabelecimentoConfigPos, valorAPagar, tipoPagamento, tipoParcelamento, numParcelas, terminalSelecionado);
                case (int)SubadquirenteEnum.ConnectStone:
                    return RealizarPagamentoPosNoConnectStone(transacaoFicticia, estabelecimentoConfigPos, valorAPagar, tipoPagamento, tipoParcelamento, numParcelas, terminalSelecionado);
                default:
                    throw new NotImplementedException("Integração não implementada: " + config.TipoPOS.Descricao);
            }
        }

        public string RealizarCadastroRecebedor(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto)
        {
            var integracaoConnectPagarmeService = new IntegracaoConnectPagarmeService();
            return integracaoConnectPagarmeService.RealizarCadastroRecebedor(dadosParaCadastrarRecebedorDto);
        }
      
        public RetornoListaDeRecebedoresDto ObterListaDeRecebedores(string secretKey, int page, int size)
        {
            var integracaoConnectPagarmeService = new IntegracaoConnectPagarmeService();
            return integracaoConnectPagarmeService
            .ObterListaDeRecebedores(
                    secretKey,
                    page,
                    size
                    );
        }
        private RetornoPagamentoPosDto RealizarPagamentoPosNaSiclos(EstabelecimentoConfiguracaoPOS config, Transacao transacaoFicticia,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfigPOS, decimal valorAPagar, TipoPagamentoEnum tipoPagamento,
            TipoParcelamentoEnum? tipoParcelamento, int? numParcelas)
        {
            IntegracaoSiclosService integracaoService;
            IIntegracaoSplit integracaoTransferencia;

            integracaoTransferencia = new IntegracaoSiclos("", null, POSLogger.IntegracaoPOSLogger, Domain.WebContext.IdEstabelecimentoAutenticado.ToString());
            integracaoService = new IntegracaoSiclosService();

            var dadosSplit = GerarDadosParaRegistroDeSplitSeNecessario(new TransacaoPOS(), transacaoFicticia, integracaoTransferencia, config).Result;
            dadosSplit.AgruparSplitsPorRecipient();

            return integracaoService.RealizarPagamentoPOS(estabelecimentoConfigPOS, (int)(valorAPagar * 100), tipoPagamento, tipoParcelamento, numParcelas, dadosSplit);
        }

        private RetornoPagamentoPosDto RealizarPagamentoPosNoConnectPagarme(Transacao transacaoFicticia,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfigPOS, decimal valorAPagar,
            TipoPagamentoEnum tipoPagamento,
            TipoParcelamentoEnum? tipoParcelamento, int? numParcelas, string terminalSelecionado)
        {
            var chaveDiariaTransacao = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterChaveDiariaTransacao(estabelecimentoConfigPOS.Estabelecimento.IdEstabelecimento);

            var integracaoConnectPagarmeService = new IntegracaoConnectPagarmeService();
            var retornoPagamentoPos = integracaoConnectPagarmeService
                .RealizarPagamentoPos(new RealizarPagamentoDto()
                {
                    EstabelecimentoConfigPos = estabelecimentoConfigPOS,
                    TransacaoFicticia = transacaoFicticia,
                    ValorAPagar = valorAPagar,
                    TipoPagamento = tipoPagamento,
                    TipoParcelamento = tipoParcelamento,
                    NumerosDeParcelas = numParcelas,
                    ChaveDiariaTransacao = chaveDiariaTransacao,
                    Terminal = terminalSelecionado
                });

            return retornoPagamentoPos;
        }

        private RetornoPagamentoPosDto RealizarPagamentoPosNoConnectStone(Transacao transacaoFicticia,
            EstabelecimentoConfiguracaoPOS estabelecimentoConfigPOS, decimal valorAPagar,
            TipoPagamentoEnum tipoPagamento,
            TipoParcelamentoEnum? tipoParcelamento, int? numParcelas, string terminalSelecionado)
        {

            var chaveDiariaTransacao = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterChaveDiariaTransacao(estabelecimentoConfigPOS.Estabelecimento.IdEstabelecimento);

            var integracaoConnectStoneService = new IntegracaoConnectStoneService();
            var retornoPagamentoPos = integracaoConnectStoneService
                    .RealizarPagamentoPos(new RealizarPagamentoDto()
                    {
                        EstabelecimentoConfigPos = estabelecimentoConfigPOS,
                        TransacaoFicticia = transacaoFicticia,
                        ValorAPagar = valorAPagar,
                        TipoPagamento = tipoPagamento,
                        TipoParcelamento = tipoParcelamento,
                        NumerosDeParcelas = numParcelas,
                        ChaveDiariaTransacao = chaveDiariaTransacao,
                        Terminal = terminalSelecionado
                    });

            return retornoPagamentoPos;
        }

        public bool CancelarPreTransacaoPos(string codigoPreTransacao)
        {
            var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                .ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            switch (config.TipoPOS.Id)
            {
                case (int)SubadquirenteEnum.StoneSiclos:
                    return CancelarPreTransacaoPosSiclos(codigoPreTransacao);
                case (int)SubadquirenteEnum.ConnectPagarme:
                    return CancelarPreTransacaoPosConnectPagarme(config, codigoPreTransacao);
                case (int)SubadquirenteEnum.ConnectStone:
                    return CancelarPreTransacaoPosConnectStone(config, codigoPreTransacao);
                default:
                    throw new NotImplementedException("Integração não implementada: " + config.TipoPOS.Descricao);
            }
        }

        public void CancelarOrdemConnect(string idOrdem, int idEstabelecimento)
        {
            var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);

            switch (config.TipoPOS.Id)
            {
                case (int)SubadquirenteEnum.ConnectStone:
                    CancelarOrdemConnectStone(config, idOrdem);
                    break;
                case (int)SubadquirenteEnum.ConnectPagarme:
                    CancelarOrdemConnectPagarme(config, idOrdem);
                    break;
                default:
                    throw new NotImplementedException("Integração não implementada: " + config.TipoPOS.Descricao);
            }

            var transacaoPosWebhookRequest = Domain.Financeiro.TransacaoPosWebhookRequestRepository
                .ObterTransacaoPosWebhookRequestPorIdExternoTransacao(idOrdem);

            if (transacaoPosWebhookRequest == null)
                return;

            transacaoPosWebhookRequest.OperacaoCancelada = true;
            Domain.Financeiro.TransacaoPosWebhookRequestRepository.Update(transacaoPosWebhookRequest);
        }

        public void CancelarOrdemConnectStone(EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPos, string idOrdem)
        {
            var integracao = new IntegracaoConnectStoneService();
            var ordem = integracao.ObterOrdem(estabelecimentoConfiguracaoPos.SecretKey, idOrdem);

            if (ordem.Status == ConnectStoneOrderStatusEnum.Pending && (ordem.Charges == null || ordem.Charges.Count == 0))
            {
                integracao.CancelarOrdem(estabelecimentoConfiguracaoPos.SecretKey, idOrdem);
            }
        }

        public void CancelarOrdemConnectPagarme(EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPos, string idOrdem)
        {
            var integracao = new IntegracaoConnectPagarmeService();
            var ordem = integracao.ObterOrdem(estabelecimentoConfiguracaoPos.SecretKey, idOrdem);

            if (ordem.Status != "paid" && (ordem.Charges == null || ordem.Charges.Count == 0))
                integracao.CancelarOrdem(estabelecimentoConfiguracaoPos.SecretKey, idOrdem);
        }

        private bool CancelarPreTransacaoPosConnectPagarme(EstabelecimentoConfiguracaoPOS config, string codigoPreTransacao)
        {
            var integracao = new IntegracaoConnectPagarmeService();
            return integracao.CancelarOrdem(config.SecretKey, codigoPreTransacao);
        }

        private bool CancelarPreTransacaoPosConnectStone(EstabelecimentoConfiguracaoPOS config, string codigoPreTransacao)
        {
            var integracao = new IntegracaoConnectStoneService();
            return integracao.CancelarOrdem(config.SecretKey, codigoPreTransacao);
        }

        private bool CancelarPreTransacaoPosSiclos(string codigoPreTransacao)
        {
            var integracao = new IntegracaoSiclosService();
            return integracao.CancelarPreTransacaoPOS(codigoPreTransacao);
        }

        public RetornoCadastroDeEstabelecimentoDTO AssociarEstabelecimentoASubadquirente(Estabelecimento estabelecimento)
        {
            IntegracaoSiclosService integracao = new IntegracaoSiclosService();

            return integracao.AssociarEstabelecimento(estabelecimento);
        }

        public async Task AtualizarStatusPosSplits()
        {
            var splits = Domain.Financeiro.TransacaoPOSSplitRepository.ListarSplitsPendentesProcessamento();
            IIntegracaoSplit integracaoTransferencia = new IntegracaoSplitStone();

            foreach (var splitPos in splits)
            {
                var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(splitPos.TransacaoPOS.Estabelecimento.IdEstabelecimento);
                try
                {
                    await Domain.Financeiro.SubadquirenteService.ObterStatusSplitEAtualizarTransacaoPOS(integracaoTransferencia, splitPos, config);
                }
                catch (Exception e)
                {
                    ValidationHelper.Instance.AdicionarItemNotificacao((e.InnerException != null ? e.InnerException.Formatada() : e.Message) + "(transacao_pos_transferencia_id: " + splitPos.Id + ")");
                    LogService<MensagemService>.Error("(transacao_pos_transferencia_id: " + splitPos.Id + ") " + e.Formatada());
                }
            }
        }

        public async Task ObterStatusSplitEAtualizarTransacaoPOS(IIntegracaoSplit integracaoTransferencia, TransacaoPOSSplit splitPos, EstabelecimentoConfiguracaoPOS config)
        {
            var statusSplit = await integracaoTransferencia.ConsultarStatusDoSplit(config.MerchantKey, config.SecretKey, splitPos.IdExternoSplit);
            var transacao = Domain.Financeiro.TransacaoRepository.ObterPorIdTransacao(splitPos.TransacaoPOS.Transacao.Id);

            var erro = false;

            if (!erro)
            {
                splitPos.AtualizarStatus(statusSplit);
                Domain.Financeiro.TransacaoPOSSplitRepository.Update(splitPos);
            }
        }

        public async Task<bool> CancelarSplit(int idTransacao)
        {
            var transacaoPOS = Domain.Financeiro.TransacaoPOSRepository.ObterTransacaoPOSPorIdTransacao(idTransacao);
            var transacaoPosSplits = Domain.Financeiro.TransacaoPOSSplitRepository.ListarTransferKeysPorTransacaoPOS(transacaoPOS.Id);

            var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            IIntegracaoSplit integracaoTranferencia;
            if (transacaoPOS.TipoPOS.Id == (int)SubadquirenteEnum.Stone)
                integracaoTranferencia = new IntegracaoSplitStone();
            else
                throw new NotImplementedException("Integração de split não implementada: " + config.TipoPOS.Descricao);

            if (transacaoPosSplits.Count == 0)
            {
                throw new NotImplementedException("Essa transacão não possui split para estorno.");
            }
            else
            {
                foreach (var splitKey in transacaoPosSplits)
                {
                    var idExternoSplit = await integracaoTranferencia.CancelarSplit(config.MerchantKey, config.SecretKey, splitKey, ContextHelper.Instance.AmbienteProducao);
                }
            }

            return true;
        }

        public async Task<RequestSplitModel> GerarDadosParaRegistroDeSplitSeNecessario(TransacaoPOS transacaoPOS, Transacao transacao, IIntegracaoSplit integracaoTranferencia, EstabelecimentoConfiguracaoPOS config)
        {
            var dadosParaRegistroDeSplit = new RequestSplitModel();
            var formaPagamentoPOS = transacao.FormasPagamento.FirstOrDefault(p => p.FormaPagamento.TipoPOS != null);
            if (formaPagamentoPOS != null && formaPagamentoPOS.ValorPago > 0 && transacao.FormasPagamento.Count <= 1)
            {
                decimal valorTotalAhSerPagoEmSplit = 0;
                foreach (var horarioTransacao in transacao.HorariosTransacoes)
                {
                    Horario horario = horarioTransacao.Horario;
                    var profissionalUsaSplit = Domain.Pessoas.EstabelecimentoProfissionalRepository.EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);

                    if (profissionalUsaSplit && horarioTransacao.Comissao != null)
                    {
                        var valorComissaoDaParcela = Domain.Financeiro.SplitService.ObterValorSplit(formaPagamentoPOS, horarioTransacao.Comissao);
                        if (valorComissaoDaParcela > 0)
                        {
                            valorTotalAhSerPagoEmSplit += valorComissaoDaParcela;
                            var splitPagamentoProfissionalModel = await GerarDadosParaRegistroDeSplitDeProfissional(horario, valorComissaoDaParcela, integracaoTranferencia, config);
                            dadosParaRegistroDeSplit.Splits.Add(splitPagamentoProfissionalModel);
                        }
                    }

                    var existeAssistenteNoAgendamento = horario.EstabelecimentoProfissionalAssistente != null;
                    if (existeAssistenteNoAgendamento)
                    {
                        var assistenteEstaUtilizandoSplitPagamento =
                            Domain.Pessoas.EstabelecimentoProfissionalRepository.EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);

                        if (assistenteEstaUtilizandoSplitPagamento && horarioTransacao.ComissaoAssistente != null)
                        {
                            var valorComissaoDaParcela = Domain.Financeiro.SplitService.ObterValorSplit(formaPagamentoPOS, horarioTransacao.ComissaoAssistente);
                            if (valorComissaoDaParcela > 0)
                            {
                                valorTotalAhSerPagoEmSplit += valorComissaoDaParcela;
                                var splitPagamentoProfissionalModel = await GerarDadosParaRegistroDeSplitDeAssistente(horario, valorComissaoDaParcela, integracaoTranferencia, config);
                                dadosParaRegistroDeSplit.Splits.Add(splitPagamentoProfissionalModel);
                            }
                        }
                    }
                }

                if (dadosParaRegistroDeSplit.Splits.Any())
                {
                    if (formaPagamentoPOS.ValorPago == dadosParaRegistroDeSplit.Splits.Sum(s => s.Valor))
                        dadosParaRegistroDeSplit.TipoResponsabilidadePorTaxaStoneEnum = TipoResponsabilidadePorTaxaStoneEnum.recipient;
                    else
                        dadosParaRegistroDeSplit.TipoResponsabilidadePorTaxaStoneEnum = TipoResponsabilidadePorTaxaStoneEnum.shared;
                }

                dadosParaRegistroDeSplit.ValorTransacao = formaPagamentoPOS.ValorPago;
                dadosParaRegistroDeSplit.IdExternoTransacao = transacaoPOS.AcquirerTransactionKey;
            }

            return dadosParaRegistroDeSplit;
        }

        private static async Task<RequestSplitModel.SplitModel> GerarDadosParaRegistroDeSplitDeAssistente(Horario horario, decimal valorComissaoDaParcela, IIntegracaoSplit integracaoTranferencia, EstabelecimentoConfiguracaoPOS config)
        {
            var estabelecimentoProfissional = horario.EstabelecimentoProfissionalAssistente;
            var estabelecimentoPermiteComissaoProfissionalAssistente = horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissaoDeAssistentes && !estabelecimentoProfissional.EhExcecaoDescontoTaxaOperadoraAssistente;
            return await GerarDadosParaRegistroDeSplitDeEstabelecimentoProfissional(valorComissaoDaParcela, estabelecimentoProfissional, integracaoTranferencia, config, estabelecimentoPermiteComissaoProfissionalAssistente);
        }

        private static async Task<RequestSplitModel.SplitModel> GerarDadosParaRegistroDeSplitDeProfissional(Horario horario, decimal valorComissaoDaParcela, IIntegracaoSplit integracaoTranferencia, EstabelecimentoConfiguracaoPOS config)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);
            var estabelecimentoPermiteComissaoProfissional = horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao && !estabelecimentoProfissional.EhExcecaoDescontoTaxaOperadora;
            return await GerarDadosParaRegistroDeSplitDeEstabelecimentoProfissional(valorComissaoDaParcela, estabelecimentoProfissional, integracaoTranferencia, config, estabelecimentoPermiteComissaoProfissional);
        }

        private static async Task<RequestSplitModel.SplitModel> GerarDadosParaRegistroDeSplitDeEstabelecimentoProfissional(decimal valorComissaoDaParcela, EstabelecimentoProfissional estabelecimentoProfissional, IIntegracaoSplit integracaoTranferencia, EstabelecimentoConfiguracaoPOS config, bool cobrarTaxaDoProfissional = false)
        {
            var idBeneficiario = integracaoTranferencia == null ? estabelecimentoProfissional.IdExternosDeBeneficiario : estabelecimentoProfissional.CodigoIdentificacaoPOS;

            if (integracaoTranferencia is IntegracaoSplitStone)
            {
                if (string.IsNullOrWhiteSpace(estabelecimentoProfissional.IdExternosDeBeneficiario))
                {
                    var dados = new RequestRecipientModelStone
                    {
                        recipient_affiliation_key_in_provider = estabelecimentoProfissional.CodigoIdentificacaoPOS,
                        recipient_name = estabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto
                    };
                    estabelecimentoProfissional.IdExternosDeBeneficiario = await integracaoTranferencia.CriarBeneficiarioNaAPI(config.MerchantKey, config.SecretKey, dados, ContextHelper.Instance.AmbienteProducao);
                }

                idBeneficiario = estabelecimentoProfissional.IdExternosDeBeneficiario;
            }

            if (integracaoTranferencia is IntegracaoSiclos)
            {
                idBeneficiario = estabelecimentoProfissional.IdExternosDeBeneficiario;
            }

            return new RequestSplitModel.SplitModel
            {
                Valor = valorComissaoDaParcela,
                IdExternoBeneficiario = idBeneficiario,
                CobrarTaxaDoProfissional = cobrarTaxaDoProfissional
            };
        }

        public void GravarLogPOS(string AuthorizationReportValue, int IdCliente)
        {
            var bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();

            byte[] byteArray = System.Text.Encoding.UTF8.GetBytes(AuthorizationReportValue);
            MemoryStream stream = new MemoryStream(byteArray);

            var s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();

            var nomeArquivo = String.Format("pos_{0}_{1}_{2}.json", Domain.WebContext.IdEstabelecimentoAutenticado, IdCliente, DateTime.Now.ToString("yyyyMMddHHmmss"));
            s3AmazonControleDeArquivos.GravarArquivo(stream, bucket + "/POS/" + Domain.WebContext.IdEstabelecimentoAutenticado, nomeArquivo);
        }

        public Task<string> CancelarTransferencia(string internalTransferSecurityKey, string originMerchantAffiliationKey, string transferOrderKey)
        {
            throw new System.NotImplementedException();
        }

        public Task<TransferInternaStatusEnum> ObterStatusTransferencia(string transferOrderKey, string internalTransferSecurityKey)
        {
            throw new System.NotImplementedException();
        }

        public RetornoCadastroDeRecebedorDTO AssociarProfissionalComoRecebedorPOS(EstabelecimentoProfissional ep)
        {
            var integracao = new IntegracaoSiclosService();
            return integracao.CadastrarProfissionalComoRecebedorSemStoneCode(ep);
        }

        public RetornoCadastroDeRecebedorDTO AssociarProfissionalComoRecebedorPOSComStonecode(EstabelecimentoProfissional ep, string documento)
        {
            var integracao = new IntegracaoSiclosService();
            return integracao.CadastrarProfissionalComoRecebedorComStoneCode(ep, documento);
        }

        public bool CancelarCadastroProfissionalRecebedor(EstabelecimentoProfissional ep)
        {
            var integracao = new IntegracaoSiclosService();
            return integracao.CancelarCadastroProfissionalRecebedor(ep);
        }

        public bool CancelarCadastroEstabelecimento(EstabelecimentoConfiguracaoPOS estabelecimentoPos)
        {
            var integracao = new IntegracaoSiclosService();
            return integracao.CancelarCadastroEstabelecimento(estabelecimentoPos);
        }

        public bool CapturarPedidoComSplit(int idEstabelecimento, SplitDTO split)
        {
            var config = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);
            var integracao = new IntegracaoConnectPagarmeService();
            return integracao.CapturarTransacaoComSplit(idEstabelecimento, split, config.SecretKey);
        }
    }
}