﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Calculos;
using Perlink.Trinks.Financeiro.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Services
{
    public class RelatorioPagamentoProfissionalService : BaseService, IRelatorioPagamentoProfissionalService
    {

        public TotaisParaPagamentoProfissionalDTO ObterTotaisParaPagamento(FiltroRelatorioPagamentoProfissional filtro)
        {
            ValidarDataDeConsulta(filtro.DataInicio, filtro.DataFim);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var idPessoaDosProfissionais = ObterIdPessoaDosProfissionais(filtro.IdEstabelecimento, filtro.IdsProfissionais);

            var comissoesPagas = Domain.Financeiro.ValorDeComissaoAReceberRepository
                .ObterQueryComissoesPagas(filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(c => (decimal?)c.Valor);

            var comissoesAbertas = Domain.Financeiro.ValorDeComissaoAReceberRepository
                .ObterQueryComissoesAbertas(filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(c => (decimal?)c.Valor);

            var bonificacoesPagas = Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(b => (decimal?)b.Valor);

            var bonificacoesAbertas = Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(b => (decimal?)b.Valor);

            var valesPagos = Domain.Despesas.LancamentoRepository
                .ObterQueryValesPagos(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(b => (decimal?)b.Valor);

            var valesAbertos = Domain.Despesas.LancamentoRepository
                .ObterQueryValesAbertos(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(b => (decimal?)b.Valor);

            var gorjetasPagas = Domain.Financeiro.GorjetaRepository
                .ObterQueryGorjetasPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(c => (decimal?)c.Valor);

            var gorjetasAbertas = Domain.Financeiro.GorjetaRepository
                .ObterQueryGorjetasAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(c => (decimal?)c.Valor);

            var vendasDeProdutosPagas = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository
                .ObterQueryVendaDeProdutosParaProfissionalPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaEstabelecimento)
                .Sum(tp => (decimal?)tp.Valor);

            var vendasDeProdutosAbertas = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository
                .ObterQueryVendaDeProdutosParaProfissionalAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaEstabelecimento)
                .Sum(tp => (decimal?)tp.Valor);

            var splitsPagas = Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(sp => (decimal?)sp.Valor);

            var splitsAbertas = Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsAbertas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, idPessoaDosProfissionais)
                .Sum(sp => (decimal?)sp.Valor);

            var comissoesAbertasComDespesas = (comissoesAbertas ?? 0) + (bonificacoesAbertas ?? 0) - (valesAbertos ?? 0) - (vendasDeProdutosAbertas ?? 0) - (splitsAbertas ?? 0);
            var comissoesPagasComDespesas = (comissoesPagas ?? 0) + (bonificacoesPagas ?? 0) - (valesPagos ?? 0) - (vendasDeProdutosPagas ?? 0) - (splitsPagas ?? 0);

            var totalPago = comissoesPagasComDespesas + (gorjetasPagas ?? 0);
            var totalRestante = comissoesAbertasComDespesas + (gorjetasAbertas ?? 0);

            return new TotaisParaPagamentoProfissionalDTO
            {
                TotalPago = totalPago,
                TotalRestante = totalRestante,
                TotalComissao = comissoesAbertasComDespesas + comissoesPagasComDespesas,
                TotalGorjeta = (gorjetasAbertas ?? 0) + (gorjetasPagas ?? 0),
            };
        }

        private IEnumerable<int> ObterIdPessoaDosProfissionais(int idEstabelecimento, List<int> idsDosProfissionais)
        {
            if (idsDosProfissionais == null || !idsDosProfissionais.Any())
            {
                return Enumerable.Empty<int>();
            }

            return Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarIdPessoaPorIdProfissional(idsDosProfissionais, idEstabelecimento);
        }

        private void ValidarDataDeConsulta(DateTime dataInicio, DateTime dataFim)
        {
            if (dataFim < dataInicio)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A data de fim não pode ser anterior à data de início.");
            }

            if ((dataFim - dataInicio).TotalDays > 31)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O intervalo entre a data de início e a data de fim não pode ultrapassar 1 mês.");
            }
        }


        public ResultadoProfissionaisAPagarDTO ListarProfissionaisParaPagamento(FiltroRelatorioPagamentoProfissional filtro)
        {

            var listaProfissionaisAPagarDTOs = FiltrarProfissionaisParaPagamento(filtro);

            var resultado = new ResultadoProfissionaisAPagarDTO
            {
                Registros = listaProfissionaisAPagarDTOs,
                Paginacao = filtro.Paginacao
            };

            return resultado;
        }

        private List<ProfissionaisAPagarDTO> FiltrarProfissionaisParaPagamento(FiltroRelatorioPagamentoProfissional filtro)
        {
            var listaDeProfissionais = ObterListaProfissionaisPorEstabelecimento(filtro);

            var listaProfissionaisAPagarDTOs = ObterListaDeProfissionaisParaPagamento(filtro, listaDeProfissionais);

            return listaProfissionaisAPagarDTOs;
        }

        private List<ProfissionaisAPagarDTO> ObterListaDeProfissionaisParaPagamento(FiltroRelatorioPagamentoProfissional filtro, List<ListarProfissionaisPorEstabelecimentoDTO> listaProfissionais)
        {
            List<ProfissionaisAPagarDTO> listaProfissionaisAPagarDTOs = new List<ProfissionaisAPagarDTO>();

            var listaIdPessoaProfissional = listaProfissionais.Select(p => p.IdPessoaProfissional).ToList();

            var comissoesPagas = ListarComissoesPagasAgrupadasPorProfissional(filtro, listaIdPessoaProfissional);
            var comissoesAbertas = ListarComissoesAbertasAgrupadasPorProfissional(filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional);

            var gorjetasPagas = ListarGorjetasPagasAgrupadasPorProfissional(filtro, listaIdPessoaProfissional);
            var gorjetasAbertas = ListarGorjetasAbertasAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional);

            var bonificacoesPagas = ListarBonificacoesPagasAgrupadasPorProfissional(filtro, listaIdPessoaProfissional);
            var bonificacoesAbertas = ListarBonificacoesAbertasAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional);

            var valesPagos = ListarValesPagosAgrupadasPorProfissional(filtro, listaIdPessoaProfissional);
            var valesAbertos = ListarValesAbertosAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional);

            var splitsPagas = ListarSplitsPagasAgrupadasPorProfissional(filtro, listaIdPessoaProfissional);
            var splitsAbertas = ListarSplitsAbertasAgrupadasPorProfissional(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional);

            var vendaDeProdutosPagas = ListarVendaDeProdutosPagasAgrupadosPorProfissional(filtro, listaIdPessoaProfissional);
            var vendaDeProdutosAbertas = ListarVendaDeProdutosAbertasAgrupadosPorProfissional(filtro.IdEstabelecimento,
                filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional);

            foreach (var profissional in listaProfissionais)
            {
                var totalComissoesPagas = comissoesPagas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();
                var totalComissoesAbertas = comissoesAbertas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();

                var totalGorjetasPagas = gorjetasPagas.Where(g => g.Key == profissional.IdProfissional).Select(g => g.Value).FirstOrDefault();
                var totalGorjetasAbertas = gorjetasAbertas.Where(g => g.Key == profissional.IdProfissional).Select(g => g.Value).FirstOrDefault();

                var totalBonficacoesPagas = bonificacoesPagas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();
                var totalBonificacoesAbertas = bonificacoesAbertas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();

                var totalValesPagos = valesPagos.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();
                var totalValesAbertos = valesAbertos.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();

                var totalSplitsPagas = splitsPagas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();
                var totalSplitsAbertas = splitsAbertas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();

                var totalVendaDeProdutosPagas = vendaDeProdutosPagas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();
                var totalVendaDeProdutosAbertas = vendaDeProdutosAbertas.Where(g => g.Key == profissional.IdPessoaProfissional).Select(g => (decimal?)g.Value).FirstOrDefault();

                var totalComissoesAbertasComDespesas = CalculoValorComissaoComLancamentos.ObterValorComissaoComDespesas(totalComissoesAbertas, totalBonificacoesAbertas, totalValesAbertos, totalVendaDeProdutosAbertas, totalSplitsAbertas);
                var totalComissoesPagasComDespesas = CalculoValorComissaoComLancamentos.ObterValorComissaoComDespesas(totalComissoesPagas, totalBonficacoesPagas, totalValesPagos, totalVendaDeProdutosPagas, totalSplitsPagas);

                var totalComissoes = totalComissoesAbertasComDespesas + totalComissoesPagasComDespesas;

                var totalGorjetas = totalGorjetasAbertas + totalGorjetasPagas;

                var totalPagar = totalComissoes + totalGorjetas;
                var totalPago = totalGorjetasPagas + totalComissoesPagasComDespesas;
                var totalRestante = totalPagar - totalPago;

                var existePagamentoAgendadoAguardandoPagamentoOuComFalha = Domain.ContaDigital.PagamentoAgendadoService.ExistePagamentoAgendadoAguardandoPagamentoOuComFalha(profissional.IdEstabelecimentoProfissional, filtro.IdEstabelecimento);

                listaProfissionaisAPagarDTOs.Add(new ProfissionaisAPagarDTO
                {
                    IdProfissional = profissional.IdProfissional,
                    IdEstabelecimentoProfissional = profissional.IdEstabelecimentoProfissional,
                    NomeProfissional = profissional.NomeProfissional,
                    Ativo = profissional.Ativo,
                    IdChavePixProfissional = profissional.IdChavePixProfissional,
                    TotalAPagar = totalPagar,
                    TotalPago = totalPago,
                    TotalRestante = totalRestante,
                    TotalGorjetasAbertas = totalGorjetasAbertas,
                    TotalComissoesAbertas = totalComissoesAbertasComDespesas,
                    TotalComissao = totalComissoes,
                    TotalGorjeta = totalGorjetas,
                    ExistePagamentoAgendadoAguardandoPagamentoOuComFalha = existePagamentoAgendadoAguardandoPagamentoOuComFalha
                });
            }

            return listaProfissionaisAPagarDTOs;
        }

        #region Gorjetas

        private List<KeyValuePair<int, decimal>> ListarGorjetasPagasAgrupadasPorProfissional(FiltroRelatorioPagamentoProfissional filtro, List<int> listaIdPessoaProfissional)
        {
            return Domain.Financeiro.GorjetaRepository
                .ObterQueryGorjetasPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional)
                .GroupBy(g => g.Profissional.IdProfissional)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(g => g.Valor)))
                .ToList();
        }

        public List<KeyValuePair<int, decimal>> ListarGorjetasAbertasAgrupadasPorProfissional(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, List<int> listaIdPessoaProfissional)
        {
            return Domain.Financeiro.GorjetaRepository
                .ObterQueryGorjetasAbertas(idEstabelecimento, dataInicio, dataFim, listaIdPessoaProfissional)
                .GroupBy(g => g.Profissional.IdProfissional)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(g => g.Valor)))
                .ToList();
        }

        #endregion Gorjetas

        #region Comissoes

        private List<KeyValuePair<int, decimal>> ListarComissoesPagasAgrupadasPorProfissional(FiltroRelatorioPagamentoProfissional filtro, List<int> listaIdPessoaProfissional)
        {
            return Domain.Financeiro.ValorDeComissaoAReceberRepository
                .ObterQueryComissoesPagas(filtro.IdPessoaEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional)
                .GroupBy(c => c.Comissao.PessoaComissionada.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(c => c.Valor)))
                .ToList();
        }

        public List<KeyValuePair<int, decimal>> ListarComissoesAbertasAgrupadasPorProfissional(int idPessoaEstabelecimento, DateTime dataInicio, DateTime dataFim, List<int> listaIdPessoaProfissional)
        {
            return Domain.Financeiro.ValorDeComissaoAReceberRepository
                .ObterQueryComissoesAbertas(idPessoaEstabelecimento, dataInicio, dataFim, listaIdPessoaProfissional)
                .GroupBy(c => c.Comissao.PessoaComissionada.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(c => c.Valor)))
                .ToList();
        }

        #endregion Comissoes

        #region Bonificacoes

        public List<KeyValuePair<int, decimal>> ListarBonificacoesAbertasAgrupadasPorProfissional(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, List<int> listaIdPessoaProfissional)
        {
            return Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesAbertas(idEstabelecimento, dataInicio, dataFim, listaIdPessoaProfissional)
                .GroupBy(b => b.PessoaQueRecebeuOuPagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(b => b.Valor)))
                .ToList();
        }

        private List<KeyValuePair<int, decimal>> ListarBonificacoesPagasAgrupadasPorProfissional(FiltroRelatorioPagamentoProfissional filtro, List<int> listaIdPessoaProfissional)
        {
            return Domain.Despesas.LancamentoRepository
                .ObterQueryBonificacoesPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional)
                .GroupBy(b => b.PessoaQueRecebeuOuPagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(b => b.Valor)))
                .ToList();
        }

        #endregion Bonificacoes   

        #region Vales

        public List<KeyValuePair<int, decimal>> ListarValesAbertosAgrupadasPorProfissional(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, List<int> listaIdPessoaProfissional)
        {
            return Domain.Despesas.LancamentoRepository
                .ObterQueryValesAbertos(idEstabelecimento, dataInicio, dataFim, listaIdPessoaProfissional)
                .GroupBy(b => b.PessoaQueRecebeuOuPagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(v => v.Valor)))
                .ToList();
        }

        public List<KeyValuePair<int, decimal>> ListarValesPagosAgrupadasPorProfissional(FiltroRelatorioPagamentoProfissional filtro, List<int> listaIdPessoaProfissional)
        {
            return Domain.Despesas.LancamentoRepository
                .ObterQueryValesPagos(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional)
                .GroupBy(v => v.PessoaQueRecebeuOuPagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(v => v.Valor)))
                .ToList();
        }

        #endregion Vales

        #region Pagamento de Split

        public List<KeyValuePair<int, decimal>> ListarSplitsAbertasAgrupadasPorProfissional(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, List<int> listaIdPessoaProfissional)
        {
            return Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsAbertas(idEstabelecimento, dataInicio, dataFim, listaIdPessoaProfissional)
                .GroupBy(s => s.PessoaQueRecebeuOuPagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(s => s.Valor)))
                .ToList();
        }

        public List<KeyValuePair<int, decimal>> ListarSplitsPagasAgrupadasPorProfissional(FiltroRelatorioPagamentoProfissional filtro, List<int> listaIdPessoaProfissional)
        {
            return Domain.Despesas.LancamentoRepository
                .ObterQuerySplitsPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, listaIdPessoaProfissional)
                .GroupBy(s => s.PessoaQueRecebeuOuPagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(s => s.Valor)))
                .ToList();
        }

        #endregion

        #region Venda de Produtos

        public List<KeyValuePair<int, decimal>> ListarVendaDeProdutosAbertasAgrupadosPorProfissional(int idEstabelecimento, int idPessoaEstabelecimento, DateTime dataInicio, DateTime dataFim, List<int> listaIdPessoaProfissional)
        {
            var vendaProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ObterQueryVendaDeProdutosParaProfissionalAbertas(idEstabelecimento, dataInicio, dataFim, idPessoaEstabelecimento);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                vendaProduto = vendaProduto.Where(vp => listaIdPessoaProfissional.Contains(vp.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa));
            }

            return vendaProduto
                .GroupBy(tp => tp.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(vpr => vpr.Valor)))
                .ToList();
        }

        public List<KeyValuePair<int, decimal>> ListarVendaDeProdutosPagasAgrupadosPorProfissional(FiltroRelatorioPagamentoProfissional filtro, List<int> listaIdPessoaProfissional)
        {
            var vendaProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ObterQueryVendaDeProdutosParaProfissionalPagas(filtro.IdEstabelecimento, filtro.DataInicio, filtro.DataFim, filtro.IdPessoaEstabelecimento);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                vendaProduto = vendaProduto.Where(vp => listaIdPessoaProfissional.Contains(vp.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa));
            }

            return vendaProduto
                .GroupBy(vp => vp.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa)
                .Select(kv => new KeyValuePair<int, decimal>(kv.Key, kv.Sum(vpr => vpr.Valor)))
                .ToList();
        }

        #endregion

        public List<ListarProfissionaisPorEstabelecimentoDTO> ListarProfissionaisPorEstabelecimento(int idEstabelecimento, List<int> idsProfissionais)
        {
            var listaProfissionais =
                ObterQueryProfissionaisPorEstabelecimentoDTO(idEstabelecimento)
                .ToList();

            PreencherChavePixProfissional(listaProfissionais);

            if (idsProfissionais != null && idsProfissionais.Any())
            {
                return listaProfissionais.Where(p => idsProfissionais.Contains(p.IdProfissional))
                    .ToList();
            }

            return listaProfissionais.ToList();
        }

        private List<ListarProfissionaisPorEstabelecimentoDTO> ObterListaProfissionaisPorEstabelecimento(FiltroRelatorioPagamentoProfissional filtro)
        {
            var queryProfissionais = ObterQueryProfissionaisPorEstabelecimentoDTO(filtro.IdEstabelecimento);

            queryProfissionais = OrdenarListaDeProfissionais(filtro, queryProfissionais);
            queryProfissionais = AplicarFiltro(queryProfissionais, filtro.IdsProfissionais);

            filtro.Paginacao.TotalItens = queryProfissionais.Count();
            queryProfissionais = AplicarPaginacao(queryProfissionais, filtro.Paginacao.RegistroInicial, filtro.Paginacao.RegistrosPorPagina);

            var listaProfissionais = queryProfissionais.ToList();

            PreencherChavePixProfissional(listaProfissionais);

            return listaProfissionais;
        }

        private void PreencherChavePixProfissional(List<ListarProfissionaisPorEstabelecimentoDTO> listaProfissionais)
        {
            var estabelecimentoProfissionalIds = listaProfissionais.Select(lp => lp.IdEstabelecimentoProfissional).ToList();

            var chavesPixProfissional = Domain.ContaDigital.ChavePixProfissionalRepository.ListarKeyValueIdProfissonalIdChavePix(estabelecimentoProfissionalIds);

            foreach (var profissional in listaProfissionais)
            {
                var idChavePix = chavesPixProfissional.Where(c => c.Key == profissional.IdProfissional).Select(c => c.Value).FirstOrDefault();
                profissional.IdChavePixProfissional = idChavePix;
            }
        }

        private IQueryable<ListarProfissionaisPorEstabelecimentoDTO> OrdenarListaDeProfissionais(FiltroRelatorioPagamentoProfissional filtro, IQueryable<ListarProfissionaisPorEstabelecimentoDTO> query)
        {
            switch (filtro.OrdenarPor)
            {
                case FiltroRelatorioPagamentoProfissional.OrdernarPagamentoProfissional.Profissional:
                    query = query.OrderByDescending(p => p.Ativo).ThenBy(p => p.NomeProfissional);
                    break;
            }

            return query;
        }

        private IQueryable<T> AplicarPaginacao<T>(IQueryable<T> query, int registroInicial, int registrosPorPagina)
        {
            query = query.Skip(registroInicial - 1).Take(registrosPorPagina);
            return query;
        }

        private IQueryable<ListarProfissionaisPorEstabelecimentoDTO> AplicarFiltro(IQueryable<ListarProfissionaisPorEstabelecimentoDTO> query, List<int> idsProfissionais)
        {
            if (idsProfissionais != null && idsProfissionais.Any())
            {
                return query.Where(p => idsProfissionais.Contains(p.IdProfissional));
            }

            return query;
        }

        private IQueryable<ListarProfissionaisPorEstabelecimentoDTO> ObterQueryProfissionaisPorEstabelecimentoDTO(int idEstabelecimento)
        {
            return
                Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterQueryPorEstabelecimento(idEstabelecimento)
                .Select(l => new ListarProfissionaisPorEstabelecimentoDTO
                {
                    IdProfissional = l.Profissional.IdProfissional,
                    NomeProfissional = (l.Profissional.PessoaFisica.Apelido ?? l.Profissional.PessoaFisica.NomeCompleto) + (l.Ativo ? string.Empty : " [Inativo]"),
                    IdPessoaProfissional = l.Profissional.PessoaFisica.IdPessoa,
                    IdChavePixProfissional = (int?)null,
                    IdEstabelecimentoProfissional = l.Codigo,
                    Ativo = l.Ativo,
                });
        }
    }
}
