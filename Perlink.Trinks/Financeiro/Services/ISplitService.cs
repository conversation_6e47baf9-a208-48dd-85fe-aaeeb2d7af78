﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas;
using System;
using System.Threading.Tasks;

namespace Perlink.Trinks.Financeiro.Services
{

    public interface ISplitService : IService
    {
        void GerarRegistroDeSplitDePagamento(int idEstabelecimento);

        void GerarRegistroDeSplitDePagamentoSeNecessario(Transacao transacao);

        void RemoverTaxaOperadoraPosSplit(Transacao transacao);

        void GerarRegistroDeSplitDePagamentoSeNecessario(HorarioTransacao horarioTransacao);

        decimal ObterTaxaDaSubAdquirente(decimal descontoOperadora, decimal valorPago, DateTime dataHoraVencimento, EstabelecimentoConfiguracaoPOS configuracaoPOS);

        decimal ObterValorSplitPorParcela(TransacaoFormaPagamento transacaoFormaPagamentoPOS, Comissao comissao);

        decimal ObterValorSplit(TransacaoFormaPagamento transacaoFormaPagamentoPOS, Comissao comissao);

        void DesfazerSplit(int idTransacao);
    }
}