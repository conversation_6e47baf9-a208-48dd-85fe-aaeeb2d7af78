﻿using Castle.ActiveRecord;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using System;

namespace Perlink.Trinks.Financeiro
{
    [ActiveRecord("Forma_Pagamento", UseAutoImport = false, Lazy = true)]
    [Serializable]
    public class FormaPagamento : ActiveRecordBase<FormaPagamento>
    {

        public FormaPagamento()
        {
            UtilizadoEmPagamento = true;
            UtilizadoEmPagamento = true;
            TemControleOperadora = false;
            Ativo = true;
        }

        public FormaPagamento(int id)
        {
            Id = id;
            Refresh();
        }

        [Property("aceita_parcelamento")]
        public virtual Boolean AceitaParcelamento { get; set; }

        [Property("ativo")]
        public virtual Boolean Ativo { get; set; }

        [Property("aparece_no_hostite", ColumnType = "Boolean")]
        public virtual bool ApareceHotSite { get; set; }

        [Property("dias_para_receber_da_operadora")]
        public virtual Int32 DiasParaReceberDaOperadora { get; set; }

        [Property("exibir_transacao_paga_nos_relatorios")]
        public virtual Boolean ExibirTransacoesPagasNosRelatorios { get; set; }

        [PrimaryKey(PrimaryKeyType.Native, "id_forma_pagamento", ColumnType = "Int32")]
        public virtual Int32 Id { get; set; }

        [Property("maximo_de_parcelas")]
        public virtual int MaximoDeParcelas { get; set; }

        [Property("nm_forma_pagamento")]
        public virtual String Nome { get; set; }

        [Property("observacao")]
        public virtual String Observacao { get; set; }

        [Property("percentual_cobrado_pela_operadora")]
        public virtual Decimal PercentualCobradoPeloOperadora { get; set; }

        [Property("tem_controle_operadora")]
        public virtual Boolean TemControleOperadora { get; set; }

        [BelongsTo("id_forma_pagamento_tipo", Lazy = FetchWhen.OnInvoke)]
        public virtual FormaPagamentoTipo Tipo { get; set; }

        [Property("utilizada_em_pagamento")]
        public virtual Boolean UtilizadoEmPagamento { get; set; }

        [BelongsTo("id_tipo_pos", Lazy = FetchWhen.OnInvoke)]
        public virtual TipoPOS TipoPOS { get; set; }

        [Property("utilizada_em_recebimento")]
        public virtual Boolean UtilizadoEmRecebimento { get; set; }

        [Property("id_forma_pagamento_nfe")]
        public virtual Int32? IdFormaPagamentoNFe { get; set; }

        public virtual Int32 IdAdministradoraDeCartaoDeCredito { get; set; }

        [Property("id_forma_pagamento_cfe")]
        public virtual int CodigoFixoSat { get; set; }

        [Property("brand_id")]
        public virtual Int32? BrandId { get; set; }

        [Property("texto_do_tooltip_no_fechamento_de_conta")]
        public virtual string TextoDoTooltipNoFechamentoDeConta { get; set; }

        public static implicit operator FormaPagamento(FormaPagamentoEnum status)
        {
            return new FormaPagamento { Id = (int)status };
        }

        public static implicit operator FormaPagamentoEnum(FormaPagamento status)
        {
            return (FormaPagamentoEnum)status.Id;
        }

        #region Métodos públicos

        public virtual bool TemTaxasConfiguradasPeloTrinks()
        {
            return Id == (int)FormaPagamentoEnum.PagamentoOnline
                || Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnline
                || Id == (int)FormaPagamentoEnum.PagamentoOnlinePorLink
                || Id == (int)FormaPagamentoEnum.ClubeDeAssinaturaPorLink
                || Id == (int)FormaPagamentoEnum.PagamentoOnlineHotsite
                || Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite
                || Id == (int)FormaPagamentoEnum.PagarmeCredito
                || Id == (int)FormaPagamentoEnum.PagarmePix;
        }

        public virtual bool PermiteAssociarContaFinanceiraDaConciliacao()
        {
            return Tipo != Pessoas.Enums.FormaPagamentoTipoEnum.PrePago
                && Tipo != Pessoas.Enums.FormaPagamentoTipoEnum.PagamentoEmDebito;
        }

        public virtual MetodoDePagamentoNoGatewayEnum ToMetodoDePagamentoNoGatewayEnum()
        {
            switch (Id)
            {
                case (int)FormaPagamentoEnum.PagarmePix:
                    return MetodoDePagamentoNoGatewayEnum.Pix;
                default:
                    return MetodoDePagamentoNoGatewayEnum.CartaoDeCredito;
            }
        }

        #endregion
    }
}