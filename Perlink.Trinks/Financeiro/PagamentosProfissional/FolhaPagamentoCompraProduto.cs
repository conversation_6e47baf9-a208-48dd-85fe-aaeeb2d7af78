﻿using Castle.ActiveRecord;
using Perlink.Trinks.Vendas;

namespace Perlink.Trinks.Financeiro
{
    [ActiveRecord(DiscriminatorValue = "VEND")]
    public class FolhaPagamentoCompraProduto : FolhaPagamentoItem
    {
        [BelongsTo("id_transacao_forma_pagamento_parcela", NotNull = false, Cascade = CascadeEnum.All)]
        public TransacaoFormaPagamentoParcela TransacaoFormaPagamentoParcela { get; set; }

        public FolhaPagamentoCompraProduto() { }

        public FolhaPagamentoCompraProduto(FechamentoFolhaMesProfissional folhaProfissional, TransacaoFormaPagamentoParcela transacaoFormaPagamentoParcela)
        {
            FechamentoFolhaMesProfissional = folhaProfissional;
            TransacaoFormaPagamentoParcela = transacaoFormaPagamentoParcela;
            Valor = transacaoFormaPagamentoParcela.Valor;
            Ativo = true;
        }
    }
}
