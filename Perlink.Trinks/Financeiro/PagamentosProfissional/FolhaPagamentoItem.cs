﻿using Castle.ActiveRecord;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Perlink.Trinks.Financeiro
{

    [ActiveRecord("Folha_Pagamento_Item", DiscriminatorColumn = "tipo", DiscriminatorType = "String", DiscriminatorValue = "ITEM", Lazy = true)]
    public class FolhaPagamentoItem : ActiveRecordBase<FolhaPagamentoItem>
    {

        public FolhaPagamentoItem() { }

        [PrimaryKey(PrimaryKeyType.Native, "id", ColumnType = "Int32")]
        public virtual int Id { get; set; }

        [BelongsTo("id_estabelecimento_folha_profissional")]
        public virtual FechamentoFolhaMesProfissional FechamentoFolhaMesProfissional { get; set; }

        [Property("valor")]
        public virtual decimal Valor { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }
    }
}
