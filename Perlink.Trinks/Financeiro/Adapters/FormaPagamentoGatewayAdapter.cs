﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Financeiro.Enums;

namespace Perlink.Trinks.Financeiro.Adapters
{
    internal static class FormaPagamentoGatewayAdapter
    {
        public static FormaPagamentoEnum ToFormaPagamentoEnum(this MetodoDePagamentoNoGatewayEnum metodo)
        {
            switch (metodo)
            {
                case MetodoDePagamentoNoGatewayEnum.Pix:
                    return FormaPagamentoEnum.PagarmePix;
                case MetodoDePagamentoNoGatewayEnum.CartaoDeCredito:
                default:
                    return FormaPagamentoEnum.PagarmeCredito;
            }
        }
    }
}