﻿using MassTransit.Saga;
using NHibernate.Linq;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.ExtensionMethods;
using Perlink.Trinks.Pacotes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.RPS;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Vendas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Perlink.Trinks.Financeiro.Repositories
{

    public partial class TransacaoRepository : ITransacaoRepository
    {

        public DateTime? ObterDataUltimoFechamentoDeContaRealizado(int idPessoaJuridica)
        {
            return Queryable().Where(p => p.PessoaQueRecebeu.IdPessoa == idPessoaJuridica &&
                                          p.TransacaoQueEstounouEsta == null &&
                                          p.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento)
                              .OrderByDescending(p => p.DataHora)
                              .Select(p => (DateTime?)p.DataHora)
                              .FirstOrDefault();
        }

        public IQueryable<Transacao> ObterPorEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(f =>
                f.Ativo &&
                f.HorariosTransacoes.Any(g => g.Horario.Estabelecimento.IdEstabelecimento == idEstabelecimento));
        }

        public IQueryable<Transacao> ObterPorEstabelecimentoNoPeriodo(int idPessoaJuridica, DateTime dataInicio, DateTime dataFim)
        {
            var dtInicio = new DateTime(dataInicio.Year, dataInicio.Month, dataInicio.Day, 0, 0, 0);
            var dtFim = new DateTime(dataFim.Year, dataFim.Month, dataFim.Day, 23, 59, 59);

            return Queryable().Where(p => p.Ativo && p.PessoaQueRecebeu.IdPessoa == idPessoaJuridica && p.DataHora >= dtInicio && p.DataHora <= dtFim);
        }

        public Transacao ObterTransacaoPorResponsavelEstabelecimentoECliente(int idResponsavelEstabelecimento, int idCliente)
        {
            return Queryable()
                            .Where(q => q.Ativo && q.PessoaQueRecebeu.IdPessoa == idResponsavelEstabelecimento && q.PessoaQuePagou.IdPessoa == idCliente)
                            .OrderByDescending(q => q.DataHora)
                            .FirstOrDefault();
        }
        public IQueryable<Transacao> TransacoesParaRelatorioLotesRPS(ParametrosFiltrosLotesRPS filtro)
        {
            var query = Queryable();

            query = FiltroTransacao(query, filtro.IdsTransacao);

            int idPessoaJuridica = ObterIdPessoaJuridicaDaTransacao(filtro, ref query);

            bool aplicarFiltroEmissoesRPS = false;
            var filtroDeEmissoesRPS = Domain.RPS.EmissaoRPSRepository.Queryable().Where(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica);
            if (filtro.NumeroRPS.HasValue || filtro.NumeroLote.HasValue)
            {
                if (filtro.NumeroLote.HasValue)
                {
                    aplicarFiltroEmissoesRPS = true;
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.Lote == filtro.NumeroLote.Value);
                }
                if (filtro.NumeroRPS.HasValue)
                {
                    aplicarFiltroEmissoesRPS = true;
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.Numero == filtro.NumeroRPS.Value);
                }
            }
            else
            {
                if (filtro.TipoData == TipoDataRelatorio.DataTransacao)
                {
                    if (filtro.DataInicial.HasValue)
                        query = query.Where(f => f.DataHora >= filtro.DataInicial.Value.Date);

                    if (filtro.DataFinal.HasValue)
                        query = query.Where(f => f.DataHora < filtro.DataFinal.Value.Date.AddDays(1));
                }
                else if (filtro.TipoData == TipoDataRelatorio.DataGeracaoLoteRPS)
                {
                    if (filtro.DataInicial.HasValue)
                    {
                        aplicarFiltroEmissoesRPS = true;
                        var emissoesMaxData = Domain.RPS.EmissaoRPSRepository.Queryable();
                        filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.DataEmissao == (emissoesMaxData.Where(g => g.Transacao == e.Transacao)
                            .Max(g => g.DataEmissao)) && e.DataEmissao >= filtro.DataInicial.Value.Date);
                    }
                    if (filtro.DataFinal.HasValue)
                    {
                        aplicarFiltroEmissoesRPS = true;
                        var emissoesMaxData = Domain.RPS.EmissaoRPSRepository.Queryable();
                        filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.DataEmissao == (emissoesMaxData.Where(g => g.Transacao == e.Transacao)
                            .Max(g => g.DataEmissao)) && e.DataEmissao < filtro.DataFinal.Value.Date.AddDays(1));
                    }
                }

                query = FiltroTipoPagamento(query);

                var buscaDadosRPS = Domain.RPS.DadosRPSTransacaoRepository.Queryable();
                query = query.Where(f => f.TransacaoQueEstounouEsta == null
                                            || buscaDadosRPS.Any(d => d.StatusRPS != StatusRpsEnum.NaoEmitido
                                                                        && d.Transacao.Id == f.Id
                                                                        && d.PessoaJuridica.IdPessoa == idPessoaJuridica));

                query = FiltroFormaPagamentoPositiva(query, filtro.IdsTipoFormaPagamento);

                if (filtro.ExibicaoDeEmissaoManual.HasValue)
                {
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.DadosRPSTransacao.EmissaoManual == filtro.ExibicaoDeEmissaoManual.Value);
                }

                query = FiltroPagamentoTipo(query, filtro.IdFormaPagamentoTipo);
                query = FiltroStatusRPS(query, filtro.StatusRPSSelecionados, idPessoaJuridica);
            }

            if (filtro.IdsPessoasDosProfissionais.Any())
            {
                query = query.Where(f => f.HorariosTransacoes.Any(g => filtro.IdsPessoasDosProfissionais.Contains(g.Comissao.PessoaComissionada.IdPessoa) || filtro.IdsPessoasDosProfissionais.Contains(g.ComissaoAssistente.PessoaComissionada.IdPessoa)));
                //idsPessoasDosProfissionaisParceiros.Contains(h.Comissao.PessoaComissionada.IdPessoa)
            }

            if (aplicarFiltroEmissoesRPS)
                query = query.Where(t => filtroDeEmissoesRPS.Select(e => e.Transacao).Contains(t));

            return query;
        }

        public int ObterIdPagamentoPeloIdTransacao(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Queryable();
            var transacaoItem = Domain.Financeiro.TransacaoItemRepository.Queryable();

            return (from tran in transacao
                    join tItem in transacaoItem on tran.Id equals tItem.Transacao.Id
                    where tran.Id == idTransacao && tItem.Tipo == TransacaoItemTipo.PagamentoDeAssinatura
                    select tItem.IdObjetoReferencia)
                    .FirstOrDefault();
        }

        public IQueryable<Transacao> TransacoesParaRelatorioCotaParteParceiro(ParametrosFiltrosLotesRPS filtro)
        {
            var query = Queryable();
            //query = query.Fetch(f => f.DadosRPS);

            query = FiltroTransacao(query, filtro.IdsTransacao);

            int idPessoaJuridica = ObterIdPessoaJuridicaDaTransacao(filtro, ref query);

            bool aplicarFiltroEmissoesRPS = false;
            var filtroDeEmissoesRPS = Domain.RPS.EmissaoRPSRepository.Queryable().Where(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica);
            if (filtro.NumeroRPS.HasValue || filtro.NumeroLote.HasValue)
            {
                if (filtro.NumeroLote.HasValue)
                {
                    aplicarFiltroEmissoesRPS = true;
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.Lote == filtro.NumeroLote.Value);
                }
                if (filtro.NumeroRPS.HasValue)
                {
                    aplicarFiltroEmissoesRPS = true;
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.Numero == filtro.NumeroRPS.Value);
                }
            }
            else
            {
                if (filtro.TipoData == TipoDataRelatorio.DataTransacao)
                {
                    if (filtro.DataInicial.HasValue)
                        query = query.Where(f => f.DataHora >= filtro.DataInicial.Value.Date);

                    if (filtro.DataFinal.HasValue)
                        query = query.Where(f => f.DataHora < filtro.DataFinal.Value.Date.AddDays(1));
                }
                else if (filtro.TipoData == TipoDataRelatorio.DataGeracaoLoteRPS)
                {
                    if (filtro.DataInicial.HasValue)
                    {
                        aplicarFiltroEmissoesRPS = true;
                        var emissoesMaxData = Domain.RPS.EmissaoRPSRepository.Queryable();
                        filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.DataEmissao == (emissoesMaxData.Where(g => g.Transacao == e.Transacao)
                            .Max(g => g.DataEmissao)) && e.DataEmissao >= filtro.DataInicial.Value.Date);
                    }
                    if (filtro.DataFinal.HasValue)
                    {
                        aplicarFiltroEmissoesRPS = true;
                        var emissoesMaxData = Domain.RPS.EmissaoRPSRepository.Queryable();
                        filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.DataEmissao == (emissoesMaxData.Where(g => g.Transacao == e.Transacao)
                            .Max(g => g.DataEmissao)) && e.DataEmissao < filtro.DataFinal.Value.Date.AddDays(1));
                    }
                }

                query = FiltroTipoPagamento(query);

                var buscaDadosRPS = Domain.RPS.DadosRPSTransacaoRepository.Queryable();
                query = query.Where(f => f.TransacaoQueEstounouEsta == null
                                            || buscaDadosRPS.Any(d => d.StatusRPS != StatusRpsEnum.NaoEmitido
                                                                        && d.Transacao.Id == f.Id
                                                                        && d.PessoaJuridica.IdPessoa == idPessoaJuridica));

                query = FiltroFormaPagamentoPositiva(query, filtro.IdsTipoFormaPagamento);

                if (filtro.ExibicaoDeEmissaoManual.HasValue)
                {
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.DadosRPSTransacao.EmissaoManual == filtro.ExibicaoDeEmissaoManual.Value);
                }

                query = FiltroPagamentoTipo(query, filtro.IdFormaPagamentoTipo);
                query = FiltroStatusRPS(query, filtro.StatusRPSSelecionados, filtro.Estabelecimento.PessoaJuridica.IdPessoaJuridica);
            }

            if (aplicarFiltroEmissoesRPS)
                query = query.Where(t => filtroDeEmissoesRPS.Select(e => e.Transacao).Contains(t));

            return query;
        }

        public IQueryable<Transacao> TransacoesParaRelatorioNFC(ParametrosFiltrosLotesRPS filtro)
        {
            var query = Queryable();
            //query = query.Fetch(f => f.DadosRPS);

            query = FiltroTransacao(query, filtro.IdsTransacao);

            int idPessoaJuridica = ObterIdPessoaJuridicaDaTransacao(filtro, ref query);

            bool aplicarFiltroEmissoesRPS = false;
            var filtroDeEmissoesRPS = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Queryable().Where(e => e.Estabelecimento.PessoaJuridica.IdPessoa == idPessoaJuridica);
            if (filtro.NumeroRPS.HasValue)
            {
                if (filtro.NumeroRPS.HasValue)
                {
                    aplicarFiltroEmissoesRPS = true;
                    filtroDeEmissoesRPS = filtroDeEmissoesRPS.Where(e => e.NumeroNota == filtro.NumeroRPS.Value);
                }
            }
            else
            {
                if (filtro.TipoData == TipoDataRelatorio.DataTransacao)
                {
                    if (filtro.DataInicial.HasValue)
                        query = query.Where(f => f.DataHora >= filtro.DataInicial.Value.Date);

                    if (filtro.DataFinal.HasValue)
                        query = query.Where(f => f.DataHora < filtro.DataFinal.Value.Date.AddDays(1));
                }

                query = FiltroFormaPagamentoPositiva(query, filtro.IdsTipoFormaPagamento);
                query = FiltroTipoPagamento(query);
                query = FiltroPagamentoTipo(query, filtro.IdFormaPagamentoTipo);

                var buscaDadosNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Queryable();
                if (!filtro.StatusRPSSelecionados.Contains(StatusRpsEnum.NaoEmitido))
                {
                    query = query.Where(f => buscaDadosNFC.Any(d => d.Transacao.Id == f.Id));
                }

                query = query.Where(f => f.TransacaoQueEstounouEsta == null);

                //query = FiltroFormaPagamentoPositiva(query, filtro.IdsTipoFormaPagamento);

                //query = FiltroPagamentoTipo(query, filtro.IdFormaPagamentoTipo);
                //query = FiltroStatusRPS(query, filtro.StatusRPSSelecionados, idPessoaJuridica);
            }

            return query;
        }

        private int ObterIdPessoaJuridicaDaTransacao(ParametrosFiltrosLotesRPS filtro, ref IQueryable<Transacao> query)
        {
            int idPessoaJuridica = 0;
            if (filtro.IdPessoaDaPessoaJuridica > 0)
            {
                idPessoaJuridica = filtro.IdPessoaDaPessoaJuridica;
                query = FiltroPessoaJuridica(query, filtro.IdPessoaDaPessoaJuridica, filtro.Estabelecimento.PessoaJuridica.IdPessoa);
            }
            else
            {
                idPessoaJuridica = filtro.Estabelecimento.PessoaJuridica.IdPessoa;
                query = FiltroEstabelecimento(query, filtro.Estabelecimento);
            }

            return idPessoaJuridica;
        }

        public Estabelecimento ObterEstabelecimentoDaTransacao(Transacao transacao)
        {
            return Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
        }

        public bool TransacaoEhDoEstabelecimentoLogado(int idTransacao, int idEstabelecimento)
        {
            var transacao = Queryable().Where(t => t.Id == idTransacao).FirstOrDefault();
            var estabelecimentoDaTransacao = ObterEstabelecimentoDaTransacao(transacao);
            return idEstabelecimento == estabelecimentoDaTransacao.IdEstabelecimento ? true : false;
        }

        #region Relatório Financeiro

        public IList<RelatorioTransacao> ObterTransacoesParaPrimeiraLinhaDoRelatorio(
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return ObterTransacoesParaPrimeiraLinhaDoRelatorioPorDataTransacao(parametros);

            return ObterTransacoesParaPrimeiraLinhaDoRelatorioPorDataAtendimento(parametros);
        }

        public IList<RelatorioTransacao> ObterTransacoesParaSegundaLinhaDoRelatorio(
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return ObterTransacoesParaSegundaLinhaDoRelatorioPorDataTransacao(parametros);

            return ObterTransacoesParaSegundaLinhaDoRelatorioPorDataAtendimento(parametros);
        }

        public IList<Transacao> ObterTransacoesParaTerceiraLinhaDoRelatorio(ParametrosFiltrosRelatorio parametros)
        {
            var retorno = FiltrarTransacoes(parametros);

            retorno = retorno.OrderBy(p => p.DataHora);
            retorno = retorno.FetchMany(p => p.HorariosTransacoes);
            retorno = retorno.Fetch(p => p.PessoaQuePagou);

            return retorno.ToList();
        }

        public IQueryable<Transacao> FiltrarTransacoes(ParametrosFiltrosRelatorio parametros)
        {
            DateTime? dtInicio = null;
            DateTime? dtFim = null;

            if (parametros.DataInicial.HasValue)
                dtInicio = new DateTime(parametros.DataInicial.Value.Year, parametros.DataInicial.Value.Month,
                    parametros.DataInicial.Value.Day, 0, 0, 0);

            if (parametros.DataFinal.HasValue)
                dtFim = new DateTime(parametros.DataFinal.Value.Year, parametros.DataFinal.Value.Month,
                    parametros.DataFinal.Value.Day, 23, 59, 59);

            IQueryable<Transacao> retorno = null;

            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
            {
                retorno = Queryable()
                    .Where(
                        p => p.Ativo &&
                             p.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa);

                if (dtInicio.HasValue && !dtFim.HasValue)
                    retorno = retorno.Where(p => p.DataHora >= dtInicio);
                else if (!dtInicio.HasValue && dtFim.HasValue)
                    retorno = retorno.Where(p => p.DataHora <= dtFim);
                else if (dtInicio.HasValue && dtFim.HasValue)
                    retorno = retorno.Where(p => p.DataHora >= dtInicio && p.DataHora <= dtFim);
            }
            else
            {
                if (dtInicio.HasValue && !dtFim.HasValue)
                {
                    retorno = Queryable()
                        .Where(
                            p => p.Ativo
                                 && p.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa
                                 && p.DataReferencia >= dtInicio);
                }
                else if (!dtInicio.HasValue && dtFim.HasValue)
                {
                    retorno = Queryable()
                        .Where(
                            p => p.Ativo
                                 && p.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa
                                 && p.DataReferencia <= dtFim);
                }
                else if (dtInicio.HasValue && dtFim.HasValue)
                {
                    retorno = Queryable()
                        .Where(
                            p => p.Ativo
                                 && p.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa
                                 && p.DataReferencia >= dtInicio
                                 && p.DataReferencia <= dtFim);
                }
                else if (!dtInicio.HasValue && !dtFim.HasValue)
                {
                    retorno = Queryable()
                        .Where(
                            p => p.Ativo
                                 && p.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa);
                }
            }

            if (!parametros.ExibirEstornos)
                retorno = retorno.Where(f => f.TransacaoQueEstounouEsta == null && f.TipoTransacao.Id == 1);

            retorno = AdicionarCriterioTipoFiltroTransacaoProduto(retorno, parametros);
            retorno = AdicionarCriterioFormaPagamentoDiferenteDescontoFornecedor(retorno);

            if (parametros.ExibirApenasComDescontos)
            {
                var queryableVendas = Domain.Vendas.VendaRepository.Queryable().Where(p => p.ItensVenda.Any(p2 => p2.Desconto != 0));
                retorno = retorno.Where(f => f.HorariosTransacoes.Any(ht => ht.Desconto.HasValue && ht.Desconto.Value != 0) || queryableVendas.Any(v => v.Transacao.Id == f.Id));
            }

            if (parametros.ExibirApenasTransacoesComDescontoCashback)
            {
                var queryableCashback = Domain.Cashback.CashbackTransacaoRepository.Queryable();
                retorno = (from tr in retorno
                           where (queryableCashback.Where(cashback => cashback.IdTransacao == tr.Id)
                         .Sum(cashb => (decimal?)cashb.Valor) ?? 0) != 0
                           select tr);
            }

            if (parametros.IdMotivoDesconto > 0)
            {
                var queryableVendas = Domain.Vendas.VendaRepository.Queryable().Where(p => p.ItensVenda.Any(p2 => p2.MotivoDesconto.Id == parametros.IdMotivoDesconto));
                retorno = retorno.Where(f => f.HorariosTransacoes.Any(ht => ht.MotivoDesconto.Id == parametros.IdMotivoDesconto) || queryableVendas.Any(v => v.Transacao.Id == f.Id));
            }

            if (parametros.NumeroComanda.HasValue && parametros.NumeroComanda.Value > 0)
            {
                var queryItemVendaProduto = Domain.Vendas.ItemVendaProdutoRepository.Queryable();
                queryItemVendaProduto = queryItemVendaProduto.Where(a => a.PreVenda != null);
                queryItemVendaProduto = queryItemVendaProduto.Where(a => a.PreVenda.Comanda != null);
                queryItemVendaProduto = queryItemVendaProduto.Where(a => a.PreVenda.Comanda.Numero == parametros.NumeroComanda.Value);

                //var retornoProdutos = queryItemVendaProduto.Select(a => a.Venda.Transacao.Id).ToList();

                var queryServicos = Domain.Vendas.PreVendaServicoRepository.Queryable();
                queryServicos = queryServicos.Where(a => a.Comanda != null);
                queryServicos = queryServicos.Where(a => a.Comanda.Numero == parametros.NumeroComanda.Value);

                //var retornoServicos = queryServicos.SelectMany(a => a.Horario.HorariosTransacoes, (a, b) => new { Id = b.Transacao.Id }).Select(a => new KeyValuePair<Int32, Int32>(a.Id, a.Numero)).ToList();
                //retornoServicos.AddRange(retornoProdutos);
                retorno = retorno.Where(f => queryItemVendaProduto.Any(a => a.Venda.Transacao.Id == f.Id) || queryServicos.Any(a => a.Horario.HorariosTransacoes.Any(b => b.Transacao.Id == f.Id)));
            }

            return retorno;
        }

        public int ObterQuantidadeDeClientesDistintos(ParametrosFiltrosRelatorio parametros)
        {
            return AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros).Select(f => f.PessoaQuePagou).Distinct().Count();
        }

        public FechamentosDiaDTO ObterResumoDosFechamentosDoDia(ParametrosFiltrosRelatorio parametros)
        {
            var transacoesQueryable = FiltrarTransacoes(parametros);

            FechamentosDiaDTO resumo = new FechamentosDiaDTO();

            var transacoes = transacoesQueryable.GroupBy(f => f.PessoaQueRecebeu).Select(f => new
            {
                TotalProdutos = f.Sum(g => g.TotalProdutos),
                TotalServicos = f.Sum(g => g.TotalServicos),
                TotalPacotes = f.Sum(g => g.TotalPacotes),
                TotalClubeDeAssinaturas = f.Sum(g => g.TotalClubeDeAssinaturas),
                TotalValePresente = f.Sum(g => (decimal?)g.TotalValePresente),
                Troco = f.Sum(g => g.Troco),
                Descontos = f.Sum(g => g.Descontos),
                TotalPagoEmDinheiro = f.Sum(g => g.TotalPagoEmDinheiro)
            }).FirstOrDefault();

            var idsTransacao = transacoesQueryable.Select(p => p.Id).ToList();
            var transacoesLamentoFinanceiro = Domain.Financeiro.TransacaoLancamentoFinanceiroRepository.Queryable().Where(p => idsTransacao.Contains(p.Transacao.Id));
            var valorSplitPagamento = transacoesLamentoFinanceiro.Sum(p => (decimal?)p.Lancamento.Valor);

            if (transacoes == null)
                return resumo;

            if (parametros.Estabelecimento.EstabelecimentoConfiguracaoGeral.JaTeveCaixaPorProfissionalAberto)
                resumo.TeveRegistroDeCaixaNaDataDoResumo = Domain.Financeiro.RegistroDeCaixaPorOperadorRepository.HouveRegistroDeCaixaNaData(parametros.DataInicial.Value, parametros.Estabelecimento.PessoaJuridica.IdPessoa);
            else
                resumo.TeveRegistroDeCaixaNaDataDoResumo = false;

            resumo.EstabelecimentoJaTrabalhouComPagamentoOnline = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.EstabelecimentoJaTrabalhouComPagamentoOnline(parametros.Estabelecimento);

            resumo.ValorProdutos = transacoes.TotalProdutos ?? 0;
            resumo.ValorServicos = transacoes.TotalServicos ?? 0;
            resumo.ValorPacotes = transacoes.TotalPacotes ?? 0;
            resumo.ValorClubeDeAssinaturas = transacoes.TotalClubeDeAssinaturas ?? 0;
            resumo.ValorValesPresenteComprados = transacoes.TotalValePresente ?? 0;
            resumo.ValorDescontos = transacoes.Descontos ?? 0;
            resumo.ValorTroco = transacoes.Troco ?? 0;
            resumo.ValorSplitPagamento = valorSplitPagamento ?? 0;

            var idPessoaEstabelecimento = parametros.Estabelecimento.PessoaJuridica.IdPessoa;
            var diaDoResumo = parametros.DataInicial.Value;
            var primeiroDiaDoMesAtual = new DateTime(diaDoResumo.Year, diaDoResumo.Month, 1);

            resumo.ValorTotalMesAno = ObterValorTotalAcumuladoNoPeriodo(idPessoaEstabelecimento, primeiroDiaDoMesAtual, diaDoResumo);

            var horariosTransacoes = transacoesQueryable.SelectMany(t => t.HorariosTransacoes).ToList();
            resumo.QtdServicos = horariosTransacoes.Count();

            List<ItemVenda> itensVendas = Domain.Vendas.ItemVendaRepository.ListarVendidosNaData(parametros.Estabelecimento.PessoaJuridica.IdPessoa, parametros.DataInicial.Value);
            resumo.QtdPacotes = itensVendas.Where(i => i is ItemVendaPacote).Count();
            resumo.QtdValePresenteComprados = itensVendas.Where(i => i is ItemVendaValePresente).Count();

            List<TransacaoFormaPagamentoDTO> transacoesFormasPagamentos;
            if (resumo.TeveRegistroDeCaixaNaDataDoResumo)
            {
                transacoesFormasPagamentos = Domain.Financeiro.TransacaoFormaPagamentoRepository.ListarPagamentosNaoEstornadosComMovimentacaoEmCaixaPorData(parametros.Estabelecimento.PessoaJuridica.IdPessoa, parametros.DataInicial.Value);
                if (resumo.EstabelecimentoJaTrabalhouComPagamentoOnline)
                {
                    var transacoesFormasPagamentosDePagamentoOnline = Domain.Financeiro.TransacaoFormaPagamentoRepository.ListarPagamentosNaoEstornadosQueVenhamDePagamentoOnlineAntecipadoPorData(parametros.Estabelecimento.PessoaJuridica.IdPessoa, parametros.DataInicial.Value);
                    transacoesFormasPagamentos.AddRange(transacoesFormasPagamentosDePagamentoOnline);
                }
            }
            else
                transacoesFormasPagamentos = Domain.Financeiro.TransacaoFormaPagamentoRepository.ListarPagamentosNaoEstornadosPorData(parametros.Estabelecimento.PessoaJuridica.IdPessoa, parametros.DataInicial.Value);

            resumo.QtdClubeDeAssinaturas = transacoesFormasPagamentos.Where(f => f.FormaPagamento == FormaPagamentoEnum.ClubeDeAssinatura && f.ValorPago > 0).Distinct().Count();

            var tfpCreditosDeClientes = transacoesFormasPagamentos.Where(f => f.FormaPagamento == FormaPagamentoEnum.CreditoCliente);
            resumo.ValorCreditoClienteComprados = tfpCreditosDeClientes.Where(f => f.ValorPago < 0).Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdCreditoClienteComprados = tfpCreditosDeClientes.Where(f => f.ValorPago < 0).Count();
            resumo.ValorCreditoClienteUtilizados = tfpCreditosDeClientes.Where(f => f.ValorPago > 0).Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdCreditoClienteUtilizados = tfpCreditosDeClientes.Where(f => f.ValorPago > 0).Select(f => f.IdPessoaQuePagou).Distinct().Count();

            var tfpCreditosDePagamentoOnline = transacoesFormasPagamentos.Where(f => f.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnline);
            resumo.ValorCreditoDePagamentoOnlineComprados = tfpCreditosDePagamentoOnline.Where(f => f.ValorPago < 0).Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdCreditoDePagamentoOnlineComprados = tfpCreditosDePagamentoOnline.Where(f => f.ValorPago < 0).Count();
            resumo.ValorCreditoDePagamentoOnlineUtilizados = tfpCreditosDePagamentoOnline.Where(f => f.ValorPago > 0).Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdCreditoDePagamentoOnlineUtilizados = tfpCreditosDePagamentoOnline.Where(f => f.ValorPago > 0).Select(f => f.IdPessoaQuePagou).Distinct().Count();

            var tfpDividas = transacoesFormasPagamentos.Where(f => f.FormaPagamento == FormaPagamentoEnum.DeixarFaltaComoDivida);
            resumo.ValorPagamentosDeDividas = tfpDividas.Where(f => f.ValorPago < 0).Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdPagamentosDeDividas = tfpDividas.Where(f => f.ValorPago < 0).Count();
            resumo.ValorDividasDeixadas = tfpDividas.Where(f => f.ValorPago > 0).Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdClientesComDividasDeixadas = tfpDividas.Where(f => f.ValorPago > 0).Select(f => f.IdPessoaQuePagou).Distinct().Count();

            var tfpValesPresentesUtilizados = transacoesFormasPagamentos.Where(f => f.FormaPagamento == FormaPagamentoEnum.ValePresente);
            resumo.ValorValesPresenteUtilizados = tfpValesPresentesUtilizados.Select(f => f.ValorPago).Sum() * (-1);
            resumo.QtdValePresenteUtilizados = tfpValesPresentesUtilizados.Select(f => f.NumeroValePresente).Distinct().Count();

            var unidadesDeMedidas = itensVendas
                .OfType<ItemVendaProduto>()
                .GroupBy(ivp => new { TipoQuantidade = ivp.TipoDeQuantidade, ivp.EstabelecimentoProduto.UnidadeMedida.Simbolo })
                .Select(group => new DetalhesItemVendaProdutoDTO
                {
                    SimboloUnidadeMedida = group.Key.Simbolo,
                    TipoDeQuantidade = group.Key.TipoQuantidade,
                    QuantidadeEmUnidade = group.Select(ivp => ivp.Quantidade / ivp.EstabelecimentoProduto.MedidasPorUnidade).Sum(),
                    QuantidadeEmFracao = group.Select(ivp => ivp.Quantidade).Sum()
                })
                .ToList();

            resumo.QtdProdutos = FechamentosDiaDTO.GerarDescricaoDaQuantidadeDeProdutos(unidadesDeMedidas);

            List<KeyValuePair<string, decimal>> despesas = new List<KeyValuePair<string, decimal>>();
            despesas.AddRange(horariosTransacoes
                .Where(ht => ht.MotivoDesconto != null)
                .Select(ht => new KeyValuePair<string, decimal>(ht.MotivoDesconto.Descricao, ht.Desconto ?? 0))
                .ToList());

            despesas.AddRange(itensVendas
                .Where(iv => iv.MotivoDesconto != null)
                .Select(iv => new KeyValuePair<string, decimal>(iv.MotivoDesconto.Descricao, iv.Desconto))//(g.Key, g.Sum(iv => iv.Desconto)))
                .ToList());

            resumo.DetalhesDescontos = despesas
                .GroupBy(g => g.Key)
                .Select(g => new DetalhesDescontosDTO
                {
                    Descricao = g.Key,
                    Valor = g.Sum(d => d.Value),
                    Quantidade = g.Count()
                })
                .OrderBy(d => d.Descricao)
                .ToList();


            var totalClientesAtendidos = transacoesQueryable.Select(t => t.PessoaQuePagou.IdPessoa).Distinct().Count();

            var totalClientesComPagamento = totalClientesAtendidos > resumo.QtdClientesComDividasDeixadas
                ? totalClientesAtendidos - resumo.QtdClientesComDividasDeixadas
                : 0;

            //PreencherQuadroCaixaDoResumoDoDia(resumo, parametros, transacoes.TotalPagoEmDinheiro, transacoes.Troco);
            PreencherQuadroTotaisDoResumoDoDia(resumo, parametros, totalClientesComPagamento);
            PreencherPagamentosNosCaixas(resumo, parametros, transacoesFormasPagamentos);
            PreencherFechamentoPorProfissionalDoResumoDoDia(resumo, parametros);
            PreencherDadosDoRodizioDeProfissionais(parametros, resumo);

            return resumo;
        }

        #region Métodos Privados da Impressão do Resumo do Dia

        public decimal ObterValorTotalAcumuladoNoPeriodo(int idPessoaEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {

            var valores = Domain.Financeiro.TransacaoRepository.Queryable()
                .Where(t => t.PessoaQueRecebeu.IdPessoa == idPessoaEstabelecimento
                         && t.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento
                         && t.TransacaoQueEstounouEsta == null
                         && t.DataHora >= dataInicio
                         && t.DataHora < dataFim.AddDays(1).Date
                         && t.Ativo
                         && t.FormasPagamento.Any(p2 => p2.FormaPagamento.ExibirTransacoesPagasNosRelatorios))
                .GroupBy(t => t.PessoaQueRecebeu.IdPessoa)
                .Select(g => new
                {
                    TotalPago = g.Sum(t => t.TotalPago),
                    Troco = g.Sum(t => t.Troco),
                }).FirstOrDefault();

            return valores != null ? (valores.TotalPago ?? 0) + (valores.Troco ?? 0) : 0;
        }

        private void PreencherDadosDoRodizioDeProfissionais(ParametrosFiltrosRelatorio parametros, FechamentosDiaDTO resumo)
        {

            if (parametros.Estabelecimento.JaTrabalhouComRodizioDeProfissionais())
            {

                if (!resumo.FechamentosPorProfissional.Any())
                    return;

                var dataResumo = parametros.DataInicial ?? Calendario.Hoje();

                var qtdAgendamentosPorProfissional = Domain.RodizioDeProfissionais.AgendamentoComProfissionalDaVezStory
                    .ListarQuantidadesDeAgendamentosQueForamDaVezPorProfissionalEQueEstejamAssociadosAoProfissionalDeOrigem(parametros.Estabelecimento.IdEstabelecimento, dataResumo);

                if (qtdAgendamentosPorProfissional.Any())
                {
                    resumo.ExibeDadosDoRodizioDeProfissional = true;

                    foreach (var profissionalDoRateio in resumo.FechamentosPorProfissional)
                    {
                        int? qtdDeAgendamentosDaVez = qtdAgendamentosPorProfissional.Where(g => g.Key == profissionalDoRateio.IdProfissional).Select(g => g.Value).FirstOrDefault();
                        profissionalDoRateio.QtdDeAgendamentosDaVez = qtdDeAgendamentosDaVez ?? 0;
                    }
                }
            }
        }

        private void PreencherQuadroCaixaParaDiaSemControleDeCaixa(FechamentosDiaDTO resumo, ParametrosFiltrosRelatorio parametros, decimal? totalPagoEmDinheiro, decimal? trocoEmDinheiro)
        {
            resumo.AberturaCaixa = Domain.Financeiro.AberturaFechamentoCaixaHistoricoRepository
                .ObterValorDeAberturaDeCaixaDoDia(parametros.Estabelecimento.IdEstabelecimento, parametros.DataInicial.Value) ?? 0;

            resumo.Sangria = Domain.Financeiro.SangriaHistoricoRepository
                .ObterTotalDoDia(parametros.Estabelecimento.IdEstabelecimento, parametros.DataInicial.Value) ?? 0;

            resumo.DetalhesDespesas = Domain.Despesas.LancamentoRepository
                .ListarDespesasPagasPeloCaixa(parametros.Estabelecimento.IdEstabelecimento, parametros.DataInicial, parametros.DataFinal);

            resumo.RecebidoNoDiaEmDinheiro = totalPagoEmDinheiro ?? 0;
            resumo.TrocoEmDinheiro = trocoEmDinheiro ?? 0;
        }

        private void PreencherQuadroCaixaParaDiaComControleDeCaixa(FechamentosDiaDTO resumo, ParametrosFiltrosRelatorio parametros, decimal? totalPagoEmDinheiro, decimal? trocoEmDinheiro)
        {

            resumo.AberturaCaixa = resumo.RegistrosDeCaixas.Sum(r => r.AberturaDeCaixa);
            resumo.Sangria = resumo.RegistrosDeCaixas.Sum(r => r.Sangria);

            resumo.DetalhesDespesas = Domain.Despesas.LancamentoRepository
                .ListarDespesasPagasPeloCaixa(parametros.Estabelecimento.IdEstabelecimento, parametros.DataInicial, parametros.DataFinal);

            resumo.RecebidoNoDiaEmDinheiro = totalPagoEmDinheiro ?? 0;
            resumo.TrocoEmDinheiro = trocoEmDinheiro ?? 0;
        }

        private void PreencherQuadroTotaisDoResumoDoDia(FechamentosDiaDTO resumo, ParametrosFiltrosRelatorio parametros, int totalClientesDistintos = 0)
        {
            var preVendasProdutoDoDia = Domain.Vendas.PreVendaProdutoRepository
                                .ObterDebitosEmPreVendaNoDia(parametros.Estabelecimento.IdEstabelecimento, parametros.DataInicial.Value)
                                .Select(p => new { IdPessoaComprador = (int?)p.PessoaFisicaComprador.IdPessoa, p.Subtotal })
                                .ToList();

            resumo.ValorTotalProdutosEmDebito = preVendasProdutoDoDia.Any() ? preVendasProdutoDoDia.Select(pv => pv.Subtotal).Sum() : 0;

            List<Horario> horariosDoDia = Domain.Pessoas.HorarioRepository.ListarTodosDoDia(parametros.Estabelecimento.IdEstabelecimento, parametros.DataInicial.Value);

            //Agendamentos do Dia
            resumo.TotalAgendamentos.AguardandoConfirmacao = horariosDoDia.Where(a => a.Status == StatusHorarioEnum.Aguardando_Confirmacao).Count();
            resumo.TotalAgendamentos.Confirmados = horariosDoDia.Where(a => a.Status == StatusHorarioEnum.Confirmado).Count();
            resumo.TotalAgendamentos.EmAtendimento = horariosDoDia.Where(a => a.Status == StatusHorarioEnum.Em_Andamento).Count();
            resumo.TotalAgendamentos.Finalizados = horariosDoDia.Where(a => a.Status == StatusHorarioEnum.Finalizado).Count();
            resumo.TotalAgendamentos.Cancelados = horariosDoDia.Where(h => h.Status == StatusHorarioEnum.Cancelado).Count();
            resumo.TotalAgendamentos.ClienteFaltou = horariosDoDia.Where(a => a.Status == StatusHorarioEnum.Cliente_Faltou).Count();
            resumo.TotalAgendamentos.Total = horariosDoDia.Count();

            resumo.TotalClientesComPagamento = totalClientesDistintos;

            var idsPessoasEmDebito = new List<int>();
            idsPessoasEmDebito.AddRange(horariosDoDia.Where(h => h.Status == StatusHorarioEnum.Finalizado && !h.FoiPago).Select(h => h.Cliente.PessoaFisica.IdPessoa).ToList());
            idsPessoasEmDebito.AddRange(preVendasProdutoDoDia.Where(pv => pv.IdPessoaComprador.HasValue).Select(pv => pv.IdPessoaComprador.Value).ToList());
            resumo.TotalClientesEmDebito = idsPessoasEmDebito.GroupBy(i => new { IdPessoaDoCliente = i }).Select(g => g.Key.IdPessoaDoCliente).Count();

            resumo.ValorTotalServicosEmDebito = horariosDoDia.Where(h => h.Status == StatusHorarioEnum.Finalizado && !h.FoiPago).Sum(h => h.Valor);
        }

        private void PreencherPagamentosNosCaixas(FechamentosDiaDTO resumo, ParametrosFiltrosRelatorio parametros, List<TransacaoFormaPagamentoDTO> transacoesFormasPagamentos)
        {
            DateTime dataPesquisa = parametros.DataFinal ?? Calendario.Hoje();

            var formasPagamentos = transacoesFormasPagamentos
                .Where(fp => fp.ValorPago > 0
                          && fp.FormaPagamento != FormaPagamentoEnum.CreditoCliente
                          && fp.FormaPagamento != FormaPagamentoEnum.ValePresente
                          && fp.FormaPagamento != FormaPagamentoEnum.DescontoDeProfissional
                          && fp.FormaPagamento != FormaPagamentoEnum.CreditoDePagamentoOnline
                          && fp.FormaPagamento != FormaPagamentoEnum.DeixarFaltaComoDivida)
                .ToList();

            var formasPagamentosDto = formasPagamentos
                .GroupBy(f => new { f.NumeroParcelas, NomePagamento = f.NomeDaFormaPagamento, TipoPagamento = f.NomeTipoDaFormaPagamento })
                .Select(f => new FormaDePagamentoDTO
                {
                    NumeroDeParcelas = f.Key.NumeroParcelas,
                    NomePagamento = f.Key.NomePagamento,
                    NomeTipoPagamento = f.Key.TipoPagamento,
                    Valor = f.Sum(p => p.ValorPago)
                })
                .ToList();

            var trocosRealizados = transacoesFormasPagamentos
                .Where(tfp => tfp.FormaPagamento == FormaPagamentoEnum.Dinheiro && tfp.ValorPago < 0).ToList();

            var totalTrocosNoDia = new FormaDePagamentoDTO
            {
                NumeroDeParcelas = 1,
                NomePagamento = "Dinheiro",
                NomeTipoPagamento = "Troco",
                Valor = trocosRealizados.Sum(t => t.ValorPago)
            };

            if (totalTrocosNoDia.Valor != 0)
                formasPagamentosDto.Add(totalTrocosNoDia);

            resumo.FormasPagamento = formasPagamentosDto.OrderBy(p => p.NomeTipoPagamento).ThenBy(p => p.NomePagamento).ToList();

            decimal? totalRecebidoEmDinheiro = formasPagamentos.Where(fp => fp.ValorPago > 0 && fp.FormaPagamento == FormaPagamentoEnum.Dinheiro).Sum(fp => fp.ValorPago);

            if (resumo.TeveRegistroDeCaixaNaDataDoResumo)
            {
                resumo.RegistrosDeCaixas = ObterValoresParaDiaComRegistroDeCaixa(parametros, transacoesFormasPagamentos, formasPagamentos);
                PreencherQuadroCaixaParaDiaComControleDeCaixa(resumo, parametros, totalRecebidoEmDinheiro, totalTrocosNoDia.Valor);

                bool profissionalLogadoEhDaRecepcao = parametros.AcessoBackoffice == AcessoBackoffice.Somente_Agenda;
                if (profissionalLogadoEhDaRecepcao)
                {
                    resumo.RegistrosDeCaixas.RemoveAll(r => r.IdPessoaOperadorDeCaixa != parametros.ContaAutenticada.Pessoa.IdPessoa);
                }
            }
            else
            {
                resumo.RegistrosDeCaixas = ObterValoresParaDiaSemRegistroDeCaixa(parametros, formasPagamentos, trocosRealizados);
                PreencherQuadroCaixaParaDiaSemControleDeCaixa(resumo, parametros, totalRecebidoEmDinheiro, totalTrocosNoDia.Valor);
            }
        }

        private List<RegistroDeCaixaPorOperadorDTO> ObterValoresParaDiaComRegistroDeCaixa(ParametrosFiltrosRelatorio parametros, List<TransacaoFormaPagamentoDTO> transacoesFormasPagamentos, List<TransacaoFormaPagamentoDTO> formasPagamento)
        {
            DateTime dataPesquisa = parametros.DataInicial ?? Calendario.Hoje();
            int idPessoaJuridica = parametros.Estabelecimento.PessoaJuridica.IdPessoa;
            var registrosDeCaixa = new List<RegistroDeCaixaPorOperadorDTO>();
            var queryableRegistroDeCaixa = Domain.Financeiro.RegistroDeCaixaPorOperadorRepository.Queryable();

            var registrosDeCaixasAbertosNoDia = queryableRegistroDeCaixa
                .FiltrarPorPessoaJuridica(idPessoaJuridica)
                .FiltrarPelaDataCadastro(dataPesquisa)
                .Select(r => new RegistroDeCaixaPorOperadorDTO
                {
                    IdRegistroDeCaixa = r.Id,
                    IdPessoaOperadorDeCaixa = r.PessoaFisicaOperador.IdPessoa,
                    NomeCompletoOperador = r.PessoaFisicaOperador.NomeCompleto,
                    ApelidoOperador = r.PessoaFisicaOperador.Apelido,
                    AberturaDeCaixa = r.ValorDaAbertura,
                    Sangria = r.ValorDaSangria,
                    SaldoRealDoCaixaEmDinheiro = r.SaldoRealEmDinheiroDoOperador
                })
                .ToList();

            if (registrosDeCaixasAbertosNoDia != null && registrosDeCaixasAbertosNoDia.Any())
            {

                bool profissionalLogadoEhDaRecepcao = parametros.AcessoBackoffice == AcessoBackoffice.Somente_Agenda;
                if (profissionalLogadoEhDaRecepcao)
                {
                    formasPagamento.RemoveAll(f => f.IdPessoaQueRealizou != parametros.ContaAutenticada.Pessoa.IdPessoa);
                }

                var formasPagamentoAgrupadasPorNome = formasPagamento
                    .GroupBy(group => new { group.IdPessoaOperadorCaixa, group.NumeroParcelas, NomePagamento = group.NomeDaFormaPagamento, TipoPagamento = group.NomeTipoDaFormaPagamento })
                    .Select(group => new
                    {
                        IdPessoaOperadorCaixa = group.Key.IdPessoaOperadorCaixa,
                        FormaPagamentoDto = new FormaDePagamentoDTO
                        {
                            NumeroDeParcelas = group.Key.NumeroParcelas,
                            NomePagamento = group.Key.NomePagamento,
                            NomeTipoPagamento = group.Key.TipoPagamento,
                            Valor = group.Sum(tfp => tfp.ValorPago)
                        }
                    })
                    .OrderBy(p => p.FormaPagamentoDto.NomeTipoPagamento)
                    .ThenBy(p => p.FormaPagamentoDto.NomePagamento)
                    .ToList();

                var despesasDosCaixas = Domain.Financeiro.MovimentacaoNoCaixaPorOperadorLancamentoRepository.Queryable()
                     .Where(m => m.CaixaPorOperador.DataHoraCadastro >= dataPesquisa.Date && m.CaixaPorOperador.DataHoraCadastro < dataPesquisa.AddDays(1).Date && m.Ativo)
                     .GroupBy(m => m.CaixaPorOperador.Id)
                     .Select(m => new KeyValuePair<int, decimal>(m.Key, m.Sum(d => d.Lancamento.Valor)))
                     .ToList();

                var datasAberturaDosCaixas = Domain.Financeiro.HistoricoDoCaixaPorOperadorRepository.ListarDataHoraDeAberturaDosCaixasQueForamAbertos(idPessoaJuridica, dataPesquisa);
                var datasFechamentoDosCaixas = Domain.Financeiro.HistoricoDoCaixaPorOperadorRepository.ListarDataHoraDeFechamentoDosCaixasQueForamAbertos(idPessoaJuridica, dataPesquisa);

                foreach (var caixa in registrosDeCaixasAbertosNoDia)
                {

                    caixa.FormasPagamentos = formasPagamentoAgrupadasPorNome.Where(f => f.IdPessoaOperadorCaixa == caixa.IdPessoaOperadorDeCaixa).Select(f => f.FormaPagamentoDto).ToList();

                    decimal? trocoEmDinhero = transacoesFormasPagamentos
                        .Where(tfp => tfp.IdPessoaOperadorCaixa == caixa.IdPessoaOperadorDeCaixa && tfp.FormaPagamento == FormaPagamentoEnum.Dinheiro && tfp.ValorPago < 0)
                        .Sum(tfp => tfp.ValorPago);

                    if (trocoEmDinhero != null && trocoEmDinhero != 0)
                    {
                        caixa.TrocoDinheiro = trocoEmDinhero.Value;

                        var totalTrocosNoDiaPeloOperador = new FormaDePagamentoDTO
                        {
                            NumeroDeParcelas = 1,
                            NomePagamento = "Dinheiro",
                            NomeTipoPagamento = "Troco",
                            Valor = trocoEmDinhero.Value
                        };

                        caixa.FormasPagamentos.Add(totalTrocosNoDiaPeloOperador);
                    }

                    decimal? totalRecebidoEmDinheiro = transacoesFormasPagamentos
                        .Where(tfp => tfp.IdPessoaOperadorCaixa == caixa.IdPessoaOperadorDeCaixa && tfp.FormaPagamento == FormaPagamentoEnum.Dinheiro && tfp.ValorPago > 0)
                        .Sum(tfp => tfp.ValorPago);

                    caixa.RecebidoEmDinheiro = totalRecebidoEmDinheiro ?? 0;

                    decimal? valorDaDespesa = despesasDosCaixas.Where(d => d.Key == caixa.IdRegistroDeCaixa).Select(d => d.Value).FirstOrDefault();
                    caixa.DespesasPagasEmDinheiro = valorDaDespesa ?? 0;

                    var dataHoraQueFoiAberto = datasAberturaDosCaixas.Where(h => h.Key == caixa.IdRegistroDeCaixa);
                    if (dataHoraQueFoiAberto.Any())
                        caixa.DataHoraAberturaDoCaixa = dataHoraQueFoiAberto.Select(h => h.Value).FirstOrDefault();

                    var dataHoraQueFoiFechado = datasFechamentoDosCaixas.Where(h => h.Key == caixa.IdRegistroDeCaixa);
                    if (dataHoraQueFoiFechado.Any())
                        caixa.DataHoraFechamentoDoCaixa = dataHoraQueFoiFechado.Select(h => h.Value).FirstOrDefault();

                    registrosDeCaixa.Add(caixa);
                }
            }

            return registrosDeCaixa;
        }

        private List<RegistroDeCaixaPorOperadorDTO> ObterValoresParaDiaSemRegistroDeCaixa(ParametrosFiltrosRelatorio parametros, List<TransacaoFormaPagamentoDTO> formasPagamento, List<TransacaoFormaPagamentoDTO> trocosRealizados)
        {

            bool profissionalLogadoEhDaRecepcao = parametros.AcessoBackoffice == AcessoBackoffice.Somente_Agenda;
            if (profissionalLogadoEhDaRecepcao)
                formasPagamento.RemoveAll(f => f.IdPessoaQueRealizou != parametros.ContaAutenticada.Pessoa.IdPessoa);

            var formaPagamentosAgrupadosPorOperador = formasPagamento
                    .GroupBy(f => new { NomePessoa = (f.FormaPagamento == FormaPagamentoEnum.PagamentoOnline ? "Pagamento Online" : f.NomeCompletoPessoaQueRealizou), ApelidoPessoa = (f.FormaPagamento == FormaPagamentoEnum.PagamentoOnline ? "" : f.ApelidoPessoaQueRealizou) })
                    .Select(f => new RegistroDeCaixaPorOperadorDTO
                    {
                        NomeCompletoOperador = f.Key.NomePessoa,
                        ApelidoOperador = f.Key.ApelidoPessoa,
                        FormasPagamentos = f.GroupBy(group => new { group.NumeroParcelas, NomePagamento = group.NomeDaFormaPagamento, TipoPagamento = group.NomeTipoDaFormaPagamento })
                        .Select(group => new FormaDePagamentoDTO
                        {
                            NumeroDeParcelas = group.Key.NumeroParcelas,
                            NomePagamento = group.Key.NomePagamento,
                            NomeTipoPagamento = group.Key.TipoPagamento,
                            Valor = group.Sum(tfp => tfp.ValorPago)
                        }).OrderBy(p => p.NomeTipoPagamento).ThenBy(p => p.NomePagamento).ToList()
                    }).OrderBy(p => p.NomeCompletoOperador)
                    .ToList();

            var trocosAgrupadosPorOperador = trocosRealizados
                .GroupBy(t => t.NomeCompletoPessoaQueRealizou)
                .Select(group => new
                {
                    NomeOperador = group.Key,
                    TotalTroco = group.Sum(t => t.ValorPago)
                })
                .ToList();

            foreach (var operador in trocosAgrupadosPorOperador)
            {
                var formaPagamentoPoPessoaDto = formaPagamentosAgrupadosPorOperador.Find(p => p.NomeCompletoOperador.Equals(operador.NomeOperador));
                if (formaPagamentoPoPessoaDto == null)
                    continue;

                var totalTrocosNoDiaPeloOperador = new FormaDePagamentoDTO
                {
                    NumeroDeParcelas = 1,
                    NomePagamento = "Dinheiro",
                    NomeTipoPagamento = "Troco",
                    Valor = operador.TotalTroco
                };

                formaPagamentoPoPessoaDto.FormasPagamentos.Add(totalTrocosNoDiaPeloOperador);
            }

            return formaPagamentosAgrupadosPorOperador;
        }

        private void PreencherFechamentoPorProfissionalDoResumoDoDia(FechamentosDiaDTO resumo, ParametrosFiltrosRelatorio parametros)
        {
            var buscaDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Listar(parametros);

            var comissoesItemVendaProduto = ObterComissaoDeProduto(buscaDeValores);
            var comissoesHorarios = ObterComissaoDeServico(buscaDeValores);
            var comissoesItemVendaPacote = ObterComissaoDePacote(buscaDeValores);
            var valoresDoFechamento = ObterComissaoPorProfissional(buscaDeValores);


            var profissionais = Domain.Pessoas.ProfissionalRepository.ListarAtivos(parametros.Estabelecimento.IdEstabelecimento);
            var medidas = Domain.Pessoas.UnidadeMedidaRepository.ListarAtivos();

            foreach (var fechamento in valoresDoFechamento)
            {
                var profissional = profissionais.Find(p => p.IdProfissional.Equals(fechamento.IdProfissionalComissionado));
                if (profissional == null)
                    continue;

                var dto = new FechamentoPorProfissionalDTO();
                dto.IdProfissional = profissional.IdProfissional;
                dto.NomeCompleto = profissional.PessoaFisica.NomeCompleto;
                dto.Apelido = profissional.PessoaFisica.Apelido;
                dto.ValorRateio = fechamento.ValorRateio;
                dto.QtdClientesAtendidos = fechamento.QuantidadeClientesAtendidos;

                var dadosItemVendaProduto = comissoesItemVendaProduto.Where(p => p.IdProfissionalComissionado.Equals(fechamento.IdProfissionalComissionado)).ToList();
                if (dadosItemVendaProduto.Any())
                {
                    var listaDetalhesDto = new List<DetalhesItemVendaProdutoDTO>();

                    foreach (var item in dadosItemVendaProduto)
                    {
                        string simboloUnidadeMedida = medidas.Where(m => m.Id.Equals(item.IdUnidadeMedida)).Select(m => m.Simbolo).FirstOrDefault();
                        var detalhesMedidasProdutos = new DetalhesItemVendaProdutoDTO(simboloUnidadeMedida, item.TipoDeQuantidade, item.QuantidadeEmUnidade, item.QuantidadeEmFracao);
                        listaDetalhesDto.Add(detalhesMedidasProdutos);
                    }

                    var listaVendasEmUnidades = listaDetalhesDto.Where(u => u.TipoDeQuantidade == TipoDeQuantidadeDeProduto.PorUnidade).ToList();
                    if (listaVendasEmUnidades.Count > 1)
                    {
                        listaDetalhesDto.RemoveAll(u => u.TipoDeQuantidade == TipoDeQuantidadeDeProduto.PorUnidade);
                        var detalhesEmUnidades = new DetalhesItemVendaProdutoDTO(
                                simboloUnidadeMedida: "UN",
                                tipoDeQuantidade: TipoDeQuantidadeDeProduto.PorUnidade,
                                quantidadeEmUnidades: listaVendasEmUnidades.Sum(p => p.QuantidadeEmUnidade));
                        listaDetalhesDto.Add(detalhesEmUnidades);
                    }

                    listaDetalhesDto = listaDetalhesDto.OrderBy(p => p.SimboloUnidadeMedida != "UN").ThenBy(p => p.SimboloUnidadeMedida).ToList();
                    string descricao = String.Empty;
                    foreach (var item in listaDetalhesDto)
                    {
                        descricao += string.Format("{0}, ", item.GerarDescricao());
                    }

                    dto.QtdProdutos = descricao.Remove(descricao.LastIndexOf(", "));
                    dto.ValorProdutos = CalculaValorTotalDosProdutosOuServicos(dadosItemVendaProduto.Sum(p => p.TotalServicos), Convert.ToDecimal(dadosItemVendaProduto.Sum(p => p.ValorDesconto)));
                }
                else
                {
                    dto.QtdProdutos = "0";
                    dto.ValorProdutos = Decimal.Zero;
                }

                var dadosItemVendaPacote = comissoesItemVendaPacote.FirstOrDefault(p => p.IdProfissionalComissionado.Equals(fechamento.IdProfissionalComissionado));
                if (dadosItemVendaPacote != null)
                {
                    dto.QtdPacotes = dadosItemVendaPacote.QuantidadePacotes;
                    dto.ValorPacotes = CalculaValorTotalDosProdutosOuServicos(dadosItemVendaPacote.TotalServicos, Convert.ToDecimal(dadosItemVendaPacote.ValorDesconto));
                }

                var dadosHorarios = comissoesHorarios.FirstOrDefault(p => p.IdProfissionalComissionado.Equals(fechamento.IdProfissionalComissionado));
                if (dadosHorarios != null)
                {
                    dto.QtdServicos = dadosHorarios.QuantidadeServicos;
                    dto.ValorServicos = CalculaValorTotalDosProdutosOuServicos(dadosHorarios.TotalServicos, dadosHorarios.ValorDesconto);
                }

                resumo.FechamentosPorProfissional.Add(dto);
            }
        }

        private List<ValoresComissaoHorariosDTO> ObterComissaoDeServico(IQueryable<ValorDeComissaoAReceber> buscaDeValores)
        {
            return (from vcr in buscaDeValores
                    join p in Domain.Pessoas.ProfissionalRepository.Queryable() on vcr.Comissao.PessoaComissionada.IdPessoa equals p.PessoaFisica.IdPessoa
                    where vcr.Comissao.TipoOrigemComissao == TipoOrigemComissaoEnum.Servico
                    group vcr by p.IdProfissional into valor
                    select new ValoresComissaoHorariosDTO
                    {
                        IdProfissionalComissionado = valor.Key,
                        TotalServicos = valor.Select(c => c.ValorBrutoProporcional).Sum(),
                        ValorDesconto = valor.Select(c => c.DescontoClienteProporcional).Sum(),
                        QuantidadeServicos = valor.Count()
                    }).ToList();
        }

        private List<ValoresComissaoItemVendaProdutoDTO> ObterComissaoDeProduto(IQueryable<ValorDeComissaoAReceber> buscaDeValores)
        {
            return (from vcr in buscaDeValores
                    join ivp in Domain.Vendas.ItemVendaProdutoRepository.Queryable() on vcr.Comissao.Id equals ivp.Comissao.Id
                    let estabProd = ivp.EstabelecimentoProduto
                    join unidMed in Domain.Pessoas.UnidadeMedidaRepository.Queryable() on estabProd.UnidadeMedida equals unidMed
                    join p in Domain.Pessoas.ProfissionalRepository.Queryable() on vcr.Comissao.PessoaComissionada.IdPessoa equals p.PessoaFisica.IdPessoa
                    where vcr.Comissao.TipoOrigemComissao == TipoOrigemComissaoEnum.Produto
                    group vcr by new
                    {
                        IdProfissional = p.IdProfissional,
                        IdUnidadeMedida = unidMed.Id,
                        Quantidade = ivp.Quantidade,
                        MedidasPorUnidade = estabProd.MedidasPorUnidade,
                        TipoDeQuantidade = ivp.TipoDeQuantidade
                    }
                    into valor
                    select new ValoresComissaoItemVendaProdutoDTO
                    {
                        IdProfissionalComissionado = valor.Key.IdProfissional,
                        IdUnidadeMedida = valor.Key.IdUnidadeMedida,
                        QuantidadeEmUnidade = valor.Key.Quantidade / valor.Key.MedidasPorUnidade,
                        QuantidadeEmFracao = valor.Key.Quantidade,
                        TipoDeQuantidade = valor.Key.TipoDeQuantidade,
                        TotalServicos = valor.Select(c => c.ValorBrutoProporcional).Sum(),
                        ValorDesconto = valor.Select(c => c.DescontoClienteProporcional).Sum()
                    }).ToList();
        }

        private List<ValoresComissaoItemVendaPacoteDTO> ObterComissaoDePacote(IQueryable<ValorDeComissaoAReceber> buscaDeValores)
        {
            return (from vcr in buscaDeValores
                    join ivp in Domain.Vendas.ItemVendaPacoteRepository.Queryable() on vcr.Comissao.Id equals ivp.Comissao.Id
                    join p in Domain.Pessoas.ProfissionalRepository.Queryable() on vcr.Comissao.PessoaComissionada.IdPessoa equals p.PessoaFisica.IdPessoa
                    where vcr.Comissao.TipoOrigemComissao == TipoOrigemComissaoEnum.Pacote
                    group vcr by p.IdProfissional into valor
                    select new ValoresComissaoItemVendaPacoteDTO
                    {
                        IdProfissionalComissionado = valor.Key,
                        TotalServicos = valor.Select(c => c.ValorBrutoProporcional).Sum(),
                        ValorDesconto = valor.Select(c => c.DescontoClienteProporcional).Sum(),
                        QuantidadePacotes = valor.Count()
                    }).ToList();
        }

        private List<ValoresComissaoPorProfissionalDTO> ObterComissaoPorProfissional(IQueryable<ValorDeComissaoAReceber> buscaDeValores)
        {
            return (from vcr in buscaDeValores
                    join p in Domain.Pessoas.ProfissionalRepository.Queryable() on vcr.Comissao.PessoaComissionada.IdPessoa equals p.PessoaFisica.IdPessoa
                    where (vcr.ValorBrutoProporcional + vcr.ValorBrutoProporcional) > 0
                    group vcr by p.IdProfissional into valor
                    select new ValoresComissaoPorProfissionalDTO
                    {
                        IdProfissionalComissionado = valor.Key,
                        TotalServicos = valor.Select(c => c.ValorBrutoProporcional).Sum(),
                        ValorDesconto = valor.Select(c => c.DescontoClienteProporcional).Sum(),
                        ValorRateio = valor.Sum(c => c.Valor),
                        QuantidadeClientesAtendidos = valor.Select(c => c.Comissao.Transacao.PessoaQuePagou).Distinct().Count()
                    }).ToList();
        }

        private decimal CalculaValorTotalDosProdutosOuServicos(decimal valorTotal, decimal desconto)
        {
            return valorTotal - (desconto * (-1));
        }

        #endregion Métodos Privados da Impressão do Resumo do Dia

        #region Métodos Privados

        public Transacao ObterPorTransacaoQueEstornou(int idTransacao)
        {
            return Queryable().FirstOrDefault(p => p.TransacaoQueEstounouEsta.Id == idTransacao);
        }

        public IList<RelatorioTransacao> ObterTransacoesParaConsolidadoClienteEstabelecimentoMes(ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> queryBaseRelatorioTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);
            var clientesEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();
            var queryRelatorioTransacaoAgrupadosPorTipoEData =
                (from t in queryBaseRelatorioTransacao
                 join ce in clientesEstabelecimento on new { IdPfC = t.PessoaQuePagou.IdPessoa, IdPjE = t.PessoaQueRecebeu.IdPessoa } equals
                    new { IdPfC = ce.Cliente.PessoaFisica.IdPessoa, IdPjE = ce.Estabelecimento.PessoaJuridica.IdPessoa }
                 where t.PessoaQuePagou != null
                        && ce.Estabelecimento.IdEstabelecimento == parametros.Estabelecimento.IdEstabelecimento
                 group t by
                     new { t.DataReferencia.Month, t.DataReferencia.Year, ce.Codigo, }
                     into g
                 select new RelatorioTransacao
                 {
                     DataHoraInicioHorario = new DateTime(g.Key.Year, g.Key.Month, 1),
                     IdClienteEstabelecimento = g.Key.Codigo
                 }).ToList();

            return queryRelatorioTransacaoAgrupadosPorTipoEData.ToList();
        }

        public IList<RelatorioTransacao> ObterTransacoesParaConsolidadoMes(ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> queryBaseRelatorioTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            var queryTransacaoSomentePagamento = queryBaseRelatorioTransacao
                .Where(f => f.TransacaoQueEstounouEsta == null && f.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && f.DataHora != null)
                .Select(f => new { f.DataReferencia, f.PessoaQuePagou, f.TotalPagar }).ToList();

            var queryRelatorioTransacaoAgrupadosPorTipoEData =
                (from t in queryBaseRelatorioTransacao
                 group t by
                     new { t.DataReferencia.Month, t.DataReferencia.Year }
                     into g
                 select new RelatorioTransacao
                 {
                     DataHoraInicioHorario = new DateTime(g.Key.Year, g.Key.Month, 1)
                 }).ToList();

            foreach (var item in queryRelatorioTransacaoAgrupadosPorTipoEData)
            {
                var listaItens = queryTransacaoSomentePagamento.Where(f => f.DataReferencia.Date.Month == item.DataHoraInicioHorario.Value.Date.Month && f.DataReferencia.Date.Year == item.DataHoraInicioHorario.Value.Date.Year);
                item.NumeroClientes = listaItens.Select(f => f.PessoaQuePagou).Distinct().Count();
            }

            return queryRelatorioTransacaoAgrupadosPorTipoEData.ToList();
        }

        public IList<RelatorioTransacao> ObterTransacoesParaPrimeiraLinhaDoRelatorioPorDataAtendimento(
            ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> queryBaseRelatorioTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            var queryTransacaoSomentePagamento = queryBaseRelatorioTransacao
                .Where(f => f.TransacaoQueEstounouEsta == null && f.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && f.DataHora != null)
                .Select(f => new { f.DataReferencia, f.PessoaQuePagou, f.TotalPagar }).ToList();

            var queryableTransacaoLancamentoFinanceiro = Domain.Financeiro.TransacaoLancamentoFinanceiroRepository.Queryable();
            var listaTransacaoLancamentoFinanceiroDTO = from t in queryBaseRelatorioTransacao
                                                        select new TransacaoLancamentoFinanceiroDTO
                                                        {
                                                            IdTransacao = t.Id,
                                                            DataReferencia = t.DataReferencia,
                                                            TotalDescontos = t.Descontos ?? 0,
                                                            TotalServicos = t.TotalServicos ?? 0,
                                                            TotalProdutos = t.TotalProdutos ?? 0,
                                                            TotalPacotes = t.TotalPacotes ?? 0,
                                                            TotalClubeAssinaturas = t.TotalClubeDeAssinaturas ?? 0,
                                                            TotalCreditoCliente = t.TotalCreditoCliente,
                                                            TotalValePresente = t.TotalValePresente,
                                                            TotalPagar = t.TotalPagar ?? 0,
                                                            TotalDinheiro = t.TotalPagoEmDinheiro ?? 0,
                                                            TotalPrePago = t.TotalPagoEmPrePago,
                                                            TotalCredito = t.TotalPagoEmCredito ?? 0,
                                                            TotalDebito = t.TotalPagoEmDebito ?? 0,
                                                            TotalOutros = t.TotalPagoEmOutros ?? 0,
                                                            TotalTroco = t.Troco ?? 0,
                                                            TotalGorjeta = t.Gorjetas.Where(g => g.Transacao == t && g.Ativo).Sum(g => (decimal?)g.Valor) ?? 0,
                                                            TotalPago = t.TotalPago ?? 0 + t.Troco ?? 0 - t.Gorjetas.Where(g => g.Transacao == t && g.Ativo).Sum(g => (decimal?)g.Valor) ?? 0,
                                                            TotalPagamentoSplit = queryableTransacaoLancamentoFinanceiro.Where(f => f.Transacao == t).Sum(f => (decimal?)f.Lancamento.Valor) ?? 0
                                                        };

            var queryRelatorioTransacaoAgrupadosPorTipoEData =
                (from t in listaTransacaoLancamentoFinanceiroDTO.ToList()
                 group t by
                     new { t.DataReferencia.Month, t.DataReferencia.Year }
                     into g
                 select new RelatorioTransacao
                 {
                     DataHoraInicioHorario = new DateTime(g.Key.Year, g.Key.Month, 1),
                     Produtos = g.Sum(f => f.TotalProdutos),
                     Pacotes = g.Sum(f => f.TotalPacotes),
                     ClubeDeAssinaturas = g.Sum(f => f.TotalClubeAssinaturas),
                     TotalPagoEmValePresente = g.Sum(f => f.TotalValePresente),
                     TotalPagoEmCreditoCliente = g.Sum(f => f.TotalCreditoCliente),
                     Descontos = g.Sum(f => f.TotalDescontos),
                     SubTotal = g.Sum(f => f.TotalServicos),
                     TotalPagar = g.Sum(f => f.TotalPagar),
                     TotalPago = g.Sum(f => f.TotalPago),
                     TotalPagoEmDebito = g.Sum(f => f.TotalDebito),
                     TotalPagoEmCredito = g.Sum(f => f.TotalCredito),
                     TotalPagoEmDinheiro = g.Sum(f => f.TotalDinheiro),
                     TotalPagoEmPrePago = g.Sum(f => f.TotalPrePago),
                     TotalPagoEmOutros = g.Sum(f => f.TotalOutros),
                     Troco = g.Sum(f => f.TotalTroco),
                     Gorjeta = g.Sum(f => f.TotalGorjeta),
                     TotalPagamentoSplit = g.Sum(f => f.TotalPagamentoSplit)
                 }).ToList();

            // Foi necessário fazer desta forma pois o LINQ to NH não consegue fazer count condicional dentro de group
            foreach (var item in queryRelatorioTransacaoAgrupadosPorTipoEData)
            {
                var listaItens = queryTransacaoSomentePagamento.Where(f => f.DataReferencia.Date.Month == item.DataHoraInicioHorario.Value.Date.Month && f.DataReferencia.Date.Year == item.DataHoraInicioHorario.Value.Date.Year);
                item.QuantidadeAtendimentos = listaItens.Count(f => f.TotalPagar.Value > 0);
                item.NumeroClientes = listaItens.Select(f => f.PessoaQuePagou).Distinct().Count();
            }

            return queryRelatorioTransacaoAgrupadosPorTipoEData.ToList();
        }

        public IList<RelatorioTransacao> ObterTransacoesParaPrimeiraLinhaDoRelatorioPorDataTransacao(
            ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> queryBaseRelatorioTransacao =
                AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            //var listaTransacao = queryBaseRelatorioTransacao.ToList();
            var queryableTransacaoLancamentoFinanceiro = Domain.Financeiro.TransacaoLancamentoFinanceiroRepository.Queryable();
            var listaTransacaoLancamentoFinanceiroDTO = from t in queryBaseRelatorioTransacao
                                                        select new TransacaoLancamentoFinanceiroDTO
                                                        {
                                                            IdTransacao = t.Id,
                                                            DataMovimentacao = t.DataHora,
                                                            TotalDescontos = t.Descontos ?? 0,
                                                            TotalServicos = t.TotalServicos ?? 0,
                                                            TotalProdutos = t.TotalProdutos ?? 0,
                                                            TotalPacotes = t.TotalPacotes ?? 0,
                                                            TotalClubeAssinaturas = t.TotalClubeDeAssinaturas ?? 0,
                                                            TotalCreditoCliente = t.TotalCreditoCliente,
                                                            TotalValePresente = t.TotalValePresente,
                                                            TotalPagar = t.TotalPagar ?? 0,
                                                            TotalDinheiro = t.TotalPagoEmDinheiro ?? 0,
                                                            TotalPrePago = t.TotalPagoEmPrePago,
                                                            TotalCredito = t.TotalPagoEmCredito ?? 0,
                                                            TotalDebito = t.TotalPagoEmDebito ?? 0,
                                                            TotalOutros = t.TotalPagoEmOutros ?? 0,
                                                            TotalTroco = t.Troco ?? 0,
                                                            TotalGorjeta = t.Gorjetas.Where(g => g.Transacao == t && g.Ativo).Sum(g => (decimal?)g.Valor) ?? 0,
                                                            TotalPago = t.TotalPago ?? 0 + t.Troco ?? 0 - t.Gorjetas.Where(g => g.Transacao == t && g.Ativo).Sum(g => (decimal?)g.Valor) ?? 0,
                                                            TotalPagamentoSplit = queryableTransacaoLancamentoFinanceiro.Where(f => f.Transacao == t).Sum(p => (decimal?)p.Lancamento.Valor) ?? 0
                                                        };

            IEnumerable<RelatorioTransacao> queryRelatorioTransacaoAgrupadosPorTipoEData =
                (from t in listaTransacaoLancamentoFinanceiroDTO.ToList()
                 group t by
                     new { t.DataMovimentacao.Month, t.DataMovimentacao.Year }
                     into g
                 select new RelatorioTransacao
                 {
                     DataHora = new DateTime(g.Key.Year, g.Key.Month, 1),
                     Produtos = g.Sum(f => f.TotalProdutos),
                     Pacotes = g.Sum(f => f.TotalPacotes),
                     ClubeDeAssinaturas = g.Sum(f => f.TotalClubeAssinaturas),
                     TotalPagoEmValePresente = g.Sum(f => f.TotalValePresente),
                     TotalPagoEmCreditoCliente = g.Sum(f => f.TotalCreditoCliente),
                     Descontos = g.Sum(f => f.TotalDescontos),
                     SubTotal = g.Sum(f => f.TotalServicos),
                     TotalPagar = g.Sum(f => f.TotalPagar),
                     TotalPago = g.Sum(f => f.TotalPago),
                     TotalPagoEmDebito = g.Sum(f => f.TotalDebito),
                     TotalPagoEmCredito = g.Sum(f => f.TotalCredito),
                     TotalPagoEmDinheiro = g.Sum(f => f.TotalDinheiro),
                     TotalPagoEmPrePago = g.Sum(f => f.TotalPrePago),
                     TotalPagoEmOutros = g.Sum(f => f.TotalOutros),
                     Troco = g.Sum(f => f.TotalTroco),
                     Gorjeta = g.Sum(f => f.TotalGorjeta),
                     TotalPagamentoSplit = g.Sum(f => f.TotalPagamentoSplit)
                 });

            var retorno = queryRelatorioTransacaoAgrupadosPorTipoEData.ToList();

            return retorno;
        }

        public IList<RelatorioTransacao> ObterTransacoesParaSegundaLinhaDoRelatorioPorDataAtendimento(
            ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> queryTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            var queryTransacaoSomentePagamento = queryTransacao
                .Where(f => f.TransacaoQueEstounouEsta == null && f.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && f.DataHora != null)
                .Select(f => new { f.DataReferencia, f.PessoaQuePagou, f.TotalPagar }).ToList();

            var queryableTransacaoLancamentoFinanceiro = Domain.Financeiro.TransacaoLancamentoFinanceiroRepository.Queryable();
            var queryableVenda = Domain.Vendas.VendaRepository.Queryable();
            var queryableItemVenda = Domain.Vendas.ItemVendaRepository.Queryable();
            var queryablePagamentoDividaQueryable = Domain.DebitoParcial.PagamentoDeDividaPeloClienteRepository.Queryable();
            var queryableDividasDeixadas = Domain.DebitoParcial.DividaDeixadaNoEstabelecimentoRepository.Queryable();

            var listaTransacaoLancamentoFinanceiroDTO = from t in queryTransacao
                                                        join v in queryableVenda on t equals v.Transacao
                                                        select new TransacaoLancamentoFinanceiroDTO
                                                        {
                                                            IdTransacao = t.Id,
                                                            PessoaQuePagouEstaInformada = t.PessoaQuePagou != null,
                                                            NomePessoaQuerPagou = t.PessoaQuePagou != null ? t.PessoaQuePagou.NomeCompleto : "",
                                                            DataReferencia = t.DataReferencia,
                                                            DataAtendimento = v.Data,
                                                            DataMovimentacao = t.DataHora,
                                                            HoraMovimentacao = t.DataHora,
                                                            PagamentoJaEstaEstornado = t.TransacaoQueEstounouEsta != null,
                                                            NomeTipoTransacao = t.TipoTransacao.Nome,
                                                            IdTipoTransacao = t.TipoTransacao.Id,
                                                            TotalDescontos = (t.Descontos ?? 0),
                                                            TotalServicos = t.TotalServicos ?? 0,
                                                            TotalProdutos = t.TotalProdutos ?? 0,
                                                            TotalPacotes = t.TotalPacotes ?? 0,
                                                            TotalClubeAssinaturas = t.TotalClubeDeAssinaturas ?? 0,
                                                            TotalCreditoCliente = t.TotalCreditoCliente,
                                                            TotalValePresente = t.TotalValePresente,
                                                            TotalPagar = t.TotalPagar ?? 0,
                                                            TotalDinheiro = t.TotalPagoEmDinheiro ?? 0,
                                                            TotalPrePago = t.TotalPagoEmPrePago,
                                                            TotalCredito = t.TotalPagoEmCredito ?? 0,
                                                            TotalDebito = t.TotalPagoEmDebito ?? 0,
                                                            TotalOutros = (t.TotalPagoEmOutros ?? 0),
                                                            TotalTroco = t.Troco ?? 0,
                                                            TotalGorjeta = t.Gorjetas.Where(g => g.Transacao == t && g.Ativo).Sum(g => (decimal?)g.Valor) ?? 0,
                                                            TotalPago = t.TotalPago ?? 0,
                                                            TotalPagamentoSplit = queryableTransacaoLancamentoFinanceiro.Where(f => f.Transacao == t).Sum(p => (decimal?)p.Lancamento.Valor) ?? 0,
                                                            TotalDividasPagas = queryablePagamentoDividaQueryable.Where(f => f.IdTransacao == t.Id).Sum(p => (decimal?)p.ValorPago) ?? 0,
                                                            TotalDividasDeixadas = queryableDividasDeixadas.Where(f => f.IdTransacao == t.Id).Sum(p => (decimal?)p.ValorInicialDaDivida) ?? 0,
                                                            TotalPix = t.FormasPagamento.Where(f => f.FormaPagamento.Tipo.Id == (int)FormaPagamentoTipoEnum.Pix).Sum(p => (decimal?)p.ValorPago) ?? 0,
                                                            EhConsumoPacote = queryableItemVenda.Any(f => f.ItemPacoteCliente != null && f.Venda.Transacao == t) || t.HorariosTransacoes.Any(f => f.ItemPacoteCliente != null),
                                                        };

            var queryRelatorioTransacaoAgrupadosPorTipoEData =
                (from t in listaTransacaoLancamentoFinanceiroDTO.ToList()
                 group t by
                    t.DataReferencia.Date
                     into g
                 select new RelatorioTransacao
                 {
                     DataHoraInicioHorario = g.Key,
                     Produtos = g.Sum(f => f.TotalProdutos),
                     Pacotes = g.Sum(f => f.TotalPacotes),
                     ClubeDeAssinaturas = g.Sum(f => f.TotalClubeAssinaturas),
                     TotalPagoEmCreditoCliente = g.Sum(f => f.TotalCreditoCliente),
                     TotalPagoEmValePresente = g.Sum(f => f.TotalValePresente),
                     Descontos = g.Sum(f => f.TotalDescontos),
                     SubTotal = g.Sum(f => f.TotalServicos),
                     TotalPagar = g.Sum(f => f.TotalPagar),
                     TotalPago = g.Sum(f => f.TotalPago),
                     TotalPagoEmDebito = g.Sum(f => f.TotalDebito),
                     TotalPagoEmCredito = g.Sum(f => f.TotalCredito),
                     TotalPagoEmDinheiro = g.Sum(f => f.TotalDinheiro),
                     TotalPagoEmPrePago = g.Sum(f => f.TotalPrePago),
                     TotalPagoEmOutros = g.Sum(f => f.TotalOutros - (parametros.OrigemRelatorio == OrigemRelatorio.Consolidado ? f.TotalPix : 0)),
                     TotalDividasDeixadas = -g.Sum(f => f.TotalDividasDeixadas),
                     TotalDividasPagas = g.Sum(f => f.TotalDividasPagas),
                     TotalPix = g.Sum(f => f.TotalPix),
                     Troco = g.Sum(f => f.TotalTroco),
                     Gorjeta = g.Sum(f => f.TotalGorjeta),
                     TotalPagamentoSplit = g.Sum(f => f.TotalPagamentoSplit),
                 }).ToList();

            // Foi necessário fazer desta forma pois o LINQ to NH não consegue fazer count condicional dentro de group
            foreach (var item in queryRelatorioTransacaoAgrupadosPorTipoEData)
            {
                var listaItens = queryTransacaoSomentePagamento.Where(f => f.DataReferencia.Date == item.DataHoraInicioHorario.Value.Date);
                item.QuantidadeAtendimentos = listaItens.Count(f => f.TotalPagar > 0);
                item.NumeroClientes = listaItens.Select(f => f.PessoaQuePagou).Distinct().Count();
            }

            var retorno = queryRelatorioTransacaoAgrupadosPorTipoEData;

            var buscarCashback = Domain.Cashback.CashbackService
                .EstabelecimentoNecessitaBucarCashback(parametros.Estabelecimento);

            if (retorno != null && retorno.Count > 0 && buscarCashback)
            {
                var listCashback = (from transacao in queryTransacao
                                    join csh in Domain.Cashback.CashbackTransacaoRepository.StatelessQueryable()
                                    on transacao.Id equals csh.IdTransacao
                                    select new { transacao.DataHora, csh.Valor }).ToList();

                retorno.ForEach(item =>
                {
                    var valorCashback = listCashback.Where(csh => csh.DataHora.Date == item.DataHora).Sum(csh => csh.Valor);
                    item.Descontos -= valorCashback;
                    item.ValorDescontoCashback = valorCashback;
                });
            }

            return retorno;
        }

        private int PegarAtendimentos(IEnumerable<Transacao> queryTransacaoSomentePagamento, DateTime dateTime)
        {
            return queryTransacaoSomentePagamento.Count(f => f.DataHora.Date == dateTime);
        }

        public IList<RelatorioTransacao> ObterTransacoesParaSegundaLinhaDoRelatorioPorDataTransacao(
            ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> queryBaseRelatorioTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            var queryableTransacaoLancamentoFinanceiro = Domain.Financeiro.TransacaoLancamentoFinanceiroRepository.Queryable();
            var queryableItemVendas = Domain.Vendas.ItemVendaRepository.Queryable();
            var queryableVendas = Domain.Vendas.VendaRepository.Queryable();
            var queryableHorarioTransacao = Domain.Pessoas.HorarioTransacaoRepository.Queryable();
            var queryablePagamentoDividaQueryable = Domain.DebitoParcial.PagamentoDeDividaPeloClienteRepository.Queryable();
            var queryableDividasDeixadas = Domain.DebitoParcial.DividaDeixadaNoEstabelecimentoRepository.Queryable();

            var listaTransacaoLancamentoFinanceiroDTO = from t in queryBaseRelatorioTransacao
                                                        join v in queryableVendas on t equals v.Transacao
                                                        select new TransacaoLancamentoFinanceiroDTO
                                                        {
                                                            IdTransacao = t.Id,
                                                            PessoaQuePagouEstaInformada = t.PessoaQuePagou != null,
                                                            NomePessoaQuerPagou = t.PessoaQuePagou != null ? t.PessoaQuePagou.NomeCompleto : "",
                                                            DataAtendimento = v.Data,
                                                            DataMovimentacao = t.DataHora,
                                                            HoraMovimentacao = t.DataHora,
                                                            PagamentoJaEstaEstornado = t.TransacaoQueEstounouEsta != null,
                                                            NomeTipoTransacao = t.TipoTransacao.Nome,
                                                            IdTipoTransacao = t.TipoTransacao.Id,
                                                            TotalDescontos = (t.Descontos ?? 0),
                                                            TotalServicos = t.TotalServicos ?? 0,
                                                            TotalProdutos = t.TotalProdutos ?? 0,
                                                            TotalPacotes = t.TotalPacotes ?? 0,
                                                            TotalClubeAssinaturas = t.TotalClubeDeAssinaturas ?? 0,
                                                            TotalCreditoCliente = t.TotalCreditoCliente,
                                                            TotalValePresente = t.TotalValePresente,
                                                            TotalPagar = t.TotalPagar ?? 0,
                                                            TotalDinheiro = t.TotalPagoEmDinheiro ?? 0,
                                                            TotalPrePago = t.TotalPagoEmPrePago,
                                                            TotalCredito = t.TotalPagoEmCredito ?? 0,
                                                            TotalDebito = t.TotalPagoEmDebito ?? 0,
                                                            TotalOutros = t.TotalPagoEmOutros ?? 0,
                                                            TotalTroco = t.Troco ?? 0,
                                                            TotalGorjeta = t.Gorjetas.Where(g => g.Transacao == t && g.Ativo).Sum(g => (decimal?)g.Valor) ?? 0,
                                                            TotalPago = t.TotalPago ?? 0,
                                                            TotalPagamentoSplit = queryableTransacaoLancamentoFinanceiro.Where(f => f.Transacao == t).Sum(f => (decimal?)f.Lancamento.Valor) ?? 0,
                                                            TotalDividasPagas = queryablePagamentoDividaQueryable.Where(f => f.IdTransacao == t.Id).Sum(p => (decimal?)p.ValorPago) ?? 0,
                                                            TotalPix = t.FormasPagamento.Where(f => f.FormaPagamento.Tipo.Id == (int)FormaPagamentoTipoEnum.Pix).Sum(p => (decimal?)p.ValorPago) ?? 0,
                                                            TotalDividasDeixadas = queryableDividasDeixadas.Where(f => f.IdTransacao == t.Id).Sum(p => (decimal?)p.ValorInicialDaDivida) ?? 0,   
                                                            EhConsumoPacote = queryableItemVendas.Any(f => f.Venda.Transacao == t && f.ItemPacoteCliente != null) || queryableHorarioTransacao.Any(f => f.Transacao == t && f.ItemPacoteCliente != null),
                                                        };

            IEnumerable<RelatorioTransacao> queryRelatorioTransacaoAgrupadosPorTipoEData =
                from t in listaTransacaoLancamentoFinanceiroDTO.ToList()
                group t by new { DataTransacao = t.DataMovimentacao.Date }
                    into g
                select new RelatorioTransacao
                {
                    DataHora = g.Key.DataTransacao,
                    Produtos = g.Sum(f => f.TotalProdutos),
                    Pacotes = g.Sum(f => f.TotalPacotes),
                    ClubeDeAssinaturas = g.Sum(f => f.TotalClubeAssinaturas),
                    TotalPagoEmCreditoCliente = g.Sum(f => f.TotalCreditoCliente),
                    TotalPagoEmValePresente = g.Sum(f => f.TotalValePresente),
                    Descontos = g.Sum(f => f.TotalDescontos),
                    SubTotal = g.Sum(f => f.TotalServicos),
                    TotalPagar = g.Sum(f => f.TotalPagar),
                    TotalPago = g.Sum(f => f.TotalPago),
                    TotalPagoEmDebito = g.Sum(f => f.TotalDebito),
                    TotalPagoEmCredito = g.Sum(f => f.TotalCredito),
                    TotalPagoEmDinheiro = g.Sum(f => f.TotalDinheiro),
                    TotalPagoEmPrePago = g.Sum(f => f.TotalPrePago),
                    TotalPagoEmOutros = g.Sum(f => f.TotalOutros - (parametros.OrigemRelatorio == OrigemRelatorio.Consolidado ? f.TotalPix : 0)),
                    TotalDividasDeixadas = -g.Sum(f => f.TotalDividasDeixadas),
                    TotalDividasPagas = g.Sum(f => f.TotalDividasPagas),
                    TotalPix = g.Sum(f => f.TotalPix),
                    Troco = g.Sum(f => f.TotalTroco),
                    Gorjeta = g.Sum(f => f.TotalGorjeta),
                    TotalPagamentoSplit = g.Sum(f => f.TotalPagamentoSplit),
                };

            var retorno = queryRelatorioTransacaoAgrupadosPorTipoEData.ToList();

            var buscarCashback = Domain.Cashback.CashbackService
                .EstabelecimentoNecessitaBucarCashback(parametros.Estabelecimento);

            if (retorno != null && retorno.Count > 0 && buscarCashback)
            {
                var listCashback = (from transacao in queryBaseRelatorioTransacao
                                         join csh in Domain.Cashback.CashbackTransacaoRepository.StatelessQueryable()
                                         on transacao.Id equals csh.IdTransacao
                                         select new { transacao.DataHora, csh.Valor }).ToList();

                retorno.ForEach(item =>
                {
                    var valorCashback = listCashback.Where(csh => csh.DataHora.Date == item.DataHora).Sum(csh => csh.Valor);
                    item.Descontos -= valorCashback;
                    item.ValorDescontoCashback = valorCashback;
                });
            }

            return retorno;
        }

        #endregion Métodos Privados

        #endregion Relatório Financeiro

        #region Trinks em Números

        public int CountCheckouts(int idEstabelecimento)
        {
            return ObterPorEstabelecimento(idEstabelecimento).Count(f => f.TransacaoQueEstounouEsta == null);
        }

        public int CountCheckouts(int idEstabelecimento, DateTime data)
        {
            return ObterPorEstabelecimento(idEstabelecimento).Count(f => f.TransacaoQueEstounouEsta == null
                && f.DataHora.Date == data.Date);
        }

        public int CountPorEstabelecimento(int idEstabelecimento)
        {
            return ObterPorEstabelecimento(idEstabelecimento).Count();
        }

        #endregion Trinks em Números

        #region Critérios

        private IQueryable<Transacao> FiltroEstabelecimento(IQueryable<Transacao> t, Estabelecimento estabelecimento)
        {
            t = t.Where(f => f.PessoaQueRecebeu.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa);
            return t;
        }

        private IQueryable<Transacao> FiltroPessoaJuridica(IQueryable<Transacao> t, int idPessoaDaPessoaJuridica, int idPessoaJuridicaEstabelecimento)
        {
            var ehPesquisaPorProfissionalParceiro = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Any(p => p.PessoaJuridica.IdPessoa == idPessoaDaPessoaJuridica);
            if (ehPesquisaPorProfissionalParceiro)
            {
                //var profissionalEstabelecimento = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterProfissionalPelaPessoaJuridica(idPessoaDaPessoaJuridica);

                List<int> idsPessoasDosProfissionaisParceiros = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                    .Where(ep => ep.PessoaJuridica.IdPessoa == idPessoaDaPessoaJuridica)
                    .Select(ep => ep.Profissional.PessoaFisica.IdPessoa)
                    .ToList();

                var trasacaoHorariosTrasacaoProfissional = Domain.Pessoas.HorarioTransacaoRepository.Queryable().Where(p => p.Transacao.PessoaQueRecebeu.IdPessoa == idPessoaJuridicaEstabelecimento &&
                                                                              idsPessoasDosProfissionaisParceiros.Contains(p.Comissao.PessoaComissionada.IdPessoa) &&
                                                                              p.Comissao.IdPessoaEstabelecimento == idPessoaJuridicaEstabelecimento).Select(f => f.Transacao);

                var trasacaoHorariosTrasacaoAssistente = Domain.Pessoas.HorarioTransacaoRepository.Queryable().Where(p => p.Transacao.PessoaQueRecebeu.IdPessoa == idPessoaJuridicaEstabelecimento &&
                                                                              idsPessoasDosProfissionaisParceiros.Contains(p.ComissaoAssistente.PessoaComissionada.IdPessoa) &&
                                                                              p.ComissaoAssistente.IdPessoaEstabelecimento == idPessoaJuridicaEstabelecimento).Select(f => f.Transacao);

                t = t.Where(f => trasacaoHorariosTrasacaoProfissional.Contains(f) || trasacaoHorariosTrasacaoAssistente.Contains(f));
            }
            else
            {
                t = t.Where(f => f.PessoaQueRecebeu.IdPessoa == idPessoaDaPessoaJuridica);
            }

            return t;
        }

        //private static IQueryable<Transacao> FiltroLoteRPS(IQueryable<Transacao> ht, int? loteRPS, int idPessoaJuridica) {
        //    if (loteRPS.HasValue) {
        //        var queryEmissoesRPS = Domain.
        //        ht = ht.Where(f => f.DadosRPS != null && f.DadosRPS.EmissoesRPS.Any(g => g.Lote == loteRPS.Value));
        //    }

        //    return ht;
        //}

        //private static IQueryable<Transacao> FiltroNumeroRPS(IQueryable<Transacao> ht, int? numeroRPS) {
        //    if (numeroRPS.HasValue)
        //        ht = ht.Where(f => f.DadosRPS != null && f.DadosRPS.EmissoesRPS.Any(g => g.Numero == numeroRPS.Value));

        //    return ht;
        //}

        private static IQueryable<Transacao> FiltroTipoPagamento(IQueryable<Transacao> t)
        {
            return t.Where(f => f.TipoTransacao.Id == 1);
        }

        private IQueryable<Transacao> AdicionarCriterioBuscaPorDataAtendimentoVendaNoPeriodo(IQueryable<Transacao> query, DateTime? dataInicio, DateTime? dataFim)
        {
            if (dataInicio.HasValue && !dataFim.HasValue)
            {
                var dtInicio = new DateTime(dataInicio.Value.Year, dataInicio.Value.Month, dataInicio.Value.Day, 0, 0, 0);
                query = query.Where(f => f.DataReferencia >= dtInicio);
            }
            else if (!dataInicio.HasValue && dataFim.HasValue)
            {
                var dtFim = new DateTime(dataFim.Value.Year, dataFim.Value.Month,
                    dataFim.Value.Day, 23, 59, 59);
                query = query.Where(f => f.DataReferencia <= dtFim);
            }
            else if (dataInicio.HasValue && dataFim.HasValue)
            {
                var dtInicio = new DateTime(dataInicio.Value.Year, dataInicio.Value.Month,
                    dataInicio.Value.Day, 0, 0, 0);
                var dtFim = new DateTime(dataFim.Value.Year, dataFim.Value.Month,
                    dataFim.Value.Day, 23, 59, 59);
                query = query.Where(f => f.DataReferencia >= dtInicio && f.DataReferencia <= dtFim);
            }

            return query;
        }

        private IQueryable<Transacao> AdicionarCriterioBuscaPorDataMovimentacaoNoPeriodo(IQueryable<Transacao> query, DateTime? dataInicio, DateTime? dataFim)
        {
            if (dataInicio.HasValue && !dataFim.HasValue)
            {
                var dtInicio = new DateTime(dataInicio.Value.Year, dataInicio.Value.Month, dataInicio.Value.Day, 0, 0, 0);
                query = query.Where(f => f.DataHora >= dtInicio);
            }
            else if (!dataInicio.HasValue && dataFim.HasValue)
            {
                var dtFim = new DateTime(dataFim.Value.Year, dataFim.Value.Month,
                    dataFim.Value.Day, 23, 59, 59);
                query = query.Where(f => f.DataHora <= dtFim);
            }
            else if (dataInicio.HasValue && dataFim.HasValue)
            {
                var dtInicio = new DateTime(dataInicio.Value.Year, dataInicio.Value.Month,
                    dataInicio.Value.Day, 0, 0, 0);
                var dtFim = new DateTime(dataFim.Value.Year, dataFim.Value.Month,
                    dataFim.Value.Day, 23, 59, 59);
                query = query.Where(f => f.DataHora >= dtInicio && f.DataHora <= dtFim);
            }

            return query;
        }

        private IQueryable<Transacao> AdicionarCriterioFormaPagamentoDiferenteDescontoFornecedor(IQueryable<Transacao> query)
        {
            return query.Where(p => !p.FormasPagamento.Any() || p.FormasPagamento.Any(p2 => p2.FormaPagamento.ExibirTransacoesPagasNosRelatorios));
        }

        private IQueryable<Transacao> AdicionarCriterioTipoFiltroTransacaoProduto(
            IQueryable<Transacao> query, ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoFiltroTransacaoProduto != TipoFiltroTransacaoProduto.Todos)
            {
                var queryableVenda = Domain.Vendas.ItemVendaRepository.Queryable();

                if (parametros.TipoFiltroTransacaoProduto == TipoFiltroTransacaoProduto.Servicos)
                    query = query.Where(f => f.HorariosTransacoes.Any());
                else if (parametros.TipoFiltroTransacaoProduto == TipoFiltroTransacaoProduto.Produtos)
                    query = query.Where(f => queryableVenda.Any(p => p.Venda.Transacao.Id == f.Id && p.Venda.ItensVenda.Any(g => g is ItemVendaProduto)));
                else if (parametros.TipoFiltroTransacaoProduto == TipoFiltroTransacaoProduto.Pacotes)
                    query = query.Where(f => queryableVenda.Any(p => p.Venda.Transacao.Id == f.Id && p.Venda.ItensVenda.Any(g => g is ItemVendaPacote)));
                else if (parametros.TipoFiltroTransacaoProduto == TipoFiltroTransacaoProduto.ClubeDeAssinatura)
                    query = query.Where(p => p.TransacaoItens.Any(f => f.Tipo == TransacaoItemTipo.PagamentoDeAssinatura));
            }

            return query;
        }

        private IQueryable<Transacao> AdicionarWhereBaseTransacaoRelatorioFinanceiro(
            ParametrosFiltrosRelatorio parametros)
        {
            IQueryable<Transacao> query = Queryable().Where(t =>
                t.Ativo && t.PessoaQueRecebeu == parametros.Estabelecimento.PessoaJuridica);

            if (parametros.TipoData == TipoDataRelatorio.DataAtendimento)
                query = AdicionarCriterioBuscaPorDataAtendimentoVendaNoPeriodo(query, parametros.DataInicial, parametros.DataFinal);

            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                query = AdicionarCriterioBuscaPorDataMovimentacaoNoPeriodo(query, parametros.DataInicial, parametros.DataFinal);

            query = AdicionarCriterioTipoFiltroTransacaoProduto(query, parametros);
            query = AdicionarCriterioFormaPagamentoDiferenteDescontoFornecedor(query);

            if (!parametros.ExibirEstornos)
                query = query.Where(f => f.TransacaoQueEstounouEsta == null && f.TipoTransacao.Id == 1);

            if (parametros.ExibirApenasComDescontos)
            {
                var queryableVendas = Domain.Vendas.VendaRepository.Queryable().Where(p => p.ItensVenda.Any(p2 => p2.Desconto != 0));
                query = query.Where(f => f.HorariosTransacoes.Any(ht => ht.Desconto.HasValue && ht.Desconto.Value != 0) || queryableVendas.Any(v => v.Transacao.Id == f.Id));
            }

            if (parametros.ExibirApenasTransacoesComDescontoCashback)
            {
                var queryableCashback = Domain.Cashback.CashbackTransacaoRepository.Queryable();
                query = (from tr in query
                         where (queryableCashback.Where(cashback => cashback.IdTransacao == tr.Id)
                         .Sum(cashb => (decimal?)cashb.Valor) ?? 0) != 0
                         select tr);
            }

            if (parametros.IdMotivoDesconto > 0)
            {
                var queryableVendas = Domain.Vendas.VendaRepository.Queryable().Where(p => p.ItensVenda.Any(p2 => p2.MotivoDesconto.Id == parametros.IdMotivoDesconto));
                query = query.Where(f => f.HorariosTransacoes.Any(ht => ht.MotivoDesconto.Id == parametros.IdMotivoDesconto) || queryableVendas.Any(v => v.Transacao.Id == f.Id));
            }

            if (parametros.NumeroComanda.HasValue && parametros.NumeroComanda.Value > 0)
            {
                var queryItemVendaProduto = Domain.Vendas.ItemVendaProdutoRepository.Queryable();
                queryItemVendaProduto = queryItemVendaProduto.Where(a => a.PreVenda != null);
                queryItemVendaProduto = queryItemVendaProduto.Where(a => a.PreVenda.Comanda != null);
                queryItemVendaProduto = queryItemVendaProduto.Where(a => a.PreVenda.Comanda.Numero == parametros.NumeroComanda.Value);

                //var retornoProdutos = queryItemVendaProduto.Select(a => a.Venda.Transacao.Id).ToList();

                var queryServicos = Domain.Vendas.PreVendaServicoRepository.Queryable();
                queryServicos = queryServicos.Where(a => a.Comanda != null);
                queryServicos = queryServicos.Where(a => a.Comanda.Numero == parametros.NumeroComanda.Value);

                //var retornoServicos = queryServicos.SelectMany(a => a.Horario.HorariosTransacoes, (a, b) => new { Id = b.Transacao.Id }).Select(a => new KeyValuePair<Int32, Int32>(a.Id, a.Numero)).ToList();
                //retornoServicos.AddRange(retornoProdutos);
                query = query.Where(f => queryItemVendaProduto.Any(a => a.Venda.Transacao.Id == f.Id) || queryServicos.Any(a => a.Horario.HorariosTransacoes.Any(b => b.Transacao.Id == f.Id)));
            }

            return query;
        }

        //private IQueryable<Transacao> FiltroEmissaoManual(IQueryable<Transacao> ht, bool? exibicaoDeEmissaoManual) {
        //    if (exibicaoDeEmissaoManual.HasValue)
        //        ht = ht.Where(f => f.DadosRPS != null && f.DadosRPS.EmissaoManual == exibicaoDeEmissaoManual.Value);
        //    return ht;
        //}

        private IQueryable<Transacao> FiltroFormaPagamento(IQueryable<Transacao> ht, int? idFormaPagamento)
        {
            if (idFormaPagamento.HasValue && idFormaPagamento > 0)
                ht =
                    ht.Where(
                        f =>
                            f.FormasPagamento.Any(
                                g => g.FormaPagamento.Id == idFormaPagamento));
            return ht;
        }

        private IQueryable<Transacao> FiltroFormaPagamentoPositiva(IQueryable<Transacao> query, List<int> list)
        {
            if (list != null && list.Any())
                query = query.Where(f => f.FormasPagamento.Any(g => g.ValorPago > 0 && list.Contains(g.FormaPagamento.Tipo.Id)));
            return query;
        }

        private IQueryable<Transacao> FiltroFormaPagamento(IQueryable<Transacao> query, List<int> list)
        {
            if (list != null && list.Any())
                query = query.Where(f => f.FormasPagamento.Any(g => list.Contains(g.FormaPagamento.Tipo.Id)));
            return query;
        }

        private IQueryable<Transacao> FiltroPagamentoTipo(IQueryable<Transacao> ht, int? idFormaPagamentoTipo)
        {
            if (idFormaPagamentoTipo.HasValue && idFormaPagamentoTipo > 0)
                ht =
                    ht.Where(
                        f =>
                            f.FormasPagamento.Any(
                                g => g.FormaPagamento.Tipo.Id == idFormaPagamentoTipo));
            return ht;
        }

        //private IQueryable<Transacao> FiltroPeriodoPagamentoOuGeracaoLoteRPS(ParametrosFiltrosLotesRPS filtro, IQueryable<Transacao> t) {
        //    if (filtro.TipoData == TipoDataRelatorio.DataTransacao) {
        //        if (filtro.DataInicial.HasValue)
        //            t = t.Where(f => f.DataHora >= filtro.DataInicial.Value.Date);

        //        if (filtro.DataFinal.HasValue)
        //            t = t.Where(f => f.DataHora < filtro.DataFinal.Value.Date.AddDays(1));
        //    }
        //    else if (filtro.TipoData == TipoDataRelatorio.DataGeracaoLoteRPS) {
        //        if (filtro.DataInicial.HasValue)
        //            t = t.Where(f => f.DadosRPS.EmissoesRPS.Max(g => g.DataEmissao) >= filtro.DataInicial.Value.Date);

        //        if (filtro.DataFinal.HasValue)
        //            t = t.Where(f => f.DadosRPS.EmissoesRPS.Max(g => g.DataEmissao) < filtro.DataFinal.Value.Date.AddDays(1));
        //    }

        //    return t;
        //}

        private IQueryable<Transacao> FiltroStatusRPS(IQueryable<Transacao> ht, List<StatusRpsEnum> statusRPSSelecionados, int idPessoaJuridica)
        {
            if (statusRPSSelecionados != null && statusRPSSelecionados.Any())
            {
                var buscaDeRPS = Domain.RPS.DadosRPSTransacaoRepository.Queryable();
                ht = statusRPSSelecionados.Contains(StatusRpsEnum.NaoEmitido)
                    ? ht.Where(f => !buscaDeRPS.Any(rps => rps.PessoaJuridica.IdPessoa == idPessoaJuridica && rps.Transacao.Id == f.Id)
                                || buscaDeRPS.Any(rps => rps.PessoaJuridica.IdPessoa == idPessoaJuridica
                                                    && rps.Transacao.Id == f.Id && statusRPSSelecionados.Contains(rps.StatusRPS)))
                    : ht.Where(f => buscaDeRPS.Any(rps => rps.PessoaJuridica.IdPessoa == idPessoaJuridica
                                                    && rps.Transacao.Id == f.Id && statusRPSSelecionados.Contains(rps.StatusRPS)));
            }
            return ht;
        }

        private IQueryable<Transacao> FiltroTransacao(IQueryable<Transacao> query, IEnumerable<int> idsTransacao)
        {
            if (idsTransacao != null && idsTransacao.Any())
                query = query.Where(f => idsTransacao.Contains(f.Id));

            query = query.Where(
                f =>
                f.HorariosTransacoes
                    .Any(g => g.ItemPacoteCliente == null ||
                        g.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo)
                    ||
                    (f.Vendas.Any(
                         g =>
                             g.ItensVenda.Any(
                                 h =>
                                     h is ItemVendaPacote &&
                                     ((ItemVendaPacote)h).PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Venda &&
                                     ((ItemVendaPacote)h).PacoteCliente.ItensPacoteCliente.Any(
                                         i => i is ItemPacoteClienteServico))))
                    ||
                    (f.FormasPagamento.Any(g =>
                    g.FormaPagamento == FormaPagamentoEnum.ClubeDeAssinatura ||
                    g.FormaPagamento == FormaPagamentoEnum.ClubeDeAssinaturaPorLink)
                      ));

            return query;
        }

        #endregion Critérios

        public IQueryable<Transacao> ObterTransacoesDePagamentosNaoEstornadosDosEstabelecimentosPorData(DateTime dataDaTransacao)
        {
            var dtInicio = dataDaTransacao.Date;
            return Queryable()
                .Where(t => t.Ativo
                && t.DataHora >= dtInicio.Date
                && t.DataHora < dtInicio.Date.AddDays(1)
                && t.TransacaoQueEstounouEsta == null
                && t.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento
                && !t.FormasPagamento.Any(tfp => tfp.FormaPagamento == FormaPagamentoEnum.DescontoDeProfissional)); 
        }

        public Transacao ObterPorIdTransacao(int idTransacao)
        {
            return Queryable().Where(t => t.Id == idTransacao).FirstOrDefault();
        }

        public bool VerificarSeTransacaoPodeTerSplitGerado(int idTransacao)
        {
            var transacao = ObterPorIdTransacao(idTransacao);
            var transacaoPOS = Domain.Financeiro.TransacaoPOSRepository.Queryable().Where(p => p.Transacao.Id == transacao.Id).FirstOrDefault();
            if (transacaoPOS != null)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
                var estabelecimentoConfiguracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

                if (estabelecimentoConfiguracaoPOS.TipoPOS.ApenasUltimaTransacaoPodeIndicarSplitManual)
                {
                    var ultimoIdTransacaoPOS =
                                        Domain.Financeiro.TransacaoPOSRepository.Queryable()
                                            .Where(p => p.Estabelecimento.IdEstabelecimento == transacaoPOS.Estabelecimento.IdEstabelecimento &&
                                                        p.TipoPOS.Id == transacaoPOS.TipoPOS.Id)
                                            .Max(p => p.Id);

                    if (transacaoPOS.Id != ultimoIdTransacaoPOS)
                        return false;
                }

                if (Domain.Financeiro.TransacaoPOSSplitRepository.ListarTransferKeysPorTransacaoPOS(transacaoPOS.Id).Any())
                    return false;

                var existeFormaDepagamentoComSplitHabilitado =
                        transacao.FormasPagamento.Any(p => p.FormaPagamento.TipoPOS != null && p.FormaPagamento.TipoPOS.HabilitaSplit);

                var estabelecimentoEstaUtilizandoSplitDePagamento =
                        Domain.Pessoas.EstabelecimentoRepository.EstabelecimentoEstaUtilizandoSplitDePagamento(estabelecimento.IdEstabelecimento);

                var transacaoPossuiRegistroDeSplitAtivo =
                        Domain.Despesas.LancamentoRepository.TransacaoPossuiRegistroDeSplitAtivo(transacao.Id);

                var dataMaximaPermissaoParaEmissaoManualDeSplit =
                    transacao.DataHora.AddHours(estabelecimentoConfiguracaoPOS.TipoPOS.LimiteMaximoHorasParaGerarSplitManualmente);

                var algumProfissionalDaTransacaoPossuiRegistroDeSplit = false;
                if (estabelecimentoEstaUtilizandoSplitDePagamento)
                {
                    foreach (var horarioTransacao in transacao.HorariosTransacoes)
                    {
                        var profissionalPrincipal = horarioTransacao.Horario.Profissional;
                        var profissionalAssistente = horarioTransacao.Horario.EstabelecimentoProfissionalAssistente;

                        if (profissionalPrincipal != null)
                        {
                            var estabelecimentoProfissionalEstaUtilizandoSplitPagamento =
                                Domain.Pessoas.EstabelecimentoProfissionalRepository
                                    .EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(profissionalPrincipal.IdProfissional, estabelecimento.IdEstabelecimento);

                            if (estabelecimentoProfissionalEstaUtilizandoSplitPagamento &&
                                horarioTransacao.Comissao != null && horarioTransacao.Comissao.ComissaoParaPagar > 0)
                                algumProfissionalDaTransacaoPossuiRegistroDeSplit = true;
                        }

                        if (profissionalAssistente != null)
                        {
                            var estabelecimentoProfissionalEstaUtilizandoSplitPagamento =
                                Domain.Pessoas.EstabelecimentoProfissionalRepository
                                    .EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(profissionalAssistente.Profissional.IdProfissional, estabelecimento.IdEstabelecimento);

                            if (estabelecimentoProfissionalEstaUtilizandoSplitPagamento &&
                                horarioTransacao.ComissaoAssistente != null && horarioTransacao.ComissaoAssistente.ComissaoParaPagar > 0)
                                algumProfissionalDaTransacaoPossuiRegistroDeSplit = true;
                        }
                    }
                }

                if (transacaoPOS.TipoPOS.Id != (int)SubadquirenteEnum.GranitoPos)
                {
                    return
                           ((!String.IsNullOrEmpty(transacaoPOS.IdentificadorEntreAplicacoes) && !String.IsNullOrEmpty(transacaoPOS.CodigoAutorizacaoTransacaoPDV)) ||
                            (!String.IsNullOrEmpty(transacaoPOS.AcquirerTransactionKey) && !String.IsNullOrEmpty(transacaoPOS.InitiatorTransactionKey))) &&
                           existeFormaDepagamentoComSplitHabilitado &&
                           algumProfissionalDaTransacaoPossuiRegistroDeSplit &&
                           !transacaoPossuiRegistroDeSplitAtivo &&
                           estabelecimentoConfiguracaoPOS.TipoPOS.PermiteGerarSplitManualmente &&
                           dataMaximaPermissaoParaEmissaoManualDeSplit > Calendario.Agora();
                }
                else
                {
                    return
                       (!String.IsNullOrEmpty(transacaoPOS.CodigoAutorizacaoTransacaoPDV) &&
                       existeFormaDepagamentoComSplitHabilitado &&
                       !transacaoPossuiRegistroDeSplitAtivo &&
                       estabelecimentoConfiguracaoPOS.TipoPOS.PermiteGerarSplitManualmente &&
                       dataMaximaPermissaoParaEmissaoManualDeSplit > Calendario.Agora());
                }
            }
            else
            {
                return false;
            }
        }

        public DateTime ObterDataHoraPorIdTransacao(int idTransacao)
        {
            return Queryable()
                .Where(t => t.Id == idTransacao)
                .Select(t => t.DataHora)
                .FirstOrDefault();
        }

        public List<Transacao> ObterTransacoes(int idPessoaJuridica, EstabelecimentoFormaPagamento estabelecimentoFormaPagamento)
        {
            return Queryable().Where(t => t.PessoaQueRecebeu.IdPessoa == idPessoaJuridica &&
                                     t.FormasPagamento.Any(fp => fp.FormaPagamento.Id == estabelecimentoFormaPagamento.FormaPagamento.Id))
                                     .ToList();
        }

        public List<DadosTransacaoAssinaturaClubeDTO> ObterDadosTransacoesAssinaturasClube(IQueryable<Transacao> query)
        {

            var transacaoItem = Domain.Financeiro.TransacaoItemRepository.Queryable();
            return (from tran in query
                    join tItem in transacaoItem on tran.Id equals tItem.Transacao.Id
                    where tItem.Tipo == TransacaoItemTipo.PagamentoDeAssinatura
                    select new DadosTransacaoAssinaturaClubeDTO
                    {
                        IdTransacao = tran.Id,
                        Descricao = tItem.Nome,
                        Valor = tItem.Valor,
                        IdPagamentoAssinatura = tItem.IdObjetoReferencia
                    }).ToList();
        }

        public List<Transacao> ListarPorIds(List<int> idsTransacoes)
        {
            if (idsTransacoes == null || idsTransacoes.Count == 0)
                return new List<Transacao>();

            var predicate = idsTransacoes
                .Select(id => (Expression<Func<Transacao, bool>>)(t => t.Id == id))
                .Aggregate((prev, next) => prev.Or(next));

            var transacoes = Queryable()
                .Where(predicate)
                .ToList();

            return transacoes;
        }


    }
}