﻿using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using System;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Repositories
{
    public partial class TransacaoFormaPagamentoParcelaRepository : ITransacaoFormaPagamentoParcelaRepository
    {
        private const int IdFormaPagamentoDescontarProfissional = (int)FormaPagamentoEnum.DescontoDeProfissional;
        public IQueryable<TransacaoFormaPagamentoParcela> ListarComDescontoProfissional(ParametrosFiltrosRelatorio parametros)
        {
            var retorno = Queryable();

            retorno = retorno.Where(f => !f.TransacaoFormaPagamento.FormaPagamento.ExibirTransacoesPagasNosRelatorios);

            retorno = retorno.Where(f => f.TransacaoFormaPagamento.Transacao.PessoaQueRecebeu == parametros.Estabelecimento.PessoaJuridica);

            if (parametros.IdProfissional > 0)
            {
                var profissional = Domain.Pessoas.ProfissionalRepository.Load(parametros.IdProfissional);
                retorno = retorno.Where(f => f.TransacaoFormaPagamento.Transacao.PessoaQuePagou == profissional.PessoaFisica);
            }

            if (parametros.IdsProfissional.Any())
            {
                var idsPessoas = Domain.Pessoas.ProfissionalRepository.Queryable().Where(p => parametros.IdsProfissional.Contains(p.IdProfissional)).Select(p => p.PessoaFisica.IdPessoa).ToList();
                retorno = retorno.Where(f => idsPessoas.Contains(f.TransacaoFormaPagamento.Transacao.PessoaQuePagou.IdPessoa));
            }

            if (!parametros.ExibirEstornos)
                retorno = FiltrarPagamentosNaoEstornados(retorno);
            else
                retorno = retorno.Where(f => (f.TransacaoFormaPagamento.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento && f.TransacaoFormaPagamento.Transacao.TransacaoQueEstounouEsta == null)
                || (f.TransacaoFormaPagamento.Transacao.TipoTransacao.Id != (int)TipoTransacaoEnum.Pagamento && f.TransacaoFormaPagamento.Transacao.TransacaoQueEstounouEsta != null && f.TransacaoFormaPagamento.FormaPagamento.Id != IdFormaPagamentoDescontarProfissional));

            if (parametros.DataInicial.HasValue)
                retorno = retorno.Where(f => f.DataPagamento >= parametros.DataInicial.Value.Date);

            if (parametros.DataFinal.HasValue)
                retorno = retorno.Where(f => f.DataPagamento < parametros.DataFinal.Value.AddDays(1).Date);

            if (parametros.IdRelacaoProfissional > 0)
            {
                var buscaDeProfissionaisComRelacao = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(ep => ep.FormaRelacaoProfissional.Id == parametros.IdRelacaoProfissional);
                retorno = retorno.Where(v => buscaDeProfissionaisComRelacao.Any(p => v.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa == p.Profissional.PessoaFisica.IdPessoa && p.Estabelecimento.IdEstabelecimento == parametros.IdEstabelecimento));
            }

            return retorno;
        }

        public IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosAbertas(int idEstabelecimento, DateTime dataInicio,
            DateTime dataFim, int idPessoaEstabelecimento, int idPessoaQuePagou)
        {
            var query = ObterQueryVendaDeProdutosParaProfissionalAbertas(idEstabelecimento, dataInicio, dataFim, idPessoaEstabelecimento);
            query = query.Where(tp => tp.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa == idPessoaQuePagou);
            return query;
        }

        public IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosParaProfissionalAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarPagamentosNaoEstornados(query);
            query = FiltrarPagamentosComDescontoDeProfissional(query);
            query = FiltrarPorIdPessoaEstabelecimento(query, idPessoaEstabelecimento);
            query = FiltrarPorDataPagamento(query, dataInicio, dataFim);

            var folhaItem = Domain.Financeiro.FolhaPagamentoCompraProdutoRepository.ObterQueryAtivoPorPessoaEstabelecimento(idPessoaEstabelecimento);

            var queryAbertas = (from p in query
                             where p.Valor > 0 
                             && !folhaItem.Any(f => f.TransacaoFormaPagamentoParcela.Id == p.Id)
                             select p);

            return queryAbertas;
        }

        public IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosPagas(int idEstabelecimento, DateTime dataInicio,
                DateTime dataFim, int idPessoaEstabelecimento, int idPessoaQuePagou)
        {
            var query = ObterQueryVendaDeProdutosParaProfissionalPagas(idEstabelecimento, dataInicio, dataFim, idPessoaEstabelecimento);
            query = query.Where(tp => tp.TransacaoFormaPagamento.PessoaQuePagou.IdPessoa == idPessoaQuePagou);
            return query;
        }

        public IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosParaProfissionalPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarPagamentosNaoEstornados(query);
            query = FiltrarPagamentosComDescontoDeProfissional(query);
            query = FiltrarPorIdPessoaEstabelecimento(query, idPessoaEstabelecimento);
            query = FiltrarPorDataPagamento(query, dataInicio, dataFim);

            var folhaItem = Domain.Financeiro.FolhaPagamentoCompraProdutoRepository.ObterQueryAtivoPorPessoaEstabelecimento(idPessoaEstabelecimento);

            var queryPaga = (from p in query
                        where p.Valor > 0 &&
                              folhaItem.Any(f => f.TransacaoFormaPagamentoParcela.Id == p.Id)
                        select p);

            return queryPaga;
        }

        public decimal ObterValorDescontoProfissionalVendaProdutoNoPeriodo(FiltroDescontoCompraProdutoDTO filtro)
        {
            var query = Queryable();
            query = FiltrarPagamentosNaoEstornados(query);
            query = FiltrarPagamentosComDescontoDeProfissional(query);
            query = FiltrarPessoaQuePagou(query, filtro.IdPessoaProfissional);
            query = FiltrarPorIdPessoaEstabelecimento(query, filtro.IdPessoaEstabelecimento);

            query = query.Where(p => p.DataPagamento > filtro.DataInicial && p.DataPagamento < filtro.DataFinal);
            //Não foi possível utilizar as datas no padrão correto estabelecido.
            //Motivo: A data está vindo do front e sendo utilizada amplamente para chamar outros método,
            //Cuidado: caso for usar esse método, tenha cuidado com o formato das datas.

            decimal valorParcelasNoPeriodo = query.Sum(p => (decimal?)p.Valor).GetValueOrDefault();

            return valorParcelasNoPeriodo;
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> FiltrarPagamentosNaoEstornados(IQueryable<TransacaoFormaPagamentoParcela> query)
        {
            return query.Where(q => q.TransacaoFormaPagamento.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento &&
                             q.TransacaoFormaPagamento.Transacao.TransacaoQueEstounouEsta == null);
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> FiltrarPagamentosComDescontoDeProfissional(IQueryable<TransacaoFormaPagamentoParcela> query)
        {
            return query.Where(q => 
                    q.TransacaoFormaPagamento.FormaPagamento.Id == IdFormaPagamentoDescontarProfissional
                    && q.TransacaoFormaPagamento.Transacao.FoiVendaParaProfissional);
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> FiltrarPessoaQuePagou(IQueryable<TransacaoFormaPagamentoParcela> query, int idPessoaQuePagou)
        {
            return query.Where(q => q.TransacaoFormaPagamento.Transacao.PessoaQuePagou.IdPessoa == idPessoaQuePagou);
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> FiltrarPorIdPessoaEstabelecimento(IQueryable<TransacaoFormaPagamentoParcela> query, int idPessoaEstalecimento)
        {
            return query.Where(q => q.TransacaoFormaPagamento.Transacao.PessoaQueRecebeu.IdPessoa.Equals(idPessoaEstalecimento));
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> FiltrarPorDataPagamento(IQueryable<TransacaoFormaPagamentoParcela> query, DateTime dataInicial, DateTime dataFinal)
        {
            return query.Where(q => q.DataPagamento >= dataInicial.Date 
                    && q.DataPagamento < dataFinal.Date.AddDays(1));
        }
    }
}