﻿using Nest;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Repositories
{

    public partial class TransacaoPosWebhookRequestRepository : ITransacaoPosWebhookRequestRepository
    {

        public TransacaoPosWebhookRequest ObterTransacaoPosWebhookRequestPorIdTransacao(int idTransacao)
        {
            var query = Queryable(true).Where(p => p.Transacao.Id == idTransacao);
            return query.FirstOrDefault();
        }

        public TransacaoPosWebhookRequest ObterTransacaoPosWebhookRequestPorIdTransacaoPos(int idTransacaoPos)
        {
            var query = Queryable(true)
                .Where(p => p.TransacaoPos.Id == idTransacaoPos);
            return query.FirstOrDefault();
        }

        public TransacaoPosWebhookRequest ObterTransacaoPosWebhookRequestPorIdExternoTransacao(string idExternoTransacao)
        {
            var query = Queryable(true).Where(p => p.CodigoTransacao == idExternoTransacao);

            return query.FirstOrDefault();
        }

        public DateTime? ObterDataCriacaoTransacaoPosWebhookRequestPorStoneTransactionId(string stoneTransactionId)
        {
            var dado = Queryable(true).Where(p => p.StoneTransactionId == stoneTransactionId).Select(r => r.DataCriacao).Take(1).ToList();

            return dado.Any() ? dado.First().Value : (DateTime?)null;
        }

        public DateTime? ObterDataCriacaoTransacaoPosWebhookRequestPorTransacaoPosId(int transacaoPosId)
        {
            var dado = Queryable(true).Where(p => p.TransacaoPos.Id == transacaoPosId).Select(r => r.DataCriacao).Take(1).ToList();

            return dado.Any() ? dado.First().Value : (DateTime?)null;
        }

        public TransacaoPosWebhookRequest ObterTransacaoAbertaPosWebhookRequestPorChaveDiariaTransacao(int idEstabelecimento, int idCliente, int chaveDiaria)
        {
            var startDateTime = DateTime.Today; //Hoje 00:00:00
            var endDateTime = DateTime.Today.AddDays(1).AddTicks(-1); //Hoje 23:59:59

            var query = Queryable().Where(p => p.ChaveDiariaTransacao == chaveDiaria && p.Estabelecimento.IdEstabelecimento == idEstabelecimento
            && p.Cliente.IdCliente == idCliente && p.DataCriacao >= startDateTime && p.DataCriacao <= endDateTime);

            return query.FirstOrDefault();
        }

        public List<TransacaoPosWebhookRequest> ListarTransacaoAbertaPosWebhookRequestPorEstabelecimento(int idEstabelecimento)
        {
            var startDateTime = DateTime.Now.AddMinutes(-3); //Hoje 00:00:00
            var endDateTime = DateTime.Now.AddMinutes(3); //Hoje 23:59:59

            var query = Queryable(true).Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento
            && !p.FoiPago && !p.OperacaoCancelada && !p.PagamentoEstornado
            && p.DataCriacao >= startDateTime && p.DataCriacao <= endDateTime);
            return query.ToList();
        }

        public TransacaoPosWebhookRequest ObterTransacaoAbertaPosWebhookRequestPorChaveDiariaTransacaoECnpj(string cnpj, int chaveDiaria)
        {
            var startDateTime = DateTime.Today; //Hoje 00:00:00
            var endDateTime = DateTime.Today.AddDays(1).AddTicks(-1); //Hoje 23:59:59

            var query = Queryable(true).Where(p => p.ChaveDiariaTransacao == chaveDiaria
            && !p.FoiPago && !p.OperacaoCancelada && !p.PagamentoEstornado
            && p.CNPJ == cnpj && p.DataCriacao >= startDateTime && p.DataCriacao <= endDateTime);
            return query.FirstOrDefault();
        }

        public TransacaoPosWebhookRequest ObterTransacaoAbertaPosWebhookRequestPorIdOperacaoECnpj(int idEstabelecimento, string cnpj, string idOperacao)
        {
            var query = Queryable(true).Where(p => p.IdOperacaoPos == idOperacao
            && !p.FoiPago && !p.OperacaoCancelada && !p.PagamentoEstornado
            && p.CNPJ == cnpj && p.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return query.FirstOrDefault();
        }
        public TransacaoPosWebhookRequest ObterTransacaoAbertaPosWebhookRequestPorCodigoTransacaoEEstabelecimento(int idEstabelecimento, string codigoTransacao)
        {
            var query = Queryable(true).Where(p => p.CodigoTransacao == codigoTransacao
                && p.FoiPago && !p.OperacaoCancelada && !p.PagamentoEstornado
                && p.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return query.FirstOrDefault();
        }
        public int ObterChaveDiariaTransacao(int idEstabelecimento)
        {
            var startDateTime = DateTime.Today; //Hoje 00:00:00
            var endDateTime = DateTime.Today.AddDays(1).AddTicks(-1); //Hoje 23:59:59

            var resultado = Queryable(true).OrderByDescending(o => o.DataCriacao).FirstOrDefault(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento
            && p.DataCriacao >= startDateTime && p.DataCriacao <= endDateTime);

            var chaveDiaria = 0;

            if (resultado != null)
                chaveDiaria = resultado.ChaveDiariaTransacao + 1;
            else
                chaveDiaria = 1;

            return chaveDiaria;
        }

        public List<TransacaoPOSWebhookPosRequestDTO> ListarTransacoesDeHojeQueQueForamFeitasComConnectENaoForamPagasEForamCriadasAMaisDeDoisMinutos()
        {
            var agora = Calendario.Agora();
            var doisMinutosAtras = agora.AddMinutes(-2);

            return Queryable(true)
                .Select(t => new TransacaoPOSWebhookPosRequestDTO()
                {
                    Id = t.Id,
                    FoiPago = t.FoiPago,
                    OperacaoCancelada = t.OperacaoCancelada,
                    PagamentoEstornado = t.PagamentoEstornado,
                    DataCriacao = t.DataCriacao,
                    IdTipoPos = t.TipoPos.Id,
                    IdEstabelecimento = t.Estabelecimento.IdEstabelecimento,
                    ExternalTransactionId = t.ExternalTransactionId
                })
                .Where(p =>
                    !p.FoiPago &&                  
                    p.DataCriacao <= doisMinutosAtras &&
                    p.DataCriacao >= agora.Date &&
                    (
                        p.IdTipoPos == (int)SubadquirenteEnum.ConnectPagarme ||
                        p.IdTipoPos == (int)SubadquirenteEnum.ConnectStone)
                ).ToList();
        }
    }
}