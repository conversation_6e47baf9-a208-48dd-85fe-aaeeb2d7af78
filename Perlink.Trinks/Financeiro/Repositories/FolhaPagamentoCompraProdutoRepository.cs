﻿using System.Linq;

namespace Perlink.Trinks.Financeiro.Repositories
{
    public partial class FolhaPagamentoCompraProdutoRepository : IFolhaPagamentoCompraProdutoRepository
    {
        public IQueryable<FolhaPagamentoCompraProduto> ObterQueryAtivoPorPessoaEstabelecimento(int idPessoaEstabelecimento)
        {
            return Queryable()
                    .Where(p => p.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa == idPessoaEstabelecimento
                        && p.Ativo);
        }
    }
}
