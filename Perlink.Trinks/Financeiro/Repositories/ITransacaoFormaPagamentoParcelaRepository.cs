﻿using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Linq;

namespace Perlink.Trinks.Financeiro.Repositories
{
    public partial interface ITransacaoFormaPagamentoParcelaRepository
    {
        IQueryable<TransacaoFormaPagamentoParcela> ListarComDescontoProfissional(ParametrosFiltrosRelatorio parametros);
        IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosParaProfissionalAbertas(
            int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaEstabelecimento);
        IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosParaProfissionalPagas(int idEstabelecimento,
            DateTime dataInicio, DateTime dataFim, int idPessoaEstabelecimento);
        IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosAbertas(int idEstabelecimento,
            DateTime dataInicio,
            DateTime dataFim, int idPessoaEstabelecimento, int idPessoaQuePagou);
        IQueryable<TransacaoFormaPagamentoParcela> ObterQueryVendaDeProdutosPagas(int idEstabelecimento,
            DateTime dataInicio,
            DateTime dataFim, int idPessoaEstabelecimento, int idPessoaQuePagou);

        decimal ObterValorDescontoProfissionalVendaProdutoNoPeriodo(FiltroDescontoCompraProdutoDTO filtro);
    }
}