﻿using Castle.ActiveRecord;
using Perlink.Trinks.Cashback;
using Perlink.Trinks.Pessoas;
using System;

namespace Perlink.Trinks.Financeiro
{

    [ActiveRecord("Comissao_Valor_A_Receber", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class ValorDeComissaoAReceber : ActiveRecordBase<ValorDeComissaoAReceber>
    {

        public ValorDeComissaoAReceber()
        {
            Ativo = true;
            DataDeCadastro = Calendario.Agora();
        }

        public ValorDeComissaoAReceber(int idPessoaEstabelecimento) : this()
        {
            IdPessoaEstabelecimento = idPessoaEstabelecimento;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("dt_comissao_receber")]
        public virtual DateTime DataDaComissaoAReceber { get; set; }

        #region Proporcionais

        [Property("valor_base_proporcional")]
        public virtual Decimal ValorBaseProporcional { get; set; }

        [Property("valor_bruto_proporcional")]
        public virtual decimal ValorBrutoProporcional { get; set; }

        [Property("desconto_operadora_proporcional")]
        public virtual Decimal DescontoOperadoraProporcional { get; set; }

        [Property("desconto_extra_proporcional")]
        public virtual Decimal DescontoExtraProporcional { get; set; }

        [Property("desconto_cliente_proporcional")]
        public virtual decimal DescontoClienteProporcional { get; set; }

        #endregion

        [Property("valor_comissao_a_pagar")]
        public virtual decimal Valor { get; set; }

        [BelongsTo("id_comissao", Lazy = FetchWhen.OnInvoke)]
        public virtual Comissao Comissao { get; set; }

        [BelongsTo("id_transacao_parcela", Lazy = FetchWhen.OnInvoke)]
        public virtual TransacaoFormaPagamentoParcela TransacaoFormaPagamentoParcela { get; set; }

        [BelongsTo("id_estabelecimento_folha_profissional", Lazy = FetchWhen.OnInvoke)]
        public virtual FechamentoFolhaMesProfissional FechamentoProfisisonal { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }

        [Property("dt_cadastro")]
        public virtual DateTime DataDeCadastro { get; set; }

        [Property("id_pessoa_estabelecimento", NotNull = true)]
        public virtual int IdPessoaEstabelecimento { get; set; }

        #region Controle de Alteração

        [Property("valor_esta_alterado_manualmente")]
        public virtual bool ValorAlteradoManualmente { get; set; }

        [Property("valor_comissao_a_pagar_original")]
        public virtual decimal? ValorOriginal { get; set; }

        [Property("dt_hora_ultima_alteracao_manual_valor_comissao")]
        public virtual DateTime? DataHoraUltimaAlteracaoManualDeValor { get; set; }

        [BelongsTo("id_pessoa_ultima_alteracao_manual_valor_comissao", Lazy = FetchWhen.OnInvoke)]
        public virtual PessoaFisica ResponsavelPelaUltimaAlteracaoManualDeValor { get; set; }

        [Property("dt_comissao_receber_esta_alterado_manualmente")]
        public virtual bool DataDaComissaoAReceberAlteradaManualmente { get; set; }

        [Property("dt_comissao_receber_original")]
        public virtual DateTime? DataDaComissaoAReceberOriginal { get; set; }

        [Property("dt_hora_ultima_alteracao_manual_dt_comissao")]
        public virtual DateTime? DataHoraUltimaAlteracaoManualDeDataDaComissaoAReceber { get; set; }

        [BelongsTo("id_pessoa_ultima_alteracao_manual_dt_comissao", Lazy = FetchWhen.OnInvoke)]
        public virtual Pessoa ResponsavelPelaUltimaAlteracaoManualDeDataDaComissaoAReceber { get; set; }

        #endregion Controle de Alteração

        #region Cashback
        private CashbackComissaoValorAReceber _cashback = null;
        public virtual void AtribuirCashbackSemPersistencia(CashbackComissaoValorAReceber cashback)
        {
            _cashback = cashback;
        }

        public virtual bool PossuiCashbackNaoPersistido()
        {
            return _cashback != null;
        }

        public virtual CashbackComissaoValorAReceber ObterCashbackNaoPersistido()
        {
            return _cashback;
        }
        #endregion

        public virtual void EstornarPagamento()
        {
            FechamentoProfisisonal = null;
        }
    }
}