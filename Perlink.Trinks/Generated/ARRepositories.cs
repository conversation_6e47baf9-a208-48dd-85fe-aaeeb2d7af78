using Perlink.DomainInfrastructure.Repositories;
namespace Perlink.Trinks.Autoatendimento.Repositories {

	public partial class CheckInEstablishmentsRepository : BaseActiveRecordRepository<CheckInEstablishments>, ICheckInEstablishmentsRepository {
		public Perlink.Trinks.Autoatendimento.Factories.ICheckInEstablishmentsFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Autoatendimento.Factories.ICheckInEstablishmentsFactory>();	}
		}			
	}
	public partial class ConfigurationsEstablishmentRepository : BaseActiveRecordRepository<ConfigurationsEstablishment>, IConfigurationsEstablishmentRepository {
		public Perlink.Trinks.Autoatendimento.Factories.IConfigurationsEstablishmentFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Autoatendimento.Factories.IConfigurationsEstablishmentFactory>();	}
		}			
	}
	public partial class StatusServiceCustomerEstablishmentRepository : BaseActiveRecordRepository<StatusServiceCustomerEstablishment>, IStatusServiceCustomerEstablishmentRepository {
		public Perlink.Trinks.Autoatendimento.Factories.IStatusServiceCustomerEstablishmentFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Autoatendimento.Factories.IStatusServiceCustomerEstablishmentFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Autoatendimento.DTO.Repositories {

 
}
namespace Perlink.Trinks.Autoatendimento.Enums.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.BaseIBPT.Repositories {

	public partial class DadosIBPTRepository : BaseActiveRecordRepository<DadosIBPT>, IDadosIBPTRepository {
		public Perlink.Trinks.BaseIBPT.Factories.IDadosIBPTFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.BaseIBPT.Factories.IDadosIBPTFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Repositories {

	public partial class BandeiraCartaoMDRRepository : BaseActiveRecordRepository<BandeiraCartaoMDR>, IBandeiraCartaoMDRRepository {
		public Perlink.Trinks.Belezinha.Factories.IBandeiraCartaoMDRFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.IBandeiraCartaoMDRFactory>();	}
		}			
	}
	public partial class CredenciamentoRepository : BaseActiveRecordRepository<Credenciamento>, ICredenciamentoRepository {
		public Perlink.Trinks.Belezinha.Factories.ICredenciamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.ICredenciamentoFactory>();	}
		}			
	}
	public partial class CredenciamentoComStoneCodeRepository : BaseActiveRecordRepository<CredenciamentoComStoneCode>, ICredenciamentoComStoneCodeRepository {
		public Perlink.Trinks.Belezinha.Factories.ICredenciamentoComStoneCodeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.ICredenciamentoComStoneCodeFactory>();	}
		}			
	}
	public partial class EstabelecimentoTerminalPosRepository : BaseActiveRecordRepository<EstabelecimentoTerminalPos>, IEstabelecimentoTerminalPosRepository {
		public Perlink.Trinks.Belezinha.Factories.IEstabelecimentoTerminalPosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.IEstabelecimentoTerminalPosFactory>();	}
		}			
	}
	public partial class HierarquiaRepository : BaseActiveRecordRepository<Hierarquia>, IHierarquiaRepository {
		public Perlink.Trinks.Belezinha.Factories.IHierarquiaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.IHierarquiaFactory>();	}
		}			
	}
	public partial class MccRepository : BaseActiveRecordRepository<Mcc>, IMccRepository {
		public Perlink.Trinks.Belezinha.Factories.IMccFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.IMccFactory>();	}
		}			
	}
	public partial class TaxaAntecipacaoRepository : BaseActiveRecordRepository<TaxaAntecipacao>, ITaxaAntecipacaoRepository {
		public Perlink.Trinks.Belezinha.Factories.ITaxaAntecipacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.ITaxaAntecipacaoFactory>();	}
		}			
	}
	public partial class TaxaMDRRepository : BaseActiveRecordRepository<TaxaMDR>, ITaxaMDRRepository {
		public Perlink.Trinks.Belezinha.Factories.ITaxaMDRFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.ITaxaMDRFactory>();	}
		}			
	}
	public partial class TpvMensalRepository : BaseActiveRecordRepository<TpvMensal>, ITpvMensalRepository {
		public Perlink.Trinks.Belezinha.Factories.ITpvMensalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.ITpvMensalFactory>();	}
		}			
	}
	public partial class TransacaoAvulsaPOSWebhookRequestRepository : BaseActiveRecordRepository<TransacaoAvulsaPOSWebhookRequest>, ITransacaoAvulsaPOSWebhookRequestRepository {
		public Perlink.Trinks.Belezinha.Factories.ITransacaoAvulsaPOSWebhookRequestFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Belezinha.Factories.ITransacaoAvulsaPOSWebhookRequestFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Belezinha.DTO.AgendaDeRecebiveis.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Enums.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Filters.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Helpers.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Strategies.Pagarme.Repositories {

 
}
namespace Perlink.Trinks.Caching.Repositories {

 
}
namespace Perlink.Trinks.Cashback.Repositories {

	public partial class BonusTransacaoRepository : BaseActiveRecordRepository<BonusTransacao>, IBonusTransacaoRepository {
		public Perlink.Trinks.Cashback.Factories.IBonusTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.IBonusTransacaoFactory>();	}
		}			
	}
	public partial class CashbackComissaoRepository : BaseActiveRecordRepository<CashbackComissao>, ICashbackComissaoRepository {
		public Perlink.Trinks.Cashback.Factories.ICashbackComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.ICashbackComissaoFactory>();	}
		}			
	}
	public partial class CashbackComissaoValorAReceberRepository : BaseActiveRecordRepository<CashbackComissaoValorAReceber>, ICashbackComissaoValorAReceberRepository {
		public Perlink.Trinks.Cashback.Factories.ICashbackComissaoValorAReceberFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.ICashbackComissaoValorAReceberFactory>();	}
		}			
	}
	public partial class CashbackHorarioTransacaoRepository : BaseActiveRecordRepository<CashbackHorarioTransacao>, ICashbackHorarioTransacaoRepository {
		public Perlink.Trinks.Cashback.Factories.ICashbackHorarioTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.ICashbackHorarioTransacaoFactory>();	}
		}			
	}
	public partial class CashbackItemVendaRepository : BaseActiveRecordRepository<CashbackItemVenda>, ICashbackItemVendaRepository {
		public Perlink.Trinks.Cashback.Factories.ICashbackItemVendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.ICashbackItemVendaFactory>();	}
		}			
	}
	public partial class CashbackTransacaoRepository : BaseActiveRecordRepository<CashbackTransacao>, ICashbackTransacaoRepository {
		public Perlink.Trinks.Cashback.Factories.ICashbackTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.ICashbackTransacaoFactory>();	}
		}			
	}
	public partial class EstabelecimentoDadosIntegracaoRepository : BaseActiveRecordRepository<EstabelecimentoDadosIntegracao>, IEstabelecimentoDadosIntegracaoRepository {
		public Perlink.Trinks.Cashback.Factories.IEstabelecimentoDadosIntegracaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cashback.Factories.IEstabelecimentoDadosIntegracaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Cashback.DTO.CrmBonus.Repositories {

 
}
namespace Perlink.Trinks.Cashback.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.Repositories {

	public partial class AnotacaoRepository : BaseActiveRecordRepository<Anotacao>, IAnotacaoRepository {
		public Perlink.Trinks.ClientesAcompanhamentos.Factories.IAnotacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClientesAcompanhamentos.Factories.IAnotacaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.Stories.Repositories {

 
}
namespace Perlink.Trinks.ClientesAnexos.Repositories {

	public partial class ClienteAnexoRepository : BaseActiveRecordRepository<ClienteAnexo>, IClienteAnexoRepository {
		public Perlink.Trinks.ClientesAnexos.Factories.IClienteAnexoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClientesAnexos.Factories.IClienteAnexoFactory>();	}
		}			
	}
	public partial class MeuAnexoRepository : BaseActiveRecordRepository<MeuAnexo>, IMeuAnexoRepository {
		public Perlink.Trinks.ClientesAnexos.Factories.IMeuAnexoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClientesAnexos.Factories.IMeuAnexoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ClientesAnexos.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClientesAnexos.Enums.Repositories {

 
}
namespace Perlink.Trinks.ClientesAnexos.ObjetosDeValor.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories {

	public partial class AssinaturaDoClienteRepository : BaseActiveRecordRepository<AssinaturaDoCliente>, IAssinaturaDoClienteRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IAssinaturaDoClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IAssinaturaDoClienteFactory>();	}
		}			
	}
	public partial class BeneficioRepository : BaseActiveRecordRepository<Beneficio>, IBeneficioRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioFactory>();	}
		}			
	}
	public partial class BeneficioDaAssinaturaRepository : BaseActiveRecordRepository<BeneficioDaAssinatura>, IBeneficioDaAssinaturaRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioDaAssinaturaFactory>();	}
		}			
	}
	public partial class BeneficioDoPlanoRepository : BaseActiveRecordRepository<BeneficioDoPlano>, IBeneficioDoPlanoRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioDoPlanoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioDoPlanoFactory>();	}
		}			
	}
	public partial class BeneficioProdutoRepository : BaseActiveRecordRepository<BeneficioProduto>, IBeneficioProdutoRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioProdutoFactory>();	}
		}			
	}
	public partial class BeneficioServicoRepository : BaseActiveRecordRepository<BeneficioServico>, IBeneficioServicoRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioServicoFactory>();	}
		}			
	}
	public partial class BeneficioUsadoRepository : BaseActiveRecordRepository<BeneficioUsado>, IBeneficioUsadoRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioUsadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioUsadoFactory>();	}
		}			
	}
	public partial class ContratoDeAdesaoRepository : BaseActiveRecordRepository<ContratoDeAdesao>, IContratoDeAdesaoRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IContratoDeAdesaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IContratoDeAdesaoFactory>();	}
		}			
	}
	public partial class HistoricoDeStatusAssinaturaDoClubeRepository : BaseActiveRecordRepository<HistoricoDeStatusAssinaturaDoClube>, IHistoricoDeStatusAssinaturaDoClubeRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IHistoricoDeStatusAssinaturaDoClubeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IHistoricoDeStatusAssinaturaDoClubeFactory>();	}
		}			
	}
	public partial class IntencaoEdicaoDoPlanoClienteRepository : BaseActiveRecordRepository<IntencaoEdicaoDoPlanoCliente>, IIntencaoEdicaoDoPlanoClienteRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IIntencaoEdicaoDoPlanoClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IIntencaoEdicaoDoPlanoClienteFactory>();	}
		}			
	}
	public partial class LinkDePagamentoDaAssinaturaRepository : BaseActiveRecordRepository<LinkDePagamentoDaAssinatura>, ILinkDePagamentoDaAssinaturaRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.ILinkDePagamentoDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.ILinkDePagamentoDaAssinaturaFactory>();	}
		}			
	}
	public partial class LinkDePagamentoDoCancelamentoDaAssinaturaRepository : BaseActiveRecordRepository<LinkDePagamentoDoCancelamentoDaAssinatura>, ILinkDePagamentoDoCancelamentoDaAssinaturaRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.ILinkDePagamentoDoCancelamentoDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.ILinkDePagamentoDoCancelamentoDaAssinaturaFactory>();	}
		}			
	}
	public partial class PagamentoDeAssinaturaRepository : BaseActiveRecordRepository<PagamentoDeAssinatura>, IPagamentoDeAssinaturaRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IPagamentoDeAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IPagamentoDeAssinaturaFactory>();	}
		}			
	}
	public partial class PagamentoMultaDeCancelamentoDaAssinaturaRepository : BaseActiveRecordRepository<PagamentoMultaDeCancelamentoDaAssinatura>, IPagamentoMultaDeCancelamentoDaAssinaturaRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IPagamentoMultaDeCancelamentoDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IPagamentoMultaDeCancelamentoDaAssinaturaFactory>();	}
		}			
	}
	public partial class PlanoClienteRepository : BaseActiveRecordRepository<PlanoCliente>, IPlanoClienteRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IPlanoClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IPlanoClienteFactory>();	}
		}			
	}
	public partial class VendaOnlineRepository : BaseActiveRecordRepository<VendaOnline>, IVendaOnlineRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IVendaOnlineFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IVendaOnlineFactory>();	}
		}			
	}
	public partial class VigenciaDeAssinaturaRepository : BaseActiveRecordRepository<VigenciaDeAssinatura>, IVigenciaDeAssinaturaRepository {
		public Perlink.Trinks.ClubeDeAssinaturas.Factories.IVigenciaDeAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ClubeDeAssinaturas.Factories.IVigenciaDeAssinaturaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Calculos.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Enums.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Repositories {

	public partial class AdicionalCobradoRepository : BaseActiveRecordRepository<AdicionalCobrado>, IAdicionalCobradoRepository {
		public Perlink.Trinks.Cobranca.Factories.IAdicionalCobradoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IAdicionalCobradoFactory>();	}
		}			
	}
	public partial class AdicionalNaAssinaturaRepository : BaseActiveRecordRepository<AdicionalNaAssinatura>, IAdicionalNaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IAdicionalNaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IAdicionalNaAssinaturaFactory>();	}
		}			
	}
	public partial class AgendamentoDeMigracaoDoPlanoRepository : BaseActiveRecordRepository<AgendamentoDeMigracaoDoPlano>, IAgendamentoDeMigracaoDoPlanoRepository {
		public Perlink.Trinks.Cobranca.Factories.IAgendamentoDeMigracaoDoPlanoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IAgendamentoDeMigracaoDoPlanoFactory>();	}
		}			
	}
	public partial class AssinaturaRepository : BaseActiveRecordRepository<Assinatura>, IAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IAssinaturaFactory>();	}
		}			
	}
	public partial class BeneficioDoPlanoAssinaturaRepository : BaseActiveRecordRepository<BeneficioDoPlanoAssinatura>, IBeneficioDoPlanoAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IBeneficioDoPlanoAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IBeneficioDoPlanoAssinaturaFactory>();	}
		}			
	}
	public partial class BeneficioDoPlanoMeuPlanoRepository : BaseActiveRecordRepository<BeneficioDoPlanoMeuPlano>, IBeneficioDoPlanoMeuPlanoRepository {
		public Perlink.Trinks.Cobranca.Factories.IBeneficioDoPlanoMeuPlanoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IBeneficioDoPlanoMeuPlanoFactory>();	}
		}			
	}
	public partial class ContaFinanceiraRepository : BaseActiveRecordRepository<ContaFinanceira>, IContaFinanceiraRepository {
		public Perlink.Trinks.Cobranca.Factories.IContaFinanceiraFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IContaFinanceiraFactory>();	}
		}			
	}
	public partial class DadosSalesRepository : BaseActiveRecordRepository<DadosSales>, IDadosSalesRepository {
		public Perlink.Trinks.Cobranca.Factories.IDadosSalesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IDadosSalesFactory>();	}
		}			
	}
	public partial class DescontoNaAssinaturaRepository : BaseActiveRecordRepository<DescontoNaAssinatura>, IDescontoNaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IDescontoNaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IDescontoNaAssinaturaFactory>();	}
		}			
	}
	public partial class DescontoNoAdicionalDaAssinaturaRepository : BaseActiveRecordRepository<DescontoNoAdicionalDaAssinatura>, IDescontoNoAdicionalDaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IDescontoNoAdicionalDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IDescontoNoAdicionalDaAssinaturaFactory>();	}
		}			
	}
	public partial class DescontoNoPlanoDaAssinaturaRepository : BaseActiveRecordRepository<DescontoNoPlanoDaAssinatura>, IDescontoNoPlanoDaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IDescontoNoPlanoDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IDescontoNoPlanoDaAssinaturaFactory>();	}
		}			
	}
	public partial class DorDoClienteRepository : BaseActiveRecordRepository<DorDoCliente>, IDorDoClienteRepository {
		public Perlink.Trinks.Cobranca.Factories.IDorDoClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IDorDoClienteFactory>();	}
		}			
	}
	public partial class DorDoClienteNaAssinaturaRepository : BaseActiveRecordRepository<DorDoClienteNaAssinatura>, IDorDoClienteNaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IDorDoClienteNaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IDorDoClienteNaAssinaturaFactory>();	}
		}			
	}
	public partial class EstabelecimentoParceriasTrinksRepository : BaseActiveRecordRepository<EstabelecimentoParceriasTrinks>, IEstabelecimentoParceriasTrinksRepository {
		public Perlink.Trinks.Cobranca.Factories.IEstabelecimentoParceriasTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IEstabelecimentoParceriasTrinksFactory>();	}
		}			
	}
	public partial class ExperimentacaoEstabelecimentoRepository : BaseActiveRecordRepository<ExperimentacaoEstabelecimento>, IExperimentacaoEstabelecimentoRepository {
		public Perlink.Trinks.Cobranca.Factories.IExperimentacaoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IExperimentacaoEstabelecimentoFactory>();	}
		}			
	}
	public partial class FaturaRepository : BaseActiveRecordRepository<Fatura>, IFaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IFaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IFaturaFactory>();	}
		}			
	}
	public partial class FaturaMarketingRepository : BaseActiveRecordRepository<FaturaMarketing>, IFaturaMarketingRepository {
		public Perlink.Trinks.Cobranca.Factories.IFaturaMarketingFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IFaturaMarketingFactory>();	}
		}			
	}
	public partial class FaturaTrinksRepository : BaseActiveRecordRepository<FaturaTrinks>, IFaturaTrinksRepository {
		public Perlink.Trinks.Cobranca.Factories.IFaturaTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IFaturaTrinksFactory>();	}
		}			
	}
	public partial class FaturaWhatsAppRepository : BaseActiveRecordRepository<FaturaWhatsApp>, IFaturaWhatsAppRepository {
		public Perlink.Trinks.Cobranca.Factories.IFaturaWhatsAppFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IFaturaWhatsAppFactory>();	}
		}			
	}
	public partial class FormaDeContratacaoDoAdicionalRepository : BaseActiveRecordRepository<FormaDeContratacaoDoAdicional>, IFormaDeContratacaoDoAdicionalRepository {
		public Perlink.Trinks.Cobranca.Factories.IFormaDeContratacaoDoAdicionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IFormaDeContratacaoDoAdicionalFactory>();	}
		}			
	}
	public partial class FormaPagamentoRepository : BaseActiveRecordRepository<FormaPagamento>, IFormaPagamentoRepository {
		public Perlink.Trinks.Cobranca.Factories.IFormaPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IFormaPagamentoFactory>();	}
		}			
	}
	public partial class HistoricoDoAdicionalNaAssinaturaRepository : BaseActiveRecordRepository<HistoricoDoAdicionalNaAssinatura>, IHistoricoDoAdicionalNaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IHistoricoDoAdicionalNaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IHistoricoDoAdicionalNaAssinaturaFactory>();	}
		}			
	}
	public partial class MotivoDoCancelamentoRepository : BaseActiveRecordRepository<MotivoDoCancelamento>, IMotivoDoCancelamentoRepository {
		public Perlink.Trinks.Cobranca.Factories.IMotivoDoCancelamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IMotivoDoCancelamentoFactory>();	}
		}			
	}
	public partial class MotivoDoCancelamentoDaAssinaturaRepository : BaseActiveRecordRepository<MotivoDoCancelamentoDaAssinatura>, IMotivoDoCancelamentoDaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IMotivoDoCancelamentoDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IMotivoDoCancelamentoDaAssinaturaFactory>();	}
		}			
	}
	public partial class ObservacaoAssinaturaRepository : BaseActiveRecordRepository<ObservacaoAssinatura>, IObservacaoAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IObservacaoAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IObservacaoAssinaturaFactory>();	}
		}			
	}
	public partial class OfertaDeServicoAdicionalRepository : BaseActiveRecordRepository<OfertaDeServicoAdicional>, IOfertaDeServicoAdicionalRepository {
		public Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalFactory>();	}
		}			
	}
	public partial class OfertaDeServicoAdicionalMeuPlanoRepository : BaseActiveRecordRepository<OfertaDeServicoAdicionalMeuPlano>, IOfertaDeServicoAdicionalMeuPlanoRepository {
		public Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalMeuPlanoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalMeuPlanoFactory>();	}
		}			
	}
	public partial class OfertaDeServicoAdicionalMeuPlanoDisponibilidadeRepository : BaseActiveRecordRepository<OfertaDeServicoAdicionalMeuPlanoDisponibilidade>, IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeRepository {
		public Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeFactory>();	}
		}			
	}
	public partial class ParceriaTipoTrinksRepository : BaseActiveRecordRepository<ParceriaTipoTrinks>, IParceriaTipoTrinksRepository {
		public Perlink.Trinks.Cobranca.Factories.IParceriaTipoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IParceriaTipoTrinksFactory>();	}
		}			
	}
	public partial class ParceriaTrinksRepository : BaseActiveRecordRepository<ParceriaTrinks>, IParceriaTrinksRepository {
		public Perlink.Trinks.Cobranca.Factories.IParceriaTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IParceriaTrinksFactory>();	}
		}			
	}
	public partial class PessoaJuridicaPagamentoExternoRepository : BaseActiveRecordRepository<PessoaJuridicaPagamentoExterno>, IPessoaJuridicaPagamentoExternoRepository {
		public Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFactory>();	}
		}			
	}
	public partial class PessoaJuridicaPagamentoExternoFaturaRepository : BaseActiveRecordRepository<PessoaJuridicaPagamentoExternoFatura>, IPessoaJuridicaPagamentoExternoFaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFaturaFactory>();	}
		}			
	}
	public partial class PessoaJuridicaPagamentoExternoFaturaHistoricoRepository : BaseActiveRecordRepository<PessoaJuridicaPagamentoExternoFaturaHistorico>, IPessoaJuridicaPagamentoExternoFaturaHistoricoRepository {
		public Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFaturaHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFaturaHistoricoFactory>();	}
		}			
	}
	public partial class PlanoAssinaturaRepository : BaseActiveRecordRepository<PlanoAssinatura>, IPlanoAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IPlanoAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPlanoAssinaturaFactory>();	}
		}			
	}
	public partial class PromocaoNaAssinaturaRepository : BaseActiveRecordRepository<PromocaoNaAssinatura>, IPromocaoNaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IPromocaoNaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPromocaoNaAssinaturaFactory>();	}
		}			
	}
	public partial class PromocaoPraContaFinanceiraRepository : BaseActiveRecordRepository<PromocaoPraContaFinanceira>, IPromocaoPraContaFinanceiraRepository {
		public Perlink.Trinks.Cobranca.Factories.IPromocaoPraContaFinanceiraFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPromocaoPraContaFinanceiraFactory>();	}
		}			
	}
	public partial class PromocaoTrinksRepository : BaseActiveRecordRepository<PromocaoTrinks>, IPromocaoTrinksRepository {
		public Perlink.Trinks.Cobranca.Factories.IPromocaoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IPromocaoTrinksFactory>();	}
		}			
	}
	public partial class RelatorioAssinaturaRepository : BaseActiveRecordRepository<RelatorioAssinatura>, IRelatorioAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IRelatorioAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IRelatorioAssinaturaFactory>();	}
		}			
	}
	public partial class RelatorioFaturamentoRepository : BaseActiveRecordRepository<RelatorioFaturamento>, IRelatorioFaturamentoRepository {
		public Perlink.Trinks.Cobranca.Factories.IRelatorioFaturamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IRelatorioFaturamentoFactory>();	}
		}			
	}
	public partial class ResponsavelAtendimentoRepository : BaseActiveRecordRepository<ResponsavelAtendimento>, IResponsavelAtendimentoRepository {
		public Perlink.Trinks.Cobranca.Factories.IResponsavelAtendimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IResponsavelAtendimentoFactory>();	}
		}			
	}
	public partial class ServicoTrinksRepository : BaseActiveRecordRepository<ServicoTrinks>, IServicoTrinksRepository {
		public Perlink.Trinks.Cobranca.Factories.IServicoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IServicoTrinksFactory>();	}
		}			
	}
	public partial class SolicitacaoCancelamentoDaAssinaturaRepository : BaseActiveRecordRepository<SolicitacaoCancelamentoDaAssinatura>, ISolicitacaoCancelamentoDaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.ISolicitacaoCancelamentoDaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.ISolicitacaoCancelamentoDaAssinaturaFactory>();	}
		}			
	}
	public partial class StatusContaRepository : BaseActiveRecordRepository<StatusConta>, IStatusContaRepository {
		public Perlink.Trinks.Cobranca.Factories.IStatusContaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IStatusContaFactory>();	}
		}			
	}
	public partial class StatusFaturaRepository : BaseActiveRecordRepository<StatusFatura>, IStatusFaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IStatusFaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IStatusFaturaFactory>();	}
		}			
	}
	public partial class TipoAssociacaoRepository : BaseActiveRecordRepository<TipoAssociacao>, ITipoAssociacaoRepository {
		public Perlink.Trinks.Cobranca.Factories.ITipoAssociacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.ITipoAssociacaoFactory>();	}
		}			
	}
	public partial class TipoFormaPagamentoRepository : BaseActiveRecordRepository<TipoFormaPagamento>, ITipoFormaPagamentoRepository {
		public Perlink.Trinks.Cobranca.Factories.ITipoFormaPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.ITipoFormaPagamentoFactory>();	}
		}			
	}
	public partial class TipoServicoRepository : BaseActiveRecordRepository<TipoServico>, ITipoServicoRepository {
		public Perlink.Trinks.Cobranca.Factories.ITipoServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.ITipoServicoFactory>();	}
		}			
	}
	public partial class ValorDeAdesaoDoAdicionalPorFaixaRepository : BaseActiveRecordRepository<ValorDeAdesaoDoAdicionalPorFaixa>, IValorDeAdesaoDoAdicionalPorFaixaRepository {
		public Perlink.Trinks.Cobranca.Factories.IValorDeAdesaoDoAdicionalPorFaixaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IValorDeAdesaoDoAdicionalPorFaixaFactory>();	}
		}			
	}
	public partial class ValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaRepository : BaseActiveRecordRepository<ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura>, IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaRepository {
		public Perlink.Trinks.Cobranca.Factories.IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaFactory>();	}
		}			
	}
	public partial class ValorDoAdicionalPorFaixaRepository : BaseActiveRecordRepository<ValorDoAdicionalPorFaixa>, IValorDoAdicionalPorFaixaRepository {
		public Perlink.Trinks.Cobranca.Factories.IValorDoAdicionalPorFaixaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IValorDoAdicionalPorFaixaFactory>();	}
		}			
	}
	public partial class ValorDoAdicionalPorFaixaDaOfertaRepository : BaseActiveRecordRepository<ValorDoAdicionalPorFaixaDaOferta>, IValorDoAdicionalPorFaixaDaOfertaRepository {
		public Perlink.Trinks.Cobranca.Factories.IValorDoAdicionalPorFaixaDaOfertaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IValorDoAdicionalPorFaixaDaOfertaFactory>();	}
		}			
	}
	public partial class ValorPorFaixaRepository : BaseActiveRecordRepository<ValorPorFaixa>, IValorPorFaixaRepository {
		public Perlink.Trinks.Cobranca.Factories.IValorPorFaixaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cobranca.Factories.IValorPorFaixaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Cobranca.ConfiguracoesDosAdicionais.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.ContratacaoDosAdicionais.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.DTO.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Enums.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Extensions.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Factories.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Helpers.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.ObjectValues.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Stories.Repositories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.DTO.Repositories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.Enum.Repositories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.Stories.Repositories {

 
}
namespace Perlink.Trinks.Compromissos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Compromissos.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ComunidadeTrinks.Repositories {

	public partial class ArtigoDeNovidadeRepository : BaseActiveRecordRepository<ArtigoDeNovidade>, IArtigoDeNovidadeRepository {
		public Perlink.Trinks.ComunidadeTrinks.Factories.IArtigoDeNovidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ComunidadeTrinks.Factories.IArtigoDeNovidadeFactory>();	}
		}			
	}
	public partial class SugestaoRepository : BaseActiveRecordRepository<Sugestao>, ISugestaoRepository {
		public Perlink.Trinks.ComunidadeTrinks.Factories.ISugestaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ComunidadeTrinks.Factories.ISugestaoFactory>();	}
		}			
	}
	public partial class TopicoDeVotacaoRepository : BaseActiveRecordRepository<TopicoDeVotacao>, ITopicoDeVotacaoRepository {
		public Perlink.Trinks.ComunidadeTrinks.Factories.ITopicoDeVotacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ComunidadeTrinks.Factories.ITopicoDeVotacaoFactory>();	}
		}			
	}
	public partial class VotacaoDeSugestaoRepository : BaseActiveRecordRepository<VotacaoDeSugestao>, IVotacaoDeSugestaoRepository {
		public Perlink.Trinks.ComunidadeTrinks.Factories.IVotacaoDeSugestaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ComunidadeTrinks.Factories.IVotacaoDeSugestaoFactory>();	}
		}			
	}
	public partial class VotoRepository : BaseActiveRecordRepository<Voto>, IVotoRepository {
		public Perlink.Trinks.ComunidadeTrinks.Factories.IVotoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ComunidadeTrinks.Factories.IVotoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ComunidadeTrinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Repositories {

	public partial class ContaFinanceiraDoEstabelecimentoRepository : BaseActiveRecordRepository<ContaFinanceiraDoEstabelecimento>, IContaFinanceiraDoEstabelecimentoRepository {
		public Perlink.Trinks.ConciliacaoBancaria.Factories.IContaFinanceiraDoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ConciliacaoBancaria.Factories.IContaFinanceiraDoEstabelecimentoFactory>();	}
		}			
	}
	public partial class ContaFinanceiraPadraoRepository : BaseActiveRecordRepository<ContaFinanceiraPadrao>, IContaFinanceiraPadraoRepository {
		public Perlink.Trinks.ConciliacaoBancaria.Factories.IContaFinanceiraPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ConciliacaoBancaria.Factories.IContaFinanceiraPadraoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ConciliacaoBancaria.DTO.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Enums.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Exportadores.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Dtos.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Dtos.V2.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.PagarMe.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Siclos.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Stone.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Repositories {

	public partial class AutenticacaoContaDigitalRepository : BaseActiveRecordRepository<AutenticacaoContaDigital>, IAutenticacaoContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalFactory>();	}
		}			
	}
	public partial class AutenticacaoContaDigitalConfirmacaoRepository : BaseActiveRecordRepository<AutenticacaoContaDigitalConfirmacao>, IAutenticacaoContaDigitalConfirmacaoRepository {
		public Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalConfirmacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalConfirmacaoFactory>();	}
		}			
	}
	public partial class AutenticacaoContaDigitalEnvioRepository : BaseActiveRecordRepository<AutenticacaoContaDigitalEnvio>, IAutenticacaoContaDigitalEnvioRepository {
		public Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalEnvioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalEnvioFactory>();	}
		}			
	}
	public partial class AutenticacaoIdentidadePreCadastroRepository : BaseActiveRecordRepository<AutenticacaoIdentidadePreCadastro>, IAutenticacaoIdentidadePreCadastroRepository {
		public Perlink.Trinks.ContaDigital.Factories.IAutenticacaoIdentidadePreCadastroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IAutenticacaoIdentidadePreCadastroFactory>();	}
		}			
	}
	public partial class AutenticacaoIdentidadeUsuarioContaRepository : BaseActiveRecordRepository<AutenticacaoIdentidadeUsuarioConta>, IAutenticacaoIdentidadeUsuarioContaRepository {
		public Perlink.Trinks.ContaDigital.Factories.IAutenticacaoIdentidadeUsuarioContaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IAutenticacaoIdentidadeUsuarioContaFactory>();	}
		}			
	}
	public partial class CategoriaPermissaoContaDigitalRepository : BaseActiveRecordRepository<CategoriaPermissaoContaDigital>, ICategoriaPermissaoContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.ICategoriaPermissaoContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.ICategoriaPermissaoContaDigitalFactory>();	}
		}			
	}
	public partial class ChavePixRepository : BaseActiveRecordRepository<ChavePix>, IChavePixRepository {
		public Perlink.Trinks.ContaDigital.Factories.IChavePixFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IChavePixFactory>();	}
		}			
	}
	public partial class ChavePixContaDigitalRepository : BaseActiveRecordRepository<ChavePixContaDigital>, IChavePixContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IChavePixContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IChavePixContaDigitalFactory>();	}
		}			
	}
	public partial class ChavePixProfissionalRepository : BaseActiveRecordRepository<ChavePixProfissional>, IChavePixProfissionalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IChavePixProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IChavePixProfissionalFactory>();	}
		}			
	}
	public partial class ConfiguracoesContaDigitalRepository : BaseActiveRecordRepository<ConfiguracoesContaDigital>, IConfiguracoesContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IConfiguracoesContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IConfiguracoesContaDigitalFactory>();	}
		}			
	}
	public partial class ContaBancariaDigitalRepository : BaseActiveRecordRepository<ContaBancariaDigital>, IContaBancariaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IContaBancariaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IContaBancariaDigitalFactory>();	}
		}			
	}
	public partial class ContaEstabelecimentoRepository : BaseActiveRecordRepository<ContaEstabelecimento>, IContaEstabelecimentoRepository {
		public Perlink.Trinks.ContaDigital.Factories.IContaEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IContaEstabelecimentoFactory>();	}
		}			
	}
	public partial class ContaUsuarioDigitalRepository : BaseActiveRecordRepository<ContaUsuarioDigital>, IContaUsuarioDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IContaUsuarioDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IContaUsuarioDigitalFactory>();	}
		}			
	}
	public partial class DonoRepository : BaseActiveRecordRepository<Dono>, IDonoRepository {
		public Perlink.Trinks.ContaDigital.Factories.IDonoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IDonoFactory>();	}
		}			
	}
	public partial class DuvidaFrequenteRepository : BaseActiveRecordRepository<DuvidaFrequente>, IDuvidaFrequenteRepository {
		public Perlink.Trinks.ContaDigital.Factories.IDuvidaFrequenteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IDuvidaFrequenteFactory>();	}
		}			
	}
	public partial class EtapaCadastroRepository : BaseActiveRecordRepository<EtapaCadastro>, IEtapaCadastroRepository {
		public Perlink.Trinks.ContaDigital.Factories.IEtapaCadastroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IEtapaCadastroFactory>();	}
		}			
	}
	public partial class LimiteContaDigitalRepository : BaseActiveRecordRepository<LimiteContaDigital>, ILimiteContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.ILimiteContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.ILimiteContaDigitalFactory>();	}
		}			
	}
	public partial class LimitePlanoRepository : BaseActiveRecordRepository<LimitePlano>, ILimitePlanoRepository {
		public Perlink.Trinks.ContaDigital.Factories.ILimitePlanoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.ILimitePlanoFactory>();	}
		}			
	}
	public partial class LOGAprovacaoTransferenciasRepository : BaseActiveRecordRepository<LOGAprovacaoTransferencias>, ILOGAprovacaoTransferenciasRepository {
		public Perlink.Trinks.ContaDigital.Factories.ILOGAprovacaoTransferenciasFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.ILOGAprovacaoTransferenciasFactory>();	}
		}			
	}
	public partial class OperadorRepository : BaseActiveRecordRepository<Operador>, IOperadorRepository {
		public Perlink.Trinks.ContaDigital.Factories.IOperadorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IOperadorFactory>();	}
		}			
	}
	public partial class PagamentoAgendadoRepository : BaseActiveRecordRepository<PagamentoAgendado>, IPagamentoAgendadoRepository {
		public Perlink.Trinks.ContaDigital.Factories.IPagamentoAgendadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IPagamentoAgendadoFactory>();	}
		}			
	}
	public partial class PagamentoAgendadoFolhaMesProfissionalRepository : BaseActiveRecordRepository<PagamentoAgendadoFolhaMesProfissional>, IPagamentoAgendadoFolhaMesProfissionalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IPagamentoAgendadoFolhaMesProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IPagamentoAgendadoFolhaMesProfissionalFactory>();	}
		}			
	}
	public partial class PermissaoContaDigitalRepository : BaseActiveRecordRepository<PermissaoContaDigital>, IPermissaoContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IPermissaoContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IPermissaoContaDigitalFactory>();	}
		}			
	}
	public partial class PermissaoOperadorRepository : BaseActiveRecordRepository<PermissaoOperador>, IPermissaoOperadorRepository {
		public Perlink.Trinks.ContaDigital.Factories.IPermissaoOperadorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IPermissaoOperadorFactory>();	}
		}			
	}
	public partial class ResponsavelRepository : BaseActiveRecordRepository<Responsavel>, IResponsavelRepository {
		public Perlink.Trinks.ContaDigital.Factories.IResponsavelFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IResponsavelFactory>();	}
		}			
	}
	public partial class TransferenciaRepository : BaseActiveRecordRepository<Transferencia>, ITransferenciaRepository {
		public Perlink.Trinks.ContaDigital.Factories.ITransferenciaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.ITransferenciaFactory>();	}
		}			
	}
	public partial class UsuarioContaDigitalRepository : BaseActiveRecordRepository<UsuarioContaDigital>, IUsuarioContaDigitalRepository {
		public Perlink.Trinks.ContaDigital.Factories.IUsuarioContaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ContaDigital.Factories.IUsuarioContaDigitalFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ContaDigital.Builders.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.DTO.LogDTO.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Enums.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Providers.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.VO.Repositories {

 
}
namespace Perlink.Trinks.Conteudo.Repositories {

	public partial class ConteudoImagemLogoEstabelecimentoRepository : BaseActiveRecordRepository<ConteudoImagemLogoEstabelecimento>, IConteudoImagemLogoEstabelecimentoRepository {
		public Perlink.Trinks.Conteudo.Factories.IConteudoImagemLogoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Conteudo.Factories.IConteudoImagemLogoEstabelecimentoFactory>();	}
		}			
	}
	public partial class ConteudoTextoRepository : BaseActiveRecordRepository<ConteudoTexto>, IConteudoTextoRepository {
		public Perlink.Trinks.Conteudo.Factories.IConteudoTextoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Conteudo.Factories.IConteudoTextoFactory>();	}
		}			
	}
	public partial class MenuItemRepository : BaseActiveRecordRepository<MenuItem>, IMenuItemRepository {
		public Perlink.Trinks.Conteudo.Factories.IMenuItemFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Conteudo.Factories.IMenuItemFactory>();	}
		}			
	}
	public partial class MenuOpcaoAliasBuscaRepository : BaseActiveRecordRepository<MenuOpcaoAliasBusca>, IMenuOpcaoAliasBuscaRepository {
		public Perlink.Trinks.Conteudo.Factories.IMenuOpcaoAliasBuscaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Conteudo.Factories.IMenuOpcaoAliasBuscaFactory>();	}
		}			
	}
	public partial class MenuOpcaoBuscaRepository : BaseActiveRecordRepository<MenuOpcaoBusca>, IMenuOpcaoBuscaRepository {
		public Perlink.Trinks.Conteudo.Factories.IMenuOpcaoBuscaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Conteudo.Factories.IMenuOpcaoBuscaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Conteudo.DTO.Repositories {

 
}
namespace Perlink.Trinks.Controle.Repositories {

	public partial class PalavraProibidaRepository : BaseActiveRecordRepository<PalavraProibida>, IPalavraProibidaRepository {
		public Perlink.Trinks.Controle.Factories.IPalavraProibidaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Controle.Factories.IPalavraProibidaFactory>();	}
		}			
	}
	public partial class PalavraProibidaSMSRepository : BaseActiveRecordRepository<PalavraProibidaSMS>, IPalavraProibidaSMSRepository {
		public Perlink.Trinks.Controle.Factories.IPalavraProibidaSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Controle.Factories.IPalavraProibidaSMSFactory>();	}
		}			
	}
	public partial class PalavraProibidaTrinksRepository : BaseActiveRecordRepository<PalavraProibidaTrinks>, IPalavraProibidaTrinksRepository {
		public Perlink.Trinks.Controle.Factories.IPalavraProibidaTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Controle.Factories.IPalavraProibidaTrinksFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ControleDeCTAs.Repositories {

	public partial class CTARepository : BaseActiveRecordRepository<CTA>, ICTARepository {
		public Perlink.Trinks.ControleDeCTAs.Factories.ICTAFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeCTAs.Factories.ICTAFactory>();	}
		}			
	}
	public partial class CTAGrupoRepository : BaseActiveRecordRepository<CTAGrupo>, ICTAGrupoRepository {
		public Perlink.Trinks.ControleDeCTAs.Factories.ICTAGrupoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeCTAs.Factories.ICTAGrupoFactory>();	}
		}			
	}
	public partial class CTAInformacoesAdicionaisTrinksProRepository : BaseActiveRecordRepository<CTAInformacoesAdicionaisTrinksPro>, ICTAInformacoesAdicionaisTrinksProRepository {
		public Perlink.Trinks.ControleDeCTAs.Factories.ICTAInformacoesAdicionaisTrinksProFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeCTAs.Factories.ICTAInformacoesAdicionaisTrinksProFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ControleDeCTAs.DTOs.Repositories {

 
}
namespace Perlink.Trinks.ControleDeCTAs.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeCTAs.Geradores.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Enum.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Repositories {

	public partial class LancamentoAporteRepository : BaseActiveRecordRepository<LancamentoAporte>, ILancamentoAporteRepository {
		public Perlink.Trinks.ControleDeEntradaESaida.Factories.ILancamentoAporteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeEntradaESaida.Factories.ILancamentoAporteFactory>();	}
		}			
	}
	public partial class LancamentoDeReceitaCategoriaRepository : BaseActiveRecordRepository<LancamentoDeReceitaCategoria>, ILancamentoDeReceitaCategoriaRepository {
		public Perlink.Trinks.ControleDeEntradaESaida.Factories.ILancamentoDeReceitaCategoriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeEntradaESaida.Factories.ILancamentoDeReceitaCategoriaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ControleDeFotos.Repositories {

	public partial class ArquivoDeImagemRepository : BaseActiveRecordRepository<ArquivoDeImagem>, IArquivoDeImagemRepository {
		public Perlink.Trinks.ControleDeFotos.Factories.IArquivoDeImagemFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeFotos.Factories.IArquivoDeImagemFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ControleDeFotos.Controladores.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFotos.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFotos.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFotos.Factories.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Repositories {

	public partial class DisponibilidadeEspecificaRepository : BaseActiveRecordRepository<DisponibilidadeEspecifica>, IDisponibilidadeEspecificaRepository {
		public Perlink.Trinks.ControleDeFuncionalidades.Factories.IDisponibilidadeEspecificaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeFuncionalidades.Factories.IDisponibilidadeEspecificaFactory>();	}
		}			
	}
	public partial class DisponibilidadeGeralRepository : BaseActiveRecordRepository<DisponibilidadeGeral>, IDisponibilidadeGeralRepository {
		public Perlink.Trinks.ControleDeFuncionalidades.Factories.IDisponibilidadeGeralFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeFuncionalidades.Factories.IDisponibilidadeGeralFactory>();	}
		}			
	}
	public partial class PreferenciasDaContaRepository : BaseActiveRecordRepository<PreferenciasDaConta>, IPreferenciasDaContaRepository {
		public Perlink.Trinks.ControleDeFuncionalidades.Factories.IPreferenciasDaContaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeFuncionalidades.Factories.IPreferenciasDaContaFactory>();	}
		}			
	}
	public partial class ValorDeConfiguracaoEspecificaRepository : BaseActiveRecordRepository<ValorDeConfiguracaoEspecifica>, IValorDeConfiguracaoEspecificaRepository {
		public Perlink.Trinks.ControleDeFuncionalidades.Factories.IValorDeConfiguracaoEspecificaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeFuncionalidades.Factories.IValorDeConfiguracaoEspecificaFactory>();	}
		}			
	}
	public partial class ValorDeConfiguracaoGeralRepository : BaseActiveRecordRepository<ValorDeConfiguracaoGeral>, IValorDeConfiguracaoGeralRepository {
		public Perlink.Trinks.ControleDeFuncionalidades.Factories.IValorDeConfiguracaoGeralFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeFuncionalidades.Factories.IValorDeConfiguracaoGeralFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Stories.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Repositories {

	public partial class AvaliacaoDeSatisfacaoRepository : BaseActiveRecordRepository<AvaliacaoDeSatisfacao>, IAvaliacaoDeSatisfacaoRepository {
		public Perlink.Trinks.ControleDeSatisfacao.Factories.IAvaliacaoDeSatisfacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeSatisfacao.Factories.IAvaliacaoDeSatisfacaoFactory>();	}
		}			
	}
	public partial class AvaliacaoDeSatisfacaoRecebidaRetentativaRepository : BaseActiveRecordRepository<AvaliacaoDeSatisfacaoRecebidaRetentativa>, IAvaliacaoDeSatisfacaoRecebidaRetentativaRepository {
		public Perlink.Trinks.ControleDeSatisfacao.Factories.IAvaliacaoDeSatisfacaoRecebidaRetentativaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeSatisfacao.Factories.IAvaliacaoDeSatisfacaoRecebidaRetentativaFactory>();	}
		}			
	}
	public partial class ContatoRepository : BaseActiveRecordRepository<Contato>, IContatoRepository {
		public Perlink.Trinks.ControleDeSatisfacao.Factories.IContatoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeSatisfacao.Factories.IContatoFactory>();	}
		}			
	}
	public partial class ContatoCelularRepository : BaseActiveRecordRepository<ContatoCelular>, IContatoCelularRepository {
		public Perlink.Trinks.ControleDeSatisfacao.Factories.IContatoCelularFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeSatisfacao.Factories.IContatoCelularFactory>();	}
		}			
	}
	public partial class ItemParaAvaliarRepository : BaseActiveRecordRepository<ItemParaAvaliar>, IItemParaAvaliarRepository {
		public Perlink.Trinks.ControleDeSatisfacao.Factories.IItemParaAvaliarFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ControleDeSatisfacao.Factories.IItemParaAvaliarFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ControleDeSatisfacao.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Factories.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Stories.Repositories {

 
}
namespace Perlink.Trinks.Correios.Repositories {

	public partial class ConsultaCepRepository : BaseActiveRecordRepository<ConsultaCep>, IConsultaCepRepository {
		public Perlink.Trinks.Correios.Factories.IConsultaCepFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Correios.Factories.IConsultaCepFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Cupom.Repositories {

	public partial class CupomBaseRepository : BaseActiveRecordRepository<CupomBase>, ICupomBaseRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomBaseFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomBaseFactory>();	}
		}			
	}
	public partial class CupomDescontoRepository : BaseActiveRecordRepository<CupomDesconto>, ICupomDescontoRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomDescontoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomDescontoFactory>();	}
		}			
	}
	public partial class CupomEstabelecimentoRepository : BaseActiveRecordRepository<CupomEstabelecimento>, ICupomEstabelecimentoRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomEstabelecimentoFactory>();	}
		}			
	}
	public partial class CupomEstabelecimentoProdutoRepository : BaseActiveRecordRepository<CupomEstabelecimentoProduto>, ICupomEstabelecimentoProdutoRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomEstabelecimentoProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomEstabelecimentoProdutoFactory>();	}
		}			
	}
	public partial class CupomHorarioTransacaoRepository : BaseActiveRecordRepository<CupomHorarioTransacao>, ICupomHorarioTransacaoRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomHorarioTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomHorarioTransacaoFactory>();	}
		}			
	}
	public partial class CupomItemVendaRepository : BaseActiveRecordRepository<CupomItemVenda>, ICupomItemVendaRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomItemVendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomItemVendaFactory>();	}
		}			
	}
	public partial class CupomPessoaFisicaRepository : BaseActiveRecordRepository<CupomPessoaFisica>, ICupomPessoaFisicaRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomPessoaFisicaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomPessoaFisicaFactory>();	}
		}			
	}
	public partial class CupomServicoEstabelecimentoRepository : BaseActiveRecordRepository<CupomServicoEstabelecimento>, ICupomServicoEstabelecimentoRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomServicoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomServicoEstabelecimentoFactory>();	}
		}			
	}
	public partial class CupomUsoPessoaFisicaRepository : BaseActiveRecordRepository<CupomUsoPessoaFisica>, ICupomUsoPessoaFisicaRepository {
		public Perlink.Trinks.Cupom.Factories.ICupomUsoPessoaFisicaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Cupom.Factories.ICupomUsoPessoaFisicaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Cupom.DTO.Repositories {

 
}
namespace Perlink.Trinks.Cupom.Enums.Repositories {

 
}
namespace Perlink.Trinks.Cupom.Filters.Repositories {

 
}
namespace Perlink.Trinks.Cupom.Models.Repositories {

 
}
namespace Perlink.Trinks.Dashboard.DTOs.Repositories {

 
}
namespace Perlink.Trinks.Dashboard.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.DataQuery.DTO.Repositories {

 
}
namespace Perlink.Trinks.DataQuery.Repositories {

 
}
namespace Perlink.Trinks.DataQuery.Strategies.Repositories {

 
}
namespace Perlink.Trinks.DebitoParcial.Repositories {

	public partial class AbatimentoDeDividaRepository : BaseActiveRecordRepository<AbatimentoDeDivida>, IAbatimentoDeDividaRepository {
		public Perlink.Trinks.DebitoParcial.Factories.IAbatimentoDeDividaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.DebitoParcial.Factories.IAbatimentoDeDividaFactory>();	}
		}			
	}
	public partial class DividaDeixadaNoEstabelecimentoRepository : BaseActiveRecordRepository<DividaDeixadaNoEstabelecimento>, IDividaDeixadaNoEstabelecimentoRepository {
		public Perlink.Trinks.DebitoParcial.Factories.IDividaDeixadaNoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.DebitoParcial.Factories.IDividaDeixadaNoEstabelecimentoFactory>();	}
		}			
	}
	public partial class HistoricoDaDividaRepository : BaseActiveRecordRepository<HistoricoDaDivida>, IHistoricoDaDividaRepository {
		public Perlink.Trinks.DebitoParcial.Factories.IHistoricoDaDividaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.DebitoParcial.Factories.IHistoricoDaDividaFactory>();	}
		}			
	}
	public partial class PagamentoDeDividaPeloClienteRepository : BaseActiveRecordRepository<PagamentoDeDividaPeloCliente>, IPagamentoDeDividaPeloClienteRepository {
		public Perlink.Trinks.DebitoParcial.Factories.IPagamentoDeDividaPeloClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.DebitoParcial.Factories.IPagamentoDeDividaPeloClienteFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.DebitoParcial.DTO.Repositories {

 
}
namespace Perlink.Trinks.DebitoParcial.Filtros.Repositories {

 
}
namespace Perlink.Trinks.DebitoParcial.Stories.Repositories {

 
}
namespace Perlink.Trinks.Despesas.Repositories {

	public partial class LancamentosRecorrentesSelecionadasRepository : BaseActiveRecordRepository<LancamentosRecorrentesSelecionadas>, ILancamentosRecorrentesSelecionadasRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentosRecorrentesSelecionadasFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentosRecorrentesSelecionadasFactory>();	}
		}			
	}
	public partial class LancamentoRepository : BaseActiveRecordRepository<Lancamento>, ILancamentoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoFactory>();	}
		}			
	}
	public partial class LancamentoCategoriaRepository : BaseActiveRecordRepository<LancamentoCategoria>, ILancamentoCategoriaRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoCategoriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoCategoriaFactory>();	}
		}			
	}
	public partial class LancamentoCategoriaPadraoRepository : BaseActiveRecordRepository<LancamentoCategoriaPadrao>, ILancamentoCategoriaPadraoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoCategoriaPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoCategoriaPadraoFactory>();	}
		}			
	}
	public partial class LancamentoGeradoPorMovimentacaoEstoqueRepository : BaseActiveRecordRepository<LancamentoGeradoPorMovimentacaoEstoque>, ILancamentoGeradoPorMovimentacaoEstoqueRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoGeradoPorMovimentacaoEstoqueFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoGeradoPorMovimentacaoEstoqueFactory>();	}
		}			
	}
	public partial class LancamentoGrupoRepository : BaseActiveRecordRepository<LancamentoGrupo>, ILancamentoGrupoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoGrupoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoGrupoFactory>();	}
		}			
	}
	public partial class LancamentoGrupoPadraoRepository : BaseActiveRecordRepository<LancamentoGrupoPadrao>, ILancamentoGrupoPadraoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoGrupoPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoGrupoPadraoFactory>();	}
		}			
	}
	public partial class LancamentoRecorrenciaRepository : BaseActiveRecordRepository<LancamentoRecorrencia>, ILancamentoRecorrenciaRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoRecorrenciaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoRecorrenciaFactory>();	}
		}			
	}
	public partial class LancamentoRecorrenciaTipoRepository : BaseActiveRecordRepository<LancamentoRecorrenciaTipo>, ILancamentoRecorrenciaTipoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoRecorrenciaTipoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoRecorrenciaTipoFactory>();	}
		}			
	}
	public partial class LancamentoStatusPagamentoRepository : BaseActiveRecordRepository<LancamentoStatusPagamento>, ILancamentoStatusPagamentoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoStatusPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoStatusPagamentoFactory>();	}
		}			
	}
	public partial class LancamentoTipoRepository : BaseActiveRecordRepository<LancamentoTipo>, ILancamentoTipoRepository {
		public Perlink.Trinks.Despesas.Factories.ILancamentoTipoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.ILancamentoTipoFactory>();	}
		}			
	}
	public partial class RenovacaoDeLancamentosRepository : BaseActiveRecordRepository<RenovacaoDeLancamentos>, IRenovacaoDeLancamentosRepository {
		public Perlink.Trinks.Despesas.Factories.IRenovacaoDeLancamentosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Despesas.Factories.IRenovacaoDeLancamentosFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Despesas.DTO.Repositories {

 
}
namespace Perlink.Trinks.Despesas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Despesas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Disponibilidade.Adapters.Repositories {

 
}
namespace Perlink.Trinks.Disponibilidade.Repositories {

 
}
namespace Perlink.Trinks.Disponibilidade.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Dispositivos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Dispositivos.Repositories {

	public partial class TipoImpressaoRepository : BaseActiveRecordRepository<TipoImpressao>, ITipoImpressaoRepository {
		public Perlink.Trinks.Dispositivos.Factories.ITipoImpressaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Dispositivos.Factories.ITipoImpressaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.DTO.Repositories {

	public partial class HorarioFuturosExportacaoRepository : BaseActiveRecordRepository<HorarioFuturosExportacao>, IHorarioFuturosExportacaoRepository {
		public Perlink.Trinks.DTO.Factories.IHorarioFuturosExportacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.DTO.Factories.IHorarioFuturosExportacaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.DTO.PushSNS.Repositories {

 
}
namespace Perlink.Trinks.Encurtador.DTO.Repositories {

 
}
namespace Perlink.Trinks.Encurtador.Repositories {

	public partial class EncurtadorDeDadosRepository : BaseActiveRecordRepository<EncurtadorDeDados>, IEncurtadorDeDadosRepository {
		public Perlink.Trinks.Encurtador.Factories.IEncurtadorDeDadosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Encurtador.Factories.IEncurtadorDeDadosFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Encurtador.Enums.Repositories {

 
}
namespace Perlink.Trinks.Encurtador.Factories.Repositories {

 
}
namespace Perlink.Trinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.Env.Clock.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Antifraude.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Enums.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Repositories {

	public partial class AvaliacaoEstabelecimentoRepository : BaseActiveRecordRepository<AvaliacaoEstabelecimento>, IAvaliacaoEstabelecimentoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IAvaliacaoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IAvaliacaoEstabelecimentoFactory>();	}
		}			
	}
	public partial class ConfiguracaoDeAvaliacaoDeSatisfacaoRepository : BaseActiveRecordRepository<ConfiguracaoDeAvaliacaoDeSatisfacao>, IConfiguracaoDeAvaliacaoDeSatisfacaoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IConfiguracaoDeAvaliacaoDeSatisfacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IConfiguracaoDeAvaliacaoDeSatisfacaoFactory>();	}
		}			
	}
	public partial class ConfiguracaoEstabelecimentoRepository : BaseActiveRecordRepository<ConfiguracaoEstabelecimento>, IConfiguracaoEstabelecimentoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IConfiguracaoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IConfiguracaoEstabelecimentoFactory>();	}
		}			
	}
	public partial class EstabelecimentoFavoritoRepository : BaseActiveRecordRepository<EstabelecimentoFavorito>, IEstabelecimentoFavoritoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoFavoritoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoFavoritoFactory>();	}
		}			
	}
	public partial class EstabelecimentoProfissionalFavoritoRepository : BaseActiveRecordRepository<EstabelecimentoProfissionalFavorito>, IEstabelecimentoProfissionalFavoritoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoProfissionalFavoritoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoProfissionalFavoritoFactory>();	}
		}			
	}
	public partial class EstabelecimentoUUIDRepository : BaseActiveRecordRepository<EstabelecimentoUUID>, IEstabelecimentoUUIDRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoUUIDFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoUUIDFactory>();	}
		}			
	}
	public partial class ItemConfiguradoParaSerAvaliadoRepository : BaseActiveRecordRepository<ItemConfiguradoParaSerAvaliado>, IItemConfiguradoParaSerAvaliadoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IItemConfiguradoParaSerAvaliadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IItemConfiguradoParaSerAvaliadoFactory>();	}
		}			
	}
	public partial class ServicoConfiguradoParaSerAvaliadoRepository : BaseActiveRecordRepository<ServicoConfiguradoParaSerAvaliado>, IServicoConfiguradoParaSerAvaliadoRepository {
		public Perlink.Trinks.Estabelecimentos.Factories.IServicoConfiguradoParaSerAvaliadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.Factories.IServicoConfiguradoParaSerAvaliadoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Estabelecimentos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories {

	public partial class EstabelecimentoComInformacoesConsolidadasRepository : BaseActiveRecordRepository<EstabelecimentoComInformacoesConsolidadas>, IEstabelecimentoComInformacoesConsolidadasRepository {
		public Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IEstabelecimentoComInformacoesConsolidadasFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IEstabelecimentoComInformacoesConsolidadasFactory>();	}
		}			
	}
	public partial class InformacoesConsolidadasDaBuscaDoPortalRepository : BaseActiveRecordRepository<InformacoesConsolidadasDaBuscaDoPortal>, IInformacoesConsolidadasDaBuscaDoPortalRepository {
		public Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IInformacoesConsolidadasDaBuscaDoPortalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IInformacoesConsolidadasDaBuscaDoPortalFactory>();	}
		}			
	}
	public partial class OpcaoDeAutocompletarDoPortalRepository : BaseActiveRecordRepository<OpcaoDeAutocompletarDoPortal>, IOpcaoDeAutocompletarDoPortalRepository {
		public Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IOpcaoDeAutocompletarDoPortalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IOpcaoDeAutocompletarDoPortalFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Estabelecimentos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Factories.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.Estatistica.Repositories {

	public partial class BIUsoDoSistemaRepository : BaseActiveRecordRepository<BIUsoDoSistema>, IBIUsoDoSistemaRepository {
		public Perlink.Trinks.Estatistica.Factories.IBIUsoDoSistemaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Estatistica.Factories.IBIUsoDoSistemaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.EstilosVisuais.Enums.Repositories {

 
}
namespace Perlink.Trinks.EstilosVisuais.Repositories {

	public partial class TemaCssRepository : BaseActiveRecordRepository<TemaCss>, ITemaCssRepository {
		public Perlink.Trinks.EstilosVisuais.Factories.ITemaCssFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstilosVisuais.Factories.ITemaCssFactory>();	}
		}			
	}
	public partial class TemaCssBackofficeRepository : BaseActiveRecordRepository<TemaCssBackoffice>, ITemaCssBackofficeRepository {
		public Perlink.Trinks.EstilosVisuais.Factories.ITemaCssBackofficeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstilosVisuais.Factories.ITemaCssBackofficeFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Calculos.Repositories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories {

	public partial class ConfiguracaoDoServicoRepository : BaseActiveRecordRepository<ConfiguracaoDoServico>, IConfiguracaoDoServicoRepository {
		public Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IConfiguracaoDoServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IConfiguracaoDoServicoFactory>();	}
		}			
	}
	public partial class ConfiguracaoParaBaixaAutomaticaRepository : BaseActiveRecordRepository<ConfiguracaoParaBaixaAutomatica>, IConfiguracaoParaBaixaAutomaticaRepository {
		public Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IConfiguracaoParaBaixaAutomaticaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IConfiguracaoParaBaixaAutomaticaFactory>();	}
		}			
	}
	public partial class HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioRepository : BaseActiveRecordRepository<HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario>, IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioRepository {
		public Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioFactory>();	}
		}			
	}
	public partial class HistoricoDeUsoDeProdutoNoHorarioRepository : BaseActiveRecordRepository<HistoricoDeUsoDeProdutoNoHorario>, IHistoricoDeUsoDeProdutoNoHorarioRepository {
		public Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IHistoricoDeUsoDeProdutoNoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IHistoricoDeUsoDeProdutoNoHorarioFactory>();	}
		}			
	}
	public partial class ItemConfiguradoParaBaixaAutomaticaRepository : BaseActiveRecordRepository<ItemConfiguradoParaBaixaAutomatica>, IItemConfiguradoParaBaixaAutomaticaRepository {
		public Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IItemConfiguradoParaBaixaAutomaticaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IItemConfiguradoParaBaixaAutomaticaFactory>();	}
		}			
	}
	public partial class UsoDeProdutoNoHorarioRepository : BaseActiveRecordRepository<UsoDeProdutoNoHorario>, IUsoDeProdutoNoHorarioRepository {
		public Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IUsoDeProdutoNoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IUsoDeProdutoNoHorarioFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.DTO.Repositories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.Repositories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.Repositories {

 
}
namespace Perlink.Trinks.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Extratores.DTO.Repositories {

 
}
namespace Perlink.Trinks.Extratores.Repositories {

	public partial class ExtratorRepository : BaseActiveRecordRepository<Extrator>, IExtratorRepository {
		public Perlink.Trinks.Extratores.Factories.IExtratorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Extratores.Factories.IExtratorFactory>();	}
		}			
	}
	public partial class VisaoRepository : BaseActiveRecordRepository<Visao>, IVisaoRepository {
		public Perlink.Trinks.Extratores.Factories.IVisaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Extratores.Factories.IVisaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Facebook.DTOs.Repositories {

 
}
namespace Perlink.Trinks.Facebook.Enums.Repositories {

 
}
namespace Perlink.Trinks.Facebook.Repositories {

	public partial class FBERepository : BaseActiveRecordRepository<FBE>, IFBERepository {
		public Perlink.Trinks.Facebook.Factories.IFBEFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Facebook.Factories.IFBEFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.FAQ.Repositories {

	public partial class AssuntoRepository : BaseActiveRecordRepository<Assunto>, IAssuntoRepository {
		public Perlink.Trinks.FAQ.Factories.IAssuntoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.FAQ.Factories.IAssuntoFactory>();	}
		}			
	}
	public partial class AssuntoPerguntaRespostaRepository : BaseActiveRecordRepository<AssuntoPerguntaResposta>, IAssuntoPerguntaRespostaRepository {
		public Perlink.Trinks.FAQ.Factories.IAssuntoPerguntaRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.FAQ.Factories.IAssuntoPerguntaRespostaFactory>();	}
		}			
	}
	public partial class PerguntaRespostaRepository : BaseActiveRecordRepository<PerguntaResposta>, IPerguntaRespostaRepository {
		public Perlink.Trinks.FAQ.Factories.IPerguntaRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.FAQ.Factories.IPerguntaRespostaFactory>();	}
		}			
	}
	public partial class TelaRepository : BaseActiveRecordRepository<Tela>, ITelaRepository {
		public Perlink.Trinks.FAQ.Factories.ITelaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.FAQ.Factories.ITelaFactory>();	}
		}			
	}
	public partial class TelaAssuntoRepository : BaseActiveRecordRepository<TelaAssunto>, ITelaAssuntoRepository {
		public Perlink.Trinks.FAQ.Factories.ITelaAssuntoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.FAQ.Factories.ITelaAssuntoFactory>();	}
		}			
	}
	public partial class TelaPerguntaRespostaRepository : BaseActiveRecordRepository<TelaPerguntaResposta>, ITelaPerguntaRespostaRepository {
		public Perlink.Trinks.FAQ.Factories.ITelaPerguntaRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.FAQ.Factories.ITelaPerguntaRespostaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Fidelidade.Repositories {

	public partial class AgendamentoOnlineQueGerouPontosRepository : BaseActiveRecordRepository<AgendamentoOnlineQueGerouPontos>, IAgendamentoOnlineQueGerouPontosRepository {
		public Perlink.Trinks.Fidelidade.Factories.IAgendamentoOnlineQueGerouPontosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IAgendamentoOnlineQueGerouPontosFactory>();	}
		}			
	}
	public partial class MovimentacaoDePontosRepository : BaseActiveRecordRepository<MovimentacaoDePontos>, IMovimentacaoDePontosRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosFactory>();	}
		}			
	}
	public partial class MovimentacaoDePontosAgendamentoOnlineRepository : BaseActiveRecordRepository<MovimentacaoDePontosAgendamentoOnline>, IMovimentacaoDePontosAgendamentoOnlineRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosAgendamentoOnlineFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosAgendamentoOnlineFactory>();	}
		}			
	}
	public partial class MovimentacaoDePontosAvulsoRepository : BaseActiveRecordRepository<MovimentacaoDePontosAvulso>, IMovimentacaoDePontosAvulsoRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosAvulsoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosAvulsoFactory>();	}
		}			
	}
	public partial class MovimentacaoDePontosHorarioTransacaoRepository : BaseActiveRecordRepository<MovimentacaoDePontosHorarioTransacao>, IMovimentacaoDePontosHorarioTransacaoRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosHorarioTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosHorarioTransacaoFactory>();	}
		}			
	}
	public partial class MovimentacaoDePontosItemVendaRepository : BaseActiveRecordRepository<MovimentacaoDePontosItemVenda>, IMovimentacaoDePontosItemVendaRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosItemVendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosItemVendaFactory>();	}
		}			
	}
	public partial class MovimentacaoDePontosPagamentoAntecipadoRepository : BaseActiveRecordRepository<MovimentacaoDePontosPagamentoAntecipado>, IMovimentacaoDePontosPagamentoAntecipadoRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosPagamentoAntecipadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosPagamentoAntecipadoFactory>();	}
		}			
	}
	public partial class MovimentacaoDeTransferenciaDePontosRepository : BaseActiveRecordRepository<MovimentacaoDeTransferenciaDePontos>, IMovimentacaoDeTransferenciaDePontosRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDeTransferenciaDePontosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDeTransferenciaDePontosFactory>();	}
		}			
	}
	public partial class MovimentacaoDeUmPontoGanhoRepository : BaseActiveRecordRepository<MovimentacaoDeUmPontoGanho>, IMovimentacaoDeUmPontoGanhoRepository {
		public Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDeUmPontoGanhoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDeUmPontoGanhoFactory>();	}
		}			
	}
	public partial class PagamentoAntecipadoQueGerouPontosRepository : BaseActiveRecordRepository<PagamentoAntecipadoQueGerouPontos>, IPagamentoAntecipadoQueGerouPontosRepository {
		public Perlink.Trinks.Fidelidade.Factories.IPagamentoAntecipadoQueGerouPontosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IPagamentoAntecipadoQueGerouPontosFactory>();	}
		}			
	}
	public partial class PontoGanhoRepository : BaseActiveRecordRepository<PontoGanho>, IPontoGanhoRepository {
		public Perlink.Trinks.Fidelidade.Factories.IPontoGanhoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IPontoGanhoFactory>();	}
		}			
	}
	public partial class ProgramaDeFidelidadeRepository : BaseActiveRecordRepository<ProgramaDeFidelidade>, IProgramaDeFidelidadeRepository {
		public Perlink.Trinks.Fidelidade.Factories.IProgramaDeFidelidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IProgramaDeFidelidadeFactory>();	}
		}			
	}
	public partial class ProgramaDeFidelidadeDiaSemanaRepository : BaseActiveRecordRepository<ProgramaDeFidelidadeDiaSemana>, IProgramaDeFidelidadeDiaSemanaRepository {
		public Perlink.Trinks.Fidelidade.Factories.IProgramaDeFidelidadeDiaSemanaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.IProgramaDeFidelidadeDiaSemanaFactory>();	}
		}			
	}
	public partial class TransferenciaDePontosRepository : BaseActiveRecordRepository<TransferenciaDePontos>, ITransferenciaDePontosRepository {
		public Perlink.Trinks.Fidelidade.Factories.ITransferenciaDePontosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fidelidade.Factories.ITransferenciaDePontosFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Fidelidade.DTO.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Enums.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Factories.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Repositories {

	public partial class AberturaFechamentoCaixaRepository : BaseActiveRecordRepository<AberturaFechamentoCaixa>, IAberturaFechamentoCaixaRepository {
		public Perlink.Trinks.Financeiro.Factories.IAberturaFechamentoCaixaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IAberturaFechamentoCaixaFactory>();	}
		}			
	}
	public partial class AberturaFechamentoCaixaHistoricoRepository : BaseActiveRecordRepository<AberturaFechamentoCaixaHistorico>, IAberturaFechamentoCaixaHistoricoRepository {
		public Perlink.Trinks.Financeiro.Factories.IAberturaFechamentoCaixaHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IAberturaFechamentoCaixaHistoricoFactory>();	}
		}			
	}
	public partial class ComissaoRepository : BaseActiveRecordRepository<Comissao>, IComissaoRepository {
		public Perlink.Trinks.Financeiro.Factories.IComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IComissaoFactory>();	}
		}			
	}
	public partial class ContaBancariaPessoaRepository : BaseActiveRecordRepository<ContaBancariaPessoa>, IContaBancariaPessoaRepository {
		public Perlink.Trinks.Financeiro.Factories.IContaBancariaPessoaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IContaBancariaPessoaFactory>();	}
		}			
	}
	public partial class FechamentoFolhaMesRepository : BaseActiveRecordRepository<FechamentoFolhaMes>, IFechamentoFolhaMesRepository {
		public Perlink.Trinks.Financeiro.Factories.IFechamentoFolhaMesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFechamentoFolhaMesFactory>();	}
		}			
	}
	public partial class FechamentoFolhaMesProfissionalRepository : BaseActiveRecordRepository<FechamentoFolhaMesProfissional>, IFechamentoFolhaMesProfissionalRepository {
		public Perlink.Trinks.Financeiro.Factories.IFechamentoFolhaMesProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFechamentoFolhaMesProfissionalFactory>();	}
		}			
	}
	public partial class FormaPagamentoRepository : BaseActiveRecordRepository<FormaPagamento>, IFormaPagamentoRepository {
		public Perlink.Trinks.Financeiro.Factories.IFormaPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFormaPagamentoFactory>();	}
		}			
	}
	public partial class FormaPagamentoTipoRepository : BaseActiveRecordRepository<FormaPagamentoTipo>, IFormaPagamentoTipoRepository {
		public Perlink.Trinks.Financeiro.Factories.IFormaPagamentoTipoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFormaPagamentoTipoFactory>();	}
		}			
	}
	public partial class GorjetaRepository : BaseActiveRecordRepository<Gorjeta>, IGorjetaRepository {
		public Perlink.Trinks.Financeiro.Factories.IGorjetaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IGorjetaFactory>();	}
		}			
	}
	public partial class LancamentoDeAntecipacaoRepository : BaseActiveRecordRepository<LancamentoDeAntecipacao>, ILancamentoDeAntecipacaoRepository {
		public Perlink.Trinks.Financeiro.Factories.ILancamentoDeAntecipacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ILancamentoDeAntecipacaoFactory>();	}
		}			
	}
	public partial class MotivoDescontoRepository : BaseActiveRecordRepository<MotivoDesconto>, IMotivoDescontoRepository {
		public Perlink.Trinks.Financeiro.Factories.IMotivoDescontoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IMotivoDescontoFactory>();	}
		}			
	}
	public partial class PagamentoFolhaMesProfissionalRepository : BaseActiveRecordRepository<PagamentoFolhaMesProfissional>, IPagamentoFolhaMesProfissionalRepository {
		public Perlink.Trinks.Financeiro.Factories.IPagamentoFolhaMesProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IPagamentoFolhaMesProfissionalFactory>();	}
		}			
	}
	public partial class SangriaRepository : BaseActiveRecordRepository<Sangria>, ISangriaRepository {
		public Perlink.Trinks.Financeiro.Factories.ISangriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ISangriaFactory>();	}
		}			
	}
	public partial class SangriaHistoricoRepository : BaseActiveRecordRepository<SangriaHistorico>, ISangriaHistoricoRepository {
		public Perlink.Trinks.Financeiro.Factories.ISangriaHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ISangriaHistoricoFactory>();	}
		}			
	}
	public partial class TipoContaBancariaRepository : BaseActiveRecordRepository<TipoContaBancaria>, ITipoContaBancariaRepository {
		public Perlink.Trinks.Financeiro.Factories.ITipoContaBancariaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITipoContaBancariaFactory>();	}
		}			
	}
	public partial class TipoTransacaoRepository : BaseActiveRecordRepository<TipoTransacao>, ITipoTransacaoRepository {
		public Perlink.Trinks.Financeiro.Factories.ITipoTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITipoTransacaoFactory>();	}
		}			
	}
	public partial class TransacaoRepository : BaseActiveRecordRepository<Transacao>, ITransacaoRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoFactory>();	}
		}			
	}
	public partial class TransacaoFormaPagamentoRepository : BaseActiveRecordRepository<TransacaoFormaPagamento>, ITransacaoFormaPagamentoRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoFormaPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoFormaPagamentoFactory>();	}
		}			
	}
	public partial class TransacaoFormaPagamentoParcelaRepository : BaseActiveRecordRepository<TransacaoFormaPagamentoParcela>, ITransacaoFormaPagamentoParcelaRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoFormaPagamentoParcelaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoFormaPagamentoParcelaFactory>();	}
		}			
	}
	public partial class TransacaoHistoricoRepository : BaseActiveRecordRepository<TransacaoHistorico>, ITransacaoHistoricoRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoHistoricoFactory>();	}
		}			
	}
	public partial class TransacaoItemRepository : BaseActiveRecordRepository<TransacaoItem>, ITransacaoItemRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoItemFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoItemFactory>();	}
		}			
	}
	public partial class TransacaoLancamentoFinanceiroRepository : BaseActiveRecordRepository<TransacaoLancamentoFinanceiro>, ITransacaoLancamentoFinanceiroRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoLancamentoFinanceiroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoLancamentoFinanceiroFactory>();	}
		}			
	}
	public partial class TransacaoPOSRepository : BaseActiveRecordRepository<TransacaoPOS>, ITransacaoPOSRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoPOSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoPOSFactory>();	}
		}			
	}
	public partial class TransacaoPOSSplitRepository : BaseActiveRecordRepository<TransacaoPOSSplit>, ITransacaoPOSSplitRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoPOSSplitFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoPOSSplitFactory>();	}
		}			
	}
	public partial class TransacaoPosWebhookRequestRepository : BaseActiveRecordRepository<TransacaoPosWebhookRequest>, ITransacaoPosWebhookRequestRepository {
		public Perlink.Trinks.Financeiro.Factories.ITransacaoPosWebhookRequestFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.ITransacaoPosWebhookRequestFactory>();	}
		}			
	}
	public partial class ValorDeComissaoAReceberRepository : BaseActiveRecordRepository<ValorDeComissaoAReceber>, IValorDeComissaoAReceberRepository {
		public Perlink.Trinks.Financeiro.Factories.IValorDeComissaoAReceberFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IValorDeComissaoAReceberFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Financeiro.Adapters.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Calculos.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Repositories {

	public partial class HistoricoDoCaixaPorOperadorRepository : BaseActiveRecordRepository<HistoricoDoCaixaPorOperador>, IHistoricoDoCaixaPorOperadorRepository {
		public Perlink.Trinks.Financeiro.Factories.IHistoricoDoCaixaPorOperadorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IHistoricoDoCaixaPorOperadorFactory>();	}
		}			
	}
	public partial class MovimentacaoNoCaixaPorOperadorRepository : BaseActiveRecordRepository<MovimentacaoNoCaixaPorOperador>, IMovimentacaoNoCaixaPorOperadorRepository {
		public Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorFactory>();	}
		}			
	}
	public partial class MovimentacaoNoCaixaPorOperadorLancamentoRepository : BaseActiveRecordRepository<MovimentacaoNoCaixaPorOperadorLancamento>, IMovimentacaoNoCaixaPorOperadorLancamentoRepository {
		public Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorLancamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorLancamentoFactory>();	}
		}			
	}
	public partial class MovimentacaoNoCaixaPorOperadorTransacaoRepository : BaseActiveRecordRepository<MovimentacaoNoCaixaPorOperadorTransacao>, IMovimentacaoNoCaixaPorOperadorTransacaoRepository {
		public Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorTransacaoFactory>();	}
		}			
	}
	public partial class RegistroDeCaixaPorOperadorRepository : BaseActiveRecordRepository<RegistroDeCaixaPorOperador>, IRegistroDeCaixaPorOperadorRepository {
		public Perlink.Trinks.Financeiro.Factories.IRegistroDeCaixaPorOperadorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IRegistroDeCaixaPorOperadorFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Financeiro.Repositories {

	public partial class DescontoPersonalizadoRepository : BaseActiveRecordRepository<DescontoPersonalizado>, IDescontoPersonalizadoRepository {
		public Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoFactory>();	}
		}			
	}
	public partial class DescontoPersonalizadoAssistentesRepository : BaseActiveRecordRepository<DescontoPersonalizadoAssistentes>, IDescontoPersonalizadoAssistentesRepository {
		public Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoAssistentesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoAssistentesFactory>();	}
		}			
	}
	public partial class DescontoPersonalizadoPacoteRepository : BaseActiveRecordRepository<DescontoPersonalizadoPacote>, IDescontoPersonalizadoPacoteRepository {
		public Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoPacoteFactory>();	}
		}			
	}
	public partial class DescontoPersonalizadoProdutoRepository : BaseActiveRecordRepository<DescontoPersonalizadoProduto>, IDescontoPersonalizadoProdutoRepository {
		public Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoProdutoFactory>();	}
		}			
	}
	public partial class DescontoPersonalizadoProfissionaisRepository : BaseActiveRecordRepository<DescontoPersonalizadoProfissionais>, IDescontoPersonalizadoProfissionaisRepository {
		public Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoProfissionaisFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoProfissionaisFactory>();	}
		}			
	}
	public partial class DescontoPersonalizadoServicoRepository : BaseActiveRecordRepository<DescontoPersonalizadoServico>, IDescontoPersonalizadoServicoRepository {
		public Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoServicoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Financeiro.DTO.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.CalculoComissao.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Checkout.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Connect.Pagarme.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Connect.Stone.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.DescontosPersonalizados.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.POS.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Enums.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Factories.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Repositories {

	public partial class FolhaPagamentoCompraProdutoRepository : BaseActiveRecordRepository<FolhaPagamentoCompraProduto>, IFolhaPagamentoCompraProdutoRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoCompraProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoCompraProdutoFactory>();	}
		}			
	}
	public partial class FolhaPagamentoItemRepository : BaseActiveRecordRepository<FolhaPagamentoItem>, IFolhaPagamentoItemRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemFactory>();	}
		}			
	}
	public partial class FolhaPagamentoItemBonificacaoRepository : BaseActiveRecordRepository<FolhaPagamentoItemBonificacao>, IFolhaPagamentoItemBonificacaoRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemBonificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemBonificacaoFactory>();	}
		}			
	}
	public partial class FolhaPagamentoItemGorjetaRepository : BaseActiveRecordRepository<FolhaPagamentoItemGorjeta>, IFolhaPagamentoItemGorjetaRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemGorjetaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemGorjetaFactory>();	}
		}			
	}
	public partial class FolhaPagamentoItemSplitRepository : BaseActiveRecordRepository<FolhaPagamentoItemSplit>, IFolhaPagamentoItemSplitRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemSplitFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemSplitFactory>();	}
		}			
	}
	public partial class FolhaPagamentoItemValeRepository : BaseActiveRecordRepository<FolhaPagamentoItemVale>, IFolhaPagamentoItemValeRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemValeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemValeFactory>();	}
		}			
	}
	public partial class FolhaPagamentoLancamentoRepository : BaseActiveRecordRepository<FolhaPagamentoLancamento>, IFolhaPagamentoLancamentoRepository {
		public Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoLancamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoLancamentoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Financeiro.POS.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Stories.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Repositories {

	public partial class AssinaturaDigitalRepository : BaseActiveRecordRepository<AssinaturaDigital>, IAssinaturaDigitalRepository {
		public Perlink.Trinks.Formulario.Factories.IAssinaturaDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IAssinaturaDigitalFactory>();	}
		}			
	}
	public partial class ConfiguracaoDoFormularioRepository : BaseActiveRecordRepository<ConfiguracaoDoFormulario>, IConfiguracaoDoFormularioRepository {
		public Perlink.Trinks.Formulario.Factories.IConfiguracaoDoFormularioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IConfiguracaoDoFormularioFactory>();	}
		}			
	}
	public partial class FormularioDinamicoRepository : BaseActiveRecordRepository<FormularioDinamico>, IFormularioDinamicoRepository {
		public Perlink.Trinks.Formulario.Factories.IFormularioDinamicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IFormularioDinamicoFactory>();	}
		}			
	}
	public partial class FormularioRespondidoRepository : BaseActiveRecordRepository<FormularioRespondido>, IFormularioRespondidoRepository {
		public Perlink.Trinks.Formulario.Factories.IFormularioRespondidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IFormularioRespondidoFactory>();	}
		}			
	}
	public partial class OpcaoDeRespostaRepository : BaseActiveRecordRepository<OpcaoDeResposta>, IOpcaoDeRespostaRepository {
		public Perlink.Trinks.Formulario.Factories.IOpcaoDeRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IOpcaoDeRespostaFactory>();	}
		}			
	}
	public partial class PessoaPerguntadaRepository : BaseActiveRecordRepository<PessoaPerguntada>, IPessoaPerguntadaRepository {
		public Perlink.Trinks.Formulario.Factories.IPessoaPerguntadaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IPessoaPerguntadaFactory>();	}
		}			
	}
	public partial class QuestaoDoFormularioRepository : BaseActiveRecordRepository<QuestaoDoFormulario>, IQuestaoDoFormularioRepository {
		public Perlink.Trinks.Formulario.Factories.IQuestaoDoFormularioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IQuestaoDoFormularioFactory>();	}
		}			
	}
	public partial class RespostaDoFormularioRepository : BaseActiveRecordRepository<RespostaDoFormulario>, IRespostaDoFormularioRepository {
		public Perlink.Trinks.Formulario.Factories.IRespostaDoFormularioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IRespostaDoFormularioFactory>();	}
		}			
	}
	public partial class SolicitacaoDeAssinaturaRepository : BaseActiveRecordRepository<SolicitacaoDeAssinatura>, ISolicitacaoDeAssinaturaRepository {
		public Perlink.Trinks.Formulario.Factories.ISolicitacaoDeAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.ISolicitacaoDeAssinaturaFactory>();	}
		}			
	}
	public partial class TipoDeRespostaRepository : BaseActiveRecordRepository<TipoDeResposta>, ITipoDeRespostaRepository {
		public Perlink.Trinks.Formulario.Factories.ITipoDeRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.ITipoDeRespostaFactory>();	}
		}			
	}
	public partial class VersaoDaQuestaoDoFormularioRepository : BaseActiveRecordRepository<VersaoDaQuestaoDoFormulario>, IVersaoDaQuestaoDoFormularioRepository {
		public Perlink.Trinks.Formulario.Factories.IVersaoDaQuestaoDoFormularioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IVersaoDaQuestaoDoFormularioFactory>();	}
		}			
	}
	public partial class VersaoDoFormularioDinamicoRepository : BaseActiveRecordRepository<VersaoDoFormularioDinamico>, IVersaoDoFormularioDinamicoRepository {
		public Perlink.Trinks.Formulario.Factories.IVersaoDoFormularioDinamicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Formulario.Factories.IVersaoDoFormularioDinamicoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Formulario.DTO.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Enums.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Stories.Repositories {

 
}
namespace Perlink.Trinks.Fotos.ControleDeFotos.Repositories {

 
}
namespace Perlink.Trinks.Fotos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Fotos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Fotos.Factories.Repositories {

 
}
namespace Perlink.Trinks.Fotos.Repositories {

	public partial class FotoRepository : BaseActiveRecordRepository<Foto>, IFotoRepository {
		public Perlink.Trinks.Fotos.Factories.IFotoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Fotos.Factories.IFotoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Fotos.Repositories {

 
}
namespace Perlink.Trinks.Google.Comparers.Repositories {

 
}
namespace Perlink.Trinks.Google.DTO.Repositories {

 
}
namespace Perlink.Trinks.Google.Enums.Repositories {

 
}
namespace Perlink.Trinks.Google.Providers.Repositories {

 
}
namespace Perlink.Trinks.GyraMais.Repositories {

	public partial class DadosClienteRepository : BaseActiveRecordRepository<DadosCliente>, IDadosClienteRepository {
		public Perlink.Trinks.GyraMais.Factories.IDadosClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.GyraMais.Factories.IDadosClienteFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Identity.Repositories {

	public partial class ApiAccountRepository : BaseActiveRecordRepository<ApiAccount>, IApiAccountRepository {
		public Perlink.Trinks.Identity.Factories.IApiAccountFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Identity.Factories.IApiAccountFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Importacao.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Conversores.Repositories {

 
}
namespace Perlink.Trinks.Importacao.DTO.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Importadores.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Statics.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Repositories {

	public partial class SolicitacaoDeImportacaoRepository : BaseActiveRecordRepository<SolicitacaoDeImportacao>, ISolicitacaoDeImportacaoRepository {
		public Perlink.Trinks.ImportacaoDeDados.Factories.ISolicitacaoDeImportacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ImportacaoDeDados.Factories.ISolicitacaoDeImportacaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ImportacaoDeDados.DTO.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Importadores.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.TiposDeColuna.Repositories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.DTO.Repositories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories {

	public partial class EventoIntegracaoComOutrosSistemasRepository : BaseActiveRecordRepository<EventoIntegracaoComOutrosSistemas>, IEventoIntegracaoComOutrosSistemasRepository {
		public Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IEventoIntegracaoComOutrosSistemasFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IEventoIntegracaoComOutrosSistemasFactory>();	}
		}			
	}
	public partial class FranquiaComChaveDeIntegracaoRepository : BaseActiveRecordRepository<FranquiaComChaveDeIntegracao>, IFranquiaComChaveDeIntegracaoRepository {
		public Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IFranquiaComChaveDeIntegracaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IFranquiaComChaveDeIntegracaoFactory>();	}
		}			
	}
	public partial class FranquiaEstabelecimentoComChaveDeIntegracaoRepository : BaseActiveRecordRepository<FranquiaEstabelecimentoComChaveDeIntegracao>, IFranquiaEstabelecimentoComChaveDeIntegracaoRepository {
		public Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IFranquiaEstabelecimentoComChaveDeIntegracaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IFranquiaEstabelecimentoComChaveDeIntegracaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Enums.Repositories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.IntegracaoComTrinks.Repositories {

 
}
namespace Perlink.Trinks.InternoProduto.Enum.Repositories {

 
}
namespace Perlink.Trinks.InternoProduto.Repositories {

	public partial class QuestionarioProdutoRepository : BaseActiveRecordRepository<QuestionarioProduto>, IQuestionarioProdutoRepository {
		public Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoFactory>();	}
		}			
	}
	public partial class QuestionarioProdutoOpcaoDeRespostaRepository : BaseActiveRecordRepository<QuestionarioProdutoOpcaoDeResposta>, IQuestionarioProdutoOpcaoDeRespostaRepository {
		public Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoOpcaoDeRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoOpcaoDeRespostaFactory>();	}
		}			
	}
	public partial class QuestionarioProdutoPerguntaRepository : BaseActiveRecordRepository<QuestionarioProdutoPergunta>, IQuestionarioProdutoPerguntaRepository {
		public Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoPerguntaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoPerguntaFactory>();	}
		}			
	}
	public partial class QuestionarioProdutoRespondidoRepository : BaseActiveRecordRepository<QuestionarioProdutoRespondido>, IQuestionarioProdutoRespondidoRepository {
		public Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoRespondidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoRespondidoFactory>();	}
		}			
	}
	public partial class QuestionarioProdutoRespondidoRespostaRepository : BaseActiveRecordRepository<QuestionarioProdutoRespondidoResposta>, IQuestionarioProdutoRespondidoRespostaRepository {
		public Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoRespondidoRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoRespondidoRespostaFactory>();	}
		}			
	}
	public partial class QuestionarioProdutoTipoRespostaRepository : BaseActiveRecordRepository<QuestionarioProdutoTipoResposta>, IQuestionarioProdutoTipoRespostaRepository {
		public Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoTipoRespostaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoTipoRespostaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.LGPD.Helpers.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamento.DTOs.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamento.Enums.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamento.Repositories {

	public partial class ItemLinkDePagamentoRepository : BaseActiveRecordRepository<ItemLinkDePagamento>, IItemLinkDePagamentoRepository {
		public Perlink.Trinks.LinksDePagamento.Factories.IItemLinkDePagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.LinksDePagamento.Factories.IItemLinkDePagamentoFactory>();	}
		}			
	}
	public partial class LinkDePagamentoRepository : BaseActiveRecordRepository<LinkDePagamento>, ILinkDePagamentoRepository {
		public Perlink.Trinks.LinksDePagamento.Factories.ILinkDePagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.LinksDePagamento.Factories.ILinkDePagamentoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Repositories {

	public partial class LinkDePagamentoNoTrinksRepository : BaseActiveRecordRepository<LinkDePagamentoNoTrinks>, ILinkDePagamentoNoTrinksRepository {
		public Perlink.Trinks.LinksDePagamentoNoTrinks.Factories.ILinkDePagamentoNoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.LinksDePagamentoNoTrinks.Factories.ILinkDePagamentoNoTrinksFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Localizacoes.DTO.Repositories {

 
}
namespace Perlink.Trinks.Localizacoes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Loggers.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.Repositories {

	public partial class CorEtiquetaRepository : BaseActiveRecordRepository<CorEtiqueta>, ICorEtiquetaRepository {
		public Perlink.Trinks.Marcadores.Factories.ICorEtiquetaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marcadores.Factories.ICorEtiquetaFactory>();	}
		}			
	}
	public partial class EtiquetaRepository : BaseActiveRecordRepository<Etiqueta>, IEtiquetaRepository {
		public Perlink.Trinks.Marcadores.Factories.IEtiquetaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marcadores.Factories.IEtiquetaFactory>();	}
		}			
	}
	public partial class ObjetoEtiquetadoRepository : BaseActiveRecordRepository<ObjetoEtiquetado>, IObjetoEtiquetadoRepository {
		public Perlink.Trinks.Marcadores.Factories.IObjetoEtiquetadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marcadores.Factories.IObjetoEtiquetadoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Marcadores.DTO.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.Enums.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Repositories {

	public partial class ConfiguracoesEstabelecimentoMarketingRepository : BaseActiveRecordRepository<ConfiguracoesEstabelecimentoMarketing>, IConfiguracoesEstabelecimentoMarketingRepository {
		public Perlink.Trinks.Marketing.Factories.IConfiguracoesEstabelecimentoMarketingFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IConfiguracoesEstabelecimentoMarketingFactory>();	}
		}			
	}
	public partial class ConviteDeRetornoRepository : BaseActiveRecordRepository<ConviteDeRetorno>, IConviteDeRetornoRepository {
		public Perlink.Trinks.Marketing.Factories.IConviteDeRetornoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IConviteDeRetornoFactory>();	}
		}			
	}
	public partial class ConviteDeRetornoEmailRepository : BaseActiveRecordRepository<ConviteDeRetornoEmail>, IConviteDeRetornoEmailRepository {
		public Perlink.Trinks.Marketing.Factories.IConviteDeRetornoEmailFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IConviteDeRetornoEmailFactory>();	}
		}			
	}
	public partial class ConviteDeRetornoParaQuemEnviarRepository : BaseActiveRecordRepository<ConviteDeRetornoParaQuemEnviar>, IConviteDeRetornoParaQuemEnviarRepository {
		public Perlink.Trinks.Marketing.Factories.IConviteDeRetornoParaQuemEnviarFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IConviteDeRetornoParaQuemEnviarFactory>();	}
		}			
	}
	public partial class ConviteDeRetornoSMSRepository : BaseActiveRecordRepository<ConviteDeRetornoSMS>, IConviteDeRetornoSMSRepository {
		public Perlink.Trinks.Marketing.Factories.IConviteDeRetornoSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IConviteDeRetornoSMSFactory>();	}
		}			
	}
	public partial class ConviteDeRetornoWhatsAppRepository : BaseActiveRecordRepository<ConviteDeRetornoWhatsApp>, IConviteDeRetornoWhatsAppRepository {
		public Perlink.Trinks.Marketing.Factories.IConviteDeRetornoWhatsAppFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IConviteDeRetornoWhatsAppFactory>();	}
		}			
	}
	public partial class MarketingCampanhaRepository : BaseActiveRecordRepository<MarketingCampanha>, IMarketingCampanhaRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaFactory>();	}
		}			
	}
	public partial class MarketingCampanhaEmailRepository : BaseActiveRecordRepository<MarketingCampanhaEmail>, IMarketingCampanhaEmailRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaEmailFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaEmailFactory>();	}
		}			
	}
	public partial class MarketingCampanhaHistoricoRepository : BaseActiveRecordRepository<MarketingCampanhaHistorico>, IMarketingCampanhaHistoricoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaHistoricoFactory>();	}
		}			
	}
	public partial class MarketingCampanhaPublicoAlvoRepository : BaseActiveRecordRepository<MarketingCampanhaPublicoAlvo>, IMarketingCampanhaPublicoAlvoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoFactory>();	}
		}			
	}
	public partial class MarketingCampanhaPublicoAlvoServicoEstabelecimentoRepository : BaseActiveRecordRepository<MarketingCampanhaPublicoAlvoServicoEstabelecimento>, IMarketingCampanhaPublicoAlvoServicoEstabelecimentoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoFactory>();	}
		}			
	}
	public partial class MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoRepository : BaseActiveRecordRepository<MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo>, IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoFactory>();	}
		}			
	}
	public partial class MarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository : BaseActiveRecordRepository<MarketingCampanhaPublicoAlvoClienteEstabelecimento>, IMarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoClienteEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoClienteEstabelecimentoFactory>();	}
		}			
	}
	public partial class MarketingCampanhaPublicoAlvoProfissionalRepository : BaseActiveRecordRepository<MarketingCampanhaPublicoAlvoProfissional>, IMarketingCampanhaPublicoAlvoProfissionalRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoProfissionalFactory>();	}
		}			
	}
	public partial class MarketingCampanhaSMSRepository : BaseActiveRecordRepository<MarketingCampanhaSMS>, IMarketingCampanhaSMSRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaSMSFactory>();	}
		}			
	}
	public partial class MarketingCampanhaWhatsAppRepository : BaseActiveRecordRepository<MarketingCampanhaWhatsApp>, IMarketingCampanhaWhatsAppRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCampanhaWhatsAppFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCampanhaWhatsAppFactory>();	}
		}			
	}
	public partial class MarketingCompraCreditoRepository : BaseActiveRecordRepository<MarketingCompraCredito>, IMarketingCompraCreditoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingCompraCreditoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingCompraCreditoFactory>();	}
		}			
	}
	public partial class MarketingEnvioRepository : BaseActiveRecordRepository<MarketingEnvio>, IMarketingEnvioRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioFactory>();	}
		}			
	}
	public partial class MarketingEnvioClienteRepository : BaseActiveRecordRepository<MarketingEnvioCliente>, IMarketingEnvioClienteRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteFactory>();	}
		}			
	}
	public partial class MarketingEnvioClienteEmailRepository : BaseActiveRecordRepository<MarketingEnvioClienteEmail>, IMarketingEnvioClienteEmailRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteEmailFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteEmailFactory>();	}
		}			
	}
	public partial class MarketingEnvioClienteParametroEnvioRepository : BaseActiveRecordRepository<MarketingEnvioClienteParametroEnvio>, IMarketingEnvioClienteParametroEnvioRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteParametroEnvioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteParametroEnvioFactory>();	}
		}			
	}
	public partial class MarketingEnvioClienteSMSRepository : BaseActiveRecordRepository<MarketingEnvioClienteSMS>, IMarketingEnvioClienteSMSRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteSMSFactory>();	}
		}			
	}
	public partial class MarketingEnvioClienteWhatsAppRepository : BaseActiveRecordRepository<MarketingEnvioClienteWhatsApp>, IMarketingEnvioClienteWhatsAppRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteWhatsAppFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteWhatsAppFactory>();	}
		}			
	}
	public partial class MarketingEnvioEmailRepository : BaseActiveRecordRepository<MarketingEnvioEmail>, IMarketingEnvioEmailRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioEmailFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioEmailFactory>();	}
		}			
	}
	public partial class MarketingEnvioSMSRepository : BaseActiveRecordRepository<MarketingEnvioSMS>, IMarketingEnvioSMSRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioSMSFactory>();	}
		}			
	}
	public partial class MarketingEnvioWhatsAppRepository : BaseActiveRecordRepository<MarketingEnvioWhatsApp>, IMarketingEnvioWhatsAppRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingEnvioWhatsAppFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingEnvioWhatsAppFactory>();	}
		}			
	}
	public partial class MarketingFaixaProfissionaisRepository : BaseActiveRecordRepository<MarketingFaixaProfissionais>, IMarketingFaixaProfissionaisRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingFaixaProfissionaisFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingFaixaProfissionaisFactory>();	}
		}			
	}
	public partial class MarketingPacoteCreditoRepository : BaseActiveRecordRepository<MarketingPacoteCredito>, IMarketingPacoteCreditoRepository {
		public Perlink.Trinks.Marketing.Factories.IMarketingPacoteCreditoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Marketing.Factories.IMarketingPacoteCreditoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Marketing.DTO.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Enums.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Factories.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Filtro.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Strategies.Repositories {

 
}
namespace Perlink.Trinks.MarketingInterno.Repositories {

	public partial class DadosMarketingRepository : BaseActiveRecordRepository<DadosMarketing>, IDadosMarketingRepository {
		public Perlink.Trinks.MarketingInterno.Factories.IDadosMarketingFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.MarketingInterno.Factories.IDadosMarketingFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.MensagemEmTela.DTOs.Repositories {

 
}
namespace Perlink.Trinks.MensagemEmTela.Implementacoes.Repositories {

 
}
namespace Perlink.Trinks.MensagemEmTela.Repositories {

	public partial class MensagemAvisoRepository : BaseActiveRecordRepository<MensagemAviso>, IMensagemAvisoRepository {
		public Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoFactory>();	}
		}			
	}
	public partial class MensagemAvisoTextoLivreRepository : BaseActiveRecordRepository<MensagemAvisoTextoLivre>, IMensagemAvisoTextoLivreRepository {
		public Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoTextoLivreFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoTextoLivreFactory>();	}
		}			
	}
	public partial class MensagemAvisoImplementacaoRepository : BaseActiveRecordRepository<MensagemAvisoImplementacao>, IMensagemAvisoImplementacaoRepository {
		public Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoImplementacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoImplementacaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.MensagensEmMassa.Repositories {

	public partial class ModeloDeMensagemRepository : BaseActiveRecordRepository<ModeloDeMensagem>, IModeloDeMensagemRepository {
		public Perlink.Trinks.MensagensEmMassa.Factories.IModeloDeMensagemFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.MensagensEmMassa.Factories.IModeloDeMensagemFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.MessageQueue.Repositories {

 
}
namespace Perlink.Trinks.Metricas.Dto.Repositories {

 
}
namespace Perlink.Trinks.Metricas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Metricas.Repositories {

	public partial class MetricaDesativadaRepository : BaseActiveRecordRepository<MetricaDesativada>, IMetricaDesativadaRepository {
		public Perlink.Trinks.Metricas.Factories.IMetricaDesativadaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Metricas.Factories.IMetricaDesativadaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Repositories {

	public partial class ConfiguracaoDeNFCDoEstabelecimentoRepository : BaseActiveRecordRepository<ConfiguracaoDeNFCDoEstabelecimento>, IConfiguracaoDeNFCDoEstabelecimentoRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IConfiguracaoDeNFCDoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IConfiguracaoDeNFCDoEstabelecimentoFactory>();	}
		}			
	}
	public partial class ConfiguracaoNFCEstadoRepository : BaseActiveRecordRepository<ConfiguracaoNFCEstado>, IConfiguracaoNFCEstadoRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IConfiguracaoNFCEstadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IConfiguracaoNFCEstadoFactory>();	}
		}			
	}
	public partial class ImpressaoDeNFCRepository : BaseActiveRecordRepository<ImpressaoDeNFC>, IImpressaoDeNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IImpressaoDeNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IImpressaoDeNFCFactory>();	}
		}			
	}
	public partial class NfcSituacaoTributariaRepository : BaseActiveRecordRepository<NfcSituacaoTributaria>, INfcSituacaoTributariaRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INfcSituacaoTributariaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INfcSituacaoTributariaFactory>();	}
		}			
	}
	public partial class NomenclaturaNCMeNBSRepository : BaseActiveRecordRepository<NomenclaturaNCMeNBS>, INomenclaturaNCMeNBSRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INomenclaturaNCMeNBSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INomenclaturaNCMeNBSFactory>();	}
		}			
	}
	public partial class NotaFormaPagamentoNFCRepository : BaseActiveRecordRepository<NotaFormaPagamentoNFC>, INotaFormaPagamentoNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaFormaPagamentoNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaFormaPagamentoNFCFactory>();	}
		}			
	}
	public partial class NotaInutilizadaNFCRepository : BaseActiveRecordRepository<NotaInutilizadaNFC>, INotaInutilizadaNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaInutilizadaNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaInutilizadaNFCFactory>();	}
		}			
	}
	public partial class NotaItensNFCRepository : BaseActiveRecordRepository<NotaItensNFC>, INotaItensNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaItensNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaItensNFCFactory>();	}
		}			
	}
	public partial class NotaNFCRepository : BaseActiveRecordRepository<NotaNFC>, INotaNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaNFCFactory>();	}
		}			
	}
	public partial class PessoaJuridicaCertificadoDigitalRepository : BaseActiveRecordRepository<PessoaJuridicaCertificadoDigital>, IPessoaJuridicaCertificadoDigitalRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IPessoaJuridicaCertificadoDigitalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IPessoaJuridicaCertificadoDigitalFactory>();	}
		}			
	}
	public partial class StatusNotaNFCRepository : BaseActiveRecordRepository<StatusNotaNFC>, IStatusNotaNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IStatusNotaNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IStatusNotaNFCFactory>();	}
		}			
	}
	public partial class TabelaIBPTRepository : BaseActiveRecordRepository<TabelaIBPT>, ITabelaIBPTRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.ITabelaIBPTFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.ITabelaIBPTFactory>();	}
		}			
	}
	public partial class TipoRegimeTributarioNFCRepository : BaseActiveRecordRepository<TipoRegimeTributarioNFC>, ITipoRegimeTributarioNFCRepository {
		public Perlink.Trinks.NotaFiscalDoConsumidor.Factories.ITipoRegimeTributarioNFCFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotaFiscalDoConsumidor.Factories.ITipoRegimeTributarioNFCFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.DTO.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Enums.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Factories.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF.SAT.Cancelamento.Repositories {

 
}
namespace Perlink.Trinks.Web.Areas.BackOffice.Models.Produtos.Repositories {

 
}
namespace Perlink.Trinks.Notificacoes.Repositories {

	public partial class CampanhaPushRepository : BaseActiveRecordRepository<CampanhaPush>, ICampanhaPushRepository {
		public Perlink.Trinks.Notificacoes.Factories.ICampanhaPushFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.ICampanhaPushFactory>();	}
		}			
	}
	public partial class ContaQtdNotificacoesNovasRepository : BaseActiveRecordRepository<ContaQtdNotificacoesNovas>, IContaQtdNotificacoesNovasRepository {
		public Perlink.Trinks.Notificacoes.Factories.IContaQtdNotificacoesNovasFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IContaQtdNotificacoesNovasFactory>();	}
		}			
	}
	public partial class DispositivoRepository : BaseActiveRecordRepository<Dispositivo>, IDispositivoRepository {
		public Perlink.Trinks.Notificacoes.Factories.IDispositivoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IDispositivoFactory>();	}
		}			
	}
	public partial class DispositivoB2cValidoRepository : BaseActiveRecordRepository<DispositivoB2cValido>, IDispositivoB2cValidoRepository {
		public Perlink.Trinks.Notificacoes.Factories.IDispositivoB2cValidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IDispositivoB2cValidoFactory>();	}
		}			
	}
	public partial class InscricaoEmNotificacaoRepository : BaseActiveRecordRepository<InscricaoEmNotificacao>, IInscricaoEmNotificacaoRepository {
		public Perlink.Trinks.Notificacoes.Factories.IInscricaoEmNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IInscricaoEmNotificacaoFactory>();	}
		}			
	}
	public partial class NotificacaoDoTrinksRepository : BaseActiveRecordRepository<NotificacaoDoTrinks>, INotificacaoDoTrinksRepository {
		public Perlink.Trinks.Notificacoes.Factories.INotificacaoDoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.INotificacaoDoTrinksFactory>();	}
		}			
	}
	public partial class NotificacaoPushRepository : BaseActiveRecordRepository<NotificacaoPush>, INotificacaoPushRepository {
		public Perlink.Trinks.Notificacoes.Factories.INotificacaoPushFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.INotificacaoPushFactory>();	}
		}			
	}
	public partial class OperadoraRepository : BaseActiveRecordRepository<Operadora>, IOperadoraRepository {
		public Perlink.Trinks.Notificacoes.Factories.IOperadoraFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IOperadoraFactory>();	}
		}			
	}
	public partial class OperadoraServicoSMSRepository : BaseActiveRecordRepository<OperadoraServicoSMS>, IOperadoraServicoSMSRepository {
		public Perlink.Trinks.Notificacoes.Factories.IOperadoraServicoSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IOperadoraServicoSMSFactory>();	}
		}			
	}
	public partial class RegistroNotificacaoRepository : BaseActiveRecordRepository<RegistroNotificacao>, IRegistroNotificacaoRepository {
		public Perlink.Trinks.Notificacoes.Factories.IRegistroNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IRegistroNotificacaoFactory>();	}
		}			
	}
	public partial class ServicoSMSRepository : BaseActiveRecordRepository<ServicoSMS>, IServicoSMSRepository {
		public Perlink.Trinks.Notificacoes.Factories.IServicoSMSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.IServicoSMSFactory>();	}
		}			
	}
	public partial class TipoNotificacaoRepository : BaseActiveRecordRepository<TipoNotificacao>, ITipoNotificacaoRepository {
		public Perlink.Trinks.Notificacoes.Factories.ITipoNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Notificacoes.Factories.ITipoNotificacaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Notificacoes.DTO.Repositories {

 
}
namespace Perlink.Trinks.Notificacoes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Notificacoes.Filtros.Repositories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Repositories {

	public partial class CanalDaNotificacaoRepository : BaseActiveRecordRepository<CanalDaNotificacao>, ICanalDaNotificacaoRepository {
		public Perlink.Trinks.NotificacoesApps.Factories.ICanalDaNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotificacoesApps.Factories.ICanalDaNotificacaoFactory>();	}
		}			
	}
	public partial class EventoDeNotificacaoRepository : BaseActiveRecordRepository<EventoDeNotificacao>, IEventoDeNotificacaoRepository {
		public Perlink.Trinks.NotificacoesApps.Factories.IEventoDeNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotificacoesApps.Factories.IEventoDeNotificacaoFactory>();	}
		}			
	}
	public partial class MensagemDeNotificacaoRepository : BaseActiveRecordRepository<MensagemDeNotificacao>, IMensagemDeNotificacaoRepository {
		public Perlink.Trinks.NotificacoesApps.Factories.IMensagemDeNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotificacoesApps.Factories.IMensagemDeNotificacaoFactory>();	}
		}			
	}
	public partial class PreferenciaDeNotificacaoDoUsuarioRepository : BaseActiveRecordRepository<PreferenciaDeNotificacaoDoUsuario>, IPreferenciaDeNotificacaoDoUsuarioRepository {
		public Perlink.Trinks.NotificacoesApps.Factories.IPreferenciaDeNotificacaoDoUsuarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotificacoesApps.Factories.IPreferenciaDeNotificacaoDoUsuarioFactory>();	}
		}			
	}
	public partial class TipoDeNotificacaoRepository : BaseActiveRecordRepository<TipoDeNotificacao>, ITipoDeNotificacaoRepository {
		public Perlink.Trinks.NotificacoesApps.Factories.ITipoDeNotificacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.NotificacoesApps.Factories.ITipoDeNotificacaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.NotificacoesApps.DTO.Repositories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Repositories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Novidades.DTO.Repositories {

 
}
namespace Perlink.Trinks.Novidades.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.Comparers.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.DTOs.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.Enums.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.Repositories {

	public partial class EstabelecimentoTrilhaRepository : BaseActiveRecordRepository<EstabelecimentoTrilha>, IEstabelecimentoTrilhaRepository {
		public Perlink.Trinks.Onboardings.Factories.IEstabelecimentoTrilhaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.IEstabelecimentoTrilhaFactory>();	}
		}			
	}
	public partial class QuestionarioOnboardingPorFaixaRepository : BaseActiveRecordRepository<QuestionarioOnboardingPorFaixa>, IQuestionarioOnboardingPorFaixaRepository {
		public Perlink.Trinks.Onboardings.Factories.IQuestionarioOnboardingPorFaixaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.IQuestionarioOnboardingPorFaixaFactory>();	}
		}			
	}
	public partial class RastreioDeTarefaRepository : BaseActiveRecordRepository<RastreioDeTarefa>, IRastreioDeTarefaRepository {
		public Perlink.Trinks.Onboardings.Factories.IRastreioDeTarefaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.IRastreioDeTarefaFactory>();	}
		}			
	}
	public partial class TarefaRepository : BaseActiveRecordRepository<Tarefa>, ITarefaRepository {
		public Perlink.Trinks.Onboardings.Factories.ITarefaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.ITarefaFactory>();	}
		}			
	}
	public partial class TrilhaRepository : BaseActiveRecordRepository<Trilha>, ITrilhaRepository {
		public Perlink.Trinks.Onboardings.Factories.ITrilhaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.ITrilhaFactory>();	}
		}			
	}
	public partial class TrilhaAcaoRepository : BaseActiveRecordRepository<TrilhaAcao>, ITrilhaAcaoRepository {
		public Perlink.Trinks.Onboardings.Factories.ITrilhaAcaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.ITrilhaAcaoFactory>();	}
		}			
	}
	public partial class TrilhaAcaoGrupoRepository : BaseActiveRecordRepository<TrilhaAcaoGrupo>, ITrilhaAcaoGrupoRepository {
		public Perlink.Trinks.Onboardings.Factories.ITrilhaAcaoGrupoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Onboardings.Factories.ITrilhaAcaoGrupoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Onboardings.Stories.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Adapters.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Repositories {

	public partial class ConfiguracaoPacotePersonalizadoRepository : BaseActiveRecordRepository<ConfiguracaoPacotePersonalizado>, IConfiguracaoPacotePersonalizadoRepository {
		public Perlink.Trinks.Pacotes.Factories.IConfiguracaoPacotePersonalizadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IConfiguracaoPacotePersonalizadoFactory>();	}
		}			
	}
	public partial class HistoricoConfiguracoesPacoteRepository : BaseActiveRecordRepository<HistoricoConfiguracoesPacote>, IHistoricoConfiguracoesPacoteRepository {
		public Perlink.Trinks.Pacotes.Factories.IHistoricoConfiguracoesPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IHistoricoConfiguracoesPacoteFactory>();	}
		}			
	}
	public partial class ItemPacoteRepository : BaseActiveRecordRepository<ItemPacote>, IItemPacoteRepository {
		public Perlink.Trinks.Pacotes.Factories.IItemPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IItemPacoteFactory>();	}
		}			
	}
	public partial class ItemPacoteClienteRepository : BaseActiveRecordRepository<ItemPacoteCliente>, IItemPacoteClienteRepository {
		public Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteFactory>();	}
		}			
	}
	public partial class ItemPacoteClienteProdutoRepository : BaseActiveRecordRepository<ItemPacoteClienteProduto>, IItemPacoteClienteProdutoRepository {
		public Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteProdutoFactory>();	}
		}			
	}
	public partial class ItemPacoteClienteServicoRepository : BaseActiveRecordRepository<ItemPacoteClienteServico>, IItemPacoteClienteServicoRepository {
		public Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteServicoFactory>();	}
		}			
	}
	public partial class ItemPacoteProdutoRepository : BaseActiveRecordRepository<ItemPacoteProduto>, IItemPacoteProdutoRepository {
		public Perlink.Trinks.Pacotes.Factories.IItemPacoteProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IItemPacoteProdutoFactory>();	}
		}			
	}
	public partial class ItemPacoteServicoRepository : BaseActiveRecordRepository<ItemPacoteServico>, IItemPacoteServicoRepository {
		public Perlink.Trinks.Pacotes.Factories.IItemPacoteServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IItemPacoteServicoFactory>();	}
		}			
	}
	public partial class LinkDePagamentoDoPacoteRepository : BaseActiveRecordRepository<LinkDePagamentoDoPacote>, ILinkDePagamentoDoPacoteRepository {
		public Perlink.Trinks.Pacotes.Factories.ILinkDePagamentoDoPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.ILinkDePagamentoDoPacoteFactory>();	}
		}			
	}
	public partial class PacoteRepository : BaseActiveRecordRepository<Pacote>, IPacoteRepository {
		public Perlink.Trinks.Pacotes.Factories.IPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IPacoteFactory>();	}
		}			
	}
	public partial class PacoteClienteRepository : BaseActiveRecordRepository<PacoteCliente>, IPacoteClienteRepository {
		public Perlink.Trinks.Pacotes.Factories.IPacoteClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IPacoteClienteFactory>();	}
		}			
	}
	public partial class PacoteClienteHistoricoRepository : BaseActiveRecordRepository<PacoteClienteHistorico>, IPacoteClienteHistoricoRepository {
		public Perlink.Trinks.Pacotes.Factories.IPacoteClienteHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pacotes.Factories.IPacoteClienteHistoricoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Pacotes.DTO.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Factories.Repositories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.DTO.Repositories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Enums.Repositories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories {

	public partial class PagamentoAntecipadoHotsiteConfiguracoesRepository : BaseActiveRecordRepository<PagamentoAntecipadoHotsiteConfiguracoes>, IPagamentoAntecipadoHotsiteConfiguracoesRepository {
		public Perlink.Trinks.PagamentoAntecipadoHotsite.Factories.IPagamentoAntecipadoHotsiteConfiguracoesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentoAntecipadoHotsite.Factories.IPagamentoAntecipadoHotsiteConfiguracoesFactory>();	}
		}			
	}
	public partial class PagamentoAntecipadoHotsiteServicosRepository : BaseActiveRecordRepository<PagamentoAntecipadoHotsiteServicos>, IPagamentoAntecipadoHotsiteServicosRepository {
		public Perlink.Trinks.PagamentoAntecipadoHotsite.Factories.IPagamentoAntecipadoHotsiteServicosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentoAntecipadoHotsite.Factories.IPagamentoAntecipadoHotsiteServicosFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Pagamentos.Calculos.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Repositories {

	public partial class CartaoDeCompradorRepository : BaseActiveRecordRepository<CartaoDeComprador>, ICartaoDeCompradorRepository {
		public Perlink.Trinks.Pagamentos.Factories.ICartaoDeCompradorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ICartaoDeCompradorFactory>();	}
		}			
	}
	public partial class CartaoDeCompradorGatewayRepository : BaseActiveRecordRepository<CartaoDeCompradorGateway>, ICartaoDeCompradorGatewayRepository {
		public Perlink.Trinks.Pagamentos.Factories.ICartaoDeCompradorGatewayFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ICartaoDeCompradorGatewayFactory>();	}
		}			
	}
	public partial class CompradorRepository : BaseActiveRecordRepository<Comprador>, ICompradorRepository {
		public Perlink.Trinks.Pagamentos.Factories.ICompradorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ICompradorFactory>();	}
		}			
	}
	public partial class CompradorGatewayRepository : BaseActiveRecordRepository<CompradorGateway>, ICompradorGatewayRepository {
		public Perlink.Trinks.Pagamentos.Factories.ICompradorGatewayFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ICompradorGatewayFactory>();	}
		}			
	}
	public partial class ContaBancariaRepository : BaseActiveRecordRepository<ContaBancaria>, IContaBancariaRepository {
		public Perlink.Trinks.Pagamentos.Factories.IContaBancariaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IContaBancariaFactory>();	}
		}			
	}
	public partial class ContaBancariaGatewayRepository : BaseActiveRecordRepository<ContaBancariaGateway>, IContaBancariaGatewayRepository {
		public Perlink.Trinks.Pagamentos.Factories.IContaBancariaGatewayFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IContaBancariaGatewayFactory>();	}
		}			
	}
	public partial class DocumentoDeRecebedorCredenciadoRepository : BaseActiveRecordRepository<DocumentoDeRecebedorCredenciado>, IDocumentoDeRecebedorCredenciadoRepository {
		public Perlink.Trinks.Pagamentos.Factories.IDocumentoDeRecebedorCredenciadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IDocumentoDeRecebedorCredenciadoFactory>();	}
		}			
	}
	public partial class EnderecoDeCobrancaRepository : BaseActiveRecordRepository<EnderecoDeCobranca>, IEnderecoDeCobrancaRepository {
		public Perlink.Trinks.Pagamentos.Factories.IEnderecoDeCobrancaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IEnderecoDeCobrancaFactory>();	}
		}			
	}
	public partial class EtapaCadastroPagarmeRepository : BaseActiveRecordRepository<EtapaCadastroPagarme>, IEtapaCadastroPagarmeRepository {
		public Perlink.Trinks.Pagamentos.Factories.IEtapaCadastroPagarmeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IEtapaCadastroPagarmeFactory>();	}
		}			
	}
	public partial class GatewayRepository : BaseActiveRecordRepository<Gateway>, IGatewayRepository {
		public Perlink.Trinks.Pagamentos.Factories.IGatewayFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IGatewayFactory>();	}
		}			
	}
	public partial class ItemPagamentoRepository : BaseActiveRecordRepository<ItemPagamento>, IItemPagamentoRepository {
		public Perlink.Trinks.Pagamentos.Factories.IItemPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IItemPagamentoFactory>();	}
		}			
	}
	public partial class LimitePagamentoRepository : BaseActiveRecordRepository<LimitePagamento>, ILimitePagamentoRepository {
		public Perlink.Trinks.Pagamentos.Factories.ILimitePagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ILimitePagamentoFactory>();	}
		}			
	}
	public partial class LimitePagamentoRecebedorRepository : BaseActiveRecordRepository<LimitePagamentoRecebedor>, ILimitePagamentoRecebedorRepository {
		public Perlink.Trinks.Pagamentos.Factories.ILimitePagamentoRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ILimitePagamentoRecebedorFactory>();	}
		}			
	}
	public partial class PagamentoRepository : BaseActiveRecordRepository<Pagamento>, IPagamentoRepository {
		public Perlink.Trinks.Pagamentos.Factories.IPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IPagamentoFactory>();	}
		}			
	}
	public partial class RecebedorRepository : BaseActiveRecordRepository<Recebedor>, IRecebedorRepository {
		public Perlink.Trinks.Pagamentos.Factories.IRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IRecebedorFactory>();	}
		}			
	}
	public partial class RecebedorCredenciadoRepository : BaseActiveRecordRepository<RecebedorCredenciado>, IRecebedorCredenciadoRepository {
		public Perlink.Trinks.Pagamentos.Factories.IRecebedorCredenciadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IRecebedorCredenciadoFactory>();	}
		}			
	}
	public partial class SplitPagamentoRepository : BaseActiveRecordRepository<SplitPagamento>, ISplitPagamentoRepository {
		public Perlink.Trinks.Pagamentos.Factories.ISplitPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.ISplitPagamentoFactory>();	}
		}			
	}
	public partial class UsoLimiteAntecipacaoDiarioRecebedorRepository : BaseActiveRecordRepository<UsoLimiteAntecipacaoDiarioRecebedor>, IUsoLimiteAntecipacaoDiarioRecebedorRepository {
		public Perlink.Trinks.Pagamentos.Factories.IUsoLimiteAntecipacaoDiarioRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IUsoLimiteAntecipacaoDiarioRecebedorFactory>();	}
		}			
	}
	public partial class UsoLimiteAntecipacaoMensalRecebedorRepository : BaseActiveRecordRepository<UsoLimiteAntecipacaoMensalRecebedor>, IUsoLimiteAntecipacaoMensalRecebedorRepository {
		public Perlink.Trinks.Pagamentos.Factories.IUsoLimiteAntecipacaoMensalRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IUsoLimiteAntecipacaoMensalRecebedorFactory>();	}
		}			
	}
	public partial class UsoLimitePagamentoDiarioRecebedorRepository : BaseActiveRecordRepository<UsoLimitePagamentoDiarioRecebedor>, IUsoLimitePagamentoDiarioRecebedorRepository {
		public Perlink.Trinks.Pagamentos.Factories.IUsoLimitePagamentoDiarioRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IUsoLimitePagamentoDiarioRecebedorFactory>();	}
		}			
	}
	public partial class UsoLimitePagamentoMensalRecebedorRepository : BaseActiveRecordRepository<UsoLimitePagamentoMensalRecebedor>, IUsoLimitePagamentoMensalRecebedorRepository {
		public Perlink.Trinks.Pagamentos.Factories.IUsoLimitePagamentoMensalRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pagamentos.Factories.IUsoLimitePagamentoMensalRecebedorFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Pagamentos.Config.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Factories.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Providers.Repositories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Repositories {

	public partial class BeneficiosEstabelecimentoRepository : BaseActiveRecordRepository<BeneficiosEstabelecimento>, IBeneficiosEstabelecimentoRepository {
		public Perlink.Trinks.PagamentosAntecipados.Factories.IBeneficiosEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosAntecipados.Factories.IBeneficiosEstabelecimentoFactory>();	}
		}			
	}
	public partial class BeneficiosPagamentoRepository : BaseActiveRecordRepository<BeneficiosPagamento>, IBeneficiosPagamentoRepository {
		public Perlink.Trinks.PagamentosAntecipados.Factories.IBeneficiosPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosAntecipados.Factories.IBeneficiosPagamentoFactory>();	}
		}			
	}
	public partial class ItemPagamentoAntecipadoRepository : BaseActiveRecordRepository<ItemPagamentoAntecipado>, IItemPagamentoAntecipadoRepository {
		public Perlink.Trinks.PagamentosAntecipados.Factories.IItemPagamentoAntecipadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosAntecipados.Factories.IItemPagamentoAntecipadoFactory>();	}
		}			
	}
	public partial class ItemPagamentoHorarioRepository : BaseActiveRecordRepository<ItemPagamentoHorario>, IItemPagamentoHorarioRepository {
		public Perlink.Trinks.PagamentosAntecipados.Factories.IItemPagamentoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosAntecipados.Factories.IItemPagamentoHorarioFactory>();	}
		}			
	}
	public partial class PagamentoAntecipadoRepository : BaseActiveRecordRepository<PagamentoAntecipado>, IPagamentoAntecipadoRepository {
		public Perlink.Trinks.PagamentosAntecipados.Factories.IPagamentoAntecipadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosAntecipados.Factories.IPagamentoAntecipadoFactory>();	}
		}			
	}
	public partial class ServicoHabilitadoRepository : BaseActiveRecordRepository<ServicoHabilitado>, IServicoHabilitadoRepository {
		public Perlink.Trinks.PagamentosAntecipados.Factories.IServicoHabilitadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosAntecipados.Factories.IServicoHabilitadoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.PagamentosAntecipados.DTO.Repositories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Enums.Repositories {

 
}
namespace Perlink.Trinks.Specifications.Repositories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Utils.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories {

	public partial class ConfiguracaoAdiantamentoFuncionalidadeAntecipacaoRepository : BaseActiveRecordRepository<ConfiguracaoAdiantamentoFuncionalidadeAntecipacao>, IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoFactory>();	}
		}			
	}
	public partial class ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository : BaseActiveRecordRepository<ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline>, IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineFactory>();	}
		}			
	}
	public partial class EstabelecimentoRecebedorRepository : BaseActiveRecordRepository<EstabelecimentoRecebedor>, IEstabelecimentoRecebedorRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IEstabelecimentoRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IEstabelecimentoRecebedorFactory>();	}
		}			
	}
	public partial class InstituicaoBancariaRepository : BaseActiveRecordRepository<InstituicaoBancaria>, IInstituicaoBancariaRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IInstituicaoBancariaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IInstituicaoBancariaFactory>();	}
		}			
	}
	public partial class LOGCancelamentoAntecipacaoRepository : BaseActiveRecordRepository<LOGCancelamentoAntecipacao>, ILOGCancelamentoAntecipacaoRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ILOGCancelamentoAntecipacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ILOGCancelamentoAntecipacaoFactory>();	}
		}			
	}
	public partial class LOGSolicitacaoAntecipacaoRepository : BaseActiveRecordRepository<LOGSolicitacaoAntecipacao>, ILOGSolicitacaoAntecipacaoRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ILOGSolicitacaoAntecipacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ILOGSolicitacaoAntecipacaoFactory>();	}
		}			
	}
	public partial class PagamentoOnlineNoTrinksRepository : BaseActiveRecordRepository<PagamentoOnlineNoTrinks>, IPagamentoOnlineNoTrinksRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IPagamentoOnlineNoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IPagamentoOnlineNoTrinksFactory>();	}
		}			
	}
	public partial class TaxasDoEstabelecimentoRepository : BaseActiveRecordRepository<TaxasDoEstabelecimento>, ITaxasDoEstabelecimentoRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasDoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasDoEstabelecimentoFactory>();	}
		}			
	}
	public partial class TaxasDoPagamentoOnlineNoTrinksRepository : BaseActiveRecordRepository<TaxasDoPagamentoOnlineNoTrinks>, ITaxasDoPagamentoOnlineNoTrinksRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasDoPagamentoOnlineNoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasDoPagamentoOnlineNoTrinksFactory>();	}
		}			
	}
	public partial class TaxasPadraoRepository : BaseActiveRecordRepository<TaxasPadrao>, ITaxasPadraoRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasPadraoFactory>();	}
		}			
	}
	public partial class TaxasPadraoEstabelecimentoRecebedorRepository : BaseActiveRecordRepository<TaxasPadraoEstabelecimentoRecebedor>, ITaxasPadraoEstabelecimentoRecebedorRepository {
		public Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasPadraoEstabelecimentoRecebedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasPadraoEstabelecimentoRecebedorFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.Repositories {

 
}
namespace Perlink.Trinks.PDF.Repositories {

 
}
namespace Perlink.Trinks.Permissoes.AreaPerlink.Repositories {

	public partial class AreaPerlinkPerfilRepository : BaseActiveRecordRepository<AreaPerlinkPerfil>, IAreaPerlinkPerfilRepository {
		public Perlink.Trinks.Permissoes.AreaPerlink.Factories.IAreaPerlinkPerfilFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.AreaPerlink.Factories.IAreaPerlinkPerfilFactory>();	}
		}			
	}
	public partial class AreaPerlinkPerfilPermissaoRepository : BaseActiveRecordRepository<AreaPerlinkPerfilPermissao>, IAreaPerlinkPerfilPermissaoRepository {
		public Perlink.Trinks.Permissoes.AreaPerlink.Factories.IAreaPerlinkPerfilPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.AreaPerlink.Factories.IAreaPerlinkPerfilPermissaoFactory>();	}
		}			
	}
	public partial class ContaPerfilRepository : BaseActiveRecordRepository<ContaPerfil>, IContaPerfilRepository {
		public Perlink.Trinks.Permissoes.AreaPerlink.Factories.IContaPerfilFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.AreaPerlink.Factories.IContaPerfilFactory>();	}
		}			
	}
	public partial class ContaPerfilPermissaoRepository : BaseActiveRecordRepository<ContaPerfilPermissao>, IContaPerfilPermissaoRepository {
		public Perlink.Trinks.Permissoes.AreaPerlink.Factories.IContaPerfilPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.AreaPerlink.Factories.IContaPerfilPermissaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Permissoes.AreaPerlink.Repositories {

 
}
namespace Perlink.Trinks.Permissoes.Repositories {

	public partial class CategoriaPermissaoRepository : BaseActiveRecordRepository<CategoriaPermissao>, ICategoriaPermissaoRepository {
		public Perlink.Trinks.Permissoes.Factories.ICategoriaPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.ICategoriaPermissaoFactory>();	}
		}			
	}
	public partial class DescricaoPermissaoRepository : BaseActiveRecordRepository<DescricaoPermissao>, IDescricaoPermissaoRepository {
		public Perlink.Trinks.Permissoes.Factories.IDescricaoPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IDescricaoPermissaoFactory>();	}
		}			
	}
	public partial class FranquiaPermissaoRepository : BaseActiveRecordRepository<FranquiaPermissao>, IFranquiaPermissaoRepository {
		public Perlink.Trinks.Permissoes.Factories.IFranquiaPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IFranquiaPermissaoFactory>();	}
		}			
	}
	public partial class PerfilRepository : BaseActiveRecordRepository<Perfil>, IPerfilRepository {
		public Perlink.Trinks.Permissoes.Factories.IPerfilFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IPerfilFactory>();	}
		}			
	}
	public partial class PerfilPermissaoRepository : BaseActiveRecordRepository<PerfilPermissao>, IPerfilPermissaoRepository {
		public Perlink.Trinks.Permissoes.Factories.IPerfilPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IPerfilPermissaoFactory>();	}
		}			
	}
	public partial class PermissaoAreaRepository : BaseActiveRecordRepository<PermissaoArea>, IPermissaoAreaRepository {
		public Perlink.Trinks.Permissoes.Factories.IPermissaoAreaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IPermissaoAreaFactory>();	}
		}			
	}
	public partial class UsuarioPerfilRepository : BaseActiveRecordRepository<UsuarioPerfil>, IUsuarioPerfilRepository {
		public Perlink.Trinks.Permissoes.Factories.IUsuarioPerfilFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IUsuarioPerfilFactory>();	}
		}			
	}
	public partial class UsuarioPerfilPermissaoRepository : BaseActiveRecordRepository<UsuarioPerfilPermissao>, IUsuarioPerfilPermissaoRepository {
		public Perlink.Trinks.Permissoes.Factories.IUsuarioPerfilPermissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Permissoes.Factories.IUsuarioPerfilPermissaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Permissoes.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Repositories {

	public partial class BairroRepository : BaseActiveRecordRepository<Bairro>, IBairroRepository {
		public Perlink.Trinks.Pessoas.Factories.IBairroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IBairroFactory>();	}
		}			
	}
	public partial class CacheLocalidadeRepository : BaseActiveRecordRepository<CacheLocalidade>, ICacheLocalidadeRepository {
		public Perlink.Trinks.Pessoas.Factories.ICacheLocalidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ICacheLocalidadeFactory>();	}
		}			
	}
	public partial class CategoriaPortalServicoRepository : BaseActiveRecordRepository<CategoriaPortalServico>, ICategoriaPortalServicoRepository {
		public Perlink.Trinks.Pessoas.Factories.ICategoriaPortalServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ICategoriaPortalServicoFactory>();	}
		}			
	}
	public partial class CidadeRepository : BaseActiveRecordRepository<Cidade>, ICidadeRepository {
		public Perlink.Trinks.Pessoas.Factories.ICidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ICidadeFactory>();	}
		}			
	}
	public partial class ClienteRepository : BaseActiveRecordRepository<Cliente>, IClienteRepository {
		public Perlink.Trinks.Pessoas.Factories.IClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IClienteFactory>();	}
		}			
	}
	public partial class ClienteAreaPerlinkRepository : BaseActiveRecordRepository<ClienteAreaPerlink>, IClienteAreaPerlinkRepository {
		public Perlink.Trinks.Pessoas.Factories.IClienteAreaPerlinkFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IClienteAreaPerlinkFactory>();	}
		}			
	}
	public partial class ClienteEstabelecimentoRepository : BaseActiveRecordRepository<ClienteEstabelecimento>, IClienteEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IClienteEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IClienteEstabelecimentoFactory>();	}
		}			
	}
	public partial class ClienteEstabelecimentoSaldosRepository : BaseActiveRecordRepository<ClienteEstabelecimentoSaldos>, IClienteEstabelecimentoSaldosRepository {
		public Perlink.Trinks.Pessoas.Factories.IClienteEstabelecimentoSaldosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IClienteEstabelecimentoSaldosFactory>();	}
		}			
	}
	public partial class ComoConheceuRepository : BaseActiveRecordRepository<ComoConheceu>, IComoConheceuRepository {
		public Perlink.Trinks.Pessoas.Factories.IComoConheceuFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IComoConheceuFactory>();	}
		}			
	}
	public partial class ComoEstabelecimentoConheceuOTrinksRepository : BaseActiveRecordRepository<ComoEstabelecimentoConheceuOTrinks>, IComoEstabelecimentoConheceuOTrinksRepository {
		public Perlink.Trinks.Pessoas.Factories.IComoEstabelecimentoConheceuOTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IComoEstabelecimentoConheceuOTrinksFactory>();	}
		}			
	}
	public partial class CompartilhamentoNaRedeRepository : BaseActiveRecordRepository<CompartilhamentoNaRede>, ICompartilhamentoNaRedeRepository {
		public Perlink.Trinks.Pessoas.Factories.ICompartilhamentoNaRedeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ICompartilhamentoNaRedeFactory>();	}
		}			
	}
	public partial class ConfiguracaoHotsiteAderenciaRepository : BaseActiveRecordRepository<ConfiguracaoHotsiteAderencia>, IConfiguracaoHotsiteAderenciaRepository {
		public Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteAderenciaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteAderenciaFactory>();	}
		}			
	}
	public partial class ConfiguracaoHotsiteInicioMarcosRepository : BaseActiveRecordRepository<ConfiguracaoHotsiteInicioMarcos>, IConfiguracaoHotsiteInicioMarcosRepository {
		public Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteInicioMarcosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteInicioMarcosFactory>();	}
		}			
	}
	public partial class ConfiguracaoHotsiteIntervaloRepository : BaseActiveRecordRepository<ConfiguracaoHotsiteIntervalo>, IConfiguracaoHotsiteIntervaloRepository {
		public Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteIntervaloFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteIntervaloFactory>();	}
		}			
	}
	public partial class ConfiguracaoHotsiteUniversoRepository : BaseActiveRecordRepository<ConfiguracaoHotsiteUniverso>, IConfiguracaoHotsiteUniversoRepository {
		public Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteUniversoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteUniversoFactory>();	}
		}			
	}
	public partial class ContaRepository : BaseActiveRecordRepository<Conta>, IContaRepository {
		public Perlink.Trinks.Pessoas.Factories.IContaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IContaFactory>();	}
		}			
	}
	public partial class ContaFranquiaRepository : BaseActiveRecordRepository<ContaFranquia>, IContaFranquiaRepository {
		public Perlink.Trinks.Pessoas.Factories.IContaFranquiaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IContaFranquiaFactory>();	}
		}			
	}
	public partial class DadosParaRecalculoComissaoRepository : BaseActiveRecordRepository<DadosParaRecalculoComissao>, IDadosParaRecalculoComissaoRepository {
		public Perlink.Trinks.Pessoas.Factories.IDadosParaRecalculoComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IDadosParaRecalculoComissaoFactory>();	}
		}			
	}
	public partial class DataEspecialRepository : BaseActiveRecordRepository<DataEspecial>, IDataEspecialRepository {
		public Perlink.Trinks.Pessoas.Factories.IDataEspecialFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IDataEspecialFactory>();	}
		}			
	}
	public partial class DiaSemanaRepository : BaseActiveRecordRepository<DiaSemana>, IDiaSemanaRepository {
		public Perlink.Trinks.Pessoas.Factories.IDiaSemanaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IDiaSemanaFactory>();	}
		}			
	}
	public partial class EmailRejeitadoAmazonRepository : BaseActiveRecordRepository<EmailRejeitadoAmazon>, IEmailRejeitadoAmazonRepository {
		public Perlink.Trinks.Pessoas.Factories.IEmailRejeitadoAmazonFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEmailRejeitadoAmazonFactory>();	}
		}			
	}
	public partial class EnderecoRepository : BaseActiveRecordRepository<Endereco>, IEnderecoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEnderecoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEnderecoFactory>();	}
		}			
	}
	public partial class EnderecoPreenchidoManualmenteRepository : BaseActiveRecordRepository<EnderecoPreenchidoManualmente>, IEnderecoPreenchidoManualmenteRepository {
		public Perlink.Trinks.Pessoas.Factories.IEnderecoPreenchidoManualmenteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEnderecoPreenchidoManualmenteFactory>();	}
		}			
	}
	public partial class EstabelecimentoRepository : BaseActiveRecordRepository<Estabelecimento>, IEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFactory>();	}
		}			
	}
	public partial class EstabelecimentoAssistenteServicoRepository : BaseActiveRecordRepository<EstabelecimentoAssistenteServico>, IEstabelecimentoAssistenteServicoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAssistenteServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAssistenteServicoFactory>();	}
		}			
	}
	public partial class EstabelecimentoAssistenteServicoComissaoRepository : BaseActiveRecordRepository<EstabelecimentoAssistenteServicoComissao>, IEstabelecimentoAssistenteServicoComissaoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAssistenteServicoComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAssistenteServicoComissaoFactory>();	}
		}			
	}
	public partial class EstabelecimentoAtendeCriancaRepository : BaseActiveRecordRepository<EstabelecimentoAtendeCrianca>, IEstabelecimentoAtendeCriancaRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAtendeCriancaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAtendeCriancaFactory>();	}
		}			
	}
	public partial class EstabelecimentoConfiguracaoComissaoRepository : BaseActiveRecordRepository<EstabelecimentoConfiguracaoComissao>, IEstabelecimentoConfiguracaoComissaoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoComissaoFactory>();	}
		}			
	}
	public partial class EstabelecimentoConfiguracaoGeralRepository : BaseActiveRecordRepository<EstabelecimentoConfiguracaoGeral>, IEstabelecimentoConfiguracaoGeralRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoGeralFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoGeralFactory>();	}
		}			
	}
	public partial class EstabelecimentoConfiguracaoPOSRepository : BaseActiveRecordRepository<EstabelecimentoConfiguracaoPOS>, IEstabelecimentoConfiguracaoPOSRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoPOSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoPOSFactory>();	}
		}			
	}
	public partial class EstabelecimentoDadosGeraisRepository : BaseActiveRecordRepository<EstabelecimentoDadosGerais>, IEstabelecimentoDadosGeraisRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosGeraisFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosGeraisFactory>();	}
		}			
	}
	public partial class EstabelecimentoDadosPreCadastroRepository : BaseActiveRecordRepository<EstabelecimentoDadosPreCadastro>, IEstabelecimentoDadosPreCadastroRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroFactory>();	}
		}			
	}
	public partial class EstabelecimentoDadosPreCadastroCargoRepository : BaseActiveRecordRepository<EstabelecimentoDadosPreCadastroCargo>, IEstabelecimentoDadosPreCadastroCargoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroCargoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroCargoFactory>();	}
		}			
	}
	public partial class EstabelecimentoDadosPreCadastroMotivosCadastroRepository : BaseActiveRecordRepository<EstabelecimentoDadosPreCadastroMotivosCadastro>, IEstabelecimentoDadosPreCadastroMotivosCadastroRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroMotivosCadastroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroMotivosCadastroFactory>();	}
		}			
	}
	public partial class EstabelecimentoDadosPreCadastroSegmentosRepository : BaseActiveRecordRepository<EstabelecimentoDadosPreCadastroSegmentos>, IEstabelecimentoDadosPreCadastroSegmentosRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroSegmentosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroSegmentosFactory>();	}
		}			
	}
	public partial class EstabelecimentoFabricanteProdutoRepository : BaseActiveRecordRepository<EstabelecimentoFabricanteProduto>, IEstabelecimentoFabricanteProdutoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFabricanteProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFabricanteProdutoFactory>();	}
		}			
	}
	public partial class EstabelecimentoFormaPagamentoRepository : BaseActiveRecordRepository<EstabelecimentoFormaPagamento>, IEstabelecimentoFormaPagamentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFormaPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFormaPagamentoFactory>();	}
		}			
	}
	public partial class EstabelecimentoFormaPagamentoParcelaRepository : BaseActiveRecordRepository<EstabelecimentoFormaPagamentoParcela>, IEstabelecimentoFormaPagamentoParcelaRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFormaPagamentoParcelaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFormaPagamentoParcelaFactory>();	}
		}			
	}
	public partial class EstabelecimentoFornecedorRepository : BaseActiveRecordRepository<EstabelecimentoFornecedor>, IEstabelecimentoFornecedorRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFornecedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFornecedorFactory>();	}
		}			
	}
	public partial class EstabelecimentoHorarioEspecialFuncionamentoRepository : BaseActiveRecordRepository<EstabelecimentoHorarioEspecialFuncionamento>, IEstabelecimentoHorarioEspecialFuncionamentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioEspecialFuncionamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioEspecialFuncionamentoFactory>();	}
		}			
	}
	public partial class EstabelecimentoHorarioEspecialFuncionamentoTipoRepository : BaseActiveRecordRepository<EstabelecimentoHorarioEspecialFuncionamentoTipo>, IEstabelecimentoHorarioEspecialFuncionamentoTipoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioEspecialFuncionamentoTipoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioEspecialFuncionamentoTipoFactory>();	}
		}			
	}
	public partial class EstabelecimentoHorarioFuncionamentoRepository : BaseActiveRecordRepository<EstabelecimentoHorarioFuncionamento>, IEstabelecimentoHorarioFuncionamentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioFuncionamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioFuncionamentoFactory>();	}
		}			
	}
	public partial class EstabelecimentoIndicadoRepository : BaseActiveRecordRepository<EstabelecimentoIndicado>, IEstabelecimentoIndicadoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoIndicadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoIndicadoFactory>();	}
		}			
	}
	public partial class EstabelecimentoMovimentacaoEstoqueRepository : BaseActiveRecordRepository<EstabelecimentoMovimentacaoEstoque>, IEstabelecimentoMovimentacaoEstoqueRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoMovimentacaoEstoqueFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoMovimentacaoEstoqueFactory>();	}
		}			
	}
	public partial class EstabelecimentoPossuiEstacionamentoRepository : BaseActiveRecordRepository<EstabelecimentoPossuiEstacionamento>, IEstabelecimentoPossuiEstacionamentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoPossuiEstacionamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoPossuiEstacionamentoFactory>();	}
		}			
	}
	public partial class EstabelecimentoPreCadastroRepository : BaseActiveRecordRepository<EstabelecimentoPreCadastro>, IEstabelecimentoPreCadastroRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoPreCadastroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoPreCadastroFactory>();	}
		}			
	}
	public partial class EstabelecimentoProdutoRepository : BaseActiveRecordRepository<EstabelecimentoProduto>, IEstabelecimentoProdutoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProdutoFactory>();	}
		}			
	}
	public partial class EstabelecimentoProfissionalRepository : BaseActiveRecordRepository<EstabelecimentoProfissional>, IEstabelecimentoProfissionalRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalFactory>();	}
		}			
	}
	public partial class EstabelecimentoProfissionalRedeSocialRepository : BaseActiveRecordRepository<EstabelecimentoProfissionalRedeSocial>, IEstabelecimentoProfissionalRedeSocialRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalRedeSocialFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalRedeSocialFactory>();	}
		}			
	}
	public partial class EstabelecimentoProfissionalServicoRepository : BaseActiveRecordRepository<EstabelecimentoProfissionalServico>, IEstabelecimentoProfissionalServicoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalServicoFactory>();	}
		}			
	}
	public partial class EstabelecimentoProfissionalServicoComissaoRepository : BaseActiveRecordRepository<EstabelecimentoProfissionalServicoComissao>, IEstabelecimentoProfissionalServicoComissaoRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalServicoComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalServicoComissaoFactory>();	}
		}			
	}
	public partial class EstabelecimentoSolicitacaoAparecerBuscaRepository : BaseActiveRecordRepository<EstabelecimentoSolicitacaoAparecerBusca>, IEstabelecimentoSolicitacaoAparecerBuscaRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoSolicitacaoAparecerBuscaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoSolicitacaoAparecerBuscaFactory>();	}
		}			
	}
	public partial class EstabelecimentoTemplateRepository : BaseActiveRecordRepository<EstabelecimentoTemplate>, IEstabelecimentoTemplateRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstabelecimentoTemplateFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstabelecimentoTemplateFactory>();	}
		}			
	}
	public partial class EstadoCivilRepository : BaseActiveRecordRepository<EstadoCivil>, IEstadoCivilRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstadoCivilFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstadoCivilFactory>();	}
		}			
	}
	public partial class EstatisticaExibicaoTelefoneRepository : BaseActiveRecordRepository<EstatisticaExibicaoTelefone>, IEstatisticaExibicaoTelefoneRepository {
		public Perlink.Trinks.Pessoas.Factories.IEstatisticaExibicaoTelefoneFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IEstatisticaExibicaoTelefoneFactory>();	}
		}			
	}
	public partial class FabricanteProdutoPadraoRepository : BaseActiveRecordRepository<FabricanteProdutoPadrao>, IFabricanteProdutoPadraoRepository {
		public Perlink.Trinks.Pessoas.Factories.IFabricanteProdutoPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFabricanteProdutoPadraoFactory>();	}
		}			
	}
	public partial class FormaRelacaoProfissionalRepository : BaseActiveRecordRepository<FormaRelacaoProfissional>, IFormaRelacaoProfissionalRepository {
		public Perlink.Trinks.Pessoas.Factories.IFormaRelacaoProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFormaRelacaoProfissionalFactory>();	}
		}			
	}
	public partial class FormaRelacaoProfissionalPadraoRepository : BaseActiveRecordRepository<FormaRelacaoProfissionalPadrao>, IFormaRelacaoProfissionalPadraoRepository {
		public Perlink.Trinks.Pessoas.Factories.IFormaRelacaoProfissionalPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFormaRelacaoProfissionalPadraoFactory>();	}
		}			
	}
	public partial class FornecedorRepository : BaseActiveRecordRepository<Fornecedor>, IFornecedorRepository {
		public Perlink.Trinks.Pessoas.Factories.IFornecedorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFornecedorFactory>();	}
		}			
	}
	public partial class FotoDeClienteEstabelecimentoRepository : BaseActiveRecordRepository<FotoDeClienteEstabelecimento>, IFotoDeClienteEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IFotoDeClienteEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFotoDeClienteEstabelecimentoFactory>();	}
		}			
	}
	public partial class FotoDeServicoRealizadoEmHorarioRepository : BaseActiveRecordRepository<FotoDeServicoRealizadoEmHorario>, IFotoDeServicoRealizadoEmHorarioRepository {
		public Perlink.Trinks.Pessoas.Factories.IFotoDeServicoRealizadoEmHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFotoDeServicoRealizadoEmHorarioFactory>();	}
		}			
	}
	public partial class FotoEstabelecimentoRepository : BaseActiveRecordRepository<FotoEstabelecimento>, IFotoEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IFotoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFotoEstabelecimentoFactory>();	}
		}			
	}
	public partial class FotoPessoaRepository : BaseActiveRecordRepository<FotoPessoa>, IFotoPessoaRepository {
		public Perlink.Trinks.Pessoas.Factories.IFotoPessoaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFotoPessoaFactory>();	}
		}			
	}
	public partial class FranquiaRepository : BaseActiveRecordRepository<Franquia>, IFranquiaRepository {
		public Perlink.Trinks.Pessoas.Factories.IFranquiaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFranquiaFactory>();	}
		}			
	}
	public partial class FranquiaEstabelecimentoRepository : BaseActiveRecordRepository<FranquiaEstabelecimento>, IFranquiaEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IFranquiaEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFranquiaEstabelecimentoFactory>();	}
		}			
	}
	public partial class FuncaoDoProfissionalRepository : BaseActiveRecordRepository<FuncaoDoProfissional>, IFuncaoDoProfissionalRepository {
		public Perlink.Trinks.Pessoas.Factories.IFuncaoDoProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFuncaoDoProfissionalFactory>();	}
		}			
	}
	public partial class FuncaoDoProfissionalPadraoRepository : BaseActiveRecordRepository<FuncaoDoProfissionalPadrao>, IFuncaoDoProfissionalPadraoRepository {
		public Perlink.Trinks.Pessoas.Factories.IFuncaoDoProfissionalPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IFuncaoDoProfissionalPadraoFactory>();	}
		}			
	}
	public partial class HistoricoAcaoUnidadeAoModeloRepository : BaseActiveRecordRepository<HistoricoAcaoUnidadeAoModelo>, IHistoricoAcaoUnidadeAoModeloRepository {
		public Perlink.Trinks.Pessoas.Factories.IHistoricoAcaoUnidadeAoModeloFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHistoricoAcaoUnidadeAoModeloFactory>();	}
		}			
	}
	public partial class HistoricoClienteRepository : BaseActiveRecordRepository<HistoricoCliente>, IHistoricoClienteRepository {
		public Perlink.Trinks.Pessoas.Factories.IHistoricoClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHistoricoClienteFactory>();	}
		}			
	}
	public partial class HorarioRepository : BaseActiveRecordRepository<Horario>, IHorarioRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioFactory>();	}
		}			
	}
	public partial class HorarioHistoricoRepository : BaseActiveRecordRepository<HorarioHistorico>, IHorarioHistoricoRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioHistoricoFactory>();	}
		}			
	}
	public partial class HorarioHistoricoEtiquetaRepository : BaseActiveRecordRepository<HorarioHistoricoEtiqueta>, IHorarioHistoricoEtiquetaRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioHistoricoEtiquetaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioHistoricoEtiquetaFactory>();	}
		}			
	}
	public partial class HorarioOrigemRepository : BaseActiveRecordRepository<HorarioOrigem>, IHorarioOrigemRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioOrigemFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioOrigemFactory>();	}
		}			
	}
	public partial class HorarioQuemCancelouRepository : BaseActiveRecordRepository<HorarioQuemCancelou>, IHorarioQuemCancelouRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioQuemCancelouFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioQuemCancelouFactory>();	}
		}			
	}
	public partial class HorarioTrabalhoRepository : BaseActiveRecordRepository<HorarioTrabalho>, IHorarioTrabalhoRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioTrabalhoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioTrabalhoFactory>();	}
		}			
	}
	public partial class HorarioTransacaoRepository : BaseActiveRecordRepository<HorarioTransacao>, IHorarioTransacaoRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioTransacaoFactory>();	}
		}			
	}
	public partial class HorarioVeraoCidadeRepository : BaseActiveRecordRepository<HorarioVeraoCidade>, IHorarioVeraoCidadeRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioVeraoCidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioVeraoCidadeFactory>();	}
		}			
	}
	public partial class HorarioVeraoUFRepository : BaseActiveRecordRepository<HorarioVeraoUF>, IHorarioVeraoUFRepository {
		public Perlink.Trinks.Pessoas.Factories.IHorarioVeraoUFFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHorarioVeraoUFFactory>();	}
		}			
	}
	public partial class HotsiteEstabelecimentoRepository : BaseActiveRecordRepository<HotsiteEstabelecimento>, IHotsiteEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IHotsiteEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IHotsiteEstabelecimentoFactory>();	}
		}			
	}
	public partial class ItemComboClienteRepository : BaseActiveRecordRepository<ItemComboCliente>, IItemComboClienteRepository {
		public Perlink.Trinks.Pessoas.Factories.IItemComboClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IItemComboClienteFactory>();	}
		}			
	}
	public partial class LinkDePagamentoDoHorarioRepository : BaseActiveRecordRepository<LinkDePagamentoDoHorario>, ILinkDePagamentoDoHorarioRepository {
		public Perlink.Trinks.Pessoas.Factories.ILinkDePagamentoDoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ILinkDePagamentoDoHorarioFactory>();	}
		}			
	}
	public partial class MidiaSocialRepository : BaseActiveRecordRepository<MidiaSocial>, IMidiaSocialRepository {
		public Perlink.Trinks.Pessoas.Factories.IMidiaSocialFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IMidiaSocialFactory>();	}
		}			
	}
	public partial class MotivoAusenciaRepository : BaseActiveRecordRepository<MotivoAusencia>, IMotivoAusenciaRepository {
		public Perlink.Trinks.Pessoas.Factories.IMotivoAusenciaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IMotivoAusenciaFactory>();	}
		}			
	}
	public partial class MotivoQueEscolheuOTrinksRepository : BaseActiveRecordRepository<MotivoQueEscolheuOTrinks>, IMotivoQueEscolheuOTrinksRepository {
		public Perlink.Trinks.Pessoas.Factories.IMotivoQueEscolheuOTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IMotivoQueEscolheuOTrinksFactory>();	}
		}			
	}
	public partial class MotivoQueEstabelecimentoEscolheuOTrinksRepository : BaseActiveRecordRepository<MotivoQueEstabelecimentoEscolheuOTrinks>, IMotivoQueEstabelecimentoEscolheuOTrinksRepository {
		public Perlink.Trinks.Pessoas.Factories.IMotivoQueEstabelecimentoEscolheuOTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IMotivoQueEstabelecimentoEscolheuOTrinksFactory>();	}
		}			
	}
	public partial class NotificacaoEstabelecimentoRepository : BaseActiveRecordRepository<NotificacaoEstabelecimento>, INotificacaoEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.INotificacaoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.INotificacaoEstabelecimentoFactory>();	}
		}			
	}
	public partial class NovidadeRepository : BaseActiveRecordRepository<Novidade>, INovidadeRepository {
		public Perlink.Trinks.Pessoas.Factories.INovidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.INovidadeFactory>();	}
		}			
	}
	public partial class ParametrizacaoTrinksRepository : BaseActiveRecordRepository<ParametrizacaoTrinks>, IParametrizacaoTrinksRepository {
		public Perlink.Trinks.Pessoas.Factories.IParametrizacaoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IParametrizacaoTrinksFactory>();	}
		}			
	}
	public partial class PeriodoAusenciaRepository : BaseActiveRecordRepository<PeriodoAusencia>, IPeriodoAusenciaRepository {
		public Perlink.Trinks.Pessoas.Factories.IPeriodoAusenciaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IPeriodoAusenciaFactory>();	}
		}			
	}
	public partial class PermissoesDaUnidadeBaseadaEmModeloRepository : BaseActiveRecordRepository<PermissoesDaUnidadeBaseadaEmModelo>, IPermissoesDaUnidadeBaseadaEmModeloRepository {
		public Perlink.Trinks.Pessoas.Factories.IPermissoesDaUnidadeBaseadaEmModeloFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IPermissoesDaUnidadeBaseadaEmModeloFactory>();	}
		}			
	}
	public partial class PessoaRepository : BaseActiveRecordRepository<Pessoa>, IPessoaRepository {
		public Perlink.Trinks.Pessoas.Factories.IPessoaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IPessoaFactory>();	}
		}			
	}
	public partial class PessoaFisicaRepository : BaseActiveRecordRepository<PessoaFisica>, IPessoaFisicaRepository {
		public Perlink.Trinks.Pessoas.Factories.IPessoaFisicaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IPessoaFisicaFactory>();	}
		}			
	}
	public partial class PessoaJuridicaRepository : BaseActiveRecordRepository<PessoaJuridica>, IPessoaJuridicaRepository {
		public Perlink.Trinks.Pessoas.Factories.IPessoaJuridicaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IPessoaJuridicaFactory>();	}
		}			
	}
	public partial class PessoaJuridicaConfiguracaoNFeRepository : BaseActiveRecordRepository<PessoaJuridicaConfiguracaoNFe>, IPessoaJuridicaConfiguracaoNFeRepository {
		public Perlink.Trinks.Pessoas.Factories.IPessoaJuridicaConfiguracaoNFeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IPessoaJuridicaConfiguracaoNFeFactory>();	}
		}			
	}
	public partial class ProfissionalRepository : BaseActiveRecordRepository<Profissional>, IProfissionalRepository {
		public Perlink.Trinks.Pessoas.Factories.IProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IProfissionalFactory>();	}
		}			
	}
	public partial class QuestionarioRepository : BaseActiveRecordRepository<Questionario>, IQuestionarioRepository {
		public Perlink.Trinks.Pessoas.Factories.IQuestionarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IQuestionarioFactory>();	}
		}			
	}
	public partial class RecorrenciaHorarioRepository : BaseActiveRecordRepository<RecorrenciaHorario>, IRecorrenciaHorarioRepository {
		public Perlink.Trinks.Pessoas.Factories.IRecorrenciaHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IRecorrenciaHorarioFactory>();	}
		}			
	}
	public partial class RelatorioFormaPagamentoRepository : BaseActiveRecordRepository<RelatorioFormaPagamento>, IRelatorioFormaPagamentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IRelatorioFormaPagamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IRelatorioFormaPagamentoFactory>();	}
		}			
	}
	public partial class SaldoDeSMSLembreteDoEstabelecimentoRepository : BaseActiveRecordRepository<SaldoDeSMSLembreteDoEstabelecimento>, ISaldoDeSMSLembreteDoEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.ISaldoDeSMSLembreteDoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ISaldoDeSMSLembreteDoEstabelecimentoFactory>();	}
		}			
	}
	public partial class ServicoRepository : BaseActiveRecordRepository<Servico>, IServicoRepository {
		public Perlink.Trinks.Pessoas.Factories.IServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IServicoFactory>();	}
		}			
	}
	public partial class ServicoCategoriaRepository : BaseActiveRecordRepository<ServicoCategoria>, IServicoCategoriaRepository {
		public Perlink.Trinks.Pessoas.Factories.IServicoCategoriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IServicoCategoriaFactory>();	}
		}			
	}
	public partial class ServicoCategoriaEstabelecimentoRepository : BaseActiveRecordRepository<ServicoCategoriaEstabelecimento>, IServicoCategoriaEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IServicoCategoriaEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IServicoCategoriaEstabelecimentoFactory>();	}
		}			
	}
	public partial class ServicoEstabelecimentoRepository : BaseActiveRecordRepository<ServicoEstabelecimento>, IServicoEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IServicoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IServicoEstabelecimentoFactory>();	}
		}			
	}
	public partial class ServicoSinonimoRepository : BaseActiveRecordRepository<ServicoSinonimo>, IServicoSinonimoRepository {
		public Perlink.Trinks.Pessoas.Factories.IServicoSinonimoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IServicoSinonimoFactory>();	}
		}			
	}
	public partial class SincronizacaoEntreEstabelecimentosModelosFilaRepository : BaseActiveRecordRepository<SincronizacaoEntreEstabelecimentosModelosFila>, ISincronizacaoEntreEstabelecimentosModelosFilaRepository {
		public Perlink.Trinks.Pessoas.Factories.ISincronizacaoEntreEstabelecimentosModelosFilaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ISincronizacaoEntreEstabelecimentosModelosFilaFactory>();	}
		}			
	}
	public partial class SincronizacaoEstabelecimentosFilaRepository : BaseActiveRecordRepository<SincronizacaoEstabelecimentosFila>, ISincronizacaoEstabelecimentosFilaRepository {
		public Perlink.Trinks.Pessoas.Factories.ISincronizacaoEstabelecimentosFilaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ISincronizacaoEstabelecimentosFilaFactory>();	}
		}			
	}
	public partial class StatusHorarioRepository : BaseActiveRecordRepository<StatusHorario>, IStatusHorarioRepository {
		public Perlink.Trinks.Pessoas.Factories.IStatusHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IStatusHorarioFactory>();	}
		}			
	}
	public partial class TelefoneRepository : BaseActiveRecordRepository<Telefone>, ITelefoneRepository {
		public Perlink.Trinks.Pessoas.Factories.ITelefoneFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITelefoneFactory>();	}
		}			
	}
	public partial class TelefoneInternacionalRepository : BaseActiveRecordRepository<TelefoneInternacional>, ITelefoneInternacionalRepository {
		public Perlink.Trinks.Pessoas.Factories.ITelefoneInternacionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITelefoneInternacionalFactory>();	}
		}			
	}
	public partial class TemaHotsiteRepository : BaseActiveRecordRepository<TemaHotsite>, ITemaHotsiteRepository {
		public Perlink.Trinks.Pessoas.Factories.ITemaHotsiteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITemaHotsiteFactory>();	}
		}			
	}
	public partial class TemplateRepository : BaseActiveRecordRepository<Template>, ITemplateRepository {
		public Perlink.Trinks.Pessoas.Factories.ITemplateFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITemplateFactory>();	}
		}			
	}
	public partial class TemplateHotsiteRepository : BaseActiveRecordRepository<TemplateHotsite>, ITemplateHotsiteRepository {
		public Perlink.Trinks.Pessoas.Factories.ITemplateHotsiteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITemplateHotsiteFactory>();	}
		}			
	}
	public partial class TipoComissaoRepository : BaseActiveRecordRepository<TipoComissao>, ITipoComissaoRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoComissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoComissaoFactory>();	}
		}			
	}
	public partial class TipoDescontoRepository : BaseActiveRecordRepository<TipoDesconto>, ITipoDescontoRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoDescontoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoDescontoFactory>();	}
		}			
	}
	public partial class TipoEstabelecimentoRepository : BaseActiveRecordRepository<TipoEstabelecimento>, ITipoEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoEstabelecimentoFactory>();	}
		}			
	}
	public partial class TipoFranquiaRepository : BaseActiveRecordRepository<TipoFranquia>, ITipoFranquiaRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoFranquiaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoFranquiaFactory>();	}
		}			
	}
	public partial class TipoLogradouroRepository : BaseActiveRecordRepository<TipoLogradouro>, ITipoLogradouroRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoLogradouroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoLogradouroFactory>();	}
		}			
	}
	public partial class TipoPOSRepository : BaseActiveRecordRepository<TipoPOS>, ITipoPOSRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoPOSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoPOSFactory>();	}
		}			
	}
	public partial class TipoPrecoRepository : BaseActiveRecordRepository<TipoPreco>, ITipoPrecoRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoPrecoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoPrecoFactory>();	}
		}			
	}
	public partial class TipoRecorrenciaHorarioRepository : BaseActiveRecordRepository<TipoRecorrenciaHorario>, ITipoRecorrenciaHorarioRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoRecorrenciaHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoRecorrenciaHorarioFactory>();	}
		}			
	}
	public partial class TipoServicoEstabelecimentoRepository : BaseActiveRecordRepository<TipoServicoEstabelecimento>, ITipoServicoEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoServicoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoServicoEstabelecimentoFactory>();	}
		}			
	}
	public partial class TipoTelefoneRepository : BaseActiveRecordRepository<TipoTelefone>, ITipoTelefoneRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoTelefoneFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoTelefoneFactory>();	}
		}			
	}
	public partial class TipoTemplateRepository : BaseActiveRecordRepository<TipoTemplate>, ITipoTemplateRepository {
		public Perlink.Trinks.Pessoas.Factories.ITipoTemplateFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.ITipoTemplateFactory>();	}
		}			
	}
	public partial class UFRepository : BaseActiveRecordRepository<UF>, IUFRepository {
		public Perlink.Trinks.Pessoas.Factories.IUFFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IUFFactory>();	}
		}			
	}
	public partial class UnidadeMedidaRepository : BaseActiveRecordRepository<UnidadeMedida>, IUnidadeMedidaRepository {
		public Perlink.Trinks.Pessoas.Factories.IUnidadeMedidaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IUnidadeMedidaFactory>();	}
		}			
	}
	public partial class UsuarioEstabelecimentoRepository : BaseActiveRecordRepository<UsuarioEstabelecimento>, IUsuarioEstabelecimentoRepository {
		public Perlink.Trinks.Pessoas.Factories.IUsuarioEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Pessoas.Factories.IUsuarioEstabelecimentoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Pessoas.Builders.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Configuracoes.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.DTO.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.DTO.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Helpers.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Repositories.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Statics.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Stories.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.VO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Enums.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Repositories {

	public partial class EstabelecimentoProdutoCategoriaRepository : BaseActiveRecordRepository<EstabelecimentoProdutoCategoria>, IEstabelecimentoProdutoCategoriaRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IEstabelecimentoProdutoCategoriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IEstabelecimentoProdutoCategoriaFactory>();	}
		}			
	}
	public partial class InventarioRepository : BaseActiveRecordRepository<Inventario>, IInventarioRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IInventarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IInventarioFactory>();	}
		}			
	}
	public partial class InventarioMovimentacaoEstoqueRepository : BaseActiveRecordRepository<InventarioMovimentacaoEstoque>, IInventarioMovimentacaoEstoqueRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IInventarioMovimentacaoEstoqueFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IInventarioMovimentacaoEstoqueFactory>();	}
		}			
	}
	public partial class MovimentoEstoqueTipoRepository : BaseActiveRecordRepository<MovimentoEstoqueTipo>, IMovimentoEstoqueTipoRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IMovimentoEstoqueTipoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IMovimentoEstoqueTipoFactory>();	}
		}			
	}
	public partial class OpcaoDaPropriedadeDeProdutoRepository : BaseActiveRecordRepository<OpcaoDaPropriedadeDeProduto>, IOpcaoDaPropriedadeDeProdutoRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IOpcaoDaPropriedadeDeProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IOpcaoDaPropriedadeDeProdutoFactory>();	}
		}			
	}
	public partial class ProdutoCategoriaPadraoRepository : BaseActiveRecordRepository<ProdutoCategoriaPadrao>, IProdutoCategoriaPadraoRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IProdutoCategoriaPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IProdutoCategoriaPadraoFactory>();	}
		}			
	}
	public partial class ProdutoDoInventarioRepository : BaseActiveRecordRepository<ProdutoDoInventario>, IProdutoDoInventarioRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IProdutoDoInventarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IProdutoDoInventarioFactory>();	}
		}			
	}
	public partial class ProdutoPadraoRepository : BaseActiveRecordRepository<ProdutoPadrao>, IProdutoPadraoRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IProdutoPadraoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IProdutoPadraoFactory>();	}
		}			
	}
	public partial class PropriedadeDeProdutoRepository : BaseActiveRecordRepository<PropriedadeDeProduto>, IPropriedadeDeProdutoRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IPropriedadeDeProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IPropriedadeDeProdutoFactory>();	}
		}			
	}
	public partial class ValorPropriedadeDoProdutoRepository : BaseActiveRecordRepository<ValorPropriedadeDoProduto>, IValorPropriedadeDoProdutoRepository {
		public Perlink.Trinks.ProdutoEstoque.Factories.IValorPropriedadeDoProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProdutoEstoque.Factories.IValorPropriedadeDoProdutoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ProdutoEstoque.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Factories.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Stories.Repositories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Repositories {

	public partial class DisponibilidadeNaAgendaRepository : BaseActiveRecordRepository<DisponibilidadeNaAgenda>, IDisponibilidadeNaAgendaRepository {
		public Perlink.Trinks.ProfissionalAgenda.Factories.IDisponibilidadeNaAgendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProfissionalAgenda.Factories.IDisponibilidadeNaAgendaFactory>();	}
		}			
	}
	public partial class LiberacaoDeHorarioNaAgendaRepository : BaseActiveRecordRepository<LiberacaoDeHorarioNaAgenda>, ILiberacaoDeHorarioNaAgendaRepository {
		public Perlink.Trinks.ProfissionalAgenda.Factories.ILiberacaoDeHorarioNaAgendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProfissionalAgenda.Factories.ILiberacaoDeHorarioNaAgendaFactory>();	}
		}			
	}
	public partial class MotivoDeLiberacaoRepository : BaseActiveRecordRepository<MotivoDeLiberacao>, IMotivoDeLiberacaoRepository {
		public Perlink.Trinks.ProfissionalAgenda.Factories.IMotivoDeLiberacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProfissionalAgenda.Factories.IMotivoDeLiberacaoFactory>();	}
		}			
	}
	public partial class RelatorioAusenciaELiberacaoHorarioRepository : BaseActiveRecordRepository<RelatorioAusenciaELiberacaoHorario>, IRelatorioAusenciaELiberacaoHorarioRepository {
		public Perlink.Trinks.ProfissionalAgenda.Factories.IRelatorioAusenciaELiberacaoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProfissionalAgenda.Factories.IRelatorioAusenciaELiberacaoHorarioFactory>();	}
		}			
	}
	public partial class TipoDeDisponibilidadeRepository : BaseActiveRecordRepository<TipoDeDisponibilidade>, ITipoDeDisponibilidadeRepository {
		public Perlink.Trinks.ProfissionalAgenda.Factories.ITipoDeDisponibilidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProfissionalAgenda.Factories.ITipoDeDisponibilidadeFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ProfissionalAgenda.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Enums.Repositories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Stories.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Repositories {

	public partial class CupomRepository : BaseActiveRecordRepository<Cupom>, ICupomRepository {
		public Perlink.Trinks.ProjetoBackToSalon.Factories.ICupomFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoBackToSalon.Factories.ICupomFactory>();	}
		}			
	}
	public partial class EstabelecimentoParticipanteBTSRepository : BaseActiveRecordRepository<EstabelecimentoParticipanteBTS>, IEstabelecimentoParticipanteBTSRepository {
		public Perlink.Trinks.ProjetoBackToSalon.Factories.IEstabelecimentoParticipanteBTSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoBackToSalon.Factories.IEstabelecimentoParticipanteBTSFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ProjetoBackToSalon.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Enum.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Filters.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.Repositories {

	public partial class CompraDeVoucherRepository : BaseActiveRecordRepository<CompraDeVoucher>, ICompraDeVoucherRepository {
		public Perlink.Trinks.ProjetoBelezaAmiga.Factories.ICompraDeVoucherFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoBelezaAmiga.Factories.ICompraDeVoucherFactory>();	}
		}			
	}
	public partial class EstabelecimentoParticipanteRepository : BaseActiveRecordRepository<EstabelecimentoParticipante>, IEstabelecimentoParticipanteRepository {
		public Perlink.Trinks.ProjetoBelezaAmiga.Factories.IEstabelecimentoParticipanteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoBelezaAmiga.Factories.IEstabelecimentoParticipanteFactory>();	}
		}			
	}
	public partial class VoucherRepository : BaseActiveRecordRepository<Voucher>, IVoucherRepository {
		public Perlink.Trinks.ProjetoBelezaAmiga.Factories.IVoucherFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoBelezaAmiga.Factories.IVoucherFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.Enums.Repositories {

 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.Repositories {

	public partial class EstabelecimentoParticipanteESSV2Repository : BaseActiveRecordRepository<EstabelecimentoParticipanteESSV2>, IEstabelecimentoParticipanteESSV2Repository {
		public Perlink.Trinks.ProjetoEncontreSeuSalao.Factories.IEstabelecimentoParticipanteESSV2Factory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoEncontreSeuSalao.Factories.IEstabelecimentoParticipanteESSV2Factory>();	}
		}			
	}
	public partial class GmapsLimitStatusRepository : BaseActiveRecordRepository<GmapsLimitStatus>, IGmapsLimitStatusRepository {
		public Perlink.Trinks.ProjetoEncontreSeuSalao.Factories.IGmapsLimitStatusFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ProjetoEncontreSeuSalao.Factories.IGmapsLimitStatusFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.Filters.Repositories {

 
}
namespace Perlink.Trinks.Promocoes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Promocoes.Repositories {

	public partial class PromocaoRepository : BaseActiveRecordRepository<Promocao>, IPromocaoRepository {
		public Perlink.Trinks.Promocoes.Factories.IPromocaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Promocoes.Factories.IPromocaoFactory>();	}
		}			
	}
	public partial class PromocaoDiaDaSemanaRepository : BaseActiveRecordRepository<PromocaoDiaDaSemana>, IPromocaoDiaDaSemanaRepository {
		public Perlink.Trinks.Promocoes.Factories.IPromocaoDiaDaSemanaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Promocoes.Factories.IPromocaoDiaDaSemanaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.PromocoesOnline.Repositories {

	public partial class AgendamentoTemporarioRepository : BaseActiveRecordRepository<AgendamentoTemporario>, IAgendamentoTemporarioRepository {
		public Perlink.Trinks.PromocoesOnline.Factories.IAgendamentoTemporarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromocoesOnline.Factories.IAgendamentoTemporarioFactory>();	}
		}			
	}
	public partial class HorariosDaPromocaoRepository : BaseActiveRecordRepository<HorariosDaPromocao>, IHorariosDaPromocaoRepository {
		public Perlink.Trinks.PromocoesOnline.Factories.IHorariosDaPromocaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromocoesOnline.Factories.IHorariosDaPromocaoFactory>();	}
		}			
	}
	public partial class PromocaoOnlineRepository : BaseActiveRecordRepository<PromocaoOnline>, IPromocaoOnlineRepository {
		public Perlink.Trinks.PromocoesOnline.Factories.IPromocaoOnlineFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromocoesOnline.Factories.IPromocaoOnlineFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.PromocoesOnline.DTO.Repositories {

 
}
namespace Perlink.Trinks.PromotoresDoTrinks.Repositories {

	public partial class HistoricoUsoCuponsParceriaRepository : BaseActiveRecordRepository<HistoricoUsoCuponsParceria>, IHistoricoUsoCuponsParceriaRepository {
		public Perlink.Trinks.PromotoresDoTrinks.Factories.IHistoricoUsoCuponsParceriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromotoresDoTrinks.Factories.IHistoricoUsoCuponsParceriaFactory>();	}
		}			
	}
	public partial class NovosProfissionaisPromotoresDoTrinksRepository : BaseActiveRecordRepository<NovosProfissionaisPromotoresDoTrinks>, INovosProfissionaisPromotoresDoTrinksRepository {
		public Perlink.Trinks.PromotoresDoTrinks.Factories.INovosProfissionaisPromotoresDoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromotoresDoTrinks.Factories.INovosProfissionaisPromotoresDoTrinksFactory>();	}
		}			
	}
	public partial class PromotorDoTrinksRepository : BaseActiveRecordRepository<PromotorDoTrinks>, IPromotorDoTrinksRepository {
		public Perlink.Trinks.PromotoresDoTrinks.Factories.IPromotorDoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromotoresDoTrinks.Factories.IPromotorDoTrinksFactory>();	}
		}			
	}
	public partial class SaldoPromotorRepository : BaseActiveRecordRepository<SaldoPromotor>, ISaldoPromotorRepository {
		public Perlink.Trinks.PromotoresDoTrinks.Factories.ISaldoPromotorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromotoresDoTrinks.Factories.ISaldoPromotorFactory>();	}
		}			
	}
	public partial class TransacaoPromotorRepository : BaseActiveRecordRepository<TransacaoPromotor>, ITransacaoPromotorRepository {
		public Perlink.Trinks.PromotoresDoTrinks.Factories.ITransacaoPromotorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.PromotoresDoTrinks.Factories.ITransacaoPromotorFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.PromotoresDoTrinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.PromotoresDoTrinks.Stories.Repositories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Repositories {

	public partial class AssinaturaRecorrenteRepository : BaseActiveRecordRepository<AssinaturaRecorrente>, IAssinaturaRecorrenteRepository {
		public Perlink.Trinks.RecorrenciaDeAssinatura.Factories.IAssinaturaRecorrenteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RecorrenciaDeAssinatura.Factories.IAssinaturaRecorrenteFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.DTO.Repositories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Enums.Repositories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.Repositories {

	public partial class ClienteAtendidoRepository : BaseActiveRecordRepository<ClienteAtendido>, IClienteAtendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IClienteAtendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IClienteAtendidoFactory>();	}
		}			
	}
	public partial class ConsultaRelatorioConsolidadoDiaRepository : BaseActiveRecordRepository<ConsultaRelatorioConsolidadoDia>, IConsultaRelatorioConsolidadoDiaRepository {
		public Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoDiaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoDiaFactory>();	}
		}			
	}
	public partial class ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository : BaseActiveRecordRepository<ConsultaRelatorioConsolidadoEstabelecimentoClienteMes>, IConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository {
		public Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoEstabelecimentoClienteMesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoEstabelecimentoClienteMesFactory>();	}
		}			
	}
	public partial class ConsultaRelatorioConsolidadoMesRepository : BaseActiveRecordRepository<ConsultaRelatorioConsolidadoMes>, IConsultaRelatorioConsolidadoMesRepository {
		public Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoMesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoMesFactory>();	}
		}			
	}
	public partial class RankingDeClienteRepository : BaseActiveRecordRepository<RankingDeCliente>, IRankingDeClienteRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeClienteFactory>();	}
		}			
	}
	public partial class RankingDeClienteEstendidoRepository : BaseActiveRecordRepository<RankingDeClienteEstendido>, IRankingDeClienteEstendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeClienteEstendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeClienteEstendidoFactory>();	}
		}			
	}
	public partial class RankingDePacotesRepository : BaseActiveRecordRepository<RankingDePacotes>, IRankingDePacotesRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDePacotesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDePacotesFactory>();	}
		}			
	}
	public partial class RankingDePacotesEstendidoRepository : BaseActiveRecordRepository<RankingDePacotesEstendido>, IRankingDePacotesEstendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDePacotesEstendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDePacotesEstendidoFactory>();	}
		}			
	}
	public partial class RankingDeProdutosRepository : BaseActiveRecordRepository<RankingDeProdutos>, IRankingDeProdutosRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeProdutosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeProdutosFactory>();	}
		}			
	}
	public partial class RankingDeProdutosEstendidoRepository : BaseActiveRecordRepository<RankingDeProdutosEstendido>, IRankingDeProdutosEstendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeProdutosEstendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeProdutosEstendidoFactory>();	}
		}			
	}
	public partial class RankingDeProfissionaisRepository : BaseActiveRecordRepository<RankingDeProfissionais>, IRankingDeProfissionaisRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeProfissionaisFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeProfissionaisFactory>();	}
		}			
	}
	public partial class RankingDeProfissionaisEstendidoRepository : BaseActiveRecordRepository<RankingDeProfissionaisEstendido>, IRankingDeProfissionaisEstendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeProfissionaisEstendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeProfissionaisEstendidoFactory>();	}
		}			
	}
	public partial class RankingDeServicosRepository : BaseActiveRecordRepository<RankingDeServicos>, IRankingDeServicosRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeServicosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeServicosFactory>();	}
		}			
	}
	public partial class RankingDeServicosEstendidoRepository : BaseActiveRecordRepository<RankingDeServicosEstendido>, IRankingDeServicosEstendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingDeServicosEstendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingDeServicosEstendidoFactory>();	}
		}			
	}
	public partial class RankingEstendidoEstabelecimentosRepository : BaseActiveRecordRepository<RankingEstendidoEstabelecimentos>, IRankingEstendidoEstabelecimentosRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingEstendidoEstabelecimentosFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingEstendidoEstabelecimentosFactory>();	}
		}			
	}
	public partial class RankingItensDePacotesRepository : BaseActiveRecordRepository<RankingItensDePacotes>, IRankingItensDePacotesRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingItensDePacotesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingItensDePacotesFactory>();	}
		}			
	}
	public partial class RankingItensDePacotesEstendidoRepository : BaseActiveRecordRepository<RankingItensDePacotesEstendido>, IRankingItensDePacotesEstendidoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRankingItensDePacotesEstendidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRankingItensDePacotesEstendidoFactory>();	}
		}			
	}
	public partial class RelatorioDemonstrativoDeResultadoRepository : BaseActiveRecordRepository<RelatorioDemonstrativoDeResultado>, IRelatorioDemonstrativoDeResultadoRepository {
		public Perlink.Trinks.Relatorios.Factories.IRelatorioDemonstrativoDeResultadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRelatorioDemonstrativoDeResultadoFactory>();	}
		}			
	}
	public partial class RelatorioDemonstrativoDeResultadoReceitaRepository : BaseActiveRecordRepository<RelatorioDemonstrativoDeResultadoReceita>, IRelatorioDemonstrativoDeResultadoReceitaRepository {
		public Perlink.Trinks.Relatorios.Factories.IRelatorioDemonstrativoDeResultadoReceitaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.IRelatorioDemonstrativoDeResultadoReceitaFactory>();	}
		}			
	}
	public partial class TelaRelatorioRepository : BaseActiveRecordRepository<TelaRelatorio>, ITelaRelatorioRepository {
		public Perlink.Trinks.Relatorios.Factories.ITelaRelatorioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.ITelaRelatorioFactory>();	}
		}			
	}
	public partial class TelaRelatorioCategoriaRepository : BaseActiveRecordRepository<TelaRelatorioCategoria>, ITelaRelatorioCategoriaRepository {
		public Perlink.Trinks.Relatorios.Factories.ITelaRelatorioCategoriaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Relatorios.Factories.ITelaRelatorioCategoriaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Relatorios.DTO.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.DTO.DemonstrativoResultado.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.DTO.RetornoDeClientes.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.Enums.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.MapaDeCalor.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Repositories {

	public partial class ColocacaoDoProfissionalRepository : BaseActiveRecordRepository<ColocacaoDoProfissional>, IColocacaoDoProfissionalRepository {
		public Perlink.Trinks.RodizioDeProfissionais.Factories.IColocacaoDoProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RodizioDeProfissionais.Factories.IColocacaoDoProfissionalFactory>();	}
		}			
	}
	public partial class HorarioNoRodizioRepository : BaseActiveRecordRepository<HorarioNoRodizio>, IHorarioNoRodizioRepository {
		public Perlink.Trinks.RodizioDeProfissionais.Factories.IHorarioNoRodizioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RodizioDeProfissionais.Factories.IHorarioNoRodizioFactory>();	}
		}			
	}
	public partial class MovimentacaoNoRodizioRepository : BaseActiveRecordRepository<MovimentacaoNoRodizio>, IMovimentacaoNoRodizioRepository {
		public Perlink.Trinks.RodizioDeProfissionais.Factories.IMovimentacaoNoRodizioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RodizioDeProfissionais.Factories.IMovimentacaoNoRodizioFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.RodizioDeProfissionais.DTO.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Enums.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Factories.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Stories.Repositories {

 
}
namespace Perlink.Trinks.RPS.Repositories {

	public partial class CobRpsEmissaoRepository : BaseActiveRecordRepository<CobRpsEmissao>, ICobRpsEmissaoRepository {
		public Perlink.Trinks.RPS.Factories.ICobRpsEmissaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.ICobRpsEmissaoFactory>();	}
		}			
	}
	public partial class CobRpsLoteRepository : BaseActiveRecordRepository<CobRpsLote>, ICobRpsLoteRepository {
		public Perlink.Trinks.RPS.Factories.ICobRpsLoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.ICobRpsLoteFactory>();	}
		}			
	}
	public partial class ConfiguracaoPadraoNFSRepository : BaseActiveRecordRepository<ConfiguracaoPadraoNFS>, IConfiguracaoPadraoNFSRepository {
		public Perlink.Trinks.RPS.Factories.IConfiguracaoPadraoNFSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.IConfiguracaoPadraoNFSFactory>();	}
		}			
	}
	public partial class DadosRPSTransacaoRepository : BaseActiveRecordRepository<DadosRPSTransacao>, IDadosRPSTransacaoRepository {
		public Perlink.Trinks.RPS.Factories.IDadosRPSTransacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.IDadosRPSTransacaoFactory>();	}
		}			
	}
	public partial class EmissaoRPSRepository : BaseActiveRecordRepository<EmissaoRPS>, IEmissaoRPSRepository {
		public Perlink.Trinks.RPS.Factories.IEmissaoRPSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.IEmissaoRPSFactory>();	}
		}			
	}
	public partial class LoteRPSRepository : BaseActiveRecordRepository<LoteRPS>, ILoteRPSRepository {
		public Perlink.Trinks.RPS.Factories.ILoteRPSFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.ILoteRPSFactory>();	}
		}			
	}
	public partial class MunicipioPadraoNfseRepository : BaseActiveRecordRepository<MunicipioPadraoNfse>, IMunicipioPadraoNfseRepository {
		public Perlink.Trinks.RPS.Factories.IMunicipioPadraoNfseFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.IMunicipioPadraoNfseFactory>();	}
		}			
	}
	public partial class NfseConfiguracaoMunicipioRepository : BaseActiveRecordRepository<NfseConfiguracaoMunicipio>, INfseConfiguracaoMunicipioRepository {
		public Perlink.Trinks.RPS.Factories.INfseConfiguracaoMunicipioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.RPS.Factories.INfseConfiguracaoMunicipioFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.RPS.DTO.Repositories {

 
}
namespace Perlink.Trinks.RPS.Enums.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.ABRASF.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.BHISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.DSF.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.EGOVERNE.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.GINFES.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.Ginfes_SJRP.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.GINFES3.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.LONDRINA_SIGCORP_SIGISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.NFCe.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.PAULISTANA.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SALVADOR_BA.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SIGCORP_SIGISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SIMPLISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.VVISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.WEBISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.Integracao.Repositories {

 
}
namespace Perlink.Trinks.RPS.Integracao.Schemas.Repositories {

 
}
namespace Perlink.Trinks.RPS.ManipuladoresDeStreamParaRPS.Repositories {

 
}
namespace Perlink.Trinks.Seguranca.Repositories {

	public partial class AcoesProibidasMvcRepository : BaseActiveRecordRepository<AcoesProibidasMvc>, IAcoesProibidasMvcRepository {
		public Perlink.Trinks.Seguranca.Factories.IAcoesProibidasMvcFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Seguranca.Factories.IAcoesProibidasMvcFactory>();	}
		}			
	}
	public partial class ApiClientRepository : BaseActiveRecordRepository<ApiClient>, IApiClientRepository {
		public Perlink.Trinks.Seguranca.Factories.IApiClientFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Seguranca.Factories.IApiClientFactory>();	}
		}			
	}
	public partial class ApiRefreshTokenRepository : BaseActiveRecordRepository<ApiRefreshToken>, IApiRefreshTokenRepository {
		public Perlink.Trinks.Seguranca.Factories.IApiRefreshTokenFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Seguranca.Factories.IApiRefreshTokenFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Seguranca.DTO.Repositories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories {

	public partial class CancelamentoDoPedidoRepository : BaseActiveRecordRepository<CancelamentoDoPedido>, ICancelamentoDoPedidoRepository {
		public Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.ICancelamentoDoPedidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.ICancelamentoDoPedidoFactory>();	}
		}			
	}
	public partial class EstabelecimentoMovimentacaoEstoquePedidoRepository : BaseActiveRecordRepository<EstabelecimentoMovimentacaoEstoquePedido>, IEstabelecimentoMovimentacaoEstoquePedidoRepository {
		public Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IEstabelecimentoMovimentacaoEstoquePedidoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IEstabelecimentoMovimentacaoEstoquePedidoFactory>();	}
		}			
	}
	public partial class InformacaoAlteradaRepository : BaseActiveRecordRepository<InformacaoAlterada>, IInformacaoAlteradaRepository {
		public Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IInformacaoAlteradaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IInformacaoAlteradaFactory>();	}
		}			
	}
	public partial class ItemDePedidoDeCompraRepository : BaseActiveRecordRepository<ItemDePedidoDeCompra>, IItemDePedidoDeCompraRepository {
		public Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IItemDePedidoDeCompraFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IItemDePedidoDeCompraFactory>();	}
		}			
	}
	public partial class PedidoDeCompraRepository : BaseActiveRecordRepository<PedidoDeCompra>, IPedidoDeCompraRepository {
		public Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IPedidoDeCompraFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IPedidoDeCompraFactory>();	}
		}			
	}
	public partial class RegistroDeAlteracaoRepository : BaseActiveRecordRepository<RegistroDeAlteracao>, IRegistroDeAlteracaoRepository {
		public Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IRegistroDeAlteracaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IRegistroDeAlteracaoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.DTO.Repositories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Filtros.Repositories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Enums.Repositories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Stories.Repositories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Repositories {

	public partial class SurveyAppProRepository : BaseActiveRecordRepository<SurveyAppPro>, ISurveyAppProRepository {
		public Perlink.Trinks.SurveyAppB2B.Factories.ISurveyAppProFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.SurveyAppB2B.Factories.ISurveyAppProFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.TesteAB.Repositories {

	public partial class AmostraRepository : BaseActiveRecordRepository<Amostra>, IAmostraRepository {
		public Perlink.Trinks.TesteAB.Factories.IAmostraFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TesteAB.Factories.IAmostraFactory>();	}
		}			
	}
	public partial class GrupoRepository : BaseActiveRecordRepository<Grupo>, IGrupoRepository {
		public Perlink.Trinks.TesteAB.Factories.IGrupoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TesteAB.Factories.IGrupoFactory>();	}
		}			
	}
	public partial class MetricaRepository : BaseActiveRecordRepository<Metrica>, IMetricaRepository {
		public Perlink.Trinks.TesteAB.Factories.IMetricaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TesteAB.Factories.IMetricaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.TesteAB.DTO.Repositories {

 
}
namespace Perlink.Trinks.TesteAB.Enums.Repositories {

 
}
namespace Perlink.Trinks.TestesAB.Enums.Repositories {

 
}
namespace Perlink.Trinks.TestesAB.Repositories {

	public partial class TesteABAssinaturaRepository : BaseActiveRecordRepository<TesteABAssinatura>, ITesteABAssinaturaRepository {
		public Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaFactory>();	}
		}			
	}
	public partial class TesteABAssinaturaPlanoAssinaturaRepository : BaseActiveRecordRepository<TesteABAssinaturaPlanoAssinatura>, ITesteABAssinaturaPlanoAssinaturaRepository {
		public Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaPlanoAssinaturaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaPlanoAssinaturaFactory>();	}
		}			
	}
	public partial class TesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository : BaseActiveRecordRepository<TesteABAssinaturaPlanoAssinaturaEstabelecimento>, ITesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository {
		public Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoFactory>();	}
		}			
	}
	public partial class TesteABWhyTrinksRepository : BaseActiveRecordRepository<TesteABWhyTrinks>, ITesteABWhyTrinksRepository {
		public Perlink.Trinks.TestesAB.Factories.ITesteABWhyTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TestesAB.Factories.ITesteABWhyTrinksFactory>();	}
		}			
	}
	public partial class TesteABWhyTrinksHistoricoRepository : BaseActiveRecordRepository<TesteABWhyTrinksHistorico>, ITesteABWhyTrinksHistoricoRepository {
		public Perlink.Trinks.TestesAB.Factories.ITesteABWhyTrinksHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TestesAB.Factories.ITesteABWhyTrinksHistoricoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.TrinksApps.Repositories {

	public partial class AplicativoDeAgendamentoRepository : BaseActiveRecordRepository<AplicativoDeAgendamento>, IAplicativoDeAgendamentoRepository {
		public Perlink.Trinks.TrinksApps.Factories.IAplicativoDeAgendamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksApps.Factories.IAplicativoDeAgendamentoFactory>();	}
		}			
	}
	public partial class AplicativoDeAgendamentoFuncionalidadesRepository : BaseActiveRecordRepository<AplicativoDeAgendamentoFuncionalidades>, IAplicativoDeAgendamentoFuncionalidadesRepository {
		public Perlink.Trinks.TrinksApps.Factories.IAplicativoDeAgendamentoFuncionalidadesFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksApps.Factories.IAplicativoDeAgendamentoFuncionalidadesFactory>();	}
		}			
	}
	public partial class ConfiguracoesAppProfissionalRepository : BaseActiveRecordRepository<ConfiguracoesAppProfissional>, IConfiguracoesAppProfissionalRepository {
		public Perlink.Trinks.TrinksApps.Factories.IConfiguracoesAppProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksApps.Factories.IConfiguracoesAppProfissionalFactory>();	}
		}			
	}
	public partial class DadosOnboardingOQueProcuraNoTrinksRepository : BaseActiveRecordRepository<DadosOnboardingOQueProcuraNoTrinks>, IDadosOnboardingOQueProcuraNoTrinksRepository {
		public Perlink.Trinks.TrinksApps.Factories.IDadosOnboardingOQueProcuraNoTrinksFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksApps.Factories.IDadosOnboardingOQueProcuraNoTrinksFactory>();	}
		}			
	}
	public partial class DispositivoComAplicativoRepository : BaseActiveRecordRepository<DispositivoComAplicativo>, IDispositivoComAplicativoRepository {
		public Perlink.Trinks.TrinksApps.Factories.IDispositivoComAplicativoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksApps.Factories.IDispositivoComAplicativoFactory>();	}
		}			
	}
	public partial class FuncionalidadeDoAplicativoDeAgendamentoRepository : BaseActiveRecordRepository<FuncionalidadeDoAplicativoDeAgendamento>, IFuncionalidadeDoAplicativoDeAgendamentoRepository {
		public Perlink.Trinks.TrinksApps.Factories.IFuncionalidadeDoAplicativoDeAgendamentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksApps.Factories.IFuncionalidadeDoAplicativoDeAgendamentoFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.TrinksApps.Dto.Repositories {

 
}
namespace Perlink.Trinks.TrinksApps.Enums.Repositories {

 
}
namespace Perlink.Trinks.TrinksApps.ObjectValues.Repositories {

 
}
namespace Perlink.Trinks.TrinksAtendimento.Repositories {

	public partial class AssuntoFaleConoscoTrinksProfissionalRepository : BaseActiveRecordRepository<AssuntoFaleConoscoTrinksProfissional>, IAssuntoFaleConoscoTrinksProfissionalRepository {
		public Perlink.Trinks.TrinksAtendimento.Factories.IAssuntoFaleConoscoTrinksProfissionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.TrinksAtendimento.Factories.IAssuntoFaleConoscoTrinksProfissionalFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.TrinksAtendimento.DTOs.Repositories {

 
}
namespace Perlink.Trinks.TrinksAtendimento.Stories.Repositories {

 
}
namespace Perlink.Trinks.TrinksPay.Stories.Repositories {

 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.DTO.Repositories {

 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.Repositories {

	public partial class EmailVerificadoRepository : BaseActiveRecordRepository<EmailVerificado>, IEmailVerificadoRepository {
		public Perlink.Trinks.ValidacaoDeIdentidade.Factories.IEmailVerificadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ValidacaoDeIdentidade.Factories.IEmailVerificadoFactory>();	}
		}			
	}
	public partial class TelefoneVerificadoRepository : BaseActiveRecordRepository<TelefoneVerificado>, ITelefoneVerificadoRepository {
		public Perlink.Trinks.ValidacaoDeIdentidade.Factories.ITelefoneVerificadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ValidacaoDeIdentidade.Factories.ITelefoneVerificadoFactory>();	}
		}			
	}
	public partial class VerificacaoDeContaPelaAreaPerlinkRepository : BaseActiveRecordRepository<VerificacaoDeContaPelaAreaPerlink>, IVerificacaoDeContaPelaAreaPerlinkRepository {
		public Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeContaPelaAreaPerlinkFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeContaPelaAreaPerlinkFactory>();	}
		}			
	}
	public partial class VerificacaoDeIdentidadeRepository : BaseActiveRecordRepository<VerificacaoDeIdentidade>, IVerificacaoDeIdentidadeRepository {
		public Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeIdentidadeFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeIdentidadeFactory>();	}
		}			
	}
	public partial class VerificacaoDeIdentidadeEnvioRepository : BaseActiveRecordRepository<VerificacaoDeIdentidadeEnvio>, IVerificacaoDeIdentidadeEnvioRepository {
		public Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeIdentidadeEnvioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeIdentidadeEnvioFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.Enums.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Repositories {

	public partial class ComandaRepository : BaseActiveRecordRepository<Comanda>, IComandaRepository {
		public Perlink.Trinks.Vendas.Factories.IComandaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IComandaFactory>();	}
		}			
	}
	public partial class ItemVendaRepository : BaseActiveRecordRepository<ItemVenda>, IItemVendaRepository {
		public Perlink.Trinks.Vendas.Factories.IItemVendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IItemVendaFactory>();	}
		}			
	}
	public partial class ItemVendaAssinaturaClienteRepository : BaseActiveRecordRepository<ItemVendaAssinaturaCliente>, IItemVendaAssinaturaClienteRepository {
		public Perlink.Trinks.Vendas.Factories.IItemVendaAssinaturaClienteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IItemVendaAssinaturaClienteFactory>();	}
		}			
	}
	public partial class ItemVendaPacoteRepository : BaseActiveRecordRepository<ItemVendaPacote>, IItemVendaPacoteRepository {
		public Perlink.Trinks.Vendas.Factories.IItemVendaPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IItemVendaPacoteFactory>();	}
		}			
	}
	public partial class ItemVendaProdutoRepository : BaseActiveRecordRepository<ItemVendaProduto>, IItemVendaProdutoRepository {
		public Perlink.Trinks.Vendas.Factories.IItemVendaProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IItemVendaProdutoFactory>();	}
		}			
	}
	public partial class ItemVendaValePresenteRepository : BaseActiveRecordRepository<ItemVendaValePresente>, IItemVendaValePresenteRepository {
		public Perlink.Trinks.Vendas.Factories.IItemVendaValePresenteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IItemVendaValePresenteFactory>();	}
		}			
	}
	public partial class PreVendaRepository : BaseActiveRecordRepository<PreVenda>, IPreVendaRepository {
		public Perlink.Trinks.Vendas.Factories.IPreVendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IPreVendaFactory>();	}
		}			
	}
	public partial class PreVendaProdutoRepository : BaseActiveRecordRepository<PreVendaProduto>, IPreVendaProdutoRepository {
		public Perlink.Trinks.Vendas.Factories.IPreVendaProdutoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IPreVendaProdutoFactory>();	}
		}			
	}
	public partial class PreVendaServicoRepository : BaseActiveRecordRepository<PreVendaServico>, IPreVendaServicoRepository {
		public Perlink.Trinks.Vendas.Factories.IPreVendaServicoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IPreVendaServicoFactory>();	}
		}			
	}
	public partial class PreVendaHistoricoRepository : BaseActiveRecordRepository<PreVendaHistorico>, IPreVendaHistoricoRepository {
		public Perlink.Trinks.Vendas.Factories.IPreVendaHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IPreVendaHistoricoFactory>();	}
		}			
	}
	public partial class PreVendaStatusRepository : BaseActiveRecordRepository<PreVendaStatus>, IPreVendaStatusRepository {
		public Perlink.Trinks.Vendas.Factories.IPreVendaStatusFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IPreVendaStatusFactory>();	}
		}			
	}
	public partial class ValePresenteRepository : BaseActiveRecordRepository<ValePresente>, IValePresenteRepository {
		public Perlink.Trinks.Vendas.Factories.IValePresenteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IValePresenteFactory>();	}
		}			
	}
	public partial class VendaRepository : BaseActiveRecordRepository<Venda>, IVendaRepository {
		public Perlink.Trinks.Vendas.Factories.IVendaFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.Vendas.Factories.IVendaFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.Vendas.DTO.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Repositories.Filtros.Repositories {

 
}
namespace Perlink.Trinks.VO.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Repositories {

	public partial class AllowedTestEstablishmentsRepository : BaseActiveRecordRepository<AllowedTestEstablishments>, IAllowedTestEstablishmentsRepository {
		public Perlink.Trinks.WhatsApp.Factories.IAllowedTestEstablishmentsFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IAllowedTestEstablishmentsFactory>();	}
		}			
	}
	public partial class AvaliacaoHistoricoRepository : BaseActiveRecordRepository<AvaliacaoHistorico>, IAvaliacaoHistoricoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IAvaliacaoHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IAvaliacaoHistoricoFactory>();	}
		}			
	}
	public partial class CartaoEstabelecimentoRepository : BaseActiveRecordRepository<CartaoEstabelecimento>, ICartaoEstabelecimentoRepository {
		public Perlink.Trinks.WhatsApp.Factories.ICartaoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.ICartaoEstabelecimentoFactory>();	}
		}			
	}
	public partial class CompraCreditoRepository : BaseActiveRecordRepository<CompraCredito>, ICompraCreditoRepository {
		public Perlink.Trinks.WhatsApp.Factories.ICompraCreditoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.ICompraCreditoFactory>();	}
		}			
	}
	public partial class CompraRecorrenteEstabelecimentoRepository : BaseActiveRecordRepository<CompraRecorrenteEstabelecimento>, ICompraRecorrenteEstabelecimentoRepository {
		public Perlink.Trinks.WhatsApp.Factories.ICompraRecorrenteEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.ICompraRecorrenteEstabelecimentoFactory>();	}
		}			
	}
	public partial class CompraRecorrenteEstabelecimentoHistoricoRepository : BaseActiveRecordRepository<CompraRecorrenteEstabelecimentoHistorico>, ICompraRecorrenteEstabelecimentoHistoricoRepository {
		public Perlink.Trinks.WhatsApp.Factories.ICompraRecorrenteEstabelecimentoHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.ICompraRecorrenteEstabelecimentoHistoricoFactory>();	}
		}			
	}
	public partial class EstablishmentConfigurationRepository : BaseActiveRecordRepository<EstablishmentConfiguration>, IEstablishmentConfigurationRepository {
		public Perlink.Trinks.WhatsApp.Factories.IEstablishmentConfigurationFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IEstablishmentConfigurationFactory>();	}
		}			
	}
	public partial class FranquiaValorRepository : BaseActiveRecordRepository<FranquiaValor>, IFranquiaValorRepository {
		public Perlink.Trinks.WhatsApp.Factories.IFranquiaValorFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IFranquiaValorFactory>();	}
		}			
	}
	public partial class HistoricoCompraAdicionalRepository : BaseActiveRecordRepository<HistoricoCompraAdicional>, IHistoricoCompraAdicionalRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHistoricoCompraAdicionalFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHistoricoCompraAdicionalFactory>();	}
		}			
	}
	public partial class HistoricoComunicacaoHorarioRepository : BaseActiveRecordRepository<HistoricoComunicacaoHorario>, IHistoricoComunicacaoHorarioRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHistoricoComunicacaoHorarioFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHistoricoComunicacaoHorarioFactory>();	}
		}			
	}
	public partial class HistoricoHorarioTagRepository : BaseActiveRecordRepository<HistoricoHorarioTag>, IHistoricoHorarioTagRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHistoricoHorarioTagFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHistoricoHorarioTagFactory>();	}
		}			
	}
	public partial class HistoricoSessaoRepository : BaseActiveRecordRepository<HistoricoSessao>, IHistoricoSessaoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoFactory>();	}
		}			
	}
	public partial class HistoricoSessaoProcessoRepository : BaseActiveRecordRepository<HistoricoSessaoProcesso>, IHistoricoSessaoProcessoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoProcessoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoProcessoFactory>();	}
		}			
	}
	public partial class HistoricoSessaoProcessoRegistroRepository : BaseActiveRecordRepository<HistoricoSessaoProcessoRegistro>, IHistoricoSessaoProcessoRegistroRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoProcessoRegistroFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoProcessoRegistroFactory>();	}
		}			
	}
	public partial class HorarioComunicacaoRepository : BaseActiveRecordRepository<HorarioComunicacao>, IHorarioComunicacaoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHorarioComunicacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHorarioComunicacaoFactory>();	}
		}			
	}
	public partial class HorarioTagRepository : BaseActiveRecordRepository<HorarioTag>, IHorarioTagRepository {
		public Perlink.Trinks.WhatsApp.Factories.IHorarioTagFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IHorarioTagFactory>();	}
		}			
	}
	public partial class MessageTemplateRepository : BaseActiveRecordRepository<MessageTemplate>, IMessageTemplateRepository {
		public Perlink.Trinks.WhatsApp.Factories.IMessageTemplateFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IMessageTemplateFactory>();	}
		}			
	}
	public partial class MovimentacaoSaldoRepository : BaseActiveRecordRepository<MovimentacaoSaldo>, IMovimentacaoSaldoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IMovimentacaoSaldoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IMovimentacaoSaldoFactory>();	}
		}			
	}
	public partial class OptOutRepository : BaseActiveRecordRepository<OptOut>, IOptOutRepository {
		public Perlink.Trinks.WhatsApp.Factories.IOptOutFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IOptOutFactory>();	}
		}			
	}
	public partial class OptOutHistoricoRepository : BaseActiveRecordRepository<OptOutHistorico>, IOptOutHistoricoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IOptOutHistoricoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IOptOutHistoricoFactory>();	}
		}			
	}
	public partial class PacoteCreditoRepository : BaseActiveRecordRepository<PacoteCredito>, IPacoteCreditoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IPacoteCreditoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IPacoteCreditoFactory>();	}
		}			
	}
	public partial class PacoteCreditoFormaContratacaoRepository : BaseActiveRecordRepository<PacoteCreditoFormaContratacao>, IPacoteCreditoFormaContratacaoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IPacoteCreditoFormaContratacaoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IPacoteCreditoFormaContratacaoFactory>();	}
		}			
	}
	public partial class PacotePersonalizadoRepository : BaseActiveRecordRepository<PacotePersonalizado>, IPacotePersonalizadoRepository {
		public Perlink.Trinks.WhatsApp.Factories.IPacotePersonalizadoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IPacotePersonalizadoFactory>();	}
		}			
	}
	public partial class SaldoEstabelecimentoRepository : BaseActiveRecordRepository<SaldoEstabelecimento>, ISaldoEstabelecimentoRepository {
		public Perlink.Trinks.WhatsApp.Factories.ISaldoEstabelecimentoFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.ISaldoEstabelecimentoFactory>();	}
		}			
	}
	public partial class TipoValorPacoteRepository : BaseActiveRecordRepository<TipoValorPacote>, ITipoValorPacoteRepository {
		public Perlink.Trinks.WhatsApp.Factories.ITipoValorPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.ITipoValorPacoteFactory>();	}
		}			
	}
	public partial class ValorPacoteRepository : BaseActiveRecordRepository<ValorPacote>, IValorPacoteRepository {
		public Perlink.Trinks.WhatsApp.Factories.IValorPacoteFactory Factory {
			get {  return DomainInfrastructure.Domain.Factory<Perlink.Trinks.WhatsApp.Factories.IValorPacoteFactory>();	}
		}			
	}
 
}
namespace Perlink.Trinks.WhatsApp.DTO.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Enums.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Factories.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Filters.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Strategies.Repositories {

 
}
namespace Perlink.Trinks.WhyTrinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.WhyTrinks.Repositories {

 
}
namespace Perlink.Trinks.Wrapper.Repositories {

 
}
