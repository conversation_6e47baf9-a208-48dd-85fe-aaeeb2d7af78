namespace Perlink.Trinks.Factories {

	public interface ISetupInstances {
		void SetupInstance(Autoatendimento.CheckInEstablishments instance);
		void SetupInstance(Autoatendimento.ConfigurationsEstablishment instance);
		void SetupInstance(Autoatendimento.StatusServiceCustomerEstablishment instance);
		void SetupInstance(BaseIBPT.DadosIBPT instance);
		void SetupInstance(Belezinha.BandeiraCartaoMDR instance);
		void SetupInstance(Belezinha.Credenciamento instance);
		void SetupInstance(Belezinha.CredenciamentoComStoneCode instance);
		void SetupInstance(Belezinha.EstabelecimentoTerminalPos instance);
		void SetupInstance(Belezinha.Hierarquia instance);
		void SetupInstance(Belezinha.Mcc instance);
		void SetupInstance(Belezinha.TaxaAntecipacao instance);
		void SetupInstance(Belezinha.TaxaMDR instance);
		void SetupInstance(Belezinha.TpvMensal instance);
		void SetupInstance(Belezinha.TransacaoAvulsaPOSWebhookRequest instance);
		void SetupInstance(Cashback.BonusTransacao instance);
		void SetupInstance(Cashback.CashbackComissao instance);
		void SetupInstance(Cashback.CashbackComissaoValorAReceber instance);
		void SetupInstance(Cashback.CashbackHorarioTransacao instance);
		void SetupInstance(Cashback.CashbackItemVenda instance);
		void SetupInstance(Cashback.CashbackTransacao instance);
		void SetupInstance(Cashback.EstabelecimentoDadosIntegracao instance);
		void SetupInstance(ClientesAcompanhamentos.Anotacao instance);
		void SetupInstance(ClientesAnexos.ClienteAnexo instance);
		void SetupInstance(ClientesAnexos.MeuAnexo instance);
		void SetupInstance(ClubeDeAssinaturas.AssinaturaDoCliente instance);
		void SetupInstance(ClubeDeAssinaturas.Beneficio instance);
		void SetupInstance(ClubeDeAssinaturas.BeneficioDaAssinatura instance);
		void SetupInstance(ClubeDeAssinaturas.BeneficioDoPlano instance);
		void SetupInstance(ClubeDeAssinaturas.BeneficioProduto instance);
		void SetupInstance(ClubeDeAssinaturas.BeneficioServico instance);
		void SetupInstance(ClubeDeAssinaturas.BeneficioUsado instance);
		void SetupInstance(ClubeDeAssinaturas.ContratoDeAdesao instance);
		void SetupInstance(ClubeDeAssinaturas.HistoricoDeStatusAssinaturaDoClube instance);
		void SetupInstance(ClubeDeAssinaturas.IntencaoEdicaoDoPlanoCliente instance);
		void SetupInstance(ClubeDeAssinaturas.LinkDePagamentoDaAssinatura instance);
		void SetupInstance(ClubeDeAssinaturas.LinkDePagamentoDoCancelamentoDaAssinatura instance);
		void SetupInstance(ClubeDeAssinaturas.PagamentoDeAssinatura instance);
		void SetupInstance(ClubeDeAssinaturas.PagamentoMultaDeCancelamentoDaAssinatura instance);
		void SetupInstance(ClubeDeAssinaturas.PlanoCliente instance);
		void SetupInstance(ClubeDeAssinaturas.VendaOnline instance);
		void SetupInstance(ClubeDeAssinaturas.VigenciaDeAssinatura instance);
		void SetupInstance(Cobranca.AdicionalCobrado instance);
		void SetupInstance(Cobranca.AdicionalNaAssinatura instance);
		void SetupInstance(Cobranca.AgendamentoDeMigracaoDoPlano instance);
		void SetupInstance(Cobranca.Assinatura instance);
		void SetupInstance(Cobranca.BeneficioDoPlanoAssinatura instance);
		void SetupInstance(Cobranca.BeneficioDoPlanoMeuPlano instance);
		void SetupInstance(Cobranca.ContaFinanceira instance);
		void SetupInstance(Cobranca.DadosSales instance);
		void SetupInstance(Cobranca.DescontoNaAssinatura instance);
		void SetupInstance(Cobranca.DescontoNoAdicionalDaAssinatura instance);
		void SetupInstance(Cobranca.DescontoNoPlanoDaAssinatura instance);
		void SetupInstance(Cobranca.DorDoCliente instance);
		void SetupInstance(Cobranca.DorDoClienteNaAssinatura instance);
		void SetupInstance(Cobranca.EstabelecimentoParceriasTrinks instance);
		void SetupInstance(Cobranca.ExperimentacaoEstabelecimento instance);
		void SetupInstance(Cobranca.Fatura instance);
		void SetupInstance(Cobranca.FaturaMarketing instance);
		void SetupInstance(Cobranca.FaturaTrinks instance);
		void SetupInstance(Cobranca.FaturaWhatsApp instance);
		void SetupInstance(Cobranca.FormaDeContratacaoDoAdicional instance);
		void SetupInstance(Cobranca.FormaPagamento instance);
		void SetupInstance(Cobranca.HistoricoDoAdicionalNaAssinatura instance);
		void SetupInstance(Cobranca.MotivoDoCancelamento instance);
		void SetupInstance(Cobranca.MotivoDoCancelamentoDaAssinatura instance);
		void SetupInstance(Cobranca.ObservacaoAssinatura instance);
		void SetupInstance(Cobranca.OfertaDeServicoAdicional instance);
		void SetupInstance(Cobranca.OfertaDeServicoAdicionalMeuPlano instance);
		void SetupInstance(Cobranca.OfertaDeServicoAdicionalMeuPlanoDisponibilidade instance);
		void SetupInstance(Cobranca.ParceriaTipoTrinks instance);
		void SetupInstance(Cobranca.ParceriaTrinks instance);
		void SetupInstance(Cobranca.PessoaJuridicaPagamentoExterno instance);
		void SetupInstance(Cobranca.PessoaJuridicaPagamentoExternoFatura instance);
		void SetupInstance(Cobranca.PessoaJuridicaPagamentoExternoFaturaHistorico instance);
		void SetupInstance(Cobranca.PlanoAssinatura instance);
		void SetupInstance(Cobranca.PromocaoNaAssinatura instance);
		void SetupInstance(Cobranca.PromocaoPraContaFinanceira instance);
		void SetupInstance(Cobranca.PromocaoTrinks instance);
		void SetupInstance(Cobranca.RelatorioAssinatura instance);
		void SetupInstance(Cobranca.RelatorioFaturamento instance);
		void SetupInstance(Cobranca.ResponsavelAtendimento instance);
		void SetupInstance(Cobranca.ServicoTrinks instance);
		void SetupInstance(Cobranca.SolicitacaoCancelamentoDaAssinatura instance);
		void SetupInstance(Cobranca.StatusConta instance);
		void SetupInstance(Cobranca.StatusFatura instance);
		void SetupInstance(Cobranca.TipoAssociacao instance);
		void SetupInstance(Cobranca.TipoFormaPagamento instance);
		void SetupInstance(Cobranca.TipoServico instance);
		void SetupInstance(Cobranca.ValorDeAdesaoDoAdicionalPorFaixa instance);
		void SetupInstance(Cobranca.ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura instance);
		void SetupInstance(Cobranca.ValorDoAdicionalPorFaixa instance);
		void SetupInstance(Cobranca.ValorDoAdicionalPorFaixaDaOferta instance);
		void SetupInstance(Cobranca.ValorPorFaixa instance);
		void SetupInstance(ComunidadeTrinks.ArtigoDeNovidade instance);
		void SetupInstance(ComunidadeTrinks.Sugestao instance);
		void SetupInstance(ComunidadeTrinks.TopicoDeVotacao instance);
		void SetupInstance(ComunidadeTrinks.VotacaoDeSugestao instance);
		void SetupInstance(ComunidadeTrinks.Voto instance);
		void SetupInstance(ConciliacaoBancaria.ContaFinanceiraDoEstabelecimento instance);
		void SetupInstance(ConciliacaoBancaria.ContaFinanceiraPadrao instance);
		void SetupInstance(ContaDigital.AutenticacaoContaDigital instance);
		void SetupInstance(ContaDigital.AutenticacaoContaDigitalConfirmacao instance);
		void SetupInstance(ContaDigital.AutenticacaoContaDigitalEnvio instance);
		void SetupInstance(ContaDigital.AutenticacaoIdentidadePreCadastro instance);
		void SetupInstance(ContaDigital.AutenticacaoIdentidadeUsuarioConta instance);
		void SetupInstance(ContaDigital.CategoriaPermissaoContaDigital instance);
		void SetupInstance(ContaDigital.ChavePix instance);
		void SetupInstance(ContaDigital.ChavePixContaDigital instance);
		void SetupInstance(ContaDigital.ChavePixProfissional instance);
		void SetupInstance(ContaDigital.ConfiguracoesContaDigital instance);
		void SetupInstance(ContaDigital.ContaBancariaDigital instance);
		void SetupInstance(ContaDigital.ContaEstabelecimento instance);
		void SetupInstance(ContaDigital.ContaUsuarioDigital instance);
		void SetupInstance(ContaDigital.Dono instance);
		void SetupInstance(ContaDigital.DuvidaFrequente instance);
		void SetupInstance(ContaDigital.EtapaCadastro instance);
		void SetupInstance(ContaDigital.LimiteContaDigital instance);
		void SetupInstance(ContaDigital.LimitePlano instance);
		void SetupInstance(ContaDigital.LOGAprovacaoTransferencias instance);
		void SetupInstance(ContaDigital.Operador instance);
		void SetupInstance(ContaDigital.PagamentoAgendado instance);
		void SetupInstance(ContaDigital.PagamentoAgendadoFolhaMesProfissional instance);
		void SetupInstance(ContaDigital.PermissaoContaDigital instance);
		void SetupInstance(ContaDigital.PermissaoOperador instance);
		void SetupInstance(ContaDigital.Responsavel instance);
		void SetupInstance(ContaDigital.Transferencia instance);
		void SetupInstance(ContaDigital.UsuarioContaDigital instance);
		void SetupInstance(Conteudo.ConteudoImagemLogoEstabelecimento instance);
		void SetupInstance(Conteudo.ConteudoTexto instance);
		void SetupInstance(Conteudo.MenuItem instance);
		void SetupInstance(Conteudo.MenuOpcaoAliasBusca instance);
		void SetupInstance(Conteudo.MenuOpcaoBusca instance);
		void SetupInstance(Controle.PalavraProibida instance);
		void SetupInstance(Controle.PalavraProibidaSMS instance);
		void SetupInstance(Controle.PalavraProibidaTrinks instance);
		void SetupInstance(ControleDeCTAs.CTA instance);
		void SetupInstance(ControleDeCTAs.CTAGrupo instance);
		void SetupInstance(ControleDeCTAs.CTAInformacoesAdicionaisTrinksPro instance);
		void SetupInstance(ControleDeEntradaESaida.LancamentoAporte instance);
		void SetupInstance(ControleDeEntradaESaida.LancamentoDeReceitaCategoria instance);
		void SetupInstance(ControleDeFotos.ArquivoDeImagem instance);
		void SetupInstance(ControleDeFuncionalidades.DisponibilidadeEspecifica instance);
		void SetupInstance(ControleDeFuncionalidades.DisponibilidadeGeral instance);
		void SetupInstance(ControleDeFuncionalidades.PreferenciasDaConta instance);
		void SetupInstance(ControleDeFuncionalidades.ValorDeConfiguracaoEspecifica instance);
		void SetupInstance(ControleDeFuncionalidades.ValorDeConfiguracaoGeral instance);
		void SetupInstance(ControleDeSatisfacao.AvaliacaoDeSatisfacao instance);
		void SetupInstance(ControleDeSatisfacao.AvaliacaoDeSatisfacaoRecebidaRetentativa instance);
		void SetupInstance(ControleDeSatisfacao.Contato instance);
		void SetupInstance(ControleDeSatisfacao.ContatoCelular instance);
		void SetupInstance(ControleDeSatisfacao.ItemParaAvaliar instance);
		void SetupInstance(Correios.ConsultaCep instance);
		void SetupInstance(Cupom.CupomBase instance);
		void SetupInstance(Cupom.CupomDesconto instance);
		void SetupInstance(Cupom.CupomEstabelecimento instance);
		void SetupInstance(Cupom.CupomEstabelecimentoProduto instance);
		void SetupInstance(Cupom.CupomHorarioTransacao instance);
		void SetupInstance(Cupom.CupomItemVenda instance);
		void SetupInstance(Cupom.CupomPessoaFisica instance);
		void SetupInstance(Cupom.CupomServicoEstabelecimento instance);
		void SetupInstance(Cupom.CupomUsoPessoaFisica instance);
		void SetupInstance(DebitoParcial.AbatimentoDeDivida instance);
		void SetupInstance(DebitoParcial.DividaDeixadaNoEstabelecimento instance);
		void SetupInstance(DebitoParcial.HistoricoDaDivida instance);
		void SetupInstance(DebitoParcial.PagamentoDeDividaPeloCliente instance);
		void SetupInstance(Despesas.LancamentosRecorrentesSelecionadas instance);
		void SetupInstance(Despesas.Lancamento instance);
		void SetupInstance(Despesas.LancamentoCategoria instance);
		void SetupInstance(Despesas.LancamentoCategoriaPadrao instance);
		void SetupInstance(Despesas.LancamentoGeradoPorMovimentacaoEstoque instance);
		void SetupInstance(Despesas.LancamentoGrupo instance);
		void SetupInstance(Despesas.LancamentoGrupoPadrao instance);
		void SetupInstance(Despesas.LancamentoRecorrencia instance);
		void SetupInstance(Despesas.LancamentoRecorrenciaTipo instance);
		void SetupInstance(Despesas.LancamentoStatusPagamento instance);
		void SetupInstance(Despesas.LancamentoTipo instance);
		void SetupInstance(Despesas.RenovacaoDeLancamentos instance);
		void SetupInstance(Dispositivos.TipoImpressao instance);
		void SetupInstance(DTO.HorarioFuturosExportacao instance);
		void SetupInstance(Encurtador.EncurtadorDeDados instance);
		void SetupInstance(Estabelecimentos.AvaliacaoEstabelecimento instance);
		void SetupInstance(Estabelecimentos.ConfiguracaoDeAvaliacaoDeSatisfacao instance);
		void SetupInstance(Estabelecimentos.ConfiguracaoEstabelecimento instance);
		void SetupInstance(Estabelecimentos.EstabelecimentoFavorito instance);
		void SetupInstance(Estabelecimentos.EstabelecimentoProfissionalFavorito instance);
		void SetupInstance(Estabelecimentos.EstabelecimentoUUID instance);
		void SetupInstance(Estabelecimentos.ItemConfiguradoParaSerAvaliado instance);
		void SetupInstance(Estabelecimentos.ServicoConfiguradoParaSerAvaliado instance);
		void SetupInstance(Estabelecimentos.ElasticSearch.EstabelecimentoComInformacoesConsolidadas instance);
		void SetupInstance(Estabelecimentos.ElasticSearch.InformacoesConsolidadasDaBuscaDoPortal instance);
		void SetupInstance(Estabelecimentos.ElasticSearch.OpcaoDeAutocompletarDoPortal instance);
		void SetupInstance(Estatistica.BIUsoDoSistema instance);
		void SetupInstance(EstilosVisuais.TemaCss instance);
		void SetupInstance(EstilosVisuais.TemaCssBackoffice instance);
		void SetupInstance(EstoqueComBaixaAutomatica.ConfiguracaoDoServico instance);
		void SetupInstance(EstoqueComBaixaAutomatica.ConfiguracaoParaBaixaAutomatica instance);
		void SetupInstance(EstoqueComBaixaAutomatica.HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario instance);
		void SetupInstance(EstoqueComBaixaAutomatica.HistoricoDeUsoDeProdutoNoHorario instance);
		void SetupInstance(EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomatica instance);
		void SetupInstance(EstoqueComBaixaAutomatica.UsoDeProdutoNoHorario instance);
		void SetupInstance(Extratores.Extrator instance);
		void SetupInstance(Extratores.Visao instance);
		void SetupInstance(Facebook.FBE instance);
		void SetupInstance(FAQ.Assunto instance);
		void SetupInstance(FAQ.AssuntoPerguntaResposta instance);
		void SetupInstance(FAQ.PerguntaResposta instance);
		void SetupInstance(FAQ.Tela instance);
		void SetupInstance(FAQ.TelaAssunto instance);
		void SetupInstance(FAQ.TelaPerguntaResposta instance);
		void SetupInstance(Fidelidade.AgendamentoOnlineQueGerouPontos instance);
		void SetupInstance(Fidelidade.MovimentacaoDePontos instance);
		void SetupInstance(Fidelidade.MovimentacaoDePontosAgendamentoOnline instance);
		void SetupInstance(Fidelidade.MovimentacaoDePontosAvulso instance);
		void SetupInstance(Fidelidade.MovimentacaoDePontosHorarioTransacao instance);
		void SetupInstance(Fidelidade.MovimentacaoDePontosItemVenda instance);
		void SetupInstance(Fidelidade.MovimentacaoDePontosPagamentoAntecipado instance);
		void SetupInstance(Fidelidade.MovimentacaoDeTransferenciaDePontos instance);
		void SetupInstance(Fidelidade.MovimentacaoDeUmPontoGanho instance);
		void SetupInstance(Fidelidade.PagamentoAntecipadoQueGerouPontos instance);
		void SetupInstance(Fidelidade.PontoGanho instance);
		void SetupInstance(Fidelidade.ProgramaDeFidelidade instance);
		void SetupInstance(Fidelidade.ProgramaDeFidelidadeDiaSemana instance);
		void SetupInstance(Fidelidade.TransferenciaDePontos instance);
		void SetupInstance(Financeiro.AberturaFechamentoCaixa instance);
		void SetupInstance(Financeiro.AberturaFechamentoCaixaHistorico instance);
		void SetupInstance(Financeiro.Comissao instance);
		void SetupInstance(Financeiro.ContaBancariaPessoa instance);
		void SetupInstance(Financeiro.FechamentoFolhaMes instance);
		void SetupInstance(Financeiro.FechamentoFolhaMesProfissional instance);
		void SetupInstance(Financeiro.FormaPagamento instance);
		void SetupInstance(Financeiro.FormaPagamentoTipo instance);
		void SetupInstance(Financeiro.Gorjeta instance);
		void SetupInstance(Financeiro.LancamentoDeAntecipacao instance);
		void SetupInstance(Financeiro.MotivoDesconto instance);
		void SetupInstance(Financeiro.PagamentoFolhaMesProfissional instance);
		void SetupInstance(Financeiro.Sangria instance);
		void SetupInstance(Financeiro.SangriaHistorico instance);
		void SetupInstance(Financeiro.TipoContaBancaria instance);
		void SetupInstance(Financeiro.TipoTransacao instance);
		void SetupInstance(Financeiro.Transacao instance);
		void SetupInstance(Financeiro.TransacaoFormaPagamento instance);
		void SetupInstance(Financeiro.TransacaoFormaPagamentoParcela instance);
		void SetupInstance(Financeiro.TransacaoHistorico instance);
		void SetupInstance(Financeiro.TransacaoItem instance);
		void SetupInstance(Financeiro.TransacaoLancamentoFinanceiro instance);
		void SetupInstance(Financeiro.TransacaoPOS instance);
		void SetupInstance(Financeiro.TransacaoPOSSplit instance);
		void SetupInstance(Financeiro.TransacaoPosWebhookRequest instance);
		void SetupInstance(Financeiro.ValorDeComissaoAReceber instance);
		void SetupInstance(Financeiro.HistoricoDoCaixaPorOperador instance);
		void SetupInstance(Financeiro.MovimentacaoNoCaixaPorOperador instance);
		void SetupInstance(Financeiro.MovimentacaoNoCaixaPorOperadorLancamento instance);
		void SetupInstance(Financeiro.MovimentacaoNoCaixaPorOperadorTransacao instance);
		void SetupInstance(Financeiro.RegistroDeCaixaPorOperador instance);
		void SetupInstance(Financeiro.DescontoPersonalizado instance);
		void SetupInstance(Financeiro.DescontoPersonalizadoAssistentes instance);
		void SetupInstance(Financeiro.DescontoPersonalizadoPacote instance);
		void SetupInstance(Financeiro.DescontoPersonalizadoProduto instance);
		void SetupInstance(Financeiro.DescontoPersonalizadoProfissionais instance);
		void SetupInstance(Financeiro.DescontoPersonalizadoServico instance);
		void SetupInstance(Financeiro.FolhaPagamentoCompraProduto instance);
		void SetupInstance(Financeiro.FolhaPagamentoItem instance);
		void SetupInstance(Financeiro.FolhaPagamentoItemBonificacao instance);
		void SetupInstance(Financeiro.FolhaPagamentoItemGorjeta instance);
		void SetupInstance(Financeiro.FolhaPagamentoItemSplit instance);
		void SetupInstance(Financeiro.FolhaPagamentoItemVale instance);
		void SetupInstance(Financeiro.FolhaPagamentoLancamento instance);
		void SetupInstance(Formulario.AssinaturaDigital instance);
		void SetupInstance(Formulario.ConfiguracaoDoFormulario instance);
		void SetupInstance(Formulario.FormularioDinamico instance);
		void SetupInstance(Formulario.FormularioRespondido instance);
		void SetupInstance(Formulario.OpcaoDeResposta instance);
		void SetupInstance(Formulario.PessoaPerguntada instance);
		void SetupInstance(Formulario.QuestaoDoFormulario instance);
		void SetupInstance(Formulario.RespostaDoFormulario instance);
		void SetupInstance(Formulario.SolicitacaoDeAssinatura instance);
		void SetupInstance(Formulario.TipoDeResposta instance);
		void SetupInstance(Formulario.VersaoDaQuestaoDoFormulario instance);
		void SetupInstance(Formulario.VersaoDoFormularioDinamico instance);
		void SetupInstance(Fotos.Foto instance);
		void SetupInstance(GyraMais.DadosCliente instance);
		void SetupInstance(Identity.ApiAccount instance);
		void SetupInstance(ImportacaoDeDados.SolicitacaoDeImportacao instance);
		void SetupInstance(IntegracaoComOutrosSistemas.EventoIntegracaoComOutrosSistemas instance);
		void SetupInstance(IntegracaoComOutrosSistemas.FranquiaComChaveDeIntegracao instance);
		void SetupInstance(IntegracaoComOutrosSistemas.FranquiaEstabelecimentoComChaveDeIntegracao instance);
		void SetupInstance(InternoProduto.QuestionarioProduto instance);
		void SetupInstance(InternoProduto.QuestionarioProdutoOpcaoDeResposta instance);
		void SetupInstance(InternoProduto.QuestionarioProdutoPergunta instance);
		void SetupInstance(InternoProduto.QuestionarioProdutoRespondido instance);
		void SetupInstance(InternoProduto.QuestionarioProdutoRespondidoResposta instance);
		void SetupInstance(InternoProduto.QuestionarioProdutoTipoResposta instance);
		void SetupInstance(LinksDePagamento.ItemLinkDePagamento instance);
		void SetupInstance(LinksDePagamento.LinkDePagamento instance);
		void SetupInstance(LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinks instance);
		void SetupInstance(Marcadores.CorEtiqueta instance);
		void SetupInstance(Marcadores.Etiqueta instance);
		void SetupInstance(Marcadores.ObjetoEtiquetado instance);
		void SetupInstance(Marketing.ConfiguracoesEstabelecimentoMarketing instance);
		void SetupInstance(Marketing.ConviteDeRetorno instance);
		void SetupInstance(Marketing.ConviteDeRetornoEmail instance);
		void SetupInstance(Marketing.ConviteDeRetornoParaQuemEnviar instance);
		void SetupInstance(Marketing.ConviteDeRetornoSMS instance);
		void SetupInstance(Marketing.ConviteDeRetornoWhatsApp instance);
		void SetupInstance(Marketing.MarketingCampanha instance);
		void SetupInstance(Marketing.MarketingCampanhaEmail instance);
		void SetupInstance(Marketing.MarketingCampanhaHistorico instance);
		void SetupInstance(Marketing.MarketingCampanhaPublicoAlvo instance);
		void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoServicoEstabelecimento instance);
		void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo instance);
		void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoClienteEstabelecimento instance);
		void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoProfissional instance);
		void SetupInstance(Marketing.MarketingCampanhaSMS instance);
		void SetupInstance(Marketing.MarketingCampanhaWhatsApp instance);
		void SetupInstance(Marketing.MarketingCompraCredito instance);
		void SetupInstance(Marketing.MarketingEnvio instance);
		void SetupInstance(Marketing.MarketingEnvioCliente instance);
		void SetupInstance(Marketing.MarketingEnvioClienteEmail instance);
		void SetupInstance(Marketing.MarketingEnvioClienteParametroEnvio instance);
		void SetupInstance(Marketing.MarketingEnvioClienteSMS instance);
		void SetupInstance(Marketing.MarketingEnvioClienteWhatsApp instance);
		void SetupInstance(Marketing.MarketingEnvioEmail instance);
		void SetupInstance(Marketing.MarketingEnvioSMS instance);
		void SetupInstance(Marketing.MarketingEnvioWhatsApp instance);
		void SetupInstance(Marketing.MarketingFaixaProfissionais instance);
		void SetupInstance(Marketing.MarketingPacoteCredito instance);
		void SetupInstance(MarketingInterno.DadosMarketing instance);
		void SetupInstance(MensagemEmTela.MensagemAviso instance);
		void SetupInstance(MensagemEmTela.MensagemAvisoTextoLivre instance);
		void SetupInstance(MensagemEmTela.MensagemAvisoImplementacao instance);
		void SetupInstance(MensagensEmMassa.ModeloDeMensagem instance);
		void SetupInstance(Metricas.MetricaDesativada instance);
		void SetupInstance(NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimento instance);
		void SetupInstance(NotaFiscalDoConsumidor.ConfiguracaoNFCEstado instance);
		void SetupInstance(NotaFiscalDoConsumidor.ImpressaoDeNFC instance);
		void SetupInstance(NotaFiscalDoConsumidor.NfcSituacaoTributaria instance);
		void SetupInstance(NotaFiscalDoConsumidor.NomenclaturaNCMeNBS instance);
		void SetupInstance(NotaFiscalDoConsumidor.NotaFormaPagamentoNFC instance);
		void SetupInstance(NotaFiscalDoConsumidor.NotaInutilizadaNFC instance);
		void SetupInstance(NotaFiscalDoConsumidor.NotaItensNFC instance);
		void SetupInstance(NotaFiscalDoConsumidor.NotaNFC instance);
		void SetupInstance(NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigital instance);
		void SetupInstance(NotaFiscalDoConsumidor.StatusNotaNFC instance);
		void SetupInstance(NotaFiscalDoConsumidor.TabelaIBPT instance);
		void SetupInstance(NotaFiscalDoConsumidor.TipoRegimeTributarioNFC instance);
		void SetupInstance(Notificacoes.CampanhaPush instance);
		void SetupInstance(Notificacoes.ContaQtdNotificacoesNovas instance);
		void SetupInstance(Notificacoes.Dispositivo instance);
		void SetupInstance(Notificacoes.DispositivoB2cValido instance);
		void SetupInstance(Notificacoes.InscricaoEmNotificacao instance);
		void SetupInstance(Notificacoes.NotificacaoDoTrinks instance);
		void SetupInstance(Notificacoes.NotificacaoPush instance);
		void SetupInstance(Notificacoes.Operadora instance);
		void SetupInstance(Notificacoes.OperadoraServicoSMS instance);
		void SetupInstance(Notificacoes.RegistroNotificacao instance);
		void SetupInstance(Notificacoes.ServicoSMS instance);
		void SetupInstance(Notificacoes.TipoNotificacao instance);
		void SetupInstance(NotificacoesApps.CanalDaNotificacao instance);
		void SetupInstance(NotificacoesApps.EventoDeNotificacao instance);
		void SetupInstance(NotificacoesApps.MensagemDeNotificacao instance);
		void SetupInstance(NotificacoesApps.PreferenciaDeNotificacaoDoUsuario instance);
		void SetupInstance(NotificacoesApps.TipoDeNotificacao instance);
		void SetupInstance(Onboardings.EstabelecimentoTrilha instance);
		void SetupInstance(Onboardings.QuestionarioOnboardingPorFaixa instance);
		void SetupInstance(Onboardings.RastreioDeTarefa instance);
		void SetupInstance(Onboardings.Tarefa instance);
		void SetupInstance(Onboardings.Trilha instance);
		void SetupInstance(Onboardings.TrilhaAcao instance);
		void SetupInstance(Onboardings.TrilhaAcaoGrupo instance);
		void SetupInstance(Pacotes.ConfiguracaoPacotePersonalizado instance);
		void SetupInstance(Pacotes.HistoricoConfiguracoesPacote instance);
		void SetupInstance(Pacotes.ItemPacote instance);
		void SetupInstance(Pacotes.ItemPacoteCliente instance);
		void SetupInstance(Pacotes.ItemPacoteClienteProduto instance);
		void SetupInstance(Pacotes.ItemPacoteClienteServico instance);
		void SetupInstance(Pacotes.ItemPacoteProduto instance);
		void SetupInstance(Pacotes.ItemPacoteServico instance);
		void SetupInstance(Pacotes.LinkDePagamentoDoPacote instance);
		void SetupInstance(Pacotes.Pacote instance);
		void SetupInstance(Pacotes.PacoteCliente instance);
		void SetupInstance(Pacotes.PacoteClienteHistorico instance);
		void SetupInstance(PagamentoAntecipadoHotsite.PagamentoAntecipadoHotsiteConfiguracoes instance);
		void SetupInstance(PagamentoAntecipadoHotsite.PagamentoAntecipadoHotsiteServicos instance);
		void SetupInstance(Pagamentos.CartaoDeComprador instance);
		void SetupInstance(Pagamentos.CartaoDeCompradorGateway instance);
		void SetupInstance(Pagamentos.Comprador instance);
		void SetupInstance(Pagamentos.CompradorGateway instance);
		void SetupInstance(Pagamentos.ContaBancaria instance);
		void SetupInstance(Pagamentos.ContaBancariaGateway instance);
		void SetupInstance(Pagamentos.DocumentoDeRecebedorCredenciado instance);
		void SetupInstance(Pagamentos.EnderecoDeCobranca instance);
		void SetupInstance(Pagamentos.EtapaCadastroPagarme instance);
		void SetupInstance(Pagamentos.Gateway instance);
		void SetupInstance(Pagamentos.ItemPagamento instance);
		void SetupInstance(Pagamentos.LimitePagamento instance);
		void SetupInstance(Pagamentos.LimitePagamentoRecebedor instance);
		void SetupInstance(Pagamentos.Pagamento instance);
		void SetupInstance(Pagamentos.Recebedor instance);
		void SetupInstance(Pagamentos.RecebedorCredenciado instance);
		void SetupInstance(Pagamentos.SplitPagamento instance);
		void SetupInstance(Pagamentos.UsoLimiteAntecipacaoDiarioRecebedor instance);
		void SetupInstance(Pagamentos.UsoLimiteAntecipacaoMensalRecebedor instance);
		void SetupInstance(Pagamentos.UsoLimitePagamentoDiarioRecebedor instance);
		void SetupInstance(Pagamentos.UsoLimitePagamentoMensalRecebedor instance);
		void SetupInstance(PagamentosAntecipados.BeneficiosEstabelecimento instance);
		void SetupInstance(PagamentosAntecipados.BeneficiosPagamento instance);
		void SetupInstance(PagamentosAntecipados.ItemPagamentoAntecipado instance);
		void SetupInstance(PagamentosAntecipados.ItemPagamentoHorario instance);
		void SetupInstance(PagamentosAntecipados.PagamentoAntecipado instance);
		void SetupInstance(PagamentosAntecipados.ServicoHabilitado instance);
		void SetupInstance(PagamentosOnlineNoTrinks.ConfiguracaoAdiantamentoFuncionalidadeAntecipacao instance);
		void SetupInstance(PagamentosOnlineNoTrinks.ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline instance);
		void SetupInstance(PagamentosOnlineNoTrinks.EstabelecimentoRecebedor instance);
		void SetupInstance(PagamentosOnlineNoTrinks.InstituicaoBancaria instance);
		void SetupInstance(PagamentosOnlineNoTrinks.LOGCancelamentoAntecipacao instance);
		void SetupInstance(PagamentosOnlineNoTrinks.LOGSolicitacaoAntecipacao instance);
		void SetupInstance(PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinks instance);
		void SetupInstance(PagamentosOnlineNoTrinks.TaxasDoEstabelecimento instance);
		void SetupInstance(PagamentosOnlineNoTrinks.TaxasDoPagamentoOnlineNoTrinks instance);
		void SetupInstance(PagamentosOnlineNoTrinks.TaxasPadrao instance);
		void SetupInstance(PagamentosOnlineNoTrinks.TaxasPadraoEstabelecimentoRecebedor instance);
		void SetupInstance(Permissoes.AreaPerlink.AreaPerlinkPerfil instance);
		void SetupInstance(Permissoes.AreaPerlink.AreaPerlinkPerfilPermissao instance);
		void SetupInstance(Permissoes.AreaPerlink.ContaPerfil instance);
		void SetupInstance(Permissoes.AreaPerlink.ContaPerfilPermissao instance);
		void SetupInstance(Permissoes.CategoriaPermissao instance);
		void SetupInstance(Permissoes.DescricaoPermissao instance);
		void SetupInstance(Permissoes.FranquiaPermissao instance);
		void SetupInstance(Permissoes.Perfil instance);
		void SetupInstance(Permissoes.PerfilPermissao instance);
		void SetupInstance(Permissoes.PermissaoArea instance);
		void SetupInstance(Permissoes.UsuarioPerfil instance);
		void SetupInstance(Permissoes.UsuarioPerfilPermissao instance);
		void SetupInstance(Pessoas.Bairro instance);
		void SetupInstance(Pessoas.CacheLocalidade instance);
		void SetupInstance(Pessoas.CategoriaPortalServico instance);
		void SetupInstance(Pessoas.Cidade instance);
		void SetupInstance(Pessoas.Cliente instance);
		void SetupInstance(Pessoas.ClienteAreaPerlink instance);
		void SetupInstance(Pessoas.ClienteEstabelecimento instance);
		void SetupInstance(Pessoas.ClienteEstabelecimentoSaldos instance);
		void SetupInstance(Pessoas.ComoConheceu instance);
		void SetupInstance(Pessoas.ComoEstabelecimentoConheceuOTrinks instance);
		void SetupInstance(Pessoas.CompartilhamentoNaRede instance);
		void SetupInstance(Pessoas.ConfiguracaoHotsiteAderencia instance);
		void SetupInstance(Pessoas.ConfiguracaoHotsiteInicioMarcos instance);
		void SetupInstance(Pessoas.ConfiguracaoHotsiteIntervalo instance);
		void SetupInstance(Pessoas.ConfiguracaoHotsiteUniverso instance);
		void SetupInstance(Pessoas.Conta instance);
		void SetupInstance(Pessoas.ContaFranquia instance);
		void SetupInstance(Pessoas.DadosParaRecalculoComissao instance);
		void SetupInstance(Pessoas.DataEspecial instance);
		void SetupInstance(Pessoas.DiaSemana instance);
		void SetupInstance(Pessoas.EmailRejeitadoAmazon instance);
		void SetupInstance(Pessoas.Endereco instance);
		void SetupInstance(Pessoas.EnderecoPreenchidoManualmente instance);
		void SetupInstance(Pessoas.Estabelecimento instance);
		void SetupInstance(Pessoas.EstabelecimentoAssistenteServico instance);
		void SetupInstance(Pessoas.EstabelecimentoAssistenteServicoComissao instance);
		void SetupInstance(Pessoas.EstabelecimentoAtendeCrianca instance);
		void SetupInstance(Pessoas.EstabelecimentoConfiguracaoComissao instance);
		void SetupInstance(Pessoas.EstabelecimentoConfiguracaoGeral instance);
		void SetupInstance(Pessoas.EstabelecimentoConfiguracaoPOS instance);
		void SetupInstance(Pessoas.EstabelecimentoDadosGerais instance);
		void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastro instance);
		void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastroCargo instance);
		void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastroMotivosCadastro instance);
		void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastroSegmentos instance);
		void SetupInstance(Pessoas.EstabelecimentoFabricanteProduto instance);
		void SetupInstance(Pessoas.EstabelecimentoFormaPagamento instance);
		void SetupInstance(Pessoas.EstabelecimentoFormaPagamentoParcela instance);
		void SetupInstance(Pessoas.EstabelecimentoFornecedor instance);
		void SetupInstance(Pessoas.EstabelecimentoHorarioEspecialFuncionamento instance);
		void SetupInstance(Pessoas.EstabelecimentoHorarioEspecialFuncionamentoTipo instance);
		void SetupInstance(Pessoas.EstabelecimentoHorarioFuncionamento instance);
		void SetupInstance(Pessoas.EstabelecimentoIndicado instance);
		void SetupInstance(Pessoas.EstabelecimentoMovimentacaoEstoque instance);
		void SetupInstance(Pessoas.EstabelecimentoPossuiEstacionamento instance);
		void SetupInstance(Pessoas.EstabelecimentoPreCadastro instance);
		void SetupInstance(Pessoas.EstabelecimentoProduto instance);
		void SetupInstance(Pessoas.EstabelecimentoProfissional instance);
		void SetupInstance(Pessoas.EstabelecimentoProfissionalRedeSocial instance);
		void SetupInstance(Pessoas.EstabelecimentoProfissionalServico instance);
		void SetupInstance(Pessoas.EstabelecimentoProfissionalServicoComissao instance);
		void SetupInstance(Pessoas.EstabelecimentoSolicitacaoAparecerBusca instance);
		void SetupInstance(Pessoas.EstabelecimentoTemplate instance);
		void SetupInstance(Pessoas.EstadoCivil instance);
		void SetupInstance(Pessoas.EstatisticaExibicaoTelefone instance);
		void SetupInstance(Pessoas.FabricanteProdutoPadrao instance);
		void SetupInstance(Pessoas.FormaRelacaoProfissional instance);
		void SetupInstance(Pessoas.FormaRelacaoProfissionalPadrao instance);
		void SetupInstance(Pessoas.Fornecedor instance);
		void SetupInstance(Pessoas.FotoDeClienteEstabelecimento instance);
		void SetupInstance(Pessoas.FotoDeServicoRealizadoEmHorario instance);
		void SetupInstance(Pessoas.FotoEstabelecimento instance);
		void SetupInstance(Pessoas.FotoPessoa instance);
		void SetupInstance(Pessoas.Franquia instance);
		void SetupInstance(Pessoas.FranquiaEstabelecimento instance);
		void SetupInstance(Pessoas.FuncaoDoProfissional instance);
		void SetupInstance(Pessoas.FuncaoDoProfissionalPadrao instance);
		void SetupInstance(Pessoas.HistoricoAcaoUnidadeAoModelo instance);
		void SetupInstance(Pessoas.HistoricoCliente instance);
		void SetupInstance(Pessoas.Horario instance);
		void SetupInstance(Pessoas.HorarioHistorico instance);
		void SetupInstance(Pessoas.HorarioHistoricoEtiqueta instance);
		void SetupInstance(Pessoas.HorarioOrigem instance);
		void SetupInstance(Pessoas.HorarioQuemCancelou instance);
		void SetupInstance(Pessoas.HorarioTrabalho instance);
		void SetupInstance(Pessoas.HorarioTransacao instance);
		void SetupInstance(Pessoas.HorarioVeraoCidade instance);
		void SetupInstance(Pessoas.HorarioVeraoUF instance);
		void SetupInstance(Pessoas.HotsiteEstabelecimento instance);
		void SetupInstance(Pessoas.ItemComboCliente instance);
		void SetupInstance(Pessoas.LinkDePagamentoDoHorario instance);
		void SetupInstance(Pessoas.MidiaSocial instance);
		void SetupInstance(Pessoas.MotivoAusencia instance);
		void SetupInstance(Pessoas.MotivoQueEscolheuOTrinks instance);
		void SetupInstance(Pessoas.MotivoQueEstabelecimentoEscolheuOTrinks instance);
		void SetupInstance(Pessoas.NotificacaoEstabelecimento instance);
		void SetupInstance(Pessoas.Novidade instance);
		void SetupInstance(Pessoas.ParametrizacaoTrinks instance);
		void SetupInstance(Pessoas.PeriodoAusencia instance);
		void SetupInstance(Pessoas.PermissoesDaUnidadeBaseadaEmModelo instance);
		void SetupInstance(Pessoas.Pessoa instance);
		void SetupInstance(Pessoas.PessoaFisica instance);
		void SetupInstance(Pessoas.PessoaJuridica instance);
		void SetupInstance(Pessoas.PessoaJuridicaConfiguracaoNFe instance);
		void SetupInstance(Pessoas.Profissional instance);
		void SetupInstance(Pessoas.Questionario instance);
		void SetupInstance(Pessoas.RecorrenciaHorario instance);
		void SetupInstance(Pessoas.RelatorioFormaPagamento instance);
		void SetupInstance(Pessoas.SaldoDeSMSLembreteDoEstabelecimento instance);
		void SetupInstance(Pessoas.Servico instance);
		void SetupInstance(Pessoas.ServicoCategoria instance);
		void SetupInstance(Pessoas.ServicoCategoriaEstabelecimento instance);
		void SetupInstance(Pessoas.ServicoEstabelecimento instance);
		void SetupInstance(Pessoas.ServicoSinonimo instance);
		void SetupInstance(Pessoas.SincronizacaoEntreEstabelecimentosModelosFila instance);
		void SetupInstance(Pessoas.SincronizacaoEstabelecimentosFila instance);
		void SetupInstance(Pessoas.StatusHorario instance);
		void SetupInstance(Pessoas.Telefone instance);
		void SetupInstance(Pessoas.TelefoneInternacional instance);
		void SetupInstance(Pessoas.TemaHotsite instance);
		void SetupInstance(Pessoas.Template instance);
		void SetupInstance(Pessoas.TemplateHotsite instance);
		void SetupInstance(Pessoas.TipoComissao instance);
		void SetupInstance(Pessoas.TipoDesconto instance);
		void SetupInstance(Pessoas.TipoEstabelecimento instance);
		void SetupInstance(Pessoas.TipoFranquia instance);
		void SetupInstance(Pessoas.TipoLogradouro instance);
		void SetupInstance(Pessoas.TipoPOS instance);
		void SetupInstance(Pessoas.TipoPreco instance);
		void SetupInstance(Pessoas.TipoRecorrenciaHorario instance);
		void SetupInstance(Pessoas.TipoServicoEstabelecimento instance);
		void SetupInstance(Pessoas.TipoTelefone instance);
		void SetupInstance(Pessoas.TipoTemplate instance);
		void SetupInstance(Pessoas.UF instance);
		void SetupInstance(Pessoas.UnidadeMedida instance);
		void SetupInstance(Pessoas.UsuarioEstabelecimento instance);
		void SetupInstance(ProdutoEstoque.EstabelecimentoProdutoCategoria instance);
		void SetupInstance(ProdutoEstoque.Inventario instance);
		void SetupInstance(ProdutoEstoque.InventarioMovimentacaoEstoque instance);
		void SetupInstance(ProdutoEstoque.MovimentoEstoqueTipo instance);
		void SetupInstance(ProdutoEstoque.OpcaoDaPropriedadeDeProduto instance);
		void SetupInstance(ProdutoEstoque.ProdutoCategoriaPadrao instance);
		void SetupInstance(ProdutoEstoque.ProdutoDoInventario instance);
		void SetupInstance(ProdutoEstoque.ProdutoPadrao instance);
		void SetupInstance(ProdutoEstoque.PropriedadeDeProduto instance);
		void SetupInstance(ProdutoEstoque.ValorPropriedadeDoProduto instance);
		void SetupInstance(ProfissionalAgenda.DisponibilidadeNaAgenda instance);
		void SetupInstance(ProfissionalAgenda.LiberacaoDeHorarioNaAgenda instance);
		void SetupInstance(ProfissionalAgenda.MotivoDeLiberacao instance);
		void SetupInstance(ProfissionalAgenda.RelatorioAusenciaELiberacaoHorario instance);
		void SetupInstance(ProfissionalAgenda.TipoDeDisponibilidade instance);
		void SetupInstance(ProjetoBackToSalon.Cupom instance);
		void SetupInstance(ProjetoBackToSalon.EstabelecimentoParticipanteBTS instance);
		void SetupInstance(ProjetoBelezaAmiga.CompraDeVoucher instance);
		void SetupInstance(ProjetoBelezaAmiga.EstabelecimentoParticipante instance);
		void SetupInstance(ProjetoBelezaAmiga.Voucher instance);
		void SetupInstance(ProjetoEncontreSeuSalao.EstabelecimentoParticipanteESSV2 instance);
		void SetupInstance(ProjetoEncontreSeuSalao.GmapsLimitStatus instance);
		void SetupInstance(Promocoes.Promocao instance);
		void SetupInstance(Promocoes.PromocaoDiaDaSemana instance);
		void SetupInstance(PromocoesOnline.AgendamentoTemporario instance);
		void SetupInstance(PromocoesOnline.HorariosDaPromocao instance);
		void SetupInstance(PromocoesOnline.PromocaoOnline instance);
		void SetupInstance(PromotoresDoTrinks.HistoricoUsoCuponsParceria instance);
		void SetupInstance(PromotoresDoTrinks.NovosProfissionaisPromotoresDoTrinks instance);
		void SetupInstance(PromotoresDoTrinks.PromotorDoTrinks instance);
		void SetupInstance(PromotoresDoTrinks.SaldoPromotor instance);
		void SetupInstance(PromotoresDoTrinks.TransacaoPromotor instance);
		void SetupInstance(RecorrenciaDeAssinatura.AssinaturaRecorrente instance);
		void SetupInstance(Relatorios.ClienteAtendido instance);
		void SetupInstance(Relatorios.ConsultaRelatorioConsolidadoDia instance);
		void SetupInstance(Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMes instance);
		void SetupInstance(Relatorios.ConsultaRelatorioConsolidadoMes instance);
		void SetupInstance(Relatorios.RankingDeCliente instance);
		void SetupInstance(Relatorios.RankingDeClienteEstendido instance);
		void SetupInstance(Relatorios.RankingDePacotes instance);
		void SetupInstance(Relatorios.RankingDePacotesEstendido instance);
		void SetupInstance(Relatorios.RankingDeProdutos instance);
		void SetupInstance(Relatorios.RankingDeProdutosEstendido instance);
		void SetupInstance(Relatorios.RankingDeProfissionais instance);
		void SetupInstance(Relatorios.RankingDeProfissionaisEstendido instance);
		void SetupInstance(Relatorios.RankingDeServicos instance);
		void SetupInstance(Relatorios.RankingDeServicosEstendido instance);
		void SetupInstance(Relatorios.RankingEstendidoEstabelecimentos instance);
		void SetupInstance(Relatorios.RankingItensDePacotes instance);
		void SetupInstance(Relatorios.RankingItensDePacotesEstendido instance);
		void SetupInstance(Relatorios.RelatorioDemonstrativoDeResultado instance);
		void SetupInstance(Relatorios.RelatorioDemonstrativoDeResultadoReceita instance);
		void SetupInstance(Relatorios.TelaRelatorio instance);
		void SetupInstance(Relatorios.TelaRelatorioCategoria instance);
		void SetupInstance(RodizioDeProfissionais.ColocacaoDoProfissional instance);
		void SetupInstance(RodizioDeProfissionais.HorarioNoRodizio instance);
		void SetupInstance(RodizioDeProfissionais.MovimentacaoNoRodizio instance);
		void SetupInstance(RPS.CobRpsEmissao instance);
		void SetupInstance(RPS.CobRpsLote instance);
		void SetupInstance(RPS.ConfiguracaoPadraoNFS instance);
		void SetupInstance(RPS.DadosRPSTransacao instance);
		void SetupInstance(RPS.EmissaoRPS instance);
		void SetupInstance(RPS.LoteRPS instance);
		void SetupInstance(RPS.MunicipioPadraoNfse instance);
		void SetupInstance(RPS.NfseConfiguracaoMunicipio instance);
		void SetupInstance(Seguranca.AcoesProibidasMvc instance);
		void SetupInstance(Seguranca.ApiClient instance);
		void SetupInstance(Seguranca.ApiRefreshToken instance);
		void SetupInstance(SugestoesEPedidosDeCompra.CancelamentoDoPedido instance);
		void SetupInstance(SugestoesEPedidosDeCompra.EstabelecimentoMovimentacaoEstoquePedido instance);
		void SetupInstance(SugestoesEPedidosDeCompra.InformacaoAlterada instance);
		void SetupInstance(SugestoesEPedidosDeCompra.ItemDePedidoDeCompra instance);
		void SetupInstance(SugestoesEPedidosDeCompra.PedidoDeCompra instance);
		void SetupInstance(SugestoesEPedidosDeCompra.RegistroDeAlteracao instance);
		void SetupInstance(SurveyAppB2B.SurveyAppPro instance);
		void SetupInstance(TesteAB.Amostra instance);
		void SetupInstance(TesteAB.Grupo instance);
		void SetupInstance(TesteAB.Metrica instance);
		void SetupInstance(TestesAB.TesteABAssinatura instance);
		void SetupInstance(TestesAB.TesteABAssinaturaPlanoAssinatura instance);
		void SetupInstance(TestesAB.TesteABAssinaturaPlanoAssinaturaEstabelecimento instance);
		void SetupInstance(TestesAB.TesteABWhyTrinks instance);
		void SetupInstance(TestesAB.TesteABWhyTrinksHistorico instance);
		void SetupInstance(TrinksApps.AplicativoDeAgendamento instance);
		void SetupInstance(TrinksApps.AplicativoDeAgendamentoFuncionalidades instance);
		void SetupInstance(TrinksApps.ConfiguracoesAppProfissional instance);
		void SetupInstance(TrinksApps.DadosOnboardingOQueProcuraNoTrinks instance);
		void SetupInstance(TrinksApps.DispositivoComAplicativo instance);
		void SetupInstance(TrinksApps.FuncionalidadeDoAplicativoDeAgendamento instance);
		void SetupInstance(TrinksAtendimento.AssuntoFaleConoscoTrinksProfissional instance);
		void SetupInstance(ValidacaoDeIdentidade.EmailVerificado instance);
		void SetupInstance(ValidacaoDeIdentidade.TelefoneVerificado instance);
		void SetupInstance(ValidacaoDeIdentidade.VerificacaoDeContaPelaAreaPerlink instance);
		void SetupInstance(ValidacaoDeIdentidade.VerificacaoDeIdentidade instance);
		void SetupInstance(ValidacaoDeIdentidade.VerificacaoDeIdentidadeEnvio instance);
		void SetupInstance(Vendas.Comanda instance);
		void SetupInstance(Vendas.ItemVenda instance);
		void SetupInstance(Vendas.ItemVendaAssinaturaCliente instance);
		void SetupInstance(Vendas.ItemVendaPacote instance);
		void SetupInstance(Vendas.ItemVendaProduto instance);
		void SetupInstance(Vendas.ItemVendaValePresente instance);
		void SetupInstance(Vendas.PreVenda instance);
		void SetupInstance(Vendas.PreVendaProduto instance);
		void SetupInstance(Vendas.PreVendaServico instance);
		void SetupInstance(Vendas.PreVendaHistorico instance);
		void SetupInstance(Vendas.PreVendaStatus instance);
		void SetupInstance(Vendas.ValePresente instance);
		void SetupInstance(Vendas.Venda instance);
		void SetupInstance(WhatsApp.AllowedTestEstablishments instance);
		void SetupInstance(WhatsApp.AvaliacaoHistorico instance);
		void SetupInstance(WhatsApp.CartaoEstabelecimento instance);
		void SetupInstance(WhatsApp.CompraCredito instance);
		void SetupInstance(WhatsApp.CompraRecorrenteEstabelecimento instance);
		void SetupInstance(WhatsApp.CompraRecorrenteEstabelecimentoHistorico instance);
		void SetupInstance(WhatsApp.EstablishmentConfiguration instance);
		void SetupInstance(WhatsApp.FranquiaValor instance);
		void SetupInstance(WhatsApp.HistoricoCompraAdicional instance);
		void SetupInstance(WhatsApp.HistoricoComunicacaoHorario instance);
		void SetupInstance(WhatsApp.HistoricoHorarioTag instance);
		void SetupInstance(WhatsApp.HistoricoSessao instance);
		void SetupInstance(WhatsApp.HistoricoSessaoProcesso instance);
		void SetupInstance(WhatsApp.HistoricoSessaoProcessoRegistro instance);
		void SetupInstance(WhatsApp.HorarioComunicacao instance);
		void SetupInstance(WhatsApp.HorarioTag instance);
		void SetupInstance(WhatsApp.MessageTemplate instance);
		void SetupInstance(WhatsApp.MovimentacaoSaldo instance);
		void SetupInstance(WhatsApp.OptOut instance);
		void SetupInstance(WhatsApp.OptOutHistorico instance);
		void SetupInstance(WhatsApp.PacoteCredito instance);
		void SetupInstance(WhatsApp.PacoteCreditoFormaContratacao instance);
		void SetupInstance(WhatsApp.PacotePersonalizado instance);
		void SetupInstance(WhatsApp.SaldoEstabelecimento instance);
		void SetupInstance(WhatsApp.TipoValorPacote instance);
		void SetupInstance(WhatsApp.ValorPacote instance);
 
	}
}
