namespace Perlink.Trinks.Factories {

	public class BaseSetupInstances: ISetupInstances {
		public virtual void SetupInstance(Autoatendimento.CheckInEstablishments instance) {}
		public virtual void SetupInstance(Autoatendimento.ConfigurationsEstablishment instance) {}
		public virtual void SetupInstance(Autoatendimento.StatusServiceCustomerEstablishment instance) {}
		public virtual void SetupInstance(BaseIBPT.DadosIBPT instance) {}
		public virtual void SetupInstance(Belezinha.BandeiraCartaoMDR instance) {}
		public virtual void SetupInstance(Belezinha.Credenciamento instance) {}
		public virtual void SetupInstance(Belezinha.CredenciamentoComStoneCode instance) {}
		public virtual void SetupInstance(Belezinha.EstabelecimentoTerminalPos instance) {}
		public virtual void SetupInstance(Belezinha.Hierarquia instance) {}
		public virtual void SetupInstance(Belezinha.Mcc instance) {}
		public virtual void SetupInstance(Belezinha.TaxaAntecipacao instance) {}
		public virtual void SetupInstance(Belezinha.TaxaMDR instance) {}
		public virtual void SetupInstance(Belezinha.TpvMensal instance) {}
		public virtual void SetupInstance(Belezinha.TransacaoAvulsaPOSWebhookRequest instance) {}
		public virtual void SetupInstance(Cashback.BonusTransacao instance) {}
		public virtual void SetupInstance(Cashback.CashbackComissao instance) {}
		public virtual void SetupInstance(Cashback.CashbackComissaoValorAReceber instance) {}
		public virtual void SetupInstance(Cashback.CashbackHorarioTransacao instance) {}
		public virtual void SetupInstance(Cashback.CashbackItemVenda instance) {}
		public virtual void SetupInstance(Cashback.CashbackTransacao instance) {}
		public virtual void SetupInstance(Cashback.EstabelecimentoDadosIntegracao instance) {}
		public virtual void SetupInstance(ClientesAcompanhamentos.Anotacao instance) {}
		public virtual void SetupInstance(ClientesAnexos.ClienteAnexo instance) {}
		public virtual void SetupInstance(ClientesAnexos.MeuAnexo instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.AssinaturaDoCliente instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.Beneficio instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.BeneficioDaAssinatura instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.BeneficioDoPlano instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.BeneficioProduto instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.BeneficioServico instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.BeneficioUsado instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.ContratoDeAdesao instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.HistoricoDeStatusAssinaturaDoClube instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.IntencaoEdicaoDoPlanoCliente instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.LinkDePagamentoDaAssinatura instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.LinkDePagamentoDoCancelamentoDaAssinatura instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.PagamentoDeAssinatura instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.PagamentoMultaDeCancelamentoDaAssinatura instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.PlanoCliente instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.VendaOnline instance) {}
		public virtual void SetupInstance(ClubeDeAssinaturas.VigenciaDeAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.AdicionalCobrado instance) {}
		public virtual void SetupInstance(Cobranca.AdicionalNaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.AgendamentoDeMigracaoDoPlano instance) {}
		public virtual void SetupInstance(Cobranca.Assinatura instance) {}
		public virtual void SetupInstance(Cobranca.BeneficioDoPlanoAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.BeneficioDoPlanoMeuPlano instance) {}
		public virtual void SetupInstance(Cobranca.ContaFinanceira instance) {}
		public virtual void SetupInstance(Cobranca.DadosSales instance) {}
		public virtual void SetupInstance(Cobranca.DescontoNaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.DescontoNoAdicionalDaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.DescontoNoPlanoDaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.DorDoCliente instance) {}
		public virtual void SetupInstance(Cobranca.DorDoClienteNaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.EstabelecimentoParceriasTrinks instance) {}
		public virtual void SetupInstance(Cobranca.ExperimentacaoEstabelecimento instance) {}
		public virtual void SetupInstance(Cobranca.Fatura instance) {}
		public virtual void SetupInstance(Cobranca.FaturaMarketing instance) {}
		public virtual void SetupInstance(Cobranca.FaturaTrinks instance) {}
		public virtual void SetupInstance(Cobranca.FaturaWhatsApp instance) {}
		public virtual void SetupInstance(Cobranca.FormaDeContratacaoDoAdicional instance) {}
		public virtual void SetupInstance(Cobranca.FormaPagamento instance) {}
		public virtual void SetupInstance(Cobranca.HistoricoDoAdicionalNaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.MotivoDoCancelamento instance) {}
		public virtual void SetupInstance(Cobranca.MotivoDoCancelamentoDaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.ObservacaoAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.OfertaDeServicoAdicional instance) {}
		public virtual void SetupInstance(Cobranca.OfertaDeServicoAdicionalMeuPlano instance) {}
		public virtual void SetupInstance(Cobranca.OfertaDeServicoAdicionalMeuPlanoDisponibilidade instance) {}
		public virtual void SetupInstance(Cobranca.ParceriaTipoTrinks instance) {}
		public virtual void SetupInstance(Cobranca.ParceriaTrinks instance) {}
		public virtual void SetupInstance(Cobranca.PessoaJuridicaPagamentoExterno instance) {}
		public virtual void SetupInstance(Cobranca.PessoaJuridicaPagamentoExternoFatura instance) {}
		public virtual void SetupInstance(Cobranca.PessoaJuridicaPagamentoExternoFaturaHistorico instance) {}
		public virtual void SetupInstance(Cobranca.PlanoAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.PromocaoNaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.PromocaoPraContaFinanceira instance) {}
		public virtual void SetupInstance(Cobranca.PromocaoTrinks instance) {}
		public virtual void SetupInstance(Cobranca.RelatorioAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.RelatorioFaturamento instance) {}
		public virtual void SetupInstance(Cobranca.ResponsavelAtendimento instance) {}
		public virtual void SetupInstance(Cobranca.ServicoTrinks instance) {}
		public virtual void SetupInstance(Cobranca.SolicitacaoCancelamentoDaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.StatusConta instance) {}
		public virtual void SetupInstance(Cobranca.StatusFatura instance) {}
		public virtual void SetupInstance(Cobranca.TipoAssociacao instance) {}
		public virtual void SetupInstance(Cobranca.TipoFormaPagamento instance) {}
		public virtual void SetupInstance(Cobranca.TipoServico instance) {}
		public virtual void SetupInstance(Cobranca.ValorDeAdesaoDoAdicionalPorFaixa instance) {}
		public virtual void SetupInstance(Cobranca.ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura instance) {}
		public virtual void SetupInstance(Cobranca.ValorDoAdicionalPorFaixa instance) {}
		public virtual void SetupInstance(Cobranca.ValorDoAdicionalPorFaixaDaOferta instance) {}
		public virtual void SetupInstance(Cobranca.ValorPorFaixa instance) {}
		public virtual void SetupInstance(ComunidadeTrinks.ArtigoDeNovidade instance) {}
		public virtual void SetupInstance(ComunidadeTrinks.Sugestao instance) {}
		public virtual void SetupInstance(ComunidadeTrinks.TopicoDeVotacao instance) {}
		public virtual void SetupInstance(ComunidadeTrinks.VotacaoDeSugestao instance) {}
		public virtual void SetupInstance(ComunidadeTrinks.Voto instance) {}
		public virtual void SetupInstance(ConciliacaoBancaria.ContaFinanceiraDoEstabelecimento instance) {}
		public virtual void SetupInstance(ConciliacaoBancaria.ContaFinanceiraPadrao instance) {}
		public virtual void SetupInstance(ContaDigital.AutenticacaoContaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.AutenticacaoContaDigitalConfirmacao instance) {}
		public virtual void SetupInstance(ContaDigital.AutenticacaoContaDigitalEnvio instance) {}
		public virtual void SetupInstance(ContaDigital.AutenticacaoIdentidadePreCadastro instance) {}
		public virtual void SetupInstance(ContaDigital.AutenticacaoIdentidadeUsuarioConta instance) {}
		public virtual void SetupInstance(ContaDigital.CategoriaPermissaoContaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.ChavePix instance) {}
		public virtual void SetupInstance(ContaDigital.ChavePixContaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.ChavePixProfissional instance) {}
		public virtual void SetupInstance(ContaDigital.ConfiguracoesContaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.ContaBancariaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.ContaEstabelecimento instance) {}
		public virtual void SetupInstance(ContaDigital.ContaUsuarioDigital instance) {}
		public virtual void SetupInstance(ContaDigital.Dono instance) {}
		public virtual void SetupInstance(ContaDigital.DuvidaFrequente instance) {}
		public virtual void SetupInstance(ContaDigital.EtapaCadastro instance) {}
		public virtual void SetupInstance(ContaDigital.LimiteContaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.LimitePlano instance) {}
		public virtual void SetupInstance(ContaDigital.LOGAprovacaoTransferencias instance) {}
		public virtual void SetupInstance(ContaDigital.Operador instance) {}
		public virtual void SetupInstance(ContaDigital.PagamentoAgendado instance) {}
		public virtual void SetupInstance(ContaDigital.PagamentoAgendadoFolhaMesProfissional instance) {}
		public virtual void SetupInstance(ContaDigital.PermissaoContaDigital instance) {}
		public virtual void SetupInstance(ContaDigital.PermissaoOperador instance) {}
		public virtual void SetupInstance(ContaDigital.Responsavel instance) {}
		public virtual void SetupInstance(ContaDigital.Transferencia instance) {}
		public virtual void SetupInstance(ContaDigital.UsuarioContaDigital instance) {}
		public virtual void SetupInstance(Conteudo.ConteudoImagemLogoEstabelecimento instance) {}
		public virtual void SetupInstance(Conteudo.ConteudoTexto instance) {}
		public virtual void SetupInstance(Conteudo.MenuItem instance) {}
		public virtual void SetupInstance(Conteudo.MenuOpcaoAliasBusca instance) {}
		public virtual void SetupInstance(Conteudo.MenuOpcaoBusca instance) {}
		public virtual void SetupInstance(Controle.PalavraProibida instance) {}
		public virtual void SetupInstance(Controle.PalavraProibidaSMS instance) {}
		public virtual void SetupInstance(Controle.PalavraProibidaTrinks instance) {}
		public virtual void SetupInstance(ControleDeCTAs.CTA instance) {}
		public virtual void SetupInstance(ControleDeCTAs.CTAGrupo instance) {}
		public virtual void SetupInstance(ControleDeCTAs.CTAInformacoesAdicionaisTrinksPro instance) {}
		public virtual void SetupInstance(ControleDeEntradaESaida.LancamentoAporte instance) {}
		public virtual void SetupInstance(ControleDeEntradaESaida.LancamentoDeReceitaCategoria instance) {}
		public virtual void SetupInstance(ControleDeFotos.ArquivoDeImagem instance) {}
		public virtual void SetupInstance(ControleDeFuncionalidades.DisponibilidadeEspecifica instance) {}
		public virtual void SetupInstance(ControleDeFuncionalidades.DisponibilidadeGeral instance) {}
		public virtual void SetupInstance(ControleDeFuncionalidades.PreferenciasDaConta instance) {}
		public virtual void SetupInstance(ControleDeFuncionalidades.ValorDeConfiguracaoEspecifica instance) {}
		public virtual void SetupInstance(ControleDeFuncionalidades.ValorDeConfiguracaoGeral instance) {}
		public virtual void SetupInstance(ControleDeSatisfacao.AvaliacaoDeSatisfacao instance) {}
		public virtual void SetupInstance(ControleDeSatisfacao.AvaliacaoDeSatisfacaoRecebidaRetentativa instance) {}
		public virtual void SetupInstance(ControleDeSatisfacao.Contato instance) {}
		public virtual void SetupInstance(ControleDeSatisfacao.ContatoCelular instance) {}
		public virtual void SetupInstance(ControleDeSatisfacao.ItemParaAvaliar instance) {}
		public virtual void SetupInstance(Correios.ConsultaCep instance) {}
		public virtual void SetupInstance(Cupom.CupomBase instance) {}
		public virtual void SetupInstance(Cupom.CupomDesconto instance) {}
		public virtual void SetupInstance(Cupom.CupomEstabelecimento instance) {}
		public virtual void SetupInstance(Cupom.CupomEstabelecimentoProduto instance) {}
		public virtual void SetupInstance(Cupom.CupomHorarioTransacao instance) {}
		public virtual void SetupInstance(Cupom.CupomItemVenda instance) {}
		public virtual void SetupInstance(Cupom.CupomPessoaFisica instance) {}
		public virtual void SetupInstance(Cupom.CupomServicoEstabelecimento instance) {}
		public virtual void SetupInstance(Cupom.CupomUsoPessoaFisica instance) {}
		public virtual void SetupInstance(DebitoParcial.AbatimentoDeDivida instance) {}
		public virtual void SetupInstance(DebitoParcial.DividaDeixadaNoEstabelecimento instance) {}
		public virtual void SetupInstance(DebitoParcial.HistoricoDaDivida instance) {}
		public virtual void SetupInstance(DebitoParcial.PagamentoDeDividaPeloCliente instance) {}
		public virtual void SetupInstance(Despesas.LancamentosRecorrentesSelecionadas instance) {}
		public virtual void SetupInstance(Despesas.Lancamento instance) {}
		public virtual void SetupInstance(Despesas.LancamentoCategoria instance) {}
		public virtual void SetupInstance(Despesas.LancamentoCategoriaPadrao instance) {}
		public virtual void SetupInstance(Despesas.LancamentoGeradoPorMovimentacaoEstoque instance) {}
		public virtual void SetupInstance(Despesas.LancamentoGrupo instance) {}
		public virtual void SetupInstance(Despesas.LancamentoGrupoPadrao instance) {}
		public virtual void SetupInstance(Despesas.LancamentoRecorrencia instance) {}
		public virtual void SetupInstance(Despesas.LancamentoRecorrenciaTipo instance) {}
		public virtual void SetupInstance(Despesas.LancamentoStatusPagamento instance) {}
		public virtual void SetupInstance(Despesas.LancamentoTipo instance) {}
		public virtual void SetupInstance(Despesas.RenovacaoDeLancamentos instance) {}
		public virtual void SetupInstance(Dispositivos.TipoImpressao instance) {}
		public virtual void SetupInstance(DTO.HorarioFuturosExportacao instance) {}
		public virtual void SetupInstance(Encurtador.EncurtadorDeDados instance) {}
		public virtual void SetupInstance(Estabelecimentos.AvaliacaoEstabelecimento instance) {}
		public virtual void SetupInstance(Estabelecimentos.ConfiguracaoDeAvaliacaoDeSatisfacao instance) {}
		public virtual void SetupInstance(Estabelecimentos.ConfiguracaoEstabelecimento instance) {}
		public virtual void SetupInstance(Estabelecimentos.EstabelecimentoFavorito instance) {}
		public virtual void SetupInstance(Estabelecimentos.EstabelecimentoProfissionalFavorito instance) {}
		public virtual void SetupInstance(Estabelecimentos.EstabelecimentoUUID instance) {}
		public virtual void SetupInstance(Estabelecimentos.ItemConfiguradoParaSerAvaliado instance) {}
		public virtual void SetupInstance(Estabelecimentos.ServicoConfiguradoParaSerAvaliado instance) {}
		public virtual void SetupInstance(Estabelecimentos.ElasticSearch.EstabelecimentoComInformacoesConsolidadas instance) {}
		public virtual void SetupInstance(Estabelecimentos.ElasticSearch.InformacoesConsolidadasDaBuscaDoPortal instance) {}
		public virtual void SetupInstance(Estabelecimentos.ElasticSearch.OpcaoDeAutocompletarDoPortal instance) {}
		public virtual void SetupInstance(Estatistica.BIUsoDoSistema instance) {}
		public virtual void SetupInstance(EstilosVisuais.TemaCss instance) {}
		public virtual void SetupInstance(EstilosVisuais.TemaCssBackoffice instance) {}
		public virtual void SetupInstance(EstoqueComBaixaAutomatica.ConfiguracaoDoServico instance) {}
		public virtual void SetupInstance(EstoqueComBaixaAutomatica.ConfiguracaoParaBaixaAutomatica instance) {}
		public virtual void SetupInstance(EstoqueComBaixaAutomatica.HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario instance) {}
		public virtual void SetupInstance(EstoqueComBaixaAutomatica.HistoricoDeUsoDeProdutoNoHorario instance) {}
		public virtual void SetupInstance(EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomatica instance) {}
		public virtual void SetupInstance(EstoqueComBaixaAutomatica.UsoDeProdutoNoHorario instance) {}
		public virtual void SetupInstance(Extratores.Extrator instance) {}
		public virtual void SetupInstance(Extratores.Visao instance) {}
		public virtual void SetupInstance(Facebook.FBE instance) {}
		public virtual void SetupInstance(FAQ.Assunto instance) {}
		public virtual void SetupInstance(FAQ.AssuntoPerguntaResposta instance) {}
		public virtual void SetupInstance(FAQ.PerguntaResposta instance) {}
		public virtual void SetupInstance(FAQ.Tela instance) {}
		public virtual void SetupInstance(FAQ.TelaAssunto instance) {}
		public virtual void SetupInstance(FAQ.TelaPerguntaResposta instance) {}
		public virtual void SetupInstance(Fidelidade.AgendamentoOnlineQueGerouPontos instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDePontos instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDePontosAgendamentoOnline instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDePontosAvulso instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDePontosHorarioTransacao instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDePontosItemVenda instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDePontosPagamentoAntecipado instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDeTransferenciaDePontos instance) {}
		public virtual void SetupInstance(Fidelidade.MovimentacaoDeUmPontoGanho instance) {}
		public virtual void SetupInstance(Fidelidade.PagamentoAntecipadoQueGerouPontos instance) {}
		public virtual void SetupInstance(Fidelidade.PontoGanho instance) {}
		public virtual void SetupInstance(Fidelidade.ProgramaDeFidelidade instance) {}
		public virtual void SetupInstance(Fidelidade.ProgramaDeFidelidadeDiaSemana instance) {}
		public virtual void SetupInstance(Fidelidade.TransferenciaDePontos instance) {}
		public virtual void SetupInstance(Financeiro.AberturaFechamentoCaixa instance) {}
		public virtual void SetupInstance(Financeiro.AberturaFechamentoCaixaHistorico instance) {}
		public virtual void SetupInstance(Financeiro.Comissao instance) {}
		public virtual void SetupInstance(Financeiro.ContaBancariaPessoa instance) {}
		public virtual void SetupInstance(Financeiro.FechamentoFolhaMes instance) {}
		public virtual void SetupInstance(Financeiro.FechamentoFolhaMesProfissional instance) {}
		public virtual void SetupInstance(Financeiro.FormaPagamento instance) {}
		public virtual void SetupInstance(Financeiro.FormaPagamentoTipo instance) {}
		public virtual void SetupInstance(Financeiro.Gorjeta instance) {}
		public virtual void SetupInstance(Financeiro.LancamentoDeAntecipacao instance) {}
		public virtual void SetupInstance(Financeiro.MotivoDesconto instance) {}
		public virtual void SetupInstance(Financeiro.PagamentoFolhaMesProfissional instance) {}
		public virtual void SetupInstance(Financeiro.Sangria instance) {}
		public virtual void SetupInstance(Financeiro.SangriaHistorico instance) {}
		public virtual void SetupInstance(Financeiro.TipoContaBancaria instance) {}
		public virtual void SetupInstance(Financeiro.TipoTransacao instance) {}
		public virtual void SetupInstance(Financeiro.Transacao instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoFormaPagamento instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoFormaPagamentoParcela instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoHistorico instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoItem instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoLancamentoFinanceiro instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoPOS instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoPOSSplit instance) {}
		public virtual void SetupInstance(Financeiro.TransacaoPosWebhookRequest instance) {}
		public virtual void SetupInstance(Financeiro.ValorDeComissaoAReceber instance) {}
		public virtual void SetupInstance(Financeiro.HistoricoDoCaixaPorOperador instance) {}
		public virtual void SetupInstance(Financeiro.MovimentacaoNoCaixaPorOperador instance) {}
		public virtual void SetupInstance(Financeiro.MovimentacaoNoCaixaPorOperadorLancamento instance) {}
		public virtual void SetupInstance(Financeiro.MovimentacaoNoCaixaPorOperadorTransacao instance) {}
		public virtual void SetupInstance(Financeiro.RegistroDeCaixaPorOperador instance) {}
		public virtual void SetupInstance(Financeiro.DescontoPersonalizado instance) {}
		public virtual void SetupInstance(Financeiro.DescontoPersonalizadoAssistentes instance) {}
		public virtual void SetupInstance(Financeiro.DescontoPersonalizadoPacote instance) {}
		public virtual void SetupInstance(Financeiro.DescontoPersonalizadoProduto instance) {}
		public virtual void SetupInstance(Financeiro.DescontoPersonalizadoProfissionais instance) {}
		public virtual void SetupInstance(Financeiro.DescontoPersonalizadoServico instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoCompraProduto instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoItem instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoItemBonificacao instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoItemGorjeta instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoItemSplit instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoItemVale instance) {}
		public virtual void SetupInstance(Financeiro.FolhaPagamentoLancamento instance) {}
		public virtual void SetupInstance(Formulario.AssinaturaDigital instance) {}
		public virtual void SetupInstance(Formulario.ConfiguracaoDoFormulario instance) {}
		public virtual void SetupInstance(Formulario.FormularioDinamico instance) {}
		public virtual void SetupInstance(Formulario.FormularioRespondido instance) {}
		public virtual void SetupInstance(Formulario.OpcaoDeResposta instance) {}
		public virtual void SetupInstance(Formulario.PessoaPerguntada instance) {}
		public virtual void SetupInstance(Formulario.QuestaoDoFormulario instance) {}
		public virtual void SetupInstance(Formulario.RespostaDoFormulario instance) {}
		public virtual void SetupInstance(Formulario.SolicitacaoDeAssinatura instance) {}
		public virtual void SetupInstance(Formulario.TipoDeResposta instance) {}
		public virtual void SetupInstance(Formulario.VersaoDaQuestaoDoFormulario instance) {}
		public virtual void SetupInstance(Formulario.VersaoDoFormularioDinamico instance) {}
		public virtual void SetupInstance(Fotos.Foto instance) {}
		public virtual void SetupInstance(GyraMais.DadosCliente instance) {}
		public virtual void SetupInstance(Identity.ApiAccount instance) {}
		public virtual void SetupInstance(ImportacaoDeDados.SolicitacaoDeImportacao instance) {}
		public virtual void SetupInstance(IntegracaoComOutrosSistemas.EventoIntegracaoComOutrosSistemas instance) {}
		public virtual void SetupInstance(IntegracaoComOutrosSistemas.FranquiaComChaveDeIntegracao instance) {}
		public virtual void SetupInstance(IntegracaoComOutrosSistemas.FranquiaEstabelecimentoComChaveDeIntegracao instance) {}
		public virtual void SetupInstance(InternoProduto.QuestionarioProduto instance) {}
		public virtual void SetupInstance(InternoProduto.QuestionarioProdutoOpcaoDeResposta instance) {}
		public virtual void SetupInstance(InternoProduto.QuestionarioProdutoPergunta instance) {}
		public virtual void SetupInstance(InternoProduto.QuestionarioProdutoRespondido instance) {}
		public virtual void SetupInstance(InternoProduto.QuestionarioProdutoRespondidoResposta instance) {}
		public virtual void SetupInstance(InternoProduto.QuestionarioProdutoTipoResposta instance) {}
		public virtual void SetupInstance(LinksDePagamento.ItemLinkDePagamento instance) {}
		public virtual void SetupInstance(LinksDePagamento.LinkDePagamento instance) {}
		public virtual void SetupInstance(LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinks instance) {}
		public virtual void SetupInstance(Marcadores.CorEtiqueta instance) {}
		public virtual void SetupInstance(Marcadores.Etiqueta instance) {}
		public virtual void SetupInstance(Marcadores.ObjetoEtiquetado instance) {}
		public virtual void SetupInstance(Marketing.ConfiguracoesEstabelecimentoMarketing instance) {}
		public virtual void SetupInstance(Marketing.ConviteDeRetorno instance) {}
		public virtual void SetupInstance(Marketing.ConviteDeRetornoEmail instance) {}
		public virtual void SetupInstance(Marketing.ConviteDeRetornoParaQuemEnviar instance) {}
		public virtual void SetupInstance(Marketing.ConviteDeRetornoSMS instance) {}
		public virtual void SetupInstance(Marketing.ConviteDeRetornoWhatsApp instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanha instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaEmail instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaHistorico instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaPublicoAlvo instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoServicoEstabelecimento instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoClienteEstabelecimento instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaPublicoAlvoProfissional instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaSMS instance) {}
		public virtual void SetupInstance(Marketing.MarketingCampanhaWhatsApp instance) {}
		public virtual void SetupInstance(Marketing.MarketingCompraCredito instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvio instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioCliente instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioClienteEmail instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioClienteParametroEnvio instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioClienteSMS instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioClienteWhatsApp instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioEmail instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioSMS instance) {}
		public virtual void SetupInstance(Marketing.MarketingEnvioWhatsApp instance) {}
		public virtual void SetupInstance(Marketing.MarketingFaixaProfissionais instance) {}
		public virtual void SetupInstance(Marketing.MarketingPacoteCredito instance) {}
		public virtual void SetupInstance(MarketingInterno.DadosMarketing instance) {}
		public virtual void SetupInstance(MensagemEmTela.MensagemAviso instance) {}
		public virtual void SetupInstance(MensagemEmTela.MensagemAvisoTextoLivre instance) {}
		public virtual void SetupInstance(MensagemEmTela.MensagemAvisoImplementacao instance) {}
		public virtual void SetupInstance(MensagensEmMassa.ModeloDeMensagem instance) {}
		public virtual void SetupInstance(Metricas.MetricaDesativada instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimento instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.ConfiguracaoNFCEstado instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.ImpressaoDeNFC instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.NfcSituacaoTributaria instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.NomenclaturaNCMeNBS instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.NotaFormaPagamentoNFC instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.NotaInutilizadaNFC instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.NotaItensNFC instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.NotaNFC instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigital instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.StatusNotaNFC instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.TabelaIBPT instance) {}
		public virtual void SetupInstance(NotaFiscalDoConsumidor.TipoRegimeTributarioNFC instance) {}
		public virtual void SetupInstance(Notificacoes.CampanhaPush instance) {}
		public virtual void SetupInstance(Notificacoes.ContaQtdNotificacoesNovas instance) {}
		public virtual void SetupInstance(Notificacoes.Dispositivo instance) {}
		public virtual void SetupInstance(Notificacoes.DispositivoB2cValido instance) {}
		public virtual void SetupInstance(Notificacoes.InscricaoEmNotificacao instance) {}
		public virtual void SetupInstance(Notificacoes.NotificacaoDoTrinks instance) {}
		public virtual void SetupInstance(Notificacoes.NotificacaoPush instance) {}
		public virtual void SetupInstance(Notificacoes.Operadora instance) {}
		public virtual void SetupInstance(Notificacoes.OperadoraServicoSMS instance) {}
		public virtual void SetupInstance(Notificacoes.RegistroNotificacao instance) {}
		public virtual void SetupInstance(Notificacoes.ServicoSMS instance) {}
		public virtual void SetupInstance(Notificacoes.TipoNotificacao instance) {}
		public virtual void SetupInstance(NotificacoesApps.CanalDaNotificacao instance) {}
		public virtual void SetupInstance(NotificacoesApps.EventoDeNotificacao instance) {}
		public virtual void SetupInstance(NotificacoesApps.MensagemDeNotificacao instance) {}
		public virtual void SetupInstance(NotificacoesApps.PreferenciaDeNotificacaoDoUsuario instance) {}
		public virtual void SetupInstance(NotificacoesApps.TipoDeNotificacao instance) {}
		public virtual void SetupInstance(Onboardings.EstabelecimentoTrilha instance) {}
		public virtual void SetupInstance(Onboardings.QuestionarioOnboardingPorFaixa instance) {}
		public virtual void SetupInstance(Onboardings.RastreioDeTarefa instance) {}
		public virtual void SetupInstance(Onboardings.Tarefa instance) {}
		public virtual void SetupInstance(Onboardings.Trilha instance) {}
		public virtual void SetupInstance(Onboardings.TrilhaAcao instance) {}
		public virtual void SetupInstance(Onboardings.TrilhaAcaoGrupo instance) {}
		public virtual void SetupInstance(Pacotes.ConfiguracaoPacotePersonalizado instance) {}
		public virtual void SetupInstance(Pacotes.HistoricoConfiguracoesPacote instance) {}
		public virtual void SetupInstance(Pacotes.ItemPacote instance) {}
		public virtual void SetupInstance(Pacotes.ItemPacoteCliente instance) {}
		public virtual void SetupInstance(Pacotes.ItemPacoteClienteProduto instance) {}
		public virtual void SetupInstance(Pacotes.ItemPacoteClienteServico instance) {}
		public virtual void SetupInstance(Pacotes.ItemPacoteProduto instance) {}
		public virtual void SetupInstance(Pacotes.ItemPacoteServico instance) {}
		public virtual void SetupInstance(Pacotes.LinkDePagamentoDoPacote instance) {}
		public virtual void SetupInstance(Pacotes.Pacote instance) {}
		public virtual void SetupInstance(Pacotes.PacoteCliente instance) {}
		public virtual void SetupInstance(Pacotes.PacoteClienteHistorico instance) {}
		public virtual void SetupInstance(PagamentoAntecipadoHotsite.PagamentoAntecipadoHotsiteConfiguracoes instance) {}
		public virtual void SetupInstance(PagamentoAntecipadoHotsite.PagamentoAntecipadoHotsiteServicos instance) {}
		public virtual void SetupInstance(Pagamentos.CartaoDeComprador instance) {}
		public virtual void SetupInstance(Pagamentos.CartaoDeCompradorGateway instance) {}
		public virtual void SetupInstance(Pagamentos.Comprador instance) {}
		public virtual void SetupInstance(Pagamentos.CompradorGateway instance) {}
		public virtual void SetupInstance(Pagamentos.ContaBancaria instance) {}
		public virtual void SetupInstance(Pagamentos.ContaBancariaGateway instance) {}
		public virtual void SetupInstance(Pagamentos.DocumentoDeRecebedorCredenciado instance) {}
		public virtual void SetupInstance(Pagamentos.EnderecoDeCobranca instance) {}
		public virtual void SetupInstance(Pagamentos.EtapaCadastroPagarme instance) {}
		public virtual void SetupInstance(Pagamentos.Gateway instance) {}
		public virtual void SetupInstance(Pagamentos.ItemPagamento instance) {}
		public virtual void SetupInstance(Pagamentos.LimitePagamento instance) {}
		public virtual void SetupInstance(Pagamentos.LimitePagamentoRecebedor instance) {}
		public virtual void SetupInstance(Pagamentos.Pagamento instance) {}
		public virtual void SetupInstance(Pagamentos.Recebedor instance) {}
		public virtual void SetupInstance(Pagamentos.RecebedorCredenciado instance) {}
		public virtual void SetupInstance(Pagamentos.SplitPagamento instance) {}
		public virtual void SetupInstance(Pagamentos.UsoLimiteAntecipacaoDiarioRecebedor instance) {}
		public virtual void SetupInstance(Pagamentos.UsoLimiteAntecipacaoMensalRecebedor instance) {}
		public virtual void SetupInstance(Pagamentos.UsoLimitePagamentoDiarioRecebedor instance) {}
		public virtual void SetupInstance(Pagamentos.UsoLimitePagamentoMensalRecebedor instance) {}
		public virtual void SetupInstance(PagamentosAntecipados.BeneficiosEstabelecimento instance) {}
		public virtual void SetupInstance(PagamentosAntecipados.BeneficiosPagamento instance) {}
		public virtual void SetupInstance(PagamentosAntecipados.ItemPagamentoAntecipado instance) {}
		public virtual void SetupInstance(PagamentosAntecipados.ItemPagamentoHorario instance) {}
		public virtual void SetupInstance(PagamentosAntecipados.PagamentoAntecipado instance) {}
		public virtual void SetupInstance(PagamentosAntecipados.ServicoHabilitado instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.ConfiguracaoAdiantamentoFuncionalidadeAntecipacao instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.EstabelecimentoRecebedor instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.InstituicaoBancaria instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.LOGCancelamentoAntecipacao instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.LOGSolicitacaoAntecipacao instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinks instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.TaxasDoEstabelecimento instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.TaxasDoPagamentoOnlineNoTrinks instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.TaxasPadrao instance) {}
		public virtual void SetupInstance(PagamentosOnlineNoTrinks.TaxasPadraoEstabelecimentoRecebedor instance) {}
		public virtual void SetupInstance(Permissoes.AreaPerlink.AreaPerlinkPerfil instance) {}
		public virtual void SetupInstance(Permissoes.AreaPerlink.AreaPerlinkPerfilPermissao instance) {}
		public virtual void SetupInstance(Permissoes.AreaPerlink.ContaPerfil instance) {}
		public virtual void SetupInstance(Permissoes.AreaPerlink.ContaPerfilPermissao instance) {}
		public virtual void SetupInstance(Permissoes.CategoriaPermissao instance) {}
		public virtual void SetupInstance(Permissoes.DescricaoPermissao instance) {}
		public virtual void SetupInstance(Permissoes.FranquiaPermissao instance) {}
		public virtual void SetupInstance(Permissoes.Perfil instance) {}
		public virtual void SetupInstance(Permissoes.PerfilPermissao instance) {}
		public virtual void SetupInstance(Permissoes.PermissaoArea instance) {}
		public virtual void SetupInstance(Permissoes.UsuarioPerfil instance) {}
		public virtual void SetupInstance(Permissoes.UsuarioPerfilPermissao instance) {}
		public virtual void SetupInstance(Pessoas.Bairro instance) {}
		public virtual void SetupInstance(Pessoas.CacheLocalidade instance) {}
		public virtual void SetupInstance(Pessoas.CategoriaPortalServico instance) {}
		public virtual void SetupInstance(Pessoas.Cidade instance) {}
		public virtual void SetupInstance(Pessoas.Cliente instance) {}
		public virtual void SetupInstance(Pessoas.ClienteAreaPerlink instance) {}
		public virtual void SetupInstance(Pessoas.ClienteEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.ClienteEstabelecimentoSaldos instance) {}
		public virtual void SetupInstance(Pessoas.ComoConheceu instance) {}
		public virtual void SetupInstance(Pessoas.ComoEstabelecimentoConheceuOTrinks instance) {}
		public virtual void SetupInstance(Pessoas.CompartilhamentoNaRede instance) {}
		public virtual void SetupInstance(Pessoas.ConfiguracaoHotsiteAderencia instance) {}
		public virtual void SetupInstance(Pessoas.ConfiguracaoHotsiteInicioMarcos instance) {}
		public virtual void SetupInstance(Pessoas.ConfiguracaoHotsiteIntervalo instance) {}
		public virtual void SetupInstance(Pessoas.ConfiguracaoHotsiteUniverso instance) {}
		public virtual void SetupInstance(Pessoas.Conta instance) {}
		public virtual void SetupInstance(Pessoas.ContaFranquia instance) {}
		public virtual void SetupInstance(Pessoas.DadosParaRecalculoComissao instance) {}
		public virtual void SetupInstance(Pessoas.DataEspecial instance) {}
		public virtual void SetupInstance(Pessoas.DiaSemana instance) {}
		public virtual void SetupInstance(Pessoas.EmailRejeitadoAmazon instance) {}
		public virtual void SetupInstance(Pessoas.Endereco instance) {}
		public virtual void SetupInstance(Pessoas.EnderecoPreenchidoManualmente instance) {}
		public virtual void SetupInstance(Pessoas.Estabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoAssistenteServico instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoAssistenteServicoComissao instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoAtendeCrianca instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoConfiguracaoComissao instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoConfiguracaoGeral instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoConfiguracaoPOS instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoDadosGerais instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastro instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastroCargo instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastroMotivosCadastro instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoDadosPreCadastroSegmentos instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoFabricanteProduto instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoFormaPagamento instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoFormaPagamentoParcela instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoFornecedor instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoHorarioEspecialFuncionamento instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoHorarioEspecialFuncionamentoTipo instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoHorarioFuncionamento instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoIndicado instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoMovimentacaoEstoque instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoPossuiEstacionamento instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoPreCadastro instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoProduto instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoProfissional instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoProfissionalRedeSocial instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoProfissionalServico instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoProfissionalServicoComissao instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoSolicitacaoAparecerBusca instance) {}
		public virtual void SetupInstance(Pessoas.EstabelecimentoTemplate instance) {}
		public virtual void SetupInstance(Pessoas.EstadoCivil instance) {}
		public virtual void SetupInstance(Pessoas.EstatisticaExibicaoTelefone instance) {}
		public virtual void SetupInstance(Pessoas.FabricanteProdutoPadrao instance) {}
		public virtual void SetupInstance(Pessoas.FormaRelacaoProfissional instance) {}
		public virtual void SetupInstance(Pessoas.FormaRelacaoProfissionalPadrao instance) {}
		public virtual void SetupInstance(Pessoas.Fornecedor instance) {}
		public virtual void SetupInstance(Pessoas.FotoDeClienteEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.FotoDeServicoRealizadoEmHorario instance) {}
		public virtual void SetupInstance(Pessoas.FotoEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.FotoPessoa instance) {}
		public virtual void SetupInstance(Pessoas.Franquia instance) {}
		public virtual void SetupInstance(Pessoas.FranquiaEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.FuncaoDoProfissional instance) {}
		public virtual void SetupInstance(Pessoas.FuncaoDoProfissionalPadrao instance) {}
		public virtual void SetupInstance(Pessoas.HistoricoAcaoUnidadeAoModelo instance) {}
		public virtual void SetupInstance(Pessoas.HistoricoCliente instance) {}
		public virtual void SetupInstance(Pessoas.Horario instance) {}
		public virtual void SetupInstance(Pessoas.HorarioHistorico instance) {}
		public virtual void SetupInstance(Pessoas.HorarioHistoricoEtiqueta instance) {}
		public virtual void SetupInstance(Pessoas.HorarioOrigem instance) {}
		public virtual void SetupInstance(Pessoas.HorarioQuemCancelou instance) {}
		public virtual void SetupInstance(Pessoas.HorarioTrabalho instance) {}
		public virtual void SetupInstance(Pessoas.HorarioTransacao instance) {}
		public virtual void SetupInstance(Pessoas.HorarioVeraoCidade instance) {}
		public virtual void SetupInstance(Pessoas.HorarioVeraoUF instance) {}
		public virtual void SetupInstance(Pessoas.HotsiteEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.ItemComboCliente instance) {}
		public virtual void SetupInstance(Pessoas.LinkDePagamentoDoHorario instance) {}
		public virtual void SetupInstance(Pessoas.MidiaSocial instance) {}
		public virtual void SetupInstance(Pessoas.MotivoAusencia instance) {}
		public virtual void SetupInstance(Pessoas.MotivoQueEscolheuOTrinks instance) {}
		public virtual void SetupInstance(Pessoas.MotivoQueEstabelecimentoEscolheuOTrinks instance) {}
		public virtual void SetupInstance(Pessoas.NotificacaoEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.Novidade instance) {}
		public virtual void SetupInstance(Pessoas.ParametrizacaoTrinks instance) {}
		public virtual void SetupInstance(Pessoas.PeriodoAusencia instance) {}
		public virtual void SetupInstance(Pessoas.PermissoesDaUnidadeBaseadaEmModelo instance) {}
		public virtual void SetupInstance(Pessoas.Pessoa instance) {}
		public virtual void SetupInstance(Pessoas.PessoaFisica instance) {}
		public virtual void SetupInstance(Pessoas.PessoaJuridica instance) {}
		public virtual void SetupInstance(Pessoas.PessoaJuridicaConfiguracaoNFe instance) {}
		public virtual void SetupInstance(Pessoas.Profissional instance) {}
		public virtual void SetupInstance(Pessoas.Questionario instance) {}
		public virtual void SetupInstance(Pessoas.RecorrenciaHorario instance) {}
		public virtual void SetupInstance(Pessoas.RelatorioFormaPagamento instance) {}
		public virtual void SetupInstance(Pessoas.SaldoDeSMSLembreteDoEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.Servico instance) {}
		public virtual void SetupInstance(Pessoas.ServicoCategoria instance) {}
		public virtual void SetupInstance(Pessoas.ServicoCategoriaEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.ServicoEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.ServicoSinonimo instance) {}
		public virtual void SetupInstance(Pessoas.SincronizacaoEntreEstabelecimentosModelosFila instance) {}
		public virtual void SetupInstance(Pessoas.SincronizacaoEstabelecimentosFila instance) {}
		public virtual void SetupInstance(Pessoas.StatusHorario instance) {}
		public virtual void SetupInstance(Pessoas.Telefone instance) {}
		public virtual void SetupInstance(Pessoas.TelefoneInternacional instance) {}
		public virtual void SetupInstance(Pessoas.TemaHotsite instance) {}
		public virtual void SetupInstance(Pessoas.Template instance) {}
		public virtual void SetupInstance(Pessoas.TemplateHotsite instance) {}
		public virtual void SetupInstance(Pessoas.TipoComissao instance) {}
		public virtual void SetupInstance(Pessoas.TipoDesconto instance) {}
		public virtual void SetupInstance(Pessoas.TipoEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.TipoFranquia instance) {}
		public virtual void SetupInstance(Pessoas.TipoLogradouro instance) {}
		public virtual void SetupInstance(Pessoas.TipoPOS instance) {}
		public virtual void SetupInstance(Pessoas.TipoPreco instance) {}
		public virtual void SetupInstance(Pessoas.TipoRecorrenciaHorario instance) {}
		public virtual void SetupInstance(Pessoas.TipoServicoEstabelecimento instance) {}
		public virtual void SetupInstance(Pessoas.TipoTelefone instance) {}
		public virtual void SetupInstance(Pessoas.TipoTemplate instance) {}
		public virtual void SetupInstance(Pessoas.UF instance) {}
		public virtual void SetupInstance(Pessoas.UnidadeMedida instance) {}
		public virtual void SetupInstance(Pessoas.UsuarioEstabelecimento instance) {}
		public virtual void SetupInstance(ProdutoEstoque.EstabelecimentoProdutoCategoria instance) {}
		public virtual void SetupInstance(ProdutoEstoque.Inventario instance) {}
		public virtual void SetupInstance(ProdutoEstoque.InventarioMovimentacaoEstoque instance) {}
		public virtual void SetupInstance(ProdutoEstoque.MovimentoEstoqueTipo instance) {}
		public virtual void SetupInstance(ProdutoEstoque.OpcaoDaPropriedadeDeProduto instance) {}
		public virtual void SetupInstance(ProdutoEstoque.ProdutoCategoriaPadrao instance) {}
		public virtual void SetupInstance(ProdutoEstoque.ProdutoDoInventario instance) {}
		public virtual void SetupInstance(ProdutoEstoque.ProdutoPadrao instance) {}
		public virtual void SetupInstance(ProdutoEstoque.PropriedadeDeProduto instance) {}
		public virtual void SetupInstance(ProdutoEstoque.ValorPropriedadeDoProduto instance) {}
		public virtual void SetupInstance(ProfissionalAgenda.DisponibilidadeNaAgenda instance) {}
		public virtual void SetupInstance(ProfissionalAgenda.LiberacaoDeHorarioNaAgenda instance) {}
		public virtual void SetupInstance(ProfissionalAgenda.MotivoDeLiberacao instance) {}
		public virtual void SetupInstance(ProfissionalAgenda.RelatorioAusenciaELiberacaoHorario instance) {}
		public virtual void SetupInstance(ProfissionalAgenda.TipoDeDisponibilidade instance) {}
		public virtual void SetupInstance(ProjetoBackToSalon.Cupom instance) {}
		public virtual void SetupInstance(ProjetoBackToSalon.EstabelecimentoParticipanteBTS instance) {}
		public virtual void SetupInstance(ProjetoBelezaAmiga.CompraDeVoucher instance) {}
		public virtual void SetupInstance(ProjetoBelezaAmiga.EstabelecimentoParticipante instance) {}
		public virtual void SetupInstance(ProjetoBelezaAmiga.Voucher instance) {}
		public virtual void SetupInstance(ProjetoEncontreSeuSalao.EstabelecimentoParticipanteESSV2 instance) {}
		public virtual void SetupInstance(ProjetoEncontreSeuSalao.GmapsLimitStatus instance) {}
		public virtual void SetupInstance(Promocoes.Promocao instance) {}
		public virtual void SetupInstance(Promocoes.PromocaoDiaDaSemana instance) {}
		public virtual void SetupInstance(PromocoesOnline.AgendamentoTemporario instance) {}
		public virtual void SetupInstance(PromocoesOnline.HorariosDaPromocao instance) {}
		public virtual void SetupInstance(PromocoesOnline.PromocaoOnline instance) {}
		public virtual void SetupInstance(PromotoresDoTrinks.HistoricoUsoCuponsParceria instance) {}
		public virtual void SetupInstance(PromotoresDoTrinks.NovosProfissionaisPromotoresDoTrinks instance) {}
		public virtual void SetupInstance(PromotoresDoTrinks.PromotorDoTrinks instance) {}
		public virtual void SetupInstance(PromotoresDoTrinks.SaldoPromotor instance) {}
		public virtual void SetupInstance(PromotoresDoTrinks.TransacaoPromotor instance) {}
		public virtual void SetupInstance(RecorrenciaDeAssinatura.AssinaturaRecorrente instance) {}
		public virtual void SetupInstance(Relatorios.ClienteAtendido instance) {}
		public virtual void SetupInstance(Relatorios.ConsultaRelatorioConsolidadoDia instance) {}
		public virtual void SetupInstance(Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMes instance) {}
		public virtual void SetupInstance(Relatorios.ConsultaRelatorioConsolidadoMes instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeCliente instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeClienteEstendido instance) {}
		public virtual void SetupInstance(Relatorios.RankingDePacotes instance) {}
		public virtual void SetupInstance(Relatorios.RankingDePacotesEstendido instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeProdutos instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeProdutosEstendido instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeProfissionais instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeProfissionaisEstendido instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeServicos instance) {}
		public virtual void SetupInstance(Relatorios.RankingDeServicosEstendido instance) {}
		public virtual void SetupInstance(Relatorios.RankingEstendidoEstabelecimentos instance) {}
		public virtual void SetupInstance(Relatorios.RankingItensDePacotes instance) {}
		public virtual void SetupInstance(Relatorios.RankingItensDePacotesEstendido instance) {}
		public virtual void SetupInstance(Relatorios.RelatorioDemonstrativoDeResultado instance) {}
		public virtual void SetupInstance(Relatorios.RelatorioDemonstrativoDeResultadoReceita instance) {}
		public virtual void SetupInstance(Relatorios.TelaRelatorio instance) {}
		public virtual void SetupInstance(Relatorios.TelaRelatorioCategoria instance) {}
		public virtual void SetupInstance(RodizioDeProfissionais.ColocacaoDoProfissional instance) {}
		public virtual void SetupInstance(RodizioDeProfissionais.HorarioNoRodizio instance) {}
		public virtual void SetupInstance(RodizioDeProfissionais.MovimentacaoNoRodizio instance) {}
		public virtual void SetupInstance(RPS.CobRpsEmissao instance) {}
		public virtual void SetupInstance(RPS.CobRpsLote instance) {}
		public virtual void SetupInstance(RPS.ConfiguracaoPadraoNFS instance) {}
		public virtual void SetupInstance(RPS.DadosRPSTransacao instance) {}
		public virtual void SetupInstance(RPS.EmissaoRPS instance) {}
		public virtual void SetupInstance(RPS.LoteRPS instance) {}
		public virtual void SetupInstance(RPS.MunicipioPadraoNfse instance) {}
		public virtual void SetupInstance(RPS.NfseConfiguracaoMunicipio instance) {}
		public virtual void SetupInstance(Seguranca.AcoesProibidasMvc instance) {}
		public virtual void SetupInstance(Seguranca.ApiClient instance) {}
		public virtual void SetupInstance(Seguranca.ApiRefreshToken instance) {}
		public virtual void SetupInstance(SugestoesEPedidosDeCompra.CancelamentoDoPedido instance) {}
		public virtual void SetupInstance(SugestoesEPedidosDeCompra.EstabelecimentoMovimentacaoEstoquePedido instance) {}
		public virtual void SetupInstance(SugestoesEPedidosDeCompra.InformacaoAlterada instance) {}
		public virtual void SetupInstance(SugestoesEPedidosDeCompra.ItemDePedidoDeCompra instance) {}
		public virtual void SetupInstance(SugestoesEPedidosDeCompra.PedidoDeCompra instance) {}
		public virtual void SetupInstance(SugestoesEPedidosDeCompra.RegistroDeAlteracao instance) {}
		public virtual void SetupInstance(SurveyAppB2B.SurveyAppPro instance) {}
		public virtual void SetupInstance(TesteAB.Amostra instance) {}
		public virtual void SetupInstance(TesteAB.Grupo instance) {}
		public virtual void SetupInstance(TesteAB.Metrica instance) {}
		public virtual void SetupInstance(TestesAB.TesteABAssinatura instance) {}
		public virtual void SetupInstance(TestesAB.TesteABAssinaturaPlanoAssinatura instance) {}
		public virtual void SetupInstance(TestesAB.TesteABAssinaturaPlanoAssinaturaEstabelecimento instance) {}
		public virtual void SetupInstance(TestesAB.TesteABWhyTrinks instance) {}
		public virtual void SetupInstance(TestesAB.TesteABWhyTrinksHistorico instance) {}
		public virtual void SetupInstance(TrinksApps.AplicativoDeAgendamento instance) {}
		public virtual void SetupInstance(TrinksApps.AplicativoDeAgendamentoFuncionalidades instance) {}
		public virtual void SetupInstance(TrinksApps.ConfiguracoesAppProfissional instance) {}
		public virtual void SetupInstance(TrinksApps.DadosOnboardingOQueProcuraNoTrinks instance) {}
		public virtual void SetupInstance(TrinksApps.DispositivoComAplicativo instance) {}
		public virtual void SetupInstance(TrinksApps.FuncionalidadeDoAplicativoDeAgendamento instance) {}
		public virtual void SetupInstance(TrinksAtendimento.AssuntoFaleConoscoTrinksProfissional instance) {}
		public virtual void SetupInstance(ValidacaoDeIdentidade.EmailVerificado instance) {}
		public virtual void SetupInstance(ValidacaoDeIdentidade.TelefoneVerificado instance) {}
		public virtual void SetupInstance(ValidacaoDeIdentidade.VerificacaoDeContaPelaAreaPerlink instance) {}
		public virtual void SetupInstance(ValidacaoDeIdentidade.VerificacaoDeIdentidade instance) {}
		public virtual void SetupInstance(ValidacaoDeIdentidade.VerificacaoDeIdentidadeEnvio instance) {}
		public virtual void SetupInstance(Vendas.Comanda instance) {}
		public virtual void SetupInstance(Vendas.ItemVenda instance) {}
		public virtual void SetupInstance(Vendas.ItemVendaAssinaturaCliente instance) {}
		public virtual void SetupInstance(Vendas.ItemVendaPacote instance) {}
		public virtual void SetupInstance(Vendas.ItemVendaProduto instance) {}
		public virtual void SetupInstance(Vendas.ItemVendaValePresente instance) {}
		public virtual void SetupInstance(Vendas.PreVenda instance) {}
		public virtual void SetupInstance(Vendas.PreVendaProduto instance) {}
		public virtual void SetupInstance(Vendas.PreVendaServico instance) {}
		public virtual void SetupInstance(Vendas.PreVendaHistorico instance) {}
		public virtual void SetupInstance(Vendas.PreVendaStatus instance) {}
		public virtual void SetupInstance(Vendas.ValePresente instance) {}
		public virtual void SetupInstance(Vendas.Venda instance) {}
		public virtual void SetupInstance(WhatsApp.AllowedTestEstablishments instance) {}
		public virtual void SetupInstance(WhatsApp.AvaliacaoHistorico instance) {}
		public virtual void SetupInstance(WhatsApp.CartaoEstabelecimento instance) {}
		public virtual void SetupInstance(WhatsApp.CompraCredito instance) {}
		public virtual void SetupInstance(WhatsApp.CompraRecorrenteEstabelecimento instance) {}
		public virtual void SetupInstance(WhatsApp.CompraRecorrenteEstabelecimentoHistorico instance) {}
		public virtual void SetupInstance(WhatsApp.EstablishmentConfiguration instance) {}
		public virtual void SetupInstance(WhatsApp.FranquiaValor instance) {}
		public virtual void SetupInstance(WhatsApp.HistoricoCompraAdicional instance) {}
		public virtual void SetupInstance(WhatsApp.HistoricoComunicacaoHorario instance) {}
		public virtual void SetupInstance(WhatsApp.HistoricoHorarioTag instance) {}
		public virtual void SetupInstance(WhatsApp.HistoricoSessao instance) {}
		public virtual void SetupInstance(WhatsApp.HistoricoSessaoProcesso instance) {}
		public virtual void SetupInstance(WhatsApp.HistoricoSessaoProcessoRegistro instance) {}
		public virtual void SetupInstance(WhatsApp.HorarioComunicacao instance) {}
		public virtual void SetupInstance(WhatsApp.HorarioTag instance) {}
		public virtual void SetupInstance(WhatsApp.MessageTemplate instance) {}
		public virtual void SetupInstance(WhatsApp.MovimentacaoSaldo instance) {}
		public virtual void SetupInstance(WhatsApp.OptOut instance) {}
		public virtual void SetupInstance(WhatsApp.OptOutHistorico instance) {}
		public virtual void SetupInstance(WhatsApp.PacoteCredito instance) {}
		public virtual void SetupInstance(WhatsApp.PacoteCreditoFormaContratacao instance) {}
		public virtual void SetupInstance(WhatsApp.PacotePersonalizado instance) {}
		public virtual void SetupInstance(WhatsApp.SaldoEstabelecimento instance) {}
		public virtual void SetupInstance(WhatsApp.TipoValorPacote instance) {}
		public virtual void SetupInstance(WhatsApp.ValorPacote instance) {}
 
	}
}
