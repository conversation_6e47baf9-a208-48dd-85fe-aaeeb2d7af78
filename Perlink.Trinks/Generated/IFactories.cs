using System;
using Perlink.DomainInfrastructure.Factories;
namespace Perlink.Trinks.Autoatendimento.Factories {

    /// <summary>
    /// Interface para repositório da entidade CheckInEstablishments.
    /// </summary>
    public partial interface ICheckInEstablishmentsFactory :IBaseFactory {  
    
		CheckInEstablishments Create();    
		CheckInEstablishments Create(int id, System.DateTime dataRealizacaoCheckIn, int idEstabelecimento, int idEstabelecimentoCliente, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.Autoatendimento.Enums.CheckInTypeEnum tipo, int ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfigurationsEstablishment.
    /// </summary>
    public partial interface IConfigurationsEstablishmentFactory :IBaseFactory {  
    
		ConfigurationsEstablishment Create();    
		ConfigurationsEstablishment Create(int id, bool terminalAberto, int? tempoAntecedenciaCheckInPadrao, string corDoTerminal, int idEstabelecimento, bool removerBackground, bool checkInAgendados, bool checkoutHabilitado, bool pagamentoObrigatorio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade StatusServiceCustomerEstablishment.
    /// </summary>
    public partial interface IStatusServiceCustomerEstablishmentFactory :IBaseFactory {  
    
		StatusServiceCustomerEstablishment Create();    
		StatusServiceCustomerEstablishment Create(int id, bool servicoValidado, int idHorario, int idEstabelecimentoProfissional, int idCheckIn, int idEstabelecimentoServico, int ativo, bool notificacaoEnviada);
			 
    }
 
}
namespace Perlink.Trinks.Autoatendimento.DTO.Factories {

 
}
namespace Perlink.Trinks.Autoatendimento.Enums.Factories {

 
}
namespace Perlink.Trinks.Factories {

 
}
namespace Perlink.Trinks.BaseIBPT.Factories {

    /// <summary>
    /// Interface para repositório da entidade DadosIBPT.
    /// </summary>
    public partial interface IDadosIBPTFactory :IBaseFactory {  
    
		DadosIBPT Create();    
		DadosIBPT Create(int id, int idLocalUF, string codigo, string ex, string tipo, string descricao, decimal nacionalFederal, decimal importadoFederal, decimal estadual, decimal municipal, System.DateTime? vigenciaDataInicio, System.DateTime? vigenciaDataFim, string chave, string versao, string fonte);
			 
    }
 
}
namespace Perlink.Trinks.Factories {

 
}
namespace Perlink.Trinks.Factories {

 
}
namespace Perlink.Trinks.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Factories {

    /// <summary>
    /// Interface para repositório da entidade BandeiraCartaoMDR.
    /// </summary>
    public partial interface IBandeiraCartaoMDRFactory :IBaseFactory {  
    
		BandeiraCartaoMDR Create();    
		BandeiraCartaoMDR Create(int id, string nome, string linkImagem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Credenciamento.
    /// </summary>
    public partial interface ICredenciamentoFactory :IBaseFactory {  
    
		Credenciamento Create();    
		Credenciamento Create(int id, int responsavelCadastro, int idEstabelecimento, string documentNumber, int tpv, int mcc, string emailAccessStone, string cellPhone, bool anticipationIsAutomatic, decimal anticipationAutomaticRate, decimal anticipationSpotRate, System.DateTime createdAt);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CredenciamentoComStoneCode.
    /// </summary>
    public partial interface ICredenciamentoComStoneCodeFactory :IBaseFactory {  
    
		CredenciamentoComStoneCode Create();    
		CredenciamentoComStoneCode Create(int id, int idEstabelecimento, string documentNumber, string legalName, string businessName, string stoneCode, string emailAgentStone, decimal anticipationAutomaticRate, decimal anticipationSpotRate, System.DateTime createdAt);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoTerminalPos.
    /// </summary>
    public partial interface IEstabelecimentoTerminalPosFactory :IBaseFactory {  
    
		EstabelecimentoTerminalPos Create();    
		EstabelecimentoTerminalPos Create(int id, Perlink.Trinks.Pessoas.EstabelecimentoConfiguracaoPOS estabelecimentoConfigPos, string nome, string serialNumber, Perlink.Trinks.Pessoas.TipoPOS tipoPos, bool vinculadaAoAutoatendimento, System.DateTime? dataUltimaTransacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Hierarquia.
    /// </summary>
    public partial interface IHierarquiaFactory :IBaseFactory {  
    
		Hierarquia Create();    
		Hierarquia Create(int id, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Mcc.
    /// </summary>
    public partial interface IMccFactory :IBaseFactory {  
    
		Mcc Create();    
		Mcc Create(int id, string codigo, string segmento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TaxaAntecipacao.
    /// </summary>
    public partial interface ITaxaAntecipacaoFactory :IBaseFactory {  
    
		TaxaAntecipacao Create();    
		TaxaAntecipacao Create(int id, Perlink.Trinks.Belezinha.Hierarquia hierarquia, Perlink.Trinks.Belezinha.TpvMensal tpvMensal, decimal? posWifi, decimal? pinpad, decimal? mobpin, int isencao, decimal? ravAuto, decimal? ravSpot, decimal? gateway, decimal? antifraude, decimal? pix, System.DateTime dataCriacao, Perlink.Trinks.Pessoas.Pessoa pessoaQueCriou, System.DateTime dataUltimaAtualizacao, Perlink.Trinks.Pessoas.Pessoa pessoaQueAlterouPorUltimo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TaxaMDR.
    /// </summary>
    public partial interface ITaxaMDRFactory :IBaseFactory {  
    
		TaxaMDR Create();    
		TaxaMDR Create(int id, Perlink.Trinks.Belezinha.Hierarquia hierarquia, Perlink.Trinks.Belezinha.TpvMensal tpvMensal, Perlink.Trinks.Belezinha.Mcc mcc, Perlink.Trinks.Belezinha.BandeiraCartaoMDR cartao, decimal? debito, decimal? credito, decimal? credito2a6, decimal? credito7a12, System.DateTime dataCriacao, Perlink.Trinks.Pessoas.Pessoa pessoaQueCriou, System.DateTime dataUltimaAtualizacao, Perlink.Trinks.Pessoas.Pessoa pessoaQueAlterouPorUltimo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TpvMensal.
    /// </summary>
    public partial interface ITpvMensalFactory :IBaseFactory {  
    
		TpvMensal Create();    
		TpvMensal Create(int id, string descricao, decimal valorInicial, decimal valorFinal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoAvulsaPOSWebhookRequest.
    /// </summary>
    public partial interface ITransacaoAvulsaPOSWebhookRequestFactory :IBaseFactory {  
    
		TransacaoAvulsaPOSWebhookRequest Create();    
		TransacaoAvulsaPOSWebhookRequest Create(int id, string orderId, string chargeId, Perlink.Trinks.Belezinha.EstabelecimentoTerminalPos estabelecimentoTerminalPos, decimal valor, Perlink.Trinks.Belezinha.Enums.StatusDaCapturaEnum statusDaCaptura, System.DateTime dataCriacao);
			 
    }
 
}
namespace Perlink.Trinks.Belezinha.DTO.AgendaDeRecebiveis.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Enums.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Filters.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Helpers.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Strategies.Factories {

 
}
namespace Perlink.Trinks.Belezinha.Strategies.Pagarme.Factories {

 
}
namespace Perlink.Trinks.Caching.Factories {

 
}
namespace Perlink.Trinks.Cashback.Factories {

    /// <summary>
    /// Interface para repositório da entidade BonusTransacao.
    /// </summary>
    public partial interface IBonusTransacaoFactory :IBaseFactory {  
    
		BonusTransacao Create();    
		BonusTransacao Create(int idTransacao, Perlink.Trinks.Financeiro.Transacao transacao, decimal valorBonusConsumido, decimal valorBonusGerado, int idBonusCrmBonus, int idPedidoCrmBonus, int idClienteCrmBonus, string celularCliente, bool enviado, bool cancelado, string objetoEnvioAssincrono, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackComissao.
    /// </summary>
    public partial interface ICashbackComissaoFactory :IBaseFactory {  
    
		CashbackComissao Create();    
		CashbackComissao Create(int idComissao, Perlink.Trinks.Financeiro.Comissao comissao, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackComissaoValorAReceber.
    /// </summary>
    public partial interface ICashbackComissaoValorAReceberFactory :IBaseFactory {  
    
		CashbackComissaoValorAReceber Create();    
		CashbackComissaoValorAReceber Create(int idComissaoValorReceber, Perlink.Trinks.Financeiro.ValorDeComissaoAReceber comissaoValorAReceber, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackHorarioTransacao.
    /// </summary>
    public partial interface ICashbackHorarioTransacaoFactory :IBaseFactory {  
    
		CashbackHorarioTransacao Create();    
		CashbackHorarioTransacao Create(int idHorarioTransacao, Perlink.Trinks.Pessoas.HorarioTransacao horarioTransacao, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackItemVenda.
    /// </summary>
    public partial interface ICashbackItemVendaFactory :IBaseFactory {  
    
		CashbackItemVenda Create();    
		CashbackItemVenda Create(int idItemVenda, Perlink.Trinks.Vendas.ItemVenda itemVenda, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackTransacao.
    /// </summary>
    public partial interface ICashbackTransacaoFactory :IBaseFactory {  
    
		CashbackTransacao Create();    
		CashbackTransacao Create(int idTransacao, Perlink.Trinks.Financeiro.Transacao transacao, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosIntegracao.
    /// </summary>
    public partial interface IEstabelecimentoDadosIntegracaoFactory :IBaseFactory {  
    
		EstabelecimentoDadosIntegracao Create();    
		EstabelecimentoDadosIntegracao Create(int idEstabelecimento, string urlBaseIntegracao, string autorizacaoCrmBonus, int idLojaCrmBonus, string codigoEmpresaCrmBonus, System.DateTime dataCriacao, string urlBaseEnvioAssincrono, string usuarioEnvioAssincrono, string senhaEnvioAssincrono);
			 
    }
 
}
namespace Perlink.Trinks.Cashback.DTO.CrmBonus.Factories {

 
}
namespace Perlink.Trinks.Cashback.DTO.Factories {

 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.Factories {

    /// <summary>
    /// Interface para repositório da entidade Anotacao.
    /// </summary>
    public partial interface IAnotacaoFactory :IBaseFactory {  
    
		Anotacao Create();    
		Anotacao Create(int id, int idEstabelecimento, int idHorario, string notasDoAtendimento, int idPessoaQueCriou, System.DateTime dataHoraCriacao, int? idPessoaQueFezUltimaAlteracao, System.DateTime? dataHoraUltimaAlteracao, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.DTO.Factories {

 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.Stories.Factories {

 
}
namespace Perlink.Trinks.ClientesAnexos.Factories {

    /// <summary>
    /// Interface para repositório da entidade ClienteAnexo.
    /// </summary>
    public partial interface IClienteAnexoFactory :IBaseFactory {  
    
		ClienteAnexo Create();    
		ClienteAnexo Create(int id, Perlink.Trinks.ClientesAnexos.MeuAnexo anexo, int idClienteEstabelecimento, int idEstabelecimento, string nomeDeExibicaoDoArquivo, Perlink.Trinks.ClientesAnexos.Enums.TipoClienteAnexoEnum tipo, int? idObjetoReferencia);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MeuAnexo.
    /// </summary>
    public partial interface IMeuAnexoFactory :IBaseFactory {  
    
		MeuAnexo Create();    
		MeuAnexo Create(int idAnexo, string nomeArquivo, string extensaoDoArquivo, System.DateTime dataHoraUpload, int idPessoaQueFezUpload);
			 
    }
 
}
namespace Perlink.Trinks.ClientesAnexos.DTO.Factories {

 
}
namespace Perlink.Trinks.ClientesAnexos.Enums.Factories {

 
}
namespace Perlink.Trinks.ClientesAnexos.ObjetosDeValor.Factories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Factories {

    /// <summary>
    /// Interface para repositório da entidade AssinaturaDoCliente.
    /// </summary>
    public partial interface IAssinaturaDoClienteFactory :IBaseFactory {  
    
		AssinaturaDoCliente Create();    
		AssinaturaDoCliente Create(int id, int idEstabelecimento, int idPessoaFisicaCliente, string nome, string descricao, Perlink.Trinks.ClubeDeAssinaturas.PlanoCliente planoCliente, Perlink.Trinks.ClubeDeAssinaturas.Enums.StatusDaAssinaturaEnum status, bool permitiConsumoAteFimPeriodoPago, bool podeConsumirItens, decimal valorAPagar, int? encerraAposXCobrancas, int? quantidadeDeVigenciasPagas, Perlink.Trinks.ClubeDeAssinaturas.Enums.PeriodoDoCicloEnum cicloDeCobranca, System.DateTime dataAssinatura, Perlink.Trinks.ClubeDeAssinaturas.Enums.CanalDeVendaEnum canalDeVenda, System.DateTime? dataProgramadaEncerramento, System.DateTime? dataUltimoPagamento, System.DateTime? dataProximoPagamento, Perlink.Trinks.ClubeDeAssinaturas.VigenciaDeAssinatura vigenciaAtual, System.DateTime? dataCancelamento, int? idPessoaQueCancelou, int diaVencimento, System.Collections.Generic.IList<Perlink.Trinks.ClubeDeAssinaturas.BeneficioDaAssinatura> beneficiosDaAssinatura, System.Guid identificadorAssinatura, Perlink.Trinks.ClubeDeAssinaturas.Enums.MetodoCobrancaEnum metodoCobranca, Perlink.Trinks.ClubeDeAssinaturas.PagamentoMultaDeCancelamentoDaAssinatura multaDeCancelamentoDaAssinatura, bool emailContratoDeAdesaoEnviado, bool emailAvisoEdicaoEnviado, System.DateTime? dataAssinaturaContrato, System.Collections.Generic.IList<Perlink.Trinks.ClubeDeAssinaturas.HistoricoDeStatusAssinaturaDoClube> historicoDeStatusDaAssinatura);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Beneficio.
    /// </summary>
    public partial interface IBeneficioFactory :IBaseFactory {  
    
		Beneficio Create();    
		Beneficio Create(int id, bool consumoLimitado, int? quantidadeMaximaConsumo, Perlink.Trinks.ClubeDeAssinaturas.Enums.CicloDeConsumoEnum cicloDeConsumo, int? prazoParaConsumo, decimal valorUnitario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDaAssinatura.
    /// </summary>
    public partial interface IBeneficioDaAssinaturaFactory :IBaseFactory {  
    
		BeneficioDaAssinatura Create();    
		BeneficioDaAssinatura Create(int id, Perlink.Trinks.ClubeDeAssinaturas.AssinaturaDoCliente assinatura, Perlink.Trinks.ClubeDeAssinaturas.Beneficio beneficio, string nomeDoItem, bool ativo, System.DateTime dataDeInclusao, int quantidadeConsumidaNoCicloAtual, int quantidadeConsumidaTotal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDoPlano.
    /// </summary>
    public partial interface IBeneficioDoPlanoFactory :IBaseFactory {  
    
		BeneficioDoPlano Create();    
		BeneficioDoPlano Create(int id, Perlink.Trinks.ClubeDeAssinaturas.PlanoCliente plano, Perlink.Trinks.ClubeDeAssinaturas.Beneficio beneficio, bool ativo, System.DateTime dataDeInclusao, System.DateTime? dataDeDesativacao, Perlink.Trinks.ClubeDeAssinaturas.BeneficioDoPlano beneficioDoPlanoModelo, Perlink.Trinks.ClubeDeAssinaturas.IntencaoEdicaoDoPlanoCliente intencaoEdicaoDoPlanoCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioProduto.
    /// </summary>
    public partial interface IBeneficioProdutoFactory :IBaseFactory {  
    
		BeneficioProduto Create();    
		BeneficioProduto Create(Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioServico.
    /// </summary>
    public partial interface IBeneficioServicoFactory :IBaseFactory {  
    
		BeneficioServico Create();    
		BeneficioServico Create(Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioUsado.
    /// </summary>
    public partial interface IBeneficioUsadoFactory :IBaseFactory {  
    
		BeneficioUsado Create();    
		BeneficioUsado Create(int id, Perlink.Trinks.ClubeDeAssinaturas.BeneficioDaAssinatura beneficioDaAssinatura, System.DateTime dataUtilizacao, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Pessoas.HorarioTransacao horarioTransacao, Perlink.Trinks.Vendas.ItemVenda itemVenda);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContratoDeAdesao.
    /// </summary>
    public partial interface IContratoDeAdesaoFactory :IBaseFactory {  
    
		ContratoDeAdesao Create();    
		ContratoDeAdesao Create(int id, string termosDoContrato, int versao, Perlink.Trinks.ClubeDeAssinaturas.PlanoCliente planoCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDeStatusAssinaturaDoClube.
    /// </summary>
    public partial interface IHistoricoDeStatusAssinaturaDoClubeFactory :IBaseFactory {  
    
		HistoricoDeStatusAssinaturaDoClube Create();    
		HistoricoDeStatusAssinaturaDoClube Create(int id, Perlink.Trinks.ClubeDeAssinaturas.Enums.StatusDaAssinaturaEnum status, Perlink.Trinks.ClubeDeAssinaturas.AssinaturaDoCliente assinaturaDoCliente, System.DateTime dataCriacao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade IntencaoEdicaoDoPlanoCliente.
    /// </summary>
    public partial interface IIntencaoEdicaoDoPlanoClienteFactory :IBaseFactory {  
    
		IntencaoEdicaoDoPlanoCliente Create();    
		IntencaoEdicaoDoPlanoCliente Create(int id, Perlink.Trinks.ClubeDeAssinaturas.DadosDoPlanoCliente dadosDoPlanoCliente, Perlink.Trinks.ClubeDeAssinaturas.PlanoCliente planoCliente, System.Collections.Generic.IList<Perlink.Trinks.ClubeDeAssinaturas.BeneficioDoPlano> beneficiosDoPlano, bool disponivelParaEdicao, System.DateTime dataParaEdicao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDaAssinatura.
    /// </summary>
    public partial interface ILinkDePagamentoDaAssinaturaFactory :IBaseFactory {  
    
		LinkDePagamentoDaAssinatura Create();    
		LinkDePagamentoDaAssinatura Create(int id, int idLinkDePagamento, int idAssinaturaDoCliente, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDoCancelamentoDaAssinatura.
    /// </summary>
    public partial interface ILinkDePagamentoDoCancelamentoDaAssinaturaFactory :IBaseFactory {  
    
		LinkDePagamentoDoCancelamentoDaAssinatura Create();    
		LinkDePagamentoDoCancelamentoDaAssinatura Create(int id, int idLinkDePagamento, int idAssinaturaDoCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoDeAssinatura.
    /// </summary>
    public partial interface IPagamentoDeAssinaturaFactory :IBaseFactory {  
    
		PagamentoDeAssinatura Create();    
		PagamentoDeAssinatura Create(int id, Perlink.Trinks.ClubeDeAssinaturas.AssinaturaDoCliente assinatura, System.DateTime dataPagamento, decimal valorPago, System.DateTime? dataEstorno);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoMultaDeCancelamentoDaAssinatura.
    /// </summary>
    public partial interface IPagamentoMultaDeCancelamentoDaAssinaturaFactory :IBaseFactory {  
    
		PagamentoMultaDeCancelamentoDaAssinatura Create();    
		PagamentoMultaDeCancelamentoDaAssinatura Create(int id, decimal valor, System.DateTime? dataPagamento, System.DateTime dataCriacao, System.DateTime? dataAtualizacao, Perlink.Trinks.ClubeDeAssinaturas.Enums.MetodoCobrancaEnum? metodoDeCobranca);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PlanoCliente.
    /// </summary>
    public partial interface IPlanoClienteFactory :IBaseFactory {  
    
		PlanoCliente Create();    
		PlanoCliente Create(int id, bool ativo, Perlink.Trinks.ClubeDeAssinaturas.DadosDoPlanoCliente dadosDoPlanoCliente, Perlink.Trinks.ClubeDeAssinaturas.PlanoCliente planoClienteModelo, System.Collections.Generic.IList<Perlink.Trinks.ClubeDeAssinaturas.BeneficioDoPlano> beneficiosDoPlano, Perlink.Trinks.ClubeDeAssinaturas.VendaOnline configuracaoDeVendaOnline, System.Collections.Generic.IList<Perlink.Trinks.ClubeDeAssinaturas.ContratoDeAdesao> contratosDeAdesao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VendaOnline.
    /// </summary>
    public partial interface IVendaOnlineFactory :IBaseFactory {  
    
		VendaOnline Create();    
		VendaOnline Create(int id, bool exibirAssinaturaHotsite, bool habilitaVendaHotsite, string termosDeUso);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VigenciaDeAssinatura.
    /// </summary>
    public partial interface IVigenciaDeAssinaturaFactory :IBaseFactory {  
    
		VigenciaDeAssinatura Create();    
		VigenciaDeAssinatura Create(int id, Perlink.Trinks.ClubeDeAssinaturas.AssinaturaDoCliente assinatura, Perlink.Trinks.ClubeDeAssinaturas.PeriodoDatas periodo, Perlink.Trinks.ClubeDeAssinaturas.PagamentoDeAssinatura pagamento, bool foiPago, System.DateTime dataPrevistaPagamento);
			 
    }
 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Calculos.Factories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.DTO.Factories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Enums.Factories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Factories.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Factories {

    /// <summary>
    /// Interface para repositório da entidade AdicionalCobrado.
    /// </summary>
    public partial interface IAdicionalCobradoFactory :IBaseFactory {  
    
		AdicionalCobrado Create();    
		AdicionalCobrado Create(int id, decimal? valorSemDesconto, decimal valorCobrado, Perlink.Trinks.Cobranca.FaturaTrinks fatura, Perlink.Trinks.Cobranca.AdicionalNaAssinatura adicionalDeReferencia, Perlink.Trinks.Cobranca.ValorDoAdicionalPorFaixa valorDeReferencia, Perlink.Trinks.Cobranca.DescontoNoAdicionalDaAssinatura origemDesconto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AdicionalNaAssinatura.
    /// </summary>
    public partial interface IAdicionalNaAssinaturaFactory :IBaseFactory {  
    
		AdicionalNaAssinatura Create();    
		AdicionalNaAssinatura Create(int id, Perlink.Trinks.Cobranca.ServicoTrinks servico, Perlink.Trinks.Cobranca.OfertaDeServicoAdicional ofertaEscolhida, Perlink.Trinks.Cobranca.Assinatura assinatura, bool ativo, System.DateTime dataContratacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueContratou, System.DateTime? dataCancelamento, int? idPessoaQueCancelou, Perlink.Trinks.Cobranca.FormaDeContratacaoDoAdicional formaDeContratacao, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.HistoricoDoAdicionalNaAssinatura> historicos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AgendamentoDeMigracaoDoPlano.
    /// </summary>
    public partial interface IAgendamentoDeMigracaoDoPlanoFactory :IBaseFactory {  
    
		AgendamentoDeMigracaoDoPlano Create();    
		AgendamentoDeMigracaoDoPlano Create(int id, int idPlanoAtual, Perlink.Trinks.Cobranca.PlanoAssinatura novoPlano, System.DateTime dataDaMigracao, System.DateTime dataMinimaDoCadastro, System.DateTime dataMaximaDoCadastro, string motivoDaMigracao, bool ehMigracaoDeFranqueados);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Assinatura.
    /// </summary>
    public partial interface IAssinaturaFactory :IBaseFactory {  
    
		Assinatura Create();    
		Assinatura Create(int idAssinatura, Perlink.Trinks.Cobranca.ContaFinanceira contaFinanceira, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, System.DateTime? dataInicio, System.DateTime? dataFim, System.DateTime? dataFimDeDuracaoDoPlano, System.DateTime? podeCancelarAPartirDe, int diasGratis, bool ativo, int? diaDeVencimento, Perlink.Trinks.Cobranca.FormaPagamento formaPagamento, int? idFormaPagamento, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueCancelou, Perlink.Trinks.Cobranca.Enums.AreaEnum? areaRealizadaCancelamento, string motivoCancelamento, string motivoCancelamentoPipedrive, int numProfissionaisPipedrive, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.DorDoClienteNaAssinatura> dorDoClienteNaAssinatura, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.MotivoDoCancelamentoDaAssinatura> motivoDoCancelamentoDaAssinatura, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.Fatura> faturas, int? quantidadeDeProfissionaisEsperada, bool emailDeRenovacaoSeAproximandoFoiEnviado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDoPlanoAssinatura.
    /// </summary>
    public partial interface IBeneficioDoPlanoAssinaturaFactory :IBaseFactory {  
    
		BeneficioDoPlanoAssinatura Create();    
		BeneficioDoPlanoAssinatura Create(int idBeneficio, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, Perlink.Trinks.Cobranca.ServicoTrinks servico, Perlink.Trinks.Cobranca.OfertaDeServicoAdicional ofertaDeServicoAdicional, int porcentagemDeDesconto, int duracaoBeneficiosEmMeses, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDoPlanoMeuPlano.
    /// </summary>
    public partial interface IBeneficioDoPlanoMeuPlanoFactory :IBaseFactory {  
    
		BeneficioDoPlanoMeuPlano Create();    
		BeneficioDoPlanoMeuPlano Create(int idBeneficio, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, Perlink.Trinks.Cobranca.ServicoTrinks servico, Perlink.Trinks.Cobranca.OfertaDeServicoAdicional ofertaDeServicoAdicional, int porcentagemDeDesconto, int duracaoBeneficiosEmMeses, bool ativo, bool ehExperimentacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaFinanceira.
    /// </summary>
    public partial interface IContaFinanceiraFactory :IBaseFactory {  
    
		ContaFinanceira Create();    
		ContaFinanceira Create(int idContaFinanceira, Perlink.Trinks.Pessoas.Pessoa pessoa, Perlink.Trinks.Cobranca.StatusConta status, System.DateTime? dataPrimeiraAssinatura, System.DateTime? dataCancelamento, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.Assinatura> assinaturas, string guid, System.DateTime? dataDaModificacaoDoStatus, bool ativo, bool ehAPrimeiraAssinatura, bool realizaPagamentoPorFora, bool permiteBoletoFormaDePagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DadosSales.
    /// </summary>
    public partial interface IDadosSalesFactory :IBaseFactory {  
    
		DadosSales Create();    
		DadosSales Create(int idEstabelecimentoDadosSales, int idEstabelecimento, string nomeEstabelecimento, string motivoCancelamento, int numeroDeProfissionais, System.DateTime? dataDeCancelamento, string sistemaEscolhido, string observacoesDoCancelamento, double valorDaMensalidade, string origemDoLead, string midiaDePublicidade, string campanhaDeMarketing, string palavrasChaveDaCampanhaDeMarketing, string resumoDaCampanhaDeMarketing, string dispositivo, string codigoParceria);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoNaAssinatura.
    /// </summary>
    public partial interface IDescontoNaAssinaturaFactory :IBaseFactory {  
    
		DescontoNaAssinatura Create();    
		DescontoNaAssinatura Create(int id, bool ativo, Perlink.Trinks.Cobranca.Assinatura assinatura, int idPessoaContaFinanceira, decimal percentualDoDesconto, int quantidadeDeOcorrenciasProgramadas, int idPessoaQueAtribuiu, System.DateTime dataAtribuicao, string motivoDaAtribuicao, int? idDescontoQueSucedeuEste, bool foiGeradoPorBeneficioDePlano);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoNoAdicionalDaAssinatura.
    /// </summary>
    public partial interface IDescontoNoAdicionalDaAssinaturaFactory :IBaseFactory {  
    
		DescontoNoAdicionalDaAssinatura Create();    
		DescontoNoAdicionalDaAssinatura Create(Perlink.Trinks.Cobranca.AdicionalNaAssinatura adicionalNaAssinatura);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoNoPlanoDaAssinatura.
    /// </summary>
    public partial interface IDescontoNoPlanoDaAssinaturaFactory :IBaseFactory {  
    
		DescontoNoPlanoDaAssinatura Create();    
		DescontoNoPlanoDaAssinatura Create(Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DorDoCliente.
    /// </summary>
    public partial interface IDorDoClienteFactory :IBaseFactory {  
    
		DorDoCliente Create();    
		DorDoCliente Create(int id, string texto, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DorDoClienteNaAssinatura.
    /// </summary>
    public partial interface IDorDoClienteNaAssinaturaFactory :IBaseFactory {  
    
		DorDoClienteNaAssinatura Create();    
		DorDoClienteNaAssinatura Create(int id, Perlink.Trinks.Cobranca.Assinatura assinatura, Perlink.Trinks.Cobranca.DorDoCliente dorDoCliente, Perlink.Trinks.Pessoas.PessoaJuridica pessoa, bool ativo, System.DateTime dataDaAtivacao, System.DateTime? dataDaDesativacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParceriasTrinks.
    /// </summary>
    public partial interface IEstabelecimentoParceriasTrinksFactory :IBaseFactory {  
    
		EstabelecimentoParceriasTrinks Create();    
		EstabelecimentoParceriasTrinks Create(int id, int idEstabelecimento, int idParceria, System.DateTime dataCadastro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ExperimentacaoEstabelecimento.
    /// </summary>
    public partial interface IExperimentacaoEstabelecimentoFactory :IBaseFactory {  
    
		ExperimentacaoEstabelecimento Create();    
		ExperimentacaoEstabelecimento Create(int idExperimentacao, Perlink.Trinks.Cobranca.BeneficioDoPlanoMeuPlano beneficioDoPlanoMeuPlano, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime dataQueEntrouNaExperimentacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Fatura.
    /// </summary>
    public partial interface IFaturaFactory :IBaseFactory {  
    
		Fatura Create();    
		Fatura Create(int idFatura, Perlink.Trinks.Cobranca.Assinatura assinatura, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime? dataEmissao, System.DateTime? inicioPeriodoReferencia, System.DateTime? fimPeriodoReferencia, int? quantMaxProfissionais, decimal? valorCobrado, decimal? valorLiberado, System.DateTime? dataVencimento, System.DateTime? dataParaProcessar, System.DateTime? dataAprovacao, Perlink.Trinks.Cobranca.StatusFatura status, decimal? valorAprovado, long? idTransacaoGateway, Perlink.GatewayPagamento.Enum.GatewayEnum? gateway, string tokenDeRecorrencia, string idTransacao, Perlink.Trinks.Cobranca.FormaPagamento formaPagamento, string numeroCartaoCredito, int? mesValidadeCartaoCredito, int? anoValidadeCartaoCredito, string nomePortadorCartaoCredito, string cpfPortadorCartaoCredito, string numeroNFe, System.DateTime? dataEmissaoNFe, System.DateTime? dataUltimoUploadNFe, System.DateTime? dataLiberacaoCredito, string observacoes, string motivoCancelamento, System.DateTime? dataCancelamento, System.DateTime? dataUltimoProcessamento, System.DateTime? dataDeTolerancia, bool ativo, string motivoTemporarioTransacaoGateway, string statusTemporarioTransacaoGateway, int vezesQueJahInformouPagamentoParaDesbloqueio, int diasToleranciaNaoPagamento, decimal? valorEstornado, bool faturaPodeSerPaga, bool estaPaga);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FaturaMarketing.
    /// </summary>
    public partial interface IFaturaMarketingFactory :IBaseFactory {  
    
		FaturaMarketing Create();    
		FaturaMarketing Create(System.Collections.Generic.IList<Perlink.Trinks.Marketing.MarketingCompraCredito> listaMarketingCompraCredito);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FaturaTrinks.
    /// </summary>
    public partial interface IFaturaTrinksFactory :IBaseFactory {  
    
		FaturaTrinks Create();    
		FaturaTrinks Create(Perlink.Trinks.Cobranca.ValorPorFaixa valorPorFaixa, Perlink.Trinks.Cobranca.PromocaoPraContaFinanceira promocaoPraContaFinanceira, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.AdicionalCobrado> adicionaisCobrados);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FaturaWhatsApp.
    /// </summary>
    public partial interface IFaturaWhatsAppFactory :IBaseFactory {  
    
		FaturaWhatsApp Create();    
		FaturaWhatsApp Create(System.Collections.Generic.IList<Perlink.Trinks.WhatsApp.CompraCredito> listaWhatsAppCompraCredito);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormaDeContratacaoDoAdicional.
    /// </summary>
    public partial interface IFormaDeContratacaoDoAdicionalFactory :IBaseFactory {  
    
		FormaDeContratacaoDoAdicional Create();    
		FormaDeContratacaoDoAdicional Create(int id, string nome, int idServicoAdicional, int? idObjetoAssociado, decimal? valorDeTaxaExtra, string tituloDaTaxaExtra, string resumoDaTaxaExtra, bool exibicaoMeuPlano, bool exibicaoAssinatura, string textoApresentacaoMeuPlano, string textoApresentacaoAssinatura, int hierarquiaContratacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormaPagamento.
    /// </summary>
    public partial interface IFormaPagamentoFactory :IBaseFactory {  
    
		FormaPagamento Create();    
		FormaPagamento Create(int idFormaPagamento, Perlink.Trinks.Cobranca.TipoFormaPagamento tipo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDoAdicionalNaAssinatura.
    /// </summary>
    public partial interface IHistoricoDoAdicionalNaAssinaturaFactory :IBaseFactory {  
    
		HistoricoDoAdicionalNaAssinatura Create();    
		HistoricoDoAdicionalNaAssinatura Create(int id, int idPessoaContaFinanceira, Perlink.Trinks.Cobranca.Assinatura assinatura, Perlink.Trinks.Cobranca.AdicionalNaAssinatura adicionalNaAssinatura, Perlink.Trinks.Cobranca.ServicoTrinks servico, string evento, System.DateTime dataHoraDoEvento, int? idPessoaDoEvento, Perlink.Trinks.Cobranca.ObjectValues.MotivoHistoricoDoAdicional motivo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDoCancelamento.
    /// </summary>
    public partial interface IMotivoDoCancelamentoFactory :IBaseFactory {  
    
		MotivoDoCancelamento Create();    
		MotivoDoCancelamento Create(int id, string texto, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDoCancelamentoDaAssinatura.
    /// </summary>
    public partial interface IMotivoDoCancelamentoDaAssinaturaFactory :IBaseFactory {  
    
		MotivoDoCancelamentoDaAssinatura Create();    
		MotivoDoCancelamentoDaAssinatura Create(int id, Perlink.Trinks.Cobranca.Assinatura assinatura, Perlink.Trinks.Cobranca.MotivoDoCancelamento motivoDoCancelamento, Perlink.Trinks.Pessoas.PessoaJuridica pessoa, bool ativo, System.DateTime dataDaAtivacao, System.DateTime? dataDaDesativacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ObservacaoAssinatura.
    /// </summary>
    public partial interface IObservacaoAssinaturaFactory :IBaseFactory {  
    
		ObservacaoAssinatura Create();    
		ObservacaoAssinatura Create(int idObservacaoAssinatura, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.Pessoa pessoaEvento, System.DateTime dataEvento, string observacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OfertaDeServicoAdicional.
    /// </summary>
    public partial interface IOfertaDeServicoAdicionalFactory :IBaseFactory {  
    
		OfertaDeServicoAdicional Create();    
		OfertaDeServicoAdicional Create(int id, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, Perlink.Trinks.Cobranca.ServicoTrinks servico, Perlink.Trinks.Cobranca.TipoAssociacao tipo, bool ativo, System.DateTime vigenciaAPartirDe, bool possuiControleDeFaixaProprio, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.ValorDoAdicionalPorFaixaDaOferta> valoresPorFaixa, Perlink.Trinks.Cobranca.BeneficiosDaOfertaDeServicoAdicional beneficios);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OfertaDeServicoAdicionalMeuPlano.
    /// </summary>
    public partial interface IOfertaDeServicoAdicionalMeuPlanoFactory :IBaseFactory {  
    
		OfertaDeServicoAdicionalMeuPlano Create();    
		OfertaDeServicoAdicionalMeuPlano Create(int idOferta, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, Perlink.Trinks.Cobranca.ServicoTrinks servico, Perlink.Trinks.Cobranca.Enums.SelecionarOfertaAutomaticamenteNoMeuPlanoEnum selecionarOfertaAutomaticamente, string tituloCTA, string textoCTA, string textoBotaoCTA);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OfertaDeServicoAdicionalMeuPlanoDisponibilidade.
    /// </summary>
    public partial interface IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeFactory :IBaseFactory {  
    
		OfertaDeServicoAdicionalMeuPlanoDisponibilidade Create();    
		OfertaDeServicoAdicionalMeuPlanoDisponibilidade Create(int idOfertaDisponibilidade, Perlink.Trinks.Cobranca.OfertaDeServicoAdicionalMeuPlano ofertaDeServicoAdicional, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime dataInicio, System.DateTime dataFim, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ParceriaTipoTrinks.
    /// </summary>
    public partial interface IParceriaTipoTrinksFactory :IBaseFactory {  
    
		ParceriaTipoTrinks Create();    
		ParceriaTipoTrinks Create(int id, string tipo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ParceriaTrinks.
    /// </summary>
    public partial interface IParceriaTrinksFactory :IBaseFactory {  
    
		ParceriaTrinks Create();    
		ParceriaTrinks Create(int id, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, string cupom, string linkCupom, bool ativo, Perlink.Trinks.Cobranca.PromocaoTrinks promocao, string nome, string email, string banner, string bannerMobile, Perlink.Trinks.PromotoresDoTrinks.PromotorDoTrinks promotorDoTrinks, Perlink.Trinks.Cobranca.ParceriaTipoTrinks tipo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaPagamentoExterno.
    /// </summary>
    public partial interface IPessoaJuridicaPagamentoExternoFactory :IBaseFactory {  
    
		PessoaJuridicaPagamentoExterno Create();    
		PessoaJuridicaPagamentoExterno Create(int id, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, string idExterno);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaPagamentoExternoFatura.
    /// </summary>
    public partial interface IPessoaJuridicaPagamentoExternoFaturaFactory :IBaseFactory {  
    
		PessoaJuridicaPagamentoExternoFatura Create();    
		PessoaJuridicaPagamentoExternoFatura Create(int id, Perlink.Trinks.Cobranca.PessoaJuridicaPagamentoExterno pessoaJuridicaPagamentoExterno, Perlink.Trinks.Cobranca.Fatura fatura, string idExterno);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaPagamentoExternoFaturaHistorico.
    /// </summary>
    public partial interface IPessoaJuridicaPagamentoExternoFaturaHistoricoFactory :IBaseFactory {  
    
		PessoaJuridicaPagamentoExternoFaturaHistorico Create();    
		PessoaJuridicaPagamentoExternoFaturaHistorico Create(int id, Perlink.Trinks.Cobranca.PessoaJuridicaPagamentoExternoFatura pessoaJuridicaPagamentoExternoFatura, string idExterno, System.DateTime dataHora);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PlanoAssinatura.
    /// </summary>
    public partial interface IPlanoAssinaturaFactory :IBaseFactory {  
    
		PlanoAssinatura Create();    
		PlanoAssinatura Create(int idPlano, string nome, string descricao, int diasGratisInicial, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.OfertaDeServicoAdicional> servicosDoPlano, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.ValorPorFaixa> valoresPorFaixa, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.BeneficioDoPlanoAssinatura> beneficios, bool ativo, bool habilitaUsoDeNFSe, bool habilitaUsoDeNFCe, string titulo, string tituloCiclo, int quantidadeDeParcelas, int? duracaoDoPlanoComFidelidade, bool exibirAoPublico, bool possuiDesconto, string observacaoDePagamento, int? idPlanoPai, bool permiteContratarAdicionaisPorFora, string informacoesExtras, Perlink.Trinks.Conteudo.ConteudoTexto detalhesTopoPlano);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoNaAssinatura.
    /// </summary>
    public partial interface IPromocaoNaAssinaturaFactory :IBaseFactory {  
    
		PromocaoNaAssinatura Create();    
		PromocaoNaAssinatura Create(int id, Perlink.Trinks.Cobranca.PromocaoTrinks promocao, Perlink.Trinks.Cobranca.Assinatura assinatura, string nome, System.DateTime dataInicio, System.DateTime dataFim, decimal desconto, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoPraContaFinanceira.
    /// </summary>
    public partial interface IPromocaoPraContaFinanceiraFactory :IBaseFactory {  
    
		PromocaoPraContaFinanceira Create();    
		PromocaoPraContaFinanceira Create(int id, Perlink.Trinks.Cobranca.PromocaoTrinks promocao, Perlink.Trinks.Cobranca.ContaFinanceira contaFinanceira, System.DateTime dataInicio, System.DateTime dataLimiteDeAquisicao, decimal desconto, bool ativo, bool jahAdquirido, int quantasMensalidadesODescontoSerahAplicado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoTrinks.
    /// </summary>
    public partial interface IPromocaoTrinksFactory :IBaseFactory {  
    
		PromocaoTrinks Create();    
		PromocaoTrinks Create(int id, string nome, System.DateTime dataInicio, System.DateTime dataFim, decimal desconto, int mesesDeDuracaoDoDesconto, Perlink.Trinks.Cobranca.Enums.TipoDePromocaoTrinks tipo, int diasVisivelAposOferta, string urlImagemBanner, string urlLinkBanner, bool ativo, int ordem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioAssinatura.
    /// </summary>
    public partial interface IRelatorioAssinaturaFactory :IBaseFactory {  
    
		RelatorioAssinatura Create();    
		RelatorioAssinatura Create(int codigoAssinatura, string responsavelNomeCompleto, string pessoaJuridicaRazaoSocial, string pessoaJuridicaCNPJ, string pessoaJuridicaInscricaoMunicipal, string pessoaJuridicaInscricaoEstadual, string pessoaJuridicaNomeFantasia, string responsavelEmail, System.DateTime? dataValidacaoEmail, string pessoaJuridicaEnderecoTipoLogradouro, string pessoaJuridicaEnderecoUf, string pessoaJuridicaEnderecoBairro, string pessoaJuridicaEnderecoCidade, string pessoaJuridicaEnderecoNumero, string pessoaJuridicaEnderecoComplemento, string pessoaJuridicaEnderecoLogradouro, int diasAdicionaisToleranciaNaoPagamento, int codigoPlanoAssinatura, string nomePlanoAssinatura, System.DateTime responsavelDataCadastro, System.DateTime estabelecimentoDataCadastro, System.DateTime? contaFinanceiraDataAssinatura, System.DateTime dataFimAssinaturaGratis, System.DateTime dataUltimaAlteracaoStatus, System.DateTime? dataEmissaoProximaFatura, System.DateTime? dataLimiteInadiplenteForaTolerancia, System.DateTime? contaFinanceiraDataCancelamento, string contaFinanceiraNomeStatus, int contaFinanceiraCodigoStatus, string apelidoResponsavelFinanceiro, string emailConta, string estabelecimentoEmail, string cpfResponsavel, int idResponsavel, int pessoaJuridicaId, Perlink.Trinks.Pessoas.Pessoa pessoaResponsavel, int? tipoCadastro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioFaturamento.
    /// </summary>
    public partial interface IRelatorioFaturamentoFactory :IBaseFactory {  
    
		RelatorioFaturamento Create();    
		RelatorioFaturamento Create(int id, int assinaturaCodigo, int contaCodigo, string estabelecimentoNomeFantasia, string estabelecimentoRazaoSocial, string estabelecimentoCNPJ, string responsavelFinanceiroNomeCompleto, string responsavelFinanceiroApelido, string responsavelFinanceiroEmail, string responsavelFinanceiroCPF, int codigoPlanoAssinatura, string planoAssinaturaNome, System.DateTime? estabelecimentoDataCadastro, System.DateTime? contaPrimeiraAssinatura, System.DateTime? contaCancelamentoAssinatura, System.DateTime? faturaDataEmissao, System.DateTime? faturaDataVencimento, System.DateTime? faturaPagamentoAprovado, System.DateTime? faturaEmissaoNFE, System.DateTime? faturaUltimoUploadPDFNFE, System.DateTime? faturaLiberacaoDoCredito, System.DateTime? dataUltimoProcessamentoFatura, System.DateTime? dataReprocessamentoFatura, decimal faturaValorLiberadoDoCredito, int faturaCodigo, System.DateTime? faturaDataInicioReferencia, System.DateTime? faturaDataFimReferencia, decimal faturaValorCobrado, string faturaNumeroNotafiscalEletronica, string faturaNomeStatus, int faturaStatusCodigo, int planoAssinaturaLimiteSuperior, int planoAssinaturaLimiteInferior, decimal planoAssinaturaValorFaixa, string faturaCodigoTransacaoAprovada, string faturaTIDAprovada, decimal faturaValorPagamentoAprovado, int faturaIdFormaDePagamento, string faturaFormaDePagamento, string faturaNumeroCartaoDeCredito, int faturaMesValidadeCartao, int faturaAnoValidadeCartao, string faturaCPFPortador, string faturaNomePortador, string faturaMotivoCancelamento, System.DateTime? faturaDataCancelamento, int quantidadeProfissionaisDoEstabelecimento, int estabelecimentoCodigo, string estabelecimentoInscricaoEstadual, string estabelecimentoInscricaoMunicipal, string estabelecimentoEnderecoTipoLogradouro, string estabelecimentoEnderecoUf, string estabelecimentoEnderecoBairro, string estabelecimentoEnderecoCidade, string estabelecimentoEnderecoNumero, string estabelecimentoEnderecoComplemento, string estabelecimentoEnderecoLogradouro, string estabelecimentoEnderecoCEP, string estabelecimentoEmailContato, System.DateTime? responsavelFinanceiroDataValidacaoEmail, string contaStatus, System.DateTime? assinaturaDataFim, System.DateTime? assinaturaDataInicio, int idPessoaResponsavel, string observacao, System.DateTime? dataAgendadaParaReprocessarPagamentoDaFatura, int idPessoa, int assinaturaDiasGratisOficial, int assinaturaDiasGratisCliente, string tipoFatura, decimal? valorEstorno);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ResponsavelAtendimento.
    /// </summary>
    public partial interface IResponsavelAtendimentoFactory :IBaseFactory {  
    
		ResponsavelAtendimento Create();    
		ResponsavelAtendimento Create(int idResponsavel, Perlink.Trinks.Pessoas.Pessoa pessoa, bool ativo, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime? dataAssociacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoTrinks.
    /// </summary>
    public partial interface IServicoTrinksFactory :IBaseFactory {  
    
		ServicoTrinks Create();    
		ServicoTrinks Create(int idServico, string nome, string descricao, Perlink.Trinks.Cobranca.TipoServico tipo, bool ativo, Perlink.Trinks.Cobranca.Enums.ExibicaoNoMeuPlanoEnum exibicaoNoMeuPlano, bool exibicaoNoFluxoAssinatura, string textoDeApresentacaoNoMeuPlano, string textoDeApresentacaoNoFluxoDeAssinatura, string caminhoRelativoParaTelaDeConfiguracao, string caminhoRelativoParaPopUpDeConfiguracao, bool exibePrecoAoPublico, Perlink.Trinks.Cobranca.FormaDeContratacaoDoAdicional formaContratacaoPadrao, int ordemDeExibicaoParaContratacao, string textoDaConfiguracaoAutomatica, int? idRecurso, string observacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SolicitacaoCancelamentoDaAssinatura.
    /// </summary>
    public partial interface ISolicitacaoCancelamentoDaAssinaturaFactory :IBaseFactory {  
    
		SolicitacaoCancelamentoDaAssinatura Create();    
		SolicitacaoCancelamentoDaAssinatura Create(int idSolicitacaoCancelamentoDeConta, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.Conta contaQueMostrouInteresseEmCancelar, System.DateTime? dataQueMostrouInteresseEmCancelar, Perlink.Trinks.Pessoas.Conta contaQueSolicitouOCancelamento, System.DateTime? dataQueSolicitouOCancelamento, Perlink.Trinks.Cobranca.Enums.StatusDaSolicitacaoDeCancelamentoDaAssinaturaEnum statusDaSolicitacao, Perlink.Trinks.Cobranca.Assinatura assinatura, System.DateTime? dataQueOClienteFoiRevertido, System.DateTime? dataQueOClienteFoiCancelado, System.DateTime? dataQueASolicitacaoFoiEncerrada, Perlink.Trinks.Pessoas.Conta contaResponsavelPeloAtendimento, string motivoReversao, int? tempoDesconto, decimal? porcentagemDesconto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade StatusConta.
    /// </summary>
    public partial interface IStatusContaFactory :IBaseFactory {  
    
		StatusConta Create();    
		StatusConta Create(int idStatus, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade StatusFatura.
    /// </summary>
    public partial interface IStatusFaturaFactory :IBaseFactory {  
    
		StatusFatura Create();    
		StatusFatura Create(int idStatus, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoAssociacao.
    /// </summary>
    public partial interface ITipoAssociacaoFactory :IBaseFactory {  
    
		TipoAssociacao Create();    
		TipoAssociacao Create(int idTipoAssociacao, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoFormaPagamento.
    /// </summary>
    public partial interface ITipoFormaPagamentoFactory :IBaseFactory {  
    
		TipoFormaPagamento Create();    
		TipoFormaPagamento Create(int idTipoFormaPagamento, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoServico.
    /// </summary>
    public partial interface ITipoServicoFactory :IBaseFactory {  
    
		TipoServico Create();    
		TipoServico Create(int idTipoAssociacao, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeAdesaoDoAdicionalPorFaixa.
    /// </summary>
    public partial interface IValorDeAdesaoDoAdicionalPorFaixaFactory :IBaseFactory {  
    
		ValorDeAdesaoDoAdicionalPorFaixa Create();    
		ValorDeAdesaoDoAdicionalPorFaixa Create(int idAdesaoAdicionalPorFaixa, decimal valor, int minimoDeProfissionais, int maximoDeProfissionais, string termoApresentado, Perlink.Trinks.Cobranca.ServicoTrinks servico);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura.
    /// </summary>
    public partial interface IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaFactory :IBaseFactory {  
    
		ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura Create();    
		ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura Create(Perlink.Trinks.Cobranca.ValorDoAdicionalPorFaixaDaOferta valorDoAdicionalPorFaixaDaOferta, Perlink.Trinks.Cobranca.AdicionalNaAssinatura adicionalNaAssinatura);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDoAdicionalPorFaixa.
    /// </summary>
    public partial interface IValorDoAdicionalPorFaixaFactory :IBaseFactory {  
    
		ValorDoAdicionalPorFaixa Create();    
		ValorDoAdicionalPorFaixa Create(int id, decimal valor, int maximoDeProfissionais, int minimoDeProfissionais, bool ehLimite, Perlink.Trinks.Cobranca.ServicoTrinks servico, Perlink.Trinks.Cobranca.FormaDeContratacaoDoAdicional formaDeContratacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDoAdicionalPorFaixaDaOferta.
    /// </summary>
    public partial interface IValorDoAdicionalPorFaixaDaOfertaFactory :IBaseFactory {  
    
		ValorDoAdicionalPorFaixaDaOferta Create();    
		ValorDoAdicionalPorFaixaDaOferta Create(int? idOfertaDeAdicional, int? idFormaContratacao, Perlink.Trinks.Cobranca.ValorPorFaixa faixaDoPlano, Perlink.Trinks.Cobranca.OfertaDeServicoAdicional ofertaDeAdicional);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorPorFaixa.
    /// </summary>
    public partial interface IValorPorFaixaFactory :IBaseFactory {  
    
		ValorPorFaixa Create();    
		ValorPorFaixa Create(int idFaixa, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinatura, int? limiteInferior, int? limiteSuperior, decimal? valor, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.Cobranca.ConfiguracoesDosAdicionais.Factories {

 
}
namespace Perlink.Trinks.Cobranca.ContratacaoDosAdicionais.Factories {

 
}
namespace Perlink.Trinks.Cobranca.DTO.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Enums.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Exceptions.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Extensions.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Factories.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Filtros.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Helpers.Factories {

 
}
namespace Perlink.Trinks.Cobranca.ObjectValues.Factories {

 
}
namespace Perlink.Trinks.Cobranca.Stories.Factories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.DTO.Factories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.Enum.Factories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.Stories.Factories {

 
}
namespace Perlink.Trinks.Compromissos.DTO.Factories {

 
}
namespace Perlink.Trinks.Compromissos.Filtros.Factories {

 
}
namespace Perlink.Trinks.ComunidadeTrinks.Factories {

    /// <summary>
    /// Interface para repositório da entidade ArtigoDeNovidade.
    /// </summary>
    public partial interface IArtigoDeNovidadeFactory :IBaseFactory {  
    
		ArtigoDeNovidade Create();    
		ArtigoDeNovidade Create(int id, string titulo, string conteudo, System.DateTime dataHoraPublicacao, string nomeAutor, string urlFotoAutor, Perlink.Trinks.ComunidadeTrinks.StatusArtigoDePublicacao status, string textoBotaoPrimario, string linkBotaoPrimario, string textoBotaoSecundario, string linkBotaoSecundario, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueFezUltimaAlteracao, System.DateTime dataHoraUltimaModificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Sugestao.
    /// </summary>
    public partial interface ISugestaoFactory :IBaseFactory {  
    
		Sugestao Create();    
		Sugestao Create(int id, string texto, int? idConta, int? idPessoa, int? idEstabelecimento, System.DateTime dataHoraRegistro, Perlink.Trinks.ComunidadeTrinks.OrigemRegistroSugestao origemRegistroSugestao, string tituloDoConteudoOrigem, int? idArtigoDeNovidade, int? idTopicoDeVotacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TopicoDeVotacao.
    /// </summary>
    public partial interface ITopicoDeVotacaoFactory :IBaseFactory {  
    
		TopicoDeVotacao Create();    
		TopicoDeVotacao Create(int id, string proposta, Perlink.Trinks.ComunidadeTrinks.SitucaoTopicoDeVotacao situacao, string urlArtigoNovidade, System.DateTime dataCadastro, System.DateTime? dataAbertura, System.DateTime? dataFechamento, System.DateTime? dataImplementacao, int totalDeVotos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VotacaoDeSugestao.
    /// </summary>
    public partial interface IVotacaoDeSugestaoFactory :IBaseFactory {  
    
		VotacaoDeSugestao Create();    
		VotacaoDeSugestao Create(int id, int idSugestao, int idTopicoDeVotacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Voto.
    /// </summary>
    public partial interface IVotoFactory :IBaseFactory {  
    
		Voto Create();    
		Voto Create(int id, Perlink.Trinks.ComunidadeTrinks.TopicoDeVotacao topicoDeVotacao, System.DateTime dataHoraVotacao, int idConta, int idPessoaDaConta, int? idEstabelecimento);
			 
    }
 
}
namespace Perlink.Trinks.ComunidadeTrinks.DTO.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Factories {

    /// <summary>
    /// Interface para repositório da entidade ContaFinanceiraDoEstabelecimento.
    /// </summary>
    public partial interface IContaFinanceiraDoEstabelecimentoFactory :IBaseFactory {  
    
		ContaFinanceiraDoEstabelecimento Create();    
		ContaFinanceiraDoEstabelecimento Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, string nome, bool ativo, System.DateTime dataCadastro, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueCadastrou, int? idContaFinanceiraPadrao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaFinanceiraPadrao.
    /// </summary>
    public partial interface IContaFinanceiraPadraoFactory :IBaseFactory {  
    
		ContaFinanceiraPadrao Create();    
		ContaFinanceiraPadrao Create(int id, string nome, System.DateTime dataCriacao, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.ConciliacaoBancaria.DTO.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Enums.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Exportadores.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Filtros.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Dtos.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Dtos.V2.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Interfaces.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.PagarMe.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Siclos.Factories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Stone.Factories {

 
}
namespace Perlink.Trinks.ContaDigital.Factories {

    /// <summary>
    /// Interface para repositório da entidade AutenticacaoContaDigital.
    /// </summary>
    public partial interface IAutenticacaoContaDigitalFactory :IBaseFactory {  
    
		AutenticacaoContaDigital Create();    
		AutenticacaoContaDigital Create(int id, string telefone, bool podeTrocarTelefone, System.DateTime? dataHoraConfirmacao, System.DateTime? dataHoraValidade, System.Collections.Generic.IList<Perlink.Trinks.ContaDigital.AutenticacaoContaDigitalEnvio> envios, int? quantidadeTentativas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoContaDigitalConfirmacao.
    /// </summary>
    public partial interface IAutenticacaoContaDigitalConfirmacaoFactory :IBaseFactory {  
    
		AutenticacaoContaDigitalConfirmacao Create();    
		AutenticacaoContaDigitalConfirmacao Create(int id, int idAutenticacaoContaDigital, int idAutenticacaoContaDigitalEnvio, string contato, System.DateTime dataConfirmacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoContaDigitalEnvio.
    /// </summary>
    public partial interface IAutenticacaoContaDigitalEnvioFactory :IBaseFactory {  
    
		AutenticacaoContaDigitalEnvio Create();    
		AutenticacaoContaDigitalEnvio Create(int id, Perlink.Trinks.ContaDigital.AutenticacaoContaDigital autenticacaoContaDigital, string codigoConfirmacao, System.DateTime dataHoraEnvio, System.DateTime dataHoraExpiracao, System.DateTime? dataHoraConfirmacao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoIdentidadePreCadastro.
    /// </summary>
    public partial interface IAutenticacaoIdentidadePreCadastroFactory :IBaseFactory {  
    
		AutenticacaoIdentidadePreCadastro Create();    
		AutenticacaoIdentidadePreCadastro Create(int idAutenticacaoContaDigital, int idEstabelecimento, int idPessoa);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoIdentidadeUsuarioConta.
    /// </summary>
    public partial interface IAutenticacaoIdentidadeUsuarioContaFactory :IBaseFactory {  
    
		AutenticacaoIdentidadeUsuarioConta Create();    
		AutenticacaoIdentidadeUsuarioConta Create(int idAutenticacaoContaDigital, int idUsuarioContaDigital);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CategoriaPermissaoContaDigital.
    /// </summary>
    public partial interface ICategoriaPermissaoContaDigitalFactory :IBaseFactory {  
    
		CategoriaPermissaoContaDigital Create();    
		CategoriaPermissaoContaDigital Create(int id, string nome, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ChavePix.
    /// </summary>
    public partial interface IChavePixFactory :IBaseFactory {  
    
		ChavePix Create();    
		ChavePix Create(int id, long? idExterno, string chave, Perlink.Trinks.ContaDigital.Enums.TipoChavePixEnum tipo, Perlink.Trinks.ContaDigital.Enums.StatusChavePixEnum status, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ChavePixContaDigital.
    /// </summary>
    public partial interface IChavePixContaDigitalFactory :IBaseFactory {  
    
		ChavePixContaDigital Create();    
		ChavePixContaDigital Create(int id, int idChavePix, int idContaDigital, int idUsuarioQueAlterou);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ChavePixProfissional.
    /// </summary>
    public partial interface IChavePixProfissionalFactory :IBaseFactory {  
    
		ChavePixProfissional Create();    
		ChavePixProfissional Create(int id, int idEstabelecimentoProfissional, int idChavePix, int? idUsuarioQueAlterou, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracoesContaDigital.
    /// </summary>
    public partial interface IConfiguracoesContaDigitalFactory :IBaseFactory {  
    
		ConfiguracoesContaDigital Create();    
		ConfiguracoesContaDigital Create(int id, int idContaDigital, bool primeiroAcessoRealizado, System.DateTime? dataUltimaAtualizacao, int? idUsuarioQueAlterou);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancariaDigital.
    /// </summary>
    public partial interface IContaBancariaDigitalFactory :IBaseFactory {  
    
		ContaBancariaDigital Create();    
		ContaBancariaDigital Create(int id, string idExterno, string numeroConta, string agencia, Perlink.Trinks.ContaDigital.Enums.CategoriaContaDigitalEnum tipoConta, Perlink.Trinks.ContaDigital.Instituicao instituicao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaEstabelecimento.
    /// </summary>
    public partial interface IContaEstabelecimentoFactory :IBaseFactory {  
    
		ContaEstabelecimento Create();    
		ContaEstabelecimento Create(int id, int idEstabelecimento, int idContaDigital, bool ativo, System.DateTime dataCriacao, System.DateTime? dataAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaUsuarioDigital.
    /// </summary>
    public partial interface IContaUsuarioDigitalFactory :IBaseFactory {  
    
		ContaUsuarioDigital Create();    
		ContaUsuarioDigital Create(int id, int idExterno, Perlink.Trinks.ContaDigital.Dono dono, Perlink.Trinks.ContaDigital.ContaBancariaDigital contaBancaria, Perlink.Trinks.ContaDigital.Enums.TipoContaDigitalEnum tipoConta, Perlink.Trinks.ContaDigital.Enums.StatusContaDigitalEnum status, bool ativo, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, System.Collections.Generic.IList<Perlink.Trinks.ContaDigital.UsuarioContaDigital> usuariosContaDigital);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Dono.
    /// </summary>
    public partial interface IDonoFactory :IBaseFactory {  
    
		Dono Create();    
		Dono Create(int id, string nome, System.DateTime? dateDeAniversario, Perlink.Trinks.ContaDigital.Endereco endereco, Perlink.Trinks.ContaDigital.VO.DocumentoVO documento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DuvidaFrequente.
    /// </summary>
    public partial interface IDuvidaFrequenteFactory :IBaseFactory {  
    
		DuvidaFrequente Create();    
		DuvidaFrequente Create(int id, string pergunta, string resposta, int ordemExibicao, bool ativo, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EtapaCadastro.
    /// </summary>
    public partial interface IEtapaCadastroFactory :IBaseFactory {  
    
		EtapaCadastro Create();    
		EtapaCadastro Create(int id, int idEstabelecimento, Perlink.Trinks.ContaDigital.Enums.EtapaCadastroEnum etapaAtual, string telefoneValidacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LimiteContaDigital.
    /// </summary>
    public partial interface ILimiteContaDigitalFactory :IBaseFactory {  
    
		LimiteContaDigital Create();    
		LimiteContaDigital Create(int id, Perlink.Trinks.ContaDigital.LimitePlano limitePlano, Perlink.Trinks.ContaDigital.ContaUsuarioDigital contaDigital);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LimitePlano.
    /// </summary>
    public partial interface ILimitePlanoFactory :IBaseFactory {  
    
		LimitePlano Create();    
		LimitePlano Create(int id, int idExterno);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LOGAprovacaoTransferencias.
    /// </summary>
    public partial interface ILOGAprovacaoTransferenciasFactory :IBaseFactory {  
    
		LOGAprovacaoTransferencias Create();    
		LOGAprovacaoTransferencias Create(int id, int idPagamentoAgendado, int idPessoaQueAprovou, System.DateTime dataTransferencia, string detalhes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Operador.
    /// </summary>
    public partial interface IOperadorFactory :IBaseFactory {  
    
		Operador Create();    
		Operador Create(int idOperador, System.Collections.Generic.IList<Perlink.Trinks.ContaDigital.PermissaoOperador> permissoes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAgendado.
    /// </summary>
    public partial interface IPagamentoAgendadoFactory :IBaseFactory {  
    
		PagamentoAgendado Create();    
		PagamentoAgendado Create(int id, int idContaUsuarioDigital, Perlink.Trinks.ContaDigital.Enums.StatusPagamentoAgendadoEnum status, System.DateTime dataAgendamento, System.DateTime dataCriacao, int? idTransferencia, decimal valor, int? idUsuarioQueAlterou, int? idPessoaQueCriou);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAgendadoFolhaMesProfissional.
    /// </summary>
    public partial interface IPagamentoAgendadoFolhaMesProfissionalFactory :IBaseFactory {  
    
		PagamentoAgendadoFolhaMesProfissional Create();    
		PagamentoAgendadoFolhaMesProfissional Create(int id, int idFolhaMesProfissional, int idPagamentoAgendado, int? idEstabelecimentoProfissional);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PermissaoContaDigital.
    /// </summary>
    public partial interface IPermissaoContaDigitalFactory :IBaseFactory {  
    
		PermissaoContaDigital Create();    
		PermissaoContaDigital Create(string nome, string descicao, Perlink.Trinks.ContaDigital.CategoriaPermissaoContaDigital categoria);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PermissaoOperador.
    /// </summary>
    public partial interface IPermissaoOperadorFactory :IBaseFactory {  
    
		PermissaoOperador Create();    
		PermissaoOperador Create(int id, Perlink.Trinks.ContaDigital.Operador operador, string permissao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Responsavel.
    /// </summary>
    public partial interface IResponsavelFactory :IBaseFactory {  
    
		Responsavel Create();    
		Responsavel Create(int idResponsavel, System.DateTime? dataDeAniversario, Perlink.Trinks.ContaDigital.VO.DocumentoVO documento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Transferencia.
    /// </summary>
    public partial interface ITransferenciaFactory :IBaseFactory {  
    
		Transferencia Create();    
		Transferencia Create(int id, int idContaDigitalOrigem, long idTransferenciaGateway, decimal valor, System.DateTime dataCriacao, System.DateTime? dataAtualizacao, Perlink.Trinks.ContaDigital.Enums.TipoTransferenciaEnum tipo, Perlink.Trinks.ContaDigital.Enums.StatusTransferenciaEnum status, Perlink.Trinks.ContaDigital.DestinatarioTransferencia destinatario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioContaDigital.
    /// </summary>
    public partial interface IUsuarioContaDigitalFactory :IBaseFactory {  
    
		UsuarioContaDigital Create();    
		UsuarioContaDigital Create(int id, Perlink.Trinks.ContaDigital.ContaUsuarioDigital contaDigital, int idExternoTrinks, int? idUsuarioQueAlterou, string nome, string email, string numeroTelefone, bool ativo, System.DateTime? dataInativacao, System.DateTime? dataValidadeTokenSms, System.DateTime? dataBloqueioValidade);
			 
    }
 
}
namespace Perlink.Trinks.ContaDigital.Builders.Factories {

 
}
namespace Perlink.Trinks.ContaDigital.Factories {

 
}
namespace Perlink.Trinks.ContaDigital.DTO.LogDTO.Factories {

 
}
namespace Perlink.Trinks.ContaDigital.Enums.Factories {

 
}
namespace Perlink.Trinks.ContaDigital.Providers.Factories {

 
}
namespace Perlink.Trinks.ContaDigital.VO.Factories {

 
}
namespace Perlink.Trinks.Conteudo.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConteudoImagemLogoEstabelecimento.
    /// </summary>
    public partial interface IConteudoImagemLogoEstabelecimentoFactory :IBaseFactory {  
    
		ConteudoImagemLogoEstabelecimento Create();    
		ConteudoImagemLogoEstabelecimento Create(int idEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConteudoTexto.
    /// </summary>
    public partial interface IConteudoTextoFactory :IBaseFactory {  
    
		ConteudoTexto Create();    
		ConteudoTexto Create(int id, string conteudo, Perlink.Trinks.Conteudo.TipoConteudoEnum tipoConteudo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MenuItem.
    /// </summary>
    public partial interface IMenuItemFactory :IBaseFactory {  
    
		MenuItem Create();    
		MenuItem Create(string id, string texto, string urlLink, System.DateTime dataLimiteNotificacaoNovoItem, string notificacaoTexto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MenuOpcaoAliasBusca.
    /// </summary>
    public partial interface IMenuOpcaoAliasBuscaFactory :IBaseFactory {  
    
		MenuOpcaoAliasBusca Create();    
		MenuOpcaoAliasBusca Create(string id, Perlink.Trinks.Conteudo.MenuOpcaoBusca menuOpcaoBusca, string alias);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MenuOpcaoBusca.
    /// </summary>
    public partial interface IMenuOpcaoBuscaFactory :IBaseFactory {  
    
		MenuOpcaoBusca Create();    
		MenuOpcaoBusca Create(int id, string nome, string icone, string link, int ordem);
			 
    }
 
}
namespace Perlink.Trinks.Conteudo.DTO.Factories {

 
}
namespace Perlink.Trinks.Controle.Factories {

    /// <summary>
    /// Interface para repositório da entidade PalavraProibida.
    /// </summary>
    public partial interface IPalavraProibidaFactory :IBaseFactory {  
    
		PalavraProibida Create();    
		PalavraProibida Create(long id, string palavra);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PalavraProibidaSMS.
    /// </summary>
    public partial interface IPalavraProibidaSMSFactory :IBaseFactory {  
    
		PalavraProibidaSMS Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade PalavraProibidaTrinks.
    /// </summary>
    public partial interface IPalavraProibidaTrinksFactory :IBaseFactory {  
    
		PalavraProibidaTrinks Create();    
 
    }
 
}
namespace Perlink.Trinks.ControleDeCTAs.Factories {

    /// <summary>
    /// Interface para repositório da entidade CTA.
    /// </summary>
    public partial interface ICTAFactory :IBaseFactory {  
    
		CTA Create();    
		CTA Create(int idCTA, string titulo, string descricao, string textoBotaoPrimario, string uRLDestinoPrimaria, Perlink.Trinks.ControleDeCTAs.Enums.CTAAcoesEnum acaoPrimaria, string textoBotaoSecundario, string uRLDestinoSecundaria, Perlink.Trinks.ControleDeCTAs.Enums.CTAAcoesEnum acaoSecundaria, string tagMetricaRegistro, string imagem, Perlink.Trinks.ControleDeCTAs.Enums.CTAVisibilidadeEnum visibilidade, Perlink.Trinks.ControleDeCTAs.Enums.CTAModelosEnum idModelo, Perlink.Trinks.ControleDeCTAs.CTAGrupo grupo, int? idRecurso, string contexto, int ordem, string tipoGeradorParametrosCTA, Perlink.Trinks.ControleDeCTAs.Enums.CTAPermissaoEnum permissao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CTAGrupo.
    /// </summary>
    public partial interface ICTAGrupoFactory :IBaseFactory {  
    
		CTAGrupo Create();    
		CTAGrupo Create(int idGrupo, string nome, int periodoCurtoParaEsconderOsCTAS, int periodoLongoParaEsconderOsCTAS);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CTAInformacoesAdicionaisTrinksPro.
    /// </summary>
    public partial interface ICTAInformacoesAdicionaisTrinksProFactory :IBaseFactory {  
    
		CTAInformacoesAdicionaisTrinksPro Create();    
		CTAInformacoesAdicionaisTrinksPro Create(int idCtaInformacoesAdicionais, string tituloDoWebviewDaAcaoPrincipal, string tituloDoWebviewDaAcaoSecundaria, Perlink.Trinks.TrinksApps.Enums.TipoEventoEnum tipoEventoPrimario, Perlink.Trinks.TrinksApps.Enums.TipoEventoEnum tipoEventoSecundario, Perlink.Trinks.TrinksApps.Enums.TipoEventoEnum tipoEventoFechar, Perlink.Trinks.ControleDeCTAs.Enums.AbrirURLsUtilizandoEnum abrirUrlPrimariaUtilizando, Perlink.Trinks.ControleDeCTAs.Enums.AbrirURLsUtilizandoEnum abrirUrlSecundariaUtilizando, Perlink.Trinks.ControleDeCTAs.CTA cTA);
			 
    }
 
}
namespace Perlink.Trinks.ControleDeCTAs.DTOs.Factories {

 
}
namespace Perlink.Trinks.ControleDeCTAs.Enums.Factories {

 
}
namespace Perlink.Trinks.ControleDeCTAs.Geradores.Factories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.DTO.Factories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Enum.Factories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Filtros.Factories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Factories {

    /// <summary>
    /// Interface para repositório da entidade LancamentoAporte.
    /// </summary>
    public partial interface ILancamentoAporteFactory :IBaseFactory {  
    
		LancamentoAporte Create();    
		LancamentoAporte Create(int idAporte, decimal valor, System.DateTime dataDePagamento, System.DateTime dataDeCriacao, string descricao, string observacao, System.DateTime? dataDoEstorno, Perlink.Trinks.Pessoas.Pessoa pessoaQueEstornou, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoDeReceitaCategoria.
    /// </summary>
    public partial interface ILancamentoDeReceitaCategoriaFactory :IBaseFactory {  
    
		LancamentoDeReceitaCategoria Create();    
		LancamentoDeReceitaCategoria Create(int idLancamentoCategoria, string nome, string urlDeLancamentoDeReceita, Perlink.Trinks.Enums.AreasTrinksEnum area, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.ControleDeFotos.Factories {

    /// <summary>
    /// Interface para repositório da entidade ArquivoDeImagem.
    /// </summary>
    public partial interface IArquivoDeImagemFactory :IBaseFactory {  
    
		ArquivoDeImagem Create();    
		ArquivoDeImagem Create(int id, int idPessoa, Perlink.Trinks.ControleDeFotos.Enums.TipoDeFoto tipoDeFoto, int? referenciaDoTipoDeFoto, string nomeDoArquivoComExtensao, System.DateTime dataDoUpload);
			 
    }
 
}
namespace Perlink.Trinks.ControleDeFotos.Controladores.Factories {

 
}
namespace Perlink.Trinks.ControleDeFotos.DTO.Factories {

 
}
namespace Perlink.Trinks.ControleDeFotos.Enums.Factories {

 
}
namespace Perlink.Trinks.ControleDeFotos.Factories.Factories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Factories {

    /// <summary>
    /// Interface para repositório da entidade DisponibilidadeEspecifica.
    /// </summary>
    public partial interface IDisponibilidadeEspecificaFactory :IBaseFactory {  
    
		DisponibilidadeEspecifica Create();    
		DisponibilidadeEspecifica Create(int id, int idRecurso, bool disponivel, Perlink.Trinks.ControleDeFuncionalidades.Enums.MotivoDaIndisponibilidadeEnum? motivoDaIndisponibilidade, int? idEstabelecimento, int? idFranquia, System.DateTime? dataCadastroMinima, System.DateTime? dataCadastroMaxima, Perlink.Trinks.Estabelecimentos.Enums.FaixaProfissionaisEstabelecimentoEnum? faixaProfissionaisEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DisponibilidadeGeral.
    /// </summary>
    public partial interface IDisponibilidadeGeralFactory :IBaseFactory {  
    
		DisponibilidadeGeral Create();    
		DisponibilidadeGeral Create(int id, int idRecurso, bool disponivel, Perlink.Trinks.ControleDeFuncionalidades.Enums.MotivoDaIndisponibilidadeEnum? motivoDaIndisponibilidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreferenciasDaConta.
    /// </summary>
    public partial interface IPreferenciasDaContaFactory :IBaseFactory {  
    
		PreferenciasDaConta Create();    
		PreferenciasDaConta Create(int id, string chave, int idConta, string valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeConfiguracaoEspecifica.
    /// </summary>
    public partial interface IValorDeConfiguracaoEspecificaFactory :IBaseFactory {  
    
		ValorDeConfiguracaoEspecifica Create();    
		ValorDeConfiguracaoEspecifica Create(int id, string nome, int? idEstabelecimento, int? idFranquia, string valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeConfiguracaoGeral.
    /// </summary>
    public partial interface IValorDeConfiguracaoGeralFactory :IBaseFactory {  
    
		ValorDeConfiguracaoGeral Create();    
		ValorDeConfiguracaoGeral Create(string nome, string valor);
			 
    }
 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.DTO.Factories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Enums.Factories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Filtros.Factories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor.Factories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Stories.Factories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Factories {

    /// <summary>
    /// Interface para repositório da entidade AvaliacaoDeSatisfacao.
    /// </summary>
    public partial interface IAvaliacaoDeSatisfacaoFactory :IBaseFactory {  
    
		AvaliacaoDeSatisfacao Create();    
		AvaliacaoDeSatisfacao Create(int id, int idDono, Perlink.Trinks.Pessoas.PessoaFisica pessoaAvaliador, Perlink.Trinks.ControleDeSatisfacao.Enums.StatusDaAvaliacaoEnum status, Perlink.Trinks.ControleDeSatisfacao.Enums.CanalDeComunicacaoEnum canalDeComunicacao, int contextoDaAvaliacao, Perlink.Trinks.ControleDeSatisfacao.ItemParaAvaliar itemQueOriginouEstaAvaliacao, System.DateTime dataHoraQueFoiCriado, System.DateTime? dataHoraEnvioDaPergunta, string textoDaPergunta, System.DateTime? dataHoraResposta, int? nota, string comentario, System.Collections.Generic.IList<Perlink.Trinks.ControleDeSatisfacao.ItemParaAvaliar> itensParaAvaliar, System.Collections.Generic.IList<Perlink.Trinks.ControleDeSatisfacao.Contato> contatos, int quantidadeDeSMSsEnviados, int quantidadeDeSMSsRecebidos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AvaliacaoDeSatisfacaoRecebidaRetentativa.
    /// </summary>
    public partial interface IAvaliacaoDeSatisfacaoRecebidaRetentativaFactory :IBaseFactory {  
    
		AvaliacaoDeSatisfacaoRecebidaRetentativa Create();    
		AvaliacaoDeSatisfacaoRecebidaRetentativa Create(int id, Perlink.Trinks.ControleDeSatisfacao.AvaliacaoDeSatisfacao avaliacaoDeSatisfacao, string mensagem, System.DateTime dataHoraResposta, System.DateTime dataHoraQueFoiCriado, System.DateTime? dataHoraQueFoiFeitaNovaTentativa);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Contato.
    /// </summary>
    public partial interface IContatoFactory :IBaseFactory {  
    
		Contato Create();    
		Contato Create(int id, Perlink.Trinks.ControleDeSatisfacao.AvaliacaoDeSatisfacao avaliacaoDeSatisfacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContatoCelular.
    /// </summary>
    public partial interface IContatoCelularFactory :IBaseFactory {  
    
		ContatoCelular Create();    
		ContatoCelular Create(string dDD, string numero);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemParaAvaliar.
    /// </summary>
    public partial interface IItemParaAvaliarFactory :IBaseFactory {  
    
		ItemParaAvaliar Create();    
		ItemParaAvaliar Create(int id, Perlink.Trinks.ControleDeSatisfacao.AvaliacaoDeSatisfacao avaliacaoDeSatisfacao, int idDono, int idDoObjetoParaAvaliar, int tipoDoObjetoParaAvaliar, int contextoDaAvaliacao, string descricaoDoObjeto, int? nota, string comentario);
			 
    }
 
}
namespace Perlink.Trinks.ControleDeSatisfacao.DTO.Factories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Enums.Factories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Factories.Factories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Filtros.Factories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Stories.Factories {

 
}
namespace Perlink.Trinks.Correios.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConsultaCep.
    /// </summary>
    public partial interface IConsultaCepFactory :IBaseFactory {  
    
		ConsultaCep Create();    
		ConsultaCep Create(string uF, string cidade, int? bairro, string tipoLogradouro, string logradouro, string cEP, string nome);
			 
    }
 
}
namespace Perlink.Trinks.Cupom.Factories {

    /// <summary>
    /// Interface para repositório da entidade CupomBase.
    /// </summary>
    public partial interface ICupomBaseFactory :IBaseFactory {  
    
		CupomBase Create();    
		CupomBase Create(int id, string codigo, string descricao, string nome, Perlink.Trinks.Cupom.Intervalo validade, Perlink.Trinks.Cupom.Intervalo resgate, bool ativo, System.DateTime? dataInativacao, int qtdUsoCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomDesconto.
    /// </summary>
    public partial interface ICupomDescontoFactory :IBaseFactory {  
    
		CupomDesconto Create();    
		CupomDesconto Create(int idCupomDesconto, Perlink.Trinks.Pessoas.Enums.FormaPagamentoTipoEnum? metodoPagamento, int? descontoMaximo, int? gastoMinimo, int desconto, bool aliquota, bool descontoComissao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomEstabelecimento.
    /// </summary>
    public partial interface ICupomEstabelecimentoFactory :IBaseFactory {  
    
		CupomEstabelecimento Create();    
		CupomEstabelecimento Create(int idCupomEstabelecimento, int idEstabelecimento, Perlink.Trinks.Cupom.Enums.ContextoUso contextoUso);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomEstabelecimentoProduto.
    /// </summary>
    public partial interface ICupomEstabelecimentoProdutoFactory :IBaseFactory {  
    
		CupomEstabelecimentoProduto Create();    
		CupomEstabelecimentoProduto Create(int idCupomEstabelecimentoProduto, int idCupomBase, int? idEstabelecimentoProduto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomHorarioTransacao.
    /// </summary>
    public partial interface ICupomHorarioTransacaoFactory :IBaseFactory {  
    
		CupomHorarioTransacao Create();    
		CupomHorarioTransacao Create(int idCupomHorarioTransacao, int idCupom, int idHorarioTransacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomItemVenda.
    /// </summary>
    public partial interface ICupomItemVendaFactory :IBaseFactory {  
    
		CupomItemVenda Create();    
		CupomItemVenda Create(int idCupomItemVenda, int idCupom, int idItemVenda);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomPessoaFisica.
    /// </summary>
    public partial interface ICupomPessoaFisicaFactory :IBaseFactory {  
    
		CupomPessoaFisica Create();    
		CupomPessoaFisica Create(int id, int idCupom, int idEstabelecimento, int idPessoa, int? idHorario, int? idTransacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomServicoEstabelecimento.
    /// </summary>
    public partial interface ICupomServicoEstabelecimentoFactory :IBaseFactory {  
    
		CupomServicoEstabelecimento Create();    
		CupomServicoEstabelecimento Create(int idCupomServicoEstabelecimento, int idCupomBase, int? idServicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CupomUsoPessoaFisica.
    /// </summary>
    public partial interface ICupomUsoPessoaFisicaFactory :IBaseFactory {  
    
		CupomUsoPessoaFisica Create();    
		CupomUsoPessoaFisica Create(int id, int idCupom, int idPessoa, int quantidade, bool associadoAutomaticamente);
			 
    }
 
}
namespace Perlink.Trinks.Cupom.DTO.Factories {

 
}
namespace Perlink.Trinks.Cupom.Enums.Factories {

 
}
namespace Perlink.Trinks.Cupom.Filters.Factories {

 
}
namespace Perlink.Trinks.Cupom.Models.Factories {

 
}
namespace Perlink.Trinks.Dashboard.DTOs.Factories {

 
}
namespace Perlink.Trinks.Dashboard.Factories {

 
}
namespace Perlink.Trinks.Factories {

 
}
namespace Perlink.Trinks.DataQuery.DTO.Factories {

 
}
namespace Perlink.Trinks.DataQuery.Factories {

 
}
namespace Perlink.Trinks.DataQuery.Strategies.Factories {

 
}
namespace Perlink.Trinks.DebitoParcial.Factories {

    /// <summary>
    /// Interface para repositório da entidade AbatimentoDeDivida.
    /// </summary>
    public partial interface IAbatimentoDeDividaFactory :IBaseFactory {  
    
		AbatimentoDeDivida Create();    
		AbatimentoDeDivida Create(int id, Perlink.Trinks.DebitoParcial.PagamentoDeDividaPeloCliente pagamentoDeDivida, Perlink.Trinks.DebitoParcial.DividaDeixadaNoEstabelecimento dividaDeixadaNoEstabelecimento, decimal valorAbatido, decimal valorFantanteAposAbatido);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DividaDeixadaNoEstabelecimento.
    /// </summary>
    public partial interface IDividaDeixadaNoEstabelecimentoFactory :IBaseFactory {  
    
		DividaDeixadaNoEstabelecimento Create();    
		DividaDeixadaNoEstabelecimento Create(int id, int idPessoaDoEstabelecimento, int idPessoaDoCliente, int idTransacao, int idTransacaoFormaPagamento, int idPessoaQueRegistrou, System.DateTime dataQueDeixouDivida, decimal valorInicialDaDivida, decimal valorTotalPago, decimal valorTotalFaltante, Perlink.Trinks.DebitoParcial.StatusDaDivida status, bool estaPendenteDePagamento, System.Collections.Generic.IList<Perlink.Trinks.DebitoParcial.HistoricoDaDivida> historicos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDaDivida.
    /// </summary>
    public partial interface IHistoricoDaDividaFactory :IBaseFactory {  
    
		HistoricoDaDivida Create();    
		HistoricoDaDivida Create(int id, Perlink.Trinks.DebitoParcial.DividaDeixadaNoEstabelecimento dividaDeixada, Perlink.Trinks.DebitoParcial.HistoricoTipoEvento tipoEvento, System.DateTime dataEvento, int idPessoaQueRegistrou, string observacao, decimal valorMovimentado, decimal valorAposMovimentacao, System.DateTime dataQueDeixouDivida);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoDeDividaPeloCliente.
    /// </summary>
    public partial interface IPagamentoDeDividaPeloClienteFactory :IBaseFactory {  
    
		PagamentoDeDividaPeloCliente Create();    
		PagamentoDeDividaPeloCliente Create(int id, int idPessoaDoCliente, int idPessoaDoEstabelecimento, int idTransacao, int idTransacaoFormaPagamento, System.DateTime dataDoPagamento, decimal valorPago, decimal valorFantanteAposPagamento, Perlink.Trinks.DebitoParcial.StatusDoPagamentoDeDivida status, System.Collections.Generic.IList<Perlink.Trinks.DebitoParcial.AbatimentoDeDivida> abatimentos);
			 
    }
 
}
namespace Perlink.Trinks.DebitoParcial.DTO.Factories {

 
}
namespace Perlink.Trinks.DebitoParcial.Filtros.Factories {

 
}
namespace Perlink.Trinks.DebitoParcial.Stories.Factories {

 
}
namespace Perlink.Trinks.Despesas.Factories {

    /// <summary>
    /// Interface para repositório da entidade LancamentosRecorrentesSelecionadas.
    /// </summary>
    public partial interface ILancamentosRecorrentesSelecionadasFactory :IBaseFactory {  
    
		LancamentosRecorrentesSelecionadas Create();    
		LancamentosRecorrentesSelecionadas Create(int id, Perlink.Trinks.Despesas.RenovacaoDeLancamentos idRenovacaoDeLancamentos, int idLancamentoRecorrente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Lancamento.
    /// </summary>
    public partial interface ILancamentoFactory :IBaseFactory {  
    
		Lancamento Create();    
		Lancamento Create(int idLancamento, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Despesas.LancamentoCategoria lancamentoCategoria, string descricao, decimal valor, Perlink.Trinks.Despesas.LancamentoStatusPagamento status, System.DateTime dataVencimento, System.DateTime? dataCompetencia, System.DateTime? dataPagamento, Perlink.Trinks.Financeiro.FormaPagamento formaPagamento, Perlink.Trinks.Pessoas.Pessoa pessoaQueRecebeuOuPagou, Perlink.Trinks.Despesas.LancamentoRecorrencia lancamentoRecorrencia, System.DateTime dataCriacao, Perlink.Trinks.Pessoas.Pessoa pessoaQueCriou, System.DateTime dataUltimaAtualizacao, Perlink.Trinks.Pessoas.Pessoa pessoaQueAlterouPorUltimo, bool ativo, bool registradoEmLote, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Financeiro.ValorDeComissaoAReceber valorDeComissaoAReceber, bool registradoEmImportacao, bool pessoaQueRecebeuOuPagouPessoaFisicaAtivo, bool pessoaQueRecebeuOuPagouPessoaJuridicaAtivo, bool existePessoaJuridica, bool existePessoaFisica, string nomeProfissionalComStatus, string nomeFornecedorComStatus, string dataVencimentoDiaMes, string dataVencimentoDiaDaSemana, string valorEmDecimal, Perlink.Trinks.ConciliacaoBancaria.ContaFinanceiraDoEstabelecimento contaFinanceira);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoCategoria.
    /// </summary>
    public partial interface ILancamentoCategoriaFactory :IBaseFactory {  
    
		LancamentoCategoria Create();    
		LancamentoCategoria Create(int idLancamentoCategoria, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Despesas.LancamentoTipo lancamentoTipo, Perlink.Trinks.Despesas.LancamentoCategoriaPadrao lancamentoCategoriaPadrao, Perlink.Trinks.Despesas.LancamentoGrupo lancamentoGrupo, string nome, int nivelMinimoAcesso, bool necessarioInformarProfissional, bool necessarioInformarFornecedor, bool ativo, bool permiteLancamentoManual, bool permiteEdicao, bool permiteExclusao, bool ehDespesaPersonalizadaComCategoriaPadrao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoCategoriaPadrao.
    /// </summary>
    public partial interface ILancamentoCategoriaPadraoFactory :IBaseFactory {  
    
		LancamentoCategoriaPadrao Create();    
		LancamentoCategoriaPadrao Create(int idLancamentoCategoria, Perlink.Trinks.Despesas.LancamentoTipo lancamentoTipo, Perlink.Trinks.Despesas.LancamentoGrupoPadrao lancamentoGrupoPadrao, string nome, Perlink.Trinks.Pessoas.Enums.AcessoBackoffice nivelMinimoAcesso, bool necessarioInformarProfissional, bool necessarioInformarFornecedor, bool ativo, bool criarDuranteCadastroEstabelecimento, int? utilizarAsMesmasRegrasDaCategoriaId);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoGeradoPorMovimentacaoEstoque.
    /// </summary>
    public partial interface ILancamentoGeradoPorMovimentacaoEstoqueFactory :IBaseFactory {  
    
		LancamentoGeradoPorMovimentacaoEstoque Create();    
		LancamentoGeradoPorMovimentacaoEstoque Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Despesas.Lancamento lancamento, Perlink.Trinks.Despesas.LancamentoRecorrencia lancamentoRecorrencia, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoMovimentacaoEstoque);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoGrupo.
    /// </summary>
    public partial interface ILancamentoGrupoFactory :IBaseFactory {  
    
		LancamentoGrupo Create();    
		LancamentoGrupo Create(int idLancamentoGrupo, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int? idLancamentoGrupoPadrao, string nomeGrupoCategoria, Perlink.Trinks.Despesas.Enums.LancamentoGrupoTipoGrupoEnum tipoGrupo, bool ativo, string aliasExibicaoGrafico, string corExibicaoGrafico, int versao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoGrupoPadrao.
    /// </summary>
    public partial interface ILancamentoGrupoPadraoFactory :IBaseFactory {  
    
		LancamentoGrupoPadrao Create();    
		LancamentoGrupoPadrao Create(int idLancamentoGrupoPadrao, string nomeGrupoCategoria, bool ativo, Perlink.Trinks.Despesas.Enums.LancamentoGrupoTipoGrupoEnum tipoGrupo, string aliasExibicaoGrafico, string corExibicaoGrafico, int versao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoRecorrencia.
    /// </summary>
    public partial interface ILancamentoRecorrenciaFactory :IBaseFactory {  
    
		LancamentoRecorrencia Create();    
		LancamentoRecorrencia Create(int idLancamentoRecorrencia, Perlink.Trinks.Despesas.LancamentoRecorrenciaTipo lancamentoRecorrenciaTipo, System.DateTime inicioRecorrencia, System.DateTime fimRecorrencia, System.Collections.Generic.IList<Perlink.Trinks.Despesas.Lancamento> lancamentos, bool ativo, int numeroDeParcelas, Perlink.Trinks.ConciliacaoBancaria.Enums.FormaDeAplicarCompetenciaNasParcelasEnum? formaDeAplicarDataDeCompetenciaNasParcelas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoRecorrenciaTipo.
    /// </summary>
    public partial interface ILancamentoRecorrenciaTipoFactory :IBaseFactory {  
    
		LancamentoRecorrenciaTipo Create();    
		LancamentoRecorrenciaTipo Create(int idLancamentoRecorrenciaTipo, string nome, int diasFrequencia, int duracaoMaxima, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoStatusPagamento.
    /// </summary>
    public partial interface ILancamentoStatusPagamentoFactory :IBaseFactory {  
    
		LancamentoStatusPagamento Create();    
		LancamentoStatusPagamento Create(int idLancamentoStatusPagamento, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoTipo.
    /// </summary>
    public partial interface ILancamentoTipoFactory :IBaseFactory {  
    
		LancamentoTipo Create();    
		LancamentoTipo Create(int idLancamentoTipo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RenovacaoDeLancamentos.
    /// </summary>
    public partial interface IRenovacaoDeLancamentosFactory :IBaseFactory {  
    
		RenovacaoDeLancamentos Create();    
		RenovacaoDeLancamentos Create(int id, int idEstabelecimento, System.DateTime dataHoraInicio, System.DateTime dataHoraFim, int idPessoaQueRenovou, System.Collections.Generic.IList<Perlink.Trinks.Despesas.LancamentosRecorrentesSelecionadas> lancamentosRecorrentesSelecionadas);
			 
    }
 
}
namespace Perlink.Trinks.Despesas.DTO.Factories {

 
}
namespace Perlink.Trinks.Despesas.Enums.Factories {

 
}
namespace Perlink.Trinks.Despesas.Factories.Factories {

 
}
namespace Perlink.Trinks.Disponibilidade.Adapters.Factories {

 
}
namespace Perlink.Trinks.Disponibilidade.Factories {

 
}
namespace Perlink.Trinks.Disponibilidade.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.Dispositivos.Enums.Factories {

 
}
namespace Perlink.Trinks.Dispositivos.Factories {

    /// <summary>
    /// Interface para repositório da entidade TipoImpressao.
    /// </summary>
    public partial interface ITipoImpressaoFactory :IBaseFactory {  
    
		TipoImpressao Create();    
		TipoImpressao Create(int idTipoImpressao, string nomeTipoImpressao, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.DTO.Factories {

 
}
namespace Perlink.Trinks.DTO.Factories {

    /// <summary>
    /// Interface para repositório da entidade HorarioFuturosExportacao.
    /// </summary>
    public partial interface IHorarioFuturosExportacaoFactory :IBaseFactory {  
    
		HorarioFuturosExportacao Create();    
		HorarioFuturosExportacao Create(int idHorario, int idEstabelecimento, string nomeEstabelecimento, System.DateTime dataHora, int idProfissional, string nomeProfissional, int idServicoEstabelecimento, string nomeServico, int duracao, int idCliente, int idEstabelecimentoCliente, string clienteNome, string telefones, decimal valor, string status, string observacao, string observacaoCliente, string hotSite, bool foiPago, System.DateTime dataCadastro);
			 
    }
 
}
namespace Perlink.Trinks.DTO.PushSNS.Factories {

 
}
namespace Perlink.Trinks.Encurtador.DTO.Factories {

 
}
namespace Perlink.Trinks.Encurtador.Factories {

    /// <summary>
    /// Interface para repositório da entidade EncurtadorDeDados.
    /// </summary>
    public partial interface IEncurtadorDeDadosFactory :IBaseFactory {  
    
		EncurtadorDeDados Create();    
		EncurtadorDeDados Create(int id, int idCodigo, string codigo, string dado, string chaveDeSeguranca, Perlink.Trinks.Encurtador.Enums.TipoEncurtadorDeDadosEnum tipo, System.DateTime dataValidade);
			 
    }
 
}
namespace Perlink.Trinks.Encurtador.Enums.Factories {

 
}
namespace Perlink.Trinks.Encurtador.Factories.Factories {

 
}
namespace Perlink.Trinks.Enums.Factories {

 
}
namespace Perlink.Trinks.Env.Clock.Factories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Antifraude.Factories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Enums.Factories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Exceptions.Factories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Factories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Strategies.Factories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Factories {

    /// <summary>
    /// Interface para repositório da entidade AvaliacaoEstabelecimento.
    /// </summary>
    public partial interface IAvaliacaoEstabelecimentoFactory :IBaseFactory {  
    
		AvaliacaoEstabelecimento Create();    
		AvaliacaoEstabelecimento Create(int id, int idEstabelecimento, int idConta, int avaliacao, int? idHorario, System.DateTime dataHoraCadastro, System.DateTime dataHoraUltimaAtualizacao, string textoAvaliacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDeAvaliacaoDeSatisfacao.
    /// </summary>
    public partial interface IConfiguracaoDeAvaliacaoDeSatisfacaoFactory :IBaseFactory {  
    
		ConfiguracaoDeAvaliacaoDeSatisfacao Create();    
		ConfiguracaoDeAvaliacaoDeSatisfacao Create(int idConfiguracao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, bool permiteQueClientesAvaliemOServico, System.Collections.Generic.IList<Perlink.Trinks.Estabelecimentos.ItemConfiguradoParaSerAvaliado> itensParaAvaliar);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoEstabelecimento.
    /// </summary>
    public partial interface IConfiguracaoEstabelecimentoFactory :IBaseFactory {  
    
		ConfiguracaoEstabelecimento Create();    
		ConfiguracaoEstabelecimento Create(int id, int idEstabelecimento, string chave, string valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFavorito.
    /// </summary>
    public partial interface IEstabelecimentoFavoritoFactory :IBaseFactory {  
    
		EstabelecimentoFavorito Create();    
		EstabelecimentoFavorito Create(int id, int idEstabelecimento, int idContaQueFavoritou, int idPessoaQueFavoritou, bool ehFavorito, System.DateTime dataHora);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalFavorito.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalFavoritoFactory :IBaseFactory {  
    
		EstabelecimentoProfissionalFavorito Create();    
		EstabelecimentoProfissionalFavorito Create(int id, int idEstabelecimento, int idEstabelecimentoProfissional, int idContaQueFavoritou, int idPessoaQueFavoritou, bool ehFavorito);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoUUID.
    /// </summary>
    public partial interface IEstabelecimentoUUIDFactory :IBaseFactory {  
    
		EstabelecimentoUUID Create();    
		EstabelecimentoUUID Create(int id, int idEstabelecimento, System.Guid uUID);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemConfiguradoParaSerAvaliado.
    /// </summary>
    public partial interface IItemConfiguradoParaSerAvaliadoFactory :IBaseFactory {  
    
		ItemConfiguradoParaSerAvaliado Create();    
		ItemConfiguradoParaSerAvaliado Create(int id, Perlink.Trinks.Estabelecimentos.ConfiguracaoDeAvaliacaoDeSatisfacao configuracaoDeAvaliacao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, bool permiteAvaliar);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoConfiguradoParaSerAvaliado.
    /// </summary>
    public partial interface IServicoConfiguradoParaSerAvaliadoFactory :IBaseFactory {  
    
		ServicoConfiguradoParaSerAvaliado Create();    
		ServicoConfiguradoParaSerAvaliado Create(Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento);
			 
    }
 
}
namespace Perlink.Trinks.Estabelecimentos.DTO.Factories {

 
}
namespace Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoComInformacoesConsolidadas.
    /// </summary>
    public partial interface IEstabelecimentoComInformacoesConsolidadasFactory :IBaseFactory {  
    
		EstabelecimentoComInformacoesConsolidadas Create();    
		EstabelecimentoComInformacoesConsolidadas Create(int id, string nome, string nomeParaBusca, int numeroTotalAvaliacoes, int mediaAvaliacoes, int? idPessoaFotoPrincipal, int quantidadeAgendamentos, int? idEstabelecimentoModelo, string urlHotsite, Perlink.Trinks.Estabelecimentos.ElasticSearch.EnderecoProjetado endereco, Perlink.Trinks.Estabelecimentos.ElasticSearch.ConfiguracaoProjetado configuracoes, System.Collections.Generic.IList<Perlink.Trinks.Estabelecimentos.ElasticSearch.ServicoProjetado> servicos, string urlRelativoLogo, string urlAbsolutaLogo, int? idFranquia, bool aceitaPagamentoOnline);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade InformacoesConsolidadasDaBuscaDoPortal.
    /// </summary>
    public partial interface IInformacoesConsolidadasDaBuscaDoPortalFactory :IBaseFactory {  
    
		InformacoesConsolidadasDaBuscaDoPortal Create();    
		InformacoesConsolidadasDaBuscaDoPortal Create(string id, int? idEstabelecimento, int? idServico, string nomeDoEstabelecimento, string nomeDoEstabelecimentoParaBusca, string nomeDoServico, string nomeDoServicoParaBusca, int tipoResultado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OpcaoDeAutocompletarDoPortal.
    /// </summary>
    public partial interface IOpcaoDeAutocompletarDoPortalFactory :IBaseFactory {  
    
		OpcaoDeAutocompletarDoPortal Create();    
		OpcaoDeAutocompletarDoPortal Create(string id, string nomeDoServicoOuEstabelecimento, int tipoResultado);
			 
    }
 
}
namespace Perlink.Trinks.Estabelecimentos.Enums.Factories {

 
}
namespace Perlink.Trinks.Estabelecimentos.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Factories.Factories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Filtros.Factories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Interfaces.Factories {

 
}
namespace Perlink.Trinks.Estatistica.Factories {

    /// <summary>
    /// Interface para repositório da entidade BIUsoDoSistema.
    /// </summary>
    public partial interface IBIUsoDoSistemaFactory :IBaseFactory {  
    
		BIUsoDoSistema Create();    
		BIUsoDoSistema Create(int idBIUsoDoSistema, System.DateTime dataReferencia, System.DateTime dataRegistro, int quantidadeEstabelecimentoCriaramAgendamentoPeloBackofficeNoDia, int quantidadeEstabelecimentoReceberamAgendamentoDoCliente, int quantidadeEstabelecimentoCadastrados, int quantidadeEstabelecimentoCadastradosAparecemNoPortal, int quantidadeEstabelecimentoCadastradosNoDia, int quantidadeEstabelecimentoFecharamContaNoDia, int quantidadeAgendamentosCriadosNoDia, int quantidadeAgendamentosCriadosPeloWebNoDia, int quantidadeAgendamentosCriadosPeloMobileNoDia, int quantidadeSMSEnviadosNoDia, int quantidadeUsuariosWebAtivos, int quantidadeUsuariosBalcaoAtivos, int quantidadeUsuariosWebCadastradosNoDia, int quantidadeUsuariosBalcaoCadastradosNoDia, decimal? somatorioFaturasPagasNoPeriodo, decimal? somatorioFaturasPagasMarketingNoPeriodo, int quantidadeHorariosCriadosAtravesSiteDaFranquiaNoDia, int? quantidadeEstabelecimentosAssinadosNoDia, int? quantidadeEstabelecimentosCanceladosNoDia, int? quantidadeEstabelecimentosComPrimeiraFaturaPagaNoDia, int quantidadeAgendamentosCriadosPeloMobileRedeNoDia, int quantidadeAgendamentosCadastradosGoogleReserve);
			 
    }
 
}
namespace Perlink.Trinks.EstilosVisuais.Enums.Factories {

 
}
namespace Perlink.Trinks.EstilosVisuais.Factories {

    /// <summary>
    /// Interface para repositório da entidade TemaCss.
    /// </summary>
    public partial interface ITemaCssFactory :IBaseFactory {  
    
		TemaCss Create();    
		TemaCss Create(int id, int? idEstabelecimento, int? idFranquia, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TemaCssBackoffice.
    /// </summary>
    public partial interface ITemaCssBackofficeFactory :IBaseFactory {  
    
		TemaCssBackoffice Create();    
		TemaCssBackoffice Create(string corPrimaria, string logo, int logoLargura);
			 
    }
 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Calculos.Factories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDoServico.
    /// </summary>
    public partial interface IConfiguracaoDoServicoFactory :IBaseFactory {  
    
		ConfiguracaoDoServico Create();    
		ConfiguracaoDoServico Create(int id, int idEstabelecimento, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, System.DateTime? dataHoraUltimaAlteracao, int? idServicoConfiguracaoModelo, System.DateTime? dataHoraUltimaSincroniaModelo, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoEnum fonteDeCustoParaDescontoUsoDeProdutos, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoParaClienteEnum fonteDeCustoParaDescontoUsoDeProdutosParaCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoParaBaixaAutomatica.
    /// </summary>
    public partial interface IConfiguracaoParaBaixaAutomaticaFactory :IBaseFactory {  
    
		ConfiguracaoParaBaixaAutomatica Create();    
		ConfiguracaoParaBaixaAutomatica Create(int idConfiguracao, int idEstabelecimento, bool podeEditarNaEdicaoDoAgendamento, bool podeEditarNoFechamentoDeConta, bool podeEditarNoPAT, bool podeEditarNaAgendaPorCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario.
    /// </summary>
    public partial interface IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioFactory :IBaseFactory {  
    
		HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario Create();    
		HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario Create(int id, Perlink.Trinks.EstoqueComBaixaAutomatica.UsoDeProdutoNoHorario usoDeProdutoNoHorario, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoUltimaMovimentacaoEstoque);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDeUsoDeProdutoNoHorario.
    /// </summary>
    public partial interface IHistoricoDeUsoDeProdutoNoHorarioFactory :IBaseFactory {  
    
		HistoricoDeUsoDeProdutoNoHorario Create();    
		HistoricoDeUsoDeProdutoNoHorario Create(int id, bool ativo, Perlink.Trinks.EstoqueComBaixaAutomatica.StatusFinalizacaoUsoDescartavel statusFinalizacaoDoUso, Perlink.Trinks.EstoqueComBaixaAutomatica.UsoDeProdutoNoHorario usoDeProdutoNoHorario, Perlink.Trinks.Pessoas.PessoaFisica responsavelPelaAlteracao, System.DateTime dataAlteracao, Perlink.Trinks.Pessoas.Horario horario, Perlink.Trinks.Pessoas.HorarioHistorico horarioHistorico, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, int quantidade, Perlink.Trinks.Pessoas.Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade, Perlink.Trinks.Pessoas.UnidadeMedida unidadeMedidaDoProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomatica itemConfiguradoParaBaixaAutomatica, bool profissionalPagaPeloProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoEnum? fonteDeCustoDoProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoParaClienteEnum? fonteDeCustoDoProdutoParaCliente, decimal custoDoUsoDoProdutoParaOProfissional, decimal custoDoUsoDoProdutoParaOCliente, decimal custoMedioDoProduto, decimal precoRevendaProfissionalDoProduto, decimal valorPorMedidaDoProduto, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoUltimaMovimentacaoEstoque);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemConfiguradoParaBaixaAutomatica.
    /// </summary>
    public partial interface IItemConfiguradoParaBaixaAutomaticaFactory :IBaseFactory {  
    
		ItemConfiguradoParaBaixaAutomatica Create();    
		ItemConfiguradoParaBaixaAutomatica Create(int id, int idEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, int quantidade, Perlink.Trinks.Pessoas.Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, bool profissionalPagaPeloProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoEnum? fonteDeCustoDoProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoParaClienteEnum? fonteDeCustoDoProdutoParaCliente, bool ativo, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueCriou, System.DateTime dataHoraCriacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueFezUltimaAlteracao, System.DateTime? dataHoraUltimaAlteracao, int? idItemConfiguradoParaBaixaAutomaticaModelo, System.DateTime? dataHoraUltimaSincroniaModelo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsoDeProdutoNoHorario.
    /// </summary>
    public partial interface IUsoDeProdutoNoHorarioFactory :IBaseFactory {  
    
		UsoDeProdutoNoHorario Create();    
		UsoDeProdutoNoHorario Create(int id, bool ativo, Perlink.Trinks.EstoqueComBaixaAutomatica.StatusFinalizacaoUsoDescartavel statusFinalizacaoDoUso, Perlink.Trinks.Pessoas.Horario horario, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, int quantidade, Perlink.Trinks.Pessoas.Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade, bool profissionalPagaPeloProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoEnum? fonteDeCustoDoProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.FonteDeCustoDoProdutoParaClienteEnum? fonteDeCustoDoProdutoParaCliente, decimal custoDoUsoDoProdutoParaOProfissional, decimal custoDoUsoDoProdutoParaOCliente, decimal custoMedioDoProduto, decimal precoRevendaProfissionalDoProduto, decimal valorPorMedidaDoProduto, Perlink.Trinks.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomatica itemConfiguradoParaBaixaAutomatica, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoUltimaMovimentacaoEstoque, System.Collections.Generic.IList<Perlink.Trinks.EstoqueComBaixaAutomatica.HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario> historicoDeMovimentacoes, System.Collections.Generic.IList<Perlink.Trinks.EstoqueComBaixaAutomatica.HistoricoDeUsoDeProdutoNoHorario> historicoDeUsoDeProdutoNoHorario);
			 
    }
 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.DTO.Factories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.Factories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.Factories {

 
}
namespace Perlink.Trinks.Exceptions.Factories {

 
}
namespace Perlink.Trinks.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.Extratores.DTO.Factories {

 
}
namespace Perlink.Trinks.Extratores.Factories {

    /// <summary>
    /// Interface para repositório da entidade Extrator.
    /// </summary>
    public partial interface IExtratorFactory :IBaseFactory {  
    
		Extrator Create();    
		Extrator Create(int id, bool ativo, Perlink.Trinks.Extratores.Repositories.TipoDeSaidaDeExtrator tipo, string query, System.DateTime? ultimaExecucao, string colunasDoCabecalho);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Visao.
    /// </summary>
    public partial interface IVisaoFactory :IBaseFactory {  
    
		Visao Create();    
		Visao Create(int id, Perlink.Trinks.Extratores.Extrator extrator, string queryBuscaChavesDeFiltro, string tipoQueryBuscaChavesDeFiltro, string queryBuscaContatos, string tipoQueryBuscaContatos, string assuntoDoEmail, string corpoDoEmail);
			 
    }
 
}
namespace Perlink.Trinks.Facebook.DTOs.Factories {

 
}
namespace Perlink.Trinks.Facebook.Enums.Factories {

 
}
namespace Perlink.Trinks.Facebook.Factories {

    /// <summary>
    /// Interface para repositório da entidade FBE.
    /// </summary>
    public partial interface IFBEFactory :IBaseFactory {  
    
		FBE Create();    
		FBE Create(int idEstabelecimento, string adAccountId, string businessManagerId, string catalogId, Perlink.Trinks.Facebook.Enums.FbeEventEnum fbeEvent, string flow, System.DateTime installTime, string pixelId, string accessToken, bool onsiteEligible, string tokenType);
			 
    }
 
}
namespace Perlink.Trinks.FAQ.Factories {

    /// <summary>
    /// Interface para repositório da entidade Assunto.
    /// </summary>
    public partial interface IAssuntoFactory :IBaseFactory {  
    
		Assunto Create();    
		Assunto Create(int idAssunto, string nome, string descricao, System.Collections.Generic.IList<Perlink.Trinks.FAQ.TelaAssunto> listaTelaAssunto, System.Collections.Generic.IList<Perlink.Trinks.FAQ.AssuntoPerguntaResposta> listaAssuntoPerguntaResposta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AssuntoPerguntaResposta.
    /// </summary>
    public partial interface IAssuntoPerguntaRespostaFactory :IBaseFactory {  
    
		AssuntoPerguntaResposta Create();    
		AssuntoPerguntaResposta Create(int idAssuntoPergunta, Perlink.Trinks.FAQ.Assunto assunto, Perlink.Trinks.FAQ.PerguntaResposta perguntaResposta, int ordemExibicaoDaPerguntaNoAssunto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PerguntaResposta.
    /// </summary>
    public partial interface IPerguntaRespostaFactory :IBaseFactory {  
    
		PerguntaResposta Create();    
		PerguntaResposta Create(int idPergunta, string pergunta, string resposta, bool exibeNoPortal, bool exibeNoBackoffice, System.Collections.Generic.IList<Perlink.Trinks.FAQ.AssuntoPerguntaResposta> listaAssuntoPerguntaResposta, System.Collections.Generic.IList<Perlink.Trinks.FAQ.TelaPerguntaResposta> listaTelaPerguntaResposta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Tela.
    /// </summary>
    public partial interface ITelaFactory :IBaseFactory {  
    
		Tela Create();    
		Tela Create(int idTela, string url, string titulo, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TelaAssunto.
    /// </summary>
    public partial interface ITelaAssuntoFactory :IBaseFactory {  
    
		TelaAssunto Create();    
		TelaAssunto Create(int idTelaAssunto, Perlink.Trinks.FAQ.Assunto assunto, Perlink.Trinks.FAQ.Tela tela);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TelaPerguntaResposta.
    /// </summary>
    public partial interface ITelaPerguntaRespostaFactory :IBaseFactory {  
    
		TelaPerguntaResposta Create();    
		TelaPerguntaResposta Create(int idTelaPerguntaResposta, Perlink.Trinks.FAQ.PerguntaResposta perguntaResposta, Perlink.Trinks.FAQ.Tela tela);
			 
    }
 
}
namespace Perlink.Trinks.Fidelidade.Factories {

    /// <summary>
    /// Interface para repositório da entidade AgendamentoOnlineQueGerouPontos.
    /// </summary>
    public partial interface IAgendamentoOnlineQueGerouPontosFactory :IBaseFactory {  
    
		AgendamentoOnlineQueGerouPontos Create();    
		AgendamentoOnlineQueGerouPontos Create(int id, Perlink.Trinks.Pessoas.Horario horario, int idTransacao, System.DateTime dataInicio, int ultimosPontosDeFidelidadeGerados, int pontosDeFidelidade, bool usouPontosDeFidelidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontos.
    /// </summary>
    public partial interface IMovimentacaoDePontosFactory :IBaseFactory {  
    
		MovimentacaoDePontos Create();    
		MovimentacaoDePontos Create(int id, int? idTransacao, int idPessoaDoEstabelecimento, Perlink.Trinks.Fidelidade.PontoGanho pontoGanhoQueAMovimentacaoGerou, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaDoCliente, int quantidadeDePontos, System.DateTime dataMovimentacao, System.DateTime horaMovimentacao, System.DateTime? dataValidade, Perlink.Trinks.Fidelidade.Enums.OperacaoDaMovimentacaoDePontosEnum tipoDeOperacaoDeMovimentacao, string descricaoDoItem, bool foiEstornado, Perlink.Trinks.Fidelidade.MovimentacaoDePontos movimentacaoQueEstornouEsta, Perlink.Trinks.Fidelidade.Enums.TipoOrigemMovimentacaoDePontosEnum tipoOrigem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosAgendamentoOnline.
    /// </summary>
    public partial interface IMovimentacaoDePontosAgendamentoOnlineFactory :IBaseFactory {  
    
		MovimentacaoDePontosAgendamentoOnline Create();    
		MovimentacaoDePontosAgendamentoOnline Create(Perlink.Trinks.Fidelidade.AgendamentoOnlineQueGerouPontos agendamentoOnline);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosAvulso.
    /// </summary>
    public partial interface IMovimentacaoDePontosAvulsoFactory :IBaseFactory {  
    
		MovimentacaoDePontosAvulso Create();    
		MovimentacaoDePontosAvulso Create(Perlink.Trinks.Pessoas.PessoaFisica responsavelPorMovimentacaoDePontoAvulso, string motivo, Perlink.Trinks.Fidelidade.MovimentacaoDePontos movimentacaoQueFoiCancelada);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosHorarioTransacao.
    /// </summary>
    public partial interface IMovimentacaoDePontosHorarioTransacaoFactory :IBaseFactory {  
    
		MovimentacaoDePontosHorarioTransacao Create();    
		MovimentacaoDePontosHorarioTransacao Create(Perlink.Trinks.Pessoas.HorarioTransacao horarioTransacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosItemVenda.
    /// </summary>
    public partial interface IMovimentacaoDePontosItemVendaFactory :IBaseFactory {  
    
		MovimentacaoDePontosItemVenda Create();    
		MovimentacaoDePontosItemVenda Create(Perlink.Trinks.Vendas.ItemVenda itemVenda);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosPagamentoAntecipado.
    /// </summary>
    public partial interface IMovimentacaoDePontosPagamentoAntecipadoFactory :IBaseFactory {  
    
		MovimentacaoDePontosPagamentoAntecipado Create();    
		MovimentacaoDePontosPagamentoAntecipado Create(Perlink.Trinks.Fidelidade.PagamentoAntecipadoQueGerouPontos pagamentoAntecipadoQueGerouPontos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDeTransferenciaDePontos.
    /// </summary>
    public partial interface IMovimentacaoDeTransferenciaDePontosFactory :IBaseFactory {  
    
		MovimentacaoDeTransferenciaDePontos Create();    
		MovimentacaoDeTransferenciaDePontos Create(Perlink.Trinks.Fidelidade.TransferenciaDePontos transferenciaDePontos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDeUmPontoGanho.
    /// </summary>
    public partial interface IMovimentacaoDeUmPontoGanhoFactory :IBaseFactory {  
    
		MovimentacaoDeUmPontoGanho Create();    
		MovimentacaoDeUmPontoGanho Create(int id, int? idTransacao, int idPessoaDoEstabelecimento, Perlink.Trinks.Fidelidade.PontoGanho pontoGanhoQueAMovimentacaoAlterou, Perlink.Trinks.Fidelidade.MovimentacaoDePontos movimentacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaDoCliente, int quantidadeDePontos, System.DateTime dataMovimentacao, System.DateTime horaMovimentacao, Perlink.Trinks.Fidelidade.Enums.OperacaoDaMovimentacaoDePontosEnum tipoDeOperacaoDeMovimentacao, string descricaoDoItem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipadoQueGerouPontos.
    /// </summary>
    public partial interface IPagamentoAntecipadoQueGerouPontosFactory :IBaseFactory {  
    
		PagamentoAntecipadoQueGerouPontos Create();    
		PagamentoAntecipadoQueGerouPontos Create(int id, Perlink.Trinks.PagamentosAntecipados.PagamentoAntecipado pagamentoAntecipado, int pontosDeFidelidade, bool usouPontosDeFidelidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PontoGanho.
    /// </summary>
    public partial interface IPontoGanhoFactory :IBaseFactory {  
    
		PontoGanho Create();    
		PontoGanho Create(int id, int? idTransacao, int idPessoaDoEstabelecimento, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaDoCliente, Perlink.Trinks.Fidelidade.MovimentacaoDePontos movimentacaoQueGerouPontos, System.Collections.Generic.IList<Perlink.Trinks.Fidelidade.MovimentacaoDeUmPontoGanho> movimentacoesAssociadas, int quantidadeDePontosGanhos, int quantidadeDePontosDisponiveis, int quantidadeDePontosUsados, Perlink.Trinks.Fidelidade.Enums.StatusMovimentacaoPontosEnum status, System.DateTime dataMovimentacao, System.DateTime horaMovimentacao, System.DateTime? dataValidade, string descricaoDoItem, System.DateTime? dataHoraUltimaProrrogacaoDeValidade, Perlink.Trinks.Pessoas.PessoaFisica responsavelPelaUltimaProrrogacao, System.DateTime? dataHoraUltimaAlteracaoDeStatus);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ProgramaDeFidelidade.
    /// </summary>
    public partial interface IProgramaDeFidelidadeFactory :IBaseFactory {  
    
		ProgramaDeFidelidade Create();    
		ProgramaDeFidelidade Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, decimal valorCorrespondenteAUmPonto, int? validadeDoPontoEmDias, bool enviarEmailAosClientesDeValidadeDosPontos, int? diasDeAntecedenciaParaNotificarValidadeDosPontos, int? maximoDePontosRegatadosPorDia, bool incluirPromocoes, System.Collections.Generic.IList<Perlink.Trinks.Fidelidade.ProgramaDeFidelidadeDiaSemana> diasDaSemana, int quantosProdutosParaGanharUm, int quantosServicosParaGanharUm, int? quantidadesPontosCreditadosPorAgendamentoOnline, bool? limitarCreditoPontosAoPrimeiroUso, bool clienteDeveTerCadastroCompleto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ProgramaDeFidelidadeDiaSemana.
    /// </summary>
    public partial interface IProgramaDeFidelidadeDiaSemanaFactory :IBaseFactory {  
    
		ProgramaDeFidelidadeDiaSemana Create();    
		ProgramaDeFidelidadeDiaSemana Create(int id, Perlink.Trinks.Fidelidade.ProgramaDeFidelidade programaDeFidelidade, Perlink.Trinks.Pessoas.DiaSemana diaDaSemana, bool podeTrocarPontos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransferenciaDePontos.
    /// </summary>
    public partial interface ITransferenciaDePontosFactory :IBaseFactory {  
    
		TransferenciaDePontos Create();    
		TransferenciaDePontos Create(int id, Perlink.Trinks.Fidelidade.Enums.TipoDaTransferenciaDePontosEnum tipo, int pontosDeFidelidade, bool usouPontosDeFidelidade, int idPessoaDoEstabelecimentoQueForneceuOsPontos, int idPessoaDoEstabelecimentoQueRecebeuOsPontos);
			 
    }
 
}
namespace Perlink.Trinks.Fidelidade.DTO.Factories {

 
}
namespace Perlink.Trinks.Fidelidade.Enums.Factories {

 
}
namespace Perlink.Trinks.Fidelidade.Factories {

 
}
namespace Perlink.Trinks.Fidelidade.Factories.Factories {

 
}
namespace Perlink.Trinks.Fidelidade.Filtros.Factories {

 
}
namespace Perlink.Trinks.Fidelidade.Strategies.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Factories {

    /// <summary>
    /// Interface para repositório da entidade AberturaFechamentoCaixa.
    /// </summary>
    public partial interface IAberturaFechamentoCaixaFactory :IBaseFactory {  
    
		AberturaFechamentoCaixa Create();    
		AberturaFechamentoCaixa Create(int id, decimal valor, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime data);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AberturaFechamentoCaixaHistorico.
    /// </summary>
    public partial interface IAberturaFechamentoCaixaHistoricoFactory :IBaseFactory {  
    
		AberturaFechamentoCaixaHistorico Create();    
		AberturaFechamentoCaixaHistorico Create(int id, Perlink.Trinks.Financeiro.AberturaFechamentoCaixa aberturaFechamentoCaixa, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizou, decimal valor, System.DateTime data);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Comissao.
    /// </summary>
    public partial interface IComissaoFactory :IBaseFactory {  
    
		Comissao Create();    
		Comissao Create(int id, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaComissionada, decimal valorComissao, decimal valorBase, Perlink.Trinks.Pessoas.TipoComissao tipoComissao, decimal? comissaoParaPagar, bool comissaoAlteradaManualmente, decimal? percentualDeComissaoOriginal, decimal? valorComissaoParaPagarOriginal, System.DateTime? dataHoraUltimaAlteracaoManualComissao, Perlink.Trinks.Pessoas.PessoaFisica responsavelPelaUltimaAlteracaoDeComissao, decimal descontoOperadora, decimal descontoExtra, bool descontoExtraNoValorBase, decimal valorBruto, bool consumoDePacote, decimal descontoCliente, Perlink.Trinks.Financeiro.Enums.TipoOrigemComissaoEnum tipoOrigemComissao, int idPessoaEstabelecimento, bool ehComissaoComSplit, System.Collections.Generic.IList<Perlink.Trinks.Financeiro.ValorDeComissaoAReceber> valoresAReceber);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancariaPessoa.
    /// </summary>
    public partial interface IContaBancariaPessoaFactory :IBaseFactory {  
    
		ContaBancariaPessoa Create();    
		ContaBancariaPessoa Create(int id, string codigoBanco, string agencia, string digitoVerificadorAgencia, string numeroConta, string digitoVerificadorNumeroConta, string numeroDaOperacao, Perlink.Trinks.Financeiro.TipoContaBancaria tipoConta, Perlink.Trinks.Pessoas.Pessoa pessoa, bool antecipacaoAutomatica, string documento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FechamentoFolhaMes.
    /// </summary>
    public partial interface IFechamentoFolhaMesFactory :IBaseFactory {  
    
		FechamentoFolhaMes Create();    
		FechamentoFolhaMes Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime referenciaInicio, System.DateTime referenciaFim, int mesReferencia, int anoReferencia, bool mesFechado, System.DateTime dataUltimaAlteracao, Perlink.Trinks.Pessoas.PessoaFisica pessoaUltimaAlteracao, string xML);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FechamentoFolhaMesProfissional.
    /// </summary>
    public partial interface IFechamentoFolhaMesProfissionalFactory :IBaseFactory {  
    
		FechamentoFolhaMesProfissional Create();    
		FechamentoFolhaMesProfissional Create(decimal alimentacao, string arquivo, decimal comissaoProdutos, decimal comissaoServicos, decimal comissaoPacotes, decimal compraProduto, System.DateTime dataUltimaAlteracao, decimal decimoTerceiro, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, decimal faltasAtrasos, Perlink.Trinks.Financeiro.FechamentoFolhaMes fechamentoFolhaMes, decimal ferias, decimal horaExtra, int id, decimal iNSS, string observacoes, decimal outrosDescontos, decimal outrosRecebimentos, Perlink.Trinks.Pessoas.PessoaFisica pessoaUltimaAlteracao, decimal salario, decimal salarioFamilia, decimal vales, decimal bonificacoes, decimal splitsPagamento, decimal valeTransporte, bool valoresSalvos, bool foiEstornado, int? idPessoaQueEstornou, Perlink.Trinks.Financeiro.PagamentoFolhaMesProfissional pagamentoFolhaMesProfissional, bool carregarMesAnterior, System.Collections.Generic.List<Perlink.Trinks.Financeiro.DTO.FechamentoMesComissaoDeProdutoDTO> comissaoProdutosLista, System.Collections.Generic.List<Perlink.Trinks.Financeiro.DTO.FechamentoMesComissaoDeServicoDTO> comissaoServicosLista, System.Collections.Generic.List<Perlink.Trinks.Financeiro.DTO.FechamentoMesComissaoDePacoteDTO> comissaoPacotesLista, System.Collections.Generic.List<Perlink.Trinks.Financeiro.DTO.FechamentoMesCompraProdutoDTO> compraProdutoLista, System.Collections.Generic.List<Perlink.Trinks.Despesas.Lancamento> valesLista, System.Collections.Generic.List<Perlink.Trinks.Despesas.Lancamento> bonificacoesLista, System.Collections.Generic.List<Perlink.Trinks.Despesas.Lancamento> splitsPagamentoLista);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormaPagamento.
    /// </summary>
    public partial interface IFormaPagamentoFactory :IBaseFactory {  
    
		FormaPagamento Create();    
		FormaPagamento Create(bool aceitaParcelamento, bool ativo, bool apareceHotSite, int diasParaReceberDaOperadora, bool exibirTransacoesPagasNosRelatorios, int id, int maximoDeParcelas, string nome, string observacao, decimal percentualCobradoPeloOperadora, bool temControleOperadora, Perlink.Trinks.Financeiro.FormaPagamentoTipo tipo, bool utilizadoEmPagamento, Perlink.Trinks.Pessoas.TipoPOS tipoPOS, bool utilizadoEmRecebimento, int? idFormaPagamentoNFe, int idAdministradoraDeCartaoDeCredito, int codigoFixoSat, int? brandId, string textoDoTooltipNoFechamentoDeConta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormaPagamentoTipo.
    /// </summary>
    public partial interface IFormaPagamentoTipoFactory :IBaseFactory {  
    
		FormaPagamentoTipo Create();    
		FormaPagamentoTipo Create(int id, string nome, bool ativo, int ordemNoFechamentoDeConta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Gorjeta.
    /// </summary>
    public partial interface IGorjetaFactory :IBaseFactory {  
    
		Gorjeta Create();    
		Gorjeta Create(int idGorjeta, int idEstabelecimento, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Pessoas.Profissional profissional, decimal valor, System.DateTime? dataPagamento, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoDeAntecipacao.
    /// </summary>
    public partial interface ILancamentoDeAntecipacaoFactory :IBaseFactory {  
    
		LancamentoDeAntecipacao Create();    
		LancamentoDeAntecipacao Create(int id, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRegistrou, System.DateTime dataHoraRegistro, Perlink.Trinks.Financeiro.TransacaoFormaPagamentoParcela transacaoParcela, System.DateTime dataRecebimentoOriginal, decimal taxaDesconto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDesconto.
    /// </summary>
    public partial interface IMotivoDescontoFactory :IBaseFactory {  
    
		MotivoDesconto Create();    
		MotivoDesconto Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, string descricao, bool descontoRefleteNaComissao, bool ativo, Perlink.Trinks.Financeiro.Enums.MotivoDeDescontoDoTrinksEnum? motivoDeDescontoDoTrinks);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoFolhaMesProfissional.
    /// </summary>
    public partial interface IPagamentoFolhaMesProfissionalFactory :IBaseFactory {  
    
		PagamentoFolhaMesProfissional Create();    
		PagamentoFolhaMesProfissional Create(int id, Perlink.Trinks.ConciliacaoBancaria.ContaFinanceiraDoEstabelecimento contaFinanceira, Perlink.Trinks.Financeiro.FormaPagamento formaPagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Sangria.
    /// </summary>
    public partial interface ISangriaFactory :IBaseFactory {  
    
		Sangria Create();    
		Sangria Create(int id, decimal valor, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime data);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SangriaHistorico.
    /// </summary>
    public partial interface ISangriaHistoricoFactory :IBaseFactory {  
    
		SangriaHistorico Create();    
		SangriaHistorico Create(int id, Perlink.Trinks.Financeiro.Sangria sangria, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizou, decimal valor, System.DateTime data);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoContaBancaria.
    /// </summary>
    public partial interface ITipoContaBancariaFactory :IBaseFactory {  
    
		TipoContaBancaria Create();    
		TipoContaBancaria Create(int id, string nomeTipoConta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoTransacao.
    /// </summary>
    public partial interface ITipoTransacaoFactory :IBaseFactory {  
    
		TipoTransacao Create();    
		TipoTransacao Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Transacao.
    /// </summary>
    public partial interface ITransacaoFactory :IBaseFactory {  
    
		Transacao Create();    
		Transacao Create(bool ativo, Perlink.Trinks.Vendas.Comanda comanda, string comentarioEstorno, string comentarioFechamentoConta, System.DateTime dataHora, System.DateTime dataReferencia, bool foiVendaParaProfissional, decimal? descontos, decimal? descontosPacotes, decimal? descontosProdutos, decimal? descontosServicos, decimal? descontosValePresente, System.Collections.Generic.IList<Perlink.Trinks.Financeiro.TransacaoFormaPagamento> formasPagamento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.HorarioTransacao> horariosTransacoes, System.Collections.Generic.IList<Perlink.Trinks.Financeiro.TransacaoItem> transacaoItens, int id, int? numeroDaPreVenda, bool pagamentoJaEstornado, decimal percentualMedioDescontoOperadoras, Perlink.Trinks.Pessoas.PessoaFisica pessoaQuePagou, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizou, Perlink.Trinks.Pessoas.PessoaJuridica pessoaQueRecebeu, Perlink.Trinks.Financeiro.TipoTransacao tipoTransacao, decimal totalCreditoCliente, decimal? totalDescartaveis, decimal totalDescontoOperadoras, decimal? totalPacotes, decimal? totalClubeDeAssinaturas, decimal? totalPagar, decimal? totalPago, decimal? totalPagoEmCredito, decimal? totalPagoEmDebito, decimal? totalPagoEmDinheiro, decimal? totalPagoEmOutros, decimal totalPagoEmPrePago, decimal? totalProdutos, decimal? totalServicos, decimal totalValePresente, Perlink.Trinks.Financeiro.Transacao transacaoQueEstounouEsta, int? idTransacaoQueEstounouEsta, decimal? troco, System.Collections.Generic.IList<Perlink.Trinks.Financeiro.Gorjeta> gorjetas, System.Collections.Generic.IList<Perlink.Trinks.Vendas.Venda> vendas, int saldoDePontosDeFidelidadeDoClienteAposEfetuarTransacao, Perlink.Trinks.Cashback.BonusTransacao bonusTransacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoFormaPagamento.
    /// </summary>
    public partial interface ITransacaoFormaPagamentoFactory :IBaseFactory {  
    
		TransacaoFormaPagamento Create();    
		TransacaoFormaPagamento Create(bool ativo, int diasParaReceberDaOperadora, Perlink.Trinks.Financeiro.FormaPagamento formaPagamento, int idTransacaoFormaPagamento, int numeroParcelas, System.Collections.Generic.IList<Perlink.Trinks.Financeiro.TransacaoFormaPagamentoParcela> parcelas, decimal? percentualCobradoPelaOperadora, Perlink.Trinks.Pessoas.Pessoa pessoaQuePagou, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Vendas.ValePresente valePresente, decimal valorPago, decimal? valorBaseCalculoPercentualDesconto, decimal? valorFixoCobradoPelaOperadora);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoFormaPagamentoParcela.
    /// </summary>
    public partial interface ITransacaoFormaPagamentoParcelaFactory :IBaseFactory {  
    
		TransacaoFormaPagamentoParcela Create();    
		TransacaoFormaPagamentoParcela Create(int id, Perlink.Trinks.Financeiro.TransacaoFormaPagamento transacaoFormaPagamento, int numeroParcela, decimal valor, System.DateTime dataPagamento, System.DateTime dataRecebimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoHistorico.
    /// </summary>
    public partial interface ITransacaoHistoricoFactory :IBaseFactory {  
    
		TransacaoHistorico Create();    
		TransacaoHistorico Create(int id, Perlink.Trinks.Financeiro.Transacao transacao, int idPessoaQueRecebeu, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueAlterou, System.DateTime dataHoraQueAlterou, System.DateTime dataHoraAnterior, System.DateTime dataHoraNovo, int? idPessoaFisicaOperadorDeCaixaAnterior, int? idPessoaFisicaOperadorDeCaixaNovo, int? idRegistroDeCaixaAntigo, int? idRegistroDeCaixaNovo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoItem.
    /// </summary>
    public partial interface ITransacaoItemFactory :IBaseFactory {  
    
		TransacaoItem Create();    
		TransacaoItem Create(int id, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Financeiro.MotivoDesconto motivoDesconto, decimal valor, decimal? valorDesconto, string nome, string tipo, int idObjetoReferencia, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoLancamentoFinanceiro.
    /// </summary>
    public partial interface ITransacaoLancamentoFinanceiroFactory :IBaseFactory {  
    
		TransacaoLancamentoFinanceiro Create();    
		TransacaoLancamentoFinanceiro Create(int id, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Despesas.Lancamento lancamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPOS.
    /// </summary>
    public partial interface ITransacaoPOSFactory :IBaseFactory {  
    
		TransacaoPOS Create();    
		TransacaoPOS Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Pessoas.TipoPOS tipoPOS, System.DateTime data, decimal valor, string acquirerTransactionKey, string initiatorTransactionKey, string nomeBandeira, bool registradoPeloTrinks, Perlink.Trinks.Financeiro.TransacaoPOS transacaoPOSEstornada, int? brandId, string cardHolderName, System.DateTime? expirationDate, string maskedAccountNumber, string transactionType, string identificadorEntreAplicacoes, string codigoAutorizacaoTransacaoPDV, string guidCupom, System.DateTime? dataHoraConciliacao, int? idContaConciliacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPOSSplit.
    /// </summary>
    public partial interface ITransacaoPOSSplitFactory :IBaseFactory {  
    
		TransacaoPOSSplit Create();    
		TransacaoPOSSplit Create(int id, Perlink.Trinks.Financeiro.TransacaoPOS transacaoPOS, string idExternoSplit, int idStatus);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPosWebhookRequest.
    /// </summary>
    public partial interface ITransacaoPosWebhookRequestFactory :IBaseFactory {  
    
		TransacaoPosWebhookRequest Create();    
		TransacaoPosWebhookRequest Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, Perlink.Trinks.Pessoas.Cliente cliente, Perlink.Trinks.Financeiro.Transacao transacao, string merchantId, string cNPJ, string serial, string idOperacaoPos, System.DateTime? dataOperacao, int tipoOperacao, string proofOfSale, string pDV, int idTransacaoPos, string numeroCartao, string codigoBandeiraCartao, decimal valorFechamento, decimal valorPago, int? formaPagamentoPos, int? modoPagamentoPos, int? modoParcelamento, int? numeroParcelas, string codigoTransacao, System.DateTime? dataTransacaoPos, System.DateTime? dataCriacao, int chaveDiariaTransacao, bool operacaoCancelada, bool pagamentoEstornado, bool foiPago, Perlink.Trinks.Financeiro.TransacaoPOS transacaoPos, Perlink.Trinks.Pessoas.TipoPOS tipoPos, string externalTransactionId, string stoneTransactionId, bool ehSplit, string statusPreTransacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeComissaoAReceber.
    /// </summary>
    public partial interface IValorDeComissaoAReceberFactory :IBaseFactory {  
    
		ValorDeComissaoAReceber Create();    
		ValorDeComissaoAReceber Create(int id, System.DateTime dataDaComissaoAReceber, decimal valorBaseProporcional, decimal valorBrutoProporcional, decimal descontoOperadoraProporcional, decimal descontoExtraProporcional, decimal descontoClienteProporcional, decimal valor, Perlink.Trinks.Financeiro.Comissao comissao, Perlink.Trinks.Financeiro.TransacaoFormaPagamentoParcela transacaoFormaPagamentoParcela, Perlink.Trinks.Financeiro.FechamentoFolhaMesProfissional fechamentoProfisisonal, bool ativo, System.DateTime dataDeCadastro, int idPessoaEstabelecimento, bool valorAlteradoManualmente, decimal? valorOriginal, System.DateTime? dataHoraUltimaAlteracaoManualDeValor, Perlink.Trinks.Pessoas.PessoaFisica responsavelPelaUltimaAlteracaoManualDeValor, bool dataDaComissaoAReceberAlteradaManualmente, System.DateTime? dataDaComissaoAReceberOriginal, System.DateTime? dataHoraUltimaAlteracaoManualDeDataDaComissaoAReceber, Perlink.Trinks.Pessoas.Pessoa responsavelPelaUltimaAlteracaoManualDeDataDaComissaoAReceber);
			 
    }
 
}
namespace Perlink.Trinks.Financeiro.Adapters.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Calculos.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Factories {

    /// <summary>
    /// Interface para repositório da entidade HistoricoDoCaixaPorOperador.
    /// </summary>
    public partial interface IHistoricoDoCaixaPorOperadorFactory :IBaseFactory {  
    
		HistoricoDoCaixaPorOperador Create();    
		HistoricoDoCaixaPorOperador Create(int id, Perlink.Trinks.Financeiro.Enums.TipoHistoricoAlteracaoCaixa tipo, System.DateTime dataAlteracao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueAlterou, int idRegistroDeCaixaPorOperador, int idPessoaJuridica, int idPessoaFisicaOperador, Perlink.Trinks.Financeiro.Enums.StatusCaixaOperador status, decimal valorAntigo, decimal valorNovo, Perlink.Trinks.Enums.OrigemAlteracaoHistoricoEnum origemAlteracaoHistorico, string motivoDeSangria);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoCaixaPorOperador.
    /// </summary>
    public partial interface IMovimentacaoNoCaixaPorOperadorFactory :IBaseFactory {  
    
		MovimentacaoNoCaixaPorOperador Create();    
		MovimentacaoNoCaixaPorOperador Create(int id, System.DateTime dataHoraCadastro, Perlink.Trinks.Financeiro.RegistroDeCaixaPorOperador caixaPorOperador, int idPessoaJuridica, int idPessoaFisicaOperador, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoCaixaPorOperadorLancamento.
    /// </summary>
    public partial interface IMovimentacaoNoCaixaPorOperadorLancamentoFactory :IBaseFactory {  
    
		MovimentacaoNoCaixaPorOperadorLancamento Create();    
		MovimentacaoNoCaixaPorOperadorLancamento Create(Perlink.Trinks.Despesas.Lancamento lancamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoCaixaPorOperadorTransacao.
    /// </summary>
    public partial interface IMovimentacaoNoCaixaPorOperadorTransacaoFactory :IBaseFactory {  
    
		MovimentacaoNoCaixaPorOperadorTransacao Create();    
		MovimentacaoNoCaixaPorOperadorTransacao Create(Perlink.Trinks.Financeiro.Transacao transacao, int? idTransacaoQueEstornouEsta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RegistroDeCaixaPorOperador.
    /// </summary>
    public partial interface IRegistroDeCaixaPorOperadorFactory :IBaseFactory {  
    
		RegistroDeCaixaPorOperador Create();    
		RegistroDeCaixaPorOperador Create(int id, System.DateTime dataHoraCadastro, bool aberto, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaOperador, Perlink.Trinks.Financeiro.Enums.StatusCaixaOperador status, decimal valorDaAbertura, decimal valorDaSangria, decimal saldoRealEmDinheiroDoOperador);
			 
    }
 
}
namespace Perlink.Trinks.Financeiro.Factories {

    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizado.
    /// </summary>
    public partial interface IDescontoPersonalizadoFactory :IBaseFactory {  
    
		DescontoPersonalizado Create();    
		DescontoPersonalizado Create(int id, int idEstabelecimento, string nome, decimal valor, Perlink.Trinks.Financeiro.Enums.DescontoPersonalizadoTipoEnum tipo, Perlink.Trinks.Financeiro.Enums.AplicacaoNaComissaoEnum aplicacaoNaComissao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoAssistentes.
    /// </summary>
    public partial interface IDescontoPersonalizadoAssistentesFactory :IBaseFactory {  
    
		DescontoPersonalizadoAssistentes Create();    
		DescontoPersonalizadoAssistentes Create(int id, Perlink.Trinks.Financeiro.DescontoPersonalizado descontoPersonalizado, int idPessoa);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoPacote.
    /// </summary>
    public partial interface IDescontoPersonalizadoPacoteFactory :IBaseFactory {  
    
		DescontoPersonalizadoPacote Create();    
		DescontoPersonalizadoPacote Create(int id, int idEstabelecimento, Perlink.Trinks.Financeiro.DescontoPersonalizado descontoPersonalizado, int idPacote);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoProduto.
    /// </summary>
    public partial interface IDescontoPersonalizadoProdutoFactory :IBaseFactory {  
    
		DescontoPersonalizadoProduto Create();    
		DescontoPersonalizadoProduto Create(int id, int idEstabelecimento, Perlink.Trinks.Financeiro.DescontoPersonalizado descontoPersonalizado, int idEstabelecimentoProduto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoProfissionais.
    /// </summary>
    public partial interface IDescontoPersonalizadoProfissionaisFactory :IBaseFactory {  
    
		DescontoPersonalizadoProfissionais Create();    
		DescontoPersonalizadoProfissionais Create(int id, Perlink.Trinks.Financeiro.DescontoPersonalizado descontoPersonalizado, int idPessoa);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoServico.
    /// </summary>
    public partial interface IDescontoPersonalizadoServicoFactory :IBaseFactory {  
    
		DescontoPersonalizadoServico Create();    
		DescontoPersonalizadoServico Create(int id, int idEstabelecimento, Perlink.Trinks.Financeiro.DescontoPersonalizado descontoPersonalizado, int idServicoEstabelecimento);
			 
    }
 
}
namespace Perlink.Trinks.Financeiro.DTO.Factories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.CalculoComissao.Factories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Checkout.Factories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Connect.Pagarme.Factories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Connect.Stone.Factories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.DescontosPersonalizados.Factories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.POS.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Enums.Factories {

 
}
namespace Perlink.Trinks.Financeiro.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Factories.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Filtros.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Interfaces.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Factories {

    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoCompraProduto.
    /// </summary>
    public partial interface IFolhaPagamentoCompraProdutoFactory :IBaseFactory {  
    
		FolhaPagamentoCompraProduto Create();    
		FolhaPagamentoCompraProduto Create(Perlink.Trinks.Financeiro.TransacaoFormaPagamentoParcela transacaoFormaPagamentoParcela);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItem.
    /// </summary>
    public partial interface IFolhaPagamentoItemFactory :IBaseFactory {  
    
		FolhaPagamentoItem Create();    
		FolhaPagamentoItem Create(int id, Perlink.Trinks.Financeiro.FechamentoFolhaMesProfissional fechamentoFolhaMesProfissional, decimal valor, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemBonificacao.
    /// </summary>
    public partial interface IFolhaPagamentoItemBonificacaoFactory :IBaseFactory {  
    
		FolhaPagamentoItemBonificacao Create();    
		FolhaPagamentoItemBonificacao Create(Perlink.Trinks.Despesas.Lancamento lancamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemGorjeta.
    /// </summary>
    public partial interface IFolhaPagamentoItemGorjetaFactory :IBaseFactory {  
    
		FolhaPagamentoItemGorjeta Create();    
		FolhaPagamentoItemGorjeta Create(Perlink.Trinks.Financeiro.Gorjeta gorjeta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemSplit.
    /// </summary>
    public partial interface IFolhaPagamentoItemSplitFactory :IBaseFactory {  
    
		FolhaPagamentoItemSplit Create();    
		FolhaPagamentoItemSplit Create(Perlink.Trinks.Despesas.Lancamento lancamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemVale.
    /// </summary>
    public partial interface IFolhaPagamentoItemValeFactory :IBaseFactory {  
    
		FolhaPagamentoItemVale Create();    
		FolhaPagamentoItemVale Create(Perlink.Trinks.Despesas.Lancamento lancamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoLancamento.
    /// </summary>
    public partial interface IFolhaPagamentoLancamentoFactory :IBaseFactory {  
    
		FolhaPagamentoLancamento Create();    
		FolhaPagamentoLancamento Create(int id, Perlink.Trinks.Financeiro.FechamentoFolhaMesProfissional fechamentoFolhaMesProfissional, int idLancamento, bool foiEstornado, System.DateTime? dataUltimaAtualizacao, int? idPessoaQueAlterou);
			 
    }
 
}
namespace Perlink.Trinks.Financeiro.POS.Factories {

 
}
namespace Perlink.Trinks.Financeiro.Stories.Factories {

 
}
namespace Perlink.Trinks.Formulario.Factories {

    /// <summary>
    /// Interface para repositório da entidade AssinaturaDigital.
    /// </summary>
    public partial interface IAssinaturaDigitalFactory :IBaseFactory {  
    
		AssinaturaDigital Create();    
		AssinaturaDigital Create(int id, int idEstabelecimento, Perlink.Trinks.Formulario.FormularioRespondido formularioRespondido, string nomeArquivo, int idPessoaAssinante, string nomeDoAssinante, string documentoDoAssinante, System.DateTime dataHoraAssinatura);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDoFormulario.
    /// </summary>
    public partial interface IConfiguracaoDoFormularioFactory :IBaseFactory {  
    
		ConfiguracaoDoFormulario Create();    
		ConfiguracaoDoFormulario Create(int id, Perlink.Trinks.Formulario.FormularioDinamico formulario, bool habilitaAceiteEletronicoCliente, bool habilitaAceiteEletronicoProfissional);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormularioDinamico.
    /// </summary>
    public partial interface IFormularioDinamicoFactory :IBaseFactory {  
    
		FormularioDinamico Create();    
		FormularioDinamico Create(int id, int idEstabelecimento, string titulo, string descricao, System.DateTime dataCriacao, int idPessoaQueCriou, System.DateTime? dataModificacao, bool ativo, Perlink.Trinks.Formulario.VersaoDoFormularioDinamico versaoMaisRecente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormularioRespondido.
    /// </summary>
    public partial interface IFormularioRespondidoFactory :IBaseFactory {  
    
		FormularioRespondido Create();    
		FormularioRespondido Create(int id, int idEstabelecimento, Perlink.Trinks.Formulario.FormularioDinamico formularioDinamico, Perlink.Trinks.Formulario.VersaoDoFormularioDinamico versaoRespondida, System.DateTime dataResposta, System.DateTime? dataModificacao, Perlink.Trinks.Formulario.PessoaPerguntada pessoaPerguntada, System.Collections.Generic.IList<Perlink.Trinks.Formulario.RespostaDoFormulario> respostas, Perlink.Trinks.Pessoas.PessoaFisica pessoaQuePerguntou);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OpcaoDeResposta.
    /// </summary>
    public partial interface IOpcaoDeRespostaFactory :IBaseFactory {  
    
		OpcaoDeResposta Create();    
		OpcaoDeResposta Create(int id, Perlink.Trinks.Formulario.QuestaoDoFormulario questao, string texto, Perlink.Trinks.Formulario.Enums.TipoOpcaoDeRespostaEnum tipo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaPerguntada.
    /// </summary>
    public partial interface IPessoaPerguntadaFactory :IBaseFactory {  
    
		PessoaPerguntada Create();    
		PessoaPerguntada Create(int id, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestaoDoFormulario.
    /// </summary>
    public partial interface IQuestaoDoFormularioFactory :IBaseFactory {  
    
		QuestaoDoFormulario Create();    
		QuestaoDoFormulario Create(int id, Perlink.Trinks.Formulario.FormularioDinamico formulario, string pergunta, Perlink.Trinks.Formulario.TipoDeResposta tipoResposta, int? maximoDeOpcoesDeResposta, System.DateTime dataDeInclusao, System.Collections.Generic.IList<Perlink.Trinks.Formulario.OpcaoDeResposta> opcoesDeRespostas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RespostaDoFormulario.
    /// </summary>
    public partial interface IRespostaDoFormularioFactory :IBaseFactory {  
    
		RespostaDoFormulario Create();    
		RespostaDoFormulario Create(int id, Perlink.Trinks.Formulario.FormularioRespondido formularioRespondido, Perlink.Trinks.Formulario.QuestaoDoFormulario questaoDeOrigem, Perlink.Trinks.Formulario.OpcaoDeResposta opcaoDeRespostaDeOrigem, string textoResposta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SolicitacaoDeAssinatura.
    /// </summary>
    public partial interface ISolicitacaoDeAssinaturaFactory :IBaseFactory {  
    
		SolicitacaoDeAssinatura Create();    
		SolicitacaoDeAssinatura Create(int id, Perlink.Trinks.Formulario.FormularioRespondido formularioRespondido, string guidAcessoTelaPublica, System.DateTime dataHoraEmailEnviado, System.DateTime dataHoraLimite, bool ativo, Perlink.Trinks.Formulario.AssinaturaDigital assinaturaDigital, System.DateTime? dataHoraCancelamento, Perlink.Trinks.Formulario.Enums.FormaEnvioDaSolicitacaoEnum formaDeEnvio, string contatoDoEnvio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDeResposta.
    /// </summary>
    public partial interface ITipoDeRespostaFactory :IBaseFactory {  
    
		TipoDeResposta Create();    
		TipoDeResposta Create(int id, string nome, int? maximoDeOpcoesParaSelecionar, string opcoesExibirInicialmente, bool ehNecessarioTerOpcoesDeRespostas, string templateDeCampos, int ordemDeExibicao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VersaoDaQuestaoDoFormulario.
    /// </summary>
    public partial interface IVersaoDaQuestaoDoFormularioFactory :IBaseFactory {  
    
		VersaoDaQuestaoDoFormulario Create();    
		VersaoDaQuestaoDoFormulario Create(int id, Perlink.Trinks.Formulario.VersaoDoFormularioDinamico versaoDoFormulario, int idQuestaoDoFormulario, int? idDaProximaQuestao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VersaoDoFormularioDinamico.
    /// </summary>
    public partial interface IVersaoDoFormularioDinamicoFactory :IBaseFactory {  
    
		VersaoDoFormularioDinamico Create();    
		VersaoDoFormularioDinamico Create(int id, Perlink.Trinks.Formulario.FormularioDinamico formulario, string titulo, string descricao, System.Collections.Generic.IList<Perlink.Trinks.Formulario.VersaoDaQuestaoDoFormulario> questoes);
			 
    }
 
}
namespace Perlink.Trinks.Formulario.DTO.Factories {

 
}
namespace Perlink.Trinks.Formulario.Enums.Factories {

 
}
namespace Perlink.Trinks.Formulario.Filtros.Factories {

 
}
namespace Perlink.Trinks.Formulario.Stories.Factories {

 
}
namespace Perlink.Trinks.Fotos.ControleDeFotos.Factories {

 
}
namespace Perlink.Trinks.Fotos.DTO.Factories {

 
}
namespace Perlink.Trinks.Fotos.Enums.Factories {

 
}
namespace Perlink.Trinks.Fotos.Factories.Factories {

 
}
namespace Perlink.Trinks.Fotos.Factories {

    /// <summary>
    /// Interface para repositório da entidade Foto.
    /// </summary>
    public partial interface IFotoFactory :IBaseFactory {  
    
		Foto Create();    
		Foto Create(int id, string legenda, string extencao, System.DateTime dataDoCadastro);
			 
    }
 
}
namespace Perlink.Trinks.Fotos.Factories {

 
}
namespace Perlink.Trinks.Google.Comparers.Factories {

 
}
namespace Perlink.Trinks.Google.DTO.Factories {

 
}
namespace Perlink.Trinks.Google.Enums.Factories {

 
}
namespace Perlink.Trinks.Google.Providers.Factories {

 
}
namespace Perlink.Trinks.GyraMais.Factories {

    /// <summary>
    /// Interface para repositório da entidade DadosCliente.
    /// </summary>
    public partial interface IDadosClienteFactory :IBaseFactory {  
    
		DadosCliente Create();    
		DadosCliente Create(int id, int idEstabelecimento, System.DateTime dataElegivel, int? idPessoaAceite, System.DateTime? dataAceite, string origemAceite, int valorSelecionado, System.DateTime? dataEnvioDadosGyra, System.DateTime? dataRetorno, bool? aprovado, string linkOferta, string dadosOferta, System.DateTime? dataClicouContratar, System.DateTime? dataContratou, string dadosContrato);
			 
    }
 
}
namespace Perlink.Trinks.Identity.Factories {

    /// <summary>
    /// Interface para repositório da entidade ApiAccount.
    /// </summary>
    public partial interface IApiAccountFactory :IBaseFactory {  
    
		ApiAccount Create();    
		ApiAccount Create(int id, string token, bool ativo, int idPessoa);
			 
    }
 
}
namespace Perlink.Trinks.Importacao.Factories {

 
}
namespace Perlink.Trinks.Importacao.Conversores.Factories {

 
}
namespace Perlink.Trinks.Importacao.DTO.Factories {

 
}
namespace Perlink.Trinks.Importacao.Exceptions.Factories {

 
}
namespace Perlink.Trinks.Importacao.Importadores.Factories {

 
}
namespace Perlink.Trinks.Importacao.Statics.Factories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Factories {

    /// <summary>
    /// Interface para repositório da entidade SolicitacaoDeImportacao.
    /// </summary>
    public partial interface ISolicitacaoDeImportacaoFactory :IBaseFactory {  
    
		SolicitacaoDeImportacao Create();    
		SolicitacaoDeImportacao Create(int id, int idEstabelecimento, int idPessoaQueSolicitou, System.DateTime dataHoraSolicitacao, Perlink.Trinks.ImportacaoDeDados.OrigemSolicitacao origem, Perlink.Trinks.ImportacaoDeDados.TipoImportacao tipoImportacao, Perlink.Trinks.ImportacaoDeDados.StatusDaImportacao status, string emailDeNotificacao, string nomeArquivo, System.DateTime? dataHoraInicioImportacao, System.DateTime? dataHoraFimImportacao, int qtdRegistrosParaImportar, int? qtdRegistrosImportados);
			 
    }
 
}
namespace Perlink.Trinks.ImportacaoDeDados.DTO.Factories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Exceptions.Factories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Importadores.Factories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.TiposDeColuna.Factories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.DTO.Factories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Factories {

    /// <summary>
    /// Interface para repositório da entidade EventoIntegracaoComOutrosSistemas.
    /// </summary>
    public partial interface IEventoIntegracaoComOutrosSistemasFactory :IBaseFactory {  
    
		EventoIntegracaoComOutrosSistemas Create();    
		EventoIntegracaoComOutrosSistemas Create(int id, Perlink.Trinks.IntegracaoComOutrosSistemas.Enums.TipoDeEventoEnum tipo, string jsonDosDados, bool eventoFoiNotificado, string chaveDeIntegracaoComOutrosSistemas, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaComChaveDeIntegracao.
    /// </summary>
    public partial interface IFranquiaComChaveDeIntegracaoFactory :IBaseFactory {  
    
		FranquiaComChaveDeIntegracao Create();    
		FranquiaComChaveDeIntegracao Create(int idFranquia, string chaveIntegracaoOutrosSistemas, bool permiteEnvioDadosAgendamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaEstabelecimentoComChaveDeIntegracao.
    /// </summary>
    public partial interface IFranquiaEstabelecimentoComChaveDeIntegracaoFactory :IBaseFactory {  
    
		FranquiaEstabelecimentoComChaveDeIntegracao Create();    
		FranquiaEstabelecimentoComChaveDeIntegracao Create(int idEstabelecimento, string chaveIntegracaoOutrosSistemas, bool permiteEnvioDadosAgendamento);
			 
    }
 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Enums.Factories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.IntegracaoComTrinks.Factories {

 
}
namespace Perlink.Trinks.InternoProduto.Enum.Factories {

 
}
namespace Perlink.Trinks.InternoProduto.Factories {

    /// <summary>
    /// Interface para repositório da entidade QuestionarioProduto.
    /// </summary>
    public partial interface IQuestionarioProdutoFactory :IBaseFactory {  
    
		QuestionarioProduto Create();    
		QuestionarioProduto Create(int id, string titulo, string descricao, string identificador, string jSFile, int? idRecurso, System.Collections.Generic.IList<Perlink.Trinks.InternoProduto.QuestionarioProdutoPergunta> perguntas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoOpcaoDeResposta.
    /// </summary>
    public partial interface IQuestionarioProdutoOpcaoDeRespostaFactory :IBaseFactory {  
    
		QuestionarioProdutoOpcaoDeResposta Create();    
		QuestionarioProdutoOpcaoDeResposta Create(int id, Perlink.Trinks.InternoProduto.QuestionarioProdutoPergunta pergunta, string texto, Perlink.Trinks.InternoProduto.Enum.TipoOpcaoDeRespostaEnum tipo, Perlink.Trinks.InternoProduto.Enum.AcaoDaOpcaoDeRespostaEnum acao, int? idReferenteAAcao, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoPergunta.
    /// </summary>
    public partial interface IQuestionarioProdutoPerguntaFactory :IBaseFactory {  
    
		QuestionarioProdutoPergunta Create();    
		QuestionarioProdutoPergunta Create(int id, Perlink.Trinks.InternoProduto.QuestionarioProduto questionario, Perlink.Trinks.InternoProduto.QuestionarioProdutoTipoResposta tipoResposta, string pergunta, bool ativo, int ordem, bool randomizarOrdemRespostas, Perlink.Trinks.InternoProduto.Enum.AcaoAoResponderPerguntaEnum? acao, int? idReferenteAAcao, string textoComplementar);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoRespondido.
    /// </summary>
    public partial interface IQuestionarioProdutoRespondidoFactory :IBaseFactory {  
    
		QuestionarioProdutoRespondido Create();    
		QuestionarioProdutoRespondido Create(int id, Perlink.Trinks.InternoProduto.QuestionarioProduto questionario, System.DateTime dataResposta, int? idEstabelecimento, int? idConta, System.Collections.Generic.IList<Perlink.Trinks.InternoProduto.QuestionarioProdutoRespondidoResposta> respostas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoRespondidoResposta.
    /// </summary>
    public partial interface IQuestionarioProdutoRespondidoRespostaFactory :IBaseFactory {  
    
		QuestionarioProdutoRespondidoResposta Create();    
		QuestionarioProdutoRespondidoResposta Create(int id, Perlink.Trinks.InternoProduto.QuestionarioProdutoRespondido questionarioRespondido, Perlink.Trinks.InternoProduto.QuestionarioProdutoPergunta questionarioPergunta, string textoResposta);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoTipoResposta.
    /// </summary>
    public partial interface IQuestionarioProdutoTipoRespostaFactory :IBaseFactory {  
    
		QuestionarioProdutoTipoResposta Create();    
		QuestionarioProdutoTipoResposta Create(int id, string nome, string templateDeCampos);
			 
    }
 
}
namespace Perlink.Trinks.LGPD.Helpers.Factories {

 
}
namespace Perlink.Trinks.LinksDePagamento.DTOs.Factories {

 
}
namespace Perlink.Trinks.LinksDePagamento.Enums.Factories {

 
}
namespace Perlink.Trinks.LinksDePagamento.Factories {

    /// <summary>
    /// Interface para repositório da entidade ItemLinkDePagamento.
    /// </summary>
    public partial interface IItemLinkDePagamentoFactory :IBaseFactory {  
    
		ItemLinkDePagamento Create();    
		ItemLinkDePagamento Create(int idItemLink, string nome, decimal valor, Perlink.Trinks.LinksDePagamento.LinkDePagamento linkDePagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamento.
    /// </summary>
    public partial interface ILinkDePagamentoFactory :IBaseFactory {  
    
		LinkDePagamento Create();    
		LinkDePagamento Create(int idLinkDePagamento, string identificador, int idComprador, int? idPagamento, string nomeComprador, string emailComprador, int idRecebedor, string nomeRecebedor, decimal valor, Perlink.Trinks.LinksDePagamento.Enums.StatusPagamentoEnum statusPagamento, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, System.DateTime? dataPagamento, System.DateTime? dataPrimeiroAcesso, System.DateTime dataDeValidade, bool ativo, System.Collections.Generic.IList<Perlink.Trinks.LinksDePagamento.ItemLinkDePagamento> itens, System.DateTime? dataQueFoiAdicionadoNaFilaDeProcessamentoDoPagamento, string urlValidacao);
			 
    }
 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs.Factories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Enums.Factories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Factories {

    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoNoTrinks.
    /// </summary>
    public partial interface ILinkDePagamentoNoTrinksFactory :IBaseFactory {  
    
		LinkDePagamentoNoTrinks Create();    
		LinkDePagamentoNoTrinks Create(int idLinkDePagamentoNoTrinks, int idLinkDePagamento, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.PagamentosAntecipados.PagamentoAntecipado pagamentoAntecipado, Perlink.Trinks.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinks pagamentoOnlineNoTrinks, Perlink.Trinks.LinksDePagamentoNoTrinks.Enums.OrigemDoLinkDePagamentoEnum origemDoLinkDePagamento, System.DateTime? dataDeProcessamentoDoPagamento);
			 
    }
 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.Factories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies.Factories {

 
}
namespace Perlink.Trinks.Localizacoes.DTO.Factories {

 
}
namespace Perlink.Trinks.Localizacoes.Enums.Factories {

 
}
namespace Perlink.Trinks.Loggers.Factories {

 
}
namespace Perlink.Trinks.Marcadores.Factories {

    /// <summary>
    /// Interface para repositório da entidade CorEtiqueta.
    /// </summary>
    public partial interface ICorEtiquetaFactory :IBaseFactory {  
    
		CorEtiqueta Create();    
		CorEtiqueta Create(int id, string cor, string corTexto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Etiqueta.
    /// </summary>
    public partial interface IEtiquetaFactory :IBaseFactory {  
    
		Etiqueta Create();    
		Etiqueta Create(int idEtiqueta, int idDono, Perlink.Trinks.Marcadores.Enums.TipoDeEtiquetaEnum tipo, string conteudo, string corEmHexadecimal, bool ativo, System.DateTime? dataCadastro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ObjetoEtiquetado.
    /// </summary>
    public partial interface IObjetoEtiquetadoFactory :IBaseFactory {  
    
		ObjetoEtiquetado Create();    
		ObjetoEtiquetado Create(int id, Perlink.Trinks.Marcadores.Etiqueta etiqueta, int idObjetoEtiquetado, System.DateTime? dataCadastro);
			 
    }
 
}
namespace Perlink.Trinks.Marcadores.DTO.Factories {

 
}
namespace Perlink.Trinks.Marcadores.Enums.Factories {

 
}
namespace Perlink.Trinks.Marcadores.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.Marcadores.Filtros.Factories {

 
}
namespace Perlink.Trinks.Marketing.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracoesEstabelecimentoMarketing.
    /// </summary>
    public partial interface IConfiguracoesEstabelecimentoMarketingFactory :IBaseFactory {  
    
		ConfiguracoesEstabelecimentoMarketing Create();    
		ConfiguracoesEstabelecimentoMarketing Create(int idConfiguracaoEstabelecimentoMarketing, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int saldoMarketingSMS, int saldoMarketingEmail, int saldoMarketingEmailBrinde, bool enviarSMSAniversariantes, bool enviarEmailAniversariantes, string conteudoSMSAniversariantes, string conteudoEmailAniversariantes, Perlink.Trinks.Marketing.Enums.TipoMarketingAniversariantesEnum tipoSMSAniversariantes, int? diasAntecedenciaSMS, Perlink.Trinks.Marketing.Enums.TipoMarketingAniversariantesEnum tipoEmailAniversariantes, int? diasAntecedenciaEmail, System.DateTime? dataRecebimentoCortesiaPeloPortal, System.DateTime? dataRecebimentoCortesiaNoCadastro, int? quantidadeRecebimentoCortesiaSmsPeloPortal, int? quantidadeRecebimentoCortesiaSmsNoCadastro, int? quantidadeRecebimentoCortesiaEmailNoCadastro, bool jaRecebeuBrindeExibicaoPortal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetorno.
    /// </summary>
    public partial interface IConviteDeRetornoFactory :IBaseFactory {  
    
		ConviteDeRetorno Create();    
		ConviteDeRetorno Create(int id, bool ativo, System.DateTime? horaEnvio, string conteudo, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Marketing.ConviteDeRetornoParaQuemEnviar> publicosParaQuemMandar, Perlink.Trinks.Marketing.MarketingCampanha marketingCampanha);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoEmail.
    /// </summary>
    public partial interface IConviteDeRetornoEmailFactory :IBaseFactory {  
    
		ConviteDeRetornoEmail Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoParaQuemEnviar.
    /// </summary>
    public partial interface IConviteDeRetornoParaQuemEnviarFactory :IBaseFactory {  
    
		ConviteDeRetornoParaQuemEnviar Create();    
		ConviteDeRetornoParaQuemEnviar Create(int id, bool ativo, Perlink.Trinks.Marketing.ConviteDeRetorno conviteDeRetorno, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, int quantosDiasAtrasOClienteFezOServico, bool queNaoTenhaFeitoOutroServicoNesseTempo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoSMS.
    /// </summary>
    public partial interface IConviteDeRetornoSMSFactory :IBaseFactory {  
    
		ConviteDeRetornoSMS Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoWhatsApp.
    /// </summary>
    public partial interface IConviteDeRetornoWhatsAppFactory :IBaseFactory {  
    
		ConviteDeRetornoWhatsApp Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanha.
    /// </summary>
    public partial interface IMarketingCampanhaFactory :IBaseFactory {  
    
		MarketingCampanha Create();    
		MarketingCampanha Create(bool campanhaEnviada, Perlink.Trinks.Conteudo.ConteudoTexto conteudoCampanha, System.DateTime? dataCriacao, System.DateTime? dataLimiteRecorrencia, bool envioSuspenso, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int idMarketingCampanha, Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvo marketingCampanhaPublicoAlvo, string nomeCampanha, System.DateTime? proximoEnvio, int quantidadeUltimoEnvio, Perlink.Trinks.Marketing.Enums.StatusCampanhaEnum statusCampanha, Perlink.Trinks.Marketing.Enums.TipoRecorrenciaEnum tipoRecorrencia, Perlink.Trinks.Marketing.Enums.MarketingCampanhaDoTrinksEnum? marketingCampanhaDoTrinks);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaEmail.
    /// </summary>
    public partial interface IMarketingCampanhaEmailFactory :IBaseFactory {  
    
		MarketingCampanhaEmail Create();    
		MarketingCampanhaEmail Create(string assuntoCampanha);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaHistorico.
    /// </summary>
    public partial interface IMarketingCampanhaHistoricoFactory :IBaseFactory {  
    
		MarketingCampanhaHistorico Create();    
		MarketingCampanhaHistorico Create(int idMarketingCampanhaHistorico, Perlink.Trinks.Marketing.MarketingCampanha marketingCampanha, System.DateTime dataHora, string descricao, Perlink.Trinks.Marketing.Enums.TipoEnvioEnum tipoEnvio, Perlink.Trinks.Marketing.Enums.TipoCampanhaHistoricoEnum tipoCampanhaHistoricoEnum, Perlink.Trinks.Marketing.MarketingEnvio marketingEnvio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvo.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoFactory :IBaseFactory {  
    
		MarketingCampanhaPublicoAlvo Create();    
		MarketingCampanhaPublicoAlvo Create(int idMarketingCampanhaPublicoAlvo, string sexo, Perlink.Trinks.Marketing.Enums.TipoPublicoAlvoEnum tipoPublicoAlvo, Perlink.Trinks.Marketing.Enums.TipoPublicoAlvoServicoEnum tipoPublicoAlvoServico, Perlink.Trinks.Marketing.Enums.PublicoAlvoTipoDataServicoEnum tipoDataServico, System.Collections.Generic.IList<Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvoServicoEstabelecimento> servicosRealizados, System.Collections.Generic.IList<Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvoClienteEstabelecimento> marketingCampanhaPublicoAlvoClienteEstabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvoProfissional> marketingCampanhaPublicoAlvoProfissional, int? idadeMinima, int? idadeMaxima, int? diasAntecedenciaServicoFeito, System.DateTime? dataInicioServicoRealizado, System.DateTime? dataFimServicoRealizado, System.Collections.Generic.IList<Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo> servicosSemAgendamentoFuturo, bool semAgendamentoFuturoDoServico, int? idServicoNaoRealizado, System.DateTime? periodoUltimaVisitaInicio, System.DateTime? periodoUltimaVisitaFim);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoServicoEstabelecimento.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoServicoEstabelecimentoFactory :IBaseFactory {  
    
		MarketingCampanhaPublicoAlvoServicoEstabelecimento Create();    
		MarketingCampanhaPublicoAlvoServicoEstabelecimento Create(int id, Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvo publicoAlvo, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, bool queNaoTenhaFeitoOutroServico, int? diasAtrasClienteFezOServico);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoFactory :IBaseFactory {  
    
		MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo Create();    
		MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo Create(int id, Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvo publicoAlvo, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoClienteEstabelecimento.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoClienteEstabelecimentoFactory :IBaseFactory {  
    
		MarketingCampanhaPublicoAlvoClienteEstabelecimento Create();    
		MarketingCampanhaPublicoAlvoClienteEstabelecimento Create(int idMarketingCampanhaPublicoAlvoClienteEstabelecimento, Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvo marketingCampanhaPublicoAlvo, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoProfissional.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoProfissionalFactory :IBaseFactory {  
    
		MarketingCampanhaPublicoAlvoProfissional Create();    
		MarketingCampanhaPublicoAlvoProfissional Create(int id, Perlink.Trinks.Marketing.MarketingCampanhaPublicoAlvo publicoAlvo, Perlink.Trinks.Pessoas.Profissional profissional);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaSMS.
    /// </summary>
    public partial interface IMarketingCampanhaSMSFactory :IBaseFactory {  
    
		MarketingCampanhaSMS Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaWhatsApp.
    /// </summary>
    public partial interface IMarketingCampanhaWhatsAppFactory :IBaseFactory {  
    
		MarketingCampanhaWhatsApp Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCompraCredito.
    /// </summary>
    public partial interface IMarketingCompraCreditoFactory :IBaseFactory {  
    
		MarketingCompraCredito Create();    
		MarketingCompraCredito Create(int idMarketingCompraCredito, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Marketing.Enums.TipoEnvioEnum tipoEnvio, bool ativo, Perlink.Trinks.Cobranca.FaturaMarketing faturaMarketing, Perlink.Trinks.Marketing.MarketingPacoteCredito marketingPacoteCredito, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvio.
    /// </summary>
    public partial interface IMarketingEnvioFactory :IBaseFactory {  
    
		MarketingEnvio Create();    
		MarketingEnvio Create(int idMarketingEnvio, System.DateTime dataHoraProgramada, System.DateTime? dataHoraEnvio, int quantidadeEnvios, Perlink.Trinks.Marketing.Enums.StatusMarketingEnvioEnum statusMarketingEnvio, bool faltouCredito, Perlink.Trinks.Marketing.MarketingCampanha marketingCampanha, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Marketing.MarketingEnvio envioOriginal, bool ehPrimeiroEnvio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioCliente.
    /// </summary>
    public partial interface IMarketingEnvioClienteFactory :IBaseFactory {  
    
		MarketingEnvioCliente Create();    
		MarketingEnvioCliente Create(int idMarketingEnvioCliente, Perlink.Trinks.Marketing.MarketingEnvio marketingEnvio, System.DateTime? dataHoraEnvio, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, System.DateTime? dataEmissao, Perlink.Trinks.Conteudo.ConteudoTexto variaveis, Perlink.Trinks.Marketing.MarketingEnvioClienteParametroEnvio parametroEnvio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteEmail.
    /// </summary>
    public partial interface IMarketingEnvioClienteEmailFactory :IBaseFactory {  
    
		MarketingEnvioClienteEmail Create();    
		MarketingEnvioClienteEmail Create(string emailClienteEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteParametroEnvio.
    /// </summary>
    public partial interface IMarketingEnvioClienteParametroEnvioFactory :IBaseFactory {  
    
		MarketingEnvioClienteParametroEnvio Create();    
		MarketingEnvioClienteParametroEnvio Create(int idMarketingEnvioCliente, int idPublicoAlvoServicoEstabelecimento, int idServicoEstabelecimento, int diasAtras, bool queNaoTenhaFeitoOutroServico, Perlink.Trinks.Marketing.MarketingEnvioCliente marketingEnvioCliente, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteSMS.
    /// </summary>
    public partial interface IMarketingEnvioClienteSMSFactory :IBaseFactory {  
    
		MarketingEnvioClienteSMS Create();    
		MarketingEnvioClienteSMS Create(string ddiTelefone, string dDDTelefone, string idMensagemGateway, string statusGateway, string numeroTelefone, Perlink.Trinks.Notificacoes.Operadora operadora);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteWhatsApp.
    /// </summary>
    public partial interface IMarketingEnvioClienteWhatsAppFactory :IBaseFactory {  
    
		MarketingEnvioClienteWhatsApp Create();    
		MarketingEnvioClienteWhatsApp Create(string ddiTelefone, string dDDTelefone, string idMensagemGateway, string statusGateway, string numeroTelefone);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioEmail.
    /// </summary>
    public partial interface IMarketingEnvioEmailFactory :IBaseFactory {  
    
		MarketingEnvioEmail Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioSMS.
    /// </summary>
    public partial interface IMarketingEnvioSMSFactory :IBaseFactory {  
    
		MarketingEnvioSMS Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioWhatsApp.
    /// </summary>
    public partial interface IMarketingEnvioWhatsAppFactory :IBaseFactory {  
    
		MarketingEnvioWhatsApp Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingFaixaProfissionais.
    /// </summary>
    public partial interface IMarketingFaixaProfissionaisFactory :IBaseFactory {  
    
		MarketingFaixaProfissionais Create();    
		MarketingFaixaProfissionais Create(int idMarketingFaixaProfissionais, int? minimoProfissionais, int? maximoProfissionais, int brindeMarketingSMSCadastro, int brindeMarketingSMSBuscaPortal, int brindeMarketingEmailMensal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingPacoteCredito.
    /// </summary>
    public partial interface IMarketingPacoteCreditoFactory :IBaseFactory {  
    
		MarketingPacoteCredito Create();    
		MarketingPacoteCredito Create(int idMarketingPacoteCredito, Perlink.Trinks.Marketing.Enums.TipoEnvioEnum tipoEnvio, int quantidade, decimal valor, bool ativo, int quantidadePadrao);
			 
    }
 
}
namespace Perlink.Trinks.Marketing.DTO.Factories {

 
}
namespace Perlink.Trinks.Marketing.Enums.Factories {

 
}
namespace Perlink.Trinks.Marketing.Factories {

 
}
namespace Perlink.Trinks.Marketing.Factories.Factories {

 
}
namespace Perlink.Trinks.Marketing.Filtro.Factories {

 
}
namespace Perlink.Trinks.Marketing.Filtros.Factories {

 
}
namespace Perlink.Trinks.Marketing.Strategies.Factories {

 
}
namespace Perlink.Trinks.MarketingInterno.Factories {

    /// <summary>
    /// Interface para repositório da entidade DadosMarketing.
    /// </summary>
    public partial interface IDadosMarketingFactory :IBaseFactory {  
    
		DadosMarketing Create();    
		DadosMarketing Create(int id, int idPessoa, string utmSource, string utmMedium, string utmCampaign, string utmTerm, string utmContent, string dispositivo, string landingUrl, string referrer, string firstClickUrl, System.DateTime? firstClickUrlDate, string firstLandingUrl, System.DateTime? firstLandingUrlDate, string gclid);
			 
    }
 
}
namespace Perlink.Trinks.MensagemEmTela.DTOs.Factories {

 
}
namespace Perlink.Trinks.MensagemEmTela.Implementacoes.Factories {

 
}
namespace Perlink.Trinks.MensagemEmTela.Factories {

    /// <summary>
    /// Interface para repositório da entidade MensagemAviso.
    /// </summary>
    public partial interface IMensagemAvisoFactory :IBaseFactory {  
    
		MensagemAviso Create();    
		MensagemAviso Create(int id, string ativo, string textoDispensar, int diasParaNotificarNovamente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MensagemAvisoTextoLivre.
    /// </summary>
    public partial interface IMensagemAvisoTextoLivreFactory :IBaseFactory {  
    
		MensagemAvisoTextoLivre Create();    
		MensagemAvisoTextoLivre Create(string conteudo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MensagemAvisoImplementacao.
    /// </summary>
    public partial interface IMensagemAvisoImplementacaoFactory :IBaseFactory {  
    
		MensagemAvisoImplementacao Create();    
		MensagemAvisoImplementacao Create(string classeImplementacao);
			 
    }
 
}
namespace Perlink.Trinks.MensagensEmMassa.Factories {

    /// <summary>
    /// Interface para repositório da entidade ModeloDeMensagem.
    /// </summary>
    public partial interface IModeloDeMensagemFactory :IBaseFactory {  
    
		ModeloDeMensagem Create();    
		ModeloDeMensagem Create(int id, string identificadorDeUsoExterno, int? minutosDeAtrasoParaEnvio, System.DateTime? dataDeEnvio, Perlink.GatewayMensagens.Enums.TipoDeServicoDeMensagem tipoDeServicoDeMensagem, string sQLDeBuscaDosDestinatarios, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.MessageQueue.Factories {

 
}
namespace Perlink.Trinks.Metricas.Dto.Factories {

 
}
namespace Perlink.Trinks.Metricas.Enums.Factories {

 
}
namespace Perlink.Trinks.Metricas.Factories {

    /// <summary>
    /// Interface para repositório da entidade MetricaDesativada.
    /// </summary>
    public partial interface IMetricaDesativadaFactory :IBaseFactory {  
    
		MetricaDesativada Create();    
		MetricaDesativada Create(int id, string tag, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDeNFCDoEstabelecimento.
    /// </summary>
    public partial interface IConfiguracaoDeNFCDoEstabelecimentoFactory :IBaseFactory {  
    
		ConfiguracaoDeNFCDoEstabelecimento Create();    
		ConfiguracaoDeNFCDoEstabelecimento Create(int idEstabelecimento, Perlink.Trinks.NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC interfaceNFC, int? numeroDaUltimaNFC, int ultimaNfcHml, int numeroSerieNFC, int? numeroDaLoja, int? numeroECF, int? versaoProdutoServico, Perlink.Trinks.NotaFiscalDoConsumidor.TipoRegimeTributarioNFC tipoRegimeTributario, string identificadorCSC, string cSC, int numeroDoUltimoCFEmitido, bool nFCProducao, int tipoIntegracaoPagamento, bool incluirServicosNaNota, bool? indIncentivo, bool? utilizarComissaoProfissionalComoCota, bool? enviarPisConfinsSysPDV, int? indIss, string cnpjAlternativo, string razaoSocialAlternativa, Perlink.Trinks.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimento.ConfiguracoesSAT sAT, string itemListaServico, decimal aliquotaISS);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoNFCEstado.
    /// </summary>
    public partial interface IConfiguracaoNFCEstadoFactory :IBaseFactory {  
    
		ConfiguracaoNFCEstado Create();    
		ConfiguracaoNFCEstado Create(int idConfiguracaoNFCEstado, Perlink.Trinks.Pessoas.UF uF, bool nFCEImplementada, string observacaoCorpoNFCE, string urlConsultaNFCe, bool sincrono, string textoComplementarSimplesNacional, string textoComplementarLucroPresumido);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ImpressaoDeNFC.
    /// </summary>
    public partial interface IImpressaoDeNFCFactory :IBaseFactory {  
    
		ImpressaoDeNFC Create();    
		ImpressaoDeNFC Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Financeiro.Transacao transacao, string idEstacao, string tipoRegistro, string nomeDoArquivoDeImpressao, string conteudoDoArquivo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NfcSituacaoTributaria.
    /// </summary>
    public partial interface INfcSituacaoTributariaFactory :IBaseFactory {  
    
		NfcSituacaoTributaria Create();    
		NfcSituacaoTributaria Create(int id, int idInterfaceNFC, string codigo, string produtoOuServico, string descricao, bool ehTributado, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NomenclaturaNCMeNBS.
    /// </summary>
    public partial interface INomenclaturaNCMeNBSFactory :IBaseFactory {  
    
		NomenclaturaNCMeNBS Create();    
		NomenclaturaNCMeNBS Create(int id, string codigoNcmNbs, char produtoOuServico, string descricao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotaFormaPagamentoNFC.
    /// </summary>
    public partial interface INotaFormaPagamentoNFCFactory :IBaseFactory {  
    
		NotaFormaPagamentoNFC Create();    
		NotaFormaPagamentoNFC Create(int idNotaFormaPagamentoNFC, Perlink.Trinks.NotaFiscalDoConsumidor.NotaNFC notaNFC, Perlink.Trinks.NotaFiscalDoConsumidor.Enums.TipoFormaPagamentoNFCeEnum? formaPagamento, decimal valorPagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotaInutilizadaNFC.
    /// </summary>
    public partial interface INotaInutilizadaNFCFactory :IBaseFactory {  
    
		NotaInutilizadaNFC Create();    
		NotaInutilizadaNFC Create(int idNotaInutilizadaNFC, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Financeiro.Transacao transacao, System.DateTime dataVenda, int numeroNota, int numeroNotaFinal, int numeroSerie, string mensagemErro, string retornoInutilizacaoSEFAZ, bool numeroFoiInutilizado, bool nFCProducao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotaItensNFC.
    /// </summary>
    public partial interface INotaItensNFCFactory :IBaseFactory {  
    
		NotaItensNFC Create();    
		NotaItensNFC Create(int idNotaNFC, Perlink.Trinks.NotaFiscalDoConsumidor.NotaNFC notaNFC, int sequencialProduto, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, int? codigoHorarioTransacao, decimal quantidade, decimal precoUnitario, decimal descontoUnitario, decimal valorBrutoTotal, decimal valorLiquidoTotal, string descricaoCotaParte, decimal desconto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotaNFC.
    /// </summary>
    public partial interface INotaNFCFactory :IBaseFactory {  
    
		NotaNFC Create();    
		NotaNFC Create(int idNotaNFC, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Financeiro.Transacao transacao, int numeroNota, int numeroSerie, System.DateTime dataVenda, Perlink.Trinks.NotaFiscalDoConsumidor.StatusNotaNFC statusNota, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, decimal totalBruto, decimal totalDesconto, decimal totalLiquido, string autorizacaoSEFAZ, string loteSEFAZ, string reciboSEFAZ, string protocoloSEFAZ, bool nFCProducao, System.DateTime? dataEmissao, System.DateTime? dataRecebimentoSEFAZ, string digestValue, bool cancelamentoFoiRealizado, string urlDoQRCode, string textoDeInformacaoComplementar, string mensagemDeErroAoEmitirNota, System.Collections.Generic.IList<Perlink.Trinks.NotaFiscalDoConsumidor.NotaItensNFC> itens, System.Collections.Generic.IList<Perlink.Trinks.NotaFiscalDoConsumidor.NotaFormaPagamentoNFC> formasPagamento, Perlink.Trinks.Pessoas.PessoaFisica ultimaPessoaQueEmitiuSAT, System.DateTime? dataContingenciaParaHomologacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaCertificadoDigital.
    /// </summary>
    public partial interface IPessoaJuridicaCertificadoDigitalFactory :IBaseFactory {  
    
		PessoaJuridicaCertificadoDigital Create();    
		PessoaJuridicaCertificadoDigital Create(int id, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, string serialCertificado, System.DateTime? dataInicioSerialCertificado, System.DateTime? dataFimSerialCertificado, string senhaCertificado, string senhaCertificadoCriptografada, bool ehCnpjAlternativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade StatusNotaNFC.
    /// </summary>
    public partial interface IStatusNotaNFCFactory :IBaseFactory {  
    
		StatusNotaNFC Create();    
		StatusNotaNFC Create(int idStatusNotaNFC, Perlink.Trinks.NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC interfaceNFC, string status);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TabelaIBPT.
    /// </summary>
    public partial interface ITabelaIBPTFactory :IBaseFactory {  
    
		TabelaIBPT Create();    
		TabelaIBPT Create(int id, Perlink.Trinks.Pessoas.UF uf, int tipo, string descricao, string codigo, decimal aliquotaFederalNacional, decimal aliquotaFederalImportado, decimal aliquotaEstadual, decimal aliquotaMunicipal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoRegimeTributarioNFC.
    /// </summary>
    public partial interface ITipoRegimeTributarioNFCFactory :IBaseFactory {  
    
		TipoRegimeTributarioNFC Create();    
		TipoRegimeTributarioNFC Create(int idTipoRegimeTributarioNFC, Perlink.Trinks.NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC interfaceNFC, string codigo, string descricao);
			 
    }
 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.DTO.Factories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Enums.Factories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Exceptions.Factories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Factories.Factories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF.Factories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF.SAT.Cancelamento.Factories {

 
}
namespace Perlink.Trinks.Web.Areas.BackOffice.Models.Produtos.Factories {

 
}
namespace Perlink.Trinks.Notificacoes.Factories {

    /// <summary>
    /// Interface para repositório da entidade CampanhaPush.
    /// </summary>
    public partial interface ICampanhaPushFactory :IBaseFactory {  
    
		CampanhaPush Create();    
		CampanhaPush Create(int idCampanhaPush, Perlink.Trinks.Pessoas.Franquia franquia, int idContaAutenticada, string titulo, string mensagem, System.DateTime dataHoraCriacao, int quantidadeDeDestinatarios, Perlink.Trinks.Notificacoes.Enums.CampanhaPushStatus status, string mensagemDeStatus, bool ativo, int? quantidadeDestinatariosApple, int? quantidadeDestinatariosGoogle, System.DateTime? dataHoraConsolidacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaQtdNotificacoesNovas.
    /// </summary>
    public partial interface IContaQtdNotificacoesNovasFactory :IBaseFactory {  
    
		ContaQtdNotificacoesNovas Create();    
		ContaQtdNotificacoesNovas Create(int id, int idConta, int qtdNotificacoesNovas, int idAplicativoDeAgendamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Dispositivo.
    /// </summary>
    public partial interface IDispositivoFactory :IBaseFactory {  
    
		Dispositivo Create();    
		Dispositivo Create(int idDispositivo, string uUID, string deviceToken, Perlink.Trinks.Pessoas.Conta conta, System.DateTime dataHoraRegistro, System.DateTime dataHoraUltimaAtualizacao, Perlink.Trinks.Notificacoes.Enums.TipoPlataformaEnum plataforma, string versaoPlataforma, Perlink.Trinks.TrinksApps.AplicativoDeAgendamento aplicativo, string versaoDoAplicativo, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DispositivoB2cValido.
    /// </summary>
    public partial interface IDispositivoB2cValidoFactory :IBaseFactory {  
    
		DispositivoB2cValido Create();    
		DispositivoB2cValido Create(int idDispositivo, int idAplicativoDeAgendamento, Perlink.Trinks.Notificacoes.Enums.TipoPlataformaEnum plataforma, int? idConta, string deviceToken);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade InscricaoEmNotificacao.
    /// </summary>
    public partial interface IInscricaoEmNotificacaoFactory :IBaseFactory {  
    
		InscricaoEmNotificacao Create();    
		InscricaoEmNotificacao Create(int id, int? idPessoaJuridica, int idPessoaDestinatario, Perlink.Trinks.Notificacoes.TipoNotificacaoEnum tipo, Perlink.Trinks.Notificacoes.Enums.NotificacaoEnum notificacao, bool receberNotificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotificacaoDoTrinks.
    /// </summary>
    public partial interface INotificacaoDoTrinksFactory :IBaseFactory {  
    
		NotificacaoDoTrinks Create();    
		NotificacaoDoTrinks Create(int id, System.DateTime dataRegistro, System.DateTime? dataEnvioParaFila, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.Pessoas.Pessoa pessoaQueRecebeu, Perlink.Trinks.Notificacoes.ServicoSMS servicoSMS, string telefoneDDD, string telefoneNumero, string conteudo, Perlink.Trinks.Notificacoes.TipoNotificacao tipoNotificacao, Perlink.Trinks.EnvioMensagem.Enums.OrigemMensagemEnum origem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotificacaoPush.
    /// </summary>
    public partial interface INotificacaoPushFactory :IBaseFactory {  
    
		NotificacaoPush Create();    
		NotificacaoPush Create(int idNotificacaoPush, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int idPessoa, int idConta, string tituloNotificacao, string textoNotificacao, System.DateTime dataHoraCriacao, int? idHorario, int? idRecorrencia, Perlink.Trinks.Notificacoes.Enums.AcaoNotificacaoPushEnum acaoNotificacaoPushEnum, string parametro, Perlink.Trinks.TrinksApps.AplicativoDeAgendamento aplicativoDeAgendamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Operadora.
    /// </summary>
    public partial interface IOperadoraFactory :IBaseFactory {  
    
		Operadora Create();    
		Operadora Create(int id, string nome, string urlImagem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OperadoraServicoSMS.
    /// </summary>
    public partial interface IOperadoraServicoSMSFactory :IBaseFactory {  
    
		OperadoraServicoSMS Create();    
		OperadoraServicoSMS Create(int id, Perlink.Trinks.Notificacoes.Operadora operadora, Perlink.Trinks.Notificacoes.ServicoSMS servicoSMS);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RegistroNotificacao.
    /// </summary>
    public partial interface IRegistroNotificacaoFactory :IBaseFactory {  
    
		RegistroNotificacao Create();    
		RegistroNotificacao Create(int id, Perlink.Trinks.Notificacoes.TipoNotificacao tipoNotificacao, System.DateTime dataEnvio, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.Notificacoes.ServicoSMS servicoSMS, string idMensagem, string telefoneDDD, string telefoneNumero, string conteudo, string statusEnvio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoSMS.
    /// </summary>
    public partial interface IServicoSMSFactory :IBaseFactory {  
    
		ServicoSMS Create();    
		ServicoSMS Create(int id, string nome, string url, bool ehPadrao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoNotificacao.
    /// </summary>
    public partial interface ITipoNotificacaoFactory :IBaseFactory {  
    
		TipoNotificacao Create();    
		TipoNotificacao Create(int id, string nome);
			 
    }
 
}
namespace Perlink.Trinks.Notificacoes.DTO.Factories {

 
}
namespace Perlink.Trinks.Notificacoes.Enums.Factories {

 
}
namespace Perlink.Trinks.Notificacoes.Filtros.Factories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Factories {

    /// <summary>
    /// Interface para repositório da entidade CanalDaNotificacao.
    /// </summary>
    public partial interface ICanalDaNotificacaoFactory :IBaseFactory {  
    
		CanalDaNotificacao Create();    
		CanalDaNotificacao Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EventoDeNotificacao.
    /// </summary>
    public partial interface IEventoDeNotificacaoFactory :IBaseFactory {  
    
		EventoDeNotificacao Create();    
		EventoDeNotificacao Create(int id, int idEstabelecimento, int idPessoa, Perlink.Trinks.NotificacoesApps.TipoDeNotificacao tipo, System.DateTime dataHoraProgramado, System.DateTime? dataHoraEnvio, int quantidadeDeEnviosRealizados, string parametros, bool jaFoiLida);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MensagemDeNotificacao.
    /// </summary>
    public partial interface IMensagemDeNotificacaoFactory :IBaseFactory {  
    
		MensagemDeNotificacao Create();    
		MensagemDeNotificacao Create(int id, string titulo, string mensagem, Perlink.Trinks.NotificacoesApps.CanalDaNotificacao canal, Perlink.Trinks.NotificacoesApps.EventoDeNotificacao eventoDeNotificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreferenciaDeNotificacaoDoUsuario.
    /// </summary>
    public partial interface IPreferenciaDeNotificacaoDoUsuarioFactory :IBaseFactory {  
    
		PreferenciaDeNotificacaoDoUsuario Create();    
		PreferenciaDeNotificacaoDoUsuario Create(int id, bool desejaSilenciar, int idPessoa, Perlink.Trinks.NotificacoesApps.TipoDeNotificacao tipo, Perlink.Trinks.NotificacoesApps.CanalDaNotificacao canal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDeNotificacao.
    /// </summary>
    public partial interface ITipoDeNotificacaoFactory :IBaseFactory {  
    
		TipoDeNotificacao Create();    
		TipoDeNotificacao Create(int id, string nome, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.NotificacoesApps.DTO.Factories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Factories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Strategies.Factories {

 
}
namespace Perlink.Trinks.Novidades.DTO.Factories {

 
}
namespace Perlink.Trinks.Novidades.Factories {

 
}
namespace Perlink.Trinks.Onboardings.Comparers.Factories {

 
}
namespace Perlink.Trinks.Onboardings.DTOs.Factories {

 
}
namespace Perlink.Trinks.Onboardings.Enums.Factories {

 
}
namespace Perlink.Trinks.Onboardings.Factories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoTrilha.
    /// </summary>
    public partial interface IEstabelecimentoTrilhaFactory :IBaseFactory {  
    
		EstabelecimentoTrilha Create();    
		EstabelecimentoTrilha Create(int idEstabelecimentoTrilha, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Onboardings.Enums.ETrilhaOnboarding? trilhaSelecionada, Perlink.Trinks.Onboardings.Enums.ETrilhaOnboarding? trilhaRecomendada, bool naoEncontrouTrilha);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioOnboardingPorFaixa.
    /// </summary>
    public partial interface IQuestionarioOnboardingPorFaixaFactory :IBaseFactory {  
    
		QuestionarioOnboardingPorFaixa Create();    
		QuestionarioOnboardingPorFaixa Create(int idQuestionarioOnboardingPorFaixa, Perlink.Trinks.InternoProduto.QuestionarioProduto questionario, Perlink.Trinks.Estabelecimentos.Enums.FaixaProfissionaisEstabelecimentoEnum faixaDeProfissionais, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RastreioDeTarefa.
    /// </summary>
    public partial interface IRastreioDeTarefaFactory :IBaseFactory {  
    
		RastreioDeTarefa Create();    
		RastreioDeTarefa Create(int id, int idEstabelecimento, Perlink.Trinks.Onboardings.Tarefa tarefaOnboarding, int qtdRealizada, bool necessitaNotificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Tarefa.
    /// </summary>
    public partial interface ITarefaFactory :IBaseFactory {  
    
		Tarefa Create();    
		Tarefa Create(int id, string descricao, string descricaoCurta, int qtdNecessaria, bool ativo, int ordem, Perlink.Trinks.Onboardings.Enums.ETrilhaOnboarding? trilha, Perlink.Trinks.Onboardings.Enums.ETrilhaAcaoApp acaoApp, string acaoParam, string acaoNome, string webUrl, Perlink.Trinks.Onboardings.Enums.ETrilhaWebUrlTarget webUrlTarget);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Trilha.
    /// </summary>
    public partial interface ITrilhaFactory :IBaseFactory {  
    
		Trilha Create();    
		Trilha Create(Perlink.Trinks.Onboardings.Enums.ETrilhaOnboarding idTrilha, string titulo, string descricao, string textoPaginaTrilha, string icone, string webImageUrl, string appImageResourceName, System.Collections.Generic.IList<Perlink.Trinks.Onboardings.TrilhaAcaoGrupo> gruposAcoes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TrilhaAcao.
    /// </summary>
    public partial interface ITrilhaAcaoFactory :IBaseFactory {  
    
		TrilhaAcao Create();    
		TrilhaAcao Create(int id, string titulo, Perlink.Trinks.Onboardings.Enums.ETrilhaAcaoApp acaoApp, string acaoParam, string urlWeb, string iconeWeb, string iconeApp, bool promovidoAHome, Perlink.Trinks.Onboardings.TrilhaAcaoGrupo grupo, Perlink.Trinks.Onboardings.Enums.ETrilhaWebUrlTarget webUrlTarget);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TrilhaAcaoGrupo.
    /// </summary>
    public partial interface ITrilhaAcaoGrupoFactory :IBaseFactory {  
    
		TrilhaAcaoGrupo Create();    
		TrilhaAcaoGrupo Create(int id, string nome, string cor, Perlink.Trinks.Onboardings.Trilha trilha, System.Collections.Generic.IList<Perlink.Trinks.Onboardings.TrilhaAcao> acoes);
			 
    }
 
}
namespace Perlink.Trinks.Onboardings.Stories.Factories {

 
}
namespace Perlink.Trinks.Pacotes.Adapters.Factories {

 
}
namespace Perlink.Trinks.Pacotes.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoPacotePersonalizado.
    /// </summary>
    public partial interface IConfiguracaoPacotePersonalizadoFactory :IBaseFactory {  
    
		ConfiguracaoPacotePersonalizado Create();    
		ConfiguracaoPacotePersonalizado Create(int id, int idEstabelecimento, int? validadeDeUsoEmMeses, decimal? percentualComissaoProfissional, bool estabelecerValidade, bool permiteEditarValorDeVenda);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoConfiguracoesPacote.
    /// </summary>
    public partial interface IHistoricoConfiguracoesPacoteFactory :IBaseFactory {  
    
		HistoricoConfiguracoesPacote Create();    
		HistoricoConfiguracoesPacote Create(int id, Perlink.Trinks.Pacotes.Pacote pacote, int idPessoaQueAlterou, Perlink.Trinks.Pacotes.Enums.TipoConfiguracaoPacoteEnum tipo, System.DateTime dataAlteracao, string descricaoAlteracao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacote.
    /// </summary>
    public partial interface IItemPacoteFactory :IBaseFactory {  
    
		ItemPacote Create();    
		ItemPacote Create(int id, Perlink.Trinks.Pacotes.Pacote pacote, decimal valorUnitario, decimal valorUnitarioFiscal, int quantidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteCliente.
    /// </summary>
    public partial interface IItemPacoteClienteFactory :IBaseFactory {  
    
		ItemPacoteCliente Create();    
		ItemPacoteCliente Create(int id, Perlink.Trinks.Pacotes.PacoteCliente pacoteCliente, decimal valorUnitario, decimal valorUnitarioFiscal, int quantidade, int quantidadeConsumida);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteClienteProduto.
    /// </summary>
    public partial interface IItemPacoteClienteProdutoFactory :IBaseFactory {  
    
		ItemPacoteClienteProduto Create();    
		ItemPacoteClienteProduto Create(Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteClienteServico.
    /// </summary>
    public partial interface IItemPacoteClienteServicoFactory :IBaseFactory {  
    
		ItemPacoteClienteServico Create();    
		ItemPacoteClienteServico Create(Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteProduto.
    /// </summary>
    public partial interface IItemPacoteProdutoFactory :IBaseFactory {  
    
		ItemPacoteProduto Create();    
		ItemPacoteProduto Create(Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteServico.
    /// </summary>
    public partial interface IItemPacoteServicoFactory :IBaseFactory {  
    
		ItemPacoteServico Create();    
		ItemPacoteServico Create(Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDoPacote.
    /// </summary>
    public partial interface ILinkDePagamentoDoPacoteFactory :IBaseFactory {  
    
		LinkDePagamentoDoPacote Create();    
		LinkDePagamentoDoPacote Create(int id, int idLinkDePagamento, int idPacote, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Pacote.
    /// </summary>
    public partial interface IPacoteFactory :IBaseFactory {  
    
		Pacote Create();    
		Pacote Create(bool ativo, string descricao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int id, System.Collections.Generic.IList<Perlink.Trinks.Pacotes.ItemPacote> itensPacote, string nome, Perlink.Trinks.Pacotes.Pacote pacoteModelo, decimal valor, bool exibePacote, bool vendaHotsite, int? validadeDeUsoEmMeses, bool ehPacoteDeAssinatura, decimal? percentualComissaoProfissional, int? idPacoteReferencia, bool compartilhadoNaRede, Perlink.Trinks.Pacotes.ItemPacote itemQueRepresentaOsDadosDeNotaFiscal, decimal? aliquotaICMSDoPrimeiroItemComNomenclatura, string codigoNCM, int? idSituacaoTributaria, bool ehNacionalParaNotaFiscal, bool clienteGanhaPontos, bool clientePodeResgatarPontos, int pontosNecessariosParaResgate, bool ehPersonalizado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteCliente.
    /// </summary>
    public partial interface IPacoteClienteFactory :IBaseFactory {  
    
		PacoteCliente Create();    
		PacoteCliente Create(int id, System.Collections.Generic.IList<Perlink.Trinks.Pacotes.ItemPacoteCliente> itensPacoteCliente, string nome, decimal valor, Perlink.Trinks.Pessoas.Cliente cliente, Perlink.Trinks.Pacotes.Pacote pacoteOriginal, Perlink.Trinks.Vendas.Venda venda, bool ativo, Perlink.Trinks.Pacotes.Enums.TipoNFPacoteEnum configuracaoNF, System.DateTime? dataValidade, System.Collections.Generic.IList<Perlink.Trinks.Pacotes.PacoteClienteHistorico> historico);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteClienteHistorico.
    /// </summary>
    public partial interface IPacoteClienteHistoricoFactory :IBaseFactory {  
    
		PacoteClienteHistorico Create();    
		PacoteClienteHistorico Create(int id, Perlink.Trinks.Pacotes.PacoteCliente pacoteCliente, Perlink.Trinks.Pacotes.PacoteClienteEvento evento, System.DateTime dataHoraEvento, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizou, string motivo);
			 
    }
 
}
namespace Perlink.Trinks.Pacotes.DTO.Factories {

 
}
namespace Perlink.Trinks.Pacotes.Enums.Factories {

 
}
namespace Perlink.Trinks.Pacotes.Factories.Factories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.DTO.Factories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Enums.Factories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Factories {

    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipadoHotsiteConfiguracoes.
    /// </summary>
    public partial interface IPagamentoAntecipadoHotsiteConfiguracoesFactory :IBaseFactory {  
    
		PagamentoAntecipadoHotsiteConfiguracoes Create();    
		PagamentoAntecipadoHotsiteConfiguracoes Create(int id, int idRecebedor, bool habilitaPagamentoAntecipadoServicoHotsite, System.DateTime dataAlteracao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipadoHotsiteServicos.
    /// </summary>
    public partial interface IPagamentoAntecipadoHotsiteServicosFactory :IBaseFactory {  
    
		PagamentoAntecipadoHotsiteServicos Create();    
		PagamentoAntecipadoHotsiteServicos Create(int id, int idRecebedor, int idServicoEstabelecimento, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.Pagamentos.Calculos.Factories {

 
}
namespace Perlink.Trinks.Pagamentos.Factories {

    /// <summary>
    /// Interface para repositório da entidade CartaoDeComprador.
    /// </summary>
    public partial interface ICartaoDeCompradorFactory :IBaseFactory {  
    
		CartaoDeComprador Create();    
		CartaoDeComprador Create(int idCartaoDeComprador, Perlink.Trinks.Pagamentos.Comprador comprador, Perlink.Pagamentos.Gateways.Enums.BandeiraDoCartaoEnum bandeira, string nomeDoTitular, string ultimosQuatroDigitos, int mesExpiracao, int anoExpiracao, bool ativo, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CartaoDeCompradorGateway.
    /// </summary>
    public partial interface ICartaoDeCompradorGatewayFactory :IBaseFactory {  
    
		CartaoDeCompradorGateway Create();    
		CartaoDeCompradorGateway Create(int idCompradorGatewayCartao, Perlink.Trinks.Pagamentos.CompradorGateway compradorGateway, Perlink.Trinks.Pagamentos.CartaoDeComprador cartao, string idNoGateway, string fingerprint, bool ativo, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Comprador.
    /// </summary>
    public partial interface ICompradorFactory :IBaseFactory {  
    
		Comprador Create();    
		Comprador Create(int idComprador, string primeiroNome, string ultimoNome, string email, string telefone, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CompradorGateway.
    /// </summary>
    public partial interface ICompradorGatewayFactory :IBaseFactory {  
    
		CompradorGateway Create();    
		CompradorGateway Create(int idCompradorGateway, Perlink.Trinks.Pagamentos.Comprador comprador, Perlink.Trinks.Pagamentos.Gateway gateway, string idNoGateway, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancaria.
    /// </summary>
    public partial interface IContaBancariaFactory :IBaseFactory {  
    
		ContaBancaria Create();    
		ContaBancaria Create(int idContaBancaria, Perlink.Trinks.Pagamentos.Recebedor recebedor, string nomeTitular, string cNPJ, Perlink.Pagamentos.Gateways.Enums.TipoDeContaBancariaEnum tipoDeContaBancaria, string codigoDoBanco, string agencia, string agenciaDV, string numeroConta, string numeroContaDV, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancariaGateway.
    /// </summary>
    public partial interface IContaBancariaGatewayFactory :IBaseFactory {  
    
		ContaBancariaGateway Create();    
		ContaBancariaGateway Create(int idContaBancariaGateway, Perlink.Trinks.Pagamentos.ContaBancaria contaBancaria, Perlink.Trinks.Pagamentos.Gateway gateway, string idDaContaNoGateway, string fingerprint, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DocumentoDeRecebedorCredenciado.
    /// </summary>
    public partial interface IDocumentoDeRecebedorCredenciadoFactory :IBaseFactory {  
    
		DocumentoDeRecebedorCredenciado Create();    
		DocumentoDeRecebedorCredenciado Create(int idDocumentoDeRecebedorCredenciado, Perlink.Trinks.Pagamentos.RecebedorCredenciado recebedorCredenciado, string idDocumentoNoGateway, string categoria, int tamanhoEmBytes, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EnderecoDeCobranca.
    /// </summary>
    public partial interface IEnderecoDeCobrancaFactory :IBaseFactory {  
    
		EnderecoDeCobranca Create();    
		EnderecoDeCobranca Create(int id, int idCompradorCartao, Perlink.Trinks.Pagamentos.Endereco endereco);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EtapaCadastroPagarme.
    /// </summary>
    public partial interface IEtapaCadastroPagarmeFactory :IBaseFactory {  
    
		EtapaCadastroPagarme Create();    
		EtapaCadastroPagarme Create(int id, int idEstabelecimento, Perlink.Trinks.Pagamentos.Enums.StatusContaPagarmeEnum etapaAtual, System.DateTime dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Gateway.
    /// </summary>
    public partial interface IGatewayFactory :IBaseFactory {  
    
		Gateway Create();    
		Gateway Create(int idGateway, string nome);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPagamento.
    /// </summary>
    public partial interface IItemPagamentoFactory :IBaseFactory {  
    
		ItemPagamento Create();    
		ItemPagamento Create(int idItemPagamento, Perlink.Trinks.Pagamentos.Pagamento pagamento, string nome, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LimitePagamento.
    /// </summary>
    public partial interface ILimitePagamentoFactory :IBaseFactory {  
    
		LimitePagamento Create();    
		LimitePagamento Create(int id, string name, int limitePagamentoDiarioEmCentavos, int limitePagamentoMensalEmCentavos, int limiteAntecipacaoDiarioEmCentavos, int limiteAntecipacaoMensalEmCentavos, int? idPessoaQueAlterou, System.DateTime? dataUltimaAlteracao, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LimitePagamentoRecebedor.
    /// </summary>
    public partial interface ILimitePagamentoRecebedorFactory :IBaseFactory {  
    
		LimitePagamentoRecebedor Create();    
		LimitePagamentoRecebedor Create(int idRecebedor, Perlink.Trinks.Pagamentos.LimitePagamento limite, int? idPessoaQueAlterou, System.DateTime? dataUltimaAlteracao, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Pagamento.
    /// </summary>
    public partial interface IPagamentoFactory :IBaseFactory {  
    
		Pagamento Create();    
		Pagamento Create(int idPagamento, int idRecebedorCredenciado, int? idCompradorGatewayCartao, decimal valor, int quantidadeParcelas, Perlink.Trinks.Pagamentos.Enums.StatusPagamentoEnum statusPagamento, System.DateTime? dataPagamento, string nomeDeExibicaoNaFatura, bool pagamentoRealizado, string statusGateway, string motivoStatusGateway, string motivoRejeicaoGateway, string idTransacaoGateway, decimal? custoTransacaoGateway, System.DateTime? dataPrevisaoRecebimento, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, System.Collections.Generic.IList<Perlink.Trinks.Pagamentos.ItemPagamento> itens, System.Collections.Generic.IList<Perlink.Trinks.Pagamentos.SplitPagamento> regrasDeSplit, string idCobrancaGateway, string referenciaDeOrigem, Perlink.Pagamentos.Gateways.Enums.MetodoDePagamentoNoGatewayEnum? metodoPagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Recebedor.
    /// </summary>
    public partial interface IRecebedorFactory :IBaseFactory {  
    
		Recebedor Create();    
		Recebedor Create(int idRecebedor, string nome, string cNPJ, string telefone, string email, string website, Perlink.Trinks.Pagamentos.Endereco endereco, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, System.DateTime? dataDeAberturaDaEmpresa, bool? permiteAntecipacao, System.Collections.Generic.IList<Perlink.Trinks.Pagamentos.RecebedorCredenciado> recebedoresCredenciados);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RecebedorCredenciado.
    /// </summary>
    public partial interface IRecebedorCredenciadoFactory :IBaseFactory {  
    
		RecebedorCredenciado Create();    
		RecebedorCredenciado Create(int idRecebedorCredenciado, Perlink.Trinks.Pagamentos.Gateway gateway, Perlink.Trinks.Pagamentos.Recebedor recebedor, string idRecebedorNoGateway, bool habilitado, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, string statusGateway, string motivoStatusGateway, string statusKyc, string motivoStatusKyc);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SplitPagamento.
    /// </summary>
    public partial interface ISplitPagamentoFactory :IBaseFactory {  
    
		SplitPagamento Create();    
		SplitPagamento Create(int idSplitPagamento, Perlink.Trinks.Pagamentos.Pagamento pagamento, int idRecebedorCredenciado, bool responsavelPelasTaxasDaOperacao, bool responsavelPeloChargeback, bool responsavelPeloRestoDaDivisaoDasTaxas, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimiteAntecipacaoDiarioRecebedor.
    /// </summary>
    public partial interface IUsoLimiteAntecipacaoDiarioRecebedorFactory :IBaseFactory {  
    
		UsoLimiteAntecipacaoDiarioRecebedor Create();    
		UsoLimiteAntecipacaoDiarioRecebedor Create(int id, int idUsoLimiteRecebedor, int valorDiarioUsadoEmCentavos, System.DateTime dataParametro, System.DateTime? dataUltimaAlteracao, System.DateTime dataCriacao, int versao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimiteAntecipacaoMensalRecebedor.
    /// </summary>
    public partial interface IUsoLimiteAntecipacaoMensalRecebedorFactory :IBaseFactory {  
    
		UsoLimiteAntecipacaoMensalRecebedor Create();    
		UsoLimiteAntecipacaoMensalRecebedor Create(int id, int idUsoLimiteRecebedor, int valorMensalUsadoEmCentavos, int mes, int ano, System.DateTime? dataUltimaAlteracao, System.DateTime dataCriacao, int versao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimitePagamentoDiarioRecebedor.
    /// </summary>
    public partial interface IUsoLimitePagamentoDiarioRecebedorFactory :IBaseFactory {  
    
		UsoLimitePagamentoDiarioRecebedor Create();    
		UsoLimitePagamentoDiarioRecebedor Create(int id, int idUsoLimiteRecebedor, int valorDiarioUsadoEmCentavos, System.DateTime dataParametro, System.DateTime? dataUltimaAlteracao, System.DateTime dataCriacao, int versao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimitePagamentoMensalRecebedor.
    /// </summary>
    public partial interface IUsoLimitePagamentoMensalRecebedorFactory :IBaseFactory {  
    
		UsoLimitePagamentoMensalRecebedor Create();    
		UsoLimitePagamentoMensalRecebedor Create(int id, int idUsoLimiteRecebedor, int valorMensalUsadoEmCentavos, int mes, int ano, System.DateTime? dataUltimaAlteracao, System.DateTime dataCriacao, int versao);
			 
    }
 
}
namespace Perlink.Trinks.Pagamentos.Config.Factories {

 
}
namespace Perlink.Trinks.Pagamentos.DTO.Factories {

 
}
namespace Perlink.Trinks.Pagamentos.Enums.Factories {

 
}
namespace Perlink.Trinks.Pagamentos.Exceptions.Factories {

 
}
namespace Perlink.Trinks.Pagamentos.Factories.Factories {

 
}
namespace Perlink.Trinks.Pagamentos.Providers.Factories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Factories {

    /// <summary>
    /// Interface para repositório da entidade BeneficiosEstabelecimento.
    /// </summary>
    public partial interface IBeneficiosEstabelecimentoFactory :IBaseFactory {  
    
		BeneficiosEstabelecimento Create();    
		BeneficiosEstabelecimento Create(int id, int idEstabelecimento, decimal? percentualDescontoPorServico, Perlink.Trinks.PagamentosAntecipados.BeneficiosDoProgramaDeFidelidade programaDeFidelidade, int quantasHorasAntesPodeAgendarComBeneficios, Perlink.Trinks.PagamentosAntecipados.Enums.UnidadeTempoParaLimiteDeReceberDescontosEnum unidadeTempoParaLimiteDeReceberDescontos, System.DateTime dataHoraCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficiosPagamento.
    /// </summary>
    public partial interface IBeneficiosPagamentoFactory :IBaseFactory {  
    
		BeneficiosPagamento Create();    
		BeneficiosPagamento Create(int id, Perlink.Trinks.PagamentosAntecipados.PagamentoAntecipado pagamentoAntecipado, bool disponivelAoCliente, decimal? percentualDeDesconto, Perlink.Trinks.PagamentosAntecipados.BeneficiosDoProgramaDeFidelidade programaDeFidelidade, int quantasHorasAntesPodeAgendarComBeneficios);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPagamentoAntecipado.
    /// </summary>
    public partial interface IItemPagamentoAntecipadoFactory :IBaseFactory {  
    
		ItemPagamentoAntecipado Create();    
		ItemPagamentoAntecipado Create(int id, Perlink.Trinks.PagamentosAntecipados.PagamentoAntecipado pagamentoAntecipado, string nomeItem, decimal valorOriginal, decimal? valorComDesconto, decimal? percentualDeDesconto, System.DateTime dataDeExpiracaoDosBeneficios, Perlink.Trinks.Financeiro.Transacao transacaoDeCancelamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPagamentoHorario.
    /// </summary>
    public partial interface IItemPagamentoHorarioFactory :IBaseFactory {  
    
		ItemPagamentoHorario Create();    
		ItemPagamentoHorario Create(Perlink.Trinks.Pessoas.Horario horario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipado.
    /// </summary>
    public partial interface IPagamentoAntecipadoFactory :IBaseFactory {  
    
		PagamentoAntecipado Create();    
		PagamentoAntecipado Create(int id, int idEstabelecimento, int idPessoaDoCliente, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinks pagamentoOnline, decimal valorTotal, Perlink.Trinks.PagamentosAntecipados.Enums.StatusPagamentoAntecipadoEnum statusPagamento, System.DateTime dataCriacao, System.DateTime? dataHoraPagamento, System.DateTime? dataUltimaAtualizacao, Perlink.Trinks.PagamentosAntecipados.DadosPesquisadosParaPagamento dadosPesquisados, System.Collections.Generic.IList<Perlink.Trinks.PagamentosAntecipados.ItemPagamentoAntecipado> itens, Perlink.Trinks.PagamentosAntecipados.BeneficiosPagamento beneficios);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoHabilitado.
    /// </summary>
    public partial interface IServicoHabilitadoFactory :IBaseFactory {  
    
		ServicoHabilitado Create();    
		ServicoHabilitado Create(int id, int idEstabelecimento, int idServicoEstabelecimento, bool ativo, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
 
}
namespace Perlink.Trinks.PagamentosAntecipados.DTO.Factories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Enums.Factories {

 
}
namespace Perlink.Trinks.Specifications.Factories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Utils.Factories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Factories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoAdiantamentoFuncionalidadeAntecipacao.
    /// </summary>
    public partial interface IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoFactory :IBaseFactory {  
    
		ConfiguracaoAdiantamentoFuncionalidadeAntecipacao Create();    
		ConfiguracaoAdiantamentoFuncionalidadeAntecipacao Create(int id, int idEstabelecimento, bool ativo, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline.
    /// </summary>
    public partial interface IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineFactory :IBaseFactory {  
    
		ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline Create();    
		ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline Create(int id, int idEstabelecimento, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoRecebedor.
    /// </summary>
    public partial interface IEstabelecimentoRecebedorFactory :IBaseFactory {  
    
		EstabelecimentoRecebedor Create();    
		EstabelecimentoRecebedor Create(int id, int idEstabelecimento, int idRecebedor, bool habilitado, bool ativo, System.DateTime dataCriacao, Perlink.Trinks.PagamentosOnlineNoTrinks.Enums.StatusDaAtivacaoDoRecebedorEnum status);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade InstituicaoBancaria.
    /// </summary>
    public partial interface IInstituicaoBancariaFactory :IBaseFactory {  
    
		InstituicaoBancaria Create();    
		InstituicaoBancaria Create(int id, string codigo, string nome, string site);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LOGCancelamentoAntecipacao.
    /// </summary>
    public partial interface ILOGCancelamentoAntecipacaoFactory :IBaseFactory {  
    
		LOGCancelamentoAntecipacao Create();    
		LOGCancelamentoAntecipacao Create(int id, string idSolicitacao, int idRecebedor, int idPessoaQueCancelou, System.DateTime dataCancelamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LOGSolicitacaoAntecipacao.
    /// </summary>
    public partial interface ILOGSolicitacaoAntecipacaoFactory :IBaseFactory {  
    
		LOGSolicitacaoAntecipacao Create();    
		LOGSolicitacaoAntecipacao Create(int id, string idSolicitacao, int idRecebedor, int idPessoaQueSolicitou, System.DateTime dataSolicitacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoOnlineNoTrinks.
    /// </summary>
    public partial interface IPagamentoOnlineNoTrinksFactory :IBaseFactory {  
    
		PagamentoOnlineNoTrinks Create();    
		PagamentoOnlineNoTrinks Create(int id, int idPagamentoOnline, Perlink.Trinks.PagamentosOnlineNoTrinks.TaxasDoPagamentoOnlineNoTrinks taxas, bool ehPrimeiroPagamentoDoCliente, Perlink.Trinks.Financeiro.Transacao transacao, decimal valorTotal, Perlink.Trinks.PagamentosOnlineNoTrinks.Enums.StatusPagamentoNoTrinksEnum status, System.DateTime dataCriacao, System.DateTime? dataHoraPagamento, System.DateTime? dataUltimaAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasDoEstabelecimento.
    /// </summary>
    public partial interface ITaxasDoEstabelecimentoFactory :IBaseFactory {  
    
		TaxasDoEstabelecimento Create();    
		TaxasDoEstabelecimento Create(int id, int idEstabelecimento, int idEstabelecimentoRecebedor, Perlink.Trinks.PagamentosOnlineNoTrinks.ParametrosDeRecebimentoNoTrinks taxas, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, Perlink.Pagamentos.Gateways.Enums.MetodoDePagamentoNoGatewayEnum metodoDePagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasDoPagamentoOnlineNoTrinks.
    /// </summary>
    public partial interface ITaxasDoPagamentoOnlineNoTrinksFactory :IBaseFactory {  
    
		TaxasDoPagamentoOnlineNoTrinks Create();    
		TaxasDoPagamentoOnlineNoTrinks Create(int id, Perlink.Trinks.PagamentosOnlineNoTrinks.ParametrosDeRecebimentoNoTrinksParaTransacao parametrosDeRecebimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasPadrao.
    /// </summary>
    public partial interface ITaxasPadraoFactory :IBaseFactory {  
    
		TaxasPadrao Create();    
		TaxasPadrao Create(int id, string nome, string descricao, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao, Perlink.Trinks.PagamentosOnlineNoTrinks.ParametrosDeRecebimentoNoTrinks taxas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasPadraoEstabelecimentoRecebedor.
    /// </summary>
    public partial interface ITaxasPadraoEstabelecimentoRecebedorFactory :IBaseFactory {  
    
		TaxasPadraoEstabelecimentoRecebedor Create();    
		TaxasPadraoEstabelecimentoRecebedor Create(int id, int idTaxaPadrao, int idTaxasEstabelecimento, bool ativo, System.DateTime dataCriacao, System.DateTime? dataUltimaAtualizacao);
			 
    }
 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.DTO.Factories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Enums.Factories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Exceptions.Factories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.Factories {

 
}
namespace Perlink.Trinks.PDF.Factories {

 
}
namespace Perlink.Trinks.Permissoes.AreaPerlink.Factories {

    /// <summary>
    /// Interface para repositório da entidade AreaPerlinkPerfil.
    /// </summary>
    public partial interface IAreaPerlinkPerfilFactory :IBaseFactory {  
    
		AreaPerlinkPerfil Create();    
		AreaPerlinkPerfil Create(int id, string nome, System.Collections.Generic.IList<Perlink.Trinks.Permissoes.AreaPerlink.AreaPerlinkPerfilPermissao> permissoes, string urlDestino);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AreaPerlinkPerfilPermissao.
    /// </summary>
    public partial interface IAreaPerlinkPerfilPermissaoFactory :IBaseFactory {  
    
		AreaPerlinkPerfilPermissao Create();    
		AreaPerlinkPerfilPermissao Create(int id, Perlink.Trinks.Permissoes.AreaPerlink.AreaPerlinkPerfil perfil, Perlink.Trinks.Permissoes.AreaPerlink.PermissaoEnum permissao, Perlink.Trinks.Permissoes.AreaPerlink.TipoAcessoEnum tipoAcesso);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaPerfil.
    /// </summary>
    public partial interface IContaPerfilFactory :IBaseFactory {  
    
		ContaPerfil Create();    
		ContaPerfil Create(int idConta, Perlink.Trinks.Permissoes.AreaPerlink.AreaPerlinkPerfil perfil, System.Collections.Generic.IList<Perlink.Trinks.Permissoes.AreaPerlink.ContaPerfilPermissao> usuarioPerfilPermissoes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaPerfilPermissao.
    /// </summary>
    public partial interface IContaPerfilPermissaoFactory :IBaseFactory {  
    
		ContaPerfilPermissao Create();    
		ContaPerfilPermissao Create(int id, Perlink.Trinks.Permissoes.AreaPerlink.PermissaoEnum permissao, Perlink.Trinks.Permissoes.AreaPerlink.TipoAcessoEnum tipoAcesso, Perlink.Trinks.Permissoes.AreaPerlink.ContaPerfil usuarioPerfil);
			 
    }
 
}
namespace Perlink.Trinks.Permissoes.AreaPerlink.Factories {

 
}
namespace Perlink.Trinks.Permissoes.Factories {

    /// <summary>
    /// Interface para repositório da entidade CategoriaPermissao.
    /// </summary>
    public partial interface ICategoriaPermissaoFactory :IBaseFactory {  
    
		CategoriaPermissao Create();    
		CategoriaPermissao Create(int id, string nome, Perlink.Trinks.Permissoes.CategoriaPermissao categoriaPai);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DescricaoPermissao.
    /// </summary>
    public partial interface IDescricaoPermissaoFactory :IBaseFactory {  
    
		DescricaoPermissao Create();    
		DescricaoPermissao Create(string permissao, string descricao, bool visivelAreaPerlink, string urlPaginaRelacionada, Perlink.Trinks.Permissoes.CategoriaPermissao categoria, System.Collections.Generic.IList<Perlink.Trinks.Permissoes.PermissaoArea> areas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaPermissao.
    /// </summary>
    public partial interface IFranquiaPermissaoFactory :IBaseFactory {  
    
		FranquiaPermissao Create();    
		FranquiaPermissao Create(int id, Perlink.Trinks.Pessoas.Franquia franquia, string permissao, Perlink.Trinks.Permissoes.TipoAcessoEnum tipoAcesso);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Perfil.
    /// </summary>
    public partial interface IPerfilFactory :IBaseFactory {  
    
		Perfil Create();    
		Perfil Create(int id, string nome, System.Collections.Generic.IList<Perlink.Trinks.Permissoes.PerfilPermissao> permissoes, string urlDestino, string urlDestinoSecundaria, Perlink.Trinks.Pessoas.Enums.AcessoBackoffice perfilAcessoRelacionado, int ordem, bool exibeNoBackoffice, string detalhes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PerfilPermissao.
    /// </summary>
    public partial interface IPerfilPermissaoFactory :IBaseFactory {  
    
		PerfilPermissao Create();    
		PerfilPermissao Create(int id, Perlink.Trinks.Permissoes.Perfil perfil, string permissao, Perlink.Trinks.Permissoes.TipoAcessoEnum tipoAcesso);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PermissaoArea.
    /// </summary>
    public partial interface IPermissaoAreaFactory :IBaseFactory {  
    
		PermissaoArea Create();    
		PermissaoArea Create(int id, Perlink.Trinks.Permissoes.DescricaoPermissao permissao, Perlink.Trinks.Enums.AreasTrinksEnum areaSistema);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioPerfil.
    /// </summary>
    public partial interface IUsuarioPerfilFactory :IBaseFactory {  
    
		UsuarioPerfil Create();    
		UsuarioPerfil Create(int idUsuarioEstabelecimento, Perlink.Trinks.Permissoes.Perfil perfil, System.Collections.Generic.IList<Perlink.Trinks.Permissoes.UsuarioPerfilPermissao> usuarioPerfilPermissoes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioPerfilPermissao.
    /// </summary>
    public partial interface IUsuarioPerfilPermissaoFactory :IBaseFactory {  
    
		UsuarioPerfilPermissao Create();    
		UsuarioPerfilPermissao Create(int id, string permissao, Perlink.Trinks.Permissoes.TipoAcessoEnum tipoAcesso, Perlink.Trinks.Permissoes.UsuarioPerfil usuarioPerfil);
			 
    }
 
}
namespace Perlink.Trinks.Permissoes.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Factories {

    /// <summary>
    /// Interface para repositório da entidade Bairro.
    /// </summary>
    public partial interface IBairroFactory :IBaseFactory {  
    
		Bairro Create();    
		Bairro Create(int codigo, string nome, int? codigoCorreios, Perlink.Trinks.Pessoas.Cidade cidade, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CacheLocalidade.
    /// </summary>
    public partial interface ICacheLocalidadeFactory :IBaseFactory {  
    
		CacheLocalidade Create();    
		CacheLocalidade Create(int codigo, int idFranquia, string locaisMobile, string locaisPortal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CategoriaPortalServico.
    /// </summary>
    public partial interface ICategoriaPortalServicoFactory :IBaseFactory {  
    
		CategoriaPortalServico Create();    
		CategoriaPortalServico Create(int id, string nome, bool ativo, string nomeDoIconeNoAplicativo, int? ordem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Cidade.
    /// </summary>
    public partial interface ICidadeFactory :IBaseFactory {  
    
		Cidade Create();    
		Cidade Create(int codigo, string nome, Perlink.Trinks.Pessoas.UF uF, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Bairro> bairros, bool ativo, string codigoIBGE, bool possuiDeducaoNfse, bool possuiBaseDeCalculoNfse, int? quantidadeMaximaRpsLote);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Cliente.
    /// </summary>
    public partial interface IClienteFactory :IBaseFactory {  
    
		Cliente Create();    
		Cliente Create(int idCliente, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisica, System.DateTime? ultimoAgendamento, System.DateTime? dataUltimaAlteracao, int? idPessoaQueAlterou, Perlink.Trinks.Pessoas.Enums.TipoClienteEnum tipoCliente, string tokenTelaLembreteSms, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.ClienteEstabelecimento> clientesEstabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.ClienteEstabelecimento> clientesEstabelecimentoAtivos, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Estabelecimento> estabelecimentosFavoritos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ClienteAreaPerlink.
    /// </summary>
    public partial interface IClienteAreaPerlinkFactory :IBaseFactory {  
    
		ClienteAreaPerlink Create();    
		ClienteAreaPerlink Create(int id, int idCliente, int? idClienteEstabelecimento, string cpf, string nomeCompleto, int idEstabelecimentoAssociado, string nomeFantasiaEstabelecimentoAssociado, string observacoesEstabelecimentoAssociado, string ramalTelefone, string dDITelefone, string dDDTelefone, string numeroTelefone, string sexo, string email, System.DateTime? dataNascimento, int codigoTipoCliente, int totalAgendamentosBalcao, int totalAgendamentosWeb, bool estaComCadastroIncompleto, bool ehProfissionalDoEstabelecimento, bool possuiVinculoComFacebook, int? idClienteNivelAcessoBackOffice, int? idClienteEstabelecimentoNivelAcessoBackOffice, int? responsavelEstabelecimentoNivelAcessoBackOffice, int? idPessoaFisicaNivelAcessoBackOffice, System.DateTime dataDeUltimoLogin, bool contaAtiva, bool podeAgendarOnlineEmEstabelecimento, Perlink.Trinks.Pessoas.Enums.TipoClienteEnum tipoCliente, string telefoneTextoFormatado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ClienteEstabelecimento.
    /// </summary>
    public partial interface IClienteEstabelecimentoFactory :IBaseFactory {  
    
		ClienteEstabelecimento Create();    
		ClienteEstabelecimento Create(bool ativo, Perlink.Trinks.Pessoas.Cliente cliente, int codigo, Perlink.Trinks.Pessoas.ClienteEstabelecimento.DadosCliente dadosDoCliente, System.DateTime? dataNascimentoCliente, System.DateTime? dataCadastro, bool enviarEmailAgendamentoCliente, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.ComoConheceu comoNosConheceu, string nomeCompletoCliente, string observacoes, bool podeAgendarOnlineNoEstabelecimento, bool? recebeNotificacao, Perlink.Trinks.Pessoas.VO.Genero genero, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Telefone> telefonesProprios, Perlink.Trinks.Pessoas.EnderecoDeClienteEstabelecimento endereco, decimal valorCredito, string idExterno, string emailAlternativo, Perlink.Trinks.Pessoas.Horario primeiroAgendamento, Perlink.Trinks.Pessoas.FotoDeClienteEstabelecimento fotoDoPerfil, bool recebeEmailMarketing, bool recebeSMSMarketing, bool recebeEmailProgramaFidelidade, System.DateTime? dataCancelamentoRecebimentoEmailMarketingPeloCliente, int saldoDePontosDeFidelidade, string cNPJ, System.Collections.Generic.List<Perlink.Trinks.Marcadores.DTO.EtiquetaDTO> listaDeEtiquetasDoCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ClienteEstabelecimentoSaldos.
    /// </summary>
    public partial interface IClienteEstabelecimentoSaldosFactory :IBaseFactory {  
    
		ClienteEstabelecimentoSaldos Create();    
		ClienteEstabelecimentoSaldos Create(int id, decimal? totalDebitos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ComoConheceu.
    /// </summary>
    public partial interface IComoConheceuFactory :IBaseFactory {  
    
		ComoConheceu Create();    
		ComoConheceu Create(int id, bool ativo, string descricao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, string outrosNomes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ComoEstabelecimentoConheceuOTrinks.
    /// </summary>
    public partial interface IComoEstabelecimentoConheceuOTrinksFactory :IBaseFactory {  
    
		ComoEstabelecimentoConheceuOTrinks Create();    
		ComoEstabelecimentoConheceuOTrinks Create(int id, string descricao, int ordem, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CompartilhamentoNaRede.
    /// </summary>
    public partial interface ICompartilhamentoNaRedeFactory :IBaseFactory {  
    
		CompartilhamentoNaRede Create();    
		CompartilhamentoNaRede Create(int id, int idEstabelecimentoOrigem, int idEstabelecimentoDestino);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteAderencia.
    /// </summary>
    public partial interface IConfiguracaoHotsiteAderenciaFactory :IBaseFactory {  
    
		ConfiguracaoHotsiteAderencia Create();    
		ConfiguracaoHotsiteAderencia Create(int codigo, string descricao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteInicioMarcos.
    /// </summary>
    public partial interface IConfiguracaoHotsiteInicioMarcosFactory :IBaseFactory {  
    
		ConfiguracaoHotsiteInicioMarcos Create();    
		ConfiguracaoHotsiteInicioMarcos Create(int codigo, string descricao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteIntervalo.
    /// </summary>
    public partial interface IConfiguracaoHotsiteIntervaloFactory :IBaseFactory {  
    
		ConfiguracaoHotsiteIntervalo Create();    
		ConfiguracaoHotsiteIntervalo Create(int codigo, int intervaloEmMinutos, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteUniverso.
    /// </summary>
    public partial interface IConfiguracaoHotsiteUniversoFactory :IBaseFactory {  
    
		ConfiguracaoHotsiteUniverso Create();    
		ConfiguracaoHotsiteUniverso Create(int codigo, string descricao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Conta.
    /// </summary>
    public partial interface IContaFactory :IBaseFactory {  
    
		Conta Create();    
		Conta Create(bool ativo, bool bloqueadoPagamento, bool bloqueadoSenha, string codigoConfirmacao, string codigoFacebook, bool confirmada, System.DateTime? dataAceiteTermosUso, System.DateTime dataCadastro, System.DateTime? dataConfirmacaoEmissaoGuid, System.DateTime? dataConfirmacaoGuid, System.DateTime dataUltimaAtualizacao, System.DateTime? dataUltimoLogin, string email, System.Collections.Generic.List<Perlink.Trinks.Pessoas.UsuarioEstabelecimento> estabelecimentosVinculados, string guid, string guidAutenticacao, int idConta, Perlink.Trinks.Pessoas.Pessoa pessoa, bool receberNews, string senha, int versaoTermosUso, bool exibirPromocaoProgramaDeFidelidade, bool jahFoiNotificadaComCodigoDeConfirmacao, string guidAutenticacaoAppPro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ContaFranquia.
    /// </summary>
    public partial interface IContaFranquiaFactory :IBaseFactory {  
    
		ContaFranquia Create();    
		ContaFranquia Create(int id, Perlink.Trinks.Pessoas.Conta conta, Perlink.Trinks.Pessoas.Franquia franquia, bool ativo, bool acessoAdministrador, bool permiteAcessarBackOfficeDosEstabelecimentosDaFranquia, bool recebeEmailSobrePedidoEnviado, bool utilizaMenuLateral, bool permiteAcessarConfiguracoesDeUnidades);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DadosParaRecalculoComissao.
    /// </summary>
    public partial interface IDadosParaRecalculoComissaoFactory :IBaseFactory {  
    
		DadosParaRecalculoComissao Create();    
		DadosParaRecalculoComissao Create(int idHorarioTransacao, int idEstabelecimento, int idPessoaEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, Perlink.Trinks.Pessoas.EstabelecimentoProfissional assistente, Perlink.Trinks.Pessoas.HorarioTransacao horarioTransacao, Perlink.Trinks.Financeiro.Transacao transacao, System.DateTime dataHoraTransacao, System.DateTime dataHoraReferencia, decimal custoDescartaveis, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProfissionalServicoComissao comissao, Perlink.Trinks.Pessoas.EstabelecimentoAssistenteServicoComissao comissaoAssistente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DataEspecial.
    /// </summary>
    public partial interface IDataEspecialFactory :IBaseFactory {  
    
		DataEspecial Create();    
		DataEspecial Create(int id, int dia, int mes, int? ano, Perlink.Trinks.Pessoas.Enums.TipoDataEspecialEnum tipoDataEspecial, string descricao, bool ehDataSubadquirente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DiaSemana.
    /// </summary>
    public partial interface IDiaSemanaFactory :IBaseFactory {  
    
		DiaSemana Create();    
		DiaSemana Create(int idDiaSemana, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EmailRejeitadoAmazon.
    /// </summary>
    public partial interface IEmailRejeitadoAmazonFactory :IBaseFactory {  
    
		EmailRejeitadoAmazon Create();    
		EmailRejeitadoAmazon Create(string emailContato, int id, string justificativa);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Endereco.
    /// </summary>
    public partial interface IEnderecoFactory :IBaseFactory {  
    
		Endereco Create();    
		Endereco Create(int idEndereco, Perlink.Trinks.Pessoas.Pessoa pessoa, string cep, Perlink.Trinks.Pessoas.TipoLogradouro tipoLogradouro, string logradouro, string numero, string complemento, string bairro, string cidade, Perlink.Trinks.Pessoas.Pessoa dono, Perlink.Trinks.Pessoas.UF uF, Perlink.Trinks.Pessoas.Bairro bairroEntidade, bool ativo, double? latitude, double? longitude);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EnderecoPreenchidoManualmente.
    /// </summary>
    public partial interface IEnderecoPreenchidoManualmenteFactory :IBaseFactory {  
    
		EnderecoPreenchidoManualmente Create();    
		EnderecoPreenchidoManualmente Create(int idEnderecoPreenchidoManualmente, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Estabelecimento.
    /// </summary>
    public partial interface IEstabelecimentoFactory :IBaseFactory {  
    
		Estabelecimento Create();    
		Estabelecimento Create(System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Horario> agendamentos, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.ServicoCategoriaEstabelecimento> categoriasEstabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.ClienteEstabelecimento> clienteEstabelecimentoLista, Perlink.Trinks.Pessoas.EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral, Perlink.Trinks.Pessoas.EstabelecimentoDadosGerais estabelecimentoDadosGerais, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoProfissional> estabelecimentoProfissionalLista, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstatisticaExibicaoTelefone> estatisticaExibicaoTelefoneLista, Perlink.Trinks.Pessoas.FranquiaEstabelecimento franquiaEstabelecimento, bool habilitaCobranca, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoHorarioFuncionamento> horariosDeFuncionamento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoHorarioEspecialFuncionamento> horariosEspeciaisDeFuncionamento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.HotsiteEstabelecimento> hotsiteEstabelecimentoLista, int idEstabelecimento, bool isNovo, string nomeDeExibicaoNoPortal, int pesoBuscaPortal, bool hotsiteFoiAcessadoPelasOrientacoesIniciais, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, string referrer, int fuso, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.ServicoEstabelecimento> servicosEstabelecimento, bool servicosJaAssociadosAoProfissionalBasico, Perlink.Trinks.Pessoas.TipoEstabelecimento tipoEstabelecimento, Perlink.Trinks.Estabelecimentos.Enums.FaixaProfissionaisEstabelecimentoEnum faixaProfissionais, bool permiteQueProfissionaisVisualizemSuaPropriaAgenda, Perlink.Trinks.Pessoas.TipoFranquia tipoFranquia, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.UsuarioEstabelecimento> vinculoUsuarios, Perlink.Trinks.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimento configuracaoDeNFC, System.DateTime? dataPrimeiroAgendamento, string nomeDoConsolidado, string random, string motivoCadastro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoAssistenteServico.
    /// </summary>
    public partial interface IEstabelecimentoAssistenteServicoFactory :IBaseFactory {  
    
		EstabelecimentoAssistenteServico Create();    
		EstabelecimentoAssistenteServico Create(int idEstabelecimentoAssistenteServico, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, bool ativo, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoAssistenteServicoComissao> estabelecimentoAssistenteServicoComissaoLista);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoAssistenteServicoComissao.
    /// </summary>
    public partial interface IEstabelecimentoAssistenteServicoComissaoFactory :IBaseFactory {  
    
		EstabelecimentoAssistenteServicoComissao Create();    
		EstabelecimentoAssistenteServicoComissao Create(int idEstabelecimentoAssistenteServicoComissao, Perlink.Trinks.Pessoas.EstabelecimentoAssistenteServico estabelecimentoAssistenteServico, decimal? valorComissao, Perlink.Trinks.Pessoas.TipoComissao tipoComissao, System.DateTime? dataHoraInicioVigencia, System.DateTime? dataHoraFimVigencia, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoAtendeCrianca.
    /// </summary>
    public partial interface IEstabelecimentoAtendeCriancaFactory :IBaseFactory {  
    
		EstabelecimentoAtendeCrianca Create();    
		EstabelecimentoAtendeCrianca Create(int idEstabelecimentoAtendeCrianca, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoConfiguracaoComissao.
    /// </summary>
    public partial interface IEstabelecimentoConfiguracaoComissaoFactory :IBaseFactory {  
    
		EstabelecimentoConfiguracaoComissao Create();    
		EstabelecimentoConfiguracaoComissao Create(int idEstabelecimento, bool subtrairDescontoCashbackValorBaseComissao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoConfiguracaoGeral.
    /// </summary>
    public partial interface IEstabelecimentoConfiguracaoGeralFactory :IBaseFactory {  
    
		EstabelecimentoConfiguracaoGeral Create();    
		EstabelecimentoConfiguracaoGeral Create(int antecedenciaDeNotificacaoEmMinutos, bool antecedenciaDeNotificacaoEmMinutosAlterado, Perlink.Trinks.Pessoas.Template templateLembreteSelecionado, int codigoTipoDeImpressao, bool comandaEstaAtiva, bool considerarDescontoOperadoraNaComissao, bool considerarDescontoOperadoraNaComissaoDeAssistentes, System.DateTime? dataSolicitacaoDesejaSerLembradoSobreExibicaoNoPortal, bool descontarDescartaveisDoValorPago, bool desejaSerLembradoSobreExibicaoNoPortal, bool enviarEmailDespesasHabilitado, bool enviarEmailEstoqueHabilitado, bool enviarEmailsAgendamentoParaClientes, bool enviarNotificacaoParaClientes, bool enviarNotificacaoParaClientesAlterado, bool envioDeNotificacaoHabilitado, bool permiteEnvioDeLembreteSmsGratuito, bool exibeNumeroDeFechamento, bool exibeNumeroDaComandaParaImpressao, bool exibirValorProdutoUsadoNaImpressaoComanda, bool exibeVideosTreinamento, bool exibirAlertasCobrancaParaRecepcionista, System.DateTime? exibirDadosAPartirDe, bool exibirNomeProfissionalEmEmailESms, bool filaDeEsperaEstaAtiva, bool habilitaAdicaoRapidaDeProduto, bool habilitaBelezinha, bool habilitarPesquisaClientesDeOutrosProfissionais, bool habilitarProfissionalParceiro, int idEstabelecimento, bool imprimirFechamentoAoFecharConta, bool imprimirQRCodeFechamento, int? numeroDoUltimoFechamento, int numeroMaximoComanda, int numeroMinimoComanda, bool pagarComissoesNaDataPrevistaDeRecebimento, decimal? percentualAplicadoTotaisServicosPAT, decimal? percentualDeDescontoMaximo, bool permiteEnviarNotificacaoDeAgendePeloTrinks, bool permiteFinalizarApenasClientesEmAtendimento, Perlink.Trinks.Pessoas.PessoaFisica pessoaSolicitouDesejaSerLembradoSobreExibicaoNoPortal, bool produtoFracionadoEstaAtivo, bool profissionaisPodemAlterarPrecosDosServicos, bool profissionaisPodemIncluirObservacoesNosAgendamentos, bool profissionaisPodemVerPrecosDosServicos, bool profissionaisVeemSeusPropriosAgendamentosApenas, int profissionalPodeVerAgendaDeDiasAnterioresEPosteriores, bool profissionalPodeVerTotaisComissoes, bool profissionalPodeVerTotaisServicos, bool profissionalPodeVerTotaisProdutos, bool trabalhaComDescartaveis, System.DateTime? ultimaExecucaoDaRotinaDoConsolidado, bool utilizarCodigoInterno, bool utilizarNFeServico, bool verSomenteHorarioDeFuncionamentoNaAgenda, bool exibirCampoAssinaturaEmImpressaoRelatorioComissoes, bool habilitaEnvioCopiaEmailResumoComissaoProfissionalParaResponsavelFinanceiro, string mensagemImpressaoRelatorioComissoes, int diaInicioFechamentoMensal, bool profissionaisPodemVerAColunaComissoes, bool habilitaProgramaDeFidelidade, System.DateTime? habilitaProgramaDeFidelidadeDataHora, bool assistentePodeVerAgendamentosQuePodeParticipar, bool habilitaSplitPagamento, bool habilitaControleDeCaixaPorProfissional, bool habilitaMotivoSangriaObrigatorio, bool jaTeveCaixaPorProfissionalAberto, bool permiteClientesAvaliemOEstabelecimento, bool permiteClientesAvaliemOsProfissionais, Perlink.Trinks.Pessoas.Enums.ConfiguracaoSugestaoDeProdutoEnum exibirSugestaoDeProdutoQuando, int? percentualEstoqueMinimoSugestaoDeProduto, int? tipoDeEmissaoNfeComProfissionalParceiro, bool habilitaPedidosDeCompraCasoNaoSejaDeFranquia, bool permiteAgendarGoogleReserve, bool habilitarPesquisaDeSatisfacao, bool habilitaTerminalDeConsultaDePreco, bool abrirInventarioComContagemIgualAoEstoqueAtual, bool trabalhaComRodizioDeProfissionais, bool jaTrabalhouComRodizioDeProfissionais, bool recepcaoPodeIncluirEExcluirProfissionaisNoRodizio, int profissionalPodeVerAreaProfissionalDeDiasAnterioresEPosteriores, int incrementoDeHorarioClicado, int valorEmPorcentagemDoCustoOperacional, bool possuiCustoOperacionalHabilitado, bool possuiCustoOperacionalProdutoHabilitado, bool assistentePodeSeAssociarAServicos, bool habilitaBaixaAutomaticaDeEstoque, bool profissionalPodeVerResumoAgenda, bool permiteMGMProfissional, int? quantidadeDeDiasParaConsultaRps, bool alertaEstoqueNegativo, bool profissionaisCadastradosVeraoMenuLateralEHeader, bool configuracaoAutomaticaDoProgramaDeFidelidadeEstaAtiva, bool exibirSomenteOsPrimeirosProfissionaisDoRodizioNaAgenda, int qtdDeProfissionaisExibirDuranteRodizio, bool gerenciaNotificacoesDoClienteNoAgendamento, Perlink.Trinks.Pessoas.Enums.StatusHorarioEnum statusDeAgendamentosFeitoPeloEstabelecimento, bool permiteUtilizarLinkDePagamento, bool lancamentoRapidoComandaPermiteBuscarServicoPeloNome, bool receberEmailsAutomaticosDeAgendamento, bool imprimirComandaAposAbrir, bool associarServicosEProdutosAComandaAbertaAutomaticamente, bool permiteAbrirComandaVazia, bool verSomenteClientesEmAtendimento, bool trabalhaComRodizioDeProfissionaisPorCategoria, bool podeVerPosicaoNoRodizio, bool momentoRetornoAoRodizio, Perlink.Trinks.Pacotes.Enums.TipoNFPacoteEnum configuracaoNFPacote, bool cancelarEnvioEmailCadastroClienteBalcao, bool avisarSobreServicosNaoFinalizados, bool avisarSobreServicosAindaPendentes, bool habilitaVisualizacaoDoValorDoProdutoNaBaixaAutomatica);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoConfiguracaoPOS.
    /// </summary>
    public partial interface IEstabelecimentoConfiguracaoPOSFactory :IBaseFactory {  
    
		EstabelecimentoConfiguracaoPOS Create();    
		EstabelecimentoConfiguracaoPOS Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, string identificadorAparelho, Perlink.Trinks.Pessoas.Enums.PassoConfiguracaoPOSEnum passoConfiguracaoPOSEnum, Perlink.Trinks.Pessoas.TipoPOS tipoPOS, System.DateTime dataAlteracao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueAlterou, bool concluiuConfiguracao, bool habilitaSplit, string secretKey, string affiliationKey, string merchantKey, decimal? automaticRate, decimal? spotRate, bool queroImprimirMeusComprovantes, string cNPJConfiguracao, bool queroNotificarMeusClientesComDadosTransacao, bool aguardandoRetornoDoCredenciamentoNaAdquirente, bool aguardandoRetornoDaHabilitacaoDoSplit, bool? usaConciliacaoAutomatica, string idRecebedor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosGerais.
    /// </summary>
    public partial interface IEstabelecimentoDadosGeraisFactory :IBaseFactory {  
    
		EstabelecimentoDadosGerais Create();    
		EstabelecimentoDadosGerais Create(int idEstabelecimento, string quemSomos, string instrucoesEspeciais, string politicaCancelamento, bool possuiAcessoDeficientes, string tipoEstabelecimentoOutro, bool formaPagamentoVisa, bool formaPagamentoMaster, bool formaPagamentoDinner, bool formaPagamentoAmex, bool formaPagamentoAura, bool formaPagamentoDinheiro, bool formaPagamentoBoleto, bool formaPagamentoVisaEletron, bool formaPagamentoOiPago, bool formaPagamentoCheque, string formaPagamentoOutros, bool possuiBarLanchonete, Perlink.Trinks.Pessoas.EstabelecimentoPossuiEstacionamento estacionamento, bool possuiAcessoWiFi, Perlink.Trinks.Pessoas.EstabelecimentoAtendeCrianca atendimentoCrianca, bool idiomaPortugues, bool idiomaIngles, bool idiomaEspanhol, string idiomaOutros, bool possuiTv, string webSite, string urlEcommerce, string facebook, string instagram);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastro.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroFactory :IBaseFactory {  
    
		EstabelecimentoDadosPreCadastro Create();    
		EstabelecimentoDadosPreCadastro Create(int idEstabelecimentoPreCadastro, string emailResponsavel, string cpf, string celularResponsavel, string nomeFantasiaEstabelecimento, string enderecoEstabelecimento, string numeroEstabelecimento, string complementoEstabelecimento, int idBairroEstabelecimento, string bairroEstabelecimento, string cidadeEstabelecimento, int idEstadoEstabelecimento, string estadoEstabelecimento, int idTipoEndereco, string cepEstabelecimento, string nomeResponsavel, Perlink.Trinks.Pessoas.EstabelecimentoDadosPreCadastroCargo cargoOcupado, Perlink.Trinks.Pessoas.ComoEstabelecimentoConheceuOTrinks comoChegouAteOTrinks, string senhaResponsavel, Perlink.Trinks.Pessoas.EstabelecimentoDadosPreCadastroSegmentos segmento, Perlink.Trinks.Pessoas.EstabelecimentoDadosPreCadastroMotivosCadastro motivoCadastro, int quantidadeDeProfissionaisObsoleta, int faixaProfissionais, int? idTipoFranquia, int temOutraUnidade, int idPessoaResponsavel, int idContaResponsavel, bool ehPossivelCadastrarEstabelecimentoModelo, int tipoCadastroModelo, int idModeloSelecionado, string cupomParceria, int? idEstabelecimento, int quantidadeMinimaDeProfissionais, int quantidadeMaximaDeProfissionais, bool enderecoFoiPreenchimentoManual);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastroCargo.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroCargoFactory :IBaseFactory {  
    
		EstabelecimentoDadosPreCadastroCargo Create();    
		EstabelecimentoDadosPreCadastroCargo Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastroMotivosCadastro.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroMotivosCadastroFactory :IBaseFactory {  
    
		EstabelecimentoDadosPreCadastroMotivosCadastro Create();    
		EstabelecimentoDadosPreCadastroMotivosCadastro Create(int id, string nome, int ordem, bool ativo, int? idTrilhaRecomendada, string nomeReduzido, string icone);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastroSegmentos.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroSegmentosFactory :IBaseFactory {  
    
		EstabelecimentoDadosPreCadastroSegmentos Create();    
		EstabelecimentoDadosPreCadastroSegmentos Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFabricanteProduto.
    /// </summary>
    public partial interface IEstabelecimentoFabricanteProdutoFactory :IBaseFactory {  
    
		EstabelecimentoFabricanteProduto Create();    
		EstabelecimentoFabricanteProduto Create(int idEstabelecimentoFabricanteProduto, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.FabricanteProdutoPadrao fabricanteProdutoPadrao, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFormaPagamento.
    /// </summary>
    public partial interface IEstabelecimentoFormaPagamentoFactory :IBaseFactory {  
    
		EstabelecimentoFormaPagamento Create();    
		EstabelecimentoFormaPagamento Create(bool ativo, int diasParaReceberDaOperadora, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoFormaPagamentoParcela> parcelamento, Perlink.Trinks.Financeiro.FormaPagamento formaPagamento, int id, decimal? percentualCobradoPelaOperadora, bool aceitaParcelamento, bool aceitaPreDatado, Perlink.Trinks.ConciliacaoBancaria.ContaFinanceiraDoEstabelecimento contaFinanceira, int diasParaRecebimentoDoPagamento, decimal? percentualDescontoNaComissao, bool possuiAntecipacaoAutomatica, decimal? taxaAntecipacaoAutomatica);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFormaPagamentoParcela.
    /// </summary>
    public partial interface IEstabelecimentoFormaPagamentoParcelaFactory :IBaseFactory {  
    
		EstabelecimentoFormaPagamentoParcela Create();    
		EstabelecimentoFormaPagamentoParcela Create(decimal descontoOperadora, Perlink.Trinks.Pessoas.EstabelecimentoFormaPagamento estabelecimentoFormaPagamento, int id, int numeroParcela, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFornecedor.
    /// </summary>
    public partial interface IEstabelecimentoFornecedorFactory :IBaseFactory {  
    
		EstabelecimentoFornecedor Create();    
		EstabelecimentoFornecedor Create(int idEstabelecimentoFornecedor, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoFabricanteProduto estabelecimentoFabricanteProduto, Perlink.Trinks.Pessoas.Fornecedor fornecedor, string nomeContato, string emailContato, string observacao, bool fornecedorDeProduto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoHorarioEspecialFuncionamento.
    /// </summary>
    public partial interface IEstabelecimentoHorarioEspecialFuncionamentoFactory :IBaseFactory {  
    
		EstabelecimentoHorarioEspecialFuncionamento Create();    
		EstabelecimentoHorarioEspecialFuncionamento Create(int codigo, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoHorarioEspecialFuncionamentoTipo estabelecimentoHorarioEspecialFuncionamentoTipo, System.DateTime dataInicio, System.DateTime dataFim, string motivo, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoHorarioEspecialFuncionamentoTipo.
    /// </summary>
    public partial interface IEstabelecimentoHorarioEspecialFuncionamentoTipoFactory :IBaseFactory {  
    
		EstabelecimentoHorarioEspecialFuncionamentoTipo Create();    
		EstabelecimentoHorarioEspecialFuncionamentoTipo Create(int codigo, string nome, string tipoOperacao, string descricao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoHorarioFuncionamento.
    /// </summary>
    public partial interface IEstabelecimentoHorarioFuncionamentoFactory :IBaseFactory {  
    
		EstabelecimentoHorarioFuncionamento Create();    
		EstabelecimentoHorarioFuncionamento Create(int idHorarioFuncionamento, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.DiaSemana diaSemana, System.TimeSpan? horaAbertura, System.TimeSpan? horaFechamento, bool ativo, bool aberto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoIndicado.
    /// </summary>
    public partial interface IEstabelecimentoIndicadoFactory :IBaseFactory {  
    
		EstabelecimentoIndicado Create();    
		EstabelecimentoIndicado Create(int idEstabelecimentoIndicado, string nomePessoaIndicante, string emailPessoaIndicante, string nomeEstabelecimento, string nomeContatoEstabelecimento, string dDDTelefoneEstabelecimento1, string numeroTelefoneEstabelecimento1, string dDDTelefoneEstabelecimento2, string numeroTelefoneEstabelecimento2, string emailEstabelecimento, string observacaoSobreEstabelecimento, System.DateTime dataIndicacao, string telefoneEstabelecimento1, string telefoneEstabelecimento2);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoMovimentacaoEstoque.
    /// </summary>
    public partial interface IEstabelecimentoMovimentacaoEstoqueFactory :IBaseFactory {  
    
		EstabelecimentoMovimentacaoEstoque Create();    
		EstabelecimentoMovimentacaoEstoque Create(int idEstabelecimentoMovimentacaoEstoque, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.Pessoas.Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.ProdutoEstoque.MovimentoEstoqueTipo movimentoEstoqueTipo, string notaFiscal, System.DateTime dataMovimentacao, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.Pessoas.EstabelecimentoFornecedor estabelecimentoFornecedor, int quantidade, decimal precoUnitario, decimal desconto, decimal precoUnitarioFinal, decimal? custoMedio, string observacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaCriacao, System.DateTime dataCriacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaUltimaAlteracao, Perlink.Trinks.Vendas.ItemVenda itemVenda, Perlink.Trinks.Vendas.PreVenda preVenda, System.DateTime dataUltimaAlteracao, bool ativo, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoMovimentacaoEstoqueSaida, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaDoProfissional);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoPossuiEstacionamento.
    /// </summary>
    public partial interface IEstabelecimentoPossuiEstacionamentoFactory :IBaseFactory {  
    
		EstabelecimentoPossuiEstacionamento Create();    
		EstabelecimentoPossuiEstacionamento Create(int idEstabelecimentoPossuiEstacionamento, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoPreCadastro.
    /// </summary>
    public partial interface IEstabelecimentoPreCadastroFactory :IBaseFactory {  
    
		EstabelecimentoPreCadastro Create();    
		EstabelecimentoPreCadastro Create(int idEstabelecimentoPreCadastro, string nome, string email, string telefones, bool completouCadastro, System.Guid? metricasGuid);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProduto.
    /// </summary>
    public partial interface IEstabelecimentoProdutoFactory :IBaseFactory {  
    
		EstabelecimentoProduto Create();    
		EstabelecimentoProduto Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.ProdutoEstoque.ProdutoPadrao produtoPadrao, Perlink.Trinks.Pessoas.EstabelecimentoFabricanteProduto estabelecimentoFabricanteProduto, Perlink.Trinks.ProdutoEstoque.EstabelecimentoProdutoCategoria estabelecimentoProdutoCategoria, string descricao, int estoqueInicial, int estoqueAtual, int estoqueMinimo, bool permitirRevenda, decimal precoRevendaCliente, decimal precoRevendaProfissional, decimal valorComissaoRevenda, Perlink.Trinks.Pessoas.TipoComissao tipoComissao, decimal custoMedio, decimal valorPorMedida, Perlink.Trinks.Pessoas.UnidadeMedida unidadeMedida, int medidasPorUnidade, string descricaoComplementar, Perlink.Trinks.Pessoas.PessoaFisica pessoaCriacao, System.DateTime dataCriacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaUltimaAlteracao, System.DateTime dataUltimaAlteracao, bool ativo, bool produtoEhNacional, string codigoNCM, decimal? aliquotaICMS, decimal? aliquotaPIS, decimal? aliquotaCOFINS, int? idSituacaoTributaria, int? origemProduto, string codigoBarras, string codigoDeIdentificacao, string codigoFiscalOperacao, string tipoUnidade, bool ehRevendaParaProfissional, bool ehRevendaParaCliente, bool fabricacaoPropria, string cEST, string codigoPis, string codigoCofins, decimal? icmsOrigem, decimal? reducaoIcmsOrigem, int indiceNivelEstoque, string idExterno, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProdutoModelo, decimal valorDeCompra, int estoqueEmPedidos, System.Collections.Generic.IList<Perlink.Trinks.ProdutoEstoque.ValorPropriedadeDoProduto> valoresDePropriedades, bool clienteGanhaPontos, bool clientePodeResgatarPontos, int pontosNecessariosParaResgate);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissional.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalFactory :IBaseFactory {  
    
		EstabelecimentoProfissional Create();    
		EstabelecimentoProfissional Create(bool ativo, int codigo, string codigoDeAcessoAoPainelAtendimento, string codigoIntegracao, string codigoInterno, decimal comissaoPadrao, string ctps, string ctpsSerie, System.DateTime? dataCadastro, System.DateTime? dataExpedicaoRG, System.DateTime? dataInativacao, System.DateTime? dataNascimento, bool ehResponsavelEstabelecimento, bool ehUsuarioAdministrativoEstabelecimento, Perlink.Trinks.Pessoas.Endereco endereco, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoProfissionalServico> estabelecimentoProfissionalServicoLista, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoAssistenteServico> estabelecimentoAssistenteServicoLista, Perlink.Trinks.Pessoas.EstadoCivil estadoCivil, Perlink.Trinks.Pessoas.FotoPessoa fotoPrincipal, System.Collections.Generic.List<Perlink.Trinks.Pessoas.HorarioTrabalho> horariosTrabalhoProfissionalAtivos, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.HorarioTrabalho> horarioTrabalhoLista, string observacaoSobreProfissional, string orgaoExpedidorRG, bool permiteEnvioEmail, string pIS, bool podeAcessarMinhaAgenda, Perlink.Trinks.Pessoas.Profissional profissional, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, string rG, Perlink.Trinks.Pessoas.VO.Genero genero, bool recebeComissaoNaDataPrevistaDeRecebimento, bool ehExcecaoComissaoNaDataPrevistaDeRecebimento, bool descontaTaxaOperadoraNaComissao, bool ehExcecaoDescontoTaxaOperadora, bool descontaTaxaOperadoraNaComissaoAssistente, bool ehExcecaoDescontoTaxaOperadoraAssistente, System.Collections.Generic.List<Perlink.Trinks.Pessoas.Telefone> telefones, System.DateTime? dataInicio, Perlink.Trinks.Pessoas.TipoComissao tipoComissao, Perlink.Trinks.Pessoas.FormaRelacaoProfissional formaRelacaoProfissional, Perlink.Trinks.Pessoas.FuncaoDoProfissional funcaoProfissional, Perlink.Trinks.Pessoas.UsuarioEstabelecimento usuarioEstabelecimento, bool possuiAgenda, bool? podeAddFotosServicoPATPainel, bool habilitaSplitPagamento, string codigoIdentificacaoPOS, string idExternosDeBeneficiario, bool aguardandoRetornoDaHabilitacaoDoSplit, System.DateTime? dataFimDoContratoDoProfissional, bool utilizaMenuLateral, Perlink.Trinks.Financeiro.TipoContaBancaria tipoDeContaBancaria, int? codigoDoBanco, string agencia, string agenciaDV, string numeroConta, string numeroContaDV, int? contaMesmaTitularidade, string documentoDaContaBancaria, string nomeDoTitularDaContaBancaria, bool usandoSplit);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalRedeSocial.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalRedeSocialFactory :IBaseFactory {  
    
		EstabelecimentoProfissionalRedeSocial Create();    
		EstabelecimentoProfissionalRedeSocial Create(int id, int? idEstabelecimentoProfissional, int idEstabelecimento, string identificacao, Perlink.Trinks.Pessoas.Enums.TipoMidiaSocialEnum tipoRedeSocial, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalServico.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalServicoFactory :IBaseFactory {  
    
		EstabelecimentoProfissionalServico Create();    
		EstabelecimentoProfissionalServico Create(int codigo, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, bool ativo, bool permiteAgendamentoHotsite, bool servicoExecutadoComAssistente, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoProfissionalServicoComissao> estabelecimentoProfissionalServicoComissaoLista);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalServicoComissao.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalServicoComissaoFactory :IBaseFactory {  
    
		EstabelecimentoProfissionalServicoComissao Create();    
		EstabelecimentoProfissionalServicoComissao Create(int codigo, Perlink.Trinks.Pessoas.EstabelecimentoProfissionalServico estabelecimentoProfissionalServico, decimal valorComissao, Perlink.Trinks.Pessoas.TipoComissao tipoComissao, System.DateTime? inicioVigencia, System.DateTime? fimVigencia, bool ativo, decimal? valorComissaoComUmAssistente, Perlink.Trinks.Pessoas.TipoComissao tipoComissaoDoAssistente, Perlink.Trinks.Pessoas.Enums.TipoComissaoEnum tipoComissaoEnum, Perlink.Trinks.Pessoas.Enums.TipoComissaoEnum tipoComissaoDoAssistenteEnum);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoSolicitacaoAparecerBusca.
    /// </summary>
    public partial interface IEstabelecimentoSolicitacaoAparecerBuscaFactory :IBaseFactory {  
    
		EstabelecimentoSolicitacaoAparecerBusca Create();    
		EstabelecimentoSolicitacaoAparecerBusca Create(int idEstabelecimentoSolicitacaoAparecerBusca, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueSolicitou, System.DateTime dataSolicitacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueAprovou, System.DateTime? dataRegistroDecisao, bool aprovada, string observacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoTemplate.
    /// </summary>
    public partial interface IEstabelecimentoTemplateFactory :IBaseFactory {  
    
		EstabelecimentoTemplate Create();    
		EstabelecimentoTemplate Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.Template template);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstadoCivil.
    /// </summary>
    public partial interface IEstadoCivilFactory :IBaseFactory {  
    
		EstadoCivil Create();    
		EstadoCivil Create(int codigo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstatisticaExibicaoTelefone.
    /// </summary>
    public partial interface IEstatisticaExibicaoTelefoneFactory :IBaseFactory {  
    
		EstatisticaExibicaoTelefone Create();    
		EstatisticaExibicaoTelefone Create(int codigo, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, string areaSistema, System.DateTime instanteClique);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FabricanteProdutoPadrao.
    /// </summary>
    public partial interface IFabricanteProdutoPadraoFactory :IBaseFactory {  
    
		FabricanteProdutoPadrao Create();    
		FabricanteProdutoPadrao Create(int idEstabelecimentoFabricanteProduto, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormaRelacaoProfissional.
    /// </summary>
    public partial interface IFormaRelacaoProfissionalFactory :IBaseFactory {  
    
		FormaRelacaoProfissional Create();    
		FormaRelacaoProfissional Create(int id, string descricao, int idEstabelecimento, int? idFormaRelacaoProfissionalPadrao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FormaRelacaoProfissionalPadrao.
    /// </summary>
    public partial interface IFormaRelacaoProfissionalPadraoFactory :IBaseFactory {  
    
		FormaRelacaoProfissionalPadrao Create();    
		FormaRelacaoProfissionalPadrao Create(int idFormaRelacaoProfissionalPadrao, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Fornecedor.
    /// </summary>
    public partial interface IFornecedorFactory :IBaseFactory {  
    
		Fornecedor Create();    
		Fornecedor Create(int idFornecedor, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FotoDeClienteEstabelecimento.
    /// </summary>
    public partial interface IFotoDeClienteEstabelecimentoFactory :IBaseFactory {  
    
		FotoDeClienteEstabelecimento Create();    
		FotoDeClienteEstabelecimento Create(int id, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, Perlink.Trinks.Fotos.Foto foto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FotoDeServicoRealizadoEmHorario.
    /// </summary>
    public partial interface IFotoDeServicoRealizadoEmHorarioFactory :IBaseFactory {  
    
		FotoDeServicoRealizadoEmHorario Create();    
		FotoDeServicoRealizadoEmHorario Create(int id, Perlink.Trinks.Pessoas.Horario horario, Perlink.Trinks.Fotos.Foto foto, Perlink.Trinks.Pessoas.Enums.MomentoDaFotoEnum? momentoDaFoto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FotoEstabelecimento.
    /// </summary>
    public partial interface IFotoEstabelecimentoFactory :IBaseFactory {  
    
		FotoEstabelecimento Create();    
		FotoEstabelecimento Create(int? codigo, Perlink.Trinks.Pessoas.Pessoa pessoa, bool principal, string legenda, System.DateTime dataAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FotoPessoa.
    /// </summary>
    public partial interface IFotoPessoaFactory :IBaseFactory {  
    
		FotoPessoa Create();    
		FotoPessoa Create(int? codigo, Perlink.Trinks.Pessoas.Pessoa pessoa, bool principal, string legenda, System.DateTime dataAtualizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Franquia.
    /// </summary>
    public partial interface IFranquiaFactory :IBaseFactory {  
    
		Franquia Create();    
		Franquia Create(bool ativo, int id, string nome, bool temAplicativoPersonalizado, string urlDownloadAppAndroid, string urlDownloadAppIOS, bool permiteAlterarServicosDosProfissionais, bool possuiTemaBackoffice, bool possuiTemaFrameBusca, bool possuiTemaAreaAdministrativa, string urlPinoMaps, bool habilitaUsoNFSe, bool habilitaUsoNFCe, bool habilitaAlteracaoDeNomeFantasia, bool permiteAlterarLogo, bool permiteAlteracaoDeValores, bool habilitaAlteracaoDeFotos, bool cadastrarComCobrancaManual, Perlink.Trinks.Cobranca.PlanoAssinatura planoAssinaturaPadrao, bool aceitaPreCadastroClienteNoFrame, bool exibirMensagemSolicitacaoAparicaoPortal, string urlRedirecionamentoFrameBusca, string idAppandroid, string idAppIos, bool alertarAusenciaDeDadosDoClienteParaProgramaDeFidelidade, string identificadorDownloadAppLista, string identificadorDownloadApp, bool realizaTransferenciaDeProdutosEntreEstoques, bool permiteAlterarValorDeCompra, bool permiteAlterarEstoqueMinimo, bool permiteAlterarDadosFiscais, bool permitePedirQuantidadeAbaixoDaSugestao, bool habilitaPedidosDeCompra, bool permiteExibirClassificacaoDoProduto, bool compartilhaClientes, bool permiteTransferenciaDePontosDeFidelidadeEntreUnidades, bool permiteContratarItensAdicionaisNoPlano, bool permiteExibicaoDoValorDeServicoEhProduto, string rastreadorDoGoogleAnalytics, bool permiteQueProfissionaisVisualizemSuaPropriaAgenda, bool permiteEnvioDeLembreteSmsGratuito, bool permiteAtivarEnvioDeSmsAniversario, bool permiteEnviarNotificacaoPushMarketing, bool limitaAlteracaDeDataDosFechamentosContaParaOutrosMeses, bool limitaEstornoDosFechamentosContaParaOutrosMeses, bool exigeSenhaAoRegistrarUsoInterno, bool permiteSolicitarAssinaturaAoCliente, bool permiteEnviarRespostaAnamnesePorEmail, bool permiteProfissionalAltereProprioNome, string slug, bool ocultaAgendamentosParaPerfilProfissional2);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaEstabelecimento.
    /// </summary>
    public partial interface IFranquiaEstabelecimentoFactory :IBaseFactory {  
    
		FranquiaEstabelecimento Create();    
		FranquiaEstabelecimento Create(bool ativo, bool compoeConsolidadoFranqueador, Perlink.Trinks.Pessoas.Estabelecimento estabelecimentoModelo, Perlink.Trinks.Pessoas.Franquia franquia, bool habilitaAlteracaoDeFotos, int idEstabelecimento, string nomeUnidade, bool permiteAlteracaoDeValores, bool permiteAlterarLogo, Perlink.Trinks.Pessoas.Enums.TipoDeCadastroDeEstabelecimentoFranqueado tipoDeCadastroDeEstabelecimento, int? identificacaoDoEstabelecimentoNaFranquia, bool habilitaTransferenciaDePontosDeFidelidade, bool permiteAcessoBackOfficePorOutrosFranqueadores, Perlink.Trinks.Pessoas.Enums.TipoCompartilhaNaRedeEnum tipoCompartilhamentoNaRede, bool existePacoteDaRedeDisponivelParaConsumo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FuncaoDoProfissional.
    /// </summary>
    public partial interface IFuncaoDoProfissionalFactory :IBaseFactory {  
    
		FuncaoDoProfissional Create();    
		FuncaoDoProfissional Create(int id, string descricao, int idEstabelecimento, int? idFuncaoProfissionalPadrao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FuncaoDoProfissionalPadrao.
    /// </summary>
    public partial interface IFuncaoDoProfissionalPadraoFactory :IBaseFactory {  
    
		FuncaoDoProfissionalPadrao Create();    
		FuncaoDoProfissionalPadrao Create(int id, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoAcaoUnidadeAoModelo.
    /// </summary>
    public partial interface IHistoricoAcaoUnidadeAoModeloFactory :IBaseFactory {  
    
		HistoricoAcaoUnidadeAoModelo Create();    
		HistoricoAcaoUnidadeAoModelo Create(int id, int idEstabelecimentoModelo, int idEstabelecimentoUnidade, int idPessoaQueRealizouAcao, System.DateTime dataVinculo, string acaoRealizado, string origem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoCliente.
    /// </summary>
    public partial interface IHistoricoClienteFactory :IBaseFactory {  
    
		HistoricoCliente Create();    
		HistoricoCliente Create(int? idHistoricoCliente, int? idFranquia, int? idEstabelecimento, string nomeCliente, string numeroTelefone, System.DateTime? dataHistorico, string nomeServico, string nomeProfissional, decimal valor, string observacao, string cpf);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Horario.
    /// </summary>
    public partial interface IHorarioFactory :IBaseFactory {  
    
		Horario Create();    
		Horario Create(bool ativo, Perlink.Trinks.Pessoas.Cliente cliente, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento, int? codigo, int id, System.DateTime? dataDoStatus, System.DateTime dataFim, System.DateTime? dataHoraNotificacaoClienteEnviada, System.DateTime? dataHoraNotificacaoClienteProgramada, System.DateTime dataInicio, int duracao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, bool foiPago, bool foiPagoAntecipadamente, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissionalAssistente, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.HorarioHistorico> historicos, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.HorarioHistoricoEtiqueta> historicosEtiqueta, Perlink.Trinks.Pessoas.HorarioOrigem horarioOrigem, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.HorarioTransacao> horariosTransacoes, System.Collections.Generic.IList<Perlink.Trinks.Vendas.PreVendaServico> preVendas, string mensagemPreco, string observacao, string observacaoCliente, bool possuiHistorico, int precoFixo, Perlink.Trinks.Pessoas.PessoaFisica pessoaQuemMarcou, System.DateTime dataHoraCriacao, System.DateTime? dataDeEmissaoFilaParaEnvio, Perlink.Trinks.Pessoas.Profissional profissional, Perlink.Trinks.Pessoas.RecorrenciaHorario recorrenciaHorario, int? idRecorrenciaHorario, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Pessoas.StatusHorario status, decimal valor, string promoCode, System.Collections.Generic.IList<Perlink.Trinks.EstoqueComBaixaAutomatica.UsoDeProdutoNoHorario> usoDeProdutosNoHorario, bool dataInicioAlterado, bool duracaoAlterado, bool profissionalAlterado, bool servicoEstabelecimentoAlterado, bool statusFoiAlterado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioHistorico.
    /// </summary>
    public partial interface IHorarioHistoricoFactory :IBaseFactory {  
    
		HorarioHistorico Create();    
		HorarioHistorico Create(bool alteradoPeloPAT, bool ativo, Perlink.Trinks.Pessoas.Cliente cliente, int codigo, System.DateTime dataFim, System.DateTime dataHoraAlteracao, System.DateTime dataInicio, int duracao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.Horario horario, int idHorario, Perlink.Trinks.Pessoas.HorarioQuemCancelou horarioQuemCancelou, int? idComanda, string mensagemPreco, string motivoCancelamento, int? numeroComanda, string observacao, string observacaoCliente, Perlink.Trinks.Pessoas.PessoaFisica pessoaQuemMarcou, Perlink.Trinks.Pessoas.PessoaFisica pessoaQuemRealizouAlteracao, string precoEmReais, int precoFixo, Perlink.Trinks.Pessoas.Profissional profissional, string nomeAssistente, Perlink.Trinks.Pessoas.RecorrenciaHorario recorrenciaHorario, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Pessoas.StatusHorario status, decimal valor, decimal? valorPagoAntecipadamente, Perlink.Trinks.Financeiro.Transacao transacao, bool alteradoPorComandaRapida, bool alteradoNaAgendaClientePorProfissionalAcessoSomenteAEla);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioHistoricoEtiqueta.
    /// </summary>
    public partial interface IHorarioHistoricoEtiquetaFactory :IBaseFactory {  
    
		HorarioHistoricoEtiqueta Create();    
		HorarioHistoricoEtiqueta Create(int id, Perlink.Trinks.Pessoas.Horario horario, Perlink.Trinks.Pessoas.HorarioHistorico horarioHistorico, string observacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioOrigem.
    /// </summary>
    public partial interface IHorarioOrigemFactory :IBaseFactory {  
    
		HorarioOrigem Create();    
		HorarioOrigem Create(int idHorarioOrigem, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioQuemCancelou.
    /// </summary>
    public partial interface IHorarioQuemCancelouFactory :IBaseFactory {  
    
		HorarioQuemCancelou Create();    
		HorarioQuemCancelou Create(int codigo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioTrabalho.
    /// </summary>
    public partial interface IHorarioTrabalhoFactory :IBaseFactory {  
    
		HorarioTrabalho Create();    
		HorarioTrabalho Create(int codigo, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, Perlink.Trinks.Pessoas.DiaSemana diaSemana, System.TimeSpan? horaEntrada, System.TimeSpan? horaSaida, System.TimeSpan? inicioIntervalo, System.TimeSpan? fimIntervalo, System.TimeSpan? inicioAlmoco, System.TimeSpan? fimAlmoco, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioTransacao.
    /// </summary>
    public partial interface IHorarioTransacaoFactory :IBaseFactory {  
    
		HorarioTransacao Create();    
		HorarioTransacao Create(bool ativo, int codigo, Perlink.Trinks.Financeiro.Comissao comissao, System.DateTime? dataHoraInicioHorario, decimal? descartaveis, decimal? desconto, Perlink.Trinks.Pessoas.Horario horario, Perlink.Trinks.Financeiro.Comissao comissaoAssistente, Perlink.Trinks.Pacotes.ItemPacoteCliente itemPacoteCliente, Perlink.Trinks.Financeiro.MotivoDesconto motivoDesconto, decimal? preco, Perlink.Trinks.Pessoas.TipoDesconto tipoDesconto, bool realizadoPorProfissionalParceiroAtivo, bool realizadoPorAssistenteParceiroAtivo, Perlink.Trinks.Financeiro.Transacao transacao, int pontosDeFidelidade, bool usouPontosDeFidelidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioVeraoCidade.
    /// </summary>
    public partial interface IHorarioVeraoCidadeFactory :IBaseFactory {  
    
		HorarioVeraoCidade Create();    
		HorarioVeraoCidade Create(int idHorarioVeraoUF, Perlink.Trinks.Pessoas.Cidade cidade, System.DateTime dataHoraInicioHorarioVerao, System.DateTime dataHoraFimHorarioVerao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioVeraoUF.
    /// </summary>
    public partial interface IHorarioVeraoUFFactory :IBaseFactory {  
    
		HorarioVeraoUF Create();    
		HorarioVeraoUF Create(int idHorarioVeraoUF, Perlink.Trinks.Pessoas.UF uF, System.DateTime dataHoraInicioHorarioVerao, System.DateTime dataHoraFimHorarioVerao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HotsiteEstabelecimento.
    /// </summary>
    public partial interface IHotsiteEstabelecimentoFactory :IBaseFactory {  
    
		HotsiteEstabelecimento Create();    
		HotsiteEstabelecimento Create(int codigo, Perlink.Trinks.Pessoas.StatusHorario statusPadraoAgendamentoWeb, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.TemaHotsite temaHotsite, string url, Perlink.Trinks.Pessoas.ConfiguracaoHotsiteIntervalo configuracaoHotsiteIntervalo, Perlink.Trinks.Pessoas.ConfiguracaoHotsiteAderencia configuracaoHotsiteAderencia, Perlink.Trinks.Pessoas.ConfiguracaoHotsiteInicioMarcos configuracaoHotsiteInicioMarcos, Perlink.Trinks.Pessoas.ConfiguracaoHotsiteUniverso configuracaoHotsiteUniverso, bool permiteBuscaHotsite, bool? valorAnteriorPermiteBuscaHotsite, bool permiteAgendamentoHotsite, bool permiteExibicaoTelefonesHotsite, bool permiteExibicaoVitrineProfissionaisHotsite, bool permiteExibicaoPrecoHotsite, bool permiteAgendamentosRepetidosNoDiaHotsite, Perlink.Trinks.Pessoas.Enums.PeriodoDiasEnum numeroDiasMaximoParaAgendarHorarios, bool desejaAparecerBuscaPortal, bool desejaTerHotsite, bool permiteExibicaoPacotesHotsite, bool prontoParaAparecerNoPortalPelaPrimeiraVez, bool desejaOcultarDoPortal, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueDesejaOcultarDoPortal, System.DateTime? dataDoPedidoDeOcultacaoDoPortal, System.DateTime? dataQueEstaPronto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemComboCliente.
    /// </summary>
    public partial interface IItemComboClienteFactory :IBaseFactory {  
    
		ItemComboCliente Create();    
		ItemComboCliente Create(int idClienteEstabelecimento, int idCliente, int idPessoa, int idEstabelecimento, string nome, string email, string telefones, string telefonesInexado);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDoHorario.
    /// </summary>
    public partial interface ILinkDePagamentoDoHorarioFactory :IBaseFactory {  
    
		LinkDePagamentoDoHorario Create();    
		LinkDePagamentoDoHorario Create(int id, int idLinkDePagamento, int idHorario, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MidiaSocial.
    /// </summary>
    public partial interface IMidiaSocialFactory :IBaseFactory {  
    
		MidiaSocial Create();    
		MidiaSocial Create(int idMidiaSocial, string identificacao, bool ativo, Perlink.Trinks.Pessoas.Enums.TipoMidiaSocialEnum tipoMidiaSocial, Perlink.Trinks.Pessoas.PessoaFisica pessoa);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoAusencia.
    /// </summary>
    public partial interface IMotivoAusenciaFactory :IBaseFactory {  
    
		MotivoAusencia Create();    
		MotivoAusencia Create(int codigo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoQueEscolheuOTrinks.
    /// </summary>
    public partial interface IMotivoQueEscolheuOTrinksFactory :IBaseFactory {  
    
		MotivoQueEscolheuOTrinks Create();    
		MotivoQueEscolheuOTrinks Create(int id, string descricao, int ordem, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoQueEstabelecimentoEscolheuOTrinks.
    /// </summary>
    public partial interface IMotivoQueEstabelecimentoEscolheuOTrinksFactory :IBaseFactory {  
    
		MotivoQueEstabelecimentoEscolheuOTrinks Create();    
		MotivoQueEstabelecimentoEscolheuOTrinks Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.MotivoQueEscolheuOTrinks motivo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NotificacaoEstabelecimento.
    /// </summary>
    public partial interface INotificacaoEstabelecimentoFactory :IBaseFactory {  
    
		NotificacaoEstabelecimento Create();    
		NotificacaoEstabelecimento Create(int idNotificacaoEstabelecimento, Perlink.Trinks.Pessoas.Conta conta, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.Horario horario, System.DateTime dataHoraReferencia, System.DateTime dataHoraRegistroNotificacao, bool visualizada, string texto, int tipoNotificacao, int? notaAvaliacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Novidade.
    /// </summary>
    public partial interface INovidadeFactory :IBaseFactory {  
    
		Novidade Create();    
		Novidade Create(int idNovidade, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizouOperacao, string nomeArquivoS3, System.DateTime dataNovidade, System.DateTime? dataInicioVigencia, System.DateTime? dataFimVigencia);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ParametrizacaoTrinks.
    /// </summary>
    public partial interface IParametrizacaoTrinksFactory :IBaseFactory {  
    
		ParametrizacaoTrinks Create();    
		ParametrizacaoTrinks Create(string nome, string valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PeriodoAusencia.
    /// </summary>
    public partial interface IPeriodoAusenciaFactory :IBaseFactory {  
    
		PeriodoAusencia Create();    
		PeriodoAusencia Create(int codigo, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, Perlink.Trinks.Pessoas.MotivoAusencia motivo, System.DateTime? dataHoraInicio, System.DateTime? dataHoraFim, string observacao, bool ativo, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueCriou, System.DateTime? dataHoraQueFoiCriado, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueFezUltimaAlteracao, System.DateTime? dataHoraUltimaAlteracao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PermissoesDaUnidadeBaseadaEmModelo.
    /// </summary>
    public partial interface IPermissoesDaUnidadeBaseadaEmModeloFactory :IBaseFactory {  
    
		PermissoesDaUnidadeBaseadaEmModelo Create();    
		PermissoesDaUnidadeBaseadaEmModelo Create(int id, int idEstabelecimentoModelo, int idEstabelecimentoUnidade, System.DateTime dataUltimaAlteracao, Perlink.Trinks.Pessoas.PermissoesParaClubeDeAssinaturas permissoesParaClubeDeAssinaturas, Perlink.Trinks.Pessoas.PermissoesParaPacotes permissoesParaPacotes, Perlink.Trinks.Pessoas.PermissoesParaProdutos permissoesParaProdutos, Perlink.Trinks.Pessoas.PermissoesParaServicos permissoesParaServicos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Pessoa.
    /// </summary>
    public partial interface IPessoaFactory :IBaseFactory {  
    
		Pessoa Create();    
		Pessoa Create(int idPessoa, System.DateTime? dataCadastro, System.DateTime? dataAtualizacao, bool ativo, string codigoSalesForce, string email, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Endereco> enderecos, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Telefone> telefones, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Conta> contas, System.Collections.Generic.IList<Perlink.Trinks.Cobranca.ContaFinanceira> contasFinanceiras, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.FotoEstabelecimento> fotos, Perlink.Trinks.Pessoas.Conta primeiraConta, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisica, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, Perlink.Trinks.Pessoas.Endereco enderecoProprio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaFisica.
    /// </summary>
    public partial interface IPessoaFisicaFactory :IBaseFactory {  
    
		PessoaFisica Create();    
		PessoaFisica Create(string apelido, string codigoIntegracao, string idGoogleReserveUser, string cpf, string ctps, string ctpsSerie, System.DateTime? dataExpedicaoRG, System.DateTime? dataNascimento, Perlink.Trinks.Pessoas.EstadoCivil estadoCivil, Perlink.Trinks.Pessoas.FotoPessoa fotoPrincipal, int idPessoaFisica, string nomeCompleto, string orgaoExpedidorRG, string pIS, string rG, Perlink.Trinks.Pessoas.VO.Genero genero, int? idPessoaUnificacao, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.UsuarioEstabelecimento> vinculoEstabelecimentos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridica.
    /// </summary>
    public partial interface IPessoaJuridicaFactory :IBaseFactory {  
    
		PessoaJuridica Create();    
		PessoaJuridica Create(int idPessoaJuridica, string cNPJ, string numeroContratoParceiro, string razaoSocial, string nomeFantasia, string inscricaoEstadual, string inscricaoMunicipal, Perlink.Trinks.Pessoas.PessoaJuridicaConfiguracaoNFe configuracaoNFe, Perlink.Trinks.Pessoas.PessoaFisica responsavelFinanceiro, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaConfiguracaoNFe.
    /// </summary>
    public partial interface IPessoaJuridicaConfiguracaoNFeFactory :IBaseFactory {  
    
		PessoaJuridicaConfiguracaoNFe Create();    
		PessoaJuridicaConfiguracaoNFe Create(int idPessoaJuridica, string numeroProcessoJudicial, string codigoOptanteSimplesNacional, string codigoTipoRegimeEspecialTributacao, string itemListaServico, string codigoTributacaoMunicipio, string codigoCnae, bool? habilitaEnviarCnaeNull, int ultimoLoteGerado, int ultimoRpsEmitido, decimal aliquotaISS, decimal? aliquota_Iss_NaoFormatada, string cpfUsuarioCadastrado, string senhaUsuarioCadastrado, bool aguardandoEnvio, System.DateTime? dataUltimoEnvio, string cmcPrestador, bool envioWebServiceHabilitado, string idExterno, string idExternoHomologacao, Perlink.Trinks.RPS.Enums.IntegracaoNFSeEnum integracaoNFSe, string serie, string naturezaDaOperacao, bool possuiNfseDeducao, bool possuiNfseBaseCalculo, bool possuiNfseBaseCalculoValorToral, bool permitirEnvioDataEmissaoNaDataCompetencia, bool permitirEnvioDataCompetenciaNaDataEmissao, bool? utilizarComissaoProfissionalComoCota, bool? habilitarNaoEmitirDescontoOperadora, bool utilizarPercentualCargaTributaria, bool issRetido, bool exibirBaseCalculoDescricao, bool producao, bool emitirNfseBrasilia, int? cfpsNoMunicipio, int? cfpsForaMunicipio, int? cfpsForaEstado, Perlink.Trinks.RPS.Enums.IntegracaoNFSeEnum? integracaoNFSeAnteriorNacionalMei, bool habilitadoPadraoNacionalMei, bool habilitadoEmissaoEI, bool habilitadoPadraoNacionalEstabelecimento, bool habilitarItemvUnitDuasCasasDecimais, decimal aliquotaPis, decimal aliquotaCofins);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Profissional.
    /// </summary>
    public partial interface IProfissionalFactory :IBaseFactory {  
    
		Profissional Create();    
		Profissional Create(int idProfissional, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisica, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoProfissional> estabelecimentoProfissionalLista, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Horario> horarios, System.Collections.Generic.List<Perlink.Trinks.Pessoas.Estabelecimento> estabelecimentosAtivos, Perlink.Trinks.Pessoas.FotoPessoa fotoPrincipal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Questionario.
    /// </summary>
    public partial interface IQuestionarioFactory :IBaseFactory {  
    
		Questionario Create();    
		Questionario Create(int codigo, string nomeDoPromotor, System.DateTime dataVisita, string tipoDaVisita, string nomeDoEstabelecimento, string tamanhoDoEstabelecimento, string enderecoDoEstabelecimento, string telefonesDoEstabelecimento, string possuiComputador, string possuiRecepcionista, string nivelOrganizacional, string foiRecebido, string foiRecebidoPor, string cargoDeQuemRecepcionou, string proprietarioPresente, string nomeProprietario, string tipoDaAgenda, string apresentouOTrinks, string videoPrincipalFoiApresentado, string videoTutorialFoiApresentado, string nivelDeInteresse, string foiMarcadoRetorno, string quandoRetornar, string observacoes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RecorrenciaHorario.
    /// </summary>
    public partial interface IRecorrenciaHorarioFactory :IBaseFactory {  
    
		RecorrenciaHorario Create();    
		RecorrenciaHorario Create(Perlink.Trinks.Pessoas.Cliente cliente, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Horario> horarios, int idHorarioRecorrencia, Perlink.Trinks.Pessoas.TipoRecorrenciaHorario tipoRecorrenciaHorario, System.DateTime dataInicio, System.DateTime? dataLimite, string observacao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioFormaPagamento.
    /// </summary>
    public partial interface IRelatorioFormaPagamentoFactory :IBaseFactory {  
    
		RelatorioFormaPagamento Create();    
		RelatorioFormaPagamento Create(int idTransacaoParcela, Perlink.Trinks.Financeiro.FormaPagamento formaPagamento, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizou, Perlink.Trinks.Pessoas.Pessoa pessoaQueRecebeu, Perlink.Trinks.Pessoas.Pessoa pessoaQuePagou, Perlink.Trinks.Financeiro.TipoTransacao tipoTransacao, int? idTransacaoQueEstounouEsta, System.DateTime dataReferencia, System.DateTime dataHoraTransacao, System.DateTime dataHoraParaReceberOperadora, decimal valorPago, decimal percentualCobradoOperadora, decimal? valorFixcoCobradoPelaOperadora, decimal valorCobradoOperadora, decimal valorASerRecebido, decimal diasParaReceberDaOperadora, int numeroParcela, int numeroParcelas, decimal valorSplit, decimal? valorRecebimentoAnterior, decimal valorDescontoAntecipacao, int? idLancamentoAntecipacao, int? idTransacaoFormaPagamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SaldoDeSMSLembreteDoEstabelecimento.
    /// </summary>
    public partial interface ISaldoDeSMSLembreteDoEstabelecimentoFactory :IBaseFactory {  
    
		SaldoDeSMSLembreteDoEstabelecimento Create();    
		SaldoDeSMSLembreteDoEstabelecimento Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int limiteDeEnviosEmPeriodoGratis, int quantidadeDeEnviadosNoPeriodo, System.DateTime? dataUltimaRenovacaoQuantidadeDeEnvios);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Servico.
    /// </summary>
    public partial interface IServicoFactory :IBaseFactory {  
    
		Servico Create();    
		Servico Create(bool ativo, string codigoInterno, string descricao, int duracao, int idServico, string nome, decimal preco, Perlink.Trinks.Pessoas.ServicoCategoria servicoCategoria, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.TipoServicoEstabelecimento> tiposServicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoCategoria.
    /// </summary>
    public partial interface IServicoCategoriaFactory :IBaseFactory {  
    
		ServicoCategoria Create();    
		ServicoCategoria Create(int codigo, string nome, string descricao, bool ativo, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Servico> servicosPadroes, Perlink.Trinks.Pessoas.CategoriaPortalServico categoriaPortal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoCategoriaEstabelecimento.
    /// </summary>
    public partial interface IServicoCategoriaEstabelecimentoFactory :IBaseFactory {  
    
		ServicoCategoriaEstabelecimento Create();    
		ServicoCategoriaEstabelecimento Create(int codigo, string nome, string descricao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.ServicoCategoria servicoCategoria, bool ativo, bool ehCategoriaUtilizadaEmCadastroMinimo, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.ServicoEstabelecimento> servicos, Perlink.Trinks.Pessoas.FotoServicoCategoriaEstabelecimento foto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoEstabelecimento.
    /// </summary>
    public partial interface IServicoEstabelecimentoFactory :IBaseFactory {  
    
		ServicoEstabelecimento Create();    
		ServicoEstabelecimento Create(bool ativo, string codigoInterno, bool ehUsadoNaFilaDeEspera, decimal custoDescartaveis, System.DateTime? dataAtualizacao, System.DateTime? dataCadastro, string descricao, int duracao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.EstabelecimentoProfissionalServico> estabelecimentoProfissionalServicoLista, bool exibePreco, int idServicoEstabelecimento, string nome, decimal preco, bool precoFixo, Perlink.Trinks.Pessoas.Servico servico, Perlink.Trinks.Pessoas.ServicoCategoriaEstabelecimento servicoCategoriaEstabelecimento, Perlink.Trinks.Pessoas.TipoPreco tipoPreco, string codigoNCM, decimal? aliquotaICMS, decimal? aliquotaPIS, decimal? aliquotaCOFINS, int? idSituacaoTributaria, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimentoModelo, Perlink.Trinks.Promocoes.Promocao promocao, System.Collections.Generic.List<Perlink.Trinks.Promocoes.PromocaoDiaDaSemana> listaPromocaoDiaDaSemana, System.Collections.Generic.List<Perlink.Trinks.Promocoes.PromocaoDiaDaSemana> listaPromocaoDiaDaSemanaOriginal, bool servicoIndiposnivelParaCliente, bool clienteGanhaPontos, bool clientePodeResgatarPontos, int pontosNecessariosParaResgate);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoSinonimo.
    /// </summary>
    public partial interface IServicoSinonimoFactory :IBaseFactory {  
    
		ServicoSinonimo Create();    
		ServicoSinonimo Create(int idServicoSinonimo, Perlink.Trinks.Pessoas.Servico primeiroServico, Perlink.Trinks.Pessoas.Servico segundoServico);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SincronizacaoEntreEstabelecimentosModelosFila.
    /// </summary>
    public partial interface ISincronizacaoEntreEstabelecimentosModelosFilaFactory :IBaseFactory {  
    
		SincronizacaoEntreEstabelecimentosModelosFila Create();    
		SincronizacaoEntreEstabelecimentosModelosFila Create(int id, int idEstabelecimento, Perlink.Trinks.Pessoas.Enums.TipoSincronizacaoEnum tipoSincronizacao, System.DateTime dataAgendamento, System.DateTime? dataFinalizacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SincronizacaoEstabelecimentosFila.
    /// </summary>
    public partial interface ISincronizacaoEstabelecimentosFilaFactory :IBaseFactory {  
    
		SincronizacaoEstabelecimentosFila Create();    
		SincronizacaoEstabelecimentosFila Create(int id, int idEstabelecimento, Perlink.Trinks.Pessoas.Enums.TipoSincronizacaoEnum tipoSincronizacao, System.DateTime dataAgendamento, System.DateTime? dataFinalizacao, int prioridade, int? qtdRegistros);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade StatusHorario.
    /// </summary>
    public partial interface IStatusHorarioFactory :IBaseFactory {  
    
		StatusHorario Create();    
		StatusHorario Create(int codigo, string nome, string descricao, string corAntiga, string cor, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Telefone.
    /// </summary>
    public partial interface ITelefoneFactory :IBaseFactory {  
    
		Telefone Create();    
		Telefone Create(int idTelefone, Perlink.Trinks.Pessoas.Pessoa pessoa, int idPessoa, string dDD, string numero, string ramal, Perlink.Trinks.Pessoas.Pessoa dono, Perlink.Trinks.Pessoas.TipoTelefone tipo, Perlink.Trinks.Notificacoes.Operadora operadora, bool ativo, string ddi);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TelefoneInternacional.
    /// </summary>
    public partial interface ITelefoneInternacionalFactory :IBaseFactory {  
    
		TelefoneInternacional Create();    
		TelefoneInternacional Create(int idTelefone, int idPessoa, int idCliente, string dDI, string dDD, string numero, int tipo, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TemaHotsite.
    /// </summary>
    public partial interface ITemaHotsiteFactory :IBaseFactory {  
    
		TemaHotsite Create();    
		TemaHotsite Create(int codigo, Perlink.Trinks.Pessoas.TemplateHotsite templateHotsite, string nome, string descricao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Template.
    /// </summary>
    public partial interface ITemplateFactory :IBaseFactory {  
    
		Template Create();    
		Template Create(int id, string valor, string ajuda, Perlink.Trinks.Pessoas.TipoTemplate tipoTemplate);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TemplateHotsite.
    /// </summary>
    public partial interface ITemplateHotsiteFactory :IBaseFactory {  
    
		TemplateHotsite Create();    
		TemplateHotsite Create(int codigo, string nome, string descricao, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.TemaHotsite> temaHotsiteLista);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoComissao.
    /// </summary>
    public partial interface ITipoComissaoFactory :IBaseFactory {  
    
		TipoComissao Create();    
		TipoComissao Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDesconto.
    /// </summary>
    public partial interface ITipoDescontoFactory :IBaseFactory {  
    
		TipoDesconto Create();    
		TipoDesconto Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoEstabelecimento.
    /// </summary>
    public partial interface ITipoEstabelecimentoFactory :IBaseFactory {  
    
		TipoEstabelecimento Create();    
		TipoEstabelecimento Create(int idTipoEstabelecimento, string nome, bool ativo, string genero, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.TipoServicoEstabelecimento> servicosPadroes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoFranquia.
    /// </summary>
    public partial interface ITipoFranquiaFactory :IBaseFactory {  
    
		TipoFranquia Create();    
		TipoFranquia Create(int idTipoFranquia, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoLogradouro.
    /// </summary>
    public partial interface ITipoLogradouroFactory :IBaseFactory {  
    
		TipoLogradouro Create();    
		TipoLogradouro Create(int idTipoLogragrouro, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoPOS.
    /// </summary>
    public partial interface ITipoPOSFactory :IBaseFactory {  
    
		TipoPOS Create();    
		TipoPOS Create(int id, string descricao, string nomeExibicao, bool habilitaSplit, int numeroMinimoDiasRecebimento, bool percentualDescontoOperadoraIncideSobreValorTotal, bool permiteGerarSplitManualmente, int limiteMaximoHorasParaGerarSplitManualmente, bool apenasUltimaTransacaoPodeIndicarSplitManual, bool permiteAlteracaoTaxaOperadora, decimal? taxaMDR, decimal? taxaRAVBase, int? diasRecebimentoOperadora, bool podeAlterarDiasRecebimentoOperadora, bool descontaTaxaOperadoraNaComissao, bool ehSplitStone, bool ehSplitGranitoPos, bool ehSplitSiclos, bool ehConnectStone, bool ehConnectPagarme);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoPreco.
    /// </summary>
    public partial interface ITipoPrecoFactory :IBaseFactory {  
    
		TipoPreco Create();    
		TipoPreco Create(int codigo, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoRecorrenciaHorario.
    /// </summary>
    public partial interface ITipoRecorrenciaHorarioFactory :IBaseFactory {  
    
		TipoRecorrenciaHorario Create();    
		TipoRecorrenciaHorario Create(int idTipoRecorrenciaHorario, string nome, int? diasFrequencia, int? duracaoMaxima, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoServicoEstabelecimento.
    /// </summary>
    public partial interface ITipoServicoEstabelecimentoFactory :IBaseFactory {  
    
		TipoServicoEstabelecimento Create();    
		TipoServicoEstabelecimento Create(int idTipoServicoEstabelecimento, Perlink.Trinks.Pessoas.TipoEstabelecimento tipoEstabelecimento, Perlink.Trinks.Pessoas.Servico servico, bool ehItemListaReduzida, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoTelefone.
    /// </summary>
    public partial interface ITipoTelefoneFactory :IBaseFactory {  
    
		TipoTelefone Create();    
		TipoTelefone Create(bool ativo, int idTipoTelefone, string nome);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoTemplate.
    /// </summary>
    public partial interface ITipoTemplateFactory :IBaseFactory {  
    
		TipoTemplate Create();    
		TipoTemplate Create(int idTipoTemplate, string nome);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UF.
    /// </summary>
    public partial interface IUFFactory :IBaseFactory {  
    
		UF Create();    
		UF Create(int idUF, string nome, string sigla, bool ativo, string codigoIBGE, int fuso, bool nfceHabilitada, string nfcDesabilitadaMensagem, bool icmsStHabilitado, System.Collections.Generic.IList<Perlink.Trinks.Pessoas.Cidade> cidades);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UnidadeMedida.
    /// </summary>
    public partial interface IUnidadeMedidaFactory :IBaseFactory {  
    
		UnidadeMedida Create();    
		UnidadeMedida Create(int id, string nome, string simbolo, string genero, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioEstabelecimento.
    /// </summary>
    public partial interface IUsuarioEstabelecimentoFactory :IBaseFactory {  
    
		UsuarioEstabelecimento Create();    
		UsuarioEstabelecimento Create(int idUsuarioEstabelecimento, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisica, bool ehResponsavel, System.DateTime dataCadastro, System.DateTime dataAtualizacao, bool ativo, int alturaHoraInteiraAgenda, int larguraColunaAgenda, bool exibeProfissionaisEmDiaDeFolga, Perlink.Trinks.Pessoas.Enums.AcessoBackoffice perfilAcesso, Perlink.Trinks.Permissoes.UsuarioPerfil usuarioPerfil, string urlDestino);
			 
    }
 
}
namespace Perlink.Trinks.Pessoas.Builders.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Configuracoes.Factories {

 
}
namespace Perlink.Trinks.Pessoas.DTO.Factories {

 
}
namespace Perlink.Trinks.Pessoas.DTO.Filtros.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Enums.Factories {

 
}
namespace Perlink.Trinks.Pessoas.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Factories.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Filtros.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Helpers.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Interfaces.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Repositories.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Statics.Factories {

 
}
namespace Perlink.Trinks.Pessoas.Stories.Factories {

 
}
namespace Perlink.Trinks.Pessoas.VO.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Enums.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Factories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProdutoCategoria.
    /// </summary>
    public partial interface IEstabelecimentoProdutoCategoriaFactory :IBaseFactory {  
    
		EstabelecimentoProdutoCategoria Create();    
		EstabelecimentoProdutoCategoria Create(int idEstabelecimentoProdutoCategoria, string nome, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.ProdutoEstoque.ProdutoCategoriaPadrao produtoCategoriaPadrao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Inventario.
    /// </summary>
    public partial interface IInventarioFactory :IBaseFactory {  
    
		Inventario Create();    
		Inventario Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.ProdutoEstoque.Enums.InventarioStatusEnum status, System.DateTime dataHoraAbertura, System.DateTime? dataHoraConclusao, System.DateTime? dataHoraCancelamento, System.DateTime dataHoraUltimaAlteracao, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueFezUltimaAlteracao, System.Collections.Generic.IList<Perlink.Trinks.ProdutoEstoque.ProdutoDoInventario> produtos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade InventarioMovimentacaoEstoque.
    /// </summary>
    public partial interface IInventarioMovimentacaoEstoqueFactory :IBaseFactory {  
    
		InventarioMovimentacaoEstoque Create();    
		InventarioMovimentacaoEstoque Create(int id, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoMovimentacaoEstoque, Perlink.Trinks.ProdutoEstoque.ProdutoDoInventario produtoDoInventario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentoEstoqueTipo.
    /// </summary>
    public partial interface IMovimentoEstoqueTipoFactory :IBaseFactory {  
    
		MovimentoEstoqueTipo Create();    
		MovimentoEstoqueTipo Create(int idMovimentoEstoqueTipo, Perlink.Trinks.ProdutoEstoque.MovimentoEstoqueTipo movimentoEstoqueTipoPai, string descricao, bool ativo, bool disponivelMovimentacaoEstoque);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OpcaoDaPropriedadeDeProduto.
    /// </summary>
    public partial interface IOpcaoDaPropriedadeDeProdutoFactory :IBaseFactory {  
    
		OpcaoDaPropriedadeDeProduto Create();    
		OpcaoDaPropriedadeDeProduto Create(int id, Perlink.Trinks.ProdutoEstoque.PropriedadeDeProduto propriedadeDeProduto, string conteudo, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ProdutoCategoriaPadrao.
    /// </summary>
    public partial interface IProdutoCategoriaPadraoFactory :IBaseFactory {  
    
		ProdutoCategoriaPadrao Create();    
		ProdutoCategoriaPadrao Create(int id, string nome, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ProdutoDoInventario.
    /// </summary>
    public partial interface IProdutoDoInventarioFactory :IBaseFactory {  
    
		ProdutoDoInventario Create();    
		ProdutoDoInventario Create(int id, Perlink.Trinks.ProdutoEstoque.Inventario inventario, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, string codigoDeIdentificacao, Perlink.Trinks.ProdutoEstoque.EstabelecimentoProdutoCategoria estabelecimentoProdutoCategoria, Perlink.Trinks.Pessoas.EstabelecimentoFabricanteProduto estabelecimentoFabricanteProduto, string descricao, Perlink.Trinks.Pessoas.UnidadeMedida unidadeMedida, int medidasPorUnidade, decimal? custoMedioUnitario, decimal? custoMedioFracionado, bool permitirRevenda, decimal precoRevendaCliente, decimal precoRevendaProfissional, int estoqueAtual, int? estoqueContagemParteEmUnidade, int? estoqueContagemParteEmFracao, int? diferencaEmEstoque, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque movimentacaoDeEstoque);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ProdutoPadrao.
    /// </summary>
    public partial interface IProdutoPadraoFactory :IBaseFactory {  
    
		ProdutoPadrao Create();    
		ProdutoPadrao Create(int id, string nome, Perlink.Trinks.Pessoas.FabricanteProdutoPadrao fabricanteProdutoPadrao, Perlink.Trinks.ProdutoEstoque.ProdutoCategoriaPadrao produtoCategoriaPadrao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PropriedadeDeProduto.
    /// </summary>
    public partial interface IPropriedadeDeProdutoFactory :IBaseFactory {  
    
		PropriedadeDeProduto Create();    
		PropriedadeDeProduto Create(int id, Perlink.Trinks.ProdutoEstoque.Enums.PropriedadePadraoEnum propriedadePadrao, string titulo, int? idFranquia, bool ativo, System.Collections.Generic.IList<Perlink.Trinks.ProdutoEstoque.OpcaoDaPropriedadeDeProduto> opcoes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorPropriedadeDoProduto.
    /// </summary>
    public partial interface IValorPropriedadeDoProdutoFactory :IBaseFactory {  
    
		ValorPropriedadeDoProduto Create();    
		ValorPropriedadeDoProduto Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.ProdutoEstoque.PropriedadeDeProduto propriedadeDeProduto, Perlink.Trinks.ProdutoEstoque.Enums.PropriedadePadraoEnum propriedadePadrao, Perlink.Trinks.ProdutoEstoque.OpcaoDaPropriedadeDeProduto opcaoDaPropriedadeDeProduto, Perlink.Trinks.ProdutoEstoque.ValorPropriedadeDoProduto valorPropriedadeDoProdutoModelo);
			 
    }
 
}
namespace Perlink.Trinks.ProdutoEstoque.ExtensionMethods.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Factories.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Filtros.Factories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Stories.Factories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Factories {

    /// <summary>
    /// Interface para repositório da entidade DisponibilidadeNaAgenda.
    /// </summary>
    public partial interface IDisponibilidadeNaAgendaFactory :IBaseFactory {  
    
		DisponibilidadeNaAgenda Create();    
		DisponibilidadeNaAgenda Create(int id, int idEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, bool disponivel, bool ativo, Perlink.Trinks.ProfissionalAgenda.TipoDeDisponibilidade tipo, System.DateTime dataHoraInicio, System.DateTime dataHoraFim);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LiberacaoDeHorarioNaAgenda.
    /// </summary>
    public partial interface ILiberacaoDeHorarioNaAgendaFactory :IBaseFactory {  
    
		LiberacaoDeHorarioNaAgenda Create();    
		LiberacaoDeHorarioNaAgenda Create(int id, Perlink.Trinks.ProfissionalAgenda.DisponibilidadeNaAgenda disponibilidadeNaAgenda, Perlink.Trinks.ProfissionalAgenda.MotivoDeLiberacao motivo, string observacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueCriou, System.DateTime dataHoraQueFoiCriado, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueFezUltimaAlteracao, System.DateTime? dataHoraUltimaAlteracao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDeLiberacao.
    /// </summary>
    public partial interface IMotivoDeLiberacaoFactory :IBaseFactory {  
    
		MotivoDeLiberacao Create();    
		MotivoDeLiberacao Create(int id, string nome, bool ativo, Perlink.Trinks.ProfissionalAgenda.TipoDeDisponibilidade tipoDeDisponibilidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioAusenciaELiberacaoHorario.
    /// </summary>
    public partial interface IRelatorioAusenciaELiberacaoHorarioFactory :IBaseFactory {  
    
		RelatorioAusenciaELiberacaoHorario Create();    
		RelatorioAusenciaELiberacaoHorario Create(int id, Perlink.Trinks.ProfissionalAgenda.TipoDeDisponibilidade tipoDeDisponibilidade, int idEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, System.DateTime dataInicio, System.DateTime dataFim, Perlink.Trinks.ProfissionalAgenda.MotivoDeLiberacao motivoDeLiberacao, string observacao, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDeDisponibilidade.
    /// </summary>
    public partial interface ITipoDeDisponibilidadeFactory :IBaseFactory {  
    
		TipoDeDisponibilidade Create();    
		TipoDeDisponibilidade Create(int id, string nome, int ordemDePrioridade);
			 
    }
 
}
namespace Perlink.Trinks.ProfissionalAgenda.DTO.Factories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Enums.Factories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Stories.Factories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Factories {

    /// <summary>
    /// Interface para repositório da entidade Cupom.
    /// </summary>
    public partial interface ICupomFactory :IBaseFactory {  
    
		Cupom Create();    
		Cupom Create(int idCupom, string guid, System.DateTime dataCriacao, string cPF, string email, string telefone, string nomeComprador, Perlink.Trinks.ProjetoBackToSalon.EstabelecimentoParticipanteBTS estabelecimentoParticipante, System.DateTime validade, bool ofertaLoreal, bool ofertaTrinks, bool ofertaEmail, bool ofertaSMS);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParticipanteBTS.
    /// </summary>
    public partial interface IEstabelecimentoParticipanteBTSFactory :IBaseFactory {  
    
		EstabelecimentoParticipanteBTS Create();    
		EstabelecimentoParticipanteBTS Create(int idEstabelecimento, string guid, string nomeFantasia, string cNPJ, string telefone, string email, Perlink.Trinks.ProjetoBackToSalon.Endereco endereco, int? idEstabelecimentoNoTrinks, string nomeCompletoDoResponsavel, string cPFDoResponsavel, int qtdCupons);
			 
    }
 
}
namespace Perlink.Trinks.ProjetoBackToSalon.DTO.Factories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Enum.Factories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Filters.Factories {

 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.Factories {

    /// <summary>
    /// Interface para repositório da entidade CompraDeVoucher.
    /// </summary>
    public partial interface ICompraDeVoucherFactory :IBaseFactory {  
    
		CompraDeVoucher Create();    
		CompraDeVoucher Create(int id, string guid, System.DateTime data, string cPF, string email, string telefone, string nomeComprador, Perlink.Trinks.ProjetoBelezaAmiga.EstabelecimentoParticipante estabelecimentoParticipante, decimal valorPago, string idPagamento, string fingerPrintCartao, bool aceiteOfertas);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParticipante.
    /// </summary>
    public partial interface IEstabelecimentoParticipanteFactory :IBaseFactory {  
    
		EstabelecimentoParticipante Create();    
		EstabelecimentoParticipante Create(int id, string guid, string token, string idPagarme, string nomeFantasia, string razaoSocial, bool queroReceberInformacoes, string cNPJ, string telefone, string email, string website, Perlink.Trinks.ProjetoBelezaAmiga.Endereco endereco, Perlink.Trinks.ProjetoBelezaAmiga.ContaBancaria contaBancaria, System.DateTime dataCriacao, System.DateTime? dataDeAberturaDaEmpresa, bool ativo, bool aprovado, int? idEstabelecimentoNoTrinks, string nomeCompletoDoResponsavel, Perlink.Trinks.Estabelecimentos.Enums.FaixaProfissionaisEstabelecimentoEnum faixa, Perlink.Trinks.Pessoas.Enums.TipoEstabelecimentoEnum tipo, string jaEhCliente, System.DateTime? dataDaAprovacao, System.DateTime? dataDaReprovacao, string origem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Voucher.
    /// </summary>
    public partial interface IVoucherFactory :IBaseFactory {  
    
		Voucher Create();    
		Voucher Create(int id, string codigoVoucher, Perlink.Trinks.ProjetoBelezaAmiga.CompraDeVoucher compraDeVoucher, Perlink.Trinks.ProjetoBelezaAmiga.Enums.TipoVoucherEnum tipoVoucher, System.DateTime validade);
			 
    }
 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.DTO.Factories {

 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.Enums.Factories {

 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.Factories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParticipanteESSV2.
    /// </summary>
    public partial interface IEstabelecimentoParticipanteESSV2Factory :IBaseFactory {  
    
		EstabelecimentoParticipanteESSV2 Create();    
		EstabelecimentoParticipanteESSV2 Create(int idEstabelecimento, string guid, string nomeFantasia, string cNPJ, string telefone, string logradouro, string numero, string bairro, string cidade, string estado, string cEP, string complemento, Perlink.Trinks.ProjetoEncontreSeuSalao.Coordenada coordenadas, bool ehClienteTrinks, string hotsite, bool aceitaNovidadesLoreal, int codigoEmissorLoreal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade GmapsLimitStatus.
    /// </summary>
    public partial interface IGmapsLimitStatusFactory :IBaseFactory {  
    
		GmapsLimitStatus Create();    
		GmapsLimitStatus Create(int id, System.DateTime date);
			 
    }
 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.DTO.Factories {

 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.Filters.Factories {

 
}
namespace Perlink.Trinks.Promocoes.Enums.Factories {

 
}
namespace Perlink.Trinks.Promocoes.Factories {

    /// <summary>
    /// Interface para repositório da entidade Promocao.
    /// </summary>
    public partial interface IPromocaoFactory :IBaseFactory {  
    
		Promocao Create();    
		Promocao Create(int idPromocao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, System.DateTime? dataInicio, System.DateTime? dataFim, int tipoVigencia);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoDiaDaSemana.
    /// </summary>
    public partial interface IPromocaoDiaDaSemanaFactory :IBaseFactory {  
    
		PromocaoDiaDaSemana Create();    
		PromocaoDiaDaSemana Create(int idPromocaoDiaDaSemana, Perlink.Trinks.Promocoes.Promocao promocao, int diaDaSemana, decimal? valor);
			 
    }
 
}
namespace Perlink.Trinks.PromocoesOnline.Factories {

    /// <summary>
    /// Interface para repositório da entidade AgendamentoTemporario.
    /// </summary>
    public partial interface IAgendamentoTemporarioFactory :IBaseFactory {  
    
		AgendamentoTemporario Create();    
		AgendamentoTemporario Create(int id, Perlink.Trinks.PromocoesOnline.PromocaoOnline promocaoOnline, Perlink.Trinks.LinksDePagamento.LinkDePagamento linkDePagamento, int idClienteEstabelecimento, int idProfissional, System.DateTime data, Perlink.Trinks.Pessoas.Horario horario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorariosDaPromocao.
    /// </summary>
    public partial interface IHorariosDaPromocaoFactory :IBaseFactory {  
    
		HorariosDaPromocao Create();    
		HorariosDaPromocao Create(int id, Perlink.Trinks.PromocoesOnline.PromocaoOnline promocaoOnline, Perlink.Trinks.Pessoas.Horario horario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoOnline.
    /// </summary>
    public partial interface IPromocaoOnlineFactory :IBaseFactory {  
    
		PromocaoOnline Create();    
		PromocaoOnline Create(int id, string nome, string tag, System.DateTime dataCriacaoUtc, int idPessoaQueCriou, System.DateTime dataInicioUtc, System.DateTime dataFimUtc, System.DateTime? dataDesativacaoManualUtc, int? idPessoaQueDesativou, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Financeiro.MotivoDesconto motivoDesconto, decimal desconto, int horariosAgendados, decimal totalEmVendas);
			 
    }
 
}
namespace Perlink.Trinks.PromocoesOnline.DTO.Factories {

 
}
namespace Perlink.Trinks.PromotoresDoTrinks.Factories {

    /// <summary>
    /// Interface para repositório da entidade HistoricoUsoCuponsParceria.
    /// </summary>
    public partial interface IHistoricoUsoCuponsParceriaFactory :IBaseFactory {  
    
		HistoricoUsoCuponsParceria Create();    
		HistoricoUsoCuponsParceria Create(int id, Perlink.Trinks.PromotoresDoTrinks.PromotorDoTrinks promotorDoTrinks, Perlink.Trinks.Pessoas.Estabelecimento estabelecimentoCadastrado, System.DateTime dataCadastro, bool assinou, int? transacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NovosProfissionaisPromotoresDoTrinks.
    /// </summary>
    public partial interface INovosProfissionaisPromotoresDoTrinksFactory :IBaseFactory {  
    
		NovosProfissionaisPromotoresDoTrinks Create();    
		NovosProfissionaisPromotoresDoTrinks Create(int linha, int idPessoa, string nomeCompleto, string email);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PromotorDoTrinks.
    /// </summary>
    public partial interface IPromotorDoTrinksFactory :IBaseFactory {  
    
		PromotorDoTrinks Create();    
		PromotorDoTrinks Create(int id, string nome, string email, string telefone, Perlink.Trinks.PromotoresDoTrinks.TipoDePromotor tipo, int? idPessoa, bool ativo, System.DateTime dataCadastro, bool recebeNotificacoes, bool acessouLandingPage);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SaldoPromotor.
    /// </summary>
    public partial interface ISaldoPromotorFactory :IBaseFactory {  
    
		SaldoPromotor Create();    
		SaldoPromotor Create(int id, Perlink.Trinks.PromotoresDoTrinks.PromotorDoTrinks promotorDoTrinks, decimal saldo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPromotor.
    /// </summary>
    public partial interface ITransacaoPromotorFactory :IBaseFactory {  
    
		TransacaoPromotor Create();    
		TransacaoPromotor Create(int id, Perlink.Trinks.PromotoresDoTrinks.PromotorDoTrinks promotorDoTrinks, Perlink.Trinks.PromotoresDoTrinks.TipoTransacaoPromotor tipoTransacao, System.DateTime data, decimal valor, bool sucesso, bool atendida, System.DateTime? dataUltimaAtualizacao, string descricao);
			 
    }
 
}
namespace Perlink.Trinks.PromotoresDoTrinks.DTO.Factories {

 
}
namespace Perlink.Trinks.PromotoresDoTrinks.Stories.Factories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Factories {

    /// <summary>
    /// Interface para repositório da entidade AssinaturaRecorrente.
    /// </summary>
    public partial interface IAssinaturaRecorrenteFactory :IBaseFactory {  
    
		AssinaturaRecorrente Create();    
		AssinaturaRecorrente Create(int idAssinaturaRecorrente, System.DateTime dataDeCriacao, System.DateTime? dataDaUltimaRenovacao, decimal? ultimoValorPago, Perlink.Trinks.RecorrenciaDeAssinatura.Enums.StatusDaAssinaturaRecorrenteEnum statusDaAssinatura, Perlink.Trinks.Pacotes.Pacote pacote, Perlink.Trinks.Pacotes.PacoteCliente pacoteCliente, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimento);
			 
    }
 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.DTO.Factories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Enums.Factories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Filtros.Factories {

 
}
namespace Perlink.Trinks.Relatorios.Factories {

    /// <summary>
    /// Interface para repositório da entidade ClienteAtendido.
    /// </summary>
    public partial interface IClienteAtendidoFactory :IBaseFactory {  
    
		ClienteAtendido Create();    
		ClienteAtendido Create(int id, int idEstabelecimento, int idClienteEstabelecimento, int idTransacao, System.DateTime dataAtendimento, int? idServicoEstabelecimento, int? idProfissional);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConsultaRelatorioConsolidadoDia.
    /// </summary>
    public partial interface IConsultaRelatorioConsolidadoDiaFactory :IBaseFactory {  
    
		ConsultaRelatorioConsolidadoDia Create();    
		ConsultaRelatorioConsolidadoDia Create(int codigo, int idEstabelecimento, System.DateTime dia, int numeroClientes, int numeroAtendimentos, decimal ticketMedio, decimal valorServicos, decimal valorProdutos, decimal valorPacotes, decimal valorClube, decimal valorDescontos, decimal valorTotal, decimal valorGorjeta, decimal valorCredito, decimal valorDebito, decimal valorDinheiro, decimal valorOutros, decimal valorPrePago, decimal valorCreditoCliente, decimal valorValePresente, decimal valorDividasDeixadas, decimal valorDividasPagas, decimal valorTroco, decimal valorDescontoCashback, decimal valorPix);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConsultaRelatorioConsolidadoEstabelecimentoClienteMes.
    /// </summary>
    public partial interface IConsultaRelatorioConsolidadoEstabelecimentoClienteMesFactory :IBaseFactory {  
    
		ConsultaRelatorioConsolidadoEstabelecimentoClienteMes Create();    
		ConsultaRelatorioConsolidadoEstabelecimentoClienteMes Create(long idConsolidadoEstabelecimentoCliente, int idEstabelecimento, int mes, int ano, int idEstabelecimentoCliente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConsultaRelatorioConsolidadoMes.
    /// </summary>
    public partial interface IConsultaRelatorioConsolidadoMesFactory :IBaseFactory {  
    
		ConsultaRelatorioConsolidadoMes Create();    
		ConsultaRelatorioConsolidadoMes Create(int codigo, int idEstabelecimento, int mes, int ano, int numeroClientes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeCliente.
    /// </summary>
    public partial interface IRankingDeClienteFactory :IBaseFactory {  
    
		RankingDeCliente Create();    
		RankingDeCliente Create(System.DateTime? dataNascimento, System.DateTime? dataReferencia, System.DateTime dataUltimaVenda, System.DateTime dataUltimoAgendamento, string cPF, string emailCliente, int idCliente, int idClienteEstabelecimento, int idPessoaQueRecebeu, int idPessoaQuePagou, int idUltimaTransacao, string nomeCliente, int numeroVisitas, decimal totalPacotes, decimal totalProdutos, decimal totalServicos, string telefones, System.DateTime dataDeCadastroCliente, long posicao, decimal valorTotal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeClienteEstendido.
    /// </summary>
    public partial interface IRankingDeClienteEstendidoFactory :IBaseFactory {  
    
		RankingDeClienteEstendido Create();    
		RankingDeClienteEstendido Create(System.DateTime? dataNascimento, System.DateTime? dataReferencia, System.DateTime dataUltimaVenda, System.DateTime dataUltimoAgendamento, string cPF, string emailCliente, int idCliente, int idClienteEstabelecimento, int idPessoaQueRecebeu, int idPessoaQuePagou, int idUltimaTransacao, string nomeCliente, int numeroVisitas, decimal totalPacotes, decimal totalProdutos, decimal totalServicos, string telefones, System.DateTime dataDeCadastroCliente, long posicao, decimal valorTotal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDePacotes.
    /// </summary>
    public partial interface IRankingDePacotesFactory :IBaseFactory {  
    
		RankingDePacotes Create();    
		RankingDePacotes Create(int id, int idPessoaEstabelecimento, int idPessoaCliente, int idTransacao, System.DateTime dataVenda, Perlink.Trinks.Pacotes.Pacote pacote, int quantidade, decimal totalPago);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDePacotesEstendido.
    /// </summary>
    public partial interface IRankingDePacotesEstendidoFactory :IBaseFactory {  
    
		RankingDePacotesEstendido Create();    
		RankingDePacotesEstendido Create(int id, int idPessoaEstabelecimento, int idPessoaCliente, int idTransacao, System.DateTime dataVenda, Perlink.Trinks.Pacotes.Pacote pacote, int quantidade, decimal totalPago);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProdutos.
    /// </summary>
    public partial interface IRankingDeProdutosFactory :IBaseFactory {  
    
		RankingDeProdutos Create();    
		RankingDeProdutos Create(int idRankingProduto, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto produto, Perlink.Trinks.ProdutoEstoque.EstabelecimentoProdutoCategoria categoria, int quantidade, decimal valorComDesconto, decimal? valorUnitarioNoPacote, System.DateTime dataTransacaoFoiPaga, int? idPessoaQuePagou, string simboloUnidadeMedida, int medidasPorUnidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProdutosEstendido.
    /// </summary>
    public partial interface IRankingDeProdutosEstendidoFactory :IBaseFactory {  
    
		RankingDeProdutosEstendido Create();    
		RankingDeProdutosEstendido Create(int idRankingProduto, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto produto, Perlink.Trinks.ProdutoEstoque.EstabelecimentoProdutoCategoria categoria, int quantidade, decimal valorComDesconto, decimal? valorUnitarioNoPacote, System.DateTime dataTransacaoFoiPaga, int? idPessoaQuePagou, string simboloUnidadeMedida, int medidasPorUnidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProfissionais.
    /// </summary>
    public partial interface IRankingDeProfissionaisFactory :IBaseFactory {  
    
		RankingDeProfissionais Create();    
		RankingDeProfissionais Create(int idRankingProfissionais, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, int idEstabelecimento, decimal? valorServicoFinalSemPacote, decimal? valorServicoUnitarioPacote, int idServicoCategoriaEstabelecimento, System.DateTime dataTransacaoPaga, decimal? valorProdutoFinalSemPacote, decimal? valorProdutoPacote, decimal? valorTotal, int idPessoaPagou, int quantidadeFechamentos, int quantidadeDeServicosRealizados, int quantidadeDeProdutosEmUnidades);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProfissionaisEstendido.
    /// </summary>
    public partial interface IRankingDeProfissionaisEstendidoFactory :IBaseFactory {  
    
		RankingDeProfissionaisEstendido Create();    
		RankingDeProfissionaisEstendido Create(int idRankingProfissionais, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, int idEstabelecimento, decimal? valorServicoFinalSemPacote, decimal? valorServicoUnitarioPacote, int idServicoCategoriaEstabelecimento, System.DateTime dataTransacaoPaga, decimal? valorProdutoFinalSemPacote, decimal? valorProdutoPacote, decimal? valorTotal, int idPessoaPagou, int quantidadeFechamentos, int quantidadeDeServicosRealizados, int quantidadeDeProdutosEmUnidades);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeServicos.
    /// </summary>
    public partial interface IRankingDeServicosFactory :IBaseFactory {  
    
		RankingDeServicos Create();    
		RankingDeServicos Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int idTransacao, Perlink.Trinks.Pessoas.ServicoCategoriaEstabelecimento categoria, Perlink.Trinks.Pessoas.ServicoEstabelecimento servico, Perlink.Trinks.Pessoas.Profissional profissional, Perlink.Trinks.Pessoas.Cliente cliente, int? idPacoteCliente, System.DateTime data, decimal valorTotal, int qtdServicosRealizados);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeServicosEstendido.
    /// </summary>
    public partial interface IRankingDeServicosEstendidoFactory :IBaseFactory {  
    
		RankingDeServicosEstendido Create();    
		RankingDeServicosEstendido Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, int idTransacao, Perlink.Trinks.Pessoas.ServicoCategoriaEstabelecimento categoria, Perlink.Trinks.Pessoas.ServicoEstabelecimento servico, Perlink.Trinks.Pessoas.Profissional profissional, Perlink.Trinks.Pessoas.Cliente cliente, int? idPacoteCliente, System.DateTime data, decimal valorTotal, int qtdServicosRealizados);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingEstendidoEstabelecimentos.
    /// </summary>
    public partial interface IRankingEstendidoEstabelecimentosFactory :IBaseFactory {  
    
		RankingEstendidoEstabelecimentos Create();    
		RankingEstendidoEstabelecimentos Create(int id, int idEstabelecimento, int? idFranquia, int periodoPermitido);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingItensDePacotes.
    /// </summary>
    public partial interface IRankingItensDePacotesFactory :IBaseFactory {  
    
		RankingItensDePacotes Create();    
		RankingItensDePacotes Create(int id, int idPessoaEstabelecimento, int idPessoaCliente, int idTransacao, System.DateTime dataVenda, int idPacote, int quantidade, decimal valorTotal, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Pessoas.ServicoCategoriaEstabelecimento servicoCategoriaEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.ProdutoEstoque.EstabelecimentoProdutoCategoria estabelecimentoProdutoCategoria);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RankingItensDePacotesEstendido.
    /// </summary>
    public partial interface IRankingItensDePacotesEstendidoFactory :IBaseFactory {  
    
		RankingItensDePacotesEstendido Create();    
		RankingItensDePacotesEstendido Create(int id, int idPessoaEstabelecimento, int idPessoaCliente, int idTransacao, System.DateTime dataVenda, int idPacote, int quantidade, decimal valorTotal, Perlink.Trinks.Pessoas.ServicoEstabelecimento servicoEstabelecimento, Perlink.Trinks.Pessoas.ServicoCategoriaEstabelecimento servicoCategoriaEstabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.ProdutoEstoque.EstabelecimentoProdutoCategoria estabelecimentoProdutoCategoria);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioDemonstrativoDeResultado.
    /// </summary>
    public partial interface IRelatorioDemonstrativoDeResultadoFactory :IBaseFactory {  
    
		RelatorioDemonstrativoDeResultado Create();    
		RelatorioDemonstrativoDeResultado Create(int idRelatorioDemonstrativoDeResultado, int idLancamentoCategoria, int idEstabelecimento, int idLancamentoGrupo, decimal valor, int ano, int mes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioDemonstrativoDeResultadoReceita.
    /// </summary>
    public partial interface IRelatorioDemonstrativoDeResultadoReceitaFactory :IBaseFactory {  
    
		RelatorioDemonstrativoDeResultadoReceita Create();    
		RelatorioDemonstrativoDeResultadoReceita Create(int idRelatorioDemonstrativoDeResultadoReceita, int idEstabelecimento, Perlink.Trinks.Relatorios.Enums.TipoReceitaEnum tipoReceita, decimal valor, int ano, int mes);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TelaRelatorio.
    /// </summary>
    public partial interface ITelaRelatorioFactory :IBaseFactory {  
    
		TelaRelatorio Create();    
		TelaRelatorio Create(int id, string titulo, string descricao, Perlink.Trinks.Relatorios.TelaRelatorioCategoria categoria, string url, bool ativo, string chavePermissao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TelaRelatorioCategoria.
    /// </summary>
    public partial interface ITelaRelatorioCategoriaFactory :IBaseFactory {  
    
		TelaRelatorioCategoria Create();    
		TelaRelatorioCategoria Create(int id, string nome, string icone, System.Collections.Generic.IList<Perlink.Trinks.Relatorios.TelaRelatorio> relatorios);
			 
    }
 
}
namespace Perlink.Trinks.Relatorios.DTO.Factories {

 
}
namespace Perlink.Trinks.Relatorios.DTO.DemonstrativoResultado.Factories {

 
}
namespace Perlink.Trinks.Relatorios.DTO.RetornoDeClientes.Factories {

 
}
namespace Perlink.Trinks.Relatorios.Enums.Factories {

 
}
namespace Perlink.Trinks.Relatorios.Filtros.Factories {

 
}
namespace Perlink.Trinks.Relatorios.MapaDeCalor.Factories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Factories {

    /// <summary>
    /// Interface para repositório da entidade ColocacaoDoProfissional.
    /// </summary>
    public partial interface IColocacaoDoProfissionalFactory :IBaseFactory {  
    
		ColocacaoDoProfissional Create();    
		ColocacaoDoProfissional Create(int id, int idEstabelecimento, System.DateTime? dataReferenciaDaFila, int idProfissional, System.DateTime? dataHoraUltimaColocacao, int? idPessoaQueColocouNaFila, System.DateTime? dataHoraUltimaEntrada, int? idPessoaQueRetirouDaFila, System.DateTime? dataHoraUltimaSaida, int? idServicoCategoriaEstabelecimento, bool emAtendimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioNoRodizio.
    /// </summary>
    public partial interface IHorarioNoRodizioFactory :IBaseFactory {  
    
		HorarioNoRodizio Create();    
		HorarioNoRodizio Create(int id, int idHorario, Perlink.Trinks.RodizioDeProfissionais.MovimentacaoNoRodizio movimentacaoNoRodizio, int idProfissional, int idPessoaDoCliente, int idServicoEstabelecimento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoRodizio.
    /// </summary>
    public partial interface IMovimentacaoNoRodizioFactory :IBaseFactory {  
    
		MovimentacaoNoRodizio Create();    
		MovimentacaoNoRodizio Create(int id, int idEstabelecimento, System.DateTime dataReferenciaDaFila, int idProfissional, Perlink.Trinks.RodizioDeProfissionais.ColocacaoDoProfissional colocacaoDoProfissional, Perlink.Trinks.RodizioDeProfissionais.Enums.TipoDeMovimentacaoNoRodizio tipoDeMovimentacao, System.DateTime dataHoraMovimentacao, System.DateTime? dataHoraColocacaoAnterior, System.DateTime? dataHoraColocacaoAtual, int? posicaoAnterior, int? posicaoAtual, System.Collections.Generic.IList<Perlink.Trinks.RodizioDeProfissionais.HorarioNoRodizio> horariosNaMovimentacao, int? idPessoaQueOriginouMovimentacao);
			 
    }
 
}
namespace Perlink.Trinks.RodizioDeProfissionais.DTO.Factories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Enums.Factories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Factories.Factories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Stories.Factories {

 
}
namespace Perlink.Trinks.RPS.Factories {

    /// <summary>
    /// Interface para repositório da entidade CobRpsEmissao.
    /// </summary>
    public partial interface ICobRpsEmissaoFactory :IBaseFactory {  
    
		CobRpsEmissao Create();    
		CobRpsEmissao Create(int id, System.DateTime dataEmissao, Perlink.Trinks.RPS.CobRpsLote cobRpsLote, Perlink.Trinks.Cobranca.Fatura fatura, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueRealizouOperacao, Perlink.Trinks.RPS.Enums.TipoCobRpsEmissao tipoCobRpsEmissao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CobRpsLote.
    /// </summary>
    public partial interface ICobRpsLoteFactory :IBaseFactory {  
    
		CobRpsLote Create();    
		CobRpsLote Create(int id, int numeroLote, string nomeArquivoLote);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoPadraoNFS.
    /// </summary>
    public partial interface IConfiguracaoPadraoNFSFactory :IBaseFactory {  
    
		ConfiguracaoPadraoNFS Create();    
		ConfiguracaoPadraoNFS Create(string identificador, string tipoRegimeEspecialTributacao, string utilizacaoIncentivoCultural, string optanteSimplesNacional, string tipoTributacaoServico, bool pilotoAtivo, bool enderecoClienteObrigatorio, bool cPFClienteObrigatorio, bool ambienteHomologacao, Perlink.Trinks.RPS.Enums.IntegracaoNFSeEnum integracaoNFSe, bool ativo, bool rpsSequencial, string chaveParaCancelamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DadosRPSTransacao.
    /// </summary>
    public partial interface IDadosRPSTransacaoFactory :IBaseFactory {  
    
		DadosRPSTransacao Create();    
		DadosRPSTransacao Create(int id, System.Collections.Generic.IList<Perlink.Trinks.RPS.EmissaoRPS> emissoesRPS, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, Perlink.Trinks.RPS.StatusRpsEnum statusRPS, decimal valorTotal, bool emissaoManual, System.DateTime? emissaoManualData, Perlink.Trinks.Pessoas.PessoaFisica emissaoManualPessoa, System.DateTime? dataCancelamento, int? loteCancelamento, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueCancelou, int? numeroNotaFiscalACancelar);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EmissaoRPS.
    /// </summary>
    public partial interface IEmissaoRPSFactory :IBaseFactory {  
    
		EmissaoRPS Create();    
		EmissaoRPS Create(int id, Perlink.Trinks.Financeiro.Transacao transacao, Perlink.Trinks.RPS.DadosRPSTransacao dadosRPSTransacao, Perlink.Trinks.Pessoas.PessoaJuridica pessoaJuridica, System.DateTime dataEmissao, int lote, int numero, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueEmitiu, bool? ehNotaUnificada, string linkPDF, string linkDocPrefeitura, string numeroNfse, string motivoRejeicao, int quantidadeReenvioRejeicao, string chaveAcesso);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade LoteRPS.
    /// </summary>
    public partial interface ILoteRPSFactory :IBaseFactory {  
    
		LoteRPS Create();    
		LoteRPS Create(int id, int numeroLote, Perlink.Trinks.Pessoas.PessoaJuridica pessoaEmitente, string nomeArquivoLote, string protocolo, bool processado, System.DateTime? ultimaConsulta, int quantidadeDeConsultas, string descricaoErro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MunicipioPadraoNfse.
    /// </summary>
    public partial interface IMunicipioPadraoNfseFactory :IBaseFactory {  
    
		MunicipioPadraoNfse Create();    
		MunicipioPadraoNfse Create(int id, string codigoIBGE, string identificador);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade NfseConfiguracaoMunicipio.
    /// </summary>
    public partial interface INfseConfiguracaoMunicipioFactory :IBaseFactory {  
    
		NfseConfiguracaoMunicipio Create();    
		NfseConfiguracaoMunicipio Create(int id, Perlink.Trinks.Pessoas.Cidade cidade, string textoProcon, bool habilitaItemDeducaoNaoTributavel, bool emitirTagDeducao, bool emitirTagBaseDeCalculo, bool habilitaCfDf, bool habilitaPadraoNacionalMeiProfissionais, bool habilitaPadraoNacionalEstabelecimentos, bool habilitarDeducaoDuasCasasDecimais, bool habilitarBasedeCalculoDuasCasasDecimais, bool arredondarBaseDeCalculoAntesCalcularIss, bool habilitarItemvUnitDuasCasasDecimais, bool habilitarEnvioTagItemVlrLiquido, bool habilitaNotaUnificada, bool habilitarFormatoNumeracaoIsaneto, bool utilizarCnpjPrestadorNoTomador, bool utilizarEmailPrestadorNoTomador, bool naoEnviarDadosTomadorSemCpf, bool habilitarTagItemSeqComecarUm, bool habilitarDeducaoPorServicoPadraoIPM, int? maxQtdcaracteresDiscriminacao, bool habilitarTruncarValissDuasCasasDecimais);
			 
    }
 
}
namespace Perlink.Trinks.RPS.DTO.Factories {

 
}
namespace Perlink.Trinks.RPS.Enums.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.ABRASF.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.BHISS.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.DSF.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.EGOVERNE.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.GINFES.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.Ginfes_SJRP.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.GINFES3.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.LONDRINA_SIGCORP_SIGISS.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.NFCe.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.PAULISTANA.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SALVADOR_BA.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SIGCORP_SIGISS.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SIMPLISS.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.VVISS.Factories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.WEBISS.Factories {

 
}
namespace Perlink.Trinks.RPS.Integracao.Factories {

 
}
namespace Perlink.Trinks.RPS.Integracao.Schemas.Factories {

 
}
namespace Perlink.Trinks.RPS.ManipuladoresDeStreamParaRPS.Factories {

 
}
namespace Perlink.Trinks.Seguranca.Factories {

    /// <summary>
    /// Interface para repositório da entidade AcoesProibidasMvc.
    /// </summary>
    public partial interface IAcoesProibidasMvcFactory :IBaseFactory {  
    
		AcoesProibidasMvc Create();    
		AcoesProibidasMvc Create(int id, string caminho, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ApiClient.
    /// </summary>
    public partial interface IApiClientFactory :IBaseFactory {  
    
		ApiClient Create();    
		ApiClient Create(int id, string name, string clientId, string secret, Perlink.Trinks.Seguranca.ApiClientApplicationType applicationType, bool active, int? tokenLifetime, int refreshTokenLifetime, string allowedOrigin);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ApiRefreshToken.
    /// </summary>
    public partial interface IApiRefreshTokenFactory :IBaseFactory {  
    
		ApiRefreshToken Create();    
		ApiRefreshToken Create(int id, string token, string subject, string clientId, System.DateTime issuedUtc, System.DateTime expiresUtc, string protectedTicket);
			 
    }
 
}
namespace Perlink.Trinks.Seguranca.DTO.Factories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Factories {

    /// <summary>
    /// Interface para repositório da entidade CancelamentoDoPedido.
    /// </summary>
    public partial interface ICancelamentoDoPedidoFactory :IBaseFactory {  
    
		CancelamentoDoPedido Create();    
		CancelamentoDoPedido Create(int idDoCancelamento, Perlink.Trinks.SugestoesEPedidosDeCompra.PedidoDeCompra pedido, string motivoDoCancelamento, System.DateTime dataDoCancelamento, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueCancelou);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoMovimentacaoEstoquePedido.
    /// </summary>
    public partial interface IEstabelecimentoMovimentacaoEstoquePedidoFactory :IBaseFactory {  
    
		EstabelecimentoMovimentacaoEstoquePedido Create();    
		EstabelecimentoMovimentacaoEstoquePedido Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.Pessoas.EstabelecimentoMovimentacaoEstoque estabelecimentoMovimentacaoEstoque, Perlink.Trinks.SugestoesEPedidosDeCompra.PedidoDeCompra pedidoDeCompra, Perlink.Trinks.SugestoesEPedidosDeCompra.ItemDePedidoDeCompra itemDePedidoDeCompra);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade InformacaoAlterada.
    /// </summary>
    public partial interface IInformacaoAlteradaFactory :IBaseFactory {  
    
		InformacaoAlterada Create();    
		InformacaoAlterada Create(int id, Perlink.Trinks.SugestoesEPedidosDeCompra.RegistroDeAlteracao registroDeAlteracao, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.SugestoesEPedidosDeCompra.PedidoDeCompra pedido, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueAlterou, string qualInformacao, string valorAntigo, string valorNovo, System.DateTime dataHoraAlteracao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemDePedidoDeCompra.
    /// </summary>
    public partial interface IItemDePedidoDeCompraFactory :IBaseFactory {  
    
		ItemDePedidoDeCompra Create();    
		ItemDePedidoDeCompra Create(int id, bool ativo, Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.SugestoesEPedidosDeCompra.PedidoDeCompra pedido, int quantidadePedida, int? quantidadeSugerida, decimal valorDeCompraUnitario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PedidoDeCompra.
    /// </summary>
    public partial interface IPedidoDeCompraFactory :IBaseFactory {  
    
		PedidoDeCompra Create();    
		PedidoDeCompra Create(int id, int numeroDoPedido, int? identificadorDoEstabelecimentoParaPedidosNaFranquia, Perlink.Trinks.Pessoas.EstabelecimentoFabricanteProduto estabelecimentoFabricanteProduto, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Pessoas.Franquia franquia, Perlink.Trinks.SugestoesEPedidosDeCompra.Enums.StatusPedidoDeCompraEnum status, int quantidadeDeItensNoPedido, System.Collections.Generic.IList<Perlink.Trinks.SugestoesEPedidosDeCompra.ItemDePedidoDeCompra> itensPedidos, bool ativo, decimal valorTotal, System.DateTime dataCadastro, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQuemCriou, System.DateTime? dataEnvio, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQuemEnviou, System.DateTime? dataEfetivacao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQuemEfetivou, System.DateTime? dataFaturamento, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQuemFaturou, System.DateTime? dataRecebimento, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQuemRecebeu, System.DateTime dataHoraUltimaAlteracao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaUltimaAlteracao, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueFezAUltimaAlteracaoDeStatus, System.DateTime dataHoraUltimaAlteracaoDeStatus, Perlink.Trinks.SugestoesEPedidosDeCompra.CancelamentoDoPedido cancelamento);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade RegistroDeAlteracao.
    /// </summary>
    public partial interface IRegistroDeAlteracaoFactory :IBaseFactory {  
    
		RegistroDeAlteracao Create();    
		RegistroDeAlteracao Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.SugestoesEPedidosDeCompra.PedidoDeCompra pedido, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaQueAlterou, System.Collections.Generic.IList<Perlink.Trinks.SugestoesEPedidosDeCompra.InformacaoAlterada> informacoesAlteradas, Perlink.Trinks.SugestoesEPedidosDeCompra.EventoDeOrigemDaAlteracaoEnum eventoDeOrigemDaAlteracao, System.DateTime dataHoraAlteracao, Perlink.Trinks.SugestoesEPedidosDeCompra.Enums.StatusPedidoDeCompraEnum statusNaHoraDaAlteracao);
			 
    }
 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.DTO.Factories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Factories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Filtros.Factories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Enums.Factories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Stories.Factories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Factories {

    /// <summary>
    /// Interface para repositório da entidade SurveyAppPro.
    /// </summary>
    public partial interface ISurveyAppProFactory :IBaseFactory {  
    
		SurveyAppPro Create();    
		SurveyAppPro Create(int id, Perlink.Trinks.Pessoas.Pessoa pessoa, Perlink.Trinks.SurveyAppB2B.Enums.FeedbackEnum feedbackEnum, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.SurveyAppB2B.Enums.FluxoDeTelaQueOriginouOFeedbackEnum fluxoDeTelaQueOriginouOFeedbackEnum, string comentario, string numeroDaVersao);
			 
    }
 
}
namespace Perlink.Trinks.TesteAB.Factories {

    /// <summary>
    /// Interface para repositório da entidade Amostra.
    /// </summary>
    public partial interface IAmostraFactory :IBaseFactory {  
    
		Amostra Create();    
		Amostra Create(int id, Perlink.Trinks.TesteAB.Grupo grupo, int? idObjetoReferencia, int? idEstabelecimento, int? idConta, System.DateTime dataHoraRegistro);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Grupo.
    /// </summary>
    public partial interface IGrupoFactory :IBaseFactory {  
    
		Grupo Create();    
		Grupo Create(int id, Perlink.Trinks.TesteAB.Enums.ExperimentoEnum experimento, string nome, bool ativo, string variante);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Metrica.
    /// </summary>
    public partial interface IMetricaFactory :IBaseFactory {  
    
		Metrica Create();    
		Metrica Create(int id, int idAmostra, string acao, int tempoDecorrido);
			 
    }
 
}
namespace Perlink.Trinks.TesteAB.DTO.Factories {

 
}
namespace Perlink.Trinks.TesteAB.Enums.Factories {

 
}
namespace Perlink.Trinks.TestesAB.Enums.Factories {

 
}
namespace Perlink.Trinks.TestesAB.Factories {

    /// <summary>
    /// Interface para repositório da entidade TesteABAssinatura.
    /// </summary>
    public partial interface ITesteABAssinaturaFactory :IBaseFactory {  
    
		TesteABAssinatura Create();    
		TesteABAssinatura Create(int idTesteABAssinatura, Perlink.Trinks.TestesAB.TesteAB testeAB, System.Collections.Generic.IList<Perlink.Trinks.TestesAB.TesteABAssinaturaPlanoAssinatura> planos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABAssinaturaPlanoAssinatura.
    /// </summary>
    public partial interface ITesteABAssinaturaPlanoAssinaturaFactory :IBaseFactory {  
    
		TesteABAssinaturaPlanoAssinatura Create();    
		TesteABAssinaturaPlanoAssinatura Create(int idTesteABAssinaturaPlanoAssinatura, Perlink.Trinks.Cobranca.PlanoAssinatura plano, Perlink.Trinks.TestesAB.TesteABAssinatura testeABAssinatura, string servicosQueDevemEstarDisponiveisNaAssinatura);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABAssinaturaPlanoAssinaturaEstabelecimento.
    /// </summary>
    public partial interface ITesteABAssinaturaPlanoAssinaturaEstabelecimentoFactory :IBaseFactory {  
    
		TesteABAssinaturaPlanoAssinaturaEstabelecimento Create();    
		TesteABAssinaturaPlanoAssinaturaEstabelecimento Create(int idTesteABAssinaturaPlanoAssinaturaEstabelecimento, Perlink.Trinks.Cobranca.PlanoAssinatura plano, Perlink.Trinks.TestesAB.TesteABAssinaturaPlanoAssinatura testeABAssinaturaPlanoAssinatura, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, System.DateTime dataQueEntrouNoTeste);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABWhyTrinks.
    /// </summary>
    public partial interface ITesteABWhyTrinksFactory :IBaseFactory {  
    
		TesteABWhyTrinks Create();    
		TesteABWhyTrinks Create(int idTesteABWhyTrinks, string uRLDoTesteAB, Perlink.Trinks.TestesAB.TesteAB testeAB);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABWhyTrinksHistorico.
    /// </summary>
    public partial interface ITesteABWhyTrinksHistoricoFactory :IBaseFactory {  
    
		TesteABWhyTrinksHistorico Create();    
		TesteABWhyTrinksHistorico Create(int id, int idDaLpDoWhyTrinks, System.DateTime dataQueEntrouNoSite, string sessao);
			 
    }
 
}
namespace Perlink.Trinks.TrinksApps.Factories {

    /// <summary>
    /// Interface para repositório da entidade AplicativoDeAgendamento.
    /// </summary>
    public partial interface IAplicativoDeAgendamentoFactory :IBaseFactory {  
    
		AplicativoDeAgendamento Create();    
		AplicativoDeAgendamento Create(int id, int? idFranquia, int? idEstabelecimento, string nome, string nomeArquivoBundle, string firebaseProjectId, string firebaseClientEmail, string firebaseKey, Perlink.Trinks.TrinksApps.ObjectValues.ConfiguracaoDePushiOS configuracaoDePushiOS, Perlink.Trinks.TrinksApps.ObjectValues.ConfiguracaoDePushAndroid configuracaoDePushAndroid, System.Collections.Generic.IList<Perlink.Trinks.TrinksApps.AplicativoDeAgendamentoFuncionalidades> funcionalidades, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AplicativoDeAgendamentoFuncionalidades.
    /// </summary>
    public partial interface IAplicativoDeAgendamentoFuncionalidadesFactory :IBaseFactory {  
    
		AplicativoDeAgendamentoFuncionalidades Create();    
		AplicativoDeAgendamentoFuncionalidades Create(int id, Perlink.Trinks.TrinksApps.AplicativoDeAgendamento aplicativo, Perlink.Trinks.TrinksApps.FuncionalidadeDoAplicativoDeAgendamento funcionalidade, string versao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracoesAppProfissional.
    /// </summary>
    public partial interface IConfiguracoesAppProfissionalFactory :IBaseFactory {  
    
		ConfiguracoesAppProfissional Create();    
		ConfiguracoesAppProfissional Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.TrinksApps.Enums.TermoUtilizadoNaCarteiraAppProfissionalEnum termoUtilizadoNaCarteira);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DadosOnboardingOQueProcuraNoTrinks.
    /// </summary>
    public partial interface IDadosOnboardingOQueProcuraNoTrinksFactory :IBaseFactory {  
    
		DadosOnboardingOQueProcuraNoTrinks Create();    
		DadosOnboardingOQueProcuraNoTrinks Create(int id, int idConta, int idEstabelecimento, string oQueProcuraNoTrinks, System.DateTime data);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade DispositivoComAplicativo.
    /// </summary>
    public partial interface IDispositivoComAplicativoFactory :IBaseFactory {  
    
		DispositivoComAplicativo Create();    
		DispositivoComAplicativo Create(int id, int idConta, int idPessoaDaConta, string tokenDispositivo, string uUID, System.DateTime dataHoraRegistro, System.DateTime? dataHoraUltimaAtualizacao, Perlink.Trinks.Notificacoes.Enums.TipoPlataformaEnum plataforma, string versaoPlataforma, string versaoDoAplicativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FuncionalidadeDoAplicativoDeAgendamento.
    /// </summary>
    public partial interface IFuncionalidadeDoAplicativoDeAgendamentoFactory :IBaseFactory {  
    
		FuncionalidadeDoAplicativoDeAgendamento Create();    
		FuncionalidadeDoAplicativoDeAgendamento Create(int id, string identificador, string descricao, System.Collections.Generic.IList<Perlink.Trinks.TrinksApps.AplicativoDeAgendamentoFuncionalidades> aplicativos);
			 
    }
 
}
namespace Perlink.Trinks.TrinksApps.Dto.Factories {

 
}
namespace Perlink.Trinks.TrinksApps.Enums.Factories {

 
}
namespace Perlink.Trinks.TrinksApps.ObjectValues.Factories {

 
}
namespace Perlink.Trinks.TrinksAtendimento.Factories {

    /// <summary>
    /// Interface para repositório da entidade AssuntoFaleConoscoTrinksProfissional.
    /// </summary>
    public partial interface IAssuntoFaleConoscoTrinksProfissionalFactory :IBaseFactory {  
    
		AssuntoFaleConoscoTrinksProfissional Create();    
		AssuntoFaleConoscoTrinksProfissional Create(int id, string nome, int idTipoEventoAssuntoSelecionado, int idTipoEventoAssuntoEnviado, int ordemExibicao, string tituloWebView, string urlWebview, int? idRecurso);
			 
    }
 
}
namespace Perlink.Trinks.TrinksAtendimento.DTOs.Factories {

 
}
namespace Perlink.Trinks.TrinksAtendimento.Stories.Factories {

 
}
namespace Perlink.Trinks.TrinksPay.Stories.Factories {

 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.DTO.Factories {

 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.Factories {

    /// <summary>
    /// Interface para repositório da entidade EmailVerificado.
    /// </summary>
    public partial interface IEmailVerificadoFactory :IBaseFactory {  
    
		EmailVerificado Create();    
		EmailVerificado Create(int id, Perlink.Trinks.ValidacaoDeIdentidade.VerificacaoDeIdentidadeEnvio verificacaoDeIdentidadeEnvio, Perlink.Trinks.ValidacaoDeIdentidade.VerificacaoDeIdentidade verificacaoDeIdentidade, int idConta, string email, System.DateTime dataHoraVerificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TelefoneVerificado.
    /// </summary>
    public partial interface ITelefoneVerificadoFactory :IBaseFactory {  
    
		TelefoneVerificado Create();    
		TelefoneVerificado Create(int id, Perlink.Trinks.ValidacaoDeIdentidade.VerificacaoDeIdentidadeEnvio verificacaoDeIdentidadeEnvio, Perlink.Trinks.ValidacaoDeIdentidade.VerificacaoDeIdentidade verificacaoDeIdentidade, int idTelefone, int idConta, string telefone, System.DateTime dataHoraVerificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VerificacaoDeContaPelaAreaPerlink.
    /// </summary>
    public partial interface IVerificacaoDeContaPelaAreaPerlinkFactory :IBaseFactory {  
    
		VerificacaoDeContaPelaAreaPerlink Create();    
		VerificacaoDeContaPelaAreaPerlink Create(int id, int idContaVerificada, int idPessoaFisicaQueVerificou, System.DateTime dataHoraVerificacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VerificacaoDeIdentidade.
    /// </summary>
    public partial interface IVerificacaoDeIdentidadeFactory :IBaseFactory {  
    
		VerificacaoDeIdentidade Create();    
		VerificacaoDeIdentidade Create(int id, int? idConta, string contato, Perlink.Trinks.ValidacaoDeIdentidade.Enums.TipoDeContatoEnum tipoContato, bool podePular, bool podeTrocarContato, Perlink.Trinks.ValidacaoDeIdentidade.Enums.MotivoSolicitacaoEnum motivoSolicitacao, int? idObjetoQueOriginouVerificacao, Perlink.Trinks.ValidacaoDeIdentidade.Enums.TipoObjetoQueOriginouEnum tipoObjetoQueOriginouVerificacao, bool verificado, string contatoVerificado, System.DateTime? dataHoraConfirmacao, System.Collections.Generic.IList<Perlink.Trinks.ValidacaoDeIdentidade.VerificacaoDeIdentidadeEnvio> envios);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade VerificacaoDeIdentidadeEnvio.
    /// </summary>
    public partial interface IVerificacaoDeIdentidadeEnvioFactory :IBaseFactory {  
    
		VerificacaoDeIdentidadeEnvio Create();    
		VerificacaoDeIdentidadeEnvio Create(int id, Perlink.Trinks.ValidacaoDeIdentidade.VerificacaoDeIdentidade verificacaoDeIdentidade, string contato, System.DateTime dataHoraEnvio, string codigoConfirmacao, System.DateTime dataHoraExpiracao, System.DateTime? dataHoraConfirmacao, bool ativo);
			 
    }
 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.Enums.Factories {

 
}
namespace Perlink.Trinks.Vendas.Factories {

    /// <summary>
    /// Interface para repositório da entidade Comanda.
    /// </summary>
    public partial interface IComandaFactory :IBaseFactory {  
    
		Comanda Create();    
		Comanda Create(System.DateTime data, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, bool fechada, int id, int numero, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaComprador);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVenda.
    /// </summary>
    public partial interface IItemVendaFactory :IBaseFactory {  
    
		ItemVenda Create();    
		ItemVenda Create(bool ativo, Perlink.Trinks.Financeiro.Comissao comissao, decimal desconto, int id, Perlink.Trinks.Pacotes.ItemPacoteCliente itemPacoteCliente, Perlink.Trinks.Financeiro.MotivoDesconto motivoDesconto, int quantidade, Perlink.Trinks.Pessoas.TipoDesconto tipoDesconto, decimal valorUnitario, Perlink.Trinks.Vendas.Venda venda, decimal subTotal, int pontosDeFidelidade, bool usouPontosDeFidelidade, Perlink.Trinks.Pessoas.PessoaFisica pessoaComissionada);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaAssinaturaCliente.
    /// </summary>
    public partial interface IItemVendaAssinaturaClienteFactory :IBaseFactory {  
    
		ItemVendaAssinaturaCliente Create();    
 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaPacote.
    /// </summary>
    public partial interface IItemVendaPacoteFactory :IBaseFactory {  
    
		ItemVendaPacote Create();    
		ItemVendaPacote Create(Perlink.Trinks.Pacotes.PacoteCliente pacoteCliente, int idTemporarioParaTelaItemVendaPacote);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaProduto.
    /// </summary>
    public partial interface IItemVendaProdutoFactory :IBaseFactory {  
    
		ItemVendaProduto Create();    
		ItemVendaProduto Create(Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.Vendas.PreVenda preVenda, Perlink.Trinks.Pessoas.Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade, Perlink.Trinks.Pessoas.PessoaFisica pessoaQueUtilizou, Perlink.Trinks.Pessoas.ClienteEstabelecimento clienteEstabelecimentoQueUsou);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaValePresente.
    /// </summary>
    public partial interface IItemVendaValePresenteFactory :IBaseFactory {  
    
		ItemVendaValePresente Create();    
		ItemVendaValePresente Create(Perlink.Trinks.Vendas.ValePresente valePresente);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreVenda.
    /// </summary>
    public partial interface IPreVendaFactory :IBaseFactory {  
    
		PreVenda Create();    
		PreVenda Create(Perlink.Trinks.Vendas.Comanda comanda, System.DateTime dataCadastro, decimal desconto, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, Perlink.Trinks.Financeiro.MotivoDesconto estabelecimentoMotivoDesconto, Perlink.Trinks.Pessoas.EstabelecimentoProfissional estabelecimentoProfissional, int id, Perlink.Trinks.Pessoas.PessoaFisica pessoaFisicaComprador, decimal preco, Perlink.Trinks.Vendas.PreVendaStatus preVendaStatus, int quantidade, decimal subtotal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaProduto.
    /// </summary>
    public partial interface IPreVendaProdutoFactory :IBaseFactory {  
    
		PreVendaProduto Create();    
		PreVendaProduto Create(Perlink.Trinks.Pessoas.EstabelecimentoProduto estabelecimentoProduto, Perlink.Trinks.Pessoas.Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaServico.
    /// </summary>
    public partial interface IPreVendaServicoFactory :IBaseFactory {  
    
		PreVendaServico Create();    
		PreVendaServico Create(Perlink.Trinks.Pessoas.Horario horario, int idHorario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaHistorico.
    /// </summary>
    public partial interface IPreVendaHistoricoFactory :IBaseFactory {  
    
		PreVendaHistorico Create();    
		PreVendaHistorico Create(int idPreVendaHistorico, System.DateTime dataAlteracao, Perlink.Trinks.Vendas.PreVenda preVenda, Perlink.Trinks.Pessoas.PessoaFisica pessoaAlteracao, string mensagem);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaStatus.
    /// </summary>
    public partial interface IPreVendaStatusFactory :IBaseFactory {  
    
		PreVendaStatus Create();    
		PreVendaStatus Create(int idStatusPreVenda, string nome);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValePresente.
    /// </summary>
    public partial interface IValePresenteFactory :IBaseFactory {  
    
		ValePresente Create();    
		ValePresente Create(int id, Perlink.Trinks.Vendas.ItemVendaValePresente itemVendaValePresente, string numero, Perlink.Trinks.Pessoas.PessoaJuridica pessoaEstabelecimento, decimal saldoAtual, decimal saldoInicial, System.Collections.Generic.IList<Perlink.Trinks.Financeiro.TransacaoFormaPagamento> transacoesFormaPagamento, System.DateTime? validade);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade Venda.
    /// </summary>
    public partial interface IVendaFactory :IBaseFactory {  
    
		Venda Create();    
		Venda Create(int id, System.DateTime data, Perlink.Trinks.Financeiro.Transacao transacao, System.Collections.Generic.IList<Perlink.Trinks.Vendas.ItemVenda> itensVenda);
			 
    }
 
}
namespace Perlink.Trinks.Vendas.DTO.Factories {

 
}
namespace Perlink.Trinks.Vendas.Enums.Factories {

 
}
namespace Perlink.Trinks.Vendas.Factories.Factories {

 
}
namespace Perlink.Trinks.Vendas.Repositories.Filtros.Factories {

 
}
namespace Perlink.Trinks.VO.Factories {

 
}
namespace Perlink.Trinks.WhatsApp.Factories {

    /// <summary>
    /// Interface para repositório da entidade AllowedTestEstablishments.
    /// </summary>
    public partial interface IAllowedTestEstablishmentsFactory :IBaseFactory {  
    
		AllowedTestEstablishments Create();    
		AllowedTestEstablishments Create(int id, int idEstabelecimento, System.DateTime createdAt);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade AvaliacaoHistorico.
    /// </summary>
    public partial interface IAvaliacaoHistoricoFactory :IBaseFactory {  
    
		AvaliacaoHistorico Create();    
		AvaliacaoHistorico Create(int id, string numero, Perlink.Trinks.WhatsApp.HistoricoHorarioTag historicoHorarioTag, int idHorario, Perlink.Trinks.Pessoas.Horario horario, int? resposta, System.DateTime? dataUltimaAtualizacao, System.DateTime dataCriacao, string comentario);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CartaoEstabelecimento.
    /// </summary>
    public partial interface ICartaoEstabelecimentoFactory :IBaseFactory {  
    
		CartaoEstabelecimento Create();    
		CartaoEstabelecimento Create(int id, string numeroCartao, string nomeCompleto, int mesVencimento, int anoVencimento, int idEstabelecimento, Perlink.Trinks.Cobranca.FormaPagamento formaPagamento, string cpfPortadorCartao, string idCardPagamento, bool ativo, string idComprador);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CompraCredito.
    /// </summary>
    public partial interface ICompraCreditoFactory :IBaseFactory {  
    
		CompraCredito Create();    
		CompraCredito Create(int id, Perlink.Trinks.Pessoas.Estabelecimento estabelecimento, bool ativo, Perlink.Trinks.Cobranca.Fatura fatura, Perlink.Trinks.WhatsApp.PacoteCredito pacoteCredito, decimal valor, bool recompraAutomatica);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CompraRecorrenteEstabelecimento.
    /// </summary>
    public partial interface ICompraRecorrenteEstabelecimentoFactory :IBaseFactory {  
    
		CompraRecorrenteEstabelecimento Create();    
		CompraRecorrenteEstabelecimento Create(int id, int idEstabelecimento, Perlink.Trinks.WhatsApp.Enums.TipoSaldo tipo, int idPacoteCredito);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade CompraRecorrenteEstabelecimentoHistorico.
    /// </summary>
    public partial interface ICompraRecorrenteEstabelecimentoHistoricoFactory :IBaseFactory {  
    
		CompraRecorrenteEstabelecimentoHistorico Create();    
		CompraRecorrenteEstabelecimentoHistorico Create(int id, int idEstabelecimento, bool ativacao, Perlink.Trinks.WhatsApp.Enums.TipoSaldo tipo, int? idPacoteCredito, int? idPessoaAlterou, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade EstablishmentConfiguration.
    /// </summary>
    public partial interface IEstablishmentConfigurationFactory :IBaseFactory {  
    
		EstablishmentConfiguration Create();    
		EstablishmentConfiguration Create(int id, int idEstabelecimento, bool isSchedulingConfirmationEnabled, bool independentFlowSending, Perlink.Trinks.WhatsApp.Enums.RatingTypeEnum ratingType, Perlink.Trinks.WhatsApp.Enums.SendConfirmationTypeEnum sendConfirmationType, bool ratingEnabled, bool reminderEnabled, bool confirmationEnabled);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaValor.
    /// </summary>
    public partial interface IFranquiaValorFactory :IBaseFactory {  
    
		FranquiaValor Create();    
		FranquiaValor Create(int id, int idFranquia, bool ativo, int idTipoValorPacote);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoCompraAdicional.
    /// </summary>
    public partial interface IHistoricoCompraAdicionalFactory :IBaseFactory {  
    
		HistoricoCompraAdicional Create();    
		HistoricoCompraAdicional Create(int id, int idEstabelecimento, int idFatura, System.DateTime dataEmissao, string formaDePagamento, string numeroCartao, int status, int saldo, decimal valor);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoComunicacaoHorario.
    /// </summary>
    public partial interface IHistoricoComunicacaoHorarioFactory :IBaseFactory {  
    
		HistoricoComunicacaoHorario Create();    
		HistoricoComunicacaoHorario Create(int id, int idHorario, Perlink.Trinks.WhatsApp.Enums.FluxoDeComunicacaoWhatsApp tipoComunicacao, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoHorarioTag.
    /// </summary>
    public partial interface IHistoricoHorarioTagFactory :IBaseFactory {  
    
		HistoricoHorarioTag Create();    
		HistoricoHorarioTag Create(int id, int idHorario, Perlink.Trinks.WhatsApp.HorarioTag horarioTag, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoSessao.
    /// </summary>
    public partial interface IHistoricoSessaoFactory :IBaseFactory {  
    
		HistoricoSessao Create();    
		HistoricoSessao Create(int id, string numero, System.DateTime dataCriacao, Perlink.Trinks.WhatsApp.Enums.OrigemHistoricoSessao origem, int? idEstabelecimentoCliente, System.Collections.Generic.IList<Perlink.Trinks.WhatsApp.HistoricoSessaoProcesso> sessaoProcessos);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoSessaoProcesso.
    /// </summary>
    public partial interface IHistoricoSessaoProcessoFactory :IBaseFactory {  
    
		HistoricoSessaoProcesso Create();    
		HistoricoSessaoProcesso Create(int id, System.DateTime dataCriacao, int idHistoricoSessao, int? idEstabelecimentoCliente, Perlink.Trinks.WhatsApp.Enums.TipoSaldo tipo, Perlink.Trinks.WhatsApp.Enums.FluxoDeComunicacaoWhatsApp fluxoAtual, int passoAtual, string dadosOpcionais, Perlink.Trinks.WhatsApp.SessionOptionalData dadosOpcionaisObjeto, System.Collections.Generic.IList<Perlink.Trinks.WhatsApp.HistoricoSessaoProcessoRegistro> sessaoProcessoRegistros);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoSessaoProcessoRegistro.
    /// </summary>
    public partial interface IHistoricoSessaoProcessoRegistroFactory :IBaseFactory {  
    
		HistoricoSessaoProcessoRegistro Create();    
		HistoricoSessaoProcessoRegistro Create(int id, System.DateTime dataCriacao, int idHistoricoSessaoProcesso, Perlink.Trinks.WhatsApp.Enums.FluxoDeComunicacaoWhatsApp fluxoAtual, int passoAtual, System.DateTime? dataResposta, string dadosOpcionais, Perlink.Trinks.WhatsApp.SessionOptionalData dadosOpcionaisObjeto);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioComunicacao.
    /// </summary>
    public partial interface IHorarioComunicacaoFactory :IBaseFactory {  
    
		HorarioComunicacao Create();    
		HorarioComunicacao Create(int id, int idHorario, int idEstabelecimento, int idEstabelecimentoCliente, System.DateTime dataCriacao, System.DateTime dataInicio, bool aptoEnvio);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioTag.
    /// </summary>
    public partial interface IHorarioTagFactory :IBaseFactory {  
    
		HorarioTag Create();    
		HorarioTag Create(int id, string nome, string descricao, string icone, string cor, bool ativo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MessageTemplate.
    /// </summary>
    public partial interface IMessageTemplateFactory :IBaseFactory {  
    
		MessageTemplate Create();    
		MessageTemplate Create(int id, string name, string content, string twilioTemplateSid);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoSaldo.
    /// </summary>
    public partial interface IMovimentacaoSaldoFactory :IBaseFactory {  
    
		MovimentacaoSaldo Create();    
		MovimentacaoSaldo Create(int id, Perlink.Trinks.WhatsApp.SaldoEstabelecimento saldoEstabelecimento, Perlink.Trinks.WhatsApp.Enums.TipoMovimentoSaldo tipo, int quantidade, Perlink.Trinks.WhatsApp.Enums.OrigemMovimentoSaldo origem, System.DateTime dataCriacao);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OptOut.
    /// </summary>
    public partial interface IOptOutFactory :IBaseFactory {  
    
		OptOut Create();    
		OptOut Create(int id, string phone, int? idEstabelecimento, int? idFranquia, System.DateTime createdAt);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade OptOutHistorico.
    /// </summary>
    public partial interface IOptOutHistoricoFactory :IBaseFactory {  
    
		OptOutHistorico Create();    
		OptOutHistorico Create(int id, string phone, int? idEstabelecimento, int? idFranquia, System.DateTime createdAt);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteCredito.
    /// </summary>
    public partial interface IPacoteCreditoFactory :IBaseFactory {  
    
		PacoteCredito Create();    
		PacoteCredito Create(int id, int quantidade, bool ativo, Perlink.Trinks.WhatsApp.Enums.TipoSaldo tipo);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteCreditoFormaContratacao.
    /// </summary>
    public partial interface IPacoteCreditoFormaContratacaoFactory :IBaseFactory {  
    
		PacoteCreditoFormaContratacao Create();    
		PacoteCreditoFormaContratacao Create(int id, Perlink.Trinks.Cobranca.FormaDeContratacaoDoAdicional formaContratacao, Perlink.Trinks.WhatsApp.PacoteCredito pacoteCredito);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade PacotePersonalizado.
    /// </summary>
    public partial interface IPacotePersonalizadoFactory :IBaseFactory {  
    
		PacotePersonalizado Create();    
		PacotePersonalizado Create(int id, int idFranquia, int idEstabelecimento, Perlink.Trinks.WhatsApp.Enums.TipoSaldo tipo, bool ativo, int idTipoValorPacote);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade SaldoEstabelecimento.
    /// </summary>
    public partial interface ISaldoEstabelecimentoFactory :IBaseFactory {  
    
		SaldoEstabelecimento Create();    
		SaldoEstabelecimento Create(int id, int idEstabelecimento, int limiteDeEnvios, int quantidadeDeEnviadosNoPeriodo, System.DateTime? dataUltimaAtualizacaoRecorrenteLimiteDeEnvios, System.DateTime? dataUltimaAtualizacaoLimiteDeEnvios, Perlink.Trinks.WhatsApp.Enums.TipoSaldo tipo, bool realizouAlgumaAutomacao, System.Collections.Generic.IList<Perlink.Trinks.WhatsApp.MovimentacaoSaldo> listaWhatsAppMovimentacaoSaldo, int limiteDeEnviosCortesia, int quantidadeDeEnviosCortesiaNoPeriodo, int saldoPadrao, int saldoCortesia, int saldoTotal);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade TipoValorPacote.
    /// </summary>
    public partial interface ITipoValorPacoteFactory :IBaseFactory {  
    
		TipoValorPacote Create();    
		TipoValorPacote Create(int id, string nome);
			 
    }
    /// <summary>
    /// Interface para repositório da entidade ValorPacote.
    /// </summary>
    public partial interface IValorPacoteFactory :IBaseFactory {  
    
		ValorPacote Create();    
		ValorPacote Create(int id, int idPacote, decimal valor, int idTipoValorPacote);
			 
    }
 
}
namespace Perlink.Trinks.WhatsApp.DTO.Factories {

 
}
namespace Perlink.Trinks.WhatsApp.Enums.Factories {

 
}
namespace Perlink.Trinks.WhatsApp.Exceptions.Factories {

 
}
namespace Perlink.Trinks.WhatsApp.Factories.Factories {

 
}
namespace Perlink.Trinks.WhatsApp.Filters.Factories {

 
}
namespace Perlink.Trinks.WhatsApp.Strategies.Factories {

 
}
namespace Perlink.Trinks.WhyTrinks.Enums.Factories {

 
}
namespace Perlink.Trinks.WhyTrinks.Factories {

 
}
namespace Perlink.Trinks.Wrapper.Factories {

 
}
