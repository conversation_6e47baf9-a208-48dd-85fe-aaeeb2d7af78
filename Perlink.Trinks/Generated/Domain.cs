﻿// <autogenerated>
//   This file was generated by Perlink DDD Generator using Domain.tt.
//   Any changes made manually will be lost next time the file is regenerated.
// </autogenerated>

using Perlink.Trinks.LGPD.Helpers;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks {

	public static partial class Domain {		
		public static partial class Autoatendimento {		
			public static Perlink.Trinks.Autoatendimento.Repositories.ICheckInEstablishmentsRepository CheckInEstablishmentsRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Autoatendimento.Repositories.ICheckInEstablishmentsRepository>();}
			}
			public static Perlink.Trinks.Autoatendimento.Repositories.IConfigurationsEstablishmentRepository ConfigurationsEstablishmentRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Autoatendimento.Repositories.IConfigurationsEstablishmentRepository>();}
			}
			public static Perlink.Trinks.Autoatendimento.Repositories.IStatusServiceCustomerEstablishmentRepository StatusServiceCustomerEstablishmentRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Autoatendimento.Repositories.IStatusServiceCustomerEstablishmentRepository>();}
			}
		}
		public static partial class Autoatendimento {		
		public static partial class DTO {		
		}
		}
		public static partial class Autoatendimento {		
		public static partial class Enums {		
		}
		}
		public static partial class BaseIBPT {		
			public static Perlink.Trinks.BaseIBPT.Repositories.IDadosIBPTRepository DadosIBPTRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.BaseIBPT.Repositories.IDadosIBPTRepository>();}
			}
		}
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Repositories.IBandeiraCartaoMDRRepository BandeiraCartaoMDRRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.IBandeiraCartaoMDRRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.ICredenciamentoRepository CredenciamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.ICredenciamentoRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.ICredenciamentoComStoneCodeRepository CredenciamentoComStoneCodeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.ICredenciamentoComStoneCodeRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.IEstabelecimentoTerminalPosRepository EstabelecimentoTerminalPosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.IEstabelecimentoTerminalPosRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.IHierarquiaRepository HierarquiaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.IHierarquiaRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.IMccRepository MccRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.IMccRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.ITaxaAntecipacaoRepository TaxaAntecipacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.ITaxaAntecipacaoRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.ITaxaMDRRepository TaxaMDRRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.ITaxaMDRRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.ITpvMensalRepository TpvMensalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.ITpvMensalRepository>();}
			}
			public static Perlink.Trinks.Belezinha.Repositories.ITransacaoAvulsaPOSWebhookRequestRepository TransacaoAvulsaPOSWebhookRequestRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Belezinha.Repositories.ITransacaoAvulsaPOSWebhookRequestRepository>();}
			}
		}
		public static partial class Belezinha {		
		public static partial class DTO {		
		public static partial class AgendaDeRecebiveis {		
		}
		}
		}
		public static partial class Belezinha {		
		}
		public static partial class Belezinha {		
		public static partial class Enums {		
		}
		}
		public static partial class Belezinha {		
		public static partial class Filters {		
		}
		}
		public static partial class Belezinha {		
		public static partial class Helpers {		
		}
		}
		public static partial class Belezinha {		
		public static partial class Strategies {		
		}
		}
		public static partial class Belezinha {		
		public static partial class Strategies {		
		public static partial class Pagarme {		
		}
		}
		}
		public static partial class Caching {		
		}
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Repositories.IBonusTransacaoRepository BonusTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.IBonusTransacaoRepository>();}
			}
			public static Perlink.Trinks.Cashback.Repositories.ICashbackComissaoRepository CashbackComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.ICashbackComissaoRepository>();}
			}
			public static Perlink.Trinks.Cashback.Repositories.ICashbackComissaoValorAReceberRepository CashbackComissaoValorAReceberRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.ICashbackComissaoValorAReceberRepository>();}
			}
			public static Perlink.Trinks.Cashback.Repositories.ICashbackHorarioTransacaoRepository CashbackHorarioTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.ICashbackHorarioTransacaoRepository>();}
			}
			public static Perlink.Trinks.Cashback.Repositories.ICashbackItemVendaRepository CashbackItemVendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.ICashbackItemVendaRepository>();}
			}
			public static Perlink.Trinks.Cashback.Repositories.ICashbackTransacaoRepository CashbackTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.ICashbackTransacaoRepository>();}
			}
			public static Perlink.Trinks.Cashback.Repositories.IEstabelecimentoDadosIntegracaoRepository EstabelecimentoDadosIntegracaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cashback.Repositories.IEstabelecimentoDadosIntegracaoRepository>();}
			}
		}
		public static partial class Cashback {		
		public static partial class DTO {		
		public static partial class CrmBonus {		
		}
		}
		}
		public static partial class Cashback {		
		public static partial class DTO {		
		}
		}
		public static partial class ClientesAcompanhamentos {		
			public static Perlink.Trinks.ClientesAcompanhamentos.Repositories.IAnotacaoRepository AnotacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClientesAcompanhamentos.Repositories.IAnotacaoRepository>();}
			}
		}
		public static partial class ClientesAcompanhamentos {		
		public static partial class DTO {		
		}
		}
		public static partial class ClientesAcompanhamentos {		
		public static partial class Stories {		
		}
		}
		public static partial class ClientesAnexos {		
			public static Perlink.Trinks.ClientesAnexos.Repositories.IClienteAnexoRepository ClienteAnexoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClientesAnexos.Repositories.IClienteAnexoRepository>();}
			}
			public static Perlink.Trinks.ClientesAnexos.Repositories.IMeuAnexoRepository MeuAnexoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClientesAnexos.Repositories.IMeuAnexoRepository>();}
			}
		}
		public static partial class ClientesAnexos {		
		public static partial class DTO {		
		}
		}
		public static partial class ClientesAnexos {		
		public static partial class Enums {		
		}
		}
		public static partial class ClientesAnexos {		
		public static partial class ObjetosDeValor {		
		}
		}
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IAssinaturaDoClienteRepository AssinaturaDoClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IAssinaturaDoClienteRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioRepository BeneficioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioDaAssinaturaRepository BeneficioDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioDoPlanoRepository BeneficioDoPlanoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioDoPlanoRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioProdutoRepository BeneficioProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioProdutoRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioServicoRepository BeneficioServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioServicoRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioUsadoRepository BeneficioUsadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IBeneficioUsadoRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IContratoDeAdesaoRepository ContratoDeAdesaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IContratoDeAdesaoRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IHistoricoDeStatusAssinaturaDoClubeRepository HistoricoDeStatusAssinaturaDoClubeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IHistoricoDeStatusAssinaturaDoClubeRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IIntencaoEdicaoDoPlanoClienteRepository IntencaoEdicaoDoPlanoClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IIntencaoEdicaoDoPlanoClienteRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.ILinkDePagamentoDaAssinaturaRepository LinkDePagamentoDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.ILinkDePagamentoDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.ILinkDePagamentoDoCancelamentoDaAssinaturaRepository LinkDePagamentoDoCancelamentoDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.ILinkDePagamentoDoCancelamentoDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IPagamentoDeAssinaturaRepository PagamentoDeAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IPagamentoDeAssinaturaRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IPagamentoMultaDeCancelamentoDaAssinaturaRepository PagamentoMultaDeCancelamentoDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IPagamentoMultaDeCancelamentoDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IPlanoClienteRepository PlanoClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IPlanoClienteRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IVendaOnlineRepository VendaOnlineRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IVendaOnlineRepository>();}
			}
			public static Perlink.Trinks.ClubeDeAssinaturas.Repositories.IVigenciaDeAssinaturaRepository VigenciaDeAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ClubeDeAssinaturas.Repositories.IVigenciaDeAssinaturaRepository>();}
			}
		}
		public static partial class ClubeDeAssinaturas {		
		public static partial class Calculos {		
		}
		}
		public static partial class ClubeDeAssinaturas {		
		public static partial class DTO {		
		}
		}
		public static partial class ClubeDeAssinaturas {		
		public static partial class Enums {		
		}
		}
		public static partial class ClubeDeAssinaturas {		
		public static partial class Factories {		
		}
		}
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Repositories.IAdicionalCobradoRepository AdicionalCobradoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IAdicionalCobradoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IAdicionalNaAssinaturaRepository AdicionalNaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IAdicionalNaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IAgendamentoDeMigracaoDoPlanoRepository AgendamentoDeMigracaoDoPlanoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IAgendamentoDeMigracaoDoPlanoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IAssinaturaRepository AssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IBeneficioDoPlanoAssinaturaRepository BeneficioDoPlanoAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IBeneficioDoPlanoAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IBeneficioDoPlanoMeuPlanoRepository BeneficioDoPlanoMeuPlanoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IBeneficioDoPlanoMeuPlanoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IContaFinanceiraRepository ContaFinanceiraRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IContaFinanceiraRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IDadosSalesRepository DadosSalesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IDadosSalesRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IDescontoNaAssinaturaRepository DescontoNaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IDescontoNaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IDescontoNoAdicionalDaAssinaturaRepository DescontoNoAdicionalDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IDescontoNoAdicionalDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IDescontoNoPlanoDaAssinaturaRepository DescontoNoPlanoDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IDescontoNoPlanoDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IDorDoClienteRepository DorDoClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IDorDoClienteRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IDorDoClienteNaAssinaturaRepository DorDoClienteNaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IDorDoClienteNaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IEstabelecimentoParceriasTrinksRepository EstabelecimentoParceriasTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IEstabelecimentoParceriasTrinksRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IExperimentacaoEstabelecimentoRepository ExperimentacaoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IExperimentacaoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IFaturaRepository FaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IFaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IFaturaMarketingRepository FaturaMarketingRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IFaturaMarketingRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IFaturaTrinksRepository FaturaTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IFaturaTrinksRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IFaturaWhatsAppRepository FaturaWhatsAppRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IFaturaWhatsAppRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IFormaDeContratacaoDoAdicionalRepository FormaDeContratacaoDoAdicionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IFormaDeContratacaoDoAdicionalRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IFormaPagamentoRepository FormaPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IFormaPagamentoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IHistoricoDoAdicionalNaAssinaturaRepository HistoricoDoAdicionalNaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IHistoricoDoAdicionalNaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IMotivoDoCancelamentoRepository MotivoDoCancelamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IMotivoDoCancelamentoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IMotivoDoCancelamentoDaAssinaturaRepository MotivoDoCancelamentoDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IMotivoDoCancelamentoDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IObservacaoAssinaturaRepository ObservacaoAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IObservacaoAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IOfertaDeServicoAdicionalRepository OfertaDeServicoAdicionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IOfertaDeServicoAdicionalRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IOfertaDeServicoAdicionalMeuPlanoRepository OfertaDeServicoAdicionalMeuPlanoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IOfertaDeServicoAdicionalMeuPlanoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeRepository OfertaDeServicoAdicionalMeuPlanoDisponibilidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IParceriaTipoTrinksRepository ParceriaTipoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IParceriaTipoTrinksRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IParceriaTrinksRepository ParceriaTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IParceriaTrinksRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPessoaJuridicaPagamentoExternoRepository PessoaJuridicaPagamentoExternoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPessoaJuridicaPagamentoExternoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPessoaJuridicaPagamentoExternoFaturaRepository PessoaJuridicaPagamentoExternoFaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPessoaJuridicaPagamentoExternoFaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPessoaJuridicaPagamentoExternoFaturaHistoricoRepository PessoaJuridicaPagamentoExternoFaturaHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPessoaJuridicaPagamentoExternoFaturaHistoricoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPlanoAssinaturaRepository PlanoAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPlanoAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPromocaoNaAssinaturaRepository PromocaoNaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPromocaoNaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPromocaoPraContaFinanceiraRepository PromocaoPraContaFinanceiraRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPromocaoPraContaFinanceiraRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IPromocaoTrinksRepository PromocaoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IPromocaoTrinksRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IRelatorioAssinaturaRepository RelatorioAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IRelatorioAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IRelatorioFaturamentoRepository RelatorioFaturamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IRelatorioFaturamentoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IResponsavelAtendimentoRepository ResponsavelAtendimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IResponsavelAtendimentoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IServicoTrinksRepository ServicoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IServicoTrinksRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.ISolicitacaoCancelamentoDaAssinaturaRepository SolicitacaoCancelamentoDaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.ISolicitacaoCancelamentoDaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IStatusContaRepository StatusContaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IStatusContaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IStatusFaturaRepository StatusFaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IStatusFaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.ITipoAssociacaoRepository TipoAssociacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.ITipoAssociacaoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.ITipoFormaPagamentoRepository TipoFormaPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.ITipoFormaPagamentoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.ITipoServicoRepository TipoServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.ITipoServicoRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IValorDeAdesaoDoAdicionalPorFaixaRepository ValorDeAdesaoDoAdicionalPorFaixaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IValorDeAdesaoDoAdicionalPorFaixaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaRepository ValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IValorDoAdicionalPorFaixaRepository ValorDoAdicionalPorFaixaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IValorDoAdicionalPorFaixaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IValorDoAdicionalPorFaixaDaOfertaRepository ValorDoAdicionalPorFaixaDaOfertaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IValorDoAdicionalPorFaixaDaOfertaRepository>();}
			}
			public static Perlink.Trinks.Cobranca.Repositories.IValorPorFaixaRepository ValorPorFaixaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cobranca.Repositories.IValorPorFaixaRepository>();}
			}
		}
		public static partial class Cobranca {		
		public static partial class ConfiguracoesDosAdicionais {		
		}
		}
		public static partial class Cobranca {		
		public static partial class ContratacaoDosAdicionais {		
		}
		}
		public static partial class Cobranca {		
		public static partial class DTO {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Enums {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Exceptions {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Extensions {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Factories {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Filtros {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Helpers {		
		}
		}
		public static partial class Cobranca {		
		public static partial class ObjectValues {		
		}
		}
		public static partial class Cobranca {		
		public static partial class Stories {		
		}
		}
		public static partial class ComissaoAppB2B {		
		public static partial class DTO {		
		}
		}
		public static partial class ComissaoAppB2B {		
		public static partial class Enum {		
		}
		}
		public static partial class ComissaoAppB2B {		
		public static partial class Stories {		
		}
		}
		public static partial class Compromissos {		
		public static partial class DTO {		
		}
		}
		public static partial class Compromissos {		
		public static partial class Filtros {		
		}
		}
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Repositories.IArtigoDeNovidadeRepository ArtigoDeNovidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ComunidadeTrinks.Repositories.IArtigoDeNovidadeRepository>();}
			}
			public static Perlink.Trinks.ComunidadeTrinks.Repositories.ISugestaoRepository SugestaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ComunidadeTrinks.Repositories.ISugestaoRepository>();}
			}
			public static Perlink.Trinks.ComunidadeTrinks.Repositories.ITopicoDeVotacaoRepository TopicoDeVotacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ComunidadeTrinks.Repositories.ITopicoDeVotacaoRepository>();}
			}
			public static Perlink.Trinks.ComunidadeTrinks.Repositories.IVotacaoDeSugestaoRepository VotacaoDeSugestaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ComunidadeTrinks.Repositories.IVotacaoDeSugestaoRepository>();}
			}
			public static Perlink.Trinks.ComunidadeTrinks.Repositories.IVotoRepository VotoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ComunidadeTrinks.Repositories.IVotoRepository>();}
			}
		}
		public static partial class ComunidadeTrinks {		
		public static partial class DTO {		
		}
		}
		public static partial class ConciliacaoBancaria {		
			public static Perlink.Trinks.ConciliacaoBancaria.Repositories.IContaFinanceiraDoEstabelecimentoRepository ContaFinanceiraDoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ConciliacaoBancaria.Repositories.IContaFinanceiraDoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.ConciliacaoBancaria.Repositories.IContaFinanceiraPadraoRepository ContaFinanceiraPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ConciliacaoBancaria.Repositories.IContaFinanceiraPadraoRepository>();}
			}
		}
		public static partial class ConciliacaoBancaria {		
		public static partial class DTO {		
		}
		}
		public static partial class ConciliacaoBancaria {		
		public static partial class Enums {		
		}
		}
		public static partial class ConciliacaoBancaria {		
		public static partial class Exportadores {		
		}
		}
		public static partial class ConciliacaoBancaria {		
		public static partial class Filtros {		
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Dtos {		
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Dtos {		
		public static partial class V2 {		
		}
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Interfaces {		
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Strategies {		
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Strategies {		
		public static partial class PagarMe {		
		}
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Strategies {		
		public static partial class Siclos {		
		}
		}
		}
		public static partial class ConciliacaoBelezinha {		
		public static partial class Strategies {		
		public static partial class Stone {		
		}
		}
		}
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoContaDigitalRepository AutenticacaoContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoContaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoContaDigitalConfirmacaoRepository AutenticacaoContaDigitalConfirmacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoContaDigitalConfirmacaoRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoContaDigitalEnvioRepository AutenticacaoContaDigitalEnvioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoContaDigitalEnvioRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoIdentidadePreCadastroRepository AutenticacaoIdentidadePreCadastroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoIdentidadePreCadastroRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoIdentidadeUsuarioContaRepository AutenticacaoIdentidadeUsuarioContaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IAutenticacaoIdentidadeUsuarioContaRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.ICategoriaPermissaoContaDigitalRepository CategoriaPermissaoContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.ICategoriaPermissaoContaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IChavePixRepository ChavePixRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IChavePixRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IChavePixContaDigitalRepository ChavePixContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IChavePixContaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IChavePixProfissionalRepository ChavePixProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IChavePixProfissionalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IConfiguracoesContaDigitalRepository ConfiguracoesContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IConfiguracoesContaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IContaBancariaDigitalRepository ContaBancariaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IContaBancariaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IContaEstabelecimentoRepository ContaEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IContaEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IContaUsuarioDigitalRepository ContaUsuarioDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IContaUsuarioDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IDonoRepository DonoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IDonoRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IDuvidaFrequenteRepository DuvidaFrequenteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IDuvidaFrequenteRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IEtapaCadastroRepository EtapaCadastroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IEtapaCadastroRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.ILimiteContaDigitalRepository LimiteContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.ILimiteContaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.ILimitePlanoRepository LimitePlanoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.ILimitePlanoRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.ILOGAprovacaoTransferenciasRepository LOGAprovacaoTransferenciasRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.ILOGAprovacaoTransferenciasRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IOperadorRepository OperadorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IOperadorRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IPagamentoAgendadoRepository PagamentoAgendadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IPagamentoAgendadoRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IPagamentoAgendadoFolhaMesProfissionalRepository PagamentoAgendadoFolhaMesProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IPagamentoAgendadoFolhaMesProfissionalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IPermissaoContaDigitalRepository PermissaoContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IPermissaoContaDigitalRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IPermissaoOperadorRepository PermissaoOperadorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IPermissaoOperadorRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IResponsavelRepository ResponsavelRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IResponsavelRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.ITransferenciaRepository TransferenciaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.ITransferenciaRepository>();}
			}
			public static Perlink.Trinks.ContaDigital.Repositories.IUsuarioContaDigitalRepository UsuarioContaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ContaDigital.Repositories.IUsuarioContaDigitalRepository>();}
			}
		}
		public static partial class ContaDigital {		
		public static partial class Builders {		
		}
		}
		public static partial class ContaDigital {		
		}
		public static partial class ContaDigital {		
		public static partial class DTO {		
		public static partial class LogDTO {		
		}
		}
		}
		public static partial class ContaDigital {		
		public static partial class Enums {		
		}
		}
		public static partial class ContaDigital {		
		public static partial class Providers {		
		}
		}
		public static partial class ContaDigital {		
		public static partial class VO {		
		}
		}
		public static partial class Conteudo {		
			public static Perlink.Trinks.Conteudo.Repositories.IConteudoImagemLogoEstabelecimentoRepository ConteudoImagemLogoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Conteudo.Repositories.IConteudoImagemLogoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Conteudo.Repositories.IConteudoTextoRepository ConteudoTextoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Conteudo.Repositories.IConteudoTextoRepository>();}
			}
			public static Perlink.Trinks.Conteudo.Repositories.IMenuItemRepository MenuItemRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Conteudo.Repositories.IMenuItemRepository>();}
			}
			public static Perlink.Trinks.Conteudo.Repositories.IMenuOpcaoAliasBuscaRepository MenuOpcaoAliasBuscaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Conteudo.Repositories.IMenuOpcaoAliasBuscaRepository>();}
			}
			public static Perlink.Trinks.Conteudo.Repositories.IMenuOpcaoBuscaRepository MenuOpcaoBuscaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Conteudo.Repositories.IMenuOpcaoBuscaRepository>();}
			}
		}
		public static partial class Conteudo {		
		public static partial class DTO {		
		}
		}
		public static partial class Controle {		
			public static Perlink.Trinks.Controle.Repositories.IPalavraProibidaRepository PalavraProibidaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Controle.Repositories.IPalavraProibidaRepository>();}
			}
			public static Perlink.Trinks.Controle.Repositories.IPalavraProibidaSMSRepository PalavraProibidaSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Controle.Repositories.IPalavraProibidaSMSRepository>();}
			}
			public static Perlink.Trinks.Controle.Repositories.IPalavraProibidaTrinksRepository PalavraProibidaTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Controle.Repositories.IPalavraProibidaTrinksRepository>();}
			}
		}
		public static partial class ControleDeCTAs {		
			public static Perlink.Trinks.ControleDeCTAs.Repositories.ICTARepository CTARepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeCTAs.Repositories.ICTARepository>();}
			}
			public static Perlink.Trinks.ControleDeCTAs.Repositories.ICTAGrupoRepository CTAGrupoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeCTAs.Repositories.ICTAGrupoRepository>();}
			}
			public static Perlink.Trinks.ControleDeCTAs.Repositories.ICTAInformacoesAdicionaisTrinksProRepository CTAInformacoesAdicionaisTrinksProRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeCTAs.Repositories.ICTAInformacoesAdicionaisTrinksProRepository>();}
			}
		}
		public static partial class ControleDeCTAs {		
		public static partial class DTOs {		
		}
		}
		public static partial class ControleDeCTAs {		
		public static partial class Enums {		
		}
		}
		public static partial class ControleDeCTAs {		
		public static partial class Geradores {		
		}
		}
		public static partial class ControleDeEntradaESaida {		
		public static partial class DTO {		
		}
		}
		public static partial class ControleDeEntradaESaida {		
		public static partial class Enum {		
		}
		}
		public static partial class ControleDeEntradaESaida {		
		public static partial class Filtros {		
		}
		}
		public static partial class ControleDeEntradaESaida {		
			public static Perlink.Trinks.ControleDeEntradaESaida.Repositories.ILancamentoAporteRepository LancamentoAporteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeEntradaESaida.Repositories.ILancamentoAporteRepository>();}
			}
			public static Perlink.Trinks.ControleDeEntradaESaida.Repositories.ILancamentoDeReceitaCategoriaRepository LancamentoDeReceitaCategoriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeEntradaESaida.Repositories.ILancamentoDeReceitaCategoriaRepository>();}
			}
		}
		public static partial class ControleDeFotos {		
			public static Perlink.Trinks.ControleDeFotos.Repositories.IArquivoDeImagemRepository ArquivoDeImagemRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeFotos.Repositories.IArquivoDeImagemRepository>();}
			}
		}
		public static partial class ControleDeFotos {		
		public static partial class Controladores {		
		}
		}
		public static partial class ControleDeFotos {		
		public static partial class DTO {		
		}
		}
		public static partial class ControleDeFotos {		
		public static partial class Enums {		
		}
		}
		public static partial class ControleDeFotos {		
		public static partial class Factories {		
		}
		}
		public static partial class ControleDeFuncionalidades {		
			public static Perlink.Trinks.ControleDeFuncionalidades.Repositories.IDisponibilidadeEspecificaRepository DisponibilidadeEspecificaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeFuncionalidades.Repositories.IDisponibilidadeEspecificaRepository>();}
			}
			public static Perlink.Trinks.ControleDeFuncionalidades.Repositories.IDisponibilidadeGeralRepository DisponibilidadeGeralRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeFuncionalidades.Repositories.IDisponibilidadeGeralRepository>();}
			}
			public static Perlink.Trinks.ControleDeFuncionalidades.Repositories.IPreferenciasDaContaRepository PreferenciasDaContaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeFuncionalidades.Repositories.IPreferenciasDaContaRepository>();}
			}
			public static Perlink.Trinks.ControleDeFuncionalidades.Repositories.IValorDeConfiguracaoEspecificaRepository ValorDeConfiguracaoEspecificaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeFuncionalidades.Repositories.IValorDeConfiguracaoEspecificaRepository>();}
			}
			public static Perlink.Trinks.ControleDeFuncionalidades.Repositories.IValorDeConfiguracaoGeralRepository ValorDeConfiguracaoGeralRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeFuncionalidades.Repositories.IValorDeConfiguracaoGeralRepository>();}
			}
		}
		public static partial class ControleDeFuncionalidades {		
		public static partial class DTO {		
		}
		}
		public static partial class ControleDeFuncionalidades {		
		public static partial class Enums {		
		}
		}
		public static partial class ControleDeFuncionalidades {		
		public static partial class Filtros {		
		}
		}
		public static partial class ControleDeFuncionalidades {		
		public static partial class ObjetosDeValor {		
		}
		}
		public static partial class ControleDeFuncionalidades {		
		public static partial class Stories {		
		}
		}
		public static partial class ControleDeSatisfacao {		
			public static Perlink.Trinks.ControleDeSatisfacao.Repositories.IAvaliacaoDeSatisfacaoRepository AvaliacaoDeSatisfacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeSatisfacao.Repositories.IAvaliacaoDeSatisfacaoRepository>();}
			}
			public static Perlink.Trinks.ControleDeSatisfacao.Repositories.IAvaliacaoDeSatisfacaoRecebidaRetentativaRepository AvaliacaoDeSatisfacaoRecebidaRetentativaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeSatisfacao.Repositories.IAvaliacaoDeSatisfacaoRecebidaRetentativaRepository>();}
			}
			public static Perlink.Trinks.ControleDeSatisfacao.Repositories.IContatoRepository ContatoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeSatisfacao.Repositories.IContatoRepository>();}
			}
			public static Perlink.Trinks.ControleDeSatisfacao.Repositories.IContatoCelularRepository ContatoCelularRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeSatisfacao.Repositories.IContatoCelularRepository>();}
			}
			public static Perlink.Trinks.ControleDeSatisfacao.Repositories.IItemParaAvaliarRepository ItemParaAvaliarRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ControleDeSatisfacao.Repositories.IItemParaAvaliarRepository>();}
			}
		}
		public static partial class ControleDeSatisfacao {		
		public static partial class DTO {		
		}
		}
		public static partial class ControleDeSatisfacao {		
		public static partial class Enums {		
		}
		}
		public static partial class ControleDeSatisfacao {		
		public static partial class Factories {		
		}
		}
		public static partial class ControleDeSatisfacao {		
		public static partial class Filtros {		
		}
		}
		public static partial class ControleDeSatisfacao {		
		public static partial class Stories {		
		}
		}
		public static partial class Correios {		
			public static Perlink.Trinks.Correios.Repositories.IConsultaCepRepository ConsultaCepRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Correios.Repositories.IConsultaCepRepository>();}
			}
		}
		public static partial class Cupom {		
			public static Perlink.Trinks.Cupom.Repositories.ICupomBaseRepository CupomBaseRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomBaseRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomDescontoRepository CupomDescontoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomDescontoRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomEstabelecimentoRepository CupomEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomEstabelecimentoProdutoRepository CupomEstabelecimentoProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomEstabelecimentoProdutoRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomHorarioTransacaoRepository CupomHorarioTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomHorarioTransacaoRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomItemVendaRepository CupomItemVendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomItemVendaRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomPessoaFisicaRepository CupomPessoaFisicaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomPessoaFisicaRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomServicoEstabelecimentoRepository CupomServicoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomServicoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Cupom.Repositories.ICupomUsoPessoaFisicaRepository CupomUsoPessoaFisicaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Cupom.Repositories.ICupomUsoPessoaFisicaRepository>();}
			}
		}
		public static partial class Cupom {		
		public static partial class DTO {		
		}
		}
		public static partial class Cupom {		
		public static partial class Enums {		
		}
		}
		public static partial class Cupom {		
		public static partial class Filters {		
		}
		}
		public static partial class Cupom {		
		public static partial class Models {		
		}
		}
		public static partial class Dashboard {		
		public static partial class DTOs {		
		}
		}
		public static partial class Dashboard {		
		}
		public static partial class DataQuery {		
		public static partial class DTO {		
		}
		}
		public static partial class DataQuery {		
		}
		public static partial class DataQuery {		
		public static partial class Strategies {		
		}
		}
		public static partial class DebitoParcial {		
			public static Perlink.Trinks.DebitoParcial.Repositories.IAbatimentoDeDividaRepository AbatimentoDeDividaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.DebitoParcial.Repositories.IAbatimentoDeDividaRepository>();}
			}
			public static Perlink.Trinks.DebitoParcial.Repositories.IDividaDeixadaNoEstabelecimentoRepository DividaDeixadaNoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.DebitoParcial.Repositories.IDividaDeixadaNoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.DebitoParcial.Repositories.IHistoricoDaDividaRepository HistoricoDaDividaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.DebitoParcial.Repositories.IHistoricoDaDividaRepository>();}
			}
			public static Perlink.Trinks.DebitoParcial.Repositories.IPagamentoDeDividaPeloClienteRepository PagamentoDeDividaPeloClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.DebitoParcial.Repositories.IPagamentoDeDividaPeloClienteRepository>();}
			}
		}
		public static partial class DebitoParcial {		
		public static partial class DTO {		
		}
		}
		public static partial class DebitoParcial {		
		public static partial class Filtros {		
		}
		}
		public static partial class DebitoParcial {		
		public static partial class Stories {		
		}
		}
		public static partial class Despesas {		
			public static Perlink.Trinks.Despesas.Repositories.ILancamentosRecorrentesSelecionadasRepository LancamentosRecorrentesSelecionadasRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentosRecorrentesSelecionadasRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoRepository LancamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoCategoriaRepository LancamentoCategoriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoCategoriaRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoCategoriaPadraoRepository LancamentoCategoriaPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoCategoriaPadraoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoGeradoPorMovimentacaoEstoqueRepository LancamentoGeradoPorMovimentacaoEstoqueRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoGeradoPorMovimentacaoEstoqueRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoGrupoRepository LancamentoGrupoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoGrupoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoGrupoPadraoRepository LancamentoGrupoPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoGrupoPadraoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoRecorrenciaRepository LancamentoRecorrenciaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoRecorrenciaRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoRecorrenciaTipoRepository LancamentoRecorrenciaTipoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoRecorrenciaTipoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoStatusPagamentoRepository LancamentoStatusPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoStatusPagamentoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.ILancamentoTipoRepository LancamentoTipoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.ILancamentoTipoRepository>();}
			}
			public static Perlink.Trinks.Despesas.Repositories.IRenovacaoDeLancamentosRepository RenovacaoDeLancamentosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Despesas.Repositories.IRenovacaoDeLancamentosRepository>();}
			}
		}
		public static partial class Despesas {		
		public static partial class DTO {		
		}
		}
		public static partial class Despesas {		
		public static partial class Enums {		
		}
		}
		public static partial class Despesas {		
		public static partial class Factories {		
		}
		}
		public static partial class Disponibilidade {		
		public static partial class Adapters {		
		}
		}
		public static partial class Disponibilidade {		
		}
		public static partial class Disponibilidade {		
		public static partial class ExtensionMethods {		
		}
		}
		public static partial class Dispositivos {		
		public static partial class Enums {		
		}
		}
		public static partial class Dispositivos {		
			public static Perlink.Trinks.Dispositivos.Repositories.ITipoImpressaoRepository TipoImpressaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Dispositivos.Repositories.ITipoImpressaoRepository>();}
			}
		}
		public static partial class DTO {		
		}
		public static partial class DTO {		
			public static Perlink.Trinks.DTO.Repositories.IHorarioFuturosExportacaoRepository HorarioFuturosExportacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.DTO.Repositories.IHorarioFuturosExportacaoRepository>();}
			}
		}
		public static partial class DTO {		
		public static partial class PushSNS {		
		}
		}
		public static partial class Encurtador {		
		public static partial class DTO {		
		}
		}
		public static partial class Encurtador {		
			public static Perlink.Trinks.Encurtador.Repositories.IEncurtadorDeDadosRepository EncurtadorDeDadosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Encurtador.Repositories.IEncurtadorDeDadosRepository>();}
			}
		}
		public static partial class Encurtador {		
		public static partial class Enums {		
		}
		}
		public static partial class Encurtador {		
		public static partial class Factories {		
		}
		}
		public static partial class Enums {		
		}
		public static partial class Env {		
		public static partial class Clock {		
		}
		}
		public static partial class EnvioMensagem {		
		public static partial class Antifraude {		
		}
		}
		public static partial class EnvioMensagem {		
		public static partial class Enums {		
		}
		}
		public static partial class EnvioMensagem {		
		public static partial class Exceptions {		
		}
		}
		public static partial class EnvioMensagem {		
		}
		public static partial class EnvioMensagem {		
		public static partial class Strategies {		
		}
		}
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Repositories.IAvaliacaoEstabelecimentoRepository AvaliacaoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IAvaliacaoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IConfiguracaoDeAvaliacaoDeSatisfacaoRepository ConfiguracaoDeAvaliacaoDeSatisfacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IConfiguracaoDeAvaliacaoDeSatisfacaoRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IConfiguracaoEstabelecimentoRepository ConfiguracaoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IConfiguracaoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IEstabelecimentoFavoritoRepository EstabelecimentoFavoritoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IEstabelecimentoFavoritoRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IEstabelecimentoProfissionalFavoritoRepository EstabelecimentoProfissionalFavoritoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IEstabelecimentoProfissionalFavoritoRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IEstabelecimentoUUIDRepository EstabelecimentoUUIDRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IEstabelecimentoUUIDRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IItemConfiguradoParaSerAvaliadoRepository ItemConfiguradoParaSerAvaliadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IItemConfiguradoParaSerAvaliadoRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.Repositories.IServicoConfiguradoParaSerAvaliadoRepository ServicoConfiguradoParaSerAvaliadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.Repositories.IServicoConfiguradoParaSerAvaliadoRepository>();}
			}
		}
		public static partial class Estabelecimentos {		
		public static partial class DTO {		
		}
		}
		public static partial class Estabelecimentos {		
		public static partial class ElasticSearch {		
			public static Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories.IEstabelecimentoComInformacoesConsolidadasRepository EstabelecimentoComInformacoesConsolidadasRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories.IEstabelecimentoComInformacoesConsolidadasRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories.IInformacoesConsolidadasDaBuscaDoPortalRepository InformacoesConsolidadasDaBuscaDoPortalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories.IInformacoesConsolidadasDaBuscaDoPortalRepository>();}
			}
			public static Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories.IOpcaoDeAutocompletarDoPortalRepository OpcaoDeAutocompletarDoPortalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories.IOpcaoDeAutocompletarDoPortalRepository>();}
			}
		}
		}
		public static partial class Estabelecimentos {		
		public static partial class Enums {		
		}
		}
		public static partial class Estabelecimentos {		
		public static partial class ExtensionMethods {		
		}
		}
		public static partial class Estabelecimentos {		
		public static partial class Factories {		
		}
		}
		public static partial class Estabelecimentos {		
		public static partial class Filtros {		
		}
		}
		public static partial class Estabelecimentos {		
		public static partial class Interfaces {		
		}
		}
		public static partial class Estatistica {		
			public static Perlink.Trinks.Estatistica.Repositories.IBIUsoDoSistemaRepository BIUsoDoSistemaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Estatistica.Repositories.IBIUsoDoSistemaRepository>();}
			}
		}
		public static partial class EstilosVisuais {		
		public static partial class Enums {		
		}
		}
		public static partial class EstilosVisuais {		
			public static Perlink.Trinks.EstilosVisuais.Repositories.ITemaCssRepository TemaCssRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstilosVisuais.Repositories.ITemaCssRepository>();}
			}
			public static Perlink.Trinks.EstilosVisuais.Repositories.ITemaCssBackofficeRepository TemaCssBackofficeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstilosVisuais.Repositories.ITemaCssBackofficeRepository>();}
			}
		}
		public static partial class EstoqueComBaixaAutomatica {		
		public static partial class Calculos {		
		}
		}
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IConfiguracaoDoServicoRepository ConfiguracaoDoServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IConfiguracaoDoServicoRepository>();}
			}
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IConfiguracaoParaBaixaAutomaticaRepository ConfiguracaoParaBaixaAutomaticaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IConfiguracaoParaBaixaAutomaticaRepository>();}
			}
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioRepository HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioRepository>();}
			}
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IHistoricoDeUsoDeProdutoNoHorarioRepository HistoricoDeUsoDeProdutoNoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IHistoricoDeUsoDeProdutoNoHorarioRepository>();}
			}
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IItemConfiguradoParaBaixaAutomaticaRepository ItemConfiguradoParaBaixaAutomaticaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IItemConfiguradoParaBaixaAutomaticaRepository>();}
			}
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IUsoDeProdutoNoHorarioRepository UsoDeProdutoNoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories.IUsoDeProdutoNoHorarioRepository>();}
			}
		}
		public static partial class EstoqueComBaixaAutomatica {		
		public static partial class DTO {		
		}
		}
		public static partial class EstoqueComBaixaAutomatica {		
		public static partial class Enums {		
		}
		}
		public static partial class EstoqueComBaixaAutomatica {		
		public static partial class Stories {		
		}
		}
		public static partial class Exceptions {		
		}
		public static partial class ExtensionMethods {		
		}
		public static partial class Extratores {		
		public static partial class DTO {		
		}
		}
		public static partial class Extratores {		
			public static Perlink.Trinks.Extratores.Repositories.IExtratorRepository ExtratorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Extratores.Repositories.IExtratorRepository>();}
			}
			public static Perlink.Trinks.Extratores.Repositories.IVisaoRepository VisaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Extratores.Repositories.IVisaoRepository>();}
			}
		}
		public static partial class Facebook {		
		public static partial class DTOs {		
		}
		}
		public static partial class Facebook {		
		public static partial class Enums {		
		}
		}
		public static partial class Facebook {		
			public static Perlink.Trinks.Facebook.Repositories.IFBERepository FBERepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Facebook.Repositories.IFBERepository>();}
			}
		}
		public static partial class FAQ {		
			public static Perlink.Trinks.FAQ.Repositories.IAssuntoRepository AssuntoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.FAQ.Repositories.IAssuntoRepository>();}
			}
			public static Perlink.Trinks.FAQ.Repositories.IAssuntoPerguntaRespostaRepository AssuntoPerguntaRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.FAQ.Repositories.IAssuntoPerguntaRespostaRepository>();}
			}
			public static Perlink.Trinks.FAQ.Repositories.IPerguntaRespostaRepository PerguntaRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.FAQ.Repositories.IPerguntaRespostaRepository>();}
			}
			public static Perlink.Trinks.FAQ.Repositories.ITelaRepository TelaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.FAQ.Repositories.ITelaRepository>();}
			}
			public static Perlink.Trinks.FAQ.Repositories.ITelaAssuntoRepository TelaAssuntoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.FAQ.Repositories.ITelaAssuntoRepository>();}
			}
			public static Perlink.Trinks.FAQ.Repositories.ITelaPerguntaRespostaRepository TelaPerguntaRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.FAQ.Repositories.ITelaPerguntaRespostaRepository>();}
			}
		}
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Repositories.IAgendamentoOnlineQueGerouPontosRepository AgendamentoOnlineQueGerouPontosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IAgendamentoOnlineQueGerouPontosRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosRepository MovimentacaoDePontosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosAgendamentoOnlineRepository MovimentacaoDePontosAgendamentoOnlineRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosAgendamentoOnlineRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosAvulsoRepository MovimentacaoDePontosAvulsoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosAvulsoRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosHorarioTransacaoRepository MovimentacaoDePontosHorarioTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosHorarioTransacaoRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosItemVendaRepository MovimentacaoDePontosItemVendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosItemVendaRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosPagamentoAntecipadoRepository MovimentacaoDePontosPagamentoAntecipadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDePontosPagamentoAntecipadoRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDeTransferenciaDePontosRepository MovimentacaoDeTransferenciaDePontosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDeTransferenciaDePontosRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDeUmPontoGanhoRepository MovimentacaoDeUmPontoGanhoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IMovimentacaoDeUmPontoGanhoRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IPagamentoAntecipadoQueGerouPontosRepository PagamentoAntecipadoQueGerouPontosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IPagamentoAntecipadoQueGerouPontosRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IPontoGanhoRepository PontoGanhoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IPontoGanhoRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IProgramaDeFidelidadeRepository ProgramaDeFidelidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IProgramaDeFidelidadeRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.IProgramaDeFidelidadeDiaSemanaRepository ProgramaDeFidelidadeDiaSemanaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.IProgramaDeFidelidadeDiaSemanaRepository>();}
			}
			public static Perlink.Trinks.Fidelidade.Repositories.ITransferenciaDePontosRepository TransferenciaDePontosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fidelidade.Repositories.ITransferenciaDePontosRepository>();}
			}
		}
		public static partial class Fidelidade {		
		public static partial class DTO {		
		}
		}
		public static partial class Fidelidade {		
		public static partial class Enums {		
		}
		}
		public static partial class Fidelidade {		
		}
		public static partial class Fidelidade {		
		public static partial class Factories {		
		}
		}
		public static partial class Fidelidade {		
		public static partial class Filtros {		
		}
		}
		public static partial class Fidelidade {		
		public static partial class Strategies {		
		}
		}
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Repositories.IAberturaFechamentoCaixaRepository AberturaFechamentoCaixaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IAberturaFechamentoCaixaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IAberturaFechamentoCaixaHistoricoRepository AberturaFechamentoCaixaHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IAberturaFechamentoCaixaHistoricoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IComissaoRepository ComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IComissaoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IContaBancariaPessoaRepository ContaBancariaPessoaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IContaBancariaPessoaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFechamentoFolhaMesRepository FechamentoFolhaMesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFechamentoFolhaMesRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFechamentoFolhaMesProfissionalRepository FechamentoFolhaMesProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFechamentoFolhaMesProfissionalRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFormaPagamentoRepository FormaPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFormaPagamentoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFormaPagamentoTipoRepository FormaPagamentoTipoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFormaPagamentoTipoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IGorjetaRepository GorjetaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IGorjetaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ILancamentoDeAntecipacaoRepository LancamentoDeAntecipacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ILancamentoDeAntecipacaoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IMotivoDescontoRepository MotivoDescontoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IMotivoDescontoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IPagamentoFolhaMesProfissionalRepository PagamentoFolhaMesProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IPagamentoFolhaMesProfissionalRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ISangriaRepository SangriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ISangriaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ISangriaHistoricoRepository SangriaHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ISangriaHistoricoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITipoContaBancariaRepository TipoContaBancariaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITipoContaBancariaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITipoTransacaoRepository TipoTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITipoTransacaoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoRepository TransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoFormaPagamentoRepository TransacaoFormaPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoFormaPagamentoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoFormaPagamentoParcelaRepository TransacaoFormaPagamentoParcelaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoFormaPagamentoParcelaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoHistoricoRepository TransacaoHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoHistoricoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoItemRepository TransacaoItemRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoItemRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoLancamentoFinanceiroRepository TransacaoLancamentoFinanceiroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoLancamentoFinanceiroRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoPOSRepository TransacaoPOSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoPOSRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoPOSSplitRepository TransacaoPOSSplitRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoPOSSplitRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.ITransacaoPosWebhookRequestRepository TransacaoPosWebhookRequestRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.ITransacaoPosWebhookRequestRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IValorDeComissaoAReceberRepository ValorDeComissaoAReceberRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IValorDeComissaoAReceberRepository>();}
			}
		}
		public static partial class Financeiro {		
		public static partial class Adapters {		
		}
		}
		public static partial class Financeiro {		
		public static partial class Calculos {		
		}
		}
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Repositories.IHistoricoDoCaixaPorOperadorRepository HistoricoDoCaixaPorOperadorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IHistoricoDoCaixaPorOperadorRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IMovimentacaoNoCaixaPorOperadorRepository MovimentacaoNoCaixaPorOperadorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IMovimentacaoNoCaixaPorOperadorRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IMovimentacaoNoCaixaPorOperadorLancamentoRepository MovimentacaoNoCaixaPorOperadorLancamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IMovimentacaoNoCaixaPorOperadorLancamentoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IMovimentacaoNoCaixaPorOperadorTransacaoRepository MovimentacaoNoCaixaPorOperadorTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IMovimentacaoNoCaixaPorOperadorTransacaoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IRegistroDeCaixaPorOperadorRepository RegistroDeCaixaPorOperadorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IRegistroDeCaixaPorOperadorRepository>();}
			}
		}
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoRepository DescontoPersonalizadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoAssistentesRepository DescontoPersonalizadoAssistentesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoAssistentesRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoPacoteRepository DescontoPersonalizadoPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoPacoteRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoProdutoRepository DescontoPersonalizadoProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoProdutoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoProfissionaisRepository DescontoPersonalizadoProfissionaisRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoProfissionaisRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoServicoRepository DescontoPersonalizadoServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IDescontoPersonalizadoServicoRepository>();}
			}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		public static partial class CalculoComissao {		
		}
		}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		public static partial class Checkout {		
		}
		}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		public static partial class Connect {		
		public static partial class Pagarme {		
		}
		}
		}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		public static partial class Connect {		
		public static partial class Stone {		
		}
		}
		}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		public static partial class DescontosPersonalizados {		
		}
		}
		}
		public static partial class Financeiro {		
		public static partial class DTO {		
		public static partial class POS {		
		}
		}
		}
		public static partial class Financeiro {		
		public static partial class Enums {		
		}
		}
		public static partial class Financeiro {		
		public static partial class ExtensionMethods {		
		}
		}
		public static partial class Financeiro {		
		public static partial class Factories {		
		}
		}
		public static partial class Financeiro {		
		public static partial class Filtros {		
		}
		}
		public static partial class Financeiro {		
		public static partial class Interfaces {		
		}
		}
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoCompraProdutoRepository FolhaPagamentoCompraProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoCompraProdutoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemRepository FolhaPagamentoItemRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemBonificacaoRepository FolhaPagamentoItemBonificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemBonificacaoRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemGorjetaRepository FolhaPagamentoItemGorjetaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemGorjetaRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemSplitRepository FolhaPagamentoItemSplitRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemSplitRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemValeRepository FolhaPagamentoItemValeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoItemValeRepository>();}
			}
			public static Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoLancamentoRepository FolhaPagamentoLancamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Financeiro.Repositories.IFolhaPagamentoLancamentoRepository>();}
			}
		}
		public static partial class Financeiro {		
		public static partial class POS {		
		}
		}
		public static partial class Financeiro {		
		public static partial class Stories {		
		}
		}
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Repositories.IAssinaturaDigitalRepository AssinaturaDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IAssinaturaDigitalRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IConfiguracaoDoFormularioRepository ConfiguracaoDoFormularioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IConfiguracaoDoFormularioRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IFormularioDinamicoRepository FormularioDinamicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IFormularioDinamicoRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IFormularioRespondidoRepository FormularioRespondidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IFormularioRespondidoRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IOpcaoDeRespostaRepository OpcaoDeRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IOpcaoDeRespostaRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IPessoaPerguntadaRepository PessoaPerguntadaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IPessoaPerguntadaRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IQuestaoDoFormularioRepository QuestaoDoFormularioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IQuestaoDoFormularioRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IRespostaDoFormularioRepository RespostaDoFormularioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IRespostaDoFormularioRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.ISolicitacaoDeAssinaturaRepository SolicitacaoDeAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.ISolicitacaoDeAssinaturaRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.ITipoDeRespostaRepository TipoDeRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.ITipoDeRespostaRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IVersaoDaQuestaoDoFormularioRepository VersaoDaQuestaoDoFormularioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IVersaoDaQuestaoDoFormularioRepository>();}
			}
			public static Perlink.Trinks.Formulario.Repositories.IVersaoDoFormularioDinamicoRepository VersaoDoFormularioDinamicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Formulario.Repositories.IVersaoDoFormularioDinamicoRepository>();}
			}
		}
		public static partial class Formulario {		
		public static partial class DTO {		
		}
		}
		public static partial class Formulario {		
		public static partial class Enums {		
		}
		}
		public static partial class Formulario {		
		public static partial class Filtros {		
		}
		}
		public static partial class Formulario {		
		public static partial class Stories {		
		}
		}
		public static partial class Fotos {		
		public static partial class ControleDeFotos {		
		}
		}
		public static partial class Fotos {		
		public static partial class DTO {		
		}
		}
		public static partial class Fotos {		
		public static partial class Enums {		
		}
		}
		public static partial class Fotos {		
		public static partial class Factories {		
		}
		}
		public static partial class Fotos {		
			public static Perlink.Trinks.Fotos.Repositories.IFotoRepository FotoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Fotos.Repositories.IFotoRepository>();}
			}
		}
		public static partial class Fotos {		
		}
		public static partial class Google {		
		public static partial class Comparers {		
		}
		}
		public static partial class Google {		
		public static partial class DTO {		
		}
		}
		public static partial class Google {		
		public static partial class Enums {		
		}
		}
		public static partial class Google {		
		public static partial class Providers {		
		}
		}
		public static partial class GyraMais {		
			public static Perlink.Trinks.GyraMais.Repositories.IDadosClienteRepository DadosClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.GyraMais.Repositories.IDadosClienteRepository>();}
			}
		}
		public static partial class Identity {		
			public static Perlink.Trinks.Identity.Repositories.IApiAccountRepository ApiAccountRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Identity.Repositories.IApiAccountRepository>();}
			}
		}
		public static partial class Importacao {		
		}
		public static partial class Importacao {		
		public static partial class Conversores {		
		}
		}
		public static partial class Importacao {		
		public static partial class DTO {		
		}
		}
		public static partial class Importacao {		
		public static partial class Exceptions {		
		}
		}
		public static partial class Importacao {		
		public static partial class Importadores {		
		}
		}
		public static partial class Importacao {		
		public static partial class Statics {		
		}
		}
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Repositories.ISolicitacaoDeImportacaoRepository SolicitacaoDeImportacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ImportacaoDeDados.Repositories.ISolicitacaoDeImportacaoRepository>();}
			}
		}
		public static partial class ImportacaoDeDados {		
		public static partial class DTO {		
		}
		}
		public static partial class ImportacaoDeDados {		
		public static partial class Exceptions {		
		}
		}
		public static partial class ImportacaoDeDados {		
		public static partial class Importadores {		
		}
		}
		public static partial class ImportacaoDeDados {		
		public static partial class TiposDeColuna {		
		}
		}
		public static partial class IntegracaoComOutrosSistemas {		
		public static partial class DTO {		
		}
		}
		public static partial class IntegracaoComOutrosSistemas {		
			public static Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories.IEventoIntegracaoComOutrosSistemasRepository EventoIntegracaoComOutrosSistemasRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories.IEventoIntegracaoComOutrosSistemasRepository>();}
			}
			public static Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories.IFranquiaComChaveDeIntegracaoRepository FranquiaComChaveDeIntegracaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories.IFranquiaComChaveDeIntegracaoRepository>();}
			}
			public static Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories.IFranquiaEstabelecimentoComChaveDeIntegracaoRepository FranquiaEstabelecimentoComChaveDeIntegracaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories.IFranquiaEstabelecimentoComChaveDeIntegracaoRepository>();}
			}
		}
		public static partial class IntegracaoComOutrosSistemas {		
		public static partial class Enums {		
		}
		}
		public static partial class IntegracaoComOutrosSistemas {		
		public static partial class IntegracaoComTrinks {		
		}
		}
		public static partial class InternoProduto {		
		public static partial class Enum {		
		}
		}
		public static partial class InternoProduto {		
			public static Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoRepository QuestionarioProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoRepository>();}
			}
			public static Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoOpcaoDeRespostaRepository QuestionarioProdutoOpcaoDeRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoOpcaoDeRespostaRepository>();}
			}
			public static Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoPerguntaRepository QuestionarioProdutoPerguntaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoPerguntaRepository>();}
			}
			public static Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoRespondidoRepository QuestionarioProdutoRespondidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoRespondidoRepository>();}
			}
			public static Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoRespondidoRespostaRepository QuestionarioProdutoRespondidoRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoRespondidoRespostaRepository>();}
			}
			public static Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoTipoRespostaRepository QuestionarioProdutoTipoRespostaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.InternoProduto.Repositories.IQuestionarioProdutoTipoRespostaRepository>();}
			}
		}
		public static partial class LGPD {		
		public static partial class Helpers {		
		}
		}
		public static partial class LinksDePagamento {		
		public static partial class DTOs {		
		}
		}
		public static partial class LinksDePagamento {		
		public static partial class Enums {		
		}
		}
		public static partial class LinksDePagamento {		
			public static Perlink.Trinks.LinksDePagamento.Repositories.IItemLinkDePagamentoRepository ItemLinkDePagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.LinksDePagamento.Repositories.IItemLinkDePagamentoRepository>();}
			}
			public static Perlink.Trinks.LinksDePagamento.Repositories.ILinkDePagamentoRepository LinkDePagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.LinksDePagamento.Repositories.ILinkDePagamentoRepository>();}
			}
		}
		public static partial class LinksDePagamentoNoTrinks {		
		public static partial class DTOs {		
		}
		}
		public static partial class LinksDePagamentoNoTrinks {		
		public static partial class Enums {		
		}
		}
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Repositories.ILinkDePagamentoNoTrinksRepository LinkDePagamentoNoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.LinksDePagamentoNoTrinks.Repositories.ILinkDePagamentoNoTrinksRepository>();}
			}
		}
		public static partial class LinksDePagamentoNoTrinks {		
		public static partial class Stories {		
		}
		}
		public static partial class LinksDePagamentoNoTrinks {		
		public static partial class Strategies {		
		}
		}
		public static partial class Localizacoes {		
		public static partial class DTO {		
		}
		}
		public static partial class Localizacoes {		
		public static partial class Enums {		
		}
		}
		public static partial class Loggers {		
		}
		public static partial class Marcadores {		
			public static Perlink.Trinks.Marcadores.Repositories.ICorEtiquetaRepository CorEtiquetaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marcadores.Repositories.ICorEtiquetaRepository>();}
			}
			public static Perlink.Trinks.Marcadores.Repositories.IEtiquetaRepository EtiquetaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marcadores.Repositories.IEtiquetaRepository>();}
			}
			public static Perlink.Trinks.Marcadores.Repositories.IObjetoEtiquetadoRepository ObjetoEtiquetadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marcadores.Repositories.IObjetoEtiquetadoRepository>();}
			}
		}
		public static partial class Marcadores {		
		public static partial class DTO {		
		}
		}
		public static partial class Marcadores {		
		public static partial class Enums {		
		}
		}
		public static partial class Marcadores {		
		public static partial class ExtensionMethods {		
		}
		}
		public static partial class Marcadores {		
		public static partial class Filtros {		
		}
		}
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Repositories.IConfiguracoesEstabelecimentoMarketingRepository ConfiguracoesEstabelecimentoMarketingRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IConfiguracoesEstabelecimentoMarketingRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoRepository ConviteDeRetornoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoEmailRepository ConviteDeRetornoEmailRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoEmailRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoParaQuemEnviarRepository ConviteDeRetornoParaQuemEnviarRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoParaQuemEnviarRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoSMSRepository ConviteDeRetornoSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoSMSRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoWhatsAppRepository ConviteDeRetornoWhatsAppRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IConviteDeRetornoWhatsAppRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaRepository MarketingCampanhaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaEmailRepository MarketingCampanhaEmailRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaEmailRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaHistoricoRepository MarketingCampanhaHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaHistoricoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoRepository MarketingCampanhaPublicoAlvoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoRepository MarketingCampanhaPublicoAlvoServicoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoRepository MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository MarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoProfissionalRepository MarketingCampanhaPublicoAlvoProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaPublicoAlvoProfissionalRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaSMSRepository MarketingCampanhaSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaSMSRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaWhatsAppRepository MarketingCampanhaWhatsAppRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCampanhaWhatsAppRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingCompraCreditoRepository MarketingCompraCreditoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingCompraCreditoRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioRepository MarketingEnvioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteRepository MarketingEnvioClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteEmailRepository MarketingEnvioClienteEmailRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteEmailRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteParametroEnvioRepository MarketingEnvioClienteParametroEnvioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteParametroEnvioRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteSMSRepository MarketingEnvioClienteSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteSMSRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteWhatsAppRepository MarketingEnvioClienteWhatsAppRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioClienteWhatsAppRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioEmailRepository MarketingEnvioEmailRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioEmailRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioSMSRepository MarketingEnvioSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioSMSRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingEnvioWhatsAppRepository MarketingEnvioWhatsAppRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingEnvioWhatsAppRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingFaixaProfissionaisRepository MarketingFaixaProfissionaisRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingFaixaProfissionaisRepository>();}
			}
			public static Perlink.Trinks.Marketing.Repositories.IMarketingPacoteCreditoRepository MarketingPacoteCreditoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Marketing.Repositories.IMarketingPacoteCreditoRepository>();}
			}
		}
		public static partial class Marketing {		
		public static partial class DTO {		
		}
		}
		public static partial class Marketing {		
		public static partial class Enums {		
		}
		}
		public static partial class Marketing {		
		}
		public static partial class Marketing {		
		public static partial class Factories {		
		}
		}
		public static partial class Marketing {		
		public static partial class Filtro {		
		}
		}
		public static partial class Marketing {		
		public static partial class Filtros {		
		}
		}
		public static partial class Marketing {		
		public static partial class Strategies {		
		}
		}
		public static partial class MarketingInterno {		
			public static Perlink.Trinks.MarketingInterno.Repositories.IDadosMarketingRepository DadosMarketingRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.MarketingInterno.Repositories.IDadosMarketingRepository>();}
			}
		}
		public static partial class MensagemEmTela {		
		public static partial class DTOs {		
		}
		}
		public static partial class MensagemEmTela {		
		public static partial class Implementacoes {		
		}
		}
		public static partial class MensagemEmTela {		
			public static Perlink.Trinks.MensagemEmTela.Repositories.IMensagemAvisoRepository MensagemAvisoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.MensagemEmTela.Repositories.IMensagemAvisoRepository>();}
			}
			public static Perlink.Trinks.MensagemEmTela.Repositories.IMensagemAvisoTextoLivreRepository MensagemAvisoTextoLivreRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.MensagemEmTela.Repositories.IMensagemAvisoTextoLivreRepository>();}
			}
			public static Perlink.Trinks.MensagemEmTela.Repositories.IMensagemAvisoImplementacaoRepository MensagemAvisoImplementacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.MensagemEmTela.Repositories.IMensagemAvisoImplementacaoRepository>();}
			}
		}
		public static partial class MensagensEmMassa {		
			public static Perlink.Trinks.MensagensEmMassa.Repositories.IModeloDeMensagemRepository ModeloDeMensagemRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.MensagensEmMassa.Repositories.IModeloDeMensagemRepository>();}
			}
		}
		public static partial class MessageQueue {		
		}
		public static partial class Metricas {		
		public static partial class Dto {		
		}
		}
		public static partial class Metricas {		
		public static partial class Enums {		
		}
		}
		public static partial class Metricas {		
			public static Perlink.Trinks.Metricas.Repositories.IMetricaDesativadaRepository MetricaDesativadaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Metricas.Repositories.IMetricaDesativadaRepository>();}
			}
		}
		public static partial class NotaFiscalDoConsumidor {		
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IConfiguracaoDeNFCDoEstabelecimentoRepository ConfiguracaoDeNFCDoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IConfiguracaoDeNFCDoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IConfiguracaoNFCEstadoRepository ConfiguracaoNFCEstadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IConfiguracaoNFCEstadoRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IImpressaoDeNFCRepository ImpressaoDeNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IImpressaoDeNFCRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INfcSituacaoTributariaRepository NfcSituacaoTributariaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INfcSituacaoTributariaRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INomenclaturaNCMeNBSRepository NomenclaturaNCMeNBSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INomenclaturaNCMeNBSRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaFormaPagamentoNFCRepository NotaFormaPagamentoNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaFormaPagamentoNFCRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaInutilizadaNFCRepository NotaInutilizadaNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaInutilizadaNFCRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaItensNFCRepository NotaItensNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaItensNFCRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaNFCRepository NotaNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.INotaNFCRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IPessoaJuridicaCertificadoDigitalRepository PessoaJuridicaCertificadoDigitalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IPessoaJuridicaCertificadoDigitalRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IStatusNotaNFCRepository StatusNotaNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.IStatusNotaNFCRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.ITabelaIBPTRepository TabelaIBPTRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.ITabelaIBPTRepository>();}
			}
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.ITipoRegimeTributarioNFCRepository TipoRegimeTributarioNFCRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotaFiscalDoConsumidor.Repositories.ITipoRegimeTributarioNFCRepository>();}
			}
		}
		public static partial class NotaFiscalDoConsumidor {		
		public static partial class DTO {		
		}
		}
		public static partial class NotaFiscalDoConsumidor {		
		public static partial class Enums {		
		}
		}
		public static partial class NotaFiscalDoConsumidor {		
		public static partial class Exceptions {		
		}
		}
		public static partial class NotaFiscalDoConsumidor {		
		public static partial class Factories {		
		}
		}
		public static partial class NotaFiscalDoConsumidor {		
		public static partial class GeradoresDeNF {		
		}
		}
		public static partial class NotaFiscalDoConsumidor {		
		public static partial class GeradoresDeNF {		
		public static partial class SAT {		
		public static partial class Cancelamento {		
		}
		}
		}
		}
		public static partial class Web {		
		public static partial class Areas {		
		public static partial class BackOffice {		
		public static partial class Models {		
		public static partial class Produtos {		
		}
		}
		}
		}
		}
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Repositories.ICampanhaPushRepository CampanhaPushRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.ICampanhaPushRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IContaQtdNotificacoesNovasRepository ContaQtdNotificacoesNovasRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IContaQtdNotificacoesNovasRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IDispositivoRepository DispositivoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IDispositivoRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IDispositivoB2cValidoRepository DispositivoB2cValidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IDispositivoB2cValidoRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IInscricaoEmNotificacaoRepository InscricaoEmNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IInscricaoEmNotificacaoRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.INotificacaoDoTrinksRepository NotificacaoDoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.INotificacaoDoTrinksRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.INotificacaoPushRepository NotificacaoPushRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.INotificacaoPushRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IOperadoraRepository OperadoraRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IOperadoraRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IOperadoraServicoSMSRepository OperadoraServicoSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IOperadoraServicoSMSRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IRegistroNotificacaoRepository RegistroNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IRegistroNotificacaoRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.IServicoSMSRepository ServicoSMSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.IServicoSMSRepository>();}
			}
			public static Perlink.Trinks.Notificacoes.Repositories.ITipoNotificacaoRepository TipoNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Notificacoes.Repositories.ITipoNotificacaoRepository>();}
			}
		}
		public static partial class Notificacoes {		
		public static partial class DTO {		
		}
		}
		public static partial class Notificacoes {		
		public static partial class Enums {		
		}
		}
		public static partial class Notificacoes {		
		public static partial class Filtros {		
		}
		}
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Repositories.ICanalDaNotificacaoRepository CanalDaNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotificacoesApps.Repositories.ICanalDaNotificacaoRepository>();}
			}
			public static Perlink.Trinks.NotificacoesApps.Repositories.IEventoDeNotificacaoRepository EventoDeNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotificacoesApps.Repositories.IEventoDeNotificacaoRepository>();}
			}
			public static Perlink.Trinks.NotificacoesApps.Repositories.IMensagemDeNotificacaoRepository MensagemDeNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotificacoesApps.Repositories.IMensagemDeNotificacaoRepository>();}
			}
			public static Perlink.Trinks.NotificacoesApps.Repositories.IPreferenciaDeNotificacaoDoUsuarioRepository PreferenciaDeNotificacaoDoUsuarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotificacoesApps.Repositories.IPreferenciaDeNotificacaoDoUsuarioRepository>();}
			}
			public static Perlink.Trinks.NotificacoesApps.Repositories.ITipoDeNotificacaoRepository TipoDeNotificacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.NotificacoesApps.Repositories.ITipoDeNotificacaoRepository>();}
			}
		}
		public static partial class NotificacoesApps {		
		public static partial class DTO {		
		}
		}
		public static partial class NotificacoesApps {		
		}
		public static partial class NotificacoesApps {		
		public static partial class Strategies {		
		}
		}
		public static partial class Novidades {		
		public static partial class DTO {		
		}
		}
		public static partial class Novidades {		
		}
		public static partial class Onboardings {		
		public static partial class Comparers {		
		}
		}
		public static partial class Onboardings {		
		public static partial class DTOs {		
		}
		}
		public static partial class Onboardings {		
		public static partial class Enums {		
		}
		}
		public static partial class Onboardings {		
			public static Perlink.Trinks.Onboardings.Repositories.IEstabelecimentoTrilhaRepository EstabelecimentoTrilhaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.IEstabelecimentoTrilhaRepository>();}
			}
			public static Perlink.Trinks.Onboardings.Repositories.IQuestionarioOnboardingPorFaixaRepository QuestionarioOnboardingPorFaixaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.IQuestionarioOnboardingPorFaixaRepository>();}
			}
			public static Perlink.Trinks.Onboardings.Repositories.IRastreioDeTarefaRepository RastreioDeTarefaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.IRastreioDeTarefaRepository>();}
			}
			public static Perlink.Trinks.Onboardings.Repositories.ITarefaRepository TarefaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.ITarefaRepository>();}
			}
			public static Perlink.Trinks.Onboardings.Repositories.ITrilhaRepository TrilhaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.ITrilhaRepository>();}
			}
			public static Perlink.Trinks.Onboardings.Repositories.ITrilhaAcaoRepository TrilhaAcaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.ITrilhaAcaoRepository>();}
			}
			public static Perlink.Trinks.Onboardings.Repositories.ITrilhaAcaoGrupoRepository TrilhaAcaoGrupoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Onboardings.Repositories.ITrilhaAcaoGrupoRepository>();}
			}
		}
		public static partial class Onboardings {		
		public static partial class Stories {		
		}
		}
		public static partial class Pacotes {		
		public static partial class Adapters {		
		}
		}
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Repositories.IConfiguracaoPacotePersonalizadoRepository ConfiguracaoPacotePersonalizadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IConfiguracaoPacotePersonalizadoRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IHistoricoConfiguracoesPacoteRepository HistoricoConfiguracoesPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IHistoricoConfiguracoesPacoteRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IItemPacoteRepository ItemPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IItemPacoteRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IItemPacoteClienteRepository ItemPacoteClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IItemPacoteClienteRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IItemPacoteClienteProdutoRepository ItemPacoteClienteProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IItemPacoteClienteProdutoRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IItemPacoteClienteServicoRepository ItemPacoteClienteServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IItemPacoteClienteServicoRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IItemPacoteProdutoRepository ItemPacoteProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IItemPacoteProdutoRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IItemPacoteServicoRepository ItemPacoteServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IItemPacoteServicoRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.ILinkDePagamentoDoPacoteRepository LinkDePagamentoDoPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.ILinkDePagamentoDoPacoteRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IPacoteRepository PacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IPacoteRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IPacoteClienteRepository PacoteClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IPacoteClienteRepository>();}
			}
			public static Perlink.Trinks.Pacotes.Repositories.IPacoteClienteHistoricoRepository PacoteClienteHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pacotes.Repositories.IPacoteClienteHistoricoRepository>();}
			}
		}
		public static partial class Pacotes {		
		public static partial class DTO {		
		}
		}
		public static partial class Pacotes {		
		public static partial class Enums {		
		}
		}
		public static partial class Pacotes {		
		public static partial class Factories {		
		}
		}
		public static partial class PagamentoAntecipadoHotsite {		
		public static partial class DTO {		
		}
		}
		public static partial class PagamentoAntecipadoHotsite {		
		public static partial class Enums {		
		}
		}
		public static partial class PagamentoAntecipadoHotsite {		
			public static Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories.IPagamentoAntecipadoHotsiteConfiguracoesRepository PagamentoAntecipadoHotsiteConfiguracoesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories.IPagamentoAntecipadoHotsiteConfiguracoesRepository>();}
			}
			public static Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories.IPagamentoAntecipadoHotsiteServicosRepository PagamentoAntecipadoHotsiteServicosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories.IPagamentoAntecipadoHotsiteServicosRepository>();}
			}
		}
		public static partial class Pagamentos {		
		public static partial class Calculos {		
		}
		}
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Repositories.ICartaoDeCompradorRepository CartaoDeCompradorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ICartaoDeCompradorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.ICartaoDeCompradorGatewayRepository CartaoDeCompradorGatewayRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ICartaoDeCompradorGatewayRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.ICompradorRepository CompradorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ICompradorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.ICompradorGatewayRepository CompradorGatewayRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ICompradorGatewayRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IContaBancariaRepository ContaBancariaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IContaBancariaRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IContaBancariaGatewayRepository ContaBancariaGatewayRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IContaBancariaGatewayRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IDocumentoDeRecebedorCredenciadoRepository DocumentoDeRecebedorCredenciadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IDocumentoDeRecebedorCredenciadoRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IEnderecoDeCobrancaRepository EnderecoDeCobrancaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IEnderecoDeCobrancaRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IEtapaCadastroPagarmeRepository EtapaCadastroPagarmeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IEtapaCadastroPagarmeRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IGatewayRepository GatewayRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IGatewayRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IItemPagamentoRepository ItemPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IItemPagamentoRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.ILimitePagamentoRepository LimitePagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ILimitePagamentoRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.ILimitePagamentoRecebedorRepository LimitePagamentoRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ILimitePagamentoRecebedorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IPagamentoRepository PagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IPagamentoRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IRecebedorRepository RecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IRecebedorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IRecebedorCredenciadoRepository RecebedorCredenciadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IRecebedorCredenciadoRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.ISplitPagamentoRepository SplitPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.ISplitPagamentoRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IUsoLimiteAntecipacaoDiarioRecebedorRepository UsoLimiteAntecipacaoDiarioRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IUsoLimiteAntecipacaoDiarioRecebedorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IUsoLimiteAntecipacaoMensalRecebedorRepository UsoLimiteAntecipacaoMensalRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IUsoLimiteAntecipacaoMensalRecebedorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IUsoLimitePagamentoDiarioRecebedorRepository UsoLimitePagamentoDiarioRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IUsoLimitePagamentoDiarioRecebedorRepository>();}
			}
			public static Perlink.Trinks.Pagamentos.Repositories.IUsoLimitePagamentoMensalRecebedorRepository UsoLimitePagamentoMensalRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pagamentos.Repositories.IUsoLimitePagamentoMensalRecebedorRepository>();}
			}
		}
		public static partial class Pagamentos {		
		public static partial class Config {		
		}
		}
		public static partial class Pagamentos {		
		public static partial class DTO {		
		}
		}
		public static partial class Pagamentos {		
		public static partial class Enums {		
		}
		}
		public static partial class Pagamentos {		
		public static partial class Exceptions {		
		}
		}
		public static partial class Pagamentos {		
		public static partial class Factories {		
		}
		}
		public static partial class Pagamentos {		
		public static partial class Providers {		
		}
		}
		public static partial class PagamentosAntecipados {		
			public static Perlink.Trinks.PagamentosAntecipados.Repositories.IBeneficiosEstabelecimentoRepository BeneficiosEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosAntecipados.Repositories.IBeneficiosEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.PagamentosAntecipados.Repositories.IBeneficiosPagamentoRepository BeneficiosPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosAntecipados.Repositories.IBeneficiosPagamentoRepository>();}
			}
			public static Perlink.Trinks.PagamentosAntecipados.Repositories.IItemPagamentoAntecipadoRepository ItemPagamentoAntecipadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosAntecipados.Repositories.IItemPagamentoAntecipadoRepository>();}
			}
			public static Perlink.Trinks.PagamentosAntecipados.Repositories.IItemPagamentoHorarioRepository ItemPagamentoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosAntecipados.Repositories.IItemPagamentoHorarioRepository>();}
			}
			public static Perlink.Trinks.PagamentosAntecipados.Repositories.IPagamentoAntecipadoRepository PagamentoAntecipadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosAntecipados.Repositories.IPagamentoAntecipadoRepository>();}
			}
			public static Perlink.Trinks.PagamentosAntecipados.Repositories.IServicoHabilitadoRepository ServicoHabilitadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosAntecipados.Repositories.IServicoHabilitadoRepository>();}
			}
		}
		public static partial class PagamentosAntecipados {		
		public static partial class DTO {		
		}
		}
		public static partial class PagamentosAntecipados {		
		public static partial class Enums {		
		}
		}
		public static partial class Specifications {		
		}
		public static partial class PagamentosAntecipados {		
		public static partial class Utils {		
		}
		}
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoRepository ConfiguracaoAdiantamentoFuncionalidadeAntecipacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IEstabelecimentoRecebedorRepository EstabelecimentoRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IEstabelecimentoRecebedorRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IInstituicaoBancariaRepository InstituicaoBancariaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IInstituicaoBancariaRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ILOGCancelamentoAntecipacaoRepository LOGCancelamentoAntecipacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ILOGCancelamentoAntecipacaoRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ILOGSolicitacaoAntecipacaoRepository LOGSolicitacaoAntecipacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ILOGSolicitacaoAntecipacaoRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IPagamentoOnlineNoTrinksRepository PagamentoOnlineNoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.IPagamentoOnlineNoTrinksRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasDoEstabelecimentoRepository TaxasDoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasDoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasDoPagamentoOnlineNoTrinksRepository TaxasDoPagamentoOnlineNoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasDoPagamentoOnlineNoTrinksRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasPadraoRepository TaxasPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasPadraoRepository>();}
			}
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasPadraoEstabelecimentoRecebedorRepository TaxasPadraoEstabelecimentoRecebedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories.ITaxasPadraoEstabelecimentoRecebedorRepository>();}
			}
		}
		public static partial class PagamentosOnlineNoTrinks {		
		public static partial class DTO {		
		}
		}
		public static partial class PagamentosOnlineNoTrinks {		
		public static partial class Enums {		
		}
		}
		public static partial class PagamentosOnlineNoTrinks {		
		public static partial class Exceptions {		
		}
		}
		public static partial class PagamentosOnlineNoTrinks {		
		public static partial class Factories {		
		}
		}
		public static partial class PDF {		
		}
		public static partial class Permissoes {		
		public static partial class AreaPerlink {		
			public static Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IAreaPerlinkPerfilRepository AreaPerlinkPerfilRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IAreaPerlinkPerfilRepository>();}
			}
			public static Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IAreaPerlinkPerfilPermissaoRepository AreaPerlinkPerfilPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IAreaPerlinkPerfilPermissaoRepository>();}
			}
			public static Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IContaPerfilRepository ContaPerfilRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IContaPerfilRepository>();}
			}
			public static Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IContaPerfilPermissaoRepository ContaPerfilPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.AreaPerlink.Repositories.IContaPerfilPermissaoRepository>();}
			}
		}
		}
		public static partial class Permissoes {		
		public static partial class AreaPerlink {		
		}
		}
		public static partial class Permissoes {		
			public static Perlink.Trinks.Permissoes.Repositories.ICategoriaPermissaoRepository CategoriaPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.ICategoriaPermissaoRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IDescricaoPermissaoRepository DescricaoPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IDescricaoPermissaoRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IFranquiaPermissaoRepository FranquiaPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IFranquiaPermissaoRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IPerfilRepository PerfilRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IPerfilRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IPerfilPermissaoRepository PerfilPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IPerfilPermissaoRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IPermissaoAreaRepository PermissaoAreaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IPermissaoAreaRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IUsuarioPerfilRepository UsuarioPerfilRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IUsuarioPerfilRepository>();}
			}
			public static Perlink.Trinks.Permissoes.Repositories.IUsuarioPerfilPermissaoRepository UsuarioPerfilPermissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Permissoes.Repositories.IUsuarioPerfilPermissaoRepository>();}
			}
		}
		public static partial class Permissoes {		
		}
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Repositories.IBairroRepository BairroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IBairroRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ICacheLocalidadeRepository CacheLocalidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ICacheLocalidadeRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ICategoriaPortalServicoRepository CategoriaPortalServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ICategoriaPortalServicoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ICidadeRepository CidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ICidadeRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IClienteRepository ClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IClienteRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IClienteAreaPerlinkRepository ClienteAreaPerlinkRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IClienteAreaPerlinkRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IClienteEstabelecimentoRepository ClienteEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IClienteEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IClienteEstabelecimentoSaldosRepository ClienteEstabelecimentoSaldosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IClienteEstabelecimentoSaldosRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IComoConheceuRepository ComoConheceuRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IComoConheceuRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IComoEstabelecimentoConheceuOTrinksRepository ComoEstabelecimentoConheceuOTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IComoEstabelecimentoConheceuOTrinksRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ICompartilhamentoNaRedeRepository CompartilhamentoNaRedeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ICompartilhamentoNaRedeRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteAderenciaRepository ConfiguracaoHotsiteAderenciaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteAderenciaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteInicioMarcosRepository ConfiguracaoHotsiteInicioMarcosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteInicioMarcosRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteIntervaloRepository ConfiguracaoHotsiteIntervaloRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteIntervaloRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteUniversoRepository ConfiguracaoHotsiteUniversoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IConfiguracaoHotsiteUniversoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IContaRepository ContaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IContaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IContaFranquiaRepository ContaFranquiaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IContaFranquiaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IDadosParaRecalculoComissaoRepository DadosParaRecalculoComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IDadosParaRecalculoComissaoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IDataEspecialRepository DataEspecialRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IDataEspecialRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IDiaSemanaRepository DiaSemanaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IDiaSemanaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEmailRejeitadoAmazonRepository EmailRejeitadoAmazonRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEmailRejeitadoAmazonRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEnderecoRepository EnderecoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEnderecoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEnderecoPreenchidoManualmenteRepository EnderecoPreenchidoManualmenteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEnderecoPreenchidoManualmenteRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoRepository EstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoAssistenteServicoRepository EstabelecimentoAssistenteServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoAssistenteServicoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoAssistenteServicoComissaoRepository EstabelecimentoAssistenteServicoComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoAssistenteServicoComissaoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoAtendeCriancaRepository EstabelecimentoAtendeCriancaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoAtendeCriancaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoConfiguracaoComissaoRepository EstabelecimentoConfiguracaoComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoConfiguracaoComissaoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoConfiguracaoGeralRepository EstabelecimentoConfiguracaoGeralRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoConfiguracaoGeralRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoConfiguracaoPOSRepository EstabelecimentoConfiguracaoPOSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoConfiguracaoPOSRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosGeraisRepository EstabelecimentoDadosGeraisRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosGeraisRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroRepository EstabelecimentoDadosPreCadastroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroCargoRepository EstabelecimentoDadosPreCadastroCargoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroCargoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroMotivosCadastroRepository EstabelecimentoDadosPreCadastroMotivosCadastroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroMotivosCadastroRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroSegmentosRepository EstabelecimentoDadosPreCadastroSegmentosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoDadosPreCadastroSegmentosRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFabricanteProdutoRepository EstabelecimentoFabricanteProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFabricanteProdutoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFormaPagamentoRepository EstabelecimentoFormaPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFormaPagamentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFormaPagamentoParcelaRepository EstabelecimentoFormaPagamentoParcelaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFormaPagamentoParcelaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFornecedorRepository EstabelecimentoFornecedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoFornecedorRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoHorarioEspecialFuncionamentoRepository EstabelecimentoHorarioEspecialFuncionamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoHorarioEspecialFuncionamentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoHorarioEspecialFuncionamentoTipoRepository EstabelecimentoHorarioEspecialFuncionamentoTipoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoHorarioEspecialFuncionamentoTipoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoHorarioFuncionamentoRepository EstabelecimentoHorarioFuncionamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoHorarioFuncionamentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoIndicadoRepository EstabelecimentoIndicadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoIndicadoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoMovimentacaoEstoqueRepository EstabelecimentoMovimentacaoEstoqueRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoMovimentacaoEstoqueRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoPossuiEstacionamentoRepository EstabelecimentoPossuiEstacionamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoPossuiEstacionamentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoPreCadastroRepository EstabelecimentoPreCadastroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoPreCadastroRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProdutoRepository EstabelecimentoProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProdutoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalRepository EstabelecimentoProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalRedeSocialRepository EstabelecimentoProfissionalRedeSocialRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalRedeSocialRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalServicoRepository EstabelecimentoProfissionalServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalServicoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalServicoComissaoRepository EstabelecimentoProfissionalServicoComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoProfissionalServicoComissaoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoSolicitacaoAparecerBuscaRepository EstabelecimentoSolicitacaoAparecerBuscaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoSolicitacaoAparecerBuscaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoTemplateRepository EstabelecimentoTemplateRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstabelecimentoTemplateRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstadoCivilRepository EstadoCivilRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstadoCivilRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IEstatisticaExibicaoTelefoneRepository EstatisticaExibicaoTelefoneRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IEstatisticaExibicaoTelefoneRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFabricanteProdutoPadraoRepository FabricanteProdutoPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFabricanteProdutoPadraoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFormaRelacaoProfissionalRepository FormaRelacaoProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFormaRelacaoProfissionalRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFormaRelacaoProfissionalPadraoRepository FormaRelacaoProfissionalPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFormaRelacaoProfissionalPadraoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFornecedorRepository FornecedorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFornecedorRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFotoDeClienteEstabelecimentoRepository FotoDeClienteEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFotoDeClienteEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFotoDeServicoRealizadoEmHorarioRepository FotoDeServicoRealizadoEmHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFotoDeServicoRealizadoEmHorarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFotoEstabelecimentoRepository FotoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFotoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFotoPessoaRepository FotoPessoaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFotoPessoaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFranquiaRepository FranquiaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFranquiaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFranquiaEstabelecimentoRepository FranquiaEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFranquiaEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFuncaoDoProfissionalRepository FuncaoDoProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFuncaoDoProfissionalRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IFuncaoDoProfissionalPadraoRepository FuncaoDoProfissionalPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IFuncaoDoProfissionalPadraoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHistoricoAcaoUnidadeAoModeloRepository HistoricoAcaoUnidadeAoModeloRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHistoricoAcaoUnidadeAoModeloRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHistoricoClienteRepository HistoricoClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHistoricoClienteRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioRepository HorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioHistoricoRepository HorarioHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioHistoricoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioHistoricoEtiquetaRepository HorarioHistoricoEtiquetaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioHistoricoEtiquetaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioOrigemRepository HorarioOrigemRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioOrigemRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioQuemCancelouRepository HorarioQuemCancelouRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioQuemCancelouRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioTrabalhoRepository HorarioTrabalhoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioTrabalhoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioTransacaoRepository HorarioTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioTransacaoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioVeraoCidadeRepository HorarioVeraoCidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioVeraoCidadeRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHorarioVeraoUFRepository HorarioVeraoUFRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHorarioVeraoUFRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IHotsiteEstabelecimentoRepository HotsiteEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IHotsiteEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IItemComboClienteRepository ItemComboClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IItemComboClienteRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ILinkDePagamentoDoHorarioRepository LinkDePagamentoDoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ILinkDePagamentoDoHorarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IMidiaSocialRepository MidiaSocialRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IMidiaSocialRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IMotivoAusenciaRepository MotivoAusenciaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IMotivoAusenciaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IMotivoQueEscolheuOTrinksRepository MotivoQueEscolheuOTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IMotivoQueEscolheuOTrinksRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IMotivoQueEstabelecimentoEscolheuOTrinksRepository MotivoQueEstabelecimentoEscolheuOTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IMotivoQueEstabelecimentoEscolheuOTrinksRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.INotificacaoEstabelecimentoRepository NotificacaoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.INotificacaoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.INovidadeRepository NovidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.INovidadeRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IParametrizacaoTrinksRepository ParametrizacaoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IParametrizacaoTrinksRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IPeriodoAusenciaRepository PeriodoAusenciaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IPeriodoAusenciaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IPermissoesDaUnidadeBaseadaEmModeloRepository PermissoesDaUnidadeBaseadaEmModeloRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IPermissoesDaUnidadeBaseadaEmModeloRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IPessoaRepository PessoaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IPessoaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IPessoaFisicaRepository PessoaFisicaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IPessoaFisicaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IPessoaJuridicaRepository PessoaJuridicaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IPessoaJuridicaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IPessoaJuridicaConfiguracaoNFeRepository PessoaJuridicaConfiguracaoNFeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IPessoaJuridicaConfiguracaoNFeRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IProfissionalRepository ProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IProfissionalRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IQuestionarioRepository QuestionarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IQuestionarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IRecorrenciaHorarioRepository RecorrenciaHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IRecorrenciaHorarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IRelatorioFormaPagamentoRepository RelatorioFormaPagamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IRelatorioFormaPagamentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ISaldoDeSMSLembreteDoEstabelecimentoRepository SaldoDeSMSLembreteDoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ISaldoDeSMSLembreteDoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IServicoRepository ServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IServicoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IServicoCategoriaRepository ServicoCategoriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IServicoCategoriaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IServicoCategoriaEstabelecimentoRepository ServicoCategoriaEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IServicoCategoriaEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IServicoEstabelecimentoRepository ServicoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IServicoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IServicoSinonimoRepository ServicoSinonimoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IServicoSinonimoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ISincronizacaoEntreEstabelecimentosModelosFilaRepository SincronizacaoEntreEstabelecimentosModelosFilaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ISincronizacaoEntreEstabelecimentosModelosFilaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ISincronizacaoEstabelecimentosFilaRepository SincronizacaoEstabelecimentosFilaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ISincronizacaoEstabelecimentosFilaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IStatusHorarioRepository StatusHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IStatusHorarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITelefoneRepository TelefoneRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITelefoneRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITelefoneInternacionalRepository TelefoneInternacionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITelefoneInternacionalRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITemaHotsiteRepository TemaHotsiteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITemaHotsiteRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITemplateRepository TemplateRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITemplateRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITemplateHotsiteRepository TemplateHotsiteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITemplateHotsiteRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoComissaoRepository TipoComissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoComissaoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoDescontoRepository TipoDescontoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoDescontoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoEstabelecimentoRepository TipoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoFranquiaRepository TipoFranquiaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoFranquiaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoLogradouroRepository TipoLogradouroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoLogradouroRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoPOSRepository TipoPOSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoPOSRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoPrecoRepository TipoPrecoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoPrecoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoRecorrenciaHorarioRepository TipoRecorrenciaHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoRecorrenciaHorarioRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoServicoEstabelecimentoRepository TipoServicoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoServicoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoTelefoneRepository TipoTelefoneRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoTelefoneRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.ITipoTemplateRepository TipoTemplateRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.ITipoTemplateRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IUFRepository UFRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IUFRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IUnidadeMedidaRepository UnidadeMedidaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IUnidadeMedidaRepository>();}
			}
			public static Perlink.Trinks.Pessoas.Repositories.IUsuarioEstabelecimentoRepository UsuarioEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Pessoas.Repositories.IUsuarioEstabelecimentoRepository>();}
			}
		}
		public static partial class Pessoas {		
		public static partial class Builders {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Configuracoes {		
		}
		}
		public static partial class Pessoas {		
		public static partial class DTO {		
		}
		}
		public static partial class Pessoas {		
		public static partial class DTO {		
		public static partial class Filtros {		
		}
		}
		}
		public static partial class Pessoas {		
		public static partial class Enums {		
		}
		}
		public static partial class Pessoas {		
		public static partial class ExtensionMethods {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Factories {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Filtros {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Helpers {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Interfaces {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Repositories {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Statics {		
		}
		}
		public static partial class Pessoas {		
		public static partial class Stories {		
		}
		}
		public static partial class Pessoas {		
		public static partial class VO {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class DTO {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class DTO {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class DTO {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class Enums {		
		}
		}
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IEstabelecimentoProdutoCategoriaRepository EstabelecimentoProdutoCategoriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IEstabelecimentoProdutoCategoriaRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IInventarioRepository InventarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IInventarioRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IInventarioMovimentacaoEstoqueRepository InventarioMovimentacaoEstoqueRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IInventarioMovimentacaoEstoqueRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IMovimentoEstoqueTipoRepository MovimentoEstoqueTipoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IMovimentoEstoqueTipoRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IOpcaoDaPropriedadeDeProdutoRepository OpcaoDaPropriedadeDeProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IOpcaoDaPropriedadeDeProdutoRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IProdutoCategoriaPadraoRepository ProdutoCategoriaPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IProdutoCategoriaPadraoRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IProdutoDoInventarioRepository ProdutoDoInventarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IProdutoDoInventarioRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IProdutoPadraoRepository ProdutoPadraoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IProdutoPadraoRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IPropriedadeDeProdutoRepository PropriedadeDeProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IPropriedadeDeProdutoRepository>();}
			}
			public static Perlink.Trinks.ProdutoEstoque.Repositories.IValorPropriedadeDoProdutoRepository ValorPropriedadeDoProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProdutoEstoque.Repositories.IValorPropriedadeDoProdutoRepository>();}
			}
		}
		public static partial class ProdutoEstoque {		
		public static partial class ExtensionMethods {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class Factories {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class Filtros {		
		}
		}
		public static partial class ProdutoEstoque {		
		public static partial class Stories {		
		}
		}
		public static partial class ProfissionalAgenda {		
			public static Perlink.Trinks.ProfissionalAgenda.Repositories.IDisponibilidadeNaAgendaRepository DisponibilidadeNaAgendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProfissionalAgenda.Repositories.IDisponibilidadeNaAgendaRepository>();}
			}
			public static Perlink.Trinks.ProfissionalAgenda.Repositories.ILiberacaoDeHorarioNaAgendaRepository LiberacaoDeHorarioNaAgendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProfissionalAgenda.Repositories.ILiberacaoDeHorarioNaAgendaRepository>();}
			}
			public static Perlink.Trinks.ProfissionalAgenda.Repositories.IMotivoDeLiberacaoRepository MotivoDeLiberacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProfissionalAgenda.Repositories.IMotivoDeLiberacaoRepository>();}
			}
			public static Perlink.Trinks.ProfissionalAgenda.Repositories.IRelatorioAusenciaELiberacaoHorarioRepository RelatorioAusenciaELiberacaoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProfissionalAgenda.Repositories.IRelatorioAusenciaELiberacaoHorarioRepository>();}
			}
			public static Perlink.Trinks.ProfissionalAgenda.Repositories.ITipoDeDisponibilidadeRepository TipoDeDisponibilidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProfissionalAgenda.Repositories.ITipoDeDisponibilidadeRepository>();}
			}
		}
		public static partial class ProfissionalAgenda {		
		public static partial class DTO {		
		}
		}
		public static partial class ProfissionalAgenda {		
		public static partial class Enums {		
		}
		}
		public static partial class ProfissionalAgenda {		
		public static partial class Stories {		
		}
		}
		public static partial class ProjetoBackToSalon {		
			public static Perlink.Trinks.ProjetoBackToSalon.Repositories.ICupomRepository CupomRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoBackToSalon.Repositories.ICupomRepository>();}
			}
			public static Perlink.Trinks.ProjetoBackToSalon.Repositories.IEstabelecimentoParticipanteBTSRepository EstabelecimentoParticipanteBTSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoBackToSalon.Repositories.IEstabelecimentoParticipanteBTSRepository>();}
			}
		}
		public static partial class ProjetoBackToSalon {		
		public static partial class DTO {		
		}
		}
		public static partial class ProjetoBackToSalon {		
		public static partial class Enum {		
		}
		}
		public static partial class ProjetoBackToSalon {		
		public static partial class Filters {		
		}
		}
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Repositories.ICompraDeVoucherRepository CompraDeVoucherRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoBelezaAmiga.Repositories.ICompraDeVoucherRepository>();}
			}
			public static Perlink.Trinks.ProjetoBelezaAmiga.Repositories.IEstabelecimentoParticipanteRepository EstabelecimentoParticipanteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoBelezaAmiga.Repositories.IEstabelecimentoParticipanteRepository>();}
			}
			public static Perlink.Trinks.ProjetoBelezaAmiga.Repositories.IVoucherRepository VoucherRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoBelezaAmiga.Repositories.IVoucherRepository>();}
			}
		}
		public static partial class ProjetoBelezaAmiga {		
		public static partial class DTO {		
		}
		}
		public static partial class ProjetoBelezaAmiga {		
		public static partial class Enums {		
		}
		}
		public static partial class ProjetoEncontreSeuSalao {		
			public static Perlink.Trinks.ProjetoEncontreSeuSalao.Repositories.IEstabelecimentoParticipanteESSV2Repository EstabelecimentoParticipanteESSV2Repository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoEncontreSeuSalao.Repositories.IEstabelecimentoParticipanteESSV2Repository>();}
			}
			public static Perlink.Trinks.ProjetoEncontreSeuSalao.Repositories.IGmapsLimitStatusRepository GmapsLimitStatusRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ProjetoEncontreSeuSalao.Repositories.IGmapsLimitStatusRepository>();}
			}
		}
		public static partial class ProjetoEncontreSeuSalao {		
		public static partial class DTO {		
		}
		}
		public static partial class ProjetoEncontreSeuSalao {		
		public static partial class Filters {		
		}
		}
		public static partial class Promocoes {		
		public static partial class Enums {		
		}
		}
		public static partial class Promocoes {		
			public static Perlink.Trinks.Promocoes.Repositories.IPromocaoRepository PromocaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Promocoes.Repositories.IPromocaoRepository>();}
			}
			public static Perlink.Trinks.Promocoes.Repositories.IPromocaoDiaDaSemanaRepository PromocaoDiaDaSemanaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Promocoes.Repositories.IPromocaoDiaDaSemanaRepository>();}
			}
		}
		public static partial class PromocoesOnline {		
			public static Perlink.Trinks.PromocoesOnline.Repositories.IAgendamentoTemporarioRepository AgendamentoTemporarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromocoesOnline.Repositories.IAgendamentoTemporarioRepository>();}
			}
			public static Perlink.Trinks.PromocoesOnline.Repositories.IHorariosDaPromocaoRepository HorariosDaPromocaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromocoesOnline.Repositories.IHorariosDaPromocaoRepository>();}
			}
			public static Perlink.Trinks.PromocoesOnline.Repositories.IPromocaoOnlineRepository PromocaoOnlineRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromocoesOnline.Repositories.IPromocaoOnlineRepository>();}
			}
		}
		public static partial class PromocoesOnline {		
		public static partial class DTO {		
		}
		}
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Repositories.IHistoricoUsoCuponsParceriaRepository HistoricoUsoCuponsParceriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromotoresDoTrinks.Repositories.IHistoricoUsoCuponsParceriaRepository>();}
			}
			public static Perlink.Trinks.PromotoresDoTrinks.Repositories.INovosProfissionaisPromotoresDoTrinksRepository NovosProfissionaisPromotoresDoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromotoresDoTrinks.Repositories.INovosProfissionaisPromotoresDoTrinksRepository>();}
			}
			public static Perlink.Trinks.PromotoresDoTrinks.Repositories.IPromotorDoTrinksRepository PromotorDoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromotoresDoTrinks.Repositories.IPromotorDoTrinksRepository>();}
			}
			public static Perlink.Trinks.PromotoresDoTrinks.Repositories.ISaldoPromotorRepository SaldoPromotorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromotoresDoTrinks.Repositories.ISaldoPromotorRepository>();}
			}
			public static Perlink.Trinks.PromotoresDoTrinks.Repositories.ITransacaoPromotorRepository TransacaoPromotorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.PromotoresDoTrinks.Repositories.ITransacaoPromotorRepository>();}
			}
		}
		public static partial class PromotoresDoTrinks {		
		public static partial class DTO {		
		}
		}
		public static partial class PromotoresDoTrinks {		
		public static partial class Stories {		
		}
		}
		public static partial class RecorrenciaDeAssinatura {		
			public static Perlink.Trinks.RecorrenciaDeAssinatura.Repositories.IAssinaturaRecorrenteRepository AssinaturaRecorrenteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RecorrenciaDeAssinatura.Repositories.IAssinaturaRecorrenteRepository>();}
			}
		}
		public static partial class RecorrenciaDeAssinatura {		
		public static partial class DTO {		
		}
		}
		public static partial class RecorrenciaDeAssinatura {		
		public static partial class Enums {		
		}
		}
		public static partial class RecorrenciaDeAssinatura {		
		public static partial class Filtros {		
		}
		}
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Repositories.IClienteAtendidoRepository ClienteAtendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IClienteAtendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IConsultaRelatorioConsolidadoDiaRepository ConsultaRelatorioConsolidadoDiaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IConsultaRelatorioConsolidadoDiaRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IConsultaRelatorioConsolidadoMesRepository ConsultaRelatorioConsolidadoMesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IConsultaRelatorioConsolidadoMesRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeClienteRepository RankingDeClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeClienteRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeClienteEstendidoRepository RankingDeClienteEstendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeClienteEstendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDePacotesRepository RankingDePacotesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDePacotesRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDePacotesEstendidoRepository RankingDePacotesEstendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDePacotesEstendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeProdutosRepository RankingDeProdutosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeProdutosRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeProdutosEstendidoRepository RankingDeProdutosEstendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeProdutosEstendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeProfissionaisRepository RankingDeProfissionaisRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeProfissionaisRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeProfissionaisEstendidoRepository RankingDeProfissionaisEstendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeProfissionaisEstendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeServicosRepository RankingDeServicosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeServicosRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingDeServicosEstendidoRepository RankingDeServicosEstendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingDeServicosEstendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingEstendidoEstabelecimentosRepository RankingEstendidoEstabelecimentosRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingEstendidoEstabelecimentosRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingItensDePacotesRepository RankingItensDePacotesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingItensDePacotesRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRankingItensDePacotesEstendidoRepository RankingItensDePacotesEstendidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRankingItensDePacotesEstendidoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRelatorioDemonstrativoDeResultadoRepository RelatorioDemonstrativoDeResultadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRelatorioDemonstrativoDeResultadoRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.IRelatorioDemonstrativoDeResultadoReceitaRepository RelatorioDemonstrativoDeResultadoReceitaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.IRelatorioDemonstrativoDeResultadoReceitaRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.ITelaRelatorioRepository TelaRelatorioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.ITelaRelatorioRepository>();}
			}
			public static Perlink.Trinks.Relatorios.Repositories.ITelaRelatorioCategoriaRepository TelaRelatorioCategoriaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Relatorios.Repositories.ITelaRelatorioCategoriaRepository>();}
			}
		}
		public static partial class Relatorios {		
		public static partial class DTO {		
		}
		}
		public static partial class Relatorios {		
		public static partial class DTO {		
		public static partial class DemonstrativoResultado {		
		}
		}
		}
		public static partial class Relatorios {		
		public static partial class DTO {		
		public static partial class RetornoDeClientes {		
		}
		}
		}
		public static partial class Relatorios {		
		public static partial class Enums {		
		}
		}
		public static partial class Relatorios {		
		public static partial class Filtros {		
		}
		}
		public static partial class Relatorios {		
		public static partial class MapaDeCalor {		
		}
		}
		public static partial class RodizioDeProfissionais {		
			public static Perlink.Trinks.RodizioDeProfissionais.Repositories.IColocacaoDoProfissionalRepository ColocacaoDoProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RodizioDeProfissionais.Repositories.IColocacaoDoProfissionalRepository>();}
			}
			public static Perlink.Trinks.RodizioDeProfissionais.Repositories.IHorarioNoRodizioRepository HorarioNoRodizioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RodizioDeProfissionais.Repositories.IHorarioNoRodizioRepository>();}
			}
			public static Perlink.Trinks.RodizioDeProfissionais.Repositories.IMovimentacaoNoRodizioRepository MovimentacaoNoRodizioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RodizioDeProfissionais.Repositories.IMovimentacaoNoRodizioRepository>();}
			}
		}
		public static partial class RodizioDeProfissionais {		
		public static partial class DTO {		
		}
		}
		public static partial class RodizioDeProfissionais {		
		public static partial class Enums {		
		}
		}
		public static partial class RodizioDeProfissionais {		
		public static partial class Factories {		
		}
		}
		public static partial class RodizioDeProfissionais {		
		public static partial class Stories {		
		}
		}
		public static partial class RPS {		
			public static Perlink.Trinks.RPS.Repositories.ICobRpsEmissaoRepository CobRpsEmissaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.ICobRpsEmissaoRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.ICobRpsLoteRepository CobRpsLoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.ICobRpsLoteRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.IConfiguracaoPadraoNFSRepository ConfiguracaoPadraoNFSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.IConfiguracaoPadraoNFSRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.IDadosRPSTransacaoRepository DadosRPSTransacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.IDadosRPSTransacaoRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.IEmissaoRPSRepository EmissaoRPSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.IEmissaoRPSRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.ILoteRPSRepository LoteRPSRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.ILoteRPSRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.IMunicipioPadraoNfseRepository MunicipioPadraoNfseRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.IMunicipioPadraoNfseRepository>();}
			}
			public static Perlink.Trinks.RPS.Repositories.INfseConfiguracaoMunicipioRepository NfseConfiguracaoMunicipioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.RPS.Repositories.INfseConfiguracaoMunicipioRepository>();}
			}
		}
		public static partial class RPS {		
		public static partial class DTO {		
		}
		}
		public static partial class RPS {		
		public static partial class Enums {		
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class ABRASF {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class BHISS {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class DSF {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class EGOVERNE {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class GINFES {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class Ginfes_SJRP {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class GINFES3 {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class LONDRINA_SIGCORP_SIGISS {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class NFCe {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class PAULISTANA {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class SALVADOR_BA {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class SIGCORP_SIGISS {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class SIMPLISS {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class VVISS {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class GeradorDeArquivo {		
		public static partial class WEBISS {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class Integracao {		
		}
		}
		public static partial class RPS {		
		public static partial class Integracao {		
		public static partial class Schemas {		
		}
		}
		}
		public static partial class RPS {		
		public static partial class ManipuladoresDeStreamParaRPS {		
		}
		}
		public static partial class Seguranca {		
			public static Perlink.Trinks.Seguranca.Repositories.IAcoesProibidasMvcRepository AcoesProibidasMvcRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Seguranca.Repositories.IAcoesProibidasMvcRepository>();}
			}
			public static Perlink.Trinks.Seguranca.Repositories.IApiClientRepository ApiClientRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Seguranca.Repositories.IApiClientRepository>();}
			}
			public static Perlink.Trinks.Seguranca.Repositories.IApiRefreshTokenRepository ApiRefreshTokenRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Seguranca.Repositories.IApiRefreshTokenRepository>();}
			}
		}
		public static partial class Seguranca {		
		public static partial class DTO {		
		}
		}
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.ICancelamentoDoPedidoRepository CancelamentoDoPedidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.ICancelamentoDoPedidoRepository>();}
			}
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IEstabelecimentoMovimentacaoEstoquePedidoRepository EstabelecimentoMovimentacaoEstoquePedidoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IEstabelecimentoMovimentacaoEstoquePedidoRepository>();}
			}
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IInformacaoAlteradaRepository InformacaoAlteradaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IInformacaoAlteradaRepository>();}
			}
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IItemDePedidoDeCompraRepository ItemDePedidoDeCompraRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IItemDePedidoDeCompraRepository>();}
			}
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IPedidoDeCompraRepository PedidoDeCompraRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IPedidoDeCompraRepository>();}
			}
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IRegistroDeAlteracaoRepository RegistroDeAlteracaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories.IRegistroDeAlteracaoRepository>();}
			}
		}
		public static partial class SugestoesEPedidosDeCompra {		
		public static partial class DTO {		
		}
		}
		public static partial class SugestoesEPedidosDeCompra {		
		}
		public static partial class SugestoesEPedidosDeCompra {		
		public static partial class Filtros {		
		}
		}
		public static partial class SurveyAppB2B {		
		public static partial class Enums {		
		}
		}
		public static partial class SurveyAppB2B {		
		public static partial class Stories {		
		}
		}
		public static partial class SurveyAppB2B {		
			public static Perlink.Trinks.SurveyAppB2B.Repositories.ISurveyAppProRepository SurveyAppProRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.SurveyAppB2B.Repositories.ISurveyAppProRepository>();}
			}
		}
		public static partial class TesteAB {		
			public static Perlink.Trinks.TesteAB.Repositories.IAmostraRepository AmostraRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TesteAB.Repositories.IAmostraRepository>();}
			}
			public static Perlink.Trinks.TesteAB.Repositories.IGrupoRepository GrupoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TesteAB.Repositories.IGrupoRepository>();}
			}
			public static Perlink.Trinks.TesteAB.Repositories.IMetricaRepository MetricaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TesteAB.Repositories.IMetricaRepository>();}
			}
		}
		public static partial class TesteAB {		
		public static partial class DTO {		
		}
		}
		public static partial class TesteAB {		
		public static partial class Enums {		
		}
		}
		public static partial class TestesAB {		
		public static partial class Enums {		
		}
		}
		public static partial class TestesAB {		
			public static Perlink.Trinks.TestesAB.Repositories.ITesteABAssinaturaRepository TesteABAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TestesAB.Repositories.ITesteABAssinaturaRepository>();}
			}
			public static Perlink.Trinks.TestesAB.Repositories.ITesteABAssinaturaPlanoAssinaturaRepository TesteABAssinaturaPlanoAssinaturaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TestesAB.Repositories.ITesteABAssinaturaPlanoAssinaturaRepository>();}
			}
			public static Perlink.Trinks.TestesAB.Repositories.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository TesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TestesAB.Repositories.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.TestesAB.Repositories.ITesteABWhyTrinksRepository TesteABWhyTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TestesAB.Repositories.ITesteABWhyTrinksRepository>();}
			}
			public static Perlink.Trinks.TestesAB.Repositories.ITesteABWhyTrinksHistoricoRepository TesteABWhyTrinksHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TestesAB.Repositories.ITesteABWhyTrinksHistoricoRepository>();}
			}
		}
		public static partial class TrinksApps {		
			public static Perlink.Trinks.TrinksApps.Repositories.IAplicativoDeAgendamentoRepository AplicativoDeAgendamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksApps.Repositories.IAplicativoDeAgendamentoRepository>();}
			}
			public static Perlink.Trinks.TrinksApps.Repositories.IAplicativoDeAgendamentoFuncionalidadesRepository AplicativoDeAgendamentoFuncionalidadesRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksApps.Repositories.IAplicativoDeAgendamentoFuncionalidadesRepository>();}
			}
			public static Perlink.Trinks.TrinksApps.Repositories.IConfiguracoesAppProfissionalRepository ConfiguracoesAppProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksApps.Repositories.IConfiguracoesAppProfissionalRepository>();}
			}
			public static Perlink.Trinks.TrinksApps.Repositories.IDadosOnboardingOQueProcuraNoTrinksRepository DadosOnboardingOQueProcuraNoTrinksRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksApps.Repositories.IDadosOnboardingOQueProcuraNoTrinksRepository>();}
			}
			public static Perlink.Trinks.TrinksApps.Repositories.IDispositivoComAplicativoRepository DispositivoComAplicativoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksApps.Repositories.IDispositivoComAplicativoRepository>();}
			}
			public static Perlink.Trinks.TrinksApps.Repositories.IFuncionalidadeDoAplicativoDeAgendamentoRepository FuncionalidadeDoAplicativoDeAgendamentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksApps.Repositories.IFuncionalidadeDoAplicativoDeAgendamentoRepository>();}
			}
		}
		public static partial class TrinksApps {		
		public static partial class Dto {		
		}
		}
		public static partial class TrinksApps {		
		public static partial class Enums {		
		}
		}
		public static partial class TrinksApps {		
		public static partial class ObjectValues {		
		}
		}
		public static partial class TrinksAtendimento {		
			public static Perlink.Trinks.TrinksAtendimento.Repositories.IAssuntoFaleConoscoTrinksProfissionalRepository AssuntoFaleConoscoTrinksProfissionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.TrinksAtendimento.Repositories.IAssuntoFaleConoscoTrinksProfissionalRepository>();}
			}
		}
		public static partial class TrinksAtendimento {		
		public static partial class DTOs {		
		}
		}
		public static partial class TrinksAtendimento {		
		public static partial class Stories {		
		}
		}
		public static partial class TrinksPay {		
		public static partial class Stories {		
		}
		}
		public static partial class ValidacaoDeIdentidade {		
		public static partial class DTO {		
		}
		}
		public static partial class ValidacaoDeIdentidade {		
			public static Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IEmailVerificadoRepository EmailVerificadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IEmailVerificadoRepository>();}
			}
			public static Perlink.Trinks.ValidacaoDeIdentidade.Repositories.ITelefoneVerificadoRepository TelefoneVerificadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ValidacaoDeIdentidade.Repositories.ITelefoneVerificadoRepository>();}
			}
			public static Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IVerificacaoDeContaPelaAreaPerlinkRepository VerificacaoDeContaPelaAreaPerlinkRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IVerificacaoDeContaPelaAreaPerlinkRepository>();}
			}
			public static Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IVerificacaoDeIdentidadeRepository VerificacaoDeIdentidadeRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IVerificacaoDeIdentidadeRepository>();}
			}
			public static Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IVerificacaoDeIdentidadeEnvioRepository VerificacaoDeIdentidadeEnvioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.ValidacaoDeIdentidade.Repositories.IVerificacaoDeIdentidadeEnvioRepository>();}
			}
		}
		public static partial class ValidacaoDeIdentidade {		
		public static partial class Enums {		
		}
		}
		public static partial class Vendas {		
			public static Perlink.Trinks.Vendas.Repositories.IComandaRepository ComandaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IComandaRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IItemVendaRepository ItemVendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IItemVendaRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IItemVendaAssinaturaClienteRepository ItemVendaAssinaturaClienteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IItemVendaAssinaturaClienteRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IItemVendaPacoteRepository ItemVendaPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IItemVendaPacoteRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IItemVendaProdutoRepository ItemVendaProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IItemVendaProdutoRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IItemVendaValePresenteRepository ItemVendaValePresenteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IItemVendaValePresenteRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IPreVendaRepository PreVendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IPreVendaRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IPreVendaProdutoRepository PreVendaProdutoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IPreVendaProdutoRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IPreVendaServicoRepository PreVendaServicoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IPreVendaServicoRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IPreVendaHistoricoRepository PreVendaHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IPreVendaHistoricoRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IPreVendaStatusRepository PreVendaStatusRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IPreVendaStatusRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IValePresenteRepository ValePresenteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IValePresenteRepository>();}
			}
			public static Perlink.Trinks.Vendas.Repositories.IVendaRepository VendaRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.Vendas.Repositories.IVendaRepository>();}
			}
		}
		public static partial class Vendas {		
		public static partial class DTO {		
		}
		}
		public static partial class Vendas {		
		public static partial class Enums {		
		}
		}
		public static partial class Vendas {		
		public static partial class Factories {		
		}
		}
		public static partial class Vendas {		
		public static partial class Repositories {		
		public static partial class Filtros {		
		}
		}
		}
		public static partial class VO {		
		}
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Repositories.IAllowedTestEstablishmentsRepository AllowedTestEstablishmentsRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IAllowedTestEstablishmentsRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IAvaliacaoHistoricoRepository AvaliacaoHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IAvaliacaoHistoricoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.ICartaoEstabelecimentoRepository CartaoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.ICartaoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.ICompraCreditoRepository CompraCreditoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.ICompraCreditoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.ICompraRecorrenteEstabelecimentoRepository CompraRecorrenteEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.ICompraRecorrenteEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.ICompraRecorrenteEstabelecimentoHistoricoRepository CompraRecorrenteEstabelecimentoHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.ICompraRecorrenteEstabelecimentoHistoricoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IEstablishmentConfigurationRepository EstablishmentConfigurationRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IEstablishmentConfigurationRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IFranquiaValorRepository FranquiaValorRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IFranquiaValorRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHistoricoCompraAdicionalRepository HistoricoCompraAdicionalRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHistoricoCompraAdicionalRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHistoricoComunicacaoHorarioRepository HistoricoComunicacaoHorarioRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHistoricoComunicacaoHorarioRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHistoricoHorarioTagRepository HistoricoHorarioTagRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHistoricoHorarioTagRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHistoricoSessaoRepository HistoricoSessaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHistoricoSessaoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHistoricoSessaoProcessoRepository HistoricoSessaoProcessoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHistoricoSessaoProcessoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHistoricoSessaoProcessoRegistroRepository HistoricoSessaoProcessoRegistroRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHistoricoSessaoProcessoRegistroRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHorarioComunicacaoRepository HorarioComunicacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHorarioComunicacaoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IHorarioTagRepository HorarioTagRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IHorarioTagRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IMessageTemplateRepository MessageTemplateRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IMessageTemplateRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IMovimentacaoSaldoRepository MovimentacaoSaldoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IMovimentacaoSaldoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IOptOutRepository OptOutRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IOptOutRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IOptOutHistoricoRepository OptOutHistoricoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IOptOutHistoricoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IPacoteCreditoRepository PacoteCreditoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IPacoteCreditoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IPacoteCreditoFormaContratacaoRepository PacoteCreditoFormaContratacaoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IPacoteCreditoFormaContratacaoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IPacotePersonalizadoRepository PacotePersonalizadoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IPacotePersonalizadoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.ISaldoEstabelecimentoRepository SaldoEstabelecimentoRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.ISaldoEstabelecimentoRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.ITipoValorPacoteRepository TipoValorPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.ITipoValorPacoteRepository>();}
			}
			public static Perlink.Trinks.WhatsApp.Repositories.IValorPacoteRepository ValorPacoteRepository  {
				get {return DomainInfrastructure.Domain.Repository<Perlink.Trinks.WhatsApp.Repositories.IValorPacoteRepository>();}
			}
		}
		public static partial class WhatsApp {		
		public static partial class DTO {		
		}
		}
		public static partial class WhatsApp {		
		public static partial class Enums {		
		}
		}
		public static partial class WhatsApp {		
		public static partial class Exceptions {		
		}
		}
		public static partial class WhatsApp {		
		public static partial class Factories {		
		}
		}
		public static partial class WhatsApp {		
		public static partial class Filters {		
		}
		}
		public static partial class WhatsApp {		
		public static partial class Strategies {		
		}
		}
		public static partial class WhyTrinks {		
		public static partial class Enums {		
		}
		}
		public static partial class WhyTrinks {		
		}
		public static partial class Wrapper {		
		}
		public static partial class Agenda {		
			public static Perlink.Trinks.Agenda.Services.IMeusAgendamentosService MeusAgendamentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Agenda.Services.IMeusAgendamentosService>();}
			}		
		}		
		public static partial class Autoatendimento {		
			public static Perlink.Trinks.Autoatendimento.Services.IAutoatendimentoService AutoatendimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Autoatendimento.Services.IAutoatendimentoService>();}
			}		
		}		
		public static partial class Autoatendimento {		
			public static Perlink.Trinks.Autoatendimento.Services.ICheckInService CheckInService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Autoatendimento.Services.ICheckInService>();}
			}		
		}		
		public static partial class Autoatendimento {		
			public static Perlink.Trinks.Autoatendimento.Services.IEstablishmentConfigurationService EstablishmentConfigurationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Autoatendimento.Services.IEstablishmentConfigurationService>();}
			}		
		}		
		public static partial class BaseIBPT {		
			public static Perlink.Trinks.BaseIBPT.Services.IIBPTService IBPTService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.BaseIBPT.Services.IIBPTService>();}
			}		
		}		
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Services.IAgendaDeRecebiveisService AgendaDeRecebiveisService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Belezinha.Services.IAgendaDeRecebiveisService>();}
			}		
		}		
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Services.IBelezinhaStoneService BelezinhaStoneService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Belezinha.Services.IBelezinhaStoneService>();}
			}		
		}		
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Services.ICancelamentoAutomaticoDeTransacoesConnectService CancelamentoAutomaticoDeTransacoesConnectService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Belezinha.Services.ICancelamentoAutomaticoDeTransacoesConnectService>();}
			}		
		}		
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Services.ICredenciamentoBelezinhaService CredenciamentoBelezinhaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Belezinha.Services.ICredenciamentoBelezinhaService>();}
			}		
		}		
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Services.IEstabelecimentoTerminalPosService EstabelecimentoTerminalPosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Belezinha.Services.IEstabelecimentoTerminalPosService>();}
			}		
		}		
		public static partial class Belezinha {		
			public static Perlink.Trinks.Belezinha.Services.IMigracaoDaSiclosParaOConnectService MigracaoDaSiclosParaOConnectService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Belezinha.Services.IMigracaoDaSiclosParaOConnectService>();}
			}		
		}		
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Services.ICashbackService CashbackService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cashback.Services.ICashbackService>();}
			}		
		}		
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Services.IDadosIntegracaoCrmBonusService DadosIntegracaoCrmBonusService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cashback.Services.IDadosIntegracaoCrmBonusService>();}
			}		
		}		
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Services.IEnvioAssincronoCrmBonusService EnvioAssincronoCrmBonusService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cashback.Services.IEnvioAssincronoCrmBonusService>();}
			}		
		}		
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Services.IIdentificacaoClienteCrmBonusService IdentificacaoClienteCrmBonusService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cashback.Services.IIdentificacaoClienteCrmBonusService>();}
			}		
		}		
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Services.IInformacaoBonusCrmBonusService InformacaoBonusCrmBonusService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cashback.Services.IInformacaoBonusCrmBonusService>();}
			}		
		}		
		public static partial class Cashback {		
			public static Perlink.Trinks.Cashback.Services.IProcessamentoBonusCrmBonusService ProcessamentoBonusCrmBonusService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cashback.Services.IProcessamentoBonusCrmBonusService>();}
			}		
		}		
		public static partial class ClientesAnexos {		
			public static Perlink.Trinks.ClientesAnexos.Services.IConsultaDeAnexosService ConsultaDeAnexosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClientesAnexos.Services.IConsultaDeAnexosService>();}
			}		
		}		
		public static partial class ClientesAnexos {		
			public static Perlink.Trinks.ClientesAnexos.Services.IDownloadDeArquivosService DownloadDeArquivosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClientesAnexos.Services.IDownloadDeArquivosService>();}
			}		
		}		
		public static partial class ClientesAnexos {		
			public static Perlink.Trinks.ClientesAnexos.Services.IExclusaoDeAnexosService ExclusaoDeAnexosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClientesAnexos.Services.IExclusaoDeAnexosService>();}
			}		
		}		
		public static partial class ClientesAnexos {		
			public static Perlink.Trinks.ClientesAnexos.Services.IUploadDeArquivosService UploadDeArquivosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClientesAnexos.Services.IUploadDeArquivosService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.ICancelamentoDeAssinaturaClienteService CancelamentoDeAssinaturaClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.ICancelamentoDeAssinaturaClienteService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IConsumoDeAssinaturaService ConsumoDeAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IConsumoDeAssinaturaService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IEnvioDeEmailService EnvioDeEmailService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IEnvioDeEmailService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IMontagemDePlanoService MontagemDePlanoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IMontagemDePlanoService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IPagamentosService PagamentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IPagamentosService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IRelatorioConsumoDasAssinaturasService RelatorioConsumoDasAssinaturasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IRelatorioConsumoDasAssinaturasService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IRelatorioDePlanosService RelatorioDePlanosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IRelatorioDePlanosService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IRelatoriosDaAssinaturaClienteService RelatoriosDaAssinaturaClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IRelatoriosDaAssinaturaClienteService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IRotinasDeManutencaoService RotinasDeManutencaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IRotinasDeManutencaoService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.ISincroniaDePlanoService SincroniaDePlanoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.ISincroniaDePlanoService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IVendaDeAssinaturaService VendaDeAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IVendaDeAssinaturaService>();}
			}		
		}		
		public static partial class ClubeDeAssinaturas {		
			public static Perlink.Trinks.ClubeDeAssinaturas.Services.IVendaOnlineService VendaOnlineService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ClubeDeAssinaturas.Services.IVendaOnlineService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IAdicionalNaAssinaturaService AdicionalNaAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IAdicionalNaAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IAssinaturaService AssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IBeneficioDoPlanoAssinaturaService BeneficioDoPlanoAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IBeneficioDoPlanoAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IBeneficioDoPlanoNoMeuPlanoService BeneficioDoPlanoNoMeuPlanoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IBeneficioDoPlanoNoMeuPlanoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IContaFinanceiraService ContaFinanceiraService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IContaFinanceiraService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IDorDoClienteService DorDoClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IDorDoClienteService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IEscolhaDePlanoParaAssinaturaService EscolhaDePlanoParaAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IEscolhaDePlanoParaAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IEstabelecimentoParceriasTrinksService EstabelecimentoParceriasTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IEstabelecimentoParceriasTrinksService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IExperimentacaoEstabelecimentoService ExperimentacaoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IExperimentacaoEstabelecimentoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IFaturaIntegracaoPagamentoExternoService FaturaIntegracaoPagamentoExternoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IFaturaIntegracaoPagamentoExternoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IFaturaService FaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IFaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IFaturaTrinksService FaturaTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IFaturaTrinksService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IMigracaoDoPlanoDeAssinaturaService MigracaoDoPlanoDeAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IMigracaoDoPlanoDeAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IMotivoDoCancelamentoService MotivoDoCancelamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IMotivoDoCancelamentoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IObservacaoAssinaturaService ObservacaoAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IObservacaoAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IOfertaDeServicoAdicionalMeuPlanoService OfertaDeServicoAdicionalMeuPlanoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IOfertaDeServicoAdicionalMeuPlanoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IOfertaDeServicoAdicionalService OfertaDeServicoAdicionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IOfertaDeServicoAdicionalService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IPagamentoAssincronoService PagamentoAssincronoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IPagamentoAssincronoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IPagamentoDeFaturaService PagamentoDeFaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IPagamentoDeFaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IParceriaTipoTrinksService ParceriaTipoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IParceriaTipoTrinksService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IParceriaTrinksService ParceriaTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IParceriaTrinksService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IPlanoAssinaturaService PlanoAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IPlanoAssinaturaService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IPromocaoPraContaFinanceiraService PromocaoPraContaFinanceiraService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IPromocaoPraContaFinanceiraService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IPromocaoTrinksService PromocaoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IPromocaoTrinksService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IRelatorioFaturamentoService RelatorioFaturamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IRelatorioFaturamentoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IResponsavelAtendimentoService ResponsavelAtendimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IResponsavelAtendimentoService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IServicoTrinksService ServicoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IServicoTrinksService>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Services.IValorDeAdesaoDoAdicionalPorFaixaService ValorDeAdesaoDoAdicionalPorFaixaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cobranca.Services.IValorDeAdesaoDoAdicionalPorFaixaService>();}
			}		
		}		
		public static partial class ComissaoAppB2B {		
			public static Perlink.Trinks.ComissaoAppB2B.Services.IComissaoService ComissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComissaoAppB2B.Services.IComissaoService>();}
			}		
		}		
		public static partial class Compromissos {		
			public static Perlink.Trinks.Compromissos.Services.IConsultaParaRealizarAgendamentoService ConsultaParaRealizarAgendamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Compromissos.Services.IConsultaParaRealizarAgendamentoService>();}
			}		
		}		
		public static partial class Compromissos {		
			public static Perlink.Trinks.Compromissos.Services.IMeusCompromissosService MeusCompromissosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Compromissos.Services.IMeusCompromissosService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IArmazenamentoDeArquivoService ArmazenamentoDeArquivoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IArmazenamentoDeArquivoService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IArtigosPublicadosService ArtigosPublicadosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IArtigosPublicadosService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IDefinicaoDosTopicosDeVotacaoService DefinicaoDosTopicosDeVotacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IDefinicaoDosTopicosDeVotacaoService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IPublicacaoDeArtigosService PublicacaoDeArtigosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IPublicacaoDeArtigosService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IRegistrosDeSugestoesService RegistrosDeSugestoesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IRegistrosDeSugestoesService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IRelatorioDeSugestaoService RelatorioDeSugestaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IRelatorioDeSugestaoService>();}
			}		
		}		
		public static partial class ComunidadeTrinks {		
			public static Perlink.Trinks.ComunidadeTrinks.Services.IVotacaoService VotacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ComunidadeTrinks.Services.IVotacaoService>();}
			}		
		}		
		public static partial class ConciliacaoBancaria {		
			public static Perlink.Trinks.ConciliacaoBancaria.Services.IContasFinanceirasService ContasFinanceirasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ConciliacaoBancaria.Services.IContasFinanceirasService>();}
			}		
		}		
		public static partial class ConciliacaoBancaria {		
			public static Perlink.Trinks.ConciliacaoBancaria.Services.IExportacaoLancamentosFinanceiroService ExportacaoLancamentosFinanceiroService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ConciliacaoBancaria.Services.IExportacaoLancamentosFinanceiroService>();}
			}		
		}		
		public static partial class ConciliacaoBelezinha {		
			public static Perlink.Trinks.ConciliacaoBelezinha.Services.IConciliacaoPosService ConciliacaoPosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ConciliacaoBelezinha.Services.IConciliacaoPosService>();}
			}		
		}		
		public static partial class ConciliacaoBelezinha {		
			public static Perlink.Trinks.ConciliacaoBelezinha.Services.IConsultasNaApiDaSiclosService ConsultasNaApiDaSiclosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ConciliacaoBelezinha.Services.IConsultasNaApiDaSiclosService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IAberturaContaDigitalApplicationService AberturaContaDigitalApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IAberturaContaDigitalApplicationService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IAberturaContaDigitalService AberturaContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IAberturaContaDigitalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IAprovacaoDePagamentosService AprovacaoDePagamentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IAprovacaoDePagamentosService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IArmazenamentoDeArquivoService ArmazenamentoDeArquivoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IArmazenamentoDeArquivoService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IAutenticacaoContaDigitalService AutenticacaoContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IAutenticacaoContaDigitalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IChavePermissaoService ChavePermissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IChavePermissaoService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IChavePixApplicationService ChavePixApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IChavePixApplicationService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IChavePixService ChavePixService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IChavePixService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IConfiguracoesContaDigitalService ConfiguracoesContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IConfiguracoesContaDigitalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IDuvidaFrequenteService DuvidaFrequenteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IDuvidaFrequenteService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IKycApplicationService KycApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IKycApplicationService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IKycService KycService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IKycService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.ILimitePixService LimitePixService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.ILimitePixService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.ILogContaDigitalService LogContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.ILogContaDigitalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IOperadorPermissaoService OperadorPermissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IOperadorPermissaoService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IPagamentoAgendadoFolhaProfissionalService PagamentoAgendadoFolhaProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IPagamentoAgendadoFolhaProfissionalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IPagamentoAgendadoService PagamentoAgendadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IPagamentoAgendadoService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IProcessamentoDeMensagensService ProcessamentoDeMensagensService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IProcessamentoDeMensagensService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IProcessamentoDeTransacoesService ProcessamentoDeTransacoesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IProcessamentoDeTransacoesService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IRegistroOperadorContaDigitalService RegistroOperadorContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IRegistroOperadorContaDigitalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IRegistroPermissaoService RegistroPermissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IRegistroPermissaoService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IRelatorioOperadorContaDigitalService RelatorioOperadorContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IRelatorioOperadorContaDigitalService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.ISaldoExtratoApplicationService SaldoExtratoApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.ISaldoExtratoApplicationService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.ISaldoExtratoService SaldoExtratoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.ISaldoExtratoService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.ITransferenciaApplicationService TransferenciaApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.ITransferenciaApplicationService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.ITransferenciaService TransferenciaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.ITransferenciaService>();}
			}		
		}		
		public static partial class ContaDigital {		
			public static Perlink.Trinks.ContaDigital.Services.IUsuarioContaDigitalService UsuarioContaDigitalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ContaDigital.Services.IUsuarioContaDigitalService>();}
			}		
		}		
		public static partial class Conteudo {		
			public static Perlink.Trinks.Conteudo.Services.IConteudoFotoClienteEstabelecimentoService ConteudoFotoClienteEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Conteudo.Services.IConteudoFotoClienteEstabelecimentoService>();}
			}		
		}		
		public static partial class Conteudo {		
			public static Perlink.Trinks.Conteudo.Services.IConteudoFotoEstabelecimentoService ConteudoFotoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Conteudo.Services.IConteudoFotoEstabelecimentoService>();}
			}		
		}		
		public static partial class Conteudo {		
			public static Perlink.Trinks.Conteudo.Services.IConteudoFotoHorarioService ConteudoFotoHorarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Conteudo.Services.IConteudoFotoHorarioService>();}
			}		
		}		
		public static partial class Conteudo {		
			public static Perlink.Trinks.Conteudo.Services.IConteudoImagemLogoEstabelecimentoService ConteudoImagemLogoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Conteudo.Services.IConteudoImagemLogoEstabelecimentoService>();}
			}		
		}		
		public static partial class Conteudo {		
			public static Perlink.Trinks.Conteudo.Services.IMenuItemService MenuItemService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Conteudo.Services.IMenuItemService>();}
			}		
		}		
		public static partial class Controle {		
			public static Perlink.Trinks.Controle.Services.IPalavraProibidaService PalavraProibidaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Controle.Services.IPalavraProibidaService>();}
			}		
		}		
		public static partial class ControleDeCTAs {		
			public static Perlink.Trinks.ControleDeCTAs.Services.ICtasService CtasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeCTAs.Services.ICtasService>();}
			}		
		}		
		public static partial class ControleDeEntradaESaida {		
			public static Perlink.Trinks.ControleDeEntradaESaida.Services.ILancamentoAporteService LancamentoAporteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeEntradaESaida.Services.ILancamentoAporteService>();}
			}		
		}		
		public static partial class ControleDeEntradaESaida {		
			public static Perlink.Trinks.ControleDeEntradaESaida.Services.ILancamentoService LancamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeEntradaESaida.Services.ILancamentoService>();}
			}		
		}		
		public static partial class ControleDeFotos {		
			public static Perlink.Trinks.ControleDeFotos.Services.IBuscaDeFotosService BuscaDeFotosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeFotos.Services.IBuscaDeFotosService>();}
			}		
		}		
		public static partial class ControleDeFuncionalidades {		
			public static Perlink.Trinks.ControleDeFuncionalidades.Services.IControleDeRecursosService ControleDeRecursosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeFuncionalidades.Services.IControleDeRecursosService>();}
			}		
		}		
		public static partial class ControleDeFuncionalidades {		
			public static Perlink.Trinks.ControleDeFuncionalidades.Services.IDisponibilidadeDeRecursosService DisponibilidadeDeRecursosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeFuncionalidades.Services.IDisponibilidadeDeRecursosService>();}
			}		
		}		
		public static partial class ControleDeFuncionalidades {		
			public static Perlink.Trinks.ControleDeFuncionalidades.Services.ILiberacaoDeRecursoService LiberacaoDeRecursoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeFuncionalidades.Services.ILiberacaoDeRecursoService>();}
			}		
		}		
		public static partial class ControleDeSatisfacao {		
			public static Perlink.Trinks.ControleDeSatisfacao.Services.IAvaliacaoDeSatisfacaoService AvaliacaoDeSatisfacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeSatisfacao.Services.IAvaliacaoDeSatisfacaoService>();}
			}		
		}		
		public static partial class ControleDeSatisfacao {		
			public static Perlink.Trinks.ControleDeSatisfacao.Services.IConsultaDeAvaliacoesDeSatisfacaoService ConsultaDeAvaliacoesDeSatisfacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ControleDeSatisfacao.Services.IConsultaDeAvaliacoesDeSatisfacaoService>();}
			}		
		}		
		public static partial class Cupom {		
			public static Perlink.Trinks.Cupom.Services.ICupomPessoaFisicaService CupomPessoaFisicaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cupom.Services.ICupomPessoaFisicaService>();}
			}		
		}		
		public static partial class Cupom {		
			public static Perlink.Trinks.Cupom.Services.ICupomService CupomService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Cupom.Services.ICupomService>();}
			}		
		}		
		public static partial class Dashboard {		
			public static Perlink.Trinks.Dashboard.Services.IAtendimentosService AtendimentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Dashboard.Services.IAtendimentosService>();}
			}		
		}		
		public static partial class Dashboard {		
			public static Perlink.Trinks.Dashboard.Services.IDadosClienteService DadosClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Dashboard.Services.IDadosClienteService>();}
			}		
		}		
		public static partial class Dashboard {		
			public static Perlink.Trinks.Dashboard.Services.IDadosServicoService DadosServicoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Dashboard.Services.IDadosServicoService>();}
			}		
		}		
		public static partial class Dashboard {		
			public static Perlink.Trinks.Dashboard.Services.IDespesaDiariaPessoaJuridicaService DespesaDiariaPessoaJuridicaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Dashboard.Services.IDespesaDiariaPessoaJuridicaService>();}
			}		
		}		
		public static partial class Dashboard {		
			public static Perlink.Trinks.Dashboard.Services.IReceitaDiariaPessoaJuridicaService ReceitaDiariaPessoaJuridicaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Dashboard.Services.IReceitaDiariaPessoaJuridicaService>();}
			}		
		}		
		public static partial class DebitoParcial {		
			public static Perlink.Trinks.DebitoParcial.Services.IPagamentoDeDividaService PagamentoDeDividaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.DebitoParcial.Services.IPagamentoDeDividaService>();}
			}		
		}		
		public static partial class DebitoParcial {		
			public static Perlink.Trinks.DebitoParcial.Services.IRegistroDeDividaService RegistroDeDividaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.DebitoParcial.Services.IRegistroDeDividaService>();}
			}		
		}		
		public static partial class DebitoParcial {		
			public static Perlink.Trinks.DebitoParcial.Services.IUnificacaoDeDividasService UnificacaoDeDividasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.DebitoParcial.Services.IUnificacaoDeDividasService>();}
			}		
		}		
		public static partial class Despesas {		
			public static Perlink.Trinks.Despesas.Services.ILancamentoCategoriaService LancamentoCategoriaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Despesas.Services.ILancamentoCategoriaService>();}
			}		
		}		
		public static partial class Despesas {		
			public static Perlink.Trinks.Despesas.Services.ILancamentoGrupoService LancamentoGrupoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Despesas.Services.ILancamentoGrupoService>();}
			}		
		}		
		public static partial class Despesas {		
			public static Perlink.Trinks.Despesas.Services.ILancamentoRecorrenciaService LancamentoRecorrenciaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Despesas.Services.ILancamentoRecorrenciaService>();}
			}		
		}		
		public static partial class Despesas {		
			public static Perlink.Trinks.Despesas.Services.ILancamentoService LancamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Despesas.Services.ILancamentoService>();}
			}		
		}		
		public static partial class Despesas {		
			public static Perlink.Trinks.Despesas.Services.IRenovacaoLancamentoService RenovacaoLancamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Despesas.Services.IRenovacaoLancamentoService>();}
			}		
		}		
		public static partial class Encurtador {		
			public static Perlink.Trinks.Encurtador.Services.IEncurtadorDeDadosService EncurtadorDeDadosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Encurtador.Services.IEncurtadorDeDadosService>();}
			}		
		}		
		public static partial class Encurtador {		
			public static Perlink.Trinks.Encurtador.Services.IQRCodeService QRCodeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Encurtador.Services.IQRCodeService>();}
			}		
		}		
		public static partial class EnvioMensagem {		
			public static Perlink.Trinks.EnvioMensagem.Services.IMensagemService MensagemService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EnvioMensagem.Services.IMensagemService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IAvaliacaoDeEstabelecimentoService AvaliacaoDeEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IAvaliacaoDeEstabelecimentoService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConfiguracaoDeAvaliacaoDoEstabelecimentoService ConfiguracaoDeAvaliacaoDoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConfiguracaoDeAvaliacaoDoEstabelecimentoService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConfiguracaoDoSmsLembreteService ConfiguracaoDoSmsLembreteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConfiguracaoDoSmsLembreteService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConsultaDeEstabelecimentosParaOClienteService ConsultaDeEstabelecimentosParaOClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConsultaDeEstabelecimentosParaOClienteService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConsultaDeFotosDeEstabelecimentosService ConsultaDeFotosDeEstabelecimentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConsultaDeFotosDeEstabelecimentosService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConsultaDePacotesParaOClienteService ConsultaDePacotesParaOClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConsultaDePacotesParaOClienteService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConsultaDeProfissionaisParaOClienteService ConsultaDeProfissionaisParaOClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConsultaDeProfissionaisParaOClienteService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IConsultaDeServicosParaOClienteService ConsultaDeServicosParaOClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IConsultaDeServicosParaOClienteService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IDadosDoEstabelecimentoParaOClienteService DadosDoEstabelecimentoParaOClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IDadosDoEstabelecimentoParaOClienteService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IEstabelecimentoComInformacoesConsolidadasService EstabelecimentoComInformacoesConsolidadasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IEstabelecimentoComInformacoesConsolidadasService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IEstabelecimentoFavoritoService EstabelecimentoFavoritoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IEstabelecimentoFavoritoService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IEstabelecimentoProfissionalFavoritoService EstabelecimentoProfissionalFavoritoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IEstabelecimentoProfissionalFavoritoService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IFuncionamentoDeEstabelecimentoService FuncionamentoDeEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IFuncionamentoDeEstabelecimentoService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IGerarPesquisaDeSatisfacaoService GerarPesquisaDeSatisfacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IGerarPesquisaDeSatisfacaoService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.IInformacoesDoUsuarioService InformacoesDoUsuarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.IInformacoesDoUsuarioService>();}
			}		
		}		
		public static partial class Estabelecimentos {		
			public static Perlink.Trinks.Estabelecimentos.Services.INotificacoesParaEstabelecimentosService NotificacoesParaEstabelecimentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Estabelecimentos.Services.INotificacoesParaEstabelecimentosService>();}
			}		
		}		
		public static partial class EstilosVisuais {		
			public static Perlink.Trinks.EstilosVisuais.Services.ITemaCssService TemaCssService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstilosVisuais.Services.ITemaCssService>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IConfiguracaoDeBaixaEstoqueService ConfiguracaoDeBaixaEstoqueService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IConfiguracaoDeBaixaEstoqueService>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IConfiguracaoDoServicoService ConfiguracaoDoServicoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IConfiguracaoDoServicoService>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IEdicaoNoUsoDeProdutoNoHorarioService EdicaoNoUsoDeProdutoNoHorarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IEdicaoNoUsoDeProdutoNoHorarioService>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IItemConfiguradoParaBaixaAutomaticaService ItemConfiguradoParaBaixaAutomaticaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IItemConfiguradoParaBaixaAutomaticaService>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Services.ISincroniaDaBaixaAutomaticaService SincroniaDaBaixaAutomaticaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstoqueComBaixaAutomatica.Services.ISincroniaDaBaixaAutomaticaService>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IUsoDeProdutoNoHorarioService UsoDeProdutoNoHorarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.EstoqueComBaixaAutomatica.Services.IUsoDeProdutoNoHorarioService>();}
			}		
		}		
		public static partial class Extratores {		
			public static Perlink.Trinks.Extratores.Services.IExtratorService ExtratorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Extratores.Services.IExtratorService>();}
			}		
		}		
		public static partial class Facebook {		
			public static Perlink.Trinks.Facebook.Services.IFBEService FBEService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Facebook.Services.IFBEService>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.ICalculoDePontosService CalculoDePontosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.ICalculoDePontosService>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.IInformacoesParaOClienteService InformacoesParaOClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.IInformacoesParaOClienteService>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.IMovimentacaoDePontosSevice MovimentacaoDePontosSevice  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.IMovimentacaoDePontosSevice>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.IPontoGanhoService PontoGanhoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.IPontoGanhoService>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.IProgramaDeFidelidadeService ProgramaDeFidelidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.IProgramaDeFidelidadeService>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.IResgateDePontosDeFidelidadeService ResgateDePontosDeFidelidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.IResgateDePontosDeFidelidadeService>();}
			}		
		}		
		public static partial class Fidelidade {		
			public static Perlink.Trinks.Fidelidade.Services.ITransferenciaDePontosService TransferenciaDePontosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fidelidade.Services.ITransferenciaDePontosService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IAberturaFechamentoCaixaService AberturaFechamentoCaixaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IAberturaFechamentoCaixaService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ICalculoComissaoService CalculoComissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ICalculoComissaoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ICheckoutService CheckoutService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ICheckoutService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IComissaoService ComissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IComissaoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IConsultaControleDeCaixaService ConsultaControleDeCaixaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IConsultaControleDeCaixaService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IConsultaDeComissoesService ConsultaDeComissoesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IConsultaDeComissoesService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IControleDeCaixaService ControleDeCaixaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IControleDeCaixaService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ICupomBelezinhaService CupomBelezinhaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ICupomBelezinhaService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IDescontoPersonalizadoService DescontoPersonalizadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IDescontoPersonalizadoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IDetalhamentoDePagamentosAoProfissionalService DetalhamentoDePagamentosAoProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IDetalhamentoDePagamentosAoProfissionalService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IEstornoPagamentoDeProfissionaisService EstornoPagamentoDeProfissionaisService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IEstornoPagamentoDeProfissionaisService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IFechamentoFolhaMesService FechamentoFolhaMesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IFechamentoFolhaMesService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IFormaPagamentoService FormaPagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IFormaPagamentoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IGorjetaService GorjetaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IGorjetaService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IHistoricoPagamentosProfissionalService HistoricoPagamentosProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IHistoricoPagamentosProfissionalService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ILancamentoDeAntecipacaoService LancamentoDeAntecipacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ILancamentoDeAntecipacaoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IMotivoDescontoService MotivoDescontoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IMotivoDescontoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IPagamentoComissaoComDescontosService PagamentoComissaoComDescontosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IPagamentoComissaoComDescontosService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IPagamentoDeProfissionaisService PagamentoDeProfissionaisService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IPagamentoDeProfissionaisService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IRelatorioComissaoService RelatorioComissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IRelatorioComissaoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IRelatorioCreditoClienteService RelatorioCreditoClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IRelatorioCreditoClienteService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IRelatorioPagamentoProfissionalService RelatorioPagamentoProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IRelatorioPagamentoProfissionalService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IRelatoriosFinanceiroService RelatoriosFinanceiroService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IRelatoriosFinanceiroService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ISangriaService SangriaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ISangriaService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ISelecaoDeProfissionaisParaPagamentoService SelecaoDeProfissionaisParaPagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ISelecaoDeProfissionaisParaPagamentoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IServicoDescontoPersonalizadoService ServicoDescontoPersonalizadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IServicoDescontoPersonalizadoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ISplitService SplitService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ISplitService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ISubadquirenteService SubadquirenteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ISubadquirenteService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ITransacaoFormaPagamentoService TransacaoFormaPagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ITransacaoFormaPagamentoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ITransacaoPOSService TransacaoPOSService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ITransacaoPOSService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ITransacaoPosWebhookRequestService TransacaoPosWebhookRequestService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ITransacaoPosWebhookRequestService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.ITransacaoService TransacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.ITransacaoService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IValorDeComissaoAReceberService ValorDeComissaoAReceberService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IValorDeComissaoAReceberService>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Services.IVendasPeloHotsiteService VendasPeloHotsiteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Financeiro.Services.IVendasPeloHotsiteService>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Services.IArmazenamentoDeAssinaturasDigitaisService ArmazenamentoDeAssinaturasDigitaisService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Formulario.Services.IArmazenamentoDeAssinaturasDigitaisService>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Services.IControleVersionadoDosFormulariosService ControleVersionadoDosFormulariosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Formulario.Services.IControleVersionadoDosFormulariosService>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Services.IFormularioRespondidoService FormularioRespondidoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Formulario.Services.IFormularioRespondidoService>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Services.IPermissoesNosFormulariosService PermissoesNosFormulariosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Formulario.Services.IPermissoesNosFormulariosService>();}
			}		
		}		
		public static partial class Fotos {		
			public static Perlink.Trinks.Fotos.Services.IFotoPortalService FotoPortalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fotos.Services.IFotoPortalService>();}
			}		
		}		
		public static partial class Fotos {		
			public static Perlink.Trinks.Fotos.Services.IFotoService FotoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fotos.Services.IFotoService>();}
			}		
		}		
		public static partial class Fotos {		
			public static Perlink.Trinks.Fotos.Services.IImagensService ImagensService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fotos.Services.IImagensService>();}
			}		
		}		
		public static partial class Fotos {		
			public static Perlink.Trinks.Fotos.Services.IPoliticaDeControleDeFotos PoliticaDeControleDeFotos  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Fotos.Services.IPoliticaDeControleDeFotos>();}
			}		
		}		
		public static partial class Google {		
			public static Perlink.Trinks.Google.Services.IGoogleReserveConversionTrackingService GoogleReserveConversionTrackingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Google.Services.IGoogleReserveConversionTrackingService>();}
			}		
		}		
		public static partial class Google {		
			public static Perlink.Trinks.Google.Services.IGoogleReserveFeedService GoogleReserveFeedService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Google.Services.IGoogleReserveFeedService>();}
			}		
		}		
		public static partial class GyraMais {		
			public static Perlink.Trinks.GyraMais.Services.IGyraOptinService GyraOptinService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.GyraMais.Services.IGyraOptinService>();}
			}		
		}		
		public static partial class Identity {		
			public static Perlink.Trinks.Identity.Services.IGeracaoDeTokenDeAPIService GeracaoDeTokenDeAPIService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Identity.Services.IGeracaoDeTokenDeAPIService>();}
			}		
		}		
		public static partial class Importacao {		
			public static Perlink.Trinks.Importacao.Services.IImportacaoService ImportacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Importacao.Services.IImportacaoService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.IAgendamentoDeImportacaoService AgendamentoDeImportacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.IAgendamentoDeImportacaoService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.IArmazenamentoDeArquivoService ArmazenamentoDeArquivoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.IArmazenamentoDeArquivoService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.IExportacaoDeDadosService ExportacaoDeDadosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.IExportacaoDeDadosService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.IImportacaoDeArquivoService ImportacaoDeArquivoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.IImportacaoDeArquivoService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.IModeloDeImportacaoService ModeloDeImportacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.IModeloDeImportacaoService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.INotificacaoDaImportacaoService NotificacaoDaImportacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.INotificacaoDaImportacaoService>();}
			}		
		}		
		public static partial class ImportacaoDeDados {		
			public static Perlink.Trinks.ImportacaoDeDados.Services.IOpcoesDaImportacaoService OpcoesDaImportacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ImportacaoDeDados.Services.IOpcoesDaImportacaoService>();}
			}		
		}		
		public static partial class IntegracaoComOutrosSistemas {		
		public static partial class IntegracaoComTrinks {		
			public static Perlink.Trinks.IntegracaoComOutrosSistemas.IntegracaoComTrinks.Services.INotificacaoDeEventoParaIntegracaoApartirDoTrinksService NotificacaoDeEventoParaIntegracaoApartirDoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.IntegracaoComOutrosSistemas.IntegracaoComTrinks.Services.INotificacaoDeEventoParaIntegracaoApartirDoTrinksService>();}
			}		
		}		
		}		
		public static partial class IntegracaoComOutrosSistemas {		
			public static Perlink.Trinks.IntegracaoComOutrosSistemas.Services.INotificacaoDeEventoParaIntegracaoService NotificacaoDeEventoParaIntegracaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.IntegracaoComOutrosSistemas.Services.INotificacaoDeEventoParaIntegracaoService>();}
			}		
		}		
		public static partial class InternoProduto {		
			public static Perlink.Trinks.InternoProduto.Services.IFeedbackPesquisaRapidaService FeedbackPesquisaRapidaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.InternoProduto.Services.IFeedbackPesquisaRapidaService>();}
			}		
		}		
		public static partial class InternoProduto {		
			public static Perlink.Trinks.InternoProduto.Services.IQuestionarioProdutoRespondidoService QuestionarioProdutoRespondidoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.InternoProduto.Services.IQuestionarioProdutoRespondidoService>();}
			}		
		}		
		public static partial class LinksDePagamento {		
			public static Perlink.Trinks.LinksDePagamento.Services.ICancelamentoLinkPagamentoService CancelamentoLinkPagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.LinksDePagamento.Services.ICancelamentoLinkPagamentoService>();}
			}		
		}		
		public static partial class LinksDePagamento {		
			public static Perlink.Trinks.LinksDePagamento.Services.IDisparoMensagensDeProcessamentoService DisparoMensagensDeProcessamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.LinksDePagamento.Services.IDisparoMensagensDeProcessamentoService>();}
			}		
		}		
		public static partial class LinksDePagamento {		
			public static Perlink.Trinks.LinksDePagamento.Services.IEnvioEmailService EnvioEmailService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.LinksDePagamento.Services.IEnvioEmailService>();}
			}		
		}		
		public static partial class LinksDePagamento {		
			public static Perlink.Trinks.LinksDePagamento.Services.ILinkDePagamentoService LinkDePagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.LinksDePagamento.Services.ILinkDePagamentoService>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Services.ILinkDePagamentoNoTrinksService LinkDePagamentoNoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.LinksDePagamentoNoTrinks.Services.ILinkDePagamentoNoTrinksService>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Services.IProcessamentoDeMensagensService ProcessamentoDeMensagensService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.LinksDePagamentoNoTrinks.Services.IProcessamentoDeMensagensService>();}
			}		
		}		
		public static partial class Localizacoes {		
			public static Perlink.Trinks.Localizacoes.Services.IBuscaDeEnderecosService BuscaDeEnderecosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Localizacoes.Services.IBuscaDeEnderecosService>();}
			}		
		}		
		public static partial class Localizacoes {		
			public static Perlink.Trinks.Localizacoes.Services.IGoogleMapsGeolocationService GoogleMapsGeolocationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Localizacoes.Services.IGoogleMapsGeolocationService>();}
			}		
		}		
		public static partial class Marcadores {		
			public static Perlink.Trinks.Marcadores.Services.IConsultaDeEtiquetasService ConsultaDeEtiquetasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marcadores.Services.IConsultaDeEtiquetasService>();}
			}		
		}		
		public static partial class Marcadores {		
			public static Perlink.Trinks.Marcadores.Services.ICorEtiquetaService CorEtiquetaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marcadores.Services.ICorEtiquetaService>();}
			}		
		}		
		public static partial class Marcadores {		
			public static Perlink.Trinks.Marcadores.Services.IManterEtiquetasService ManterEtiquetasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marcadores.Services.IManterEtiquetasService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IAvaliacaoDoTrinksService AvaliacaoDoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IAvaliacaoDoTrinksService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IConfiguracaoDeConviteDeRetornoService ConfiguracaoDeConviteDeRetornoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IConfiguracaoDeConviteDeRetornoService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IConfiguracaoSmsAniversariantesService ConfiguracaoSmsAniversariantesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IConfiguracaoSmsAniversariantesService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IEmailsRejeitadosService EmailsRejeitadosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IEmailsRejeitadosService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IImagemEmailMarketingService ImagemEmailMarketingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IImagemEmailMarketingService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IMarketingCampanhaDoConviteDeRetornoService MarketingCampanhaDoConviteDeRetornoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IMarketingCampanhaDoConviteDeRetornoService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IMarketingCampanhaHistoricoService MarketingCampanhaHistoricoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IMarketingCampanhaHistoricoService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IMarketingCampanhaService MarketingCampanhaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IMarketingCampanhaService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IMarketingEnvioClienteService MarketingEnvioClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IMarketingEnvioClienteService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IMarketingService MarketingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IMarketingService>();}
			}		
		}		
		public static partial class Marketing {		
			public static Perlink.Trinks.Marketing.Services.IMarketingSMSService MarketingSMSService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Marketing.Services.IMarketingSMSService>();}
			}		
		}		
		public static partial class MarketingInterno {		
			public static Perlink.Trinks.MarketingInterno.Services.IDadosMarketingService DadosMarketingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.MarketingInterno.Services.IDadosMarketingService>();}
			}		
		}		
		public static partial class MensagemEmTela {		
			public static Perlink.Trinks.MensagemEmTela.Services.IMensagemAvisoService MensagemAvisoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.MensagemEmTela.Services.IMensagemAvisoService>();}
			}		
		}		
		public static partial class Metricas {		
			public static Perlink.Trinks.Metricas.Services.IRegistroMetricaService RegistroMetricaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Metricas.Services.IRegistroMetricaService>();}
			}		
		}		
		public static partial class NotaFiscalDoConsumidor {		
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Services.IImpressaoDeNotaService ImpressaoDeNotaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotaFiscalDoConsumidor.Services.IImpressaoDeNotaService>();}
			}		
		}		
		public static partial class NotaFiscalDoConsumidor {		
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Services.IInformacoesDeNFCService InformacoesDeNFCService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotaFiscalDoConsumidor.Services.IInformacoesDeNFCService>();}
			}		
		}		
		public static partial class NotaFiscalDoConsumidor {		
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Services.IIntegradorDeDadosDeNFService IntegradorDeDadosDeNFService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotaFiscalDoConsumidor.Services.IIntegradorDeDadosDeNFService>();}
			}		
		}		
		public static partial class NotaFiscalDoConsumidor {		
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Services.INfcSituacaoTributariaService NfcSituacaoTributariaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotaFiscalDoConsumidor.Services.INfcSituacaoTributariaService>();}
			}		
		}		
		public static partial class NotaFiscalDoConsumidor {		
			public static Perlink.Trinks.NotaFiscalDoConsumidor.Services.INotaNFCService NotaNFCService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotaFiscalDoConsumidor.Services.INotaNFCService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IAppB2CFcmServiceLogger AppB2CFcmServiceLogger  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IAppB2CFcmServiceLogger>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IAppB2CFirebaseCredentialsRepository AppB2CFirebaseCredentialsRepository  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IAppB2CFirebaseCredentialsRepository>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.ICampanhaPushService CampanhaPushService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.ICampanhaPushService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IContaQtdNotificacoesNovasService ContaQtdNotificacoesNovasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IContaQtdNotificacoesNovasService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IDispositivoService DispositivoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IDispositivoService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IEnvioDeNotificacoesService EnvioDeNotificacoesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IEnvioDeNotificacoesService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IFTPCopiaArquivoPortabilidadeService FTPCopiaArquivoPortabilidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IFTPCopiaArquivoPortabilidadeService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IManterInscricaoEmNotificacaoService ManterInscricaoEmNotificacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IManterInscricaoEmNotificacaoService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IMinhasNotificacoesService MinhasNotificacoesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IMinhasNotificacoesService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.INotificacaoDeAgendePeloTrinksService NotificacaoDeAgendePeloTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.INotificacaoDeAgendePeloTrinksService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.INotificacaoDoTrinksService NotificacaoDoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.INotificacaoDoTrinksService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.INotificacaoPushService NotificacaoPushService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.INotificacaoPushService>();}
			}		
		}		
		public static partial class Notificacoes {		
			public static Perlink.Trinks.Notificacoes.Services.IRegistroNotificacaoService RegistroNotificacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Notificacoes.Services.IRegistroNotificacaoService>();}
			}		
		}		
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Services.IAppProFcmServiceLogger AppProFcmServiceLogger  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotificacoesApps.Services.IAppProFcmServiceLogger>();}
			}		
		}		
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Services.ICanalDaNotificaoAppProService CanalDaNotificaoAppProService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotificacoesApps.Services.ICanalDaNotificaoAppProService>();}
			}		
		}		
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Services.IDisparoMensagensDeNotificacaoService DisparoMensagensDeNotificacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotificacoesApps.Services.IDisparoMensagensDeNotificacaoService>();}
			}		
		}		
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Services.IGeracaoDeMensagensDeNotificacaoService GeracaoDeMensagensDeNotificacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotificacoesApps.Services.IGeracaoDeMensagensDeNotificacaoService>();}
			}		
		}		
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Services.INotificacaoAppProService NotificacaoAppProService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotificacoesApps.Services.INotificacaoAppProService>();}
			}		
		}		
		public static partial class NotificacoesApps {		
			public static Perlink.Trinks.NotificacoesApps.Services.IPreferenciaDeNotificacaoAppProService PreferenciaDeNotificacaoAppProService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.NotificacoesApps.Services.IPreferenciaDeNotificacaoAppProService>();}
			}		
		}		
		public static partial class Novidades {		
			public static Perlink.Trinks.Novidades.Services.IConfiguracaoDeStoriesService ConfiguracaoDeStoriesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Novidades.Services.IConfiguracaoDeStoriesService>();}
			}		
		}		
		public static partial class Novidades {		
			public static Perlink.Trinks.Novidades.Services.IImagemNovidadeService ImagemNovidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Novidades.Services.IImagemNovidadeService>();}
			}		
		}		
		public static partial class Novidades {		
			public static Perlink.Trinks.Novidades.Services.INovidadeService NovidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Novidades.Services.INovidadeService>();}
			}		
		}		
		public static partial class Onboardings {		
			public static Perlink.Trinks.Onboardings.Services.IRastreioTarefaService RastreioTarefaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Onboardings.Services.IRastreioTarefaService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IConfiguracaoDePacotesPersonalizadosService ConfiguracaoDePacotesPersonalizadosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IConfiguracaoDePacotesPersonalizadosService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IDadosDeConsumoDePacoteService DadosDeConsumoDePacoteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IDadosDeConsumoDePacoteService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IEncerramentoDePacotesVendidosService EncerramentoDePacotesVendidosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IEncerramentoDePacotesVendidosService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IPacoteClienteService PacoteClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IPacoteClienteService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IPacoteService PacoteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IPacoteService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IRelatorioConsumoDePacotesService RelatorioConsumoDePacotesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IRelatorioConsumoDePacotesService>();}
			}		
		}		
		public static partial class Pacotes {		
			public static Perlink.Trinks.Pacotes.Services.IUtilizacaoDePacotesService UtilizacaoDePacotesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pacotes.Services.IUtilizacaoDePacotesService>();}
			}		
		}		
		public static partial class PagamentoAntecipadoHotsite {		
			public static Perlink.Trinks.PagamentoAntecipadoHotsite.Services.ICancelarPagamentoAntecipadoHotsiteService CancelarPagamentoAntecipadoHotsiteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentoAntecipadoHotsite.Services.ICancelarPagamentoAntecipadoHotsiteService>();}
			}		
		}		
		public static partial class PagamentoAntecipadoHotsite {		
			public static Perlink.Trinks.PagamentoAntecipadoHotsite.Services.IConfigurarPagamentoAntecipadoNoHotsiteService ConfigurarPagamentoAntecipadoNoHotsiteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentoAntecipadoHotsite.Services.IConfigurarPagamentoAntecipadoNoHotsiteService>();}
			}		
		}		
		public static partial class PagamentoAntecipadoHotsite {		
			public static Perlink.Trinks.PagamentoAntecipadoHotsite.Services.IGerenciarServicosComPagamentoAntecipadoHotsiteService GerenciarServicosComPagamentoAntecipadoHotsiteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentoAntecipadoHotsite.Services.IGerenciarServicosComPagamentoAntecipadoHotsiteService>();}
			}		
		}		
		public static partial class PagamentoAntecipadoHotsite {		
			public static Perlink.Trinks.PagamentoAntecipadoHotsite.Services.IRealizarPagamentoAntecipadoHotsiteService RealizarPagamentoAntecipadoHotsiteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentoAntecipadoHotsite.Services.IRealizarPagamentoAntecipadoHotsiteService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IAntecipacoesApplicationService AntecipacoesApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IAntecipacoesApplicationService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.ICartaoApplicationService CartaoApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.ICartaoApplicationService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.ICompradorApplicationService CompradorApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.ICompradorApplicationService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IDisparoMensagensConsumoLimitePagamentoService DisparoMensagensConsumoLimitePagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IDisparoMensagensConsumoLimitePagamentoService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IEnvioEmailService EnvioEmailService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IEnvioEmailService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.ILimiteRecebedorService LimiteRecebedorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.ILimiteRecebedorService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IPagamentosApplicationService PagamentosApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IPagamentosApplicationService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IPagamentosService PagamentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IPagamentosService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IProcessamentoMensagensConsumoLimitePagamentoService ProcessamentoMensagensConsumoLimitePagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IProcessamentoMensagensConsumoLimitePagamentoService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IRecebedorApplicationService RecebedorApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IRecebedorApplicationService>();}
			}		
		}		
		public static partial class Pagamentos {		
			public static Perlink.Trinks.Pagamentos.Services.IRecebiveisApplicationService RecebiveisApplicationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pagamentos.Services.IRecebiveisApplicationService>();}
			}		
		}		
		public static partial class PagamentosAntecipados {		
			public static Perlink.Trinks.PagamentosAntecipados.Services.ICancelamentoDePagamentoOnlineAntecipadoService CancelamentoDePagamentoOnlineAntecipadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosAntecipados.Services.ICancelamentoDePagamentoOnlineAntecipadoService>();}
			}		
		}		
		public static partial class PagamentosAntecipados {		
			public static Perlink.Trinks.PagamentosAntecipados.Services.IFechamentoDeContaPagoAntecipadaService FechamentoDeContaPagoAntecipadaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosAntecipados.Services.IFechamentoDeContaPagoAntecipadaService>();}
			}		
		}		
		public static partial class PagamentosAntecipados {		
			public static Perlink.Trinks.PagamentosAntecipados.Services.INotificacaoDePagamentoAntecipadoOnlineService NotificacaoDePagamentoAntecipadoOnlineService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosAntecipados.Services.INotificacaoDePagamentoAntecipadoOnlineService>();}
			}		
		}		
		public static partial class PagamentosAntecipados {		
			public static Perlink.Trinks.PagamentosAntecipados.Services.IPagamentoOnlineAntecipadoService PagamentoOnlineAntecipadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosAntecipados.Services.IPagamentoOnlineAntecipadoService>();}
			}		
		}		
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IAntecipacoesAgendadasService AntecipacoesAgendadasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IAntecipacoesAgendadasService>();}
			}		
		}		
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IFuncionalidadeVendaOnlineService FuncionalidadeVendaOnlineService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IFuncionalidadeVendaOnlineService>();}
			}		
		}		
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IPagamentoOnlineNoTrinksService PagamentoOnlineNoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IPagamentoOnlineNoTrinksService>();}
			}		
		}		
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IRelatorioDeRecebiveisService RelatorioDeRecebiveisService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosOnlineNoTrinks.Services.IRelatorioDeRecebiveisService>();}
			}		
		}		
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Services.ISolicitacaoDeAntecipacoesService SolicitacaoDeAntecipacoesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosOnlineNoTrinks.Services.ISolicitacaoDeAntecipacoesService>();}
			}		
		}		
		public static partial class PagamentosOnlineNoTrinks {		
			public static Perlink.Trinks.PagamentosOnlineNoTrinks.Services.ITaxasService TaxasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PagamentosOnlineNoTrinks.Services.ITaxasService>();}
			}		
		}		
		public static partial class Permissoes {		
			public static Perlink.Trinks.Permissoes.Services.IPermissoesAcessoService PermissoesAcessoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Permissoes.Services.IPermissoesAcessoService>();}
			}		
		}		
		public static partial class Permissoes {		
			public static Perlink.Trinks.Permissoes.Services.IUsuarioPerfilService UsuarioPerfilService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Permissoes.Services.IUsuarioPerfilService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IAgendaPorClienteService AgendaPorClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IAgendaPorClienteService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IAgendaService AgendaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IAgendaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IAlteracaoCadastroPessoaService AlteracaoCadastroPessoaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IAlteracaoCadastroPessoaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IAtivacaoPosNoEstabelecimentoService AtivacaoPosNoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IAtivacaoPosNoEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IBuscaService BuscaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IBuscaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ICacheLocalidadeService CacheLocalidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ICacheLocalidadeService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ICadastroEstabelecimentoService CadastroEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ICadastroEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ICategoriaPortalServicoService CategoriaPortalServicoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ICategoriaPortalServicoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IClienteAreaPerlinkService ClienteAreaPerlinkService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IClienteAreaPerlinkService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IClienteEstabelecimentoService ClienteEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IClienteEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IClienteService ClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IClienteService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ICompartilhamentoNaRedeService CompartilhamentoNaRedeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ICompartilhamentoNaRedeService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IContaService ContaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IContaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IControleDeSaldoSMSLembreteService ControleDeSaldoSMSLembreteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IControleDeSaldoSMSLembreteService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ICriarEEditarDePermissaoService CriarEEditarDePermissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ICriarEEditarDePermissaoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoClienteEstabelecimentoService DadosCadastraisDoClienteEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoClienteEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoEstabelecimentoProfissionalService DadosCadastraisDoEstabelecimentoProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoEstabelecimentoProfissionalService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoServicoEstabelecimentoService DadosCadastraisDoServicoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoServicoEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoUsuarioService DadosCadastraisDoUsuarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosCadastraisDoUsuarioService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosDeAgendamentosDosEstabelecimentosService DadosDeAgendamentosDosEstabelecimentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosDeAgendamentosDosEstabelecimentosService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosDeHistoricoDoClienteService DadosDeHistoricoDoClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosDeHistoricoDoClienteService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosDeRelacaoProfissionalService DadosDeRelacaoProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosDeRelacaoProfissionalService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDadosDeServicoEComissaoDosProfissionaisService DadosDeServicoEComissaoDosProfissionaisService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDadosDeServicoEComissaoDosProfissionaisService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDataEspecialService DataEspecialService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDataEspecialService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDesassociarUnidadeDoModeloService DesassociarUnidadeDoModeloService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDesassociarUnidadeDoModeloService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IDisponibilidadeService DisponibilidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IDisponibilidadeService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEnderecoService EnderecoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEnderecoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEnvioEmailService EnvioEmailService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEnvioEmailService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoConfiguracaoComissaoService EstabelecimentoConfiguracaoComissaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoConfiguracaoComissaoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoConfiguracaoGeralService EstabelecimentoConfiguracaoGeralService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoConfiguracaoGeralService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoConfiguracaoPosService EstabelecimentoConfiguracaoPosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoConfiguracaoPosService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoEhResponsavelFinanceiroService EstabelecimentoEhResponsavelFinanceiroService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoEhResponsavelFinanceiroService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoFabricanteProdutoService EstabelecimentoFabricanteProdutoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoFabricanteProdutoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoFormaPagamentoService EstabelecimentoFormaPagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoFormaPagamentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoHorarioEspecialFuncionamentoService EstabelecimentoHorarioEspecialFuncionamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoHorarioEspecialFuncionamentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoMovimentacaoEstoqueService EstabelecimentoMovimentacaoEstoqueService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoMovimentacaoEstoqueService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoPreCadastroService EstabelecimentoPreCadastroService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoPreCadastroService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoProdutoService EstabelecimentoProdutoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoProdutoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoProfissionalService EstabelecimentoProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoProfissionalService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoProfissionalServicoService EstabelecimentoProfissionalServicoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoProfissionalServicoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoService EstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoSolicitacaoAparecerBuscaService EstabelecimentoSolicitacaoAparecerBuscaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoSolicitacaoAparecerBuscaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoTemplateService EstabelecimentoTemplateService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoTemplateService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IFluxoDeCadastroEstabelecimentoService FluxoDeCadastroEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IFluxoDeCadastroEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IEstabelecimentoFornecedorService EstabelecimentoFornecedorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IEstabelecimentoFornecedorService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IFotoDeClienteEstabelecimentoService FotoDeClienteEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IFotoDeClienteEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IFotoDePessoasService FotoDePessoasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IFotoDePessoasService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IFotoDeServicoRealizadoEmHorarioService FotoDeServicoRealizadoEmHorarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IFotoDeServicoRealizadoEmHorarioService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IFotoService FotoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IFotoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IFranquiaService FranquiaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IFranquiaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IGeradorDeIdentificacaoDeEstabelecimentoNaFranquiaService GeradorDeIdentificacaoDeEstabelecimentoNaFranquiaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IGeradorDeIdentificacaoDeEstabelecimentoNaFranquiaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IHistoricoClienteService HistoricoClienteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IHistoricoClienteService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IHorarioService HorarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IHorarioService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IHotsiteEstabelecimentoService HotsiteEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IHotsiteEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IMidiaSocialService MidiaSocialService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IMidiaSocialService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.INotificacaoEstabelecimentoService NotificacaoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.INotificacaoEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.INotificacoesDoAgendamentoService NotificacoesDoAgendamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.INotificacoesDoAgendamentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IPATService PATService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IPATService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IPeriodoAusenciaService PeriodoAusenciaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IPeriodoAusenciaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IPessoaJuridicaConfiguracaoNFeService PessoaJuridicaConfiguracaoNFeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IPessoaJuridicaConfiguracaoNFeService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IPessoaService PessoaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IPessoaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IPoliticaDeControleDeFotosDeServicoRealizadoEmHorario PoliticaDeControleDeFotosDeServicoRealizadoEmHorario  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IPoliticaDeControleDeFotosDeServicoRealizadoEmHorario>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IPoliticaDeControleDeFotosDoClienteEstabelecimento PoliticaDeControleDeFotosDoClienteEstabelecimento  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IPoliticaDeControleDeFotosDoClienteEstabelecimento>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IProfissionalService ProfissionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IProfissionalService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IRecorrenciaHorarioService RecorrenciaHorarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IRecorrenciaHorarioService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IRelatorioDePermissoesDaUnidadesService RelatorioDePermissoesDaUnidadesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IRelatorioDePermissoesDaUnidadesService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IRelatoriosDePermissoesDaUnidadeService RelatoriosDePermissoesDaUnidadeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IRelatoriosDePermissoesDaUnidadeService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IRelatorioService RelatorioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IRelatorioService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IRotinasService RotinasService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IRotinasService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IServicoCategoriaEstabelecimentoService ServicoCategoriaEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IServicoCategoriaEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IServicoCategoriaService ServicoCategoriaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IServicoCategoriaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IServicoEstabelecimentoService ServicoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IServicoEstabelecimentoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IServicoService ServicoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IServicoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IServicoSinonimoService ServicoSinonimoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IServicoSinonimoService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ISincroniaEstabelecimentoModeloService SincroniaEstabelecimentoModeloService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ISincroniaEstabelecimentoModeloService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ISincronizacaoEntreEstabelecimentosModelosFilaService SincronizacaoEntreEstabelecimentosModelosFilaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ISincronizacaoEntreEstabelecimentosModelosFilaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ISincronizacaoEstabelecimentosFilaService SincronizacaoEstabelecimentosFilaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ISincronizacaoEstabelecimentosFilaService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ITelefoneInternacionalService TelefoneInternacionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ITelefoneInternacionalService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ITelefoneService TelefoneService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ITelefoneService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.ITemplateService TemplateService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.ITemplateService>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Services.IUnificacaoService UnificacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Pessoas.Services.IUnificacaoService>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Services.IConsultaDeInventariosService ConsultaDeInventariosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProdutoEstoque.Services.IConsultaDeInventariosService>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Services.IConsultaPropriedadesDeProdutosService ConsultaPropriedadesDeProdutosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProdutoEstoque.Services.IConsultaPropriedadesDeProdutosService>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Services.IEstabelecimentoProdutoCategoriaService EstabelecimentoProdutoCategoriaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProdutoEstoque.Services.IEstabelecimentoProdutoCategoriaService>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Services.IInventarioService InventarioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProdutoEstoque.Services.IInventarioService>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Services.IManterPropriedadesDoProdutoService ManterPropriedadesDoProdutoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProdutoEstoque.Services.IManterPropriedadesDoProdutoService>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Services.IMovimentadorDeEstoqueService MovimentadorDeEstoqueService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProdutoEstoque.Services.IMovimentadorDeEstoqueService>();}
			}		
		}		
		public static partial class ProjetoBackToSalon {		
			public static Perlink.Trinks.ProjetoBackToSalon.Services.ICupomService CupomService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBackToSalon.Services.ICupomService>();}
			}		
		}		
		public static partial class ProjetoBackToSalon {		
			public static Perlink.Trinks.ProjetoBackToSalon.Services.IEnvioEmailService EnvioEmailService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBackToSalon.Services.IEnvioEmailService>();}
			}		
		}		
		public static partial class ProjetoBackToSalon {		
			public static Perlink.Trinks.ProjetoBackToSalon.Services.IEstabelecimentoService EstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBackToSalon.Services.IEstabelecimentoService>();}
			}		
		}		
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Services.ICompraDeVoucherService CompraDeVoucherService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBelezaAmiga.Services.ICompraDeVoucherService>();}
			}		
		}		
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Services.IEnvioEmailService EnvioEmailService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBelezaAmiga.Services.IEnvioEmailService>();}
			}		
		}		
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Services.IEstabelecimentoParticipanteService EstabelecimentoParticipanteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBelezaAmiga.Services.IEstabelecimentoParticipanteService>();}
			}		
		}		
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Services.IPagarMeAPI PagarMeAPI  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBelezaAmiga.Services.IPagarMeAPI>();}
			}		
		}		
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Services.IPagarMeService PagarMeService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBelezaAmiga.Services.IPagarMeService>();}
			}		
		}		
		public static partial class ProjetoBelezaAmiga {		
			public static Perlink.Trinks.ProjetoBelezaAmiga.Services.IVoucherService VoucherService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoBelezaAmiga.Services.IVoucherService>();}
			}		
		}		
		public static partial class ProjetoEncontreSeuSalao {		
			public static Perlink.Trinks.ProjetoEncontreSeuSalao.Services.IEstabelecimentoService EstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoEncontreSeuSalao.Services.IEstabelecimentoService>();}
			}		
		}		
		public static partial class ProjetoEncontreSeuSalao {		
			public static Perlink.Trinks.ProjetoEncontreSeuSalao.Services.IGmapsLimitStatusService GmapsLimitStatusService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ProjetoEncontreSeuSalao.Services.IGmapsLimitStatusService>();}
			}		
		}		
		public static partial class Promocoes {		
			public static Perlink.Trinks.Promocoes.Services.IPromocaoService PromocaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Promocoes.Services.IPromocaoService>();}
			}		
		}		
		public static partial class PromocoesOnline {		
			public static Perlink.Trinks.PromocoesOnline.Services.IPromocaoOnlineService PromocaoOnlineService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromocoesOnline.Services.IPromocaoOnlineService>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Services.IDisponibilidadeDeCupomService DisponibilidadeDeCupomService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromotoresDoTrinks.Services.IDisponibilidadeDeCupomService>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Services.IHistoricoUsoCuponsParceriaService HistoricoUsoCuponsParceriaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromotoresDoTrinks.Services.IHistoricoUsoCuponsParceriaService>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Services.INotificacoesDoAcompanhamentoService NotificacoesDoAcompanhamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromotoresDoTrinks.Services.INotificacoesDoAcompanhamentoService>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Services.IPromotorDoTrinksService PromotorDoTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromotoresDoTrinks.Services.IPromotorDoTrinksService>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Services.ISaldoPromotorService SaldoPromotorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromotoresDoTrinks.Services.ISaldoPromotorService>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Services.ITransacoesPromotorService TransacoesPromotorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.PromotoresDoTrinks.Services.ITransacoesPromotorService>();}
			}		
		}		
		public static partial class RecorrenciaDeAssinatura {		
			public static Perlink.Trinks.RecorrenciaDeAssinatura.Services.IAssinaturaRecorrenteService AssinaturaRecorrenteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RecorrenciaDeAssinatura.Services.IAssinaturaRecorrenteService>();}
			}		
		}		
		public static partial class RecorrenciaDeAssinatura {		
			public static Perlink.Trinks.RecorrenciaDeAssinatura.Services.IPacoteDeAssinaturaService PacoteDeAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RecorrenciaDeAssinatura.Services.IPacoteDeAssinaturaService>();}
			}		
		}		
		public static partial class RecuperacaoDeConta {		
			public static Perlink.Trinks.RecuperacaoDeConta.Services.IRecuperacaoDeContaService RecuperacaoDeContaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RecuperacaoDeConta.Services.IRecuperacaoDeContaService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IClientesQueNaoRetornaramService ClientesQueNaoRetornaramService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IClientesQueNaoRetornaramService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IConfiguracoesDoMapaDeCalorService ConfiguracoesDoMapaDeCalorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IConfiguracoesDoMapaDeCalorService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IMapaDeCalorService MapaDeCalorService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IMapaDeCalorService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRankingEstendidoEstabelecimentosService RankingEstendidoEstabelecimentosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRankingEstendidoEstabelecimentosService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRelatorioConsolidadoService RelatorioConsolidadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRelatorioConsolidadoService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRelatorioDemonstrativoDeResultadoService RelatorioDemonstrativoDeResultadoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRelatorioDemonstrativoDeResultadoService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRelatorioRankingDeClientesEstendidoService RelatorioRankingDeClientesEstendidoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRelatorioRankingDeClientesEstendidoService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRelatorioRankingDeClientesService RelatorioRankingDeClientesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRelatorioRankingDeClientesService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRelatorioRankingDePacotesService RelatorioRankingDePacotesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRelatorioRankingDePacotesService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRelatorioRankingDeServicosService RelatorioRankingDeServicosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRelatorioRankingDeServicosService>();}
			}		
		}		
		public static partial class Relatorios {		
			public static Perlink.Trinks.Relatorios.Services.IRetornoDeClientesService RetornoDeClientesService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Relatorios.Services.IRetornoDeClientesService>();}
			}		
		}		
		public static partial class RodizioDeProfissionais {		
			public static Perlink.Trinks.RodizioDeProfissionais.Services.IControleDeFilaDoRodizioService ControleDeFilaDoRodizioService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RodizioDeProfissionais.Services.IControleDeFilaDoRodizioService>();}
			}		
		}		
		public static partial class RPS {		
			public static Perlink.Trinks.RPS.Services.ICobDadosRpsService CobDadosRpsService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RPS.Services.ICobDadosRpsService>();}
			}		
		}		
		public static partial class RPS {		
			public static Perlink.Trinks.RPS.Services.IDadosRPSService DadosRPSService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RPS.Services.IDadosRPSService>();}
			}		
		}		
		public static partial class RPS {		
			public static Perlink.Trinks.RPS.Services.IEmissaoRPSService EmissaoRPSService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RPS.Services.IEmissaoRPSService>();}
			}		
		}		
		public static partial class RPS {		
			public static Perlink.Trinks.RPS.Services.IInvoicyMetodosServices InvoicyMetodosServices  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.RPS.Services.IInvoicyMetodosServices>();}
			}		
		}		
		public static partial class Seguranca {		
			public static Perlink.Trinks.Seguranca.Services.IApiClientService ApiClientService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Seguranca.Services.IApiClientService>();}
			}		
		}		
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IConsultarPedidosDeCompraService ConsultarPedidosDeCompraService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IConsultarPedidosDeCompraService>();}
			}		
		}		
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IControleDePermissoesParaPedidosDeCompra ControleDePermissoesParaPedidosDeCompra  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IControleDePermissoesParaPedidosDeCompra>();}
			}		
		}		
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IHistoricoDePedidoService HistoricoDePedidoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IHistoricoDePedidoService>();}
			}		
		}		
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IMontarPedidoDeCompraDeProdutosService MontarPedidoDeCompraDeProdutosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IMontarPedidoDeCompraDeProdutosService>();}
			}		
		}		
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IReceberPedidoService ReceberPedidoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SugestoesEPedidosDeCompra.Services.IReceberPedidoService>();}
			}		
		}		
		public static partial class SugestoesEPedidosDeCompra {		
			public static Perlink.Trinks.SugestoesEPedidosDeCompra.Services.ISugestaoDeCompraDeProdutosService SugestaoDeCompraDeProdutosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SugestoesEPedidosDeCompra.Services.ISugestaoDeCompraDeProdutosService>();}
			}		
		}		
		public static partial class SurveyAppB2B {		
			public static Perlink.Trinks.SurveyAppB2B.Services.ISurveyAppProService SurveyAppProService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.SurveyAppB2B.Services.ISurveyAppProService>();}
			}		
		}		
		public static partial class TesteAB {		
			public static Perlink.Trinks.TesteAB.Services.ITesteABService TesteABService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TesteAB.Services.ITesteABService>();}
			}		
		}		
		public static partial class TestesAB {		
			public static Perlink.Trinks.TestesAB.Services.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoService TesteABAssinaturaPlanoAssinaturaEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TestesAB.Services.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoService>();}
			}		
		}		
		public static partial class TestesAB {		
			public static Perlink.Trinks.TestesAB.Services.ITesteABPlanoAssinaturaService TesteABPlanoAssinaturaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TestesAB.Services.ITesteABPlanoAssinaturaService>();}
			}		
		}		
		public static partial class TestesAB {		
			public static Perlink.Trinks.TestesAB.Services.ITesteABWhyTrinksService TesteABWhyTrinksService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TestesAB.Services.ITesteABWhyTrinksService>();}
			}		
		}		
		public static partial class TrinksApps {		
			public static Perlink.Trinks.TrinksApps.Services.IAplicativoDeAgendamentoService AplicativoDeAgendamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TrinksApps.Services.IAplicativoDeAgendamentoService>();}
			}		
		}		
		public static partial class TrinksApps {		
			public static Perlink.Trinks.TrinksApps.Services.IRegistroDadosOnboardingService RegistroDadosOnboardingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TrinksApps.Services.IRegistroDadosOnboardingService>();}
			}		
		}		
		public static partial class TrinksApps {		
			public static Perlink.Trinks.TrinksApps.Services.IRegistroDeDispositivosService RegistroDeDispositivosService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TrinksApps.Services.IRegistroDeDispositivosService>();}
			}		
		}		
		public static partial class TrinksAtendimento {		
			public static Perlink.Trinks.TrinksAtendimento.Services.IFaleConoscoService FaleConoscoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TrinksAtendimento.Services.IFaleConoscoService>();}
			}		
		}		
		public static partial class TrinksAtendimento {		
			public static Perlink.Trinks.TrinksAtendimento.Services.IIntegracaoMovideskService IntegracaoMovideskService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.TrinksAtendimento.Services.IIntegracaoMovideskService>();}
			}		
		}		
		public static partial class ValidacaoDeIdentidade {		
			public static Perlink.Trinks.ValidacaoDeIdentidade.Services.IEnvioDeCodigoConfirmacaoService EnvioDeCodigoConfirmacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ValidacaoDeIdentidade.Services.IEnvioDeCodigoConfirmacaoService>();}
			}		
		}		
		public static partial class ValidacaoDeIdentidade {		
			public static Perlink.Trinks.ValidacaoDeIdentidade.Services.ISolicitacaoIdentidadeVerificadaService SolicitacaoIdentidadeVerificadaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ValidacaoDeIdentidade.Services.ISolicitacaoIdentidadeVerificadaService>();}
			}		
		}		
		public static partial class ValidacaoDeIdentidade {		
			public static Perlink.Trinks.ValidacaoDeIdentidade.Services.IValidacaoDeCodigoConfirmacaoService ValidacaoDeCodigoConfirmacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ValidacaoDeIdentidade.Services.IValidacaoDeCodigoConfirmacaoService>();}
			}		
		}		
		public static partial class ValidacaoDeIdentidade {		
			public static Perlink.Trinks.ValidacaoDeIdentidade.Services.IVerificacaoDeContaPelaAreaPerlinkService VerificacaoDeContaPelaAreaPerlinkService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.ValidacaoDeIdentidade.Services.IVerificacaoDeContaPelaAreaPerlinkService>();}
			}		
		}		
		public static partial class Vendas {		
			public static Perlink.Trinks.Vendas.Services.IAssociacaoRapidaDeComandaService AssociacaoRapidaDeComandaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Vendas.Services.IAssociacaoRapidaDeComandaService>();}
			}		
		}		
		public static partial class Vendas {		
			public static Perlink.Trinks.Vendas.Services.IComandaService ComandaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Vendas.Services.IComandaService>();}
			}		
		}		
		public static partial class Vendas {		
			public static Perlink.Trinks.Vendas.Services.IPreVendaHistoricoService PreVendaHistoricoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Vendas.Services.IPreVendaHistoricoService>();}
			}		
		}		
		public static partial class Vendas {		
			public static Perlink.Trinks.Vendas.Services.IPreVendaService PreVendaService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.Vendas.Services.IPreVendaService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IAdicionalWhatsAppService AdicionalWhatsAppService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IAdicionalWhatsAppService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ICartaoEstabelecimentoService CartaoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ICartaoEstabelecimentoService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ICompraRecorrenteService CompraRecorrenteService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ICompraRecorrenteService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ICreditoService CreditoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ICreditoService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IDashboardService DashboardService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IDashboardService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IEstablishmentConfigurationService EstablishmentConfigurationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IEstablishmentConfigurationService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IHistoricoCompraAdicionalService HistoricoCompraAdicionalService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IHistoricoCompraAdicionalService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IHorarioComunicacaoService HorarioComunicacaoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IHorarioComunicacaoService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IMessageTemplateService MessageTemplateService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IMessageTemplateService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IOptInService OptInService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IOptInService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IOptOutService OptOutService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IOptOutService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IPacoteCreditoService PacoteCreditoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IPacoteCreditoService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IPagamentoService PagamentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IPagamentoService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.IReceiveService ReceiveService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.IReceiveService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ISaldoEstabelecimentoService SaldoEstabelecimentoService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ISaldoEstabelecimentoService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ISchedulingConfirmationService SchedulingConfirmationService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ISchedulingConfirmationService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ISchedulingRatingService SchedulingRatingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ISchedulingRatingService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ISchedulingReminderService SchedulingReminderService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ISchedulingReminderService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ISendService SendService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ISendService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ISessionService SessionService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ISessionService>();}
			}		
		}		
		public static partial class WhatsApp {		
			public static Perlink.Trinks.WhatsApp.Services.ITestingService TestingService  {
				get {return DomainInfrastructure.Domain.Service<Perlink.Trinks.WhatsApp.Services.ITestingService>();}
			}		
		}		
		public static partial class ClientesAcompanhamentos {		
			public static Perlink.Trinks.ClientesAcompanhamentos.Stories.IRegistroDeAnotacaoStory RegistroDeAnotacaoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ClientesAcompanhamentos.Stories.IRegistroDeAnotacaoStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IAcessoAreaMeuPlanoStory AcessoAreaMeuPlanoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IAcessoAreaMeuPlanoStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IAlteracaoNaAssinaturaAreaPerlinkStory AlteracaoNaAssinaturaAreaPerlinkStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IAlteracaoNaAssinaturaAreaPerlinkStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.ICancelamentoDaAssinaturaStory CancelamentoDaAssinaturaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.ICancelamentoDaAssinaturaStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IContratacaoDeServicoAdicionalBackOfficeStory ContratacaoDeServicoAdicionalBackOfficeStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IContratacaoDeServicoAdicionalBackOfficeStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IDetalhesDeFaturamentoAreaPerlinkStory DetalhesDeFaturamentoAreaPerlinkStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IDetalhesDeFaturamentoAreaPerlinkStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IDorDoClienteNaAssinaturaStory DorDoClienteNaAssinaturaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IDorDoClienteNaAssinaturaStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IFluxoAssinaturaStory FluxoAssinaturaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IFluxoAssinaturaStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IMeuPlanoStory MeuPlanoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IMeuPlanoStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IMotivoDoCancelamentoDaAssinaturaStory MotivoDoCancelamentoDaAssinaturaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IMotivoDoCancelamentoDaAssinaturaStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IOfertaDeAdicionalStory OfertaDeAdicionalStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IOfertaDeAdicionalStory>();}
			}		
		}		
		public static partial class Cobranca {		
			public static Perlink.Trinks.Cobranca.Stories.IReassinaturaStory ReassinaturaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Cobranca.Stories.IReassinaturaStory>();}
			}		
		}		
		public static partial class ComissaoAppB2B {		
			public static Perlink.Trinks.ComissaoAppB2B.Stories.ICarteiraPageStory CarteiraPageStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ComissaoAppB2B.Stories.ICarteiraPageStory>();}
			}		
		}		
		public static partial class ComissaoAppB2B {		
			public static Perlink.Trinks.ComissaoAppB2B.Stories.IDetalhesComissaoPageStory DetalhesComissaoPageStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ComissaoAppB2B.Stories.IDetalhesComissaoPageStory>();}
			}		
		}		
		public static partial class ComissaoAppB2B {		
			public static Perlink.Trinks.ComissaoAppB2B.Stories.IHistoricoDeRecebidosStory HistoricoDeRecebidosStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ComissaoAppB2B.Stories.IHistoricoDeRecebidosStory>();}
			}		
		}		
		public static partial class ControleDeFuncionalidades {		
			public static Perlink.Trinks.ControleDeFuncionalidades.Stories.IGerenciarRecursosStory GerenciarRecursosStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ControleDeFuncionalidades.Stories.IGerenciarRecursosStory>();}
			}		
		}		
		public static partial class ControleDeSatisfacao {		
			public static Perlink.Trinks.ControleDeSatisfacao.Stories.IExportacaoDeAvaliacaoDeSatisfacaoStory ExportacaoDeAvaliacaoDeSatisfacaoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ControleDeSatisfacao.Stories.IExportacaoDeAvaliacaoDeSatisfacaoStory>();}
			}		
		}		
		public static partial class ControleDeSatisfacao {		
			public static Perlink.Trinks.ControleDeSatisfacao.Stories.IExportacaoDeAvaliacaoPublicaStory ExportacaoDeAvaliacaoPublicaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ControleDeSatisfacao.Stories.IExportacaoDeAvaliacaoPublicaStory>();}
			}		
		}		
		public static partial class DebitoParcial {		
			public static Perlink.Trinks.DebitoParcial.Stories.IPagamentoDeDividaStory PagamentoDeDividaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.DebitoParcial.Stories.IPagamentoDeDividaStory>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.IConfiguracaoBaixaAutomaticaNoServicoStory ConfiguracaoBaixaAutomaticaNoServicoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.IConfiguracaoBaixaAutomaticaNoServicoStory>();}
			}		
		}		
		public static partial class EstoqueComBaixaAutomatica {		
			public static Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.IFinalizacaoDoUsoDeProdutosStory FinalizacaoDoUsoDeProdutosStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.IFinalizacaoDoUsoDeProdutosStory>();}
			}		
		}		
		public static partial class Financeiro {		
			public static Perlink.Trinks.Financeiro.Stories.IExportacaoComissoesStory ExportacaoComissoesStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Financeiro.Stories.IExportacaoComissoesStory>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Stories.ICadastroDoFormularioStory CadastroDoFormularioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Formulario.Stories.ICadastroDoFormularioStory>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Stories.IImpressaoDeFormularioStory ImpressaoDeFormularioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Formulario.Stories.IImpressaoDeFormularioStory>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Stories.IListarFormularioDinamicoStory ListarFormularioDinamicoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Formulario.Stories.IListarFormularioDinamicoStory>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Stories.IListarFormulariosRespondidosStory ListarFormulariosRespondidosStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Formulario.Stories.IListarFormulariosRespondidosStory>();}
			}		
		}		
		public static partial class Formulario {		
			public static Perlink.Trinks.Formulario.Stories.IPreenchimentoDeFormularioStory PreenchimentoDeFormularioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Formulario.Stories.IPreenchimentoDeFormularioStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.IClientesComLinksStory ClientesComLinksStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.IClientesComLinksStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.IMultaClubeDeAssinaturasStory MultaClubeDeAssinaturasStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.IMultaClubeDeAssinaturasStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoClubeDeAssinaturasStory TransacaoClubeDeAssinaturasStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoClubeDeAssinaturasStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoDeCreditoDeClienteStory TransacaoDeCreditoDeClienteStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoDeCreditoDeClienteStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoPagamentoAntecipadoHotsiteStory TransacaoPagamentoAntecipadoHotsiteStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoPagamentoAntecipadoHotsiteStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoPromocaoOnlineStory TransacaoPromocaoOnlineStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoPromocaoOnlineStory>();}
			}		
		}		
		public static partial class LinksDePagamentoNoTrinks {		
			public static Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoVendaHotsiteStory TransacaoVendaHotsiteStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.ITransacaoVendaHotsiteStory>();}
			}		
		}		
		public static partial class Onboardings {		
			public static Perlink.Trinks.Onboardings.Stories.ITarefasOnboardingStory TarefasOnboardingStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Onboardings.Stories.ITarefasOnboardingStory>();}
			}		
		}		
		public static partial class Onboardings {		
			public static Perlink.Trinks.Onboardings.Stories.ITrilhaOnboardingStory TrilhaOnboardingStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Onboardings.Stories.ITrilhaOnboardingStory>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Stories.IDadosExtrasDoClienteStory DadosExtrasDoClienteStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Pessoas.Stories.IDadosExtrasDoClienteStory>();}
			}		
		}		
		public static partial class Pessoas {		
			public static Perlink.Trinks.Pessoas.Stories.IRequisitosDeBuscaNoPortalStory RequisitosDeBuscaNoPortalStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.Pessoas.Stories.IRequisitosDeBuscaNoPortalStory>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Stories.ICancelarInventarioStory CancelarInventarioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProdutoEstoque.Stories.ICancelarInventarioStory>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Stories.IConcluirInventarioStory ConcluirInventarioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProdutoEstoque.Stories.IConcluirInventarioStory>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Stories.IExportacaoDeInventariosStory ExportacaoDeInventariosStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProdutoEstoque.Stories.IExportacaoDeInventariosStory>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Stories.IExportacaoDeProdutosDoInventarioStory ExportacaoDeProdutosDoInventarioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProdutoEstoque.Stories.IExportacaoDeProdutosDoInventarioStory>();}
			}		
		}		
		public static partial class ProdutoEstoque {		
			public static Perlink.Trinks.ProdutoEstoque.Stories.IImpressaoDeInventarioStory ImpressaoDeInventarioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProdutoEstoque.Stories.IImpressaoDeInventarioStory>();}
			}		
		}		
		public static partial class ProfissionalAgenda {		
			public static Perlink.Trinks.ProfissionalAgenda.Stories.ICadastroDeLiberacoesNaAgendaStory CadastroDeLiberacoesNaAgendaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProfissionalAgenda.Stories.ICadastroDeLiberacoesNaAgendaStory>();}
			}		
		}		
		public static partial class ProfissionalAgenda {		
			public static Perlink.Trinks.ProfissionalAgenda.Stories.IRelatorioAusenciasELiberacoesStory RelatorioAusenciasELiberacoesStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.ProfissionalAgenda.Stories.IRelatorioAusenciasELiberacoesStory>();}
			}		
		}		
		public static partial class PromotoresDoTrinks {		
			public static Perlink.Trinks.PromotoresDoTrinks.Stories.ICompartilhamentoDeCupomStory CompartilhamentoDeCupomStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.PromotoresDoTrinks.Stories.ICompartilhamentoDeCupomStory>();}
			}		
		}		
		public static partial class RodizioDeProfissionais {		
			public static Perlink.Trinks.RodizioDeProfissionais.Stories.IAgendamentoComProfissionalDaVezStory AgendamentoComProfissionalDaVezStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.RodizioDeProfissionais.Stories.IAgendamentoComProfissionalDaVezStory>();}
			}		
		}		
		public static partial class RodizioDeProfissionais {		
			public static Perlink.Trinks.RodizioDeProfissionais.Stories.IDesativacaoDoRodizioStory DesativacaoDoRodizioStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.RodizioDeProfissionais.Stories.IDesativacaoDoRodizioStory>();}
			}		
		}		
		public static partial class RodizioDeProfissionais {		
			public static Perlink.Trinks.RodizioDeProfissionais.Stories.IEntrarOuSairDoRodizioPeloPATStory EntrarOuSairDoRodizioPeloPATStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.RodizioDeProfissionais.Stories.IEntrarOuSairDoRodizioPeloPATStory>();}
			}		
		}		
		public static partial class RodizioDeProfissionais {		
			public static Perlink.Trinks.RodizioDeProfissionais.Stories.IIncluirOuExcluirProfissionaisPelaAgendaStory IncluirOuExcluirProfissionaisPelaAgendaStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.RodizioDeProfissionais.Stories.IIncluirOuExcluirProfissionaisPelaAgendaStory>();}
			}		
		}		
		public static partial class SurveyAppB2B {		
			public static Perlink.Trinks.SurveyAppB2B.Stories.ISurveyAppProStory SurveyAppProStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.SurveyAppB2B.Stories.ISurveyAppProStory>();}
			}		
		}		
		public static partial class TrinksAtendimento {		
			public static Perlink.Trinks.TrinksAtendimento.Stories.IFaleConoscoStory FaleConoscoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.TrinksAtendimento.Stories.IFaleConoscoStory>();}
			}		
		}		
		public static partial class TrinksPay {		
			public static Perlink.Trinks.TrinksPay.Stories.IAtivacaoDeProfissionalPOSStory AtivacaoDeProfissionalPOSStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.TrinksPay.Stories.IAtivacaoDeProfissionalPOSStory>();}
			}		
		}		
		public static partial class TrinksPay {		
			public static Perlink.Trinks.TrinksPay.Stories.IHabilitacaoDeBelezinhaNoEstabelecimentoStory HabilitacaoDeBelezinhaNoEstabelecimentoStory  {
				get {return DomainInfrastructure.Domain.Story<Perlink.Trinks.TrinksPay.Stories.IHabilitacaoDeBelezinhaNoEstabelecimentoStory>();}
			}		
		}		
		public static Perlink.Trinks.Views.IViewLoader Views {
            get { return DomainInfrastructure.Domain.ViewLoader<Perlink.Trinks.Views.IViewLoader>(); }
        }
        
        public static Perlink.DomainInfrastructure.Web.IWebContext WebContext {
            get { return DomainInfrastructure.Domain.Infrastructure.WebContext; }
        }
		
		public static OcultarDadosService OcultarDadosService {
			get { return DomainInfrastructure.Domain.Resolve<OcultarDadosService>(); }
		}
	}
}
