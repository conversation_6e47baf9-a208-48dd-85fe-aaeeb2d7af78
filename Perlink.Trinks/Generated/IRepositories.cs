
using Perlink.DomainInfrastructure.Repositories;
namespace Perlink.Trinks.Autoatendimento.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CheckInEstablishments.
    /// </summary>
    public partial interface ICheckInEstablishmentsRepository : IRepository<CheckInEstablishments> { 
		Perlink.Trinks.Autoatendimento.Factories.ICheckInEstablishmentsFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfigurationsEstablishment.
    /// </summary>
    public partial interface IConfigurationsEstablishmentRepository : IRepository<ConfigurationsEstablishment> { 
		Perlink.Trinks.Autoatendimento.Factories.IConfigurationsEstablishmentFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade StatusServiceCustomerEstablishment.
    /// </summary>
    public partial interface IStatusServiceCustomerEstablishmentRepository : IRepository<StatusServiceCustomerEstablishment> { 
		Perlink.Trinks.Autoatendimento.Factories.IStatusServiceCustomerEstablishmentFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Autoatendimento.DTO.Repositories {

 
}
namespace Perlink.Trinks.Autoatendimento.Enums.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.BaseIBPT.Repositories {

    /// <summary>
    /// Interface para repositório da entidade DadosIBPT.
    /// </summary>
    public partial interface IDadosIBPTRepository : IRepository<DadosIBPT> { 
		Perlink.Trinks.BaseIBPT.Factories.IDadosIBPTFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Repositories {

    /// <summary>
    /// Interface para repositório da entidade BandeiraCartaoMDR.
    /// </summary>
    public partial interface IBandeiraCartaoMDRRepository : IRepository<BandeiraCartaoMDR> { 
		Perlink.Trinks.Belezinha.Factories.IBandeiraCartaoMDRFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Credenciamento.
    /// </summary>
    public partial interface ICredenciamentoRepository : IRepository<Credenciamento> { 
		Perlink.Trinks.Belezinha.Factories.ICredenciamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CredenciamentoComStoneCode.
    /// </summary>
    public partial interface ICredenciamentoComStoneCodeRepository : IRepository<CredenciamentoComStoneCode> { 
		Perlink.Trinks.Belezinha.Factories.ICredenciamentoComStoneCodeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoTerminalPos.
    /// </summary>
    public partial interface IEstabelecimentoTerminalPosRepository : IRepository<EstabelecimentoTerminalPos> { 
		Perlink.Trinks.Belezinha.Factories.IEstabelecimentoTerminalPosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Hierarquia.
    /// </summary>
    public partial interface IHierarquiaRepository : IRepository<Hierarquia> { 
		Perlink.Trinks.Belezinha.Factories.IHierarquiaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Mcc.
    /// </summary>
    public partial interface IMccRepository : IRepository<Mcc> { 
		Perlink.Trinks.Belezinha.Factories.IMccFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TaxaAntecipacao.
    /// </summary>
    public partial interface ITaxaAntecipacaoRepository : IRepository<TaxaAntecipacao> { 
		Perlink.Trinks.Belezinha.Factories.ITaxaAntecipacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TaxaMDR.
    /// </summary>
    public partial interface ITaxaMDRRepository : IRepository<TaxaMDR> { 
		Perlink.Trinks.Belezinha.Factories.ITaxaMDRFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TpvMensal.
    /// </summary>
    public partial interface ITpvMensalRepository : IRepository<TpvMensal> { 
		Perlink.Trinks.Belezinha.Factories.ITpvMensalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoAvulsaPOSWebhookRequest.
    /// </summary>
    public partial interface ITransacaoAvulsaPOSWebhookRequestRepository : IRepository<TransacaoAvulsaPOSWebhookRequest> { 
		Perlink.Trinks.Belezinha.Factories.ITransacaoAvulsaPOSWebhookRequestFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Belezinha.DTO.AgendaDeRecebiveis.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Enums.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Filters.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Helpers.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Belezinha.Strategies.Pagarme.Repositories {

 
}
namespace Perlink.Trinks.Caching.Repositories {

 
}
namespace Perlink.Trinks.Cashback.Repositories {

    /// <summary>
    /// Interface para repositório da entidade BonusTransacao.
    /// </summary>
    public partial interface IBonusTransacaoRepository : IRepository<BonusTransacao> { 
		Perlink.Trinks.Cashback.Factories.IBonusTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackComissao.
    /// </summary>
    public partial interface ICashbackComissaoRepository : IRepository<CashbackComissao> { 
		Perlink.Trinks.Cashback.Factories.ICashbackComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackComissaoValorAReceber.
    /// </summary>
    public partial interface ICashbackComissaoValorAReceberRepository : IRepository<CashbackComissaoValorAReceber> { 
		Perlink.Trinks.Cashback.Factories.ICashbackComissaoValorAReceberFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackHorarioTransacao.
    /// </summary>
    public partial interface ICashbackHorarioTransacaoRepository : IRepository<CashbackHorarioTransacao> { 
		Perlink.Trinks.Cashback.Factories.ICashbackHorarioTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackItemVenda.
    /// </summary>
    public partial interface ICashbackItemVendaRepository : IRepository<CashbackItemVenda> { 
		Perlink.Trinks.Cashback.Factories.ICashbackItemVendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CashbackTransacao.
    /// </summary>
    public partial interface ICashbackTransacaoRepository : IRepository<CashbackTransacao> { 
		Perlink.Trinks.Cashback.Factories.ICashbackTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosIntegracao.
    /// </summary>
    public partial interface IEstabelecimentoDadosIntegracaoRepository : IRepository<EstabelecimentoDadosIntegracao> { 
		Perlink.Trinks.Cashback.Factories.IEstabelecimentoDadosIntegracaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Cashback.DTO.CrmBonus.Repositories {

 
}
namespace Perlink.Trinks.Cashback.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Anotacao.
    /// </summary>
    public partial interface IAnotacaoRepository : IRepository<Anotacao> { 
		Perlink.Trinks.ClientesAcompanhamentos.Factories.IAnotacaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClientesAcompanhamentos.Stories.Repositories {

 
}
namespace Perlink.Trinks.ClientesAnexos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ClienteAnexo.
    /// </summary>
    public partial interface IClienteAnexoRepository : IRepository<ClienteAnexo> { 
		Perlink.Trinks.ClientesAnexos.Factories.IClienteAnexoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MeuAnexo.
    /// </summary>
    public partial interface IMeuAnexoRepository : IRepository<MeuAnexo> { 
		Perlink.Trinks.ClientesAnexos.Factories.IMeuAnexoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ClientesAnexos.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClientesAnexos.Enums.Repositories {

 
}
namespace Perlink.Trinks.ClientesAnexos.ObjetosDeValor.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AssinaturaDoCliente.
    /// </summary>
    public partial interface IAssinaturaDoClienteRepository : IRepository<AssinaturaDoCliente> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IAssinaturaDoClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Beneficio.
    /// </summary>
    public partial interface IBeneficioRepository : IRepository<Beneficio> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDaAssinatura.
    /// </summary>
    public partial interface IBeneficioDaAssinaturaRepository : IRepository<BeneficioDaAssinatura> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDoPlano.
    /// </summary>
    public partial interface IBeneficioDoPlanoRepository : IRepository<BeneficioDoPlano> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioDoPlanoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioProduto.
    /// </summary>
    public partial interface IBeneficioProdutoRepository : IRepository<BeneficioProduto> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioServico.
    /// </summary>
    public partial interface IBeneficioServicoRepository : IRepository<BeneficioServico> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioUsado.
    /// </summary>
    public partial interface IBeneficioUsadoRepository : IRepository<BeneficioUsado> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IBeneficioUsadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContratoDeAdesao.
    /// </summary>
    public partial interface IContratoDeAdesaoRepository : IRepository<ContratoDeAdesao> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IContratoDeAdesaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDeStatusAssinaturaDoClube.
    /// </summary>
    public partial interface IHistoricoDeStatusAssinaturaDoClubeRepository : IRepository<HistoricoDeStatusAssinaturaDoClube> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IHistoricoDeStatusAssinaturaDoClubeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade IntencaoEdicaoDoPlanoCliente.
    /// </summary>
    public partial interface IIntencaoEdicaoDoPlanoClienteRepository : IRepository<IntencaoEdicaoDoPlanoCliente> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IIntencaoEdicaoDoPlanoClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDaAssinatura.
    /// </summary>
    public partial interface ILinkDePagamentoDaAssinaturaRepository : IRepository<LinkDePagamentoDaAssinatura> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.ILinkDePagamentoDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDoCancelamentoDaAssinatura.
    /// </summary>
    public partial interface ILinkDePagamentoDoCancelamentoDaAssinaturaRepository : IRepository<LinkDePagamentoDoCancelamentoDaAssinatura> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.ILinkDePagamentoDoCancelamentoDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoDeAssinatura.
    /// </summary>
    public partial interface IPagamentoDeAssinaturaRepository : IRepository<PagamentoDeAssinatura> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IPagamentoDeAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoMultaDeCancelamentoDaAssinatura.
    /// </summary>
    public partial interface IPagamentoMultaDeCancelamentoDaAssinaturaRepository : IRepository<PagamentoMultaDeCancelamentoDaAssinatura> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IPagamentoMultaDeCancelamentoDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PlanoCliente.
    /// </summary>
    public partial interface IPlanoClienteRepository : IRepository<PlanoCliente> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IPlanoClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VendaOnline.
    /// </summary>
    public partial interface IVendaOnlineRepository : IRepository<VendaOnline> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IVendaOnlineFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VigenciaDeAssinatura.
    /// </summary>
    public partial interface IVigenciaDeAssinaturaRepository : IRepository<VigenciaDeAssinatura> { 
		Perlink.Trinks.ClubeDeAssinaturas.Factories.IVigenciaDeAssinaturaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Calculos.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.DTO.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Enums.Repositories {

 
}
namespace Perlink.Trinks.ClubeDeAssinaturas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AdicionalCobrado.
    /// </summary>
    public partial interface IAdicionalCobradoRepository : IRepository<AdicionalCobrado> { 
		Perlink.Trinks.Cobranca.Factories.IAdicionalCobradoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AdicionalNaAssinatura.
    /// </summary>
    public partial interface IAdicionalNaAssinaturaRepository : IRepository<AdicionalNaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IAdicionalNaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AgendamentoDeMigracaoDoPlano.
    /// </summary>
    public partial interface IAgendamentoDeMigracaoDoPlanoRepository : IRepository<AgendamentoDeMigracaoDoPlano> { 
		Perlink.Trinks.Cobranca.Factories.IAgendamentoDeMigracaoDoPlanoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Assinatura.
    /// </summary>
    public partial interface IAssinaturaRepository : IRepository<Assinatura> { 
		Perlink.Trinks.Cobranca.Factories.IAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDoPlanoAssinatura.
    /// </summary>
    public partial interface IBeneficioDoPlanoAssinaturaRepository : IRepository<BeneficioDoPlanoAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IBeneficioDoPlanoAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficioDoPlanoMeuPlano.
    /// </summary>
    public partial interface IBeneficioDoPlanoMeuPlanoRepository : IRepository<BeneficioDoPlanoMeuPlano> { 
		Perlink.Trinks.Cobranca.Factories.IBeneficioDoPlanoMeuPlanoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaFinanceira.
    /// </summary>
    public partial interface IContaFinanceiraRepository : IRepository<ContaFinanceira> { 
		Perlink.Trinks.Cobranca.Factories.IContaFinanceiraFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DadosSales.
    /// </summary>
    public partial interface IDadosSalesRepository : IRepository<DadosSales> { 
		Perlink.Trinks.Cobranca.Factories.IDadosSalesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoNaAssinatura.
    /// </summary>
    public partial interface IDescontoNaAssinaturaRepository : IRepository<DescontoNaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IDescontoNaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoNoAdicionalDaAssinatura.
    /// </summary>
    public partial interface IDescontoNoAdicionalDaAssinaturaRepository : IRepository<DescontoNoAdicionalDaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IDescontoNoAdicionalDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoNoPlanoDaAssinatura.
    /// </summary>
    public partial interface IDescontoNoPlanoDaAssinaturaRepository : IRepository<DescontoNoPlanoDaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IDescontoNoPlanoDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DorDoCliente.
    /// </summary>
    public partial interface IDorDoClienteRepository : IRepository<DorDoCliente> { 
		Perlink.Trinks.Cobranca.Factories.IDorDoClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DorDoClienteNaAssinatura.
    /// </summary>
    public partial interface IDorDoClienteNaAssinaturaRepository : IRepository<DorDoClienteNaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IDorDoClienteNaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParceriasTrinks.
    /// </summary>
    public partial interface IEstabelecimentoParceriasTrinksRepository : IRepository<EstabelecimentoParceriasTrinks> { 
		Perlink.Trinks.Cobranca.Factories.IEstabelecimentoParceriasTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ExperimentacaoEstabelecimento.
    /// </summary>
    public partial interface IExperimentacaoEstabelecimentoRepository : IRepository<ExperimentacaoEstabelecimento> { 
		Perlink.Trinks.Cobranca.Factories.IExperimentacaoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Fatura.
    /// </summary>
    public partial interface IFaturaRepository : IRepository<Fatura> { 
		Perlink.Trinks.Cobranca.Factories.IFaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FaturaMarketing.
    /// </summary>
    public partial interface IFaturaMarketingRepository : IRepository<FaturaMarketing> { 
		Perlink.Trinks.Cobranca.Factories.IFaturaMarketingFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FaturaTrinks.
    /// </summary>
    public partial interface IFaturaTrinksRepository : IRepository<FaturaTrinks> { 
		Perlink.Trinks.Cobranca.Factories.IFaturaTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FaturaWhatsApp.
    /// </summary>
    public partial interface IFaturaWhatsAppRepository : IRepository<FaturaWhatsApp> { 
		Perlink.Trinks.Cobranca.Factories.IFaturaWhatsAppFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormaDeContratacaoDoAdicional.
    /// </summary>
    public partial interface IFormaDeContratacaoDoAdicionalRepository : IRepository<FormaDeContratacaoDoAdicional> { 
		Perlink.Trinks.Cobranca.Factories.IFormaDeContratacaoDoAdicionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormaPagamento.
    /// </summary>
    public partial interface IFormaPagamentoRepository : IRepository<FormaPagamento> { 
		Perlink.Trinks.Cobranca.Factories.IFormaPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDoAdicionalNaAssinatura.
    /// </summary>
    public partial interface IHistoricoDoAdicionalNaAssinaturaRepository : IRepository<HistoricoDoAdicionalNaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IHistoricoDoAdicionalNaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDoCancelamento.
    /// </summary>
    public partial interface IMotivoDoCancelamentoRepository : IRepository<MotivoDoCancelamento> { 
		Perlink.Trinks.Cobranca.Factories.IMotivoDoCancelamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDoCancelamentoDaAssinatura.
    /// </summary>
    public partial interface IMotivoDoCancelamentoDaAssinaturaRepository : IRepository<MotivoDoCancelamentoDaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IMotivoDoCancelamentoDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ObservacaoAssinatura.
    /// </summary>
    public partial interface IObservacaoAssinaturaRepository : IRepository<ObservacaoAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IObservacaoAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OfertaDeServicoAdicional.
    /// </summary>
    public partial interface IOfertaDeServicoAdicionalRepository : IRepository<OfertaDeServicoAdicional> { 
		Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OfertaDeServicoAdicionalMeuPlano.
    /// </summary>
    public partial interface IOfertaDeServicoAdicionalMeuPlanoRepository : IRepository<OfertaDeServicoAdicionalMeuPlano> { 
		Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalMeuPlanoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OfertaDeServicoAdicionalMeuPlanoDisponibilidade.
    /// </summary>
    public partial interface IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeRepository : IRepository<OfertaDeServicoAdicionalMeuPlanoDisponibilidade> { 
		Perlink.Trinks.Cobranca.Factories.IOfertaDeServicoAdicionalMeuPlanoDisponibilidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ParceriaTipoTrinks.
    /// </summary>
    public partial interface IParceriaTipoTrinksRepository : IRepository<ParceriaTipoTrinks> { 
		Perlink.Trinks.Cobranca.Factories.IParceriaTipoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ParceriaTrinks.
    /// </summary>
    public partial interface IParceriaTrinksRepository : IRepository<ParceriaTrinks> { 
		Perlink.Trinks.Cobranca.Factories.IParceriaTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaPagamentoExterno.
    /// </summary>
    public partial interface IPessoaJuridicaPagamentoExternoRepository : IRepository<PessoaJuridicaPagamentoExterno> { 
		Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaPagamentoExternoFatura.
    /// </summary>
    public partial interface IPessoaJuridicaPagamentoExternoFaturaRepository : IRepository<PessoaJuridicaPagamentoExternoFatura> { 
		Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaPagamentoExternoFaturaHistorico.
    /// </summary>
    public partial interface IPessoaJuridicaPagamentoExternoFaturaHistoricoRepository : IRepository<PessoaJuridicaPagamentoExternoFaturaHistorico> { 
		Perlink.Trinks.Cobranca.Factories.IPessoaJuridicaPagamentoExternoFaturaHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PlanoAssinatura.
    /// </summary>
    public partial interface IPlanoAssinaturaRepository : IRepository<PlanoAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IPlanoAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoNaAssinatura.
    /// </summary>
    public partial interface IPromocaoNaAssinaturaRepository : IRepository<PromocaoNaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IPromocaoNaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoPraContaFinanceira.
    /// </summary>
    public partial interface IPromocaoPraContaFinanceiraRepository : IRepository<PromocaoPraContaFinanceira> { 
		Perlink.Trinks.Cobranca.Factories.IPromocaoPraContaFinanceiraFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoTrinks.
    /// </summary>
    public partial interface IPromocaoTrinksRepository : IRepository<PromocaoTrinks> { 
		Perlink.Trinks.Cobranca.Factories.IPromocaoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioAssinatura.
    /// </summary>
    public partial interface IRelatorioAssinaturaRepository : IRepository<RelatorioAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IRelatorioAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioFaturamento.
    /// </summary>
    public partial interface IRelatorioFaturamentoRepository : IRepository<RelatorioFaturamento> { 
		Perlink.Trinks.Cobranca.Factories.IRelatorioFaturamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ResponsavelAtendimento.
    /// </summary>
    public partial interface IResponsavelAtendimentoRepository : IRepository<ResponsavelAtendimento> { 
		Perlink.Trinks.Cobranca.Factories.IResponsavelAtendimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoTrinks.
    /// </summary>
    public partial interface IServicoTrinksRepository : IRepository<ServicoTrinks> { 
		Perlink.Trinks.Cobranca.Factories.IServicoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SolicitacaoCancelamentoDaAssinatura.
    /// </summary>
    public partial interface ISolicitacaoCancelamentoDaAssinaturaRepository : IRepository<SolicitacaoCancelamentoDaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.ISolicitacaoCancelamentoDaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade StatusConta.
    /// </summary>
    public partial interface IStatusContaRepository : IRepository<StatusConta> { 
		Perlink.Trinks.Cobranca.Factories.IStatusContaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade StatusFatura.
    /// </summary>
    public partial interface IStatusFaturaRepository : IRepository<StatusFatura> { 
		Perlink.Trinks.Cobranca.Factories.IStatusFaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoAssociacao.
    /// </summary>
    public partial interface ITipoAssociacaoRepository : IRepository<TipoAssociacao> { 
		Perlink.Trinks.Cobranca.Factories.ITipoAssociacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoFormaPagamento.
    /// </summary>
    public partial interface ITipoFormaPagamentoRepository : IRepository<TipoFormaPagamento> { 
		Perlink.Trinks.Cobranca.Factories.ITipoFormaPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoServico.
    /// </summary>
    public partial interface ITipoServicoRepository : IRepository<TipoServico> { 
		Perlink.Trinks.Cobranca.Factories.ITipoServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeAdesaoDoAdicionalPorFaixa.
    /// </summary>
    public partial interface IValorDeAdesaoDoAdicionalPorFaixaRepository : IRepository<ValorDeAdesaoDoAdicionalPorFaixa> { 
		Perlink.Trinks.Cobranca.Factories.IValorDeAdesaoDoAdicionalPorFaixaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura.
    /// </summary>
    public partial interface IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaRepository : IRepository<ValorDiferenciadoDeAdicionalPorFaixaNaAssinatura> { 
		Perlink.Trinks.Cobranca.Factories.IValorDiferenciadoDeAdicionalPorFaixaNaAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDoAdicionalPorFaixa.
    /// </summary>
    public partial interface IValorDoAdicionalPorFaixaRepository : IRepository<ValorDoAdicionalPorFaixa> { 
		Perlink.Trinks.Cobranca.Factories.IValorDoAdicionalPorFaixaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDoAdicionalPorFaixaDaOferta.
    /// </summary>
    public partial interface IValorDoAdicionalPorFaixaDaOfertaRepository : IRepository<ValorDoAdicionalPorFaixaDaOferta> { 
		Perlink.Trinks.Cobranca.Factories.IValorDoAdicionalPorFaixaDaOfertaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorPorFaixa.
    /// </summary>
    public partial interface IValorPorFaixaRepository : IRepository<ValorPorFaixa> { 
		Perlink.Trinks.Cobranca.Factories.IValorPorFaixaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Cobranca.ConfiguracoesDosAdicionais.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.ContratacaoDosAdicionais.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.DTO.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Enums.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Extensions.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Factories.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Helpers.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.ObjectValues.Repositories {

 
}
namespace Perlink.Trinks.Cobranca.Stories.Repositories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.DTO.Repositories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.Enum.Repositories {

 
}
namespace Perlink.Trinks.ComissaoAppB2B.Stories.Repositories {

 
}
namespace Perlink.Trinks.Compromissos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Compromissos.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ComunidadeTrinks.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ArtigoDeNovidade.
    /// </summary>
    public partial interface IArtigoDeNovidadeRepository : IRepository<ArtigoDeNovidade> { 
		Perlink.Trinks.ComunidadeTrinks.Factories.IArtigoDeNovidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Sugestao.
    /// </summary>
    public partial interface ISugestaoRepository : IRepository<Sugestao> { 
		Perlink.Trinks.ComunidadeTrinks.Factories.ISugestaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TopicoDeVotacao.
    /// </summary>
    public partial interface ITopicoDeVotacaoRepository : IRepository<TopicoDeVotacao> { 
		Perlink.Trinks.ComunidadeTrinks.Factories.ITopicoDeVotacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VotacaoDeSugestao.
    /// </summary>
    public partial interface IVotacaoDeSugestaoRepository : IRepository<VotacaoDeSugestao> { 
		Perlink.Trinks.ComunidadeTrinks.Factories.IVotacaoDeSugestaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Voto.
    /// </summary>
    public partial interface IVotoRepository : IRepository<Voto> { 
		Perlink.Trinks.ComunidadeTrinks.Factories.IVotoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ComunidadeTrinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ContaFinanceiraDoEstabelecimento.
    /// </summary>
    public partial interface IContaFinanceiraDoEstabelecimentoRepository : IRepository<ContaFinanceiraDoEstabelecimento> { 
		Perlink.Trinks.ConciliacaoBancaria.Factories.IContaFinanceiraDoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaFinanceiraPadrao.
    /// </summary>
    public partial interface IContaFinanceiraPadraoRepository : IRepository<ContaFinanceiraPadrao> { 
		Perlink.Trinks.ConciliacaoBancaria.Factories.IContaFinanceiraPadraoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ConciliacaoBancaria.DTO.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Enums.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Exportadores.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBancaria.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Dtos.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Dtos.V2.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.PagarMe.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Siclos.Repositories {

 
}
namespace Perlink.Trinks.ConciliacaoBelezinha.Strategies.Stone.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AutenticacaoContaDigital.
    /// </summary>
    public partial interface IAutenticacaoContaDigitalRepository : IRepository<AutenticacaoContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoContaDigitalConfirmacao.
    /// </summary>
    public partial interface IAutenticacaoContaDigitalConfirmacaoRepository : IRepository<AutenticacaoContaDigitalConfirmacao> { 
		Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalConfirmacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoContaDigitalEnvio.
    /// </summary>
    public partial interface IAutenticacaoContaDigitalEnvioRepository : IRepository<AutenticacaoContaDigitalEnvio> { 
		Perlink.Trinks.ContaDigital.Factories.IAutenticacaoContaDigitalEnvioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoIdentidadePreCadastro.
    /// </summary>
    public partial interface IAutenticacaoIdentidadePreCadastroRepository : IRepository<AutenticacaoIdentidadePreCadastro> { 
		Perlink.Trinks.ContaDigital.Factories.IAutenticacaoIdentidadePreCadastroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AutenticacaoIdentidadeUsuarioConta.
    /// </summary>
    public partial interface IAutenticacaoIdentidadeUsuarioContaRepository : IRepository<AutenticacaoIdentidadeUsuarioConta> { 
		Perlink.Trinks.ContaDigital.Factories.IAutenticacaoIdentidadeUsuarioContaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CategoriaPermissaoContaDigital.
    /// </summary>
    public partial interface ICategoriaPermissaoContaDigitalRepository : IRepository<CategoriaPermissaoContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.ICategoriaPermissaoContaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ChavePix.
    /// </summary>
    public partial interface IChavePixRepository : IRepository<ChavePix> { 
		Perlink.Trinks.ContaDigital.Factories.IChavePixFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ChavePixContaDigital.
    /// </summary>
    public partial interface IChavePixContaDigitalRepository : IRepository<ChavePixContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IChavePixContaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ChavePixProfissional.
    /// </summary>
    public partial interface IChavePixProfissionalRepository : IRepository<ChavePixProfissional> { 
		Perlink.Trinks.ContaDigital.Factories.IChavePixProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracoesContaDigital.
    /// </summary>
    public partial interface IConfiguracoesContaDigitalRepository : IRepository<ConfiguracoesContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IConfiguracoesContaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancariaDigital.
    /// </summary>
    public partial interface IContaBancariaDigitalRepository : IRepository<ContaBancariaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IContaBancariaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaEstabelecimento.
    /// </summary>
    public partial interface IContaEstabelecimentoRepository : IRepository<ContaEstabelecimento> { 
		Perlink.Trinks.ContaDigital.Factories.IContaEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaUsuarioDigital.
    /// </summary>
    public partial interface IContaUsuarioDigitalRepository : IRepository<ContaUsuarioDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IContaUsuarioDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Dono.
    /// </summary>
    public partial interface IDonoRepository : IRepository<Dono> { 
		Perlink.Trinks.ContaDigital.Factories.IDonoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DuvidaFrequente.
    /// </summary>
    public partial interface IDuvidaFrequenteRepository : IRepository<DuvidaFrequente> { 
		Perlink.Trinks.ContaDigital.Factories.IDuvidaFrequenteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EtapaCadastro.
    /// </summary>
    public partial interface IEtapaCadastroRepository : IRepository<EtapaCadastro> { 
		Perlink.Trinks.ContaDigital.Factories.IEtapaCadastroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LimiteContaDigital.
    /// </summary>
    public partial interface ILimiteContaDigitalRepository : IRepository<LimiteContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.ILimiteContaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LimitePlano.
    /// </summary>
    public partial interface ILimitePlanoRepository : IRepository<LimitePlano> { 
		Perlink.Trinks.ContaDigital.Factories.ILimitePlanoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LOGAprovacaoTransferencias.
    /// </summary>
    public partial interface ILOGAprovacaoTransferenciasRepository : IRepository<LOGAprovacaoTransferencias> { 
		Perlink.Trinks.ContaDigital.Factories.ILOGAprovacaoTransferenciasFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Operador.
    /// </summary>
    public partial interface IOperadorRepository : IRepository<Operador> { 
		Perlink.Trinks.ContaDigital.Factories.IOperadorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAgendado.
    /// </summary>
    public partial interface IPagamentoAgendadoRepository : IRepository<PagamentoAgendado> { 
		Perlink.Trinks.ContaDigital.Factories.IPagamentoAgendadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAgendadoFolhaMesProfissional.
    /// </summary>
    public partial interface IPagamentoAgendadoFolhaMesProfissionalRepository : IRepository<PagamentoAgendadoFolhaMesProfissional> { 
		Perlink.Trinks.ContaDigital.Factories.IPagamentoAgendadoFolhaMesProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PermissaoContaDigital.
    /// </summary>
    public partial interface IPermissaoContaDigitalRepository : IRepository<PermissaoContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IPermissaoContaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PermissaoOperador.
    /// </summary>
    public partial interface IPermissaoOperadorRepository : IRepository<PermissaoOperador> { 
		Perlink.Trinks.ContaDigital.Factories.IPermissaoOperadorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Responsavel.
    /// </summary>
    public partial interface IResponsavelRepository : IRepository<Responsavel> { 
		Perlink.Trinks.ContaDigital.Factories.IResponsavelFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Transferencia.
    /// </summary>
    public partial interface ITransferenciaRepository : IRepository<Transferencia> { 
		Perlink.Trinks.ContaDigital.Factories.ITransferenciaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioContaDigital.
    /// </summary>
    public partial interface IUsuarioContaDigitalRepository : IRepository<UsuarioContaDigital> { 
		Perlink.Trinks.ContaDigital.Factories.IUsuarioContaDigitalFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ContaDigital.Builders.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.DTO.LogDTO.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Enums.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.Providers.Repositories {

 
}
namespace Perlink.Trinks.ContaDigital.VO.Repositories {

 
}
namespace Perlink.Trinks.Conteudo.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConteudoImagemLogoEstabelecimento.
    /// </summary>
    public partial interface IConteudoImagemLogoEstabelecimentoRepository : IRepository<ConteudoImagemLogoEstabelecimento> { 
		Perlink.Trinks.Conteudo.Factories.IConteudoImagemLogoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConteudoTexto.
    /// </summary>
    public partial interface IConteudoTextoRepository : IRepository<ConteudoTexto> { 
		Perlink.Trinks.Conteudo.Factories.IConteudoTextoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MenuItem.
    /// </summary>
    public partial interface IMenuItemRepository : IRepository<MenuItem> { 
		Perlink.Trinks.Conteudo.Factories.IMenuItemFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MenuOpcaoAliasBusca.
    /// </summary>
    public partial interface IMenuOpcaoAliasBuscaRepository : IRepository<MenuOpcaoAliasBusca> { 
		Perlink.Trinks.Conteudo.Factories.IMenuOpcaoAliasBuscaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MenuOpcaoBusca.
    /// </summary>
    public partial interface IMenuOpcaoBuscaRepository : IRepository<MenuOpcaoBusca> { 
		Perlink.Trinks.Conteudo.Factories.IMenuOpcaoBuscaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Conteudo.DTO.Repositories {

 
}
namespace Perlink.Trinks.Controle.Repositories {

    /// <summary>
    /// Interface para repositório da entidade PalavraProibida.
    /// </summary>
    public partial interface IPalavraProibidaRepository : IRepository<PalavraProibida> { 
		Perlink.Trinks.Controle.Factories.IPalavraProibidaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PalavraProibidaSMS.
    /// </summary>
    public partial interface IPalavraProibidaSMSRepository : IRepository<PalavraProibidaSMS> { 
		Perlink.Trinks.Controle.Factories.IPalavraProibidaSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PalavraProibidaTrinks.
    /// </summary>
    public partial interface IPalavraProibidaTrinksRepository : IRepository<PalavraProibidaTrinks> { 
		Perlink.Trinks.Controle.Factories.IPalavraProibidaTrinksFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ControleDeCTAs.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CTA.
    /// </summary>
    public partial interface ICTARepository : IRepository<CTA> { 
		Perlink.Trinks.ControleDeCTAs.Factories.ICTAFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CTAGrupo.
    /// </summary>
    public partial interface ICTAGrupoRepository : IRepository<CTAGrupo> { 
		Perlink.Trinks.ControleDeCTAs.Factories.ICTAGrupoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CTAInformacoesAdicionaisTrinksPro.
    /// </summary>
    public partial interface ICTAInformacoesAdicionaisTrinksProRepository : IRepository<CTAInformacoesAdicionaisTrinksPro> { 
		Perlink.Trinks.ControleDeCTAs.Factories.ICTAInformacoesAdicionaisTrinksProFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ControleDeCTAs.DTOs.Repositories {

 
}
namespace Perlink.Trinks.ControleDeCTAs.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeCTAs.Geradores.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Enum.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ControleDeEntradaESaida.Repositories {

    /// <summary>
    /// Interface para repositório da entidade LancamentoAporte.
    /// </summary>
    public partial interface ILancamentoAporteRepository : IRepository<LancamentoAporte> { 
		Perlink.Trinks.ControleDeEntradaESaida.Factories.ILancamentoAporteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoDeReceitaCategoria.
    /// </summary>
    public partial interface ILancamentoDeReceitaCategoriaRepository : IRepository<LancamentoDeReceitaCategoria> { 
		Perlink.Trinks.ControleDeEntradaESaida.Factories.ILancamentoDeReceitaCategoriaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ControleDeFotos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ArquivoDeImagem.
    /// </summary>
    public partial interface IArquivoDeImagemRepository : IRepository<ArquivoDeImagem> { 
		Perlink.Trinks.ControleDeFotos.Factories.IArquivoDeImagemFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ControleDeFotos.Controladores.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFotos.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFotos.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFotos.Factories.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Repositories {

    /// <summary>
    /// Interface para repositório da entidade DisponibilidadeEspecifica.
    /// </summary>
    public partial interface IDisponibilidadeEspecificaRepository : IRepository<DisponibilidadeEspecifica> { 
		Perlink.Trinks.ControleDeFuncionalidades.Factories.IDisponibilidadeEspecificaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DisponibilidadeGeral.
    /// </summary>
    public partial interface IDisponibilidadeGeralRepository : IRepository<DisponibilidadeGeral> { 
		Perlink.Trinks.ControleDeFuncionalidades.Factories.IDisponibilidadeGeralFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreferenciasDaConta.
    /// </summary>
    public partial interface IPreferenciasDaContaRepository : IRepository<PreferenciasDaConta> { 
		Perlink.Trinks.ControleDeFuncionalidades.Factories.IPreferenciasDaContaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeConfiguracaoEspecifica.
    /// </summary>
    public partial interface IValorDeConfiguracaoEspecificaRepository : IRepository<ValorDeConfiguracaoEspecifica> { 
		Perlink.Trinks.ControleDeFuncionalidades.Factories.IValorDeConfiguracaoEspecificaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeConfiguracaoGeral.
    /// </summary>
    public partial interface IValorDeConfiguracaoGeralRepository : IRepository<ValorDeConfiguracaoGeral> { 
		Perlink.Trinks.ControleDeFuncionalidades.Factories.IValorDeConfiguracaoGeralFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor.Repositories {

 
}
namespace Perlink.Trinks.ControleDeFuncionalidades.Stories.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AvaliacaoDeSatisfacao.
    /// </summary>
    public partial interface IAvaliacaoDeSatisfacaoRepository : IRepository<AvaliacaoDeSatisfacao> { 
		Perlink.Trinks.ControleDeSatisfacao.Factories.IAvaliacaoDeSatisfacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AvaliacaoDeSatisfacaoRecebidaRetentativa.
    /// </summary>
    public partial interface IAvaliacaoDeSatisfacaoRecebidaRetentativaRepository : IRepository<AvaliacaoDeSatisfacaoRecebidaRetentativa> { 
		Perlink.Trinks.ControleDeSatisfacao.Factories.IAvaliacaoDeSatisfacaoRecebidaRetentativaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Contato.
    /// </summary>
    public partial interface IContatoRepository : IRepository<Contato> { 
		Perlink.Trinks.ControleDeSatisfacao.Factories.IContatoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContatoCelular.
    /// </summary>
    public partial interface IContatoCelularRepository : IRepository<ContatoCelular> { 
		Perlink.Trinks.ControleDeSatisfacao.Factories.IContatoCelularFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemParaAvaliar.
    /// </summary>
    public partial interface IItemParaAvaliarRepository : IRepository<ItemParaAvaliar> { 
		Perlink.Trinks.ControleDeSatisfacao.Factories.IItemParaAvaliarFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ControleDeSatisfacao.DTO.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Enums.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Factories.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ControleDeSatisfacao.Stories.Repositories {

 
}
namespace Perlink.Trinks.Correios.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConsultaCep.
    /// </summary>
    public partial interface IConsultaCepRepository : IRepository<ConsultaCep> { 
		Perlink.Trinks.Correios.Factories.IConsultaCepFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Cupom.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CupomBase.
    /// </summary>
    public partial interface ICupomBaseRepository : IRepository<CupomBase> { 
		Perlink.Trinks.Cupom.Factories.ICupomBaseFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomDesconto.
    /// </summary>
    public partial interface ICupomDescontoRepository : IRepository<CupomDesconto> { 
		Perlink.Trinks.Cupom.Factories.ICupomDescontoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomEstabelecimento.
    /// </summary>
    public partial interface ICupomEstabelecimentoRepository : IRepository<CupomEstabelecimento> { 
		Perlink.Trinks.Cupom.Factories.ICupomEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomEstabelecimentoProduto.
    /// </summary>
    public partial interface ICupomEstabelecimentoProdutoRepository : IRepository<CupomEstabelecimentoProduto> { 
		Perlink.Trinks.Cupom.Factories.ICupomEstabelecimentoProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomHorarioTransacao.
    /// </summary>
    public partial interface ICupomHorarioTransacaoRepository : IRepository<CupomHorarioTransacao> { 
		Perlink.Trinks.Cupom.Factories.ICupomHorarioTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomItemVenda.
    /// </summary>
    public partial interface ICupomItemVendaRepository : IRepository<CupomItemVenda> { 
		Perlink.Trinks.Cupom.Factories.ICupomItemVendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomPessoaFisica.
    /// </summary>
    public partial interface ICupomPessoaFisicaRepository : IRepository<CupomPessoaFisica> { 
		Perlink.Trinks.Cupom.Factories.ICupomPessoaFisicaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomServicoEstabelecimento.
    /// </summary>
    public partial interface ICupomServicoEstabelecimentoRepository : IRepository<CupomServicoEstabelecimento> { 
		Perlink.Trinks.Cupom.Factories.ICupomServicoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CupomUsoPessoaFisica.
    /// </summary>
    public partial interface ICupomUsoPessoaFisicaRepository : IRepository<CupomUsoPessoaFisica> { 
		Perlink.Trinks.Cupom.Factories.ICupomUsoPessoaFisicaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Cupom.DTO.Repositories {

 
}
namespace Perlink.Trinks.Cupom.Enums.Repositories {

 
}
namespace Perlink.Trinks.Cupom.Filters.Repositories {

 
}
namespace Perlink.Trinks.Cupom.Models.Repositories {

 
}
namespace Perlink.Trinks.Dashboard.DTOs.Repositories {

 
}
namespace Perlink.Trinks.Dashboard.Repositories {

 
}
namespace Perlink.Trinks.Repositories {

 
}
namespace Perlink.Trinks.DataQuery.DTO.Repositories {

 
}
namespace Perlink.Trinks.DataQuery.Repositories {

 
}
namespace Perlink.Trinks.DataQuery.Strategies.Repositories {

 
}
namespace Perlink.Trinks.DebitoParcial.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AbatimentoDeDivida.
    /// </summary>
    public partial interface IAbatimentoDeDividaRepository : IRepository<AbatimentoDeDivida> { 
		Perlink.Trinks.DebitoParcial.Factories.IAbatimentoDeDividaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DividaDeixadaNoEstabelecimento.
    /// </summary>
    public partial interface IDividaDeixadaNoEstabelecimentoRepository : IRepository<DividaDeixadaNoEstabelecimento> { 
		Perlink.Trinks.DebitoParcial.Factories.IDividaDeixadaNoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDaDivida.
    /// </summary>
    public partial interface IHistoricoDaDividaRepository : IRepository<HistoricoDaDivida> { 
		Perlink.Trinks.DebitoParcial.Factories.IHistoricoDaDividaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoDeDividaPeloCliente.
    /// </summary>
    public partial interface IPagamentoDeDividaPeloClienteRepository : IRepository<PagamentoDeDividaPeloCliente> { 
		Perlink.Trinks.DebitoParcial.Factories.IPagamentoDeDividaPeloClienteFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.DebitoParcial.DTO.Repositories {

 
}
namespace Perlink.Trinks.DebitoParcial.Filtros.Repositories {

 
}
namespace Perlink.Trinks.DebitoParcial.Stories.Repositories {

 
}
namespace Perlink.Trinks.Despesas.Repositories {

    /// <summary>
    /// Interface para repositório da entidade LancamentosRecorrentesSelecionadas.
    /// </summary>
    public partial interface ILancamentosRecorrentesSelecionadasRepository : IRepository<LancamentosRecorrentesSelecionadas> { 
		Perlink.Trinks.Despesas.Factories.ILancamentosRecorrentesSelecionadasFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Lancamento.
    /// </summary>
    public partial interface ILancamentoRepository : IRepository<Lancamento> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoCategoria.
    /// </summary>
    public partial interface ILancamentoCategoriaRepository : IRepository<LancamentoCategoria> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoCategoriaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoCategoriaPadrao.
    /// </summary>
    public partial interface ILancamentoCategoriaPadraoRepository : IRepository<LancamentoCategoriaPadrao> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoCategoriaPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoGeradoPorMovimentacaoEstoque.
    /// </summary>
    public partial interface ILancamentoGeradoPorMovimentacaoEstoqueRepository : IRepository<LancamentoGeradoPorMovimentacaoEstoque> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoGeradoPorMovimentacaoEstoqueFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoGrupo.
    /// </summary>
    public partial interface ILancamentoGrupoRepository : IRepository<LancamentoGrupo> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoGrupoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoGrupoPadrao.
    /// </summary>
    public partial interface ILancamentoGrupoPadraoRepository : IRepository<LancamentoGrupoPadrao> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoGrupoPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoRecorrencia.
    /// </summary>
    public partial interface ILancamentoRecorrenciaRepository : IRepository<LancamentoRecorrencia> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoRecorrenciaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoRecorrenciaTipo.
    /// </summary>
    public partial interface ILancamentoRecorrenciaTipoRepository : IRepository<LancamentoRecorrenciaTipo> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoRecorrenciaTipoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoStatusPagamento.
    /// </summary>
    public partial interface ILancamentoStatusPagamentoRepository : IRepository<LancamentoStatusPagamento> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoStatusPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoTipo.
    /// </summary>
    public partial interface ILancamentoTipoRepository : IRepository<LancamentoTipo> { 
		Perlink.Trinks.Despesas.Factories.ILancamentoTipoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RenovacaoDeLancamentos.
    /// </summary>
    public partial interface IRenovacaoDeLancamentosRepository : IRepository<RenovacaoDeLancamentos> { 
		Perlink.Trinks.Despesas.Factories.IRenovacaoDeLancamentosFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Despesas.DTO.Repositories {

 
}
namespace Perlink.Trinks.Despesas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Despesas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Disponibilidade.Adapters.Repositories {

 
}
namespace Perlink.Trinks.Disponibilidade.Repositories {

 
}
namespace Perlink.Trinks.Disponibilidade.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Dispositivos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Dispositivos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade TipoImpressao.
    /// </summary>
    public partial interface ITipoImpressaoRepository : IRepository<TipoImpressao> { 
		Perlink.Trinks.Dispositivos.Factories.ITipoImpressaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.DTO.Repositories {

    /// <summary>
    /// Interface para repositório da entidade HorarioFuturosExportacao.
    /// </summary>
    public partial interface IHorarioFuturosExportacaoRepository : IRepository<HorarioFuturosExportacao> { 
		Perlink.Trinks.DTO.Factories.IHorarioFuturosExportacaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.DTO.PushSNS.Repositories {

 
}
namespace Perlink.Trinks.Encurtador.DTO.Repositories {

 
}
namespace Perlink.Trinks.Encurtador.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EncurtadorDeDados.
    /// </summary>
    public partial interface IEncurtadorDeDadosRepository : IRepository<EncurtadorDeDados> { 
		Perlink.Trinks.Encurtador.Factories.IEncurtadorDeDadosFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Encurtador.Enums.Repositories {

 
}
namespace Perlink.Trinks.Encurtador.Factories.Repositories {

 
}
namespace Perlink.Trinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.Env.Clock.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Antifraude.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Enums.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Repositories {

 
}
namespace Perlink.Trinks.EnvioMensagem.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AvaliacaoEstabelecimento.
    /// </summary>
    public partial interface IAvaliacaoEstabelecimentoRepository : IRepository<AvaliacaoEstabelecimento> { 
		Perlink.Trinks.Estabelecimentos.Factories.IAvaliacaoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDeAvaliacaoDeSatisfacao.
    /// </summary>
    public partial interface IConfiguracaoDeAvaliacaoDeSatisfacaoRepository : IRepository<ConfiguracaoDeAvaliacaoDeSatisfacao> { 
		Perlink.Trinks.Estabelecimentos.Factories.IConfiguracaoDeAvaliacaoDeSatisfacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoEstabelecimento.
    /// </summary>
    public partial interface IConfiguracaoEstabelecimentoRepository : IRepository<ConfiguracaoEstabelecimento> { 
		Perlink.Trinks.Estabelecimentos.Factories.IConfiguracaoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFavorito.
    /// </summary>
    public partial interface IEstabelecimentoFavoritoRepository : IRepository<EstabelecimentoFavorito> { 
		Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoFavoritoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalFavorito.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalFavoritoRepository : IRepository<EstabelecimentoProfissionalFavorito> { 
		Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoProfissionalFavoritoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoUUID.
    /// </summary>
    public partial interface IEstabelecimentoUUIDRepository : IRepository<EstabelecimentoUUID> { 
		Perlink.Trinks.Estabelecimentos.Factories.IEstabelecimentoUUIDFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemConfiguradoParaSerAvaliado.
    /// </summary>
    public partial interface IItemConfiguradoParaSerAvaliadoRepository : IRepository<ItemConfiguradoParaSerAvaliado> { 
		Perlink.Trinks.Estabelecimentos.Factories.IItemConfiguradoParaSerAvaliadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoConfiguradoParaSerAvaliado.
    /// </summary>
    public partial interface IServicoConfiguradoParaSerAvaliadoRepository : IRepository<ServicoConfiguradoParaSerAvaliado> { 
		Perlink.Trinks.Estabelecimentos.Factories.IServicoConfiguradoParaSerAvaliadoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Estabelecimentos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.ElasticSearch.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoComInformacoesConsolidadas.
    /// </summary>
    public partial interface IEstabelecimentoComInformacoesConsolidadasRepository : IRepository<EstabelecimentoComInformacoesConsolidadas> { 
		Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IEstabelecimentoComInformacoesConsolidadasFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade InformacoesConsolidadasDaBuscaDoPortal.
    /// </summary>
    public partial interface IInformacoesConsolidadasDaBuscaDoPortalRepository : IRepository<InformacoesConsolidadasDaBuscaDoPortal> { 
		Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IInformacoesConsolidadasDaBuscaDoPortalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OpcaoDeAutocompletarDoPortal.
    /// </summary>
    public partial interface IOpcaoDeAutocompletarDoPortalRepository : IRepository<OpcaoDeAutocompletarDoPortal> { 
		Perlink.Trinks.Estabelecimentos.ElasticSearch.Factories.IOpcaoDeAutocompletarDoPortalFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Estabelecimentos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Factories.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Estabelecimentos.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.Estatistica.Repositories {

    /// <summary>
    /// Interface para repositório da entidade BIUsoDoSistema.
    /// </summary>
    public partial interface IBIUsoDoSistemaRepository : IRepository<BIUsoDoSistema> { 
		Perlink.Trinks.Estatistica.Factories.IBIUsoDoSistemaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.EstilosVisuais.Enums.Repositories {

 
}
namespace Perlink.Trinks.EstilosVisuais.Repositories {

    /// <summary>
    /// Interface para repositório da entidade TemaCss.
    /// </summary>
    public partial interface ITemaCssRepository : IRepository<TemaCss> { 
		Perlink.Trinks.EstilosVisuais.Factories.ITemaCssFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TemaCssBackoffice.
    /// </summary>
    public partial interface ITemaCssBackofficeRepository : IRepository<TemaCssBackoffice> { 
		Perlink.Trinks.EstilosVisuais.Factories.ITemaCssBackofficeFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Calculos.Repositories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDoServico.
    /// </summary>
    public partial interface IConfiguracaoDoServicoRepository : IRepository<ConfiguracaoDoServico> { 
		Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IConfiguracaoDoServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoParaBaixaAutomatica.
    /// </summary>
    public partial interface IConfiguracaoParaBaixaAutomaticaRepository : IRepository<ConfiguracaoParaBaixaAutomatica> { 
		Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IConfiguracaoParaBaixaAutomaticaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario.
    /// </summary>
    public partial interface IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioRepository : IRepository<HistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorario> { 
		Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IHistoricoMovimentacaoEstoquePeloUsoDeProdutoNoHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoDeUsoDeProdutoNoHorario.
    /// </summary>
    public partial interface IHistoricoDeUsoDeProdutoNoHorarioRepository : IRepository<HistoricoDeUsoDeProdutoNoHorario> { 
		Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IHistoricoDeUsoDeProdutoNoHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemConfiguradoParaBaixaAutomatica.
    /// </summary>
    public partial interface IItemConfiguradoParaBaixaAutomaticaRepository : IRepository<ItemConfiguradoParaBaixaAutomatica> { 
		Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IItemConfiguradoParaBaixaAutomaticaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsoDeProdutoNoHorario.
    /// </summary>
    public partial interface IUsoDeProdutoNoHorarioRepository : IRepository<UsoDeProdutoNoHorario> { 
		Perlink.Trinks.EstoqueComBaixaAutomatica.Factories.IUsoDeProdutoNoHorarioFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.DTO.Repositories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Enums.Repositories {

 
}
namespace Perlink.Trinks.EstoqueComBaixaAutomatica.Stories.Repositories {

 
}
namespace Perlink.Trinks.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Extratores.DTO.Repositories {

 
}
namespace Perlink.Trinks.Extratores.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Extrator.
    /// </summary>
    public partial interface IExtratorRepository : IRepository<Extrator> { 
		Perlink.Trinks.Extratores.Factories.IExtratorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Visao.
    /// </summary>
    public partial interface IVisaoRepository : IRepository<Visao> { 
		Perlink.Trinks.Extratores.Factories.IVisaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Facebook.DTOs.Repositories {

 
}
namespace Perlink.Trinks.Facebook.Enums.Repositories {

 
}
namespace Perlink.Trinks.Facebook.Repositories {

    /// <summary>
    /// Interface para repositório da entidade FBE.
    /// </summary>
    public partial interface IFBERepository : IRepository<FBE> { 
		Perlink.Trinks.Facebook.Factories.IFBEFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.FAQ.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Assunto.
    /// </summary>
    public partial interface IAssuntoRepository : IRepository<Assunto> { 
		Perlink.Trinks.FAQ.Factories.IAssuntoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AssuntoPerguntaResposta.
    /// </summary>
    public partial interface IAssuntoPerguntaRespostaRepository : IRepository<AssuntoPerguntaResposta> { 
		Perlink.Trinks.FAQ.Factories.IAssuntoPerguntaRespostaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PerguntaResposta.
    /// </summary>
    public partial interface IPerguntaRespostaRepository : IRepository<PerguntaResposta> { 
		Perlink.Trinks.FAQ.Factories.IPerguntaRespostaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Tela.
    /// </summary>
    public partial interface ITelaRepository : IRepository<Tela> { 
		Perlink.Trinks.FAQ.Factories.ITelaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TelaAssunto.
    /// </summary>
    public partial interface ITelaAssuntoRepository : IRepository<TelaAssunto> { 
		Perlink.Trinks.FAQ.Factories.ITelaAssuntoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TelaPerguntaResposta.
    /// </summary>
    public partial interface ITelaPerguntaRespostaRepository : IRepository<TelaPerguntaResposta> { 
		Perlink.Trinks.FAQ.Factories.ITelaPerguntaRespostaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Fidelidade.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AgendamentoOnlineQueGerouPontos.
    /// </summary>
    public partial interface IAgendamentoOnlineQueGerouPontosRepository : IRepository<AgendamentoOnlineQueGerouPontos> { 
		Perlink.Trinks.Fidelidade.Factories.IAgendamentoOnlineQueGerouPontosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontos.
    /// </summary>
    public partial interface IMovimentacaoDePontosRepository : IRepository<MovimentacaoDePontos> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosAgendamentoOnline.
    /// </summary>
    public partial interface IMovimentacaoDePontosAgendamentoOnlineRepository : IRepository<MovimentacaoDePontosAgendamentoOnline> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosAgendamentoOnlineFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosAvulso.
    /// </summary>
    public partial interface IMovimentacaoDePontosAvulsoRepository : IRepository<MovimentacaoDePontosAvulso> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosAvulsoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosHorarioTransacao.
    /// </summary>
    public partial interface IMovimentacaoDePontosHorarioTransacaoRepository : IRepository<MovimentacaoDePontosHorarioTransacao> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosHorarioTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosItemVenda.
    /// </summary>
    public partial interface IMovimentacaoDePontosItemVendaRepository : IRepository<MovimentacaoDePontosItemVenda> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosItemVendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDePontosPagamentoAntecipado.
    /// </summary>
    public partial interface IMovimentacaoDePontosPagamentoAntecipadoRepository : IRepository<MovimentacaoDePontosPagamentoAntecipado> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDePontosPagamentoAntecipadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDeTransferenciaDePontos.
    /// </summary>
    public partial interface IMovimentacaoDeTransferenciaDePontosRepository : IRepository<MovimentacaoDeTransferenciaDePontos> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDeTransferenciaDePontosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoDeUmPontoGanho.
    /// </summary>
    public partial interface IMovimentacaoDeUmPontoGanhoRepository : IRepository<MovimentacaoDeUmPontoGanho> { 
		Perlink.Trinks.Fidelidade.Factories.IMovimentacaoDeUmPontoGanhoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipadoQueGerouPontos.
    /// </summary>
    public partial interface IPagamentoAntecipadoQueGerouPontosRepository : IRepository<PagamentoAntecipadoQueGerouPontos> { 
		Perlink.Trinks.Fidelidade.Factories.IPagamentoAntecipadoQueGerouPontosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PontoGanho.
    /// </summary>
    public partial interface IPontoGanhoRepository : IRepository<PontoGanho> { 
		Perlink.Trinks.Fidelidade.Factories.IPontoGanhoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ProgramaDeFidelidade.
    /// </summary>
    public partial interface IProgramaDeFidelidadeRepository : IRepository<ProgramaDeFidelidade> { 
		Perlink.Trinks.Fidelidade.Factories.IProgramaDeFidelidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ProgramaDeFidelidadeDiaSemana.
    /// </summary>
    public partial interface IProgramaDeFidelidadeDiaSemanaRepository : IRepository<ProgramaDeFidelidadeDiaSemana> { 
		Perlink.Trinks.Fidelidade.Factories.IProgramaDeFidelidadeDiaSemanaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransferenciaDePontos.
    /// </summary>
    public partial interface ITransferenciaDePontosRepository : IRepository<TransferenciaDePontos> { 
		Perlink.Trinks.Fidelidade.Factories.ITransferenciaDePontosFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Fidelidade.DTO.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Enums.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Factories.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Fidelidade.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AberturaFechamentoCaixa.
    /// </summary>
    public partial interface IAberturaFechamentoCaixaRepository : IRepository<AberturaFechamentoCaixa> { 
		Perlink.Trinks.Financeiro.Factories.IAberturaFechamentoCaixaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AberturaFechamentoCaixaHistorico.
    /// </summary>
    public partial interface IAberturaFechamentoCaixaHistoricoRepository : IRepository<AberturaFechamentoCaixaHistorico> { 
		Perlink.Trinks.Financeiro.Factories.IAberturaFechamentoCaixaHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Comissao.
    /// </summary>
    public partial interface IComissaoRepository : IRepository<Comissao> { 
		Perlink.Trinks.Financeiro.Factories.IComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancariaPessoa.
    /// </summary>
    public partial interface IContaBancariaPessoaRepository : IRepository<ContaBancariaPessoa> { 
		Perlink.Trinks.Financeiro.Factories.IContaBancariaPessoaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FechamentoFolhaMes.
    /// </summary>
    public partial interface IFechamentoFolhaMesRepository : IRepository<FechamentoFolhaMes> { 
		Perlink.Trinks.Financeiro.Factories.IFechamentoFolhaMesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FechamentoFolhaMesProfissional.
    /// </summary>
    public partial interface IFechamentoFolhaMesProfissionalRepository : IRepository<FechamentoFolhaMesProfissional> { 
		Perlink.Trinks.Financeiro.Factories.IFechamentoFolhaMesProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormaPagamento.
    /// </summary>
    public partial interface IFormaPagamentoRepository : IRepository<FormaPagamento> { 
		Perlink.Trinks.Financeiro.Factories.IFormaPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormaPagamentoTipo.
    /// </summary>
    public partial interface IFormaPagamentoTipoRepository : IRepository<FormaPagamentoTipo> { 
		Perlink.Trinks.Financeiro.Factories.IFormaPagamentoTipoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Gorjeta.
    /// </summary>
    public partial interface IGorjetaRepository : IRepository<Gorjeta> { 
		Perlink.Trinks.Financeiro.Factories.IGorjetaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LancamentoDeAntecipacao.
    /// </summary>
    public partial interface ILancamentoDeAntecipacaoRepository : IRepository<LancamentoDeAntecipacao> { 
		Perlink.Trinks.Financeiro.Factories.ILancamentoDeAntecipacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDesconto.
    /// </summary>
    public partial interface IMotivoDescontoRepository : IRepository<MotivoDesconto> { 
		Perlink.Trinks.Financeiro.Factories.IMotivoDescontoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoFolhaMesProfissional.
    /// </summary>
    public partial interface IPagamentoFolhaMesProfissionalRepository : IRepository<PagamentoFolhaMesProfissional> { 
		Perlink.Trinks.Financeiro.Factories.IPagamentoFolhaMesProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Sangria.
    /// </summary>
    public partial interface ISangriaRepository : IRepository<Sangria> { 
		Perlink.Trinks.Financeiro.Factories.ISangriaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SangriaHistorico.
    /// </summary>
    public partial interface ISangriaHistoricoRepository : IRepository<SangriaHistorico> { 
		Perlink.Trinks.Financeiro.Factories.ISangriaHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoContaBancaria.
    /// </summary>
    public partial interface ITipoContaBancariaRepository : IRepository<TipoContaBancaria> { 
		Perlink.Trinks.Financeiro.Factories.ITipoContaBancariaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoTransacao.
    /// </summary>
    public partial interface ITipoTransacaoRepository : IRepository<TipoTransacao> { 
		Perlink.Trinks.Financeiro.Factories.ITipoTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Transacao.
    /// </summary>
    public partial interface ITransacaoRepository : IRepository<Transacao> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoFormaPagamento.
    /// </summary>
    public partial interface ITransacaoFormaPagamentoRepository : IRepository<TransacaoFormaPagamento> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoFormaPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoFormaPagamentoParcela.
    /// </summary>
    public partial interface ITransacaoFormaPagamentoParcelaRepository : IRepository<TransacaoFormaPagamentoParcela> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoFormaPagamentoParcelaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoHistorico.
    /// </summary>
    public partial interface ITransacaoHistoricoRepository : IRepository<TransacaoHistorico> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoItem.
    /// </summary>
    public partial interface ITransacaoItemRepository : IRepository<TransacaoItem> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoItemFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoLancamentoFinanceiro.
    /// </summary>
    public partial interface ITransacaoLancamentoFinanceiroRepository : IRepository<TransacaoLancamentoFinanceiro> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoLancamentoFinanceiroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPOS.
    /// </summary>
    public partial interface ITransacaoPOSRepository : IRepository<TransacaoPOS> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoPOSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPOSSplit.
    /// </summary>
    public partial interface ITransacaoPOSSplitRepository : IRepository<TransacaoPOSSplit> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoPOSSplitFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPosWebhookRequest.
    /// </summary>
    public partial interface ITransacaoPosWebhookRequestRepository : IRepository<TransacaoPosWebhookRequest> { 
		Perlink.Trinks.Financeiro.Factories.ITransacaoPosWebhookRequestFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorDeComissaoAReceber.
    /// </summary>
    public partial interface IValorDeComissaoAReceberRepository : IRepository<ValorDeComissaoAReceber> { 
		Perlink.Trinks.Financeiro.Factories.IValorDeComissaoAReceberFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Financeiro.Adapters.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Calculos.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Repositories {

    /// <summary>
    /// Interface para repositório da entidade HistoricoDoCaixaPorOperador.
    /// </summary>
    public partial interface IHistoricoDoCaixaPorOperadorRepository : IRepository<HistoricoDoCaixaPorOperador> { 
		Perlink.Trinks.Financeiro.Factories.IHistoricoDoCaixaPorOperadorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoCaixaPorOperador.
    /// </summary>
    public partial interface IMovimentacaoNoCaixaPorOperadorRepository : IRepository<MovimentacaoNoCaixaPorOperador> { 
		Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoCaixaPorOperadorLancamento.
    /// </summary>
    public partial interface IMovimentacaoNoCaixaPorOperadorLancamentoRepository : IRepository<MovimentacaoNoCaixaPorOperadorLancamento> { 
		Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorLancamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoCaixaPorOperadorTransacao.
    /// </summary>
    public partial interface IMovimentacaoNoCaixaPorOperadorTransacaoRepository : IRepository<MovimentacaoNoCaixaPorOperadorTransacao> { 
		Perlink.Trinks.Financeiro.Factories.IMovimentacaoNoCaixaPorOperadorTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RegistroDeCaixaPorOperador.
    /// </summary>
    public partial interface IRegistroDeCaixaPorOperadorRepository : IRepository<RegistroDeCaixaPorOperador> { 
		Perlink.Trinks.Financeiro.Factories.IRegistroDeCaixaPorOperadorFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Financeiro.Repositories {

    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizado.
    /// </summary>
    public partial interface IDescontoPersonalizadoRepository : IRepository<DescontoPersonalizado> { 
		Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoAssistentes.
    /// </summary>
    public partial interface IDescontoPersonalizadoAssistentesRepository : IRepository<DescontoPersonalizadoAssistentes> { 
		Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoAssistentesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoPacote.
    /// </summary>
    public partial interface IDescontoPersonalizadoPacoteRepository : IRepository<DescontoPersonalizadoPacote> { 
		Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoProduto.
    /// </summary>
    public partial interface IDescontoPersonalizadoProdutoRepository : IRepository<DescontoPersonalizadoProduto> { 
		Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoProfissionais.
    /// </summary>
    public partial interface IDescontoPersonalizadoProfissionaisRepository : IRepository<DescontoPersonalizadoProfissionais> { 
		Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoProfissionaisFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescontoPersonalizadoServico.
    /// </summary>
    public partial interface IDescontoPersonalizadoServicoRepository : IRepository<DescontoPersonalizadoServico> { 
		Perlink.Trinks.Financeiro.Factories.IDescontoPersonalizadoServicoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Financeiro.DTO.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.CalculoComissao.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Checkout.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Connect.Pagarme.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.Connect.Stone.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.DescontosPersonalizados.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.DTO.POS.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Enums.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Factories.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Repositories {

    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoCompraProduto.
    /// </summary>
    public partial interface IFolhaPagamentoCompraProdutoRepository : IRepository<FolhaPagamentoCompraProduto> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoCompraProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItem.
    /// </summary>
    public partial interface IFolhaPagamentoItemRepository : IRepository<FolhaPagamentoItem> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemBonificacao.
    /// </summary>
    public partial interface IFolhaPagamentoItemBonificacaoRepository : IRepository<FolhaPagamentoItemBonificacao> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemBonificacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemGorjeta.
    /// </summary>
    public partial interface IFolhaPagamentoItemGorjetaRepository : IRepository<FolhaPagamentoItemGorjeta> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemGorjetaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemSplit.
    /// </summary>
    public partial interface IFolhaPagamentoItemSplitRepository : IRepository<FolhaPagamentoItemSplit> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemSplitFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoItemVale.
    /// </summary>
    public partial interface IFolhaPagamentoItemValeRepository : IRepository<FolhaPagamentoItemVale> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoItemValeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FolhaPagamentoLancamento.
    /// </summary>
    public partial interface IFolhaPagamentoLancamentoRepository : IRepository<FolhaPagamentoLancamento> { 
		Perlink.Trinks.Financeiro.Factories.IFolhaPagamentoLancamentoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Financeiro.POS.Repositories {

 
}
namespace Perlink.Trinks.Financeiro.Stories.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AssinaturaDigital.
    /// </summary>
    public partial interface IAssinaturaDigitalRepository : IRepository<AssinaturaDigital> { 
		Perlink.Trinks.Formulario.Factories.IAssinaturaDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDoFormulario.
    /// </summary>
    public partial interface IConfiguracaoDoFormularioRepository : IRepository<ConfiguracaoDoFormulario> { 
		Perlink.Trinks.Formulario.Factories.IConfiguracaoDoFormularioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormularioDinamico.
    /// </summary>
    public partial interface IFormularioDinamicoRepository : IRepository<FormularioDinamico> { 
		Perlink.Trinks.Formulario.Factories.IFormularioDinamicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormularioRespondido.
    /// </summary>
    public partial interface IFormularioRespondidoRepository : IRepository<FormularioRespondido> { 
		Perlink.Trinks.Formulario.Factories.IFormularioRespondidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OpcaoDeResposta.
    /// </summary>
    public partial interface IOpcaoDeRespostaRepository : IRepository<OpcaoDeResposta> { 
		Perlink.Trinks.Formulario.Factories.IOpcaoDeRespostaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaPerguntada.
    /// </summary>
    public partial interface IPessoaPerguntadaRepository : IRepository<PessoaPerguntada> { 
		Perlink.Trinks.Formulario.Factories.IPessoaPerguntadaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestaoDoFormulario.
    /// </summary>
    public partial interface IQuestaoDoFormularioRepository : IRepository<QuestaoDoFormulario> { 
		Perlink.Trinks.Formulario.Factories.IQuestaoDoFormularioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RespostaDoFormulario.
    /// </summary>
    public partial interface IRespostaDoFormularioRepository : IRepository<RespostaDoFormulario> { 
		Perlink.Trinks.Formulario.Factories.IRespostaDoFormularioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SolicitacaoDeAssinatura.
    /// </summary>
    public partial interface ISolicitacaoDeAssinaturaRepository : IRepository<SolicitacaoDeAssinatura> { 
		Perlink.Trinks.Formulario.Factories.ISolicitacaoDeAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDeResposta.
    /// </summary>
    public partial interface ITipoDeRespostaRepository : IRepository<TipoDeResposta> { 
		Perlink.Trinks.Formulario.Factories.ITipoDeRespostaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VersaoDaQuestaoDoFormulario.
    /// </summary>
    public partial interface IVersaoDaQuestaoDoFormularioRepository : IRepository<VersaoDaQuestaoDoFormulario> { 
		Perlink.Trinks.Formulario.Factories.IVersaoDaQuestaoDoFormularioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VersaoDoFormularioDinamico.
    /// </summary>
    public partial interface IVersaoDoFormularioDinamicoRepository : IRepository<VersaoDoFormularioDinamico> { 
		Perlink.Trinks.Formulario.Factories.IVersaoDoFormularioDinamicoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Formulario.DTO.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Enums.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Formulario.Stories.Repositories {

 
}
namespace Perlink.Trinks.Fotos.ControleDeFotos.Repositories {

 
}
namespace Perlink.Trinks.Fotos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Fotos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Fotos.Factories.Repositories {

 
}
namespace Perlink.Trinks.Fotos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Foto.
    /// </summary>
    public partial interface IFotoRepository : IRepository<Foto> { 
		Perlink.Trinks.Fotos.Factories.IFotoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Fotos.Repositories {

 
}
namespace Perlink.Trinks.Google.Comparers.Repositories {

 
}
namespace Perlink.Trinks.Google.DTO.Repositories {

 
}
namespace Perlink.Trinks.Google.Enums.Repositories {

 
}
namespace Perlink.Trinks.Google.Providers.Repositories {

 
}
namespace Perlink.Trinks.GyraMais.Repositories {

    /// <summary>
    /// Interface para repositório da entidade DadosCliente.
    /// </summary>
    public partial interface IDadosClienteRepository : IRepository<DadosCliente> { 
		Perlink.Trinks.GyraMais.Factories.IDadosClienteFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Identity.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ApiAccount.
    /// </summary>
    public partial interface IApiAccountRepository : IRepository<ApiAccount> { 
		Perlink.Trinks.Identity.Factories.IApiAccountFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Importacao.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Conversores.Repositories {

 
}
namespace Perlink.Trinks.Importacao.DTO.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Importadores.Repositories {

 
}
namespace Perlink.Trinks.Importacao.Statics.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Repositories {

    /// <summary>
    /// Interface para repositório da entidade SolicitacaoDeImportacao.
    /// </summary>
    public partial interface ISolicitacaoDeImportacaoRepository : IRepository<SolicitacaoDeImportacao> { 
		Perlink.Trinks.ImportacaoDeDados.Factories.ISolicitacaoDeImportacaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ImportacaoDeDados.DTO.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.Importadores.Repositories {

 
}
namespace Perlink.Trinks.ImportacaoDeDados.TiposDeColuna.Repositories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.DTO.Repositories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EventoIntegracaoComOutrosSistemas.
    /// </summary>
    public partial interface IEventoIntegracaoComOutrosSistemasRepository : IRepository<EventoIntegracaoComOutrosSistemas> { 
		Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IEventoIntegracaoComOutrosSistemasFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaComChaveDeIntegracao.
    /// </summary>
    public partial interface IFranquiaComChaveDeIntegracaoRepository : IRepository<FranquiaComChaveDeIntegracao> { 
		Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IFranquiaComChaveDeIntegracaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaEstabelecimentoComChaveDeIntegracao.
    /// </summary>
    public partial interface IFranquiaEstabelecimentoComChaveDeIntegracaoRepository : IRepository<FranquiaEstabelecimentoComChaveDeIntegracao> { 
		Perlink.Trinks.IntegracaoComOutrosSistemas.Factories.IFranquiaEstabelecimentoComChaveDeIntegracaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Enums.Repositories {

 
}
namespace Perlink.Trinks.IntegracaoComOutrosSistemas.IntegracaoComTrinks.Repositories {

 
}
namespace Perlink.Trinks.InternoProduto.Enum.Repositories {

 
}
namespace Perlink.Trinks.InternoProduto.Repositories {

    /// <summary>
    /// Interface para repositório da entidade QuestionarioProduto.
    /// </summary>
    public partial interface IQuestionarioProdutoRepository : IRepository<QuestionarioProduto> { 
		Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoOpcaoDeResposta.
    /// </summary>
    public partial interface IQuestionarioProdutoOpcaoDeRespostaRepository : IRepository<QuestionarioProdutoOpcaoDeResposta> { 
		Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoOpcaoDeRespostaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoPergunta.
    /// </summary>
    public partial interface IQuestionarioProdutoPerguntaRepository : IRepository<QuestionarioProdutoPergunta> { 
		Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoPerguntaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoRespondido.
    /// </summary>
    public partial interface IQuestionarioProdutoRespondidoRepository : IRepository<QuestionarioProdutoRespondido> { 
		Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoRespondidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoRespondidoResposta.
    /// </summary>
    public partial interface IQuestionarioProdutoRespondidoRespostaRepository : IRepository<QuestionarioProdutoRespondidoResposta> { 
		Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoRespondidoRespostaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioProdutoTipoResposta.
    /// </summary>
    public partial interface IQuestionarioProdutoTipoRespostaRepository : IRepository<QuestionarioProdutoTipoResposta> { 
		Perlink.Trinks.InternoProduto.Factories.IQuestionarioProdutoTipoRespostaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.LGPD.Helpers.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamento.DTOs.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamento.Enums.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamento.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ItemLinkDePagamento.
    /// </summary>
    public partial interface IItemLinkDePagamentoRepository : IRepository<ItemLinkDePagamento> { 
		Perlink.Trinks.LinksDePagamento.Factories.IItemLinkDePagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamento.
    /// </summary>
    public partial interface ILinkDePagamentoRepository : IRepository<LinkDePagamento> { 
		Perlink.Trinks.LinksDePagamento.Factories.ILinkDePagamentoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Repositories {

    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoNoTrinks.
    /// </summary>
    public partial interface ILinkDePagamentoNoTrinksRepository : IRepository<LinkDePagamentoNoTrinks> { 
		Perlink.Trinks.LinksDePagamentoNoTrinks.Factories.ILinkDePagamentoNoTrinksFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories.Repositories {

 
}
namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Localizacoes.DTO.Repositories {

 
}
namespace Perlink.Trinks.Localizacoes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Loggers.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CorEtiqueta.
    /// </summary>
    public partial interface ICorEtiquetaRepository : IRepository<CorEtiqueta> { 
		Perlink.Trinks.Marcadores.Factories.ICorEtiquetaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Etiqueta.
    /// </summary>
    public partial interface IEtiquetaRepository : IRepository<Etiqueta> { 
		Perlink.Trinks.Marcadores.Factories.IEtiquetaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ObjetoEtiquetado.
    /// </summary>
    public partial interface IObjetoEtiquetadoRepository : IRepository<ObjetoEtiquetado> { 
		Perlink.Trinks.Marcadores.Factories.IObjetoEtiquetadoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Marcadores.DTO.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.Enums.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Marcadores.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracoesEstabelecimentoMarketing.
    /// </summary>
    public partial interface IConfiguracoesEstabelecimentoMarketingRepository : IRepository<ConfiguracoesEstabelecimentoMarketing> { 
		Perlink.Trinks.Marketing.Factories.IConfiguracoesEstabelecimentoMarketingFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetorno.
    /// </summary>
    public partial interface IConviteDeRetornoRepository : IRepository<ConviteDeRetorno> { 
		Perlink.Trinks.Marketing.Factories.IConviteDeRetornoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoEmail.
    /// </summary>
    public partial interface IConviteDeRetornoEmailRepository : IRepository<ConviteDeRetornoEmail> { 
		Perlink.Trinks.Marketing.Factories.IConviteDeRetornoEmailFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoParaQuemEnviar.
    /// </summary>
    public partial interface IConviteDeRetornoParaQuemEnviarRepository : IRepository<ConviteDeRetornoParaQuemEnviar> { 
		Perlink.Trinks.Marketing.Factories.IConviteDeRetornoParaQuemEnviarFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoSMS.
    /// </summary>
    public partial interface IConviteDeRetornoSMSRepository : IRepository<ConviteDeRetornoSMS> { 
		Perlink.Trinks.Marketing.Factories.IConviteDeRetornoSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConviteDeRetornoWhatsApp.
    /// </summary>
    public partial interface IConviteDeRetornoWhatsAppRepository : IRepository<ConviteDeRetornoWhatsApp> { 
		Perlink.Trinks.Marketing.Factories.IConviteDeRetornoWhatsAppFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanha.
    /// </summary>
    public partial interface IMarketingCampanhaRepository : IRepository<MarketingCampanha> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaEmail.
    /// </summary>
    public partial interface IMarketingCampanhaEmailRepository : IRepository<MarketingCampanhaEmail> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaEmailFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaHistorico.
    /// </summary>
    public partial interface IMarketingCampanhaHistoricoRepository : IRepository<MarketingCampanhaHistorico> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvo.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoRepository : IRepository<MarketingCampanhaPublicoAlvo> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoServicoEstabelecimento.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoServicoEstabelecimentoRepository : IRepository<MarketingCampanhaPublicoAlvoServicoEstabelecimento> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoRepository : IRepository<MarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturo> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoServicoEstabelecimentoSemAgendamentoFuturoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoClienteEstabelecimento.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository : IRepository<MarketingCampanhaPublicoAlvoClienteEstabelecimento> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoClienteEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaPublicoAlvoProfissional.
    /// </summary>
    public partial interface IMarketingCampanhaPublicoAlvoProfissionalRepository : IRepository<MarketingCampanhaPublicoAlvoProfissional> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaPublicoAlvoProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaSMS.
    /// </summary>
    public partial interface IMarketingCampanhaSMSRepository : IRepository<MarketingCampanhaSMS> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCampanhaWhatsApp.
    /// </summary>
    public partial interface IMarketingCampanhaWhatsAppRepository : IRepository<MarketingCampanhaWhatsApp> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCampanhaWhatsAppFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingCompraCredito.
    /// </summary>
    public partial interface IMarketingCompraCreditoRepository : IRepository<MarketingCompraCredito> { 
		Perlink.Trinks.Marketing.Factories.IMarketingCompraCreditoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvio.
    /// </summary>
    public partial interface IMarketingEnvioRepository : IRepository<MarketingEnvio> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioCliente.
    /// </summary>
    public partial interface IMarketingEnvioClienteRepository : IRepository<MarketingEnvioCliente> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteEmail.
    /// </summary>
    public partial interface IMarketingEnvioClienteEmailRepository : IRepository<MarketingEnvioClienteEmail> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteEmailFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteParametroEnvio.
    /// </summary>
    public partial interface IMarketingEnvioClienteParametroEnvioRepository : IRepository<MarketingEnvioClienteParametroEnvio> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteParametroEnvioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteSMS.
    /// </summary>
    public partial interface IMarketingEnvioClienteSMSRepository : IRepository<MarketingEnvioClienteSMS> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioClienteWhatsApp.
    /// </summary>
    public partial interface IMarketingEnvioClienteWhatsAppRepository : IRepository<MarketingEnvioClienteWhatsApp> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioClienteWhatsAppFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioEmail.
    /// </summary>
    public partial interface IMarketingEnvioEmailRepository : IRepository<MarketingEnvioEmail> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioEmailFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioSMS.
    /// </summary>
    public partial interface IMarketingEnvioSMSRepository : IRepository<MarketingEnvioSMS> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingEnvioWhatsApp.
    /// </summary>
    public partial interface IMarketingEnvioWhatsAppRepository : IRepository<MarketingEnvioWhatsApp> { 
		Perlink.Trinks.Marketing.Factories.IMarketingEnvioWhatsAppFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingFaixaProfissionais.
    /// </summary>
    public partial interface IMarketingFaixaProfissionaisRepository : IRepository<MarketingFaixaProfissionais> { 
		Perlink.Trinks.Marketing.Factories.IMarketingFaixaProfissionaisFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MarketingPacoteCredito.
    /// </summary>
    public partial interface IMarketingPacoteCreditoRepository : IRepository<MarketingPacoteCredito> { 
		Perlink.Trinks.Marketing.Factories.IMarketingPacoteCreditoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Marketing.DTO.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Enums.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Factories.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Filtro.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Marketing.Strategies.Repositories {

 
}
namespace Perlink.Trinks.MarketingInterno.Repositories {

    /// <summary>
    /// Interface para repositório da entidade DadosMarketing.
    /// </summary>
    public partial interface IDadosMarketingRepository : IRepository<DadosMarketing> { 
		Perlink.Trinks.MarketingInterno.Factories.IDadosMarketingFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.MensagemEmTela.DTOs.Repositories {

 
}
namespace Perlink.Trinks.MensagemEmTela.Implementacoes.Repositories {

 
}
namespace Perlink.Trinks.MensagemEmTela.Repositories {

    /// <summary>
    /// Interface para repositório da entidade MensagemAviso.
    /// </summary>
    public partial interface IMensagemAvisoRepository : IRepository<MensagemAviso> { 
		Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MensagemAvisoTextoLivre.
    /// </summary>
    public partial interface IMensagemAvisoTextoLivreRepository : IRepository<MensagemAvisoTextoLivre> { 
		Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoTextoLivreFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MensagemAvisoImplementacao.
    /// </summary>
    public partial interface IMensagemAvisoImplementacaoRepository : IRepository<MensagemAvisoImplementacao> { 
		Perlink.Trinks.MensagemEmTela.Factories.IMensagemAvisoImplementacaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.MensagensEmMassa.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ModeloDeMensagem.
    /// </summary>
    public partial interface IModeloDeMensagemRepository : IRepository<ModeloDeMensagem> { 
		Perlink.Trinks.MensagensEmMassa.Factories.IModeloDeMensagemFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.MessageQueue.Repositories {

 
}
namespace Perlink.Trinks.Metricas.Dto.Repositories {

 
}
namespace Perlink.Trinks.Metricas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Metricas.Repositories {

    /// <summary>
    /// Interface para repositório da entidade MetricaDesativada.
    /// </summary>
    public partial interface IMetricaDesativadaRepository : IRepository<MetricaDesativada> { 
		Perlink.Trinks.Metricas.Factories.IMetricaDesativadaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoDeNFCDoEstabelecimento.
    /// </summary>
    public partial interface IConfiguracaoDeNFCDoEstabelecimentoRepository : IRepository<ConfiguracaoDeNFCDoEstabelecimento> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IConfiguracaoDeNFCDoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoNFCEstado.
    /// </summary>
    public partial interface IConfiguracaoNFCEstadoRepository : IRepository<ConfiguracaoNFCEstado> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IConfiguracaoNFCEstadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ImpressaoDeNFC.
    /// </summary>
    public partial interface IImpressaoDeNFCRepository : IRepository<ImpressaoDeNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IImpressaoDeNFCFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NfcSituacaoTributaria.
    /// </summary>
    public partial interface INfcSituacaoTributariaRepository : IRepository<NfcSituacaoTributaria> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INfcSituacaoTributariaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NomenclaturaNCMeNBS.
    /// </summary>
    public partial interface INomenclaturaNCMeNBSRepository : IRepository<NomenclaturaNCMeNBS> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INomenclaturaNCMeNBSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotaFormaPagamentoNFC.
    /// </summary>
    public partial interface INotaFormaPagamentoNFCRepository : IRepository<NotaFormaPagamentoNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaFormaPagamentoNFCFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotaInutilizadaNFC.
    /// </summary>
    public partial interface INotaInutilizadaNFCRepository : IRepository<NotaInutilizadaNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaInutilizadaNFCFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotaItensNFC.
    /// </summary>
    public partial interface INotaItensNFCRepository : IRepository<NotaItensNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaItensNFCFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotaNFC.
    /// </summary>
    public partial interface INotaNFCRepository : IRepository<NotaNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.INotaNFCFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaCertificadoDigital.
    /// </summary>
    public partial interface IPessoaJuridicaCertificadoDigitalRepository : IRepository<PessoaJuridicaCertificadoDigital> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IPessoaJuridicaCertificadoDigitalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade StatusNotaNFC.
    /// </summary>
    public partial interface IStatusNotaNFCRepository : IRepository<StatusNotaNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.IStatusNotaNFCFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TabelaIBPT.
    /// </summary>
    public partial interface ITabelaIBPTRepository : IRepository<TabelaIBPT> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.ITabelaIBPTFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoRegimeTributarioNFC.
    /// </summary>
    public partial interface ITipoRegimeTributarioNFCRepository : IRepository<TipoRegimeTributarioNFC> { 
		Perlink.Trinks.NotaFiscalDoConsumidor.Factories.ITipoRegimeTributarioNFCFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.DTO.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Enums.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.Factories.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF.Repositories {

 
}
namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF.SAT.Cancelamento.Repositories {

 
}
namespace Perlink.Trinks.Web.Areas.BackOffice.Models.Produtos.Repositories {

 
}
namespace Perlink.Trinks.Notificacoes.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CampanhaPush.
    /// </summary>
    public partial interface ICampanhaPushRepository : IRepository<CampanhaPush> { 
		Perlink.Trinks.Notificacoes.Factories.ICampanhaPushFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaQtdNotificacoesNovas.
    /// </summary>
    public partial interface IContaQtdNotificacoesNovasRepository : IRepository<ContaQtdNotificacoesNovas> { 
		Perlink.Trinks.Notificacoes.Factories.IContaQtdNotificacoesNovasFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Dispositivo.
    /// </summary>
    public partial interface IDispositivoRepository : IRepository<Dispositivo> { 
		Perlink.Trinks.Notificacoes.Factories.IDispositivoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DispositivoB2cValido.
    /// </summary>
    public partial interface IDispositivoB2cValidoRepository : IRepository<DispositivoB2cValido> { 
		Perlink.Trinks.Notificacoes.Factories.IDispositivoB2cValidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade InscricaoEmNotificacao.
    /// </summary>
    public partial interface IInscricaoEmNotificacaoRepository : IRepository<InscricaoEmNotificacao> { 
		Perlink.Trinks.Notificacoes.Factories.IInscricaoEmNotificacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotificacaoDoTrinks.
    /// </summary>
    public partial interface INotificacaoDoTrinksRepository : IRepository<NotificacaoDoTrinks> { 
		Perlink.Trinks.Notificacoes.Factories.INotificacaoDoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotificacaoPush.
    /// </summary>
    public partial interface INotificacaoPushRepository : IRepository<NotificacaoPush> { 
		Perlink.Trinks.Notificacoes.Factories.INotificacaoPushFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Operadora.
    /// </summary>
    public partial interface IOperadoraRepository : IRepository<Operadora> { 
		Perlink.Trinks.Notificacoes.Factories.IOperadoraFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OperadoraServicoSMS.
    /// </summary>
    public partial interface IOperadoraServicoSMSRepository : IRepository<OperadoraServicoSMS> { 
		Perlink.Trinks.Notificacoes.Factories.IOperadoraServicoSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RegistroNotificacao.
    /// </summary>
    public partial interface IRegistroNotificacaoRepository : IRepository<RegistroNotificacao> { 
		Perlink.Trinks.Notificacoes.Factories.IRegistroNotificacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoSMS.
    /// </summary>
    public partial interface IServicoSMSRepository : IRepository<ServicoSMS> { 
		Perlink.Trinks.Notificacoes.Factories.IServicoSMSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoNotificacao.
    /// </summary>
    public partial interface ITipoNotificacaoRepository : IRepository<TipoNotificacao> { 
		Perlink.Trinks.Notificacoes.Factories.ITipoNotificacaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Notificacoes.DTO.Repositories {

 
}
namespace Perlink.Trinks.Notificacoes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Notificacoes.Filtros.Repositories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CanalDaNotificacao.
    /// </summary>
    public partial interface ICanalDaNotificacaoRepository : IRepository<CanalDaNotificacao> { 
		Perlink.Trinks.NotificacoesApps.Factories.ICanalDaNotificacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EventoDeNotificacao.
    /// </summary>
    public partial interface IEventoDeNotificacaoRepository : IRepository<EventoDeNotificacao> { 
		Perlink.Trinks.NotificacoesApps.Factories.IEventoDeNotificacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MensagemDeNotificacao.
    /// </summary>
    public partial interface IMensagemDeNotificacaoRepository : IRepository<MensagemDeNotificacao> { 
		Perlink.Trinks.NotificacoesApps.Factories.IMensagemDeNotificacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreferenciaDeNotificacaoDoUsuario.
    /// </summary>
    public partial interface IPreferenciaDeNotificacaoDoUsuarioRepository : IRepository<PreferenciaDeNotificacaoDoUsuario> { 
		Perlink.Trinks.NotificacoesApps.Factories.IPreferenciaDeNotificacaoDoUsuarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDeNotificacao.
    /// </summary>
    public partial interface ITipoDeNotificacaoRepository : IRepository<TipoDeNotificacao> { 
		Perlink.Trinks.NotificacoesApps.Factories.ITipoDeNotificacaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.NotificacoesApps.DTO.Repositories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Repositories {

 
}
namespace Perlink.Trinks.NotificacoesApps.Strategies.Repositories {

 
}
namespace Perlink.Trinks.Novidades.DTO.Repositories {

 
}
namespace Perlink.Trinks.Novidades.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.Comparers.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.DTOs.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.Enums.Repositories {

 
}
namespace Perlink.Trinks.Onboardings.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoTrilha.
    /// </summary>
    public partial interface IEstabelecimentoTrilhaRepository : IRepository<EstabelecimentoTrilha> { 
		Perlink.Trinks.Onboardings.Factories.IEstabelecimentoTrilhaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade QuestionarioOnboardingPorFaixa.
    /// </summary>
    public partial interface IQuestionarioOnboardingPorFaixaRepository : IRepository<QuestionarioOnboardingPorFaixa> { 
		Perlink.Trinks.Onboardings.Factories.IQuestionarioOnboardingPorFaixaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RastreioDeTarefa.
    /// </summary>
    public partial interface IRastreioDeTarefaRepository : IRepository<RastreioDeTarefa> { 
		Perlink.Trinks.Onboardings.Factories.IRastreioDeTarefaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Tarefa.
    /// </summary>
    public partial interface ITarefaRepository : IRepository<Tarefa> { 
		Perlink.Trinks.Onboardings.Factories.ITarefaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Trilha.
    /// </summary>
    public partial interface ITrilhaRepository : IRepository<Trilha> { 
		Perlink.Trinks.Onboardings.Factories.ITrilhaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TrilhaAcao.
    /// </summary>
    public partial interface ITrilhaAcaoRepository : IRepository<TrilhaAcao> { 
		Perlink.Trinks.Onboardings.Factories.ITrilhaAcaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TrilhaAcaoGrupo.
    /// </summary>
    public partial interface ITrilhaAcaoGrupoRepository : IRepository<TrilhaAcaoGrupo> { 
		Perlink.Trinks.Onboardings.Factories.ITrilhaAcaoGrupoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Onboardings.Stories.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Adapters.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoPacotePersonalizado.
    /// </summary>
    public partial interface IConfiguracaoPacotePersonalizadoRepository : IRepository<ConfiguracaoPacotePersonalizado> { 
		Perlink.Trinks.Pacotes.Factories.IConfiguracaoPacotePersonalizadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoConfiguracoesPacote.
    /// </summary>
    public partial interface IHistoricoConfiguracoesPacoteRepository : IRepository<HistoricoConfiguracoesPacote> { 
		Perlink.Trinks.Pacotes.Factories.IHistoricoConfiguracoesPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacote.
    /// </summary>
    public partial interface IItemPacoteRepository : IRepository<ItemPacote> { 
		Perlink.Trinks.Pacotes.Factories.IItemPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteCliente.
    /// </summary>
    public partial interface IItemPacoteClienteRepository : IRepository<ItemPacoteCliente> { 
		Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteClienteProduto.
    /// </summary>
    public partial interface IItemPacoteClienteProdutoRepository : IRepository<ItemPacoteClienteProduto> { 
		Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteClienteServico.
    /// </summary>
    public partial interface IItemPacoteClienteServicoRepository : IRepository<ItemPacoteClienteServico> { 
		Perlink.Trinks.Pacotes.Factories.IItemPacoteClienteServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteProduto.
    /// </summary>
    public partial interface IItemPacoteProdutoRepository : IRepository<ItemPacoteProduto> { 
		Perlink.Trinks.Pacotes.Factories.IItemPacoteProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPacoteServico.
    /// </summary>
    public partial interface IItemPacoteServicoRepository : IRepository<ItemPacoteServico> { 
		Perlink.Trinks.Pacotes.Factories.IItemPacoteServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDoPacote.
    /// </summary>
    public partial interface ILinkDePagamentoDoPacoteRepository : IRepository<LinkDePagamentoDoPacote> { 
		Perlink.Trinks.Pacotes.Factories.ILinkDePagamentoDoPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Pacote.
    /// </summary>
    public partial interface IPacoteRepository : IRepository<Pacote> { 
		Perlink.Trinks.Pacotes.Factories.IPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteCliente.
    /// </summary>
    public partial interface IPacoteClienteRepository : IRepository<PacoteCliente> { 
		Perlink.Trinks.Pacotes.Factories.IPacoteClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteClienteHistorico.
    /// </summary>
    public partial interface IPacoteClienteHistoricoRepository : IRepository<PacoteClienteHistorico> { 
		Perlink.Trinks.Pacotes.Factories.IPacoteClienteHistoricoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Pacotes.DTO.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Pacotes.Factories.Repositories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.DTO.Repositories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Enums.Repositories {

 
}
namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories {

    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipadoHotsiteConfiguracoes.
    /// </summary>
    public partial interface IPagamentoAntecipadoHotsiteConfiguracoesRepository : IRepository<PagamentoAntecipadoHotsiteConfiguracoes> { 
		Perlink.Trinks.PagamentoAntecipadoHotsite.Factories.IPagamentoAntecipadoHotsiteConfiguracoesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipadoHotsiteServicos.
    /// </summary>
    public partial interface IPagamentoAntecipadoHotsiteServicosRepository : IRepository<PagamentoAntecipadoHotsiteServicos> { 
		Perlink.Trinks.PagamentoAntecipadoHotsite.Factories.IPagamentoAntecipadoHotsiteServicosFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Pagamentos.Calculos.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CartaoDeComprador.
    /// </summary>
    public partial interface ICartaoDeCompradorRepository : IRepository<CartaoDeComprador> { 
		Perlink.Trinks.Pagamentos.Factories.ICartaoDeCompradorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CartaoDeCompradorGateway.
    /// </summary>
    public partial interface ICartaoDeCompradorGatewayRepository : IRepository<CartaoDeCompradorGateway> { 
		Perlink.Trinks.Pagamentos.Factories.ICartaoDeCompradorGatewayFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Comprador.
    /// </summary>
    public partial interface ICompradorRepository : IRepository<Comprador> { 
		Perlink.Trinks.Pagamentos.Factories.ICompradorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CompradorGateway.
    /// </summary>
    public partial interface ICompradorGatewayRepository : IRepository<CompradorGateway> { 
		Perlink.Trinks.Pagamentos.Factories.ICompradorGatewayFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancaria.
    /// </summary>
    public partial interface IContaBancariaRepository : IRepository<ContaBancaria> { 
		Perlink.Trinks.Pagamentos.Factories.IContaBancariaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaBancariaGateway.
    /// </summary>
    public partial interface IContaBancariaGatewayRepository : IRepository<ContaBancariaGateway> { 
		Perlink.Trinks.Pagamentos.Factories.IContaBancariaGatewayFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DocumentoDeRecebedorCredenciado.
    /// </summary>
    public partial interface IDocumentoDeRecebedorCredenciadoRepository : IRepository<DocumentoDeRecebedorCredenciado> { 
		Perlink.Trinks.Pagamentos.Factories.IDocumentoDeRecebedorCredenciadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EnderecoDeCobranca.
    /// </summary>
    public partial interface IEnderecoDeCobrancaRepository : IRepository<EnderecoDeCobranca> { 
		Perlink.Trinks.Pagamentos.Factories.IEnderecoDeCobrancaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EtapaCadastroPagarme.
    /// </summary>
    public partial interface IEtapaCadastroPagarmeRepository : IRepository<EtapaCadastroPagarme> { 
		Perlink.Trinks.Pagamentos.Factories.IEtapaCadastroPagarmeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Gateway.
    /// </summary>
    public partial interface IGatewayRepository : IRepository<Gateway> { 
		Perlink.Trinks.Pagamentos.Factories.IGatewayFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPagamento.
    /// </summary>
    public partial interface IItemPagamentoRepository : IRepository<ItemPagamento> { 
		Perlink.Trinks.Pagamentos.Factories.IItemPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LimitePagamento.
    /// </summary>
    public partial interface ILimitePagamentoRepository : IRepository<LimitePagamento> { 
		Perlink.Trinks.Pagamentos.Factories.ILimitePagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LimitePagamentoRecebedor.
    /// </summary>
    public partial interface ILimitePagamentoRecebedorRepository : IRepository<LimitePagamentoRecebedor> { 
		Perlink.Trinks.Pagamentos.Factories.ILimitePagamentoRecebedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Pagamento.
    /// </summary>
    public partial interface IPagamentoRepository : IRepository<Pagamento> { 
		Perlink.Trinks.Pagamentos.Factories.IPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Recebedor.
    /// </summary>
    public partial interface IRecebedorRepository : IRepository<Recebedor> { 
		Perlink.Trinks.Pagamentos.Factories.IRecebedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RecebedorCredenciado.
    /// </summary>
    public partial interface IRecebedorCredenciadoRepository : IRepository<RecebedorCredenciado> { 
		Perlink.Trinks.Pagamentos.Factories.IRecebedorCredenciadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SplitPagamento.
    /// </summary>
    public partial interface ISplitPagamentoRepository : IRepository<SplitPagamento> { 
		Perlink.Trinks.Pagamentos.Factories.ISplitPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimiteAntecipacaoDiarioRecebedor.
    /// </summary>
    public partial interface IUsoLimiteAntecipacaoDiarioRecebedorRepository : IRepository<UsoLimiteAntecipacaoDiarioRecebedor> { 
		Perlink.Trinks.Pagamentos.Factories.IUsoLimiteAntecipacaoDiarioRecebedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimiteAntecipacaoMensalRecebedor.
    /// </summary>
    public partial interface IUsoLimiteAntecipacaoMensalRecebedorRepository : IRepository<UsoLimiteAntecipacaoMensalRecebedor> { 
		Perlink.Trinks.Pagamentos.Factories.IUsoLimiteAntecipacaoMensalRecebedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimitePagamentoDiarioRecebedor.
    /// </summary>
    public partial interface IUsoLimitePagamentoDiarioRecebedorRepository : IRepository<UsoLimitePagamentoDiarioRecebedor> { 
		Perlink.Trinks.Pagamentos.Factories.IUsoLimitePagamentoDiarioRecebedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsoLimitePagamentoMensalRecebedor.
    /// </summary>
    public partial interface IUsoLimitePagamentoMensalRecebedorRepository : IRepository<UsoLimitePagamentoMensalRecebedor> { 
		Perlink.Trinks.Pagamentos.Factories.IUsoLimitePagamentoMensalRecebedorFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Pagamentos.Config.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.DTO.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Enums.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Factories.Repositories {

 
}
namespace Perlink.Trinks.Pagamentos.Providers.Repositories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Repositories {

    /// <summary>
    /// Interface para repositório da entidade BeneficiosEstabelecimento.
    /// </summary>
    public partial interface IBeneficiosEstabelecimentoRepository : IRepository<BeneficiosEstabelecimento> { 
		Perlink.Trinks.PagamentosAntecipados.Factories.IBeneficiosEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade BeneficiosPagamento.
    /// </summary>
    public partial interface IBeneficiosPagamentoRepository : IRepository<BeneficiosPagamento> { 
		Perlink.Trinks.PagamentosAntecipados.Factories.IBeneficiosPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPagamentoAntecipado.
    /// </summary>
    public partial interface IItemPagamentoAntecipadoRepository : IRepository<ItemPagamentoAntecipado> { 
		Perlink.Trinks.PagamentosAntecipados.Factories.IItemPagamentoAntecipadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemPagamentoHorario.
    /// </summary>
    public partial interface IItemPagamentoHorarioRepository : IRepository<ItemPagamentoHorario> { 
		Perlink.Trinks.PagamentosAntecipados.Factories.IItemPagamentoHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoAntecipado.
    /// </summary>
    public partial interface IPagamentoAntecipadoRepository : IRepository<PagamentoAntecipado> { 
		Perlink.Trinks.PagamentosAntecipados.Factories.IPagamentoAntecipadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoHabilitado.
    /// </summary>
    public partial interface IServicoHabilitadoRepository : IRepository<ServicoHabilitado> { 
		Perlink.Trinks.PagamentosAntecipados.Factories.IServicoHabilitadoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.PagamentosAntecipados.DTO.Repositories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Enums.Repositories {

 
}
namespace Perlink.Trinks.Specifications.Repositories {

 
}
namespace Perlink.Trinks.PagamentosAntecipados.Utils.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoAdiantamentoFuncionalidadeAntecipacao.
    /// </summary>
    public partial interface IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoRepository : IRepository<ConfiguracaoAdiantamentoFuncionalidadeAntecipacao> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IConfiguracaoAdiantamentoFuncionalidadeAntecipacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline.
    /// </summary>
    public partial interface IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository : IRepository<ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnline> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoRecebedor.
    /// </summary>
    public partial interface IEstabelecimentoRecebedorRepository : IRepository<EstabelecimentoRecebedor> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IEstabelecimentoRecebedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade InstituicaoBancaria.
    /// </summary>
    public partial interface IInstituicaoBancariaRepository : IRepository<InstituicaoBancaria> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IInstituicaoBancariaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LOGCancelamentoAntecipacao.
    /// </summary>
    public partial interface ILOGCancelamentoAntecipacaoRepository : IRepository<LOGCancelamentoAntecipacao> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ILOGCancelamentoAntecipacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LOGSolicitacaoAntecipacao.
    /// </summary>
    public partial interface ILOGSolicitacaoAntecipacaoRepository : IRepository<LOGSolicitacaoAntecipacao> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ILOGSolicitacaoAntecipacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PagamentoOnlineNoTrinks.
    /// </summary>
    public partial interface IPagamentoOnlineNoTrinksRepository : IRepository<PagamentoOnlineNoTrinks> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.IPagamentoOnlineNoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasDoEstabelecimento.
    /// </summary>
    public partial interface ITaxasDoEstabelecimentoRepository : IRepository<TaxasDoEstabelecimento> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasDoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasDoPagamentoOnlineNoTrinks.
    /// </summary>
    public partial interface ITaxasDoPagamentoOnlineNoTrinksRepository : IRepository<TaxasDoPagamentoOnlineNoTrinks> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasDoPagamentoOnlineNoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasPadrao.
    /// </summary>
    public partial interface ITaxasPadraoRepository : IRepository<TaxasPadrao> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TaxasPadraoEstabelecimentoRecebedor.
    /// </summary>
    public partial interface ITaxasPadraoEstabelecimentoRecebedorRepository : IRepository<TaxasPadraoEstabelecimentoRecebedor> { 
		Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.ITaxasPadraoEstabelecimentoRecebedorFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Factories.Repositories {

 
}
namespace Perlink.Trinks.PDF.Repositories {

 
}
namespace Perlink.Trinks.Permissoes.AreaPerlink.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AreaPerlinkPerfil.
    /// </summary>
    public partial interface IAreaPerlinkPerfilRepository : IRepository<AreaPerlinkPerfil> { 
		Perlink.Trinks.Permissoes.AreaPerlink.Factories.IAreaPerlinkPerfilFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AreaPerlinkPerfilPermissao.
    /// </summary>
    public partial interface IAreaPerlinkPerfilPermissaoRepository : IRepository<AreaPerlinkPerfilPermissao> { 
		Perlink.Trinks.Permissoes.AreaPerlink.Factories.IAreaPerlinkPerfilPermissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaPerfil.
    /// </summary>
    public partial interface IContaPerfilRepository : IRepository<ContaPerfil> { 
		Perlink.Trinks.Permissoes.AreaPerlink.Factories.IContaPerfilFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaPerfilPermissao.
    /// </summary>
    public partial interface IContaPerfilPermissaoRepository : IRepository<ContaPerfilPermissao> { 
		Perlink.Trinks.Permissoes.AreaPerlink.Factories.IContaPerfilPermissaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Permissoes.AreaPerlink.Repositories {

 
}
namespace Perlink.Trinks.Permissoes.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CategoriaPermissao.
    /// </summary>
    public partial interface ICategoriaPermissaoRepository : IRepository<CategoriaPermissao> { 
		Perlink.Trinks.Permissoes.Factories.ICategoriaPermissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DescricaoPermissao.
    /// </summary>
    public partial interface IDescricaoPermissaoRepository : IRepository<DescricaoPermissao> { 
		Perlink.Trinks.Permissoes.Factories.IDescricaoPermissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaPermissao.
    /// </summary>
    public partial interface IFranquiaPermissaoRepository : IRepository<FranquiaPermissao> { 
		Perlink.Trinks.Permissoes.Factories.IFranquiaPermissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Perfil.
    /// </summary>
    public partial interface IPerfilRepository : IRepository<Perfil> { 
		Perlink.Trinks.Permissoes.Factories.IPerfilFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PerfilPermissao.
    /// </summary>
    public partial interface IPerfilPermissaoRepository : IRepository<PerfilPermissao> { 
		Perlink.Trinks.Permissoes.Factories.IPerfilPermissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PermissaoArea.
    /// </summary>
    public partial interface IPermissaoAreaRepository : IRepository<PermissaoArea> { 
		Perlink.Trinks.Permissoes.Factories.IPermissaoAreaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioPerfil.
    /// </summary>
    public partial interface IUsuarioPerfilRepository : IRepository<UsuarioPerfil> { 
		Perlink.Trinks.Permissoes.Factories.IUsuarioPerfilFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioPerfilPermissao.
    /// </summary>
    public partial interface IUsuarioPerfilPermissaoRepository : IRepository<UsuarioPerfilPermissao> { 
		Perlink.Trinks.Permissoes.Factories.IUsuarioPerfilPermissaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Permissoes.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Bairro.
    /// </summary>
    public partial interface IBairroRepository : IRepository<Bairro> { 
		Perlink.Trinks.Pessoas.Factories.IBairroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CacheLocalidade.
    /// </summary>
    public partial interface ICacheLocalidadeRepository : IRepository<CacheLocalidade> { 
		Perlink.Trinks.Pessoas.Factories.ICacheLocalidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CategoriaPortalServico.
    /// </summary>
    public partial interface ICategoriaPortalServicoRepository : IRepository<CategoriaPortalServico> { 
		Perlink.Trinks.Pessoas.Factories.ICategoriaPortalServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Cidade.
    /// </summary>
    public partial interface ICidadeRepository : IRepository<Cidade> { 
		Perlink.Trinks.Pessoas.Factories.ICidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Cliente.
    /// </summary>
    public partial interface IClienteRepository : IRepository<Cliente> { 
		Perlink.Trinks.Pessoas.Factories.IClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ClienteAreaPerlink.
    /// </summary>
    public partial interface IClienteAreaPerlinkRepository : IRepository<ClienteAreaPerlink> { 
		Perlink.Trinks.Pessoas.Factories.IClienteAreaPerlinkFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ClienteEstabelecimento.
    /// </summary>
    public partial interface IClienteEstabelecimentoRepository : IRepository<ClienteEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IClienteEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ClienteEstabelecimentoSaldos.
    /// </summary>
    public partial interface IClienteEstabelecimentoSaldosRepository : IRepository<ClienteEstabelecimentoSaldos> { 
		Perlink.Trinks.Pessoas.Factories.IClienteEstabelecimentoSaldosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ComoConheceu.
    /// </summary>
    public partial interface IComoConheceuRepository : IRepository<ComoConheceu> { 
		Perlink.Trinks.Pessoas.Factories.IComoConheceuFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ComoEstabelecimentoConheceuOTrinks.
    /// </summary>
    public partial interface IComoEstabelecimentoConheceuOTrinksRepository : IRepository<ComoEstabelecimentoConheceuOTrinks> { 
		Perlink.Trinks.Pessoas.Factories.IComoEstabelecimentoConheceuOTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CompartilhamentoNaRede.
    /// </summary>
    public partial interface ICompartilhamentoNaRedeRepository : IRepository<CompartilhamentoNaRede> { 
		Perlink.Trinks.Pessoas.Factories.ICompartilhamentoNaRedeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteAderencia.
    /// </summary>
    public partial interface IConfiguracaoHotsiteAderenciaRepository : IRepository<ConfiguracaoHotsiteAderencia> { 
		Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteAderenciaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteInicioMarcos.
    /// </summary>
    public partial interface IConfiguracaoHotsiteInicioMarcosRepository : IRepository<ConfiguracaoHotsiteInicioMarcos> { 
		Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteInicioMarcosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteIntervalo.
    /// </summary>
    public partial interface IConfiguracaoHotsiteIntervaloRepository : IRepository<ConfiguracaoHotsiteIntervalo> { 
		Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteIntervaloFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoHotsiteUniverso.
    /// </summary>
    public partial interface IConfiguracaoHotsiteUniversoRepository : IRepository<ConfiguracaoHotsiteUniverso> { 
		Perlink.Trinks.Pessoas.Factories.IConfiguracaoHotsiteUniversoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Conta.
    /// </summary>
    public partial interface IContaRepository : IRepository<Conta> { 
		Perlink.Trinks.Pessoas.Factories.IContaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ContaFranquia.
    /// </summary>
    public partial interface IContaFranquiaRepository : IRepository<ContaFranquia> { 
		Perlink.Trinks.Pessoas.Factories.IContaFranquiaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DadosParaRecalculoComissao.
    /// </summary>
    public partial interface IDadosParaRecalculoComissaoRepository : IRepository<DadosParaRecalculoComissao> { 
		Perlink.Trinks.Pessoas.Factories.IDadosParaRecalculoComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DataEspecial.
    /// </summary>
    public partial interface IDataEspecialRepository : IRepository<DataEspecial> { 
		Perlink.Trinks.Pessoas.Factories.IDataEspecialFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DiaSemana.
    /// </summary>
    public partial interface IDiaSemanaRepository : IRepository<DiaSemana> { 
		Perlink.Trinks.Pessoas.Factories.IDiaSemanaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EmailRejeitadoAmazon.
    /// </summary>
    public partial interface IEmailRejeitadoAmazonRepository : IRepository<EmailRejeitadoAmazon> { 
		Perlink.Trinks.Pessoas.Factories.IEmailRejeitadoAmazonFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Endereco.
    /// </summary>
    public partial interface IEnderecoRepository : IRepository<Endereco> { 
		Perlink.Trinks.Pessoas.Factories.IEnderecoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EnderecoPreenchidoManualmente.
    /// </summary>
    public partial interface IEnderecoPreenchidoManualmenteRepository : IRepository<EnderecoPreenchidoManualmente> { 
		Perlink.Trinks.Pessoas.Factories.IEnderecoPreenchidoManualmenteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Estabelecimento.
    /// </summary>
    public partial interface IEstabelecimentoRepository : IRepository<Estabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoAssistenteServico.
    /// </summary>
    public partial interface IEstabelecimentoAssistenteServicoRepository : IRepository<EstabelecimentoAssistenteServico> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAssistenteServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoAssistenteServicoComissao.
    /// </summary>
    public partial interface IEstabelecimentoAssistenteServicoComissaoRepository : IRepository<EstabelecimentoAssistenteServicoComissao> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAssistenteServicoComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoAtendeCrianca.
    /// </summary>
    public partial interface IEstabelecimentoAtendeCriancaRepository : IRepository<EstabelecimentoAtendeCrianca> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoAtendeCriancaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoConfiguracaoComissao.
    /// </summary>
    public partial interface IEstabelecimentoConfiguracaoComissaoRepository : IRepository<EstabelecimentoConfiguracaoComissao> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoConfiguracaoGeral.
    /// </summary>
    public partial interface IEstabelecimentoConfiguracaoGeralRepository : IRepository<EstabelecimentoConfiguracaoGeral> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoGeralFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoConfiguracaoPOS.
    /// </summary>
    public partial interface IEstabelecimentoConfiguracaoPOSRepository : IRepository<EstabelecimentoConfiguracaoPOS> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoConfiguracaoPOSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosGerais.
    /// </summary>
    public partial interface IEstabelecimentoDadosGeraisRepository : IRepository<EstabelecimentoDadosGerais> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosGeraisFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastro.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroRepository : IRepository<EstabelecimentoDadosPreCadastro> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastroCargo.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroCargoRepository : IRepository<EstabelecimentoDadosPreCadastroCargo> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroCargoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastroMotivosCadastro.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroMotivosCadastroRepository : IRepository<EstabelecimentoDadosPreCadastroMotivosCadastro> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroMotivosCadastroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoDadosPreCadastroSegmentos.
    /// </summary>
    public partial interface IEstabelecimentoDadosPreCadastroSegmentosRepository : IRepository<EstabelecimentoDadosPreCadastroSegmentos> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoDadosPreCadastroSegmentosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFabricanteProduto.
    /// </summary>
    public partial interface IEstabelecimentoFabricanteProdutoRepository : IRepository<EstabelecimentoFabricanteProduto> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFabricanteProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFormaPagamento.
    /// </summary>
    public partial interface IEstabelecimentoFormaPagamentoRepository : IRepository<EstabelecimentoFormaPagamento> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFormaPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFormaPagamentoParcela.
    /// </summary>
    public partial interface IEstabelecimentoFormaPagamentoParcelaRepository : IRepository<EstabelecimentoFormaPagamentoParcela> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFormaPagamentoParcelaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoFornecedor.
    /// </summary>
    public partial interface IEstabelecimentoFornecedorRepository : IRepository<EstabelecimentoFornecedor> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoFornecedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoHorarioEspecialFuncionamento.
    /// </summary>
    public partial interface IEstabelecimentoHorarioEspecialFuncionamentoRepository : IRepository<EstabelecimentoHorarioEspecialFuncionamento> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioEspecialFuncionamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoHorarioEspecialFuncionamentoTipo.
    /// </summary>
    public partial interface IEstabelecimentoHorarioEspecialFuncionamentoTipoRepository : IRepository<EstabelecimentoHorarioEspecialFuncionamentoTipo> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioEspecialFuncionamentoTipoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoHorarioFuncionamento.
    /// </summary>
    public partial interface IEstabelecimentoHorarioFuncionamentoRepository : IRepository<EstabelecimentoHorarioFuncionamento> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoHorarioFuncionamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoIndicado.
    /// </summary>
    public partial interface IEstabelecimentoIndicadoRepository : IRepository<EstabelecimentoIndicado> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoIndicadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoMovimentacaoEstoque.
    /// </summary>
    public partial interface IEstabelecimentoMovimentacaoEstoqueRepository : IRepository<EstabelecimentoMovimentacaoEstoque> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoMovimentacaoEstoqueFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoPossuiEstacionamento.
    /// </summary>
    public partial interface IEstabelecimentoPossuiEstacionamentoRepository : IRepository<EstabelecimentoPossuiEstacionamento> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoPossuiEstacionamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoPreCadastro.
    /// </summary>
    public partial interface IEstabelecimentoPreCadastroRepository : IRepository<EstabelecimentoPreCadastro> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoPreCadastroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProduto.
    /// </summary>
    public partial interface IEstabelecimentoProdutoRepository : IRepository<EstabelecimentoProduto> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissional.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalRepository : IRepository<EstabelecimentoProfissional> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalRedeSocial.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalRedeSocialRepository : IRepository<EstabelecimentoProfissionalRedeSocial> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalRedeSocialFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalServico.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalServicoRepository : IRepository<EstabelecimentoProfissionalServico> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProfissionalServicoComissao.
    /// </summary>
    public partial interface IEstabelecimentoProfissionalServicoComissaoRepository : IRepository<EstabelecimentoProfissionalServicoComissao> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoProfissionalServicoComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoSolicitacaoAparecerBusca.
    /// </summary>
    public partial interface IEstabelecimentoSolicitacaoAparecerBuscaRepository : IRepository<EstabelecimentoSolicitacaoAparecerBusca> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoSolicitacaoAparecerBuscaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoTemplate.
    /// </summary>
    public partial interface IEstabelecimentoTemplateRepository : IRepository<EstabelecimentoTemplate> { 
		Perlink.Trinks.Pessoas.Factories.IEstabelecimentoTemplateFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstadoCivil.
    /// </summary>
    public partial interface IEstadoCivilRepository : IRepository<EstadoCivil> { 
		Perlink.Trinks.Pessoas.Factories.IEstadoCivilFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstatisticaExibicaoTelefone.
    /// </summary>
    public partial interface IEstatisticaExibicaoTelefoneRepository : IRepository<EstatisticaExibicaoTelefone> { 
		Perlink.Trinks.Pessoas.Factories.IEstatisticaExibicaoTelefoneFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FabricanteProdutoPadrao.
    /// </summary>
    public partial interface IFabricanteProdutoPadraoRepository : IRepository<FabricanteProdutoPadrao> { 
		Perlink.Trinks.Pessoas.Factories.IFabricanteProdutoPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormaRelacaoProfissional.
    /// </summary>
    public partial interface IFormaRelacaoProfissionalRepository : IRepository<FormaRelacaoProfissional> { 
		Perlink.Trinks.Pessoas.Factories.IFormaRelacaoProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FormaRelacaoProfissionalPadrao.
    /// </summary>
    public partial interface IFormaRelacaoProfissionalPadraoRepository : IRepository<FormaRelacaoProfissionalPadrao> { 
		Perlink.Trinks.Pessoas.Factories.IFormaRelacaoProfissionalPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Fornecedor.
    /// </summary>
    public partial interface IFornecedorRepository : IRepository<Fornecedor> { 
		Perlink.Trinks.Pessoas.Factories.IFornecedorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FotoDeClienteEstabelecimento.
    /// </summary>
    public partial interface IFotoDeClienteEstabelecimentoRepository : IRepository<FotoDeClienteEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IFotoDeClienteEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FotoDeServicoRealizadoEmHorario.
    /// </summary>
    public partial interface IFotoDeServicoRealizadoEmHorarioRepository : IRepository<FotoDeServicoRealizadoEmHorario> { 
		Perlink.Trinks.Pessoas.Factories.IFotoDeServicoRealizadoEmHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FotoEstabelecimento.
    /// </summary>
    public partial interface IFotoEstabelecimentoRepository : IRepository<FotoEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IFotoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FotoPessoa.
    /// </summary>
    public partial interface IFotoPessoaRepository : IRepository<FotoPessoa> { 
		Perlink.Trinks.Pessoas.Factories.IFotoPessoaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Franquia.
    /// </summary>
    public partial interface IFranquiaRepository : IRepository<Franquia> { 
		Perlink.Trinks.Pessoas.Factories.IFranquiaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaEstabelecimento.
    /// </summary>
    public partial interface IFranquiaEstabelecimentoRepository : IRepository<FranquiaEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IFranquiaEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FuncaoDoProfissional.
    /// </summary>
    public partial interface IFuncaoDoProfissionalRepository : IRepository<FuncaoDoProfissional> { 
		Perlink.Trinks.Pessoas.Factories.IFuncaoDoProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FuncaoDoProfissionalPadrao.
    /// </summary>
    public partial interface IFuncaoDoProfissionalPadraoRepository : IRepository<FuncaoDoProfissionalPadrao> { 
		Perlink.Trinks.Pessoas.Factories.IFuncaoDoProfissionalPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoAcaoUnidadeAoModelo.
    /// </summary>
    public partial interface IHistoricoAcaoUnidadeAoModeloRepository : IRepository<HistoricoAcaoUnidadeAoModelo> { 
		Perlink.Trinks.Pessoas.Factories.IHistoricoAcaoUnidadeAoModeloFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoCliente.
    /// </summary>
    public partial interface IHistoricoClienteRepository : IRepository<HistoricoCliente> { 
		Perlink.Trinks.Pessoas.Factories.IHistoricoClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Horario.
    /// </summary>
    public partial interface IHorarioRepository : IRepository<Horario> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioHistorico.
    /// </summary>
    public partial interface IHorarioHistoricoRepository : IRepository<HorarioHistorico> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioHistoricoEtiqueta.
    /// </summary>
    public partial interface IHorarioHistoricoEtiquetaRepository : IRepository<HorarioHistoricoEtiqueta> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioHistoricoEtiquetaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioOrigem.
    /// </summary>
    public partial interface IHorarioOrigemRepository : IRepository<HorarioOrigem> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioOrigemFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioQuemCancelou.
    /// </summary>
    public partial interface IHorarioQuemCancelouRepository : IRepository<HorarioQuemCancelou> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioQuemCancelouFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioTrabalho.
    /// </summary>
    public partial interface IHorarioTrabalhoRepository : IRepository<HorarioTrabalho> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioTrabalhoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioTransacao.
    /// </summary>
    public partial interface IHorarioTransacaoRepository : IRepository<HorarioTransacao> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioVeraoCidade.
    /// </summary>
    public partial interface IHorarioVeraoCidadeRepository : IRepository<HorarioVeraoCidade> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioVeraoCidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioVeraoUF.
    /// </summary>
    public partial interface IHorarioVeraoUFRepository : IRepository<HorarioVeraoUF> { 
		Perlink.Trinks.Pessoas.Factories.IHorarioVeraoUFFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HotsiteEstabelecimento.
    /// </summary>
    public partial interface IHotsiteEstabelecimentoRepository : IRepository<HotsiteEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IHotsiteEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemComboCliente.
    /// </summary>
    public partial interface IItemComboClienteRepository : IRepository<ItemComboCliente> { 
		Perlink.Trinks.Pessoas.Factories.IItemComboClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LinkDePagamentoDoHorario.
    /// </summary>
    public partial interface ILinkDePagamentoDoHorarioRepository : IRepository<LinkDePagamentoDoHorario> { 
		Perlink.Trinks.Pessoas.Factories.ILinkDePagamentoDoHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MidiaSocial.
    /// </summary>
    public partial interface IMidiaSocialRepository : IRepository<MidiaSocial> { 
		Perlink.Trinks.Pessoas.Factories.IMidiaSocialFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoAusencia.
    /// </summary>
    public partial interface IMotivoAusenciaRepository : IRepository<MotivoAusencia> { 
		Perlink.Trinks.Pessoas.Factories.IMotivoAusenciaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoQueEscolheuOTrinks.
    /// </summary>
    public partial interface IMotivoQueEscolheuOTrinksRepository : IRepository<MotivoQueEscolheuOTrinks> { 
		Perlink.Trinks.Pessoas.Factories.IMotivoQueEscolheuOTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoQueEstabelecimentoEscolheuOTrinks.
    /// </summary>
    public partial interface IMotivoQueEstabelecimentoEscolheuOTrinksRepository : IRepository<MotivoQueEstabelecimentoEscolheuOTrinks> { 
		Perlink.Trinks.Pessoas.Factories.IMotivoQueEstabelecimentoEscolheuOTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NotificacaoEstabelecimento.
    /// </summary>
    public partial interface INotificacaoEstabelecimentoRepository : IRepository<NotificacaoEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.INotificacaoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Novidade.
    /// </summary>
    public partial interface INovidadeRepository : IRepository<Novidade> { 
		Perlink.Trinks.Pessoas.Factories.INovidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ParametrizacaoTrinks.
    /// </summary>
    public partial interface IParametrizacaoTrinksRepository : IRepository<ParametrizacaoTrinks> { 
		Perlink.Trinks.Pessoas.Factories.IParametrizacaoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PeriodoAusencia.
    /// </summary>
    public partial interface IPeriodoAusenciaRepository : IRepository<PeriodoAusencia> { 
		Perlink.Trinks.Pessoas.Factories.IPeriodoAusenciaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PermissoesDaUnidadeBaseadaEmModelo.
    /// </summary>
    public partial interface IPermissoesDaUnidadeBaseadaEmModeloRepository : IRepository<PermissoesDaUnidadeBaseadaEmModelo> { 
		Perlink.Trinks.Pessoas.Factories.IPermissoesDaUnidadeBaseadaEmModeloFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Pessoa.
    /// </summary>
    public partial interface IPessoaRepository : IRepository<Pessoa> { 
		Perlink.Trinks.Pessoas.Factories.IPessoaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaFisica.
    /// </summary>
    public partial interface IPessoaFisicaRepository : IRepository<PessoaFisica> { 
		Perlink.Trinks.Pessoas.Factories.IPessoaFisicaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridica.
    /// </summary>
    public partial interface IPessoaJuridicaRepository : IRepository<PessoaJuridica> { 
		Perlink.Trinks.Pessoas.Factories.IPessoaJuridicaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PessoaJuridicaConfiguracaoNFe.
    /// </summary>
    public partial interface IPessoaJuridicaConfiguracaoNFeRepository : IRepository<PessoaJuridicaConfiguracaoNFe> { 
		Perlink.Trinks.Pessoas.Factories.IPessoaJuridicaConfiguracaoNFeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Profissional.
    /// </summary>
    public partial interface IProfissionalRepository : IRepository<Profissional> { 
		Perlink.Trinks.Pessoas.Factories.IProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Questionario.
    /// </summary>
    public partial interface IQuestionarioRepository : IRepository<Questionario> { 
		Perlink.Trinks.Pessoas.Factories.IQuestionarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RecorrenciaHorario.
    /// </summary>
    public partial interface IRecorrenciaHorarioRepository : IRepository<RecorrenciaHorario> { 
		Perlink.Trinks.Pessoas.Factories.IRecorrenciaHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioFormaPagamento.
    /// </summary>
    public partial interface IRelatorioFormaPagamentoRepository : IRepository<RelatorioFormaPagamento> { 
		Perlink.Trinks.Pessoas.Factories.IRelatorioFormaPagamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SaldoDeSMSLembreteDoEstabelecimento.
    /// </summary>
    public partial interface ISaldoDeSMSLembreteDoEstabelecimentoRepository : IRepository<SaldoDeSMSLembreteDoEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.ISaldoDeSMSLembreteDoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Servico.
    /// </summary>
    public partial interface IServicoRepository : IRepository<Servico> { 
		Perlink.Trinks.Pessoas.Factories.IServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoCategoria.
    /// </summary>
    public partial interface IServicoCategoriaRepository : IRepository<ServicoCategoria> { 
		Perlink.Trinks.Pessoas.Factories.IServicoCategoriaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoCategoriaEstabelecimento.
    /// </summary>
    public partial interface IServicoCategoriaEstabelecimentoRepository : IRepository<ServicoCategoriaEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IServicoCategoriaEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoEstabelecimento.
    /// </summary>
    public partial interface IServicoEstabelecimentoRepository : IRepository<ServicoEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IServicoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ServicoSinonimo.
    /// </summary>
    public partial interface IServicoSinonimoRepository : IRepository<ServicoSinonimo> { 
		Perlink.Trinks.Pessoas.Factories.IServicoSinonimoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SincronizacaoEntreEstabelecimentosModelosFila.
    /// </summary>
    public partial interface ISincronizacaoEntreEstabelecimentosModelosFilaRepository : IRepository<SincronizacaoEntreEstabelecimentosModelosFila> { 
		Perlink.Trinks.Pessoas.Factories.ISincronizacaoEntreEstabelecimentosModelosFilaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SincronizacaoEstabelecimentosFila.
    /// </summary>
    public partial interface ISincronizacaoEstabelecimentosFilaRepository : IRepository<SincronizacaoEstabelecimentosFila> { 
		Perlink.Trinks.Pessoas.Factories.ISincronizacaoEstabelecimentosFilaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade StatusHorario.
    /// </summary>
    public partial interface IStatusHorarioRepository : IRepository<StatusHorario> { 
		Perlink.Trinks.Pessoas.Factories.IStatusHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Telefone.
    /// </summary>
    public partial interface ITelefoneRepository : IRepository<Telefone> { 
		Perlink.Trinks.Pessoas.Factories.ITelefoneFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TelefoneInternacional.
    /// </summary>
    public partial interface ITelefoneInternacionalRepository : IRepository<TelefoneInternacional> { 
		Perlink.Trinks.Pessoas.Factories.ITelefoneInternacionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TemaHotsite.
    /// </summary>
    public partial interface ITemaHotsiteRepository : IRepository<TemaHotsite> { 
		Perlink.Trinks.Pessoas.Factories.ITemaHotsiteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Template.
    /// </summary>
    public partial interface ITemplateRepository : IRepository<Template> { 
		Perlink.Trinks.Pessoas.Factories.ITemplateFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TemplateHotsite.
    /// </summary>
    public partial interface ITemplateHotsiteRepository : IRepository<TemplateHotsite> { 
		Perlink.Trinks.Pessoas.Factories.ITemplateHotsiteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoComissao.
    /// </summary>
    public partial interface ITipoComissaoRepository : IRepository<TipoComissao> { 
		Perlink.Trinks.Pessoas.Factories.ITipoComissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDesconto.
    /// </summary>
    public partial interface ITipoDescontoRepository : IRepository<TipoDesconto> { 
		Perlink.Trinks.Pessoas.Factories.ITipoDescontoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoEstabelecimento.
    /// </summary>
    public partial interface ITipoEstabelecimentoRepository : IRepository<TipoEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.ITipoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoFranquia.
    /// </summary>
    public partial interface ITipoFranquiaRepository : IRepository<TipoFranquia> { 
		Perlink.Trinks.Pessoas.Factories.ITipoFranquiaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoLogradouro.
    /// </summary>
    public partial interface ITipoLogradouroRepository : IRepository<TipoLogradouro> { 
		Perlink.Trinks.Pessoas.Factories.ITipoLogradouroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoPOS.
    /// </summary>
    public partial interface ITipoPOSRepository : IRepository<TipoPOS> { 
		Perlink.Trinks.Pessoas.Factories.ITipoPOSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoPreco.
    /// </summary>
    public partial interface ITipoPrecoRepository : IRepository<TipoPreco> { 
		Perlink.Trinks.Pessoas.Factories.ITipoPrecoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoRecorrenciaHorario.
    /// </summary>
    public partial interface ITipoRecorrenciaHorarioRepository : IRepository<TipoRecorrenciaHorario> { 
		Perlink.Trinks.Pessoas.Factories.ITipoRecorrenciaHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoServicoEstabelecimento.
    /// </summary>
    public partial interface ITipoServicoEstabelecimentoRepository : IRepository<TipoServicoEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.ITipoServicoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoTelefone.
    /// </summary>
    public partial interface ITipoTelefoneRepository : IRepository<TipoTelefone> { 
		Perlink.Trinks.Pessoas.Factories.ITipoTelefoneFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoTemplate.
    /// </summary>
    public partial interface ITipoTemplateRepository : IRepository<TipoTemplate> { 
		Perlink.Trinks.Pessoas.Factories.ITipoTemplateFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UF.
    /// </summary>
    public partial interface IUFRepository : IRepository<UF> { 
		Perlink.Trinks.Pessoas.Factories.IUFFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UnidadeMedida.
    /// </summary>
    public partial interface IUnidadeMedidaRepository : IRepository<UnidadeMedida> { 
		Perlink.Trinks.Pessoas.Factories.IUnidadeMedidaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade UsuarioEstabelecimento.
    /// </summary>
    public partial interface IUsuarioEstabelecimentoRepository : IRepository<UsuarioEstabelecimento> { 
		Perlink.Trinks.Pessoas.Factories.IUsuarioEstabelecimentoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Pessoas.Builders.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Configuracoes.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.DTO.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.DTO.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Helpers.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Interfaces.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Repositories.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Statics.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.Stories.Repositories {

 
}
namespace Perlink.Trinks.Pessoas.VO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Enums.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoProdutoCategoria.
    /// </summary>
    public partial interface IEstabelecimentoProdutoCategoriaRepository : IRepository<EstabelecimentoProdutoCategoria> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IEstabelecimentoProdutoCategoriaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Inventario.
    /// </summary>
    public partial interface IInventarioRepository : IRepository<Inventario> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IInventarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade InventarioMovimentacaoEstoque.
    /// </summary>
    public partial interface IInventarioMovimentacaoEstoqueRepository : IRepository<InventarioMovimentacaoEstoque> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IInventarioMovimentacaoEstoqueFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentoEstoqueTipo.
    /// </summary>
    public partial interface IMovimentoEstoqueTipoRepository : IRepository<MovimentoEstoqueTipo> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IMovimentoEstoqueTipoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OpcaoDaPropriedadeDeProduto.
    /// </summary>
    public partial interface IOpcaoDaPropriedadeDeProdutoRepository : IRepository<OpcaoDaPropriedadeDeProduto> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IOpcaoDaPropriedadeDeProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ProdutoCategoriaPadrao.
    /// </summary>
    public partial interface IProdutoCategoriaPadraoRepository : IRepository<ProdutoCategoriaPadrao> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IProdutoCategoriaPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ProdutoDoInventario.
    /// </summary>
    public partial interface IProdutoDoInventarioRepository : IRepository<ProdutoDoInventario> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IProdutoDoInventarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ProdutoPadrao.
    /// </summary>
    public partial interface IProdutoPadraoRepository : IRepository<ProdutoPadrao> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IProdutoPadraoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PropriedadeDeProduto.
    /// </summary>
    public partial interface IPropriedadeDeProdutoRepository : IRepository<PropriedadeDeProduto> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IPropriedadeDeProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorPropriedadeDoProduto.
    /// </summary>
    public partial interface IValorPropriedadeDoProdutoRepository : IRepository<ValorPropriedadeDoProduto> { 
		Perlink.Trinks.ProdutoEstoque.Factories.IValorPropriedadeDoProdutoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ProdutoEstoque.ExtensionMethods.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Factories.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Filtros.Repositories {

 
}
namespace Perlink.Trinks.ProdutoEstoque.Stories.Repositories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Repositories {

    /// <summary>
    /// Interface para repositório da entidade DisponibilidadeNaAgenda.
    /// </summary>
    public partial interface IDisponibilidadeNaAgendaRepository : IRepository<DisponibilidadeNaAgenda> { 
		Perlink.Trinks.ProfissionalAgenda.Factories.IDisponibilidadeNaAgendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LiberacaoDeHorarioNaAgenda.
    /// </summary>
    public partial interface ILiberacaoDeHorarioNaAgendaRepository : IRepository<LiberacaoDeHorarioNaAgenda> { 
		Perlink.Trinks.ProfissionalAgenda.Factories.ILiberacaoDeHorarioNaAgendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MotivoDeLiberacao.
    /// </summary>
    public partial interface IMotivoDeLiberacaoRepository : IRepository<MotivoDeLiberacao> { 
		Perlink.Trinks.ProfissionalAgenda.Factories.IMotivoDeLiberacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioAusenciaELiberacaoHorario.
    /// </summary>
    public partial interface IRelatorioAusenciaELiberacaoHorarioRepository : IRepository<RelatorioAusenciaELiberacaoHorario> { 
		Perlink.Trinks.ProfissionalAgenda.Factories.IRelatorioAusenciaELiberacaoHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoDeDisponibilidade.
    /// </summary>
    public partial interface ITipoDeDisponibilidadeRepository : IRepository<TipoDeDisponibilidade> { 
		Perlink.Trinks.ProfissionalAgenda.Factories.ITipoDeDisponibilidadeFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ProfissionalAgenda.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Enums.Repositories {

 
}
namespace Perlink.Trinks.ProfissionalAgenda.Stories.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Cupom.
    /// </summary>
    public partial interface ICupomRepository : IRepository<Cupom> { 
		Perlink.Trinks.ProjetoBackToSalon.Factories.ICupomFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParticipanteBTS.
    /// </summary>
    public partial interface IEstabelecimentoParticipanteBTSRepository : IRepository<EstabelecimentoParticipanteBTS> { 
		Perlink.Trinks.ProjetoBackToSalon.Factories.IEstabelecimentoParticipanteBTSFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ProjetoBackToSalon.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Enum.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBackToSalon.Filters.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CompraDeVoucher.
    /// </summary>
    public partial interface ICompraDeVoucherRepository : IRepository<CompraDeVoucher> { 
		Perlink.Trinks.ProjetoBelezaAmiga.Factories.ICompraDeVoucherFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParticipante.
    /// </summary>
    public partial interface IEstabelecimentoParticipanteRepository : IRepository<EstabelecimentoParticipante> { 
		Perlink.Trinks.ProjetoBelezaAmiga.Factories.IEstabelecimentoParticipanteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Voucher.
    /// </summary>
    public partial interface IVoucherRepository : IRepository<Voucher> { 
		Perlink.Trinks.ProjetoBelezaAmiga.Factories.IVoucherFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProjetoBelezaAmiga.Enums.Repositories {

 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoParticipanteESSV2.
    /// </summary>
    public partial interface IEstabelecimentoParticipanteESSV2Repository : IRepository<EstabelecimentoParticipanteESSV2> { 
		Perlink.Trinks.ProjetoEncontreSeuSalao.Factories.IEstabelecimentoParticipanteESSV2Factory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade GmapsLimitStatus.
    /// </summary>
    public partial interface IGmapsLimitStatusRepository : IRepository<GmapsLimitStatus> { 
		Perlink.Trinks.ProjetoEncontreSeuSalao.Factories.IGmapsLimitStatusFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.DTO.Repositories {

 
}
namespace Perlink.Trinks.ProjetoEncontreSeuSalao.Filters.Repositories {

 
}
namespace Perlink.Trinks.Promocoes.Enums.Repositories {

 
}
namespace Perlink.Trinks.Promocoes.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Promocao.
    /// </summary>
    public partial interface IPromocaoRepository : IRepository<Promocao> { 
		Perlink.Trinks.Promocoes.Factories.IPromocaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoDiaDaSemana.
    /// </summary>
    public partial interface IPromocaoDiaDaSemanaRepository : IRepository<PromocaoDiaDaSemana> { 
		Perlink.Trinks.Promocoes.Factories.IPromocaoDiaDaSemanaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.PromocoesOnline.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AgendamentoTemporario.
    /// </summary>
    public partial interface IAgendamentoTemporarioRepository : IRepository<AgendamentoTemporario> { 
		Perlink.Trinks.PromocoesOnline.Factories.IAgendamentoTemporarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorariosDaPromocao.
    /// </summary>
    public partial interface IHorariosDaPromocaoRepository : IRepository<HorariosDaPromocao> { 
		Perlink.Trinks.PromocoesOnline.Factories.IHorariosDaPromocaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PromocaoOnline.
    /// </summary>
    public partial interface IPromocaoOnlineRepository : IRepository<PromocaoOnline> { 
		Perlink.Trinks.PromocoesOnline.Factories.IPromocaoOnlineFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.PromocoesOnline.DTO.Repositories {

 
}
namespace Perlink.Trinks.PromotoresDoTrinks.Repositories {

    /// <summary>
    /// Interface para repositório da entidade HistoricoUsoCuponsParceria.
    /// </summary>
    public partial interface IHistoricoUsoCuponsParceriaRepository : IRepository<HistoricoUsoCuponsParceria> { 
		Perlink.Trinks.PromotoresDoTrinks.Factories.IHistoricoUsoCuponsParceriaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NovosProfissionaisPromotoresDoTrinks.
    /// </summary>
    public partial interface INovosProfissionaisPromotoresDoTrinksRepository : IRepository<NovosProfissionaisPromotoresDoTrinks> { 
		Perlink.Trinks.PromotoresDoTrinks.Factories.INovosProfissionaisPromotoresDoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PromotorDoTrinks.
    /// </summary>
    public partial interface IPromotorDoTrinksRepository : IRepository<PromotorDoTrinks> { 
		Perlink.Trinks.PromotoresDoTrinks.Factories.IPromotorDoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SaldoPromotor.
    /// </summary>
    public partial interface ISaldoPromotorRepository : IRepository<SaldoPromotor> { 
		Perlink.Trinks.PromotoresDoTrinks.Factories.ISaldoPromotorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TransacaoPromotor.
    /// </summary>
    public partial interface ITransacaoPromotorRepository : IRepository<TransacaoPromotor> { 
		Perlink.Trinks.PromotoresDoTrinks.Factories.ITransacaoPromotorFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.PromotoresDoTrinks.DTO.Repositories {

 
}
namespace Perlink.Trinks.PromotoresDoTrinks.Stories.Repositories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AssinaturaRecorrente.
    /// </summary>
    public partial interface IAssinaturaRecorrenteRepository : IRepository<AssinaturaRecorrente> { 
		Perlink.Trinks.RecorrenciaDeAssinatura.Factories.IAssinaturaRecorrenteFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.DTO.Repositories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Enums.Repositories {

 
}
namespace Perlink.Trinks.RecorrenciaDeAssinatura.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ClienteAtendido.
    /// </summary>
    public partial interface IClienteAtendidoRepository : IRepository<ClienteAtendido> { 
		Perlink.Trinks.Relatorios.Factories.IClienteAtendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConsultaRelatorioConsolidadoDia.
    /// </summary>
    public partial interface IConsultaRelatorioConsolidadoDiaRepository : IRepository<ConsultaRelatorioConsolidadoDia> { 
		Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoDiaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConsultaRelatorioConsolidadoEstabelecimentoClienteMes.
    /// </summary>
    public partial interface IConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository : IRepository<ConsultaRelatorioConsolidadoEstabelecimentoClienteMes> { 
		Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoEstabelecimentoClienteMesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConsultaRelatorioConsolidadoMes.
    /// </summary>
    public partial interface IConsultaRelatorioConsolidadoMesRepository : IRepository<ConsultaRelatorioConsolidadoMes> { 
		Perlink.Trinks.Relatorios.Factories.IConsultaRelatorioConsolidadoMesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeCliente.
    /// </summary>
    public partial interface IRankingDeClienteRepository : IRepository<RankingDeCliente> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeClienteEstendido.
    /// </summary>
    public partial interface IRankingDeClienteEstendidoRepository : IRepository<RankingDeClienteEstendido> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeClienteEstendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDePacotes.
    /// </summary>
    public partial interface IRankingDePacotesRepository : IRepository<RankingDePacotes> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDePacotesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDePacotesEstendido.
    /// </summary>
    public partial interface IRankingDePacotesEstendidoRepository : IRepository<RankingDePacotesEstendido> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDePacotesEstendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProdutos.
    /// </summary>
    public partial interface IRankingDeProdutosRepository : IRepository<RankingDeProdutos> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeProdutosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProdutosEstendido.
    /// </summary>
    public partial interface IRankingDeProdutosEstendidoRepository : IRepository<RankingDeProdutosEstendido> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeProdutosEstendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProfissionais.
    /// </summary>
    public partial interface IRankingDeProfissionaisRepository : IRepository<RankingDeProfissionais> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeProfissionaisFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeProfissionaisEstendido.
    /// </summary>
    public partial interface IRankingDeProfissionaisEstendidoRepository : IRepository<RankingDeProfissionaisEstendido> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeProfissionaisEstendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeServicos.
    /// </summary>
    public partial interface IRankingDeServicosRepository : IRepository<RankingDeServicos> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeServicosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingDeServicosEstendido.
    /// </summary>
    public partial interface IRankingDeServicosEstendidoRepository : IRepository<RankingDeServicosEstendido> { 
		Perlink.Trinks.Relatorios.Factories.IRankingDeServicosEstendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingEstendidoEstabelecimentos.
    /// </summary>
    public partial interface IRankingEstendidoEstabelecimentosRepository : IRepository<RankingEstendidoEstabelecimentos> { 
		Perlink.Trinks.Relatorios.Factories.IRankingEstendidoEstabelecimentosFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingItensDePacotes.
    /// </summary>
    public partial interface IRankingItensDePacotesRepository : IRepository<RankingItensDePacotes> { 
		Perlink.Trinks.Relatorios.Factories.IRankingItensDePacotesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RankingItensDePacotesEstendido.
    /// </summary>
    public partial interface IRankingItensDePacotesEstendidoRepository : IRepository<RankingItensDePacotesEstendido> { 
		Perlink.Trinks.Relatorios.Factories.IRankingItensDePacotesEstendidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioDemonstrativoDeResultado.
    /// </summary>
    public partial interface IRelatorioDemonstrativoDeResultadoRepository : IRepository<RelatorioDemonstrativoDeResultado> { 
		Perlink.Trinks.Relatorios.Factories.IRelatorioDemonstrativoDeResultadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RelatorioDemonstrativoDeResultadoReceita.
    /// </summary>
    public partial interface IRelatorioDemonstrativoDeResultadoReceitaRepository : IRepository<RelatorioDemonstrativoDeResultadoReceita> { 
		Perlink.Trinks.Relatorios.Factories.IRelatorioDemonstrativoDeResultadoReceitaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TelaRelatorio.
    /// </summary>
    public partial interface ITelaRelatorioRepository : IRepository<TelaRelatorio> { 
		Perlink.Trinks.Relatorios.Factories.ITelaRelatorioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TelaRelatorioCategoria.
    /// </summary>
    public partial interface ITelaRelatorioCategoriaRepository : IRepository<TelaRelatorioCategoria> { 
		Perlink.Trinks.Relatorios.Factories.ITelaRelatorioCategoriaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Relatorios.DTO.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.DTO.DemonstrativoResultado.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.DTO.RetornoDeClientes.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.Enums.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.Filtros.Repositories {

 
}
namespace Perlink.Trinks.Relatorios.MapaDeCalor.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Repositories {

    /// <summary>
    /// Interface para repositório da entidade ColocacaoDoProfissional.
    /// </summary>
    public partial interface IColocacaoDoProfissionalRepository : IRepository<ColocacaoDoProfissional> { 
		Perlink.Trinks.RodizioDeProfissionais.Factories.IColocacaoDoProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioNoRodizio.
    /// </summary>
    public partial interface IHorarioNoRodizioRepository : IRepository<HorarioNoRodizio> { 
		Perlink.Trinks.RodizioDeProfissionais.Factories.IHorarioNoRodizioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoNoRodizio.
    /// </summary>
    public partial interface IMovimentacaoNoRodizioRepository : IRepository<MovimentacaoNoRodizio> { 
		Perlink.Trinks.RodizioDeProfissionais.Factories.IMovimentacaoNoRodizioFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.RodizioDeProfissionais.DTO.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Enums.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Factories.Repositories {

 
}
namespace Perlink.Trinks.RodizioDeProfissionais.Stories.Repositories {

 
}
namespace Perlink.Trinks.RPS.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CobRpsEmissao.
    /// </summary>
    public partial interface ICobRpsEmissaoRepository : IRepository<CobRpsEmissao> { 
		Perlink.Trinks.RPS.Factories.ICobRpsEmissaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CobRpsLote.
    /// </summary>
    public partial interface ICobRpsLoteRepository : IRepository<CobRpsLote> { 
		Perlink.Trinks.RPS.Factories.ICobRpsLoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracaoPadraoNFS.
    /// </summary>
    public partial interface IConfiguracaoPadraoNFSRepository : IRepository<ConfiguracaoPadraoNFS> { 
		Perlink.Trinks.RPS.Factories.IConfiguracaoPadraoNFSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DadosRPSTransacao.
    /// </summary>
    public partial interface IDadosRPSTransacaoRepository : IRepository<DadosRPSTransacao> { 
		Perlink.Trinks.RPS.Factories.IDadosRPSTransacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EmissaoRPS.
    /// </summary>
    public partial interface IEmissaoRPSRepository : IRepository<EmissaoRPS> { 
		Perlink.Trinks.RPS.Factories.IEmissaoRPSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade LoteRPS.
    /// </summary>
    public partial interface ILoteRPSRepository : IRepository<LoteRPS> { 
		Perlink.Trinks.RPS.Factories.ILoteRPSFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MunicipioPadraoNfse.
    /// </summary>
    public partial interface IMunicipioPadraoNfseRepository : IRepository<MunicipioPadraoNfse> { 
		Perlink.Trinks.RPS.Factories.IMunicipioPadraoNfseFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade NfseConfiguracaoMunicipio.
    /// </summary>
    public partial interface INfseConfiguracaoMunicipioRepository : IRepository<NfseConfiguracaoMunicipio> { 
		Perlink.Trinks.RPS.Factories.INfseConfiguracaoMunicipioFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.RPS.DTO.Repositories {

 
}
namespace Perlink.Trinks.RPS.Enums.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.ABRASF.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.BHISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.DSF.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.EGOVERNE.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.GINFES.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.Ginfes_SJRP.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.GINFES3.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.LONDRINA_SIGCORP_SIGISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.NFCe.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.PAULISTANA.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SALVADOR_BA.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SIGCORP_SIGISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.SIMPLISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.VVISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.GeradorDeArquivo.WEBISS.Repositories {

 
}
namespace Perlink.Trinks.RPS.Integracao.Repositories {

 
}
namespace Perlink.Trinks.RPS.Integracao.Schemas.Repositories {

 
}
namespace Perlink.Trinks.RPS.ManipuladoresDeStreamParaRPS.Repositories {

 
}
namespace Perlink.Trinks.Seguranca.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AcoesProibidasMvc.
    /// </summary>
    public partial interface IAcoesProibidasMvcRepository : IRepository<AcoesProibidasMvc> { 
		Perlink.Trinks.Seguranca.Factories.IAcoesProibidasMvcFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ApiClient.
    /// </summary>
    public partial interface IApiClientRepository : IRepository<ApiClient> { 
		Perlink.Trinks.Seguranca.Factories.IApiClientFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ApiRefreshToken.
    /// </summary>
    public partial interface IApiRefreshTokenRepository : IRepository<ApiRefreshToken> { 
		Perlink.Trinks.Seguranca.Factories.IApiRefreshTokenFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Seguranca.DTO.Repositories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories {

    /// <summary>
    /// Interface para repositório da entidade CancelamentoDoPedido.
    /// </summary>
    public partial interface ICancelamentoDoPedidoRepository : IRepository<CancelamentoDoPedido> { 
		Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.ICancelamentoDoPedidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstabelecimentoMovimentacaoEstoquePedido.
    /// </summary>
    public partial interface IEstabelecimentoMovimentacaoEstoquePedidoRepository : IRepository<EstabelecimentoMovimentacaoEstoquePedido> { 
		Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IEstabelecimentoMovimentacaoEstoquePedidoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade InformacaoAlterada.
    /// </summary>
    public partial interface IInformacaoAlteradaRepository : IRepository<InformacaoAlterada> { 
		Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IInformacaoAlteradaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemDePedidoDeCompra.
    /// </summary>
    public partial interface IItemDePedidoDeCompraRepository : IRepository<ItemDePedidoDeCompra> { 
		Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IItemDePedidoDeCompraFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PedidoDeCompra.
    /// </summary>
    public partial interface IPedidoDeCompraRepository : IRepository<PedidoDeCompra> { 
		Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IPedidoDeCompraFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade RegistroDeAlteracao.
    /// </summary>
    public partial interface IRegistroDeAlteracaoRepository : IRepository<RegistroDeAlteracao> { 
		Perlink.Trinks.SugestoesEPedidosDeCompra.Factories.IRegistroDeAlteracaoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.DTO.Repositories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Repositories {

 
}
namespace Perlink.Trinks.SugestoesEPedidosDeCompra.Filtros.Repositories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Enums.Repositories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Stories.Repositories {

 
}
namespace Perlink.Trinks.SurveyAppB2B.Repositories {

    /// <summary>
    /// Interface para repositório da entidade SurveyAppPro.
    /// </summary>
    public partial interface ISurveyAppProRepository : IRepository<SurveyAppPro> { 
		Perlink.Trinks.SurveyAppB2B.Factories.ISurveyAppProFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.TesteAB.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Amostra.
    /// </summary>
    public partial interface IAmostraRepository : IRepository<Amostra> { 
		Perlink.Trinks.TesteAB.Factories.IAmostraFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Grupo.
    /// </summary>
    public partial interface IGrupoRepository : IRepository<Grupo> { 
		Perlink.Trinks.TesteAB.Factories.IGrupoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Metrica.
    /// </summary>
    public partial interface IMetricaRepository : IRepository<Metrica> { 
		Perlink.Trinks.TesteAB.Factories.IMetricaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.TesteAB.DTO.Repositories {

 
}
namespace Perlink.Trinks.TesteAB.Enums.Repositories {

 
}
namespace Perlink.Trinks.TestesAB.Enums.Repositories {

 
}
namespace Perlink.Trinks.TestesAB.Repositories {

    /// <summary>
    /// Interface para repositório da entidade TesteABAssinatura.
    /// </summary>
    public partial interface ITesteABAssinaturaRepository : IRepository<TesteABAssinatura> { 
		Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABAssinaturaPlanoAssinatura.
    /// </summary>
    public partial interface ITesteABAssinaturaPlanoAssinaturaRepository : IRepository<TesteABAssinaturaPlanoAssinatura> { 
		Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaPlanoAssinaturaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABAssinaturaPlanoAssinaturaEstabelecimento.
    /// </summary>
    public partial interface ITesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository : IRepository<TesteABAssinaturaPlanoAssinaturaEstabelecimento> { 
		Perlink.Trinks.TestesAB.Factories.ITesteABAssinaturaPlanoAssinaturaEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABWhyTrinks.
    /// </summary>
    public partial interface ITesteABWhyTrinksRepository : IRepository<TesteABWhyTrinks> { 
		Perlink.Trinks.TestesAB.Factories.ITesteABWhyTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TesteABWhyTrinksHistorico.
    /// </summary>
    public partial interface ITesteABWhyTrinksHistoricoRepository : IRepository<TesteABWhyTrinksHistorico> { 
		Perlink.Trinks.TestesAB.Factories.ITesteABWhyTrinksHistoricoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.TrinksApps.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AplicativoDeAgendamento.
    /// </summary>
    public partial interface IAplicativoDeAgendamentoRepository : IRepository<AplicativoDeAgendamento> { 
		Perlink.Trinks.TrinksApps.Factories.IAplicativoDeAgendamentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AplicativoDeAgendamentoFuncionalidades.
    /// </summary>
    public partial interface IAplicativoDeAgendamentoFuncionalidadesRepository : IRepository<AplicativoDeAgendamentoFuncionalidades> { 
		Perlink.Trinks.TrinksApps.Factories.IAplicativoDeAgendamentoFuncionalidadesFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ConfiguracoesAppProfissional.
    /// </summary>
    public partial interface IConfiguracoesAppProfissionalRepository : IRepository<ConfiguracoesAppProfissional> { 
		Perlink.Trinks.TrinksApps.Factories.IConfiguracoesAppProfissionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DadosOnboardingOQueProcuraNoTrinks.
    /// </summary>
    public partial interface IDadosOnboardingOQueProcuraNoTrinksRepository : IRepository<DadosOnboardingOQueProcuraNoTrinks> { 
		Perlink.Trinks.TrinksApps.Factories.IDadosOnboardingOQueProcuraNoTrinksFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade DispositivoComAplicativo.
    /// </summary>
    public partial interface IDispositivoComAplicativoRepository : IRepository<DispositivoComAplicativo> { 
		Perlink.Trinks.TrinksApps.Factories.IDispositivoComAplicativoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FuncionalidadeDoAplicativoDeAgendamento.
    /// </summary>
    public partial interface IFuncionalidadeDoAplicativoDeAgendamentoRepository : IRepository<FuncionalidadeDoAplicativoDeAgendamento> { 
		Perlink.Trinks.TrinksApps.Factories.IFuncionalidadeDoAplicativoDeAgendamentoFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.TrinksApps.Dto.Repositories {

 
}
namespace Perlink.Trinks.TrinksApps.Enums.Repositories {

 
}
namespace Perlink.Trinks.TrinksApps.ObjectValues.Repositories {

 
}
namespace Perlink.Trinks.TrinksAtendimento.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AssuntoFaleConoscoTrinksProfissional.
    /// </summary>
    public partial interface IAssuntoFaleConoscoTrinksProfissionalRepository : IRepository<AssuntoFaleConoscoTrinksProfissional> { 
		Perlink.Trinks.TrinksAtendimento.Factories.IAssuntoFaleConoscoTrinksProfissionalFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.TrinksAtendimento.DTOs.Repositories {

 
}
namespace Perlink.Trinks.TrinksAtendimento.Stories.Repositories {

 
}
namespace Perlink.Trinks.TrinksPay.Stories.Repositories {

 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.DTO.Repositories {

 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.Repositories {

    /// <summary>
    /// Interface para repositório da entidade EmailVerificado.
    /// </summary>
    public partial interface IEmailVerificadoRepository : IRepository<EmailVerificado> { 
		Perlink.Trinks.ValidacaoDeIdentidade.Factories.IEmailVerificadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TelefoneVerificado.
    /// </summary>
    public partial interface ITelefoneVerificadoRepository : IRepository<TelefoneVerificado> { 
		Perlink.Trinks.ValidacaoDeIdentidade.Factories.ITelefoneVerificadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VerificacaoDeContaPelaAreaPerlink.
    /// </summary>
    public partial interface IVerificacaoDeContaPelaAreaPerlinkRepository : IRepository<VerificacaoDeContaPelaAreaPerlink> { 
		Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeContaPelaAreaPerlinkFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VerificacaoDeIdentidade.
    /// </summary>
    public partial interface IVerificacaoDeIdentidadeRepository : IRepository<VerificacaoDeIdentidade> { 
		Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeIdentidadeFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade VerificacaoDeIdentidadeEnvio.
    /// </summary>
    public partial interface IVerificacaoDeIdentidadeEnvioRepository : IRepository<VerificacaoDeIdentidadeEnvio> { 
		Perlink.Trinks.ValidacaoDeIdentidade.Factories.IVerificacaoDeIdentidadeEnvioFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.ValidacaoDeIdentidade.Enums.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Repositories {

    /// <summary>
    /// Interface para repositório da entidade Comanda.
    /// </summary>
    public partial interface IComandaRepository : IRepository<Comanda> { 
		Perlink.Trinks.Vendas.Factories.IComandaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVenda.
    /// </summary>
    public partial interface IItemVendaRepository : IRepository<ItemVenda> { 
		Perlink.Trinks.Vendas.Factories.IItemVendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaAssinaturaCliente.
    /// </summary>
    public partial interface IItemVendaAssinaturaClienteRepository : IRepository<ItemVendaAssinaturaCliente> { 
		Perlink.Trinks.Vendas.Factories.IItemVendaAssinaturaClienteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaPacote.
    /// </summary>
    public partial interface IItemVendaPacoteRepository : IRepository<ItemVendaPacote> { 
		Perlink.Trinks.Vendas.Factories.IItemVendaPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaProduto.
    /// </summary>
    public partial interface IItemVendaProdutoRepository : IRepository<ItemVendaProduto> { 
		Perlink.Trinks.Vendas.Factories.IItemVendaProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ItemVendaValePresente.
    /// </summary>
    public partial interface IItemVendaValePresenteRepository : IRepository<ItemVendaValePresente> { 
		Perlink.Trinks.Vendas.Factories.IItemVendaValePresenteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreVenda.
    /// </summary>
    public partial interface IPreVendaRepository : IRepository<PreVenda> { 
		Perlink.Trinks.Vendas.Factories.IPreVendaFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaProduto.
    /// </summary>
    public partial interface IPreVendaProdutoRepository : IRepository<PreVendaProduto> { 
		Perlink.Trinks.Vendas.Factories.IPreVendaProdutoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaServico.
    /// </summary>
    public partial interface IPreVendaServicoRepository : IRepository<PreVendaServico> { 
		Perlink.Trinks.Vendas.Factories.IPreVendaServicoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaHistorico.
    /// </summary>
    public partial interface IPreVendaHistoricoRepository : IRepository<PreVendaHistorico> { 
		Perlink.Trinks.Vendas.Factories.IPreVendaHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PreVendaStatus.
    /// </summary>
    public partial interface IPreVendaStatusRepository : IRepository<PreVendaStatus> { 
		Perlink.Trinks.Vendas.Factories.IPreVendaStatusFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValePresente.
    /// </summary>
    public partial interface IValePresenteRepository : IRepository<ValePresente> { 
		Perlink.Trinks.Vendas.Factories.IValePresenteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade Venda.
    /// </summary>
    public partial interface IVendaRepository : IRepository<Venda> { 
		Perlink.Trinks.Vendas.Factories.IVendaFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.Vendas.DTO.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Enums.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Factories.Repositories {

 
}
namespace Perlink.Trinks.Vendas.Repositories.Filtros.Repositories {

 
}
namespace Perlink.Trinks.VO.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Repositories {

    /// <summary>
    /// Interface para repositório da entidade AllowedTestEstablishments.
    /// </summary>
    public partial interface IAllowedTestEstablishmentsRepository : IRepository<AllowedTestEstablishments> { 
		Perlink.Trinks.WhatsApp.Factories.IAllowedTestEstablishmentsFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade AvaliacaoHistorico.
    /// </summary>
    public partial interface IAvaliacaoHistoricoRepository : IRepository<AvaliacaoHistorico> { 
		Perlink.Trinks.WhatsApp.Factories.IAvaliacaoHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CartaoEstabelecimento.
    /// </summary>
    public partial interface ICartaoEstabelecimentoRepository : IRepository<CartaoEstabelecimento> { 
		Perlink.Trinks.WhatsApp.Factories.ICartaoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CompraCredito.
    /// </summary>
    public partial interface ICompraCreditoRepository : IRepository<CompraCredito> { 
		Perlink.Trinks.WhatsApp.Factories.ICompraCreditoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CompraRecorrenteEstabelecimento.
    /// </summary>
    public partial interface ICompraRecorrenteEstabelecimentoRepository : IRepository<CompraRecorrenteEstabelecimento> { 
		Perlink.Trinks.WhatsApp.Factories.ICompraRecorrenteEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade CompraRecorrenteEstabelecimentoHistorico.
    /// </summary>
    public partial interface ICompraRecorrenteEstabelecimentoHistoricoRepository : IRepository<CompraRecorrenteEstabelecimentoHistorico> { 
		Perlink.Trinks.WhatsApp.Factories.ICompraRecorrenteEstabelecimentoHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade EstablishmentConfiguration.
    /// </summary>
    public partial interface IEstablishmentConfigurationRepository : IRepository<EstablishmentConfiguration> { 
		Perlink.Trinks.WhatsApp.Factories.IEstablishmentConfigurationFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade FranquiaValor.
    /// </summary>
    public partial interface IFranquiaValorRepository : IRepository<FranquiaValor> { 
		Perlink.Trinks.WhatsApp.Factories.IFranquiaValorFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoCompraAdicional.
    /// </summary>
    public partial interface IHistoricoCompraAdicionalRepository : IRepository<HistoricoCompraAdicional> { 
		Perlink.Trinks.WhatsApp.Factories.IHistoricoCompraAdicionalFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoComunicacaoHorario.
    /// </summary>
    public partial interface IHistoricoComunicacaoHorarioRepository : IRepository<HistoricoComunicacaoHorario> { 
		Perlink.Trinks.WhatsApp.Factories.IHistoricoComunicacaoHorarioFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoHorarioTag.
    /// </summary>
    public partial interface IHistoricoHorarioTagRepository : IRepository<HistoricoHorarioTag> { 
		Perlink.Trinks.WhatsApp.Factories.IHistoricoHorarioTagFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoSessao.
    /// </summary>
    public partial interface IHistoricoSessaoRepository : IRepository<HistoricoSessao> { 
		Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoSessaoProcesso.
    /// </summary>
    public partial interface IHistoricoSessaoProcessoRepository : IRepository<HistoricoSessaoProcesso> { 
		Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoProcessoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HistoricoSessaoProcessoRegistro.
    /// </summary>
    public partial interface IHistoricoSessaoProcessoRegistroRepository : IRepository<HistoricoSessaoProcessoRegistro> { 
		Perlink.Trinks.WhatsApp.Factories.IHistoricoSessaoProcessoRegistroFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioComunicacao.
    /// </summary>
    public partial interface IHorarioComunicacaoRepository : IRepository<HorarioComunicacao> { 
		Perlink.Trinks.WhatsApp.Factories.IHorarioComunicacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade HorarioTag.
    /// </summary>
    public partial interface IHorarioTagRepository : IRepository<HorarioTag> { 
		Perlink.Trinks.WhatsApp.Factories.IHorarioTagFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MessageTemplate.
    /// </summary>
    public partial interface IMessageTemplateRepository : IRepository<MessageTemplate> { 
		Perlink.Trinks.WhatsApp.Factories.IMessageTemplateFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade MovimentacaoSaldo.
    /// </summary>
    public partial interface IMovimentacaoSaldoRepository : IRepository<MovimentacaoSaldo> { 
		Perlink.Trinks.WhatsApp.Factories.IMovimentacaoSaldoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OptOut.
    /// </summary>
    public partial interface IOptOutRepository : IRepository<OptOut> { 
		Perlink.Trinks.WhatsApp.Factories.IOptOutFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade OptOutHistorico.
    /// </summary>
    public partial interface IOptOutHistoricoRepository : IRepository<OptOutHistorico> { 
		Perlink.Trinks.WhatsApp.Factories.IOptOutHistoricoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteCredito.
    /// </summary>
    public partial interface IPacoteCreditoRepository : IRepository<PacoteCredito> { 
		Perlink.Trinks.WhatsApp.Factories.IPacoteCreditoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PacoteCreditoFormaContratacao.
    /// </summary>
    public partial interface IPacoteCreditoFormaContratacaoRepository : IRepository<PacoteCreditoFormaContratacao> { 
		Perlink.Trinks.WhatsApp.Factories.IPacoteCreditoFormaContratacaoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade PacotePersonalizado.
    /// </summary>
    public partial interface IPacotePersonalizadoRepository : IRepository<PacotePersonalizado> { 
		Perlink.Trinks.WhatsApp.Factories.IPacotePersonalizadoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade SaldoEstabelecimento.
    /// </summary>
    public partial interface ISaldoEstabelecimentoRepository : IRepository<SaldoEstabelecimento> { 
		Perlink.Trinks.WhatsApp.Factories.ISaldoEstabelecimentoFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade TipoValorPacote.
    /// </summary>
    public partial interface ITipoValorPacoteRepository : IRepository<TipoValorPacote> { 
		Perlink.Trinks.WhatsApp.Factories.ITipoValorPacoteFactory Factory {get;}		
    }
    /// <summary>
    /// Interface para repositório da entidade ValorPacote.
    /// </summary>
    public partial interface IValorPacoteRepository : IRepository<ValorPacote> { 
		Perlink.Trinks.WhatsApp.Factories.IValorPacoteFactory Factory {get;}		
    }
 
}
namespace Perlink.Trinks.WhatsApp.DTO.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Enums.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Exceptions.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Factories.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Filters.Repositories {

 
}
namespace Perlink.Trinks.WhatsApp.Strategies.Repositories {

 
}
namespace Perlink.Trinks.WhyTrinks.Enums.Repositories {

 
}
namespace Perlink.Trinks.WhyTrinks.Repositories {

 
}
namespace Perlink.Trinks.Wrapper.Repositories {

 
}
