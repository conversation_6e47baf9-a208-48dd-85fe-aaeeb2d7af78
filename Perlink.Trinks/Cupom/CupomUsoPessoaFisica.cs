﻿using Castle.ActiveRecord;

namespace Perlink.Trinks.Cupom
{
    [ActiveRecord("Cupom_Uso_Pessoa_Fisica", Schema = "Cupom", DynamicInsert = true, DynamicUpdate = true)]
    public class CupomUsoPessoaFisica
    {
        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("id_cupom", NotNull = true)]
        public virtual int IdCupom { get; set; }

        [Property("id_pessoa", NotNull = true)]
        public virtual int IdPessoa { get; set; }

        [Property("qtd", NotNull = true)]
        public virtual int Quantidade { get; set; }

        [Property("associado_automaticamente", NotNull = true)]
        public virtual bool AssociadoAutomaticamente { get; set; }

        public CupomUsoPessoaFisica() { }

        public CupomUsoPessoaFisica(int idCupom, int idPessoa, int qtd, bool associadoAutomaticamente)
        {
            IdCupom = idCupom;
            IdPessoa = idPessoa;
            Quantidade = qtd;
            AssociadoAutomaticamente = associadoAutomaticamente;
        }
    }
}
