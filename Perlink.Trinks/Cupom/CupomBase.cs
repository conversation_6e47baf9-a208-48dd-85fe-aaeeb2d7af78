using Castle.ActiveRecord;
using NHibernate.Mapping;
using Perlink.Trinks.Cupom.DTO;
using System;

namespace Perlink.Trinks.Cupom
{
    [ActiveRecord("Cupom", Schema = "Cupom", DynamicInsert = true, DynamicUpdate = true), JoinedBase]
    public class CupomBase : ActiveRecordBase
    {

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("codigo", NotNull = true)]
        public virtual string Codigo { get; set; }

        [Property("descricao")]
        public virtual string Descricao { get; set; }

        [Property("nome", NotNull = true)]
        public virtual string Nome { get; set; }

        [Nested("validade_")]
        public virtual Intervalo Validade { get; set; }

        [Nested("resgate_")]
        public virtual Intervalo Resgate { get; set; }

        [Property("ativo_computado", Insert = false, Update = false)]
        public virtual bool Ativo { get; set; }

        [Property("data_inativacao")]
        public virtual DateTime? DataInativacao { get; set; }

        [Property("qtd_uso_cliente")]
        public virtual int QtdUsoCliente { get; set; }

        public CupomBase() { }
        public CupomBase(CupomResponsavelDTO dadosCupom)
        {
            Codigo = dadosCupom.Codigo;
            Validade = dadosCupom.Validade;
            Resgate = dadosCupom.Resgate;
            Descricao = dadosCupom.Descricao;
            Nome = dadosCupom.Nome;
            DataInativacao = dadosCupom.DataInativacao;
            QtdUsoCliente = dadosCupom.QtdUsoCliente;
        }
    }
}