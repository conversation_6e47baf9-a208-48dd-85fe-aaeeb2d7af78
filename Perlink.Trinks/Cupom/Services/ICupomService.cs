﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Cupom.DTO;
using Perlink.Trinks.Cupom.Filters;
using Perlink.Trinks.Cupom.Models;
using System.Collections.Generic;

namespace Perlink.Trinks.Cupom.Services
{

    public interface ICupomService : IService
    {

        bool CriarCupom(CriacaoCupomDTO dadosVoucher);

        bool CriarPrimeiroCupom(CriacaoCupomDTO dadosVoucher);


        ControleDeCupomDTO ListarCuponsEstabelecimento(ControleDeCupomFilter filter);

        CuponsInfosDTO ListaCuponsInfos(int idPessoa);

        int? ObterCuponsResgatadosContagem(int idCupom);

        int? ObterCuponsDisponiveisContagem(int idPessoa);

        IList<string> ObterNomesServicosDoCupom(IList<int?> idsServicos, bool todosServiços = false);

        IList<string> ObterNomesProdutosDoCupom(IList<int?> idsProdutos, bool todosProdutos = false);

        IList<string> ObterNomesClientesDoCupom(int idCupom);

        CuponsInfosDTO ObterCupomPorCodigo(string codigoCupom, int? idPessoa);

        CupomInfosDTO ObterCupomPorId(int idCupom);

        List<ClienteEstabelecimentoDTO> ObterClientesSelecionados(int idCupom);

        List<ClienteEstabelecimentoDTO> ListarClientesEstabelecimentoParaAssociar(string conteudoFiltro, int idCupom);

        void AssociarClientesAoCupom(AssociacaoClienteInfosModel model);

        bool EstabelecimentoTemCupom();

        bool InativarCupom(int idCupom);

        bool CriarCupomResponsavel(CriacaoCupomDTO dadosCupom);

        void CriarCupomPagamento(CriacaoCupomDTO cupom, int id);

        void CriarCupomPessoaFisica(int idEstabelecimento, int idCupom, int idPessoa);

        CupomInfosDTO ObterCupomElegivel(int idPessoa, List<int> idsHorarisos);
        IEnumerable<ServicosEstabelecimentoDTO> ObterServicosEstabelecimento(int IdEstabelecimento);
        IEnumerable<ProdutosEstabelecimentoDTO> ObterProdutosEstabelecimento(int IdEstabelecimento);
        bool ExisteCupomAssociadoATransacao(int idTransacao);
        CupomInfosDTO ObterCupomPorCodigoHotsite(ObterCupomPorCodigoFilter filtro);
        List<QuantidadeDeUsoPorClienteDTO> ObterQtdDeResgatesDeClientesPorCupom(string conteudoFiltro, int idCupom);
    }
}