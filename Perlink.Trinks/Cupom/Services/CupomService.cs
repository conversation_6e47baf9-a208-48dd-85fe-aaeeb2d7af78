using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Cupom.DTO;
using Perlink.Trinks.Cupom.Enums;
using Perlink.Trinks.Cupom.Filters;
using Perlink.Trinks.Cupom.Models;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using static Perlink.Trinks.PropertyNames.Cupom;
using DL = Perlink.Trinks.Domain.Pessoas;

namespace Perlink.Trinks.Cupom.Services
{

    public class CupomService : BaseService, ICupomService
    {

        public bool CriarCupom(CriacaoCupomDTO dadosCupom)
        {
            var cupom = CriarCupomResponsavel(dadosCupom);
            return cupom;
        }

        public bool CriarPrimeiroCupom(CriacaoCupomDTO dadosCupom)
        {
            var cupom = CriarCupomResponsavel(dadosCupom);
            CriarMotivoDeDescontoCupom();
            return cupom;
        }

        public ControleDeCupomDTO ListarCuponsEstabelecimento(ControleDeCupomFilter filter)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var cuponsFiltrados = Domain.Cupom.CupomEstabelecimentoRepository
                .ObterListaCupons(idEstabelecimento, filter);

            filter.Paginacao.TotalItens = Domain.Cupom.CupomEstabelecimentoRepository
                .ObterContagemCupons(idEstabelecimento, filter);

            return new ControleDeCupomDTO() { Registros = cuponsFiltrados, Paginacao = filter.Paginacao };
        }

        public bool CriarCupomResponsavel(CriacaoCupomDTO dadosCupom)
        {
            var idEstabelecimento = (int)ContextHelper.Instance.IdEstabelecimento;

            if (!CupomEhValido(dadosCupom))
                return false;

            var CupomEstabelecimentoDTO = new CupomResponsavelDTO(dadosCupom);

            var cupomEstabelecimento = new CupomEstabelecimento(CupomEstabelecimentoDTO, idEstabelecimento);
            cupomEstabelecimento.ContextoUso = AssociarContextoUso(dadosCupom.ContextoUso);
            Domain.Cupom.CupomEstabelecimentoRepository.SaveNew(cupomEstabelecimento);

            CriarCupomPagamento(dadosCupom, cupomEstabelecimento.Id);
            LigarServicosAoCupom(dadosCupom, cupomEstabelecimento.Id);
            LigarProdutosAoCupom(dadosCupom, cupomEstabelecimento.Id);

            return true;
        }

        private bool CupomEhValido(CriacaoCupomDTO dadosCupom)
        {
            var idEstabelecimento = (int)ContextHelper.Instance.IdEstabelecimento;
            Regex r = new Regex("^[a-zA-Z0-9]*$");

            if (CupomMesmoNome(idEstabelecimento, dadosCupom.CupomBase.Codigo) || !r.IsMatch(dadosCupom.CupomBase.Codigo))
                return false;

            if (dadosCupom.CupomBase.QtdUsoCliente % 1 != 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A quantidade de uso do cupom deve ser um número inteiro.");
                return false;
            }

            if (dadosCupom.CupomBase.QtdUsoCliente < 1 || dadosCupom.CupomBase.QtdUsoCliente > 999999999)
            {
                var msgValidacao = dadosCupom.CupomBase.QtdUsoCliente < 1
                    ? "O campo de quantidade de uso por cliente deve ser pelo menos 1."
                    : "O valor máximo permitido é 999999999.";

                ValidationHelper.Instance.AdicionarItemValidacao(msgValidacao);
                return false;
            }

            if (dadosCupom.CupomDesconto.Desconto <= 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Valor de desconto inválido. O desconto deve ser um valor inteiro entre 0% e 100%.");
                return false;
            }

            return true;
        }

        private ContextoUso AssociarContextoUso(ContextoUsoDTO contextoUsoDto)
        {
            ContextoUso contextoUso = ContextoUso.Nenhum;
            if (contextoUsoDto.Balcao)
                contextoUso |= ContextoUso.Balcao;
            if (contextoUsoDto.AgendamentoOnline != null && (bool)contextoUsoDto.AgendamentoOnline)
                contextoUso |= ContextoUso.AgendamentoOnline;
            return contextoUso;
        }

        public CuponsInfosDTO ListaCuponsInfos(int idPessoa)
         {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var cupomPessoaFisicaRepo = Domain.Cupom.CupomPessoaFisicaRepository.Queryable();
            var cuponsBaseRepo = Domain.Cupom.CupomBaseRepository.Queryable();
            var cupomEstabelecimentoRepo = Domain.Cupom.CupomEstabelecimentoRepository.Queryable();

            var cuponsClienteEstabelecimento = (from cpfr in cupomPessoaFisicaRepo
                                                join cbr in cuponsBaseRepo
                                                on cpfr.IdCupom equals cbr.Id
                                                join cer in cupomEstabelecimentoRepo
                                                on cpfr.IdCupom equals cer.Id
                                                where cpfr.IdPessoa == idPessoa
                                                where cpfr.IdTransacao == null
                                                where DateTime.Now >= cbr.Validade.Inicio
                                                where DateTime.Now <= cbr.Validade.Fim
                                                where cbr.Ativo == true
                                                where cer.IdEstabelecimento == idEstabelecimento
                                                select cpfr).ToList();

            if (cuponsClienteEstabelecimento.Any())
                return new CuponsInfosDTO(cuponsClienteEstabelecimento.Select(cc => new CupomInfosDTO(cc)).ToList());
            return null;
        }

        public CupomInfosDTO ObterCupomPorId(int idCupom)
        {
            return new CupomInfosDTO(Domain.Cupom.CupomBaseRepository.Load(idCupom), null);
        }

        public int? ObterCuponsDisponiveisContagem(int idPessoa)
        {
            var cuponsInfos = ListaCuponsInfos(idPessoa);

            if (cuponsInfos == null)
                return 0;
            return cuponsInfos.Cupons?.Count;
        }

        public int? ObterCuponsResgatadosContagem(int idCupom)
        {
            int idEstbelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var cupomUsoPessoaFisicaRepo = Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable();
            var cuponsBaseRepo = Domain.Cupom.CupomBaseRepository.Queryable();
            return (from cpfr in cupomUsoPessoaFisicaRepo
                    join cbr in cuponsBaseRepo on cpfr.IdCupom equals cbr.Id
                    where cbr.Id == idCupom
                    select cpfr.Quantidade).Sum();
        }

        public IList<string> ObterNomesServicosDoCupom(IList<int?> idsServicos, bool todosServicos = false)
        {
            if (todosServicos)
            {
                return new List<string> { "Válido para todos os serviços" };
            }

            return Domain.Pessoas.ServicoEstabelecimentoRepository.Queryable()
                .Where(se => idsServicos.Contains(se.IdServicoEstabelecimento))
                .Select(cse => cse.Nome)
                .ToList();
        }

        public IList<string> ObterNomesProdutosDoCupom(IList<int?> idsProdutos, bool produtosTodos = false)
        {
            if (produtosTodos)
            {
                return new List<string> { "Válido para todos os produtos" };
            }

            return Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                .Where(epr => idsProdutos.Contains(epr.Id))
                .Select(epr => epr.Descricao)
                .ToList();
        }

        public List<ClienteEstabelecimentoDTO> ObterClientesSelecionados(int idCupom)
        {

            return Domain.Cupom.CupomPessoaFisicaRepository.ObterClientesSelecionados(idCupom);
        }

        public List<ClienteEstabelecimentoDTO> ListarClientesEstabelecimentoParaAssociar(string conteudoFiltro, int idCupom)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;
            var limiteUso = Domain.Cupom.CupomBaseRepository.ObterQuantidadeMaximaDeUsosPorCliente(idCupom);

            var comboClientes = Domain.Pessoas.ItemComboClienteRepository.Queryable();
            var cupomUsoPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable();

            var usosFiltrados = cupomUsoPessoaFisica
                .Where(u => u.IdCupom == idCupom)
                .Select(u => new
                {
                    u.IdPessoa,
                    u.Quantidade,
                    u.AssociadoAutomaticamente
                })
                .ToList();


            var clientesQuery = comboClientes
                .Where(cliente => cliente.IdEstabelecimento == idEstabelecimento &&
                                  cliente.Nome.StartsWith(conteudoFiltro))
                .ToList(); 

            var clientesEstabelecimento = clientesQuery
                .GroupJoin(
                    usosFiltrados,
                    cliente => cliente.IdPessoa,
                    uso => uso.IdPessoa,
                    (cliente, usosDoCliente) => new ClienteEstabelecimentoDTO
                    {
                        IdPessoa = cliente.IdPessoa,
                        Nome = cliente.Nome,
                        Telefone = cliente.Telefones,
                        Email = cliente.Email,
                        Associado = usosDoCliente.Any(u => u.AssociadoAutomaticamente),
                        AtingiuLimiteDeResgate = usosDoCliente.Select(u => u.Quantidade).FirstOrDefault() >= limiteUso,
                        QuantidadeResgates = usosDoCliente.Select(u => u.Quantidade).FirstOrDefault()
                    }
                )
                .ToList();

            return clientesEstabelecimento;
        }

        public IList<string> ObterNomesClientesDoCupom(int idCupom)
        {
            int idEstbelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var cupomPessoaFisicaRepo = Domain.Cupom.CupomPessoaFisicaRepository.Queryable();
            var clienteEstabelecimentosRepo = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();

            var idPessoas = cupomPessoaFisicaRepo
                        .Where(x => x.IdCupom == idCupom)
                        .Select(x => x.IdPessoa)
                        .Distinct();

            var result = clienteEstabelecimentosRepo
                         .Where(x => idPessoas.Contains(x.Cliente.PessoaFisica.IdPessoa)
                         && x.Estabelecimento.IdEstabelecimento == idEstbelecimento)
                         .Select(x => x.Cliente.PessoaFisica.NomeCompleto)
                         .ToList();

            return result;
        }

        public bool EstabelecimentoTemCupom()
        {
            int idEstbelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            return Domain.Cupom.CupomEstabelecimentoRepository.Queryable()
                .Where(cer => cer.IdEstabelecimento == idEstbelecimento)
                .Any();
        }

        public bool InativarCupom(int idCupom)
        {
            var cupom = Domain.Cupom.CupomBaseRepository.Load(idCupom);
            if (cupom == null) return false;

            if (cupom.DataInativacao < DateTime.Now) return false;

            cupom.DataInativacao = DateTime.Now;
            Domain.Cupom.CupomBaseRepository.Update(cupom);

            return true;
        }

        public void CriarCupomPagamento(CriacaoCupomDTO cupom, int id)
        {
            var cupomDesconto = new CupomDesconto(cupom, id);
            Domain.Cupom.CupomDescontoRepository.SaveNew(cupomDesconto);
        }

        public void CriarCupomPessoaFisica(int idEstabelecimento, int idCupom, int idPessoa)
        {
            var cupomPessoaFisica = new CupomPessoaFisica(idEstabelecimento, idCupom, idPessoa);
            Domain.Cupom.CupomPessoaFisicaRepository.SaveNew(cupomPessoaFisica);
        }

        public CuponsInfosDTO ObterCupomPorCodigo(string codigoCupom, int? idPessoa)
        {
            var cuponsEstabelecimentoRepo = Domain.Cupom.CupomEstabelecimentoRepository.Queryable();
            var cuponsBaseRepo = Domain.Cupom.CupomBaseRepository.Queryable();
            var cuponsBase = from ce in cuponsEstabelecimentoRepo
                             join cb in cuponsBaseRepo on ce.Id equals cb.Id
                             where ce.IdEstabelecimento == ContextHelper.Instance.IdEstabelecimento.Value
                             && cb.Codigo == codigoCupom
                             && cb.Ativo                     
                             select cb;
            var cupomBase = cuponsBase.FirstOrDefault();
            if (cupomBase == null) return null;
            var cupom = new CupomInfosDTO(cupomBase, idPessoa);
            cupom.ValidarCupom();
            return new CuponsInfosDTO(new List<CupomInfosDTO> { cupom });
        }

        public CupomInfosDTO ObterCupomElegivel(int idPessoa, List<int> idsHorarisos)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var cupomPessoaFisicaRepo = Domain.Cupom.CupomPessoaFisicaRepository.Queryable();

            // Eu espero que só tenha um resultado nessa lista
            if (idsHorarisos != null && idsHorarisos.Count > 0)
            {
                var cupomAssociadoHorario = cupomPessoaFisicaRepo
                    .FirstOrDefault(cpf => cpf.IdHorario.HasValue && cpf.IdHorario.Value != 0 && idsHorarisos.Contains(cpf.IdHorario.Value));
                if (cupomAssociadoHorario != null)
                    return new CupomInfosDTO(cupomAssociadoHorario);
            }

            var cupomPessoaFisicas = Domain.Cupom.CupomPessoaFisicaRepository.Queryable();
            var cuponsBase = Domain.Cupom.CupomBaseRepository.Queryable();
            var cupomUsoPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable();

            var cuponsAtivos = from cpf in cupomPessoaFisicas
                               join c in cuponsBase on cpf.IdCupom equals c.Id
                               join cup in cupomUsoPessoaFisica
                                    on new { cpf.IdCupom, cpf.IdPessoa } equals new { cup.IdCupom, cup.IdPessoa }
                               where c.Ativo
                                     && cpf.IdHorario == null
                                     && (cup.Quantidade < c.QtdUsoCliente)
                                     && cup.AssociadoAutomaticamente
                               orderby c.Validade.Fim
                               select cpf;

            var cupomPessoaFisica = cuponsAtivos
                .FirstOrDefault(cc => cc.IdPessoa == idPessoa && cc.IdEstabelecimento == idEstabelecimento);

            if (cupomPessoaFisica == null)
                return null;

            return new CupomInfosDTO(cupomPessoaFisica);
        }

        public IEnumerable<ServicosEstabelecimentoDTO> ObterServicosEstabelecimento(int IdEstabelecimento)
        {
            var servicos =
                DL.ServicoEstabelecimentoRepository.ListarAtivosPorEstabelecimento(IdEstabelecimento);

            return servicos.Select(cc => new ServicosEstabelecimentoDTO(cc));
        }

        public IEnumerable<ProdutosEstabelecimentoDTO> ObterProdutosEstabelecimento(int IdEstabelecimento)
        {
            var produtos =
                DL.EstabelecimentoProdutoRepository.ListarAtivosPorEstabelecimento(IdEstabelecimento);

            return produtos.Select(cc => new ProdutosEstabelecimentoDTO(cc)); ;
        }

        private void CriarMotivoDeDescontoCupom()
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId((int)ContextHelper.Instance.IdEstabelecimento);

            List<MotivoDeDescontoDoTrinksEnum> motivosDoTrinksAtivados;
            Domain.Pessoas.EstabelecimentoService.CriarOuDesativarMotivosDeDescontoDoTrinksSeNecessario(estabelecimento, out motivosDoTrinksAtivados, MotivoDeDescontoDoTrinksEnum.CupomComDesconto);
            Domain.Pessoas.EstabelecimentoService.CriarOuDesativarMotivosDeDescontoDoTrinksSeNecessario(estabelecimento, out motivosDoTrinksAtivados, MotivoDeDescontoDoTrinksEnum.CupomSemDesconto);

        }

        private bool CupomMesmoNome(int idEstabelecimento, string codigo)
        {
            var cupom = Domain.Cupom.CupomEstabelecimentoRepository.Queryable().Where(a => a.IdEstabelecimento == idEstabelecimento && a.Codigo == codigo).FirstOrDefault();
            return cupom != null;
        }
        public void LigarServicosAoCupom(CriacaoCupomDTO cupom, int id)
        {
            if (cupom.Servicos is Array && cupom.Servicos.First() != -1)
            {
                foreach (var servico in cupom.Servicos)
                {
                    var cupomServicoEstabelecimento = new CupomServicoEstabelecimento(id, servico);
                    Domain.Cupom.CupomServicoEstabelecimentoRepository.SaveNew(cupomServicoEstabelecimento);
                }
            }
            else if (cupom.Servicos == null)
            {
                var cupomServicoEstabelecimento = new CupomServicoEstabelecimento(id, null);
                Domain.Cupom.CupomServicoEstabelecimentoRepository.SaveNew(cupomServicoEstabelecimento);
            }
        }

        public void LigarProdutosAoCupom(CriacaoCupomDTO cupom, int id)
        {
            if (cupom.Produtos is Array && cupom.Produtos.First() != -1)
            {
                foreach (var produto in cupom.Produtos)
                {
                    var cupomEstabelecimentoProduto = new CupomEstabelecimentoProduto(id, produto);
                    Domain.Cupom.CupomEstabelecimentoProdutoRepository.SaveNew(cupomEstabelecimentoProduto);
                }
            }
            else if (cupom.Produtos == null)
            {
                var cupomEstabelecimentoProduto = new CupomEstabelecimentoProduto(id, null);
                Domain.Cupom.CupomEstabelecimentoProdutoRepository.SaveNew(cupomEstabelecimentoProduto);
            }
        }

        public void AssociarClientesAoCupom(AssociacaoClienteInfosModel model)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var QtdUsoCliente = Domain.Cupom.CupomBaseRepository.ObterQuantidadeMaximaDeUsosPorCliente(model.IdCupom);

            var clienteAsQueryable = model.Clientes.Select(c => c.IdPessoa).ToList();

            var cuponsUsoPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository
                .Queryable()
                .Where(cupomUso => cupomUso.IdCupom == model.IdCupom && clienteAsQueryable.Contains(cupomUso.IdPessoa))
                .ToList();

            foreach (var cliente in model.Clientes)
            {
                var cupomUsoPessoaFisica = cuponsUsoPessoaFisica.FirstOrDefault(x => x.IdPessoa == cliente.IdPessoa);

                if (!cliente.Associado)
                {
                    Domain.Cupom.CupomUsoPessoaFisicaRepository.AtualizarStatusAssociacaoAutomatica(cupomUsoPessoaFisica, false);
                    continue;
                }
                
                if(cupomUsoPessoaFisica == null)
                {
                    Domain.Cupom.CupomPessoaFisicaRepository.SaveNewNoFlush(new CupomPessoaFisica(idEstabelecimento, model.IdCupom, cliente.IdPessoa));
                    var cupomUsoDTO = new CupomUsoPessoaFisicaDTO(model.IdCupom, cliente.IdPessoa, 0, true);
                    Domain.Cupom.CupomUsoPessoaFisicaRepository.CriarAssociacaoAutomatica(cupomUsoDTO);
                    continue;
                }

                Domain.Cupom.CupomUsoPessoaFisicaRepository.AtualizarStatusAssociacaoAutomatica(cupomUsoPessoaFisica, true); 
            }

            Domain.Cupom.CupomUsoPessoaFisicaRepository.Flush();
        }

        public bool ExisteCupomAssociadoATransacao(int idTransacao)
        {
            var cupomPessoaFisica = Domain.Cupom.CupomPessoaFisicaRepository.Queryable();

            var resultado = cupomPessoaFisica.Any(x => x.IdTransacao == idTransacao);

            return resultado;
        }

        public CupomInfosDTO ObterCupomPorCodigoHotsite(ObterCupomPorCodigoFilter filtro)
        {
            var idConta = ContextHelper.Instance.IdConta.Value;
            filtro.IdPessoa = Domain.Pessoas.ContaService.ObterConta(idConta).Pessoa.IdPessoa;

            filtro.IdsHorario = Domain.Pessoas.HorarioRepository
                .ObterIdsDosHorariosDisponiveisDoClienteNoDia(filtro.IdPessoa.Value, filtro.IdEstabelecimento, filtro.Data.Date);

            if (Domain.Cupom.CupomPessoaFisicaRepository.ExisteHorarioAssociadoNoDia(filtro.IdsHorario, filtro.IdPessoa.Value))
                return null;

            var cupomBase = Domain.Cupom.CupomBaseRepository
                .ObterDisponivelParaServicoPorCodigo(filtro.CodigoCupom, filtro.IdEstabelecimento, filtro.IdServico);

            if (cupomBase == null) return null;

            var cupom = new CupomInfosDTO(cupomBase, filtro.IdPessoa);
            cupom.ValidarCupom();

            return cupom;
        }

        public List<QuantidadeDeUsoPorClienteDTO> ObterQtdDeResgatesDeClientesPorCupom(string conteudoFiltro, int idCupom)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;
            return Domain.Cupom.CupomPessoaFisicaRepository.ObterQtdDeResgatesDeClientesPorCupom(idCupom, idEstabelecimento, conteudoFiltro);
        }
    }
}