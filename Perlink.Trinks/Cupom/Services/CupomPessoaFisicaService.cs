﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.Cupom.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using System;
using System.Linq;

namespace Perlink.Trinks.Cupom.Services
{
    public class CupomPessoaFisicaService : BaseService, ICupomPessoaFisicaService
    {
        [TransactionInitRequired]
        public CupomPessoaFisica AssociarCupomAPessoaFisica(Horario horario, string codigoCupom)
        {
            var cupomValido = Domain.Cupom.CupomBaseRepository
                .ObterDisponivelParaServicoPorCodigo(codigoCupom, horario.Estabelecimento.IdEstabelecimento,
                    horario.ServicoEstabelecimento.IdServicoEstabelecimento);

            if (cupomValido == null)
            {
                throw new NullReferenceException("Cupom inválido");
            }

            var cupomPessoaFisica = new CupomPessoaFisica
            {
                IdPessoa = horario.Cliente.PessoaFisica.IdPessoaFisica,
                IdCupom = cupomValido.Id,
                IdEstabelecimento = horario.Estabelecimento.IdEstabelecimento,
                IdHorario = horario.Id,
            };
            Domain.Cupom.CupomPessoaFisicaRepository.SaveNew(cupomPessoaFisica);

            return cupomPessoaFisica;
        }

        [TransactionInitRequired]
        public void AssociarCupomATransacao(int idCupom, int idTransacao, int? idHorario = null)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            var cupomPessoaFisica = Domain.Cupom.CupomPessoaFisicaRepository
                .Queryable()
                .FirstOrDefault(c => c.IdCupom == idCupom
                                && c.IdPessoa == transacao.PessoaQuePagou.IdPessoa
                                && c.IdTransacao == null
                                && c.IdHorario == idHorario);

            var cupomUsoPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository.ObterCupomUsoPorIdCupomEIdPessoa(idCupom, transacao.PessoaQuePagou.IdPessoa);

            if (cupomPessoaFisica == null)
            {
                cupomPessoaFisica = new CupomPessoaFisica
                {
                    IdPessoa = transacao.PessoaQuePagou.IdPessoa,
                    IdCupom = idCupom,
                    IdEstabelecimento = Domain.WebContext.IdEstabelecimentoAutenticado ?? 0
                };
                Domain.Cupom.CupomPessoaFisicaRepository.SaveNew(cupomPessoaFisica);
                
            }
            if (cupomUsoPessoaFisica == null)
            {
                var cupomDTO = new CupomUsoPessoaFisicaDTO(idCupom, transacao.PessoaQuePagou.IdPessoa, 1, false);
                Domain.Cupom.CupomUsoPessoaFisicaRepository.CriarAssociacaoAutomatica(cupomDTO);
            }
            else
            {
                Domain.Cupom.CupomUsoPessoaFisicaRepository.IncrementarQuantidadeDeUsos(cupomUsoPessoaFisica);
            }

            cupomPessoaFisica.IdTransacao = idTransacao;
            
            var htsComCupom = transacao.HorariosTransacoes
                .Where(ht =>
                    ht.MotivoDesconto != null &&
                    (ht.MotivoDesconto.MotivoDeDescontoDoTrinks == MotivoDeDescontoDoTrinksEnum.CupomComDesconto ||
                     ht.MotivoDesconto.MotivoDeDescontoDoTrinks == MotivoDeDescontoDoTrinksEnum.CupomComDesconto));

            foreach (var ht in htsComCupom)
            {
                var cupomHorarioTransacao = new CupomHorarioTransacao
                {
                    IdCupom = idCupom,
                    IdHorarioTransacao = ht.Codigo
                };
                Domain.Cupom.CupomHorarioTransacaoRepository.SaveNew(cupomHorarioTransacao);
            }

            var ivsComCupom = transacao.Vendas.FirstOrDefault()?.ItensVenda
                .Where(iv =>
                    iv.MotivoDesconto != null &&
                    (iv.MotivoDesconto.MotivoDeDescontoDoTrinks == MotivoDeDescontoDoTrinksEnum.CupomComDesconto ||
                     iv.MotivoDesconto.MotivoDeDescontoDoTrinks == MotivoDeDescontoDoTrinksEnum.CupomSemDesconto));

            if (ivsComCupom == null)
                foreach (var iv in ivsComCupom)
                {
                    var cupomItemVenda = new CupomItemVenda
                    {
                        IdCupom = idCupom,
                        IdItemVenda = iv.Id
                    };
                    Domain.Cupom.CupomItemVendaRepository.SaveNew(cupomItemVenda);
                }
        }

        public void DesvincularCupomDoAgendamento(int idCupom, int idHorario)
        {
            Domain.Cupom.CupomPessoaFisicaRepository.DesvincularCupomDoAgendamento(idCupom, idHorario);
        }
    }
}