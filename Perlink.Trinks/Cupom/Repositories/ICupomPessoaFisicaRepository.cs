using Perlink.Trinks.Cupom.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.Cupom.Repositories
{
    public partial interface ICupomPessoaFisicaRepository
    {

        void DesvincularCupomDoAgendamento(int idCupom, int idHorario);

        bool ExisteHorarioAssociadoNoDia(List<int> idsHorarios, int idPessoa);

        List<QuantidadeDeUsoPorClienteDTO> ObterQtdDeResgatesDeClientesPorCupom(int idCupom, int idEstabelecimento, string conteudoFiltro);
        List<ClienteEstabelecimentoDTO> ObterClientesSelecionados(int idCupom);

        IList<CupomPessoaFisica> ObterCuponsPessoaFisicaPorIdCupomEIdPessoa(int idCupom, int idPessoa);

        CupomPessoaFisica ObterCupomPessoaFisicaAindaNaoTransacionadoPorIdCupomEIdPessoa(int idCupom, int idPessoa);
    }
}