﻿using Castle.ActiveRecord;
using NHibernate;
using NHibernate.Transform;
using Perlink.Trinks.Cupom.DTO;
using Perlink.Trinks.Cupom.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Perlink.Trinks.Cupom.Repositories
{

    public partial class CupomEstabelecimentoRepository : ICupomEstabelecimentoRepository
    {

        public CupomEstabelecimento ObterPorCodigoEstabelecimento(string cupom, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(c => c.Codigo == cupom && c.IdEstabelecimento == idEstabelecimento);
        }

        public List<ControleDeCupomItemDTO> ObterListaCupons(int idEstabelecimento, ControleDeCupomFilter filter)
        {
            var query = new StringBuilder();
            query.AppendLine("SELECT *");
            query.AppendLine("FROM Cupom.vw_Cupons");
            query.AppendLine("WHERE IdEstabelecimento = :idEstabelecimento");

            if (filter.Status.HasValue)
                query.AppendLine("AND Ativo = :status");

            if (!string.IsNullOrWhiteSpace(filter.Codigo))
                query.AppendLine("AND Codigo LIKE '%' + :codigo + '%'");

            if (!string.IsNullOrWhiteSpace(filter.Nome))
                query.AppendLine("AND Nome LIKE '%' + :nome + '%'");

            if (filter.ValidadeInicial.HasValue)
                query.AppendLine("AND ValidadeInicio >= :validadeInicial");

            if (filter.ValidadeFinal.HasValue)
                query.AppendLine("AND ValidadeFim <= :validadeFinal");

            query.AppendLine("ORDER BY Id DESC");

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session = holder.CreateSession(typeof(ActiveRecordBase));

            try
            {
                var sql = query.ToString();
                var dataQuery = session.CreateSQLQuery(sql)
                    .SetInt32("idEstabelecimento", idEstabelecimento)
                    .SetResultTransformer(Transformers.AliasToBean(typeof(ControleDeCupomItemDTO)));

                if (filter.Status.HasValue)
                    dataQuery.SetParameter("status", filter.Status.Value, NHibernateUtil.Boolean);

                if (!string.IsNullOrWhiteSpace(filter.Codigo))
                    dataQuery.SetParameter("codigo", filter.Codigo, NHibernateUtil.String);

                if (!string.IsNullOrWhiteSpace(filter.Nome))
                    dataQuery.SetParameter("nome", filter.Nome, NHibernateUtil.String);

                if (filter.ValidadeInicial.HasValue)
                    dataQuery.SetParameter("validadeInicial", filter.ValidadeInicial.Value, NHibernateUtil.DateTime2);

                if (filter.ValidadeFinal.HasValue)
                    dataQuery.SetParameter("validadeFinal", filter.ValidadeFinal.Value, NHibernateUtil.DateTime2);

                dataQuery.SetFirstResult(filter.Paginacao.RegistroInicial - 1);
                dataQuery.SetMaxResults(filter.Paginacao.RegistrosPorPagina);

                return dataQuery.List<ControleDeCupomItemDTO>().ToList();
            }
            finally
            {
                holder.ReleaseSession(session);
            }
        }


        public int ObterContagemCupons(int idEstabelecimento, ControleDeCupomFilter filter)
        {
            var query = new StringBuilder();
            query.AppendLine("SELECT COUNT(1)");
            query.AppendLine("FROM Cupom.vw_Cupons");
            query.AppendLine("WHERE IdEstabelecimento = :idEstabelecimento");

            if (filter.Status.HasValue)
                query.AppendLine("AND Ativo = :status");

            if (!string.IsNullOrWhiteSpace(filter.Codigo))
                query.AppendLine("AND Codigo LIKE '%' + :codigo + '%'");

            if (!string.IsNullOrWhiteSpace(filter.Nome))
                query.AppendLine("AND Nome LIKE '%' + :nome + '%'");

            if (filter.ValidadeInicial.HasValue)
                query.AppendLine("AND ValidadeInicio >= :validadeInicial");

            if (filter.ValidadeFinal.HasValue)
                query.AppendLine("AND ValidadeFim <= :validadeFinal");

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session = holder.CreateSession(typeof(ActiveRecordBase));

            try
            {
                var sql = query.ToString();
                var countQuery = session.CreateSQLQuery(sql)
                    .SetInt32("idEstabelecimento", idEstabelecimento);

                if (filter.Status.HasValue)
                    countQuery.SetParameter("status", filter.Status.Value, NHibernateUtil.Boolean);

                if (!string.IsNullOrWhiteSpace(filter.Codigo))
                    countQuery.SetParameter("codigo", filter.Codigo, NHibernateUtil.String);

                if (!string.IsNullOrWhiteSpace(filter.Nome))
                    countQuery.SetParameter("nome", filter.Nome, NHibernateUtil.String);

                if (filter.ValidadeInicial.HasValue)
                    countQuery.SetParameter("validadeInicial", filter.ValidadeInicial.Value, NHibernateUtil.DateTime2);

                if (filter.ValidadeFinal.HasValue)
                    countQuery.SetParameter("validadeFinal", filter.ValidadeFinal.Value, NHibernateUtil.DateTime2);

                var total = countQuery.UniqueResult();
                return total == null ? 0 : Convert.ToInt32(total);
            }
            finally
            {
                holder.ReleaseSession(session);
            }
        }
    }
}