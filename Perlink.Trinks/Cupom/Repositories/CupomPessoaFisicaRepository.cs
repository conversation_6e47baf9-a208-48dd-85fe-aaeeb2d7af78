using Perlink.Trinks.Cupom.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Wrapper;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cupom.Repositories
{
    public partial class CupomPessoaFisicaRepository : ICupomPessoaFisicaRepository
    {

        public void DesvincularCupomDoAgendamento(int idCupom, int idHorario)
        {
            var cupomPessoaFisica = Queryable()
                .FirstOrDefault(cpf => cpf.IdCupom == idCupom && cpf.IdHorario == idHorario);
            if (cupomPessoaFisica != null)
            {
                Delete(cupomPessoaFisica);
            }
        }

        public bool ExisteHorarioAssociadoNoDia(List<int> idsHorarios, int idPessoa)
        {
            return Queryable()
                .Any(cpf => cpf.IdHorario.HasValue && idsHorarios.Contains(cpf.IdHorario.Value) && cpf.IdPessoa == idPessoa); ;
        }

        public List<QuantidadeDeUsoPorClienteDTO> ObterQtdDeResgatesDeClientesPorCupom(int idCupom,
            int idEstabelecimento, string conteudoFiltro)
        {
            var cupomUsoPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable();
            var comboClientes = Domain.Pessoas.ItemComboClienteRepository.Queryable();

            var resultado = (from uso in cupomUsoPessoaFisica
                             join clientes in comboClientes on uso.IdPessoa equals clientes.IdPessoa
                             where uso.IdCupom == idCupom
                             && clientes.IdEstabelecimento == idEstabelecimento
                             && clientes.Nome.StartsWith(conteudoFiltro)
                             select new QuantidadeDeUsoPorClienteDTO
                             {
                                 Nome = clientes.Nome,
                                 QuantidadeResgates = uso.Quantidade
                             }).ToList();

            return resultado;
        }

        public List<ClienteEstabelecimentoDTO> ObterClientesSelecionados(int idCupom)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var cupomUsoPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable();
            var cupom = Domain.Cupom.CupomBaseRepository.Queryable();
            var comboClientes = Domain.Pessoas.ItemComboClienteRepository.Queryable();
            var query = from uso in cupomUsoPessoaFisica
                        join clientes in comboClientes on uso.IdPessoa equals clientes.IdPessoa
                        join cb in cupom on uso.IdCupom equals cb.Id
                        where uso.IdCupom == idCupom && clientes.IdEstabelecimento == idEstabelecimento
                        && uso.AssociadoAutomaticamente
                        select new ClienteEstabelecimentoDTO
                        {
                            IdPessoa = clientes.IdPessoa,
                            Nome = clientes.Nome,
                            Telefone = clientes.Telefones,
                            Email = clientes.Email,
                            Associado = true,
                            AtingiuLimiteDeResgate = uso.Quantidade >= cb.QtdUsoCliente,
                            QuantidadeResgates = uso.Quantidade
                        };

            return query
                .ToList();
        }

        public IList<CupomPessoaFisica> ObterCuponsPessoaFisicaPorIdCupomEIdPessoa(int idCupom, int idPessoa)
        {
            return Domain.Cupom.CupomPessoaFisicaRepository.Queryable()
                    .Where(cc => cc.IdCupom == idCupom
                            && cc.IdPessoa == idPessoa)
                    .ToList();
        }

        public CupomPessoaFisica ObterCupomPessoaFisicaAindaNaoTransacionadoPorIdCupomEIdPessoa(int idCupom, int idPessoa)
        {
            return Domain.Cupom.CupomPessoaFisicaRepository.Queryable()
                    .Where(x => x.IdCupom == idCupom && x.IdPessoa == idPessoa && x.IdTransacao == null)
                    .FirstOrDefault();
        }
    }
}