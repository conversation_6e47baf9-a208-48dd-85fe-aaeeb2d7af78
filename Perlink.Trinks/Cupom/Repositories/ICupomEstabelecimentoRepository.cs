﻿using Perlink.Trinks.Cupom.DTO;
using Perlink.Trinks.Cupom.Filters;
using System.Collections.Generic;

namespace Perlink.Trinks.Cupom.Repositories
{

    public partial interface ICupomEstabelecimentoRepository
    {

        CupomEstabelecimento ObterPorCodigoEstabelecimento(string cupom, int idEstabelecimento);

        List<ControleDeCupomItemDTO> ObterListaCupons(int idEstabelecimento, ControleDeCupomFilter filter);
        int ObterContagemCupons(int idEstabelecimento, ControleDeCupomFilter filter);

    }
}