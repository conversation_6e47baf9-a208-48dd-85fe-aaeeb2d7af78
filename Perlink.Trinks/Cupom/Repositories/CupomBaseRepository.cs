using System.Linq;

namespace Perlink.Trinks.Cupom.Repositories
{

    public partial class CupomBaseRepository : ICupomBaseRepository
    {

        public CupomBase ObterDisponivelParaServicoPorCodigo(string codigoCupom, int idEstabelecimento, int idServicoEstabelecimento)
        {
            var cupomBaseRepo = StatelessQueryable()
                .Where(x => x.Codigo == codigoCupom && x.Ativo);

            var cupomServicoEstabelecimentoRepo = Domain.Cupom.CupomServicoEstabelecimentoRepository.StatelessQueryable();
            var cupomEstabelecimentoRepo = Domain.Cupom.CupomEstabelecimentoRepository.StatelessQueryable();

            var cuponsValidos = (from cb in cupomBaseRepo
                                 join cse in cupomServicoEstabelecimentoRepo on cb.Id equals cse.IdCupomBase
                                 join ce in cupomEstabelecimentoRepo on cb.Id equals ce.Id
                                 where ce.IdEstabelecimento == idEstabelecimento
                                 where cb.Codigo == codigoCupom
                                 where cse.IdServicoEstabelecimento == null || cse.IdServicoEstabelecimento == idServicoEstabelecimento
                                 select cb);

            var cupomValido = cuponsValidos.FirstOrDefault();

            return cupomValido;
        }

        public int ObterQuantidadeMaximaDeUsosPorCliente(int idCupom)
        {
            return Domain.Cupom.CupomBaseRepository.Queryable()
                .Where(c => c.Id == idCupom)
                .Select(c => c.QtdUsoCliente)
                .FirstOrDefault();
        }
    }
}