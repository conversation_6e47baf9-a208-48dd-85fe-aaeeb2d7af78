﻿using Perlink.Trinks.Cupom.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cupom.Repositories
{
    public partial class CupomUsoPessoaFisicaRepository : ICupomUsoPessoaFisicaRepository
    {
        public void CriarAssociacaoAutomatica(CupomUsoPessoaFisicaDTO cupom)
        {
            var cupomUsoPessoaFisica = new CupomUsoPessoaFisica(cupom.IdCupom, cupom.IdPessoa, cupom.Quantidade, cupom.AssociadoAutomaticamente);
            Domain.Cupom.CupomUsoPessoaFisicaRepository.SaveNewNoFlush(cupomUsoPessoaFisica);
        }

        public void IncrementarQuantidadeDeUsos(CupomUsoPessoaFisica cupomUso)
        {
            cupomUso.Quantidade++;
            Domain.Cupom.CupomUsoPessoaFisicaRepository.UpdateNoFlush(cupomUso);
        }

        public void AtualizarStatusAssociacaoAutomatica(CupomUsoPessoaFisica cupomUso, bool associaAutomaticamente)
        {
            cupomUso.AssociadoAutomaticamente = associaAutomaticamente;
            Domain.Cupom.CupomUsoPessoaFisicaRepository.UpdateNoFlush(cupomUso);
        }

        public CupomUsoPessoaFisica ObterCupomUsoPorIdCupomEIdPessoa(int idCupom, int idPessoa)
        {
            return Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable()
                    .Where(x => x.IdCupom == idCupom && x.IdPessoa == idPessoa)
                    .FirstOrDefault();
        }
        public List<CupomUsoPessoaFisica> ObterCuponsUsoPorIdCupomEIdsPessoa(int idCupom, List<int> idsPessoa)
        {
            return Domain.Cupom.CupomUsoPessoaFisicaRepository.Queryable()
                    .Where(x => x.IdCupom == idCupom && idsPessoa.Contains(x.IdPessoa))
                    .ToList();
        }
    }
}
