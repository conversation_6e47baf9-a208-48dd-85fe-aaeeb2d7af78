﻿using Perlink.Trinks.Cupom.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.Cupom.Repositories
{
    public partial interface ICupomUsoPessoaFisicaRepository
    {
        void CriarAssociacaoAutomatica(CupomUsoPessoaFisicaDTO cupom);
        void IncrementarQuantidadeDeUsos(CupomUsoPessoaFisica cupomUso);
        void AtualizarStatusAssociacaoAutomatica(CupomUsoPessoaFisica cupomUso, bool associaAutomaticamente);
        CupomUsoPessoaFisica ObterCupomUsoPorIdCupomEIdPessoa(int idCupom, int idPessoa);
        List<CupomUsoPessoaFisica> ObterCuponsUsoPorIdCupomEIdsPessoa(int idCupom, List<int> idsPessoa);

    }

}