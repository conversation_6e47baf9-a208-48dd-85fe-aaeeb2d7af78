﻿using System;

namespace Perlink.Trinks.Cupom.DTO
{
    public class CupomResponsavelDTO
    {

        public virtual string Codigo { get; set; }

        public virtual Intervalo Validade { get; set; }

        public virtual Intervalo Resgate { get; set; }

        public virtual int? Desconto { get; set; }

        public virtual string Descricao { get; set; }

        public virtual string Nome { get; set; }

        public virtual DateTime? DataInativacao { get; set; }

        public virtual int QtdUsoCliente { get; set; }

        public virtual CupomEstabelecimento CupomEstabelecimento { get; set; }

        public CupomResponsavelDTO(CriacaoCupomDTO voucher)
        {
            Codigo = voucher.CupomBase.Codigo;
            Validade = voucher.CupomBase.Validade;
            Resgate = voucher.CupomBase.Resgate;
            Descricao = voucher.CupomBase.Descricao;
            Nome = voucher.CupomBase.Nome;
            DataInativacao = voucher.CupomBase.DataInativacao;
            QtdUsoCliente = voucher.CupomBase.QtdUsoCliente;
        }
    }
}