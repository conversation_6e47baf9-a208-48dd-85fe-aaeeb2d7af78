﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cupom.DTO
{

    public sealed class CupomInfosDTO
    {

        public readonly CupomBase CupomBase;

        public readonly CupomPessoaFisica CupomPessoaFisica;

        public readonly CupomDesconto CupomDesconto;

        public readonly IList<int?> Servicos;

        public readonly bool ServicosTodos;

        public readonly IList<int?> Produtos;

        public readonly bool ProdutosTodos;

        public Validacoes Validacoes { get; set; }
        public int? Resgates { get; }

        public ContextoUsoDTO ContextoUso { get; }
        public IList<CupomPessoaFisica> CuponsPessoaFisica { get; }
        public CupomUsoPessoaFisica UsoCupomPessoaFisica { get; }

        public CupomInfosDTO(CupomBase cupomBase, int? idPessoa)
        {
            CupomBase = cupomBase;
            if (idPessoa != null)
            {
                CuponsPessoaFisica = Domain.Cupom.CupomPessoaFisicaRepository.ObterCuponsPessoaFisicaPorIdCupomEIdPessoa(cupomBase.Id, (int)idPessoa);

                UsoCupomPessoaFisica = Domain.Cupom.CupomUsoPessoaFisicaRepository.ObterCupomUsoPorIdCupomEIdPessoa(cupomBase.Id, (int)idPessoa);
            }
                
            try
            {
                CupomDesconto = Domain.Cupom.CupomDescontoRepository.Load(cupomBase.Id);
            }
            catch
            {
                CupomDesconto = null;
            }
            Servicos = Domain.Cupom.CupomServicoEstabelecimentoRepository.Queryable()
                .Where(cse => cse.IdCupomBase == cupomBase.Id)
                .Select(cse => cse.IdServicoEstabelecimento)
                .ToList();

            ServicosTodos = Servicos.Count == 1 && Servicos.FirstOrDefault() == null ? true : false;
            Produtos = Domain.Cupom.CupomEstabelecimentoProdutoRepository.Queryable()
                .Where(cep => cep.IdCupomBase == cupomBase.Id)
                .Select(cep => cep.IdEstabelecimentoProduto)
                .ToList();
            ProdutosTodos = Produtos.Count == 1 && Produtos.FirstOrDefault() == null ? true : false;
            Validacoes = new Validacoes();
            ContextoUso =
                new ContextoUsoDTO(Domain.Cupom.CupomEstabelecimentoRepository.Load(cupomBase.Id).ContextoUso);
        }

        public CupomInfosDTO(CupomPessoaFisica cupomPessoaFisica)
        {
            CupomPessoaFisica = cupomPessoaFisica;
            CupomBase = Domain.Cupom.CupomBaseRepository.Load(cupomPessoaFisica.IdCupom);
            try
            {
                CupomDesconto = Domain.Cupom.CupomDescontoRepository.Load(cupomPessoaFisica.IdCupom);
            }
            catch
            {
                CupomDesconto = null;
            }
            Servicos = Domain.Cupom.CupomServicoEstabelecimentoRepository.Queryable()
                .Where(cse => cse.IdCupomBase == cupomPessoaFisica.IdCupom)
                .Select(cse => cse.IdServicoEstabelecimento)
                .ToList();
            ServicosTodos = Servicos.Count == 1 && Servicos.FirstOrDefault() == null ? true : false;
            Produtos = Domain.Cupom.CupomEstabelecimentoProdutoRepository.Queryable()
                .Where(cep => cep.IdCupomBase == cupomPessoaFisica.IdCupom)
                .Select(cep => cep.IdEstabelecimentoProduto)
                .ToList();
            ProdutosTodos = Produtos.Count == 1 && Produtos.FirstOrDefault() == null ? true : false;
            Validacoes = new Validacoes();
        }
        public void ValidarCupom()
        {
            Validacoes.UsadoPeloCliente = CuponsPessoaFisica?.FirstOrDefault(cpf => cpf.IdTransacao != null) != null;
            Validacoes.DentroValidade = CupomBase.Validade.Contem(DateTime.Now);
            Validacoes.AtingiuLimiteDeUso = UsoCupomPessoaFisica != null && UsoCupomPessoaFisica.Quantidade >= CupomBase.QtdUsoCliente;
        }
    }
}