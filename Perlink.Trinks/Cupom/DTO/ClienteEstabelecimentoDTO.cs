﻿namespace Perlink.Trinks.Cupom.DTO
{
    public sealed class ClienteEstabelecimentoDTO
    {
        public int IdPessoa { get; set; }

        public string Nome { get; set; }

        public string Telefone { get; set; }

        public string Email { get; set; }

        public bool Associado { get; set; }

        public bool AtingiuLimiteDeResgate { get; set; }

        public int? QuantidadeResgates { get; set; }

        public ClienteEstabelecimentoDTO() { }

        public ClienteEstabelecimentoDTO(int idPessoa, string nome, string telefone,
            string email, int? qtdResgates = null, bool associado = false, bool atingiuLimiteDeResgate = false)
        {
            IdPessoa = idPessoa;
            Nome = nome;
            Telefone = telefone;
            Email = email;
            Associado = associado;
            AtingiuLimiteDeResgate = atingiuLimiteDeResgate;
            QuantidadeResgates = qtdResgates;
        }
    }
}