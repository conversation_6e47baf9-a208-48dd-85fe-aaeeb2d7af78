﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class MontagemDePlanoService : BaseService, IMontagemDePlanoService
    {
        #region Validações
        private void ValidarPlano(PlanoDTO planoDTO)
        {
            ValidarNome(planoDTO.Nome);

            if (!string.IsNullOrEmpty(planoDTO.VendaOnline?.TermosDeUsoClube))
                ValidarTermosDeUso(planoDTO.VendaOnline?.TermosDeUsoClube);

            if (HtmlPossuiConteudo(planoDTO.ContratoDeAdesao?.TermosDoContrato))
                ValidarContratoDeAdesao(planoDTO.ContratoDeAdesao?.TermosDoContrato);

            if (planoDTO.Descricao?.Length > 500)
                ValidationHelper.Instance.AdicionarItemValidacao("A descrição do plano deve ter no máximo 500 caracteres.");

            if (planoDTO.Valor <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao("O preço plano deve ser maior que zero.");

            ValidarBeneficios(planoDTO.BeneficiosDoPlano);
        }        

        public void ValidarNome(string nome)
        {
            if (string.IsNullOrWhiteSpace(nome))
                ValidationHelper.Instance.AdicionarItemValidacao("O nome do plano deve estar preenchido.");

            if (nome.Length > 90)
                ValidationHelper.Instance.AdicionarItemValidacao("O nome do plano deve ter no máximo 90 caracteres.");

            if (EhSomenteNumero(nome))
                ValidationHelper.Instance.AdicionarItemValidacao("O nome do plano não pode conter apenas números.");

            if (EhSomenteCaractereEspecial(nome))
                ValidationHelper.Instance.AdicionarItemValidacao("O nome do plano não pode conter apenas caracteres especiais.");

            if (EhSomenteNumeroECaractereEspecial(nome))
                ValidationHelper.Instance.AdicionarItemValidacao("O nome do plano não pode conter apenas números e caracteres especiais.");
        }

        public void ValidarTermosDeUso(string termosDeUso)
        {
            if (termosDeUso.Length > 0 && termosDeUso.Length < 5)
                ValidationHelper.Instance.AdicionarItemValidacao("Os termos de uso do plano devem ter no mínimo 5 caracteres.");

            if (EhSomenteNumero(termosDeUso))
                ValidationHelper.Instance.AdicionarItemValidacao("Os termos de uso do plano não podem conter apenas números.");

            if (EhSomenteCaractereEspecial(termosDeUso))
                ValidationHelper.Instance.AdicionarItemValidacao("Os termos de uso do plano não podem conter apenas caracteres especiais.");

            if (EhSomenteNumeroECaractereEspecial(termosDeUso))
                ValidationHelper.Instance.AdicionarItemValidacao("Os termos de uso do plano não podem conter apenas números e caracteres especiais.");
        }

        public void ValidarContratoDeAdesao(string contratoDeAdesao)
        {
            if (contratoDeAdesao.Length > 0 && contratoDeAdesao.Length < 300)
                ValidationHelper.Instance.AdicionarItemValidacao("O contrato de adesão do plano deve ter no mínimo 300 caracteres.");

            if (EhSomenteNumero(contratoDeAdesao))
                ValidationHelper.Instance.AdicionarItemValidacao("O contrato de adesão do plano não pode conter apenas números.");

            if (EhSomenteCaractereEspecial(contratoDeAdesao))
                ValidationHelper.Instance.AdicionarItemValidacao("O contrato de adesão do plano não pode conter apenas caracteres especiais.");

            if (EhSomenteNumeroECaractereEspecial(contratoDeAdesao))
                ValidationHelper.Instance.AdicionarItemValidacao("O contrato de adesão do plano não pode conter apenas números e caracteres especiais.");
        }

        private void ValidarBeneficios(List<BeneficioDoPlanoDTO> beneficios)
        {
            if (beneficios.Count <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao("O plano deve conter ao menos um beneficio configurado.");

            var idsProduto = beneficios.Where(a => a.Tipo == TipoDeBeneficioEnum.Produto).Select(b => b.IdItemReferencia);
            if (idsProduto.Count() != idsProduto.Distinct().Count())
                ValidationHelper.Instance.AdicionarItemValidacao("Existem produtos repetidos no plano.");

            var idsServico = beneficios.Where(a => a.Tipo == TipoDeBeneficioEnum.Servico).Select(b => b.IdItemReferencia);
            if (idsServico.Count() != idsServico.Distinct().Count())
                ValidationHelper.Instance.AdicionarItemValidacao("Existem serviços repetidos no plano.");

            foreach (var item in beneficios)
            {
                if (item.ConsumoLimitado && item.QuantidadePorCiclo <= 0)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Para itens de beneficio limitados, a quantidade de consumo deve ser maior que zero.");
                    break;
                }

                if (item.CicloDeConsumo == CicloDeConsumoEnum.Especifico && item.PrazoParaConsumo <= 0)
                    ValidationHelper.Instance.AdicionarItemValidacao("Inclua um prazo máximo válido para o consumo.");
            }
        }

        private static bool HtmlPossuiConteudo(string html)
        {
            return !string.IsNullOrEmpty(Regex.Replace(html ?? "", "<.*?>", "").Trim());
        }

        private static bool EhSomenteNumero(string texto)
        {
            return Regex.IsMatch(texto, @"^\d+$");
        }

        private static bool EhSomenteCaractereEspecial(string texto)
        {
            return Regex.IsMatch(texto, "^[^a-zA-Z0-9]+$");
        }

        private static bool EhSomenteNumeroECaractereEspecial(string texto)
        {
            return Regex.IsMatch(texto, "^[^a-zA-Z]+$");
        }
        #endregion

        [TransactionInitRequired]
        public void SalvarPlano(PlanoDTO dados)
        {
            ValidarPlano(dados);

            if (!ValidationHelper.Instance.IsValid)
                return;

            CriarPlano(dados);
        }

        [TransactionInitRequired]
        public void EditarPlano(PlanoDTO dados)
        {
            var plano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Obter(dados.Id, dados.IdEstabelecimento);

            EditarVendaOnline(plano, dados.VendaOnline);
            EditarContratoDeAdesao(plano, dados.ContratoDeAdesao);
            plano.DadosDoPlanoCliente.Editar(dados);
            EditarDadosNaoSensiveisNaAssinatura(plano);

            GerarIntencaoDeEdicaoDoPlanoSeNecessario(plano, dados);

            Domain.ClubeDeAssinaturas.PlanoClienteRepository.Update(plano);
        }

        private void GerarIntencaoDeEdicaoDoPlanoSeNecessario(PlanoCliente plano, PlanoDTO dados)
        {
            var possuiAssinaturasParaRegularizar = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.VerificarSePlanoPossuiAssinaturasParaRegularizar(plano.Id, dados.IdEstabelecimento);

            if (possuiAssinaturasParaRegularizar || !plano.ContratosDeAdesao.Any() || !ValidarEditouDadosSensiveis(plano, dados))
                return;

            var intencaoEdicao = new IntencaoEdicaoDoPlanoCliente(dados, plano);
            
            foreach(var beneficioDoPlanoDTO in dados.BeneficiosDoPlano)
            {
                var beneficio = Domain.ClubeDeAssinaturas.BeneficioRepository.Factory.CreateBeneficio(beneficioDoPlanoDTO, plano.DadosDoPlanoCliente.IdEstabelecimento);
                intencaoEdicao.AdicionarBeneficio(beneficio);
            }

            var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasDisponiveisPorPlano(plano.Id);
            
            foreach (var assinatura in assinaturas)
                assinatura.EmailAvisoEdicaoEnviado = false;

            Domain.ClubeDeAssinaturas.IntencaoEdicaoDoPlanoClienteRepository.SaveNew(intencaoEdicao);
        }

        private bool ValidarEditouDadosSensiveis(PlanoCliente plano, PlanoDTO dados)
        {
            var editouValor = plano.DadosDoPlanoCliente.Valor != dados.Valor;
            var editouEncerraAposXCobrancas = plano.DadosDoPlanoCliente.EncerraAposXCobrancas != dados.EncerraAposXCobrancas;
            var editouBeneficios = plano.BeneficiosDoPlano.Count != dados.BeneficiosDoPlano.Count;
            var editouPermitiConsumoAteFimPeriodoPago = plano.DadosDoPlanoCliente.PermitiConsumoAteFimPeriodoPago != dados.PermitiConsumoAteFimPeriodoPago;

            return editouValor || editouEncerraAposXCobrancas || editouBeneficios || editouPermitiConsumoAteFimPeriodoPago;
        }

        private void EditarDadosNaoSensiveisNaAssinatura(PlanoCliente plano)
        {
            var assinaturasDisponiveis = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasDisponiveisPorPlano(plano.Id);
            
            foreach(var assinatura in assinaturasDisponiveis)
            {
                assinatura.Nome = plano.DadosDoPlanoCliente.Nome;
                assinatura.Descricao = plano.DadosDoPlanoCliente.Descricao;
                Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.UpdateNoFlush(assinatura);
            }

            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Flush();
        }

        private void CriarPlano(PlanoDTO dados)
        {
            ConfigurarEstabelecimentoParaUtilizarClubeSeNecessario(dados.IdEstabelecimento);

            var vendaOnline = CriarVendaOnline(dados.VendaOnline);
            var plano = new PlanoCliente(dados, vendaOnline);

            SalvarBeneficios(dados, plano);
            Domain.ClubeDeAssinaturas.PlanoClienteRepository.SaveNew(plano);

            CriarContratoDeAdesao(dados.ContratoDeAdesao, plano);
        }

        private VendaOnline CriarVendaOnline(VendaOnlineDTO dados)
        {
            var vendaOnline = new VendaOnline(dados);
            Domain.ClubeDeAssinaturas.VendaOnlineRepository.SaveNew(vendaOnline);
            return vendaOnline;
        }

        private void CriarContratoDeAdesao(ContratoDeAdesaoDTO dados, PlanoCliente planoCliente)
        {
            if (dados.TermosDoContrato != null)
            {
                dados.Versao += 1;
                var contratoDeAdesao = new ContratoDeAdesao(dados, planoCliente);
                Domain.ClubeDeAssinaturas.ContratoDeAdesaoRepository.SaveNew(contratoDeAdesao);
            }
        }

        public void ConfigurarEstabelecimentoParaUtilizarClubeSeNecessario(int idEstabelecimento)
        {
            AssociarFormaDePagamentoDoClubeDeAssinaturaAoEstabelecimento(idEstabelecimento);
            AssociarMotivoDeDescontoDoClubeDeAssinaturaAoEstabelecimento(idEstabelecimento);
        }

        public void EditarVendaOnline(PlanoCliente plano, VendaOnlineDTO dados)
        {
            if (plano.ConfiguracaoDeVendaOnline == null)
                plano.ConfiguracaoDeVendaOnline = CriarVendaOnline(dados);
            else plano.ConfiguracaoDeVendaOnline.Editar(dados);
        }

        public void EditarContratoDeAdesao(PlanoCliente plano, ContratoDeAdesaoDTO dados)
        {
            var contratoDeAdesaoAtivo = plano.ContratosDeAdesao.OrderByDescending(x => x.Versao).FirstOrDefault();
            bool contratoFoiEditado = dados?.TermosDoContrato != contratoDeAdesaoAtivo?.TermosDoContrato;

            if (contratoFoiEditado)
            {
                if (plano.ContratosDeAdesao.Any())
                    dados.Versao = contratoDeAdesaoAtivo.Versao;

                CriarContratoDeAdesao(dados, plano);
                AtualizarStatusDasAssinaturasDeUmPlanoParaContratoPendente(plano.Id);
            }
        }

        public void InativarPlano(int id, int idEstabelecimento)
        {
            var plano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Obter(id, idEstabelecimento);
            var podeInativarPlanoDoModelo = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.PodeInativarClubeDoModelo(idEstabelecimento);
            var podeInativarPlanoDaUnidade = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.PodeCriarEditarInativarClubeDaUnidade(idEstabelecimento);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var ehEstabelecimentoBaseadoEmModeloENaoEhUmModelo = estabelecimento.EstabelecimentoEhBaseadoEmModeloENaoEhModelo();

            if (ehEstabelecimentoBaseadoEmModeloENaoEhUmModelo && !podeInativarPlanoDoModelo)
                ValidationHelper.Instance.AdicionarItemValidacao("Você não tem permissão para inativar esse plano.");

            if (ehEstabelecimentoBaseadoEmModeloENaoEhUmModelo && !podeInativarPlanoDaUnidade)
                ValidationHelper.Instance.AdicionarItemValidacao("Você não tem permissão para inativar esse plano.");

            if (plano == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Não foi possível localizar o plano.");

            if (ValidationHelper.Instance.IsValid)
            {
                plano.InativarPlano();
                Domain.ClubeDeAssinaturas.PlanoClienteRepository.Update(plano);
            }
        }

        public void RemoverClubeDeAssinaturaDasFormasDePagamentoCasoNaoTenhaSidoAssociado(ref List<EstabelecimentoFormaPagamento> estFormaPagamento, ref IEnumerable<Financeiro.FormaPagamento> formasDePagamento)
        {
            bool jaTeveClubeDeAssinaturaAssociado = estFormaPagamento.Any(x => x.FormaPagamento.Id == (int)FormaPagamentoEnum.ClubeDeAssinatura);

            if (!jaTeveClubeDeAssinaturaAssociado)
            {
                formasDePagamento = formasDePagamento.Where(x => x.Tipo != FormaPagamentoTipoEnum.ClubeDeAssinaturas).ToList();
            }
        }

        private void SalvarBeneficios(PlanoDTO dados, PlanoCliente plano)
        {
            foreach (var beneficioDoPlanoDTO in dados.BeneficiosDoPlano)
                SalvarBeneficio(dados.IdEstabelecimento, plano, beneficioDoPlanoDTO);

            InativarBeneficios(dados.BeneficiosDoPlano, plano);
        }

        public void InativarBeneficios(List<BeneficioDoPlanoDTO> beneficiosDoPlanoDTO, PlanoCliente plano)
        {
            var beneficiosRemovidos = plano.BeneficiosDoPlano.Where(be => be.Id > 0 && !beneficiosDoPlanoDTO.Any(beDTO => beDTO.Id == be.Id));

            foreach (var beneficioRemovido in beneficiosRemovidos)
                beneficioRemovido.Inativar();
        }

        public void SalvarBeneficio(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlanoDTO beneficioDoPlanoDTO)
        {
            var beneficioPlano = plano.BeneficiosDoPlano.FirstOrDefault(be => be.Id > 0 && be.Id == beneficioDoPlanoDTO.Id);

            var estaEditando = beneficioPlano != null && beneficioPlano.Id > 0;
            var jaTemAssinaturaUsandoEsteBeneficio = estaEditando && Domain.ClubeDeAssinaturas.BeneficioDaAssinaturaRepository.BeneficioEstaEmUso(beneficioPlano.Beneficio.Id);
            var houveAlteracaoNoBeneficio = VerificarSeHouveAlteracaoNoBeneficio(beneficioPlano, beneficioDoPlanoDTO);

            if (jaTemAssinaturaUsandoEsteBeneficio && houveAlteracaoNoBeneficio)
                SubstituirBeneficio(idEstabelecimento, plano, beneficioDoPlanoDTO, beneficioPlano);
            if (!jaTemAssinaturaUsandoEsteBeneficio && houveAlteracaoNoBeneficio)
                beneficioPlano.Beneficio.Editar(beneficioDoPlanoDTO.ConsumoLimitado, beneficioDoPlanoDTO.ValorUnitario, beneficioDoPlanoDTO.QuantidadePorCiclo, beneficioDoPlanoDTO.CicloDeConsumo, beneficioDoPlanoDTO.PrazoParaConsumo);
            if (beneficioPlano == null)
                CriarEAssociarBeneficioAoPlano(idEstabelecimento, plano, beneficioDoPlanoDTO);
        }

        public void UnidadeTemPermissaoParaCriarPlano(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (!estabelecimento.EstabelecimentoEhBaseadoEmModeloENaoEhModelo())
                return;

            var unidadePodeCriarPlano = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.PodeCriarEditarInativarClubeDaUnidade(idEstabelecimento);
            if (!unidadePodeCriarPlano)
                ValidationHelper.Instance.AdicionarItemValidacao("Você não possui permissão para criar um plano.");
        }

        public void UnidadeTemPermissaoParaEditarPlano(int idPlano, int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (!estabelecimento.EstabelecimentoEhBaseadoEmModeloENaoEhModelo())
                return;

            var plano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Obter(idPlano, idEstabelecimento);
            var unidadePodeEditarDoModelo = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.PodeEditarClubeDoModelo(idEstabelecimento);
            if (plano.PlanoClienteModelo != null && !unidadePodeEditarDoModelo)
                ValidationHelper.Instance.AdicionarItemValidacao("Você não possui permissão para editar um plano.");
        }

        private void CriarEAssociarBeneficioAoPlano(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlanoDTO beneficioDoPlanoDTO)
        {
            var beneficio = Domain.ClubeDeAssinaturas.BeneficioRepository.Factory.CreateBeneficio(beneficioDoPlanoDTO, idEstabelecimento);
            plano.AdicionarBeneficio(beneficio);
        }

        private void SubstituirBeneficio(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlanoDTO beneficioDoPlanoDTO, BeneficioDoPlano beneficioPlano)
        {
            beneficioPlano.Inativar();

            CriarEAssociarBeneficioAoPlano(idEstabelecimento, plano, beneficioDoPlanoDTO);
        }

        private void AssociarFormaDePagamentoDoClubeDeAssinaturaAoEstabelecimento(int idEstabelecimento)
        {
            var estabelecimentoPossuiFormaDePagamentoClubeDeAssinaturas = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.VerificarSeEstabelecimentoUsaFormaPagamento(idEstabelecimento, FormaPagamentoEnum.ClubeDeAssinatura);

            if (!estabelecimentoPossuiFormaDePagamentoClubeDeAssinaturas)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
                var formaDePagamentoClubeDeAssinatura = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(p => p.Id == (int)FormaPagamentoEnum.ClubeDeAssinatura).FirstOrDefault();
                Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoClubeDeAssinatura, estabelecimento);
            }

            var estabelecimentoPossuiFormaDePagamentoClubeDeAssinaturasPorLink = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.VerificarSeEstabelecimentoUsaFormaPagamento(idEstabelecimento, FormaPagamentoEnum.ClubeDeAssinaturaPorLink);

            if (!estabelecimentoPossuiFormaDePagamentoClubeDeAssinaturasPorLink)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
                var formaDePagamentoClubeDeAssinaturaPorLink = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(p => p.Id == (int)FormaPagamentoEnum.ClubeDeAssinaturaPorLink).FirstOrDefault();
                Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoClubeDeAssinaturaPorLink, estabelecimento);
            }
        }

        private void AssociarMotivoDeDescontoDoClubeDeAssinaturaAoEstabelecimento(int idEstabelecimento)
        {
            try
            {
                var estabelecimentoPossuiMotivoDescontClubeDeAssinaturas = Domain.Financeiro.MotivoDescontoRepository.ObterMotivoDeDescontoPadrao(idEstabelecimento, MotivoDeDescontoDoTrinksEnum.ClubeDeAssinaturas);
            }
            catch (ArgumentException)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

                Domain.Pessoas.EstabelecimentoService.CriarOuDesativarMotivosDeDescontoDoTrinksSeNecessario
                    (estabelecimento, out _, MotivoDeDescontoDoTrinksEnum.ClubeDeAssinaturas);
            }
        }

        private static bool VerificarSeHouveAlteracaoNoBeneficio(BeneficioDoPlano beneficioDoPlano, BeneficioDoPlanoDTO beneficioDoPlanoDTO)
        {
            if (beneficioDoPlano == null)
                return false;

            return beneficioDoPlano.Beneficio.ConsumoLimitado != beneficioDoPlanoDTO.ConsumoLimitado
                || beneficioDoPlano.Beneficio.QuantidadeMaximaConsumo != beneficioDoPlanoDTO.QuantidadePorCiclo
                || beneficioDoPlano.Beneficio.ValorUnitario != beneficioDoPlanoDTO.ValorUnitario
                || beneficioDoPlano.Beneficio.CicloDeConsumo != beneficioDoPlanoDTO.CicloDeConsumo
                || beneficioDoPlano.Beneficio.PrazoParaConsumo != beneficioDoPlanoDTO.PrazoParaConsumo;
        }

        private void AtualizarStatusDasAssinaturasDeUmPlanoParaContratoPendente(int planoId)
        {
            var assinaturasDisponiveis = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasComLinkDisponiveisPorPlano(planoId);

            if (assinaturasDisponiveis != null && assinaturasDisponiveis.Any())
            {
                foreach (var assinatura in assinaturasDisponiveis)
                {
                    assinatura.AlterarAssinaturaParaContratoPendente();
                    Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.UpdateNoFlush(assinatura);
                }

                Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Flush();
            }
        }
    }
}