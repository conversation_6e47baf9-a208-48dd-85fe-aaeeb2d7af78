﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.Pessoas;
using System.Collections.Generic;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public interface IMontagemDePlanoService : IService
    {
        void SalvarPlano(PlanoDTO dados);
        void EditarPlano(PlanoDTO dados);
        void InativarPlano(int idPlano, int idEstabelecimento);
        void RemoverClubeDeAssinaturaDasFormasDePagamentoCasoNaoTenhaSidoAssociado(ref List<EstabelecimentoFormaPagamento> estFormaPagamento, ref IEnumerable<Financeiro.FormaPagamento> formasDePagamento);
        void SalvarBeneficio(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlanoDTO beneficioDoPlanoDTO);
        void InativarBeneficios(List<BeneficioDoPlanoDTO> beneficiosDoPlanoDTO, PlanoCliente plano);
        void ConfigurarEstabelecimentoParaUtilizarClubeSeNecessario(int idEstabelecimento);
        void UnidadeTemPermissaoParaCriarPlano(int idEstabelecimento);
        void UnidadeTemPermissaoParaEditarPlano(int idPlano, int idEstabelecimento);
    }
}
