﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Shared.Text;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class VendaDeAssinaturaService : BaseService, IVendaDeAssinaturaService
    {
        public List<PlanosDoEstabelecimentoParaVenda> ObterPlanosParaVenda(int idEstabelecimento)
        {
            var planos = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterListaDePlanosAtivosParaVenda(idEstabelecimento);
            return planos;
        }

        [TransactionInitRequired]
        public int? AssinarPlano(AssinaturaDoPlanoDTO dados)
        {
            Validar(dados);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var planoCliente = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Load(dados.IdDoPlano);
            var assinatura = new AssinaturaDoCliente(planoCliente, dados.IdPessoaFisicaCliente, dados.DataDaAssinatura, dados.CanalDeVenda ?? CanalDeVendaEnum.Estabelecimento, dados.MetodoCobranca ?? MetodoCobrancaEnum.LinkDePagamento);
            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.SaveNew(assinatura);

            return assinatura.Id;
        }

        private static void Validar(AssinaturaDoPlanoDTO dados)
        {
            if (dados.IdDoPlano <= 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Selecione uma assinatura valida");
            }

            if (dados.IdPessoaFisicaCliente <= 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Selecione um cliente valido");
            }
        }

        public string ObterLinkDeAssinaturaDoContratoDeAdesao(int idAssinaturaCliente, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPorIdAssinaturaEIdEstabelecimento(idAssinaturaCliente, idEstabelecimento);

            if (assinaturaCliente != null)
            {
                if (assinaturaCliente.PlanoCliente.ContratosDeAdesao.Any())
                {
                    var urlHotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(assinaturaCliente.IdEstabelecimento).UrlCompleta();
                    return $"{urlHotsite}/clube-assinatura-contrato-adesao?id={assinaturaCliente.IdentificadorAssinatura}";
                }
            }

            return null;
        }

        public ContratoDeAdesaoDTO ObterContratoDeAdesao(Guid identificadorAssinaturaCliente, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPeloIdentificadorEPeloIdEstabelecimento(identificadorAssinaturaCliente, idEstabelecimento);

            if (assinaturaCliente != null)
            {
                var contratoAtivo = assinaturaCliente.PlanoCliente?.ContratosDeAdesao?.OrderByDescending(x => x.Versao).FirstOrDefault();
                var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(assinaturaCliente.IdPessoaFisicaCliente);

                var contratoDTO = new ContratoDeAdesaoDTO()
                {
                    TermosDoContrato = contratoAtivo.TermosDoContrato,
                    NomeDoPlano = assinaturaCliente.PlanoCliente.DadosDoPlanoCliente.Nome,
                    NomeDoCliente = cliente.PessoaFisica.NomeCompleto,
                    TipoDoCliente = cliente.TipoCliente,
                    DataDeAssinatura = assinaturaCliente.DataAssinaturaContrato,
                    JaFoiAssinado = assinaturaCliente.Status != StatusDaAssinaturaEnum.ContratoPendente && assinaturaCliente.DataAssinaturaContrato.HasValue,
                };

                return contratoDTO;
            }

            return null;
        }

        public StatusDaAssinaturaEnum? ObterStatusDaAssinatura(int idAssinaturaCliente, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPorIdAssinaturaEIdEstabelecimento(idAssinaturaCliente, idEstabelecimento);

            if (assinaturaCliente == null)
                return null;

            return assinaturaCliente.Status;
        }

        public bool VerificarSePlanoPossuiAssinaturasParaRegularizar(int idPlano, int idEstabelecimento)
        {
            return Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.VerificarSePlanoPossuiAssinaturasParaRegularizar(idPlano, idEstabelecimento);
        }

        public DadosDaVendaDoClubeDeAssinaturaDTO ObterAssinaturaPeloIdentificador(Guid identificadorClubeDeAssinatura, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPeloIdentificadorEPeloIdEstabelecimento(identificadorClubeDeAssinatura, idEstabelecimento);

            if (assinaturaCliente == null)
                return null;

            var nomeDoCliente = Domain.Pessoas.PessoaFisicaRepository.Load(assinaturaCliente.IdPessoaFisicaCliente).NomeCompleto;
            var linkDePagamento = assinaturaCliente.Status == StatusDaAssinaturaEnum.AguardandoPagamento && assinaturaCliente.MetodoCobranca == MetodoCobrancaEnum.LinkDePagamento ?
                Domain.ClubeDeAssinaturas.VendaOnlineService.GerarLinkDePagamentoParaVendaDeClubeDeAssinaturaNoHotSite(assinaturaCliente).UrlDePagamento : null;

            return new DadosDaVendaDoClubeDeAssinaturaDTO()
            {
                Id = assinaturaCliente.Id,
                NomeDoPlano = assinaturaCliente.PlanoCliente.DadosDoPlanoCliente.Nome,
                DataAssinaturaContrato = assinaturaCliente.DataAssinaturaContrato,
                NomeDoCliente = nomeDoCliente,
                ValorDoPlano = assinaturaCliente.PlanoCliente.DadosDoPlanoCliente.Valor,
                IdPlano = assinaturaCliente.PlanoCliente.Id,
                LinkDePagamento = linkDePagamento
            };
        }

        public int? ObterIdDoPlanoPeloIdentificadorDaAssinatura(Guid identificadorAssinatura, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPeloIdentificadorEPeloIdEstabelecimento(identificadorAssinatura, idEstabelecimento);

            if (assinaturaCliente == null)
                return null;

            return assinaturaCliente.PlanoCliente.Id;
        }

        public AdicionalDoClubeDTO ObterDadosAdicionalDoClubePorEstabelecimento(int idEstabelecimento)
        {
            var idAssinatura = Domain.Cobranca.AssinaturaRepository.ObterIdUltimaAssinaturaPorIdEstabelecimento(idEstabelecimento);
            var adicional = Domain.Cobranca.AdicionalNaAssinaturaService.ObterAtivoPorServicoAdicional(idAssinatura, ServicoAdicionalEnum.ClubeDeAssinaturas);

            return new AdicionalDoClubeDTO
            {
                PossuiAdicional = adicional != null,
                TipoAdicional = adicional?.FormaDeContratacao.Id
            };
        }

        public bool VerificarSeEstabelecimentoPossuiLinkDePagamentoHabilitado(Estabelecimento estabelecimento)
        {
            bool formaDePagamentoAtiva = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .VerificarSeEstabelecimentoUsaFormaPagamento(estabelecimento.IdEstabelecimento, FormaPagamentoEnum.PagamentoOnlinePorLink);
            bool recebedorConfigurado = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ExisteEstabelecimentoAtivoEHabilitadoNosGateways(estabelecimento.IdEstabelecimento, GatewayEnum.PagarMe, GatewayEnum.PagarMeV5);
            bool estabelecimentoPossuiRecursoLinkHabilitado = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.LinkDePagamento).EstaDisponivel;

            return formaDePagamentoAtiva && recebedorConfigurado && estabelecimentoPossuiRecursoLinkHabilitado;
        }

        public bool ReenviarEmailClienteBalcao(Guid identificadorAssinatura, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPeloIdentificadorEPeloIdEstabelecimento(identificadorAssinatura, idEstabelecimento);

            if (assinaturaCliente == null) return false;

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorIdPessoaEIdEstabelecimento(assinaturaCliente.IdPessoaFisicaCliente, idEstabelecimento);

            if (clienteEstabelecimento == null || string.IsNullOrEmpty(clienteEstabelecimento.Cliente.PessoaFisica.Email)) return false;

            Domain.Pessoas.EnvioEmailService.EnviarEmailClienteBalcaoCadastrado(clienteEstabelecimento);
            return true;
        }

        public ResultadoPaginado<ClienteComAssinaturaParaRegularizarDTO> ListarClientesComAssinaturaParaRegularizar(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro)
        {
            var queryRetorno = ObterQueryDeRetorno(filtro);

            var pag = filtro.Paginacao;
            pag.TotalItens = queryRetorno.Count();
            queryRetorno = queryRetorno.Skip(pag.RegistroInicial - 1).Take(pag.RegistrosPorPagina);

            var retorno = queryRetorno.ToList();

            return new ResultadoPaginado<ClienteComAssinaturaParaRegularizarDTO>(retorno, filtro.Paginacao);
        }

        private IQueryable<ClienteComAssinaturaParaRegularizarDTO> ObterQueryDeRetorno(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro)
        {
            var queryRetorno = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterClientesComAssinaturaParaRegularizar(filtro);

            queryRetorno = ObterQueryOrdenada(queryRetorno);

            return queryRetorno;
        }

        private IQueryable<ClienteComAssinaturaParaRegularizarDTO> ObterQueryOrdenada(IQueryable<ClienteComAssinaturaParaRegularizarDTO> query)
        {
            return query.OrderBy(q => q.NomeDoCliente);
        }

        public List<ClienteComAssinaturaParaRegularizarDTO> ObterClientesComAssinaturaParaRegularizarParaExportacao(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro)
        {
            var queryRetorno = ObterQueryDeRetorno(filtro);

            var retorno = queryRetorno.ToList();

            return retorno;
        }

        public MemoryStream GerarCsvClientesComAssinaturaParaRegularizar(List<ClienteComAssinaturaParaRegularizarDTO> listaClientes, FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro)
        {
            Csv csv = new Csv();

            var cabecalho = new Csv.Linha();
            cabecalho.AdicionarCelula("Clientes com pendências");
            cabecalho.AdicionarCelula("Telefone");
            cabecalho.AdicionarCelula("Status");
            csv.AdicionarLinha(cabecalho);

            foreach (var p in listaClientes)
            {
                var linha = new Csv.Linha();

                linha.AdicionarCelula(p.NomeDoCliente);
                linha.AdicionarCelula(p.TelefoneDoCliente);
                linha.AdicionarCelula(p.StatusPendencia);

                csv.AdicionarLinha(linha);
            }

            return csv.ObterArquivo();
        }
    }
}