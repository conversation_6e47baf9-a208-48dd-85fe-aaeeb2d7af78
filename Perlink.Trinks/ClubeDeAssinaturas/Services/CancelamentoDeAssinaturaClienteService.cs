﻿using NHibernate.Util;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Pessoas;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class CancelamentoDeAssinaturaClienteService : BaseService, ICancelamentoDeAssinaturaClienteService
    {
        public void CancelarAssinatura(CancelarAssinaturaClienteDto cancelarAssinaturaClienteDto, int idPessoaQueCancelou, int idEstabelecimento)
        {
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Load(cancelarAssinaturaClienteDto.IdAssinatura);

            ValidarDadosParaCancelamento(assinaturaCliente, cancelarAssinaturaClienteDto, idEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return;

            if (cancelarAssinaturaClienteDto.CobrarMultaDeCancelamento)
                AplicarMultaDeCancelamento(assinaturaCliente, cancelarAssinaturaClienteDto);


            BloquearConsumoDoClienteComAssinaturasAtrasadas(assinaturaCliente);

            assinaturaCliente.Cancelar(idPessoaQueCancelou);
            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Update(assinaturaCliente);

            SinalizarCancelamentoDeAssinatura(assinaturaCliente.Id);
        }

        private void AplicarMultaDeCancelamento(AssinaturaDoCliente assinaturaCliente, CancelarAssinaturaClienteDto cancelarAssinaturaClienteDto)
        {
            assinaturaCliente.MultaDeCancelamentoDaAssinatura = new PagamentoMultaDeCancelamentoDaAssinatura
            {
                Valor = cancelarAssinaturaClienteDto.ValorDaMulta,
                MetodoDeCobranca = cancelarAssinaturaClienteDto.MetodoDeCobrancaDaMulta
            };
            if (cancelarAssinaturaClienteDto.MetodoDeCobrancaDaMulta == MetodoCobrancaEnum.Manual)
                assinaturaCliente.MultaDeCancelamentoDaAssinatura.DataPagamento = Calendario.Agora();
        }

        private void ValidarDadosParaCancelamento(AssinaturaDoCliente assinaturaCliente, CancelarAssinaturaClienteDto cancelarAssinaturaClienteDto, int idEstabelecimento)
        {
            if (assinaturaCliente.IdEstabelecimento != idEstabelecimento)
                ValidationHelper.Instance.AdicionarItemValidacao("Operação não permitida");

            if (assinaturaCliente.Status == StatusDaAssinaturaEnum.Cancelada || assinaturaCliente.Status == StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento)
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possivel cancelar uma assinatura já cancelada ou não renovada");

            if (cancelarAssinaturaClienteDto.CobrarMultaDeCancelamento && cancelarAssinaturaClienteDto.ValorDaMulta <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao("O valor da multa deve ser maior que zero");
        }

        public void CancelarAssinaturaClienteAposEstorno(int idTransacao, int idPessoaQueCancelou, int idEstabelecimento)
        {
            var idAssinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterIdAssinaturaPeloIdTransacao(idTransacao);
            CancelarAssinatura(new CancelarAssinaturaClienteDto()
            {
                IdAssinatura = idAssinatura,
            }, idPessoaQueCancelou, idEstabelecimento);
        }

        private void SinalizarCancelamentoDeAssinatura(int idAssinatura)
        {
            CancelarLinkDePagamentoDaAssinatura(idAssinatura);
        }

        private void CancelarLinkDePagamentoDaAssinatura(int idAssinatura)
        {
            var idLinkPagamento = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterIdDoLinkDePagamentoPelaAssinatura(idAssinatura);

            if (idLinkPagamento == 0)
                return;

            Domain.LinksDePagamento.CancelamentoLinkPagamentoService.CancelarLinkPagamento(idLinkPagamento);
        }

        public void CancelarTodasAssinaturasClientesEstabelecimento(Estabelecimento estabelecimento, PessoaFisica pessoaLogada = null)
        {
            var idsAssinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasAtivasDoEstabelecimento(estabelecimento.IdEstabelecimento);

            if (idsAssinaturas != null && idsAssinaturas.Any())
            {
                var idPessoaResponsavel = pessoaLogada?.IdPessoa ?? estabelecimento.PessoaJuridica.ResponsavelFinanceiro.IdPessoa;

                foreach (var idAssinatura in idsAssinaturas)
                {
                    CancelarAssinatura(new CancelarAssinaturaClienteDto()
                    {
                        IdAssinatura = idAssinatura,
                    }, idPessoaResponsavel, estabelecimento.IdEstabelecimento);
                }
            }
        }

        private void BloquearConsumoDoClienteComAssinaturasAtrasadas(AssinaturaDoCliente assinatura)
        {
            if (assinatura == null || assinatura.PermitiConsumoAteFimPeriodoPago)
                return;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(assinatura.IdEstabelecimento);
            var toggleBloqueioDeAgendamentoParaClientesComClubeInadimplente = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.BloqueioDeAgendamentoParaClientesComClubeInadimplente).EstaDisponivel;

            if (!toggleBloqueioDeAgendamentoParaClientesComClubeInadimplente)
                return;

            var clientePossuiAssinaturaAdimplente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ClientePossuiAssinaturaAdimplente(assinatura.IdEstabelecimento, assinatura.IdPessoaFisicaCliente);

            if (clientePossuiAssinaturaAdimplente)
                return;

            var clienteEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorIdPessoaEIdEstabelecimento(assinatura.IdPessoaFisicaCliente, assinatura.IdEstabelecimento);
            Domain.Pessoas.ClienteService.BloquearAgendamentoOnlineDoClienteNoEstabelecimento(clienteEstabelecimento);
        }
    }
}