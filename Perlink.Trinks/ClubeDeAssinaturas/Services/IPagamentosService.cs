﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public interface IPagamentosService : IService
    {
        Task<int?> ConfirmarPagamentoAsync(ConfirmarPagamentoDTO confirmarPagamentoDTO);
        ResumoDoLinkDTO ObterLinkDePagamentoDoClube(AssinaturaDoCliente assinatura, DateTime dataPagamento);
        ResumoDoLinkDTO ObterLinkDePagamentoDaMultaDeCancelamentoDoClube(AssinaturaDoCliente assinatura);
        void RenovarValidadeDoLinkDePagamento(int idDoLink, int idEstabelecimento);
        Task RealizarPagamentoRecorrenteDaAssinaturaAsync(List<AssinaturaDoCliente> assinaturas, DateTime hoje);
        Task<int> GerarTransacaoFinanceiraComConfirmacaoDoPagamento(GerarTransacaoDTO dto);
        void ValidarAssinaturas(AssinaturaDoCliente assinatura, bool ehImportacao);
        void ValidarVigencia(VigenciaDeAssinatura vigenciaAPagar, bool ehImportacao);
        void ValidacoesDoCaixa(int idEstabelecimento, PessoaFisica pessoaQueRealizou, bool ehImportacao);
        PagamentoDeAssinatura RegistrarPagamento(AssinaturaDoCliente assinatura, DateTime dataDoPagamento, bool ehPrimeiroPagamento, bool ehImportacao, bool ehPraConfigurarDataPagamento = false);
        PagamentoMultaDeCancelamentoDaAssinatura RegistrarPagamentoDaMultaDeCancelamento(AssinaturaDoCliente assinatura, DateTime dataDoPagamento);
        void EstornarPagamento(int idTransacao, DateTime dataDoEstorno);
        ResumoDoLinkDTO GerarNovoLinkDePagamentoDoClube(AssinaturaDoCliente assinatura);
    }
}