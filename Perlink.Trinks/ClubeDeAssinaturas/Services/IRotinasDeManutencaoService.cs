﻿using Perlink.DomainInfrastructure.Services;
using System;
using System.Threading.Tasks;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public interface IRotinasDeManutencaoService : IService
    {
        void RenovarOuEncerrarAssinaturasAposTerminoDeVigencia(DateTime hoje);
        void EncerrarConsumoAssinaturasCanceladasForaDaVigencia(DateTime hoje);
        void TornarAssinaturasSemPagamentosComoVencidas(DateTime hoje);
        void SuspenderAssinaturasSemPagamentosHaXDias(DateTime hoje);
        Task RealizarPagamentoRecorrenteDaAssinaturaAsync(DateTime hoje);
        void RealizarEdicaoDosPlanosDepoisDe30Dias(DateTime hoje);
    }
}