﻿using NHibernate.Util;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Auditing;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class RotinasDeManutencaoService : BaseService, IRotinasDeManutencaoService
    {

        public void RenovarOuEncerrarAssinaturasAposTerminoDeVigencia(DateTime hoje)
        {
            var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasAtivasQueTerminaramVigenciaAtual(hoje);
            bool temBeneficioComCicloDeConsumoEspecifico = false;

            foreach (var assinatura in assinaturas)
            {
                try
                {
                    temBeneficioComCicloDeConsumoEspecifico = RenovarOuEncerrarAssinaturaAposTerminoDaVigencia(hoje, temBeneficioComCicloDeConsumoEspecifico, assinatura);
                }
                catch (Exception ex)
                {
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                    LogService<RotinasDeManutencaoService>.Error($"[Recorrencia-Clube] - Erro ao renovar ou encerrar assinatura {assinatura.Id}.");
                }
            }
        }

        [TransactionInitRequired]
        private bool RenovarOuEncerrarAssinaturaAposTerminoDaVigencia(DateTime hoje, bool temBeneficioComCicloDeConsumoEspecifico, AssinaturaDoCliente assinatura)
        {
            var dataUltimaVigencia = Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.ObterDataInicialDaUltimaVigencia(assinatura.Id);

            if (assinatura.DataProgramadaEncerramento < hoje)
            {
                DateTime dataLimite = hoje;

                foreach (var beneficio in assinatura.BeneficiosDaAssinatura)
                {
                    if (beneficio.Beneficio.CicloDeConsumo == 0)
                    {
                        var dataTemp = assinatura.DataAssinatura.AddMonths(beneficio.Beneficio.PrazoParaConsumo ?? 0);

                        if (dataTemp > dataLimite)
                        {
                            dataLimite = dataTemp;
                        }

                        temBeneficioComCicloDeConsumoEspecifico = true;
                    }
                }

                if (dataLimite > hoje)
                {
                    assinatura.Renovar(dataUltimaVigencia, true);
                }
                else
                {
                    assinatura.EncerrarAssinatura();
                    DesabilitarLinkDePagamentoDaAssinatura(assinatura.Id);
                }
            }
            else
                assinatura.Renovar(dataUltimaVigencia, false);

            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Update(assinatura);
            return temBeneficioComCicloDeConsumoEspecifico;
        }

        public void EncerrarConsumoAssinaturasCanceladasForaDaVigencia(DateTime hoje)
        {
            var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasCanceladasComConsumoEForaDaVigencia(hoje);

            foreach (var assinatura in assinaturas)
            {
                try
                {
                    EncerrarConsumoAssinaturaCanceladaForaDaVigencia(assinatura);
                    BloquearConsumoDoClienteComAssinaturasAtrasadas(assinatura);
                }
                catch (Exception ex)
                {
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                    LogService<RotinasDeManutencaoService>.Error($"[Recorrencia-Clube] - Erro ao renovar ou encerrar assinatura {assinatura.Id}.");
                }
            }
        }

        [TransactionInitRequired]
        private static void EncerrarConsumoAssinaturaCanceladaForaDaVigencia(AssinaturaDoCliente assinatura)
        {
            assinatura.SinalizarFimPeriodoConsumoAssinaturaCancelada();
            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Update(assinatura);
        }

        public void TornarAssinaturasSemPagamentosComoVencidas(DateTime hoje)
        {
            var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasQueRenovaramSemPagarVigenciaPassada(hoje);

            foreach (var assinatura in assinaturas)
            {
                try
                {
                    TornarAssinaturaSemPagagamentoComoVencida(assinatura);
                    BloquearConsumoDoClienteComAssinaturasAtrasadas(assinatura);
                }
                catch (Exception ex)
                {
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                    LogService<RotinasDeManutencaoService>.Error($"[Recorrencia-Clube] - Erro ao tornar assinatura sem pagamentos como vencidas. Assinatura {assinatura.Id}.");
                }
            }
        }

        private void BloquearConsumoDoClienteComAssinaturasAtrasadas(AssinaturaDoCliente assinatura)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(assinatura.IdEstabelecimento);
            var toggleBloqueioDeAgendamentoParaClientesComClubeInadimplente = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.BloqueioDeAgendamentoParaClientesComClubeInadimplente).EstaDisponivel;

            if (!toggleBloqueioDeAgendamentoParaClientesComClubeInadimplente)
                return;

            var clientePossuiAssinaturaAdimplente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ClientePossuiAssinaturaAdimplente(assinatura.IdEstabelecimento, assinatura.IdPessoaFisicaCliente);

            if (clientePossuiAssinaturaAdimplente)
                return;

            var clienteEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorIdPessoaEIdEstabelecimento(assinatura.IdPessoaFisicaCliente, assinatura.IdEstabelecimento);
            Domain.Pessoas.ClienteService.BloquearAgendamentoOnlineDoClienteNoEstabelecimento(clienteEstabelecimento);
        }

        [TransactionInitRequired]
        private static void TornarAssinaturaSemPagagamentoComoVencida(AssinaturaDoCliente assinatura)
        {
            assinatura.RegistrarPagamentoEmAtraso();
            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Update(assinatura);
        }

        public void SuspenderAssinaturasSemPagamentosHaXDias(DateTime hoje)
        {

            var diasParaSuspender = new ParametrosTrinks<int>(ParametrosTrinksEnum.dias_para_suspender_assinaturas_do_cliente_do_clube_de_assinaturas).ObterValor();
            var dataLimitePagamento = Calendario.Hoje().AddDays(-diasParaSuspender);

            var assinaturasAtrasadas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasAtrasadasQueNaoForamPagasHaXDias(dataLimitePagamento);

            foreach (var assinatura in assinaturasAtrasadas)
            {
                try
                {
                    SuspenderAssinaturaSemPagamentoHaXDias(hoje, assinatura);
                }
                catch (Exception ex)
                {
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                    LogService<RotinasDeManutencaoService>.Error($"[Recorrencia-Clube] - Erro ao suspender assinaturas sem pagamentos a X dias. Assinatura {assinatura.Id}.");
                }
            }
        }

        [TransactionInitRequired]
        private static void SuspenderAssinaturaSemPagamentoHaXDias(DateTime hoje, AssinaturaDoCliente assinatura)
        {
            assinatura.SuspenderPorFaltaDePagamento(hoje);
            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Update(assinatura);
        }

        public async Task RealizarPagamentoRecorrenteDaAssinaturaAsync(DateTime hoje)
        {
            var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasComLinkParaPagamento(hoje);
            await Domain.ClubeDeAssinaturas.PagamentosService.RealizarPagamentoRecorrenteDaAssinaturaAsync(assinaturas, hoje);
        }

        public void RealizarEdicaoDosPlanosDepoisDe30Dias(DateTime hoje)
        {
            var planosParaEditar = Domain.ClubeDeAssinaturas.IntencaoEdicaoDoPlanoClienteRepository.BuscarPlanosComIntencaoDeEdicao(hoje);

            if (!planosParaEditar.Any())
                return;

            foreach (var planoParaEditar in planosParaEditar)
            {
                var planoCliente = planoParaEditar.PlanoCliente;
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(planoCliente.DadosDoPlanoCliente.IdEstabelecimento);
                planoCliente.EditarDadosSensiveis(planoParaEditar);
                SalvarBeneficios(planoParaEditar, estabelecimento.IdEstabelecimento);
                Domain.ClubeDeAssinaturas.PlanoClienteRepository.UpdateNoFlush(planoCliente);

                var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasDisponiveisPorPlano(planoCliente.Id);
                foreach(var assinatura in assinaturas)
                {
                    assinatura.EditarDadosSensiveis(planoCliente);
                    Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.UpdateNoFlush(assinatura);
                }

                planoParaEditar.ConcluirEdicao();
            }

            Domain.ClubeDeAssinaturas.PlanoClienteRepository.Flush();
            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Flush();
        }

        private void DesabilitarLinkDePagamentoDaAssinatura(int idAssinatura)
        {
            var linkDaAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterLinkDePagamentoDaAssinaturaPorIdAssinatura(idAssinatura);
            if (linkDaAssinatura != null)
                linkDaAssinatura.Desativar();
        }

        private void SalvarBeneficios(IntencaoEdicaoDoPlanoCliente planoParaEditar, int idEstabelecimento)
        {
            foreach (var beneficioDoPlanoDTO in planoParaEditar.BeneficiosDoPlano)
                SalvarBeneficio(idEstabelecimento, planoParaEditar.PlanoCliente, beneficioDoPlanoDTO);

            InativarBeneficios(planoParaEditar.BeneficiosDoPlano, planoParaEditar.PlanoCliente);
        }

        private void InativarBeneficios(IList<BeneficioDoPlano> beneficiosDoPlanoDTO, PlanoCliente plano)
        {
            var beneficiosRemovidos = plano.BeneficiosDoPlano.Where(be => be.Id > 0 && !beneficiosDoPlanoDTO.Any(beDTO => beDTO.Id == be.Id));

            foreach (var beneficioRemovido in beneficiosRemovidos)
                beneficioRemovido.Inativar();
        }

        private void SalvarBeneficio(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlano beneficioDoPlanoDTO)
        {
            var beneficioPlano = plano.BeneficiosDoPlano.FirstOrDefault(be => be.Id > 0 && be.Id == beneficioDoPlanoDTO.Id);

            var estaEditando = beneficioPlano != null && beneficioPlano.Id > 0;
            var jaTemAssinaturaUsandoEsteBeneficio = estaEditando && Domain.ClubeDeAssinaturas.BeneficioDaAssinaturaRepository.BeneficioEstaEmUso(beneficioPlano.Beneficio.Id);
            var houveAlteracaoNoBeneficio = VerificarSeHouveAlteracaoNoBeneficio(beneficioPlano, beneficioDoPlanoDTO);

            if (jaTemAssinaturaUsandoEsteBeneficio && houveAlteracaoNoBeneficio)
                SubstituirBeneficio(idEstabelecimento, plano, beneficioDoPlanoDTO, beneficioPlano);
            if (!jaTemAssinaturaUsandoEsteBeneficio && houveAlteracaoNoBeneficio)
                beneficioPlano.Beneficio.Editar(beneficioDoPlanoDTO.Beneficio.ConsumoLimitado, beneficioDoPlanoDTO.Beneficio.ValorUnitario, beneficioDoPlanoDTO.Beneficio.QuantidadeMaximaConsumo, beneficioDoPlanoDTO.Beneficio.CicloDeConsumo, beneficioDoPlanoDTO.Beneficio.PrazoParaConsumo);
            if (beneficioPlano == null)
                CriarEAssociarBeneficioAoPlano(idEstabelecimento, plano, beneficioDoPlanoDTO);
        }

        private static bool VerificarSeHouveAlteracaoNoBeneficio(BeneficioDoPlano beneficioDoPlano, BeneficioDoPlano beneficioDoPlanoDTO)
        {
            if (beneficioDoPlano == null)
                return false;

            return beneficioDoPlano.Beneficio.ConsumoLimitado != beneficioDoPlanoDTO.Beneficio.ConsumoLimitado
                || beneficioDoPlano.Beneficio.QuantidadeMaximaConsumo != beneficioDoPlanoDTO.Beneficio.QuantidadeMaximaConsumo
                || beneficioDoPlano.Beneficio.ValorUnitario != beneficioDoPlanoDTO.Beneficio.ValorUnitario
                || beneficioDoPlano.Beneficio.CicloDeConsumo != beneficioDoPlanoDTO.Beneficio.CicloDeConsumo
                || beneficioDoPlano.Beneficio.PrazoParaConsumo != beneficioDoPlanoDTO.Beneficio.PrazoParaConsumo;
        }

        private void SubstituirBeneficio(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlano beneficioDoPlanoDTO, BeneficioDoPlano beneficioPlano)
        {
            beneficioPlano.Inativar();

            CriarEAssociarBeneficioAoPlano(idEstabelecimento, plano, beneficioDoPlanoDTO);
        }

        private void CriarEAssociarBeneficioAoPlano(int idEstabelecimento, PlanoCliente plano, BeneficioDoPlano beneficioDoPlanoDTO)
        {
            plano.AdicionarBeneficio(beneficioDoPlanoDTO.Beneficio);
        }
    }
}