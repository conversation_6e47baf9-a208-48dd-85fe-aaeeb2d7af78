﻿using NHibernate.Util;
using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.Auditing;
using System;
using System.Collections.Generic;
using System.Net.Mail;
using System.Web;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class EnvioDeEmailService : BaseService, IEnvioDeEmailService
    {
        public void EnviarEmailDeAssinaturaDoContratoDeAdesao()
        {
            var assinaturasComContratoPendente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasParaReceberEmailDoContratoDeAdesao();

            if (assinaturasComContratoPendente == null && !assinaturasComContratoPendente.Any())
                return;

            foreach (var assinatura in assinaturasComContratoPendente)
            {
                try
                {
                    LogService<EnvioDeEmailService>.Info($"[CDA - Contrato de adesão] Iniciando envio de e-mail. Assinatura {assinatura.Id}");

                    var pessoaFisica = Domain.Pessoas.PessoaFisicaRepository.ObterPorId(assinatura.IdPessoaFisicaCliente);
                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(assinatura.IdEstabelecimento);
                    var linkDeAssinaturaDoContrato = Domain.ClubeDeAssinaturas.VendaDeAssinaturaService.
                        ObterLinkDeAssinaturaDoContratoDeAdesao(assinatura.Id, assinatura.IdEstabelecimento);

                    var assunto = $"Assinatura de Contrato do {assinatura.PlanoCliente.DadosDoPlanoCliente.Nome} - Ação Necessária";
                    var remetente = Pessoas.Services.EnvioEmailService.ObterRemetentePadrao();

                    var model = new
                    {
                        NomeDoCliente = pessoaFisica.NomeCompleto,
                        NomeDoPlano = assinatura.PlanoCliente.DadosDoPlanoCliente.Nome,
                        NomeDoEstabelecimento = estabelecimento.NomeDeExibicaoNoPortal,
                        LinkDeAssinaturaDoContrato = linkDeAssinaturaDoContrato
                    };

                    var destinatario = new MailAddress(pessoaFisica.Email, pessoaFisica.NomeOuApelido());
                    var nomeArquivoEmail = "ClubeDeAssinaturas.ContratoDeAdesaoPendente.cshtml";
                    var corpoEmail = Domain.Pessoas.EnvioEmailService.ObterCorpoDeEmailParaClienteEstabelecimentoPeloNomeDoArquivo(estabelecimento, nomeArquivoEmail, model);

                    LogService<EnvioDeEmailService>.Info($"[CDA - Contrato de adesão] Disparando e-mail para {pessoaFisica.Email} sobre a assinatura {assinatura.Id}, " +
                        $"pessoa {pessoaFisica.IdPessoa} do estabelecimento: {estabelecimento.IdEstabelecimento}");

                    Domain.Pessoas.EnvioEmailService.DispararEmail(assunto, corpoEmail, remetente, destinatario, null);

                    assinatura.EmailContratoDeAdesaoEnviado = true;
                    Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.UpdateNoFlush(assinatura);
                }
                catch (Exception e)
                {
                    LogService<EnvioDeEmailService>.Error($"[CDA - Contrato de adesão] Erro ao enviar e-mail: {e}");
                    Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(e));
                }
            }

            try
            {
                Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Flush();
            }
            catch (Exception e)
            {
                LogService<EnvioDeEmailService>.Error($"[CDA - Contrato de adesão] Erro ao salvar 'EmailContratoDeAdesaoEnviado': {e}");
                Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(e));
            }
        }

        public void EnviarEmailDeAvisoEdicaoDoClube()
        {
            var assinaturasComAvisoPendente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarAssinaturasParaReceberEmailDaEdicaoDoClube();

            if (assinaturasComAvisoPendente == null || !assinaturasComAvisoPendente.Any())
                return;

            foreach (var assinatura in assinaturasComAvisoPendente)
            {
                var planoEditado = Domain.ClubeDeAssinaturas.IntencaoEdicaoDoPlanoClienteRepository.PlanoCongeladoParaEdicao(assinatura.PlanoCliente.Id);

                try
                {
                    LogService<EnvioDeEmailService>.Info($"[CDA - Edição de Plano] Iniciando envio de e-mail. Assinatura {assinatura.Id}");

                    var pessoaFisica = Domain.Pessoas.PessoaFisicaRepository.ObterPorId(assinatura.IdPessoaFisicaCliente);
                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(planoEditado.DadosDoPlanoCliente.IdEstabelecimento);
                    var assunto = $"{planoEditado.DadosDoPlanoCliente.Nome} - Sua assinatura foi atualizada!";
                    var remetente = Pessoas.Services.EnvioEmailService.ObterRemetentePadrao();
                    var destinatario = new MailAddress(pessoaFisica.Email, pessoaFisica.NomeOuApelido());
                    var listaBeneficios = new List<string>();

                    foreach (var beneficio in planoEditado.BeneficiosDoPlano)
                    {
                        listaBeneficios.Add($"{beneficio.Beneficio.QuantidadeMaximaConsumo}x {beneficio.Beneficio.ObterDescricao()}");
                    }

                    var model = new
                    {
                        NomeDoCliente = pessoaFisica.NomeCompleto,
                        NomeDaAssinatura = planoEditado.DadosDoPlanoCliente.Nome,
                        ValorDaAssinatura = planoEditado.DadosDoPlanoCliente.Valor.ToString("N2"),
                        ListaDeBeneficiosDaAssinatura = listaBeneficios,
                        NomeDoEstabelecimento = estabelecimento.NomeDeExibicaoNoPortal
                    };

                    var nomeArquivoEmail = "ClubeDeAssinaturas.AssinaturaEditada.cshtml";
                    var corpoEmail = Domain.Pessoas.EnvioEmailService.ObterCorpoDeEmailParaClienteEstabelecimentoPeloNomeDoArquivo(estabelecimento, nomeArquivoEmail, model);

                    LogService<EnvioDeEmailService>.Info($"[CDA - Edição de Plano] Disparando e-mail para {pessoaFisica.Email} sobre a assinatura {assinatura.Id}, " +
                        $"pessoa {pessoaFisica.IdPessoa} do estabelecimento: {estabelecimento.IdEstabelecimento}");

                    Domain.Pessoas.EnvioEmailService.DispararEmail(assunto, corpoEmail, remetente, destinatario, null);

                    assinatura.EmailAvisoEdicaoEnviado = true;
                    Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.UpdateNoFlush(assinatura);

                }
                catch (Exception e)
                {
                    LogService<EnvioDeEmailService>.Error($"[CDA - Edição de Plano] Erro ao enviar e-mail: {e}");
                    Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(e));
                }
            }

            try
            {
                Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Flush();
            }
            catch (Exception e)
            {
                LogService<EnvioDeEmailService>.Error($"[CDA - Edição de Plano] Erro ao salvar 'EmailAvisoEdicaoEnviado': {e}");
                Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(e));
            }
        }
    }
}