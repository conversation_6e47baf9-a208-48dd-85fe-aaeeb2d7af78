﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Perlink.Trinks.ClubeDeAssinaturas.DTO.FiltroPaginacaoPlanoClienteDTO;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class VendaOnlineService : BaseService, IVendaOnlineService
    {
        private bool _estabelecimentoPossuiLinkHabilitado { get; set; }
        private bool _parametroHabilitaVendaOnlineDeAssinatura { get; set; }

        public VendaOnlineService()
        {
            _parametroHabilitaVendaOnlineDeAssinatura = new ParametrosTrinks<bool>(ParametrosTrinksEnum.habilita_venda_online_de_assinatura).ObterValor();
        }

        #region Métodos Públicos
        public ResultadoDadosDoClubeDeAssinaturaParaNoVendaHotsiteDto ObterPlanosVisiveisNoHotsite(FiltroPaginacaoPlanoClienteDTO filtro)
        {
            _estabelecimentoPossuiLinkHabilitado = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .VerificarSeEstabelecimentoUsaFormaPagamentoComCache(filtro.IdEstabelecimento, Financeiro.Enums.FormaPagamentoEnum.PagamentoOnlinePorLink);
            var registros = Filtrar(filtro);
            var configuracoesDeUnidade = ObterConfiguracoesDeUnidades(filtro.IdEstabelecimento);

            var resultado = new ResultadoDadosDoClubeDeAssinaturaParaNoVendaHotsiteDto
            {
                Registros = registros,
                Paginacao = filtro.Paginacao,
                ConfiguracoesDeUnidades = configuracoesDeUnidade,
            };

            return resultado;
        }

        public DadosDoClubeDeAssinaturaParaVendaHotsiteDTO ObterPlanoParaExibicaoNoHotsite(int idPlano, int idEstabelecimento)
        {
            _estabelecimentoPossuiLinkHabilitado = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .VerificarSeEstabelecimentoUsaFormaPagamentoComCache(idEstabelecimento, Financeiro.Enums.FormaPagamentoEnum.PagamentoOnlinePorLink);

            var planoCliente = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Obter(idPlano, idEstabelecimento);

            if (planoCliente == null)
                return null;

            var dadosDoClube = ToDadosDoClubeDeAssinaturaParaVendaHotsiteDto(planoCliente);

            AplicarDadosDosBeneficios(new List<DadosDoClubeDeAssinaturaParaVendaHotsiteDTO>() { dadosDoClube });
            return dadosDoClube;
        }

        public int? AssinarPlanoDeAssinaturaPeloHotsite(int idPessoa, int idPlanoAssinatura, int idEstabelecimento)
        {
            var planoCliente = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Obter(idPlanoAssinatura, idEstabelecimento);

            if (planoCliente == null)
                return null;

            var idAssinatura = Domain.ClubeDeAssinaturas.VendaDeAssinaturaService.AssinarPlano(new AssinaturaDoPlanoDTO()
            {
                IdDoPlano = idPlanoAssinatura,
                IdPessoaFisicaCliente = idPessoa,
                DataDaAssinatura = Calendario.Agora(),
                CanalDeVenda = CanalDeVendaEnum.Site
            });

            return idAssinatura;            
        }

        public string ObterUrlDeRedirecionamentoDoClienteAposAssinaturaDoPlano(int idAssinatura, int idEstabelecimento)
        {
            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPorIdAssinaturaEIdEstabelecimento(idAssinatura, idEstabelecimento);

            if(assinatura != null)
            {
                if (assinatura.Status == StatusDaAssinaturaEnum.ContratoPendente)
                    return Domain.ClubeDeAssinaturas.VendaDeAssinaturaService.ObterLinkDeAssinaturaDoContratoDeAdesao(idAssinatura, idEstabelecimento);
                else
                    return GerarLinkDePagamentoParaVendaDeClubeDeAssinaturaNoHotSite(assinatura).Url;
            }

            return null;
        }

        public ResumoDoLinkDTO GerarLinkDePagamentoParaVendaDeClubeDeAssinaturaNoHotSite(AssinaturaDoCliente assinatura)
        {
            var primeiroPagamentoJaEfetuado = assinatura.DataUltimoPagamento != null;

            if (primeiroPagamentoJaEfetuado)
                return null;

            return Domain.ClubeDeAssinaturas.PagamentosService.ObterLinkDePagamentoDoClube(assinatura, Calendario.Agora());
        }

        public async Task<string> AssinarContratoClubeDeAssinaturaERedirecionarCliente(Guid identificadorAssinatura, PessoaFisica pessoaFisicaAutenticada)
        {
            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPeloIdentificador(identificadorAssinatura);

            if (assinatura == null || pessoaFisicaAutenticada == null)
                return null;

            if (assinatura.IdPessoaFisicaCliente != pessoaFisicaAutenticada.IdPessoaFisica &&
                assinatura.IdPessoaFisicaCliente != pessoaFisicaAutenticada.IdPessoaUnificacao)
                return ObterUrlDeRedirecionamentoAposAssinarContratoDoClube(assinatura, false);            

            await AtualizarAssinaturaAposConfirmarContratoDoClube(assinatura, pessoaFisicaAutenticada);

            NotificarEstabelecimentoDaAssinaturaDoContratoDoClube(assinatura);

            return ObterUrlDeRedirecionamentoAposAssinarContratoDoClube(assinatura);
        }
        #endregion

        #region Métodos privados
        private async Task AtualizarAssinaturaAposConfirmarContratoDoClube(AssinaturaDoCliente assinatura, PessoaFisica pessoaFisicaAutenticada)
        {
            var statusAnterior = assinatura.HistoricoDeStatusDaAssinatura.OrderByDescending(x => x.DataCriacao).FirstOrDefault(x => !x.Ativo);

            if (statusAnterior == null)
            {
                if (assinatura.MetodoCobranca == MetodoCobrancaEnum.Manual)
                {
                    var confirmarPagamentoDTO = new ConfirmarPagamentoDTO
                    {
                        Assinatura = assinatura,
                        DataDoPagamento = Calendario.Agora(),
                        PessoaQueRealizou = pessoaFisicaAutenticada,
                        EhPrimeiroPagamento = true,
                        EhImportacao = false,
                    };

                    await Domain.ClubeDeAssinaturas.PagamentosService.ConfirmarPagamentoAsync(confirmarPagamentoDTO);

                }
                else assinatura.TratarStatusDaAssinatura(StatusDaAssinaturaEnum.AguardandoPagamento);
            }
            else
                assinatura.TratarStatusDaAssinatura(statusAnterior.Status);

            assinatura.DataAssinaturaContrato = Calendario.Agora();
        }

        private string ObterUrlDeRedirecionamentoAposAssinarContratoDoClube(AssinaturaDoCliente assinatura, bool assinaturaConfirmada = true)
        {
            var urlHotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(assinatura.IdEstabelecimento).UrlCompleta();

            if (!assinaturaConfirmada) return $"{urlHotsite}/assinatura-recusada-clube-assinatura?id={assinatura.IdentificadorAssinatura}";

            return $"{urlHotsite}/contrato-assinado-clube-assinatura?id={assinatura.IdentificadorAssinatura}";
        }

        private PermissaoParaClubeDeAssinaturasDTO ObterConfiguracoesDeUnidades(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (!estabelecimento.EstabelecimentoEhBaseadoEmModeloENaoEhModelo())
                return null;

            var idEstabelecimentoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdDoModeloDoEstabelecimento(idEstabelecimento);
            var configuracoes = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterConfiguracoesDeClubeDaUnidade(idEstabelecimento, idEstabelecimentoModelo);

            return configuracoes;
        }

        private List<DadosDoClubeDeAssinaturaParaVendaHotsiteDTO> Filtrar(FiltroPaginacaoPlanoClienteDTO filtro)
        {
            var query = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ListarPlanosFiltrados(filtro);

            filtro.Paginacao.TotalItens = query.Select(q => q.Id).Distinct().Count();

            query = AplicarOrdenacao(query, filtro);

            if (filtro.AplicarPaginacao)
                query = AplicarPaginacao(query, filtro.Paginacao);

            return ProjetarDadosDoClubeDeAssinaturaComBeneficiosParaVendaNoHotsite(query);
        }

        private List<DadosDoClubeDeAssinaturaParaVendaHotsiteDTO> ProjetarDadosDoClubeDeAssinaturaComBeneficiosParaVendaNoHotsite(IQueryable<PlanoCliente> query)
        {
            var registros = query.Select(ToDadosDoClubeDeAssinaturaParaVendaHotsiteDto).ToList();

            if (registros != null && registros.Count > 0)
                AplicarDadosDosBeneficios(registros);

            return registros;
        }

        private DadosDoClubeDeAssinaturaParaVendaHotsiteDTO ToDadosDoClubeDeAssinaturaParaVendaHotsiteDto(PlanoCliente p)
        {
            return new DadosDoClubeDeAssinaturaParaVendaHotsiteDTO
            {
                Id = p.Id,
                Nome = p.DadosDoPlanoCliente.Nome,
                DataCriacao = p.DadosDoPlanoCliente.DataDaCriacao,
                EhAssinaturaDoModelo = p.PlanoClienteModelo != null,
                ValorTotal = p.DadosDoPlanoCliente.Valor,
                EncerraAposXCobrancas = p.DadosDoPlanoCliente.EncerraAposXCobrancas,
                CicloDeCobranca = p.DadosDoPlanoCliente.CicloDeCobranca,
                Descricao = p.DadosDoPlanoCliente.Descricao,
                HabilitaVendaHotsite = VendaNoHotsiteEstaAtiva(p),
                TermoDeUso = p.ConfiguracaoDeVendaOnline != null ? p.ConfiguracaoDeVendaOnline.TermosDeUso : string.Empty,
            };
        }

        private bool VendaNoHotsiteEstaAtiva(PlanoCliente p)
        {
            return _estabelecimentoPossuiLinkHabilitado && _parametroHabilitaVendaOnlineDeAssinatura &&
                (p.ConfiguracaoDeVendaOnline != null && p.ConfiguracaoDeVendaOnline.HabilitaVendaHotsite);
        }

        private void AplicarDadosDosBeneficios(List<DadosDoClubeDeAssinaturaParaVendaHotsiteDTO> registros)
        {
            var registroIds = registros.Select(r => r.Id).ToList();

            var beneficiosAgrupados = Domain.ClubeDeAssinaturas.BeneficioDoPlanoRepository.ObterBeneficiosDosPlanos(registroIds);

            registros.ForEach(registro =>
            {
                registro.Beneficios = beneficiosAgrupados.TryGetValue(registro.Id, out var beneficios)
                    ? beneficios
                    : new List<DetalhesDoBeneficio>();
            });
        }

        private IQueryable<PlanoCliente> AplicarOrdenacao(IQueryable<PlanoCliente> query, FiltroPaginacaoPlanoClienteDTO filtro)
        {
            switch (filtro.OrdenarPor)
            {
                case OrdenarPlano.AssinaturasMaisRecente:
                    query = query.OrderByDescending(o => o.DadosDoPlanoCliente.DataDaCriacao);
                    break;
                case OrdenarPlano.Nome:
                    query = query.OrderBy(o => o.DadosDoPlanoCliente.Nome);
                    break;
                case OrdenarPlano.Status:
                    query = query.OrderByDescending(o => o.Ativo);
                    break;
            }

            return query;
        }

        private IQueryable<PlanoCliente> AplicarPaginacao(IQueryable<PlanoCliente> query, Shared.NHibernate.Paginacao.ParametrosPaginacao paginacao)
        {
            return query.Skip(paginacao.RegistroInicial - 1).Take(paginacao.RegistrosPorPagina);
        }

        private void NotificarEstabelecimentoDaAssinaturaDoContratoDoClube(AssinaturaDoCliente assinatura)
        {
            var pessoaCliente = Domain.Pessoas.PessoaFisicaRepository.Load(assinatura.IdPessoaFisicaCliente);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(assinatura.IdEstabelecimento);
            var texto = $"<p>{pessoaCliente.NomeOuApelido()} assinou o contrato do clube {assinatura.PlanoCliente.DadosDoPlanoCliente.Nome}. <b style='color: #ff9254;'>Clique aqui</b> para acessar a tabela de vendas do Clube de Assinatura e acompanhar os detalhes.</p>";
            var conta = estabelecimento.ObterResponsavel().Contas.FirstOrDefault();

            var notificacaoEstabelecimento = new NotificacaoEstabelecimento
            {
                Estabelecimento = estabelecimento,
                DataHoraReferencia = DateTime.Now,
                DataHoraRegistroNotificacao = DateTime.Now,
                Visualizada = false,
                Texto = texto,
                TipoNotificacao = 7,
                Conta = conta
            };

            Domain.Pessoas.NotificacaoEstabelecimentoRepository.SaveNew(notificacaoEstabelecimento);
        }

        #endregion
    }
}