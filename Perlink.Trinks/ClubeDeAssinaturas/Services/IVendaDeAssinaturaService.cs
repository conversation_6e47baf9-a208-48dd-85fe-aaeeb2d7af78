﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.IO;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public interface IVendaDeAssinaturaService : IService
    {
        int? AssinarPlano(AssinaturaDoPlanoDTO filtro);
        List<PlanosDoEstabelecimentoParaVenda> ObterPlanosParaVenda(int idEstabelecimento);
        string ObterLinkDeAssinaturaDoContratoDeAdesao(int idAssinaturaCliente, int idEstabelecimento);
        ContratoDeAdesaoDTO ObterContratoDeAdesao(Guid identificadorAssinaturaCliente, int idEstabelecimento);
        StatusDaAssinaturaEnum? ObterStatusDaAssinatura(int idAssinaturaCliente, int idEstabelecimento);
        DadosDaVendaDoClubeDeAssinaturaDTO ObterAssinaturaPeloIdentificador(Guid identificadorClubeDeAssinatura, int idEstabelecimento);
        int? ObterIdDoPlanoPeloIdentificadorDaAssinatura(Guid identificadorAssinatura, int idEstabelecimento);
        bool VerificarSePlanoPossuiAssinaturasParaRegularizar(int idPlano, int idEstabelecimento);
        AdicionalDoClubeDTO ObterDadosAdicionalDoClubePorEstabelecimento(int idEstabelecimento);
        bool VerificarSeEstabelecimentoPossuiLinkDePagamentoHabilitado(Estabelecimento estabelecimento);
        bool ReenviarEmailClienteBalcao(Guid identificadorAssinatura, int idEstabelecimento);
        ResultadoPaginado<ClienteComAssinaturaParaRegularizarDTO> ListarClientesComAssinaturaParaRegularizar(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro);
        List<ClienteComAssinaturaParaRegularizarDTO> ObterClientesComAssinaturaParaRegularizarParaExportacao(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro);
        MemoryStream GerarCsvClientesComAssinaturaParaRegularizar(List<ClienteComAssinaturaParaRegularizarDTO> listaClientes, FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro);
    }
}