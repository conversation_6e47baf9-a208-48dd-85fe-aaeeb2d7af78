﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Auditing;
using Perlink.Shared.Exceptions;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.PagamentosOnlineNoTrinks;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class PagamentosService : BaseService, IPagamentosService
    {

        public async Task<int?> ConfirmarPagamentoAsync(ConfirmarPagamentoDTO confirmarPagamentoDTO)
        {

            ValidarAssinaturas(confirmarPagamentoDTO.Assinatura, confirmarPagamentoDTO.EhImportacao);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var pagamento = RegistrarPagamento(confirmarPagamentoDTO.Assinatura, confirmarPagamentoDTO.DataDoPagamento, confirmarPagamentoDTO.EhPrimeiroPagamento, confirmarPagamentoDTO.EhImportacao);

            var dto = new GerarTransacaoDTO(confirmarPagamentoDTO.Assinatura, pagamento.Id, confirmarPagamentoDTO.PessoaQueRealizou);

            await GerarTransacaoFinanceiraComConfirmacaoDoPagamento(dto);

            return pagamento.Id;
        }

        public PagamentoDeAssinatura RegistrarPagamento(AssinaturaDoCliente assinatura, DateTime dataDoPagamento, bool ehPrimeiroPagamento, bool ehImportacao, bool ehPraConfigurarDataPagamento = false)
        {
            var pagamento = new PagamentoDeAssinatura(assinatura, dataDoPagamento, assinatura.ValorAPagar);
            Domain.ClubeDeAssinaturas.PagamentoDeAssinaturaRepository.SaveNew(pagamento);

            if (ehPrimeiroPagamento)
                assinatura.CriarNovaVigencia(pagamento.DataPagamento);

            assinatura.ConfirmarPagamento(pagamento.DataPagamento);

            TratarLiberacaoDoAgendamentoOnlineDoCliente(assinatura);

            if (ehPraConfigurarDataPagamento)
                assinatura.AtualizarDataProximoPagamento();

            Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Update(assinatura);

            var idVigenciaAPagar = Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.ObterIdVigenciaMaisAntigaNaoPaga(assinatura.Id);

            var vigenciaAPagar = Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.Load(idVigenciaAPagar);

            ValidarVigencia(vigenciaAPagar, ehImportacao);

            vigenciaAPagar.RegistrarPagamento(pagamento);
            Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.Update(vigenciaAPagar);

            return pagamento;
        }

        private void TratarLiberacaoDoAgendamentoOnlineDoCliente(AssinaturaDoCliente assinatura)
        {
            LogService<PagamentosService>.Info("[Pagamento-Clube] - Verificando se o Cliente estava com o agendamento bloqueado.");

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(assinatura.IdEstabelecimento);
            var toggleBloqueioDeAgendamentoParaClientesComClubeInadimplente = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.BloqueioDeAgendamentoParaClientesComClubeInadimplente).EstaDisponivel;

            if (!toggleBloqueioDeAgendamentoParaClientesComClubeInadimplente)
            {
                LogService<PagamentosService>.Error("[Pagamento-Clube] - Toggle não está habilitado para esse estabelecimento.");
                return;
            }

            var clienteEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorIdPessoaEIdEstabelecimento(assinatura.IdPessoaFisicaCliente, assinatura.IdEstabelecimento);

            if(clienteEstabelecimento != null && !clienteEstabelecimento.PodeAgendarOnlineNoEstabelecimento)
            {
                LogService<PagamentosService>.Info("[Pagamento-Clube] - Cliente estava com o agendamento bloqueado.");
                Domain.Pessoas.ClienteService.DesbloquearAgendamentoOnlineDoClienteNoEstabelecimento(clienteEstabelecimento);
            }
        }

        public PagamentoMultaDeCancelamentoDaAssinatura RegistrarPagamentoDaMultaDeCancelamento(AssinaturaDoCliente assinatura, DateTime dataDoPagamento)
        {
            assinatura.MultaDeCancelamentoDaAssinatura.RegistrarPagamento(dataDoPagamento, assinatura.MultaDeCancelamentoDaAssinatura.Valor);
            assinatura.Status = assinatura.PodeConsumirItens ? StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo : Enums.StatusDaAssinaturaEnum.Cancelada;

            return assinatura.MultaDeCancelamentoDaAssinatura;
        }

        public void ValidarAssinaturas(AssinaturaDoCliente assinatura, bool ehImportacao)
        {

            var vigenciaAtual = assinatura.VigenciaAtual != null ? Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.Load(assinatura.VigenciaAtual.Id) : null;

            if (!assinatura.PodeConfirmar())
            {
                if (ehImportacao) throw new Exception("Esta assinatura não aceita pagamentos.");

                ValidationHelper.Instance.AdicionarItemValidacao("Esta assinatura não aceita pagamentos.");
            }

            if (vigenciaAtual == null && assinatura.PossuiLinkDePagamento() && assinatura.Status == Enums.StatusDaAssinaturaEnum.AguardandoPagamento)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O primeiro pagamento desta assinatura só pode ser efetuado através do Link de Pagamento.");
            }

            if (vigenciaAtual != null && vigenciaAtual.FoiPago)
            {
                if (ehImportacao) throw new Exception("Esta assinatura não possui pendências de pagamento. Não é possível fazer adiantamento.");

                ValidationHelper.Instance.AdicionarItemValidacao("Esta assinatura não possui pendências de pagamento. Não é possível fazer adiantamento.");
            }

            var cobrancaEstaAtiva = assinatura.CobrancaDaAssinaturaEstaAtiva();

            if (!cobrancaEstaAtiva)
            {
                if (ehImportacao) throw new Exception("Esta assinatura não possui pendências de pagamento.");

                ValidationHelper.Instance.AdicionarItemValidacao("Esta assinatura não possui pendências de pagamento.");
            }
        }

        public void ValidarVigencia(VigenciaDeAssinatura vigenciaAPagar, bool ehImportacao)
        {
            if (vigenciaAPagar == null)
            {
                if (ehImportacao) throw new Exception("Esta assinatura não possui vigência pendente. Não é possível fazer adiantamento.");

                ValidationHelper.Instance.AdicionarItemValidacao("Esta assinatura não possui vigência pendente. Não é possível fazer adiantamento.");
            }
        }

        public void ValidacoesDoCaixa(int idEstabelecimento, PessoaFisica pessoaQueRealizou, bool ehImportacao)
        {
            var pessoaQueRecebeu = Domain.Pessoas.EstabelecimentoRepository.Queryable()
                                        .Where(p => p.IdEstabelecimento == idEstabelecimento)
                                        .Select(p => p.PessoaJuridica)
                                        .FirstOrDefault();

            var fuso = 0;

            if (pessoaQueRecebeu != null &&
                pessoaQueRecebeu.EnderecoProprio != null &&
                pessoaQueRecebeu.EnderecoProprio.UF != null)
            {
                fuso = pessoaQueRecebeu.EnderecoProprio.UF.Fuso;
            }

            var temControleDeCaixa = Domain.Financeiro.ControleDeCaixaService.PessoaJuridicaPossuiControleDeCaixaHabilitado(pessoaQueRecebeu.IdPessoa);
            if (!temControleDeCaixa)
            {
                return;
            }

            var caixaAbertoOuReabertoDoDiaAnteriorQuePrecisaSerFechado = Domain.Financeiro.RegistroDeCaixaPorOperadorRepository.ObterAbertoOuReabertoEmDataMenor(pessoaQueRecebeu.IdPessoa, pessoaQueRealizou.IdPessoa, Calendario.Hoje(fuso));
            if (caixaAbertoOuReabertoDoDiaAnteriorQuePrecisaSerFechado != null)
            {
                if (ehImportacao) throw new Exception("O caixa do dia " + caixaAbertoOuReabertoDoDiaAnteriorQuePrecisaSerFechado.DataHoraCadastro.ToString("dd/MM/yyyy") + " está aberto. Faça o fechamento do caixa para abrir o caixa de hoje.");

                ValidationHelper.Instance.AdicionarItemValidacao("O caixa do dia " + caixaAbertoOuReabertoDoDiaAnteriorQuePrecisaSerFechado.DataHoraCadastro.ToString("dd/MM/yyyy") + " está aberto. Faça o fechamento do caixa para abrir o caixa de hoje.");
            }

            var temCaixaAbertoOuReaberto = Domain.Financeiro.ControleDeCaixaService.CaixaDoOperadorEstaAbertoOuReaberto(pessoaQueRecebeu.IdPessoa, pessoaQueRealizou.IdPessoa, Calendario.Hoje(fuso));
            if (!temCaixaAbertoOuReaberto)
            {
                if (ehImportacao) throw new Exception("Não existe caixa registrado para este profissional nesta data.");

                ValidationHelper.Instance.AdicionarItemValidacao("Não existe caixa registrado para este profissional nesta data.");
            }
        }

        public ResumoDoLinkDTO ObterLinkDePagamentoDoClube(AssinaturaDoCliente assinatura, DateTime dataPagamento)
        {

            ValidarSeEstabelecimentoPossuiLinkHabilitado(assinatura.IdEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var jaPossuiLink = ValidarSeAssinaturaJaPossuiLinkPagamento(assinatura.Id);

            if (jaPossuiLink)
            {
                var idDoLink = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterIdDoLinkDePagamentoPelaAssinatura(assinatura.Id);

                var linkExistente = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idDoLink);

                var resumoDoLink = new ResumoDoLinkDTO
                {
                    IdDoLink = linkExistente.IdLinkDePagamento,
                    Ativo = linkExistente.Ativo,
                    EstaExpirado = linkExistente.DataDeValidade < Calendario.Agora(),
                    Url = linkExistente.ObterUrlDeCompartilhamento(),
                    UrlDePagamento = linkExistente.ObterUrlDePagamento()
                };

                return resumoDoLink;
            }

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoService.ObterClienteEstabelecimentoPorIdPessoa(assinatura.IdPessoaFisicaCliente, assinatura.IdEstabelecimento);
            var linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.GerarLinkDePagamentoRecorrenteParaOClube(clienteEstabelecimento.Codigo, assinatura);

            var link = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(linkNoTrinks.IdLinkDePagamento);

            CriarLinkPagamentoDaAssinatura(assinatura.Id, link.IdLinkDePagamento);

            var retorno = new ResumoDoLinkDTO
            {
                IdDoLink = link.IdLinkDePagamento,
                Ativo = link.Ativo,
                EstaExpirado = link.DataDeValidade < Calendario.Agora(),
                Url = link.ObterUrlDeCompartilhamento(),
                UrlDePagamento = link.ObterUrlDePagamento()
            };

            return retorno;
        }

        public ResumoDoLinkDTO GerarNovoLinkDePagamentoDoClube(AssinaturaDoCliente assinatura)
        {
            DesabilitarLinkDePagamentoDaAssinatura(assinatura.Id);
            ValidarSeEstabelecimentoPossuiLinkHabilitado(assinatura.IdEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoService.ObterClienteEstabelecimentoPorIdPessoa(assinatura.IdPessoaFisicaCliente, assinatura.IdEstabelecimento);
            var linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.GerarLinkDePagamentoRecorrenteParaOClube(clienteEstabelecimento.Codigo, assinatura);

            var link = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(linkNoTrinks.IdLinkDePagamento);

            CriarLinkPagamentoDaAssinatura(assinatura.Id, link.IdLinkDePagamento);

            var retorno = new ResumoDoLinkDTO
            {
                IdDoLink = link.IdLinkDePagamento,
                Ativo = link.Ativo,
                EstaExpirado = link.DataDeValidade < Calendario.Agora(),
                Url = link.ObterUrlDeCompartilhamento(),
                UrlDePagamento = link.ObterUrlDePagamento()
            };

            assinatura.Status = StatusDaAssinaturaEnum.AguardandoPagamento;
            assinatura.DataUltimoPagamento = null;
            assinatura.DataProximoPagamento = null;
            assinatura.VigenciaAtual.FoiPago = false;

            return retorno;
        }

        public void RenovarValidadeDoLinkDePagamento(int idDoLink, int idEstabelecimento)
        {
            var link = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idDoLink);

            var linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.ObterPorIdLinkDePagamento(idDoLink);

            if (linkNoTrinks == null || linkNoTrinks.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento != idEstabelecimento)
                throw new Exception("Operação não permitida");

            if (link == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Esse link não pode ser renovado.");
                return;
            }

            link.RenovarValidadeDoLink();
        }

        public async Task RealizarPagamentoRecorrenteDaAssinaturaAsync(List<AssinaturaDoCliente> assinaturas, DateTime hoje)
        {
            foreach (AssinaturaDoCliente assinatura in assinaturas)
            {
                try
                {
                    await RealizarPagamentoOnlineDaAssinaturaAsync(assinatura, hoje, OrigemDePagamentoEnum.RecorrenciaDaAssinatura);
                }
                catch (Exception ex)
                {
                    LogService<PagamentosService>.Error("[Recorrencia-Clube] - Erro ao realizar pagamento da assinatura " + assinatura.Id + ". Mensagem de erro: " + ex.Formatada());
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                    EnviarEmailSinalizarErroPagamentoRecorrente(assinatura.Id);
                }
            }
        }

        private void EnviarEmailSinalizarErroPagamentoRecorrente(int idAssinatura)
        {

            try
            {
                var assunto = $"[Recorrencia-Clube] - Erro ao realizar pagamento da assinatura {idAssinatura} ";
                var destinatario = Pessoas.Services.EnvioEmailService.ObterEmailErrosPagarme();
                var remetente = Pessoas.Services.EnvioEmailService.ObterRemetentePadrao();



                var paragrafos = new List<string>();

                paragrafos.Add($"Erro ao realizar pagamento da assinatura {idAssinatura}");
                paragrafos.Add($"<b>Para mais informações, visualize o erro no elmah ou peça os logs do Windows Services</b>");

                var corpoEmail = string.Join("<br /><br />", paragrafos);

                Domain.Pessoas.EnvioEmailService.DispararEmail(assunto, corpoEmail, remetente, destinatario, null);

            }
            catch (Exception ex)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Não foi possível enviar o e-mail de erro. Mensagem de erro: " + ex.Formatada());
                return;
            }
        }

        private async Task RealizarPagamentoOnlineDaAssinaturaAsync(AssinaturaDoCliente assinatura, DateTime hoje, OrigemDePagamentoEnum origem)
        {
            var idEstabelecimento = assinatura.IdEstabelecimento;

            var estabelecimentoRecebedor = ObterEstabelecimentoRecebedorAtivoEHabilitadoParaLinkDePagamento(idEstabelecimento);

            if (estabelecimentoRecebedor is null)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Erro ao encontrar estabelecimento recebedor ativo e habilitado no gateway da funcionalidade");
                return;
            }

            ValidarAssinaturas(assinatura, false);

            if (!ValidationHelper.Instance.IsValid)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Erro na validação da assinatura");
                return;
            }

            var idLink = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterIdDoLinkDePagamentoPelaAssinatura(assinatura.Id);
            var link = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idLink);

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var pessoaFisicaCliente = Domain.Pessoas.PessoaFisicaRepository.Load(assinatura.IdPessoaFisicaCliente);
            var tipoFormaDePagamento = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterTipoDeFormaDePagamento(link);

            var idCartaoDoComprador = ObterIdCartaoDoCompradorDoPrimeiroPagamento(idLink);

            if (idCartaoDoComprador <= 0)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Erro ao encontrar cartão do comprador");
                return;
            }

            var resultadoDoPagamento = await RealizarPagamentoNoGateway(assinatura, estabelecimentoRecebedor, idCartaoDoComprador, origem);

            var pagamentoOnline = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarNovoPagamentoOnlineNoTrinks(resultadoDoPagamento.Valor.IdPagamento, resultadoDoPagamento.Valor.ValorPago, idEstabelecimento);

            try
            {
                if (resultadoDoPagamento.Falha)
                {
                    LogService<PagamentosService>.Error("[Recorrencia-Clube] - Falha ao tentar realizar o pagamento no gateway. Assinatura: " + assinatura.Id);
                    throw new Exception("Falha no pagamento");
                }
            }
            catch (Exception ex)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Erro ao realizar pagamento " + ex.Message);
            }

            try
            {
                if (resultadoDoPagamento.Valor.PagamentoFoiRealizado)
                {
                    pagamentoOnline.IndicarQueFoiPagoComSucesso(resultadoDoPagamento.Valor.ValorPago);

                    var pagamentoDaAssinatura = RegistrarPagamento(assinatura, hoje, false, false);

                    var dto = new GerarTransacaoDTO(assinatura, pagamentoDaAssinatura.Id, pessoaFisicaCliente, pagamentoOnline.Taxas.Id);
                    var idAssinatura = await GerarTransacaoFinanceiraComConfirmacaoDoPagamento(dto);

                    var transacao = Domain.Financeiro.TransacaoRepository.Load(idAssinatura);

                    pagamentoOnline.Transacao = transacao;

                    DispararEmailComprovantePagamentoClube(assinatura, pagamentoOnline.DataHoraPagamento, estabelecimento, idCartaoDoComprador, pessoaFisicaCliente.NomeCompleto, tipoFormaDePagamento);
                }
                else
                {
                    pagamentoOnline.IndicarQueTeveProblemasNoPagamento();
                }

                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamentoOnline);
            }
            catch (Exception ex)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Falha no pagamento da assinatura. Assinatura: " + assinatura.Id + ". PagamentoOnline: " + pagamentoOnline.Id);
                throw ex;
            }
        }

        private int ObterIdCartaoDoCompradorDoPrimeiroPagamento(int idLink)
        {
            var idPagamento = Domain.LinksDePagamento.LinkDePagamentoRepository.ObterIdPagamentoDoLink(idLink);

            if (idPagamento <= 0)
                return 0;

            return Domain.Pagamentos.CartaoDeCompradorGatewayRepository.ObterIdCartaoDoCompradorPorIdPagamento(idPagamento);
        }

        private async Task<Perlink.Pagamentos.Gateways.Resultado<NovoPagamentoNoGateway>> RealizarPagamentoNoGateway(AssinaturaDoCliente assinatura, EstabelecimentoRecebedor estabelecimentoRecebedor, int idCartaoDoComprador, OrigemDePagamentoEnum origem)
        {

            var idConta = Domain.Pessoas.ContaRepository.ObterIdContaPorIdPessoa(assinatura.IdPessoaFisicaCliente);
            var splitsDoPagamentoOnline =
                                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.CalcularSplitDoPagamentoOnline(estabelecimentoRecebedor,
                                    idConta, assinatura.ValorAPagar, MetodoDePagamentoNoGatewayEnum.CartaoDeCredito, out _, out _);

            var novoPagamento = new NovoPagamentoDTO
            {
                OrigemDePagamento = origem,
                IdRecebedor = estabelecimentoRecebedor.IdRecebedor,
                IdCartaoDeComprador = idCartaoDoComprador,
                Valor = assinatura.ValorAPagar,
                Itens = new List<ItemNovoPagamentoDTO> { new ItemNovoPagamentoDTO() { Nome = assinatura.Nome, Valor = assinatura.ValorAPagar } },
                RegrasDeSplit = splitsDoPagamentoOnline,
            };

            var resultadoDoPagamento = await Domain.Pagamentos.PagamentosApplicationService.RealizarPagamento(novoPagamento);

            return resultadoDoPagamento;
        }

        private void DispararEmailComprovantePagamentoClube(AssinaturaDoCliente assinatura, DateTime? dataHoraPagamento, Estabelecimento estabelecimento, int idCartaoDoComprador, string nomeCompleto, string tipoFormaDePagamento)
        {
            try
            {
                Domain.LinksDePagamento.EnvioEmailService.EnviarEmailComprovantePagamentoDeClubeDeAssinatura(new EmailComprovantePagamentoDeClubeDeAssinaturaDTO
                {
                    NomeComprador = nomeCompleto,
                    EmailComprador = Domain.Pagamentos.CartaoDeCompradorRepository.ObterEmailPorIdCartaoDeComprador(idCartaoDoComprador),
                    Estabelecimento = estabelecimento,
                    FormaPagamento = tipoFormaDePagamento,
                    NomeAssinatura = assinatura.Nome,
                    ValorAssinatura = assinatura.ValorAPagar,
                    BeneficiosDaAssinatura = Domain.ClubeDeAssinaturas.BeneficioDaAssinaturaRepository.ObterBeneficiosPorIdAssinatura(assinatura.Id),
                    DataHora = dataHoraPagamento ?? throw new Exception("Data invalida"),
                    TextoTituloPagamentoAprovado = "Pagamento",
                    TextoCorpoPagamentoAprovado = $"O pagamento da mensalidade da sua assinatura {assinatura.Nome} foi aprovado.",
                });
            }
            catch (Exception ex)
            {
                LogService<PagamentosService>.Error("[Recorrencia-Clube] - Erro ao enviar email de confirmação de pagamento " + ex.Message);
            }
        }

        public async Task<int> GerarTransacaoFinanceiraComConfirmacaoDoPagamento(GerarTransacaoDTO dto)
        {
            var filtro = ObterFiltroParaTransacao(dto);
            filtro.PessoaQueRealizou = dto.PessoaQueRealizou;

            var idAssinatura = await Domain.Financeiro.TransacaoService.CriarTransacaoParaPagamentosDoClubeAsync(filtro);

            return idAssinatura;
        }

        public void EstornarPagamento(int idTransacao, DateTime dataDoEstorno)
        {
            var idPagamento = Domain.Financeiro.TransacaoRepository.ObterIdPagamentoPeloIdTransacao(idTransacao);

            if (idPagamento == 0) return;

            var pagamento = Domain.ClubeDeAssinaturas.PagamentoDeAssinaturaRepository.Load(idPagamento);
            pagamento.EstornarPagamento(dataDoEstorno);

            Domain.ClubeDeAssinaturas.PagamentoDeAssinaturaRepository.Update(pagamento);
        }

        private bool ValidarSeAssinaturaJaPossuiLinkPagamento(int idAssinatura)
        {
            return Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.VerificarSeAssinaturaJaPossuiLinkPagamento(idAssinatura);
        }

        private void ValidarSeEstabelecimentoPossuiLinkHabilitado(int idEstabelecimento)
        {
            var estabelecimentoPossuiLinkHabilitado =
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.VerificarSeEstabelecimentoUsaFormaPagamento(idEstabelecimento, Financeiro.Enums.FormaPagamentoEnum.PagamentoOnlinePorLink);

            if (!estabelecimentoPossuiLinkHabilitado)
                ValidationHelper.Instance.AdicionarItemValidacao("Esse estabelecimento não possui o link de pagamento habilitado.");
        }

        private void CriarLinkPagamentoDaAssinatura(int idAssinatura, int idLinkDePagamento)
        {
            var linkPagamentoDaAssinatura = new LinkDePagamentoDaAssinatura(idLinkDePagamento, idAssinatura);
            Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.SaveNew(linkPagamentoDaAssinatura);
        }

        private void DesabilitarLinkDePagamentoDaAssinatura(int idAssinatura)
        {
            var linkDaAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterLinkDePagamentoDaAssinaturaPorIdAssinatura(idAssinatura);
            linkDaAssinatura.Desativar();
        }

        private TransicaoParaClubeAssinaturaDTO ObterFiltroParaTransacao(GerarTransacaoDTO dto)
        {

            var assinatura = dto.Assinatura;

            var ehAssinaturaPorLink = dto.IdTaxasPagamentoOnlineTrinks != null ? true : false;

            var pessoaQueRecebeu = Domain.Pessoas.EstabelecimentoRepository.Queryable()
                            .Where(p => p.IdEstabelecimento == assinatura.IdEstabelecimento)
                            .Select(p => p.PessoaJuridica)
                            .FirstOrDefault();

            var pessoaCliente = Domain.Pessoas.PessoaFisicaRepository.Queryable()
                .FirstOrDefault(p => p.IdPessoa == assinatura.IdPessoaFisicaCliente);

            var filtro = new TransicaoParaClubeAssinaturaDTO
            {
                ValorPago = assinatura.ValorAPagar,
                IdObjetoReferencia = dto.IdPagamento,
                Nome = dto.EhTransacaoDeMultaDeCancelamento ? "Multa Cancelamento " + assinatura.Nome : assinatura.Nome,
                PessoaQueRecebeu = pessoaQueRecebeu,
                PessoaQuePagou = pessoaCliente,
                IdEstabelecimento = assinatura.IdEstabelecimento,
                DataDoPagamento = dto.EhTransacaoDeMultaDeCancelamento ? assinatura.MultaDeCancelamentoDaAssinatura.DataPagamento.Value : assinatura.DataUltimoPagamento.Value,
                EhAssinaturaPorLink = ehAssinaturaPorLink,
                EhTransacaoDeMultaDeCancelamento = dto.EhTransacaoDeMultaDeCancelamento,
                Taxas = ObterTaxasParaTransacao(dto.IdTaxasPagamentoOnlineTrinks)
            };

            return filtro;
        }

        private TaxasDTO ObterTaxasParaTransacao(int? idTaxasPagamentoOnlineTrinks)
        {
            return Domain.Financeiro.TransacaoService.ObterTaxasParaTransacao(idTaxasPagamentoOnlineTrinks);
        }

        private EstabelecimentoRecebedor ObterEstabelecimentoRecebedorAtivoEHabilitadoParaLinkDePagamento(int idEstabelecimento)
        {
            var gatewaysParaLinkDePagamento = Domain.Pagamentos.PagamentosService.ListarGatewaysParaLinkDePagamento();

            var recebedores = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                        .ListarAtivosEHabilitadosComGatewayPorEstabelecimento(idEstabelecimento);

            return recebedores
                .Where(r => gatewaysParaLinkDePagamento.Contains(r.Gateway))
                .OrderBy(r => gatewaysParaLinkDePagamento.IndexOf(r.Gateway))
                .Select(r => r.Recebedor)
                .FirstOrDefault();
        }

        public ResumoDoLinkDTO ObterLinkDePagamentoDaMultaDeCancelamentoDoClube(AssinaturaDoCliente assinatura)
        {
            ValidarSeEstabelecimentoPossuiLinkHabilitado(assinatura.IdEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            var jaPossuiLink = ValidarSeAssinaturaJaPossuiLinkPagamentoDaMulta(assinatura.Id);

            if (jaPossuiLink)
            {
                var linkExistente = Domain.ClubeDeAssinaturas.LinkDePagamentoDoCancelamentoDaAssinaturaRepository.ObterLinkDePagamentoPelaAssinatura(assinatura.Id);

                var resumoDoLink = new ResumoDoLinkDTO
                {
                    IdDoLink = linkExistente.IdLinkDePagamento,
                    Ativo = linkExistente.Ativo,
                    EstaExpirado = linkExistente.DataDeValidade < Calendario.Agora(),
                    Url = linkExistente.ObterUrlDeCompartilhamento(),
                    UrlDePagamento = linkExistente.ObterUrlDePagamento()
                };

                return resumoDoLink;
            }

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoService.ObterClienteEstabelecimentoPorIdPessoa(assinatura.IdPessoaFisicaCliente, assinatura.IdEstabelecimento);
            var linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.GerarLinkDePagamentoDaMultaDeCancelamentoDoClubeDeAssinatura(clienteEstabelecimento.Codigo, assinatura);

            var link = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(linkNoTrinks.IdLinkDePagamento);

            CriarLinkDePagamentoDoCancelamentoDaAssinatura(assinatura.Id, link.IdLinkDePagamento);

            var retorno = new ResumoDoLinkDTO
            {
                IdDoLink = link.IdLinkDePagamento,
                Ativo = link.Ativo,
                EstaExpirado = link.DataDeValidade < Calendario.Agora(),
                Url = link.ObterUrlDeCompartilhamento(),
                UrlDePagamento = link.ObterUrlDePagamento()
            };

            return retorno;
        }

        private bool ValidarSeAssinaturaJaPossuiLinkPagamentoDaMulta(int idAssinatura)
        {
            return Domain.ClubeDeAssinaturas.LinkDePagamentoDoCancelamentoDaAssinaturaRepository.VerificarSeAssinaturaJaPossuiLinkPagamentoDaMulta(idAssinatura);
        }

        private void CriarLinkDePagamentoDoCancelamentoDaAssinatura(int idAssinatura, int idLinkDePagamento)
        {
            var linkPagamentoDaAssinatura = new LinkDePagamentoDoCancelamentoDaAssinatura(idLinkDePagamento, idAssinatura);
            Domain.ClubeDeAssinaturas.LinkDePagamentoDoCancelamentoDaAssinaturaRepository.SaveNew(linkPagamentoDaAssinatura);
        }
    }
}