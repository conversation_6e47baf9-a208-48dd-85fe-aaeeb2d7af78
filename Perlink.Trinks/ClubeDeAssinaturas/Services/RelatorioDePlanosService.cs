﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.Text;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using static Perlink.Trinks.ClubeDeAssinaturas.DTO.FiltroPaginacaoPlanoClienteDTO;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class RelatorioDePlanosService : BaseService, IRelatorioDePlanosService
    {
        public ResultadoPlanoClienteDTO ListarPlanos(FiltroPaginacaoPlanoClienteDTO filtro)
        {
            var registros = Filtrar(filtro);
            var configuracoesDeUnidade = ObterConfiguracoesDeUnidades(filtro.IdEstabelecimento);

            var resultado = new ResultadoPlanoClienteDTO
            {
                Registros = registros,
                Paginacao = filtro.Paginacao,
                ConfiguracoesDeUnidades = configuracoesDeUnidade,
            };

            return resultado;
        }

        public PlanoDTO CarregarPlano(int id, int idEstabelecimento)
        {
            var plano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Obter(id, idEstabelecimento);

            if(plano == null)
                throw new Exception("Plano não encontrado");

            var configuracoesDeUnidade = ObterConfiguracoesDeUnidades(idEstabelecimento);

            var vendaOnline = new VendaOnlineDTO()
            {
                ExibeAssinaturaHotsite = plano.ConfiguracaoDeVendaOnline?.ExibirAssinaturaHotsite ?? false,
                HabilitaVendaHotsite = plano.ConfiguracaoDeVendaOnline?.HabilitaVendaHotsite ?? false,
                TermosDeUsoClube = plano.ConfiguracaoDeVendaOnline?.TermosDeUso
            };

            var contratoDeAdesao = new ContratoDeAdesaoDTO(){TermosDoContrato = plano.ContratosDeAdesao.OrderByDescending(x => x.Versao).FirstOrDefault()?.TermosDoContrato};
            var planoPossuiAssinaturaAtiva = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.PlanoPossuiAssinatura(plano.Id);
            var planoCongelado =  Domain.ClubeDeAssinaturas.IntencaoEdicaoDoPlanoClienteRepository.PlanoCongeladoParaEdicao(plano.Id);

            var planoDTO = new PlanoDTO
            {
                Id = id,
                Nome = plano.DadosDoPlanoCliente.Nome,
                Descricao = plano.DadosDoPlanoCliente.Descricao,
                Valor = planoCongelado != null ? planoCongelado.DadosDoPlanoCliente.Valor : plano.DadosDoPlanoCliente.Valor,
                EncerraAposXCobrancas = planoCongelado != null ? planoCongelado.DadosDoPlanoCliente.EncerraAposXCobrancas : plano.DadosDoPlanoCliente.EncerraAposXCobrancas,
                CicloDeCobranca = planoCongelado != null ? planoCongelado.DadosDoPlanoCliente.CicloDeCobranca : plano.DadosDoPlanoCliente.CicloDeCobranca,
                Ativo = plano.Ativo,
                ConfiguracoesDeUnidades = configuracoesDeUnidade,
                EhAssinaturaDoModelo = plano.PlanoClienteModelo != null,
                PermitiConsumoAteFimPeriodoPago = planoCongelado != null ? planoCongelado.DadosDoPlanoCliente.PermitiConsumoAteFimPeriodoPago : plano.DadosDoPlanoCliente.PermitiConsumoAteFimPeriodoPago,
                VendaOnline = vendaOnline,
                ContratoDeAdesao = contratoDeAdesao,
                PlanoPossuiAssinaturaAtiva = planoPossuiAssinaturaAtiva,
                PlanoEstaCongelado = planoCongelado?.DisponivelParaEdicao??false,
                DataDeDescongelamento = planoCongelado?.DataParaEdicao
            };

            foreach (var beneficioPlano in planoCongelado != null ? planoCongelado.BeneficiosDoPlano : plano.BeneficiosDoPlano)
            {

                if (beneficioPlano.Ativo)
                {
                    bool naoEhBeneficioDoModelo = beneficioPlano.BeneficioDoPlanoModelo == null;
                    var beneficio = naoEhBeneficioDoModelo ? beneficioPlano.Beneficio : beneficioPlano.BeneficioDoPlanoModelo.Beneficio;

                    var beneficioDTO = new BeneficioDoPlanoDTO(beneficioPlano.Id, beneficio);

                    planoDTO.BeneficiosDoPlano.Add(beneficioDTO);
                }

            }

            return planoDTO;
        }

        public MemoryStream GerarCsvPlanos(List<PlanoClienteDTO> listaPlanos, FiltroPaginacaoPlanoClienteDTO filtro)
        {
            var estabelecimentoBaseadoEmModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.EstabelecimentoEhFranquiaBaseadoEmModelo(filtro.IdEstabelecimento);

            Csv csv = new Csv();

            if (listaPlanos != null && listaPlanos.Any())
            {
                foreach (var item in filtro.ResumosFiltro)
                {
                    csv.AdicionarLinha(item);
                }
                csv.AdicionarLinha("");

                var cabecalho = new Csv.Linha();
                cabecalho.AdicionarCelula("Nome da assinatura");
                cabecalho.AdicionarCelula("Data de criação");
                cabecalho.AdicionarCelula("Itens da assinatura");
                cabecalho.AdicionarCelula("Preço da assinatura por mês");
                cabecalho.AdicionarCelula("Cobrança encerra após");
                cabecalho.AdicionarCelula("Status");
                csv.AdicionarLinha(cabecalho);

                foreach (var p in listaPlanos)
                {
                    var linha = new Csv.Linha();

                    linha.AdicionarCelula($"{p.Nome} {(p.EhAssinaturaDoModelo && estabelecimentoBaseadoEmModelo ? "(modelo)" : "")}");
                    linha.AdicionarCelula(p.DataCriacao.ToShortDateString());

                    var itens = string.Join(", ", p.Beneficios);
                    linha.AdicionarCelula(itens);

                    linha.AdicionarCelula($"R$ {p.ValorTotal}");

                    linha.AdicionarCelula(p.EncerraAposXCobrancas > 0 ? $"{p.EncerraAposXCobrancas.Value.ToString()} {p.CicloDeCobranca.ObterNome(p.EncerraAposXCobrancas.Value)}" : "Não encerra");
                    linha.AdicionarCelula(p.Ativo ? "Ativo" : "Inativo");

                    csv.AdicionarLinha(linha);
                }
            }

            return csv.ObterArquivo();
        }

        private PermissaoParaClubeDeAssinaturasDTO ObterConfiguracoesDeUnidades(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (!estabelecimento.EstabelecimentoEhBaseadoEmModeloENaoEhModelo())
                return null;

            var idEstabelecimentoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdDoModeloDoEstabelecimento(idEstabelecimento);
            var configuracoes = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterConfiguracoesDeClubeDaUnidade(idEstabelecimento, idEstabelecimentoModelo);

            return configuracoes;
        }

        private List<PlanoClienteDTO> Filtrar(FiltroPaginacaoPlanoClienteDTO filtro)
        {
            var query = ObterFiltrada(filtro);

            filtro.Paginacao.TotalItens = query.Select(q => q.Id).Distinct().Count();

            query = AplicarOrdenacao(query, filtro);

            if (filtro.AplicarPaginacao)
                query = AplicarPaginacao(query, filtro.Paginacao);

            return ProjetarPlanoComBeneficios(query);
        }

        private List<PlanoClienteDTO> ProjetarPlanoComBeneficios(IQueryable<PlanoCliente> query)
        {
            var registros = query.Select(p =>
                new PlanoClienteDTO
                {
                    Id = p.Id,
                    Nome = p.DadosDoPlanoCliente.Nome,
                    DataCriacao = p.DadosDoPlanoCliente.DataDaCriacao,
                    EhAssinaturaDoModelo = p.PlanoClienteModelo != null,
                    ValorTotal = p.DadosDoPlanoCliente.Valor,
                    EncerraAposXCobrancas = p.DadosDoPlanoCliente.EncerraAposXCobrancas,
                    CicloDeCobranca = p.DadosDoPlanoCliente.CicloDeCobranca,
                    Ativo = p.Ativo,
                    ExibirAssinaturaHotsite = p.ConfiguracaoDeVendaOnline != null && p.ConfiguracaoDeVendaOnline.ExibirAssinaturaHotsite,
                    HabilitaVendaHotsite = p.ConfiguracaoDeVendaOnline != null && p.ConfiguracaoDeVendaOnline.HabilitaVendaHotsite
                }).ToList();

            if (registros != null && registros.Count > 0)
                AplicarDadosDosBeneficios(registros);

            return registros;
        }

        private void AplicarDadosDosBeneficios(List<PlanoClienteDTO> registros)
        {
            var beneficioProdutoQuery = Domain.ClubeDeAssinaturas.BeneficioProdutoRepository.Queryable();
            var beneficioServicoQuery = Domain.ClubeDeAssinaturas.BeneficioServicoRepository.Queryable();
            var beneficiosDoPlanoQuery = Domain.ClubeDeAssinaturas.BeneficioDoPlanoRepository.Queryable();

            foreach (var registro in registros)
            {

                var beneficiosProduto = (from bp in beneficiosDoPlanoQuery
                                         join bProd in beneficioProdutoQuery on bp.Beneficio.Id equals bProd.Id
                                         where bp.Plano.Id == registro.Id && bp.Ativo
                                         select bProd.EstabelecimentoProduto.Descricao)
                                        .ToList() ?? new List<string>();

                var beneficiosServico = (from bp in beneficiosDoPlanoQuery
                                         join bServ in beneficioServicoQuery on bp.Beneficio.Id equals bServ.Id
                                         where bp.Plano.Id == registro.Id && bp.Ativo
                                         select bServ.ServicoEstabelecimento.Nome)
                                         .ToList() ?? new List<string>();

                var itensBeneficio = beneficiosProduto.Union(beneficiosServico);

                registro.Beneficios = itensBeneficio.ToList();
            }
        }

        public DetalhesDoPlanoDTO ObterDetalheDoPlano(int idPlano, int idEstabelecimeto)
        {
            var hoje = Calendario.Hoje();
            var plano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Queryable()
                .Where(p => p.Id == idPlano && p.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimeto)
                .Select(p => new DetalhesDoPlanoDTO
                {
                    Id = p.Id,
                    Nome = p.DadosDoPlanoCliente.Nome,
                    Descricao = p.DadosDoPlanoCliente.Descricao,
                    Valor = p.DadosDoPlanoCliente.Valor,
                    EncerraAposXCobrancas = p.DadosDoPlanoCliente.EncerraAposXCobrancas,
                    CicloDeCobranca = p.DadosDoPlanoCliente.CicloDeCobranca,
                    DataEncerramento = Calculos.CalculosDeVigencias.ObterDataDeEncerramento(hoje, p.DadosDoPlanoCliente.CicloDeCobranca, p.DadosDoPlanoCliente.EncerraAposXCobrancas),
                    PossuiContratoDeAdesao = p.ContratosDeAdesao.Any()
                }).FirstOrDefault();

            ObterDetalhesDosBeneficios(plano);

            return plano;
        }

        public bool EstabelecimentoPossuiPlano(int idEstabelecimento)
        {
            var estabelecimentoPossuiPlano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Queryable()
                .Where(p => p.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento)
                .Any();

            return estabelecimentoPossuiPlano;
        }

        private void ObterDetalhesDosBeneficios(DetalhesDoPlanoDTO plano)
        {
            var beneficioProdutoQuery = Domain.ClubeDeAssinaturas.BeneficioProdutoRepository.Queryable();
            var beneficioServicoQuery = Domain.ClubeDeAssinaturas.BeneficioServicoRepository.Queryable();
            var beneficiosDoPlanoQuery = Domain.ClubeDeAssinaturas.BeneficioDoPlanoRepository.Queryable();

            var beneficiosProduto = (from bp in beneficiosDoPlanoQuery
                                     join bProd in beneficioProdutoQuery on bp.Beneficio.Id equals bProd.Id
                                     where bp.Plano.Id == plano.Id && bp.Ativo
                                     select new DetalhesDoBeneficio
                                     {
                                         Item = bProd.EstabelecimentoProduto.Descricao,
                                         Quantidade = bProd.ConsumoLimitado ? bProd.ObterNomeParaQuantidadePorPeriodo() : "Ilimitado",
                                         ValorUnitario = bProd.ValorUnitario,
                                         TipoDoItem = "Produto",
                                         CicloDeConsumo = bProd.CicloDeConsumo,
                                         PrazoParaConsumo = bProd.PrazoParaConsumo
                                     }).ToList();

            var beneficiosServico = (from bp in beneficiosDoPlanoQuery
                                     join bServ in beneficioServicoQuery on bp.Beneficio.Id equals bServ.Id
                                     where bp.Plano.Id == plano.Id && bp.Ativo
                                     select new DetalhesDoBeneficio
                                     {
                                         Item = bServ.ServicoEstabelecimento.Nome,
                                         Quantidade = bServ.ConsumoLimitado ? bServ.ObterNomeParaQuantidadePorPeriodo() : "Ilimitado",
                                         ValorUnitario = bServ.ValorUnitario,
                                         TipoDoItem = "Serviço",
                                         CicloDeConsumo = bServ.CicloDeConsumo,
                                         PrazoParaConsumo = bServ.PrazoParaConsumo
                                     }).ToList();


            var itensBeneficio = beneficiosProduto.Union(beneficiosServico);

            plano.Beneficios = itensBeneficio.ToList();
        }

        private IQueryable<PlanoCliente> ObterFiltrada(FiltroPaginacaoPlanoClienteDTO filtro)
        {
            var query = Domain.ClubeDeAssinaturas.PlanoClienteRepository.Queryable()
                        .Where(p => p.DadosDoPlanoCliente.IdEstabelecimento == filtro.IdEstabelecimento);

            switch (filtro.Status)
            {
                case StatusDoPlano.Ativo:
                    query = query.Where(q => q.Ativo);
                    break;
                case StatusDoPlano.Inativo:
                    query = query.Where(q => !q.Ativo);
                    break;
            }

            if (filtro.IdsPlanoCliente != null && filtro.IdsPlanoCliente.Count > 0)
                query = query.Where(q => filtro.IdsPlanoCliente.Contains(q.Id));

            if (!string.IsNullOrWhiteSpace(filtro.TextoBusca) && filtro.TipoBusca == FiltroPaginacaoPlanoClienteDTO.ParametroDeBuscaPlano.Nome)
                query = query.Where(q => q.DadosDoPlanoCliente.Nome.Contains(filtro.TextoBusca));

            if (filtro.TipoItemSincronia == TipoItemSincroniaEnum.ApenasAssinaturasDentroDoModelo)
                query = query.Where(q => q.PlanoClienteModelo != null);

            if (filtro.TipoItemSincronia == TipoItemSincroniaEnum.ApenasAssinaturasForaDoModelo)
                query = query.Where(q => q.PlanoClienteModelo == null);

            switch (filtro.VendaOnline)
            {
                case OpcoesVendaOnline.Habilitada:
                    query = query.Where(q => q.ConfiguracaoDeVendaOnline != null && q.ConfiguracaoDeVendaOnline.HabilitaVendaHotsite);
                    break;
                case OpcoesVendaOnline.SomenteExibicao:
                    query = query.Where(q => q.ConfiguracaoDeVendaOnline != null &&
                                              q.ConfiguracaoDeVendaOnline.ExibirAssinaturaHotsite && !q.ConfiguracaoDeVendaOnline.HabilitaVendaHotsite);
                    break;
                case OpcoesVendaOnline.NãoHabilitada:
                    query = query.Where(q => q.ConfiguracaoDeVendaOnline == null ||
                                              (!q.ConfiguracaoDeVendaOnline.ExibirAssinaturaHotsite && !q.ConfiguracaoDeVendaOnline.HabilitaVendaHotsite));
                    break;
            }

            //if (!string.IsNullOrWhiteSpace(filtro.TextoBusca) && filtro.TipoBusca == ParametroDeBuscaPlano.Descricao)
            //    query = query.Where(q => q.Descricao.Contains(filtro.TextoBusca));

            return query;
        }

        private IQueryable<PlanoCliente> AplicarOrdenacao(IQueryable<PlanoCliente> query, FiltroPaginacaoPlanoClienteDTO filtro)
        {
            switch (filtro.OrdenarPor)
            {
                case FiltroPaginacaoPlanoClienteDTO.OrdenarPlano.AssinaturasMaisRecente:
                    query = query.OrderByDescending(o => o.DadosDoPlanoCliente.DataDaCriacao);
                    break;
                case FiltroPaginacaoPlanoClienteDTO.OrdenarPlano.Nome:
                    query = query.OrderBy(o => o.DadosDoPlanoCliente.Nome);
                    break;
                case FiltroPaginacaoPlanoClienteDTO.OrdenarPlano.Status:
                    query = query.OrderByDescending(o => o.Ativo);
                    break;
                case FiltroPaginacaoPlanoClienteDTO.OrdenarPlano.VendaOnline:
                    query = query.OrderByDescending(q => q.ConfiguracaoDeVendaOnline.HabilitaVendaHotsite);
                    break;
            }

            return query;
        }

        private IQueryable<PlanoCliente> AplicarPaginacao(IQueryable<PlanoCliente> query, Shared.NHibernate.Paginacao.ParametrosPaginacao paginacao)
        {
            return query.Skip(paginacao.RegistroInicial - 1).Take(paginacao.RegistrosPorPagina);
        }
    }
}