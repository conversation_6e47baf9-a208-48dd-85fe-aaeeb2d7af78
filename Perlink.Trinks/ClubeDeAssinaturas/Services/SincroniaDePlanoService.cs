﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public class SincroniaDePlanoService : BaseService, ISincroniaDePlanoService
    {

        public void SincronizarPlanosDoClubeDeAssinaturaComModelo(IEnumerable<PlanoCliente> planosClienteModeloNaoSincronizados, int idEstabelecimentoDestino)
        {
            Domain.ClubeDeAssinaturas.MontagemDePlanoService.ConfigurarEstabelecimentoParaUtilizarClubeSeNecessario(idEstabelecimentoDestino);

            var idEstabelecimentoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdDoModeloDoEstabelecimento(idEstabelecimentoDestino);

            var permissaoClube = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterConfiguracoesDeClubeDaUnidade(idEstabelecimentoDestino, idEstabelecimentoModelo);

            var unidadeTemPermissaoDeCriarNovosPlanosDoClube = permissaoClube != null ? permissaoClube.CriarEditarInativarDaUnidade : false;

            var planosClienteDoEstabelecimentoDestino = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterPlanos(idEstabelecimentoDestino);

            foreach (var planoClienteDoModeloParaSerSincronizado in planosClienteModeloNaoSincronizados)
                GerarPlanoClienteComBeneficios(idEstabelecimentoDestino, planosClienteDoEstabelecimentoDestino, planoClienteDoModeloParaSerSincronizado, permissaoClube);

            if (!unidadeTemPermissaoDeCriarNovosPlanosDoClube)
            {
                foreach (var planoCliente in planosClienteDoEstabelecimentoDestino.Where(f => f.PlanoClienteModelo == null))
                    planoCliente.InativarPlano();
            }

            Domain.ClubeDeAssinaturas.PlanoClienteRepository.Flush();
        }

        private void GerarPlanoClienteComBeneficios(int idEstabelecimentoDestino, List<PlanoCliente> planosClienteDoEstabelecimentoDestino, PlanoCliente planoClienteDoModeloParaSerSincronizado, PermissaoParaClubeDeAssinaturasDTO permissoes)
        {
            var planoClienteDoEstabelecimentoDestino = planosClienteDoEstabelecimentoDestino
                .FirstOrDefault(f => f.PlanoClienteModelo != null && f.PlanoClienteModelo.Id == planoClienteDoModeloParaSerSincronizado.Id);

            if (planoClienteDoEstabelecimentoDestino == null)
                planoClienteDoEstabelecimentoDestino = new PlanoCliente(idEstabelecimentoDestino, planoClienteDoModeloParaSerSincronizado, permissoes);
            else
                planoClienteDoEstabelecimentoDestino.DadosDoPlanoCliente.Sincronizar(planoClienteDoModeloParaSerSincronizado, permissoes, idEstabelecimentoDestino);

            var beneficiosDoPlanoDestino = planoClienteDoEstabelecimentoDestino.BeneficiosDoPlano
                .Where(p => p.Ativo);

            foreach (var beneficioDoPlanoDestino in beneficiosDoPlanoDestino)
                beneficioDoPlanoDestino.Inativar();

            foreach (var beneficioDoPlanoModelo in planoClienteDoModeloParaSerSincronizado.BeneficiosDoPlano)
                SincronizarBeneficioDoPlano(idEstabelecimentoDestino, planoClienteDoEstabelecimentoDestino, beneficioDoPlanoModelo);

            if (planoClienteDoEstabelecimentoDestino.Id <= 0)
                Domain.ClubeDeAssinaturas.PlanoClienteRepository.SaveNewNoFlush(planoClienteDoEstabelecimentoDestino);
            else
                Domain.ClubeDeAssinaturas.PlanoClienteRepository.UpdateNoFlush(planoClienteDoEstabelecimentoDestino);
        }

        private void SincronizarBeneficioDoPlano(int idEstabelecimentoDestino, PlanoCliente planoClienteDoEstabelecimentoDestino, BeneficioDoPlano modelo)
        {
            Beneficio beneficio = CriarBeneficio(idEstabelecimentoDestino, modelo);
            planoClienteDoEstabelecimentoDestino.SincronizarBeneficio(beneficio, modelo);
        }

        private Beneficio CriarBeneficio(int idEstabelecimentoDestino, BeneficioDoPlano modelo)
        {
            Beneficio beneficio = null;

            if (modelo.Beneficio == TipoDeBeneficioEnum.Produto)
            {
                var estabelecimentoProdutoDestino = Domain.Pessoas.EstabelecimentoProdutoRepository
                    .ObterBaseadoNoModelo(idEstabelecimentoDestino, modelo.Beneficio.ObterIdDoItem());

                beneficio = new BeneficioProduto
                {
                    EstabelecimentoProduto = estabelecimentoProdutoDestino
                };

            }
            else if (modelo.Beneficio == TipoDeBeneficioEnum.Servico)
            {
                var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository
                    .ObterBaseadoNoModelo(idEstabelecimentoDestino, modelo.Beneficio.ObterIdDoItem());

                beneficio = new BeneficioServico
                {
                    ServicoEstabelecimento = servicoEstabelecimento
                };
            }

            beneficio.Sincronizar(modelo.Beneficio);

            return beneficio;
        }
    }
}
