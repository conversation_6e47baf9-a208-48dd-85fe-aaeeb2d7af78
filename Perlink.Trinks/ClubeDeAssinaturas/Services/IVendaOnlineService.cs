﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.Pessoas;
using System;
using System.Threading.Tasks;

namespace Perlink.Trinks.ClubeDeAssinaturas.Services
{
    public interface IVendaOnlineService : IService
    {
        int? AssinarPlanoDeAssinaturaPeloHotsite(int idPessoa, int idPlanoAssinatura, int idEstabelecimento);
        DadosDoClubeDeAssinaturaParaVendaHotsiteDTO ObterPlanoParaExibicaoNoHotsite(int idPlano, int idEstabelecimento);
        ResultadoDadosDoClubeDeAssinaturaParaNoVendaHotsiteDto ObterPlanosVisiveisNoHotsite(FiltroPaginacaoPlanoClienteDTO filtro);
        ResumoDoLinkDTO GerarLinkDePagamentoParaVendaDeClubeDeAssinaturaNoHotSite(AssinaturaDoCliente assinatura);
        Task<string> AssinarContratoClubeDeAssinaturaERedirecionarCliente(Guid identificadorAssinatura, PessoaFisica pessoaFisicaAutenticada);
        string ObterUrlDeRedirecionamentoDoClienteAposAssinaturaDoPlano(int idAssinatura, int idEstabelecimento);
    }
}