﻿using Castle.ActiveRecord;
using DocumentFormat.OpenXml.Wordprocessing;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Perlink.Trinks.PropertyNames.ClubeDeAssinaturas;

namespace Perlink.Trinks.ClubeDeAssinaturas
{
    public class DadosDoPlanoCliente
    {
        [Property("id_estabelecimento")]
        public virtual int IdEstabelecimento { get; set; }

        [Property("nome")]
        public virtual string Nome { get; set; }

        [Property("descricao")]
        public virtual string Descricao { get; set; }

        [Property("data_da_criacao")]
        public virtual DateTime DataDaCriacao { get; set; }

        [Property("permiti_consumo_ate_fim_periodo_pago")]
        public virtual bool PermitiConsumoAteFimPeriodoPago { get; set; }

        [Property("valor")]
        public virtual decimal Valor { get; set; }

        [Property("cobranca_ciclo_periodo")]
        public virtual PeriodoDoCicloEnum CicloDeCobranca { get; set; }

        [Property("encerra_apos_x_cobrancas")]
        public virtual int? EncerraAposXCobrancas { get; set; }

        public DadosDoPlanoCliente()
        {
            DataDaCriacao = Calendario.Agora();
        }

        public DadosDoPlanoCliente(PlanoDTO dados)
        {
            IdEstabelecimento = dados.IdEstabelecimento;
            Nome = dados.Nome;
            Descricao = dados.Descricao;
            Valor = dados.Valor;
            PermitiConsumoAteFimPeriodoPago = dados.PermitiConsumoAteFimPeriodoPago;
            CicloDeCobranca = dados.CicloDeCobranca;
            EncerraAposXCobrancas = dados.EncerraAposXCobrancas;
            DataDaCriacao = Calendario.Agora();
        }

        public virtual void Editar(PlanoDTO dados)
        {
            Nome = dados.Nome;
            Descricao = dados.Descricao;
        }

        public virtual void EditarDadosSensiveis(IntencaoEdicaoDoPlanoCliente dados)
        {
            Valor = dados.DadosDoPlanoCliente.Valor;
            PermitiConsumoAteFimPeriodoPago = dados.DadosDoPlanoCliente.PermitiConsumoAteFimPeriodoPago;
            CicloDeCobranca = dados.DadosDoPlanoCliente.CicloDeCobranca;
            EncerraAposXCobrancas = dados.DadosDoPlanoCliente.EncerraAposXCobrancas;
        }

        public virtual void Sincronizar(PlanoCliente planoClienteModelo, PermissaoParaClubeDeAssinaturasDTO permissoes, int idEstabelecimento)
        {
            var permissaoPrecoPeriodoDoModelo = permissoes != null ? permissoes.PrecoPeriodoEncerramentoDoModelo : false;

            Nome = planoClienteModelo.DadosDoPlanoCliente.Nome;
            Descricao = planoClienteModelo.DadosDoPlanoCliente.Descricao;
            IdEstabelecimento = idEstabelecimento;

            if (!permissaoPrecoPeriodoDoModelo)
            {
                Valor = planoClienteModelo.DadosDoPlanoCliente.Valor;
                CicloDeCobranca = planoClienteModelo.DadosDoPlanoCliente.CicloDeCobranca;
                EncerraAposXCobrancas = planoClienteModelo.DadosDoPlanoCliente.EncerraAposXCobrancas;
            }
        }
    }
}
