﻿using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using System;

namespace Perlink.Trinks.ClubeDeAssinaturas.DTO
{
    public class AssinaturaClienteDTO
    {
        public int IdAssinaturaCliente { get; set; }
        public int IdPessoaCliente { get; set; }
        public string NomeCliente { get; set; }
        public string NomeDaAssinatura { get; set; }
        public StatusDaAssinaturaEnum StatusId { get; set; }
        public string StatusNome { get; set; }
        public DateTime? DataProximoPagamento { get; set; }
        public DateTime? DataCobrancaEncerra { get; set; }
        public DateTime DataAssinatura { get; set; }
        public CanalDeVendaEnum CanalDeVenda { get; set; }
        public decimal Preco { get; set; }
        public bool PodeConfirmar { get; set; }
        public bool PodeCancelar { get; set; }
        public DateTime? FimDaVigencia { get; set; }
        public bool PossuiLinkDePagamento { get; set; }
        public bool PermitiConsumoAteFimPeriodoPago { get; set; }
        public bool FoiPagoComLink { get; set; }
    }
}