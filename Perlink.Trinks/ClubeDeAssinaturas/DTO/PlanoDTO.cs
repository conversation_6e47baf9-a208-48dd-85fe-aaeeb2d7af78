﻿using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.ClubeDeAssinaturas.DTO
{
    public class PlanoDTO
    {
        public int Id { get; set; }
        public string Nome { get; set; }
        public string Descricao { get; set; }
        public decimal Valor { get; set; }
        public bool Ativo { get; set; }
        public bool PermitiConsumoAteFimPeriodoPago { get; set; }
        public bool EhAssinaturaDoModelo { get; set; }
        public List<BeneficioDoPlanoDTO> BeneficiosDoPlano { get; set; } = new List<BeneficioDoPlanoDTO>();
        public int? EncerraAposXCobrancas { get; set; }
        public PeriodoDoCicloEnum CicloDeCobranca { get; set; }
        public PermissaoParaClubeDeAssinaturasDTO ConfiguracoesDeUnidades { get; set; }
        public VendaOnlineDTO VendaOnline { get; set; }
        public int IdEstabelecimento { get; set; }
        public ContratoDeAdesaoDTO ContratoDeAdesao { get; set; }
        public bool PlanoPossuiAssinaturaAtiva { get; set; }
        public bool PlanoEstaCongelado { get; set; }
        public DateTime? DataDeDescongelamento { get; set; }
    }
}
