﻿using Castle.ActiveRecord;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;

namespace Perlink.Trinks.ClubeDeAssinaturas
{

    [ActiveRecord("PLCLI_Contrato_De_Adesao", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public class ContratoDeAdesao
    {
        [PrimaryKey(PrimaryKeyType.Native, "id_contrato_de_adesao")]
        public virtual int Id { get; set; }

        [Property("termos_do_contrato", ColumnType = "StringClob", SqlType = "nvarchar(MAX)")]
        public virtual string TermosDoContrato { get; set; }

        [Property("versao")]
        public virtual int Versao { get; set; }

        [BelongsTo("id_plano_cliente", NotNull = true)]
        public virtual PlanoCliente PlanoCliente { get; set; }

        public ContratoDeAdesao() { }

        public ContratoDeAdesao(ContratoDeAdesaoDTO dados, PlanoCliente planoCliente)
        {
            PlanoCliente = planoCliente;
            TermosDoContrato = dados.TermosDoContrato;
            Versao = dados.Versao;
        }
    }
}
