﻿using Castle.ActiveRecord;
using NHibernate.Util;
using Perlink.Shared.Auditing;
using Perlink.Trinks.ClubeDeAssinaturas.Calculos;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas
{
    [ActiveRecord("PLCLI_Assinatura_Cliente", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public class AssinaturaDoCliente
    {
        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("id_estabelecimento")]
        public virtual int IdEstabelecimento { get; set; }

        [Property("id_pessoa_fisica_cliente")]
        public virtual int IdPessoaFisicaCliente { get; set; }

        [Property("nome")]
        public virtual string Nome { get; set; }

        [Property("descricao")]
        public virtual string Descricao { get; set; }

        [BelongsTo("id_plano", NotNull = true, Lazy = FetchWhen.OnInvoke)]
        public virtual PlanoCliente PlanoCliente { get; set; }

        [Property("status")]
        public virtual StatusDaAssinaturaEnum Status { get; set; }

        [Property("permiti_consumo_ate_fim_periodo_pago")]
        public virtual bool PermitiConsumoAteFimPeriodoPago { get; set; }

        [Property("pode_consumir_itens")]
        public virtual bool PodeConsumirItens { get; set; }

        [Property("valor_a_pagar")]
        public virtual decimal ValorAPagar { get; set; }

        [Property("encerra_apos_x_cobrancas")]
        public virtual int? EncerraAposXCobrancas { get; set; }

        [Property("qtd_vigencia_paga")]
        public virtual int? QuantidadeDeVigenciasPagas { get; set; }

        [Property("cobranca_ciclo_periodo")]
        public virtual PeriodoDoCicloEnum CicloDeCobranca { get; set; }

        [Property("dt_assinatura")]
        public virtual DateTime DataAssinatura { get; set; }

        [Property("canal_venda")]
        public virtual CanalDeVendaEnum CanalDeVenda { get; set; }

        [Property("dt_programada_encerramento")]
        public virtual DateTime? DataProgramadaEncerramento { get; set; }

        [Property("dt_ultimo_pagamento")]
        public virtual DateTime? DataUltimoPagamento { get; set; }

        [Property("dt_proximo_pagamento")]
        public virtual DateTime? DataProximoPagamento { get; set; }

        [BelongsTo("id_vigencia_atual", Cascade = CascadeEnum.SaveUpdate)]
        public virtual VigenciaDeAssinatura VigenciaAtual { get; set; }

        [Property("dt_cancelamento")]
        public virtual DateTime? DataCancelamento { get; set; }

        [Property("id_pessoa_que_cancelou")]
        public virtual int? IdPessoaQueCancelou { get; set; }

        [Property("dia_vencimento")]
        public virtual int DiaVencimento { get; set; }

        [HasMany(ColumnKey = "id_assinatura", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true, Lazy = true)]
        public virtual IList<BeneficioDaAssinatura> BeneficiosDaAssinatura { get; set; }

        [Property("identificador_assinatura")]
        public virtual Guid IdentificadorAssinatura { get; set; }

        [Property("metodo_cobranca")]
        public virtual MetodoCobrancaEnum MetodoCobranca { get; set; }

        [BelongsTo("id_multa_de_cancelamento", Cascade = CascadeEnum.SaveUpdate)]
        public virtual PagamentoMultaDeCancelamentoDaAssinatura MultaDeCancelamentoDaAssinatura { get; set; }

        [Property("email_contrato_de_adesao_enviado")]
        public virtual bool EmailContratoDeAdesaoEnviado { get; set; }

        [Property("email_aviso_edicao_enviado")]
        public virtual bool EmailAvisoEdicaoEnviado { get; set; }

        [Property("dt_assinatura_contrato")]
        public virtual DateTime? DataAssinaturaContrato { get; set; }

        [HasMany(ColumnKey = "id_assinatura_cliente", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true)]
        public virtual IList<HistoricoDeStatusAssinaturaDoClube> HistoricoDeStatusDaAssinatura { get; set; }

        public AssinaturaDoCliente()
        {
            BeneficiosDaAssinatura = new List<BeneficioDaAssinatura>();
            HistoricoDeStatusDaAssinatura = new List<HistoricoDeStatusAssinaturaDoClube>();
        }

        public AssinaturaDoCliente(PlanoCliente planoCliente, int idPessoaCliente, DateTime agora, CanalDeVendaEnum canalDeVenda, MetodoCobrancaEnum metodoCobrancaEnum) : this()
        {
            var status = ClienteDeveAssinarContratoDeAdesao(planoCliente, metodoCobrancaEnum) 
                ? StatusDaAssinaturaEnum.ContratoPendente : StatusDaAssinaturaEnum.AguardandoPagamento;

            IdEstabelecimento = planoCliente.DadosDoPlanoCliente.IdEstabelecimento;
            IdPessoaFisicaCliente = idPessoaCliente;
            PlanoCliente = planoCliente;
            Nome = planoCliente.DadosDoPlanoCliente.Nome;
            Descricao = planoCliente.DadosDoPlanoCliente.Descricao;
            TratarStatusDaAssinatura(status);
            PodeConsumirItens = false;
            PermitiConsumoAteFimPeriodoPago = planoCliente.DadosDoPlanoCliente.PermitiConsumoAteFimPeriodoPago;
            ValorAPagar = planoCliente.DadosDoPlanoCliente.Valor;
            CicloDeCobranca = planoCliente.DadosDoPlanoCliente.CicloDeCobranca;
            DataAssinatura = agora;
            CanalDeVenda = canalDeVenda;
            DiaVencimento = agora.Day;
            EncerraAposXCobrancas = planoCliente.DadosDoPlanoCliente.EncerraAposXCobrancas;
            QuantidadeDeVigenciasPagas = ObterValorInicialDaQuantidadeDeVigenciasPagas(EncerraAposXCobrancas);
            IdentificadorAssinatura = Guid.NewGuid();
            MetodoCobranca = metodoCobrancaEnum;
            EmailContratoDeAdesaoEnviado = false;
            EmailAvisoEdicaoEnviado = true;

            DataProgramadaEncerramento = CalculosDeVigencias
                .ObterDataDeEncerramento(DataAssinatura, CicloDeCobranca, planoCliente.DadosDoPlanoCliente.EncerraAposXCobrancas);

            CopiarBeneficiosDoPlano();
        }

        #region Métodos públicos
        public virtual void ConfirmarPagamento(DateTime dataUltimoPagamento)
        {
            AtualizarQuantidadeDeVigenciasPagas();
            var cobrancaEstaAtiva = CobrancaDaAssinaturaEstaAtiva();

            if (DataUltimoPagamento != null)
            {
                if (cobrancaEstaAtiva)
                {
                    DataProximoPagamento = ObterDataProximoPagamento();
                }
                else
                {
                    DataProximoPagamento = null;
                }
            }

            TratarStatusDaAssinatura(StatusDaAssinaturaEnum.Ativa);
            PodeConsumirItens = true;
            DataUltimoPagamento = dataUltimoPagamento;
        }

        public virtual void AtualizarDataProximoPagamento()
        {
            DataProximoPagamento = ObterDataProximoPagamento();
        }

        public virtual bool CobrancaDaAssinaturaEstaAtiva()
        {
            var ehAssinaturaPorTempoLimitado = EhAssinaturaLimitada();
            var quantidadeDeVigenciasPagasEhMenorQueONumeroDeCobrancaTotal = ehAssinaturaPorTempoLimitado && QuantidadeDeVigenciasPagas < EncerraAposXCobrancas;
            return !ehAssinaturaPorTempoLimitado || quantidadeDeVigenciasPagasEhMenorQueONumeroDeCobrancaTotal;
        }

        public virtual void SuspenderPorFaltaDePagamento(DateTime dataEncerramento)
        {
            TratarStatusDaAssinatura(StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento);
            PodeConsumirItens = false;
            DataCancelamento = Calendario.Agora();
            DataProximoPagamento = null;
        }

        public virtual void RegistrarPagamentoEmAtraso()
        {
            TratarStatusDaAssinatura(StatusDaAssinaturaEnum.Atrasada);
            PodeConsumirItens = false;
        }

        public virtual void EditarDadosSensiveis(PlanoCliente dados)
        {
            ValorAPagar = dados.DadosDoPlanoCliente.Valor;
            PermitiConsumoAteFimPeriodoPago = dados.DadosDoPlanoCliente.PermitiConsumoAteFimPeriodoPago;
            CicloDeCobranca = dados.DadosDoPlanoCliente.CicloDeCobranca;
            EncerraAposXCobrancas = dados.DadosDoPlanoCliente.EncerraAposXCobrancas;
            BeneficiosDaAssinatura.Clear();
            CopiarBeneficiosDoPlano();
        }

        public virtual void Cancelar(int idPessoaQueCancelou)
        {
            if (AssinaturaDeveSerCanceladaSemPeriodoDeConsumo())
            {
                CancelarAssinatura(idPessoaQueCancelou);
                return;
            }

            CancelarAssinaturaEmPeriodoConsumo(idPessoaQueCancelou);
        }

        private bool AssinaturaDeveSerCanceladaSemPeriodoDeConsumo()
        {
            if (MetodoCobranca == MetodoCobrancaEnum.Manual &&
                    Status == StatusDaAssinaturaEnum.Ativa && 
                    !PermitiConsumoAteFimPeriodoPago)
                return true;

            if (MetodoCobranca == MetodoCobrancaEnum.LinkDePagamento &&
                Status == StatusDaAssinaturaEnum.Ativa &&
                PermitiConsumoAteFimPeriodoPago)
                return false;

            return (Status == StatusDaAssinaturaEnum.ContratoPendente ||
                            Status == StatusDaAssinaturaEnum.AguardandoPagamento ||
                            !PermitiConsumoAteFimPeriodoPago) &&
                            (VigenciaAtual == null || !VigenciaAtual.FoiPago);
        }

        public virtual void SinalizarFimPeriodoConsumoAssinaturaCancelada()
        {
            TratarStatusDaAssinatura(StatusDaAssinaturaEnum.Cancelada);
            PodeConsumirItens = false;
        }

        public virtual void EncerrarAssinatura()
        {
            TratarStatusDaAssinatura(StatusDaAssinaturaEnum.Encerrada);
            PodeConsumirItens = false;
            DataProximoPagamento = null;
        }

        public virtual void AlterarAssinaturaParaContratoPendente()
        {
            if (Status != StatusDaAssinaturaEnum.ContratoPendente)
                TratarStatusDaAssinatura(StatusDaAssinaturaEnum.ContratoPendente);

            EmailContratoDeAdesaoEnviado = false;
        }

        public virtual void AtualizarVigencia(DateTime dataUltimaVigencia)
        {
            var cobrancaEstaAtiva = CobrancaDaAssinaturaEstaAtiva();

            if (!cobrancaEstaAtiva)
            {
                DataProximoPagamento = null;
            }
            else
            {
                var primeiroDiaProximaVigencia = ObterPrimeiroDiaProximaVigencia(dataUltimaVigencia);
                CriarNovaVigencia(primeiroDiaProximaVigencia);
            }
        }

        public virtual void CriarNovaVigencia(DateTime primeiroDiaDaNovaVigencia)
        {
            var diaInicialPrimeiraVigencia = ObterDiaInicialDaPrimeiraVigencia();
            var novaVigencia = CalculosDeVigencias.ObterVigenciaAtual(primeiroDiaDaNovaVigencia, CicloDeCobranca);
            var ultimoDiaDoMes = new DateTime(primeiroDiaDaNovaVigencia.Year, primeiroDiaDaNovaVigencia.Month, DateTime.DaysInMonth(primeiroDiaDaNovaVigencia.Year, primeiroDiaDaNovaVigencia.Month));

            if (novaVigencia.Inicio.Day != primeiroDiaDaNovaVigencia.Day)
            {
                DiaVencimento = novaVigencia.Inicio.Day;

                if (primeiroDiaDaNovaVigencia.Day != diaInicialPrimeiraVigencia)
                {
                    DataProximoPagamento = ultimoDiaDoMes;
                    novaVigencia.Fim = novaVigencia.Fim.AddDays(diaInicialPrimeiraVigencia - primeiroDiaDaNovaVigencia.Day);
                }

                else DataProximoPagamento = novaVigencia.Inicio.AddMonths(1);

                PodeConsumirItens = false;
            }

            else if (VigenciaAtual == null)
            {
                DiaVencimento = primeiroDiaDaNovaVigencia.Day;
                DataProximoPagamento = primeiroDiaDaNovaVigencia.AddMonths(1);
            }

            else
            {

                if (primeiroDiaDaNovaVigencia.Day != diaInicialPrimeiraVigencia)
                    novaVigencia.Fim = novaVigencia.Fim.AddDays(diaInicialPrimeiraVigencia - primeiroDiaDaNovaVigencia.Day);

                DataProximoPagamento = novaVigencia.Inicio;
                PodeConsumirItens = false;
            }

            VigenciaAtual = new VigenciaDeAssinatura(this, novaVigencia);
        }

        public virtual void Renovar(DateTime dataUltimaVigencia, bool pagamentoEstaEncerrado)
        {
            AtualizarVigencia(dataUltimaVigencia);

            foreach (var beneficio in BeneficiosDaAssinatura)
            {
                if (beneficio.Beneficio.CicloDeConsumo != 0 && !pagamentoEstaEncerrado)
                {
                    beneficio.ResetarConsumo();
                }
            }
        }

        public virtual bool PodeConfirmar()
        {
            var podeConfirmarManual = Status == StatusDaAssinaturaEnum.Ativa
                                                || Status == StatusDaAssinaturaEnum.Atrasada
                                                || Status == StatusDaAssinaturaEnum.AguardandoPagamento
                                                || Status == StatusDaAssinaturaEnum.ContratoPendente;

            var podeConfirmarLink = Status == StatusDaAssinaturaEnum.Ativa
                                              || Status == StatusDaAssinaturaEnum.Atrasada
                                              || Status == StatusDaAssinaturaEnum.ContratoPendente
                                              || Status == StatusDaAssinaturaEnum.AguardandoPagamento;

            return MetodoCobranca == MetodoCobrancaEnum.Manual ? podeConfirmarManual : podeConfirmarLink;
        }

        public virtual bool PodeCancelar()
        {
            return Status == StatusDaAssinaturaEnum.Ativa
                || Status == StatusDaAssinaturaEnum.Atrasada;
        }

        public virtual bool EhAssinaturaLimitada()
        {
            return EncerraAposXCobrancas.HasValue;
        }

        public virtual bool EstaAguardandoPagamentoDaMulta()
        {
            return MultaDeCancelamentoDaAssinatura != null && MultaDeCancelamentoDaAssinatura.MetodoDeCobranca == MetodoCobrancaEnum.LinkDePagamento;
        }

        public virtual bool PossuiLinkDePagamento()
        {
            return Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.VerificarSeAssinaturaJaPossuiLinkPagamento(Id);
        }

        public virtual void TratarStatusDaAssinatura(StatusDaAssinaturaEnum novoStatus)
        {
            try
            {
                Status = novoStatus;
                if (HistoricoDeStatusDaAssinatura == null)
                {
                    HistoricoDeStatusDaAssinatura = new List<HistoricoDeStatusAssinaturaDoClube>();
                }

                var statusAnterior = HistoricoDeStatusDaAssinatura.FirstOrDefault(x => x.Ativo);
                if (statusAnterior != null)
                {
                    statusAnterior.Desabilitar();
                }

                HistoricoDeStatusDaAssinatura.Add(new HistoricoDeStatusAssinaturaDoClube(novoStatus, this));
            }
            catch (Exception ex)
            {
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                LogService<AssinaturaDoCliente>.Error($"[Assinatura do cliente] - Erro ao alterar status da assinatura {Id} para o status: {novoStatus}.");
            }
        }

        #endregion Métodos públicos

        #region Métodos privados
        private bool ClienteDeveAssinarContratoDeAdesao(PlanoCliente planoCliente, MetodoCobrancaEnum metodoCobrancaEnum)
        {
            return planoCliente.ContratosDeAdesao != null && planoCliente.ContratosDeAdesao.Any()
                && metodoCobrancaEnum == MetodoCobrancaEnum.LinkDePagamento;
        }

        private void CancelarAssinatura(int idPessoaQueCancelou)
        {
            TratarStatusDaAssinatura(EstaAguardandoPagamentoDaMulta() ? StatusDaAssinaturaEnum.AguardandoPagamentoDaMultaDeCancelamento : StatusDaAssinaturaEnum.Cancelada);
            DataProximoPagamento = null;
            DataCancelamento = Calendario.Agora();
            IdPessoaQueCancelou = idPessoaQueCancelou;
            PodeConsumirItens = false;
        }

        private void CancelarAssinaturaEmPeriodoConsumo(int idPessoaQueCancelou)
        {
            var podeConsumir = VigenciaAtual != null && VigenciaAtual.Periodo.Fim >= Calendario.Agora();

            TratarStatusDaAssinatura(EstaAguardandoPagamentoDaMulta() ? StatusDaAssinaturaEnum.AguardandoPagamentoDaMultaDeCancelamento : StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo);
            DataProximoPagamento = null;
            DataCancelamento = Calendario.Agora();
            IdPessoaQueCancelou = idPessoaQueCancelou;
            PodeConsumirItens = podeConsumir;
        }

        private int ObterDiaInicialDaPrimeiraVigencia()
        {
            return Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.ObterDiaInicialDaPrimeiraVigencia(Id);
        }

        private DateTime ObterPrimeiroDiaProximaVigencia(DateTime dataUltimaVigencia)
        {
            var mesesParaProximaCobranca = CicloDeCobranca.ObterMeses();
            var diaInicialPrimeiraVigencia = ObterDiaInicialDaPrimeiraVigencia();

            if (dataUltimaVigencia.Day != diaInicialPrimeiraVigencia)
                return dataUltimaVigencia.AddMonths(mesesParaProximaCobranca).AddDays(diaInicialPrimeiraVigencia - dataUltimaVigencia.Day);

            else
                return dataUltimaVigencia.AddMonths(mesesParaProximaCobranca);
        }

        private DateTime ObterDataProximoPagamento()
        {
            var mesesParaProximaCobranca = CicloDeCobranca.ObterMeses();
            var diaInicialPrimeiraVigencia = ObterDiaInicialDaPrimeiraVigencia();
            var vigenciaAtual = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterVigenciaAtualDaAssinatura(Id);

            //Não é possivel utilizar a propriedade VigenciaAtual dessa entidade.
            //Carregar a vigencia atual via query evita erros de proxy ao acessar diretamente a propriedade.
            //Necessário para contexto de Windows Service.

            if (DataProximoPagamento.HasValue)
            {
                if (diaInicialPrimeiraVigencia != vigenciaAtual.Periodo.Inicio.Day)
                    return vigenciaAtual.Periodo.Fim.AddDays(1);

                else
                    return DataProximoPagamento.Value.AddMonths(mesesParaProximaCobranca);
            }

            return vigenciaAtual.DataPrevistaPagamento.AddMonths(mesesParaProximaCobranca);
        }

        private void AtualizarQuantidadeDeVigenciasPagas()
        {
            var ehAssinaturaPorTempoLimitado = EhAssinaturaLimitada();

            if (ehAssinaturaPorTempoLimitado)
            {
                QuantidadeDeVigenciasPagas += 1;
            }
        }

        private int? ObterValorInicialDaQuantidadeDeVigenciasPagas(int? encerraAposXCobrancas)
        {
            if (encerraAposXCobrancas.HasValue)
            {
                return 0;
            }

            return null;
        }

        private void CopiarBeneficiosDoPlano()
        {
            foreach (var beneficioDoPlano in PlanoCliente.ListarBeneficiosAtivos())
            {
                BeneficiosDaAssinatura.Add(new BeneficioDaAssinatura(this, beneficioDoPlano.Beneficio));
            }
        }

        #endregion Métodos privados
    }
}