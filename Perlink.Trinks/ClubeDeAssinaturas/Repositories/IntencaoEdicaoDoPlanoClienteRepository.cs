﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories
{
    public partial class IntencaoEdicaoDoPlanoClienteRepository : IIntencaoEdicaoDoPlanoClienteRepository
    {
        public IList<IntencaoEdicaoDoPlanoCliente> BuscarPlanosComIntencaoDeEdicao(DateTime hoje)
        {
            return Queryable()
                .Where(a => a.DisponivelParaEdicao && a.DataParaEdicao.Date <= hoje.Date)
                .ToList();
        }

        public IntencaoEdicaoDoPlanoCliente PlanoCongeladoParaEdicao(int idPlano)
        {
            return Queryable()
                .FirstOrDefault(a => a.PlanoCliente.Id == idPlano && a.DisponivelParaEdicao);
        }
    }
}
