﻿using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using System.Collections.Generic;
using System.Linq;
using static Perlink.Trinks.ClubeDeAssinaturas.DTO.FiltroPaginacaoPlanoClienteDTO;

namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories
{
    public partial class PlanoClienteRepository : IPlanoClienteRepository
    {
        public PlanoCliente Obter(int id, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(p => p.Id == id && p.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento);
        }

        public PlanoCliente ObterPlanoAtivoPorNome(string nomePlano, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(p => p.DadosDoPlanoCliente.Nome.ToLower() == nomePlano.ToLower() && p.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento && p.Ativo);
        }

        public bool EstabelecimentoPossuiPlanoAtivo(int idEstabelecimento)
        {
            return Queryable().Where(p => p.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento && p.Ativo).Count() > 0;
        }

        public List<PlanoCliente> ObterPlanosAtivos(int idEstabelecimento)
        {
            return Queryable().Where(e => e.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento && e.Ativo).ToList();
        }

        public List<PlanoCliente> ObterPlanos(int idEstabelecimento)
        {
            return Queryable().Where(e => e.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento).ToList();
        }

        public List<PlanosDoEstabelecimentoParaVenda> ObterListaDePlanosAtivosParaVenda(int idEstabelecimento)
        {
            return Domain.ClubeDeAssinaturas.PlanoClienteRepository.Queryable()
                .Where(p => p.DadosDoPlanoCliente.IdEstabelecimento == idEstabelecimento && p.Ativo)
                .Select(p => new PlanosDoEstabelecimentoParaVenda
                {
                    IdPlano = p.Id,
                    NomeDoPlano = p.DadosDoPlanoCliente.Nome
                }).ToList();
        }

        public IQueryable<PlanoCliente> ListarPlanosFiltrados(FiltroPaginacaoPlanoClienteDTO filtro)
        {
            var query = Queryable().Where(p => p.DadosDoPlanoCliente.IdEstabelecimento == filtro.IdEstabelecimento);

            if (filtro.Status == FiltroPaginacaoPlanoClienteDTO.StatusDoPlano.Ativo)
                query = query.Where(q => q.Ativo);

            if (filtro.IdsPlanoCliente?.Any() == true)
                query = query.Where(q => filtro.IdsPlanoCliente.Contains(q.Id));

            if (!string.IsNullOrWhiteSpace(filtro.TextoBusca) && filtro.TipoBusca == FiltroPaginacaoPlanoClienteDTO.ParametroDeBuscaPlano.Nome)
                query = query.Where(q => q.DadosDoPlanoCliente.Nome.Contains(filtro.TextoBusca));

            if (filtro.TipoItemSincronia == TipoItemSincroniaEnum.ApenasAssinaturasDentroDoModelo)
                query = query.Where(q => q.PlanoClienteModelo != null);

            if (filtro.TipoItemSincronia == TipoItemSincroniaEnum.ApenasAssinaturasForaDoModelo)
                query = query.Where(q => q.PlanoClienteModelo == null);

            if (filtro.SomentePlanosExibidosNoHotsite)
                query = query.Where(q => q.ConfiguracaoDeVendaOnline != null && q.ConfiguracaoDeVendaOnline.ExibirAssinaturaHotsite);

            return query;
        }
    }
}