﻿using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories
{
    public partial class BeneficioDaAssinaturaRepository : IBeneficioDaAssinaturaRepository
    {

        public BeneficioDaAssinatura ObterPorId(int id)
        {
            return Queryable().FirstOrDefault(b => b.Id == id);
        }

        public bool PossuiBeneficioComOServico(Int32 idServicoEstabelecimento)
        {
            var beneficiosServicos = Domain.ClubeDeAssinaturas.BeneficioServicoRepository.Queryable();
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Queryable();

            var existeBeneficio = from ba in Queryable()
                                  join bs in beneficiosServicos
                                  on ba.Beneficio.Id equals bs.Id
                                  join ass in assinaturaCliente
                                  on ba.Assinatura.Id equals ass.Id
                                  where bs.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento
                                  && (ass.Status == StatusDaAssinaturaEnum.Ativa || ass.Status == StatusDaAssinaturaEnum.Atrasada)
                                  && ba.Ativo
                                  select ba;

            return existeBeneficio.Any();
        }

        public bool PossuiBeneficioComServicosSelecionados(ICollection<int> idsServicoEstabelecimento)
        {
            var beneficiosServicos = Domain.ClubeDeAssinaturas.BeneficioServicoRepository.Queryable();
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Queryable();

            var existeBeneficio = from ba in Queryable()
                                  join bs in beneficiosServicos
                                  on ba.Beneficio.Id equals bs.Id
                                  join ass in assinaturaCliente
                                  on ba.Assinatura.Id equals ass.Id
                                  where idsServicoEstabelecimento.Contains(bs.ServicoEstabelecimento.IdServicoEstabelecimento)
                                  && (ass.Status == StatusDaAssinaturaEnum.Ativa || ass.Status == StatusDaAssinaturaEnum.Atrasada)
                                  && ba.Ativo
                                  select ba;

            return existeBeneficio.Any();
        }

        public bool PossuiBeneficioComProdutosSelecionados(ICollection<int> idsProdutoEstabelecimento)
        {
            var beneficiosProdutos = Domain.ClubeDeAssinaturas.BeneficioProdutoRepository.Queryable();
            var assinaturaCliente = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Queryable();

            var existeBeneficio = from ba in Queryable()
                                  join bp in beneficiosProdutos
                                  on ba.Beneficio.Id equals bp.Id
                                  join ass in assinaturaCliente
                                  on ba.Assinatura.Id equals ass.Id
                                  where idsProdutoEstabelecimento.Contains(bp.EstabelecimentoProduto.Id)
                                  && (ass.Status == StatusDaAssinaturaEnum.Ativa || ass.Status == StatusDaAssinaturaEnum.Atrasada)
                                  && ba.Ativo
                                  select ba;

            return existeBeneficio.Any();
        }

        public bool BeneficioEstaEmUso(int idBeneficio)
        {
            return Queryable().Where(b => b.Beneficio.Id == idBeneficio).Count() > 0;
        }

        public List<BeneficioDaAssinatura> ObterBeneficiosPorIdAssinatura(int idAssinatura)
        {
            return Queryable().Where(b=> b.Assinatura.Id == idAssinatura).ToList();
        }
    }
}
