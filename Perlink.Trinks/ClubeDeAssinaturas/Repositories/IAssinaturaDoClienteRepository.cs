﻿using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories
{
    public partial interface IAssinaturaDoClienteRepository
    {
        AssinaturaDoCliente ObterPorIdAssinaturaEIdEstabelecimento(int idAssinaturaCliente, int idEstabelecimento);
        AssinaturaDoCliente ObterPorIdAssinatura(int idAssinatura);
        List<AssinaturaDoCliente> ListarAssinaturasAtrasadasQueNaoForamPagasHaXDias(DateTime data);
        List<AssinaturaDoCliente> ListarAssinaturasCanceladasComConsumoEForaDaVigencia(DateTime data);
        List<AssinaturaDoCliente> ListarAssinaturasQueRenovaramSemPagarVigenciaPassada(DateTime data);
        List<AssinaturaDoCliente> ListarAssinaturasAtivasQueTerminaramVigenciaAtual(DateTime data);
        IQueryable<AssinaturaClienteDTO> ObterQueryDTOParaRelatoriosDaAssinaturaCliente(FiltroAssinaturaClienteDTO filtro);
        AssinaturaDoCliente ObterAssinaturaDoClienteQuePodeConsumirItens(int idPessoaDoCliente, string nomeDoPlano);
        List<KeyValuePair<int, string>> ListarDisponiveisParaConsumoPorCliente(int idEstabelecimento, int idPessoa);
        bool ClienteTemAlgumPacoteDeAssinaturaAtivo(int idEstabelecimento, int idPessoaFisica);
        bool PossuiAssinaturaJaPaga(int idEstabelecimento, int idPessoaFisica);
        StatusDaAssinaturaEnum ObterStatusDaAssinaturaDoCliente(int idEstabelecimento, int idPessoaFisica);
        List<AssinaturaDoCliente> ListarAssinaturasComLinkParaPagamento(DateTime hoje);
        int ObterIdAssinaturaPeloIdTransacao(int idTransacao);
        List<AssinaturaDoCliente> ListarTodasAssinaturasDoCliente(int idPessoaFisica);
        bool EstabelecimentoPossuiAssinaturaPorLinkAtiva(int idEstabelecimento);
        VigenciaDeAssinatura ObterVigenciaAtualDaAssinatura(int idAssinatura);
        List<int> ListarAssinaturasAtivasDoEstabelecimento(int idEstabelecimento);
        bool PlanoPossuiAssinatura(int idPlano);
        List<AssinaturaDoCliente> ListarAssinaturasDisponiveisPorPlano(int idPlano);
        List<AssinaturaDoCliente> ListarAssinaturasComLinkDisponiveisPorPlano(int idPlano);
        List<AssinaturaDoCliente> ListarAssinaturasParaReceberEmailDoContratoDeAdesao();
        AssinaturaDoCliente ObterPeloIdentificadorEPeloIdEstabelecimento(Guid identificadorAssinatura, int idEstabelecimento);
        AssinaturaDoCliente ObterPeloIdentificador(Guid identificadorAssinatura);
        List<AssinaturaDoCliente> ListarAssinaturasParaReceberEmailDaEdicaoDoClube();
        bool VerificarSePlanoPossuiAssinaturasParaRegularizar(int idPlano, int idEstabelecimento);
        bool ClientePossuiAssinaturaAdimplente(int idEstabelecimento, int idPessoaFisica);
        IQueryable<ClienteComAssinaturaParaRegularizarDTO> ObterClientesComAssinaturaParaRegularizar(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro);
    }
}