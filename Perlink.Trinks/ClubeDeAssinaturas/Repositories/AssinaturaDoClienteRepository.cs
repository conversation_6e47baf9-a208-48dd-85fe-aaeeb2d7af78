﻿using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories
{
    public partial class AssinaturaDoClienteRepository : IAssinaturaDoClienteRepository
    {
        public AssinaturaDoCliente ObterPorIdAssinaturaEIdEstabelecimento(int idAssinaturaCliente, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(a => a.Id == idAssinaturaCliente && a.IdEstabelecimento == idEstabelecimento);
        }

        public AssinaturaDoCliente ObterPorIdAssinatura(int idAssinatura)
        {
            return Queryable().FirstOrDefault(a => a.Id.Equals(idAssinatura));
        }

        public List<AssinaturaDoCliente> ListarAssinaturasAtrasadasQueNaoForamPagasHaXDias(DateTime data)
        {
            return Queryable()
                .Where(x => x.Status == StatusDaAssinaturaEnum.Atrasada
                       && x.VigenciaAtual.DataPrevistaPagamento < data
                       && x.VigenciaAtual.Pagamento == null
                       && !x.VigenciaAtual.FoiPago)
                .ToList();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasCanceladasComConsumoEForaDaVigencia(DateTime data)
        {
            return Queryable()
                .Where(x => x.Status == StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo
                       && x.PodeConsumirItens
                       && x.VigenciaAtual.Periodo.Fim < data
                       && x.VigenciaAtual.FoiPago
                       && x.VigenciaAtual.Pagamento != null)
                .ToList();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasQueRenovaramSemPagarVigenciaPassada(DateTime data)
        {
            return Queryable()
               .Where(x => x.Status == StatusDaAssinaturaEnum.Ativa
                       && x.VigenciaAtual.DataPrevistaPagamento < data
                       && !x.VigenciaAtual.FoiPago
                       && x.VigenciaAtual.Pagamento == null)
               .ToList();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasAtivasQueTerminaramVigenciaAtual(DateTime data)
        {
            return Queryable()
                .Where(p => p.Status != StatusDaAssinaturaEnum.Cancelada
                         && p.Status != StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo
                         && p.Status != StatusDaAssinaturaEnum.Encerrada
                         && p.Status != StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento
                         && p.VigenciaAtual.Periodo.Fim < data)
                .ToList();
        }

        public IQueryable<AssinaturaClienteDTO> ObterQueryDTOParaRelatoriosDaAssinaturaCliente(FiltroAssinaturaClienteDTO filtro)
        {
            var queryClientePessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var query = Queryable().Where(p => p.IdEstabelecimento == filtro.IdEstabelecimento);

            var queryRetorno = (from adc in query
                                join clpf in queryClientePessoaFisica
                                on adc.IdPessoaFisicaCliente equals clpf.IdPessoa
                                select new AssinaturaClienteDTO
                                {
                                    IdAssinaturaCliente = adc.Id,
                                    IdPessoaCliente = clpf.IdPessoa,
                                    NomeCliente = clpf.NomeCompleto,
                                    NomeDaAssinatura = adc.Nome,
                                    StatusNome = adc.Status.ObterNome(),
                                    StatusId = adc.Status,
                                    DataProximoPagamento = adc.DataProximoPagamento.Value,
                                    DataCobrancaEncerra = adc.DataProgramadaEncerramento,
                                    DataAssinatura = adc.DataAssinatura,
                                    CanalDeVenda = adc.CanalDeVenda,
                                    Preco = adc.ValorAPagar,
                                    PodeConfirmar = adc.PodeConfirmar(),
                                    PodeCancelar = adc.PodeCancelar(),
                                    FimDaVigencia = adc.VigenciaAtual.Periodo.Fim,
                                    PossuiLinkDePagamento = adc.PossuiLinkDePagamento(),
                                    PermitiConsumoAteFimPeriodoPago = adc.PermitiConsumoAteFimPeriodoPago,
                                    FoiPagoComLink = adc.MetodoCobranca == MetodoCobrancaEnum.LinkDePagamento
                                });

            return queryRetorno;
        }

        public AssinaturaDoCliente ObterAssinaturaDoClienteQuePodeConsumirItens(int idPessoaDoCliente, string nomeDoPlano)
        {
            return Queryable()
                .Where(
                    ass => ass.IdPessoaFisicaCliente == idPessoaDoCliente
                         && ass.Nome.ToLower() == nomeDoPlano.ToLower()
                         && ass.PodeConsumirItens
                )
                .FirstOrDefault();
        }

        public List<KeyValuePair<int, string>> ListarDisponiveisParaConsumoPorCliente(int idEstabelecimento, int idPessoaCliente)
        {
            return Queryable()
                .Where(a => a.IdEstabelecimento == idEstabelecimento && a.PodeConsumirItens && a.Status != StatusDaAssinaturaEnum.ContratoPendente)
                .Where(a => a.IdPessoaFisicaCliente == idPessoaCliente)
                .Select(a => new KeyValuePair<int, string>(a.Id, a.Nome))
                .ToList();
        }

        public bool ClienteTemAlgumPacoteDeAssinaturaAtivo(int idEstabelecimento, int idPessoaFisica)
        {
            return Queryable().Any(f => f.IdPessoaFisicaCliente.Equals(idPessoaFisica) && f.IdEstabelecimento.Equals(idEstabelecimento) && f.PodeConsumirItens);
        }

        public bool PossuiAssinaturaJaPaga(int idEstabelecimento, int idPessoaFisica)
        {
            return Queryable()
                .Any(a => a.IdEstabelecimento.Equals(idEstabelecimento)
                && a.IdPessoaFisicaCliente.Equals(idPessoaFisica)
                && a.DataUltimoPagamento != null);
        }

        public StatusDaAssinaturaEnum ObterStatusDaAssinaturaDoCliente(int idEstabelecimento, int idPessoaFisica)
        {
            return Queryable()
                .Where(a => a.IdEstabelecimento.Equals(idEstabelecimento) && a.IdPessoaFisicaCliente.Equals(idPessoaFisica))
                .Where(a => a.Status != StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento && a.Status != StatusDaAssinaturaEnum.AguardandoPagamento)
                .OrderByDescending(a => a.DataAssinatura)
                .Select(a => a.Status)
                .FirstOrDefault();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasComLinkParaPagamento(DateTime hoje)
        {
            var assinaturas = Queryable();
            var linkDeAssinaturas = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.Queryable();

            return (from ass in assinaturas
                    join la in linkDeAssinaturas on ass.Id equals la.IdAssinaturaDoCliente
                    where ass.VigenciaAtual.Periodo.Inicio < hoje.AddDays(1)
                    && !ass.VigenciaAtual.FoiPago
                    && (ass.Status == StatusDaAssinaturaEnum.Ativa || ass.Status == StatusDaAssinaturaEnum.Atrasada)
                    && la.Ativo
                    select ass).ToList();
        }

        public int ObterIdAssinaturaPeloIdTransacao(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Queryable();
            var transacaoItem = Domain.Financeiro.TransacaoItemRepository.Queryable();
            var pagamentoAssinatura = Domain.ClubeDeAssinaturas.PagamentoDeAssinaturaRepository.Queryable();

            return (from tran in transacao
                    join tItem in transacaoItem on tran.Id equals tItem.Transacao.Id
                    join pgass in pagamentoAssinatura on tItem.IdObjetoReferencia equals pgass.Id
                    where tran.Id == idTransacao
                    select pgass.Assinatura.Id)
                    .FirstOrDefault();
        }

        public List<AssinaturaDoCliente> ListarTodasAssinaturasDoCliente(int idPessoaFisica)
        {
            return Queryable()
                .Where(a => a.IdPessoaFisicaCliente.Equals(idPessoaFisica))
                .ToList();
        }

        public bool EstabelecimentoPossuiAssinaturaPorLinkAtiva(int idEstabelecimento)
        {
            var assinaturas = Queryable().Where(a => a.IdEstabelecimento == idEstabelecimento);

            var linkDePagamentoDaAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.Queryable();

            return (from ass in assinaturas
                    join link in linkDePagamentoDaAssinatura
                    on ass.Id equals link.IdAssinaturaDoCliente
                    where ass.VigenciaAtual != null
                       && (ass.Status == StatusDaAssinaturaEnum.Ativa || ass.Status == StatusDaAssinaturaEnum.Atrasada)
                    select ass)
                    .Any();
        }

        public VigenciaDeAssinatura ObterVigenciaAtualDaAssinatura(int idAssinatura)
        {
            return Queryable().Where(ass => ass.Id.Equals(idAssinatura)).Select(ass => ass.VigenciaAtual).FirstOrDefault();
        }

        public List<int> ListarAssinaturasAtivasDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable()
                .Where(p => p.Status != StatusDaAssinaturaEnum.Cancelada
                         && p.Status != StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo
                         && p.Status != StatusDaAssinaturaEnum.Encerrada
                         && p.Status != StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento
                         && p.Status != StatusDaAssinaturaEnum.AguardandoPagamentoDaMultaDeCancelamento
                         && p.IdEstabelecimento == idEstabelecimento)
                .Select(p => p.Id)
                .ToList();
        }

        public bool PlanoPossuiAssinatura(int idPlano)
        {
            return Queryable()
                .Any(p => p.PlanoCliente.Id == idPlano);
        }

        public List<AssinaturaDoCliente> ListarAssinaturasDisponiveisPorPlano(int idPlano)
        {
            return Queryable()
                .Where(p => p.Status != StatusDaAssinaturaEnum.Cancelada
                         && p.Status != StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo
                         && p.Status != StatusDaAssinaturaEnum.Encerrada
                         && p.Status != StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento
                         && p.Status != StatusDaAssinaturaEnum.AguardandoPagamentoDaMultaDeCancelamento
                         && p.PlanoCliente.Id == idPlano)
                .ToList();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasComLinkDisponiveisPorPlano(int idPlano)
        {
            return Queryable()
                .Where(p => p.Status != StatusDaAssinaturaEnum.Cancelada
                         && p.Status != StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo
                         && p.Status != StatusDaAssinaturaEnum.Encerrada
                         && p.Status != StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento
                         && p.Status != StatusDaAssinaturaEnum.AguardandoPagamentoDaMultaDeCancelamento
                         && p.MetodoCobranca == MetodoCobrancaEnum.LinkDePagamento
                         && p.PlanoCliente.Id == idPlano)
                .ToList();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasParaReceberEmailDoContratoDeAdesao()
        {
            var pessoasFisicas = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var query = from a in Queryable()
                        join pf in pessoasFisicas
                            on a.IdPessoaFisicaCliente equals pf.IdPessoa
                        where a.Status == StatusDaAssinaturaEnum.ContratoPendente
                              && !a.EmailContratoDeAdesaoEnviado
                              && pf.Email != null 
                              && pf.Email.Trim() != ""
                        select a;

            return query.ToList();
        }

        public AssinaturaDoCliente ObterPeloIdentificadorEPeloIdEstabelecimento(Guid identificadorAssinatura, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(ac => ac.IdentificadorAssinatura == identificadorAssinatura && ac.IdEstabelecimento == idEstabelecimento);
        }

        public AssinaturaDoCliente ObterPeloIdentificador(Guid identificadorAssinatura)
        {
            return Queryable().FirstOrDefault(ac => ac.IdentificadorAssinatura == identificadorAssinatura);
        }

        public bool VerificarSePlanoPossuiAssinaturasParaRegularizar(int idPlano, int idEstabelecimento)
        {
            var pessoasFisicas = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var query = from a in Queryable()
                        join pf in pessoasFisicas on a.IdPessoaFisicaCliente equals pf.IdPessoa
                        where a.PlanoCliente.Id == idPlano
                              && a.IdEstabelecimento == idEstabelecimento
                              && (a.Status == StatusDaAssinaturaEnum.ContratoPendente
                              || pf.Email == null || pf.Email.Trim() == "")
                        select a;

            return query.Any();
        }

        public List<AssinaturaDoCliente> ListarAssinaturasParaReceberEmailDaEdicaoDoClube()
        {
            var pessoasFisicas = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var statusInvalidos = new[]
            {
                StatusDaAssinaturaEnum.Cancelada,
                StatusDaAssinaturaEnum.CanceladaEmPeriodoDeConsumo,
                StatusDaAssinaturaEnum.Encerrada,
                StatusDaAssinaturaEnum.SuspensaPorFaltaDePagamento,
                StatusDaAssinaturaEnum.AguardandoPagamentoDaMultaDeCancelamento
            };

            var query = from a in Queryable()
                        join pf in pessoasFisicas
                            on a.IdPessoaFisicaCliente equals pf.IdPessoa
                        where !statusInvalidos.Contains(a.Status)
                              && !a.EmailAvisoEdicaoEnviado
                              && pf.Email != null
                              && pf.Email.Trim() != ""
                        select a;

            return query.ToList();
        }

        public bool ClientePossuiAssinaturaAdimplente(int idEstabelecimento, int idPessoaFisica)
        {
            return Queryable()
                .Any(a => a.IdEstabelecimento == idEstabelecimento
                          && a.IdPessoaFisicaCliente.Equals(idPessoaFisica)
                          && a.Status == StatusDaAssinaturaEnum.Ativa);
        }

        public IQueryable<ClienteComAssinaturaParaRegularizarDTO> ObterClientesComAssinaturaParaRegularizar(FiltroTabelaDeRegularizacaoDeAssinaturasDTO filtro)
        {
            var pessoasFisicas = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var query = from a in Queryable()
                        join pf in pessoasFisicas on a.IdPessoaFisicaCliente equals pf.IdPessoa
                        where a.IdEstabelecimento == filtro.IdEstabelecimento
                              && a.PlanoCliente.Id == filtro.IdPlano
                              && (a.Status == StatusDaAssinaturaEnum.ContratoPendente
                              || pf.Email == null || pf.Email.Trim() == "")
                        select new
                        {
                            pf.IdPessoa,
                            pf.NomeCompleto,
                            pf.Email,
                            pf.Telefones
                        };

            var lista = query.ToList();

            var listaDTO = lista
                .GroupBy(x => x.IdPessoa)
                .Select(g =>
                {
                    var cliente = g.First();
                    var telefone = cliente.Telefones.FirstOrDefault(t => t.Ativo && t.Numero != null);

                    return new ClienteComAssinaturaParaRegularizarDTO
                    {
                        NomeDoCliente = cliente.NomeCompleto,
                        TelefoneDoCliente = telefone?.ToString(),
                        StatusPendencia = string.IsNullOrWhiteSpace(cliente.Email) ? "Sem e-mail" : "Contrato pendente"
                    };
                })
                .ToList();

            return listaDTO.AsQueryable();
        }
    }
}