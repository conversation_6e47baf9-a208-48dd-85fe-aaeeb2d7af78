﻿using System.Collections.Generic;

namespace Perlink.Trinks.ClubeDeAssinaturas.Repositories
{
    public partial interface IBeneficioDaAssinaturaRepository
    {
        BeneficioDaAssinatura ObterPorId(int id);
        bool PossuiBeneficioComOServico(int idServicoEstabelecimento);
        bool PossuiBeneficioComServicosSelecionados(ICollection<int> idsServicoEstabelecimento);
        bool PossuiBeneficioComProdutosSelecionados(ICollection<int> idsProdutoEstabelecimento);
        bool BeneficioEstaEmUso(int idBeneficio);
        List<BeneficioDaAssinatura> ObterBeneficiosPorIdAssinatura(int idAssinatura);
    }
}
