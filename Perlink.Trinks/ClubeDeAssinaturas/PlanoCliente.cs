﻿using Castle.ActiveRecord;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ClubeDeAssinaturas
{
    [ActiveRecord("PLCLI_Plano_Cliente", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public class PlanoCliente
    {
        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }

        [Nested]
        public virtual DadosDoPlanoCliente DadosDoPlanoCliente { get; set; }

        [BelongsTo("id_plano_cliente_modelo", Lazy = FetchWhen.OnInvoke)]
        public virtual PlanoCliente PlanoClienteModelo { get; set; }

        [HasMany(ColumnKey = "id_plano", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true, Lazy = true)]
        public virtual IList<BeneficioDoPlano> BeneficiosDoPlano { get; set; }

        [BelongsTo("id_venda_online", Lazy = FetchWhen.OnInvoke)]
        public virtual VendaOnline ConfiguracaoDeVendaOnline { get; set; } = null;

        [HasMany(ColumnKey = "id_plano_cliente", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true, Lazy = true)]
        public virtual IList<ContratoDeAdesao> ContratosDeAdesao { get; set; }

        public PlanoCliente()
        {
            Ativo = true;
            DadosDoPlanoCliente = new DadosDoPlanoCliente();
            BeneficiosDoPlano = new List<BeneficioDoPlano>();
            ContratosDeAdesao = new List<ContratoDeAdesao>();
        }

        public PlanoCliente(PlanoDTO dados, VendaOnline configuracaoDeVendaOnline) : this()
        {
            DadosDoPlanoCliente = new DadosDoPlanoCliente(dados);
            ConfiguracaoDeVendaOnline = configuracaoDeVendaOnline;
        }

        public PlanoCliente(int idEstabelecimentoDestino, PlanoCliente planoClienteModelo, PermissaoParaClubeDeAssinaturasDTO permissoes) : this()
        {
            Ativo = planoClienteModelo.Ativo;
            PlanoClienteModelo = planoClienteModelo;
            DadosDoPlanoCliente.Sincronizar(planoClienteModelo, permissoes, idEstabelecimentoDestino);
        }

        public virtual void AdicionarBeneficio(Beneficio beneficio)
        {
            var beneficioDoPlano = new BeneficioDoPlano()
            {
                Beneficio = beneficio,
                Plano = this,
            };

            BeneficiosDoPlano.Add(beneficioDoPlano);
        }

        public virtual void SincronizarBeneficio(Beneficio beneficio, BeneficioDoPlano modelo)
        {
            var beneficioDoPlano = new BeneficioDoPlano()
            {
                Beneficio = beneficio,
                Plano = this,
                BeneficioDoPlanoModelo = modelo,
                Ativo = modelo.Ativo
            };

            BeneficiosDoPlano.Add(beneficioDoPlano);
        }

        public virtual void DesassociarModelo()
        {
            InativarPlano();
            PlanoClienteModelo = null;
        }

        public virtual void AssociarModelo(PlanoCliente modelo)
        {
            PlanoClienteModelo = modelo;
            Ativo = true;
        }

        public virtual void InativarPlano()
        {
            Ativo = false;
        }

        public virtual void EditarDadosSensiveis(IntencaoEdicaoDoPlanoCliente dados)
        {
            DadosDoPlanoCliente.EditarDadosSensiveis(dados);
        }

        public virtual List<BeneficioDoPlano> ListarBeneficiosAtivos()
        {
            return BeneficiosDoPlano.Where(be => be.Ativo).ToList();
        }
    }
}