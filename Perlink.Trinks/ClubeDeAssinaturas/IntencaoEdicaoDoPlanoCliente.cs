﻿using Castle.ActiveRecord;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.ClubeDeAssinaturas
{
    [ActiveRecord("PLCLI_Intencao_Edicao_Plano_Cliente", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public class IntencaoEdicaoDoPlanoCliente
    {
        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Nested]
        public virtual DadosDoPlanoCliente DadosDoPlanoCliente { get; set; }

        [BelongsTo("id_plano_cliente", Lazy = FetchWhen.OnInvoke)]
        public virtual PlanoCliente PlanoCliente { get; set; }

        [HasMany(ColumnKey = "id_intencao_edicao_plano_cliente", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Inverse = true, Lazy = true)]
        public virtual IList<BeneficioDoPlano> BeneficiosDoPlano { get; set; }

        [Property("disponivel_para_edicao")]
        public virtual bool DisponivelParaEdicao { get; set; }

        [Property("data_para_edicao")]
        public virtual DateTime DataParaEdicao { get; set; }

        public IntencaoEdicaoDoPlanoCliente()
        {
            DadosDoPlanoCliente = new DadosDoPlanoCliente();
            BeneficiosDoPlano = new List<BeneficioDoPlano>();
        }

        public IntencaoEdicaoDoPlanoCliente(PlanoDTO dados, PlanoCliente plano)
        {
            DadosDoPlanoCliente = new DadosDoPlanoCliente(dados);
            DisponivelParaEdicao = true;
            DataParaEdicao = Calendario.Agora().AddDays(30);
            PlanoCliente = plano;
            BeneficiosDoPlano = new List<BeneficioDoPlano>();
        }

        public virtual void AdicionarBeneficio(Beneficio beneficio)
        {
            var beneficioDoPlano = new BeneficioDoPlano()
            {
                Beneficio = beneficio,
                Plano = PlanoCliente,
                IntencaoEdicaoDoPlanoCliente = this
            };

            BeneficiosDoPlano.Add(beneficioDoPlano);
        }

        public virtual void ConcluirEdicao()
        {
            DisponivelParaEdicao = false;
        }
    }
}
