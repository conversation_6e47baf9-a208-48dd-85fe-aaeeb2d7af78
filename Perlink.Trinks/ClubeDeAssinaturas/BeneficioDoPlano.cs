﻿using Castle.ActiveRecord;
using System;

namespace Perlink.Trinks.ClubeDeAssinaturas
{

    [ActiveRecord("PLCLI_Beneficio_Do_Plano", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public class BeneficioDoPlano
    {

        public BeneficioDoPlano()
        {
            Ativo = true;
            DataDeInclusao = Calendario.Agora();
        }

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [BelongsTo("id_plano")]
        public virtual PlanoCliente Plano { get; set; }

        [BelongsTo("id_beneficio", Lazy = FetchWhen.Immediate, Cascade = CascadeEnum.SaveUpdate)]
        public virtual Beneficio Beneficio { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }

        [Property("data_inclusao")]
        public virtual DateTime DataDeInclusao { get; set; }

        [Property("data_desativacao")]
        public virtual DateTime? DataDeDesativacao { get; set; }

        [BelongsTo("id_beneficio_do_plano_modelo", Lazy = FetchWhen.OnInvoke)]
        public virtual BeneficioDoPlano BeneficioDoPlanoModelo { get; set; }

        [BelongsTo("id_intencao_edicao_plano_cliente", Lazy = FetchWhen.OnInvoke)]
        public virtual IntencaoEdicaoDoPlanoCliente IntencaoEdicaoDoPlanoCliente { get; set; }

        public virtual void Inativar()
        {
            Ativo = false;
            DataDeDesativacao = Calendario.Agora();
        }

        public virtual void Sincronizar(BeneficioDoPlano modelo)
        {
            Ativo = modelo.Ativo;
            DataDeInclusao = modelo.DataDeInclusao;
            DataDeDesativacao = modelo.DataDeDesativacao;
            BeneficioDoPlanoModelo = modelo;

            Beneficio.Sincronizar(modelo.Beneficio);
        }
    }
}
