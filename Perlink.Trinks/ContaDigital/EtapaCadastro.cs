﻿using Castle.ActiveRecord;
using Perlink.Trinks.ContaDigital.Enums;
using Perlink.Trinks.ExtensionMethods;
using System;

namespace Perlink.Trinks.ContaDigital
{
    [ActiveRecord("Etapa_Cadastro", Schema = "ContaDigital", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]

    public class EtapaCadastro
    {
        public EtapaCadastro() { }

        public EtapaCadastro(int idEstabelecimento)
        {
            IdEstabelecimento = idEstabelecimento;
            EtapaAtual = EtapaCadastroEnum.InserirTelefone;
            TelefoneValidacao = string.Empty;
            DataUltimaAtualizacao = null;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("id_estabelecimento", NotNull = true)]
        public virtual int IdEstabelecimento { get; set; }

        [Property("etapa_atual", NotNull = true)]
        public virtual EtapaCadastroEnum EtapaAtual { get; set; }

        [Property("telefone_validacao")]
        public virtual string TelefoneValidacao { get; set; }

        [Property("data_ultima_atualizacao")]
        public virtual DateTime? DataUltimaAtualizacao { get; set; }

        public virtual void AtualizarEtapa(EtapaCadastroEnum novaEtapa)
        {
            EtapaAtual = novaEtapa;
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual void AtualizarTelefoneValidacao(string novoTelefone)
        {
            TelefoneValidacao = novoTelefone.SomenteNumeros();
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual void AvancarEtapa()
        {
            if (EtapaAtual == EtapaCadastroEnum.ContaAtivada)
                return;

            var proximaEtapa = (EtapaCadastroEnum)((int)EtapaAtual + 1);

            AtualizarEtapa(proximaEtapa);
        }
    }
}
