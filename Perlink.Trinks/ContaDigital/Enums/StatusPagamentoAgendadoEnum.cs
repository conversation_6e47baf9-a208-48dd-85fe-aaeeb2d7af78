﻿using Perlink.Trinks.ContaDigital.DTO;
using System;
using System.Linq.Expressions;

namespace Perlink.Trinks.ContaDigital.Enums
{
    public enum StatusPagamentoAgendadoEnum
    {
        AguardandoAprovacao = 1,
        EmAndamento = 2,
        FalhaNaTransacao = 3,
        Cancelado = 4,
        Concluido = 5,
        ProcessandoPagamento = 6,
        Pago = 7,
        <PERSON>riado = 8,
        <PERSON>egado = 9,
    }

    public static class StatusPagamentoAgendadoEnumExtensions
    {
        public static Expression<Func<PagamentosAgendadosDTO, int>> ObterOrdenacaoLogica()
        {
            return p =>
                p.Status == StatusPagamentoAgendadoEnum.Criado ? 1 :
                p.Status == StatusPagamentoAgendadoEnum.EmAndamento ? 2 :
                p.Status == StatusPagamentoAgendadoEnum.FalhaNaTransacao ? 3 :
                p.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao ? 4 :
                p.Status == StatusPagamentoAgendadoEnum.ProcessandoPagamento ? 5 :
                p.Status == StatusPagamentoAgendadoEnum.Pago ? 6 :
                p.Status == StatusPagamentoAgendadoEnum.Concluido ? 7 :
                p.Status == StatusPagamentoAgendadoEnum.Negado ? 8 :
                p.Status == StatusPagamentoAgendadoEnum.Cancelado ? 9 :
                999;
        }
    }
}
