﻿using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ContaDigital.Repositories
{
    public partial class ChavePixRepository : IChavePixRepository
    {
        public ChavePix ObterChavePixPorIdExterno(long idChavePixExterno)
        {
            return Queryable().FirstOrDefault(c => c.IdExterno == idChavePixExterno);
        }

        public List<ChavePixContaDigitalDTO> ObterChavesPixDaContaDigital(int idEstabelecimento)
        {
            var chavePix = ObterQueryableDeChavePixAtivas();
            var chavePixContaDigital = Domain.ContaDigital.ChavePixContaDigitalRepository.Queryable();
            var contaEstab = Domain.ContaDigital.ContaEstabelecimentoRepository.Queryable();

            return (from cp in chavePix
                    join ccd in chavePixContaDigital on cp.Id equals ccd.IdChavePix
                    join ce in contaEstab on ccd.IdContaDigital equals ce.IdContaDigital
                    where ce.IdEstabelecimento == idEstabelecimento && cp.Status != StatusChavePixEnum.Negado
                    select new ChavePixContaDigitalDTO
                    {
                        IdChavePix = cp.Id,
                        ChavePix = cp.Chave,
                        StatusPendente = cp.Status == StatusChavePixEnum.Pendente,
                        TipoChave = cp.Tipo,
                        IdContaDigital = ce.IdContaDigital,
                        IdEstabelecimento = ce.IdEstabelecimento
                    }
                ).ToList();
        }

        public int ObterIdContaDigitalDaChavePix(int idChavePix)
        {
            var chavePix = Queryable();
            var chavePixContaDigital = Domain.ContaDigital.ChavePixContaDigitalRepository.Queryable();

            return (from cp in chavePix
                    join ccd in chavePixContaDigital on cp.Id equals ccd.IdChavePix
                    where cp.Id == idChavePix
                    select ccd.IdContaDigital
                ).FirstOrDefault();
        }

        public bool VerificarSeNovaChaveJaExisteEEmUso(ChavePixDTO novaChavePix)
        {
            return ObterQueryableDeChavePixAtivas().Any(cp => cp.Chave == novaChavePix.Chave && cp.Status == StatusChavePixEnum.Aprovado);
        }

        public bool VerificarSeNovaChaveJaExisteEEmUsoNoMesmoEstabelecimento(ChavePixDTO novaChavePix)
        {
            var chavePixProfissional = Domain.ContaDigital.ChavePixProfissionalRepository.Queryable();
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();

            return (
                from cp in Queryable()
                join cpp in chavePixProfissional on cp.Id equals cpp.IdChavePix
                join ep in estabelecimentoProfissional on cpp.IdEstabelecimentoProfissional equals ep.Codigo
                where cp.Ativo &&
                    cp.Chave == novaChavePix.Chave &&
                    cp.Status == StatusChavePixEnum.Aprovado &&
                    ep.Estabelecimento.IdEstabelecimento == novaChavePix.IdEstabelecimento
                select cp.Id
            ).Any();
        }

        public IQueryable<ChavePix> ObterQueryableDeChavePixAtivas()
        {
            return Queryable().Where(cp => cp.Ativo);
        }

        public ChavePix ObterAtivaPorIdEstabelecimentoProfissional(int idEstabelecimentoProfissional)
        {
            var chavePix = ObterQueryableDeChavePixAtivas();
            var chavePixProfissional = Domain.ContaDigital.ChavePixProfissionalRepository.Queryable();

            return (from cp in chavePix
                    join cpp in chavePixProfissional on cp.Id equals cpp.IdChavePix
                    where cpp.IdEstabelecimentoProfissional == idEstabelecimentoProfissional
                    select cp
                ).FirstOrDefault();
        }
    }
}
