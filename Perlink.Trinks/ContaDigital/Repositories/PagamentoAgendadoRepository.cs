﻿using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ContaDigital.Repositories
{
    public partial class PagamentoAgendadoRepository : IPagamentoAgendadoRepository
    {
        public List<IGrouping<int, PagamentosAgendadosDTO>> ObterListadePagamentosAgendadosAgrupadosPorDestinatario(List<int> idsPagamentosAgendados)
        {
            var queryFolhaMesProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Queryable();
            var pagAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();
            var chavePixProfissional = Domain.ContaDigital.ChavePixProfissionalRepository.Queryable();
            var chavePix = Domain.ContaDigital.ChavePixRepository.Queryable();

            return (from p in Queryable()
                    join pfm in pagAgendadoFolhaMes on p.Id equals pfm.IdPagamentoAgendado
                    join f in queryFolhaMesProfissional on pfm.IdFolhaMesProfissional equals f.Id
                    join cpp in chavePixProfissional on f.EstabelecimentoProfissional.Codigo equals cpp.IdEstabelecimentoProfissional
                    join cp in chavePix on cpp.IdChavePix equals cp.Id
                    where
                        idsPagamentosAgendados.Contains(p.Id) &&
                        p.Status != StatusPagamentoAgendadoEnum.EmAndamento &&
                        cpp.Ativo &&
                        cp.Ativo
                    select new PagamentosAgendadosDTO
                    {
                        Id = p.Id,
                        NomeProfissional = f.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                        IdEstabelecimentoProfissional = f.EstabelecimentoProfissional.Codigo,
                        DataCriacao = p.DataAgendamento,
                        Status = p.Status,
                        Valor = p.Valor,
                        ChavePixProfissional = cp.Chave,
                    })
                    .ToList()
                    .GroupBy(p => p.IdEstabelecimentoProfissional)
                    .ToList();
        }

        public IQueryable<PagamentosAgendadosDTO> ListarDtoPagamentosAgendados(List<int> idsAgendados)
        {
            var queryFolhaMesProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Queryable();
            var pagAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();


            var listaPagamentosAgendadosDTOs = (from p in Queryable()
                                                join pfm in pagAgendadoFolhaMes on p.Id equals pfm.IdPagamentoAgendado
                                                join f in queryFolhaMesProfissional on pfm.IdFolhaMesProfissional equals f.Id
                                                where idsAgendados.Contains(p.Id)
                                                select new PagamentosAgendadosDTO
                                                {
                                                    Id = p.Id,
                                                    NomeProfissional = f.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                                                    IdEstabelecimentoProfissional = f.EstabelecimentoProfissional.Profissional.IdProfissional,
                                                    DataCriacao = p.DataAgendamento,
                                                    Status = p.Status,
                                                    Valor = p.Valor
                                                });

            return listaPagamentosAgendadosDTOs;
        }

        public bool ExistePagamentoAgendadoParaOEstabelecimento(int idEstabelecimento)
        {
            var queryFolhaMesProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Queryable();
            var pagAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();

            return (from p in Queryable()
                    join pfm in pagAgendadoFolhaMes on p.Id equals pfm.IdPagamentoAgendado
                    join f in queryFolhaMesProfissional on pfm.IdFolhaMesProfissional equals f.Id
                    where (p.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao ||
                          p.Status == StatusPagamentoAgendadoEnum.EmAndamento ||
                          p.Status == StatusPagamentoAgendadoEnum.FalhaNaTransacao) &&
                          f.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento
                    select p).Any();
        }

        public bool ExistemPagamentosNoHistorico(int idEstabelecimento)
        {
            var queryFolhaMesProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Queryable();
            var pagAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();

            return (from p in Queryable()
                    join pfm in pagAgendadoFolhaMes on p.Id equals pfm.IdPagamentoAgendado
                    join f in queryFolhaMesProfissional on pfm.IdFolhaMesProfissional equals f.Id
                    where (
                                p.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao
                                || p.Status == StatusPagamentoAgendadoEnum.EmAndamento
                                || p.Status == StatusPagamentoAgendadoEnum.FalhaNaTransacao
                                || p.Status == StatusPagamentoAgendadoEnum.Concluido
                                || p.Status == StatusPagamentoAgendadoEnum.Negado
                          ) 
                          && f.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento
                    select p).Any();
        }

        public IQueryable<PagamentosAgendadosDTO> ObterQueryablePagamentosAgendadosFiltrados(int idEstabelecimento)
        {
            var aprovadorStoneDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(idEstabelecimento, Recurso.ContaDigitalAprovadorStone)
                .EstaDisponivel;

            var queryFolhaMesProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Queryable();
            var pagAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();
            var queryPagamentos = Queryable();

            var statusPermitidos = new[]
            {
                StatusPagamentoAgendadoEnum.AguardandoAprovacao,
                StatusPagamentoAgendadoEnum.EmAndamento,
                StatusPagamentoAgendadoEnum.FalhaNaTransacao
            };

            if (aprovadorStoneDisponivel)
            {
                statusPermitidos = statusPermitidos
                    .Append(StatusPagamentoAgendadoEnum.Concluido)
                    .Append(StatusPagamentoAgendadoEnum.Negado)
                    .ToArray();
            }

            return from p in queryPagamentos
                   join pfm in pagAgendadoFolhaMes on p.Id equals pfm.IdPagamentoAgendado
                   join f in queryFolhaMesProfissional on pfm.IdFolhaMesProfissional equals f.Id
                   where statusPermitidos.Contains(p.Status) &&
                         f.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento
                   select new PagamentosAgendadosDTO
                   {
                       Id = p.Id,
                       NomeProfissional = f.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                       IdEstabelecimentoProfissional = f.EstabelecimentoProfissional.Profissional.IdProfissional,
                       DataCriacao = p.DataCriacao,
                       Status = p.Status,
                       Valor = p.Valor
                   };
        }


        public PagamentoAgendado ObterPorIdTransferencia(int idTransferencia)
            => Queryable().FirstOrDefault(p => p.IdTransferencia == idTransferencia);

        public List<PagamentoAgendado> ObterListaDePagamentoAgendado(List<int> idsPagamento, int idEstabelecimento)
        {
            var pagamentoAgendado = Queryable();
            var contaUsuarioDigital = Domain.ContaDigital.ContaUsuarioDigitalRepository.Queryable();
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.Queryable();

            return (
                    from pa in pagamentoAgendado
                    join cud in contaUsuarioDigital on pa.IdContaUsuarioDigital equals cud.Id
                    join ce in contaEstabelecimento on cud.Id equals ce.IdContaDigital
                    where idsPagamento.Contains(pa.Id) && ce.IdEstabelecimento.Equals(idEstabelecimento)
                    select pa
                ).ToList();
        }

        public int ObterIdEstabelecimentoPeloIdPagamentoAgendado(int idPagamentoAgendado)
        {
            var pagamentoAgendado = Queryable();
            var contaUsuarioDigital = Domain.ContaDigital.ContaUsuarioDigitalRepository.Queryable();
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.Queryable();
            return (
                from pa in pagamentoAgendado
                join cud in contaUsuarioDigital on pa.IdContaUsuarioDigital equals cud.Id
                join ce in contaEstabelecimento on cud.Id equals ce.IdContaDigital
                where pa.Id == idPagamentoAgendado
                select ce.IdEstabelecimento
            ).FirstOrDefault();
        }

        public int ObterIdFolhaMesProfissionalPeloIdPagamentoAgendado(int idPagamentoAgendado)
        {
            return (
                from pa in Domain.ContaDigital.PagamentoAgendadoRepository.Queryable()
                join pafmp in Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable()
                    on pa.Id equals pafmp.IdPagamentoAgendado
                where pa.Id == idPagamentoAgendado
                select pafmp.IdFolhaMesProfissional
            ).FirstOrDefault();
        }

        public int ObterIdPessoaEstabelecimentoDoIdPagamentoAgendado(int idPagamentoAgendado)
        {
            return (
                from pa in Domain.ContaDigital.PagamentoAgendadoRepository.Queryable()
                join pafmp in Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable()
                    on pa.Id equals pafmp.IdPagamentoAgendado
                join ep in Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                    on pafmp.IdEstabelecimentoProfissional equals ep.Codigo
                join e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    on ep.Estabelecimento.IdEstabelecimento equals e.IdEstabelecimento
                where pa.Id == idPagamentoAgendado
                select e.PessoaJuridica.IdPessoa
            ).FirstOrDefault();
        }

        public (int idPessoaEstabelecimento, int idFolhaMesProfissional) ObterIdPessoaEstabelecimentoEIdFolhaMesProfissionalDoIdPagamentoAgendado(int idPagamentoAgendado)
        {
            var resultado = (
                from pa in Domain.ContaDigital.PagamentoAgendadoRepository.Queryable()
                join pafmp in Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable()
                    on pa.Id equals pafmp.IdPagamentoAgendado
                join ep in Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                    on pafmp.IdEstabelecimentoProfissional equals ep.Codigo
                join e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    on ep.Estabelecimento.IdEstabelecimento equals e.IdEstabelecimento
                where pa.Id == idPagamentoAgendado
                select new { IdPessoaEstabelecimento = e.PessoaJuridica.IdPessoa, pafmp.IdFolhaMesProfissional }
            ).FirstOrDefault();

            return (resultado.IdPessoaEstabelecimento, resultado.IdFolhaMesProfissional);
        }

        public List<EstornoPagamentoDTO> ObterInformacoesParaEstornoDePagamentos(List<int> idsDosPagamentos)
        {
            return (
                from pa in Domain.ContaDigital.PagamentoAgendadoRepository.Queryable()
                join pafmp in Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable()
                    on pa.Id equals pafmp.IdPagamentoAgendado
                join ep in Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                    on pafmp.IdEstabelecimentoProfissional equals ep.Codigo
                join e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    on ep.Estabelecimento.IdEstabelecimento equals e.IdEstabelecimento
                where idsDosPagamentos.Contains(pa.Id)
                select new EstornoPagamentoDTO(pa.Id, e.PessoaJuridica.IdPessoa, pafmp.IdFolhaMesProfissional)
            ).ToList();
        }

        public bool ExistePagamentoAgendadoParaEstabelecimentoProfissional(int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            var queryFolhaMesProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository.Queryable();
            var pagAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();

            return (from p in Queryable()
                    join pfm in pagAgendadoFolhaMes on p.Id equals pfm.IdPagamentoAgendado
                    join f in queryFolhaMesProfissional on pfm.IdFolhaMesProfissional equals f.Id
                    where (p.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao ||
                          p.Status == StatusPagamentoAgendadoEnum.EmAndamento ||
                          p.Status == StatusPagamentoAgendadoEnum.Criado) &&
                          f.EstabelecimentoProfissional.Codigo == idEstabelecimentoProfissional &&
                          f.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento
                    select p).Any();
        }

        public bool ExistePagamentoAgendadoAguardandoPagamentoOuComFalha(int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            var pagamentoAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();
            var contaDigital = Domain.ContaDigital.ContaUsuarioDigitalRepository.Queryable();
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.Queryable();

            var dados = (from p in Queryable()
                         join pafm in pagamentoAgendadoFolhaMes on p.Id equals pafm.IdPagamentoAgendado
                         join cd in contaDigital on p.IdContaUsuarioDigital equals cd.Id
                         join ce in contaEstabelecimento on cd.Id equals ce.IdContaDigital
                         where (p.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao 
                                    || p.Status == StatusPagamentoAgendadoEnum.EmAndamento
                                    || p.Status == StatusPagamentoAgendadoEnum.FalhaNaTransacao) 
                               && pafm.IdEstabelecimentoProfissional == idEstabelecimentoProfissional 
                               && ce.IdEstabelecimento == idEstabelecimento
                         select p);

            return dados.Any();
        }

        public bool ExistePagamentoAgendadosPendentesComAprovadorExterno(int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            var pagamentoAgendadoFolhaMes = Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable();
            var contaDigital = Domain.ContaDigital.ContaUsuarioDigitalRepository.Queryable();
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.Queryable();

            var dados = (from p in Queryable()
                         join pafm in pagamentoAgendadoFolhaMes on p.Id equals pafm.IdPagamentoAgendado
                         join cd in contaDigital on p.IdContaUsuarioDigital equals cd.Id
                         join ce in contaEstabelecimento on cd.Id equals ce.IdContaDigital
                         where (p.Status == StatusPagamentoAgendadoEnum.Criado
                                    || p.Status == StatusPagamentoAgendadoEnum.EmAndamento
                                    || p.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao) 
                               && pafm.IdEstabelecimentoProfissional == idEstabelecimentoProfissional &&
                               ce.IdEstabelecimento == idEstabelecimento
                         select p);

            return dados.Any();
        }

        public List<(PagamentoAgendado, Transferencia)> ObterPagamentosAguardandoAprovacaoNoAppStoneAlemDoTempoLimite(DateTime dataHoraAtual, int quantidadeDeHorasLimite)
        {
            var dataHoraLimite = dataHoraAtual.AddHours(-quantidadeDeHorasLimite);

            var pagamentosAguardandoAprovacao = (
                from pafmp in Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.Queryable()
                join pa in Domain.ContaDigital.PagamentoAgendadoRepository.Queryable() on pafmp.IdPagamentoAgendado equals pa.Id
                join t in Domain.ContaDigital.TransferenciaRepository.Queryable() on pa.IdTransferencia equals t.Id
                join ep in Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable() on pafmp.IdEstabelecimentoProfissional equals ep.Codigo
                where pa.Status == StatusPagamentoAgendadoEnum.AguardandoAprovacao && t.DataCriacao <= dataHoraLimite
                select new { pa, t }
            ).ToList();

            var pagamentos = pagamentosAguardandoAprovacao.Select(x => (x.pa, x.t)).ToList();

            return pagamentos;
        }
    }
}
