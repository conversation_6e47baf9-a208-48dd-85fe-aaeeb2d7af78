﻿using Perlink.Trinks.ContaDigital.DTO;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ContaDigital.Repositories
{
    public partial interface IChavePixRepository
    {
        ChavePix ObterChavePixPorIdExterno(long idChavePixExterno);

        List<ChavePixContaDigitalDTO> ObterChavesPixDaContaDigital(int idEstabelecimento);

        bool VerificarSeNovaChaveJaExisteEEmUso(ChavePixDTO novaChavePix);

        int ObterIdContaDigitalDaChavePix(int idChavePix);

        IQueryable<ChavePix> ObterQueryableDeChavePixAtivas();

        ChavePix ObterAtivaPorIdEstabelecimentoProfissional(int idEstabelecimentoProfissional);
        bool VerificarSeNovaChaveJaExisteEEmUsoNoMesmoEstabelecimento(ChavePixDTO novaChavePix);
    }
}
