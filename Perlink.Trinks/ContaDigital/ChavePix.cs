﻿using Castle.ActiveRecord;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;

namespace Perlink.Trinks.ContaDigital
{
    [ActiveRecord("Chave_Pix", Schema = "ContaDigital", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public class ChavePix
    {
        public ChavePix() { }

        public ChavePix(ChavePixDTO pixDto)
        {
            IdExterno = pixDto.IdExterno;
            Chave = pixDto.Tipo == TipoChavePixEnum.Telefone ? "+55" + pixDto.Chave : pixDto.Chave;
            Tipo = pixDto.Tipo;
            Status = StatusChavePixEnum.Pendente;
            Ativo = true;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("id_externo", NotNull = false)]
        public virtual long? IdExterno { get; set; }

        [Property("chave", NotNull = false)]
        public virtual string Chave { get; set; }

        [Property("tipo")]
        public virtual TipoChavePixEnum Tipo { get; set; }

        [Property("status")]
        public virtual StatusChavePixEnum Status { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }

        public virtual void AtualizarStatus(StatusChavePixEnum novoStatus)
        {
            Status = novoStatus;
        }

        public virtual void AtualizarChave(string chavePix)
        {
            Chave = chavePix;
        }

        public virtual void InativarChave()
        {
            Ativo = false;
        }
    }
}
