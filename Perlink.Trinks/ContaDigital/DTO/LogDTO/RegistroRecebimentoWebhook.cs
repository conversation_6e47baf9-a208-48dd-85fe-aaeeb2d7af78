﻿namespace Perlink.Trinks.ContaDigital.DTO.LogDTO
{
    public class RegistroRecebimentoWebhook : ILogDTO
    {

        public string TipoWebhook { get; set; }
        public string TipoEvento { get; set; }
        public long IdReferencia { get; set; }
        public string CorpoWebhook { get; set; }

        public RegistroRecebimentoWebhook(string tipoWebhook, string tipoEvento, long idReferencia, string corpoWebhook)
        {
            TipoWebhook = tipoWebhook;
            TipoEvento = tipoEvento;
            IdReferencia = idReferencia;
            CorpoWebhook = corpoWebhook;
        }

    }
}