﻿using System;

namespace Perlink.Trinks.ContaDigital.DTO
{
    public class RetornoTranferenciaPixDTO
    {
        public long IdTransacaoNoGateway { get; set; }

        public int IdContaDigitalOrigemExterno { get; set; }

        public int ValorEmCentavos { get; set; }

        public string Status { get; set; }

        public DateTime DataCriacao { get; set; }

        public string CodigoBanco { get; set; }

        public string NumeroAgencia { get; set; }

        public string NumeroConta { get; set; }

        public string NomeDestinatario { get; set; }

        public int IdPagamentoAgendado { get; set; }

        public string DetalhesErro { get; set; }
    }
}
