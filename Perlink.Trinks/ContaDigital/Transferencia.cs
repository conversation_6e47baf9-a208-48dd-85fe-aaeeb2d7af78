﻿using Castle.ActiveRecord;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;
using System;

namespace Perlink.Trinks.ContaDigital
{
    [ActiveRecord("Transferencia", Schema = "ContaDigital", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]

    public class Transferencia
    {
        public Transferencia() { }

        public Transferencia(long idTransacaoNoGateway, int idContaDigitalOrigem, decimal valor, TipoTransferenciaEnum tipoTransferencia)
        {
            IdTransferenciaGateway = idTransacaoNoGateway;
            IdContaDigitalOrigem = idContaDigitalOrigem;
            Valor = valor;
            Status = StatusTransferenciaEnum.Pendente;
            DataCriacao = Calendario.Agora();
            Tipo = tipoTransferencia;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("id_conta_digital_origem", NotNull = true)]
        public virtual int IdContaDigitalOrigem { get; set; }

        [Property("id_transferencia_gateway", NotNull = true)]
        public virtual long IdTransferenciaGateway { get; set; }

        [Property("valor")]
        public virtual decimal Valor { get; set; }

        [Property("data_criacao")]
        public virtual DateTime DataCriacao { get; set; }

        [Property("data_atualizacao", NotNull = false)]
        public virtual DateTime? DataAtualizacao { get; set; }

        [Property("tipo")]
        public virtual TipoTransferenciaEnum Tipo { get; set; }

        [Property("status")]
        public virtual StatusTransferenciaEnum Status { get; set; }

        [Nested]
        public virtual DestinatarioTransferencia Destinatario { get; set; }

        public virtual void AtualizarStatus(StatusTransferenciaEnum status)
        {
            Status = status;
            DataAtualizacao = Calendario.Agora();
        }

        public virtual void SinalizarTransferenciaNegada()
        {
            AtualizarStatus(StatusTransferenciaEnum.Negado);
        }

        public virtual void AssociarDestinatario(DestinatarioTransferenciaDTO destinatario)
        {
            Destinatario = new DestinatarioTransferencia(destinatario);
        }
    }
}
