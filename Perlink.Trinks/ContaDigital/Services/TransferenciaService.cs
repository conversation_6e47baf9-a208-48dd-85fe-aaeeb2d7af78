﻿using Perlink.DomainInfrastructure.Facilities;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Auditing;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.DTO.LogDTO;
using Perlink.Trinks.ContaDigital.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.Builders;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ContaDigital.Services
{
    public class TransferenciaService : BaseService, ITransferenciaService
    {
        public void AgendarTransferencias(List<TransferenciaDTO> transferencias, int idEstabelecimento)
        {
            ValidarEstabelecimento(idEstabelecimento);

            if (!ValidationHelper.Instance.IsValid) return;

            foreach (var transferencia in transferencias)
            {
                if (transferencia.TipoTransferencia == TipoTransferenciaEnum.Pix)
                {
                    AgendarTransferencia(transferencia, idEstabelecimento);
                }
            }
        }

        private void AgendarTransferencia(TransferenciaDTO dados, int idEstabelecimento)
        {
            var idContaDigitalExterno = Domain.ContaDigital.ContaUsuarioDigitalRepository.ObterIdContaDigitalExternoPeloIdEstabelecimento(idEstabelecimento);

            var chavePix = Domain.ContaDigital.ChavePixService.ObterChavePixDoProfissional(dados.IdEstabelecimentoProfissional);

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            var stringChave = Domain.ContaDigital.ChavePixRepository.Load(chavePix.IdChavePix).Chave;

            var contaAutenticada = Domain.Pessoas.ContaRepository.ObterPorId(Domain.WebContext.IdContaAutenticada.Value);
            var idPessoaQueAprovou = contaAutenticada.Pessoa.PessoaFisica.IdPessoaFisica;

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            var mensagemFila = new MensagemNaFilaDisparo
            {
                ChavePixDestino = stringChave,
                Valor = dados.ValorTransferencia,
                IdContaDigitalOrigemExterno = idContaDigitalExterno,
                IdPagamentoAgendado = dados.IdPagamentoAgendado,
                IdEstabelecimentoProfissional = dados.IdEstabelecimentoProfissional,
                IdPessoaQueAprovou = idPessoaQueAprovou,
                IdEstabelecimento = idEstabelecimento,
            };

            Domain.ContaDigital.ProcessamentoDeMensagensService.IncluirNaFila(mensagemFila);
        }

        public int CriarTransferenciaViaPix(RetornoTranferenciaPixDTO dadosRetorno, int idChavePixDestinatario)
        {
            var valorTransferenciaEmReais = ObterValorEmReais(dadosRetorno.ValorEmCentavos);
            var idContaDigitalOrigem = Domain.ContaDigital.ContaUsuarioDigitalRepository.ObterIdContaDigitalPeloIdContaDigitalExterno(dadosRetorno.IdContaDigitalOrigemExterno);
            var transferencia = new Transferencia(dadosRetorno.IdTransacaoNoGateway, idContaDigitalOrigem, valorTransferenciaEmReais, TipoTransferenciaEnum.Pix);

            transferencia.AssociarDestinatario(new DestinatarioTransferenciaDTO()
            {
                CodigoBanco = dadosRetorno.CodigoBanco,
                Agencia = dadosRetorno.NumeroAgencia,
                NumeroConta = dadosRetorno.NumeroConta,
                NomeDestinatario = dadosRetorno.NomeDestinatario,
                IdChavePixDestinatario = idChavePixDestinatario,
            });

            Domain.ContaDigital.TransferenciaRepository.SaveNew(transferencia);

            RegistrarNovaTransferencia(transferencia.Id, transferencia.IdContaDigitalOrigem, dadosRetorno.IdPagamentoAgendado);

            return transferencia.Id;
        }

        private void RegistrarNovaTransferencia(int idTransferencia, int idContaDigitalOrigem, int idPagamentoAgendado)
        {
            var dto = new RegistroNovaTransferenciaDTO(idContaDigitalOrigem, idTransferencia, idPagamentoAgendado);
            Domain.ContaDigital.LogContaDigitalService.RegistrarLog(dto);
        }

        private void ValidarEstabelecimento(int idEstabelecimento)
        {
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.ObterPorIdEstabelecimento(idEstabelecimento);

            if (contaEstabelecimento == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Este estabelecimento não possui conta digital cadastrada.");
        }

        public void AtualizarTranferencia(long idTransferenciaGateway, string novoStatus)
        {
            var transferencia = Domain.ContaDigital.TransferenciaRepository.ObterPorIdTransferenciaGateway(idTransferenciaGateway);

            var ehTransferenciaCriadaEmNossoSistema = transferencia != null;

            if (!ehTransferenciaCriadaEmNossoSistema)
                return;

            RegistrarAlteracaoStatusTransaferencia(transferencia.Id, transferencia.IdContaDigitalOrigem, novoStatus);

            if (Enum.TryParse(novoStatus, out StatusTransferenciaEnum statusTranferencia))
            {
                transferencia.AtualizarStatus(statusTranferencia);
                var agendamento =
                    Domain.ContaDigital.PagamentoAgendadoRepository.ObterPorIdTransferencia(transferencia.Id);

                var usaAprovadorContaDigital = AprovadorStoneHabilitado();

                if (statusTranferencia.Equals(StatusTransferenciaEnum.Negado))
                {
                    if (usaAprovadorContaDigital)
                    {
                        NegarTransferenciaDeEstabelecimentoComAprovador(transferencia, agendamento);
                    }
                    else
                    {
                        agendamento.SinalizarPagamentoNegado();
                    }
                }

                if (statusTranferencia.Equals(StatusTransferenciaEnum.Aprovado))
                {
                    if (usaAprovadorContaDigital && agendamento.Status == StatusPagamentoAgendadoEnum.Negado)
                    {
                        EnviarEmailDeTransferenciaJaNegadaSendoAprovadaNoApp(agendamento.Id, transferencia.Id);
                    }
                    else
                    {
                        agendamento.ConcluirPagamento();
                    }

                }

                Domain.ContaDigital.TransferenciaRepository.Update(transferencia);

                Domain.ContaDigital.PagamentoAgendadoRepository.Update(agendamento);
            }
            else
            {
                throw new NotImplementedException("[Webhook Trampol.in] Erro ao atualizar status da transferência. O status recebido não existe no StatusTransferenciaEnum.");
            }
        }

        private bool AprovadorStoneHabilitado()
        {
            var aprovadorStoneDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeGeralRepository
                .ObterPrimeiroPorRecurso((int)RecursoEnum.ContaDigital_Aprovador_Stone)
                .Disponivel;

            return aprovadorStoneDisponivel;
        }

        private void NegarTransferenciaDeEstabelecimentoComAprovador(Transferencia transferencia, PagamentoAgendado pagamentoAgendado)
        {
            var (idPessoaEstabelecimento, idFolhaMesProfissional) = Domain.ContaDigital.PagamentoAgendadoRepository
                .ObterIdPessoaEstabelecimentoEIdFolhaMesProfissionalDoIdPagamentoAgendado(pagamentoAgendado.Id);

            SinalizarPagamentoComoNegado(transferencia, pagamentoAgendado);

            Domain.Financeiro.EstornoPagamentoDeProfissionaisService.EstornarPagamento(idFolhaMesProfissional, idPessoaEstabelecimento, OrigemPedidoEstornoEnum.ContaDigital);
        }

        public void CancelarTransferencias(List<(PagamentoAgendado, Transferencia)> pagamentos)
        {
            CancelarPagamentosAgendadosEmLote(pagamentos);

            var idsDosPagamentosAgendados = pagamentos.Select(p => p.Item1.Id).ToList();

            RealizarEstornoDePagamentosEmLote(idsDosPagamentosAgendados);
        }

        private static void CancelarPagamentosAgendadosEmLote(List<(PagamentoAgendado, Transferencia)> pagamentos)
        {
            var session = NHibernateFacility.GetSession<PagamentoAgendado>();

            using (var tx = session.BeginTransaction())
            {
                try
                {
                    foreach (var (pagamentoAgendado, transferencia) in pagamentos)
                    {
                        pagamentoAgendado.SinalizarPagamentoNegado();

                        transferencia.SinalizarTransferenciaNegada();

                        Domain.ContaDigital.PagamentoAgendadoRepository.UpdateNoFlush(pagamentoAgendado);

                        Domain.ContaDigital.TransferenciaRepository.UpdateNoFlush(transferencia);
                    }

                    Domain.ContaDigital.PagamentoAgendadoRepository.Flush();

                    tx.Commit();
                }
                catch (Exception ex)
                {
                    tx.Rollback();

                    LogService<TransferenciaService>.Error($"Erro ao cancelar transferências e pagamentos agendados: {ex.Message}");

                    throw;
                }
            }
        }

        private void RealizarEstornoDePagamentosEmLote(List<int> idsPagamentos)
        {
            var listaDePagamentosParaEstornar = Domain.ContaDigital.PagamentoAgendadoRepository.ObterInformacoesParaEstornoDePagamentos(idsPagamentos);

            Domain.Financeiro.EstornoPagamentoDeProfissionaisService.EstornarPagamentos(listaDePagamentosParaEstornar, OrigemPedidoEstornoEnum.ContaDigital);
        }

        private void RegistrarAlteracaoStatusTransaferencia(int idTransferencia, int idContaDigitalOrigem, string statusTranferencia)
        {
            var dto = new RegistroAlteracaoStatusTransaferenciaDTO(idTransferencia, statusTranferencia, idContaDigitalOrigem);
            Domain.ContaDigital.LogContaDigitalService.RegistrarLog(dto);
        }

        private decimal ObterValorEmReais(int valorEmCentavos)
        {
            return Convert.ToDecimal(valorEmCentavos / 100);
        }

        
        private void SinalizarPagamentoComoNegado(Transferencia transferencia, PagamentoAgendado pagamentoAgendado)
        {
            var session = NHibernateFacility.GetSession<PagamentoAgendado>();

            using (var tx = session.BeginTransaction())
            {
                try
                {
                    transferencia.SinalizarTransferenciaNegada();

                    pagamentoAgendado.SinalizarPagamentoNegado();

                    Domain.ContaDigital.TransferenciaRepository.Update(transferencia);

                    Domain.ContaDigital.PagamentoAgendadoRepository.Update(pagamentoAgendado);

                    tx.Commit();
                }
                catch (Exception ex)
                {
                    tx.Rollback();

                    var mensagem = $"[ContaDigital] - Erro ao sinalizar pagamento como negado. Mensagem de erro: {ex.Message}";

                    LogService<TransferenciaService>.Error(mensagem);

                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new BusinessException(mensagem)));
                }
            }
        }

        public void EnviarEmailDeTransferenciaJaNegadaSendoAprovadaNoApp(int idPagamentoAgendado, int idTransferencia)
        {
            try
            {
                var idEstabelecimento = Domain.ContaDigital.PagamentoAgendadoRepository
                    .ObterIdEstabelecimentoPeloIdPagamentoAgendado(idPagamentoAgendado);

                var dataHora = Calendario.Agora();

                var corpoDoEmail = new CorpoDeEmailParaEnvioBuilder()
                    .AdicionarParagrafo("Transferência já negada pela rotina de cancelamento, foi aprovada no App Stone")
                    .AdicionarLinha("Id da transferência...: {0}", idTransferencia)
                    .AdicionarLinha("Id do estabelecimento.: {0}", idEstabelecimento)
                    .AdicionarLinha("Data e hora do webhook: {0}", $"{dataHora:dd/MM/yyyy} às {dataHora:HH:mm}")
                    .Build();

                var emailParaEnvioDTO = new EmailParaEnvioBuilder()
                    .AdicionarAssunto("[ContaDigital] - Transferência já negada foi aprovada no App Stone")
                    .AdicionarRemetente(Pessoas.Services.EnvioEmailService.ObterRemetentePadrao())
                    .AdicionarDestinatario(Pessoas.Services.EnvioEmailService.ObterEmailErrosContaDigital())
                    .AdicionarCorpo(corpoDoEmail)
                    .Build();

                Domain.Pessoas.EnvioEmailService.DispararEmail(emailParaEnvioDTO);
            }
            catch (Exception ex)
            {
                string mensagemDeErroDoEnvioDeEmail = $"[ContaDigital] - Erro no envio de e-mail. Mensagem de erro: {ex.Message}";

                LogService<TransferenciaService>.Error(mensagemDeErroDoEnvioDeEmail);

                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new BusinessException(mensagemDeErroDoEnvioDeEmail)));
            }
        }
    }
}
