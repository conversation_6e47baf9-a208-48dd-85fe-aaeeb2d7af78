﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ContaDigital.DTO.LogDTO;
using Perlink.Trinks.ContaDigital.Enums;
using System;

namespace Perlink.Trinks.ContaDigital.Services
{
    public class LogContaDigitalService : BaseService, ILogContaDigitalService
    {

        public void RegistrarLog(ILogDTO logDTO)
        {
            try
            {
                RegistrarLogContaDigital(logDTO);
            }
            catch (Exception ex)
            {
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                return;
            }

        }

        private void RegistrarLogContaDigital(ILogDTO logDTO)
        {
            switch (logDTO)
            {

                case RegistroCriacaoContaDigitalDTO dto:
                    RegistrarCriacaoContaDigital(dto);
                    break;
                case RegistroAlteracaoStatusContaDigitalDTO dto:
                    RegistrarAlteracaoStatusContaDigital(dto);
                    break;
                case RegistroNovaTransferenciaDTO dto:
                    RegistrarNovaTransferencia(dto);
                    break;
                case RegistroAlteracaoStatusTransaferenciaDTO dto:
                    RegistrarAlteracaoStatusTransaferencia(dto);
                    break;
                case RegistroCriacaoChavePixDTO dto:
                    RegistrarCriacaoChavePix(dto);
                    break;
                case RegistroAlteracaoStatusChavePixDTO dto:
                    RegistrarAlteracaoStatusChavePixCriadaParaConta(dto);
                    break;
                case RegistroAlteracaoLimiteContaDigitalDTO dto:
                    RegistrarAlteracaoLimiteContaDigital(dto);
                    break;
                case RegistroRecebimentoWebhook dto:
                    RegistrarRecebimentoWebhook(dto);
                    break;
                default:
                    throw new NotImplementedException();
            }
        }

        private void RegistrarRecebimentoWebhook(RegistroRecebimentoWebhook dto)
        {
            var dataAnoMesDia = DateTime.Now.ToString("yyyyMMdd");
            var caminhoPasta = $"Webhook/{dataAnoMesDia}/{dto.TipoWebhook}/{dto.TipoEvento}";

            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.RecebimentoWebhook, dto.IdReferencia);
            var conteudo = $"Webhook recebido. Tipo: {dto.TipoWebhook} / Evento: {dto.TipoEvento} \n Corpo: {dto.CorpoWebhook}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarCriacaoContaDigital(RegistroCriacaoContaDigitalDTO dto)
        {
            var caminhoPasta = $"Conta/{dto.IdContaDigital}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.CriacaoContaDigital, dto.IdContaDigital);
            var conteudo = $"Conta digital criada. id_conta_usuario_digital: {dto.IdContaDigital}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarAlteracaoStatusContaDigital(RegistroAlteracaoStatusContaDigitalDTO dto)
        {
            var caminhoPasta = $"Conta/{dto.IdContaDigital}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.AlteracaoStatusContaDigital, dto.IdContaDigital);
            var conteudo = $"Status conta digital alterada para {dto.NovoStatus}. id_conta_usuario_digital: {dto.IdContaDigital}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarNovaTransferencia(RegistroNovaTransferenciaDTO dto)
        {
            var caminhoPasta = $"Transferencia/TransferenciaStatus/{dto.IdContaDigital}/{dto.IdTransferencia}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.PedidoTransaferencia, dto.IdContaDigital);
            var conteudo = $"Pedido de transferência criado. id_conta_usuario_digital: {dto.IdContaDigital} / id_pagamento_agendado: {dto.IdPagamentoAgendado} / id_pagamento_agendado: {dto.IdPagamentoAgendado}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarAlteracaoStatusTransaferencia(RegistroAlteracaoStatusTransaferenciaDTO dto)
        {
            var caminhoPasta = $"Transferencia/TransferenciaStatus/{dto.IdContaDigital}/{dto.IdTransferencia}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.AlteracaoStatusTransaferencia, dto.IdContaDigital);
            var conteudo = $"Status transferência alterada para {dto.NovoStatusTransacao}. id_conta_usuario_digital: {dto.IdContaDigital} / id_transferencia: {dto.IdTransferencia}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarCriacaoChavePix(RegistroCriacaoChavePixDTO dto)
        {
            var caminhoPasta = $"ChavePix/{dto.IdContaDigital}/{dto.IdChavePix}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.CriacaoChavePix, dto.IdContaDigital);
            var conteudo = $"Chave PIX criada. id_conta_usuario_digital: {dto.IdContaDigital} / id_chave_pix: {dto.IdChavePix}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarAlteracaoStatusChavePixCriadaParaConta(RegistroAlteracaoStatusChavePixDTO dto)
        {
            var caminhoPasta = $"ChavePix/{dto.IdContaDigital}/{dto.IdChavePix}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.AlteracaoStatusChavePixCriadaParaConta, dto.IdContaDigital);
            var conteudo = $"O status da chave PIX foi modificado para {dto.Status}. id_conta_usuario_digital: {dto.IdContaDigital} / id_chave_pix: {dto.IdChavePix}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private void RegistrarAlteracaoLimiteContaDigital(RegistroAlteracaoLimiteContaDigitalDTO dto)
        {
            var caminhoPasta = $"Limite/{dto.IdContaDigital}";
            var nomeArquivo = GerarNomeArquivo(LogContaDigitalEnum.AlteracaoLimiteContaDigital, dto.IdContaDigital);
            var conteudo = $"Limite da conta digital alterado. id_conta_usuario_digital: {dto.IdContaDigital} / id_limite_plano: {dto.IdLimitePlano}";

            SalvarArquivo(caminhoPasta, nomeArquivo, conteudo);
        }

        private string GerarNomeArquivo(LogContaDigitalEnum tipo, int identificador)
        {
            return $"[{tipo}]_{identificador}_{DateTime.Now.ToString("yyyyMMddHHmmss_fffffff")}.txt";
        }

        private string GerarNomeArquivo(LogContaDigitalEnum tipo, long identificador)
        {
            return $"[{tipo}]_{identificador}_{DateTime.Now.ToString("yyyyMMddHHmmss_fffffff")}.txt";
        }

        private void SalvarArquivo(string caminhoPasta, string nomeArquivo, string conteudo)
        {
            var caminhoCompleto = $"log/{caminhoPasta}";
            Domain.ContaDigital.ArmazenamentoDeArquivoService.SalvarArquivo(nomeArquivo, caminhoCompleto, conteudo);
        }
    }
}
