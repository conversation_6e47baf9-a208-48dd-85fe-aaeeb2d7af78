﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Perlink.Trinks.ContaDigital.Services
{
    public class PagamentoAgendadoService : BaseService, IPagamentoAgendadoService
    {
        public ResultadoPagamentosAgendadosDTO ListarPagamentosAgendados(PagamentosAgendadosFiltroDTO filtro)
        {
            var queryPagamentosAgendadosDTOs = FiltrarPagamentosAgendados(filtro);
            queryPagamentosAgendadosDTOs = OrdenarListaDePagamentosAgendados(filtro, queryPagamentosAgendadosDTOs);

            filtro.Paginacao.TotalItens = queryPagamentosAgendadosDTOs.Count();

            if (filtro.AplicarPaginacao)
                queryPagamentosAgendadosDTOs = AplicarPaginacao(filtro, queryPagamentosAgendadosDTOs);


            var listaPagamentosAgendadosDTOs = queryPagamentosAgendadosDTOs.ToList();

            var resultado = new ResultadoPagamentosAgendadosDTO
            {
                Registros = listaPagamentosAgendadosDTOs,
                Paginacao = filtro.Paginacao
            };

            return resultado;
        }

        public ResultadoPagamentosAgendadosDTO ListarPagamentosAgendadosSelecionados(PagamentosAgendadosFiltroDTO filtro)
        {

            var queryPagamentosAgendadosDtOs =
                Domain.ContaDigital.PagamentoAgendadoRepository.ListarDtoPagamentosAgendados(
                    filtro.IdsPagamentosAgendandos);

            filtro.Paginacao.TotalItens = queryPagamentosAgendadosDtOs.Count();

            if (filtro.AplicarPaginacao)
                queryPagamentosAgendadosDtOs = AplicarPaginacao(filtro, queryPagamentosAgendadosDtOs);

            var listaPagamentosAgendadosDtOs = queryPagamentosAgendadosDtOs.ToList();

            var resultado = new ResultadoPagamentosAgendadosDTO
            {
                Registros = listaPagamentosAgendadosDtOs,
                Paginacao = filtro.Paginacao
            };

            return resultado;
        }

        public bool VerificarSeExistemPagamentosAgendados(int idEstabelecimento)
        {

            var estabelecimentoUsaAprovadorContaDigital = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(idEstabelecimento, Recurso.ContaDigitalAprovadorStone).EstaDisponivel;

            if (estabelecimentoUsaAprovadorContaDigital)
                return Domain.ContaDigital.PagamentoAgendadoRepository.ExistemPagamentosNoHistorico(idEstabelecimento);

            return Domain.ContaDigital.PagamentoAgendadoRepository.ExistePagamentoAgendadoParaOEstabelecimento(idEstabelecimento);
        }

        private static IQueryable<PagamentosAgendadosDTO> FiltrarPagamentosAgendados(PagamentosAgendadosFiltroDTO filtro)
        {

            var listaPagamentosAgendados =
                Domain.ContaDigital.PagamentoAgendadoRepository.ObterQueryablePagamentosAgendadosFiltrados(
                    filtro.IdEstabelecimento);

            if (filtro.IdsStatus != null && filtro.IdsStatus.Any())
            {
                var listaStatusSelecionadosNoFiltro = filtro.IdsStatus
                    .Select(x => (StatusPagamentoAgendadoEnum)x)
                    .ToList();

                listaPagamentosAgendados = listaPagamentosAgendados.Where(p => listaStatusSelecionadosNoFiltro.Contains(p.Status));
            }

            if (filtro.IdsDestinatarios == null)
                return listaPagamentosAgendados;

            return listaPagamentosAgendados.Where(p => filtro.IdsDestinatarios.Contains(p.IdEstabelecimentoProfissional));
        }

        private static IQueryable<PagamentosAgendadosDTO> OrdenarListaDePagamentosAgendados(PagamentosAgendadosFiltroDTO filtro, IQueryable<PagamentosAgendadosDTO> consulta)
        {
            var ascendente = filtro.SentidoOrdenacao == (int)SentidoOrdenacaoPagamentoAgendadoEnum.Ascendente;

            switch (filtro.OrdenarPor)
            {
                case OrdernarPagamentosAgendadosEnum.DataAgendamento:
                    consulta = ascendente ?
                        consulta.OrderBy(p => p.DataCriacao) :
                        consulta.OrderByDescending(p => p.DataCriacao);
                    break;

                case OrdernarPagamentosAgendadosEnum.Destinatario:
                    consulta = ascendente ?
                        consulta.OrderBy(p => p.NomeProfissional) :
                        consulta.OrderByDescending(p => p.NomeProfissional);
                    break;

                case OrdernarPagamentosAgendadosEnum.Valor:
                    consulta = ascendente ?
                        consulta.OrderBy(p => p.Valor) :
                        consulta.OrderByDescending(p => p.Valor);
                    break;

                case OrdernarPagamentosAgendadosEnum.Status:
                    var ordemDeExibicaoDosStatus = StatusPagamentoAgendadoEnumExtensions.ObterOrdenacaoLogica();

                    consulta = ascendente ?
                        consulta.OrderBy(ordemDeExibicaoDosStatus) :
                        consulta.OrderByDescending(ordemDeExibicaoDosStatus);
                    break;

                default:
                    throw new ArgumentOutOfRangeException();
            }

            return consulta;
        }


        private static IQueryable<T> AplicarPaginacao<T>(PagamentosAgendadosFiltroDTO filtro, IQueryable<T> query)
        {
            query = query.Skip(filtro.Paginacao.RegistroInicial - 1).Take(filtro.Paginacao.RegistrosPorPagina);
            return query;
        }

        public void AprovarPagamentosAgendados(List<IGrouping<int, PagamentosAgendadosDTO>> listaPagamentos, int idEstabelecimento)
        {
            var listaTransferencias = new List<TransferenciaDTO>();

            if (!listaPagamentos.Any()) return;

            foreach (var grupo in listaPagamentos)
            {
                listaTransferencias.AddRange(grupo.Select(pagamento => new TransferenciaDTO(grupo.Key, pagamento.Valor, pagamento.Id, TipoTransferenciaEnum.Pix)));
            }

            Domain.ContaDigital.TransferenciaService.AgendarTransferencias(listaTransferencias, idEstabelecimento);
        }

        public void CancelarPagamentoAgendado(PagamentoAgendado pagamentoAgendado)
        {
            pagamentoAgendado.CancelarPagamento();
            Domain.ContaDigital.PagamentoAgendadoRepository.Update(pagamentoAgendado);
        }

        public void AgendarPagamento(AgendarPagamentoDTO agendarPagamentoDto)
        {
            var idContaUsuarioDigital = Domain.ContaDigital.ContaUsuarioDigitalRepository.ObterIdContaDigitalPeloIdEstabelecimento(agendarPagamentoDto.IdEstabelecimento);

            if (idContaUsuarioDigital == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Conta Digital não encontrada.");
                return;
            }

            var idEstabelecimentoProfissional = Domain.Financeiro.FechamentoFolhaMesProfissionalRepository
                .ObterIdEstabelecimentoProfissionalPeloIdFechamentoFolha(agendarPagamentoDto.IdFolhaProfissional);

            if (idEstabelecimentoProfissional == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Profissional do estabelecimento não encontrado.");
                return;
            }

            var aprovadorStoneDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(agendarPagamentoDto.IdEstabelecimento, Recurso.ContaDigitalAprovadorStone).EstaDisponivel;

            var statusInicial = aprovadorStoneDisponivel ? StatusPagamentoAgendadoEnum.Criado : StatusPagamentoAgendadoEnum.AguardandoAprovacao;

            var agendamento = new PagamentoAgendado(agendarPagamentoDto.DataPagamento, agendarPagamentoDto.ValorTotal, agendarPagamentoDto.IdUsuarioQueAlterou, idContaUsuarioDigital, agendarPagamentoDto.IdPessoaQueCriou, statusInicial);

            Domain.ContaDigital.PagamentoAgendadoRepository.SaveNew(agendamento);

            var pagamentoFolha = new PagamentoAgendadoFolhaMesProfissional(agendarPagamentoDto.IdFolhaProfissional, agendamento.Id, idEstabelecimentoProfissional);

            Domain.ContaDigital.PagamentoAgendadoFolhaMesProfissionalRepository.SaveNew(pagamentoFolha);

            RealizarTransferenciaSeAprovadorStoneEstiverDisponivel(idEstabelecimentoProfissional, agendamento.Id, agendarPagamentoDto, aprovadorStoneDisponivel);
        }

        private void RealizarTransferenciaSeAprovadorStoneEstiverDisponivel(int idEstabelecimentoProfissional, int idPagamentoAgendado, AgendarPagamentoDTO dto, bool aprovadorStoneDisponivel)
        {
            if (aprovadorStoneDisponivel)
            {
                var transferenciaDto = new TransferenciaDTO(idEstabelecimentoProfissional, dto.ValorTotal, idPagamentoAgendado, TipoTransferenciaEnum.Pix);

                var listaTransferencias = new List<TransferenciaDTO>
                {
                    transferenciaDto
                };

                Domain.ContaDigital.TransferenciaService.AgendarTransferencias(listaTransferencias, dto.IdEstabelecimento);
            }
        }

        public bool ExistePagamentoAgendadoAguardandoPagamentoOuComFalha(int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            var estabelecimentoUsaAprovadorContaDigital = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                            .ObterDisponibilidadeDeRecurso(idEstabelecimento, Recurso.ContaDigitalAprovadorStone).EstaDisponivel;

            if (estabelecimentoUsaAprovadorContaDigital)
            {
                return Domain.ContaDigital.PagamentoAgendadoRepository.ExistePagamentoAgendadosPendentesComAprovadorExterno(idEstabelecimentoProfissional, idEstabelecimento);
            }

            return Domain.ContaDigital.PagamentoAgendadoRepository.ExistePagamentoAgendadoAguardandoPagamentoOuComFalha(idEstabelecimentoProfissional, idEstabelecimento);
        }
    }
}