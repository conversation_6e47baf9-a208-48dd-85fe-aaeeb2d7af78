﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.DTO.LogDTO;
using Perlink.Trinks.ContaDigital.Enums;
using Perlink.Trinks.ContaDigital.Repositories;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.ContaDigital.Services
{

    public class ChavePixService : BaseService, IChavePixService
    {
        #region Variáveis

        private readonly IChavePixRepository _chavePixRepository;
        private readonly IChavePixProfissionalRepository _chavePixProfissionalRepository;
        private readonly IChavePixApplicationService _chavePixApplicationService;
        private readonly IContaEstabelecimentoRepository _contaEstabelecimentoRepository;
        private readonly IChavePixContaDigitalRepository _chavePixContaDigitalRepository;
        private readonly ValidationHelper _validationHelper;

        #endregion

        #region Construtores

        public ChavePixService()
        {
            _chavePixRepository = Domain.ContaDigital.ChavePixRepository;
            _chavePixProfissionalRepository = Domain.ContaDigital.ChavePixProfissionalRepository;
            _chavePixApplicationService = Domain.ContaDigital.ChavePixApplicationService;
            _contaEstabelecimentoRepository = Domain.ContaDigital.ContaEstabelecimentoRepository;
            _chavePixContaDigitalRepository = Domain.ContaDigital.ChavePixContaDigitalRepository;
            _validationHelper = ValidationHelper.Instance;
        }

        public ChavePixService(IChavePixRepository chavePixRepository, IChavePixProfissionalRepository chavePixProfissionalRepository, IChavePixApplicationService chavePixApplicationService, IContaEstabelecimentoRepository contaEstabelecimentoRepository, IChavePixContaDigitalRepository chavePixContaDigitalRepository, ValidationHelper validationHelper)
        {
            _chavePixRepository = chavePixRepository;
            _chavePixProfissionalRepository = chavePixProfissionalRepository;
            _chavePixApplicationService = chavePixApplicationService;
            _contaEstabelecimentoRepository = contaEstabelecimentoRepository;
            _chavePixContaDigitalRepository = chavePixContaDigitalRepository;
            _validationHelper = validationHelper;
        }

        #endregion

        public ChavePixProfissional ObterChavePixDoProfissional(int idEstabelecimentoProfissional)
        {
            var chavePix = _chavePixProfissionalRepository.ObterAtivaPorIdEstabelecimentoProfissional(idEstabelecimentoProfissional);

            if (chavePix == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Não existe nenhuma chave pix associada a este profissional");

            return chavePix;
        }

        public async Task<ChavePixDTO> CadastrarNovaChaveContaDigital(ChavePixDTO dto)
        {
            var dadosNovaChave = await _chavePixApplicationService.CadastrarNovaChaveParaEstabelecimento(dto);

            if (!ValidationHelper.Instance.IsValid) return null;

            dto.IdExterno = dadosNovaChave?.IdExterno;

            dto.Chave = dadosNovaChave?.Chave ?? null;

            var dtoFormatado = RemoverFormatacaoPeloTipoDaChave(dto);

            var novaChave = new ChavePix(dtoFormatado);

            _chavePixRepository.SaveNew(novaChave);

            var idContaDigital = _contaEstabelecimentoRepository.ObterIdContaDigitalPorIdEstabelecimento(dto.IdEstabelecimento);

            var novaChaveConta = new ChavePixContaDigital(
                novaChave.Id,
                idContaDigital,
                dto.IdUsuarioQueAlterou
            );

            RegistrarCriacaoChavePix(novaChaveConta.IdContaDigital, novaChave.Id);

            _chavePixContaDigitalRepository.SaveNew(novaChaveConta);

            return dto;
        }

        public async Task<DadosChavePixExterna> ObterDadosChavePixExterna(DadosParaBuscarChavePixExterna dto)
        {
            var dtoChavePix = new ChavePixDTO
            {
                Chave = dto.ChavePixExterna.Trim(),
                Tipo = ObterTipoChavePix(dto.ChavePixExterna)
            };

            var chaveFormatada = RemoverFormatacaoPeloTipoDaChave(dtoChavePix);

            if (dtoChavePix.Tipo == TipoChavePixEnum.Telefone)
            {
                dto.ChavePixExterna = "+55" + chaveFormatada.Chave;
            }
            else
            {
                dto.ChavePixExterna = chaveFormatada.Chave;
            }

            var dadosNovaChave = await _chavePixApplicationService.ObterDadosChavePixExterna(dto);

            if (dadosNovaChave.ChavePixExterna == null) return null;

            var dadosChaveExterna = new DadosChavePixExterna
            {
                Chave = dadosNovaChave.ChavePixExterna,
                NomeTitularDaConta = dadosNovaChave.Proprietario.NomeTitular,
                NumeroDocumento = dadosNovaChave.Proprietario.NumeroDocumento,
                TipoDocumento = dadosNovaChave.Proprietario.TipoDocumento,
                DadosDaContaExterna = new DadosDaContaChavePixExterna
                {
                    NomeDoBanco = dadosNovaChave.DadosDaContaDaChavePixExterna.NomeDoBanco,
                }
            };

            return dadosChaveExterna;
        }

        private void ValidarDadosNovaChavePixProfissional(int idEstabelecimentoProfissional)
        {
            var profissionalJaTemChaveCadastradaAtiva = _chavePixProfissionalRepository.ProfissionalJaTemChaveCadastradaAtiva(idEstabelecimentoProfissional);

            if (profissionalJaTemChaveCadastradaAtiva)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Este profissional já possui chave Pix cadastrada ativa.");
            }
        }

        public List<ProfissionalDTO> ObterProfissionaisAtivosSemChaveCadastrada(int idEstabelecimento)
        {
            var profissionaisAtivos = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarAtivosPorEstabelecimento(idEstabelecimento)
                     .Select(p => new ProfissionalDTO
                     {
                         Nome = p.Profissional.PessoaFisica.NomeCompleto,
                         IdEstabelecimentoProfissional = p.Codigo
                     });

            var idsEstabelecimentoProfissionalAtivos = profissionaisAtivos.Select(p => p.IdEstabelecimentoProfissional).ToList();

            var idsEstabelecimentoProfissionalComChavePix = Domain.ContaDigital.ChavePixProfissionalRepository.ObterQueryableChavesPixProfissionaisDosProfissionaisAtivos(idEstabelecimento)
                .Where(p => idsEstabelecimentoProfissionalAtivos.Contains(p.IdEstabelecimentoProfissional))
                .Select(c => c.IdEstabelecimentoProfissional).ToList();

            var profissionaisAtivosSemChavePix = profissionaisAtivos
                .Where(p => !idsEstabelecimentoProfissionalComChavePix.Contains(p.IdEstabelecimentoProfissional))
                .ToList();

            return profissionaisAtivosSemChavePix;
        }

        public bool VerificarSeNovaChaveJaFoiSalva(string novaChavePix, int idEstabelecimento)
        {
            var dto = new ChavePixDTO
            {
                Chave = novaChavePix.Trim(),
                Tipo = ObterTipoChavePix(novaChavePix),
                IdEstabelecimento = idEstabelecimento,
            };

            var chaveTratada = RemoverFormatacaoPeloTipoDaChave(dto);

            if (chaveTratada.Tipo == TipoChavePixEnum.Telefone)
            {
                chaveTratada.Chave = "+55" + chaveTratada.Chave;
            }

            var chaveJaExisteEEmUso = Domain.ContaDigital.ChavePixRepository.VerificarSeNovaChaveJaExisteEEmUsoNoMesmoEstabelecimento(chaveTratada);

            return chaveJaExisteEEmUso;
        }

        public void CadastrarNovaChavePixProfissional(int idEstabelecimentoProfissional, string chavePixProfissional, int idUsuarioContaDigital)
        {
            ValidarDadosNovaChavePixProfissional(idEstabelecimentoProfissional);

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            var dto = new ChavePixDTO
            {
                Chave = chavePixProfissional.Trim(),
                Tipo = ObterTipoChavePix(chavePixProfissional),
                IdExterno = null,
            };

            var dtoFormatado = RemoverFormatacaoPeloTipoDaChave(dto);

            var novaChave = new ChavePix(dtoFormatado);

            novaChave.AtualizarStatus(StatusChavePixEnum.Aprovado);

            _chavePixRepository.SaveNew(novaChave);

            var chaveProfissional = new ChavePixProfissional(novaChave.Id, idEstabelecimentoProfissional, idUsuarioContaDigital);

            _chavePixProfissionalRepository.SaveNew(chaveProfissional);
        }

        public void EditarChavePixProfissional(EditarChavePixDTO editarDto)
        {
            ValidarSeProfissionalEhDoEstabelecimento(editarDto.IdEstabelecimento, editarDto.IdEstabelecimentoProfissional);

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            InativarChavePixProfissional(editarDto.IdEstabelecimentoProfissional, editarDto.IdEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
            {
                return;
            }

            CadastrarNovaChavePixProfissional(editarDto.IdEstabelecimentoProfissional, editarDto.NovaChavePix, editarDto.IdUsuarioQueAlterou);
        }

        public ChavePix ExcluirChavePixProfissional(ExcluirChavePixDTO excluirDto)
        {
            ValidarSeProfissionalEhDoEstabelecimento(excluirDto.IdEstabelecimento, excluirDto.IdEstabelecimentoProfissional);

            if (!ValidationHelper.Instance.IsValid)
            {
                return null;
            }

            var chaveInativada = InativarChavePixProfissional(excluirDto.IdEstabelecimentoProfissional, excluirDto.IdEstabelecimento);

            return chaveInativada;
        }

        private void ValidarSeProfissionalEhDoEstabelecimento(int idEstabelecimento, int idEstabelecimentoProfissional)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional);

            if (estabelecimentoProfissional == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Profissional não encontrado.");
                return;
            }

            if (estabelecimentoProfissional.Estabelecimento.IdEstabelecimento != idEstabelecimento)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível executar esta ação.");
            }
        }

        private ChavePix InativarChavePixProfissional(int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            var chavePixProfissional = Domain.ContaDigital.ChavePixProfissionalRepository.ObterAtivaPorIdEstabelecimentoProfissional(idEstabelecimentoProfissional);

            ValidarInativarChavePix(chavePixProfissional, idEstabelecimentoProfissional, idEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
            {
                return null;
            }

            chavePixProfissional.InativarChave();

            Domain.ContaDigital.ChavePixProfissionalRepository.Update(chavePixProfissional);

            var chavePix = Domain.ContaDigital.ChavePixRepository.Load(chavePixProfissional.IdChavePix);

            chavePix.InativarChave();

            Domain.ContaDigital.ChavePixRepository.Update(chavePix);

            return chavePix;
        }

        private void ValidarInativarChavePix(ChavePixProfissional chavePixProfissional, int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            var existePagamentosAgendadosParaEstabelecimentoProfissional = Domain.ContaDigital.PagamentoAgendadoRepository.ExistePagamentoAgendadoParaEstabelecimentoProfissional(idEstabelecimentoProfissional, idEstabelecimento);

            if (existePagamentosAgendadosParaEstabelecimentoProfissional)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não será possível alterar esta chave PIX, pois existem pagamentos agendados vinculados a ela.");
            }

            if (chavePixProfissional == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Chave pix não encontrada.");
            }
        }

        public InfosParaExcluirOuEditarChaveProfissionalDTO ObterInfosParaExcluirOuEditarChavePixProfissional(int idEstabelecimentoProfissional)
        {
            var chavePix = Domain.ContaDigital.ChavePixRepository.ObterAtivaPorIdEstabelecimentoProfissional(idEstabelecimentoProfissional);

            if (chavePix == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Chave PIX não encontrada.");
                return null;
            }

            var nomeProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional).Profissional.PessoaFisica.NomeCompleto;

            var dto = new InfosParaExcluirOuEditarChaveProfissionalDTO(nomeProfissional, chavePix.Chave, chavePix.Tipo);

            return dto;
        }

        public ChavePixDTO RemoverFormatacaoPeloTipoDaChave(ChavePixDTO chaveDTO)
        {
            var tipo = chaveDTO.Tipo;

            switch (tipo)
            {
                case TipoChavePixEnum.Cpf:
                    chaveDTO.Chave = chaveDTO.Chave.RemoverFormatacaoCPFeCPNJ();
                    return chaveDTO;
                case TipoChavePixEnum.Cnpj:
                    chaveDTO.Chave = chaveDTO.Chave.RemoverFormatacaoCPFeCPNJ();
                    return chaveDTO;
                case TipoChavePixEnum.Telefone:
                    chaveDTO.Chave = chaveDTO.Chave.SomenteNumeros();
                    return chaveDTO;
                default:
                    chaveDTO.Chave = chaveDTO.Chave?.Trim();
                    return chaveDTO;
            }
        }

        public TipoChavePixEnum ObterTipoChavePix(string chave)
        {
            chave = chave.Trim();

            if (chave.ValidarCPF()) return TipoChavePixEnum.Cpf;

            if (chave.EmailValido()) return TipoChavePixEnum.Email;

            if (chave.EhUmTelefoneCelularComDDD()) return TipoChavePixEnum.Telefone;

            if (chave.ValidarCnpj()) return TipoChavePixEnum.Cnpj;

            return TipoChavePixEnum.Aleatoria;
        }

        public ResultadoPaginado<ChavePixProfissionalDTO> ObterChavesPixProfissionais(FiltroListagemChavePixProfissionalDTO filtro)
        {
            var query = ObterQueryRetorno(filtro);

            var pag = filtro.Paginacao;
            pag.TotalItens = query.Count();
            query = query.Skip(pag.RegistroInicial - 1).Take(pag.RegistrosPorPagina);

            var retorno = query.ToList();

            return new ResultadoPaginado<ChavePixProfissionalDTO>(retorno, filtro.Paginacao);
        }

        private IQueryable<ChavePixProfissionalDTO> ObterQueryRetorno(FiltroListagemChavePixProfissionalDTO filtro)
        {
            var queryGeral = Domain.ContaDigital.ChavePixProfissionalRepository.ObterQueryableChavesPixProfissionaisDosProfissionaisAtivos(filtro.IdEstabelecimento);

            queryGeral = ObterQueryFiltrada(queryGeral, filtro);

            queryGeral = ObterQueryOrdenada(queryGeral, filtro);

            return queryGeral;
        }

        private IQueryable<ChavePixProfissionalDTO> ObterQueryFiltrada(IQueryable<ChavePixProfissionalDTO> query, FiltroListagemChavePixProfissionalDTO filtro)
        {
            if (!string.IsNullOrEmpty(filtro.NomeProfissional))
            {
                query = query.Where(q => q.NomeCompleto.Contains(filtro.NomeProfissional));
            }

            if (!string.IsNullOrEmpty(filtro.ChavePixProfissional))
            {
                query = query.Where(q => q.ChavePix.Contains(filtro.ChavePixProfissional));
            }

            return query;
        }

        private IQueryable<ChavePixProfissionalDTO> ObterQueryOrdenada(IQueryable<ChavePixProfissionalDTO> query, FiltroListagemChavePixProfissionalDTO filtro)
        {
            if (filtro.OrdenarPor == OrdenarChavesPixProfissionalEnum.NomeProfissional)
            {
                if (filtro.SentidoOrdenacao == SentidoOrdenacaoEnum.Ascendente) return query.OrderBy(q => q.NomeCompleto);

                return query.OrderByDescending(q => q.NomeCompleto);
            }

            if (filtro.OrdenarPor == OrdenarChavesPixProfissionalEnum.ChavePixProfissional)
            {
                return query.OrderBy(q => q.ChavePix);
            }

            return query;
        }

        public List<ChavePixContaDigitalDTO> ObterChavesPixContaDigital(int idEstabelecimento)
        {
            var chavesPixContaDigital = _chavePixRepository.ObterChavesPixDaContaDigital(idEstabelecimento);

            return chavesPixContaDigital;
        }

        public bool VerficarSeExistemChavesProfissionaisSalvas(int idEstabelecimento)
        {
            var existe = _chavePixProfissionalRepository.VerificarSeExistemChavePixProssionalSalvas(idEstabelecimento);

            return existe;
        }

        public void AtualizarChavePix(DadosChavePix dadosChavePix)
        {
            var chavePix = Domain.ContaDigital.ChavePixRepository.ObterChavePixPorIdExterno(dadosChavePix.IdChavePixExterno);

            RegistrarAlteracaoStatusChavePixCriadaParaConta(chavePix.Id, dadosChavePix.NovoStatusChave);

            if (Enum.TryParse(dadosChavePix.NovoStatusChave, out StatusChavePixEnum statusChavePix))
            {
                chavePix.AtualizarStatus(statusChavePix);
                chavePix.AtualizarChave(dadosChavePix.ChavePix);

                Domain.ContaDigital.ChavePixRepository.Update(chavePix);
            }
            else
            {
                throw new NotImplementedException("[Webhook Trampol.in] Erro ao atualizar status da chave PIX. O status recebido não existe no StatusChavePixEnum.");
            }
        }

        private void RegistrarAlteracaoStatusChavePixCriadaParaConta(int idChavePix, string novoStatusChave)
        {
            var idContaDigital = Domain.ContaDigital.ChavePixRepository.ObterIdContaDigitalDaChavePix(idChavePix);

            var dto = new RegistroAlteracaoStatusChavePixDTO(idChavePix, novoStatusChave, idContaDigital);

            Domain.ContaDigital.LogContaDigitalService.RegistrarLog(dto);
        }

        private void RegistrarCriacaoChavePix(int idContaDigital, int idChavePix)
        {
            var dto = new RegistroCriacaoChavePixDTO(idChavePix, idContaDigital);
            Domain.ContaDigital.LogContaDigitalService.RegistrarLog(dto);
        }
    }
}
