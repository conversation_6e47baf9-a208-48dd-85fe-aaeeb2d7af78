﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.ContaDigital.Services
{
    public interface IChavePixService : IService
    {
        ChavePixProfissional ObterChavePixDoProfissional(int idEstabelecimntoProfissional);

        Task<ChavePixDTO> CadastrarNovaChaveContaDigital(ChavePixDTO dto);

        Task<DadosChavePixExterna> ObterDadosChavePixExterna(DadosParaBuscarChavePixExterna dto);

        TipoChavePixEnum ObterTipoChavePix(string chave);

        void CadastrarNovaChavePixProfissional(int idEstabelecimentoProfissional, string chavePixProfissional, int idUsuarioContaDigital);

        ResultadoPaginado<ChavePixProfissionalDTO> ObterChavesPixProfissionais(FiltroListagemChavePixProfissionalDTO filtro);

        List<ChavePixContaDigitalDTO> ObterChavesPixContaDigital(int idEstabelecimento);

        bool VerficarSeExistemChavesProfissionaisSalvas(int idEstabelecimento);

        ChavePixDTO RemoverFormatacaoPeloTipoDaChave(ChavePixDTO chaveDTO);

        void AtualizarChavePix(DadosChavePix dadosChavePix);

        bool VerificarSeNovaChaveJaFoiSalva(string novaChavePix, int idEstabelecimento);

        List<ProfissionalDTO> ObterProfissionaisAtivosSemChaveCadastrada(int idEstabelecimento);

        ChavePix ExcluirChavePixProfissional(ExcluirChavePixDTO excluirDto);

        void EditarChavePixProfissional(EditarChavePixDTO editarDto);

        InfosParaExcluirOuEditarChaveProfissionalDTO ObterInfosParaExcluirOuEditarChavePixProfissional(int idEstabelecimentoProfissional);
    }
}
