﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ContaDigital.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.ContaDigital.Services
{
    public interface ITransferenciaService : IService
    {
        void AgendarTransferencias(List<TransferenciaDTO> transferencias, int idEstabelecimento);

        int CriarTransferenciaViaPix(RetornoTranferenciaPixDTO dadosRetorno, int idChavePixDestinatario);

        void AtualizarTranferencia(long idTransferenciaGateway, string novoStatus);
        void CancelarTransferencias(List<(PagamentoAgendado, Transferencia)> pagamentos);
    }
}
