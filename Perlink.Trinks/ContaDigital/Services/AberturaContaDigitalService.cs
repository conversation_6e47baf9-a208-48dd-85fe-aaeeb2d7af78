﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ConciliacaoBancaria;
using Perlink.Trinks.ConciliacaoBancaria.Enums;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.ContaDigital.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Threading.Tasks;

namespace Perlink.Trinks.ContaDigital.Services
{
    public class AberturaContaDigitalService : BaseService, IAberturaContaDigitalService
    {
        public string ObterNumeroDoSuporte()
        {
            var telefoneSuporte = new ParametrosTrinks<string>(ParametrosTrinksEnum.numero_de_contato_do_suporte).ObterValor();

            return telefoneSuporte;
        }

        public async Task<bool> CriarNovaContaDigital(DadosFormDTO dados, int idEstabelecimento)
        {

            ValidarDados(dados);

            if (!ValidationHelper.Instance.IsValid)
                return false;

            var criarNovaContaDigitalDTO = new CriarNovaContaDigitalDTO(dados);

            try
            {
                int? idContaDigital = await Domain.ContaDigital.AberturaContaDigitalApplicationService.CriarContaDigital(criarNovaContaDigitalDTO);

                if (!ValidationHelper.Instance.IsValid || idContaDigital is null)
                    return false;

                var contaEstabelecimento = new ContaEstabelecimento(idEstabelecimento, idContaDigital.Value);

                Domain.ContaDigital.ContaEstabelecimentoRepository.SaveNew(contaEstabelecimento);

                AtualizarEtapaAtualCadastro(EtapaCadastroEnum.VerificacaoDeIdentidade, dados.NumeroTelefoneResponsavel, idEstabelecimento);

                return true;
            }
            catch (Exception ex)
            {
                Elmah.ErrorSignal.FromCurrentContext().Raise(new Exception($"[ContaDigital] - Erro na criação da conta digital. Retorno foi: {ex.Message}"));

                return false;
            }
        }

        private void ValidarDados(DadosFormDTO dados)
        {

            #region Dados do Responsável

            if (string.IsNullOrEmpty(dados.NomeResponsavel))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O nome do responsável é obrigatório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.NomeResponsavel) && dados.NomeResponsavel.Length < 10)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um nome válido para o responsável.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.NomeResponsavel) && dados.NomeResponsavel.SomenteNumeros().Length > 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O nome do responsável não pode conter números.");
                return;
            }

            if (string.IsNullOrEmpty(dados.NumeroTelefoneResponsavel))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O telefone do responsável é obrigatório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.NumeroTelefoneResponsavel) && !dados.NumeroTelefoneResponsavel.NumeroDeCelularComDDDEstaValido())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um número de celular válido para o responsável.");
                return;
            }

            if (string.IsNullOrEmpty(dados.EmailResponsavel))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O e-mail do responsável é obrigatório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.EmailResponsavel) && !dados.EmailResponsavel.EmailValido())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um e-mail válido para o responsável.");
                return;
            }

            if (string.IsNullOrEmpty(dados.DocumentoResponsavel))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O documento do responsável é obrigátório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.DocumentoResponsavel) && dados.TipoDocumentoResponsavel == TipoDocumentoEnum.CPF)
            {
                if (!dados.DocumentoResponsavel.SomenteNumeros().ValidarCPF())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um CPF válido para o responsável.");
                    return;
                }
            }

            if (!string.IsNullOrEmpty(dados.DocumentoResponsavel) && dados.TipoDocumentoResponsavel == TipoDocumentoEnum.CNPJ)
            {
                if (!dados.DocumentoResponsavel.SomenteNumeros().ValidarCnpj())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um CNPJ válido para o responsável.");
                    return;
                }
            }

            if (!dados.DataNascimentoResponsavel.DataDeNascimentoEstaValida())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira uma data de nascimento válida para o responsável.");
                return;
            }

            #endregion

            #region Dados do Dono

            if (string.IsNullOrEmpty(dados.NomeDono))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O nome da empresa é obrigatório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.NomeDono) && dados.NomeDono.Length < 10)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um nome válido para a empresa.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.NomeDono) && dados.NomeDono.SomenteNumeros().Length > 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O nome da empresa não pode conter números.");
                return;
            }

            if (string.IsNullOrEmpty(dados.DocumentoDono))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O documento da empresa é obrigátório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.DocumentoDono) && dados.TipoDocumentoDono == TipoDocumentoEnum.CPF)
            {
                if (!dados.DocumentoDono.SomenteNumeros().ValidarCPF())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um CPF válido para o dono.");
                    return;
                }
            }

            if (!string.IsNullOrEmpty(dados.DocumentoDono) && dados.TipoDocumentoDono == TipoDocumentoEnum.CNPJ)
            {
                if (!dados.DocumentoDono.SomenteNumeros().ValidarCnpj())
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um CNPJ válido para a empresa.");
                    return;
                }
            }

            if (string.IsNullOrEmpty(dados.NumeroTelefoneDono))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O telefone do estabelecimento é obrigatório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.NumeroTelefoneDono) && !dados.NumeroTelefoneDono.NumeroDeCelularComDDDEstaValido())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um número de celular válido para o estabelecimento.");
                return;
            }

            if (string.IsNullOrEmpty(dados.EmailDono))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O e-mail do estabelecimento é obrigatório.");
                return;
            }

            if (!string.IsNullOrEmpty(dados.EmailDono) && !dados.EmailDono.EmailValido())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um e-mail válido para o estabelecimento.");
                return;
            }

            if (!dados.DataNascimentoDono.DataDeNascimentoEstaValida())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira uma data válida para a abertura da empresa.");
                return;
            }

            #endregion

            if (dados.NumeroTelefoneDono.SomenteNumeros() == dados.NumeroTelefoneResponsavel.SomenteNumeros())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O telefone do responsável e do estabelecimento não podem sem iguais.");
                return;
            }

            if (dados.EmailDono.Trim().ToLower() == dados.EmailResponsavel.Trim().ToLower())
            {
                ValidationHelper.Instance.AdicionarItemValidacao(@"O e-mail do responsável e do estabelecimento não podem sem iguais.");
                return;
            }


            #region Dados do Endereço

            if (string.IsNullOrEmpty(dados.Rua))
                ValidationHelper.Instance.AdicionarItemValidacao(@"O nome da rua é obrigatório.");

            if (string.IsNullOrEmpty(dados.NumeroEndereco))
                ValidationHelper.Instance.AdicionarItemValidacao(@"O número é obrigatório.");

            if (string.IsNullOrEmpty(dados.Bairro))
                ValidationHelper.Instance.AdicionarItemValidacao(@"O bairro é obrigatório.");

            if (string.IsNullOrEmpty(dados.Cidade))
                ValidationHelper.Instance.AdicionarItemValidacao(@"A cidade é obrigatória.");

            if (string.IsNullOrEmpty(dados.Estado))
                ValidationHelper.Instance.AdicionarItemValidacao(@"O estado é obrigatório.");

            if (string.IsNullOrEmpty(dados.Cep))
                ValidationHelper.Instance.AdicionarItemValidacao(@"O CEP é obrigatório.");

            if (!string.IsNullOrEmpty(dados.Cep) && dados.Cep.SomenteNumeros().Length != 8)
                ValidationHelper.Instance.AdicionarItemValidacao(@"Insira um CEP válido.");

            if (string.IsNullOrEmpty(dados.Pais))
                ValidationHelper.Instance.AdicionarItemValidacao(@"O país é obrigatório.");

            #endregion
        }

        public DadosParaConfirmacaoDTO CarregarDadosParaConfirmacao(int idEstabelecimento)
        {
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.ObterPorIdEstabelecimento(idEstabelecimento);

            if (contaEstabelecimento == null)
            {
                throw new ArgumentException("O estabelecimento informado não possui Conta Digital cadastrada.");
            }

            var contaDigital = Domain.ContaDigital.ContaUsuarioDigitalRepository.Load(contaEstabelecimento.IdContaDigital);
            var responsavel = Domain.ContaDigital.ContaUsuarioDigitalRepository.ObterResponsavelContaDigitalPorIdContaDigital(contaDigital.Id);

            var retorno = new DadosParaConfirmacaoDTO()
            {
                DadosDono = contaDigital.Dono,
                DadosResponsavel = responsavel,
                Endereco = contaDigital.Dono.Endereco
            };

            return retorno;
        }

        public async Task<DadosDaContaDigitalDTO> ObterDadosDaContaDigital(int idEstabelecimento)
        {
            var contaEstabelecimento = Domain.ContaDigital.ContaEstabelecimentoRepository.ObterPorIdEstabelecimento(idEstabelecimento);

            if (contaEstabelecimento == null)
            {
                throw new ArgumentException("O estabelecimento informado não possui Conta Digital cadastrada.");
            }

            return Domain.ContaDigital.ContaUsuarioDigitalRepository.ObterDadosContaDigitalPorId(contaEstabelecimento.IdContaDigital);
        }

        public async Task<DadosContaBancariaDTO> ObterDetalhesContaBancariaExterna(int idContaDigitalExterno)
        {
            var detalhesConta = await Domain.ContaDigital.AberturaContaDigitalApplicationService.ObterDetalhesDaContaBancariaExterna(idContaDigitalExterno);

            return detalhesConta;
        }

        public EtapaCadastroDTO ObterEtapaAtualCadastro(int idEstabelecimento)
        {
            var etapaAtual = Domain.ContaDigital.EtapaCadastroRepository.ObterPorIdEstabelecimento(idEstabelecimento);

            if (etapaAtual == null)
            {
                var novaEtapaCadastro = new EtapaCadastro(idEstabelecimento);
                Domain.ContaDigital.EtapaCadastroRepository.SaveNew(novaEtapaCadastro);

                return new EtapaCadastroDTO(novaEtapaCadastro);
            }

            VerificarInconsistenciaNaEtapaDeAutenticacaoDeTelefone(etapaAtual);

            return new EtapaCadastroDTO(etapaAtual);
        }

        public void AtualizarEtapaAtualCadastro(EtapaCadastroEnum novaEtapa, string novoTelefone, int idEstabelecimento)
        {
            var etapa = Domain.ContaDigital.EtapaCadastroRepository.ObterPorIdEstabelecimento(idEstabelecimento);
            etapa.AtualizarEtapa(novaEtapa);

            if (novaEtapa == EtapaCadastroEnum.AutenticarTelefone)
            {
                etapa.AtualizarTelefoneValidacao(novoTelefone);
            }

            if (novaEtapa == EtapaCadastroEnum.InserirTelefone)
            {
                etapa.AtualizarTelefoneValidacao(string.Empty);
            }

            Domain.ContaDigital.EtapaCadastroRepository.Update(etapa);
        }

        public void CriarContaFinanceiraContaDigital(int idContaDigital)
        {
            try
            {
                var idEstabelecimento =
                    Domain.ContaDigital.ContaEstabelecimentoRepository.ObterIdEstabelecimentoPorIdContaDigital(
                        idContaDigital);

                var contaFinanceiraDoEstabelecimento = Domain.ConciliacaoBancaria.ContaFinanceiraDoEstabelecimentoRepository.ObterContaFinanceiraContaDigital(idEstabelecimento);

                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

                if (contaFinanceiraDoEstabelecimento == null)
                {
                    var idContaFinanceiraPadrao = (int)ContaFinanceiraPadraoEnum.ContaDigital;
                    var contaFinanceiraPadrao = Domain.ConciliacaoBancaria.ContaFinanceiraPadraoRepository.Load(idContaFinanceiraPadrao);
                    
                    contaFinanceiraDoEstabelecimento = new ContaFinanceiraDoEstabelecimento(estabelecimento, contaFinanceiraPadrao);
                    Domain.ConciliacaoBancaria.ContasFinanceirasService.ManterContaFinanceira(contaFinanceiraDoEstabelecimento);
                }

                CriarOuAtualizarFormaPagamentoPix(estabelecimento);
            }
            catch (Exception ex)
            {
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
            }
        }

        public EtapaCadastroEnum ObterEtapaCadastroEnumAtual(int idEstabelecimento)
        {
            return Domain.ContaDigital.EtapaCadastroRepository.ObterEtapaAtualDeCadastro(idEstabelecimento);
        }

        private static void CriarOuAtualizarFormaPagamentoPix(Estabelecimento estabelecimento)
        {
            var idFormaPagamentoPix = (int)FormaPagamentoEnum.Pix;
            var formaPagamento = Domain.Financeiro.FormaPagamentoRepository.Load(idFormaPagamentoPix);

            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaPagamento, estabelecimento);
        }

        private void VerificarInconsistenciaNaEtapaDeAutenticacaoDeTelefone(EtapaCadastro etapa)
        {
            bool ehEtapaDeAutenticacaoDeTelefone = etapa != null && etapa.EtapaAtual == EtapaCadastroEnum.AutenticarTelefone;
            
            if (!ehEtapaDeAutenticacaoDeTelefone)
                return;

            var autenticacao = Domain.ContaDigital.AutenticacaoIdentidadePreCadastroRepository.ObterPorIdEstabelecimento(etapa.IdEstabelecimento);

            if (autenticacao != null && autenticacao.JaFoiConfirmado())
            {
                etapa.AvancarEtapa();

                Domain.ContaDigital.EtapaCadastroRepository.Update(etapa);
            }
        }
    }
}
