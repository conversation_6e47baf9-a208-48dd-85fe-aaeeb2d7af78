﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Auditing;
using Perlink.Trinks.ContaDigital.Builders;
using Perlink.Trinks.ContaDigital.DTO;
using Perlink.Trinks.EnvioMensagem;
using Perlink.Trinks.EnvioMensagem.Services;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System.Collections.Generic;

namespace Perlink.Trinks.ContaDigital.Services
{
    public class AutenticacaoContaDigitalService : BaseService, IAutenticacaoContaDigitalService
    {
        public AutenticacaoIdentidadePreCadastro EnviarCodigoCadastro(AutenticacaoContaDigitalCadastroDTO autenticacaoDto)
        {
            var dadosAutenticacaoPreCadastro = ObterAutenticacaoIdentidadePreCadastro(autenticacaoDto);

            if (dadosAutenticacaoPreCadastro.JaFoiConfirmado())
            {
                AvancarEtapaDeAutenticacao(autenticacaoDto.IdEstabelecimento);

                return dadosAutenticacaoPreCadastro;
            }

            EnviarSMS(dadosAutenticacaoPreCadastro, autenticacaoDto.Telefone.SomenteNumeros());

            return dadosAutenticacaoPreCadastro;
        }

        public AutenticacaoIdentidadePreCadastro ObterAutenticacaoIdentidadePreCadastro(AutenticacaoContaDigitalCadastroDTO autenticacaoDto)
        {
            var preCadastro = Domain.ContaDigital.AutenticacaoIdentidadePreCadastroRepository.ObterPorIdEstabelecimento(autenticacaoDto.IdEstabelecimento);

            if (preCadastro is null)
            {
                preCadastro = new AutenticacaoIdentidadePreCadastro(autenticacaoDto);
                Domain.ContaDigital.AutenticacaoIdentidadePreCadastroRepository.SaveNew(preCadastro);

                return preCadastro;
            }

            var trocouTelefone = autenticacaoDto.Telefone != preCadastro.Telefone;

            if (trocouTelefone && !autenticacaoDto.PodeTrocarTelefone)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é permitido trocar o telefone.");
                return null;
            }

            if (trocouTelefone)
            {
                AtualizarTelefonePreCadastro(autenticacaoDto.Telefone, preCadastro);
            }

            return preCadastro;
        }

        private void AtualizarTelefonePreCadastro(string novoTelefone, AutenticacaoIdentidadePreCadastro preCadastro)
        {
            preCadastro.Telefone = novoTelefone;
            Domain.ContaDigital.AutenticacaoIdentidadePreCadastroRepository.Update(preCadastro);
        }

        private void EnviarSMS(AutenticacaoContaDigital autenticacaoContaDigital, string telefone)
        {

            if (!telefone.EhUmTelefoneCelularComDDD())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Número de telefone inválido.");
                return;
            }

            var codigoConfirmacao = autenticacaoContaDigital.RegistrarEnvioDeCodigo(telefone);
            Domain.ContaDigital.AutenticacaoContaDigitalRepository.Update(autenticacaoContaDigital);

            var servico = Domain.Notificacoes.NotificacaoDoTrinksService.ObterServicoSMSParaEnvioComRetornoParaValidacao();
            var urlServicoSMS = servico.Url;

            var ddd = telefone.Substring(0, 2);
            var numero = telefone.Substring(2);

            var mensagem = new MensagemSMS
            {
                DDD = int.Parse(ddd),
                Telefone = int.Parse(numero),
                Conteudo = $"{codigoConfirmacao} e seu codigo de confirmacao do Trinks.",
                Id = autenticacaoContaDigital.Id.ToString(),
                Origem = EnvioMensagem.Enums.OrigemMensagemEnum.ValidacaoDePreCadastro,
                GatewayUrl = urlServicoSMS
            };

            Domain.EnvioMensagem.MensagemService.IncluirDisparoMensagensNaFila<MensagensSMSImediatoServiceMQSQS, MensagemSMS>(new List<MensagemSMS> { mensagem });
            LogService<AutenticacaoContaDigitalService>.Info($"SMS ({mensagem.DDD}){mensagem.Telefone} / {mensagem.Conteudo}");
        }

        public ValidacaoDeCodigoVerificacaoDTO ValidarCodigo(ValidacaoDeCodigoAutenticacaoDTO dados, int idPessoa, int idEstabelecimento)
        {
            dados.Contato = dados.Contato.SomenteNumeros();

            var autenticacao = Domain.ContaDigital.AutenticacaoContaDigitalRepository.ObterPorId(dados.IdVerificacao);

            if (autenticacao == null)
                return new ValidacaoDeCodigoVerificacaoDTO(codigoCorreto: false);

            if (autenticacao.JaFoiConfirmado())
                return new ValidacaoDeCodigoVerificacaoDTO(codigoCorreto: true);

            if (autenticacao.CodigoEstaExpirado())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Este código já está expirado.");
                return new ValidacaoDeCodigoVerificacaoDTO(codigoCorreto: false);
            }

            if (!autenticacao.CodigoConfirmacaoEstaCorreto(dados.Contato, dados.Codigo))
                return CodigoIncorreto(autenticacao, idPessoa, idEstabelecimento);

            autenticacao.ConcluirVerificacao();

            autenticacao.Update();

            SalvarConfirmacao(autenticacao, dados.Contato);

            AtualizarValidadeTokenUsuarioContaDigital(autenticacao);

            return new ValidacaoDeCodigoVerificacaoDTO(codigoCorreto: true);
        }

        private ValidacaoDeCodigoVerificacaoDTO CodigoIncorreto(AutenticacaoContaDigital autenticacao, int idPessoa, int idEstabelecimento)
        {
            var usuarioContaDigital = Domain.ContaDigital.UsuarioContaDigitalRepository.ObterUsuarioContaDigital(idPessoa, idEstabelecimento);

            var validacaoDtoBuilder = new ValidacaoDeCodigoVerificacaoDTOBuilder().DefinirCodigoComoIncorreto();

            var ehCadastroContaDigital = usuarioContaDigital == null;

            if (ehCadastroContaDigital)
                return validacaoDtoBuilder.Build();

            autenticacao.IncrementarTentativa();

            autenticacao.Update();

            var maximoDeTentativas = new ParametrosTrinks<int>(ParametrosTrinksEnum.contaDigital_maximo_tentativas_autenticacao).ObterValor();

            var aplicarBloqueioPorFaltaDeTentativasRestantes = autenticacao.QuantidadeTentativas.Value >= maximoDeTentativas;

            validacaoDtoBuilder.DefinirQuantidadeDeTentativasRestantes(autenticacao.QuantidadeTentativas.Value, maximoDeTentativas);

            if (aplicarBloqueioPorFaltaDeTentativasRestantes)
            {
                var quantidadeMinutosBloqueio = new ParametrosTrinks<int>(ParametrosTrinksEnum.contaDigital_quantidade_minutos_bloqueio_autenticacao).ObterValor();

                var dataHoraBloqueioValidade = Calendario.Agora().AddMinutes(quantidadeMinutosBloqueio);

                validacaoDtoBuilder.DefinirDataHoraBloqueioValidade(dataHoraBloqueioValidade);

                usuarioContaDigital.DataBloqueioValidade = dataHoraBloqueioValidade;

                usuarioContaDigital.Update();
            }

            var validacaoDto = validacaoDtoBuilder.Build();

            return validacaoDto;
        }

        private void AtualizarValidadeTokenUsuarioContaDigital(AutenticacaoContaDigital autenticacao)
        {
            if (autenticacao is AutenticacaoIdentidadePreCadastro)
            {
                return;
            }

            var autenticacaoUsuario = Domain.ContaDigital.AutenticacaoIdentidadeUsuarioContaRepository.Load(autenticacao.Id);
            var idUsuarioConta = autenticacaoUsuario.IdUsuarioContaDigital;
            var usuario = Domain.ContaDigital.UsuarioContaDigitalRepository.Load(idUsuarioConta);

            usuario.AtualizarDataValidadeTokenSms(autenticacao.DataHoraValidade);

            Domain.ContaDigital.UsuarioContaDigitalRepository.Update(usuario);
        }

        private void SalvarConfirmacao(AutenticacaoContaDigital autenticacao, string telefone)
        {
            if (!autenticacao.JaFoiConfirmado())
                return;

            var envio = autenticacao.ObterEnvioVigente();

            var confirmacao = new AutenticacaoContaDigitalConfirmacao(autenticacao.Id, envio.Id, telefone);

            Domain.ContaDigital.AutenticacaoContaDigitalConfirmacaoRepository.SaveNew(confirmacao);
        }

        public AutenticacaoIdentidadePreCadastro ObterAutenticacaoCadastro(AutenticacaoContaDigitalCadastroDTO autenticacaoDto)
        {
            var dadosAutenticacaoPreCadastro = ObterAutenticacaoIdentidadePreCadastro(autenticacaoDto);

            return dadosAutenticacaoPreCadastro;
        }

        public DadosAutenticacaoContaDigitalDTO GerarAutenticacaoContaDigital(UsuarioContaTelefoneEIdDTO dto)
        {

            if (dto is null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Erro ao obter informações do Usuário.");
                return null;
            }

            dto.Telefone = dto.Telefone.SomenteNumeros();

            var autenticacaoId = ObterAutenticacao(dto.IdUsuarioContaDigital, dto.Telefone, false);

            var segundosParaReenvio = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterValorParametro<int>(ParametrosTrinksEnum.segundos_para_reenvio_codigo_de_autenticacao);

            return new DadosAutenticacaoContaDigitalDTO(dto.Telefone, autenticacaoId, segundosParaReenvio);
        }

        private int ObterAutenticacao(int idUsuarioContaDigital, string telefone, bool podeTrocarTelefone)
        {
            var ultimaAutenticacaoCriada = Domain.ContaDigital.AutenticacaoIdentidadeUsuarioContaRepository
                .ObterAutenticacaoPorIdUsuarioContaDigital(idUsuarioContaDigital);

            if (ultimaAutenticacaoCriada != null && !ultimaAutenticacaoCriada.DataHoraConfirmacao.HasValue)
                return ultimaAutenticacaoCriada.Id;

            var autenticacaoUsuarioContaDigital = new AutenticacaoIdentidadeUsuarioConta(idUsuarioContaDigital, telefone, podeTrocarTelefone);

            Domain.ContaDigital.AutenticacaoIdentidadeUsuarioContaRepository.UpdateOrSaveNew(autenticacaoUsuarioContaDigital);

            return autenticacaoUsuarioContaDigital.IdAutenticacaoContaDigital;
        }

        public void EnviarCodigoAutenticacao(int idVerificacao, int idPessoa, int idEstabelecimento)
        {
            var autenticacao = Domain.ContaDigital.AutenticacaoContaDigitalRepository.ObterPorId(idVerificacao);

            if (!autenticacao.QuantidadeTentativas.HasValue)
            {
                autenticacao.QuantidadeTentativas = 0;
                autenticacao.Update();
            }

            if (autenticacao.JaFoiConfirmado())
            {
                AvancarEtapaDeAutenticacao(idEstabelecimento);

                return;
            }

            ResetarQuantidadeDeTentativasSeAcabouBloqueio(autenticacao, idPessoa, idEstabelecimento);

            EnviarSMS(autenticacao, autenticacao.Telefone.SomenteNumeros());
        }

        public bool UsuarioEstaValidado(int idExternoTrinks, int idEstabelecimento)
        {
            var dataValidade = Domain.ContaDigital.UsuarioContaDigitalRepository.ObterDataValidadeTokenSms(idExternoTrinks, idEstabelecimento);

            var dataHoraAtual = Calendario.Agora();

            return dataValidade.HasValue && dataValidade.Value > dataHoraAtual;
        }

        public InformacoesBloqueioAutenticacaoDTO ObterInformacoesDeBloqueioDeAutenticacao(int idPessoa, int idEstabelecimento)
        {
            var usuarioContaDigital = Domain.ContaDigital.UsuarioContaDigitalRepository.ObterUsuarioContaDigital(idPessoa, idEstabelecimento);

            var resultado = new InformacoesBloqueioAutenticacaoDTO
            {
                TempoEmMinutosDoBloqueio = new ParametrosTrinks<int>(ParametrosTrinksEnum.contaDigital_quantidade_minutos_bloqueio_autenticacao).ObterValor(),

                EstaBloqueadoParaAutenticacao = usuarioContaDigital.EstaBloqueadoParaAutenticacao(),

                DataHoraBloqueioValidade = usuarioContaDigital.DataBloqueioValidade
            };

            if (resultado.EstaBloqueadoParaAutenticacao && resultado.DataHoraBloqueioValidade.HasValue)
            {
                var tempoRestanteDeBloqueio = resultado.DataHoraBloqueioValidade.Value - Calendario.Agora();

                resultado.TempoRestanteDoBloqueioEmSegundos = (int)tempoRestanteDeBloqueio.TotalSeconds;
            }

            return resultado;
        }

        private void ResetarQuantidadeDeTentativasSeAcabouBloqueio(AutenticacaoContaDigital autenticacao, int idPessoa, int idEstabelecimento)
        {
            var usuarioContaDigital = Domain.ContaDigital.UsuarioContaDigitalRepository.ObterUsuarioContaDigital(idPessoa, idEstabelecimento);

            var precisaResetarAutenticacao =
                usuarioContaDigital.DataBloqueioValidade.HasValue &&
                Calendario.Agora() > usuarioContaDigital.DataBloqueioValidade;

            if (precisaResetarAutenticacao)
            {
                autenticacao.QuantidadeTentativas = 0;

                autenticacao.Update();

                usuarioContaDigital.DataBloqueioValidade = null;

                usuarioContaDigital.Update();
            }
        }

        private void AvancarEtapaDeAutenticacao(int idEstabelecimento)
        {
            var etapa = Domain.ContaDigital.EtapaCadastroRepository.ObterPorIdEstabelecimento(idEstabelecimento);

            if (etapa == null)
            {
                LogService<AutenticacaoContaDigitalService>.Error(
                    $"Etapa de cadastro da conta digital não encontrada para o estabelecimento {idEstabelecimento}.");
                
                return;
            }

            if (etapa.EtapaAtual == Enums.EtapaCadastroEnum.AutenticarTelefone)
                etapa.AvancarEtapa();
        }
    }
}
