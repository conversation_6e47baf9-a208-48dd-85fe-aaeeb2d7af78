﻿using Perlink.GatewayPagamento;
using Perlink.GatewayPagamento.CobreBem.Services;
using Perlink.GatewayPagamento.Enum;
using Perlink.GatewayPagamento.PagarMe.Services;
using Perlink.GatewayPagamento.Services;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using System;

namespace Perlink.Trinks.Cobranca.Factories
{

    public static class GatewayFaturaFactory
    {

        public static ProcessamentoService CriarGatewayParaNovasAssinaturas(bool pagarmeV5Disponivel)
        {
            GatewayEnum gatewayConfigurado = ObterGatewayParaNovasAssinaturas(pagarmeV5Disponivel);
            return CriarGateway(gatewayConfigurado);
        }

        public static ProcessamentoService CriarGatewayParaPagamentosPix()
        {
            GatewayEnum gatewayConfigurado = ObterGatewayParaPagamentosPix();
            return CriarGateway(gatewayConfigurado);
        }

        public static ProcessamentoService CriarGatewayParaPagamentosRecorrentes(bool pagarmeV5Disponivel,
            GatewayEnum gatewayUtilizadoAnteriormente)
        {
            if (pagarmeV5Disponivel)
            {
                return CriarGateway(GatewayEnum.PagarMeV5);
            }
            
            if (gatewayUtilizadoAnteriormente == GatewayEnum.PagarMeV5)
            {
                return CriarGateway(GatewayEnum.PagarMe);
            }
            
            return CriarGateway(gatewayUtilizadoAnteriormente);
        }

        private static ProcessamentoService CriarGateway(GatewayEnum gateway)
        {
            switch (gateway)
            {
                case GatewayEnum.CobreBem:
                    return InstanciarServiceCobreBemNoAmbienteConfigurado();

                case GatewayEnum.PagarMe:
                    return InstanciarServicePagarMe(ObterConfiguracaoGatewayFatura(gateway));
                
                case GatewayEnum.PagarMeV5:
                    return InstanciarServicePagarMeV5(ObterConfiguracaoGatewayFatura(gateway));

                default:
                    throw new InvalidOperationException("O gateway configurado não é suportado");
            }
        }

        public static ProcessamentoService CriarGatewayEmHomologacao(GatewayEnum gateway)
        {
            switch (gateway)
            {
                case GatewayEnum.CobreBem:
                    return InstanciarServiceCobreBemEmHomologacao();

                case GatewayEnum.PagarMe:
                    return InstanciarServicePagarMe(ObterConfiguracaoGatewayFatura(gateway));
                
                case GatewayEnum.PagarMeV5:
                    return InstanciarServicePagarMeV5(ObterConfiguracaoGatewayFatura(gateway));

                default:
                    throw new InvalidOperationException("O gateway configurado não é suportado em modo homologação");
            }
        }

        private static ProcessamentoCobreBemService InstanciarServiceCobreBemNoAmbienteConfigurado()
        {
            return new ProcessamentoCobreBemService(ConfiguracoesTrinks.Cobranca.LoginCobrebem, ConfiguracoesTrinks.Cobranca.HabilitaHomologacaoCobrebem);
        }

        private static ProcessamentoCobreBemService InstanciarServiceCobreBemEmHomologacao()
        {
            return new ProcessamentoCobreBemService(ConfiguracoesTrinks.Cobranca.LoginCobrebem, true);
        }

        private static ProcessamentoPagarMeService InstanciarServicePagarMe(ConfiguracaoGatewayFatura configuracao)
        {
            
            return new ProcessamentoPagarMeService(configuracao);
        }
        
        private static ProcessamentoPagarMeV5Service InstanciarServicePagarMeV5(ConfiguracaoGatewayFatura configuracao)
        {
            
            return new ProcessamentoPagarMeV5Service(configuracao);
        }

        private static GatewayEnum ObterGatewayParaNovasAssinaturas(bool pagarmeV5Disponivel)
        {
            string gateway = pagarmeV5Disponivel 
                        ? new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_fatura_novas_assinaturas).ObterValor() 
                        : new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_novas_assinaturas).ObterValor();
            
            return (GatewayEnum)Enum.Parse(typeof(GatewayEnum), gateway);
        }

        private static GatewayEnum ObterGatewayParaPagamentosPix()
        {
            string gateway = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_fatura_pix).ObterValor();
            return (GatewayEnum)Enum.Parse(typeof(GatewayEnum), gateway);
        }

        public static ConfiguracaoGatewayFatura ObterConfiguracaoGatewayFatura(GatewayEnum gateway)
        {
            var configuracao = new ConfiguracaoGatewayFatura();

            switch (gateway)
            {
                case GatewayEnum.PagarMe:
                    configuracao.ApiKey = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_pagarme_api_key).ObterValor();
                    configuracao.IdRecebedorTrinks = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_pagarme_id_recebedor_trinks).ObterValor();
                    configuracao.VersionHeader = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgtoOnline_pagarme_v3_version_header).ObterValor();
                    break;
                case GatewayEnum.PagarMeV5:
                    configuracao.ApiKey = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_fatura_pagarmev5_api_key).ObterValor();
                    configuracao.IdRecebedorTrinks = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_fatura_pagarmev5_id_recebedor_trinks).ObterValor();
                    configuracao.WebhookUsername = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_fatura_webhook_username).ObterValor();
                    configuracao.WebhookPassword = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgto_gateway_fatura_webhook_password).ObterValor();
                    break;
                case GatewayEnum.CobreBem:
                default:
                    throw new ArgumentOutOfRangeException(nameof(gateway), gateway, null);
            }
            
            return configuracao;
        }
    }
}