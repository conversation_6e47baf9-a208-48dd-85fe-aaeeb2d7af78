﻿using Perlink.Shared.Auditing;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cobranca.Repositories
{

    public partial class ContaFinanceiraRepository : IContaFinanceiraRepository
    {

        public bool PessoaJuridicaEstahAdimplente(int idPessoa)
        {
            return Queryable().Any(f => f.Ativo && f.Pessoa.IdPessoa == idPessoa
                && (new[] { 1, 3, 4, 9 }).Contains(f.Status.IdStatus));
        }

        public IList<ContaFinanceira> ObterTodasComStatus(StatusConta status)
        {
            var contas = Queryable();
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);

            contas = AdicionarFiltroStatus(status, contas);
            contas = AdicionarFiltroAtivo(contas);

            return contas.ToList();
        }

        public IList<ContaFinanceira> ObterTodasAsContasNoStatusXDias(StatusConta statusConta, long qtdDias,
            DateTime dataHora)
        {
            var contas = Queryable();
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);

            return contas
                .Where(
                    a =>
                        a.Ativo && a.Status == statusConta &&
                        (a.DataDaModificacaoDoStatus.HasValue ? a.DataDaModificacaoDoStatus.Value.Date : dataHora) ==
                        dataHora.AddDays(-qtdDias).Date)
                .ToList();
        }

        public IList<ContaFinanceira> ObterTodasAsContasNoStatusPeloMenosXDias(StatusConta statusConta, long qtdDias, DateTime dataHora)
        {
            return ObterTodasAsContasNoStatusPeloMenosXDiasQueryable(statusConta, qtdDias, dataHora).ToList();
        }

        public IList<ContaFinanceira> ObterTodasAsContasNoStatusPeloMenosXDiasQuePossuemAssinatura(StatusConta statusConta, long qtdDias, DateTime dataHora)
        {
            var contas = ObterTodasAsContasNoStatusPeloMenosXDiasQueryable(statusConta, qtdDias, dataHora);
            var contasSemAssinatura = contas.Where(a => a.DataPrimeiraAssinatura.HasValue);
            return contasSemAssinatura.ToList();
        }

        public IList<ContaFinanceira> ObterTodasAsContasNoStatusPeloMenosXDiasExcetoPlanoDeAssinaturaSemestralOuAnual(StatusConta statusConta, long qtdDias,
            DateTime dataHora)
        {
            var contas = ObterTodasAsContasNoStatusPeloMenosXDiasQueryable(statusConta, qtdDias, dataHora);
            var contasSemAssinatura = contas.Where(a =>
                a.DataPrimeiraAssinatura.HasValue).ToList();

            foreach (var conta in contasSemAssinatura)
                if (conta.AssinaturaAtiva().PlanoAssinatura.QuantidadeDeParcelas == 6 ||
                conta.AssinaturaAtiva().PlanoAssinatura.QuantidadeDeParcelas == 12)
                    contasSemAssinatura.Remove(conta);

            return contasSemAssinatura.ToList();
        }


        public IList<ContaFinanceira> ObterTodasAsContasNoStatusPeloMenosXDiasComPlanoDeAssinaturaSemestralOuAnual(StatusConta statusConta, long qtdDias,
            DateTime dataHora)
        {
            var contas = ObterTodasAsContasNoStatusPeloMenosXDiasQueryable(statusConta, qtdDias, dataHora).ToList();
            var contasSemAssinatura = contas.Where(a =>
            a.DataPrimeiraAssinatura.HasValue &&
            a.AssinaturaAtiva().PlanoAssinatura.QuantidadeDeParcelas == 6 ||
            a.AssinaturaAtiva().PlanoAssinatura.QuantidadeDeParcelas == 12);
            return contasSemAssinatura.ToList();
        }

        private IQueryable<ContaFinanceira> ObterTodasAsContasNoStatusPeloMenosXDiasQueryable(StatusConta statusConta, long qtdDias,
            DateTime dataHora)
        {
            var contas = Queryable();
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);

            return contas.Where(a => a.Status.IdStatus == statusConta.IdStatus &&
                                    (a.DataDaModificacaoDoStatus.HasValue ? a.DataDaModificacaoDoStatus.Value.Date : dataHora) <= dataHora.AddDays(-qtdDias).Date);
        }

        public IList<ContaFinanceira> ObterTodasAsContasQueEstejamComFaturaVencidaAPeloMenosXDias(
            StatusContaFinanceira statusContaFinanceira, int qtdDias, DateTime dataHora)
        {
            var contas = Queryable();
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);

            var buscaDeFaturas = Domain.Cobranca.FaturaTrinksRepository.Queryable();

            return contas
                .Where(a => a.Ativo && a.Status.IdStatus == (int)statusContaFinanceira
                            && buscaDeFaturas
                                .Any(
                                    f =>
                                        f.Ativo &&
                                        f.Assinatura.ContaFinanceira.IdContaFinanceira == a.IdContaFinanceira
                                        &&
                                        (f.Status.IdStatus == (int)StatusFaturaEnum.Vencida ||
                                         f.Status.IdStatus == (int)StatusFaturaEnum.Aberta)
                                        && f.DataVencimento != null &&
                                        f.DataVencimento <= dataHora.AddDays(-qtdDias).Date
                                )
                )
                .ToList();
        }

        public IList<ContaFinanceira> ObterTodasAsContasQueEstejamComFaturaVencidaAPeloMenosXDiasExcetoPlanoDeAssinaturaSemestralOuAnual(
            List<ContaFinanceira> contasForaDeTolerancia, int qtdDias, DateTime dataHora)
        {
            List<ContaFinanceira> contasFinanceirasFiltradas = FiltrarContasForaDeToleranciaComFaturaVencidaAPeloMenosXDias(contasForaDeTolerancia, qtdDias, dataHora);
            List<ContaFinanceira> contasFinanceirasComFaturaVencidaOuAbertaExcetoPlanoSemestralOuAnual = new List<ContaFinanceira>();

            foreach (var conta in contasFinanceirasFiltradas)
            {
                var assinatura = conta.AssinaturaAtiva();

                if (assinatura is null)
                {
                    LogService<ContaFinanceiraRepository>.Info($"[cobranca] Conta Financeira {conta.IdContaFinanceira} sem assinatura ativa");
                    continue;
                }

                var planoAssinatura = assinatura.PlanoAssinatura;

                if (planoAssinatura is null)
                {
                    LogService<ContaFinanceiraRepository>.Info($"[cobranca] Assinatura {assinatura.IdAssinatura} sem plano de assinatura");
                    continue;
                }

                if (planoAssinatura.QuantidadeDeParcelas != 6 && planoAssinatura.QuantidadeDeParcelas != 12)
                    contasFinanceirasComFaturaVencidaOuAbertaExcetoPlanoSemestralOuAnual.Add(conta);
            }

            return contasFinanceirasComFaturaVencidaOuAbertaExcetoPlanoSemestralOuAnual;
        }

        public IList<ContaFinanceira> ObterTodasAsContasQueEstejamComFaturaVencidaAPeloMenosXDiasComPlanoDeAssinaturaSemestralOuAnual(
            List<ContaFinanceira> contasForaDeTolerancia, int qtdDias, DateTime dataHora)
        {
            List<ContaFinanceira> contasFinanceirasFiltradas = FiltrarContasForaDeToleranciaComFaturaVencidaAPeloMenosXDias(contasForaDeTolerancia, qtdDias, dataHora);
            List<ContaFinanceira> contasFinanceirasComFaturaVencidaOuAbertaComPlanoSemestralOuAnual = new List<ContaFinanceira>();

            foreach (var conta in contasFinanceirasFiltradas)
                if (conta.AssinaturaAtiva().PlanoAssinatura.QuantidadeDeParcelas == 6 || conta.AssinaturaAtiva().PlanoAssinatura.QuantidadeDeParcelas == 12)
                    contasFinanceirasComFaturaVencidaOuAbertaComPlanoSemestralOuAnual.Add(conta);

            return contasFinanceirasComFaturaVencidaOuAbertaComPlanoSemestralOuAnual;
        }

        private List<ContaFinanceira> FiltrarContasForaDeToleranciaComFaturaVencidaAPeloMenosXDias(List<ContaFinanceira> contasFinaceiras, int qtdDias, DateTime dataHora)
        {
            var buscaDeFaturas = Domain.Cobranca.FaturaTrinksRepository.Queryable();

            return contasFinaceiras.Where(a =>
                buscaDeFaturas
                    .Any(
                        f =>
                            f.Ativo &&
                            f.Assinatura.ContaFinanceira.IdContaFinanceira == a.IdContaFinanceira
                            &&
                            (f.Status.IdStatus == (int)StatusFaturaEnum.Vencida ||
                             f.Status.IdStatus == (int)StatusFaturaEnum.Aberta)
                            && f.DataVencimento != null
                            && f.DataVencimento <= dataHora.AddDays(-qtdDias).Date
                )
                ).ToList();
        }

        public List<ContaFinanceira> ContasFinanceirasForaDeToleranciaEComFaturaVencidaOuAberta(StatusContaFinanceira statusContaFinanceira)
        {
            var contas = Queryable();
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);

            var buscaDeFaturas = Domain.Cobranca.FaturaTrinksRepository.Queryable();

            var contasForaDeTolerancia = contas
                .Where(a => a.Ativo && a.Status.IdStatus == (int)statusContaFinanceira
                &&
                buscaDeFaturas
                    .Any(
                        f =>
                            f.Ativo &&
                            f.Assinatura.ContaFinanceira.IdContaFinanceira == a.IdContaFinanceira
                            &&
                            (f.Status.IdStatus == (int)StatusFaturaEnum.Vencida ||
                             f.Status.IdStatus == (int)StatusFaturaEnum.Aberta)
                    )
                )
                .ToList();
            return contasForaDeTolerancia;
        }

        public List<ContaFinanceira> ObterTodasAsContasQueEstejamComAssinaturaInativaOuFinalizadaComStatusPositivo()
        {
            var contas = Queryable().Where(f => f.Status == StatusContaFinanceira.Adimplente ||
                                                f.Status == StatusContaFinanceira.PeriodoGratis ||
                                                f.Status == StatusContaFinanceira.InadimplenteEmTolerancia);

            var assinaturas = Domain.Cobranca.AssinaturaRepository.Queryable();
            contas = contas.Where(p => p.Assinaturas.All(a => !a.Ativo || a.DataFim != null));
            return contas.ToList();
        }

        public IList<ContaFinanceira> ObterContasComFaturaVencidasOuAbertasAReprocessar(DateTime dataReferencia)
        {
            var contas = Queryable()
                .Where(f => f.Ativo &&
                            (f.Status == StatusContaFinanceira.InadimplenteEmTolerancia ||
                             f.Status == StatusContaFinanceira.InadimplenteForaTolerancia));
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);
            contas = contas.Where(f =>
                f.Assinaturas.Any(a =>
                    a.Ativo &&
                    a.Faturas.Any(fat => (fat.Status == StatusFaturaEnum.Aberta ||
                                          fat.Status == StatusFaturaEnum.Vencida) &&
                                         fat.DataParaProcessar.HasValue &&
                                         fat.DataParaProcessar < dataReferencia.Date.AddDays(1))));

            return contas.ToList();
        }

        public IList<ContaFinanceira> ObterContasComPeriodoDegustacaoExpirado(DateTime dataReferencia)
        {
            var contas = Queryable().Where(f => f.Ativo && f.Status == StatusContaFinanceira.PeriodoGratis);
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);
            contas = contas.Where(f => f.Pessoa.DataCadastro.HasValue);
            contas = contas.Where(f => f.Assinaturas.Any(g => g.Ativo));

            var assinaturas = contas.Select(f => f.Assinaturas.FirstOrDefault(g => g.Ativo)).ToList();
            var contasProcessadas = contas.ToList();
            var retorno = from f in contasProcessadas
                          let assinatura = assinaturas.First(g => g.ContaFinanceira == f)
                          where f.Pessoa.DataCadastro < dataReferencia.AddDays(-assinatura.DiasGratis - 1).Date.AddDays(1)
                          select f;

            return retorno.ToList();
        }

        public IList<ContaFinanceira> ObterContasComPeriodoDegustacaoAExpirarNoDia(DateTime dataReferencia)
        {
            var contas = Queryable().Where(f => f.Ativo && f.Status == StatusContaFinanceira.PeriodoGratis);
            contas = LimitarEstabelecimentoComCobrancaDesabilitada(contas);
            contas = contas.Where(f => f.Pessoa.DataCadastro.HasValue);
            contas = contas.Where(f => f.Assinaturas.Any(g => g.Ativo));

            var assinaturas = contas.Select(f => f.Assinaturas.FirstOrDefault(g => g.Ativo)).ToList();
            var contasProcessadas = contas.ToList();

            var retorno = from f in contasProcessadas
                          let assinatura = assinaturas.First(g => g.ContaFinanceira == f)
                          where f.Pessoa.DataCadastro.Value.Date == dataReferencia.AddDays(-assinatura.DiasGratis)
                          select f;
            return retorno.ToList();
        }

        public ContaFinanceira ObterAtivaPorEstabelecimento(Estabelecimento estabelecimento)
        {
            var contas = Queryable(true);
            return contas.FirstOrDefault(f => f.Ativo && f.Pessoa == estabelecimento.PessoaJuridica);
        }

        public IList<ContaFinanceira> ObterContasAtivasNaoConfirmadas(Pessoa pessoa)
        {
            var pjs =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .Where(f => f.PessoaFisica.IdPessoa == pessoa.IdPessoa)
                    .Select(f => f.Estabelecimento.PessoaJuridica);
            var contas =
                Domain.Cobranca.ContaFinanceiraRepository.Queryable()
                    .Where(
                        f =>
                            pjs.Select(g => g.IdPessoa).Contains(f.Pessoa.IdPessoa) &&
                            f.Status == StatusContaFinanceira.ContaNaoConfirmada);
            return LimitarEstabelecimentoComCobrancaDesabilitadaN1(contas.ToList());
        }

        public IList<ContaFinanceira> ObterContasAtivasNaoConfirmadasSemStateless(Pessoa pessoa)
        {
            var pjs =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .Where(f => f.PessoaFisica.IdPessoa == pessoa.IdPessoa)
                    .Select(f => f.Estabelecimento.PessoaJuridica);
            var contas =
                Domain.Cobranca.ContaFinanceiraRepository.Queryable()
                    .Where(
                        f =>
                            pjs.Select(g => g.IdPessoa).Contains(f.Pessoa.IdPessoa) &&
                            f.Status == StatusContaFinanceira.ContaNaoConfirmada);
            return LimitarEstabelecimentoComCobrancaDesabilitadaN1(contas.ToList());
        }

        public ContaFinanceira ObterContaAtiva(Pessoa pessoa)
        {
            return ObterContaAtiva(pessoa.IdPessoa);
        }

        public ContaFinanceira ObterContaAtiva(int idPessoa)
        {
            return Queryable().FirstOrDefault(x => x.Pessoa.IdPessoa == idPessoa && x.Ativo);
        }

        public ContaFinanceira ObterContaFinanceiraCancelada(Estabelecimento estabelecimento)
        {
            return Queryable().FirstOrDefault(c => c.DataCancelamento != null && c.Pessoa.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa);
        }

        #region Métodos Privados

        private static IQueryable<ContaFinanceira> AdicionarFiltroStatus(StatusConta status,
            IQueryable<ContaFinanceira> retorno)
        {
            return retorno.Where(f => f.Status.IdStatus == status.IdStatus);
        }

        private static IQueryable<ContaFinanceira> AdicionarFiltroAtivo(IQueryable<ContaFinanceira> retorno)
        {
            return retorno.Where(f => f.Ativo);
        }

        private static IQueryable<ContaFinanceira> LimitarEstabelecimentoComCobrancaDesabilitada(
            IQueryable<ContaFinanceira> contas)
        {
            return contas.Where(f => f.Status != StatusContaFinanceira.CobrancaManual);
        }

        private static IList<ContaFinanceira> LimitarEstabelecimentoComCobrancaDesabilitadaN1(
            IEnumerable<ContaFinanceira> contas)
        {
            // Realiza N+1 propositalmente, mas somente dos estabelecimentos associados a conta.
            // A implementação anterior, carrega todos os estabelecimentos.
            return (from contaFinanceira in contas
                    let estabelecimento =
                        Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(contaFinanceira.Pessoa.IdPessoa)
                    where estabelecimento.HabilitaCobranca
                    select contaFinanceira).ToList();
        }

        #endregion Métodos Privados

        public IList<ContaFinanceira> ObterContasEmToleranciaComFaturasForaDeTolerancia(DateTime dataHora)
        {
            var faturasForaDeTolerancia = Domain.Cobranca.FaturaRepository.ObterFaturasForaDeTolerancia(dataHora).Where(f => f is FaturaTrinks);
            return Queryable().Where(c => c.Status == StatusContaFinanceira.InadimplenteEmTolerancia
                                    && faturasForaDeTolerancia.Any(f => f.Assinatura.ContaFinanceira.IdContaFinanceira == c.IdContaFinanceira)).ToList();
        }

        public IQueryable<ContaFinanceira> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManual()
        {
            return Queryable().Where(f => f.Ativo && (new[] { 1, 3, 4, 9 }).Contains(f.Status.IdStatus));
        }

        public IQueryable<ContaFinanceira> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManualOuForaToleranciaOuCancelada()
        {
            return Queryable().Where(f => f.Ativo && (new[] { 1, 3, 4, 5, 6, 9 }).Contains(f.Status.IdStatus));
        }

        public IQueryable<ContaFinanceira> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManualOuCancelada()
        {
            return Queryable().Where(f => f.Ativo && (new[] { 1, 3, 4, 6, 9 }).Contains(f.Status.IdStatus));
        }

        public int ObterQuantasContasForamCanceladasNoDia(DateTime diaPesquisa)
        {
            return Queryable().Where(c => c.DataCancelamento >= diaPesquisa.Date && c.DataCancelamento < diaPesquisa.Date.AddDays(1)).Count();
        }

        public DateTime? ObterDataPrimeiraAssinaturaDaPessoa(int idPessoa)
        {
            return Queryable()
                .Where(f => f.Ativo && f.Pessoa.IdPessoa == idPessoa)
                .Select(f => f.DataPrimeiraAssinatura)
                .FirstOrDefault();
        }

        public StatusContaFinanceira ObterStatusContaFinanceira(PessoaJuridica pessoaJuridica)
        {
            return Queryable()
                .Where(f => f.Ativo && f.Pessoa == pessoaJuridica)
                .Select(f => f.Status)
                .FirstOrDefault();
        }

        public bool EstabelecimentoLogadoEmPeriodoGratis()
        {
            var estabelecimentoLogado = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento.Value);
            return ObterContaAtiva(estabelecimentoLogado.PessoaJuridica).Status == StatusContaFinanceira.PeriodoGratis;
        }
    }
}