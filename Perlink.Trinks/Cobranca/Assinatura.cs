﻿using Castle.ActiveRecord;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.Cobranca.Helpers;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Cobranca
{

    [ActiveRecord("COB_Assinatura", DynamicInsert = true, DynamicUpdate = true)]
    [Serializable]
    public class Assinatura : ActiveRecordBase<Assinatura>
    {

        public Assinatura()
        {
            Ativo = true;
        }

        public Assinatura(ContaFinanceira contaFinanceira, PlanoAssinatura planoAssinatura)
            : this()
        {
            ContaFinanceira = contaFinanceira;

            //if (EhPrimeiraAssinatura(contaFinanceira))
            //    contaFinanceira.DataPrimeiraAssinatura = Calendario.Agora();

            PlanoAssinatura = planoAssinatura;
            DiasGratis = planoAssinatura.DiasGratisInicial;
            DataInicio = Calendario.Agora();
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_cob_assinatura")]
        public virtual int IdAssinatura { get; set; }

        [BelongsTo("id_cob_conta_financeira")]
        public virtual ContaFinanceira ContaFinanceira { get; set; }

        public virtual bool PrecisaAssinar()
        {
            return EhDegustadorComMaisDiasDeSaldo() || EhDegustadorComDiasAntesDoTermino() ||
                EhNaoAssinante() || EhContaFinanceiraInativa() || EhContaFinanceiraCancelada();
        }

        [BelongsTo("id_cob_plano_assinatura")]
        public virtual PlanoAssinatura PlanoAssinatura { get; set; }

        [Property("dt_ini_assinatura")]
        public virtual DateTime? DataInicio { get; set; }

        [Property("dt_fim_assinatura")]
        public virtual DateTime? DataFim { get; set; }

        [Property("dt_fim_duracao_plano")]
        public virtual DateTime? DataFimDeDuracaoDoPlano { get; set; }

        [Property("dt_pode_cancelar_apartir")]
        public virtual DateTime? PodeCancelarAPartirDe { get; set; }

        [Property("dias_gratis_cliente")]
        public virtual int DiasGratis { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }

        [Property("dia_vencimento")]
        public virtual int? DiaDeVencimento { get; set; }

        [BelongsTo("id_cob_forma_pagamento")]
        public virtual FormaPagamento FormaPagamento { get; set; }

        [Property("id_cob_forma_pagamento", Update = false, Insert = false)]
        public virtual int? IdFormaPagamento { get; set; }

        [BelongsTo("id_pessoa_que_cancelou")]
        public virtual PessoaFisica PessoaQueCancelou { get; set; }

        [Property("area_realizada_cancelamento")]
        public virtual AreaEnum? AreaRealizadaCancelamento { get; set; }

        [Property("motivo_cancelamento")]
        public virtual string MotivoCancelamento { get; set; }

        [Property("motivo_cancelamento_pipedrive")]
        public virtual string MotivoCancelamentoPipedrive { get; set; }

        [Property("numero_profissionais_pipedrive")]
        public virtual int NumProfissionaisPipedrive { get; set; }

        [HasMany(typeof(DorDoClienteNaAssinatura), ColumnKey = "id_cob_assinatura",
    Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO", Table = "COB_Assinatura_Dor_Cliente",
    Inverse = true, Lazy = true)]
        public virtual IList<DorDoClienteNaAssinatura> DorDoClienteNaAssinatura { get; set; }

        [HasMany(typeof(MotivoDoCancelamentoDaAssinatura), ColumnKey = "id_cob_assinatura",
    Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO", Table = "COB_Assinatura_Motivo_Cancelamento",
    Inverse = true, Lazy = true)]
        public virtual IList<MotivoDoCancelamentoDaAssinatura> MotivoDoCancelamentoDaAssinatura { get; set; }

        [Obsolete("Não utilizar. Obter faturas pelo repositório.")]
        [HasMany(ColumnKey = "id_cob_assinatura", Cascade = ManyRelationCascadeEnum.None, Inverse = true, Lazy = true)]
        public virtual IList<Fatura> Faturas { get; set; }

        [Property("quantidade_profissionais_esperada")]
        public virtual int? QuantidadeDeProfissionaisEsperada { get; set; }

        [Property("email_renovacao_proxima_enviado")]
        public virtual bool EmailDeRenovacaoSeAproximandoFoiEnviado { get; set; }

        public override string ToString()
        {
            return PlanoAssinatura.ToString() + " - " + ContaFinanceira.ToString();
        }

        public int TotalDeDiasRestantesDaAssinatura(DateTime dataHora)
        {
            return (DataFimPeriodoPagoOuGratuito().Date - dataHora.Date).Days;
        }

        public DateTime DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura()
        {
            var pessoa = Domain.Pessoas.PessoaRepository.Load(ContaFinanceira.Pessoa.IdPessoa);
            return pessoa.DataCadastro.Value.AddDays(DiasGratis).Date;
        }

        public DateTime DataFinalDaDegustacaoAtual()
        {
            return DataInicio.Value.AddDays(DiasGratis).Date;
        }

        public DateTime DataComDiasAntesDoFinalDaDegustacao()
        {
            var diasAntesDeLiberarAssinatura = new ParametrosTrinks<Int32>(ParametrosTrinksEnum.qtd_dias_antes_assinatura);
            var dias = diasAntesDeLiberarAssinatura.ObterValor();
            //return DataFinalDaDegustacao().AddDays(-PeriodoDegustacao.QuinzeDias);
            return DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura().AddDays(-dias);
        }

        public Int32 DiasRestantesDeDegustacao(DateTime data)
        {
            var dias = (DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura() - data.Date).Days;
            dias = dias > 0 ? dias : 0;
            dias = dias > DiasGratis ? DiasGratis : dias;
            return dias;
        }

        public Boolean EhDegustadorComMaisDiasDeSaldo()
        {
            var diasAntesDeLiberarAssinatura = new ParametrosTrinks<Int32>(ParametrosTrinksEnum.qtd_dias_antes_assinatura);
            var dias = diasAntesDeLiberarAssinatura.ObterValor();
            return (ContaFinanceira.Status == StatusContaFinanceira.PeriodoGratis || ContaFinanceira.Status == StatusContaFinanceira.NaoAssinado) && DiasRestantesDeDegustacao(Calendario.Agora()) > dias;
        }

        public Boolean EhDegustadorComDiasAntesDoTermino()
        {
            var diasAntesDeLiberarAssinatura = new ParametrosTrinks<Int32>(ParametrosTrinksEnum.qtd_dias_antes_assinatura);
            var dias = diasAntesDeLiberarAssinatura.ObterValor();

            var temPromocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoAOferecerNaAssinatura(ContaFinanceira) != null;

            return (ContaFinanceira.Status == StatusContaFinanceira.PeriodoGratis || ContaFinanceira.Status == StatusContaFinanceira.NaoAssinado) && (DiasRestantesDeDegustacao(Calendario.Agora()) <= dias || temPromocao);
        }

        public Boolean EhContaFinanceiraInativa()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.ContaInativa;
        }

        public Boolean EhContaFinanceiraCancelada()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.ContaCancelada;
        }

        public Boolean EhAdimplente()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.Adimplente;
        }

        public Boolean EhAdimplenteComDegustacao()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.Adimplente && DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura().Date >= Calendario.Hoje();
        }

        public Boolean EhInadimplenteNoPeriodoDeTolerancia()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.InadimplenteEmTolerancia;
        }

        public Boolean EhInadimplenteForaDoPeriodoDeTolerancia()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.InadimplenteForaTolerancia;
        }

        public Boolean EhNaoAssinante()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.NaoAssinado && DiasRestantesDeDegustacao(Calendario.Agora()) == 0;
        }

        public bool EhCobrancaManual()
        {
            return ContaFinanceira.Status == StatusContaFinanceira.CobrancaManual;
        }

        public bool EhStatusComContasEmDia()
        {
            return EhAdimplente() || EhInadimplenteNoPeriodoDeTolerancia() || EhCobrancaManual()
                || EhDegustadorComDiasAntesDoTermino();
        }

        public Boolean CartaoDeCreditoVaiExpirar()
        {
            return Domain.Cobranca.AssinaturaService.VerificarSeCartaoDeCreditoVaiExpirar(IdAssinatura);
        }

        public Boolean CartaoDeCreditoExpirou()
        {
            return Domain.Cobranca.AssinaturaService.VerificarSeCartaoDeCreditoExpirou(IdAssinatura);
        }

        public Boolean EhAssinanteComPagamentoPorBoleto()
        {
            return FormaPagamento == FormaDePagamentoEnum.Boleto;
        }

        public Boolean EhAssinanteComPagamentoPorCartao()
        {
            return FormaPagamentoHelper.EhCartaoDeCredito(FormaPagamento);
        }

        public DateTime DataInicioProximoPeriodo()
        {
            DateTime? dataFimUltimaFatura = Domain.Cobranca.FaturaRepository.ObterMaiorDataFimReferenciaDeFaturasPagas(IdAssinatura);

            if (dataFimUltimaFatura.HasValue)
                return dataFimUltimaFatura.Value.AddDays(1);
            else
            {
                var inicioNaoDegustacao = DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura().AddDays(1);
                return inicioNaoDegustacao < Calendario.Hoje() ? Calendario.Hoje() : inicioNaoDegustacao;
            }
        }

        public DateTime DataFimPeriodoPagoOuGratuito()
        {
            return DataInicioProximoPeriodo().AddDays(-1);
        }

        public DateTime DataFimDaAssinaturaNoPeriodoDeTolerancia()
        {
            var dataFimDaTolerancia = Domain.Cobranca.ContaFinanceiraService.FinalDaToleranciaNaoPagamento(ContaFinanceira);

            if (dataFimDaTolerancia == DateTime.MaxValue)
                return dataFimDaTolerancia;
            else
                return dataFimDaTolerancia.AddDays(1);
        }

        protected override void OnSave()
        {
            // Author = "Test Author 1";

            base.OnSave();
        }

        public bool EhOPrimeiroPagamento()
        {
            if (IdAssinatura < 0)
                return false;
            return !Domain.Cobranca.FaturaRepository.ExisteAlgumaFaturaPaga(IdAssinatura);
        }

        public bool EhAtivaEVigente(DateTime? dataReferencia)
        {
            dataReferencia = dataReferencia ?? Calendario.Agora();
            return Ativo && (DataFim == null || DataFim > dataReferencia);
        }

        //public virtual DateTime ObterDataVencimentoProximaFatura(DateTime? dataReferencia) {
        //    return ObterDataInicioProximoPeriodo(this, dataReferencia);
        //}

        //public bool AssinaturaEstahAprovada() {
        //    return ContaFinanceira.Status != StatusContaFinanceira.ContaCancelada
        //        && ContaFinanceira.Status != StatusContaFinanceira.ContaInativa
        //        && ContaFinanceira.Status != StatusContaFinanceira.ContaNaoConfirmada
        //        && ContaFinanceira.Status != StatusContaFinanceira.InadimplenteForaTolerancia
        //        && ContaFinanceira.Status != StatusContaFinanceira.NaoAssinado;
        //}

        public PromocaoPraContaFinanceira PromocaoNaProximaFatura()
        {
            PromocaoPraContaFinanceira promocaoPraConta = null;
            if (ProximaFaturaTemDescontoDePromocao())
            {
                promocaoPraConta = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraPelaAssinatura(IdAssinatura);
            }
            return promocaoPraConta;
        }

        public bool ProximaFaturaTemDescontoDePromocao()
        {
            return Domain.Cobranca.FaturaService.ProximaFaturaTerahDescontoDePromocao(IdAssinatura);
        }

        public int QuantasFaturasAindaTeraoDescontoDePromocao()
        {
            return Domain.Cobranca.FaturaService.QuantasFaturasAindaTeraoDescontoDePromocao(IdAssinatura);
        }

        public bool PodeInformarPagamentoDeBoletoParaDesbloqueio(DateTime data)
        {
            bool podeInformar =
                ContaFinanceira.Status == StatusContaFinanceira.InadimplenteForaTolerancia
                && Domain.Cobranca.FaturaTrinksRepository.ExisteFaturaQuePodeInformarPagamentoDeBoletoParaDesbloqueio(IdAssinatura, data);

            return podeInformar;
        }

        public bool JaInformouPagamentoDeBoletoUmaVez()
        {
            bool jaInformou = Domain.Cobranca.FaturaTrinksRepository.ExisteFaturaPendenteQueInformouSoUmaVezOPagamento(IdAssinatura);
            return jaInformou;
        }

        public int DiasToleranciaNaoPagamento()
        {
            var fatura = Domain.Cobranca.FaturaTrinksRepository.ObterFaturaTrinksMaisAntigaPendenteDePagamento(IdAssinatura);
            if (fatura != null)
                return fatura.DiasToleranciaNaoPagamento;
            return 0;
        }

        public bool PossuiPlanoComFidelidade()
        {
            return PlanoAssinatura.DuracaoDoPlanoComFidelidade != null;
        }

        public bool DeveTerFaturaCobrindoTodoPeriodoDeDuracaoDaAssinatura()
        {
            return PossuiPlanoComFidelidade() && FormaPagamento == FormaDePagamentoEnum.Boleto;
        }

        public bool PodeAlterarFormaDePagamentoParaBoleto()
        {
            bool podeAlterarPraBoleto =
                FormaPagamento != FormaDePagamentoEnum.Boleto && !PossuiPlanoComFidelidade();//TODO:http://jira.perlink.net/browse/TRINKS-8276
                                                                                             //(PodeCancelarAPartirDe == null || PodeCancelarAPartirDe.Value <= Calendario.Hoje());

            return podeAlterarPraBoleto;
        }

        public bool PodeAlterarFormaDePagamentoParaPix()
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(ContaFinanceira.Pessoa.IdPessoa);
            var togglePix = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.PagamentoFaturaPix);
            return FormaPagamento != FormaDePagamentoEnum.Pix && !PossuiPlanoComFidelidade() && togglePix.EstaDisponivel;
        }

        public bool PodeTrocarDePlanoNoMomento()
        {
            return !PossuiPlanoComFidelidade() || (PodeCancelarAPartirDe == null || PodeCancelarAPartirDe.Value <= Calendario.Hoje());
        }

        public DateTime? DataDeRenovacaoPlano()
        {
            return DataFimDeDuracaoDoPlano.HasValue ? DataFimDeDuracaoDoPlano.Value.AddDays(1) : DataFimDeDuracaoDoPlano;
        }

        public bool ResponsavelDoEstabelecimentoPodeCancelarAssinatura()
        {
            return PlanoAssinatura != null && (PodeCancelarAPartirDe <= Calendario.Hoje() || PodeCancelarAPartirDe == null);
        }

        public int ObterQuantidadeDeProfissionaisParaConsiderarNaCobranca()
        {
            var quantidadeDeProfissionaisNoEstabelecimento = ContaFinanceira.Pessoa.PessoaJuridica.Estabelecimento.ObterQuantidadeDeProfissionaisComServicosAtivos();
            var quantidadeParaRetornar = QuantidadeDeProfissionaisEsperada > quantidadeDeProfissionaisNoEstabelecimento
                ? QuantidadeDeProfissionaisEsperada.Value
                : quantidadeDeProfissionaisNoEstabelecimento;

            quantidadeParaRetornar = quantidadeParaRetornar <= 0 ? 1 : quantidadeParaRetornar;
            return quantidadeParaRetornar;
        }

        public DateTime? ObterDataInicioDeDuracaoDoPlano()
        {
            // Cálculo inverso ao existente no método AssinaturaService.ObterDataFimDeDuracaoDoPlano
            if (DataFimDeDuracaoDoPlano == null || !PossuiPlanoComFidelidade() || !PlanoAssinatura.PossuiFidelidade()) return null;
            return DataFimDeDuracaoDoPlano.Value.AddDays(1).AddMonths(-PlanoAssinatura.DuracaoDoPlanoComFidelidade.Value);
        }

        public int? ObterQuantidadeDeFaturasRestantesParaTerminarPeriodoAtual()
        {
            var dataInicio = ObterDataInicioDeDuracaoDoPlano();
            var dataFim = DataFimDeDuracaoDoPlano;

            if (dataInicio == null || dataFim == null)
                return null;

            var qtdDeFaturasNoPeriodo = PlanoAssinatura.DuracaoDoPlanoComFidelidade.Value;
            var qtdDeFaturasGeradas = Domain.Cobranca.FaturaTrinksRepository
                .ObterQuantidadeFaturasNoPeriodoDeReferenciaQueNaoSejaCancelada(IdAssinatura, dataInicio.Value, dataFim.Value);

            return qtdDeFaturasNoPeriodo - qtdDeFaturasGeradas;
        }
    }
}