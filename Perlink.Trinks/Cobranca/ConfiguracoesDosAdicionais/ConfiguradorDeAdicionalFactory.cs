﻿using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using System;

namespace Perlink.Trinks.Cobranca.ConfiguracoesDosAdicionais
{
    public static class ConfiguradorDeAdicionalFactory
    {

        internal static IConfiguradorDeAdicional ObterConfigurador(AdicionalNaAssinatura adicional)
        {
            return ObterConfiguradorPorIdServico(adicional.OfertaEscolhida.Servico.IdServico);
        }

        internal static IConfiguradorDeAdicional ObterConfigurador(int idServicoAdicional)
        {
            return ObterConfiguradorPorIdServico(idServicoAdicional);
        }

        private static IConfiguradorDeAdicional ObterConfiguradorPorIdServico(int idServico)
        {
            switch ((ServicoAdicionalEnum)idServico)
            {

                case ServicoAdicionalEnum.LembretePremium:
                    return new ConfiguradorDeLembretePremium();

                case ServicoAdicionalEnum.ProgramaDeFidelidade:
                    return new ConfiguradorDeProgramaDeFidelidade();

                case ServicoAdicionalEnum.NFCe:
                    return new ConfiguradorDeNFCe();

                case ServicoAdicionalEnum.NFSe:
                    return new ConfiguradorDeNFSe();

                case ServicoAdicionalEnum.ProfissionalParceiro:
                    return new ConfiguradorDeProfissionalParceiro();

                case ServicoAdicionalEnum.BelezinhaSemSplit:
                    return new ConfiguradorDeBelezinhaSemSplit();

                case ServicoAdicionalEnum.BelezinhaComSplit:
                    return new ConfiguradorDeBelezinha();

                case ServicoAdicionalEnum.AppWhiteLabelExclusivo:
                    return new ConfiguradorDeAppWhiteLabelExclusivo();

                case ServicoAdicionalEnum.AppExclusivoNovo:
                    return new ConfiguradorDeAppExclusivoNovo();

                case ServicoAdicionalEnum.Pacote2000SMSMarketing:
                    return new ConfiguradorDePacote2000SMSMarketing();

                case ServicoAdicionalEnum.IntegracaoComApi:
                    return new ConfiguradorDeIntegracaoComAPI();

                case ServicoAdicionalEnum.BelezinhaTon:
                    return new ConfiguradorDeBelezinhaTon();

                case ServicoAdicionalEnum.Autoatendimento:
                    return new ConfiguradorDeAutoatendimento();

                case ServicoAdicionalEnum.Webhook:
                    return new ConfiguradorDeWebhook();

                case ServicoAdicionalEnum.RotinaMensagem:
                    return new ConfiguradorDeRotinaDeMensagem();

                case ServicoAdicionalEnum.Cloudia200:
                    return new ConfiguradorDeCloudia200();

                case ServicoAdicionalEnum.Cloudia600:
                    return new ConfiguradorDeCloudia600();

                case ServicoAdicionalEnum.Cloudia900:
                    return new ConfiguradorDeCloudia900();

                case ServicoAdicionalEnum.Cloudia1200:
                    return new ConfiguradorDeCloudia1200();

                case ServicoAdicionalEnum.Cloudia1600:
                    return new ConfiguradorDeCloudia1600();

                case ServicoAdicionalEnum.Cloudia2200:
                    return new ConfiguradorDeCloudia2200();

                case ServicoAdicionalEnum.ClubeDeAssinaturas:
                    return new ConfiguradorDeClubeDeAssinaturas();

                case ServicoAdicionalEnum.ConectorBI5Lojas:
                    return new ConfiguradorDeConectorBI5Lojas();

                case ServicoAdicionalEnum.ConectorBI6Ate50Lojas:
                    return new ConfiguradorDeConectorBI6Ate50Lojas();

                case ServicoAdicionalEnum.ConectorBIAcima50Lojas:
                    return new ConfiguradorDeConectorBIAcima50Lojas();

                case ServicoAdicionalEnum.ConectorBIEspecial:
                    return new ConfiguradorDeConectorBIEspecial();

                default:
                    throw new ArgumentException("Não foi encontrado o configurador para o adicional com id " + idServico);
            }
        }
    }
}
