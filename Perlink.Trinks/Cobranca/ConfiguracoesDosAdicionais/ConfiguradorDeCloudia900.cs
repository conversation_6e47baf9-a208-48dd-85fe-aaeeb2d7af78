﻿namespace Perlink.Trinks.Cobranca.ConfiguracoesDosAdicionais
{
    public class ConfiguradorDeCloudia900 : IConfiguradorDeAdicional
    {
        public void AplicarRegrasAposAlteracaoDeStatusDaContaFinanceira(AdicionalNaAssinatura adicional)
        {
        }

        public void AplicarRegrasDeAtivacaoAutomatica(AdicionalNaAssinatura adicional)
        {
        }

        public void AplicarRegrasDePosAtivacao(AdicionalNaAssinatura adicional)
        {
        }

        public void AplicarRegrasDePosDesativacao(AdicionalNaAssinatura adicional)
        {
        }

        public void AplicarRegrasDePosTrocaDaFormaDeContratacao(AdicionalNaAssinatura adicional)
        {
        }
    }
}
