﻿using System.ComponentModel;

namespace Perlink.Trinks.Cobranca.Enums
{

    public enum FormaDeContratacaoDoAdicionalEnum
    {
        AluguelMaquinaPOSIntegracaoStoneSiclos = 1,
        CompraMaquinaPOSIntegracaoGranito = 2,
        SomenteIntegracaoStoneSiclos = 3,
        SomenteIntegracaoGranito = 4,
        IntegracaoAntigaPagoCartoes = 5,
        IntegracaoAntigaStone = 6,
        EmissaoNotaSeparada = 7,
        EmissaoNotaUnica = 8,
        TonUltraTon = 12,
        TonMegaTon = 13,
        TonGigaTon = 14,
        TonTonBasico = 15,
        <PERSON>uguelMaquinaPOSIntegracaoStone2_0 = 18,
        SomenteIntegracaoStone2_0 = 19,
        <PERSON>uguelMaquinaPOSIntegracaoPagarMe2_0 = 20,

        [Description("Check-in")]
        CheckIn = 21,

        [Description("Check-in + Check-out")]
        CheckInECheckout = 22,

        [Description("Gestão Manual")]
        GestaoManual = 27,

        [Description("Gestão Automatizada")]
        GestaoAutomatizada = 28,

        [Description("Gestão Antiga")]
        GestaoAntiga = 29
    }
}
