﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Cobranca.DTO;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Cobranca.Services
{

    public interface IPlanoAssinaturaService : IService
    {

        void Manter(PlanoAssinatura entity, Boolean ehEdicao);

        List<ValorPorFaixaDoPlanoDTO> ListarValoresDeFaixasDoPlano(int idPlanoAssinatura);

        void AlterarPermissaoParaContratarAdicionaisPorFora(int idPlanoAssinatura);
    }

}
