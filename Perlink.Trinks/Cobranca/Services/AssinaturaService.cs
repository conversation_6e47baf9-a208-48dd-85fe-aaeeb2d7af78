﻿using Perlink.DomainInfrastructure;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.Cobranca.Helpers;
using Perlink.Trinks.Cobranca.ObjectValues;
using Perlink.Trinks.Cobranca.Repositories;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using RESMensagens = Perlink.Trinks.Resources.Mensagens;

namespace Perlink.Trinks.Cobranca.Services
{

    public class AssinaturaService : BaseService, IAssinaturaService
    {

        #region Propriedades de Apoio

        private IAssinaturaRepository AssinaturaRepository
        {
            get { return Domain.Cobranca.AssinaturaRepository; }
        }

        #endregion Propriedades de Apoio

        [TransactionInitRequired]
        public Assinatura AdicionarPrazoDeTolerancia(int id, int prazo, DateTime dataHora)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(id);

            var assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaVigenteEOuAtivaPorConta(assinatura.ContaFinanceira);
            if (assinaturaVigente == null)
                assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaPorConta(assinatura.ContaFinanceira);

            if (assinaturaVigente != null)
            {
                var fatura = Domain.Cobranca.FaturaTrinksRepository.ObterFaturaTrinksMaisAntigaPendenteDePagamento(assinaturaVigente.IdAssinatura);
                if (fatura == null)
                    fatura = (FaturaTrinks)Domain.Cobranca.FaturaService.GerarNovaFatura(assinaturaVigente, dataHora);

                DateTime? novaData = Domain.Cobranca.FaturaService.CalcularDataDeToleranciaDaFatura(fatura);
                if (novaData > fatura.FimPeriodoReferencia)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Data de tolerância não pode ser após a data fim da última fatura.");
                }
                else
                {
                    fatura.DiasToleranciaNaoPagamento += prazo;
                    fatura.DataDeTolerancia = novaData;
                    Domain.Cobranca.FaturaTrinksRepository.Update(fatura);
                }
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Conta financeira não possui assinatura vigente válida.");
            }

            return assinaturaVigente;
        }

        [TransactionInitRequired]
        public Fatura AlterarFormaDePagamentoEPagarSeNecessario(Assinatura assinaturaVigente, FonteDePagamento fonteDePagamento, string ipCliente, bool alterarFormaDePagamentoTambemDaAssinatura)
        {
            Fatura faturaAposTrocaDeDadosDePagamento = null;
            var existeFaturaQuePodeSerPaga = Domain.Cobranca.FaturaRepository.ExisteFaturaQuePodeSerPaga(assinaturaVigente.IdAssinatura);

            if (existeFaturaQuePodeSerPaga)
            {
                faturaAposTrocaDeDadosDePagamento = Domain.Cobranca.FaturaRepository.ObterFaturaMaisAntigaQuePodeSerPaga(assinaturaVigente.IdAssinatura);

                if (assinaturaVigente.PossuiPlanoComFidelidade())
                {
                    Domain.Cobranca.FaturaService.CancelarFatura(faturaAposTrocaDeDadosDePagamento, Calendario.Agora());
                    assinaturaVigente.FormaPagamento = Domain.Cobranca.PagamentoDeFaturaService.ObterFormaDePagamento(fonteDePagamento);
                    Domain.Cobranca.AssinaturaRepository.Update(assinaturaVigente);
                    faturaAposTrocaDeDadosDePagamento = null;
                }
            }

            if (faturaAposTrocaDeDadosDePagamento == null)
            {
                if (!FormaPagamentoHelper.TemProcessoAssincrono(fonteDePagamento.FormaPagamento.Tipo))
                {
                    faturaAposTrocaDeDadosDePagamento = Domain.Cobranca.FaturaService.GerarNovaFatura(assinaturaVigente, Calendario.Agora());
                }
                else
                {
                    var antecedenciaVerificacaoProximoBoleto = new ParametrosTrinks<int>(ParametrosTrinksEnum.qtd_dias_antecedencia_verificacao_proximo_boleto).ObterValor();
                    if (!Domain.Cobranca.FaturaRepository.Queryable()
                                .Any(g => g.Assinatura.IdAssinatura == assinaturaVigente.IdAssinatura
                                          && g.Ativo && g.Status != StatusFaturaEnum.Cancelada
                                          && g.FimPeriodoReferencia.HasValue
                                          && g.FimPeriodoReferencia >= Calendario.Hoje().AddDays(antecedenciaVerificacaoProximoBoleto)))
                    {
                        faturaAposTrocaDeDadosDePagamento = Domain.Cobranca.FaturaService.GerarNovaFatura(assinaturaVigente, Calendario.Agora());
                    }
                }
            }

            if (faturaAposTrocaDeDadosDePagamento != null && faturaAposTrocaDeDadosDePagamento.IdFatura > 0)
            {
                faturaAposTrocaDeDadosDePagamento.FormaPagamento = fonteDePagamento.FormaPagamento;
                Domain.Cobranca.FaturaService.ProcessarPagamentoAvulso(faturaAposTrocaDeDadosDePagamento, fonteDePagamento, ipCliente, Calendario.Agora(), false, alterarFormaDePagamentoTambemDaAssinatura);
            }

            if (alterarFormaDePagamentoTambemDaAssinatura &&
                ((!FormaPagamentoHelper.TemProcessoAssincrono(fonteDePagamento.FormaPagamento.Tipo) && faturaAposTrocaDeDadosDePagamento.Status == StatusFaturaEnum.Paga) || FormaPagamentoHelper.TemProcessoAssincrono(fonteDePagamento.FormaPagamento.Tipo)))
            {
                assinaturaVigente.FormaPagamento = Domain.Cobranca.PagamentoDeFaturaService.ObterFormaDePagamento(fonteDePagamento);
                Domain.Cobranca.AssinaturaRepository.Update(assinaturaVigente);
            }

            if (faturaAposTrocaDeDadosDePagamento != null && faturaAposTrocaDeDadosDePagamento.IdFatura > 0)
            {
                Domain.Cobranca.ParceriaTrinksService.NotificarPromotorDoTrinksSobreIndicadoQueTevePrimeiraAssinaturaPaga(faturaAposTrocaDeDadosDePagamento as FaturaTrinks);
            }

            return faturaAposTrocaDeDadosDePagamento;
        }

        public void ValidarNovoPrazoDeToleranciaEstipulado(Assinatura assinatura, DateTime dataHora,
            Conta ContaAutenticada, Int32 diasIncluidos)
        {
            var finalDaToleranciaNaoPagamento = Domain.Cobranca.ContaFinanceiraService.FinalDaToleranciaNaoPagamento(assinatura.ContaFinanceira);

            var estaEmTolerancia = finalDaToleranciaNaoPagamento.Date >= dataHora.Date;
            var finalTolerancia = finalDaToleranciaNaoPagamento.Date;

            if (estaEmTolerancia)
            {
                // Data do novo prazo de tolerância é maior que a dataUltimaPaga de vencimento da fatura mais antiga.
                if (!SimulationTool.Current.EhSimulacao)
                    assinatura.ContaFinanceira.Status = StatusContaFinanceira.InadimplenteEmTolerancia;

                var configHotsite = assinatura.ContaFinanceira.Pessoa.PessoaJuridica.Estabelecimento.Hotsite();
                configHotsite.PermiteBuscaHotsite = configHotsite.ValorAnteriorPermiteBuscaHotsite ?? false;

                var estabelecimento =
                    Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                        assinatura.ContaFinanceira.Pessoa.IdPessoa);
                Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, dataHora);

                Domain.Pessoas.EnvioEmailService.EnviarEmailPrazoAdicionado(
                    estabelecimento.NomeDeExibicaoNoPortal, assinatura.ContaFinanceira.Status.ToString(),
                    finalTolerancia, ContaAutenticada, diasIncluidos);

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(assinatura.ContaFinanceira);
                Domain.Pessoas.ContaService.DeslogarTodosOsUsuariosDoEstabelecimento(estabelecimento.IdEstabelecimento);
            }
        }

        public void CancelarAssinatura(int idPessoaJuridica, PessoaFisica pessoaLogada, String comentarioMotivoCancelamento,
                                       DateTime dataHora, Conta ContaAutenticada, AreaEnum areaDoCancelamento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaVigenciaEOuAtivaDaPJ(idPessoaJuridica);
            var motivoCancelamento = "";
            CancelarAssinatura(assinatura, motivoCancelamento, dataHora, ContaAutenticada, comentarioMotivoCancelamento, areaDoCancelamento);
        }

        [TransactionInitRequired]
        public void CancelarAssinatura(Assinatura assinatura, String motivoCancelamento, DateTime dataHora,
            Conta contaAutenticada, string comentarioMotivoDoCancelamento, AreaEnum areaDoCancelamento)
        {
            PessoaFisica pessoaLogada = null;

            if (contaAutenticada != null)
                pessoaLogada = Domain.Pessoas.PessoaFisicaRepository.Load(contaAutenticada.Pessoa.IdPessoa);

            var pessoaJuridica = assinatura.ContaFinanceira.Pessoa.PessoaJuridica;
            var cancelamentoPelaAreaPerlink = areaDoCancelamento == AreaEnum.Perlink;

            if (assinatura.ContaFinanceira.Status == StatusContaFinanceira.CobrancaManual)
            {
                DesativarCobrancaManual(assinatura.IdAssinatura, contaAutenticada);
            }

            assinatura.DataFim = dataHora;
            assinatura.Ativo = false;
            assinatura.PessoaQueCancelou = pessoaLogada;
            if (areaDoCancelamento == AreaEnum.BackOffice)
                assinatura.MotivoCancelamento = motivoCancelamento;
            assinatura.AreaRealizadaCancelamento = areaDoCancelamento;

            Domain.Cobranca.FaturaService.CancelarFaturasDaAssinatura(assinatura, dataHora, areaDoCancelamento == AreaEnum.Perlink, pessoaLogada);
            Domain.Cobranca.PromocaoPraContaFinanceiraService.InativarPromocaoDaContaFinanceiraPelaAssinatura(assinatura.IdAssinatura);
            Domain.Cobranca.AdicionalNaAssinaturaService.CancelarTodosOsAdicionaisDaAssinatura(assinatura, pessoaLogada, "Outros", "Assinatura cancelada");

            var responsavelFinanceiro = pessoaJuridica.ResponsavelFinanceiro;
            var quantidadeProfissionaisCobranca = assinatura.ObterQuantidadeDeProfissionaisParaConsiderarNaCobranca();
            var faixaAtual = Domain.Cobranca.AssinaturaService.ObterFaixaDePreco(assinatura, quantidadeProfissionaisCobranca);
            string faixa = faixaAtual.ObterDescricaoDaFaixa();

            Domain.Cobranca.AssinaturaRepository.Update(assinatura);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(assinatura.ContaFinanceira.Pessoa.IdPessoa);
            var quantidadeDeProfissionaisAtivosComAgenda = estabelecimento.ProfissionaisComAcessoAgenda().Where(p => p.Ativo).ToList().Count;

            Domain.ClubeDeAssinaturas.CancelamentoDeAssinaturaClienteService.CancelarTodasAssinaturasClientesEstabelecimento(estabelecimento, pessoaLogada);

            if (cancelamentoPelaAreaPerlink)
                Domain.Pessoas.EnvioEmailService.EnviarEmailCancelamentoDeAssinatura(
                    pessoaJuridica.NomeFantasia, contaAutenticada, quantidadeDeProfissionaisAtivosComAgenda, motivoCancelamento, comentarioMotivoDoCancelamento, faixa);
            else
            {
                var configHotsite = estabelecimento.Hotsite();
                Domain.Pessoas.EnvioEmailService.EnviarEmailAssinaturaCanceladaPeloUsuario(
                    responsavelFinanceiro.PrimeiraConta.Email,
                    assinatura.DataFim.Value,
                    pessoaLogada,
                    motivoCancelamento, configHotsite);
            }

            var aindaPossuiAssinaturaAtiva = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaVigentePorConta(assinatura.ContaFinanceira) != null;
            if (!aindaPossuiAssinaturaAtiva)
                Domain.Cobranca.ContaFinanceiraService.CancelarConta(assinatura.ContaFinanceira, dataHora, areaDoCancelamento == AreaEnum.Perlink,
                    pessoaLogada);

            var solicitacaoDeCancelamentoEmAberto = Domain.Cobranca.CancelamentoDaAssinaturaStory
                .ObterSolicitacaoDeCancelamentoPorEstabelecimentoEStatus(estabelecimento, StatusDaSolicitacaoDeCancelamentoDaAssinaturaEnum.EmAberto);
            if (solicitacaoDeCancelamentoEmAberto != null)
                solicitacaoDeCancelamentoEmAberto.MarcarQueAAssinaturaFoiCancelada(contaAutenticada);
        }

        public Fatura RealizarAssinatura(Estabelecimento estabelecimento, PessoaFisica usuarioLogado, FonteDePagamento fonteDePagamento, String ipComprador,
            DateTime dataHora, PlanoAssinatura planoEscolhido, int? idFaixaEscolhida, List<ServicoTrinksAContratarDTO> adicionaisContratados)
        {
            if (FormaPagamentoHelper.TemProcessoAssincrono(fonteDePagamento.FormaPagamento.Tipo) && planoEscolhido.PossuiFidelidade() && idFaixaEscolhida == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Para esta forma de pagamento é necessário informar a quantidade de profissionais esperada.");

            if (ValidationHelper.Instance.IsValid)
            {
                var assinaturaVigente =
                    AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(
                        estabelecimento.PessoaJuridica.IdPessoa);

                if (assinaturaVigente != null)
                {
                    var trocouDePlano = assinaturaVigente.PlanoAssinatura != planoEscolhido;

                    AplicarInformacoesDoPlanoNaAssinatura(dataHora, planoEscolhido, assinaturaVigente, idFaixaEscolhida);

                    if (trocouDePlano)
                    {
                        AtualizarAdicionaisContratadosComAsOfertasDoPlanoAssinado(assinaturaVigente, usuarioLogado, "Assinatura", "Alteração da oferta do adicional após assinar pelo BackOffice com plano diferente do que estava na assinatura.");
                    }

                    Domain.Cobranca.PagamentoDeFaturaService.GravarInformacoesNaAssinaturaPelaFonteDePagamento(assinaturaVigente, fonteDePagamento);

                    return Assinar(assinaturaVigente, fonteDePagamento, ipComprador, dataHora, adicionaisContratados, usuarioLogado);
                }

                var faturaReassinar = Reassinar(estabelecimento, fonteDePagamento, usuarioLogado, ipComprador, dataHora, planoEscolhido, idFaixaEscolhida, adicionaisContratados);

                var periodoAtualEstaPago = faturaReassinar.Assinatura.DataFimPeriodoPagoOuGratuito() > dataHora;
                if (!periodoAtualEstaPago)
                {
                    var contaFinanceira = estabelecimento.PessoaJuridica.ContasFinanceiras.OrderByDescending(c => c.DataCancelamento).FirstOrDefault();
                    contaFinanceira.Status = StatusContaFinanceira.InadimplenteForaTolerancia;
                    Domain.Cobranca.ContaFinanceiraRepository.Update(contaFinanceira);
                    faturaReassinar.DataDeTolerancia = Calendario.Hoje();
                    faturaReassinar.DataVencimento = Calendario.Hoje();
                }
                return faturaReassinar;
            }

            return null;
        }

        public bool UltimaFaturaFicouEmAberto(PessoaJuridica pessoaJuridica)
        {
            var ultimaFatura = Domain.Cobranca.FaturaTrinksRepository.Queryable()
                .Where(f => f.Assinatura.ContaFinanceira.Pessoa == pessoaJuridica)
                .OrderByDescending(f => f.DataVencimento)
                .FirstOrDefault();

            if (ultimaFatura == null)
                return false;

            return ultimaFatura.Status != StatusFaturaEnum.Paga;
        }

        public bool VerificarSeCartaoDeCreditoVaiExpirar(int idAssinatura)
        {
            return Domain.Cobranca.FaturaRepository.VerificarSeCartaoVaiExpirarDaUltimaFaturaPaga(idAssinatura);
        }

        public bool VerificarExisteBoletoParaDownload(int idAssinatura)
        {
            var retorno = false;
            var fatura = Domain.Cobranca.FaturaRepository.ObterFaturaMaisAntigaQuePodeSerPaga(idAssinatura);
            retorno = Domain.Cobranca.PagamentoDeFaturaService.FaturaDispoeDeBoletoParaDownload(fatura);
            return retorno;
        }

        public bool VerificarExisteBoletoPendenteForaDaMargteDeIdentificacaoDePagamento(int idAssinatura)
        {
            var retorno = false;
            var fatura = Domain.Cobranca.FaturaRepository.ObterFaturaMaisAntigaPendenteDePagamento(idAssinatura);
            retorno =
                Domain.Cobranca.PagamentoDeFaturaService.FaturaTemFaturaPendenteForaDaMargemDeIdentificacaoDePagamento(
                    fatura);
            return retorno;
        }

        public ValorPorFaixa ObterFaixaDePreco(Assinatura assinatura, int? quantidadeDeFuncionariosComAgenda = null, PlanoAssinatura planoAssinatura = null)
        {
            planoAssinatura = planoAssinatura ?? assinatura.PlanoAssinatura;

            var quantidadeDeProfissionaisNoEstabelecimento = quantidadeDeFuncionariosComAgenda ??
                Domain.Pessoas.ProfissionalRepository.QuantidadeDeProfissionaisComServicosAtivos(
                    assinatura.ContaFinanceira.Pessoa.PessoaJuridica.Estabelecimento.IdEstabelecimento);

            return planoAssinatura.ValoresPorFaixa.FirstOrDefault(p =>
                quantidadeDeProfissionaisNoEstabelecimento >= p.LimiteInferior &&
                (p.LimiteSuperior.HasValue && quantidadeDeProfissionaisNoEstabelecimento <= p.LimiteSuperior
                 || !p.LimiteSuperior.HasValue));
        }

        public String ObterValorDaProximaFaixaDePreco(Assinatura assinatura, int limiteSuperiorDeProfissionaisNaFaixaAtual)
        {
            var proximaFaixa = ObterFaixaDePreco(assinatura, limiteSuperiorDeProfissionaisNaFaixaAtual + 1);

            String novoValor = String.Empty;
            if (proximaFaixa != null && proximaFaixa.Valor.HasValue)
                novoValor = proximaFaixa.ValorPorMes().Value.ObterValorEmReais();

            return novoValor;
        }

        public String ObterMensagemDeLimiteDeFaixaAtingido(Assinatura assinatura, int quantidadeAtualDeProfissionaisAtivosComAgenda)
        {
            String mensagem = String.Empty;

            if (FormaPagamentoHelper.TemProcessoAssincrono(assinatura.FormaPagamento))
            {
                mensagem = RESMensagens.LimiteDeFaixaAtingidoParaAssinaturaComBoletos;
            }
            else
            {
                var novoValor = ObterValorDaProximaFaixaDePreco(assinatura, quantidadeAtualDeProfissionaisAtivosComAgenda);
                mensagem = String.Format(RESMensagens.LimiteDeFaixaAtingidoParaAssinaturaComCartaoDeCredito, novoValor);
            }

            return mensagem;
        }

        public DateTime ObterDataInicioProximoPeriodoSemFatura(Assinatura assinatura)
        {
            if (assinatura == null)
                return Calendario.Hoje();

            var fimPeriodoGratuito = assinatura.DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura();
            var dataInicioAssinatura = assinatura.EhAtivaEVigente(DateTime.Now) ? assinatura.DataInicio : Calendario.Hoje();

            var existeDataPrimeiraFatura = Domain.Cobranca.FaturaRepository.ObterDataPrimeiraFaturaPaga(assinatura.IdAssinatura) == null;
            if (existeDataPrimeiraFatura && assinatura.EhAtivaEVigente(DateTime.Now))
                dataInicioAssinatura = Calendario.Hoje();

            var maiorDataFimReferenciaDeFaturasPaga = Domain.Cobranca.FaturaRepository.ObterMaiorDataFimReferenciaDeFaturasPaga(assinatura.ContaFinanceira);

            return ObterDataInicioProximoPeriodoSemFatura(dataInicioAssinatura, fimPeriodoGratuito, maiorDataFimReferenciaDeFaturasPaga);
        }

        public static DateTime ObterDataInicioProximoPeriodoSemFatura(DateTime? dataAssinatura, DateTime fimPeriodoGratuito, DateTime? maiorDataFimReferenciaDeFaturasPaga)
        {
            var fimDoUltimoPeriodoAdimplente = fimPeriodoGratuito;

            fimDoUltimoPeriodoAdimplente = maiorDataFimReferenciaDeFaturasPaga > fimDoUltimoPeriodoAdimplente
                ? maiorDataFimReferenciaDeFaturasPaga.Value : fimDoUltimoPeriodoAdimplente;

            fimDoUltimoPeriodoAdimplente = dataAssinatura.HasValue && dataAssinatura.Value.AddDays(-1) > fimDoUltimoPeriodoAdimplente
                ? dataAssinatura.Value.AddDays(-1) : fimDoUltimoPeriodoAdimplente;

            var dataInicioProximoPeriodo = fimDoUltimoPeriodoAdimplente.AddDays(1);

            return dataInicioProximoPeriodo;
        }

        public bool VerificarSeCartaoDeCreditoExpirou(int idAssinatura)
        {
            var expirou = Domain.Cobranca.FaturaRepository.VerificarSeCartaoExpirouDaUltimaFaturaPaga(idAssinatura);
            return expirou;
        }

        public void AtivarCobrancaManual(Int32 idAssinatura, Conta ContaAutenticada)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    assinatura.ContaFinanceira.Pessoa.IdPessoa);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");
            if (estabelecimento == null)
                throw new Exception("Estabelecimento está nulo");

            if (assinatura.ContaFinanceira.Status == StatusContaFinanceira.CobrancaManual)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Conta financeira já está em cobrança manual.\nNenhuma alteração realizada");

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.ContaFinanceira.Status = StatusContaFinanceira.CobrancaManual;
                assinatura.DataFim = null;
                assinatura.Ativo = true;
                assinatura.ContaFinanceira.DataCancelamento = null;
                estabelecimento.HabilitaCobranca = false;
                Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, Calendario.Agora());
                estabelecimento.Update();
                Domain.Pessoas.EnvioEmailService.EnviarEmailStatusCobrancaAlterado(
                    estabelecimento.NomeDeExibicaoNoPortal, assinatura.ContaFinanceira.Status.ToString(),
                    ContaAutenticada);

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(assinatura.ContaFinanceira);
            }
        }

        public void TrocarFormaDePagamentoParaBoleto(Int32 idAssinatura, DateTime dataAtual)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);
            var existemFaturasPendentesDePagamento = Domain.Cobranca.FaturaRepository.ExisteFaturaPendenteDePagamento(assinatura.ContaFinanceira.Pessoa, dataAtual);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.FormaPagamento = FormaDePagamentoEnum.Boleto;

                if (existemFaturasPendentesDePagamento)
                {
                    var listaDeFaturasAindaPendentesDePagamento = Domain.Cobranca.FaturaRepository.ObterTodasPendentesDePagamentoOuQueSeraoPagasUmDia(idAssinatura);
                    foreach (var fatura in listaDeFaturasAindaPendentesDePagamento)
                    {
                        fatura.FormaPagamento = FormaDePagamentoEnum.Boleto;
                        Domain.Cobranca.FaturaRepository.Update(fatura);
                    }
                }

                Domain.Cobranca.AssinaturaRepository.Update(assinatura);
            }
        }

        public void DesativarCobrancaManual(Int32 idAssinatura, Conta ContaAutenticada)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    assinatura.ContaFinanceira.Pessoa.IdPessoa);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");

            if (estabelecimento == null)
                throw new Exception("Estabelecimento está nulo");

            if (assinatura.ContaFinanceira.Status != StatusContaFinanceira.CobrancaManual)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Conta financeira não está em cobrança manual.\nNenhuma alteração realizada");

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.ContaFinanceira.Status = StatusContaFinanceira.PeriodoGratis;
                estabelecimento.HabilitaCobranca = true;
                Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, Calendario.Agora());
                estabelecimento.Update();
                Domain.Pessoas.EnvioEmailService.EnviarEmailStatusCobrancaAlterado(
                    estabelecimento.NomeDeExibicaoNoPortal, assinatura.ContaFinanceira.Status.ToString(),
                    ContaAutenticada);

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(assinatura.ContaFinanceira);
            }
        }

        public void EstenderPrazoGratuidade(Int32 idAssinatura, DateTime novaDataFinalAssinaturaGratis,
            Conta ContaAutenticada)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");

            if (assinatura.ContaFinanceira.Status != StatusContaFinanceira.PeriodoGratis)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Conta financeira precisa estar em período gratuito!\nNenhuma alteração realizada.");
            }

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.DiasGratis += novaDataFinalAssinaturaGratis.Subtract(assinatura.DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura()).Days;
                Domain.Pessoas.EnvioEmailService.EnviarEmailPeriodoGratuidadeEstendido(
                    assinatura.ContaFinanceira.Pessoa.PessoaJuridica.NomeFantasia,
                    novaDataFinalAssinaturaGratis.Date.ToString("dd/MM/yyyy"), ContaAutenticada);
            }
        }

        public void TransformarEmPeriodoGratis(Int32 idAssinatura, Conta ContaAutenticada, bool validarStatusAtual = true)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    assinatura.ContaFinanceira.Pessoa.IdPessoa);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");

            if (estabelecimento == null)
                throw new Exception("Estabelecimento está nulo");

            if (validarStatusAtual && assinatura.ContaFinanceira.Status != StatusContaFinanceira.NaoAssinado)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Operação válida apenas para contas financeiras não assinadas!\nNenhuma alteração realizada.");

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.ContaFinanceira.Status = StatusContaFinanceira.PeriodoGratis;
                assinatura.DataFim = null;
                assinatura.Ativo = true;
                assinatura.ContaFinanceira.DataCancelamento = null;
                estabelecimento.HabilitaCobranca = true;
                estabelecimento.EstabelecimentoConfiguracaoGeral.PermiteAgendarGoogleReserve = true;
                if (estabelecimento.Hotsite() != null)
                {
                    estabelecimento.Hotsite().PermiteAgendamentoHotsite = true;
                    estabelecimento.Hotsite().PermiteBuscaHotsite = estabelecimento.Hotsite().ValorAnteriorPermiteBuscaHotsite ?? false;
                }
                Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, Calendario.Agora());
                estabelecimento.Update();
                Domain.Pessoas.EnvioEmailService.EnviarEmailStatusCobrancaAlterado(
                    estabelecimento.NomeDeExibicaoNoPortal, assinatura.ContaFinanceira.Status.ToString(),
                    ContaAutenticada);

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(assinatura.ContaFinanceira);
            }
        }

        public void MudarStatusParaAdimplente(Int32 idAssinatura, Conta ContaAutenticada)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    assinatura.ContaFinanceira.Pessoa.IdPessoa);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");

            if (estabelecimento == null)
                throw new Exception("Estabelecimento está nulo");

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.ContaFinanceira.Status = StatusContaFinanceira.Adimplente;
                assinatura.DataFim = null;
                assinatura.Ativo = true;
                assinatura.ContaFinanceira.DataCancelamento = null;
                estabelecimento.HabilitaCobranca = true;
                estabelecimento.EstabelecimentoConfiguracaoGeral.PermiteAgendarGoogleReserve = true;
                if (estabelecimento.Hotsite() != null)
                {
                    estabelecimento.Hotsite().PermiteAgendamentoHotsite = true;
                    estabelecimento.Hotsite().PermiteBuscaHotsite = estabelecimento.Hotsite().ValorAnteriorPermiteBuscaHotsite ?? false;
                }
                Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, Calendario.Agora());
                estabelecimento.Update();
                Domain.Pessoas.EnvioEmailService.EnviarEmailStatusCobrancaAlterado(
                    estabelecimento.NomeDeExibicaoNoPortal, assinatura.ContaFinanceira.Status.ToString(),
                    ContaAutenticada);

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(assinatura.ContaFinanceira);
            }
        }

        public void TransformarCanceladoEmNaoAssinado(Int32 idAssinatura, Conta ContaAutenticada, bool validarStatusAtual = true)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    assinatura.ContaFinanceira.Pessoa.IdPessoa);

            if (assinatura == null)
                throw new Exception("Assinatura está nula");

            if (estabelecimento == null)
                throw new Exception("Estabelecimento está nulo");

            if (validarStatusAtual && assinatura.ContaFinanceira.Status != StatusContaFinanceira.ContaCancelada)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Conta financeira não está cancelada.\nNenhuma alteração realizada");

            if (ValidationHelper.Instance.IsValid)
            {
                assinatura.ContaFinanceira.Status = StatusContaFinanceira.NaoAssinado;
                assinatura.DataFim = null;
                assinatura.Ativo = true;
                assinatura.ContaFinanceira.DataCancelamento = null;
                if (estabelecimento.Hotsite() != null)
                {
                    estabelecimento.Hotsite().PermiteBuscaHotsite = false;
                    estabelecimento.Hotsite().PermiteExibicaoTelefonesHotsite = true;
                }
                Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, Calendario.Agora());
                estabelecimento.Update();
                Domain.Pessoas.EnvioEmailService.EnviarEmailStatusCobrancaAlterado(
                    assinatura.ContaFinanceira.Pessoa.PessoaJuridica.NomeFantasia,
                    assinatura.ContaFinanceira.Status.ToString(), ContaAutenticada);

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(assinatura.ContaFinanceira);
            }
        }

        public void EstenderDuracaoFatura(Int32 idAssinatura, DateTime novaDataVencimento, Conta ContaAutenticada, DateTime dataAtual)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(idAssinatura);

            var assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaVigenteEOuAtivaPorConta(assinatura.ContaFinanceira);
            if (assinaturaVigente == null)
                assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaPorConta(assinatura.ContaFinanceira);

            if (assinaturaVigente != null)
            {
                var fatura = Domain.Cobranca.FaturaRepository.ObterUltimaFatura(idAssinatura);

                if (assinatura == null)
                    throw new Exception("Assinatura está nula");

                if (fatura == null)
                    throw new Exception("Fatura está nula");

                if (!assinatura.EhAdimplente() &&
                    !assinatura.EhInadimplenteNoPeriodoDeTolerancia() &&
                    !assinatura.EhInadimplenteForaDoPeriodoDeTolerancia())
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "Conta financeira deve ser adimplente, inadimplente em tolerância ou inadimplente fora de tolerância.\nNenhuma alteração realizada.");

                if (ValidationHelper.Instance.IsValid)
                {
                    fatura.DataVencimento = novaDataVencimento.Date;
                    fatura.DataDeTolerancia = Domain.Cobranca.FaturaService.CalcularDataDeToleranciaDaFatura(fatura);

                    Domain.Cobranca.FaturaRepository.Update(fatura);
                    Domain.Pessoas.EnvioEmailService.EnviarEmailDuracaoFaturaEstendida(
                        assinatura.ContaFinanceira.Pessoa.PessoaJuridica.NomeFantasia,
                        novaDataVencimento.Date.ToString("dd/MM/yyyy"), ContaAutenticada);

                    Domain.Cobranca.ContaFinanceiraService.AtualizarStatusDaContaParaAdimplenteInadimplenteOuForaDeTolerancia(assinatura.ContaFinanceira, dataAtual);
                }
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Conta financeira não possui assinatura vigente válida.");
            }
        }

        public List<Int32> ObterPossiveisDiasVencimentoBoleto(Estabelecimento estabelecimento)
        {
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);

            var assinaturaAtivaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaVigentePorConta(contaFinanceira);

            DateTime dataDeReferencia;

            if (assinaturaAtivaVigente != null)
                dataDeReferencia =
                    Domain.Cobranca.AssinaturaService.ObterDataInicioProximoPeriodoSemFatura(assinaturaAtivaVigente);
            else
            {
                dataDeReferencia = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametro(Calendario.Hoje());
            }

            var diasPossiveis =
                new ParametrosTrinks<List<Int32>>(ParametrosTrinksEnum.boleto_dias_vencimento).ObterValor();
            var diasPossiveisAPartirDaDataDeReferencia = diasPossiveis.Where(d => d >= dataDeReferencia.Day).ToList();
            diasPossiveisAPartirDaDataDeReferencia.AddRange(diasPossiveis);

            var tresPrimeirosDias = new List<Int32>();

            foreach (var dia in diasPossiveisAPartirDaDataDeReferencia)
            {
                if (tresPrimeirosDias.Count < 3)
                {
                    tresPrimeirosDias.Add(dia);
                }
                else
                    break;
            }

            return tresPrimeirosDias;
        }

        public DateTime ObterProximoDiaVencimento(DateTime? dataInicioReferencia, int diaVencimento, bool primeirafatura = false)
        {
            DateTime valorDataReferencia = dataInicioReferencia ?? DateTime.Today;
            DateTime dataAux = valorDataReferencia.AddMonths(1);

            int diasNoMes = DateTime.DaysInMonth(dataAux.Year, dataAux.Month);
            DateTime dataProximoVencimento;

            if (diasNoMes >= diaVencimento)
            {
                dataProximoVencimento = new DateTime(dataAux.Year, dataAux.Month, diaVencimento);
            }
            else
            {
                int ultimoDiaMesSeguinte = DateTime.DaysInMonth(dataAux.Year, dataAux.Month);
                dataProximoVencimento = new DateTime(dataAux.Year, dataAux.Month, ultimoDiaMesSeguinte);
            }

            if (primeirafatura)
            {
                TimeSpan diferenca = dataProximoVencimento - valorDataReferencia;
                if (diferenca.Days < diasNoMes)
                {
                    dataProximoVencimento = dataProximoVencimento.AddMonths(1);
                }
            }

            return dataProximoVencimento;
        }

        public DateTime ObterProximoDataVencimento(Assinatura assinatura, DateTime dataHora)
        {
            var fimDegustacao = assinatura.DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura();
            var fimDoUltimoPeriodoPago = Domain.Cobranca.FaturaRepository.ObterMaiorDataFimReferenciaDeFaturasNaoCanceladas(assinatura.ContaFinanceira) ?? Calendario.Hoje().AddDays(-1);
            var dataAnteriorAAssinatura = (assinatura.DataInicio ?? dataHora.Date).AddDays(-1);

            var fimDoUltimoPeriodo = fimDoUltimoPeriodoPago > fimDegustacao ? fimDoUltimoPeriodoPago : fimDegustacao;
            fimDoUltimoPeriodo = fimDoUltimoPeriodo > dataAnteriorAAssinatura ? fimDoUltimoPeriodo : dataAnteriorAAssinatura;

            return fimDoUltimoPeriodo.AddDays(1);
        }

        public void DefinirDiaDeVencimento(Assinatura assinatura, DateTime dataHora)
        {
            if (assinatura.DiaDeVencimento.HasValue)
                throw new Exception("Esta assinatura já possui dia de vencimento");

            var fimDoUltimoPeriodo = ObterProximoDataVencimento(assinatura, dataHora);

            var diaVencimento = assinatura.DiaDeVencimento ?? fimDoUltimoPeriodo.AddDays(1).Day;
            assinatura.DiaDeVencimento = diaVencimento;
        }

        public bool FaturaTemBoletoPendenteDePagamento(Fatura fatura)
        {
            var retorno = false;
            if (fatura != null && fatura.FormaPagamento == FormaDePagamentoEnum.Boleto)
            {
                var parametro =
                    new ParametrosTrinks<int>(
                        ParametrosTrinksEnum.qtd_dias_antecedencia_verificacao_boleto_pendente_pagamento).ObterValor();
                if (fatura != null && fatura.DataVencimento.HasValue &&
                    fatura.DataVencimento.Value.AddDays(parametro).Date <= Calendario.Hoje())
                {
                    retorno = true;
                }
            }
            return retorno;
        }

        public void AlterarPlanoDeAssinaturaDoEstabelecimento(int idAssinatura, int idPlanoAssinatura, PessoaFisica pessoaLogada, IList<ContratacaoDeOfertaDeServicoAdicionalDTO> servicosAdicionais)
        {

            #region Paramentros de apoio

            Assinatura assinatura = AssinaturaRepository.Load(idAssinatura);
            PlanoAssinatura planoSelecionado = Domain.Cobranca.PlanoAssinaturaRepository.Load(idPlanoAssinatura);
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(assinatura.ContaFinanceira.Pessoa.IdPessoa);
            List<AdicionalNaAssinatura> ofertasAtivasNaAssinatura = new List<AdicionalNaAssinatura>();
            bool trocouDePlano = false;

            #endregion Paramentros de apoio

            ValidarTrocaDoPlanoDeAssinatura(assinatura, planoSelecionado, estabelecimento, ref trocouDePlano);
            ValidarContratacaoECancelamentoDeServicosAdicionaisDoPlano(servicosAdicionais, assinatura, ref ofertasAtivasNaAssinatura);

            if (ValidationHelper.Instance.IsValid)
            {// && !EhCobrancaManual(assinatura)) {
                if (trocouDePlano)
                {
                    if (!planoSelecionado.PermiteContratarAdicionaisPorFora)
                    {
                        ConfigurarEstabelecimentoComItensDisponiveisNoPlanoDeAssinatura(estabelecimento, planoSelecionado, assinatura);
                    }
                    TrocarPlanoDaAssinatura(assinatura, planoSelecionado, pessoaLogada);
                }

                Domain.Cobranca.AdicionalNaAssinaturaService.ContratarOuCancelarServicosAdicionaisDaAssinatura(assinatura, pessoaLogada, servicosAdicionais, ofertasAtivasNaAssinatura);

                if (trocouDePlano && FormaPagamentoHelper.TemProcessoAssincrono(assinatura.FormaPagamento) && planoSelecionado.QuantidadeDeParcelas > 1)
                    CorrigirFaturaDeBoletoParaONovoPlano(assinatura);
            }
        }

        public void TrocarPlanoDaAssinatura(Assinatura assinatura, PlanoAssinatura planoSelecionado, PessoaFisica pessoaLogada)
        {
            string nomeDoPlanoAnterior = assinatura.PlanoAssinatura.Nome;
            AplicarInformacoesDoPlanoNaAssinatura(Calendario.Agora(), planoSelecionado, assinatura, null);
            Domain.Cobranca.AssinaturaRepository.Update(assinatura);
            AtualizarAdicionaisContratadosComAsOfertasDoPlanoAssinado(assinatura, pessoaLogada, "Troca de plano", $"Plano anterior: {nomeDoPlanoAnterior}");

            AdicionarBeneficiosDoPlanoNaAssinatura(assinatura, pessoaLogada);
        }
        public void AplicarInformacoesDoPlanoNaAssinaturaParaEstabelecimentosSemAssinaturaAtiva(Assinatura assinatura, PlanoAssinatura planoSelecionado)
        {
            if (assinatura.PrecisaAssinar())
            {
                AplicarInformacoesDoPlanoNaAssinatura(Calendario.Agora(), planoSelecionado, assinatura, null);
                Domain.Cobranca.AssinaturaRepository.Update(assinatura);
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O estabelecimento não deve ter assinatura ativa");
            }
        }

        private void AtualizarAdicionaisContratadosComAsOfertasDoPlanoAssinado(Assinatura assinatura, PessoaFisica pessoaLogada, string identificacaoMotivo, string comentarioMotivo)
        {
            var adicionaisAtivos = Domain.Cobranca.AdicionalNaAssinaturaRepository.ObterAdicionaisAtivosNaAssinatura(assinatura);

            if (!adicionaisAtivos.Any())
                return;

            var ofertasDoPlano = Domain.Cobranca.OfertaDeServicoAdicionalService.ListarOfertasDisponiveisNoPlanoDeAssintura(assinatura.PlanoAssinatura.IdPlano);

            foreach (var adicionalNaAssinatura in adicionaisAtivos)
            {
                var ofertaDesteServico = ofertasDoPlano.FirstOrDefault(ofe => ofe.Servico == adicionalNaAssinatura.Servico);
                var motivoTroca = new MotivoHistoricoDoAdicional(identificacaoMotivo, comentarioMotivo);
                Domain.Cobranca.AdicionalNaAssinaturaService.TrocarOfertaDoAdicionalContratado(adicionalNaAssinatura, ofertaDesteServico, pessoaLogada, motivoTroca);
            }
        }

        private void ValidarContratacaoECancelamentoDeServicosAdicionaisDoPlano(IList<ContratacaoDeOfertaDeServicoAdicionalDTO> servicosAdicionais, Assinatura assinatura, ref List<AdicionalNaAssinatura> ofertasAtivasNaAssinatura)
        {
            if (servicosAdicionais.Any())
            {
                ofertasAtivasNaAssinatura = Domain.Cobranca.AdicionalNaAssinaturaRepository.FiltrarAtivosPorAssinatura(assinatura.IdAssinatura).ToList();
                foreach (var servicoAdicional in servicosAdicionais)
                {
                    var servicoJaContratado = ofertasAtivasNaAssinatura.FirstOrDefault(o => o.OfertaEscolhida.Id == servicoAdicional.IdOfertaDeServicoAdicional);
                    if (!servicoAdicional.DesejaTerOferta
                        && servicoJaContratado != null
                        && assinatura.ContaFinanceira.Status != StatusContaFinanceira.CobrancaManual
                        && !Domain.Cobranca.AdicionalNaAssinaturaService.ExisteAoMenosUmAdicionalCobradoEmUmaFaturaGeradaAposAdicionalContratado(assinatura, idOfertaDeServicoAdicional: servicoAdicional.IdOfertaDeServicoAdicional))
                        ValidationHelper.Instance.AdicionarItemValidacao("Para cancelar o adicional de " + servicoJaContratado.OfertaEscolhida.Servico.Nome + ", é necessário ter pelo menos uma fatura com a cobrança do mesmo.");
                }
            }
        }

        public void ValidarTrocaDoPlanoDeAssinatura(Assinatura assinatura, PlanoAssinatura planoSelecionado, Estabelecimento estabelecimento, ref bool trocouDePlano)
        {
            trocouDePlano = assinatura.PlanoAssinatura != planoSelecionado;

            if (trocouDePlano)
            {
                //if (assinatura.PossuiPlanoComFidelidade() && !assinatura.PodeTrocarDePlanoNoMomento() && assinatura.PlanoAssinatura.QuantidadeDeParcelas != planoSelecionado.QuantidadeDeParcelas)
                //    ValidationHelper.Instance.AdicionarItemValidacao("A assinatura tem um plano com fidelidade e ainda não está no período de renovação para a troca de plano.");

                //if (planoSelecionado.HabilitaUsoDeNFSe && !EhPlanoEspecialEsmalteria(planoSelecionado)) {
                //    if (!ExisteImplementacaoDeNotaFiscalDeServicoNoMunicipio(estabelecimento))
                //        ValidationHelper.Instance.AdicionarItemValidacao("Plano não pode ser selecionado, pois a emissão de nota de serviço não está implementada para o município deste estabelecimento! ");
                //}

                //if (planoSelecionado.HabilitaUsoDeNFCe && !EhPlanoEsmalteriaComNotaDeProduto(planoSelecionado)) {
                //    if (!NFcPossuiImplementacaoNoEstadoDoEstabelecimento(estabelecimento))
                //        ValidationHelper.Instance.AdicionarItemValidacao("Plano não pode ser selecionado, pois a emissão de nota de produto não está implementada para o estado deste estabelecimento!");
                //}

                if (planoSelecionado.DuracaoDoPlanoComFidelidade > 1 && FormaPagamentoHelper.TemProcessoAssincrono(assinatura.FormaPagamento))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Pagamento por boleto ou PIX só pode utilizar em planos mensais.");
                }

                var estaVoltandoParaEstruturaAntiga = !planoSelecionado.PermiteContratarAdicionaisPorFora && assinatura.PlanoAssinatura.PermiteContratarAdicionaisPorFora;
                if (estaVoltandoParaEstruturaAntiga)
                {
                    var idsServicos = Domain.Cobranca.AdicionalNaAssinaturaRepository.ListarIdsServicosDosAdicionaisAtivosNaAssinatura(assinatura.IdAssinatura);
                    var temServicoAdicionalContratadoPorFora = idsServicos.Any(id => id != (int)ServicoAdicionalEnum.LembretePremium);
                    if (temServicoAdicionalContratadoPorFora)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao($"O plano {planoSelecionado.Nome} não possui suporte a contratação de adicionais.\nPara continuar, é necessário realizar o cancelamento dos adicionais ativos na assinatura.");
                    }
                }
            }
        }

        public bool NFcPossuiImplementacaoNoEstadoDoEstabelecimento(Estabelecimento estabelecimento)
        {
            var configuracaoNfcEstado = Domain.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.ObterConfiguracaoNFCeDoUF(estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF);
            return configuracaoNfcEstado != null && configuracaoNfcEstado.NFCEImplementada;
        }

        public bool ExisteImplementacaoDeNotaFiscalDeServicoNoMunicipio(Estabelecimento estabelecimento)
        {
            if ((estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade != null)
                && (estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade != null)
                && (estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE != null))
            {
                return Domain.Pessoas.CidadeRepository.ExisteImplementacaoDeNFSeParaCidade(estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);
            }
            else
            {
                return false;
            }
        }

        public void ConfigurarEstabelecimentoComItensDisponiveisNoPlanoDeAssinatura(Estabelecimento estabelecimento, PlanoAssinatura planoAssinatura, Assinatura assinatura)
        {
            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);

            if (planoAssinatura.HabilitaUsoDeNFSe && planoAssinatura.HabilitaUsoDeNFCe)
            {
                AlterarPlanoParaNFCeENFSeHabilitados(estabelecimento);
            }
            else if (planoAssinatura.HabilitaUsoDeNFSe)
            {
                AlterarPlanoParaNFSeHabilitado(planoAssinatura, estabelecimento);
            }
            else if (planoAssinatura.HabilitaUsoDeNFCe)
            {
                AlterarPlanoParaNFCeHabilitado(estabelecimento);
            }
            else
            {
                DesabilitaUsoDeNotas(estabelecimento);
            }

            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
        }

        private static void CorrigirFaturaDeBoletoParaONovoPlano(Assinatura assinatura)
        {
            CancelarFaturaPorBoletoNaoPagaDoPlanoAnterior(assinatura);
            if (Domain.Cobranca.AssinaturaRepository.AssinaturaPossuiFaturaRecorrenteAGerar(assinatura, Calendario.Hoje()))
                Domain.Cobranca.FaturaService.GerarNovaFatura(assinatura, Calendario.Hoje());
        }

        private static void CancelarFaturaPorBoletoNaoPagaDoPlanoAnterior(Assinatura assinatura)
        {
            var faturaPorBoletoNaoPagaDoPlanoAnterior = Domain.Cobranca.FaturaRepository.ObterFaturaPorBoletoMaisAntigaPendenteDePagamento(assinatura.IdAssinatura, Calendario.Hoje());
            if (faturaPorBoletoNaoPagaDoPlanoAnterior != null)
                Domain.Cobranca.FaturaService.CancelarFatura(faturaPorBoletoNaoPagaDoPlanoAnterior, Calendario.Agora());
        }

        private void AlterarPlanoParaNFCeENFSeHabilitados(Estabelecimento estabelecimento)
        {
            HabilitaUsoDeNFC(estabelecimento);
            HabilitaUsoDeNFS(estabelecimento);
        }

        //private void AlterarPlanoParaNFCeENFSeHabilitados(PlanoAssinatura planoSelecionado, Estabelecimento estabelecimento) {
        //    bool existeImplementacaoNoMunicipio = Domain.Pessoas.CidadeRepository.ExisteImplementacaoDeNFSeParaCidade(estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);
        //    var configuracaoNFC = Domain.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.ObterConfiguracaoNFCeDoUF(estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF);

        //    if (!EhPlanoEsmalteriaComNotaDeProduto(planoSelecionado)) {
        //        if (existeImplementacaoNoMunicipio && configuracaoNFC != null && configuracaoNFC.NFCEImplementada) {
        //            estabelecimento = HabilitaUsoDeNFC(estabelecimento);
        //            estabelecimento = HabilitaUsoDeNFS(estabelecimento);
        //        }
        //        else
        //            ValidationHelper.Instance.AdicionarItemValidacao("Plano não pode ser selecionado, pois a emissão de nota de serviço e/ou produto não estão implementadas para o município ou para o estado deste estabelecimento!");
        //    }
        //    else {
        //        //planoSelecionado == PlanoEspecial Esmalteria Com Nota de produto
        //        if (configuracaoNFC != null && configuracaoNFC.NFCEImplementada) {
        //            estabelecimento = HabilitaUsoDeNFC(estabelecimento);
        //            if (existeImplementacaoNoMunicipio) {
        //                estabelecimento = HabilitaUsoDeNFS(estabelecimento);
        //            }
        //        }
        //        else
        //            ValidationHelper.Instance.AdicionarItemValidacao("Plano não pode ser selecionado, pois a emissão de nota de produto não está implementada para o estado deste estabelecimento!");
        //    }
        //}

        private void AlterarPlanoParaNFCeHabilitado(Estabelecimento estabelecimento)
        {
            HabilitaUsoDeNFC(estabelecimento);
            DesabilitaUsoDeNFS(estabelecimento);
        }

        private void AlterarPlanoParaNFSeHabilitado(PlanoAssinatura planoSelecionado, Estabelecimento estabelecimento)
        {
            HabilitaUsoDeNFS(estabelecimento);
            DesabilitaUsoDeNFC(estabelecimento);
        }

        //private void AlterarPlanoParaNFSeHabilitadoEsmalteria(Estabelecimento estabelecimento) {
        //    bool existeImplementacaoNoMunicipio = Domain.Pessoas.CidadeRepository.ExisteImplementacaoDeNFSeParaCidade(estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);

        //    if (existeImplementacaoNoMunicipio) {
        //        estabelecimento = HabilitaUsoDeNFS(estabelecimento);
        //        estabelecimento = DesabilitaUsoDeNFC(estabelecimento);
        //    }
        //}

        //private void AlterarPlanoParaNFSeHabilitado(Estabelecimento estabelecimento) {
        //    bool existeImplementacaoNoMunicipio = Domain.Pessoas.CidadeRepository.ExisteImplementacaoDeNFSeParaCidade(estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);
        //    if (existeImplementacaoNoMunicipio) {
        //    }
        //    else
        //        ValidationHelper.Instance.AdicionarItemValidacao("Plano não pode ser selecionado, pois a emissão de nota de serviço não está implementada para o município deste estabelecimento! ");
        //}

        #region PRIVADOS

        private Fatura Reassinar(Estabelecimento estabelecimento, FonteDePagamento fonteDePagamento, PessoaFisica pessoaLogada, String ipComprador, DateTime dataHora,
            PlanoAssinatura planoEscolhido, int? idFaixaEscolhida, List<ServicoTrinksAContratarDTO> adicionaisContratados)
        {
            var contaFinanceiraAtual =
                Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);

            Assinatura novaAssinatura;

            if (contaFinanceiraAtual != null && contaFinanceiraAtual.Status == StatusContaFinanceira.ContaCancelada)
            {
                contaFinanceiraAtual.Status = StatusContaFinanceira.NaoAssinado;
                contaFinanceiraAtual.DataCancelamento = null;
                Domain.Cobranca.ContaFinanceiraRepository.Update(contaFinanceiraAtual);

                novaAssinatura = GerarNovaSemDireitoADegustacao(contaFinanceiraAtual, fonteDePagamento.FormaPagamento);
            }
            else if (contaFinanceiraAtual == null)
            {
                var novaContaFinanceira = Domain.Cobranca.ContaFinanceiraService.GerarNovaConta(estabelecimento);
                novaAssinatura = GerarNovaSemDireitoADegustacao(novaContaFinanceira, fonteDePagamento.FormaPagamento);
            }
            else
            {
                novaAssinatura = contaFinanceiraAtual.AssinaturaAtiva();
            }

            if (novaAssinatura == null)
                novaAssinatura = GerarNovaSemDireitoADegustacao(contaFinanceiraAtual, fonteDePagamento.FormaPagamento);

            AplicarInformacoesDoPlanoNaAssinatura(dataHora, planoEscolhido, novaAssinatura, idFaixaEscolhida);

            AssociarPromocaoNaAssinaturaSeHouver(novaAssinatura, fonteDePagamento, estabelecimento);

            ContratarAdicionaisNaAssinatura(adicionaisContratados, novaAssinatura, pessoaLogada, "Contratação realizada pela tela Fluxo de Assinatura");

            AdicionarBeneficiosDoPlanoNaAssinatura(novaAssinatura, pessoaLogada, adicionaisContratados);

            Domain.Cobranca.PagamentoDeFaturaService.GravarInformacoesNaAssinaturaPelaFonteDePagamento(novaAssinatura,
                fonteDePagamento);

            var fatura = Domain.Cobranca.FaturaService.GerarNovaFatura(novaAssinatura, dataHora);
            Domain.Cobranca.FaturaService.ProcessarPagamentoAvulso(fatura, fonteDePagamento, ipComprador, dataHora);

            if (fatura.Status == StatusFaturaEnum.Paga || fatura.Status == StatusFaturaEnum.Aberta)
                HabilitarExibicaoNoPortalEBuscaPeloHotSite(novaAssinatura);

            Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, dataHora);

            return fatura;
        }

        private void AplicarInformacoesDoPlanoNaAssinatura(DateTime dataHora, PlanoAssinatura planoEscolhido, Assinatura assinatura, int? idFaixaEscolhida)
        {
            if (idFaixaEscolhida != null)
            {
                var faixa = Domain.Cobranca.ValorPorFaixaRepository.Load(idFaixaEscolhida.Value);
                assinatura.QuantidadeDeProfissionaisEsperada = faixa.LimiteSuperior ?? faixa.LimiteInferior;
            }
            assinatura.PlanoAssinatura = planoEscolhido;
            assinatura.DataFimDeDuracaoDoPlano = Domain.Cobranca.AssinaturaService.ObterDataFimDeDuracaoDoPlano(assinatura, planoEscolhido);
            assinatura.PodeCancelarAPartirDe = null;
            if (assinatura.DataFimDeDuracaoDoPlano != null)
            {
                int diasDeAntecedenciaParaPoderCancelar = new ParametrosTrinks<int>(ParametrosTrinksEnum.dias_antecedencia_para_cancelar_assinatura).ObterValor();
                assinatura.PodeCancelarAPartirDe = assinatura.DataFimDeDuracaoDoPlano.Value.AddDays(-diasDeAntecedenciaParaPoderCancelar);
            }
        }

        private Fatura Assinar(Assinatura assinaturaAtiva, FonteDePagamento fonteDePagamento, String ipComprador,
            DateTime dataHora, List<ServicoTrinksAContratarDTO> adicionaisContratados, PessoaFisica pessoaFisicaLogada)
        {
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    assinaturaAtiva.ContaFinanceira.Pessoa.IdPessoa);

            ContratarAdicionaisNaAssinatura(adicionaisContratados, assinaturaAtiva, pessoaFisicaLogada, "Contratação realizada pela tela Fluxo de Assinatura");

            AdicionarBeneficiosDoPlanoNaAssinatura(assinaturaAtiva, pessoaFisicaLogada, adicionaisContratados);

            AssociarPromocaoNaAssinaturaSeHouver(assinaturaAtiva, fonteDePagamento, estabelecimento);

            var fatura = Domain.Cobranca.FaturaService.GerarNovaFatura(assinaturaAtiva, dataHora);
            Domain.Cobranca.FaturaService.ProcessarPagamentoAvulso(fatura, fonteDePagamento, ipComprador, dataHora);

            Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, dataHora);

            var configHotSite = fatura.Assinatura.ContaFinanceira.Pessoa.PessoaJuridica.Estabelecimento.Hotsite();
            if (!configHotSite.PermiteBuscaHotsite)
                configHotSite.PermiteBuscaHotsite = configHotSite.ValorAnteriorPermiteBuscaHotsite ?? false;

            return fatura;
        }

        public void ContratarAdicionaisNaAssinatura(List<ServicoTrinksAContratarDTO> adicionaisContratados, Assinatura assinaturaAtiva, PessoaFisica pessoaFisicaLogada, string motivoDoAdicional)
        {
            if (adicionaisContratados != null)
            {
                foreach (var servicoAdicional in adicionaisContratados)
                {
                    FormaDeContratacaoDoAdicional formaDeContratacao = null;

                    if (servicoAdicional.FormaDeContratacaoDoAdicional != null)
                    {
                        formaDeContratacao = Domain.Cobranca.FormaDeContratacaoDoAdicionalRepository.Load((int)servicoAdicional.FormaDeContratacaoDoAdicional);
                    }

                    var adicionalNaAssinatura = Domain.Cobranca.AdicionalNaAssinaturaRepository.Factory.CriarPeloServicoAdicional(assinaturaAtiva, servicoAdicional.ServicoTrinks, pessoaFisicaLogada, formaDeContratacao);
                    var motivo = new MotivoHistoricoDoAdicional("Contratação pelo Backoffice", motivoDoAdicional);

                    var descontoNoAdicional = AplicarDescontoNaBelezinhaContratadaJuntoComAutoatendimento(adicionaisContratados, servicoAdicional);

                    Domain.Cobranca.AdicionalNaAssinaturaService.SalvarNovaContratacao(adicionalNaAssinatura, descontoNoAdicional, motivo);
                }
            }
        }

        public void AdicionarBeneficiosDoPlanoNaAssinatura(Assinatura assinaturaAtiva, PessoaFisica pessoaFisicaLogada, List<ServicoTrinksAContratarDTO> adicionaisSelecionadosNoFluxo = null)
        {
            RemoverBeneficiosAnterioresDoPlanoNaAssinatura(assinaturaAtiva, pessoaFisicaLogada);

            if (!assinaturaAtiva.PlanoAssinatura.PermiteContratarAdicionaisPorFora)
                return;

            var adicionaisBeneficio = Domain.Cobranca.BeneficioDoPlanoAssinaturaService.ListarBeneficiosDoPlanoNaAssinatura(assinaturaAtiva, assinaturaAtiva.PlanoAssinatura);
            if (adicionaisSelecionadosNoFluxo != null)
                adicionaisBeneficio = adicionaisBeneficio.Where(f => adicionaisSelecionadosNoFluxo.Select(a => a.ServicoTrinks.IdServico).Contains(f.Servico.IdServico)).ToList();
            string motivoDoAdicional = $"Beneficio do Plano - {assinaturaAtiva.PlanoAssinatura.Nome}";

            var adicionaisContratados = Domain.Cobranca.AdicionalNaAssinaturaRepository.ObterAdicionaisAtivosNaAssinatura(assinaturaAtiva);

            // Testa se um adicional já foi contratado, se sim adiciona o desconto, senão cadastra o adicional com o desconto
            foreach (var adicionalBeneficio in adicionaisBeneficio)
            {
                var adicional = adicionaisContratados.FirstOrDefault(a => a.Servico.IdServico == adicionalBeneficio.Servico.IdServico);

                var desconto = new DescontoNoAdicionalContratadoDTO
                {
                    Percentual = adicionalBeneficio.PorcentagemDeDesconto,
                    QuantidadeDeDescontosProgramados = adicionalBeneficio.DuracaoBeneficiosEmMeses,
                    ComentarioMotivo = motivoDoAdicional,
                    IdentificaoMotivo = "Contratação de Plano",
                    FoiGeradoPorBeneficioDePlano = true
                };

                if (adicional != null)
                {
                    // add desconto
                    Domain.Cobranca.AdicionalNaAssinaturaService.AtribuirDescontoNoAdicional(adicional, desconto, pessoaFisicaLogada);
                }
                else
                {
                    // add adicional
                    if (adicionalBeneficio.PorcentagemDeDesconto < 100)
                        continue;

                    var adicionalNaAssinatura = Domain.Cobranca.AdicionalNaAssinaturaRepository.Factory.CriarPeloServicoAdicional(assinaturaAtiva, (ServicoAdicionalEnum)adicionalBeneficio.Servico.IdServico, pessoaFisicaLogada);
                    var motivo = new MotivoHistoricoDoAdicional("Benefício na Assinatura de Plano", motivoDoAdicional);

                    Domain.Cobranca.AdicionalNaAssinaturaService.SalvarNovaContratacao(adicionalNaAssinatura, desconto, motivo);
                }
            }
        }

        public bool PodeContratarAdicional(Estabelecimento estabelecimento, ServicoTrinks servico, DisponibilidadeDeContratacaoDoAdicionalDTO disponibilidade)
        {
            if (estabelecimento.EhUmEstabelecimentoFranqueado() && !estabelecimento.FranquiaEstabelecimento.Franquia.PermiteContratarItensAdicionaisNoPlano)
                return false;

            return disponibilidade.PodeContratar;// && servico.ExibicaoNoFluxoAssinatura == true;
        }

        public void RemoverBeneficiosAnterioresDoPlanoNaAssinatura(Assinatura assinaturaAtiva, PessoaFisica pessoaFisicaLogada)
        {
            var descontosAtivos = Domain.Cobranca.DescontoNoAdicionalDaAssinaturaRepository.ListarDescontosAtivosEmAdicionaisQueEstaoAtivosNaAssinatura(assinaturaAtiva.IdAssinatura);

            foreach (var descontoAtivo in descontosAtivos)
            {
                // Se o desconto não foi gerado por um beneficio, não remove
                if (!descontoAtivo.Ativo || !descontoAtivo.FoiGeradoPorBeneficioDePlano)
                    continue;

                var motivo = new MotivoHistoricoDoAdicional("Remover beneficio do plano", "Mudança de plano");
                Domain.Cobranca.AdicionalNaAssinaturaService.RemoverDescontoDoAdicional(descontoAtivo.AdicionalNaAssinatura, pessoaFisicaLogada, motivo);
            }
        }

        private DescontoNoAdicionalContratadoDTO AplicarDescontoNaBelezinhaContratadaJuntoComAutoatendimento(List<ServicoTrinksAContratarDTO> adicionaisContratados, ServicoTrinksAContratarDTO servicoAtual)
        {
            var contemBelezinhaEAutoatendimentoCompleto = adicionaisContratados.Where(servico =>
                servico.ServicoTrinks == ServicoAdicionalEnum.BelezinhaComSplit ||
                servico.ServicoTrinks == ServicoAdicionalEnum.BelezinhaSemSplit ||
                servico.ServicoTrinks == ServicoAdicionalEnum.Autoatendimento && servico.FormaDeContratacaoDoAdicional == FormaDeContratacaoDoAdicionalEnum.CheckInECheckout
            ).Count() == 2;

            DescontoNoAdicionalContratadoDTO descontoNoAdicional = null;

            if (contemBelezinhaEAutoatendimentoCompleto &&
                (servicoAtual.ServicoTrinks == ServicoAdicionalEnum.BelezinhaComSplit || servicoAtual.ServicoTrinks == ServicoAdicionalEnum.BelezinhaSemSplit))
            {
                var motivo = "Contratação de Belezinha e Autoatendimento Completo pelo Plano da Assinatura";

                descontoNoAdicional = new DescontoNoAdicionalContratadoDTO()
                {
                    Percentual = 100,
                    QuantidadeDeDescontosProgramados = 9999,
                    ComentarioMotivo = motivo,
                    IdentificaoMotivo = motivo,
                };
            }

            return descontoNoAdicional;
        }

        private Assinatura GerarNovaSemDireitoADegustacao(ContaFinanceira contaFinanceira, FormaPagamento formaPagamento)
        {
            PlanoAssinatura plano;

            var ultimaAssinatura = contaFinanceira.Assinaturas.LastOrDefault();
            if (ultimaAssinatura == null)
            {
                plano = Domain.Cobranca.PlanoAssinaturaRepository.ObterPlanoDeAssinaturaPadrao();
            }
            else
                plano = ultimaAssinatura.PlanoAssinatura;

            var novaAssinatura = new Assinatura(contaFinanceira, plano) { DiasGratis = 0, FormaPagamento = formaPagamento };
            AssinaturaRepository.SaveNew(novaAssinatura);

            contaFinanceira.DataCancelamento = null;
            contaFinanceira.Ativo = true;

            return novaAssinatura;
        }

        private void HabilitarExibicaoNoPortalEBuscaPeloHotSite(Assinatura assinatura)
        {
            var configHotsite = assinatura.ContaFinanceira.Pessoa.PessoaJuridica.Estabelecimento.Hotsite();

            if (!configHotsite.PermiteBuscaHotsite)
                configHotsite.PermiteBuscaHotsite = configHotsite.ValorAnteriorPermiteBuscaHotsite ?? false;

            //configHotsite.DesejaAparecerBuscaPortal = true;
            configHotsite.DesejaTerHotsite = true;
        }

        private void DesabilitarExibicaoNoPortalEBuscaPeloHotSite(Assinatura assinatura)
        {
            var configHotsite = assinatura.ContaFinanceira.Pessoa.PessoaJuridica.Estabelecimento.Hotsite();
            configHotsite.PermiteBuscaHotsite = false;
            configHotsite.DesejaAparecerBuscaPortal = false;
            configHotsite.DesejaTerHotsite = false;
        }

        private void SalvarAlteracaoDePlano(Assinatura assinatura, PlanoAssinatura plano, Estabelecimento estabelecimento)
        {
            assinatura.PlanoAssinatura = plano;
            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
            Domain.Cobranca.AssinaturaRepository.Update(assinatura);
        }

        private bool EhPlanoEspecialEsmalteria(PlanoAssinatura plano)
        {
            int planoEsmalteria = new ParametrosTrinks<int>(ParametrosTrinksEnum.plano_especial_esmalteria).ObterValor();
            return plano.IdPlano == planoEsmalteria;
        }

        private bool EhPlanoComNotaDeProdutoEServicoENaoEhPlanoEsmalteriaComNotaDeProduto(PlanoAssinatura plano)
        {
            return plano.HabilitaUsoDeNFSe && plano.HabilitaUsoDeNFCe && !EhPlanoEsmalteriaComNotaDeProduto(plano);
        }

        private bool EhPlanoEsmalteriaComNotaDeProduto(PlanoAssinatura plano)
        {
            return plano.IdPlano == new ParametrosTrinks<int>(ParametrosTrinksEnum.plano_especial_esmalteria_com_nota_produto).ObterValor();
        }

        private bool EhFranquiaSpaDasSobrancelha(Estabelecimento estabelecimento)
        {
            return estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Franquia.Id == new ParametrosTrinks<int>(ParametrosTrinksEnum.franquia_spa_sobrancelhas).ObterValor();
        }

        private void DesabilitaUsoDeNotas(Estabelecimento estabelecimento)
        {
            DesabilitaUsoDeNFS(estabelecimento);
            DesabilitaUsoDeNFC(estabelecimento);
        }

        private void DesabilitaUsoDeNFS(Estabelecimento estabelecimento)
        {
            estabelecimento.EstabelecimentoConfiguracaoGeral.UtilizarNFeServico = false;
        }

        private void DesabilitaUsoDeNFC(Estabelecimento estabelecimento)
        {
            if (estabelecimento.ConfiguracaoDeNFC != null)
                estabelecimento.ConfiguracaoDeNFC.InterfaceNFC = 0;
        }

        private void HabilitaUsoDeNFS(Estabelecimento estabelecimento)
        {
            estabelecimento.EstabelecimentoConfiguracaoGeral.UtilizarNFeServico = true;
            Domain.Pessoas.PessoaJuridicaConfiguracaoNFeService.GerarConfirguracaoNFe(estabelecimento.PessoaJuridica);
        }

        private void HabilitaUsoDeNFC(Estabelecimento estabelecimento)
        {
            if (estabelecimento.ConfiguracaoDeNFC == null)
            {
                Domain.Pessoas.EstabelecimentoService.SalvaConfiguracaoNFCEstabelecimento(estabelecimento);
            }
            else if (estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Nenhuma)
            {
                estabelecimento.ConfiguracaoDeNFC.InterfaceNFC = TipoDeInterfaceNFC.NotaFiscalEletronica;
            }
            else
            {
                //estabelecimento.ConfiguracaoDeNFC.InterfaceNFC = NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.NotaFiscalEletronica;
                var envDev = ConfigurationManager.AppSettings["EnvDev"];
                estabelecimento.ConfiguracaoDeNFC.NFCProducao = envDev == null || !bool.Parse(envDev);
            }
        }

        private bool EhPlanoEspecialEsmalteriaNacional(PlanoAssinatura plano)
        {
            return plano.IdPlano == new ParametrosTrinks<int>(ParametrosTrinksEnum.plano_especial_esmalteria).ObterValor();
        }

        private bool EhCobrancaManual(Assinatura assinatura)
        {
            return assinatura.ContaFinanceira.Status == StatusContaFinanceira.CobrancaManual;
        }

        #endregion PRIVADOS

        public DateTime ObterDataInicioProximoPeriodoNaoPago(Assinatura assinatura, DateTime dataReferencia)
        {
            if (assinatura == null)
                return Calendario.Hoje();

            var fimDegustacao = assinatura.DataFinalDaDegustacaoValidoApenasParaPrimeiraAssinatura();

            var dataInicio = assinatura.EhAtivaEVigente(dataReferencia) ? assinatura.DataInicio : Calendario.Hoje();

            return ObterDataInicioProximoPeriodoNaoPago(dataReferencia, assinatura, dataInicio, fimDegustacao);
        }

        private DateTime ObterDataInicioProximoPeriodoNaoPago(DateTime dataReferencia, Assinatura assinatura, DateTime? dataInicio, DateTime fimDegustacao)
        {
            var fimDoUltimoPeriodoPago =
                Domain.Cobranca.FaturaRepository.ObterMaiorDataFimReferenciaDeFaturasPagas(assinatura.IdAssinatura) ??
                Calendario.Hoje().AddDays(-1);
            var dataAnteriorAAssinatura = (dataInicio ?? dataReferencia).AddDays(-1);
            var fimDoUltimoPeriodo = fimDoUltimoPeriodoPago > fimDegustacao ? fimDoUltimoPeriodoPago : fimDegustacao;
            fimDoUltimoPeriodo = fimDoUltimoPeriodo > dataAnteriorAAssinatura ? fimDoUltimoPeriodo : dataAnteriorAAssinatura;

            var dataInicioProximoPeriodo = fimDoUltimoPeriodo.AddDays(1);

            return dataInicioProximoPeriodo;
        }

        public DateTime? ObterDataFimDeDuracaoDoPlano(Assinatura assinaturaNova, PlanoAssinatura plano)
        {
            DateTime dataInicioDoPeriodoPago = ObterDataInicioProximoPeriodoSemFatura(assinaturaNova);

            if (plano.DuracaoDoPlanoComFidelidade == null)
                return null;
            else
                return dataInicioDoPeriodoPago.AddMonths(plano.DuracaoDoPlanoComFidelidade.Value).AddDays(-1);
        }

        public void RenovarPlanoDeAssinatura(Assinatura assinatura, DateTime dataHora)
        {
            assinatura.EmailDeRenovacaoSeAproximandoFoiEnviado = false;
            var dataCadastro = assinatura.ContaFinanceira.Pessoa.DataCadastro;
            var ehFraqueado = Domain.Pessoas.EstabelecimentoRepository.EhPessoaJuridicaFranqueadaAtiva(assinatura.ContaFinanceira.Pessoa.IdPessoa);
            var filtroMigracao = new Filtros.FiltroAgendamentoMigracaoDePlano(dataHora, assinatura.PlanoAssinatura.IdPlano, ehFraqueado, dataCadastro);
            var agendamentoDeMigracaoDoPlano = Domain.Cobranca.MigracaoDoPlanoDeAssinaturaService.ObterMigracaoAgendadaParaOPlano(filtroMigracao);

            if (agendamentoDeMigracaoDoPlano != null)
                ProsseguirRenovacaoDeAssinaturaComNovoPlanoAgendadoParaMigrar(dataHora, assinatura, agendamentoDeMigracaoDoPlano);
            else
                ProsseguirRenovacaoDeAssinaturaComMesmoPlano(assinatura, dataHora);
        }

        private void ProsseguirRenovacaoDeAssinaturaComMesmoPlano(Assinatura assinatura, DateTime dataHora)
        {
            AplicarInformacoesDoPlanoNaAssinatura(dataHora, assinatura.PlanoAssinatura, assinatura, null);
        }

        private void ProsseguirRenovacaoDeAssinaturaComNovoPlanoAgendadoParaMigrar(DateTime dataHora, Assinatura assinatura, AgendamentoDeMigracaoDoPlano agendamentoDeMigracaoDoPlano)
        {
            var idPessoaTrinksInterno = new ParametrosTrinks<int>(ParametrosTrinksEnum.id_pessoa_conta_trinks_interno).ObterValor();
            var pessoaTrinksInterno = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaTrinksInterno);
            var comentarioMotivo = $"{agendamentoDeMigracaoDoPlano.MotivoDaMigracao}. Plano anterior: {assinatura.PlanoAssinatura.Nome}";

            AplicarInformacoesDoPlanoNaAssinatura(agendamentoDeMigracaoDoPlano.DataDaMigracao, agendamentoDeMigracaoDoPlano.NovoPlano, assinatura, null);
            AtualizarAdicionaisContratadosComAsOfertasDoPlanoAssinado(assinatura, pessoaTrinksInterno, "Renovação da assinatura", comentarioMotivo);
        }

        private DateTime AjustarDataBaseadaNaAntecedencia(DateTime dataHora)
        {
            //E utilizando o parametro de boleto, pois ele é utilizado nos calculos de antecedencia em todo o sistema.
            int diasAntecedenciaBoleto = new ParametrosTrinks<int>(ParametrosTrinksEnum.qtd_dias_antecedencia_verificacao_proximo_boleto).ObterValor();
            int diasAntecedenciaRenovacao = new ParametrosTrinks<int>(ParametrosTrinksEnum.dias_antecedencia_renovacao_assinatura).ObterValor();

            return dataHora.AddDays(diasAntecedenciaBoleto + diasAntecedenciaRenovacao);
        }

        public void RenovarAssinaturasEmDataDeRenovacao(DateTime dataHora)
        {
            DateTime dataHoraAjustada = AjustarDataBaseadaNaAntecedencia(dataHora);
            var assinaturasARenovar = Domain.Cobranca.AssinaturaRepository.ObterAssinaturasARenovar(dataHoraAjustada);

            if (!SimulationTool.Current.EhSimulacao)
                foreach (var assinaturaParaRenovar in assinaturasARenovar)
                {
                    RenovarPlanoDeAssinatura(assinaturaParaRenovar, dataHoraAjustada);
                }
        }

        public void EnviarEmailDeAvisoDeRenovacaoDePlanoProxima(DateTime dataHora)
        {
            var assinaturasParaAvisar = Domain.Cobranca.AssinaturaRepository.ObterAssinaturasComRenovacaoDePlanoProximaAindaNaoAvisada(dataHora);

            if (!SimulationTool.Current.EhSimulacao)
                foreach (var assinaturaParaAvisar in assinaturasParaAvisar)
                {
                    Domain.Pessoas.EnvioEmailService.EnviarEmailDeRenovacaoDoPlanoProxima(assinaturasParaAvisar);
                }
        }

        public void AlterarDiaVencimentoDasProximasFaturas(int codigoDaAssinatura, int diaVencimento, DateTime dataReferencia)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Load(codigoDaAssinatura);

            var assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaVigenteEOuAtivaPorConta(assinatura.ContaFinanceira)
                ?? Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaPorConta(assinatura.ContaFinanceira);

            if (assinaturaVigente != null)
            {
                assinaturaVigente.DiaDeVencimento = diaVencimento;

                var fatura = Domain.Cobranca.FaturaTrinksRepository.ObterFaturaTrinksMaisAntigaPendenteDePagamento(assinaturaVigente.IdAssinatura);
                if (fatura?.InicioPeriodoReferencia != null)
                {
                    fatura.FimPeriodoReferencia = Fatura.CalculaFimReferenciaFatura(fatura.InicioPeriodoReferencia.Value, diaVencimento);
                    Domain.Cobranca.FaturaTrinksRepository.Update(fatura);
                }

                Domain.Cobranca.AssinaturaRepository.Update(assinaturaVigente);
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Conta financeira não possui assinatura vigente válida.");
            }
        }

        public void DesabilitaUsoDeNotaDeProduto(Estabelecimento estabelecimento)
        {
            DesabilitaUsoDeNFC(estabelecimento);
            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
        }

        public void HabilitaUsoDeNotaDeProduto(Estabelecimento estabelecimento)
        {
            HabilitaUsoDeNFC(estabelecimento);
            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
        }

        public void DesfazerCancelamentoDaAssinatura(Assinatura assinatura)
        {
            if (assinatura.ContaFinanceira.Status != StatusContaFinanceira.ContaCancelada)
                return;

            assinatura.DataFim = null;
            assinatura.Ativo = true;
        }

        public string ObterFaixaDeProfissionaisAssinada(int idAssinatura)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.Queryable().Where(a => a.IdAssinatura == idAssinatura).FirstOrDefault();
            var faixa = "N/A";
            if (assinatura != null && assinatura.QuantidadeDeProfissionaisEsperada != null)
            {
                if (assinatura.QuantidadeDeProfissionaisEsperada % 2 == 0)
                    faixa = "até " + assinatura.QuantidadeDeProfissionaisEsperada;
                else
                {
                    faixa = "Acima de " + assinatura.QuantidadeDeProfissionaisEsperada;
                }
            }
            return faixa;
        }

        public ValoresDaAssinaturaDoPlanoDTO ObterValoresParaContratacaoDoPlano(Assinatura assinatura, int idPlanoAssinatura)
        {
            var quantidadeProfissionaisCobranca = assinatura.ObterQuantidadeDeProfissionaisParaConsiderarNaCobranca();
            var planoSelecionado = Domain.Cobranca.PlanoAssinaturaRepository.ObterPorId(idPlanoAssinatura);
            var faixaAssinaturaAtual = Domain.Cobranca.AssinaturaService.ObterFaixaDePreco(assinatura, quantidadeProfissionaisCobranca, planoSelecionado);

            var valoresDoPlano = new ValoresDaAssinaturaDoPlanoDTO
            {
                IdPlano = planoSelecionado.IdPlano,
                TituloCiclo = planoSelecionado.TituloCiclo,
                QuantidadeDeParcelas = planoSelecionado.QuantidadeDeParcelas,
                ValorPorMes = faixaAssinaturaAtual?.ValorPorMes() ?? 0,
                DescricaoDaFaixa = faixaAssinaturaAtual?.ObterDescricaoDaFaixa() ?? "",
                ValoresDosAdicionais = ListarValoresDosAdicionaisConsiderandoContratacoesNaAssinatura(assinatura, idPlanoAssinatura, quantidadeProfissionaisCobranca),
                Beneficios = Domain.Cobranca.BeneficioDoPlanoNoMeuPlanoService.ListarBeneficiosDoPlanoNoMeuPlano(assinatura, planoSelecionado).Select(f => new BeneficioDoPlanoDTO(f)).ToList()
            };

            var promocaoContaFinanceira = assinatura.PromocaoNaProximaFatura();
            if (promocaoContaFinanceira != null)
            {
                valoresDoPlano.PercentualDesconto = promocaoContaFinanceira.Desconto;
                valoresDoPlano.QuantidadeDeDescontosProgramados = promocaoContaFinanceira.QuantasMensalidadesODescontoSerahAplicado;
                valoresDoPlano.QuantidadeDeDescontosDisponiveis = assinatura.QuantasFaturasAindaTeraoDescontoDePromocao();
                valoresDoPlano.QuantidadeDeDescontosUsados = valoresDoPlano.QuantidadeDeDescontosProgramados - valoresDoPlano.QuantidadeDeDescontosDisponiveis;
            }

            return valoresDoPlano;
        }

        public List<ValorDoAdicionalNoPlanoDTO> ListarValoresDosAdicionaisConsiderandoContratacoesNaAssinatura(Assinatura assinatura, int idPlanoAssinatura, int quantidadeProfissionaisCobranca)
        {
            var ofertasPadraoDosAdicionais = Domain.Cobranca.OfertaDeServicoAdicionalService.ListarValoresDeAdicionaisPorPlanoEQuantidadeDeProfissionaisNaCobranca(idPlanoAssinatura, quantidadeProfissionaisCobranca);
            var valoresPorFormaContratacao = Domain.Cobranca.OfertaDeServicoAdicionalService.ListarValoresDeFormaContratacaoAdicionaisPorPlanoEQuantidadeDeProfissionaisNaCobranca(idPlanoAssinatura, quantidadeProfissionaisCobranca);
            var adicionaisContratados = Domain.Cobranca.AdicionalNaAssinaturaService.ObterAdicionaisAtivosNaAssinatura(assinatura);
            var descontosNosAdicionais = Domain.Cobranca.AdicionalNaAssinaturaService.ListarDescontosVigentesDosAdicionaisAtivosDaAssinatura(assinatura.IdAssinatura);

            var dadosDosAdicionais = new List<ValorDoAdicionalNoPlanoDTO>();

            foreach (var ofertaPadraoDoAdicional in ofertasPadraoDosAdicionais)
            {
                if (ofertaPadraoDoAdicional == null)
                    continue;

                var adicionalContratado = adicionaisContratados.FirstOrDefault(a => a.Servico == ofertaPadraoDoAdicional.Servico);

                var ofertaDeCobrancaDoAdicional = adicionalContratado != null
                    ? Domain.Cobranca.OfertaDeServicoAdicionalService.ObterValorDeCobrancaDoAdicionalNaOferta(adicionalContratado.OfertaEscolhida, quantidadeProfissionaisCobranca, adicionalContratado.FormaDeContratacao)
                    : ofertaPadraoDoAdicional;

                var formaDeContratacao = adicionalContratado != null
                    ? adicionalContratado.FormaDeContratacao
                    : ofertaDeCobrancaDoAdicional.ObterFormaDeContratacaoCobrada();

                var adesaoDoAdicional = Domain.Cobranca.ValorDeAdesaoDoAdicionalPorFaixaService.ObterAdesaoDoAdicional(ofertaDeCobrancaDoAdicional.Servico, quantidadeProfissionaisCobranca);
                if (ofertaDeCobrancaDoAdicional.Servico.IdServico == 9)
                    _ = Domain.Cobranca.ValorDeAdesaoDoAdicionalPorFaixaService.ObterAdesaoDoAdicional(ofertaDeCobrancaDoAdicional.Servico, quantidadeProfissionaisCobranca);

                var dadosAdicional = new ValorDoAdicionalNoPlanoDTO
                {
                    IdServico = ofertaDeCobrancaDoAdicional.Servico.IdServico,
                    Nome = ofertaDeCobrancaDoAdicional.Servico.Nome,
                    ValorMensalSemDesconto = ofertaDeCobrancaDoAdicional.Valor,
                    IdFormaDeContratacao = formaDeContratacao?.Id,
                    ValorDeTaxaExtra = formaDeContratacao?.ValorDeTaxaExtra,
                    TituloDaTaxaExtra = formaDeContratacao?.TituloDaTaxaExtra,
                    ResumoDaTaxaExtra = formaDeContratacao?.ResumoDaTaxaExtra,
                    ValorDeAdesao = adesaoDoAdicional?.Valor,
                    TermoDaAdesao = adesaoDoAdicional?.Termo,
                    FormasContratacao = valoresPorFormaContratacao != null ?
                    valoresPorFormaContratacao.Where(fc => fc.IdServico == ofertaDeCobrancaDoAdicional.Servico.IdServico).ToList() :
                    null
                };

                if (adicionalContratado != null)
                {
                    var descontoNoAdicional = descontosNosAdicionais.FirstOrDefault(d => d.AdicionalNaAssinatura.Servico.IdServico == ofertaDeCobrancaDoAdicional.Servico.IdServico);

                    if (descontoNoAdicional != null)
                    {
                        dadosAdicional.PercentualDesconto = descontoNoAdicional.PercentualDoDesconto;
                        dadosAdicional.QuantidadeDeDescontosProgramados = descontoNoAdicional.QuantidadeDeOcorrenciasProgramadas;
                        dadosAdicional.QuantidadeDeDescontosUsados = descontoNoAdicional.ObterQuantidadeDeFaturasQueUtilizaramEsteDesconto();
                        dadosAdicional.QuantidadeDeDescontosDisponiveis = dadosAdicional.QuantidadeDeDescontosProgramados - dadosAdicional.QuantidadeDeDescontosUsados;
                    }
                }

                dadosDosAdicionais.Add(dadosAdicional);
            }

            return dadosDosAdicionais;
        }

        private void AssociarPromocaoNaAssinaturaSeHouver(Assinatura assinatura, FonteDePagamento fonteDePagamento, Estabelecimento estabelecimento)
        {
            var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoAOferecerNaAssinatura(assinatura.ContaFinanceira);
            var temCupomDeParceria = promocao != null && Domain.Cobranca.EstabelecimentoParceriasTrinksService.EstabelecimentoTemParceiraAtiva(estabelecimento.IdEstabelecimento);
            if (PromocaoPodeSerAssociadaAoPlanoEFormaDePagamento(assinatura, fonteDePagamento, promocao, temCupomDeParceria))
                Domain.Cobranca.PromocaoTrinksService.ParticiparDePromocao(promocao);
        }

        private bool PromocaoPodeSerAssociadaAoPlanoEFormaDePagamento(Assinatura assinatura, FonteDePagamento fonteDePagamento, PromocaoPraContaFinanceira promocao, bool temCupomDeParceria)
        {
            return Domain.Cobranca.EscolhaDePlanoParaAssinaturaService.PromocaoPodeSerAssociadaAoPlanoEFormaDePagamento(promocao, assinatura.PlanoAssinatura, fonteDePagamento.FormaPagamento.IdFormaPagamento, temCupomDeParceria);
        }

        public string TrocarFaixaDeProfissionais(int idFaixa, int idEstabelecimento)
        {
            var idPessoaEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterIdPessoaDoEstabelecimento(idEstabelecimento);
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaDoEstabelecimento(idPessoaEstabelecimento);

            if (assinatura != null)
            {
                var FaixaSelecionada = Domain.Cobranca.ValorPorFaixaRepository.Load(idFaixa);
                assinatura.QuantidadeDeProfissionaisEsperada = FaixaSelecionada.LimiteSuperior != null ? FaixaSelecionada.LimiteSuperior : FaixaSelecionada.LimiteInferior;

                Domain.Cobranca.AssinaturaRepository.Update(assinatura);
            }

            var quantidadeProfissionaisCobranca = assinatura.ObterQuantidadeDeProfissionaisParaConsiderarNaCobranca();
            var faixaAssinaturaAtual = ObterFaixaDePreco(assinatura, quantidadeProfissionaisCobranca, assinatura?.PlanoAssinatura);

            return faixaAssinaturaAtual.ObterDescricaoDaFaixa();
        }

        public void ValidarEAtualizarQuantidadeProfissionaisEsperadoNaAssinatura(Estabelecimento estabelecimento)
        {
            var assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);

            if (assinaturaVigente != null)
            {
                var assinaturaVigenteFaixa = Domain.Cobranca.ValorPorFaixaRepository.ObterVinculadosAoPlano(assinaturaVigente.PlanoAssinatura.IdPlano);

                int profissionaisAtivosComAgenda = estabelecimento.ObterQuantidadeDeProfissionaisAtivosComServicosAtivosComAgenda();

                int quantidadeEsperada = assinaturaVigente.QuantidadeDeProfissionaisEsperada ?? 1;
                int quantidadeFinal = Math.Max(profissionaisAtivosComAgenda, quantidadeEsperada);

                AtualizarQuantidadeProfissionaisEsperadoNaAssinatura(assinaturaVigente, assinaturaVigenteFaixa, quantidadeFinal);
            }
        }

        private void AtualizarQuantidadeProfissionaisEsperadoNaAssinatura(Assinatura assinaturaVigente, IList<ValorPorFaixa> assinaturaVigenteFaixa, int quantidadeProfissionais)
        {
            var faixa = assinaturaVigenteFaixa.FirstOrDefault(f =>
                f.LimiteInferior <= quantidadeProfissionais &&
                (f.LimiteSuperior == null || f.LimiteSuperior >= quantidadeProfissionais));

            if (faixa != null)
            {
                var novolimiteEsperado = faixa.LimiteSuperior ?? faixa.LimiteInferior;

                if (assinaturaVigente.QuantidadeDeProfissionaisEsperada != novolimiteEsperado)
                {
                    assinaturaVigente.QuantidadeDeProfissionaisEsperada = novolimiteEsperado;
                    Domain.Cobranca.AssinaturaRepository.Update(assinaturaVigente);
                }
            }
        }
    }
}