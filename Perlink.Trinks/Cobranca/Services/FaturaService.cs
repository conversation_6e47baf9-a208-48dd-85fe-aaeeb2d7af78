﻿using Elmah;
using Newtonsoft.Json;
using Perlink.DomainInfrastructure;
using Perlink.DomainInfrastructure.Facilities;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.GatewayPagamento;
using Perlink.GatewayPagamento.Enum;
using Perlink.GatewayPagamento.Services;
using Perlink.Shared.Notificacao;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.Cobranca.Factories;
using Perlink.Trinks.Cobranca.Helpers;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.DTO;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Marketing;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Services;
using Perlink.Trinks.WhatsApp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Perlink.Trinks.Cobranca.Services
{

    public class FaturaService : BaseService, IFaturaService
    {

        #region IFaturaService Members

        [TransactionInitRequired]
        public Fatura GerarNovaFatura(Assinatura assinatura, DateTime dataHora)
        {
            if (!assinatura.DiaDeVencimento.HasValue)
                Domain.Cobranca.AssinaturaService.DefinirDiaDeVencimento(assinatura, dataHora);

            var faturaFactory = new FaturaTrinksFactory();
            var fatura = faturaFactory.CreateParaGerarFatura(assinatura);

            if (!SimulationTool.Current.EhSimulacao)
                Domain.Cobranca.FaturaTrinksRepository.SaveNew(fatura);

            var primeiroPagamento = !Domain.Cobranca.FaturaRepository.ExisteAlgumaFaturaPaga(assinatura.IdAssinatura);

            if (!SimulationTool.Current.EhSimulacao && primeiroPagamento)
            {
                if (fatura.FormaPagamento == FormaDePagamentoEnum.Boleto || fatura.FormaPagamento == FormaDePagamentoEnum.Pix)
                {
                    Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoAguardandoConfirmacao(
                        assinatura.ContaFinanceira.Pessoa.PessoaJuridica, fatura);
                }
            }
            string formaPagamento = fatura.FormaPagamento != null ? fatura.FormaPagamento.Nome : "Boleto Bancario";

            fatura.DataDeTolerancia = CalcularDataDeToleranciaDaFatura(fatura);

            LogService<FaturaService>.Info("Gerou fatura #" + fatura.IdFatura + " da assinatura #" +
                assinatura.IdAssinatura + " - Forma pagamento: " + formaPagamento);

            if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento))
                Domain.Pessoas.EnvioEmailService.EnviarEmailFaturaGerada(fatura);

            return fatura;
        }

        public DateTime? CalcularDataDeToleranciaDaFatura(Fatura fatura)
        {
            if (fatura.DataVencimento == null)
                return null;

            var quantidadeDeDiasPadraoDeToleranciaParaNaoPagamento =
                new ParametrosTrinks<Int32>(ParametrosTrinksEnum.qtd_dias_tolerancia_padrao_nao_pagamento)
                    .ObterValor();

            var retorno = fatura.DataVencimento.Value;
            retorno = Domain.Pessoas.DataEspecialService.AdicionarDiasUteis(retorno, quantidadeDeDiasPadraoDeToleranciaParaNaoPagamento);
            retorno = Domain.Pessoas.DataEspecialService.AdicionarDiasUteis(retorno, fatura.DiasToleranciaNaoPagamento);
            retorno = Domain.Pessoas.DataEspecialService.AcertarParaOProximoDiaSerUmDiaUtil(retorno);
            return retorno;
        }

        public decimal? CalcularValorFatura(FaturaTrinks fatura)
        {
            if (fatura.Assinatura == null)
                return fatura.ValorPorFaixa.ValorPorMes();

            var valor = fatura.ValorPorFaixa.ValorPorMes();

            if (fatura.Assinatura.DeveTerFaturaCobrindoTodoPeriodoDeDuracaoDaAssinatura())
            {
                valor = fatura.ValorPorFaixa.Valor;
            }

            bool proximaFaturaTerahDescontoDePromocao = ProximaFaturaTerahDescontoDePromocao(fatura.Assinatura.IdAssinatura);

            if (valor.HasValue && valor.Value > 0)
            {
                if (proximaFaturaTerahDescontoDePromocao)
                {
                    var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraAtivaEAssinadaPelaAssinatura(fatura.Assinatura.IdAssinatura);
                    //var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraPelaAssinatura(fatura.Assinatura.IdAssinatura);

                    valor = AplicarDescontoDaPromocao(valor, promocao);

                    fatura.PromocaoPraContaFinanceira = promocao;
                    fatura.ValorCobrado = valor;
                }
                else
                {
                    fatura.PromocaoPraContaFinanceira = null;
                }
            }
            else
            {
                fatura.PromocaoPraContaFinanceira = null;
            }

            if (fatura.AdicionaisCobrados.Any())
                valor += fatura.ObterValoresSomadosDosAdicionaisCobrados();

            return valor;
        }

        public decimal? AplicarDescontoDaPromocao(decimal? valor, PromocaoPraContaFinanceira promocao)
        {
            if (valor == null || valor.Value == 0)
                return valor;
            valor = valor * (1 - (promocao.Desconto / 100));
            return valor;
        }

        public bool ProximaFaturaTerahDescontoDePromocao(int idAssinatura)
        {
            bool proximaFaturaTerahPromocao = false;
            var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraAtivaEAssinadaPelaAssinatura(idAssinatura);
            //var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraPelaAssinatura(idAssinatura);

            if (promocao == null || !promocao.JahAdquirido || promocao.Desconto == 0)
                return false;

            var qtdFaturasUsadas = Domain.Cobranca.FaturaTrinksRepository.ObterQuantidadeFaturasParaPromocao(idAssinatura, promocao.Id);

            proximaFaturaTerahPromocao = qtdFaturasUsadas < promocao.QuantasMensalidadesODescontoSerahAplicado;
            return proximaFaturaTerahPromocao;
        }

        [TransactionInitRequired]
        public FaturaMarketing GerarNovaFaturaParaCreditosDeMarketing(Estabelecimento estabelecimento, FonteDePagamento fonteDePagamento, DateTime dataHora, List<MarketingCompraCredito> comprasCredito, decimal valorTotalASerCobrado)
        {
            var fatura = new FaturaMarketing();
            AplicarListaDeComprasNaFatura(comprasCredito, fatura);
            fatura.DataEmissao = dataHora;
            fatura.InicioPeriodoReferencia = dataHora;
            fatura.FimPeriodoReferencia = dataHora;
            fatura.Status = StatusFaturaEnum.Aberta;
            fatura.ValorCobrado = valorTotalASerCobrado;
            fatura.Ativo = true;
            fatura.FormaPagamento = fonteDePagamento.FormaPagamento;
            fatura.Estabelecimento = estabelecimento;
            fatura.DataVencimento = dataHora;
            if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento))
            {
                fatura.DataVencimento = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametro(dataHora);
            }
            else
            {
                fatura.DataVencimento = Calendario.Hoje();
            }

            if (fatura.FormaPagamento != null && fatura.FormaPagamento.PossuiProcessamentoAutomatico())
                fatura.DataParaProcessar = fatura.DataVencimento;

            if (!SimulationTool.Current.EhSimulacao)
                Domain.Cobranca.FaturaMarketingRepository.SaveNew(fatura);

            //TODO: verificar: vai ter email?
            //var primeiroPagamento = !Domain.Cobranca.FaturaRepository.ExisteAlgumaFaturaPaga(assinatura.IdAssinatura);
            //if (fatura.FormaPagamento == FormaDePagamentoEnum.Boleto && primeiroPagamento)
            //{
            //    Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoPorBoletoAguardandoConfirmacao(
            //        assinatura.ContaFinanceira.Pessoa.PessoaJuridica, fatura);
            //}

            LogService<FaturaService>.Info("Gerou fatura #" + fatura.IdFatura + " da compra de créditos de marketing " +
                                           " do Estabelecimento #" + estabelecimento.IdEstabelecimento +
                                           " - Forma pagamento: " + fatura.FormaPagamento.Nome);
            return fatura;
        }

        private static void AplicarListaDeComprasNaFatura(List<MarketingCompraCredito> comprasCredito, FaturaMarketing fatura)
        {
            foreach (var compra in comprasCredito)
            {
                compra.FaturaMarketing = fatura;
            }
            fatura.ListaMarketingCompraCredito = comprasCredito;
        }

        public void ProcessarPagamentoAvulso(Fatura fatura, FonteDePagamento fonteDePagamento, String ipComprador,
            DateTime dataHora, bool utilizandoNovoCartao = false, bool alterandoFormaPagamento = false)
        {
            if ((fatura as FaturaTrinks) != null)
            {
                ProcessarPagamentoAvulsoFaturaTrinks((fatura as FaturaTrinks), fonteDePagamento, ipComprador, dataHora, utilizandoNovoCartao, alterandoFormaPagamento);
                if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento))
                    Domain.WebContext.DadosFixos("PermissoesAtualizadas", null);
            }
            else if ((fatura as FaturaMarketing) != null)
            {
                ProcessarPagamentoAvulsoFaturaMarketing((fatura as FaturaMarketing), fonteDePagamento, ipComprador, dataHora, utilizandoNovoCartao);
            }
            else if ((fatura as FaturaWhatsApp) != null)
            {
                ProcessarPagamentoAvulsoFaturaWhatsApp((fatura as FaturaWhatsApp), fonteDePagamento, ipComprador, dataHora, utilizandoNovoCartao);
            }
            else
                throw new NotImplementedException("ProcessarPagamentoAvulso não está implementado para este tipo de fatura.");
        }

        [TransactionInitRequired]
        private void ProcessarPagamentoAvulsoFaturaMarketing(FaturaMarketing fatura, FonteDePagamento fonteDePagamento, string ipComprador, DateTime dataHora, bool utilizandoNovoCartao)
        {
            if (!SimulationTool.Current.EhSimulacao)
            {
                ResultadoTransacao resultado = null;
                if (Domain.Cobranca.PagamentoDeFaturaService.FaturaRealizaTransacao(fatura))
                {
                    resultado = RealizarTransacao(fonteDePagamento, ipComprador, fatura);
                    AtualizarStatusFatura(fatura, fonteDePagamento, resultado, dataHora, utilizandoNovoCartao, false, false);
                }

                //TODO: verificar: Vai ter email de pagamento aprovado?
                //if (resultado != null && resultado.TransacaoAprovada)
                //Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoDaFaturaAprovado(
                //    contaFinanceira.Pessoa.PessoaJuridica, fatura);
            }
        }

        [TransactionInitRequired]
        private void ProcessarPagamentoAvulsoFaturaWhatsApp(FaturaWhatsApp fatura, FonteDePagamento fonteDePagamento, string ipComprador, DateTime dataHora, bool utilizandoNovoCartao)
        {
            if (!SimulationTool.Current.EhSimulacao)
            {
                ResultadoTransacao resultado = null;
                if (Domain.Cobranca.PagamentoDeFaturaService.FaturaRealizaTransacao(fatura))
                {
                    resultado = RealizarTransacao(fonteDePagamento, ipComprador, fatura);
                    AtualizarStatusFatura(fatura, fonteDePagamento, resultado, dataHora, utilizandoNovoCartao, false, false);
                }
            }
        }

        [TransactionInitRequired]
        public void ProcessarPagamentoAvulsoFaturaTrinks(FaturaTrinks fatura, FonteDePagamento fonteDePagamento, String ipComprador,
            DateTime dataHora, bool utilizandoNovoCartao = false, bool alterandoFormaPagamento = false)
        {
            if (!SimulationTool.Current.EhSimulacao)
            {
                var primeiroPagamento =
                    !Domain.Cobranca.FaturaRepository.ExisteAlgumaFaturaPaga(fatura.Assinatura.IdAssinatura);
                var contaFinanceira = fatura.Assinatura.ContaFinanceira;

                ResultadoTransacao resultado = null;
                if (Domain.Cobranca.PagamentoDeFaturaService.FaturaRealizaTransacao(fatura))
                {
                    resultado = RealizarTransacao(fonteDePagamento, ipComprador, fatura);
                    AtualizarStatusContaEFatura(fatura, fonteDePagamento, resultado, dataHora, utilizandoNovoCartao,
                        primeiroPagamento);
                }
                else
                    AtualizarStatusConta(fatura, dataHora);

                if (resultado != null && resultado.TransacaoAprovada && !alterandoFormaPagamento)
                    Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoDaFaturaAprovado(
                        contaFinanceira.Pessoa.PessoaJuridica, fatura);
            }
        }

        [TransactionInitRequired]
        public Fatura ProcessarPagamentoRecorrente(Fatura fatura, String ipComprador, DateTime dataHora)
        {
            var faturaAnterior =
                Domain.Cobranca.FaturaTrinksRepository.ObterUltimaFaturaTrinksPagaComCartao(fatura.Assinatura.IdAssinatura);
            var enviaEmail = !fatura.EstaPaga;

            if (faturaAnterior == null)
            {
                DomainInfrastructure.Validation.ValidationHelper.Instance.AdicionarItemValidacao("Somente faturas com fatura anterior podem ser pagas por recorrência.");
                return fatura;
            }

            return RealizarPagamentoRecorrente(fatura, faturaAnterior, ipComprador, dataHora, enviaEmail);
        }

        [TransactionInitRequired]
        public ResultadoReprocessamento ReProcessarPagamentoRecorrenteNegadoPorData(DateTime dataHora, bool ehReprocessamentoManual = false)
        {
            var faturas = Domain.Cobranca.FaturaTrinksRepository.ListarFaturasTrinksComPagamentNegadoNoCartaoPorData(dataHora).ToList();

            LogService<ContaFinanceiraService>.Info("FaturasAProcessar: " + faturas.Count());
            if (SimulationTool.Current.EhSimulacao || faturas == null)
                return null;

            int totalFaturasProcessadas = 0;
            int totalFaturasNaoProcessadas = 0;
            List<RelatorioFaturamento> relatoriaFaturamento = new List<RelatorioFaturamento>();
            foreach (var fatura in faturas)
            {
                LogService<ContaFinanceiraService>.Info("Processando fatura #" + fatura.IdFatura + " vencimento " + fatura.DataVencimento);
                var assinatura = fatura.Assinatura;
                var contaFinanceira = assinatura.ContaFinanceira;

                if (contaFinanceira.Status == StatusContaFinanceira.ContaCancelada)
                    continue; //Verificação feita neste ponto para não realizar uma pesquisa desnecessária no banco caso o estabelecimento atenda aos status

                var faturaAnterior =
                     Domain.Cobranca.FaturaTrinksRepository.ObterUltimaFaturaTrinksPagaComCartao(assinatura.IdAssinatura);

                if (faturaAnterior == null)
                    continue; //"Somente faturas com fatura anterior podem ser pagas por recorrência."

                var novaFatura = RealizarPagamentoRecorrente(fatura, faturaAnterior, null, Calendario.Agora(), enviaEmail: true, ehReprocessamentoManual);
                if (novaFatura.Status == StatusFaturaEnum.Paga)
                {
                    totalFaturasProcessadas += 1;
                }
                else
                {
                    totalFaturasNaoProcessadas += 1;
                }
                relatoriaFaturamento.Add(Domain.Cobranca.RelatorioFaturamentoRepository.Queryable().Where(rf => fatura.IdFatura == rf.FaturaCodigo).FirstOrDefault());
            }

            var resultado = new ResultadoReprocessamento
            {
                TotalFaturasProcessadas = totalFaturasProcessadas,
                TotalFaturasNaoProcessadas = totalFaturasNaoProcessadas,
                RelatorioFaturamento = relatoriaFaturamento
            };

            return resultado;
        }

        public BoletoDTO ObterDadosParaBoleto(Fatura fatura)
        {
            if (!fatura.DataVencimento.HasValue)
                throw new Exception(
                    "Foi solicitado uma geração de boleto com uma fatura sem data de vencimento definida.");

            if (!fatura.ValorCobrado.HasValue)
                throw new Exception(
                    "Foi solicitado uma geração de boleto com uma fatura sem valor a ser cobrado definido.");

            var boletoDTO = new BoletoDTO()
            {
                DataVencimento = fatura.DataVencimento.Value,
                ValorBoleto = fatura.ValorCobrado.Value,
                NomeSacado = fatura.Estabelecimento.PessoaJuridica.RazaoSocial,
                NossoNumero = fatura.IdFatura.ToString(),
                NumeroDocumento = fatura.IdFatura.ToString(),
                CnpjOuCpf = !String.IsNullOrEmpty(fatura.Estabelecimento.PessoaJuridica.CNPJ)
                    ? fatura.Estabelecimento.PessoaJuridica.CNPJ
                    : (fatura.Estabelecimento.ObterResponsavel() != null
                          ? fatura.Estabelecimento.ObterResponsavel().Cpf : "")
            };

            var dataParaPagamento = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametro(boletoDTO.DataVencimento.Date);
            if (dataParaPagamento < Calendario.Hoje())
            {
                var porcentagemDeMulta =
                    new ParametrosTrinks<decimal>(ParametrosTrinksEnum.boleto_multa_pagamento_vencido).ObterValor();

                if (fatura is FaturaTrinks)
                    boletoDTO.Multa = boletoDTO.ValorBoleto * (porcentagemDeMulta / 100);

                boletoDTO.ValorBoleto = boletoDTO.ValorBoleto + boletoDTO.Multa;
                boletoDTO.DataVencimento = Calendario.Hoje();
            }

            return boletoDTO;
        }

        [TransactionInitRequired]
        public void PagarFaturaManualmente(Fatura fatura, DateTime dataPagamentoIndicado,
            decimal? valorTotalPagamentoIndicado, string observacao)
        {
            fatura.StatusTemporarioTransacaoGateway = "Aprovada";
            fatura.Observacoes = observacao;

            if (FormaPagamentoHelper.EhCartaoDeCredito(fatura.FormaPagamento))
                fatura.FormaPagamento = FormaDePagamentoEnum.Boleto;

            var primeiroPagamento = false;
            if (fatura.Assinatura != null)
            {
                primeiroPagamento =
                        !Domain.Cobranca.FaturaRepository.ExisteAlgumaFaturaPaga(fatura.Assinatura.IdAssinatura);
            }

            AtualizarStatusFatura(fatura, StatusFaturaEnum.Paga, dataPagamentoIndicado, valorTotalPagamentoIndicado, false, primeiroPagamento, false);

            if (fatura.Assinatura != null)
                AtualizarStatusConta(fatura, Calendario.Agora());

            if (fatura is FaturaTrinks)
            {
                Domain.Cobranca.ParceriaTrinksService.NotificarPromotorDoTrinksSobreIndicadoQueTevePrimeiraAssinaturaPaga(fatura as FaturaTrinks);
            }
        }

        public void ColocarFaturaComoVencida(Fatura fatura, DateTime dataHora)
        {
            if (SimulationTool.Current.EhSimulacao)
                return;

            fatura.Status = StatusFaturaEnum.Vencida;
            fatura.DataUltimoProcessamento = dataHora;
            fatura.DataParaProcessar = null;
            Domain.Cobranca.FaturaRepository.Update(fatura);

            if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento))
            {
                var contaFinanceira = fatura.Assinatura.ContaFinanceira;

                Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoFaturaNaoRecebido(
                    contaFinanceira.Pessoa.PessoaJuridica, fatura);
            }
        }

        [TransactionInitRequired]
        public Fatura RealizarPagamentoRecorrente(Fatura faturaNova, Fatura faturaAnterior, String ipComprador,
            DateTime dataHora, Boolean enviaEmail, bool ehReprocessamentoManual = false)
        {
            var assinaturaAtiva = faturaNova.Assinatura;

            if (!SimulationTool.Current.EhSimulacao)
            {
                var responsavelFinanceiro = faturaNova.Estabelecimento.ObterResponsavel();
                var endereco = faturaNova.Estabelecimento.PessoaJuridica.EnderecoProprio;
                var cpf = string.IsNullOrEmpty(faturaAnterior.CpfPortadorCartaoCredito) 
                    ? responsavelFinanceiro.Cpf
                    : faturaAnterior.CpfPortadorCartaoCredito;
                
                var recorrenciaTransacao = new DadosRecorrenciaTransacao
                {
                    NumeroDocumento = faturaNova.IdFatura.ToString(),
                    TransacaoAnterior = faturaAnterior.IdTransacaoGateway.ToString(),
                    IPComprador = ipComprador,
                    TokenDeRecorrencia = faturaAnterior.TokenDeRecorrencia,
                    NomeComprador = responsavelFinanceiro.NomeCompleto,
                    Email = responsavelFinanceiro.Email,
                    Telefone = responsavelFinanceiro.ObterTelefoneDePessoaFisicaParaCadastro(),
                    DocumentoComprador = cpf,
                    Endereco = new EnderecoDeCobranca
                    {
                        Logradouro = endereco.Logradouro,
                        Numero = endereco.Numero,
                        Complemento = endereco.Complemento,
                        Bairro = endereco.Bairro,
                        CEP = endereco.Cep,
                        Cidade = endereco.Cidade,
                        Estado = endereco.UF.ToString(),
                    },
                };
                if (faturaNova.ValorCobrado.HasValue)
                    recorrenciaTransacao.Valor = faturaNova.ValorCobrado.Value;

                if (faturaNova.EstaPaga && !ConfiguracoesTrinks.Cobranca.HabilitarReprocessamentoFaturasPagas)
                    throw new Exception("A fatura a ser processada já está paga");

                var session = NHibernateFacility.GetSession<Fatura>();

                using (var tx = session.BeginTransaction())
                {
                    try
                    {
                        var pagarmeV5Disponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                            .ObterDisponibilidadeDeRecurso(faturaAnterior.Estabelecimento, Recurso.PagamentoFaturaTrinksPagarmeV5)
                            .EstaDisponivel;
                        
                        if(faturaAnterior.Gateway == null)
                            throw new Exception("A fatura anterior não foi paga através de nenhum gateway de pagamento.");
                        
                        var servicoGateway = GatewayFaturaFactory.CriarGatewayParaPagamentosRecorrentes(pagarmeV5Disponivel, faturaAnterior.Gateway.Value);

                        if (SimulationTool.Current.EhSimulacao || SimulationTool.Current.EhSimulacaoSomentePagamento)
                        {
                            servicoGateway = GatewayFaturaFactory.CriarGatewayEmHomologacao(faturaAnterior.Gateway.Value);
                        }

                        if (faturaNova.ValorCobrado > 0)
                            ProcessarFaturaRecorrenteComValor(faturaNova, faturaAnterior, dataHora, enviaEmail, ehReprocessamentoManual, assinaturaAtiva, recorrenciaTransacao, servicoGateway);
                        else
                            ProcessarFaturaRecorrenteSemValor(faturaNova, faturaAnterior, dataHora, ehReprocessamentoManual);
                        tx.Commit();
                    }
                    catch (Exception)
                    {
                        tx.Rollback();
                        throw;
                    }
                }
                Domain.Cobranca.FaturaRepository.Flush();

                LogService<FaturaService>.Info("Status final da fatura #" + faturaNova.IdFatura + " e o resultado foi " +
                                               (StatusFaturaEnum)faturaNova.Status);
            }
            return faturaNova;
        }

        //novo
        [TransactionInitRequired]
        public Fatura RealizarPagamentoCartaoSalvo(string idCardPagamento, string idTransacaoUltimaFatura, Fatura faturaNova,
            FonteDePagamento fonteDePagamento, string ipComprador, DateTime dataHora)
        {
            if (!SimulationTool.Current.EhSimulacao)
            {
                var responsavelFinanceiro = faturaNova.Estabelecimento.ObterResponsavel();
                var endereco = faturaNova.Estabelecimento.PessoaJuridica.EnderecoProprio;
                var pagamentoCartao = fonteDePagamento as FonteDePagamentoCartao;
                var cpf = string.IsNullOrEmpty(pagamentoCartao?.DadosCartao.PortadorCPF) 
                    ? responsavelFinanceiro.Cpf
                    : pagamentoCartao.DadosCartao.PortadorCPF;
                
                var recorrenciaTransacao = new DadosRecorrenciaTransacao
                {
                    NumeroDocumento = faturaNova.IdFatura.ToString(),
                    TransacaoAnterior = idTransacaoUltimaFatura,
                    IPComprador = ipComprador,
                    TokenDeRecorrencia = idCardPagamento,
                    NomeComprador = responsavelFinanceiro.NomeCompleto,
                    Telefone = responsavelFinanceiro.ObterTelefoneDePessoaFisicaParaCadastro(),
                    Email = responsavelFinanceiro.Email,
                    DocumentoComprador = cpf,
                    Endereco = new EnderecoDeCobranca
                    {
                        Logradouro = endereco.Logradouro,
                        Numero = endereco.Numero,
                        Complemento = endereco.Complemento,
                        Bairro = endereco.Bairro,
                        CEP = endereco.Cep,
                        Cidade = endereco.Cidade,
                        Estado = endereco.UF.ToString(),
                    },
                };

                if (faturaNova.ValorCobrado.HasValue)
                    recorrenciaTransacao.Valor = faturaNova.ValorCobrado.Value;

                if (faturaNova.EstaPaga && !ConfiguracoesTrinks.Cobranca.HabilitarReprocessamentoFaturasPagas)
                    throw new Exception("A fatura a ser processada já está paga");

                var session = NHibernateFacility.GetSession<Fatura>();

                using (var tx = session.BeginTransaction())
                {
                    try
                    {
                        var pagarmeV5Disponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                            .ObterDisponibilidadeDeRecurso(faturaNova.Estabelecimento, Recurso.PagamentoFaturaTrinksPagarmeV5)
                            .EstaDisponivel;
                        
                        var servicoGateway = GatewayFaturaFactory.CriarGatewayParaNovasAssinaturas(pagarmeV5Disponivel);

                        if (faturaNova.ValorCobrado > 0)
                        {
                            var resultado = servicoGateway.SolicitarRecorrenciaDeTransacao(recorrenciaTransacao);

                            LogService<FaturaService>.Info("Solicitou recorrência de Pagamento da fatura #" +
                                           idTransacaoUltimaFatura +
                                           " para pagar a fatura #" + faturaNova.IdFatura + " e o resultado foi " +
                                           resultado.ResultadoSolicitacaoAprovacao);

                            if (faturaNova.IdFatura > 0)
                                Domain.Cobranca.FaturaRepository.Update(faturaNova);
                            else
                                Domain.Cobranca.FaturaRepository.SaveNew(faturaNova);

                            LogService<FaturaService>.Info("Solicitou pagamento da fatura #" + faturaNova.IdFatura +
                                                   " e o resultado foi " + resultado.ResultadoSolicitacaoAprovacao);

                            AtualizarStatusFatura(faturaNova, fonteDePagamento, resultado, dataHora, false, false, false);

                            //if (!resultado.TransacaoAprovada && resultado.Exception == null)
                            //    Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoDaFaturaNaoAutorizado(
                            //        assinaturaAtiva.ContaFinanceira.Pessoa.PessoaJuridica, faturaNova);
                        }

                        tx.Commit();
                    }
                    catch (Exception)
                    {
                        tx.Rollback();
                        throw;
                    }
                }
                Domain.Cobranca.FaturaRepository.Flush();

                LogService<FaturaService>.Info("Status final da fatura #" + faturaNova.IdFatura + " e o resultado foi " +
                                               (StatusFaturaEnum)faturaNova.Status);
            }
            return faturaNova;
        }

        private void ProcessarFaturaRecorrenteComValor(Fatura faturaNova, Fatura faturaAnterior, DateTime dataHora, bool enviaEmail, bool ehReprocessamentoManual, Assinatura assinaturaAtiva, DadosRecorrenciaTransacao recorrenciaTransacao, ProcessamentoService servicoGateway)
        {
            var resultado = servicoGateway.SolicitarRecorrenciaDeTransacao(recorrenciaTransacao);
            LogService<FaturaService>.Info("Solicitou recorrência de Pagamento da fatura #" +
                                           faturaAnterior.IdFatura +
                                           " para pagar a fatura #" + faturaNova.IdFatura + " e o resultado foi " +
                                           resultado.ResultadoSolicitacaoAprovacao);

            if (faturaNova.IdFatura > 0)
                Domain.Cobranca.FaturaRepository.Update(faturaNova);
            else
                Domain.Cobranca.FaturaRepository.SaveNew(faturaNova);

            AtualizarStatusContaEFaturaParaPagamentoRecorrente(faturaNova, faturaAnterior, resultado, dataHora, ehReprocessamentoManual);

            if (!resultado.TransacaoAprovada && enviaEmail && resultado.Exception == null)
                Domain.Pessoas.EnvioEmailService.EnviarEmailPagamentoDaFaturaNaoAutorizado(
                    assinaturaAtiva.ContaFinanceira.Pessoa.PessoaJuridica, faturaNova);
        }

        private void ProcessarFaturaRecorrenteSemValor(Fatura faturaNova, Fatura faturaAnterior, DateTime dataHora, bool ehReprocessamentoManual)
        {
            var resultado = new ResultadoTransacaoSemValorDTO(faturaNova, faturaAnterior);
            LogService<FaturaService>.Info("Solicitou recorrência de Pagamento da fatura #" +
                                           faturaAnterior.IdFatura +
                                           " para pagar a fatura #" + faturaNova.IdFatura + " com valor zerado");

            if (faturaNova.IdFatura > 0)
                Domain.Cobranca.FaturaRepository.Update(faturaNova);
            else
                Domain.Cobranca.FaturaRepository.SaveNew(faturaNova);

            AtualizarStatusContaEFaturaParaPagamentoRecorrente(faturaNova, faturaAnterior, resultado, dataHora, ehReprocessamentoManual);
        }

        #endregion IFaturaService Members

        private static ResultadoTransacao RealizarTransacao(FonteDePagamento fonteDePagamento, String ipComprador,
            Fatura fatura)
        {
            ResultadoTransacao resultado = null;
            if (fonteDePagamento.FormaPagamento.Tipo == TipoFormaPagamentoEnum.CartaoDeCredito)
            {
                var dadosCartao = ((FonteDePagamentoCartao)fonteDePagamento).DadosCartao;
                var dadosTransacao = new DadosTransacao();
                dadosTransacao.CartaoCredito = dadosCartao;
                dadosTransacao.NumeroDocumento = fatura.IdFatura.ToString();
                dadosTransacao.Operadora = CartaoCreditoOperadoraEnum.CIELO;
                dadosTransacao.IPComprador = ipComprador;
                
                var responsavelFinanceiro = fatura.Estabelecimento.ObterResponsavel();
                var endereco = fatura.Estabelecimento.PessoaJuridica.EnderecoProprio;
                var cpf = string.IsNullOrEmpty(dadosCartao.PortadorCPF) 
                    ? responsavelFinanceiro.Cpf
                    : dadosCartao.PortadorCPF;
                
                dadosTransacao.NomeComprador = responsavelFinanceiro.NomeCompleto;
                dadosTransacao.Email = responsavelFinanceiro.Email;
                dadosTransacao.Telefone = responsavelFinanceiro.ObterTelefoneDePessoaFisicaParaCadastro();
                dadosTransacao.DocumentoComprador = cpf;
                dadosTransacao.Endereco = new EnderecoDeCobranca
                {
                    Logradouro = endereco.Logradouro,
                    Numero = endereco.Numero,
                    Complemento = endereco.Complemento,
                    Bairro = endereco.Bairro,
                    CEP = endereco.Cep,
                    Cidade = endereco.Cidade,
                    Estado = endereco.UF.ToString(),
                };

                if (fatura.ValorCobrado.HasValue)
                    dadosTransacao.Valor = decimal.Round(fatura.ValorCobrado.Value, 2, MidpointRounding.AwayFromZero);

                if (fatura.EstaPaga)
                    throw new Exception("A fatura a ser processada já está paga");
                
                var pagarmeV5Disponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                    .ObterDisponibilidadeDeRecurso(fatura.Estabelecimento, Recurso.PagamentoFaturaTrinksPagarmeV5)
                    .EstaDisponivel;
                
                var servicoGateway = GatewayFaturaFactory.CriarGatewayParaNovasAssinaturas(pagarmeV5Disponivel);

                resultado = new ResultadoTransacao();

                if (!SimulationTool.Current.EhSimulacao)
                {
                    resultado = servicoGateway.SolicitarTransacao(dadosTransacao);

                    LogService<FaturaService>.Info("Solicitou pagamento da fatura #" + fatura.IdFatura +
                                                   " e o resultado foi " + resultado.ResultadoSolicitacaoAprovacao);
                }
            }
            else
                throw new Exception(
                    "Foi solicitado para realizar transacao de uma fatura que não possui realização de transação implementada.");

            return resultado;
        }

        /// <summary>
        /// Facilitador para testes de uma transação no ambiente de homologaçao.
        /// </summary>
        /// <returns>Resultado do teste</returns>
        public ResultadoTransacao TestarProcessamentoDeTransacao()
        {
            ResultadoTransacao resultado = null;

            var dadosCartao = new CartaoCredito();
            dadosCartao.Bandeira = CartaoCreditoBandeiraEnum.VISA;
            dadosCartao.CodigoSeguranca = "123";
            dadosCartao.Numero = "****************";
            dadosCartao.ValidadeAno = DateTime.Now.Year % 100;
            dadosCartao.ValidadeMes = 12;
            dadosCartao.PortadorNome = "Teste de Processamento - Nome";

            var dadostransacao = new DadosTransacao();
            dadostransacao.CartaoCredito = dadosCartao;
            dadostransacao.NumeroDocumento = "1";
            dadostransacao.Operadora = CartaoCreditoOperadoraEnum.CIELO;
            dadostransacao.IPComprador = "127.0.0.1";
            dadostransacao.Valor = 1;
            dadostransacao.Parcelas = 1;

            var servicoGateway = GatewayFaturaFactory.CriarGatewayEmHomologacao(GatewayEnum.CobreBem);

            resultado = new ResultadoTransacao();
            resultado = servicoGateway.SolicitarTransacao(dadostransacao);
            return resultado;
        }

        private void AtualizarStatusContaEFaturaParaPagamentoRecorrente(Fatura fatura, Fatura faturaAnterior, ResultadoTransacao resultado,
            DateTime dataHora, bool ehReprocessamentoManual = false)
        {
            var dadosCartao = new CartaoCredito
            {
                Bandeira = faturaAnterior.FormaPagamento,
                ValidadeMes = faturaAnterior.MesValidadeCartaoCredito.Value,
                ValidadeAno = faturaAnterior.AnoValidadeCartaoCredito.Value,
                PortadorNome = faturaAnterior.NomePortadorCartaoCredito,
                PortadorCPF = faturaAnterior.CpfPortadorCartaoCredito.RemoverFormatacaoCPFeCPNJ(),
                Numero = faturaAnterior.NumeroCartaoCredito
            };
            FonteDePagamento fonteDePagamento = new FonteDePagamentoCartao(faturaAnterior) { DadosCartao = dadosCartao };

            AtualizarStatusContaEFatura(fatura, fonteDePagamento, resultado, dataHora, false, false, true, ehReprocessamentoManual);
        }

        private void AtualizarStatusContaEFatura(Fatura fatura, FonteDePagamento fonteDePagamento,
            ResultadoTransacao resultado, DateTime dataHora, bool utilizandoNovoCartao = false,
            bool primeiroPagamento = false, bool ehRecorrencia = false, bool ehReprocessamentoManual = false)
        {
            AtualizarStatusFatura(fatura, fonteDePagamento, resultado, dataHora, utilizandoNovoCartao, primeiroPagamento, ehRecorrencia);
            if (resultado?.Exception == null)
                AtualizarStatusConta(fatura, dataHora, ehReprocessamentoManual);
        }

        private static void AtualizarStatusConta(Fatura fatura, DateTime dataHora, bool ehReprocessamentoManual = false)
        {
            if (SimulationTool.Current.EhSimulacao)
                return;

            StatusContaFinanceira statusInicial = fatura.Assinatura.ContaFinanceira.Status;
            StatusContaFinanceira statusFinal;

            var existeFaturasPendentesPagamento =
                Domain.Cobranca.FaturaRepository.ExisteFaturaPendenteDePagamentoEForaDaMargemDeIdentificacaoDePagamento(
                    fatura.Assinatura.IdAssinatura, dataHora);

            var naoEhNecessarioTerAFaturaPagaParaEstarAdimplente =
                Domain.Cobranca.PagamentoDeFaturaService.NaoEhNecessarioTerAFaturaPagaParaEstarAdimplente(fatura);

            if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento) && !fatura.Assinatura.ContaFinanceira.DataPrimeiraAssinatura.HasValue)
            {
                fatura.Assinatura.ContaFinanceira.FaturaPaga();
            }

            if ((naoEhNecessarioTerAFaturaPagaParaEstarAdimplente || fatura.EstaPaga) &&
                !existeFaturasPendentesPagamento)
            {
                Domain.Cobranca.AssinaturaService.DesfazerCancelamentoDaAssinatura(fatura.Assinatura);

                //TRINKS-13736 - Prevenção de null ao receber o mesmo enum.
                if (fatura.Assinatura.ContaFinanceira.Status != StatusContaFinanceira.Adimplente)
                    fatura.Assinatura.ContaFinanceira.Status = StatusContaFinanceira.Adimplente;

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(fatura.Assinatura.ContaFinanceira);
            }
            else if (existeFaturasPendentesPagamento && !ehReprocessamentoManual)
            {
                var dataDeBloqueioPorNaoPagamento = fatura.Assinatura.DataFimDaAssinaturaNoPeriodoDeTolerancia();
                var aindaEstaEmTolerancia = dataDeBloqueioPorNaoPagamento.Date > dataHora.Date;

                if (aindaEstaEmTolerancia)
                {
                    fatura.Assinatura.ContaFinanceira.Status = StatusContaFinanceira.InadimplenteEmTolerancia;
                }
                else
                {
                    fatura.Assinatura.ContaFinanceira.Status = StatusContaFinanceira.InadimplenteForaTolerancia;
                }

                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(fatura.Assinatura.ContaFinanceira);
            }

            Domain.Cobranca.AdicionalNaAssinaturaService.AplicarRegrasAposAlteracaoDeStatusDaContaFinanceiraNosAdicionaisContratados(fatura.Assinatura);

            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(
                    fatura.Assinatura.ContaFinanceira.Pessoa.IdPessoa);
            Domain.Pessoas.EstabelecimentoService.DefinirPesoBuscaPortal(estabelecimento, dataHora);

            HotsiteEstabelecimento hotSiteEstabelecimento = estabelecimento.Hotsite();

            if (hotSiteEstabelecimento != null)
                hotSiteEstabelecimento.PermiteBuscaHotsite = hotSiteEstabelecimento.ValorAnteriorPermiteBuscaHotsite ?? false;

            statusFinal = fatura.Assinatura.ContaFinanceira.Status;

            if (statusInicial == StatusContaFinanceira.InadimplenteForaTolerancia
                && (statusFinal == StatusContaFinanceira.Adimplente || statusFinal == StatusContaFinanceira.InadimplenteEmTolerancia))
            {
                Domain.Pessoas.ContaService.DeslogarTodosOsUsuariosDoEstabelecimento(estabelecimento.IdEstabelecimento);
            }
        }

        private void AtualizarStatusFatura(Fatura fatura, FonteDePagamento fonteDePagamento,
            ResultadoTransacao resultado, DateTime dataHora, bool utilizandoNovoCartao, bool primeiroPagamento, bool ehrecorrencia)
        {
            if (SimulationTool.Current.EhSimulacao)
                return;

            //TODO: se for necessário verificar implementação para ontras formas de pagamento
            CartaoCredito dadosCartao;
            if (fonteDePagamento.FormaPagamento.Tipo == TipoFormaPagamentoEnum.CartaoDeCredito)
                dadosCartao = ((FonteDePagamentoCartao)fonteDePagamento).DadosCartao;
            else
                throw new Exception("AtualizarStatusFatura não está implementado para " +
                                    fonteDePagamento.FormaPagamento.Tipo);
            //TODO: se for necessário verificar implementação para ontras formas de pagamento

            #region SimulacoesParaTestes

            //TODO: Faz não serem aprovados os pagamentos do CNPJ contido em Session["cnpjErro"]
            var testePermiteAprovarTransacao = true;
            if (HttpContext.Current != null)
            {
                var cnpjErro = (string)HttpContext.Current.Session["cnpjErro"];
                if (!string.IsNullOrWhiteSpace(cnpjErro))
                {
                    var cnpj = fatura.Assinatura.ContaFinanceira.Pessoa.PessoaJuridica.CNPJ;
                    if (cnpjErro == cnpj)
                    {
                        testePermiteAprovarTransacao = false;
                        LogService<FaturaService>.Info("Forçando a não aprovação da fatura #" + fatura.IdFatura +
                                                       " do cnpj " + cnpj);
                        HttpContext.Current.Session["cnpjErro"] = null;
                    }
                }
            }

            #endregion SimulacoesParaTestes

            if (resultado.TransacaoAprovada && testePermiteAprovarTransacao)
            {
                var numeroCartao = dadosCartao.Numero;
                if (char.IsDigit(resultado.CartaoMascarado.First()))
                    numeroCartao = resultado.CartaoMascarado;

                fatura.IdTransacaoGateway = Int64.Parse(resultado.Transacao);
                fatura.TokenDeRecorrencia = resultado.TokenDeRecorrencia;
                fatura.Gateway = resultado.Gateway;
                fatura.IdTransacao = resultado.CodigoAutorizacao;
                fatura.FormaPagamento = dadosCartao.Bandeira;
                fatura.FormaPagamento.Nome = fonteDePagamento.FormaPagamento.Nome;
                fatura.NumeroCartaoCredito = numeroCartao;
                fatura.MesValidadeCartaoCredito = dadosCartao.ValidadeMes;
                fatura.AnoValidadeCartaoCredito = dadosCartao.ValidadeAno;
                fatura.NomePortadorCartaoCredito = dadosCartao.PortadorNome;
                fatura.CpfPortadorCartaoCredito = dadosCartao.PortadorCPF.RemoverFormatacaoCPFeCPNJ();
                fatura.StatusTemporarioTransacaoGateway = "Aprovada";

                AtualizarStatusFatura(fatura, StatusFaturaEnum.Paga, dataHora: dataHora, primeiroPagamento: primeiroPagamento, valorPago: fatura.ValorCobrado, cancelamentoPelaAreaPerlink: true);
            }
            else if (resultado.Exception == null)
            {
                if (primeiroPagamento || utilizandoNovoCartao)
                {
                    // Só cancela se ela anteriormente estava aberta
                    if (fatura.Status == StatusFaturaEnum.Aberta)
                        AtualizarStatusFatura(fatura, StatusFaturaEnum.Cancelada, dataHora, utilizandoNovoCartao: utilizandoNovoCartao, primeiroPagamento: primeiroPagamento);
                }
                else if (ehrecorrencia)
                {
                    AtualizarStatusFatura(fatura, StatusFaturaEnum.Vencida, dataHora, null, utilizandoNovoCartao, primeiroPagamento);
                }
                else
                {
                    //TRINKS-9820 - PARA ERRO DE PAGAMENTO NO CARTÃO A FATURA SÓ SERÁ CANCELADA SE ESTIVER UTILIZANDO UM NOVO CARTÃO OU SE FOR O PRIMEIRO PAGAMENTO
                    //PARA AS DEMAIS SITUAÇÕES O STATUS SERÁ SEMPRE "ABERTA"
                    AtualizarStatusFatura(fatura, StatusFaturaEnum.Aberta, dataHora, null, utilizandoNovoCartao, primeiroPagamento);
                }

                fatura.StatusTemporarioTransacaoGateway = "Não Aprovada";
                fatura.MotivoTemporarioTransacaoGateway = resultado.ResultadoSolicitacaoAprovacao;
            }
            else
            {
                if (primeiroPagamento || utilizandoNovoCartao)
                {
                    fatura.StatusTemporarioTransacaoGateway = "Não Aprovada";
                    AtualizarStatusFatura(fatura, StatusFaturaEnum.Cancelada, dataHora: dataHora, utilizandoNovoCartao: utilizandoNovoCartao, primeiroPagamento: primeiroPagamento);
                }
                else
                {
                    fatura.StatusTemporarioTransacaoGateway = "Em Processamento";
                    if (fatura.Status == StatusFaturaEnum.Aberta)
                        AtualizarStatusFatura(fatura, StatusFaturaEnum.Cancelada, dataHora: dataHora, utilizandoNovoCartao: utilizandoNovoCartao, primeiroPagamento: primeiroPagamento);
                }
                fatura.MotivoTemporarioTransacaoGateway =
                    ("Gateway de pagamento fora do ar - " + resultado.ResultadoSolicitacaoAprovacao).Left(200);
            }

            fatura.DataUltimoProcessamento = dataHora;
            fatura.DataParaProcessar = null;
            Domain.Cobranca.FaturaRepository.Update(fatura);
        }

        public void AtualizarStatusFatura(Fatura fatura, StatusFaturaEnum novoStatus, DateTime dataHora, decimal? valorPago = null, bool utilizandoNovoCartao = false,
            bool primeiroPagamento = false, bool cancelamentoPelaAreaPerlink = false, PessoaFisica pessoaLogada = null)
        {
            if (novoStatus == fatura.Status)
                return;

            if (!SimulationTool.Current.EhSimulacao)
            {
                switch (novoStatus)
                {
                    case StatusFaturaEnum.Aberta:
                        fatura.Status = StatusFaturaEnum.Aberta;
                        break;

                    case StatusFaturaEnum.Paga:
                        fatura.Status = StatusFaturaEnum.Paga;
                        fatura.DataAprovacao = dataHora;
                        fatura.MotivoTemporarioTransacaoGateway = String.Empty;
                        fatura.ValorAprovado = valorPago ?? fatura.ValorCobrado;

                        Domain.Cobranca.AdicionalNaAssinaturaService
                            .AplicarRegrasPosPagamentoDeFatura(fatura.Assinatura);

                        if (fatura is FaturaMarketing)
                        {
                            var faturaMarketing = (FaturaMarketing)fatura;
                            AdicionarCreditosDaFaturaMarketing(faturaMarketing);
                        }
                        if (fatura is FaturaWhatsApp)
                        {
                            var faturaWhatsApp = (FaturaWhatsApp)fatura;
                            AdicionarCreditosDaFaturaWhatsApp(faturaWhatsApp);
                        }
                        break;

                    case StatusFaturaEnum.Vencida:
                        fatura.Status = StatusFaturaEnum.Vencida;
                        break;

                    case StatusFaturaEnum.Cancelada:
                        fatura.Status = StatusFaturaEnum.Cancelada;
                        fatura.DataCancelamento = dataHora;

                        if (cancelamentoPelaAreaPerlink && pessoaLogada != null)
                        {
                            fatura.MotivoCancelamento = "Conta cancelada por ação do usuário administrativo, através da opção Cancelar Assinatura, em Área Perlink > Cobrança";
                        }
                        else if (!cancelamentoPelaAreaPerlink && pessoaLogada != null)
                        {
                            fatura.MotivoCancelamento = "Conta cancelada por ação do usuário, através da opção Cancelar Assinatura, em BackOffice > Conta.";
                        }
                        else
                        {
                            fatura.MotivoCancelamento = "Conta cancelada pela rotina diária";
                        }

                        // http://jira.perlink.net/browse/TRINKS-2982
                        if (primeiroPagamento)
                        {
                            fatura.MotivoCancelamento =
                                "Fatura cancelada em tentativa de primeira assinatura de conta financeira";
                            if (fatura is FaturaTrinks)
                            {
                                var faturaTrinks = (FaturaTrinks)fatura;
                                if (faturaTrinks.PromocaoPraContaFinanceira != null)
                                {
                                    faturaTrinks.PromocaoPraContaFinanceira.JahAdquirido = false;
                                }
                            }
                        }
                        else if (utilizandoNovoCartao)
                            fatura.MotivoCancelamento = "Fatura cancelada em tentativa de alterar o cartão utilizado";
                        break;

                    default:
                        break;
                }

                Domain.Cobranca.FaturaRepository.Update(fatura);
                if (fatura is FaturaTrinks)
                {
                    if (novoStatus == StatusFaturaEnum.Paga)
                    {
                        var faturaTrinks = (FaturaTrinks)fatura;
                        RealizarPushDadosPrimeiroPagamento(faturaTrinks, primeiroPagamento);
                    }
                }
            }

            switch (novoStatus)
            {
                case StatusFaturaEnum.Aberta:
                    LogService<FaturaService>.Info("Abriu a fatura #" + fatura.IdFatura);
                    break;

                case StatusFaturaEnum.Paga:
                    LogService<FaturaService>.Info("Pagou a fatura #" + fatura.IdFatura);
                    break;

                case StatusFaturaEnum.Vencida:
                    LogService<FaturaService>.Info("Colocou como vencida a fatura #" + fatura.IdFatura);
                    break;

                case StatusFaturaEnum.Cancelada:
                    LogService<FaturaService>.Info("Cancelou a fatura #" + fatura.IdFatura);
                    break;

                default:
                    break;
            }
        }

        private void AdicionarCreditosDaFaturaMarketing(FaturaMarketing fatura)
        {
            var pacotesCompraDeCredito = fatura.ListaMarketingCompraCredito;
            foreach (var item in pacotesCompraDeCredito)
            {
                Domain.Marketing.MarketingService.AdicionarCredito(item.MarketingPacoteCredito.IdMarketingPacoteCredito, fatura.Estabelecimento.IdEstabelecimento);
            }
        }

        private void AdicionarCreditosDaFaturaWhatsApp(FaturaWhatsApp fatura)
        {
            var pacotesCompraDeCredito = fatura.ListaWhatsAppCompraCredito;
            foreach (var item in pacotesCompraDeCredito)
            {
                Domain.WhatsApp.CreditoService.AdicionarCreditoCompraPacote(item.PacoteCredito.Id, item.PacoteCredito.Tipo,
                    fatura.Estabelecimento.IdEstabelecimento, item.RecompraAutomatica);
            }
        }

        public bool AdicionarNotificacaoDeValidadeDoCartaoCredito(Assinatura assinaturaVigente)
        {
            if (assinaturaVigente == null || !assinaturaVigente.Ativo || !assinaturaVigente.IdFormaPagamento.HasValue
                || !FormaPagamentoHelper.EhCartaoDeCredito((FormaDePagamentoEnum)assinaturaVigente.IdFormaPagamento.Value)
                || assinaturaVigente.ContaFinanceira.Status == StatusContaFinanceira.CobrancaManual)
                return false;

            var cartaoExpirado = Domain.Cobranca.FaturaRepository.VerificarSeCartaoExpirouDaUltimaFaturaPaga(assinaturaVigente.IdAssinatura);
            var cartaoVaiExpirar = false;
            var podeTrocarCartao = false;

            if (!cartaoExpirado)
            {
                cartaoVaiExpirar = Domain.Cobranca.FaturaRepository.VerificarSeCartaoVaiExpirarDaUltimaFaturaPaga(assinaturaVigente.IdAssinatura);

                if (cartaoVaiExpirar)
                    podeTrocarCartao = VerificarSePodeTrocarCartao(assinaturaVigente);
            }

            return cartaoExpirado || (cartaoVaiExpirar && podeTrocarCartao);
        }

        public DateTime ObterDataInicioParaTrocarFormaPagamento(Assinatura assinaturaVigente)
        {
            var diasAntecedenciaMaximaParaTrocaFormaPagamento = new ParametrosTrinks<int>(ParametrosTrinksEnum.dias_antecedencia_maxima_para_troca_cartao).ObterValor();
            var dataInicioProximoPeriodo = Domain.Cobranca.AssinaturaService.ObterDataInicioProximoPeriodoNaoPago(assinaturaVigente, Calendario.Agora());

            return dataInicioProximoPeriodo.AddDays(-diasAntecedenciaMaximaParaTrocaFormaPagamento);
        }

        public bool VerificarSePodeTrocarCartao(Assinatura assinaturaVigente)
        {
            return ObterDataInicioParaTrocarFormaPagamento(assinaturaVigente).Date <= Calendario.Hoje();
        }

        public int QuantasFaturasAindaTeraoDescontoDePromocao(int idAssinatura)
        {
            int quantasFaturas = 0;
            var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraAtivaEAssinadaPelaAssinatura(idAssinatura);
            //var promocao = Domain.Cobranca.PromocaoPraContaFinanceiraRepository.ObterPromocaoContaFinanceiraPelaAssinatura(idAssinatura);

            if (promocao == null && !promocao.JahAdquirido && promocao.Desconto == 0)
                return 0;

            var qtdFaturasUsadas = Domain.Cobranca.FaturaTrinksRepository.ObterQuantidadeFaturasParaPromocao(idAssinatura, promocao.Id);

            quantasFaturas = promocao.QuantasMensalidadesODescontoSerahAplicado - qtdFaturasUsadas;
            return quantasFaturas >= 0 ? quantasFaturas : 0;
        }

        public void CancelarFatura(Fatura fatura, DateTime dataHora)
        {
            AtualizarStatusFatura(fatura, StatusFaturaEnum.Cancelada, dataHora);
        }

        public void CancelarFatura(int idFatura)
        {
            var fatura = Domain.Cobranca.FaturaRepository.Load(idFatura);
            if (fatura != null)
            {
                AtualizarStatusFatura(fatura, StatusFaturaEnum.Cancelada, Calendario.Agora());
            }
        }

        public void CancelarFaturasDaAssinatura(Assinatura assinatura, DateTime dataHora, bool cancelamentoPelaAreaPerlink = false, PessoaFisica pessoaLogada = null)
        {
            var faturas = Domain.Cobranca.FaturaRepository.ObterTodasAbertaOuVencida(assinatura);
            foreach (var fatura in faturas)
            {
                Domain.Cobranca.FaturaService.CancelarFatura(fatura, dataHora);
            }
        }

        private void RealizarPushDadosPrimeiroPagamento(FaturaTrinks fatura, bool primeiroPagamento)
        {
            try
            {
                var percentualDeDescontoNaAssinatura = fatura.PromocaoPraContaFinanceira != null ? fatura.PromocaoPraContaFinanceira.Desconto : 0;

                var nomesDosAdicionaisNaAssinatura = Domain.Cobranca.AdicionalNaAssinaturaService.ListarNomeDosAdicionaisContratadosNaAssinatura(fatura.Assinatura);
                var stringAdicionaisContrataos = "";
                if (nomesDosAdicionaisNaAssinatura.Count > 0)
                {
                    stringAdicionaisContrataos = string.Join(" / ", nomesDosAdicionaisNaAssinatura);
                }

                // informações sobre a faixa faturada [KANBANFDB-33]:
                var faixaString = "";
                switch (fatura.Assinatura.QuantidadeDeProfissionaisEsperada)
                {
                    case 1:
                    case 2:
                        faixaString = "1 a 2 profissonais";
                        break;

                    case 4:
                        faixaString = "3 a 4 profissonais";
                        break;

                    case 10:
                        faixaString = "5 a 10 profissonais";
                        break;

                    case 20:
                        faixaString = "11 a 20 profissonais";
                        break;

                    case 30:
                        faixaString = "21 a 30 profissonais";
                        break;

                    case 40:
                        faixaString = "31 a 40 profissionais";
                        break;

                    case 50:
                        faixaString = "41 a 50 profissionais";
                        break;

                    case 60:
                        faixaString = "51 a 60 profissionais";
                        break;

                    case 70:
                        faixaString = "61 a 70 profissionais";
                        break;

                    case 71:
                        faixaString = "acima de 70 profissionais";
                        break;

                    default:
                        faixaString = "Número máximo de profissionais cadastrado: " + fatura.QuantMaxProfissionais.ToString();
                        break;
                };

                var dados = new
                {
                    Estabelecimento = fatura.Estabelecimento.NomeDeExibicaoNoPortal,
                    IdEstabelecimento = fatura.Estabelecimento.IdEstabelecimento,
                    ValorFatura = fatura.ValorAprovado,
                    DataPagamento = fatura.DataAprovacao,
                    PrimeiroPagamento = primeiroPagamento,
                    PlanoDeAssinatura = fatura.Assinatura != null ? fatura.Assinatura.PlanoAssinatura.Nome : string.Empty,
                    DescontoNaAssinatura = percentualDeDescontoNaAssinatura,
                    AdicionaisContratados = stringAdicionaisContrataos,
                    FaixaFaturada = faixaString, // informações sobre a faixa faturada [KANBANFDB-33]
                    EmailResponsavel = fatura.Assinatura.ContaFinanceira.Pessoa.Email, // e-mail do lead, usado pelo RD Station [KANBANFDB-112]
                };

                var json = JsonConvert.SerializeObject(dados);
                var sns = ConfiguracoesTrinks.AWS.PushDadosPagamentoFaturaTrinks;
                ServicoDeNotificacaoSNS.PublicarNotificacaoSNS(sns, json);
            }
            catch (Exception e)
            {
                ErrorSignal.FromContext(HttpContext.Current).Raise(e, HttpContext.Current);
            }
        }

        public FaturaTrinks CriarProximaFaturaFicticia(Assinatura assinatura, PlanoAssinatura planoAssinatura = null)
        {
            var faturaFicticia = Domain.Cobranca.FaturaTrinksRepository.Factory.CreateParaGerarFaturaFicticia(assinatura, planoAssinatura);
            return faturaFicticia;
        }

        public List<KeyValuePair<int, StatusFaturaEnum>> ObterFaturasQueUtilizaramDescontoNaCobrancaDoAdicionalNaAssinatura(int idAdicionalNaAssinatura, int idDescontoNoAdicional)
        {
            var adicionaisCobrados = Domain.Cobranca.AdicionalCobradoRepository.Queryable();

            var cobrancasComDesconto = from ac in adicionaisCobrados
                                       where ac.AdicionalDeReferencia.Id == idAdicionalNaAssinatura
                                          && ac.OrigemDesconto.Id == idDescontoNoAdicional
                                          && ac.ValorSemDesconto != null
                                       select new KeyValuePair<int, StatusFaturaEnum>(ac.Fatura.IdFatura, (StatusFaturaEnum)ac.Fatura.Status.IdStatus);

            return cobrancasComDesconto.ToList();
        }

        public bool ExisteFasturasPagasComDescontoEmPeriodoDeReferencia(int idAdicionalNaAssinatura, int idDescontoNoAdicional)
        {
            var adicionaisCobrados = Domain.Cobranca.AdicionalCobradoRepository.Queryable();

            var cobrancasComDesconto = from ac in adicionaisCobrados
                                       where ac.AdicionalDeReferencia.Id == idAdicionalNaAssinatura
                                             && ac.OrigemDesconto.Id == idDescontoNoAdicional
                                             && ac.ValorSemDesconto != null
                                             && ac.Fatura.Status.IdStatus == (int)StatusFaturaEnum.Paga
                                             && ac.Fatura.FimPeriodoReferencia >= Calendario.Hoje()
                                       select ac;

            return cobrancasComDesconto.Any();
        }

        public void AtualizarValoresDaFaturaPendenteDePagamento(Assinatura assinatura)
        {
            var faturaTrinksCorrente = Domain.Cobranca.FaturaTrinksRepository.ObterFaturaTrinksMaisAntigaPendenteDePagamento(assinatura.IdAssinatura);
            if (faturaTrinksCorrente != null)
            {
                var qtdProfissionaisNaCobranca = assinatura.ObterQuantidadeDeProfissionaisParaConsiderarNaCobranca();
                faturaTrinksCorrente.GerarRegistroDeCobrancaDosServicosAdicionais(qtdProfissionaisNaCobranca);
                faturaTrinksCorrente.ValorCobrado = CalcularValorFatura(faturaTrinksCorrente);
                Domain.Cobranca.FaturaTrinksRepository.Update(faturaTrinksCorrente);
            }
        }

        [TransactionInitRequired]
        public FaturaWhatsApp GerarNovaFaturaParaCreditosDeWhatsApp(Estabelecimento estabelecimento, FonteDePagamento fonteDePagamento,
            DateTime dataHora, List<CompraCredito> comprasCredito, decimal valorTotalASerCobrado)
        {
            var fatura = new FaturaWhatsApp();
            AplicarListaDeComprasNaFaturaWhatsApp(comprasCredito, fatura);
            fatura.DataEmissao = dataHora;
            fatura.InicioPeriodoReferencia = dataHora;
            fatura.FimPeriodoReferencia = dataHora;
            fatura.Status = StatusFaturaEnum.Aberta;
            fatura.ValorCobrado = valorTotalASerCobrado;
            fatura.Ativo = true;
            fatura.FormaPagamento = fonteDePagamento.FormaPagamento;
            fatura.Estabelecimento = estabelecimento;
            fatura.DataVencimento = dataHora;

            if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento))
                fatura.DataVencimento = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametro(dataHora);
            else
                fatura.DataVencimento = Calendario.Hoje();

            if (fatura.FormaPagamento != null && fatura.FormaPagamento.PossuiProcessamentoAutomatico())
                fatura.DataParaProcessar = fatura.DataVencimento;

            if (!SimulationTool.Current.EhSimulacao)
                Domain.Cobranca.FaturaWhatsAppRepository.SaveNew(fatura);

            LogService<FaturaService>.Info("Gerou fatura #" + fatura.IdFatura + " da compra de créditos de WhatsApp " +
                                           " do Estabelecimento #" + estabelecimento.IdEstabelecimento +
                                           " - Forma pagamento: " + fatura.FormaPagamento.Nome + " - Pacote: " + comprasCredito[0].Id);
            return fatura;
        }

        [TransactionInitRequired]
        public FaturaWhatsApp AtualizarFaturaCreditosDeWhatsApp(FaturaWhatsApp fatura,
            DateTime dataHora, List<CompraCredito> comprasCredito, decimal valorTotalASerCobrado)
        {
            AplicarListaDeComprasNaFaturaWhatsApp(comprasCredito, fatura, true);

            fatura.ValorCobrado = valorTotalASerCobrado;
            fatura.Ativo = true;

            if (FormaPagamentoHelper.TemProcessoAssincrono(fatura.FormaPagamento))
                fatura.DataVencimento = Domain.Pessoas.DataEspecialService.ProximoDiaUtilIncluindoADataNoParametro(dataHora);
            else
                fatura.DataVencimento = Calendario.Hoje();

            if (fatura.FormaPagamento != null && fatura.FormaPagamento.PossuiProcessamentoAutomatico())
                fatura.DataParaProcessar = fatura.DataVencimento;

            if (!SimulationTool.Current.EhSimulacao)
                Domain.Cobranca.FaturaWhatsAppRepository.Update(fatura);

            LogService<FaturaService>.Info("Atualização da fatura #" + fatura.IdFatura + " da compra de créditos de WhatsApp " +
                                           " do Estabelecimento #" + fatura.Estabelecimento.IdEstabelecimento +
                                           " - Forma pagamento: " + fatura.FormaPagamento.Nome + " - Pacote: " + comprasCredito[0].Id);
            return fatura;
        }

        private static void AplicarListaDeComprasNaFaturaWhatsApp(List<CompraCredito> comprasCredito, FaturaWhatsApp fatura,
            bool atualizacao = false)
        {
            //Desativar compra crédito anteriores
            if (atualizacao)
                foreach (var cc in fatura.ListaWhatsAppCompraCredito)
                    cc.Ativo = false;

            //Adicionar novos compra créditos
            foreach (var compra in comprasCredito)
            {
                compra.Fatura = fatura;
                if (atualizacao)
                {
                    if (fatura.ListaWhatsAppCompraCredito.Where(l => l.PacoteCredito.Id == compra.PacoteCredito.Id).Any())
                    {
                        var item = fatura.ListaWhatsAppCompraCredito.Where(l => l.PacoteCredito.Id == compra.PacoteCredito.Id).First();
                        item.Ativo = true;
                        item.Valor = compra.Valor;
                    }
                    else
                        fatura.ListaWhatsAppCompraCredito.Add(compra);
                }

            }

            //Atribuir somente em caso de não ser atualização da fatura
            if (!atualizacao)
                fatura.ListaWhatsAppCompraCredito = comprasCredito;
        }

        public void ColocarFaturaComoCancelada(Fatura fatura, string motivo)
        {
            if (fatura == null || fatura.IdFatura == 0)
                return;

            fatura.Status = StatusFaturaEnum.Cancelada;
            fatura.DataCancelamento = Calendario.Agora();
            fatura.MotivoCancelamento = String.IsNullOrEmpty(motivo)
                    ? "Conta cancelada por ação do usuário administrativo, através da opção Cancelar Assinatura, em Área Perlink > Cobrança"
                    : motivo;
            fatura.DataAprovacao = null;
            fatura.ValorAprovado = null;
            fatura.IdTransacaoGateway = null;
            fatura.IdTransacao = null;
            fatura.StatusTemporarioTransacaoGateway = null;

            Domain.Cobranca.FaturaRepository.Update(fatura);
        }

        public void ColocarFaturaComoNaoPaga(Fatura fatura, DateTime? dataVencimento = null)
        {
            if (fatura == null || fatura.IdFatura == 0)
                return;

            var data = dataVencimento.HasValue ? dataVencimento.Value : Calendario.Agora();

            fatura.DataVencimento = data;
            fatura.DataParaProcessar = data;
            fatura.DataCancelamento = null;
            fatura.MotivoCancelamento = null;
            fatura.DataAprovacao = null;
            fatura.ValorAprovado = null;
            fatura.IdTransacao = null;
            fatura.Status = StatusFaturaEnum.Aberta;
            fatura.DataDeTolerancia = data.AddDays(fatura.DiasToleranciaNaoPagamento);

            Domain.Cobranca.FaturaRepository.Update(fatura);
        }

        public void AjustarDataReferenciaFatura(Fatura fatura, DateTime? dataInicio, DateTime? dataFim)
        {
            if (fatura == null || fatura.IdFatura == 0)
                return;

            if (dataInicio.HasValue)
                fatura.InicioPeriodoReferencia = dataInicio.Value;

            if (dataFim.HasValue)
                fatura.FimPeriodoReferencia = dataFim.Value;

            Domain.Cobranca.FaturaRepository.Update(fatura);
        }
    }
}
