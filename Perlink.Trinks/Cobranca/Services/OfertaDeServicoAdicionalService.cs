﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cobranca.Services
{
    public class OfertaDeServicoAdicionalService : BaseService, IOfertaDeServicoAdicionalService
    {

        public OfertaDeServicoAdicional ObterOfertaDeServicoAdicionai(int idPlanoAssinatura, ServicoAdicionalEnum recurso)
        {

            OfertaDeServicoAdicional oferta = Domain.Cobranca.OfertaDeServicoAdicionalRepository.ObterVinculadaAoPlanoPeloServico(idPlanoAssinatura, (int)recurso);

            bool existeOfertaAssociadaAoPlano = oferta != null;
            if (!existeOfertaAssociadaAoPlano)
                oferta = Domain.Cobranca.OfertaDeServicoAdicionalRepository.ObterOfertaDefaultQueNaoEstejaVinculadaAUmPlanoPeloServico((int)recurso);

            return oferta;
        }

        public ValorDoAdicionalPorFaixaDaOferta ObterValorDeCobrancaDoAdicional(int idEstabelecimento, ServicoAdicionalEnum idServico, int? idFormaDeContratacao)
        {

            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinaturaPorIdEstabelecimento(idEstabelecimento);
            var qtdProfissionais = assinatura.ObterQuantidadeDeProfissionaisParaConsiderarNaCobranca();
            var ofertaDoPlano = Domain.Cobranca.OfertaDeServicoAdicionalService.ObterOfertaDeServicoAdicionai(assinatura.PlanoAssinatura.IdPlano, idServico);
            var formaDeContratacao = Domain.Cobranca.AdicionalNaAssinaturaService.ObterFormaDeContratacaoDoServicoAdicional(ofertaDoPlano.Servico, idFormaDeContratacao);

            return ObterValorDeCobrancaDoAdicionalNaOferta(ofertaDoPlano, qtdProfissionais, formaDeContratacao);
        }

        public ValorDoAdicionalPorFaixaDaOferta ObterValorDeCobrancaDoAdicionalNaOferta(OfertaDeServicoAdicional oferta, int qtdProfissionaisParaConsiderarNaCobranca, FormaDeContratacaoDoAdicional formaDeContratacao)
        {
            ValorDoAdicionalPorFaixaDaOferta valorDeReferencia = null;

            if (formaDeContratacao != null)
                valorDeReferencia = ObterValorDoAdicionalPorFaixaDaOfertaPorFaixa(qtdProfissionaisParaConsiderarNaCobranca, oferta, formaDeContratacao.Id);

            if (valorDeReferencia == null)
                valorDeReferencia = ObterValorDoAdicionalPorFaixaDaOfertaPorFaixa(qtdProfissionaisParaConsiderarNaCobranca, oferta, null);

            return valorDeReferencia;
        }

        private ValorDoAdicionalPorFaixaDaOferta ObterValorDoAdicionalPorFaixaDaOfertaPorFaixa(int quantidadeDeProfissionaisParaConsiderarNaCobranca, OfertaDeServicoAdicional ofertaDeServicoAdicional, int? idFormaDeContratacaoDoAdicional)
        {
            return Domain.Cobranca.ValorDoAdicionalPorFaixaDaOfertaRepository.Queryable()
                .FirstOrDefault(v => v.OfertaDeAdicional.Id == ofertaDeServicoAdicional.Id
                && v.MinimoDeProfissionais <= quantidadeDeProfissionaisParaConsiderarNaCobranca
                && v.MaximoDeProfissionais >= quantidadeDeProfissionaisParaConsiderarNaCobranca
                && v.FormaDeContratacao.Id == idFormaDeContratacaoDoAdicional);
        }

        public DisponibilidadeDeContratacaoDaOfertaEnum ObterDisponibilidadeDeContratacaoDaOfertaPeloEstabelecimento(Assinatura assinatura, OfertaDeServicoAdicional oferta)
        {
            var disponibilidade = DisponibilidadeDeContratacaoDaOfertaEnum.Contratacao_Disponivel_Pelo_BackOffice;

            if (assinatura.ContaFinanceira.Status == StatusContaFinanceira.CobrancaManual)
                disponibilidade = DisponibilidadeDeContratacaoDaOfertaEnum.Contratacao_Disponivel_Somente_Pelo_Atendimento;

            else if (EstabelecimentoPrecisaRegularSituacaoAntesDaContratacaoDeAdicional(assinatura.ContaFinanceira.Status))
                disponibilidade = DisponibilidadeDeContratacaoDaOfertaEnum.Contratacao_Bloqueada_Necessario_Rever_Pendencias;

            else if (Domain.Cobranca.AdicionalNaAssinaturaService.ServicoJahEstahContratadoNessaAssinatura(assinatura, oferta.Servico))
                disponibilidade = DisponibilidadeDeContratacaoDaOfertaEnum.Contratacao_Bloqueada_Outros_Motivos;

            return disponibilidade;
        }

        private bool EstabelecimentoPrecisaRegularSituacaoAntesDaContratacaoDeAdicional(StatusContaFinanceira situacaoAtual)
        {
            StatusContaFinanceira[] statusBloqueadosParaContratacao = new StatusContaFinanceira[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.NaoAssinado,
                StatusContaFinanceira.InadimplenteForaTolerancia,
                StatusContaFinanceira.ContaCancelada
            };
            return statusBloqueadosParaContratacao.Contains(situacaoAtual);
        }

        public List<ValorDoAdicionalPorFaixaDaOferta> ListarValoresDeAdicionaisPorPlanoEQuantidadeDeProfissionaisNaCobranca(int idPlanoAssinaura, int qtdProfissionaisNaCobranca)
        {
            var valores = new List<ValorDoAdicionalPorFaixaDaOferta>();

            var ofertasDeServicosAdicionaisDoPlano = ListarOfertasDisponiveisNoPlanoDeAssintura(idPlanoAssinaura);

            foreach (var ofertaDeServicoAdicional in ofertasDeServicosAdicionaisDoPlano)
            {

                var valorDeCobrancaDoAdicional = Domain.Cobranca.OfertaDeServicoAdicionalService
                    .ObterValorDeCobrancaDoAdicionalNaOferta(ofertaDeServicoAdicional, qtdProfissionaisNaCobranca, ofertaDeServicoAdicional.Servico.FormaContratacaoPadrao);

                valores.Add(valorDeCobrancaDoAdicional);
            }

            return valores;
        }

        public List<OfertaDeServicoAdicional> ListarOfertasDisponiveisNoPlanoDeAssintura(int idPlanoAssinatura)
        {

            var ofertasEspecificasDoPlano = Domain.Cobranca.OfertaDeServicoAdicionalRepository.FiltrarPorOfertasAtivasQueEstejamVinculadasAoPlano(idPlanoAssinatura).ToList();
            var ofertasGerais = Domain.Cobranca.OfertaDeServicoAdicionalRepository.FiltrarPorOfertasAtivasQueNaoEstejaVinculadaAUmPlano().ToList();

            ofertasGerais.RemoveAll(ofertaGeral => ofertasEspecificasDoPlano.Any(ofe => ofe.Servico == ofertaGeral.Servico));

            return ofertasEspecificasDoPlano.Union(ofertasGerais).ToList();
        }

        public List<ValorDoAdicionalNoPlanoPorFormaContratacaoDTO> ListarValoresDeFormaContratacaoAdicionaisPorPlanoEQuantidadeDeProfissionaisNaCobranca
            (int idPlanoAssinaura, int qtdProfissionaisNaCobranca)
        {
            var valores = new List<ValorDoAdicionalNoPlanoPorFormaContratacaoDTO>();

            var ofertasDeServicosAdicionaisDoPlano = ListarOfertasDisponiveisNoPlanoDeAssintura(idPlanoAssinaura);
            var formasContratacao = Domain.Cobranca.FormaDeContratacaoDoAdicionalRepository.Queryable().ToList();
            var idsOfertas = ofertasDeServicosAdicionaisDoPlano.Select(oferta => oferta.Id).ToList();
            var valoresDaFaixa = Domain.Cobranca.ValorDoAdicionalPorFaixaDaOfertaRepository.Queryable()
                        .Where(v => v.MinimoDeProfissionais <= qtdProfissionaisNaCobranca
                        && v.MaximoDeProfissionais >= qtdProfissionaisNaCobranca &&
                        v.IdOfertaDeAdicional != null &&
                        idsOfertas.Contains(v.IdOfertaDeAdicional.Value))
                        .Select(v => new
                        {
                            OfertaAdicionalId = v.IdOfertaDeAdicional,
                            FormaContratacaoId = v.IdFormaContratacao,
                            v.Valor
                        }).ToList();

            foreach (var ofertaDeServicoAdicional in ofertasDeServicosAdicionaisDoPlano)
            {
                foreach (var formaContratacao in formasContratacao.Where(fc => fc.IdServicoAdicional == ofertaDeServicoAdicional.Servico.IdServico))
                {
                    var valoresPorAdicionalEFormaContratacao = valoresDaFaixa
                        .Where(v => v.OfertaAdicionalId == ofertaDeServicoAdicional.Id
                        && (v.FormaContratacaoId == null || v.FormaContratacaoId == formaContratacao.Id)).ToList();

                    if (valoresPorAdicionalEFormaContratacao.Count() == 0) continue;

                    var valorPorFaixa = valoresPorAdicionalEFormaContratacao.Count() > 1 ?
                        valoresPorAdicionalEFormaContratacao.FirstOrDefault(v => v.FormaContratacaoId == formaContratacao.Id) :
                        valoresPorAdicionalEFormaContratacao.FirstOrDefault();

                    valores.Add(new ValorDoAdicionalNoPlanoPorFormaContratacaoDTO()
                    {
                        IdServico = formaContratacao.IdServicoAdicional,
                        ExibirFluxoAssinatura = formaContratacao.ExibicaoAssinatura,
                        ExibirMeuPlano = formaContratacao.ExibicaoMeuPlano,
                        IdFormaDeContratacao = formaContratacao.Id,
                        Nome = formaContratacao.Nome,
                        ValorMensalSemDesconto = valorPorFaixa.Valor
                    });
                }
            }

            return valores;
        }

    }
}
