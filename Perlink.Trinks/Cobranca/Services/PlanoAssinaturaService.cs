﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using DL = Perlink.Trinks.Domain;

namespace Perlink.Trinks.Cobranca.Services
{

    public class PlanoAssinaturaService : BaseService, IPlanoAssinaturaService
    {

        #region Propriedades de Apoio

        private IPlanoAssinaturaRepository PlanoAssinaturaRepository { get { return DL.Cobranca.PlanoAssinaturaRepository; } }
        private IOfertaDeServicoAdicionalRepository ServicoDoPlanoAssinaturaRepository { get { return DL.Cobranca.OfertaDeServicoAdicionalRepository; } }
        private IValorPorFaixaRepository ValorPorFaixaRepository { get { return DL.Cobranca.ValorPorFaixaRepository; } }
        #endregion

        [TransactionInitRequired]
        public void Manter(PlanoAssinatura entity, Boolean ehEdicao)
        {
            var servicos = entity.ServicosDoPlano;
            var faixas = entity.ValoresPorFaixa;
            if (ehEdicao)
            {

                if (!entity.ExibirAoPublico && !ExistemOutrosPlanosExibidosAoPublicoSemSerEste(entity))
                    ValidationHelper.Instance.AdicionarItemValidacao("Não existem outros planos exibidos ao público sem ser este");
                if (entity.ExibirAoPublico && string.IsNullOrWhiteSpace(entity.Titulo))
                    ValidationHelper.Instance.AdicionarItemValidacao("Se o plano é xibido ao público é necessário preencher o nome que será exibido ao público.");

                if (ValidationHelper.Instance.IsValid)
                {
                    var servicoCadastradosNoPlano = Domain.Cobranca.OfertaDeServicoAdicionalRepository.ObterVinculadosAoPlano(entity.IdPlano);
                    servicoCadastradosNoPlano.ToList().ForEach(p => Domain.Cobranca.OfertaDeServicoAdicionalRepository.DeleteInt(p.Id));

                    var faixasCadastradasNoPlano = Domain.Cobranca.ValorPorFaixaRepository.ObterVinculadosAoPlano(entity.IdPlano);
                    faixasCadastradasNoPlano.ToList().ForEach(p => Domain.Cobranca.ValorPorFaixaRepository.DeleteInt(p.IdFaixa));

                    PlanoAssinaturaRepository.Update(entity);
                }
            }
            else
            {
                entity.ServicosDoPlano = null;
                entity.ValoresPorFaixa = null;

                PlanoAssinaturaRepository.SaveNew(entity);
            }

            if (ValidationHelper.Instance.IsValid)
            {
                foreach (var servico in servicos)
                {
                    servico.PlanoAssinatura = new PlanoAssinatura() { IdPlano = entity.IdPlano };
                    ServicoDoPlanoAssinaturaRepository.SaveNew(servico);
                }
                foreach (var faixa in faixas)
                {
                    faixa.PlanoAssinatura = new PlanoAssinatura() { IdPlano = entity.IdPlano };
                    ValorPorFaixaRepository.SaveNew(faixa);
                }
            }

        }

        private bool ExistemOutrosPlanosExibidosAoPublicoSemSerEste(PlanoAssinatura entity)
        {
            return Domain.Cobranca.PlanoAssinaturaRepository.Queryable().Any(p => p.IdPlano != entity.IdPlano && p.ExibirAoPublico && p.Ativo);
        }

        public List<ValorPorFaixaDoPlanoDTO> ListarValoresDeFaixasDoPlano(int idPlanoAssinatura)
        {
            return Domain.Cobranca.ValorPorFaixaRepository.Queryable()
                .Where(f => f.PlanoAssinatura.IdPlano == idPlanoAssinatura)
                .Select(f => new ValorPorFaixaDoPlanoDTO(f))
                .ToList();
        }

        public void AlterarPermissaoParaContratarAdicionaisPorFora(int idPlanoAssinatura)
        {
            var planoAssinatura = Domain.Cobranca.PlanoAssinaturaRepository.Load(idPlanoAssinatura);
            if (planoAssinatura == null)
                return;

            planoAssinatura.PermiteContratarAdicionaisPorFora = !planoAssinatura.PermiteContratarAdicionaisPorFora;
            Domain.Cobranca.PlanoAssinaturaRepository.Update(planoAssinatura);
        }
    }

}
