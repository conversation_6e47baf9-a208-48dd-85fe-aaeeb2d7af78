﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Pessoas;
using System.Collections.Generic;

namespace Perlink.Trinks.Cobranca.Stories
{
    public interface IMeuPlanoStory : IStory
    {
        DetalhesMinhaAssinaturaDTO ObterDetalhesDaAssinaturaDoEstabelecimento(Estabelecimento estabelecimento);
        void AtualizarAssinatura(Estabelecimento estabelecimento, int idPlanoAssinatura, List<ServicosParaContratarDTO> servicosParaContratar, PessoaFisica pessoaLogada);
        ValoresDaAssinaturaDoPlanoDTO ObterValoresDeContratacaoDoPlano(Estabelecimento estabelecimento, int idPlanoAssinatura);
        OfertaDeServicoAdicionalMeuPlanoDTO ObterOfertaDePlanoParaOEstabelecimento(Estabelecimento estabelecimento);
        OfertaDeServicoAdicionalMeuPlanoDTO ObterOfertaDePlanoParaOEstabelecimentoPorId(Estabelecimento estabelecimentoAutenticado, int idOferta);
        OfertaDeServicoAdicionalMeuPlanoDTO ObterOfertaDePlanoParaOEstabelecimentoPorAdicional(Estabelecimento estabelecimentoAutenticado, int idAdicional);
        List<ExibicaoDoAdicionalNoFluxoDeAtivacaoDTO> ObterDetalhesDosAdicionasContratadosComMesesGratisConsiderandoADataReferenciaDaUltimaFatura(Estabelecimento estabelecimento);
        void AtivarConfiguracaoAutomaticaDeTodosOsAdicionaisContratados(Estabelecimento estabelecimento);
        void AtivarConfiguracaoAutomaticaDoAdicionalContratado(int idAdicional, Estabelecimento estabelecimento);
        int ObterIdUltimaFaturaDoEstabelecimento(Estabelecimento estabelecimento);
        List<ConquistaDTO> ObterConquistasDoEstabelecimento(Estabelecimento estabelecimento);
        List<ExperienciaDTO> ObterExperienciasDoEstabelecimento(Estabelecimento estabelecimento);
        void EditarFormaDeContratacaoDeAdicional(EdicaoFormaDeContratacaoAdicionalMeuPlanoDTO edicaoFormaDeContratacaoAdicionalDTO);
    }
}
