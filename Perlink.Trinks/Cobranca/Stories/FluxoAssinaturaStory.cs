﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Validation;
using Perlink.GatewayPagamento;
using Perlink.GatewayPagamento.Enum;
using Perlink.Shared.Enums;
using Perlink.Trinks.Cobranca.ContratacaoDosAdicionais;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.Cobranca.Helpers;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cobranca.Stories
{

    public class FluxoAssinaturaStory : BaseStory, IFluxoAssinaturaStory
    {

        public DadosParaAssinaturaDTO ObterDadosParaEscolherPlanoAssinatura(Estabelecimento estabelecimento)
        {
            var contaAutenticada = Domain.Pessoas.ContaService.ObterContaAutenticada();
            var QuantidadeDeProfissionaisComAgenda = Domain.Pessoas.ProfissionalRepository.QuantidadeDeProfissionaisComServicosAtivos(estabelecimento.IdEstabelecimento);
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(estabelecimento.PessoaJuridica.IdPessoa);

            var planos = Domain.Cobranca.EscolhaDePlanoParaAssinaturaService.ListarPlanosDisponiveisParaAssinar(assinatura, estabelecimento);

            // Ordenando os planos -> Mensal, Semestral, Anual
            planos.Sort(delegate (PlanosParaAssinarDTO x, PlanosParaAssinarDTO y)
            {
                if (x.IdPlano > y.IdPlano) return 1;
                else return -1;
            });

            var contaFinanceira = assinatura.ContaFinanceira;

            string documento = estabelecimento.PessoaJuridica.ResponsavelFinanceiro.Cpf.Formatar("999.999.999-99");
            string nome = estabelecimento.PessoaJuridica.ResponsavelFinanceiro.NomeCompleto;
            if (!String.IsNullOrWhiteSpace(estabelecimento.PessoaJuridica.CNPJ) && !String.IsNullOrWhiteSpace(estabelecimento.PessoaJuridica.RazaoSocial))
            {
                documento = estabelecimento.PessoaJuridica.CNPJ.Formatar("99.999.999/9999-99");
                nome = estabelecimento.PessoaJuridica.RazaoSocial;
            }

            int parcelasDoPlanoMaisPopular = new ParametrosTrinks<int>(Pessoas.Enums.ParametrosTrinksEnum.parcelas_plano_com_selo_mais_popular_fluxo_assinatura).ObterValor();
            var detalhes = new DadosParaAssinaturaDTO
            {
                PlanosParaAssinar = planos,
                NomeUsuarioLogado = contaAutenticada.Pessoa.PessoaFisica.PrimeiroNomeOuApelido(),
                DiasRestantesDeGratuidade = assinatura.DiasRestantesDeDegustacao(Calendario.Hoje()),
                DiasRecebidosDeGratuidade = assinatura.DiasGratis,
                CodigoDaFaixaSugerida = Domain.Cobranca.EscolhaDePlanoParaAssinaturaService.ObterCodigoDaFaixaSugerida(faixas: planos.FirstOrDefault().ValoresPorFaixa, quantidadeProfissionaisComAgenda: QuantidadeDeProfissionaisComAgenda, faixaInformadaNoCadastro: estabelecimento.FaixaProfissionais),
                ParcelasDoPlanoComSeloMaisPopular = parcelasDoPlanoMaisPopular,
                IdPlanoMaisPopular = planos.Where(p => p.QuantidadeDeParcelas == parcelasDoPlanoMaisPopular).Min(p => (int?)p.IdPlano),
                DadosParaPagamentoComBoleto = new DadosParaPagamentoComBoletoDTO(
                    nome,
                    documento,
                    estabelecimento.PessoaJuridica.EnderecoProprio.ObterTextoEndereco(),
                    Domain.Cobranca.AssinaturaService.ObterPossiveisDiasVencimentoBoleto(estabelecimento),
                    Domain.Cobranca.AssinaturaService.ObterDataInicioProximoPeriodoSemFatura(contaFinanceira.AssinaturaAtiva())
                ),
                AdicionaisDisponiveis = CarregarDadosGeraisDosAdicionaisParaExibirNoFluxoDeAssinatura(estabelecimento, assinatura),
                StatusContaFinanceira = contaFinanceira.Status.IdStatus,
                CPFResponsavelNaoInformado = string.IsNullOrWhiteSpace(estabelecimento.ObterResponsavel().Cpf),
                CodigoParceriaAtiva = Domain.Cobranca.EstabelecimentoParceriasTrinksRepository.ObterParceriaAtiva(estabelecimento.IdEstabelecimento)?.Cupom,
                UrlCentralDeAjuda = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_central_ajuda).ObterValor(),
            };

            return detalhes;
        }

        private List<ExibicaoDoAdicionalNoFluxoDeAssinaturaDTO> CarregarDadosGeraisDosAdicionaisParaExibirNoFluxoDeAssinatura(Estabelecimento estabelecimento, Assinatura assinatura)
        {
            var lista = new List<ExibicaoDoAdicionalNoFluxoDeAssinaturaDTO>();

            var planoPadrao = Domain.Cobranca.EscolhaDePlanoParaAssinaturaService.ObterPlanoPadraoParaAssinatura(estabelecimento, assinatura.ContaFinanceira);
            var disponibilidadesDeContratacao = Domain.Cobranca.AdicionalNaAssinaturaService.ObterDisponibilidadesDeContratacoesDosAdicionais(estabelecimento, assinatura, planoPadrao);
            var adicionaisObrigatorios = ListarServicosAdicionaisQueDevemSerObrigatoriamenteContratadosAoAssinar(assinatura);
            var adicionaisAtivosAssinatura = Domain.Cobranca.AdicionalNaAssinaturaRepository.ObterAdicionaisAtivosNaAssinatura(assinatura);
            var formasContratacaoAdicional = Domain.Cobranca.FormaDeContratacaoDoAdicionalRepository.Queryable()
                .Where(formaContratacao => formaContratacao.ExibicaoAssinatura).ToList();

            foreach (var disponibilidade in disponibilidadesDeContratacao)
            {
                bool ehObrigatorioContratarAdicional = adicionaisObrigatorios.Select(a => a.IdServico).Any(s => s == disponibilidade.ServicoAdicional);

                if (!PodeExibirAdicionalNoFluxoDeAssinatura(estabelecimento, disponibilidade, ehObrigatorioContratarAdicional))
                    continue;

                var servico = disponibilidade.ServicoAdicional;

                var formasContratacao = formasContratacaoAdicional.Where(formaContratacao => formaContratacao.IdServicoAdicional == servico.IdServico)
                    .Select(formaContratacao => new FormaDeContratacaoDoAdicionalDTO()
                    {
                        Id = formaContratacao.Id,
                        IdServico = servico.IdServico,
                        TextoApresentacaoAssinatura = formaContratacao.TextoApresentacaoAssinatura,
                        TextoApresentacaoMeuPlano = formaContratacao.TextoApresentacaoMeuPlano,
                        IdObjetoAssociado = formaContratacao.IdObjetoAssociado,
                        Nome = formaContratacao.Nome,
                        HierarquiaContratacao = formaContratacao.HierarquiaContratacao,
                        EstaContratado = disponibilidade.JaEstaContratado && adicionaisAtivosAssinatura.Exists(aa => aa.Servico.IdServico == servico.IdServico &&
                        aa.FormaDeContratacao.Id == formaContratacao.Id),
                    }).ToList();

                var ofertaDTO = new ExibicaoDoAdicionalNoFluxoDeAssinaturaDTO
                {
                    IdServico = servico.IdServico,
                    NomeServico = servico.Nome,
                    TextoDeApresentacao = servico.TextoDeApresentacaoNoFluxoDeAssinatura,
                    URLImagemPrincipal = servico.ObterUrlCompletaDaImagemPrincipal(),
                    JaEstaContratado = disponibilidade.JaEstaContratado,
                    EhObrigatorioContratar = ehObrigatorioContratarAdicional,
                    ServicoAdicionalRequerido = disponibilidade.ServicoAdicionalRequerido,
                    FormasContratacao = formasContratacao,
                };

                lista.Add(ofertaDTO);
            }

            return lista;
        }

        private bool PodeExibirAdicionalNoFluxoDeAssinatura(Estabelecimento estabelecimento, DisponibilidadeDeContratacaoDoAdicionalDTO disponibilidade, bool ehObrigatorioContratar)
        {
            if (disponibilidade.JaEstaContratado || ehObrigatorioContratar)
                return true;

            if (estabelecimento.EhUmEstabelecimentoFranqueado() && !estabelecimento.FranquiaEstabelecimento.Franquia.PermiteContratarItensAdicionaisNoPlano)
                return false; // disponibilidade.JaEstaContratado;

            return disponibilidade.PodeContratar && disponibilidade.ServicoAdicional.ExibicaoNoFluxoAssinatura == true;
        }

        public CartaoCreditoBandeiraEnum? ConverterFormaDePagamentoParaBandeira(FormaDePagamentoEnum formaDePagamentoEnum)
        {
            return new FormaPagamento((int)formaDePagamentoEnum);
        }

        public DadosAposAssinarDTO RealizarAssinatura(DadosAoAssinarDTO dadosAoAssinar, Estabelecimento estabelecimento, PessoaFisica pessoaFisicaLogada)
        {
            IncluirServicosAdicionaisQueDevemSerContratadosObrigatoriamenteAoAssinar(dadosAoAssinar, estabelecimento);

            var retorno = new DadosAposAssinarDTO() { AssinaturaAprovada = false };
            var ipCliente = dadosAoAssinar.IpCliente;
            var fonteDePagamento = CarregarFonteDePagamento(dadosAoAssinar.DadosDaCobranca.IdFormaDePagamento, ConverterFormaDePagamentoParaBandeira((FormaDePagamentoEnum)dadosAoAssinar.DadosDaCobranca.IdFormaDePagamento), dadosAoAssinar.DadosDaCobranca.NumeroDoCartao, dadosAoAssinar.DadosDaCobranca.AnoDeValidadeDoCartao, dadosAoAssinar.DadosDaCobranca.CodigoDeSegurancaDoCartao, dadosAoAssinar.DadosDaCobranca.CpfDoTitular, dadosAoAssinar.DadosDaCobranca.NomeDoTitular, dadosAoAssinar.DadosDaCobranca.MesDeValidadeDoCartao, dadosAoAssinar.DadosDaCobranca.DiaVencimentoBoleto);
            var planoEscolhido = Domain.Cobranca.PlanoAssinaturaRepository.Load(dadosAoAssinar.IdPlanoEscolhido);
            var servicosAdicionaisContratados = new List<ServicoTrinksAContratarDTO>();
            var servicosAdicionaisValidados = new List<ServicoTrinksAContratarDTO>();
            var contratouAdicionais = dadosAoAssinar.IdsServicosAdicionais != null && dadosAoAssinar.IdsServicosAdicionais.Any();

            if (!planoEscolhido.PermiteContratarAdicionaisPorFora)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A assinatura atual não suporta a contratação de adicionais ou alteração do plano pela tela Meu Plano.");
            }

            if (planoEscolhido.DuracaoDoPlanoComFidelidade > 1 && FormaPagamentoHelper.TemProcessoAssincrono(fonteDePagamento.FormaPagamento))
            {
                ValidationHelper.Instance.AdicionarItemValidacao($"Esse desconto é válido apenas para quem paga com cartão de crédito.\nClique em 'Editar pagamento' e aproveite mais esse desconto no plano {planoEscolhido.ObterNomeDoPeriodoDoPlano().ToLower()} ;)");
            }

            if (contratouAdicionais)
            {
                foreach (var servicoParaContratar in dadosAoAssinar.IdsServicosAdicionais)
                {
                    var servicoAdicional = Domain.Cobranca.ServicoTrinksRepository.Load((int)servicoParaContratar.IdServico, false);
                    servicosAdicionaisContratados.Add(new ServicoTrinksAContratarDTO(servicoAdicional, servicoParaContratar.IdFormaContratacao));
                }

                var assinaturaVigente = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
                var contratacao = new DisponibilidadeDeContratacao(estabelecimento, assinaturaVigente);
                var servicosValidados = contratacao.ValidarContratacaoConjunta(servicosAdicionaisContratados, planoEscolhido, false);
                var servicosInvalidados = servicosAdicionaisContratados.Where(servico => !servicosValidados.Contains(servico)).ToList();
                contratacao.ValidarContratacoes(servicosInvalidados, planoEscolhido, false);
            }

            if (ValidationHelper.Instance.IsValid)
            {
                var faturaAposAssinatura = Domain.Cobranca.AssinaturaService.RealizarAssinatura(estabelecimento, pessoaFisicaLogada, fonteDePagamento,
                    ipCliente, Calendario.Agora(), planoEscolhido, dadosAoAssinar.IdFaixaEscolhida, servicosAdicionaisContratados);

                if (ValidationHelper.Instance.IsValid)
                {
                    Domain.Pessoas.ContaService.DefinirPermissoesDaContaAutenticadaComoDesatualizadas();
                    retorno.TipoDeFormaPagamento = fonteDePagamento.FormaPagamento.Tipo;

                    if (fonteDePagamento.FormaPagamento.Tipo == TipoFormaPagamentoEnum.CartaoDeCredito)
                    {
                        retorno = new DadosAposAssinarDTO
                        {
                            AssinaturaAprovada = faturaAposAssinatura.Status == StatusFaturaEnum.Paga,
                            IdStatusFatura = faturaAposAssinatura.Status.IdStatus,
                            NumeroFatura = faturaAposAssinatura.IdFatura,
                            StatusTransacao = faturaAposAssinatura.StatusTemporarioTransacaoGateway,
                            TidAprovacao = faturaAposAssinatura.IdTransacao,
                            MotivoCancelamentoTransacao = faturaAposAssinatura.MotivoTemporarioTransacaoGateway
                        };
                    }
                    else if (FormaPagamentoHelper.TemProcessoAssincrono(fonteDePagamento.FormaPagamento.Tipo))
                    {
                        retorno = new DadosAposAssinarDTO
                        {
                            AssinaturaAprovada = true,
                            IdStatusFatura = faturaAposAssinatura.Status.IdStatus,
                            NumeroFatura = faturaAposAssinatura.IdFatura
                        };
                    }

                    if (!retorno.AssinaturaAprovada && contratouAdicionais)
                    {
                        DescontratarAdicionaisNaAssinaturaAposTransacaoNaoTerSidoAprovado(estabelecimento, pessoaFisicaLogada);
                    }

                    if (retorno.AssinaturaAprovada && contratouAdicionais)
                    {
                        NotificarAtendimentoSobreAdicionaisContratadosCasoTransacaoTenhaSidoAprovada(dadosAoAssinar, estabelecimento, pessoaFisicaLogada);
                    }

                    if (retorno.AssinaturaAprovada)
                    {
                        var faturaTrinks = faturaAposAssinatura as FaturaTrinks;
                        Domain.Cobranca.ParceriaTrinksService.NotificarPromotorDoTrinksSobreIndicadoQueTevePrimeiraAssinaturaPaga(faturaTrinks);
                    }
                }
            }

            return retorno;
        }

        private void IncluirServicosAdicionaisQueDevemSerContratadosObrigatoriamenteAoAssinar(DadosAoAssinarDTO dadosAoAssinar, Estabelecimento estabelecimento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(estabelecimento.PessoaJuridica.IdPessoa);
            dadosAoAssinar.IdsServicosAdicionais.AddRange(ListarServicosAdicionaisQueDevemSerObrigatoriamenteContratadosAoAssinar(assinatura));
        }

        private void NotificarAtendimentoSobreAdicionaisContratadosCasoTransacaoTenhaSidoAprovada(DadosAoAssinarDTO dadosAoAssinar, Estabelecimento estabelecimento, PessoaFisica pessoaFisicaLogada)
        {
            var nomesAdicionaisContratados = dadosAoAssinar.IdsServicosAdicionais.Select(s => EnumActions.GetEnumText(s.IdServico)).ToList();
            Domain.Pessoas.EnvioEmailService.EnviarEmailAoAtendimentoParaAdicionalContratadoPeloEstabelecimento(estabelecimento.NomeDeExibicaoNoPortal,
                        nomesAdicionaisContratados, "Fluxo de Assinatura", pessoaFisicaLogada.NomeCompleto);
        }

        private void DescontratarAdicionaisNaAssinaturaAposTransacaoNaoTerSidoAprovado(Estabelecimento estabelecimento, PessoaFisica pessoaFisicaLogada)
        {
            // TODO:duvida Solução temporária - Criado a tarefa TRINKS-449 para resolver.
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            Domain.Cobranca.AdicionalNaAssinaturaService.CancelarTodosOsAdicionaisDaAssinatura(assinatura, pessoaFisicaLogada, "Outros", "Transação não aprovada durante tentativa de assinatura pela tela Fluxo Assinatura");
        }

        public FonteDePagamento CarregarFonteDePagamento(int idFormaDePagamento, CartaoCreditoBandeiraEnum? cartaoCreditoBandeira, string numeroDoCartao, int? anoDeValidadeDoCartao, string codigoDeSegurancaDoCartao, string cpfDoTitular, string nomeDoTitular, int? mesDeValidadeDoCartao, int diaVencimentoBoleto)
        {
            FonteDePagamento fonteDePagamento;

            var formaPagamento = Domain.Cobranca.FormaPagamentoRepository.Load(idFormaDePagamento);

            if (formaPagamento.Tipo.IdTipoFormaPagamento == (int)TipoFormaPagamentoEnum.CartaoDeCredito)
            {
                var bandeiraCartao = cartaoCreditoBandeira;
                var numeroDoCartaoSemEspacos = numeroDoCartao.Replace(" ", String.Empty);
                var anoDeValidadeCom2Digitos = anoDeValidadeDoCartao; //- 2000;

                var dadosCartao = new CartaoCredito
                {
                    Bandeira = bandeiraCartao.Value,
                    CodigoSeguranca = codigoDeSegurancaDoCartao,
                    Numero = numeroDoCartaoSemEspacos,
                    PortadorCPF = cpfDoTitular,
                    PortadorNome = nomeDoTitular,
                    ValidadeAno = anoDeValidadeCom2Digitos.Value,
                    ValidadeMes = mesDeValidadeDoCartao.Value
                };

                fonteDePagamento = new FonteDePagamentoCartao(formaPagamento) { DadosCartao = dadosCartao };
            }
            else
            {
                fonteDePagamento = new FonteDePagamentoAssincrona(formaPagamento) { DiaDeVencimento = diaVencimentoBoleto };
            }

            fonteDePagamento.FormaPagamento = formaPagamento;

            return fonteDePagamento;
        }

        public List<AdicionalNoFluxoDeAssinaturaDTO> ObterAdicionaisPorPlanoEQuantidadeDeProfissionais(Estabelecimento estabelecimento, int idPlanoAssinaura, int? minProfissionais, int? maxProfissionais)
        {
            // Buscar o min/max da faixa
            int qtdProfissionaisNaCobranca = minProfissionais ?? maxProfissionais ?? 1;
            qtdProfissionaisNaCobranca = qtdProfissionaisNaCobranca <= 0 ? 1 : qtdProfissionaisNaCobranca;

            var adicionaisDTO = new List<AdicionalNoFluxoDeAssinaturaDTO>();
            var planoAssinatura = Domain.Cobranca.PlanoAssinaturaRepository.Load(idPlanoAssinaura);
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(estabelecimento.PessoaJuridica.IdPessoa);
            var dadosAdicionais = Domain.Cobranca.AssinaturaService.ListarValoresDosAdicionaisConsiderandoContratacoesNaAssinatura(assinatura, idPlanoAssinaura, qtdProfissionaisNaCobranca);
            var disponibilidadesDeContratacao = Domain.Cobranca.AdicionalNaAssinaturaService.ObterDisponibilidadesDeContratacoesDosAdicionais(estabelecimento, assinatura, planoAssinatura);
            var adicionaisObrigatorios = ListarServicosAdicionaisQueDevemSerObrigatoriamenteContratadosAoAssinar(assinatura);

            foreach (var adicional in dadosAdicionais)
            {
                var disponibilidade = disponibilidadesDeContratacao.FirstOrDefault(d => d.ServicoAdicional.IdServico == adicional.IdServico);
                bool ehObrigatorioContratarAdicional = adicionaisObrigatorios.Select(a => a.IdServico).Any(s => s == disponibilidade.ServicoAdicional);

                if (disponibilidade != null && !PodeExibirAdicionalNoFluxoDeAssinatura(estabelecimento, disponibilidade, ehObrigatorioContratarAdicional))
                    continue;

                var adicionalModel = new AdicionalNoFluxoDeAssinaturaDTO
                {
                    IdServico = adicional.IdServico,
                    NomeServico = adicional.Nome,
                    Valor = adicional.ValorMensalSemDesconto,
                    ValorDeTaxaExtra = adicional.ValorDeTaxaExtra,
                    TituloDaTaxaExtra = adicional.TituloDaTaxaExtra,
                    ResumoDaTaxaExtra = adicional.ResumoDaTaxaExtra,
                    QuantidadeDeDescontosDisponiveis = adicional.QuantidadeDeDescontosDisponiveis,
                    PercentualDesconto = adicional.PercentualDesconto,
                    FormasContratacao = adicional.FormasContratacao.Where(fc => fc.ExibirFluxoAssinatura).ToList(),
                };

                adicionaisDTO.Add(adicionalModel);
            }

            return adicionaisDTO;
        }

        private List<ServicosParaContratarDTO> ListarServicosAdicionaisQueDevemSerObrigatoriamenteContratadosAoAssinar(Assinatura assinatura)
        {
            return ListarServicosAdicionaisQueFaziamParteDoPlanoAnterior(assinatura.PlanoAssinatura);
        }

        private List<ServicosParaContratarDTO> ListarServicosAdicionaisQueFaziamParteDoPlanoAnterior(PlanoAssinatura planoAnterior)
        {
            var servicosAdicionaisDoPlanoAntigo = new List<ServicosParaContratarDTO>();

            var ehPlanoDaEstruturaAntiga = !planoAnterior.PermiteContratarAdicionaisPorFora;

            if (ehPlanoDaEstruturaAntiga)
            {
                var nomePlanoAnterior = planoAnterior.Nome.ToLower();

                if (nomePlanoAnterior.Contains("fidelidade"))
                    servicosAdicionaisDoPlanoAntigo.Add(new ServicosParaContratarDTO(ServicoAdicionalEnum.ProgramaDeFidelidade, null));

                if (nomePlanoAnterior.Contains("nota de produto") || nomePlanoAnterior.Contains("nfc"))
                    servicosAdicionaisDoPlanoAntigo.Add(new ServicosParaContratarDTO(ServicoAdicionalEnum.NFCe, null));

                if (nomePlanoAnterior.Contains("nota de serviço") || nomePlanoAnterior.Contains("nfs"))
                    servicosAdicionaisDoPlanoAntigo.Add(new ServicosParaContratarDTO(ServicoAdicionalEnum.NFSe, null));

                if (nomePlanoAnterior.Contains("parceiro"))
                    servicosAdicionaisDoPlanoAntigo.Add(new ServicosParaContratarDTO(ServicoAdicionalEnum.ProfissionalParceiro, null));

                if (nomePlanoAnterior.Contains("belezinha"))
                    servicosAdicionaisDoPlanoAntigo.Add(new ServicosParaContratarDTO(ServicoAdicionalEnum.BelezinhaComSplit, null));

                if (nomePlanoAnterior.Contains("app"))
                    servicosAdicionaisDoPlanoAntigo.Add(new ServicosParaContratarDTO(ServicoAdicionalEnum.AppWhiteLabelExclusivo, null));
            }

            return servicosAdicionaisDoPlanoAntigo;
        }
    }
}