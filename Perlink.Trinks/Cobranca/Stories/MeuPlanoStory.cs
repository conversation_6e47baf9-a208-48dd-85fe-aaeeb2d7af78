﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Enums;
using Perlink.Trinks.Cobranca.ContratacaoDosAdicionais;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.Cobranca.Helpers;
using Perlink.Trinks.Cobranca.ObjectValues;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.InternoProduto.Enum;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cobranca.Stories
{

    public class MeuPlanoStory : BaseStory, IMeuPlanoStory
    {

        public DetalhesMinhaAssinaturaDTO ObterDetalhesDaAssinaturaDoEstabelecimento(Estabelecimento estabelecimento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            var planoAtual = Domain.Cobranca.AssinaturaService.ObterValoresParaContratacaoDoPlano(assinatura, assinatura.PlanoAssinatura.IdPlano);

            foreach (var adicional in planoAtual.ValoresDosAdicionais)
            {
                if (adicional.FormasContratacao != null && adicional.FormasContratacao.Count > 0)
                    adicional.FormasContratacao = adicional.FormasContratacao.Where(fc => fc.ExibirMeuPlano).ToList();
            }

            return new DetalhesMinhaAssinaturaDTO
            {
                IdEstabelecimentoAutenticado = estabelecimento.IdEstabelecimento,
                PrecisaRegularizarPagamentoParaConfigurarAdicionais = assinatura.EhInadimplenteForaDoPeriodoDeTolerancia(),
                NecessarioConfirmacaoAntesDeAtualizarAssinatura = new ParametrosTrinks<bool>(ParametrosTrinksEnum.necessario_confirmacao_antes_atualizar_assinatura_meu_plano).ObterValor(),
                PlanoAtual = planoAtual,
                FaixasDoPlanoAtual = assinatura.PlanoAssinatura.ValoresPorFaixa.Select(vp => new ValorPorFaixaDoPlanoDTO(vp)).ToList(),
                OpcoesDePlanosParaTroca = ListarOpcoesDePlanosDisponiveisParaTrocaNaAssinatura(assinatura),
                FaturasRestantesParaTerminarPeriodoAtual = assinatura.ObterQuantidadeDeFaturasRestantesParaTerminarPeriodoAtual(),
                DadosDosAdicionais = CarregarDadosGeraisDosAdicionaisParaExibirNoMeuPlano(estabelecimento, assinatura),
                StatusContaFinanceira = assinatura.ContaFinanceira.Status.IdStatus,
                DadosDoCancelamento = CarregarDadosDoNecessariosParaOCancelamentoDaAssinatura(estabelecimento)
            };
        }

        public List<ExibicaoDoAdicionalNoFluxoDeAtivacaoDTO> ObterDetalhesDosAdicionasContratadosComMesesGratisConsiderandoADataReferenciaDaUltimaFatura(Estabelecimento estabelecimento)
        {
            return Domain.Cobranca.AdicionalNaAssinaturaService.ObterDetalhesDosAdicionasContratadosComMesesGratisConsiderandoADataReferenciaDaUltimaFatura(estabelecimento);
        }

        public void AtualizarAssinatura(Estabelecimento estabelecimento, int idPlanoAssinatura, List<ServicosParaContratarDTO> servicosParaContratar, PessoaFisica pessoaLogada)
        {
            bool trocouPlano = false;
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            var planoSelecionado = Domain.Cobranca.PlanoAssinaturaRepository.ObterPorId(idPlanoAssinatura);
            var servicosAdicionaisContratados = new List<ServicoTrinksAContratarDTO>();
            var contratouAdicionais = servicosParaContratar != null && servicosParaContratar.Any();

            #region Validações

            if (!assinaturaAtiva.PlanoAssinatura.PermiteContratarAdicionaisPorFora)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A assinatura atual não suporta a contratação de adicionais ou alteração do plano pela tela Meu Plano.");
                return;
            }

            if (planoSelecionado.DuracaoDoPlanoComFidelidade > 1 && !FormaPagamentoHelper.EhCartaoDeCredito(assinaturaAtiva.FormaPagamento))
            {
                ValidationHelper.Instance.AdicionarItemValidacao($"Esse desconto é válido apenas para quem paga com cartão de crédito.\nClique em 'Editar pagamento' e aproveite mais esse desconto no plano {planoSelecionado.ObterNomeDoPeriodoDoPlano().ToLower()} ;)");
                return;
            }

            Domain.Cobranca.AssinaturaService.ValidarTrocaDoPlanoDeAssinatura(assinaturaAtiva, planoSelecionado, estabelecimento, ref trocouPlano);

            if (contratouAdicionais)
            {
                foreach (var servicoParaContratar in servicosParaContratar)
                {
                    var servicoAdicional = Domain.Cobranca.ServicoTrinksRepository.Load((int)servicoParaContratar.IdServico, false);
                    servicosAdicionaisContratados.Add(new ServicoTrinksAContratarDTO(servicoAdicional, servicoParaContratar.IdFormaContratacao));
                }

                var contratacao = new DisponibilidadeDeContratacao(estabelecimento, assinaturaAtiva);
                contratacao.ValidarContratacoes(servicosAdicionaisContratados, planoSelecionado, false);
            }

            #endregion Validações

            if (ValidationHelper.Instance.IsValid)
            {
                if (trocouPlano)
                {
                    Domain.Cobranca.AssinaturaService.TrocarPlanoDaAssinatura(assinaturaAtiva, planoSelecionado, pessoaLogada);
                }

                if (contratouAdicionais)
                {
                    Domain.Cobranca.AssinaturaService.ContratarAdicionaisNaAssinatura(servicosAdicionaisContratados, assinaturaAtiva, pessoaLogada, "Contratação realizada pela tela Meu Plano");
                }

                Domain.Pessoas.ContaService.DefinirPermissoesDaContaAutenticadaComoDesatualizadas();

                if (contratouAdicionais)
                {
                    NotificarAtendimentoSobreAdicionaisContratadosCasoTransacaoTenhaSidoAprovada(estabelecimento, servicosParaContratar, pessoaLogada);
                }
            }
        }

        public void EditarFormaDeContratacaoDeAdicional(EdicaoFormaDeContratacaoAdicionalMeuPlanoDTO dados)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(dados.IdPessoaDoEstabelemento);
            var contaAutenticada = Domain.Pessoas.ContaService.ObterContaAutenticada();
            var adicionalNaAssinatura = Domain.Cobranca.AdicionalNaAssinaturaService.ObterAtivoPorServicoAdicional(assinatura.IdAssinatura, dados.ServicoAdicional);

            var motivo = new MotivoHistoricoDoAdicional("Outros", "Alteração de forma de contratação de adicional pelo Meu plano.");
            Domain.Cobranca.AdicionalNaAssinaturaService.TrocarFormaDeContratacao(adicionalNaAssinatura, (int)dados.FormaDeContratacao, contaAutenticada.Pessoa.PessoaFisica, motivo);
        }

        public ValoresDaAssinaturaDoPlanoDTO ObterValoresDeContratacaoDoPlano(Estabelecimento estabelecimento, int idPlanoAssinatura)
        {
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            var valoresParaContratacaoPlano = Domain.Cobranca.AssinaturaService.ObterValoresParaContratacaoDoPlano(assinaturaAtiva, idPlanoAssinatura);

            foreach (var adicional in valoresParaContratacaoPlano.ValoresDosAdicionais)
            {
                if (adicional.FormasContratacao != null && adicional.FormasContratacao.Count > 0)
                    adicional.FormasContratacao = adicional.FormasContratacao.Where(formaContratacao => formaContratacao.ExibirMeuPlano).ToList();
            }

            return valoresParaContratacaoPlano;
        }

        public OfertaDeServicoAdicionalMeuPlanoDTO ObterOfertaDePlanoParaOEstabelecimento(Estabelecimento estabelecimento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            var oferta = Domain.Cobranca.OfertaDeServicoAdicionalMeuPlanoService.ListarOfertaDisponivelNoPlanoDeAssinatura(assinatura);
            if (oferta == null) return null;

            return ToOfertaDeAdicionalParaOPlanoDTO(estabelecimento, oferta, new ParametrosTrinks<int>(ParametrosTrinksEnum.qtd_dias_para_exibir_card_de_oferta_novamente).ObterValor());
        }

        public OfertaDeServicoAdicionalMeuPlanoDTO ObterOfertaDePlanoParaOEstabelecimentoPorId(Estabelecimento estabelecimento, int idOferta)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            var oferta = Domain.Cobranca.OfertaDeServicoAdicionalMeuPlanoService.ObterOfertaDePlanoParaOEstabelecimentoPorId(assinatura, idOferta);
            if (oferta == null) return null;

            return ToOfertaDeAdicionalParaOPlanoDTO(estabelecimento, oferta, new ParametrosTrinks<int>(ParametrosTrinksEnum.qtd_dias_para_exibir_card_de_oferta_novamente).ObterValor());
        }

        public OfertaDeServicoAdicionalMeuPlanoDTO ObterOfertaDePlanoParaOEstabelecimentoPorAdicional(Estabelecimento estabelecimento, int idAdicional)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            var oferta = Domain.Cobranca.OfertaDeServicoAdicionalMeuPlanoService.ObterOfertaDePlanoParaOEstabelecimentoPorAdicional(assinatura, idAdicional);
            if (oferta == null) return null;

            return ToOfertaDeAdicionalParaOPlanoDTO(estabelecimento, oferta, new ParametrosTrinks<int>(ParametrosTrinksEnum.qtd_dias_para_exibir_card_de_oferta_novamente).ObterValor());
        }

        public void AtivarConfiguracaoAutomaticaDeTodosOsAdicionaisContratados(Estabelecimento estabelecimento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            Domain.Cobranca.AdicionalNaAssinaturaService.AtivarConfiguracaoAutomaticaDeTodosOsAdicionaisContratados(assinatura);
        }

        public void AtivarConfiguracaoAutomaticaDoAdicionalContratado(int idAdicional, Estabelecimento estabelecimento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
            Domain.Cobranca.AdicionalNaAssinaturaService.AtivarConfiguracaoAutomaticaDoAdicionalContratado(idAdicional, assinatura);
        }
        public int ObterIdUltimaFaturaDoEstabelecimento(Estabelecimento estabelecimento)
        {
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(contaFinanceira);
            return Domain.Cobranca.FaturaRepository.Queryable()
                .OrderByDescending(f => f.DataEmissao)
                .Where(f => f.Ativo && f.Assinatura == assinatura)
                .Select(f => f.IdFatura)
                .FirstOrDefault();
        }

        public List<ConquistaDTO> ObterConquistasDoEstabelecimento(Estabelecimento estabelecimento)
        {
            var assinatura = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);

            var dadosDosAdicionais = CarregarDadosGeraisDosAdicionaisParaExibirNoMeuPlano(estabelecimento, assinatura);

            var adicionaisContratados = dadosDosAdicionais.Where(x => x.EstaContratado).ToList();

            return adicionaisContratados.Select(a => ConquistaDTO.Create(a.IdServico)).Where(x => x != null).ToList();
        }

        public List<ExperienciaDTO> ObterExperienciasDoEstabelecimento(Estabelecimento estabelecimento)
        {
            return new[] {
                ObterExperienciaTempoDeAssinatura(estabelecimento),
                ObterExperienciaQuantidadeDeAgendamentosOnline(estabelecimento)
            }.Where(x => x != null).ToList();
        }

        #region Métodos Privados

        private ExperienciaDTO ObterExperienciaTempoDeAssinatura(Estabelecimento estabelecimento)
        {
            if (estabelecimento.PessoaJuridica.DataCadastro == null) return null;

            var tempoDeCadastro = DateTime.Now - estabelecimento.PessoaJuridica.DataCadastro.Value;
            var meses = tempoDeCadastro.Days / 30;
            if (meses == 0) return null;
            var textoMeses = meses > 1 ? "meses" : "mês";
            return new ExperienciaDTO
            {
                ImgUrl = "~/Content/img/meu-plano/experiencias/familia-trinks.png",
                Texto = $"Há {meses} {textoMeses} você faz parte da família Trinks."
            };

        }

        private ExperienciaDTO ObterExperienciaQuantidadeDeAgendamentosOnline(Estabelecimento estabelecimento)
        {
            var quantidadeAgendamentosOnline = Domain.Pessoas.HorarioService.ContarAgendamentosOnline(estabelecimento.IdEstabelecimento);

            if (quantidadeAgendamentosOnline == 0) return null;
            var textoAgendamentos = quantidadeAgendamentosOnline > 1 ? "agendamentos" : "agendamento";
            return new ExperienciaDTO
            {
                ImgUrl = "~/Content/img/meu-plano/experiencias/agendamentos-online.png",
                Texto = $"Você conseguiu {quantidadeAgendamentosOnline} {textoAgendamentos} online."
            };
        }

        private void NotificarAtendimentoSobreAdicionaisContratadosCasoTransacaoTenhaSidoAprovada(Estabelecimento estabelecimento, List<ServicosParaContratarDTO> servicosParaContratar, PessoaFisica pessoaLogada)
        {
            var nomesAdicionaisContratados = servicosParaContratar.Select(s => EnumActions.GetEnumText(s.IdServico)).ToList();
            Domain.Pessoas.EnvioEmailService.EnviarEmailAoAtendimentoParaAdicionalContratadoPeloEstabelecimento(estabelecimento.NomeDeExibicaoNoPortal,
                        nomesAdicionaisContratados, "Meu Plano", pessoaLogada.NomeCompleto);
        }

        private OfertaDeServicoAdicionalMeuPlanoDTO ToOfertaDeAdicionalParaOPlanoDTO(Estabelecimento estabelecimento, OfertaDeServicoAdicionalMeuPlano oferta, int reexibirCardEmXDias)
        {
            return new OfertaDeServicoAdicionalMeuPlanoDTO()
            {
                IdOfertaDeAdicional = oferta.IdOferta,
                IdPlanoAssinatura = oferta.PlanoAssinatura.IdPlano,
                IdEstabelecimento = estabelecimento.IdEstabelecimento,
                IdServico = oferta.Servico?.IdServico,
                SelecionarOfertaAutomaticamente = (int)oferta.SelecionarOfertaAutomaticamente,
                TextoBotaoCTA = oferta.TextoBotaoCTA,
                TextoCTA = oferta.TextoCTA,
                TituloCTA = oferta.TituloCTA,
                ReExibirCardEmXDias = reexibirCardEmXDias
            };
        }

        private List<KeyValuePair<int, int>> ListarOpcoesDePlanosDisponiveisParaTrocaNaAssinatura(Assinatura assinatura)
        {
            if (!assinatura.PodeTrocarDePlanoNoMomento()) return new List<KeyValuePair<int, int>>();

            var planos = Domain.Cobranca.PlanoAssinaturaRepository
                .ObterAtivoPorIdPlanoOuIdPlanoPai(assinatura.PlanoAssinatura.IdPlano)
                .Where(pl => pl.IdPlano != assinatura.PlanoAssinatura.IdPlano);

            if (!string.IsNullOrEmpty(assinatura.PlanoAssinatura.TituloCiclo))
            {
                planos = planos
                    .Where(pl => pl.TituloCiclo == assinatura.PlanoAssinatura.TituloCiclo);
            }

            return planos
                .Select(pl => new KeyValuePair<int, int>(pl.IdPlano, pl.QuantidadeDeParcelas))
                .ToList();
        }

        private List<ExibicaoDoAdicionalMeuPlanoDTO> CarregarDadosGeraisDosAdicionaisParaExibirNoMeuPlano(Estabelecimento estabelecimento, Assinatura assinatura)
        {
            var lista = new List<ExibicaoDoAdicionalMeuPlanoDTO>();

            var disponibilidadesDeContratacao = Domain.Cobranca.AdicionalNaAssinaturaService.ObterDisponibilidadesDeContratacoesDosAdicionais(estabelecimento, assinatura, assinatura.PlanoAssinatura);
            var adicionaisAtivosAssinatura = Domain.Cobranca.AdicionalNaAssinaturaRepository.ObterAdicionaisAtivosNaAssinatura(assinatura);
            var formasContratacaoAdicional = Domain.Cobranca.FormaDeContratacaoDoAdicionalRepository.Queryable()
                .Where(formaContratacao => formaContratacao.ExibicaoMeuPlano).ToList();

            foreach (var disponibilidade in disponibilidadesDeContratacao)
            {
                if (!PodeExibirAdicionalNaTelaMeuPlano(estabelecimento, disponibilidade))
                    continue;

                if (!PodeExibirAdicionalDoClubeDeAssinaturas(disponibilidade, adicionaisAtivosAssinatura))
                    continue;

                var servico = disponibilidade.ServicoAdicional;
                var formasContratacao = formasContratacaoAdicional.Where(formaContratacao => formaContratacao.IdServicoAdicional == servico.IdServico)
                    .Select(formaContratacao => new FormaDeContratacaoDoAdicionalDTO()
                    {
                        Id = formaContratacao.Id,
                        IdServico = servico.IdServico,
                        TextoApresentacaoAssinatura = formaContratacao.TextoApresentacaoAssinatura,
                        TextoApresentacaoMeuPlano = formaContratacao.TextoApresentacaoMeuPlano,
                        IdObjetoAssociado = formaContratacao.IdObjetoAssociado,
                        Nome = formaContratacao.Nome,
                        HierarquiaContratacao = formaContratacao.HierarquiaContratacao,
                        EstaContratado = disponibilidade.JaEstaContratado && adicionaisAtivosAssinatura.Exists(aa => aa.Servico.IdServico == servico.IdServico &&
                        aa.FormaDeContratacao.Id == formaContratacao.Id),
                    }).ToList();

                var ofertaDTO = new ExibicaoDoAdicionalMeuPlanoDTO
                {
                    IdServico = servico.IdServico,
                    NomeServico = servico.Nome,
                    EstaContratado = disponibilidade.JaEstaContratado,
                    PermiteContratar = disponibilidade.PodeContratar && servico.ExibicaoNoMeuPlano == ExibicaoNoMeuPlanoEnum.Adicionar,
                    TextoDeApresentacao = servico.TextoDeApresentacaoNoMeuPlano,
                    TextoDaConfiguracaoAutomatica = servico.TextoDaConfiguracaoAutomatica,
                    ExibePrecoAoPublico = servico.ExibePrecoAoPublico,
                    LinkParaTelaDeConfiguracao = servico.ObterUrlCompletaParaTelaDeConfiguracao(),
                    LinkParaPopUpDeConfiguracao = servico.CaminhoRelativoParaPopUpDeConfiguracao,
                    URLImagemPrincipal = servico.ObterUrlCompletaDaImagemPrincipal(),
                    NomeDoArquivoHtmlSaibaMais = ObterNomeDoArquivoHtmlSaibaMais(estabelecimento, servico),
                    PastaDoConteudoSaibaMais = servico.ObterNomeDaPastaQuePossuiConteudoDoSaibaMais(),
                    OrdemDeExibicao = servico.OrdemDeExibicaoParaContratacao,
                    ServicoAdicionalRequerido = disponibilidade.ServicoAdicionalRequerido,
                    QuestionarioParaEntenderInteressePeloAdicionalEstaAtivo = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.QuestionarioParaEntenderInteressePeloAdicionalEstaAtivo).EstaDisponivel,
                    IdDoQuestionarioParaEntenderInteressePeloAdicional = Convert.ToInt32(Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("id_do_questionario_para_entender_o_interesse_pelo_adicional_" + servico.IdServico)?.Valor ?? null),
                    FormasContratacao = formasContratacao,
                    IdFormaContratacaoPadrao = servico.FormaContratacaoPadrao?.Id,
                };

                lista.Add(ofertaDTO);
            }

            return lista;
        }

        private string ObterNomeDoArquivoHtmlSaibaMais(Estabelecimento estabelecimento, ServicoTrinks servico)
        {
            if (VideoNoSaibaMaisDoAdicionalEstaDisponivel(estabelecimento, (ServicoAdicionalEnum)servico.IdServico))
                return servico.ObterNomeDoArquivoHtmlConteudoSaibaMaisComVideo();

            return servico.ObterNomeDoArquivoHtmlConteudoSaibaMais();
        }

        private bool VideoNoSaibaMaisDoAdicionalEstaDisponivel(Estabelecimento estabelecimento, ServicoAdicionalEnum servicoAdicionalEnum)
        {
            var estaDisponivel = false;

            switch (servicoAdicionalEnum)
            {
                case ServicoAdicionalEnum.ServicoPadrao:
                    break;
                case ServicoAdicionalEnum.LembretePremium:
                    break;
                case ServicoAdicionalEnum.ProgramaDeFidelidade:
                    break;
                case ServicoAdicionalEnum.NFCe:
                    break;
                case ServicoAdicionalEnum.NFSe:
                    estaDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                   estabelecimento, new Recurso(RecursoEnum.Saiba_Mais_NFS_Video, null)).EstaDisponivel;
                    break;
                case ServicoAdicionalEnum.ProfissionalParceiro:
                    break;
                case ServicoAdicionalEnum.BelezinhaSemSplit:
                    break;
                case ServicoAdicionalEnum.BelezinhaComSplit:
                    break;
                case ServicoAdicionalEnum.AppWhiteLabelExclusivo:
                    break;
                case ServicoAdicionalEnum.AppExclusivoNovo:
                    break;
                case ServicoAdicionalEnum.Pacote2000SMSMarketing:
                    break;
                default:
                    break;
            }

            return estaDisponivel;
        }

        private bool PodeExibirAdicionalNaTelaMeuPlano(Estabelecimento estabelecimento, DisponibilidadeDeContratacaoDoAdicionalDTO disponibilidade)
        {
            if (disponibilidade.ServicoAdicional.IdRecurso.HasValue)
            {
                var recurso = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                    .ObterRecursoPorId(disponibilidade.ServicoAdicional.IdRecurso.Value, disponibilidade.ServicoAdicional.IdServico);
                var disponibilidadeDoRecurso = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, recurso);
                return disponibilidadeDoRecurso == DisponibilidadeDoRecurso.IndisponivelNaoContratado || disponibilidadeDoRecurso.EstaDisponivel;
            }

            if (disponibilidade.OcultoParaOEstabelecimento)
                return false;

            if (estabelecimento.EhUmEstabelecimentoFranqueado() && !estabelecimento.FranquiaEstabelecimento.Franquia.PermiteContratarItensAdicionaisNoPlano)
            {
                return disponibilidade.JaEstaContratado;
            }
            else
            {
                return disponibilidade.JaEstaContratado || disponibilidade.ServicoAdicional.ExibicaoNoMeuPlano != ExibicaoNoMeuPlanoEnum.NaoExibir;
            }
        }

        private static bool PodeExibirAdicionalDoClubeDeAssinaturas(DisponibilidadeDeContratacaoDoAdicionalDTO disponibilidade, List<AdicionalNaAssinatura> adicionaisAtivosAssinatura)
        {
            return !(disponibilidade.JaEstaContratado
                && disponibilidade.ServicoAdicional.IdServico == (int)ServicoAdicionalEnum.ClubeDeAssinaturas
                && adicionaisAtivosAssinatura.Exists(aa => aa?.FormaDeContratacao?.Id == (int)FormaDeContratacaoDoAdicionalEnum.GestaoAntiga));
        }

        private DadosNecessariosParaOCancelamentoDTO CarregarDadosDoNecessariosParaOCancelamentoDaAssinatura(Estabelecimento estabelecimento)
        {
            var dadosCancelamento = new DadosNecessariosParaOCancelamentoDTO();
            var ehPermitidoCancelar = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento.IdEstabelecimento, Recurso.SolicitarCancelamentoDaAssinaturaPeloMeuPlano).EstaDisponivel;
            if (ehPermitidoCancelar)
            {
                dadosCancelamento.EhPermitidoCancelar = ehPermitidoCancelar;
                dadosCancelamento.JaExisteSolicitacaoEmAndamento = Domain.Cobranca.CancelamentoDaAssinaturaStory.JaExisteSolicitacaoDeCancelamentoEmAndamento(estabelecimento);
                dadosCancelamento.IdentificadorDoQuestionarioDeCancelamento = IdentificadorQuestionarioEnum.retencao_cancelamento.ToString();
                dadosCancelamento.MensagemSolicitacaoFoiRecebida = new ParametrosTrinks<string>(ParametrosTrinksEnum.conteudo_da_popup_solicitacao_de_cancelamento_da_assinatura_recebida).ObterValor();
                dadosCancelamento.MensagemSolicitacaoEmAndamento = new ParametrosTrinks<string>(ParametrosTrinksEnum.conteudo_da_popup_solicitacao_de_cancelamento_da_assinatura_em_andamento).ObterValor();
            }
            return dadosCancelamento;
        }
        #endregion


    }
}