﻿using Perlink.Trinks.ClubeDeAssinaturas;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.ImportacaoDeDados.TiposDeColuna;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.ImportacaoDeDados.Importadores
{
    public class ImportadorClubeAssinatura : Importador
    {

        public ImportadorClubeAssinatura() { }

        public override void Configurar(Estabelecimento estabelecimento)
        {
            Configuracao = new ConfiguracaoImportador
            {
                IdEstabelecimento = estabelecimento.IdEstabelecimento,
                Colunas = new List<Coluna> {
                    new ColunaTextoLivre(1, "Nome da assinatura", obrigatorio: true, nomePropriedadeNoDTO: nameof(ClubeAssinaturaDTO.NomeDoPlano), valorDeExemplo: "Plano teste"),
                    new ColunaCPF(2, "CPF", obrigatorio: true, nomePropriedadeNoDTO: nameof(ClubeAssinaturaDTO.Cpf)),
                    new ColunaEmail(3, "E-mail", obrigatorio: true, nomePropriedadeNoDTO: nameof(ClubeAssinaturaDTO.Email)),
                    new ColunaData(4, "Data do pagamento", obrigatorio: true, nomePropriedadeNoDTO: nameof(ClubeAssinaturaDTO.DataDoPagamento)),
                },
                PermiteAtualizarRegistrosExistentes = false,
                PermiteAgendarImportacaoParaUnidadesVinculadas = false,
                ExplicacaoAtualizacaoDosRegistros = ""
            };

            IdEstabelecimento = estabelecimento.IdEstabelecimento;
        }

        protected override int? Importar(RegistroParaImportar registro, MapeamentoDoArquivo mapeamento)
        {

            var pessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Load(IdPessoaAutenticada);
            var dto = ConverterPara<ClubeAssinaturaDTO>(registro, mapeamento);

            if (dto.DataDoPagamento > Calendario.Hoje())
            {
                throw new Exception("Não é possível fazer importações para datas futuras.");
            }

            var assinaturaDTO = ObterAssinatura(dto);

            int? idFaturaPaga = null;

            var confirmarPagamentoDTO = new ConfirmarPagamentoDTO
            {
                Assinatura = assinaturaDTO.Assinatura,
                DataDoPagamento = dto.DataDoPagamento,
                PessoaQueRealizou = pessoaFisica,
                EhPrimeiroPagamento = assinaturaDTO.EhPrimeiroPagamento,
                EhImportacao = true,
            };

            Task.WhenAll(Domain.ClubeDeAssinaturas.PagamentosService.ConfirmarPagamentoAsync(confirmarPagamentoDTO))
                .ContinueWith(pagamentoTask =>
                {
                    idFaturaPaga = pagamentoTask.Result[0];
                });

            return idFaturaPaga;
        }

        private AssinaturaImportadorDTO ObterAssinatura(ClubeAssinaturaDTO dto)
        {
            var idPessoafisicaCliente = ObterIdPessoaFisicaCliente(dto.Cpf, dto.Email);

            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterAssinaturaDoClienteQuePodeConsumirItens(idPessoafisicaCliente, dto.NomeDoPlano);

            var ehPrimeiroPagamento = false;

            if (assinatura == null)
            {
                assinatura = AssinarPlano(dto.NomeDoPlano, idPessoafisicaCliente, dto.DataDoPagamento);
                ehPrimeiroPagamento = true;
            }

            return new AssinaturaImportadorDTO
            {
                Assinatura = assinatura,
                EhPrimeiroPagamento = ehPrimeiroPagamento,
            };
        }

        private AssinaturaDoCliente AssinarPlano(string nomeDoPlano, int idPessoafisicaCliente, DateTime dataPagamento)
        {
            var plano = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterPlanoAtivoPorNome(nomeDoPlano, IdEstabelecimento);

            if (plano == null)
            {
                throw new Exception("Esta assinatura não existe ou não está ativa.");
            }

            var idAssinatura = Domain.ClubeDeAssinaturas.VendaDeAssinaturaService.AssinarPlano(
                new AssinaturaDoPlanoDTO
                {
                    IdDoPlano = plano.Id,
                    IdPessoaFisicaCliente = idPessoafisicaCliente,
                    DataDaAssinatura = dataPagamento,
                }
            );

            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPorIdAssinatura((int)idAssinatura);

            return assinatura;
        }

        private int ObterIdPessoaFisicaCliente(string cpf, string email)
        {
            Cliente cliente = null;

            if (!string.IsNullOrWhiteSpace(cpf))
            {
                var obterPorCpf = Domain.Pessoas.ClienteRepository.ObterPorCpf(cpf);

                if (obterPorCpf != null)
                    cliente = obterPorCpf;
            }

            if (cliente == null && !string.IsNullOrWhiteSpace(email))
            {
                var obterPorEmail = Domain.Pessoas.ClienteRepository.ObterPorEmail(email);

                if (obterPorEmail != null)
                    cliente = obterPorEmail;
            }

            if (cliente == null)
            {
                throw new Exception("CPF e/ou e-mail não encontrados.");
            }

            return cliente.PessoaFisica.IdPessoa;
        }
    }

    public class ClubeAssinaturaDTO
    {
        public string NomeDoPlano { get; set; }
        public string Cpf { get; set; }
        public string Email { get; set; }
        public DateTime DataDoPagamento { get; set; }
    }
}
