﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.Text;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.VO;
using System.IO;

namespace Perlink.Trinks.ImportacaoDeDados.Services
{
    public class ModeloDeImportacaoService : BaseService, IModeloDeImportacaoService
    {

        public MemoryStream BaixarArquivo(Estabelecimento estabelecimento, TipoImportacao tipo)
        {

            var importador = Domain.ImportacaoDeDados.OpcoesDaImportacaoService.ObterImportadorConfigurado(estabelecimento, tipo);
            var configuracaoDoImportador = importador.ObterConfiguracao();

            return GerarAquivoCsv(configuracaoDoImportador);
        }

        private MemoryStream GerarAquivoCsv(ConfiguracaoImportador configuracaoDoImportador)
        {
            var generos = Genero.ObterTodos();

            var csv = new Csv();

            var cabecalho = new Csv.Linha();
            foreach (var coluna in configuracaoDoImportador.Colunas)
            {
                cabecalho.AdicionarCelula(coluna.Nome);
            }
            csv.AdicionarLinha(cabecalho);

            foreach (var genero in generos)
            {
                var linha = new Csv.Linha();

                foreach (var coluna in configuracaoDoImportador.Colunas)
                {
                    string valor = string.Empty;

                    switch (coluna.Nome.ToLower())
                    {
                        case "nome":
                            valor = genero.Codigo() == Genero.NaoInformado.Codigo()
                                ? $"Cliente {genero.Descricao()} ou não preenchido"
                                : $"Cliente {genero.Descricao()}";
                            break;
                        case "gênero":
                            valor = genero.Codigo();
                            break;
                        default:
                            valor = string.Empty;
                            break;
                    }

                    linha.AdicionarCelula(valor);
                }

                csv.AdicionarLinha(linha);
            }

            return csv.ObterArquivo();
        }


    }
}
