﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ImportacaoDeDados.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;

namespace Perlink.Trinks.ImportacaoDeDados.Services
{
    public class AgendamentoDeImportacaoService : BaseService, IAgendamentoDeImportacaoService
    {

        public void AgendarImportacaoPelaAreaPerlink(AgendamentoDeImportacaoDTO agendamentoDeImportacaoDTO)
        {
            AgendarImportacao(agendamentoDeImportacaoDTO);
        }

        public void AgendarImportacaoPeloBackoffice(AgendamentoDeImportacaoDTO agendamentoDeImportacaoDTO)
        {
            AgendarImportacao(agendamentoDeImportacaoDTO);
        }

        private void AgendarImportacao(AgendamentoDeImportacaoDTO agendamentoDeImportacaoDTO)
        {

            var arquivo = agendamentoDeImportacaoDTO.DadosParaImportacao.ToArquivoDeImportacao();
            var nomeArquivo = Domain.ImportacaoDeDados.ArmazenamentoDeArquivoService.GerarNomeDoArquivo();
            Domain.ImportacaoDeDados.ArmazenamentoDeArquivoService.SalvarArquivo(arquivo, nomeArquivo);

            var qtdRegistrosParaImportar = arquivo.ObterQuantidadeDeRegistros();
            var solicitacao = new SolicitacaoDeImportacao(agendamentoDeImportacaoDTO.IdEstabelecimento, agendamentoDeImportacaoDTO.IdPessoa, agendamentoDeImportacaoDTO.OrigemSolicitacao, agendamentoDeImportacaoDTO.DadosParaImportacao.TipoImportacao, agendamentoDeImportacaoDTO.Email, nomeArquivo, qtdRegistrosParaImportar);
            Domain.ImportacaoDeDados.SolicitacaoDeImportacaoRepository.SaveNew(solicitacao);
        }

        public void ExecutarImportacoesNaFila()
        {
            if (!RotinaHabilitada())
                return;

            // Process all scheduled requests until none remain
            while (true)
            {
                var solicitacao = Domain.ImportacaoDeDados.SolicitacaoDeImportacaoRepository.ObterPrimeiraAgendada();

                // Exit the loop when no more scheduled requests are found
                if (solicitacao == null)
                    break;

                Domain.ImportacaoDeDados.ImportacaoDeArquivoService.ExecutarImportacao(solicitacao);
            }
        }

        private bool RotinaHabilitada()
        {
            return new ParametrosTrinks<bool>(ParametrosTrinksEnum.rotina_importacao_backoffice_esta_habilitada).ObterValor();
        }

        public void CancelarSolicitacao(int idSolicitacao, int idEstabelecimento)
        {
            var solicitacao = Domain.ImportacaoDeDados.SolicitacaoDeImportacaoRepository.Obter(idSolicitacao, idEstabelecimento);

            if (solicitacao == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Solicitação não encontrada.");

            else if (!solicitacao.PodeCancelar())
                ValidationHelper.Instance.AdicionarItemValidacao("Solicitação não pode ser cancelada");

            if (!ValidationHelper.Instance.IsValid)
                return;

            solicitacao.Cancelar();
            Domain.ImportacaoDeDados.SolicitacaoDeImportacaoRepository.Update(solicitacao);
        }
    }
}
