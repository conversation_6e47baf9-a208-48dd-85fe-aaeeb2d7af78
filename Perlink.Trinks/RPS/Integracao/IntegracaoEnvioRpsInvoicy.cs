﻿using Perlink.NFSe;
using Perlink.NFSe.Invoicy;
using Perlink.NFSe.Models;
using Perlink.Shared.Encryptor;
using Perlink.Shared.Exceptions;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Pessoas.Services;
using Perlink.Trinks.Resources.Areas.HotSite.Models;
using Perlink.Trinks.RPS.GeradorDeArquivo;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.RPS.Integracao
{

    public class IntegracaoEnvioRpsInvoicy : IntegracaoEnvioRPS
    {
        private const int QuantidadeItensFaixa = 10;
        private const int QuantidadeIdsPorBuscaDadosParaGeracaoDeRPS = 50;
        public override string Enviar(List<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int idPessoaSolicitante, int? numeroLote = null)
        {
            // Verificar e preparar ambiente
            var configuracaoNFe = pessoaJuridica.ConfiguracaoNFe;
            var ambienteProducao = configuracaoNFe.EhProducaoConsiderandoAmbiente();
            VerificarECadastrarEmpresa(pessoaJuridica, ambienteProducao);

            if (!registros.Any())
                return null;

            var pessoaSolicitante = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaSolicitante);

            // Gerar lote RPS
            var parametros = new ParametrosGerarLoteRPSComEmissoesRPS(registros, pessoaJuridica, pessoaSolicitante, Enums.TipoGerarPara.GerarParaEmissaoWebService);
            var (loteRPS, dados) = Domain.RPS.DadosRPSService.GerarLoteRPSComEmissoesRPSComTransactionDeBanco(parametros);

            // Gerar modelo de lote para envio
            var loteRPSModel = GeraLoteRPSModel(pessoaJuridica, configuracaoNFe, ambienteProducao, registros, loteRPS);
            try
            {
                // Enviar para Invoicy
                var retornoInvoicy = EnviarParaInvoicy(pessoaJuridica, loteRPSModel, loteRPS.NumeroLote);

                // Definir status do lote como pendente
                Domain.RPS.DadosRPSService.DefinirStatusDeTodoOLote(dados, StatusRpsEnum.Pendente);

                // Obter RPSs do lote e registrar fim do processamento
                var rpssDoLote = Domain.RPS.EmissaoRPSRepository.ListarPorPJLote(pessoaJuridica.IdPessoa, loteRPS.NumeroLote).ToList();
                Domain.RPS.EmissaoRPSService.RegistrarFimProcessamento(loteRPS.Id);

                // Processar retorno
                return ProcessarRetornoInvoicy(loteRPS, pessoaJuridica, retornoInvoicy, rpssDoLote, false, true);
            }
            catch (Exception e)
            {
                RegistraLogEmissao(pessoaJuridica.CNPJ, $"Erro ao enviar lote {loteRPS} para Invoicy: {e.Formatada()}");

                Domain.RPS.DadosRPSService.RejeitarOuDesfazerLote(pessoaJuridica.ConfiguracaoNFe.UltimoLoteGerado, pessoaJuridica.IdPessoa, e.Message);
                RegistraLogEmissao(pessoaJuridica.CNPJ, $"Lote {loteRPS} desfeito.");

                throw;
            }
        }

        public override string EnviarNotaUnificada(List<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int idPessoaSolicitante, int? numeroLote = null)
        {
            // Verificar e preparar ambiente
            var configDefault = Domain.RPS.ConfiguracaoPadraoNFSRepository.ObterValoresDefaultPorCidade(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade);
            var configuracaoNFe = pessoaJuridica.ConfiguracaoNFe;
            var ambienteProducao = configuracaoNFe.EhProducaoConsiderandoAmbiente();
            VerificarECadastrarEmpresa(pessoaJuridica, ambienteProducao);

            var pessoaSolicitante = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaSolicitante);

            // Gerar lote RPS
            var parametros = new ParametrosGerarLoteRPSComEmissoesRPS(registros, pessoaJuridica, pessoaSolicitante, Enums.TipoGerarPara.GerarParaEmissaoWebService, numeroLote);
            var (loteRPS, dados) = Domain.RPS.DadosRPSService.GerarLoteRPSComEmissoesRPSComTransactionDeBanco(parametros);

            registros = registros.Where(f => f.DadosRPS != null).ToList(); // Removendo registros que já emitidos

            // Preparar RPS unificado
            var loteRPSModel = new LoteRPSModel();
            var rpsUnificado = PrepararRpsUnificado(registros, pessoaJuridica, configuracaoNFe, ambienteProducao, numeroLote, loteRPSModel);

            if (rpsUnificado.ValorTotal <= 0)
                return "Sem valor a emitir";

            // Enviar para Invoicy
            var retornoInvoicy = EnviarParaInvoicy(pessoaJuridica, loteRPSModel, loteRPS.NumeroLote);

            // Definir status do lote como pendente
            Domain.RPS.DadosRPSService.DefinirStatusDeTodoOLote(dados, StatusRpsEnum.Pendente);

            // Obter RPSs do lote e registrar fim do processamento
            var rpssDoLote = Domain.RPS.EmissaoRPSRepository.ListarPorPJLote(pessoaJuridica.IdPessoa, loteRPS.NumeroLote).ToList();
            Domain.RPS.EmissaoRPSService.RegistrarFimProcessamento(loteRPS.Id);

            return ProcessarRetornoInvoicy(loteRPS, pessoaJuridica, retornoInvoicy, rpssDoLote, true, true);
        }

        private void VerificarECadastrarEmpresa(PessoaJuridica pessoaJuridica, bool ambienteProducao)
        {
            if (string.IsNullOrWhiteSpace(pessoaJuridica.ConfiguracaoNFe.IdExternoDoAmbiente()))
                CadastrarEmpresa(pessoaJuridica, ambienteProducao);
        }

        private RetornoConsultaDocumentoModel EnviarParaInvoicy(PessoaJuridica pessoaJuridica, LoteRPSModel loteRPSModel, int numeroLote)
        {
            var stopwatch = Stopwatch.StartNew();

            RegistraLogEmissao(pessoaJuridica.CNPJ, $"Enviando lote {numeroLote} para Invoicy");

            RetornoConsultaDocumentoModel retornoInvoicy;

            var emissaoRpsSequencial = Domain.RPS.ConfiguracaoPadraoNFSRepository.CidadePossuiEmissaoRpsSequencial(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade);
            var togglePermitirEmitirRpsSequencial = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(pessoaJuridica.Estabelecimento, Recurso.PermiteEmisasoSequencialRPS).EstaDisponivel;
            if (emissaoRpsSequencial && togglePermitirEmitirRpsSequencial)
            {
                RegistraLogEmissao(pessoaJuridica.CNPJ, $"Emitindo RPS sequencial para Invoicy. Lote {numeroLote}.");
                var retornoEmissaoSequencial = new IntegracaoInvoicy().EmitirNotaRpsSequencial(loteRPSModel, pessoaJuridica.ConfiguracaoNFe.EhProducaoConsiderandoAmbiente(), pessoaJuridica.ConfiguracaoNFe.IdExternoDoAmbiente(), pessoaJuridica.ConfiguracaoNFe.UltimoRpsEmitido);

                #region Atualiza os numeros das emissoes
                foreach (var rpsProcessado in retornoEmissaoSequencial.RetornoRpsNovaNumeracao)
                {
                    var emissaoRps = Domain.RPS.EmissaoRPSRepository.Load(rpsProcessado.IdEmissao);
                    emissaoRps.Numero = rpsProcessado.Numero;
                    Domain.RPS.EmissaoRPSRepository.UpdateNoFlush(emissaoRps);
                }
                Domain.RPS.EmissaoRPSRepository.Flush();
                #endregion

                retornoInvoicy = retornoEmissaoSequencial.RetornoDocumentoModel;
            }
            else
                retornoInvoicy = new IntegracaoInvoicy().EmitirNota(loteRPSModel, pessoaJuridica.ConfiguracaoNFe.EhProducaoConsiderandoAmbiente(), pessoaJuridica.ConfiguracaoNFe.IdExternoDoAmbiente());

            stopwatch.Stop();
            double tempo = (int)stopwatch.Elapsed.TotalMilliseconds;

            RegistraLogEmissao(pessoaJuridica.CNPJ, $"Retorno envio Invoicy do lote {numeroLote}: {retornoInvoicy.CodigoStatus}|{retornoInvoicy.Status} em {tempo}ms");

            return retornoInvoicy;
        }

        private DadosParaGeracaoLoteRPSDTO PrepararRpsUnificado(List<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica,
            PessoaJuridicaConfiguracaoNFe configuracaoNFe, bool ambienteProducao, int? numeroLote, LoteRPSModel lote)
        {
            var rpsUnificado = registros.First(); // Pegando o primeiro como base para o unificado

            var nomeServico = "";
            rpsUnificado.BaseCalculo = registros.Sum(r => r.BaseCalculo);
            rpsUnificado.DataTransacao = registros.Max(r => r.DataTransacao);
            rpsUnificado.NomesServicos = new List<string>() { "Cota-parte em parceira com " + rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.RazaoSocial + " - " + rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.CNPJ.FormatarCNPJ() + " (LF n° 12.592/2012) | De acordo com a resolução CGSN N° 137, de 4 de Dezembro de 2017. | " };
            nomeServico = "Cota-parte em parceira com " + rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.RazaoSocial + " - " + rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.CNPJ.FormatarCNPJ() + " (LF n° 12.592/2012) | De acordo com a resolução CGSN N° 137, de 4 de Dezembro de 2017. | ";
            rpsUnificado.ValorDeducoes = registros.Sum(r => r.ValorDeducoes);
            rpsUnificado.ValorTotal = registros.Sum(r => r.ValorTotal);
            rpsUnificado.TextoInformandoValorDosImpostos = "";
            rpsUnificado.NomeCliente = rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.RazaoSocial;
            rpsUnificado.Email = rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.Email;
            rpsUnificado.Cpf = rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.CNPJ;
            rpsUnificado.Cnpj = rpsUnificado.Transacao.PessoaQueRecebeu.PessoaJuridica.CNPJ;

            rpsUnificado.Servicos = new List<ServicoDoRpsDTO>() {
                new ServicoDoRpsDTO() {
                    Valor = registros.Sum(r => r.ValorTotal),
                    AliquotaIBPT = registros.FirstOrDefault()?.Servicos.FirstOrDefault()?.AliquotaIBPT
                }
            };

            var gerador = GeradorDeArquivoRPS.PorPessoaJuridica(pessoaJuridica);
            var configuracao = configuracaoNFe;

            var numero = numeroLote ?? configuracao.UltimoLoteGerado;

            // Determinar datas de competência e emissão
            var dataCompetencia = rpsUnificado.DataTransacao;
            if (Calendario.Hoje().Subtract(dataCompetencia).TotalDays > 9 && configuracao.PermitirEnvioDataEmissaoNaDataCompetencia)
                dataCompetencia = Calendario.Hoje();

            var dataEmissao = Calendario.Hoje();
            if (configuracao.PermitirEnvioDataCompetenciaNaDataEmissao)
                dataEmissao = rpsUnificado.DataTransacao;

            var estabelecimentoCodIbge = pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;

            // Preparar item de serviço
            string nome = estabelecimentoCodIbge == "5208707" ? nomeServico.Replace("|", "; ") : nomeServico;
            decimal valorUnitario = rpsUnificado.ValorTotal;

            string cnae = configuracao.CodigoCnae != null ? configuracao.CodigoCnae.ToString() : null;

            var prefeiturasQueRecebemItemListaServicoNoLugarDoCnae = new[] { "1501402" };
            if (prefeiturasQueRecebemItemListaServicoNoLugarDoCnae.Contains(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE))
                cnae = configuracao.ItemListaServico;

            var prefeiturasQueNaoRecebemCnae = new[] { "3302403", "3552502", "5102702" };
            if (prefeiturasQueNaoRecebemCnae.Contains(estabelecimentoCodIbge))
                cnae = string.Empty;

            var itemServico = new RPSModel.ItemRPSModel
            {
                AliquotaISS = configuracao.Aliquota_Iss_NaoFormatada == null ? configuracao.AliquotaISS : configuracao.Aliquota_Iss_NaoFormatada.Value,
                CNAE = cnae,
                Codigo = "1",
                Desconto = 0,
                Descricao = nome,
                ItemListaServico = configuracao.ItemListaServico,
                Quantidade = 1,
                ValorUnitario = valorUnitario
            };

            var itensServico = new List<RPSModel.ItemRPSModel> { itemServico };

            // Obter estabelecimento profissional
            var ep = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .QueryableProfissionaisParceirosDoEstabelecimento(rpsUnificado.Transacao.PessoaQueRecebeu.Estabelecimento.IdEstabelecimento)
                .FirstOrDefault(p => p.PessoaJuridica == pessoaJuridica);

            // Criar modelo RPS
            var tcRps = new RPSModel
            {
                DataEmissao = dataEmissao,
                DataCompetencia = dataCompetencia,
                Numero = numero,
                AmbienteProducao = ambienteProducao,
                OptanteSimples = configuracao.CodigoOptanteSimplesNacional == "1",
                Prestador = GerarEmpresaNotaUnificadaModel(pessoaJuridica, ep, ambienteProducao),
                Tomador = GerarTomadorNotaUnificadaModel(rpsUnificado),
                CMC = configuracao.CmcPrestador,
                RegimeEspecialTributacao = gerador.CamposExistentes().CodigoRegimeEspecialTributacao || configuracao.CodigoTipoRegimeEspecialTributacao != null ? configuracao.CodigoTipoRegimeEspecialTributacao : null,
                Serie = configuracao.Serie == null ? gerador.SerieDaNota() : configuracao.Serie,
                NaturezaDaOperacao = configuracao.NaturezaDaOperacao,
                StatusRPS = rpsUnificado.StatusRPS == StatusRpsEnum.ACancelar ? RPSModel.StatusRPSEnum.Cancelada : RPSModel.StatusRPSEnum.Normal,
                CodigoTributacaoMunicipio = configuracao.CodigoTributacaoMunicipio,
                TextoInformandoValorDosImpostos = rpsUnificado.TextoInformandoValorDosImpostos,
                ValorDeducao = rpsUnificado.ValorDeducoes ?? 0,
                ValorBaseDeCalculo = rpsUnificado.BaseCalculo ?? 0,
                AliquotaPis = configuracao.AliquotaPis,
                AliquotaCofins = configuracao.AliquotaCofins,
                Itens = itensServico,
                HabilitadoPadraoNacionalMei = configuracaoNFe.HabilitadoPadraoNacionalMei || configuracaoNFe.HabilitadoPadraoNacionalEstabelecimento
            };

            lote.RPSs.Add(tcRps);
            return rpsUnificado;
        }

        public override string RecuperarEspelhoRPS(int numeroRPS, PessoaJuridica pessoaJuridica, bool retornarEspelho = true, bool retornarChaveAcesso = false)
        {
            var configNfse = pessoaJuridica.ConfiguracaoNFe;

            var rps = Domain.RPS.EmissaoRPSRepository.ObterRPSEmitidoOuCanceladoPorNumero(numeroRPS, pessoaJuridica.IdPessoa);
            string linkPdf = null;
            string linkDocPrefeitura = null;
            string chaveAcesso = null;

            if (rps != null)
            {
                if (!string.IsNullOrEmpty(rps.LinkPDF) && !string.IsNullOrEmpty(rps.LinkDocPrefeitura) && !string.IsNullOrEmpty(rps.ChaveAcesso))
                {
                    linkPdf = rps.LinkPDF;
                    linkDocPrefeitura = rps.LinkDocPrefeitura;
                    chaveAcesso = rps.ChaveAcesso;
                }
                else
                {
                    var estabelecimentoCodIbge = pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;
                    var configMunicipio = Domain.RPS.NfseConfiguracaoMunicipioRepository.ConfiguracaoMunicipio(estabelecimentoCodIbge);
                    var habilitarNumeracaoIsaneto = configMunicipio != null ? configMunicipio.HabilitarFormatoNumeracaoIsaneto : false;

                    var invoicy = new IntegracaoInvoicy();
                    var retornoConsulta = invoicy.ConsultarEspelhoNota(pessoaJuridica.CNPJ, numeroRPS, configNfse.Serie, configNfse.EhProducaoConsiderandoAmbiente(), configNfse.IdExternoDoAmbiente(), habilitarNumeracaoIsaneto);

                    if (retornoConsulta != null)
                    {
                        linkPdf = retornoConsulta.LinkPDF;
                        linkDocPrefeitura = retornoConsulta.LinkDocPrefeitura;
                        chaveAcesso = retornoConsulta.ChaveAcesso;
                    }
                }

                if (!string.IsNullOrEmpty(linkPdf) || !string.IsNullOrEmpty(linkDocPrefeitura) || !string.IsNullOrEmpty(chaveAcesso))
                {
                    if (!string.IsNullOrEmpty(linkPdf))
                    {
                        rps.LinkPDF = linkPdf;
                    }
                    if (!string.IsNullOrEmpty(linkDocPrefeitura))
                    {
                        rps.LinkDocPrefeitura = linkDocPrefeitura;
                    }
                    if (!string.IsNullOrEmpty(chaveAcesso))
                    {
                        rps.ChaveAcesso = chaveAcesso;
                    }

                    Domain.RPS.EmissaoRPSRepository.Update(rps);
                }
            }

            if (retornarChaveAcesso)
            {
                return chaveAcesso;
            }
            else if (retornarEspelho)
            {
                return linkPdf;
            }
            else
            {
                return linkDocPrefeitura;
            }
        }



        private void RegistraLog(string cnpj, string mensagem)
        {
            LogService<IntegracaoEnvioRpsInvoicy>.Info($"[Consulta] CNPJ: {cnpj} - {mensagem}");
        }

        private void RegistraLogEmissao(string cnpj, string mensagem)
        {
            LogService<IntegracaoEnvioRpsInvoicy>.Info($"[Emissao] CNPJ: {cnpj} - {mensagem}");
        }

        private LoteRPSModel GeraLoteRPSModel(PessoaJuridica pessoaJuridica, PessoaJuridicaConfiguracaoNFe configuracaoNFSe, bool ambienteProducao, List<DadosParaGeracaoLoteRPSDTO> registros, LoteRPS loteRPS)
        {
            var lote = new LoteRPSModel();

            var gerador = GeradorDeArquivoRPS.ObterPadraoPorPessoaJuridica(pessoaJuridica);

            foreach (var r in registros)
            {
                if (r.ValorTotal <= 0)
                    continue;
                var dadosRPSTransacao = r.DadosRPS;

                var emissaoVigente = dadosRPSTransacao.EmissoesRPS.LastOrDefault();

                var numero = 0;
                var idEmissao = 0;
                if (emissaoVigente != null)
                {
                    numero = emissaoVigente.Numero;
                    idEmissao = emissaoVigente.Id;
                }

                RegistraLogEmissao(pessoaJuridica.CNPJ, $"Gerando dados Invoicy do RPS {numero} lote {loteRPS.NumeroLote}");

                //var emissaoSubstituida = dadosRPSTransacao.EmissoesRPS.LastOrDefault(f => f.Numero != numero);
                //var numeroRPSSubstituido = emissaoSubstituida != null ? emissaoSubstituida.Numero : 0;

                var dataCompetencia = r.DataTransacao;
                if (Calendario.Hoje().Subtract(dataCompetencia).TotalDays > 9 && configuracaoNFSe.PermitirEnvioDataEmissaoNaDataCompetencia)
                    dataCompetencia = Calendario.Hoje();

                var dataEmissao = Calendario.Hoje();
                if (configuracaoNFSe.PermitirEnvioDataCompetenciaNaDataEmissao)
                    dataEmissao = r.DataTransacao;

                var estabelecimentoCodIbge = pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;

                var textoProcon = Domain.RPS.NfseConfiguracaoMunicipioRepository.ObterDescricaoComplementarCodigoIbge(estabelecimentoCodIbge);
                var configMunicipio = Domain.RPS.NfseConfiguracaoMunicipioRepository.ConfiguracaoMunicipio(estabelecimentoCodIbge);

                if (r.EnderecoDoCliente != null)
                {
                    if (r.EnderecoDoCliente.Cidade != null && r.EnderecoDoCliente.UF != null
                        && r.EnderecoDoCliente.Cidade == pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.Nome
                        && r.EnderecoDoCliente.UF.IdUF == pessoaJuridica.EnderecoProprio.UF.IdUF
                        && configuracaoNFSe.CfpsNoMunicipio != null)
                    {
                        configuracaoNFSe.CodigoTributacaoMunicipio = configuracaoNFSe.CfpsNoMunicipio.ToString();
                    }

                    if (r.EnderecoDoCliente.Cidade != null && r.EnderecoDoCliente.UF != null
                        && r.EnderecoDoCliente.Cidade != pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.Nome
                        && r.EnderecoDoCliente.UF.IdUF == pessoaJuridica.EnderecoProprio.UF.IdUF
                        && configuracaoNFSe.CfpsForaMunicipio != null)
                    {
                        configuracaoNFSe.CodigoTributacaoMunicipio = configuracaoNFSe.CfpsForaMunicipio.ToString();
                    }

                    if (r.EnderecoDoCliente.UF != null && r.EnderecoDoCliente.UF.IdUF != pessoaJuridica.EnderecoProprio.UF.IdUF
                        && configuracaoNFSe.CfpsForaEstado != null)
                    {
                        configuracaoNFSe.CodigoTributacaoMunicipio = configuracaoNFSe.CfpsForaEstado.ToString();
                    }
                }

                var tcRps = new RPSModel
                {
                    DataEmissao = dataEmissao,
                    DataCompetencia = dataCompetencia,
                    Numero = numero,
                    IdEmissao = idEmissao,
                    AmbienteProducao = ambienteProducao,
                    OptanteSimples = configuracaoNFSe.CodigoOptanteSimplesNacional == "1",
                    Prestador = GerarEmpresaModel(pessoaJuridica, ambienteProducao),
                    Tomador = GerarTomadorModel(r, pessoaJuridica),
                    CMC = configuracaoNFSe.CmcPrestador,
                    RegimeEspecialTributacao = gerador.CamposExistentes().CodigoRegimeEspecialTributacao || configuracaoNFSe.CodigoTipoRegimeEspecialTributacao != null ? configuracaoNFSe.CodigoTipoRegimeEspecialTributacao : null,
                    Serie = configuracaoNFSe.Serie == null ? gerador.SerieDaNota() : configuracaoNFSe.Serie,
                    NaturezaDaOperacao = configuracaoNFSe.NaturezaDaOperacao,
                    StatusRPS = r.StatusRPS == StatusRpsEnum.ACancelar ? RPSModel.StatusRPSEnum.Cancelada : RPSModel.StatusRPSEnum.Normal,
                    CodigoTributacaoMunicipio = configuracaoNFSe.CodigoTributacaoMunicipio,
                    TextoInformandoValorDosImpostos = r.TextoInformandoValorDosImpostos,
                    TextoProcon = textoProcon,
                    HabilitadoDeducaoItemNaoTributavel = configMunicipio != null ? configMunicipio.HabilitaItemDeducaoNaoTributavel : false,
                    EmiteTagDeducao = configMunicipio != null ? configMunicipio.EmitirTagDeducao : true,
                    EmiteTagBaseDeCalculo = configMunicipio != null ? configMunicipio.EmitirTagBaseDeCalculo : true,
                    HabilitarDeducaoDuasCasasDecimais = configMunicipio != null ? configMunicipio.HabilitarDeducaoDuasCasasDecimais : false,
                    HabilitarBasedeCalculoDuasCasasDecimais = configMunicipio != null ? configMunicipio.HabilitarBasedeCalculoDuasCasasDecimais : false,
                    ArredondarBaseDeCalculoAntesCalcularIss = configMunicipio != null ? configMunicipio.ArredondarBaseDeCalculoAntesCalcularIss : false,
                    HabilitarTruncarValissDuasCasasDecimais = configMunicipio != null ? configMunicipio.HabilitarTruncarValissDuasCasasDecimais : false,
                    HabilitarItemvUnitDuasCasasDecimais = (configMunicipio != null && configMunicipio.HabilitarItemvUnitDuasCasasDecimais) || configuracaoNFSe.HabilitarItemvUnitDuasCasasDecimais,
                    HabilitarEnvioTagItemVlrLiquido = configMunicipio != null ? configMunicipio.HabilitarEnvioTagItemVlrLiquido : false,
                    HabilitarNumeracaoIsaneto = configMunicipio != null ? configMunicipio.HabilitarFormatoNumeracaoIsaneto : false,
                    UtilizarCnpjPrestadorNoTomador = configMunicipio != null ? configMunicipio.UtilizarCnpjPrestadorNoTomador : false,
                    UtilizarEmailPrestadorNoTomador = configMunicipio != null ? configMunicipio.UtilizarEmailPrestadorNoTomador : false,
                    NaoEnviarDadosTomadorSemCpf = configMunicipio != null ? configMunicipio.NaoEnviarDadosTomadorSemCpf : false,
                    ValorDeducao = r.ValorDeducoes ?? 0,
                    ValorBaseDeCalculo = r.BaseCalculo ?? 0,
                    HabilitadoPadraoNacionalMei = configuracaoNFSe.HabilitadoPadraoNacionalMei,
                    HabilitadoPadraoNacionalEstabelecimento = configuracaoNFSe.HabilitadoPadraoNacionalEstabelecimento,
                    HabilitarTagItemSeqComecarUm = configMunicipio != null ? configMunicipio.HabilitarTagItemSeqComecarUm : false,
                    HabilitarDeducaoPorServicoPadraoIPM = configMunicipio != null ? configMunicipio.HabilitarDeducaoPorServicoPadraoIPM : false,
                    HabilitaEnviarCnaeNull = configuracaoNFSe.HabilitaEnviarCnaeNull ?? false,
                    MaxQtdcaracteresDiscriminacao = configMunicipio != null ? configMunicipio.MaxQtdcaracteresDiscriminacao : null,
                    AliquotaPis = configuracaoNFSe.AliquotaPis,
                    AliquotaCofins = configuracaoNFSe.AliquotaCofins,
                    Itens = r.Servicos.Select(f =>
                    {
                        string nome = estabelecimentoCodIbge == "5208707" ? f.Descricao.Replace("|", "; ") : f.Descricao;
                        decimal valorUnitario = f.Valor;

                        if (nome.Length < gerador.MinimoCaracteresDescricaoServico())
                            nome = "Serviço de " + nome;

                        string cnae = !string.IsNullOrWhiteSpace(configuracaoNFSe.CodigoCnae) ? configuracaoNFSe.CodigoCnae : null;

                        var prefeiturasQueRecebemItemListaServicoNoLugarDoCnae = new[] { "1501402" };
                        if (prefeiturasQueRecebemItemListaServicoNoLugarDoCnae.Contains(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE)) // Foi necessário fazer esta adaptação pois o Invoicy envia o CNAE onde deveria ser o ItemListaServico em algumas prefeituras do DSF
                            cnae = configuracaoNFSe.ItemListaServico;

                        var prefeiturasQueNaoRecebemCnae = new[] { "3302403", "3552502", "5102702" };
                        if (prefeiturasQueNaoRecebemCnae.Contains(estabelecimentoCodIbge)) //Para Macaé o CNAE não é obrigatório TRINKS-12279
                            cnae = string.Empty;

                        return new RPSModel.ItemRPSModel
                        {
                            AliquotaISS = configuracaoNFSe.Aliquota_Iss_NaoFormatada == null ? configuracaoNFSe.AliquotaISS : configuracaoNFSe.Aliquota_Iss_NaoFormatada.Value,
                            CNAE = cnae,
                            Codigo = "1",
                            Desconto = 0,
                            Descricao = nome,
                            ItemListaServico = configuracaoNFSe.ItemListaServico,
                            Quantidade = 1,
                            ValorUnitario = valorUnitario,
                            ValorCota = f.ValorCota
                        };
                    }).ToList()
                };

                lote.RPSs.Add(tcRps);
            }

            return lote;
        }

        private ClienteModel GerarTomadorNotaUnificadaModel(DadosParaGeracaoLoteRPSDTO r)
        {
            var pjEstabelecimento = r.Transacao.PessoaQueRecebeu;
            Telefone telefone = pjEstabelecimento.Telefones.FirstOrDefault(t => t.Ddi == DdiConstants.Brasil) ?? new Telefone { DDD = "00", Numero = "00000000" };

            var retorno = new ClienteModel
            {
                Cpf = r.Cpf,
                Cnpj = r.Cnpj,
                Email = r.Email,
                Nome = r.NomeCliente,
                InscricaoMunicipal = pjEstabelecimento.InscricaoMunicipal
            };

            if (telefone != null)
            {
                if (pjEstabelecimento.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE == "5211909")
                    retorno.Telefone = telefone.Numero.TrimStart('0');
                else
                    retorno.Telefone = telefone.DDD + telefone.Numero;
            }

            var end = pjEstabelecimento.EnderecoProprio;
            retorno.Endereco = GerarEnderecoModel(end);

            return retorno;
        }

        private ClienteModel GerarTomadorModel(DadosParaGeracaoLoteRPSDTO r, PessoaJuridica pj)
        {
            Telefone telefone = r.PessoaCliente.Telefones.FirstOrDefault(f => f.Ativo && f.Ddi == DdiConstants.Brasil);
            var retorno = new ClienteModel
            {
                Cpf = r.Cpf,
                Cnpj = r.Cnpj,
                Email = r.Email,
                Nome = r.NomeCliente
            };

            if (telefone != null)
            {
                if (pj.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE == "5211909")
                    retorno.Telefone = telefone.Numero.TrimStart('0');
                else
                    retorno.Telefone = telefone.DDD + telefone.Numero;
            }

            var endereco = r.EnderecoDoCliente;
            if (endereco != null && endereco.EnderecoEstahCompleto())
            {
                var municipio = Domain.Pessoas.CidadeRepository.Obter(endereco.Cidade, endereco.UF.Sigla);

                retorno.Endereco = new EnderecoModel
                {
                    Bairro = endereco.Bairro,
                    CEP = endereco.Cep,
                    CodigoIbgeMunicipio = municipio != null ? municipio.CodigoIBGE : null,
                    Cidade = municipio != null ? municipio.Nome : null,
                    Complemento = endereco.Complemento,
                    Logradouro = endereco.Logradouro,
                    TipoLogradouro = "Rua",
                    Numero = endereco.Numero,
                    UF = endereco.UF != null ? endereco.UF.Sigla : null
                };
            }
            else
            {
                var end = pj.EnderecoProprio;
                retorno.Endereco = GerarEnderecoModel(end);
            }

            return retorno;
        }



        private static EmpresaRetornoModel CadastrarEmpresa(PessoaJuridica pj, bool ambienteProducao)
        {
            EmpresaModel empresaModel = GerarEmpresaModel(pj, ambienteProducao);

            var invoicy = new IntegracaoInvoicy();
            var arquivoCertificado = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterCertificadoEhMigrarArquivosSeNecessario(pj);
            if (arquivoCertificado != null)
            {
                var fs = arquivoCertificado.S3FileInfo.OpenRead();

                var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
                var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == pj.IdPessoa);
                var senhaCertificado = criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);

                var autenticacao = new Autenticacao
                {
                    ArquivoCertificado = fs,
                    SenhaCertificado = senhaCertificado
                };
                empresaModel.Autenticacao = autenticacao;
            }
            else if (!string.IsNullOrWhiteSpace(pj.ConfiguracaoNFe.SenhaUsuarioCadastrado))
            {
                var autenticacao = new Autenticacao
                {
                    Login = string.IsNullOrWhiteSpace(pj.ConfiguracaoNFe.CpfUsuarioCadastrado) ? pj.CNPJ : pj.ConfiguracaoNFe.CpfUsuarioCadastrado,
                    Senha = pj.ConfiguracaoNFe.SenhaUsuarioCadastrado
                };
                empresaModel.Autenticacao = autenticacao;
            }

            var retornoInvoicy = invoicy.CadastrarEmpresa(empresaModel, ambienteProducao);

            if (retornoInvoicy.IdExterno != null)
            {
                var configuracao = pj.ConfiguracaoNFe;

                if (configuracao.EhProducaoConsiderandoAmbiente())
                    configuracao.IdExterno = retornoInvoicy.IdExterno;
                else
                    configuracao.IdExternoHomologacao = retornoInvoicy.IdExterno;
            }

            return retornoInvoicy;
        }

        private static EmpresaModel GerarEmpresaNotaUnificadaModel(PessoaJuridica pj, EstabelecimentoProfissional ep, bool ambienteProducao)
        {
            var tel = ep?.Profissional.PessoaFisica.Telefones?.FirstOrDefault(f => f.Ativo && f.Ddi == DdiConstants.Brasil) ?? new Telefone { DDD = "00", Numero = "00000000" };
            var email = ep?.Profissional.PessoaFisica.Email ?? "";

            var end = pj.EnderecoProprio;

            var endereco = GerarEnderecoModel(end);

            var inscricaoMunicipal = pj.InscricaoMunicipal;

            var habilitadoPadraoNacionalMei = false;
            var habilitadoPadraoNacionalEstabelecimento = false;
            if (pj.ConfiguracaoNFe != null)
            {
                habilitadoPadraoNacionalMei = pj.ConfiguracaoNFe.HabilitadoPadraoNacionalMei;
                habilitadoPadraoNacionalEstabelecimento = pj.ConfiguracaoNFe.HabilitadoPadraoNacionalEstabelecimento;
            }

            var empresaModel = new EmpresaModel
            {
                CNPJ = pj.CNPJ,
                TelefoneDDD = tel.DDD,
                Telefone = tel.Numero,
                Endereco = endereco,
                InscricaoEstadual = pj.InscricaoEstadual.SomenteNumeros(),
                InscricaoMunicipal = inscricaoMunicipal,
                NomeFantasia = pj.NomeFantasia,
                RazaoSocial = pj.RazaoSocial,
                Email = email,
                AmbienteProducao = ambienteProducao,
                HabilitadoPadraoNacionalMei = habilitadoPadraoNacionalMei || habilitadoPadraoNacionalEstabelecimento
            };

            return empresaModel;
        }

        private static EmpresaModel GerarEmpresaModel(PessoaJuridica pj, bool ambienteProducao)
        {
            var tel = pj.Telefones.FirstOrDefault(f => f.Ativo && f.Ddi == DdiConstants.Brasil) ?? new Telefone { DDD = "00", Numero = "00000000" };
            var end = pj.EnderecoProprio;

            var endereco = GerarEnderecoModel(end);

            //if (!ambienteProducao) endereco.CodigoIbgeMunicipio = "9999999";

            var inscricaoMunicipal = pj.InscricaoMunicipal;

            //if (inscricaoMunicipal.SomenteNumeros().Length == 7 && end.BairroEntidade.Cidade.CodigoIBGE == "2304400") {
            //    inscricaoMunicipal = inscricaoMunicipal.Left(6) + "-" + inscricaoMunicipal.Right(1);
            //}

            var habilitadoPadraoNacionalMei = false;
            var habilitadoPadraoNacionalEstabelecimento = false;
            if (pj.ConfiguracaoNFe != null)
            {
                habilitadoPadraoNacionalMei = pj.ConfiguracaoNFe.HabilitadoPadraoNacionalMei;
                habilitadoPadraoNacionalEstabelecimento = pj.ConfiguracaoNFe.HabilitadoPadraoNacionalEstabelecimento;
            }

            var empresaModel = new EmpresaModel
            {
                CNPJ = pj.CNPJ,
                TelefoneDDD = tel.DDD,
                Telefone = tel.Numero,
                Endereco = endereco,
                InscricaoEstadual = pj.InscricaoEstadual.SomenteNumeros(),
                InscricaoMunicipal = inscricaoMunicipal,
                NomeFantasia = pj.NomeFantasia,
                RazaoSocial = pj.RazaoSocial,
                Email = pj.Email,
                AmbienteProducao = ambienteProducao,
                HabilitadoPadraoNacionalMei = habilitadoPadraoNacionalMei || habilitadoPadraoNacionalEstabelecimento
            };

            return empresaModel;
        }

        private static EnderecoModel GerarEnderecoModel(Endereco end)
        {
            return new EnderecoModel
            {
                Bairro = end.Bairro,
                CEP = end.Cep,
                CodigoIbgeMunicipio = end.BairroEntidade.Cidade.CodigoIBGE,
                Complemento = end.Complemento,
                Cidade = end.Cidade,
                Logradouro = end.Logradouro,
                TipoLogradouro = end.TipoLogradouro.Nome,
                Numero = end.Numero,
                UF = end.UF.Sigla
            };
        }


        public override string AtualizarSituacaoLote(LoteRPS lote)
        {
            RegistraLog(lote.PessoaEmitente.CNPJ, "AtualizarSituacaoLote Lote: " + lote.Id);

            var pessoaJuridica = lote.PessoaEmitente;
            var idExterno = pessoaJuridica.ConfiguracaoNFe.IdExternoDoAmbiente();
            var rpssDoLote = Domain.RPS.EmissaoRPSRepository.ListarPorPJLote(pessoaJuridica.IdPessoa, lote.NumeroLote).ToList();

            if (rpssDoLote.All(r => r.DadosRPSTransacao.Processado()))
            {
                Domain.RPS.LoteRPSRepository.MarcarLoteProcessado(lote.Id, true);
                Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.AlterarAguardandoEnvio(pessoaJuridica.IdPessoa, false);
                return "Lote já totalmente processado";
            }

            var existeRpsNoLote = rpssDoLote.Any();
            if (existeRpsNoLote)
            {
                var ehNotaUnificadaParceiro = Domain.RPS.EmissaoRPSService.EhProfissionalParceiroComNotaUnificada(pessoaJuridica);

                var retorno = ObterRpsInvoicy(pessoaJuridica.ConfiguracaoNFe.EhProducaoConsiderandoAmbiente(), pessoaJuridica.IdPessoa, idExterno, rpssDoLote, lote.NumeroLote, ehNotaUnificadaParceiro, false);

                return ProcessarRetornoInvoicy(lote, pessoaJuridica, retorno, rpssDoLote, ehNotaUnificadaParceiro, false);
            }
            else
            {
                Domain.RPS.DadosRPSService.RejeitarOuDesfazerLote(lote.NumeroLote, pessoaJuridica.IdPessoa, "Lote desfeito por falta de RPS");
                return "Lote desfeito por falta de RPS";
            }
        }

        private List<EmissaoRPS> GetEmissaoRpsConsiderandoQueEmProfissionaisParceirosONumeroPodeSerDoLote(int numero, PessoaJuridica pessoaJuridica)
        {
            var ehNotaUnificadaParceiro = Domain.RPS.EmissaoRPSService.EhProfissionalParceiroComNotaUnificada(pessoaJuridica);

            if (ehNotaUnificadaParceiro)
            {
                RegistraLog(pessoaJuridica.CNPJ, $"Numero {numero} buscado como sendo o número do lote devido a ser um profissional parceiro com nota unificada");
                var emissoes = GetEmissaoConsiderandoNumeroDoLote(numero, pessoaJuridica);

                if (emissoes.Any()) return emissoes;
                RegistraLog(pessoaJuridica.CNPJ, $"Não foi encontrado um lote {numero}, será buscado então o número no número do RPS");
            }

            return GetEmissaoConsiderandoNumeroDoRPS(numero, pessoaJuridica);
        }

        private static List<EmissaoRPS> GetEmissaoConsiderandoNumeroDoRPS(int numero, PessoaJuridica pessoaJuridica)
        {
            return Domain.RPS.EmissaoRPSRepository.Queryable()
                .Where(f => f.PessoaJuridica == pessoaJuridica && f.Numero == numero)
                .ToList();
        }

        private static List<EmissaoRPS> GetEmissaoConsiderandoNumeroDoLote(int numero, PessoaJuridica pessoaJuridica)
        {
            return Domain.RPS.EmissaoRPSRepository.Queryable()
                .Where(f => f.PessoaJuridica == pessoaJuridica && f.Lote == numero)
                .ToList();
        }

        private string ProcessarRetornoInvoicy(LoteRPS lote, PessoaJuridica pessoaJuridica, RetornoConsultaDocumentoModel retorno, List<EmissaoRPS> rpssDoLote, bool ehNotaUnificadaParceiro, bool ehRetornoDeEmissao)
        {
            var retornoValidado = ValidarRetornoConsultaDocumento(lote, pessoaJuridica, retorno, rpssDoLote, ehNotaUnificadaParceiro, ehRetornoDeEmissao);
            if (!string.IsNullOrEmpty(retornoValidado)) return retornoValidado;
            string retornoQuantidadeDocumentosValido = null;

            if (retorno.NaoHaviaRPSPendenteParaConsultar)
            {
                retornoQuantidadeDocumentosValido = ValidarRetornoQuantidadeDocumentos(lote, pessoaJuridica, ref retorno, rpssDoLote, ehNotaUnificadaParceiro, ehRetornoDeEmissao);
                if (!string.IsNullOrEmpty(retornoQuantidadeDocumentosValido)) return retornoQuantidadeDocumentosValido;

                Domain.RPS.LoteRPSRepository.MarcarLoteProcessado(lote.Id, true);
                Domain.RPS.LoteRPSRepository.Refresh(lote);

                Domain.RPS.DadosRPSService.AtualizarEspelho(pessoaJuridica, retorno);
                ProcederComLoteProcessadoComSucesso(lote, ExtrairArquivos(lote, retorno), rpssDoLote);

                return lote + " - Processado";
            }

            IgualarStatusTrinksEInvoicy(retorno, ref rpssDoLote, ehNotaUnificadaParceiro);


            var existemDocumentosNaoProcessadosNoInvoicy = retorno.Documentos.Any() && retorno.Documentos.Any(f => !f.Processado);
            var existemDocumentosNaoProcessadosNoBanco = rpssDoLote.Any(r => !r.DadosRPSTransacao.Processado());

            if (existemDocumentosNaoProcessadosNoInvoicy || existemDocumentosNaoProcessadosNoBanco)
            {
                if (lote.QuantidadeDeConsultas > 0 && lote.QuantidadeDeConsultas % 3 == 0)
                    ReenviarRPSsPendentes(lote, pessoaJuridica, rpssDoLote, ehNotaUnificadaParceiro);

                return $"Ainda existem RPSs pendentes: Invoicy: {retorno.Documentos.Count(f => !f.Processado)} | Trinks: {rpssDoLote.Count(r => !r.DadosRPSTransacao.Processado())}";
            }


            retornoQuantidadeDocumentosValido = ValidarRetornoQuantidadeDocumentos(lote, pessoaJuridica, ref retorno, rpssDoLote, ehNotaUnificadaParceiro, ehRetornoDeEmissao);
            if (!string.IsNullOrEmpty(retornoQuantidadeDocumentosValido)) return retornoQuantidadeDocumentosValido;

            Domain.RPS.DadosRPSService.AtualizarEspelho(pessoaJuridica, retorno);
            ProcederComLoteProcessadoComSucesso(lote, ExtrairArquivos(lote, retorno), rpssDoLote);

            return lote + " - Processado";
        }

        private string ValidarRetornoQuantidadeDocumentos(LoteRPS lote, PessoaJuridica pessoaJuridica, ref RetornoConsultaDocumentoModel retorno, List<EmissaoRPS> rpssDoLote, bool ehNotaUnificadaParceiro, bool ehRetornoDeEmissao)
        {
            if (retorno.Documentos.Count < rpssDoLote.Count)
            {
                var retornoRps = ObterRpsInvoicy(pessoaJuridica.ConfiguracaoNFe.EhProducaoConsiderandoAmbiente(), pessoaJuridica.IdPessoa, pessoaJuridica.ConfiguracaoNFe.IdExternoDoAmbiente(), rpssDoLote, lote.NumeroLote, ehNotaUnificadaParceiro, true);
                var retornoRpsValidado = ValidarRetornoConsultaDocumento(lote, pessoaJuridica, retornoRps, rpssDoLote, ehNotaUnificadaParceiro, ehRetornoDeEmissao);
                if (!string.IsNullOrEmpty(retornoRpsValidado)) return retornoRpsValidado;

                retorno = retornoRps;
            }
            return null;
        }

        private string ValidarRetornoConsultaDocumento(LoteRPS lote, PessoaJuridica pessoaJuridica, RetornoConsultaDocumentoModel retorno, List<EmissaoRPS> rpssDoLote, bool ehNotaUnificadaParceiro, bool ehRetornoDeEmissao)
        {
            var statusesDeFalhaTotal = new[] { 173, 606 };
            if (statusesDeFalhaTotal.Contains(retorno.CodigoStatus) && ehRetornoDeEmissao) // 173 - Chave de comunicação inválida
            {
                Domain.RPS.DadosRPSService.RejeitarOuDesfazerLote(lote.NumeroLote, pessoaJuridica.IdPessoa, $"{retorno.CodigoStatus} - {retorno.Status}");
                return $"{retorno.CodigoStatus} - {retorno.Status}";
            }

            if (retorno?.Documentos == null || !retorno.Documentos.Any())
            {
                if (lote.QuantidadeDeConsultas > 0 && lote.QuantidadeDeConsultas % 3 == 0)
                    ReenviarRPSsPendentes(lote, pessoaJuridica, rpssDoLote, ehNotaUnificadaParceiro);

                return $"Ainda não está na base do Invoicy: {retorno.CodigoStatus} - {retorno.Status}";
            }

            if (retorno.CodigoStatus == 313)
            {
                return "Aguardar realização da consulta pois o Invoicy responde código 313 de forma errada";
            }

            if (!retorno.TemItemQueNaoFoiEnviadoParaPrefeitura && (retorno.FalhaTotal || retorno.FalhaParcial) && !PassouTempoDeSegurancaParaRejeitarOuDesfazerEmissao(rpssDoLote))
            {
                RegistraLog(pessoaJuridica.CNPJ, "Lote " + lote.NumeroLote + ". Aguardar realização da próxima consulta. Houve retorno de status de rejeição e deve ser aguardado o tempo de segurança de verificação desse status.");
                return "Aguardar realização da próxima consulta. Houve retorno de status de rejeição e deve ser aguardado o tempo de segurança de verificação desse status.";
            }

            return null;
        }

        private void ReenviarRPSsPendentes(LoteRPS lote, PessoaJuridica pessoaJuridica, List<EmissaoRPS> rpssDoLote, bool ehNotaUnificadaParceiro)
        {
            var statusPendentes = new[] { StatusRpsEnum.Pendente, StatusRpsEnum.NaoEmitido };
            var rpsPendentes = rpssDoLote.Where(r => statusPendentes.Contains(r.DadosRPSTransacao.StatusRPS)).ToList();

            if (rpsPendentes.Any())
            {
                RegistraLog(pessoaJuridica.CNPJ, $"Reenviando {rpsPendentes.Count} RPS pendentes do lote {lote.NumeroLote}");

                try
                {
                    var registros = Domain.RPS.DadosRPSService.ListarDadosParaGeracaoDeRPSPorPaginasDeIdsTransacao(new ParametrosFiltrosLotesRPS
                    {
                        IdsTransacao = rpsPendentes.Select(r => r.DadosRPSTransacao.Transacao.Id).ToList(),
                        IdPessoaDaPessoaJuridica = pessoaJuridica.IdPessoa,
                        Estabelecimento = rpsPendentes.First().DadosRPSTransacao.Transacao.PessoaQueRecebeu.Estabelecimento

                    }, QuantidadeIdsPorBuscaDadosParaGeracaoDeRPS);

                    var loteRPSModel = GeraLoteRPSModel(pessoaJuridica, pessoaJuridica.ConfiguracaoNFe, pessoaJuridica.ConfiguracaoNFe.EhProducaoConsiderandoAmbiente(), registros, lote);

                    RegistraLog(pessoaJuridica.CNPJ, $"Reenviando lote {lote.NumeroLote} para Invoicy");

                    if (ehNotaUnificadaParceiro)
                    {
                        // TODO: Implementar reenvio de nota unificada
                        // Temos problema de compartilhamento do status do lote entre os RPSs do profissional e do estabelecimento
                        // Temos problema de o ValorTotal da DTO estar sendo manipulados na controller de emissão unificada, ao invés de no Service
                        throw new NotImplementedException("Reenvio de nota unificada não implementado.");
                    }
                    else
                    {
                        var retornoInvoicy = EnviarParaInvoicy(pessoaJuridica, loteRPSModel, lote.NumeroLote);
                    }
                }
                catch (Exception e)
                {
                    RegistraLog(pessoaJuridica.CNPJ, $"Erro ao reenviar RPS pendentes do lote {lote}: {e.Formatada()}");
                }
            }
        }

        private bool PassouTempoDeSegurancaParaRejeitarOuDesfazerEmissao(List<EmissaoRPS> rpssDoLote)
        {
            if (!rpssDoLote.Any())
                return false;

            var dataEmissaoLote = rpssDoLote.Max(f => f.DataEmissao);
            int tempoDeSegurancaParaRejeitarLote = new ParametrosTrinks<Int32>(ParametrosTrinksEnum.tempo_seguranca_para_desfazer_lote).ObterValor();

            return dataEmissaoLote.AddMinutes(tempoDeSegurancaParaRejeitarLote) <= DateTime.Now;
        }



        #region Processamento de lote

        private void IgualarStatusTrinksEInvoicy(RetornoConsultaDocumentoModel retorno, ref List<EmissaoRPS> rpssDoLote, bool ehNotaUnificadaParceiro)
        {
            foreach (var emissaoRPS in rpssDoLote)
            {
                var dadosRPSTransacao = emissaoRPS.DadosRPSTransacao;

                var numeroNoInvoicy = ehNotaUnificadaParceiro ? emissaoRPS.Lote : emissaoRPS.Numero;
                var rpsInvoicy = retorno?.Documentos?.FirstOrDefault(f => f.Numero == numeroNoInvoicy);

                if (rpsInvoicy == null) continue;

                var statusDesejado = rpsInvoicy.Rejeitado ? StatusRpsEnum.Rejeitado
                    : rpsInvoicy.Autorizado ? StatusRpsEnum.Emitido
                    : rpsInvoicy.Cancelado ? StatusRpsEnum.Cancelado
                    : StatusRpsEnum.Pendente;

                if (dadosRPSTransacao.StatusRPS != statusDesejado)
                {
                    if (dadosRPSTransacao.PessoaJuridica.IdPessoa == emissaoRPS.PessoaJuridica.IdPessoa)
                    {
                        dadosRPSTransacao.StatusRPS = statusDesejado;
                    }

                    if (statusDesejado == StatusRpsEnum.Rejeitado)
                    {
                        string mensagem = ObterMotivoDeRejeicao(rpsInvoicy);

                        RejeitarOuDesfazerRPSPeloNumeroConsiderandoQuePodeSerNotaUnificada(dadosRPSTransacao.PessoaJuridica, rpsInvoicy.Numero, mensagem.Left(100));
                    }

                    RegistraLog(emissaoRPS.PessoaJuridica.CNPJ, $"Lote: {emissaoRPS.Lote} RPS: {emissaoRPS.Numero} alterado para {statusDesejado}");
                }
            }

            Domain.RPS.DadosRPSTransacaoRepository.Flush();
        }

        private static string ObterMotivoDeRejeicao(SituacaoDocumentoModel rpsInvoicy)
        {
            string pattern = @"Mensagem:\s*(.*)";
            Match match = Regex.Match(rpsInvoicy.Status, pattern);

            var mensagem = rpsInvoicy.Status;

            if (match.Success)
                mensagem = match.Groups[1].Value;
            return mensagem;
        }

        private RetornoConsultaDocumentoModel ObterRpsInvoicy(bool ambienteProducao, int idPessoaJuridica, string idExterno, IEnumerable<EmissaoRPS> rpsLote, int numeroLote, bool ehNotaUnificadaParceiro, bool consultarTodosRps)
        {
            var pj = Domain.Pessoas.PessoaJuridicaRepository.Load(idPessoaJuridica);
            var statusPendentes = new[] { StatusRpsEnum.Pendente, StatusRpsEnum.NaoEmitido, StatusRpsEnum.ACancelar };

            var minRps = ehNotaUnificadaParceiro ? numeroLote : GetPrimeiroRPSDoLoteConsiderandoQueProfissionalParceiroComNotaUnificadaUsaLoteNoLugarDoNumero(pj, numeroLote, rpsLote);
            var maxRps = ehNotaUnificadaParceiro ? numeroLote : GetUltimoRPSDoLoteConsiderandoQueProfissionalParceiroComNotaUnificadaUsaLoteNoLugarDoNumero(pj, numeroLote, rpsLote);
            List<(int Min, int Max)> faixasDeNumerosRPS;
            if (ehNotaUnificadaParceiro)
                faixasDeNumerosRPS = new List<(int Min, int Max)> { (minRps, maxRps) };
            else
            {
                List<int> numeros;
                if (consultarTodosRps)
                    numeros = rpsLote.Select(r => r.Numero).ToList();
                else
                    numeros = rpsLote.Where(r => statusPendentes.Contains(r.DadosRPSTransacao.StatusRPS)).Select(r => r.Numero).ToList();

                faixasDeNumerosRPS = ObterFaixasSequenciais(numeros, QuantidadeItensFaixa);
            }

            RegistraLog(pj.CNPJ, $"ObterRpsInvoicy Lote: {numeroLote} RPSs no Invoicy de {minRps} até {maxRps}");

            var invoicy = new IntegracaoInvoicy();

            var gerador = GeradorDeArquivoRPS.PorPessoaJuridica(pj);

            var serie = pj.ConfiguracaoNFe != null && pj.ConfiguracaoNFe.Serie != null ? pj.ConfiguracaoNFe.Serie : gerador.SerieDaNota();

            var estabelecimentoCodIbge = pj.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;
            var configMunicipio = Domain.RPS.NfseConfiguracaoMunicipioRepository.ConfiguracaoMunicipio(estabelecimentoCodIbge);
            var habilitarNumeracaoIsaneto = configMunicipio != null && configMunicipio.HabilitarFormatoNumeracaoIsaneto;

            if (!faixasDeNumerosRPS.Any()) // Se não houver RPS pendente
            {
                RegistraLog(pj.CNPJ, $"Lote {numeroLote} não tem RPS pendente");
                return new RetornoConsultaDocumentoModel
                {
                    CodigoStatus = -1 // Não há RPS pendente
                };
            }

            RetornoConsultaDocumentoModel retorno = null;

            foreach (var (Min, Max) in faixasDeNumerosRPS)
            {
                var stopwatch = Stopwatch.StartNew();

                var retornoConsulta = invoicy.ConsultarNotas(pj.CNPJ, Min, Max, serie, ambienteProducao, idExterno, habilitarNumeracaoIsaneto);

                if (retorno == null)
                    retorno = retornoConsulta;
                else
                    retorno.Documentos.AddRange(retornoConsulta.Documentos);

                stopwatch.Stop();
                double tempo = (int)stopwatch.Elapsed.TotalMilliseconds;
                RegistraLog(pj.CNPJ, $"ConsultarNotas Invoicy de {Min} até {Max} em {tempo}ms | {retornoConsulta.Documentos?.Count ?? 0} registros | {retornoConsulta.CodigoStatus} - {retornoConsulta.Status}");
            }

            if (retorno.Documentos != null && retorno.Documentos.Any())
            {
                RegistraLog(pj.CNPJ, $"Retorno Invoicy de {retorno.Documentos.Min(d => d.Numero)} até {retorno.Documentos.Max(d => d.Numero)} com {retorno.Documentos.Count} registros");
            }

            return retorno;
        }

        private List<(int Min, int Max)> ObterFaixasSequenciais(List<int> numerosRps, int quantidadeMaximaItensFaixa)
        {
            var faixas = new List<(int Min, int Max)>();
            if (numerosRps == null || !numerosRps.Any())
                return faixas;

            numerosRps.Sort();
            var numerosRpsAgrupados = numerosRps.ChunkBy(quantidadeMaximaItensFaixa);

            foreach (var grupoDeNumerosRps in numerosRpsAgrupados)
            {
                int inicio = grupoDeNumerosRps[0];
                int fim = grupoDeNumerosRps[0];
                for (int i = 1; i < grupoDeNumerosRps.Count; i++)
                {
                    if (grupoDeNumerosRps[i] == fim + 1)
                    {
                        fim = grupoDeNumerosRps[i];
                    }
                    else
                    {
                        faixas.Add((inicio, fim));
                        inicio = grupoDeNumerosRps[i];
                        fim = grupoDeNumerosRps[i];
                    }
                }
                faixas.Add((inicio, fim));
            }

            return faixas;
        }

        private static int GetUltimoRPSDoLoteConsiderandoQueProfissionalParceiroComNotaUnificadaUsaLoteNoLugarDoNumero(PessoaJuridica pessoaJuridica, int numeroLote, IEnumerable<EmissaoRPS> rpsLote)
        {
            var ehNotaUnificadaParceiro = Domain.RPS.EmissaoRPSService.EhProfissionalParceiroComNotaUnificada(pessoaJuridica);

            return ehNotaUnificadaParceiro ? numeroLote : (rpsLote.Max(f => (int?)f.Numero) ?? 0);
        }

        private static int GetPrimeiroRPSDoLoteConsiderandoQueProfissionalParceiroComNotaUnificadaUsaLoteNoLugarDoNumero(PessoaJuridica pessoaJuridica, int numeroLote, IEnumerable<EmissaoRPS> rpsLote)
        {
            var ehNotaUnificadaParceiro = Domain.RPS.EmissaoRPSService.EhProfissionalParceiroComNotaUnificada(pessoaJuridica);

            return ehNotaUnificadaParceiro ? numeroLote : (rpsLote.Min(f => (int?)f.Numero) ?? 0);
        }

        private void ProcederComLoteProcessadoComSucesso(LoteRPS lote, IDictionary<string, string> arquivos, List<EmissaoRPS> rpssDoLote = null)
        {
            RegistraLog(lote.PessoaEmitente.CNPJ, $"ProcederComLoteProcessadoComSucesso, lote: {lote}");
            Domain.RPS.EmissaoRPSService.MarcarLoteProcessadoELiberarOuNaoNovasEmissoes(lote, true);

            List<DadosParaGeracaoLoteRPSDTO> dadosRPSrejeitados = null;

            if (rpssDoLote?.Any() == true)
            {
                var podeListarRpsRejeitados = Domain.RPS.DadosRPSService.PodeRejeitarLote(rpssDoLote.First().Transacao.PessoaQueRecebeu.IdPessoa);

                if (podeListarRpsRejeitados)
                {
                    var rpsRejeitados = rpssDoLote.Where(r => r.DadosRPSTransacao.StatusRPS == StatusRpsEnum.Rejeitado);
                    if (rpsRejeitados?.Any() == true)
                    {
                        dadosRPSrejeitados = Domain.RPS.DadosRPSService.ListarDadosParaGeracaoDeRPSPorPaginasDeIdsTransacao(new ParametrosFiltrosLotesRPS
                        {
                            IdsTransacao = rpsRejeitados.Select(r => r.DadosRPSTransacao.Transacao.Id).ToList(),
                            IdPessoaDaPessoaJuridica = lote.PessoaEmitente.IdPessoa,
                            Estabelecimento = rpsRejeitados.First().DadosRPSTransacao.Transacao.PessoaQueRecebeu.Estabelecimento
                        }, QuantidadeIdsPorBuscaDadosParaGeracaoDeRPS);
                    }
                }
            }

            Domain.Pessoas.EnvioEmailService.EnviarEMailRPSLote(lote.PessoaEmitente.IdPessoa, lote.NumeroLote, arquivos, false, null, dadosRPSrejeitados);

            if (arquivos != null)
                foreach (var a in arquivos)
                {
                    GravarArquivo(a.Key, a.Value, lote.PessoaEmitente.CNPJ);
                }
        }

        private IDictionary<string, string> ExtrairArquivos(LoteRPS lote, RetornoConsultaDocumentoModel retorno)
        {
            RegistraLog(lote.PessoaEmitente.CNPJ, $"ExtrairArquivos, lote: {lote} - {retorno.Documentos.Count} documentos");

            IDictionary<string, string> arquivos = new Dictionary<string, string>();

            if (string.IsNullOrWhiteSpace(lote.NomeArquivoLote))
                return arquivos;

            foreach (var n in retorno.Documentos)
            {
                var nomeArquivo = lote.NomeArquivoLote;
                string key = nomeArquivo.Replace(".", "_N_" + n.Numero + ".");

                if (!arquivos.ContainsKey(key))
                    arquivos.Add(key, n.Xml);
            }

            return arquivos;
        }

        private void RejeitarOuDesfazerRPSPeloNumeroConsiderandoQuePodeSerNotaUnificada(PessoaJuridica pessoaJuridica, int numero, string motivoRejeicao)
        {
            var emissoes = GetEmissaoRpsConsiderandoQueEmProfissionaisParceirosONumeroPodeSerDoLote(numero, pessoaJuridica);

            foreach (var emissao in emissoes)
            {
                Domain.RPS.DadosRPSService.RejeitarOuDesfazerEmissaoRPS(emissao, motivoRejeicao);
            }
        }

        public void GravarArquivo(string nomeArquivo, string conteudo, string cnpj)
        {
            RegistraLog(cnpj, $"GravarArquivo, nomeArquivo: {nomeArquivo}");

            ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();
            string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
            var caminho = string.Join("/", "NFSE", cnpj, "Enviado", nomeArquivo);
            s3AmazonControleDeArquivos.GravarArquivo(conteudo, bucket, caminho);
        }

        #endregion Consulta RPS
    }
}
