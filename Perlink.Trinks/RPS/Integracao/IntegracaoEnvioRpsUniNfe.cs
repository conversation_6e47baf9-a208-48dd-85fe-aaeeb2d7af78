﻿using log4net;
using Perlink.Shared.Compression;
using Perlink.Shared.Encryptor;
using Perlink.Shared.Exceptions;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Shared.Patterns;
using Perlink.Shared.Xml;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Services;
using Perlink.Trinks.RPS.Enums;
using Perlink.Trinks.RPS.GeradorDeArquivo;
using Perlink.Trinks.RPS.Integracao.Schemas;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.Xml.Serialization;

namespace Perlink.Trinks.RPS.Integracao
{

    public class IntegracaoEnvioRpsUniNfe : IntegracaoEnvioRPS
    {

        public static void InicializeFileWatcher()
        {
            if (string.IsNullOrWhiteSpace(ConfiguracoesTrinks.Geral.UniNFEPath))
            {
                LogManager.GetLogger(typeof(IntegracaoEnvioRpsUniNfe)).Debug("FileWatcher do UniNFE não iniciar pois não está configurado neste ambiente");
                return;
            }

            var configs = GerenciadorDeConfiguracaoUniNFE.ObterConfiguracoesNfse();

            foreach (var empresa in configs)
            {
                empresa.PastaXmlAutorizado = empresa.PastaXmlEnviado + @"\Autorizados";

                IniciarVerificacaoDasPastas(empresa);
            }
        }

        public static void IniciarVerificacaoDasPastas(Empresa empresa)
        {
            LogService<IntegracaoEnvioRpsUniNfe>.Info("IniciarVerificacaoDasPastas: " + empresa.Nome);

            // Pasta erro
            ProcessarPasta(empresa.PastaXmlErro, "*-ped*.*", OnChangedError);
            ProcessarPasta(empresa.PastaXmlErro, "*-env-loterps*.*", OnChangedError);
            ProcessarPasta(empresa.PastaXmlErro, "*-nfe*.*", OnChangedError);

            FolderWatch(empresa.PastaXmlErro, "*-ped*.*", OnChangedError);
            FolderWatch(empresa.PastaXmlErro, "*-env-loterps*.*", OnChangedError);
            FolderWatch(empresa.PastaXmlErro, "*-nfe*.*", OnChangedError);

            //Pasta retorno
            ProcessarPasta(empresa.PastaXmlRetorno, "*.err", OnChangedError);
            ProcessarPasta(empresa.PastaXmlRetorno, "*-rec*.xml", OnChangedRetorno);
            ProcessarPasta(empresa.PastaXmlRetorno, "*-pro-rec*.xml", OnChangedRetorno);
            ProcessarPasta(empresa.PastaXmlRetorno, "*-nfe*.xml", OnChangedRetorno);
            ProcessarPasta(empresa.PastaXmlRetorno, "*-env-loterps*.txt", OnChangedRetorno);
            ProcessarPasta(empresa.PastaXmlRetorno, "*-ret-loterps.xml", OnChangedMensagemRetorno);
            ProcessarPasta(empresa.PastaXmlRetorno, "*-loterps.xml", OnChangedMensagemRetornoConsultaLote);

            FolderWatch(empresa.PastaXmlRetorno, "*.err", OnChangedError);
            FolderWatch(empresa.PastaXmlRetorno, "*-rec*.xml", OnChangedRetorno);
            FolderWatch(empresa.PastaXmlRetorno, "*-pro-rec*.xml", OnChangedRetorno);
            FolderWatch(empresa.PastaXmlRetorno, "*-env-loterps*.xml", OnChangedRetorno);
            FolderWatch(empresa.PastaXmlRetorno, "*-nfe*.xml", OnChangedRetorno);
            FolderWatch(empresa.PastaXmlRetorno, "*-env-loterps*.txt", OnChangedRetorno);
            FolderWatch(empresa.PastaXmlRetorno, "*-ret-loterps.xml", OnChangedMensagemRetorno);
            FolderWatch(empresa.PastaXmlRetorno, "*-loterps.xml", OnChangedMensagemRetornoConsultaLote);

            //Pasta envio
            ProcessarPasta(empresa.PastaXmlEnvio, "*.zip", OnChangedUnZip);
            ProcessarPasta(empresa.PastaXmlEnvio + @"\Temp", "*.*", OnChangedRecuperarSeNaoProcessaremUmMinuto);

            FolderWatch(empresa.PastaXmlEnvio, "*.zip", OnChangedUnZip);

            // Pasta autorizado
            ProcessarPasta(empresa.PastaXmlAutorizado, "*.err", OnChangedErrorSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-rec*.xml", OnChangedRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-pro-rec*.xml", OnChangedRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-num-lot*.xml", OnChangedRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-env-loterps*.xml", OnChangedRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-nfe*.xml", OnChangedRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-env-loterps*.txt", OnChangedRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-ret-loterps.xml", OnChangedMensagemRetornoSoMover, true);
            ProcessarPasta(empresa.PastaXmlAutorizado, "*-procNFe.xml", OnChangedRetorno, true);

            FolderWatch(empresa.PastaXmlAutorizado, "*.err", OnChangedErrorSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-rec*.xml", OnChangedRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-pro-rec*.xml", OnChangedRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-num-lot*.xml", OnChangedRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-env-loterps*.xml", OnChangedRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-nfe*.xml", OnChangedRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-env-loterps*.txt", OnChangedRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-ret-loterps.xml", OnChangedMensagemRetornoSoMover, true);
            FolderWatch(empresa.PastaXmlAutorizado, "*-procNFe.xml", OnChangedRetorno, true);
        }

        public override string Enviar(List<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int idPessoaSolicitante, int? numeroLote = null)
        {
            bool existeImplementacaoNoMunicipio = Domain.Pessoas.CidadeRepository.ExisteImplementacaoDeNFSeParaCidade(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);
            if (!existeImplementacaoNoMunicipio)
                throw new Exception("A emissão de nota de serviço não está implementada para este município.");

            GerenciadorDeConfiguracaoUniNFE.CriarPastasEConfiguracoesDaPessoaJuridicaNoUniNFESeForNecessario(pessoaJuridica);

            var endereco = Domain.Pessoas.EnderecoRepository.Queryable().First(f => f.Pessoa.IdPessoa == pessoaJuridica.IdPessoa);
            var uf = endereco.UF;

            var emiteServicoNaNFCe = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.CidadeEmiteServicoPorNFCe(pessoaJuridica);

            var configuracoes = GerenciadorDeConfiguracaoUniNFE.ObterConfiguracaoNfse(pessoaJuridica.CNPJ, emiteServicoNaNFCe ? 0 : 2);
            if (configuracoes == null)
                return null;

            var path = configuracoes.PastaXmlEnvio;

            var pessoaQueEmitiu = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaSolicitante);

            if (!registros.Any())
                return null;

            var primeiraTransacao = Domain.Financeiro.TransacaoRepository.Load(registros.First().IdTransacao);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(primeiraTransacao.PessoaQueRecebeu.IdPessoa);


            string nomeArquivo;
            var ms = Domain.RPS.DadosRPSService.PrepararArquivoLoteRPS(TipoGerarPara.GerarParaEmissaoWebService, registros, pessoaJuridica, pessoaQueEmitiu, out nomeArquivo);

            pessoaJuridica.Refresh();

            var ehNfe = emiteServicoNaNFCe;
            var nomeArquivoNovo = IncluirSufixoEnvioLoteNoNomeDoArquivo(nomeArquivo, ehNfe);

            AtualizarNomeArquivoLote(nomeArquivo, nomeArquivoNovo);

            GerarArquivo(ms, path + @"\" + nomeArquivoNovo);

            Domain.RPS.DadosRPSService.DefinirStatusDeTodoOLote(registros.Select(r => r.DadosRPS), StatusRpsEnum.Pendente);

            return nomeArquivoNovo;
        }

        private static void RejeitarOuDesfazerLoteOuRPSPeloNomeDoArquivo(string fullpath)
        {
            fullpath = fullpath.ToLower();
            if (fullpath.Contains("_n_"))
                Retry.Do(() => RejeitarOuDesfazerRPSPeloNomeDoArquivo(fullpath), TimeSpan.FromSeconds(1), 10);
            else
                RejeitarOuDesfazerLotePeloFullPathDoArquivo(fullpath);
        }

        private static void RejeitarOuDesfazerLotePeloFullPathDoArquivo(string fullpath)
        {
            LogService<IntegracaoEnvioRpsUniNfe>.Debug($"RejeitarOuDesfazerLotePeloFullPathDoArquivo: {fullpath}");
            var filename = Path.GetFileName(fullpath);
            IDictionary<string, string> arquivos = new Dictionary<string, string>();

            var nomeArquivo = LimparNomeArquivo(filename);
            var numeroLote = ExtrairNumeroLoteRPS(filename);
            if (numeroLote >= 0)
            {
                var cnpj = ObterCnpjPeloCaminhoFisico(fullpath);

                LoteRPS lote = Domain.RPS.LoteRPSRepository.ObterPorLoteECNPJ(numeroLote, cnpj);

                if (lote == null)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug($"RejeitarOuDesfazerLotePeloFullPathDoArquivo: sem lote identificado com numero {numeroLote} e CNPJ {cnpj}");
                    return;
                }
                var sr = new StreamReader(fullpath);
                string xml = sr.ReadToEnd();
                sr.Close();

                arquivos.Add(nomeArquivo, xml);

                LogService<IntegracaoEnvioRpsUniNfe>.Debug($"RejeitarOuDesfazerLotePeloFullPathDoArquivo lote: {lote}");

                Domain.Pessoas.EnvioEmailService.EnviarEMailRPSLote(lote.PessoaEmitente.IdPessoa, lote.NumeroLote, arquivos, true);
                Domain.RPS.DadosRPSService.RejeitarOuDesfazerLote(lote.NumeroLote, lote.PessoaEmitente.IdPessoa, "Lote rejeitado");
            }
            else
                LogService<IntegracaoEnvioRpsUniNfe>.Debug($"RejeitarOuDesfazerLotePeloFullPathDoArquivo: sem numero de lote identificado o nome {filename}");
        }

        private static void RejeitarOuDesfazerRPSPeloNomeDoArquivo(string fullpath)
        {
            LogService<IntegracaoEnvioRpsUniNfe>.Debug("RejeitarOuDesfazerRPSPeloNomeDoArquivo: " + fullpath);

            var arquivo = Path.GetFileName(fullpath);
            var cnpj = ObterCnpjPeloCaminhoFisico(fullpath);

            if (!arquivo.Contains("_n_"))
                throw new Exception("Deve conter '_N_' no nome do arquivo");

            var Xml = string.Empty;
            IDictionary<string, string> arquivos = new Dictionary<string, string>();

            var sr = new StreamReader(fullpath);
            Xml = sr.ReadToEnd();
            sr.Close();

            arquivos.Add(arquivo, Xml);

            var pj = Domain.Pessoas.PessoaJuridicaRepository.Obter(cnpj);
            var numeroRps = int.Parse(arquivo.Split('_').Last().SomenteNumeros());
            var rps = Domain.RPS.EmissaoRPSRepository.Queryable().FirstOrDefault(f => f.PessoaJuridica == pj && f.Numero == numeroRps);

            if (rps == null)
                return;

            LogService<IntegracaoEnvioRpsUniNfe>.Debug($"RejeitarOuDesfazerRPSPeloNomeDoArquivo EmissaoRPS ID: {rps.Id}");

            var lote = rps.Lote;

            Domain.Pessoas.EnvioEmailService.EnviarEMailRPSLote(rps.PessoaJuridica.IdPessoa, lote, arquivos, true);
            Domain.RPS.DadosRPSService.RejeitarOuDesfazerEmissaoRPS(rps, "Rejeitado");
        }

        private static string ObterCnpjPeloCaminhoFisico(string fullpath)
        {
            fullpath = fullpath.Replace(@"\\", @"\");
            var caminhoRelativo = fullpath.Replace(ConfiguracoesTrinks.Geral.UniNFEPath, "");
            var partes = caminhoRelativo.Split('\\');
            return partes.First(f => f.SomenteNumeros().Length == 14);
        }

        private static void FolderWatch(string path, string filter, FileSystemEventHandler watcherOnChanged, bool includeSubDirectories = false)
        {
            //LogService<IntegracaoEnvioRpsUniNfe>.Info(String.Format("FileWatcher iniciado:: {0} - {1}", path, filter));

            if (String.IsNullOrEmpty(path))
                return;
            var di = new DirectoryInfo(path);
            if (di.Exists)
            {
                var watcher = new FileSystemWatcher();
                watcher.Path = path;
                watcher.NotifyFilter = NotifyFilters.FileName;
                watcher.Filter = filter;
                watcher.IncludeSubdirectories = includeSubDirectories;
                //watcher.Changed += watcherOnChanged;
                watcher.Created += watcherOnChanged;
                //watcher.Deleted += watcherOnChanged;

                watcher.EnableRaisingEvents = true;

                //LogManager.GetLogger(typeof(IntegracaoEnvioRpsUniNfe)).Debug("FileWatcher iniciado:" + path + "\\" + filter);
            }
        }

        private static void GerarArquivo(MemoryStream ms, string path)
        {
            using (var file = new FileStream(path, FileMode.Create, FileAccess.Write))
            {
                var bytes = new byte[ms.Length];
                ms.Read(bytes, 0, (int)ms.Length);
                file.Write(bytes, 0, bytes.Length);
                ms.Close();
            }
        }

        private static string IncluirSufixoEnvioLoteNoNomeDoArquivo(string nomeArquivo, bool ehNfe)
        {
            var indexDoPonto = nomeArquivo.LastIndexOf('.');
            var nome = nomeArquivo.Substring(0, indexDoPonto);

            var partes = nomeArquivo.Split('.');
            var extensao = partes.Last();

            if (!ehNfe)
                return nome + "-env-loterps." + extensao;
            else
                return nome + "-nfe." + extensao;
        }

        private static string IncluirSufixoConsultaLoteNoNomeDoArquivo(string nomeArquivo)
        {
            var indexDoPonto = nomeArquivo.LastIndexOf('.');
            var nome = nomeArquivo.Substring(0, indexDoPonto);

            var partes = nomeArquivo.Split('.');
            var extensao = partes.Last();

            return nome + "-ped-loterps." + extensao;
        }

        private static string LimparNomeArquivo(string fileName)
        {
            var nomeArquivo = fileName.Replace("-env", "");
            nomeArquivo = nomeArquivo.Replace("-loterps", "");
            nomeArquivo = nomeArquivo.Replace("-nfe", "");
            nomeArquivo = nomeArquivo.Replace("-ret", "");
            nomeArquivo = nomeArquivo.Replace("-ped", "");

            if (nomeArquivo.Contains("_N_"))
            {
                var partes = Regex.Split(nomeArquivo, "_N_");
                nomeArquivo = partes[0] + Path.GetExtension(nomeArquivo);
            }

            return nomeArquivo;
        }

        private static void MoverParaBackupECriarPastaSeNecessario(string path)
        {
            var pathBackup = ObterPathBackup(path);

            Retry.Do(() => MoverArquivoParaOhS3(path), TimeSpan.FromSeconds(30), 3);
            //MoverArquivoParaOhS3(path);

            var pastaBackup = Path.GetDirectoryName(pathBackup);
            if (pastaBackup != null && !Directory.Exists(pathBackup))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("Criando o diretório " + pastaBackup);
                Directory.CreateDirectory(pastaBackup);
            }

            if (!File.Exists(path))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("Arquivo não existe " + path);
                return;
            }

            if (File.Exists(pathBackup) && File.Exists(path))
                File.Delete(pathBackup);

            Retry.Do(() => { if (File.Exists(path)) File.Move(path, pathBackup); }, TimeSpan.FromSeconds(30), 3);
        }

        private static void MoverArquivoParaOhS3(string path)
        {
            var relativePath = path.Replace(ConfiguracoesTrinks.Geral.UniNFEPath + '\\', String.Empty);
            var lista = relativePath.Split('\\');
            var cnpjEstabelecimento = lista.FirstOrDefault();
            var nomeArquivo = lista.LastOrDefault();
            var pathDiretorio = path.Replace(nomeArquivo, String.Empty);

            ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();
            string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();

            var di = new DirectoryInfo(pathDiretorio);
            var file = di.EnumerateFiles("*").FirstOrDefault(p => p.Name.Contains(nomeArquivo));

            if (file == null)
                return;

            if (!di.Exists)
                di.Create();

            var caminhoFisico = string.Join("/", bucket, "NFSE", cnpjEstabelecimento, lista[1]);
            s3AmazonControleDeArquivos.GravarArquivo(file.OpenRead(), caminhoFisico, file.Name);
        }

        private static string ObterPathBackup(string pathOriginal)
        {
            pathOriginal = pathOriginal.ToLower();

            if (pathOriginal.Contains("erro"))
                return pathOriginal.Replace(@"\erro\", @"\backup_erro\");

            if (pathOriginal.Contains("retorno"))
                return pathOriginal.Replace(@"\retorno\", @"\backup_retorno\");

            if (pathOriginal.Contains("autorizados"))
                return pathOriginal.Replace(@"\enviado\autorizados\", @"\backup_autorizados\");

            throw new Exception("Caminho original inválido");
        }

        private static void OnChangedError(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("OnChangedError " + e.FullPath);

                    RejeitarOuDesfazerLoteOuRPSPeloNomeDoArquivo(e.FullPath);

                    MoverParaBackupECriarPastaSeNecessario(e.FullPath);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static void OnChangedErrorSoMover(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("OnChangedErrorSoMover " + e.FullPath);
                    MoverParaBackupECriarPastaSeNecessario(e.FullPath);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static void OnChangedMensagemRetorno(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Info("OnChangedMensagemRetorno " + e.FullPath + " - " + e.ChangeType);

                    XDocument xmlDoc = null;

                    Retry.Do(() => xmlDoc = XDocument.Parse(File.ReadAllText(e.FullPath)), TimeSpan.FromSeconds(30), 4);

                    var detalhes = new DetalhesXML(xmlDoc.Root);

                    var status = new IntegracaoEnvioRpsUniNfe().StatusRetorno(detalhes);
                    switch (status)
                    {
                        case StatusRetornoXMLEnum.Sucesso:
                            OnChangedRetorno(sender, e);
                            break;

                        case StatusRetornoXMLEnum.Falha:
                            RejeitarOuDesfazerLoteOuRPSPeloNomeDoArquivo(e.FullPath);
                            MoverParaBackupECriarPastaSeNecessario(e.FullPath);
                            break;

                        case StatusRetornoXMLEnum.EmProcessamento:
                            OnChangedEmProcessamento(sender, e);
                            break;
                    }
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
                LogService<IntegracaoEnvioRpsUniNfe>.Error(e.Name);
            }
        }

        private static void OnChangedEmProcessamento(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug($"OnChangedEmProcessamento {e.FullPath} - {e.ChangeType}");

                    XDocument xmlDoc = null;

                    Retry.Do(() => xmlDoc = XDocument.Parse(File.ReadAllText(e.FullPath)), TimeSpan.FromSeconds(30), 4);
                    var detalhes = new DetalhesXML(xmlDoc.Root);

                    var status = new IntegracaoEnvioRpsUniNfe().StatusRetorno(detalhes);
                    if (status != StatusRetornoXMLEnum.EmProcessamento)
                        return;

                    var nomeArquivo = LimparNomeArquivo(e.Name);

                    string fullPath = e.FullPath;
                    string fileName = e.Name;

                    var numeroLote = ExtrairNumeroLoteRPS(fileName);
                    var cnpj = ObterCnpjPeloCaminhoFisico(fullPath);
                    var lote = Domain.RPS.LoteRPSRepository.ObterPorLoteECNPJ(numeroLote, cnpj);

                    if (lote == null)
                        return;

                    LogService<IntegracaoEnvioRpsUniNfe>.Debug($"Lote {lote} em processamento");

                    string numeroProtocolo = GetProtocolo(detalhes, "Protocolo")
                        ?? GetProtocolo(detalhes, "ns1:Protocolo")
                        ?? GetProtocolo(detalhes, "ns2:Protocolo")
                        ?? GetProtocolo(detalhes, "ns3:Protocolo");

                    if (string.IsNullOrWhiteSpace(numeroProtocolo))
                        return;

                    Domain.RPS.LoteRPSRepository.DefinirProtocoloDiretoNoBanco(lote.Id, numeroProtocolo);
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug($"Protocolo {numeroProtocolo} definido para o lote {lote}");

                    MoverParaBackupECriarPastaSeNecessario(e.FullPath);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static string GetProtocolo(DetalhesXML detalhes, string tag)
        {
            var protocolo = detalhes.ObterRecursivoPorLabel(tag).FirstOrDefault();

            if (protocolo == null)
                return null;
            var numeroProtocolo = protocolo.Valor;
            return string.IsNullOrWhiteSpace(numeroProtocolo) ? null : numeroProtocolo;
        }

        private static void OnChangedMensagemRetornoConsultaLote(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    if (e.FullPath.Contains("-ped-") || e.FullPath.Contains("-env-") || e.FullPath.Contains("-ret-"))
                        return;

                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("OnChangedMensagemRetornoConsultaLote " + e.FullPath + " - " + e.ChangeType);

                    XDocument xmlDoc = null;

                    Retry.Do(() => xmlDoc = XDocument.Parse(File.ReadAllText(e.FullPath)), TimeSpan.FromSeconds(30), 4);
                    var detalhes = new DetalhesXML(xmlDoc.Root);

                    var status = new IntegracaoEnvioRpsUniNfe().StatusRetorno(detalhes);
                    if (status == StatusRetornoXMLEnum.Sucesso)
                        OnChangedRetorno(sender, e);
                    else if (status == StatusRetornoXMLEnum.Falha)
                        OnChangedError(sender, e);
                    else
                        OnChangedRetornoSoMover(sender, e);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static void OnChangedMensagemRetornoSoMover(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("OnChangedMensagemRetornoSoMover " + e.FullPath + " - " + e.ChangeType);

                    XDocument xmlDoc = null;

                    Retry.Do(() => xmlDoc = XDocument.Parse(File.ReadAllText(e.FullPath)), TimeSpan.FromSeconds(30), 4);
                    var detalhes = new DetalhesXML(xmlDoc.Root);

                    var status = new IntegracaoEnvioRpsUniNfe().StatusRetorno(detalhes);
                    switch (status)
                    {
                        case StatusRetornoXMLEnum.Sucesso:
                            OnChangedRetornoSoMover(sender, e);
                            break;

                        case StatusRetornoXMLEnum.Falha:
                            OnChangedErrorSoMover(sender, e);
                            break;
                    }
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        //Mapeamento de Códigos de erro
        public StatusRetornoXMLEnum StatusRetorno(DetalhesXML detalhes)
        {
            if (detalhes.ObterRecursivoPorLabel("ConsultarLoteRpsResposta").Any() && detalhes.ObterRecursivoPorLabel("ListaNfse").Any(f => f.Filhos.Any()))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Sucesso - Condição 1");
                return StatusRetornoXMLEnum.Sucesso;
            }

            if (detalhes.ObterRecursivoPorLabel("Sucesso").Any(f => f.Valor.ToLower() == "true"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Sucesso - Condição 2");

                return StatusRetornoXMLEnum.Sucesso;
            }

            if (detalhes.ObterRecursivoPorLabel("Sucesso").Any(f => f.Valor.ToLower() == "false"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 3");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("Codigo").Any(f => f.Valor == "E160"))
                if (detalhes.ObterRecursivoPorLabel("ConsultarLoteRpsResposta").Any())
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 4");
                    return StatusRetornoXMLEnum.EmProcessamento;
                }
                else
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 4");
                    return StatusRetornoXMLEnum.Falha;
                }

            if (detalhes.ObterRecursivoPorLabel("Codigo").Any(f => f.Valor == "A31"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Sucesso - Condição 5");
                return StatusRetornoXMLEnum.Sucesso;
            }

            if (detalhes.ObterRecursivoPorLabel("Codigo").Any(f => f.Valor == "AL957"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Sucesso - Condição 6");
                return StatusRetornoXMLEnum.Sucesso;
            }

            var codigosEmProcessamento = new[] { "E180", "E517", "A02", "E4", "E302" };
            if (detalhes.ObterRecursivoPorLabel("Codigo").Any(f => codigosEmProcessamento.Contains(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 7");
                return StatusRetornoXMLEnum.EmProcessamento;
            }

            if (detalhes.ObterRecursivoPorLabel("SitCodigo").Any(f => f.Valor == "111"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 8");
                return StatusRetornoXMLEnum.Falha;
            }

            var codigosFalha = ConfiguracoesTrinks.NFSe.CodigosDeErroNaEmissao;
            if (detalhes.ObterRecursivoPorLabel("Codigo").Any(f => codigosFalha.Contains(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 9");
                return StatusRetornoXMLEnum.Falha;
            }
            if (detalhes.ObterRecursivoPorLabel("ns2:Codigo").Any(f => codigosFalha.Contains(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 10");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("cStat").Any(f => f.Valor == "539"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 12");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("xMotivo").Any(f => f.Valor.ToLower().Contains("rejeicao")))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 13");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("Mensagem").Any(f => f.Valor.ToLower().Contains("incorretamente") || f.Valor.ToLower().Contains("Ocorreu um erro Inesperado")))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 14");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("cStat").Any(f => f.Valor == "100"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Sucesso - Condição 15");
                return StatusRetornoXMLEnum.Sucesso;
            }

            if (detalhes.ObterRecursivoPorLabel("xMotivo").Any(f => f.Valor.ToLower().Contains("autorizado o uso da nf-e")))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 16");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("Correcao").Any(f => !f.Valor.Contains("convertidos em NFS-e com sucesso")))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Falha - Condição 17");
                return StatusRetornoXMLEnum.Falha;
            }

            if (detalhes.ObterRecursivoPorLabel("Protocolo").Any(f => !string.IsNullOrWhiteSpace(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 18");
                return StatusRetornoXMLEnum.EmProcessamento;
            }

            if (detalhes.ObterRecursivoPorLabel("ns1:Protocolo").Any(f => !string.IsNullOrWhiteSpace(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 19");
                return StatusRetornoXMLEnum.EmProcessamento;
            }

            if (detalhes.ObterRecursivoPorLabel("ns2:Protocolo").Any(f => !string.IsNullOrWhiteSpace(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 20");
                return StatusRetornoXMLEnum.EmProcessamento;
            }

            if (detalhes.ObterRecursivoPorLabel("ns3:Protocolo").Any(f => !string.IsNullOrWhiteSpace(f.Valor)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 21");
                return StatusRetornoXMLEnum.EmProcessamento;
            }

            if (detalhes.ObterRecursivoPorLabel("Sucesso").Any(f => f.Valor == "true") && detalhes.ObterRecursivoPorLabel("QtdNotasProcessadas").Any(f => f.Valor != "0"))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.Sucesso - Condição 22");
                return StatusRetornoXMLEnum.Sucesso;
            }

            if (detalhes.ObterRecursivoPorLabel("Codigo").Any(f => f.Valor.ToLower().StartsWith("e", StringComparison.CurrentCulture)))
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug("StatusRetornoXMLEnum.EmProcessamento - Condição 11");
                return StatusRetornoXMLEnum.EmProcessamento;
            }

            LogSeTiverValor("Codigo", detalhes.ObterRecursivoPorLabel("Codigo"));
            LogSeTiverValor("SitCodigo", detalhes.ObterRecursivoPorLabel("SitCodigo"));
            LogSeTiverValor("ns2:Codigo", detalhes.ObterRecursivoPorLabel("ns2:Codigo"));
            LogSeTiverValor("cStat", detalhes.ObterRecursivoPorLabel("cStat"));
            LogSeTiverValor("xMotivo", detalhes.ObterRecursivoPorLabel("xMotivo"));
            LogSeTiverValor("Mensagem", detalhes.ObterRecursivoPorLabel("Mensagem"));
            LogSeTiverValor("Sucesso", detalhes.ObterRecursivoPorLabel("Sucesso"));

            return StatusRetornoXMLEnum.SemResultado;
        }

        private void LogSeTiverValor(string chave, List<DetalhesXML> detalhesXMLs)
        {
            foreach (var detalhesXML in detalhesXMLs)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Debug($"StatusRetornoXMLEnum.SemResultado {chave}: {detalhesXML.Valor}");
            }
        }

        public static bool CriarArquivoDeConsultaDeLote(LoteRPS lote)
        {
            var pessoaJuridica = lote.PessoaEmitente;
            var gerador = GeradorDeArquivoRPS.PorPessoaJuridica(pessoaJuridica);
            var arquivoConsulta = gerador.GerarConsultaLote(lote);

            if (arquivoConsulta == null)
                return false;

            var configuracoes = GerenciadorDeConfiguracaoUniNFE.ObterConfiguracaoNfse(pessoaJuridica);
            if (configuracoes == null)
                return false;

            var nomeArquivoComSufixo = IncluirSufixoConsultaLoteNoNomeDoArquivo(lote.NomeArquivoLote);

            var path = configuracoes.PastaXmlEnvio;
            GerarArquivo(arquivoConsulta, path + @"\" + nomeArquivoComSufixo);

            return true;
        }

        private static void OnChangedRetorno(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("OnChangedRetorno " + e.FullPath + " - " + e.ChangeType);
                    var xml = string.Empty;

                    string fullPath = e.FullPath;
                    string fileName = e.Name;

                    ProcessarOnChangedRetornoPeloNomeDoArquivo(fullPath, fileName);
                    MoverParaBackupECriarPastaSeNecessario(fullPath);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static void ProcessarOnChangedRetornoPeloNomeDoArquivo(string fullPath, string fileName)
        {
            var nomeArquivo = LimparNomeArquivo(fileName);

            if (fileName.EndsWith(".err"))
            {
                RejeitarOuDesfazerLoteOuRPSPeloNomeDoArquivo(fullPath);
            }
            else
            {
                var numeroLote = ExtrairNumeroLoteRPS(fileName);
                if (numeroLote >= 0)
                {
                    var cnpj = ObterCnpjPeloCaminhoFisico(fullPath);

                    var lote = Domain.RPS.LoteRPSRepository.ObterPorLoteECNPJ(numeroLote, cnpj);

                    if (lote == null || !File.Exists(fullPath))
                        return;

                    FinalizarProcessamentoDoLote(fullPath, nomeArquivo, lote);

                }
                else
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug($"RejeitarLoteOuRPSPeloNomeDoArquivo: sem numero de lote identificado o nome {fileName}");
            }
        }

        private static void FinalizarProcessamentoDoLote(string fullPath, string nomeArquivo, LoteRPS lote)
        {
            IDictionary<string, string> arquivos = new Dictionary<string, string>();

            string xml;
            var sr = new StreamReader(fullPath);
            xml = sr.ReadToEnd();
            sr.Close();

            xml = LimparNamespaceDoXML(xml);

            arquivos.Add(nomeArquivo, xml);

            Domain.RPS.EmissaoRPSService.MarcarRPSComoEmitidosLoteComoProcessado(lote);

            Domain.Pessoas.EnvioEmailService.EnviarEMailRPSLote(lote.PessoaEmitente.IdPessoa, lote.NumeroLote, arquivos, false);
        }

        public static int ExtrairNumeroLoteRPS(string fileName)
        {
            // Definindo o padrão da expressão regular para capturar o número após LOTE_RPS_
            string pattern = @"LOTE_RPS_(\d+)_";

            // Executando a expressão regular
            var match = Regex.Match(fileName.ToUpper(), pattern);

            if (match.Success && int.TryParse(match.Groups[1].Value, out int loteNumber))
            {
                return loteNumber;
            }

            // Retorna -1 ou algum valor de erro caso não encontre o padrão
            return -1;
        }

        private static string LimparNamespaceDoXML(string xml)
        {
            return xml
                .Replace("<ns1:", "<")
                .Replace("</ns1:", "</")
                .Replace("<ns2:", "<")
                .Replace("</ns2:", "</")
                .Replace("<ns3:", "<")
                .Replace("</ns3:", "</")
                .Replace("<ns4:", "<")
                .Replace("</ns4:", "</");
        }

        private static void OnChangedRetornoSoMover(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    LogService<IntegracaoEnvioRpsUniNfe>.Debug("OnChangedRetornoSoMover " + e.FullPath + " - " + e.ChangeType);
                    var Xml = string.Empty;
                    IDictionary<string, string> arquivos = new Dictionary<string, string>();

                    MoverParaBackupECriarPastaSeNecessario(e.FullPath);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static void OnChangedUnZip(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Changed || e.ChangeType == WatcherChangeTypes.Created)
                {
                    var unzipFile = new UnzipFile();
                    unzipFile.ZipFile = e.FullPath;

                    var ehNfe = e.FullPath.Contains("-nfe");

                    var arquivos = UnzipFile.GetFileNames(e.FullPath);

                    var directoryName = Path.GetDirectoryName(e.FullPath);
                    unzipFile.Unzip(directoryName);

                    foreach (var a in arquivos)
                    {
                        var sourceFileName = directoryName + "/" + a;
                        var targetFileName = IncluirSufixoEnvioLoteNoNomeDoArquivo(sourceFileName, ehNfe);

                        try
                        {
                            File.Move(sourceFileName, targetFileName);
                        }
                        catch (Exception)
                        {
                        }
                    }

                    File.Delete(e.FullPath);
                }
            }
            catch (Exception er)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(er.Formatada());
            }
        }

        private static void OnChangedRecuperarSeNaoProcessaremUmMinuto(object sender, FileSystemEventArgs e)
        {
            try
            {
                if (e.ChangeType == WatcherChangeTypes.Created)
                {
                    Task.Delay(TimeSpan.FromMinutes(1)).ContinueWith(_ =>
                    {
                        if (File.Exists(e.FullPath))
                        {
                            var parentDirectory = Directory.GetParent(e.FullPath).Parent.FullName;
                            var destinationPath = Path.Combine(parentDirectory, Path.GetFileName(e.FullPath));

                            if (File.Exists(destinationPath))
                            {
                                File.Delete(destinationPath);
                            }

                            File.Move(e.FullPath, destinationPath);
                            LogService<IntegracaoEnvioRpsUniNfe>.Debug($"File moved from {e.FullPath} to {destinationPath}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogService<IntegracaoEnvioRpsUniNfe>.Error(ex.Formatada());
            }
        }

        private static void ProcessarPasta(string path, string filter, EventHandler<FileSystemEventArgs> acao, bool includeSubDirectories = false)
        {
            //LogService<IntegracaoEnvioRpsUniNfe>.Info(String.Format("ProcessarPasta: {0} - {1}", path, filter));

            if (String.IsNullOrEmpty(path))
                return;

            var di = new DirectoryInfo(path);
            if (di.Exists)
            {
                var files = di.GetFiles(filter);

                foreach (var f in files)
                {
                    acao(null, new FileSystemEventArgs(WatcherChangeTypes.Created, path, f.Name));
                }

                if (includeSubDirectories)
                {
                    di.GetDirectories().ToList().ForEach(a =>
                    {
                        ProcessarPasta(a.FullName, filter, acao, includeSubDirectories);
                    });
                }
            }
        }

        private void AtualizarNomeArquivoLote(string nomeArquivo, string nomeArquivoNovo)
        {
            var lote = Domain.RPS.LoteRPSRepository.Queryable().FirstOrDefault(f => f.NomeArquivoLote == nomeArquivo);

            if (lote != null)
                lote.NomeArquivoLote = LimparNomeArquivo(nomeArquivoNovo);
        }

        public override string AtualizarSituacaoLote(LoteRPS lote)
        {
            CriarArquivoDeConsultaDeLote(lote);
            return lote.NumeroLote + "|" + lote.Id + " - Consulta realizada";
        }

        public override string RecuperarEspelhoRPS(int numeroRPS, PessoaJuridica pessoaJuridica, bool retornarEspelho = true, bool retornarChaveAcesso = true)
        {
            return "";
        }

        public static async Task ExecutarRecorrenteAsync()
        {
            while (true)
            {
                try
                {
                    var emissaoTasks = new List<Task>();

                    while (true)
                    {
                        emissaoTasks.Add(Task.Run(() => Domain.RPS.EmissaoRPSService.EmitirFilaDeRPS()));
                        await Task.Delay(10000);

                        if (emissaoTasks.Count > 3 || emissaoTasks.Any(t => t.IsCompleted))
                        {
                            await Task.WhenAll(emissaoTasks);
                            break;
                        }
                    }
                    await Task.Delay(10000);

                }
                catch (Exception e)
                {
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(e));
                    LogService<IntegracaoEnvioRpsUniNfe>.Error("Erro na rotina: EmitirFilaDeRPS - " + e.Formatada());
                }
            }
        }

        public override string EnviarNotaUnificada(List<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int idPessoaSolicitante, int? numeroLote = null)
        {
            throw new NotImplementedException();
        }

        public static class GerenciadorDeConfiguracaoUniNFE
        {

            public static void CriarPastasEConfiguracoesDaPessoaJuridicaNoUniNFESeForNecessario(PessoaJuridica pessoaJuridica)
            {
                var precisouIncluirPessoaJuridica = CadastrarPessoaJuridicaNoUniNFESeNecessario(pessoaJuridica);
                var precisouCopiarCertificado = CopiarCertificadoDigitalSeNecessario(pessoaJuridica);
                var precisouCriarPastaConfiguracoes = CriarPastaEConfiguracaoDaPessoaJuridicaSeNecessario(pessoaJuridica);

                var configuracaoTeveQueSerCriada = precisouIncluirPessoaJuridica || precisouCopiarCertificado || precisouCriarPastaConfiguracoes;

                if (configuracaoTeveQueSerCriada)
                    CriarArquivoParaAtualizacaoDasConfiguracoes(pessoaJuridica);
            }

            public static Empresa ObterConfiguracaoNfse(PessoaJuridica pessoaJuridica)
            {
                return ObterConfiguracaoNfse(pessoaJuridica.CNPJ, EhNFe(pessoaJuridica) ? 0 : 2);
            }

            public static Empresa ObterConfiguracaoNfse(string cnpj, int servico)
            {
                var nfePath = ConfiguracoesTrinks.Geral.UniNFEPath;
                //RETIRADO PARA A TAREFA http://jira.perlink.net/browse/TRINKS-8073, A CONFIGURAÇÃO DE PASTAS DO UNINFE FOI ALTERADA
                //var path = nfePath + @"\" + cnpj + (servico == 2 ? @"\nfse" : "") + @"\UniNfeConfig.xml";
                var path = nfePath + @"\" + cnpj + @"\UniNfeConfig.xml";
                var pathNfse = nfePath + @"\" + cnpj + @"\nfse\UniNfeConfig.xml";

                if (!File.Exists(path))
                {
                    path = pathNfse;
                    if (!File.Exists(path))
                        return null;
                }

                var serializer = new XmlSerializer(typeof(Empresa));
                using (var reader = new StreamReader(path))
                {
                    return (Empresa)serializer.Deserialize(reader);
                }
            }

            public static List<Empresa> ObterConfiguracoesNfse()
            {
                var nfePath = ConfiguracoesTrinks.Geral.UniNFEPath;
                var path = nfePath + @"\UniNfeEmpresa.xml";
                var xdoc = XDocument.Load(path);
                var root = xdoc.Root;

                if (root == null)
                    throw new Exception("UniNfeEmpresa.xml deve ter elemento raiz.");
                var cnpjs = from r in root.Elements("Registro")
                            select new { CNPJ = (string)r.Attribute("CNPJ"), Servico = (int)r.Attribute("Servico") };

                return cnpjs.Select(p => ObterConfiguracaoNfse(p.CNPJ, p.Servico)).Where(f => f != null).ToList();
            }

            private static void CadastrarPessoaJuridicaNoUniNFE(PessoaJuridica pessoaJuridica)
            {
                var nfePath = ConfiguracoesTrinks.Geral.UniNFEPath;
                var path = nfePath + @"\UniNfeEmpresa.xml";

                var xdoc = XDocument.Load(path);
                var elemento = new XElement("Registro", new XAttribute("CNPJ", pessoaJuridica.CNPJ),
                    new XAttribute("Servico", (EhNFe(pessoaJuridica) ? "0" : "2")));
                elemento.Add(new XElement("Nome", pessoaJuridica.NomeFantasia));
                if (xdoc.Root == null)
                    throw new Exception("UniNfeEmpresa.xml deve ter elemento raiz.");
                xdoc.Root.Add(elemento);
                xdoc.Save(path);
            }

            private static bool EhNFe(PessoaJuridica pessoaJuridica)
            {
                return Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.CidadeEmiteServicoPorNFCe(pessoaJuridica);
            }

            private static bool CadastrarPessoaJuridicaNoUniNFESeNecessario(PessoaJuridica pessoaJuridica)
            {
                var pessoaJuridicaPossuiCadastroNoUniNfe = PessoaJuridicaPossuiCadastroNoUniNFE(pessoaJuridica);
                if (!pessoaJuridicaPossuiCadastroNoUniNfe)
                    CadastrarPessoaJuridicaNoUniNFE(pessoaJuridica);

                return !pessoaJuridicaPossuiCadastroNoUniNfe;
            }

            private static bool CopiarCertificadoDigitalSeNecessario(PessoaJuridica pessoaJuridica)
            {
                var certificadoNomeArquivo = ObterCaminhoFisicoCertificado(pessoaJuridica);

                var pathCertificado = ObterCaminhoFisicoPastaCertificados();
                var certificado = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterCertificadoEhMigrarArquivosSeNecessario(pessoaJuridica);

                if (certificado == null)
                    return false;
                var dataArquivoS3 = certificado.S3FileInfo.LastWriteTime;

                var arquivoExiste = File.Exists(certificadoNomeArquivo);
                var precisaEnviar = !arquivoExiste;

                if (arquivoExiste)
                {
                    var dataArquivoLocal = File.GetLastWriteTime(certificadoNomeArquivo);
                    if (dataArquivoLocal < dataArquivoS3)
                        precisaEnviar = true;
                }

                if (precisaEnviar)
                {
                    var controleDeArquivosAmazonS3 = new ControleDeArquivosAmazonS3();
                    controleDeArquivosAmazonS3.SalvarArquivoLocalmente(pathCertificado, certificado, true, ObterNomeArquivoCertificado(pessoaJuridica));

                    CriarPastaEConfiguracaoDaPessoaJuridica(pessoaJuridica);
                }
                return precisaEnviar;
            }

            private static void CriarArquivoParaAtualizacaoDasConfiguracoes(PessoaJuridica pessoaJuridica)
            {
                var nfePath = ConfiguracoesTrinks.Geral.UniNFEPath;
                var pathGeral = CriarPastaSeNecessario(nfePath, "Geral");

                var dadosEmpresa = new DadosEmpresa
                {
                    CNPJ = pessoaJuridica.CNPJ,
                    Servico = EhNFe(pessoaJuridica) ? 0 : 2,
                    Nome = pessoaJuridica.RazaoSocial
                };

                GravarConfiguracaoNfse(dadosEmpresa, pathGeral);
            }

            private static void CriarPastaEConfiguracaoDaPessoaJuridica(PessoaJuridica pessoaJuridica)
            {
                var nfePath = ConfiguracoesTrinks.Geral.UniNFEPath;

                X509Certificate2 certificado = null;
                var gerador = GeradorDeArquivoRPS.PorPessoaJuridica(pessoaJuridica);
                if (gerador.CamposExistentes().CertificadoDigital)
                {
                    if (gerador.CamposObrigatorios().CertificadoDigital || PossuiCertificadoDigital(pessoaJuridica))
                        certificado = ObterCertificadoJaCopiadoParaOUniNFE(pessoaJuridica);
                }

                var pathEmpresa = CriarPastaSeNecessario(nfePath, pessoaJuridica.CNPJ);
                var endereco = Domain.Pessoas.EnderecoRepository.Queryable()
                    .FirstOrDefault(f => f.Pessoa.IdPessoa == pessoaJuridica.IdPessoa);
                if (endereco == null)
                    return;

                var configuracaoPadraoNFS = Domain.RPS.ConfiguracaoPadraoNFSRepository.ObterValoresDefaultPorCidade(endereco.BairroEntidade.Cidade);

                var config = new Empresa();

                var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);

                var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == pessoaJuridica.IdPessoa);

                if (pessoaJuridicaCertificadoDigital == null)
                {
                    throw new Exception("Não foi encontrado certificado digital para a pessoa jurídica: " + pessoaJuridica.NomeFantasia + " - id: " + pessoaJuridica.IdPessoa.ToString());
                }

                var senhaCertificado = criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);

                config.PastaXmlEnvio = CriarPastaSeNecessario(pathEmpresa, "Envio");
                config.PastaXmlRetorno = CriarPastaSeNecessario(pathEmpresa, "Retorno");
                config.PastaXmlEnviado = CriarPastaSeNecessario(pathEmpresa, "Enviado");
                config.PastaXmlAutorizado = CriarPastaSeNecessario(config.PastaXmlEnviado, "Autorizados");
                config.PastaXmlErro = CriarPastaSeNecessario(pathEmpresa, "Erro");
                config.PastaValidar = CriarPastaSeNecessario(pathEmpresa, "Validar");
                config.CNPJ = pessoaJuridica.CNPJ;
                config.Nome = pessoaJuridica.RazaoSocial;
                config.AmbienteCodigo = pessoaJuridica.ConfiguracaoNFe.EhProducaoConsiderandoAmbiente() ? "1" : "2"; // 1 = Produção / 2 = Homologação
                config.tpEmis = "1";
                config.UsaCertificado = "true";
                config.CertificadoInstalado = "false";
                config.CertificadoArquivo = ObterCaminhoFisicoCertificado(pessoaJuridica);
                config.CertificadoSenha = senhaCertificado;
                config.GravarRetornoTXTNFe = "false";
                config.DiasLimpeza = "0";
                config.TempoConsulta = "2";

                if (endereco.UF.CodigoIBGE == "53")
                { //Se for Destrito Federal
                    config.UnidadeFederativaCodigo = endereco.UF.CodigoIBGE;
                    config.Servico = "Nfe";
                }
                else
                {
                    config.UnidadeFederativaCodigo = endereco.BairroEntidade.Cidade.CodigoIBGE;
                    config.Servico = "Nfse";
                }

                config.CriaPastasAutomaticamente = "true";
                config.GravarEventosNaPastaEnviadosNFe = "false";
                config.GravarEventosCancelamentoNaPastaEnviadosNFe = "false";
                config.GravarEventosDeTerceiros = "false";
                config.CompactarNfe = "false";
                config.IndSinc = "true";

                config.XMLDanfeMonNFe = "false";
                config.XMLDanfeMonProcNFe = "false";
                config.XMLDanfeMonDenegadaNFe = "false";

                config.AdicionaEmailDanfe = "true";
                config.diretorioSalvarComo = "AM";

                if (certificado != null)
                {
                    config.Certificado = certificado.Subject;
                    config.CertificadoDigitalThumbPrint = certificado.Thumbprint;
                }

                if (endereco.UF.CodigoIBGE == "53")
                { //Se for Destrito Federal
                    GravarConfiguracaoNfse(config, pathEmpresa);
                }
                else
                {
                    var pathNfse = CriarPastaSeNecessario(pathEmpresa, "nfse");
                    GravarConfiguracaoNfse(config, pathNfse);
                }

                IniciarVerificacaoDasPastas(config);
            }

            private static bool CriarPastaEConfiguracaoDaPessoaJuridicaSeNecessario(PessoaJuridica pessoaJuridica)
            {
                var pessoaJuridicaPossuiPasta = PessoaJuridicaPossuiPasta(pessoaJuridica);
                if (!pessoaJuridicaPossuiPasta)
                    CriarPastaEConfiguracaoDaPessoaJuridica(pessoaJuridica);

                return !pessoaJuridicaPossuiPasta;
            }

            private static string CriarPastaSeNecessario(string path, string nome)
            {
                var pathCompleto = path + @"\" + nome;
                var di = new DirectoryInfo(pathCompleto);
                if (!di.Exists)
                    di.Create();
                return pathCompleto;
            }

            private static bool PessoaJuridicaPossuiCadastroNoUniNFE(PessoaJuridica pessoaJuridica)
            {
                if (string.IsNullOrWhiteSpace(pessoaJuridica.CNPJ))
                    throw new Exception("Pessoa jurídica deve possuir CNPJ");

                var path = ConfiguracoesTrinks.Geral.UniNFEPath;

                var xdoc = XDocument.Load(path + @"\UniNfeEmpresa.xml");
                var configuracacao = from r in xdoc.Descendants("Registro")
                                     where (string)r.Attribute("CNPJ") == pessoaJuridica.CNPJ
                                     select r;

                return configuracacao.Any();
            }

            private static bool PessoaJuridicaPossuiPasta(PessoaJuridica pessoaJuridica)
            {
                var path = ConfiguracoesTrinks.Geral.UniNFEPath;
                var pathCompleto = path + @"\" + pessoaJuridica.CNPJ;
                var di = new DirectoryInfo(pathCompleto);
                return di.Exists;
            }

            private static void GravarConfiguracaoNfse(DadosEmpresa dadosEmpresa, string path)
            {
                var serializer = new XmlSerializer(typeof(DadosEmpresa));
                using (TextWriter writer = new StreamWriter(path + @"\" + dadosEmpresa.CNPJ + "-alt-con.xml"))
                {
                    serializer.Serialize(writer, dadosEmpresa);
                }
            }

            private static void GravarConfiguracaoNfse(Empresa config, string path)
            {
                var serializer = new XmlSerializer(typeof(Empresa));
                using (TextWriter writer = new StreamWriter(path + @"\UniNfeConfig.xml"))
                {
                    serializer.Serialize(writer, config);
                }
            }

            private static string ObterCaminhoFisicoCertificado(PessoaJuridica pessoaJuridica)
            {
                var pathCertificado = ObterCaminhoFisicoPastaCertificados();
                var certificadoNomeArquivo = pathCertificado + @"\" + ObterNomeArquivoCertificado(pessoaJuridica);
                return certificadoNomeArquivo;
            }

            private static string ObterCaminhoFisicoPastaCertificados()
            {
                var nfePath = ConfiguracoesTrinks.Geral.UniNFEPath;
                var pathCertificado = CriarPastaSeNecessario(nfePath, "certificados");
                return pathCertificado;
            }

            private static X509Certificate2 ObterCertificadoJaCopiadoParaOUniNFE(PessoaJuridica pessoaJuridica)
            {
                var caminhoFisicoCertificado = ObterCaminhoFisicoCertificado(pessoaJuridica);
                bool certificadoExiste = PossuiCertificadoDigital(pessoaJuridica);
                if (!certificadoExiste)
                    throw new Exception(string.Format("Certificado digital \"{0}\" não encontrado.", caminhoFisicoCertificado));

                X509Certificate2 x509Cert;

                using (var fs = new FileStream(caminhoFisicoCertificado, FileMode.Open, FileAccess.Read))
                {
                    var buffer = new byte[fs.Length];
                    fs.Read(buffer, 0, buffer.Length);

                    var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);

                    var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == pessoaJuridica.IdPessoa);
                    var senhaCertificado = criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);

                    x509Cert = new X509Certificate2(buffer,
                        senhaCertificado ?? "",
                        X509KeyStorageFlags.MachineKeySet |
                        X509KeyStorageFlags.PersistKeySet |
                        X509KeyStorageFlags.Exportable);
                }

                return x509Cert;
            }

            private static bool PossuiCertificadoDigital(PessoaJuridica pessoaJuridica)
            {
                var caminhoFisicoCertificado = ObterCaminhoFisicoCertificado(pessoaJuridica);
                return File.Exists(caminhoFisicoCertificado);
            }

            private static string ObterNomeArquivoCertificado(PessoaJuridica pessoaJuridica)
            {
                return pessoaJuridica.CNPJ + ".pfx";
            }
        }
    }
}