﻿using Perlink.Trinks.Pessoas;
using Perlink.Trinks.RPS.GeradorDeArquivo;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.RPS.Repositories
{
    public partial class ConfiguracaoPadraoNFSRepository : IConfiguracaoPadraoNFSRepository
    {
        public bool CidadePossuiNFeImplementada(Cidade cidade)
        {
            return GeradorDeArquivoRPS.ExisteImplementacaoParaCidade(cidade.CodigoIBGE);
        }

        public ConfiguracaoPadraoNFS ObterValoresDefaultPorCidade(Cidade cidade)
        {
            var identificadorPadrao = GeradorDeArquivoRPS.ObterPadraoCidadeDoUniNFe(cidade.CodigoIBGE);

            if (identificadorPadrao == null) return null;

            var configuracaoPadrao = Queryable();
            return configuracaoPadrao.FirstOrDefault(f => f.Identificador == identificadorPadrao);
        }

        public ConfiguracaoPadraoNFS ObterValoresDefaultPorPadrao(string identificadorPadrao)
        {
            var configuracaoPadrao = Queryable();
            return configuracaoPadrao.FirstOrDefault(f => f.Identificador == identificadorPadrao);
        }

        public bool PadraoEstaComPilotoAtivo(Cidade cidade)
        {
            var configuracao = ObterValoresDefaultPorCidade(cidade);
            return configuracao != null && configuracao.PilotoAtivo;
        }

        public bool CidadePossuiNFeAtiva(Cidade cidade)
        {
            var identificadorPadrao = GeradorDeArquivoRPS.ObterPadraoCidadeDoUniNFe(cidade.CodigoIBGE);

            var configuracaoPadrao = Queryable();
            return configuracaoPadrao.Any(f => f.Identificador == identificadorPadrao && f.Ativo) || cidade.Nome == "Brasília";
        }

        public bool CidadePossuiEmissaoRpsSequencial(Cidade cidade)
        {
            var configuracao = ObterValoresDefaultPorCidade(cidade);
            return configuracao != null && configuracao.RpsSequencial && configuracao.Ativo;
        }

        public IEnumerable<string> ListarPadroesNfseNome()
        {
            return Queryable().Select(p => p.Identificador).ToList() ;
        }

    }
}