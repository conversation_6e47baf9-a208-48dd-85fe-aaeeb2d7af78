﻿using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.RPS.Repositories
{
    public partial class EmissaoRPSRepository : IEmissaoRPSRepository
    {

        public int? ObterNumeroDeRPSEmitidoComTransacao(int idTransacao)
        {
            return Queryable()
                .Where(e => e.Transacao.Id == idTransacao && e.DadosRPSTransacao.StatusRPS == StatusRpsEnum.Emitido)
                .Select(e => (int?)e.Numero)
                .FirstOrDefault();
        }

        public EmissaoRPS ObterRPSEmitidoOuCanceladoPorNumero(int numero, int idPessoaJuridica)
        {
            return Queryable().Where(e => e.Pessoa<PERSON>uri<PERSON>a.IdPessoa == idPessoaJuridica && e.Numero == numero
                && (e.DadosRPSTransacao.StatusRPS == StatusRpsEnum.Emitido
                || e.DadosRPSTransacao.StatusRPS == StatusRpsEnum.ACancelar
                || e.DadosRPSTransacao.StatusRPS == StatusRpsEnum.Cancelado))
                .FirstOrDefault();
        }

        public List<EmissaoRPS> ObterRPSNaoEmitidosEPendentesPorLote(int idPessoaJuridica, int numeroLote)
        {
            return Queryable()
                .Where(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica && e.Lote == numeroLote && (e.DadosRPSTransacao.StatusRPS == StatusRpsEnum.NaoEmitido || e.DadosRPSTransacao.StatusRPS == StatusRpsEnum.Pendente))
                .ToList();
        }
        public IQueryable<EmissaoRPS> ListarPorPJLote(int idPessoa, int numeroLote)
        {
            return Queryable().Where(f => f.PessoaJuridica.IdPessoa == idPessoa && f.Lote == numeroLote);
        }

    }
}
