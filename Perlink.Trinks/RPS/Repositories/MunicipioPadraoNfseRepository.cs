﻿using System.Linq;

namespace Perlink.Trinks.RPS.Repositories
{
    public partial class MunicipioPadraoNfseRepository : IMunicipioPadraoNfseRepository
    {
        public string BuscarPadraoPorCodIbge(string codIbge)
        {
            var padraoMunicipio = BuscarPorCodIbge(codIbge);
            return padraoMunicipio?.Identificador;
        }

        public MunicipioPadraoNfse BuscarPorCodIbge(string codIbge)
        {
            return Queryable().Where(p => p.CodigoIBGE.Equals(codIbge)).FirstOrDefault();
        }

        public void SalvarPadraoNfseCodIbge(string codIbge, string padraoNfse)
        {

            var padraoMunicipio = BuscarPorCodIbge(codIbge) ?? new MunicipioPadraoNfse { CodigoIBGE = codIbge };

            padraoMunicipio.Identificador = padraoNfse;

            if (padraoMunicipio.Id == 0)
            {
                SaveNew(padraoMunicipio);
            }
            else
            {
                Update(padraoMunicipio);
            }
        }
        
    }
}
