﻿using Perlink.Trinks.Pessoas;
using System.Collections.Generic;

namespace Perlink.Trinks.RPS.Repositories
{
    public partial interface IConfiguracaoPadraoNFSRepository
    {
        ConfiguracaoPadraoNFS ObterValoresDefaultPorCidade(Cidade cidade);
        ConfiguracaoPadraoNFS ObterValoresDefaultPorPadrao(string identificadorPadrao);
        bool PadraoEstaComPilotoAtivo(Cidade cidade);
        bool CidadePossuiNFeImplementada(Cidade cidade);
        bool CidadePossuiNFeAtiva(Cidade cidade);
        bool CidadePossuiEmissaoRpsSequencial(Cidade cidade);
        IEnumerable<string> ListarPadroesNfseNome();
    }
}
