﻿using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Perlink.Trinks.RPS.GeradorDeArquivo
{
    public class GeradorDeArquivoRPS_InvoicySequencial : GeradorDeArquivoRPS_Base
    {
        public GeradorDeArquivoRPS_InvoicySequencial(string codigoIbgeMunicipio) : base(codigoIbgeMunicipio)
        {
        }

        public override int LimiteDeItensPorLoteWebservice
        {
            get
            {
                return 100;
            }
        }

        public override CamposExistentes CamposExistentes()
        {
            return new CamposExistentes
            {
                AliquotaISS = _codigoIbgeMunicipio == "3306305" ? false : true,
                AliquotaISSNaoFormatada = _codigoIbgeMunicipio == "3306305" ? true : false,
                CodigoRegimeEspecialTributacao = true,
                ItemListaServico = true,
                CodigoTributacaoMunicipio = true,
                CertificadoDigital = true,
                Cnae = true,
                CpfUsuarioCadastrado = true,
                SenhaUsuarioCadastrado = true,
                CmcPrestador = true
            };
        }

        public override CamposObrigatorios CamposObrigatorios()
        {
            return new CamposObrigatorios
            {
                AliquotaISS = _codigoIbgeMunicipio == "3306305" ? false : true,
                AliquotaISSNaoFormatada = _codigoIbgeMunicipio == "3306305" ? true : false,
                CodigoRegimeEspecialTributacao = false,
                ItemListaServico = true,
                CodigoTributacaoMunicipio = false,
                CertificadoDigital = true,
                CpfUsuarioCadastrado = false,
                SenhaUsuarioCadastrado = false,
                CmcPrestador = false
            };
        }

        public override bool EnviaPorWebService(PessoaJuridicaConfiguracaoNFe configuracaoNFe)
        {
            return true;
        }

        public override MemoryStream Gerar(IEnumerable<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int lote)
        {
            throw new NotImplementedException();
        }

        public override bool PermiteDownload()
        {
            return false;
        }

        public override string OrientacaoItemListaServico()
        {
            return "Ex.: 06.01";
        }

        public override string OrientacaoCodigoRegimeEspecialTributacao()
        {
            return "Geralmente um número entre 1 e 6";
        }

        public override bool CPFClienteObrigatorio
        {
            get
            {
                var cidadesQueExigemCPF = new[] { "3525003", "2919207" };

                return cidadesQueExigemCPF.Contains(_codigoIbgeMunicipio);
            }
        }
    }
}
