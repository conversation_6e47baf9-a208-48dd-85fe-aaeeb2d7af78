﻿using Perlink.Shared.Encryptor;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Trinks.Pessoas;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Xml;

namespace Perlink.Trinks.RPS.GeradorDeArquivo
{

    public delegate void CryptographicExceptionHandler(object sender, EventArgs args);

    public static class AssinaturaDigital
    {

        public static MemoryStream AssinarXML(Stream xmlStream, string tagAssinatura, X509Certificate2 x509Cert)
        {
            // Create a new XML document.
            var xmlDoc = new XmlDocument();

            // Format the document to ignore white spaces.
            xmlDoc.PreserveWhitespace = false;

            // Load the passed XML file using it’s name.
            xmlDoc.Load(xmlStream);

            return AssinarXML(xmlDoc, tagAssinatura, x509Cert);
        }

        public static MemoryStream AssinarXML(XmlDocument xmlDoc, string tagAssinatura, X509Certificate2 x509Cert)
        {
            xmlDoc.PreserveWhitespace = false;

            if (xmlDoc.GetElementsByTagName(tagAssinatura).Count == 0)
            {
                throw new Exception("A tag de assinatura " + tagAssinatura.Trim() + " não existe no XML. (Código do Erro: 5)");
            }

            XmlNodeList lists = xmlDoc.GetElementsByTagName(tagAssinatura);

            foreach (XmlNode childNodes in lists)
            {
                if (childNodes.Name == "Signature") continue;                Reference reference = ObterReferencia(childNodes);
                
                SignedXml signedXml = new SignedXml(xmlDoc);
                
                // Use the modern approach to get the RSA private key
                using (RSA rsa = x509Cert.GetRSAPrivateKey())
                {
                    if (rsa == null)
                        throw new InvalidOperationException("Certificado não possui chave privada RSA ou a chave não está acessível.");
                    
                    signedXml.SigningKey = rsa;
                    signedXml.AddReference(reference);

                    KeyInfo keyInfo = new KeyInfo();

                    // Load the certificate into a KeyInfoX509Data object
                    // and add it to the KeyInfo object.
                    keyInfo.AddClause(new KeyInfoX509Data(x509Cert));

                    // Add the KeyInfo object to the SignedXml object.
                    signedXml.KeyInfo = keyInfo;
                    signedXml.ComputeSignature();

                    XmlElement xmlDigitalSignature = signedXml.GetXml();

                    childNodes.ParentNode.AppendChild(xmlDoc.ImportNode(xmlDigitalSignature, true));
                }
            }

            var retorno = new MemoryStream();

            XmlTextWriter wr = new XmlTextWriter(retorno, Encoding.UTF8);
            wr.Formatting = Formatting.None;

            xmlDoc.Save(wr);

            retorno.Flush();
            retorno.Position = 0;

            return retorno;
        }

        public static MemoryStream AssinarXML(XmlDocument xmlDoc, string tagAssinatura, PessoaJuridica pessoaCertificado)
        {
            var x509Cert = ObterCertificadoDaPJ(pessoaCertificado);
            return AssinarXML(xmlDoc, tagAssinatura, x509Cert);
        }

        public static byte[] HashAndSignBytes(byte[] DataToSign, RSAParameters Key)
        {
            try
            {
                return HashAndSignBytes(DataToSign, Key, new SHA1CryptoServiceProvider());
            }
            catch (CryptographicException e)
            {
                Console.WriteLine(e.Message);

                return null;
            }
        }

        public static byte[] HashAndSignBytes(byte[] DataToSign, RSAParameters Key, object hashAlgorithm)
        {
            try
            {
                // Create a new instance of RSACryptoServiceProvider using the
                // key from RSAParameters.
                RSACryptoServiceProvider RSAalg = new RSACryptoServiceProvider();

                RSAalg.ImportParameters(Key);

                return RSAalg.SignData(DataToSign, hashAlgorithm);
            }
            catch (CryptographicException e)
            {
                Console.WriteLine(e.Message);

                return null;
            }
        }

        public static string ObterAssinaturaString(string conteudo, PessoaJuridica pessoaCertificado)
        {
            var certificado = ObterCertificadoDaPJ(pessoaCertificado);

            return ObterAssinaturaString(conteudo, certificado);
        }

        public static string ObterAssinaturaString(string conteudo, X509Certificate2 certificado)
        {
            var encrypted = ObterAssinaturaArrayDeByte(conteudo, certificado);
            var retorno = Convert.ToBase64String(encrypted);

            return retorno;
        }        public static byte[] ObterAssinaturaArrayDeByte(string conteudo, X509Certificate2 certificado)
        {
            var data = Encoding.UTF8.GetBytes(conteudo);
            
            // Use the modern approach to get RSA private key
            using (RSA rsa = certificado.GetRSAPrivateKey())
            {
                if (rsa == null)
                    throw new InvalidOperationException("Certificado não possui chave privada RSA ou a chave não está acessível.");
                
                // Sign data directly using RSA object instead of exporting parameters
                // This works with HSM and non-exportable keys
                return rsa.SignData(data, HashAlgorithmName.SHA1, RSASignaturePadding.Pkcs1);
            }
        }

        public static X509Certificate2 ObterCertificadoDaPJ(PessoaJuridica pessoaJuridica)
        {
            var certificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.ObterPorPessoaJuridica(pessoaJuridica);

            var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
            var senhaCertificado = criptografia.Decrypt(certificadoDigital.SenhaCertificadoCriptografada);

            ArquivoAmazonS3 arquivoCertificado = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterCertificadoEhMigrarArquivosSeNecessario(pessoaJuridica);
            if (arquivoCertificado == null)
                return null;

            var fs = arquivoCertificado.S3FileInfo.OpenRead();            var buffer = new byte[fs.Length];
            fs.Read(buffer, 0, buffer.Length);

            // Try different key storage flags to improve compatibility with various certificate types
            X509Certificate2 x509Cert = null;
            var keyStorageFlags = new[]
            {
                X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.Exportable,
                X509KeyStorageFlags.UserKeySet | X509KeyStorageFlags.Exportable,
                X509KeyStorageFlags.Exportable,
                X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet,
                X509KeyStorageFlags.DefaultKeySet | X509KeyStorageFlags.Exportable
            };

            foreach (var flags in keyStorageFlags)
            {
                try
                {
                    x509Cert = new X509Certificate2(buffer, senhaCertificado ?? "", flags);
                    // Test if we can access the private key
                    using (var rsa = x509Cert.GetRSAPrivateKey())
                    {
                        if (rsa != null) break; // Success
                    }
                }
                catch
                {
                    x509Cert?.Dispose();
                    x509Cert = null;
                }
            }

            if (x509Cert == null)
            {
                // Fallback to the original approach if all attempts fail
                x509Cert = new X509Certificate2(buffer, senhaCertificado ?? "", X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.Exportable);
            }

            return x509Cert;
        }

        public static bool XMLEstaAssinado(StreamReader sr, string tagAssinatura)
        {
            string arqXML = sr.ReadToEnd();

            bool retorno = false;
            try
            {
                var doc = new XmlDocument();
                doc.Load(arqXML);

                if (doc.GetElementsByTagName(tagAssinatura)[0].LastChild.Name == "Signature")
                    retorno = true;
            }
            catch { }

            return retorno;
        }

        private static Reference ObterReferencia(XmlNode childNodes)
        {
            var reference = new Reference();
            reference.Uri = "";

            // pega o uri que deve ser assinada
            var childElemen = (XmlElement)childNodes;
            if (childElemen.GetAttributeNode("Id") != null)
            {
                reference.Uri = "#" + childElemen.GetAttributeNode("Id").Value;
            }
            else if (childElemen.GetAttributeNode("id") != null)
            {
                reference.Uri = "#" + childElemen.GetAttributeNode("id").Value;
            }

            // Add an enveloped transformation to the reference.
            XmlDsigEnvelopedSignatureTransform env = new XmlDsigEnvelopedSignatureTransform();
            reference.AddTransform(env);

            XmlDsigC14NTransform c14 = new XmlDsigC14NTransform();
            reference.AddTransform(c14);
            return reference;
        }
    }
}