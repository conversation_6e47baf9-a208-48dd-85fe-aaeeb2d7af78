﻿using Perlink.Shared.Numeric;
using Perlink.Shared.Security;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.RPS.Enums;
using Perlink.Trinks.RPS.GeradorDeArquivo.DSF;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace Perlink.Trinks.RPS.GeradorDeArquivo
{

    public class GeradorDeArquivoRPS_DSF : GeradorDeArquivoRPS_Base
    {

        public GeradorDeArquivoRPS_DSF(string codigoIbgeMunicipio) : base(codigoIbgeMunicipio)
        {
        }

        public override bool CPFClienteObrigatorio
        {
            get
            {
                if (_codigoIbgeMunicipio == "3170206") return true; // Uberlandia

                return base.CPFClienteObrigatorio;
            }
        }

        public override bool EnderecoDoClienteEhObrigatorio
        {
            get
            {
                if (_codigoIbgeMunicipio == "3170206") return true; // Uberlandia

                return base.EnderecoDoClienteEhObrigatorio;
            }
        }

        public override CamposExistentes CamposExistentes()
        {
            return new CamposExistentes
            {
                AliquotaISS = true,
                CodigoTributacaoMunicipio = true,
                CodigoOptanteSimplesNacional = false,
                ItemListaServico = true,
                CertificadoDigital = true,
                Cnae = true
            };
        }

        public override CamposObrigatorios CamposObrigatorios()
        {
            return new CamposObrigatorios
            {
                AliquotaISS = true,
                CodigoTributacaoMunicipio = true,
                CodigoOptanteSimplesNacional = false,
                ItemListaServico = true,
                CertificadoDigital = true,
                Cnae = true
            };
        }

        public override bool EnviaPorWebService(PessoaJuridicaConfiguracaoNFe configuracaoNFe)
        {
            return true;
        }

        public override MemoryStream Gerar(IEnumerable<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica,
            int lote)
        {
            var configuracao = pessoaJuridica.ConfiguracaoNFe;

            if (configuracao == null)
                throw new ArgumentException(pessoaJuridica.NomeFantasia +
                                    " não está configurado para NFS-e");

            if (configuracao.CodigoTributacaoMunicipio == "")
                throw new ArgumentException(
                    "Não consta tipo de tributação para o estabelecimento informado. Por favor, verifique se o estabelecimento está configurado.");

            var endereco = pessoaJuridica.Enderecos.First(f => f.BairroEntidade != null);
            var cidade = endereco.BairroEntidade.Cidade;
            var codigoSIAFI = ObterCodigoSIAFIpeloCodigoIBGEDaCidade(int.Parse(cidade.CodigoIBGE));

            var dados = new ReqEnvioLoteRPS
            {
                Cabecalho = new ReqEnvioLoteRPSCabecalho()
                {
                    CodCidade = codigoSIAFI,
                    MetodoEnvio = tpMetodoEnvio.WS,
                    ValorTotalServicos = registros.Sum(p => p.ValorTotal),
                    dtInicio = registros.Min(p => p.DataTransacao),
                    dtFim = registros.Max(p => p.DataTransacao),
                    QtdRPS = registros.Count().ToString(),
                    RazaoSocialRemetente = pessoaJuridica.RazaoSocial,
                    CPFCNPJRemetente = pessoaJuridica.CNPJ
                },
                Lote = new tpLote
                {
                    Id = "L" + lote,
                    RPS = new tpRPS[0]
                }
            };

            foreach (var r in registros)
            {
                var dadosRPSTransacao = r.DadosRPS;
                var emissaoVigente = dadosRPSTransacao.EmissoesRPS.LastOrDefault();

                var numero = 0;
                if (emissaoVigente != null)
                    numero = emissaoVigente.Numero;
                var emissaoSubstituida = dadosRPSTransacao.EmissoesRPS.LastOrDefault(f => f.Numero != numero);
                var numeroRPSSubstituido = emissaoSubstituida != null ? emissaoSubstituida.Numero : 0;
                tpTributacao tpTributacao;
                try
                {
                    tpTributacao = (tpTributacao)Enum.Parse(typeof(tpTributacao), configuracao.CodigoTributacaoMunicipio, true);
                }
                catch (Exception)
                {
                    throw new ArgumentException("Tipo tributação inválido");
                }

                var rps = new tpRPS
                {
                    NumeroRPS = emissaoVigente.Numero,
                    SerieRPS = tpSerieRPS.NF,
                    DataEmissaoRPS = r.DataTransacao,
                    Operacao = tpOperacao.A,
                    Tributacao = tpTributacao,
                    SituacaoRPS = r.StatusRPS == StatusRpsEnum.ACancelar ? tpSituacaoRPS.C : tpSituacaoRPS.N
                };

                if (r.StatusRPS == StatusRpsEnum.ACancelar)
                    rps.MotCancelamento = string.IsNullOrWhiteSpace(r.MotivoCancelamento) ? "Não informado" : r.MotivoCancelamento;

                rps.Itens = new tpItens[0];
                rps.SeriePrestacao = sbyte.Parse("99");

                rps.CodigoAtividade = int.Parse(configuracao.ItemListaServico.SomenteNumeros());
                rps.AliquotaAtividade = configuracao.AliquotaISS;
                rps.MunicipioPrestacao = ObterCodigoSIAFIpeloCodigoIBGEDaCidade(int.Parse(cidade.CodigoIBGE));
                rps.MunicipioPrestacaoDescricao = cidade.Nome;
                rps.DescricaoRPS = string.Join("|", r.NomesServicos) + (r.DadosFiscaisDoServicoNaoInformado ? "" : "||" + r.TextoInformandoValorDosImpostos);

                var telefone = pessoaJuridica.Telefones.FirstOrDefault(t => t.Ddi == DdiConstants.Brasil);

                #region Prestador

                rps.DDDPrestador = telefone != null ? telefone.DDD : "00";
                rps.TelefonePrestador = /*telefone != null ? telefone.Numero : */"00000000";
                rps.InscricaoMunicipalPrestador = long.Parse(pessoaJuridica.InscricaoMunicipal.SomenteNumeros());
                rps.RazaoSocialPrestador = pessoaJuridica.RazaoSocial;

                #endregion Prestador

                #region Tomador

                rps.InscricaoMunicipalTomador = "0000000";
                rps.CPFCNPJTomador = !string.IsNullOrWhiteSpace(r.Cpf) ? r.Cpf : "77777777777";
                rps.RazaoSocialTomador = r.NomeCliente;

                var enderecoTomador = r.EnderecoDoCliente;

                if (enderecoTomador == null || !enderecoTomador.EnderecoEstahCompleto())
                    enderecoTomador = new EnderecoDeClienteEstabelecimento
                    {
                        Cidade = endereco.Cidade,
                        Bairro = "-",
                        Cep = "00000000",
                        Numero = "0",
                        UF = endereco.UF,
                        Logradouro = "-"
                    };

                if (_codigoIbgeMunicipio == "3509502"
                    && !enderecoTomador.Cidade.Contains("Campinas")
                    && (string.IsNullOrWhiteSpace(rps.CPFCNPJTomador) || rps.CPFCNPJTomador == "77777777777"))
                {
                    enderecoTomador = new EnderecoDeClienteEstabelecimento
                    {
                        Cidade = endereco.Cidade,
                        Bairro = endereco.Bairro,
                        Cep = endereco.Cep,
                        Numero = endereco.Numero,
                        UF = endereco.UF,
                        Logradouro = endereco.Logradouro,
                        Complemento = endereco.Complemento
                    };
                }

                //TODO: VERIFICAR COMO FICA O TRATAMENTO PARA A CIDADE DO CLIENTE, HOJE
                // É UTILIZADA A CIDADE DO ESTABELECIMENTO EM AMBOS OS CAMPOS
                rps.CidadeTomador = codigoSIAFI;
                rps.TipoLogradouroTomador = "Rua";
                rps.LogradouroTomador = enderecoTomador.Logradouro;
                rps.NumeroEnderecoTomador = enderecoTomador.Numero;
                rps.ComplementoEnderecoTomador = enderecoTomador.Complemento;

                string cep = enderecoTomador.Cep.SomenteNumeros();
                if (!string.IsNullOrWhiteSpace(cep) && cep.Length == 8)
                    rps.CEPTomador = int.Parse(cep);

                rps.TipoBairroTomador = "Bairro";
                rps.BairroTomador = enderecoTomador.Bairro;
                rps.CidadeTomadorDescricao = enderecoTomador.Cidade;
                rps.EmailTomador = string.IsNullOrWhiteSpace(r.Email) ? "-" : r.Email;

                rps.DDDTomador = "00";
                rps.TelefoneTomador = "00000000";

                #endregion Tomador

                var tpItens = rps.Itens.ToList();
                tpItens.Add(new tpItens()
                {
                    DiscriminacaoServico = string.Join("|", r.NomesServicos),
                    Quantidade = r.NomesServicos.Count,
                    ValorTotal = r.ValorTotal,
                    ValorUnitario = Math.Round(r.ValorTotal / r.NomesServicos.Count, 3)
                });
                rps.Itens = tpItens.ToArray();

                if (numeroRPSSubstituido > 0)
                {
                    rps.NumeroRPSSubstituido = numeroRPSSubstituido;
                    rps.SerieRPSSubstituido = "";
                }

                rps.Assinatura = GerarHashParaAssinatura(rps);

                var tpRpss = dados.Lote.RPS.ToList();
                tpRpss.Add(rps);
                dados.Lote.RPS = tpRpss.ToArray();
            }

            var retorno = new MemoryStream();
            var serializer = new XmlSerializer(typeof(ReqEnvioLoteRPS));
            XmlSerializerNamespaces ns1 = new XmlSerializerNamespaces();
            ns1.Add("xsi", "http://www.w3.org/2001/XMLSchema-instance");
            ns1.Add("tipos", "http://localhost:8080/WsNFe2/tp");
            ns1.Add("ns1", "http://localhost:8080/WsNFe2/lote");

            serializer.Serialize(retorno, dados, ns1);
            retorno.Flush();
            retorno.Seek(0, SeekOrigin.Begin);
            return retorno;
        }

        // Gerar assinatura (http://nfse.campinas.sp.gov.br/NotaFiscal/cpqPDF/WebService.pdf)
        // Página 09.
        private string GerarHashParaAssinatura(tpRPS rps)
        {
            StringBuilder sbHashContent = new StringBuilder();
            sbHashContent.Append(rps.InscricaoMunicipalPrestador.ToString("D11"));
            sbHashContent.Append(rps.SerieRPS.ToString().PadRight(5));
            sbHashContent.Append(rps.NumeroRPS.ToString("D12"));
            sbHashContent.Append(rps.DataEmissaoRPS.ToString("yyyyMMdd"));
            sbHashContent.Append(Shared.Enums.EnumActions.GetEnumText(rps.Tributacao).PadRight(2));
            sbHashContent.Append(Shared.Enums.EnumActions.GetEnumText(rps.SituacaoRPS));
            sbHashContent.Append(rps.TipoRecolhimento == tpTipoRecolhimento.A ? "N" : "S");
            sbHashContent.Append(rps.Itens.Sum(p => p.ValorTotal).RemoveDecimalPoint().PadLeft(15, '0'));

            sbHashContent.Append(rps.Deducoes == null
                ? 0.ToString("D15")
                : rps.Deducoes.Sum(p => p.ValorDeduzir).RemoveDecimalPoint().PadLeft(15, '0'));

            sbHashContent.Append(rps.CodigoAtividade.ToString("D10"));
            sbHashContent.Append(rps.CPFCNPJTomador.PadLeft(14, '0'));
            return SHA1Generator.Hash(sbHashContent.ToString());
        }

        private static uint ObterCodigoSIAFIpeloCodigoIBGEDaCidade(int codigoIBGE)
        {
            switch (codigoIBGE)
            {
                case (int)CodigoIBGEPadraoDSFEnum.Belem:
                    return (uint)CodigoSIAFIEnum.Belem;

                case (int)CodigoIBGEPadraoDSFEnum.Campinas:
                    return (uint)CodigoSIAFIEnum.Campinas;

                case (int)CodigoIBGEPadraoDSFEnum.CampoGrande:
                    return (uint)CodigoSIAFIEnum.CampoGrande;

                case (int)CodigoIBGEPadraoDSFEnum.NovaIguaçu:
                    return (uint)CodigoSIAFIEnum.NovaIguaçu;

                case (int)CodigoIBGEPadraoDSFEnum.SaoLuis:
                    return (uint)CodigoSIAFIEnum.SaoLuis;

                case (int)CodigoIBGEPadraoDSFEnum.Sorocaba:
                    return (uint)CodigoSIAFIEnum.Sorocaba;

                case (int)CodigoIBGEPadraoDSFEnum.Teresina:
                    return (uint)CodigoSIAFIEnum.Teresina;

                case (int)CodigoIBGEPadraoDSFEnum.Uberlandia:
                    return (uint)CodigoSIAFIEnum.Uberlandia;

                default:
                    throw new ArgumentException("O município cadastrado para este estabelecimento não utiliza o padrão DSF");
            }
        }

        public override void ValidarConfiguracao(PessoaJuridicaConfiguracaoNFe configuracao, TipoGerarPara tipoDeGeracao)
        {
            base.ValidarConfiguracao(configuracao, tipoDeGeracao);

            //var codigosTributacaoAceitaveis = new[] { "F", "K", "E", "T", "H", "G", "N", "M" };
            //if (!codigosTributacaoAceitaveis.Contains(configuracao.CodigoTributacaoMunicipio)) {
            //    throw new ArgumentException("O código de Tipo de Tributação é inválido (" + configuracao.CodigoTributacaoMunicipio + @"). Deve ser um dos valores abaixo:
            //        F - Imune
            //        K - Depósito em Juízo
            //        E - Não Incidente no Município
            //        T - Tributável
            //        H - Tributável Simples Nacional
            //        G - Tributável Fixo
            //        N - Não Tributável
            //        M - Tributação Microempresário Individual (MEI)");
            //}

            //int lengthItemListaServico = configuracao.ItemListaServico.SomenteNumeros().Length;
            //if (lengthItemListaServico != 9 && lengthItemListaServico != 4)
            //    throw new ArgumentException("O código Item Lista Serviço deve ter 4 ou 9 dígitos.");
        }

        public override bool PermiteDownload()
        {
            return false;
        }

        public override string OrientacaoCodigoTributacaoMunicipio()
        {
            return "Ex.: H, M, T";
        }

        public override string OrientacaoItemListaServico()
        {
            return "Ex.: 960250100";
        }

        public override string SerieDaNota()
        {
            return "99";
        }
    }
}