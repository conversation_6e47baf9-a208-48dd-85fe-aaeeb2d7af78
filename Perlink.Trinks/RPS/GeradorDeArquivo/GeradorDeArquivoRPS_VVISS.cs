﻿using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.RPS.Enums;
using Perlink.Trinks.RPS.GeradorDeArquivo.VVISS;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Serialization;

namespace Perlink.Trinks.RPS.GeradorDeArquivo
{

    public class GeradorDeArquivoRPS_VVISS : GeradorDeArquivoRPS_Base
    {

        public GeradorDeArquivoRPS_VVISS(string codigoIbgeMunicipio) : base(codigoIbgeMunicipio)
        {
        }

        public override CamposExistentes CamposExistentes()
        {
            return new CamposExistentes
            {
                AliquotaISS = true,
                CodigoTributacaoMunicipio = true,
                CodigoRegimeEspecialTributacao = true,
                ItemListaServico = true,
                CertificadoDigital = true,
                Cnae = true
            };
        }

        public override CamposObrigatorios CamposObrigatorios()
        {
            return new CamposObrigatorios
            {
                AliquotaISS = true,
                CodigoTributacaoMunicipio = true,
                CodigoRegimeEspecialTributacao = false,
                ItemListaServico = true,
                Cnae = true
            };
        }

        public override bool EnviaPorWebService(PessoaJuridicaConfiguracaoNFe configuracaoNFe)
        {
            return true;
        }

        public override MemoryStream Gerar(IEnumerable<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int lote)
        {
            var configuracao = pessoaJuridica.ConfiguracaoNFe;
            if (configuracao.CodigoTributacaoMunicipio == null)
                throw new ArgumentException("Não consta tipo de tributação para o estabelecimento informado. Por favor, verifique se o estabelecimento está configurado.");

            var dados = GerarEnviarLoteRpsEnvio(pessoaJuridica, lote, configuracao, registros);
            var retorno = new MemoryStream();
            var serializer = new XmlSerializer(typeof(EnviarLoteRpsEnvio));
            serializer.Serialize(retorno, dados);
            retorno.Flush();
            retorno.Seek(0, SeekOrigin.Begin);
            return retorno;
        }

        public override void ValidarConfiguracao(PessoaJuridicaConfiguracaoNFe configuracao, TipoGerarPara tipoDeGeracao)
        {
            base.ValidarConfiguracao(configuracao, tipoDeGeracao);

            if ((configuracao.ItemListaServico.SomenteNumeros().Length != 3 && configuracao.ItemListaServico.SomenteNumeros().Length != 4)
                || !configuracao.ItemListaServico.Contains("."))
                throw new ArgumentException("O Código 'Item Lista Serviço' deve conter 3 ou 4 números e um ponto separador.");

            if (configuracao.CodigoCnae.Length != 7)
                throw new ArgumentException("O Código 'CNAE' deve conter 7 números.");

            if (configuracao.CodigoTipoRegimeEspecialTributacao.Length != 1)
                throw new ArgumentException("O tipo de regime especial de tributação deve conter somente 1 dígito.");

            if (configuracao.CodigoOptanteSimplesNacional.Length != 1)
                throw new ArgumentException("O código de optante Simples deve conter somente 1 dígito.");

            if (int.Parse(configuracao.CodigoOptanteSimplesNacional) < 1 || int.Parse(configuracao.CodigoOptanteSimplesNacional) > 2)
                throw new ArgumentException("O código de optante Simples deve ser 1 (Sim) ou 2 (Não).");
        }

        #region Geradores

        private static tcContato GerarContato(DadosParaGeracaoLoteRPSDTO r)
        {
            return new tcContato
            {
                Email = r.Email
            };
        }

        private static tcCpfCnpj GerarCpfCnpj(PessoaJuridica pessoaJuridica)
        {
            return new tcCpfCnpj
            {
                Item = pessoaJuridica.CNPJ,
                ItemElementName = ItemChoiceType.Cnpj
            };
        }

        private static tcDeclaracaoPrestacaoServico GerarDeclaracaoPrestacaoServico(PessoaJuridica pessoaJuridica, PessoaJuridicaConfiguracaoNFe configuracao, DadosParaGeracaoLoteRPSDTO r)
        {
            return new tcDeclaracaoPrestacaoServico()
            {
                InfDeclaracaoPrestacaoServico = GerarInfDeclaracaoPrestacaoServico(pessoaJuridica, configuracao, r)
            };
        }

        private static EnviarLoteRpsEnvio GerarEnviarLoteRpsEnvio(PessoaJuridica pessoaJuridica, int lote, PessoaJuridicaConfiguracaoNFe configuracao, IEnumerable<DadosParaGeracaoLoteRPSDTO> registros)
        {
            var dados = new EnviarLoteRpsEnvio
            {
                LoteRps = GerarLote(pessoaJuridica, lote, configuracao, registros)
            };
            return dados;
        }

        private static tcIdentificacaoRps GerarIdentificacaoRps(int numero)
        {
            return new tcIdentificacaoRps
            {
                Numero = numero.ToString(),
                Serie = "00000",
                Tipo = 1
            };
        }

        private static tcInfDeclaracaoPrestacaoServico GerarInfDeclaracaoPrestacaoServico(PessoaJuridica pessoaJuridica, PessoaJuridicaConfiguracaoNFe configuracao, DadosParaGeracaoLoteRPSDTO r)
        {
            return new tcInfDeclaracaoPrestacaoServico
            {
                Rps = GerarRps(r),
                Competencia = r.DataTransacao,
                OptanteSimplesNacional = Convert.ToSByte(configuracao.CodigoOptanteSimplesNacional),
                RegimeEspecialTributacao = !string.IsNullOrWhiteSpace(configuracao.CodigoTipoRegimeEspecialTributacao) ? Convert.ToSByte(configuracao.CodigoTipoRegimeEspecialTributacao) : default(sbyte),
                RegimeEspecialTributacaoSpecified = true,
                Prestador = GerarPrestador(pessoaJuridica),
                Servico = GerarServico(pessoaJuridica, configuracao, r),
                Tomador = GerarTomador(r, pessoaJuridica),
                IncentivoFiscal = 2
            };
        }

        private static tcLoteRps GerarLote(PessoaJuridica pessoaJuridica, int lote, PessoaJuridicaConfiguracaoNFe configuracao, IEnumerable<DadosParaGeracaoLoteRPSDTO> registros)
        {
            return new tcLoteRps
            {
                NumeroLote = lote.ToString(),
                InscricaoMunicipal = pessoaJuridica.InscricaoMunicipal,
                QuantidadeRps = registros.Count(),
                CpfCnpj = GerarCpfCnpj(pessoaJuridica),
                versao = "1.00",
                Id = "L1",
                ListaRps = registros.Select(r => GerarDeclaracaoPrestacaoServico(pessoaJuridica, configuracao, r)).ToArray()
            };
        }

        private static tcIdentificacaoPrestador GerarPrestador(PessoaJuridica pessoaJuridica)
        {
            return new tcIdentificacaoPrestador
            {
                CpfCnpj = GerarCpfCnpj(pessoaJuridica),
                InscricaoMunicipal = pessoaJuridica.InscricaoMunicipal
            };
        }

        private static tcInfRps GerarRps(DadosParaGeracaoLoteRPSDTO r)
        {
            var dadosRPSTransacao = r.DadosRPS;
            var emissaoVigente = dadosRPSTransacao.EmissoesRPS.LastOrDefault();

            var numero = 0;
            if (emissaoVigente != null)
            {
                numero = emissaoVigente.Numero;
            }
            var emissaoSubstituida = dadosRPSTransacao.EmissoesRPS.LastOrDefault(f => f.Numero != numero);
            var numeroRPSSubstituido = emissaoSubstituida != null ? emissaoSubstituida.Numero : 0;

            return new tcInfRps
            {
                Status = Convert.ToSByte(r.StatusRPS == StatusRpsEnum.ACancelar ? "2" : "1"),
                DataEmissao = r.DataTransacao,
                IdentificacaoRps = GerarIdentificacaoRps(numero),
                Id = "R" + numero.ToString(),
                RpsSubstituido = GerarRpsSubstituido(numeroRPSSubstituido)
            };
        }

        private static tcIdentificacaoRps GerarRpsSubstituido(int numeroRPSSubstituido)
        {
            return numeroRPSSubstituido > 0 ? new tcIdentificacaoRps
            {
                Numero = numeroRPSSubstituido.ToString(),
                Serie = "00000",
                Tipo = 1
            } : null;
        }

        private static tcDadosServico GerarServico(PessoaJuridica pessoaJuridica, PessoaJuridicaConfiguracaoNFe configuracao, DadosParaGeracaoLoteRPSDTO r)
        {
            return new tcDadosServico
            {
                Valores = GerarValores(r, configuracao),
                ItemListaServico = configuracao.ItemListaServico.Left(5),
                Discriminacao = string.Join("|", r.NomesServicos) + (r.DadosFiscaisDoServicoNaoInformado ? "" : "||" + r.TextoInformandoValorDosImpostos),
                CodigoMunicipio = Convert.ToInt32(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE),
                MunicipioIncidencia = Convert.ToInt32(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE),
                MunicipioIncidenciaSpecified = true,
                CodigoTributacaoMunicipio = configuracao.CodigoTributacaoMunicipio,
                IssRetido = 2,
                ExigibilidadeISS = 1,
                CodigoCnae = configuracao.CodigoCnae,
                CodigoCnaeSpecified = true
            };
        }

        private static tcDadosTomador GerarTomador(DadosParaGeracaoLoteRPSDTO r, PessoaJuridica pessoaJuridica)
        {
            var tcDadosTomador = new tcDadosTomador
            {
                RazaoSocial = r.NomeCliente,
                Contato = GerarContato(r),
                Endereco = GetEndereco(r, pessoaJuridica)
            };

            if (!string.IsNullOrWhiteSpace(r.Cpf))
            {
                tcDadosTomador.IdentificacaoTomador = new tcIdentificacaoTomador()
                {
                    CpfCnpj = new tcCpfCnpj()
                    {
                        ItemElementName = ItemChoiceType.Cpf,
                        Item = r.PessoaCliente.Cpf
                    }
                };
            }

            return tcDadosTomador;
        }

        private static tcValoresDeclaracaoServico GerarValores(DadosParaGeracaoLoteRPSDTO r, PessoaJuridicaConfiguracaoNFe configuracao)
        {
            var tcvalores = new tcValoresDeclaracaoServico
            {
                ValorServicos = r.ValorTotal,
                AliquotaSpecified = true,
                Aliquota = configuracao.AliquotaISS
            };

            if (r.ValorDeducoes != null)
            {
                tcvalores.ValorDeducoes = Math.Round(r.ValorDeducoes.Value, 2);
                tcvalores.ValorDeducoesSpecified = true;
            }

            return tcvalores;
        }

        private static tcEndereco GetEndereco(DadosParaGeracaoLoteRPSDTO r, PessoaJuridica pessoaJuridica)
        {

            if (r.EnderecoDoCliente != null)
            {
                var tcEndereco = new tcEndereco
                {
                    Endereco = r.EnderecoDoCliente.Logradouro,
                    Bairro = r.EnderecoDoCliente.Bairro,
                    Cep = string.IsNullOrWhiteSpace(r.EnderecoDoCliente.Cep) || r.EnderecoDoCliente.Cep.Length != 8 ? null : r.EnderecoDoCliente.Cep,
                    Numero = r.EnderecoDoCliente.Numero,
                    Uf = r.EnderecoDoCliente.UF != null ? r.EnderecoDoCliente.UF.Sigla : pessoaJuridica.EnderecoProprio.UF.Sigla
                };


                if (!string.IsNullOrEmpty(r.EnderecoDoCliente.Complemento))
                    tcEndereco.Complemento = r.EnderecoDoCliente.Complemento;

                return tcEndereco;
            }

            return null;
        }

        #endregion Geradores

        public override string OrientacaoCodigoTributacaoMunicipio()
        {
            return "Ex.: 06.02.01";
        }

        public override string OrientacaoItemListaServico()
        {
            return "Ex.: 06.02";
        }

        public override string OrientacaoCodigoRegimeEspecialTributacao()
        {
            return "1";
        }
    }
}