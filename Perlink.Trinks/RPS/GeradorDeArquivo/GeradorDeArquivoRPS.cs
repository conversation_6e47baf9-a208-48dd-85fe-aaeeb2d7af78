﻿using Perlink.Shared.IO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.RPS.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Web;
using System.Xml.Linq;

namespace Perlink.Trinks.RPS.GeradorDeArquivo
{

    public static class GeradorDeArquivoRPS
    {
        private const string NAMESPACE_GERADORES = "Perlink.Trinks.RPS.GeradorDeArquivo";

        private static Dictionary<String, String> implementacoesTrinks;

        /// <summary>
        /// Construtor estático, utilizado para carregar a lista de implementações de RPS do Trinks
        /// que não constam no XML do UniNFe.
        /// </summary>
        static GeradorDeArquivoRPS()
        {
            CarregarImplementacoesRPSTrinks();
        }

        /// <summary>
        /// Obtém uma instância do gerador de arquivo RPS para a cidade identificada pelo código informado.
        /// </summary>
        /// <param name="codigo">Código IBGE da cidade a ser pesquisada.</param>
        /// <returns>Instância de IGeradorDeArquivoRPS para a cidade informada.</returns>
        /// <exception cref="System.TypeLoadException">Ocorre quando não há uma classe geradora de arquivo para o padrão da cidade informada.</exception>
        public static GeradorDeArquivoRPS_Base ParaCidade(string codigoIBGE)
        {
            // Pesquisa o padrão de nota fiscal utilizado na cidade especificada, dando
            // preferência a implementações na lista do Trinks. Caso o Trinks não possua
            // um padrão para a cidade, é feita a pesquisa no XML do UniNFe.
            var padraoCidade = ObterPadraoCidadeDoUniNFe(codigoIBGE);

            if (padraoCidade == null)
                return null;

            // Tenta instanciar o gerador para a cidade
            var tipo = Type.GetType(String.Format("{0}.GeradorDeArquivoRPS_{1}", NAMESPACE_GERADORES, padraoCidade));

            return (GeradorDeArquivoRPS_Base)Activator.CreateInstance(tipo, args: new object[] { codigoIBGE }); ;
        }

        public static GeradorDeArquivoRPS_Base PorPessoaJuridica(PessoaJuridica pessoaJuridica)
        {
            var codIbge = pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;
            var gerador = ParaCidade(codIbge);
            return gerador;
        }

        public static bool ExisteImplementacaoParaCidade(string codigoIBGE)
        {

            var padraoCidade = ObterPadraoCidadeDoUniNFe(codigoIBGE);
            var fullName = String.Format("{0}.GeradorDeArquivoRPS_{1}", NAMESPACE_GERADORES, padraoCidade);

            return Type.GetType(fullName) != null || implementacoesTrinks.Any(x => x.Key.Equals(codigoIBGE));
        }

        /// <summary>
        /// Localiza o padrão de nota de serviço para a cidade identificada pelo código informado.
        /// </summary>
        /// <param name="codigoIBGE">Código IBGE da cidade a ser pesquisada.</param>
        /// <returns>Nome do padrão de nota de serviço utilizado na cidade informada.</returns>
        /// <exception cref="System.ArgumentException">Ocorre quando não é encontrada nenhuma cidade na base de municípios do UniNFe com o código informado.</exception>
        /// <exception cref="System.InvalidOperationException">Ocorre quando mais de uma cidade é encontrada com o código informado.</exception>
        [Obsolete]
        public static string ObterPadraoCidadeDoUniNFePorArquivo(string codigoIBGE)
        {
            if (implementacoesTrinks == null)
                CarregarImplementacoesRPSTrinks();

            if (implementacoesTrinks.Any(x => x.Key.Equals(codigoIBGE)))
                return implementacoesTrinks[codigoIBGE];

            var path = "";
            path = HttpContext.Current != null
                ? HttpContext.Current.Server.MapPath("~")
                : System.IO.Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

            var uniNfePathBaseMunicipios = ConfiguracoesTrinks.Geral.UniNFEPathBaseMunicipios;
            if (uniNfePathBaseMunicipios.StartsWith("~"))
                path = Path.UrlCombine(path, uniNfePathBaseMunicipios.Replace("~", ""));
            else
            {
                path = uniNfePathBaseMunicipios;
            }

            var doc = XDocument.Load(path);

            var municipio = (from m in doc.Descendants("Registro")
                             where m.Attribute("ID").Value.Equals(codigoIBGE)
                             select m).SingleOrDefault();

            if (municipio == null)
                return null;

            return municipio.Attribute("Padrao").Value;
        }

        /// <summary>
        /// Localiza o padrão de nota de serviço para a cidade identificada pelo código informado.
        /// </summary>
        /// <param name="codigoIBGE">Código IBGE da cidade a ser pesquisada.</param>
        /// <returns>Nome do padrão de nota de serviço utilizado na cidade informada.</returns>
        public static string ObterPadraoCidadeDoUniNFe(string codigoIBGE)
        {
            if (implementacoesTrinks == null)
                CarregarImplementacoesRPSTrinks();

            if (implementacoesTrinks.Any(x => x.Key.Equals(codigoIBGE)))
                return implementacoesTrinks[codigoIBGE];

            return Domain.RPS.MunicipioPadraoNfseRepository.BuscarPadraoPorCodIbge(codigoIBGE);
        }

        private static void CarregarImplementacoesRPSTrinks()
        {
            implementacoesTrinks = new Dictionary<string, string>();
            implementacoesTrinks.Add("3505708", "SP_Barueri");              //Barueri
            //implementacoesTrinks.Add("3549805", "SP_SaoJoseDoRioPreto");    //São José do Rio Preto
            implementacoesTrinks.Add("4113700", "PR_Londrina");             //Londrina, PR
        }

        public static GeradorDeArquivoRPS_Base ObterPadraoPorPessoaJuridica(PessoaJuridica pessoaJuridica)
        {
            var estabelecimentoConfiguradoEmissaoInvoicy = pessoaJuridica.ConfiguracaoNFe?.IntegracaoNFSe == IntegracaoNFSeEnum.Invoicy;

            // Pesquisa o padrão de nota fiscal utilizado na cidade especificada, dando
            // preferência a implementações na lista do Trinks. Caso o Trinks não possua
            // um padrão para a cidade, é feita a pesquisa no XML do UniNFe.
            var codigoIBGE = pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;
            var padraoCidade = estabelecimentoConfiguradoEmissaoInvoicy ? IntegracaoNFSeEnum.Invoicy.ToString() : ObterPadraoCidadeDoUniNFe(codigoIBGE);

            if (padraoCidade == null)
                return null;

            // Tenta instanciar o gerador para a cidade
            var tipo = Type.GetType(String.Format("{0}.GeradorDeArquivoRPS_{1}", NAMESPACE_GERADORES, padraoCidade));

            return (GeradorDeArquivoRPS_Base)Activator.CreateInstance(tipo, args: new object[] { codigoIBGE }); ;
        }
    }
}