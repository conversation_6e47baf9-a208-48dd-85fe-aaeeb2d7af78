﻿namespace Perlink.Trinks.RPS.GeradorDeArquivo.VVISS
{
    //------------------------------------------------------------------------------
    // <auto-generated>
    //     This code was generated by a tool.
    //     Runtime Version:4.0.30319.42000
    //
    //     Changes to this file may cause incorrect behavior and will be lost if
    //     the code is regenerated.
    // </auto-generated>
    //------------------------------------------------------------------------------

    // 
    // This source code was auto-generated by xsd, Version=********.
    // 


    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ListaMensagemRetornoLote
    {

        private tcMensagemRetornoLote[] mensagemRetornoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MensagemRetorno")]
        public tcMensagemRetornoLote[] MensagemRetorno
        {
            get
            {
                return this.mensagemRetornoField;
            }
            set
            {
                this.mensagemRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcMensagemRetornoLote
    {

        private tcIdentificacaoRps identificacaoRpsField;

        private string codigoField;

        private string mensagemField;

        /// <remarks/>
        public tcIdentificacaoRps IdentificacaoRps
        {
            get
            {
                return this.identificacaoRpsField;
            }
            set
            {
                this.identificacaoRpsField = value;
            }
        }

        /// <remarks/>
        public string Codigo
        {
            get
            {
                return this.codigoField;
            }
            set
            {
                this.codigoField = value;
            }
        }

        /// <remarks/>
        public string Mensagem
        {
            get
            {
                return this.mensagemField;
            }
            set
            {
                this.mensagemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoRps
    {

        private string numeroField;

        private string serieField;

        private sbyte tipoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string Numero
        {
            get
            {
                return this.numeroField;
            }
            set
            {
                this.numeroField = value;
            }
        }

        /// <remarks/>
        public string Serie
        {
            get
            {
                return this.serieField;
            }
            set
            {
                this.serieField = value;
            }
        }

        /// <remarks/>
        public sbyte Tipo
        {
            get
            {
                return this.tipoField;
            }
            set
            {
                this.tipoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoConsulente
    {

        private tcCpfCnpj cpfCnpjField;

        private string inscricaoMunicipalField;

        /// <remarks/>
        public tcCpfCnpj CpfCnpj
        {
            get
            {
                return this.cpfCnpjField;
            }
            set
            {
                this.cpfCnpjField = value;
            }
        }

        /// <remarks/>
        public string InscricaoMunicipal
        {
            get
            {
                return this.inscricaoMunicipalField;
            }
            set
            {
                this.inscricaoMunicipalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcCpfCnpj
    {

        private string itemField;

        private ItemChoiceType itemElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Cnpj", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("Cpf", typeof(string))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemElementName")]
        public string Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemChoiceType ItemElementName
        {
            get
            {
                return this.itemElementNameField;
            }
            set
            {
                this.itemElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IncludeInSchema = false)]
    public enum ItemChoiceType
    {

        /// <remarks/>
        Cnpj,

        /// <remarks/>
        Cpf,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcRetCancelamento
    {

        private tcCancelamentoNfse nfseCancelamentoField;

        /// <remarks/>
        public tcCancelamentoNfse NfseCancelamento
        {
            get
            {
                return this.nfseCancelamentoField;
            }
            set
            {
                this.nfseCancelamentoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcCancelamentoNfse
    {

        private tcConfirmacaoCancelamento confirmacaoField;

        private SignatureType signatureField;

        private string versaoField;

        /// <remarks/>
        public tcConfirmacaoCancelamento Confirmacao
        {
            get
            {
                return this.confirmacaoField;
            }
            set
            {
                this.confirmacaoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "token")]
        public string versao
        {
            get
            {
                return this.versaoField;
            }
            set
            {
                this.versaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcConfirmacaoCancelamento
    {

        private tcPedidoCancelamento pedidoField;

        private System.DateTime dataHoraField;

        private string idField;

        /// <remarks/>
        public tcPedidoCancelamento Pedido
        {
            get
            {
                return this.pedidoField;
            }
            set
            {
                this.pedidoField = value;
            }
        }

        /// <remarks/>
        public System.DateTime DataHora
        {
            get
            {
                return this.dataHoraField;
            }
            set
            {
                this.dataHoraField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcPedidoCancelamento
    {

        private tcInfPedidoCancelamento infPedidoCancelamentoField;

        private SignatureType signatureField;

        /// <remarks/>
        public tcInfPedidoCancelamento InfPedidoCancelamento
        {
            get
            {
                return this.infPedidoCancelamentoField;
            }
            set
            {
                this.infPedidoCancelamentoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcInfPedidoCancelamento
    {

        private tcIdentificacaoNfse identificacaoNfseField;

        private sbyte codigoCancelamentoField;

        private bool codigoCancelamentoFieldSpecified;

        private string idField;

        /// <remarks/>
        public tcIdentificacaoNfse IdentificacaoNfse
        {
            get
            {
                return this.identificacaoNfseField;
            }
            set
            {
                this.identificacaoNfseField = value;
            }
        }

        /// <remarks/>
        public sbyte CodigoCancelamento
        {
            get
            {
                return this.codigoCancelamentoField;
            }
            set
            {
                this.codigoCancelamentoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CodigoCancelamentoSpecified
        {
            get
            {
                return this.codigoCancelamentoFieldSpecified;
            }
            set
            {
                this.codigoCancelamentoFieldSpecified = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoNfse
    {

        private string numeroField;

        private tcCpfCnpj cpfCnpjField;

        private string inscricaoMunicipalField;

        private int codigoMunicipioField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string Numero
        {
            get
            {
                return this.numeroField;
            }
            set
            {
                this.numeroField = value;
            }
        }

        /// <remarks/>
        public tcCpfCnpj CpfCnpj
        {
            get
            {
                return this.cpfCnpjField;
            }
            set
            {
                this.cpfCnpjField = value;
            }
        }

        /// <remarks/>
        public string InscricaoMunicipal
        {
            get
            {
                return this.inscricaoMunicipalField;
            }
            set
            {
                this.inscricaoMunicipalField = value;
            }
        }

        /// <remarks/>
        public int CodigoMunicipio
        {
            get
            {
                return this.codigoMunicipioField;
            }
            set
            {
                this.codigoMunicipioField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("Signature", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SignatureType
    {

        private SignedInfoType signedInfoField;

        private SignatureValueType signatureValueField;

        private KeyInfoType keyInfoField;

        private ObjectType[] objectField;

        private string idField;

        /// <remarks/>
        public SignedInfoType SignedInfo
        {
            get
            {
                return this.signedInfoField;
            }
            set
            {
                this.signedInfoField = value;
            }
        }

        /// <remarks/>
        public SignatureValueType SignatureValue
        {
            get
            {
                return this.signatureValueField;
            }
            set
            {
                this.signatureValueField = value;
            }
        }

        /// <remarks/>
        public KeyInfoType KeyInfo
        {
            get
            {
                return this.keyInfoField;
            }
            set
            {
                this.keyInfoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Object")]
        public ObjectType[] Object
        {
            get
            {
                return this.objectField;
            }
            set
            {
                this.objectField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("SignedInfo", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SignedInfoType
    {

        private CanonicalizationMethodType canonicalizationMethodField;

        private SignatureMethodType signatureMethodField;

        private ReferenceType[] referenceField;

        private string idField;

        /// <remarks/>
        public CanonicalizationMethodType CanonicalizationMethod
        {
            get
            {
                return this.canonicalizationMethodField;
            }
            set
            {
                this.canonicalizationMethodField = value;
            }
        }

        /// <remarks/>
        public SignatureMethodType SignatureMethod
        {
            get
            {
                return this.signatureMethodField;
            }
            set
            {
                this.signatureMethodField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Reference")]
        public ReferenceType[] Reference
        {
            get
            {
                return this.referenceField;
            }
            set
            {
                this.referenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("CanonicalizationMethod", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class CanonicalizationMethodType
    {

        private System.Xml.XmlNode[] anyField;

        private string algorithmField;

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Algorithm
        {
            get
            {
                return this.algorithmField;
            }
            set
            {
                this.algorithmField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("SignatureMethod", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SignatureMethodType
    {

        private string hMACOutputLengthField;

        private System.Xml.XmlNode[] anyField;

        private string algorithmField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
        public string HMACOutputLength
        {
            get
            {
                return this.hMACOutputLengthField;
            }
            set
            {
                this.hMACOutputLengthField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Algorithm
        {
            get
            {
                return this.algorithmField;
            }
            set
            {
                this.algorithmField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("Reference", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class ReferenceType
    {

        private TransformType[] transformsField;

        private DigestMethodType digestMethodField;

        private byte[] digestValueField;

        private string idField;

        private string uRIField;

        private string typeField;

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Transform", IsNullable = false)]
        public TransformType[] Transforms
        {
            get
            {
                return this.transformsField;
            }
            set
            {
                this.transformsField = value;
            }
        }

        /// <remarks/>
        public DigestMethodType DigestMethod
        {
            get
            {
                return this.digestMethodField;
            }
            set
            {
                this.digestMethodField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] DigestValue
        {
            get
            {
                return this.digestValueField;
            }
            set
            {
                this.digestValueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string URI
        {
            get
            {
                return this.uRIField;
            }
            set
            {
                this.uRIField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("Transform", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class TransformType
    {

        private object[] itemsField;

        private string[] textField;

        private string algorithmField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("XPath", typeof(string))]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Algorithm
        {
            get
            {
                return this.algorithmField;
            }
            set
            {
                this.algorithmField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("DigestMethod", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class DigestMethodType
    {

        private System.Xml.XmlNode[] anyField;

        private string algorithmField;

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Algorithm
        {
            get
            {
                return this.algorithmField;
            }
            set
            {
                this.algorithmField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("SignatureValue", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SignatureValueType
    {

        private string idField;

        private byte[] valueField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute(DataType = "base64Binary")]
        public byte[] Value
        {
            get
            {
                return this.valueField;
            }
            set
            {
                this.valueField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("KeyInfo", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class KeyInfoType
    {

        private object[] itemsField;

        private ItemsChoiceType2[] itemsElementNameField;

        private string[] textField;

        private string idField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("KeyName", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("KeyValue", typeof(KeyValueType))]
        [System.Xml.Serialization.XmlElementAttribute("MgmtData", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("PGPData", typeof(PGPDataType))]
        [System.Xml.Serialization.XmlElementAttribute("RetrievalMethod", typeof(RetrievalMethodType))]
        [System.Xml.Serialization.XmlElementAttribute("SPKIData", typeof(SPKIDataType))]
        [System.Xml.Serialization.XmlElementAttribute("X509Data", typeof(X509DataType))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType2[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("KeyValue", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class KeyValueType
    {

        private object itemField;

        private string[] textField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("DSAKeyValue", typeof(DSAKeyValueType))]
        [System.Xml.Serialization.XmlElementAttribute("RSAKeyValue", typeof(RSAKeyValueType))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("DSAKeyValue", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class DSAKeyValueType
    {

        private byte[] pField;

        private byte[] qField;

        private byte[] gField;

        private byte[] yField;

        private byte[] jField;

        private byte[] seedField;

        private byte[] pgenCounterField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] P
        {
            get
            {
                return this.pField;
            }
            set
            {
                this.pField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] Q
        {
            get
            {
                return this.qField;
            }
            set
            {
                this.qField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] G
        {
            get
            {
                return this.gField;
            }
            set
            {
                this.gField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] Y
        {
            get
            {
                return this.yField;
            }
            set
            {
                this.yField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] J
        {
            get
            {
                return this.jField;
            }
            set
            {
                this.jField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] Seed
        {
            get
            {
                return this.seedField;
            }
            set
            {
                this.seedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] PgenCounter
        {
            get
            {
                return this.pgenCounterField;
            }
            set
            {
                this.pgenCounterField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("RSAKeyValue", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class RSAKeyValueType
    {

        private byte[] modulusField;

        private byte[] exponentField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] Modulus
        {
            get
            {
                return this.modulusField;
            }
            set
            {
                this.modulusField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "base64Binary")]
        public byte[] Exponent
        {
            get
            {
                return this.exponentField;
            }
            set
            {
                this.exponentField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("PGPData", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class PGPDataType
    {

        private object[] itemsField;

        private ItemsChoiceType1[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("PGPKeyID", typeof(byte[]), DataType = "base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("PGPKeyPacket", typeof(byte[]), DataType = "base64Binary")]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType1[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#", IncludeInSchema = false)]
    public enum ItemsChoiceType1
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("##any:")]
        Item,

        /// <remarks/>
        PGPKeyID,

        /// <remarks/>
        PGPKeyPacket,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("RetrievalMethod", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class RetrievalMethodType
    {

        private TransformType[] transformsField;

        private string uRIField;

        private string typeField;

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Transform", IsNullable = false)]
        public TransformType[] Transforms
        {
            get
            {
                return this.transformsField;
            }
            set
            {
                this.transformsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string URI
        {
            get
            {
                return this.uRIField;
            }
            set
            {
                this.uRIField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Type
        {
            get
            {
                return this.typeField;
            }
            set
            {
                this.typeField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("SPKIData", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SPKIDataType
    {

        private byte[][] sPKISexpField;

        private System.Xml.XmlElement anyField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SPKISexp", DataType = "base64Binary")]
        public byte[][] SPKISexp
        {
            get
            {
                return this.sPKISexpField;
            }
            set
            {
                this.sPKISexpField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlElement Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("X509Data", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class X509DataType
    {

        private object[] itemsField;

        private ItemsChoiceType[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("X509CRL", typeof(byte[]), DataType = "base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("X509Certificate", typeof(byte[]), DataType = "base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("X509IssuerSerial", typeof(X509IssuerSerialType))]
        [System.Xml.Serialization.XmlElementAttribute("X509SKI", typeof(byte[]), DataType = "base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("X509SubjectName", typeof(string))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    public partial class X509IssuerSerialType
    {

        private string x509IssuerNameField;

        private string x509SerialNumberField;

        /// <remarks/>
        public string X509IssuerName
        {
            get
            {
                return this.x509IssuerNameField;
            }
            set
            {
                this.x509IssuerNameField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
        public string X509SerialNumber
        {
            get
            {
                return this.x509SerialNumberField;
            }
            set
            {
                this.x509SerialNumberField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#", IncludeInSchema = false)]
    public enum ItemsChoiceType
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("##any:")]
        Item,

        /// <remarks/>
        X509CRL,

        /// <remarks/>
        X509Certificate,

        /// <remarks/>
        X509IssuerSerial,

        /// <remarks/>
        X509SKI,

        /// <remarks/>
        X509SubjectName,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#", IncludeInSchema = false)]
    public enum ItemsChoiceType2
    {

        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("##any:")]
        Item,

        /// <remarks/>
        KeyName,

        /// <remarks/>
        KeyValue,

        /// <remarks/>
        MgmtData,

        /// <remarks/>
        PGPData,

        /// <remarks/>
        RetrievalMethod,

        /// <remarks/>
        SPKIData,

        /// <remarks/>
        X509Data,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("Object", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class ObjectType
    {

        private System.Xml.XmlNode[] anyField;

        private string idField;

        private string mimeTypeField;

        private string encodingField;

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string MimeType
        {
            get
            {
                return this.mimeTypeField;
            }
            set
            {
                this.mimeTypeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Encoding
        {
            get
            {
                return this.encodingField;
            }
            set
            {
                this.encodingField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcLoteRps
    {

        private string numeroLoteField;

        private tcCpfCnpj cpfCnpjField;

        private string inscricaoMunicipalField;

        private int quantidadeRpsField;

        private tcDeclaracaoPrestacaoServico[] listaRpsField;

        private string idField;

        private string versaoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NumeroLote
        {
            get
            {
                return this.numeroLoteField;
            }
            set
            {
                this.numeroLoteField = value;
            }
        }

        /// <remarks/>
        public tcCpfCnpj CpfCnpj
        {
            get
            {
                return this.cpfCnpjField;
            }
            set
            {
                this.cpfCnpjField = value;
            }
        }

        /// <remarks/>
        public string InscricaoMunicipal
        {
            get
            {
                return this.inscricaoMunicipalField;
            }
            set
            {
                this.inscricaoMunicipalField = value;
            }
        }

        /// <remarks/>
        public int QuantidadeRps
        {
            get
            {
                return this.quantidadeRpsField;
            }
            set
            {
                this.quantidadeRpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Rps", IsNullable = false)]
        public tcDeclaracaoPrestacaoServico[] ListaRps
        {
            get
            {
                return this.listaRpsField;
            }
            set
            {
                this.listaRpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "token")]
        public string versao
        {
            get
            {
                return this.versaoField;
            }
            set
            {
                this.versaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcDeclaracaoPrestacaoServico
    {

        private tcInfDeclaracaoPrestacaoServico infDeclaracaoPrestacaoServicoField;

        private SignatureType signatureField;

        /// <remarks/>
        public tcInfDeclaracaoPrestacaoServico InfDeclaracaoPrestacaoServico
        {
            get
            {
                return this.infDeclaracaoPrestacaoServicoField;
            }
            set
            {
                this.infDeclaracaoPrestacaoServicoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcInfDeclaracaoPrestacaoServico
    {

        private tcInfRps rpsField;

        private System.DateTime competenciaField;

        private tcDadosServico servicoField;

        private tcIdentificacaoPrestador prestadorField;

        private tcDadosTomador tomadorField;

        private tcDadosIntermediario intermediarioField;

        private tcDadosConstrucaoCivil construcaoCivilField;

        private sbyte regimeEspecialTributacaoField;

        private bool regimeEspecialTributacaoFieldSpecified;

        private sbyte optanteSimplesNacionalField;

        private sbyte incentivoFiscalField;

        private string idField;

        /// <remarks/>
        public tcInfRps Rps
        {
            get
            {
                return this.rpsField;
            }
            set
            {
                this.rpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime Competencia
        {
            get
            {
                return this.competenciaField;
            }
            set
            {
                this.competenciaField = value;
            }
        }

        /// <remarks/>
        public tcDadosServico Servico
        {
            get
            {
                return this.servicoField;
            }
            set
            {
                this.servicoField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoPrestador Prestador
        {
            get
            {
                return this.prestadorField;
            }
            set
            {
                this.prestadorField = value;
            }
        }

        /// <remarks/>
        public tcDadosTomador Tomador
        {
            get
            {
                return this.tomadorField;
            }
            set
            {
                this.tomadorField = value;
            }
        }

        /// <remarks/>
        public tcDadosIntermediario Intermediario
        {
            get
            {
                return this.intermediarioField;
            }
            set
            {
                this.intermediarioField = value;
            }
        }

        /// <remarks/>
        public tcDadosConstrucaoCivil ConstrucaoCivil
        {
            get
            {
                return this.construcaoCivilField;
            }
            set
            {
                this.construcaoCivilField = value;
            }
        }

        /// <remarks/>
        public sbyte RegimeEspecialTributacao
        {
            get
            {
                return this.regimeEspecialTributacaoField;
            }
            set
            {
                this.regimeEspecialTributacaoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool RegimeEspecialTributacaoSpecified
        {
            get
            {
                return this.regimeEspecialTributacaoFieldSpecified;
            }
            set
            {
                this.regimeEspecialTributacaoFieldSpecified = value;
            }
        }

        /// <remarks/>
        public sbyte OptanteSimplesNacional
        {
            get
            {
                return this.optanteSimplesNacionalField;
            }
            set
            {
                this.optanteSimplesNacionalField = value;
            }
        }

        /// <remarks/>
        public sbyte IncentivoFiscal
        {
            get
            {
                return this.incentivoFiscalField;
            }
            set
            {
                this.incentivoFiscalField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcInfRps
    {

        private tcIdentificacaoRps identificacaoRpsField;

        private System.DateTime dataEmissaoField;

        private sbyte statusField;

        private tcIdentificacaoRps rpsSubstituidoField;

        private string idField;

        /// <remarks/>
        public tcIdentificacaoRps IdentificacaoRps
        {
            get
            {
                return this.identificacaoRpsField;
            }
            set
            {
                this.identificacaoRpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataEmissao
        {
            get
            {
                return this.dataEmissaoField;
            }
            set
            {
                this.dataEmissaoField = value;
            }
        }

        /// <remarks/>
        public sbyte Status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoRps RpsSubstituido
        {
            get
            {
                return this.rpsSubstituidoField;
            }
            set
            {
                this.rpsSubstituidoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcDadosServico
    {

        private tcValoresDeclaracaoServico valoresField;

        private sbyte issRetidoField;

        private sbyte responsavelRetencaoField;

        private bool responsavelRetencaoFieldSpecified;

        private string itemListaServicoField;

        private string codigoCnaeField;

        private bool codigoCnaeFieldSpecified;

        private string codigoTributacaoMunicipioField;

        private string discriminacaoField;

        private int codigoMunicipioField;

        private string codigoPaisField;

        private sbyte exigibilidadeISSField;

        private int municipioIncidenciaField;

        private bool municipioIncidenciaFieldSpecified;

        private string numeroProcessoField;

        /// <remarks/>
        public tcValoresDeclaracaoServico Valores
        {
            get
            {
                return this.valoresField;
            }
            set
            {
                this.valoresField = value;
            }
        }

        /// <remarks/>
        public sbyte IssRetido
        {
            get
            {
                return this.issRetidoField;
            }
            set
            {
                this.issRetidoField = value;
            }
        }

        /// <remarks/>
        public sbyte ResponsavelRetencao
        {
            get
            {
                return this.responsavelRetencaoField;
            }
            set
            {
                this.responsavelRetencaoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ResponsavelRetencaoSpecified
        {
            get
            {
                return this.responsavelRetencaoFieldSpecified;
            }
            set
            {
                this.responsavelRetencaoFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string ItemListaServico
        {
            get
            {
                return this.itemListaServicoField;
            }
            set
            {
                this.itemListaServicoField = value;
            }
        }

        /// <remarks/>
        public string CodigoCnae
        {
            get
            {
                return this.codigoCnaeField;
            }
            set
            {
                this.codigoCnaeField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CodigoCnaeSpecified
        {
            get
            {
                return this.codigoCnaeFieldSpecified;
            }
            set
            {
                this.codigoCnaeFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string CodigoTributacaoMunicipio
        {
            get
            {
                return this.codigoTributacaoMunicipioField;
            }
            set
            {
                this.codigoTributacaoMunicipioField = value;
            }
        }

        /// <remarks/>
        public string Discriminacao
        {
            get
            {
                return this.discriminacaoField;
            }
            set
            {
                this.discriminacaoField = value;
            }
        }

        /// <remarks/>
        public int CodigoMunicipio
        {
            get
            {
                return this.codigoMunicipioField;
            }
            set
            {
                this.codigoMunicipioField = value;
            }
        }

        /// <remarks/>
        public string CodigoPais
        {
            get
            {
                return this.codigoPaisField;
            }
            set
            {
                this.codigoPaisField = value;
            }
        }

        /// <remarks/>
        public sbyte ExigibilidadeISS
        {
            get
            {
                return this.exigibilidadeISSField;
            }
            set
            {
                this.exigibilidadeISSField = value;
            }
        }

        /// <remarks/>
        public int MunicipioIncidencia
        {
            get
            {
                return this.municipioIncidenciaField;
            }
            set
            {
                this.municipioIncidenciaField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool MunicipioIncidenciaSpecified
        {
            get
            {
                return this.municipioIncidenciaFieldSpecified;
            }
            set
            {
                this.municipioIncidenciaFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string NumeroProcesso
        {
            get
            {
                return this.numeroProcessoField;
            }
            set
            {
                this.numeroProcessoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcValoresDeclaracaoServico
    {

        private decimal valorServicosField;

        private decimal valorDeducoesField;

        private bool valorDeducoesFieldSpecified;

        private decimal valorPisField;

        private bool valorPisFieldSpecified;

        private decimal valorCofinsField;

        private bool valorCofinsFieldSpecified;

        private decimal valorInssField;

        private bool valorInssFieldSpecified;

        private decimal valorIrField;

        private bool valorIrFieldSpecified;

        private decimal valorCsllField;

        private bool valorCsllFieldSpecified;

        private decimal outrasRetencoesField;

        private bool outrasRetencoesFieldSpecified;

        private decimal valorIssField;

        private bool valorIssFieldSpecified;

        private decimal aliquotaField;

        private bool aliquotaFieldSpecified;

        private decimal descontoIncondicionadoField;

        private bool descontoIncondicionadoFieldSpecified;

        private decimal descontoCondicionadoField;

        private bool descontoCondicionadoFieldSpecified;

        /// <remarks/>
        public decimal ValorServicos
        {
            get
            {
                return this.valorServicosField;
            }
            set
            {
                this.valorServicosField = value;
            }
        }

        /// <remarks/>
        public decimal ValorDeducoes
        {
            get
            {
                return this.valorDeducoesField;
            }
            set
            {
                this.valorDeducoesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorDeducoesSpecified
        {
            get
            {
                return this.valorDeducoesFieldSpecified;
            }
            set
            {
                this.valorDeducoesFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorPis
        {
            get
            {
                return this.valorPisField;
            }
            set
            {
                this.valorPisField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorPisSpecified
        {
            get
            {
                return this.valorPisFieldSpecified;
            }
            set
            {
                this.valorPisFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorCofins
        {
            get
            {
                return this.valorCofinsField;
            }
            set
            {
                this.valorCofinsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorCofinsSpecified
        {
            get
            {
                return this.valorCofinsFieldSpecified;
            }
            set
            {
                this.valorCofinsFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorInss
        {
            get
            {
                return this.valorInssField;
            }
            set
            {
                this.valorInssField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorInssSpecified
        {
            get
            {
                return this.valorInssFieldSpecified;
            }
            set
            {
                this.valorInssFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorIr
        {
            get
            {
                return this.valorIrField;
            }
            set
            {
                this.valorIrField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorIrSpecified
        {
            get
            {
                return this.valorIrFieldSpecified;
            }
            set
            {
                this.valorIrFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorCsll
        {
            get
            {
                return this.valorCsllField;
            }
            set
            {
                this.valorCsllField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorCsllSpecified
        {
            get
            {
                return this.valorCsllFieldSpecified;
            }
            set
            {
                this.valorCsllFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal OutrasRetencoes
        {
            get
            {
                return this.outrasRetencoesField;
            }
            set
            {
                this.outrasRetencoesField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool OutrasRetencoesSpecified
        {
            get
            {
                return this.outrasRetencoesFieldSpecified;
            }
            set
            {
                this.outrasRetencoesFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorIss
        {
            get
            {
                return this.valorIssField;
            }
            set
            {
                this.valorIssField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorIssSpecified
        {
            get
            {
                return this.valorIssFieldSpecified;
            }
            set
            {
                this.valorIssFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal Aliquota
        {
            get
            {
                return this.aliquotaField;
            }
            set
            {
                this.aliquotaField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AliquotaSpecified
        {
            get
            {
                return this.aliquotaFieldSpecified;
            }
            set
            {
                this.aliquotaFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal DescontoIncondicionado
        {
            get
            {
                return this.descontoIncondicionadoField;
            }
            set
            {
                this.descontoIncondicionadoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DescontoIncondicionadoSpecified
        {
            get
            {
                return this.descontoIncondicionadoFieldSpecified;
            }
            set
            {
                this.descontoIncondicionadoFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal DescontoCondicionado
        {
            get
            {
                return this.descontoCondicionadoField;
            }
            set
            {
                this.descontoCondicionadoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DescontoCondicionadoSpecified
        {
            get
            {
                return this.descontoCondicionadoFieldSpecified;
            }
            set
            {
                this.descontoCondicionadoFieldSpecified = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoPrestador
    {

        private tcCpfCnpj cpfCnpjField;

        private string inscricaoMunicipalField;

        /// <remarks/>
        public tcCpfCnpj CpfCnpj
        {
            get
            {
                return this.cpfCnpjField;
            }
            set
            {
                this.cpfCnpjField = value;
            }
        }

        /// <remarks/>
        public string InscricaoMunicipal
        {
            get
            {
                return this.inscricaoMunicipalField;
            }
            set
            {
                this.inscricaoMunicipalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcDadosTomador
    {

        private tcIdentificacaoTomador identificacaoTomadorField;

        private string razaoSocialField;

        private tcEndereco enderecoField;

        private tcContato contatoField;

        /// <remarks/>
        public tcIdentificacaoTomador IdentificacaoTomador
        {
            get
            {
                return this.identificacaoTomadorField;
            }
            set
            {
                this.identificacaoTomadorField = value;
            }
        }

        /// <remarks/>
        public string RazaoSocial
        {
            get
            {
                return this.razaoSocialField;
            }
            set
            {
                this.razaoSocialField = value;
            }
        }

        /// <remarks/>
        public tcEndereco Endereco
        {
            get
            {
                return this.enderecoField;
            }
            set
            {
                this.enderecoField = value;
            }
        }

        /// <remarks/>
        public tcContato Contato
        {
            get
            {
                return this.contatoField;
            }
            set
            {
                this.contatoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoTomador
    {

        private tcCpfCnpj cpfCnpjField;

        private string inscricaoMunicipalField;

        /// <remarks/>
        public tcCpfCnpj CpfCnpj
        {
            get
            {
                return this.cpfCnpjField;
            }
            set
            {
                this.cpfCnpjField = value;
            }
        }

        /// <remarks/>
        public string InscricaoMunicipal
        {
            get
            {
                return this.inscricaoMunicipalField;
            }
            set
            {
                this.inscricaoMunicipalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcEndereco
    {

        private string enderecoField;

        private string numeroField;

        private string complementoField;

        private string bairroField;

        private int codigoMunicipioField;

        private bool codigoMunicipioFieldSpecified;

        private string ufField;

        private string codigoPaisField;

        private string cepField;

        /// <remarks/>
        public string Endereco
        {
            get
            {
                return this.enderecoField;
            }
            set
            {
                this.enderecoField = value;
            }
        }

        /// <remarks/>
        public string Numero
        {
            get
            {
                return this.numeroField;
            }
            set
            {
                this.numeroField = value;
            }
        }

        /// <remarks/>
        public string Complemento
        {
            get
            {
                return this.complementoField;
            }
            set
            {
                this.complementoField = value;
            }
        }

        /// <remarks/>
        public string Bairro
        {
            get
            {
                return this.bairroField;
            }
            set
            {
                this.bairroField = value;
            }
        }

        /// <remarks/>
        public int CodigoMunicipio
        {
            get
            {
                return this.codigoMunicipioField;
            }
            set
            {
                this.codigoMunicipioField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool CodigoMunicipioSpecified
        {
            get
            {
                return this.codigoMunicipioFieldSpecified;
            }
            set
            {
                this.codigoMunicipioFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string Uf
        {
            get
            {
                return this.ufField;
            }
            set
            {
                this.ufField = value;
            }
        }

        /// <remarks/>
        public string CodigoPais
        {
            get
            {
                return this.codigoPaisField;
            }
            set
            {
                this.codigoPaisField = value;
            }
        }

        /// <remarks/>
        public string Cep
        {
            get
            {
                return this.cepField;
            }
            set
            {
                this.cepField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcContato
    {

        private string telefoneField;

        private string emailField;

        /// <remarks/>
        public string Telefone
        {
            get
            {
                return this.telefoneField;
            }
            set
            {
                this.telefoneField = value;
            }
        }

        /// <remarks/>
        public string Email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcDadosIntermediario
    {

        private tcIdentificacaoIntermediario identificacaoIntermediarioField;

        private string razaoSocialField;

        /// <remarks/>
        public tcIdentificacaoIntermediario IdentificacaoIntermediario
        {
            get
            {
                return this.identificacaoIntermediarioField;
            }
            set
            {
                this.identificacaoIntermediarioField = value;
            }
        }

        /// <remarks/>
        public string RazaoSocial
        {
            get
            {
                return this.razaoSocialField;
            }
            set
            {
                this.razaoSocialField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoIntermediario
    {

        private tcCpfCnpj cpfCnpjField;

        private string inscricaoMunicipalField;

        /// <remarks/>
        public tcCpfCnpj CpfCnpj
        {
            get
            {
                return this.cpfCnpjField;
            }
            set
            {
                this.cpfCnpjField = value;
            }
        }

        /// <remarks/>
        public string InscricaoMunicipal
        {
            get
            {
                return this.inscricaoMunicipalField;
            }
            set
            {
                this.inscricaoMunicipalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcDadosConstrucaoCivil
    {

        private string codigoObraField;

        private string artField;

        /// <remarks/>
        public string CodigoObra
        {
            get
            {
                return this.codigoObraField;
            }
            set
            {
                this.codigoObraField = value;
            }
        }

        /// <remarks/>
        public string Art
        {
            get
            {
                return this.artField;
            }
            set
            {
                this.artField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcInfSubstituicaoNfse
    {

        private string nfseSubstituidoraField;

        private string idField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NfseSubstituidora
        {
            get
            {
                return this.nfseSubstituidoraField;
            }
            set
            {
                this.nfseSubstituidoraField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcSubstituicaoNfse
    {

        private tcInfSubstituicaoNfse substituicaoNfseField;

        private SignatureType[] signatureField;

        private string versaoField;

        /// <remarks/>
        public tcInfSubstituicaoNfse SubstituicaoNfse
        {
            get
            {
                return this.substituicaoNfseField;
            }
            set
            {
                this.substituicaoNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Signature", Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType[] Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "token")]
        public string versao
        {
            get
            {
                return this.versaoField;
            }
            set
            {
                this.versaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcIdentificacaoOrgaoGerador
    {

        private int codigoMunicipioField;

        private string ufField;

        /// <remarks/>
        public int CodigoMunicipio
        {
            get
            {
                return this.codigoMunicipioField;
            }
            set
            {
                this.codigoMunicipioField = value;
            }
        }

        /// <remarks/>
        public string Uf
        {
            get
            {
                return this.ufField;
            }
            set
            {
                this.ufField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcDadosPrestador
    {

        private tcIdentificacaoPrestador identificacaoPrestadorField;

        private string razaoSocialField;

        private string nomeFantasiaField;

        private tcEndereco enderecoField;

        private tcContato contatoField;

        /// <remarks/>
        public tcIdentificacaoPrestador IdentificacaoPrestador
        {
            get
            {
                return this.identificacaoPrestadorField;
            }
            set
            {
                this.identificacaoPrestadorField = value;
            }
        }

        /// <remarks/>
        public string RazaoSocial
        {
            get
            {
                return this.razaoSocialField;
            }
            set
            {
                this.razaoSocialField = value;
            }
        }

        /// <remarks/>
        public string NomeFantasia
        {
            get
            {
                return this.nomeFantasiaField;
            }
            set
            {
                this.nomeFantasiaField = value;
            }
        }

        /// <remarks/>
        public tcEndereco Endereco
        {
            get
            {
                return this.enderecoField;
            }
            set
            {
                this.enderecoField = value;
            }
        }

        /// <remarks/>
        public tcContato Contato
        {
            get
            {
                return this.contatoField;
            }
            set
            {
                this.contatoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcValoresNfse
    {

        private decimal baseCalculoField;

        private bool baseCalculoFieldSpecified;

        private decimal aliquotaField;

        private bool aliquotaFieldSpecified;

        private decimal valorIssField;

        private bool valorIssFieldSpecified;

        private decimal valorLiquidoNfseField;

        /// <remarks/>
        public decimal BaseCalculo
        {
            get
            {
                return this.baseCalculoField;
            }
            set
            {
                this.baseCalculoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool BaseCalculoSpecified
        {
            get
            {
                return this.baseCalculoFieldSpecified;
            }
            set
            {
                this.baseCalculoFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal Aliquota
        {
            get
            {
                return this.aliquotaField;
            }
            set
            {
                this.aliquotaField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AliquotaSpecified
        {
            get
            {
                return this.aliquotaFieldSpecified;
            }
            set
            {
                this.aliquotaFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorIss
        {
            get
            {
                return this.valorIssField;
            }
            set
            {
                this.valorIssField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorIssSpecified
        {
            get
            {
                return this.valorIssFieldSpecified;
            }
            set
            {
                this.valorIssFieldSpecified = value;
            }
        }

        /// <remarks/>
        public decimal ValorLiquidoNfse
        {
            get
            {
                return this.valorLiquidoNfseField;
            }
            set
            {
                this.valorLiquidoNfseField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcInfNfse
    {

        private string numeroField;

        private string codigoVerificacaoField;

        private System.DateTime dataEmissaoField;

        private string nfseSubstituidaField;

        private string outrasInformacoesField;

        private tcValoresNfse valoresNfseField;

        private decimal valorCreditoField;

        private bool valorCreditoFieldSpecified;

        private tcDadosPrestador prestadorServicoField;

        private tcIdentificacaoOrgaoGerador orgaoGeradorField;

        private tcDeclaracaoPrestacaoServico declaracaoPrestacaoServicoField;

        private string idField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string Numero
        {
            get
            {
                return this.numeroField;
            }
            set
            {
                this.numeroField = value;
            }
        }

        /// <remarks/>
        public string CodigoVerificacao
        {
            get
            {
                return this.codigoVerificacaoField;
            }
            set
            {
                this.codigoVerificacaoField = value;
            }
        }

        /// <remarks/>
        public System.DateTime DataEmissao
        {
            get
            {
                return this.dataEmissaoField;
            }
            set
            {
                this.dataEmissaoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NfseSubstituida
        {
            get
            {
                return this.nfseSubstituidaField;
            }
            set
            {
                this.nfseSubstituidaField = value;
            }
        }

        /// <remarks/>
        public string OutrasInformacoes
        {
            get
            {
                return this.outrasInformacoesField;
            }
            set
            {
                this.outrasInformacoesField = value;
            }
        }

        /// <remarks/>
        public tcValoresNfse ValoresNfse
        {
            get
            {
                return this.valoresNfseField;
            }
            set
            {
                this.valoresNfseField = value;
            }
        }

        /// <remarks/>
        public decimal ValorCredito
        {
            get
            {
                return this.valorCreditoField;
            }
            set
            {
                this.valorCreditoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValorCreditoSpecified
        {
            get
            {
                return this.valorCreditoFieldSpecified;
            }
            set
            {
                this.valorCreditoFieldSpecified = value;
            }
        }

        /// <remarks/>
        public tcDadosPrestador PrestadorServico
        {
            get
            {
                return this.prestadorServicoField;
            }
            set
            {
                this.prestadorServicoField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoOrgaoGerador OrgaoGerador
        {
            get
            {
                return this.orgaoGeradorField;
            }
            set
            {
                this.orgaoGeradorField = value;
            }
        }

        /// <remarks/>
        public tcDeclaracaoPrestacaoServico DeclaracaoPrestacaoServico
        {
            get
            {
                return this.declaracaoPrestacaoServicoField;
            }
            set
            {
                this.declaracaoPrestacaoServicoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcNfse
    {

        private tcInfNfse infNfseField;

        private SignatureType signatureField;

        private string versaoField;

        /// <remarks/>
        public tcInfNfse InfNfse
        {
            get
            {
                return this.infNfseField;
            }
            set
            {
                this.infNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "token")]
        public string versao
        {
            get
            {
                return this.versaoField;
            }
            set
            {
                this.versaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class tcMensagemRetorno
    {

        private string codigoField;

        private string mensagemField;

        private string correcaoField;

        /// <remarks/>
        public string Codigo
        {
            get
            {
                return this.codigoField;
            }
            set
            {
                this.codigoField = value;
            }
        }

        /// <remarks/>
        public string Mensagem
        {
            get
            {
                return this.mensagemField;
            }
            set
            {
                this.mensagemField = value;
            }
        }

        /// <remarks/>
        public string Correcao
        {
            get
            {
                return this.correcaoField;
            }
            set
            {
                this.correcaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ListaMensagemRetorno
    {

        private tcMensagemRetorno[] mensagemRetornoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MensagemRetorno")]
        public tcMensagemRetorno[] MensagemRetorno
        {
            get
            {
                return this.mensagemRetornoField;
            }
            set
            {
                this.mensagemRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ListaMensagemAlertaRetorno
    {

        private tcMensagemRetorno[] mensagemRetornoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("MensagemRetorno")]
        public tcMensagemRetorno[] MensagemRetorno
        {
            get
            {
                return this.mensagemRetornoField;
            }
            set
            {
                this.mensagemRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class cabecalho
    {

        private string versaoDadosField;

        private string versaoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "token")]
        public string versaoDados
        {
            get
            {
                return this.versaoDadosField;
            }
            set
            {
                this.versaoDadosField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "token")]
        public string versao
        {
            get
            {
                return this.versaoField;
            }
            set
            {
                this.versaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute("CompNfse", Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class tcCompNfse
    {

        private tcNfse nfseField;

        private tcCancelamentoNfse nfseCancelamentoField;

        private tcSubstituicaoNfse nfseSubstituicaoField;

        /// <remarks/>
        public tcNfse Nfse
        {
            get
            {
                return this.nfseField;
            }
            set
            {
                this.nfseField = value;
            }
        }

        /// <remarks/>
        public tcCancelamentoNfse NfseCancelamento
        {
            get
            {
                return this.nfseCancelamentoField;
            }
            set
            {
                this.nfseCancelamentoField = value;
            }
        }

        /// <remarks/>
        public tcSubstituicaoNfse NfseSubstituicao
        {
            get
            {
                return this.nfseSubstituicaoField;
            }
            set
            {
                this.nfseSubstituicaoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class EnviarLoteRpsEnvio
    {

        private tcLoteRps loteRpsField;

        private SignatureType signatureField;

        /// <remarks/>
        public tcLoteRps LoteRps
        {
            get
            {
                return this.loteRpsField;
            }
            set
            {
                this.loteRpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class EnviarLoteRpsResposta
    {

        private object[] itemsField;

        private ItemsChoiceType3[] itemsElementNameField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("DataRecebimento", typeof(System.DateTime))]
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("NumeroLote", typeof(string), DataType = "nonNegativeInteger")]
        [System.Xml.Serialization.XmlElementAttribute("Protocolo", typeof(string))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType3[] ItemsElementName
        {
            get
            {
                return this.itemsElementNameField;
            }
            set
            {
                this.itemsElementNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IncludeInSchema = false)]
    public enum ItemsChoiceType3
    {

        /// <remarks/>
        DataRecebimento,

        /// <remarks/>
        ListaMensagemRetorno,

        /// <remarks/>
        NumeroLote,

        /// <remarks/>
        Protocolo,
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class EnviarLoteRpsSincronoEnvio
    {

        private tcLoteRps loteRpsField;

        private SignatureType signatureField;

        /// <remarks/>
        public tcLoteRps LoteRps
        {
            get
            {
                return this.loteRpsField;
            }
            set
            {
                this.loteRpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class EnviarLoteRpsSincronoResposta
    {

        private string numeroLoteField;

        private System.DateTime dataRecebimentoField;

        private bool dataRecebimentoFieldSpecified;

        private string protocoloField;

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NumeroLote
        {
            get
            {
                return this.numeroLoteField;
            }
            set
            {
                this.numeroLoteField = value;
            }
        }

        /// <remarks/>
        public System.DateTime DataRecebimento
        {
            get
            {
                return this.dataRecebimentoField;
            }
            set
            {
                this.dataRecebimentoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DataRecebimentoSpecified
        {
            get
            {
                return this.dataRecebimentoFieldSpecified;
            }
            set
            {
                this.dataRecebimentoFieldSpecified = value;
            }
        }

        /// <remarks/>
        public string Protocolo
        {
            get
            {
                return this.protocoloField;
            }
            set
            {
                this.protocoloField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetornoLote", typeof(ListaMensagemRetornoLote))]
        [System.Xml.Serialization.XmlElementAttribute("ListaNfse", typeof(EnviarLoteRpsSincronoRespostaListaNfse))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class EnviarLoteRpsSincronoRespostaListaNfse
    {

        private tcCompNfse compNfseField;

        private tcMensagemRetorno[] listaMensagemAlertaRetornoField;

        /// <remarks/>
        public tcCompNfse CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("MensagemRetorno", IsNullable = false)]
        public tcMensagemRetorno[] ListaMensagemAlertaRetorno
        {
            get
            {
                return this.listaMensagemAlertaRetornoField;
            }
            set
            {
                this.listaMensagemAlertaRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class GerarNfseEnvio
    {

        private tcDeclaracaoPrestacaoServico rpsField;

        /// <remarks/>
        public tcDeclaracaoPrestacaoServico Rps
        {
            get
            {
                return this.rpsField;
            }
            set
            {
                this.rpsField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class GerarNfseResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("ListaNfse", typeof(GerarNfseRespostaListaNfse))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class GerarNfseRespostaListaNfse
    {

        private tcCompNfse compNfseField;

        private tcMensagemRetorno[] listaMensagemAlertaRetornoField;

        /// <remarks/>
        public tcCompNfse CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("MensagemRetorno", IsNullable = false)]
        public tcMensagemRetorno[] ListaMensagemAlertaRetorno
        {
            get
            {
                return this.listaMensagemAlertaRetornoField;
            }
            set
            {
                this.listaMensagemAlertaRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class CancelarNfseEnvio
    {

        private tcPedidoCancelamento pedidoField;

        /// <remarks/>
        public tcPedidoCancelamento Pedido
        {
            get
            {
                return this.pedidoField;
            }
            set
            {
                this.pedidoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class CancelarNfseResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("RetCancelamento", typeof(tcRetCancelamento))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class SubstituirNfseEnvio
    {

        private SubstituirNfseEnvioSubstituicaoNfse substituicaoNfseField;

        private SignatureType signatureField;

        /// <remarks/>
        public SubstituirNfseEnvioSubstituicaoNfse SubstituicaoNfse
        {
            get
            {
                return this.substituicaoNfseField;
            }
            set
            {
                this.substituicaoNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature
        {
            get
            {
                return this.signatureField;
            }
            set
            {
                this.signatureField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class SubstituirNfseEnvioSubstituicaoNfse
    {

        private tcPedidoCancelamento pedidoField;

        private tcDeclaracaoPrestacaoServico rpsField;

        private string idField;

        /// <remarks/>
        public tcPedidoCancelamento Pedido
        {
            get
            {
                return this.pedidoField;
            }
            set
            {
                this.pedidoField = value;
            }
        }

        /// <remarks/>
        public tcDeclaracaoPrestacaoServico Rps
        {
            get
            {
                return this.rpsField;
            }
            set
            {
                this.rpsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class SubstituirNfseResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("RetSubstituicao", typeof(SubstituirNfseRespostaRetSubstituicao))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class SubstituirNfseRespostaRetSubstituicao
    {

        private SubstituirNfseRespostaRetSubstituicaoNfseSubstituida nfseSubstituidaField;

        private SubstituirNfseRespostaRetSubstituicaoNfseSubstituidora nfseSubstituidoraField;

        /// <remarks/>
        public SubstituirNfseRespostaRetSubstituicaoNfseSubstituida NfseSubstituida
        {
            get
            {
                return this.nfseSubstituidaField;
            }
            set
            {
                this.nfseSubstituidaField = value;
            }
        }

        /// <remarks/>
        public SubstituirNfseRespostaRetSubstituicaoNfseSubstituidora NfseSubstituidora
        {
            get
            {
                return this.nfseSubstituidoraField;
            }
            set
            {
                this.nfseSubstituidoraField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class SubstituirNfseRespostaRetSubstituicaoNfseSubstituida
    {

        private tcCompNfse compNfseField;

        private tcMensagemRetorno[] listaMensagemAlertaRetornoField;

        /// <remarks/>
        public tcCompNfse CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("MensagemRetorno", IsNullable = false)]
        public tcMensagemRetorno[] ListaMensagemAlertaRetorno
        {
            get
            {
                return this.listaMensagemAlertaRetornoField;
            }
            set
            {
                this.listaMensagemAlertaRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class SubstituirNfseRespostaRetSubstituicaoNfseSubstituidora
    {

        private tcCompNfse compNfseField;

        /// <remarks/>
        public tcCompNfse CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarLoteRpsEnvio
    {

        private tcIdentificacaoPrestador prestadorField;

        private string protocoloField;

        /// <remarks/>
        public tcIdentificacaoPrestador Prestador
        {
            get
            {
                return this.prestadorField;
            }
            set
            {
                this.prestadorField = value;
            }
        }

        /// <remarks/>
        public string Protocolo
        {
            get
            {
                return this.protocoloField;
            }
            set
            {
                this.protocoloField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarLoteRpsResposta
    {

        private sbyte situacaoField;

        private object itemField;

        /// <remarks/>
        public sbyte Situacao
        {
            get
            {
                return this.situacaoField;
            }
            set
            {
                this.situacaoField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetornoLote", typeof(ListaMensagemRetornoLote))]
        [System.Xml.Serialization.XmlElementAttribute("ListaNfse", typeof(ConsultarLoteRpsRespostaListaNfse))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarLoteRpsRespostaListaNfse
    {

        private tcCompNfse[] compNfseField;

        private tcMensagemRetorno[] listaMensagemAlertaRetornoField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CompNfse")]
        public tcCompNfse[] CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("MensagemRetorno", IsNullable = false)]
        public tcMensagemRetorno[] ListaMensagemAlertaRetorno
        {
            get
            {
                return this.listaMensagemAlertaRetornoField;
            }
            set
            {
                this.listaMensagemAlertaRetornoField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseRpsEnvio
    {

        private tcIdentificacaoRps identificacaoRpsField;

        private tcIdentificacaoPrestador prestadorField;

        /// <remarks/>
        public tcIdentificacaoRps IdentificacaoRps
        {
            get
            {
                return this.identificacaoRpsField;
            }
            set
            {
                this.identificacaoRpsField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoPrestador Prestador
        {
            get
            {
                return this.prestadorField;
            }
            set
            {
                this.prestadorField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseRpsResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CompNfse", typeof(tcCompNfse))]
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseServicoPrestadoEnvio
    {

        private tcIdentificacaoPrestador prestadorField;

        private string numeroNfseField;

        private object itemField;

        private tcIdentificacaoTomador tomadorField;

        private tcIdentificacaoIntermediario intermediarioField;

        private string paginaField;

        /// <remarks/>
        public tcIdentificacaoPrestador Prestador
        {
            get
            {
                return this.prestadorField;
            }
            set
            {
                this.prestadorField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NumeroNfse
        {
            get
            {
                return this.numeroNfseField;
            }
            set
            {
                this.numeroNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PeriodoCompetencia", typeof(ConsultarNfseServicoPrestadoEnvioPeriodoCompetencia))]
        [System.Xml.Serialization.XmlElementAttribute("PeriodoEmissao", typeof(ConsultarNfseServicoPrestadoEnvioPeriodoEmissao))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoTomador Tomador
        {
            get
            {
                return this.tomadorField;
            }
            set
            {
                this.tomadorField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoIntermediario Intermediario
        {
            get
            {
                return this.intermediarioField;
            }
            set
            {
                this.intermediarioField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string Pagina
        {
            get
            {
                return this.paginaField;
            }
            set
            {
                this.paginaField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseServicoPrestadoEnvioPeriodoCompetencia
    {

        private System.DateTime dataInicialField;

        private System.DateTime dataFinalField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataInicial
        {
            get
            {
                return this.dataInicialField;
            }
            set
            {
                this.dataInicialField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataFinal
        {
            get
            {
                return this.dataFinalField;
            }
            set
            {
                this.dataFinalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseServicoPrestadoEnvioPeriodoEmissao
    {

        private System.DateTime dataInicialField;

        private System.DateTime dataFinalField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataInicial
        {
            get
            {
                return this.dataInicialField;
            }
            set
            {
                this.dataInicialField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataFinal
        {
            get
            {
                return this.dataFinalField;
            }
            set
            {
                this.dataFinalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseServicoPrestadoResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("ListaNfse", typeof(ConsultarNfseServicoPrestadoRespostaListaNfse))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseServicoPrestadoRespostaListaNfse
    {

        private tcCompNfse[] compNfseField;

        private string proximaPaginaField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CompNfse")]
        public tcCompNfse[] CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string ProximaPagina
        {
            get
            {
                return this.proximaPaginaField;
            }
            set
            {
                this.proximaPaginaField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseServicoTomadoEnvio
    {

        private tcIdentificacaoConsulente consulenteField;

        private string numeroNfseField;

        private object itemField;

        private tcIdentificacaoPrestador prestadorField;

        private tcIdentificacaoTomador tomadorField;

        private tcIdentificacaoIntermediario intermediarioField;

        private string paginaField;

        /// <remarks/>
        public tcIdentificacaoConsulente Consulente
        {
            get
            {
                return this.consulenteField;
            }
            set
            {
                this.consulenteField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NumeroNfse
        {
            get
            {
                return this.numeroNfseField;
            }
            set
            {
                this.numeroNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("PeriodoCompetencia", typeof(ConsultarNfseServicoTomadoEnvioPeriodoCompetencia))]
        [System.Xml.Serialization.XmlElementAttribute("PeriodoEmissao", typeof(ConsultarNfseServicoTomadoEnvioPeriodoEmissao))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoPrestador Prestador
        {
            get
            {
                return this.prestadorField;
            }
            set
            {
                this.prestadorField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoTomador Tomador
        {
            get
            {
                return this.tomadorField;
            }
            set
            {
                this.tomadorField = value;
            }
        }

        /// <remarks/>
        public tcIdentificacaoIntermediario Intermediario
        {
            get
            {
                return this.intermediarioField;
            }
            set
            {
                this.intermediarioField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string Pagina
        {
            get
            {
                return this.paginaField;
            }
            set
            {
                this.paginaField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseServicoTomadoEnvioPeriodoCompetencia
    {

        private System.DateTime dataInicialField;

        private System.DateTime dataFinalField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataInicial
        {
            get
            {
                return this.dataInicialField;
            }
            set
            {
                this.dataInicialField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataFinal
        {
            get
            {
                return this.dataFinalField;
            }
            set
            {
                this.dataFinalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseServicoTomadoEnvioPeriodoEmissao
    {

        private System.DateTime dataInicialField;

        private System.DateTime dataFinalField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataInicial
        {
            get
            {
                return this.dataInicialField;
            }
            set
            {
                this.dataInicialField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime DataFinal
        {
            get
            {
                return this.dataFinalField;
            }
            set
            {
                this.dataFinalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseServicoTomadoResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("ListaNfse", typeof(ConsultarNfseServicoTomadoRespostaListaNfse))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseServicoTomadoRespostaListaNfse
    {

        private tcCompNfse[] compNfseField;

        private string proximaPaginaField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CompNfse")]
        public tcCompNfse[] CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string ProximaPagina
        {
            get
            {
                return this.proximaPaginaField;
            }
            set
            {
                this.proximaPaginaField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseFaixaEnvio
    {

        private tcIdentificacaoPrestador prestadorField;

        private ConsultarNfseFaixaEnvioFaixa faixaField;

        private string paginaField;

        /// <remarks/>
        public tcIdentificacaoPrestador Prestador
        {
            get
            {
                return this.prestadorField;
            }
            set
            {
                this.prestadorField = value;
            }
        }

        /// <remarks/>
        public ConsultarNfseFaixaEnvioFaixa Faixa
        {
            get
            {
                return this.faixaField;
            }
            set
            {
                this.faixaField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string Pagina
        {
            get
            {
                return this.paginaField;
            }
            set
            {
                this.paginaField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseFaixaEnvioFaixa
    {

        private string numeroNfseInicialField;

        private string numeroNfseFinalField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NumeroNfseInicial
        {
            get
            {
                return this.numeroNfseInicialField;
            }
            set
            {
                this.numeroNfseInicialField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string NumeroNfseFinal
        {
            get
            {
                return this.numeroNfseFinalField;
            }
            set
            {
                this.numeroNfseFinalField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://www.abrasf.org.br/nfse.xsd", IsNullable = false)]
    public partial class ConsultarNfseFaixaResposta
    {

        private object itemField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ListaMensagemRetorno", typeof(ListaMensagemRetorno))]
        [System.Xml.Serialization.XmlElementAttribute("ListaNfse", typeof(ConsultarNfseFaixaRespostaListaNfse))]
        public object Item
        {
            get
            {
                return this.itemField;
            }
            set
            {
                this.itemField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://www.abrasf.org.br/nfse.xsd")]
    public partial class ConsultarNfseFaixaRespostaListaNfse
    {

        private tcCompNfse[] compNfseField;

        private string proximaPaginaField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("CompNfse")]
        public tcCompNfse[] CompNfse
        {
            get
            {
                return this.compNfseField;
            }
            set
            {
                this.compNfseField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "nonNegativeInteger")]
        public string ProximaPagina
        {
            get
            {
                return this.proximaPaginaField;
            }
            set
            {
                this.proximaPaginaField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("Transforms", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class TransformsType
    {

        private TransformType[] transformField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Transform")]
        public TransformType[] Transform
        {
            get
            {
                return this.transformField;
            }
            set
            {
                this.transformField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("Manifest", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class ManifestType
    {

        private ReferenceType[] referenceField;

        private string idField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Reference")]
        public ReferenceType[] Reference
        {
            get
            {
                return this.referenceField;
            }
            set
            {
                this.referenceField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("SignatureProperties", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SignaturePropertiesType
    {

        private SignaturePropertyType[] signaturePropertyField;

        private string idField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SignatureProperty")]
        public SignaturePropertyType[] SignatureProperty
        {
            get
            {
                return this.signaturePropertyField;
            }
            set
            {
                this.signaturePropertyField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "********")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://www.w3.org/2000/09/xmldsig#")]
    [System.Xml.Serialization.XmlRootAttribute("SignatureProperty", Namespace = "http://www.w3.org/2000/09/xmldsig#", IsNullable = false)]
    public partial class SignaturePropertyType
    {

        private System.Xml.XmlElement[] itemsField;

        private string[] textField;

        private string targetField;

        private string idField;

        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlElement[] Items
        {
            get
            {
                return this.itemsField;
            }
            set
            {
                this.itemsField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text
        {
            get
            {
                return this.textField;
            }
            set
            {
                this.textField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "anyURI")]
        public string Target
        {
            get
            {
                return this.targetField;
            }
            set
            {
                this.targetField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType = "ID")]
        public string Id
        {
            get
            {
                return this.idField;
            }
            set
            {
                this.idField = value;
            }
        }
    }
}