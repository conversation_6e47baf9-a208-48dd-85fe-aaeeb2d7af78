﻿using Perlink.Shared.Compression;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.RPS.Enums;
using Perlink.Trinks.RPS.GeradorDeArquivo.NFCe;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml.Serialization;

namespace Perlink.Trinks.RPS.GeradorDeArquivo
{

    public static class GeradorChaveBrasilia
    {

        public static string GerarChave(TNFeInfNFe infNFe)
        {
            var cpfcnpj = Regex.Match(infNFe.emit.Item, @"\d+").Value;

            var tipoEmissao = "";
            var tamanhocNf = 9;
            var anoMesEmissao = Convert.ToDateTime(infNFe.ide.dhEmi).ToString("yyMM");

            if (Decimal.Parse(infNFe.versao, CultureInfo.InvariantCulture) >= 2)
            { //De acordo com o manual de oriantação v5.0 pág. 92
                var stringEnum = infNFe.ide.tpEmis.ToString();
                tipoEmissao = stringEnum.Substring(stringEnum.Length - 1);
                tamanhocNf = 8;
            }

            var stringcUFEnum = infNFe.ide.cUF.ToString();
            var cUF = stringcUFEnum.Substring(stringcUFEnum.Length - 2);

            var stringModEnum = infNFe.ide.mod.ToString();
            var mod = stringModEnum.Substring(stringModEnum.Length - 2);

            var chave =
                cUF.PadLeft(2, '0') + //cUF - Código da UF do emitente do Documento Fisca
                anoMesEmissao + //AAMM - Ano e Mês de emissão da NF-e
                cpfcnpj.PadLeft(14, '0') + //CNPJ - CNPJ do emitente
                mod.PadLeft(2, '0') + //mod - Modelo do Documento Fiscal
                infNFe.ide.serie.ToString().PadLeft(3, '0') + //serie - Série do Documento Fiscal
                infNFe.ide.nNF.ToString().PadLeft(9, '0') + //nNF - Número do Documento Fiscal
                tipoEmissao + //tpEmis – forma de emissão da NF-e
                infNFe.ide.cNF.PadLeft(tamanhocNf, '0'); //cNF - Código Numérico que compõe a Chave de Acesso

            var cDv = GerarDigitoVerificadorNFe(chave);
            infNFe.ide.cDV = cDv;
            return chave + cDv;
        }

        public static string GerarId(string chave)
        {
            return "NFe" + chave;
        }

        private static string GerarDigitoVerificadorNFe(string chave)
        {
            var soma = 0; // Vai guardar a Soma
            var mod = -1; // Vai guardar o Resto da divisão
            var dv = -1; // Vai guardar o DigitoVerificador
            var pesso = 2; // vai guardar o pesso de multiplicacao

            //percorrendo cada caracter da chave da direita para esquerda para fazer os calculos com o pesso
            for (var i = chave.Length - 1; i != -1; i--)
            {
                var ch = Convert.ToInt32(chave[i].ToString());
                soma += ch * pesso;
                //sempre que for 9 voltamos o pesso a 2
                if (pesso < 9)
                    pesso += 1;
                else
                    pesso = 2;
            }

            //Agora que tenho a soma vamos pegar o resto da divisão por 11
            mod = soma % 11;
            //Aqui temos uma regrinha, se o resto da divisão for 0 ou 1 então o dv vai ser 0
            if (mod == 0 || mod == 1)
                dv = 0;
            else
                dv = 11 - mod;

            return dv.ToString();
        }
    }

    public class GeradorDeArquivoRPS_NFCe : GeradorDeArquivoRPS_Base
    {
        private DateTime _dataHora;


        public GeradorDeArquivoRPS_NFCe(string codigoIbgeMunicipio) : base(codigoIbgeMunicipio)
        {
            _dataHora = Calendario.Agora();
        }

        public override bool EnviaPorWebService(PessoaJuridicaConfiguracaoNFe configuracaoNFe)
        {
            return true;
        }

        private PessoaJuridicaConfiguracaoNFe ConfiguracaoNFe { get; set; }
        private PessoaJuridica PessoaJuridica { get; set; }
        private Transacao Transacao { get; set; }


        public override int LimiteDeItensPorLoteWebservice
        {
            get
            {
                return 100;
            }
        }

        public override CamposExistentes CamposExistentes()
        {
            return new CamposExistentes
            {
                AliquotaISS = true,
                AliquotaISSNaoFormatada = false,
                CodigoRegimeEspecialTributacao = true,
                ItemListaServico = true,
                CodigoTributacaoMunicipio = true,
                CertificadoDigital = true,
                Cnae = true
            };
        }

        public override CamposObrigatorios CamposObrigatorios()
        {
            return new CamposObrigatorios
            {
                AliquotaISS = true,
                AliquotaISSNaoFormatada = false,
                CodigoRegimeEspecialTributacao = true,
                ItemListaServico = true,
                CodigoTributacaoMunicipio = false,
                CertificadoDigital = true
            };
        }

        public override MemoryStream Gerar(IEnumerable<DadosParaGeracaoLoteRPSDTO> registros, PessoaJuridica pessoaJuridica, int lote)
        {
            ConfiguracaoNFe = pessoaJuridica.ConfiguracaoNFe;

            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoaJuridica.IdPessoa);

            PessoaJuridica = pessoaJuridica;

            var arquivoZip = new ZipFile();

            if (estabelecimento != null)
            {
                int numeroNFe = 0;
                numeroNFe = estabelecimento.ConfiguracaoDeNFC.NumeroDaUltimaNFC ?? 0; // Como é conjugado, tem que usar a numeração do NFC

                foreach (var registro in registros)
                {
                    numeroNFe++;

                    var nota = GetNf(registro, numeroNFe, lote);
                    var dadosXml = GerarXml(nota, pessoaJuridica);
                    var nomeXml = ObterNomeArquivoXml(numeroNFe, lote);

                    if (!arquivoZip.Streams.Any(p => p.Key == nomeXml))
                        arquivoZip.Streams.Add(nomeXml, dadosXml); //nome do item do dicionario
                }

                estabelecimento.ConfiguracaoDeNFC.NumeroDaUltimaNFC = ConfiguracaoNFe.UltimoRpsEmitido = numeroNFe; //Atualizar o ultimo NFC
            }
            else
            {
                foreach (var registro in registros)
                {
                    var numeroNFe = registro.DadosRPS.EmissoesRPS.Max(f => f.Numero);

                    var nota = GetNf(registro, numeroNFe, lote);
                    var dadosXml = GerarXml(nota, pessoaJuridica);
                    var nomeXml = ObterNomeArquivoXml(numeroNFe, lote);

                    if (!arquivoZip.Streams.Any(p => p.Key == nomeXml))
                        arquivoZip.Streams.Add(nomeXml, dadosXml); //nome do item do dicionario
                }
            }

            var conteudoZip = new MemoryStream();
            arquivoZip.Save(conteudoZip, ZipContentType.Stream);
            conteudoZip.Flush();
            conteudoZip.Seek(0, SeekOrigin.Begin);
            return conteudoZip;
        }

        public TNFeInfNFePagTPag GetTPag(FormaPagamento item)
        {
            return (TNFeInfNFePagTPag)Enum.Parse(typeof(TNFeInfNFePagTPag), "Item" + item.IdFormaPagamentoNFe.Value.ToString("00"));
        }

        public override string ObterNomeArquivo(int lote)
        {
            return string.Format("LOTE_{0}_{1:yyyyMMddhhmmss}.zip", lote, _dataHora);
        }

        public string ObterNomeArquivoXml(int numeroRps, int lote)
        {
            var nomeArquivoExterno = ObterNomeArquivo(lote);
            nomeArquivoExterno = nomeArquivoExterno.Replace(".zip", "");

            return string.Format("{0}_N_RPS_{1}.xml", nomeArquivoExterno, numeroRps);
        }

        public override void ValidarConfiguracao(PessoaJuridicaConfiguracaoNFe configuracao, TipoGerarPara tipoDeGeracao)
        {
            base.ValidarConfiguracao(configuracao, tipoDeGeracao);

            var codigoServico = configuracao.ItemListaServico;

            //try {
            //    Enum.Parse(typeof(TCListServ), "Item" + codigoServico);
            //}
            //catch (Exception e) {
            //    throw new ArgumentException("O código de Tipo de Serviço Prestado informado pelo cliente é inválido (" + codigoServico + "). Deve ser um valor entre 0101 e 4001.");
            //}

            //if (configuracao.AliquotaISS < 0 || configuracao.AliquotaISS > 100) {
            //    throw new ArgumentException("O valor do ISS deve ser maior que zero.");
            //}

            if (configuracao.AliquotaISS < 0 || configuracao.AliquotaISS > 100)
            {
                throw new ArgumentException("O valor do ISS deve estar entre 0,00 e 100,00.");
            }
        }

        protected virtual TNFeInfNFeInfAdic GetInfAdic(String observacao)
        {
            var infAdic = new TNFeInfNFeInfAdic()
            {
                infCpl = observacao
            };

            return infAdic;
        }

        protected virtual TNFeInfNFeTotal GetTotal(TNFeInfNFe infNFe, DadosParaGeracaoLoteRPSDTO registro)
        {
            var vServ = registro.ValorTotal;

            var icmsTot = new TNFeInfNFeTotalICMSTot
            {
                vICMSDeson = "0",
                vBCST = "0",
                vST = "0",
                vFrete = "0",
                vSeg = "0",
                vII = "0",
                vIPI = "0",
                vPIS = "0",
                vCOFINS = "0",
                vOutro = "0",
                vBC = "0",
                vICMS = "0",
                vProd = "0",
                vNF = convertDecimalToString(vServ),
                vDesc = "0"
            };

            var valores = from s in registro.Servicos
                          let valor = (s.Valor / 100) * PessoaJuridica.ConfiguracaoNFe.AliquotaISS
                          select Math.Round(valor, 2);
            var vISS = valores.Sum();

            var issqnTot = new TNFeInfNFeTotalISSQNtot
            {
                vServ = convertDecimalToString(vServ, true),
                vBC = convertDecimalToString(vServ, true),
                vISS = convertDecimalToString(vISS, true),
                dCompet = registro.DataTransacao.ToString("yyyy-MM-dd")
            };

            var tot = new TNFeInfNFeTotal
            {
                ICMSTot = icmsTot,
                ISSQNtot = issqnTot
            };
            return tot;
        }

        private string convertDecimalToString(decimal value, bool nuloSeZero = false)
        {
            if ((value == 0 || value < 0.01m) && nuloSeZero)
                return null;

            return string.Format(CultureInfo.InvariantCulture, "{0:N2}", value);
        }

        private string convertDecimalToString(decimal value, int casasDecimais)
        {
            return string.Format(CultureInfo.InvariantCulture, "{0:N" + casasDecimais + "}", value);
        }

        private Stream GerarXml(TNFe nota, PessoaJuridica pessoaJuridica)
        {
            var retorno = new MemoryStream();
            var serializer = new XmlSerializer(typeof(TNFe));
            XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
            ns.Add("", "http://www.portalfiscal.inf.br/nfe");
            serializer.Serialize(retorno, nota, ns);
            retorno.Flush();
            retorno.Seek(0, SeekOrigin.Begin);
            var x509Cert = AssinaturaDigital.ObterCertificadoDaPJ(pessoaJuridica);

            var xmlRpsAssinado = AssinaturaDigital.AssinarXML(retorno, "infNFe", x509Cert);
            return xmlRpsAssinado;
        }

        private TNFe GetNf(DadosParaGeracaoLoteRPSDTO registro, int numeroNfe, int lote)
        {
            Transacao = Domain.Financeiro.TransacaoRepository.Load(registro.IdTransacao);

            var infNFe = new TNFeInfNFe
            {
                versao = "4.0",
                ide = GetIdentificacao(numeroNfe.ToString(), registro.EnderecoDoCliente.UF),
                emit = GetEmitente(),
                dest = GetDestinatario(registro),
                transp = new TNFeInfNFeTransp { modFrete = TNFeInfNFeTranspModFrete.Item9 } //Sem frete
            };

            IncluirServicos(infNFe, registro);

            infNFe.total = GetTotal(infNFe, registro);

            var idUFEstabelecimento = PessoaJuridica.EnderecoProprio.UF.IdUF;
            var configNFCeDoUF = Domain.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.ObterConfiguracaoNFCeDoUF(idUFEstabelecimento);

            if (configNFCeDoUF != null && !String.IsNullOrEmpty(configNFCeDoUF.ObservacaoCorpoNFCE))
                infNFe.infAdic = GetInfAdic(configNFCeDoUF.ObservacaoCorpoNFCE + " " + registro.TextoInformandoValorDosImpostos);
            else if (!registro.DadosFiscaisDoServicoNaoInformado)
                infNFe.infAdic = GetInfAdic(registro.TextoInformandoValorDosImpostos);

            var chave = GeradorChaveBrasilia.GerarChave(infNFe);
            infNFe.Id = GeradorChaveBrasilia.GerarId(chave);

            return new TNFe() { infNFe = infNFe };
        }

        #region Serviços e Produtos

        private void IncluirServicos(TNFeInfNFe infNFe, DadosParaGeracaoLoteRPSDTO registro)
        {
            var itens = new List<TNFeInfNFeDet>();

            var enderecoDoEstabelecimento = PessoaJuridica.EnderecoProprio;

            for (int i = 0; i < registro.Servicos.Count; i++)
            {
                var codigoServico = PessoaJuridica.ConfiguracaoNFe.ItemListaServico;
                TCListServ cListServ = TCListServ.Item0101;

                try
                {
                    cListServ = (TCListServ)Enum.Parse(typeof(TCListServ), "Item" + codigoServico);
                }
                catch (Exception e)
                {
                    throw new ArgumentException("O Tipo de Serviço Prestado informado pelo cliente é inválido (" + codigoServico + "). Por favor, verifique se o estabelecimento está configurado corretamente.");
                }

                var items = ObterItensImposto(registro, cListServ, i);

                var imposto = new TNFeInfNFeDetImposto
                {
                    Items = items.ToArray(),
                    PIS = new TNFeInfNFeDetImpostoPIS()
                    {
                        Item = new TNFeInfNFeDetImpostoPISPISAliq
                        {
                            CST = TNFeInfNFeDetImpostoPISPISAliqCST.Item01,
                            vBC = convertDecimalToString(0),
                            pPIS = convertDecimalToString(0),
                            vPIS = convertDecimalToString(0)
                        }
                    },
                    COFINS = new TNFeInfNFeDetImpostoCOFINS()
                    {
                        Item = new TNFeInfNFeDetImpostoCOFINSCOFINSAliq
                        {
                            CST = TNFeInfNFeDetImpostoCOFINSCOFINSAliqCST.Item01,
                            pCOFINS = convertDecimalToString(0),
                            vBC = convertDecimalToString(0),
                            vCOFINS = convertDecimalToString(0)
                        }
                    }
                };

                var cProd = i + 1;
                var det = new TNFeInfNFeDet
                {
                    nItem = cProd.ToString(),
                    prod = new TNFeInfNFeDetProd
                    {
                        cProd = cProd.ToString().PadLeft(5, '0'),
                        NCM = "00",
                        cEAN = "",
                        cEANTrib = "",
                        CFOP = (registro.EnderecoDoCliente == null || registro.EnderecoDoCliente.UF.Sigla == enderecoDoEstabelecimento.UF.Sigla) ? TCfop.Item5933 : TCfop.Item6933,
                        xProd = registro.Servicos[i].Descricao,
                        uCom = "UN",
                        vProd = convertDecimalToString(registro.Servicos[i].Valor),
                        vUnCom = convertDecimalToString(registro.Servicos[i].Valor, 10),
                        qCom = convertDecimalToString(1, 4),
                        indTot = TNFeInfNFeDetProdIndTot.Item1,
                        qTrib = convertDecimalToString(1, 4),
                        uTrib = "UN",
                        vUnTrib = convertDecimalToString(registro.Servicos[i].Valor, 10)
                    },
                    imposto = imposto
                };

                itens.Add(det);
            }

            infNFe.det = itens.ToArray();
        }

        private List<object> ObterItensImposto(DadosParaGeracaoLoteRPSDTO registro, TCListServ cListServ, int index)
        {
            var items = new List<object>();

            var vServ = registro.Servicos[index].Valor;

            var vISS = Math.Round((vServ / 100) * PessoaJuridica.ConfiguracaoNFe.AliquotaISS, 2);

            items.Add(new TNFeInfNFeDetImpostoISSQN()
            {
                vBC = convertDecimalToString(vServ),
                cMunFG = PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE,
                vAliq = convertDecimalToString(PessoaJuridica.ConfiguracaoNFe.AliquotaISS),
                vISSQN = convertDecimalToString(vISS),
                cListServ = cListServ
            });

            return items;
        }

        #endregion Serviços e Produtos

        #region GetIdentificacao

        private TNFeInfNFeIde GetIdentificacao(string numeroNf, UF ufCliente)
        {
            var enderecoDoEstabelecimento = PessoaJuridica.EnderecoProprio;
            if (enderecoDoEstabelecimento == null)
                throw new ArgumentException("O estabelecimento não possui endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.UF == null)
                throw new ArgumentException("O estabelecimento não possui UF em seu endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.BairroEntidade == null)
                throw new ArgumentException("O estabelecimento não possui Bairro em seu endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.UF.CodigoIBGE == null)
                throw new ArgumentException("O estabelecimento não possui UF com código IBGE em seu endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.BairroEntidade.Cidade.CodigoIBGE == null)
                throw new ArgumentException("O estabelecimento não possui CIDADE com código IBGE em seu endereço para emissão de NFC-e");

            var codigoIBGEdaUF = (TCodUfIBGE)Enum.Parse(typeof(TCodUfIBGE), "Item" + enderecoDoEstabelecimento.UF.CodigoIBGE);
            Random randNum = new Random();

            var ide = new TNFeInfNFeIde
            {
                cUF = codigoIBGEdaUF,
                natOp = "VENDA",
                indPag = TNFeInfNFeIdeIndPag.Item0, //A vista
                mod = TMod.Item55, //NFe
                serie = "1",
                nNF = numeroNf,
                tpNF = TNFeInfNFeIdeTpNF.Item1, //saída
                cMunFG = enderecoDoEstabelecimento.BairroEntidade.Cidade.CodigoIBGE,
                tpEmis = TNFeInfNFeIdeTpEmis.Item1, //Normal
                tpImp = TNFeInfNFeIdeTpImp.Item1, //Para NF-e somente poderá ser tipos 1 ou 2
                cNF = randNum.Next(9999999, 99999999).ToString(), //Código numérico que compõe a Chave de Acesso. Número aleatório gerado pelo emitente para cada NF-e.
                tpAmb = GetTpAmb(),
                finNFe = TFinNFe.Item1, //Normal
                verProc = "3.000",
                dhEmi = Calendario.Agora().ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ss''K"),
                idDest = enderecoDoEstabelecimento.UF.Sigla == ufCliente.Sigla ? TNFeInfNFeIdeIdDest.Item1 : TNFeInfNFeIdeIdDest.Item2
            };

            return ide;
        }

        private TAmb GetTpAmb()
        {
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(PessoaJuridica.IdPessoa);

            var ambiente = TAmb.Item2;
            bool nfcProducao = true;

            if (estabelecimento.EstabelecimentoPossuiNFC())
                nfcProducao = estabelecimento.ConfiguracaoDeNFC.NFCProducaoLevandoEmContaParametro();

            if (nfcProducao)
            {
                ambiente = TAmb.Item1;
            }

            return ambiente;
            //var endereco = PessoaJuridica.EnderecoProprio;
            //var configuracaoPadraoNFS = Domain.RPS.ConfiguracaoPadraoNFSRepository.ObterValoresDefaultPorCidade(endereco.BairroEntidade.Cidade);

            //return configuracaoPadraoNFS.AmbienteHomologacao
            //    ? TAmb.Item2 // Homologação
            //    : TAmb.Item1; // Produção
        }

        #endregion GetIdentificacao

        #region GetEmitente

        public TUfEmi GetTUfEmi(string siglaUF)
        {
            return (TUfEmi)Enum.Parse(typeof(TUfEmi), siglaUF);
        }

        private TNFeInfNFeEmit GetEmitente()
        {
            var emit = new TNFeInfNFeEmit();

            emit.Item = PessoaJuridica.CNPJ;
            emit.ItemElementName = ItemChoiceType2.CNPJ;
            emit.IE = PessoaJuridica.InscricaoEstadual.ToUpper();
            emit.xNome = PessoaJuridica.RazaoSocial.Trim();
            emit.xFant = PessoaJuridica.NomeFantasia;
            emit.IM = PessoaJuridica.InscricaoMunicipal;

            var endereco = new TEnderEmi();
            endereco.xLgr = (PessoaJuridica.EnderecoProprio.TipoLogradouro != null ? PessoaJuridica.EnderecoProprio.TipoLogradouro.Nome : "") + " " + PessoaJuridica.EnderecoProprio.Logradouro;
            endereco.xLgr = endereco.xLgr.Trim();

            endereco.nro = PessoaJuridica.EnderecoProprio.Numero.Trim();
            if (!string.IsNullOrWhiteSpace(PessoaJuridica.EnderecoProprio.Complemento))
                endereco.xCpl = PessoaJuridica.EnderecoProprio.Complemento.Trim();
            endereco.xBairro = PessoaJuridica.EnderecoProprio.Bairro.Trim();
            endereco.cMun = PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE.Trim();
            endereco.xMun = PessoaJuridica.EnderecoProprio.Cidade.Trim();
            endereco.UF = GetTUfEmi(PessoaJuridica.EnderecoProprio.UF.Sigla);
            endereco.CEP = PessoaJuridica.EnderecoProprio.Cep.Trim();
            endereco.fone = null;

            if (!String.IsNullOrEmpty(PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE))
                endereco.cMun = PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE;

            var telefones = Domain.Pessoas.TelefoneRepository.ObterTelefonesDaPessoa(PessoaJuridica.IdPessoa).TelefonesNacionais();
            if (telefones.Any())
            {
                var telefone = telefones.First();
                var numero = String.Format("{0}{1}", telefone.DDD, telefone.Numero);
                endereco.fone = numero;
            }

            endereco.cPais = TEnderEmiCPais.Item1058;
            endereco.xPais = TEnderEmiXPais.BRASIL;
            emit.enderEmit = endereco;
            return emit;
        }

        #endregion GetEmitente

        #region GetDestinatario

        public TUf GetTUf(string sigla)
        {
            return (TUf)Enum.Parse(typeof(TUf), sigla);
        }

        private TNFeInfNFeDest GetDestinatario(DadosParaGeracaoLoteRPSDTO registro)
        {
            var cliente = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(registro.idClienteEstabelecimento);
            if (string.IsNullOrEmpty(cliente.Cliente.PessoaFisica.Cpf))
                return new TNFeInfNFeDest();

            var dest = new TNFeInfNFeDest();

            dest.Item = cliente.Cliente.PessoaFisica.Cpf;
            dest.ItemElementName = ItemChoiceType3.CPF;
            dest.xNome = cliente.Cliente.PessoaFisica.NomeCompleto.Trim();
            dest.indIEDest = TNFeInfNFeDestIndIEDest.Item2;

            if (cliente.Endereco != null)
            {
                var codigoIBGE = Domain.Pessoas.CidadeRepository.ObterCodigoIBGEDoClienteOuEstabelecimento(cliente.Endereco.UF, cliente.Endereco.Cidade,
                        PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade);

                // Para enviar o endereço, precisa ter todas as informações do cliente.
                var temTodasInformacoesParaEndereco = !(string.IsNullOrEmpty(cliente.Endereco.Logradouro)
                                                        || string.IsNullOrEmpty(cliente.Endereco.Numero)
                                                        || string.IsNullOrEmpty(cliente.Endereco.Bairro)
                                                        || string.IsNullOrEmpty(cliente.Endereco.Cidade)
                                                        || string.IsNullOrEmpty(cliente.Endereco.Cep)
                                                        || cliente.Endereco.UF == null
                                                        || codigoIBGE <= 0);

                if (temTodasInformacoesParaEndereco)
                {
                    dest.enderDest = new TEndereco
                    {
                        xLgr = cliente.Endereco.Logradouro.Trim(),
                        nro = cliente.Endereco.Numero.Trim(),
                        xCpl = string.IsNullOrWhiteSpace(cliente.Endereco.Complemento) ? "-" : cliente.Endereco.Complemento.Trim(),
                        xBairro = cliente.Endereco.Bairro.Trim(),
                        cMun = codigoIBGE.ToString().Trim(),
                        xMun = cliente.Endereco.Cidade.Trim(),
                        UF = GetTUf(cliente.Endereco.UF.Sigla),
                        CEP = cliente.Endereco.Cep.Trim(),
                        cPais = Tpais.Item1058,
                        xPais = "BRASIL"
                    };
                }
            }

            return dest;
        }

        #endregion GetDestinatario

        public override bool PermiteDownload()
        {
            return false;
        }
    }
}