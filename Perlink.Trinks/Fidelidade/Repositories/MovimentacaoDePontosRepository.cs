﻿using NHibernate.Linq;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Fidelidade.DTO;
using Perlink.Trinks.Fidelidade.Enums;
using Perlink.Trinks.Fidelidade.Filtros;
using Perlink.Trinks.Financeiro;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Fidelidade.Repositories
{

    public partial class MovimentacaoDePontosRepository : IMovimentacaoDePontosRepository
    {

        public List<MovimentacaoDePontos> ObterMovimentacoesDePontosDestaTransacao
            (Transacao transacao)
        {
            List<MovimentacaoDePontos> movimentacoes = new List<MovimentacaoDePontos>();

            var movimentacoesDeHorarioTransacao = Domain.Fidelidade.MovimentacaoDePontosHorarioTransacaoRepository.ObterMovimentacoesDePontosDestaTransacao(transacao);
            var movimentacoesDeItemVenda = Domain.Fidelidade.MovimentacaoDePontosItemVendaRepository.ObterMovimentacoesDePontosDestaTransacao(transacao);
            var movimentacoesDeAgendamentoOnline = Domain.Fidelidade.MovimentacaoDePontosAgendamentoOnlineRepository.ObterMovimentacoesDePontosDestaTransacao(transacao);
            var movimentacoesDePagamentoAntecipado = Domain.Fidelidade.MovimentacaoDePontosPagamentoAntecipadoRepository.ObterMovimentacoesDePontosDestaTransacao(transacao);


            movimentacoes.AddRange(movimentacoesDeHorarioTransacao);
            movimentacoes.AddRange(movimentacoesDeItemVenda);
            movimentacoes.AddRange(movimentacoesDeAgendamentoOnline);
            movimentacoes.AddRange(movimentacoesDePagamentoAntecipado);

            return movimentacoes;
        }

        public MovimentacaoDePontos ObterPrimeiraMovimentacaoDoClienteEmEstabelecimento(int idPessoaDoEstabelecimento, int idPessoaDoCliente)
        {
            return Queryable()
                .Where(m => m.PessoaFisicaDoCliente.IdPessoa == idPessoaDoCliente &&
                            m.IdPessoaDoEstabelecimento == idPessoaDoEstabelecimento)
                .OrderBy(m => m.HoraMovimentacao)
                .Fetch(f => f.PontoGanhoQueAMovimentacaoGerou)
                .FirstOrDefault();
        }

        public List<MovimentacaoDePontos> ObterHistoricoDoCliente(FiltroHistoricoMovimentacaoDePontos filtroHistorico)
        {
            var query = Queryable()
                .Where(m => m.PessoaFisicaDoCliente.IdPessoa == filtroHistorico.IdPessoaFisicaDoCliente
                            && m.IdPessoaDoEstabelecimento == filtroHistorico.IdPessoaDoEstabelecimento);

            if (filtroHistorico.BuscarPorData)
            {
                query = query.Where(m => m.DataMovimentacao >= filtroHistorico.DataInicioDoFiltro.Date
                                         && m.DataMovimentacao <= filtroHistorico.DataFimDoFiltro.Date);
            }

            query = query.OrderByDescending(m => m.HoraMovimentacao).Fetch(f => f.PontoGanhoQueAMovimentacaoGerou);

            if (filtroHistorico.BuscaPaginada)
            {
                int qtdRegistrosIgnorar = filtroHistorico.Pagina > 1 ? ((filtroHistorico.Pagina - 1) * filtroHistorico.QuantidadeDeRegistros) : 0;
                query = query.Skip(qtdRegistrosIgnorar).Take(filtroHistorico.QuantidadeDeRegistros);
            }

            return query.ToList();
        }

        public List<RelatorioMovimentacaoDePontosDTO> ObterRelatorio(FiltroRelatorioMovimentacaoDePontos filtroRelatorio, out int totalGeralDePontosDeFidelidade, bool ehPaginado = false)
        {
            totalGeralDePontosDeFidelidade = 0;

            var retorno = new List<RelatorioMovimentacaoDePontosDTO>();

            if (filtroRelatorio.TipoDeConsulta == TipoConsultaMovimentacaoPontosEnum.PontosAExpirar)
            {
                return Domain.Fidelidade.PontoGanhoRepository.ObterRelatorioDePontosASeremUsados(filtroRelatorio, out totalGeralDePontosDeFidelidade, ehPaginado);
            }

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(filtroRelatorio.IdPessoaDoEstabelecimento);
            bool permiteTransferenciaDePontos = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();

            var filtrados = Filtrar(filtroRelatorio);
            var filtradosOrdenados = filtrados.OrderBy(x => x.DataMovimentacao);

            var busca = (from i in filtradosOrdenados
                         select new RelatorioMovimentacaoDePontosDTO
                         {
                             DataMovimentacao = i.HoraMovimentacao,
                             DataExpiracao = i.PontoGanhoQueAMovimentacaoGerou.DataValidade,
                             NomeCliente = i.PessoaFisicaDoCliente.NomeOuApelido(),
                             IdPessoaDoCliente = i.PessoaFisicaDoCliente.IdPessoa,
                             CPFDoCliente = i.PessoaFisicaDoCliente.Cpf,
                             EmailDoCliente = i.PessoaFisicaDoCliente.Email,
                             TipoDeItemMovimentado = i.TipoOrigem,
                             DescricaoDoItem = i.DescricaoDoItem,
                             PontosMovimentados = i.QuantidadeDePontos,
                             IdTransacao = i.IdTransacao,
                             IdMovimentacaoDePontos = i.Id,
                             NomeQuemProrrogou = i.PontoGanhoQueAMovimentacaoGerou.ResponsavelPelaUltimaProrrogacao.Apelido ?? i.PontoGanhoQueAMovimentacaoGerou.ResponsavelPelaUltimaProrrogacao.NomeCompleto,
                             DataUltimaProrrogacao = i.PontoGanhoQueAMovimentacaoGerou.DataHoraUltimaProrrogacaoDeValidade,
                             Status = (StatusMovimentacaoPontosEnum?)i.PontoGanhoQueAMovimentacaoGerou.Status
                         });

            int? totalDePontos = filtrados.Sum(x => (int?)x.QuantidadeDePontos); //total de pontos do programa de fidelidade conforme consulta
            totalGeralDePontosDeFidelidade = totalDePontos ?? 0;

            bool temRegistros = busca.Any();
            if (ehPaginado && temRegistros)
            {
                retorno = busca.ToResultadoPaginadoQueryable(filtroRelatorio.Paginacao).ToList();
            }
            else
            {
                retorno = busca.ToList();
            }

            filtroRelatorio.Paginacao.TotalItens = busca.Count();

            if (filtroRelatorio.CarregarSaldoDosClientes && temRegistros)
            {
                AplicarSaldoDePontosDosClientesNosRegistrosFiltrados(filtroRelatorio, estabelecimento, permiteTransferenciaDePontos, retorno, filtrados, ehPaginado);
            }

            if (filtroRelatorio.TipoDeConsulta == TipoConsultaMovimentacaoPontosEnum.Transferencias)
            {
                AplicarNomeDasUnidadesQueTiveramPontosTransferidosNosRegistrosFiltrados(filtroRelatorio, retorno, estabelecimento);
            }

            return retorno;
        }

        private void AplicarNomeDasUnidadesQueTiveramPontosTransferidosNosRegistrosFiltrados(FiltroRelatorioMovimentacaoDePontos filtroRelatorio, List<RelatorioMovimentacaoDePontosDTO> retorno, Pessoas.Estabelecimento estabelecimento)
        {
            var unidadesComPontoTransferido = ListarDadosDasUnidadesQueTiveramPontosTransferidos(filtroRelatorio, estabelecimento);

            foreach (var registroDoRelatorio in retorno)
            {
                var transferencia = unidadesComPontoTransferido.FirstOrDefault(u => u.IdMovimentacao == registroDoRelatorio.IdMovimentacaoDePontos);
                if (transferencia != null)
                {
                    registroDoRelatorio.NomeUnidade = transferencia.NomeUnidade;
                }
            }
        }

        private void AplicarSaldoDePontosDosClientesNosRegistrosFiltrados(FiltroRelatorioMovimentacaoDePontos filtroRelatorio, Pessoas.Estabelecimento estabelecimento, bool permiteTransferenciaDePontos, List<RelatorioMovimentacaoDePontosDTO> retorno, IQueryable<MovimentacaoDePontos> filtrados, bool ehPaginado)
        {

            var saldosDosPontosNesteEstabelecimento = new List<SaldoDePontosDaPessoaClienteDTO>();
            var saldosDosPontosEmOutrosEstabelecimentos = new List<SaldoDePontosDaPessoaClienteDTO>();
            if (!ehPaginado)
            {

                saldosDosPontosNesteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                        .Where(p => p.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento
                                                    && p.Ativo
                                                    && filtrados.Any(f => f.PessoaFisicaDoCliente.IdPessoa == p.Cliente.PessoaFisica.IdPessoa))
                                        .Select(p => new DTO.SaldoDePontosDaPessoaClienteDTO() { IdPessoaDoCliente = p.Cliente.PessoaFisica.IdPessoa, SaldoDePontosAtualizado = p.SaldoDePontosDeFidelidade }).ToList();

                if (permiteTransferenciaDePontos)
                {

                    saldosDosPontosEmOutrosEstabelecimentos = (from p in Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                                               where p.Estabelecimento.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                                   && p.Ativo
                                                                   && p.Estabelecimento.IdEstabelecimento != estabelecimento.IdEstabelecimento
                                                                   && filtrados.Any(f => f.PessoaFisicaDoCliente.Cpf == p.Cliente.PessoaFisica.Cpf
                                                                                          && f.PessoaFisicaDoCliente.Cpf != null && f.PessoaFisicaDoCliente.Cpf != "")
                                                               group p by new
                                                               {
                                                                   CPFDoCliente = p.Cliente.PessoaFisica.Cpf,
                                                                   EmailDoCliente = p.Cliente.PessoaFisica.Email
                                                               } into dataGroup
                                                               select new SaldoDePontosDaPessoaClienteDTO
                                                               {
                                                                   CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                                   EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                                   SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.SaldoDePontosDeFidelidade)
                                                               }).ToList();

                    saldosDosPontosEmOutrosEstabelecimentos.AddRange(
                                                    (from p in Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                                     where p.Estabelecimento.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                             && p.Ativo
                                                             && p.Estabelecimento.IdEstabelecimento != estabelecimento.IdEstabelecimento
                                                             && filtrados.Any(f => f.PessoaFisicaDoCliente.Email == p.Cliente.PessoaFisica.Email
                                                                                  && f.PessoaFisicaDoCliente.Email != null && f.PessoaFisicaDoCliente.Email != "")
                                                     group p by new
                                                     {
                                                         CPFDoCliente = p.Cliente.PessoaFisica.Cpf,
                                                         EmailDoCliente = p.Cliente.PessoaFisica.Email
                                                     } into dataGroup
                                                     select new SaldoDePontosDaPessoaClienteDTO
                                                     {
                                                         CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                         EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                         SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.SaldoDePontosDeFidelidade)
                                                     }).ToList());

                }
            }
            else if (ehPaginado)
            {
                var idsPessoaDosClientes = retorno.Select(r => r.IdPessoaDoCliente).ToList();
                var cpfsDosClientes = retorno.Select(r => r.CPFDoCliente).Where(cpf => !string.IsNullOrWhiteSpace(cpf)).ToList();
                var emailsDosClientes = retorno.Select(r => r.EmailDoCliente).Where(email => !string.IsNullOrWhiteSpace(email)).ToList();

                saldosDosPontosNesteEstabelecimento = Domain.Fidelidade.PontoGanhoRepository.Queryable()
                                        .Where(p => p.IdPessoaDoEstabelecimento == estabelecimento.PessoaJuridica.IdPessoa
                                                    && p.Status == StatusMovimentacaoPontosEnum.Disponivel
                                                    && p.IdPessoaDoEstabelecimento == estabelecimento.PessoaJuridica.IdPessoa
                                                    && idsPessoaDosClientes.Contains(p.PessoaFisicaDoCliente.IdPessoa))
                                        .GroupBy(g => g.PessoaFisicaDoCliente.IdPessoa)
                                        .Select(pg => new DTO.SaldoDePontosDaPessoaClienteDTO() { IdPessoaDoCliente = pg.Key, SaldoDePontosAtualizado = pg.Sum(pga => pga.QuantidadeDePontosDisponiveis) }).ToList();

                if (permiteTransferenciaDePontos)
                {
                    var buscaEstabelecimentos = Domain.Pessoas.EstabelecimentoRepository.Queryable();
                    saldosDosPontosEmOutrosEstabelecimentos = (from p in Domain.Fidelidade.PontoGanhoRepository.Queryable()
                                                               join e in buscaEstabelecimentos on p.IdPessoaDoEstabelecimento equals e.PessoaJuridica.IdPessoa
                                                               where e.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                                   && p.Status == StatusMovimentacaoPontosEnum.Disponivel
                                                                   && p.IdPessoaDoEstabelecimento != estabelecimento.PessoaJuridica.IdPessoa
                                                                   && emailsDosClientes.Contains(p.PessoaFisicaDoCliente.Email)
                                                               group p by new
                                                               {
                                                                   CPFDoCliente = p.PessoaFisicaDoCliente.Cpf,
                                                                   EmailDoCliente = p.PessoaFisicaDoCliente.Email
                                                               } into dataGroup
                                                               select new SaldoDePontosDaPessoaClienteDTO
                                                               {
                                                                   CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                                   EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                                   SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.QuantidadeDePontosDisponiveis)
                                                               }).ToList();

                    saldosDosPontosEmOutrosEstabelecimentos.AddRange(
                                                    (from p in Domain.Fidelidade.PontoGanhoRepository.Queryable()
                                                     join e in buscaEstabelecimentos on p.IdPessoaDoEstabelecimento equals e.PessoaJuridica.IdPessoa
                                                     where e.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                         && p.Status == StatusMovimentacaoPontosEnum.Disponivel
                                                         && p.IdPessoaDoEstabelecimento != estabelecimento.PessoaJuridica.IdPessoa
                                                         && cpfsDosClientes.Contains(p.PessoaFisicaDoCliente.Cpf)
                                                     group p by new
                                                     {
                                                         CPFDoCliente = p.PessoaFisicaDoCliente.Cpf,
                                                         EmailDoCliente = p.PessoaFisicaDoCliente.Email
                                                     } into dataGroup
                                                     select new SaldoDePontosDaPessoaClienteDTO
                                                     {
                                                         CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                         EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                         SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.QuantidadeDePontosDisponiveis)
                                                     }).ToList());
                }
            }

            foreach (var registroDoRelatorio in retorno)
            {
                registroDoRelatorio.SaldosDePontosDoCliente.RedePermiteTransferencias = permiteTransferenciaDePontos;
                var saldoNesteEstabelecimento = saldosDosPontosNesteEstabelecimento.FirstOrDefault(p => p.IdPessoaDoCliente == registroDoRelatorio.IdPessoaDoCliente);
                if (saldoNesteEstabelecimento != null)
                    registroDoRelatorio.SaldosDePontosDoCliente.PontosNesteEstabelecimento = saldoNesteEstabelecimento.SaldoDePontosAtualizado;

                if (permiteTransferenciaDePontos)
                {

                    var saldoEmOutrosEstabelecimentosPorEmailECPF = saldosDosPontosEmOutrosEstabelecimentos
                        .FirstOrDefault(p => registroDoRelatorio.EmailDoCliente != null && registroDoRelatorio.CPFDoCliente != null
                        && p.EmailDoCliente == registroDoRelatorio.EmailDoCliente && p.CPFDoCliente == registroDoRelatorio.CPFDoCliente);

                    var saldoEmOutrosEstabelecimentosPorEmailENaoCPF = saldosDosPontosEmOutrosEstabelecimentos
                        .FirstOrDefault(p => registroDoRelatorio.EmailDoCliente != null && p.EmailDoCliente == registroDoRelatorio.EmailDoCliente
                        && p.CPFDoCliente != registroDoRelatorio.CPFDoCliente);

                    var saldoEmOutrosEstabelecimentosPorCPFENaoEmail = saldosDosPontosEmOutrosEstabelecimentos
                        .FirstOrDefault(p => p.EmailDoCliente != registroDoRelatorio.EmailDoCliente
                        && registroDoRelatorio.CPFDoCliente != null && p.CPFDoCliente == registroDoRelatorio.CPFDoCliente);

                    if (saldoEmOutrosEstabelecimentosPorEmailECPF != null)
                        registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos += saldoEmOutrosEstabelecimentosPorEmailECPF.SaldoDePontosAtualizado;
                    if (saldoEmOutrosEstabelecimentosPorEmailENaoCPF != null)
                        registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos += saldoEmOutrosEstabelecimentosPorEmailENaoCPF.SaldoDePontosAtualizado;
                    if (saldoEmOutrosEstabelecimentosPorCPFENaoEmail != null)
                        registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos += saldoEmOutrosEstabelecimentosPorCPFENaoEmail.SaldoDePontosAtualizado;
                }

                registroDoRelatorio.SaldosDePontosDoCliente.TotalDePontosNaRede = registroDoRelatorio.SaldosDePontosDoCliente.PontosNesteEstabelecimento + registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos;
            }
        }

        private List<UnidadeQueTevePontoTransferidoDTO> ListarDadosDasUnidadesQueTiveramPontosTransferidos(FiltroRelatorioMovimentacaoDePontos filtroRelatorio, Pessoas.Estabelecimento estabelecimento)
        {
            var unidadesComPontoTransferido = new List<UnidadeQueTevePontoTransferidoDTO>();

            if (filtroRelatorio.TipoDeConsulta == TipoConsultaMovimentacaoPontosEnum.Transferencias)
            {
                var movimentacaoTransferencia = Domain.Fidelidade.MovimentacaoDeTransferenciaDePontosRepository.Filtrar(filtroRelatorio);
                var transferenciaQueryable = Domain.Fidelidade.TransferenciaDePontosRepository.Queryable();
                var estabelecimentoQueryable = Domain.Pessoas.EstabelecimentoRepository.Queryable();

                var estabelecimentosQueForneceram = (from movim in movimentacaoTransferencia
                                                     join trans in transferenciaQueryable on movim.TransferenciaDePontos equals trans
                                                     join estab in estabelecimentoQueryable on trans.IdPessoaDoEstabelecimentoQueForneceuOsPontos equals estab.PessoaJuridica.IdPessoa
                                                     where trans.Tipo == TipoDaTransferenciaDePontosEnum.Entrada
                                                        && trans.IdPessoaDoEstabelecimentoQueRecebeuOsPontos == estabelecimento.PessoaJuridica.IdPessoa
                                                     select new UnidadeQueTevePontoTransferidoDTO
                                                     {
                                                         IdMovimentacao = movim.Id,
                                                         NomeUnidade = estab.FranquiaEstabelecimento.NomeUnidade
                                                     }).ToList();

                unidadesComPontoTransferido.AddRange(estabelecimentosQueForneceram);

                var estabelecimentosQueReceberam = (from movim in movimentacaoTransferencia
                                                    join trans in transferenciaQueryable on movim.TransferenciaDePontos equals trans
                                                    join estab in estabelecimentoQueryable on trans.IdPessoaDoEstabelecimentoQueRecebeuOsPontos equals estab.PessoaJuridica.IdPessoa
                                                    where trans.Tipo == TipoDaTransferenciaDePontosEnum.Saida
                                                       && trans.IdPessoaDoEstabelecimentoQueForneceuOsPontos == estabelecimento.PessoaJuridica.IdPessoa
                                                    select new UnidadeQueTevePontoTransferidoDTO
                                                    {
                                                        IdMovimentacao = movim.Id,
                                                        NomeUnidade = estab.FranquiaEstabelecimento.NomeUnidade
                                                    }).ToList();

                unidadesComPontoTransferido.AddRange(estabelecimentosQueReceberam);
            }

            return unidadesComPontoTransferido;
        }

        public List<Transacao> ObterTransacoesQueImpedemEstornoDeGanho(PontoGanho pontoGanhoQueAMovimentacaoGerou)
        {
            var transacoes = Domain.Financeiro.TransacaoRepository.Queryable();
            var idsTransacoes = pontoGanhoQueAMovimentacaoGerou.MovimentacoesAssociadas
                        .Where(m => m.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.UsoDePontos
                                    && m.Movimentacao.FoiEstornado == false)
                         .Select(p => p.IdTransacao).Distinct().ToList();
            return transacoes.Where(t => idsTransacoes.Contains(t.Id)).ToList();
        }

        private IQueryable<MovimentacaoDePontos> Filtrar(FiltroRelatorioMovimentacaoDePontos filtroRelatorio)
        {
            var query = Queryable()
                    .Where(p => p.IdPessoaDoEstabelecimento == filtroRelatorio.IdPessoaDoEstabelecimento
                             && p.DataMovimentacao >= filtroRelatorio.DataInicio
                             && p.DataMovimentacao <= filtroRelatorio.DataFim);

            if (filtroRelatorio.TipoDeConsulta == TipoConsultaMovimentacaoPontosEnum.PontosGanhos)
            {
                query = query.Where(p => p.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos
                                     && (p.PontoGanhoQueAMovimentacaoGerou.Status == StatusMovimentacaoPontosEnum.Disponivel
                                          || p.PontoGanhoQueAMovimentacaoGerou.Status == StatusMovimentacaoPontosEnum.Expirado));

                if (filtroRelatorio.NumeroDiasAExpirar > 0)
                    query = query.Where(p => p.PontoGanhoQueAMovimentacaoGerou.DataValidade <= Calendario.Hoje().AddDays(filtroRelatorio.NumeroDiasAExpirar));
            }
            else if (filtroRelatorio.TipoDeConsulta == TipoConsultaMovimentacaoPontosEnum.PontosUsados)
            {
                query = query.Where(p => p.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.UsoDePontos && !p.FoiEstornado);
            }
            else if (filtroRelatorio.TipoDeConsulta == TipoConsultaMovimentacaoPontosEnum.Transferencias)
            {
                query = query.Where(p => p.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.Transferencia && !p.FoiEstornado);
            }

            if (filtroRelatorio.ConsultarPorCliente)
            {
                if (filtroRelatorio.TipoConsultaCliente == TipoConsultaClienteEnum.CPF)
                    query = query.Where(p => p.PessoaFisicaDoCliente.Cpf.Contains(filtroRelatorio.TextoFiltroCliente.RemoverFormatacaoCPFeCPNJ()));
                else if (filtroRelatorio.TipoConsultaCliente == TipoConsultaClienteEnum.Nome)
                    query = query.Where(p => p.PessoaFisicaDoCliente.NomeCompleto.Contains(filtroRelatorio.TextoFiltroCliente));
                else if (filtroRelatorio.TipoConsultaCliente == TipoConsultaClienteEnum.Email)
                    query = query.Where(p => p.PessoaFisicaDoCliente.Email == filtroRelatorio.TextoFiltroCliente.ToLower());
                else
                    query = query.Where(p => p.PessoaFisicaDoCliente.Telefones.Any(t => (t.Ddi + t.DDD + t.Numero).Contains(filtroRelatorio.TextoFiltroCliente)));
            }

            return query;
        }
        public DateTime? ObterUltimaDataPontoResgatado(int idPessoa)
        {
            return Queryable().Where(p => p.IdPessoaDoEstabelecimento == idPessoa &&
                               !p.FoiEstornado &&
                               p.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.UsoDePontos)
                               .Max(p => (DateTime?)p.HoraMovimentacao);
        }

        public DateTime? ObterUltimaDataPontoGanho(int idPessoa)
        {
            return Queryable().Where(p => p.IdPessoaDoEstabelecimento == idPessoa &&
                              !p.FoiEstornado &&
                              p.TipoDeOperacaoDeMovimentacao == OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos &&
                              p.TipoOrigem != TipoOrigemMovimentacaoDePontosEnum.PontoAvulso)
                              .Max(p => (DateTime?)p.HoraMovimentacao);
        }
    }
}