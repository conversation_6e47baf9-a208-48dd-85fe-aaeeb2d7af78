﻿using Castle.ActiveRecord;
using NHibernate.Transform;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Fidelidade.DTO;
using Perlink.Trinks.Fidelidade.Enums;
using Perlink.Trinks.Fidelidade.Filtros;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Fidelidade.Repositories
{

    public partial class PontoGanhoRepository : IPontoGanhoRepository
    {

        List<PontoGanho> IPontoGanhoRepository.ObterPontosASeremUsados(int quantidadeDePontosNecessarios, int idPessoaDoEstabelecimento, int idPessoaCliente)
        {
            return Queryable()
                .Where(pg => pg.Status == Enums.StatusMovimentacaoPontosEnum.Disponivel
                    && pg.IdPessoaDoEstabelecimento == idPessoaDoEstabelecimento
                    && pg.PessoaFisicaDoCliente.IdPessoa == idPessoaCliente)
                .OrderBy(pg => pg.DataValidade).Take(quantidadeDePontosNecessarios + 1).ToList();//+1 por causa da solução temporária para estornos que geraram negativos TRINKS-10094
        }

        public int ObterSomaDosPontosASeremExpirados(int idPessoaCliente, int idPessoaJuridicaDoEstabelecimento, DateTime dataLimite)
        {
            var query = Queryable().Where(p => p.Status == Enums.StatusMovimentacaoPontosEnum.Disponivel
                                       && p.DataValidade <= dataLimite
                                       && p.PessoaFisicaDoCliente.IdPessoa == idPessoaCliente
                                       && p.IdPessoaDoEstabelecimento == idPessoaJuridicaDoEstabelecimento);

            return query.Sum(p => (int?)p.QuantidadeDePontosDisponiveis) ?? 0;
        }

        public int ObterSaldoDePontosDisponiveisDoCliente(ClienteEstabelecimento clienteEstabelecimento)
        {
            var query = Queryable().Where(p => p.Status == Enums.StatusMovimentacaoPontosEnum.Disponivel
                                       && p.PessoaFisicaDoCliente.IdPessoa == clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa
                                       && p.IdPessoaDoEstabelecimento == clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa);

            return query.Sum(p => (int?)p.QuantidadeDePontosDisponiveis) ?? 0;
        }

        public IQueryable<PontoGanho> ObterPontosExpiradosQueAindaEstamComoDisponiveis(DateTime hoje)
        {
            var query = Queryable().Where(p => p.Status == Enums.StatusMovimentacaoPontosEnum.Disponivel
                                       && p.DataValidade < hoje);

            return query;
        }

        public List<PontosAExpirarDTO> ListarPontosAExpirarNosProximosDias(int idPessoaCliente, int idPessoaJuridicaDoEstabelecimento, DateTime dataLimite)
        {
            var query = Queryable().Where(p => p.Status == Enums.StatusMovimentacaoPontosEnum.Disponivel
                                       && p.DataValidade <= dataLimite
                                       && p.PessoaFisicaDoCliente.IdPessoa == idPessoaCliente
                                       && p.IdPessoaDoEstabelecimento == idPessoaJuridicaDoEstabelecimento);

            return query.GroupBy(p => p.DataValidade).Select(p => new PontosAExpirarDTO() { DataAExpirar = p.Key.Value, TotalDePontos = p.Sum(s => s.QuantidadeDePontosDisponiveis) }).ToList();
        }

        public List<PontoGanho> ObterPontosGanhosEnvolvidosComAMovimentacao(MovimentacaoDePontos movimentacaoAEstornar)
        {
            return Domain.Fidelidade.MovimentacaoDeUmPontoGanhoRepository.Queryable()
                .Where(mp => mp.Movimentacao.Id == movimentacaoAEstornar.Id).Select(mp => mp.PontoGanhoQueAMovimentacaoAlterou).ToList();
        }

        public PontoGanho ObterPontoGanhoQueAMovimentacaoGerou(int idMovimentacaoDePontos)
        {
            var ponto = Queryable().FirstOrDefault(p => p.MovimentacaoQueGerouPontos.Id == idMovimentacaoDePontos);
            return ponto;
        }

        private IQueryable<PontoGanho> ObterQueryableDePontosGanhosDisponiveisPorDataDeValidade(DateTime dataDeValidadeEspecificaOuInicial, int idPessoaDoEstabelecimento, int? idPessoaDoCliente = null, DateTime? dataDeValidadeFinal = null)
        {
            var query = Queryable()
                .Where(p => p.Status == Enums.StatusMovimentacaoPontosEnum.Disponivel
                            && p.IdPessoaDoEstabelecimento == idPessoaDoEstabelecimento
                            && p.QuantidadeDePontosDisponiveis > 0);

            if (dataDeValidadeFinal.HasValue)
            {
                // Se a data final foi informada, faz uma pesquisa por range da data de validade.
                query = query.Where(p => p.DataValidade >= dataDeValidadeEspecificaOuInicial.Date && p.DataValidade <= dataDeValidadeFinal.Value.Date);
            }
            else
            {
                // Se a data final não foi informada, pesquisa somente pelos registros com data de validade igual a data inicial
                query = query.Where(p => p.DataValidade == dataDeValidadeEspecificaOuInicial.Date);
            }

            if (idPessoaDoCliente.HasValue)
            {
                // Se o id do cliente foi informado, filtra pelos registros desse cliente
                query = query.Where(p => p.PessoaFisicaDoCliente.IdPessoa == idPessoaDoCliente.Value);
            }

            return query;
        }

        public List<PontosAExpirarDTO> ObterQuantidadeDePontosAExpirarDoClienteNoRangeDeDatas(DateTime dataValidadeInicial, DateTime dataValidadeFinal, int idPessoaDoEstabelecimento, int idPessoaDoCliente)
        {
            var query = ObterQueryableDePontosGanhosDisponiveisPorDataDeValidade(
                dataDeValidadeEspecificaOuInicial: dataValidadeInicial,
                idPessoaDoEstabelecimento: idPessoaDoEstabelecimento,
                idPessoaDoCliente: idPessoaDoCliente,
                dataDeValidadeFinal: dataValidadeFinal);

            var resultado = (from p in query.Where(x => x.DataValidade.HasValue)
                             group p by p.DataValidade into g
                             select new PontosAExpirarDTO()
                             {
                                 DataAExpirar = g.Key.Value,
                                 TotalDePontos = g.Sum(x => x.QuantidadeDePontosDisponiveis)
                             }).ToList();

            return resultado;
        }

        List<PontosExpiradosNoEstabelecimentoDTO> IPontoGanhoRepository.ObterQuatidadeDePontosExpiradosNoDiaPorCliente(DateTime data, int idPessoaDoEstabelecimento)
        {
            var query = ObterQueryableDePontosGanhosDisponiveisPorDataDeValidade(
                dataDeValidadeEspecificaOuInicial: data,
                idPessoaDoEstabelecimento: idPessoaDoEstabelecimento,
                idPessoaDoCliente: null,
                dataDeValidadeFinal: null);

            var queryableConta = Domain.Pessoas.ContaRepository.Queryable();
            var join = (from pontoGanho in query
                        join conta in queryableConta on pontoGanho.PessoaFisicaDoCliente.IdPessoa equals conta.Pessoa.IdPessoa
                        select new
                        {
                            IdPessoaDoCliente = pontoGanho.PessoaFisicaDoCliente.IdPessoa,
                            Email = pontoGanho.PessoaFisicaDoCliente.Email,
                            NomeCompleto = pontoGanho.PessoaFisicaDoCliente.NomeCompleto,
                            Apelido = pontoGanho.PessoaFisicaDoCliente.Apelido,
                            IdConta = conta.IdConta,
                            QuantidadeDePontosDisponiveis = pontoGanho.QuantidadeDePontosDisponiveis
                        }).ToList();

            var retorno = join.GroupBy(p => new
            {
                IdPessoaDoCliente = p.IdPessoaDoCliente,
                p.Email,
                p.NomeCompleto,
                p.Apelido,
                p.IdConta
            })
            .Select(p => new PontosExpiradosNoEstabelecimentoDTO()
            {
                EmailDoCliente = p.Key.Email,
                NomeDoCliente = p.Key.Apelido ?? p.Key.NomeCompleto,
                TotalDePontos = p.Sum(s => s.QuantidadeDePontosDisponiveis),
                IdPessoaDoCliente = p.Key.IdPessoaDoCliente,
                IdContaDoCliente = p.Key.IdConta
            }).ToList();

            var dadosClientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                .Where(x => x.Ativo == true && x.RecebeEmailProgramaFidelidade == true
                                    && query.Any(y => y.PessoaFisicaDoCliente.IdPessoa == x.Cliente.PessoaFisica.IdPessoa)
                                    && x.Estabelecimento.PessoaJuridica.IdPessoa == idPessoaDoEstabelecimento).Select(ce => new
                                    {
                                        ce.Cliente.PessoaFisica.IdPessoa,
                                        ce.SaldoDePontosDeFidelidade
                                    }).ToList();

            retorno = retorno.Where(x => dadosClientesEstabelecimento.Select(f => f.IdPessoa).Contains(x.IdPessoaDoCliente)).ToList();

            foreach (var item in dadosClientesEstabelecimento)
            {
                var dado = retorno.FirstOrDefault(f => f.IdPessoaDoCliente == item.IdPessoa);
                if (dado == null) continue;
                dado.SaldoDePontosDeFidelidade = item.SaldoDePontosDeFidelidade;
            }

            return retorno;
        }

        public List<RelatorioMovimentacaoDePontosDTO> ObterRelatorioDePontosASeremUsados(FiltroRelatorioMovimentacaoDePontos filtroRelatorio, out int totalGeralDePontosDeFidelidade, bool ehPaginado = false)
        {
            totalGeralDePontosDeFidelidade = 0;
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(filtroRelatorio.IdPessoaDoEstabelecimento);
            bool permiteTransferenciaDePontos = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();
            var retorno = new List<RelatorioMovimentacaoDePontosDTO>();

            var filtrados = Filtrar(filtroRelatorio);

            var busca = (from p in filtrados
                         group p by new { p.DataValidade, p.PessoaFisicaDoCliente.IdPessoa, p.PessoaFisicaDoCliente.Email, p.PessoaFisicaDoCliente.Cpf, p.PessoaFisicaDoCliente.NomeCompleto } into i
                         select new RelatorioMovimentacaoDePontosDTO
                         {
                             DataExpiracao = i.Key.DataValidade,
                             NomeCliente = i.Key.NomeCompleto,
                             IdPessoaDoCliente = i.Key.IdPessoa,
                             EmailDoCliente = i.Key.Email,
                             CPFDoCliente = i.Key.Cpf,
                             PontosMovimentados = i.Sum(m => m.QuantidadeDePontosDisponiveis)
                         });

            totalGeralDePontosDeFidelidade = Math.Abs(busca.ToList().Sum(x => (int?)x.PontosMovimentados) ?? 0); //total de pontos do programa de fidelidade conforme consulta

            bool existemRegistros = busca.Any();
            if (ehPaginado && existemRegistros)
            {
                retorno = busca.ToResultadoPaginadoQueryable(filtroRelatorio.Paginacao).ToList();
                filtroRelatorio.Paginacao.TotalItens = busca.Select(f => f.IdPessoaDoCliente).ToList().Count();
            }
            else
            {
                retorno = busca.ToList();
            }

            if (filtroRelatorio.CarregarSaldoDosClientes && existemRegistros)
            {
                AplicarSaldoDePontosDosClientesNosRegistrosFiltrados(filtroRelatorio, estabelecimento, permiteTransferenciaDePontos, retorno, filtrados, ehPaginado);
            }

            return retorno;
        }

        internal void AplicarSaldoDePontosDosClientesNosRegistrosFiltrados(FiltroRelatorioMovimentacaoDePontos filtroRelatorio, Estabelecimento estabelecimento, bool permiteTransferenciaDePontos, List<RelatorioMovimentacaoDePontosDTO> retorno, IQueryable<PontoGanho> filtrados, bool ehPaginado = false)
        {
            var saldosDosPontosNesteEstabelecimento = new List<SaldoDePontosDaPessoaClienteDTO>();
            var saldosDosPontosEmOutrosEstabelecimentos = new List<SaldoDePontosDaPessoaClienteDTO>();
            if (!ehPaginado)
            {
                saldosDosPontosNesteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                        .Where(p => p.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento
                                                    && p.Ativo
                                                    && filtrados.Any(f => f.PessoaFisicaDoCliente.IdPessoa == p.Cliente.PessoaFisica.IdPessoa))
                                        .Select(p => new DTO.SaldoDePontosDaPessoaClienteDTO() { IdPessoaDoCliente = p.Cliente.PessoaFisica.IdPessoa, SaldoDePontosAtualizado = p.SaldoDePontosDeFidelidade }).ToList();

                if (permiteTransferenciaDePontos)
                {
                    saldosDosPontosEmOutrosEstabelecimentos = (from p in Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                                               where p.Estabelecimento.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                                   && p.Ativo
                                                                   && p.Estabelecimento.IdEstabelecimento != estabelecimento.IdEstabelecimento
                                                                   && filtrados.Any(f => f.PessoaFisicaDoCliente.Cpf == p.Cliente.PessoaFisica.Cpf
                                                                                          && f.PessoaFisicaDoCliente.Cpf != null && f.PessoaFisicaDoCliente.Cpf != "")
                                                               group p by new
                                                               {
                                                                   CPFDoCliente = p.Cliente.PessoaFisica.Cpf,
                                                                   EmailDoCliente = p.Cliente.PessoaFisica.Email
                                                               } into dataGroup
                                                               select new SaldoDePontosDaPessoaClienteDTO
                                                               {
                                                                   CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                                   EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                                   SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.SaldoDePontosDeFidelidade)
                                                               }).ToList();

                    saldosDosPontosEmOutrosEstabelecimentos.AddRange(
                                                    (from p in Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                                                     where p.Estabelecimento.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                             && p.Ativo
                                                             && p.Estabelecimento.IdEstabelecimento != estabelecimento.IdEstabelecimento
                                                             && filtrados.Any(f => f.PessoaFisicaDoCliente.Email == p.Cliente.PessoaFisica.Email
                                                                                  && f.PessoaFisicaDoCliente.Email != null && f.PessoaFisicaDoCliente.Email != "")
                                                     group p by new
                                                     {
                                                         CPFDoCliente = p.Cliente.PessoaFisica.Cpf,
                                                         EmailDoCliente = p.Cliente.PessoaFisica.Email
                                                     } into dataGroup
                                                     select new SaldoDePontosDaPessoaClienteDTO
                                                     {
                                                         CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                         EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                         SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.SaldoDePontosDeFidelidade)
                                                     }).ToList());
                }
            }
            else if (ehPaginado)
            {
                var idsPessoaDosClientes = retorno.Select(r => r.IdPessoaDoCliente).ToList();
                var cpfsDosClientes = retorno.Select(r => r.CPFDoCliente).Where(cpf => !string.IsNullOrWhiteSpace(cpf)).ToList();
                var emailsDosClientes = retorno.Select(r => r.EmailDoCliente).Where(email => !string.IsNullOrWhiteSpace(email)).ToList();

                saldosDosPontosNesteEstabelecimento = Queryable()
                                        .Where(p => p.IdPessoaDoEstabelecimento == estabelecimento.PessoaJuridica.IdPessoa
                                                    && p.Status == StatusMovimentacaoPontosEnum.Disponivel
                                                    && p.IdPessoaDoEstabelecimento == estabelecimento.PessoaJuridica.IdPessoa
                                                    && idsPessoaDosClientes.Contains(p.PessoaFisicaDoCliente.IdPessoa))
                                        .GroupBy(g => g.PessoaFisicaDoCliente.IdPessoa)
                                        .Select(pg => new DTO.SaldoDePontosDaPessoaClienteDTO() { IdPessoaDoCliente = pg.Key, SaldoDePontosAtualizado = pg.Sum(pga => pga.QuantidadeDePontosDisponiveis) }).ToList();

                if (permiteTransferenciaDePontos)
                {
                    var buscaEstabelecimentos = Domain.Pessoas.EstabelecimentoRepository.Queryable();
                    saldosDosPontosEmOutrosEstabelecimentos = (from p in Queryable()
                                                               join e in buscaEstabelecimentos on p.IdPessoaDoEstabelecimento equals e.PessoaJuridica.IdPessoa
                                                               where e.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                                   && p.Status == StatusMovimentacaoPontosEnum.Disponivel
                                                                   && p.IdPessoaDoEstabelecimento != estabelecimento.PessoaJuridica.IdPessoa
                                                                   && emailsDosClientes.Contains(p.PessoaFisicaDoCliente.Email)
                                                               group p by new
                                                               {
                                                                   CPFDoCliente = p.PessoaFisicaDoCliente.Cpf,
                                                                   EmailDoCliente = p.PessoaFisicaDoCliente.Email
                                                               } into dataGroup
                                                               select new SaldoDePontosDaPessoaClienteDTO
                                                               {
                                                                   CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                                   EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                                   SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.QuantidadeDePontosDisponiveis)
                                                               }).ToList();

                    saldosDosPontosEmOutrosEstabelecimentos.AddRange(
                                                    (from p in Queryable()
                                                     join e in buscaEstabelecimentos on p.IdPessoaDoEstabelecimento equals e.PessoaJuridica.IdPessoa
                                                     where e.FranquiaEstabelecimento.Franquia == estabelecimento.FranquiaEstabelecimento.Franquia
                                                         && p.Status == StatusMovimentacaoPontosEnum.Disponivel
                                                         && p.IdPessoaDoEstabelecimento != estabelecimento.PessoaJuridica.IdPessoa
                                                         && cpfsDosClientes.Contains(p.PessoaFisicaDoCliente.Cpf)
                                                     group p by new
                                                     {
                                                         CPFDoCliente = p.PessoaFisicaDoCliente.Cpf,
                                                         EmailDoCliente = p.PessoaFisicaDoCliente.Email
                                                     } into dataGroup
                                                     select new SaldoDePontosDaPessoaClienteDTO
                                                     {
                                                         CPFDoCliente = dataGroup.Key.CPFDoCliente,
                                                         EmailDoCliente = dataGroup.Key.EmailDoCliente,
                                                         SaldoDePontosAtualizado = dataGroup.Sum(pga => pga.QuantidadeDePontosDisponiveis)
                                                     }).ToList());
                }
            }

            foreach (var registroDoRelatorio in retorno)
            {
                registroDoRelatorio.SaldosDePontosDoCliente.RedePermiteTransferencias = permiteTransferenciaDePontos;
                var saldoNesteEstabelecimento = saldosDosPontosNesteEstabelecimento.FirstOrDefault(p => p.IdPessoaDoCliente == registroDoRelatorio.IdPessoaDoCliente);
                if (saldoNesteEstabelecimento != null)
                    registroDoRelatorio.SaldosDePontosDoCliente.PontosNesteEstabelecimento = saldoNesteEstabelecimento.SaldoDePontosAtualizado;

                if (permiteTransferenciaDePontos)
                {
                    var saldoEmOutrosEstabelecimentosPorEmailECPF = saldosDosPontosEmOutrosEstabelecimentos
                        .FirstOrDefault(p => registroDoRelatorio.EmailDoCliente != null && registroDoRelatorio.CPFDoCliente != null
                        && p.EmailDoCliente == registroDoRelatorio.EmailDoCliente && p.CPFDoCliente == registroDoRelatorio.CPFDoCliente);

                    var saldoEmOutrosEstabelecimentosPorEmailENaoCPF = saldosDosPontosEmOutrosEstabelecimentos
                        .FirstOrDefault(p => registroDoRelatorio.EmailDoCliente != null && p.EmailDoCliente == registroDoRelatorio.EmailDoCliente
                        && p.CPFDoCliente != registroDoRelatorio.CPFDoCliente);

                    var saldoEmOutrosEstabelecimentosPorCPFENaoEmail = saldosDosPontosEmOutrosEstabelecimentos
                        .FirstOrDefault(p => p.EmailDoCliente != registroDoRelatorio.EmailDoCliente
                        && registroDoRelatorio.CPFDoCliente != null && p.CPFDoCliente == registroDoRelatorio.CPFDoCliente);

                    if (saldoEmOutrosEstabelecimentosPorEmailECPF != null)
                        registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos += saldoEmOutrosEstabelecimentosPorEmailECPF.SaldoDePontosAtualizado;
                    if (saldoEmOutrosEstabelecimentosPorEmailENaoCPF != null)
                        registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos += saldoEmOutrosEstabelecimentosPorEmailENaoCPF.SaldoDePontosAtualizado;
                    if (saldoEmOutrosEstabelecimentosPorCPFENaoEmail != null)
                        registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos += saldoEmOutrosEstabelecimentosPorCPFENaoEmail.SaldoDePontosAtualizado;
                }

                registroDoRelatorio.SaldosDePontosDoCliente.TotalDePontosNaRede = registroDoRelatorio.SaldosDePontosDoCliente.PontosNesteEstabelecimento + registroDoRelatorio.SaldosDePontosDoCliente.PontosEmOutrosEstabelecimentos;
            }
        }

        private IQueryable<PontoGanho> Filtrar(FiltroRelatorioMovimentacaoDePontos filtroRelatorio)
        {
            var query = Queryable()
                .Where(p => p.IdPessoaDoEstabelecimento == filtroRelatorio.IdPessoaDoEstabelecimento
                         && p.DataMovimentacao >= filtroRelatorio.DataInicio
                         && p.DataMovimentacao <= filtroRelatorio.DataFim
                         && p.Status == StatusMovimentacaoPontosEnum.Disponivel);

            if (filtroRelatorio.ConsultarPorCliente)
            {
                if (filtroRelatorio.TipoConsultaCliente == TipoConsultaClienteEnum.CPF)
                    query = query.Where(p => p.PessoaFisicaDoCliente.Cpf.Contains(filtroRelatorio.TextoFiltroCliente.RemoverFormatacaoCPFeCPNJ()));
                else if (filtroRelatorio.TipoConsultaCliente == TipoConsultaClienteEnum.Nome)
                    query = query.Where(p => p.PessoaFisicaDoCliente.NomeCompleto.Contains(filtroRelatorio.TextoFiltroCliente));
                else if (filtroRelatorio.TipoConsultaCliente == TipoConsultaClienteEnum.Email)
                    query = query.Where(p => p.PessoaFisicaDoCliente.Email == filtroRelatorio.TextoFiltroCliente.ToLower());
                else
                    query = query.Where(p => p.PessoaFisicaDoCliente.Telefones.Any(t => (t.Ddi + t.DDD + t.Numero).Contains(filtroRelatorio.TextoFiltroCliente)));
            }
            if (filtroRelatorio.NumeroDiasAExpirar > 0)
                query = query.Where(p => p.DataValidade <= Calendario.Hoje().AddDays(filtroRelatorio.NumeroDiasAExpirar));

            return query;
        }

        public List<ClienteInfosFidelidadeDTO> ListarClientesInfosFidelidade(FiltroRelatorioProgramaFidelidade filtro)
        {

            if (filtro.filtroPeriodo > 360) filtro.filtroPeriodo = 360;
            if (filtro.menorValorResgate == null) return new List<ClienteInfosFidelidadeDTO>();

            var resultadoFiltro = DefinirFiltroDeResgate(filtro);

            var queryString = @"
                    SELECT  EC.id_cliente AS IdCliente,
		                    RC.nome_completo AS NomeCompleto,
		                    RC.telefones AS Telefone,
		                    RC.ultima_transacao AS UltimoAgendamento,
		                    EC.saldo_pontos_fidelidade AS Pontos
                    FROM [dbo].[Estabelecimento_Cliente] as EC WITH (NOLOCK)
                    JOIN (
	                    SELECT 
	                    nome_completo, telefones, id_estabelecimento_cliente, MAX(ultima_transacao) as ultima_transacao
                        FROM [dbo].[FACT_Ranking_Clientes] WITH (NOLOCK)
	                    GROUP BY nome_completo, telefones, id_estabelecimento_cliente
                    ) RC ON RC.id_estabelecimento_cliente = EC.id_estabelecimento_cliente
                    WHERE EC.id_estabelecimento = :idEstabelecimento AND CONVERT(DATE,ultima_transacao) > DATEADD(day, -:filtroPeriodo, CAST(GETDATE() AS date))" + resultadoFiltro +
                    "ORDER BY UltimoAgendamento";

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var sessionPontoGanho = holder.CreateSession(typeof(ActiveRecordBase));

            var iSQLQuery = sessionPontoGanho.CreateSQLQuery(queryString);

            var query = iSQLQuery
                .SetInt32("idEstabelecimento", filtro.idEstabelecimento)
                .SetInt32("filtroPeriodo", filtro.filtroPeriodo)
                .SetResultTransformer(Transformers.AliasToBean(typeof(ClienteInfosFidelidadeDTO)));

            var result = query.List<ClienteInfosFidelidadeDTO>();
            holder.ReleaseSession(sessionPontoGanho);

            filtro.Paginacao.TotalItens = result.Count();

            var resultToReturn = result
                .Skip(filtro.Paginacao.RegistroInicial - 1)
                .Take(filtro.Paginacao.RegistrosPorPagina)
                .ToList();

            var formatador = new FormatadorDeTelefone();

            resultToReturn.ForEach(item =>
            {
                if (item.Telefone != null)
                {
                    item.Telefone = item.Telefone.ToTextoFormatadoListaConcatenada();
                }
            });

            return resultToReturn;


            //var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();
            //var rankingDeClientes = Domain.Relatorios.RankingDeClienteRepository.Queryable();

            //var query =

            //  (from ce in clienteestabelecimento
            //   join rc in rankingdeclientes on ce.cliente.idcliente equals rc.idcliente
            //   group rc by new {
            //       nomecliente = rc.nomecliente,
            //       idcliente = rc.idcliente,
            //       telefone = rc.telefones,
            //       pontos = ce.saldodepontosdefidelidade
            //   } into clientes
            //   select new clienteinfosfidelidadedto() {
            //       idcliente = clientes.key.idcliente,
            //       nomecompleto = clientes.key.nomecliente,
            //       ultimoagendamento = clientes.max(m => m.dataultimavenda),
            //       pontos = clientes.key.pontos,
            //       telefone = clientes.key.telefone
            //   }).tolist();

            //return query;

        }
        private String DefinirFiltroDeResgate(FiltroRelatorioProgramaFidelidade filtro)
        {
            if (filtro.menorValorResgate == null) return "";

            string resgate;
            var decimalArredondado = Decimal.Ceiling((decimal)((decimal)filtro.percentual * filtro.menorValorResgate / 100));

            switch (filtro.filtroResgate)
            {
                case TipoResgateEnum.Próximos_De_Resgatar:
                    resgate = "AND saldo_pontos_fidelidade >= " + decimalArredondado + "AND saldo_pontos_fidelidade < " + filtro.menorValorResgate;
                    break;

                case TipoResgateEnum.Podem_Resgatar:
                    resgate = "AND saldo_pontos_fidelidade >= " + filtro.menorValorResgate;
                    break;

                default:
                    resgate = "AND saldo_pontos_fidelidade >=" + decimalArredondado;
                    break;
            }

            return resgate;
        }
    }
}