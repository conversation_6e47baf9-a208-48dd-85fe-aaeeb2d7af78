﻿using Perlink.Pagamentos.Gateways.Enums;
using System.Collections.Generic;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories
{
    public partial interface IEstabelecimentoRecebedorRepository
    {

        EstabelecimentoRecebedor ObterAtivoPorEstabelecimento(int idEstabelecimento);
        (EstabelecimentoRecebedor, GatewayEnum?) ObterAtivoComGatewayPorEstabelecimento(int idEstabelecimento);
        EstabelecimentoRecebedor ObterAtivoEHabilitadoPorIdEstabelecimento(int idEstabelecimento);
        EstabelecimentoRecebedor ObterAtivoPorIdRecebedor(int idRecebedor);
        EstabelecimentoRecebedor ObterPorIdRecebedor(int idRecebedor);
        IEnumerable<int> ObterIdsRecebedorDoEstabelecimento(int idEstabelecimento);
        bool EstabelecimentoPossuiAlgumCadastroDeRecebedor(int idEstabelecimento);
        bool EstabelecimentoPossuiAlgumCadastroDeRecebedorAtivo(int idEstabelecimento);
        EstabelecimentoRecebedor ObterPorEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway);
        List<(EstabelecimentoRecebedor Recebedor, GatewayEnum Gateway)> ListarComGatewayPorEstabelecimento(int idEstabelecimento);
        List<(EstabelecimentoRecebedor Recebedor, GatewayEnum Gateway)> ListarAtivosComGatewayPorEstabelecimento(int idEstabelecimento);
        List<(EstabelecimentoRecebedor Recebedor, GatewayEnum Gateway)> ListarAtivosEHabilitadosComGatewayPorEstabelecimento(int idEstabelecimento);
        EstabelecimentoRecebedor ObterAtivoPorEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway);
        bool EstabelecimentoPossuiCadastroNoGateway(int idEstabelecimento, GatewayEnum gateway);
        EstabelecimentoRecebedor ObterAtivoEHabilitadoPorIdEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway);
        bool ExisteEstabelecimentoAtivoEHabilitadoNoGateway(int idEstabelecimento, GatewayEnum gateway);
        bool ExisteEstabelecimentoAtivoEHabilitadoNosGateways(int idEstabelecimento, params GatewayEnum[] gateways);
        int ObterIdEstabelecimentoPorIdRecebedor(int idRecebedor);
        int ObterIdRecebedorPorIdEstabelecimento(int idEstabelecimento);
        string ObterNomeEstabelecimentoPorIdRecebedor(int idRecebedor);
        TaxasDoEstabelecimento ObterTaxasEstabelecimentoRecebedorAtivoPorIdEstabelecimento(int idEstabelecimento, MetodoDePagamentoNoGatewayEnum metodoDePagamento);
        bool ExisteEstabelecimentoAtivoENaoHabilidadoNoGateway(int idEstabelecimento, GatewayEnum gateway);
        bool ExisteEstabelecimentoAtivoENaoHabilidadoNosGateways(int idEstabelecimento, params GatewayEnum[] gateways);
        GatewayEnum ObterGatewayDoEstabelecimentoRecebedorAtivo(int idEstabelecimentoRecebedor);
        GatewayEnum ObterGatewayAtivoDoEstabelecimento(int idEstabelecimento);
        List<(GatewayEnum Gateway, bool Habilitado)> ListarGatewaysAtivosDoEstabelecimento(int idEstabelecimento);
        (GatewayEnum? gateway, string idRecebedorNoGateway) ObterGatewayAtivoHabilitadoEIdRecebedorNoGatewayDoEstabelecimento(int idEstabelecimento);
    }
}
