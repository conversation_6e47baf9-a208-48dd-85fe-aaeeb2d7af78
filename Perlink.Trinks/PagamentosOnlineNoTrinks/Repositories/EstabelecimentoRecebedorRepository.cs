﻿using Perlink.Pagamentos.Gateways.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories
{
    public partial class EstabelecimentoRecebedorRepository : IEstabelecimentoRecebedorRepository
    {
        public EstabelecimentoRecebedor ObterAtivoPorEstabelecimento(int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(p => p.IdEstabelecimento == idEstabelecimento && p.Ativo);
        }

        public (EstabelecimentoRecebedor, GatewayEnum?) ObterAtivoComGatewayPorEstabelecimento(int idEstabelecimento)
        {
            var estabelecimentoRecebedor = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            var result = (from er in estabelecimentoRecebedor
                          join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                          where er.IdEstabelecimento == idEstabelecimento && er.Ativo
                          select new
                          {
                              EstabelecimentoRecebedor = er,
                              Gateway = rc.Gateway.IdGateway,
                          })
                    .FirstOrDefault();

            return (result?.EstabelecimentoRecebedor, (GatewayEnum?)result?.Gateway);
        }

        public EstabelecimentoRecebedor ObterAtivoEHabilitadoPorIdEstabelecimento(int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(p => p.IdEstabelecimento == idEstabelecimento && p.Ativo && p.Habilitado);
        }

        public EstabelecimentoRecebedor ObterAtivoPorIdRecebedor(int idRecebedor)
        {
            return Queryable().FirstOrDefault(p => p.IdRecebedor == idRecebedor && p.Ativo);
        }

        public EstabelecimentoRecebedor ObterPorIdRecebedor(int idRecebedor)
        {
            return Queryable().FirstOrDefault(p => p.IdRecebedor == idRecebedor);
        }

        public int ObterIdEstabelecimentoPorIdRecebedor(int idRecebedor)
        {
            return Queryable().Where(p => p.IdRecebedor == idRecebedor && p.Ativo).Select(p => p.IdEstabelecimento).FirstOrDefault();
        }

        public string ObterNomeEstabelecimentoPorIdRecebedor(int idRecebedor)
        {
            var queryEstab = Domain.Pessoas.EstabelecimentoRepository.Queryable();

            return (from er in Queryable()
                    join estab in queryEstab on er.IdEstabelecimento equals estab.IdEstabelecimento
                    where er.IdRecebedor == idRecebedor
                    select estab.NomeDeExibicaoNoPortal)
                .FirstOrDefault();
        }

        public IEnumerable<int> ObterIdsRecebedorDoEstabelecimento(int idEstabelecimento)
        {
            return Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .Queryable()
                .Where(x => x.IdEstabelecimento == idEstabelecimento && x.Ativo)
                .Select(x => x.IdRecebedor)
                .ToList();
        }

        public bool EstabelecimentoPossuiAlgumCadastroDeRecebedor(int idEstabelecimento)
        {
            return Queryable()
                .Any(e => e.IdEstabelecimento.Equals(idEstabelecimento));
        }

        public bool EstabelecimentoPossuiAlgumCadastroDeRecebedorAtivo(int idEstabelecimento)
        {
            return Queryable()
                .Any(e => e.IdEstabelecimento.Equals(idEstabelecimento) && e.Ativo);
        }

        public EstabelecimentoRecebedor ObterPorEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var estabelecimentoRecebedor = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            return (from er in estabelecimentoRecebedor
                    join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento && rc.Gateway.IdGateway == (int)gateway
                    select er)
                    .FirstOrDefault();
        }

        public List<(EstabelecimentoRecebedor Recebedor, GatewayEnum Gateway)> ListarComGatewayPorEstabelecimento(int idEstabelecimento) 
        {
            var query = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            var result = (from er in query
                    join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                    select new
                    {
                        EstabelecimentoRecebedor = er,
                        Gateway = rc.Gateway.IdGateway,
                    })
                    .ToList();

            return result.ConvertAll(x => (x.EstabelecimentoRecebedor, (GatewayEnum)x.Gateway));
        }

        public List<(EstabelecimentoRecebedor Recebedor, GatewayEnum Gateway)> ListarAtivosComGatewayPorEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            var result = (from er in query
                          join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                          where er.IdEstabelecimento == idEstabelecimento && er.Ativo
                          select new
                          {
                              EstabelecimentoRecebedor = er,
                              Gateway = rc.Gateway.IdGateway,
                          })
                    .ToList();

            return result.ConvertAll(x => (x.EstabelecimentoRecebedor, (GatewayEnum)x.Gateway));
        }

        public List<(EstabelecimentoRecebedor Recebedor, GatewayEnum Gateway)> ListarAtivosEHabilitadosComGatewayPorEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            var result = (from er in query
                          join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                          where er.IdEstabelecimento == idEstabelecimento && er.Ativo && rc.Habilitado
                          select new
                          {
                              EstabelecimentoRecebedor = er,
                              Gateway = rc.Gateway.IdGateway,
                          })
                    .ToList();

            return result.ConvertAll(x => (x.EstabelecimentoRecebedor, (GatewayEnum)x.Gateway));
        }

        public EstabelecimentoRecebedor ObterAtivoPorEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var estabelecimentoRecebedor = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            return (from er in estabelecimentoRecebedor
                    join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento && er.Ativo && rc.Gateway.IdGateway == (int)gateway
                    select er)
                    .FirstOrDefault();
        }

        public bool EstabelecimentoPossuiCadastroNoGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var estabelecimentoRecebedor = Queryable();
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();

            return (from er in estabelecimentoRecebedor
                    join rc in recebedorCredenciado on er.IdRecebedor equals rc.Recebedor.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento && rc.Gateway.IdGateway == (int)gateway
                    select er)
                    .Any();
        }

        public EstabelecimentoRecebedor ObterAtivoEHabilitadoPorIdEstabelecimentoEGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            return (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                    && er.Ativo
                    && rc.Gateway.IdGateway == (int)gateway
                    && rc.Habilitado
                    select er)
                    .FirstOrDefault();
        }

        public bool ExisteEstabelecimentoAtivoEHabilitadoNoGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            return (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                    && er.Ativo
                    && rc.Gateway.IdGateway == (int)gateway
                    && rc.Habilitado
                    select er)
                    .Any();
        }
        
        public bool ExisteEstabelecimentoAtivoEHabilitadoNosGateways(int idEstabelecimento, params GatewayEnum[] gateways)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            return (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                          && er.Ativo
                          && gateways.Contains((GatewayEnum)rc.Gateway.IdGateway)
                          && rc.Habilitado
                    select er)
                .Any();
        }

        public int ObterIdRecebedorPorIdEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(p => p.IdEstabelecimento == idEstabelecimento && p.Ativo).Select(p => p.IdRecebedor).FirstOrDefault();
        }

        public TaxasDoEstabelecimento ObterTaxasEstabelecimentoRecebedorAtivoPorIdEstabelecimento(int idEstabelecimento, MetodoDePagamentoNoGatewayEnum metodoDePagamento)
        {
            var estabRec = Queryable();
            var taxasEstab = Domain.PagamentosOnlineNoTrinks.TaxasDoEstabelecimentoRepository.Queryable();

            return (from er in estabRec
                    join tx in taxasEstab on er.Id equals tx.IdEstabelecimentoRecebedor
                    where er.IdEstabelecimento == idEstabelecimento && 
                          er.Ativo &&
                          tx.MetodoDePagamento == metodoDePagamento
                    select tx).FirstOrDefault();
        }

        public bool ExisteEstabelecimentoAtivoENaoHabilidadoNoGateway(int idEstabelecimento, GatewayEnum gateway)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            return (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                    && er.Ativo
                    && rc.Gateway.IdGateway == (int)gateway
                    && !rc.Habilitado
                    select er)
                    .Any();
        }
        
        public bool ExisteEstabelecimentoAtivoENaoHabilidadoNosGateways(int idEstabelecimento, params GatewayEnum[] gateways)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            return (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                          && er.Ativo
                          && gateways.Contains((GatewayEnum)rc.Gateway.IdGateway)
                          && !rc.Habilitado
                    select er)
                .Any();
        }

        public List<(GatewayEnum Gateway, bool Habilitado)> ListarGatewaysAtivosDoEstabelecimento(int idEstabelecimento)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            var gateways = (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento && er.Ativo
                    select new
                    {
                        rc.Gateway.IdGateway,
                        rc.Habilitado,
                    });

            return gateways.ToList().ConvertAll(g => ((GatewayEnum)g.IdGateway, g.Habilitado));
        }

        public GatewayEnum ObterGatewayDoEstabelecimentoRecebedorAtivo(int idEstabelecimentoRecebedor)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();

            var gateway = (from rc in recebedorCredenciado
                           join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                           where er.Id == idEstabelecimentoRecebedor
                           && er.Ativo
                           && rc.Habilitado
                           select rc.Gateway.IdGateway)
                        .FirstOrDefault();

            return (GatewayEnum)gateway;
        }

        public GatewayEnum ObterGatewayAtivoDoEstabelecimento(int idEstabelecimento)
        {
            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedor = Queryable();
            
            var gateway = (from rc in recebedorCredenciado
                    join er in estabelecimentoRecebedor on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                          && er.Ativo
                          && rc.Habilitado
                    select rc.Gateway.IdGateway)
                .FirstOrDefault();

            return (GatewayEnum)gateway;
        }
        
        public (GatewayEnum? gateway, string idRecebedorNoGateway) ObterGatewayAtivoHabilitadoEIdRecebedorNoGatewayDoEstabelecimento(int idEstabelecimento)
        {
            var recebedorCredenciadoQuery = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable();
            var estabelecimentoRecebedorQuery = Queryable();
            
            var recebedorCredenciado = (from rc in recebedorCredenciadoQuery
                    join er in estabelecimentoRecebedorQuery on rc.Recebedor.IdRecebedor equals er.IdRecebedor
                    where er.IdEstabelecimento == idEstabelecimento
                          && er.Ativo
                          && rc.Habilitado
                    select rc)
                .FirstOrDefault();

            return ((GatewayEnum?)recebedorCredenciado?.Gateway.IdGateway, recebedorCredenciado?.IdRecebedorNoGateway);
        }
    }
}
