﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas;
using System.Collections.Generic;
using System.Security.Principal;
using System.Web;
using System.Web.Mvc;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Services
{
    public class FuncionalidadeVendaOnlineService : BaseService, IFuncionalidadeVendaOnlineService
    {
        public List<FuncionalidadeVendaOnlineDTO> ObterFuncionalidadesDeVendaOnline(Estabelecimento estabelecimento)
        {
            var user = HttpContext.Current.User;
            var urlHelper = new UrlHelper(HttpContext.Current.Request.RequestContext);

            return new List<FuncionalidadeVendaOnlineDTO>
            {
                ObterFuncionalidadeDeAntecipacaoDeRecebiveis(estabelecimento, urlHelper),
                ObterFuncionalidadeDeConfiguracoes(estabelecimento, urlHelper),
                ObterFuncionalidadeDeClubeDeAssinatura(user, urlHelper),
                ObterFuncionalidadeDeVendaPacoteOnline(estabelecimento, urlHelper),
                ObterFuncionalidadeDeCreditoDeCliente(user, urlHelper),
                ObterFuncionalidadeDePagamentoAntecipado(estabelecimento, urlHelper),
                ObterFuncionalidadeDePromocaoOnline(estabelecimento, urlHelper),
            };
        }

        private static FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDeAntecipacaoDeRecebiveis(Estabelecimento estabelecimento, UrlHelper urlHelper)
        {
            bool temAcessoFuncionalidadeAntecipacaoRecebiveis = AtivoEHabilitadoNaPagarme(estabelecimento) &&
                                                                Domain.PagamentosOnlineNoTrinks.AntecipacoesAgendadasService.RecebedorTemAcessoAntecipacao(estabelecimento);
            
            return new FuncionalidadeVendaOnlineDTO()
            {
                Exibir = temAcessoFuncionalidadeAntecipacaoRecebiveis,
                Icone = "ds-icon-eye-regular",
                Nome = "Previsão de Recebíveis",
                Tipo = TipoFuncionalidadeVendaOnline.Informacao,
                Url = urlHelper.Action("PrevisaoRecebiveis", "VendaOnline"),
            };
        }

        private static FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDeConfiguracoes(Estabelecimento estabelecimento, UrlHelper urlHelper)
            => new FuncionalidadeVendaOnlineDTO
            {
                Exibir = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.EstabelecimentoPossuiAlgumCadastroDeRecebedor(estabelecimento.IdEstabelecimento),
                Icone = "ds-icon-settings-regular",
                Nome = "Dados da Conta",
                Tipo = TipoFuncionalidadeVendaOnline.Informacao,
                Url = urlHelper.Action("Configuracoes", "VendaOnline"),
            };

        private static FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDeCreditoDeCliente(IPrincipal user, UrlHelper urlHelper)
            => new FuncionalidadeVendaOnlineDTO
            {
                Exibir = user.IsInRole(Permissao.CreditoCliente),
                Icone = "ds-icon-wallet-regular",
                Nome = "Crédito de Cliente",
                Tipo = TipoFuncionalidadeVendaOnline.Produto,
                Url = urlHelper.Action("Index", "CreditoCliente"),
            };

        private static FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDeVendaPacoteOnline(Estabelecimento estabelecimento, UrlHelper urlHelper)
            => new FuncionalidadeVendaOnlineDTO
            {
                Exibir = Domain.Pessoas.RelatoriosDePermissoesDaUnidadeService.PodeConfigurarVendaPacoteHotsite(estabelecimento),
                Icone = "ds-icon-package",
                Nome = "Venda de Pacote Online",
                Tipo = TipoFuncionalidadeVendaOnline.Produto,
                Url = urlHelper.Action("Configuracoes", "Pacotes"),
            };

        private static FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDePagamentoAntecipado(
            Estabelecimento estabelecimento, UrlHelper urlHelper)
        {
            var temAcessoPagamentoAntecipadoHotsite = AtivoEHabilitadoNaPagarme(estabelecimento) &&
                                                           Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                                                                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.PagamentoAntecipadoDeServicosNoHotsite)
                                                                .EstaDisponivel;
                
             return new FuncionalidadeVendaOnlineDTO
             {
                Exibir = temAcessoPagamentoAntecipadoHotsite,
                Icone = "ds-icon-payment-forms-regular",
                Nome = "Pagamento antecipado",
                Tipo = TipoFuncionalidadeVendaOnline.Produto,
                Url = urlHelper.Action("PagamentoAntecipado", "VendaOnline"),
            };
        }

        private FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDeClubeDeAssinatura(IPrincipal user, UrlHelper urlHelper) 
            => new FuncionalidadeVendaOnlineDTO
            {
                Exibir = user.IsInRole(Permissao.ClubeDeAssinaturas_TodasAssinaturas) || user.IsInRole(Permissao.ClubeDeAssinaturas_VendaRenovacoesDeAssinaturas) || user.IsInRole(Permissao.ClubeDeAssinaturas_ConsumoDeAssinaturas),
                Icone = "ds-icon-signature-regular",
                Nome = "Clube de assinatura",
                Tipo = TipoFuncionalidadeVendaOnline.Produto,
                Url = urlHelper.Action("Index", "ClubeDeAssinaturas"),
            };
        
        private static bool AtivoEHabilitadoNaPagarme(Estabelecimento estabelecimento)
        {
            var idEstabelecimento = estabelecimento.IdEstabelecimento;
            bool estabelecimentoAtivoEHabilitadoNaPagarme = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ExisteEstabelecimentoAtivoEHabilitadoNosGateways(idEstabelecimento, GatewayEnum.PagarMe, GatewayEnum.PagarMeV5);
            
            if (!estabelecimentoAtivoEHabilitadoNaPagarme) return false;
            
            StatusContaPagarmeEnum etapaCadastroPagarme = Domain.Pagamentos.RecebedorApplicationService.ObterEtapaAtualCadastro(idEstabelecimento);
            
            return etapaCadastroPagarme == StatusContaPagarmeEnum.ContaAtivada;
        }
        
        private static FuncionalidadeVendaOnlineDTO ObterFuncionalidadeDePromocaoOnline(
            Estabelecimento estabelecimento, UrlHelper urlHelper)
        {
            var temAcessoPromocaoOnline = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                                                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.PromocaoOnline)
                                                .EstaDisponivel;
                
            return new FuncionalidadeVendaOnlineDTO
            {
                Exibir = temAcessoPromocaoOnline,
                Icone = "ds-icon-discount",
                Nome = "Promoção Online",
                Tipo = TipoFuncionalidadeVendaOnline.Produto,
                Url = urlHelper.Action("Index", "PromocaoOnline"),
            };
        }
    }
}
