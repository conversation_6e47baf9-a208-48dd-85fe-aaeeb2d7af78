﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.PagamentosOnlineNoTrinks.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Services
{
    public class RelatorioDeRecebiveisService : BaseService, IRelatorioDeRecebiveisService
    {
        public async Task<ResultadoDadosDosRecebiveisDto> ListarDadosDosRecebiveisAsync(FiltroRelatorioDeRecebiveisDto filtro)
        {
            var dados = await ObterRecebiveisAsync(filtro);
            var resumo = await ObterResumoDeRecebiveis(filtro);

            var resultado = new ResultadoDadosDosRecebiveisDto
            {
                Registros = dados,
                Resumo = resumo,
                Paginacao = filtro.Paginacao
            };

            return resultado;
        }

        public async Task<IEnumerable<ResultadoUnidadeRecebiveisDTO>> ListarDadosUnidadeRecebiveis(FiltroRelatorioUnidadeRecebiveisDTO filtro)
        {
            var idRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterIdRecebedorPorIdEstabelecimento(filtro.IdEstabelecimento);

            var dadosUR = await Domain.Pagamentos.RecebedorApplicationService.ObterUnidadeRecebiveis(idRecebedor, filtro.DataInicio, filtro.DataFim);

            var unidadeRecebiveis = dadosUR.Select(ur => new ResultadoUnidadeRecebiveisDTO
            {
                UnidadeRecebiveis = new UnidadeRecebiveisDTO()
                {
                    MetodoPagamento = ur.MetodoPagamento,
                    MarcaCartao = ur.MarcaCartao,
                    ValorLiquido = ur.ValorLiquido,
                    ValorBruto = ur.ValorBruto,
                    DataPagamento = ur.DataPagamento,
                    Deducao = new UnidadeRecebiveisDeducaoDTO
                    {
                        ValorLiquidacoes = ur.Deducoes.ValorLiquidacoes,
                        ValorChargebackAReceber = ur.Deducoes.ValorChargebackAReceber,
                        ValorChargebackRecebido = ur.Deducoes.ValorChargebackRecebido,
                        ValorEstorno = ur.Deducoes.ValorEstorno,
                        ValorTaxa = ur.Deducoes.ValorTaxa
                    }
                }
            });

            return unidadeRecebiveis;
        }

        public async Task<ResultadoDadosContratoRecebedorDTO> ObterContratosRecebedor(FiltroContratosRecebedorDTO filtro)
        {
            filtro.IdRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterIdRecebedorPorIdEstabelecimento(filtro.IdEstabelecimento);

            var contrato = await Domain.Pagamentos.RecebedorApplicationService.ObterContratos(filtro);

            var Dadoscontrato = new ResultadoDadosContratoRecebedorDTO
            {
                Contrato = contrato.DadosContrato.Select(dc => new ContratoDTO
                {
                    DataDeContratoPrevista = dc.DataDeContratoPrevista,
                    DetentorDeAtivoOriginal = dc.DetentorDeAtivoOriginal,
                    ResumoContrato = dc.ObrigacoesDeContrato.Select(rc => new ResumoContratoDTO
                    {
                        EsquemaDePagamento = rc.EsquemaDePagamento,
                        ValorTotal = rc.ValorTotal,
                        ValorNaoComprometido = rc.ValorNaoComprometido,
                        DataDeContratoPrevista = rc.DataDeContratoPrevista,
                        ChaveContrato = rc.ChaveContrato,
                        DetentorContrato = rc.DetentorContrato,
                        PrioridadeEfeito = rc.PrioridadeEfeito,
                        TipoContrato = rc.TipoContrato,
                        MetodoDivisao = rc.MetodoDivisao,
                        ValorEfeito = rc.ValorEfeito,
                        ValorEfeitoComprometido = rc.ValorEfeitoComprometido
                    })
                }),
                Paginacao = new PaginacaoDTO
                {
                    PaginaAtual = contrato.ResumoPagina.PaginaAtual,
                    RegistrosPorPagina = contrato.ResumoPagina.RegistrosPorPagina,
                    TotalPaginas = contrato.ResumoPagina.TotalPaginas
                }
            };

            return Dadoscontrato;
        }

        private async Task<ResumoRecebiveisDto> ObterResumoDeRecebiveis(FiltroRelatorioDeRecebiveisDto filtro)
        {
            try
            {
                const string statusAguardandoPagamento = "waiting_funds";
                var dados = await FiltrarEAgruparRecebiveisPendentes(filtro);

                var valorTotal = dados.Where(d => d.Status == statusAguardandoPagamento).Sum(d => d.ValorAReceber);

                var resumo = new ResumoRecebiveisDto(valorTotal);

                return resumo;
            }
            catch (Exception ex)
            {
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new Exception($"[PAGARME] {ex.Message}")));
                ValidationHelper.Instance.AdicionarItemValidacao("Erro ao tentar obter resumo de recebíveis.");
                return null;
            }
        }

        private async Task<List<DadosDosRecebiveisDTO>> ObterRecebiveisAsync(FiltroRelatorioDeRecebiveisDto filtro)
        {
            try
            {
                var dados = await FiltrarEAgruparRecebiveisPendentes(filtro);

                filtro.Paginacao.TotalItens = dados.Any() ? dados.Count() : 0;
                dados = AplicarPaginacao(dados, filtro.Paginacao);

                return dados.ToList();
            }
            catch (Exception ex)
            {
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(new Exception($"[PAGARME] {ex.Message}")));
                ValidationHelper.Instance.AdicionarItemValidacao("Erro ao tentar obter recebíveis.");
                return null;
            }
        }

        private async Task<IEnumerable<DadosDosRecebiveisDTO>> FiltrarEAgruparRecebiveisPendentes(FiltroRelatorioDeRecebiveisDto filtro)
        {
            const string statusAguardandoPagamento = "waiting_funds";
            var idRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterIdRecebedorPorIdEstabelecimento(filtro.IdEstabelecimento);
            var recebiveis = await Domain.Pagamentos.RecebiveisApplicationService
            .ObterRecebiveis(idRecebedor, filtro.DataInicio, filtro.DataFim, statusAguardandoPagamento);

            var idsTransacoesNoGateway = recebiveis.Select(r => r.IdTransacaoNoGateway).ToList();
            var dadosDosRecebiveis = Domain.Pagamentos.PagamentoRepository.ListarDadosDosRecebiveisDtos(idsTransacoesNoGateway);

            dadosDosRecebiveis = Filtrar(dadosDosRecebiveis, filtro);

            var dadosAgrupados = (from dado in dadosDosRecebiveis.ToList()
                                  join recebivel in recebiveis on dado.IdTransacaoGateway equals recebivel.IdTransacaoNoGateway
                                  select new { dado, recebivel })
                                  .ToList();

            var dados = dadosAgrupados
                .Select(group => new DadosDosRecebiveisDTO(group.recebivel, group.dado))
                .OrderBy(d => d.DataRecebimento);

            return dados;
        }

        private IQueryable<DadosDosRecebiveisDTO> Filtrar(IQueryable<DadosDosRecebiveisDTO> query, FiltroRelatorioDeRecebiveisDto filtro)
        {
            if (!string.IsNullOrWhiteSpace(filtro.Cliente))
                query = query.Where(q => q.NomeCliente.ToLower().Contains(filtro.Cliente.ToLower()));

            if (!string.IsNullOrWhiteSpace(filtro.Item))
                query = query.Where(q => q.NomeItem.ToLower().Contains(filtro.Item.ToLower()));

            if (filtro.TipoItem != 0)
                query = FiltrarPeloTipoItem(query, filtro);

            return query;
        }

        private IQueryable<DadosDosRecebiveisDTO> FiltrarPeloTipoItem(IQueryable<DadosDosRecebiveisDTO> query, FiltroRelatorioDeRecebiveisDto filtro)
        {
            switch (filtro.TipoItem)
            {
                case TipoItemRecebivelEnum.Assinatura:
                    return query.Where(q => q.IdFormaPagamento == (int)FormaPagamentoEnum.ClubeDeAssinaturaPorLink);
                case TipoItemRecebivelEnum.VendaHotsite:
                    return query.Where(q => q.IdFormaPagamento == (int)FormaPagamentoEnum.PagamentoOnlineHotsite || q.IdFormaPagamento == (int)FormaPagamentoEnum.PagarmeCredito);
                case TipoItemRecebivelEnum.CreditoDeCliente:
                    return query.Where(q => q.IdFormaPagamento == (int)FormaPagamentoEnum.PagamentoOnlinePorLink || q.IdFormaPagamento == (int)FormaPagamentoEnum.CreditoCliente);
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        private List<DadosDosRecebiveisDTO> AplicarPaginacao(IEnumerable<DadosDosRecebiveisDTO> lista, Shared.NHibernate.Paginacao.ParametrosPaginacao paginacao)
        {
            return lista.Skip(paginacao.RegistroInicial - 1).Take(paginacao.RegistrosPorPagina).ToList();
        }
    }
}