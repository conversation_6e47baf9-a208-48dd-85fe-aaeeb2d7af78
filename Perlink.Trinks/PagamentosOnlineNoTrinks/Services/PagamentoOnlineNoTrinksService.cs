using Elmah;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways;
using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Auditing;
using Perlink.Shared.Enums;
using Perlink.Shared.Text;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.Pagamentos.Exceptions;
using Perlink.Trinks.Pagamentos.Factories;
using Perlink.Trinks.Pagamentos.Providers;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.PagamentosOnlineNoTrinks.Exceptions;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Services
{
    public class PagamentoOnlineNoTrinksService : BaseService, IPagamentoOnlineNoTrinksService
    {


        #region Configurações do Pagamento Online

        private DisponibilidadeDoRecurso ObterDisponibilidadeDoPagamentoOnline(Estabelecimento estabelecimento)
        {
            return Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.PagamentoOnlineAntecipado);
        }

        public bool EstabelecimentoEUsuarioTemAcessoAFuncionalidadePagamentoOnline(Estabelecimento estabelecimento)
        {
            var usuarioLogadoTemPermissao = UsuarioLogadoTemPermissao();

            if (!usuarioLogadoTemPermissao)
                return false;

            var linkDePagamentoPodeSerConfiguradoNoEstabelecimento = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.LinkDePagamento).EstaDisponivel;

            if (!linkDePagamentoPodeSerConfiguradoNoEstabelecimento)
                return false;

            bool temRecursoRealizarPagamentoOnlineAntecipado = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.PagamentoOnlineAntecipado).EstaDisponivel;

            if (!temRecursoRealizarPagamentoOnlineAntecipado)
                return false;

            var elegivelPagamentoOnline = EstabelecimentoEstaElegivelAoPagamentoOnline(estabelecimento);

            var estabelecimentoTemAdiantamentoDeVisibilidadeDaAbaDePagamentoOnline = EstabelecimentoTemAdiantamentoDeVisibilidadeDaAbaDePagamentoOnline(estabelecimento.IdEstabelecimento);


            return (elegivelPagamentoOnline || estabelecimentoTemAdiantamentoDeVisibilidadeDaAbaDePagamentoOnline);
        }

        private bool UsuarioLogadoTemPermissao()
        {
            if (HttpContext.Current is null)
                return false;

            var user = HttpContext.Current.User;
            
            if (user is null)
                return false;

            var usuarioTemPermissao = user.IsInRole(Permissao.AcessoVendaOnline);
            
            return usuarioTemPermissao;
        }

        public bool EstabelecimentoEstaElegivelAoPagamentoOnline(Estabelecimento estabelecimento)
        {
            bool elegivel = false;
            var disponibilidade = ObterDisponibilidadeDoPagamentoOnline(estabelecimento);

            if (disponibilidade.EstaDisponivel)
            {
                var ehDeFranquiaAtiva = estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Ativo;

                if (ehDeFranquiaAtiva)
                    elegivel = true;
                else
                    elegivel = EstabelecimentoEstaAdimplentePorTempoSuficienteParaTerPagamentoOnline(estabelecimento);
            }

            return elegivel;
        }

        private bool EstabelecimentoEstaAdimplentePorTempoSuficienteParaTerPagamentoOnline(Estabelecimento estabelecimento)
        {
            bool ehAdimplentePorTempoSuficiente = false;

            bool adimplente = Domain.Cobranca.ContaFinanceiraRepository.PessoaJuridicaEstahAdimplente(estabelecimento.PessoaJuridica.IdPessoa);
            if (adimplente)
            {
                ContaFinanceira contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);
                Assinatura assinaturaAtiva = contaFinanceira.AssinaturaAtiva();
                ehAdimplentePorTempoSuficiente = assinaturaAtiva.DataInicio != null && assinaturaAtiva.DataInicio.Value.Date <= Calendario.Hoje().AddMonths(NumeroMinimoDeMesesComAssinaturaPagaParaAtivarPagamentoOnline * -1);
            }

            return ehAdimplentePorTempoSuficiente;
        }

        public bool EstabelecimentoPossuiPagamentoOnlineAtivado(int idEstabelecimento)
        {
            return Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .Queryable()
                .Where(x => x.Estabelecimento.IdEstabelecimento == idEstabelecimento && x.Ativo && x.FormaPagamento == FormaPagamentoEnum.PagamentoOnline)
                .Any();
        }

        public bool EstabelecimentoEstaElegivelEhAtivadoNoPagamentoOnline(Estabelecimento estabelecimento)
        {
            return EstabelecimentoEstaElegivelAoPagamentoOnline(estabelecimento) && EstabelecimentoPossuiPagamentoOnlineAtivado(estabelecimento.IdEstabelecimento);
        }



        public int NumeroMinimoDeMesesComAssinaturaPagaParaAtivarPagamentoOnline
        {
            get
            {
                return new ParametrosTrinks<int>(ParametrosTrinksEnum.pgtoOnline_numero_minimo_meses_assinatura_paga_para_ativar_pagamento_online).ObterValor();
            }
        }



        public TimeSpan HorarioDeEnvioDeNotificacoesDeIncentivoAoPagamentoOnline
        {
            get
            {
                return TimeSpan.Parse(new ParametrosTrinks<string>(ParametrosTrinksEnum.pgtoOnline_horario_envio_notificacoes_incentivo).ObterValor());
            }
        }

        private ParametrosDeRecebimentoNoTrinks ObterTaxasPadraoDoTrinks(int idEstabelecimento, int? numeroParcela = 1)
        {
            var gatewayAtivo = Domain.Pagamentos.PagamentosService.ObterGatewayDoEstabelecimento(idEstabelecimento);

            var numeroParcelaValue = numeroParcela ?? 1;

            return ObterTaxasDoGateway(gatewayAtivo, numeroParcelaValue);
        }

        private ParametrosDeRecebimentoNoTrinks ObterTaxasPadraoDoTrinksPorOrigem(OrigemDeCadastroEnum origem, int? numeroParcela = 1)
        {
            var gatewayAtivo = Domain.Pagamentos.PagamentosService.ObterGatewayPadraoParaCredenciamento(origem);

            var numeroParcelaValue = numeroParcela ?? 1;

            return ObterTaxasDoGateway(gatewayAtivo, numeroParcelaValue);
        }
        
        private ParametrosDeRecebimentoNoTrinks ObterTaxasPadraoDoTrinksPorMetodoDePagamento(int idEstabelecimento, MetodoDePagamentoNoGatewayEnum metodoDepagamento, int? numeroParcela = 1)
        {
            var gatewayAtivo = Domain.Pagamentos.PagamentosService.ObterGatewayDoEstabelecimento(idEstabelecimento);

            var numeroParcelaValue = numeroParcela ?? 1;

            return ObterTaxasDoGatewayPorMetodoDePagamento(gatewayAtivo, metodoDepagamento, numeroParcelaValue);
        }

        public ParametrosDeRecebimentoNoTrinks ObterTaxasDoGateway(GatewayEnum gatewayAtivo, int numeroParcela = 1)
        {
            var taxasPadrao = ConfiguracaoGatewayFactory.ObterConfiguracoesDeAcordoComOGateway(gatewayAtivo);

            return ObterParametrosDeRecebimentoNoTrinksPorTaxas(numeroParcela, taxasPadrao);
        }
        
        public ParametrosDeRecebimentoNoTrinks ObterTaxasDoGatewayPorMetodoDePagamento(GatewayEnum gatewayAtivo, MetodoDePagamentoNoGatewayEnum metodoDepagamento, int numeroParcela = 1)
        {
            var taxasPadrao = ConfiguracaoGatewayFactory.ObterConfiguracoesDeAcordoComOGateway(gatewayAtivo, metodoDepagamento);

            return ObterParametrosDeRecebimentoNoTrinksPorTaxas(numeroParcela, taxasPadrao);
        }

        public TaxasDoGateway ObterTaxasGatewayParaSimulacao(FiltroObterTaxasParaSimulacao filtro)
        {
            var taxasPadraoDoGateway = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterTaxasConfiguradasParaEstabelecimentoPorOrigem(filtro.IdEstabelecimento, filtro.Gateway, filtro.NumeroParcelas);

            if (filtro.Gateway == OrigemDeCadastroEnum.Pagarme || filtro.Gateway == OrigemDeCadastroEnum.PagarMeV5)
            {
                var idTaxaPromocionalPagarme = new ParametrosTrinks<int>(ParametrosTrinksEnum.pgtoOnline_pagarme_id_taxas_padrao).ObterValor();

                if (idTaxaPromocionalPagarme < 1)
                {
                    return new TaxasDoGateway
                    {
                        TaxaFixa = taxasPadraoDoGateway.ObterValorFixoTotalPorTransacao(),
                        TaxaPercentual = taxasPadraoDoGateway.ObterPercentualTotalPorTransacao()
                    };
                }

                var taxaPromocional = Domain.PagamentosOnlineNoTrinks.TaxasPadraoRepository.ObterPorId(idTaxaPromocionalPagarme);

                var taxasAplicadas = new ParametrosDeRecebimentoNoTrinks(
                       percentualTrinks: CompararTaxaASerAplicada(taxaPromocional.Taxas.PercentualTrinks, taxasPadraoDoGateway.Parametros.PercentualTrinks),
                       percentualTrinksPrimeiroPagamento: CompararTaxaASerAplicada(taxaPromocional.Taxas.PercentualTrinksPrimeiroPagamento, taxasPadraoDoGateway.Parametros.PercentualTrinksPrimeiroPagamento),
                       valorFixoTrinks: CompararTaxaASerAplicada(taxaPromocional.Taxas.ValorFixoTrinks, taxasPadraoDoGateway.Parametros.ValorFixoTrinks),
                       valorFixoTrinksPrimeiroPagamento: CompararTaxaASerAplicada(taxaPromocional.Taxas.ValorFixoTrinksPrimeiroPagamento, taxasPadraoDoGateway.Parametros.ValorFixoTrinksPrimeiroPagamento),
                       percentualOperadora: CompararTaxaASerAplicada(taxaPromocional.Taxas.PercentualOperadora, taxasPadraoDoGateway.Parametros.PercentualOperadora),
                       valorFixoOperadora: CompararTaxaASerAplicada(taxaPromocional.Taxas.ValorFixoOperadora, taxasPadraoDoGateway.Parametros.ValorFixoOperadora),
                       valorPorTransferencia: CompararTaxaASerAplicada(taxaPromocional.Taxas.ValorPorTransferencia, taxasPadraoDoGateway.Parametros.ValorPorTransferencia),
                       diasParaReceber: CompararTaxaASerAplicada(taxaPromocional.Taxas.DiasParaReceber, taxasPadraoDoGateway.Parametros.DiasParaReceber)
                );

                return new TaxasDoGateway
                {
                    TaxaFixa = taxasAplicadas.ObterValorFixoTotalPorTransacao(),
                    TaxaPercentual = taxasAplicadas.ObterPercentualTotalPorTransacao()
                };
            }

            return new TaxasDoGateway
            {
                TaxaFixa = taxasPadraoDoGateway.ObterValorFixoTotalPorTransacao(),
                TaxaPercentual = taxasPadraoDoGateway.ObterPercentualTotalPorTransacao()
            };
        }

        public ParametrosDeRecebimentoNoTrinks ObterParametrosDeRecebimentoNoTrinksPorTaxas(int numeroParcela, Pagamentos.Config.IConfiguracoesGateway taxasPadrao)
        {
            var percentualOperadora = taxasPadrao.ObterPercentualOperadoraDeAcordoComParcelas(numeroParcela);

            return new ParametrosDeRecebimentoNoTrinks(
                percentualTrinks: taxasPadrao.PercentualTrinks,
                percentualTrinksPrimeiroPagamento: taxasPadrao.PercentualTrinksPrimeiroPagamento,
                valorFixoTrinks: taxasPadrao.ValorFixoTrinks,
                valorFixoTrinksPrimeiroPagamento: taxasPadrao.ValorFixoTrinksPrimeiroPagamento,
                percentualOperadora: percentualOperadora,
                valorFixoOperadora: taxasPadrao.ValorFixoOperadora,
                valorPorTransferencia: taxasPadrao.ValorPorTransferencia,
                diasParaReceber: taxasPadrao.DiasParaReceber
            );
        }

        public DadosDeRecebedorDTO ObterDadosDeRecebedor(int idEstabelecimento)
        {
            var gatewayPagamentoAntecipado = Domain.Pagamentos.PagamentosService.ObterGatewayPadraoParaPagamentoAntecipado();
            var cadastroPagarMeV3 = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .EstabelecimentoPossuiCadastroNoGateway(idEstabelecimento, GatewayEnum.PagarMe);
            var cadastroPagarMeV5 = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .EstabelecimentoPossuiCadastroNoGateway(idEstabelecimento, GatewayEnum.PagarMeV5);

            return new DadosDeRecebedorDTO()
            {
                PossuiAlgumCadastroDeRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                    .EstabelecimentoPossuiAlgumCadastroDeRecebedor(idEstabelecimento),
                PossuiCadastroNoLinkDePagamento = cadastroPagarMeV3 || cadastroPagarMeV5,
                PossuiCadastroNoPagamentoOnline = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                    .EstabelecimentoPossuiCadastroNoGateway(idEstabelecimento, gatewayPagamentoAntecipado),
                PossuiCadastroNaPagarMeV5 = cadastroPagarMeV5,
                GatewayAtivo = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterGatewayAtivoDoEstabelecimento(idEstabelecimento),
                
            };
        }

        public void DesativarFuncionalidadesDoGatewayInativado(int idEstabelecimento, GatewayEnum gateway)
        {
            switch (gateway)
            {
                case GatewayEnum.PagarMe:
                case GatewayEnum.PagarMeV5:
                    DesabilitarFuncionalidadesAssociadasPagarme(idEstabelecimento);
                    break;
                case GatewayEnum.Zoop:
                    InativarEstabelecimentoFormaDePagamentoOnline(idEstabelecimento);
                    break;
                default:
                    throw new NotImplementedException($"Gateway não implementado: {gateway.ToString()}");
            }
        }

        public void AtualizarFormasDePagamento(int idEstabelecimento, GatewayEnum gateway)
        {
            HabilitarConfiguracoesNoGatewayHabilitado(gateway, idEstabelecimento);
            DesabilitarConfiguracoesNaoAssociadasAoGatewayAtivo(gateway, idEstabelecimento);
        }

        #endregion

        private Conta ObterContaDoUsuarioLogado()
        {
            if (String.IsNullOrEmpty(Domain.WebContext.EmailDoUsuarioAutenticado))
                return Domain.Pessoas.ContaRepository.ObterContaPorIdPessoa(
                    Convert.ToInt32(Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("id_pessoa_conta_trinks_interno").Valor));

            return Domain.Pessoas.ContaRepository.ObterContaPorEmail(Domain.WebContext.EmailDoUsuarioAutenticado);
        }

        public async Task<decimal> ObterSaldoPendenteDoRecebedorDoEstabelecimento(int idEstabelecimento)
        {
            try
            {
                var estabelecimentoRecebedor =
                    Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(
                        idEstabelecimento);
                if (estabelecimentoRecebedor == null)
                    throw new EstabelecimentoNaoConfiguradoComoRecebedorException();

                var gatewayAtivo =
                    Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedor(estabelecimentoRecebedor.IdRecebedor);

                var recebedorCredenciado =
                    Domain.Pagamentos.RecebedorCredenciadoRepository.ObterRecebedorCredenciadoNoGateway(
                        estabelecimentoRecebedor.IdRecebedor, gatewayAtivo);

                var gatewayAntecipacao = GatewayAntecipacaoFactory.Create(gatewayAtivo, new PagamentoOnlineConfigurationProvider());

                var result = await gatewayAntecipacao.ObterSaldoDisponivelDoRecebedorNoGateway(recebedorCredenciado
                    .IdRecebedorNoGateway);

                return result.Sucesso ? result.Valor / 100 : 0;
            }
            catch (Exception)
            {
                return 0;
            }

        }

        //FOI+/-
        public async Task<DTO.DadosDoPagamentoRealizadoDTO> RealizarPagamentoOnlineNoEstabelecimento(int idEstabelecimento, NovoPagamentoOnlineDTO dadosDoPagamento)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idEstabelecimento);
            if (estabelecimentoRecebedor == null)
                throw new EstabelecimentoNaoConfiguradoComoRecebedorException();

            Conta conta = ObterContaDoUsuarioLogado();
            if (conta == null)
                throw new UnauthorizedAccessException();

            ParametrosDeRecebimentoNoTrinksParaConfiguracao parametrosDeRecebimento;
            bool primeiraVisitaNoEstabelecimentoEVeioPeloTrinks = false;

            List<SplitPagamentoDTO> splitsDoPagamentoOnline =
                 CalcularSplitDoPagamentoOnline(estabelecimentoRecebedor,
                    conta.IdConta, dadosDoPagamento.ValorTotal, MetodoDePagamentoNoGatewayEnum.CartaoDeCredito,
                    out primeiraVisitaNoEstabelecimentoEVeioPeloTrinks, out parametrosDeRecebimento);

            var resultadoDoPagamento = await Domain.Pagamentos.PagamentosApplicationService
                .RealizarPagamento(new NovoPagamentoDTO()
                {
                    OrigemDePagamento = OrigemDePagamentoEnum.PagamentoOnline,
                    IdRecebedor = estabelecimentoRecebedor.IdRecebedor,
                    IdCartaoDeComprador = dadosDoPagamento.IdCartao,
                    Valor = dadosDoPagamento.ValorTotal,
                    Itens = dadosDoPagamento.ItensPagos.Select(i => new ItemNovoPagamentoDTO()
                    {
                        Nome = i.NomeItem,
                        Valor = i.ValorComDesconto.HasValue ? i.ValorComDesconto.Value : i.ValorOriginal
                    }).ToList(),
                    RegrasDeSplit = splitsDoPagamentoOnline
                });

            var pagamento = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Factory.CreateParaRealizacaoDePagamento(
                idPagamentoOnline: resultadoDoPagamento.Valor.IdPagamento,
                primeiroPagamentoDoCliente: primeiraVisitaNoEstabelecimentoEVeioPeloTrinks,
                parametrosDeRecebimentos: new ParametrosDeRecebimentoNoTrinksParaTransacao(parametrosDeRecebimento.Parametros));

            if (resultadoDoPagamento.Valor.PagamentoFoiRealizado)
            {
                pagamento.IndicarQueFoiPagoComSucesso(resultadoDoPagamento.Valor.ValorPago);
            }
            else
            {
                pagamento.IndicarQueTeveProblemasNoPagamento();
            }

            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.SaveNew(pagamento);
            return new DTO.DadosDoPagamentoRealizadoDTO() { DataHoraPagamento = resultadoDoPagamento.Valor.DataPagamento, Pagamento = pagamento, PagamentoFoiRealizado = resultadoDoPagamento.Valor.PagamentoFoiRealizado };
        }

        public List<SplitPagamentoDTO> CalcularSplitDoPagamentoOnline(EstabelecimentoRecebedor estabelecimentoRecebedor, int idConta, 
            decimal valorTotalTransacao, MetodoDePagamentoNoGatewayEnum metodoDePagamento, out bool primeiraVisitaNoEstabelecimentoEVeioPeloTrinks, 
            out ParametrosDeRecebimentoNoTrinksParaConfiguracao taxasACobrarDaTransacao, int? numeroParcela = 1)
        {
            primeiraVisitaNoEstabelecimentoEVeioPeloTrinks = ClienteEstahEmSuaPrimeiraVisitaNoEstabelecimentoEVeioPeloTrinks(idConta, estabelecimentoRecebedor.IdEstabelecimento);
            taxasACobrarDaTransacao = ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(estabelecimentoRecebedor.IdEstabelecimento, metodoDePagamento, numeroParcela);
            var gatewayAtivo =
                Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedor(estabelecimentoRecebedor.IdRecebedor);

            var naoSuportaConfigsDeTaxasFixas = gatewayAtivo == GatewayEnum.Zoop && taxasACobrarDaTransacao.ObterValorFixoTotalPorTransacao(primeiraVisitaNoEstabelecimentoEVeioPeloTrinks) > 0;

            if (naoSuportaConfigsDeTaxasFixas)
            {
                var exception = new ErroNaConfiguracaoDoPagamentoOnlineException("[PgtoOnline] O pagamento online não suporta configuração de taxas fixas");
                ErrorSignal.FromCurrentContext().Raise(exception);
                throw exception;
            }

            decimal totalDasTaxas = Math.Truncate(
                // total percentual a ser descontado +
                (valorTotalTransacao * (taxasACobrarDaTransacao.ObterPercentualTotalPorTransacao(primeiraVisitaNoEstabelecimentoEVeioPeloTrinks) / 100) +
                // total fixo
                taxasACobrarDaTransacao.ObterValorFixoTotalPorTransacao(primeiraVisitaNoEstabelecimentoEVeioPeloTrinks)
                ) * 100
            ) / 100;
            decimal totalEstabelecimento = valorTotalTransacao - totalDasTaxas;

            if (gatewayAtivo == GatewayEnum.Zoop)
                // Enquanto aguardamos resposta da Zoop quanto ao ajuste no cálculo do Split
                totalEstabelecimento = totalEstabelecimento - 0.01m;

            //decimal totalDeTaxa = taxasACobrarDaTransacao.ObterPercentualTotalPorTransacao(primeiraVisitaNoEstabelecimentoEVeioPeloTrinks);
            //decimal totalEstabelecimento = Math.Truncate((100 - totalDeTaxa) / 100 * valorTotalTransacao * 100) / 100;
            //decimal totalTrinks = valorTotalTransacao - totalEstabelecimento;

            var configGateway = ConfiguracaoGatewayFactory.ObterConfiguracoesDeAcordoComOGateway(gatewayAtivo, metodoDePagamento);

            var idRecebedorTrinks = configGateway.IdRecebedorTrinks;

            return new List<SplitPagamentoDTO>() {
			   // Trinks
			   new SplitPagamentoDTO() {
                   IdRecebedor = idRecebedorTrinks,
                   ResponsavelPelasTaxasDaOperacao = true,
                   ResponsavelPeloChargeback = false,
                   ResponsavelPeloRestoDaDivisaoDeTaxas = true,
                   Valor = totalDasTaxas
               },
			   // Estabelecimento
			   new SplitPagamentoDTO() {
                   IdRecebedor = estabelecimentoRecebedor.IdRecebedor,
                   ResponsavelPelasTaxasDaOperacao = false,
                   ResponsavelPeloChargeback = true,
                   ResponsavelPeloRestoDaDivisaoDeTaxas = false,
                   Valor = totalEstabelecimento
               }
           };
        }

        public bool ClienteEstahEmSuaPrimeiraVisitaNoEstabelecimentoEVeioPeloTrinks(int idConta, int idEstabelecimento)
        {
            return false;
        }

        public DadosParaAtivacaoDTO ObterDadosParaAtivacaoDeRecebedor(Estabelecimento estabelecimento)
        {
            var (recebedor, gateway) = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoComGatewayPorEstabelecimento(estabelecimento.IdEstabelecimento);

            if (!gateway.HasValue || gateway == GatewayEnum.Zoop)
                return new DadosParaAtivacaoDTO(estabelecimento);

            return ObterDadosParaAtivacaoDeRecebedor(estabelecimento, recebedor);
        }

        public DadosParaAtivacaoDTO ObterDadosParaAtivacaoDeRecebedor(Estabelecimento estabelecimento, GatewayEnum gateway)
        {
            if (gateway == 0 || gateway == GatewayEnum.Zoop)
                return new DadosParaAtivacaoDTO(estabelecimento);
            
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ObterPorEstabelecimentoEGateway(estabelecimento.IdEstabelecimento, gateway);

            return ObterDadosParaAtivacaoDeRecebedor(estabelecimento, estabelecimentoRecebedor);
        }

        private DadosParaAtivacaoDTO ObterDadosParaAtivacaoDeRecebedor(Estabelecimento estabelecimento, EstabelecimentoRecebedor estabelecimentoRecebedor)
        {
            if (estabelecimentoRecebedor == null)
                return new DadosParaAtivacaoDTO(estabelecimento);

            var dadosDoRecebedor = Domain.Pagamentos.RecebedorApplicationService.ObterDadosDeRecebedor(estabelecimentoRecebedor.IdRecebedor);

            if (estabelecimentoRecebedor.Status == Enums.StatusDaAtivacaoDoRecebedorEnum.Recusado || !dadosDoRecebedor.PossuiContaBancariaAtiva)
                return new DadosParaAtivacaoDTO(dadosDoRecebedor, estabelecimento);

            var contaBancaria = dadosDoRecebedor.ContaBancaria;

            var institucaoBancaria = Domain.PagamentosOnlineNoTrinks.InstituicaoBancariaRepository.ObterInstitucaoBancariaPorCodigo(contaBancaria.CodigoBanco);//TODO:duvida - agora tem que trazer o nome do banco no pagamentos
            if (institucaoBancaria == null)
                throw new InvalidOperationException("A institução bancaria não existe.");

            var retorno = new DadosParaAtivacaoDTO(dadosDoRecebedor, institucaoBancaria);
            return retorno;
        }

        public ParametrosDeRecebimentoNoTrinksParaConfiguracao ObterTaxasConfiguradasParaEstabelecimentoPorOrigem(int idEstabelecimento, OrigemDeCadastroEnum origem, int? numeroParcela = 1)
        {
            ParametrosDeRecebimentoNoTrinks taxasPadrao = ObterTaxasPadraoDoTrinksPorOrigem(origem, numeroParcela);

            return CompararTaxas(idEstabelecimento, taxasPadrao);
        }

        public ParametrosDeRecebimentoNoTrinksParaConfiguracao ObterTaxasConfiguradasParaEstabelecimento(int idEstabelecimento, int? numeroParcela = 1)
        {
            ParametrosDeRecebimentoNoTrinks taxasPadrao = ObterTaxasPadraoDoTrinks(idEstabelecimento, numeroParcela);

            return CompararTaxas(idEstabelecimento, taxasPadrao);
        }
        
        public ParametrosDeRecebimentoNoTrinksParaConfiguracao ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(int idEstabelecimento, 
            MetodoDePagamentoNoGatewayEnum metodoDePagamento, int? numeroParcela = 1)
        {
            ParametrosDeRecebimentoNoTrinks taxasPadrao = ObterTaxasPadraoDoTrinksPorMetodoDePagamento(idEstabelecimento, metodoDePagamento, numeroParcela);

            return CompararTaxas(idEstabelecimento, taxasPadrao, metodoDePagamento);
        }

        //FOI+/-
        private ParametrosDeRecebimentoNoTrinksParaConfiguracao CompararTaxas(int idEstabelecimento, ParametrosDeRecebimentoNoTrinks taxasPadrao, 
            MetodoDePagamentoNoGatewayEnum metodoDePagamento = MetodoDePagamentoNoGatewayEnum.CartaoDeCredito)
        {
            var taxasConfiguradasParaOEstabelecimento = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterTaxasEstabelecimentoRecebedorAtivoPorIdEstabelecimento(idEstabelecimento, metodoDePagamento);

            ParametrosDeRecebimentoNoTrinks taxasEstabelecimento = taxasConfiguradasParaOEstabelecimento?.Taxas ?? new ParametrosDeRecebimentoNoTrinks();

            return new ParametrosDeRecebimentoNoTrinksParaConfiguracao()
            {
                Parametros = new ParametrosDeRecebimentoNoTrinks(
               percentualTrinks: CompararTaxaASerAplicada(taxasEstabelecimento.PercentualTrinks, taxasPadrao.PercentualTrinks),
               percentualTrinksPrimeiroPagamento: CompararTaxaASerAplicada(taxasEstabelecimento.PercentualTrinksPrimeiroPagamento, taxasPadrao.PercentualTrinksPrimeiroPagamento),
               valorFixoTrinks: CompararTaxaASerAplicada(taxasEstabelecimento.ValorFixoTrinks, taxasPadrao.ValorFixoTrinks),
               valorFixoTrinksPrimeiroPagamento: CompararTaxaASerAplicada(taxasEstabelecimento.ValorFixoTrinksPrimeiroPagamento, taxasPadrao.ValorFixoTrinksPrimeiroPagamento),
               percentualOperadora: CompararTaxaASerAplicada(taxasEstabelecimento.PercentualOperadora, taxasPadrao.PercentualOperadora),
               valorFixoOperadora: CompararTaxaASerAplicada(taxasEstabelecimento.ValorFixoOperadora, taxasPadrao.ValorFixoOperadora),
               valorPorTransferencia: CompararTaxaASerAplicada(taxasEstabelecimento.ValorPorTransferencia, taxasPadrao.ValorPorTransferencia),
               diasParaReceber: CompararTaxaASerAplicada(taxasEstabelecimento.DiasParaReceber, taxasPadrao.DiasParaReceber)
           ),
                DiasParaRecebimentoEhAproximado = new ParametrosTrinks<bool>(ParametrosTrinksEnum.pgtoOnline_dias_para_recebimento_eh_aproximado).ObterValor() //true//TODO:duvida - ZZZ
            };
        }

        public bool EstabelecimentoEstaConfiguradoComoRecebedor(int idEstabelecimento)
        {
            return Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable().Any(er => er.Ativo && er.IdEstabelecimento == idEstabelecimento);
        }

        public bool EstabelecimentoEstaHabilitadoParaPagamentoOnline(int idEstabelecimento)
        {
            return Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable().Any(er => er.Ativo && er.IdEstabelecimento == idEstabelecimento && er.Habilitado);
        }

        public bool EstabelecimentoEstaHabilitadoParaPagamentoOnlineNoGatewayOuAindaNaoRealizouCadastro(int idEstabelecimento)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Queryable().Where(er => er.Ativo && er.IdEstabelecimento == idEstabelecimento).FirstOrDefault();
            var estabelecimentoJaFezOCadastro = estabelecimentoRecebedor != null;

            if (!estabelecimentoJaFezOCadastro)
                return true;

            return estabelecimentoRecebedor.Habilitado && estabelecimentoRecebedor.Status == Enums.StatusDaAtivacaoDoRecebedorEnum.Aprovado;
        }

        public UltimaTransacaoRealizadaDTO ObterDadosDaUltimaTransacaoRealizadaPorConta(int idConta)
        {
            var idUltimoCartao = Domain.Pagamentos.CartaoApplicationService.ObterIdUltimoCartaoUsadoDoUsuarioAutenticado();

            if (idUltimoCartao > 0)
                return new UltimaTransacaoRealizadaDTO() { IdCartao = idUltimoCartao.Value };
            else
                return null;

        }


        public bool StatusPermitePagamentoOnline(StatusHorarioEnum status)
        {
            return status == StatusHorarioEnum.Confirmado || status == StatusHorarioEnum.Aguardando_Confirmacao;
        }


        //FOI+/-
        [TransactionInitRequired]
        public async Task CadastrarContaBancaria(int idEstabelecimento, CadastrarContaBancariaDTO dadosDaNovaConta)
        {
            var estabelecimentoRecebedor = ObterEstabelecimentoRecebedorAtivo(idEstabelecimento);

            if (estabelecimentoRecebedor == null)
                throw new ArgumentException("O estabelecimento informado não possui configuração de pagamento online");

            var contaParaCadastrarDTO = new ContaBancariaParaCadastrarDTO()
            {
                IdRecebedor = estabelecimentoRecebedor.IdRecebedor,
                Agencia = dadosDaNovaConta.Agencia,
                AgenciaDV = dadosDaNovaConta.AgenciaDV,
                CNPJ = dadosDaNovaConta.NumeroDocumento.SomenteLetrasENumeros(),
                CodigoBanco = dadosDaNovaConta.CodigoDoBanco,
                NumeroConta = dadosDaNovaConta.Conta,
                NumeroContaDV = dadosDaNovaConta.ContaDV,
                TipoDaConta = dadosDaNovaConta.TipoConta,
                Titular = dadosDaNovaConta.NomeLegal
            };

            await Domain.Pagamentos.RecebedorApplicationService.CadastrarContaBancariaParaRecebedor(contaParaCadastrarDTO);
        }

        public void MarcarEstabelecimentoRecebedorComoNegado(int idEstabelecimento)
        {
            EstabelecimentoRecebedor estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idEstabelecimento);

            if (estabelecimentoRecebedor != null)
            {
                estabelecimentoRecebedor.Status = Enums.StatusDaAtivacaoDoRecebedorEnum.Recusado;
                estabelecimentoRecebedor.Habilitado = false;
                Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Update(estabelecimentoRecebedor);
            }
        }

        public Task AtualizarStatusDeRecebedor(string idRecebedorCredenciado, GatewayEnum gateway)
        {
            throw new NotImplementedException();
        }

        public void AtualizarStatusRecebedorGateway(AtualizarStatusRecebedorGatewayDTO dto)
        {
            var recebedorCadastrado = Domain.Pagamentos.RecebedorApplicationService.AtualizarStatusDoRecebdor(dto.IdNoGateway, dto.Gateway, dto.NovoStatus);

            if (recebedorCadastrado == null)
            {
                LogService<PagamentoOnlineNoTrinksService>.Error("O recebedor credenciado não foi encontrado.");
                return;
            }

            AtualizarStatusKycDoRecebedor(dto);

            var idEstabelecimento = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterIdEstabelecimentoPorIdRecebedor(recebedorCadastrado.Id);

            AtualizarEtapaCadastroPagarme(idEstabelecimento);

            if (!recebedorCadastrado.Habilitado)
                return;

            HabilitarEstabelecimentoRecebedor(recebedorCadastrado);

            HabilitarConfiguracoesNoGatewayHabilitado(dto.Gateway, idEstabelecimento);
        }

        private void AtualizarEtapaCadastroPagarme(int idEstabelecimento)
        {
            Domain.Pagamentos.RecebedorApplicationService.AtualizarEtapaCadastroPagarme(idEstabelecimento);
        }

        private void AtualizarStatusKycDoRecebedor(AtualizarStatusRecebedorGatewayDTO dto)
        {
            var kycDto = new AtualizarKycDTO(dto.IdNoGateway, dto.Gateway, dto.StatusKyc, dto.MotivoStatusKyc);

            Domain.Pagamentos.RecebedorApplicationService.AtualizarStatusKycDoRecebedor(kycDto);
        }

        private void HabilitarConfiguracoesNoGatewayHabilitado(GatewayEnum gateway, int idEstabelecimento)
        {
            switch (gateway)
            {
                case GatewayEnum.PagarMe:
                case GatewayEnum.PagarMeV5:
                    HabilitarFuncionalidadesAssociadasPagarme(idEstabelecimento);
                    break;
                case GatewayEnum.Zoop:
                    HabilitarFuncionalidadesAssociadasZoop(idEstabelecimento);
                    break;
                default:
                    throw new NotImplementedException($"Gateway não implementado: {gateway.ToString()}");
            }
        }

        private void DesabilitarConfiguracoesNaoAssociadasAoGatewayAtivo(GatewayEnum gateway, int idEstabelecimento)
        {
            switch (gateway)
            {
                case GatewayEnum.PagarMe:
                case GatewayEnum.PagarMeV5:
                    DesabilitarConfiguracoesNaoDisponiveisNoGatewayPagarme(idEstabelecimento);
                    break;
                case GatewayEnum.Zoop:
                    DesabilitarConfiguracoesNaoDisponiveisNoGatewayZoop(idEstabelecimento);
                    break;
                default:
                    throw new NotImplementedException($"Gateway não implementado: {gateway.ToString()}");
            }
        }

        private void DesabilitarConfiguracoesNaoDisponiveisNoGatewayPagarme(int idEstabelecimento)
        {
            InativarEstabelecimentoFormaDePagamentoOnline(idEstabelecimento);
        }

        private void DesabilitarConfiguracoesNaoDisponiveisNoGatewayZoop(int idEstabelecimento)
        {
            DesabilitarFuncionalidadesAssociadasPagarme(idEstabelecimento);
        }

        private void HabilitarFuncionalidadesAssociadasZoop(int idEstabelecimento)
        {
            AssociarEstabelecimentoAFormaDePagamentoOnline(idEstabelecimento);
            ;
        }

        private void HabilitarFuncionalidadesAssociadasPagarme(int idEstabelecimento)
        {
            Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.AssociarEstabelecimentoAFormaDePagamentoLinkDePagamento(idEstabelecimento);
        }

        private void DesabilitarFuncionalidadesAssociadasPagarme(int idEstabelecimento)
        {
            Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.InativarEstabelecimentoAFormaDePagamentoLinkDePagamento(idEstabelecimento);
        }

        private void HabilitarEstabelecimentoRecebedor(RecebedorCadastradoDTO recebedorCadastrado)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterPorIdRecebedor(recebedorCadastrado.Id);

            estabelecimentoRecebedor.AprovarAtivacao();
            Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Update(estabelecimentoRecebedor);
        }

        //FOI+/-
        [TransactionInitRequired]
        public async Task<bool> ConfigurarEstabelecimentoComoRecebedor(ConfigurarEstabelecimentoRecebedorDTO dtoEstabelecimentoRecebedor)
        {

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(dtoEstabelecimentoRecebedor.IdEstabelecimento);

            var gateway = (GatewayEnum)(int)dtoEstabelecimentoRecebedor.Origem;

            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimentoEGateway(dtoEstabelecimentoRecebedor.IdEstabelecimento, gateway);
            
            if (estabelecimentoRecebedor != null)
                throw new ArgumentException("O estabelecimento informado já possui configuração de pagamento online");
            
            var instituicaoBancaria = Domain.PagamentosOnlineNoTrinks.InstituicaoBancariaRepository.Load(dtoEstabelecimentoRecebedor.Banco);
            var tipoConta = (TipoDeContaBancariaEnum)dtoEstabelecimentoRecebedor.TipoDaConta;

            RecebedorCadastradoDTO retornoCadastro;

            if (dtoEstabelecimentoRecebedor.Origem == OrigemDeCadastroEnum.PagarMeV5)
            {
                var dtoPagarme = ObterPagarmeDTO(dtoEstabelecimentoRecebedor as ConfigurarEstabelecimentoRecebedorPagarmeDTO, estabelecimento);

                retornoCadastro = await Domain.Pagamentos.RecebedorApplicationService.CadastrarRecebedor(dtoPagarme, new ContaBancariaParaCadastrarDTO()
                {
                    Agencia = dtoEstabelecimentoRecebedor.AgenciaBancaria,
                    AgenciaDV = dtoEstabelecimentoRecebedor.AgenciaBancariaDV,
                    CNPJ = dtoEstabelecimentoRecebedor.CNPJ,
                    CodigoBanco = instituicaoBancaria.Codigo,
                    NumeroConta = dtoEstabelecimentoRecebedor.NumeroConta,
                    NumeroContaDV = dtoEstabelecimentoRecebedor.NumeroContaDV,
                    TipoDaConta = tipoConta,
                    Titular = dtoEstabelecimentoRecebedor.NomeTitular
                }, dtoEstabelecimentoRecebedor.Origem);
            }
            else
            {
                var recebedorZoopDTO = new RecebedorParaCadastrarNoGatewayDTO()
                {
                    CNPJ = dtoEstabelecimentoRecebedor.CNPJ.SomenteLetrasENumeros(),
                    Endereco = ObterEnderecoParaSalvarNoCredenciamentoDoRecebedor(estabelecimento),
                    Nome = estabelecimento.PessoaJuridica.NomeFantasia,
                    TelefoneEmpresa = estabelecimento.PessoaJuridica.Telefones.FiltrarTelefoneAtivoPorDono(estabelecimento.PessoaJuridica.IdPessoa).FirstOrDefault()?.ToString(),
                    WebsiteEmpresa = dtoEstabelecimentoRecebedor.WebsiteEmpresa,
                    EmailEmpresa = estabelecimento.ObterResponsavel().Email,
                    DataDeAberturaDaEmpresa = dtoEstabelecimentoRecebedor.DataAberturaEmpresa,
                };

                retornoCadastro = await Domain.Pagamentos.RecebedorApplicationService.CadastrarRecebedor(recebedorZoopDTO, new ContaBancariaParaCadastrarDTO()
                {
                    Agencia = dtoEstabelecimentoRecebedor.AgenciaBancaria,
                    AgenciaDV = dtoEstabelecimentoRecebedor.AgenciaBancariaDV,
                    CNPJ = dtoEstabelecimentoRecebedor.CNPJ,
                    CodigoBanco = instituicaoBancaria.Codigo,
                    NumeroConta = dtoEstabelecimentoRecebedor.NumeroConta,
                    NumeroContaDV = dtoEstabelecimentoRecebedor.NumeroContaDV,
                    TipoDaConta = tipoConta,
                    Titular = dtoEstabelecimentoRecebedor.NomeTitular
                }, dtoEstabelecimentoRecebedor.Origem);
            }

            if (ValidationHelper.Instance.IsValid && retornoCadastro != null)
            {
                EstabelecimentoRecebedor recebedor = new EstabelecimentoRecebedor()
                {
                    IdEstabelecimento = dtoEstabelecimentoRecebedor.IdEstabelecimento,
                    IdRecebedor = retornoCadastro.Id,
                    Status = Enums.StatusDaAtivacaoDoRecebedorEnum.EmAnalise
                };

                if (retornoCadastro.Habilitado && OrigemDeCadastroEnum.Zoop == dtoEstabelecimentoRecebedor.Origem)
                {
                    recebedor.AprovarAtivacao();
                    AssociarEstabelecimentoAFormaDePagamentoOnline(dtoEstabelecimentoRecebedor.IdEstabelecimento);//TODO:duvida - quando tiver o evento de ativação, colocar lá
                }

                if (retornoCadastro.Habilitado && OrigemDeCadastroEnum.PagarMeV5 == dtoEstabelecimentoRecebedor.Origem)
                {
                    recebedor.AprovarAtivacao();
                    Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.AssociarEstabelecimentoAFormaDePagamentoLinkDePagamento(dtoEstabelecimentoRecebedor.IdEstabelecimento);
                }

                Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.SaveNew(recebedor);
            }

            return retornoCadastro?.Habilitado == true;
        }

        private RecebedorParaCadastrarNaPagarmeDTO ObterPagarmeDTO(ConfigurarEstabelecimentoRecebedorPagarmeDTO dtoEstabelecimentoRecebedor, Estabelecimento estabelecimento)
        {
            var dto = new RecebedorParaCadastrarNaPagarmeDTO
            {
                CNPJ = dtoEstabelecimentoRecebedor.CNPJ,
                DataDeAberturaDaEmpresa = dtoEstabelecimentoRecebedor.DataAberturaEmpresa,
                Mcc = dtoEstabelecimentoRecebedor.Mcc,
                TipoDeRegistro = dtoEstabelecimentoRecebedor.TipoDeRegistro,
                FaturamentoAnualEmpresa = dtoEstabelecimentoRecebedor.FaturamentoAnualEmpresa,
                Nome = estabelecimento.PessoaJuridica.NomeFantasia,
                RazaoSocial = dtoEstabelecimentoRecebedor.RazaoSocial,
                TelefoneEmpresa = dtoEstabelecimentoRecebedor.TelefoneEmpresa,
                WebsiteEmpresa = dtoEstabelecimentoRecebedor.WebsiteEmpresa,
                EmailEmpresa = dtoEstabelecimentoRecebedor.EmailEmpresa,
                Endereco = new EnderecoParaCadastrarDTO
                {
                    Bairro = dtoEstabelecimentoRecebedor.Endereco.Bairro,
                    CEP = dtoEstabelecimentoRecebedor.Endereco.CEP,
                    Cidade = dtoEstabelecimentoRecebedor.Endereco.Cidade,
                    Complemento = dtoEstabelecimentoRecebedor.Endereco.Complemento,
                    Estado = dtoEstabelecimentoRecebedor.Endereco.Estado,
                    Logradouro = dtoEstabelecimentoRecebedor.Endereco.Logradouro,
                    Numero = dtoEstabelecimentoRecebedor.Endereco.Numero,
                    PontoDeReferencia = dtoEstabelecimentoRecebedor.Endereco.PontoDeReferencia
                },
                Responsavel = new ResponsavelLegal
                {
                    DataNascimento = dtoEstabelecimentoRecebedor.Responsavel.DataNascimento,
                    NumeroDocumento = dtoEstabelecimentoRecebedor.Responsavel.NumeroDocumento,
                    Email = dtoEstabelecimentoRecebedor.Responsavel.Email,
                    Nome = dtoEstabelecimentoRecebedor.Responsavel.Nome,
                    Telefone = dtoEstabelecimentoRecebedor.Responsavel.Telefone,
                    Tipo = dtoEstabelecimentoRecebedor.Responsavel.Tipo,
                    RendaMensalDeclaradaCentavos = dtoEstabelecimentoRecebedor.Responsavel.RendaMensalDeclaradaCentavos,
                    ProfissaoDoResponsavel = dtoEstabelecimentoRecebedor.Responsavel.ProfissaoDoResponsavel,
                    EhRepresentanteLegalDaEmpresa = dtoEstabelecimentoRecebedor.Responsavel.EhRepresentanteLegalDaEmpresa,
                    Endereco = new EnderecoParaCadastrarDTO
                    {
                        Bairro = dtoEstabelecimentoRecebedor.Responsavel.Endereco.Bairro,
                        CEP = dtoEstabelecimentoRecebedor.Responsavel.Endereco.CEP,
                        Cidade = dtoEstabelecimentoRecebedor.Responsavel.Endereco.Cidade,
                        Estado = dtoEstabelecimentoRecebedor.Responsavel.Endereco.Estado,
                        Complemento = dtoEstabelecimentoRecebedor.Responsavel.Endereco.Complemento,
                        PontoDeReferencia = dtoEstabelecimentoRecebedor.Responsavel.Endereco.PontoDeReferencia,
                        Logradouro = dtoEstabelecimentoRecebedor.Responsavel.Endereco.Logradouro,
                        Numero = dtoEstabelecimentoRecebedor.Responsavel.Endereco.Numero
                    }
                }
            };

            return dto;
        }

        public EnderecoParaCadastrarDTO ObterEnderecoParaSalvarNoCredenciamentoDoRecebedor(Estabelecimento estabelecimento)
        {
            var enderecoDoRecebedorDTO = new EnderecoParaCadastrarDTO();
            if (estabelecimento.PessoaJuridica.EnderecoProprio != null)
            {
                var endereco = estabelecimento.PessoaJuridica.EnderecoProprio;
                enderecoDoRecebedorDTO.Logradouro = $"{endereco.TipoLogradouro.Nome} {endereco.Logradouro}";
                enderecoDoRecebedorDTO.Numero = endereco.Numero;
                enderecoDoRecebedorDTO.Bairro = endereco.Bairro;
                enderecoDoRecebedorDTO.Cidade = endereco.Cidade;
                enderecoDoRecebedorDTO.Estado = endereco.UF.Sigla;
                enderecoDoRecebedorDTO.CEP = endereco.Cep;
                enderecoDoRecebedorDTO.Complemento = string.IsNullOrEmpty(endereco.Complemento) ? "n/a" : endereco.Complemento;
            }

            return enderecoDoRecebedorDTO;
        }

        public EstabelecimentoRecebedor ObterEstabelecimentoRecebedorAtivo(int idEstabelecimento)
        {
            return Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idEstabelecimento);
        }

        public void AssociarEstabelecimentoAFormaDePagamentoOnline(int idEstabelecimento)
        {
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
            var formaDePagamentoPagamentoOnline = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(p => p.Id == (int)FormaPagamentoEnum.PagamentoOnline).Single();
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoPagamentoOnline, estabelecimento);

            var formaDePagamentoDeCreditoPagamentoOnline = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(p => p.Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnline).Single();
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoDeCreditoPagamentoOnline, estabelecimento);

            var formaDePagamentoDeCreditoCliente = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(p => p.Id == (int)FormaPagamentoEnum.CreditoCliente).Single();
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoDeCreditoCliente, estabelecimento);

            List<MotivoDeDescontoDoTrinksEnum> motivosDoTrinksAtivados;
            Domain.Pessoas.EstabelecimentoService.CriarOuDesativarMotivosDeDescontoDoTrinksSeNecessario(estabelecimento, out motivosDoTrinksAtivados);
        }

        private void InativarEstabelecimentoFormaDePagamentoOnline(int idEstabelecimento)
        {
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.Inativar((int)FormaPagamentoEnum.PagamentoOnline, idEstabelecimento);

            Domain.Pessoas.EstabelecimentoFormaPagamentoService.Inativar((int)FormaPagamentoEnum.CreditoDePagamentoOnline, idEstabelecimento);
        }

        private T CompararTaxaASerAplicada<T>(T taxaDoEstabelecimento, T taxaPadrao)
        {
            return taxaDoEstabelecimento != null ? taxaDoEstabelecimento : taxaPadrao;
        }


        private const int NumeroMaximoDeCaracteresDoTextoDeExibicaoNaFatura = 13;
        private string GerarNomeDeExibicaoNaFatura(string nomeDoEstabelecimento)
        {
            return nomeDoEstabelecimento.ToUpper().RemoverAcentos().SomenteLetrasENumeros().Left(NumeroMaximoDeCaracteresDoTextoDeExibicaoNaFatura);
        }

        public List<KeyValuePair<int, string>> ObterListaDeTipoContaBancarias()
        {
            return EnumActions.GetElements(typeof(TipoDeContaBancariaEnum))
                .Where(x => x.Key > 0)
                .ToList();
        }

        public List<KeyValuePair<int, string>> ObterListaDeInstituicoesBancarias()
        {
            return Domain.PagamentosOnlineNoTrinks.InstituicaoBancariaRepository
                .StatelessQueryable()
                .Where(x => x.Id != new ParametrosTrinks<int>(ParametrosTrinksEnum.id_banco_temporario).ObterValor())
                .Select(x => new KeyValuePair<int, string>(x.Id, x.Codigo + " - " + x.Nome))
                .OrderBy(x => x.Value)
                .ToList();
        }

        public string ObterNomeDaInstituicaoBancariaPeloCodigo(string codigoDoBanco)
        {
            return Domain.PagamentosOnlineNoTrinks.InstituicaoBancariaRepository.Queryable(true)
                .FirstOrDefault(x => x.Codigo == codigoDoBanco)
                ?.Nome ?? string.Empty;
        }

        public CadastrarContaBancariaDTO OrganizarDadosCadastroContaBancaria(
                                                    int banco,
                                                    string numeroConta,
                                                    string numeroContaDV,
                                                    int tipoDaConta,
                                                    string cnpjConta,
                                                    string agenciaBancaria,
                                                    string agenciaBancariaDV,
                                                    string nomeTitular)
        {


            var instituicaoBancaria = Domain.PagamentosOnlineNoTrinks.InstituicaoBancariaRepository.Load(banco);
            var tipoConta = (Perlink.Pagamentos.Gateways.Enums.TipoDeContaBancariaEnum)tipoDaConta;

            return new CadastrarContaBancariaDTO()
            {
                CodigoDoBanco = instituicaoBancaria.Codigo,
                Agencia = agenciaBancaria,
                Conta = numeroConta,
                TipoConta = tipoConta,
                NumeroDocumento = cnpjConta,
                NomeLegal = nomeTitular,
                AgenciaDV = agenciaBancariaDV,
                ContaDV = numeroContaDV
            };
        }

        public bool EstabelecimentoEstaRegistradoComoRecebedor(int idEstabelecimento, OrigemDeCadastroEnum origem)
        {
            var gateway = Domain.Pagamentos.PagamentosService.ObterGatewayPadraoParaCredenciamento(origem);

            var todosRecebedores = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ListarAtivosComGatewayPorEstabelecimento(idEstabelecimento);

            return todosRecebedores.Exists(r => r.Gateway == gateway);
        }

        public bool EstabelecimentoEstaRegistradoComoRecebedor(int idEstabelecimento, params OrigemDeCadastroEnum[] origens)
        {
            var gateways = origens.Select(o => Domain.Pagamentos.PagamentosService.ObterGatewayPadraoParaCredenciamento(o));

            var todosRecebedores = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ListarComGatewayPorEstabelecimento(idEstabelecimento);

            return todosRecebedores.Select(r => r.Gateway).Intersect(gateways).Any();
        }

        public async Task<bool> EstornarPagamentoOnline(PagamentoOnlineNoTrinks pagamentoOnline)
        {
            bool estornado = false;

            var retornoDoGateway = await Domain.Pagamentos.PagamentosApplicationService.EstornarPagamento(pagamentoOnline.IdPagamentoOnline);

            if (retornoDoGateway.Sucesso)
            {
                pagamentoOnline.IndicarQueFoiEstornado();
                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamentoOnline);
                estornado = true;
            }

            return estornado;
        }

        //FOI+/-
        [TransactionInitRequired]
        public async Task<RetornoDeUploadDoDocumentoDTO> CarregarDocumentoParaCredenciamento(int idEstabelecimento, byte[] arquivo, string nomeDoArquivo, Enums.TipoDocumentoCredenciamentoEnum tipoDocumento)
        {

            var estabelecimentoRecebedor = ObterEstabelecimentoRecebedorAtivo(idEstabelecimento);
            if (estabelecimentoRecebedor == null)
                throw new ArgumentException("O estabelecimento informado não possui configuração de Pagamento Online");

            var documentoDTO = new DocumentoParaCadastrarDTO()
            {
                Arquivo = arquivo,
                IdRecebedor = estabelecimentoRecebedor.IdRecebedor,
                TipoDocumento = tipoDocumento.ToString(),
                NomeDoArquivo = nomeDoArquivo
            };
            var retornoCarregamento = await Domain.Pagamentos.RecebedorApplicationService.CadastrarDocumentoParaRecebedor(documentoDTO);

            return new RetornoDeUploadDoDocumentoDTO() { CarregadoComSucesso = retornoCarregamento.IdDocumento > 0 };
        }

        public AtivacaoDoRecebedor ObterDadosDaAtivacaoDoEstabelecimentoComoRecebedor(int idEstabelecimento)
        {

            var ativacao = new AtivacaoDoRecebedor();
            var estabelecimentoRecebedor = ObterEstabelecimentoRecebedorAtivo(idEstabelecimento);

            if (estabelecimentoRecebedor != null)
            {

                var gatewayRecebedor = Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedor(estabelecimentoRecebedor.IdRecebedor);

                var requerUploadDeDocumentos = gatewayRecebedor == GatewayEnum.Zoop;

                if (requerUploadDeDocumentos)
                {
                    ativacao.JaSolicitouAtivacao = estabelecimentoRecebedor.JaSolicitouAtivacao();
                    ativacao.AtivadoComSucesso = estabelecimentoRecebedor.AtivacaoFoiRealizadaComSucesso();
                    ativacao.AtivacaoEstaEmAnalise = estabelecimentoRecebedor.AtivacaoEstaEmAnalise();
                    ativacao.SituacaoDosDocumentos = ObterSituacaoDosDocumentosDoRecebedor(estabelecimentoRecebedor.IdRecebedor);
                }
                else
                {
                    ativacao.JaSolicitouAtivacao = true;
                    ativacao.AtivadoComSucesso = true;
                    ativacao.AtivacaoEstaEmAnalise = false;

                    ativacao.SituacaoDosDocumentos = new SituacoesDosDocumentosDTO
                    {
                        DocumentoEmpresaCarregadoComSucesso = true,
                        DocumentoProprietarioCarregadoComSucesso = true,
                        DocumentoResidenciaCarregadoComSucesso = true
                    };
                }

                ativacao.PossuiServicosJaConfigurados = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ValidarSeEstabelecimentoTemServicosHabilitados(idEstabelecimento);
                ativacao.PossuiBeneficiosJaConfigurados = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.EstabelecimentoJaPossuiBeneficiosConfigurados(idEstabelecimento);
            }
            else
            {
                ativacao.JaSolicitouAtivacao = false;
                ativacao.AtivadoComSucesso = false;
                ativacao.AtivacaoEstaEmAnalise = false;
            }

            return ativacao;
        }

        private SituacoesDosDocumentosDTO ObterSituacaoDosDocumentosDoRecebedor(int idRecebedor)
        {
            List<Pagamentos.DocumentoDeRecebedorCredenciado> documentosCarregados = Domain.Pagamentos.RecebedorApplicationService.ObterDocumentosDoRecebedor(idRecebedor);
            return new SituacoesDosDocumentosDTO()
            {
                DocumentoEmpresaCarregadoComSucesso = documentosCarregados.Any(d => d.Categoria == Enums.TipoDocumentoCredenciamentoEnum.IdentificacaoDaEmpresa.ToString()),
                DocumentoProprietarioCarregadoComSucesso = documentosCarregados.Any(d => d.Categoria == Enums.TipoDocumentoCredenciamentoEnum.IdentificacaoDoProprietario.ToString()),
                DocumentoResidenciaCarregadoComSucesso = documentosCarregados.Any(d => d.Categoria == Enums.TipoDocumentoCredenciamentoEnum.ComprovanteDeResidencia.ToString())
            };
        }

        public DateTime ObterDataHoraTransacaoCorreta(DateTime? dataHoraPagamento)
        {
            DateTime dataHoraCorreta = Calendario.Agora();
            if (Calendario.ExisteConfiguracaoDefinida() && dataHoraPagamento.HasValue)
            {
                dataHoraCorreta = dataHoraPagamento.Value.Date == Calendario.Hoje()
                    ? dataHoraPagamento.Value.Date.AddHours(Calendario.Agora().Hour)
                        .AddMinutes(Calendario.Agora().Minute)
                        .AddSeconds(Calendario.Agora().Second)
                    : dataHoraPagamento.Value;
            }
            else if (!Calendario.ExisteConfiguracaoDefinida() && dataHoraPagamento.HasValue)
                dataHoraCorreta = dataHoraPagamento.Value;

            return dataHoraCorreta;
        }

        public PagamentoOnlineNoTrinks GerarNovoPagamentoOnlineNoTrinks(int idPagamento, decimal valor, int idEstabelecimento)
        {

            var taxas = ObterTaxasConfiguradasParaEstabelecimento(idEstabelecimento);

            var pagamentoOnline = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Factory.CreateParaRealizacaoDePagamento(
            idPagamentoOnline: idPagamento,
            primeiroPagamentoDoCliente: false,
            parametrosDeRecebimentos: new ParametrosDeRecebimentoNoTrinksParaTransacao(taxas.Parametros));

            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.SaveNew(pagamentoOnline);

            return pagamentoOnline;
        }

        public PagamentoOnlineNoTrinks GerarPagamentoOnlineNoTrinks(int idClienteEstabelecimento, int idPagamentoOnline, decimal valor)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            var primeiraVisitaNoEstabelecimentoEVeioPeloTrinks = ClienteEstahEmSuaPrimeiraVisitaNoEstabelecimentoEVeioPeloTrinks(0, clienteEstabelecimento.Estabelecimento.IdEstabelecimento);
            var parametrosDeRecebimento = ObterTaxasConfiguradasParaEstabelecimento(clienteEstabelecimento.Estabelecimento.IdEstabelecimento);

            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Factory.CreateParaRealizacaoDePagamento(
                idPagamentoOnline: idPagamentoOnline,
                primeiroPagamentoDoCliente: primeiraVisitaNoEstabelecimentoEVeioPeloTrinks,
                parametrosDeRecebimentos: new ParametrosDeRecebimentoNoTrinksParaTransacao(parametrosDeRecebimento.Parametros));

            pagamentoOnlineNoTrinks.ValorTotal = valor;

            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.SaveNew(pagamentoOnlineNoTrinks);

            return pagamentoOnlineNoTrinks;
        }

        public bool EstabelecimentoTemAdiantamentoDeVisibilidadeDaAbaDePagamentoOnline(int idEstabelecimento)
        {
            var estabelecimento = Domain.PagamentosOnlineNoTrinks.ConfiguracaoAdiantamentoVisibilidadeAbaPagamentoOnlineRepository.ObterPorIdEstabelecimento(idEstabelecimento);

            if (estabelecimento == null)
                return false;

            return estabelecimento.TemAdiantamentoAtivo();
        }

        public void InativarEstabelecimentoRecebedor(EstabelecimentoRecebedor estabelecimentoRecebedor)
        {
            estabelecimentoRecebedor.Inativar();
            Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Update(estabelecimentoRecebedor);
        }

        public void ApenasDesativarRecebedorAtual(int idEstabelecimento, GatewayEnum gateway)
        {
            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterPorEstabelecimentoEGateway(idEstabelecimento, gateway);

            if (estabelecimentoRecebedor == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não possui recebedor cadastrado no gateway escolhido.");
                return;
            }

            if (gateway == GatewayEnum.PagarMe || gateway == GatewayEnum.PagarMeV5)
                ValidarSePossuiAssinaturaDoClubePorLinkAtiva(idEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return;

            InativarEstabelecimentoRecebedor(estabelecimentoRecebedor);
        }

        public void ReativarEstabelecimentoRecebedor(int idEstabelecimento, GatewayEnum gateway)
        {
            var todosRecebedoresAssociados = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ListarComGatewayPorEstabelecimento(idEstabelecimento);

            var estabelecimentoRecebedor = todosRecebedoresAssociados.Where(x => x.Gateway == gateway).Select(x => x.Recebedor).FirstOrDefault();

            if (estabelecimentoRecebedor == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Estabelecimento não possui recebedor cadastrado no gateway escolhido.");
                return;
            }

            if (estabelecimentoRecebedor.Ativo)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não existe recebedor para reativar.");
                return;
            }

            if (gateway == GatewayEnum.PagarMe || gateway == GatewayEnum.PagarMeV5)
                ValidarSePossuiAssinaturaDoClubePorLinkAtiva(idEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return;

            var recebedoresAInativar = todosRecebedoresAssociados.Where(x => x.Gateway != gateway).Select(x => x.Recebedor).ToList();
            foreach (var recebedorParaInativar in recebedoresAInativar)
                InativarEstabelecimentoRecebedor(recebedorParaInativar);

            estabelecimentoRecebedor.Ativar();

            Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.Update(estabelecimentoRecebedor);
        }

        private void ValidarSePossuiAssinaturaDoClubePorLinkAtiva(int idEstabelecimento)
        {
            var estabelecimentoPossuiAssinaturaDeLinkAtiva = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.EstabelecimentoPossuiAssinaturaPorLinkAtiva(idEstabelecimento);

            if (estabelecimentoPossuiAssinaturaDeLinkAtiva)
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível desabilitar o Pagar.me pois o estabelecimento possui assinaturas com recorrências ativas.");
        }

        public async Task<NovoQrCodeDTO> ObterQrCode(int idEstabelecimento)
        {
            var qrCode = await Domain.Pagamentos.RecebedorApplicationService.ObterQrCode(idEstabelecimento);

            if (!ValidationHelper.Instance.IsValid)
                return null;

            return qrCode;
        }

        private bool DeveHabilitarEtapaKycPagarme()
        {
            var valorString = new ParametrosTrinks<string>(ParametrosTrinksEnum.pgtoOnline_pagarme_habilitar_kyc).ObterValor();

            bool habilitarKyc = true;

            if (Int32.TryParse(valorString, out int valorInt))
            {
                habilitarKyc = valorInt.Equals(1);
            }

            return habilitarKyc;
        }

        public DadosTelaCadastroPagarmeDTO ObterDadosTelaCadastroPagarme(Estabelecimento estabelecimento)
        {
            var telefoneSuporte = new ParametrosTrinks<string>(ParametrosTrinksEnum.numero_de_contato_do_suporte).ObterValor();

            bool deveHabilitarKyc = DeveHabilitarEtapaKycPagarme();

            var gateway = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterGatewayAtivoDoEstabelecimento(estabelecimento.IdEstabelecimento);

            var dadosAtivacao = ObterDadosParaAtivacaoDeRecebedor(estabelecimento, gateway);

            var endereco = ObterEnderecoParaSalvarNoCredenciamentoDoRecebedor(estabelecimento);

            var instituicoesBancarias = ObterListaDeInstituicoesBancarias();

            var statusConta = Domain.Pagamentos.RecebedorApplicationService.ObterEtapaAtualCadastro(estabelecimento.IdEstabelecimento);

            var dados = new DadosTelaCadastroPagarmeDTO(estabelecimento, telefoneSuporte, deveHabilitarKyc, dadosAtivacao, endereco, instituicoesBancarias, statusConta);

            return dados;
        }

        public DadosTelaConfiguracoesVendaOnlineDTO ObterDadosTelaConfiguracoesVendaOnline(Estabelecimento estabelecimento)
        {
            var requerUploadDeDocumentos = Domain.Pagamentos.PagamentosService.GatewayAtivoDoRecebedorRequerUploadDeDocumentos(estabelecimento.IdEstabelecimento);

            var pagamentoOnlineAtivo = PagamentoOnlineEstahAtivo(estabelecimento.IdEstabelecimento);

            var linkDePagamentoAtivo = LinkDePagamentoEstahAtivo(estabelecimento.IdEstabelecimento);

            bool linkDePagamentoEstaDisponivelParaSerConfigurado = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
               estabelecimento, Recurso.LinkDePagamento).EstaDisponivel;

            var dadosAtivacaoRecebedor = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterDadosDaAtivacaoDoEstabelecimentoComoRecebedor(estabelecimento.IdEstabelecimento);

            bool estabelecimentoEstaHabilitadoParaPagamentoOnlineNoGatewayOuAindaNaoRealizouCadastro = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstabelecimentoEstaHabilitadoParaPagamentoOnlineNoGatewayOuAindaNaoRealizouCadastro(estabelecimento.IdEstabelecimento);

            var dadosRecebedor = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterDadosDeRecebedor(estabelecimento.IdEstabelecimento);

            var telefoneSuporte = new ParametrosTrinks<string>(ParametrosTrinksEnum.numero_de_contato_do_suporte).ObterValor();

            var dados = new DadosTelaConfiguracoesVendaOnlineDTO(requerUploadDeDocumentos, pagamentoOnlineAtivo, linkDePagamentoAtivo, linkDePagamentoEstaDisponivelParaSerConfigurado, dadosAtivacaoRecebedor, estabelecimentoEstaHabilitadoParaPagamentoOnlineNoGatewayOuAindaNaoRealizouCadastro, dadosRecebedor, telefoneSuporte);

            return dados;
        }

        private bool PagamentoOnlineEstahAtivo(int idEstabelecimento)
        {
            var formaDePagamentoAtiva =
              Domain.Pessoas.EstabelecimentoFormaPagamentoService.FormaDePagamentoEstahAtivaParaEstabelecimento(idEstabelecimento, FormaPagamentoEnum.PagamentoOnline);

            var estabelecimentoEstahCredenciado = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstabelecimentoEstaRegistradoComoRecebedor(
                idEstabelecimento, OrigemDeCadastroEnum.Zoop);

            return formaDePagamentoAtiva && estabelecimentoEstahCredenciado;
        }

        private bool LinkDePagamentoEstahAtivo(int idEstabelecimento)
        {
            var formaDePagamentoAtiva =
               Domain.Pessoas.EstabelecimentoFormaPagamentoService.FormaDePagamentoEstahAtivaParaEstabelecimento(idEstabelecimento, FormaPagamentoEnum.PagamentoOnlinePorLink);

            if (!formaDePagamentoAtiva)
                return false;

            return Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                    .ExisteEstabelecimentoAtivoEHabilitadoNosGateways(idEstabelecimento, GatewayEnum.PagarMe, GatewayEnum.PagarMeV5);
        }
    }
}