﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.Services
{
    public interface IPagamentoOnlineNoTrinksService : IService
    {

        bool EstabelecimentoEstaElegivelAoPagamentoOnline(Estabelecimento estabelecimento);

        bool EstabelecimentoEUsuarioTemAcessoAFuncionalidadePagamentoOnline(Estabelecimento estabelecimento);

        bool EstabelecimentoEstaElegivelEhAtivadoNoPagamentoOnline(Estabelecimento estabelecimento);

        int NumeroMinimoDeMesesComAssinaturaPagaParaAtivarPagamentoOnline { get; }

        Task AtualizarStatusDeRecebedor(string idRecebedorCredenciado, GatewayEnum gateway);

        void AtualizarStatusRecebedorGateway(AtualizarStatusRecebedorGatewayDTO dto);

        Task<DTO.DadosDoPagamentoRealizadoDTO> RealizarPagamentoOnlineNoEstabelecimento(int idEstabelecimento, NovoPagamentoOnlineDTO dadosDoPagamento);
        PagamentoOnlineNoTrinks GerarNovoPagamentoOnlineNoTrinks(int idPagamento, decimal valor, int idEstabelecimento);
        PagamentoOnlineNoTrinks GerarPagamentoOnlineNoTrinks(int idClienteEstabelecimento, int idPagamentoOnline, decimal valor);

        DadosParaAtivacaoDTO ObterDadosParaAtivacaoDeRecebedor(Estabelecimento estabelecimento);
        DadosParaAtivacaoDTO ObterDadosParaAtivacaoDeRecebedor(Estabelecimento estabelecimento, GatewayEnum gateway);

        ParametrosDeRecebimentoNoTrinksParaConfiguracao ObterTaxasConfiguradasParaEstabelecimento(int idEstabelecimento, int? numeroParcela = 1);

        ParametrosDeRecebimentoNoTrinksParaConfiguracao ObterTaxasConfiguradasParaEstabelecimentoPorOrigem(int idEstabelecimento, OrigemDeCadastroEnum origem, int? numeroParcela = 1);

        ParametrosDeRecebimentoNoTrinks ObterTaxasDoGateway(GatewayEnum gatewayAtivo, int numeroParcela = 1);

        DTO.AtivacaoDoRecebedor ObterDadosDaAtivacaoDoEstabelecimentoComoRecebedor(int idEstabelecimento);

        bool EstabelecimentoEstaConfiguradoComoRecebedor(int idEstabelecimento);

        bool EstabelecimentoEstaHabilitadoParaPagamentoOnline(int idEstabelecimento);

        bool EstabelecimentoEstaHabilitadoParaPagamentoOnlineNoGatewayOuAindaNaoRealizouCadastro(int idEstabelecimento);

        bool EstabelecimentoPossuiPagamentoOnlineAtivado(int idEstabelecimento);

        Task CadastrarContaBancaria(int idEstabelecimento, CadastrarContaBancariaDTO dadosDaNovaConta);

        Task<bool> ConfigurarEstabelecimentoComoRecebedor(ConfigurarEstabelecimentoRecebedorDTO dtoEstabelecimentoRecebedor);

        void MarcarEstabelecimentoRecebedorComoNegado(int idEstabelecimento);

        EstabelecimentoRecebedor ObterEstabelecimentoRecebedorAtivo(int idEstabelecimento);

        void AssociarEstabelecimentoAFormaDePagamentoOnline(int idEstabelecimento);

        bool StatusPermitePagamentoOnline(StatusHorarioEnum status);

        UltimaTransacaoRealizadaDTO ObterDadosDaUltimaTransacaoRealizadaPorConta(int idConta);

        List<KeyValuePair<int, string>> ObterListaDeTipoContaBancarias();

        List<KeyValuePair<int, string>> ObterListaDeInstituicoesBancarias();

        string ObterNomeDaInstituicaoBancariaPeloCodigo(string codigoDoBanco);
        CadastrarContaBancariaDTO OrganizarDadosCadastroContaBancaria(
            int banco,
            string numeroConta,
            string numeroContaDV,
            int tipoDaConta,
            string cnpjConta,
            string agenciaBancaria,
            string agenciaBancariaDV,
            string nomeTitular);

        Task<decimal> ObterSaldoPendenteDoRecebedorDoEstabelecimento(int idEstabelecimento);

        bool EstabelecimentoEstaRegistradoComoRecebedor(int idEstabelecimento, OrigemDeCadastroEnum origem);

        bool EstabelecimentoEstaRegistradoComoRecebedor(int idEstabelecimento, params OrigemDeCadastroEnum[] origem);

        Task<bool> EstornarPagamentoOnline(PagamentoOnlineNoTrinks pagamentoOnline);

        Task<RetornoDeUploadDoDocumentoDTO> CarregarDocumentoParaCredenciamento(int idEstabelecimento, byte[] arquivo, string nomeDoArquivo, Enums.TipoDocumentoCredenciamentoEnum tipoDocumento);

        List<SplitPagamentoDTO> CalcularSplitDoPagamentoOnline(EstabelecimentoRecebedor estabelecimentoRecebedor,
            int idConta, decimal valorTotalTransacao, MetodoDePagamentoNoGatewayEnum metodoDePagamento, out bool primeiraVisitaNoEstabelecimentoEVeioPeloTrinks,
            out ParametrosDeRecebimentoNoTrinksParaConfiguracao taxasACobrarDaTransacao, int? numeroParcela = 1);

        DateTime ObterDataHoraTransacaoCorreta(DateTime? dataHoraPagamento);
        bool ClienteEstahEmSuaPrimeiraVisitaNoEstabelecimentoEVeioPeloTrinks(int idConta, int idEstabelecimento);
        bool EstabelecimentoTemAdiantamentoDeVisibilidadeDaAbaDePagamentoOnline(int idEstabelecimento);
        DadosDeRecebedorDTO ObterDadosDeRecebedor(int idEstabelecimento);
        void InativarEstabelecimentoRecebedor(EstabelecimentoRecebedor estabelecimentoRecebedor);
        void ReativarEstabelecimentoRecebedor(int idEstabelecimento, GatewayEnum gateway);
        void ApenasDesativarRecebedorAtual(int idEstabelecimento, GatewayEnum gateway);
        void DesativarFuncionalidadesDoGatewayInativado(int idEstabelecimento, GatewayEnum gateway);
        void AtualizarFormasDePagamento(int idEstabelecimento, GatewayEnum gateway);
        ParametrosDeRecebimentoNoTrinks ObterParametrosDeRecebimentoNoTrinksPorTaxas(int numeroParcela, Pagamentos.Config.IConfiguracoesGateway taxasPadrao);
        EnderecoParaCadastrarDTO ObterEnderecoParaSalvarNoCredenciamentoDoRecebedor(Estabelecimento estabelecimento);
        TaxasDoGateway ObterTaxasGatewayParaSimulacao(FiltroObterTaxasParaSimulacao filtro);
        Task<NovoQrCodeDTO> ObterQrCode(int idEstabelecimento);
        DadosTelaCadastroPagarmeDTO ObterDadosTelaCadastroPagarme(Estabelecimento estabelecimento);
        DadosTelaConfiguracoesVendaOnlineDTO ObterDadosTelaConfiguracoesVendaOnline(Estabelecimento estabelecimento);
        ParametrosDeRecebimentoNoTrinksParaConfiguracao ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(
            int idEstabelecimento, MetodoDePagamentoNoGatewayEnum metodoDePagamento, int? numeroParcela = 1);
    }
}
