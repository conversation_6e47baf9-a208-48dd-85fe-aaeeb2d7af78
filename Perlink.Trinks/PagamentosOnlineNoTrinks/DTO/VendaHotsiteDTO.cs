using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pagamentos;
using System.Collections.Generic;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.DTO
{
    public class VendaHotsiteDTO
    {
        public DadosDoCartaoDTO Cartao { get; set; }

        public Pagamento Pagamento { get; set; }

        public CompradorDTO Comprador { get; set; }

        public List<ItemParaVendaDTO> Itens { get; set; }

        public int IdEstabelecimento { get; set; }

        public int NumeroParcelas { get; set; }
        public string Nome { get; set; }
        public string Tipo { get; set; }
    }

    public class ItemParaVendaDTO
    {
        public decimal ValorUnitario { get; set; }

        public int Quantidade { get; set; }

        public int IdReferenciaItem { get; set; }

        public TipoItemEnum TipoItem { get; set; }
    }

    public enum TipoItemEnum
    {
        pacote = 1
    }

    public class DadosDoCartaoDTO
    {
        public string CVV { get; set; }
        public string Validade { get; set; }
        public string Nome { get; set; }
        public string Numero { get; set; }
    }

    public class CompradorDTO
    {
        public EnderecoDTO Endereco { get; set; }
        public string Nome { get; set; }
        public string Email { get; set; }
        public string Telefone { get; set; }
        public string CpfCnpj { get; set; }
    }

    public class EnderecoDTO
    {
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string CEP { get; set; }
        public string Estado { get; set; }
        public string Cidade { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
    }
}
