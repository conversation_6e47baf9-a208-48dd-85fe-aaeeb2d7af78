﻿using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pagamentos;
using Perlink.Trinks.Pessoas;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks.DTO
{
    public class PagamentoAntecipadoHotsiteDto
    {
        public Pagamento Pagamento { get; set; }
        public int IdEstabelecimento { get; set; }
        public int NumeroParcelas { get; set; }
        public PessoaFisica PessoaQuePagou { get; set; }
        public PessoaJuridica PessoaQueRecebeu { get; set; }
        public decimal ValorPago { get; set; }
        public int IdReferencia { get; set; }
        public string Nome { get; set; }
        public string Tipo { get; set; }
    }
}