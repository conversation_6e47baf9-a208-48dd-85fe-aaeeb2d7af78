﻿using Castle.ActiveRecord;
using Perlink.Pagamentos.Gateways.Enums;
using System;

namespace Perlink.Trinks.PagamentosOnlineNoTrinks
{

    [ActiveRecord("PGONT_Taxas_Do_Estabelecimento", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    public partial class TaxasDoEstabelecimento : ActiveRecordBase<TaxasDoEstabelecimento>
    {

        public TaxasDoEstabelecimento()
        {
            DataCriacao = Calendario.Agora();
            MetodoDePagamento = MetodoDePagamentoNoGatewayEnum.CartaoDeCredito;
        }

        public TaxasDoEstabelecimento(int idEstabelecimentoRecebedor, int idEstabelecimento, ParametrosDeRecebimentoNoTrinks taxas) : this()
        {
            DataCriacao = Calendario.Agora();
            IdEstabelecimentoRecebedor = idEstabelecimentoRecebedor;
            IdEstabelecimento = idEstabelecimento;
            Taxas = taxas;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id")]
        public virtual int Id { get; set; }

        [Property("id_estabelecimento")]
        public virtual int IdEstabelecimento { get; set; }

        [Property("id_estabelecimento_recebedor")]
        public virtual int IdEstabelecimentoRecebedor { get; set; }

        [Nested]
        public virtual ParametrosDeRecebimentoNoTrinks Taxas { get; set; }

        [Property("dt_criacao")]
        public virtual DateTime DataCriacao { get; set; }

        [Property("dt_ultima_atualizacao", NotNull = false)]
        public virtual DateTime? DataUltimaAtualizacao { get; set; }
        
        [Property("metodo_pagamento")]
        public virtual MetodoDePagamentoNoGatewayEnum MetodoDePagamento { get; set; }
    }
}