﻿using System.Collections.Generic;

namespace Perlink.Trinks.LGPD.Helpers
{

    public static class OcultarDadosHelper
    {
        public static string CPF(string cpf)
        {
            return Domain.OcultarDadosService.CPF(cpf);
        }

        public static string Email(string email)
        {
            return Domain.OcultarDadosService.Email(email);
        }

        public static string Telefone(string telefone)
        {
            return Domain.OcultarDadosService.Telefone(telefone);
        }

        public static List<KeyValuePair<string, bool>> OcultarTelefonesCelulares(List<KeyValuePair<string, bool>> telefones)
        {
            var resultado = new List<KeyValuePair<string, bool>>();

            foreach (var tel in telefones)
            {
                var telefone = Telefone(tel.Key);
                var ehCelular = !telefone.Contains("*") && tel.Value;

                resultado.Add(new KeyValuePair<string, bool>(telefone, ehCelular));
            }

            return resultado;
        }
    }
}