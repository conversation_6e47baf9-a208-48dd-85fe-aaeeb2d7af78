﻿using System.Linq;
using System.Text.RegularExpressions;
using System.Web;

namespace Perlink.Trinks.LGPD.Helpers
{

    public class OcultarDadosService
    {
        private bool? estaEmAmbienteProtegido;
        private bool? podeVerCPF;
        private bool? podeVerEmail;
        private bool? podeVerTelefone;

        public bool EstaEmAmbienteProtegido => estaEmAmbienteProtegido ?? (estaEmAmbienteProtegido = EstaEmContextoProtegido(HttpContext.Current?.Request.Url.AbsoluteUri)).Value;

        public bool PodeVerCPF => podeVerCPF ?? (podeVerCPF = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.LGPD.Ver_Cpf)).Value;

        public bool PodeVerEmail => podeVerEmail ?? (podeVerEmail = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.LGPD.Ver_Email)).Value;

        public bool PodeVerTelefone => podeVerTelefone ?? (podeVerTelefone = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.LGPD.Ver_Telefone)).Value;

        public string CPF(string cpf)
        {
            if (string.IsNullOrWhiteSpace(cpf) || !EstaEmAmbienteProtegido || PodeVerCPF)
                return cpf;
            return Regex.Replace(cpf, "[0-9]", "*");
        }

        public string Email(string email)
        {
            if (string.IsNullOrWhiteSpace(email) || !EstaEmAmbienteProtegido || PodeVerEmail)
                return email;
            return Regex.Replace(email, "[0-9a-zA-Z]", "*");
        }

        public string Telefone(string telefone)
        {
            if (string.IsNullOrWhiteSpace(telefone) || !EstaEmAmbienteProtegido || PodeVerTelefone)
                return telefone;

            return Regex.Replace(telefone, "[0-9]", "*");
        }

        private bool EstaEmContextoProtegido(string url)
        {
            var partesURL = new[] { "/Cliente/HistoricoClientePopupPeloCodigoClienteEstabelecimento", "/Cliente/HistoricoClientePopupPeloManterAgendamento", "/backoffice/", "/proapi/", "/minhaarea/" };

            return HttpContext.Current != null &&
                partesURL.Any(p => url.IndexOf(p, System.StringComparison.OrdinalIgnoreCase) >= 0);
        }
    }
}