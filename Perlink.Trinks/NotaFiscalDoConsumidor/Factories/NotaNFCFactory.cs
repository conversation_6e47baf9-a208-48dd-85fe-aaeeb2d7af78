﻿using Perlink.Trinks.NotaFiscalDoConsumidor.DTO;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.Pacotes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.NotaFiscalDoConsumidor.Factories
{

    public partial class NotaNFCFactory : INotaNFCFactory
    {

        public virtual NotaNFC Create(Financeiro.Transacao transacao)
        {
            var nota = new NotaNFC();
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            var configuracaoNFC = estabelecimento.ConfiguracaoDeNFC;

            var incluiServicos = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.CidadeEmiteServicoPorNFCe(transacao.PessoaQueRecebeu);

            nota.Transacao = transacao;
            nota.ClienteEstabelecimento = transacao.ClienteEstabelecimento();
            nota.DataVenda = transacao.DataHora;
            nota.Estabelecimento = estabelecimento;
            nota.EstabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(transacao.PessoaQueRealizou.IdPessoaFisica, nota.Estabelecimento.IdEstabelecimento);
            nota.StatusNota = new StatusNotaNFC { IdStatusNotaNFC = 1 };
            nota.TotalBruto = transacao.TotalPagar.HasValue ? transacao.TotalPagar.Value : 0;
            nota.TotalDesconto = transacao.TotalDescontoOperadoras;
            nota.TotalLiquido = transacao.TotalPago.HasValue ? transacao.TotalPago.Value : 0;

            nota.NFCProducao = configuracaoNFC.NFCProducaoLevandoEmContaParametro();

            nota.Itens = new List<NotaItensNFC>();
            int numeroSequencialDoItemDaNota = 0;

            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);
            if (venda != null)
            {
                var itensVenda = venda.ItensVenda.Where(f => f.ValorFinal() > 0 || (f.ItemPacoteCliente != null && f.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo));
                numeroSequencialDoItemDaNota = AdicionarItensVenda(nota, incluiServicos, numeroSequencialDoItemDaNota, itensVenda);
            }

            if (incluiServicos)
            {
                IList<HorarioTransacao> horariosTransacoes = transacao.HorariosTransacoes;
                numeroSequencialDoItemDaNota = AdicionarHorariosTransacao(nota, numeroSequencialDoItemDaNota, horariosTransacoes);
            }

            nota.TotalBruto = Math.Round(nota.Itens.Sum(a => a.ValorBrutoTotal), 2);
            nota.TotalLiquido = Math.Round(nota.Itens.Sum(a => a.ValorLiquidoTotal), 2);
            nota.TotalDesconto = Math.Round(nota.Itens.Sum(a => a.Desconto), 2);

            var formasDePagamentoElegiveis = transacao.FormasPagamento
                .Where(a => a.FormaPagamento.IdFormaPagamentoNFe.HasValue && a.FormaPagamento.IdFormaPagamentoNFe.Value > 0)
                .Select(a => new FormaPagamentoElegivelNfDTO(a.ValorPago, a.FormaPagamento.IdFormaPagamentoNFe.Value)).ToList();

            var formasDePagamentoParaConsumoDePacote = AdicionarConsumoDePacoteCasoNecessario(venda, transacao.HorariosTransacoes);

            if (formasDePagamentoParaConsumoDePacote.Count > 0)
                formasDePagamentoElegiveis = formasDePagamentoElegiveis.Concat(formasDePagamentoParaConsumoDePacote).ToList();

            var percentualDoPagamentoASerConsiderado = incluiServicos ? 100 : (nota.TotalLiquido / formasDePagamentoElegiveis.Sum(f => f.ValorPago)) * 100;

            AdicionarFormasDePagamento(nota, formasDePagamentoElegiveis, percentualDoPagamentoASerConsiderado);

            return nota;
        }

        private List<FormaPagamentoElegivelNfDTO> AdicionarConsumoDePacoteCasoNecessario(Venda venda, IList<HorarioTransacao> horariosTransacoes)
        {

            var idFormaPagamentoNFe = (int)TipoFormaPagamentoNFCeEnum.fpOutro;

            var retorno = new List<FormaPagamentoElegivelNfDTO>();

            foreach (var itemVenda in venda.ItensVenda)
            {
                if (itemVenda.ItemPacoteCliente != null && itemVenda.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo)
                {
                    var valor = itemVenda.ItemPacoteCliente.ValorUnitarioFiscal;
                    retorno.Add(new FormaPagamentoElegivelNfDTO(valor, idFormaPagamentoNFe));
                }
            }

            foreach (var ht in horariosTransacoes)
            {
                if (ht.ItemPacoteCliente != null && ht.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo)
                {
                    var valor = ht.ItemPacoteCliente.ValorUnitarioFiscal;
                    retorno.Add(new FormaPagamentoElegivelNfDTO(valor, idFormaPagamentoNFe));
                }
            }

            return retorno;

        }

        public virtual int AdicionarItensVenda(NotaNFC nota, bool incluiServicos, int numeroSequencialDoItemDaNota, IEnumerable<ItemVenda> itensVenda)
        {
            foreach (var itemVenda in itensVenda)
            {
                numeroSequencialDoItemDaNota = AdicionaItemVenda(nota, incluiServicos, numeroSequencialDoItemDaNota, itemVenda);
            }

            return numeroSequencialDoItemDaNota;
        }

        private int AdicionarHorariosTransacao(NotaNFC nota, int numeroSequencialDoItemDaNota, IList<HorarioTransacao> horariosTransacoes)
        {
            foreach (var ht in horariosTransacoes)
            {
                numeroSequencialDoItemDaNota = AdicionarHorarioTransacao(nota, numeroSequencialDoItemDaNota, ht);
            }

            return numeroSequencialDoItemDaNota;
        }

        private static void AdicionarFormasDePagamento(NotaNFC nota, IEnumerable<FormaPagamentoElegivelNfDTO> formasDePagamentoElegiveis, decimal percentualDoPagamentoASerConsiderado)
        {
            nota.FormasPagamento = new List<NotaFormaPagamentoNFC>();
            foreach (var formaPagamento in formasDePagamentoElegiveis.GroupBy(a => a.IdFormaPagamentoNFe))
            {
                nota.FormasPagamento.Add(new NotaFormaPagamentoNFC
                {
                    ValorPagamento = Math.Round(Math.Round((percentualDoPagamentoASerConsiderado * formaPagamento.Sum(a => a.ValorPago)) / 100, 3), 2),
                    NotaNFC = nota,
                    FormaPagamento = (TipoFormaPagamentoNFCeEnum)formaPagamento.Key
                });
            }
        }

        private int AdicionarHorarioTransacao(NotaNFC nota, int numeroSequencialDoItemDaNota, HorarioTransacao ht)
        {
            if (ht.ItemPacoteCliente == null && ht.SubTotal() > 0)
            {
                var itemDaNota = CriarItemNotaDeHorarioTransacao(ht, nota, ref numeroSequencialDoItemDaNota);
                nota.Itens.Add(itemDaNota);
                return numeroSequencialDoItemDaNota;
            }

            if (ht.ItemPacoteCliente != null && ht.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo)
            {
                var itemNota = AdicionarHorarioTransacaoParaConsumoDePacote(ht, nota, ref numeroSequencialDoItemDaNota);
                nota.Itens.Add(itemNota);
                return numeroSequencialDoItemDaNota;
            }

            return numeroSequencialDoItemDaNota;
        }

        private int AdicionaItemVenda(NotaNFC nota, bool incluiServicos, int numeroSequencialDoItemDaNota, ItemVenda itemVenda)
        {
            if (itemVenda is ItemVendaProduto)
            {
                numeroSequencialDoItemDaNota = AdicionaItemVendaProduto(nota, numeroSequencialDoItemDaNota, itemVenda);
            }
            else if (itemVenda is ItemVendaPacote && PacoteClienteTemProduto(((ItemVendaPacote)itemVenda).PacoteCliente))
            {
                numeroSequencialDoItemDaNota = AdicionarItensPacoteClienteDePacoteComProduto(nota, incluiServicos, numeroSequencialDoItemDaNota, itemVenda);
            }
            else if (itemVenda is ItemVendaPacote && incluiServicos && PacoteClienteTemServico(((ItemVendaPacote)itemVenda).PacoteCliente))
            {
                numeroSequencialDoItemDaNota = AdicionarItensPacoteClienteDePacoteSoComServico(nota, numeroSequencialDoItemDaNota, itemVenda);
            }

            return numeroSequencialDoItemDaNota;
        }

        private static int AdicionarItensPacoteClienteDePacoteSoComServico(NotaNFC nota, int numeroSequencialDoItemDaNota, ItemVenda itemVenda)
        {
            var itemVendaPacote = (ItemVendaPacote)itemVenda;

            if (itemVendaPacote.PacoteCliente.ConfiguracaoNF != Pacotes.Enums.TipoNFPacoteEnum.Venda)
                return numeroSequencialDoItemDaNota;

            var valorCashback = Domain.Cashback.CashbackItemVendaRepository.ObterValorCashbackDoItemVenda(itemVendaPacote.Id);
            var percentualDesconto = (itemVendaPacote.Desconto + valorCashback) / itemVendaPacote.SubTotal;

            foreach (var item in itemVendaPacote.PacoteCliente.ItensPacoteCliente)
            {
                var objetoComCarregamentoForcado = Domain.Pacotes.ItemPacoteClienteRepository.Load(item.Id);
                if (objetoComCarregamentoForcado.ValorUnitario > 0)
                { // Não adicionar produto com valor zero
                    var itemDaNota = CriarItemNotaDeItemPacoteCliente(objetoComCarregamentoForcado, nota, percentualDesconto, ref numeroSequencialDoItemDaNota);
                    nota.Itens.Add(itemDaNota);
                }
            }

            return numeroSequencialDoItemDaNota;
        }

        private static int AdicionarItensPacoteClienteDePacoteComProduto(NotaNFC nota, bool incluiServicos, int numeroSequencialDoItemDaNota, ItemVenda itemVenda)
        {
            var itemVendaPacote = (ItemVendaPacote)itemVenda;

            if (itemVendaPacote.PacoteCliente.ConfiguracaoNF != Pacotes.Enums.TipoNFPacoteEnum.Venda)
                return numeroSequencialDoItemDaNota;

            var valorCashback = Domain.Cashback.CashbackItemVendaRepository.ObterValorCashbackDoItemVenda(itemVendaPacote.Id);
            var descontoCashback = valorCashback;
            var percentualDesconto = (itemVendaPacote.Desconto + descontoCashback) / itemVendaPacote.SubTotal;

            foreach (var item in itemVendaPacote.PacoteCliente.ItensPacoteCliente)
            {
                var objetoComCarregamentoForcado = Domain.Pacotes.ItemPacoteClienteRepository.Load(item.Id);

                if (!incluiServicos && !(objetoComCarregamentoForcado is ItemPacoteClienteProduto))
                    continue;

                if (objetoComCarregamentoForcado.ValorUnitario > 0 || objetoComCarregamentoForcado is ItemPacoteClienteProduto)
                {
                    var itemDaNota = CriarItemNotaDeItemPacoteCliente(objetoComCarregamentoForcado, nota, percentualDesconto, ref numeroSequencialDoItemDaNota);
                    nota.Itens.Add(itemDaNota);
                }
            }

            return numeroSequencialDoItemDaNota;
        }

        private int AdicionaItemVendaProduto(NotaNFC nota, int numeroSequencialDoItemDaNota, ItemVenda itemVenda)
        {
            var itemVendaProduto = (ItemVendaProduto)itemVenda;

            if (itemVenda.ItemPacoteCliente != null && itemVenda.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo)
            {
                var itemNotaConsumoProdutoPacote = AdicionarItemVendaProdutoComConsumoDePacote(nota, ref numeroSequencialDoItemDaNota, itemVendaProduto);
                nota.Itens.Add(itemNotaConsumoProdutoPacote);
                return numeroSequencialDoItemDaNota;
            }

            if (itemVenda.ItemPacoteCliente != null && itemVenda.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Venda)
            {
                return numeroSequencialDoItemDaNota;
            }

            var itemDaNota = CriarItemNotaDeItemVendaProduto(itemVendaProduto, nota, ref numeroSequencialDoItemDaNota);

            nota.Itens.Add(itemDaNota);
            return numeroSequencialDoItemDaNota;
        }

        private NotaItensNFC AdicionarItemVendaProdutoComConsumoDePacote(NotaNFC nota, ref int numeroSequencialDoItemDaNota, ItemVendaProduto itemVenda)
        {
            var itemDaNota = new NotaItensNFC();

            itemDaNota.EstabelecimentoProduto = itemVenda.EstabelecimentoProduto;

            NaoPermitirEmissaoCasoOProdutoNaoTenhaSituacaoTributaria(itemDaNota);

            var quantidade = ObterQuantidadeConsiderandoUnidadeFracao(itemVenda);

            var valorUnitarioProporcionalDoItemNoPacote = itemVenda.ItemPacoteCliente.ValorUnitarioFiscal;
            var valorTotal = valorUnitarioProporcionalDoItemNoPacote * quantidade;
            var descontoUnitario = 0;

            itemDaNota.NotaNFC = nota;
            itemDaNota.PrecoUnitario = valorUnitarioProporcionalDoItemNoPacote;
            itemDaNota.SequencialProduto = ++numeroSequencialDoItemDaNota;
            itemDaNota.ValorBrutoTotal = valorTotal;
            itemDaNota.ValorLiquidoTotal = valorTotal;
            itemDaNota.DescontoUnitario = descontoUnitario;

            itemDaNota.Quantidade = quantidade;
            return itemDaNota;

        }

        private NotaItensNFC CriarItemNotaDeHorarioTransacao(HorarioTransacao ht, NotaNFC nota, ref int numeroSequencialDoItemDaNota)
        {
            var itemDaNota = new NotaItensNFC();
            var cashback = Domain.Cashback.CashbackHorarioTransacaoRepository.ObterValorCashbackDoHorarioTransacao(ht.Codigo);
            var descontoUnitarioCashback = cashback;

            itemDaNota.ServicoEstabelecimento = ht.Horario.ServicoEstabelecimento;

            itemDaNota.NotaNFC = nota;
            itemDaNota.PrecoUnitario = (ht.SubTotal() ?? 0) + descontoUnitarioCashback;
            itemDaNota.SequencialProduto = ++numeroSequencialDoItemDaNota;
            itemDaNota.ValorBrutoTotal = ht.Preco ?? 0;
            itemDaNota.ValorLiquidoTotal = (ht.SubTotal() ?? 0) + descontoUnitarioCashback;
            itemDaNota.DescontoUnitario = (ht.Desconto ?? 0) + descontoUnitarioCashback;
            itemDaNota.CodigoHorarioTransacao = ht.Codigo;

            itemDaNota.Quantidade = 1;
            return itemDaNota;
        }

        private NotaItensNFC AdicionarHorarioTransacaoParaConsumoDePacote(HorarioTransacao ht, NotaNFC nota, ref int numeroSequencialDoItemDaNota)
        {
            var itemDaNota = new NotaItensNFC();

            var valorUnitarioProporcionalDoItemNoPacote = ht.ItemPacoteCliente.ValorUnitarioFiscal;
            var valorTotal = valorUnitarioProporcionalDoItemNoPacote;
            var descontoUnitario = 0;

            itemDaNota.ServicoEstabelecimento = ht.Horario.ServicoEstabelecimento;

            itemDaNota.NotaNFC = nota;
            itemDaNota.PrecoUnitario = valorTotal;
            itemDaNota.SequencialProduto = ++numeroSequencialDoItemDaNota;
            itemDaNota.ValorBrutoTotal = valorTotal;
            itemDaNota.ValorLiquidoTotal = valorTotal;
            itemDaNota.DescontoUnitario = descontoUnitario;
            itemDaNota.CodigoHorarioTransacao = ht.Codigo;

            itemDaNota.Quantidade = 1;
            return itemDaNota;
        }

        private static NotaItensNFC CriarItemNotaDeItemVendaProduto(ItemVendaProduto itemVenda, NotaNFC nota, ref int numeroSequencialDoItemDaNota)
        {
            var itemDaNota = new NotaItensNFC();

            itemDaNota.EstabelecimentoProduto = itemVenda.EstabelecimentoProduto;

            // Obs: Em várias situações tributárias de um produto, é possível realizar a emissão de NFC-e mesmo sem alíquota,
            //      como p. ex. em casos de Substituição Tributária.
            NaoPermitirEmissaoCasoOProdutoNaoTenhaSituacaoTributaria(itemDaNota);

            var quantidade = ObterQuantidadeConsiderandoUnidadeFracao(itemVenda);
            var valorCashback = Domain.Cashback.CashbackItemVendaRepository.ObterValorCashbackDoItemVenda(itemVenda.Id);
            var descontoUnitarioCashback = valorCashback;

            itemDaNota.NotaNFC = nota;
            itemDaNota.PrecoUnitario = (itemVenda.ValorFinal() + descontoUnitarioCashback) / quantidade;
            itemDaNota.SequencialProduto = ++numeroSequencialDoItemDaNota;
            itemDaNota.ValorBrutoTotal = itemVenda.SubTotal;
            itemDaNota.ValorLiquidoTotal = itemVenda.ValorFinal() + descontoUnitarioCashback;
            itemDaNota.DescontoUnitario = (itemVenda.Desconto + descontoUnitarioCashback) / quantidade;

            itemDaNota.Quantidade = quantidade;
            return itemDaNota;
        }

        private static NotaItensNFC CriarItemNotaDeItemPacoteCliente(ItemPacoteCliente itemPacote, NotaNFC nota, decimal percentualDesconto, ref int numeroSequencialDoItemDaNota)
        {
            var itemDaNota = new NotaItensNFC();
            if (itemPacote is ItemPacoteClienteProduto)
            {
                itemDaNota.EstabelecimentoProduto = ((ItemPacoteClienteProduto)itemPacote).EstabelecimentoProduto;

                // Obs: Em várias situações tributárias de um produto, é possível realizar a emissão de NFC-e mesmo sem alíquota,
                //      como p. ex. em casos de Substituição Tributária.
                NaoPermitirEmissaoCasoOProdutoNaoTenhaSituacaoTributaria(itemDaNota);
            }
            else if (itemPacote is ItemPacoteClienteServico)
            {
                itemDaNota.ServicoEstabelecimento = ((ItemPacoteClienteServico)itemPacote).ServicoEstabelecimento;
            }

            int quantidade = itemPacote.Quantidade;
            var valorUnitarioDoItem = itemPacote.ValorUnitario;
            var subtotal = valorUnitarioDoItem * quantidade;

            PacoteCliente pacoteCliente = itemPacote.PacoteCliente;
            var totalItensPacote = pacoteCliente.ValorTotalItens();
            var valorPacote = pacoteCliente.Valor;

            double percentualValorDentroDoPacote = totalItensPacote > 0
                ? (double)subtotal / (double)totalItensPacote
                : (double)1 / (double)pacoteCliente.ItensPacoteCliente.Count(); // Se os itens do pacote somarem zero, os itens terão valor rateado igualmente.

            var valorDoItemRateado = (decimal)((double)valorPacote * percentualValorDentroDoPacote);

            itemDaNota.Quantidade = quantidade;
            itemDaNota.ValorBrutoTotal = valorDoItemRateado;
            itemDaNota.PrecoUnitario = Math.Round(valorDoItemRateado / quantidade, 2);
            itemDaNota.SequencialProduto = ++numeroSequencialDoItemDaNota;
            itemDaNota.DescontoUnitario = Math.Round((valorDoItemRateado * percentualDesconto), 2);
            //itemDaNota.Desconto = itemDaNota.ValorBrutoTotal * percentualDesconto; //Setado automaticamente

            itemDaNota.ValorLiquidoTotal = Math.Round(itemDaNota.ValorBrutoTotal + (itemDaNota.ValorBrutoTotal * percentualDesconto), 2);

            itemDaNota.NotaNFC = nota;
            return itemDaNota;
        }

        private static int ObterQuantidadeConsiderandoUnidadeFracao(ItemVenda itemVenda)
        {
            var quantidade = itemVenda.Quantidade;
            var itemVendaProduto = (ItemVendaProduto)itemVenda;
            if (itemVenda is ItemVendaProduto && itemVendaProduto.TipoDeQuantidade == TipoDeQuantidadeDeProduto.PorUnidade)
                quantidade = Domain.Pessoas.EstabelecimentoProdutoService.ObterQuantidateTotalEmUnidades(quantidade, itemVendaProduto.EstabelecimentoProduto.MedidasPorUnidade);
            return quantidade;
        }

        private static void NaoPermitirEmissaoCasoOProdutoNaoTenhaSituacaoTributaria(NotaItensNFC itemDaNota)
        {
            var exigeAliquotaIcms =
                Domain.Pessoas.EstabelecimentoService.EstabelecimentoExigeAliquotaICMS(
                    ContextHelper.Instance.IdEstabelecimento.Value);
            if (exigeAliquotaIcms && (itemDaNota.EstabelecimentoProduto.IdSituacaoTributaria == null || itemDaNota.EstabelecimentoProduto.IdSituacaoTributaria == 0))
                throw new Exception("Nota não pode ser emitida pois o um ou mais produtos não possuem situação tributária.");
        }

        private bool PacoteClienteTemProduto(PacoteCliente pacoteCliente)
        {
            foreach (var i in pacoteCliente.ItensPacoteCliente)
            {
                var objetoComLoadForcado = Domain.Pacotes.ItemPacoteClienteRepository.Load(i.Id, false);
                if (objetoComLoadForcado is ItemPacoteClienteProduto)
                    return true;
            }
            return false;
        }

        private bool PacoteClienteTemServico(PacoteCliente pacoteCliente)
        {
            foreach (var i in pacoteCliente.ItensPacoteCliente)
            {
                var objetoComLoadForcado = Domain.Pacotes.ItemPacoteClienteRepository.Load(i.Id, false);
                if (objetoComLoadForcado is ItemPacoteClienteServico)
                    return true;
            }
            return false;
        }
    }
}