﻿using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Importacao.Statics;
using Perlink.Trinks.NotaFiscalDoConsumidor.DTO;
using Perlink.Trinks.Pacotes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF
{

    public class GeradorDeDadosParaNotaFiscalSysPDV : GeradorDeDadosParaNotaFiscal
    {

        public GeradorDeDadosParaNotaFiscalSysPDV(NotaNFC notaNFC)
        {
            NotaNFC = notaNFC;
            Estabelecimento = notaNFC.Estabelecimento;
            Transacao = notaNFC.Transacao;
            TipoDeInterface = Enums.TipoDeInterfaceNFC.SysPDV;
        }

        public GeradorDeDadosParaNotaFiscalSysPDV(Transacao transacao)
        {
            Transacao = transacao;
            TipoDeInterface = Enums.TipoDeInterfaceNFC.SysPDV;
        }

        public GeradorDeDadosParaNotaFiscalSysPDV(Estabelecimento estabelecimento)
        {
            Estabelecimento = estabelecimento;
            TipoDeInterface = Enums.TipoDeInterfaceNFC.SysPDV;
        }

        public Estabelecimento Estabelecimento { get; set; }
        public NotaNFC NotaNFC { get; set; }

        public Transacao Transacao { get; set; }

        public override void CancelarNF(string justificativa)
        {
            Domain.NotaFiscalDoConsumidor.NotaNFCService.CancelarNotaNFC(Transacao);
        }

        public override string GerarConteudoDeDadosParaNF()
        {
            throw new NotImplementedException();
        }

        public override object GerarDadosDeAtualizacaoDeIntegracao()
        {
            throw new NotImplementedException();
        }

        public override string GerarIdentificacaoDaGeracao(int numeroDoFechamentoGerado)
        {
            throw new NotImplementedException();
        }

        public override async Task<int> EmitirNotaFiscal()
        {
            NotaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCService.GerarNotaNFC(Transacao);

            if (NotaNFC == null)
                return 0;

            return NotaNFC.NumeroNota;
        }

        public override async Task<string> GerarNFAssinadaDownload()
        {
            throw new NotImplementedException();
        }

        //public override int GerarEGravarNumeroDoFechamento(Estabelecimento estabelecimento) {
        //    throw new NotImplementedException();
        //}
        public override void GravarGeracaoDeNFC(ImpressaoDeNFC impressaoDeNFC)
        {
            throw new NotImplementedException();
        }

        public override void RealizarInutilizacoesPendentes(int quantidadeMaxima = 2)
        {
            throw new NotImplementedException();
        }

        public MemoryStream ObterNFCancelamentoSysPDV()
        {
            if (NotaNFC == null && Transacao != null)
                NotaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(Transacao);

            if (NotaNFC == null)
                throw new Exception("Foi solicitado o cancelamento sem ter nota fiscal preenchida.");

            var sb = new StringBuilder();
            var stream = new MemoryStream { Position = 0 };
            var writer = new StreamWriter(stream, Encoding.Default, 1024);

            PreencherDadosCancelamento(sb, writer);

            writer.Flush();
            if (stream.Length > 0)
                stream.Position = 0;

            return stream;
        }

        public MemoryStream ObterNFSysPDV()
        {
            if (NotaNFC == null && Transacao != null)
                NotaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(Transacao);

            if (NotaNFC == null)
                throw new Exception("Foi solicitado a obtenção de dados sem ter nota fiscal preenchida.");

            var nfcItens = Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.Queryable().Where(p => p.NotaNFC.IdNotaNFC == NotaNFC.IdNotaNFC).ToList();
            if (!nfcItens.Any() && (!Estabelecimento.ConfiguracaoDeNFC.IncluirServicosNaNota ||
                (Estabelecimento.ConfiguracaoDeNFC.IncluirServicosNaNota && !NotaNFC.Transacao.HorariosTransacoes.Any())))
            {
                throw new Exception("Foi solicitado a obtenção de dados de uma nota fiscal sem itens.");
            }

            var sb = new StringBuilder();
            var stream = new MemoryStream { Position = 0 };
            var writer = new StreamWriter(stream, Encoding.Default, 1024);

            // Registro Tipo 01 – Cabeçalho
            PreencherDadosCabecalho(sb, writer);

            //Registros Tipo 02 - Itens Venda
            PreencherDadosItensVenda(sb, writer, nfcItens);

            if (Estabelecimento.ConfiguracaoDeNFC.IncluirServicosNaNota)
            {
                var hts = NotaNFC.Transacao.HorariosTransacoes.Where(f => f.SubTotal() > 0);
                PreencherDadosHorarioTransacao(sb, writer, hts);

                var venda = NotaNFC.Transacao.Vendas.FirstOrDefault();
                if (venda != null)
                {
                    var itensVendaPacote = venda.ItensVenda.Where(f => f is ItemVendaPacote).Cast<ItemVendaPacote>().ToList();

                    var itensPacoteServico = new List<ItemPacoteClienteServico>();
                    var itensPacote = itensVendaPacote.SelectMany(f => f.PacoteCliente.ItensPacoteCliente).ToList();
                    foreach (var item in itensPacote)
                    { // Tive que fazer pois ele não estava detectando o tipo do ItemPacote por causa do tipo proxy do NH
                        var itemCarregado = Domain.Pacotes.ItemPacoteClienteRepository.Load(item.Id);
                        if (itemCarregado.ValorUnitario > 0 && itemCarregado is ItemPacoteClienteServico)
                            itensPacoteServico.Add((ItemPacoteClienteServico)itemCarregado);
                    }
                    PreencherDadosItensPacoteServico(sb, writer, itensPacoteServico);
                }
            }

            writer.Flush();
            if (stream.Length > 0)
                stream.Position = 0;

            return stream;
        }

        protected override decimal ObterAliquotaCOFINSDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override decimal ObterAliquotaICMSDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override decimal ObterAliquotaPISDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(EstabelecimentoProduto estabelecimentoProduto)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(ServicoEstabelecimento servicoEstabelecimento)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(Pacotes.Pacote Pacote)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(Horario horario)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(TransacaoFormaPagamento credito)
        {
            throw new NotImplementedException();
        }

        protected override string ObterCodigoDeBarrasDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override string ObterCodigoFiscalDeOperacaoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override string ObterDescricaoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override bool ObterFabricacaoPropriaDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override int ObterOrigemProdutoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override bool ObterProdutoImportadoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override int ObterSituacaoTributariaDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override string ObterTipoUnidadeDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        private void PreencherDadosCabecalho(StringBuilder sb, StreamWriter writer)
        {
            //campos preenchidos de acordo com o tipo de venda se é para profissional ou cliente
            var idClienteOuProfissional = 0;
            var cpfClienteOuProfissional = string.Empty;
            var nomeCompletoClienteOuProfissional = string.Empty;
            var logradouro = string.Empty;
            var bairro = string.Empty;
            var cidade = string.Empty;
            var siglaUF = string.Empty;
            var numeroEndereco = string.Empty;
            var complemento = string.Empty;
            var telefoneFormatado = string.Empty;

            var incluirServicosNaNota = Estabelecimento.ConfiguracaoDeNFC.IncluirServicosNaNota;
            var clienteEstabelecimento = NotaNFC.ClienteEstabelecimento;
            if (clienteEstabelecimento != null)
            {
                idClienteOuProfissional = clienteEstabelecimento.Codigo;
                var cliente = clienteEstabelecimento.Cliente;
                var pessoaFisica = cliente.PessoaFisica;
                cpfClienteOuProfissional = pessoaFisica.Cpf;
                nomeCompletoClienteOuProfissional = pessoaFisica.NomeCompleto;

                var telefone = pessoaFisica.Telefones.FirstOrDefault(t => t.Ddi == DdiConstants.Brasil);
                if (telefone != null)
                {
                    telefoneFormatado = String.Format("{0}{1}{2}", telefone.DDD, telefone.Numero.Substring(0, telefone.Numero.Length - 4),
                        telefone.Numero.Substring(telefone.Numero.Length - 4));
                }


                var enderecoProprio = pessoaFisica.EnderecoProprio;
                if (enderecoProprio != null)
                {
                    var uf = enderecoProprio.UF;
                    if (uf != null)
                        siglaUF = enderecoProprio.UF.Sigla;

                    logradouro = enderecoProprio.Logradouro;
                    bairro = enderecoProprio.Bairro;
                    cidade = enderecoProprio.Cidade;
                    numeroEndereco = enderecoProprio.Numero;
                    complemento = enderecoProprio.Complemento;
                }
            }
            else
            {
                idClienteOuProfissional = NotaNFC.EstabelecimentoProfissional.Codigo;
                cpfClienteOuProfissional = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.Cpf;
                nomeCompletoClienteOuProfissional = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto;

                var telefone = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.Telefones.FirstOrDefault(t => t.Ddi == DdiConstants.Brasil);
                if (telefone != null)
                {
                    telefoneFormatado = String.Format("{0}{1}{2}", telefone.DDD, telefone.Numero.Substring(0, telefone.Numero.Length - 4),
                    telefone.Numero.Substring(telefone.Numero.Length - 4));
                }

                if (NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio != null)
                {
                    var uf = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.UF;
                    if (uf != null)
                        siglaUF = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.UF.Sigla;

                    logradouro = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.Logradouro;
                    bairro = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.Bairro;
                    cidade = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.Cidade;
                    numeroEndereco = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.Numero;
                    complemento = NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.EnderecoProprio.Complemento;
                }
            }

            //2 digitos tipo de registro
            sb.Append(string.Format("{0,2}", "01"));

            //9 digitos número da prevenda/pedido
            sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(9, '0'));

            //15 digitos cpf/cnpj ou código do cliente
            sb.Append(!String.IsNullOrEmpty(cpfClienteOuProfissional) ?
                cpfClienteOuProfissional.LarguraFixa(15, '0') : idClienteOuProfissional.ToString().LarguraFixa(15, '0'));

            //8 digitos data emissao no formato DDMMAAAA
            sb.Append(NotaNFC.DataVenda.ToString("ddMMyyyy"));

            //4 digitos hora de emissao
            sb.Append(NotaNFC.DataVenda.ToString("HHmm"));

            //4 digitos codigo do vendedor
            sb.Append(NotaNFC.EstabelecimentoProfissional.Codigo.ToString().LarguraFixa(4, '0'));

            var totalLiquido = NotaNFC.TotalBruto;
            totalLiquido -= NotaNFC.TotalDesconto > 0 ? NotaNFC.TotalDesconto : NotaNFC.TotalDesconto * -1; // Considerando desconto

            if (incluirServicosNaNota)
            {
                totalLiquido += NotaNFC.Transacao.TotalServicos ?? 0;
                totalLiquido -= ((NotaNFC.Transacao.DescontosServicos > 0 ? NotaNFC.Transacao.DescontosServicos : NotaNFC.Transacao.DescontosServicos * -1) ?? 0); // Considerando desconto
            }
            //15 digitos valor total, preencher com zeros àesquerda e com 2 casas decimais
            sb.Append(totalLiquido.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

            //60 digitos observacao
            sb.Append(NotaNFC.Transacao.ComentarioFechamentoConta.LarguraFixa(60, ' '));

            //15 digitos valor total de descontos, preencher com zeros àesquerda e com 2 casas decimais
            var totalDesconto = 0; // Aqui só entra desconto na nota, não inclui descontos nos itens
            sb.Append(totalDesconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

            //40 digitos nome completo cliente
            sb.Append(nomeCompletoClienteOuProfissional.LarguraFixaComEspacamentoADireita(40, ' '));

            //45 digitos endereco do cliente
            sb.Append(logradouro.LarguraFixa(45, ' '));

            //15 digitos bairro cliente
            sb.Append(bairro.LarguraFixa(15, ' '));

            //20 digitos cidade cliente
            sb.Append(cidade.LarguraFixa(20, ' '));

            //2 digitos estado cliente
            sb.Append(siglaUF.LarguraFixa(2, ' '));

            //06 digitos numero endereco cliente
            sb.Append(numeroEndereco.LarguraFixa(6, ' '));

            //15 digitos complemento endereco cliente
            sb.Append(complemento.LarguraFixa(15, ' '));

            //06 digitos codigo do vendedo
            sb.Append(NotaNFC.EstabelecimentoProfissional.Codigo.ToString().LarguraFixa(6, '0'));

            //10 digitos número da prevenda/pedido
            sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(10, '0'));

            //15 digitos nome do vendedor
            sb.Append(NotaNFC.EstabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto.LarguraFixa(15, ' '));

            //12 telefone do cliente
            sb.Append(telefoneFormatado.LarguraFixa(12, ' '));

            writer.WriteLine(sb.ToString());
            sb.Clear();
        }

        private void PreencherDadosCancelamento(StringBuilder sb, StreamWriter writer)
        {
            //4 digitos codigo do funcionario
            sb.Append(NotaNFC.EstabelecimentoProfissional.Codigo.ToString().LarguraFixa(4, '0'));

            //9 digitos numero da prevenda/pedido
            sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(9, '0'));

            //6 digitos sequencial do cupon fiscal
            sb.Append(NotaNFC.NumeroNota.ToString().LarguraFixa(6, '0'));

            //15 digitos valor total
            sb.Append(NotaNFC.TotalLiquido.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

            //3 digitos numero do caixa
            sb.Append(string.Format("{0,3}", "001"));

            //20 digitos serie do ECF
            sb.Append(string.Format("{0,20}", ""));

            //6 digitos codigo do funcionario
            sb.Append(NotaNFC.EstabelecimentoProfissional.Codigo.ToString().LarguraFixa(6, '0'));

            //6 digitos sequencial interno do sistema
            sb.Append(NotaNFC.IdNotaNFC.ToString().LarguraFixa(6, '0'));

            //10 digitos numero da prevenda/pedido
            sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(10, '0'));

            writer.WriteLine(sb.ToString());
            sb.Clear();
        }

        private void PreencherDadosHorarioTransacao(StringBuilder sb, StreamWriter writer, IEnumerable<HorarioTransacao> horariosTransacao)
        {
            var dadosTributarios = new SituacaoTributariaDTO(Estabelecimento);

            foreach (var ht in horariosTransacao)
            {
                //2 digitos tipo de registro
                sb.Append(string.Format("{0,2}", "02"));

                //9 digitos número da prevenda/pedido
                sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(9, '0'));
                var horario = ht.Horario;
                var servicoEstabelecimento = horario.ServicoEstabelecimento;

                //14 digitos codigo do produto ou codigo de barras
                sb.Append(servicoEstabelecimento.IdServicoEstabelecimento.LarguraFixa(14));

                //45 digitos descricao produto
                sb.Append(servicoEstabelecimento.Nome.LarguraFixa(45, ' '));

                //20 digitos descricao reduzida produto
                sb.Append(servicoEstabelecimento.Nome.LarguraFixa(20, ' '));

                //Quantidade - Preencher com zeros à esquerda e com 3 casas decimais (15 digitos)
                sb.Append((1).ToString("F3", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                //15 digitos valor unitatio
                var valorUnitarioSemDesconto = ht.Preco ?? 0;
                sb.Append(valorUnitarioSemDesconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                //15 digitos valor descontos
                var desconto = (ht.Desconto > 0 ? ht.Desconto : ht.Desconto * -1) ?? 0;
                sb.Append(desconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                ////3 digitos codigo tributacao de venda
                //if (Estabelecimento != null &&
                //    Estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.IdTipoRegimeTributarioNFC != (int)TipoRegimeTributarioEnum.SimplesNacionalSysPDV)
                //    throw new Exception("O tipo de regime tributário do estabelecimento deve ser Simples Nacional.");

                sb.Append(string.Format("{0,3}", "P00"));

                //70 digitos Complemento descrição produto.
                //Utilizado para fins de impressão detalhada no cupom fiscal, deve-se atentar se o modelo do ECF suporta impressão com descrição maior que 20 caracteres
                sb.Append(string.Format("{0,70}", ""));

                //255 digitos observacao
                sb.Append(NotaNFC.Transacao.ComentarioFechamentoConta.LarguraFixa(255, ' '));

                //1 digitos Alterar produto no cadastro. S = Sim ou N = Não
                sb.Append(string.Format("{0,1}", "S"));

                //10 digitos número da prevenda/pedido
                sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(10, '0'));

                //20 digitos codigo auxiliar
                sb.Append(string.Format("{0,20}", ""));

                //3 digitos unidade de venda
                sb.Append(string.Format("{0,3}", ""));

                var ncmNBS = string.Empty;
                //8 digitos codigo NCM
                sb.Append(ncmNBS.LarguraFixa(8, '0'));

                //1 digitos sigla do PIS
                sb.Append(string.Format("{0,1}", dadosTributarios.SiglaPIS));

                //8 digitos alíquota do PIS
                sb.Append(string.Format("{0,8}", dadosTributarios.aliquotaPIS));

                //2 digitos CST(codigo da situacao tributaria) PIS
                sb.Append(string.Format("{0,2}", dadosTributarios.CstPIS));

                //1 digitos sigla cofins
                sb.Append(string.Format("{0,1}", dadosTributarios.SiglaCONFINS));

                //8 digitos aliquota cofins
                sb.Append(string.Format("{0,8}", dadosTributarios.aliquotaCONFINS));

                //2 digitos codigo da situacao tributaria da cofins
                sb.Append(string.Format("{0,2}", dadosTributarios.CstCONFINS));

                //3 digitos natureza dos impostos federais
                sb.Append(string.Format("{0,3}", ""));

                //2 digitos codigo exceção NCM
                sb.Append(string.Format("{0,2}", ""));

                //1 digito tabela A -> 0 produto nacional e 2 para produto importado
                sb.Append(string.Format("{0,1}", "0"));

                //15 digitos valor garantia
                sb.Append(string.Format("{0,15}", ""));

                writer.WriteLine(sb.ToString());
                sb.Clear();
            }
        }

        private void PreencherDadosItensPacoteServico(StringBuilder sb, StreamWriter writer, List<ItemPacoteClienteServico> itensServico)
        {
            var dadosTributarios = new SituacaoTributariaDTO(Estabelecimento);

            foreach (var i in itensServico)
            {
                //2 digitos tipo de registro
                sb.Append(string.Format("{0,2}", "02"));

                //9 digitos número da prevenda/pedido
                sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(9, '0'));
                var servicoEstabelecimento = i.ServicoEstabelecimento;

                //14 digitos codigo do produto ou codigo de barras
                sb.Append(servicoEstabelecimento.IdServicoEstabelecimento.LarguraFixa(14));

                //45 digitos descricao produto
                sb.Append(servicoEstabelecimento.Nome.LarguraFixa(45, ' '));

                //20 digitos descricao reduzida produto
                sb.Append(servicoEstabelecimento.Nome.LarguraFixa(20, ' '));

                //Quantidade - Preencher com zeros à esquerda e com 3 casas decimais (15 digitos)
                sb.Append((i.Quantidade).ToString("F3", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                //15 digitos valor unitatio
                var valorUnitarioSemDesconto = i.ValorUnitario;
                sb.Append(valorUnitarioSemDesconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                //15 digitos valor descontos
                var desconto = 0;
                sb.Append(desconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                ////3 digitos codigo tributacao de venda
                //if (Estabelecimento != null &&
                //    Estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.IdTipoRegimeTributarioNFC != (int)TipoRegimeTributarioEnum.SimplesNacionalSysPDV)
                //    throw new Exception("O tipo de regime tributário do estabelecimento deve ser Simples Nacional.");

                sb.Append(string.Format("{0,3}", "P00"));

                //70 digitos Complemento descrição produto.
                //Utilizado para fins de impressão detalhada no cupom fiscal, deve-se atentar se o modelo do ECF suporta impressão com descrição maior que 20 caracteres
                sb.Append(string.Format("{0,70}", ""));

                //255 digitos observacao
                sb.Append(NotaNFC.Transacao.ComentarioFechamentoConta.LarguraFixa(255, ' '));

                //1 digitos Alterar produto no cadastro. S = Sim ou N = Não
                sb.Append(string.Format("{0,1}", "S"));

                //10 digitos número da prevenda/pedido
                sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(10, '0'));

                //20 digitos codigo auxiliar
                sb.Append(string.Format("{0,20}", ""));

                //3 digitos unidade de venda
                sb.Append(string.Format("{0,3}", ""));

                var ncmNBS = string.Empty;
                //8 digitos codigo NCM
                sb.Append(ncmNBS.LarguraFixa(8, '0'));

                //1 digitos sigla do PIS
                sb.Append(string.Format("{0,1}", dadosTributarios.SiglaPIS));

                //8 digitos alíquota do PIS
                sb.Append(string.Format("{0,8}", dadosTributarios.aliquotaPIS));

                //2 digitos CST(codigo da situacao tributaria) PIS
                sb.Append(string.Format("{0,2}", dadosTributarios.CstPIS));

                //1 digitos sigla cofins
                sb.Append(string.Format("{0,1}", dadosTributarios.SiglaCONFINS));

                //8 digitos aliquota cofins
                sb.Append(string.Format("{0,8}", dadosTributarios.aliquotaCONFINS));

                //2 digitos codigo da situacao tributaria da cofins
                sb.Append(string.Format("{0,2}", dadosTributarios.CstCONFINS));

                //3 digitos natureza dos impostos federais
                sb.Append(string.Format("{0,3}", ""));

                //2 digitos codigo exceção NCM
                sb.Append(string.Format("{0,2}", ""));

                //1 digito tabela A -> 0 produto nacional e 2 para produto importado
                sb.Append(string.Format("{0,1}", "0"));

                //15 digitos valor garantia
                sb.Append(string.Format("{0,15}", ""));

                writer.WriteLine(sb.ToString());
                sb.Clear();
            }
        }

        private void PreencherDadosItensVenda(StringBuilder sb, StreamWriter writer, List<NotaItensNFC> nfcItens)
        {
            var dadosTributarios = new SituacaoTributariaDTO(Estabelecimento);

            foreach (var itemNota in nfcItens)
            {
                //2 digitos tipo de registro
                sb.Append(string.Format("{0,2}", "02"));

                //9 digitos número da prevenda/pedido
                sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(9, '0'));
                var estabelecimentoProduto = itemNota.EstabelecimentoProduto;

                //14 digitos codigo do produto ou codigo de barras
                sb.Append(estabelecimentoProduto.Id.LarguraFixa(14));

                //45 digitos descricao produto
                sb.Append(estabelecimentoProduto.Descricao.LarguraFixa(45, ' '));

                //20 digitos descricao reduzida produto
                sb.Append(estabelecimentoProduto.Descricao.LarguraFixa(20, ' '));

                //Quantidade - Preencher com zeros à esquerda e com 3 casas decimais (15 digitos)
                sb.Append(itemNota.Quantidade.ToString("F3", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                //15 digitos valor unitatio
                var valorUnitarioSemDesconto = itemNota.PrecoUnitario + (itemNota.DescontoUnitario > 0 ? itemNota.DescontoUnitario : itemNota.DescontoUnitario * -1);
                sb.Append(valorUnitarioSemDesconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                //15 digitos valor descontos
                var desconto = itemNota.Desconto;
                sb.Append(desconto.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0'));

                ////3 digitos codigo tributacao de venda
                //if (Estabelecimento != null &&
                //    Estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.IdTipoRegimeTributarioNFC != (int)TipoRegimeTributarioEnum.SimplesNacionalSysPDV)
                //    throw new Exception("O tipo de regime tributário do estabelecimento deve ser Simples Nacional.");
                string codigoSituacaoTributaria = null;
                var interfaceNfc = itemNota.EstabelecimentoProduto.Estabelecimento.ConfiguracaoDeNFC.InterfaceNFC;
                if (itemNota.EstabelecimentoProduto.IdSituacaoTributaria != null)
                    codigoSituacaoTributaria = TipoSituacaoTributaria.ObterTipoSituacaoTributariaPorId(itemNota.EstabelecimentoProduto.IdSituacaoTributaria.Value).Codigo;

                var cest = itemNota.EstabelecimentoProduto.CEST ?? null;

                sb.Append(string.Format("{0,3}", !string.IsNullOrEmpty(codigoSituacaoTributaria) ? codigoSituacaoTributaria : "T00"));

                //70 digitos Complemento descrição produto.
                //Utilizado para fins de impressão detalhada no cupom fiscal, deve-se atentar se o modelo do ECF suporta impressão com descrição maior que 20 caracteres
                sb.Append(string.Format("{0,70}", ""));

                //255 digitos observacao
                sb.Append(NotaNFC.Transacao.ComentarioFechamentoConta.LarguraFixa(255, ' '));

                //1 digitos Alterar produto no cadastro. S = Sim ou N = Não
                sb.Append(string.Format("{0,1}", "S"));

                //10 digitos número da prevenda/pedido
                sb.Append(NotaNFC.Transacao.Id.ToString().LarguraFixa(10, '0'));

                //20 digitos codigo auxiliar
                sb.Append(string.Format("{0,20}", ""));

                //3 digitos unidade de venda
                sb.Append(string.Format("{0,3}", ""));

                var ncmNBS = string.Empty;
                if (!string.IsNullOrEmpty(estabelecimentoProduto.CodigoNCM))
                    ncmNBS = estabelecimentoProduto.CodigoNCM;

                //8 digitos codigo NCM
                sb.Append(ncmNBS.LarguraFixa(8, '0'));

                //1 digitos sigla do PIS
                sb.Append(string.Format("{0,1}", dadosTributarios.SiglaPIS.LarguraFixa(1)));

                //8 dígitos alíquota do PIS
                sb.Append(string.Format("{0,8}", dadosTributarios.aliquotaPIS.LarguraFixa(8)));

                //2 digitos CST(codigo da situacao tributaria) PIS
                sb.Append(string.Format("{0,2}", dadosTributarios.CstPIS.LarguraFixa(2)));

                //1 digitos sigla confins
                sb.Append(string.Format("{0,1}", dadosTributarios.SiglaCONFINS.LarguraFixa(1)));

                //8 digitos aliquota confins
                sb.Append(string.Format("{0,8}", dadosTributarios.aliquotaCONFINS.LarguraFixa(8)));

                //2 digitos codigo da situacao tributaria da confins
                sb.Append(string.Format("{0,2}", dadosTributarios.CstCONFINS.LarguraFixa(2)));

                //3 digitos natureza dos impostos federais
                sb.Append(string.Format("{0,3}", ""));

                //2 digitos codigo exceção NCM
                sb.Append(string.Format("{0,2}", ""));

                //1 digito tabela A -> 0 produto nacional e 2 para produto importado
                if (estabelecimentoProduto.ProdutoEhNacional)
                    sb.Append(string.Format("{0,1}", "0"));
                else
                    sb.Append(string.Format("{0,1}", "2"));

                //15 digitos valor garantia
                sb.Append(string.Format("{0,15}", ""));

                var IcmstHabilitado = Estabelecimento.PessoaJuridica.EnderecoProprio.UF.IcmsStHabilitado;

                if (!string.IsNullOrEmpty(codigoSituacaoTributaria) && (codigoSituacaoTributaria == "F" || codigoSituacaoTributaria == "F00") && IcmstHabilitado)
                {
                    //4 dígitos
                    sb.Append(string.Format("{0,5}", ""));

                    //4 dígitos CFOP
                    sb.Append(string.Format("{0,4}", ""));

                    //2 dígitos código do imposto PIS
                    sb.Append(string.Format("{0,2}", itemNota.EstabelecimentoProduto.CodigoPis.LarguraFixa(2)));

                    //2 dígitos código do imposto COFINS
                    sb.Append(string.Format("{0,2}", itemNota.EstabelecimentoProduto.CodigoCofins.LarguraFixa(2)));

                    //7 dígitos CEST
                    sb.Append(string.Format("{0,7}", cest.LarguraFixa(7)));

                    var icmsOrigem = itemNota.EstabelecimentoProduto.IcmsOrigem.Value.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0');
                    //15 dígitos alíquota do ICMS de origem
                    sb.Append(string.Format("{0,15}", icmsOrigem));

                    var reducaoIcmsOrigem = itemNota.EstabelecimentoProduto.ReducaoIcmsOrigem.Value.ToString("F", CultureInfo.CreateSpecificCulture("en-US")).LarguraFixa(15, '0');
                    //15 dígitos percentual de redução do ICMS de origem
                    sb.Append(string.Format("{0,15}", reducaoIcmsOrigem));
                }
                else
                {
                    sb.Append(string.Format("{0,50}", ""));
                }

                writer.WriteLine(sb.ToString());
                sb.Clear();
            }
        }

        public override void Inutilizar(NotaInutilizadaNFC inutilizacao)
        {
            throw new NotImplementedException();
        }
    }
}