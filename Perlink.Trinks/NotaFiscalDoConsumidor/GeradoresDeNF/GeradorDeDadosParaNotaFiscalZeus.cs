﻿using DFe.Classes.Entidades;
using DFe.Classes.Flags;
using DFe.Utils;
using Elmah;
using NFe.Classes;
using NFe.Classes.Informacoes;
using NFe.Classes.Informacoes.Cobranca;
using NFe.Classes.Informacoes.Destinatario;
using NFe.Classes.Informacoes.Detalhe;
using NFe.Classes.Informacoes.Detalhe.Tributacao;
using NFe.Classes.Informacoes.Detalhe.Tributacao.Estadual;
using NFe.Classes.Informacoes.Detalhe.Tributacao.Estadual.Tipos;
using NFe.Classes.Informacoes.Detalhe.Tributacao.Federal;
using NFe.Classes.Informacoes.Detalhe.Tributacao.Federal.Tipos;
using NFe.Classes.Informacoes.Detalhe.Tributacao.Municipal;
using NFe.Classes.Informacoes.Emitente;
using NFe.Classes.Informacoes.Identificacao;
using NFe.Classes.Informacoes.Observacoes;
using NFe.Classes.Informacoes.Pagamento;
using NFe.Classes.Informacoes.Total;
using NFe.Classes.Informacoes.Transporte;
using NFe.Classes.Protocolo;
using NFe.Classes.Servicos.Tipos;
using NFe.Servicos;
using NFe.Servicos.Retorno;
using NFe.Utils;
using NFe.Utils.Excecoes;
using NFe.Utils.NFe;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Encryptor;
using Perlink.Shared.Enums;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Shared.Patterns;
using Perlink.Shared.Text;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.NotaFiscalDoConsumidor.DTO;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor.Exceptions;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.RPS;
using Perlink.Trinks.Vendas;
using Shared.NFe.Classes.Informacoes.InfRespTec;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using enumFormaPagamentoNFe = NFe.Classes.Informacoes.Pagamento.FormaPagamento;

using NFEAmbientes = DFe.Classes.Flags;

using NFETipos = NFe.Classes.Informacoes.Identificacao.Tipos;

namespace Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF
{

    public class GeradorDeDadosParaNotaFiscalZeus : GeradorDeDadosParaNotaFiscal
    {
        private const string _nomePessoaParaAmbienteHomologacao = "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL";
        private const string _nomeItemParaAmbienteHomologacao = "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL";
        private const int _cfopServico = 5933;

        public enum TipoDeRegimeTributarioDeUmEstabelecimento
        {
            Normal = 1,
            SimplesNacional = 2
        }

        public Transacao Transacao { get; set; }

        public Estabelecimento Estabelecimento { get; set; }

        public NotaNFC NotaNFC { get; set; }

        public List<DadosParaGeracaoLoteRPSDTO> Registros { get; set; }

        private static bool HabilitaInutilizacaoDeNFCe
        {
            get { return new ParametrosTrinks<bool>(ParametrosTrinksEnum.habilita_inutilizar_numero_nota).ObterValor(); }
        }

        #region Construtores

        public GeradorDeDadosParaNotaFiscalZeus()
        {
            IgnorarErrosDeCertificadoDoSefaz();
        }

        public GeradorDeDadosParaNotaFiscalZeus(Transacao transacao, Estabelecimento estabelecimento, List<DadosParaGeracaoLoteRPSDTO> registros = null) : this()
        {
            Transacao = transacao;
            Estabelecimento = estabelecimento;
            Registros = registros;
            TipoDeInterface = Enums.TipoDeInterfaceNFC.NotaFiscalEletronica;
        }

        public GeradorDeDadosParaNotaFiscalZeus(Estabelecimento estabelecimento) : this()
        {
            Estabelecimento = estabelecimento;
            TipoDeInterface = Enums.TipoDeInterfaceNFC.NotaFiscalEletronica;
        }

        private ControleDeArquivosAmazonS3 _controleDeArquivos = null;

        private static ControleDeArquivosAmazonS3 ControleDeArquivos = new ControleDeArquivosAmazonS3();

        private byte[] _certificado = null;

        private byte[] Certificado
        {
            get
            {
                if (_certificado == null)
                    _certificado = ObterCertificado();
                return _certificado;
            }
        }

        private byte[] ObterCertificado()
        {
            ArquivoAmazonS3 arquivoCertificado = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.
                    ObterCertificadoEhMigrarArquivosSeNecessario(Estabelecimento.PessoaJuridica);

            if (arquivoCertificado == null)
                throw new Exception("Não foi encontrado o certificado digital ao gerar nota fiscal.");

            var stream = arquivoCertificado.S3FileInfo.OpenRead();

            byte[] result;

            using (MemoryStream streamReader = new MemoryStream())
            {
                stream.CopyTo(streamReader);
                result = streamReader.ToArray();
            }

            return result;
        }

        #endregion Construtores

        private string _senhaDoCertificado = null;

        private string SenhaDoCertificado
        {
            get
            {
                if (_senhaDoCertificado == null)
                    _senhaDoCertificado = ObterSenhaDoCertificado();
                return _senhaDoCertificado;
            }
        }

        private string ObterSenhaDoCertificado()
        {
            var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
            var cfgNfce = Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Load(Estabelecimento.IdEstabelecimento);
            var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == Estabelecimento.PessoaJuridica.IdPessoa);

            if (cfgNfce != null && !string.IsNullOrEmpty(cfgNfce.CnpjAlternativo))
                pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == Estabelecimento.PessoaJuridica.IdPessoa && p.EhCnpjAlternativo);

            return criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);
        }

        private ConfiguracaoNFCEstado _configuracaoDeNFCNoEstado = null;

        private ConfiguracaoNFCEstado ConfiguracaoDeNFCNoEstado
        {
            get
            {
                if (_configuracaoDeNFCNoEstado == null)
                {
                    var idUFEstabelecimento = NotaNFC.Estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF;
                    _configuracaoDeNFCNoEstado = Domain.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.ObterConfiguracaoNFCeDoUF(idUFEstabelecimento);
                }
                return _configuracaoDeNFCNoEstado;
            }
        }

#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously

        public override async Task<int> EmitirNotaFiscal()
        {
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously

            var retorno = 0;

            if (String.IsNullOrEmpty(Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo) && String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.CNPJ))
                throw new Exception("Foi solicitada uma geração de nota fiscal onde o estabelecimento não possui CNPJ");

            if (ConfiguracoesTrinks.NFCe.HabilitarUsoDoZeus && HabilitaInutilizacaoDeNFCe)
                ValidarPossibilidadeEmissaoNota();

            if (ValidationHelper.Instance.IsValid &&
                (ValidationHelper.Instance.ListaItensNotificacao == null ||
                 (ValidationHelper.Instance.ListaItensNotificacao != null &&
                 !ValidationHelper.Instance.ListaItensNotificacao.Any())))
            {
                var registros = Domain.RPS.DadosRPSService.ListarDadosParaGeracaoDeRPS(new ParametrosFiltrosLotesRPS
                {
                    IdsTransacao = new List<int> { Transacao.Id },
                    IdPessoaDaPessoaJuridica = Estabelecimento.PessoaJuridica.IdPessoa,
                    Estabelecimento = Estabelecimento
                });

                NotaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCService.GerarNotaNFC(Transacao, null, false, registros);

                if (ConfiguracoesTrinks.NFCe.HabilitarUsoDoZeus)
                {
                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.
                                                         ObterPorPessoaJuridica(Transacao.PessoaQueRecebeu.IdPessoa);
                    ConfiguracaoServico = CarregarConfiguracaoServico(estabelecimento);

                    var servicoNFe = new ServicosNFe(ConfiguracaoServico);

                    if (HabilitaInutilizacaoDeNFCe)
                        RealizarInutilizacoesPendentes(servicoNFe);

                    if (ValidationHelper.Instance.IsValid)
                    {
                        var foiEnviado = false;
                        retorno = NotaNFC.NumeroNota;

                        try
                        {
                            var dataAtual = Calendario.Agora();
                            var nfe = ConstruirNFe(NotaNFC);

                            NotaNFC.TextoDeInformacaoComplementar = nfe.infNFe.infAdic.infCpl;
                            NotaNFC.UrlDoQRCode = nfe.infNFeSupl.qrCode;

                            // Nova implementação de NFC-e síncrono.
                            // http://ciranda.me/tsdn/blog-da-tecnospeed/post/nfc-e-tera-transmissao-de-dados-otimizada
                            // Dessa forma, não tem o status - Lote em processamento.
                            // Propositalmente o código do Assíncrono não foi alterado, pois a tendência é que seja
                            // removido futuramente.
                            if (ConfiguracaoDeNFCNoEstado.Sincrono)
                            {
                                var retornoEnvio = servicoNFe.NFeAutorizacao(NotaNFC.NumeroNota, IndicadorSincronizacao.Sincrono, new List<NFe.Classes.NFe> { nfe });
                                NotaNFC.LoteSEFAZ = NotaNFC.NumeroNota.ToString();
                                NotaNFC.AutorizacaoSEFAZ = nfe.infNFe.Id;
                                if (nfe.Signature != null && nfe.Signature.SignedInfo != null && nfe.Signature.SignedInfo.Reference != null)
                                    NotaNFC.DigestValue = nfe.Signature.SignedInfo.Reference.DigestValue;
                                NotaNFC.DataEmissao = dataAtual;

                                if (retornoEnvio.Retorno.cStat == 104)
                                {
                                    if (retornoEnvio.Retorno.protNFe.infProt.cStat == 100)
                                    {
                                        try
                                        {
                                            NotaNFC.StatusNota = new StatusNotaNFC { IdStatusNotaNFC = 2 };
                                            NotaNFC.ProtocoloSEFAZ = retornoEnvio.Retorno.protNFe.infProt.nProt;
                                            NotaNFC.DataRecebimentoSEFAZ = retornoEnvio.Retorno.protNFe.infProt.dhRecbto;
                                            var xml = GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(retornoEnvio.EnvioStr, servicoNFe);
                                            string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
                                            ControleDeArquivos.GravarArquivo(xml, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + "/Nota", NotaNFC.AutorizacaoSEFAZ + ".xml");
                                            Domain.Pessoas.EnvioEmailService.EnviarEmailEmissaoNFCe(Transacao, xml);
                                            foiEnviado = true;
                                        }
                                        catch (Exception e)
                                        {
                                            // Qualquer erro neste trecho, não pode mais realizar rollback
                                            // pois há emissão junto ao SEFAZ. A exceção é gravada mas não é lançada.
                                            GravarErroElmah(new Exception("Exceção gerada na obtenção do XML ou no envio de email. Mesmo assim, a emissão foi confirmada no sistema pois foi realizada no SEFAZ.", e));
                                        }
                                    }
                                    else
                                    {
                                        throw new Exception(retornoEnvio.Retorno.protNFe.infProt.xMotivo);
                                    }
                                }
                                else
                                {
                                    throw new Exception(retornoEnvio.Retorno.xMotivo);
                                }

                                // Modo assíncrono (Provavelmente será descontinuado, de acordo com o sucesso da implementação síncrona
                                // que é mais vantajosa neste cenário).
                            }
                            else
                            {
                                var retornoEnvio = servicoNFe.NFeAutorizacao(NotaNFC.NumeroNota, IndicadorSincronizacao.Assincrono, new List<NFe.Classes.NFe> { nfe });
                                NotaNFC.LoteSEFAZ = NotaNFC.NumeroNota.ToString();
                                NotaNFC.ReciboSEFAZ = retornoEnvio.Retorno.infRec.nRec;
                                NotaNFC.AutorizacaoSEFAZ = nfe.infNFe.Id;
                                if (nfe.Signature != null && nfe.Signature.SignedInfo != null && nfe.Signature.SignedInfo.Reference != null)
                                    NotaNFC.DigestValue = nfe.Signature.SignedInfo.Reference.DigestValue;
                                NotaNFC.DataEmissao = dataAtual;

                                var segundosParaProcessar = retornoEnvio.Retorno.infRec.tMed;

                                if (segundosParaProcessar <= 1)
                                    segundosParaProcessar = 2;

                                System.Threading.Thread.Sleep((segundosParaProcessar + 1) * 1000);

                                Retry.Do(() => VerificarRetornoAutorizacaoNFe(servicoNFe, retornoEnvio, ControleDeArquivos), TimeSpan.FromSeconds(segundosParaProcessar * 5), 10);
                                foiEnviado = true;
                            }

                            Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Update(NotaNFC);

                            if (!NotaNFC.NFCProducao)
                                ValidationHelper.Instance.AdicionarItemNotificacao("Nota emitida em ambiente de homologação");
                        }
                        catch (Exception ex)
                        {
                            var erro = ex;
                            if (ex is AggregateException)
                                erro = ((AggregateException)ex).InnerException;

                            if (!(erro is ValidacaoSchemaException) && !erro.Message.Contains("Rejeição"))
                                GravarErroElmah(erro);
                            var message = erro.Message;
                            message = TratarMensagemEStatusDeErro(servicoNFe, ex, message, out foiEnviado);
                            ValidationHelper.Instance.AdicionarItemAlerta(message);
                        }

                        if (!foiEnviado)
                            retorno = 0;
                    }
                }
                else if (ConfiguracoesTrinks.NFCe.HabilitarUsoDoZeus)
                {
                    ColocarDadosDeTesteNaNotaParaOQRCode(NotaNFC);
                    if (NotaNFC.UrlDoQRCode != null)
                        Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Update(NotaNFC);
                }
            }

            return retorno;
        }

        private string TratarMensagemEStatusDeErro(ServicosNFe servicoNFe, Exception ex, string message, out bool foiEnviado)
        {
            string mensagemLower = message.ToLower();

            foiEnviado = false;

            try
            {
                var xml = Domain.Financeiro.TransacaoService.GerarNFCeParaDownload(Transacao).Result;
                GravarXMLNoS3(xml, "/Nota_Erro", NotaNFC.AutorizacaoSEFAZ + ".xml");
            }
            catch (Exception e)
            {
                GravarErroElmah(e);
            }

            if (mensagemLower.Contains("timeout") || (mensagemLower.Contains("time") && mensagemLower.Contains("out")))
            {
                message = "A SEFAZ está passando por instabilidades.\nA nota foi emitiva porém não é possível verificar o sucesso da emissão.\nSerá necessário realizar uma verificação manual.";
                DefinirNotaComoPendentePosErro(NotaNFC);
                foiEnviado = true;
            }
            else if (mensagemLower.Contains("http") && mensagemLower.Contains("403"))
            {
                message = "Comunicação rejeitada pelo SEFAZ.\nVerifique se o seu certificado está dentro da validade e se ele é o mesmo que foi enviado para o SEFAZ.";
                RealizarRollbackDeDadosDeNota(servicoNFe, NotaNFC, ex);
            }
            else
            {
                message = TraduzirMensagemValidacao(message);
                RealizarRollbackDeDadosDeNota(servicoNFe, NotaNFC, ex);
            }

            return message;
        }

        private void GravarXMLNoS3(string xml, string pasta, string nomeArquivo)
        {
            string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();

            var caminho = "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + pasta;
            ControleDeArquivos.GravarArquivo(xml, bucket + caminho, nomeArquivo);
        }

        private NFe.Classes.NFe ConstruirNFe(NotaNFC nota)
        {
            var dataAtual = Calendario.Agora();

            var configuracaoServico = CarregarConfiguracaoServico(nota.Estabelecimento);

            var nfe = GetNf(nota.NumeroNota, nota.NumeroSerie, ConfiguracaoServico.Instancia.ModeloDocumento, VersaoServico.Versao400, dataAtual, nota.NFCProducao);

            if (nota.DataContingenciaParaHomologacao.HasValue)
            {
                nfe.infNFe.ide.dhCont = new DateTimeOffset(nota.DataContingenciaParaHomologacao.Value);
                nfe.infNFe.ide.tpEmis = NFETipos.TipoEmissao.teOffLine;
                nfe.infNFe.ide.xJust = "Teste para homologação";
            }

            nfe.Assina(configuracaoServico);

            //var notaTecnicaJaEstaImplantada = new ParametrosTrinks<bool>(ParametrosTrinksEnum.nota_tecnica_2015_implantada).ObterValor();
            //if (notaTecnicaJaEstaImplantada) {
            var qrCode = NFe.Utils.InformacoesSuplementares.ExtinfNFeSupl.ObterUrlQrCode(nfe.infNFeSupl, nfe, VersaoQrCode.QrCodeVersao2, Estabelecimento.ConfiguracaoDeNFC.IdentificadorCSC, Estabelecimento.ConfiguracaoDeNFC.CSC);
            var urlChave = NFe.Utils.InformacoesSuplementares.ExtinfNFeSupl.ObterUrl(nfe.infNFeSupl, ConfiguracaoServico.tpAmb, ConfiguracaoServico.cUF, TipoUrlConsultaPublica.UrlConsulta, VersaoServico.Versao400, VersaoQrCode.QrCodeVersao2);
            nfe.infNFeSupl = new infNFeSupl() { qrCode = qrCode, urlChave = urlChave };
            //}

            return nfe;
        }

        private ConfiguracaoServico CarregarConfiguracaoServico(Estabelecimento estabelecimento)
        {
            var configuracaoServico = ObterConfiguracaoServico(estabelecimento);

            configuracaoServico.Certificado.Senha = SenhaDoCertificado;
            configuracaoServico.Certificado.ArrayBytesArquivo = Certificado;
            configuracaoServico.ProtocoloDeSeguranca = SecurityProtocolType.Tls12;
            return configuracaoServico;
        }

        public void CarregarConfiguracaoServico()
        {
            ConfiguracaoServico = CarregarConfiguracaoServico(Estabelecimento);
        }

        private void DefinirNotaComoPendentePosErro(NotaNFC notaNFC)
        {
            notaNFC.StatusNota = StatusNotaNFCEnum.PendenteFalha;
        }

        private static void RealizarRollbackDeDadosDeNota(ServicosNFe servicoNFe, NotaNFC notaNFC, Exception ex)
        {
            //if (HabilitaInutilizacaoDeNFCe && !ex.Message.Contains("ja esta inutilizada")) {
            //    InutilizarNota(servicoNFe, notaNFC, ex.Message);
            //}

            foreach (var item in notaNFC.Itens)
            {
                Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.Delete(item);
            }

            foreach (var item in notaNFC.FormasPagamento)
            {
                Domain.NotaFiscalDoConsumidor.NotaFormaPagamentoNFCRepository.Delete(item);
            }

            Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Delete(notaNFC);
            notaNFC.NumeroNota = 0;
        }

        public override async Task<string> GerarNFAssinadaDownload()
        {
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously

            if (String.IsNullOrEmpty(Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo) && String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.CNPJ))
                throw new Exception("Foi solicitada uma geração de nota fiscal onde o estabelecimento não possui CNPJ");

            if (ConfiguracoesTrinks.NFCe.HabilitarUsoDoZeus && HabilitaInutilizacaoDeNFCe)
                ValidarPossibilidadeEmissaoNota();

            if (ValidationHelper.Instance.IsValid &&
                (ValidationHelper.Instance.ListaItensNotificacao == null ||
                 (ValidationHelper.Instance.ListaItensNotificacao != null &&
                 !ValidationHelper.Instance.ListaItensNotificacao.Any())))
            {
                NotaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCService.GerarNotaNFC(Transacao, null, true);

                if (ConfiguracoesTrinks.NFCe.HabilitarUsoDoZeus)
                {
                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.
                                                         ObterPorPessoaJuridica(Transacao.PessoaQueRecebeu.IdPessoa);
                    ConfiguracaoServico = ObterConfiguracaoServico(estabelecimento);

                    ConfiguracaoServico.Certificado.Senha = SenhaDoCertificado;
                    ConfiguracaoServico.Certificado.ArrayBytesArquivo = Certificado;
                    ConfiguracaoServico.ProtocoloDeSeguranca = SecurityProtocolType.Tls12;

                    var servicoNFe = new ServicosNFe(ConfiguracaoServico);

                    if (HabilitaInutilizacaoDeNFCe)
                        RealizarInutilizacoesPendentes(servicoNFe);

                    if (ValidationHelper.Instance.IsValid)
                    {
                        try
                        {
                            var dataAtual = Calendario.Agora();
                            decimal impostoTotalCalculado = 0;
                            var nfe = GetNf(NotaNFC.NumeroNota, NotaNFC.NumeroSerie, ConfiguracaoServico.Instancia.ModeloDocumento, VersaoServico.Versao400, NotaNFC.DataEmissao ?? dataAtual, NotaNFC.NFCProducao);
                            nfe.Assina(ConfiguracaoServico);

                            NotaNFC.TextoDeInformacaoComplementar = nfe.infNFe.infAdic.infCpl;

                            //var notaTecnicaJaEstaImplantada = new ParametrosTrinks<bool>(ParametrosTrinksEnum.nota_tecnica_2015_implantada).ObterValor();
                            //if (notaTecnicaJaEstaImplantada) {
                            var qrCode = ObterUrlQrCode(nfe);
                            nfe.infNFeSupl = new infNFeSupl() { qrCode = qrCode };
                            NotaNFC.UrlDoQRCode = qrCode;
                            //}

                            var xml = GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(nfe, servicoNFe);

                            return xml;
                        }
                        catch (Exception ex)
                        {
                            var erro = ex;
                            if (ex is AggregateException)
                                erro = ((AggregateException)ex).InnerException;
                            else if (ex.Message.Contains(""))

                                #region Rollback

                                if (erro == null)
                                    return null;
                            if (HttpContext.Current == null)
                                return null;

                            GravarErroElmah(erro);

                            var message = erro.Message;

                            message = TraduzirMensagemValidacao(message);

                            if (message.Contains("HTTP") && message.Contains("403"))
                                message = "Comunicação rejeitada pelo SEFAZ.\nVerifique se o seu certificado está dentro da validade e se ele é o mesmo que foi enviado para o SEFAZ.";

                            ValidationHelper.Instance.AdicionarItemAlerta(message);

                            #endregion Rollback
                        }
                    }
                }
            }

            return null;
        }

        private static void GravarErroElmah(Exception e)
        {
            var erro = e;
            if (e is AggregateException)
                erro = ((AggregateException)e).InnerException;

            ErrorSignal.FromContext(HttpContext.Current).Raise(erro, HttpContext.Current);
        }

        private static string TraduzirMensagemValidacao(string mensagem)
        {
            if (mensagem.Contains("Erros da validação"))
            {
                var partes = mensagem.Split('\'');

                var campo = partes[1];
                var valor = partes[3];
                return ObterMensagem(mensagem, campo, valor);
            }
            else
            if (mensagem.Contains("Rejeição:"))
            {
                var campo = mensagem.Replace("Rejeição: ", "").Replace(" inválido", "");
                return ObterMensagem(mensagem, campo);
            }
            else
                return mensagem;
        }

        private static string ObterMensagem(string mensagem, string campo, string valor = null)
        {
            var nomeCampos = new Dictionary<string, string> {
                { "cListServ", "Código Item Lista Serviço" },
                { "fone", "Telefone" },
                { "xLgr", "Logradouro" },
                { "xProd", "Nome do Produto/Serviço" },
                { "cProd", "Código do Produto/Serviço" },
                { "cEAN", "Código de Barras" },
                { "cRegTrib", "Código do regime especial de tributação" },
                { "cServico", "Código do serviço prestado dentro do município" }
            };
            if (!string.IsNullOrWhiteSpace(campo))
            {
                campo = campo.Replace("http://www.portalfiscal.inf.br/nfe:", "");

                if (nomeCampos.ContainsKey(campo))
                    campo = nomeCampos[campo];
            }

            var retorno = "Nota rejeitada pelo SEFAZ pelo seguinte motivo:\nCampo '" + campo + "' com valor inválido";

            if (valor != null)
                retorno += " '" + valor + "'.";

            if (mensagem.Contains("MinLength"))
                retorno += "\nO valor está menor que o tamanho mínimo.";

            if (mensagem.Contains("MaxLength"))
                retorno += "\nO valor está maior que o tamanho mínimo.";

            return retorno;
        }

        private void ColocarDadosDeTesteNaNotaParaOQRCode(NotaNFC nota)
        {
            var colocarQRCodeDeTesteNoZeus = ConfigurationManager.AppSettings["ColocarQRCodeDeTesteNoZeus"];
            if (colocarQRCodeDeTesteNoZeus != null && bool.Parse(colocarQRCodeDeTesteNoZeus))
            {
                if (string.IsNullOrEmpty(NotaNFC.UrlDoQRCode))
                {
                    nota.UrlDoQRCode = "http://www4.fazenda.rj.gov.br/consultaNFCe/QRCode?chNFe=33160859546515004121650010000265821600268975&nVersao=100&tpAmb=1&dhEmi=323031362d30382d32345430393a30303a33362d30333a3030&vNF=89.90&vICMS=17.98&digVal=34716c325561736c61494855596b6b3938635439574762474c32493d&cIdToken=000001&cHashQRCode=acb136f005fba14f20e675eb1bbd10174b9ce700";
                    nota.ProtocoloSEFAZ = "333160325355008";
                    nota.AutorizacaoSEFAZ = "NFe33160859546515004121650010000265821600268975";
                    nota.DataEmissao = Calendario.Agora();
                }
            }
        }

        private void VerificarRetornoAutorizacaoNFe(ServicosNFe servicoNFe, RetornoNFeAutorizacao retornoEnvio, ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos)
        {
            var retornoRecibo = servicoNFe.NFeRetAutorizacao(NotaNFC.ReciboSEFAZ);

            var naoVerificarStatusRetornoZeus = ConfigurationManager.AppSettings["NaoVerificarStatusRetornoZeus"];

            List<Int32> idsStatusNFCeAguardando = new ParametrosTrinks<List<Int32>>(ParametrosTrinksEnum.id_status_nfce_aguardando).ObterValor();

            if (idsStatusNFCeAguardando.Count(a => a == retornoRecibo.Retorno.cStat) > 0)
            {
                throw new NFCeLoteEmProcessamentoException("Aguardando retorno NFCe");
            }
            else
            {
                if (retornoRecibo.Retorno.cStat == 104)
                {
                    var protNFe = retornoRecibo.Retorno.protNFe.First();
                    if (protNFe.infProt.cStat == 100 ||
                        (naoVerificarStatusRetornoZeus != null && bool.Parse(naoVerificarStatusRetornoZeus)))
                    {
                        NotaNFC.ProtocoloSEFAZ = protNFe.infProt.nProt;
                        NotaNFC.StatusNota = new StatusNotaNFC { IdStatusNotaNFC = 2 };
                        NotaNFC.DataRecebimentoSEFAZ = protNFe.infProt.dhRecbto;
                        var xml = GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(retornoEnvio.EnvioStr, servicoNFe);

                        Domain.Pessoas.EnvioEmailService.EnviarEmailEmissaoNFCe(Transacao, xml);
                        string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
                        s3AmazonControleDeArquivos.GravarArquivo(xml, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + "/Nota", NotaNFC.AutorizacaoSEFAZ + ".xml");
                    }
                    else
                    {
                        throw new Exception(protNFe.infProt.xMotivo);
                    }
                }
                else
                {
                    throw new Exception(retornoRecibo.Retorno.xMotivo);
                }
            }
        }

        //private static void InutilizarNota(ServicosNFe servicoNFe, NotaNFC nota, string justificativa) {
        //    RetornoNfeInutilizacao retorno = null;

        //    var jaExisteNotaInutilizadaComEsseNumeroParaEstabelecimento =
        //        Domain.NotaFiscalDoConsumidor.NotaInutilizadaNFCRepository.Queryable()
        //            .Any(p => p.NumeroNota == nota.NumeroNota &&
        //                        p.Estabelecimento.IdEstabelecimento == nota.Estabelecimento.IdEstabelecimento);

        //    if (!jaExisteNotaInutilizadaComEsseNumeroParaEstabelecimento) {
        //        var erroNota = new NotaInutilizadaNFC() {
        //            Estabelecimento = nota.Estabelecimento,
        //            Transacao = nota.Transacao,
        //            NFCProducao = nota.NFCProducao,
        //            NumeroNota = nota.NumeroNota,
        //            NumeroSerie = nota.NumeroSerie,
        //            DataVenda = nota.DataVenda,
        //            MensagemErro = justificativa
        //        };

        //        try {
        //            string ano = nota.DataVenda.Year.ToString().Substring(2, 2);
        //            retorno = servicoNFe.NfeInutilizacao(nota.Estabelecimento.PessoaJuridica.CNPJ, int.Parse(ano),
        //                 ModeloDocumento.NFCe, nota.NumeroSerie, nota.NumeroNota, nota.NumeroNota, justificativa.Substring(0, Math.Min(250, justificativa.Length)));
        //        }
        //        catch (Exception ex) {
        //            erroNota.RetornoInutilizacaoSEFAZ = ex.Message;
        //            if (ex.InnerException != null) {
        //                erroNota.RetornoInutilizacaoSEFAZ += ex.InnerException.Message;
        //            }
        //        }

        //        if (retorno != null) {
        //            if (retorno.Retorno != null && retorno.Retorno.infInut != null && retorno.Retorno.infInut.cStat == 102) {
        //                erroNota.NumeroFoiInutilizado = true;
        //            }
        //            erroNota.RetornoInutilizacaoSEFAZ = retorno != null ? retorno.RetornoStr : "";
        //        };

        //        if (erroNota.RetornoInutilizacaoSEFAZ.Length >= 1000)
        //            erroNota.RetornoInutilizacaoSEFAZ = erroNota.RetornoInutilizacaoSEFAZ.Substring(0, 999);

        //        if (!string.IsNullOrEmpty(erroNota.MensagemErro) && erroNota.MensagemErro.Length >= 500)
        //            erroNota.MensagemErro = erroNota.MensagemErro.Substring(0, 499);

        //        Domain.NotaFiscalDoConsumidor.NotaInutilizadaNFCRepository.SaveNew(erroNota);

        //        try {
        //            var bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
        //            ControleDeArquivos.GravarArquivo(retorno.EnvioStr, bucket + "/NFCe/" + nota.Estabelecimento.IdEstabelecimento.ToString() + "/INUT_", nota.AutorizacaoSEFAZ + ".xml");
        //            ControleDeArquivos.GravarArquivo(retorno.RetornoStr, bucket + "/NFCe/" + nota.Estabelecimento.IdEstabelecimento.ToString() + "/RET_INUT_", nota.AutorizacaoSEFAZ + ".xml");
        //        }
        //        catch (Exception e) {
        //            var erro = new Exception("NFC-e: Erro na criação do xml de inutilização da nota " + erroNota.NumeroNota + " (NFe)." + nota.AutorizacaoSEFAZ + " / Estabelecimento: " + nota.Estabelecimento.NomeDeExibicaoNoPortal + "(id: " + nota.Estabelecimento.IdEstabelecimento + ")", e);
        //            GravarErroElmah(erro);
        //        }
        //    }
        //}

        public override void RealizarInutilizacoesPendentes(int quantidadeMaxima = 2)
        {
            ConfiguracaoServico = ObterConfiguracaoServico(Estabelecimento);

            ConfiguracaoServico.Certificado.Senha = SenhaDoCertificado;
            ConfiguracaoServico.Certificado.ArrayBytesArquivo = Certificado;
            ConfiguracaoServico.ProtocoloDeSeguranca = SecurityProtocolType.Tls12;

            var servicoNFe = new ServicosNFe(ConfiguracaoServico);
            RealizarInutilizacoesPendentes(servicoNFe, quantidadeMaxima);
        }

        private void RealizarInutilizacoesPendentes(ServicosNFe servicoNFe, int quantidadeMaxima = 2)
        {
            var notasInutilizadas =
                    Domain.NotaFiscalDoConsumidor.NotaInutilizadaNFCRepository.ListarNotasComNumeroNaoInutilizado(Estabelecimento.IdEstabelecimento)
                        .Take(quantidadeMaxima)
                        .ToList();

            foreach (NotaInutilizadaNFC i in notasInutilizadas)
            {
                Inutilizar(servicoNFe, i);
            }
        }

        private void Inutilizar(ServicosNFe servicoNFe, NotaInutilizadaNFC inutilizacao)
        {
            RetornoNfeInutilizacao retorno = null;

            var cnpjEmissor = Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo ?? inutilizacao.Estabelecimento.PessoaJuridica.CNPJ;

            try
            {
                var ano = inutilizacao.DataVenda.Year - 2000;

                inutilizacao.LimparMensagemErro();

                var justificativa = inutilizacao.MensagemErro;

                retorno = servicoNFe.NfeInutilizacao(cnpjEmissor, ano,
                    ModeloDocumento.NFCe, inutilizacao.NumeroSerie, inutilizacao.NumeroNota, inutilizacao.NumeroNotaFinal, justificativa);
            }
            catch (Exception ex)
            {
                inutilizacao.RetornoInutilizacaoSEFAZ = ex.Message;
                if (ex.InnerException != null)
                {
                    inutilizacao.RetornoInutilizacaoSEFAZ += ex.InnerException.Message;
                }
            }

            if (retorno != null)
            {
                if (retorno.Retorno != null
                    && retorno.Retorno.infInut != null
                    && (retorno.Retorno.infInut.cStat == 102 || retorno.Retorno.infInut.cStat == 241 || retorno.Retorno.infInut.cStat == 563))
                {
                    inutilizacao.NumeroFoiInutilizado = true;
                }
                inutilizacao.RetornoInutilizacaoSEFAZ = retorno != null ? retorno.RetornoStr : "";

                if (!string.IsNullOrEmpty(inutilizacao.RetornoInutilizacaoSEFAZ) && inutilizacao.RetornoInutilizacaoSEFAZ.Length >= 1000)
                    inutilizacao.RetornoInutilizacaoSEFAZ = inutilizacao.RetornoInutilizacaoSEFAZ.Substring(0, 999);

                inutilizacao.LimparMensagemErro();

                try
                {
                    string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
                    ControleDeArquivos.GravarArquivo(retorno.EnvioStr, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + "/INUT_", inutilizacao.NumeroNota + ".xml");
                    ControleDeArquivos.GravarArquivo(retorno.RetornoStr, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + "/RET_INUT_", inutilizacao.NumeroNota + ".xml");
                }
                catch (Exception e)
                {
                    GravarErroElmah(new Exception("NFC-e: Erro na criação do xml de inutilização da numeração " + inutilizacao.NumeroNota + " / Estabelecimento: " + Estabelecimento.NomeDeExibicaoNoPortal + " (id: " + Estabelecimento.IdEstabelecimento + ")", e));
                }
            }

            Domain.NotaFiscalDoConsumidor.NotaInutilizadaNFCRepository.Update(inutilizacao);
        }

        private void ValidarPossibilidadeEmissaoNota()
        {
            var countNotasInutilizadas = Domain.NotaFiscalDoConsumidor.NotaInutilizadaNFCRepository.ObterQuantidadeNotasComNumeroNaoInutilizado(Estabelecimento.IdEstabelecimento);
            var numeroMaximoNotasParaInutilizar = new ParametrosTrinks<int>(ParametrosTrinksEnum.numero_maximo_notas_para_inutilizar).ObterValor();

            if (countNotasInutilizadas >= numeroMaximoNotasParaInutilizar)
            {
                ValidationHelper.Instance.AdicionarItemNotificacao(
                    "Existem " + countNotasInutilizadas + " notas a serem inutilizadas, por favor entre em contato com o nosso suporte através do fale conosco!");
            }
        }

        /*
            Gera o XML que é utilizado pelas empresas de contabilidade.
            Este XML tem as informações ficais, de autenticidade e o protocolo de autorização do SEFAZ, ou seja, é o arquivo final para
            arquivamento.
        */

        public static string GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(string xmlDeEnvio, ServicosNFe servicoNFe)
        {
            var nfe = new NFe.Classes.NFe().CarregarDeXmlString(xmlDeEnvio);
            var chave = nfe.infNFe.Id.Substring(3);

            if (string.IsNullOrEmpty(chave))
                throw new Exception("A Chave da NFe não foi encontrada no arquivo!");
            if (chave.Length != 44)
                throw new Exception("Chave deve conter 44 caracteres!");

            var retornoConsulta = servicoNFe.NfeConsultaProtocolo(chave);

            var nfeproc = new nfeProc
            {
                NFe = nfe,
                protNFe = retornoConsulta.Retorno.protNFe,
                versao = retornoConsulta.Retorno.versao
            };

            return FuncoesXml.ClasseParaXmlString(nfeproc);
        }

        public static string GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(NFe.Classes.NFe nfe, ServicosNFe servicoNFe)
        {
            //var nfe = new NFe.Classes.NFe().CarregarDeXmlString(xmlDeEnvio);
            var chave = nfe.infNFe.Id.Substring(3);

            if (string.IsNullOrEmpty(chave))
                throw new Exception("A Chave da NFe não foi encontrada no arquivo!");
            if (chave.Length != 44)
                throw new Exception("Chave deve conter 44 caracteres!");

            var retornoConsulta = servicoNFe.NfeConsultaProtocolo(chave);

            var nfeproc = new nfeProc
            {
                NFe = nfe,
                protNFe = retornoConsulta.Retorno.protNFe,
                versao = retornoConsulta.Retorno.versao
            };

            return FuncoesXml.ClasseParaXmlString(nfeproc);
        }

        public static ConfiguracaoServico ObterConfiguracaoServico(Estabelecimento estabelecimento)
        {
            var ambiente = NFEAmbientes.TipoAmbiente.Homologacao;
            bool nfcProducao = false;

            if (estabelecimento.EstabelecimentoPossuiNFC())
                nfcProducao = estabelecimento.ConfiguracaoDeNFC.NFCProducaoLevandoEmContaParametro();

            if (nfcProducao)
            {
                ambiente = NFEAmbientes.TipoAmbiente.Producao;
            }

            var cfg = ConfiguracaoServico.Instancia;
            cfg.SalvarXmlServicos = false;
            cfg.TimeOut = 180000;
            cfg.cUF = (Estado)Enum.Parse(typeof(Estado), estabelecimento.PessoaJuridica.EnderecoProprio.UF.Sigla);
            var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa);
            var serialCertificado = pessoaJuridicaCertificadoDigital.SerialCertificado;
            cfg.Certificado.Serial = serialCertificado;
            cfg.tpAmb = ambiente;
            cfg.ModeloDocumento = ModeloDocumento.NFCe;
            cfg.tpEmis = NFETipos.TipoEmissao.teNormal;
            cfg.DiretorioSchemas = HttpContext.Current.Server.MapPath(@"~/content/zeus/schemas/");
            cfg.VersaoLayout = VersaoServico.Versao400;
            cfg.VersaoNFeAutorizacao = VersaoServico.Versao400;
            cfg.VersaoNFeRetAutorizacao = VersaoServico.Versao400;
            cfg.VersaoNfeRecepcao = VersaoServico.Versao400;
            cfg.VersaoNfeDownloadNF = VersaoServico.Versao400;
            cfg.VersaoNfeInutilizacao = VersaoServico.Versao400;
            cfg.VersaoNfeRecepcao = VersaoServico.Versao400;
            cfg.VersaoNfeStatusServico = VersaoServico.Versao400;
            cfg.VersaoNfeConsultaDest = VersaoServico.Versao400;
            cfg.VersaoNfeConsultaProtocolo = VersaoServico.Versao400;
            cfg.VersaoNFeDistribuicaoDFe = VersaoServico.Versao400;
            cfg.Certificado.TipoCertificado = TipoCertificado.A1ByteArray;
            cfg.VersaoNfceAministracaoCSC = VersaoServico.Versao400;
            cfg.VersaoNfeConsultaCadastro = VersaoServico.Versao400;
            cfg.VersaoNfeRetRecepcao = VersaoServico.Versao400;
            cfg.VersaoRecepcaoEventoEpec = VersaoServico.Versao400;

            return cfg;
        }

        private string ObterUrlQrCode(NFe.Classes.NFe nfe)
        {
            //return ExtinfNFeSupl.ObterUrlQrCode(nfe.infNFeSupl, nfe, Estabelecimento.ConfiguracaoDeNFC.IdentificadorCSC, Estabelecimento.ConfiguracaoDeNFC.CSC);
            return NFe.Utils.InformacoesSuplementares.ExtinfNFeSupl.ObterUrlQrCode(nfe.infNFeSupl, nfe, VersaoQrCode.QrCodeVersao2, Estabelecimento.ConfiguracaoDeNFC.IdentificadorCSC, Estabelecimento.ConfiguracaoDeNFC.CSC);

            ////Passo 1: Converter o valor da Data e Hora de Emissão da NFC-e (dhEmi) para HEXA;
            //var dhEmi = ObterHexDeString(nfe.infNFe.ide.dhEmi);

            ////Passo 2: Converter o valor do Digest Value da NFC-e (digVal) para HEXA;
            ////Ao se efetuar a assinatura digital da NFCe emitida em contingência off-line, o campo digest value constante da XMl Signature deve obrigatoriamente ser idêntico ao encontrado quando da geração do digest value para a montagem QR Code.
            ////Ver página 18 do Manual de Padrões Padrões Técnicos do DANFE - NFC - e e QR Code, versão 3.2
            //if (nfe.Signature == null)
            //    throw new Exception("Não é possível obter a URL do QR-Code de uma NFCe não assinada!");
            //var digVal = ObterHexDeString(nfe.Signature.SignedInfo.Reference.DigestValue);

            ////Passo 3: Substituir os valores (“dhEmi” e “digVal”) nos parâmetros;
            //var iCMSTot = nfe.infNFe.total.ICMSTot ?? new ICMSTot();

            ////http://dec.fazenda.df.gov.br/ConsultarNFCe.aspx
            ////?chNFe=53160802978756000135650010000000401000002968
            ////&nVersao=100
            ////&tpAmb=1
            ////&dhEmi=323031362d30382d32335430393a33323a33332d30333a3030
            ////&vNF=60.00
            ////&vICMS=0.00
            ////&digVal=41562b3350376768657762486a6b473677583079375238493877493d
            ////&cIdToken=000002
            ////&cHashQRCode=AE9425C5AFEE7ED71493FA83D7789416C8FC0199

            ////http://dec.fazenda.df.gov.br/ConsultarNFCe.aspx
            ////?chNFe=53161225310622000147650000000007291000012344
            ////&nVersao=100
            ////&tpAmb=1
            ////&cDest=35831570100
            ////&dhEmi=323031362d31322d30325431393a31323a31312d30323a3030
            ////&vNF=15.50
            ////&vICMS=0
            ////&digVal=2b5459686a4b34337362683062563577665a6556455a4b6b3631413d
            ////&cIdToken=000001
            ////&cHashQRCode=23aaf5992cce1c3aad5de1984273530575a3d74e

            //var dados = new List<string>();
            //dados.Add("chNFe=" + nfe.infNFe.Id.Substring(3));
            //dados.Add("nVersao=100");
            //dados.Add("tpAmb=" + ((int)nfe.infNFe.ide.tpAmb));

            //if (nfe.infNFe.dest != null) //Na hipótese do consumidor não se identificar, não existirá o parâmetro cDest no QR Code;
            //    dados.Add("cDest=" + nfe.infNFe.dest.CPF + nfe.infNFe.dest.CNPJ + nfe.infNFe.dest.idEstrangeiro);

            //dados.Add("dhEmi=" + dhEmi);
            //dados.Add("vNF=" + iCMSTot.vNF.ToString("0.00").Replace(',', '.'));
            //dados.Add("vICMS=" + (iCMSTot.vICMS == 0 ? "0" : iCMSTot.vICMS.ToString("0.00").Replace(',', '.')));
            //dados.Add("digVal=" + digVal);
            //dados.Add("cIdToken=" + Estabelecimento.ConfiguracaoDeNFC.IdentificadorCSC);

            //var dadosBase = string.Join("&", dados);

            ////Passo 4: Adicionar, ao final dos parâmetros, o CSC (CSC do contribuinte disponibilizado pela SEFAZ do Estado onde a empresa esta localizada):
            //var dadosParaSh1 = dadosBase + Estabelecimento.ConfiguracaoDeNFC.CSC;

            ////Passo 5: Aplicar o algoritmo SHA-1 sobre todos os parâmetros concatenados. Asaída do algoritmo SHA-1 deve ser em HEXADECIMAL.
            //var sha1ComCsc = ObterHexSha1DeString(dadosParaSh1).ToUpper();

            ////Passo 6: Adicione o resultado sem o CSC e gere a imagem do QR Code: 1º parte (endereço da consulta) +2º parte (tabela 3 com indicação SIM na última coluna).
            //return ObterUrlConsultaQrCode(nfe.infNFe.ide.tpAmb, nfe.infNFe.ide.cUF) + "?" + dadosBase + "&cHashQRCode=" + sha1ComCsc;
        }

        //novos links adicionados com base no site http://nfce.encat.org/desenvolvedor/qrcode/
        //private string ObterUrlConsultaQrCode(TipoAmbiente tipoAmbiente, Estado estado) {
        //    var ambienteDeProducao = tipoAmbiente == NFEAmbientes.TipoAmbiente.Producao;

        //    switch (estado) {
        //        case Estado.AC:
        //            return ambienteDeProducao ? "http://www.sefaznet.ac.gov.br/nfce/qrcode" : "http://hml.sefaznet.ac.gov.br/nfce/qrcode";

        //        case Estado.AL:
        //            return ambienteDeProducao ? "http://nfce.sefaz.al.gov.br/QRCode/consultarNFCe.jsp" : "http://nfce.sefaz.al.gov.br/QRCode/consultarNFCe.jsp";

        //        case Estado.AP:
        //            return ambienteDeProducao ? "https://www.sefaz.ap.gov.br/nfce/nfcep.php" : "https://www.sefaz.ap.gov.br/nfcehml/nfce.php";

        //        case Estado.AM:
        //            return ambienteDeProducao ? "http://sistemas.sefaz.am.gov.br/nfceweb/consultarNFCe.jsp" : "http://homnfce.sefaz.am.gov.br/nfceweb/consultarNFCe.jsp";

        //        case Estado.BA:
        //            return ambienteDeProducao ? "http://nfe.sefaz.ba.gov.br/servicos/nfce/modulos/geral/NFCEC_consulta_chave_acesso.aspx" : "http://hml.sefaznet.ac.gov.br/nfce/qrcode";

        //        case Estado.DF:
        //            return ambienteDeProducao ? "http://dec.fazenda.df.gov.br/ConsultarNFCe.aspx" : "http://dec.fazenda.df.gov.br/ConsultarNFCe.aspx";

        //        case Estado.GO://http://www.nfe.go.gov.br/post/ver/213515/alteracao-na-url-de-consulta-nfc-e
        //            return ambienteDeProducao ? "http://nfe.sefaz.go.gov.br/nfeweb/sites/nfce/danfeNFCe" : "http://homolog.sefaz.go.gov.br/nfeweb/sites/nfce/danfeNFCe";

        //        case Estado.MA:
        //            return ambienteDeProducao ? "http://www.nfce.sefaz.ma.gov.br/portal/consultarNFCe.jsp" : "http://homolog.sefaz.go.gov.br/nfeweb/jsp/ConsultaDANFENFCe.jsf​​";

        //        case Estado.MT:
        //            return ambienteDeProducao ? "http://www.sefaz.mt.gov.br/nfce/consultanfce" : "http://homologacao.sefaz.mt.gov.br/nfce/consultanfce";

        //        case Estado.MS:
        //            return ambienteDeProducao ? "http://www.dfe.ms.gov.br/nfce/qrcode" : "http://www.dfe.ms.gov.br/nfce/qrcode";

        //        case Estado.PA:
        //            return ambienteDeProducao ? "https://appnfc.sefa.pa.gov.br/portal-homologacao/view/consultas/nfce/nfceForm.seam" : "https://appnfc.sefa.pa.gov.br/portal-homologacao/view/consultas/nfce/nfceForm.seam";

        //        case Estado.PB:
        //            return ambienteDeProducao ? "https://www5.receita.pb.gov.br/atf/seg/SEGf_AcessarFuncao.jsp?cdFuncao=FIS_1410" : "https://www6.receita.pb.gov.br/atf/seg/SEGf_AcessarFuncao.jsp?cdFuncao=FIS_1410";

        //        case Estado.PI:
        //            return ambienteDeProducao ? "http://webas.sefaz.pi.gov.br/nfceweb/consultarNFCe.jsf" : "http://webas.sefaz.pi.gov.br/nfceweb-homologacao/consultarNFCe.jsf";

        //        case Estado.PR:
        //            return ambienteDeProducao ? "http://www.dfeportal.fazenda.pr.gov.br/dfe-portal/rest/servico/consultaNFCe" : "http://www.dfeportal.fazenda.pr.gov.br/dfe-portal/rest/servico/consultaNFCe";

        //        case Estado.PE:
        //            return ambienteDeProducao ? "http://nfce.sefaz.pe.gov.br/nfce-web/consultarNFCe" : "http://nfcehomolog.sefaz.pe.gov.br/nfce-web/consultarNFCe";

        //        case Estado.RJ:
        //            return ambienteDeProducao ? "http://www4.fazenda.rj.gov.br/consultaNFCe/QRCode" : "http://www4.fazenda.rj.gov.br/consultaNFCe/QRCode";

        //        case Estado.RN:
        //            return ambienteDeProducao ? "http://nfce.set.rn.gov.br/consultarNFCe.aspx" : "http://nfce.set.rn.gov.br/consultarNFCe.aspx";

        //        case Estado.RO:
        //            return ambienteDeProducao ? "http://www.nfce.sefin.ro.gov.br/consultanfce/consulta.jsp" : "http://www.nfce.sefin.ro.gov.br/consultanfce/consulta.jsp";

        //        case Estado.RR:
        //            return ambienteDeProducao ? "https://www.sefaz.rr.gov.br/nfce/servlet/qrcode" : "http://**************:8080/nfce/servlet/qrcode";

        //        case Estado.RS:
        //            return ambienteDeProducao ? "https://www.sefaz.rs.gov.br/NFCE/NFCE-COM.aspx" : "https://www.sefaz.rs.gov.br/NFCE/NFCE-COM.aspx";

        //        case Estado.SE:
        //            return ambienteDeProducao ? "http://www.nfce.se.gov.br/portal/consultarNFCe.jsp" : "http://www.hom.nfe.se.gov.br/portal/consultarNFCe.jsp";

        //        case Estado.SP:
        //            return ambienteDeProducao ? "https://www.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaQRCode.aspx" : "https://www.homologacao.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaQRCode.aspx";

        //        //PARA ESPIRITO SANTO A URL TANTO DE HOMOLOGAÇÃO COMO DE PRODUÇÃO É A MESMA
        //        //FONTE: http://internet.sefaz.es.gov.br/informacoes/nfce/qrcode.php
        //        case Estado.ES:
        //            return "http://homologacao.sefaz.es.gov.br/ConsultaNFCe/qrcode.aspx?";

        //        default:
        //            throw new Exception(string.Format("Não foi possível obter o QR Code, para o Estado {0}, ambiente: {1}", estado, NFEAmbientes.TipoAmbiente.taDescricao()));
        //    }
        //}

        private string ObterHexSha1DeString(string s)
        {
            var bytes = Encoding.UTF8.GetBytes(s);

            var sha1 = SHA1.Create();
            var hashBytes = sha1.ComputeHash(bytes);

            return ObterHexDeByteArray(hashBytes);
        }

        private string ObterHexDeByteArray(byte[] bytes)
        {
            var sb = new StringBuilder();
            foreach (var b in bytes)
            {
                var hex = b.ToString("x2");
                sb.Append(hex);
            }
            return sb.ToString();
        }

        private string ObterHexDeString(string s)
        {
            var hex = "";
            foreach (var c in s)
            {
                int tmp = c;
                hex += string.Format("{0:x2}", Convert.ToUInt32(tmp.ToString()));
            }
            return hex;
        }

        private NFe.Classes.NFe GetNf(int numero, int numeroSerie, ModeloDocumento modelo, VersaoServico versao, DateTime dataAtual, bool ambienteProducao)
        {
            var emit = GetEmitente(ambienteProducao);
            var infNFe = new infNFe
            {
                versao = ServicoNFe.NfeStatusServico.VersaoServicoParaString(versao),
                ide = GetIdentificacao(numero, numeroSerie, modelo, versao, dataAtual, emit.CNPJ),
                emit = emit,
                dest = GetDestinatario(versao, ambienteProducao),
                transp = new transp { modFrete = ModalidadeFrete.mfSemFrete }
            };

            //var estadosRespTec = new[] { "AM", "MS", "PE", "PR", "SC", "TO" };

            //if (estadosRespTec.Contains(Estabelecimento.PessoaJuridica.EnderecoProprio.UF.Sigla))
            infNFe.infRespTec = new infRespTec { CNPJ = "73499238000187", xContato = "MARCEL GEWERC", email = "<EMAIL>", fone = "2122835151" };

            IncluirFormasDePagamentoComValorLiquido(infNFe, versao);

            var totalVpag = infNFe.pag.Sum(p => p.detPag.Sum(d => d.vPag));

            //Solução para evitar erro de troco devido a divisão de forma de pagamento baseado em porcentagem, em fechamentos que possuam tanto serviço quanto produto e a nfc-e realiza somente emissões de produto.
            //if (totalVpag > NotaNFC.TotalLiquido && infNFe.pag.Any() && infNFe.pag.LastOrDefault().detPag.Any())
            //    infNFe.pag.LastOrDefault().detPag.LastOrDefault().vPag = infNFe.pag.LastOrDefault().detPag.LastOrDefault().vPag - (totalVpag - NotaNFC.TotalLiquido);

            decimal impostoTotal = 0;
            infNFe.det = new List<det>();
            var cestEstaHabilitado = Domain.NotaFiscalDoConsumidor.InformacoesDeNFCService.CampoCESTEstaHabilitadoParaOsEstabelecimentos();
            var i = 0;

            foreach (var item in NotaNFC.Itens)
            {
                det det = GerarDet(item, cestEstaHabilitado, ambienteProducao, ref impostoTotal, ref i);
                infNFe.det.Add(det);
            }

            infNFe.total = GetTotal(versao, infNFe.det);

            //foreach(var det in infNFe.det) {
            //    var imposto = det.imposto.
            //}

            impostoTotal = infNFe.det.Sum(d => d.imposto.vTotTrib ?? 0);

            infNFe.total.ICMSTot.vTotTrib = impostoTotal;

            if (infNFe.pag.Any())
            {
                var valorPago = (infNFe.pag.Sum(p => p.detPag.Sum(d => d.vPag)) - infNFe.pag.Sum(p => p.vTroco));
                if (infNFe.total.ICMSTot.vNF != valorPago)
                    infNFe.total.ICMSTot.vNF = valorPago.Value;
            }

            var configNFCeDoUF = ConfiguracaoDeNFCNoEstado;

            var textoImpostoTotal = impostoTotal.ValorDecimal();
            var procentagemImposto = 0m;

            if (NotaNFC.TotalLiquido > 0)
                procentagemImposto = 100 * (impostoTotal / NotaNFC.TotalLiquido);

            var textoTotalTributos = Domain.BaseIBPT.IBPTService.ObterTextoInformandoValorDosImpostos(impostoTotal, procentagemImposto, Estabelecimento.PessoaJuridica);



            if (configNFCeDoUF != null && !String.IsNullOrEmpty(configNFCeDoUF.ObservacaoCorpoNFCE))
                infNFe.infAdic = GetInfAdic(configNFCeDoUF.ObservacaoCorpoNFCE + " " + textoTotalTributos);
            else
                infNFe.infAdic = GetInfAdic(textoTotalTributos);

            infNFe.infAdic.infCpl += ObterTextoComplementarPorRegimeTributario();

            var tipoEmissaoNfeParceiro = Estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitarProfissionalParceiro ? Estabelecimento.EstabelecimentoConfiguracaoGeral.TipoDeEmissaoNfeComProfissionalParceiro : null;
            if (tipoEmissaoNfeParceiro != null && tipoEmissaoNfeParceiro == Pessoas.Statics.TipoNfeProfissionalParceiro.NotaUnica.Id)
                infNFe.infAdic.infCpl += " Operacao realizada conforme contrato abrangido pela Lei 12592/2012";

            var nf = new NFe.Classes.NFe { infNFe = infNFe };

            return nf;
        }

        private string ObterTextoComplementarPorRegimeTributario()
        {
            var regimeTributario = Estabelecimento.ConfiguracaoDeNFC != null ? Estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario : null;
            var configNFCeDoUF = ConfiguracaoDeNFCNoEstado;
            if (regimeTributario != null && configNFCeDoUF != null)
            {
                if (regimeTributario.IdTipoRegimeTributarioNFC == (int)TipoDeRegimeTributarioDeUmEstabelecimento.SimplesNacional)
                {
                    return configNFCeDoUF.TextoComplementarSimplesNacional;
                }
                else
                {
                    return configNFCeDoUF.TextoComplementarLucroPresumido;
                }
            }
            return string.Empty;
        }

        private det GerarDet(NotaItensNFC item, bool cestEstaHabilitado, bool ambienteProducao, ref decimal impostoTotal, ref int i)
        {
            if (item.EstabelecimentoProduto != null)
                return GeraDetProduto(item, cestEstaHabilitado, ambienteProducao, ref impostoTotal, ref i);
            else
                return GeraDetServico(item, ambienteProducao, ref impostoTotal, ref i);
        }

        private det GeraDetProduto(NotaItensNFC item, bool cestEstaHabilitado, bool ambienteProducao, ref decimal impostoTotal, ref int i)
        {
            var estabelecimentoProduto = item.EstabelecimentoProduto;

            var nfcNcmNbs = Domain.NotaFiscalDoConsumidor.TabelaIBPTRepository.ObterAliquotasPorCodigoeUF(estabelecimentoProduto.CodigoNCM, Estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF);

            if (nfcNcmNbs == null)
                throw new Exception("Verifique o NCM do produto " + estabelecimentoProduto.Descricao);

            var aliquotaProduto =
                ((estabelecimentoProduto.ProdutoEhNacional ? nfcNcmNbs.AliquotaFederalNacional : nfcNcmNbs.AliquotaFederalImportado) +
                nfcNcmNbs.AliquotaEstadual + nfcNcmNbs.AliquotaMunicipal) / 100;

            decimal impostoProduto;
            impostoProduto = (item.PrecoUnitario * aliquotaProduto);
            impostoTotal += impostoProduto;

            var produtoPossuiCestValido = !string.IsNullOrEmpty(estabelecimentoProduto.CEST) && estabelecimentoProduto.CEST.Length == 7;

            int ncm = int.Parse(nfcNcmNbs.Codigo.SomenteNumeros());

            var codigoPIS = !string.IsNullOrEmpty(estabelecimentoProduto.CodigoPis) ? (CSTPIS)Enum.Parse(typeof(CSTPIS), estabelecimentoProduto.CodigoPis.LarguraFixa(2)) : CSTPIS.pis99;
            var codigoCofins = !string.IsNullOrEmpty(estabelecimentoProduto.CodigoCofins) ? (CSTCOFINS)Enum.Parse(typeof(CSTCOFINS), estabelecimentoProduto.CodigoCofins.LarguraFixa(2)) : CSTCOFINS.cofins99;

            //GTIN - Após criar o campo de GTIN no cadastro de produtos, alterar aqui - NFC-e 4.0
            var impostoObj = new imposto
            {
                vTotTrib = impostoProduto,
                ICMS = new ICMS { TipoICMS = InformarCSOSN(item) },
                COFINS = new COFINS { TipoCOFINS = new COFINSOutr { CST = codigoCofins, pCOFINS = 0, vBC = 0, vCOFINS = 0 } },
                PIS = new PIS { TipoPIS = new PISOutr { CST = codigoPIS, pPIS = 0, vBC = 0, vPIS = 0 } }
            };

            if (!string.IsNullOrWhiteSpace(estabelecimentoProduto.CodigoCofins) && !string.IsNullOrWhiteSpace(estabelecimentoProduto.CodigoPis))
            {
                var vBC = item.ValorBrutoTotal == 0 ? 0 : item.ValorBrutoTotal;
                var CSTCofins = (CSTCOFINS)(decimal.Parse(estabelecimentoProduto.CodigoCofins.LarguraFixa(2), CultureInfo.InvariantCulture));
                var CSTPis = (CSTPIS)(decimal.Parse(estabelecimentoProduto.CodigoPis.LarguraFixa(2), CultureInfo.InvariantCulture));

                if (int.Parse(estabelecimentoProduto.CodigoCofins) >= 49 && int.Parse(estabelecimentoProduto.CodigoPis) >= 49
                    && estabelecimentoProduto.AliquotaCOFINS > 0 && estabelecimentoProduto.AliquotaPIS > 0)
                {
                    impostoObj = new imposto
                    {
                        vTotTrib = impostoProduto,
                        ICMS = new ICMS
                        {
                            TipoICMS = InformarCSOSN(item),
                        },
                        COFINS = new COFINS
                        {
                            TipoCOFINS = new COFINSOutr
                            {
                                CST = CSTCofins,
                                pCOFINS = estabelecimentoProduto.AliquotaCOFINS ?? 0,
                                vBC = estabelecimentoProduto.AliquotaCOFINS != null && estabelecimentoProduto.AliquotaCOFINS > 0 ? vBC : 0,
                                vCOFINS = estabelecimentoProduto.AliquotaCOFINS != null && estabelecimentoProduto.AliquotaCOFINS > 0 ? (vBC * estabelecimentoProduto.AliquotaCOFINS / 100) : 0
                            }
                        },
                        PIS = new PIS
                        {
                            TipoPIS = new PISOutr
                            {
                                CST = CSTPis,
                                pPIS = estabelecimentoProduto.AliquotaPIS ?? 0,
                                vBC = estabelecimentoProduto.AliquotaPIS != null && estabelecimentoProduto.AliquotaPIS > 0 ? vBC : 0,
                                vPIS = estabelecimentoProduto.AliquotaPIS != null && estabelecimentoProduto.AliquotaPIS > 0 ? (vBC * estabelecimentoProduto.AliquotaPIS / 100) : 0
                            }
                        }
                    };
                }

                if (int.Parse(estabelecimentoProduto.CodigoCofins) <= 2 && int.Parse(estabelecimentoProduto.CodigoPis) <= 2)
                {
                    impostoObj = new imposto
                    {
                        vTotTrib = impostoProduto,
                        ICMS = new ICMS
                        {
                            TipoICMS = InformarCSOSN(item),
                        },
                        COFINS = new COFINS
                        {
                            TipoCOFINS = new COFINSAliq
                            {
                                CST = CSTCofins,
                                vBC = estabelecimentoProduto.AliquotaCOFINS != null && estabelecimentoProduto.AliquotaCOFINS > 0 ? vBC : 0,
                                pCOFINS = estabelecimentoProduto.AliquotaCOFINS ?? 0,
                                vCOFINS = estabelecimentoProduto.AliquotaCOFINS != null && estabelecimentoProduto.AliquotaCOFINS > 0 ? (vBC * estabelecimentoProduto.AliquotaCOFINS.Value / 100) : 0
                            }
                        },
                        PIS = new PIS
                        {
                            TipoPIS = new PISAliq
                            {
                                CST = CSTPis,
                                vBC = estabelecimentoProduto.AliquotaPIS != null && estabelecimentoProduto.AliquotaPIS > 0 ? vBC : 0,
                                pPIS = estabelecimentoProduto.AliquotaPIS ?? 0,
                                vPIS = estabelecimentoProduto.AliquotaPIS != null && estabelecimentoProduto.AliquotaPIS > 0 ? (vBC * estabelecimentoProduto.AliquotaPIS.Value / 100) : 0
                            }
                        }
                    };
                }

                if (int.Parse(estabelecimentoProduto.CodigoCofins) >= 4 && int.Parse(estabelecimentoProduto.CodigoCofins) <= 9
                    && int.Parse(estabelecimentoProduto.CodigoPis) >= 4 && int.Parse(estabelecimentoProduto.CodigoPis) <= 9)
                {
                    impostoObj = new imposto
                    {
                        vTotTrib = impostoProduto,
                        ICMS = new ICMS
                        {
                            TipoICMS = InformarCSOSN(item),
                        },
                        COFINS = new COFINS
                        {
                            TipoCOFINS = new COFINSNT
                            {
                                CST = CSTCofins,
                            }
                        },
                        PIS = new PIS
                        {
                            TipoPIS = new PISNT
                            {
                                CST = CSTPis
                            }
                        }
                    };
                }
            }

            var det = new det()
            {
                nItem = ++i,
                prod = new prod
                {
                    cProd = "P" + estabelecimentoProduto.Id,
                    cEAN = !string.IsNullOrWhiteSpace(estabelecimentoProduto.CodigoBarras) ? estabelecimentoProduto.CodigoBarras : "SEM GTIN",
                    NCM = ncm.ToString("D8"),
                    CFOP = ObterCFOP(estabelecimentoProduto),
                    cEANTrib = !string.IsNullOrWhiteSpace(estabelecimentoProduto.CodigoBarras) ? estabelecimentoProduto.CodigoBarras : "SEM GTIN",
                    xProd = ambienteProducao ? estabelecimentoProduto.Descricao.RemoveDiacritics().SomenteLetrasENumeros().Trim() : _nomeItemParaAmbienteHomologacao,
                    uCom = "UNID",
                    vProd = item.ValorBrutoTotal,
                    vDesc = (-1) * item.DescontoUnitario * item.Quantidade,
                    vUnCom = item.ValorBrutoTotal / item.Quantidade,
                    qCom = item.Quantidade,
                    indTot = IndicadorTotal.ValorDoItemCompoeTotalNF,
                    qTrib = item.Quantidade,
                    uTrib = "UNID",
                    vUnTrib = item.ValorBrutoTotal == 0 ? 0 : (item.ValorBrutoTotal / item.Quantidade)
                },
                imposto = impostoObj
            };

            if (cestEstaHabilitado && produtoPossuiCestValido)
            {
                det.prod.CEST = estabelecimentoProduto.CEST;
            }
            return det;
        }

        private det GeraDetServico(NotaItensNFC item, bool ambienteProducao, ref decimal impostoTotal, ref int i)
        {
            var servicoEstabelecimento = item.ServicoEstabelecimento;
            var estabelecimento = servicoEstabelecimento.Estabelecimento;
            var pessoaJuridica = estabelecimento.PessoaJuridica;

            var codigoServico = estabelecimento.ConfiguracaoDeNFC.ItemListaServico;
            if (string.IsNullOrWhiteSpace(codigoServico))
                throw new ArgumentException("Nas configurações de Nota do Comsumidor, preencha o campo Item Lista Serviço.");

            var aliquotaISS = estabelecimento.ConfiguracaoDeNFC.AliquotaISS;
            aliquotaISS = aliquotaISS > 0 ? decimal.Round(aliquotaISS, 2) : 0;

            var aliquota = Domain.NotaFiscalDoConsumidor.TabelaIBPTRepository.ObterAliquotasPorCodigoeUF(item.ServicoEstabelecimento.CodigoNCM, pessoaJuridica.EnderecoProprio.UF.IdUF);
            decimal impostoServico = 0;

            if (aliquota != null)
                impostoServico = ((aliquota.AliquotaFederalNacional + aliquota.AliquotaEstadual + aliquota.AliquotaMunicipal) / 100) * item.ValorLiquidoTotal;

            impostoTotal += impostoServico;

            var registro = Registros.FirstOrDefault();
            var servico = new ServicoDoRpsDTO();

            if (registro != null)
                servico = registro.Servicos.FirstOrDefault(s => s.IdItem == item.CodigoHorarioTransacao);

            if (servico == null)
                servico = registro.Servicos.FirstOrDefault(s => s.IdServicoEstabelecimento == item.ServicoEstabelecimento.IdServicoEstabelecimento);

            //if (aliquotaISS == 0) throw new ArgumentException("É necessário que seja definido um valor de alíquota ISS nas configurações de Nota do Consumidor");
            var vISS = Math.Round(((servico != null && servico.ValorBase > 0 ? servico.ValorBase : Math.Round(item.ValorLiquidoTotal, 2)) / 100) * aliquotaISS, 2);

            var ISSQN = new NFe.Classes.Informacoes.Detalhe.Tributacao.Municipal.ISSQN
            {
                vBC = servico != null && servico.ValorBase > 0 ? servico.ValorBase : item.ValorLiquidoTotal,
                cMunFG = long.Parse(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE),
                vAliq = aliquotaISS,
                vISSQN = vISS,
                cListServ = codigoServico
            };

            //Ativar para todos após aval dos contadores de Brasília
            //Efficace Depilação 403 Norte (Estabelecimento piloto)
            var indIss = estabelecimento.ConfiguracaoDeNFC.IndIss;
            if (indIss != null)
                ISSQN.indISS = (IndicadorISS)indIss.Value;

            var indIncentivoVal = estabelecimento.ConfiguracaoDeNFC.IndIncentivo;
            if (indIncentivoVal != null)
                ISSQN.indIncentivo = indIncentivoVal.Value ? indIncentivo.iiSim : indIncentivo.iiNao;

            var imposto = new imposto
            {
                ISSQN = ISSQN,
                vTotTrib = impostoServico
                //PIS = new PIS() {
                //    TipoPIS = new PISAliq {
                //        CST = CSTPIS.pis01,
                //        vBC = 0,
                //        pPIS = 0,
                //        vPIS = 0
                //    }
                //},
                //COFINS = new COFINS {
                //    TipoCOFINS = new COFINSAliq {
                //        CST = CSTCOFINS.cofins01,
                //        pCOFINS = 0,
                //        vBC = 0,
                //        vCOFINS = 0
                //    }
                //}
            };

            i++;

            var prodDados = new prod
            {
                cProd = "S" + item.ServicoEstabelecimento.IdServicoEstabelecimento,
                NCM = "00",
                cEAN = "SEM GTIN",
                cEANTrib = "SEM GTIN",
                CFOP = _cfopServico, // Cliente e estabelecimento no mesmo UF 5933 / UF diferente 6933,
                xProd = ambienteProducao ? item.ServicoEstabelecimento.Nome.Trim().RemoveDiacritics().SomenteLetrasENumeros().Trim() : _nomeItemParaAmbienteHomologacao,
                uCom = "UNID",
                vUnCom = item.ValorLiquidoTotal / item.Quantidade,
                qCom = item.Quantidade,
                indTot = IndicadorTotal.ValorDoItemCompoeTotalNF,
                qTrib = item.Quantidade,
                uTrib = "UNID",
                vUnTrib = item.ValorLiquidoTotal / item.Quantidade
            };
            prodDados.vProd = prodDados.vUnTrib * prodDados.qTrib;

            var det = new det
            {
                nItem = i,
                prod = prodDados,
                imposto = imposto,
                infAdProd = servico != null && servico.DescricaoCotas != null ? servico.DescricaoCotas : null
            };

            return det;
        }

        private void IncluirFormasDePagamentoComValorLiquido(infNFe infNFe, VersaoServico versao)
        {
            infNFe.pag = new List<pag>();
            //var notaTecnicaJaEstaImplantada = new ParametrosTrinks<bool>(ParametrosTrinksEnum.nota_tecnica_2015_implantada).ObterValor();

            Decimal valorDoTroco = 0;
            foreach (var item in NotaNFC.FormasPagamento)
            {
                if (valorDoTroco != 0 && item.ValorPagamento < 0)
                    throw new Exception("Verificar inconsistência, pois foram detectados dois valores negativos (troco) nas formas de pagamento."); // Condição de guarda
                if (item.ValorPagamento < 0)
                    valorDoTroco = item.ValorPagamento;
            }

            var valorLiquido = NotaNFC.TotalLiquido;
            var valorSemTroco = ObterFormasDePagamentoQueVaoEntrarNaNota(NotaNFC.FormasPagamento).Sum(f => f.ValorPagamento);
            var todasAsFormas = NotaNFC.FormasPagamento.ToList();

            var pag = new pag();

            if (versao == VersaoServico.Versao400)
            {
                pag = new pag
                {
                    detPag = new List<detPag>(),
                    vTroco = -valorDoTroco
                };
            }

            foreach (var item in NotaNFC.FormasPagamento)
            {
                var valorPagamentoLiquido = item.ValorPagamento;

                if (valorPagamentoLiquido > 0)
                {
                    var formaTipoEnum = XmlEnumExtensions.FromReflectedXmlValue<enumFormaPagamentoNFe>(((int)item.FormaPagamento.Value).ToString("00"));
                    var tPag = (enumFormaPagamentoNFe)formaTipoEnum;

                    if (versao == VersaoServico.Versao400)
                    {
                        var detPag = new detPag
                        {
                            //indPag = NFEAmbientes.IndicadorPagamentoDetalhePagamento.ipDetPgVista,
                            tPag = tPag,
                            vPag = valorPagamentoLiquido,
                        };

                        if (formaTipoEnum == enumFormaPagamentoNFe.fpCartaoCredito || formaTipoEnum == enumFormaPagamentoNFe.fpCartaoDebito)
                        {
                            detPag.card = new card
                            {
                                tpIntegra = TipoIntegracaoPagamento.TipNaoIntegrado,
                                tBand = BandeiraCartao.bcOutros

                            };
                        }

                        if (formaTipoEnum == enumFormaPagamentoNFe.fpPagamentoInstantaneio)
                        {
                            detPag.card = new card
                            {
                                tpIntegra = TipoIntegracaoPagamento.TipNaoIntegrado,
                                cAut = NotaNFC.IdNotaNFC.ToString()
                            };
                        }

                        if (formaTipoEnum == enumFormaPagamentoNFe.fpOutro)
                            detPag.xPag = "Pagamento Digital";

                        pag.detPag.Add(detPag);

                        if (tPag == enumFormaPagamentoNFe.fpBoletoBancario)
                        {
                            infNFe.cobr = new cobr()
                            {
                                dup = new List<dup>() {
                                new dup() {
                                    nDup = "001",
                                    dVenc = DateTime.Now.AddDays(5),
                                    vDup = valorPagamentoLiquido
                                }
                            },
                                fat = new fat()
                                {
                                    nFat = "1234",
                                    vDesc = infNFe.det.Sum(p => p.prod.vDesc ?? 0),
                                    vLiq = valorPagamentoLiquido,
                                    vOrig = valorPagamentoLiquido - infNFe.det.Sum(p => p.prod.vDesc ?? 0)
                                }
                            };
                        }
                    }
                    else
                    {
                        pag = new pag
                        {
                            tPag = tPag,
                            vPag = valorPagamentoLiquido
                        };
                    }

                    if (//notaTecnicaJaEstaImplantada &&
                        (tPag == enumFormaPagamentoNFe.fpCartaoCredito ||
                         tPag == enumFormaPagamentoNFe.fpCartaoDebito))
                    {
                        var tpIntegra = (TipoIntegracaoPagamento)Estabelecimento.ConfiguracaoDeNFC.TipoIntegracaoPagamento;

                        var valorcAut = Domain.Financeiro.TransacaoPOSRepository.ObterAcquirerTransactionKeyPorIdTransacao(Transacao.Id);
                        var tipoPos = Domain.Financeiro.TransacaoPOSRepository.ObterIdTipoPos(Transacao.Id);

                        if (tpIntegra == TipoIntegracaoPagamento.TipIntegradoAutomacao && !string.IsNullOrEmpty(valorcAut))
                        {
                            //throw new Exception("O tipo de integração de pagamento configurado para o estabelecimento ainda não foi implementado.");
                            pag.detPag.FirstOrDefault(p => p.tPag == enumFormaPagamentoNFe.fpCartaoCredito || p.tPag == enumFormaPagamentoNFe.fpCartaoDebito).card = new card()
                            {
                                tpIntegra = tpIntegra,
                                tBand = BandeiraCartao.bcOutros,
                                cAut = valorcAut,
                                CNPJ = tipoPos.Id == (int)SubadquirenteEnum.StoneSiclos ? "16501555000157" : null

                            };
                        }
                        else
                        {
                            if (versao == VersaoServico.Versao400)
                            {
                                pag.detPag.FirstOrDefault(p => p.tPag == enumFormaPagamentoNFe.fpCartaoCredito || p.tPag == enumFormaPagamentoNFe.fpCartaoDebito).card = new card()
                                {
                                    tpIntegra = tpIntegra,
                                    tBand = BandeiraCartao.bcOutros
                                };
                            }
                            else
                            {
                                pag.card = new card()
                                {
                                    tpIntegra = tpIntegra,
                                    tBand = BandeiraCartao.bcOutros
                                };
                            }
                        }
                    }

                    if (versao != VersaoServico.Versao400)
                        infNFe.pag.Add(pag);
                }
            }

            if (versao == VersaoServico.Versao400)
                infNFe.pag.Add(pag);
        }

        private static IEnumerable<NotaFormaPagamentoNFC> ObterFormasDePagamentoQueVaoEntrarNaNota(IEnumerable<NotaFormaPagamentoNFC> todasAsFormas)
        {
            return todasAsFormas.Where(f => f.ValorPagamento > 0);
        }

        public static decimal ObterTotalPagoNaNota(NotaNFC nota)
        {
            decimal totalPago = 0;
            var todasAsFormas = Domain.NotaFiscalDoConsumidor.NotaFormaPagamentoNFCRepository.ObterFormasDePagamentoDaNota(nota);
            var formasDePagamentoNaNota = ObterFormasDePagamentoQueVaoEntrarNaNota(todasAsFormas);
            foreach (var formaDePagamento in formasDePagamentoNaNota)
            {
                totalPago += ObterValorPagamentoLiquidoProporcional(formaDePagamento, nota, todasAsFormas);
            }

            return totalPago;
        }

        private static decimal ObterValorPagamentoLiquidoProporcional(NotaFormaPagamentoNFC formaDePagamento, NotaNFC nota, List<NotaFormaPagamentoNFC> todasAsFormas)
        {
            var valorLiquido = nota.TotalLiquido;
            var valorSemTroco = todasAsFormas.Where(f => f.ValorPagamento > 0).Sum(f => f.ValorPagamento);

            return formaDePagamento.ValorPagamento / valorSemTroco * valorLiquido;
        }

        private int ObterCFOP(EstabelecimentoProduto umProdutoDaNota)
        {
            var situacaoTributaria = Domain.NotaFiscalDoConsumidor.NfcSituacaoTributariaRepository.Load(umProdutoDaNota.IdSituacaoTributaria.Value);

            //http://www.oobj.com.br/bc/article/rejei%C3%A7%C3%A3o-725-nfc-e-com-cfop-inv%C3%A1lido-como-resolver-143.html

            // Substituição Tributária
            /*
                Venda de mercadoria, adquirida ou recebida de terceiros, sujeita ao regime de
                substituição tributária, na condição de contribuinte-substituído
            */
            if ("F".Equals(situacaoTributaria.Codigo))
                return 5405;
            /*
               Venda de mercadoria adquirida ou recebida de terceiros
               CFOP = Para produtos com Estabelecimento_Produto.fabricacao_propria = TRUE seu valor é “5101”, caso contrário seu valor é “5102”.
            */
            if ("T".Equals(situacaoTributaria.Codigo))
                return umProdutoDaNota.FabricacaoPropria ? 5101 : 5102;

            // Isento

            if ("I".Equals(situacaoTributaria.Codigo))
                return umProdutoDaNota.FabricacaoPropria ? 5101 : 5102;

            // Não tributado

            if ("N".Equals(situacaoTributaria.Codigo))
                return umProdutoDaNota.FabricacaoPropria ? 5101 : 5102;

            throw new Exception("A situação tributária do produto não está prevista para a envio ao SEFAZ.");
        }

        protected virtual infAdic GetInfAdic(String observacao)
        {
            var infAdic = new infAdic()
            {
                infCpl = observacao.Trim()
            };

            return infAdic;
        }

        protected virtual total GetTotal(VersaoServico versao, List<det> itens)
        {
            var produtos = itens.Where(f => f.prod.CFOP != _cfopServico);
            var servicos = itens.Where(f => f.prod.CFOP == _cfopServico);
            var pisType = produtos.Select(p => p.imposto.PIS.TipoPIS.GetType()).FirstOrDefault();
            var vpis = produtos.Sum(p => p.imposto.PIS.TipoPIS.GetPisValue());
            var vcofins = produtos.Sum(p => p.imposto.COFINS.TipoCOFINS.GetCofinsValue());

            var t = new total();

            var icmsTot = new ICMSTot
            {
                vProd = produtos.Sum(p => p.prod.vProd),
                vNF = itens.Sum(p => p.prod.vProd) - itens.Sum(p => p.prod.vDesc ?? 0),
                vDesc = itens.Sum(p => p.prod.vDesc ?? 0),
                vTotTrib = itens.Sum(p => p.imposto.vTotTrib ?? 0),
            };
            if (versao == VersaoServico.Versao310)
                icmsTot.vICMSDeson = 0;

            if (versao == VersaoServico.Versao400)
            {
                icmsTot.vICMSDeson = 0;
                icmsTot.vFCPUFDest = 0;
                icmsTot.vICMSUFDest = 0;
                icmsTot.vICMSUFRemet = 0;
                icmsTot.vFCP = 0;
                icmsTot.vBCST = 0;
                icmsTot.vST = 0;
                icmsTot.vFCPST = 0;
                icmsTot.vFCPSTRet = 0;
                icmsTot.vFrete = 0;
                icmsTot.vSeg = 0;
                //icmsTot.vDesc = 0;
                icmsTot.vII = 0;
                icmsTot.vIPI = 0;
                icmsTot.vIPIDevol = 0;
                icmsTot.vPIS = vpis;
                icmsTot.vCOFINS = vcofins;
                icmsTot.vOutro = 0;
            }

            foreach (var produto in itens)
            {
                if (produto.imposto.IPI != null && produto.imposto.IPI.TipoIPI.GetType() == typeof(IPITrib))
                    icmsTot.vIPI = icmsTot.vIPI + ((IPITrib)produto.imposto.IPI.TipoIPI).vIPI ?? 0;

                if (produto.imposto.ICMS != null && produto.imposto.ICMS.TipoICMS != null)
                {
                    if (produto.imposto.ICMS.TipoICMS.GetType() == typeof(ICMS00))
                    {
                        icmsTot.vBC = icmsTot.vBC + ((ICMS00)produto.imposto.ICMS.TipoICMS).vBC;
                        icmsTot.vICMS = icmsTot.vICMS + ((ICMS00)produto.imposto.ICMS.TipoICMS).vICMS;
                    }
                    if (produto.imposto.ICMS.TipoICMS.GetType() == typeof(ICMS20))
                    {
                        icmsTot.vBC = icmsTot.vBC + ((ICMS20)produto.imposto.ICMS.TipoICMS).vBC;
                        icmsTot.vICMS = icmsTot.vICMS + ((ICMS20)produto.imposto.ICMS.TipoICMS).vICMS;
                    }
                }
            }

            t.ICMSTot = icmsTot;

            if (servicos.Any())
            {
                var vBc = servicos.Sum(f => f.imposto.ISSQN.vBC);
                var vServ = servicos.Sum(f => f.prod.vProd);

                var valores = from s in servicos
                              let valor = ((s.imposto.ISSQN.vBC - (s.prod.vDesc ?? 0)) / 100) * (decimal.Parse(s.imposto.ISSQN.vAliq.ToString().Replace(".", ",")))
                              select Math.Round(valor, 2);
                var vISS = valores.Sum();
                var vISSString = vISS > 0 ? decimal.Round(vISS, 2) : (decimal?)null;

                var iSSQNtot = new ISSQNtot
                {
                    vServ = vServ,
                    vBC = vBc,
                    vISS = vISSString > 0 ? vISSString : (decimal?)null,
                    dCompet = Transacao.DataHora.ToString("yyyy-MM-dd")
                };

                t.ISSQNtot = iSSQNtot;
            }

            return t;
        }

        protected virtual ICMSBasico InformarCSOSN(NotaItensNFC item)
        {
            var umProdutoDaNota = item.EstabelecimentoProduto;
            // Condição de guarda: O ICSM abaixo só está preparado para empresas no Simples Nacional.
            if (Estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.IdTipoRegimeTributarioNFC !=
                (int)TipoDeRegimeTributarioDeUmEstabelecimento.SimplesNacional)
            {
                return InformarCST(item);
            }

            var situacaoTributaria = Domain.NotaFiscalDoConsumidor.NfcSituacaoTributariaRepository.Load(umProdutoDaNota.IdSituacaoTributaria.Value);

            // Substituição Tributária
            if ("F".Equals(situacaoTributaria.Codigo))
            {
                var ICMSSN500 = new ICMSSN500
                {
                    orig = OrigemMercadoria.OmNacional,
                    CSOSN = Csosnicms.Csosn500
                };

                if (umProdutoDaNota.IcmsOrigem != null && umProdutoDaNota.IcmsOrigem > 0)
                {
                    ICMSSN500.vICMSEfet = umProdutoDaNota.IcmsOrigem;
                    ICMSSN500.pICMSEfet = umProdutoDaNota.ReducaoIcmsOrigem;
                    ICMSSN500.pRedBCEfet = Domain.NotaFiscalDoConsumidor.TabelaIBPTRepository.ObterAliquotasPorCodigoeUF(umProdutoDaNota.CodigoNCM, Estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF)?.AliquotaEstadual;
                    ICMSSN500.vBCEfet = umProdutoDaNota.ValorDeCompra;
                }

                return ICMSSN500;
            }

            // Tributado
            if ("T".Equals(situacaoTributaria.Codigo))
            {
                return new ICMSSN102
                {
                    CSOSN = Csosnicms.Csosn102,
                    orig = OrigemMercadoria.OmNacional
                };
            }

            // Isento
            if ("I".Equals(situacaoTributaria.Codigo))
                return new ICMSSN102
                {
                    CSOSN = Csosnicms.Csosn103,
                    orig = OrigemMercadoria.OmNacional
                };

            // Não tributado
            if ("N".Equals(situacaoTributaria.Codigo))
                return new ICMSSN102
                {
                    CSOSN = Csosnicms.Csosn400,
                    orig = OrigemMercadoria.OmNacional
                };

            throw new Exception("A situação tributária do produto não está prevista para a envio ao SEFAZ.");
        }

        protected virtual ICMSBasico InformarCST(NotaItensNFC item)
        {
            var umProdutoDaNota = item.EstabelecimentoProduto;
            var situacaoTributaria = Domain.NotaFiscalDoConsumidor.NfcSituacaoTributariaRepository.Load(umProdutoDaNota.IdSituacaoTributaria.Value);

            var vBC = item.ValorBrutoTotal == 0 ? 0 : item.ValorBrutoTotal;

            // Substituição Tributária
            if ("F".Equals(situacaoTributaria.Codigo))
            {
                var ICMS60 = new ICMS60
                {
                    orig = OrigemMercadoria.OmNacional,
                    CST = Csticms.Cst60
                };

                if (umProdutoDaNota.IcmsOrigem != null && umProdutoDaNota.IcmsOrigem > 0)
                {
                    ICMS60.vICMSEfet = umProdutoDaNota.IcmsOrigem;
                    ICMS60.pICMSEfet = umProdutoDaNota.ReducaoIcmsOrigem;
                    ICMS60.pRedBCEfet = Domain.NotaFiscalDoConsumidor.TabelaIBPTRepository.ObterAliquotasPorCodigoeUF(umProdutoDaNota.CodigoNCM, Estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF)?.AliquotaEstadual;
                    ICMS60.vBCEfet = umProdutoDaNota.ValorDeCompra;
                }

                return ICMS60;
            }



            // Tributado com ou sem alíquota
            if ("T".Equals(situacaoTributaria.Codigo) && umProdutoDaNota.AliquotaICMS != null && umProdutoDaNota.AliquotaICMS > 0)
            {
                return new ICMS00
                {
                    CST = Csticms.Cst00,
                    orig = OrigemMercadoria.OmNacional,
                    vBC = umProdutoDaNota.AliquotaICMS != null && umProdutoDaNota.AliquotaICMS > 0 ? vBC : 0,
                    pICMS = umProdutoDaNota.AliquotaICMS ?? 0,
                    vICMS = umProdutoDaNota.AliquotaICMS != null && umProdutoDaNota.AliquotaICMS > 0 ? (vBC * umProdutoDaNota.AliquotaICMS.Value / 100) : 0
                };
            }
            else if ("T".Equals(situacaoTributaria.Codigo))
            {
                return new ICMS00
                {
                    CST = Csticms.Cst00,
                    orig = OrigemMercadoria.OmNacional
                };
            }

            // Isento
            if ("I".Equals(situacaoTributaria.Codigo))
                return new ICMS40
                {
                    CST = Csticms.Cst40,
                    orig = OrigemMercadoria.OmNacional
                };

            // Não tributado
            if ("N".Equals(situacaoTributaria.Codigo))
                return new ICMS40
                {
                    CST = Csticms.Cst41,
                    orig = OrigemMercadoria.OmNacional
                };

            throw new Exception("A situação tributária do produto não está prevista para a envio ao SEFAZ.");
        }

        private dest GetDestinatario(VersaoServico versao, bool ambienteProducao)
        {
            var cliente = Transacao.ClienteEstabelecimento();
            var permiteCnpjCliente = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(Estabelecimento, Recurso.UsarCnpjClienteNfce).EstaDisponivel;

            if (cliente == null)
                return null;
            if (string.IsNullOrEmpty(cliente.Cliente.PessoaFisica.Cpf))
                if (permiteCnpjCliente)
                {
                    if (string.IsNullOrEmpty(cliente.CNPJ))
                        return null;
                }
                else
                    return null;

            var dest = new dest(versao);

            if (!string.IsNullOrEmpty(cliente.CNPJ) && permiteCnpjCliente)
                dest.CNPJ = cliente.CNPJ;
            else
                dest.CPF = cliente.Cliente.PessoaFisica.Cpf;


            dest.xNome = ambienteProducao ? cliente.Cliente.PessoaFisica.NomeCompleto.Trim() : _nomePessoaParaAmbienteHomologacao;

            if (cliente.Endereco != null)
            {
                var codigoIBGE = Domain.Pessoas.CidadeRepository.ObterCodigoIBGEDoClienteOuEstabelecimento(cliente.Endereco.UF, cliente.Endereco.Cidade,
                        Estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade);

                // Para enviar o endereço, precisa ter todas as informações do cliente.
                var temTodasInformacoesParaEndereco = TemTodasInformacoesParaEndereco(cliente, codigoIBGE);

                if (temTodasInformacoesParaEndereco)
                {
                    dest.enderDest = new enderDest
                    {
                        xLgr = !String.IsNullOrEmpty(cliente.Endereco.Logradouro) ? cliente.Endereco.Logradouro.RemoveSpecialCharacters() : "",
                        nro = cliente.Endereco.Numero,
                        xCpl = !String.IsNullOrEmpty(cliente.Endereco.Complemento) ? cliente.Endereco.Complemento.RemoveSpecialCharacters() : "",
                        xBairro = !String.IsNullOrEmpty(cliente.Endereco.Bairro) ? cliente.Endereco.Bairro.RemoveSpecialCharacters() : "",
                        cMun = codigoIBGE,
                        xMun = !String.IsNullOrEmpty(cliente.Endereco.Cidade) ? cliente.Endereco.Cidade.RemoveSpecialCharacters() : "",
                        UF = cliente.Endereco.UF.Sigla,
                        CEP = cliente.Endereco.Cep,
                        cPais = 1058,
                        xPais = "BRASIL"
                    };

                    if (dest.enderDest.xLgr.Length < 5)
                        dest.enderDest.xLgr = "Rua " + dest.enderDest.xLgr;
                }
                else
                {
                    dest.enderDest = null;
                }
            }

            //if (versao == VersaoServico.ve200)
            //    dest.IE = "ISENTO";
            if (versao != VersaoServico.Versao310 && versao != VersaoServico.Versao400)
                return dest;

            if (Estabelecimento.PessoaJuridica.EnderecoProprio.UF.Sigla == "MT" && !string.IsNullOrWhiteSpace(cliente.Cliente.PessoaFisica.Cpf))
            {
                dest.IE = null;
                dest.CNPJ = null;
            }

            dest.indIEDest = indIEDest.NaoContribuinte; //NFCe: Tem que ser não contribuinte V3.00 Somente
            dest.email = cliente.Cliente.PessoaFisica.Email; //V3.00 Somente
            return dest;
        }

        private static bool TemTodasInformacoesParaEndereco(ClienteEstabelecimento clienteEstabelecimento)
        {
            if (clienteEstabelecimento.Endereco != null)
            {
                var codigoIBGE = Domain.Pessoas.CidadeRepository.ObterCodigoIBGEDoClienteOuEstabelecimento(clienteEstabelecimento.Endereco.UF, clienteEstabelecimento.Endereco.Cidade,
                            clienteEstabelecimento.Estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade);

                return TemTodasInformacoesParaEndereco(clienteEstabelecimento, codigoIBGE);
            }
            else
            {
                return false;
            }
        }

        private static bool TemTodasInformacoesParaEndereco(ClienteEstabelecimento cliente, long codigoIBGE)
        {
            return !(string.IsNullOrEmpty(cliente.Endereco.Logradouro)
                                                                    || string.IsNullOrEmpty(cliente.Endereco.Numero)
                                                                    || string.IsNullOrEmpty(cliente.Endereco.Bairro)
                                                                    || string.IsNullOrEmpty(cliente.Endereco.Cidade)
                                                                    || string.IsNullOrEmpty(cliente.Endereco.Cep)
                                                                    || cliente.Endereco.UF == null
                                                                    || codigoIBGE <= 0);
        }

        private emit GetEmitente(bool ambienteProducao)
        {
            var emit = new emit();

            if (Estabelecimento.ConfiguracaoDeNFC == null)
            {
                throw new Exception("Sem configuração de NFC para o estabelecimento");
            }

            if (Estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.IdTipoRegimeTributarioNFC !=
                                                (int)TipoDeRegimeTributarioDeUmEstabelecimento.SimplesNacional)
            {
                // Ajuste realizado pois para outras situações tributárias, o ICMS deve ser calculado e são vários casos
                // que devem ser validados.
                //throw new Exception("A empresa emitente não é simples nacional. Atualmente a emissão de NFC-e só é permitida para estabelecimentos nessa situação tributária. " +
                //                    "Caso a sua empresa não seja simples nacional, nos envie uma mensagem através do Fale Conosco para ser notificado assim que esta opção estiver disponível.");

                emit.CRT = CRT.RegimeNormal;
            }
            else
            {
                emit.CRT = CRT.SimplesNacional;
            }

            emit.CNPJ = Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo ?? Estabelecimento.PessoaJuridica.CNPJ;
            emit.IE = !string.IsNullOrEmpty(Estabelecimento.PessoaJuridica.InscricaoEstadual) ? Estabelecimento.PessoaJuridica.InscricaoEstadual.ToUpper() : "ISENTO";

            if (!string.IsNullOrEmpty(Estabelecimento.PessoaJuridica.InscricaoMunicipal))
                emit.IM = Estabelecimento.PessoaJuridica.InscricaoMunicipal.ToUpper();

            var razaoSocial = Estabelecimento.PessoaJuridica.RazaoSocial.Trim().RemoveDiacritics();

            if (Estabelecimento.ConfiguracaoDeNFC != null && !string.IsNullOrWhiteSpace(Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo) && !string.IsNullOrWhiteSpace(Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo))
                razaoSocial = Estabelecimento.ConfiguracaoDeNFC.RazaoSocialAlternativa;

            emit.xNome = ambienteProducao ? razaoSocial : _nomePessoaParaAmbienteHomologacao;
            emit.xFant = !String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.NomeFantasia) ? Estabelecimento.PessoaJuridica.NomeFantasia.RemoveSpecialCharacters().RemoveDiacritics() : "";

            var endereco = new enderEmit();
            endereco.xLgr = !String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.EnderecoProprio.Logradouro) ? Estabelecimento.PessoaJuridica.EnderecoProprio.Logradouro.RemoveSpecialCharacters().RemoveDiacritics() : "";
            if (endereco.xLgr.Length < 5)
                endereco.xLgr = "Rua " + endereco.xLgr;
            endereco.nro = Estabelecimento.PessoaJuridica.EnderecoProprio.Numero;
            endereco.xCpl = !String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.EnderecoProprio.Complemento) ? Estabelecimento.PessoaJuridica.EnderecoProprio.Complemento.RemoveSpecialCharacters() : "";
            endereco.xBairro = !String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.EnderecoProprio.Bairro) ? Estabelecimento.PessoaJuridica.EnderecoProprio.Bairro.RemoveSpecialCharacters() : "";
            endereco.cMun = 0;
            if (!String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE))
                endereco.cMun = Convert.ToInt64(Estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);
            endereco.xMun = !String.IsNullOrEmpty(Estabelecimento.PessoaJuridica.EnderecoProprio.Cidade) ? Estabelecimento.PessoaJuridica.EnderecoProprio.Cidade.RemoveSpecialCharacters() : "";
            endereco.UF = (Estado)Enum.Parse(typeof(Estado), Estabelecimento.PessoaJuridica.EnderecoProprio.UF.Sigla);
            endereco.CEP = Estabelecimento.PessoaJuridica.EnderecoProprio.Cep;
            endereco.fone = null;

            if (Estabelecimento.PessoaJuridica.TelefonesProprios().Any())
            {
                var telefone = Estabelecimento.PessoaJuridica.TelefonesProprios().First();
                var numero = String.Format("{0}{1}", telefone.DDD, telefone.Numero);
                endereco.fone = Convert.ToInt64(numero);
            }
            endereco.cPais = 1058;
            endereco.xPais = "BRASIL";
            emit.enderEmit = endereco;
            return emit;
        }

        private ide GetIdentificacao(int numero, int numeroSerie, ModeloDocumento modelo, VersaoServico versao, DateTime dataAtual, string cnpjEmitente)
        {
            var enderecoDoEstabelecimento = Estabelecimento.PessoaJuridica.EnderecoProprio;
            if (enderecoDoEstabelecimento == null)
                throw new Exception("O estabelecimento não possui endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.UF == null)
                throw new Exception("O estabelecimento não possui UF em seu endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.BairroEntidade == null)
                throw new Exception("O estabelecimento não possui Bairro em seu endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.UF.CodigoIBGE == null)
                throw new Exception("O estabelecimento não possui UF com código IBGE em seu endereço para emissão de NFC-e");
            if (enderecoDoEstabelecimento.BairroEntidade.Cidade.CodigoIBGE == null)
                throw new Exception("O estabelecimento não possui CIDADE com código IBGE em seu endereço para emissão de NFC-e");

            var codigoIbgeDoEstadoDoEstabelecimento = Convert.ToInt64(enderecoDoEstabelecimento.UF.CodigoIBGE);
            var codigoIbgeDoMunicipioDoEstabelecimento = Convert.ToInt64(enderecoDoEstabelecimento.BairroEntidade.Cidade.CodigoIBGE);

            var ide = new ide
            {
                cUF = (Estado)codigoIbgeDoEstadoDoEstabelecimento,
                natOp = "VENDA",
                //indPag = NFEAmbientes.IndicadorPagamento.ipVista,
                mod = modelo,
                serie = numeroSerie,
                nNF = numero,
                tpNF = NFETipos.TipoNFe.tnSaida,
                cMunFG = codigoIbgeDoMunicipioDoEstabelecimento,
                tpEmis = ConfiguracaoServico.tpEmis,
                tpImp = NFETipos.TipoImpressao.tiMsgEletronica,
                cNF = "1234",
                tpAmb = ConfiguracaoServico.tpAmb,
                finNFe = NFETipos.FinalidadeNFe.fnNormal,
                verProc = "3.000"
            };

            #region V2.00

            if (versao == VersaoServico.Versao200)
            {
                ide.dEmi = dataAtual; //Mude aqui para enviar a nfe vinculada ao EPEC, V2.00
                ide.dSaiEnt = dataAtual;
            }

            #endregion V2.00

            #region V3.00

            if (versao != VersaoServico.Versao310 && versao != VersaoServico.Versao400)
                return ide;
            ide.idDest = NFETipos.DestinoOperacao.doInterna;
            ide.dhEmi = dataAtual; //Mude aqui para enviar a nfe vinculada ao EPEC, V3.10

            if (ide.mod == ModeloDocumento.NFe)
                ide.dhSaiEnt = dataAtual;
            //else
            //	ide.tpImp = NFEAmbientes.TipoImpressao.tiNFCe;
            ide.procEmi = NFETipos.ProcessoEmissao.peAplicativoContribuinte;
            ide.indFinal = NFETipos.ConsumidorFinal.cfConsumidorFinal; //NFCe: Tem que ser consumidor Final
            ide.indPres = NFETipos.PresencaComprador.pcPresencial; //NFCe: deve ser 1 ou 4

            #endregion V3.00

            #region V4.00

            if (versao == VersaoServico.Versao400)
            {
                ide.dhEmi = new DateTimeOffset(ide.dhEmi.Year, ide.dhEmi.Month, ide.dhEmi.Day, ide.dhEmi.Hour, ide.dhEmi.Minute, 0, ide.dhEmi.Offset);
                ide.idDest = NFETipos.DestinoOperacao.doInterna;
                ide.cDV = ChaveFiscal.ObterChave((Estado)codigoIbgeDoEstadoDoEstabelecimento, dataAtual, cnpjEmitente, modelo, numeroSerie, numero, (int)ConfiguracaoServico.tpEmis, 1234).DigitoVerificador;
                ide.indFinal = NFETipos.ConsumidorFinal.cfConsumidorFinal;
                ide.indPres = NFETipos.PresencaComprador.pcPresencial;
                ide.procEmi = NFETipos.ProcessoEmissao.peAplicativoContribuinte;
            }

            #endregion V4.00

            return ide;
        }

        public override void CancelarNF(string justificativa)
        {
            var utilizarZeus = ConfigurationManager.AppSettings["UtilizarZeus"];
            if (utilizarZeus != null && bool.Parse(utilizarZeus))
            {
                var notaNfc = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(Transacao);

                if (notaNfc != null && (notaNfc.StatusNota.IdStatusNotaNFC == 2 || (notaNfc.StatusNota.IdStatusNotaNFC == 3 && !notaNfc.CancelamentoFoiRealizado)))
                {
                    //ConfiguracaoServico = ObterConfiguracaoServico(Transacao.ClienteEstabelecimento().Estabelecimento);

                    byte[] certificadoDigitalDoEstabelecimento = ObterCertificadoDigitalDoEstabelecimento(Estabelecimento);

                    var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
                    var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == Estabelecimento.PessoaJuridica.IdPessoa);
                    var senhaCertificado = criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);

                    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.
                                                         ObterPorPessoaJuridica(Transacao.PessoaQueRecebeu.IdPessoa);
                    ConfiguracaoServico = ObterConfiguracaoServico(estabelecimento);

                    ConfiguracaoServico.Certificado.Senha = SenhaDoCertificado;
                    ConfiguracaoServico.Certificado.ArrayBytesArquivo = Certificado;
                    ConfiguracaoServico.ProtocoloDeSeguranca = SecurityProtocolType.Tls12;
                    //Supostamente funciona em servidores com framework abaixo da versão 4.5
                    //configuracaoServico.ProtocoloDeSeguranca = (SecurityProtocolType)3072;

                    var servicoNFe = new ServicosNFe(ConfiguracaoServico);

                    var cpfcnpj = Estabelecimento.ConfiguracaoDeNFC.CnpjAlternativo ?? Estabelecimento.PessoaJuridica.CNPJ;
                    var retornoCancelamento = servicoNFe.RecepcaoEventoCancelamento(notaNfc.NumeroNota,
                        1, notaNfc.ProtocoloSEFAZ, notaNfc.AutorizacaoSEFAZ.Replace("NFe", ""), string.Format("NOTA NUMERO {0} CANCELADA", notaNfc.NumeroNota), cpfcnpj);

                    var statusDesejavelprocessamento = new[] {
                        128, // Lote Processado
                        135  // Recebido pelo Sistema de Registro de Eventos, com vinculação do evento na NF-e
                    };

                    var statusRejeicaoEvento = new[] {
                        501, // Rejeicao: Prazo de Cancelamento Superior ao Previsto na Legislacao
                        526, // Rejeição: Consulta a uma Chave de Acesso muito antiga
                        613  // Rejeição: Chave de Acesso difere da existente em BD
                    };

                    var eventoProcessado = statusDesejavelprocessamento.Contains(retornoCancelamento.Retorno.cStat);
                    var eventoComFalha = retornoCancelamento.Retorno.retEvento != null && retornoCancelamento.Retorno.retEvento.Any(f => f.infEvento != null && statusRejeicaoEvento.Contains(f.infEvento.cStat));
                    if (eventoProcessado && !eventoComFalha)
                    {
                        notaNfc.CancelamentoFoiRealizado = true;

                        var xml = retornoCancelamento.RetornoCompletoStr;
                        string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();

                        var procEventoNFe = retornoCancelamento.ProcEventosNFe.FirstOrDefault();

                        if (procEventoNFe != null)
                        {
                            string autorizacaoSEFAZ = procEventoNFe.evento.infEvento.chNFe;
                            ControleDeArquivos.GravarArquivo(retornoCancelamento.EnvioStr, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + "/Nota", "CAN_" + autorizacaoSEFAZ + ".xml");
                            ControleDeArquivos.GravarArquivo(retornoCancelamento.RetornoCompletoStr, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento.ToString() + "/Nota", "RET_CAN_" + autorizacaoSEFAZ + ".xml");
                        }

                        Domain.NotaFiscalDoConsumidor.NotaNFCService.CancelarNotaNFC(Transacao);
                    }
                }
            }
            else
            {
                Domain.NotaFiscalDoConsumidor.NotaNFCService.CancelarNotaNFC(Transacao);
            }
        }

        /*
         * O Sefaz de GOIÁS usa certificado para receber SSL em seu servidor com um
         * emitente inválido.
         *
         * Quando isso ocorre, o próprio framework .NET levanta exceção para evitar comunicação SSL
         * com destino não confiável.
         *
         * O código abaixo faz com que o framework .NET ignore os erros.
         * Não é uma prática que deve ser mantida, mas será usada para contingenciar.
         *
         * Quando o certificado de goiás for regularizado, poderemos remover. Caso não o faça, podemos
         * especializar para ser ignorado apenas quando a emissão for Goiás.
         *
         */

        public void IgnorarErrosDeCertificadoDoSefaz()
        {
            //https://stackoverflow.com/questions/2675133/c-sharp-ignore-certificate-errors
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
        }

        public string TestarComunicacaoComSEFAZ()
        {
            var configuracaoServico = CarregarConfiguracaoServico(Estabelecimento);
            var servicoNFe = new ServicosNFe(configuracaoServico);

            var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == Estabelecimento.PessoaJuridica.IdPessoa);
            var serialCertificado = pessoaJuridicaCertificadoDigital.SerialCertificado;

            configuracaoServico.Certificado.Serial = serialCertificado;

            StringBuilder sbDadosERetorno = new StringBuilder();
            sbDadosERetorno.AppendFormat("Estabelecimento: {0} <br/>", Estabelecimento.NomeDeExibicaoNoPortal);
            sbDadosERetorno.AppendFormat("Certificado (serial): {0} <br/>", configuracaoServico.Certificado.Serial);
            sbDadosERetorno.AppendFormat("Ambiente: {0} <br/>", configuracaoServico.tpAmb);

            sbDadosERetorno.AppendFormat("Protocolo: <br/>", configuracaoServico.ProtocoloDeSeguranca.ToString());

            if (ServicePointManager.SecurityProtocol == SecurityProtocolType.Tls12)
                sbDadosERetorno.Append("Protocolo TLS 1.2 <br/> ");
            else
                sbDadosERetorno.Append("Protocolo incorreto <br/> ");

            try
            {
                X509Certificate2Collection certificados = new X509Certificate2Collection();
                certificados.Import(Certificado, SenhaDoCertificado, X509KeyStorageFlags.MachineKeySet);

                foreach (var c in certificados)
                {
                    sbDadosERetorno.AppendFormat("Certificado no .pk {0} <br/>", c.Subject);
                }

                var certificadoDoEstabelecimento =
                    certificados.Cast<X509Certificate2>()
                        .AsQueryable()
                        .Where(a => a.SerialNumber == configuracaoServico.Certificado.Serial)
                        .FirstOrDefault();

                sbDadosERetorno.AppendFormat("Certificado {0} <br/>", certificadoDoEstabelecimento.Subject);

                sbDadosERetorno.AppendFormat("Retorno Status {0} <br/>", servicoNFe.NfeStatusServico().RetornoCompletoStr);
            }
            catch (WebException wex)
            {
                sbDadosERetorno.AppendFormat("Erro HTTP {0} <br/>", wex.Message);
                if (wex.Response != null)
                    sbDadosERetorno.AppendFormat("URL {0} <br/>", wex.Response.ResponseUri.ToString());
                if (wex.InnerException != null)
                    sbDadosERetorno.AppendFormat("Erro inner {0} <br/>", wex.InnerException.Message);
            }
            catch (Exception ex)
            {
                sbDadosERetorno.AppendFormat("Erro {0} <br/>", ex.Message);
                if (ex.InnerException != null)
                    sbDadosERetorno.AppendFormat("Erro inner {0} <br/>", ex.InnerException.Message);
            }

            return sbDadosERetorno.ToString();
        }

        public static byte[] ObterCertificadoDigitalDoEstabelecimento(Estabelecimento estabelecimento)
        {
            ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();

            ArquivoAmazonS3 arquivoCertificado = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterCertificadoEhMigrarArquivosSeNecessario(estabelecimento.PessoaJuridica);
            if (arquivoCertificado == null)
                throw new Exception("Não foi encontrado o certificado digital ao gerar nota fiscal.");

            var stream = arquivoCertificado.S3FileInfo.OpenRead();

            byte[] result;

            using (MemoryStream streamReader = new MemoryStream())
            {
                stream.CopyTo(streamReader);
                result = streamReader.ToArray();
            }

            return result;
        }

        public override string GerarConteudoDeDadosParaNF()
        {
            throw new NotImplementedException();
        }

        //public override int GerarEGravarNumeroDoFechamento(Estabelecimento estabelecimento) {
        //    throw new NotImplementedException();
        //}

        #region NotImplemented

        public override object GerarDadosDeAtualizacaoDeIntegracao()
        {
            throw new NotImplementedException();
        }

        public override void GravarGeracaoDeNFC(ImpressaoDeNFC impressaoDeNFC)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(EstabelecimentoProduto estabelecimentoProduto)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(ServicoEstabelecimento servicoEstabelecimento)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(Pacotes.Pacote Pacote)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(Horario horario)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override long ObterCodigoDaMercadoriaOuServico(TransacaoFormaPagamento credito)
        {
            throw new NotImplementedException();
        }

        protected override string ObterDescricaoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        public override string GerarIdentificacaoDaGeracao(int numeroDoFechamentoGerado)
        {
            throw new NotImplementedException();
        }

        protected override string ObterCodigoDeBarrasDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override string ObterCodigoFiscalDeOperacaoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override string ObterTipoUnidadeDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override int ObterOrigemProdutoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override bool ObterFabricacaoPropriaDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override bool ObterProdutoImportadoDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override decimal ObterAliquotaICMSDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override decimal ObterAliquotaPISDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override decimal ObterAliquotaCOFINSDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        protected override int ObterSituacaoTributariaDoItem(ItemVenda itemVenda)
        {
            throw new NotImplementedException();
        }

        #endregion NotImplemented

        public ConfiguracaoServico ConfiguracaoServico { get; set; }

        public override ImpressaoDeNotaDTO GerarDTODeImpressaoDeNotaDanfe(NotaNFC nota, string enderecoDoEmitente)
        {
            var impressaoDTO = base.GerarDTODeImpressaoDeNotaDanfe(nota, enderecoDoEmitente);

            impressaoDTO.TotalPago = ObterTotalPagoNaNota(nota);
            impressaoDTO.ValorDoTroco = 0;//TODO:hoje não é enviado troco

            List<NotaFormaPagamentoNFC> formasDePagamentoDaNota = Domain.NotaFiscalDoConsumidor.NotaFormaPagamentoNFCRepository.ObterFormasDePagamentoDaNota(nota);
            impressaoDTO.FormasDePagamento = ObterFormasDePagamentoQueVaoEntrarNaNota(formasDePagamentoDaNota).Select(f => new FormaDePagamentoDanfDTO(f, ObterValorPagamentoLiquidoProporcional(f, nota, formasDePagamentoDaNota))).ToList();
            foreach (var forma in impressaoDTO.FormasDePagamento)
            {
                var formaTipoEnum = XmlEnumExtensions.FromReflectedXmlValue<enumFormaPagamentoNFe>(forma.Id.ToString("00"));
                forma.Nome = EnumActions.GetEnumText((enumFormaPagamentoNFe)formaTipoEnum);
            }

            if (nota.ClienteEstabelecimento != null && TemTodasInformacoesParaEndereco(nota.ClienteEstabelecimento))
            {
                var logradouro = !String.IsNullOrEmpty(nota.ClienteEstabelecimento.Endereco.Logradouro) ? nota.ClienteEstabelecimento.Endereco.Logradouro.RemoveSpecialCharacters() + ", " : "";
                var numero = nota.ClienteEstabelecimento.Endereco.Numero + ", ";
                var complemento = !String.IsNullOrEmpty(nota.ClienteEstabelecimento.Endereco.Complemento) ? nota.ClienteEstabelecimento.Endereco.Complemento.RemoveSpecialCharacters() + ", " : "";
                var bairro = !String.IsNullOrEmpty(nota.ClienteEstabelecimento.Endereco.Bairro) ? nota.ClienteEstabelecimento.Endereco.Bairro.RemoveSpecialCharacters() + " - " : "";
                var cidade = !String.IsNullOrEmpty(nota.ClienteEstabelecimento.Endereco.Cidade) ? nota.ClienteEstabelecimento.Endereco.Cidade.RemoveSpecialCharacters() + " - " : "";
                var sigla = nota.ClienteEstabelecimento.Endereco.UF.Sigla + " - ";
                var cep = nota.ClienteEstabelecimento.Endereco.Cep.Formatar("99999-999");

                impressaoDTO.EnderecoDoCliente = logradouro + numero + complemento + bairro + cidade + sigla + cep;
            }
            else
            {
                impressaoDTO.EnderecoDoCliente = "";
            }

            return impressaoDTO;
        }

        public void ProcessarRetornoSEFAZ(protNFe protNFe, NotaNFC nota, ServicosNFe servicoNFe, string envioStr)
        {
            if (protNFe.infProt.cStat == 100)
            {
                try
                {
                    nota.StatusNota = StatusNotaNFCEnum.Emitida;
                    nota.ProtocoloSEFAZ = protNFe.infProt.nProt;
                    nota.DataRecebimentoSEFAZ = protNFe.infProt.dhRecbto;
                    var xml = GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(envioStr, servicoNFe);
                    var bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
                    ControleDeArquivos.GravarArquivo(xml, bucket + "/NFCe/" + Estabelecimento.IdEstabelecimento + "/Nota", nota.AutorizacaoSEFAZ + ".xml");
                    Domain.Pessoas.EnvioEmailService.EnviarEmailEmissaoNFCe(Transacao, xml);
                }
                catch (Exception e)
                {
                    // Qualquer erro neste trecho, não pode mais realizar rollback
                    // pois há emissão junto ao SEFAZ. A exceção é gravada mas não é lançada.
                    GravarErroElmah(new Exception("Exceção gerada na obtenção do XML ou no envio de email. Mesmo assim, a emissão foi confirmada no sistema pois foi realizada no SEFAZ.", e));
                }
            }
            else
            {
                throw new Exception(protNFe.infProt.xMotivo);
            }
        }

        public override void EnviarNFCe(NotaNFC nota)
        {
            var dataAtual = Calendario.Agora();

            Transacao = nota.Transacao;
            NotaNFC = nota;
            Registros = Domain.RPS.DadosRPSService.ListarDadosParaGeracaoDeRPS(new ParametrosFiltrosLotesRPS
            {
                IdsTransacao = new List<int> { nota.Transacao.Id },
                IdPessoaDaPessoaJuridica = nota.Transacao.PessoaQueRecebeu.IdPessoa,
                Estabelecimento = nota.Estabelecimento
            });

            ConfiguracaoServico = CarregarConfiguracaoServico(nota.Estabelecimento);
            var servicoNFe = new ServicosNFe(ConfiguracaoServico);

            var nfe = ConstruirNFe(nota);

            try
            {
                if (nota.IdNotaNFC > 0 && nota.AutorizacaoSEFAZ != null)
                {
                    var retConsulta = servicoNFe.NfeConsultaProtocolo(nota.AutorizacaoSEFAZ.SomenteNumeros());
                    if (retConsulta.Retorno.cStat == 100)
                    {
                        var xml = Domain.Financeiro.TransacaoService.GerarNFCeParaDownload(Transacao).Result;
                        ProcessarRetornoSEFAZ(retConsulta.Retorno.protNFe, nota, servicoNFe, xml);
                        return;
                    }
                }

                var retornoEnvio = servicoNFe.NFeAutorizacao(nota.NumeroNota, IndicadorSincronizacao.Sincrono, new List<NFe.Classes.NFe> { nfe });
                nota.LoteSEFAZ = nota.NumeroNota.ToString();
                nota.AutorizacaoSEFAZ = nfe.infNFe.Id;
                if (nfe.Signature != null && nfe.Signature.SignedInfo != null && nfe.Signature.SignedInfo.Reference != null)
                    nota.DigestValue = nfe.Signature.SignedInfo.Reference.DigestValue;
                nota.DataEmissao = dataAtual;

                if (retornoEnvio.Retorno.cStat == 104)
                {
                    var protNFe = retornoEnvio.Retorno.protNFe;
                    ProcessarRetornoSEFAZ(protNFe, nota, servicoNFe, retornoEnvio.EnvioStr);
                }
                else
                {
                    throw new Exception(retornoEnvio.Retorno.xMotivo);
                }
            }
            catch (Exception ex)
            {
                var erro = ex;
                if (ex is AggregateException)
                    erro = ((AggregateException)ex).InnerException;

                GravarErroElmah(erro);
                var message = erro.Message;

                bool foiEnviado;
                message = TratarMensagemEStatusDeErro(servicoNFe, ex, message, out foiEnviado);

                throw new Exception(message);
            }
        }

        //private void ConsiderarNotaComoEmitida(NotaNFC nota) {
        //    nota.StatusNota = StatusNotaNFCEnum.Emitida;
        //}

        public override void Inutilizar(NotaInutilizadaNFC inutilizacao)
        {
            ConfiguracaoServico = CarregarConfiguracaoServico(inutilizacao.Estabelecimento);

            var servicoNFe = new ServicosNFe(ConfiguracaoServico);
            Inutilizar(servicoNFe, inutilizacao);
        }

        public string ObterXML(int idEstabelecimento, string chave)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            ConfiguracaoServico = CarregarConfiguracaoServico(estabelecimento);

            var servicoNFe = new ServicosNFe(ConfiguracaoServico);
            var consulta = servicoNFe.NfeConsultaProtocolo(chave);

            return consulta.RetornoStr;
        }
    }

    public class EnderecoConsultaPublicaNfce
    {

        public EnderecoConsultaPublicaNfce(Estado estado, TipoAmbiente tipoAmbiente, ExtinfNFeSupl.TipoUrlConsultaPublica tipoUrlConsultaPublica, string url)
        {
            TipoAmbiente = tipoAmbiente;
            Estado = estado;
            TipoUrlConsultaPublica = tipoUrlConsultaPublica;
            Url = url;
        }

        public NFEAmbientes.TipoAmbiente TipoAmbiente { get; protected set; }
        public Estado Estado { get; protected set; }
        public ExtinfNFeSupl.TipoUrlConsultaPublica TipoUrlConsultaPublica { get; protected set; }
        public string Url { get; protected set; }
    }

    /// <summary>
    ///     Classe reponsável pelas regras de obtenção do endereço de consulta da NFCe pelo site e via QR-Code
    /// </summary>
    public static class ExtinfNFeSupl
    {
        private static readonly List<EnderecoConsultaPublicaNfce> EndQrCodeNfce;

        static ExtinfNFeSupl()
        {
            EndQrCodeNfce = CarregarUrls();
        }

        private static List<EnderecoConsultaPublicaNfce> CarregarUrls()
        {
            var endQrCodeNfce = new List<EnderecoConsultaPublicaNfce>
            {
                new EnderecoConsultaPublicaNfce(Estado.AC, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://hml.sefaznet.ac.gov.br/nfce/qrcode"),
                new EnderecoConsultaPublicaNfce(Estado.AC, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.sefaznet.ac.gov.br/nfce/"),
                new EnderecoConsultaPublicaNfce(Estado.AC, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://hml.sefaznet.ac.gov.br/nfce/qrcode"),
                new EnderecoConsultaPublicaNfce(Estado.AC, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://hml.sefaznet.ac.gov.br/nfce/"),

                new EnderecoConsultaPublicaNfce(Estado.AL, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://nfce.sefaz.al.gov.br/QRCode/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.AL, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.sefaz.al.gov.br/consultaNFCe.htm"),
                new EnderecoConsultaPublicaNfce(Estado.AL, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://nfce.sefaz.al.gov.br/QRCode/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.AL, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.sefaz.al.gov.br/consultaNFCe.htm"),

                new EnderecoConsultaPublicaNfce(Estado.AM, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://sistemas.sefaz.am.gov.br/nfceweb/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.AM, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://sistemas.sefaz.am.gov.br/nfceweb/formConsulta.do"),
                new EnderecoConsultaPublicaNfce(Estado.AM, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://homnfce.sefaz.am.gov.br/nfceweb/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.AM, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://homnfce.sefaz.am.gov.br/nfceweb/formConsulta.do"),

                new EnderecoConsultaPublicaNfce(Estado.AP, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "https://www.sefaz.ap.gov.br/nfce/nfcep.php"),
                new EnderecoConsultaPublicaNfce(Estado.AP, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "https://www.sefaz.ap.gov.br/sate/seg/SEGf_AcessarFuncao.jsp?cdFuncao=FIS_1261"),
                new EnderecoConsultaPublicaNfce(Estado.AP, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "https://www.sefaz.ap.gov.br/nfcehml/nfce.php"),
                new EnderecoConsultaPublicaNfce(Estado.AP, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "https://www.sefaz.ap.gov.br/sate1/seg/SEGf_AcessarFuncao.jsp?cdFuncao=FIS_1261"),

                new EnderecoConsultaPublicaNfce(Estado.BA, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://nfe.sefaz.ba.gov.br/servicos/nfce/modulos/geral/NFCEC_consulta_chave_acesso.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.BA, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://nfe.sefaz.ba.gov.br/servicos/nfce/default.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.BA, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://hnfe.sefaz.ba.gov.br/servicos/nfce/modulos/geral/NFCEC_consulta_chave_acesso.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.BA, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://nfe.sefaz.ba.gov.br/servicos/nfce/default.aspx"),

                new EnderecoConsultaPublicaNfce(Estado.DF, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://dec.fazenda.df.gov.br/ConsultarNFCe.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.DF, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://dec.fazenda.df.gov.br/nfce"),
                new EnderecoConsultaPublicaNfce(Estado.DF, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://dec.fazenda.df.gov.br/ConsultarNFCe.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.DF, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://dec.fazenda.df.gov.br/nfce"),

                new EnderecoConsultaPublicaNfce(Estado.GO, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://nfe.sefaz.go.gov.br/nfeweb/sites/nfce/danfeNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.GO, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.nfce.go.gov.br/post/ver/214278/consumid"),
                new EnderecoConsultaPublicaNfce(Estado.GO, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://homolog.sefaz.go.gov.br/nfeweb/sites/nfce/danfeNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.GO, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.nfce.go.gov.br/post/ver/214413/consulta-nfc-e-homologacao"),

                new EnderecoConsultaPublicaNfce(Estado.ES, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://app.sefaz.es.gov.br/ConsultaNFCe/qrcode.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.ES, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://app.sefaz.es.gov.br/ConsultaNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.ES, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://homologacao.sefaz.es.gov.br/ConsultaNFCe/qrcode.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.ES, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://homologacao.sefaz.es.gov.br/ConsultaNFCe"),

                new EnderecoConsultaPublicaNfce(Estado.MA, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.nfce.sefaz.ma.gov.br/portal/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.MA, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.nfce.sefaz.ma.gov.br/portal/consultaNFe.do"),
                new EnderecoConsultaPublicaNfce(Estado.MA, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www.hom.nfce.sefaz.ma.gov.br/portal/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.MA, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.hom.nfce.sefaz.ma.gov.br/portal/consultaNFe.do"),

                new EnderecoConsultaPublicaNfce(Estado.MS, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.dfe.ms.gov.br/nfce/qrcode"),
                new EnderecoConsultaPublicaNfce(Estado.MS, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.dfe.ms.gov.br/nfce/chavedeacesso"),
                new EnderecoConsultaPublicaNfce(Estado.MS, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www.dfe.ms.gov.br/nfce/qrcode"),
                new EnderecoConsultaPublicaNfce(Estado.MS, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.dfe.ms.gov.br/nfce/chavedeacesso"),

                new EnderecoConsultaPublicaNfce(Estado.MT, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.sefaz.mt.gov.br/nfce/consultanfce"),
                new EnderecoConsultaPublicaNfce(Estado.MT, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.sefaz.mt.gov.br/nfce/consultanfce"),
                new EnderecoConsultaPublicaNfce(Estado.MT, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://homologacao.sefaz.mt.gov.br/nfce/consultanfce"),
                new EnderecoConsultaPublicaNfce(Estado.MT, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://homologacao.sefaz.mt.gov.br/nfce/consultanfce"),

                new EnderecoConsultaPublicaNfce(Estado.PA, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "https://appnfc.sefa.pa.gov.br/portal/view/consultas/nfce/nfceForm.seam"),
                new EnderecoConsultaPublicaNfce(Estado.PA, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "https://appnfc.sefa.pa.gov.br/portal/view/consultas/nfce/consultanfce.seam"),
                new EnderecoConsultaPublicaNfce(Estado.PA, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "https://appnfc.sefa.pa.gov.br/portal-homologacao/view/consultas/nfce/nfceForm.seam"),
                new EnderecoConsultaPublicaNfce(Estado.PA, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "https://appnfc.sefa.pa.gov.br/portal-homologacao/view/consultas/nfce/consultanfce.seam"),

                new EnderecoConsultaPublicaNfce(Estado.PB, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.sefaz.pb.gov.br/nfce"),
                new EnderecoConsultaPublicaNfce(Estado.PB, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.sefaz.pb.gov.br/nfce/consulta"),
                new EnderecoConsultaPublicaNfce(Estado.PB, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www.sefaz.pb.gov.br/nfcehom"),
                new EnderecoConsultaPublicaNfce(Estado.PB, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.sefaz.pb.gov.br/nfcehom"),

                new EnderecoConsultaPublicaNfce(Estado.PE, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://nfce.sefaz.pe.gov.br/nfce-web/consultarNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.PE, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.sefaz.pe.gov.br/nfce-web/entradaConsNfce"),
                new EnderecoConsultaPublicaNfce(Estado.PE, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://nfcehomolog.sefaz.pe.gov.br/nfce-web/consultarNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.PE, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://nfcehomolog.sefaz.pe.gov.br/nfce-web/entradaConsNfce"),

                new EnderecoConsultaPublicaNfce(Estado.PI, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://webas.sefaz.pi.gov.br/nfceweb/consultarNFCe.jsf"),
                new EnderecoConsultaPublicaNfce(Estado.PI, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://webas.sefaz.pi.gov.br/nfceweb/consultarNFCe.jsf"),
                new EnderecoConsultaPublicaNfce(Estado.PI, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://webas.sefaz.pi.gov.br/nfceweb-homologacao/consultarNFCe.jsf"),
                new EnderecoConsultaPublicaNfce(Estado.PI, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://webas.sefaz.pi.gov.br/nfceweb-homologacao/consultarNFCe.jsf"),

                new EnderecoConsultaPublicaNfce(Estado.PR, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.dfeportal.fazenda.pr.gov.br/dfe-portal/rest/servico/consultaNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.PR, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.fazenda.pr.gov.br/"),
                new EnderecoConsultaPublicaNfce(Estado.PR, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www.dfeportal.fazenda.pr.gov.br/dfe-portal/rest/servico/consultaNFCe"),
                new EnderecoConsultaPublicaNfce(Estado.PR, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.fazenda.pr.gov.br/"),

                new EnderecoConsultaPublicaNfce(Estado.RJ, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www4.fazenda.rj.gov.br/consultaNFCe/QRCode"),
                new EnderecoConsultaPublicaNfce(Estado.RJ, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.fazenda.rj.gov.br/consulta"),
                new EnderecoConsultaPublicaNfce(Estado.RJ, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www4.fazenda.rj.gov.br/consultaNFCe/QRCode"),
                new EnderecoConsultaPublicaNfce(Estado.RJ, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.fazenda.rj.gov.br/consulta"),

                new EnderecoConsultaPublicaNfce(Estado.RN, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://nfce.set.rn.gov.br/consultarNFCe.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.RN, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.set.rn.gov.br/portalDFE/NFCe/ConsultaNFCe.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.RN, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://hom.nfce.set.rn.gov.br/consultarNFCe.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.RN, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://nfce.set.rn.gov.br/portalDFE/NFCe/ConsultaNFCe.aspx"),

                new EnderecoConsultaPublicaNfce(Estado.RO, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.nfce.sefin.ro.gov.br/consultanfce/consulta.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.RO, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.nfce.sefin.ro.gov.br"),
                new EnderecoConsultaPublicaNfce(Estado.RO, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www.nfce.sefin.ro.gov.br/consultanfce/consulta.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.RO, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.nfce.sefin.ro.gov.br/consultaAmbHomologacao.jsp"),

                new EnderecoConsultaPublicaNfce(Estado.RR, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "https://www.sefaz.rr.gov.br/nfce/servlet/qrcode"),
                new EnderecoConsultaPublicaNfce(Estado.RR, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "https://www.sefaz.rr.gov.br/nfce/servlet/wp_consulta_nfce"),
                new EnderecoConsultaPublicaNfce(Estado.RR, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://**************:8080/nfce/servlet/qrcode"),
                new EnderecoConsultaPublicaNfce(Estado.RR, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://**************:8080/nfce/servlet/wp_consulta_nfce"),

                new EnderecoConsultaPublicaNfce(Estado.RS, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "https://www.sefaz.rs.gov.br/NFCE/NFCE-COM.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.RS, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "https://www.sefaz.rs.gov.br/NFE/NFE-NFC.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.RS, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "https://www.sefaz.rs.gov.br/NFCE/NFCE-COM.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.RS, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "https://www.sefaz.rs.gov.br/NFE/NFE-NFC.aspx"),

                new EnderecoConsultaPublicaNfce(Estado.SE, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "http://www.nfce.se.gov.br/portal/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.SE, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "http://www.nfce.se.gov.br/portal"),
                new EnderecoConsultaPublicaNfce(Estado.SE, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "http://www.hom.nfe.se.gov.br/portal/consultarNFCe.jsp"),
                new EnderecoConsultaPublicaNfce(Estado.SE, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "http://www.hom.nfe.se.gov.br/portal"),

                new EnderecoConsultaPublicaNfce(Estado.SP, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "https://www.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaQRCode.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.SP, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "https://www.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaPublica.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.SP, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "https://www.homologacao.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaQRCode.aspx"),
                new EnderecoConsultaPublicaNfce(Estado.SP, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "https://www.homologacao.nfce.fazenda.sp.gov.br/NFCeConsultaPublica/Paginas/ConsultaPublica.aspx"),

                new EnderecoConsultaPublicaNfce(Estado.MG, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlQrCode, "https://portalsped.fazenda.mg.gov.br/portalnfce/sistema/qrcode.xhtml"),
                new EnderecoConsultaPublicaNfce(Estado.MG, NFEAmbientes.TipoAmbiente.Producao, TipoUrlConsultaPublica.UrlConsulta, "https://portalsped.fazenda.mg.gov.br/portalnfce"),
                new EnderecoConsultaPublicaNfce(Estado.MG, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlQrCode, "https://portalsped.fazenda.mg.gov.br/portalnfce/sistema/qrcode.xhtml"),
                new EnderecoConsultaPublicaNfce(Estado.MG, NFEAmbientes.TipoAmbiente.Homologacao, TipoUrlConsultaPublica.UrlConsulta, "https://hportalsped.fazenda.mg.gov.br/portalnfce"),
            };

            return endQrCodeNfce;
        }

        /// <summary>
        ///     Obtém a URL para uso no DANFE da NFCe
        /// </summary>
        /// <param name="infNFeSupl"></param>
        /// <param name="tipoAmbiente"></param>
        /// <param name="estado"></param>
        /// <param name="tipoUrlConsultaPublica"></param>
        /// <returns></returns>
        public static string ObterUrl(this infNFeSupl infNFeSupl, NFEAmbientes.TipoAmbiente tipoAmbiente, Estado estado, TipoUrlConsultaPublica tipoUrlConsultaPublica)
        {
            var query = from qr in EndQrCodeNfce where qr.TipoAmbiente == tipoAmbiente && qr.Estado == estado && qr.TipoUrlConsultaPublica == tipoUrlConsultaPublica select qr.Url;
            var listaRetorno = query as IList<string> ?? query.ToList();
            var qtdeRetorno = listaRetorno.Count();

            if (qtdeRetorno == 0)
                throw new Exception(string.Format("Não foi possível obter o {0}, para o Estado {1}, ambiente: {2}", tipoUrlConsultaPublica.Descricao(), estado, tipoAmbiente.Descricao()));
            if (qtdeRetorno > 1)
                throw new Exception("A função ObterUrl obteve mais de um resultado!");
            return listaRetorno.FirstOrDefault();
        }

        /// <summary>
        ///     Obtém a URL para uso no QR-Code
        /// </summary>
        /// <param name="infNFeSupl"></param>
        /// <param name="nfe"></param>
        /// <param name="cIdToken"></param>
        /// <param name="csc"></param>
        /// <returns></returns>
        public static string ObterUrlQrCode(this infNFeSupl infNFeSupl, NFe.Classes.NFe nfe, string cIdToken, string csc)
        {
            //Passo 1: Converter o valor da Data e Hora de Emissão da NFC-e (dhEmi) para HEXA;
            var dhEmi = ObterHexDeString(nfe.infNFe.ide.dhEmi.ToString());

            //Passo 2: Converter o valor do Digest Value da NFC-e (digVal) para HEXA;
            //Ao se efetuar a assinatura digital da NFCe emitida em contingência off-line, o campo digest value constante da XMl Signature deve obrigatoriamente ser idêntico ao encontrado quando da geração do digest value para a montagem QR Code.
            //Ver página 18 do Manual de Padrões Padrões Técnicos do DANFE - NFC - e e QR Code, versão 3.2
            if (nfe.Signature == null)
                throw new Exception("Não é possível obter a URL do QR-Code de uma NFCe não assinada!");
            var digVal = ObterHexDeString(nfe.Signature.SignedInfo.Reference.DigestValue);

            //Na hipótese do consumidor não se identificar, não existirá o parâmetro cDest no QR Code;
            var cDest = "";
            if (nfe.infNFe.dest != null)
                cDest = "&cDest=" + nfe.infNFe.dest.CPF + nfe.infNFe.dest.CNPJ + nfe.infNFe.dest.idEstrangeiro;

            //Passo 3: Substituir os valores (“dhEmi” e “digVal”) nos parâmetros;
            string vICMS = nfe.infNFe.total.ICMSTot.vICMS == 0 ? "0" : nfe.infNFe.total.ICMSTot.vICMS.ToString("0.00").Replace(',', '.');
            var dadosBase = "chNFe=" + nfe.infNFe.Id.Substring(3) + "&nVersao=100&tpAmb=" + ((int)nfe.infNFe.ide.tpAmb) + cDest + "&dhEmi=" + dhEmi + "&vNF=" +
                            nfe.infNFe.total.ICMSTot.vNF.ToString("0.00").Replace(',', '.') + "&vICMS=" + vICMS + "&digVal=" + digVal + "&cIdToken=" + cIdToken;

            //Passo 4: Adicionar, ao final dos parâmetros, o CSC (CSC do contribuinte disponibilizado pela SEFAZ do Estado onde a empresa esta localizada):
            var dadosParaSh1 = dadosBase + csc;

            //Passo 5: Aplicar o algoritmo SHA-1 sobre todos os parâmetros concatenados. Asaída do algoritmo SHA-1 deve ser em HEXADECIMAL.
            var sha1ComCsc = ObterHexSha1DeString(dadosParaSh1);

            //Passo 6: Adicione o resultado sem o CSC e gere a imagem do QR Code: 1º parte (endereço da consulta) +2º parte (tabela 3 com indicação SIM na última coluna).
            return ObterUrl(infNFeSupl, nfe.infNFe.ide.tpAmb, nfe.infNFe.ide.cUF, TipoUrlConsultaPublica.UrlQrCode) + "?" + dadosBase + "&cHashQRCode=" + sha1ComCsc;
        }

        /// <summary>
        /// Obtém uma string SHA1, no formato hexadecimal da string passada no parâmeto
        /// </summary>
        /// <param name="s"></param>
        /// <returns></returns>
        private static string ObterHexSha1DeString(string s)
        {
            var bytes = Encoding.UTF8.GetBytes(s);

            var sha1 = SHA1.Create();
            var hashBytes = sha1.ComputeHash(bytes);

            return ObterHexDeByteArray(hashBytes);
        }

        /// <summary>
        /// Obtém uma string Hexadecimal do array de bytes passado no parâmetro
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        private static string ObterHexDeByteArray(byte[] bytes)
        {
            var sb = new StringBuilder();
            foreach (var b in bytes)
            {
                var hex = b.ToString("x2");
                sb.Append(hex);
            }
            return sb.ToString();
        }

        /// <summary>
        /// Obtém uma string Hexadecimal de uma string passada no parâmetro
        /// </summary>
        /// <param name="s"></param>
        /// <returns></returns>
        private static string ObterHexDeString(string s)
        {
            var hex = "";
            foreach (var c in s)
            {
                int tmp = c;
                hex += string.Format("{0:x2}", Convert.ToUInt32(tmp.ToString()));
            }
            return hex;
        }

        public enum TipoUrlConsultaPublica
        {

            [Description("Endereço para consulta da NFCe através do site")]
            UrlConsulta,

            [Description("Endereço para consulta da NFCe através do QR-Code")]
            UrlQrCode
        }
    }
}