﻿using DFe.Classes.Flags;
using NFe.Servicos;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Compression;
using Perlink.Shared.Encryptor;
using Perlink.Shared.IO.Arquivos;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Shared.Text;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.RPS.GeradorDeArquivo;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace Perlink.Trinks.NotaFiscalDoConsumidor.Services
{

    public class NotaNFCService : BaseService, INotaNFCService
    {
        public static string ObterAssinatura(string conteudo, byte[] certificadoBytes, string senhaDoCertificado)
        {
            var certificado = new X509Certificate2(certificadoBytes, senhaDoCertificado, X509KeyStorageFlags.Exportable);

            var data = Encoding.UTF8.GetBytes(conteudo);
            RSAParameters Key;            // Use the modern approach to get RSA private key
            using (RSA rsa = certificado.GetRSAPrivateKey())
            {
                if (rsa == null)
                    throw new InvalidOperationException("Certificado não possui chave privada RSA ou a chave não está acessível.");

                Key = rsa.ExportParameters(true);
            }

            var encrypted = AssinaturaDigital.HashAndSignBytes(data, Key, "SHA256");
            var retorno = Convert.ToBase64String(encrypted);

            return retorno;
        }

        public void AlterarStatusParaPendenteCancelamentoDeNotaNFC(Financeiro.Transacao transacao)
        {
            var notaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(transacao);
            if (notaNFC != null)
            {
                notaNFC.StatusNota = new StatusNotaNFC { IdStatusNotaNFC = (int)StatusNotaNFCEnum.PendenteEnvioCancelamentoSat };

                Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Update(notaNFC);
            }
        }

        public void NotificarErroDeNotaFiscalSat(int idEstabelecimento, int numeroDoFechamento, string mensagemDeErro)
        {
            var nota = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByNumeroFechamento(idEstabelecimento, numeroDoFechamento);
            nota.MensagemDeErroAoEmitirNota = mensagemDeErro;
            nota.StatusNota = new StatusNotaNFC() { IdStatusNotaNFC = (int)StatusNotaNFCEnum.ErroNaEmissao };
            Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Update(nota);
            Domain.Pessoas.EnvioEmailService.EnviarEmailErroNaEmissaoNFCeSAT(nota, mensagemDeErro);
        }

        public void CancelarNotaNFC(Financeiro.Transacao transacao)
        {
            var notaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(transacao);
            if (notaNFC != null)
            {
                notaNFC.StatusNota = new StatusNotaNFC { IdStatusNotaNFC = (int)StatusNotaNFCEnum.Cancelada };

                Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Update(notaNFC);
            }
        }

        public String GerarAssinaturaSat(string cnpjsVinculado)
        {
            var s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();
            var bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
            var arquivos = s3AmazonControleDeArquivos.ListarArquivos(bucket, "certificados");

            if (Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.certificado_perlink_arquivo) == null)
                return null;

            var nomeArquivoCertificado = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.certificado_perlink_arquivo).Valor;

            var senhaCertificado = Encryptor.Decrypt(Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.certificado_perlink_senha).Valor, "00ECAwQSisjurQoLmoBiLe==");

            var arquivoAmazonS3 = (ArquivoAmazonS3)arquivos.FirstOrDefault(f => String.Equals(f.NomeComExtensao, nomeArquivoCertificado, StringComparison.CurrentCultureIgnoreCase));
            var stream = new ControleDeArquivosAmazonS3().ObterStreamDeArquivo(arquivoAmazonS3);

            byte[] result;

            using (MemoryStream streamReader = new MemoryStream())
            {
                stream.CopyTo(streamReader);
                result = streamReader.ToArray();
            }

            return ObterAssinatura(cnpjsVinculado, result, senhaCertificado);
        }

        [TransactionInitRequired]
        public NotaNFC GerarNotaNFC(Financeiro.Transacao transacao, PessoaFisica pessoaQueEmitiu = null, bool ehConsulta = false, List<DadosParaGeracaoLoteRPSDTO> registros = null)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            var estabelecimentoConfiguracao = Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Queryable().FirstOrDefault(f => f.IdEstabelecimento.Equals(estabelecimento.IdEstabelecimento));
            //var ehInterfaceSAT = estabelecimentoConfiguracao.InterfaceNFC == Enums.TipoDeInterfaceNFC.Sat;

            var notaExistente = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.LoadByTransacao(transacao);

            if (ehConsulta && notaExistente != null)
            {
                notaExistente.Itens = Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.ObterItensDaNota(notaExistente);
                notaExistente.FormasPagamento = Domain.NotaFiscalDoConsumidor.NotaFormaPagamentoNFCRepository.ObterFormasDePagamentoDaNota(notaExistente);
                return notaExistente;
            }

            bool existeErroNotaNFC = notaExistente != null && notaExistente.StatusNota.IdStatusNotaNFC == (int)StatusNotaNFCEnum.ErroNaEmissao;

            if (existeErroNotaNFC)
            {
                VoltarSituacaoDaNotaParaPoderReemitir(notaExistente, pessoaQueEmitiu);
                return notaExistente;
            }

            if (estabelecimentoConfiguracao == null)
                throw new Exception("Configuracao de NFC do Estabelecimento não encontrada");

            if (notaExistente != null)
                throw new Exception("Este fechamento de conta já possui uma nota fiscal.");

            #region Incremento do Numero do Ultimo NFC

            //if (ehInterfaceSAT) {
            //    estabelecimentoConfiguracao.NumeroDoUltimoCFEmitido++;
            //}
            //else

            #endregion Incremento do Numero do Ultimo NFC

            var notaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Factory.Create(transacao);
            notaNFC.UltimaPessoaQueEmitiuSAT = pessoaQueEmitiu;
            notaNFC.NumeroNota = estabelecimentoConfiguracao.NFCProducaoLevandoEmContaParametro() ? estabelecimentoConfiguracao.NumeroDaUltimaNFC.Value : estabelecimentoConfiguracao.UltimaNfcHml;
            notaNFC.NumeroNota++;

            //notaNFC.NumeroNota = ehInterfaceSAT ? estabelecimentoConfiguracao.NumeroDoUltimoCFEmitido : (estabelecimentoConfiguracao.NFCProducaoLevandoEmContaParametro() ? estabelecimentoConfiguracao.NumeroDaUltimaNFC.Value : estabelecimentoConfiguracao.UltimaNfcHml);
            //if (!ehInterfaceSAT)
            notaNFC.NumeroSerie = estabelecimentoConfiguracao.NumeroSerieNFC;

            Domain.NotaFiscalDoConsumidor.NotaNFCRepository.SaveNew(notaNFC);

            foreach (var item in notaNFC.Itens)
            {
                Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.SaveNew(item);
            }

            if (registros != null && registros.Any())
            {
                var servicos = registros.SelectMany(r => r.Servicos).ToList();
                if (servicos != null && servicos.Any())
                {
                    foreach (var item in notaNFC.Itens)
                    {
                        if (item.ServicoEstabelecimento != null)
                        {
                            try
                            {
                                item.DescricaoCotaParte = servicos.FirstOrDefault(s => s.IdItem == item.CodigoHorarioTransacao)?.DescricaoCotas;
                                if (item.DescricaoCotaParte == null)
                                    item.DescricaoCotaParte = servicos.FirstOrDefault(s => s.IdServicoEstabelecimento == item.ServicoEstabelecimento.IdServicoEstabelecimento)?.DescricaoCotas;
                                Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.Update(item);
                            }
                            catch (Exception)
                            {
                            }
                        }
                    }
                }
            }

            var totalFormaPagamento = notaNFC.FormasPagamento.Sum(f => f.ValorPagamento);
            if (notaNFC.FormasPagamento.Sum(f => f.ValorPagamento) < notaNFC.TotalLiquido)
            {
                var restototalFormaPagamento = notaNFC.TotalLiquido - totalFormaPagamento;
                notaNFC.FormasPagamento.LastOrDefault().ValorPagamento += restototalFormaPagamento;
            }

            foreach (var item in notaNFC.FormasPagamento)
            {
                Domain.NotaFiscalDoConsumidor.NotaFormaPagamentoNFCRepository.SaveNew(item);
            }

            return notaNFC;
        }

        public int IncrementarNumeracaoDaNFe(ConfiguracaoDeNFCDoEstabelecimento estabelecimentoConfiguracao)
        {
            if (estabelecimentoConfiguracao.NFCProducaoLevandoEmContaParametro())
            {
                if (!estabelecimentoConfiguracao.NumeroDaUltimaNFC.HasValue)
                    estabelecimentoConfiguracao.NumeroDaUltimaNFC = 0;
                estabelecimentoConfiguracao.NumeroDaUltimaNFC++;

                if (estabelecimentoConfiguracao.NumeroDaUltimaNFC > 999999999)
                {
                    estabelecimentoConfiguracao.NumeroSerieNFC++;
                }

                return estabelecimentoConfiguracao.NumeroDaUltimaNFC ?? 0;
            }
            else
            {
                estabelecimentoConfiguracao.UltimaNfcHml++;

                if (estabelecimentoConfiguracao.UltimaNfcHml > 999999999)
                {
                    estabelecimentoConfiguracao.UltimaNfcHml = 0;
                }

                return estabelecimentoConfiguracao.UltimaNfcHml;
            }

            Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Update(estabelecimentoConfiguracao);
        }

        public int GerarItensVendaNFC(int idNota)
        {
            var notaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Load(idNota);

            if (notaNFC == null)
                return 0;

            notaNFC.Itens = Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.ObterItensDaNota(notaNFC);

            if (notaNFC.Itens != null && notaNFC.Itens.Any())
                return 0;

            notaNFC.Itens = new List<NotaItensNFC>();
            int numeroSequencialDoItemDaNota = 0;

            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(notaNFC.Transacao);
            if (venda != null)
            {
                var itensVenda = venda.ItensVenda.Where(f => f.ValorFinal() > 0 || f.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo);
                numeroSequencialDoItemDaNota = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Factory.AdicionarItensVenda(notaNFC, false, numeroSequencialDoItemDaNota, itensVenda);
            }

            foreach (var item in notaNFC.Itens)
            {
                Domain.NotaFiscalDoConsumidor.NotaItensNFCRepository.SaveNew(item);
            }

            return notaNFC.Itens.Count;
        }

        private static void VoltarSituacaoDaNotaParaPoderReemitir(NotaNFC notaExistente, PessoaFisica pessaoQueEmitiu = null)
        {
            notaExistente.MensagemDeErroAoEmitirNota = "";
            notaExistente.StatusNota = StatusNotaNFCEnum.Pendente;
            notaExistente.UltimaPessoaQueEmitiuSAT = pessaoQueEmitiu;
            Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Update(notaExistente);
        }

        public List<string> ListarXmlNotasFiscaisComSubstituicaoNotasCanceladas(List<NotaNFC> listaNFCs, bool filtrarCanceladas)
        {
            var listaNFCsAut = listaNFCs.Select(p => p.AutorizacaoSEFAZ + ".xml").ToList();

            if (!filtrarCanceladas)
                return listaNFCsAut;

            foreach (var nfc in listaNFCs)
            {
                if (nfc.StatusNota == StatusNotaNFCEnum.Cancelada)
                {
                    listaNFCsAut.Add("CAN_" + nfc.AutorizacaoSEFAZ.Replace("NFe", "") + ".xml");
                    listaNFCsAut.Add("RET_CAN_" + nfc.AutorizacaoSEFAZ.Replace("NFe", "") + ".xml");
                    //listaNFCsAut.Remove(nfc.AutorizacaoSEFAZ + ".xml");
                }
            }

            return listaNFCsAut;
        }

        public MemoryStream ListarZipArquivos(int idEstabelecimento, List<string> nomesArquivos)
        {
            var arquivosS3 = ListarArquivosXmlS3(idEstabelecimento, nomesArquivos);
            var arquivoZip = new ZipFile();

            var listaStreamArquivos = new List<Stream>();
            Parallel.ForEach(arquivosS3, (arquivo) =>
            {
                var dadosXml = GerarXml(arquivo);
                var nomeXml = arquivo.NomeComExtensao;
                if (!arquivo.NomeComExtensao.Contains("NFe") && !arquivo.NomeComExtensao.Contains("CAN"))
                    nomeXml = "Inutilizacoes/" + nomeXml;

                // Caso o arquivo não exita, para não gerar erro e continuar.
                try
                {
                    arquivoZip.Streams.Add(nomeXml, dadosXml);
                }
                catch (Exception)
                {
                }
            });

            var conteudoZip = new MemoryStream();
            arquivoZip.Save(conteudoZip, ZipContentType.Stream);
            conteudoZip.Flush();
            conteudoZip.Seek(0, SeekOrigin.Begin);
            return conteudoZip;
        }

        public NFe.Servicos.Retorno.RetornoRecepcaoEvento RealizarCancelamentoNotaNF(int idEstabelecimento, int numeroNota, string protocoloSefaz, string autorizacaoSefaz)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
            byte[] certificadoDigitalDoEstabelecimento = GeradorDeDadosParaNotaFiscalZeus.ObterCertificadoDigitalDoEstabelecimento(estabelecimento);
            var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa);
            var senhaCertificado = criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);

            var cpfcnpj = estabelecimento.PessoaJuridica.CNPJ;

            var configuracaoServico = GeradorDeDadosParaNotaFiscalZeus.ObterConfiguracaoServico(estabelecimento);
            var servicoNFe = new ServicosNFe(configuracaoServico);

            return servicoNFe.RecepcaoEventoCancelamento(numeroNota, 1, protocoloSefaz, autorizacaoSefaz, string.Format("NOTA NUMERO {0} CANCELADA", numeroNota), cpfcnpj);
        }

        // Rotina temporária para realizar a migração do XML de envio (gravado no S3)
        // para XML de armazenamento para o S3.
        public void TempConverterXMLsDeEnvioParaXMLComAutorizacao(int idEstabelecimento, List<string> nomesArquivos)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var arquivosS3 = ListarArquivosXmlS3(idEstabelecimento, nomesArquivos);

            var arquivos = ConverterXMLEnvioEmXMLComAutorizacao(estabelecimento, arquivosS3);

            Parallel.ForEach(arquivos,
                new ParallelOptions { MaxDegreeOfParallelism = 5 },
                (arquivo) =>
                {
                    ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();
                    string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
                    s3AmazonControleDeArquivos.GravarArquivo(arquivo.Value, bucket + "/NFCe/" + estabelecimento.IdEstabelecimento.ToString() + "/NotaConvertida", arquivo.Key);
                });
        }

        public List<KeyValuePair<string, Stream>> ConverterXMLEnvioEmXMLComAutorizacao(Estabelecimento estabelecimento, List<IArquivo> arquivosS3, int modelo = 65)
        {
            var listaStreamArquivos = new List<KeyValuePair<string, Stream>>();

            foreach (var arquivo in arquivosS3)
            {
                var dadosXml = GerarXml(arquivo);
                var nomeXml = arquivo.NomeComExtensao;

                StreamReader reader = new StreamReader(dadosXml);
                string xmlEnviado = reader.ReadToEnd();

                if (xmlEnviado.Contains("nfeProc"))
                    continue;

                ArquivoAmazonS3 arquivoCertificado = Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterCertificadoEhMigrarArquivosSeNecessario(estabelecimento.PessoaJuridica);
                if (arquivoCertificado == null)
                    throw new Exception("Não foi encontrado o certificado digital.");

                var stream = arquivoCertificado.S3FileInfo.OpenRead();

                byte[] result;

                using (MemoryStream streamReader = new MemoryStream())
                {
                    stream.CopyTo(streamReader);
                    result = streamReader.ToArray();
                }

                var configuracaoServico = GeradorDeDadosParaNotaFiscalZeus.ObterConfiguracaoServico(estabelecimento);
                configuracaoServico.ModeloDocumento = modelo == 55 ? ModeloDocumento.NFe : ModeloDocumento.NFCe;

                var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
                var pessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa);
                var senhaCertificado = criptografia.Decrypt(pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada);

                configuracaoServico.Certificado.Senha = senhaCertificado;
                configuracaoServico.Certificado.ArrayBytesArquivo = result;

                var servicoNFe = new ServicosNFe(configuracaoServico);
                var xml = GeradorDeDadosParaNotaFiscalZeus.GerarXmlDaNotaFiscalComOsDadosFiscaisEProtocoloDeAutorizacao(xmlEnviado, servicoNFe);

                var retorno = new MemoryStream();
                var writer = new StreamWriter(retorno);
                writer.Write(xml);
                writer.Flush();
                stream.Position = 0;
                listaStreamArquivos.Add(new KeyValuePair<string, Stream>(arquivo.NomeComExtensao, retorno));
            }

            //Parallel.ForEach(arquivosS3,
            //        new ParallelOptions { MaxDegreeOfParallelism = 5 },
            //        (arquivo) => {
            //        });

            return listaStreamArquivos;
        }

        private static List<IArquivo> ListarArquivosXmlS3(int idEstabelecimento, List<string> nomesArquivos)
        {
            ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();

            var configuracao = Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Queryable().First(f => f.IdEstabelecimento == idEstabelecimento);

            var arquivosS3 = new List<IArquivo>();

            if (configuracao.InterfaceNFC == TipoDeInterfaceNFC.NotaFiscalEletronica)
            {
                string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_importacao).ObterValor();
                arquivosS3 = s3AmazonControleDeArquivos.ListarArquivosPeloNome(nomesArquivos.Where(a => a.Contains("NFe") || a.Contains("CAN")), bucket, "NFCe", idEstabelecimento.ToString(), "Nota");
                var inutilizacoes = nomesArquivos.Where(a => !a.Contains("NFe") && !a.Contains("CAN"));
                if (inutilizacoes.Any())
                    arquivosS3.AddRange(s3AmazonControleDeArquivos.ListarArquivosPeloNome(inutilizacoes.ToList(), bucket, "NFCe", idEstabelecimento.ToString(), "INUT_"));
            }
            //else if (configuracao.InterfaceNFC == TipoDeInterfaceNFC.Sat) {
            //    string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_cupom_fiscal).ObterValor();
            //    arquivosS3 = s3AmazonControleDeArquivos.ListarArquivosPeloNome(nomesArquivos, bucket, idEstabelecimento.ToString(), "retorno");
            //}
            return arquivosS3.ToList();
        }

        private Stream GerarXml(IArquivo arquivo)
        {
            var stream = ((ArquivoAmazonS3)arquivo).S3FileInfo.OpenRead();
            byte[] result;

            using (MemoryStream streamReader = new MemoryStream())
            {
                stream.CopyTo(streamReader);
                result = streamReader.ToArray();
            }

            stream.Flush();
            stream.Seek(0, SeekOrigin.Begin);

            return stream;
        }

        public void EmitirNFCe(NotaNFC nota)
        {
            if (nota.StatusNota == StatusNotaNFCEnum.Emitida)
                throw new Exception("A nota já foi emitida.");

            var gerador = GeradorDeDadosParaNotaFiscalFactory.ConstruirGeradorDeNF(nota.Estabelecimento);
            gerador.EnviarNFCe(nota);
        }

        public string ObterXML(int idEstabelecimento, string chave)
        {

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var gerador = GeradorDeDadosParaNotaFiscalFactory.ConstruirGeradorDeNF(estabelecimento) as GeradorDeDadosParaNotaFiscalZeus;
            gerador.CarregarConfiguracaoServico();

            var configuracaoServico = gerador.ConfiguracaoServico;

            var servicoNFe = new ServicosNFe(configuracaoServico);

            var autorizacao = servicoNFe.NfeConsultaProtocolo(chave);

            if (autorizacao.Retorno.cStat != 100)
            {
                var chave43 = chave.Left(43);

                for (int i = 0; i < 10; i++)
                {
                    autorizacao = servicoNFe.NfeConsultaProtocolo(chave43 + i);

                    if (autorizacao.Retorno.cStat == 100)
                        break;
                }
            }

            return autorizacao.RetornoStr;
        }
    }
}