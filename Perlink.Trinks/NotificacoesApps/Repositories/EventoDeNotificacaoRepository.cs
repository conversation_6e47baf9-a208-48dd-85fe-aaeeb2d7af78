﻿using NHibernate;
using Perlink.DomainInfrastructure.Facilities;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using static Perlink.Trinks.PropertyNames.Pessoas;

namespace Perlink.Trinks.NotificacoesApps.Repositories
{
    public partial class EventoDeNotificacaoRepository : IEventoDeNotificacaoRepository
    {
        public void RegistrarComoEnviado(int id, int qtdDispositivos)
        {
            // Executa a atualização diretamente sem transação para reduzir locks
            string sql = "UPDATE DBO.NOTIF_Evento_De_Notificacao WITH (ROWLOCK) SET dt_envio = :dataHora, qtd_envios_realizados = :qtdDispositivos WHERE id = :id";
            IQuery query = Session().CreateSQLQuery(sql);
            query.SetParameter("dataHora", DateTime.Now);
            query.SetParameter("qtdDispositivos", qtdDispositivos);
            query.SetParameter("id", id);
            query.SetTimeout(5); // 5 segundos de timeout
            query.ExecuteUpdate();

            // Removido o refresh para reduzir a carga no banco de dados
        }

        public void RegistrarComoEnviadoEmLote(Dictionary<int, int> idsEQuantidades)
        {
            if (idsEQuantidades == null || idsEQuantidades.Count == 0)
                return;

            // Processa em lotes de 10 registros
            const int tamanhoDoBatch = 10;

            // Converte o dicionário para uma lista para facilitar o processamento em lotes
            var listaDeIds = idsEQuantidades.ToList();

            // Processa em lotes de 10 em 10
            for (int indiceInicial = 0; indiceInicial < listaDeIds.Count; indiceInicial += tamanhoDoBatch)
            {
                // Obtém o lote atual (até 10 itens)
                var loteDeDados = listaDeIds
                    .Skip(indiceInicial)
                    .Take(tamanhoDoBatch)
                    .ToDictionary(pair => pair.Key, pair => pair.Value);

                // Processa o lote atual
                ProcessarLoteDeRegistros(loteDeDados);
            }
        }

        private void ProcessarLoteDeRegistros(Dictionary<int, int> loteDeDados)
        {
            if (loteDeDados.Count == 0)
                return;

            const int commandTimeout = 5; // segundos

            if (loteDeDados.Count == 1)
            {
                // Para um único item, usa o método simples
                var item = loteDeDados.First();
                string sql = "UPDATE DBO.NOTIF_Evento_De_Notificacao WITH (ROWLOCK) SET dt_envio = :dataHora, qtd_envios_realizados = :qtdDispositivos WHERE id = :id";
                IQuery query = Session().CreateSQLQuery(sql);
                query.SetParameter("dataHora", DateTime.Now);
                query.SetParameter("qtdDispositivos", item.Value);
                query.SetParameter("id", item.Key);
                query.SetTimeout(commandTimeout);
                query.ExecuteUpdate();
            }
            else
            {
                // Para múltiplos itens, usa uma query CASE para atualizar tudo de uma vez
                var dataHora = DateTime.Now;
                var parameters = new Dictionary<string, object>();
                parameters.Add("dataHora", dataHora);

                var queryBuilder = new StringBuilder();
                queryBuilder.Append("UPDATE DBO.NOTIF_Evento_De_Notificacao WITH (ROWLOCK) SET dt_envio = :dataHora, qtd_envios_realizados = CASE id ");

                int i = 0;
                foreach (var pair in loteDeDados)
                {
                    string idParam = $"id{i}";
                    string qtdParam = $"qtd{i}";

                    queryBuilder.AppendFormat("WHEN :{0} THEN :{1} ", idParam, qtdParam);

                    parameters.Add(idParam, pair.Key);
                    parameters.Add(qtdParam, pair.Value);

                    i++;
                }

                queryBuilder.Append("END WHERE id IN (");

                for (int j = 0; j < loteDeDados.Count; j++)
                {
                    if (j > 0)
                        queryBuilder.Append(", ");

                    queryBuilder.AppendFormat(":id{0}", j);
                }

                queryBuilder.Append(")");

                IQuery query = Session().CreateSQLQuery(queryBuilder.ToString());

                // Adiciona todos os parâmetros à query
                foreach (var param in parameters)
                {
                    query.SetParameter(param.Key, param.Value);
                }

                query.SetTimeout(commandTimeout);
                query.ExecuteUpdate();
            }
        }
    }
}
