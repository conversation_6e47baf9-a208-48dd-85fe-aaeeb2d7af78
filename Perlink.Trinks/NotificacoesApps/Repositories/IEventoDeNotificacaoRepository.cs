﻿using System.Collections.Generic;

namespace Perlink.Trinks.NotificacoesApps.Repositories
{
    public partial interface IEventoDeNotificacaoRepository
    {
        void RegistrarComoEnviado(int id, int qtdDispositivos);

        /// <summary>
        /// Registra múltiplos eventos de notificação como enviados em uma única operação de banco de dados
        /// </summary>
        /// <param name="idsEQuantidades">Dicionário onde a chave é o ID do evento e o valor é a quantidade de dispositivos</param>
        void RegistrarComoEnviadoEmLote(Dictionary<int, int> idsEQuantidades);
    }
}
