﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Trinks.PushNotifications;

namespace Perlink.Trinks.NotificacoesApps.Services
{
    public class DisparoMensagensDeNotificacaoService : BaseService, IDisparoMensagensDeNotificacaoService
    {
        private readonly FilaDeDisparoNotificacoesService _filaDeDisparo;
        private readonly AppProFcmService _appProFcmService;

        public DisparoMensagensDeNotificacaoService()
        {
            _filaDeDisparo = new FilaDeDisparoNotificacoesService();

            LogInfoPushB2B("Buscando credenciais do Firebase para envio de notificações push.");

            string projectId = new ParametrosTrinks<string>(ParametrosTrinksEnum.push_b2b_firebase_project_id).ObterValor();
            string clientEmail = new ParametrosTrinks<string>(ParametrosTrinksEnum.push_b2b_firebase_client_email).ObterValor();
            string privateKey = new ParametrosTrinks<string>(ParametrosTrinksEnum.push_b2b_firebase_private_key).ObterValor();

            if (string.IsNullOrWhiteSpace(projectId) || string.IsNullOrWhiteSpace(clientEmail) || string.IsNullOrWhiteSpace(privateKey))
            {
                if (string.IsNullOrWhiteSpace(projectId))
                    LogErrorPushB2B("Parâmetro 'push_b2b_firebase_project_id' não configurado.");

                if (string.IsNullOrWhiteSpace(clientEmail))
                    LogErrorPushB2B("Parâmetro 'push_b2b_firebase_client_email' não configurado.");

                if (string.IsNullOrWhiteSpace(privateKey))
                    LogErrorPushB2B("Parâmetro 'push_b2b_firebase_private_key' não configurado.");

                _appProFcmService = null;
            }
            else
            {
                LogInfoPushB2B("Criando credenciais para envio de push.");

                var firebaseCredentials = new FirebaseAppCredentials(
                    projectId: projectId,
                    clientEmail: clientEmail,
                    privateKey: privateKey
                );

                LogInfoPushB2B("Criando instância de logger para serviço de push B2B.");

                var logger = (IFcmServiceLogger)Domain.NotificacoesApps.AppProFcmServiceLogger;

                if (logger == null)
                {
                    LogErrorPushB2B("Logger não configurado.");
                    _appProFcmService = null;
                }
                else
                {
                    LogInfoPushB2B("Criando serviço de envio de push.");
                    _appProFcmService = new AppProFcmService(firebaseCredentials, logger);
                    LogInfoPushB2B("Tudo pronto para o envio de notificações push.");
                }
            }
        }

        public void IncluirNaFila(MensagemDeNotificacao mensagemDeNotificacao)
        {
            if (!Pessoas.Configuracoes.ConfiguracoesTrinks.Geral.EnviarNotificacoesPush)
                return;

            _filaDeDisparo.SaveNewMessage(new MensagemNaFilaDisparo
            {
                Id = mensagemDeNotificacao.Id,
                IdPessoaRecebedor = mensagemDeNotificacao.EventoDeNotificacao.IdPessoa,
                Mensagem = mensagemDeNotificacao.Mensagem,
                Titulo = mensagemDeNotificacao.Titulo,
                ParametrosEnvio = mensagemDeNotificacao.EventoDeNotificacao.ObterParametrosDeEnvioDeserializados(),
            });
        }

        public bool DispararMensagensDaFila()
        {
            if (_appProFcmService == null)
            {
                LogErrorPushB2B("Execução abortada porque o serviço de envio não pôde ser instanciado.");
                return false;
            }

            if (!Pessoas.Configuracoes.ConfiguracoesTrinks.Geral.EnviarNotificacoesPush)
                return false;

            int qtdMaximaDeMensagens = new ParametrosTrinks<int>(ParametrosTrinksEnum.push_b2b_quantidade_maxima_disparos_por_execucao).ObterValor();

            LogInfoPushB2B($"Procurando mensagens para envio de push. Envio de até {qtdMaximaDeMensagens} mensagens");

            var mensagens = new List<KeyValuePair<string, MensagemNaFilaDisparo>>();
            while (true)
            {
                var mensagemNaFila = _filaDeDisparo.GetMessage(out string messageHandle);
                if (mensagemNaFila == null)
                    break;

                mensagens.Add(new KeyValuePair<string, MensagemNaFilaDisparo>(messageHandle, mensagemNaFila));

                if (qtdMaximaDeMensagens > 0 && mensagens.Count >= qtdMaximaDeMensagens)
                    break;
            }

            if (mensagens.Count > 0)
                LogInfoPushB2B(string.Format("{0} mensagens sendo encaminhadas para envio.", mensagens.Count));

            if (mensagens.Count == 0)
                return false;

            int grauDeParalelismo = new ParametrosTrinks<int>(ParametrosTrinksEnum.push_b2b_grau_maximo_de_paralelismo).ObterValor();

            // Coletor para armazenar os resultados do processamento em paralelo
            var resultadosProcessamento = new System.Collections.Concurrent.ConcurrentDictionary<int, int>();

            // Processa as mensagens em paralelo, mas coleta os resultados para atualização em lote
            Parallel.ForEach(mensagens,
                    grauDeParalelismo > 0
                      ? new ParallelOptions { MaxDegreeOfParallelism = grauDeParalelismo }
                      : new ParallelOptions(),
                    m => ProcessarMensagem(m.Value, m.Key, resultadosProcessamento));

            // Após o processamento em paralelo, atualiza o banco de dados em lote
            if (resultadosProcessamento.Count > 0)
            {
                LogInfoPushB2B($"Atualizando {resultadosProcessamento.Count} eventos de notificação em lote.");
                Domain.NotificacoesApps.EventoDeNotificacaoRepository.RegistrarComoEnviadoEmLote(
                    resultadosProcessamento.ToDictionary(k => k.Key, v => v.Value));
            }

            LogInfoPushB2B("Envio concluído.");
            return true;
        }

        private static int[] IdsDeRecebedoresParaMonitorarTemporariamente = new int[] { 19678400, 36482816 };

        // Método mantido para compatibilidade com código existente
        private void DispararMensagem(MensagemNaFilaDisparo mensagemNaFila, string messageHandle)
        {
            var resultadosProcessamento = new System.Collections.Concurrent.ConcurrentDictionary<int, int>();
            ProcessarMensagem(mensagemNaFila, messageHandle, resultadosProcessamento);

            // Atualiza o banco de dados individualmente para manter compatibilidade
            foreach (var resultado in resultadosProcessamento)
            {
                Domain.NotificacoesApps.EventoDeNotificacaoRepository.RegistrarComoEnviado(resultado.Key, resultado.Value);
            }
        }

        private void ProcessarMensagem(MensagemNaFilaDisparo mensagemNaFila, string messageHandle,
            System.Collections.Concurrent.ConcurrentDictionary<int, int> resultadosProcessamento)
        {
            bool logExtraTemporario = IdsDeRecebedoresParaMonitorarTemporariamente.Contains(mensagemNaFila.IdPessoaRecebedor);

            if (logExtraTemporario)
            {
                LogInfoPushB2B(string.Format("Buscando dispositivos para pessoa {0}", mensagemNaFila.IdPessoaRecebedor));
            }

            var tokensDispositivos = Domain.TrinksApps.RegistroDeDispositivosService.ListarTokensDeDispositivosAtivosRegistradosPelaPessoa(mensagemNaFila.IdPessoaRecebedor);

            if (!tokensDispositivos.Any())
            {
                if (logExtraTemporario)
                {
                    LogInfoPushB2B(string.Format("Nenhum dispositivo encontrado para pessoa {0}", mensagemNaFila.IdPessoaRecebedor));
                }

                _filaDeDisparo.DeleteMessage(messageHandle);
                return;
            }

            if (logExtraTemporario)
            {
                LogInfoPushB2B(string.Format("{0} dispositivo(s) encontrado(s) para pessoa {1}", tokensDispositivos.Count, mensagemNaFila.IdPessoaRecebedor));
            }

            try
            {
                if (logExtraTemporario)
                {
                    LogInfoPushB2B(string.Format("Enviando notificações para pessoa {0}", mensagemNaFila.IdPessoaRecebedor));
                }

                foreach (var token in tokensDispositivos)
                {
                    _appProFcmService.SendNotification(token, mensagemNaFila.Titulo, mensagemNaFila.Mensagem, mensagemNaFila.ParametrosEnvio, FcmPayloadPriority.High);
                }

                if (logExtraTemporario)
                {
                    LogInfoPushB2B(string.Format("Registrando notificação {0} da pessoa {1} como enviada", mensagemNaFila.Id, mensagemNaFila.IdPessoaRecebedor));
                }

                // Em vez de atualizar o banco de dados imediatamente, coletamos os resultados para atualização em lote
                ColetarResultadoParaAtualizacaoEmLote(mensagemNaFila, tokensDispositivos, resultadosProcessamento);

                if (logExtraTemporario)
                {
                    LogInfoPushB2B(string.Format("Notificação {0} da pessoa {1} preparada para atualização em lote", mensagemNaFila.Id, mensagemNaFila.IdPessoaRecebedor));
                }
            }
            catch (Exception ex)
            {
                LogErrorPushB2B(string.Format("Erro no disparo de push: {0}.", ex.Message));
                LogErrorPushB2B(ex.StackTrace);

                if (ex.InnerException != null)
                {
                    LogErrorPushB2B(ex.InnerException.Message);
                    LogErrorPushB2B(ex.InnerException.StackTrace);
                }
            }
            finally
            {
                _filaDeDisparo.DeleteMessage(messageHandle);
            }
        }

        // Método mantido para compatibilidade com código existente
        private void RegistrarEnvioDaNotificacao(MensagemNaFilaDisparo mensagemNaFila, List<string> tokensDispositivos)
        {
            int qtdDispositivos = tokensDispositivos.Count();

            var mensagemDeNotificacao = Domain.NotificacoesApps.MensagemDeNotificacaoRepository.Load(mensagemNaFila.Id);
            if (mensagemDeNotificacao != null)
            {
                var eventoDeNotificacao = mensagemDeNotificacao.EventoDeNotificacao;

                if (eventoDeNotificacao.Id > 0)
                    Domain.NotificacoesApps.EventoDeNotificacaoRepository.RegistrarComoEnviado(eventoDeNotificacao.Id, qtdDispositivos);
                else
                    Domain.NotificacoesApps.EventoDeNotificacaoRepository.SaveNew(eventoDeNotificacao);
            }
        }

        private void ColetarResultadoParaAtualizacaoEmLote(MensagemNaFilaDisparo mensagemNaFila, List<string> tokensDispositivos,
            System.Collections.Concurrent.ConcurrentDictionary<int, int> resultadosProcessamento)
        {
            int qtdDispositivos = tokensDispositivos.Count();

            var mensagemDeNotificacao = Domain.NotificacoesApps.MensagemDeNotificacaoRepository.Load(mensagemNaFila.Id);
            if (mensagemDeNotificacao != null)
            {
                var eventoDeNotificacao = mensagemDeNotificacao.EventoDeNotificacao;

                if (eventoDeNotificacao.Id > 0)
                {
                    // Adiciona ao dicionário para atualização em lote
                    resultadosProcessamento.AddOrUpdate(
                        eventoDeNotificacao.Id,
                        qtdDispositivos,
                        (key, existingValue) => Math.Max(existingValue, qtdDispositivos)
                    );
                }
                else
                {
                    // Para novos eventos, ainda precisamos salvar individualmente
                    Domain.NotificacoesApps.EventoDeNotificacaoRepository.SaveNew(eventoDeNotificacao);
                }
            }
        }

        private void LogInfoPushB2B(string msg)
        {
            LogService<DisparoMensagensDeNotificacaoService>.Info(string.Format("[Push B2B][Info] {0}", msg));
        }

        private void LogErrorPushB2B(string msg)
        {
            LogService<DisparoMensagensDeNotificacaoService>.Error(string.Format("[Push B2B][Error] {0}", msg));
        }
    }
}
