﻿using Perlink.Shared.Encryptor;
using Perlink.Trinks.EnvioMensagem.Enums;
using Perlink.Trinks.Notificacoes.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;

namespace Perlink.Trinks.EnvioMensagem
{

    public class EnderecoEmail
    {
        public string Email { get; set; }

        public string Nome { get; set; }

        public static implicit operator EnderecoEmail(MailAddress mail)
        {
            return new EnderecoEmail
            {
                Email = mail.Address,
                Nome = mail.DisplayName
            };
        }

        public static implicit operator MailAddress(EnderecoEmail mail)
        {
            if (string.IsNullOrWhiteSpace(mail.Nome))
                return new MailAddress(mail.Email);

            return new MailAddress(mail.Email, mail.Nome);
        }
    }

    public class Mensagem
    {

        public Mensagem()
        {
            DataLimite = Calendario.Agora().AddHours(1);
        }

        public string Conteudo { get; set; }
        public DateTime DataLimite { get; set; }
        public string Id { get; set; }
        public OrigemMensagemEnum Origem { get; set; }

        public virtual string Hash()
        {
            return Hashing.Hash(Conteudo);
        }
    }

    public class MensagemEmail : Mensagem
    {

        public MensagemEmail()
        {
            Destinatarios = new List<EnderecoEmail>();
            DestinatariosOcultos = new List<EnderecoEmail>();
        }

        public string Assunto { get; set; }
        public List<EnderecoEmail> Destinatarios { get; set; }
        public List<EnderecoEmail> DestinatariosOcultos { get; set; }
        public EnderecoEmail Remetente { get; set; }

        public new string Hash()
        {
            return Hashing.Hash(string.Join(";", Destinatarios.Select(f => f.Email)) + Assunto + Conteudo);
        }

        public override string ToString()
        {
            return Assunto + " - " + Destinatarios;
        }
    }

    public class MensagemSMS : Mensagem
    {
        public int DDD { get; set; }
        public string GatewayUrl { get; set; }
        public int Telefone { get; set; }

        public string DiscriminadorDeEnvio { get; set; }

        public new string Hash()
        {
            if (Origem == OrigemMensagemEnum.LembreteDeAgendamento)
                return Hashing.Hash(DDD.ToString() + Telefone + DiscriminadorDeEnvio);

            return Hashing.Hash(DDD.ToString() + Telefone + Origem + Conteudo + DiscriminadorDeEnvio);
        }

        public override string ToString()
        {
            return Conteudo + " - " + DDD + Telefone;
        }
    }

    public class MensagemWhatsApp : Mensagem
    {
        public int Ddi { get; set; }
        public int DDD { get; set; }
        public int Telefone { get; set; }
    }

    public class MensagemPush
    {
        public int IdNotificacaoPush { get; set; }
        public int IdContaDoDestinatario { get; set; }
        public string Titulo { get; set; }
        public string Texto { get; set; }
        public int? IdAgendamento { get; set; }
        public int? IdRecorrencia { get; set; }
        public int? IdEstabelecimento { get; set; }
        public AcaoNotificacaoPushEnum AcaoNotificacaoPushEnum { get; set; }
        public string Parametro { get; set; }
        public int IdAplicativoDeAgendamento { get; set; }
    }

    public class MensagemPushProgramada
    {
        public int IdCampanha { get; set; }
        public int? IdFranquia { get; set; }
        public string Titulo { get; set; }
        public string Mensagem { get; set; }
    }

    public class MensagemPushParaDisparo
    {
        public int IdMensagem { get; set; }
        public int IdAplicativo { get; set; }
        public string Titulo { get; set; }
        public string Mensagem { get; set; }
        public string[] DeviceTokens { get; set; }
        public int? IdAgendamento { get; set; }
        public int? IdRecorrencia { get; set; }
        public int? IdEstabelecimento { get; set; }
        public AcaoNotificacaoPushEnum AcaoNotificacaoPush { get; set; }
        public string Parametro { get; set; }
    }
}