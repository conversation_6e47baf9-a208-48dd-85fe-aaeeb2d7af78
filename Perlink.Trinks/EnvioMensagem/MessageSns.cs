using Newtonsoft.Json;
using Perlink.Trinks.Pessoas.Helpers;
using System.Collections.Generic;

namespace Perlink.Trinks.EnvioMensagem
{
    public class MessageSns
    {

        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("countryCode")]
        public string CountryCode { get; set; }

        [JsonProperty("areaCode")]
        public string AreaCode { get; set; }

        [JsonProperty("phone")]
        public string Phone { get; set; }

        [JsonProperty("content")]
        public string Content { get; set; }

        [JsonProperty("contentSid")]
        public string TemplateSid { get; set; }

        [JsonProperty("contentVariables")]
        public Dictionary<int, string> ContentVariables { get; set; }

        [JsonProperty("customerEstablishmentId")]
        public int? CustomerEstablishmentId { get; set; }

        [JsonIgnore]
        public string FullPhone => $"+{CountryCode}{AreaCode}{PhoneNineDigitHandler()}";

        private string PhoneNineDigitHandler()
        {
            if (CountryCode != DdiConstants.Brasil || Phone.Length != 8) return Phone;

            if (Phone.StartsWith("9") || Phone.StartsWith("8") || Phone.StartsWith("7"))
            {
                return $"9{Phone}";
            }

            return Phone;
        }
    }
}