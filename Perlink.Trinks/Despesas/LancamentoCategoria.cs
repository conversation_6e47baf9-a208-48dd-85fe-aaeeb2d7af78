﻿using Castle.ActiveRecord;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;

namespace Perlink.Trinks.Despesas
{

    [ActiveRecord("FINC_LANCAMENTO_CATEGORIA", Lazy = true, DynamicInsert = true, DynamicUpdate = true)]
    [Serializable]
    public class LancamentoCategoria : ActiveRecordBase<LancamentoCategoria>
    {

        public LancamentoCategoria()
        {
            NecessarioInformarProfissional = false;
            NecessarioInformarFornecedor = false;
            Ativo = true;
            PermiteEdicao = true;
            PermiteExclusao = true;
            PermiteLancamentoManual = true;
            EhDespesaPersonalizadaComCategoriaPadrao = false;
        }

        public LancamentoCategoria(int id)
        {
            IdLancamentoCategoria = id;
            Refresh();
        }

        public LancamentoCategoria(String nome, Estabelecimento estabelecimento)
            : this()
        {
            Nome = nome;
            Estabelecimento = estabelecimento;
            LancamentoTipo = new LancamentoTipo() { IdLancamentoTipo = 2 };
            NivelMinimoAcesso = (int)AcessoBackoffice.Acesso_total;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_finc_lancamento_categoria", ColumnType = "Int32")]
        public virtual Int32 IdLancamentoCategoria { get; set; }

        [BelongsTo("id_estabelecimento", Lazy = FetchWhen.OnInvoke)]
        public virtual Estabelecimento Estabelecimento { get; set; }

        [BelongsTo("id_finc_lancamento_tipo", Lazy = FetchWhen.OnInvoke)]
        public virtual LancamentoTipo LancamentoTipo { get; set; }

        [BelongsTo("id_finc_lancamento_categoria_padrao", Lazy = FetchWhen.OnInvoke)]
        public virtual LancamentoCategoriaPadrao LancamentoCategoriaPadrao { get; set; }

        [BelongsTo("id_finc_lancamento_grupo")]
        public virtual LancamentoGrupo LancamentoGrupo { get; set; }

        [Property("nm_finc_lancamento_categoria")]
        public virtual String Nome { get; set; }

        [Property("nivel_minimo_acesso_a_lancamento_dessa_categoria")]
        public virtual int NivelMinimoAcesso { get; set; }

        [Property("necessario_informar_profissional_no_lancamento")]
        public virtual Boolean NecessarioInformarProfissional { get; set; }

        [Property("necessario_informar_fornecedor_no_lancamento")]
        public virtual Boolean NecessarioInformarFornecedor { get; set; }

        [Property("ativo")]
        public virtual Boolean Ativo { get; set; }

        [Property("pemite_lancamento_manual")]
        public virtual Boolean PermiteLancamentoManual { get; set; }

        [Property("permite_edicao")]
        public virtual Boolean PermiteEdicao { get; set; }

        [Property("permite_exclusao")]
        public virtual Boolean PermiteExclusao { get; set; }

        [Property("eh_despesa_personalizada_com_categoria_padrao")]
        public virtual bool EhDespesaPersonalizadaComCategoriaPadrao { get; set; }

        public virtual void Inativar()
        {
            Ativo = false;
        }

        public virtual void Ativar()
        {
            Ativo = true;
        }
    }
}