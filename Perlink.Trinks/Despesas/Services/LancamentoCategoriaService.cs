﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.DataQuery;
using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using RES = Perlink.Trinks.Resources.Mensagens;

namespace Perlink.Trinks.Despesas.Services
{

    public class LancamentoCategoriaService : BaseService, ILancamentoCategoriaService
    {
        /// <summary>
        /// Método para salvar ou alterar o tipo de despesa do estabelecimento
        /// </summary>
        /// <param name="lancamentoCategoria">Instancia do DespesaEstabelecimento com todas as informações para ser persistida no banco</param>
        [TransactionInitRequired]
        public void ManterLancamentoCategoria(LancamentoCategoria lancamentoCategoria)
        {
            ValidarManter(lancamentoCategoria);

            if (ValidationHelper.Instance.IsValid)
            {
                Domain.Despesas.LancamentoCategoriaRepository.SaveOrUpdate(lancamentoCategoria);
            }
        }

        /// <summary>
        ///  Método que cria DespesaEstabelecimento para o estabelecimento baseado na tabela DespesaTipo
        /// </summary>
        /// <param name="estabelecimento">Estabelecimento para qual as despesas serão criadas</param>
        [TransactionInitRequired]
        public void CriarDespesasParaOEstabelecimento(Estabelecimento estabelecimento)
        {
            var despesasTipoAtivos = Domain.Despesas.LancamentoCategoriaPadraoRepository.ListarDespesasAtivasParaCadastroEstabelecimento();

            LancamentoTipo tipo = new LancamentoTipo();
            tipo.IdLancamentoTipo = (int)LancamentoTipoEnum.Despesa;

            var validaSeJaHaDespesaCadastrada = Domain.Despesas.LancamentoCategoriaRepository.ValidaSeEstabelecimentoJaPossuiLancamentosCategoriaPorTipo(estabelecimento.IdEstabelecimento, tipo);

            if (!validaSeJaHaDespesaCadastrada)
            {
                foreach (LancamentoCategoriaPadrao despesaTipo in despesasTipoAtivos)
                {
                    LancamentoCategoria lancamentoCategoria = CriarEntidadeBaseadaEmCategoriaPadrao(estabelecimento, despesaTipo);
                    Domain.Despesas.LancamentoCategoriaRepository.SaveNew(lancamentoCategoria);
                }
            }
        }

        private LancamentoCategoria CriarEntidadeBaseadaEmCategoriaPadrao(Estabelecimento estabelecimento, LancamentoCategoriaPadrao categoriaPadrao)
        {
            return new LancamentoCategoria()
            {
                Nome = categoriaPadrao.Nome,
                LancamentoCategoriaPadrao = categoriaPadrao,
                Estabelecimento = estabelecimento,
                LancamentoTipo = categoriaPadrao.LancamentoTipo,
                NecessarioInformarFornecedor = categoriaPadrao.NecessarioInformarFornecedor,
                NecessarioInformarProfissional = categoriaPadrao.NecessarioInformarProfissional,
                Ativo = true,
                NivelMinimoAcesso = (int)categoriaPadrao.NivelMinimoAcesso,
                LancamentoGrupo = Domain.Despesas.LancamentoGrupoService.ObterOuCriarLancamentoGrupoDoEstabelecimentoPorGrupoPadrao(estabelecimento.IdEstabelecimento, categoriaPadrao.LancamentoGrupoPadrao.IdLancamentoGrupoPadrao)
            };
        }

        public ResultadoPaginado<LancamentoCategoria> Filtrar(FiltroBuscaLancamentosCategoria filtro)
        {
            var query = Domain.Despesas.LancamentoCategoriaRepository.Filtrar(filtro);

            filtro.ParametrosPaginacao.TotalItens = query.Count();

            query = query.Skip(filtro.ParametrosPaginacao.RegistroInicial - 1).Take(filtro.ParametrosPaginacao.RegistrosPorPagina);

            return new ResultadoPaginado<LancamentoCategoria>(query.ToList(), filtro.ParametrosPaginacao);
        }

        public void AlterarStatusCategoriaLancamento(int idCategoria)
        {
            if (idCategoria != 0)
            {
                LancamentoCategoria categoria = Domain.Despesas.LancamentoCategoriaRepository.Load(idCategoria);

                if (categoria != null)
                {
                    categoria.Ativo = !categoria.Ativo;
                    ManterLancamentoCategoria(categoria);
                }
            }
        }

        public bool PermiteLancarOuVisualizarTipoDeDespesa(LancamentoCategoria lancamentoCategoria, Estabelecimento estabelecimento, AcessoBackoffice acessoAoBackOffice)
        {
            return lancamentoCategoria != null &&
                   lancamentoCategoria.Estabelecimento == estabelecimento &&
                   lancamentoCategoria.NivelMinimoAcesso <= (int)acessoAoBackOffice;
        }

        #region Validações

        private void ValidarManter(LancamentoCategoria lancamentoCategoria)
        {
            if (!String.IsNullOrEmpty(lancamentoCategoria.Nome))
                lancamentoCategoria.Nome = lancamentoCategoria.Nome.Trim();

            if (String.IsNullOrEmpty(lancamentoCategoria.Nome))
                ValidationHelper.Instance.AdicionarItemValidacao(RES.ONomeDoTipoDespesaEhObrigatorio);
            else if (lancamentoCategoria.Nome.Length > 200)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.ONomeDoTipoDespesaNaoPodeUltrapassar200Caracteres);
            else if (lancamentoCategoria.LancamentoGrupo == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(RES.InformeCategoriaGrupo);
            }
            var existeLancamentoCategoriaComMesmaDescricao =
                Domain.Despesas.LancamentoCategoriaRepository.ExisteLancamentoCategoriaComMesmoNome(lancamentoCategoria.Nome, lancamentoCategoria.IdLancamentoCategoria, lancamentoCategoria.Estabelecimento.IdEstabelecimento);

            if (existeLancamentoCategoriaComMesmaDescricao)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.JaExisteUmTipoDeDespesaComADescricaoInformada);

            if (lancamentoCategoria.NecessarioInformarFornecedor && lancamentoCategoria.NecessarioInformarProfissional)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.ADespesaSoPodeSerRelacionadoAUmProfissionalOuFornecedor);
        }

        #endregion Validações

        public LancamentoCategoria ObterOuCriarLancamentoCategoriaPadrao(Estabelecimento estabelecimento, int idLancamentoCategoriaPadrao)
        {

            var lancamentoCategoriaEstabelecimento = Domain.Despesas.LancamentoCategoriaRepository.ObterLancamentoCategoriaDoTipoNoEstabelecimento((CategoriaDeLancamentoPadraoEnum)idLancamentoCategoriaPadrao, estabelecimento.IdEstabelecimento);

            if (lancamentoCategoriaEstabelecimento == null)
            {
                var lancamentoCategoriaPadrao = Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterPorId(idLancamentoCategoriaPadrao);
                lancamentoCategoriaEstabelecimento = CriarEntidadeBaseadaEmCategoriaPadrao(estabelecimento, lancamentoCategoriaPadrao);
                Domain.Despesas.LancamentoCategoriaRepository.SaveNew(lancamentoCategoriaEstabelecimento);
            }

            return lancamentoCategoriaEstabelecimento;
        }

        public List<LancamentoCategoriaParaLancamentoEmLoteDTO> ListarCategoriasParaLancamentoEmLote(int idEstabelecimento)
        {
            var lancamentosCategoriaAtivosComValeOuBonificacaoComoCategoriaPadrao = Domain.Despesas.LancamentoCategoriaRepository.ListarLancamentosCategoriaAtivosComValeOuBonificacaoComoCategoriaPadrao(idEstabelecimento);

            var tiposLancamentosEmLote = lancamentosCategoriaAtivosComValeOuBonificacaoComoCategoriaPadrao
                .Select(c => new LancamentoCategoriaParaLancamentoEmLoteDTO(c.IdLancamentoCategoria, c.LancamentoCategoriaPadrao.IdLancamentoCategoria, c.Nome))
                .ToList();

            return tiposLancamentosEmLote;
        }

        public int ObterIdLancamentoCategoriaPadrao(SalvarLancamentoCategoriaDTO salvarCategoriaDTO)
        {
            var idLancamentoCategoriaPadraoBonificacao = (int)CategoriaDeLancamentoPadraoEnum.Bonificacao;
            var idLancamentoCategoriaPadraoVale = (int)CategoriaDeLancamentoPadraoEnum.ValeProfissional;

            if (salvarCategoriaDTO.AcrescentarNaComissaoDoProfissional)
            {
                return idLancamentoCategoriaPadraoBonificacao;
            }

            if (salvarCategoriaDTO.DescontarDaComissaoDoProfissional)
            {
                return idLancamentoCategoriaPadraoVale;
            }

            return 0;
        }

        public IList<LancamentoCategoriaBaseDTO> FiltrarDespesasCadastradas(FiltroDespesasCadastradasDTO filtro)
        {
            var lancamentosCategoria = Domain.Despesas.LancamentoCategoriaRepository.ObterTodosLancamentoCategoriaDoEstabelecimento(filtro);

            if (!string.IsNullOrEmpty(filtro.NomeLancamentoGrupo))
            {
                lancamentosCategoria = lancamentosCategoria.Where(l => l.LancamentoGrupo.NomeGrupoCategoria.ToLower() == filtro.NomeLancamentoGrupo.ToLower());
            }

            if (!string.IsNullOrEmpty(filtro.Nome))
            {
                lancamentosCategoria = lancamentosCategoria.Where(l => l.Nome.ToLower() == filtro.Nome.ToLower());
            }

            if (!string.IsNullOrEmpty(filtro.Status))
            {
                Enum.TryParse<StatusLancamentoCategoriaEnum>(filtro.Status, out var statusEnum);

                if (statusEnum == StatusLancamentoCategoriaEnum.Ativo)
                {
                    lancamentosCategoria = lancamentosCategoria.Where(l => l.Ativo);
                }
                else if (statusEnum == StatusLancamentoCategoriaEnum.Inativo)
                {
                    lancamentosCategoria = lancamentosCategoria.Where(l => !l.Ativo);
                }
            }

            if (filtro.Relacao != null)
            {
                Enum.TryParse<RelacaoLancamentoCategoriaEnum>(filtro.Relacao.ToString(), out var relacaoEnum);

                switch (relacaoEnum)
                {
                    case RelacaoLancamentoCategoriaEnum.Nenhum:
                        lancamentosCategoria = lancamentosCategoria.Where(l => !l.NecessarioInformarFornecedor && !l.NecessarioInformarProfissional);
                        break;
                    case RelacaoLancamentoCategoriaEnum.Fornecedor:
                        lancamentosCategoria = lancamentosCategoria.Where(l => l.NecessarioInformarFornecedor);
                        break;
                    case RelacaoLancamentoCategoriaEnum.Profissional:
                        lancamentosCategoria = lancamentosCategoria.Where(l => l.NecessarioInformarProfissional);
                        break;
                }
            }

            lancamentosCategoria = lancamentosCategoria.ApplySorting(filtro);

            lancamentosCategoria = lancamentosCategoria.ApplyPagination(filtro);

            var despesas = lancamentosCategoria.
                Select(lancamento => new LancamentoCategoriaBaseDTO(lancamento))
                .ToList();

            return despesas;
        }

        public IList<GruposParaCadastrarDespesaDTO> ObterListaLancamentoGrupo(int idEstabelecimento)
        {
            var listaLancamentoGrupo = Domain.Despesas.LancamentoGrupoRepository.ObterLancamentosGrupoDoEstabelecimento(idEstabelecimento, true);

            var retorno = listaLancamentoGrupo
                .Select(lancamentoGrupo => new GruposParaCadastrarDespesaDTO(lancamentoGrupo))
                .ToList();

            return retorno;
        }

        public void AlterarStatusCategoriaLancamento(int idLancamentoCategoria, AlterarStatusLancamentoCategoriaEnum status)
        {
            if (idLancamentoCategoria == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Despesa não encontrada.");
                return;
            }

            var lancamentoCategoria = Domain.Despesas.LancamentoCategoriaRepository.Load(idLancamentoCategoria);

            if (lancamentoCategoria == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Despesa não encontrada.");
                return;
            }

            if (!lancamentoCategoria.PermiteExclusao)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Alteração não permitida.");
                return;
            }

            switch (status)
            {
                case AlterarStatusLancamentoCategoriaEnum.Ativar:
                    lancamentoCategoria.Ativar();
                    break;
                case AlterarStatusLancamentoCategoriaEnum.Inativar:
                    lancamentoCategoria.Inativar();
                    break;
            }

            ManterLancamentoCategoria(lancamentoCategoria);
        }

        public List<KeyValuePair<int, string>> ObterKeyValuePairStatusLancamentoCategoria()
        {
            return Enum.GetValues(typeof(StatusLancamentoCategoriaEnum))
                .Cast<StatusLancamentoCategoriaEnum>()
                .Select(e => new KeyValuePair<int, string>((int)e, e.ToString()))
                .ToList();
        }

        public List<KeyValuePair<int, string>> ObterKeyValuePairRelacaoLancamentoCategoria()
        {
            return new List<KeyValuePair<int, string>>
                {
                    new KeyValuePair<int, string>((int)RelacaoLancamentoCategoriaEnum.Nenhum, "Sem relação definida"),
                    new KeyValuePair<int, string>((int)RelacaoLancamentoCategoriaEnum.Fornecedor, "Relacionada a fornecedor"),
                    new KeyValuePair<int, string>((int)RelacaoLancamentoCategoriaEnum.Profissional, "Relacionada a profissional")
                };
        }

        public CarregarLancamentoCategoriaDTO ObterDadosLancamentoCategoria(int idLancamentoCategoria, int idEstabelecimento)
        {
            var lancamentoCategoria = Domain.Despesas.LancamentoCategoriaRepository.ObterPorIdEIdEstabelecimento(idLancamentoCategoria, idEstabelecimento, somenteAtivo: false);
            
            var existeLancamentoRealizadoComOLancamentoCategoria = Domain.Despesas.LancamentoRepository.ExisteLancamentoRealizadoComOLancamentoCategoria(idEstabelecimento, idLancamentoCategoria);

            if (lancamentoCategoria == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Despesa não encontrada.");
                return null;
            }

            return new CarregarLancamentoCategoriaDTO(lancamentoCategoria, existeLancamentoRealizadoComOLancamentoCategoria);
        }

        public string ObterTituloModalManterDespesaCadastrada(int idEstabelecimento, int idLancamentoCategoria)
        {
            if (idLancamentoCategoria == 0)
            {
                return "Cadastrar despesa";
            }

            var lancamentoCategoria = Domain.Despesas.LancamentoCategoriaRepository
                .ObterPorIdEIdEstabelecimento(idLancamentoCategoria, idEstabelecimento, somenteAtivo: false);

            if (lancamentoCategoria == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Despesa não encontrada.");
                return string.Empty;
            }

            if (lancamentoCategoria.Ativo && lancamentoCategoria.PermiteEdicao)
            {
                return "Editar despesa";
            }

            return "Visualizar despesa";
        }

        public LancamentoCategoria AtualizarCamposPermitidosParaLancamentoPadrao(LancamentoCategoria categoria, SalvarLancamentoCategoriaDTO dto)
        {
            categoria.Ativo = dto.Ativo;
            categoria.LancamentoGrupo = ObterGrupo(dto.IdLancamentoGrupo);
            categoria.NivelMinimoAcesso = ObterNivelAcesso(dto.AcessoParaRecepcao);

            return categoria;
        }

        public LancamentoCategoria AtualizarCamposPermitidosParaLancamentoCustomizadoJaUtilizado(LancamentoCategoria categoria, SalvarLancamentoCategoriaDTO dto)
        {
            categoria.Nome = dto.NomeLancamentoCategoria;
            categoria.Ativo = dto.Ativo;
            categoria.LancamentoGrupo = ObterGrupo(dto.IdLancamentoGrupo);
            categoria.NivelMinimoAcesso = ObterNivelAcesso(dto.AcessoParaRecepcao);

            return categoria;
        }

        private LancamentoGrupo ObterGrupo(int idGrupo)
        {
            return idGrupo == 0 ? null : Domain.Despesas.LancamentoGrupoRepository.Load(idGrupo);
        }

        private int ObterNivelAcesso(bool acessoParaRecepcao)
        {
            return acessoParaRecepcao
                ? (int)AcessoBackoffice.Somente_Agenda
                : (int)AcessoBackoffice.Acesso_total;
        }

        public LancamentoCategoria PreencherDadosCategoria(LancamentoCategoria categoria, SalvarLancamentoCategoriaDTO dto, Estabelecimento estabelecimento)
        {
            var idPadrao = Domain.Despesas.LancamentoCategoriaService.ObterIdLancamentoCategoriaPadrao(dto);

            categoria.Nome = dto.NomeLancamentoCategoria;
            categoria.LancamentoGrupo = ObterGrupo(dto.IdLancamentoGrupo);
            categoria.Ativo = dto.Ativo;
            categoria.NecessarioInformarProfissional = dto.InformaProfissional;
            categoria.NecessarioInformarFornecedor = dto.InformaFornecedor;
            categoria.NivelMinimoAcesso = ObterNivelAcesso(dto.AcessoParaRecepcao);
            categoria.LancamentoTipo = Domain.Despesas.LancamentoTipoRepository.Load((int)LancamentoTipoEnum.Despesa);
            categoria.Estabelecimento = estabelecimento;
            categoria.LancamentoCategoriaPadrao = idPadrao == 0
                ? null
                : Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterPorId(idPadrao);
            categoria.EhDespesaPersonalizadaComCategoriaPadrao = idPadrao != 0;

            return categoria;
        }

        public IEnumerable<int> CarregarIdsLancamentoCategoriaDeBonificacaoEVale(int idEstabelecimento)
        {
            if (idEstabelecimento == 0)
                return Enumerable.Empty<int>();

            return Domain.Despesas.LancamentoCategoriaRepository.CarregarIdsLancamentoCategoriaDeBonificacaoEVale(idEstabelecimento);
        }
    }
}