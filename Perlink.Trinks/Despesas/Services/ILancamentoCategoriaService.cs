﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System.Collections.Generic;

namespace Perlink.Trinks.Despesas.Services
{
    public interface ILancamentoCategoriaService : IService
    {
        void ManterLancamentoCategoria(LancamentoCategoria lancamentoCategoria);
        void CriarDespesasParaOEstabelecimento(Estabelecimento estabelecimento);
        ResultadoPaginado<LancamentoCategoria> Filtrar(FiltroBuscaLancamentosCategoria filtro);
        void AlterarStatusCategoriaLancamento(int idCategoria);
        void AlterarStatusCategoriaLancamento(int idLancamentoCategoria, AlterarStatusLancamentoCategoriaEnum status);
        bool PermiteLancarOuVisualizarTipoDeDespesa(LancamentoCategoria lancamentoCategoria, Estabelecimento estabelecimento, AcessoBackoffice acessoAoBackOffice);
        LancamentoCategoria ObterOuCriarLancamentoCategoriaPadrao(Estabelecimento estabelecimento, int idLancamentoCategoriaPadrao);
        List<LancamentoCategoriaParaLancamentoEmLoteDTO> ListarCategoriasParaLancamentoEmLote(int idEstabelecimento);
        int ObterIdLancamentoCategoriaPadrao(SalvarLancamentoCategoriaDTO salvarCategoriaDTO);
        IList<LancamentoCategoriaBaseDTO> FiltrarDespesasCadastradas(FiltroDespesasCadastradasDTO filtro);
        IList<GruposParaCadastrarDespesaDTO> ObterListaLancamentoGrupo(int idEstabelecimento);
        List<KeyValuePair<int, string>> ObterKeyValuePairStatusLancamentoCategoria();
        List<KeyValuePair<int, string>> ObterKeyValuePairRelacaoLancamentoCategoria();
        CarregarLancamentoCategoriaDTO ObterDadosLancamentoCategoria(int idLancamentoCategoria, int idEstabelecimento);
        string ObterTituloModalManterDespesaCadastrada(int idEstabelecimento, int idLancamentoCategoria);
        LancamentoCategoria AtualizarCamposPermitidosParaLancamentoPadrao(LancamentoCategoria categoria, SalvarLancamentoCategoriaDTO dto);
        LancamentoCategoria AtualizarCamposPermitidosParaLancamentoCustomizadoJaUtilizado(LancamentoCategoria categoria, SalvarLancamentoCategoriaDTO dto);
        LancamentoCategoria PreencherDadosCategoria(LancamentoCategoria categoria, SalvarLancamentoCategoriaDTO dto, Estabelecimento estabelecimento);
        IEnumerable<int> CarregarIdsLancamentoCategoriaDeBonificacaoEVale(int idEstabelecimento);
    }
}
