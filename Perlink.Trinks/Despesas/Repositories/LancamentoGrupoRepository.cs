﻿using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Pessoas.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Despesas.Repositories
{

    public partial class LancamentoGrupoRepository : ILancamentoGrupoRepository
    {

        public bool ValidaSeEstabelecimentoJaPossuiLancamentosCategoriaPorTipo(int idEstabelecimento, LancamentoGrupo lancamento)
        {
            var query = Queryable();
            return query.Any(l => l.Estabelecimento.IdEstabelecimento == idEstabelecimento && l.IdLancamentoGrupo == lancamento.IdLancamentoGrupo);
        }

        public IQueryable<LancamentoGrupo> ObterLancamentosGrupoDoEstabelecimento(int idEstabelecimento, bool? status)
        {
            var query = status.HasValue ? Queryable().Where(lgp => lgp.Estabelecimento.IdEstabelecimento == idEstabelecimento && lgp.Ativo == status.Value)
                   : Queryable().Where(lgp => lgp.Estabelecimento.IdEstabelecimento == idEstabelecimento && lgp.Ativo);

            if (!query.Any())
                return query;

            query = AplicarFiltroVersaoDasCategorias(query, idEstabelecimento);

            return query;
        }

        private IQueryable<LancamentoGrupo> AplicarFiltroVersaoDasCategorias(IQueryable<LancamentoGrupo> lancamentoQuery, int idEstabelecimento)
        {
            var disponibilidade =
                Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                    idEstabelecimento, Recurso.UltimaVersaoCategoriasEGruposDeDespesas);

            var ultimaVersaoDisponivel = Domain.Despesas.LancamentoGrupoRepository.Queryable(true)
                .Where(x => x.Estabelecimento.IdEstabelecimento == idEstabelecimento && x.Ativo)
                .Max(x => x.Versao);

            if (disponibilidade && ultimaVersaoDisponivel > 1)
            {
                return lancamentoQuery.Where(x => x.Versao == ultimaVersaoDisponivel);
            }

            return AplicarFiltroToggleDespesasParticulares(lancamentoQuery.Where(x => x.Versao == 1), idEstabelecimento);
        }

        public IQueryable<LancamentoGrupo> AplicarFiltroToggleDespesasParticulares(IQueryable<LancamentoGrupo> lancamentoGrupos, int idEstabelecimento)
        {
            var toggleDespesasPessoais =
                Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                    idEstabelecimento, Recurso.DespesasPessoais).EstaDisponivel;

            var idGrupoDespesasPessoaisNovas =
                Domain.Pessoas.ParametrizacaoTrinksRepository.ObterValorParametro<int>(ParametrosTrinksEnum
                    .id_grupo_despesas_pessoais_novo);

            var idGrupoDespesasPessoaisAntigo =
                Domain.Pessoas.ParametrizacaoTrinksRepository.ObterValorParametro<int>(ParametrosTrinksEnum
                    .id_grupo_despesas_pessoais_antigo);

            var query = lancamentoGrupos;

            if (toggleDespesasPessoais)
            {
                query = lancamentoGrupos.Where(q => q.IdLancamentoGrupoPadrao != idGrupoDespesasPessoaisAntigo);
            }
            else
            {
                query = lancamentoGrupos.Where(q => q.IdLancamentoGrupoPadrao != idGrupoDespesasPessoaisNovas);
            }

            return query;
        }

        public string ObterNomeDoLancamentoGrupo(int idLancamento)
        {
            return Queryable().FirstOrDefault(l => l.IdLancamentoGrupo == idLancamento).NomeGrupoCategoria;
        }

        public List<KeyValuePair<int, string>> ListarKeyValuePairAtivosDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable()
                    .Where(lgp => lgp.Estabelecimento.IdEstabelecimento == idEstabelecimento && lgp.Ativo)
                    .Select(p => new KeyValuePair<int, string>(p.IdLancamentoGrupo, p.NomeGrupoCategoria))
                    .ToList();
        }

        public List<KeyValuePair<int, string>> ListarKeyValuePairAtivosVersaoAtualDoEstabelecimento(int idEstabelecimento, bool? status)
        {
            return ObterLancamentosGrupoDoEstabelecimento(idEstabelecimento, status)
                    .Select(p => new KeyValuePair<int, string>(p.IdLancamentoGrupo, p.NomeGrupoCategoria))
                    .ToList();
        }
    }
}