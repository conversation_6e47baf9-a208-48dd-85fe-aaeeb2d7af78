﻿using NHibernate.Linq;
using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Despesas.Repositories
{

    public partial class LancamentoRepository : ILancamentoRepository
    {

        /// <summary>
        ///     Método para listar todos as despesas do estabelecimento que respeitam as propriedades configuradas na classe
        ///     FiltroBuscaLancamentos
        ///     agrupados por mês e ano
        /// </summary>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        public List<RelatorioLancamentos> FiltrarAgrupadoPorMesAno(FiltroBuscaLancamentos filtro)
        {
            IQueryable<Lancamento> query = ObterQueryBusca(filtro);

            IEnumerable<RelatorioLancamentos> listaRelatorio = AdicionarGroupByMesAnoPorDataAgrupamento(query, filtro);
            return listaRelatorio.OrderBy(p => p.DataAgrupamento).ToList();
        }

        /// <summary>
        ///     Método para listar todos as despesas do estabelecimento que respeitam as propriedades configuradas na classe
        ///     FiltroBuscaLancamentos
        ///     agrupados por data
        /// </summary>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        public List<RelatorioLancamentos> FiltrarAgrupadoPorData(FiltroBuscaLancamentos filtro)
        {
            IQueryable<Lancamento> query = ObterQueryBusca(filtro);
            IEnumerable<RelatorioLancamentos> listaRelatorio = AdicionarGroupByDataPorDataAgrupamento(query, filtro);
            return listaRelatorio.OrderBy(p => p.DataAgrupamento).ToList();
        }

        /// <summary>
        ///     Método para listar todos as despesas do estabelecimento que respeitam as propriedades configuradas na classe
        ///     FiltroBuscaLancamentos
        ///     agrupados por data
        /// </summary>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        public List<Lancamento> Filtrar(FiltroBuscaLancamentos filtro)
        {
            IQueryable<Lancamento> query = ObterQueryBusca(filtro);

            return query
                .OrderBy(p => p.DataVencimento)
                .ThenBy(p => p.DataPagamento)
                .ThenBy(p => p.DataCriacao)
                .ThenBy(p => p.DataUltimaAtualizacao)
                .Fetch(f => f.FormaPagamento)
                .Fetch(f => f.LancamentoCategoria)
                .Fetch(p => p.Status)
                .Fetch(p => p.LancamentoRecorrencia)
                .Fetch(p => p.PessoaQueRecebeuOuPagou)
                .ToList();
        }

        public bool EhDespesaRecorrente(int id, int idEstabelecimentoAutenticado)
        {
            return Queryable()
                .Where(f => f.IdLancamento == id && f.Estabelecimento.IdEstabelecimento == idEstabelecimentoAutenticado)
                .Select(f => f.LancamentoRecorrencia)
                .FirstOrDefault() != null;
        }




        public void SaveOrUpdate(Lancamento lancamento)
        {
            if (lancamento.IdLancamento > 0)
            {
                Update(lancamento);
            }
            else
            {
                SaveNew(lancamento);
            }
        }

        public decimal ValorEmVale(ParametrosFiltrosRelatorio filtro)
        {
            return ValorPorCategoria(filtro, (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.ValeProfissional));
        }

        public decimal ValorEmValeTransporte(ParametrosFiltrosRelatorio filtro)
        {
            return ValorPorCategoria(filtro, (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PassagemValeTransporte));
        }

        public decimal ValorEmAlimentacao(ParametrosFiltrosRelatorio filtro)
        {
            return ValorPorCategoria(filtro, (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Alimentacao));
        }

        public decimal ValorEmOutrosDescontos(ParametrosFiltrosRelatorio filtro)
        {
            DateTime? dataFim = filtro.DataFinal;

            if (filtro.DataFinal.HasValue)
            {
                dataFim = new DateTime(filtro.DataFinal.Value.Year, filtro.DataFinal.Value.Month, filtro.DataFinal.Value.Day,
                    23, 59, 59);
            }

            var lancamentos = Queryable()
                .Where(f => f.Ativo &&
                    f.DataPagamento >= filtro.DataInicial && f.DataPagamento <= dataFim &&
                    f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                    f.Status.IdLancamentoStatusPagamento == 1);

            if (filtro.EstabelecimentoProfissional != null)
                lancamentos = lancamentos.Where(f => f.PessoaQueRecebeuOuPagou.IdPessoa ==
                            filtro.EstabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa);

            var lancamentoCategoriaPadraoRepository = Domain.Despesas.LancamentoCategoriaPadraoRepository;
            var categorias = new List<CategoriaDeLancamentoPadraoEnum>() {
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.ValeProfissional),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoProfissional),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PassagemValeTransporte),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Alimentacao),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Bonificacao),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Comissao),
                lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Gorjeta)
            };


            lancamentos =
                lancamentos.Where(
                    f =>
                        f.LancamentoCategoria.LancamentoCategoriaPadrao == null ||
                        !(categorias.Select(categoria => (int)categoria).Contains(
                            f.LancamentoCategoria.LancamentoCategoriaPadrao.IdLancamentoCategoria)));

            return lancamentos.Sum(f => (decimal?)f.Valor) ?? 0;
        }

        public IQueryable<Lancamento> ListarVales(ParametrosFiltrosRelatorio filtro)
        {
            var lancamentos = ValoresPorCategoria(filtro, (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.ValeProfissional));
            lancamentos = AdicionarCriterioBuscaPorDataDePagamento(lancamentos, filtro.DataInicial, filtro.DataFinal);

            if (filtro.IdRelacaoProfissional > 0)
                lancamentos = FiltrarPorRelacaoProfissional(lancamentos, filtro.IdEstabelecimento, filtro.IdRelacaoProfissional);

            return lancamentos;
        }

        public IQueryable<Lancamento> ListarLancamentosExibidosNoRelatorioDeComissao(ParametrosFiltrosRelatorio filtro)
        {
            //var lancamentos = ValoresPorCategoria(filtro, 1);
            var lancamentoCategoriaPadraoRepository = Domain.Despesas.LancamentoCategoriaPadraoRepository;
            var idsCategoriasPadrao = new List<int> {
                (int)lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.ValeProfissional),
                (int)lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit),
                (int)lancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Bonificacao),
            };

            var lancamentos = Queryable()
                .Where(f => f.Ativo &&
                            idsCategoriasPadrao.Contains(f.LancamentoCategoria.LancamentoCategoriaPadrao.IdLancamentoCategoria) &&
                            f.LancamentoCategoria.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            f.Status.IdLancamentoStatusPagamento == 1);

            lancamentos = AdicionarCriterioBuscaPorDataDePagamento(lancamentos, filtro.DataInicial, filtro.DataFinal);

            if (filtro.IdRelacaoProfissional > 0)
                lancamentos = FiltrarPorRelacaoProfissional(lancamentos, filtro.IdEstabelecimento, filtro.IdRelacaoProfissional);

            return lancamentos;
        }

        private IQueryable<Lancamento> FiltrarPorRelacaoProfissional(IQueryable<Lancamento> lancamentos, int idEstabelecimento, int idRelacaoProfissional)
        {
            var buscaDeProfissionaisComRelacao = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(ep => ep.FormaRelacaoProfissional.Id == idRelacaoProfissional && ep.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            lancamentos = lancamentos.Where(v => buscaDeProfissionaisComRelacao.Any(p => v.PessoaQueRecebeuOuPagou.IdPessoa == p.Profissional.PessoaFisica.IdPessoa));
            return lancamentos;
        }

        public IQueryable<Lancamento> ListarSplitsDePagamento(ParametrosFiltrosRelatorio filtro)
        {
            var lancamentos = ValoresPorCategoria(filtro, (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit));
            DateTime? dataFim = filtro.DataFinal;

            if (filtro.DataFinal.HasValue)
            {
                dataFim = new DateTime(filtro.DataFinal.Value.Year, filtro.DataFinal.Value.Month, filtro.DataFinal.Value.Day,
                    23, 59, 59);
            }

            if (filtro.TipoData == TipoDataRelatorio.DataDaComissao)
                lancamentos = lancamentos.Where(f => f.ValorDeComissaoAReceber != null && f.ValorDeComissaoAReceber.DataDaComissaoAReceber >= filtro.DataInicial && f.ValorDeComissaoAReceber.DataDaComissaoAReceber <= dataFim);
            else if (filtro != null && filtro.TipoData == TipoDataRelatorio.DataAtendimento)
                lancamentos = lancamentos.Where(f => f.Transacao.DataReferencia >= filtro.DataInicial && f.Transacao.DataReferencia <= dataFim && f.Transacao.PessoaQueRecebeu == filtro.Estabelecimento.PessoaJuridica);
            else
                lancamentos = lancamentos.Where(f => f.Transacao.DataHora >= filtro.DataInicial && f.Transacao.DataHora <= dataFim && f.Transacao.PessoaQueRecebeu == filtro.Estabelecimento.PessoaJuridica);

            if (filtro.IdRelacaoProfissional > 0)
            {
                var buscaDeProfissionaisComRelacao = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(ep => ep.FormaRelacaoProfissional.Id == filtro.IdRelacaoProfissional && ep.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento);
                lancamentos = lancamentos.Where(v => buscaDeProfissionaisComRelacao.Any(p => v.PessoaQueRecebeuOuPagou.IdPessoa == p.Profissional.PessoaFisica.IdPessoa));
            }

            return lancamentos;
        }

        public IQueryable<Lancamento> ListarBonificacoes(ParametrosFiltrosRelatorio filtro)
        {
            var lancamentos = ValoresPorCategoria(filtro, (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.Bonificacao));
            lancamentos = AdicionarCriterioBuscaPorDataDePagamento(lancamentos, filtro.DataInicial, filtro.DataFinal);

            if (filtro.IdRelacaoProfissional > 0)
                lancamentos = FiltrarPorRelacaoProfissional(lancamentos, filtro.IdEstabelecimento, filtro.IdRelacaoProfissional);

            return lancamentos;
        }

        public decimal ObterTotalDeBonificacoesNoPeriodo(int idEstabelecimento, DateTime dataInicial, DateTime dataFinal, int idPessoaProfissional)
        {
            var lancamentos = Queryable();

            lancamentos = AdicionarCriterioBuscaPorEstabelecimento(lancamentos, idEstabelecimento);
            lancamentos = AdicionarCriterioBuscaPorDataDePagamento(lancamentos, dataInicial, dataFinal);
            lancamentos = AdicionarCriterioBuscaPorStatusDePagamento(lancamentos, (int)LancamentoStatusPagamentoEnum.Pago);
            lancamentos = AdicionarCriterioBuscaPorPessoaQueRecebeuOuPagou(lancamentos, idPessoaProfissional);
            lancamentos = AdicionarCriterioBuscaPorCategoriaPadrao(lancamentos, (int)CategoriaDeLancamentoPadraoEnum.Bonificacao);
            lancamentos = lancamentos.Where(p => p.Ativo);

            var total = lancamentos.Sum(p => (decimal?)p.Valor);

            return total ?? 0;
        }

        public IList<DetalhesDespesasDTO> ListarDespesasPagasPeloCaixa(int idEstabelecimento, DateTime? dataInicial, DateTime? dataFinal)
        {
            var query = Queryable()
                    .Where(p => p.LancamentoCategoria.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                p.LancamentoCategoria.LancamentoTipo.IdLancamentoTipo == (int)LancamentoTipoEnum.Despesa &&
                                p.DataPagamento.HasValue && p.FormaPagamento != null &&
                                p.FormaPagamento.Id == 16 &&
                                p.Ativo);

            if (dataInicial.HasValue)
            {
                query = query.Where(p => p.DataPagamento >= dataInicial.Value.Date);
            }

            if (dataFinal.HasValue)
            {
                query = query.Where(p => p.DataPagamento < dataFinal.Value.Date.AddDays(1));
            }

            var lista = query.GroupBy(p => new { NomeCategoria = p.LancamentoCategoria.Nome })
                             .Select(p => new DetalhesDespesasDTO
                             {
                                 Descricao = p.Key.NomeCategoria,
                                 Valor = p.Sum(l => l.Valor)
                             }).ToList();

            return lista;
        }

        private IQueryable<Lancamento> ObterQueryBusca(FiltroBuscaLancamentos filtro)
        {
            IQueryable<Lancamento> query = Queryable();
            query = AdicionarCriterioBuscaPorEstabelecimento(query, filtro.IdEstabelecimento);
            query = AdicionarCriterioBuscaPorNivelMinimoAcesso(query, filtro);

            if (filtro.StatusPagamento.HasValue && filtro.StatusPagamento > 0)
                query = AdicionarCriterioBuscaPorStatusDePagamento(query, (int)filtro.StatusPagamento.Value);

            query = AdicionarCriterioBuscaPorLancamentoCategoria(query, filtro);
            query = AdicionarCriterioBuscaPorLancamentoGrupo(query, filtro);
            query = AdicionarCriterioBuscaPorAtivos(query, filtro);
            query = AdicionarCriterioBuscaPorPeriodoDataAgrupamento(query, filtro);
            query = AdicionarCriterioBuscaPorProfissionalOuFornecedor(query, filtro);
            query = AdicionarCriterioBuscaPorFormasDePagamento(query, filtro);
            query = AdicionarCriterioBuscaPorContaFinanceira(query, filtro.IdContaFinanceira);
            query = AdicionarCriterioBuscaPorCategoria(query, filtro);

            return query;
        }

        private decimal ValorPorCategoria(ParametrosFiltrosRelatorio filtro, int idLancamentoCategoriaPadrao)
        {
            var lancamentos = ValoresPorCategoria(filtro, idLancamentoCategoriaPadrao);
            DateTime? dataFim = filtro.DataFinal;

            if (filtro.DataFinal.HasValue)
            {
                dataFim = new DateTime(filtro.DataFinal.Value.Year, filtro.DataFinal.Value.Month, filtro.DataFinal.Value.Day,
                    23, 59, 59);
            }

            lancamentos = lancamentos.Where(f => f.DataPagamento.HasValue &&
                                                 f.DataPagamento >= filtro.DataInicial &&
                                                 f.DataPagamento <= dataFim);

            return lancamentos.Sum(f => (decimal?)f.Valor) ?? 0;
        }

        private IQueryable<Lancamento> ValoresPorCategoria(ParametrosFiltrosRelatorio filtro, int idLancamentoCategoriaPadrao, bool stateless = false)
        {
            idLancamentoCategoriaPadrao = (int)Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA((CategoriaDeLancamentoPadraoEnum)idLancamentoCategoriaPadrao);

            var retorno = stateless ? StatelessQueryable() : Queryable()
                .Where(f => f.Ativo &&
                            f.LancamentoCategoria.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)idLancamentoCategoriaPadrao &&
                            f.LancamentoCategoria.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            f.Status.IdLancamentoStatusPagamento == 1);

            if (filtro.EstabelecimentoProfissional != null)
            {
                var profissional = Domain.Pessoas.ProfissionalRepository.Load(filtro.EstabelecimentoProfissional.Profissional.IdProfissional);
                retorno = retorno.Where(f => f.PessoaQueRecebeuOuPagou.IdPessoa ==
                            profissional.PessoaFisica.IdPessoa);
            }

            if (filtro.IdsProfissional.Any())
            {
                var idsPessoas = Domain.Pessoas.ProfissionalRepository.Queryable().Where(p => filtro.IdsProfissional.Contains(p.IdProfissional)).Select(p => p.PessoaFisica.IdPessoa).ToList();
                retorno = retorno.Where(f => idsPessoas.Contains(f.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return retorno;
        }

        private IQueryable<Lancamento> ValoresPorNome(ParametrosFiltrosRelatorio filtro, string nomeCategoria, bool stateless = false)
        {
            var retorno = stateless ? StatelessQueryable() : Queryable()
                .Where(f => f.Ativo &&
                            f.DataPagamento.HasValue &&
                            f.DataPagamento >= filtro.DataInicial &&
                            f.DataPagamento <= filtro.DataFinal &&
                            f.LancamentoCategoria.Nome == nomeCategoria &&
                            f.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento &&
                            f.Status.IdLancamentoStatusPagamento == 1);

            if (filtro.EstabelecimentoProfissional != null)
                retorno = retorno.Where(f => f.PessoaQueRecebeuOuPagou.IdPessoa ==
                            filtro.EstabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa);

            if (filtro.IdRelacaoProfissional > 0)
            {
                var buscaDeProfissionaisComRelacao = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(ep => ep.FormaRelacaoProfissional.Id == filtro.IdRelacaoProfissional);
                retorno = retorno.Where(v => buscaDeProfissionaisComRelacao.Any(p => v.PessoaQueRecebeuOuPagou.IdPessoa == p.Profissional.PessoaFisica.IdPessoa && p.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento));
            }

            return retorno;
        }

        public bool TransacaoPossuiRegistroDeSplitAtivo(int idTransacao)
        {
            return Queryable().Any(p => p.Transacao.Id == idTransacao && p.Ativo);
        }

        public decimal ObterTotalSplitNoPeriodoPorProfissional(int idPessoaProfissional, int idEstabelecimento, DateTime dataInicial, DateTime dataFinal)
        {
            var query = Queryable().Where(f => f.LancamentoCategoria.LancamentoCategoriaPadrao == Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.PagamentoSplit));

            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            query = query.Where(f => f.DataPagamento >= dataInicial);
            query = query.Where(f => f.DataPagamento < dataFinal);
            query = query.Where(f => f.Ativo);
            query = query.Where(f => f.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaProfissional);
            query = query.Where(f => f.Transacao != null);
            return query.Sum(f => (decimal?)f.Valor) ?? 0;
        }

        public LancamentoOriginalDTO ObterLancamentoOriginalPorId(int idLancamento)
        {
            return Queryable().Where(l => l.IdLancamento == idLancamento).Select(l => new LancamentoOriginalDTO()
            {
                Valor = l.Valor,
                DataPagamento = l.DataPagamento.Value,
                IdLancamento = l.IdLancamento
            }).FirstOrDefault();
        }

        public Lancamento ObterPorId(int id, int idEstabelecimento)
        {
            return Queryable().Where(l => l.IdLancamento == id && l.Estabelecimento.IdEstabelecimento == idEstabelecimento).FirstOrDefault();
        }

        public int ObterTotalDeDespesasNoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Count(lanc => lanc.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public DateTime? ObterUltimaDataDeLancamentoDeVale(int idEstabelecimento)
        {
            return Queryable().OrderByDescending(l => l.DataCriacao)
                .Where(l => l.Estabelecimento.IdEstabelecimento == idEstabelecimento
                && l.LancamentoCategoria.LancamentoCategoriaPadrao == Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(CategoriaDeLancamentoPadraoEnum.ValeProfissional))
                .Select(l => l.DataCriacao)
                .FirstOrDefault();
        }

        public IQueryable<Lancamento> ObterQueryBonificacoesAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var folhaItem = Domain.Financeiro.FolhaPagamentoItemBonificacaoRepository.Queryable()
                .Where(f => f.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return Queryable()
                .Where(b => b.DataPagamento >= dataInicio.Date && b.DataPagamento < dataFim.AddDays(1).Date &&
                b.LancamentoCategoria.LancamentoCategoriaPadrao == CategoriaDeLancamentoPadraoEnum.Bonificacao &&
                b.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                b.Status.IdLancamentoStatusPagamento == (int)LancamentoStatusPagamentoEnum.Pago &&
                b.Ativo &&
                !folhaItem.Any(f => f.Lancamento.IdLancamento == b.IdLancamento && f.Ativo));
        }


        public IQueryable<Lancamento> ObterQueryBonificacoesAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu)
        {
            var query = ObterQueryBonificacoesAbertas(idEstabelecimento, dataInicio, dataFim);
            query = query.Where(b => b.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeu);

            return query;
        }

        public IQueryable<Lancamento> ObterQueryBonificacoesAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional)
        {
            var bonificacoesAbertas = ObterQueryBonificacoesAbertas(idEstabelecimento, dataInicio, dataFim);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                bonificacoesAbertas = bonificacoesAbertas.Where(b => listaIdPessoaProfissional.Contains(b.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return bonificacoesAbertas;
        }

        public IQueryable<Lancamento> ObterQueryBonificacoesPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var folhaItem = Domain.Financeiro.FolhaPagamentoItemBonificacaoRepository.Queryable()
                .Where(f => f.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return Queryable()
                .Where(b => b.DataPagamento >= dataInicio.Date && b.DataPagamento < dataFim.AddDays(1).Date &&
                b.LancamentoCategoria.LancamentoCategoriaPadrao == CategoriaDeLancamentoPadraoEnum.Bonificacao &&
                b.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                b.Status.IdLancamentoStatusPagamento == (int)LancamentoStatusPagamentoEnum.Pago &&
                b.Ativo &&
                folhaItem.Any(f => f.Lancamento.IdLancamento == b.IdLancamento && f.Ativo));
        }

        public IQueryable<Lancamento> ObterQueryBonificacoesPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu)
        {
            var query = ObterQueryBonificacoesPagas(idEstabelecimento, dataInicio, dataFim);
            query = query.Where(b => b.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeu);

            return query;
        }

        public IQueryable<Lancamento> ObterQueryBonificacoesPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional)
        {
            var bonificacoesPagas = ObterQueryBonificacoesPagas(idEstabelecimento, dataInicio, dataFim);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                bonificacoesPagas = bonificacoesPagas.Where(b => listaIdPessoaProfissional.Contains(b.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return bonificacoesPagas;
        }

        public IQueryable<Lancamento> ObterQueryValesAbertos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var folhaItem = Domain.Financeiro.FolhaPagamentoItemValeRepository.Queryable()
                .Where(f => f.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return Queryable()
                .Where(b => b.DataPagamento >= dataInicio.Date && b.DataPagamento < dataFim.AddDays(1).Date &&
                b.LancamentoCategoria.LancamentoCategoriaPadrao == CategoriaDeLancamentoPadraoEnum.ValeProfissional &&
                b.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                b.Status.IdLancamentoStatusPagamento == (int)LancamentoStatusPagamentoEnum.Pago &&
                b.Ativo &&
                !folhaItem.Any(f => f.Lancamento.IdLancamento == b.IdLancamento && f.Ativo));
        }

        public IQueryable<Lancamento> ObterQueryValesAbertos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu)
        {
            var query = ObterQueryValesAbertos(idEstabelecimento, dataInicio, dataFim);
            query = query.Where(v => v.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeu);

            return query;
        }

        public IQueryable<Lancamento> ObterQueryValesAbertos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional)
        {
            var valesAbertos = ObterQueryValesAbertos(idEstabelecimento, dataInicio, dataFim);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                valesAbertos = valesAbertos.Where(v => listaIdPessoaProfissional.Contains(v.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return valesAbertos;
        }

        public IQueryable<Lancamento> ObterQueryValesPagos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var folhaItem = Domain.Financeiro.FolhaPagamentoItemValeRepository.Queryable()
                .Where(f => f.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return Queryable()
                .Where(b => b.DataPagamento >= dataInicio.Date && b.DataPagamento < dataFim.AddDays(1).Date &&
                b.LancamentoCategoria.LancamentoCategoriaPadrao == CategoriaDeLancamentoPadraoEnum.ValeProfissional &&
                b.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                b.Status.IdLancamentoStatusPagamento == (int)LancamentoStatusPagamentoEnum.Pago &&
                b.Ativo &&
                folhaItem.Any(f => f.Lancamento.IdLancamento == b.IdLancamento && f.Ativo));
        }

        public IQueryable<Lancamento> ObterQueryValesPagos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu)
        {
            var query = ObterQueryValesPagos(idEstabelecimento, dataInicio, dataFim);
            query = query.Where(v => v.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeu);

            return query;
        }

        public IQueryable<Lancamento> ObterQueryValesPagos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional)
        {
            var valesPagos = ObterQueryValesPagos(idEstabelecimento, dataInicio, dataFim);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                valesPagos = valesPagos.Where(v => listaIdPessoaProfissional.Contains(v.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return valesPagos;
        }

        public IQueryable<Lancamento> ObterQuerySplitsAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var folhaItem = Domain.Financeiro.FolhaPagamentoItemSplitRepository.Queryable()
                .Where(f => f.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return Queryable()
                .Where(b => b.DataVencimento >= dataInicio.Date && b.DataVencimento < dataFim.AddDays(1).Date &&
                b.LancamentoCategoria.LancamentoCategoriaPadrao == CategoriaDeLancamentoPadraoEnum.PagamentoSplit &&
                b.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                b.Ativo &&
                !folhaItem.Any(f => f.Lancamento.IdLancamento == b.IdLancamento && f.Ativo));
        }

        public IQueryable<Lancamento> ObterQuerySplitsAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu)
        {
            var query = ObterQuerySplitsAbertas(idEstabelecimento, dataInicio, dataFim);
            query = query.Where(v => v.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeu);

            return query;
        }

        public IQueryable<Lancamento> ObterQuerySplitsAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional)
        {
            var splitsAbertos = ObterQuerySplitsAbertas(idEstabelecimento, dataInicio, dataFim);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                splitsAbertos = splitsAbertos.Where(s => listaIdPessoaProfissional.Contains(s.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return splitsAbertos;
        }

        public IQueryable<Lancamento> ObterQuerySplitsPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var folhaItem = Domain.Financeiro.FolhaPagamentoItemSplitRepository.Queryable()
                .Where(f => f.FechamentoFolhaMesProfissional.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return Queryable()
                .Where(b => b.DataVencimento >= dataInicio.Date && b.DataVencimento < dataFim.AddDays(1).Date &&
                b.LancamentoCategoria.LancamentoCategoriaPadrao == CategoriaDeLancamentoPadraoEnum.PagamentoSplit &&
                b.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                b.Ativo &&
                folhaItem.Any(f => f.Lancamento.IdLancamento == b.IdLancamento && f.Ativo));
        }

        public IQueryable<Lancamento> ObterQuerySplitsPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu)
        {
            var query = ObterQuerySplitsPagas(idEstabelecimento, dataInicio, dataFim);
            query = query.Where(v => v.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeu);

            return query;
        }

        public IQueryable<Lancamento> ObterQuerySplitsPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional)
        {
            var splitsPagos = ObterQuerySplitsPagas(idEstabelecimento, dataInicio, dataFim);

            if (listaIdPessoaProfissional != null && listaIdPessoaProfissional.Any())
            {
                splitsPagos = splitsPagos.Where(s => listaIdPessoaProfissional.Contains(s.PessoaQueRecebeuOuPagou.IdPessoa));
            }

            return splitsPagos;
        }

        public bool ExisteLancamentoRealizadoComOLancamentoCategoria(int idEstabelecimento, int idLancamentoCategoria)
        {
            var query = Queryable();
            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento);

            return query.Any(l => l.LancamentoCategoria.IdLancamentoCategoria == idLancamentoCategoria);
        }

        #region Critérios

        /// <summary>
        ///     Adiciona o critério de busca por um estabelecimento específico
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorEstabelecimento(IQueryable<Lancamento> query, int idEstabelecimento)
        {
            return query.Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        /// <summary>
        ///     Adiciona o critério para buscar por Lancamentos com propriedade NivelMinimoAcesso igual ao especificado no filtro
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorNivelMinimoAcesso(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            return query.Where(p => p.LancamentoCategoria.NivelMinimoAcesso <= (int)filtro.AcessoBackoffice);
        }

        /// <summary>
        ///     Adiciona o critério para buscar por Lancamento somentes ativos ou inativos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorAtivos(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            return query.Where(p => p.Ativo == filtro.ApenasAtivos);
        }

        /// <summary>
        ///     Adiciona o critério para buscar por Lancamento com Status Pago ou A pagar
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorStatusDePagamento(IQueryable<Lancamento> query, int idStatus)
        {
            return query.Where(p => p.Status.IdLancamentoStatusPagamento == idStatus);
        }

        /// <summary>
        ///     Adiciona o critério para buscar por categoria do lancamento
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorCategoria(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {

            if (filtro.IdsCategorias == null || !filtro.IdsCategorias.Any())
            {
                return query;
            }

            return query.Where(l =>
                filtro.IdsCategorias.Contains(l.LancamentoCategoria.LancamentoGrupo.IdLancamentoGrupo));
        }

        /// <summary>
        ///     Adiciona o critério para buscar por DespesaContaAPaga com DespesaTipo.IdDespesaTipo igual a idDespesaTipo
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorLancamentoCategoria(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            if (!filtro.IdLancamentoCategoria.HasValue || filtro.IdLancamentoCategoria <= 0)
                return query;
            return query.Where(p => p.LancamentoCategoria.IdLancamentoCategoria == filtro.IdLancamentoCategoria);
        }

        /// <summary>
        ///     Adiciona o critério para buscar por LancamentoGrupo
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorLancamentoGrupo(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            if (!filtro.IdLancamentoGrupo.HasValue || filtro.IdLancamentoGrupo <= 0)
                return query;
            return query.Where(p => p.LancamentoCategoria.LancamentoGrupo.IdLancamentoGrupo == filtro.IdLancamentoGrupo);
        }

        /// <summary>
        ///     Adiciona o critério para buscar por Lancamento com Pessoa.IdPessoa igual a idPessoaProfissional ou
        ///     idPessoaFornecedor
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorProfissionalOuFornecedor(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            bool fornecedorPossuiValorValido = filtro.IdPessoaFornecedor.HasValue && filtro.IdPessoaFornecedor.Value > 0;
            bool profissionalPossuiValorValido = filtro.IdPessoaProfissional.HasValue &&
                                                 filtro.IdPessoaProfissional.Value > 0;

            if (fornecedorPossuiValorValido && profissionalPossuiValorValido)
                return
                    query.Where(
                        p =>
                            p.PessoaQueRecebeuOuPagou.IdPessoa == filtro.IdPessoaFornecedor.Value ||
                            p.PessoaQueRecebeuOuPagou.IdPessoa == filtro.IdPessoaProfissional.Value);

            if (fornecedorPossuiValorValido)
                return query.Where(p => p.PessoaQueRecebeuOuPagou.IdPessoa == filtro.IdPessoaFornecedor.Value);

            if (profissionalPossuiValorValido)
                return query.Where(p => p.PessoaQueRecebeuOuPagou.IdPessoa == filtro.IdPessoaProfissional.Value);

            return query;
        }

        /// <summary>
        ///     Adiciona o critério para buscar por Lancamento dentro do periodo de datas
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorPeriodoDataAgrupamento(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            var finalPeriodo = new DateTime(filtro.FinalPeriodo.Year, filtro.FinalPeriodo.Month, filtro.FinalPeriodo.Day,
                23, 59, 59);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataVencimento)
                return query.Where(p => p.DataVencimento >= filtro.InicioPeriodo && p.DataVencimento <= finalPeriodo);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataPagamento)
                return query.Where(p => p.DataPagamento >= filtro.InicioPeriodo && p.DataPagamento <= finalPeriodo);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataCriacao)
                return query.Where(p => p.DataCriacao >= filtro.InicioPeriodo && p.DataCriacao <= finalPeriodo);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.UltimaAtualizacao)
                return
                    query.Where(
                        p => p.DataUltimaAtualizacao >= filtro.InicioPeriodo && p.DataUltimaAtualizacao <= finalPeriodo);

            return query;
        }

        /// <summary>
        ///     Adiciona o critério para buscar por forma de pagamento
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IQueryable<Lancamento> AdicionarCriterioBuscaPorFormasDePagamento(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            bool formaDePagamentoPossuiValorValido = filtro.IdFormaPagamento.HasValue && filtro.IdFormaPagamento.Value > 0;

            if (formaDePagamentoPossuiValorValido)
                return query.Where(p => p.FormaPagamento.Id == filtro.IdFormaPagamento.Value);

            return query;
        }

        private IQueryable<Lancamento> AdicionarCriterioBuscaPorContaFinanceira(IQueryable<Lancamento> query, int? idContaFinanceira)
        {
            if (idContaFinanceira.HasValue && idContaFinanceira > 0)
                query = query.Where(lanc => lanc.ContaFinanceira.Id == idContaFinanceira.Value);

            return query;
        }

        private IQueryable<Lancamento> AdicionarCriterioBuscaPorDataDePagamento(IQueryable<Lancamento> lancamentos, DateTime? dataInicial, DateTime? dataFinal)
        {
            lancamentos = lancamentos
                .Where(f => f.DataPagamento.HasValue &&
                            f.DataPagamento >= dataInicial &&
                            f.DataPagamento <= dataFinal);
            return lancamentos;
        }

        private IQueryable<Lancamento> AdicionarCriterioBuscaPorPessoaQueRecebeuOuPagou(IQueryable<Lancamento> lancamentos, int idPessoaQueRecebeuOuPagou)
        {
            lancamentos = lancamentos.Where(p => p.PessoaQueRecebeuOuPagou.IdPessoa == idPessoaQueRecebeuOuPagou);
            return lancamentos;
        }

        private IQueryable<Lancamento> AdicionarCriterioBuscaPorCategoriaPadrao(IQueryable<Lancamento> lancamentos, int idCategoriaPadrao)
        {
            var categoriaPadrao = Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA((CategoriaDeLancamentoPadraoEnum)idCategoriaPadrao);
            lancamentos = lancamentos.Where(p => p.LancamentoCategoria.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)categoriaPadrao);
            return lancamentos;
        }

        #region Critérios de Agrupamento

        #region Agrupamentos Mês/Ano

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByMesAnoPorDataAgrupamento(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataVencimento)
                return AdicionarGroupByMesAnoPorDataVencimento(query);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataPagamento)
                return AdicionarGroupByMesAnoPorDataPagamento(query);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataCriacao)
                return AdicionarGroupByMesAnoPorDataCriacao(query);

            return AdicionarGroupByMesAnoPorDataUltimaAtualizacao(query);
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByMesAnoPorDataVencimento(IQueryable<Lancamento> query)
        {
            IQueryable<RelatorioLancamentos> queryComAgrupamentoMesAno =
                (from t in query
                 group t by new { t.DataVencimento.Month, t.DataVencimento.Year, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento.Month, t.DataAgrupamento.Year }
                        into g
                    select new RelatorioLancamentos
                    {
                        TipoBuscaPeriodoContaAPagar = TipoBuscaPeriodoLancamento.DataVencimento,
                        DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByMesAnoPorDataPagamento(IQueryable<Lancamento> query)
        {
            IQueryable<RelatorioLancamentos> queryComAgrupamentoMesAno =
                (from t in query
                 group t by
                     new { t.DataPagamento.Value.Month, t.DataPagamento.Value.Year, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento.Month, t.DataAgrupamento.Year }
                        into g
                    select new RelatorioLancamentos
                    {
                        TipoBuscaPeriodoContaAPagar = TipoBuscaPeriodoLancamento.DataPagamento,
                        DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByMesAnoPorDataCriacao(IQueryable<Lancamento> query)
        {
            IQueryable<RelatorioLancamentos> queryComAgrupamentoMesAno =
                (from t in query
                 group t by new { t.DataCriacao.Month, t.DataCriacao.Year, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento.Month, t.DataAgrupamento.Year }
                        into g
                    select new RelatorioLancamentos
                    {
                        TipoBuscaPeriodoContaAPagar = TipoBuscaPeriodoLancamento.DataCriacao,
                        DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByMesAnoPorDataUltimaAtualizacao(
            IQueryable<Lancamento> query)
        {
            IQueryable<RelatorioLancamentos> queryComAgrupamentoMesAno =
                (from t in query
                 group t by
                     new
                     {
                         t.DataUltimaAtualizacao.Month,
                         t.DataUltimaAtualizacao.Year,
                         IdContaAPagar = t.IdLancamento
                     }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento.Month, t.DataAgrupamento.Year }
                        into g
                    select new RelatorioLancamentos
                    {
                        TipoBuscaPeriodoContaAPagar = TipoBuscaPeriodoLancamento.UltimaAtualizacao,
                        DataAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        #endregion Agrupamentos Mês/Ano

        #region Agrupamentos por data

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByDataPorDataAgrupamento(IQueryable<Lancamento> query,
            FiltroBuscaLancamentos filtro)
        {
            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataVencimento)
                return AdicionarGroupByDataPorDataVencimento(query);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataPagamento)
                return AdicionarGroupByDataPorDataPagamento(query);

            if (filtro.TipoBuscaPeriodoContaAPagar == TipoBuscaPeriodoLancamento.DataCriacao)
                return AdicionarGroupByDataPorDataCriacao(query);

            return AdicionarGroupByDataPorDataUltimaAtualizacao(query);
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByDataPorDataVencimento(IQueryable<Lancamento> query)
        {
            var queryComAgrupamentoMesAno =
                (from t in query
                 group t by new { t.DataVencimento, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = g.Key.DataVencimento.Date,
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento }
                        into g
                    select new RelatorioLancamentos
                    {
                        DataAgrupamento = g.Key.DataAgrupamento,
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByDataPorDataPagamento(IQueryable<Lancamento> query)
        {
            IQueryable<RelatorioLancamentos> queryComAgrupamentoMesAno =
                (from t in query
                 group t by new { t.DataPagamento, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = g.Key.DataPagamento.Value,
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento }
                        into g
                    select new RelatorioLancamentos
                    {
                        DataAgrupamento = g.Key.DataAgrupamento,
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByDataPorDataCriacao(IQueryable<Lancamento> query)
        {
            var queryComAgrupamentoMesAno =
                (from t in query
                 group t by new { DataCriacao = t.DataCriacao.Date, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = g.Key.DataCriacao,
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento }
                        into g
                    select new RelatorioLancamentos
                    {
                        DataAgrupamento = g.Key.DataAgrupamento,
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        /// <summary>
        ///     Adiciona a ordenação de Lancamentoes pela data selecionada no FiltroBuscaLancamentos
        /// </summary>
        /// <param name="query">IQueryable[Lancamento] necessário para construir a consulta</param>
        private IEnumerable<RelatorioLancamentos> AdicionarGroupByDataPorDataUltimaAtualizacao(IQueryable<Lancamento> query)
        {
            var queryComAgrupamentoMesAno =
                (from t in query
                 group t by
                     new { DataUltimaAtualizacao = t.DataUltimaAtualizacao.Date, IdContaAPagar = t.IdLancamento }
                     into g
                 select new RelatorioLancamentos
                 {
                     DataAgrupamento = g.Key.DataUltimaAtualizacao,
                     Total = g.Sum(f => f.Valor)
                 });

            return (from t in queryComAgrupamentoMesAno.ToList()
                    group t by new { t.DataAgrupamento }
                        into g
                    select new RelatorioLancamentos
                    {
                        DataAgrupamento = g.Key.DataAgrupamento,
                        Total = g.Sum(f => f.Total)
                    }).ToList();
        }

        #endregion Agrupamentos por data

        #endregion Critérios de Agrupamento

        #endregion Critérios
    }
}