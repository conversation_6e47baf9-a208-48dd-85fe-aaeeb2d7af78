﻿using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Despesas.Repositories
{

    public partial class LancamentoCategoriaRepository : ILancamentoCategoriaRepository
    {
        /// <summary>
        /// Método para obter uma categoriade lançamento de um estabelecimento a partir do enum CategoriaDeLancamentoPadraoEnum
        /// </summary>
        /// <param name="tipoLancamentoCategoria">enum CategoriaDeLancamentoPadraoEnum</param>
        /// <param name="idEstabelecimento">id do estabelecimento em que a busca será feita</param>
        public LancamentoCategoria ObterLancamentoCategoriaDoTipoNoEstabelecimento(CategoriaDeLancamentoPadraoEnum tipoLancamentoCategoria, int idEstabelecimento)
        {
            var categoria = Domain.Despesas.LancamentoCategoriaPadraoRepository.ObterCategoriaReferenteA(tipoLancamentoCategoria);
            return Queryable().FirstOrDefault(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)categoria);
        }

        /// <summary>
        /// Método para listar todos os tipos de despesas do estabelecimento que respeitam as propriedades configuradas na classe FiltroBuscaLancamentoCategoria
        /// </summary>
        /// <param name="filtro">Instancia do filtro para realizar a busca</param>
        public IQueryable<LancamentoCategoria> Filtrar(FiltroBuscaLancamentosCategoria filtro)
        {
            IQueryable<LancamentoCategoria> query = Queryable();
            query = AdicionarCriterioBuscaPorEstabelecimento(query, filtro.IdEstabelecimento);
            query = AdicionarCriterioBuscaPorAtivos(query, filtro);
            query = AdicionarCriterioBuscaPorNivelMinimoAcesso(query, filtro);
            query = AdicionarCriterioBuscaPorNomeContemOuIgual(query, filtro);
            query = AdicionarOrdenacaoPorNome(query);
            query = AplicarFiltroVersaoDasCategorias(query, filtro.IdEstabelecimento);

            return query;
        }

        public IQueryable<LancamentoCategoria> ObterTodosLancamentoCategoriaDoEstabelecimento(FiltroDespesasCadastradasDTO filtro)
        {
            var query = Queryable();

            query = AdicionarCriterioBuscaPorEstabelecimento(query, filtro.IdEstabelecimento);
            query = AplicarFiltroVersaoDasCategorias(query, filtro.IdEstabelecimento);

            return query;
        }

        public List<KeyValuePair<int, string>> ListarKeyValueVersaoAtualTodosStatusPorEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable();

            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento);
            query = AplicarFiltroVersaoDasCategorias(query, idEstabelecimento);
            query = AdicionarOrdenacaoPorNome(query);

            return query
                .Select(p => new KeyValuePair<int, string>(p.IdLancamentoCategoria, p.Nome))
                .ToList();
        }


        public IList<LancamentoCategoria> ObterLancamentoCategoriasAtivos(int idEstabelecimento)
        {
            var query = Queryable()
                .Where(q => q.Ativo);
            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento);
            query = AplicarFiltroVersaoDasCategorias(query, idEstabelecimento);

            return query.ToList();
        }

        public LancamentoCategoria ObterPorIdEIdEstabelecimento(int idLancamentoCategoria, int idEstabelecimento, bool somenteAtivo)
        {
            var query = Queryable();

            if (somenteAtivo)
            {
                query = query.Where(q => q.Ativo);
            }

            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento); 

            query = query.Where(q => q.IdLancamentoCategoria == idLancamentoCategoria);

            return query.FirstOrDefault();
        }

        public IEnumerable<int> CarregarIdsLancamentoCategoriaDeBonificacaoEVale(int idEstabelecimento)
        {
            return Queryable()
                .Where(x => x.Ativo &&
                    x.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                    !x.EhDespesaPersonalizadaComCategoriaPadrao && (
                    x.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.Bonificacao ||
                    x.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.BonificacaoV2 ||
                    x.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.ValeProfissional))
                .Select(x => x.IdLancamentoCategoria)
                .ToList();
        }

        public IList<LancamentoCategoria> ListarLancamentosCategoriaAtivosComValeOuBonificacaoComoCategoriaPadrao(int idEstabelecimento)
        {
            var query = Queryable()
               .Where(q => q.Ativo);
            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento);
            query = query
                .Where(q => q.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.ValeProfissional || q.LancamentoCategoriaPadrao.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.Bonificacao
                );

            return query.ToList();
        }

        public IEnumerable<KeyValuePair<int, string>> ObterKeyValueCategoria(int idEstabelecimento)
        {
            var query = Queryable().Where(q => q.Ativo);
            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento);

            return query.Select(a => new KeyValuePair<int, string>(a.IdLancamentoCategoria, a.Nome)).AsEnumerable();
        }

        private IQueryable<LancamentoCategoria> AplicarFiltroVersaoDasCategorias(IQueryable<LancamentoCategoria> lancamentoQuery, int idEstabelecimento)
        {
            var disponibilidade =
                Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                    idEstabelecimento, Recurso.UltimaVersaoCategoriasEGruposDeDespesas);

            var ultimaVersaoDisponivel = Domain.Despesas.LancamentoGrupoRepository.Queryable(true)
                .Where(x => x.Estabelecimento.IdEstabelecimento == idEstabelecimento && x.Ativo)
                .Max(x => x.Versao);

            if (disponibilidade && ultimaVersaoDisponivel > 1)
            {
                return lancamentoQuery.Where(x => x.LancamentoGrupo.Versao == ultimaVersaoDisponivel);
            }

            return AplicarFiltroToggleDespesasParticulares(lancamentoQuery.Where(x => x.LancamentoGrupo.Versao == 1), idEstabelecimento);
        }

        private IQueryable<LancamentoCategoria> AplicarFiltroToggleDespesasParticulares(IQueryable<LancamentoCategoria> lancamentoQuery, int idEstabelecimento)
        {
            var query = lancamentoQuery;

            var novaCategoriasDespesasPessoaisDisponivel =
                Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                    idEstabelecimento, Recurso.DespesasPessoais).EstaDisponivel;

            var idGrupoDespesasPessoaisNovas =
                Domain.Pessoas.ParametrizacaoTrinksRepository.ObterValorParametro<int>(ParametrosTrinksEnum
                    .id_grupo_despesas_pessoais_novo);

            var idGrupoDespesasPessoaisAntigo =
                Domain.Pessoas.ParametrizacaoTrinksRepository.ObterValorParametro<int>(ParametrosTrinksEnum
                    .id_grupo_despesas_pessoais_antigo);

            if (novaCategoriasDespesasPessoaisDisponivel)
            {
                query = query.Where(l => l.LancamentoGrupo.IdLancamentoGrupoPadrao != idGrupoDespesasPessoaisAntigo);
            }
            else
            {
                query = query.Where(l => l.LancamentoGrupo.IdLancamentoGrupoPadrao != idGrupoDespesasPessoaisNovas);
            }

            return query;
        }

        private IQueryable<LancamentoCategoria> QueryableAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(FiltroBuscaLancamentosCategoria filtro)
        {
            IQueryable<LancamentoCategoria> query = Queryable();
            query = AdicionarCriterioBuscaPorEstabelecimento(query, filtro.IdEstabelecimento);

            if (filtro.NivelMinimoAcesso != null)
                query = AdicionarCriterioBuscaPorNivelMinimoAcesso(query, filtro);

            if (filtro.IdLancamentoGrupo > 0)
                query = AdicionarCriterioBuscaPorLancamentoGrupo(query, filtro);

            if (filtro.ExibirInativosComLancamentosAssociados)
                query = AdicionarCriterioBuscaPorAtivosOuInativosAssociadosALancamento(query);
            else if (filtro.IdLancamentoCategoria > 0)
                query = AdicionarCriterioBuscaPorAtivosEPeloLancamentoCategoria(query, filtro);
            else
                query = AdicionarCriterioBuscaPorAtivos(query, filtro);

            query = AdicionarCriterioBuscaPermissaoLancamentoManual(query);
            query = AdicionarOrdenacaoPorNome(query);
            return query;
        }

        public List<KeyValuePair<Int32, String>> ListarKeyValueAtivosPorEstabelecimentoOrdenadosPorNomeFantasiaQuePermitemEdicaoEExclusao(FiltroBuscaLancamentosCategoria filtro)
        {
            IQueryable<LancamentoCategoria> query = QueryableAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(filtro);
            query = AdicionarCriterioApenasQuePermitemEdicaoEExclusao(query);
            query = AplicarFiltroVersaoDasCategorias(query, filtro.IdEstabelecimento);
            return query.Select(p => new KeyValuePair<Int32, String>(p.IdLancamentoCategoria, p.Ativo ? p.Nome : String.Format("{0} [Inativo]", p.Nome))).ToList();
        }

        public List<KeyValuePair<Int32, String>> ListarKeyValueAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(FiltroBuscaLancamentosCategoria filtro)
        {
            IQueryable<LancamentoCategoria> query = QueryableAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(filtro);
            return query.Select(p => new KeyValuePair<Int32, String>(p.IdLancamentoCategoria, p.Ativo ? p.Nome : String.Format("{0} [Inativo]", p.Nome))).ToList();
        }

        private static IQueryable<LancamentoCategoria> AdicionarCriterioApenasQuePermitemEdicaoEExclusao(IQueryable<LancamentoCategoria> query)
        {
            return query.Where(p => p.PermiteEdicao && p.PermiteExclusao);
        }

        public List<Tuple<Int32, Int32, String>> ListarKeyValueComGrupoAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(FiltroBuscaLancamentosCategoria filtro)
        {
            IQueryable<LancamentoCategoria> query = Queryable();
            query = AdicionarCriterioBuscaPorEstabelecimento(query, filtro.IdEstabelecimento);
            query = AdicionarCriterioBuscaPorAtivos(query, filtro);
            query = AdicionarOrdenacaoPorNome(query);

            return query.Select(p => new Tuple<Int32, Int32, String>(p.IdLancamentoCategoria, p.LancamentoGrupo.IdLancamentoGrupo, p.Nome)).ToList();
        }

        public bool ValidaSeEstabelecimentoJaPossuiLancamentosCategoriaPorTipo(Int32 idEstabelecimento, LancamentoTipo tipo)
        {
            IQueryable<LancamentoCategoria> query = Queryable();
            return query.Any(p => p.LancamentoTipo.IdLancamentoTipo == tipo.IdLancamentoTipo && p.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        /// <summary>
        /// Método para salvar ou atualizar a entidade no banco de dados
        /// </summary>
        /// <param name="LancamentoCategoria">Instancia válida da classe LancamentoCategoria</param>
        public void SaveOrUpdate(LancamentoCategoria lancamentoCategoria)
        {
            if (lancamentoCategoria.IdLancamentoCategoria > 0)
            {
                Update(lancamentoCategoria);
            }
            else
            {
                SaveNew(lancamentoCategoria);
            }
        }

        /// <summary>
        /// Método para verificar se existe um outra categoria de lançamento com a descrição
        /// </summary>
        /// <param name="descricao">Descrição de uma Categoria de Lançamento</param>
        public Boolean ExisteLancamentoCategoriaComMesmoNome(String nome, Int32 idCategoria, Int32 idEstabelecimento)
        {
            IQueryable<LancamentoCategoria> query = Queryable();

            if (idCategoria == 0)
                query = AdicionarCriterioBuscaPorNome(query, nome);
            else
                query = AdicionarCriterioMesmoNomeEIdDiferenteDoAtual(query, nome, idCategoria);

            query = AdicionarCriterioBuscaPorEstabelecimento(query, idEstabelecimento);

            return query.Any();
        }

        /// <summary>
        /// Método para nome do LancamentoCategoria
        /// </summary>
        public String ObterNome(Int32 idLancamentoCategoria)
        {
            IQueryable<LancamentoCategoria> query = Queryable();
            query = AdicionarCriterioBuscaPorIdLancamentoCategoria(query, idLancamentoCategoria);
            return query.Select(p => p.Nome).FirstOrDefault();
        }

        #region Critérios

        /// <summary>
        /// Adiciona o critério de busca por um estabelecimento específico
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        /// <param name="idEstabelecimento">Id do Estabelecimento</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorEstabelecimento(IQueryable<LancamentoCategoria> query, Int32 idEstabelecimento)
        {
            return query.Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        /// <summary>
        /// Adiciona o critério para buscar por LancamentoCategoriaes somentes ativos ou inativos
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        /// <param name="apenasAtivos">Retornar somente forncedores ativos?</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorAtivos(IQueryable<LancamentoCategoria> query, FiltroBuscaLancamentosCategoria filtro)
        {
            if (filtro.ApenasAtivos)
                return query.Where(p => p.Ativo);

            return query;
        }

        /// <summary>
        /// Adiciona o critério para buscar por LancamentoCategoriaes somentes ativos ou inativos associados a algum lançamento
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorAtivosOuInativosAssociadosALancamento(IQueryable<LancamentoCategoria> query)
        {
            var queryLancamento = Domain.Despesas.LancamentoRepository.Queryable();
            return query.Where(lc => lc.Ativo || (!lc.Ativo && queryLancamento.Any(l => l.LancamentoCategoria == lc)));
        }

        /// <summary>
        /// Adiciona o critério para buscar por LancamentoCategoriaes somentes ativos ou inativos associados a algum lançamento
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorAtivosEPeloLancamentoCategoria(IQueryable<LancamentoCategoria> query, FiltroBuscaLancamentosCategoria filtro)
        {
            return query.Where(lc => lc.Ativo || lc.IdLancamentoCategoria == filtro.IdLancamentoCategoria);
        }

        /// <summary>
        /// Adiciona o critério para buscar por LançamentoGrupo
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorLancamentoGrupo(IQueryable<LancamentoCategoria> query, FiltroBuscaLancamentosCategoria filtro)
        {
            return query.Where(lc => lc.LancamentoGrupo.IdLancamentoGrupo == filtro.IdLancamentoGrupo);
        }

        /// <summary>
        /// Adiciona o critério para buscar por LancamentoCategoriaes com propriedade Nome que contém ou é igual ao parametro nome
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        /// <param name="nomeFantasia">Texto ou parte do texto do NomeFantasia</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorNomeContemOuIgual(IQueryable<LancamentoCategoria> query, FiltroBuscaLancamentosCategoria filtro)
        {
            if (String.IsNullOrEmpty(filtro.Nome))
                return query;
            return query.Where(p => p.Nome.Contains(filtro.Nome.Trim()));
        }

        private IQueryable<LancamentoCategoria> AdicionarCriterioMesmoNomeEIdDiferenteDoAtual(IQueryable<LancamentoCategoria> query, String nome, Int32 id)
        {
            return query.Where(p => p.Nome == nome && p.IdLancamentoCategoria != id);
        }

        /// <summary>
        /// Adiciona o critério para buscar por LancamentoCategoriaes com propriedade NivelMinimoAcesso igual ao especificado no filtro
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        /// <param name="nomeFantasia">Texto ou parte do texto do NomeFantasia</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorNivelMinimoAcesso(IQueryable<LancamentoCategoria> query, FiltroBuscaLancamentosCategoria filtro)
        {
            return query.Where(p => p.NivelMinimoAcesso <= (int)filtro.NivelMinimoAcesso);
        }

        /// <summary>
        /// Adiciona o critério para buscar por LancamentoCategoriaes com propriedade Descricao igual ao especificado no parametro descricao
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        /// <param name="descricao">Descrição de uma Categoria de Lançamento</param>
        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorNome(IQueryable<LancamentoCategoria> query, String nome)
        {
            return query.Where(p => p.Nome == nome);
        }

        /// <summary>
        /// Adiciona a ordenação de LancamentoCategoriaes pela propriedade Nome igual ao especificado no filtro
        /// </summary>
        /// <param name="query">IQueryable[LancamentoCategoria] necessário para construir a consulta</param>
        private IQueryable<LancamentoCategoria> AdicionarOrdenacaoPorNome(IQueryable<LancamentoCategoria> query)
        {
            return query.OrderBy(p => p.Nome);
        }

        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPorIdLancamentoCategoria(IQueryable<LancamentoCategoria> query, Int32 idLancamentoCategoria)
        {
            return query.Where(p => p.IdLancamentoCategoria == idLancamentoCategoria);
        }

        private IQueryable<LancamentoCategoria> AdicionarCriterioBuscaPermissaoLancamentoManual(IQueryable<LancamentoCategoria> query)
        {
            return query.Where(p => p.PermiteLancamentoManual);
        }

        #endregion Critérios
    }
}