﻿using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Despesas.Repositories
{
    public partial interface ILancamentoRepository
    {
        List<RelatorioLancamentos> FiltrarAgrupadoPorMesAno(FiltroBuscaLancamentos filtro);
        List<RelatorioLancamentos> FiltrarAgrupadoPorData(FiltroBuscaLancamentos filtro);
        List<Lancamento> Filtrar(FiltroBuscaLancamentos filtro);
        void SaveOrUpdate(Lancamento lancamento);
        decimal ValorEmVale(ParametrosFiltrosRelatorio filtro);
        decimal ValorEmValeTransporte(ParametrosFiltrosRelatorio filtro);
        decimal ValorEmAlimentacao(ParametrosFiltrosRelatorio filtro);
        decimal ValorEmOutrosDescontos(ParametrosFiltrosRelatorio filtro);
        IQueryable<Lancamento> ListarVales(ParametrosFiltrosRelatorio filtro);
        IQueryable<Lancamento> ListarLancamentosExibidosNoRelatorioDeComissao(ParametrosFiltrosRelatorio filtro);
        IList<DetalhesDespesasDTO> ListarDespesasPagasPeloCaixa(int idEstabelecimento, DateTime? dataInicial, DateTime? dataFinal);
        IQueryable<Lancamento> ListarSplitsDePagamento(ParametrosFiltrosRelatorio filtro);
        IQueryable<Lancamento> ListarBonificacoes(ParametrosFiltrosRelatorio filtro);
        decimal ObterTotalDeBonificacoesNoPeriodo(int idEstabelecimento, DateTime dataInicial, DateTime dataFinal, int idPessoaProfissional);
        bool TransacaoPossuiRegistroDeSplitAtivo(int idTransacao);
        decimal ObterTotalSplitNoPeriodoPorProfissional(int idPessoaProfissional, int idEstabelecimento, DateTime dataInicial, DateTime dataFinal);
        LancamentoOriginalDTO ObterLancamentoOriginalPorId(int idLancamento);
        int ObterTotalDeDespesasNoEstabelecimento(int idEstabelecimento);
        DateTime? ObterUltimaDataDeLancamentoDeVale(int idEstabelecimento);
        Lancamento ObterPorId(int id, int idEstabelecimento);
        IQueryable<Lancamento> ObterQueryBonificacoesAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim);
        IQueryable<Lancamento> ObterQueryBonificacoesAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu);
        IQueryable<Lancamento> ObterQueryBonificacoesPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim);
        IQueryable<Lancamento> ObterQueryBonificacoesPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu);
        IQueryable<Lancamento> ObterQueryValesAbertos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim);
        IQueryable<Lancamento> ObterQueryValesAbertos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu);
        IQueryable<Lancamento> ObterQueryValesPagos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim);
        IQueryable<Lancamento> ObterQueryValesPagos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu);
        IQueryable<Lancamento> ObterQuerySplitsAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim);
        IQueryable<Lancamento> ObterQuerySplitsAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu);
        IQueryable<Lancamento> ObterQuerySplitsPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim);

        IQueryable<Lancamento> ObterQuerySplitsPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int idPessoaQueRecebeu);

        bool EhDespesaRecorrente(int idLancamento, int idEstabelecimentoAutenticado);
        IQueryable<Lancamento> ObterQueryBonificacoesPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional);
        IQueryable<Lancamento> ObterQueryBonificacoesAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional);
        IQueryable<Lancamento> ObterQueryValesPagos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional);
        IQueryable<Lancamento> ObterQueryValesAbertos(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional);
        IQueryable<Lancamento> ObterQuerySplitsPagas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional);
        IQueryable<Lancamento> ObterQuerySplitsAbertas(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, IEnumerable<int> listaIdPessoaProfissional);
        bool ExisteLancamentoRealizadoComOLancamentoCategoria(int idEstabelecimento, int idLancamentoCategoria);
    }
}
