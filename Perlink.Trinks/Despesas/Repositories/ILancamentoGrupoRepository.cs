﻿using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Despesas.Repositories
{

    public partial interface ILancamentoGrupoRepository
    {

        bool ValidaSeEstabelecimentoJaPossuiLancamentosCategoriaPorTipo(int idEstabelecimento, LancamentoGrupo lancamento);

        IQueryable<LancamentoGrupo> ObterLancamentosGrupoDoEstabelecimento(int idEstabelecimento, bool? status);

        string ObterNomeDoLancamentoGrupo(int idLancamento);

        List<KeyValuePair<int, string>> ListarKeyValuePairAtivosDoEstabelecimento(int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarKeyValuePairAtivosVersaoAtualDoEstabelecimento(int idEstabelecimento, bool? status);
    }
}