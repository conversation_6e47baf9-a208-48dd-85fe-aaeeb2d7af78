﻿using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Despesas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Despesas.Repositories
{
    public partial interface ILancamentoCategoriaRepository
    {
        LancamentoCategoria ObterLancamentoCategoriaDoTipoNoEstabelecimento(CategoriaDeLancamentoPadraoEnum tipoLancamentoCategoria, int idEstabelecimento);
        IQueryable<LancamentoCategoria> Filtrar(FiltroBuscaLancamentosCategoria filtro);
        List<KeyValuePair<Int32, String>> ListarKeyValueAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(FiltroBuscaLancamentosCategoria filtro);
        List<Tuple<Int32, Int32, String>> ListarKeyValueComGrupoAtivosPorEstabelecimentoOrdenadosPorNomeFantasia(FiltroBuscaLancamentosCategoria filtro);
        //KeyValuePair<Int32, String> ObterKeyValuePorIdLancamentoCategoria(Int32 idLancamentoCategoria);
        void SaveOrUpdate(LancamentoCategoria lancamentoCategoria);
        String ObterNome(Int32 idLancamentoCategoria);
        Boolean ExisteLancamentoCategoriaComMesmoNome(String descricao, Int32 idCategoriaLancamento, Int32 idEstabelecimento);
        bool ValidaSeEstabelecimentoJaPossuiLancamentosCategoriaPorTipo(Int32 idEstabelecimento, LancamentoTipo tipo);
        List<KeyValuePair<Int32, String>> ListarKeyValueAtivosPorEstabelecimentoOrdenadosPorNomeFantasiaQuePermitemEdicaoEExclusao(FiltroBuscaLancamentosCategoria filtro);
        IList<LancamentoCategoria> ObterLancamentoCategoriasAtivos(int idEstabelecimento);
        IEnumerable<KeyValuePair<int, string>> ObterKeyValueCategoria(int idEstabelecimento);
        IList<LancamentoCategoria> ListarLancamentosCategoriaAtivosComValeOuBonificacaoComoCategoriaPadrao(int idEstabelecimento);
        LancamentoCategoria ObterPorIdEIdEstabelecimento(int idLancamentoCategoria, int idEstabelecimento, bool somenteAtivo);
        IQueryable<LancamentoCategoria> ObterTodosLancamentoCategoriaDoEstabelecimento(FiltroDespesasCadastradasDTO filtro);
        List<KeyValuePair<int, string>> ListarKeyValueVersaoAtualTodosStatusPorEstabelecimento(int idEstabelecimento);
        IEnumerable<int> CarregarIdsLancamentoCategoriaDeBonificacaoEVale(int idEstabelecimento);
    }
}
