﻿using Perlink.Trinks.Despesas.Enums;
namespace Perlink.Trinks.Despesas.DTO
{
    public class CarregarLancamentoCategoriaDTO : LancamentoCategoriaBaseDTO
    {
        public CarregarLancamentoCategoriaDTO(LancamentoCategoria lancamentoCategoria, bool existeLancamentoRealizadoComOLancamentoCategoria) : base(lancamentoCategoria)
        {
            DescontarDaComissaoDoProfissional = lancamentoCategoria.LancamentoCategoriaPadrao?.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.ValeProfissional;
            AcrescentarNaComissaoDoProfissional = lancamentoCategoria.LancamentoCategoriaPadrao?.IdLancamentoCategoria == (int)CategoriaDeLancamentoPadraoEnum.Bonificacao;
            ExisteLancamentoRealizadoComOLancamentoCategoria = existeLancamentoRealizadoComOLancamentoCategoria;
        }

        public bool DescontarDaComissaoDoProfissional { get; set; }
        public bool AcrescentarNaComissaoDoProfissional { get; set; }
        public bool ExisteLancamentoRealizadoComOLancamentoCategoria { get; set; }
    }
}
