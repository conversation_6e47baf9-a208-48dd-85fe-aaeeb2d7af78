﻿using Iesi.Collections;
using Perlink.Trinks.DataQuery.DTO;
using Perlink.Trinks.Pessoas.Enums;

namespace Perlink.Trinks.Despesas.DTO
{
    public class FiltroDespesasCadastradasDTO : QueryBaseDTO
    {
        public AcessoBackoffice NivelMinimoAcesso { get; set; }
        public int IdEstabelecimento { get; set; }
        public string Nome { get; set; }
        public string Status { get; set; }
        public string NomeLancamentoGrupo { get; set; }
        public int? Relacao { get; set; }

        public override string DefaultSortingKey => "Nome";
    }
}
