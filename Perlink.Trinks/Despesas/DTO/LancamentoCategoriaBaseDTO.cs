﻿using Perlink.Trinks.Pessoas.Enums;
namespace Perlink.Trinks.Despesas.DTO
{
    public class LancamentoCategoriaBaseDTO
    {
        public LancamentoCategoriaBaseDTO(LancamentoCategoria lancamentoCategoria)
        {
            NomeDespesa = lancamentoCategoria.Nome;
            IdLancamentoCategoria = lancamentoCategoria.IdLancamentoCategoria;
            NomeGrupo = lancamentoCategoria.LancamentoGrupo?.NomeGrupoCategoria ?? string.Empty;
            IdLancamentoGrupo = lancamentoCategoria.LancamentoGrupo?.IdLancamentoGrupo ?? 0;
            NecessitaProfissional = lancamentoCategoria.NecessarioInformarProfissional;
            NecessitaFornecedor = lancamentoCategoria.NecessarioInformarFornecedor;
            AcessoParaRecepcao = (lancamentoCategoria.NivelMinimoAcesso) == (int)AcessoBackoffice.Somente_Agenda;
            Ativo = lancamentoCategoria.Ativo;
            PermiteEdicao = lancamentoCategoria.PermiteEdicao;
            PermiteExclusao = lancamentoCategoria.PermiteExclusao;
            EhDespesaPersonalizadaComCategoriaPadrao = lancamentoCategoria.EhDespesaPersonalizadaComCategoriaPadrao;
            IdLancamentoCategoriaPadrao = lancamentoCategoria.LancamentoCategoriaPadrao?.IdLancamentoCategoria ?? 0;
        }

        public string NomeDespesa { get; set; }
        public int IdLancamentoCategoria { get; set; }
        public string NomeGrupo { get; set; }
        public int IdLancamentoGrupo { get; set; }
        public bool NecessitaProfissional { get; set; }
        public bool NecessitaFornecedor { get; set; }
        public bool AcessoParaRecepcao { get; set; }
        public bool Ativo { get; set; }
        public bool PermiteEdicao { get; set; }
        public bool PermiteExclusao { get; set; }
        public bool EhDespesaPersonalizadaComCategoriaPadrao { get; set; }
        public int IdLancamentoCategoriaPadrao { get; set; }
    }
}
