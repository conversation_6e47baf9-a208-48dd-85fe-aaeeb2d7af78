﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Shared.Text;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Loggers;
using Perlink.Trinks.Pacotes.DTO;
using Perlink.Trinks.Pacotes.Enums;
using Perlink.Trinks.Pacotes.Repositories;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Repositories;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Web;

namespace Perlink.Trinks.Pacotes.Services
{
    public class PacoteService : BaseService, IPacoteService
    {

        #region Atributos e construtores

        private readonly IPacoteRepository _pacoteRepository;
        private readonly IConfiguracaoPacotePersonalizadoRepository _configuracaoPacotePersonalizadoRepository;
        private readonly IEstabelecimentoProdutoRepository _estabelecimentoProdutoRepository;
        private readonly IServicoEstabelecimentoRepository _servicoEstabelecimentoRepository;
        private readonly ValidationHelper _validationHelper;
        private readonly ICloudWatchLogger _loggerPacotePersonalizado;

        public PacoteService()
        {
            _pacoteRepository = new PacoteRepository();
            _configuracaoPacotePersonalizadoRepository = new ConfiguracaoPacotePersonalizadoRepository();
            _estabelecimentoProdutoRepository = new EstabelecimentoProdutoRepository();
            _servicoEstabelecimentoRepository = new ServicoEstabelecimentoRepository();
            _validationHelper = ValidationHelper.Instance;
            _loggerPacotePersonalizado = new CloudWatchLogger(ConfiguracoesTrinks.AWS.PacotePersonalizadoLogGroupName);
        }

        public PacoteService(IPacoteRepository pacoteRepository,
            IConfiguracaoPacotePersonalizadoRepository configuracaoPacotePersonalizadoRepository,
            IEstabelecimentoProdutoRepository estabelecimentoProdutoRepository,
            IServicoEstabelecimentoRepository servicoEstabelecimentoRepository,
            ValidationHelper validationHelper,
            ICloudWatchLogger cloudWatchLogger)
        {
            _pacoteRepository = pacoteRepository;
            _configuracaoPacotePersonalizadoRepository = configuracaoPacotePersonalizadoRepository;
            _estabelecimentoProdutoRepository = estabelecimentoProdutoRepository;
            _servicoEstabelecimentoRepository = servicoEstabelecimentoRepository;
            _validationHelper = validationHelper;
            _loggerPacotePersonalizado = cloudWatchLogger;
        }
        #endregion

        public void ManterPacote(Pacote pacote)
        {

            ValidarPacote(pacote);

            if (!ValidationHelper.Instance.IsValid) return;

            if (pacote.EhPacoteDeAssinatura)
            {
                pacote.ValidadeDeUsoEmMeses = 1;
            }

            if (pacote.Id > 0)
            {
                Domain.Pacotes.PacoteRepository.Update(pacote);
            }
            else
            {
                Domain.Pacotes.PacoteRepository.SaveNew(pacote);
            }

            Domain.Pessoas.EstabelecimentoService.AtualizarVersaoDaGeracaoDeNFC(pacote.Estabelecimento);
        }

        private void ValidarPacote(Pacote pacote)
        {

            if (pacote.Valor <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao("O pacote não pode ter valor zero.");

            if (pacote.ItensPacote.Any(i => i.ValorUnitarioFiscal <= 0))
                ValidationHelper.Instance.AdicionarItemValidacao("Cada item deve ter o valor unitário fiscal maior que zero.");

            if (pacote.ItensPacote.Count == 0)
                ValidationHelper.Instance.AdicionarItemValidacao("O pacote deve conter pelo menos um item.");

            if (pacote.ItensPacote.Any(f => f.Quantidade <= 0))
                ValidationHelper.Instance.AdicionarItemValidacao("Cada item deve ter quantidade maior ou igual a um.");

            if (pacote.ItensPacote.Sum(f => f.Quantidade) > 360)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Um pacote não deve possuir mais de 360 itens de pacote somados.");

            if (_pacoteRepository.ExistePacotesComNomeRepetido(pacote.Id, pacote.Nome,
                    pacote.Estabelecimento))
                ValidationHelper.Instance.AdicionarItemValidacao("Já existe outro pacote com o mesmo nome.");

            ValidarItensDoPacote(pacote);
        }

        private static void ValidarItensDoPacote(Pacote pacote)
        {
            var idsProdutos = pacote.ItensPacote.Where(f => f is ItemPacoteProduto).Select(f => new
            {
                ((ItemPacoteProduto)f).EstabelecimentoProduto.Id
            }).ToList();
            if (idsProdutos.Count() != idsProdutos.Distinct().Count())
                ValidationHelper.Instance.AdicionarItemValidacao("Existem produtos repetidos no pacote.");

            var idsServicos = pacote.ItensPacote.Where(f => f is ItemPacoteServico).Select(f => new
            {
                ((ItemPacoteServico)f).ServicoEstabelecimento.IdServicoEstabelecimento
            }).ToList();
            if (idsServicos.Count() != idsServicos.Distinct().Count())
                ValidationHelper.Instance.AdicionarItemValidacao("Existem serviços repetidos no pacote.");
        }

        public bool PossuiPacoteDeAssinatura(int idEstabelecimento)
        {
            return Domain.Pacotes.PacoteRepository.Queryable(false).Any(p =>
                p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo && p.EhPacoteDeAssinatura);
        }
        public bool EstabelecimentoPossuiPacotesASeremExibidos(int idEstabelecimento, int? idFranquia = null)
        {
            bool permiteExibicao = Domain.Pessoas.HotsiteEstabelecimentoRepository.PermiteExibicaoDePacotesNoHotsite(idEstabelecimento);
            bool possuiPacoteExibir = permiteExibicao && Domain.Pacotes.PacoteRepository.PacotePodeSerExibido(idEstabelecimento);

            return possuiPacoteExibir;
        }

        public ResultadoPaginado<DadosDePacotesDTO> ListarPacotes(FiltroDePacoteDTO filtro)
        {

            var query = ObterQueryFiltrada(filtro);
            query = query.OrderBy(p => p.Nome);

            if (filtro.AplicarPaginacao)
            {
                var pag = filtro.Paginacao;
                pag.TotalItens = query.Count();
                query = query.Skip(pag.RegistroInicial - 1).Take(pag.RegistrosPorPagina);
            }

            var retorno = query
                .Select(p => new DadosDePacotesDTO
                {
                    IdPacote = p.Id,
                    NomePacote = p.Nome,
                    DescricaoPacote = p.Descricao,
                    PrecoPacote = p.Valor,
                    Status = p.Ativo ? StatusDoPacote.Ativo : StatusDoPacote.Todos,
                    EhPacoteDoModelo = p.PacoteModelo != null,
                }).ToList();

            PreencherInformacoesAdicionais(retorno);

            return new ResultadoPaginado<DadosDePacotesDTO>(retorno, filtro.Paginacao);

        }

        public MemoryStream GerarCsvPacotes(List<DadosDePacotesDTO> listaPacotes, FiltroDePacoteDTO filtro)
        {
            var estabelecimentoBaseadoEmModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.EstabelecimentoEhFranquiaBaseadoEmModelo(filtro.IdEstabelecimento);

            Csv csv = new Csv();
            if (listaPacotes != null && listaPacotes.Any())
            {
                foreach (var item in filtro.ResumosFiltro)
                {
                    csv.AdicionarLinha(item);
                }
                csv.AdicionarLinha("");

                var cabecalho = new Csv.Linha();
                cabecalho.AdicionarCelula("Nome");
                cabecalho.AdicionarCelula("Descrição");
                cabecalho.AdicionarCelula("Preço");
                cabecalho.AdicionarCelula("Status");
                cabecalho.AdicionarCelula("Itens");
                cabecalho.AdicionarCelula("Custo Operacional dos Serviços");
                csv.AdicionarLinha(cabecalho);

                foreach (var p in listaPacotes)
                {
                    var linha = new Csv.Linha();

                    linha.AdicionarCelula($"{p.NomePacote} {(p.EhPacoteDoModelo && estabelecimentoBaseadoEmModelo ? "(Modelo)" : "")}");
                    linha.AdicionarCelula(p.DescricaoPacote);
                    linha.AdicionarCelula(p.PrecoPacote.ValorDecimal());
                    linha.AdicionarCelula(p.Status == StatusDoPacote.Ativo ? "Ativo" : "Inativo");
                    var itens = string.Join(" / ", p.ItensDoPacote);
                    linha.AdicionarCelula(itens);
                    linha.AdicionarCelula(p.CustoOperacionalServicos.ValorDecimal());
                    csv.AdicionarLinha(linha);
                }
            }

            return csv.ObterArquivo();
        }

        public void InativarPacote(int idPacote, int idEstabelecimento)
        {
            var pacote = Domain.Pacotes.PacoteRepository.Load(idPacote);

            if (pacote.Estabelecimento.IdEstabelecimento != idEstabelecimento)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Sua proposta não pode estar em branco");
            }

            pacote.Ativo = false;
            pacote.VendaHotsite = false;

            Domain.Pacotes.PacoteRepository.Update(pacote);
        }

        public void ReativarPacote(int idPacote, int idEstabelecimento)
        {
            var pacote = Domain.Pacotes.PacoteRepository.Load(idPacote);

            if (pacote.Estabelecimento.IdEstabelecimento != idEstabelecimento)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Requisição inválida");
            }

            if (pacote.Ativo)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O pacote já está ativo");
            }

            pacote.Ativo = true;

            Domain.Pacotes.PacoteRepository.Update(pacote);
        }

        private void PreencherInformacoesAdicionais(List<DadosDePacotesDTO> pacotes)
        {

            var itensPacoteServicos = Domain.Pacotes.ItemPacoteServicoRepository.Queryable();
            var itensPacoteProdutos = Domain.Pacotes.ItemPacoteProdutoRepository.Queryable();
            foreach (var pacote in pacotes)
            {

                var itensPacote = Domain.Pacotes.PacoteRepository.ObterItensDoPacote(pacote.IdPacote);

                pacote.ItensDoPacote = itensPacote
                        .Select(f => string.Format("{0} x {1} - Valor Unitário: R$ {2}", f.Quantidade, f.Nome, f.ValorUnitario)).ToList();
                pacote.CustoOperacionalServicos = itensPacote.Sum(a => ((a.CustoProdutosBaixaAutomaticaServico + a.CustoDescartaveis) * a.Quantidade));

            }
        }

        private IQueryable<Pacote> ObterQueryFiltrada(FiltroDePacoteDTO filtro)
        {

            var query = Domain.Pacotes.PacoteRepository.Queryable().Where(p => !p.EhPacoteDeAssinatura && !p.EhPersonalizado);

            if (filtro.ApenasAtivos == StatusDoPacote.Ativo || filtro.ApenasAtivos == 0)
            {
                query = query.Where(p => p.Ativo == true);
            }

            query = query.Where(p => p.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento);

            if (!string.IsNullOrWhiteSpace(filtro.TextoBusca) && (filtro.TipoBusca == ParametroDeBuscaPacote.Nome || filtro.TipoBusca == 0))
            {
                query = query.Where(p => p.Nome.Contains(filtro.TextoBusca));
            }

            if (!string.IsNullOrWhiteSpace(filtro.TextoBusca) && filtro.TipoBusca == ParametroDeBuscaPacote.Descricao)
            {
                query = query.Where(p => p.Descricao.Contains(filtro.TextoBusca));
            }

            if (filtro.TipoItemSincronia == TipoItemSincroniaEnum.ApenasPacotesDentroDoModelo)
            {
                query = query.Where(p => p.PacoteModelo != null);
            }

            if (filtro.TipoItemSincronia == TipoItemSincroniaEnum.ApenasPacotesForaDoModelo)
            {
                query = query.Where(p => p.PacoteModelo == null);
            }

            return query;
        }

        public void ConfigurarCompartilhamentoNaRede(int idPacote, int idPessoaQueAlterou, bool compartilhadoNaRede)
        {
            var pacote = Domain.Pacotes.PacoteRepository.Load(idPacote);

            if (pacote.Estabelecimento.IdEstabelecimento != Domain.WebContext.IdEstabelecimentoAutenticado.Value)
                throw new UnauthorizedAccessException();

            pacote.CompartilhadoNaRede = compartilhadoNaRede;
            Domain.Pacotes.PacoteRepository.Update(pacote);

            var idFranquia = pacote.Estabelecimento.FranquiaEstabelecimento.Franquia.Id;
            Domain.Pessoas.FranquiaEstabelecimentoRepository.AtualizarSeExistePacoteDisponivelNaRedeParaConsumo(idFranquia);
            SalvarHistoricoDeConfiguracaoDePacote(pacote, idPessoaQueAlterou, pacote.CompartilhadoNaRede, TipoConfiguracaoPacoteEnum.Compartilhamento);
        }

        private static void SalvarHistoricoDeConfiguracaoDePacote(Pacote pacote, int idPessoaQueAlterou, bool compartilhadoNaRede, TipoConfiguracaoPacoteEnum tipo)
        {

            var estabelecimentosCompartilhados = Domain.Pessoas.CompartilhamentoNaRedeRepository.ListarUnidadesCompartilhadasComEstabelecimento((int)Domain.WebContext.IdEstabelecimentoAutenticado);

            var descricao = "";

            if (estabelecimentosCompartilhados.Count() == 0)
            {
                if (compartilhadoNaRede)
                    descricao = pacote.Nome + " : ativado compartilhamento na rede";
                else
                    descricao = pacote.Nome + " : desativado compartilhamento na rede";

                var historicoConfigPacote = new HistoricoConfiguracoesPacote(pacote, idPessoaQueAlterou, tipo, descricao);
                Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.SaveNew(historicoConfigPacote);
                return;
            }

            var tipoCompartilhamentoNaRede = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterTipoCompartilhaNaRede(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            if (tipoCompartilhamentoNaRede == Pessoas.Enums.TipoCompartilhaNaRedeEnum.NenhumaUnidade)
            {
                descricao = pacote.Nome + " : removido compartilhamento com todas unidades";

                var historicoConfigPacote = new HistoricoConfiguracoesPacote(pacote, idPessoaQueAlterou, tipo, descricao);
                Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.SaveNew(historicoConfigPacote);
            }

            if (tipoCompartilhamentoNaRede == Pessoas.Enums.TipoCompartilhaNaRedeEnum.QualquerUnidade)
            {
                descricao = pacote.Nome + " : compartilhado com todas unidades";

                var historicoConfigPacote = new HistoricoConfiguracoesPacote(pacote, idPessoaQueAlterou, tipo, descricao);
                Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.SaveNew(historicoConfigPacote);
            }

            if (tipoCompartilhamentoNaRede == Pessoas.Enums.TipoCompartilhaNaRedeEnum.Personalizado)
            {
                foreach (var estabelecimento in estabelecimentosCompartilhados)
                {
                    if (pacote.CompartilhadoNaRede == true)
                        descricao = pacote.Nome + " : compartilhado com " + estabelecimento.Nome;

                    if (pacote.CompartilhadoNaRede == false)
                        descricao = pacote.Nome + " : removido compartilhamento com " + estabelecimento.Nome;

                    var historicoConfigPacote = new HistoricoConfiguracoesPacote(pacote, idPessoaQueAlterou, tipo, descricao);
                    Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.SaveNew(historicoConfigPacote);
                }
            }
        }

        public void AtualizarExibicaoPacote(ConfiguracaoExibicaoEVendaDTO dto, int idPessoaQueAlterou)
        {
            foreach (var item in dto.Pacotes)
            {
                var pacote = Domain.Pacotes.PacoteRepository.Load(item.Id);
                var valorAtual = pacote.ExibePacote;
                var novoValor = item.PodeExibirPacoteHotsite;


                if (valorAtual != novoValor)
                {
                    pacote.ExibePacote = item.PodeExibirPacoteHotsite;
                    Domain.Pacotes.PacoteRepository.UpdateNoFlush(pacote);

                    var descricao = pacote.ExibePacote ? ("O pacote " + "'" + pacote.Nome + "'" + " será exibido no site") : ("O pacote " + "'" + pacote.Nome + "'" + " não será exibido no site");

                    var historicoConfig = new HistoricoConfiguracoesPacote(pacote, idPessoaQueAlterou, TipoConfiguracaoPacoteEnum.ExibicaoNoSite, descricao);
                    Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.SaveNewNoFlush(historicoConfig);
                }
            }
            Domain.Pacotes.PacoteRepository.Flush();
            Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.Flush();
        }

        public void AtualizarVendaPacote(ConfiguracaoExibicaoEVendaDTO dto, int idPessoaQueAlterou)
        {
            foreach (var item in dto.Pacotes)
            {
                var pacote = Domain.Pacotes.PacoteRepository.Load(item.Id);
                var valorAtual = pacote.VendaHotsite;
                var novoValor = item.PodeVenderPacoteHotsite;

                if (valorAtual != novoValor)
                {
                    pacote.VendaHotsite = item.PodeVenderPacoteHotsite;
                    Domain.Pacotes.PacoteRepository.UpdateNoFlush(pacote);

                    var descricao = pacote.VendaHotsite ? ("O pacote " + "'" + pacote.Nome + "'" + " será vendido no site") : ("O pacote " + "'" + pacote.Nome + "'" + " não será vendido no site");

                    var historicoConfig = new HistoricoConfiguracoesPacote(pacote, idPessoaQueAlterou, TipoConfiguracaoPacoteEnum.VendaNoSite, descricao);
                    Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.SaveNewNoFlush(historicoConfig);
                }
            }
            Domain.Pacotes.PacoteRepository.Flush();
            Domain.Pacotes.HistoricoConfiguracoesPacoteRepository.Flush();
        }

        private bool ValidarVendaDePacoteHotsite(int IdEstabelecimento)
        {

            var estabelecimentoEstaHabilitado = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.
               VerificarSeEstabelecimentoUsaFormaPagamento(IdEstabelecimento, FormaPagamentoEnum.PagamentoOnlineHotsite);

            return estabelecimentoEstaHabilitado;
        }

        public DadosDePacotesParaVendaHotsiteDTO ObterPacoteParaVendaHotsite(int idPacote, int idEstabelecimento)
        {
            var pacoteDTO = Domain.Pacotes.PacoteRepository.ObterPacoteParaVendaHotsite(idPacote, idEstabelecimento);
            pacoteDTO.ItensDoPacote = Domain.Pacotes.PacoteRepository.ObterItensDoPacote(idPacote);

            if (pacoteDTO.IdPacote <= 1)
            {
                return null;
            }

            var EhValido = pacoteDTO.VendaHotsite && ValidarVendaDePacoteHotsite(pacoteDTO.IdEstabelecimento);

            if (!EhValido)
            {
                return null;
            }

            pacoteDTO.PagamentoHotsite.AceitaParcelamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.
                EstabelecimentoAceitaParcelamento(pacoteDTO.IdEstabelecimento, (int)FormaPagamentoEnum.PagamentoOnlineHotsite);

            pacoteDTO.PagamentoHotsite.NumeroParcelas = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.
                ObterNumeroDeParcelaMaximo(pacoteDTO.IdEstabelecimento, (int)FormaPagamentoEnum.PagamentoOnlineHotsite);

            return pacoteDTO;
        }

        public void RemoverTodosPacotesDaVendaHotsite(int idEstabelecimento)
        {
            var pacotesAtivosAVenda = Domain.Pacotes.PacoteRepository.ListarPacotesAtivosVenderSite(idEstabelecimento);

            foreach (var pacote in pacotesAtivosAVenda)
            {
                pacote.VendaHotsite = false;

                Domain.Pacotes.PacoteRepository.UpdateNoFlush(pacote);
            }

            Domain.Pacotes.PacoteRepository.Flush();
        }

        public Pacote CriarPacotePersonalizado(PacotePersonalizadoDTO dto)
        {
            try
            {
                SetLogCorrelationId(dto.Estabelecimento.IdEstabelecimento.ToString());
                LogWithCallerName($"Criação de pacote personalizado com valores: {dto}");

                var pacote = PersonalizarPacoteComItens(new Pacote(), dto);

                _pacoteRepository.SaveNew(pacote);
                LogWithCallerName($"Pacote personalizado criado com sucesso! Id: {pacote.Id}");

                return pacote;
            }
            catch (Exception ex)
            {
                if (string.IsNullOrEmpty(_validationHelper.Mensagens))
                    _validationHelper.AdicionarItemValidacao("Erro ao criar pacote personalizado");

                LogWithCallerName($"Erro ao criar pacote personalizado: {ex.Message}");
                return null;
            }
        }

        public Pacote EditarPacotePersonalizado(EditarPacotePersonalizadoDTO dto)
        {
            var idPacote = dto.IdPacote;
            try
            {
                SetLogCorrelationId(dto.Estabelecimento.IdEstabelecimento.ToString());
                LogWithCallerName($"Edição de pacote personalizado de Id {idPacote} com valores: {dto}");

                var pacote = _pacoteRepository.Load(idPacote);

                if (pacote.Estabelecimento.IdEstabelecimento != dto.Estabelecimento.IdEstabelecimento)
                {
                    _validationHelper.AdicionarItemValidacao("O pacote indicado não pertence a este estabelecimento");
                    return null;
                }

                pacote = PersonalizarPacoteComItens(pacote, dto);

                LogWithCallerName($"Pacote personalizado editado com sucesso! Id: {pacote.Id}");

                return pacote;
            }
            catch (Exception ex)
            {
                if (string.IsNullOrEmpty(_validationHelper.Mensagens))
                    _validationHelper.AdicionarItemValidacao("Erro ao criar pacote personalizado");

                LogWithCallerName($"Erro ao editar pacote personalizado: {ex.Message}");
                return null;
            }
        }

        private Pacote PersonalizarPacoteComItens(Pacote pacote, PacotePersonalizadoDTO dto)
        {
            var configuracaoPacotePersonalizado =
                _configuracaoPacotePersonalizadoRepository.ObterPorIdEstabelecimento(dto
                    .Estabelecimento.IdEstabelecimento);

            if (configuracaoPacotePersonalizado != null)
            {
                dto.ValidadeDeUsoEmMeses = configuracaoPacotePersonalizado.ValidadeDeUsoEmMeses;
                dto.PercentualComissaoProfissional = configuracaoPacotePersonalizado.PercentualComissaoProfissional;

                LogWithCallerName($"Configuração de pacote personalizado obtida com valores: {configuracaoPacotePersonalizado}");
            }
            else
            {
                LogWithCallerName($"Configuração de pacote personalizado não encontrada para o estabelecimento.");
            }

            pacote.Personalizar(dto);
            pacote = AtualizarItensDoPacotePersonalizado(pacote, dto.ItensPacote);
            ValidarPacote(pacote);

            return pacote;
        }

        private Pacote AtualizarItensDoPacotePersonalizado(Pacote pacote, List<ItemPacoteDTO> dtos)
        {
            pacote.LimparItens();

            var novosItens = ConstruirItensDoPacote(pacote, dtos);

            foreach (var item in novosItens)
            {
                pacote.AdicionarItem(item);
            }

            return pacote;
        }

        private List<ItemPacote> ConstruirItensDoPacote(Pacote pacote, List<ItemPacoteDTO> dtos)
            => dtos.ConvertAll(dto => ConstruirItemDoPacote(pacote, dto));

        private ItemPacote ConstruirItemDoPacote(Pacote pacote, ItemPacoteDTO dto)
        {
            ItemPacote itemPacote;

            switch (dto.TipoItemPacote)
            {
                case TipoItemPacote.Produto:
                    itemPacote = new ItemPacoteProduto();
                    ((ItemPacoteProduto)itemPacote).EstabelecimentoProduto = _estabelecimentoProdutoRepository.ObterPorId(dto.IdReferenciaItem, pacote.Estabelecimento.IdEstabelecimento);
                    break;
                case TipoItemPacote.Servico:
                    itemPacote = new ItemPacoteServico();
                    ((ItemPacoteServico)itemPacote).ServicoEstabelecimento = _servicoEstabelecimentoRepository.ObterPorId(dto.IdReferenciaItem);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            itemPacote.Pacote = pacote;
            itemPacote.ValorUnitario = dto.ValorUnitario;
            itemPacote.Quantidade = dto.Quantidade;
            itemPacote.ValorUnitarioFiscal = dto.ValorUnitario;

            return itemPacote;
        }

        public string GetLogCorrelationId()
            => HttpContext.Current.Items["LogCorrelationId"]?.ToString();

        private static void SetLogCorrelationId(string baseName)
            => HttpContext.Current.Items["LogCorrelationId"] = $"{baseName}_{Guid.NewGuid()}";

        private void LogWithCallerName(string message, [CallerMemberName] string callerName = null)
            => _loggerPacotePersonalizado.Log(GetLogCorrelationId(), $"({callerName}) {message}");

    }
}