﻿using Castle.ActiveRecord;
using NHibernate.Linq;
using NHibernate.Transform;
using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pacotes.Adapters;
using Perlink.Trinks.Pacotes.DTO;
using Perlink.Trinks.Pacotes.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pacotes.Services
{
    public class UtilizacaoDePacotesService : BaseService, IUtilizacaoDePacotesService
    {
        public IEnumerable<PacoteCliente> ListarPacotesComSaldo(Estabelecimento estabelecimento, ClienteEstabelecimento clienteEstabelecimento)
        {

            var clienteNoEstabelecimentoQuery = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();
            var pacotesClienteQuery = Domain.Pacotes.PacoteClienteRepository.Queryable();

            var pacotesCliente = (from pacli in pacotesClienteQuery
                                  where pacli.Ativo
                                  && pacli.Cliente == clienteEstabelecimento.Cliente
                                  && pacli.PacoteOriginal.Estabelecimento == estabelecimento
                                  && pacli.ItensPacoteCliente.Any(p => p.QuantidadeConsumida < p.Quantidade)
                                  select pacli).ToList();

            if (estabelecimento.EhUmEstabelecimentoFranqueadoAtivo() && estabelecimento.FranquiaEstabelecimento.ExistePacoteDaRedeDisponivelParaConsumo)
            {

                var cpf = clienteEstabelecimento.Cliente.PessoaFisica.Cpf;
                var email = clienteEstabelecimento.Cliente.PessoaFisica.Email;
                var clientePossuiCpfOuEmail = !string.IsNullOrWhiteSpace(cpf) || !string.IsNullOrWhiteSpace(email);

                if (clientePossuiCpfOuEmail)
                {
                    var idsClientesEstabelecimento = new List<int>();

                    var clientesMesmoCPF = Domain.Pessoas.ClienteEstabelecimentoRepository.ListarBuscaCPFNaRede(estabelecimento.FranquiaEstabelecimento.Franquia.Id, cpf, true);
                    if (clientesMesmoCPF != null)
                        idsClientesEstabelecimento.AddRange(clientesMesmoCPF.Select(c => c.IdClienteEstabelecimento));

                    var clientesMesmoEmail = Domain.Pessoas.ClienteEstabelecimentoRepository.ListarBuscaEmailNaRede(estabelecimento.FranquiaEstabelecimento.Franquia.Id, email, true);
                    if (clientesMesmoEmail != null)
                        idsClientesEstabelecimento.AddRange(clientesMesmoEmail.Select(c => c.IdClienteEstabelecimento));

                    idsClientesEstabelecimento = idsClientesEstabelecimento.Distinct().ToList();

                    var clienteNaRedeQuery = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                        .Where(ce => idsClientesEstabelecimento.Contains(ce.Codigo));

                    var pacotesDoClienteEmOutrasUnidades = (from ce in clienteNaRedeQuery
                                                            join pacli in pacotesClienteQuery on new { ce.Cliente, ce.Estabelecimento } equals new { pacli.Cliente, pacli.PacoteOriginal.Estabelecimento }
                                                            where pacli.Ativo
                                                            && pacli.ItensPacoteCliente.Any(p => p.QuantidadeConsumida < p.Quantidade)
                                                            && pacli.PacoteOriginal.CompartilhadoNaRede
                                                            && pacli.PacoteOriginal.Estabelecimento != estabelecimento
                                                            select pacli).ToList();

                    if (pacotesDoClienteEmOutrasUnidades.Any())
                    {

                        var compartilhamentos = Domain.Pessoas.CompartilhamentoNaRedeRepository.Queryable();
                        var franquiaEstabelecimentos = Domain.Pessoas.FranquiaEstabelecimentoRepository.Queryable();

                        var liberadosParaTodaFranquia = (from fe in franquiaEstabelecimentos
                                                         where fe.Franquia.Id == estabelecimento.FranquiaEstabelecimento.Franquia.Id
                                                            && fe.TipoCompartilhamentoNaRede == TipoCompartilhaNaRedeEnum.QualquerUnidade
                                                         select fe.IdEstabelecimento).ToList();

                        var liberadosParaEstaUnidade = (from fe in franquiaEstabelecimentos
                                                        join cp in compartilhamentos on fe.IdEstabelecimento equals cp.IdEstabelecimentoOrigem
                                                        where cp.IdEstabelecimentoDestino == estabelecimento.IdEstabelecimento
                                                           && fe.Franquia.Id == estabelecimento.FranquiaEstabelecimento.Franquia.Id
                                                           && fe.TipoCompartilhamentoNaRede == TipoCompartilhaNaRedeEnum.Personalizado
                                                        select cp.IdEstabelecimentoOrigem).ToList();

                        pacotesDoClienteEmOutrasUnidades.RemoveAll(x =>
                            !liberadosParaTodaFranquia.Contains(x.PacoteOriginal.Estabelecimento.IdEstabelecimento) &&
                            !liberadosParaEstaUnidade.Contains(x.PacoteOriginal.Estabelecimento.IdEstabelecimento)
                        );

                        pacotesCliente.AddRange(pacotesDoClienteEmOutrasUnidades);
                    }
                }
            }

            return pacotesCliente;
        }

        public bool EstabelecimentoPodeExibirCompartilhamentoDePacotes(Estabelecimento estabelecimento)
        {
            var estabelecimentoEhFranqueado = estabelecimento.EhUmEstabelecimentoFranqueadoAtivo();
            var estabelecimentoEhBaseadoEmModelo = estabelecimento.EstabelecimentoEhBaseadoEmUmModelo();
            var modeloPermiteCompartilharPacotes = estabelecimentoEhBaseadoEmModelo ?
                Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.PodeCompartilharPacotesNaRede(estabelecimento.IdEstabelecimento)
                : false;

            if (estabelecimentoEhFranqueado && !estabelecimentoEhBaseadoEmModelo)
                return true;

            else if (estabelecimentoEhFranqueado && estabelecimentoEhBaseadoEmModelo)
                return modeloPermiteCompartilharPacotes;

            else return false;
        }

        public List<DadosPacoteClienteDTO> ListarPacotesComSaldo(int idClienteEstabelecimento, TipoItemPacote? tipoItemPacote)
        {
            var pacotesClienteQuery = Domain.Pacotes.PacoteClienteRepository.Queryable().Fetch(pacote => pacote.ItensPacoteCliente);
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            var estabelecimento = clienteEstabelecimento.Estabelecimento;

            var pacotesCliente = pacotesClienteQuery.Where(pacli => pacli.Ativo
                                  && pacli.Cliente == clienteEstabelecimento.Cliente
                                  && pacli.PacoteOriginal.Estabelecimento == estabelecimento
                                  && pacli.ItensPacoteCliente.Any(p => p.QuantidadeConsumida < p.Quantidade))
                                 .ToList();

            var itensPacote = Domain.Pacotes.ItemPacoteClienteRepository
                .ConsultarConsumoDeItensDosPacotesDoCliente(clienteEstabelecimento.Cliente.IdCliente, estabelecimento.IdEstabelecimento);

            var itensPacoteCliente = Domain.Pacotes.DadosDeConsumoDePacoteService.ObterDadosEConsumoDeItensDosPacotes(itensPacote);

            var resultado = pacotesCliente.ToListDadosPacoteClienteDTO(estabelecimento.IdEstabelecimento, itensPacoteCliente);

            if (tipoItemPacote.HasValue)
            {
                var removerPacotesCliente = new List<DadosPacoteClienteDTO>();

                foreach (var item in resultado)
                {
                    item.ItensPacote = item.ItensPacote.Where(itPac => itPac.TipoItemPacote == (int)tipoItemPacote).ToList();

                    if (item.ItensPacote.Count == 0)
                    {
                        removerPacotesCliente.Add(item);
                    }
                }

                foreach (var item in removerPacotesCliente)
                {
                    resultado.Remove(item);
                }
            }

            return resultado;
        }
    }
}
