﻿using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Disponibilidade
{

    public class ParametrosBusca
    {

        public ParametrosBusca()
        {
            Estabelecimentos = new List<int>();
            ServicosEstabelecimento = new List<int>();
            Profissionais = new List<int>();
            ServicosParaBackoffice = false;
        }

        public List<int> Estabelecimentos { get; set; }
        public List<int> ServicosEstabelecimento { get; set; }
        public List<int> Profissionais { get; set; }
        public DateTime? DataInicial { get; set; }
        public DateTime? DataFinal { get; set; }
        public Boolean ServicosParaBackoffice { get; set; }
        public bool ParaGoogleReserve { get; set; }
        public DateTime? DataAtual { get; set; }
        public bool DefinirMarcos { get; set; }
    }
}