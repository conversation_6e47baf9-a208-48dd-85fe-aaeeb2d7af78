﻿using Castle.ActiveRecord;
using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.Pagamentos;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.LinksDePagamento
{

    [ActiveRecord("Link_Pagamento", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class LinkDePagamento : ActiveRecordBase<LinkDePagamento>
    {

        public LinkDePagamento()
        {
            Ativo = true;
            DataCriacao = Calendario.Agora();
            Identificador = Guid.NewGuid().ToString("N");
            StatusPagamento = StatusPagamentoEnum.AguardandoConfirmacaoDePagamento;

            int quantidadeDeMinutosPadraoDeValidade = new ParametrosTrinks<int>(ParametrosTrinksEnum.linkDePagamento_minutos_de_validade).ObterValor();
            DataDeValidade = Calendario.Agora().AddMinutes(quantidadeDeMinutosPadraoDeValidade);
            DataQueFoiAdicionadoNaFilaDeProcessamentoDoPagamento = null;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_link_pagamento")]
        public virtual int IdLinkDePagamento { get; set; }

        [Property("identificador", Length = 36, NotNull = true)]
        public virtual string Identificador { get; set; }

        [Property("id_comprador")]
        public virtual int IdComprador { get; set; }

        [Property("id_pagamento")]
        public virtual int? IdPagamento { get; set; }

        [Property("nome_comprador", NotNull = true)]
        public virtual string NomeComprador { get; set; }

        [Property("email_comprador")]
        public virtual string EmailComprador { get; set; }

        [Property("id_recebedor")]
        public virtual int IdRecebedor { get; set; }

        [Property("nome_recebedor", NotNull = true)]
        public virtual string NomeRecebedor { get; set; }

        [Property("valor")]
        public virtual decimal Valor { get; set; }

        [Property("status_pagamento")]
        public virtual StatusPagamentoEnum StatusPagamento { get; set; }

        [Property("dt_criacao")]
        public virtual DateTime DataCriacao { get; set; }

        [Property("dt_ultima_atualizacao")]
        public virtual DateTime? DataUltimaAtualizacao { get; set; }

        [Property("dt_pagamento")]
        public virtual DateTime? DataPagamento { get; set; }

        [Property("dt_primeiro_acesso")]
        public virtual DateTime? DataPrimeiroAcesso { get; set; }

        [Property("dt_validade")]
        public virtual DateTime DataDeValidade { get; set; }

        [Property("ativo")]
        public virtual bool Ativo { get; set; }

        [HasMany(typeof(ItemLinkDePagamento), ColumnKey = "id_link_pagamento", Cascade = ManyRelationCascadeEnum.AllDeleteOrphan,
            Table = "Link_Pagamento_Item", Inverse = true, Lazy = true)]
        public virtual IList<ItemLinkDePagamento> Itens { get; set; }

        [Property("data_que_foi_adicionado_na_fila_de_processamento")]
        public virtual DateTime? DataQueFoiAdicionadoNaFilaDeProcessamentoDoPagamento { get; set; }
        
        [Property("url_validacao")]
        public virtual string UrlValidacao { get; set; }

        public virtual void IndicarQueFoiPagoComSucesso(Pagamento pagamento)
        {
            IdPagamento = pagamento.IdPagamento;
            StatusPagamento = StatusPagamentoEnum.Pago;
            DataPagamento = pagamento.DataPagamento;
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual void IndicarQueFoiPagoComSucesso(NovoPagamentoNoGateway pagamento)
        {
            IdPagamento = pagamento.IdPagamento;
            StatusPagamento = StatusPagamentoEnum.Pago;
            DataPagamento = pagamento.DataPagamento;
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual void IndicarQueTeveProblemasNoPagamento()
        {
            StatusPagamento = StatusPagamentoEnum.Negado;
            DataPagamento = null;
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual void IndicarQueFoiCancelado()
        {
            StatusPagamento = StatusPagamentoEnum.Cancelado;
            DataUltimaAtualizacao = Calendario.Agora();
        }

        public virtual void RenovarValidadeDoLink()
        {
            int quantidadeDeMinutosPadraoDeValidade = new ParametrosTrinks<int>(ParametrosTrinksEnum.linkDePagamento_minutos_de_validade).ObterValor();
            DataDeValidade = Calendario.Agora().AddMinutes(quantidadeDeMinutosPadraoDeValidade);
        }

        private const string RotaDeCompartilhamento = "share-link";
        private const string RotaDePagamento = "payment-resume";

        public virtual string ObterUrlDeCompartilhamento()
        {
            return GerarLink(RotaDeCompartilhamento);
        }

        public virtual string ObterUrlDePagamento()
        {
            return GerarLink(RotaDePagamento);
        }

        private string GerarLink(string rota)
        {
            return ConfiguracoesTrinks.LinkDePagamento.UrlBase
                .Append($"{rota}?guid={Identificador}")
                .ToString();
        }
    }
}