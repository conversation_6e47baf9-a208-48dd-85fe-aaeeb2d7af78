﻿using NHibernate.Linq;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies;
using Perlink.Trinks.Pagamentos.DTO;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using Perlink.Trinks.Pagamentos;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.PagamentosOnlineNoTrinks;
using Perlink.Pagamentos.Gateways;
using Perlink.Trinks.Pagamentos.Calculos;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Pagamentos.Providers;
using Elmah;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;

namespace Perlink.Trinks.LinksDePagamento.Services
{
    public class LinkDePagamentoService : BaseService, ILinkDePagamentoService
    {

        public LinkDePagamento GerarNovoLinkDePagamento(NovoLinkDePagamentoDTO novoLinkDePagamento)
        {
            var link = new LinkDePagamento()
            {
                IdRecebedor = novoLinkDePagamento.IdRecebedor,
                IdComprador = novoLinkDePagamento.IdComprador,
                Valor = novoLinkDePagamento.Valor,
                NomeComprador = novoLinkDePagamento.NomeComprador,
                EmailComprador = novoLinkDePagamento.EmailComprador,
                NomeRecebedor = novoLinkDePagamento.NomeRecebedor,
                UrlValidacao = novoLinkDePagamento.UrlValidacao,
            };

            link.Itens = novoLinkDePagamento.Itens.Select(i =>
                new ItemLinkDePagamento()
                {
                    Nome = i.Key,
                    Valor = i.Value,
                    LinkDePagamento = link
                }).ToList();

            Domain.LinksDePagamento.LinkDePagamentoRepository.SaveNew(link);

            return link;
        }

        public LinkDePagamento ObterLinkSeEstiverAguardandoPagamento(string identificador)
        {
            var agora = Calendario.Agora();
            return Domain.LinksDePagamento.LinkDePagamentoRepository.Queryable()
                .Fetch(lp => lp.Itens)
                .FirstOrDefault(f =>
                    f.Identificador == identificador &&
                    f.DataDeValidade > agora &&
                    f.StatusPagamento == StatusPagamentoEnum.AguardandoConfirmacaoDePagamento);
        }

        [TransactionInitRequired]
        public async Task<ResultadoDoPagamentoDto> RealizarPagamentoDoLink(LinkDePagamento link, RealizarPagamentoCartaoDeCreditoDto dto)
        {
            var cartao = dto.Cartao;
            var comprador = dto.Comprador;
            if (link.StatusPagamento != StatusPagamentoEnum.AguardandoConfirmacaoDePagamento)
                throw new Exception("Link de pagamento já foi pago!");

            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(link.IdRecebedor);
            var dadosDoPagamento = ConstruirDtoDePagamento(link, estabelecimentoRecebedor, comprador, MetodoDePagamentoNoGatewayEnum.CartaoDeCredito, cartao);
            var resultadoDoPagamento = await Domain.Pagamentos.PagamentosApplicationService
              .RealizarPagamento(dadosDoPagamento);

            if (resultadoDoPagamento.Falha)
            {
                var ex = new BusinessException($"Falha no pagamento. {resultadoDoPagamento.Erro}");
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                throw new Exception("Falha no pagamento");
            }

            if (resultadoDoPagamento.Valor.PagamentoFoiRealizado)
            {

                link.IndicarQueFoiPagoComSucesso(resultadoDoPagamento.Valor);
                AdicionarAFilaDeProcessamento(link, resultadoDoPagamento.Valor.IdPagamento);
                DispararEmailComprovantePagamento(link, estabelecimentoRecebedor, comprador, cartao.QuantidadeParcelas);

            }
            else
            {
                link.IndicarQueTeveProblemasNoPagamento();
            }

            Domain.LinksDePagamento.LinkDePagamentoRepository.Update(link);

            return new ResultadoDoPagamentoDto(link.StatusPagamento);
        }


        [TransactionInitRequired]
        public async Task<PixCriadoDto> CriarPagamentoPixNoLink(LinkDePagamento link, IniciarPagamentoComPixDto dto)
        {
            var comprador = dto.Comprador;
            if (link.StatusPagamento != StatusPagamentoEnum.AguardandoConfirmacaoDePagamento)
                throw new Exception("Link de pagamento já foi pago!");

            var estabelecimentoRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(link.IdRecebedor);
            var dadosDoPagamento = ConstruirDtoDePagamento(link, estabelecimentoRecebedor, comprador, MetodoDePagamentoNoGatewayEnum.Pix);
            var resultadoDoPagamento = await Domain.Pagamentos.PagamentosApplicationService
              .CriarPedidoPix(new NovoPedidoPixDTO() 
              {
                  DadosDoPagamento = dadosDoPagamento,
                  TempoDeExpiracao = ObterTempoDeExpiracaoPixEmSegundos(),
              });

            if (resultadoDoPagamento.Falha)
                throw new Exception("Falha no pagamento");

            return new PixCriadoDto()
            {
                LinkPix = resultadoDoPagamento.Valor.RetornoPixDTO.LinkPix,
                UrlQrCode = resultadoDoPagamento.Valor.RetornoPixDTO.UrlQrCode,
                IdTransacaoNoGateway = resultadoDoPagamento.Valor.RetornoPixDTO.IdTransacaoNoGateway,
                IdCobrancaNoGateway = resultadoDoPagamento.Valor.RetornoPixDTO.IdCobrancaNoGateway,
            };
        }

        private NovoPagamentoDTO ConstruirDtoDePagamento(LinkDePagamento link, EstabelecimentoRecebedor estabelecimentoRecebedor, CompradorDTO comprador, 
            MetodoDePagamentoNoGatewayEnum metodoDePagamento, DadosDoCartaoDTO cartao = null)
        {
            var splitsDoPagamentoOnline =
                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.CalcularSplitDoPagamentoOnline(estabelecimentoRecebedor,
                    link.IdComprador, link.Valor, metodoDePagamento, out var _, out var _, cartao?.QuantidadeParcelas ?? 1);

            var enderecoDeCobranca = comprador.Endereco;
            return new NovoPagamentoDTO
            {
                MetodoDePagamento = cartao != null ? MetodoDePagamentoNoGatewayEnum.CartaoDeCredito : MetodoDePagamentoNoGatewayEnum.Pix,
                OrigemDePagamento = Pagamentos.Enums.OrigemDePagamentoEnum.LinkDePagamento,
                ReferenciaDeOrigem = ObterReferenciaDeOrigemParaLink(link),
                IdRecebedor = estabelecimentoRecebedor.IdRecebedor,
                Valor = link.Valor,
                QuantidadeParcelas = cartao?.QuantidadeParcelas,
                Itens = link.Itens.Select(x => new ItemNovoPagamentoDTO()
                {
                    Nome = x.Nome,
                    Valor = x.Valor,
                }).ToList(),
                RegrasDeSplit = splitsDoPagamentoOnline,
                Comprador = new DadosDoCompradorDTO
                {
                    IdComprador = link.IdComprador,
                    Nome = comprador.Nome,
                    CpfCnpj = comprador.CpfCnpj,
                    Email = comprador.Email,
                    Telefone = comprador.Telefone,
                },
                EnderecoDeCobranca = new EnderecoDeCobrancaDTO
                {
                    Logradouro = enderecoDeCobranca.Logradouro,
                    Cidade = enderecoDeCobranca.Cidade,
                    Numero = enderecoDeCobranca.Numero,
                    Bairro = enderecoDeCobranca.Bairro,
                    CEP = enderecoDeCobranca.CEP,
                    Estado = enderecoDeCobranca.Estado,
                    Complemento = enderecoDeCobranca.Complemento,
                },
                Cartao = cartao == null? null : new CartaoDTO
                {
                    Nome = cartao.Nome,
                    Numero = cartao.Numero,
                    Validade = cartao.Validade,
                    CVV = cartao.CVV,
                },
            };
        }

        public async Task AtualizarStatusDePagamentoAsync(AtualizarStatusDePagamentoPorLinkDTO dto)
        {
            var pagamento = ObterPagamento(dto.IdDaCobranca);

            if (pagamento == null)
            {
                return;
            }

            RegistrarMudancaDeStatusDoPagamento(pagamento, dto);
            Domain.Pagamentos.PagamentoRepository.Update(pagamento);

            await AtualizarLinkAssociadoSeHouver(pagamento, dto);
        }

        public async Task AtualizarLinkAssociadoSeHouver(Pagamento pagamento, AtualizarStatusDePagamentoPorLinkDTO dto)
        {
            if (string.IsNullOrEmpty(pagamento.ReferenciaDeOrigem))
            {
                return;
            }

            if (!TryParseOrigemDoPagamento(pagamento.ReferenciaDeOrigem, out var identificadorLink))
            {
                LogService<LinkDePagamentoService>.Info($"Referência de origem do pagamento de ID {pagamento.IdPagamento} não é um link de pagamento. Ignorando atualização do link associado.");
                return;
            }

            var link = Domain.LinksDePagamento.LinkDePagamentoRepository.ObterLinkPorIdentificador(identificadorLink);

            if (link == null)
            {
                ErrorLog.GetDefault(null).Log(new Error(new BusinessException($"Foi recebido webhook para o pagamento {pagamento.IdPagamento}, porém o link {identificadorLink} associado não foi encontrado.")));
                return;
            }

            var linkJaHaviaSidoPagoEmOutraTransacao = link.StatusPagamento == StatusPagamentoEnum.Pago && link.IdPagamento != pagamento.IdPagamento;
            if (linkJaHaviaSidoPagoEmOutraTransacao && pagamento.FoiPago())
            {
                await EstornarPagamentoDeLinkAnteriormenteRealizadoAsync(pagamento, link.IdLinkDePagamento);
                return;
            }

            if (pagamento.FoiPago())
            {
                var estabelecimentoRecebedor = ObterEstabelecimentoRecebedor(link.IdRecebedor);
                link.IndicarQueFoiPagoComSucesso(pagamento);
                AdicionarAFilaDeProcessamento(link, pagamento.IdPagamento);
                DispararEmailComprovantePagamento(link, estabelecimentoRecebedor, dto.DadosParaEmailDeComprovante, quantidadeParcela: 1);
            }
            else
            {
                link.IndicarQueTeveProblemasNoPagamento();
            }

            Domain.LinksDePagamento.LinkDePagamentoRepository.Update(link);
        }

        private async Task EstornarPagamentoDeLinkAnteriormenteRealizadoAsync(Pagamento pagamento, int idLink)
        {
            LogService<LinkDePagamentoService>.Info($"O link de ID {idLink} já havia sido pago. Procedendo para estornar um novo pagamento de ID {pagamento.IdPagamento} recebido");

            try
            {
                var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Load(pagamento.IdRecebedorCredenciado);
                var gatewayService = GatewayPagamentoOnlineFactory.Create(recebedorCredenciado.Gateway.AsEnum(), new PagamentoOnlineConfigurationProvider());
                var retornoDoGateway = await gatewayService.EstornarTransacao(new Perlink.Pagamentos.Gateways.DTO.EstornarPagamentoDTO()
                {
                    IdCobrancaNoGateway = pagamento.IdCobrancaGateway,
                    IdTransacaoNoGateway = pagamento.IdTransacaoGateway,
                    IdDoRecebedorNoGateway = recebedorCredenciado.IdRecebedorNoGateway,
                    ValorTotalDoPagamento = pagamento.Valor,
                });

                if (!retornoDoGateway.Sucesso)
                {
                    ErrorLog.GetDefault(null).Log(new Error(new BusinessException($"Falha ao estornar pagamento de ID {pagamento.IdPagamento}. A requisição ao gateway teve resposta, mas não foi de sucesso - {retornoDoGateway.Erro}")));
                }
                else
                {
                    pagamento.DataUltimaAtualizacao = Calendario.Agora();
                    pagamento.StatusPagamento = Pagamentos.Enums.StatusPagamentoEnum.Estornado;
                    pagamento.StatusGateway = retornoDoGateway.Valor;
                    Domain.Pagamentos.PagamentoRepository.Update(pagamento);
                }
            }
            catch (Exception ex)
            {
                ErrorLog.GetDefault(null).Log(new Error(new BusinessException($"Falha ao estornar pagamento de ID {pagamento.IdPagamento}.", ex)));
                throw;
            }
        }

        private EstabelecimentoRecebedor ObterEstabelecimentoRecebedor(int idRecebedor)
        {
            return Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterAtivoPorEstabelecimento(idRecebedor)
                ?? throw new KeyNotFoundException($"Estabelecimento recebedor com o ID {idRecebedor} não foi encontrado.");
        }

        private Pagamento ObterPagamento(string idTransacaoNoGateway)
            => Domain.Pagamentos.PagamentoRepository
                .ObterPagamentoPorIdCobrancaNoGateway(idTransacaoNoGateway)
                .FirstOrDefault();

        private void DispararEmailComprovantePagamento(
            LinkDePagamento link,
            EstabelecimentoRecebedor estabelecimentoRecebedor,
            DadosCompradorParaEnvioEmailPagamentoDTO dadosComprador,
            int quantidadeParcela)
        {
            try
            {
                var dadosEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterDadosParaEnvioDeEmailVendaHotsitePorIdEstabelecimento(estabelecimentoRecebedor.IdEstabelecimento);
                var origemPagamento = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterOrigemPorIdLinkDoPagamento(link.IdLinkDePagamento);

                var dadosRecebedor = new DadosRecebedorParaEnvioEmailPagamentoDTO(dadosEstabelecimento.IdEstabelecimento, dadosEstabelecimento.NomeExibicaoPortal, dadosEstabelecimento.EmailResponsavelFinanceiro, dadosEstabelecimento.NomeResponsavelFinanceiro);

                var dto = new DadosParaEnvioEmailPagamentoDTO(link, quantidadeParcela, dadosComprador, dadosRecebedor);

                var strategy = LinkNoTrinksFactory.ObterPorOrigem(origemPagamento);
                strategy.EnviarEmailComprovantePagamento(dto);
            }
             catch (Exception ex)
            {
                Elmah.ErrorSignal.FromCurrentContext().Raise(new BusinessException("Erro ao enviar email de confirmação de pagamento " + ex.Message));
            }
        }

        private void DispararEmailComprovantePagamento(LinkDePagamento link, EstabelecimentoRecebedor estabelecimentoRecebedor, CompradorDTO comprador, int quantidadeParcela)
            => DispararEmailComprovantePagamento(
                link,
                estabelecimentoRecebedor,
                new DadosCompradorParaEnvioEmailPagamentoDTO(comprador.Nome, comprador.CpfCnpj, comprador.Email),
                quantidadeParcela);
        
        public void AdicionarAFilaDeProcessamento(LinkDePagamento link, int idPagamento)
        {
            link.DataQueFoiAdicionadoNaFilaDeProcessamentoDoPagamento = Calendario.Agora();
            Domain.LinksDePagamento.LinkDePagamentoRepository.Update(link);
            Domain.LinksDePagamento.DisparoMensagensDeProcessamentoService.IncluirNaFila(link, idPagamento);
        }

        public List<MetodoDePagamentoNoGatewayEnum> ListarTodosMetodosDePagamentoParaEstabelecimento(int idEstabelecimento)
        {
            var gateway = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ObterGatewayAtivoDoEstabelecimento(idEstabelecimento);
            var metodosSuportados = GatewayFuncionalidadesProvider.ObterMetodosDePagamentoSuportadosPeloGateway(gateway);

            if (!metodosSuportados.Contains(MetodoDePagamentoNoGatewayEnum.Pix))
            {
                return metodosSuportados;
            }

            var pixHabilitado = PixEstaHabilitado(idEstabelecimento);

            if (!pixHabilitado)
            {
                metodosSuportados.Remove(MetodoDePagamentoNoGatewayEnum.Pix);
            }

            return metodosSuportados;
        }

        private static bool PixEstaHabilitado(int idEstabelecimento)
            => Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                    .ObterDisponibilidadeDeRecurso(idEstabelecimento, Recurso.HabilitarPixNoLinkDePagamento).EstaDisponivel;

        private int ObterTempoDeExpiracaoPixEmSegundos()
        {
            var config = new ParametrosTrinks<int>(
                    ParametrosTrinksEnum.tempo_de_validade_do_codigo_pix_no_link_de_pagamento
                  ).ObterValor();

            if (config <= 0)
            {
                return 5 * 60; // 5 minutos
            }

            return config;
        }

        #region Métodos de atualização de status de pagamento
        private void RegistrarMudancaDeStatusDoPagamento(Pagamento pagamento, AtualizarStatusDePagamentoPorLinkDTO dto)
        {
            switch (dto.NovoStatusDoPagamento)
            {
                case StatusPagamentoEnum.Pago:
                    pagamento.RegistrarConfirmacacaoDePagamento(dto.DadosNoGateway);
                    IncluirNaFilaDeProcessamentoDeLimite(pagamento);
                    break;

                case StatusPagamentoEnum.Negado:
                case StatusPagamentoEnum.Cancelado:
                    pagamento.RegistrarCancelamento(dto.DadosNoGateway);
                    break;

                default:
                    throw new NotImplementedException($"Status de pagamento {dto.NovoStatusDoPagamento} não implementado.");
            }
        }

        private void IncluirNaFilaDeProcessamentoDeLimite(Pagamento pagamento)
        {
            var valorPago = ConversaoMonetaria.ConverterReaisParaCentavos(pagamento.Valor);
            var dataPagamento = pagamento.DataPagamento ?? Calendario.Agora();
            Domain.Pagamentos.DisparoMensagensConsumoLimitePagamentoService.IncluirNaFila(pagamento.IdRecebedorCredenciado, pagamento.IdPagamento, valorPago, dataPagamento);
        }
        #endregion

        #region String de referência de origem no pagamento
        private static readonly string PrefixoReferenciaOrigem = "link-de-pagamento-";
        private string ObterReferenciaDeOrigemParaLink(LinkDePagamento link) => $"{PrefixoReferenciaOrigem}{link.Identificador}";

        private bool TryParseOrigemDoPagamento(string referencia, out string identificador)
        {
            if (string.IsNullOrWhiteSpace(referencia))
            {
                identificador = null;
                return false;
            }

            if (!referencia.StartsWith(PrefixoReferenciaOrigem))
            {
                identificador = null;
                return false;
            }

            identificador = referencia.Replace(PrefixoReferenciaOrigem, string.Empty);
            return true;
        }
        #endregion
    }
}
