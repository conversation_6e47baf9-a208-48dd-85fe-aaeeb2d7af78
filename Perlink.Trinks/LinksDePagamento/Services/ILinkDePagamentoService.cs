﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamento.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamento.Services
{

    public interface ILinkDePagamentoService : IService
    {
        Task<ResultadoDoPagamentoDto> RealizarPagamentoDoLink(LinkDePagamento link, RealizarPagamentoCartaoDeCreditoDto dadosDoLink);
        Task<PixCriadoDto> CriarPagamentoPixNoLink(LinkDePagamento link, IniciarPagamentoComPixDto dto);
        LinkDePagamento GerarNovoLinkDePagamento(NovoLinkDePagamentoDTO novoLinkDePagamento);
        LinkDePagamento ObterLinkSeEstiverAguardandoPagamento(string identificador);
        Task AtualizarStatusDePagamentoAsync(AtualizarStatusDePagamentoPorLinkDTO dto);
        List<MetodoDePagamentoNoGatewayEnum> ListarTodosMetodosDePagamentoParaEstabelecimento(int idEstabelecimento);
    }
}