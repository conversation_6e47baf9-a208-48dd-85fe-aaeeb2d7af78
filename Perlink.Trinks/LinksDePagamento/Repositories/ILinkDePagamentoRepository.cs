﻿using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;

namespace Perlink.Trinks.LinksDePagamento.Repositories
{
    public partial interface ILinkDePagamentoRepository
    {
        string ObterIdentificador(int idLinkDePagamento);
        LinkDePagamento ObterLinkPorIdentificador(string identificador);
        OrigemDoLinkDePagamentoEnum ObterOrigem(int idLinkDePagamento);
        int ObterIdPagamentoDoLink(int idLink);
        bool LinkDoPagamentoFoiPago(int idLinkDePagamento);
    }
}