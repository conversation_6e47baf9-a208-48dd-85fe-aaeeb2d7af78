﻿using Perlink.Trinks.LinksDePagamentoNoTrinks;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;
using System.Linq;

namespace Perlink.Trinks.LinksDePagamento.Repositories
{
    public partial class LinkDePagamentoRepository : ILinkDePagamentoRepository
    {

        public string ObterIdentificador(int idLinkDePagamento)
        {
            return Queryable()
                    .Where(link => link.IdLinkDePagamento == idLinkDePagamento)
                    .Select(link => link.Identificador)
                    .FirstOrDefault();
        }

        public LinkDePagamento ObterLinkPorIdentificador(string identificador)
            => Queryable()
                .Where(link => link.Identificador == identificador)
                .FirstOrDefault();

        public LinksDePagamento.Enums.StatusPagamentoEnum ObterStatus(string identificador)
            => Queryable()
                .Where(link => link.Identificador == identificador)
                .Select(link => link.StatusPagamento)
                .FirstOrDefault();

        public OrigemDoLinkDePagamentoEnum ObterOrigem(int idLinkDePagamento)
        {
            return Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable()
                .Where(p => p.IdLinkDePagamento == idLinkDePagamento)
                .Select(p => p.OrigemDoLinkDePagamento)
                .FirstOrDefault();
        }

        public int ObterIdPagamentoDoLink(int idLink)
        {
            var idPagamento = Queryable().Where(l => l.IdLinkDePagamento.Equals(idLink)).Select(l => l.IdPagamento).FirstOrDefault();

            return idPagamento ?? 0;
        }

        public bool LinkDoPagamentoFoiPago(int idLinkDePagamento)
        {
            var ehLinkDePagamentoPago = Domain.LinksDePagamento.LinkDePagamentoRepository.Queryable()
                .Any(l => l.IdLinkDePagamento == idLinkDePagamento
                          && l.Ativo
                          && l.StatusPagamento == LinksDePagamento.Enums.StatusPagamentoEnum.Pago);

            return ehLinkDePagamentoPago;
        }
    }
}