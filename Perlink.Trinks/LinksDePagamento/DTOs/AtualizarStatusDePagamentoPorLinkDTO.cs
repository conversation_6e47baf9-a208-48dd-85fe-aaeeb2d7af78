﻿using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using System;

namespace Perlink.Trinks.LinksDePagamento.DTOs
{
    public class DadosDePagamentoNoGateway
    {
        public string IdDaCobranca { get; set; }
        public string Status { get; set; }
        public int ValorEmCentavos { get; set; }
        public DateTime DataDoPagamento { get; set; }
    }

    public class AtualizarStatusDePagamentoPorLinkDTO
    {
        public string IdDaCobranca { get; set; }

        public DadosCompradorParaEnvioEmailPagamentoDTO DadosParaEmailDeComprovante { get; set; }

        public StatusPagamentoEnum NovoStatusDoPagamento { get; set; }

        public DadosDePagamentoNoGateway DadosNoGateway { get; set; }
    }
}
