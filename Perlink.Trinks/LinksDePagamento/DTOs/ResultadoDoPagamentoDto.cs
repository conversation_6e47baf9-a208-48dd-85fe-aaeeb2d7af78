﻿using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.Pagamentos.DTO;

namespace Perlink.Trinks.LinksDePagamento.DTOs
{
    public class ResultadoDoPagamentoDto
    {
        public StatusPagamentoEnum StatusPagamento { get; set; }

        public ResultadoDoPagamentoDto(StatusPagamentoEnum statusPagamentoEnum)
        {
            StatusPagamento = statusPagamentoEnum;
        }
    }

    public class PixCriadoDto
    {
        public string UrlQrCode { get; set; } = string.Empty;
        public string LinkPix { get; set; } = string.Empty;
        public string IdTransacaoNoGateway { get; set ; } = string.Empty;
        public string IdCobrancaNoGateway { get; set; } = string.Empty;
        public PixCriadoDto() { }
    }
}
