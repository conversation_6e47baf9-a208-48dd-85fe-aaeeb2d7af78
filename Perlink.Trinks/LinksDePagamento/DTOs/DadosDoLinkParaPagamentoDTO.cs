﻿using Newtonsoft.Json;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.LinksDePagamento.DTOs
{
    public class DadosDoLinkParaPagamentoDTO
    {
        public DadosDoLinkParaPagamentoDTO(LinkDePagamento link, List<MetodoDePagamentoNoGatewayEnum> metodosDePagamentoDisponiveis)
        {
            Guid = link.Identificador;
            NomeRecebedor = link.NomeRecebedor;
            NomeComprador = link.NomeComprador;
            Valor = link.Valor;
            Status = link.StatusPagamento;
            DataDeValidade = link.DataDeValidade.ToUniversalTime();
            MetodosDePagamentoDisponiveis = new List<MetodoDePagamentoNoGatewayEnum>(metodosDePagamentoDisponiveis);
            IntervaloPollingPixMS =
                new ParametrosTrinks<int>(ParametrosTrinksEnum.linkDePagamento_intervalo_polling_pix_milissegundos).ObterValor();
            UrlValidacao = link.UrlValidacao;
        }

        public string Guid { get; private set; }
        public StatusPagamentoEnum Status { get; private set; }
        public string NomeRecebedor { get; private set; }
        public string NomeComprador { get; private set; }
        public decimal Valor { get; private set; }
        public DateTimeOffset DataDeValidade { get; private set; }
        public List<MetodoDePagamentoNoGatewayEnum> MetodosDePagamentoDisponiveis { get; private set; }
        public List<ItensDoLinkDTO> Itens { get; set; }
        public string TipoDeFormaDePagamento { get; set; }
        public string NomeDaCompra { get; set; }
        public string MensagemDeEnvioParaWhatsApp { get; set; }
        public bool AceitaParcelamento { get; set; }
        public int NumeroParcelas { get; set; }
        public List<ParcelasDTO> Parcelas { get; set; }
        public List<SubItensDoLinkDTO> SubItens { get; set; }
        public int IntervaloPollingPixMS { get; set; }
        public string UrlValidacao { get; set; }
        public ClienteDTO Cliente { get; set; }
    }

    public class ItensDoLinkDTO
    {
        public int IdItemLink { get; set; }
        public string Nome { get; set; }
        public decimal Valor { get; set; }
    }

    public class SubItensDoLinkDTO
    {

        public int IdReferencia { get; set; }
        public string Nome { get; set; }
        public string TextoQuantidade { get; set; }
    }

    public class ParcelasDTO
    {

        public ParcelasDTO() { }

        public ParcelasDTO(int numeroParcela, string textoParcela, decimal valorParcela)
        {
            NumeroParcela = numeroParcela;
            ValorParcela = valorParcela;
            TextoParcela = ObterTextoParcela();
        }

        public int NumeroParcela { get; set; }
        public decimal ValorParcela { get; set; }
        public string TextoParcela { get; set; }

        public string ObterTextoParcela()
        {
            return NumeroParcela == 1 ? $"À vista, por R$ {ValorParcela}" : $"{NumeroParcela}x de R$ {ValorParcela}";
        }
    }
    public class ClienteDTO
    {
        public ClienteDTO(string nome, string telefone, string email)
        {
            Nome = nome;
            Telefone = telefone;
            Email = email;
        }

        public string Nome { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
    }
}
