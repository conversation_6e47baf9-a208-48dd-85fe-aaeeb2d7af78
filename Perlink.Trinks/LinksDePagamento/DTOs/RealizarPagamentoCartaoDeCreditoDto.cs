﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento.Enums;

namespace Perlink.Trinks.LinksDePagamento.DTOs
{
    public class RealizarPagamentoCartaoDeCreditoDto
    {
        public DadosDoCartaoDTO Cartao { get; set; }
        public CompradorDTO Comprador { get; set; }
    }

    public class IniciarPagamentoComPixDto
    { 
        public CompradorDTO Comprador { get; set; }
    }

    public class DadosDoCartaoDTO
    {
        public string CVV { get; set; }
        public string Validade { get; set; }
        public string Nome { get; set; }
        public string Numero { get; set; }
        public int QuantidadeParcelas { get; set; }
        public DadosDoCartaoDTO() { }
    }

    public class CompradorDTO
    {
        public EnderecoDTO Endereco { get; set; }
        public string Nome { get; set; }
        public string Email { get; set; }
        public string Telefone { get; set; }
        public string CpfCnpj { get; set; }
        public CompradorDTO() { }
    }

    public class EnderecoDTO
    {
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string CEP { get; set; }
        public string Estado { get; set; }
        public string Cidade { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public EnderecoDTO() { }
    }
}