﻿using Castle.ActiveRecord;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;

namespace Perlink.Trinks.Pessoas
{

    [ActiveRecord("Cliente_Area_Perlink", Mutable = false)]
    public class ClienteAreaPerlink
    {

        [PrimaryKey(PrimaryKeyType.Native, "ID", ColumnType = "Int32")]
        public Int32 Id { get; set; }

        [Property("ID_CLIENTE", ColumnType = "Int32")]
        public Int32 IdCliente { get; set; }

        [Property("ID_ESTABELECIMENTO_CLIENTE", ColumnType = "Int32")]
        public Int32? IdClienteEstabelecimento { get; set; }

        [Property("CPF_CLIENTE", ColumnType = "String")]
        public String Cpf { get; set; }

        [Property("NOME_COMPLETO_CLIENTE", ColumnType = "String")]
        public String NomeCompleto { get; set; }

        [Property("ID_ESTABELECIMENTO", ColumnType = "Int32")]
        public Int32 IdEstabelecimentoAssociado { get; set; }

        [Property("NOME_FANTASIA_ESTABELECIMENTO", ColumnType = "String")]
        public String NomeFantasiaEstabelecimentoAssociado { get; set; }

        [Property("OBSERVACOES_ESTABELECIMENTO", ColumnType = "String")]
        public String ObservacoesEstabelecimentoAssociado { get; set; }

        [Property("RAMAL_TELEFONE_CLIENTE", ColumnType = "String")]
        public String RamalTelefone { get; set; }

        [Property("DDI_TELEFONE_CLIENTE", ColumnType = "String")]
        public String DDITelefone { get; set; }

        [Property("DDD_TELEFONE_CLIENTE", ColumnType = "String")]
        public String DDDTelefone { get; set; }

        [Property("NUMERO_TELEFONE_CLIENTE", ColumnType = "String")]
        public String NumeroTelefone { get; set; }

        [Property("SEXO_CLIENTE", ColumnType = "String")]
        public String Sexo { get; set; }

        [Property("EMAIL_CLIENTE", ColumnType = "String")]
        public String Email { get; set; }

        [Property("DATA_NASCIMENTO_CLIENTE", ColumnType = "DateTime")]
        public DateTime? DataNascimento { get; set; }

        [Property("TIPO_CLIENTE", ColumnType = "Int32")]
        public Int32 CodigoTipoCliente { get; set; }

        [Property("TOTAL_AGENDAMENTOS_BALCAO", ColumnType = "Int32")]
        public Int32 TotalAgendamentosBalcao { get; set; }

        [Property("TOTAL_AGENDAMENTOS_WEB", ColumnType = "Int32")]
        public Int32 TotalAgendamentosWeb { get; set; }

        [Property("IND_CLIENTE_CADASTRO_INCOMPLETO", ColumnType = "Boolean")]
        public Boolean EstaComCadastroIncompleto { get; set; }

        [Property("IND_CLIENTE_PROFISSIONAL_ESTABELECIMENTO", ColumnType = "Boolean")]
        public Boolean EhProfissionalDoEstabelecimento { get; set; }

        [Property("CLIENTE_POSSUI_VINCULO_FACEBOOK", ColumnType = "Boolean")]
        public Boolean PossuiVinculoComFacebook { get; set; }

        [Property("ID_CLIENTE_ACESSO_BACKOFFICE", ColumnType = "Int32")]
        public Int32? IdClienteNivelAcessoBackOffice { get; set; }

        [Property("ID_ESTABELECIMENTO_CLIENTE_ACESSO_BACKOFFICE", ColumnType = "Int32")]
        public Int32? IdClienteEstabelecimentoNivelAcessoBackOffice { get; set; }

        [Property("RESPONSAVEL_PELO_ESTABELECIMENTO_ACESSO_BACKOFFICE", ColumnType = "Int32")]
        public Int32? ResponsavelEstabelecimentoNivelAcessoBackOffice { get; set; }

        [Property("ID_PESSOA_FISICA_ACESSO_BACKOFFICE", ColumnType = "Int32")]
        public Int32? IdPessoaFisicaNivelAcessoBackOffice { get; set; }

        [Property("DATA_ULTIMO_LOGIN", ColumnType = "DateTime")]
        public DateTime DataDeUltimoLogin { get; set; }

        [Property("CONTA_ATIVA")]
        public bool ContaAtiva { get; set; }

        [Property("PODE_AGENDAR_ONLINE_ESTABELECIMENTO", ColumnType = "Boolean")]
        public Boolean PodeAgendarOnlineEmEstabelecimento { get; set; }

        public TipoClienteEnum TipoCliente
        {

            get { return (TipoClienteEnum)CodigoTipoCliente; }
            set { throw new NotImplementedException(); }

        }

        public string TelefoneTextoFormatado
        {

            get
            {
                if (!string.IsNullOrEmpty(DDITelefone) && !string.IsNullOrEmpty(DDDTelefone) && !string.IsNullOrEmpty(NumeroTelefone))
                {
                    var telefoneCompleto = $"{DDITelefone}{DDDTelefone}{NumeroTelefone}".ToTextoFormatado();

                    if (!string.IsNullOrEmpty(RamalTelefone))
                    {
                        telefoneCompleto += $" R.{RamalTelefone}";
                    }

                    return telefoneCompleto;
                }

                return "-";
            }

            set { throw new NotImplementedException(); }

        }
    }

}
