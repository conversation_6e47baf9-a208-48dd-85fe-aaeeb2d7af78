﻿using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.VO
{
    public static class GeneroHelper
    {
        public static List<Genero> ObterListaDeGenerosPorString(string sexo)
        {
            return !string.IsNullOrWhiteSpace(sexo)
                    ? sexo.Split(',').Select(g => Genero.ObterPorCodigo(g.<PERSON>())).ToList()
                    : new List<Genero>();
        }
    }
}
