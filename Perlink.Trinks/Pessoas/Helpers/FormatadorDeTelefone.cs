﻿using PhoneNumbers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.Pessoas.Helpers
{
    public enum FormatoTelefone
    {
        OitoDigitosSemDDD = 8,
        NoveDigitosSemDDD = 9,
        OitoDigitosComDDD = 10,
        NoveDigitosComDDD = 11
    }

    public class FormatadorDeTelefone
    {
        private PhoneNumberUtil phoneNumberUtil;
        public FormatadorDeTelefone()
        {
            phoneNumberUtil = PhoneNumberUtil.GetInstance();
        }

        public IEnumerable<string> Formatar(string telefone)
        {
            if (String.IsNullOrWhiteSpace(telefone))
                return Enumerable.Empty<string>();

            string telefoneSemFormatacao = RemoverFormatacao(telefone);
            if (!PossuiSomenteNumeros(telefoneSemFormatacao))
                return Enumerable.Empty<string>();

            return FormatarTelefoneNasMascarasPossiveis(telefoneSemFormatacao);
        }

        private IEnumerable<string> FormatarTelefoneNasMascarasPossiveis(string telefone)
        {
            switch (telefone.Length)
            {
                case 1:
                case 2:
                case 3:
                case 4:
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.OitoDigitosComDDD);
                    yield return FormatarTelefoneSemDDD(telefone, FormatoTelefone.OitoDigitosSemDDD);
                    break;
                case 5:
                case 6:
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.OitoDigitosComDDD);
                    yield return FormatarTelefoneSemDDD(telefone, FormatoTelefone.OitoDigitosSemDDD);
                    yield return FormatarTelefoneSemDDD(telefone, FormatoTelefone.NoveDigitosSemDDD);
                    break;
                case 7:
                case 8:
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.OitoDigitosComDDD);
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.NoveDigitosComDDD);
                    yield return FormatarTelefoneSemDDD(telefone, FormatoTelefone.OitoDigitosSemDDD);
                    yield return FormatarTelefoneSemDDD(telefone, FormatoTelefone.NoveDigitosSemDDD);
                    break;
                case 9:
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.OitoDigitosComDDD);
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.NoveDigitosComDDD);
                    yield return FormatarTelefoneSemDDD(telefone, FormatoTelefone.NoveDigitosSemDDD);
                    break;
                case 10:
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.OitoDigitosComDDD);
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.NoveDigitosComDDD);
                    break;
                case 11:
                    yield return FormatarTelefoneComDDD(telefone, FormatoTelefone.NoveDigitosComDDD);
                    break;
                default:
                    yield return telefone;
                    break;
            }
        }

        public string FormatarNaMascaraCorretaParaDdi(string telefone)
        {
            try
            {
                var phoneNumber = phoneNumberUtil.Parse($"+{telefone.Replace(" ", "")}", null);
                string formattedNational = phoneNumberUtil.Format(phoneNumber, telefone.StartsWith(DdiConstants.Brasil) ? PhoneNumberFormat.NATIONAL : PhoneNumberFormat.INTERNATIONAL);

                if (telefone.StartsWith(DdiConstants.Brasil))
                {
                    return Formatar(formattedNational).First();
                }

                return formattedNational;
            } catch
            {
                return telefone;
            }
        }

        public PhoneNumber RecuperarNumeroComFormatacao(string telefone)
        {
            return phoneNumberUtil.Parse($"+{telefone.Replace(" ", "")}", null);
        }

        private string RemoverFormatacao(string telefone)
        {
            return Regex.Replace(telefone.Trim(), "[\\s\\()-]", String.Empty);
        }

        private bool PossuiSomenteNumeros(string telefone)
        {
            return Regex.IsMatch(telefone, "^[0-9]*$");
        }

        private const int QtdDigitosDDD = 2;
        private const int QtdDigitosSufixo = 4;

        private string FormatarTelefoneSemDDD(string telefone, FormatoTelefone formatoComDDD)
        {
            int totalDigitos = (int)formatoComDDD;

            if (totalDigitos < 8 || totalDigitos > 9)
                throw new ArgumentOutOfRangeException(nameof(formatoComDDD));

            if (telefone.Length <= 4 || telefone.Length == 5 && formatoComDDD == FormatoTelefone.NoveDigitosSemDDD)
                return telefone;

            int digitosPrefixo = totalDigitos - QtdDigitosSufixo;

            return $"{telefone.Substring(0, digitosPrefixo)}-{telefone.Substring(digitosPrefixo)}";
        }

        private string FormatarTelefoneComDDD(string telefone, FormatoTelefone formatoComDDD)
        {
            int totalDigitos = (int)formatoComDDD;

            if (totalDigitos < 10 || totalDigitos > 11)
                throw new ArgumentOutOfRangeException(nameof(formatoComDDD));

            if (telefone.Length < 3)
                return $"({telefone}{(telefone.Length == 2 ? ") " : String.Empty)}";

            if (telefone.Length <= 6 || telefone.Length == 7 && formatoComDDD == FormatoTelefone.NoveDigitosComDDD)
                return $"({telefone.Substring(0, 2)}) {telefone.Substring(2)}";

            int digitosPrefixo = totalDigitos - QtdDigitosDDD - QtdDigitosSufixo;
            return $"({telefone.Substring(0, 2)}) {telefone.Substring(2, digitosPrefixo)}-{telefone.Substring(digitosPrefixo + QtdDigitosDDD)}";
        }
    }
}
