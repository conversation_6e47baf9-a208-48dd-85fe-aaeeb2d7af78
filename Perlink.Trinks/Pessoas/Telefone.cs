﻿using Castle.ActiveRecord;
using Perlink.Trinks.Notificacoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using System;

namespace Perlink.Trinks.Pessoas
{
    [ActiveRecord("Telefone", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public partial class Telefone : ActiveRecordBase<Telefone>
    {
        public Telefone()
        {
            Ativo = true;
            Operadora = null;
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_telefone", ColumnType = "Int32")]
        public virtual Int32 IdTelefone { get; set; }

        [BelongsTo("id_pessoa")]
        public virtual Pessoa Pessoa { get; set; }

        [Property("id_pessoa", ColumnType = "Int32", Insert = false, Update = false)]
        public virtual Int32 IdPessoa { get; set; }

        [Property("ddd_telefone", ColumnType = "String", NotNull = false)]
        public virtual String DDD { get; set; }

        [Property("numero_telefone", ColumnType = "String", NotNull = false)]
        public virtual String Numero { get; set; }

        [Property("ramal_telefone", ColumnType = "String", NotNull = false)]
        public virtual String Ramal { get; set; }

        [BelongsTo("id_dono")]
        public virtual Pessoa Dono { get; set; }

        [BelongsTo("id_telefone_tipo")]
        public virtual TipoTelefone Tipo { get; set; }

        [BelongsTo("id_operadora", Lazy = FetchWhen.OnInvoke)]
        public virtual Operadora Operadora { get; set; }

        [Property("ativo", ColumnType = "Boolean")]
        public virtual bool Ativo { get; set; }

        [Property("ddi_telefone", ColumnType = "String", NotNull = false)]
        public virtual string Ddi { get; set; }

        public override string ToString()
        {
            var numeroCompleto = $"{Ddi}{DDD}{Numero}";

            return numeroCompleto.ToTextoFormatado();
        }

        public virtual string ToStringSemDDD()
        {
            return String.Format("{0}-{1}", Numero.Substring(0, Numero.Length - 4), Numero.Substring(Numero.Length - 4));
        }


        protected bool Equals(Telefone other)
        {
            return string.Equals(DDD, other.DDD) && string.Equals(Numero, other.Numero);
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            var other = obj as Telefone;
            return other != null && Equals(other);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return (DDD.GetHashCode() * 397) ^ Numero.GetHashCode();
            }
        }

        public virtual bool EhCelular(string ddi = DdiConstants.Brasil)
        {
            return Numero.EhUmTelefoneCelular(ddi);
        }

        public virtual bool EhWhatsApp()
        {
            return Tipo.IdTipoTelefone == (int)TipoTelefoneEnum.WhatsApp;
        }
    }
}