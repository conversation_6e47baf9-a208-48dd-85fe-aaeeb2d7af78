﻿using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.ExtensionMethods
{
    public static class PeriodoAusenciaExtension
    {

        public static void Inativar(this IList<PeriodoAusencia> lista)
        {
            foreach (PeriodoAusencia item in lista)
                item.Ativo = false;
        }
        public static void Reativar(this IList<PeriodoAusencia> lista)
        {
            foreach (PeriodoAusencia item in lista)
                item.Ativo = true;
        }

        public static IQueryable<PeriodoAusencia> FiltrarContidasNoHorario(this IQueryable<PeriodoAusencia> listaAusencias, Horario horario)
        {
            return listaAusencias.Where(
                    a => a.Ativo == true &&
                    (a.DataHoraInicio < horario.DataFim) &&
                    (a.DataHoraFim > horario.DataInicio)

                );
        }
    }
}
