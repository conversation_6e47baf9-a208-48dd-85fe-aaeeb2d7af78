﻿using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.LGPD.Helpers;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using Perlink.Trinks.Pessoas.Helpers;
using System.Text.RegularExpressions;
using System.Text;

namespace Perlink.Trinks.Pessoas.ExtensionMethods
{

    public static class TelefoneExtension
    {

        public static bool PertenceA(this Telefone telefone, Pessoa pessoa)
        {
            return telefone.Dono.IdPessoa == pessoa.IdPessoa;
        }

        public static List<Telefone> IdentificadosComoCelular(this IEnumerable<Telefone> telefones)
        {
            return telefones.Where(t => t.IdentificadoComoCelular()).ToList();
        }

        public static List<Telefone> TelefonesNacionais(this IEnumerable<Telefone> telefones)
        {
            return telefones.Where(t => t.Ddi == DdiConstants.Brasil).ToList();
        }

        public static bool IdentificadoComoCelular(this Telefone telefone)
        {
            if (telefone == null || telefone.Numero.Length <= 0)
                return false;

            var primeiroDigitoDeCelulares = new int[] { 6, 7, 8, 9 };
            var digitosParaComparacao = primeiroDigitoDeCelulares.Select(x => x.ToString());
            return digitosParaComparacao.Contains(telefone.Numero[0].ToString()) && telefone.Ddi == DdiConstants.Brasil;
        }

        public static List<Telefone> Ativos(this IList<Telefone> lista)
        {
            return lista.Where(s => s.Ativo).ToList();
        }

        public static List<Telefone> Atualizar(this IList<Telefone> lista, IList<Telefone> novosTelefones)
        {
            //Trata telefones incluidos e atualizados
            foreach (Telefone novoTelefone in novosTelefones)
            {
                Telefone telefoneExistente = lista.EncontrarPorNumero(novoTelefone);

                if (telefoneExistente == null || telefoneExistente.IdTelefone == 0)
                {
                    var telefoneJaExiste = lista.Any(t => 
                        t.Ddi == novoTelefone.Ddi && 
                        t.DDD == novoTelefone.DDD && 
                        t.Numero == novoTelefone.Numero);

                    if (!telefoneJaExiste)
                        lista.Add(novoTelefone);
                }
                else
                {
                    telefoneExistente.Atualizar(novoTelefone);
                }
            }

            return lista.ToList();
        }

        public static List<Telefone> AtualizarOuExcluir(this IList<Telefone> lista, IList<Telefone> novosTelefones)
        {
            if (!novosTelefones.Any()) return lista.ToList();

            lista.Atualizar(novosTelefones);

            return lista.ToList();
        }

        public static Telefone Atualizar(this Telefone existente, Telefone novo)
        {
            existente.Ativo = novo.Ativo;
            existente.DDD = novo.DDD.SomenteNumeros();
            existente.Numero = novo.Numero.SomenteNumeros();
            existente.Ramal = novo.Ramal;
            existente.Tipo = new TipoTelefone(novo.Tipo != null ? novo.Tipo.IdTipoTelefone : 3);
            existente.Ddi = novo.Ddi;

            if (novo.Dono != null)
                existente.Dono = novo.Dono;

            existente.Pessoa = novo.Pessoa;

            return existente;
        }

        public static void ExcluirTodos(this IList<Telefone> lista)
        {
            lista.Clear();
        }

        public static Telefone EncontrarPorNumero(this IList<Telefone> lista, Telefone novoTelefone)
        {
            Telefone existente;

            try
            {
                existente = lista
                    .Where(l => l.Ddi == novoTelefone.Ddi)
                    .Where(l => l.Numero == novoTelefone.Numero)
                    .Where(l => l.DDD == novoTelefone.DDD)
                    .FirstOrDefault();
            }
            catch (InvalidOperationException)
            {
                existente = null;
            }

            return existente;
        }

        public static Telefone EncontrarPorCodigoPessoaDono(this IList<Telefone> lista, Int32 codigoDono)
        {
            Telefone existente;

            try
            {
                existente = (lista.Single(s => s.Dono.IdPessoa == codigoDono));
            }
            catch (InvalidOperationException)
            {
                existente = null;
            }

            return existente;
        }

        public static List<Telefone> FiltrarPorDono(this IEnumerable<Telefone> lista, int idPessoa)
        {
            return lista.Where(t => t.Dono.IdPessoa == idPessoa).ToList();
        }

        public static List<Telefone> FiltrarTelefoneAtivoPorDono(this IEnumerable<Telefone> lista, int idPessoa)
        {
            List<Telefone> telefones = FiltrarPorDono(lista, idPessoa);
            return telefones.Where(t => t.Ativo).ToList();
        }

        public static List<Telefone> FiltrarPorTipo(this IEnumerable<Telefone> lista, TipoTelefoneEnum tipoTelefone)
        {
            return lista.Where(t => t.Tipo.IdTipoTelefone == tipoTelefone.GetHashCode()).ToList();
        }

        public static List<Telefone> FiltrarPorPessoa(this IEnumerable<Telefone> lista, int idPessoa)
        {
            return lista.Where(t => t.IdPessoa == idPessoa).ToList();
        }

        public static List<String> ToListaString(this IEnumerable<Telefone> lista)
        {
            return lista.Select(item => item.ToTextoFormatado()).ToList();
        }

        public static String ToTextoFormatado(this Telefone telefone)
        {
            var formatador = new FormatadorDeTelefone();
            var telefoneCompleto = $"{telefone.Ddi}{telefone.DDD}{telefone.Numero}";
            var telefoneFormatado = formatador.FormatarNaMascaraCorretaParaDdi(telefoneCompleto.Trim());

            return OcultarDadosHelper.Telefone(telefoneFormatado);
        }

        public static string ToTextoFormatado(this string telefone)
        {
            var formatador = new FormatadorDeTelefone();

            return formatador.FormatarNaMascaraCorretaParaDdi(telefone.Trim());
        }

        public static String ToTextoFormatadoLista(this IEnumerable<Telefone> lista)
        {
            return ToTextoFormatadoLista(lista, null, null);
        }

        public static String ToTextoFormatadoLista(this IEnumerable<Telefone> lista, String caracterSeparador)
        {
            return ToTextoFormatadoLista(lista, null, caracterSeparador);
        }

        public static String ToTextoFormatadoLista(this IEnumerable<Telefone> lista, int quantidade)
        {
            return ToTextoFormatadoLista(lista, quantidade, null);
        }

        public static String ToTextoFormatadoLista(this IEnumerable<Telefone> lista, Int32? quantidade,
            String caracterSeparador)
        {
            IEnumerable<string> telefones = lista.Where(f => f.Ativo).Select(f => f.ToTextoFormatado()).Distinct();
            if (quantidade.HasValue) telefones = telefones.Take(quantidade.Value);
            if (string.IsNullOrWhiteSpace(caracterSeparador)) caracterSeparador = " / ";
            return string.Join(caracterSeparador, telefones);
        }

        public static String ToTextoFormatadoListaSemLGPD(this IEnumerable<Telefone> lista, Int32? quantidade = null, String caracterSeparador = null)
        {
            IEnumerable<string> telefones = lista.Where(f => f.Ativo).Select(f => f.ToTextoFormatadoSemLGPD()).Distinct();
            if (quantidade.HasValue) telefones = telefones.Take(quantidade.Value);
            if (string.IsNullOrWhiteSpace(caracterSeparador)) caracterSeparador = " / ";
            return string.Join(caracterSeparador, telefones);
        }

        public static String ToTextoFormatadoSemLGPD(this Telefone telefone)
        {
            var formatador = new FormatadorDeTelefone();
            var telefoneCompleto = $"{telefone.Ddi}{telefone.DDD}{telefone.Numero}";
            var telefoneFormatado = formatador.FormatarNaMascaraCorretaParaDdi(telefoneCompleto.Trim());

            return telefoneFormatado;
        }

        public static List<KeyValuePair<string, bool>> ToTextoFormatadoLista(this string lista)
        {
            var telefones = new List<KeyValuePair<string, bool>>();

            if (string.IsNullOrEmpty(lista))
            {
                return telefones;
            }

            var telefonesString = lista.Split('/');

            var formatador = new FormatadorDeTelefone();

            foreach (var telefone in telefonesString)
            {
                var apenasNumeros = Regex.Replace(telefone, @"\D", "");

                if (string.IsNullOrEmpty(apenasNumeros))
                {
                    continue;
                }

                var telefoneFormatado = formatador.FormatarNaMascaraCorretaParaDdi(apenasNumeros);
                var telefoneDestrinchado = formatador.RecuperarNumeroComFormatacao(apenasNumeros);
                var numeroComDdd = telefoneDestrinchado.NationalNumber.ToString();

                var ehCelular = numeroComDdd.EhUmTelefoneCelularComDDD(telefoneDestrinchado.CountryCode.ToString());

                telefones.Add(new KeyValuePair<string, bool>(telefoneFormatado, ehCelular));
            }

            return telefones;
        }

        public static string ToTextoFormatadoListaConcatenada(this string telefones)
        {
            if (string.IsNullOrEmpty(telefones))
                return string.Empty;

            var formatador = new FormatadorDeTelefone();
            var telefonesString = telefones.Split('/');
            var telefoneFormatadoBuilder = new StringBuilder();

            foreach (var telefone in telefonesString)
            {
                var apenasNumeros = Regex.Replace(telefone, @"\D", "");
                var telefoneSemFormatacao = telefone.Trim().StartsWith("(") ? DdiConstants.Brasil + apenasNumeros : apenasNumeros;

                telefoneFormatadoBuilder.Append(formatador.FormatarNaMascaraCorretaParaDdi(telefoneSemFormatacao) + " / ");
            }

            return telefoneFormatadoBuilder.ToString().TrimEnd(' ', '/');
        }

        public static Telefone CopiarParaPessoa(this Telefone telefoneOriginal, Pessoa novoDono)
        {
            return new Telefone
            {
                Ativo = true,
                DDD = telefoneOriginal.DDD.SomenteNumeros(),
                Dono = novoDono,
                Pessoa = telefoneOriginal.Pessoa,
                Numero = telefoneOriginal.Numero.SomenteNumeros(),
                Ramal = telefoneOriginal.Ramal,
                Tipo = telefoneOriginal.Tipo,
                Ddi = telefoneOriginal.Ddi,
            };
        }

        public static bool EhUmNumeroDeTelefoneValido(this string telefone)
        {
            var numeroDoTelefone = telefone.SomenteNumeros();
            return numeroDoTelefone.Length == 8 || numeroDoTelefone.Length == 9;
        }

        public static bool EhUmTelefoneCelularComDDD(this string numeroComDDD, string ddi = DdiConstants.Brasil)
        {
            var numeros = numeroComDDD.SomenteNumeros();

            return ddi != DdiConstants.Brasil || 
                (numeros.Length == 10 || numeros.Length == 11)
                    && numeros.Substring(2).EhUmTelefoneCelular();
        }

        public static bool EhUmTelefoneCelular(this string numero, string ddi = DdiConstants.Brasil)
        {
            return (numero.EhUmNumeroDeTelefoneValido()
                && (numero.StartsWith("9") || numero.StartsWith("8") || numero.StartsWith("7") || numero.StartsWith("6"))) 
                || ddi != DdiConstants.Brasil;
        }

        public static string ObterCelularValidoDePessoaJuridica(this IList<Telefone> telefones, PessoaJuridica pessoaJuridica)
        {
            return telefones
                .Where(p => p.Dono == p.Pessoa || p.Dono == pessoaJuridica)
                .Where(p => p.Ativo)
                .Where(p => p.Ddi == DdiConstants.Brasil)
                .Where(p => p.Numero.StartsWith("6") || p.Numero.StartsWith("7") || p.Numero.StartsWith("8") || p.Numero.StartsWith("9"))
                .OrderByDescending(p => p.IdTelefone)
                .Select(p => p.DDD + p.Numero)
                .FirstOrDefault();
        }
    }
}