﻿using Castle.ActiveRecord;
using Perlink.Trinks.Cashback;
using Perlink.Trinks.Fidelidade;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Pacotes;
using System;

namespace Perlink.Trinks.Pessoas
{
    [ActiveRecord("Horario_Transacao", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true,
    Cache = CacheEnum.Undefined, Mutable = true)]
    [Serializable]
    public class HorarioTransacao : ActiveRecordBase<HorarioTransacao>, IMovimentaPontosDeFidelidade
    {

        public HorarioTransacao()
        {
        }

        public HorarioTransacao(int codigo)
            : this()
        {
            Codigo = codigo;
            Refresh();
        }

        [Property("ativo", ColumnType = "Boolean")]
        public virtual bool Ativo { get; set; } = true;

        [PrimaryKey(PrimaryKeyType.Native, "id_horario_transacao", ColumnType = "Int32")]
        public virtual Int32 Codigo { get; set; }

        [BelongsTo("id_comissao", Cascade = CascadeEnum.All, Lazy = FetchWhen.OnInvoke)]
        public virtual Comissao Comissao { get; set; }

        [Property("data_hora_inicio_horario", NotNull = false)]
        public virtual DateTime? DataHoraInicioHorario { get; set; }

        [Property("custo_descartaveis", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? Descartaveis { get; set; }

        [Property("valor_desconto", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? Desconto { get; set; }

        [BelongsTo("id_horario", Cascade = CascadeEnum.All, Lazy = FetchWhen.OnInvoke)]
        public virtual Horario Horario { get; set; }

        [BelongsTo("id_comissao_assistente", Cascade = CascadeEnum.All, Lazy = FetchWhen.OnInvoke)]
        public virtual Comissao ComissaoAssistente { get; set; }

        [BelongsTo("id_pacote_cliente_item", Cascade = CascadeEnum.All, Lazy = FetchWhen.OnInvoke)]
        public virtual ItemPacoteCliente ItemPacoteCliente { get; set; }

        [BelongsTo("id_motivo_desconto", Lazy = FetchWhen.OnInvoke)]
        public virtual MotivoDesconto MotivoDesconto { get; set; }

        [Property("preco_servico", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal? Preco { get; set; }

        [BelongsTo("id_desconto_tipo")]
        public virtual TipoDesconto TipoDesconto { get; set; }

        [Property("realizado_por_profissional_parceiro_ativo", ColumnType = "Boolean")]
        public virtual bool RealizadoPorProfissionalParceiroAtivo { get; set; }

        [Property("realizado_por_assistente_parceiro_ativo", ColumnType = "Boolean")]
        public virtual bool RealizadoPorAssistenteParceiroAtivo { get; set; }

        [BelongsTo("id_transacao", Lazy = FetchWhen.OnInvoke)]
        public virtual Transacao Transacao { get; set; }

        [Property("pontos_fidelidade")]
        public virtual int PontosDeFidelidade { get; set; }

        [Property("usou_pontos_fidelidade")]
        public virtual bool UsouPontosDeFidelidade { get; set; }

        #region Cashback
        private CashbackHorarioTransacao _cashback = null;
        public virtual void AtribuirCashbackSemPersistencia(CashbackHorarioTransacao cashback)
        {
            _cashback = cashback;
        }

        public virtual bool PossuiCashbackNaoPersistido()
        {
            return _cashback != null;
        }

        public virtual CashbackHorarioTransacao ObterCashbackNaoPersistido()
        {
            return _cashback;
        }
        #endregion

        public virtual Decimal? SubTotal()
        {
            Decimal? subTotal = null;

            if (Preco.HasValue)
                subTotal = Preco.Value;

            if (!Desconto.HasValue) return subTotal;
            if (subTotal == null)
                subTotal = 0;

            subTotal = subTotal + Desconto.Value;

            return subTotal;
        }

        public virtual bool EhGanhoDePontos()
        {
            return SubTotal() > 0;
        }

        public virtual decimal ObterValorPago()
        {
            return SubTotal() ?? 0;
        }

        public virtual int ObterQuantidade()
        {
            return 1;
        }

        public virtual IContemConfiguracaoDePontos ObterConfiguracaoDePontosDoItem()
        {
            return Horario.ServicoEstabelecimento;
        }

        public virtual bool PossuiDescontoOuPromocao()
        {
            return SubTotal() < Horario.ServicoEstabelecimento.Preco;
        }

        public virtual bool FoiAgendadoNoBalcao()
        {
            return Horario.FoiAgendadoNoBalcao();
        }
    }
}