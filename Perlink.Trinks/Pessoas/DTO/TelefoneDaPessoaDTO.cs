﻿using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Interfaces;

namespace Perlink.Trinks.Pessoas.DTO
{
    public class TelefoneDaPessoaDTO : ITelefone
    {
        public TelefoneDaPessoaDTO()
        {

        }

        public TelefoneDaPessoaDTO(string ddi, string ddd, string numero)
        {
            DDI = ddi;
            DDD = ddd;
            Numero = numero;
        }

        public int IdPessoa { get; set; }
        public string DDI { get; set; }
        public string DDD { get; set; }
        public string Numero { get; set; }

        public override string ToString()
        {
            var numeroCompleto = $"{DDI}{DDD}{Numero}";

            return numeroCompleto.ToTextoFormatado();
        }
    }
}
