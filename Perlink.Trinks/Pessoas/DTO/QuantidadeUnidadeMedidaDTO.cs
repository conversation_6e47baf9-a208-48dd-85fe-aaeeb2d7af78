﻿namespace Perlink.Trinks.Pessoas.DTO
{
    public class QuantidadeUnidadeMedidaDTO
    {
        public QuantidadeUnidadeMedidaDTO()
        {
            MedidaDeFracaoEmCaixaBaixa = true;
        }

        public QuantidadeUnidadeMedidaDTO(int quantidadeTotalEmMedidas, string unidadeDeMedida, int medidasPorUnidade) : this()
        {
            QuantidadeTotalEmMedidas = quantidadeTotalEmMedidas;
            UnidadeDeMedida = unidadeDeMedida;
            MedidasPorUnidade = medidasPorUnidade;
            AdicionaTotalEmFracaoEntreParenteses = true;
        }

        public int QuantidadeTotalEmMedidas { get; private set; }
        public string UnidadeDeMedida { get; private set; }
        public int MedidasPorUnidade { get; private set; }
        public bool ExibeTudoComoFracaoSemSepararUnidade { get; private set; }
        public bool ColocarUnidadePorExtenso { get; private set; }
        public bool MedidaDeFracaoEmCaixaBaixa { get; private set; }
        public bool AdicionaTotalEmFracaoEntreParenteses { get; private set; }
        public bool EhAppPro { get; private set; }
    }
}
