﻿using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.DTO
{

    public class ClientesDaRedeDTO
    {
        public string NomeUnidade { get; set; }
        public string Nome { get; set; }
        public string Email { get; set; }
        public string Telefones { get; set; }
        public string Cpf { get; set; }
        public string DataNascimento { get; set; }
        public string Sexo { get; set; }
        public int IdEstabelecimento { get; set; }
        public int IdClienteEstabelecimento { get; set; }
        public int IdCliente { get; set; }

        public override bool Equals(object obj)
        {
            if (obj is ClientesDaRedeDTO other)
            {
                return IdEstabelecimento == other.IdEstabelecimento &&
                       Email == other.Email &&
                       Cpf == other.Cpf;
            }
            return false;
        }

        public override int GetHashCode()
        {
            int hashCode = -1832312239;
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(Email);
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(Cpf);
            hashCode = hashCode * -1521134295 + IdEstabelecimento.GetHashCode();
            return hashCode;
        }
    }
}