﻿using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.PromocoesOnline;
using Perlink.Trinks.Vendas;
using System;

namespace Perlink.Trinks.Pessoas.DTO
{

    public class DadosParaRealizarAgendamentoDTO
    {
        public int IdServicoEstabelecimento { get; set; }
        public int? IdCliente { get; set; }
        public DateTime DataHora { get; set; }
        public int? Duracao { get; set; }
        public int IdEstabelecimentoProfissional { get; set; }

        [Obsolete("Na verdade se trata do Id do EstabelecimentoProfissional")]
        public int IdProfissional { get { return IdEstabelecimentoProfissional; } set { IdEstabelecimentoProfissional = value; } }
        public HorarioOrigemEnum Origem { get; set; }
        public StatusHorarioEnum? StatusHorario { get; set; }
        public string Comentarios { get; set; }
        public decimal Valor { get; set; }
        public int? IdEstabelecimentoProfissionalAssistente { get; set; }
        public string Cupom { get; set; }
        public Comanda Comanda { get; set; }
        public int IdEstabelecimento { get; set; }
        public bool FoiPagoAntecipadamente { get; set; }

        public static DadosParaRealizarAgendamentoDTO CriarPelaPromocaoOnline(AgendamentoTemporario agendamentoTemp)
            => new DadosParaRealizarAgendamentoDTO
            {
                IdCliente = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(agendamentoTemp.IdClienteEstabelecimento).Cliente.IdCliente,
                IdServicoEstabelecimento = agendamentoTemp.PromocaoOnline.ServicoEstabelecimento.IdServicoEstabelecimento,
                DataHora = agendamentoTemp.Data,
                IdEstabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(agendamentoTemp.IdProfissional, agendamentoTemp.PromocaoOnline.Estabelecimento.IdEstabelecimento).Codigo,
                FoiPagoAntecipadamente = true,
                StatusHorario = StatusHorarioEnum.Confirmado,
            };
    }
}