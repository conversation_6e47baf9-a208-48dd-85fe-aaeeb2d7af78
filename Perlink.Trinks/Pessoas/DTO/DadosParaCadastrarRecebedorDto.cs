﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.DTO
{
    public class DadosParaCadastrarRecebedorDto
    {

        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("foto")]
        public string Foto { get; set; }

        [JsonProperty("emailPF")]
        public string EmailPF { get; set; }
        
        [JsonProperty("possuisplit")]
        public bool PossuiSplit { get; set; }

        [JsonProperty("datanascimento")]
        public DateTime? DataNascimento { get; set; }

        [JsonProperty("documento")]
        public string Documento { get; set; }

        [JsonProperty("codigobanco")]
        public int? CodigoBanco { get; set; }

        [JsonProperty("agencia")]
        public string Agencia { get; set; }

        [JsonProperty("digitoverificadoragencia")]
        public string DigitoVerificadorAgencia { get; set; }

        [JsonProperty("numeroconta")]
        public string NumeroConta { get; set; }

        [JsonProperty("digitoverificadorconta")]
        public string DigitoVerificadorConta { get; set; }

        [JsonProperty("idtipoconta")]
        public int IdTipoConta { get; set; }

        [JsonProperty("cep")]
        public string Cep { get; set; }

        [JsonProperty("tipologradouro")]
        public string TipoLogradouro { get; set; }

        [JsonProperty("logradouro")]
        public string Logradouro { get; set; }

        [JsonProperty("numero")]
        public string Numero { get; set; }

        [JsonProperty("bairro")]
        public string Bairro { get; set; }

        [JsonProperty("estado")]
        public string Estado { get; set; }

        [JsonProperty("cidade")]
        public string Cidade { get; set; }

        [JsonProperty("complemento")]
        public string Complemento { get; set; }

        [JsonProperty("documentoDaContaBancaria")]
        public string DocumentoDaContaBancaria { get; set; }

        [JsonProperty("nomeDoTitularDaContaBancaria")]
        public string NomeDoTitularDaContaBancaria { get; set; }

        [JsonProperty("ehRecebedorNoConnectPagarme")]
        public bool EhRecebedorNoConnectPagarme { get; set; }

        [JsonProperty("emailPJ")]
        public string EmailPJ { get; set; }

        [JsonProperty("telefonePJ")]
        public string TelefonePJ { get; set; }

        [JsonProperty("cnpj")]
        public string Cnpj { get; set; }

        [JsonProperty("razaoSocial")]
        public string RazaoSocial { get; set; }

        [JsonProperty("nomeFantasia")]
        public string NomeFantasia { get; set; }

        [JsonProperty("tipoPessoa")]
        public TipoPessoaEnum TipoPessoa { get; set; }

        [JsonProperty("rendaMensal")]
        public decimal? RendaMensal { get; set; }

        [JsonProperty("rendaAnual")]
        public decimal? RendaAnual { get; set; }

        [JsonProperty("ocupacaoProfissional")]
        public string OcupacaoProfissional { get; set; }

        [JsonProperty("telefonePF")]
        public string TelefonePF { get; set; }

        [JsonProperty("pontoReferencia")]
        public string PontoReferencia { get; set; }

        [JsonProperty("managingPartners")]
        public List<ManagingPartnerDto> ManagingPartners { get; set; } = new List<ManagingPartnerDto>();

        public string SecretKey { get; internal set; }
        public int IdEstabelecimentoProfissional { get; set; }
        public string CodigoDoBanco { get; internal set; }
    }

    public enum TipoPessoaEnum
    {
        Fisica = 1,
        Juridica = 2
    }

    public class ManagingPartnerDto
    {
        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("documento")]
        public string Documento { get; set; }

        [JsonProperty("tipo")]
        public string Tipo { get; set; } = "individual";

        [JsonProperty("dataNascimento")]
        public string DataNascimento { get; set; }

        [JsonProperty("rendaMensal")]
        public decimal? RendaMensal { get; set; }

        [JsonProperty("ocupacaoProfissional")]
        public string OcupacaoProfissional { get; set; }

        [JsonProperty("telefone")]
        public string Telefone { get; set; }

        [JsonProperty("ehRepresentanteLegal")]
        public bool EhRepresentanteLegal { get; set; }
    }
}
