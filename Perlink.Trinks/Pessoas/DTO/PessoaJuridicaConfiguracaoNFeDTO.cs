﻿namespace Perlink.Trinks.Pessoas.DTO
{
    public class PessoaJuridicaConfiguracaoNFeDTO
    {
        public int IdPessoaJuridica { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string InscricaoMunicipal { get; set; }
        public string CNPJ { get; set; }
        public string InscricaoEstadual { get; set; }
        public string CodigoServicoPrestado { get; set; }
        public string CodigoCNAE { get; set; }
        public decimal Aliquota { get; set; }
        public string Serie { get; set; }
        public int NumeroUltimoRPSEmitido { get; set; }
        public int NumeroUltimoLoteGerado { get; set; }
        public string CodigoTributacaoMunicipio { get; set; }
        public string CodigoRegimeEspecialTributacao { get; set; }
        public bool SomenteConfiguracaoDaNota { get; set; }
        public bool SomenteDadosSociais { get; set; }
    }
}
