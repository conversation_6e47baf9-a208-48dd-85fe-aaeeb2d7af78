﻿using Perlink.Shared.Enums;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Fotos.Enums;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.DTO
{

    public class ResumoDeDadosDoEstabelecimentoDTO
    {

        public ResumoDeDadosDoEstabelecimentoDTO(Estabelecimento estabelecimento
            , PessoaFisica responsavelFinanceiro, int quantidadeDeProfissionaisAtivos, bool programaDeFidelidadeEstaConfigurado
            , HotsiteEstabelecimento hostSiteEstabelecimento, bool estaNoPortal, DateTime? dataUltimoAgendamentoOnlineRealizado
            , DateTime? dataUltimoFechamentoDeContaRealizado, DateTime? dataUltimoAgendamentoRealizado, DateTime? ultimosPontosDados
            , DateTime? ultimosPontosResgatados, EstabelecimentoSolicitacaoAparecerBusca solicitacaoAparecerNaBusca, RelatorioAssinatura relatorioDatas
            , List<string> codigosParceria, string responsavelAtendimento, EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPOS, DateTime? dataUltimaFechamentoContaBelezinha)
        {
            //Logo e fotos do estabeleciemnto
            LogoDoEstabeleciemnto = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim120x120);
            FotosDoEstabelecimento = estabelecimento.PessoaJuridica.Fotos
                .Select(f =>
                    new LinksImagensestabelecimentoDTO()
                    {
                        FotoEstabelecimentoDim101x74 = f.ObterCaminhoWeb(Fotos.Enums.DimemsoesFotosEnum.Dim101x74),
                        FotoEstabelecimentoDimOriginal = f.ObterCaminhoWeb(Fotos.Enums.DimemsoesFotosEnum.Original)
                    })
                        .Take(10)
                        .ToList();

            var estabelecimentoConcluiuConfiguracaoBelezinha = estabelecimentoConfiguracaoPOS != null ? estabelecimentoConfiguracaoPOS.ConcluiuConfiguracao : false;
            var tipoPOS = estabelecimentoConfiguracaoPOS != null ? estabelecimentoConfiguracaoPOS.TipoPOS.Id : 0;
            ConcluiuConfiguracaoTipoPOS = estabelecimentoConcluiuConfiguracaoBelezinha ? "Sim" : "Não";
            NomeTipoIntegracao = estabelecimentoConfiguracaoPOS != null ? estabelecimentoConfiguracaoPOS.TipoPOS.NomeExibicao ?? "N/A" : "N/A";

            DataUltimaFechamentoContaBelezinha = dataUltimaFechamentoContaBelezinha.HasValue ? dataUltimaFechamentoContaBelezinha.Value.ToBrazilianLongDateTimeString() : "N/A";

            HotSiteDisponivel = false;
            if (hostSiteEstabelecimento != null)
            {
                var urlPertenceABlackList = ConfiguracoesTrinks.HotSite.BlackListUrlsHotsite.Contains(hostSiteEstabelecimento.Url);
                HotSiteDisponivel = !urlPertenceABlackList && hostSiteEstabelecimento.DesejaTerHotsite;
            }
            // estabeleciemnto
            NomeFantasia = estabelecimento.PessoaJuridica.NomeFantasia ?? "N/A";
            RazaoSocial = estabelecimento.PessoaJuridica.RazaoSocial ?? "N/A";
            UrlDoEstabelecimentoHotSite = estabelecimento.Hotsite() != null ? estabelecimento.Hotsite().UrlCompleta() : "N/A";
            TelefoneDoEstabelecimento = estabelecimento.PessoaJuridica.Telefones.Where(t => t.Ativo).Select(t => t.ToString()).Distinct().ToList();
            EnderecoDoEstabelecimento = estabelecimento.PessoaJuridica.Enderecos.Where(e => e.Ativo).Select(e => e.ObterTextoEndereco()).FirstOrDefault() ?? "N/A";
            var endereco = estabelecimento.PessoaJuridica.Enderecos.Where(e => e.Ativo).Select(e => new Endereco { Bairro = e.Bairro, Cidade = e.Cidade, UF = e.UF }).FirstOrDefault();
            EnderecoResumidoDoEstabelecimento = String.Format("{0}, {1}, {2}", endereco.Bairro != null ? endereco.Bairro : "N/A", endereco.Cidade != null ? endereco.Cidade : "N/A", endereco.UF != null ? endereco.UF.Sigla : "N/A");

            EmailDoEstabelecimento = estabelecimento.EmailEstabelecimento();
            NomeTipoEstabelecimento = estabelecimento.TipoEstabelecimento != null ? estabelecimento.TipoEstabelecimento.Nome : "N/A";

            // Responsavel finaceiro do estabeleciemnto
            NomeResponsavelFinaceiro = responsavelFinanceiro.NomeCompleto ?? "N/A";
            var telefones = Domain.Pessoas.TelefoneRepository.Queryable().Where(te => te.IdPessoa == responsavelFinanceiro.IdPessoa && te.Ativo)
                .Select(t => new Telefone { Ddi = t.Ddi, DDD = t.DDD, Numero = t.Numero }).ToList().Distinct();
            TelefoneDoResponsavelFinaceiro = new List<string>();
            foreach (var tel in telefones)
            {
                if (!string.IsNullOrEmpty(tel.DDD) && !string.IsNullOrEmpty(tel.Numero))
                    TelefoneDoResponsavelFinaceiro.Add(tel.ToTextoFormatado());
            }
            if (TelefoneDoResponsavelFinaceiro.Count <= 0)
                TelefoneDoResponsavelFinaceiro.Add("N/A");

            EmailDoResponsavelFinaceiro = responsavelFinanceiro.Email;

            //conta
            TextoDataDeCadastro = relatorioDatas != null ? relatorioDatas.EstabelecimentoDataCadastro.ToBrazilianShortDateString() : "N/A";//contaDaPessoaJuridicaDoEstabelecimento != null ? contaDaPessoaJuridicaDoEstabelecimento.DataCadastro.ToBrazilianShortDateString() : "N/A";
            TextoDataDaAssinatura = relatorioDatas != null && relatorioDatas.ContaFinanceiraDataAssinatura.HasValue ? relatorioDatas.ContaFinanceiraDataAssinatura.Value.ToBrazilianShortDateString() : "N/A";//cobAssinatura != null && cobAssinatura.DataInicio != null ? cobAssinatura.DataInicio.Value.ToBrazilianShortDateString() : "N/A";
            StatusDaConta = relatorioDatas != null ? relatorioDatas.ContaFinanceiraNomeStatus : "N/A";
            TextoDataFimDoPeriodoGratis = relatorioDatas != null && relatorioDatas.EstabelecimentoDataCadastro != null
                                             ? Convert.ToDateTime(relatorioDatas.DataFimAssinaturaGratis).ToString("dd/MM/yyyy") : "N/A";//cobAssinatura != null && cobAssinatura.DataInicio != null ? cobAssinatura.DataInicio.Value.AddDays(cobAssinatura.DiasGratis).ToBrazilianShortDateString() : "N/A";

            //Profissionais
            NumeroTotalDeProfissionaisComAgenda = estabelecimento.ProfissionaisComAcessoAgenda().Where(p => p.Ativo).ToList().Count.ToString();
            NumeroTotalDeProfissionaisAtivos = quantidadeDeProfissionaisAtivos.ToString();
            FaixaDeProfissionais = EnumActions.GetEnumText(estabelecimento.FaixaProfissionais);
            //agendamentos online
            SolicitacaoParaAparecerNoPortal = "N/A";
            if (solicitacaoAparecerNaBusca == null)
                SolicitacaoParaAparecerNoPortal = "Nunca foi solicitado";
            else if (solicitacaoAparecerNaBusca.DataRegistroDecisao == null)
                SolicitacaoParaAparecerNoPortal = "Pendente de aprovação";
            else if (solicitacaoAparecerNaBusca.DataRegistroDecisao.HasValue && solicitacaoAparecerNaBusca.Aprovada == true)
                SolicitacaoParaAparecerNoPortal = "Aprovada";
            else if (solicitacaoAparecerNaBusca.DataRegistroDecisao.HasValue && solicitacaoAparecerNaBusca.Aprovada == false)
                SolicitacaoParaAparecerNoPortal = "Não Aprovada ou Retirada (retirada pelo trinks ou pelo próprio cliente)";

            EstaNoPortal = estaNoPortal ? "Sim" : "Não";
            EstaComABuscaDeHorarioDisponivel = hostSiteEstabelecimento != null ? (hostSiteEstabelecimento.PermiteBuscaHotsite ? "Sim" : "Não") : "N/A";
            EstaComAgendamentoOnlineDisponivel = hostSiteEstabelecimento != null ? (hostSiteEstabelecimento.PermiteAgendamentoHotsite ? "Sim" : "Não") : "N/A";
            EstaHabilitadoNaVitrineParaAparecerNoPortal = hostSiteEstabelecimento != null ? (hostSiteEstabelecimento.DesejaAparecerBuscaPortal ? "Sim" : "Não") : "N/A";

            //Ultimo Agendamento
            TextoUltimoAgendamento = dataUltimoAgendamentoRealizado != null ? dataUltimoAgendamentoRealizado.Value.ToBrazilianLongDateTimeString() : "N/A";
            TextoUltimoAgendamentoOnline = dataUltimoAgendamentoOnlineRealizado != null ? dataUltimoAgendamentoOnlineRealizado.Value.ToBrazilianLongDateTimeString() : "N/A";
            TextoUltimoFechamentoDeConta = dataUltimoFechamentoDeContaRealizado != null ? dataUltimoFechamentoDeContaRealizado.Value.ToBrazilianLongDateTimeString() : "N/A";

            //Programa de Fidelidade
            ProgramaDeFidelidadeEstaAtivo = estabelecimento.EstabelecimentoConfiguracaoGeral != null ? (estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaProgramaDeFidelidade ? "Sim" : "Não") : "N/A";
            ProgramaDeFidelidadeEstaConfigurado = programaDeFidelidadeEstaConfigurado ? "Sim" : "Não";
            JaDeuAlgumPonto = ultimosPontosDados != null ? "Sim" : "Não";
            JaHouveAlgunResgateDePontos = ultimosPontosResgatados != null ? "Sim" : "Não";
            TextoUltimosPontosDados = ultimosPontosDados != null ? ultimosPontosDados.Value.ToBrazilianLongDateTimeString() : "N/A";
            TextoUltimosPontosResgatados = ultimosPontosResgatados != null ? ultimosPontosResgatados.Value.ToBrazilianLongDateTimeString() : "N/A";

            //Acesso sistema externo
            LinkSistemaExterno = Domain.Pessoas.EstabelecimentoService.ObterLinkSistemaExterno(estabelecimento.IdEstabelecimento);

            //Codigos Parceria
            CodigosParceria = codigosParceria;

            //responsave lAtendimento
            ResponsavelAtendimento = responsavelAtendimento;
        }

        public string EmailDoEstabelecimento { get; set; }
        public string EmailDoResponsavelFinaceiro { get; set; }
        public string EnderecoDoEstabelecimento { get; set; }
        public string LogoDoEstabeleciemnto { get; set; }
        public string NomeFantasia { get; set; }
        public string NomeResponsavelFinaceiro { get; set; }
        public string RazaoSocial { get; set; }
        public string StatusDaConta { get; set; }
        public List<string> TelefoneDoEstabelecimento { get; set; }
        public List<string> TelefoneDoResponsavelFinaceiro { get; set; }
        public List<LinksImagensestabelecimentoDTO> FotosDoEstabelecimento { get; set; }
        public string UrlDoEstabelecimentoHotSite { get; set; }
        public string TextoDataDeCadastro { get; set; }
        public string TextoDataDaAssinatura { get; set; }
        public string TextoDataFimDoPeriodoGratis { get; set; }
        public string NumeroTotalDeProfissionaisComAgenda { get; set; }
        public string FaixaDeProfissionais { get; set; }
        public string FaixaDeProfissionaisAssinada { get; set; }
        public string NumeroTotalDeProfissionaisAtivos { get; set; }
        public string SolicitacaoParaAparecerNoPortal { get; set; }
        public string EstaNoPortal { get; set; }
        public string EstaComAgendamentoOnlineDisponivel { get; set; }
        public string EstaHabilitadoNaVitrineParaAparecerNoPortal { get; set; }
        public string TextoUltimoAgendamento { get; set; }
        public string TextoUltimoAgendamentoOnline { get; set; }
        public string TextoUltimoFechamentoDeConta { get; set; }
        public string ProgramaDeFidelidadeEstaAtivo { get; set; }
        public string ProgramaDeFidelidadeEstaConfigurado { get; set; }
        public string JaDeuAlgumPonto { get; set; }
        public string JaHouveAlgunResgateDePontos { get; set; }
        public string TextoUltimosPontosDados { get; set; }
        public string TextoUltimosPontosResgatados { get; set; }
        public string EstaComABuscaDeHorarioDisponivel { get; set; }
        public bool HotSiteDisponivel { get; set; }
        public string EnderecoResumidoDoEstabelecimento { get; set; }
        public string LinkSistemaExterno { get; set; }
        public string CodigoParceria { get; set; }
        public List<string> CodigosParceria { get; set; }

        public string UrlUltimaFatura { get; set; }
        public string DataUltimaFatura { get; set; }

        public string ResponsavelAtendimento { get; set; }
        public bool ExisteAlgumaFaturaPaga { get; set; }
        public string OrigemDoLead { get; set; }
        public string MidiaDePublicidade { get; set; }
        public string CampanhaDeMarketing { get; set; }
        public string PalavrasChaveDaCampanha { get; set; }
        public string ResumoDaCampanhaDeMarketing { get; set; }
        public string Dispositivo { get; set; }
        public string ConcluiuConfiguracaoTipoPOS { get; set; }
        public string DataUltimaFechamentoContaBelezinha { get; set; }
        public string NomeTipoEstabelecimento { get; set; }
        public string NomeTipoIntegracao { get; set; }
        public bool PagarmeAtiva { get; set; }
        public string IdRecebedorPagarme { get; set; }
        public string VersaoPagarme { get; set; }
    }

    public class LinksImagensestabelecimentoDTO
    {
        public string FotoEstabelecimentoDim101x74 { get; set; }
        public string FotoEstabelecimentoDimOriginal { get; set; }
    }
}