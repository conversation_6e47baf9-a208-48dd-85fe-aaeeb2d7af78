﻿using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.Pessoas.Repositories
{
    public static class QueriesDeItemComboCliente
    {
        /// <summary>
        /// Busca itens da combo de clientes onde a coluna de telefone contenha o número informado.
        /// </summary>
        /// <param name="source">IQueryable de ItemComboCliente quer será modificado com os critérios da pesquisa por telefone</param>
        /// <param name="telefone">Número de telefone a ser pesquisado.<br/><br/>
        /// 
        /// Se for passado um parâmetro que corresponda a um número de telefone parcial ou completo (com ou sem máscara, com ou sem DDD)
        /// a busca é feita de forma a encontrar telefones em todas as máscaras possíveis.<br/><br/>
        /// 
        /// Se for passado um parâmetro que contenha mais de 11 dígitos ou que contenha outros caracteres, será feita uma busca
        /// usando o texto exato informado. Neste caso <strong>não</strong> será feita a variação das máscaras de telefone.<br/><br/>
        /// 
        /// Ex.: Se for pesquisado o texto "989865" serão retornados registros onde o telefone possa conter:<br/><br/>
        /// 
        /// <list type="bullet">
        /// <item>"(98) 9865"   (assumindo os dois primeiros dígitos como o DDD)</item>
        /// <item>"9898-65"     (assumindo os quatro primeiros dígitos como um prefixo de telefone fixo sem DDD)</item>
        /// <item>"98986-5"     (assumindo os cinco primeiros dígitos como prefixo de um número móvel com 9 dígitos)</item>
        /// </list>
        /// </param>
        /// <returns></returns>
        public static IQueryable<ItemComboCliente> ComTelefoneQueContenha(this IQueryable<ItemComboCliente> source, string telefone)
        {
            var expressaoDeConsulta = PredicateBuilder.False<ItemComboCliente>().BuscaPorTelefone(telefone);

            return source.Where(expressaoDeConsulta);
        }

        /// <summary>
        /// Busca itens da combo de clientes onde a coluna de telefone contenha o número informado.
        /// </summary>
        /// <param name="source">IQueryable de ItemComboCliente quer será modificado com os critérios da pesquisa por telefone</param>
        /// <param name="telefone">Número de telefone a ser pesquisado.<br/><br/>
        /// 
        /// Se for passado um parâmetro que corresponda a um número de telefone parcial ou completo (com ou sem máscara, com ou sem DDD)
        /// a busca é feita de forma a encontrar telefones em todas as máscaras possíveis.<br/><br/>
        /// 
        /// Se for passado um parâmetro que contenha mais de 11 dígitos ou que contenha outros caracteres, será feita uma busca
        /// usando o texto exato informado. Neste caso <strong>não</strong> será feita a variação das máscaras de telefone.<br/><br/>
        /// 
        /// Ex.: Se for pesquisado o texto "989865" serão retornados registros onde o telefone possa conter:<br/><br/>
        /// 
        /// <list type="bullet">
        /// <item>"(98) 9865"   (assumindo os dois primeiros dígitos como o DDD)</item>
        /// <item>"9898-65"     (assumindo os quatro primeiros dígitos como um prefixo de telefone fixo sem DDD)</item>
        /// <item>"98986-5"     (assumindo os cinco primeiros dígitos como prefixo de um número móvel com 9 dígitos)</item>
        /// </list>
        /// </param>
        /// <returns></returns>
        public static Expression<Func<ItemComboCliente, bool>> BuscaPorTelefone(this Expression<Func<ItemComboCliente, bool>> source,
            string telefone)
        {

            // obtém o texto sem espaços, parênteses ou traços.
            string textoSemCaracteresDeTelefone = Regex.Replace(telefone.Trim(), "[\\s\\()-]", String.Empty);

            // se o texto possuir apenas números, busca por todas as máscaras de telefone possíveis
            if (Regex.IsMatch(textoSemCaracteresDeTelefone, "^[0-9]+$") &&
                    textoSemCaracteresDeTelefone.Length > 0 &&
                    textoSemCaracteresDeTelefone.Length <= 11)
            {

                List<string> telefones = new FormatadorDeTelefone().Formatar(textoSemCaracteresDeTelefone).ToList();
                telefones.Add(textoSemCaracteresDeTelefone);
                foreach (var t in telefones)
                {
                    source = source.Or(ic => ic.TelefonesInexado.Contains(t));
                }

            }
            else
            {

                // senão realiza a pesquisa como era feita anteriormente
                source = source.Or(ic => ic.TelefonesInexado.Contains(telefone));

            }

            return source;
        }
    }
}
