using Castle.ActiveRecord;
using System;

namespace Perlink.Trinks.Pessoas
{

    [ActiveRecord("Estabelecimento_Cliente_Saldos", DynamicInsert = true, DynamicUpdate = true)]
    [Serializable]
    public class ClienteEstabelecimentoSaldos
    {
        [PrimaryKey(PrimaryKeyType.Assigned, "id_estabelecimento_cliente")]
        public virtual int Id { get; set; }

        [Property("total_debitos")] // Populado por trigger
        public virtual decimal? TotalDebitos { get; set; }
    }
}