﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Services
{
    public class TelefoneService : BaseService, ITelefoneService
    {

        public void AssociarOperadorasATelefonesSemOperadora()
        {
            var telefones = Domain.Pessoas.TelefoneRepository.Queryable().Where(p => p.Operadora == null).ToList();

            foreach (Telefone telefone in telefones.Take(10))
            {
                Int64 numeroTelefone = 0;
                Int64.TryParse(telefone.Numero, out numeroTelefone);
                AssociarOperadoraAoTelefonePeloNumero(telefone, numeroTelefone);
            }
        }

        public void AtualizarOperadorasDeTelefones()
        {
            var telefones = Domain.Pessoas.TelefoneRepository.Queryable().Where(p => p.Operadora != null).ToList();

            foreach (Telefone telefone in telefones.Take(10))
            {
                Int64 numeroTelefone = 0;
                Int64.TryParse(telefone.Numero, out numeroTelefone);
                AssociarOperadoraAoTelefonePeloNumero(telefone, numeroTelefone);
            }
        }

        private void AssociarOperadoraAoTelefonePeloNumero(Telefone telefone, Int64 numeroTelefone)
        {

            if (numeroTelefone >= 000000000 && numeroTelefone <= 900000000)
                telefone.Operadora = Domain.Notificacoes.OperadoraRepository.Load((int)OperadoraEnum.Oi);

            if (numeroTelefone >= 900000001 && numeroTelefone <= 944444444)
                telefone.Operadora = Domain.Notificacoes.OperadoraRepository.Load((int)OperadoraEnum.Vivo);

            if (numeroTelefone >= 944444445 && numeroTelefone <= 966666666)
                telefone.Operadora = Domain.Notificacoes.OperadoraRepository.Load((int)OperadoraEnum.Tim);

            if (numeroTelefone >= 966666667 && numeroTelefone <= 988888888)
                telefone.Operadora = Domain.Notificacoes.OperadoraRepository.Load((int)OperadoraEnum.Claro);

            if (numeroTelefone >= 988888889 && numeroTelefone <= 999999999)
                telefone.Operadora = Domain.Notificacoes.OperadoraRepository.Load((int)OperadoraEnum.Nextel);
        }

        public void ManterTelefonesNovosAtualizadosERemovidos(List<TelefoneDTO> listaAtualizada, PessoaFisica pessoaFisica, string versaoApp = "")
        {

            var telefonesProprios = Domain.Pessoas.TelefoneRepository.ListarTelefonesPropriosDaPessoa(pessoaFisica.IdPessoa, false);
            var exibeEAtualizaTelefonesInternacionais = PermiteExibirTelefonesInternacionais(versaoApp);

            telefonesProprios = exibeEAtualizaTelefonesInternacionais ? telefonesProprios : telefonesProprios.Where(tel => tel.Ddi == DdiConstants.Brasil).ToList();

            foreach (var telefoneExistente in telefonesProprios)
            {
                var dadosAtualizadosDoTelefoneExistente = listaAtualizada.FirstOrDefault(t => t.Id == telefoneExistente.IdTelefone);

                if (dadosAtualizadosDoTelefoneExistente != null)
                {

                    bool telefoneFoiAlterado = dadosAtualizadosDoTelefoneExistente.DDD != telefoneExistente.DDD ||
                                               dadosAtualizadosDoTelefoneExistente.Numero != telefoneExistente.Numero;

                    telefoneExistente.Ativo = true;
                    telefoneExistente.DDD = dadosAtualizadosDoTelefoneExistente.DDD.SomenteNumeros();
                    telefoneExistente.Numero = dadosAtualizadosDoTelefoneExistente.Numero.SomenteNumeros();

                    if (telefoneFoiAlterado)
                        telefoneExistente.Operadora = null;
                }
                else
                {
                    telefoneExistente.Ativo = false;
                }

                Domain.Pessoas.TelefoneRepository.UpdateNoFlush(telefoneExistente);
            }


            var novosTelefones = listaAtualizada.Where(t => t.Id == null || t.Id == 0).ToList();

            foreach (var telefone in novosTelefones)
            {
                var novoTelefone = Domain.Pessoas.TelefoneRepository.Factory.CreateNovoTelefoneCadastradoPelaPessoaFisica(telefone.DDD, telefone.Numero, pessoaFisica);
                Domain.Pessoas.TelefoneRepository.SaveNewNoFlush(novoTelefone);
            }

            Domain.Pessoas.TelefoneRepository.Flush();
        }

        public IEnumerable<Telefone> ObterTelefonesAtivosDoEstabelecimentoLogado(int idEstabelecimento)
        {
            var telefones = (from e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                             join p in Domain.Pessoas.PessoaRepository.Queryable()
                                 on e.PessoaJuridica.IdPessoa equals p.IdPessoa
                             join t in Domain.Pessoas.TelefoneRepository.Queryable()
                                 on p.IdPessoa equals t.IdPessoa
                             where e.IdEstabelecimento == idEstabelecimento && t.Ativo
                             orderby t.IdTelefone descending
                             select t).ToList();

            return telefones;
        }

        public IEnumerable<Telefone> ObterTelefonesComWhatAppAtivosDoEstabelecimentoLogado(int idEstabelecimento)
        {
            return (from e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    join p in Domain.Pessoas.PessoaRepository.Queryable()
                        on e.PessoaJuridica.IdPessoa equals p.IdPessoa
                    join t in Domain.Pessoas.TelefoneRepository.Queryable()
                        on p.IdPessoa equals t.IdPessoa
                    join tt in Domain.Pessoas.TipoTelefoneRepository.Queryable()
                        on t.Tipo.IdTipoTelefone equals tt.IdTipoTelefone
                    where e.IdEstabelecimento == idEstabelecimento
                    && t.Ativo
                    && tt.Nome == "WhatsApp"
                    orderby t.IdTelefone descending
                    select t).ToList();
        }

        public void AtualizarPrincipalTelefoneDeContatoDoEstabelecimentoLogado(string ddd, string numero, bool seEhWhatsApp)
        {
            var pessoa = (from e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                          where e.IdEstabelecimento == ContextHelper.Instance.IdEstabelecimento.Value
                          select e.PessoaJuridica).First();

            var telefones = (from t in Domain.Pessoas.TelefoneRepository.Queryable()
                             where t.IdPessoa == pessoa.IdPessoa
                             select t).ToList();

            foreach (var telefone in telefones)
            {
                telefone.Ativo = false;
                Domain.Pessoas.TelefoneRepository.UpdateNoFlush(telefone);
            }

            var telefoneAhSerSalvo = telefones.FirstOrDefault(t => t.Numero == numero && t.DDD == ddd);

            if (telefoneAhSerSalvo == null)
            {
                Domain.Pessoas.TelefoneRepository.SaveNewNoFlush(new Telefone
                {
                    DDD = ddd,
                    Ativo = true,
                    Dono = pessoa,
                    Pessoa = pessoa,
                    Numero = numero,
                    IdPessoa = pessoa.IdPessoa,
                    Tipo = ObterTipoTelefone(numero, seEhWhatsApp),
                });
            }
            else
            {
                telefoneAhSerSalvo.Ativo = true;
                telefoneAhSerSalvo.Tipo = ObterTipoTelefone(numero, seEhWhatsApp);
                Domain.Pessoas.TelefoneRepository.UpdateNoFlush(telefoneAhSerSalvo);
            }

            Domain.Pessoas.TelefoneRepository.Flush();
        }

        private TipoTelefone ObterTipoTelefone(string numero, bool seEhWhatsApp)
        {
            if (seEhWhatsApp)
                return Domain.Pessoas.TipoTelefoneRepository.Queryable(true).First(x => x.Nome == TipoTelefoneEnum.WhatsApp.ToString());
            else if (numero.EhUmTelefoneCelular())
                return TipoTelefoneEnum.Celular;
            else
                return TipoTelefoneEnum.Comercial;
        }

        public Telefone ObterTelefoneAtivoDoEstabelecimentoLogado()
        {
            var telefones = (from e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                             join p in Domain.Pessoas.PessoaRepository.Queryable()
                                 on e.PessoaJuridica.IdPessoa equals p.IdPessoa
                             join t in Domain.Pessoas.TelefoneRepository.Queryable()
                                 on p.IdPessoa equals t.IdPessoa
                             where e.IdEstabelecimento == ContextHelper.Instance.IdEstabelecimento.Value && t.Ativo
                             orderby t.IdTelefone descending
                             select t);

            return telefones.FirstOrDefault();
        }

        public Telefone ObterTelefoneParaComunicacaoWhatsApp(IList<Telefone> telefones, int idPessoaJuridicaEstabelecimento)
        {
            Telefone result = null;
            var tipoWhatsApp = TipoTelefoneEnum.WhatsApp.GetHashCode();
            telefones = telefones.Where(t => t.Ddi == DdiConstants.Brasil).ToList();
            telefones = telefones.Where(t => t.Dono.IdPessoa == t.IdPessoa || t.Dono.IdPessoa == idPessoaJuridicaEstabelecimento).ToList();
            IList<Telefone> celulares = telefones.Where(t => t.Ativo && ((t.Tipo?.IdTipoTelefone ?? 0) == tipoWhatsApp
            || t.Numero.EhUmTelefoneCelular())).ToList();

            if (celulares.Any(t => t.Dono.IdPessoa == idPessoaJuridicaEstabelecimento))
                celulares = celulares.Where(t => t.Dono.IdPessoa == idPessoaJuridicaEstabelecimento).ToList();

            if (celulares.Any(t => (t.Tipo?.IdTipoTelefone ?? 0) == tipoWhatsApp))
                result = celulares.Where(t => (t.Tipo?.IdTipoTelefone ?? 0) == tipoWhatsApp).OrderByDescending(t => t.IdTelefone).FirstOrDefault();
            else
                result = celulares.OrderByDescending(t => t.IdTelefone).FirstOrDefault();

            return result;
        }

        public List<TelefoneDTO> ValidarExibicaoTelefonesInternacionaisAppB2c(List<TelefoneDTO> telefones, string versaoDoApp)
        {
            if (PermiteExibirTelefonesInternacionais(versaoDoApp))
                return telefones;

            return telefones.Where(tel => tel.Ddi == DdiConstants.Brasil).ToList();
        }

        private bool PermiteExibirTelefonesInternacionais(string versaoDoApp)
        {
            if (string.IsNullOrEmpty(versaoDoApp))
                return false;

            var permiteVisualizarTelefoneInternacionalAppB2c = Domain.ControleDeFuncionalidades
                        .DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(0,
                        Recurso.VisualizarTelefonesInternacionaisAppB2C).EstaDisponivel;

            var versaoMinima = new ParametrosTrinks<string>(ParametrosTrinksEnum.versao_minima_app_b2c_telefone_internacional).ObterValor();

            return permiteVisualizarTelefoneInternacionalAppB2c && Domain.Notificacoes.DispositivoService.VersaoEhMaiorOuIgual(versaoDoApp, versaoMinima);
        }
    }
}
