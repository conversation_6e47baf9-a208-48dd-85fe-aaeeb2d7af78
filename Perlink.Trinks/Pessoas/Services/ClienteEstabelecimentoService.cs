﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Enums;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.IntegracaoComOutrosSistemas;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.Marcadores.DTO;
using Perlink.Trinks.Marcadores.Enums;
using Perlink.Trinks.Onboardings.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Filtros;
using Perlink.Trinks.Pessoas.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using RES = Perlink.Trinks.Resources.Pessoas.Services.ClienteEstabelecimentoService;

namespace Perlink.Trinks.Pessoas.Services
{

    public class ClienteEstabelecimentoService : BaseService, IClienteEstabelecimentoService
    {

        #region Propriedades de Apoio

        private IClienteEstabelecimentoRepository ClienteEstabelecimentoRepository
        {
            get { return Domain.Pessoas.ClienteEstabelecimentoRepository; }
        }

        private IClienteRepository ClienteRepository
        {
            get { return Domain.Pessoas.ClienteRepository; }
        }

        private IClienteService ClienteService
        {
            get { return Domain.Pessoas.ClienteService; }
        }

        private IEnvioEmailService EnvioEmailService
        {
            get { return Domain.Pessoas.EnvioEmailService; }
        }

        private IEstabelecimentoRepository EstabelecimentoRepository
        {
            get { return Domain.Pessoas.EstabelecimentoRepository; }
        }

        #endregion Propriedades de Apoio

        public void AssociarClienteAoEstabelecimento(Int32 idCliente, Int32 idEstabelecimento)
        {
            var cliente = ClienteRepository.Load(idCliente);
            var estabelecimento = EstabelecimentoRepository.Load(idEstabelecimento);

            var estabelecimentoCliente = new ClienteEstabelecimento
            {
                Cliente = cliente,
                Estabelecimento = estabelecimento,
                Ativo = true
            };

            ManterCliente(estabelecimentoCliente);
        }

        public void AssociarClienteAoEstabelecimento(List<Int32> idClientes, Int32 idEstabelecimento)
        {
            var estabelecimento = EstabelecimentoRepository.Load(idEstabelecimento);

            foreach (var idCliente in idClientes)
            {
                var cliente = ClienteRepository.Load(idCliente);

                var estabelecimentoCliente = new ClienteEstabelecimento
                {
                    Cliente = cliente,
                    Estabelecimento = estabelecimento,
                    Ativo = true
                };

                ManterCliente(estabelecimentoCliente);
            }
        }

        public ClienteEstabelecimento BuscaPorClienteExistente(ParametrosBuscaClienteEstabelecimento parametros,
            Boolean priorizarPeloCPF)
        {
            ClienteEstabelecimento cliente = null;

            if (!String.IsNullOrEmpty(parametros.Cpf))
                cliente = BuscarPorClienteEstabelecimentoPeloCpf(parametros);

            if (cliente != null && priorizarPeloCPF)
                return cliente;

            if (!String.IsNullOrEmpty(parametros.Email))
                cliente = BuscarPorClienteEstabelecimentoPeloEmail(parametros);

            return cliente;
        }

        public bool ClienteEstaEmDebito(int idEstabelecimento, int idCliente)
        {
            return Domain.Pessoas.HorarioRepository.ClienteEstaEmDebito(idEstabelecimento, idCliente);
        }

        public void DefinirEnvioNotificacaoClienteEstabelecimentoNaoDefinido(Int32 idEstabelecimento, bool novoValor)
        {
            var clientesEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository
                    .ObterClientesEstabelecimentoComEnvioNotificacaoNaoDefinido(idEstabelecimento);

            foreach (var clienteEstabelecimento in clientesEstabelecimento)
            {
                clienteEstabelecimento.RecebeNotificacao = novoValor;
            }
        }

        public List<ClienteEstabelecimento> ListarAniversariantes(int idEstabelecimento, DateTime dataComparacao)
        {
            return ClienteEstabelecimentoRepository.ListarAniversariantes(idEstabelecimento, dataComparacao);
        }

        public List<ClienteEstabelecimento> ListarClienteEstabelecimentoPorCpf(string email)
        {
            return ClienteEstabelecimentoRepository.ListarClienteEstabelecimentoPorCpf(email);
        }

        public List<ClienteEstabelecimento> ListarClienteEstabelecimentoPorEmail(string email)
        {
            return ClienteEstabelecimentoRepository.ListarClienteEstabelecimentoPorEmail(email);
        }

        public IQueryable<ClienteEstabelecimento> ListarClientes(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool veTelefone)
        {
            return ClienteEstabelecimentoRepository.ObterClientesComFiltro(parametros, filtraPorCPF, filtraPorEmail, veTelefone);
        }

        public ResultadoPaginado<ClienteEstabelecimento> ListarClientesPorEstabelecimento(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool veTelefone)
        {
            return ClienteEstabelecimentoRepository.ListarPorEstabelecimentoPaginado(parametros, filtraPorCPF, filtraPorEmail, veTelefone);
        }

        public List<ClienteEstabelecimento> ListarClientesPorEstabelecimento(Int32 codigoEstabelecimento)
        {
            return ClienteEstabelecimentoRepository.ListarPorEstabelecimento(codigoEstabelecimento).ToList();
        }

        [TransactionInitRequired]
        public ClienteEstabelecimento ManterCliente(DadosClienteDTO dto)
        {
            var pessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Queryable().FirstOrDefault(f => f.IdGoogleReserveUser == dto.IdGoogleReserveUser)
                ?? new PessoaFisica
                {
                    NomeCompleto = dto.Nome,
                    IdGoogleReserveUser = dto.IdGoogleReserveUser
                };

            if (pessoaFisica.IdPessoa == 0)
            {
                pessoaFisica.Telefones.Add(new Telefone { DDD = dto.DDD, Numero = dto.Telefone, Dono = pessoaFisica, Pessoa = pessoaFisica });
                pessoaFisica.Email = dto.Email;
            }
            var cliente = pessoaFisica.IdPessoa > 0
                ? Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(pessoaFisica.IdPessoa)
                : new Cliente
                {
                    PessoaFisica = pessoaFisica
                };

            ClienteEstabelecimento clienteEstabelecimento = null;

            if (cliente.IdCliente > 0)
                clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(cliente.IdCliente, dto.IdEstabelecimento);

            if (clienteEstabelecimento == null)
                clienteEstabelecimento = new ClienteEstabelecimento
                {
                    Estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(dto.IdEstabelecimento),
                    Cliente = cliente,
                    NomeCompletoCliente = dto.Nome
                };

            clienteEstabelecimento.Ativo = true;

            ManterCliente(clienteEstabelecimento);

            return clienteEstabelecimento;
        }

        [TransactionInitRequired]
        public void ManterCliente(ClienteEstabelecimento ce, bool enviaEmail = true, bool origemAgendamentoHotiste = false,
            int codigoEstabelecimentoRedirecionar = 0, bool unificar = true, bool forcarEnvioDeEmailDeConfirmacaoDeConta = false,
            bool confirmarGuidDaConta = false, int? codigoEstabelecimento = null, List<int> idsEtiquetasAssociadas = null)
        {
            var conta = ce.Cliente.PessoaFisica.Contas.FirstOrDefault(f => f.Ativo);
            ce = ApagarEmailAlternativoDeClienteEstabelecimento(ce);

            if (conta != null)
            {
                ce.Cliente.TipoCliente = TipoClienteEnum.Web;
                //if (ce.Estabelecimento != null && !conta.Confirmada && confirmarGuidDaConta)
                //    conta.DataConfirmacaoGuid = Calendario.Agora(); Confirmação ocorrerá agora por sms ou email
            }
            else if (ce.Cliente.TipoCliente != TipoClienteEnum.Web)
                ce.Cliente.TipoCliente = TipoClienteEnum.Balcao;

            if (ce.Estabelecimento == null)
                ManterCliente(ce.Cliente, unificar);
            else
            {
                ManterClienteEstabelecimento(ce, enviaEmail, unificar, forcarEnvioDeEmailDeConfirmacaoDeConta, origemAgendamentoHotiste, idsEtiquetasAssociadas);
            }
        }

        public ClienteEstabelecimento ObterClienteEstabelecimento(Int32 codigoCliente, Int32 codigoEstabelecimento)
        {
            return ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(codigoCliente, codigoEstabelecimento);
        }

        public ClienteEstabelecimento ObterClienteEstabelecimento(String cpfCliente, Int32 codigoEstabelecimento)
        {
            var clienteEstabelecimento =
                ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoIncluindoInativo(cpfCliente,
                    codigoEstabelecimento);

            if (clienteEstabelecimento != null)
                return clienteEstabelecimento;

            var cliente = ClienteRepository.ObterClienteWebOuBalcaoDoEstabelecimentoPorCpf(cpfCliente, codigoEstabelecimento);
            if (cliente != null)
            {
                clienteEstabelecimento = new ClienteEstabelecimento
                {
                    Cliente = cliente,
                    Estabelecimento = EstabelecimentoRepository.Load(codigoEstabelecimento)
                };
            }

            return clienteEstabelecimento;
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorIdPessoa(int idPessoa, int idEstabelecimento)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(idPessoa, idEstabelecimento);


            if (clienteEstabelecimento != null)
                return clienteEstabelecimento;

            var cliente = ClienteRepository.ObterClienteWebOuBalcaoDoEstabelecimentoPorIdPessoa(idPessoa, idEstabelecimento);

            if (cliente != null)
            {
                clienteEstabelecimento = GerarNovoClienteEstabelecimento(idEstabelecimento, cliente);
            }

            return clienteEstabelecimento;
        }

        private ClienteEstabelecimento GerarNovoClienteEstabelecimento(int idEstabelecimento, Cliente cliente)
        {

            ClienteEstabelecimento clienteEstabelecimento = new ClienteEstabelecimento
            {
                Cliente = cliente,
                Estabelecimento = EstabelecimentoRepository.Load(idEstabelecimento)
            };

            Domain.Pessoas.ClienteEstabelecimentoRepository.SaveNew(clienteEstabelecimento);

            return clienteEstabelecimento;
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoValidado(string cpf,
            Int32 codigoEstabelecimento,
            Int32? codigoDoClienteEstabelecimento)
        {
            ValidarCPFPreenchido(cpf);
            ValidarCPF(cpf);
            if (codigoDoClienteEstabelecimento.HasValue && codigoDoClienteEstabelecimento.Value > 0)
                ValidarCPFJaCadastrado(cpf, codigoEstabelecimento, codigoDoClienteEstabelecimento.Value);
            else
                ValidarCPFJaCadastrado(cpf, codigoEstabelecimento);

            if (ValidationHelper.Instance.IsValid)
                return ObterClienteEstabelecimento(cpf, codigoEstabelecimento);
            return null;
        }

        public ClienteEstabelecimento ObterClientePorEmail(string email, int idEstabelecimento)
        {
            return ClienteEstabelecimentoRepository.ObterPorEmail(email, idEstabelecimento);
        }

        public ClienteEstabelecimento ObterClientePorEmailAssociacao(string email, int idEstabelecimento)
        {
            return ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorEmailAssociacao(email,
                idEstabelecimento);
        }

        public void Reativar(int codigoClienteEstabelecimento)
        {
            var clienteEstabelecimento =
                Domain.Pessoas.ClienteEstabelecimentoRepository.Load(codigoClienteEstabelecimento);
            clienteEstabelecimento.Ativo = true;

            foreach (var telefone in clienteEstabelecimento.TelefonesProprios)
                telefone.Ativo = true;

            Domain.Pessoas.ClienteEstabelecimentoRepository.Update(clienteEstabelecimento);
        }

        [TransactionInitRequired]
        public void RemoverClienteEstabelecimento(int idClienteEstabelecimento)
        {
            var clienteEstabelecimento = ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            clienteEstabelecimento.Ativo = false;

            //Ao passar a lista vazia, ele inativa todos
            foreach (var telefone in clienteEstabelecimento.TelefonesProprios)
                telefone.Ativo = false;

            ClienteEstabelecimentoRepository.Update(clienteEstabelecimento);
        }

        [TransactionInitRequired]
        public ClienteEstabelecimento TransformarPessoaEmCliente(PessoaFisica pessoaFisica, bool enviaEmail = true)
        {
            var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(pessoaFisica.IdPessoa) ?? (ClienteService.Obter(pessoaFisica.Cpf) ?? new Cliente { PessoaFisica = pessoaFisica });

            if (pessoaFisica.Contas.Any(f => f.Ativo))
            {
                cliente.PessoaFisica = pessoaFisica;
            }

            if (cliente.PessoaFisica.Contas.Any(f => f.Ativo))
                cliente.TipoCliente = TipoClienteEnum.Web;

            var clienteEstabelecimento = new ClienteEstabelecimento { Cliente = cliente, Estabelecimento = null };

            ManterCliente(clienteEstabelecimento, enviaEmail);

            return clienteEstabelecimento;
        }

        [TransactionInitRequired]
        public ClienteEstabelecimento TransformarPessoaEmCliente(PessoaFisica pessoaFisica,
            Estabelecimento estabelecimento,
            bool enviaEmail = true)
        {
            Cliente cliente;

            if (pessoaFisica.Cpf == null)
                cliente = ClienteService.ObterPorEmailParaTransformarPessoaEmCliente(pessoaFisica.Email) ?? TransformarPessoaEmCliente(pessoaFisica).Cliente;
            else
                cliente = ClienteService.Obter(pessoaFisica.Cpf) ?? TransformarPessoaEmCliente(pessoaFisica).Cliente;

            var clienteEstabelecimento =
                cliente.ClientesEstabelecimento.ToClienteEstabelecimento(estabelecimento.IdEstabelecimento);
            if (clienteEstabelecimento == null)
            {
                clienteEstabelecimento = new ClienteEstabelecimento
                {
                    Cliente = cliente,
                    Estabelecimento = estabelecimento
                };
                ManterCliente(clienteEstabelecimento, enviaEmail);
            }
            return clienteEstabelecimento;
        }

        [TransactionInitRequired]
        public bool EmailAlternativoJaCadastradoParaOutroCliente(string email, int idEstabelecimento)
        {
            var clienteEstabelecimento = ObterClientePorEmail(email, idEstabelecimento);

            if (clienteEstabelecimento == null)
            {
                var conta = Domain.Pessoas.ContaService.ObterContaAtivaPorEmail(email);

                if (conta != null)
                {
                    var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(conta.Pessoa.PessoaFisica.IdPessoaFisica);
                    if (cliente == null)
                        return false;
                    if (cliente.TipoCliente == TipoClienteEnum.Web)
                        return true;
                    else
                        return false;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return true;
            }
        }

        private ClienteEstabelecimento BuscarPorClienteEstabelecimentoPeloCpf(
            ParametrosBuscaClienteEstabelecimento parametros)
        {
            ClienteEstabelecimento clienteEstabelecimento = null;
            var cliente = ClienteRepository.ObterClienteWebOuBalcaoDoEstabelecimentoPorCpf(parametros.Cpf,
                parametros.IdEstabelecimento);

            if (cliente != null)
            {
                clienteEstabelecimento =
                    ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(cliente.IdCliente,
                        parametros.IdEstabelecimento) ?? new ClienteEstabelecimento { Cliente = cliente };
            }

            return clienteEstabelecimento;
        }

        private ClienteEstabelecimento BuscarPorClienteEstabelecimentoPeloEmail(
            ParametrosBuscaClienteEstabelecimento parametros)
        {
            return ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorEmail(parametros.Email,
                parametros.IdEstabelecimento);
        }

        private void ManterCliente(Cliente entity, bool enviaEmailBoasVindas, bool unificar = true)
        {
            var ehCadastro = entity.IdCliente <= 0;
            ValidarCliente(entity);
            var conta = entity.PessoaFisica.PrimeiraConta;

            var contaAtual = Domain.Pessoas.ContaService.ObterContaAutenticada() ?? conta;

            if (ValidationHelper.Instance.IsValid)
            {
                var idPessoaQueAlterou = contaAtual?.Pessoa?.IdPessoa;

                if (unificar)
                {
                    ClienteRepository.SaveOrUpdate(entity, idPessoaQueAlterou);
                    Domain.Pessoas.UnificacaoService.Unificar(entity.PessoaFisica, entity);
                }

                if (entity.TipoCliente == TipoClienteEnum.Web)
                {
                    if (ehCadastro && enviaEmailBoasVindas)
                    {
                        if (conta.Confirmada)
                            Domain.Pessoas.EnvioEmailService.EnviarEmailBoasVindasClienteWeb(entity);
                    }
                }

                ClienteRepository.SaveOrUpdate(entity, idPessoaQueAlterou);

                //if (forcarEnvioDeEmailDeConfirmacaoDeConta && !conta.Confirmada) {
                //    //var estabelecimento =
                //    EnvioEmailService.EnviarEmailAtivacaoCadastroCliente(conta, origemPorAgendamentoHotsite, codigoEstabelecimentoRedirecionar, codigoEstabelecimento: codigoEstabelecimento);
                //}
                //else if (ehCadastro && enviaEmailBoasVindas) {
                //    if (conta != null && !conta.Confirmada)
                //        EnvioEmailService.EnviarEmailAtivacaoCadastroCliente(conta, origemPorAgendamentoHotsite, codigoEstabelecimentoRedirecionar, codigoEstabelecimento: codigoEstabelecimento);
                //}
            }
            else
                ClienteRepository.Clear();
        }

        private void ManterClienteBalcao(ClienteEstabelecimento entity, bool enviaEmail)
        {
            var emailClienteEstabelecimentoFoiAlterado = true;
            ValidarClienteBalcao(entity);

            if (entity.Codigo > 0)
            {
                emailClienteEstabelecimentoFoiAlterado =
                    !ClienteEstabelecimentoRepository.ExisteClienteEstabelecimentoComEmail(entity.Codigo,
                        entity.Cliente.PessoaFisica.Email);

                entity.Cliente.PessoaFisica.NomeCompleto = entity.NomeCompletoCliente;

                if (string.IsNullOrWhiteSpace(entity.Cliente.PessoaFisica.Genero))
                {
                    entity.Cliente.PessoaFisica.Genero = entity.Genero;
                }
            }

            if (string.IsNullOrWhiteSpace(entity.Cliente.PessoaFisica.NomeCompleto))
                entity.Cliente.PessoaFisica.NomeCompleto = entity.NomeCompletoCliente;

            entity.Cliente.PessoaFisica.Genero = entity.Genero;
            entity.Cliente.PessoaFisica.DataNascimento = entity.DataNascimentoCliente;

            entity.Cliente.PessoaFisica.Ativo = true;

            if (ValidationHelper.Instance.IsValid)
            {
                var conta = Domain.Pessoas.ContaService.ObterContaAutenticada() ?? entity.Cliente?.PessoaFisica?.PrimeiraConta;

                var idPessoaQueAlterou = conta?.Pessoa?.IdPessoa;
                ClienteEstabelecimentoRepository.SaveOrUpdate(entity, idPessoaQueAlterou);
                var cancelarEnvioEmailCadastroClienteBalcao = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.ObterPorIdEstabelecimento(entity.Estabelecimento.IdEstabelecimento).CancelarEnvioEmailCadastroClienteBalcao;

                if (emailClienteEstabelecimentoFoiAlterado && !string.IsNullOrWhiteSpace(entity.Cliente.PessoaFisica.Email) && enviaEmail && !cancelarEnvioEmailCadastroClienteBalcao)
                    EnvioEmailService.EnviarEmailClienteBalcaoCadastrado(entity);

                Domain.Onboardings.RastreioTarefaService.IncrementarRastreio(ETarefaOnboarding.CadastrarCliente, entity.Estabelecimento.IdEstabelecimento);
            }
            else
                ClienteEstabelecimentoRepository.Clear();
        }

        private void ManterClienteEstabelecimento(ClienteEstabelecimento clienteEstabelecimento, bool enviaEmail, bool unificar = true, bool forcarEnvioDeEmailDeConfirmacaoDeConta = false, bool origemAgendamentoHotiste = false, List<int> idsEtiquetasAssociadas = null)
        {
            bool ehCadastroNovo = clienteEstabelecimento.Codigo == 0;
            if (clienteEstabelecimento.Cliente.TipoCliente == TipoClienteEnum.Balcao)
                ManterClienteBalcao(clienteEstabelecimento, enviaEmail);
            else
                ManterClienteWeb(clienteEstabelecimento, enviaEmail, unificar, forcarEnvioDeEmailDeConfirmacaoDeConta, origemAgendamentoHotiste);

            if (ehCadastroNovo && idsEtiquetasAssociadas != null && idsEtiquetasAssociadas.Any())
            {
                var objParaEtiquetar = new EtiquetarObjetoDTO(clienteEstabelecimento.Estabelecimento.IdEstabelecimento, TipoDeEtiquetaEnum.ClienteEstabelecimento, clienteEstabelecimento.Codigo, idsEtiquetasAssociadas);
                Domain.Marcadores.ManterEtiquetasService.ManterEtiquetasDoObjeto(objParaEtiquetar);
            }

            var configuracaoGeral = clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral;
            if (configuracaoGeral.EnvioDeNotificacaoHabilitado && configuracaoGeral.EnviarNotificacaoParaClientes)
                Domain.Pessoas.HorarioService.ProgramarNotificacoesDeCliente(clienteEstabelecimento);

            if (Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.EstabelecimentoPossuiChaveParaIntegracaoComOutrosSistemas(clienteEstabelecimento.Estabelecimento))
                EnviarNotificacaoDeEventoParaIntegracaoComOutrosSistemas(clienteEstabelecimento, ehCadastroNovo);
        }

        private void EnviarNotificacaoDeEventoParaIntegracaoComOutrosSistemas(ClienteEstabelecimento clienteEstabelecimento, bool ehCadastroNovo)
        {
            TipoDeEventoEnum tipoDeEventoEnum = ehCadastroNovo ? TipoDeEventoEnum.InclusaoDeCliente : TipoDeEventoEnum.AlteracaoDeCliente;

            List<IntegracaoComOutrosSistemas.Telefone> telefonesCliente = ObterTelefonesClienteIntegracaoComOutrosSistemas(clienteEstabelecimento);

            var eventoDeCliente = new EventoDeManterCliente()
            {
                Dados = new DadosEventoDeManterCliente()
                {
                    TipoDeEvento = tipoDeEventoEnum,
                    Action = ehCadastroNovo ? IntegracaoComOutrosSistemas.Enums.TipoDeAcaoEnum.Inclusao : IntegracaoComOutrosSistemas.Enums.TipoDeAcaoEnum.Alteracao,
                    Nome = clienteEstabelecimento.Cliente.PessoaFisica.NomeCompleto,
                    CPF = clienteEstabelecimento.Cliente.PessoaFisica.Cpf,
                    Email = clienteEstabelecimento.Cliente.PessoaFisica.Email,
                    Telefone = telefonesCliente,
                    DataDeNascimento = clienteEstabelecimento.Cliente.PessoaFisica.DataNascimento.HasValue ? clienteEstabelecimento.Cliente.PessoaFisica.DataNascimento.Value.ToBrazilianShortDateString() : "",
                    Sexo = clienteEstabelecimento.Cliente.PessoaFisica.Genero,
                    Endereco = clienteEstabelecimento.Endereco == null ? new IntegracaoComOutrosSistemas.Endereco() : new IntegracaoComOutrosSistemas.Endereco() { Logradouro = clienteEstabelecimento.Endereco.Logradouro, Bairro = clienteEstabelecimento.Endereco.Bairro, Cidade = clienteEstabelecimento.Endereco.Cidade, Numero = clienteEstabelecimento.Endereco.Numero, UF = clienteEstabelecimento.Endereco.UF == null ? "" : clienteEstabelecimento.Endereco.UF.Sigla.ToString(), EnderecoCompleto = clienteEstabelecimento.Endereco.ObterEnderecoCompleto() },
                    Etiqueta = Domain.Marcadores.ObjetoEtiquetadoRepository.ListarAtivosDoObjetoEtiquetado(clienteEstabelecimento.Estabelecimento.IdEstabelecimento, TipoDeEtiquetaEnum.ClienteEstabelecimento, clienteEstabelecimento.Codigo).Select(e => e.Etiqueta.Conteudo).ToList(),
                    DataDeInclusao = ehCadastroNovo ? Calendario.Agora().ToIntegracaoLongDateTimeString() : clienteEstabelecimento.DataCadastro.Value.ToIntegracaoLongDateTimeString(),
                    IdDoCliente = clienteEstabelecimento.Cliente.IdCliente,
                    IdDoClienteNoEstabelecimento = clienteEstabelecimento.Codigo,
                    IdDoEstabelecimento = clienteEstabelecimento.Estabelecimento.IdEstabelecimento,
                    ComoNosConheceu = clienteEstabelecimento.ComoNosConheceu != null ? clienteEstabelecimento.ComoNosConheceu.Descricao : "",
                    EnviarNotificacaoParaCliente = clienteEstabelecimento.RecebeNotificacao.HasValue && clienteEstabelecimento.RecebeNotificacao.Value,
                    EnviarEmailsAgendamentosParaCliente = clienteEstabelecimento.EnviarEmailAgendamentoCliente,
                    RecebeEmailMarketing = clienteEstabelecimento.RecebeEmailMarketing,
                    RecebeSmsMarketing = clienteEstabelecimento.RecebeSMSMarketing,
                    RecebeEmailProgramaFidelidade = clienteEstabelecimento.RecebeEmailProgramaFidelidade,
                    RedeSocial = Domain.Pessoas.MidiaSocialRepository.ObterNomeMidiaSocialPorPessoa(clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa, TipoMidiaSocialEnum.Instagram),
                    OrigemDoCliente = clienteEstabelecimento.Cliente.PessoaFisica.IdGoogleReserveUser != null ? "Agendamento pelo Google" : clienteEstabelecimento.Cliente.TipoCliente.GetEnumText()
                }
            };

            eventoDeCliente.Tipo = tipoDeEventoEnum;
            Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.NotificarEventoParaIntegracaoComOutrosSistemas(clienteEstabelecimento.Estabelecimento, eventoDeCliente);
        }

        public List<IntegracaoComOutrosSistemas.Telefone> ObterTelefonesClienteIntegracaoComOutrosSistemas(ClienteEstabelecimento clienteEstabelecimento)
        {
            var telefones = Domain.Pessoas.TelefoneRepository.ObterTelefonesVisiveisParaAPessoaJuridica(clienteEstabelecimento);
            var telefonesCliente = ObterListaTelefonesCompletoParaIntegracaoComOutrosSistemas(telefones);
            return telefonesCliente;
        }

        private List<IntegracaoComOutrosSistemas.Telefone> ObterListaTelefonesCompletoParaIntegracaoComOutrosSistemas(IQueryable<Telefone> telefones)
        {
            return telefones.Select(p => new IntegracaoComOutrosSistemas.Telefone() { DDD = p.DDD, Numero = p.Numero, Operadora = p.Operadora != null ? p.Operadora.Nome : "", TelefoneCompleto = IntegracaoComOutrosSistemas.Telefone.TelefoneCompletoFormadado(p.DDD, p.Numero) }).ToList();
        }

        private void ManterClienteWeb(ClienteEstabelecimento entity, bool enviaEmail, bool unificar = true, bool forcarEnvioDeEmailDeConfirmacaoDeConta = false, bool origemAgendamentoHotiste = false)
        {
            ValidarClienteWeb(entity);

            if (ValidationHelper.Instance.IsValid)
            {
                var pessoaFisica = entity.Cliente.PessoaFisica;
                var primeiraConta = pessoaFisica.PrimeiraConta;
                var clientesExistentesComEmail = primeiraConta != null
                    ? ListarClienteEstabelecimentoPorEmail(primeiraConta.Email)
                    : new List<ClienteEstabelecimento>();

                var contaAtual = Domain.Pessoas.ContaService.ObterContaAutenticada() ?? primeiraConta;

                //if (forcarEnvioDeEmailDeConfirmacaoDeConta && !primeiraConta.Confirmada) {
                //    EnvioEmailService.EnviarEmailAtivacaoCadastroCliente(primeiraConta, origemAgendamentoHotiste, codigoEstabelecimento: entity.Estabelecimento.IdEstabelecimento);
                //}
                //else
                if (entity.Cliente.IdCliente == 0 && enviaEmail)
                    Domain.Pessoas.EnvioEmailService.EnviarEmailBoasVindasClienteWeb(entity.Cliente, origemAgendamentoHotsite: origemAgendamentoHotiste, codigoEstabelecimento: entity.Estabelecimento.IdEstabelecimento);

                if (entity.Codigo > 0)
                {
                    entity.Ativo = true;
                    var idPessoaQueAlterou = contaAtual?.Pessoa?.IdPessoa;
                    ClienteRepository.SaveOrUpdate(entity.Cliente, idPessoaQueAlterou);
                }
                else
                {
                    if (clientesExistentesComEmail?.Any() != true || (entity.Codigo == 0 && entity.Cliente.TipoCliente == TipoClienteEnum.Web))
                    {
                        var idPessoaQueAlterou = contaAtual?.Pessoa?.IdPessoa;
                        ClienteEstabelecimentoRepository.SaveOrUpdate(entity, idPessoaQueAlterou);
                    }
                }

                if (unificar && clientesExistentesComEmail != null && clientesExistentesComEmail.Any())
                {
                    Domain.Pessoas.UnificacaoService.Unificar(pessoaFisica);
                }

                if (primeiraConta != null)
                    pessoaFisica.Email = primeiraConta.Email;

                if (VerificaSePodeAlterarDataNascimentoDaPessoaFisica(entity) && entity.DataNascimentoCliente != null)
                {
                    entity.Cliente.PessoaFisica.DataNascimento = entity.DataNascimentoCliente;
                    Domain.Pessoas.PessoaFisicaRepository.Update(entity.Cliente.PessoaFisica);
                }
            }
            else
                ClienteEstabelecimentoRepository.Clear();
        }

        private ClienteEstabelecimento ApagarEmailAlternativoDeClienteEstabelecimento(ClienteEstabelecimento ce)
        {
            bool clienteComEmailAlternativo = ce.Cliente.ClientesEstabelecimento.Where(c => c.Cliente.IdCliente == ce.Cliente.IdCliente && c.EmailAlternativo != null).Any();
            if (ce.Cliente.PessoaFisica.Email != null && ((ce.EmailAlternativo != null) || clienteComEmailAlternativo))
            {
                ce.EmailAlternativo = String.Empty;

                if (clienteComEmailAlternativo)
                {
                    var cliente = ce.Cliente.ClientesEstabelecimento.Where(c => c.Cliente.IdCliente == ce.Cliente.IdCliente).FirstOrDefault();
                    cliente.EmailAlternativo = String.Empty;
                    ce.Cliente.ClientesEstabelecimento.Remove(cliente);
                    ce.Cliente.ClientesEstabelecimento.Add(cliente);
                }
            }

            return ce;
        }

        #region Validacao

        private static void ValidarCliente(Cliente cliente)
        {
            ValidarPessoaFisica(cliente.PessoaFisica);
        }

        private static void ValidarClienteWeb(ClienteEstabelecimento entity)
        {
            ValidarCliente(entity.Cliente);
        }

        private static void ValidarCPF(String cpf)
        {
            if (!cpf.ValidarCPF())
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemCpfInvalido);
        }

        private static void ValidarCPFJaCadastrado(String cpf, Int32 codigoEstabelecimento)
        {
            var clienteComCpf = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorCpf(cpf,
                codigoEstabelecimento);
            if (clienteComCpf != null && clienteComCpf.Cliente.PessoaFisica.Ativo)
            {
                if (clienteComCpf.Ativo)
                {
                    var mensagemFormatada = String.Format(RES.MensagemCpfJaCadastrado,
                        clienteComCpf.DadosDoCliente.NomeCompleto);
                    ValidationHelper.Instance.AdicionarItemValidacao(mensagemFormatada);
                }
                else
                {
                    var mensagemFormatada = String.Format(RES.MensagemCpfJaCadastradoClienteInativo,
                        clienteComCpf.DadosDoCliente.NomeCompleto);
                    ValidationHelper.Instance.AdicionarItemValidacao(mensagemFormatada);
                }
            }
        }

        private static void ValidarCPFJaCadastrado(String cpf,
            Int32 codigoEstabelecimento,
            Int32 codigoClienteEstabelecimento)
        {
            var clienteEstabelecimentoBusca =
                Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorCpf(cpf, codigoEstabelecimento);
            if (clienteEstabelecimentoBusca != null &&
                clienteEstabelecimentoBusca.Codigo != codigoClienteEstabelecimento && clienteEstabelecimentoBusca.Cliente.PessoaFisica.Ativo)
            {
                if (clienteEstabelecimentoBusca.Ativo)
                {
                    var mensagemFormatada = String.Format(RES.MensagemCpfJaCadastrado,
                        clienteEstabelecimentoBusca.DadosDoCliente.NomeCompleto);
                    ValidationHelper.Instance.AdicionarItemValidacao(mensagemFormatada);
                }
                else
                {
                    var mensagemFormatada = String.Format(RES.MensagemCpfJaCadastradoClienteInativo,
                        clienteEstabelecimentoBusca.DadosDoCliente.NomeCompleto);
                    ValidationHelper.Instance.AdicionarItemValidacao(mensagemFormatada);
                }
            }
        }

        private static void ValidarCPFPreenchido(String cpf)
        {
            if (string.IsNullOrEmpty(cpf))
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemCpfNaoPreenchido);
        }

        private static void ValidarPessoaFisica(PessoaFisica pessoaFisica)
        {
            if (string.IsNullOrEmpty(pessoaFisica.NomeCompleto))
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemNomeNaoPreenchido);
        }

        private void ValidarClienteBalcao(ClienteEstabelecimento entity)
        {
            if (string.IsNullOrEmpty(entity.Cliente.PessoaFisica.NomeCompleto))
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemNomeNaoPreenchido);

            if (!String.IsNullOrEmpty(entity.Cliente.PessoaFisica.Cpf))
            {
                ValidarCPF(entity.Cliente.PessoaFisica.Cpf);
                ValidarCPFJaCadastrado(entity.Cliente.PessoaFisica.Cpf, entity.Estabelecimento.IdEstabelecimento,
                    entity.Codigo);
            }

            ValidarCliente(entity.Cliente);
        }

        #endregion Validacao

        [TransactionInitRequired]
        public ClienteEstabelecimento TransformarPessoaEmCliente(PessoaFisica pessoaFisica, int idEstabelecimento, bool enviaEmail = true)
        {
            var cliente = ClienteRepository.ObterPorPessoaFisica(pessoaFisica.IdPessoaFisica) ?? new Cliente();

            var clienteEstabelecimento = cliente != null
                ? Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(cliente.IdCliente, idEstabelecimento)
                : Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorCpf(pessoaFisica.Cpf, idEstabelecimento);
            if (clienteEstabelecimento != null)
                cliente = clienteEstabelecimento.Cliente;
            else
            // Verifica se já não tem cliente associado, mesmo sem associação com estabelecimento.
            if (cliente == null)
                cliente = ClienteService.Obter(pessoaFisica.Cpf) ?? new Cliente { PessoaFisica = pessoaFisica };

            if (pessoaFisica.Contas.Any(f => f.Ativo))
            {
                cliente.PessoaFisica = pessoaFisica;
                cliente.TipoCliente = TipoClienteEnum.Web;
            }

            if (clienteEstabelecimento == null)
                clienteEstabelecimento = new ClienteEstabelecimento { Cliente = cliente, Estabelecimento = null };

            ManterCliente(clienteEstabelecimento, enviaEmail);

            return clienteEstabelecimento;
        }

        public bool PossuiCadastradoAlgumDosTelefonesInformados(FiltroValidacaoDeTelefone filtro)
        {
            var query = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();

            PessoaFisica pessoaFisica = query
                .Where(c => c.Codigo == filtro.IdClienteEstabelecimento)
                .Select(c => c.Cliente.PessoaFisica)
                .FirstOrDefault();

            if (pessoaFisica == null)
                return false;

            return pessoaFisica.Telefones.Any(t => filtro.Telefones.Any(te => t.DDD == te.DDD && t.Numero == te.Numero));
        }

        public ClienteEstabelecimento ObterOuTransformarEmClienteEstabelecimento(Cliente cliente, Estabelecimento estabelecimento, bool enviarEmail = true)
        {
            ClienteEstabelecimento clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(cliente.IdCliente, estabelecimento.IdEstabelecimento);

            if (clienteEstabelecimento == null)
            {
                clienteEstabelecimento = new ClienteEstabelecimento
                {
                    Cliente = cliente,
                    Estabelecimento = estabelecimento
                };

                ManterCliente(clienteEstabelecimento, enviarEmail);
            }

            return clienteEstabelecimento;
        }

        public bool VerificaSePodeAlterarDataNascimentoDaPessoaFisica(ClienteEstabelecimento ce)
        {
            if (ce.Cliente.PessoaFisica.DataNascimento.Equals(null) || !ce.Cliente.EhClienteWeb())
                return true;
            else
                return false;
        }

        public DadosClienteDTO ObterDadosDoClienteParaGoogleReserve(string userId)
        {
            var query = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable().Where(f => f.Cliente.PessoaFisica.IdGoogleReserveUser == userId);
            var dados = query.Select(f => new
            {
                f.Cliente.PessoaFisica.Email,
                f.Estabelecimento.IdEstabelecimento,
                f.Cliente.PessoaFisica.IdGoogleReserveUser,
                Nome = f.Cliente.PessoaFisica.NomeCompleto,
                IdPessoaJuridica = f.Estabelecimento.PessoaJuridica.IdPessoa,
                IdPessoaFisica = f.Cliente.PessoaFisica.IdPessoa
            }).FirstOrDefault();

            if (dados == null)
                return null;

            var telefone = Domain.Pessoas.TelefoneRepository
                .ObterTelefonesVisiveisParaAPessoaJuridica(dados.IdPessoaFisica, dados.IdPessoaJuridica)
                .FirstOrDefault() ?? new Telefone();

            return new DadosClienteDTO
            {
                DDD = telefone.DDD,
                Telefone = telefone.Numero,
                Email = dados.Email,
                IdEstabelecimento = dados.IdEstabelecimento,
                IdGoogleReserveUser = dados.IdGoogleReserveUser,
                Nome = dados.Nome
            };
        }

        public SaldosDePontosDeFidelidadeDoClienteDTO ObterPontosDeFidelidadePorIdPessoa(int idPessoaDoCliente, Estabelecimento estabelecimento)
        {
            var pontos = new SaldosDePontosDeFidelidadeDoClienteDTO();

            var dadosCliente = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable()
                    .Where(ce => ce.Cliente.PessoaFisica.IdPessoa == idPessoaDoCliente &&
                                 ce.Estabelecimento == estabelecimento)
                    .Select(ce => new
                    {
                        SaldoDePontosDeFidelidade = ce.SaldoDePontosDeFidelidade,
                        Cpf = ce.Cliente.PessoaFisica.Cpf,
                        Email = ce.Cliente.PessoaFisica.Email
                    })
                    .FirstOrDefault();

            if (dadosCliente == null)
                return new SaldosDePontosDeFidelidadeDoClienteDTO();//tratamento para o bug ocorrendo em produção que não conseguimos simular, com isso, o saldo virá zerado e encontraremos um caso de erro.

            return ObterPontosDeFidelidade(
                                estabelecimento: estabelecimento,
                                pontosNesteEstabelecimento: dadosCliente.SaldoDePontosDeFidelidade,
                                cpf: dadosCliente.Cpf,
                                email: dadosCliente.Email);
        }

        public SaldosDePontosDeFidelidadeDoClienteDTO ObterPontosDeFidelidadePorClienteEstabelecimento(ClienteEstabelecimento clienteEstabelecimento)
        {
            if (clienteEstabelecimento == null)
                return new SaldosDePontosDeFidelidadeDoClienteDTO();//tratamento para o bug ocorrendo em produção que não conseguimos simular, com isso, o saldo virá zerado e encontraremos um caso de erro.

            return ObterPontosDeFidelidade(
                                estabelecimento: clienteEstabelecimento.Estabelecimento,
                                pontosNesteEstabelecimento: clienteEstabelecimento.SaldoDePontosDeFidelidade,
                                cpf: clienteEstabelecimento.Cliente.PessoaFisica.Cpf,
                                email: clienteEstabelecimento.Cliente.PessoaFisica.Email);
        }

        private SaldosDePontosDeFidelidadeDoClienteDTO ObterPontosDeFidelidade(Estabelecimento estabelecimento, int pontosNesteEstabelecimento, string cpf, string email)
        {
            int pontosAcumuladosNaFranquia = 0;

            bool permiteTransferencias = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();
            bool clientePossuiCpfOuEmail = !string.IsNullOrWhiteSpace(cpf) || !string.IsNullOrWhiteSpace(email);

            if (permiteTransferencias && clientePossuiCpfOuEmail)
            {
                pontosAcumuladosNaFranquia = Domain.Pessoas.ClienteEstabelecimentoRepository
                        .ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(estabelecimento.FranquiaEstabelecimento.Franquia.Id, cpf, email);
            }

            var pontosDTO = new SaldosDePontosDeFidelidadeDoClienteDTO
            {
                RedePermiteTransferencias = permiteTransferencias,
                TotalDePontosNaRede = pontosAcumuladosNaFranquia == 0 ? pontosNesteEstabelecimento : pontosAcumuladosNaFranquia,
                PontosNesteEstabelecimento = pontosNesteEstabelecimento,
                PontosEmOutrosEstabelecimentos = pontosAcumuladosNaFranquia == 0 ? pontosAcumuladosNaFranquia : pontosAcumuladosNaFranquia - pontosNesteEstabelecimento,
                PermiteAdicionarPontosAvulsos = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.ProgramaFidelidade_PontosAvulsos)
            };

            return pontosDTO;
        }

        public void RecalcularSaldosConsiderandoPontosResgatadosNoFechamentoDeContas(SaldosDePontosDeFidelidadeDoClienteDTO pontosDTO, int pontosSendoResgatados)
        {
            if (pontosSendoResgatados <= 0)
                return;

            int pontosRestantesParaResgatar = pontosSendoResgatados;

            if (pontosDTO.PontosNesteEstabelecimento >= pontosRestantesParaResgatar)
            {
                pontosDTO.PontosNesteEstabelecimento -= pontosRestantesParaResgatar;
            }
            else
            {
                pontosRestantesParaResgatar -= pontosDTO.PontosNesteEstabelecimento;
                pontosDTO.PontosNesteEstabelecimento = 0;

                if (pontosDTO.RedePermiteTransferencias)
                {
                    pontosDTO.PontosEmOutrosEstabelecimentos -= pontosRestantesParaResgatar;
                }
            }

            pontosDTO.SomarPontosDisponiveis();
        }

        public bool EhClienteNovo(int idClienteEstabelecimento, int? idHorario, DateTime dataAgendamentoInicio)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            return VerificaSeEhClienteNovo(clienteEstabelecimento, idHorario, dataAgendamentoInicio);
        }

        public bool EhClienteNovo(Horario horario)
        {
            return VerificaSeEhClienteNovo(horario.ClienteEstabelecimento, horario.Codigo, horario.DataInicio);
        }

        private bool VerificaSeEhClienteNovo(ClienteEstabelecimento ce, int? idHorario, DateTime dataAgendamentoInicio)
        {
            if (ce.PrimeiroAgendamento == null)
                return true;

            if (idHorario == null)
                return false;

            if (idHorario == ce.PrimeiroAgendamento.Codigo)
                return true;

            var dataPrimeiroAgendamento = ce.PrimeiroAgendamento.DataInicio;
            var agendamentoMesmoDia = dataAgendamentoInicio.Day == dataPrimeiroAgendamento.Day && dataAgendamentoInicio.Month == dataPrimeiroAgendamento.Month && dataAgendamentoInicio.Year == dataPrimeiroAgendamento.Year;
            if (agendamentoMesmoDia)
                return true;

            return false;
        }

        public decimal ObterSaldoTotalDeDebitoAnterioresAData(int idCliente, int idPessoaCliente, int idEstabelecimento, int idPessoaEstabelecimento, DateTime dataLimite)
        {
            var debitoHorario = Domain.Pessoas.HorarioRepository.ObterTotalDebitosDeClienteAnterioresAData(idCliente, idEstabelecimento, dataLimite);
            //var debitoPreVenda = Domain.Vendas.PreVendaProdutoRepository.ObterTotalDeDebitosEmPreVendaDoClienteAnterioresAData(idPessoaCliente, idEstabelecimento, dataLimite);
            var divida = Domain.DebitoParcial.DividaDeixadaNoEstabelecimentoRepository.ObterValorTotalFaltantePorClienteAnterioresAData(idPessoaEstabelecimento, idPessoaCliente, dataLimite);

            return debitoHorario + divida;
        }

        public bool ClientePossuiPermissaoParaAgendarViaAPI(int idCliente, int idEstabelecimento)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(idCliente, idEstabelecimento);
            return clienteEstabelecimento == null ? true : clienteEstabelecimento.PodeAgendarOnlineNoEstabelecimento;
        }

        public void InativarTodosClientesEsbelecimento(int idEstabelecimento)
        {
            var clientes = Domain.Pessoas.ClienteEstabelecimentoRepository.ListarPorEstabelecimento(idEstabelecimento);
            var qtdMaxParaInativar = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_para_inativacao_via_gerenciamento).ObterValor();

            if (clientes.Count() <= qtdMaxParaInativar)
            {
                foreach (ClienteEstabelecimento cliente in clientes)
                {
                    cliente.Ativo = false;
                    cliente.Observacoes = string.IsNullOrEmpty(cliente.Observacoes) ? "Inativação Trinks" : cliente.Observacoes + "\n Inativação Trinks";

                    Domain.Pessoas.ClienteEstabelecimentoRepository.UpdateNoFlush(cliente);
                }
                Domain.Pessoas.ClienteEstabelecimentoRepository.Flush();
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A quantidade de clientes está acima do limite para realizar a inativação. Por favor, envie uma solicitação de inativação para o time de Fábrica.");
            }

        }
    }
}