﻿using Elmah;
using Newtonsoft.Json;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.Disponibilidade.ExtensionMethods;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.IntegracaoComOutrosSistemas;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.Marcadores.DTO;
using Perlink.Trinks.Marcadores.Enums;
using Perlink.Trinks.Onboardings.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Web;
using DL = Perlink.Trinks.Domain.Pessoas;

namespace Perlink.Trinks.Pessoas.Services
{

    public class HorarioService : BaseService, IHorarioService
    {

        public void AlterarPrecoAgendamentosComPromocaoParaPrecoPadrao(DateTime aPartirDe, int idEstabelecimento, Int32 idServicoEstabelecimento)
        {
            var horarios = Domain.Pessoas.HorarioRepository.ListarAgendamentosFuturosNaoPagosAPartirDe(aPartirDe, idEstabelecimento, idServicoEstabelecimento);

            foreach (Horario horario in horarios)
            {
                var promocao = Domain.Promocoes.PromocaoRepository.ObterPromocaoPorServicoEstabelecimento(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);
                var valorFinal = Domain.Pessoas.ServicoEstabelecimentoService.ObterValorReal(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);

                horario.Valor = valorFinal;

                Domain.Pessoas.HorarioService.ManterHorario(horario, observacaoHistorico: "Preço alterado de preço promocional para preço padrão");
            }
        }

        public void AlterarPrecoAgendamentosMarcadosAPartirDe(DateTime aPartirDe, Int32 idEstabelecimento, List<KeyValuePair<Int32, Decimal>> parCodigoPrecoServicoEstabelecimento)
        {
            foreach (KeyValuePair<Int32, Decimal> servicoEstabelecimento in parCodigoPrecoServicoEstabelecimento)
                AlterarPrecoAgendamentosMarcadosAPartirDe(aPartirDe, idEstabelecimento, servicoEstabelecimento.Key);
        }

        public void AlterarPrecoAgendamentosMarcadosAPartirDe(DateTime aPartirDe, int idEstabelecimento, Int32 idServicoEstabelecimento)
        {
            var horarios = Domain.Pessoas.HorarioRepository.ListarAgendamentosFuturosNaoPagosAPartirDe(aPartirDe, idEstabelecimento, idServicoEstabelecimento);

            foreach (Horario horario in horarios)
            {
                var valorFinal = Domain.Pessoas.ServicoEstabelecimentoService.ObterValorReal(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);
                horario.Valor = valorFinal;
                Domain.Pessoas.HorarioService.ManterHorario(horario);
            }
        }

        public void AlterarPrecoAgendamentosMarcadosAPartirDeParaPromocoesEPrecoPadrao(DateTime aPartirDe, int idEstabelecimento, Int32 idServicoEstabelecimento)
        {
            var horarios = Domain.Pessoas.HorarioRepository.ListarAgendamentosFuturosNaoPagosAPartirDe(aPartirDe, idEstabelecimento, idServicoEstabelecimento);

            var promocoes = Domain.Promocoes.PromocaoRepository.Queryable().Where(f => f.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento).ToList();

            var pessoaQueAlterou = ContextHelper.Instance.IdConta.HasValue ? Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value).Pessoa.PessoaFisica : null;

            foreach (Horario horario in horarios)
            {
                var data0h = horario.DataInicio.Date;
                var promocao = promocoes.FirstOrDefault(f =>
                    (!f.DataInicio.HasValue || f.DataInicio <= data0h) &&
                    (!f.DataFim.HasValue || f.DataFim >= data0h));

                //var promocao = Domain.Promocoes.PromocaoRepository.ObterPromocaoPorServicoEstabelecimento(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);
                var valorFinal = Domain.Pessoas.ServicoEstabelecimentoService.ObterValorReal(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio, promocao);

                if (horario.Valor != valorFinal)
                {
                    horario.Valor = valorFinal;

                    if (promocao != null)
                    {
                        var horarioHistorico = new HorarioHistorico(horario, "Preço alterado devido a promoção", false, pessoaQueRealizou: pessoaQueAlterou);
                        horario.Historicos.Add(horarioHistorico);
                        Domain.Pessoas.HorarioService.ManterHorario(horario, noFlush: true);
                    }
                    else
                    {
                        var horarioHistorico = new HorarioHistorico(horario, dataFoiReagendada: false, pessoaQueRealizou: pessoaQueAlterou);
                        horario.Historicos.Add(horarioHistorico);
                        Domain.Pessoas.HorarioService.ManterHorario(horario, noFlush: true);
                    }
                }
            }
        }

        public void CancelarCompromissoMobile(int id, string motivoCancelamento)
        {
            var entity = Domain.Pessoas.HorarioRepository.Load(id);
            CancelarCompromissoMobile(entity, motivoCancelamento);
        }

        public StatusHorario CancelarCompromissoMobileERetornarStatus(Horario horario, string motivoCancelamento)
        {
            CancelarCompromissoMobile(horario, motivoCancelamento);
            return horario.Status;
        }

        public void CancelarCompromissoMobile(Horario horario, string motivoCancelamento)
        {
            if (horario.Status.Codigo != (int)StatusHorarioEnum.Confirmado &&
                horario.Status.Codigo != (int)StatusHorarioEnum.Aguardando_Confirmacao)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Apenas agendamentos confirmados ou aguardando confirmação podem ser cancelados.");
            }

            if (horario.FoiPagoAntecipadamente)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível cancelar agendamentos pagos antecipadamente. O cancelamento deve ser realizado pelo estabelecimento.");
            }

            if (ValidationHelper.Instance.IsValid)
            {
                horario.Status = new StatusHorario(9);
                horario.Historicos.Add(HorarioHistoricoDeCancelamentoFeitoPeloCliente(horario, motivoCancelamento));
                DL.AgendaService.ManterAgendamento(horario);
            }
        }

        private HorarioHistorico HorarioHistoricoDeCancelamentoFeitoPeloCliente(Horario horario, string motivoCancelamento)
        {
            var entity = new HorarioHistorico(horario);
            entity.MotivoCancelamento = motivoCancelamento;
            entity.HorarioQuemCancelou = new HorarioQuemCancelou(HorarioQuemCancelouEnum.Cliente);

            return entity;
        }

        public decimal DefinirComissaoDoProfissional(EstabelecimentoProfissionalServicoComissao comissao, Horario horario)
        {
            if (comissao == null)
                return 0;
            return horario == null || horario.EstabelecimentoProfissionalAssistente == null ? comissao.ValorComissao : (comissao.ValorComissaoComUmAssistente ?? 0);
        }

        public void DesprogramarProximasNotificacoesDeCliente(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarNotificacaoParaClientes)
                throw new Exception("Só é possível desprogramar notificações em estabelecimentos com configuração de não enviar notificações.");

            var notificacoesProgramadas = Domain.Pessoas.HorarioRepository.ObterComNotificacoesProgramadas(estabelecimento);

            foreach (var horario in notificacoesProgramadas)
            {
                LimpaDataDeNotificacaoProgramada(horario);
                Domain.Pessoas.HorarioRepository.UpdateNoFlush(horario);
            }
        }

        public Boolean ExisteAgendamentoFuturoAPartirDe(DateTime aPartirDe, Int32 idEstabelecimento, Int32 idServicoEstabelecimento)
        {
            return DL.HorarioRepository.ExisteAgendamentoFuturoNaoPagosAPartirDe(aPartirDe, idEstabelecimento, idServicoEstabelecimento);
        }

        public Boolean ExisteTransacoesParaOHorario(Int32 codigoHorario)
        {
            return DL.HorarioTransacaoRepository.ExisteTransacoesParaOHorario(codigoHorario);
        }

        public bool HorarioContidoPeriodoAusencia(Horario horario)
        {
            if (horario == null || horario.Profissional == null)
                return false;

            if (horario.Codigo.HasValue && horario.Codigo.Value > 0)
                horario = Domain.Pessoas.HorarioRepository.Load(horario.Codigo.Value);

            if (horario.Profissional == null)
                return false;

            var ep = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);
            return HorarioContidoPeriodoAusencia(ep, horario);
        }

        public bool HorarioContidoPeriodoAusencia(int idProfissional, int idEstabelecimento, int idHorario, DateTime dataHoraInicio, DateTime dataHoraFim)
        {
            var horario = idHorario > 0 ? Domain.Pessoas.HorarioRepository.Load(idHorario) : new Horario() { DataInicio = dataHoraInicio, DataFim = dataHoraFim };

            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(idProfissional, idEstabelecimento);
            return HorarioContidoPeriodoAusencia(estabelecimentoProfissional, horario);
        }

        public bool HorarioContidoPeriodoAusencia(EstabelecimentoProfissional estabelecimentoProfissional, Horario horario)
        {
            if (estabelecimentoProfissional == null)
                return false;

            var query = Domain.Pessoas.PeriodoAusenciaRepository.Queryable().Where(f => f.EstabelecimentoProfissional.Codigo == estabelecimentoProfissional.Codigo);

            return query.FiltrarContidasNoHorario(horario).Count() > 0;
        }

        public bool HorarioDentroPeriodoAlmocoProfissional(Horario horario)
        {
            if (horario.Profissional == null)
                return false;
            return HorarioDentroPeriodoAlmocoProfissional(horario.DataInicio, horario.DataFim, horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);
        }

        public bool HorarioDentroPeriodoAlmocoProfissional(DateTime dataInicio, DateTime dataFim, int idProfissional, int idEstabelecimento)
        {
            var intervalo = new IntervaloData(dataInicio, dataFim);
            var profissional = idProfissional > 0 ? Domain.Pessoas.ProfissionalRepository.Load(idProfissional) : new Profissional();
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var horariosTrabalho = profissional.EstabelecimentoProfissionalLista.ToEstabelecimentoProfissional(estabelecimento, profissional).HorariosTrabalhoProfissionalAtivos;
            var horarioTrabalhoHorario = horariosTrabalho.FirstOrDefault(p => p.DiaSemana.IdDiaSemana == dataInicio.DayOfWeek.ToDiaSemana().GetHashCode());

            if (horarioTrabalhoHorario != null && (horarioTrabalhoHorario.InicioAlmoco.HasValue && horarioTrabalhoHorario.FimAlmoco.HasValue))
            {
                //TRINKS-10552 - Problemas de conflito no horário de almoço
                var almocoInicio = new DateTime(dataInicio.Year, dataInicio.Month, dataInicio.Day, horarioTrabalhoHorario.InicioAlmoco.Value.Hours, horarioTrabalhoHorario.InicioAlmoco.Value.Minutes, 0).AddSeconds(1);
                var almocoFim = new DateTime(dataInicio.Year, dataInicio.Month, dataInicio.Day, horarioTrabalhoHorario.FimAlmoco.Value.Hours, horarioTrabalhoHorario.FimAlmoco.Value.Minutes, 0).AddSeconds(-1);
                var intervalosHorarioTrabalho = new IntervaloData(almocoInicio, almocoFim);

                return intervalosHorarioTrabalho.ContidoEm(intervalo) || intervalo.ContidoEm(intervalosHorarioTrabalho) || intervalo.InicioContidoEm(intervalosHorarioTrabalho) || intervalo.PossuiIntersecao(intervalosHorarioTrabalho);
            }

            return false;
        }

        public bool HorarioDentroPeriodoTrabalhoEstabelecimento(Horario horario)
        {
            return HorarioDentroPeriodoTrabalhoEstabelecimento(horario.DataInicio, horario.DataFim, horario.Estabelecimento.IdEstabelecimento);
        }

        public bool HorarioDentroPeriodoTrabalhoEstabelecimento(DateTime dataInicio, DateTime dataFim, int idEstabelecimento)
        {
            var intervalo = new IntervaloData(dataInicio, dataFim);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var horariosFuncionamento = estabelecimento.HorariosDeFuncionamento.Ativos();
            var intervalosHorarioFuncionamento = horariosFuncionamento.ToIntervaloDataList(intervalo);

            if (!intervalosHorarioFuncionamento.Contem(intervalo))
                return false;

            return true;
        }

        public bool HorarioDentroPeriodoTrabalhoProfissional(Horario horario)
        {
            if (horario.Profissional == null)
                return true;
            return HorarioDentroPeriodoTrabalhoProfissional(horario.DataInicio, horario.DataFim, horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);
        }

        public bool HorarioDentroPeriodoTrabalhoProfissional(DateTime dataInicio, DateTime dataFim, int idProfissional, int idEstabelecimento)
        {
            var dentroHorarioTrabalho = true;
            var dentroPeriodoDeAgendaAberta = false;

            var intervalo = new IntervaloData(dataInicio, dataFim);
            var estabelecimentoProf = DL.EstabelecimentoProfissionalRepository.Obter(idProfissional, idEstabelecimento);
            var horariosTrabalho = estabelecimentoProf.HorariosTrabalhoProfissionalAtivos;
            var intervalosHorarioTrabalho = horariosTrabalho.ToIntervaloDataList(intervalo);

            if (intervalosHorarioTrabalho == null || !intervalosHorarioTrabalho.Contem(intervalo))
            {
                dentroHorarioTrabalho = false;

                dentroPeriodoDeAgendaAberta = Domain.ProfissionalAgenda.LiberacaoDeHorarioNaAgendaRepository
                    .ProfissionalLiberouAgendaDurantePeriodo(idEstabelecimento, estabelecimentoProf.Codigo, dataInicio, dataFim);
            }

            return dentroHorarioTrabalho || dentroPeriodoDeAgendaAberta;
        }

        public List<EstabelecimentoHorarioEspecialFuncionamento> HorarioForaDoHorarioEspecialDeFuncionamento(Horario horario)
        {
            return HorarioForaDoHorarioEspecialDeFuncionamento(horario.DataInicio, horario.DataFim, horario.Estabelecimento.IdEstabelecimento);
        }

        public List<EstabelecimentoHorarioEspecialFuncionamento> HorarioForaDoHorarioEspecialDeFuncionamento(DateTime dataInicio, DateTime dataFim, int idEstabelecimento)
        {
            var intervalo = new IntervaloData(dataInicio, dataFim);

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var horariosEspeciais = estabelecimento.HorariosEspeciaisDeFuncionamento.FiltrarContidasNoDia(dataFim.Date);
            var lista = new List<EstabelecimentoHorarioEspecialFuncionamento>();

            foreach (EstabelecimentoHorarioEspecialFuncionamento horarioEspecial in horariosEspeciais)
            {
                if (horarioEspecial.EstabelecimentoHorarioEspecialFuncionamentoTipo.Codigo == 2)
                    lista.Add(horarioEspecial);
                else
                {
                    var intervalosHorarioEspecial = horariosEspeciais.ToIntervaloDataList();
                    if (!intervalosHorarioEspecial.Contem(intervalo))
                        lista.Add(horarioEspecial);
                }
            }

            return lista;
        }

        //[TransactionInitRequired]
        public void ManterHorario(Horario horario, Transacao transacao = null, bool persistirHistorico = true, string observacaoHistorico = null, bool modoPAT = false, bool forcarGeracaoHistorico = false, Comanda comanda = null, bool alteradoPorAgendaCliente = false, bool ehComandaRapida = false, bool noFlush = false, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false)
        {
            bool ehNovoAgendamento = (horario.Codigo == 0 || horario.Codigo == null);

            if (horario.Status == null)
                horario.Status = StatusHorarioEnum.Confirmado;

            if (persistirHistorico && !horario.Historicos.Any(f => f.Codigo == 0))
            {
                GerarHistorico(horario, transacao, forcarGeracaoHistorico, observacaoHistorico, comanda: comanda, alteradoPorAgendaCliente: alteradoPorAgendaCliente, ehComandaRapida: ehComandaRapida);
            }

            if (horario.PessoaQuemMarcou == null)
                horario.PessoaQuemMarcou = horario.Estabelecimento.PessoaJuridica.ResponsavelFinanceiro;

            var franquiaEstabelecimento = horario.Estabelecimento.FranquiaEstabelecimento;
            var podeAlterarValor = franquiaEstabelecimento == null ||
                !franquiaEstabelecimento.Ativo ||
                franquiaEstabelecimento.PermiteAlteracaoDeValores ||
                (ContextHelper.Instance.IdConta.HasValue && Domain.Pessoas.ContaFranquiaRepository.EhContaVinculadaAFranquia(ContextHelper.Instance.IdConta.Value, franquiaEstabelecimento.Franquia.Id));
            if (!podeAlterarValor && horario.Valor > 0)
            {
                var valorOriginal = horario.Id == 0 ? -1 : Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(f => f.Id == horario.Id)
                    .Select(f => f.Valor)
                    .FirstOrDefault();

                if (valorOriginal != horario.Valor && !horario.ServicoEstabelecimentoAlterado)
                {
                    bool temClube = false;

                    if (horario.HorariosTransacoes != null)
                    {
                        temClube = horario.HorariosTransacoes.Any(h => h.MotivoDesconto != null &&
                            h.MotivoDesconto.MotivoDeDescontoDoTrinks != null &&
                            h.MotivoDesconto.MotivoDeDescontoDoTrinks == MotivoDeDescontoDoTrinksEnum.ClubeDeAssinaturas);
                    }

                    var valorServicoReal = Domain.Pessoas.ServicoEstabelecimentoService
                        .ObterValorReal(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);

                    var valorDosProdutosUsadosNoHorarioParaCliente = horario.ObterCustoDosProdutosAssociadosAoHorarioParaCliente();
                    var valorServicoRealComUsoDeProdutosCliente = valorServicoReal + valorDosProdutosUsadosNoHorarioParaCliente;

                    if (horario.Valor != valorServicoRealComUsoDeProdutosCliente && !temClube)
                    {
                        throw new Exception(string.Format("O valor do horario {2} deveria ser {0:c} e foi passado {1:c}", valorServicoRealComUsoDeProdutosCliente, horario.Valor, horario.Id));
                    }
                }
            }

            Domain.Pessoas.HorarioService.ProgramarNotificacoesDeCliente(horario);

            var pessoaLogada = Domain.Pessoas.ContaService.ObterPessoaFisicaDaContaAutenticada();

            Domain.EstoqueComBaixaAutomatica.UsoDeProdutoNoHorarioService.ControlarUsoDeProdutosAoManterHorario(horario, pessoaLogada);

            var tipoDeEvento = ObterTipoDeEventoAgendamento(horario);

            if (!noFlush)
                Domain.Pessoas.HorarioRepository.SaveOrUpdate(horario);
            else if (noFlush && ehNovoAgendamento)
                Domain.Pessoas.HorarioRepository.SaveNewNoFlush(horario);

            if (Domain.WhatsApp.EstablishmentConfigurationService.CanCreateUpdateSchedulingComunication(horario.Estabelecimento.IdEstabelecimento, horario.Codigo))
                Domain.WhatsApp.HorarioComunicacaoService.CriarAtualizarHorarioComunicacao(horario);

            if (Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks
               .NotificacaoDeEventoParaIntegracaoApartirDoTrinksService
               .EstabelecimentoPossuiChaveEPodeEnviarParaIntegracaoComOutrosSistemas(horario.Estabelecimento))
                EnviarEventoDeManterAgendamentoParaIntegracao(horario, tipoDeEvento);

            if (horario.Estabelecimento.TrabalhaComRodizioDeProfissionais() && horario.Estabelecimento.RetornoDoProfissionalAFilaDoRodizioAoFinalizarAgendamento())
                Domain.RodizioDeProfissionais.AgendamentoComProfissionalDaVezStory.ValidarRemocaoEmAtendimentoDoProfissionalDaVez(horario, pessoaLogada);

            Domain.Onboardings.RastreioTarefaService.IncrementarRastreio(ETarefaOnboarding.PrimeiroAgendamento,
                horario.Estabelecimento.IdEstabelecimento);
        }

        private TipoDeEventoEnum ObterTipoDeEventoAgendamento(Horario horario)
        {
            TipoDeEventoEnum tipoDeEventoEnum;

            if (!horario.Codigo.HasValue || horario.Id == 0)
            {
                tipoDeEventoEnum = TipoDeEventoEnum.InclusaoDeAgendamento;
            }
            else if (horario.Status == StatusHorarioEnum.Cancelado)
            {
                tipoDeEventoEnum = TipoDeEventoEnum.ExclusaoDeAgendamento;
            }
            else
            {
                tipoDeEventoEnum = TipoDeEventoEnum.AlteracaoDeAgendamento;
            }

            return tipoDeEventoEnum;
        }

        private void EnviarEventoDeManterAgendamentoParaIntegracao(Horario horario, TipoDeEventoEnum tipoDeEventoEnum)
        {
            EstabelecimentoProfissional estabelecimentoProfissional = null;
            if (horario.Profissional != null && horario.Profissional.IdProfissional > 0)
                estabelecimentoProfissional = DL.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);
            var clienteEstabelecimento = horario.ClienteEstabelecimento;

            try
            {
                TipoDeAcaoEnum tipoAcao = Domain.IntegracaoComOutrosSistemas.NotificacaoDeEventoParaIntegracaoService.TipodeAcaoEnumRetorno(tipoDeEventoEnum);

                var pessoaFisicaCliente = clienteEstabelecimento.Cliente.PessoaFisica;
                var endereco = clienteEstabelecimento.Endereco;
                var telefonesCliente = DL.ClienteEstabelecimentoService.ObterTelefonesClienteIntegracaoComOutrosSistemas(clienteEstabelecimento);
                var valorHorario = horario.Valor.ToString("F", new CultureInfo("pt-BR"));
                var statusHorario = horario.Status;

                if (statusHorario != null && statusHorario.Codigo > 0 && string.IsNullOrWhiteSpace(statusHorario.Nome))
                {
                    statusHorario = DL.StatusHorarioRepository.Load(horario.Status.Codigo) ?? statusHorario;
                }

                var dados = new DadosEventoDeAgendamento()
                {
                    IdDoEstabelecimento = horario.Estabelecimento.IdEstabelecimento,
                    IdDoCliente = horario.Cliente.IdCliente,
                    IdDoClienteNoEstabelecimento = clienteEstabelecimento.Codigo,
                    IdDoProfissional = horario.Profissional?.IdProfissional ?? 0,
                    IdDoProfissionalNoEstabelecimento = estabelecimentoProfissional?.Codigo ?? 0,
                    IdDoServico = horario.ServicoEstabelecimento.Servico?.IdServico ?? null,
                    IdDoServicoNoEstabelecimento = horario.ServicoEstabelecimento.IdServicoEstabelecimento,
                    NomeDoServicoNoEstabelecimento = horario.ServicoEstabelecimento.Nome,
                    IdDoAgendamento = horario.Codigo.Value,
                    PrecoDoServicoNoAgendamento = valorHorario,
                    DuracaoDoAgendamento = horario.Duracao,
                    DataHoraInicioDoAgendamento = horario.DataInicio.ToIntegracaoLongDateTimeString(),
                    DataHoraFimDoAgendamento = horario.DataFim.ToIntegracaoLongDateTimeString(),
                    Observacao = horario.Observacao,
                    Status = statusHorario?.Nome,
                    Origem = horario.HorarioOrigem.Nome,
                    NomeDoCliente = pessoaFisicaCliente.NomeCompleto,
                    CpfDoCliente = pessoaFisicaCliente.Cpf,
                    EmailDoCliente = pessoaFisicaCliente.Email,
                    TelefoneDoCliente = telefonesCliente,
                    SexoDoCliente = pessoaFisicaCliente.Genero,
                    DataDeNascimentoDoCliente = pessoaFisicaCliente.DataNascimento?.ToString("yyyy-MM-dd"),
                    Endereco = new IntegracaoComOutrosSistemas.Endereco()
                    {
                        Logradouro = endereco?.Logradouro,
                        Numero = endereco?.Numero,
                        Bairro = endereco?.Bairro,
                        Cidade = endereco?.Cidade,
                        UF = endereco?.UF == null ? "" : clienteEstabelecimento.Endereco.UF.Sigla.ToString(),
                        EnderecoCompleto = endereco?.ObterEnderecoCompleto()
                    },
                    DataDeInclusaoDoClienteNoEstabelecimento = clienteEstabelecimento.DataCadastro.HasValue ? clienteEstabelecimento.DataCadastro.Value.ToIntegracaoLongDateTimeString() : null,
                    DataHoraEventoGerado = Calendario.Agora().ToIntegracaoLongDateTimeString(),
                    Action = tipoAcao,
                    TipoDeEvento = tipoDeEventoEnum
                };

                dados.Etiquetas = Domain.Marcadores.ObjetoEtiquetadoRepository.ListarAtivosDoObjetoEtiquetado(clienteEstabelecimento.Estabelecimento.IdEstabelecimento, TipoDeEtiquetaEnum.Agendamento, horario.Id).Select(e => e.Etiqueta.Conteudo).ToList();

                var eventoDeManterAgendamento = new EventoDeManterAgendamento()
                {
                    Dados = dados,
                    Tipo = tipoDeEventoEnum
                };

                Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.NotificarEventoParaIntegracaoComOutrosSistemas(horario.Estabelecimento, eventoDeManterAgendamento);
            }
            catch (Exception e)
            {
                var sb = new StringBuilder();

                var objErro = new
                {
                    IdDoEstabelecimento = horario.Estabelecimento.IdEstabelecimento,
                    IdDoCliente = horario.Cliente?.IdCliente ?? 0,
                    IdDoClienteNoEstabelecimento = clienteEstabelecimento?.Codigo ?? 0,
                    IdDoProfissional = horario.Profissional?.IdProfissional ?? 0,
                    IdDoProfissionalNoEstabelecimento = estabelecimentoProfissional?.Codigo ?? 0,
                    IdDoServico = horario.ServicoEstabelecimento.Servico?.IdServico ?? 0,
                    IdDoServicoNoEstabelecimento = horario.ServicoEstabelecimento?.IdServicoEstabelecimento ?? 0,
                    IdDoAgendamento = horario.Codigo ?? 0
                };

                sb.AppendLine(JsonConvert.SerializeObject(objErro));
                sb.AppendLine(e.Message);
                sb.AppendLine(e.StackTrace);
                var exception = new Exception("Erro ao realizar o envio dos dados de agendamento.") { Source = sb.ToString() };
                ErrorLog.GetDefault(HttpContext.Current).Log(new Error(exception));

                throw;
            }
        }

        public StatusHorario ObterStatusHorarioPorCodigoHorario(Int32 codigoHorario)
        {
            return DL.StatusHorarioRepository.ObterPorCodigoHorario(codigoHorario);
        }

        public IList<Horario> PrepararServicosParaAgendamentoEmLote(Horario primeiroAgendamento, IList<KeyValuePair<int, int>> servicosSelecionados, bool mesmoHorario)
        {
            var listaDeHorarios = new List<Horario>();

            //garantir que se foi desmarcado o serviço inicial, ele não apareça na lista de agendamentos
            foreach (var servico in servicosSelecionados)
            {
                if (servico.Key == primeiroAgendamento.ServicoEstabelecimento.IdServicoEstabelecimento)
                {
                    primeiroAgendamento.Duracao = servico.Value;
                    listaDeHorarios.Add(primeiroAgendamento);
                }
            }

            //logica do botão 'Serviços no mesmo horario'
            if (mesmoHorario)
            {
                foreach (var servico in servicosSelecionados)
                {
                    var horario = primeiroAgendamento.ObterCopia();
                    if (servico.Key != primeiroAgendamento.ServicoEstabelecimento.IdServicoEstabelecimento)
                    {
                        horario.ServicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(servico.Key);
                        horario.Valor = Domain.Pessoas.ServicoEstabelecimentoService.ObterValorReal(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);
                        horario.Duracao = servico.Value;
                        horario.Id = 0;
                        horario.Codigo = 0;
                        horario.EstabelecimentoProfissionalAssistente = null;

                        listaDeHorarios.Add(horario);
                    }
                }
            }
            //logica do botão 'Servico um depois do outro'
            else
            {
                foreach (var servico in servicosSelecionados)
                {
                    var horarioAnterior = new Horario();
                    bool semHorarioAnterior = true;
                    if (listaDeHorarios.Count != 0)
                    {
                        horarioAnterior = listaDeHorarios.Last();
                        semHorarioAnterior = false;
                    }

                    var horario = primeiroAgendamento.ObterCopia();
                    if (servico.Key != primeiroAgendamento.ServicoEstabelecimento.IdServicoEstabelecimento)
                    {
                        horario.ServicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(servico.Key);
                        horario.Valor = Domain.Pessoas.ServicoEstabelecimentoService.ObterValorReal(horario.ServicoEstabelecimento.IdServicoEstabelecimento, horario.DataInicio);
                        horario.DataInicio = semHorarioAnterior ? primeiroAgendamento.DataInicio : horarioAnterior.DataInicio.AddMinutes(horarioAnterior.Duracao);
                        horario.Duracao = servico.Value;
                        horario.Id = 0;
                        horario.Codigo = 0;
                        horario.EstabelecimentoProfissionalAssistente = null;

                        listaDeHorarios.Add(horario);
                    }
                }
            }

            return listaDeHorarios;
        }

        public void ProgramarNotificacoesDeCliente(Horario horario)
        {
            if (horario.Status == StatusHorarioEnum.Confirmado && horario.Profissional != null)
            {
                var estabelecimentoConfiguracaoGeral = horario.Estabelecimento.EstabelecimentoConfiguracaoGeral;
                var estabelecimentoEnviaNotificacao =
                    estabelecimentoConfiguracaoGeral.EnviarNotificacaoParaClientes &&
                    estabelecimentoConfiguracaoGeral.EnvioDeNotificacaoHabilitado;

                if (estabelecimentoEnviaNotificacao)
                {
                    var config = horario.Estabelecimento.EstabelecimentoConfiguracaoGeral;
                    var antecedencia = config.AntecedenciaDeNotificacaoEmMinutos;
                    var novaData = horario.DataInicio.AddMinutes(-antecedencia);

                    if (novaData < Calendario.Agora())
                    {
                        horario.DataHoraNotificacaoClienteProgramada = null;
                        return;
                    }
                    if (horario.DataHoraNotificacaoClienteEnviada != null && novaData.Date <= Calendario.Hoje())
                        return;

                    if (!config.EnvioDeNotificacaoHabilitado || !config.EnviarNotificacaoParaClientes)
                        return;

                    LimpaDataEnvioSeReprogramadaParaDepoisDeHoje(horario, novaData);
                    var antecedenciaMinimaParaEnvioNotificacaoForaDoHorario = new ParametrosTrinks<int>(ParametrosTrinksEnum.antecedencia_minima_para_envio_notificacao_fora_horario).ObterValor();

                    if (antecedencia == 0)
                        throw new Exception("Não existe antecedência definida.");
                    if (novaData >= Calendario.Agora())
                    {
                        horario.DataHoraNotificacaoClienteProgramada = novaData;
                        horario.DataDeEmissaoFilaParaEnvio = null;
                    }
                    else if (horario.DataInicio.AddHours(-1) >= horario.DataHoraCriacao && antecedencia >= antecedenciaMinimaParaEnvioNotificacaoForaDoHorario)
                    {
                        horario.DataHoraNotificacaoClienteProgramada = novaData;
                        horario.DataDeEmissaoFilaParaEnvio = null;
                    }

                    //esta parte verifica se a hora de disparo de notificacao é menor que o limite no parametroTrinks
                    //caso seja menor que o limite setar o disparo para a hora limite no parametro
                    //caso a hora do agendamento seja abaixo do limite no parametro entao não deve enviar notificacao
                    if (horario.DataHoraNotificacaoClienteProgramada.HasValue)
                    {
                        var horaLimite = TimeSpan.Parse(new ParametrosTrinks<string>(ParametrosTrinksEnum.horario_limite_para_envio_notificacao_cliente).ObterValor());
                        var dataHoraLimite = new DateTime(horario.DataHoraNotificacaoClienteProgramada.Value.Year, horario.DataHoraNotificacaoClienteProgramada.Value.Month, horario.DataHoraNotificacaoClienteProgramada.Value.Day, horaLimite.Hours, horaLimite.Minutes, horaLimite.Seconds);

                        if (horario.DataInicio < dataHoraLimite)
                        {
                            horario.DataHoraNotificacaoClienteProgramada = null;
                        }
                        else if (horario.DataHoraNotificacaoClienteProgramada.HasValue && horario.DataHoraNotificacaoClienteProgramada.Value < dataHoraLimite)
                        {
                            horario.DataHoraNotificacaoClienteProgramada = dataHoraLimite;
                        }
                    }
                }
                else if (horario.DataHoraNotificacaoClienteEnviada == null)
                {
                    LimpaDataDeNotificacaoProgramada(horario);
                }
            }
            else if (horario.DataHoraNotificacaoClienteEnviada == null)
            {
                LimpaDataDeNotificacaoProgramada(horario);
            }
        }

        public void ProgramarNotificacoesDeCliente(ClienteEstabelecimento clienteEstabelecimento)
        {
            var agendamentosFuturosConfirmadosEhQueRecebamNotificacao =
                Domain.Pessoas.HorarioRepository.ObterAgendamentosFuturosConfirmadosEhOndeClienteEstabelecimentoRecebaNotificacao(clienteEstabelecimento);
            foreach (var h in agendamentosFuturosConfirmadosEhQueRecebamNotificacao)
            {
                ProgramarNotificacoesDeCliente(h);
            }
        }

        public void ReprogramarProximasNotificacoesDeCliente(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var agendamentosFuturosConfirmadosEhQueRecebamNotificacao = Domain.Pessoas.HorarioRepository.ObterAgendamentosFuturosConfirmadosEhOndeClienteEstabelecimentoRecebaNotificacao(estabelecimento);

            foreach (var h in agendamentosFuturosConfirmadosEhQueRecebamNotificacao)
            {
                ProgramarNotificacoesDeCliente(h);
            }
        }

        private static HorarioHistorico GerarHistoricoCriacao(Horario horario, Transacao transacao, Comanda comanda = null)
        {
            var historico = new HorarioHistorico(horario, comanda: comanda, pessoaQueRealizou: horario.PessoaQuemMarcou);
            historico.Transacao = transacao;
            //horario.Historicos.Add(historico);
            return historico;
        }

        private static void LimpaDataDeNotificacaoProgramada(Horario horario)
        {
            horario.DataHoraNotificacaoClienteProgramada = null;
            horario.DataDeEmissaoFilaParaEnvio = null;
        }

        private static void LimpaDataEnvioSeReprogramadaParaDepoisDeHoje(Horario horario,
            DateTime novaData)
        {
            if (horario.DataHoraNotificacaoClienteEnviada != null && novaData.Date > Calendario.Hoje())
            {
                horario.DataHoraNotificacaoClienteEnviada = null;
            }
        }

        public void GerarHistoricoParaUsoDeProduto(Horario horario)
        {
            GerarHistorico(horario, forcarGeracao: true);
        }

        private void GerarHistorico(Horario horario, Transacao transacao = null, bool forcarGeracao = false, string observacao = null, Comanda comanda = null, bool alteradoPorAgendaCliente = false, bool ehComandaRapida = false)
        {
            if (horario.Historicos.Any(f => f.Codigo == 0))
                return;

            HorarioHistorico historico = null;

            if (!horario.Codigo.HasValue || horario.Codigo == 0)
            {
                historico = GerarHistoricoCriacao(horario, transacao, comanda);
            }
            else
            {
                historico = GerarHistoricoAlteracao(horario, transacao, forcarGeracao, observacao, comanda: comanda);
            }

            if (historico != null)
            {
                horario.Historicos.Add(historico);
                historico.AlteradoPorComandaRapida = ehComandaRapida;

                if (alteradoPorAgendaCliente)
                {
                    historico.AlteradoNaAgendaClientePorProfissionalAcessoSomenteAEla = VerificarSeFoiAlteradoPorProfissionalSomenteComAcessoAAgendaCliente(historico);
                }
            }
        }

        private bool VerificarSeFoiAlteradoPorProfissionalSomenteComAcessoAAgendaCliente(HorarioHistorico historico)
        {
            bool retorno = false;

            var estabelecimentoProfissionalQueAlterouAgendamento =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(
                historico.PessoaQuemRealizouAlteracao.IdPessoaFisica, historico.Estabelecimento.IdEstabelecimento);

            if (estabelecimentoProfissionalQueAlterouAgendamento == null)
                retorno = false;
            else
            {
                retorno = estabelecimentoProfissionalQueAlterouAgendamento.UsuarioEstabelecimento.PerfilAcesso == AcessoBackoffice.SomenteMinhaAgendaEPainelAtendimento;
            }

            return retorno;
        }

        private HorarioHistorico GerarHistoricoAlteracao(Horario horario, Transacao transacao = null, bool forcarGeracao = false, string observacao = null, Comanda comanda = null)
        {
            var idProfissional = horario.Profissional != null ? horario.Profissional.IdProfissional : 0;
            var hh = horario.ObterHistoricoMaisRecente();

            decimal? valorPagoAntecipado = ObterValorPagoAntecipadamente(horario, hh);

            if (hh != null)
            {
                var idProfissionalHistorico = hh.Profissional != null ? hh.Profissional.IdProfissional : 0;
                var alterado = horario.Cliente != hh.Cliente
                       || horario.DataInicio != hh.DataInicio
                       || horario.Duracao != hh.Duracao
                       || idProfissional != idProfissionalHistorico
                       || horario.Status.Codigo != hh.Status.Codigo
                       || horario.ServicoEstabelecimento.IdServicoEstabelecimento != hh.ServicoEstabelecimento.IdServicoEstabelecimento
                       || horario.Valor != hh.Valor
                       || (horario.EstabelecimentoProfissionalAssistente == null && hh.NomeAssistente != null && hh.NomeAssistente != "-")
                       || (horario.EstabelecimentoProfissionalAssistente != null && horario.EstabelecimentoProfissionalAssistente.Profissional.PessoaFisica.ObterApelidoOuNome() != hh.NomeAssistente)
                       || comanda != null && hh.IdComanda != comanda.Id
                       || valorPagoAntecipado != hh.ValorPagoAntecipadamente
                       || horario.FoiPagoAntecipadamente != (valorPagoAntecipado != null);

                if (!alterado && !forcarGeracao)
                    return null;
            }

            var pessoaQueAlterou = ContextHelper.Instance.IdConta.HasValue
                ? Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value).Pessoa.PessoaFisica
                : null;

            var horarioHistorico = new HorarioHistorico(horario, observacao, false, comanda, pessoaQueRealizou: pessoaQueAlterou);
            horarioHistorico.Transacao = transacao;
            horarioHistorico.ValorPagoAntecipadamente = valorPagoAntecipado;
            return horarioHistorico;
        }

        public decimal? ObterValorPagoAntecipadamente(Horario horario, HorarioHistorico ultimoHistorico = null)
        {
            decimal? valorPagoAntecipado = null;

            if (horario.FoiPagoAntecipadamente)
            {
                if (ultimoHistorico != null && ultimoHistorico.ValorPagoAntecipadamente != null)
                    valorPagoAntecipado = ultimoHistorico.ValorPagoAntecipadamente;
                else
                    valorPagoAntecipado = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ObterValorPagoAntecipadamenteParaOServico(horario.Codigo ?? horario.Id);
            }

            return valorPagoAntecipado;
        }

        public MeusAgendamentosDTO ObterMeusAgendamentosDoDiaDTO(int idHorario)
        {
            var dadosHorarioDto = Domain.Pessoas.HorarioRepository.ObterDadosDoClientePeloHorario(idHorario);

            var dadosDoEstabelecimentoParaOCliente = Domain.Pessoas.EstabelecimentoService.ObterDadosDoEstabelecimentoParaOClienteDTO(dadosHorarioDto.IdEstabelecimento);

            var dadosDoClienteEstabelecimentoDto = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterDadosDoClienteEstabelecimentoParaLembreteSmsDto(dadosHorarioDto.IdCliente, dadosHorarioDto.IdEstabelecimento);

            var listaDeAgendamentos = Domain.Pessoas.HorarioRepository
                .ObterAgendamentosDoDiaPorIdClienteEIdEstabelecimento(dadosHorarioDto.DataInicio, dadosHorarioDto.IdEstabelecimento, dadosHorarioDto.IdCliente)
                .Select(h => new MeuAgendamentoDTO
                {
                    IdAgendamento = h.Id,
                    NomeServico = h.ServicoEstabelecimento.Nome,
                    NomeProfissional = h.Profissional.PessoaFisica.NomeOuApelido(),
                    StatusCodigo = (StatusHorarioEnum)h.Status.Codigo,
                    StatusNome = h.Status.Nome,
                    StatusCor = h.Status.Cor,
                    DuracaoEmMinutos = h.Duracao,
                    DataInicio = h.DataInicio,
                    PagoAntecipado = h.FoiPagoAntecipadamente
                })
                .OrderBy(h => h.DataInicio)
                .ToList();

            foreach (var agendamento in listaDeAgendamentos)
            {
                agendamento.PermiteCancelar = AgendamentoPodeSerCanceladoPeloProprioCliente(agendamento);
            }

            return new MeusAgendamentosDTO(dadosDoEstabelecimentoParaOCliente, dadosDoClienteEstabelecimentoDto, dadosHorarioDto, listaDeAgendamentos);
        }

        private bool AgendamentoPodeSerCanceladoPeloProprioCliente(MeuAgendamentoDTO agendamento)
        {
            return agendamento.EhCompromissoFuturo
                && !agendamento.PagoAntecipado
                && !(agendamento.StatusCodigo == StatusHorarioEnum.Cancelado || agendamento.StatusCodigo == StatusHorarioEnum.Cliente_Faltou || agendamento.StatusCodigo == StatusHorarioEnum.Finalizado);
        }

        public MeusAgendamentosDTO ObterMeusAgendamentosDoDiaDTOParaAgendamentosPassados(int idHorario)
        {
            MeusAgendamentosDTO dados = null;

            var dadosHorarioDto = Domain.Pessoas.HorarioRepository.ObterDadosDoClientePeloHorario(idHorario);

            if (dadosHorarioDto == null)
            {
                dados = new MeusAgendamentosDTO();
                string urlAppAndroid = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_download_app_android).ObterValor();
                dados.AppAndroid = new DownloadDeAppDTO(urlAppAndroid);

                string urlAppIos = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_download_app_ios).ObterValor();
                dados.AppIos = new DownloadDeAppDTO(urlAppIos);
            }
            else
            {
                var dadosDoEstabelecimentoParaOCliente = Domain.Pessoas.EstabelecimentoService.ObterDadosDoEstabelecimentoParaOClienteDTO(dadosHorarioDto.IdEstabelecimento);

                var dadosDoClienteEstabelecimentoDto = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterDadosDoClienteEstabelecimentoParaLembreteSmsDto(dadosHorarioDto.IdCliente, dadosHorarioDto.IdEstabelecimento);

                dados = new MeusAgendamentosDTO(dadosDoEstabelecimentoParaOCliente, dadosDoClienteEstabelecimentoDto, dadosHorarioDto);
            }

            dados.EhAgendamentoPassado = true;
            return dados;
        }

        public int? ObterIdEstabelecimentoPorIdHorario(int idHorario)
        {
            return Domain.Pessoas.HorarioRepository.Queryable().Where(h => h.Id == idHorario).Select(h => (int?)h.Estabelecimento.IdEstabelecimento).FirstOrDefault();
        }

        public Estabelecimento ObterEstabelecimentoPorIdHorario(int idHorario)
        {
            return Domain.Pessoas.HorarioRepository.Queryable().Where(h => h.Id == idHorario).Select(h => h.Estabelecimento).FirstOrDefault();
        }

        public void AssociarEtiquetasAoHorario(Horario horario, List<int> idsEtiquetas)
        {
            if (!horario.Codigo.HasValue) return;

            var dados = new EtiquetarObjetoDTO
            {
                IdDono = horario.Estabelecimento.IdEstabelecimento,
                Tipo = Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento,
                IdsEtiquetasAssociadas = idsEtiquetas,
                IdObjeto = horario.Codigo.Value
            };
            Domain.Marcadores.ManterEtiquetasService.ManterEtiquetasDoObjeto(dados);
        }

        public void AssociarEtiquetasAosHorarios(List<Horario> horarios, List<int> idsEtiquetas)
        {
            var estabelecimento = horarios.Select(h => h.Estabelecimento).FirstOrDefault();
            var dados = new EtiquetarMultiplosObjetosDTO
            {
                IdDono = estabelecimento.IdEstabelecimento,
                Tipo = Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento,
                IdsEtiquetasAssociadas = idsEtiquetas,
                IdsObjetos = horarios.Where(h => h.Codigo.HasValue && h.Codigo.Value > 0).Select(h => h.Codigo.Value).ToList()
            };
            Domain.Marcadores.ManterEtiquetasService.ManterEtiquetasEmMultiplosObjetosDeMesmoTipo(dados);
        }

        public void GerarHistoricoAssociacaoDeEtiqueta(int idEtiqueta, int? idObjetoEtiquetado = null)
        {
            var queryableObjetoEtiquetado = Domain.Marcadores.ObjetoEtiquetadoRepository.Queryable();
            var queryableHorarios = Domain.Pessoas.HorarioRepository.Queryable();

            var etiqueta = Domain.Marcadores.EtiquetaRepository.ObterPorId(idEtiqueta);
            var horarios = new List<Horario>();

            if (etiqueta.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento && idObjetoEtiquetado.HasValue)
            {
                horarios = (from hor in queryableHorarios
                            where hor.Id == idObjetoEtiquetado.Value
                            select hor).ToList();
            }
            else if (etiqueta.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento)
            {
                horarios = (from hor in queryableHorarios
                            join obj in queryableObjetoEtiquetado on hor.Id equals obj.IdObjetoEtiquetado
                            where obj.Etiqueta.IdEtiqueta == idEtiqueta && !hor.FoiPago &&
                                  hor.Status != StatusHorarioEnum.Cancelado && hor.Status != StatusHorarioEnum.Cliente_Faltou
                            select hor).ToList();
            }
            else if (idObjetoEtiquetado.HasValue)
            {
                horarios = (from hor in queryableHorarios
                            where hor.ClienteEstabelecimento.Codigo == idObjetoEtiquetado.Value && !hor.FoiPago &&
                                  hor.Status != StatusHorarioEnum.Cancelado && hor.Status != StatusHorarioEnum.Cliente_Faltou
                            select hor).ToList();
            }
            else
            {
                List<int> ids = (from obj in queryableObjetoEtiquetado
                                 where obj.Etiqueta.IdEtiqueta == idEtiqueta
                                 select obj.IdObjetoEtiquetado).ToList();

                if (ids.Any())
                    horarios = (from hor in queryableHorarios
                                where ids.Contains(hor.ClienteEstabelecimento.Codigo) && !hor.FoiPago &&
                                      hor.Status != StatusHorarioEnum.Cancelado && hor.Status != StatusHorarioEnum.Cliente_Faltou
                                select hor).ToList();
            }

            foreach (var horario in horarios)
            {
                bool estaEtiquetado = false;
                if (etiqueta.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento)
                    estaEtiquetado = queryableObjetoEtiquetado.Any(o => o.Etiqueta.IdEtiqueta == idEtiqueta && o.IdObjetoEtiquetado == horario.Id && o.Etiqueta.Ativo);
                else
                    estaEtiquetado = queryableObjetoEtiquetado.Any(o => o.Etiqueta.IdEtiqueta == idEtiqueta && o.IdObjetoEtiquetado == horario.ClienteEstabelecimento.Codigo && o.Etiqueta.Ativo);

                var historico = GerarHistoricoAlteracao(horario, transacao: null, forcarGeracao: true, observacao: null);
                var obsEtiquetas = string.Format(estaEtiquetado ? "Incluído: {0}" : "Removido: {0}", etiqueta.Conteudo);

                var historicoEtiqueta = new HorarioHistoricoEtiqueta(horario, historico, obsEtiquetas);
                Domain.Pessoas.HorarioHistoricoEtiquetaRepository.SaveNewNoFlush(historicoEtiqueta);

                Domain.Pessoas.HorarioHistoricoRepository.SaveNewNoFlush(historico);
            }

            Domain.Pessoas.HorarioHistoricoRepository.Flush();
        }

        public void AssociarAssistenteAoHorario(Horario horario, EstabelecimentoProfissional estabelecimentoProfissionalAssistente)
        {
            if (horario == null)
                return;

            horario.EstabelecimentoProfissionalAssistente = null;

            if (estabelecimentoProfissionalAssistente == null)
                return;

            bool profissionalPrincipalPodeTerAssistente = Domain.Pessoas.EstabelecimentoProfissionalService.ProfissionalPodeTerAssistente(horario.Profissional.IdProfissional, horario.ServicoEstabelecimento.IdServicoEstabelecimento);
            bool profissionalAssistenteRealizaOServicoComoAssistente = Domain.Pessoas.EstabelecimentoAssistenteServicoRepository.AtuaComoAssistenteNoServico(estabelecimentoProfissionalAssistente.Codigo, horario.ServicoEstabelecimento.IdServicoEstabelecimento);

            if (profissionalPrincipalPodeTerAssistente && profissionalAssistenteRealizaOServicoComoAssistente)
            {
                horario.EstabelecimentoProfissionalAssistente = estabelecimentoProfissionalAssistente;
            }
        }

        public bool ClientePodeSerEditado(int idHorario)
        {
            bool pagoAntecipadamente = Domain.Pessoas.HorarioRepository.FoiPagoAntecipadamente(idHorario);
            return !pagoAntecipadamente;
        }

        public Horario LoadSomentePermitidoAoUsuarioLogado(int idHorario)
        {
            var idConta = Domain.WebContext.IdContaAutenticada.Value;
            var conta = Domain.Pessoas.ContaRepository.Load(idConta);
            var horario = Domain.Pessoas.HorarioRepository.Load(idHorario, false);
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(conta.Pessoa.IdPessoa, horario.Estabelecimento.IdEstabelecimento);

            return estabelecimentoProfissional != null ? horario : null;
        }

        public void AssociarPessoaAutenticadaComoAssistente(int idHorario)
        {
            var horario = Domain.Pessoas.HorarioService.LoadSomentePermitidoAoUsuarioLogado(idHorario);
            if (horario == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Agendamento não encontrado");

            var pessoaAutenticada = Domain.Pessoas.ContaRepository.ObterPessoaFisicaDaConta(Domain.WebContext.IdContaAutenticada.Value);
            var assistente = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(pessoaAutenticada.IdPessoa, horario.Estabelecimento.IdEstabelecimento);

            if (assistente == null)
                ValidationHelper.Instance.AdicionarItemValidacao("O usuário não está cadastrado como profissional");

            var podeParticiparComoAssistenteNoHorario = Domain.Pessoas.HorarioRepository.ObterFiltroDeHorariosQueAssistentePodeParticipar(assistente).Any(h => h.Id == idHorario);

            if (!podeParticiparComoAssistenteNoHorario)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O assistente não pode ser associado ao serviço");
            }

            if (ValidationHelper.Instance.IsValid)
            {
                horario.EstabelecimentoProfissionalAssistente = assistente;
                ManterHorario(horario);
            }
        }

        public bool PermiteAcompanhamentos(Horario horario)
        {
            return horario.Id > 0 && horario.Status != StatusHorarioEnum.Cancelado && horario.DataInicio.Date <= Calendario.Hoje();
        }

        public long ContarAgendamentosOnline(int idEstabelecimento)
        {
            return Domain.Pessoas.HorarioRepository.Queryable(true)
                .Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            h.HorarioOrigem != HorarioOrigemEnum.Balcao &&
                            h.Ativo).LongCount();
        }

        public long ObterMediaDosAgendamentosFinalizados(int idEstabelecimento, int quantidadeDeMesesAtras)
        {
            var dataDeMesesAtras = DateTime.Now.AddMonths(-quantidadeDeMesesAtras).Date;

            var quantidadeDeAtendimentosFinalizadosNoPeriodo = (
                from t in Domain.Financeiro.TransacaoRepository.Queryable()
                join e in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    on t.PessoaQueRecebeu.IdPessoa equals e.PessoaJuridica.IdPessoa
                let tfp = (from f in Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable()
                           where f.Transacao.Id == t.Id
                           select f.IdTransacaoFormaPagamento)
                let tfpp = (from ff in Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable()
                            join fp in Domain.Financeiro.FormaPagamentoRepository.Queryable()
                                on ff.FormaPagamento.Id equals fp.Id
                            where ff.Transacao.Id == t.Id
                            select ff.IdTransacaoFormaPagamento)
                where
                    t.Ativo &&
                    e.IdEstabelecimento == ContextHelper.Instance.IdEstabelecimento.Value &&
                    t.DataReferencia >= dataDeMesesAtras &&
                    t.DataReferencia <= Calendario.Agora() &&
                    t.IdTransacaoQueEstounouEsta == null &&
                    t.TipoTransacao == TipoTransacaoEnum.Pagamento &&
                    (!tfp.Any() || tfpp.Any())
                select t).LongCount();

            return quantidadeDeAtendimentosFinalizadosNoPeriodo / quantidadeDeMesesAtras;
        }
    }
}