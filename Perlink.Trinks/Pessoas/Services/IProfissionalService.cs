﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Web;

namespace Perlink.Trinks.Pessoas.Services
{
    public interface IProfissionalService : IService
    {
        FotoPessoa DefinirFotoPadrao(FotoPessoa foto, HttpPostedFileBase arquivo);
        FotoPessoa DefinirFotoPadraoApi(FotoPessoa foto, HttpPostedFile arquivo);
        EstabelecimentoProfissional ObterProfissionalPorEmail(string email, int idEstabelecimento);
        Profissional ObterProfissionalPorCpf(string cpf);
        EstabelecimentoProfissional ObterProfissional(int idProfissional, int idEstabelecimento);
        EstabelecimentoProfissional ObterProfissionalInativo(int idProfissional, int idEstabelecimento);
        EstabelecimentoProfissional ObterProfissionalPorPessoaFisica(int idPessoaFisica, int idEstabelecimento);
        void AssociarServicosDoEstabelecimentoAoProfissional(Estabelecimento estabelecimento, PessoaFisica pessoaFisica);
        EstabelecimentoProfissional ManterProfissional(EstabelecimentoProfissional estabelecimentoProfissional, String email, Boolean alterouEmailExistente = false, bool manterInscricoesDeNotificacoes = true, bool recebeResumoDiarioDoFaturamentoEAgendaFutura = true, bool recebeDespesasSemanal = true, bool recebeEstoqueSemanal = true, PessoaFisica pessoaUnificar = null);
        void ManterInscricoesEmNotificacoes(EstabelecimentoProfissional estabelecimentoProfissional, bool recebeResumoDiarioDoFaturamentoEAgendaFutura, bool recebeDespesasSemanal, bool recebeEstoqueSemanal);
        UsuarioEstabelecimento AssociarComoUsuarioAdministrativo(EstabelecimentoProfissional estabelecimentoProfissional, AcessoBackoffice acessoBackoffice);
        IList<EstabelecimentoProfissional> ListarProfissionaisPorServicoEstabelecimento(int idServicoEstabelecimento);
        IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(int idCategoriaServico);
        IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(List<int> lista);
        IList<Horario> ListarHorariosProfissionalPorIntervaloData(int idProfissional, int idEstabelecimento, IntervaloData intervalo);
        void EnviarNotificacaoDeEventoParaIntegracaoComOutrosSistemas(EstabelecimentoProfissional estabelecimentoProfissional, TipoDeEventoEnum TipoDeEventoEnum);
        List<ProfissionalAssistenteDTO> ListaDeAssistentesPorServico(String codigoInternoServico, int idProfissional, int idServicoEstabelecimento, int idEstabelecimento);
        IntervaloDataList ListarIntervalosOcupadosNoPeriodo(int idProfissional, int idEstabelecimento, IntervaloData intervalo);
        IntervaloDataList ListarIntervalosAusenciasProfissionalNoPeriodo(int idProfissionalEstabelecimento, IntervaloData intervalo);
        IList<PeriodoAusencia> ListarAusenciasProfissionalPorIntervaloData(int idProfissionalEstabelecimento, IntervaloData intervalo);
        ProfissionalDto ObterProfissionalPorContaEEstabelecimento(int idConta, int idEstabelecimento);
    }
}
