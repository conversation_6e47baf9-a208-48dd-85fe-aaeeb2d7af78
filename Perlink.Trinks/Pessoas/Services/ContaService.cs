﻿using Newtonsoft.Json;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Repositories;
using Perlink.Trinks.Resources;
using Perlink.Trinks.ValidacaoDeIdentidade.DTO;
using Perlink.Trinks.ValidacaoDeIdentidade.Enums;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;
using RES = Perlink.Trinks.Resources.Pessoas.Services.ContaService;

namespace Perlink.Trinks.Pessoas.Services
{

    public class ContaService : BaseService, IContaService
    {

        #region Propriedades de Apoio

        private IContaRepository _contaRepository;

        public static IHttpSessionState SessionState
        {
            get { return SessionStateUtility.GetHttpSessionStateFromContext(HttpContext.Current); }
        }

        public IClienteRepository ClienteRepository
        {
            get { return Domain.Pessoas.ClienteRepository; }
        }

        public IPessoaService PessoaService
        {
            get { return Domain.Pessoas.PessoaService; }
        }

        private IContaRepository ContaRepository
        {
            get { return _contaRepository ?? (_contaRepository = Domain.Pessoas.ContaRepository); }
        }

        private IEnvioEmailService EnvioEmailService
        {
            get { return Domain.Pessoas.EnvioEmailService; }
        }

        private IFotoService FotoService
        {
            get { return Domain.Pessoas.FotoService; }
        }

        #endregion Propriedades de Apoio

        #region Validações

        public void ValidarLogin(string email, string senha)
        {
            var hash = FormsAuthentication.HashPasswordForStoringInConfigFile(senha, "SHA1");
            var contaExiste = ContaRepository.Existe(email, hash);

            if (!contaExiste)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemEmailSenhaNaoConfere);
        }

        public void ValidarLogin(Conta conta)
        {
            if (conta == null)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemEmailSenhaNaoConfere);
        }

        private void ValidarContaAtiva(Conta conta)
        {
            if (conta != null && !conta.Ativo)
            {
                EnvioEmailService.EnviarEmailContaBloqueada(conta);
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemPerfilInativo);
            }
        }

        private void ValidarEmailInexistente(string login)
        {
            var ehId = login.SomenteNumeros() == login;

            if (!ehId)
            {
                var contaExiste = ContaRepository.Existe(login);
                if (!contaExiste)
                    ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemEmailSenhaInvalidos);
            }
        }

        private void ValidarSeIdFacebookJaEstaAssociadoAOutraConta(String codigoFacebook)
        {
            var existeFacebookAssociado = ContaRepository.ExisteFacebookAssociado(codigoFacebook);
            if (existeFacebookAssociado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("erro-ja-associado");
            }
        }

        #endregion Validações

        #region Métodos Públicos

        public Estabelecimento AlternarEstabelecimento(int idEstabelecimento)
        {
            var conta = ObterContaAutenticada();
            if (conta == null)
                return null;
            return AlternarUsuarioEstabelecimento(idEstabelecimento, conta);
        }

        public void AlternarFranquia(int idFranquia)
        {
            ContextHelper.Instance.IdFranquia = idFranquia;
        }

        [TransactionInitRequired]
        public UsuarioEstabelecimento AssociarContaComEstabelecimento(Estabelecimento estabelecimento, PessoaFisica pessoaFisica,
            AcessoBackoffice acessoBackoffice)
        {
            var usuarioAdm =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Estabelecimento == estabelecimento &&
                            f.PessoaFisica.IdPessoa == pessoaFisica.IdPessoaFisica);

            if (usuarioAdm == null && acessoBackoffice != AcessoBackoffice.Nao_Possui)
            {
                usuarioAdm = new UsuarioEstabelecimento
                {
                    Estabelecimento = estabelecimento,
                    PessoaFisica = pessoaFisica,
                    ExibeProfissionaisEmDiaDeFolga = true
                };
                estabelecimento.VinculoUsuarios.Add(usuarioAdm);
                pessoaFisica.VinculoEstabelecimentos.Add(usuarioAdm);
            }
            else if (usuarioAdm != null && acessoBackoffice != AcessoBackoffice.Nao_Possui)
            {
                usuarioAdm.DataAtualizacao = Calendario.Agora();
                usuarioAdm.Ativo = true;
            }
            else if (usuarioAdm != null)
            {
                usuarioAdm.Ativo = false;
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Update(usuarioAdm);
                return usuarioAdm;
            }
            else
            {
                return null;
            }

            usuarioAdm.EhResponsavel = (acessoBackoffice == AcessoBackoffice.Acesso_total);
            usuarioAdm.PerfilAcesso = acessoBackoffice;

            if (usuarioAdm.IdUsuarioEstabelecimento > 0)
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Update(usuarioAdm);
            else
                Domain.Pessoas.UsuarioEstabelecimentoRepository.SaveNew(usuarioAdm);
            switch (acessoBackoffice)
            {
                case AcessoBackoffice.Acesso_total:
                    Domain.Permissoes.UsuarioPerfilService.DefinirPerfilUsuario(usuarioAdm, 1);
                    break;

                case AcessoBackoffice.Somente_Agenda:
                    Domain.Permissoes.UsuarioPerfilService.DefinirPerfilUsuario(usuarioAdm, 2);
                    break;
            }
            return usuarioAdm;
        }

        public void ConfirmarConta(Conta conta)
        {
            if (!conta.Confirmada && UsuarioVerificouEmailOuAlgumTelefone(conta))
            {
                conta.ConfirmarConta();
                Domain.Pessoas.ContaRepository.Update(conta);
            }

            ContextHelper.Instance.PrecisaConfirmarConta = conta.PrecisaConfirmar();
        }

        private bool UsuarioVerificouEmailOuAlgumTelefone(Conta conta)
        {
            var emailVerificado = Domain.ValidacaoDeIdentidade.EmailVerificadoRepository.JaVerificouEmail(conta.IdConta, conta.Email);
            var telefoneVerificado = Domain.ValidacaoDeIdentidade.TelefoneVerificadoRepository.ExisteComMesmaConta(conta.IdConta);
            return emailVerificado || telefoneVerificado;
        }

        public UsuarioEstabelecimento AssociarContaComEstabelecimento(Estabelecimento estabelecimento, PessoaFisica pessoaFisica,
           UsuarioPerfilEnum usuarioPerfilEnum)
        {
            var usuarioAdm =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Estabelecimento == estabelecimento &&
                            f.PessoaFisica.IdPessoa == pessoaFisica.IdPessoaFisica);

            if (usuarioAdm == null && usuarioPerfilEnum != UsuarioPerfilEnum.SemAcesso)
            {
                usuarioAdm = new UsuarioEstabelecimento
                {
                    Estabelecimento = estabelecimento,
                    PessoaFisica = pessoaFisica,
                    ExibeProfissionaisEmDiaDeFolga = true
                };
                estabelecimento.VinculoUsuarios.Add(usuarioAdm);
                pessoaFisica.VinculoEstabelecimentos.Add(usuarioAdm);
            }
            else if (usuarioAdm == null && usuarioPerfilEnum == UsuarioPerfilEnum.SemAcesso)
            {
                return null;
            }
            else if (usuarioAdm != null)
            {
                usuarioAdm.DataAtualizacao = Calendario.Agora();
                usuarioAdm.Ativo = true;
            }

            if (usuarioAdm.IdUsuarioEstabelecimento > 0)
            {
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Update(usuarioAdm);
            }
            else
            {
                Domain.Pessoas.UsuarioEstabelecimentoRepository.SaveNew(usuarioAdm);
            }

            Domain.Permissoes.UsuarioPerfilService.DefinirPerfilUsuario(usuarioAdm, (int)usuarioPerfilEnum);

            Domain.Pessoas.UsuarioEstabelecimentoRepository.Flush();

            return usuarioAdm;
        }

        [TransactionInitRequired]
        public void AssociarFacebookAConta(Int32 idPessoa, String usernameFacebook, String codigoFacebook)
        {
            ValidarSeIdFacebookJaEstaAssociadoAOutraConta(codigoFacebook);

            if (ValidationHelper.Instance.IsValid)
            {
                var conta = ContaRepository.ObterContaPorIdPessoa(idPessoa);
                conta.CodigoFacebook = codigoFacebook;

                if (conta.Pessoa.PessoaFisica.FotoPrincipal == null ||
                    !conta.Pessoa.PessoaFisica.FotoPrincipal.Codigo.HasValue)
                {
                    var fotoPessoa = new FotoPessoa { Pessoa = conta.Pessoa, Principal = true };

                    try
                    {
                        FotoService.SalvarFotoPorUrl(fotoPessoa,
                            String.Format(ConfiguracoesTrinks.Facebook.LinkParaFotoPerfil, usernameFacebook));
                    }
                    catch { }
                }
                else
                {
                    ContaRepository.Update(conta);
                }
            }
        }

        [TransactionInitRequired]
        public void AtivarConta(Int32 idConta, bool origemAgendamentoHotsite = false, int? idEstabelecimentoParaEnvioDeEmail = null, bool forcaAtivacaoDeConta = false)
        {
            var conta = ObterConta(idConta);
            if (forcaAtivacaoDeConta || (conta != null && !conta.Confirmada))
            {
                var contasFinanceiras = Domain.Cobranca.ContaFinanceiraRepository.ObterContasAtivasNaoConfirmadas(conta.Pessoa);
                AtivarConta(conta, contasFinanceiras, origemAgendamentoHotsite, idEstabelecimentoParaEnvioDeEmail);
            }
        }

        [TransactionInitNotRequired]
        public void AtivarConta(Conta conta, IList<ContaFinanceira> contasFinanceiras, bool origemAgendamentoHotsite = false, int? idEstabelecimentoParaEnvioDeEmail = null)
        {
            if (!conta.DataConfirmacaoGuid.HasValue)
            {
                conta.Ativo = true;
                //conta.DataConfirmacaoGuid = Calendario.Agora();

                ContaRepository.Update(conta);

                foreach (var c in contasFinanceiras)
                {
                    c.Status = StatusContaFinanceira.PeriodoGratis;
                    Domain.Cobranca.ContaFinanceiraRepository.Update(c);
                }

                if (!contasFinanceiras.Any())
                { // Confirmação de responsável de estabelecimento
                  //Domain.Pessoas.EnvioEmailService.EnviarEmailConfirmacaoCadastro(conta);
                  //}else {
                  //  // Confirmação de cliente Web
                    var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(conta.Pessoa.IdPessoa);
                    Domain.Pessoas.EnvioEmailService.EnviarEmailBoasVindasClienteWeb(cliente, origemAgendamentoHotsite, idEstabelecimentoParaEnvioDeEmail);
                }
            }
            else if (contasFinanceiras != null && contasFinanceiras.Any(cf => cf.Status.IdStatus == (int)StatusContaFinanceira.ContaNaoConfirmada))
            {
                CorrigirContasFinanceirasAindaNaoConfirmadasParaPeriodoGratis(contasFinanceiras);
            }
        }

        private void CorrigirContasFinanceirasAindaNaoConfirmadasParaPeriodoGratis(IList<ContaFinanceira> contasFinanceiras)
        {
            var contasFinanceirasAindaNaoConfirmadas = contasFinanceiras.Where(cf => cf.Status.IdStatus == (int)StatusContaFinanceira.ContaNaoConfirmada).ToList();
            foreach (var c in contasFinanceirasAindaNaoConfirmadas)
            {
                c.Status = StatusContaFinanceira.PeriodoGratis;
                Domain.Cobranca.ContaFinanceiraRepository.Update(c);
            }
        }

        public void AutenticarComoConta(string emailUsuarioComo)
        {
            var conta = Domain.Pessoas.ContaRepository.ObterContaPorEmail(emailUsuarioComo);

            if (ValidationHelper.Instance.IsValid)
                ValidarLogin(conta);
            if (ValidationHelper.Instance.IsValid)
                ValidarContaAtiva(conta);

            if (ValidationHelper.Instance.IsValid)
            {
                AutenticarConta(conta);
            }
        }

        [TransactionInitRequired]
        public void AutenticarConta(string email, String codigoFacebook)
        {
            var conta = ObterContaPorEmail(email);
            AutenticarConta(conta);

            if (ValidationHelper.Instance.IsValid && !String.IsNullOrEmpty(conta.CodigoFacebook))
            {
                conta.CodigoFacebook = codigoFacebook;
                ContaRepository.Update(conta);
            }
        }

        public void AutenticarConta(string email)
        {
            var conta = ObterContaPorEmail(email);
            AutenticarConta(conta);
        }

        public void AutenticarConta(string email, string senha, bool lembrarSenha, String codigoFacebook)
        {
            ValidarEmailInexistente(email);

            var conta = ObterConta(email, senha);

            if (ValidationHelper.Instance.IsValid)
                ValidarLogin(conta);
            if (ValidationHelper.Instance.IsValid)
                ValidarContaAtiva(conta);

            if (ValidationHelper.Instance.IsValid)
            {
                if (!String.IsNullOrEmpty(codigoFacebook))
                    conta.CodigoFacebook = codigoFacebook;

                AutenticarConta(conta);

                IncluirEmailNoCookie(lembrarSenha ? conta.Email : String.Empty);
            }
        }

        public void AutenticarConta(Conta conta)
        {
            if (conta == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Login ou senha incorretos.");

            if (!ValidationHelper.Instance.IsValid)
                return;

            PopularSessaoEGravarDataDoUltimoLogin(conta);
            GravarInicioDeLoginParaOfertaDePromocaoTrinks(conta);
        }

        private void GravarInicioDeLoginParaOfertaDePromocaoTrinks(Conta conta)
        {
            ContextHelper.Instance.EhParaOfertarPromocaoTrinksAgora(true);
        }

        public void AutenticarContaMobile(string email, string senha, out Conta contaAutenticada)
        {
            ValidarEmailInexistente(email);

            Conta conta = null;

            if (ValidationHelper.Instance.IsValid)
                conta = ObterConta(email, senha);

            if (ValidationHelper.Instance.IsValid)
                ValidarLogin(conta);
            if (ValidationHelper.Instance.IsValid)
                ValidarContaAtiva(conta);

            contaAutenticada = ValidationHelper.Instance.IsValid ? conta : null;
        }

        public bool Existe(string email)
        {
            return ContaRepository.Existe(email);
        }

        public void IncluirEmailNoCookie(String email)
        {
            HttpCookie UserCookie;
            if (HttpContext.Current.Request.Cookies["CookieEmailUsuario"] == null)
            {
                UserCookie = new HttpCookie("CookieEmailUsuario") { Value = email, Expires = Calendario.Agora().AddDays(20) };
                HttpContext.Current.Response.Cookies.Add(UserCookie);
            }
            else
            {
                UserCookie = HttpContext.Current.Request.Cookies["CookieEmailUsuario"];
                UserCookie.Value = email;

                UserCookie.Expires = Calendario.Agora().AddDays(20);
                HttpContext.Current.Response.Cookies.Set(UserCookie);
            }
        }

        public void ManterConta(Conta conta)
        {
            if (conta.IdConta > 0)
            {
                ContaRepository.Evict(conta);
                var contaAntiga = ContaRepository.Load(conta.IdConta);

                if (contaAntiga.Senha != conta.Senha)
                    Domain.Pessoas.EnvioEmailService.EnviarEmailSenhaAlterada(conta);

                ContaRepository.Evict(contaAntiga);
                ContaRepository.Update(conta);
            }
            else
            {
                ContaRepository.SaveNew(conta);
            }

            var cliente = ClienteRepository.ObterPorPessoaFisica(conta.Pessoa.IdPessoa);
            if (cliente != null)
            {
                if (cliente.PessoaFisica.Contas.Any(f => f.Ativo))
                {
                    cliente.TipoCliente = TipoClienteEnum.Web;
                }
                else if (cliente.TipoCliente != TipoClienteEnum.Web)
                {
                    cliente.TipoCliente = TipoClienteEnum.Balcao;
                }
                ClienteRepository.Update(cliente);
            }
        }

        public Conta ObterConta(Int32 IdConta)
        {
            return ContaRepository.Load(IdConta);
        }

        public Conta ObterContaAtivaPorEmail(string email)
        {
            return ContaRepository.ObterContaAtivaPorEmail(email);
        }

        [TransactionInitNotRequired]
        public Conta ObterContaNaoValidadaPorGuidCompleto(string guidcompleto)
        {
            return ContaRepository.ObterContaNaoValidadaPorGuidCompleto(guidcompleto);
        }

        public Conta ObterContaPorEmail(string email)
        {
            return ContaRepository.ObterContaPorEmail(email);
        }

        public Conta ObterContaPorGuid(string guid)
        {
            return ContaRepository.ObterContaPorGuid(guid);
        }

        public void RecriaParametrosDeSessao(string email)
        {
            var conta = ObterContaPorEmail(email);
            PopularSessao(conta);
        }

        public void RecuperarSenha(RecuperacaoDeSenhaDTO dados)
        {
            if (dados == null || String.IsNullOrEmpty(dados.Email))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(Mensagens.OCampoEmailEObrigatorio);
                return;
            }

            string mensagemErro = RecuperarSenha(dados.Email);

            if (!string.IsNullOrWhiteSpace(mensagemErro))
                ValidationHelper.Instance.AdicionarItemValidacao(Mensagens.EmailNaoPossuiContaNoTrinks);
        }

        public string RecuperarSenha(string email)
        {
            var conta = ObterContaPorEmail(email);
            var mensagem = "";

            if (conta == null)
                mensagem = RES.MensagemEmailInexistente;
            else
            {
                if (conta.Ativo)
                    EnvioEmailService.EnviarEmailRecuperacaoSenha(conta);
                else
                    mensagem = RES.UsuarioInativo;
            }

            return mensagem;
        }

        public bool UsuarioPodeEnviarComentarioEmConfirmacaoAgendamentoHotsite(Conta conta)
        {
            var possuiContaAssociadoFacebook = !String.IsNullOrEmpty(conta.CodigoFacebook);
            var ehClienteBalcaoEmAlgumEstabelecimento =
                Domain.Pessoas.HorarioTransacaoRepository.ExisteTransacoesParaOCliente(conta.Pessoa.IdPessoa);
            var possuiCheckoutRealizadoEmAlgumEstabelecimento =
                Domain.Pessoas.HorarioRepository.ExistemHorarioDeOrigemBalcaoParaCliente(conta.Pessoa.IdPessoa);

            return possuiContaAssociadoFacebook || ehClienteBalcaoEmAlgumEstabelecimento ||
                   possuiCheckoutRealizadoEmAlgumEstabelecimento;
        }

        public bool EhUsuarioFranqueador(Conta conta)
        {
            var fraquia = Domain.Pessoas.ContaFranquiaRepository.ObterPrimeiraFranquiaDaConta(conta);

            //refatorar quando a parte de controle de permissões estiver pronto com esse acesso. (esta lógica deveria estar lá)
            var contaPossuiFranquia = fraquia != null;

            return contaPossuiFranquia;
        }

        private static Conta ObterConta(string login, string senha)
        {
            var isValidGuid = Guid.TryParse(senha, out Guid guidOutput);
            if (!isValidGuid)
            {
                var hash = FormsAuthentication.HashPasswordForStoringInConfigFile(senha, "SHA1");
                return Domain.Pessoas.ContaRepository.ObterContaPorLoginSenhaHash(login, hash);
            }
            else
            {
                if (login.SomenteNumeros() == login)
                {
                    var id = int.Parse(login);
                    var conta = Domain.Pessoas.ContaRepository.ObterContaPorGuidAutenticacaoAppPro(id, senha);

                    return conta;
                }
                else
                {
                    var conta = Domain.Pessoas.ContaRepository.ObterContaPorGuid(senha);
                    if (conta.Email.ToLower() == login.ToLower())
                    {
                        return conta;
                    }
                    return null;
                }
            }
        }

        public static void RegistrarAuthTicket(Conta conta, int? idEstabelecimnto = null, IEnumerable<string> permissoes = null)
        {
            var permissoesString = "";

            var listaPermissoesConsolidada = new List<String>();

            ContextHelper.Instance.IdConta = conta.IdConta;
            ContextHelper.Instance.PrecisaConfirmarConta = conta.PrecisaConfirmar();

            var permissoesAreaPerlink = ObterPermissoesParaAreaPerlink(conta).Select(a => a.GetHashCode().ToString()).ToList();

            var permissoesContaDigital = idEstabelecimnto != null ? ObterPermissoesContaDigital(conta, idEstabelecimnto.Value) : null;

            if (permissoes != null)
            {
                listaPermissoesConsolidada.AddRange(permissoes);
            }

            if (permissoesAreaPerlink != null)
            {
                listaPermissoesConsolidada.AddRange(permissoesAreaPerlink);
            }

            if (permissoesContaDigital != null)
            {
                listaPermissoesConsolidada.AddRange(permissoesContaDigital);
            }

            AutenticarAreaPerlink(conta, permissoesAreaPerlink);

            permissoesString = string.Join(",", listaPermissoesConsolidada);

            ContextHelper.Instance.Permissoes = listaPermissoesConsolidada.ToArray();

            var userData = new UserDataDTO { GuidAutenticacao = conta.GuidAutenticacao }; //Limitando quandidade de dados no cookie

            var ticket = new FormsAuthenticationTicket(1, conta.Email, DateTime.Now, DateTime.Now.AddDays(30), true, userData.ToJSON(),
                FormsAuthentication.FormsCookiePath);
            var hash = FormsAuthentication.Encrypt(ticket);
            var cookie = new HttpCookie(FormsAuthentication.FormsCookieName, hash);
            if (ticket.IsPersistent)
                cookie.Expires = ticket.Expiration;
            HttpContext.Current.Response.Cookies.Add(cookie);
        }

        private static IEnumerable<string> ObterPermissoesContaDigital(Conta conta, int idEstabelecimento)
        {
            return Domain.ContaDigital.UsuarioContaDigitalRepository.ObterPermissoesUsuarioPorIdPessoa(conta.Pessoa.IdPessoa, idEstabelecimento);
        }

        private Estabelecimento AlternarUsuarioEstabelecimento(int idEstabelecimento, Conta conta)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var usuarioEstabelecimentoSelecionado =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .FirstOrDefault(
                        f =>
                            f.PessoaFisica.IdPessoa == conta.Pessoa.IdPessoa &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            f.Ativo);

            var estabelecimentosLigadosAoUsuarioSelecionado = Domain.Pessoas.UsuarioEstabelecimentoRepository.ListarPorPessoa(conta.Pessoa.IdPessoa).Where(p => p.Estabelecimento.IdEstabelecimento != estabelecimento.IdEstabelecimento).OrderBy(p => p.DataCadastro).Take(4).Select(p => p.Estabelecimento);
            List<Estabelecimento> listaEstabelecimentos = new List<Estabelecimento>();
            listaEstabelecimentos.Add(estabelecimento);
            listaEstabelecimentos.AddRange(estabelecimentosLigadosAoUsuarioSelecionado);

            if (usuarioEstabelecimentoSelecionado != null)
            {
                ContextHelper.Instance.IdUsuarioEstabelecimento =
                    usuarioEstabelecimentoSelecionado.IdUsuarioEstabelecimento;
                ContextHelper.Instance.IdEstabelecimento = idEstabelecimento;

                ContextHelper.Instance.NomeDeExibicaoNoPortal = estabelecimento.NomeDeExibicaoNoPortal;
                ManterMaisInformacoesNoContextoUsadaEmChatOnline(listaEstabelecimentos, usuarioEstabelecimentoSelecionado);
                RegistrarAuthTicket(conta, idEstabelecimento, Domain.Permissoes.PermissoesAcessoService.ObterPermissoes(usuarioEstabelecimentoSelecionado));
            }
            else if (Domain.Pessoas.ContaFranquiaRepository.EhContaVinculadaAUmaFranquiaDeUmEstabelecimento(
                  conta.IdConta, idEstabelecimento))
            {
                ContextHelper.Instance.IdUsuarioEstabelecimento = null;
                ContextHelper.Instance.IdEstabelecimento = idEstabelecimento;
                RegistrarAuthTicket(conta, idEstabelecimento, Domain.Permissoes.PermissoesAcessoService.ObterPermissoesParaFranqueador(estabelecimento));
            }
            else
            {
                ContextHelper.Instance.IdEstabelecimento = null;
                ContextHelper.Instance.IdUsuarioEstabelecimento = null;

                ContextHelper.Instance.NomeDeExibicaoNoPortal = estabelecimento.NomeDeExibicaoNoPortal;
                ManterMaisInformacoesNoContextoUsadaEmChatOnline(listaEstabelecimentos, null, false);
                RegistrarAuthTicket(conta, idEstabelecimento, new List<string>());
            }

            if (conta.IdConta > 0 && ContextHelper.Instance.IdEstabelecimento > 0)
                ContextHelper.Instance.IdEstabelecimentoPadrao(conta.IdConta, ContextHelper.Instance.IdEstabelecimento);

            var qtdProfissionaisAtivosComAgenda = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Count(p => p.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento && p.Ativo && p.PossuiAgenda);
            HttpCookie cookieQtdProfissionais = new HttpCookie("CookieQtdProfissionais") { Value = qtdProfissionaisAtivosComAgenda.ToString(), Expires = Calendario.Agora().AddDays(20) };
            HttpContext.Current.Response.Cookies.Add(cookieQtdProfissionais);
            return estabelecimento;
        }

        private void ManterMaisInformacoesNoContextoUsadaEmChatOnline(List<Estabelecimento> listaEstabelecimentos, UsuarioEstabelecimento usuarioEstabelecimentoSelecionado, bool apagar = false)
        {
            var metaDadosContatoUsuario = new MetaDadosUsuarioDTO
            {
                OQueSou = "Estabelecimento",
                InformacoesEstabelecimentos = new List<InformacaoEstabelecimento>(),
                Permissao = usuarioEstabelecimentoSelecionado?.UsuarioPerfil?.Perfil?.Nome,
                Estabelecimento = usuarioEstabelecimentoSelecionado?.Estabelecimento?.NomeDeExibicaoNoPortal
            };

            var listaInformacaoEstabelecimento = new List<InformacaoEstabelecimento>();
            foreach (var estabelecimento in listaEstabelecimentos)
            {
                if (estabelecimento == null)
                    return;
                if (estabelecimento.Hotsite() == null)
                    return;

                if (apagar)
                {
                    ContextHelper.Instance.NomeDeExibicaoNoPortal = null;
                    ContextHelper.Instance.UrlDoHotsite = null;
                    ContextHelper.Instance.ApareceNoPortal = false;
                    return;
                }

                var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable().FirstOrDefault(f => f.Ativo && f.Pessoa == estabelecimento.PessoaJuridica);
                if (contaFinanceira == null)
                    contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable().FirstOrDefault(f => f.Pessoa == estabelecimento.PessoaJuridica);

                var quantidadeProfissionaisAtivosComAgenda =
                    Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(p => p.Estabelecimento == estabelecimento && p.Ativo && p.PossuiAgenda).Count();

                var informacoesEstabelecimento = new InformacaoEstabelecimento
                {
                    StatusConta = contaFinanceira.Status.Nome,
                    QtdProfissionais = quantidadeProfissionaisAtivosComAgenda,
                    NomePortal = estabelecimento.NomeDeExibicaoNoPortal,
                    UrlHotsite = estabelecimento.Hotsite().Url,
                    AparecePortal = estabelecimento.Hotsite().DesejaAparecerBuscaPortal && estabelecimento.Hotsite().DesejaTerHotsite ? "Sim" : "Não"
                };

                listaInformacaoEstabelecimento.Add(informacoesEstabelecimento);
            }

            metaDadosContatoUsuario.InformacoesEstabelecimentos = listaInformacaoEstabelecimento;
            ContextHelper.Instance.MetaDadosContatoUsuario = JsonConvert.SerializeObject(metaDadosContatoUsuario);
        }

        public Conta ObterContaAutenticada()
        {
            var idConta = ContextHelper.Instance.IdConta ?? 0;
            var conta = idConta > 0 ? Domain.Pessoas.ContaRepository.Load(idConta, false) : null;
            return conta != null && conta.Ativo ? conta : null;
        }

        private void PopularSessao(Conta conta)
        {
            if (HttpContext.Current == null || conta == null)
                return;

            ConfirmarConta(conta);

            var metaDadosContatoUsuario = new MetaDadosUsuarioDTO
            {
                OQueSou = "Cliente WEB",
                InformacoesEstabelecimentos = new List<InformacaoEstabelecimento>()
            };

            ContextHelper.Instance.MetaDadosContatoUsuario = JsonConvert.SerializeObject(metaDadosContatoUsuario);

            AutenticarAreaAdministrador(conta);

            var idEstabelecimentoPadrao = ContextHelper.Instance.IdEstabelecimentoPadrao(conta.IdConta);
            var usuarioEstaAtivoNoEstabelecimentoPadrao = Domain.Pessoas.UsuarioEstabelecimentoRepository.VerificarSeUsuarioEstaAtivoNoEstabelecimento(conta.Pessoa.IdPessoa, idEstabelecimentoPadrao ?? 0);

            Estabelecimento estabelecimentoPadrao;
            if (idEstabelecimentoPadrao.HasValue && usuarioEstaAtivoNoEstabelecimentoPadrao)
                estabelecimentoPadrao = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoPadrao.Value);
            else
                estabelecimentoPadrao = Domain.Pessoas.EstabelecimentoRepository.ObterEstabelecimentoPrincipalDaConta(conta);

            if (estabelecimentoPadrao != null)
            {
                if (estabelecimentoPadrao.FranquiaEstabelecimento != null)
                {
                    ContextHelper.Instance.NomeDoConsolidado = "Consolidado de " + estabelecimentoPadrao.FranquiaEstabelecimento.Franquia.Nome;
                }

                AlternarUsuarioEstabelecimento(estabelecimentoPadrao.IdEstabelecimento, conta);
            }
            else
                RegistrarAuthTicket(conta);
        }

        private static void AutenticarAreaPerlink(Conta conta, IList<String> permissoes)
        {
            if (permissoes.Any())
            {
                ContextHelper.Instance.ContaPerlink = new ContaPerlink
                {
                    Email = conta.Email,
                    Nome = conta.Pessoa.PessoaFisica.NomeCompleto,
                    TipoDeAcesso = TipoDeAcessoPerlink.ComAcesso
                };
            }
            else
            {
                ContextHelper.Instance.ContaPerlink = null;
            }
        }

        private void AutenticarAreaAdministrador(Conta conta)
        {
            var contaFranquias = Domain.Pessoas.ContaFranquiaRepository.ListarPorConta(conta.IdConta);
            var franquiaPrincipal = contaFranquias.FirstOrDefault();
            if (franquiaPrincipal != null)
                ContextHelper.Instance.IdFranquia = franquiaPrincipal.Franquia.Id;
            else
                ContextHelper.Instance.IdFranquia = null;

            var usuarioEstabelecimento =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.ListarPorPessoa(conta.Pessoa.IdPessoa).Where(p => p.UsuarioPerfil.Perfil == UsuarioPerfilEnum.Administrador);
            ContextHelper.Instance.TemAcessoAreaAdministrador = ContextHelper.Instance.IdFranquia.HasValue ||
                                                                usuarioEstabelecimento.Count() > 1;
        }

        private void PopularSessaoEGravarDataDoUltimoLogin(Conta conta)
        {
            // Refactor, extract method (http://jira.perlink.net/browse/TRINKS-3280)
            PopularSessao(conta);
            AtualizarDataDeUltimoLogin(conta);
        }

        public List<PessoaFisica> ObterContasPorId(List<int> idContas)
        {
            var pessoas = new List<PessoaFisica>();
            foreach (var id in idContas)
            {
                pessoas.Add(Domain.Pessoas.ContaRepository.ObterPorId(id).Pessoa.PessoaFisica);
            }
            return pessoas;
        }

        #endregion Métodos Públicos

        #region Métodos Privados

        private static IEnumerable<Permissoes.AreaPerlink.PermissaoEnum> ObterPermissoesParaAreaPerlink(Conta conta)
        {
            var contaPerfil = Domain.Permissoes.AreaPerlink.ContaPerfilRepository.Load(conta.IdConta, false);

            var retorno = new List<Permissoes.AreaPerlink.PermissaoEnum>();

            if (contaPerfil != null)
            {
                retorno = contaPerfil.Perfil.Permissoes.Where(
                    f => f.TipoAcesso == Permissoes.AreaPerlink.TipoAcessoEnum.Permitido).Select(f => f.Permissao).ToList();
            }

            var permissoes = Domain.Permissoes.AreaPerlink.ContaPerfilPermissaoRepository.ObterPorConta(conta.IdConta);

            if (permissoes != null)
            {
                retorno.AddRange(permissoes.Where(
                    f => f.TipoAcesso == Permissoes.AreaPerlink.TipoAcessoEnum.Permitido).Select(f => f.Permissao).ToList());
            }

            return retorno;
        }

        private void AtualizarDataDeUltimoLogin(Conta conta)
        {
            conta.DataUltimoLogin = Calendario.Agora();
        }

        #endregion Métodos Privados

        public void ValidarSenhaDaConta(int idConta, string senha)
        {
            string hash = senha.ToSHA1();

            bool contaExiste = ContaRepository.Existe(idConta, hash);
            if (!contaExiste)
                ValidationHelper.Instance.AdicionarItemValidacao("Senha atual está incorreta.");
        }

        public bool EmailEstaAssociadoAUmaContaAtiva(string email, int? idPessoa = null)
        {
            int? idPessoaAssociadaAoEmail = Domain.Pessoas.ContaRepository.ObterIdPessoaDaContaAtivaPeloEmail(email);

            if (idPessoa != null)
                return !idPessoaAssociadaAoEmail.HasValue || idPessoaAssociadaAoEmail > 0 && idPessoaAssociadaAoEmail == idPessoa;
            else
                return !idPessoaAssociadaAoEmail.HasValue;
        }

        public bool EhContaDeFranqueadorComAcessoAosEstabelecimentosDaFranquia(Conta conta, Estabelecimento estabelecimento)
        {
            bool franqueadorComAcessoAoEstabelecimento = false;

            if (estabelecimento.EhUmEstabelecimentoFranqueadoAtivo())
            {
                franqueadorComAcessoAoEstabelecimento = Domain.Pessoas.ContaFranquiaRepository
                    .ExisteContaAssociadaAFranquiaComAcessoAdministradorNoBackOffice(conta.IdConta, estabelecimento.FranquiaEstabelecimento.Franquia.Id);
            }

            return franqueadorComAcessoAoEstabelecimento;
        }

        public void DefinirPermissoesDaContaAutenticadaComoDesatualizadas()
        {
            Domain.WebContext.DadosFixos("PermissoesAtualizadas", null);
        }

        public PessoaFisica ObterPessoaFisicaDaContaAutenticada()
        {
            bool modoPAT = ContextHelper.Instance.ModoPAT ?? false;

            if (modoPAT)
            {
                var idEstabelecimentoProfissionalPAT = ContextHelper.Instance.IdEstabelecimentoProfissionalPAT ?? 0;
                var profissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();

                return (from ep in profissionais
                        where ep.Codigo == idEstabelecimentoProfissionalPAT
                        select ep.Profissional.PessoaFisica).FirstOrDefault();
            }
            else
            {
                var idContaAutenticada = ContextHelper.Instance.IdConta ?? 0;
                var pessoasFisicas = Domain.Pessoas.PessoaFisicaRepository.Queryable();
                var contas = Domain.Pessoas.ContaRepository.Queryable();

                return (from c in contas
                        join pf in pessoasFisicas on c.Pessoa.IdPessoa equals pf.IdPessoa
                        where c.IdConta == idContaAutenticada
                        select pf).FirstOrDefault();
            }
        }

        public string GerarGuidAutenticacaoAppPro(Conta conta)
        {
            conta.GuidAutenticacaoAppPro = Guid.NewGuid().ToString();
            ContaRepository.Update(conta);
            return conta.GuidAutenticacaoAppPro;
        }

        public Conta ObterContaPorGuidAutenticacaoAppPro(int idConta, string guid)
        {
            return ContaRepository.ObterContaPorGuidAutenticacaoAppPro(idConta, guid);
        }

        public void DeletarGuidAutenticacaoAppPro(Conta conta)
        {
            conta.GuidAutenticacaoAppPro = null;
            ContaRepository.Update(conta);
        }

        public void DeslogarTodosOsUsuariosDoEstabelecimento(int idEstabelecimento)
        {
            var contasParaDeslogar = Domain.Pessoas.UsuarioEstabelecimentoRepository.ListarContasDeUsuariosAtivosDoEstabelecimento(idEstabelecimento);

            foreach (var conta in contasParaDeslogar)
            {
                conta.Deslogar();
                Domain.Pessoas.ContaRepository.Update(conta);
            }
        }
        public List<GeracaoVerificacaoDTO> ObterVerificacoesUnificacaoProfissional(Conta contaUnificar, int codigoUnificacao)
        {
            var verificacoesDaConta = new List<GeracaoVerificacaoDTO>();

            var solicitacaoEmailVerificado = new SolicitacaoIdentidadeVerificadaDTO()
            {
                IdConta = contaUnificar.IdConta,
                Contato = contaUnificar.Email,
                Tipo = TipoDeContatoEnum.Email,
                PodePular = false,
                PodeTrocarContato = false,
                MotivoSolicitacao = MotivoSolicitacaoEnum.ValidarUnificacaoProfissional,
                IdObjetoQueOriginouVerificacao = codigoUnificacao,
                TipoObjetoQueOriginouVerificacao = TipoObjetoQueOriginouEnum.IdCodigoUnificacao
            };

            var verificacaoDoEmail = Domain.ValidacaoDeIdentidade.SolicitacaoIdentidadeVerificadaService.ObterSolicitacaoUnificacaoExistenteOuGerarNova(solicitacaoEmailVerificado);
            verificacoesDaConta.Add(verificacaoDoEmail);

            return verificacoesDaConta;
        }
        public List<GeracaoVerificacaoDTO> ObterVerificacoesDaConta(Conta conta)
        {
            var verificacoesDaConta = new List<GeracaoVerificacaoDTO>();

            var solicitacaoEmailVerificado = new SolicitacaoIdentidadeVerificadaDTO()
            {
                IdConta = conta.IdConta,
                Contato = conta.Email,
                Tipo = TipoDeContatoEnum.Email,
                PodePular = false,
                PodeTrocarContato = false,
                MotivoSolicitacao = MotivoSolicitacaoEnum.ValidarCadastroDeConta,
                IdObjetoQueOriginouVerificacao = conta.IdConta,
                TipoObjetoQueOriginouVerificacao = TipoObjetoQueOriginouEnum.IdConta
            };

            var verificacaoDoEmail = Domain.ValidacaoDeIdentidade.SolicitacaoIdentidadeVerificadaService.ObterSolicitacaoExistenteOuGerarNova(solicitacaoEmailVerificado);
            verificacoesDaConta.Add(verificacaoDoEmail);


            var telefone = Domain.Pessoas.TelefoneRepository.ListarTelefonesPropriosDaPessoa(conta.Pessoa.IdPessoa)
                            .Where(t => t.IdentificadoComoCelular())
                            .FirstOrDefault();

            if (telefone != null)
            {

                var solicitacaoCelularVerificado = new SolicitacaoIdentidadeVerificadaDTO()
                {
                    IdConta = conta.IdConta,
                    Contato = telefone.ToString().SomenteNumeros(),
                    Tipo = TipoDeContatoEnum.Celular,
                    PodePular = true,
                    PodeTrocarContato = false,
                    MotivoSolicitacao = MotivoSolicitacaoEnum.ValidarCadastroDeConta,
                    IdObjetoQueOriginouVerificacao = telefone.IdTelefone,
                    TipoObjetoQueOriginouVerificacao = TipoObjetoQueOriginouEnum.IdTelefone
                };

                var verificacaoTelefone = Domain.ValidacaoDeIdentidade.SolicitacaoIdentidadeVerificadaService.ObterSolicitacaoExistenteOuGerarNova(solicitacaoCelularVerificado);
                verificacoesDaConta.Add(verificacaoTelefone);
            }

            return verificacoesDaConta;
        }

        public Telefone ObterTelefoneAtravesDaMascara(string email, string telefoneSelecionado)
        {
            var conta = Domain.Pessoas.ContaRepository.ObterContaAtivaPorEmail(email);

            var telefones = Domain.Pessoas.TelefoneRepository.ListarTelefonesPropriosDaPessoa(conta.Pessoa.IdPessoa);

            List<Telefone> telefonesCelulares = new List<Telefone>();

            foreach (Telefone telefone in telefones)
            {
                if (telefone.EhCelular(telefone.Ddi))
                {
                    telefonesCelulares.Add(telefone);
                }
            }

            var celular = telefonesCelulares
                            .Where(p => p.Numero.ToString().SomenteNumeros().Substring(5) == telefoneSelecionado)
                            .FirstOrDefault();
            return celular;
        }

        public void TrocarEmail(Conta conta, string email)
        {

            if (!email.EmailValido())
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Formato de e-mail inválido");
                return;
            }

            conta.Pessoa.Email = email;
            conta.Email = email;
            Domain.Pessoas.ContaRepository.Update(conta);
        }
    }
}