﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Services
{
    public interface ITelefoneService : IService
    {
        void AssociarOperadorasATelefonesSemOperadora();
        void AtualizarOperadorasDeTelefones();
        void AtualizarPrincipalTelefoneDeContatoDoEstabelecimentoLogado(string ddd, string numero, bool ehWhatsApp);
        void ManterTelefonesNovosAtualizadosERemovidos(List<TelefoneDTO> listaAtualizada, PessoaFisica pessoaFisica, string versaoApp = "");
        IEnumerable<Telefone> ObterTelefonesAtivosDoEstabelecimentoLogado(int idEstabelecimento);
        IEnumerable<Telefone> ObterTelefonesComWhatAppAtivosDoEstabelecimentoLogado(int idEstabelecimento);
        Telefone ObterTelefoneAtivoDoEstabelecimentoLogado();
        Telefone ObterTelefoneParaComunicacaoWhatsApp(IList<Telefone> telefones, int idPessoaJuridicaEstabelecimento);
        List<TelefoneDTO> ValidarExibicaoTelefonesInternacionaisAppB2c(List<TelefoneDTO> telefones, string versaoDoApp);
    }
}
