﻿using Perlink.DomainInfrastructure.Regra;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.IO;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Services
{

    public class FotoDeClienteEstabelecimentoService : BaseService, IFotoDeClienteEstabelecimentoService
    {

        public FotoDeClienteEstabelecimento AdicionarFoto(int idClienteEstabelecimento, Stream arquivo, string extensao)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            return AdicionarFoto(clienteEstabelecimento, arquivo, extensao);
        }

        [TransactionInitRequired]
        public FotoDeClienteEstabelecimento AdicionarFoto(ClienteEstabelecimento clienteEstabelecimento, Stream arquivo, string extensao)
        {
            VerificarSeEhPossivelAdicionarFoto(clienteEstabelecimento, arquivo, extensao);

            var foto = Domain.Fotos.FotoService.AdicionarFotoClienteEstabelecimento(arquivo, extensao, clienteEstabelecimento.Estabelecimento.IdEstabelecimento, clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa);

            var fotoDoClienteEstabelecimento = new FotoDeClienteEstabelecimento
            {
                ClienteEstabelecimento = clienteEstabelecimento,
                Foto = foto
            };
            Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.SaveNew(fotoDoClienteEstabelecimento);
            return fotoDoClienteEstabelecimento;
        }

        [TransactionInitRequired]
        public void Manter(FotoDeClienteEstabelecimento fotoClienteEstabelecimento)
        {
            VerificarSeEhPossivelManterFoto(fotoClienteEstabelecimento);

            Domain.Fotos.FotoService.Manter(fotoClienteEstabelecimento.Foto);

            Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Update(fotoClienteEstabelecimento);
        }

        private void VerificarSeEhPossivelManterFoto(FotoDeClienteEstabelecimento fotoClienteEstabelecimento)
        {
            var motorDeRegras = new MotorRegra();

            Domain.Pessoas.PoliticaDeControleDeFotosDoClienteEstabelecimento.VerificarSeEhPossivelManterFotoDoClienteEstabelecimento(fotoClienteEstabelecimento, motorDeRegras);

            motorDeRegras.Executa();
        }

        private void VerificarSeEhPossivelAdicionarFoto(ClienteEstabelecimento clienteEstabelecimento, Stream arquivo, string extencao)
        {
            var motorDeRegras = new MotorRegra();

            Domain.Pessoas.PoliticaDeControleDeFotosDoClienteEstabelecimento.VerificarSeEhPossivelAdicionarFotoDoClienteEstabelecimento(clienteEstabelecimento, arquivo, extencao, motorDeRegras);

            motorDeRegras.Executa();
        }

        private void VerificarSeEhPossivelRemoverFoto(FotoDeClienteEstabelecimento fotoClienteEstabelecimento)
        {
            var motorDeRegras = new MotorRegra();

            Domain.Pessoas.PoliticaDeControleDeFotosDoClienteEstabelecimento.VerificarSeEhPossivelRemoverFotoDoClienteEstabelecimento(fotoClienteEstabelecimento, motorDeRegras);

            motorDeRegras.Executa();
        }

        [TransactionInitRequired]
        public void RemoverFoto(FotoDeClienteEstabelecimento fotoDeClienteEstabelecimento)
        {
            VerificarSeEhPossivelRemoverFoto(fotoDeClienteEstabelecimento);

            if (fotoDeClienteEstabelecimento.ClienteEstabelecimento.FotoDoPerfil != null
                && fotoDeClienteEstabelecimento.ClienteEstabelecimento.FotoDoPerfil.Foto.Id == fotoDeClienteEstabelecimento.Foto.Id)
            {
                fotoDeClienteEstabelecimento.ClienteEstabelecimento.FotoDoPerfil = null;
                Domain.Pessoas.ClienteEstabelecimentoRepository.Update(fotoDeClienteEstabelecimento.ClienteEstabelecimento);
            }

            Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Delete(fotoDeClienteEstabelecimento);
            Domain.Fotos.FotoRepository.Delete(fotoDeClienteEstabelecimento.Foto);
        }

        public void RemoverFoto(int idFotoClienteEstabelecimento)
        {
            RemoverFoto(Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Load(idFotoClienteEstabelecimento));
        }

        public void AlterarLegendaDaFoto(int idFotoClienteEstabelecimento, string legenda)
        {
            var fotoClienteEstabelecimento = Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Load(idFotoClienteEstabelecimento);

            fotoClienteEstabelecimento.Foto.Legenda = legenda;
            Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Update(fotoClienteEstabelecimento);
        }

        public void MarcarFotoComoPrincipal(int idFotoClienteEstabelecimento)
        {
            var fotoClienteEstabelecimento = Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Load(idFotoClienteEstabelecimento);
            var clienteEstabelecimento = fotoClienteEstabelecimento.ClienteEstabelecimento;
            clienteEstabelecimento.FotoDoPerfil = fotoClienteEstabelecimento;

            Domain.Pessoas.ClienteEstabelecimentoRepository.Update(clienteEstabelecimento);
        }

        public String ObterUrlFotoDeExibicaoDoClienteEstabelecimento(int idClienteEstabelecimento, Fotos.Enums.DimemsoesFotosEnum dimensoesFoto = Fotos.Enums.DimemsoesFotosEnum.Dim50x50, bool carregarFotoPadrao = true)
        {
            if (idClienteEstabelecimento <= 0)
                return new FotoPessoa().ObterCaminhoWebFotoPadrao(dimensoesFoto);

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            var idFotoPerfil = clienteEstabelecimento.FotoDoPerfil?.Id;
            if (idFotoPerfil.HasValue)
            {
                var fotoReal = Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.Obter(idFotoPerfil.Value);
                if (fotoReal != null)
                    return Domain.Conteudo.ConteudoFotoClienteEstabelecimentoService.ObterCaminhoWeb(fotoReal, dimensoesFoto);
            }

            var pessoaFisica = clienteEstabelecimento.Cliente.PessoaFisica;

            if (Domain.Pessoas.FotoPessoaRepository.PessoaPossuiFoto(pessoaFisica) && clienteEstabelecimento.Cliente.TipoCliente == TipoClienteEnum.Web)
                return pessoaFisica.FotoPrincipal.ObterCaminhoWebFotoDePerfil(dimensoesFoto);

            var fotoMaisRecente = Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.ObterFotoMaisRecenteDoCliente(idClienteEstabelecimento);

            if (fotoMaisRecente != null)
                return Domain.Conteudo.ConteudoFotoClienteEstabelecimentoService.ObterCaminhoWeb(fotoMaisRecente, dimensoesFoto);

            if (carregarFotoPadrao)
                return pessoaFisica.FotoPrincipal.ObterCaminhoWebFotoPadrao(dimensoesFoto);

            return string.Empty;
        }
    }
}