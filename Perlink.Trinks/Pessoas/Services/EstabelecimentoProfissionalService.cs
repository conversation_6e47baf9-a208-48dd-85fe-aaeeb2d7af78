﻿using Elmah;
using Newtonsoft.Json;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Notificacao;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Fotos.Enums;
using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Repositories;
using Perlink.Trinks.Resources;
using Perlink.Trinks.Resources.Areas.BackOffice.Controllers;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;

namespace Perlink.Trinks.Pessoas.Services
{

    public class EstabelecimentoProfissionalService : BaseService, IEstabelecimentoProfissionalService
    {

        #region Propriedades de Apoio

        private IEstabelecimentoProfissionalRepository EstabelecimentoProfissionalRepository
        {
            get { return Domain.Pessoas.EstabelecimentoProfissionalRepository; }
        }

        private IEstabelecimentoRepository EstabelecimentoRepository
        {
            get { return Domain.Pessoas.EstabelecimentoRepository; }
        }

        #endregion Propriedades de Apoio

        #region Métodos Públicos

        public void AlterarCodigoPAT(int idEstabelecimentoProfissional, string novoCodigoPAT)
        {
            var profissionalEstabelcimento =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional);

            if (novoCodigoPAT == profissionalEstabelcimento.Profissional.PessoaFisica.Cpf.Substring(0, 4))
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "O novo código não pode ser os 4 primeiros números do seu CPF.");

            profissionalEstabelcimento.CodigoDeAcessoAoPainelAtendimento =
                FormsAuthentication.HashPasswordForStoringInConfigFile(novoCodigoPAT, "SHA1");
        }

        public EstabelecimentoProfissional AssociarPFaoEstabelecimentoComoProfissional(PessoaFisica pessoa, Estabelecimento estabelecimento)
        {
            //Domain.Pessoas.PessoaService.CopiarTelefonesParoOEstabelecimento(pessoa, estabelecimento);

            var profissional = Domain.Pessoas.ProfissionalRepository.ObterPorPessoaFisica(pessoa.IdPessoa)
                ?? new Profissional { PessoaFisica = pessoa };

            if (profissional.IdProfissional == 0)
                Domain.Pessoas.ProfissionalRepository.SaveNew(profissional);

            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(profissional.IdProfissional, estabelecimento.IdEstabelecimento)
                ?? new EstabelecimentoProfissional(estabelecimento, profissional) { Genero = pessoa.Genero };

            if (estabelecimentoProfissional.Codigo == 0)
            {
                var horarios = estabelecimento.HorariosDeFuncionamento.Select(f => new HorarioTrabalho
                {
                    DiaSemana = f.DiaSemana,
                    EstabelecimentoProfissional = estabelecimentoProfissional,
                    HoraEntrada = f.HoraAbertura,
                    HoraSaida = f.HoraFechamento,
                    Ativo = f.Ativo
                });

                foreach (var h in horarios)
                {
                    estabelecimentoProfissional.HorarioTrabalhoLista.Add(h);
                }

                if (String.IsNullOrEmpty(profissional.PessoaFisica.Apelido))
                    profissional.PessoaFisica.Apelido = profissional.PessoaFisica.NomeCompleto.ObterPrimeiroNome();

                Domain.Pessoas.EstabelecimentoProfissionalRepository.SaveNew(estabelecimentoProfissional);
            }
            return estabelecimentoProfissional;
        }

        [TransactionInitRequired]
        public EstabelecimentoProfissional CadastrarNovo(Estabelecimento estabelecimento, Profissional profissional)
        {
            var entity = new EstabelecimentoProfissional
            {
                Estabelecimento = estabelecimento,
                Profissional = profissional,
                Genero = profissional.PessoaFisica.Genero,
                DataNascimento = profissional.PessoaFisica.DataNascimento,
                ComissaoPadrao = 0,
                TipoComissao = new TipoComissao(2),
                Ativo = true,
                CodigoInterno = ObterProximoCodigoInterno(estabelecimento.IdEstabelecimento)
            };
            EstabelecimentoProfissionalRepository.SaveOrUpdate(entity);
            return entity;
        }

        public bool EstabelecimentoProfissionalPodeTerAssistente(int? idEstabelecimentoProfissional,
            int? idServicoEstabelecimento)
        {
            if (idEstabelecimentoProfissional == null || idServicoEstabelecimento == null)
                return false;

            return Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable()
                .Any(f =>
                    f.EstabelecimentoProfissional.Codigo == idEstabelecimentoProfissional
                    && f.Ativo
                    && f.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento
                    && f.ServicoExecutadoComAssistente);
        }

        [TransactionInitRequired]
        public void Inativar(int idEstabelecimentoProfissional)
        {
            var ep = Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional);
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional);

            #region Load

            var entity = EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional);
            var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta ?? 0);
            var estabelecimentoAutenticado = Domain.Pessoas.EstabelecimentoRepository.Load(ContextHelper.Instance.IdEstabelecimento ?? 0);

            #endregion Load

            #region Validação

            if (entity.Estabelecimento.IdEstabelecimento != estabelecimentoAutenticado.IdEstabelecimento)
                throw new Exception("Operação não permitida");

            if (entity.Profissional.PessoaFisica.IdPessoa == contaAutenticada.Pessoa.IdPessoa)
                ValidationHelper.Instance.AdicionarItemValidacao(ProfissionalController.Erro_Exclusao_Proprio_Profissional);
            else if (!ExisteOutrosUsuariosAdministrativos(entity.Profissional.PessoaFisica.IdPessoaFisica, estabelecimentoAutenticado))
                ValidationHelper.Instance.AdicionarItemValidacao(ProfissionalController.Erro_Exclusao_Unico_Profissional_Adm);
            else if (entity.Profissional.PessoaFisica.IdPessoaFisica ==
                entity.Estabelecimento.PessoaJuridica.ResponsavelFinanceiro.IdPessoa)
                ValidationHelper.Instance.AdicionarItemValidacao(ProfissionalController.Erro_Exclusao_Quem_Cadastrou);
            else
            {
                var profissionaisDoEstabelecimento = EstabelecimentoProfissionalRepository.ListarPorEstabelecimento(estabelecimentoAutenticado.IdEstabelecimento);
                var franquiaEstabelecimento = estabelecimentoAutenticado.FranquiaEstabelecimento;

                if (!profissionaisDoEstabelecimento.Any(f => f.Codigo != entity.Codigo && f.PossuiAgenda))
                {
                    if ((franquiaEstabelecimento != null && franquiaEstabelecimento.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo) || franquiaEstabelecimento == null)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao(Mensagens.NaoEhPossivelExcluirPoisEhNecessarioUmProfissionalComAcessoAAgenda);
                    }
                }
            }

            if (!ValidationHelper.Instance.IsValid)
                return;

            #endregion Validação

            ep.Ativo = false;

            var primeiraConta = ep.Profissional.PessoaFisica.PrimeiraConta;
            if (primeiraConta != null)
                primeiraConta.GerarNovoGuidDeAutenticacao();

            if (ep.Endereco != null)
                ep.Endereco.Ativo = false;

            ep.EstabelecimentoProfissionalServicoLista.Inativar();
            ep.EstabelecimentoAssistenteServicoLista.Inativar();
            ep.PodeAcessarMinhaAgenda = false;

            var usuarioEstabelecimento = ep.UsuarioEstabelecimento;
            if (usuarioEstabelecimento != null)
                usuarioEstabelecimento.Ativo = false;

            ep.HorarioTrabalhoLista.Inativar();

            Domain.Pessoas.EstabelecimentoProfissionalRepository.Update(ep);

            InativarProfissionalOperadorContaDigital(estabelecimentoProfissional);
            Domain.Pessoas.ProfissionalService.EnviarNotificacaoDeEventoParaIntegracaoComOutrosSistemas(estabelecimentoProfissional, IntegracaoComOutrosSistemas.Enums.TipoDeEventoEnum.ExclusaoDeProfissional);
            Domain.Pessoas.ProfissionalService.ManterInscricoesEmNotificacoes(ep, false, false, false);
            RealizarPushParaSNS(estabelecimentoProfissional);
        }

        private void InativarProfissionalOperadorContaDigital(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var idEstabelecimento = estabelecimentoProfissional.Estabelecimento.IdEstabelecimento;
            var idPessoa = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa;
            Domain.ContaDigital.OperadorPermissaoService.InativarOperadorPorIdEstabelecimentoEIdPessoa(idEstabelecimento, idPessoa);
        }

        public IntervaloDataList ObterHorarioDeTrabalho(DateTime dia, HorarioTrabalho horario)
        {
            var intervaloDataList = new IntervaloDataList();

            if (horario != null)
            {
                if (horario.HoraEntrada.HasValue && horario.HoraSaida.HasValue)
                {
                    DateTime entrada = dia.Date
                        .AddHours(horario.HoraEntrada.Value.Hours)
                        .AddMinutes(horario.HoraEntrada.Value.Minutes);

                    DateTime saida = dia.Date
                        .AddHours(horario.HoraSaida.Value.Hours)
                        .AddMinutes(horario.HoraSaida.Value.Minutes);

                    var intervaloTrabalho = new IntervaloData(entrada, saida);
                    intervaloDataList.Add(intervaloTrabalho);

                    if (horario.InicioIntervalo.HasValue && horario.FimIntervalo.HasValue)
                    {
                        DateTime inicio = dia.Date
                            .AddHours(horario.InicioIntervalo.Value.Hours)
                            .AddMinutes(horario.InicioIntervalo.Value.Minutes);

                        DateTime fim = dia.Date
                            .AddHours(horario.FimIntervalo.Value.Hours)
                            .AddMinutes(horario.FimIntervalo.Value.Minutes);

                        var intervaloDescanso = new IntervaloData(inicio, fim);
                        intervaloDataList.Remove(intervaloDescanso);
                    }

                    if (horario.InicioAlmoco.HasValue && horario.FimAlmoco.HasValue)
                    {
                        DateTime inicio = dia.Date
                            .AddHours(horario.InicioAlmoco.Value.Hours)
                            .AddMinutes(horario.InicioAlmoco.Value.Minutes);

                        DateTime fim = dia.Date
                            .AddHours(horario.FimAlmoco.Value.Hours)
                            .AddMinutes(horario.FimAlmoco.Value.Minutes);

                        var intervaloAlmoco = new IntervaloData(inicio, fim);
                        intervaloDataList.Remove(intervaloAlmoco);
                    }
                }
            }

            return intervaloDataList;
        }

        public IntervaloDataList ObterHorarioDeTrabalhoConsiderandoHorarioEspecial(Estabelecimento estabelecimento, DateTime dia, HorarioTrabalho horarioTrabalho)
        {
            var horariosEspeciaisEstabelecimento = Domain.Pessoas.EstabelecimentoHorarioEspecialFuncionamentoRepository.ObterHorariosEspeciaisContidosNoDia(estabelecimento, dia);

            if (horariosEspeciaisEstabelecimento.Any())
                return estabelecimento.ObterHorarioDeFuncionamentoReal(dia);
            var intervaloDataList = ObterHorarioDeTrabalho(dia, horarioTrabalho);

            return intervaloDataList;
        }

        public string ObterProximoCodigoInterno(int idEstabelecimento)
        {
            var query =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                    .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            int ultimoValor;

            int.TryParse(query.Max(f => f.CodigoInterno), out ultimoValor);

            return (ultimoValor + 1).ToString("D4");
        }

        public bool ProfissionalPodeTerAssistente(int? idProfissional, int? idServicoEstabelecimento)
        {
            if (idProfissional == null || idServicoEstabelecimento == null)
                return false;

            return Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable()
                .Any(f =>
                    f.EstabelecimentoProfissional.Profissional.IdProfissional == idProfissional
                    && f.Ativo
                    && f.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento
                    && f.ServicoExecutadoComAssistente);
        }

        public void Reativar(int idEstabelecimentoProfissional)
        {
            var ep = Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(idEstabelecimentoProfissional);

            ep.Ativo = true;

            if (ep.Profissional.PessoaFisica.PrimeiraConta != null)
            {
                ep.Profissional.PessoaFisica.PrimeiraConta.GerarNovoGuidDeAutenticacao();
            }

            if (ep.Endereco != null)
                ep.Endereco.Ativo = true;

            if (ep.UsuarioEstabelecimento != null)
            {
                var perfilAcesso = ep.UsuarioEstabelecimento.PerfilAcesso;

                if (perfilAcesso != AcessoBackoffice.Nao_Possui)
                {
                    ep.UsuarioEstabelecimento.Ativo = true;
                    ep.PodeAcessarMinhaAgenda = true;
                }
            }

            ep.HorarioTrabalhoLista.Reativar();
            ep.ConfigurarComissoes();

            Domain.Pessoas.EstabelecimentoProfissionalRepository.Update(ep);
        }

        public void ValidarCodigoInterno(int idEstabelecimentoProfissional, string codigoInterno, int idEstabelecimento)
        {
            var comOMesmoCodigoInterno =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorCodigoInterno(codigoInterno,
                    idEstabelecimento);
            if (comOMesmoCodigoInterno != null && comOMesmoCodigoInterno.Codigo != idEstabelecimentoProfissional)
                ValidationHelper.Instance.AdicionarItemValidacao("Código " + codigoInterno +
                                                                 " já associado ao profissional " +
                                                                 comOMesmoCodigoInterno.Profissional.PessoaFisica
                                                                     .NomeOuApelido());
        }

        private bool ExisteOutrosUsuariosAdministrativos(Int32 idPessoaFisica, Estabelecimento estabelecimento)
        {
            return
                estabelecimento.VinculoUsuarios.Count(
                    p => p.EhResponsavel && p.PessoaFisica.IdPessoaFisica != idPessoaFisica && p.Ativo) > 0;
        }

        //public IntervaloDataList ObterHorarioDeTrabalhoConsiderandoHorarioEspecial(List<int> idsEstabelecimentoProfissionals, Estabelecimento estabelecimento, DateTime dia) {
        //    var horariosEspeciaisEstabelecimento = Domain.Pessoas.EstabelecimentoHorarioEspecialFuncionamentoRepository.ObterHorariosEspeciaisContidosNoDia(estabelecimento, dia);

        //    if (horariosEspeciaisEstabelecimento.Any())
        //        return estabelecimento.ObterHorarioDeFuncionamentoReal(dia);
        //    var intervaloDataList = ObterHorarioDeTrabalho(estabelecimentoProfissional, dia);

        //    if (false) { // Desabilitado por problemas de performance
        //        if (ConfiguracaoHotsiteUniversoEnum.Intersecao_Horario_Profissional_Estabelecimento.GetHashCode()
        //            .Equals(estabelecimento.Hotsite().ConfiguracaoHotsiteUniverso.Codigo)) {
        //            var horarioFuncionamentoEstabelecimento = estabelecimento.ObterHorarioDeFuncionamentoReal(dia);
        //            var horarioNaoFuncionamentoEstabelecimento = new IntervaloDataList(dia);

        //            horarioNaoFuncionamentoEstabelecimento.Remove(horarioFuncionamentoEstabelecimento);

        //            intervaloDataList.Remove(horarioNaoFuncionamentoEstabelecimento);
        //        }
        //    }

        //    return intervaloDataList;
        //}

        public void ConfigurarComissaoDosProfissionaisNoEstabelecimento(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral)
        {
            var estabelecimentoProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarPorEstabelecimento(estabelecimentoConfiguracaoGeral.IdEstabelecimento);

            foreach (var p in estabelecimentoProfissionais)
            {
                p.ConfigurarComissoes();
            }

            //if (EstabelecimentoRepository.
            //        ConfiguracaoDeDataRecebimentoComissaoFoiAlterado(estabelecimentoConfiguracaoGeral)) {
            //    var lista = EstabelecimentoProfissionalRepository.ListarFiltrandoPelaConfiguracaoDataPagamentoComissao(estabelecimentoConfiguracaoGeral.IdEstabelecimento, false, null);
            //    foreach (var estabelecimentoProfissional in lista) {
            //        estabelecimentoProfissional.RecebeComissaoNaDataPrevistaDeRecebimento = estabelecimentoConfiguracaoGeral.PagarComissoesNaDataPrevistaDeRecebimento;
            //        EstabelecimentoProfissionalRepository.UpdateNoFlush(estabelecimentoProfissional);
            //    }
            //}

            //if (EstabelecimentoRepository.
            //        ConfiguracaoDeDescontoComissaoFoiAlterado(estabelecimentoConfiguracaoGeral)) {
            //    var lista = EstabelecimentoProfissionalRepository.ListarFiltrandoPelaConfiguracaoDescontoOperadoraProfissional(estabelecimentoConfiguracaoGeral.IdEstabelecimento, false, null);
            //    foreach (var estabelecimentoProfissional in lista) {
            //        estabelecimentoProfissional.DescontaTaxaOperadoraNaComissao = estabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao;
            //        EstabelecimentoProfissionalRepository.UpdateNoFlush(estabelecimentoProfissional);
            //    }
            //}

            //if (EstabelecimentoRepository.
            //        ConfiguracaoDeDescontoComissaoAssistenteFoiAlterado(estabelecimentoConfiguracaoGeral)) {
            //    var lista = EstabelecimentoProfissionalRepository.ListarFiltrandoPelaConfiguracaoDescontoOperadoraAssistente(estabelecimentoConfiguracaoGeral.IdEstabelecimento, false, null);
            //    foreach (var estabelecimentoProfissional in lista) {
            //        estabelecimentoProfissional.DescontaTaxaOperadoraNaComissaoAssistente = estabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissaoDeAssistentes;
            //        EstabelecimentoProfissionalRepository.UpdateNoFlush(estabelecimentoProfissional);
            //    }
            //}

            EstabelecimentoProfissionalRepository.Flush();
        }

        //public void AlterarExcecaoDeDataRecebimentoComissao(EstabelecimentoProfissional ep, bool ativarExcessaoDoProfissional, bool configuracaoNoEstabelecimento) {
        //    ep.ConfigurarComissoes();

        //    ep.RecebeComissaoNaDataPrevistaDeRecebimento = configuracaoNoEstabelecimento && !ativarExcessaoDoProfissional;
        //    ep.EhExcecaoComissaoNaDataPrevistaDeRecebimento = ativarExcessaoDoProfissional;
        //    EstabelecimentoProfissionalRepository.Update(ep);
        //}

        //public void AlterarExcecaoDeDescontoComissaoProfissional(EstabelecimentoProfissional ep, bool descontarTaxaOperadora, bool configuracaoNoEstabelecimento) {
        //    ep.DescontaTaxaOperadoraNaComissao = configuracaoNoEstabelecimento && !descontarTaxaOperadora;
        //    ep.EhExcecaoDescontoTaxaOperadora = descontarTaxaOperadora;
        //    EstabelecimentoProfissionalRepository.Update(ep);
        //}

        //public void AlterarExcecaoDeDescontoComissaoAssistente(EstabelecimentoProfissional ep, bool descontarTaxaOperadora, bool configuracaoNoEstabelecimento) {
        //    ep.DescontaTaxaOperadoraNaComissaoAssistente = configuracaoNoEstabelecimento && !descontarTaxaOperadora;
        //    ep.EhExcecaoDescontoTaxaOperadoraAssistente = descontarTaxaOperadora;
        //    EstabelecimentoProfissionalRepository.Update(ep);
        //}

        public List<EstabelecimentoProfissionalDTO> ObterProfissionaisEItensRelacionadosPorTransacao(int idTransacao)
        {
            var estabelecimentoProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .ObterEstabelecimentoProfissionaisEnvolvidosNaTransacao(idTransacao) ?? Enumerable.Empty<EstabelecimentoProfissional>();

            var estabelecimentoProfissionaisDTO = estabelecimentoProfissionais
                .Select(estabelecimentoProfissional => new EstabelecimentoProfissionalDTO
                {
                    IdPessoaDoProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa,
                    IdDoProfissional = estabelecimentoProfissional.Profissional.IdProfissional,
                    IdDoProfissionalNoEstabelecimento = estabelecimentoProfissional.Codigo,
                    IdDosItens = new List<int>()
                })
                .ToList();

            var idsProfissionais = estabelecimentoProfissionais.Select(p => p.Profissional.IdProfissional).ToList();
            var idsPessoasFisicas = estabelecimentoProfissionais.Select(p => p.Profissional.PessoaFisica.IdPessoa).ToList();

            var produtosPorPessoa = Domain.Vendas.ItemVendaProdutoRepository.Queryable()
                .Where(p => p.Venda.Transacao.Id == idTransacao && idsPessoasFisicas.Contains(p.PessoaComissionada.IdPessoa))
                .GroupBy(p => p.PessoaComissionada.IdPessoa)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(p => p.EstabelecimentoProduto.Id).ToList()
                );

            var servicosPorProfissional = Domain.Pessoas.HorarioTransacaoRepository.Queryable()
                .Where(p => p.Transacao.Id == idTransacao && idsProfissionais.Contains(p.Horario.Profissional.IdProfissional))
                .GroupBy(p => p.Horario.Profissional.IdProfissional)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(p => p.Horario.ServicoEstabelecimento.IdServicoEstabelecimento).ToList()
                );

            var pacotesPorPessoa = Domain.Vendas.ItemVendaPacoteRepository.Queryable()
                .Where(p => p.Venda.Transacao.Id == idTransacao && idsPessoasFisicas.Contains(p.PessoaComissionada.IdPessoa))
                .GroupBy(p => p.PessoaComissionada.IdPessoa)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(p => p.PacoteCliente.PacoteOriginal.Id).ToList()
                );

            AdicionarItensAosProfissionais(produtosPorPessoa, servicosPorProfissional, pacotesPorPessoa, estabelecimentoProfissionaisDTO);

            return estabelecimentoProfissionaisDTO;
        }

        private void AdicionarItensAosProfissionais(
            Dictionary<int, List<int>> produtosPorPessoa,
            Dictionary<int, List<int>> servicosPorProfissional,
            Dictionary<int, List<int>> pacotesPorPessoa,
            List<EstabelecimentoProfissionalDTO> estabelecimentoProfissionaisDTO)
        {
            foreach (var estabelecimentoProfissional in estabelecimentoProfissionaisDTO)
            {
                if (produtosPorPessoa.ContainsKey(estabelecimentoProfissional.IdPessoaDoProfissional))
                {
                    estabelecimentoProfissional.IdDosItens.AddRange(produtosPorPessoa[estabelecimentoProfissional.IdPessoaDoProfissional]);
                }

                if (servicosPorProfissional.ContainsKey(estabelecimentoProfissional.IdDoProfissional))
                {
                    estabelecimentoProfissional.IdDosItens.AddRange(servicosPorProfissional[estabelecimentoProfissional.IdDoProfissional]);
                }

                if (pacotesPorPessoa.ContainsKey(estabelecimentoProfissional.IdPessoaDoProfissional))
                {
                    estabelecimentoProfissional.IdDosItens.AddRange(pacotesPorPessoa[estabelecimentoProfissional.IdPessoaDoProfissional]);
                }
            }
        }

        public void ValidarEstabelecimentoProfissionalEstaAtivo(int idEstabelecimentoProfissional)
        {
            var estabelecimentoProfissionalEstaAtivo = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Any(p => p.Codigo == idEstabelecimentoProfissional && p.Ativo);
            if (!estabelecimentoProfissionalEstaAtivo)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Este profissional não está mais realizando serviços no estabelecimento.");
            }
        }

        public void RealizarPushParaSNS(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            try
            {
                var qtdProfissionaisComAgendaEServicosAtivos = Domain.Pessoas.ProfissionalRepository.QuantidadeDeProfissionaisAtivosComServicosAtivosComAgenda(estabelecimentoProfissional.Estabelecimento.IdEstabelecimento);

                var dados = new
                {
                    qtdProfissionaisComAgendaEServicosAtivos,
                    estabelecimentoProfissional.Estabelecimento.IdEstabelecimento,
                    Estabelecimento = estabelecimentoProfissional.Estabelecimento.NomeDeExibicaoNoPortal
                };

                var json = JsonConvert.SerializeObject(dados);
                var sns = ConfiguracoesTrinks.AWS.PushQtdAtualProfissionaisComAgenda;
                ServicoDeNotificacaoSNS.PublicarNotificacaoSNS(sns, json);
            }
            catch (Exception e)
            {
                ErrorSignal.FromContext(HttpContext.Current).Raise(e, HttpContext.Current);
            }
        }
        public bool TemAcessoAoResumoAgenda(Estabelecimento estabelecimentoAutenticado, EstabelecimentoProfissional estabelecimentoProfissional, bool modoPAT, bool acessoTotalOuRecepcao)
        {
            if (!modoPAT && acessoTotalOuRecepcao)
            {
                return false;
            }
            else
            {
                var profissionalPodeVerResumoAgenda = estabelecimentoAutenticado.EstabelecimentoConfiguracaoGeral.ProfissionalPodeVerResumoAgenda;
                return profissionalPodeVerResumoAgenda &&
                       estabelecimentoProfissional != null &&
                       estabelecimentoProfissional.PossuiAgenda;
            }
        }

        public List<KeyValuePair<int, string>> ObterEstabelecimentoProfissionaisParaCampanha(int idEstabelecimento)
        {
            var profissionaisAtivosEInativosQueRealizaramAlgumServico = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.ObterProfissionaisAtivosOuInativosQueRealizamOuRealizaramServico(idEstabelecimento)
                                                                        .OrderByDescending(p => p.Ativo)
                                                                        .ThenBy(p => p.Profissional.PessoaFisica.NomeCompleto)
                                                                        .ToList();
            var profissionaisDTO = profissionaisAtivosEInativosQueRealizaramAlgumServico.ToEstabelecimentoProfissionalCampanha();
            return profissionaisDTO;
        }

        public bool ValidarSePjPertenceAoEstabelecimentoOuProfissional(int idPessoaJuridica, Estabelecimento estabelecimento)
        {
            var ehPjdoEstabelecimento = idPessoaJuridica == estabelecimento.PessoaJuridica.IdPessoa;
            var ehProfissionalDoEstabelecimento = !ehPjdoEstabelecimento && Domain.Pessoas.EstabelecimentoProfissionalRepository.EhProfissionalPjDoEstabelecimento(idPessoaJuridica, estabelecimento.IdEstabelecimento);

            if (ehPjdoEstabelecimento || ehProfissionalDoEstabelecimento)
                return true;

            return false;
        }

        #endregion Métodos Públicos

        public void ReplicarHorariosParaProfissionaisDoEstabelecimento(int idEstabelecimentoProfissionalParaCopiar, List<HorarioTrabalho> horariosParaCopiar, int[] idsDeEstabelecimentoProfissionalParaIgnorar)
        {
            var profissionais = ObterProfissionaisParaReplicacaoDeHorarios(idEstabelecimentoProfissionalParaCopiar, idsDeEstabelecimentoProfissionalParaIgnorar);

            foreach (var profissional in profissionais)
            {
                foreach (var horarioParaCopiar in horariosParaCopiar)
                {
                    // Pega todos os horários do profissional para o dia da semana que está sendo copiado
                    var horariosDoProfissional = profissional.HorarioTrabalhoLista.Where(h => h.DiaSemana == horarioParaCopiar.DiaSemana);

                    HorarioTrabalho horarioParaEditar;

                    // Se o profissional só possui um horário ativo para esse dia da semana, esse é o registro a ser editado
                    if (horariosDoProfissional.Count(h => h.Ativo) == 1)
                    {
                        horarioParaEditar = horariosDoProfissional.Single(h => h.Ativo);
                    }
                    else
                    {

                        // se ele possui mais de um horário ativo (situação de exceção) ou nenhum, desativa todos os horários
                        foreach (var horario in horariosDoProfissional.Where(h => h.Ativo))
                        {
                            horario.Ativo = false;
                            Domain.Pessoas.HorarioTrabalhoRepository.UpdateNoFlush(horario);
                        }

                        // seleciona o horário mais recente para atualização
                        horarioParaEditar = horariosDoProfissional.OrderByDescending(h => h.Codigo).FirstOrDefault();

                        // se não encontrou nenhum horário, inclui um novo registro
                        if (horarioParaEditar == null)
                        {
                            horarioParaEditar = new HorarioTrabalho()
                            {
                                Codigo = 0,
                                DiaSemana = horarioParaCopiar.DiaSemana,
                                EstabelecimentoProfissional = profissional
                            };
                        }
                    }

                    horarioParaEditar.Ativo = true;
                    horarioParaEditar.FimAlmoco = horarioParaCopiar.FimAlmoco;
                    horarioParaEditar.FimIntervalo = horarioParaCopiar.FimIntervalo;
                    horarioParaEditar.HoraEntrada = horarioParaCopiar.HoraEntrada;
                    horarioParaEditar.HoraSaida = horarioParaCopiar.HoraSaida;
                    horarioParaEditar.InicioAlmoco = horarioParaCopiar.InicioAlmoco;
                    horarioParaEditar.InicioIntervalo = horarioParaCopiar.InicioIntervalo;

                    if (horarioParaEditar.Codigo > 0)
                        Domain.Pessoas.HorarioTrabalhoRepository.UpdateNoFlush(horarioParaEditar);
                    else
                        Domain.Pessoas.HorarioTrabalhoRepository.SaveNewNoFlush(horarioParaEditar);
                }
            }

            Domain.Pessoas.HorarioTrabalhoRepository.Flush();
        }

        private List<EstabelecimentoProfissional> ObterProfissionaisParaReplicacaoDeHorarios(int idEstabelecimentoProfissionalParaCopiar, int[] idsDeEstabelecimentoProfissionalParaIgnorar)
        {
            int idEstabelecimento = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterIdEstabelecimentoDoProfissional(idEstabelecimentoProfissionalParaCopiar);

            if (idEstabelecimento <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao("Estabelecimento do profissional não encontrado");

            var queryProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .ListarPorEstabelecimento(idEstabelecimento, true)
                .Where(p => p.Codigo != idEstabelecimentoProfissionalParaCopiar);

            if (idsDeEstabelecimentoProfissionalParaIgnorar != null && idsDeEstabelecimentoProfissionalParaIgnorar.Any())
            {
                queryProfissionais = queryProfissionais.Where(p => !idsDeEstabelecimentoProfissionalParaIgnorar.Contains(p.Codigo));
            }

            return queryProfissionais.ToList();
        }

        public DadosParaCadastrarRecebedorDto ObterProfissionalParaEdicaoDeSplit(int idEstabelecimentoProfissional)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorId(idEstabelecimentoProfissional);

            var dto = new DadosParaCadastrarRecebedorDto
            {
                Id = estabelecimentoProfissional.Codigo,
                Nome = estabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                Foto = ObterUrlDaFoto(estabelecimentoProfissional),
                PossuiSplit = estabelecimentoProfissional.HabilitaSplitPagamento &&
                                     estabelecimentoProfissional.EhRecebedorNoConnectPagarme(),
                EhRecebedorNoConnectPagarme = estabelecimentoProfissional.EhRecebedorNoConnectPagarme(),
                EmailPF = estabelecimentoProfissional.Profissional.PessoaFisica.Email,
                Agencia = estabelecimentoProfissional?.Agencia,
                CodigoBanco = estabelecimentoProfissional?.CodigoDoBanco,
                DigitoVerificadorAgencia = estabelecimentoProfissional?.AgenciaDV,
                DigitoVerificadorConta = estabelecimentoProfissional?.NumeroContaDV,
                NumeroConta = estabelecimentoProfissional?.NumeroConta,
                IdTipoConta = estabelecimentoProfissional.TipoDeContaBancaria != null ? estabelecimentoProfissional.TipoDeContaBancaria.Id : 0,
                DocumentoDaContaBancaria = estabelecimentoProfissional.DocumentoDaContaBancaria,
                NomeDoTitularDaContaBancaria = estabelecimentoProfissional.NomeDoTitularDaContaBancaria,
                Documento = estabelecimentoProfissional.Profissional.PessoaFisica.Cpf,
                DataNascimento = estabelecimentoProfissional.DataNascimento,
                Cep = estabelecimentoProfissional.Endereco?.Cep,
                TipoLogradouro = estabelecimentoProfissional.Endereco?.TipoLogradouro?.Nome,
                Logradouro = estabelecimentoProfissional.Endereco?.Logradouro,
                Numero = estabelecimentoProfissional.Endereco?.Numero,
                Bairro = estabelecimentoProfissional.Endereco?.Bairro,
                Estado = estabelecimentoProfissional.Endereco?.UF?.Nome,
                Cidade = estabelecimentoProfissional.Endereco?.Cidade,
                Complemento = estabelecimentoProfissional.Endereco?.Complemento,
            };

            return dto;
        }

        private string ObterUrlDaFoto(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            return estabelecimentoProfissional.FotoPrincipal.ObterCaminhoWeb(DimemsoesFotosEnum.Dim60x60);
        }

        public void EditarDadosDoRecebedor(DadosParaCadastrarRecebedorDto dadosParaCadastrarRecebedorDto)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorId(dadosParaCadastrarRecebedorDto.Id);
            PreencherInformacoesDoRecebedor(estabelecimentoProfissional, dadosParaCadastrarRecebedorDto);

            if (!dadosParaCadastrarRecebedorDto.PossuiSplit)
            {
                Domain.Pessoas.EstabelecimentoProfissionalRepository.Update(estabelecimentoProfissional);
            }
            else
            {
                var conta = ToContaBancariaPessoa(dadosParaCadastrarRecebedorDto, estabelecimentoProfissional.Profissional.PessoaFisica);
                var configuracaoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimentoProfissional.Estabelecimento.IdEstabelecimento);
                if (configuracaoPos != null && configuracaoPos.TipoPOS != null &&
                    configuracaoPos.TipoPOS.EhConnectPagarme && !estabelecimentoProfissional.EhRecebedorNoConnectPagarme())
                {
                    dadosParaCadastrarRecebedorDto.SecretKey = configuracaoPos.SecretKey;
                    dadosParaCadastrarRecebedorDto.IdEstabelecimentoProfissional = estabelecimentoProfissional.Codigo;

                    dadosParaCadastrarRecebedorDto.CodigoDoBanco = 
                        Domain.PagamentosOnlineNoTrinks.InstituicaoBancariaRepository.Load(int.Parse(conta.CodigoBanco)).Codigo.ToString();

                    var idRecebedor = Domain.Financeiro.SubadquirenteService
                        .RealizarCadastroRecebedor(dadosParaCadastrarRecebedorDto);

                    if (string.IsNullOrEmpty(idRecebedor))
                        ValidationHelper.Instance.AdicionarItemValidacao("Não foi possível criar o recebedor para esse profissional.");
                    else
                    {
                        estabelecimentoProfissional.IdExternosDeBeneficiario = idRecebedor;
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.Update(estabelecimentoProfissional);
                    }
                }
                else
                {
                    Domain.Pessoas.EstabelecimentoProfissionalRepository.Update(estabelecimentoProfissional);
                }
            }
        }

        private void PreencherInformacoesDoRecebedor(EstabelecimentoProfissional estabelecimentoProfissional, DadosParaCadastrarRecebedorDto dadosDoRecebedorDto)
        {
            estabelecimentoProfissional.HabilitaSplitPagamento = dadosDoRecebedorDto.PossuiSplit;
            estabelecimentoProfissional.Endereco = ToEndereco(dadosDoRecebedorDto, estabelecimentoProfissional.Profissional.PessoaFisica, estabelecimentoProfissional.Estabelecimento.PessoaJuridica);

            //Dados bancários
            estabelecimentoProfissional.CodigoDoBanco = dadosDoRecebedorDto.CodigoBanco;
            estabelecimentoProfissional.Agencia = dadosDoRecebedorDto.Agencia;
            estabelecimentoProfissional.AgenciaDV = dadosDoRecebedorDto.DigitoVerificadorAgencia;
            estabelecimentoProfissional.NumeroConta = dadosDoRecebedorDto.NumeroConta;
            estabelecimentoProfissional.NumeroContaDV = dadosDoRecebedorDto.DigitoVerificadorConta;
            estabelecimentoProfissional.TipoDeContaBancaria = Domain.Financeiro.TipoContaBancariaRepository.Load(dadosDoRecebedorDto.IdTipoConta);
            estabelecimentoProfissional.DataNascimento = dadosDoRecebedorDto.DataNascimento;
            estabelecimentoProfissional.DocumentoDaContaBancaria = dadosDoRecebedorDto.DocumentoDaContaBancaria.SomenteNumeros();
            estabelecimentoProfissional.NomeDoTitularDaContaBancaria = dadosDoRecebedorDto.NomeDoTitularDaContaBancaria;
        }

        public bool UtilizaMenuLateral(string email, int idConta, int idEstabelecimento)
        {
            var utilizaMenuLateral = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .UtilizaMenuLateral(email, idConta, idEstabelecimento);
            return utilizaMenuLateral;
        }

        public bool PossuiSplitHabilitado(int idPessoa, int idEstabelecimento)
        {
            var profissional = Domain.Pessoas.ProfissionalRepository.ObterPorPessoaFisica(idPessoa);
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(profissional.IdProfissional, idEstabelecimento);
            var idTipoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterIdTipoPOSDoEstabelecimento(idEstabelecimento);

            return estabelecimentoProfissional.HabilitaSplitPagamento && estabelecimentoProfissional.EhRecebedorNoConnectPagarme() && idTipoPos == (int)SubadquirenteEnum.ConnectPagarme;
        }

        private ContaBancariaPessoa ToContaBancariaPessoa(DadosParaCadastrarRecebedorDto dto, PessoaFisica pessoa)
        {
            return new ContaBancariaPessoa
            {
                Agencia = dto.Agencia,
                CodigoBanco = dto.CodigoBanco.ToString(),
                DigitoVerificadorAgencia = dto.DigitoVerificadorAgencia,
                DigitoVerificadorNumeroConta = dto.DigitoVerificadorConta,
                NumeroConta = dto.NumeroConta,
                Pessoa = pessoa,
                TipoConta = Domain.Financeiro.TipoContaBancariaRepository.Load(dto.IdTipoConta),
                AntecipacaoAutomatica = false,
                Documento = dto.DocumentoDaContaBancaria?.Replace(".", "").Replace("-", "").Replace("/", "")
            };
        }

        private Endereco ToEndereco(DadosParaCadastrarRecebedorDto dto, PessoaFisica pessoa, PessoaJuridica pessoaJuridica)
        {
            var tipoLogradouro = Domain.Pessoas.TipoLogradouroRepository.ObterPorNome(dto.TipoLogradouro);
            var uf = Domain.Pessoas.UFRepository.ObterPorNome(dto.Estado);

            return new Endereco
            {
                TipoLogradouro = tipoLogradouro,
                Logradouro = dto.Logradouro,
                Numero = dto.Numero,
                Complemento = dto.Complemento,
                Bairro = dto.Bairro,
                Cidade = dto.Cidade,
                UF = uf,
                Cep = dto.Cep.Replace("-", ""),
                Pessoa = pessoa,
                Ativo = true,
                Dono = pessoaJuridica
            };
        }

        public void AtivarSplitDePagamentoParaEstabelecimentoProfissional(int idEstabelecimento, string stoneCode, string affiliationCode)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .ObterPorEstabelecimentoEStoneCode(idEstabelecimento, stoneCode);

            if(estabelecimentoProfissional == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Profissional não encontrado.");
                return;
            }

            estabelecimentoProfissional.IdExternosDeBeneficiario = affiliationCode;
            estabelecimentoProfissional.HabilitaSplitPagamento = true;

            Domain.Pessoas.EstabelecimentoProfissionalRepository.Update(estabelecimentoProfissional);
        }
    }
}