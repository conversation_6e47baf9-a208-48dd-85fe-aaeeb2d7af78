﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Services
{

    public interface IEstabelecimentoFormaPagamentoService : IService
    {

        void Inativar(EstabelecimentoFormaPagamento entidade);

        void Inativar(int idFormaPagamento, int idEstabelecimento);

        void Ativar(int idFormaPagamento, int idEstabelecimento);

        void Manter(List<EstabelecimentoFormaPagamento> entidades);

        decimal ObterPercentualDescontoOperadora(int idEstabelecimento, int idFormaPagamento, int numeroParcelas = 1);

        decimal ObterPercentualDescontoOperadora(EstabelecimentoFormaPagamento efp, int numeroParcelas = 1);

        EstabelecimentoFormaPagamento AssociarFormaDePagamento(FormaPagamento formaPagamento, Estabelecimento estabelecimento);

        void AssociarFormaDePagamentoPorIdFormaDePagamento(int idFormaPagamento, int idEstabelecimento);

        void ManterFormasDePagamentoBelezinha(Estabelecimento estabelecimento);

        void SalvarAlteracoes(EstabelecimentoFormaPagamento estabelecimentoFormaPagamento);

        void AtualizarStatusDaFormaDePagamento(int idEstabelecimento, int idFormaDePagamento);

        void SalvarFormaDePagamentoDoEstabelecimento(int idEstabelecimento, int idTipoForma, string nomeFormaDePagamento);

        [Obsolete("Este método não considera todas as regras. Usar ListarFormasDePagamentoParaFechamentoDeContas")]
        IList<EstabelecimentoFormaPagamento> ListarOpcoesDeFormasDePagamentoParaFechamentoDeContas(Estabelecimento estabelecimentoAutenticado);

        List<EstabelecimentoFormaPagamento> ListarFormasDePagamentoParaFechamentoDeContas(bool ehCompraCredito = false);

        int ObterDiasParaReceberDaOperadora(EstabelecimentoFormaPagamento efp, Transacao transacao = null);

        void AssociarFormasDePagamentoDoTipoPOS(Estabelecimento estabelecimento, TipoPOS tipoPOS);

        void AjustarTaxaDeOperadoraParaFormaDePagamento(EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPOS);

        bool FormaDePagamentoEstahAtivaParaEstabelecimento(int idEstabelecimento, FormaPagamentoEnum formaPagamento);

        void AssociarFormaPagamentoParaVendaHotsite(Estabelecimento estabelecimento);
        void AtualizarTaxasDePagamentoDoEstabelecimento(int idTipoPosAntigo, EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPOS, int idTipoPosNovo);

        EstabelecimentoFormaPagamento ObterFormaDePagamentoDescontoProfissionalCriarSeNecessario(Estabelecimento estabelecimento);
    }
}