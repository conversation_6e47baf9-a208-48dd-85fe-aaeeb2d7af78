﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.NotaFiscalDoConsumidor;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Services
{

    public class UnificacaoService : BaseService, IUnificacaoService
    {

        [TransactionInitRequired]
        public void Unificar(PessoaFisica pessoaDescartada, PessoaFisica pessoaDefinitiva, Cliente clienteDefinitivo = null)
        {
            if (clienteDefinitivo != null)
                pessoaDefinitiva = clienteDefinitivo.PessoaFisica;

            if (pessoaDescartada != pessoaDefinitiva)
            {
                pessoaDescartada.IdPessoaUnificacao = pessoaDefinitiva.IdPessoa;
                Domain.Pessoas.PessoaFisicaRepository.Flush();
            }

            Unificar(pessoaDefinitiva);
        }

        [TransactionInitRequired]
        public void Unificar(PessoaFisica pessoaFisica, Cliente clienteDefinitivo)
        {
            if (clienteDefinitivo != null)
                pessoaFisica = clienteDefinitivo.PessoaFisica;

            Unificar(pessoaFisica);
        }

        [TransactionInitRequired]
        public void Unificar(PessoaFisica pessoaFisica)
        {
            bool ehClienteWeb;
            var possuiConflito = MarcarPessoasASeremUnificadas(pessoaFisica, out ehClienteWeb);

            if (!possuiConflito)
                return;

            Domain.Pessoas.PessoaFisicaRepository.Flush();

            CriarClienteSeNecessario(pessoaFisica, ehClienteWeb);
            UnificarClientesEstabelecimentoDePessoasJaMarcadas(pessoaFisica);
            UnificarClientesDePessoasJaMarcadas(pessoaFisica);
            UnificarPessoasJaMarcadas(pessoaFisica);

            Domain.Pessoas.PessoaFisicaRepository.Flush();

            RecalcularDadosCongeladosDoCliente(pessoaFisica);
        }

        [TransactionInitRequired]
        public List<string> Unificar1e2(int idPessoa1, int idPessoa2, string cpf, string email)
        {
            var mensagens = new List<string>();

            PessoaFisica pf1 = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoa1);
            PessoaFisica pf2 = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoa2);

            if (pf1 == null)
            {
                mensagens.Add($"Pessoa 1 não existe");
                return mensagens;
            }
            if (pf2 == null)
            {
                mensagens.Add($"Pessoa 2 não existe");
                return mensagens;
            }

            if (pf1.IdPessoaUnificacao.HasValue)
            {
                mensagens.Add($"Pessoa 1 já marcada para unficação");
                return mensagens;
            }
            if (pf2.IdPessoaUnificacao.HasValue)
            {
                mensagens.Add($"Pessoa 2 já marcada para unficação");
                return mensagens;
            }

            if (pf1.Email != email && pf2.Email != email && pf1.Cpf != cpf && pf2.Cpf != cpf)
            {
                mensagens.Add($"O e-mail e CPF informados não constam em nenhuma das 2 pessoas");
                return mensagens;
            }

            // UPDATE Pessoa_Fisica SET cpf = @cpf where id_pessoa in (@idPessoa1, @idPessoa2 )
            mensagens.Add($"CPF pessoa 1 alterado de {pf1.Cpf} para {cpf}");
            mensagens.Add($"CPF pessoa 2 alterado de {pf2.Cpf} para {cpf}");
            pf1.Cpf = pf2.Cpf = cpf;

            // UPDATE Pessoa_Fisica SET id_pessoa_unificacao = @idPessoa1 where id_pessoa in (@idPessoa2 )
            mensagens.Add($"IdPessoaUnificacao da pessoa 2 alterado de {pf2.IdPessoaUnificacao} para {idPessoa1}");
            pf2.IdPessoaUnificacao = idPessoa1;

            // UPDATE Pessoa SET email_contato = @email where id_pessoa in (@idPessoa1, @idPessoa2 )
            mensagens.Add($"Email pessoa 1 alterado de {pf1.Email} para {email}");
            mensagens.Add($"Email pessoa 2 alterado de {pf2.Email} para {email}");
            pf1.Email = pf2.Email = email;

            Domain.Pessoas.ContaRepository.Flush();

            Conta conta1 = Domain.Pessoas.ContaRepository.ObterContaAtivaPorIdPessoa(idPessoa1);
            Conta conta2 = Domain.Pessoas.ContaRepository.ObterContaAtivaPorIdPessoa(idPessoa2);

            if (conta2 != null && conta1 != null)
            {
                // UPDATE Conta set email = CONCAT('(u)', @email) where id_pessoa = @idPessoa2
                var emailAlterado = email.Replace("@", "UNIF" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + "@");
                mensagens.Add($"Email conta 2 alterado de {conta2.Email} para {emailAlterado}");
                conta2.Email = emailAlterado;

                Domain.Pessoas.ContaRepository.Flush();
            }
            if (conta1 != null)
            {
                // UPDATE Conta set email = @email where id_pessoa = @idPessoa1
                mensagens.Add($"Email conta 1 alterado de {conta1.Email} para {email}");
                conta1.Email = email;
            }
            else if (conta2 != null)
            {
                mensagens.Add($"Conta da pessoa 2 passando para pessoa 1 pq a pessoa 1 não tem conta");
                conta2.Email = email;
                conta2.Pessoa = pf1;
            }

            Domain.Pessoas.ContaRepository.Flush();

            // UPDATE Usuario_Adm_Estabelecimento SET id_pessoa_fisica = @idPessoa1 WHERE id_pessoa_fisica in (@idPessoa2)
            // AND id_estabelecimento NOT IN (SELECT id_estabelecimento FROM Usuario_Adm_Estabelecimento WHERE id_pessoa_fisica = @idPessoa1)
            List<UsuarioEstabelecimento> usuarios1 = Domain.Pessoas.UsuarioEstabelecimentoRepository.ListarPorPessoa(idPessoa1).ToList();
            List<UsuarioEstabelecimento> usuarios2 = Domain.Pessoas.UsuarioEstabelecimentoRepository.ListarPorPessoa(idPessoa2).ToList();
            foreach (var u in usuarios2)
            {
                var idEstabelecimento = u.Estabelecimento.IdEstabelecimento;
                var pf1EhUsuario = usuarios1.Any(u1 => u1.Estabelecimento.IdEstabelecimento == idEstabelecimento && u1.Ativo);

                if (!pf1EhUsuario)
                {
                    mensagens.Add($"Trocando pessoa do usuário {u.IdUsuarioEstabelecimento} para {idPessoa1} pq a pessoa 1 não tem usuário nesse estabelecimento");
                    u.PessoaFisica = pf1;
                }
            }
            Domain.Pessoas.ContaRepository.Flush();

            return mensagens;
        }

        private static bool MarcarPessoasASeremUnificadas(PessoaFisica pessoaFisica, out bool ehClienteWeb)
        {
            var pfConflitantes = ObterCadastrosDiferentesDaMesmaPessoa(pessoaFisica);
            ehClienteWeb = false;

            var pfDefinitiva = pfConflitantes.FirstOrDefault(f => f.IdPessoaUnificacao == null && f.Contas.Any(g => g.Ativo));
            if (pfDefinitiva == null)
                pfDefinitiva = pfConflitantes.FirstOrDefault(f => f.Contas.Any(g => g.Ativo));

            if (pfDefinitiva == null)
                pfDefinitiva = pessoaFisica;
            else
                ehClienteWeb = true;

            pfConflitantes.RemoveAll(f => f.IdPessoa == pfDefinitiva.IdPessoa);

            pfDefinitiva.IdPessoaUnificacao = null;

            if (!ehClienteWeb)
            {
                var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable().FirstOrDefault(f => f.Cliente.PessoaFisica == pessoaFisica);
                if (clienteEstabelecimento != null)
                {
                    var estabelecimento = clienteEstabelecimento.Estabelecimento;

                    var pessoasConflitantesARemover = new List<PessoaFisica>();

                    foreach (var pf in pfConflitantes)
                    {
                        var ceConflitante = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(pf.IdPessoa, estabelecimento.IdEstabelecimento);
                        if (ceConflitante == null)
                            pessoasConflitantesARemover.Add(pf);
                    }

                    foreach (var pf in pessoasConflitantesARemover)
                    {
                        pfConflitantes.Remove(pf);
                    }
                }
                else
                {
                    var profissional = Domain.Pessoas.ProfissionalRepository.ObterPorPessoaFisica(pfDefinitiva.IdPessoa);

                    if (profissional == null)
                        pfConflitantes.Clear();
                }
            }

            foreach (var pf in pfConflitantes)
            {
                pf.IdPessoaUnificacao = pfDefinitiva.IdPessoa;
            }

            return pfConflitantes.Any();
        }

        private static List<PessoaFisica> ObterCadastrosDiferentesDaMesmaPessoa(PessoaFisica pessoaFisica)
        {
            var pfConflitantesQuery = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var pfConflitantes = new List<PessoaFisica>();

            //TRINKS-10383 - Unificações por CPF não serão mais realizadas nos sistema
            //if (!string.IsNullOrWhiteSpace(pessoaFisica.Cpf)) {
            //    pfConflitantes.AddRange(pfConflitantesQuery.Where(f => f.Cpf == pessoaFisica.Cpf));
            //}
            if (!string.IsNullOrWhiteSpace(pessoaFisica.Email))
            {
                pfConflitantes.AddRange(pfConflitantesQuery.Where(f => f.Email == pessoaFisica.Email));
            }

            pfConflitantes.AddRange(Domain.Pessoas.PessoaFisicaRepository.Queryable().Where(f => f.IdPessoaUnificacao == pessoaFisica.IdPessoa && f.Ativo));
            pfConflitantes.Add(pessoaFisica);

            pfConflitantes = pfConflitantes.Distinct().ToList();
            return pfConflitantes;
        }

        private void RecalcularDadosCongeladosDoCliente(PessoaFisica pessoaFisica)
        {
            var estabelecimentos = ListarEstabelecimentosAAssociar(pessoaFisica);

            foreach (var e in estabelecimentos)
            {
                var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(pessoaFisica.IdPessoaFisica, e.IdEstabelecimento);
                if (clienteEstabelecimento == null)
                    continue;

                Domain.Financeiro.TransacaoService.RecalcularCreditoCliente(clienteEstabelecimento);
                Domain.Fidelidade.PontoGanhoService.RecalcularSaldoDePontosDoCliente(clienteEstabelecimento);
            }
        }

        #region PessoasFisicas

        private static IQueryable<PessoaFisica> ListarPfDescartadas(PessoaFisica pessoaFisica)
        {
            return Domain.Pessoas.PessoaFisicaRepository.Queryable().Where(f => f.IdPessoaUnificacao == pessoaFisica.IdPessoa);
        }

        private void DescartarPessoaFisica(PessoaFisica pfDescartada)
        {
            pfDescartada.Ativo = false;
        }

        private void UnificarPessoasJaMarcadas(PessoaFisica pessoaFisica)
        {
            var pfDescartadas = ListarPfDescartadas(pessoaFisica);

            foreach (var pfDescartada in pfDescartadas)
            {
                TransferirReferencias(pfDescartada, pessoaFisica);
                DescartarPessoaFisica(pfDescartada);
            }

            Domain.Pessoas.PessoaFisicaRepository.Flush();
        }

        #endregion PessoasFisicas

        #region Clientes

        private static void DescartarCliente(Cliente c)
        {
            //if (c.TipoCliente == TipoClienteEnum.Web)
            //    throw new Exception("Não é possível descartar cliente web.");
            c.PessoaFisica.Ativo = false;
            //c.PessoaFisica.Telefones.ExcluirTodos();
        }

        private static void DescartarClientes(IEnumerable<Cliente> clientes)
        {
            foreach (var c in clientes)
            {
                DescartarCliente(c);
            }
        }

        private static IQueryable<Cliente> ListarClientesDescartados(PessoaFisica pessoaFisica)
        {
            var pfDescartadas = ListarPfDescartadas(pessoaFisica);
            var clientesDescartados = Domain.Pessoas.ClienteRepository.Queryable().Where(f => pfDescartadas.Contains(f.PessoaFisica));
            return clientesDescartados;
        }

        private void CriarClienteSeNecessario(PessoaFisica pessoaFisica, bool ehClienteWeb)
        {
            var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(pessoaFisica.IdPessoa);
            if (cliente == null)
            {
                cliente = new Cliente
                {
                    PessoaFisica = pessoaFisica
                };
                Domain.Pessoas.ClienteRepository.SaveNewNoFlush(cliente);
            }

            cliente.TipoCliente = ehClienteWeb ? TipoClienteEnum.Web : TipoClienteEnum.Balcao;

            Domain.Pessoas.ClienteRepository.Flush();
        }

        private void UnificarClientesDePessoasJaMarcadas(PessoaFisica pessoaFisica)
        {
            var clientesDescartados = ListarClientesDescartados(pessoaFisica);
            var clienteDefinitivo = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(pessoaFisica.IdPessoa);

            TransferirReferencias(clientesDescartados, clienteDefinitivo);
            DescartarClientes(clientesDescartados);
        }

        #endregion Clientes

        #region ClienteEstabelecimento

        private static void DescartarClienteEstabelecimento(ClienteEstabelecimento ce)
        {
            Domain.Pessoas.ClienteEstabelecimentoRepository.Refresh(ce);
            ce.Ativo = false;
            //ce.Cliente.ClientesEstabelecimento.Remove(ce);
            //Domain.Pessoas.ClienteEstabelecimentoRepository.DeleteNoFlush(ce);
        }

        private static List<Estabelecimento> ListarEstabelecimentosAAssociar(PessoaFisica pessoaFisica)
        {
            var pfDescartadas = ListarPfDescartadas(pessoaFisica);

            var estabelecimentos = Domain.Pessoas.EstabelecimentoRepository.ListarEstabelecimentosEmQueECliente(pessoaFisica).ToList();
            foreach (var pf in pfDescartadas)
            {
                estabelecimentos.AddRange(Domain.Pessoas.EstabelecimentoRepository.ListarEstabelecimentosEmQueECliente(pf));
            }
            estabelecimentos = estabelecimentos.Distinct().ToList();
            return estabelecimentos;
        }

        private void CriarClientesEstabelecimentoSeNecessario(PessoaFisica pessoaFisica, List<Estabelecimento> estabelecimentos)
        {
            var cliente = Domain.Pessoas.ClienteRepository.ObterPorPessoaFisica(pessoaFisica.IdPessoa);
            foreach (var e in estabelecimentos)
            {
                var ce = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(pessoaFisica.IdPessoa, e.IdEstabelecimento);
                if (ce == null)
                {
                    ce = new ClienteEstabelecimento
                    {
                        Cliente = cliente,
                        Estabelecimento = e
                    };
                    Domain.Pessoas.ClienteEstabelecimentoRepository.SaveNewNoFlush(ce);
                }
                ce.Ativo = true;
            }
            Domain.Pessoas.ClienteEstabelecimentoRepository.Flush();
        }

        private void DescartarClientesEstabelecimentoDescartaveis(PessoaFisica pessoaFisica)
        {
            var pfDescartadas = ListarPfDescartadas(pessoaFisica);

            foreach (var pfDescartada in pfDescartadas)
            {
                var ceDescartados = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable().Where(f => f.Cliente.PessoaFisica == pfDescartada);
                foreach (var ceDescartado in ceDescartados)
                {
                    DescartarClienteEstabelecimento(ceDescartado);
                }
            }
        }

        private void TransferirReferenciasClienteEstabelecimento(PessoaFisica pessoaFisica)
        {
            var pfDescartadas = ListarPfDescartadas(pessoaFisica);
            var ceDefinitivos = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable().Where(f => f.Cliente.PessoaFisica == pessoaFisica).ToList();

            foreach (var pfDescartada in pfDescartadas)
            {
                var ceDescartados = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable().Where(f => f.Cliente.PessoaFisica == pfDescartada);
                foreach (var ceDescartado in ceDescartados)
                {
                    var ceDefinitivo = ceDefinitivos.FirstOrDefault(f => f.Estabelecimento.IdEstabelecimento == ceDescartado.Estabelecimento.IdEstabelecimento); // Tem que existir um CE definitivo no estabelecimento
                    TransferirReferencias(ceDescartado, ceDefinitivo);
                }
            }

            Domain.Pessoas.ClienteEstabelecimentoRepository.Flush();
        }

        private void UnificarClientesEstabelecimentoDePessoasJaMarcadas(PessoaFisica pessoaFisica)
        {
            var estabelecimentos = ListarEstabelecimentosAAssociar(pessoaFisica);
            CriarClientesEstabelecimentoSeNecessario(pessoaFisica, estabelecimentos);
            TransferirReferenciasClienteEstabelecimento(pessoaFisica);
            DescartarClientesEstabelecimentoDescartaveis(pessoaFisica);
        }

        #endregion ClienteEstabelecimento

        #region Transferir

        #region PessoaFisica

        private static void TransferirComandas(PessoaFisica de, PessoaFisica para)
        {
            var comandas = Domain.Vendas.ComandaRepository.Queryable().Where(f => f.PessoaFisicaComprador == de);

            foreach (var p in comandas)
            {
                p.PessoaFisicaComprador = para;
            }
        }

        private static void TransferirPreVendas(PessoaFisica de, PessoaFisica para)
        {
            var preVendas = Domain.Vendas.PreVendaRepository.Queryable().Where(f => f.PessoaFisicaComprador == de);

            foreach (var p in preVendas)
            {
                p.PessoaFisicaComprador = para;
            }
        }

        private static void TransferirReferencias(PessoaFisica pessoaDescartada, PessoaFisica pessoaDefinitiva)
        {
            TransferirPreVendas(pessoaDescartada, pessoaDefinitiva);
            TransferirComandas(pessoaDescartada, pessoaDefinitiva);
            TransferirTransacaoFormasDePagamento(pessoaDescartada, pessoaDefinitiva);
            TransferirTransacoes(pessoaDescartada, pessoaDefinitiva);
            TransferirProfissionais(pessoaDescartada, pessoaDefinitiva);
            TransferirTelefones(pessoaDescartada, pessoaDefinitiva);
            TransferirPontoGanhoDoProgramaDeFidelidade(pessoaDescartada, pessoaDefinitiva);
            TransferirComissoes(pessoaDescartada, pessoaDefinitiva);
            TransferirDespesas(pessoaDescartada, pessoaDefinitiva);
            Domain.DebitoParcial.UnificacaoDeDividasService.TransferirRegistrosDeDividas(pessoaDescartada.IdPessoa, pessoaDefinitiva.IdPessoa);
        }

        private static void TransferirDespesas(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;
            var despesas = Domain.Despesas.LancamentoRepository.Queryable().Where(f => f.PessoaQueRecebeuOuPagou == de);

            foreach (var p in despesas)
                p.PessoaQueRecebeuOuPagou = para;
        }

        private static void TransferirTelefones(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;

            var telefonesDe = de.Telefones.Where(f => f.Ativo);
            TransferirTelefones(telefonesDe, para);
            de.Telefones.ExcluirTodos();
        }

        public static void TransferirTelefones(IEnumerable<Telefone> telefonesDe, PessoaFisica para)
        {
            foreach (var t in telefonesDe)
            {
                var ehProprio = t.Dono == t.Pessoa;
                var jaExiste = para.Telefones.Any(f => f.Ddi == t.Ddi && f.DDD == t.DDD && f.Numero == t.Numero);

                if (!jaExiste)
                {
                    var telefone = new Telefone
                    {
                        Ddi = t.Ddi,
                        DDD = t.DDD.SomenteNumeros(),
                        Dono = ehProprio ? para : t.Dono,
                        Numero = t.Numero.SomenteNumeros(),
                        Operadora = t.Operadora,
                        Pessoa = para,
                        Ramal = t.Ramal,
                        Tipo = t.Tipo
                    };
                    para.Telefones.Add(telefone);
                }
                else if (jaExiste && ehProprio)
                {
                    var telefone = para.Telefones.First(f => f.Ddi == t.Ddi && f.DDD == t.DDD && f.Numero == t.Numero);
                    telefone.Dono = t.Pessoa;
                }

                t.Ativo = false;
            }
        }

        private static void TransferirTransacaoFormasDePagamento(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;
            var formasDePagamento =
                Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Where(f => f.PessoaQuePagou == de);

            foreach (var p in formasDePagamento)
            {
                p.PessoaQuePagou = para;
            }
        }

        private static void TransferirPontoGanhoDoProgramaDeFidelidade(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;
            var pontosGanhos =
                Domain.Fidelidade.PontoGanhoRepository.Queryable().Where(f => f.PessoaFisicaDoCliente == de);

            foreach (var p in pontosGanhos)
            {
                p.PessoaFisicaDoCliente = para;
                foreach (var m in p.MovimentacoesAssociadas)
                {
                    m.PessoaFisicaDoCliente = para;
                    m.Movimentacao.PessoaFisicaDoCliente = para;
                }
                Domain.Fidelidade.PontoGanhoRepository.UpdateNoFlush(p);
            }
        }

        private static void TransferirTransacoes(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;
            var transacoes = Domain.Financeiro.TransacaoRepository.Queryable().Where(f => f.PessoaQuePagou == de);

            foreach (var p in transacoes)
            {
                p.PessoaQuePagou = para;

                foreach (var item in p.FormasPagamento)
                {
                    item.PessoaQuePagou = para;
                }
            }
        }

        private static void TransferirProfissionais(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;
            var profissionaisDe = Domain.Pessoas.ProfissionalRepository.Queryable().Where(f => f.PessoaFisica == de);

            if (!profissionaisDe.Any())
                return;

            var profissionalPara = Domain.Pessoas.ProfissionalRepository.Queryable().FirstOrDefault(f => f.PessoaFisica == para) ?? new Profissional
            {
                PessoaFisica = para
            };

            if (profissionalPara.IdProfissional == 0)
                Domain.Pessoas.ProfissionalRepository.SaveNew(profissionalPara);

            foreach (var pDe in profissionaisDe)
            {
                TransferirHorariosHistoricos(pDe, profissionalPara);
                TransferirEstabelecimentosProfissionais(pDe, profissionalPara);
                TransferirHorarios(pDe, profissionalPara);
            }
        }

        private static void TransferirComissoes(PessoaFisica de, PessoaFisica para)
        {
            if (de == para)
                return;
            var comissoesDe = Domain.Financeiro.ComissaoRepository.Queryable().Where(f => f.PessoaComissionada == de);

            foreach (var cDe in comissoesDe)
            {
                cDe.PessoaComissionada = para;
            }
        }

        #endregion PessoaFisica

        #region ClienteEstabelecimento

        private static void TransferirEndereco(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe != null && clienteEstabelecimentoPara != null &&
                            clienteEstabelecimentoDe.EnderecoTemAlgumaInformacao() && !clienteEstabelecimentoPara.EnderecoTemAlgumaInformacao())
            {
                clienteEstabelecimentoPara.Endereco = clienteEstabelecimentoDe.Endereco;
            }
        }

        private static void TransferirDataCadastro(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe != null && clienteEstabelecimentoPara != null && clienteEstabelecimentoDe.DataCadastro < clienteEstabelecimentoPara.DataCadastro)
            {
                clienteEstabelecimentoPara.DataCadastro = clienteEstabelecimentoDe.DataCadastro;
            }
        }

        private static void TransferirFotos(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe == null || clienteEstabelecimentoPara == null)
                return;
            var listaFotosDeClienteEstabelecimentoDe = Domain.Pessoas.FotoDeClienteEstabelecimentoRepository.ListarFotosPeloClienteEstabelecimento(clienteEstabelecimentoDe.Codigo);

            foreach (FotoDeClienteEstabelecimento foto in listaFotosDeClienteEstabelecimentoDe)
            {
                foto.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirHorarios(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            var horarios = Domain.Pessoas.HorarioRepository.Queryable().Where(f => f.ClienteEstabelecimento == clienteEstabelecimentoDe).ToList();

            foreach (var horario in horarios)
            {
                horario.Cliente = clienteEstabelecimentoPara.Cliente;
                horario.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirMarketingCampanhaPublicoAlvoClienteEstabelecimento(ClienteEstabelecimento ceDe, ClienteEstabelecimento cePara)
        {
            var registros = Domain.Marketing.MarketingCampanhaPublicoAlvoClienteEstabelecimentoRepository.Queryable()
                .Where(f => f.ClienteEstabelecimento == ceDe);

            foreach (var item in registros)
            {
                item.ClienteEstabelecimento = cePara;
            }
        }

        private static void TransferirMarketingEnvioCliente(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            var envioClientes =
                Domain.Marketing.MarketingEnvioClienteRepository.Queryable()
                    .Where(f => f.ClienteEstabelecimento == clienteEstabelecimentoDe)
                    .ToList();
            foreach (var ec in envioClientes)
            {
                ec.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirMovimentacaoEstoque(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe == clienteEstabelecimentoPara)
                return;

            var itens = Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueRepository.Queryable()
                .Where(f => f.ClienteEstabelecimento == clienteEstabelecimentoDe);

            foreach (var p in itens)
            {
                p.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirNotasFiscais(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe == null || clienteEstabelecimentoPara == null)
                return;

            var listaNotasFiscaisDoClienteDe = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.ListarNotasFiscaisDoClienteEstabelecimento(clienteEstabelecimentoDe.Codigo);

            foreach (NotaNFC notaFiscal in listaNotasFiscaisDoClienteDe)
            {
                notaFiscal.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirNotificaoRegistro(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe == clienteEstabelecimentoPara)
                return;

            var itens = Domain.Notificacoes.RegistroNotificacaoRepository.Queryable()
                .Where(f => f.ClienteEstabelecimento.Codigo == clienteEstabelecimentoDe.Codigo);

            foreach (var p in itens)
            {
                p.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirReferencias(ClienteEstabelecimento ceDe, ClienteEstabelecimento cePara)
        {
            TransferirEndereco(ceDe, cePara);
            TransferirDataCadastro(ceDe, cePara);
            TransferirFotos(ceDe, cePara);
            TransferirHorarios(ceDe, cePara);
            TransferirMarketingEnvioCliente(ceDe, cePara);
            TransferirMarketingCampanhaPublicoAlvoClienteEstabelecimento(ceDe, cePara);
            TransferirNotasFiscais(ceDe, cePara);
            TransferirRegistroSMS(ceDe, cePara);
            TransferirMovimentacaoEstoque(ceDe, cePara);
            TransferirNotificaoRegistro(ceDe, cePara);
            TransferirItemVendaClienteEstabelecimentoQueUsou(ceDe, cePara);
            TransferirObservacoes(ceDe, cePara);
            TransferirFormulariosRespondidosDeAnamnese(ceDe, cePara);
            TransferirAnexos(ceDe, cePara);
            ZerarDadosCongeladosDoCliente(ceDe);
        }

        private static void ZerarDadosCongeladosDoCliente(ClienteEstabelecimento ce)
        {
            ce.SaldoDePontosDeFidelidade = 0;
            ce.ValorCredito = 0;
        }

        private static void TransferirFormulariosRespondidosDeAnamnese(ClienteEstabelecimento ceDe, ClienteEstabelecimento cePara)
        {
            if (ceDe == cePara)
                return;

            var pessoaPerguntada = Domain.Formulario.PessoaPerguntadaRepository.ObterPorIdClienteEstabelecimento(ceDe.Codigo);
            if (pessoaPerguntada != null)
            {
                pessoaPerguntada.ClienteEstabelecimento = cePara;
                Domain.Formulario.PessoaPerguntadaRepository.Update(pessoaPerguntada);
            }
        }

        private static void TransferirAnexos(ClienteEstabelecimento ceDe, ClienteEstabelecimento cePara)
        {
            if (ceDe == cePara)
                return;

            var anexosDoCliente = Domain.ClientesAnexos.ClienteAnexoRepository.ListarTodosOsAnexosDoClienteEstabelecimento(ceDe.Codigo);

            foreach (var anexo in anexosDoCliente)
            {
                anexo.IdClienteEstabelecimento = cePara.Codigo;
                Domain.ClientesAnexos.ClienteAnexoRepository.Update(anexo);
            }
        }

        private static void TransferirObservacoes(ClienteEstabelecimento ceDe, ClienteEstabelecimento cePara)
        {
            if (ceDe == cePara)
                return;

            var observacaoDe = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable().Where(t => t.Cliente.IdCliente == ceDe.Cliente.IdCliente).ToList();
            foreach (ClienteEstabelecimento ceObservacao in observacaoDe)
                if (!string.IsNullOrWhiteSpace(ceObservacao.Observacoes))
                    if (string.IsNullOrWhiteSpace(cePara.Observacoes) || !cePara.Observacoes.Contains(ceObservacao.Observacoes))
                    {
                        if (!string.IsNullOrWhiteSpace(cePara.Observacoes))
                            cePara.Observacoes += Environment.NewLine + Environment.NewLine;
                        cePara.Observacoes += ceObservacao.Observacoes;
                    }

            cePara.Observacoes = cePara.Observacoes?.Left(3000);
        }

        private static void TransferirRegistroSMS(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe == clienteEstabelecimentoPara)
                return;

            var itens = Domain.Notificacoes.RegistroNotificacaoRepository.Queryable()
                .Where(f => f.ClienteEstabelecimento == clienteEstabelecimentoDe);

            foreach (var p in itens)
            {
                p.ClienteEstabelecimento = clienteEstabelecimentoPara;
            }
        }

        private static void TransferirItemVendaClienteEstabelecimentoQueUsou(ClienteEstabelecimento clienteEstabelecimentoDe, ClienteEstabelecimento clienteEstabelecimentoPara)
        {
            if (clienteEstabelecimentoDe == clienteEstabelecimentoPara)
                return;

            var itens = Domain.Vendas.ItemVendaProdutoRepository.Queryable()
                .Where(f => f.ClienteEstabelecimentoQueUsou == clienteEstabelecimentoDe);

            foreach (var p in itens)
            {
                p.ClienteEstabelecimentoQueUsou = clienteEstabelecimentoPara;
            }
        }

        #endregion ClienteEstabelecimento

        #region Cliente

        private static void TransferirHorariosHistorico(Cliente de, Cliente para)
        {
            if (de == para)
                return;
            var horarioHistoricos = Domain.Pessoas.HorarioHistoricoRepository.Queryable().Where(f => f.Cliente == de);
            foreach (var h in horarioHistoricos)
            {
                h.Cliente = para;
            }
        }

        private static void TransferirPacotes(Cliente de, Cliente para)
        {
            if (de == para)
                return;
            var pacotes = Domain.Pacotes.PacoteClienteRepository.Queryable().Where(f => f.Cliente == de);

            foreach (var p in pacotes)
            {
                p.Cliente = para;
            }
        }

        private static void TransferirTokenDaTelaDeLembreteSms(Cliente de, Cliente para)
        {
            if (string.IsNullOrWhiteSpace(para.TokenTelaLembreteSms))
            {
                para.TokenTelaLembreteSms = de.TokenTelaLembreteSms;
            }
        }

        private static void TransferirReferencias(IEnumerable<Cliente> clientesDe, Cliente clientePara)
        {
            foreach (var de in clientesDe)
            {
                TransferirHorarios(de, clientePara);
                TransferirHorariosHistorico(de, clientePara);
                TransferirPacotes(de, clientePara);
                TransferirTokenDaTelaDeLembreteSms(de, clientePara);
                TransferirClubeDeAssinaturas(de, clientePara);
            }
        }

        private static void TransferirHorarios(Cliente de, Cliente para)
        {
            if (de == para)
                return;
            var horarios = Domain.Pessoas.HorarioRepository.Queryable().Where(f => f.Cliente == de);
            foreach (var h in horarios)
            {
                h.Cliente = para;
            }
        }

        private static void TransferirClubeDeAssinaturas(Cliente de, Cliente para)
        {
            if (de == para)
                return;

            var assinaturas = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ListarTodasAssinaturasDoCliente(de.PessoaFisica.IdPessoaFisica);

            foreach (var assinatura in assinaturas)
            {
                assinatura.IdPessoaFisicaCliente = para.PessoaFisica.IdPessoaFisica;
            }
        }

        #endregion Cliente

        #region Profissional

        private static void TransferirHorarios(Profissional de, Profissional para)
        {
            Domain.Pessoas.HorarioRepository.TransferirHorariosParaOutroProfissional(de.IdProfissional, para.IdProfissional);
        }

        private static void TransferirHorariosHistoricos(Profissional de, Profissional para)
        {
            Domain.Pessoas.HorarioHistoricoRepository.TransferirHorariosHistoricosParaOutroProfissional(de.IdProfissional, para.IdProfissional);
        }

        private static void TransferirEstabelecimentosProfissionais(Profissional de, Profissional para)
        {
            var epsDe = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Where(f => f.Profissional == de);

            foreach (var ep in epsDe)
            {
                var jahExisteProfissionalNoEstabelecimento = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable().Any(f => f.Profissional == para && f.Estabelecimento == ep.Estabelecimento);
                if (!jahExisteProfissionalNoEstabelecimento)
                {
                    ep.Profissional = para;
                }
            }
        }

        #endregion Profissional

        #endregion Transferir
    }
}