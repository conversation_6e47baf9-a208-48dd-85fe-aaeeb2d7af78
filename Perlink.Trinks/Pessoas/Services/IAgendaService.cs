﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Compromissos.DTO;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.Enums;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Marcadores.Filtros;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.PromocoesOnline;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.Pessoas.Services
{

    public interface IAgendaService : IService
    {

        void AlterarExibicaoProfissionaisDeFolga(int idUsuarioEstabelecimento, bool exibir);

        void AlterarStatusAgendamento(int codigoAgendamento, int codigoNovoStatus, PessoaFisica pessoaAutenticada, PessoaFisica pessoaQueAlterou, bool modoPAT = false, bool alteradoPorAgenda = false);

        List<DadosDeAgendamentosParaGoogleReserveDTO> ListarHorariosFurutosParaGoogleReserve(string idUserGoogleReserve, DateTime data);

        Horario RealizarReagendamentoPeloGoogleReserve(DadosParaRealizarAgendamentoDTO dadosParaRealizarAgendamentoDTO, int v);

        void AlterarConfiguracaoDeLarguraDaAgenda(Estabelecimento estabelecimento, Conta contaAutenticada, int largura);

        void AlterarConfiguracaoDeAlturaDaAgenda(Estabelecimento estabelecimento, Conta contaAutenticada, int altura);

        ResultadoBusca BuscaHorariosDisponiveisPeloCliente(ParametrosBusca parametros);

        ResultadoBusca BuscaHorariosDisponiveisPeloClienteMobile(ParametrosBusca parametros);

        void CancelarHorario(int idHorario, string motivo, HorarioQuemCancelouEnum quemCancelou, PessoaFisica pessoaQueAlterou = null);

        IntervaloDataList DividirEmMarcos(IntervaloDataList periodosParaDividir, int tamanhoIntervalos,
            int duracaoServico, DateTime dataInicio, EstabelecimentoProfissional estabelecimentoProfissional);

        ParametrosBuscaHorario FiltrarHorarios(ParametrosBuscaHorario parametros);

        void FinalizarAgendamento(int codigoAgendamento, PessoaFisica pessoaAutenticada, PessoaFisica pessoaQueAlterou, bool modoPAT = false, bool alteradoPorAgenda = false);

        void FinalizarAgendamentosDoClienteNoDia(int idClienteEstabelecimento, DateTime dataReferencia, PessoaFisica pessoaAutenticada, PessoaFisica pessoaQueAlterou);

        ResultadoBusca ListarHorariosDisponiveis(ParametrosBusca parametros);

        ResultadoPaginado<Horario> ListarHorariosPaginados(ParametrosFiltroHistoricoCliente parametros);

        bool ManterAgendamento(Horario entity, Transacao transacao = null, bool enviaEmail = true, bool sobreescreverStatusInicial = true, bool realizarValidacoes = true, bool modoPAT = false, bool forcarGeracaoHistorico = false, bool trataSplitDeComanda = true, Comanda comanda = null, bool alteradoPorAgendaCliente = false, bool ehComandaRapida = false, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false);

        void ValidarManterAgendamento(Horario entity);

        void ManterAgendamentoEmLote(List<Horario> agendamentos, bool enviarNotificacaoCliente);

        void ManterAgendamentoNoTransaction(Horario entity, Transacao transacao = null, bool enviaEmail = true, bool sobreescreverStatusInicial = true, bool realizarValidacoes = true, bool modoPAT = false, bool forcarGeracaoHistorico = false, bool trataSplitDeComanda = true, bool alteradoPorAgenda = false, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false);

        void ManterAgendamentosDoDia(List<Horario> agendamentos);

        void ManterCancelamentoDeAgendamentosEmBloco(List<Horario> lista);

        void RemoverAgendamento(int codigoAgendamento, PessoaFisica pessoaAutenticada, PessoaFisica pessoaQueAlterou);

        bool VerificarDisponibilidadeDoHorario(ParametrosBuscaHorario parametros);

        bool AlterarDuracaoDoAgendamento(Horario entity, DateTime novaDataHoraFim);

        void AssociarAssistenteAoServico(int idEstabelecimentoProfissional, int idHorario, bool alteradoPorAgendaPorCliente);

        ConsultaDeEtiquetasAssociadasDTO ListarEtiquetasParaAgenda(List<FiltroObjetoEtiquetado> filtroObjetos);

        StatusDTO ValidarERealizarAgendamentoPeloClienteWebERetornarStatus(DadosParaRealizarAgendamentoDTO dadosAgendamento);

        StatusDTO RealizarAgendamentoPeloClienteWebERetornarStatus(DadosParaRealizarAgendamentoDTO dadosAgendamento, int? idHorario);

        Horario RealizarAgendamentoPeloGoogleReserve(DadosParaRealizarAgendamentoComClienteDTO dadosAgendamento, int? idHorario = null);

        StatusDTO RealizarReagendamentoPeloClienteWebERetornarStatus(int idHorario, DadosParaRealizarAgendamentoDTO dadosAgendamento);

        BuscaClientesFuturos ListarAgendamentosFuturoPorNomeDoCliente(Estabelecimento estabelecimento, string nomeDoCliente, int pagina);

        DadosExtrasClienteDTO ObterDadosExtrasDaPesquisaDeclientesDeHoje(Estabelecimento estabelecimento, List<int> status);

        Task CancelarHorarios(List<int> idsHorarios, string motivo, HorarioQuemCancelou quemCancelou, PessoaFisica pessoaQueAlterou, AreasTrinksEnum areaDoTrinks, bool ehUmEstornoDePagamentoOnline);

        Task CancelarHorario(int idHorario, string motivo, HorarioQuemCancelou quemCancelou, PessoaFisica pessoaQueAlterou, AreasTrinksEnum areaDoTrinks);

        void ValidarSeHorarioPagoOnlineMudouADataParaUmDiaComValorDiferente(int idHorario, DateTime novaDataInicio);

        DadosServicoParaOAgendamentoDTO ObterDadosDoServicoParaUmAgendamento(int idServicoEstabelecimento, DateTime? dataParaAgendamento = null);

        bool ValidarSeHorarioFoiPagadoAntecipadamente(Horario entity);

        bool ValidarSeHorarioPodeSerReagendado(Horario horario);

        bool ValidarSeHorarioPodeSerCancelado(Horario horario);

        List<string> ObterMensagensDeConflitoDeAgendamentoComProfissional(int idProfissional, int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int? idHorarioSendoEditado);

        Horario SalvarAgendamentoDTOPeloProfissional(DadosParaRealizarAgendamentoDTO dto, int idHorario);

        bool ExisteConflitoDeHorario(int idProfissional, int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int? idHorario = null);

        List<int> ListarIdsDosProfissionaisQueTrabalhamNoDia(int idEstabelecimento, DateTime dia);

        ConsultaHorariosDisponiveisDTO ConsultarHorariosDisponiveis(int idServicoEstabelecimento, DateTime data, int? idProfissional);
        
        Horario RealizarAgendamentoWebPelaPromocaoOnline(AgendamentoTemporario agendamentoTemp);
        
        Horario RealizarAgendamentoBalcaoPelaPromocaoOnline(AgendamentoTemporario agendamentoTemp);
    }
}