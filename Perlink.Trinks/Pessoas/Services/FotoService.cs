﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Fotos.DTO;
using Perlink.Trinks.Fotos.Enums;
using Perlink.Trinks.Fotos.Services;
using Perlink.Trinks.Onboardings.Enums;
using Perlink.Trinks.Pessoas.Configuracoes;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using RES = Perlink.Trinks.Resources.Pessoas.Services.FotoService;

namespace Perlink.Trinks.Pessoas.Services
{

    public class FotoService : BaseService, IFotoService
    {

        public void DefineFotoPrincipal(Pessoa pessoa, int idFotoPesssoa)
        {
            var fotos = Domain.Pessoas.FotoEstabelecimentoRepository.Queryable().Where(f => f.Pessoa == pessoa);
            foreach (FotoEstabelecimento foto in fotos)
            {
                foto.Principal = foto.Codigo == idFotoPesssoa;
            }
        }

        public bool EstourouLimiteFotos(Pessoa pessoa)
        {
            var quantidadeMaximaFotos = ConfiguracoesTrinks.Fotos.QuantidadeMaximaFotos;
            return (Domain.Pessoas.FotoEstabelecimentoRepository.Queryable().Count(f => f.Pessoa == pessoa)
                >= quantidadeMaximaFotos);
        }

        [TransactionInitRequired]
        public void ExcluirFotoEstabelecimento(int codigoFoto)
        {
            Foto foto = Domain.Pessoas.FotoEstabelecimentoRepository.Load(codigoFoto);

            Domain.Pessoas.FotoEstabelecimentoRepository.Delete(foto);

            RemoverFotosFisicas(foto);
        }

        [TransactionInitRequired]
        public void ExcluirFotoPessoa(int codigoFoto)
        {
            Foto foto = Domain.Pessoas.FotoPessoaRepository.Load(codigoFoto);

            Domain.Pessoas.FotoPessoaRepository.Delete(foto);

            RemoverFotosFisicas(foto);
        }

        public void ExcluirFoto(Foto foto)
        {
            if (foto.TipoFoto == TipoFoto.FotoPessoa)
            {
                var fotoPessoa = (FotoEstabelecimento)foto;
                if (fotoPessoa.Codigo != null) ExcluirFotoEstabelecimento(fotoPessoa.Codigo.Value);
            }
            else
                RemoverFotosFisicas(foto);
        }

        public bool ExisteArquivo(Foto foto)
        {
            return Domain.Fotos.ImagensService.ExisteArquivo(foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original));
        }

        public void RedimensionarImagem(string ArquivoOriginal, string ArquivoNovo, Size tamanhoDestino)
        {
            var original = Image.FromFile(ArquivoOriginal);

            // as linhas abaixo são para prevenir o uso de thumbnail interna
            original.RotateFlip(RotateFlipType.Rotate180FlipNone);
            original.RotateFlip(RotateFlipType.Rotate180FlipNone);

            Image imagemNova = null;
            var realizarCropAoRedimencionarImagem = DeveRealizarCropAoRedimensionarImagem(tamanhoDestino);
            var completarImagemComFundo = DeveCompletarImagemComFundo(tamanhoDestino);

            if (realizarCropAoRedimencionarImagem)
                imagemNova = RedimensionarImagemRealizandoCrop(original, tamanhoDestino);
            else if (!realizarCropAoRedimencionarImagem && completarImagemComFundo)
                imagemNova = RedimensionarImagemMantendoAspectoCompletandoImagemDeFundo(original, tamanhoDestino);
            else
                imagemNova = RedimensionarImagemMantendoAspectoSemCompletarImagemDeFundo(original, tamanhoDestino);

            original.Dispose();

            CriarPastaSeNecessario(ArquivoNovo);

            ImageCodecInfo jpgEncoder = GetEncoder(ImageFormat.Jpeg);

            // Create an Encoder object based on the GUID
            // for the Quality parameter category.
            System.Drawing.Imaging.Encoder myEncoder =
                System.Drawing.Imaging.Encoder.Quality;

            // Create an EncoderParameters object.
            // An EncoderParameters object has an array of EncoderParameter
            // objects. In this case, there is only one
            // EncoderParameter object in the array.
            EncoderParameters myEncoderParameters = new EncoderParameters(1);

            EncoderParameter myEncoderParameter = new EncoderParameter(myEncoder, 80L);
            myEncoderParameters.Param[0] = myEncoderParameter;

            imagemNova.Save(ArquivoNovo, jpgEncoder, myEncoderParameters);
        }

        public void RotacionarImagem(Foto foto, bool sentidoHorario)
        {
            if (foto is FotoEstabelecimento fotoEstabelecimento)
            {
                fotoEstabelecimento.DataAtualizacao = Calendario.Agora();
                Domain.Pessoas.FotoEstabelecimentoRepository.Update(fotoEstabelecimento);
            }

            var path = foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original);

            var stream = Domain.Fotos.ImagensService.ObterStreamDoArquivo(path);
            var imagem = ImagensService.ObterImagemDoStream(stream);
            imagem = ImagensService.RotacionarImagem(imagem, sentidoHorario);

            Domain.Fotos.ImagensService.LimparTodosOsTamanhos(path);

            var streamRotacionada = ImagensService.ObterStreamDaImagem(imagem);
            Domain.Fotos.ImagensService.GravarArquivoDeImagem(streamRotacionada, path);
        }

        [TransactionInitRequired]
        [Obsolete("Substituido pelo método SalvarUploadDeFoto para remover a dependência do HttpPostedFileBase.")]
        public Foto SalvarFoto(Foto foto, HttpPostedFileBase arquivo)
        {
            try
            {
                if (arquivo != null)
                    ValidarArquivoFotoProfissional(arquivo);

                ValidarEGravar(foto, arquivo);
            }
            catch (Exception ex)
            {
                LogService<FotoService>.Error(String.Format("Erro no upload de imagem: {0}, StackTrace: {1}", ex.Message, ex.StackTrace));
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoCorrompida);
            }

            // as linhas abaixo são necessárias, no contrário a entidade será salva no fim da requisição mesmo não chamando o update
            if (!ValidationHelper.Instance.IsValid && (foto.TipoFoto == TipoFoto.FotoPessoa))
            {
                Domain.Pessoas.FotoEstabelecimentoRepository.Evict((FotoEstabelecimento)foto);
            }

            return foto;
        }

        [TransactionInitRequired]
        [Obsolete("Substituido pelo método SalvarUploadDeFoto para remover a dependência do HttpPostedFileBase.")]
        public Foto SalvarFotoApi(Foto foto, HttpPostedFile arquivo)
        {
            try
            {
                if (arquivo != null)
                    ValidarArquivoFotoProfissional(arquivo);

                ValidarEGravar(foto, arquivo);
            }
            catch (Exception ex)
            {
                LogService<FotoService>.Error(String.Format("Erro no upload de imagem: {0}, StackTrace: {1}", ex.Message, ex.StackTrace));
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoCorrompida);
            }

            // as linhas abaixo são necessárias, no contrário a entidade será salva no fim da requisição mesmo não chamando o update
            if (!ValidationHelper.Instance.IsValid && (foto.TipoFoto == TipoFoto.FotoPessoa))
            {
                if (foto is FotoEstabelecimento)
                {
                    Domain.Pessoas.FotoEstabelecimentoRepository.Evict((FotoEstabelecimento)foto);
                }
                else if (foto is FotoPessoa)
                {
                    Domain.Pessoas.FotoPessoaRepository.Evict((FotoPessoa)foto);
                }
            }

            return foto;
        }

        public Foto SalvarFotoLogo(Foto foto, HttpPostedFileBase arquivo)
        {
            try
            {
                if (arquivo != null)
                    ValidarArquivoLogo(arquivo);

                ValidarEGravar(foto, arquivo);
            }
            catch (Exception ex)
            {
                LogService<FotoService>.Error(String.Format("Erro no upload de imagem: {0}, StackTrace: {1}", ex.Message, ex.StackTrace));
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoCorrompida);
            }

            // as linhas abaixo são necessárias, no contrário a entidade será salva no fim da requisição mesmo não chamando o update
            if (!ValidationHelper.Instance.IsValid && (foto.TipoFoto == TipoFoto.FotoPessoa))
            {
                Domain.Pessoas.FotoEstabelecimentoRepository.Evict((FotoEstabelecimento)foto);
            }

            return foto;
        }

        [TransactionInitRequired]
        public Foto SalvarFotoPorUrl(Foto foto, String urlExterna)
        {
            var httpWebRequest = (HttpWebRequest)WebRequest.Create(urlExterna);
            var httpWebReponse = (HttpWebResponse)httpWebRequest.GetResponse();
            var stream = httpWebReponse.GetResponseStream();
            if (stream != null)
            {
                //var image = Image.FromStream(stream);

                if (ValidationHelper.Instance.IsValid)
                {
                    if (foto.TipoFoto == TipoFoto.FotoPessoa)
                    {
                        var fotoPessoa = (FotoEstabelecimento)foto;

                        if (fotoPessoa.Codigo.HasValue)
                        {
                            Domain.Pessoas.FotoEstabelecimentoRepository.Update(fotoPessoa);
                        }
                        else
                        {
                            ValidarQuantidadeDeFotos(fotoPessoa.Pessoa);

                            if (ValidationHelper.Instance.IsValid)
                            {
                                if (Domain.Pessoas.FotoEstabelecimentoRepository.PessoaPossuiFoto(fotoPessoa.Pessoa))
                                    fotoPessoa.Principal = true;

                                Domain.Pessoas.FotoEstabelecimentoRepository.SaveNew(foto);
                            }
                        }
                    }
                    else if (foto.TipoFoto == TipoFoto.EstabelecimentoLogo)
                    {
                        RemoverFotosFisicas(foto);
                    }

                    if (ValidationHelper.Instance.IsValid)
                    {
                        //CriarPastaSeNecessario(foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original));
                        //image.Save(foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original));

                        //RedimensionarFotoParaTodosTamanhos(foto);

                        var path = foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original);
                        Domain.Fotos.ImagensService.LimparTodosOsTamanhos(path);
                        Domain.Fotos.ImagensService.GravarArquivoDeImagem(stream, path);
                    }
                }
            }

            // as linhas abaixo são necessárias, no contrário a entidade será salva no fim da requisição mesmo não chamando o update
            if (!ValidationHelper.Instance.IsValid && (foto.TipoFoto == TipoFoto.FotoPessoa))
            {
                Domain.Pessoas.FotoEstabelecimentoRepository.Evict((FotoEstabelecimento)foto);
            }

            return foto;
        }

        [TransactionInitRequired]
        public Foto SalvarUploadDeFoto(Foto foto, SolicitacaoDeUploadDeFoto arquivo)
        {
            try
            {
                if (arquivo != null)
                    ValidarArquivo(arquivo);

                if (ValidationHelper.Instance.IsValid)
                {
                    ProcederComUpload(foto, arquivo);
                }
            }
            catch (Exception ex)
            {
                LogService<FotoService>.Error(String.Format("Erro no upload de imagem: {0}, StackTrace: {1}", ex.Message, ex.StackTrace));
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoCorrompida);
            }

            // as linhas abaixo são necessárias, no contrário a entidade será salva no fim da requisição mesmo não chamando o update
            if (!ValidationHelper.Instance.IsValid)
            {
                if (foto is FotoEstabelecimento fe)
                    Domain.Pessoas.FotoEstabelecimentoRepository.Evict(fe);
                else if (foto is FotoPessoa fp)
                    Domain.Pessoas.FotoPessoaRepository.Evict(fp);
            }

            return foto;
        }

        public FotoEstabelecimento SalvarUploadDeFotoEstabelecimento(Foto foto, SolicitacaoDeUploadDeFoto arquivo, int? idEstabelecimento)
        {
            try
            {
                if (arquivo != null)
                    ValidarArquivoEstabelecimento(arquivo);

                if (ValidationHelper.Instance.IsValid)
                {
                    ProcederComUpload(foto, arquivo);
                }
            }
            catch (Exception ex)
            {
                LogService<FotoService>.Error(String.Format("Erro no upload de imagem: {0}, StackTrace: {1}", ex.Message, ex.StackTrace));
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoCorrompida);
            }

            // as linhas abaixo são necessárias, no contrário a entidade será salva no fim da requisição mesmo não chamando o update
            if (!ValidationHelper.Instance.IsValid && (foto.TipoFoto == TipoFoto.FotoPessoa))
            {
                Domain.Pessoas.FotoEstabelecimentoRepository.Evict((FotoEstabelecimento)foto);
            }

            if (ValidationHelper.Instance.IsValid && idEstabelecimento != null)
            {
                Domain.Onboardings.RastreioTarefaService.IncrementarRastreio(ETarefaOnboarding.AdicionarFotosEstabelecimento, (int)idEstabelecimento);
            }

            return (FotoEstabelecimento)foto;
        }

        public void ValidarQuantidadeDeFotos(Pessoa pessoa)
        {
            var quantidadeMaximaFotos = ConfiguracoesTrinks.Fotos.QuantidadeMaximaFotos;

            if (Domain.Pessoas.FotoService.EstourouLimiteFotos(pessoa))
            {
                var mensagem = String.Format(RES.MensagemValidacaoQuantidadeMaxima, quantidadeMaximaFotos);
                ValidationHelper.Instance.AdicionarItemValidacao(mensagem);
            }
        }

        private static IEnumerable<DimemsoesFotosEnum> ObterDimensoesFotos()
        {
            return Enum.GetValues(typeof(DimemsoesFotosEnum)).Cast<DimemsoesFotosEnum>().ToList();
        }

        private void CriarPastaSeNecessario(string caminhoDoArquivo)
        {
            var caminhoFisicoPasta = RemoverNomeFotoCaminhoFisico(caminhoDoArquivo);
            if (!Directory.Exists(caminhoFisicoPasta))
            {
                Directory.CreateDirectory(caminhoFisicoPasta);
            }
        }

        private bool DeveCompletarImagemComFundo(Size novoTamanho)
        {
            if ((novoTamanho.Width == 0))
                return false;

            if ((novoTamanho.Width == 790) && (novoTamanho.Height == 550))
                return false;

            if ((novoTamanho.Width == 120) && (novoTamanho.Height == 120))
                return false;

            return true;
        }

        private bool DeveRealizarCropAoRedimensionarImagem(Size novoTamanho)
        {
            if ((novoTamanho.Width == 50) && (novoTamanho.Height == 50))
                return true;

            if ((novoTamanho.Width == 790) && (novoTamanho.Height == 550))
                return true;

            if ((novoTamanho.Width == 120) && (novoTamanho.Height == 120))
                return true;

            return false;
        }

        private ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();

            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }

        private Size ObterTamanhoEscalandoPorAltura(Size tamanhoOriginal, Size tamanhoDesejado)
        {
            var larguraOriginal = tamanhoOriginal.Width;
            var alturaOriginal = tamanhoOriginal.Height;
            var larguraFinal = tamanhoOriginal.Width;
            var alturaFinal = tamanhoOriginal.Height;

            var novoTamanho = new Size();

            if (alturaFinal > tamanhoDesejado.Height && tamanhoDesejado.Height > 0)
            {
                alturaFinal = tamanhoDesejado.Height;
                larguraFinal = alturaFinal * larguraOriginal / alturaOriginal;
            }
            if (larguraFinal > tamanhoDesejado.Width && tamanhoDesejado.Width > 0)
            {
                larguraFinal = tamanhoDesejado.Width;
                alturaFinal = larguraFinal * alturaOriginal / larguraOriginal;
            }

            novoTamanho.Height = alturaFinal;
            novoTamanho.Width = larguraFinal;

            return novoTamanho;
        }

        private void ProcederComUpload(Foto foto, SolicitacaoDeUploadDeFoto arquivo)
        {
            if (foto.TipoFoto == TipoFoto.FotoPessoa)
            {
                if (foto is FotoPessoa)
                {
                    var fotoPessoa = (FotoPessoa)foto;

                    if (fotoPessoa.Codigo.HasValue)
                    {
                        Domain.Pessoas.FotoPessoaRepository.Update(fotoPessoa);
                    }
                    else
                    {
                        ValidarQuantidadeDeFotos(fotoPessoa.Pessoa);

                        if (ValidationHelper.Instance.IsValid)
                        {
                            Domain.Pessoas.FotoPessoaRepository.SaveNew(foto);
                        }
                    }
                }
                else
                {
                    var fotoPessoa = (FotoEstabelecimento)foto;

                    if (fotoPessoa.Codigo.HasValue)
                    {
                        Domain.Pessoas.FotoEstabelecimentoRepository.Update(fotoPessoa);
                    }
                    else
                    {
                        ValidarQuantidadeDeFotos(fotoPessoa.Pessoa);

                        if (ValidationHelper.Instance.IsValid)
                        {
                            Domain.Pessoas.FotoEstabelecimentoRepository.SaveNew(foto);
                        }
                    }
                }
            }
            else if (foto.TipoFoto == TipoFoto.EstabelecimentoLogo ||
                     foto.TipoFoto == TipoFoto.FotoServicoCategoriaEstabelecimento)
            {
                RemoverFotosFisicas(foto);
            }

            if (arquivo != null)
            {
                if (ValidationHelper.Instance.IsValid)
                {
                    var path = foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original);
                    Domain.Fotos.ImagensService.GravarArquivoDeImagem(arquivo.InputStream, path);
                }
            }
        }

        private Image RedimensionarImagemMantendoAspectoCompletandoImagemDeFundo(Image original, Size tamanhoDestino)
        {
            var tamanhoOriginal = new Size(original.Width, original.Height);
            var novoTamanho = ObterTamanhoEscalandoPorAltura(tamanhoOriginal, tamanhoDestino);

            var imagem = new Bitmap(tamanhoDestino.Width, tamanhoDestino.Height, PixelFormat.Format24bppRgb);
            imagem.SetResolution(original.HorizontalResolution, original.VerticalResolution);
            var composicao = Graphics.FromImage(imagem);
            composicao.Clear(Color.White);
            composicao.InterpolationMode = InterpolationMode.HighQualityBicubic;

            var sobraW = (tamanhoDestino.Width - novoTamanho.Width) / 2;
            var sobraH = (tamanhoDestino.Height - novoTamanho.Height) / 2;

            composicao.DrawImage(original, new Rectangle(sobraW, sobraH, novoTamanho.Width, novoTamanho.Height),
                     new Rectangle(0, 0, tamanhoOriginal.Width, tamanhoOriginal.Height), GraphicsUnit.Pixel);

            composicao.Dispose();

            return imagem;
        }

        private Image RedimensionarImagemMantendoAspectoSemCompletarImagemDeFundo(Image original, Size tamanhoDestino)
        {
            int larguraOrigem = original.Width;
            int alturaOrigem = original.Height;

            if (tamanhoDestino.Width == 0)
            {
                var tamanhoOriginal = new Size(larguraOrigem, alturaOrigem);
                tamanhoDestino = ObterTamanhoEscalandoPorAltura(tamanhoOriginal, tamanhoDestino);
            }

            float nPercent = 0;
            float nPercentW = 0;
            float nPercentH = 0;

            nPercentW = ((float)tamanhoDestino.Width / (float)larguraOrigem);
            nPercentH = ((float)tamanhoDestino.Height / (float)alturaOrigem);

            if (nPercentH < nPercentW)
                nPercent = nPercentH;
            else
                nPercent = nPercentW;

            int larguraDestino = (int)(larguraOrigem * nPercent);
            int alturaDestino = (int)(alturaOrigem * nPercent);

            var imagem = new Bitmap(tamanhoDestino.Width, tamanhoDestino.Height, PixelFormat.Format24bppRgb);
            imagem.SetResolution(original.HorizontalResolution, original.VerticalResolution);

            var composicao = Graphics.FromImage(imagem);
            composicao.InterpolationMode = InterpolationMode.HighQualityBicubic;
            composicao.Clear(Color.White);

            composicao.DrawImage(original, 0, 0, larguraDestino, alturaDestino);

            composicao.Dispose();

            return imagem;
        }

        private Image RedimensionarImagemRealizandoCrop(Image original, Size tamanhoDestino)
        {
            var tamanhoOriginal = new Size(original.Width, original.Height);

            const int sourceX = 0;
            const int sourceY = 0;
            int destX = 0, destY = 0;
            float nPercent;

            var nPercentW = (tamanhoDestino.Width / ((float)(tamanhoOriginal.Width)));
            var nPercentH = (tamanhoDestino.Height / ((float)(tamanhoOriginal.Height)));

            if (nPercentH < nPercentW)
            {
                nPercent = nPercentW;
                destY = ((int)(((tamanhoDestino.Height - (tamanhoOriginal.Height * nPercent)) / 2)));
            }
            else
            {
                nPercent = nPercentH;
                destX = ((int)(((tamanhoDestino.Width - (tamanhoOriginal.Width * nPercent)) / 2)));
            }

            var destWidth = ((int)((tamanhoOriginal.Width * nPercent)));
            var destHeight = ((int)((tamanhoOriginal.Height * nPercent)));

            var imagem = new Bitmap(tamanhoDestino.Width, tamanhoDestino.Height, PixelFormat.Format24bppRgb);
            imagem.SetResolution(original.HorizontalResolution, original.VerticalResolution);
            var composicao = Graphics.FromImage(imagem);
            composicao.InterpolationMode = InterpolationMode.HighQualityBicubic;
            composicao.DrawImage(original, new Rectangle(destX - 1, destY - 1, destWidth + 2, destHeight + 2),
                new Rectangle(sourceX, sourceY, tamanhoOriginal.Width, tamanhoOriginal.Height), GraphicsUnit.Pixel);
            composicao.Dispose();
            return imagem;
        }

        private void RemoverFotosFisicas(Foto foto)
        {
            Domain.Fotos.ImagensService.LimparTodosOsTamanhos(foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original));
        }

        private String RemoverNomeFotoCaminhoFisico(String caminhoFisico)
        {
            var caminhoFisicoSplittado = caminhoFisico.Split('\\').ToList();
            var nomeDoArquivo = caminhoFisicoSplittado.LastOrDefault(f => f.Contains("."));

            if (nomeDoArquivo == null)
                return caminhoFisico;

            caminhoFisicoSplittado.Remove(nomeDoArquivo);
            return String.Join("\\", caminhoFisicoSplittado);
        }

        private void ValidarArquivo(SolicitacaoDeUploadDeFoto arquivo)
        {
            ValidarEnvioArquivo(arquivo.ContentLength);
            ValidarExtensaoArquivo(arquivo.FileName);
            ValidarTamanhoArquivo(arquivo.ContentLength);
        }

        private void ValidarArquivoEstabelecimento(SolicitacaoDeUploadDeFoto arquivo)
        {
            ValidarEnvioArquivo(arquivo.ContentLength);
            ValidarExtensaoArquivo(arquivo.FileName, arquivo.ContentType);
            ValidarTamanhoArquivo(arquivo.ContentLength);
        }

        private void ValidarArquivoFotoProfissional(HttpPostedFileBase arquivo)
        {
            ValidarEnvioArquivo(arquivo);
            ValidarExtensaoArquivo(arquivo.FileName, arquivo.ContentType);
            ValidarTamanhoArquivo(arquivo);
        }

        private void ValidarArquivoFotoProfissional(HttpPostedFile arquivo)
        {
            if (arquivo != null && arquivo.ContentLength == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoNaoEnviado);
            }

            ValidarExtensaoArquivo(arquivo.FileName, arquivo.ContentType);

            var tamanhoMaximo = ConfiguracoesTrinks.Fotos.TamanhoMaximoUploadFoto;
            if (arquivo.ContentLength > tamanhoMaximo)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoInvalida);

            var tamanhoMinimo = ConfiguracoesTrinks.Fotos.TamanhoMinimoUploadFoto;
            if (arquivo.ContentLength < tamanhoMinimo)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoInvalida);
        }

        private void ValidarArquivoLogo(HttpPostedFileBase arquivo)
        {
            ValidarEnvioArquivo(arquivo);
            ValidarExtensaoArquivo(arquivo.FileName, arquivo.ContentType);
            ValidarTamanhoArquivo(arquivo);
        }

        private void ValidarEGravar(Foto foto, HttpPostedFileBase arquivo)
        {
            if (ValidationHelper.Instance.IsValid)
            {
                if (foto.TipoFoto == TipoFoto.FotoPessoa)
                {
                    if (foto is FotoEstabelecimento)
                    {
                        var fotoPessoa = (FotoEstabelecimento)foto;
                        if (!fotoPessoa.Codigo.HasValue)
                        {
                            ValidarQuantidadeDeFotos(fotoPessoa.Pessoa);

                            if (ValidationHelper.Instance.IsValid)
                            {
                                if (Domain.Pessoas.FotoEstabelecimentoRepository.PessoaPossuiFoto(fotoPessoa.Pessoa))
                                    fotoPessoa.Principal = true;

                                Domain.Pessoas.FotoEstabelecimentoRepository.SaveNew(foto);
                            }
                        }
                        else Domain.Pessoas.FotoEstabelecimentoRepository.Update(fotoPessoa);
                    }
                    if (foto is FotoPessoa)
                    {
                        var fotoPessoa = (FotoPessoa)foto;
                        if (fotoPessoa.Codigo.HasValue)
                            Domain.Pessoas.FotoPessoaRepository.Update(fotoPessoa);
                        else
                        {
                            fotoPessoa.Principal = true;
                            Domain.Pessoas.FotoPessoaRepository.SaveNew(fotoPessoa);
                        }
                    }
                }

                if (arquivo != null)
                {
                    if (ValidationHelper.Instance.IsValid)
                    {
                        var path = foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original);

                        Domain.Fotos.ImagensService.LimparTodosOsTamanhos(path);
                        Domain.Fotos.ImagensService.GravarArquivoDeImagem(arquivo.InputStream, path);
                    }
                }
            }
        }

        private void ValidarEGravar(Foto foto, HttpPostedFile arquivo)
        {
            if (ValidationHelper.Instance.IsValid)
            {
                if (foto.TipoFoto == TipoFoto.FotoPessoa)
                {
                    if (foto is FotoEstabelecimento)
                    {
                        var fotoPessoa = (FotoEstabelecimento)foto;
                        if (!fotoPessoa.Codigo.HasValue)
                        {
                            ValidarQuantidadeDeFotos(fotoPessoa.Pessoa);

                            if (ValidationHelper.Instance.IsValid)
                            {
                                if (Domain.Pessoas.FotoEstabelecimentoRepository.PessoaPossuiFoto(fotoPessoa.Pessoa))
                                    fotoPessoa.Principal = true;

                                Domain.Pessoas.FotoEstabelecimentoRepository.SaveNew(foto);
                            }
                        }
                        else Domain.Pessoas.FotoEstabelecimentoRepository.Update(fotoPessoa);
                    }
                    if (foto is FotoPessoa)
                    {
                        var fotoPessoa = (FotoPessoa)foto;
                        if (fotoPessoa.Codigo.HasValue)
                            Domain.Pessoas.FotoPessoaRepository.Update(fotoPessoa);
                        else
                        {
                            fotoPessoa.Principal = true;
                            Domain.Pessoas.FotoPessoaRepository.SaveNew(fotoPessoa);
                        }
                    }
                }

                if (arquivo != null)
                {
                    if (ValidationHelper.Instance.IsValid)
                    {
                        var path = foto.ObterCaminhoFisico(DimemsoesFotosEnum.Original);

                        Domain.Fotos.ImagensService.LimparTodosOsTamanhos(path);
                        Domain.Fotos.ImagensService.GravarArquivoDeImagem(arquivo.InputStream, path);
                    }
                }
            }
        }

        private void ValidarEnvioArquivo(HttpPostedFileBase arquivo)
        {
            if (arquivo != null && arquivo.ContentLength == 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoNaoEnviado);
            }
        }

        private void ValidarEnvioArquivo(int contentLength)
        {
            if (contentLength <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoNaoEnviado);
        }

        private void ValidarExtensaoArquivo(string fileName, string contentType = null)
        {
            var separador = " ".ToCharArray();
            var extensoes = ConfiguracoesTrinks.Fotos.ExtensoesUploadFoto.Split(separador);

            var valido = false;

            if (contentType != null && extensoes.Any(f => contentType.Contains(f)))
                valido = true;

            if (!valido && ValidarExtensaoNomeArquivo(fileName))
                valido = true;

            if (!valido)
                ValidationHelper.Instance.AdicionarItemValidacao($"O arquivo deve estar nos formatos {string.Join(", ", extensoes).ToUpper()}.");
        }

        private bool ValidarExtensaoNomeArquivo(string fileName)
        {
            var separador = " ".ToCharArray();
            var extensoes = ConfiguracoesTrinks.Fotos.ExtensoesUploadFoto.Split(separador);
            var splitedFilename = fileName.Split(".".ToCharArray());
            var extensaoArquivo = splitedFilename[splitedFilename.Length - 1].ToLower();

            if (!extensoes.Contains(extensaoArquivo))
                return false;

            return true;
        }

        private void ValidarTamanhoArquivo(HttpPostedFileBase arquivo)
        {
            var tamanhoMaximo = ConfiguracoesTrinks.Fotos.TamanhoMaximoUploadFoto;
            if (arquivo.ContentLength > tamanhoMaximo)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoInvalida);

            var tamanhoMinimo = ConfiguracoesTrinks.Fotos.TamanhoMinimoUploadFoto;
            if (arquivo.ContentLength < tamanhoMinimo)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoInvalida);
        }

        private void ValidarTamanhoArquivo(int contentLength)
        {
            var tamanhoMaximo = ConfiguracoesTrinks.Fotos.TamanhoMaximoUploadFoto;
            if (contentLength > tamanhoMaximo)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoInvalida);

            var tamanhoMinimo = ConfiguracoesTrinks.Fotos.TamanhoMinimoUploadFoto;
            if (contentLength < tamanhoMinimo)
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoInvalida);
        }
    }
}