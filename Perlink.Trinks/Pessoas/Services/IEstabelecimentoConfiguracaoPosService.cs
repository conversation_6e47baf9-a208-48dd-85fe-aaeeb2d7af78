﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas.DTO;

namespace Perlink.Trinks.Pessoas.Services {
    public interface IEstabelecimentoConfiguracaoPosService: IService {
        TipoPosDto ObterTipoPosPorEstabelecimento(int idEstabelecimento);
        EstabelecimentoConfiguracaoPosDto ObterConfiguracaoDoPosPorEstabelecimento(int idEstabelecimento);
        void AtivarSplitDePagamentoParaEstabelecimentoSiclos(int idEstabelecimento);
    }
}
