﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.Pessoas.Services
{

    public class FranquiaService : BaseService, IFranquiaService
    {

        public string ObterUrlParaDownloadAppTrinksPorFranquia(bool ehAndroid, bool ehIOS, string identificadorDownloadApp)
        {

            string urlDownloadApp = ConfiguracoesTrinks.EnvioEmail.UrlBase;

            var franquia = Domain.Pessoas.FranquiaRepository.ObterFranquiaPorIdentificadorDownloadApp(identificadorDownloadApp);

            if (franquia != null)
            {
                if (ehAndroid)
                {
                    urlDownloadApp = franquia.UrlDownloadAppAndroid;
                }
                else if (ehIOS)
                {

                    urlDownloadApp = franquia.UrlDownloadAppIOS;
                }
            }
            else
            {
                urlDownloadApp = Domain.Pessoas.EstabelecimentoService.ObterUrlParaDownloadAppTrinks(ehAndroid, ehIOS);
            }

            return urlDownloadApp;
        }

        public string ObterUrlTemaFrameBusca(int idFranquia, string urlAtual)
        {
            string UrlTemaFrameBuscaDoS3 = "";
            if (idFranquia != 0)
            {
                var Franquia = Domain.Pessoas.FranquiaRepository.Load(idFranquia);
                if (Franquia.PossuiTemaFrameBusca)
                {
                    var bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.personalizacao_frame_busca).ObterValor();
                    if (!urlAtual.ToUpper().Contains("LOCALHOST"))
                    {
                        UrlTemaFrameBuscaDoS3 = bucket + "tema" + idFranquia.ToString() + ".min.css";
                    }
                    else
                    {
                        UrlTemaFrameBuscaDoS3 = "~/Areas/HotSiteV2/Content/estilo/framebusca_temas/tema" + idFranquia.ToString() + "/tema" + idFranquia.ToString() + ".min.css";
                    }
                }
            }

            return UrlTemaFrameBuscaDoS3;
        }

        public void SaveOrUpdate(FranquiaDTO franquiaDTO)
        {
            Franquia franquia = null;
            bool nomeFoiAlterado = false;
            bool ehNovafranquia = false;

            if (franquiaDTO.IdFranquia > 0)
            {
                franquia = Domain.Pessoas.FranquiaRepository.Load(franquiaDTO.IdFranquia.Value);
                nomeFoiAlterado = franquia.Nome != franquiaDTO.NomeFranquia;
            }
            else
            {
                franquia = new Franquia();
                ehNovafranquia = true;
            }

            bool nomeJaEstaSendoUtilizado = (nomeFoiAlterado || ehNovafranquia) && Domain.Pessoas.FranquiaRepository.ExisteFranquiaAtivaComMesmoNome(franquiaDTO.NomeFranquia);
            if (nomeJaEstaSendoUtilizado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Nome já está sendo utilizado");
            }
            if (franquiaDTO.NomeFranquia.Length > 50)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Nome precisa ter no máximo 50 caracteres");
            }

            var slugAlterado = !ehNovafranquia && franquia.Slug != franquiaDTO.Slug;

            if (string.IsNullOrEmpty(franquiaDTO.Slug) || franquiaDTO.Slug.Length > 50)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Slug é obrigatório e precisa ter no máximo 50 caracteres");
            }

            Regex padraoSlug = new Regex("^[a-zA-Z0-9-]+$"); // Expressão regular que aceita letras, números e traços
            if (!padraoSlug.IsMatch(franquiaDTO.Slug))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O campo Slug deve conter apenas letras, números e traços(-).");
            }

            if ((ehNovafranquia || slugAlterado) && Domain.Pessoas.FranquiaRepository.ExisteFranquiaComMesmoSlug(franquiaDTO.Slug))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Slug já está sendo utilizado");
            }

            if (!ValidationHelper.Instance.IsValid)
                return;


            franquia.Nome = franquiaDTO.NomeFranquia;
            franquia.Slug = franquiaDTO.Slug.ToLower();

            franquia.Ativo = franquiaDTO.FranquiaAtiva;
            franquia.CadastrarComCobrancaManual = franquiaDTO.CadastrarComCobrancaManual;
            franquia.PlanoAssinaturaPadrao = franquiaDTO.AssinaturaPadrao;
            franquia.AceitaPreCadastroClienteNoFrame = franquiaDTO.PreCadastroClienteFrame;
            franquia.ExibirMensagemSolicitacaoAparicaoPortal = franquiaDTO.ExibeMensagemSolicitacaoAparicaoPortal;

            franquia.HabilitaUsoNFSe = franquiaDTO.HabilitaUsoNFSe;
            franquia.HabilitaUsoNFCe = franquiaDTO.HabilitaUsoNFCe;
            franquia.AlertarAusenciaDeDadosDoClienteParaProgramaDeFidelidade = franquiaDTO.AlertaDadosNecessariosFidelidade;
            franquia.PermiteAlteracaoDeValores = franquiaDTO.PermiteAlterarValores;
            franquia.PermiteAlterarLogo = franquiaDTO.PermiteAlterarLogo;
            franquia.HabilitaAlteracaoDeFotos = franquiaDTO.PermiteAlterarFotos;
            franquia.PermiteAlterarEstoqueMinimo = franquiaDTO.FazSincroniaDoEstoqueMinimoDeAcordoComAFranquia;
            franquia.PermiteAlterarDadosFiscais = franquiaDTO.FazSincroniaDoDadosFiscaisDeAcordoComAFranquia;
            franquia.PermiteAlterarValorDeCompra = franquiaDTO.FazSincroniaDoValorDeCompraDeAcordoComAFranquia;
            franquia.PermitePedirQuantidadeAbaixoDaSugestao = franquiaDTO.PermitePedirQuantidadeSugestao;
            franquia.HabilitaPedidosDeCompra = franquiaDTO.HabilitaPedidosDeCompra;
            franquia.HabilitaAlteracaoDeNomeFantasia = franquiaDTO.PermiteAlterarNomeFantasiaUnidade;
            franquia.RealizaTransferenciaDeProdutosEntreEstoques = franquiaDTO.RealizaTransferenciaProdutos;
            franquia.PermiteAlterarServicosDosProfissionais = franquiaDTO.PermiteAlterarServicosProfissionais;
            franquia.PermiteTransferenciaDePontosDeFidelidadeEntreUnidades = franquiaDTO.PermiteTransferenciaDePontosDeFidelidadeEntreUnidades;
            franquia.PermiteContratarItensAdicionaisNoPlano = franquiaDTO.PermiteContratarItensAdicionaisNoPlano;
            franquia.CompartilhaClientes = franquiaDTO.CompartilhaClientes;
            franquia.PermiteExibirClassificacaoDoProduto = franquiaDTO.ExibeClassificacaoProduto;
            franquia.PermiteExibicaoDoValorDeServicoEhProduto = franquiaDTO.PermiteExibicaoDoValorDeServicoEhProduto;
            franquia.PermiteQueProfissionaisVisualizemSuaPropriaAgenda = franquiaDTO.PermiteQueProfissionaisVisualizemSuaPropriaAgenda;
            franquia.PermiteEnvioDeLembreteSmsGratuito = franquiaDTO.PermiteEnvioDeLembreteSmsGratuito;
            franquia.PermiteAtivarEnvioDeSmsAniversario = franquiaDTO.PermiteAtivarEnvioDeSmsAniversario;
            franquia.LimitaAlteracaDeDataDosFechamentosContaParaOutrosMeses = franquiaDTO.LimitaAlteracaDeDataDosFechamentosContaParaOutrosMeses;
            franquia.LimitaEstornoDosFechamentosContaParaOutrosMeses = franquiaDTO.LimitaEstornoDosFechamentosContaParaOutrosMeses;
            franquia.ExigeSenhaAoRegistrarUsoInterno = franquiaDTO.ExigeSenhaAoRegistrarUsoInterno;
            franquia.PermiteSolicitarAssinaturaAoCliente = franquiaDTO.PermiteSolicitarAssinaturaAoCliente;
            franquia.PermiteEnviarRespostaAnamnesePorEmail = franquiaDTO.PermiteEnviarRespostaAnamnesePorEmail;
            franquia.PermiteProfissionalAltereProprioNome = franquiaDTO.PermiteProfissionalAltereProprioNome;


            if (franquia.Id > 0)
                Domain.Pessoas.FranquiaRepository.Update(franquia);
            else
                Domain.Pessoas.FranquiaRepository.SaveNew(franquia);
        }

        public bool DeveOcultarAgendamentosProfissional2()
        {
            var idEstabelecimento = Domain.WebContext.IdEstabelecimentoAutenticado;
            var idFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdFranquiaPorIdEstabelecimento((int)idEstabelecimento);

            var OcultaAgendamentosParaPerfilProfissional2 = Domain.Pessoas.FranquiaRepository.ObterValorConfiguracaoOcultaAgendamentosPerfilProfissional2(idFranquia);

            int idConta = Domain.WebContext.IdContaAutenticada.Value;
            var conta = Domain.Pessoas.ContaRepository.Load(idConta);

            var usuarioEstabelecimento = Domain.Pessoas.UsuarioEstabelecimentoRepository.ObterPorPessoaFisica(conta.Pessoa.IdPessoa, (int)idEstabelecimento);

            var perfil = Domain.Permissoes.UsuarioPerfilRepository.ObterPerfilUsuario(usuarioEstabelecimento.IdUsuarioEstabelecimento);

            if (OcultaAgendamentosParaPerfilProfissional2 && perfil == UsuarioPerfilEnum.Profissional2)
            {
                return true;
            }

            return false;
        }


        public bool AlterarPermissaoParaContratarItensAdicionaisNoPlanoPorIdEstabelecimento(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (estabelecimento == null || estabelecimento.FranquiaEstabelecimento == null)
                return false;

            var franquia = estabelecimento.FranquiaEstabelecimento.Franquia;
            franquia.PermiteContratarItensAdicionaisNoPlano = !franquia.PermiteContratarItensAdicionaisNoPlano;
            Domain.Pessoas.FranquiaRepository.Update(franquia);
            return true;
        }

        public bool EstabelecimentoPodeContratarItensAdicionaisNoPlano(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            if (estabelecimento == null || estabelecimento.FranquiaEstabelecimento == null)
                return false;

            var franquia = estabelecimento.FranquiaEstabelecimento.Franquia;
            return franquia.PermiteContratarItensAdicionaisNoPlano;
        }
    }
}