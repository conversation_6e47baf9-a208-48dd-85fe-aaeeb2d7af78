﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Pessoas.VO;
using System;
using System.Collections.Generic;
using System.Linq;
using Trinks.Pro.Api.Dtos.Cliente;

namespace Perlink.Trinks.Pessoas.Services
{

    public class DadosCadastraisDoClienteEstabelecimentoService : BaseService, IDadosCadastraisDoClienteEstabelecimentoService
    {

        [TransactionInitRequired]
        public ClienteEstabelecimento SalvarClienteEstabelecimento(ClienteEstabelecimentoDTO clienteEstabelecimentoDTO)
        {
            ClienteEstabelecimento clienteEstabelecimento;
            if (clienteEstabelecimentoDTO.Id > 0)
            { //Caso o ClienteEstabelecimento Já exista
                clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(clienteEstabelecimentoDTO.Id);

                if (clienteEstabelecimento.Estabelecimento.IdEstabelecimento != clienteEstabelecimentoDTO.IdEstabelecimento)
                    return null;
            }
            else
            { //Cadastro de novo ClienteEstabelecimento
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(clienteEstabelecimentoDTO.IdEstabelecimento);

                //Campos Exclusivos Criacao
                clienteEstabelecimento = new ClienteEstabelecimento
                {
                    Estabelecimento = estabelecimento
                };

                if (clienteEstabelecimentoDTO.Cliente.Id > 0)
                    clienteEstabelecimento.Cliente = Domain.Pessoas.ClienteService.Obter(clienteEstabelecimentoDTO.Cliente.Id);
                else
                {
                    clienteEstabelecimento.Cliente = new Cliente { PessoaFisica = new PessoaFisica(), TipoCliente = TipoClienteEnum.Balcao };
                }
            }

            AtualizarClienteEstabelecimento(clienteEstabelecimento, clienteEstabelecimentoDTO);

            Domain.Pessoas.ClienteEstabelecimentoService.ManterCliente(clienteEstabelecimento, unificar: false, enviaEmail: false);

            if (clienteEstabelecimentoDTO.RedesSociais != null)
                Domain.Pessoas.MidiaSocialService.ManterInstagram(clienteEstabelecimentoDTO.RedesSociais, clienteEstabelecimento);

            return clienteEstabelecimento;
        }

        private void AtualizarClienteEstabelecimento(ClienteEstabelecimento clienteEstabelecimento, ClienteEstabelecimentoDTO clienteEstabelecimentoDTO)
        {
            var estabelecimento = clienteEstabelecimento.Estabelecimento;

            //checar se foi remarcado para receber email marketing mesmo se que o cliente tenha cancelado
            if (clienteEstabelecimentoDTO.RecebeEmailMarketing)
                clienteEstabelecimento.DataCancelamentoRecebimentoEmailMarketingPeloCliente = null; //zera a data de cancelamento, ja que o estabelecimento reabilitou
            else if (clienteEstabelecimento.DataCancelamentoRecebimentoEmailMarketingPeloCliente == null)
                clienteEstabelecimento.DataCancelamentoRecebimentoEmailMarketingPeloCliente = DateTime.Now;

            clienteEstabelecimento.Observacoes = clienteEstabelecimentoDTO.Observacoes;
            clienteEstabelecimento.RecebeSMSMarketing = clienteEstabelecimentoDTO.RecebeSMSMarketing;
            clienteEstabelecimento.RecebeEmailMarketing = clienteEstabelecimentoDTO.RecebeEmailMarketing;
            clienteEstabelecimento.Ativo = true;

            clienteEstabelecimento.EnviarEmailAgendamentoCliente = clienteEstabelecimentoDTO.RecebeEmailLembreteDeAgendamento;
            clienteEstabelecimento.ComoNosConheceu = clienteEstabelecimentoDTO.IdComoNosConheceu != 0
                ? Domain.Pessoas.ComoConheceuRepository.Load(clienteEstabelecimentoDTO.IdComoNosConheceu)
                : null;

            if (estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarNotificacaoParaClientes &&
                estabelecimento.EstabelecimentoConfiguracaoGeral.EnvioDeNotificacaoHabilitado)
                clienteEstabelecimento.RecebeNotificacao = clienteEstabelecimentoDTO.RecebeSMSLembreteDeAgendamento;

            if (clienteEstabelecimentoDTO.Cliente.Telefones != null)
            {
                if (Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Telefone) || clienteEstabelecimento.Codigo == 0)
                    AtualizarTelefones(clienteEstabelecimento, clienteEstabelecimentoDTO.Cliente.Telefones, clienteEstabelecimentoDTO.VersaoDoApp);
            }
            //Campos comuns

            AtualizarPessoaFisicaDoClienteEstabelecimento(clienteEstabelecimento, clienteEstabelecimentoDTO);
        }

        private void AtualizarPessoaFisicaDoClienteEstabelecimento(ClienteEstabelecimento clienteEstabelecimento, ClienteEstabelecimentoDTO clienteEstabelecimentoDTO)
        {
            var cliente = clienteEstabelecimento.Cliente;
            var pessoaFisica = cliente.PessoaFisica;

            pessoaFisica.Ativo = true;
            pessoaFisica.DataNascimento = clienteEstabelecimentoDTO.Cliente.DataNascimento;
            clienteEstabelecimento.DataNascimentoCliente = clienteEstabelecimentoDTO.Cliente.DataNascimento;

            if (Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Cpf) || string.IsNullOrWhiteSpace(pessoaFisica.Cpf))
                pessoaFisica.Cpf = clienteEstabelecimentoDTO.Cliente.CPF?.RemoverFormatacaoCPFeCPNJ();

            if (cliente.TipoCliente != TipoClienteEnum.Web)
            {
                if (string.IsNullOrWhiteSpace(pessoaFisica.Email) || Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Email))
                    pessoaFisica.Email = clienteEstabelecimentoDTO.Cliente.Email;

                pessoaFisica.NomeCompleto = clienteEstabelecimentoDTO.Cliente.Nome;
                pessoaFisica.Genero = clienteEstabelecimentoDTO.Cliente.Sexo;
                clienteEstabelecimento.NomeCompletoCliente = clienteEstabelecimentoDTO.Cliente.Nome;
                clienteEstabelecimento.Genero = clienteEstabelecimentoDTO.Cliente.Sexo;
            }
        }

        private void AtualizarTelefones(ClienteEstabelecimento clienteEstabelecimento, List<TelefoneClienteDTO> telefones, string versaoDoApp)
        {
            var cliente = clienteEstabelecimento.Cliente;
            var pessoaFisica = cliente.PessoaFisica;
            var novosTelefones = new List<Telefone>();
            if (pessoaFisica.Telefones == null)
                pessoaFisica.Telefones = new List<Telefone>();

            var telefonesQueNaoSaoDoEstabelecimento = pessoaFisica.Telefones.Where(f => f.Dono.IdPessoa != clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa).ToList();

            novosTelefones = ToTelefones(telefones, clienteEstabelecimento);

            var permiteVisualizarTelefoneInternacionalAppB2b = Domain.ControleDeFuncionalidades
                .DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(clienteEstabelecimento.Estabelecimento.IdEstabelecimento,
                Recurso.VisualizarTelefonesInternacionaisAppB2B).EstaDisponivel;

            var versaoMinima = new ParametrosTrinks<string>(ParametrosTrinksEnum.versao_minima_app_b2b_telefone_internacional).ObterValor();

            if (string.IsNullOrEmpty(versaoDoApp) || (!permiteVisualizarTelefoneInternacionalAppB2b || !Domain.Notificacoes.DispositivoService.VersaoEhMaiorOuIgual(versaoDoApp, versaoMinima)))
            {
                novosTelefones.AddRange(pessoaFisica.Telefones.Where(telefone => telefone.Ddi != DdiConstants.Brasil).ToList());
            }

            pessoaFisica.Telefones.Clear();
            telefonesQueNaoSaoDoEstabelecimento.ForEach(pessoaFisica.Telefones.Add);

            foreach (var t in novosTelefones)
            {
                if (pessoaFisica.Telefones.Any(f => f.Numero == t.Numero && f.DDD == t.DDD)) continue;
                pessoaFisica.Telefones.Add(t);
            }
        }

        private List<Telefone> ToTelefones(List<TelefoneClienteDTO> lista, ClienteEstabelecimento clienteEstabelecimento)
        {
            List<Telefone> retorno = new List<Telefone>();

            foreach (TelefoneClienteDTO item in lista)
            {
                var telefone = new Telefone();
                telefone.Pessoa = clienteEstabelecimento.Cliente.PessoaFisica;
                telefone.Dono = clienteEstabelecimento.Estabelecimento.PessoaJuridica;
                telefone.Tipo = (TipoTelefoneEnum)item.IdTipoDeTelefone;
                telefone.Ativo = true;
                telefone.DDD = item.DDD;
                telefone.Numero = item.Numero;

                retorno.Add(telefone);
            }

            return retorno;
        }
    }
}