﻿using Castle.ActiveRecord;
using NHibernate;
using NHibernate.Linq;
using NHibernate.Transform;
using Perlink.DomainInfrastructure.Facilities;
using Perlink.DomainInfrastructure.Services;
using Perlink.Shared.Encryptor;
using Perlink.Shared.Exceptions;
using Perlink.SuporteUsuario.DadosStone;
using Perlink.SuporteUsuario.DadosStone.DTO;
using Perlink.Trinks.ConciliacaoBelezinha.Dtos.V2;
using Perlink.Trinks.Estatistica;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Notificacoes.Enums;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.RecorrenciaDeAssinatura;
using Perlink.Trinks.Relatorios;
using Perlink.Trinks.RPS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trinks.Integracoes.Stone.Connect.Models;
using static Perlink.Trinks.PropertyNames.Web.Areas.BackOffice.Models;
using DE = Perlink.Trinks.Domain.Estatistica;
using DP = Perlink.Trinks.Domain.Pessoas;

namespace Perlink.Trinks.Pessoas.Services
{

    public class RotinasService : BaseService, IRotinasService
    {

        #region Rotina Dados Consolidado

        public void PopularDataLake()
        {
            LogService<RotinasService>.Info("Inicio popular DataLake");

            var session = Castle.ActiveRecord.ActiveRecordMediator.GetSessionFactoryHolder().CreateSession(typeof(Castle.ActiveRecord.ActiveRecordBase));
            NHibernate.IQuery query = session.CreateSQLQuery("exec AtualizarDataLakeStone");
            query.ExecuteUpdate();

            LogService<RotinasService>.Info("Fim popular DataLake");
        }

        public void EnviarDadosDataLake()
        {
            LogService<RotinasService>.Info("Inicio enviar dados do DataLake");

            var query = @"select * from DATA_LAKE_STONE";

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session = holder.CreateSession(typeof(ActiveRecordBase));
            IQuery iSQLQuery = session.CreateSQLQuery(query);
            var q = iSQLQuery.SetResultTransformer(Transformers.AliasToBean(typeof(ClientRequest)));

            var result = q.List<ClientRequest>().ToList();
            holder.ReleaseSession(session);

            var stoneDataLakeApiClient = new StoneDataLakeApiClient();

            var grupos = result.ChunkBy(5000);

            foreach (var g in grupos)
            {
                var clientResponse = stoneDataLakeApiClient.EnviarClientes(g);
                LogService<RotinasService>.Info("Retorno envio Data Lake - Status:" + clientResponse.statusCode.ToString() + " Itens Aceitos: " + clientResponse.accepted.ToString() + " Itens Rejeitados: " + clientResponse.errors.ToString());
            }
        }

        #endregion Rotina Dados Consolidado

        #region Rotina Dados Consolidado

        public void ExcluirRegistrosRelatorioConsolidadoDiaParaOsEstabelecimentosAPartirDe(Estabelecimento estabelecimento, DateTime aPartirDe)
        {
            var deleteParameters = new List<KeyValuePair<string, object>>();
            deleteParameters.Add(new KeyValuePair<string, object>("idEstabelecimento", estabelecimento.IdEstabelecimento));
            deleteParameters.Add(new KeyValuePair<string, object>("aPartirDia", aPartirDe.AddDays(-1).Date));

            Domain.Relatorios.ConsultaRelatorioConsolidadoDiaRepository.
                   Delete(" IdEstabelecimento = :idEstabelecimento " +
                              "and Dia > :aPartirDia ", deleteParameters);

            /*
            var listaConsultaRelatorioConsolidadoDia = Domain.Relatorios.ConsultaRelatorioConsolidadoDiaRepository.Queryable().Where(p => p.Dia > aPartirDe.AddDays(-1).Date).ToList();
            foreach (var consolidadoDia in listaConsultaRelatorioConsolidadoDia) {
                if (listaEstabelecimentos.Any(p => p.IdEstabelecimento == consolidadoDia.IdEstabelecimento)) {
                    Domain.Relatorios.ConsultaRelatorioConsolidadoDiaRepository.Delete(consolidadoDia);
                }
            }
            */
        }

        public void RotinaParaPreenchimentoDosDadosDeConsolidacao(int numeroDeEstabelecimentosParaTratar, int idEstabelecimento = 0)
        {
            var qtdMesesDadosConsolidados = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("qtd_meses_dados_consolidados").Valor;
            int.TryParse(qtdMesesDadosConsolidados, out int qtdMeses);
            var dataInicio = Calendario.Hoje().AddMonths(-qtdMeses);
            dataInicio = new DateTime(dataInicio.Year, dataInicio.Month, 1);
            var dataFim = Calendario.Hoje();

            var listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo = ListarEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo();
            var listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento = ListarEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento();

            var estabelecimentos = listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo;
            estabelecimentos.AddRange(listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento);
            estabelecimentos = estabelecimentos.GroupBy(p => p.IdEstabelecimento).Select(g => g.First()).ToList();

            if (numeroDeEstabelecimentosParaTratar > 0)
            {
                estabelecimentos = estabelecimentos.Take(numeroDeEstabelecimentosParaTratar).ToList();
            }

            if (idEstabelecimento > 0)
                estabelecimentos = estabelecimentos.Where(f => f.IdEstabelecimento == idEstabelecimento).ToList();

            Stack<Estabelecimento> pilhaDeEstabelecimentos = new Stack<Estabelecimento>(estabelecimentos);

            LogService<RotinasService>.Info("RotinaParaPreenchimentoDosDadosDeConsolidacao: " + pilhaDeEstabelecimentos.Count());

            while (pilhaDeEstabelecimentos.Count > 0)
            {
                try
                {
                    TratarConsolidadoDeUmEstabelecimento(dataInicio, dataFim, pilhaDeEstabelecimentos.Pop());
                }
                catch (Exception e)
                {
                    Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(e));
                    LogService<RotinasService>.Error("RotinaParaPreenchimentoDosDadosDeConsolidacao - " + e.Formatada());
                }
            }
        }

        public void Temp_RotinaParaPreenchimentoDosDadosDeConsolidacao(int numeroDeEstabelecimentosParaTratar)
        {
            var listaMesesJaSalvos = Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.Queryable().Select(f => new { f.Ano, f.Mes }).OrderBy(f => f.Ano).ThenBy(f => f.Mes);
            var ano = listaMesesJaSalvos.FirstOrDefault().Ano;
            var mes = listaMesesJaSalvos.FirstOrDefault().Mes;
            var dia = DateTime.DaysInMonth(listaMesesJaSalvos.FirstOrDefault().Ano, listaMesesJaSalvos.FirstOrDefault().Mes);
            DateTime dataFim = new DateTime(ano, mes, dia);
            DateTime dataInicio = new DateTime(dataFim.Year, dataFim.Month, 1);
            while (listaMesesJaSalvos.Any(f => f.Ano == dataFim.Year && f.Mes == dataFim.Month))
            {
                dataFim = dataFim.AddMonths(-1);
                dataInicio = new DateTime(dataFim.Year, dataFim.Month, 1);
            }

            var listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo = Temp_ListarEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo();
            var listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento = ListarEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento();

            var estabelecimentos = listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo;
            estabelecimentos.AddRange(listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento);
            estabelecimentos = estabelecimentos.GroupBy(p => p.IdEstabelecimento).Select(g => g.First()).ToList();

            if (numeroDeEstabelecimentosParaTratar > 0)
            {
                estabelecimentos = estabelecimentos.Take(numeroDeEstabelecimentosParaTratar).ToList();
            }

            Stack<Estabelecimento> pilhaDeEstabelecimentos = new Stack<Estabelecimento>(estabelecimentos);
            while (pilhaDeEstabelecimentos.Count > 0)
            {
                Temp_TratarConsolidadoDeUmEstabelecimento(dataInicio, dataFim, pilhaDeEstabelecimentos.Pop());
            }
        }

        private static DateTime APartirDe()
        {
            return Calendario.Agora().AddHours(-12);
        }

        private static string HqlWhereDelete()
        {
            return " IdEstabelecimento = :idEstabelecimento and ((Mes >= :aPartirDeMes and Ano = :aPartirDeAno) OR Ano > :aPartirDeAno) ";
        }

        private static List<KeyValuePair<string, object>> ParametrosParaExclusaoDosRegistrosAPartirDe(Estabelecimento estabelecimento, DateTime aPartirDe)
        {
            var deleteParameters = new List<KeyValuePair<string, object>>();
            deleteParameters.Add(new KeyValuePair<string, object>("idEstabelecimento", estabelecimento.IdEstabelecimento));
            deleteParameters.Add(new KeyValuePair<string, object>("aPartirDeMes", aPartirDe.Month));
            deleteParameters.Add(new KeyValuePair<string, object>("aPartirDeAno", aPartirDe.Year));
            return deleteParameters;
        }

        private void ExcluirRegistrosRelatorioConsolidadoClienteEstabelecimentoMesParaOsEstabelecimentosAPartirDe(Estabelecimento estabelecimento, DateTime aPartirDe)
        {
            Domain.Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository.
                Delete(HqlWhereDelete(), ParametrosParaExclusaoDosRegistrosAPartirDe(estabelecimento, aPartirDe));

            /*
            var listaConsultaRelatorioConsolidadoClienteEstabelecimentoMes = Domain.Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository.Queryable().Where(p => p.Mes >= aPartirDe.Month && p.Ano >= aPartirDe.Year).ToList();
            foreach (var consolidadoClienteEstabelecimentoMes in listaConsultaRelatorioConsolidadoClienteEstabelecimentoMes) {
                if (listaEstabelecimentos.Any(p => p.IdEstabelecimento == consolidadoClienteEstabelecimentoMes.IdEstabelecimento)) {
                    Domain.Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository.Delete(consolidadoClienteEstabelecimentoMes);
                }
            }
            */
        }

        private void ExcluirRegistrosRelatorioConsolidadoMesParaOsEstabelecimentosAPartirDe(Estabelecimento estabelecimento, DateTime aPartirDe)
        {
            Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.
                Delete(HqlWhereDelete(), ParametrosParaExclusaoDosRegistrosAPartirDe(estabelecimento, aPartirDe));

            /*
            var listaConsultaRelatorioConsolidadoMes = Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.Queryable().Where(p => p.Mes >= aPartirDe.Month && p.Ano >= aPartirDe.Year).ToList();
            foreach (var consolidadoMes in listaConsultaRelatorioConsolidadoMes) {
                if (listaEstabelecimentos.Any(p => p.IdEstabelecimento == consolidadoMes.IdEstabelecimento)) {
                    Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.DeleteNoFlush(consolidadoMes);
                }
            }
            Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.Flush();
            */
        }

        private void InserirDadosConsolidadoFaturamentoClienteEstabelecimentoMes(List<Estabelecimento> listaEstabelecimentos, DateTime dataInicio, DateTime dataFim)
        {
            foreach (var estabelecimento in listaEstabelecimentos)
            {
                var filtro = ObterFiltroRelatorioFinanceiro(estabelecimento, dataInicio, dataFim);

                var relatorioClienteEstabelecimentoMes = Domain.Financeiro.TransacaoRepository.ObterTransacoesParaConsolidadoClienteEstabelecimentoMes(filtro);

                var lista = new List<ConsultaRelatorioConsolidadoEstabelecimentoClienteMes>();

                foreach (var relatorioClienteEstabelecimento in relatorioClienteEstabelecimentoMes)
                {
                    //var clienteEstabelecimentoId = DP.ClienteEstabelecimentoRepository.ObterIdClienteEstabelecimentoPorPF(relatorioClienteEstabelecimento.IdPessoaQuePagou, estabelecimento.IdEstabelecimento);

                    var consolidadoFaturamentoClienteMes = new ConsultaRelatorioConsolidadoEstabelecimentoClienteMes()
                    {
                        Ano = relatorioClienteEstabelecimento.DataHoraInicioHorario.Value.Year,
                        Mes = relatorioClienteEstabelecimento.DataHoraInicioHorario.Value.Month,
                        IdEstabelecimento = estabelecimento.IdEstabelecimento,
                        IdEstabelecimentoCliente = relatorioClienteEstabelecimento.IdClienteEstabelecimento
                    };

                    lista.Add(consolidadoFaturamentoClienteMes);
                }

                foreach (var item in lista)
                {
                    Domain.Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository.SaveNewNoFlush(item);
                }
            }

            Domain.Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository.Flush();
            Domain.Relatorios.ConsultaRelatorioConsolidadoEstabelecimentoClienteMesRepository.Clear();
        }

        private void InserirDadosConsolidadoFaturamentoDia(List<Estabelecimento> listaEstabelecimentos, DateTime dataInicio, DateTime dataFim)
        {
            foreach (var estabelecimento in listaEstabelecimentos)
            {
                var filtro = ObterFiltroRelatorioFinanceiro(estabelecimento, dataInicio, dataFim);

                var relatorioDia = Domain.Financeiro.TransacaoRepository.ObterTransacoesParaSegundaLinhaDoRelatorio(filtro);
                foreach (var relatorio in relatorioDia)
                {
                    var dados = new ConsultaRelatorioConsolidadoDia();
                    dados.Dia = relatorio.DataHoraInicioHorario ?? Calendario.Hoje();
                    dados.IdEstabelecimento = estabelecimento.IdEstabelecimento;
                    dados.ValorCredito = relatorio.TotalPagoEmCredito ?? 0;
                    dados.ValorDebito = relatorio.TotalPagoEmDebito ?? 0;
                    dados.ValorDescontos = relatorio.Descontos ?? 0;
                    dados.ValorDinheiro = relatorio.TotalPagoEmDinheiro ?? 0;
                    dados.ValorOutros = relatorio.TotalPagoEmOutros ?? 0;
                    dados.ValorPacotes = relatorio.Pacotes ?? 0;
                    dados.ValorClube = relatorio.ClubeDeAssinaturas ?? 0;
                    dados.ValorProdutos = relatorio.Produtos ?? 0;
                    dados.ValorServicos = relatorio.SubTotal ?? 0;
                    dados.ValorPrePago = relatorio.TotalPagoEmPrePago;
                    dados.ValorCreditoCliente = relatorio.TotalPagoEmCreditoCliente;
                    dados.ValorValePresente = relatorio.TotalPagoEmValePresente;
                    dados.ValorTotal = (relatorio.TotalPago ?? 0) - (relatorio.Gorjeta ?? 0) + (relatorio.Troco ?? 0);
                    dados.ValorGorjeta = (relatorio.Gorjeta ?? 0);
                    dados.NumeroAtendimentos = relatorio.QuantidadeAtendimentos;
                    dados.NumeroClientes = relatorio.NumeroClientes;
                    dados.TicketMedio = relatorio.QuantidadeAtendimentos > 0 ? ((relatorio.TotalPagar ?? 0) / relatorio.QuantidadeAtendimentos) : 0;
                    dados.ValorDividasPagas = relatorio.TotalDividasPagas;
                    dados.ValorDividasDeixadas = relatorio.TotalDividasDeixadas;
                    dados.ValorPix = relatorio.TotalPix;
                    dados.ValorTroco = relatorio.Troco ?? 0;
                    dados.ValorDescontoCashback = relatorio.ValorDescontoCashback;

                    Domain.Relatorios.ConsultaRelatorioConsolidadoDiaRepository.SaveNewNoFlush(dados);
                }

                var estabelecimentoToUpdate = Domain.Pessoas.EstabelecimentoRepository.Load(estabelecimento.IdEstabelecimento);
                estabelecimentoToUpdate.EstabelecimentoConfiguracaoGeral.UltimaExecucaoDaRotinaDoConsolidado = Calendario.Agora();
                Domain.Pessoas.EstabelecimentoRepository.UpdateNoFlush(estabelecimentoToUpdate);
            }
            Domain.Relatorios.ConsultaRelatorioConsolidadoDiaRepository.Flush();
            Domain.Relatorios.ConsultaRelatorioConsolidadoDiaRepository.Clear();
        }

        private void InserirDadosConsolidadoFaturamentoMes(List<Estabelecimento> listaEstabelecimentos, DateTime dataInicio, DateTime dataFim)
        {
            foreach (var estabelecimento in listaEstabelecimentos)
            {
                var filtro = ObterFiltroRelatorioFinanceiro(estabelecimento, dataInicio, dataFim);

                var relatorioMes = Domain.Financeiro.TransacaoRepository.ObterTransacoesParaConsolidadoMes(filtro);
                foreach (var relatorio in relatorioMes)
                {
                    var consolidadoFaturamentoMes = new ConsultaRelatorioConsolidadoMes()
                    {
                        Ano = relatorio.DataHoraInicioHorario.Value.Year,
                        Mes = relatorio.DataHoraInicioHorario.Value.Month,
                        NumeroClientes = relatorio.NumeroClientes,
                        IdEstabelecimento = estabelecimento.IdEstabelecimento
                    };

                    Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.SaveNewNoFlush(consolidadoFaturamentoMes);
                }
            }
            Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.Flush();
            Domain.Relatorios.ConsultaRelatorioConsolidadoMesRepository.Clear();
        }

        private List<Estabelecimento> ListarEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento()
        {
            var estabelecimentosComContaFinanceiraEmStatusPositivo = DP.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual()
                .Where(p => p.EstabelecimentoConfiguracaoGeral.UltimaExecucaoDaRotinaDoConsolidado < APartirDe() || !p.EstabelecimentoConfiguracaoGeral.UltimaExecucaoDaRotinaDoConsolidado.HasValue)
                .Select(p => new Estabelecimento()
                {
                    IdEstabelecimento = p.IdEstabelecimento,
                    PessoaJuridica = new PessoaJuridica
                    {
                        IdPessoa = p.PessoaJuridica.IdPessoa,
                        NomeFantasia = p.PessoaJuridica.NomeFantasia
                    }
                });

            var listaUsuariosEstabelecimento = DP.UsuarioEstabelecimentoRepository.Queryable()
              .Where(p => p.Ativo && p.UsuarioPerfil.Perfil.Id == (int)UsuarioPerfilEnum.Administrador)
              .Where(f => estabelecimentosComContaFinanceiraEmStatusPositivo.Any(q => q.IdEstabelecimento == f.Estabelecimento.IdEstabelecimento))
              .Select(p => new
              {
                  Estabelecimento = new
                  {
                      p.Estabelecimento.IdEstabelecimento,
                      PessoaJuridica = new PessoaJuridica
                      {
                          IdPessoa = p.Estabelecimento.PessoaJuridica.IdPessoa,
                          NomeFantasia = p.Estabelecimento.PessoaJuridica.NomeFantasia
                      }
                  },
                  p.PessoaFisica.IdPessoa
              })
              .ToList();

            var listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento = listaUsuariosEstabelecimento
                .GroupBy(p => p.IdPessoa)
                .Where(f => f.Count() > 1)
                .SelectMany(f => f.Select(g => new { g.Estabelecimento.IdEstabelecimento, g.Estabelecimento.PessoaJuridica }))
                .Select(p => new Estabelecimento() { IdEstabelecimento = p.IdEstabelecimento, PessoaJuridica = p.PessoaJuridica }).ToList()
                .Distinct();

            listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento = listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento
                .ToList();

            return listaEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento.ToList();
        }

        private List<Estabelecimento> ListarEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo()
        {
            var estabelecimentosComContaFinanceiraEmStatusPositivoOuForaToleranciaOuCancelado = DP.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManualOuForaToleranciaOuCancelada();
            var listaIdsEstabelecimentoLigadosAFranquia = DP.FranquiaEstabelecimentoRepository.Queryable().Where(p => p.CompoeConsolidadoFranqueador && p.Ativo).Select(p => p.IdEstabelecimento);

            DateTime aPartirDe = APartirDe();
            var listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo = estabelecimentosComContaFinanceiraEmStatusPositivoOuForaToleranciaOuCancelado
                .Where(p => listaIdsEstabelecimentoLigadosAFranquia.Contains(p.IdEstabelecimento)
                        && (p.EstabelecimentoConfiguracaoGeral.UltimaExecucaoDaRotinaDoConsolidado.Value < aPartirDe
                               || !p.EstabelecimentoConfiguracaoGeral.UltimaExecucaoDaRotinaDoConsolidado.HasValue))
                .Select(p => new Estabelecimento() { IdEstabelecimento = p.IdEstabelecimento, PessoaJuridica = p.PessoaJuridica }).ToList()
                .Distinct()
                .ToList();

            return listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo;
        }

        private ParametrosFiltrosRelatorio ObterFiltroRelatorioFinanceiro(Estabelecimento estabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            return new ParametrosFiltrosRelatorio()
            {
                Estabelecimento = estabelecimento,
                DataInicial = dataInicio,
                DataFinal = dataFim,
                TipoData = TipoDataRelatorio.DataAtendimento,
                OrigemRelatorio = OrigemRelatorio.Consolidado,
            };
        }

        private List<Estabelecimento> Temp_ListarEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo()
        {
            var estabelecimentosComContaFinanceiraEmStatusPositivo = DP.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual();
            var listaIdsEstabelecimentoLigadosAFranquia = DP.FranquiaEstabelecimentoRepository.Queryable().Where(p => p.CompoeConsolidadoFranqueador && p.Ativo).Select(p => p.IdEstabelecimento);

            var listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo = estabelecimentosComContaFinanceiraEmStatusPositivo
                .Where(p => listaIdsEstabelecimentoLigadosAFranquia.Contains(p.IdEstabelecimento))
                .Select(p => new Estabelecimento() { IdEstabelecimento = p.IdEstabelecimento, PessoaJuridica = p.PessoaJuridica }).ToList()
                .Distinct()
                .ToList();

            return listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo;
        }

        private void Temp_TratarConsolidadoDeUmEstabelecimento(DateTime dataInicio, DateTime dataFim, Estabelecimento estabelecimento)
        {
            var estabelecimentos = new List<Estabelecimento>();
            estabelecimentos.Add(estabelecimento);

            var scope = new TransactionScope(TransactionMode.New);
            try
            {
                using (scope)
                {
                    InserirDadosConsolidadoFaturamentoMes(estabelecimentos, dataInicio, dataFim);
                    InserirDadosConsolidadoFaturamentoClienteEstabelecimentoMes(estabelecimentos, dataInicio, dataFim);
                    InserirDadosConsolidadoFaturamentoDia(estabelecimentos, dataInicio, dataFim);
                }
                scope.VoteCommit();
                Domain.Pessoas.EstabelecimentoRepository.Clear();
            }
            catch (Exception ex)
            {
                scope.VoteRollBack();
                throw new Exception(ex.Message, ex.InnerException);
            }
        }
        private void TratarConsolidadoDeUmEstabelecimento(DateTime dataInicio, DateTime dataFim, Estabelecimento estabelecimento)
        {
            LogService<RotinasService>.Info("TratarConsolidadoDeUmEstabelecimento: " + estabelecimento.NomeDeExibicaoNoPortal);

            // Escopo isolado para garantir limpeza completa entre execuções
            using (var scope = new TransactionScope(TransactionMode.New))
            {
                try
                {
                    var estabelecimentos = new List<Estabelecimento> {
                        estabelecimento
                    };

                    ExcluirRegistrosRelatorioConsolidadoMesParaOsEstabelecimentosAPartirDe(estabelecimento, dataInicio);
                    InserirDadosConsolidadoFaturamentoMes(estabelecimentos, dataInicio, dataFim);

                    ExcluirRegistrosRelatorioConsolidadoClienteEstabelecimentoMesParaOsEstabelecimentosAPartirDe(estabelecimento, dataInicio);
                    InserirDadosConsolidadoFaturamentoClienteEstabelecimentoMes(estabelecimentos, dataInicio, dataFim);

                    ExcluirRegistrosRelatorioConsolidadoDiaParaOsEstabelecimentosAPartirDe(estabelecimento, dataInicio);
                    InserirDadosConsolidadoFaturamentoDia(estabelecimentos, dataInicio, dataFim);

                    scope.VoteCommit();
                }
                catch (Exception)
                {
                    scope.VoteRollBack();
                    throw;
                }
            }
        }

        #endregion Rotina Dados Consolidado

        public void AplicarCriptografiaNosCertificadosDigitais()
        {
            var rotinaCriptografiaJaExecutouNoAmbiente = new ParametrosTrinks<bool>(ParametrosTrinksEnum.rotina_criptografia_executada).ObterValor();

            if (!rotinaCriptografiaJaExecutouNoAmbiente)
            {
                var listaPessoaJuridicaCertificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Queryable().Where(p => p.SenhaCertificado != null && p.SenhaCertificadoCriptografada == null).ToList();
                foreach (var pessoaJuridicaCertificadoDigital in listaPessoaJuridicaCertificadoDigital)
                {
                    var senhaCertificado = pessoaJuridicaCertificadoDigital.SenhaCertificado;

                    var criptografia = new Criptografia(Criptografia.CryptoTypes.encTypeTripleDES);
                    var senhaCriptografada = criptografia.Encrypt(senhaCertificado);
                    pessoaJuridicaCertificadoDigital.SenhaCertificadoCriptografada = senhaCriptografada;
                    Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.UpdateNoFlush(pessoaJuridicaCertificadoDigital);
                }

                Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.Flush();
            }
            else
            {
                throw new Exception("Rotina já executada neste ambiente.");
            }
        }

        [Obsolete("Substituido pelo método EnviarEmailsDoResumoDoFechamentoEhAgendamentosParaTodosEstabelecimentos()")]
        public void EnviarEmailAgendamentosDeAmanha()
        {
            var data = Calendario.Hoje();
            var estabelecimentos =
                DP.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual();

            foreach (var estabelecimento in estabelecimentos)
            {
                var estabelecimento1 = estabelecimento; // correção de bug do .NET
                var agendamentos = DP.HorarioRepository.Queryable().Where(f =>
                    f.Estabelecimento == estabelecimento1 &&
                    f.DataInicio >= data.AddDays(-1) &&
                    f.DataInicio <= data.AddDays(15)).OrderBy(f => f.DataInicio);
                if (!agendamentos.Any()) continue;
                DP.EnvioEmailService.EnviarEmailProximosAgendamentos(estabelecimento, agendamentos, data);
            }
        }

        public void EnviarEmailsDoResumoDoFechamentoEhAgendamentosParaTodosEstabelecimentos(int? idEstabelecimentoTeste = null)
        {
            var dataDoEnvio = Calendario.Hoje();
            var estabelecimentoRepository = Domain.Pessoas.EstabelecimentoRepository;

            LogService<RotinasService>.Info("============ Início DadosDeAgendamentosDosEstabelecimentosService.ObterDadosDoEmailResumoDoFechamentoEAgendamentos ================");
            var dadosDosEmails = Domain.Pessoas.DadosDeAgendamentosDosEstabelecimentosService.ObterDadosDoEmailResumoDoFechamentoEAgendamentos(dataDoEnvio, idEstabelecimentoTeste);
            LogService<RotinasService>.Info("============ Fim DadosDeAgendamentosDosEstabelecimentosService.ObterDadosDoEmailResumoDoFechamentoEAgendamentos ================");

            foreach (var dadosDoEmail in dadosDosEmails)
            {
                LogService<RotinasService>.Info("============ Início loop dadosDosEmails: " + dadosDoEmail.NomeDoEstabelecimento + " ================");
                var agendamentos = DP.HorarioRepository.Queryable().Where(f =>
                    f.Estabelecimento.IdEstabelecimento == dadosDoEmail.EstabelecimentoDTO.IdEstabelecimento &&
                    f.DataInicio >= dataDoEnvio.AddDays(-1) &&
                    f.DataInicio <= dataDoEnvio.AddDays(15)).OrderBy(f => f.DataInicio);

                DP.EnvioEmailService.EnviarEmailParaOEstabelecimentoDoResumoDoFechamentoEAgendamentos(dadosDoEmail, agendamentos, dataDoEnvio);

                LogService<RotinasService>.Info("============ Fim loop dadosDosEmails: " + dadosDoEmail.NomeDoEstabelecimento + " ================");
                Domain.Pessoas.EstabelecimentoRepository.Clear();
            }
        }

        public void EnviarEmailDespesasDoEstabelecimento(int? idEstabelecimento = null)
        {
            var data = Calendario.Agora().AddDays(7).Date;

            var estabelecimentos = DP.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual();

            if (idEstabelecimento.HasValue)
            { //Parametro para área de teste
                estabelecimentos = estabelecimentos.Where(e => e.IdEstabelecimento == idEstabelecimento.Value);
            }

            var emailsComIdEstabelecimento = Domain.Pessoas.EnvioEmailService.ObterEmailsResponsaveisDosEstabelecimentos(estabelecimentos, NotificacaoEnum.DespesasSemanal);

            if (estabelecimentos.Any(p => p.IdEstabelecimento == 30394))
            {
                LogService<EnvioEmailService>.Info("O estabelecimento " +
                                                estabelecimentos.Where(p => p.IdEstabelecimento == 30394).FirstOrDefault().NomeDeExibicaoNoPortal + " esta na lista.");
            }
            else
            {
                LogService<EnvioEmailService>.Info("O estabelecimento não foi encontrado na lista.");
            }

            foreach (var estabelecimento in estabelecimentos)
            {
                LogService<EnvioEmailService>.Info("Inicio da verificação para o envio do email de despesas do estabelecimento " +
                   estabelecimento.NomeDeExibicaoNoPortal);

                if (!estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailDespesasHabilitado)
                    continue;

                var emailsDestinatarios = emailsComIdEstabelecimento.Where(e => e.IdEstabelecimento == estabelecimento.IdEstabelecimento).Select(e => e.MailAddress()).ToList();

                if (!emailsDestinatarios.Any())
                    continue;

                var despesas = Domain.Despesas.LancamentoRepository.Queryable().Where(f =>
                    f.Ativo &&
                    f.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento
                    && f.DataPagamento == null
                    && f.DataVencimento >= Calendario.Agora().Date
                    && f.DataVencimento <= data).OrderBy(f => f.DataVencimento).ToList();
                var estabelecimento1 = estabelecimento; // Correção de bug do .NET

                DP.EnvioEmailService.EnviarEmailDespesasDoEstabelecimento(estabelecimento, emailsDestinatarios, despesas, data);
            }
        }

        public void EnviarEmailEstoqueDoEstabelecimento(int? idEstabelecimento = null)
        {
            var data = Calendario.Agora().AddDays(7).Date;

            var estabelecimentos = DP.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual();

            if (idEstabelecimento.HasValue)
            { //Parametro para área de teste
                estabelecimentos = estabelecimentos.Where(e => e.IdEstabelecimento == idEstabelecimento.Value);
            }

            var emailsComIdEstabelecimento = Domain.Pessoas.EnvioEmailService.ObterEmailsResponsaveisDosEstabelecimentos(estabelecimentos, NotificacaoEnum.EstoqueSemanal);

            if (estabelecimentos.Any(p => p.IdEstabelecimento == 30394))
            {
                LogService<EnvioEmailService>.Info("O estabelecimento " +
                                                estabelecimentos.Where(p => p.IdEstabelecimento == 30394).FirstOrDefault().NomeDeExibicaoNoPortal + " esta na lista.");
            }
            else
            {
                LogService<EnvioEmailService>.Info("O estabelecimento não foi encontrado na lista.");
            }

            foreach (var estabelecimento in estabelecimentos)
            {
                LogService<EnvioEmailService>.Info("Inicio da verificação para o envio do email de estoque do estabelecimento " +
                   estabelecimento.NomeDeExibicaoNoPortal);

                if (!estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailEstoqueHabilitado)
                    continue;

                var emailsDestinatarios = emailsComIdEstabelecimento.Where(e => e.IdEstabelecimento == estabelecimento.IdEstabelecimento).Select(e => e.MailAddress()).ToList();

                if (!emailsDestinatarios.Any())
                    continue;

                var posicaoEstoque = Domain.Pessoas.EstabelecimentoProdutoRepository.ObterProdutosPorPosicaoEstoqueMinimoOuAbaixoMinimo(estabelecimento.IdEstabelecimento);
                var estabelecimento1 = estabelecimento; // Correção de bug do .NET
                var produtosEstoque = Domain.Pessoas.EstabelecimentoProdutoRepository.ObterPosicaoEstoqueAgrupadoPorFabricante(estabelecimento.IdEstabelecimento, posicaoEstoque);
                DP.EnvioEmailService.EnviarEmailEstoqueDoEstabelecimento(estabelecimento, emailsDestinatarios, data, produtosEstoque);
            }
        }

        public void EnviarEmailParaLembrarAgendamentosDoProximoDia()
        {
            var data = Calendario.Agora().AddDays(1).Date;

            var estabelecimentosAtivosEnaoInadimplentes = Domain.Pessoas.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaENaoInadimplenteOuCanceladoOuNaoAssinado()
                .Where(f => f.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                .Fetch(f => f.FranquiaEstabelecimento)
                .Fetch(f => f.ConfiguracaoDeNFC)
                .Fetch(f => f.EstabelecimentoDadosGerais)
                .Fetch(f => f.EstabelecimentoConfiguracaoGeral);

            var agendamentosQueryable = DP.HorarioRepository.ListarNoDiaComProfissionalComClienteComEmail(data);

            var count = 0;

            var listaDeEmails = new StringBuilder();
            foreach (var e in estabelecimentosAtivosEnaoInadimplentes)
            {
                var agendamentos = agendamentosQueryable.Where(f => f.Estabelecimento.IdEstabelecimento == e.IdEstabelecimento).Select(f =>
                    new LembrarAgendamentosAgendamentoDTO
                    {
                        Email = f.Cliente.PessoaFisica.Email,
                        Nome = f.Cliente.PessoaFisica.NomeCompleto,
                        Horario = f,
                        NomeProfissional = f.Profissional.PessoaFisica.Apelido ?? f.Profissional.PessoaFisica.NomeCompleto,
                        NomeServicoEstabelecimento = f.ServicoEstabelecimento.Nome
                    }).ToList();

                var clientesEstabelecimentosDistintos = agendamentos.GroupBy(f => new
                {
                    f.Email,
                    f.Nome,
                    f.Horario
                });

                foreach (var ce in clientesEstabelecimentosDistintos)
                {
                    try
                    {
                        DP.EnvioEmailService.EnviarEmailLembreteAgendamentosMarcados(ce.Key.Nome, ce.Key.Email, ce.Key.Horario.ClienteEstabelecimento.Codigo, e, ce.ToList());
                    }
                    catch (Exception) { }
                    count++;

                    listaDeEmails.AppendLine(ce.Key.Email);
                }
            }

            var listaDeEmailsFormatada = listaDeEmails.Replace(Environment.NewLine, "<br />").ToString();

            DP.EnvioEmailService.EnviarInformacoesSobreEnviarEmailLembreteAgendamentosMarcados(count,
                listaDeEmailsFormatada);
        }

        public void GerarDadosEstatisticosPorDiaDoTrinks()
        {
            var diaPesquisa = Calendario.Hoje().AddDays(-1);

            var qntEstabelecimentosComAgendamentoParaDiaEOrigemBackoffice =
                DP.EstabelecimentoRepository.CountEstabelecimentoQuePossuemHorariosNoDia(HorarioOrigemEnum.Balcao,
                    diaPesquisa);
            var qntEstabelecimentosComAgendamentoParaDiaEOrigemHotsiteOuMobile =
                DP.EstabelecimentoRepository.CountEstabelecimentoQuePossuemHorariosNoDia(HorarioOrigemEnum.Web,
                    diaPesquisa);
            qntEstabelecimentosComAgendamentoParaDiaEOrigemHotsiteOuMobile +=
                DP.EstabelecimentoRepository.CountEstabelecimentoQuePossuemHorariosNoDia(HorarioOrigemEnum.MobileTrinks,
                    diaPesquisa);
            qntEstabelecimentosComAgendamentoParaDiaEOrigemHotsiteOuMobile += DP.EstabelecimentoRepository.CountEstabelecimentoQuePossuemHorariosNoDia(HorarioOrigemEnum.MobileRede,
                    diaPesquisa);

            //TRINKS-8591
            var qtdTotalDeHorariosCriadosAtravesDoSiteDaFranquiaNoDia = DP.HorarioRepository.CountEstabelecimentoTotalDeHorariosCriadosAtravesDoSiteDaFranquiaNoDia(HorarioOrigemEnum.SiteFranquia, diaPesquisa);

            int qntAgendamentosCriadosNoDiaPeloHotsite = ObterQuantidadeHorariosWeb(diaPesquisa);
            int qntAgendamentosCriadosNoDiaPeloMobileEMobileRede = ObterQuantidadeHorariosMobileEMobileRede(diaPesquisa);
            int qntAgendamentosCriadosNoDiaPeloMobileRede = ObterQuantidadeHorariosMobileRede(diaPesquisa);
            int qtAgendamentosCadastradosGoogleReserve = ObterQuantidadeAgendamentosCadastradosGoogleReserve(diaPesquisa);

            var qntAgendamentosParaDia = DP.HorarioRepository.CountAgendamentosCriadosNoDia(null, diaPesquisa);
            var qntDeEstabelecimentosCadastrados = DP.EstabelecimentoRepository.CountEstabelecimentoCadastrados();
            var qntDeEstabelecimentosQueAparecemNaBuscaDoPortal =
                DP.EstabelecimentoRepository.CountEstabelecimentoParaEstatisticasDiarias();
            var qntEstabelecimentosCadastradosNoDia =
                DP.EstabelecimentoRepository.CountEstabelecimentoCadastradosNoDia(diaPesquisa);
            var qntEstabelecimentosRealizaramFechamentoDeConta =
                DP.EstabelecimentoRepository.CountEstabelecimentoQueFecharamContaNoDia(diaPesquisa);
            var qntSMSEnviadosNodia =
                Domain.Notificacoes.RegistroNotificacaoRepository.ObterQuantidadeSMSEnviadoNoDia(diaPesquisa);
            var qntTotalUsuariosWebAtivos = DP.ClienteRepository.ObterTotalClientesWeb();
            var qntTotalUsuariosBalcaoAtivos =
                DP.ClienteEstabelecimentoRepository.ClientesEstabelecimentosWebBalcaoTotal();
            var qntUsuariosWebCadsatradosNoDia = DP.ClienteRepository.ObterTotalClientesWebCadastradosNaData(diaPesquisa);
            var qntUsuariosBalcaoCadsatradosNoDia =
                DP.ClienteEstabelecimentoRepository.ClientesEstabelecimentosWebBalcaoCadastradosNoDia(diaPesquisa)
                    .Count(f => f.Tipo == TipoClienteEnum.Balcao);

            var somatorioFaturasPagasNoPeriodo = Domain.Cobranca.FaturaTrinksRepository.SomatorioDeFaturasPagasNoPeriodo(30);
            var somatorioFaturasPagasMarketingNoPeriodo = Domain.Cobranca.FaturaMarketingRepository.SomatorioDeFaturasPagasNoPeriodo(30);

            int? qtdEstabelecimentosAssinadosNoDia = Domain.Cobranca.FaturaRepository.ObterQuantosEstabelecimentosTiveramSuaPrimeiraFaturaGeradaNoDia(diaPesquisa);
            int? qtdEstabelecimentosCanceladosNoDia = Domain.Cobranca.ContaFinanceiraRepository.ObterQuantasContasForamCanceladasNoDia(diaPesquisa);
            int? qtdEstabelecimentosComPrimeiraFaturaPagaNoDia = Domain.Cobranca.FaturaRepository.ObterQuantosEstabelecimentosPagaramSuaPrimeiraFaturaNoDia(diaPesquisa);

            var relatorio = new BIUsoDoSistema
            {
                DataRegistro = Calendario.Agora(),
                DataReferencia = diaPesquisa,
                QuantidadeEstabelecimentoCriaramAgendamentoPeloBackofficeNoDia =
                    qntEstabelecimentosComAgendamentoParaDiaEOrigemBackoffice,
                QuantidadeEstabelecimentoReceberamAgendamentoDoCliente =
                    qntEstabelecimentosComAgendamentoParaDiaEOrigemHotsiteOuMobile,
                QuantidadeAgendamentosCriadosPeloWebNoDia = qntAgendamentosCriadosNoDiaPeloHotsite,
                QuantidadeAgendamentosCriadosPeloMobileNoDia = qntAgendamentosCriadosNoDiaPeloMobileEMobileRede,
                QuantidadeAgendamentosCriadosNoDia = qntAgendamentosParaDia,
                QuantidadeEstabelecimentoCadastrados = qntDeEstabelecimentosCadastrados,
                QuantidadeEstabelecimentoCadastradosAparecemNoPortal = qntDeEstabelecimentosQueAparecemNaBuscaDoPortal,
                QuantidadeEstabelecimentoCadastradosNoDia = qntEstabelecimentosCadastradosNoDia,
                QuantidadeEstabelecimentoFecharamContaNoDia = qntEstabelecimentosRealizaramFechamentoDeConta,
                QuantidadeSMSEnviadosNoDia = qntSMSEnviadosNodia,
                QuantidadeUsuariosWebAtivos = qntTotalUsuariosWebAtivos,
                QuantidadeUsuariosBalcaoAtivos = qntTotalUsuariosBalcaoAtivos,
                QuantidadeUsuariosWebCadastradosNoDia = qntUsuariosWebCadsatradosNoDia,
                QuantidadeUsuariosBalcaoCadastradosNoDia = qntUsuariosBalcaoCadsatradosNoDia,
                SomatorioFaturasPagasNoPeriodo = somatorioFaturasPagasNoPeriodo,
                SomatorioFaturasPagasMarketingNoPeriodo = somatorioFaturasPagasMarketingNoPeriodo,
                //TRINKS-8591
                QuantidadeHorariosCriadosAtravesSiteDaFranquiaNoDia = qtdTotalDeHorariosCriadosAtravesDoSiteDaFranquiaNoDia,
                QuantidadeEstabelecimentosAssinadosNoDia = qtdEstabelecimentosAssinadosNoDia,
                QuantidadeEstabelecimentosCanceladosNoDia = qtdEstabelecimentosCanceladosNoDia,
                QuantidadeEstabelecimentosComPrimeiraFaturaPagaNoDia = qtdEstabelecimentosComPrimeiraFaturaPagaNoDia,
                QuantidadeAgendamentosCriadosPeloMobileRedeNoDia = qntAgendamentosCriadosNoDiaPeloMobileRede,
                QuantidadeAgendamentosCadastradosGoogleReserve = qtAgendamentosCadastradosGoogleReserve
            };

            DE.BIUsoDoSistemaRepository.SaveNew(relatorio);
            DP.EnvioEmailService.EnviarEmailEstatisticasSobreUsoDoSistema(relatorio);
        }

        private static int ObterQuantidadeHorariosWeb(DateTime diaPesquisa)
        {
            var listaHorarioOrigemEnumWeb = new List<HorarioOrigemEnum>();
            listaHorarioOrigemEnumWeb.Add(HorarioOrigemEnum.Web);
            var qntAgendamentosCriadosNoDiaPeloHotsite = DP.HorarioRepository.CountAgendamentosCriadosNoDia(listaHorarioOrigemEnumWeb, diaPesquisa);
            return qntAgendamentosCriadosNoDiaPeloHotsite;
        }

        private static int ObterQuantidadeAgendamentosCadastradosGoogleReserve(DateTime diaPesquisa)
        {
            var listaHorarioOrigemEnumGoogleReserve = new List<HorarioOrigemEnum>();
            listaHorarioOrigemEnumGoogleReserve.Add(HorarioOrigemEnum.GoogleReserve);
            var qntAgendamentosCriadosNoDiaPeloGoogleReserve = DP.HorarioRepository.CountAgendamentosCriadosNoDia(listaHorarioOrigemEnumGoogleReserve, diaPesquisa);
            return qntAgendamentosCriadosNoDiaPeloGoogleReserve;
        }

        private static int ObterQuantidadeHorariosMobileEMobileRede(DateTime diaPesquisa)
        {
            var listaHorarioOrigemEnumMobileEMobileRede = new List<HorarioOrigemEnum>();
            listaHorarioOrigemEnumMobileEMobileRede.Add(HorarioOrigemEnum.MobileTrinks);
            listaHorarioOrigemEnumMobileEMobileRede.Add(HorarioOrigemEnum.MobileRede);

            var qntAgendamentosCriadosNoDiaPeloMobile =
                DP.HorarioRepository.CountAgendamentosCriadosNoDia(listaHorarioOrigemEnumMobileEMobileRede, diaPesquisa);
            return qntAgendamentosCriadosNoDiaPeloMobile;
        }

        private static int ObterQuantidadeHorariosMobileRede(DateTime diaPesquisa)
        {
            var listaHorarioOrigemEnumMobileEMobileRede = new List<HorarioOrigemEnum>();
            listaHorarioOrigemEnumMobileEMobileRede.Add(HorarioOrigemEnum.MobileRede);

            var qntAgendamentosCriadosNoDiaPeloMobile =
                DP.HorarioRepository.CountAgendamentosCriadosNoDia(listaHorarioOrigemEnumMobileEMobileRede, diaPesquisa);
            return qntAgendamentosCriadosNoDiaPeloMobile;
        }

        public void RemoverEstabelecimentosInativosDoPortal()
        {
            var toleranciaUltimoLogin =
                Calendario.Hoje().AddDays(
                    -(new ParametrosTrinks<Int32>(
                        ParametrosTrinksEnum.qtd_dias_padrao_ultimo_login_para_manter_hotsite_no_ar).ObterValor()));

            var estabelecimentos = DP.EstabelecimentoRepository.Queryable();
            estabelecimentos = estabelecimentos.Where(f => f.PesoBuscaPortal < 40);
            estabelecimentos = estabelecimentos.Where(f => f.VinculoUsuarios.Any(
                g =>
                    g.PessoaFisica.Contas.Any(
                        p => p.DataUltimoLogin.HasValue && p.DataUltimoLogin.Value.Date < toleranciaUltimoLogin.Date)));

            var statusAdimplentes = new List<int>{
                (int) Cobranca.StatusContaFinanceira.Adimplente,
                (int) Cobranca.StatusContaFinanceira.InadimplenteEmTolerancia,
                (int) Cobranca.StatusContaFinanceira.PeriodoGratis,
                (int) Cobranca.StatusContaFinanceira.CobrancaManual
            };

            var contas = Domain.Cobranca.ContaFinanceiraRepository.Queryable().Where(f => !statusAdimplentes.Contains(f.Status.IdStatus));
            estabelecimentos = estabelecimentos.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));

            foreach (var e in estabelecimentos)
            {
                DP.EstabelecimentoService.DefinirPesoBuscaPortal(e, Calendario.Agora());
            }
        }

        public void ExecutarAtualizacaoDasPrioridadesDeSincronia()
        {
            DP.SincronizacaoEstabelecimentosFilaRepository.AtualizarPrioridadeDasSincroninas();
        }

        public void SincronizarEstabelecimentosNaFila()
        {
            var fila = DP.SincronizacaoEstabelecimentosFilaRepository.ListarEstabelecimentosNaoSincronizados();

            foreach (var solicitacaoDeSincronia in fila)
            {
                var unidadesAssociadasAoModelo = DP.EstabelecimentoRepository
                .ListarEstabelecimentosPeloModelo(solicitacaoDeSincronia.IdEstabelecimento)
                .ToList();

                SincronizarParaOModelo(solicitacaoDeSincronia, unidadesAssociadasAoModelo);

                // Limpar o 1st level cache
                Domain.Pessoas.SincronizacaoEstabelecimentosFilaRepository.Clear();
            }
        }

        public void SincronizarEstabelecimentosNaFilaComLimite()
        {
            var qtdLimiteParaExecutarSincronia = new ParametrosTrinks<int>(ParametrosTrinksEnum.qtd_max_para_executar_sincronia).ObterValor();
            var fila = DP.SincronizacaoEstabelecimentosFilaRepository.ListarEstabelecimentosNaoSincronizadosComLimite(qtdLimiteParaExecutarSincronia);

            foreach (var solicitacaoDeSincronia in fila)
            {
                var unidadesAssociadasAoModelo = DP.EstabelecimentoRepository
                    .ListarEstabelecimentosPeloModelo(solicitacaoDeSincronia.IdEstabelecimento)
                    .ToList();

                SincronizarParaOModelo(solicitacaoDeSincronia, unidadesAssociadasAoModelo);

                // Limpar o 1st level cache
                Domain.Pessoas.SincronizacaoEstabelecimentosFilaRepository.Clear();
            }
        }

        public void SincronizarModelosBaseadosEmModelosNaFila()
        {
            var fila = DP.SincronizacaoEntreEstabelecimentosModelosFilaRepository.ListarEstabelecimentosNaoSincronizados();

            foreach (var solicitacao in fila)
            {

                LogService<RotinasService>.Info("Sincronizando modelo de modelo - " + solicitacao.TipoSincronizacao + ": " + solicitacao.IdEstabelecimento);

                SincronizarModeloBaseadoEmModelo(solicitacao);

                LogService<RotinasService>.Info("Modelo de modelo sincronizado");

                // Limpar o 1st level cache
                Domain.Pessoas.SincronizacaoEntreEstabelecimentosModelosFilaRepository.Clear();
            }
        }

        private static void SincronizarModeloBaseadoEmModelo(SincronizacaoEntreEstabelecimentosModelosFila destino)
        {
            var estabelecimentoDestino = DP.EstabelecimentoRepository.Load(destino.IdEstabelecimento);
            var estabelecimentoModelo = estabelecimentoDestino.FranquiaEstabelecimento?.EstabelecimentoModelo;

            var houveFalha = false;

            var sincronizadoServicos = false;
            var sincronizadoProdutos = false;
            var sincronizadoPacotes = false;
            var sincronizadoAssinaturaRecorrente = false;
            var sincronizadoClubeDeAssinaturas = false;

            if (estabelecimentoModelo != null)
            {
                LogService<RotinasService>.Info("===> Sincronizando : Modelo Origem: " + estabelecimentoModelo.IdEstabelecimento + "-> Modelo Destino: " + destino.IdEstabelecimento);

                try
                {
                    switch (destino.TipoSincronizacao)
                    {
                        case TipoSincronizacaoEnum.Servicos:
                            DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                estabelecimentoDestino.IdEstabelecimento);
                            sincronizadoServicos = true;
                            break;

                        case TipoSincronizacaoEnum.Produtos:
                            DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                estabelecimentoDestino.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                            sincronizadoProdutos = true;
                            break;

                        case TipoSincronizacaoEnum.Pacotes:
                            DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                estabelecimentoDestino.IdEstabelecimento);
                            DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                estabelecimentoDestino.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                            DP.SincroniaEstabelecimentoModeloService.SincronizarPacotesComModelo(estabelecimentoDestino.IdEstabelecimento);

                            sincronizadoServicos = true;
                            sincronizadoProdutos = true;
                            sincronizadoPacotes = true;
                            break;

                        case TipoSincronizacaoEnum.AssinaturaRecorrente:
                            DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                estabelecimentoDestino.IdEstabelecimento);
                            DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                estabelecimentoDestino.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                            DP.SincroniaEstabelecimentoModeloService.SincronizarAssinaturaRecorrenteComModelo(estabelecimentoDestino.IdEstabelecimento);

                            sincronizadoServicos = true;
                            sincronizadoProdutos = true;
                            sincronizadoAssinaturaRecorrente = true;
                            sincronizadoPacotes = true; // SincronizarAssinaturaRecorrenteComModelo sincroniza pacotes
                            break;

                        case TipoSincronizacaoEnum.ClubeDeAssinaturas:
                            DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                estabelecimentoDestino.IdEstabelecimento);
                            DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                estabelecimentoDestino.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                            DP.SincroniaEstabelecimentoModeloService.SincronizarPlanosDoClubeDeAssinaturaComModelo(estabelecimentoDestino.IdEstabelecimento);

                            sincronizadoServicos = true;
                            sincronizadoProdutos = true;
                            sincronizadoClubeDeAssinaturas = true;
                            break;
                    }

                    LogService<RotinasService>.Info(destino.TipoSincronizacao + " do modelo " + estabelecimentoDestino.NomeDeExibicaoNoPortal + " sincronizados");
                }
                catch (Exception e)
                {
                    LogService<RotinasService>.Error("SincronizarModeloBaseadoEmModelo: Erro ao sincronizar " + destino.TipoSincronizacao + ": " + estabelecimentoDestino.NomeDeExibicaoNoPortal);
                    LogService<RotinasService>.Error(e.Formatada());
                    //houveFalha = true;
                }

                if (!houveFalha)
                {
                    DP.SincronizacaoEntreEstabelecimentosModelosFilaRepository.MarcarSincroniaComoFinalizada(destino.Id);

                    if (sincronizadoServicos)
                        if (sincronizadoProdutos)
                            DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.Produtos, destino.IdEstabelecimento);
                    if (sincronizadoPacotes)
                        DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.Pacotes, destino.IdEstabelecimento);
                    if (sincronizadoAssinaturaRecorrente)
                        DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.AssinaturaRecorrente, destino.IdEstabelecimento);
                    if (sincronizadoClubeDeAssinaturas)
                        DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.ClubeDeAssinaturas, destino.IdEstabelecimento);
                }
            }
            else
            {
                LogService<RotinasService>.Info("===> Modelo Destino " + destino.IdEstabelecimento + " não está vinculado a um modelo para ser sincronizado");
            }

            Domain.Pessoas.EstabelecimentoRepository.Clear();
        }

        private static void SincronizarParaOModelo(SincronizacaoEstabelecimentosFila solicitacao, List<Estabelecimento> estabelecimentosFilhos)
        {
            solicitacao.Refresh();

            if (solicitacao.DataFinalizacao.HasValue)
                return;

            var sincronizadoServicos = false;
            var sincronizadoProdutos = false;
            var sincronizadoPacotes = false;
            var sincronizadoAssinaturaRecorrente = false;
            var sincronizadoClubeDeAssinaturas = false;

            LogService<RotinasService>.Info("===> Modelo " + solicitacao.IdEstabelecimento);
            var pai1 = solicitacao; // correção de bug do .NET
            var estabelecimentos =
                estabelecimentosFilhos.Where(
                    p =>
                        p.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento ==
                        TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo &&
                        p.EstabelecimentoModelo().IdEstabelecimento == pai1.IdEstabelecimento);

            var houveFalha = false;

            foreach (var filho in estabelecimentos)
            {
                using (new SessionScope(FlushAction.Auto))
                {

                    try
                    {
                        LogService<RotinasService>.Info("Sincronizando " + solicitacao.TipoSincronizacao + ": " + filho.NomeDeExibicaoNoPortal);

                        switch (solicitacao.TipoSincronizacao)
                        {
                            case TipoSincronizacaoEnum.Servicos:
                                DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                    filho.IdEstabelecimento);
                                sincronizadoServicos = true;
                                break;

                            case TipoSincronizacaoEnum.Produtos:
                                DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                    filho.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                                sincronizadoProdutos = true;
                                break;

                            case TipoSincronizacaoEnum.Pacotes:
                                DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                    filho.IdEstabelecimento);
                                DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                    filho.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                                DP.SincroniaEstabelecimentoModeloService.SincronizarPacotesComModelo(filho.IdEstabelecimento);

                                sincronizadoServicos = true;
                                sincronizadoProdutos = true;
                                sincronizadoPacotes = true;
                                break;

                            case TipoSincronizacaoEnum.AssinaturaRecorrente:
                                DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                    filho.IdEstabelecimento);
                                DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                    filho.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                                DP.SincroniaEstabelecimentoModeloService.SincronizarAssinaturaRecorrenteComModelo(filho.IdEstabelecimento);

                                sincronizadoServicos = true;
                                sincronizadoProdutos = true;
                                sincronizadoAssinaturaRecorrente = true;
                                sincronizadoPacotes = true; // SincronizarAssinaturaRecorrenteComModelo sincroniza pacotes
                                break;

                            case TipoSincronizacaoEnum.ClubeDeAssinaturas:
                                DP.SincroniaEstabelecimentoModeloService.SincronizarServicosEstabelecimentoComModelo(
                                    filho.IdEstabelecimento);
                                DP.SincroniaEstabelecimentoModeloService.SincronizarEstabelecimentoProdutosComModelo(
                                    filho.IdEstabelecimento, OrigemSincroniaDeModelo.Agendamento);
                                DP.SincroniaEstabelecimentoModeloService.SincronizarPlanosDoClubeDeAssinaturaComModelo(filho.IdEstabelecimento);

                                sincronizadoServicos = true;
                                sincronizadoProdutos = true;
                                sincronizadoClubeDeAssinaturas = true;
                                break;
                        }

                        LogService<RotinasService>.Info(solicitacao.TipoSincronizacao + " sincronizados");

                    }
                    catch (Exception e)
                    {
                        LogService<RotinasService>.Error("Erro ao sincronizar " + solicitacao.TipoSincronizacao + ": " + filho.NomeDeExibicaoNoPortal);
                        LogService<RotinasService>.Error(e.Formatada());
                        houveFalha = true;
                    }
                }
            }

            if (!houveFalha || solicitacao.DataAgendamento < DateTime.Now.AddDays(-2))
            {
                DP.SincronizacaoEstabelecimentosFilaRepository.MarcarSincroniaComoFinalizada(solicitacao.Id);

                if (sincronizadoServicos)
                    if (sincronizadoProdutos)
                        DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.Produtos, solicitacao.IdEstabelecimento);
                if (sincronizadoPacotes)
                    DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.Pacotes, solicitacao.IdEstabelecimento);
                if (sincronizadoAssinaturaRecorrente)
                    DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.AssinaturaRecorrente, solicitacao.IdEstabelecimento);
                if (sincronizadoClubeDeAssinaturas)
                    DP.SincronizacaoEstabelecimentosFilaRepository.FinalizarTodaSincroniaAgendada(TipoSincronizacaoEnum.ClubeDeAssinaturas, solicitacao.IdEstabelecimento);
            }

            Domain.Pessoas.EstabelecimentoRepository.Clear();
        }

        public void AlertarClientesDoProgramaDeFidelidadeSobrePontosExpirandoNoPrazo(DateTime data)
        {
            data = data.Date;
            var programasDeFidelidade = Domain.Fidelidade.ProgramaDeFidelidadeRepository.ObterProgramasDeFidelidadeHabilitadoQueAvisamAosClientesSobreOsPontos().ToList();

            foreach (var programaDeFidelidade in programasDeFidelidade)
            {
                if (programaDeFidelidade.DiasDeAntecedenciaParaNotificarValidadeDosPontos > 0)
                {
                    var prazoDeAviso = data.AddDays(programaDeFidelidade.DiasDeAntecedenciaParaNotificarValidadeDosPontos.Value);
                    var clientesComPontosExpirando = Domain.Fidelidade.PontoGanhoRepository.ObterQuatidadeDePontosExpiradosNoDiaPorCliente(prazoDeAviso, programaDeFidelidade.Estabelecimento.PessoaJuridica.IdPessoa);

                    int pontoMinimoParaResgate;

                    var produtoParaResgatePontoMinimo = Domain.Pessoas.EnvioEmailService.ListaProdutosOuServicosComMenorPontuacaoParaResgate(programaDeFidelidade.Estabelecimento.IdEstabelecimento, out pontoMinimoParaResgate);

                    foreach (var clienteAReceberEmail in clientesComPontosExpirando)
                    {
                        Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificacaoPontosDeFidelidadeExpiradosNoPrazo(clienteAReceberEmail, programaDeFidelidade.Estabelecimento, pontoMinimoParaResgate, produtoParaResgatePontoMinimo, prazoDeAviso, programaDeFidelidade.DiasDeAntecedenciaParaNotificarValidadeDosPontos.Value);
                    }
                }
            }
            Domain.Fidelidade.ProgramaDeFidelidadeRepository.Clear();
        }

        public void AlertarClientesDoProgramaDeFidelidadeSobrePontosExpirandoNoDia(DateTime data)
        {
            data = data.Date;
            var programasDeFidelidade = Domain.Fidelidade.ProgramaDeFidelidadeRepository.ObterProgramasDeFidelidadeHabilitadoQueAvisamAosClientesSobreOsPontos().ToList();

            foreach (var programaDeFidelidade in programasDeFidelidade)
            {
                var clientesComPontosExpirando = Domain.Fidelidade.PontoGanhoRepository.ObterQuatidadeDePontosExpiradosNoDiaPorCliente(data, programaDeFidelidade.Estabelecimento.PessoaJuridica.IdPessoa);
                int pontoMinimoParaResgate;

                var produtoParaResgatePontoMinimo = Domain.Pessoas.EnvioEmailService.ListaProdutosOuServicosComMenorPontuacaoParaResgate(programaDeFidelidade.Estabelecimento.IdEstabelecimento, out pontoMinimoParaResgate);

                foreach (var clienteAReceberEmail in clientesComPontosExpirando)
                {
                    Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificacaoPontosDeFidelidadeExpiradosNoDia(clienteAReceberEmail, programaDeFidelidade.Estabelecimento, pontoMinimoParaResgate, produtoParaResgatePontoMinimo, data);
                }
            }

            Domain.Fidelidade.ProgramaDeFidelidadeRepository.Clear();
        }

        // 💩 Como o método que formata o período de pesquisa está marcado como internal, este método foi necessário
        private string FormatarPeriodoDePesquisa(DateTime? dataDasTransacoes, PeriodoDePesquisa periodoDePesquisa)
        {
            if (dataDasTransacoes.HasValue)
            {
                // Estamos assumindo que se foi solicitada uma data específica, a consulta é de um único período, com dia definido
                return periodoDePesquisa.ToDateTime().ToString("dd/MM/yyyy");
            }

            // Caso contrário é assumido que este item é um de vários meses que estão sendo pesquisados
            return periodoDePesquisa.ToDateTime().ToString("MM/yyyy");
        }

        private bool InformouEstabelecimentosParaConciliacao(int[] idsDosEstabelecimentos)
        {
            return idsDosEstabelecimentos != null && idsDosEstabelecimentos.Any();
        }

        /// <summary>
        /// Retorna os períodos de pesquisa de transação para conciliação de um estabelecimento,
        /// conforme o parâmetro de data da pesquisa informado.
        /// </summary>
        /// <param name="idEstabelecimento">Id do estabelecimento para o qual será criada a lista de períodos de pesquisa</param>
        /// <param name="dataDasTransacoes">Quando informado indica que a pesquisa deve ser feita para o dia específico informado.
        ///
        /// Quando nulo indica que deve ser pesquisado todo o histórico de transações do estabelecimento.
        /// </param>
        /// <returns></returns>
        private List<PeriodoDePesquisa> ConsultarPeriodosDePesquisaDoEstabelecimento(int idEstabelecimento,
            DateTime? dataDasTransacoes, SubadquirenteEnum subadquirente)
        {
            if (dataDasTransacoes.HasValue)
            {
                return new List<PeriodoDePesquisa>() { PeriodoDePesquisa.PorDia(dataDasTransacoes.Value).Valor };
            }

            // Quando não foi solicitada uma data específica, retorna uma lista de todos
            // os perídos (meses e anos) em que o estabelecimento possui transações da subadquirente
            var queryPeriodos = Domain.Financeiro.TransacaoPOSRepository.Queryable()
                .Where(x => x.Estabelecimento.IdEstabelecimento == idEstabelecimento && x.TipoPOS.Id == (int)subadquirente)
                .Select(x => new { Mes = x.Data.Month, Ano = x.Data.Year })
                .GroupBy(g => new { g.Mes, g.Ano })
                .Select(x => new { x.Key.Mes, x.Key.Ano })
                .ToList();

            return queryPeriodos
                .Select(x => PeriodoDePesquisa.PorMes(x.Mes, x.Ano))
                .Where(x => x != null && x.Sucesso)
                .Select(x => x.Valor)
                .ToList();
        }

        public void AtualizarCadastroRecebedoresSiclosviaAPI()
        {
            var eps = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterProfissionaisSemCodigoIdentificacaoPOS();
            if (eps != null)
            {
                var integracao = new Financeiro.Services.IntegracaoSiclosService();

                // Faz requisição para obter o stonecode que foi gerado
                foreach (var ep in eps)
                {
                    bool atualizado = integracao.AtualizarStoneCodeDoProfissional(ep);

                    if (ep.AguardandoRetornoDaHabilitacaoDoSplit == false && ep.CodigoIdentificacaoPOS != null)
                        LogService<RotinasService>.Info("Estabelecimento: " + ep.Estabelecimento.NomeDeExibicaoNoPortal + " | Profissional: " + ep.Profissional.PessoaFisica.NomeCompleto + " | Split: Habilitado <br />");
                    else
                        LogService<RotinasService>.Info("Estabelecimento: " + ep.Estabelecimento.NomeDeExibicaoNoPortal + " | Profissional: " + ep.Profissional.PessoaFisica.NomeCompleto + " | Split: NÂO Habilitado <br />");
                }
            }
        }

        public void MudarPeriodoVigenteDoRelatorioRetornoDeClientes()
        {
            var hoje = Calendario.Hoje().ToString("yyyy-MM-dd");
            var session = ActiveRecordMediator.GetSessionFactoryHolder().CreateSession(typeof(ActiveRecordBase));
            NHibernate.IQuery query = session.CreateSQLQuery($"exec ClientesAtendidos_MudarPeriodoVigente '{hoje}'");
            query.ExecuteUpdate();
        }

        public void AtualizarDadosDoPeriodoVigenteNoRelatorioRetornoDeClientes()
        {
            var hoje = Calendario.Hoje().ToString("yyyy-MM-dd");
            var session = ActiveRecordMediator.GetSessionFactoryHolder().CreateSession(typeof(ActiveRecordBase));
            NHibernate.IQuery query = session.CreateSQLQuery($"exec ClientesAtendidos_AtualizarDadosDoPeriodoVigente '{hoje}'");
            query.ExecuteUpdate();
        }


        /// <summary>
        /// Realiza a conciliação automática de transações POS
        /// </summary>
        /// <param name="subadquirente">Id da subadquirente</param>
        /// <param name="dataDasTransacoes">Data das transações a serem conciliadas. Quando nulo a conciliação é de todo o histórico de todos os estabelecimentos selecionados.</param>
        /// <param name="idsDosEstabelecimentos">Lista opcional de ids de estabelecimentos a terem as transações conciliadas.
        ///
        /// Quando nulo, todos os estabelecimentos configurados serão conciliados.
        ///
        /// Ids de estabelecimentos que não estejam aptos para conciliação serão descartados.
        /// </param>
        public void RealizarConciliacaoAutomatica(
            SubadquirenteEnum subadquirente,
            DateTime? dataDasTransacoes,
            params int[] idsDosEstabelecimentos)
        {

            LogService<RotinasService>.Info(String.Format("=== Iniciando conciliação automática {0} para {1} ===",
            subadquirente.ToString(), dataDasTransacoes.HasValue ? $"transações do dia {dataDasTransacoes.Value:dd/MM/yyyy}" : "todas as transações do histórico"));

            if (InformouEstabelecimentosParaConciliacao(idsDosEstabelecimentos))
            {
                LogEstabelecimentosParaConciliacao(subadquirente, idsDosEstabelecimentos);
            }

            var estabelecimentosParaConciliar = ObterEstabelecimentosParaConciliar(idsDosEstabelecimentos, SubadquirenteEnum.ConnectStone);

            LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()} - {estabelecimentosParaConciliar.Count()} estabelecimentos para conciliar");

            Parallel.ForEach(estabelecimentosParaConciliar, estabelecimento =>
            {
                ConciliarTransacoesDoEstabelecimento(subadquirente, dataDasTransacoes, estabelecimento);
            });
        }

        private void ConciliarTransacoesDoEstabelecimento(SubadquirenteEnum subadquirente, DateTime? dataDasTransacoes,
            Estabelecimento estabelecimento)
        {
            LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] Iniciando conciliação do estabelecimento '{estabelecimento.NomeDeExibicaoNoPortal}' (Id {estabelecimento.IdEstabelecimento})");

            var periodosDePesquisa = ConsultarPeriodosDePesquisaDoEstabelecimento(estabelecimento.IdEstabelecimento, dataDasTransacoes, SubadquirenteEnum.ConnectStone);
            if (!periodosDePesquisa.Any())
            {
                LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] Nenhum período de pesquisa obtido para o estabelecimento '{estabelecimento.NomeDeExibicaoNoPortal}' (Id {estabelecimento.IdEstabelecimento})");
                return;
            }

            foreach (var periodoDePesquisa in periodosDePesquisa)
            {
                LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] Consultando transações (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, Período: {FormatarPeriodoDePesquisa(dataDasTransacoes, periodoDePesquisa)})");

                try
                {
                    var transacoes = Domain.ConciliacaoBelezinha.ConciliacaoPosService
                        .Consultar(new ConsultarTransacaoesDto()
                        {
                            IdEstabelecimento = estabelecimento.IdEstabelecimento,
                            Ano = periodoDePesquisa.Ano,
                            Mes = periodoDePesquisa.Mes,
                            Dia = dataDasTransacoes?.Day
                        });
                    var transacoesPendentesTrinks = transacoes.Transacoes.Trinks.Pendentes.OrderBy(x => x.Id).ThenBy(x => x.Parcela);

                    if (!transacoesPendentesTrinks.Any())
                    {
                        LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] Nenhuma transação pendente encontrada (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, Período: {FormatarPeriodoDePesquisa(dataDasTransacoes, periodoDePesquisa)})");
                        return;
                    }

                    var transacoesJaConciliadas = new List<string>();

                    LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] {transacoesPendentesTrinks.Count()} transação(s) encontrada(s) (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, Período: {FormatarPeriodoDePesquisa(dataDasTransacoes, periodoDePesquisa)})");
                    foreach (var transacaoPendente in transacoesPendentesTrinks)
                    {
                        LogService<RotinasService>.Info($"[ConciliacaoConnectStone] Conciliando transação (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, IdTransacaoPOS: {transacaoPendente.Id}");

                        try
                        {
                            var transacaoStone = transacoes.Transacoes.Stone.Pendentes
                                .Where(x => x.Id == transacaoPendente.Id && x.Parcela == transacaoPendente.Parcela)
                                .SingleOrDefault();

                            if (transacaoStone == null)
                            {
                                LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] Não foi encontrada uma transação Connect Pagar.me correspondente a transação (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, IdTransacaoPOS: {transacaoPendente.Id}");
                                return;
                            }

                            var jaConciliouEstaTransacao = transacoesJaConciliadas.Any(t => t == transacaoPendente.Id);

                            Domain.ConciliacaoBelezinha.ConciliacaoPosService.Conciliar(estabelecimento, transacaoPendente, transacaoStone, !jaConciliouEstaTransacao);
                        }
                        catch (Exception ex)
                        {
                            LogService<RotinasService>.Info($"[Conciliacao {subadquirente.ToString()}] Falha ao conciliar transação (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, IdTransacaoPOS: {transacaoPendente.Id}");
                            LogService<RotinasService>.Info(ex.Message);
                            LogService<RotinasService>.Info(ex.StackTrace);

                            if (ex.InnerException != null)
                            {
                                LogService<RotinasService>.Info($"InnerException: {ex.InnerException.Message}");
                                LogService<RotinasService>.Info(ex.InnerException.StackTrace);
                            }
                        }
                        finally
                        {
                            transacoesJaConciliadas.Add(transacaoPendente.Id);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogService<RotinasService>.Info($"[Conciliacao{subadquirente.ToString()}] Falha ao obter transações para conciliação (IdEstabelecimento: {estabelecimento.IdEstabelecimento}, Período: {FormatarPeriodoDePesquisa(dataDasTransacoes, periodoDePesquisa)})");
                    LogService<RotinasService>.Info(ex.Message);
                    LogService<RotinasService>.Info(ex.StackTrace);

                    return;
                }
            }
        }

        private static void LogEstabelecimentosParaConciliacao(SubadquirenteEnum subadquirente, int[] idsDosEstabelecimentos)
        {
            LogService<RotinasService>.Info(String.Format("[Conciliacao{0}] Conciliação solicitada para os seguintes estabelecimentos: {1}",
                subadquirente.ToString(), idsDosEstabelecimentos.Select(id => id.ToString())
                    .Aggregate(new StringBuilder(),
                        (current, next) => current.Append(current.Length == 0 ? "" : ", ").Append(next))));
        }


        private IEnumerable<Estabelecimento> ObterEstabelecimentosParaConciliar(int[] idsDosEstabelecimentos, SubadquirenteEnum subadquirente)
        {
            var estabelecimentosConfiguradosParaConciliacaoConnectPagarme = DP.EstabelecimentoConfiguracaoPOSRepository
                .ObterEstabelecimentosParaConciliacaoAutomaticaPos(subadquirente);

            var estabelecimentosParaConciliar = InformouEstabelecimentosParaConciliacao(idsDosEstabelecimentos) ?
                estabelecimentosConfiguradosParaConciliacaoConnectPagarme.Where(x => idsDosEstabelecimentos.Contains(x.IdEstabelecimento)) :
                estabelecimentosConfiguradosParaConciliacaoConnectPagarme;
            return estabelecimentosParaConciliar;
        }
    }
}