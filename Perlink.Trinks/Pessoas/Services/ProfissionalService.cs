﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.IntegracaoComOutrosSistemas;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.Notificacoes.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Repositories;
using Perlink.Trinks.RPS.GeradorDeArquivo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Perlink.Trinks.Pessoas.Services
{

    public class ProfissionalService : BaseService, IProfissionalService
    {

        #region Propriedades de Apoio

        private IProfissionalRepository ProfissionalRepository
        {
            get { return Domain.Pessoas.ProfissionalRepository; }
        }

        private IEstabelecimentoProfissionalRepository EstabelecimentoProfissionalRepository
        {
            get { return Domain.Pessoas.EstabelecimentoProfissionalRepository; }
        }

        private IEstabelecimentoProfissionalService EstabelecimentoProfissionalService
        {
            get { return Domain.Pessoas.EstabelecimentoProfissionalService; }
        }

        private IUsuarioEstabelecimentoRepository UsuarioEstabelecimentoRepository
        {
            get { return Domain.Pessoas.UsuarioEstabelecimentoRepository; }
        }

        private IEnvioEmailService EmailService
        {
            get { return Domain.Pessoas.EnvioEmailService; }
        }

        private IClienteEstabelecimentoService ClienteEstabelecimentoService
        {
            get { return Domain.Pessoas.ClienteEstabelecimentoService; }
        }

        private IPeriodoAusenciaRepository PeriodoAusenciaRepository
        {
            get { return Domain.Pessoas.PeriodoAusenciaRepository; }
        }

        private IHorarioRepository HorarioRepository
        {
            get { return Domain.Pessoas.HorarioRepository; }
        }

        #endregion Propriedades de Apoio

        #region Métodos Públicos

        [TransactionInitRequired]
        public FotoPessoa DefinirFotoPadrao(FotoPessoa foto, HttpPostedFileBase arquivo)
        {
            var pessoa = Domain.Pessoas.PessoaRepository.Load(foto.Pessoa.IdPessoa);

            if (pessoa.PessoaFisica.FotoPrincipal != null)
                foto = pessoa.PessoaFisica.FotoPrincipal;

            //if (pessoa.Fotos.Count > 0) {
            //    DL.Pessoas.FotoService.ExcluirFoto(pessoa.PessoaFisica.Profissional.FotoPrincipal.Codigo.Value);
            //}

            foto.Principal = true;
            foto.DataAtualizacao = Calendario.Agora();
            Domain.Pessoas.FotoService.SalvarFoto(foto, arquivo);
            return foto;
        }

        [TransactionInitRequired]
        public FotoPessoa DefinirFotoPadraoApi(FotoPessoa foto, HttpPostedFile arquivo)
        {
            var pessoa = Domain.Pessoas.PessoaRepository.Load(foto.Pessoa.IdPessoa);

            if (pessoa.PessoaFisica.FotoPrincipal != null)
                foto = pessoa.PessoaFisica.FotoPrincipal;

            //if (pessoa.Fotos.Count > 0) {
            //    DL.Pessoas.FotoService.ExcluirFoto(pessoa.PessoaFisica.Profissional.FotoPrincipal.Codigo.Value);
            //}

            foto.Principal = true;
            foto.DataAtualizacao = Calendario.Agora();
            Domain.Pessoas.FotoService.SalvarFotoApi(foto, arquivo);
            return foto;
        }

        public EstabelecimentoProfissional ObterProfissional(int idProfissional, int idEstabelecimento)
        {
            return EstabelecimentoProfissionalRepository.Obter(idProfissional, idEstabelecimento);
        }

        public EstabelecimentoProfissional ObterProfissionalInativo(int idProfissional, int idEstabelecimento)
        {
            return EstabelecimentoProfissionalRepository.ObterInativo(idProfissional, idEstabelecimento);
        }

        public EstabelecimentoProfissional ObterProfissionalPorPessoaFisica(int idPessoaFisica, int idEstabelecimento)
        {
            return EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(idPessoaFisica, idEstabelecimento);
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisPorServicoEstabelecimento(
            int idServicoEstabelecimento)
        {
            return EstabelecimentoProfissionalRepository.ListarPorServicoEstabelecimento(idServicoEstabelecimento);
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(int idCategoriaServico)
        {
            return EstabelecimentoProfissionalRepository.ListarProfissionaisPorCategoriaServico(idCategoriaServico);
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(List<int> lista)
        {
            return EstabelecimentoProfissionalRepository.ListarProfissionaisPorCategoriaServico(lista);
        }

        public Profissional ObterProfissionalPorCpf(string cpf)
        {
            return ProfissionalRepository.ObterPorCpf(cpf);
        }

        public EstabelecimentoProfissional ObterProfissionalPorEmail(string email, int idEstabelecimento)
        {
            return EstabelecimentoProfissionalRepository.Obter(email, idEstabelecimento);
        }

        [TransactionInitRequired]
        public void AssociarServicosDoEstabelecimentoAoProfissional(Estabelecimento estabelecimento,
            PessoaFisica pessoaFisica)
        {
            var idPessoaFisica = pessoaFisica.IdPessoa;
            estabelecimento.ServicosJaAssociadosAoProfissionalBasico = true;
            var dtNascimento = pessoaFisica.DataNascimento;
            var sexo = pessoaFisica.Genero;

            pessoaFisica.Refresh();
            Domain.Pessoas.PessoaService.CopiarTelefonesParoOEstabelecimento(pessoaFisica, estabelecimento);

            var profissional = ProfissionalRepository.ObterPorPessoaFisica(idPessoaFisica);

            if (profissional == null)
            {
                profissional = new Profissional { PessoaFisica = pessoaFisica };

                ProfissionalRepository.SaveOrUpdate(profissional);
                profissional.PessoaFisica.DataNascimento = dtNascimento;
                profissional.PessoaFisica.Genero = sexo;
            }

            EstabelecimentoProfissional estabelecimentoProfissional = null;

            if (profissional.EstabelecimentoProfissionalLista != null)
            {
                estabelecimentoProfissional =
                    profissional.EstabelecimentoProfissionalLista.ToEstabelecimentoProfissional(estabelecimento,
                        profissional);
            }

            if (estabelecimentoProfissional == null)
            {
                estabelecimentoProfissional = EstabelecimentoProfissionalService.CadastrarNovo(estabelecimento,
                    profissional);
            }
            //TRINKS-4998 - Profissional padrão criado ao criar um estabelecimento não tinha acesso à própria
            //agenda

            estabelecimentoProfissional.PodeAcessarMinhaAgenda = true;

            estabelecimentoProfissional.AdicionarPrimeiroServicoPadraoOrdernadoPorCategoriaEServico();
            estabelecimentoProfissional.HorarioTrabalhoLista.Preencher(
                estabelecimentoProfissional.Estabelecimento.HorariosDeFuncionamento, estabelecimentoProfissional);
        }

        [TransactionInitRequired]
        public EstabelecimentoProfissional ManterProfissional(EstabelecimentoProfissional estabelecimentoProfissional,
            String email, Boolean alterouEmailExistente = false,
            bool manterInscricoesDeNotificacoes = true, bool recebeResumoDiarioDoFaturamentoEAgendaFutura = true
            , bool recebeDespesasSemanal = true, bool recebeEstoqueSemanal = true, PessoaFisica pessoaUnificar = null)
        {
            Validar(estabelecimentoProfissional);

            if (ValidationHelper.Instance.IsValid)
            {
                TipoDeEventoEnum ehCadastroNovo = estabelecimentoProfissional.Codigo == 0 ? TipoDeEventoEnum.InclusaoDeProfissional : TipoDeEventoEnum.AlteracaoDeProfissional;
                var ehPrimeiraConta = false;
                var possuiConta = false;
                var profissional = estabelecimentoProfissional.Profissional;
                var pessoaFisica = profissional.PessoaFisica;
                if (estabelecimentoProfissional.Estabelecimento == null)
                    ProfissionalRepository.SaveNew(profissional);
                else
                {
                    if (profissional.IdProfissional == 0)
                    {
                        pessoaFisica.RG = estabelecimentoProfissional.RG;
                        pessoaFisica.OrgaoExpedidorRG =
                            estabelecimentoProfissional.OrgaoExpedidorRG;
                        pessoaFisica.DataExpedicaoRG =
                            estabelecimentoProfissional.DataExpedicaoRG;
                        pessoaFisica.Ctps = estabelecimentoProfissional.Ctps;
                        pessoaFisica.CtpsSerie =
                            estabelecimentoProfissional.CtpsSerie;
                        pessoaFisica.PIS = estabelecimentoProfissional.PIS;
                        pessoaFisica.CodigoIntegracao =
                            estabelecimentoProfissional.CodigoIntegracao;
                        pessoaFisica.EstadoCivil =
                            estabelecimentoProfissional.EstadoCivil;
                    }

                    string senha = null;
                    if (pessoaFisica.Contas.Count == 0)
                    {
                        ehPrimeiraConta = true;
                        if (!String.IsNullOrEmpty(email))
                        {
                            senha = pessoaFisica.GerarNovaConta(email);
                            possuiConta = true;
                        }
                    }
                    else if (alterouEmailExistente)
                    {
                        if (!pessoaFisica.PrimeiraConta.DataUltimoLogin.HasValue)
                            senha = pessoaFisica.PrimeiraConta.GerarNovaSenha();

                        possuiConta = true;
                    }
                    else
                        possuiConta = true;

                    estabelecimentoProfissional.ConfigurarComissoes();

                    if (ehPrimeiraConta && possuiConta)
                    {//se gerou nova conta
                        pessoaFisica.DataNascimento = estabelecimentoProfissional.DataNascimento;
                    }

                    EstabelecimentoProfissionalRepository.SaveOrUpdate(estabelecimentoProfissional);

                    Domain.Pessoas.PessoaJuridicaConfiguracaoNFeService.Salvar(estabelecimentoProfissional.PessoaJuridica);

                    if (possuiConta)
                    {
                        if (ehPrimeiraConta || alterouEmailExistente)
                        {
                            EmailService.EnviarEmailCadastroProfissionalRealizado(
                                pessoaFisica.PrimeiraConta, senha,
                                estabelecimentoProfissional.Estabelecimento);
                        }
                    }

                    estabelecimentoProfissional.Refresh();
                }

                if (!string.IsNullOrWhiteSpace(email))
                    ClienteEstabelecimentoService.TransformarPessoaEmCliente(
                        pessoaFisica, estabelecimentoProfissional.Estabelecimento.IdEstabelecimento, false);
                if (pessoaUnificar != null)
                    Domain.Pessoas.UnificacaoService.Unificar(pessoaUnificar, pessoaFisica);
                else
                    Domain.Pessoas.UnificacaoService.Unificar(pessoaFisica);

                EnviarNotificacaoDeEventoParaIntegracaoComOutrosSistemas(estabelecimentoProfissional, ehCadastroNovo);

                Domain.Pessoas.EstabelecimentoProfissionalService.RealizarPushParaSNS(estabelecimentoProfissional);

                if (manterInscricoesDeNotificacoes)
                {
                    ManterInscricoesEmNotificacoes(estabelecimentoProfissional, recebeResumoDiarioDoFaturamentoEAgendaFutura, recebeDespesasSemanal, recebeEstoqueSemanal);
                }
            }
            else
            {
                Domain.Pessoas.ProfissionalRepository.Clear();
            }

            return estabelecimentoProfissional;
        }

        private void Validar(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var informouCpf = !string.IsNullOrWhiteSpace(estabelecimentoProfissional.Profissional.PessoaFisica.Cpf);
            if (!informouCpf)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("É obrigatório informar o CPF do profissional");
            }

            ValidarProfissionalParceiro(estabelecimentoProfissional);
        }

        public void ManterInscricoesEmNotificacoes(EstabelecimentoProfissional estabelecimentoProfissional, bool recebeResumoDiarioDoFaturamentoEAgendaFutura, bool recebeDespesasSemanal, bool recebeEstoqueSemanal)
        {
            if (!estabelecimentoProfissional.Profissional.PessoaFisica.Email.EmailValido())
            {
                recebeResumoDiarioDoFaturamentoEAgendaFutura = false;
                recebeDespesasSemanal = false;
                recebeEstoqueSemanal = false;
            }

            Domain.Notificacoes.ManterInscricaoEmNotificacaoService.Manter(estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa, estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa, NotificacaoEnum.ResumoDiarioDoFaturamentoEAgendaFutura, Notificacoes.TipoNotificacaoEnum.Email, recebeResumoDiarioDoFaturamentoEAgendaFutura);
            Domain.Notificacoes.ManterInscricaoEmNotificacaoService.Manter(estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa, estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa, NotificacaoEnum.DespesasSemanal, Notificacoes.TipoNotificacaoEnum.Email, recebeDespesasSemanal);
            Domain.Notificacoes.ManterInscricaoEmNotificacaoService.Manter(estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa, estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa, NotificacaoEnum.EstoqueSemanal, Notificacoes.TipoNotificacaoEnum.Email, recebeEstoqueSemanal);
        }

        public void EnviarNotificacaoDeEventoParaIntegracaoComOutrosSistemas(EstabelecimentoProfissional estabelecimentoProfissional, TipoDeEventoEnum tipoDeEventoEnum)
        {
            if (!Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.EstabelecimentoPossuiChaveParaIntegracaoComOutrosSistemas(estabelecimentoProfissional.Estabelecimento))
                return;

            TipoDeAcaoEnum tipoDeAcao = Domain.IntegracaoComOutrosSistemas.NotificacaoDeEventoParaIntegracaoService.TipodeAcaoEnumRetorno(tipoDeEventoEnum);
            var telefones = Domain.Pessoas.TelefoneRepository.Queryable().Where(p => p.IdPessoa == estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa && p.Ativo == true).ToListaString();

            var eventoDeProfissional = new EventoDeManterProfissional()
            {
                Dados = new DadosEventoDeManterProfissional()
                {
                    TipoDeEvento = tipoDeEventoEnum,
                    Action = tipoDeAcao,
                    Apelido = estabelecimentoProfissional.Profissional.PessoaFisica.Apelido,
                    CPF = estabelecimentoProfissional.Profissional.PessoaFisica.Cpf,
                    DataDoEvento = Calendario.Agora().ToIntegracaoLongDateTimeString(),
                    Email = estabelecimentoProfissional.Profissional.PessoaFisica.Email,
                    IdDoEstabelecimento = estabelecimentoProfissional.Estabelecimento.IdEstabelecimento,
                    IdPessoaDoProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa,
                    IdDoProfissional = estabelecimentoProfissional.Profissional.IdProfissional,
                    IdDoProfissionalNoEstabelecimento = estabelecimentoProfissional.Codigo,
                    Nome = estabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                    Telefones = telefones,
                    Funcao = estabelecimentoProfissional.FuncaoProfissional != null ? estabelecimentoProfissional.FuncaoProfissional.Descricao : null,
                    DataDeInicio = estabelecimentoProfissional.DataInicio != null ? estabelecimentoProfissional.DataInicio.ToBrazilianShortDateString() : null,
                    DataFimContrato = estabelecimentoProfissional.DataFimDoContratoDoProfissional != null ? estabelecimentoProfissional.DataFimDoContratoDoProfissional.ToBrazilianShortDateString() : null,
                    FormaDeRelacionamentoProfissional = estabelecimentoProfissional.FormaRelacaoProfissional != null ? estabelecimentoProfissional.FormaRelacaoProfissional.Descricao : null,
                    Endereco = estabelecimentoProfissional.Endereco.Logradouro,
                    Cidade = estabelecimentoProfissional.Endereco.Cidade,
                    Estado = estabelecimentoProfissional.Endereco.UF != null ? estabelecimentoProfissional.Endereco.UF.Nome : null,
                    CEP = estabelecimentoProfissional.Endereco.Cep,
                    RG = estabelecimentoProfissional.RG,
                    DataDeExpedicao = estabelecimentoProfissional.DataExpedicaoRG != null ? estabelecimentoProfissional.DataExpedicaoRG.ToBrazilianShortDateString() : null,
                    OrgaoExpeditor = estabelecimentoProfissional.OrgaoExpedidorRG,
                    CTPS = estabelecimentoProfissional.Ctps,
                    NumeroDeSerie = estabelecimentoProfissional.CtpsSerie,
                    PIS = estabelecimentoProfissional.PIS,
                    EstadoCivil = estabelecimentoProfissional.EstadoCivil != null ? estabelecimentoProfissional.EstadoCivil.Nome : null,
                    Observacoes = estabelecimentoProfissional.ObservacaoSobreProfissional,
                    Sexo = estabelecimentoProfissional.Genero,
                    DataDeNascimento = estabelecimentoProfissional.DataNascimento != null ? estabelecimentoProfissional.DataNascimento.Value.ToDataNascimentoString() : null,
                    CodigoDeIntegracao = estabelecimentoProfissional.CodigoIntegracao
                }
            };
            eventoDeProfissional.Tipo = tipoDeEventoEnum;

            Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.NotificarEventoParaIntegracaoComOutrosSistemas(estabelecimentoProfissional.Estabelecimento, eventoDeProfissional);
        }

        public void ValidarProfissionalParceiro(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            if (!estabelecimentoProfissional.Estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitarProfissionalParceiro)
            {
                return;
            }

            var pessoaJuridica = estabelecimentoProfissional.PessoaJuridica;
            if (pessoaJuridica == null || (pessoaJuridica != null && !pessoaJuridica.Ativo))
                return;

            var tipoNfeParceiro = estabelecimentoProfissional.Estabelecimento.EstabelecimentoConfiguracaoGeral.TipoDeEmissaoNfeComProfissionalParceiro;
            var habilitadoEmissaoParceiroUnificado = Domain.RPS.EmissaoRPSService.EstabelecimentoComToggleHabilitadoParaEmissaoDeParceiroUnificado(estabelecimentoProfissional.Estabelecimento);

            if (tipoNfeParceiro == Statics.TipoNfeProfissionalParceiro.NotaSeparada.Id
                || (pessoaJuridica.EnderecoProprio != null && (pessoaJuridica.EnderecoProprio != null && pessoaJuridica.EnderecoProprio.Cidade == "Rio de Janeiro" || pessoaJuridica.EnderecoProprio.Cidade == "São Paulo" || pessoaJuridica.EnderecoProprio.Cidade == "Campinas" || habilitadoEmissaoParceiroUnificado)))
            {
                if (String.IsNullOrEmpty(pessoaJuridica.CNPJ) ||
                String.IsNullOrEmpty(pessoaJuridica.RazaoSocial) ||
                String.IsNullOrEmpty(pessoaJuridica.NomeFantasia) ||
                //String.IsNullOrEmpty(pessoaJuridica.InscricaoMunicipal) ||
                String.IsNullOrEmpty(pessoaJuridica.InscricaoEstadual))
                    ValidationHelper.Instance.AdicionarItemValidacao("Todos os campos de Dados Gerais são obrigatórios.");
                else if (pessoaJuridica.EnderecoProprio == null || !pessoaJuridica.EnderecoProprio.EstaCompleto())
                    ValidationHelper.Instance.AdicionarItemValidacao("Todos os campos de Endereço são obrigatórios.");
                else if (pessoaJuridica.ConfiguracaoNFe == null)
                    ValidationHelper.Instance.AdicionarItemValidacao("Os dados de Configuração NFe não estão preenchidos corretamente.");
                else if (Domain.Pessoas.PessoaJuridicaConfiguracaoNFeService.ExisteOutroProfissionalParceiroUtilizandoCnpj(estabelecimentoProfissional.Estabelecimento.IdEstabelecimento, pessoaJuridica.CNPJ, pessoaJuridica.IdPessoaJuridica))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("CNPJ informado já está sendo utilizado por outro profissional parceiro.");
                }

                //if (ValidationHelper.Instance.IsValid) {
                //    ValidarGeradorDeArquivoRPS(pessoaJuridica);
                //}
            }
            else
            {
                if (String.IsNullOrEmpty(pessoaJuridica.CNPJ) ||
                String.IsNullOrEmpty(pessoaJuridica.RazaoSocial))
                    ValidationHelper.Instance.AdicionarItemValidacao("Todos os campos de Dados Gerais são obrigatórios.");
            }
        }

        public void ValidarGeradorDeArquivoRPS(PessoaJuridica pessoaJuridica)
        {
            var geradorDeArquivo = GeradorDeArquivoRPS.PorPessoaJuridica(pessoaJuridica);
            try
            {
                geradorDeArquivo.ValidarConfiguracao(pessoaJuridica, RPS.Enums.TipoGerarPara.GerarParaDownload);
            }
            catch (ArgumentException e)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(e);
            }
        }

        [TransactionInitRequired]
        public UsuarioEstabelecimento AssociarComoUsuarioAdministrativo(
            EstabelecimentoProfissional estabelecimentoProfissional,
            AcessoBackoffice acessoBackoffice)
        {
            return
                Domain.Pessoas.ContaService.AssociarContaComEstabelecimento(
                    estabelecimentoProfissional.Estabelecimento,
                    estabelecimentoProfissional.Profissional.PessoaFisica, acessoBackoffice);
        }

        public IList<PeriodoAusencia> ListarAusenciasProfissionalPorIntervaloData(int idProfissionalEstabelecimento,
            IntervaloData intervalo)
        {
            return PeriodoAusenciaRepository.FiltrarPorIntervaloData(idProfissionalEstabelecimento, intervalo);
        }

        public ProfissionalDto ObterProfissionalPorContaEEstabelecimento(int idConta, int idEstabelecimento)
        {            
            var profissionalDto = Domain.Pessoas.EstabelecimentoProfissionalRepository
                .ObterProfissionalPorContaEEstabelecimento(idConta, idEstabelecimento);          

            if (profissionalDto == null)
                return null;

            return profissionalDto;
        }

        public IList<Horario> ListarHorariosProfissionalPorIntervaloData(int idProfissional,
            int idEstabelecimento,
            IntervaloData intervalo)
        {
            return HorarioRepository.FiltrarPorIntervaloData(idProfissional, idEstabelecimento, intervalo);
        }

        public IntervaloDataList ListarIntervalosOcupadosNoPeriodo(int idProfissional, int idEstabelecimento, IntervaloData intervalo)
        {
            return HorarioRepository.ListarIntervalosOcupadosNoPeriodo(idProfissional, idEstabelecimento, intervalo);
        }

        public List<ProfissionalAssistenteDTO> ListaDeAssistentesPorServico(String codigoInternoServico, int idProfissional, int idServicoEstabelecimento, int idEstabelecimento)
        {
            var listaDeAssistenteDTO = new List<ProfissionalAssistenteDTO>();

            var servicoEstabelecimento = idServicoEstabelecimento > 0 ?
                Domain.Pessoas.ServicoEstabelecimentoRepository.Load(idServicoEstabelecimento) :
                Domain.Pessoas.ServicoEstabelecimentoRepository.ObterPorCodigoInterno(codigoInternoServico, idEstabelecimento);

            var listaAssistentes =
                Domain.Pessoas.ProfissionalRepository.ListarAssistentesPorServicoDoProfissional(
                    idEstabelecimento,
                    servicoEstabelecimento.IdServicoEstabelecimento,
                    idProfissional);

            foreach (var item in listaAssistentes)
            {
                var assistenteDTO = new ProfissionalAssistenteDTO();
                assistenteDTO.IdDoProfissional = item.IdProfissional;
                assistenteDTO.IdDoEstabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository
                    .ObterPorProfissionalEEstabelecimento(item.IdProfissional, idEstabelecimento).Codigo;
                assistenteDTO.CodigoInterno = Convert.ToInt32(item.EstabelecimentoProfissionalLista.Where(a => a.Profissional.IdProfissional == item.IdProfissional).FirstOrDefault().CodigoInterno);
                assistenteDTO.Nome = item.PessoaFisica.NomeOuApelido();
                assistenteDTO.NomeMaisCodigoInterno = item.PessoaFisica.NomeOuApelido() + " (" + assistenteDTO.CodigoInterno + ")";
                listaDeAssistenteDTO.Add(assistenteDTO);
            }

            return listaDeAssistenteDTO;
        }

        public IntervaloDataList ListarIntervalosAusenciasProfissionalNoPeriodo(int idProfissionalEstabelecimento, IntervaloData intervalo)
        {
            return PeriodoAusenciaRepository.ListarIntervalosAusenciasProfissionalNoPeriodo(idProfissionalEstabelecimento, intervalo);
        }

        #endregion Métodos Públicos
    }
}