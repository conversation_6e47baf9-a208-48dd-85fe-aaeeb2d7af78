﻿using Elmah;
using Perlink.DomainInfrastructure;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Email;
using Perlink.Shared.Email.Disparador;
using Perlink.Shared.Enums;
using Perlink.Shared.IO.Arquivos.ImplementacoesDeControleDeArquivos;
using Perlink.Shared.Resource;
using Perlink.Shared.Xml;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.Despesas;
using Perlink.Trinks.DTO;
using Perlink.Trinks.Estatistica;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Fotos.Enums;
using Perlink.Trinks.Marketing;
using Perlink.Trinks.NotaFiscalDoConsumidor;
using Perlink.Trinks.Notificacoes;
using Perlink.Trinks.Notificacoes.Enums;
using Perlink.Trinks.PagamentosAntecipados.DTO;
using Perlink.Trinks.PagamentosAntecipados.Enums;
using Perlink.Trinks.PDF;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Repositories;
using Perlink.Trinks.Pessoas.VO;
using Perlink.Trinks.PromotoresDoTrinks;
using Perlink.Trinks.Resources;
using Perlink.Trinks.RPS.Integracao;
using Perlink.Trinks.SugestoesEPedidosDeCompra;
using Perlink.Trinks.SugestoesEPedidosDeCompra.Enums;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.WhatsApp.Enums;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;
using System.Text.RegularExpressions;
using DL = Perlink.Trinks.Domain;
using re = RazorEngine;

namespace Perlink.Trinks.Pessoas.Services
{

    public class EnvioEmailService : BaseService, IEnvioEmailService
    {
        private const string mensagemDeErroIntegracaoIUGU = "Método para envio de e-mail com boleto foi chamado mas integração com IUGU estava desativada";

        #region Propriedades de Apoio

        private static IHorarioRepository HorarioRepository
        {
            get { return Domain.Pessoas.HorarioRepository; }
        }

        #endregion Propriedades de Apoio

        public MailAddress ObterRemetentePeloEnum(RemetenteTrinksEnum remetente)
        {
            MailAddress retorno = null;

            switch (remetente)
            {
                case RemetenteTrinksEnum.Padrao:
                    retorno = ObterRemetentePadrao();
                    break;

                case RemetenteTrinksEnum.Parceiro:
                    retorno = ObterRemetenteParceiro();
                    break;

                case RemetenteTrinksEnum.NaoResponda:
                    retorno = ObterRemetenteNaoRespondaTesteEnvioEmail();
                    break;

                case RemetenteTrinksEnum.Tecnico:
                    retorno = ObterEmailTecnico();
                    break;

                case RemetenteTrinksEnum.Fabrica:
                    retorno = ObterEmailFabrica();
                    break;

                case RemetenteTrinksEnum.Financeiro:
                    retorno = ObterEmailFinanceiro();
                    break;

                case RemetenteTrinksEnum.MudancaPlano:
                    retorno = ObterEmailMudancasPlano();
                    break;

                case RemetenteTrinksEnum.Contato:
                    retorno = ObterEmailDeContato();
                    break;

                case RemetenteTrinksEnum.Atendimento:
                    retorno = ObterEmailDeAtendimento();
                    break;

                case RemetenteTrinksEnum.Vendas:
                    retorno = ObterEmailVendas();
                    break;

                case RemetenteTrinksEnum.ParceiroTrinks:
                    retorno = ObterEmailParceiroTrinks();
                    break;

                case RemetenteTrinksEnum.CopiaInterna:
                    retorno = ObterEmailCopiaInterna();
                    break;

                case RemetenteTrinksEnum.Adicionais:
                    retorno = ObterEmailDosAdicionais();
                    break;

                case RemetenteTrinksEnum.FaleConosco:
                    retorno = ObterEmailFaleConosco();
                    break;

                case RemetenteTrinksEnum.Suporte:
                    retorno = ObterEmailDoSuporte();
                    break;
            }

            return retorno;
        }

        public static MailAddress ObterRemetentePadrao()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailPrincipal;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailPrincipal;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        private static MailAddress ObterRemetenteParceiro()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailParceiro;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailFinanceiro;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        public static MailAddress ObterRemetenteNaoResponda(Estabelecimento estabelecimento)
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailNaoResponda;
            var nomeRemetente = estabelecimento.PessoaJuridica.NomeFantasia;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        public static MailAddress ObterRemetenteNaoRespondaTesteEnvioEmail()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailNaoResponda;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailNaoResponda;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        public static MailAddress ObterEmailTecnico()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailTecnico;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailPrincipal;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        private static MailAddress ObterEmailFabrica()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFabrica;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailPrincipal;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        private static MailAddress ObterEmailFinanceiro()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailPrincipal;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        private static MailAddress ObterEmailMudancasPlano()
        {
            var enderecoRemetente = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailMudancasPlano;
            var nomeRemetente = ConfiguracoesTrinks.EnvioEmail.NomeEmailPrincipal;
            return new MailAddress(enderecoRemetente, nomeRemetente);
        }

        private static MailAddress ObterEmailDeContato()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailContato;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailContato;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailDeAtendimento()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailAtendimento;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailAtendimento;
            return new MailAddress(endereco, nome);
        }

        private static MailAddress ObterEmailVendas()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailVendas;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailVendas;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailParceiroTrinks()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailParceiroTrinks;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailParceiro;
            return new MailAddress(endereco, nome);
        }

        private static MailAddress ObterEmailCopiaInterna()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailCopiaInterna;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailAtendimento;
            return new MailAddress(endereco, nome);
        }

        private static MailAddress ObterEmailDosAdicionais()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailAdicionais;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailAdicionais;
            return new MailAddress(endereco, nome);
        }

        private static MailAddress ObterEmailFaleConosco()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFaleConosco;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailAtendimento;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailDoSuporte()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailSuporte;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailSuporte;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailDoCrossSell()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailCrossSell;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailCrossSell;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailErrosPagarme()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailErrosPagarme;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailErrosPagarme;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailErrosContaDigital()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailErrosContaDigital;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailErrosContaDigital;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailLimitePagamentosRecebedor()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailLimitePagamentosRecebedor;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailLimitePagamentosRecebedor;
            return new MailAddress(endereco, nome);
        }

        public static MailAddress ObterEmailErrosConsumoPacote()
        {
            var endereco = ConfiguracoesTrinks.EnvioEmail.EnderecoEmailErrosConsumoPacote;
            var nome = ConfiguracoesTrinks.EnvioEmail.NomeEmailErrosConsumoPacote;
            return new MailAddress(endereco, nome);
        }

        private static List<MailAddress> ObterEmailsEstabelecimento(Estabelecimento estabelecimento, bool habilitaEnvioParaEmailDoEstabelecimento = true)
        {
            var lista = new List<MailAddress>();
            var emailEstabelecimento = estabelecimento.EmailEstabelecimento();

            if (emailEstabelecimento != null)
            {
                if (emailEstabelecimento.RemoverAcentos().EmailValido() && habilitaEnvioParaEmailDoEstabelecimento)
                    lista.Add(new MailAddress(emailEstabelecimento.RemoverAcentos()));
            }
            else
            {
                foreach (var responsavel in estabelecimento.VinculoUsuarios)
                {
                    var email = responsavel.PessoaFisica.PrimeiraConta.Email.RemoverAcentos();
                    if (email.EmailValido())
                        lista.Add(new MailAddress(email));
                }
            }

            return lista;
        }

        private static List<MailAddress> ObterEmailsResponsaveisEstabelecimento(Estabelecimento estabelecimento, bool nomeCompleto = true)
        {
            var lista = new List<MailAddress>();

            foreach (var responsavel in
                estabelecimento.VinculoUsuarios.Where(f => f.EhResponsavel && f.Ativo && f.PessoaFisica.Contas.Any()))
            {
                var email = responsavel.PessoaFisica.PrimeiraConta.Email.RemoverAcentos();
                if (email.EmailValido())
                {
                    var nomeResponsavel = nomeCompleto ? responsavel.PessoaFisica.NomeCompleto : responsavel.PessoaFisica.NomeReduzido();
                    lista.Add(new MailAddress(email, nomeResponsavel));
                }
            }

            return lista;
        }

        private static List<MailAddress> ObterEmailsDosResponsaveisPelaFranquia(int idFranquia)
        {
            var lista = new List<MailAddress>();
            var listaEmails = Domain.Pessoas.ContaFranquiaRepository.ListarContasDeAdministradoresDaFranquia(idFranquia, apenasQueRecebemEmailSobrePedidoEnviado: true);
            foreach (var responsavel in listaEmails)
            {
                var email = responsavel.Email;
                if (email.EmailValido())
                {
                    var nomeResponsavel = responsavel.Pessoa.PessoaFisica.NomeCompleto;
                    lista.Add(new MailAddress(email, nomeResponsavel));
                }
            }
            return lista;
        }

        private static void ObterTextoEUrlDoAplicativo(ClienteEstabelecimento clienteEstabelecimento, EnvioEmailInformacaoDoAplicativo informacoesDoAplicativo)
        {
            Estabelecimento estabelecimento = clienteEstabelecimento.Estabelecimento;
            ObterTextoEUrlDoAplicativo(estabelecimento, informacoesDoAplicativo);
        }

        private static void ObterTextoEUrlDoAplicativo(Estabelecimento estabelecimento, EnvioEmailInformacaoDoAplicativo informacoesDoAplicativo)
        {
            FranquiaEstabelecimento franquiaEstabelecimento = estabelecimento.FranquiaEstabelecimento;
            HotsiteEstabelecimento hotsite = estabelecimento.Hotsite();
            ObterTextoEUrlDoAplicativo(franquiaEstabelecimento, hotsite, informacoesDoAplicativo);
        }

        private static void ObterTextoEUrlDoAplicativo(FranquiaEstabelecimento franquiaEstabelecimento, HotsiteEstabelecimento hotsite, EnvioEmailInformacaoDoAplicativo informacoesDoAplicativo)
        {
            if (franquiaEstabelecimento != null && franquiaEstabelecimento.Ativo && franquiaEstabelecimento.Franquia.TemAplicativoPersonalizado)
            {
                if (hotsite.PermiteAgendamentoHotsite)
                    informacoesDoAplicativo.TextoAplicativo = "<span style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px;'>Baixe o aplicativo <span style='font-family: Arial, Helvetica, sans-serif; color: #0d5ea9; text-align: left; font-weight: normal; font-size: 14px;'>" + franquiaEstabelecimento.Franquia.Nome + "</span> gratuitamente para marcar seus horários e acompanhar seus agendamentos pelo smartphone ou tablet, de onde estiver!</span>";
                else
                    informacoesDoAplicativo.TextoAplicativo = "<span style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px;'>Baixe o aplicativo <span style='font-family: Arial, Helvetica, sans-serif; color: #0d5ea9; text-align: left; font-weight: normal; font-size: 14px;'>" + franquiaEstabelecimento.Franquia.Nome + "</span> gratuitamente para nos acompanhar pelo smartphone ou tablet, de onde estiver!</span>";

                informacoesDoAplicativo.UrlAppStore = franquiaEstabelecimento.Franquia.UrlDownloadAppIOS;
                informacoesDoAplicativo.UrlPlayStore = franquiaEstabelecimento.Franquia.UrlDownloadAppAndroid;
            }
            else
            {
                if (hotsite.PermiteAgendamentoHotsite)
                    informacoesDoAplicativo.TextoAplicativo = "<span style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px;'>Baixe o aplicativo do <span style='font-family: Arial, Helvetica, sans-serif; color: #0d5ea9; text-align: left; font-weight: normal; font-size: 14px;'>Trinks.com</span> gratuitamente para marcar seus horários e acompanhar seus agendamentos pelo smartphone ou tablet, de onde estiver!</span>";
                else
                    informacoesDoAplicativo.TextoAplicativo = "<span style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px;'>Baixe o aplicativo do <span style='font-family: Arial, Helvetica, sans-serif; color: #0d5ea9; text-align: left; font-weight: normal; font-size: 14px;'>Trinks.com</span> gratuitamente para nos acompanhar pelo smartphone ou tablet, de onde estiver!</span>";
            }

            informacoesDoAplicativo.UrlAppStore = String.IsNullOrEmpty(informacoesDoAplicativo.UrlAppStore) ? ObterLinkDownloadAppIos() : informacoesDoAplicativo.UrlAppStore;
            informacoesDoAplicativo.UrlPlayStore = String.IsNullOrEmpty(informacoesDoAplicativo.UrlPlayStore) ? ObterLinkDownloadAppAndroid() : informacoesDoAplicativo.UrlPlayStore;
        }

        private static string ObterTextoConclusaoDeCadastroParaClienteBalcao(ClienteEstabelecimento clienteEstabelecimento)
        {
            if (clienteEstabelecimento.Estabelecimento.Hotsite().PermiteAgendamentoHotsite)
                return "Para que você possa marcar seus próximos horários pela internet, conclua o seu cadastro:";
            else
                return "Conclua seu cadastro para que possamos ter suas informações sempre atualizadas:";
        }

        private static string ObterTextoSobreTrinksParaClienteBalcao(ClienteEstabelecimento clienteEstabelecimento)
        {
            if (clienteEstabelecimento.Estabelecimento.Hotsite().PermiteAgendamentoHotsite)
                return "O trinks.com é um serviço gratuito que possibilita que você marque seus horários em Salões, Spas e Clinicas de Estética com toda a facilidade e praticidade que você merece";
            else
                return "O Trinks.com é um serviço gratuito que possibilita que você acompanhe seus Salões, Spas e Clinicas de Estética com toda a facilidade e praticidade que você merece";
        }

        private static List<KeyValuePair<MailAddress, string>> ObterEmailsESexoResponsaveisEstabelecimento(
            Estabelecimento estabelecimento)
        {
            var lista = new List<KeyValuePair<MailAddress, string>>();

            foreach (var responsavel in
                estabelecimento.VinculoUsuarios.Where(f => f.EhResponsavel && f.Ativo && f.PessoaFisica.Contas.Any()))
            {
                var email = responsavel.PessoaFisica.PrimeiraConta.Email.RemoverAcentos();
                if (email.EmailValido())
                {
                    lista.Add(
                        new KeyValuePair<MailAddress, string>(
                            new MailAddress(email, responsavel.PessoaFisica.NomeCompleto), responsavel.PessoaFisica.Genero));
                }
            }

            return lista;
        }

        private static string ObterLinkDownloadAppIos()
        {
            return new ParametrosTrinks<String>(ParametrosTrinksEnum.link_download_app_ios).ObterValor();
        }

        private static string ObterLinkDownloadAppAndroid()
        {
            return new ParametrosTrinks<String>(ParametrosTrinksEnum.link_download_app_android).ObterValor();
        }

        private static string ObterSufixoCompraCreditosCampanhasMarketing()
        {
            return new ParametrosTrinks<String>(ParametrosTrinksEnum.sufixo_url_compra_credito_campanha_marketing).ObterValor();
        }

        #region E-mail Pre Cadastro de Estabelecimento

        public void EnviarEmailPreCadastroEstabelecimento(EstabelecimentoPreCadastro estabelecimentoPreCadastro)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailPreCadastro;
            var corpo = ObterCorpoEmailPreCadastroEstabelecimento(estabelecimentoPreCadastro);

            var remetente = ObterEmailDeAtendimento();
            var destino = new List<MailAddress>();
            destino.Add(remetente);

            if (!SimulationTool.Current.EhSimulacao)
            {
                IDisparadorEmail emailClient = ObterDisparadorEmailAwsSeHabilitado();
                emailClient.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        public string ObterCorpoEmailPreCadastroEstabelecimento(EstabelecimentoPreCadastro estabelecimentoPreCadastro)
        {
            var caminhoTemplate = "EstabelecimentoPreCadastro.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = string.Format(corpo, estabelecimentoPreCadastro.Nome, estabelecimentoPreCadastro.Telefones, estabelecimentoPreCadastro.Email);

            return corpo;
        }

        #endregion E-mail Pre Cadastro de Estabelecimento

        #region E-mail Bloqueio de Conta

        public void EnviarEmailContaBloqueada(Conta conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaBloqueada;
            var corpo = ObterCorpoEmailContaBloqueada(conta);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            destino.Add(remetente);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public string ObterCorpoEmailContaBloqueada(Conta conta)
        {
            var caminhoTemplate = "ContaBloqueada.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = string.Format(corpo, conta.Email, Calendario.Agora());

            return corpo;
        }

        #endregion E-mail Bloqueio de Conta

        #region E-mail Ativação

        //public void EnviarEmailAtivacaoCadastro(Conta conta) {
        //    var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAtivacaoCadastro;
        //    var corpo = ObterCorpoEmailAtivacaoCadastro(conta);

        //    var remetente = ObterRemetentePadrao();
        //    var destino = new List<MailAddress>();
        //    if (!conta.Email.EmailValido())
        //        return;
        //    destino.Add(new MailAddress(conta.Email));

        //    if (!SimulationTool.Current.EhSimulacao) {
        //        IDisparadorEmail emailClient = ObterDisparadorEmailAwsSeHabilitado();
        //        emailClient.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        //    }
        //}

        //public void EnviarEmailAtivacaoCadastroCliente(Conta conta, bool origemAgendamentoHotsite, int codigoEstabelecimentoRedirecionar = 0, int? codigoEstabelecimento = null) {
        //    var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAtivacaoCadastro;
        //    var corpo = ObterCorpoEmailAtivacaoCadastroCliente(conta, origemAgendamentoHotsite, codigoEstabelecimentoRedirecionar, codigoEstabelecimento: codigoEstabelecimento);

        //    var remetente = ObterRemetentePadrao();
        //    var destino = new List<MailAddress>();
        //    if (!conta.Email.EmailValido())
        //        return;
        //    destino.Add(new MailAddress(conta.Email));

        //    if (!SimulationTool.Current.EhSimulacao) {
        //        IDisparadorEmail emailClient = ObterDisparadorEmailAwsSeHabilitado();
        //        emailClient.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        //    }
        //}

        //private string ObterCorpoEmailAtivacaoCadastro(Conta conta) {
        //    var urlConfirmacaoRecebimento = ObterUrlAtivacaoCadastro(conta.Guid);
        //    var caminhoTemplate = "AtivacaoCadastroNovo.cshtml";
        //    var nome = conta.Pessoa.PessoaFisica.NomeCompleto;
        //    var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

        //    corpo = string.Format(corpo, urlConfirmacaoRecebimento, conta.ObterOuGerarCodigoConfirmacao(), nome);

        //    return corpo;
        //}

        //private string ObterCorpoEmailAtivacaoCadastroCliente(Conta conta, bool origemAgendamentoHotsite = false, int codigoEstabelecimentoRedirecionar = 0, int? codigoEstabelecimento = null) {
        //    var urlConfirmacaoRecebimento = ObterUrlAtivacaoCadastroCliente(conta.Guid, codigoEstabelecimentoRedirecionar);
        //    var caminhoTemplate = origemAgendamentoHotsite
        //        ? "AtivacaoCadastroClientePorAgendamentoHotsite.cshtml"
        //        : "AtivacaoCadastroCliente.cshtml";
        //    var nome = conta.Pessoa.PessoaFisica.NomeCompleto;
        //    var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

        //    var Estabelecimento = codigoEstabelecimento != null ? Domain.Pessoas.EstabelecimentoRepository.ObterPorId(codigoEstabelecimento.Value) : null;
        //    var nomeEstabelecimento = Estabelecimento != null ? Estabelecimento.NomeDeExibicaoNoPortal : "";
        //    var artigo = Estabelecimento != null ? (Estabelecimento.TipoEstabelecimento != null ? (Estabelecimento.TipoEstabelecimento.Genero != null ? (Estabelecimento.TipoEstabelecimento.Genero == "M" ? "o" : "a") : "o") : "o") : "o";
        //    var fraseDeConfirmacao = codigoEstabelecimento != null && origemAgendamentoHotsite ? "Falta pouco para você finalizar o seu cadastro n" + artigo + " " + nomeEstabelecimento + " e ficar nos Trinks!" : "Falta pouco para você finalizar o seu cadastro e ficar nos Trinks!";
        //    string urlLogoEstabelecimento = ObterTrechoDeImagensParaTopoDoEmail(Estabelecimento);

        //    corpo = string.Format(corpo, urlConfirmacaoRecebimento, conta.ObterOuGerarCodigoConfirmacao(), nome, fraseDeConfirmacao, urlLogoEstabelecimento);

        //    return corpo;
        //}

        //private string ObterUrlAtivacaoCadastro(string guid) {
        //    return "portal/CadastroEstabelecimento/ConfirmarConta?guid=" + guid;
        //}

        //private string ObterUrlAtivacaoCadastroCliente(string guid, int codigoEstabelecimentoRedirecionar) {
        //    return "portal/Cliente/ConfirmarConta?guid=" + guid + "&CodigoEstabelecimentoRedirecionar=" + codigoEstabelecimentoRedirecionar;
        //}

        #endregion E-mail Ativação

        #region E-mail Confirmação de Cadastro

        //public void EnviarEmailConfirmacaoCadastro(Conta conta, Estabelecimento estabelecimentoCadastrado) {
        //    var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailConfirmacaoCadastro;
        //    var corpo = ObterCorpoEmailConfirmacaoCadastro(conta, estabelecimentoCadastrado);

        //    var remetente = ObterRemetentePadrao();
        //    var destino = new List<MailAddress>();
        //    if (!conta.Email.EmailValido())
        //        return;
        //    destino.Add(new MailAddress(conta.Email));

        //    if (!SimulationTool.Current.EhSimulacao)
        //        EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        //}

        //private string ObterCorpoEmailConfirmacaoCadastro(Conta conta, Estabelecimento estabelecimentoCadastrado) {
        //    var urlConfirmacaoRecebimento = ObterUrlAtivacaoCadastro(conta.Guid);
        //    var caminhoTemplate = "ConfirmacaoCadastro.cshtml";
        //    var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

        //    var nome = conta.Pessoa.PessoaFisica.NomeCompleto;
        //    var email = conta.Pessoa.PessoaFisica.Email;
        //    var urlHotsite = "";

        //    var configuracaoHotsite = estabelecimentoCadastrado.Hotsite();
        //    var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
        //            ? String.Empty
        //            : "/" + configuracaoHotsite.Url;

        //    urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

        //    corpo = string.Format(corpo, nome, urlHotsite, email);

        //    return corpo;
        //}

        #endregion E-mail Confirmação de Cadastro

        #region E-mail Cadastro de Profissional

        public void EnviarEmailCadastroProfissionalRealizado(Conta conta, string senha, Estabelecimento estabelecimento)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCadastroProfissionalRealizado;
            var corpo = ObterCorpoEmailCadastroProfissionalRealizado(conta, senha, estabelecimento);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (!conta.Email.EmailValido())
                return;
            destino.Add(new MailAddress(conta.Email));

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoEmailCadastroProfissionalRealizado(Conta conta,
            string senha,
            Estabelecimento estabelecimento)
        {
            var caminhoTemplate = "CadastroProfissionalRealizado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var enderecoString = String.Empty;
            if (estabelecimento.PessoaJuridica.EnderecoProprio != null)
                enderecoString = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var nomeFantasia = estabelecimento.PessoaJuridica.NomeFantasia;
            var razaoSocial = estabelecimento.PessoaJuridica.RazaoSocial;
            var nomeProfissional = conta.Pessoa.PessoaFisica.NomeCompleto;

            corpo = string.Format(corpo, nomeFantasia, conta.Email, senha, razaoSocial, enderecoString,
                estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista(), nomeProfissional);

            return corpo;
        }

        #endregion E-mail Cadastro de Profissional

        #region E-mail Senha da Conta Alterada

        public void EnviarEmailParaAdministrativoSenhaAlterada(Conta conta, String usuarioLogado)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailSenhaAlterada;
            var corpo = ObterCorpoEmailParaAdministrativoSenhaAlterada(conta, usuarioLogado);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            destino.Add(ObterRemetentePadrao());

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailSenhaAlterada(Conta conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailSenhaAlterada;
            var corpo = ObterCorpoEmailSenhaAlterada(conta);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (!conta.Email.RemoverAcentos().EmailValido())
                return;
            destino.Add(new MailAddress(conta.Email.RemoverAcentos()));

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoEmailParaAdministrativoSenhaAlterada(Conta conta, String usuarioLogado)
        {
            var caminhoTemplate = "SenhaAlteradaAdministrativo.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = String.Format(corpo, usuarioLogado, conta.DataUltimaAtualizacao.ToBrazilianLongDateTimeString(),
                conta.Email, conta.Pessoa.IdPessoa);

            return corpo;
        }

        private string ObterCorpoEmailSenhaAlterada(Conta conta)
        {
            var caminhoTemplate = "SenhaAlterada.cshtml";
            var nome = conta.Pessoa.PessoaFisica.NomeCompleto;
            return String.Format(ObterTextoCompletoDoArquivo(caminhoTemplate), nome);
        }

        #endregion E-mail Senha da Conta Alterada

        #region E-mail Link Recuperar Senha

        public void EnviarEmailRecuperacaoSenha(Conta conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailRecuperacaoSenha;
            var corpo = ObterCorpoEmailRecuperacaoSenha(conta);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (!conta.Email.RemoverAcentos().EmailValido())
                return;
            destino.Add(new MailAddress(conta.Email.RemoverAcentos()));

            if (!SimulationTool.Current.EhSimulacao)
            {
                IDisparadorEmail emailClient = ObterDisparadorEmailAwsSeHabilitado();
                emailClient.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        private string ObterCorpoEmailRecuperacaoSenha(Conta conta)
        {
            var caminhoTemplate = "RecuperacaoSenha.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var nome = conta.Pessoa.PessoaFisica.NomeCompleto;
            corpo = String.Format(corpo, ObterUrlRecuperacaoSenha(conta.Guid), nome);
            return corpo;
        }

        private string ObterUrlRecuperacaoSenha(string guid)
        {
            return "Login/RecuperacaoSenha?guid=" + guid;
        }

        #endregion E-mail Link Recuperar Senha

        #region E-mail Link Reativar Conta

        public void EnviarEmailReativacaoConta(Conta conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailReativacaoConta;
            var corpo = ObterCorpoEmailReativacaoConta(conta);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (!conta.Email.RemoverAcentos().EmailValido())
                return;
            destino.Add(new MailAddress(conta.Email.RemoverAcentos()));

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoEmailReativacaoConta(Conta conta)
        {
            var caminhoTemplate = "ReativacaoConta.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = String.Format(corpo, ObterUrlReativarConta(conta.Guid));
            return corpo;
        }

        private string ObterUrlReativarConta(string guid)
        {
            return "Login/RecuperacaoSenha?guid=" + guid;
        }

        #endregion E-mail Link Reativar Conta

        #region E-mail Erro

        public void EnviarEmailErro(string corpo)
        {
            var assunto = ConfiguracoesTrinks.TratamentoErro.AssuntoEmailErro;
            var emailTo = ConfiguracoesTrinks.TratamentoErro.DestinatarioEmailErro;

            if (String.IsNullOrEmpty(emailTo))
                emailTo = "<EMAIL>";

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            destino.Add(new MailAddress(emailTo));

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        #endregion E-mail Erro

        #region E-mail Alteração de Dados de um Agendamento

        public void EnviarEmailAlteracaoDadosAgendamento(Horario horario, HorarioHistorico historico)
        {
            var assunto = String.Format("{0} - {1}", horario.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAlteracaoDadosAgendamento);

            if (ObterListaItensAlteradosHorario(horario, historico) == "Duração")
                return;

            if (!horario.ClienteEstabelecimento.EnviarEmailAgendamentoCliente || !horario.ClienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var corpo = ObterCorpoEmailAlteracaoDadosAgendamento(horario, historico, horario.ClienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horario.Estabelecimento);

            var destino = new List<MailAddress>();

            var email = horario.ClienteEstabelecimento.Cliente.PessoaFisica.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email.RemoverAcentos()));

            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horario.Estabelecimento.IdEstabelecimento);
            var destinosOcultos = ObterEmailsEstabelecimento(horario.Estabelecimento, estabelecimentoRecebeEmailAutomaticoDeAgendamento);

            if (horario.Profissional != null && horario.Profissional.PessoaFisica.PrimeiraConta != null)
            {
                var enviarEmailParaProfissional = false;

                if (horario.Profissional != null && horario.Estabelecimento != null)
                {
                    var estabelecimentoProfissional =
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(horario.Profissional.IdProfissional,
                            horario.Estabelecimento.IdEstabelecimento);

                    enviarEmailParaProfissional = estabelecimentoProfissional.PermiteEnvioEmail;
                }

                if (enviarEmailParaProfissional)
                {
                    if (horario.Profissional.PessoaFisica == null)
                        horario.Profissional.PessoaFisica.Refresh();

                    var emailProfissional = horario.Profissional.PessoaFisica.PrimeiraConta.Email.RemoverAcentos();
                    if (emailProfissional.EmailValido())
                        destinosOcultos.Add(new MailAddress(emailProfissional));
                }
            }

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailAlteracaoDadosAgendamento(Horario horario,
            HorarioHistorico historico,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "AlteracaoDadosAgendamento.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            horario.Refresh();

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;

            var linkCompromissos = ObterUrlBase() + "/Portal/Cliente/MeusCompromissos?idClienteEstabelecimento=" +
                                   CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo);
            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    horario.Estabelecimento.IdEstabelecimento);

            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
                ? String.Empty
                : "/" + configuracaoHotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var nomeFantasia = horario.Estabelecimento.PessoaJuridica.NomeFantasia;
            var endereco = horario.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var telefones = horario.Estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();
            var status = horario.Status.Nome;

            var precoNovo = String.Format("{0} {1}", horario.ServicoEstabelecimento.ObterComplementoDoPreco(true),
                horario.ServicoEstabelecimento.ObterPreco(true));
            var duracaoNova = horario.Duracao.PorExtenso();

            var dataHoraNovo = String.Format("{0} {1}", horario.DataInicio.ObterSiglaDiaDaSemana(),
                horario.DataInicio.ToBrazilianLongDateTimeString());
            var nomeServicoNovo = horario.ServicoEstabelecimento.Nome;
            var observacaoCliente = horario.ObservacaoCliente;

            var alteracoesRealizadas = ObterListaItensAlteradosHorario(horario, historico);
            var duracaoAntiga = historico.Duracao.PorExtenso();

            var dataHoraAntiga = String.Format("{0} {1}", historico.DataInicio.ObterSiglaDiaDaSemana(),
                historico.DataInicio.ToBrazilianLongDateTimeString());

            var nomeServicoAntigo = historico.ServicoEstabelecimento.Nome;
            var observacaoClienteAntigo = historico.ObservacaoCliente;

            var exibirNomeProfissional =
                horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;

            var nomeProfissionalNovo = ObterApelidoOuNomeDoProfissional(horario.Profissional);
            var profissionalNovo = exibirNomeProfissional
                ? string.Format(@"<tr>
                        <td width='160' >
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Profissional:</p>
                        </td>
                        <td width='10'>&nbsp;</td>
                        <td width='350'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p>                                                            </td>
                    </tr>", nomeProfissionalNovo)
                : "";
            var nomeProfissionalAntigo = ObterApelidoOuNomeDoProfissional(historico.Profissional);
            var profissionalAntigo = exibirNomeProfissional
                ? string.Format(@"<tr>
                        <td width='160' >
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Profissional:</p>
                        </td>
                        <td width='10'>&nbsp;</td>
                        <td width='350'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p>                                                            </td>
                    </tr>", nomeProfissionalAntigo)
                : "";

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horario.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            string parteValorDoServico = ObterParteHtmlReferenteAoValorPagoAntecipadamente(horario);

            corpo = String.Format(corpo, nomeCliente, nomeServicoNovo, profissionalNovo, dataHoraNovo, duracaoNova,
                precoNovo, observacaoCliente, status, nomeFantasia, urlHotsite, endereco, telefones, linkCompromissos,
                alteracoesRealizadas, nomeServicoAntigo, dataHoraAntiga, duracaoAntiga, observacaoClienteAntigo,
                urlLogoEstabelecimento, profissionalAntigo, informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore, parteValorDoServico);
            return corpo;
        }

        private string ObterParteHtmlReferenteAoValorPagoAntecipadamente(Horario horario)
        {
            string corpo = string.Empty;
            if (horario.FoiPagoAntecipadamente)
            {
                var valorPagoAntecipadamente = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ObterValorPagoAntecipadamenteParaOServico(horario.Id);
                if (valorPagoAntecipadamente > 0)
                {
                    corpo = string.Format(@"
                    <tr>
                        <td width='160' >
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Preço:</p>
                        </td>
                        <td width='10'>&nbsp;</td>
                        <td width='350'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0} (pago online)</p>
                        </td>
                    </tr>", valorPagoAntecipadamente.ObterValorEmReais());
                }
            }

            return corpo;
        }

        private String ObterListaItensAlteradosHorario(Horario horario, HorarioHistorico historico)
        {
            var itens = new List<string>();

            if (horario.ServicoEstabelecimento.IdServicoEstabelecimento !=
                historico.ServicoEstabelecimento.IdServicoEstabelecimento)
                itens.Add("Serviço");

            if (horario.Profissional != historico.Profissional)
                itens.Add("Profissional");

            if (horario.DataInicio != historico.DataInicio)
                itens.Add("Data e Hora");

            if (horario.Duracao != historico.Duracao)
                itens.Add("Duração");

            return itens.ListaFormatadaComSeparador();
        }

        #endregion E-mail Alteração de Dados de um Agendamento

        #region E-mail Alteração de Agendamento Cancelado

        private void EnviarEmailAgendamentoCancelado(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            if (!horario.ClienteEstabelecimento.EnviarEmailAgendamentoCliente || !horario.ClienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var assunto = String.Format("{0} - {1}", horario.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAgendamentoCancelado);
            var corpo = ObterCorpoEmailAgendamentoCancelado(horario, statusAntigo, clienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horario.Estabelecimento);

            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horario.Estabelecimento.IdEstabelecimento);
            var destinosOcultos = ObterEmailsEstabelecimento(horario.Estabelecimento);

            if (horario.Profissional != null && horario.Profissional.PessoaFisica.PrimeiraConta != null)
            {
                var enviarEmailParaProfissional = false;

                if (horario.Profissional != null && horario.Estabelecimento != null)
                {
                    var estabelecimentoProfissional =
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(horario.Profissional.IdProfissional,
                            horario.Estabelecimento.IdEstabelecimento);

                    enviarEmailParaProfissional = estabelecimentoProfissional.PermiteEnvioEmail;
                }

                if (enviarEmailParaProfissional)
                {
                    if (horario.Profissional.PessoaFisica == null)
                        horario.Profissional.PessoaFisica.Refresh();

                    var emailProfissional = horario.Profissional.PessoaFisica.PrimeiraConta.Email;
                    if (emailProfissional.EmailValido())
                        destinosOcultos.Add(new MailAddress(emailProfissional));
                }
            }

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailAgendamentoCancelado(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "AgendamentoCancelado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var dataHora = String.Format("{0} {1}", horario.DataInicio.ObterSiglaDiaDaSemana(),
                horario.DataInicio.ToBrazilianLongDateTimeString());
            var nomeServico = horario.ServicoEstabelecimento.Nome;
            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    horario.Estabelecimento.IdEstabelecimento);
            var preco = String.Format("{0} {1}", horario.ServicoEstabelecimento.ObterComplementoDoPreco(true),
                horario.ServicoEstabelecimento.ObterPreco(true));
            var duracao = horario.Duracao.PorExtenso();
            var nomeFantasia = horario.Estabelecimento.PessoaJuridica.NomeFantasia;

            var novoStatus = horario.Status.Nome;
            var historicoDoCancelamento = Domain.Pessoas.HorarioHistoricoRepository.ObterHistoricoCancelamentoDoAgendamento(horario.Codigo.Value);
            var quemCancelou = historicoDoCancelamento.HorarioQuemCancelou.Nome;
            var motivoCancelamento = historicoDoCancelamento.MotivoCancelamento;

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horario.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
                ? String.Empty
                : "/" + configuracaoHotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var endereco = horario.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var telefones = horario.Estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var exibirNomeProfissional =
                horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
            var profissional = exibirNomeProfissional
                ? string.Format(@"<tr>
                        <td width='160'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Profissional:</p>
                        </td>
                        <td width='10'>&nbsp;</td>
                        <td width='350'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p>
                        </td>
                    </tr>", ObterApelidoOuNomeDoProfissional(horario.Profissional))
                : "";

            corpo = String.Format(corpo, nomeCliente, nomeFantasia, nomeServico, profissional, dataHora, duracao, preco,
                novoStatus, quemCancelou, motivoCancelamento, urlHotsite, endereco, telefones,
                CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo), urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo,
                informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);
            return corpo;
        }

        #endregion E-mail Alteração de Agendamento Cancelado

        #region E-mail de Agendamentos Cancelados

        public void EnviarEmailAgendamentosCancelado(List<Horario> horarios, ResultadoCancelamentoDePagamentoAntecipado cancelamentoPagamentoAntecipado)
        {
            var horarioBase = horarios.First();
            var clienteEstabelecimento = horarioBase.ClienteEstabelecimento;

            if (!clienteEstabelecimento.EnviarEmailAgendamentoCliente || !clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var assunto = String.Format("{0} - Cancelamento de Horários",
                horarioBase.Estabelecimento.PessoaJuridica.NomeFantasia);
            var corpo = ObterCorpoEmailAgendamentosCancelado(horarios, clienteEstabelecimento, cancelamentoPagamentoAntecipado);

            var remetente = ObterRemetenteNaoResponda(horarioBase.Estabelecimento);
            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horarioBase.Estabelecimento.IdEstabelecimento);
            var destinosOcultos = ObterEmailsEstabelecimento(horarioBase.Estabelecimento, estabelecimentoRecebeEmailAutomaticoDeAgendamento);

            foreach (var horario in horarios)
            {
                if (horario.Profissional != null && horario.Profissional.PessoaFisica.PrimeiraConta != null)
                {
                    var enviarEmailParaProfissional = false;

                    if (horario.Profissional != null && horario.Estabelecimento != null)
                    {
                        var estabelecimentoProfissional =
                            Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(
                                horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);

                        enviarEmailParaProfissional = estabelecimentoProfissional.PermiteEnvioEmail;
                    }

                    if (enviarEmailParaProfissional)
                    {
                        if (horario.Profissional.PessoaFisica == null)
                            horario.Profissional.PessoaFisica.Refresh();

                        var emailProfissional = horario.Profissional.PessoaFisica.PrimeiraConta.Email;
                        if (emailProfissional.EmailValido())
                            destinosOcultos.Add(new MailAddress(emailProfissional));
                    }
                }
            }

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailAgendamentosCancelado(List<Horario> horarios,
            ClienteEstabelecimento clienteEstabelecimento, ResultadoCancelamentoDePagamentoAntecipado cancelamentoPagamentoAntecipado)
        {
            var horarioBase = horarios.First();

            string caminhoTemplate = "CancelamentoDeAgendamentos.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var linkHotSite = String.Empty;

            var preco = horarioBase.Valor.ToString("c");
            var hotsite = clienteEstabelecimento.Estabelecimento.Hotsite();
            var urlHotSiteExistente = String.IsNullOrEmpty(hotsite.Url) ? String.Empty : "/" + hotsite.Url;
            var nomeFantasia = clienteEstabelecimento.Estabelecimento.PessoaJuridica.NomeFantasia;
            if (hotsite != null)
                linkHotSite = ObterUrlBase() + urlHotSiteExistente;

            var ultimoHistorico = Domain.Pessoas.HorarioHistoricoRepository.ObterUltimoHistoricoDoHorario(horarioBase);
            var quemCancelou = ultimoHistorico.HorarioQuemCancelou.Nome;
            var motivoCancelamento = ultimoHistorico.MotivoCancelamento;

            horarioBase.ServicoEstabelecimento.Refresh();

            var exibirNomeProfissional =
                clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
            var tabelaHorarios =
                "<table width='520' border='0' cellspacing='0' cellpadding='0' style='font-family:Arial, Helvetica, sans-serif;'>" +
                "<tr style='background:#F7993A; border:1px solid #e4e4e4; color:#fff; font-size:12px;'>" +
                "<th style='padding:3px;'>&nbsp;</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Serviço</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Data</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Hora</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Duração</th>" +
                (exibirNomeProfissional ? "<th style='border-left:1px solid #fff; padding:3px;'>Profissional</th>" : "") +
                "<th style='border-left:1px solid #fff; padding:3px;'>Status</th>" + "</tr>";

            var count = 1;
            foreach (var h in horarios)
            {
                tabelaHorarios +=
                    String.Format(
                        "<tr style='font-size:12px'>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{0}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{1}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{2} {3}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{4}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{5}</td>" +
                        (exibirNomeProfissional ? "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{6}</td>" : "") +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; text-align:center;'>Cancelado</td>" +
                        "</tr>", count, h.ServicoEstabelecimento.Nome, h.DataInicio.DayOfWeek.ToDiaSemanaString(),
                        h.DataInicio.ToShortDateString(), h.DataInicio.ToShortTimeString(), h.Duracao.PorExtenso(),
                       ObterApelidoOuNomeDoProfissional(h.Profissional));
                count++;
            }
            tabelaHorarios += "</table>";

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horarioBase.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            string parteInformacoesEstornoPagamentosAntecipados = ObterParteHtmlComInformacoesDoEstornoDePagamentosAntecipadosDosAgendamentosCancelados(cancelamentoPagamentoAntecipado, nomeFantasia);

            corpo = String.Format(corpo, nomeCliente, nomeFantasia, linkHotSite,
                horarioBase.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                horarioBase.Estabelecimento.PessoaJuridica.Telefones.Ativos().ToTextoFormatadoLista(),
                horarioBase.ServicoEstabelecimento.Nome, horarioBase.Duracao.PorExtenso(), preco, tabelaHorarios,
                quemCancelou, motivoCancelamento, CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo),
                urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore,
                parteInformacoesEstornoPagamentosAntecipados);
            return corpo;
        }

        private string ObterParteHtmlComInformacoesDoEstornoDePagamentosAntecipadosDosAgendamentosCancelados(ResultadoCancelamentoDePagamentoAntecipado cancelamentoPagamentoAntecipado, string nomeFantasiaDoEstabelecimento)
        {
            if (cancelamentoPagamentoAntecipado.AcaoExecutada == AcaoExecutadaAoCancelarPagamentoAntecipadoEnum.Nenhuma)
                return string.Empty;

            string mensagem = cancelamentoPagamentoAntecipado.AcaoExecutada == AcaoExecutadaAoCancelarPagamentoAntecipadoEnum.EstornadoDiretamenteAoCliente
                ? $"O estorno do seu pagamento no valor de {cancelamentoPagamentoAntecipado.ValorMovimentado.ObterValorEmReais()} estará disponível em um prazo de 3 a 7 dias úteis de acordo com o fechamento de sua fatura."
                : $"O reembolso do seu pagamento no valor de {cancelamentoPagamentoAntecipado.ValorMovimentado.ObterValorEmReais()} está disponível em crédito para ser utilizado no(a) {nomeFantasiaDoEstabelecimento} quando quiser.";

            var corpo = @"
            <tr>
                <td>
                    <table width='520' border='0' cellspacing='0' cellpadding='0' style='background: #f2f2f2; border: 1px solid #e4e4e4;'>
                        <tr>
                            <td>
                                <p style='font-family: Arial, Helvetica, sans-serif; font-size: 12px; color: #474747; margin: 10px 0; text-align: center; font-weight: bold;'>
                                    {0}
                                </p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td height='15'></td>
            </tr> ";

            corpo = string.Format(corpo, mensagem);

            return corpo;
        }

        #endregion E-mail de Agendamentos Cancelados

        #region E-mail Alteração de Status de um Agendamento

        public void EnviarEmailAlteracaoStatusAgendamento(Horario horario, StatusHorario statusAntigo)
        {
            if (!horario.ClienteEstabelecimento.EnviarEmailAgendamentoCliente || !horario.ClienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            switch (horario.Status.Codigo)
            {
                case (int)StatusHorarioEnum.Cliente_Faltou:
                    Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificacaoClienteFaltou(horario, statusAntigo);
                    //EnviarEmailAlteracaoStatusClienteFaltou(horario, statusAntigo, horario.ClienteEstabelecimento);
                    break;

                case (int)StatusHorarioEnum.Cancelado:
                    EnviarEmailAgendamentoCancelado(horario, statusAntigo, horario.ClienteEstabelecimento);
                    break;

                case (int)StatusHorarioEnum.Confirmado:
                    if (statusAntigo.Codigo == (int)StatusHorarioEnum.Cancelado)
                    {
                        Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificacaoReativacaoAgendamento(horario);
                    }
                    else
                    {
                        Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificaoConfirmacaoAgendamento(horario);
                    }

                    //EnviarEmailAgendamentoMarcado(horario);
                    break;

                case (int)StatusHorarioEnum.Aguardando_Confirmacao:
                    /*case (int) StatusHorarioEnum.Finalizado:*/
                    EnviarEmailGenericoDeAlteracaoDeStatus(horario, statusAntigo, horario.ClienteEstabelecimento);
                    break;
            }
        }

        private void EnviarEmailGenericoDeAlteracaoDeStatus(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var assunto = String.Format("{0} - {1}", horario.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAlteracaoStatusAgendamento);
            var corpo = ObterCorpoEmailAlteracaoStatusAgendamento(horario, statusAntigo, clienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horario.Estabelecimento);

            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null,
                        replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailAlteracaoStatusAgendamento(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "AlteracaoStatusAgendamento.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var dataHora = String.Format("{0} {1}", horario.DataInicio.ObterSiglaDiaDaSemana(),
                horario.DataInicio.ToBrazilianLongDateTimeString());
            var nomeServico = horario.ServicoEstabelecimento.Nome;
            var nomeProfissional = horario.Profissional != null ? horario.Profissional.PessoaFisica.NomeCompleto : Textos.FilaDeEspera;
            var apelidoProfissional = horario.Profissional != null ? horario.Profissional.PessoaFisica.ObterApelidoFormatado() : "";

            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    horario.Estabelecimento.IdEstabelecimento);

            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
                ? String.Empty
                : "/" + configuracaoHotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var preco = String.Format("{0} {1}", horario.ServicoEstabelecimento.ObterComplementoDoPreco(true),
                horario.ServicoEstabelecimento.ObterPreco(true));
            var duracao = horario.Duracao.PorExtenso();
            var nomeFantasia = horario.Estabelecimento.PessoaJuridica.NomeFantasia;
            var endereco = horario.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var novoStatus = horario.Status.Nome;
            var observacoesCliente = horario.ObservacaoCliente;

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horario.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var exibirNomeProfissional =
                horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
            var profissional = exibirNomeProfissional ? string.Format(@"<tr>
                    <td width='160'>
                        <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>
                            Profissional:
                        </p>
                    </td>
                    <td width='10'>&nbsp;
                    </td>
                    <td width='350'>
                        <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0} {1}</p>
                    </td>
                </tr>", nomeProfissional, apelidoProfissional) : "";

            corpo = String.Format(corpo, nomeCliente, dataHora, nomeServico,
                profissional, preco, duracao, nomeFantasia, endereco, statusAntigo.Nome,
                novoStatus, observacoesCliente, urlHotsite, urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore,
                informacoesDoAplicativo.UrlPlayStore);
            return corpo;
        }

        #endregion E-mail Alteração de Status de um Agendamento

        #region E-mail Comprovante Pagamento Belezinha

        public void EnviarEmailComprovantePagamentoBelezinha(Transacao transacao)
        {
            var assunto = String.Format(ConfiguracoesTrinks.EnvioEmail.AssuntoEmailComprovantePagamentoBelezinha,
                 transacao.PessoaQueRecebeu.NomeFantasia);

            var corpo = ObterCorpoEmailComprovantePagamentoBelezinha(transacao);
            var remetente = ObterRemetenteNaoResponda(transacao.PessoaQueRecebeu.Estabelecimento);
            var destino = new List<MailAddress>();

            var email = transacao.PessoaQuePagou.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var destinosOcultos = ObterEmailsEstabelecimento(transacao.PessoaQueRecebeu.Estabelecimento);

            var replyToAddress = new List<MailAddress>();
            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailComprovantePagamentoBelezinha(Transacao transacao)
        {
            string caminhoTemplate;

            caminhoTemplate = ObterTemplateEmailComprovantePagamentoBelezinha();
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento;

            //Informações do Corpo do Email
            //{0} nome do cliente
            var nomeClienteEstabelecimento = transacao.PessoaQuePagou.NomeOuApelido();

            //{1} dados do estabelecimento e link para hotsite - var dadosEstabelecimento
            var nomeEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;
            var possuiAplicativoProprio = PossuiAplicativoProprio(estabelecimento);

            //{2} texto aplicativo
            string textoAplicativo = ObterTextoAplicativo(estabelecimento, possuiAplicativoProprio);

            //{3} url para download app redirecionamento automático
            string urlDownloadApp = ObterURLAplicativo(estabelecimento, possuiAplicativoProprio);

            //{4} nome estabelecimento para p link do app
            var nomeEstabelecimentoParaLinkApp = ObterNomeParaLinkApp(estabelecimento);

            //{5} logo do estabelecimento / url logo estabelecimento
            var logoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.Obter(estabelecimento);
            var urlLogoEstabelecimento = logoEstabelecimento != null
                ? Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(logoEstabelecimento, DimemsoesFotosEnum.Dim243x92)
                : string.Empty;

            //{6} dados de pagamento belezinha
            var cupomBelezinhaDTO = Domain.Financeiro.CupomBelezinhaService.ObterCupomBelezinhaDTO(transacao.Id);

            var tagLogoEstabelecimento = string.Empty;
            if (!string.IsNullOrEmpty(urlLogoEstabelecimento))
                tagLogoEstabelecimento = string.Format("<img src='{0}{1}' height='92' alt='Logo do estabelecimento' />", ObterUrlBase(), urlLogoEstabelecimento);

            corpo = String.Format(corpo,
                             nomeClienteEstabelecimento, //0
                             textoAplicativo,//1
                             urlDownloadApp,//2
                             nomeEstabelecimentoParaLinkApp,//3
                             tagLogoEstabelecimento //4
                             );

            return re.Razor.Parse(corpo, cupomBelezinhaDTO, caminhoTemplate); // Sempre preencher cacheName
        }

        private string ObterTemplateEmailComprovantePagamentoBelezinha()
        {
            return "ComprovantePagamentoBelezinha.cshtml";
        }

        #endregion E-mail Comprovante Pagamento Belezinha

        #region E-mail Agendamento Marcado

        public void EnviarEmailAgendamentoMarcado(Horario horario)
        {
            //É necessário realizar um refresh no cliente para
            //obter um ClienteEstabelecimento criado durante o agendamento.
            horario.Refresh();
            horario.Cliente.Refresh();

            var assunto = String.Format("{0} - {1}", horario.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAgendamentoMarcado);
            var corpo = ObterCorpoEmailAgendamentoMarcado(horario, horario.ClienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horario.Estabelecimento);

            var destino = new List<MailAddress>();

            if (horario.ClienteEstabelecimento.EnviarEmailAgendamentoCliente && horario.ClienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
            {
                var email = horario.ClienteEstabelecimento.DadosDoCliente.Email;
                if (email.EmailValido())
                    destino.Add(new MailAddress(email));
            }
            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horario.Estabelecimento.IdEstabelecimento);
            var destinosOcultos = ObterEmailsEstabelecimento(horario.Estabelecimento, estabelecimentoRecebeEmailAutomaticoDeAgendamento);

            if (horario.Profissional != null && horario.Profissional.PessoaFisica.PrimeiraConta != null)
            {
                var enviarEmailParaProfissional = false;

                if (horario.Profissional != null && horario.Estabelecimento != null)
                {
                    var estabelecimentoProfissional =
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(horario.Profissional.IdProfissional,
                            horario.Estabelecimento.IdEstabelecimento);

                    enviarEmailParaProfissional = estabelecimentoProfissional.PermiteEnvioEmail;
                }

                if (enviarEmailParaProfissional)
                {
                    if (horario.Profissional.PessoaFisica == null)
                        horario.Profissional.PessoaFisica.Refresh();

                    var emailProfissional = horario.Profissional.PessoaFisica.PrimeiraConta.Email;
                    if (emailProfissional.EmailValido())
                        destinosOcultos.Add(new MailAddress(emailProfissional));
                }
            }

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    try
                    {
                        EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                            null, false, replyToAddress.ToArray());
                    }
                    catch (FileLoadException)
                    {
                    }
                }
            }
        }

        private string ObterCorpoEmailAgendamentoMarcado(Horario horario, ClienteEstabelecimento clienteEstabelecimento)
        {
            horario.Refresh();
            string caminhoTemplate;

            caminhoTemplate = ObterTemplateEmailAgendamento(horario.Status.Descricao);
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var linkHotSite = String.Empty;
            var linkMeusCompromissos = ObterUrlBase() + "/MinhaArea/MeusCompromissos";

            var preco = String.Format("{0} {1}", horario.ServicoEstabelecimento.ObterComplementoDoPreco(true),
                horario.ServicoEstabelecimento.ObterPreco(true));

            var hotsite = horario.Estabelecimento.Hotsite();
            var urlHotSiteExistente = String.IsNullOrEmpty(hotsite.Url) ? String.Empty : "/" + hotsite.Url;
            var nomeFantasia = horario.Estabelecimento.PessoaJuridica.NomeFantasia;
            if (hotsite != null)
                linkHotSite = ObterUrlBase() + urlHotSiteExistente;


            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horario.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var exibirNomeProfissional = horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;

            var profissional = exibirNomeProfissional
                ? string.Format(@"<tr><td width='160'>
                <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>
                Profissional:
                </p>
                </td>
                <td width='10'>&nbsp;
                </td>
                <td width='350'>
                <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p></td></tr>",
                    ObterApelidoOuNomeDoProfissional(horario.Profissional))
                : "";

            corpo = String.Format(corpo, nomeCliente, horario.ServicoEstabelecimento.Nome, profissional,
                String.Format("{0} {1}", horario.DataInicio.ObterSiglaDiaDaSemana(),
                    horario.DataInicio.ToString("dd/MM/yyyy")), horario.DataInicio.ToString("HH:mm"),
                horario.Duracao.PorExtenso(), preco, horario.ObservacaoCliente, horario.Status.Nome, nomeFantasia,
                linkHotSite, horario.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                horario.Estabelecimento.PessoaJuridica.Telefones.Ativos().ToTextoFormatadoListaSemLGPD(),
                CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo), urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo,
                informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore, linkMeusCompromissos);
            return corpo;
        }

        private string ObterTemplateEmailAgendamento(string descricao)
        {
            string caminhoTemplate;
            if (descricao != StatusHorarioEnum.Confirmado.ToString())
                caminhoTemplate = "InformacoesAgendamento.cshtml";
            else
                caminhoTemplate = "AgendamentoConfirmado.cshtml";
            return caminhoTemplate;
        }

        #endregion E-mail Agendamento Marcado

        #region E-mail Agendamento Recorrência

        public void EnviarEmailAgendamentoRecorrenteMarcado(RecorrenciaHorario recorrencia)
        {
            var horarioBase = recorrencia.Horarios.First();
            var clienteEstabelecimento =
                horarioBase.Cliente.ClientesEstabelecimento.ToClienteEstabelecimento(
                    horarioBase.Estabelecimento.IdEstabelecimento);

            if (!clienteEstabelecimento.EnviarEmailAgendamentoCliente || !recorrencia.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var assunto = String.Format("{0} - {1}", recorrencia.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAgendamentoMarcado);
            var corpo = ObterCorpoEmailAgendamentoRecorrenteMarcado(recorrencia, clienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horarioBase.Estabelecimento);

            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horarioBase.Estabelecimento.IdEstabelecimento);
            var destinosOcultos = ObterEmailsEstabelecimento(horarioBase.Estabelecimento);

            if (horarioBase.Profissional != null && horarioBase.Profissional.PessoaFisica.PrimeiraConta != null)
            {
                var enviarEmailParaProfissional = false;

                if (horarioBase.Profissional != null && horarioBase.Estabelecimento != null)
                {
                    var estabelecimentoProfissional =
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(
                            horarioBase.Profissional.IdProfissional, horarioBase.Estabelecimento.IdEstabelecimento);

                    enviarEmailParaProfissional = estabelecimentoProfissional.PermiteEnvioEmail;
                }

                if (enviarEmailParaProfissional)
                {
                    if (horarioBase.Profissional.PessoaFisica == null)
                        horarioBase.Profissional.PessoaFisica.Refresh();

                    var emailProfissional = horarioBase.Profissional.PessoaFisica.PrimeiraConta.Email;
                    if (emailProfissional.EmailValido())
                        destinosOcultos.Add(new MailAddress(emailProfissional));
                }
            }

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailAgendamentoRecorrenteMarcado(RecorrenciaHorario recorrencia,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var horarioBase = recorrencia.Horarios.FirstOrDefault();
            string caminhoTemplate;

            caminhoTemplate = "AgendamentoRecorrencia.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var linkHotSite = String.Empty;
            var preco = horarioBase.Valor.ToString("c");

            var hotsite = clienteEstabelecimento.Estabelecimento.Hotsite();
            var urlHotSiteExistente = String.IsNullOrEmpty(clienteEstabelecimento.Estabelecimento.Hotsite().Url)
                ? String.Empty
                : "/" + hotsite.Url;
            if (hotsite != null)
                linkHotSite = ObterUrlBase() + urlHotSiteExistente;

            horarioBase.ServicoEstabelecimento.Refresh();
            var exibirNomeProfissional =
                recorrencia.Horarios.FirstOrDefault()
                    .Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
            var tabelaHorarios =
                "<table width='520' border='0' cellspacing='0' cellpadding='0' style='font-family:Arial, Helvetica, sans-serif;'>" +
                "<tr style='background:#F7993A; border:1px solid #e4e4e4; color:#fff; font-size:12px;'>" +
                "<th style='padding:3px;'>&nbsp;</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Data</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Hora</th>" +
                (exibirNomeProfissional ? "<th style='border-left:1px solid #fff; padding:3px;'>Profissional</th>" : "") +
                "<th style='border-left:1px solid #fff; padding:3px;'>Status</th>" + "</tr>";

            var count = 1;
            foreach (var h in recorrencia.Horarios)
            {
                tabelaHorarios +=
                    String.Format(
                        "<tr style='font-size:12px'>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{0}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{1} {2}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{3}</td>" +
                        (exibirNomeProfissional ? "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{4}</td>" : "") +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; text-align:center;'>Confirmado</td>" +
                        "</tr>", count, h.DataInicio.DayOfWeek.ToDiaSemanaString(), h.DataInicio.ToShortDateString(),
                        h.DataInicio.ToShortTimeString(), ObterApelidoOuNomeDoProfissional(horarioBase.Profissional));
                count++;
            }
            tabelaHorarios += "</table>";

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horarioBase.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            corpo = String.Format(corpo, nomeCliente, linkHotSite,
                horarioBase.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                horarioBase.Estabelecimento.PessoaJuridica.Telefones.Ativos().ToTextoFormatadoLista(),
                horarioBase.ServicoEstabelecimento.Nome, nomeCliente, horarioBase.Duracao.PorExtenso(), preco,
                recorrencia.TipoRecorrenciaHorario.Nome,
                String.Format("{0} {1}", horarioBase.DataInicio.ObterSiglaDiaDaSemana(),
                    horarioBase.DataInicio.ToBrazilianLongDateTimeString()),
                recorrencia.DataLimite.Value.ToShortDateString(), tabelaHorarios,
                CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo), urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo,
                informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);
            return corpo;
        }

        #endregion E-mail Agendamento Recorrência

        #region E-mail Cancelamento de Recorrência

        public void EnviarEmailCancelamentoDeRecorrenteMarcado(List<Horario> horarios)
        {
            var horarioBase = horarios.First();
            var clienteEstabelecimento = horarioBase.ClienteEstabelecimento;
            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horarioBase.Estabelecimento.IdEstabelecimento);

            if (!clienteEstabelecimento.EnviarEmailAgendamentoCliente || !clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes || !estabelecimentoRecebeEmailAutomaticoDeAgendamento)
                return;

            var assunto = String.Format("{0} - {1}", horarioBase.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCancelamentoRecorrencia);
            var corpo = ObterCorpoEmailCancelamentoDeRecorrenteMarcado(horarios, clienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horarioBase.Estabelecimento);
            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var destinosOcultos = ObterEmailsEstabelecimento(horarioBase.Estabelecimento);

            foreach (var horario in horarios)
            {
                if (horario.Profissional != null && horario.Profissional.PessoaFisica.PrimeiraConta != null)
                {
                    var enviarEmailParaProfissional = false;

                    if (horario.Profissional != null && horario.Estabelecimento != null)
                    {
                        var estabelecimentoProfissional =
                            Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(
                                horario.Profissional.IdProfissional, horario.Estabelecimento.IdEstabelecimento);

                        enviarEmailParaProfissional = estabelecimentoProfissional.PermiteEnvioEmail;
                    }

                    if (enviarEmailParaProfissional)
                    {
                        if (horario.Profissional.PessoaFisica == null)
                            horario.Profissional.PessoaFisica.Refresh();

                        var emailProfissional = horario.Profissional.PessoaFisica.PrimeiraConta.Email;
                        if (emailProfissional.EmailValido())
                            destinosOcultos.Add(new MailAddress(emailProfissional));
                    }
                }
            }

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailCancelamentoDeRecorrenteMarcado(List<Horario> horarios,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var horarioBase = horarios.First();
            string caminhoTemplate;

            caminhoTemplate = "CancelamentoRecorrencia.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var linkHotSite = String.Empty;

            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    clienteEstabelecimento.Estabelecimento.IdEstabelecimento);
            var preco = horarioBase.Valor.ToString("c");
            var hotsite = clienteEstabelecimento.Estabelecimento.Hotsite();
            var urlHotSiteExistente = String.IsNullOrEmpty(hotsite.Url) ? String.Empty : "/" + hotsite.Url;
            var nomeFantasia = clienteEstabelecimento.Estabelecimento.PessoaJuridica.NomeFantasia;
            if (hotsite != null)
                linkHotSite = ObterUrlBase() + urlHotSiteExistente;

            var ultimoHistorico = Domain.Pessoas.HorarioHistoricoRepository.ObterUltimoHistoricoDoHorario(horarioBase);
            var quemCancelou = ultimoHistorico.HorarioQuemCancelou.Nome;
            var motivoCancelamento = ultimoHistorico.MotivoCancelamento;

            horarioBase.ServicoEstabelecimento.Refresh();
            var exibirNomeProfissional =
                horarios.FirstOrDefault()
                    .Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
            var tabelaHorarios =
                "<table width='520' border='0' cellspacing='0' cellpadding='0' style='font-family:Arial, Helvetica, sans-serif;'>" +
                "<tr style='background:#F7993A; border:1px solid #e4e4e4; color:#fff; font-size:12px;'>" +
                "<th style='padding:3px;'>&nbsp;</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Data</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Hora</th>" +
                (exibirNomeProfissional ? "<th style='border-left:1px solid #fff; padding:3px;'>Profissional</th>" : "") +
                "<th style='border-left:1px solid #fff; padding:3px;'>Status</th>" + "</tr>";

            var count = 1;
            foreach (var h in horarios)
            {
                tabelaHorarios +=
                    String.Format(
                        "<tr style='font-size:12px'>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{0}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{1} {2}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{3}</td>" +
                        (exibirNomeProfissional ? "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{4}</td>" : "") +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; text-align:center;'>Cancelado</td>" +
                        "</tr>", count, h.DataInicio.DayOfWeek.ToDiaSemanaString(), h.DataInicio.ToShortDateString(),
                        h.DataInicio.ToShortTimeString(), ObterApelidoOuNomeDoProfissional(horarioBase.Profissional));
                count++;
            }
            tabelaHorarios += "</table>";

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horarioBase.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            corpo = String.Format(corpo, nomeCliente, nomeFantasia, linkHotSite,
                horarioBase.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                horarioBase.Estabelecimento.PessoaJuridica.Telefones.Ativos().ToTextoFormatadoLista(),
                horarioBase.ServicoEstabelecimento.Nome, horarioBase.Duracao.PorExtenso(), preco, tabelaHorarios,
                quemCancelou, motivoCancelamento, CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo),
                urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);
            return corpo;
        }

        #endregion E-mail Cancelamento de Recorrência

        #region E-mail Agendamento Em Lote

        public void EnviarEmailManterAgendamentoEmLote(List<Horario> horarios)
        {
            var horarioBase = horarios.First();
            var clienteEstabelecimento = horarioBase.ClienteEstabelecimento;

            if (!clienteEstabelecimento.EnviarEmailAgendamentoCliente || !clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var assunto = String.Format("{0} - {1}", horarioBase.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailManterAgendamentoEmLote);
            var corpo = ObterCorpoEmailManterAgendamentoEmLote(horarios, clienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(clienteEstabelecimento.Estabelecimento);
            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var estabelecimentoRecebeEmailAutomaticoDeAgendamento = DL.Pessoas.EstabelecimentoConfiguracaoGeralRepository.EstabelecimentoRecebeEmailsAutomaticoDeAgendamento(horarioBase.Estabelecimento.IdEstabelecimento);
            var destinosOcultos = ObterEmailsEstabelecimento(horarioBase.Estabelecimento, estabelecimentoRecebeEmailAutomaticoDeAgendamento);

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any() || destinosOcultos.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null,
                        null, false, replyToAddress.ToArray());
                }
            }
        }

        private string ObterCorpoEmailManterAgendamentoEmLote(List<Horario> horarios,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "ManterAgendamentoEmLote.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            #region dados para o envio em lote

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    clienteEstabelecimento.Estabelecimento.IdEstabelecimento);

            var hotsite = clienteEstabelecimento.Estabelecimento.Hotsite();
            var urlHotSiteExistente = String.IsNullOrEmpty(hotsite.Url) ? String.Empty : "/" + hotsite.Url;
            var nomeFantasia = clienteEstabelecimento.Estabelecimento.PessoaJuridica.NomeFantasia;
            var linkHotSite = String.Empty;
            if (hotsite != null)
                linkHotSite = ObterUrlBase() + urlHotSiteExistente;
            var exibirNomeProfissional =
                horarios.FirstOrDefault()
                    .Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;

            var cabecalhoTabelaHorarios =
                "<table width='520' border='0' cellspacing='0' cellpadding='0' style='font-family:Arial, Helvetica, sans-serif;'>" +
                "<tr style='background:#F7993A; border:1px solid #e4e4e4; color:#fff; font-size:12px;'>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Data</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Hora</th>" +
                (exibirNomeProfissional ? "<th style='border-left:1px solid #fff; padding:3px;'>Profissional</th>" : "") +
                "<th style='border-left:1px solid #fff; padding:3px;'>Serviço</th>" +
                "<th style='border-left:1px solid #fff; padding:3px;'>Status</th>" + "</tr>";

            var count = 1;
            var tabelaHorarios = String.Empty;

            foreach (var h in horarios)
            {
                var servicoEstabelecimento =
                    Domain.Pessoas.ServicoEstabelecimentoRepository.Load(
                        h.ServicoEstabelecimento.IdServicoEstabelecimento);
                tabelaHorarios +=
                    String.Format(
                        "<tr style='font-size:12px'>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{0} {1}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{2}</td>" +
                        (exibirNomeProfissional ? "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center;'>{3}</td>" : "") +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; border-right:none; padding:3px; text-align:center'>{4}</td>" +
                        "<td style='border:1px solid #e4e4e4; border-bottom:1px dotted #e4e4e4; text-align:center;'>{5}</td>" +
                        "</tr>", h.DataInicio.ObterSiglaDiaDaSemana(), h.DataInicio.ToShortDateString(),
                        h.DataInicio.ToShortTimeString(), ObterApelidoOuNomeDoProfissional(h.Profissional),
                        servicoEstabelecimento.Nome, !String.IsNullOrEmpty(h.Status.Descricao) ? h.Status.Descricao : "Confirmado");
                count++;
            }

            tabelaHorarios += "</table>";

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(clienteEstabelecimento.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var dados = String.Format(String.Concat(cabecalhoTabelaHorarios, tabelaHorarios));

            #endregion dados para o envio em lote

            #region alteracoes de dados nos agendamentos em lote

            var tabelaModificacoes = String.Empty;
            var caminhoTemplateTabelaModificacoes = "TabelaAlteracaoDadosAgendamento.cshtml";
            var corpoTabelaModificacoes = ObterTextoCompletoDoArquivo(caminhoTemplateTabelaModificacoes);
            if (horarios.Any(f => f.ObjetoAlterado()))
            {
                var listaDeHorariosComAlteracao = horarios.Where(f => f.ObjetoAlterado());

                foreach (var horarioAlterado in listaDeHorariosComAlteracao)
                {
                    var horarioHistorico = Domain.Pessoas.HorarioHistoricoRepository.ObterUltimoHistoricoDoHorario(horarioAlterado);

                    #region dados do novo agendamento

                    var nomeNovoServico = horarioAlterado.ServicoEstabelecimento.Nome;
                    var novaDuracao = horarioAlterado.Duracao.PorExtenso();
                    var novaDataInicio = String.Format("{0} {1}", horarioAlterado.DataInicio.ObterSiglaDiaDaSemana(), horarioAlterado.DataInicio.ToBrazilianLongDateTimeString());
                    var novaObservacaoDoCliente = horarioAlterado.ObservacaoCliente;
                    var nomeOuApelidoNovoProfissional = ObterApelidoOuNomeDoProfissional(horarioAlterado.Profissional);

                    var nomeNovoProfissionalFormatadoParaTabela = exibirNomeProfissional
                    ? string.Format(@"<tr>
                        <td width='160' >
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Profissional:</p>
                        </td>
                        <td width='10'>&nbsp;</td>
                        <td width='350'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p>                                                            </td>
                    </tr>", nomeOuApelidoNovoProfissional)
                    : "";

                    #endregion dados do novo agendamento

                    #region dados do historico do agendamento

                    var nomeAntigoServico = horarioHistorico.ServicoEstabelecimento.Nome;
                    var antigaDuracao = horarioHistorico.Duracao.PorExtenso();
                    var antigaDataInicio = String.Format("{0} {1}", horarioHistorico.DataInicio.ObterSiglaDiaDaSemana(), horarioHistorico.DataInicio.ToBrazilianLongDateTimeString());
                    var antigaObservacaoDoCliente = horarioHistorico.ObservacaoCliente;
                    var nomeOuApelidoAntigoProfissional = ObterApelidoOuNomeDoProfissional(horarioHistorico.Profissional);

                    var nomeAntigoProfissionalFormatadoParaTabela = exibirNomeProfissional
                    ? string.Format(@"<tr>
                        <td width='160' >
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Profissional:</p>
                        </td>
                        <td width='10'>&nbsp;</td>
                        <td width='350'>
                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p>                                                            </td>
                    </tr>", nomeOuApelidoAntigoProfissional)
                    : "";

                    #endregion dados do historico do agendamento

                    var alteracoesRealizadas = ObterListaItensAlteradosHorario(horarioAlterado, horarioHistorico);
                    tabelaModificacoes = tabelaModificacoes +
                                         String.Format(corpoTabelaModificacoes,
                                                       alteracoesRealizadas,
                                                       nomeAntigoServico,
                                                       nomeAntigoProfissionalFormatadoParaTabela,
                                                       antigaDataInicio,
                                                       antigaDuracao,
                                                       antigaObservacaoDoCliente,
                                                       nomeNovoServico,
                                                       nomeNovoProfissionalFormatadoParaTabela,
                                                       novaDataInicio,
                                                       novaDuracao,
                                                       novaObservacaoDoCliente);
                }
            }

            #endregion alteracoes de dados nos agendamentos em lote

            corpo = String.Format(corpo, nomeCliente, nomeFantasia, dados, linkHotSite,
                clienteEstabelecimento.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                clienteEstabelecimento.Estabelecimento.PessoaJuridica.Telefones.Ativos().ToTextoFormatadoLista(),
                urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore, tabelaModificacoes);

            return corpo;
        }

        #endregion E-mail Agendamento Em Lote

        #region E-mail Fale Conosco Trinks

        public void EnviarEmailFaleConoscoTrinks(String nomeUsuario, String emailUsuario, String mensagem, String assuntoCorpo, string emailAtendimento, String telefone, string nomeEstabelecimento = null, List<Attachment> anexos = null)
        {
            if (!emailUsuario.EmailValido())
                return;

            telefone = telefone.Replace("--", "-");

            string telefoneHTML = "";
            if (!string.IsNullOrWhiteSpace(telefone))
            {
                telefoneHTML = "<b> Telefone:</b> " + telefone + "<br/>";
            }

            NotificarAtendimentoSobreMensagemEnviadaPeloUsuario(nomeUsuario, emailUsuario, mensagem, assuntoCorpo, emailAtendimento, nomeEstabelecimento, anexos, telefoneHTML);

            NotificarAoUsuarioQueRecebemosSuaMensagem(nomeUsuario, emailUsuario, mensagem, assuntoCorpo, telefone);
        }

        private void NotificarAtendimentoSobreMensagemEnviadaPeloUsuario(string nomeUsuario, string emailUsuario, string mensagem, string assuntoCorpo, string emailAtendimento, string nomeEstabelecimento, List<Attachment> anexos, string telefoneHTML)
        {
            var assuntoEmail = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailFaleConosco +
                            " - " + assuntoCorpo + (nomeEstabelecimento != null ? " - " + nomeEstabelecimento : String.Empty) + " - " + String.Format("{0:dd/MM/yyyy HH:mm}", DateTime.Now);

            string listaDeLinksDeArquivos = "";
            if (anexos != null && anexos.Count > 0)
            {
                listaDeLinksDeArquivos = "Segue arquivo anexado: ";
                foreach (var anexo in anexos)
                {
                    string url;
                    SalvarArquivoAnexoNoS3(anexo, out url);
                    var linkDoArquivo = "<a href=" + url + ">" + url + "</a><br/>";
                    listaDeLinksDeArquivos += linkDoArquivo;
                }
            }

            var corpo = ObterCorpoEmailFaleConoscoTrinks(nomeUsuario, emailUsuario, assuntoCorpo, telefoneHTML, mensagem, listaDeLinksDeArquivos);

            var faleConosco = ObterEmailFaleConosco();
            var destinatarios = new List<MailAddress>();

            var destino = !string.IsNullOrWhiteSpace(emailAtendimento)
                ? new MailAddress(emailAtendimento)
                : faleConosco;

            destinatarios.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
            {
                // Para garantia de melhor entrega, o envio do fale conosco deve ser feito pelo SES.
                IDisparadorEmail emailClient = ObterDisparadorEmailAwsSeHabilitado();
                emailClient.SendHtml(destinatarios.ToArray(), faleConosco, assuntoEmail, corpo);
            }
        }

        private void SalvarArquivoAnexoNoS3(Attachment anexo, out string url)
        {
            ControleDeArquivosAmazonS3 s3AmazonControleDeArquivos = new ControleDeArquivosAmazonS3();

            string bucket = new ParametrosTrinks<string>(ParametrosTrinksEnum.nome_do_bucketS3_para_anexo).ObterValor();

            string pastaDoDia = Calendario.Hoje().ToShortDateString().Replace("/", "-");
            if (!ControleDeArquivosAmazonS3.VerificaExisteDiretorio(bucket, "anexos/" + pastaDoDia))
                ControleDeArquivosAmazonS3.CriarDiretorio(bucket, "anexos/" + pastaDoDia);

            var preNome = Calendario.Agora().ToString().Replace("/", "-").Replace(":", "-").Replace(" ", "-");
            var nomeDoArquivo = anexo.Name.Replace(" ", "_").RemoverAcentos();
            s3AmazonControleDeArquivos.GravarArquivo(anexo.ContentStream, bucket + "/anexos/" + pastaDoDia, preNome + "_" + nomeDoArquivo, tornarPublico: true);

            string urlDeAnexos = new ParametrosTrinks<string>(ParametrosTrinksEnum.url_de_anexos).ObterValor();
            url = urlDeAnexos + pastaDoDia + "/" + preNome + "_" + nomeDoArquivo;
        }

        private string ObterCorpoEmailFaleConoscoTrinks(String nome, String email, String assuntoCorpo, String telefone, String mensagem, string listaDeLinksDeArquivos)
        {
            var caminhoTemplate = "FaleConoscoTrinks.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = String.Format(corpo, nome, email, telefone, assuntoCorpo, mensagem, listaDeLinksDeArquivos);
            return corpo;
        }

        #endregion E-mail Fale Conosco Trinks

        #region E-mail Fale Conosco Trinks Cliente

        public void NotificarAoUsuarioQueRecebemosSuaMensagem(String nome, String email, String mensagem, String assuntoCorpo, String telefone)
        {
            var assuntoEmail = "Fale Conosco Trinks";

            var corpo = ObterCorpoEmailFaleConoscoCliente(nome, email, assuntoCorpo, telefone, mensagem);

            var remetente = ObterEmailFaleConosco();
            var destino = new List<MailAddress>();
            if (!email.EmailValido())
                return;
            destino.Add(new MailAddress(email));

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assuntoEmail, corpo, null, null);
        }

        private string ObterCorpoEmailFaleConoscoCliente(String nome, String email, String assuntoCorpo, String telefone, String mensagem)
        {
            var caminhoTemplate = "FaleConoscoCliente.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            string styleTelefone = "";
            if (string.IsNullOrWhiteSpace(telefone))
            {
                styleTelefone = "display:none";
            }

            corpo = String.Format(corpo, nome, email, styleTelefone, telefone, assuntoCorpo, mensagem);
            return corpo;
        }

        #endregion E-mail Fale Conosco Trinks Cliente

        #region E-mail NFCE

        public void EnviarEmailCancelamentoNFCE(Transacao transacao)
        {
            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCancelamentoNFC;
            string corpo = ObterCorpoEmailCancelamentoNFC(transacao);

            MailAddress remetente = ObterRemetenteNaoResponda(transacao.PessoaQueRecebeu.Estabelecimento);

            List<MailAddress> destino = new List<MailAddress>();

            var email = transacao.PessoaQueRecebeu.Email;
            if (email != null)
                destino.Add(new MailAddress(email));

            if (destino.Any())
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoEmailCancelamentoNFC(Transacao transacao)
        {
            string caminhoTemplate = "EmailCancelamentoNFC.cshtml";
            string corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            string nomeClienteEstabelecimento = transacao.PessoaQuePagou.NomeOuApelido();

            ClienteEstabelecimento clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(transacao.PessoaQuePagou.IdPessoa, transacao.PessoaQueRecebeu.Estabelecimento.IdEstabelecimento);

            var enderecoClienteEstabelecimento = string.Empty;
            if (clienteEstabelecimento != null && clienteEstabelecimento.Endereco != null &&
                !string.IsNullOrWhiteSpace(clienteEstabelecimento.Endereco.Logradouro) &&
                !string.IsNullOrWhiteSpace(clienteEstabelecimento.Endereco.Numero) &&
                !string.IsNullOrWhiteSpace(clienteEstabelecimento.Endereco.Bairro) &&
                !string.IsNullOrWhiteSpace(clienteEstabelecimento.Endereco.Cidade) &&
                clienteEstabelecimento.Endereco.UF != null && !string.IsNullOrEmpty(clienteEstabelecimento.Endereco.UF.Sigla) &&
                !string.IsNullOrWhiteSpace(clienteEstabelecimento.Endereco.Cep))
            {
                enderecoClienteEstabelecimento =
                clienteEstabelecimento.Endereco != null ? clienteEstabelecimento.Endereco.Logradouro +
                ", " + (!string.IsNullOrEmpty(clienteEstabelecimento.Endereco.Numero) ? " - " : string.Empty) +
                clienteEstabelecimento.Endereco.Bairro + " - " + clienteEstabelecimento.Endereco.Cidade +
                " - " + (clienteEstabelecimento.Endereco.UF != null ? clienteEstabelecimento.Endereco.UF.Sigla : " ") +
                " " + clienteEstabelecimento.Endereco.Cep : string.Empty;
            }

            string telefonesClienteEstabelecimento = transacao.PessoaQuePagou.TelefonesProprios().Any() ? transacao.PessoaQuePagou.TelefonesProprios().ToTextoFormatadoLista() : string.Empty;
            string cpfClienteEstabelecimento = transacao.PessoaQuePagou.Cpf;
            string nomeResponsavelEstabelecimento = transacao.PessoaQueRecebeu.Estabelecimento.ObterResponsavel().NomeCompleto;

            string parteInformacoesNotaFiscal = ObterParteInformacoesNotaFiscal(transacao);

            var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento;
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var notaFiscal = DL.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterNotaNFC(transacao.PessoaQueRecebeu.Estabelecimento.IdEstabelecimento, transacao.Id);

            string chaveConsulta = string.Empty;
            if (notaFiscal != null && notaFiscal.AutorizacaoSEFAZ != null)
                chaveConsulta = notaFiscal.AutorizacaoSEFAZ.Substring(3, notaFiscal.AutorizacaoSEFAZ.Length - 3);

            var configuracaoNFCeDoEstado = DL.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.ObterConfiguracaoNFCeDoUF(transacao.PessoaQueRecebeu.EnderecoProprio.UF.IdUF);

            var textoFormaConsultaNFCe = "Acesse o portal da Nota Fiscal Eletrônica do ministério da fazenda em www.nfe.fazenda.gov.br<br /> Vá até Serviços > Consultar Resumo da NF-e e digite o código abaixo:";

            if (configuracaoNFCeDoEstado != null && !String.IsNullOrEmpty(configuracaoNFCeDoEstado.UrlConsultaNFCe))
                textoFormaConsultaNFCe = "Acesse o site " + configuracaoNFCeDoEstado.UrlConsultaNFCe + " e digite o código abaixo:";

            corpo = String.Format(corpo,
                                  nomeClienteEstabelecimento,
                                  enderecoClienteEstabelecimento,
                                  parteInformacoesNotaFiscal,
                                  telefonesClienteEstabelecimento,
                                  cpfClienteEstabelecimento.Formatar("999.999.999-99"),
                                  chaveConsulta,
                                  nomeResponsavelEstabelecimento,
                                  textoFormaConsultaNFCe,
                                  urlLogoEstabelecimento);
            return corpo;
        }

        [TransactionInitNotRequired]
        public void EnviarEmailEmissaoNFCe(Transacao transacao, String xml)
        {
            string chaveConsulta = string.Empty;
            var notaFiscal = DL.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.ObterNotaNFC(transacao.PessoaQueRecebeu.Estabelecimento.IdEstabelecimento, transacao.Id);

            if (notaFiscal != null)
                chaveConsulta = notaFiscal.AutorizacaoSEFAZ.Substring(3, notaFiscal.AutorizacaoSEFAZ.Length - 3);
            if (String.IsNullOrEmpty(chaveConsulta))
                return;

            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailEmissaoNFC + transacao.PessoaQueRecebeu.NomeFantasia;
            string corpo = ObterCorpoEmailEmissaoNFC(transacao, chaveConsulta);

            MemoryStream memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(xml));
            ContentType contentType = new ContentType("text/xml");
            contentType.Name = chaveConsulta + ".xml";
            List<Attachment> anexos = new List<Attachment>();
            anexos.Add(new Attachment(memoryStream, contentType));

            MailAddress remetente = ObterRemetenteNaoResponda(transacao.PessoaQueRecebeu.Estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            var emailCliente = transacao.PessoaQuePagou.Email;
            var emailEstabelecimento = transacao.PessoaQueRecebeu.Email;

            if (!String.IsNullOrEmpty(emailCliente))
            {
                destino.Add(new MailAddress(emailCliente));
                destinosOcultos.Add(new MailAddress(emailEstabelecimento));
            }
            else
            {
                destino.Add(new MailAddress(emailEstabelecimento));
            }

            if (destino.Any())
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null, anexos.ToArray());
        }

        private string ObterCorpoEmailEmissaoNFC(Transacao transacao, String chaveConsulta)
        {
            string caminhoTemplate = "EmailNFC.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            #region Informações Cliente

            var nomeClienteEstabelecimento = transacao.PessoaQuePagou.NomeOuApelido();

            #endregion Informações Cliente

            #region Informações Estabelecimento

            var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento;
            var responsavelEstabelecimento = estabelecimento.ObterResponsavel();
            var nomeAdministradorEstabelecimento = responsavelEstabelecimento.NomeCompleto;
            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var telefonesEstabelecimento = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();
            var nomeFantasiaEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            #endregion Informações Estabelecimento

            #region Valores Transação

            string parteInformacoesNotaFiscal = ObterParteInformacoesNotaFiscal(transacao);
            var valorTotal = transacao.TotalPagar.Value.ToString();
            var valorPago = transacao.TotalPago.Value.ToString();
            var troco = transacao.Troco.Value.ToString();

            #endregion Valores Transação

            var configuracaoNFCeDoEstado = DL.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.ObterConfiguracaoNFCeDoUF(estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF);

            var textoFormaConsultaNFCe = "Acesse o portal da Nota Fiscal Eletrônica do ministério da fazenda em www.nfe.fazenda.gov.br<br /> Vá até Serviços > Consultar Resumo da NF-e e digite o código abaixo:";

            if (configuracaoNFCeDoEstado != null && !String.IsNullOrEmpty(configuracaoNFCeDoEstado.UrlConsultaNFCe))
                textoFormaConsultaNFCe = "Acesse o site " + configuracaoNFCeDoEstado.UrlConsultaNFCe + " e digite o código abaixo:";

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(estabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            corpo = String.Format(corpo,
                                  nomeClienteEstabelecimento,
                                  nomeFantasiaEstabelecimento,
                                  parteInformacoesNotaFiscal,
                                  valorTotal,
                                  valorPago,
                                  troco,
                                  urlHotsiteEstabelecimento,
                                  enderecoEstabelecimento,
                                  telefonesEstabelecimento,
                                  informacoesDoAplicativo.TextoAplicativo,
                                  informacoesDoAplicativo.UrlAppStore,
                                  informacoesDoAplicativo.UrlPlayStore,
                                  chaveConsulta,
                                  textoFormaConsultaNFCe,
                                  urlLogoEstabelecimento);
            return corpo;
        }

        private string ObterParteInformacoesNotaFiscal(Transacao transacao)
        {
            string caminhoTemplate = "ParteServicosImpressaoNFC.cshtml";
            var parte = new StringBuilder();
            var ordenacao = 1;

            var itensDaTransacao = "";
            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                var nomeServicoEstabelecimento = horarioTransacao.Horario.ServicoEstabelecimento.Nome;
                var quantidade = 1;
                var precoPorUnidade = horarioTransacao.Horario.Valor;
                var precoTotal = quantidade * precoPorUnidade;
                var ehConsumidoDePacote = horarioTransacao.ItemPacoteCliente != null;
                var desconto = horarioTransacao.Desconto;
                var textoDescricaoDesconto = "";
                var textoDesconto = "";
                if (desconto.HasValue && desconto.Value < 0)
                {
                    textoDesconto = "<br />" + desconto.Value.ValorDecimal();
                    textoDescricaoDesconto = "<br />Desconto item " + ordenacao;
                }

                itensDaTransacao = itensDaTransacao + "<tr><td valign='top'>" + ordenacao + "</td>" +
                            "<td>" + nomeServicoEstabelecimento + "<br>" +
                            quantidade + " Un. " + (ehConsumidoDePacote ? "(consumido de pacote)" : "X " + precoPorUnidade.ValorDecimal() + textoDescricaoDesconto) + "</td>" +
                            "<td align='right' valign='top'>" + (ehConsumidoDePacote ? " - " : (precoTotal.ValorDecimal() + textoDesconto)) + "</td></tr>";
                ordenacao++;
            }

            foreach (var venda in transacao.Vendas)
            {
                var itensVendaProduto = venda.ItensVenda.Where(f => f is ItemVendaProduto).Select(p => ((ItemVendaProduto)p));
                var itensVendaPacote = venda.ItensVenda.Where(f => f is ItemVendaPacote).Select(p => ((ItemVendaPacote)p));
                var itensVendaValePresente = venda.ItensVenda.Where(f => f is ItemVendaValePresente).Select(p => ((ItemVendaValePresente)p));

                foreach (var itenVendaProduto in itensVendaProduto)
                {
                    var nomeProdutoEstabelecimento = itenVendaProduto.EstabelecimentoProduto.Descricao;
                    var quantidade = itenVendaProduto.Quantidade;
                    var precoPorUnidade = itenVendaProduto.ValorUnitario;
                    decimal precoTotal = itenVendaProduto.SubTotal;
                    var ehConsumoDePacote = itenVendaProduto.ItemPacoteCliente != null;

                    var desconto = itenVendaProduto.Desconto;
                    var textoDescricaoDesconto = "";
                    var textoDesconto = "";
                    if (desconto < 0)
                    {
                        textoDesconto = "<br />" + desconto.ValorDecimal();
                        textoDescricaoDesconto = "<br />Desconto item " + ordenacao;
                    }

                    itensDaTransacao = itensDaTransacao + "<tr>" +
                            "<td valign='top'>" + ordenacao + "</td>" +
                            "<td>" + nomeProdutoEstabelecimento + "<br>" +
                            quantidade + " Un. " + (ehConsumoDePacote ? "(consumido de pacote)" : "X " + precoPorUnidade.ValorDecimal() + textoDescricaoDesconto) + "</td>" +
                            "<td align='right' valign='top'>" + (ehConsumoDePacote ? " - " : (precoTotal.ValorDecimal() + textoDesconto)) + "</td></tr>";
                    ordenacao++;
                }

                foreach (var itenVendaPacote in itensVendaPacote)
                {
                    var nomePacote = itenVendaPacote.PacoteCliente.Nome;
                    var quantidade = itenVendaPacote.Quantidade;
                    var precoPorUnidade = itenVendaPacote.ValorUnitario;
                    var precoTotal = itenVendaPacote.SubTotal;

                    var desconto = itenVendaPacote.Desconto;
                    var textoDescricaoDesconto = "";
                    var textoDesconto = "";
                    if (desconto < 0)
                    {
                        textoDesconto = "<br />" + desconto.ValorDecimal();
                        textoDescricaoDesconto = "<br />Desconto item " + ordenacao;
                    }

                    itensDaTransacao = itensDaTransacao + "<tr>" +
                            "<td valign='top'>" + ordenacao + "</td>" +
                            "<td>" + nomePacote + "<br>" +
                            quantidade + " Un. X " + precoPorUnidade.ValorDecimal() + textoDescricaoDesconto + "</td>" +
                            "<td align='right' valign='top'>" + (precoTotal.ValorDecimal() + textoDesconto) + "</td></tr>";

                    ordenacao++;
                }

                foreach (var itenVendaValePresente in itensVendaValePresente)
                {
                    var descricaoValePresente = "Vale Presente Nº " + itenVendaValePresente.ValePresente.Numero + " - " + (itenVendaValePresente.ValePresente.Validade.HasValue ? "Válido até " + itenVendaValePresente.ValePresente.Validade.Value.ToString("dd/MM/yyyy") : "");
                    var quantidade = itenVendaValePresente.Quantidade;
                    var precoPorUnidade = itenVendaValePresente.ValorUnitario;
                    var precoTotal = itenVendaValePresente.SubTotal;

                    var desconto = itenVendaValePresente.Desconto;
                    var textoDescricaoDesconto = "";
                    var textoDesconto = "";
                    if (desconto < 0)
                    {
                        textoDesconto = "<br />" + desconto.ValorDecimal();
                        textoDescricaoDesconto = "<br />Desconto item " + ordenacao;
                    }

                    itensDaTransacao = itensDaTransacao + "<tr>" +
                            "<td valign='top'>" + ordenacao + "</td>" +
                            "<td>" + descricaoValePresente + "<br>" +
                            quantidade + " Un. X " + precoPorUnidade.ValorDecimal() + textoDescricaoDesconto + "</td>" +
                            "<td align='right' valign='top'>" + precoTotal.ValorDecimal() + textoDesconto + "</td></tr>";

                    ordenacao++;
                }
            }

            var formasPagamento =
                transacao.FormasPagamento.Where(
                    f =>
                        (f.Transacao.TipoTransacao.Id == 1 && f.ValorPago > 0) ||
                        (f.Transacao.TipoTransacao.Id == 2 && f.ValorPago < 0) ||
                        (f.FormaPagamento != FormaPagamentoEnum.Dinheiro &&
                         f.FormaPagamento != FormaPagamentoEnum.CreditoCliente))
                    .ToList();

            var valorCreditoDeixado =
                transacao.FormasPagamento.Where(
                    f =>
                        f.FormaPagamento == FormaPagamentoEnum.CreditoCliente &&
                        ((f.Transacao.TipoTransacao.Id == 1 && f.ValorPago < 0) ||
                         (f.Transacao.TipoTransacao.Id == 2 && f.ValorPago > 0))).Sum(f => f.ValorPago) *
                (transacao.TipoTransacao.Id == 1 ? -1 : 1);

            var listaIdsFormaDePagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable().Where(p => p.Estabelecimento.PessoaJuridica.IdPessoa == transacao.PessoaQueRecebeu.IdPessoa && p.AceitaPreDatado).Select(p => p.FormaPagamento.Id);
            var textoFormasDePagamento = "";
            foreach (var formaPagamento in formasPagamento)
            {
                textoFormasDePagamento = textoFormasDePagamento + "<tr><td>"
                                     + formaPagamento.FormaPagamento.Tipo.Nome + " - "
                                     + formaPagamento.FormaPagamento.Nome + (formaPagamento.ValePresente != null ? " " + formaPagamento.ValePresente.Numero : "") +
                                       (formaPagamento.NumeroParcelas > 1 ? String.Format(" (Parcelado em {0} vezes)", formaPagamento.NumeroParcelas) : "")
                                     + (formaPagamento.NumeroParcelas == 1 && formaPagamento.FormaPagamento.Id == 9 && listaIdsFormaDePagamento.Any(p => p == formaPagamento.FormaPagamento.Id) && (formaPagamento.Parcelas.FirstOrDefault().DataPagamento.CompareTo(transacao.DataHora) == 1) ? String.Format(" (Pré-datado para {0})", formaPagamento.Parcelas.FirstOrDefault().DataPagamento.ToBrazilianShortDateString()) : "")
                                     + "</td><td align='right'>" + (formaPagamento.ValorPago < 0 ? (formaPagamento.ValorPago * -1).ValorOuTraco() : formaPagamento.ValorPago.ValorOuTraco()) + "</td></tr>";
            }

            if (valorCreditoDeixado > 0)
            {
                textoFormasDePagamento = textoFormasDePagamento + "<tr>"
                    + "<td>Crédito Futuro</td>"
                    + "<td align='right'>" + valorCreditoDeixado.ValorDecimal() + "</td></tr>";
            }

            var linhaTroco = "<tr><td>"
                                     + "Troco"
                                     + "</td><td align='right'>" + (transacao.Troco.HasValue ? transacao.Troco.Value.ValorDecimal() : " - ") + "</td></tr>";

            textoFormasDePagamento = textoFormasDePagamento + linhaTroco;

            var linha = ObterTextoCompletoDoArquivo(caminhoTemplate);

            string informacaoSobreEstorno = string.Empty;
            if (transacao.TransacaoQueEstounouEsta != null)
                informacaoSobreEstorno = "Estorno feito por " + transacao.TransacaoQueEstounouEsta.PessoaQueRealizou.NomeOuApelido() + " em " + transacao.TransacaoQueEstounouEsta.DataHora.ToBrazilianLongDateTimeString();

            linha = String.Format(linha, transacao.DataHora.ToBrazilianShortDateString(), itensDaTransacao, (transacao.TotalPago.HasValue ? transacao.TotalPago.Value.ValorDecimal() : "-"), textoFormasDePagamento, informacaoSobreEstorno);

            return linha.ToString();
        }

        private string ObterParteEmailServicosRealizados(Transacao transacao)
        {
            string caminhoTemplate = "ParteServicosRealizados.cshtml";
            var parte = new StringBuilder();

            var linhaTabelaServicos = @"<tr><td colspan='3' height='1' style='border-bottom:1px dotted #c6c6c6'></td></tr>";

            var totalDeTransacoes = transacao.HorariosTransacoes.Count();
            var controle = 0;

            foreach (HorarioTransacao item in transacao.HorariosTransacoes)
            {
                string servico = item.Horario.ServicoEstabelecimento.Nome;
                String inicioComentario = "<!--";
                String fimComentario = "-->";
                var preco = ObterPreco(item);

                string desconto = String.Empty;
                if (item.Desconto > 0)
                {
                    inicioComentario = String.Empty;
                    fimComentario = String.Empty;
                    desconto = item.Desconto.Value.ToString("0.00");
                }

                string subTotal = item.SubTotal().Value.ToString("0.00");

                var linha = ObterTextoCompletoDoArquivo(caminhoTemplate);
                linha = String.Format(linha,
                                servico,
                               ObterApelidoOuNomeDoProfissional(item.Horario.Profissional),
                                inicioComentario,
                                fimComentario,
                                desconto,
                                preco);

                parte.Append(linha);

                controle++;
                if (controle < totalDeTransacoes)
                {
                    parte.Append(linhaTabelaServicos);
                }
            }

            return parte.ToString();
        }

        #endregion E-mail NFCE

        #region E-mail de erro de emissão CFe SAT

        [TransactionInitNotRequired]
        public void EnviarEmailErroNaEmissaoNFCeSAT(NotaNFC notaFical, String erroDeEmissaoCFeSAT)
        {
            var transacao = notaFical.Transacao;
            string assunto = transacao.PessoaQueRecebeu.NomeFantasia + " - Retorno de emissão de CF-e " + transacao.NumeroDaPreVenda.Value.ToString();
            var corpo = ObterCorpoDoEmailErroNaEmissaoCFeSAT(transacao, erroDeEmissaoCFeSAT);

            MailAddress remetente = ObterRemetenteNaoResponda(transacao.PessoaQueRecebeu.Estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();

            string emailResponsavelDoEstabelecimento = transacao.PessoaQueRecebeu.ResponsavelFinanceiro.Email;
            string emailDaPessaoQueEmitiu = notaFical.UltimaPessoaQueEmitiuSAT != null ? notaFical.UltimaPessoaQueEmitiuSAT.Email : "";

            if (!String.IsNullOrEmpty(emailResponsavelDoEstabelecimento))
                destino.Add(new MailAddress(emailResponsavelDoEstabelecimento));

            if (!String.IsNullOrEmpty(emailDaPessaoQueEmitiu))
                destino.Add(new MailAddress(emailDaPessaoQueEmitiu));

            if (destino.Any())
                EmailManager.SendHtml(destino.Distinct().ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoDoEmailErroNaEmissaoCFeSAT(Transacao transacao, String erroDeEmissaoCFeSAT)
        {
            string caminhoTemplate = "EmailErroEmissaoNFC.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(transacao.PessoaQueRecebeu.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var nomeColaborador = transacao.PessoaQueRealizou.ObterApelidoOuNome();
            var numeroCFe = transacao.NumeroDaPreVenda.ToString();
            var menssagemErro = erroDeEmissaoCFeSAT;

            corpo = String.Format(corpo,
                                  urlLogoEstabelecimento,
                                  nomeColaborador,
                                  numeroCFe,
                                  menssagemErro);
            return corpo;
        }

        #endregion E-mail de erro de emissão CFe SAT

        #region Avaliação do Estabelecimento

        public void EnviarEmailConviteAvaliacaoEstabelecimento(Transacao transacao)
        {
            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailConviteAvaliacaoEstabelecimento;
            string corpo = ObterCorpoEmailConviteAvaliacaoEstabelecimento(transacao);

            var emailCliente = transacao.PessoaQuePagou.Email;
            var emailEstabelecimento = transacao.PessoaQueRecebeu.Email;

            MailAddress remetente = ObterRemetenteNaoResponda(transacao.PessoaQueRecebeu.Estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            if (!String.IsNullOrEmpty(emailCliente))
            {
                destino.Add(new MailAddress(emailCliente));
                destinosOcultos.Add(new MailAddress(emailEstabelecimento));
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo);
            }
        }

        private string ObterCorpoEmailConviteAvaliacaoEstabelecimento(Transacao transacao)
        {
            string caminhoTemplate = "ConviteAvaliacaoEstabelecimento.html";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento;

            //Informações do Corpo do Email
            //{0} nome do cliente
            var nomeClienteEstabelecimento = transacao.PessoaQuePagou.NomeOuApelido();

            //{1} data do serviço
            var dataPagamentoDaTransacao = transacao.DataHora;

            //{2} dados do estabelecimento e link para hotsite - var dadosEstabelecimento
            var nomeEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;

            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var dadosEstabelecimento = ObterDadosEstabelecimento(estabelecimento);

            var possuiAplicativoProprio = PossuiAplicativoProprio(estabelecimento);
            //{3} texto aplicativo
            string textoAplicativo = ObterTextoAplicativo(estabelecimento, possuiAplicativoProprio);

            //{4} url para download app redirecionamento automático
            string urlDownloadApp = ObterURLAplicativo(estabelecimento, possuiAplicativoProprio);

            //{5} nome estabelecimento para p link do app
            var nomeEstabelecimentoParaLinkApp = ObterNomeParaLinkApp(estabelecimento);

            //{6} logo do estabelecimento / url logo estabelecimento
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(transacao.PessoaQueRecebeu.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            corpo = String.Format(corpo,
                              nomeClienteEstabelecimento, //0
                              dataPagamentoDaTransacao,//1
                              dadosEstabelecimento,//2
                              textoAplicativo,//3
                              urlDownloadApp,//4
                              nomeEstabelecimentoParaLinkApp,//5
                              urlLogoEstabelecimento //6
                              );
            return corpo;
        }

        #endregion Avaliação do Estabelecimento

        #region Amigo Indica

        public void EnviarEmailDoAmigoIndica(TipoTransacaoPromotor tipoDeResgate, string nomeDoPromotor, decimal valorDaTransacao, string emailDoPromotor, decimal saldoAtual, string descricao)
        {


            string assunto = "Resgate de pontos";
            string corpo = "Nome Fantasia:  " + nomeDoPromotor + "<br/>" +
                "E-mail:  " + emailDoPromotor + "<br/>" +
                "Saldo para resgate:  " + saldoAtual + "<br/>" +
                "Tipo de resgate solicitado:  " + descricao + "<br/>" +
                "Valor a ser resgatado:  " + valorDaTransacao + "<br/>";

            var emailDeRecebimentoDePedidosDeResgate = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.email_de_resgate_do_indica_trinks).Valor;
            MailAddress remetente = new MailAddress("<EMAIL>");
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            if (!String.IsNullOrEmpty(emailDeRecebimentoDePedidosDeResgate))
            {
                destino.Add(new MailAddress(emailDeRecebimentoDePedidosDeResgate));
                destinosOcultos.Add(new MailAddress(emailDeRecebimentoDePedidosDeResgate));

                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo);
            }
        }

        #endregion
        #region Programa de Fidelidade

        public void EnviarEmailProgramaDeFidelidadePontosGanhos(DateTime dataHora, ClienteEstabelecimento clienteEstabelecimento, int pontosGanhos, int novoSaldoDePontosDoCliente)
        {
            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailProgramaDeFidelidadePontosGanho;

            string corpo = ObterCorpoEmailProgramaDeFidelidadePontosGanhos(dataHora, clienteEstabelecimento, pontosGanhos, novoSaldoDePontosDoCliente);

            var emailCliente = clienteEstabelecimento.Cliente.PessoaFisica.Email;
            var emailEstabelecimento = clienteEstabelecimento.Estabelecimento.PessoaJuridica.Email;

            MailAddress remetente = ObterRemetenteNaoResponda(clienteEstabelecimento.Estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            if (!String.IsNullOrEmpty(emailCliente))
            {
                destino.Add(new MailAddress(emailCliente));
                destinosOcultos.Add(new MailAddress(emailEstabelecimento));
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo);
            }
        }

        public void EnviarEmailProgramaDeFidelidadePontosGanhos(Transacao transacao, int totalDePontosNaTransacao, int novoSaldoDePontosDoCliente, int totalDePontosGanhos)
        {
            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailProgramaDeFidelidadePontosGanho;
            string corpo = ObterCorpoEmailProgramaDeFidelidadePontosGanhos(transacao, totalDePontosNaTransacao, novoSaldoDePontosDoCliente, totalDePontosGanhos);

            var emailCliente = transacao.PessoaQuePagou.Email;
            var emailEstabelecimento = transacao.PessoaQueRecebeu.Email;

            MailAddress remetente = ObterRemetenteNaoResponda(transacao.PessoaQueRecebeu.Estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            if (!String.IsNullOrEmpty(emailCliente))
            {
                destino.Add(new MailAddress(emailCliente));
                destinosOcultos.Add(new MailAddress(emailEstabelecimento));
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo);
            }
        }

        private string ObterCorpoEmailProgramaDeFidelidadePontosGanhos(DateTime dataHora, ClienteEstabelecimento clienteEstabelecimento, int totalDePontosGanhoNaTransacao, int novoSaldoDePontosDoCliente)
        {
            string caminhoTemplate = "ProgramaDeFidelidadePontosGanhos.html";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var estabelecimento = clienteEstabelecimento.Estabelecimento;

            bool permiteTransferencias = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();
            bool clientePossuiCpfOuEmail = !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Cpf) || !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Email);

            //Informações do Corpo do Email
            //{0} nome do cliente
            var nomeClienteEstabelecimento = clienteEstabelecimento.Cliente.PessoaFisica.NomeOuApelido();

            //{1} pontos ganhos
            var totalDePontosGanho = totalDePontosGanhoNaTransacao;

            //{2} data do serviço
            var dataPagamentoDaTransacao = dataHora;
            var saldoAtualDePontos = TextoComSaldoTotal(novoSaldoDePontosDoCliente);

            //{4} a menor quantidade de pontos para resgatar um produto. texto: hoje a partir de XX pontos
            int pontoMinimoParaResgate;

            //{5} lista do(s) produto(s) que pode ser resgatado quantidade de pontos menor que saldo cliente
            var produtoParaResgatePontoMinimo = ListaProdutosOuServicosComMenorPontuacaoParaResgate(estabelecimento.IdEstabelecimento, out pontoMinimoParaResgate);
            //{6} dados do estabelecimento e link para hotsite - var dadosEstabelecimento
            var nomeEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;
            //{11} pontos ganhos
            var totalDePontosGanhoNaRede = permiteTransferencias && clientePossuiCpfOuEmail ? Domain.Pessoas.ClienteEstabelecimentoRepository
                        .ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(estabelecimento.FranquiaEstabelecimento.Franquia.Id, clienteEstabelecimento.Cliente.PessoaFisica.Cpf, clienteEstabelecimento.Cliente.PessoaFisica.Email) : 0;
            var textoDePontosDaRede = ObterTextoDePontosNaRede(permiteTransferencias, totalDePontosGanhoNaRede);

            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var dadosEstabelecimento = ObterDadosEstabelecimento(estabelecimento);

            var possuiAplicativoProprio = PossuiAplicativoProprio(estabelecimento);
            //{7} texto aplicativo
            string textoAplicativo = ObterTextoAplicativo(estabelecimento, possuiAplicativoProprio);

            //{8} url para download app redirecionamento automático
            string urlDownloadApp = ObterURLAplicativo(estabelecimento, possuiAplicativoProprio);

            //{9} nome estabelecimento para p link do app
            var nomeEstabelecimentoParaLinkApp = ObterNomeParaLinkApp(estabelecimento);

            //{10} logo do estabelecimento / url logo estabelecimento
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var frasePontoMinimoParaResgate = string.Empty;

            if (pontoMinimoParaResgate > 0)
                frasePontoMinimoParaResgate = "Atualmente, a partir de " + pontoMinimoParaResgate + " pontos você pode resgatar:";

            corpo = String.Format(corpo,
                              nomeClienteEstabelecimento, //0
                              totalDePontosGanho,
                              dataPagamentoDaTransacao,
                              saldoAtualDePontos,
                              frasePontoMinimoParaResgate,
                              produtoParaResgatePontoMinimo, //5
                              dadosEstabelecimento,
                              textoAplicativo,
                              urlDownloadApp,
                              nomeEstabelecimentoParaLinkApp,
                              urlLogoEstabelecimento, //10
                              textoDePontosDaRede//11
                              );
            return corpo;
        }

        private static string TextoComSaldoTotal(int novoSaldoDePontosDoCliente)
        {
            return novoSaldoDePontosDoCliente == 1 ? novoSaldoDePontosDoCliente + " PONTO!" : novoSaldoDePontosDoCliente + " PONTOS!";
        }

        private static string ObterURLAplicativo(Estabelecimento estabelecimento, bool possuiAplicativoProprio)
        {
            var urlDownloadApp = "http://www.trinks.com/app";
            if (possuiAplicativoProprio)
            {
                if (estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Ativo && !string.IsNullOrWhiteSpace(estabelecimento.FranquiaEstabelecimento.Franquia.IdentificadorDownloadApp))
                {
                    urlDownloadApp = "http://www.trinks.com/app/" + estabelecimento.FranquiaEstabelecimento.Franquia.IdentificadorDownloadApp;
                }
                else
                {
                    urlDownloadApp = "http://www.trinks.com/app/" + estabelecimento.IdEstabelecimento;
                }
            }

            return urlDownloadApp;
        }

        private static bool PossuiAplicativoProprio(Estabelecimento estabelecimento)
        {
            return estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Ativo && estabelecimento.FranquiaEstabelecimento.Franquia.TemAplicativoPersonalizado;
        }

        private static string ObterTextoAplicativo(Estabelecimento estabelecimento, bool possuiAplicativoProprio)
        {
            var hotsiteEstabelecimento = estabelecimento.Hotsite();
            string textoAplicativo = "";

            if (possuiAplicativoProprio)
            {
                textoAplicativo = "<span style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px;'>Baixe o aplicativo <span style='font-family: Arial, Helvetica, sans-serif; color: #0d5ea9; text-align: left; font-weight: normal; font-size: 14px;'>";
                textoAplicativo += estabelecimento.FranquiaEstabelecimento.Franquia.Nome;
                textoAplicativo += "</span> gratuitamente ";

                if (hotsiteEstabelecimento.PermiteAgendamentoHotsite && hotsiteEstabelecimento.PermiteBuscaHotsite)
                    textoAplicativo += "para marcar seus horários e acompanhar seus agendamentos ";
                else
                    textoAplicativo += "para nos acompanhar ";

                textoAplicativo += "pelo smartphone ou tablet, de onde estiver!</span>";
            }
            else
            {
                textoAplicativo = "<span style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px;'>Baixe o aplicativo do <span style='font-family: Arial, Helvetica, sans-serif; color: #0d5ea9; text-align: left; font-weight: normal; font-size: 14px;'>Trinks.com</span> gratuitamente ";

                if (hotsiteEstabelecimento.PermiteAgendamentoHotsite && hotsiteEstabelecimento.PermiteBuscaHotsite)
                    textoAplicativo += "para marcar seus horários e acompanhar seus agendamentos ";
                else
                    textoAplicativo += "para nos acompanhar ";

                textoAplicativo += "pelo smartphone ou tablet, de onde estiver!</span>";
            }

            return textoAplicativo;
        }

        public string ListaProdutosOuServicosComMenorPontuacaoParaResgate(int idEstabelecimento, out int pontoMinimoParaResgate)
        {
            string produtoParaResgatePontoMinimo;
            pontoMinimoParaResgate = 0;

            //{5} lista do(s) produto(s) que pode ser resgatado quantidade de pontos menor que saldo cliente
            var listaProdutoPontoMinimo = Domain.Fidelidade.ProgramaDeFidelidadeRepository.ListaServicoOuProdutoComMenorPontoNecessarioParaResgate(idEstabelecimento, out pontoMinimoParaResgate);
            produtoParaResgatePontoMinimo = "";
            foreach (var item in listaProdutoPontoMinimo)
            {
                produtoParaResgatePontoMinimo += "<div style='font-family: Arial, Helvetica, sans-serif; font-size: 14px; color: #333; padding-top:10px;'><span style='color:#fb7e02; font-weight: bold;'>&bull;</span> " + item + "</div>";
            }

            return produtoParaResgatePontoMinimo;
        }

        private string ObterCorpoEmailProgramaDeFidelidadePontosGanhos(Transacao transacao, int totalDePontosGanhoNaTransacao, int novoSaldoDePontosDoCliente, int totalDePontosGanhos = 0)
        {
            string caminhoTemplate = "ProgramaDeFidelidadePontosGanhos.html";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var estabelecimento = transacao.PessoaQueRecebeu.Estabelecimento; // Não tenho esse dado no documento.

            //Informações do Corpo do Email
            //{0} nome do cliente
            var nomeClienteEstabelecimento = transacao.PessoaQuePagou.NomeOuApelido();
            var idPessoaDoCliente = transacao.PessoaQuePagou.IdPessoa;
            //{1} pontos ganhos
            int totalDePontosGanho = ObterPontosGanhosNaTransacao(totalDePontosGanhoNaTransacao, totalDePontosGanhos);

            //{2} data do serviço
            var dataPagamentoDaTransacao = transacao.DataHora;

            //{3} saldo atual
            var saldoAtualDePontos = TextoComSaldoTotal(novoSaldoDePontosDoCliente);

            //{4} a menor quantidade de pontos para resgatar um produto. texto: hoje a partir de XX pontos
            int pontoMinimoParaResgate;

            //{5} lista do(s) produto(s) que pode ser resgatado quantidade de pontos menor que saldo cliente
            var produtoParaResgatePontoMinimo = ListaProdutosOuServicosComMenorPontuacaoParaResgate(estabelecimento.IdEstabelecimento, out pontoMinimoParaResgate);

            //{6} dados do estabelecimento e link para hotsite - var dadosEstabelecimento
            var nomeEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;

            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var dadosEstabelecimento = ObterDadosEstabelecimento(estabelecimento);

            var possuiAplicativoProprio = PossuiAplicativoProprio(estabelecimento);
            //{7} texto aplicativo
            string textoAplicativo = ObterTextoAplicativo(estabelecimento, possuiAplicativoProprio);

            //{8} url para download app redirecionamento automático
            string urlDownloadApp = ObterURLAplicativo(estabelecimento, possuiAplicativoProprio);

            //{9} nome estabelecimento para p link do app
            var nomeEstabelecimentoParaLinkApp = ObterNomeParaLinkApp(estabelecimento);

            //{10} logo do estabelecimento / url logo estabelecimento
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var frasePontoMinimoParaResgate = string.Empty;

            if (pontoMinimoParaResgate > 0)
                frasePontoMinimoParaResgate = "Atualmente, a partir de " + pontoMinimoParaResgate + " pontos você pode resgatar:";

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(idPessoaDoCliente, estabelecimento.IdEstabelecimento);
            bool clientePossuiCpfOuEmail = !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Cpf) || !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Email);
            bool permiteTransferencias = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();
            //{10} pontos ganhos
            var totalDePontosGanhoNaRede = permiteTransferencias && clientePossuiCpfOuEmail ? Domain.Pessoas.ClienteEstabelecimentoRepository
                        .ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(estabelecimento.FranquiaEstabelecimento.Franquia.Id, clienteEstabelecimento.Cliente.PessoaFisica.Cpf, clienteEstabelecimento.Cliente.PessoaFisica.Email) + totalDePontosGanhoNaTransacao : 0 + totalDePontosGanhoNaTransacao;
            string textoDePontosDaRede = ObterTextoDePontosNaRede(permiteTransferencias, totalDePontosGanhoNaRede);

            corpo = String.Format(corpo,
                          nomeClienteEstabelecimento, //0
                              totalDePontosGanho,
                              dataPagamentoDaTransacao,
                              saldoAtualDePontos,
                          frasePontoMinimoParaResgate,
                          produtoParaResgatePontoMinimo, //5
                          dadosEstabelecimento,
                          textoAplicativo,
                          urlDownloadApp,
                          nomeEstabelecimentoParaLinkApp,
                          urlLogoEstabelecimento, //10
                          textoDePontosDaRede // 11
                                  );
            return corpo;
        }

        private static int ObterPontosGanhosNaTransacao(int totalDePontosGanhoNaTransacao, int totalDePontosGanhos)
        {
            return totalDePontosGanhoNaTransacao > 0 ? totalDePontosGanhoNaTransacao : totalDePontosGanhos;
        }

        private static string ObterTextoDePontosNaRede(bool permiteTransferencias, int totalDePontosGanhoNaRede)
        {
            return permiteTransferencias ? "<br /><span style = 'font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 16px;'> SEU SALDO TOTAL NA REDE É DE </span><span style = 'font-family: Arial, Helvetica, sans-serif; color:#fb7e02; font-weight: bold; font-size: 16px;'>" + totalDePontosGanhoNaRede + (totalDePontosGanhoNaRede == 1 ? " PONTO!" : " PONTOS!") + "</span> " : "";
        }

        public void EnviarEmailProgramaDeFidelidadePontosExpirandoNoDia(Estabelecimento estabelecimento, string nomeDoCliente, int pontosExpirados, string emailDoCliente, int saldoDoCliente, int pontoMinimoParaResgate, string produtoParaResgatePontoMinimo, int idPessoaDoCliente)
        {
            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailProgramaDeFidelidadePontosExpirandoNoDia;
            string corpo = ObterCorpoEmailProgramaDeFidelidadePontosExpirandoNoDia(estabelecimento, nomeDoCliente, pontosExpirados, saldoDoCliente, pontoMinimoParaResgate, produtoParaResgatePontoMinimo, idPessoaDoCliente);

            var emailCliente = emailDoCliente;
            var emailEstabelecimento = estabelecimento.PessoaJuridica.Email;

            MailAddress remetente = ObterRemetenteNaoResponda(estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            if (!String.IsNullOrEmpty(emailCliente))
            {
                destino.Add(new MailAddress(emailCliente));
                destinosOcultos.Add(new MailAddress(emailEstabelecimento));
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo);
            }
        }

        private string ObterCorpoEmailProgramaDeFidelidadePontosExpirandoNoDia(Estabelecimento estabelecimento, string nomeDoCliente, int pontosExpirados, int saldoDoCliente, int pontoMinimoParaResgate, string produtoParaResgatePontoMinimo, int idPessoaDoCliente)
        {
            string caminhoTemplate = "ProgramaDeFidelidadePontosExpirandoNoDia.html";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            //Informações do Corpo do Email

            //{0} nome do cliente
            var nomeClienteEstabelecimento = nomeDoCliente; // <nome do cliente>

            //{1} pontos expirados
            var quantidadeDePontosExpirandoNoPrazo = pontosExpirados;

            //{5} dados do estabelecimento e link para hotsite - var dadosEstabelecimento
            var nomeEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;

            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var dadosEstabelecimento = ObterDadosEstabelecimento(estabelecimento);

            var possuiAplicativoProprio = PossuiAplicativoProprio(estabelecimento);
            //{6} texto aplicativo
            string textoAplicativo = ObterTextoAplicativo(estabelecimento, possuiAplicativoProprio);

            //{7} url para download app redirecionamento automático
            string urlDownloadApp = ObterURLAplicativo(estabelecimento, possuiAplicativoProprio);

            //{8} nome estabelecimento para p link do app
            var nomeEstabelecimentoParaLinkApp = ObterNomeParaLinkApp(estabelecimento);

            //{9} logo do estabelecimento / url logo estabelecimento
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var frasePontoMinimoParaResgate = string.Empty;

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(idPessoaDoCliente, estabelecimento.IdEstabelecimento);
            bool clientePossuiCpfOuEmail = !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Cpf) || !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Email);
            bool permiteTransferencias = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();
            //{10} pontos ganhos
            var totalDePontosGanhoNaRede = permiteTransferencias && clientePossuiCpfOuEmail ? Domain.Pessoas.ClienteEstabelecimentoRepository
                        .ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(estabelecimento.FranquiaEstabelecimento.Franquia.Id, clienteEstabelecimento.Cliente.PessoaFisica.Cpf, clienteEstabelecimento.Cliente.PessoaFisica.Email) : 0;
            var textoDePontosDaRede = ObterTextoDePontosNaRede(permiteTransferencias, totalDePontosGanhoNaRede);

            var saldoAtualDePontos = TextoComSaldoTotal(saldoDoCliente);

            if (pontoMinimoParaResgate > 0)
                frasePontoMinimoParaResgate = "Atualmente, a partir de " + pontoMinimoParaResgate + " pontos você pode resgatar:";

            corpo = String.Format(corpo,
                              nomeClienteEstabelecimento, //0
                              quantidadeDePontosExpirandoNoPrazo,
                              saldoAtualDePontos,
                              frasePontoMinimoParaResgate,
                              produtoParaResgatePontoMinimo,
                              dadosEstabelecimento, //5
                              textoAplicativo,
                              urlDownloadApp,
                              nomeEstabelecimentoParaLinkApp,
                              urlLogoEstabelecimento, //9
                              textoDePontosDaRede //10
                                  );
            return corpo;
        }

        private static string ObterNomeParaLinkApp(Estabelecimento estabelecimento)
        {
            if (estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Ativo && estabelecimento.FranquiaEstabelecimento.Franquia.TemAplicativoPersonalizado)
                return estabelecimento.FranquiaEstabelecimento.Franquia.Nome.ToUpper();

            return "TRINKS.COM";
        }

        public void EnviarEmailProgramaDeFidelidadePontosExpirandoNoPrazo(Estabelecimento estabelecimento, string nomeDoCliente, int pontosExpirados, string emailDoCliente, int diasDeAntecedenciaParaNotificarValidadeDosPontos, int saldoDoCliente, int pontoMinimoParaResgate, string produtoParaResgatePontoMinimo, int idPessoaDoCliente)
        {
            string assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailProgramaDeFidelidadePontosExpirandoNoPrazo;
            string corpo = ObterCorpoEmailProgramaDeFidelidadePontosExpirandoNoPrazo(estabelecimento, nomeDoCliente, pontosExpirados, emailDoCliente, diasDeAntecedenciaParaNotificarValidadeDosPontos, saldoDoCliente, pontoMinimoParaResgate, produtoParaResgatePontoMinimo, idPessoaDoCliente);

            var emailCliente = emailDoCliente;
            var emailEstabelecimento = estabelecimento.PessoaJuridica.Email;

            MailAddress remetente = ObterRemetenteNaoResponda(estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            if (!String.IsNullOrEmpty(emailCliente))
            {
                destino.Add(new MailAddress(emailCliente));
                destinosOcultos.Add(new MailAddress(emailEstabelecimento));
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo);
            }
        }

        private string ObterCorpoEmailProgramaDeFidelidadePontosExpirandoNoPrazo(Estabelecimento estabelecimento, string nomeDoCliente, int pontosExpirados, string emailDoCliente, int diasDeAntecedenciaParaNotificarValidadeDosPontos, int saldoDoCliente, int pontoMinimoParaResgate, string produtoParaResgatePontoMinimo, int idPessoaDoCliente)
        {
            string caminhoTemplate = "ProgramaDeFidelidadePontosExpirandoNoPrazo.html";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            //Informações do Corpo do Email

            //{0} nome do cliente
            var nomeClienteEstabelecimento = nomeDoCliente; // <nome do cliente>

            //{1} pontos expirados
            var quantidadeDePontosExpirandoNoPrazo = pontosExpirados;

            //{2} dias que expiram os pontos
            var quatidadeDeDiasQueExpiraOsPontos = diasDeAntecedenciaParaNotificarValidadeDosPontos; //<dias que expiram os pontos informado na configuração do programa de fidelidade>

            //{6} dados do estabelecimento e link para hotsite - var dadosEstabelecimento
            var nomeEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;

            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var dadosEstabelecimento = ObterDadosEstabelecimento(estabelecimento);

            var possuiAplicativoProprio = PossuiAplicativoProprio(estabelecimento);
            //{7} texto aplicativo
            string textoAplicativo = ObterTextoAplicativo(estabelecimento, possuiAplicativoProprio);

            //{8} url para download app redirecionamento automático
            var urlDownloadApp = urlHotsiteEstabelecimento + "/app";

            //{9} nome estabelecimento para p link do app
            var nomeEstabelecimentoParaLinkApp = ObterNomeParaLinkApp(estabelecimento);

            //{10} logo do estabelecimento / url logo estabelecimento
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var frasePontoMinimoParaResgate = string.Empty;

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimentoPorPF(idPessoaDoCliente, estabelecimento.IdEstabelecimento);
            bool clientePossuiCpfOuEmail = !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Cpf) || !string.IsNullOrWhiteSpace(clienteEstabelecimento.Cliente.PessoaFisica.Email);
            bool permiteTransferencias = estabelecimento.EhUnidadeDeFranquiaQueRealizaTransferenciaDePontosDeFidelidade();
            //{11} pontos ganhos
            var totalDePontosGanhoNaRede = permiteTransferencias && clientePossuiCpfOuEmail ? Domain.Pessoas.ClienteEstabelecimentoRepository
                        .ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(estabelecimento.FranquiaEstabelecimento.Franquia.Id, null, emailDoCliente) : 0;
            var textoDePontosDaRede = ObterTextoDePontosNaRede(permiteTransferencias, totalDePontosGanhoNaRede);

            var saldoAtualDePontos = TextoComSaldoTotal(saldoDoCliente);

            if (pontoMinimoParaResgate > 0)
                frasePontoMinimoParaResgate = "Atualmente, a partir de " + pontoMinimoParaResgate + " pontos você pode resgatar:";

            corpo = String.Format(corpo,
                              nomeClienteEstabelecimento, //0
                                  quantidadeDePontosExpirandoNoPrazo,
                                  quatidadeDeDiasQueExpiraOsPontos,
                              saldoAtualDePontos,
                              frasePontoMinimoParaResgate,
                              produtoParaResgatePontoMinimo, //5
                              dadosEstabelecimento,
                              textoAplicativo,
                              urlDownloadApp,
                              nomeEstabelecimentoParaLinkApp,
                              urlLogoEstabelecimento, //10
                              textoDePontosDaRede //11
                                  );
            return corpo;
        }

        private string ObterDadosEstabelecimento(Estabelecimento estabelecimento)
        {
            var configuracaoHotsiteEstabelecimento = DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url) ? String.Empty : "/" + configuracaoHotsiteEstabelecimento.Url;

            var nomeFantasia = estabelecimento.PessoaJuridica.NomeFantasia;
            var telefones = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();
            var urlHotsite = string.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);
            var endereco = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            return "<span style='color:#1175ca; font-size:14px;'>" + nomeFantasia + "</span>&nbsp;&nbsp;<a style='decoration: none; color: #333;text-decoration: none;font-size:11px;' href='" + urlHotsite +
                            "' target='_blank'>visitar site do estabelecimento</a><br /><br /><span style = 'font-family: Arial, Helvetica, sans-serif; color: #333; font-size:12px;' >" + endereco +
                            "<br />" + telefones + "</span>";
        }

        public string ObterConteudoEmailMarketingProgramaDeFidelidade(Estabelecimento estabelecimento)
        {
            string caminhoTemplate = "ConteudoEmailMarketingProgramaDeFidelidade.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = String.Format(corpo, "[cliente]", estabelecimento.NomeDeExibicaoNoPortal);
            return corpo;
        }

        public string ObterCorpoEmailMarketingProgramaDeFidelidade(Estabelecimento estabelecimento)
        {
            var corpo = ObterConteudoEmailMarketingProgramaDeFidelidade(estabelecimento);
            return ObterCorpoEmailMarketing(estabelecimento, "Nome do Cliente", corpo, 0);
        }

        #endregion Programa de Fidelidade

        #region E-mail Cliente Faltou

        public void EnviarEmailAlteracaoStatusClienteFaltou(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            if (!clienteEstabelecimento.EnviarEmailAgendamentoCliente || !clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var assunto = String.Format("{0} - {1}", horario.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailClienteFaltou);
            var corpo = ObterEnviarEmailAlteracaoStatusClienteFaltou(horario, statusAntigo, clienteEstabelecimento);

            var remetente = ObterRemetenteNaoResponda(horario.Estabelecimento);

            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null,
                        replyToAddress.ToArray());
                }
            }
        }

        private string ObterEnviarEmailAlteracaoStatusClienteFaltou(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "AgendamentoClienteFaltou.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var razaoSocial = horario.Estabelecimento.PessoaJuridica.NomeFantasia;
            var nomeServico = horario.ServicoEstabelecimento.Nome;

            var dataHora = String.Format("{0} {1}", horario.DataInicio.ObterSiglaDiaDaSemana(),
                horario.DataInicio.ToBrazilianLongDateTimeString());
            var duracao = horario.Duracao.PorExtenso();

            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    horario.Estabelecimento.IdEstabelecimento);
            var preco = String.Format("{0} {1}", horario.ServicoEstabelecimento.ObterComplementoDoPreco(true),
                horario.ServicoEstabelecimento.ObterPreco(true));
            var statusAtual = horario.Status.Nome;

            var nomeFantasia = horario.Estabelecimento.PessoaJuridica.NomeFantasia;

            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
                ? String.Empty
                : "/" + configuracaoHotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var logradouro = horario.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = horario.Estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(horario.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var exibirNomeProfissional =
                horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
            var profissional = exibirNomeProfissional ? string.Format(@"<tr>
                                                            <td width='160'>
                                                                <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: right; font-weight: bold; font-size: 12px; margin: 5px 0;'>Profissional:</p>
                                                            </td>
                                                            <td width='10'>&nbsp;</td>
                                                            <td width='350'>
                                                                <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-size: 12px; margin: 5px 0;'>{0}</p>
                                                            </td>
                                                        </tr>", ObterApelidoOuNomeDoProfissional(horario.Profissional)) : "";

            corpo = String.Format(corpo, nomeCliente, razaoSocial, nomeServico, profissional, dataHora, duracao,
                preco, statusAtual, urlHotsite, logradouro, telefones,
                CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo), urlLogoEstabelecimento, informacoesDoAplicativo.TextoAplicativo,
                informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);
            return corpo;
        }

        #endregion E-mail Cliente Faltou

        #region E-mail Status Finalizado

        private void EnviarEmailAlteracaoStatusFinalizado(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            if (!clienteEstabelecimento.EnviarEmailAgendamentoCliente || !clienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                return;

            var assunto = String.Format("{0} - {1}", horario.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAgendamentoFinalizado);
            var corpo = ObterEnviarEmailAlteracaoStatusFinalizado(horario, statusAntigo, clienteEstabelecimento);

            var remetente = ObterRemetentePadrao();

            var destino = new List<MailAddress>();

            var email = clienteEstabelecimento.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            var replyToAddress = new List<MailAddress>();
            //MailAddress atendimento = ObterEmailDeAtendimento();
            //replyToAddress.Add(atendimento);

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null,
                        replyToAddress.ToArray());
                }
            }
        }

        private string ObterEnviarEmailAlteracaoStatusFinalizado(Horario horario,
            StatusHorario statusAntigo,
            ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "AgendamentoFinalizado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            var razaoSocial = horario.Estabelecimento.PessoaJuridica.NomeFantasia;
            var nomeServico = horario.ServicoEstabelecimento.Nome;
            var dataHora = String.Format("{0} {1}", horario.DataInicio.ObterSiglaDiaDaSemana(),
                horario.DataInicio.ToBrazilianLongDateTimeString());
            var duracao = horario.Duracao.PorExtenso();

            var configuracaoHotsite =
                Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                    horario.Estabelecimento.IdEstabelecimento);
            var preco = String.Format("{0} {1}", horario.ServicoEstabelecimento.ObterComplementoDoPreco(true),
                horario.ServicoEstabelecimento.ObterPreco(true));
            var statusAtual = horario.Status.Nome;

            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
                ? String.Empty
                : "/" + configuracaoHotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var logradouro = horario.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = horario.Estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();

            var exibirNomeProfissional =
                horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;

            var profissional = exibirNomeProfissional ? string.Format(@"<tr>
                                                        <td width='160'><p style='font-family:Arial, Helvetica, sans-serif; color:#333; text-align:right; font-weight:bold; font-size:12px; margin:5px 0;'>Profissional:</p></td>
                                                        <td width='10'>&nbsp;</td>
                                                        <td width='350'><p style='font-family:Arial, Helvetica, sans-serif; color:#333; text-align:left; font-size:12px; margin:5px 0;'>{0}</p></td>
                                                    </tr>", ObterApelidoOuNomeDoProfissional(horario.Profissional)) : "";

            corpo = String.Format(corpo, nomeCliente, razaoSocial, nomeServico, profissional, dataHora, duracao, preco,
                statusAtual, urlHotsite, logradouro, telefones,
                CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo), informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);
            return corpo;
        }

        #endregion E-mail Status Finalizado

        #region E-mail Alteração de nome de Serviço ou Categoria

        public void EnviarEmailAlteracaoNomeServicoOuCategoriaPadrao(object antigo, object novo)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailMudancaNomeServicoOuCategoriaPadrao;
            var ehServico = (antigo is ServicoEstabelecimento);
            string nomePadrao, nomeNovo, nomeAnterior;
            Estabelecimento estabelecimento;
            string corpo;

            if (ehServico)
            {
                var servicoAntigo = (ServicoEstabelecimento)antigo;
                var servicoNovo = (ServicoEstabelecimento)novo;
                var servicoPadraoNovoNotNull = servicoNovo.Servico != null;
                var servicoCategoriaPadraoNovoNotNull = servicoPadraoNovoNotNull &&
                                                        servicoNovo.Servico.ServicoCategoria != null;

                nomePadrao = servicoPadraoNovoNotNull ? servicoNovo.Servico.Nome : String.Empty;
                nomeNovo = servicoNovo.Nome;
                nomeAnterior = servicoAntigo.Nome;

                var categoriaEstabelecimentoAntigo =
                    Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Load(
                        servicoAntigo.ServicoCategoriaEstabelecimento.Codigo);
                ServicoCategoria categoriaPadrao = null;
                if (servicoCategoriaPadraoNovoNotNull)
                {
                    categoriaPadrao =
                        Domain.Pessoas.ServicoCategoriaRepository.Load(servicoNovo.Servico.ServicoCategoria.Codigo);
                }

                var nomeCategoriaPadrao = servicoCategoriaPadraoNovoNotNull ? categoriaPadrao.Nome : String.Empty;
                var nomeCategoriaNovo = servicoNovo.ServicoCategoriaEstabelecimento.Nome;
                var nomeCategoriaAnterior = categoriaEstabelecimentoAntigo.Nome;

                estabelecimento = servicoNovo.Estabelecimento;
                corpo = ObterEnviarEmailAlteracaoNomeServicoPadrao(nomePadrao, nomeNovo, nomeAnterior,
                    nomeCategoriaPadrao, nomeCategoriaNovo, nomeCategoriaAnterior, estabelecimento);
            }
            else
            {
                var categoriaAntigo = (ServicoCategoriaEstabelecimento)antigo;
                var categoriaNovo = (ServicoCategoriaEstabelecimento)novo;

                nomePadrao = categoriaNovo.ServicoCategoria != null ? categoriaNovo.ServicoCategoria.Nome : String.Empty;
                nomeNovo = categoriaNovo.Nome;
                nomeAnterior = categoriaAntigo.Nome;

                estabelecimento = categoriaNovo.Estabelecimento;
                corpo = ObterEnviarEmailAlteracaoNomeCategoriaPadrao(false, nomePadrao, nomeNovo, nomeAnterior,
                    estabelecimento);
            }

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress> { ObterEmailTecnico() };

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterEnviarEmailAlteracaoNomeServicoPadrao(string nomePadrao,
            string nomeNovo,
            string nomeAnterior,
            string nomeCategoriaPadrao,
            string nomeCategoriaNovo,
            string nomeCategoriaAnterior,
            Estabelecimento estabelecimento)
        {
            var caminhoTemplate = "AlteracaoNomeServicoCategoriaPadrao.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            estabelecimento.Refresh();

            corpo = String.Format(corpo, "Serviço", nomePadrao, nomeNovo, nomeAnterior, Calendario.Agora(),
                estabelecimento.PessoaJuridica.NomeFantasia, nomeCategoriaPadrao + "/", nomeCategoriaNovo + "/",
                nomeCategoriaAnterior + "/");
            return corpo;
        }

        private string ObterEnviarEmailAlteracaoNomeCategoriaPadrao(bool ehServico,
            string nomePadrao,
            string nomeNovo,
            string nomeAnterior,
            Estabelecimento estabelecimento,
            string nomeCategoriaPadrao = "")
        {
            var caminhoTemplate = "AlteracaoNomeServicoCategoriaPadrao.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var tipo = ehServico ? "Serviço" : "Categoria";

            estabelecimento.Refresh();

            corpo = String.Format(corpo, "Categoria", nomePadrao, nomeNovo, nomeAnterior, Calendario.Agora(),
                estabelecimento.PessoaJuridica.NomeFantasia, String.Empty, String.Empty, String.Empty);
            return corpo;
        }

        #endregion E-mail Alteração de nome de Serviço ou Categoria

        #region E-mail Novo Serviço ou Categoria não padrão

        public void EnviarEmailNovoServicoOuCategoriaNaoPadrao(object novo)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailNovoServicoOuCategoriaNaoPadrao;
            var ehServico = (novo is ServicoEstabelecimento);
            string nome;
            Estabelecimento estabelecimento;

            if (ehServico)
            {
                var servicoNovo = (ServicoEstabelecimento)novo;
                nome = servicoNovo.Nome;
                estabelecimento = servicoNovo.Estabelecimento;
            }
            else
            {
                var categoriaNovo = (ServicoCategoriaEstabelecimento)novo;
                nome = categoriaNovo.Nome;
                estabelecimento = categoriaNovo.Estabelecimento;
            }

            var corpo = ObterEnviarEmailNovoServicoOuCategoriaNaoPadrao(ehServico, nome, estabelecimento);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            destino.Add(ObterRemetentePadrao());

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterEnviarEmailNovoServicoOuCategoriaNaoPadrao(bool ehServico,
            string nome,
            Estabelecimento estabelecimento)
        {
            var caminhoTemplate = "NovoServicoOuCategoriaNaoPadrao.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var tipo = ehServico ? "Serviço" : "Categoria";
            corpo = String.Format(corpo, tipo, nome, Calendario.Agora(), estabelecimento.PessoaJuridica.NomeFantasia);
            return corpo;
        }

        #endregion E-mail Novo Serviço ou Categoria não padrão

        #region Cliente Cadastrado

        public void EnviarEmailBoasVindasClienteWeb(Cliente clienteCadastrado, bool origemAgendamentoHotsite = false, int? idEstabelecimentoParaEnvioDeEmail = null)
        {
            var conteudoTemplate = "ClienteWebBoasVindas.cshtml";

            var artigoCliente = Genero.ObterPorCodigo(clienteCadastrado.PessoaFisica.Genero).PronomeTratamento().ToLower();

            var nome = clienteCadastrado.PessoaFisica.NomeCompleto;
            var assunto = String.Format(ConfiguracoesTrinks.EnvioEmail.AssuntoEmailBoasVindasClienteWeb, artigoCliente);

            var Estabelecimento = idEstabelecimentoParaEnvioDeEmail != null ? Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimentoParaEnvioDeEmail.Value) : null;
            var nomeEstabelecimento = Estabelecimento?.NomeDeExibicaoNoPortal;
            var tipoEstabelecimento = Estabelecimento?.TipoEstabelecimento;
            var artigoEstabelecimento = Genero.ObterPorCodigo(tipoEstabelecimento?.Genero).PronomeTratamento().ToLower();

            var textoBoasVindas = ObterTextosDeBoasVindas(artigoCliente, artigoEstabelecimento, nomeEstabelecimento, origemAgendamentoHotsite);
            var textoPasso1 = ObterTextosDePasso1(origemAgendamentoHotsite);

            string urlLogoEstabelecimento = ObterTrechoDeImagensParaTopoDoEmail(Estabelecimento);

            var corpo = String.Format(ObterTextoCompletoDoArquivo(conteudoTemplate), artigoCliente, nome, textoBoasVindas, textoPasso1, urlLogoEstabelecimento);

            var remetente = ObterRemetentePadrao();
            var email = clienteCadastrado.PessoaFisica.PrimeiraConta.Email;
            var destino = new List<MailAddress>();

            if (!email.EmailValido())
                return;
            destino.Add(new MailAddress(email));

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterTrechoDeImagensParaTopoDoEmail(Estabelecimento estabelecimento)
        {
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92, false);

            urlLogoEstabelecimento = string.IsNullOrWhiteSpace(urlLogoEstabelecimento)
                ? ""
                : $"<td style='border-bottom: 7px solid #EEE;' align='center'><table width='580' border='0' cellspacing='0' cellpadding='0'><tr><td align='left'><img src='{urlLogoEstabelecimento}' height='92' alt='Logo do estabelecimento'></td><td style='text-align: right; vertical-align: bottom;'><img src='trinks-email-logo-1.png'></td></tr></table></td>";

            return urlLogoEstabelecimento;
        }

        private string ObterTextosDeBoasVindas(string artigoCliente, string artigoEstabelecimento, string nomeEstabelecimento, bool origemAgendamentoHotsite)
        {
            if (origemAgendamentoHotsite && nomeEstabelecimento != null)
                return "Seja bem-vind" + artigoCliente + " ao agendamento online d" + artigoEstabelecimento + " " + nomeEstabelecimento + "!";
            else
                return "Seja bem-vind" + artigoCliente + " ao agendamento online do Trinks!";
        }

        private string ObterTextosDePasso1(bool origemAgendamentoHotsite)
        {
            if (origemAgendamentoHotsite)
                return "Acesse o site do estabelecimento desejado ou <a href='{ BASE_URL}' style='text-decoration: none;' title='Acesse o Trinks, clicando aqui.'><strong style='color:#BC262C'>www.trinks.com</strong></a> para encontrar a região, estabelecimento ou unidade da sua preferência.";
            else
                return "Acesse <a href='{ BASE_URL}' style='text-decoration: none;' title='Acesse o Trinks, clicando aqui.'><strong style='color:#BC262C'>www.trinks.com</strong></a> para encontrar a região, estabelecimento ou unidade da sua preferência.";
        }

        public void EnviarEmailClienteBalcaoCadastrado(ClienteEstabelecimento cliente)
        {
            var assunto = String.Format("{0} - {1}", cliente.Estabelecimento.PessoaJuridica.NomeFantasia,
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailClienteBalcaoCadastrado);
            var corpo = ObterCorpoEmailClienteBalcaoCadastrado(cliente);

            var remetente = ObterRemetenteNaoResponda(cliente.Estabelecimento);

            var destino = new List<MailAddress>();

            var email = cliente.DadosDoCliente.Email;
            if (email.EmailValido())
                destino.Add(new MailAddress(email));

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                    EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        private string ObterCorpoEmailClienteBalcaoCadastrado(ClienteEstabelecimento clienteEstabelecimento)
        {
            var caminhoTemplate = "ClienteBalcaoCadastrado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(clienteEstabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var artigo = ((string)clienteEstabelecimento.Cliente.PessoaFisica.Genero).ToArtigoGenero();
            var nomeCliente = clienteEstabelecimento.DadosDoCliente.NomeCompleto;
            Estabelecimento estabelecimento = clienteEstabelecimento.Estabelecimento;
            var nomeFantasia = estabelecimento.PessoaJuridica.NomeFantasia;
            var concluaSeuCadastro = ObterTextoConclusaoDeCadastroParaClienteBalcao(clienteEstabelecimento);
            var textoSobreTrinks = ObterTextoSobreTrinksParaClienteBalcao(clienteEstabelecimento);
            var email = clienteEstabelecimento.DadosDoCliente.Email;
            var codigoEstabelecimento = estabelecimento.IdEstabelecimento;
            var hotsite = estabelecimento.Hotsite().Url;

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            corpo = String.Format(corpo, nomeCliente, artigo, nomeFantasia, concluaSeuCadastro,
                CodigoClienteEmStringBase64(clienteEstabelecimento.Codigo), textoSobreTrinks, hotsite, urlLogoEstabelecimento,
                informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);

            return corpo;
        }

        #endregion Cliente Cadastrado

        #region Lembrete de Agendamentos Marcados

        public void EnviarEmailLembreteAgendamentosMarcados(string nomeCliente, string emailCliente, int idClienteEstabelecimento, Estabelecimento estabelecimento, List<LembrarAgendamentosAgendamentoDTO> agendamentos)
        {
            if (agendamentos.Count() > 0)
            {
                //if (!horarioBase.ClienteEstabelecimento.EnviarEmailAgendamentoCliente || !horarioBase.ClienteEstabelecimento.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarEmailsAgendamentoParaClientes)
                //    return;

                var assunto = String.Format("{0} - {1}", estabelecimento.PessoaJuridica.NomeFantasia,
                    ConfiguracoesTrinks.EnvioEmail.AssuntoLembreteAgendamentosAmanha);
                var corpo = String.Empty;

                var remetente = ObterRemetenteNaoResponda(estabelecimento);

                var responderPara = new List<MailAddress>();
                //responderPara.Add(ObterEmailDeAtendimento());

                var destino = new List<MailAddress>();
                destino.Add(new MailAddress(emailCliente));

                corpo = ObterCorpoEmailLembreteAgendamentosMarcados(nomeCliente, emailCliente, idClienteEstabelecimento, estabelecimento, agendamentos);

                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null,
                        responderPara.ToArray());
                }
            }
        }

        public void EnviarInformacoesSobreEnviarEmailLembreteAgendamentosMarcados(Int32 quantidadeDeEmails,
            String emailsParaEnvioDoLembrete)
        {
            var assunto = String.Format(
                ConfiguracoesTrinks.EnvioEmail.AssuntoEmailInformativoLembreteAgendamentosAmanha, quantidadeDeEmails);
            var corpo = ObterCorpoEmailClientesQuePossuemAgendamentoNoDiaSeguinte(emailsParaEnvioDoLembrete);

            var remetente = ObterRemetentePadrao();
            var destino = ObterEmailDeAtendimento();
            var listaDestino = new List<MailAddress>();
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoEmailLembreteAgendamentosMarcados(string nomeCliente, string emailCliente, int idClienteEstabelecimento, Estabelecimento estabelecimento,
            List<LembrarAgendamentosAgendamentoDTO> agendamentos)
        {
            var caminho = "LembreteAgendamentosMarcados.cshtml";
            var caminhoTemplate = RetornarUrlFormatada(caminho);

            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();

            var hotsite = estabelecimento.Hotsite();
            var franquiaEstabelecimento = estabelecimento.FranquiaEstabelecimento;

            ObterTextoEUrlDoAplicativo(franquiaEstabelecimento, hotsite, informacoesDoAplicativo); //<texto e url do aplicativo>

            var caracterS = agendamentos.Count > 1 ? "s" : String.Empty;
            var listaAgendamentos = String.Empty;

            foreach (var agendamento in agendamentos.OrderBy(f => f.Horario.DataInicio))
                listaAgendamentos += ObterTabelaDeAgendamentosMarcados(agendamento);

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var configuracaoHotsite = estabelecimento.Hotsite();

            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsite.Url)
                ? String.Empty
                : "/" + configuracaoHotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            return String.Format(corpo, nomeCliente, caracterS, listaAgendamentos,
                CodigoClienteEmStringBase64(idClienteEstabelecimento), urlLogoEstabelecimento, urlHotsite,
                informacoesDoAplicativo.TextoAplicativo, informacoesDoAplicativo.UrlAppStore, informacoesDoAplicativo.UrlPlayStore);
        }

        private string ObterTabelaDeAgendamentosMarcados(LembrarAgendamentosAgendamentoDTO agendamento)
        {
            var caminho = "ItemAgendamentoMarcado.cshtml";
            var caminhoTemplate = RetornarUrlFormatada(caminho);

            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            Estabelecimento estabelecimento = agendamento.Horario.Estabelecimento;

            var exibirNomeProfissional =
                estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;

            var estabelecimentoNome = estabelecimento.PessoaJuridica.NomeFantasia;
            var linkHotsite = estabelecimento.Hotsite() != null
                ? estabelecimento.Hotsite().Url
                : string.Empty;
            var endereco = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            var telefones = Domain.Pessoas.TelefoneRepository.ListarTelefonesPropriosDaPessoa(estabelecimento.PessoaJuridica.IdPessoa).ToTextoFormatadoLista();
            var servico = agendamento.NomeServicoEstabelecimento;
            var profissional = exibirNomeProfissional
                ? string.Format(@"<tr>
                    <td width='160'><p style='font-family:Arial, Helvetica, sans-serif; color:#333; text-align:right; font-weight:bold; font-size:12px; margin:5px 0;'>Profissional:</p></td>
                    <td width='10'>&nbsp;</td>
                    <td width='350'><p style='font-family:Arial, Helvetica, sans-serif; color:#333; text-align:left; font-size:12px; margin:5px 0;'>{0}</p></td></tr>",
                    agendamento.NomeProfissional)
                : "";

            var dataHora = String.Format("{0} {1} às {2}", agendamento.Horario.DataInicio.ObterSiglaDiaDaSemana(),
                agendamento.Horario.DataInicio.ToString("dd/MM/yyyy", new CultureInfo("pt-BR")),
                agendamento.Horario.DataInicio.ToString("HH:mm", new CultureInfo("pt-BR")));
            var duracao = agendamento.Horario.Duracao.PorExtenso();
            var valor = agendamento.Horario.MensagemPreco;
            var status = agendamento.Horario.Status.Nome;

            return String.Format(corpo, estabelecimentoNome, servico, profissional, dataHora, duracao, valor, status,
                linkHotsite, telefones, endereco);
        }

        private string ObterCorpoEmailClientesQuePossuemAgendamentoNoDiaSeguinte(String emails)
        {
            var caminhoTemplate = "ClientesComAgendamentoNoDiaSeguinte.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = String.Format(corpo, emails);

            return corpo;
        }

        #endregion Lembrete de Agendamentos Marcados

        #region Enviar Solicitação de Estabelecimento para Aparecer no Trinks.com

        public void EnviarEmailSolicitacaoEstabelecimentoAparecerNaBuscaPortal(String urlHotsite,
            String nomeFantasiaEstabelecimento)
        {
            var assunto = "Solicitação do estabelecimento para aparecer no portal";
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailDeAtendimento();
            listaDestino.Add(destino);

            var caminhoTemplate = "SolicitacaoEstabelecimentoAparecerNaBuscaPortal.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = String.Format(corpo, urlHotsite, nomeFantasiaEstabelecimento);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailSolicitacaoEstabelecimentoAparecerNaBuscaPortalParaCliente(
            EstabelecimentoSolicitacaoAparecerBusca solicitacao)
        {
            var nomeEstabelecimento = solicitacao.Estabelecimento.PessoaJuridica.NomeFantasia;
            var solicitante = solicitacao.PessoaFisicaQueSolicitou;

            var assunto = String.Format(ConfiguracoesTrinks.EnvioEmail.AssuntoEmailSolicitacaoEnviadaPeloCliente,
                nomeEstabelecimento);
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var email = solicitante.PrimeiraConta.Email;

            if (email.EmailValido())
                listaDestino.Add(new MailAddress(email));

            var hotsite = solicitacao.Estabelecimento.Hotsite();
            var urlHotSiteExistente = String.IsNullOrEmpty(hotsite.Url) ? String.Empty : "/" + hotsite.Url;

            var linkHotSite = "";
            if (hotsite != null)
                linkHotSite = ObterUrlBase() + urlHotSiteExistente;

            var caminhoTemplate = "SolicitacaoEstabelecimentoAparecerNaBuscaPortalParaCliente.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = String.Format(corpo, solicitante.NomeCompleto, nomeEstabelecimento, linkHotSite);

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(listaDestino.ToArray(), null, remetente, assunto, corpo, null, null, false);

                EmailManager.SendHtml(new[] { ObterEmailDeAtendimento() }, null, new MailAddress(email), assunto, corpo, null, null, false);
            }
        }

        #endregion Enviar Solicitação de Estabelecimento para Aparecer no Trinks.com

        #region Enviar Informação de Remoção do Estabelecimento para Aparecer no Trinks.com

        public void EnviarEmailInformandoRemocaoDoEstabelecimentoNaBuscaPortal(String nomeFantasiaEstabelecimento)
        {
            var assunto = "O estabelecimento não deseja mais aparecer no portal";
            var corpo = String.Empty;
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailDeAtendimento();
            listaDestino.Add(destino);

            corpo = String.Format("Estabelecimento {0} não deseja mais aparecer do portal.", nomeFantasiaEstabelecimento);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        #endregion Enviar Informação de Remoção do Estabelecimento para Aparecer no Trinks.com

        #region E-mail Estabelecimento Indicado

        public void EnviarEmailEstabelecimentoIndicado(EstabelecimentoIndicado estabelecimentoIndicado)
        {
            var assuntoEmail = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailEstabelecimentoIndicado + estabelecimentoIndicado.NomeEstabelecimento;

            var corpo = ObterCorpoEmailEstabelecimentoIndicado(estabelecimentoIndicado);

            var listaDestino = new List<MailAddress>();

            var remetente = ObterEmailDeContato();
            var destino = ObterEmailDeAtendimento();
            var vendas = ObterEmailVendas();
            var copiaInterna = ObterEmailCopiaInterna();
            listaDestino.Add(destino);
            listaDestino.Add(vendas);
            listaDestino.Add(copiaInterna);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assuntoEmail, corpo, null, null);
        }

        private string ObterCorpoEmailEstabelecimentoIndicado(EstabelecimentoIndicado estabelecimentoIndicado)
        {
            var caminhoTemplate = "EstabelecimentoIndicado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var telefonesDeContato = String.Empty;
            if (String.IsNullOrEmpty(estabelecimentoIndicado.TelefoneEstabelecimento2))
                telefonesDeContato = estabelecimentoIndicado.TelefoneEstabelecimento1;
            else
            {
                telefonesDeContato = String.Format("{0} / {1}", estabelecimentoIndicado.TelefoneEstabelecimento1,
                    estabelecimentoIndicado.TelefoneEstabelecimento2);
            }

            corpo = String.Format(corpo, estabelecimentoIndicado.NomePessoaIndicante,
                estabelecimentoIndicado.EmailPessoaIndicante, estabelecimentoIndicado.NomeEstabelecimento,
                estabelecimentoIndicado.NomeContatoEstabelecimento, telefonesDeContato,
                estabelecimentoIndicado.EmailEstabelecimento, estabelecimentoIndicado.ObservacaoSobreEstabelecimento);

            return corpo;
        }

        #endregion E-mail Estabelecimento Indicado

        #region E-mail Cadastro de Estabelecimento

        public void EnviarEmailEstabelecimentoCadastrado(Estabelecimento estabelecimento)
        {
            var assuntoEmail = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailEstabelecimentoCadastrado + " - " +
                               estabelecimento.IdEstabelecimento;

            var corpo = ObterCorpoEmailEstabelecimentoCadastrado(estabelecimento);

            var listaDestino = new List<MailAddress>();

            var remetente = ObterRemetentePadrao();
            var destino = ObterEmailDeAtendimento();
            var emailCopiaInternaDestino = ObterEmailCopiaInterna();
            listaDestino.Add(emailCopiaInternaDestino);
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assuntoEmail, corpo, null, null);
        }

        private string ObterCorpoEmailEstabelecimentoCadastrado(Estabelecimento estabelecimento)
        {
            var caminhoTemplate = "NovoEstabelecimentoCadastrado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var responsavelPeloEstabelecimento = estabelecimento.ObterResponsavel();
            var queryCodigoParceiro = Domain.Cobranca.EstabelecimentoParceriasTrinksRepository.ObterParceriaPorIdEstabelecimento(estabelecimento.IdEstabelecimento);
            var cupomParceiro = "--";
            if (queryCodigoParceiro != null)
                cupomParceiro = Domain.Cobranca.ParceriaTrinksRepository.ObterCupomPorIdParceria(queryCodigoParceiro.IdParceria);
            var pessoaFisica = responsavelPeloEstabelecimento.PessoaFisica;
            pessoaFisica.Refresh();
            var pessoaJuridica = estabelecimento.PessoaJuridica;
            string textoTipoDeCadastro = ObterTextoTipoDeCadastro(pessoaJuridica);

            corpo = string.Format(corpo, pessoaJuridica.NomeFantasia,
                pessoaJuridica.Telefones.ToTextoFormatadoLista(),
                estabelecimento.TipoEstabelecimento,
                pessoaFisica.NomeCompleto,
                pessoaFisica.PrimeiraConta.Email,
                pessoaFisica.TelefonesProprios().Ativos().ToTextoFormatadoLista(),
                pessoaFisica.Cpf, pessoaJuridica.RazaoSocial,
                !string.IsNullOrEmpty(pessoaJuridica.CNPJ) ? pessoaJuridica.CNPJ : "Não possui",
                pessoaJuridica.Email,
                pessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                pessoaFisica.DataNascimento.HasValue ? pessoaFisica.DataNascimento.Value.ToDataNascimentoString() : "Não informado",
                EnumActions.GetEnumText(estabelecimento.FaixaProfissionais),
                cupomParceiro,
                textoTipoDeCadastro);

            return corpo;
        }

        private static string ObterTextoTipoDeCadastro(PessoaJuridica pessoaJuridica)
        {
            var dadosMarketingEstabelecimento = Domain.MarketingInterno.DadosMarketingRepository.ObterPorIdPessoa(pessoaJuridica.IdPessoa);
            string textoTipoDeCadastro = string.Empty;
            if (dadosMarketingEstabelecimento != null)
            {
                if (dadosMarketingEstabelecimento.Dispositivo == "mobile")
                {
                    textoTipoDeCadastro = "Mobile";
                    if (dadosMarketingEstabelecimento.UtmSource == "trinks.app.profissional")
                    {
                        textoTipoDeCadastro += " - App Trinks Profissionais";
                    }
                    else
                    {
                        textoTipoDeCadastro += " - Navegador";
                    }
                }
                else
                {
                    textoTipoDeCadastro = "Web";
                }
            }
            else
            {
                textoTipoDeCadastro = "Não Informado";
            }

            return textoTipoDeCadastro;
        }

        #endregion E-mail Cadastro de Estabelecimento

        #region Estatísticas de Uso do Trinks.com

        public void EnviarEmailEstatisticasSobreUsoDoSistema(BIUsoDoSistema usoDoSistema)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEstatisticasSobreUsoDoSistema;
            var remetente = ObterRemetentePadrao();

            var destino = new List<MailAddress> { ObterEmailTecnico() };

            var corpo = ObterCorpoEmailEstatisticasSobreUsoDoSistema(usoDoSistema);
            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoEmailEstatisticasSobreUsoDoSistema(BIUsoDoSistema usoDoSistema)
        {
            var caminho = "EstatisticasSobreUsoDoSistema.cshtml";
            var caminhoTemplate = RetornarUrlFormatada(caminho);

            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            return String.Format(corpo,
                usoDoSistema.DataReferencia,
                usoDoSistema.DataRegistro,
                usoDoSistema.QuantidadeEstabelecimentoCriaramAgendamentoPeloBackofficeNoDia,
                usoDoSistema.QuantidadeEstabelecimentoReceberamAgendamentoDoCliente,
                usoDoSistema.QuantidadeEstabelecimentoCadastrados,
                usoDoSistema.QuantidadeEstabelecimentoCadastradosAparecemNoPortal,
                usoDoSistema.QuantidadeEstabelecimentoCadastradosNoDia,
                usoDoSistema.QuantidadeEstabelecimentoFecharamContaNoDia,
                usoDoSistema.QuantidadeAgendamentosCriadosNoDia,
                usoDoSistema.QuantidadeAgendamentosCriadosPeloWebNoDia,
                usoDoSistema.QuantidadeAgendamentosCriadosPeloMobileNoDia,
                usoDoSistema.QuantidadeSMSEnviadosNoDia,
                usoDoSistema.QuantidadeUsuariosBalcaoAtivos, usoDoSistema.QuantidadeUsuariosBalcaoCadastradosNoDia,
                usoDoSistema.QuantidadeUsuariosWebAtivos, usoDoSistema.QuantidadeUsuariosWebCadastradosNoDia,
                usoDoSistema.SomatorioFaturasPagasNoPeriodo.ValorDecimal(),
                usoDoSistema.SomatorioFaturasPagasMarketingNoPeriodo.ValorDecimal(),
                //TRINKS-8591
                usoDoSistema.QuantidadeHorariosCriadosAtravesSiteDaFranquiaNoDia,
                usoDoSistema.QuantidadeEstabelecimentosAssinadosNoDia,
                usoDoSistema.QuantidadeEstabelecimentosCanceladosNoDia,
                usoDoSistema.QuantidadeEstabelecimentosComPrimeiraFaturaPagaNoDia,
                usoDoSistema.QuantidadeAgendamentosCriadosPeloMobileRedeNoDia,
                //Trinks-14434
                usoDoSistema.QuantidadeAgendamentosCadastradosGoogleReserve);
        }

        #endregion Estatísticas de Uso do Trinks.com

        #region Metodos de Apoio

        private string ObterUrlBase()
        {
            var url = !String.IsNullOrWhiteSpace(ConfiguracoesTrinks.EnvioEmail.UrlBase)
                ? ConfiguracoesTrinks.EnvioEmail.UrlBase
                : Domain.WebContext.ObterUrlDoHost();

            // [init] Ajuste da URL para o mobile.
            var ajusteCaminhoBaseUrl = ConfigurationManager.AppSettings["AjusteDoCaminhoBaseUrl"];
            if (!string.IsNullOrEmpty(ajusteCaminhoBaseUrl))
            {
                var uri = new Uri(url);
                Uri.TryCreate(uri, ajusteCaminhoBaseUrl, out uri);
                url = uri.ToString();
            }
            // [end]

            return RefinaUrlBase(url);
        }

        private static string RefinaUrlBase(string url)
        {
            var ultimoCaracter = url[url.Length - 1];
            return ultimoCaracter == '/' ? url.Trim(ultimoCaracter) : url;
        }

        private string ObterTextoCompletoDoArquivo(string caminhoDoArquivo)
        {
            var telefonesRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_telefones).ObterValor();
            var emailAtendimentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_email_atendimento).ObterValor();
            var horarioFuncionamentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_horario_funcionamento).ObterValor().ToLower();

            return
                ResourceManager.ReadText(typeof(Textos).Assembly,
                    "Perlink.Trinks.Resources.TemplatesEmail." + caminhoDoArquivo).Replace("{BASE_URL}", ObterUrlBase())
                                                                                  .Replace("{TelefonesRodape}", telefonesRodape)
                                                                                  .Replace("{EmailAtendimentoRodape}", emailAtendimentoRodape)
                                                                                  .Replace("{HorarioFuncionamentoRodape}", horarioFuncionamentoRodape);
            //File.ReadAllText(caminhoDoArquivo, Encoding.GetEncoding("ISO-8859-1"));
        }

        private string ObterPreco(HorarioTransacao item)
        {
            var desconto = item.Desconto.HasValue ? item.Desconto.Value : 0;
            return item.Preco.HasValue ? (item.Preco.Value - desconto).ObterValorEmReais() : "0,00";
        }

        private string ObterApelidoOuNomeDoProfissional(Profissional profissional)
        {
            if (profissional == null)
                return Textos.FilaDeEspera;
            return !String.IsNullOrEmpty(profissional.PessoaFisica.Apelido)
                ? profissional.PessoaFisica.Apelido
                : profissional.PessoaFisica.NomeCompleto;
        }

        private string RetornarUrlFormatada(string caminhoTemplate)
        {
            var textoFinal = caminhoTemplate.Contains("bin")
                ? caminhoTemplate.Replace("\\bin", String.Empty)
                : caminhoTemplate;
            textoFinal = textoFinal.Contains("Debug") ? textoFinal.Replace("\\Debug", String.Empty) : textoFinal;
            return textoFinal;
        }

        private string CodigoClienteEmStringBase64(int codigoDoClienteEstabelecimento)
        {
            return codigoDoClienteEstabelecimento.ToString().EncodeToBase64String();
        }

        #endregion Metodos de Apoio

        #region E-mails Conta Financeira

        public void EnviarEmailStatusCobrancaAlterado(String nomeEstabelecimento, String statusCobranca, Conta ContaAutenticada)
        {
            var assunto = "Cobrança do estabelecimento '" + nomeEstabelecimento + "' foi alterada para " +
                          statusCobranca;

            if (ContaAutenticada != null)
                assunto += " por '" + ContaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'";

            var corpo = assunto;
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailMudancasPlano();
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailPrazoAdicionado(String nomeEstabelecimento, String statusCobranca, DateTime finalDaTolerancia, Conta ContaAutenticada, Int32 diasIncluidos)
        {
            var assunto = "Prazo de tolerância final do estabelecimento '" + nomeEstabelecimento + "' foi alterado de "
                + finalDaTolerancia.AddDays(-diasIncluidos).ToShortDateString() + " para " + finalDaTolerancia.ToShortDateString() + " por '"
                + ContaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'";

            var corpo = assunto;
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailMudancasPlano();
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailDuracaoFaturaEstendida(String nomeEstabelecimento, String novaData, Conta ContaAutenticada)
        {
            var assunto = "Vencimento da próxima fatura do estabelecimento '" + nomeEstabelecimento +
                          "' foi alterado para " + novaData + " por '" + ContaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'";

            var corpo = assunto;
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailMudancasPlano();
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailAssociacaoAhPromocao(String nomeEstabelecimento, String nomePromocao, Conta contaAutenticada)
        {
            var assunto = "O estabelecimento '" + nomeEstabelecimento + "' foi associado a promoção '" + nomePromocao + "' por '"
                + contaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'";

            var corpo = assunto;
            var remetente = ObterRemetentePadrao();

            var listaDeDestinatarios = new List<MailAddress>();
            var destinatario = ObterEmailMudancasPlano();
            listaDeDestinatarios.Add(destinatario);

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(listaDeDestinatarios.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        public void EnviarEmailAtribuicaoDeDescontoNoAdicional(String nomeEstabelecimento, String nomeAdicional, decimal percentualDeDesconto, int quantasFaturas, Conta contaAutenticada, string motivo)
        {
            var assunto = "O estabelecimento '" + nomeEstabelecimento
                + "' teve desconto de " + percentualDeDesconto + "% no adicional '" + nomeAdicional + "' nas próximas " + quantasFaturas + " faturas por '"
                + contaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'";

            var corpo = assunto + "<br />Motivo: " + motivo;
            var remetente = ObterRemetentePadrao();

            var listaDeDestinatarios = new List<MailAddress>();
            var destinatario = ObterEmailMudancasPlano();
            listaDeDestinatarios.Add(destinatario);

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(listaDeDestinatarios.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        public void EnviarEmailAlteracoesEmPromocaoTrinks(String nomeEstabelecimento, PromocaoPraContaFinanceira promocaoOriginal, PromocaoPraContaFinanceira promocaoAlterada, Conta contaAutenticada)
        {
            var assunto = "Alterações em promoção Trinks para o estabelecimento '" + nomeEstabelecimento + "'";
            var nomeOuApelidoContaAutenticada = contaAutenticada.Pessoa.PessoaFisica.NomeOuApelido();

            var corpo = String.Empty;

            if (promocaoOriginal.QuantasMensalidadesODescontoSerahAplicado != promocaoAlterada.QuantasMensalidadesODescontoSerahAplicado)
                corpo = corpo + "A vigência da promoção trinks para o estabelecimento '" + nomeEstabelecimento + "' foi modificada de " + promocaoOriginal.QuantasMensalidadesODescontoSerahAplicado + " meses para " + promocaoAlterada.QuantasMensalidadesODescontoSerahAplicado + " meses por '" + nomeOuApelidoContaAutenticada + "'<br />";

            if (promocaoOriginal.DataLimiteDeAquisicao != promocaoAlterada.DataLimiteDeAquisicao)
                corpo = corpo + "A data de limite da promoção trinks para o estabelecimento '" + nomeEstabelecimento + "' foi modificada por '" + nomeOuApelidoContaAutenticada + "' de " + promocaoOriginal.DataLimiteDeAquisicao.ToBrazilianShortDateString() + " para " + promocaoAlterada.DataLimiteDeAquisicao.ToBrazilianShortDateString() + "<br />";

            if (promocaoOriginal.Desconto != promocaoAlterada.Desconto)
                corpo = corpo + "O percentual de desconto da promoção trinks para o estabelecimento '" + nomeEstabelecimento + "' foi modificada por '" + nomeOuApelidoContaAutenticada + "' de " + promocaoOriginal.Desconto + "% para " + promocaoAlterada.Desconto + "%<br />";

            if (promocaoOriginal.Ativo != promocaoAlterada.Ativo)
                corpo = corpo + "A promoção trinks para o estabelecimento '" + nomeEstabelecimento + "' foi " + (promocaoAlterada.Ativo ? "ativada" : "inativada") + " por '" + nomeOuApelidoContaAutenticada + "'<br />";

            var remetente = ObterRemetentePadrao();

            var listaDeDestinatarios = new List<MailAddress>();
            var destinatario = ObterEmailMudancasPlano();
            listaDeDestinatarios.Add(destinatario);

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(listaDeDestinatarios.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        public void EnviarEmailCancelamentoDeAssinatura(String nomeEstabelecimento, Conta ContaAutenticada, int quantidadeDeProfissionaisAtivosComAgenda, string motivoCancelamento, string comentarioMotivoDoCancelamento, string faixa)
        {
            var assunto = "A assinatura do estabelecimento '" + nomeEstabelecimento + "' foi cancelada";
            var corpo = ObterCorpoEmailCancelamentoDeAssinatura(assunto, nomeEstabelecimento, ContaAutenticada, quantidadeDeProfissionaisAtivosComAgenda, motivoCancelamento, comentarioMotivoDoCancelamento, faixa);

            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailMudancasPlano();
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailPeriodoGratuidadeEstendido(String nomeEstabelecimento, String novaData, Conta ContaAutenticada)
        {
            var assunto = "Fim do período de gratuidade do estabelecimento '" + nomeEstabelecimento +
                          "' foi alterado para " + novaData + " por '" + ContaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'";

            var corpo = assunto;
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailMudancasPlano();
            listaDestino.Add(destino);

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public void EnviarEmailContaFinanceiraNaoAssinanteA10DiasDoCancelamento(ContaFinanceira conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.ContaFinanceiraNaoAssinanteExpiradaA10Dias;
            PreparaEmailContaFinanceira(conta, assunto);
            LogService<EnvioEmailService>.Info("EnviarEmailContaFinanceiraPrestesACancelar da conta #" +
                                               conta.IdContaFinanceira);
        }

        public void EnviarEmailContaFinanceiraAExpirar15(ContaFinanceira conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaFinanceiraAExpirar15;
            PreparaEmailContaFinanceira(conta, assunto);
            LogService<EnvioEmailService>.Info("EnviarEmailContaFinanceiraAExpirar15 da conta #" +
                                               conta.IdContaFinanceira);
        }

        public void EnviarEmailContaFinanceiraAExpirar10(ContaFinanceira conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaFinanceiraAExpirar10;
            PreparaEmailContaFinanceira(conta, assunto);
            LogService<EnvioEmailService>.Info("EnviarEmailContaFinanceiraAExpirar10 da conta #" +
                                               conta.IdContaFinanceira);
        }

        public void EnviarEmailCartaoAExpirar(ContaFinanceira conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaFinanceiraAExpirar10;
            PreparaEmailContaFinanceira(conta, assunto);
            LogService<EnvioEmailService>.Info("EnviarEmailContaFinanceiraAExpirar10 da conta #" +
                                               conta.IdContaFinanceira);
        }

        public void EnviarEmailContaFinanceiraAExpirar3(ContaFinanceira conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaFinanceiraAExpirar3;
            PreparaEmailContaFinanceira(conta, assunto);
            LogService<EnvioEmailService>.Info("EnviarEmailContaFinanceiraAExpirar3 da conta #" +
                                               conta.IdContaFinanceira);
        }

        public void EnviarEmailContaFinanceiraUltimoDia(ContaFinanceira conta)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaFinanceiraAExpirar1;
            PreparaEmailContaFinanceira(conta, assunto);
            LogService<EnvioEmailService>.Info("EnviarEmailContaFinanceiraUltimoDia da conta #" +
                                               conta.IdContaFinanceira);
        }

        private void PreparaEmailContaFinanceira(ContaFinanceira conta, string assunto)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(conta.Pessoa.IdPessoa);
            var responsaveis = ObterEmailsResponsaveisEstabelecimento(estabelecimento);
            string corpo;

            if (assunto == ConfiguracoesTrinks.EnvioEmail.AssuntoEmailContaFinanceiraAExpirar1)
                corpo = ObterCorpoEmailContaFinanceiraUltimoDia(conta, estabelecimento);
            else
            {
                if (assunto == ConfiguracoesTrinks.EnvioEmail.ContaFinanceiraNaoAssinanteExpiradaA10Dias)
                    corpo = ObterCorpoEmailContaFinanceiraNaoAssinanteA10DiasDoCancelamento(conta, estabelecimento);
                else
                    corpo = ObterCorpoEmailContaFinanceiraAExpirar(conta, estabelecimento);
            }

            if (!string.IsNullOrEmpty(corpo))
            {
                if (!string.IsNullOrEmpty(conta.Pessoa.Email))
                {
                    PersonalizarEEnviarEmaisDeContaFinanceira(corpo, conta.Pessoa.Email,
                        conta.Pessoa.PessoaJuridica.ResponsavelFinanceiro.NomeCompleto, assunto);
                }

                foreach (var user in responsaveis)
                {
                    if ((conta.Pessoa.Email) != (user.Address))
                        PersonalizarEEnviarEmaisDeContaFinanceira(corpo, user.Address, user.DisplayName, assunto);
                }
            }
        }

        private void PersonalizarEEnviarEmaisDeContaFinanceira(string corpoDeEmail,
            string destinatario,
            string nomeCompleto,
            string assunto)
        {
            if (String.IsNullOrEmpty(destinatario))
                return;

            var remetente = ObterRemetentePadrao();
            corpoDeEmail = IncluirNomeDoDestinatarioAoEmail(nomeCompleto, corpoDeEmail);
            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(new[] { new MailAddress(destinatario) },
                    new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente, assunto,
                    corpoDeEmail);
            }
        }

        private string ObterCorpoEmailCancelamentoDeAssinatura(string assunto, String nomeEstabelecimento, Conta ContaAutenticada, int quantidadeDeProfissionaisAtivosComAgenda, string motivoCancelamento, string comentarioMotivoDoCancelamento, string faixa)
        {
            var corpo = assunto;

            if (ContaAutenticada != null)
                corpo += " por '" + ContaAutenticada.Pessoa.PessoaFisica.NomeOuApelido() + "'" + (motivoCancelamento.Length > 0 ? $", cujo o motivo do cancelamento é: '{motivoCancelamento}'" : "") +
                    (comentarioMotivoDoCancelamento.Length > 0 ? $" e o comentario feito pelo cliente foi '{comentarioMotivoDoCancelamento}'" : "") + ".";

            corpo += " O estabelecimento possui " + quantidadeDeProfissionaisAtivosComAgenda + (quantidadeDeProfissionaisAtivosComAgenda > 1 ?
                     " profissionais ativos com agenda." : " profissional ativo com agenda.") + " Com a faixa do estabelecimento de: " + faixa;

            return corpo;
        }

        private string ObterCorpoEmailContaFinanceiraUltimoDia(ContaFinanceira conta, Estabelecimento estabelecimento)
        {
            var corpo = ObterCorpoEmailCobranca("ContaFinanceiraAExpirarUltimoDia");
            corpo = AdicionarFaixasDeCobrancaAoCorpoDoEmail(corpo);
            corpo = AdicionarDadosDoEstabelecimentoAoCorpoDoEmail(estabelecimento, corpo);
            return corpo;
        }

        private string IncluirNomeDoDestinatarioAoEmail(String nomeDoDestinatario, String corpo)
        {
            return corpo.Replace("{0}", nomeDoDestinatario);
        }

        private string ObterCorpoEmailContaFinanceiraNaoAssinanteA10DiasDoCancelamento(ContaFinanceira conta,
            Estabelecimento estabelecimento)
        {
            var corpo = ObterCorpoEmailCobranca("ContaFinanceiraNaoAssinanteExpiradaA10Dias");
            corpo = AdicionarFaixasDeCobrancaAoCorpoDoEmail(corpo);
            corpo = AdicionarDadosDoEstabelecimentoAoCorpoDoEmail(estabelecimento, corpo);
            return corpo.Replace("{1}", conta.AssinaturaAtiva().DiasRestantesDeDegustacao(Calendario.Agora()).ToString());
        }

        private string ObterCorpoEmailCobranca(string nomeDoArquivo)
        {
            var caminhoTemplate = nomeDoArquivo + ".cshtml";
            return ObterTextoCompletoDoArquivo(caminhoTemplate);
        }

        private string AdicionarFaixasDeCobrancaAoCorpoDoEmail(string corpoEmail)
        {
            return corpoEmail.Replace("{FAIXAS_COBRANCA}", ObterFaixaConbrancaHtmlEmail());
        }

        private string ObterFaixaConbrancaHtmlEmail()
        {
            var faixasDeCobranca = new StringBuilder();

            var planoAssinatura = Domain.Cobranca.PlanoAssinaturaRepository.ObterPlanoDeAssinaturaComFaixasDeMenorValor();
            var valoresPorFaixa = planoAssinatura.ValoresPorFaixa;
            var ultimaFaixa = valoresPorFaixa.LastOrDefault();
            foreach (var faixa in valoresPorFaixa)
            {
                var linha = string.Empty;
                if (!faixa.Equals(ultimaFaixa))
                {
                    linha = string.Format(@"<tr><td style='text-align:left;'>De {0} a {1} profissionais</td>
		                                    <td style='border-bottom:1px dotted #d1d1d1; width:110px'>&nbsp;</td>
		                                    <td style='text-align:left;padding-left:5px;'>a partir de {2} mensais</td></tr>", faixa.LimiteInferior,
                        faixa.LimiteSuperior, faixa.ValorPorMes().HasValue ? faixa.ValorPorMes().Value.ObterValorEmReais() : "R$ 0,00");
                }
                else
                {
                    linha = string.Format(@"<tr>
												<td style='text-align:left;'>{0} ou mais profissionais</td>
												<td style='border-bottom:1px dotted #d1d1d1; width:110px'>&nbsp;</td>
												<td style='text-align:left;padding-left:5px;'>a partir de {1} mensais</td>
											</tr>
                                            <tr>
												<td colspan='3' height='5'></td>
											</tr>
											<tr>
												<td colspan='3' height='5'><span style='font-style:italic; font-size: 12px;'>
												** preços referentes ao {2}</span></td>
											</tr>"
                                            , faixa.LimiteInferior
                                            , faixa.ValorPorMes().HasValue ? faixa.ValorPorMes().Value.ObterValorEmReais() : "R$ 0,00"
                                            , planoAssinatura.Titulo);
                }
                faixasDeCobranca.Append(linha);
            }

            return faixasDeCobranca.ToString();
        }

        private string AdicionarDadosDoEstabelecimentoAoCorpoDoEmail(Estabelecimento estabelecimento, string corpoEmail)
        {
            return corpoEmail.Replace("{DADOS_ESTABELECIMENTO}", ObterDadosEstabelecimentoHtmlEmail(estabelecimento));
        }

        private string ObterDadosEstabelecimentoHtmlEmail(Estabelecimento estabelecimento)
        {
            var dadosDoEstabelecimento = new StringBuilder();

            var endereco = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var telefone = estabelecimento.PessoaJuridica.Telefones.ToTextoFormatadoLista();
            var url = string.Empty;
            var urlCompleto = string.Empty;

            var hotsite = estabelecimento.Hotsite();

            if (hotsite != null && hotsite.PermiteBuscaHotsite)
                url = estabelecimento.Hotsite().Url;

            urlCompleto = string.Format("{0}/{1}", ObterUrlBase(), url);

            var linha1 = String.Empty;
            var linha2 = String.Empty;

            linha1 = string.Format(@"<tr><td height='25' style='text-align: center'></td></tr>
                                     <tr><td height='15'><table width='520' border='0' cellspacing='0' cellpadding='0'>
                                        <tr><td width='25'><img src='ico-info.png' width='20' height='21' /></td><td width='495'>
                                        <h1 style='font-family: Arial, Helvetica, sans-serif; font-size: 16px; color: #e07203; margin: 0; text-align: left; font-weight: bold;'>
                                        Informações do Estabelecimento</h1></td></tr></table></td></tr><tr><td height='5'>
                                    </td></tr>");

            linha2 = string.Format(@"<tr><td>
                                        <table width='520' border='0' cellspacing='0' cellpadding='0' style='background: #f2f2f2; border: 1px solid #e4e4e4;'>
                                            <tr><td width='10' height='10'></td><td width='500' height='10'></td><td width='10' height='10'></td></tr>
                                            <tr><td width='10'>&nbsp;</td><td width='500'>
                                            <table width='500' border='0' cellspacing='0' cellpadding='0'>
                                            <tr><td align='left'><p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: bold; font-size: 14px; margin: 5px 0;'></p>
                                            <a style='color: #0d5ea9; font-family: Arial, Helvetica, sans-serif;' href='{0}'>{1}</a></td></tr>
                                            <tr><td height='10'></td></tr>
                                            <tr><td height='1' background='pontilhado.png'></td></tr><tr><td height='10'></td></tr><tr><td>
                                            <p style='font-family: Arial, Helvetica, sans-serif; color: #333; text-align: left; font-weight: normal; font-size: 14px; margin: 0 0 5px 0;'>
                                            {2}<br />{3}</p></td></tr></table></td>
                                            <td width='10'>&nbsp;</td></tr>
                                            <tr><td width='10' height='10'></td><td width='500' height='10'></td><td width='10' height='10'></td></tr>
                                         </table>
                                     </td></tr>", urlCompleto, estabelecimento.NomeDeExibicaoNoPortal, endereco,
                telefone);
            dadosDoEstabelecimento.Append(linha1);
            dadosDoEstabelecimento.Append(linha2);
            return dadosDoEstabelecimento.ToString();
        }

        private string ObterCorpoEmailContaFinanceiraAExpirar(ContaFinanceira conta, Estabelecimento estabelecimento)
        {
            var corpo = ObterCorpoEmailCobranca("ContaFinanceiraAExpirar");
            corpo = AdicionarFaixasDeCobrancaAoCorpoDoEmail(corpo);
            corpo = AdicionarDadosDoEstabelecimentoAoCorpoDoEmail(estabelecimento, corpo);
            return corpo.Replace("{1}", conta.AssinaturaAtiva().DiasRestantesDeDegustacao(Calendario.Agora()).ToString());
        }

        #endregion E-mails Conta Financeira

        #region E-mail Assinatura Cancelada Pelo Usuário

        public void EnviarEmailAssinaturaCanceladaPeloUsuario(String emailResponsavelFinanceiro,
            DateTime dataCancelamento,
            Pessoa pessoaQueCancelou,
            String motivoDoCancelamento,
            HotsiteEstabelecimento hotSite)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailAssinaturaCancelada;
            var corpo = ObterCorpoEmailAssinaturaCanceladaPeloUsuario(hotSite, dataCancelamento, pessoaQueCancelou,
                motivoDoCancelamento);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (!emailResponsavelFinanceiro.EmailValido())
                return;
            destino.Add(new MailAddress(emailResponsavelFinanceiro));

            var destinosOcultos = new List<MailAddress>();
            var emailFinanceiro = ObterEmailFinanceiro();
            var emailAtendimento = ObterEmailDeAtendimento();
            destinosOcultos.Add(emailFinanceiro);
            destinosOcultos.Add(emailAtendimento);

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto,
                    corpo);
            }
        }

        private string ObterCorpoEmailAssinaturaCanceladaPeloUsuario(HotsiteEstabelecimento hotsite,
            DateTime dataCancelamento,
            Pessoa pessoaQueCancelou,
            String motivoDoCancelamento)
        {
            var caminhoTemplate = "AssinaturaCancelada.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var seExisteSite = String.IsNullOrEmpty(hotsite.Url) ? String.Empty : "/" + hotsite.Url;
            var urlHotsite = String.Format(ObterUrlBase() + "{0}", seExisteSite);
            var endereco = hotsite.Estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var telefones = hotsite.Estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();
            var nomeFantasia = hotsite.Estabelecimento.PessoaJuridica.NomeFantasia;

            var nomeCompleto = pessoaQueCancelou != null ? pessoaQueCancelou.PessoaFisica.NomeCompleto : "Sistema";
            var nomeUsuario = hotsite.Estabelecimento.ObterResponsavel().NomeCompleto;

            corpo = string.Format(corpo, dataCancelamento.ToShortDateString(),
                nomeUsuario, motivoDoCancelamento, dataCancelamento.ToShortTimeString(),
                nomeFantasia, urlHotsite, endereco, telefones, nomeCompleto);

            return corpo;
        }

        #endregion E-mail Assinatura Cancelada Pelo Usuário

        #region E-mail Pagamento da Fatura Aprovado

        public void EnviarEmailPagamentoDaFaturaAprovado(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailPagamentoFaturaAprovado;
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoaJuridica.IdPessoa);

            var corpo = ObterCorpoEmailPagamentoDaFaturaAprovado(pessoaJuridica, fatura);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();

            var responsaveis =
                estabelecimento.VinculoUsuarios.Where(f => f.PerfilAcesso == AcessoBackoffice.Acesso_total && f.Ativo)
                    .Select(f => f.PessoaFisica);

            foreach (var r in responsaveis)
                if (r.PrimeiraConta.Email.EmailValido())
                    destino.Add(new MailAddress(r.PrimeiraConta.Email));

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(),
                        new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente,
                        assunto, corpo);
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailPagamentoDaFaturaAprovado da fatura #" + fatura.IdFatura);
        }

        private string ObterCorpoEmailPagamentoDaFaturaAprovado(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var caminhoTemplate = "PagamentoDaFaturaAprovado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var inicioPeriodoReferencia = fatura.InicioPeriodoReferencia.HasValue
                ? fatura.InicioPeriodoReferencia.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var fimPeriodoReferencia = fatura.FimPeriodoReferencia.HasValue
                ? fatura.FimPeriodoReferencia.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var dataDeVencimento = fatura.DataVencimento.HasValue
                ? fatura.DataVencimento.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var dataDeAprovacao = fatura.DataAprovacao.HasValue
                ? fatura.DataAprovacao.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var valorAprovado = String.Format("R$ {0}", fatura.ValorAprovado.Value.ToString("N"));

            var statusPagamento = "Pagamento Aprovado";
            if (fatura.StatusTemporarioTransacaoGateway != "Aprovada")
                statusPagamento = String.Format("Pagamento {0}", fatura.StatusTemporarioTransacaoGateway);

            var responsavel =
                pessoaJuridica.Estabelecimento.VinculoUsuarios.Where(
                    f => f.PerfilAcesso == AcessoBackoffice.Acesso_total && f.Ativo).Select(f => f.PessoaFisica).First();

            corpo = string.Format(corpo, responsavel.NomeCompleto, pessoaJuridica.Estabelecimento.Hotsite().Url,
                fatura.IdFatura, inicioPeriodoReferencia, fimPeriodoReferencia, valorAprovado, dataDeAprovacao,
                fatura.IdTransacao, statusPagamento, String.Empty, pessoaJuridica.NomeFantasia,
                pessoaJuridica.EnderecoProprio.ToTextoFormatado(), pessoaJuridica.Telefones.ToTextoFormatadoLista());

            return corpo;
        }

        #endregion E-mail Pagamento da Fatura Aprovado

        #region E-mail Pagamento Por Boleto Aguardando Confirmação

        public void EnviarEmailPagamentoAguardandoConfirmacao(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailPagamentoFaturaAprovado;
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoaJuridica.IdPessoa);

            var corpo = ObterCorpoEmailPagamentoAguardandoConfirmacao(pessoaJuridica, fatura);

            List<Attachment> anexos = new List<Attachment>();

            if (EhPagamentoIUGU())
            {
                anexos.Add(CriarAnexoBoletoParaEmail(fatura));
            }
            else
            {
                LogService<EnvioEmailService>.Info(mensagemDeErroIntegracaoIUGU);
                throw new InvalidOperationException(mensagemDeErroIntegracaoIUGU);
            }

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();

            var responsaveis =
                estabelecimento.VinculoUsuarios.Where(f => f.PerfilAcesso == AcessoBackoffice.Acesso_total && f.Ativo)
                    .Select(f => f.PessoaFisica);

            foreach (var r in responsaveis)
            {
                if (r.PrimeiraConta != null)
                    if (r.PrimeiraConta.Email.EmailValido())
                        destino.Add(new MailAddress(r.PrimeiraConta.Email));
            }

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(),
                        new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente,
                        assunto, corpo, null, anexos.ToArray());
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailPagamentoDaFaturaAprovado da fatura #" + fatura.IdFatura);
        }

        private string ObterCorpoEmailPagamentoAguardandoConfirmacao(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var caminhoTemplate = "PagamentoAguardandoConfirmacao.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var inicioPeriodoReferencia = fatura.InicioPeriodoReferencia.HasValue
                ? fatura.InicioPeriodoReferencia.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var fimPeriodoReferencia = fatura.FimPeriodoReferencia.HasValue
                ? fatura.FimPeriodoReferencia.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var dataDeVencimento = fatura.DataVencimento.HasValue
                ? fatura.DataVencimento.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var dataDeAprovacao = fatura.DataAprovacao.HasValue
                ? fatura.DataAprovacao.Value.ToString("dd/MM/yyyy")
                : String.Empty;
            var valorAprovado = String.Format("R$ {0}", fatura.ValorCobrado.Value.ToString("N"));

            var statusPagamento = "Aguardando confirmação do pagamento";

            var responsavel =
                pessoaJuridica.Estabelecimento.VinculoUsuarios.Where(
                    f => f.PerfilAcesso == AcessoBackoffice.Acesso_total).Select(f => f.PessoaFisica).FirstOrDefault() ?? new PessoaFisica();

            corpo = string.Format(corpo, responsavel.NomeCompleto, pessoaJuridica.Estabelecimento.Hotsite().Url,
                fatura.IdFatura, inicioPeriodoReferencia, fimPeriodoReferencia, valorAprovado, dataDeVencimento,
                fatura.IdTransacao, statusPagamento, String.Empty, pessoaJuridica.NomeFantasia,
                pessoaJuridica.EnderecoProprio.ToTextoFormatado(), pessoaJuridica.Telefones.ToTextoFormatadoLista());

            return corpo;
        }

        #endregion E-mail Pagamento Por Boleto Aguardando Confirmação

        #region E-mail Pagamento da Fatura Não Autorizado

        public void EnviarEmailPagamentoDaFaturaNaoAutorizado(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailPagamentoFaturaNaoAutorizado;
            var corpo = ObterCorpoEmailPagamentoDaFaturaNaoAutorizado(pessoaJuridica, fatura);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (pessoaJuridica.ResponsavelFinanceiro.PrimeiraConta.Email.EmailValido())
                destino.Add(new MailAddress(pessoaJuridica.ResponsavelFinanceiro.PrimeiraConta.Email));

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(),
                        new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente,
                        assunto, corpo);
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailPagamentoDaFaturaNaoAutorizado da fatura #" + fatura.IdFatura);
        }

        private string ObterCorpoEmailPagamentoDaFaturaNaoAutorizado(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var caminhoTemplate = "PagamentoDaFaturaNaoAutorizado.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var hotsite = pessoaJuridica.Estabelecimento.Hotsite();
            var hotsiteUrl = hotsite != null ? ObterUrlBase() + "/" + hotsite.Url : "";
            corpo = string.Format(corpo, pessoaJuridica.ResponsavelFinanceiro.NomeCompleto, fatura.IdFatura,
                fatura.InicioPeriodoReferencia.Value.ToBrazilianShortDateString(),
                fatura.FimPeriodoReferencia.Value.ToBrazilianShortDateString(), fatura.ValorCobrado,
                Calendario.Agora().ToBrazilianLongDateTimeString(), fatura.MotivoTemporarioTransacaoGateway,
                pessoaJuridica.NomeFantasia, hotsiteUrl,
                pessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                pessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista());

            return corpo;
        }

        #endregion E-mail Pagamento da Fatura Não Autorizado

        #region E-mail Pagamento Não Recebido

        public void EnviarEmailPagamentoFaturaNaoRecebido(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailPagamentoFaturaNaoAutorizado;
            var corpo = ObterCorpoEmailPagamentoFaturaNaoRecebido(pessoaJuridica, fatura);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            if (pessoaJuridica.ResponsavelFinanceiro.PrimeiraConta.Email.EmailValido())
                destino.Add(new MailAddress(pessoaJuridica.ResponsavelFinanceiro.PrimeiraConta.Email));

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(),
                        new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente,
                        assunto, corpo);
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailPagamentoDaFaturaNaoAutorizado da fatura #" + fatura.IdFatura);
        }

        private string ObterCorpoEmailPagamentoFaturaNaoRecebido(PessoaJuridica pessoaJuridica, Fatura fatura)
        {
            var caminhoTemplate = "PagamentoFaturaNaoRecebido.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = string.Format(corpo, pessoaJuridica.ResponsavelFinanceiro.NomeCompleto, fatura.IdFatura,
                fatura.InicioPeriodoReferencia.Value.ToBrazilianShortDateString(),
                fatura.FimPeriodoReferencia.Value.ToBrazilianShortDateString(), fatura.ValorCobrado,
                fatura.DataVencimento.Value.ToBrazilianShortDateString(), fatura.MotivoTemporarioTransacaoGateway,
                pessoaJuridica.NomeFantasia, ObterUrlBase() + "/" + pessoaJuridica.Estabelecimento.Hotsite().Url,
                pessoaJuridica.EnderecoProprio.ToTextoFormatado(),
                pessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista());

            return corpo;
        }

        #endregion E-mail Pagamento Não Recebido

        #region E-mail Despesas Do Estabelecimento

        public void EnviarEmailDespesasDoEstabelecimento(Estabelecimento estabelecimento, List<MailAddress> destinatarios,
            List<Lancamento> despesas,
            DateTime data)
        {

            var assunto = "Agenda de despesas - " + estabelecimento.NomeDeExibicaoNoPortal + " - " + Calendario.Hoje().ToString("dd 'de' MMMM 'de' yyyy", System.Globalization.CultureInfo.GetCultureInfo("pt-BR"));
            var remetente = ObterRemetentePadrao();
            var responderPara = new List<MailAddress> { ObterEmailDeAtendimento() };
            //var destino = ObterEmailsESexoResponsaveisEstabelecimento(estabelecimento);
            var bcc = new[] { new MailAddress("<EMAIL>") };
            if (destinatarios.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var emailDestinatario in destinatarios)
                    {
                        var corpo = ObterCorpoEmailDespesasDoEstabelecimento(estabelecimento, despesas,
                            emailDestinatario.DisplayName);
                        EmailManager.SendHtml(new[] { emailDestinatario }, bcc, remetente, assunto, corpo, null, null, false,
                            responderPara.ToArray());
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailDespesasDoEstabelecimento " +
                                               estabelecimento.NomeDeExibicaoNoPortal);
        }



        private string ObterCorpoEmailDespesasDoEstabelecimento(Estabelecimento estabelecimento,
        IList<Lancamento> despesas,
        string nomeDoResponsavel)
        {
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var caminhoTemplate = "EmailControleDespesa.cshtml";
            var corpo = ResourceManager.ReadText(typeof(Textos).Assembly, "Perlink.Trinks.Resources.TemplatesEmail." + caminhoTemplate);
            corpo = corpo.Replace("{BASE_URL}", ObterUrlBase());
            corpo = corpo.Replace("{URL_LOGO_ESTABELECIMENTO}", urlLogoEstabelecimento);

            var model = new
            {
                Despesas = (despesas.Any() ? despesas : null),
                NomeDoResponsavel = nomeDoResponsavel,
                NomeDeExibicaoNoPortal = estabelecimento.NomeDeExibicaoNoPortal,
                TotalDespesas = despesas.Sum(f => f.Valor).ValorDecimal(),
                TelefoneRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_telefones).ObterValor(),
                EmailAtendimentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_email_atendimento).ObterValor(),
                HorarioFuncionamentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_horario_funcionamento).ObterValor().ToLower(),
                PossuiLogoEstabelecimento = urlLogoEstabelecimento != null
            };

            return re.Razor.Parse(corpo, model, caminhoTemplate); // Sempre preencher cacheName
        }

        public void EnviarEmailEstoqueDoEstabelecimento(Estabelecimento estabelecimento, List<MailAddress> destinatarios,
           DateTime data,
           ResultadoPosicaoEstoqueDTO posicaoEstoqueDTO)
        {

            var assunto = "Posição atual de estoque - " + estabelecimento.NomeDeExibicaoNoPortal + " - " + Calendario.Hoje().ToString("dd 'de' MMMM 'de' yyyy", System.Globalization.CultureInfo.GetCultureInfo("pt-BR"));
            var remetente = ObterRemetentePadrao();
            var responderPara = new List<MailAddress> { ObterEmailDeAtendimento() };
            //var destino = ObterEmailsESexoResponsaveisEstabelecimento(estabelecimento);
            var bcc = new[] { new MailAddress("<EMAIL>") };
            if (destinatarios.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var emailDestinatario in destinatarios)
                    {
                        var corpo = ObterCorpoEmailEstoqueDoEstabelecimento(estabelecimento,
                            emailDestinatario.DisplayName, posicaoEstoqueDTO);
                        EmailManager.SendHtml(new[] { emailDestinatario }, bcc, remetente, assunto, corpo, null, null, false,
                            responderPara.ToArray());
                    }
                }
            }
            LogService<EnvioEmailService>.Info("EnviarEmailEstoqueDoEstabelecimento " +
                                               estabelecimento.NomeDeExibicaoNoPortal);
        }

        private string ObterCorpoEmailEstoqueDoEstabelecimento(Estabelecimento estabelecimento,
            string nomeDoResponsavel,
            ResultadoPosicaoEstoqueDTO posicaoEstoqueDTO)
        {
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            var caminhoTemplate = "EmailControleEstoque.cshtml";
            var corpo = ResourceManager.ReadText(typeof(Textos).Assembly, "Perlink.Trinks.Resources.TemplatesEmail." + caminhoTemplate);
            corpo = corpo.Replace("{BASE_URL}", ObterUrlBase());
            corpo = corpo.Replace("{URL_LOGO_ESTABELECIMENTO}", urlLogoEstabelecimento);

            var model = new
            {
                NomeDoResponsavel = nomeDoResponsavel,
                NomeDeExibicaoNoPortal = estabelecimento.NomeDeExibicaoNoPortal,
                TelefoneRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_telefones).ObterValor(),
                EmailAtendimentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_email_atendimento).ObterValor(),
                HorarioFuncionamentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_horario_funcionamento).ObterValor().ToLower(),
                DisposicaoEstoque = InserirPosicaoEstoqueCorpoDoEmail(estabelecimento, posicaoEstoqueDTO),
                PossuiLogoEstabelecimento = urlLogoEstabelecimento != null
            };

            return re.Razor.Parse(corpo, model, caminhoTemplate); // Sempre preencher cacheName
        }


        public string InserirPosicaoEstoqueCorpoDoEmail(Estabelecimento estabelecimento, ResultadoPosicaoEstoqueDTO posicaoEstoqueDTO)
        {
            var model = new
            {
                ProdutosPorFabricante = posicaoEstoqueDTO.Fabricantes.Any() ? posicaoEstoqueDTO.Fabricantes : null,
                TotalProdutosAbaixoDoMinimoQueFaltaCarregar = posicaoEstoqueDTO.TotalProdutosAbaixoDoMinimoQueFaltaCarregar,
                TotalProdutosPertoDoMinimoQueFaltaCarregar = posicaoEstoqueDTO.TotalProdutosPertoDoMinimoQueFaltaCarregar,
                PrecisaCarregarMaisProdutosAbaixoOuPertoDoMinimo = posicaoEstoqueDTO.PrecisaCarregarMaisProdutosAbaixoOuPertoDoMinimo,
                NomeDeExibicaoNoPortal = estabelecimento.NomeDeExibicaoNoPortal
            };

            var caminhoParteTemplate = "_ParteEmailControleEstoque.cshtml";
            var htmlprodutosPorFabricante = ResourceManager.ReadText(typeof(Textos).Assembly, "Perlink.Trinks.Resources.TemplatesEmail." + caminhoParteTemplate);

            return re.Razor.Parse(htmlprodutosPorFabricante, model, caminhoParteTemplate); // Sempre preencher cacheName
        }

        #endregion E-mail Despesas Do Estabelecimento

        #region E-mail Proximos Agendamentos

        public void EnviarEmailProximosAgendamentos(Estabelecimento estabelecimento,
            IEnumerable<Horario> agendamentos,
            DateTime data)
        {
            if (!agendamentos.Any())
                return;
            var agendamentosHoje = agendamentos.Where(f => f.DataInicio.Date == data.Date);

            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailProximosAgendamentos + " - " +
                          estabelecimento.NomeDeExibicaoNoPortal;
            var remetente = ObterRemetentePadrao();

            var responderPara = new List<MailAddress> { ObterEmailDeAtendimento() };

            var destino = ObterEmailsResponsaveisEstabelecimento(estabelecimento);
            var bcc = new[] { new MailAddress("<EMAIL>") };

            var corpo = ObterCorpoEmailProximosAgendamentos(estabelecimento, agendamentosHoje, data);

            var csvHeader =
                "Data;Hora;Profissional;Serviço;Duração;Cliente;Telefones;Valor;Fechamento Conta;Status;Cadastro;Observações Agendamento;Observações Cliente";
            var csvCorpo =
                agendamentos.Select(
                    f =>
                        f.DataInicio.ToShortDateString() + ";" + f.DataInicio.ToShortTimeString() + ";" +
                       ObterApelidoOuNomeDoProfissional(f.Profissional) + ";" + f.ServicoEstabelecimento.Nome + ";" +
                        f.Duracao.PorExtenso() + ";" + f.ClienteEstabelecimento.DadosDoCliente.NomeCompleto + ";" +
                        f.ClienteEstabelecimento.DadosDoCliente.Telefones.ToTextoFormatadoLista() + ";" +
                        f.Valor.ObterValorEmReais() + ";" + (f.FoiPago ? "Fechada" : "Aberta") + ";" + f.Status.Nome +
                        ";" + (f.DataHoraCriacao.ToString()) + ";" +
                        (string.IsNullOrWhiteSpace(f.Observacao)
                            ? ""
                            : f.Observacao.Replace(Environment.NewLine, "").Replace(";", ",")) +
                        (string.IsNullOrWhiteSpace(f.ObservacaoCliente)
                            ? ""
                            : f.ObservacaoCliente.Replace(Environment.NewLine, "").Replace(";", ",")));

            var csv = csvHeader + Environment.NewLine + string.Join(Environment.NewLine, csvCorpo);

            Attachment[] anexo = {
                Attachment.CreateAttachmentFromString(csv, "agendamentos.csv", Encoding.Default, "text/csv")
            };

            if (destino.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destino.ToArray(), bcc, remetente, assunto, corpo, null, anexo, false,
                        responderPara.ToArray());
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailProximosAgendamentos do estabelecimento " +
                                               estabelecimento.NomeDeExibicaoNoPortal);
        }

        private string ObterCorpoEmailProximosAgendamentos(Estabelecimento estabelecimento,
            IEnumerable<Horario> agendamentos,
            DateTime dataHora)
        {
            var caminhoTemplate = "AgendamentosDeAmanha.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            string tabelaAgendamentos = ObterParteTabelaDeAgendamentosFuturos(agendamentos);

            corpo = string.Format(corpo, estabelecimento.PessoaJuridica.ResponsavelFinanceiro.NomeCompleto,
                dataHora.ToShortDateString(), tabelaAgendamentos, estabelecimento.NomeDeExibicaoNoPortal);

            return corpo;
        }

        private string ObterParteTabelaDeAgendamentosFuturos(IEnumerable<Horario> agendamentos)
        {
            var estruturaTabela =
                            "<table border='1' style='font-size: 12px; background:#f2f2f2; border:1px solid #e4e4e4;' cellspacing='0' cellpadding='5'>" +
                            "<tr>" + "<th>" + Textos.Hora + "</th>" + "<th>" + Textos.Profissional + "</th>" +
                            "<th style='width: 27%;'>" + Textos.Servico + "</th>" + "<th style='width: 27%;'>" + Textos.Cliente +
                            "</th>" + "<th>" + Textos.Status + "</th>" + "</tr>{0}" + "</table>";

            var estruturaLinha = "<tr>" + "<td>{0}</td>" + "<td>{1}</td>" + "<td>{2} - {3}</td>" +
                                 "<td>{4}<br />{5}</td>" + "<td>{6}</td>" + "</tr>";
            var estruturaVazio = "<tr>" + "<td colspan='10'>Sem agendamentos para hoje</td>" + "</tr>";

            var linhas =
                agendamentos.Select(
                    f =>
                        string.Format(estruturaLinha, f.DataInicio.ToShortTimeString(),
                           ObterApelidoOuNomeDoProfissional(f.Profissional), f.ServicoEstabelecimento.Nome,
                            f.Duracao.PorExtenso(), f.Cliente.PessoaFisica.NomeCompleto,
                            f.Cliente.PessoaFisica.Telefones.ToTextoFormatadoLista(), f.Status.Nome));

            var tabelaAgendamentos = string.Format(estruturaTabela,
                linhas.Any() ? string.Join(Environment.NewLine, linhas) : estruturaVazio);
            return tabelaAgendamentos;
        }

        #endregion E-mail Proximos Agendamentos

        #region E-mail CEP Sem Bairro

        public void EnviarEmailCEPSemBairro(Estabelecimento estabelecimento)
        {
            var assunto = "CEP sem Bairro";

            var corpo = ObterCorpoCEPSemBairro(estabelecimento);
            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            destino.Add(ObterRemetentePadrao());

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterCorpoCEPSemBairro(Estabelecimento estabelecimento)
        {
            var enderecoProprio = estabelecimento.PessoaJuridica.EnderecoProprio;

            if (enderecoProprio.BairroEntidade != null)
                return null;

            var caminhoTemplate = "CEPSemBairro.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = String.Format(corpo, estabelecimento.PessoaJuridica.NomeFantasia, enderecoProprio.ToTextoFormatado());
            return corpo;
        }

        #endregion E-mail CEP Sem Bairro

        #region Email Notificação LOTE RPS não validado pela Secretaria de Fazenda do Estado

        public void EnviarEMailRPSLote(int idPessoaJuridica, int numeroLote = 0, IDictionary<string, string> arquivosAnexo = null, bool EhErro = false, string mensagem = null, List<DadosParaGeracaoLoteRPSDTO> dadosRpsRejeitados = null)
        {
            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var listaDestinoOculto = new List<MailAddress>();

            var transacoes = Domain.Financeiro.TransacaoRepository.Queryable();
            var emissoes = Domain.RPS.EmissaoRPSRepository.Queryable().Where(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica && e.Lote == numeroLote);
            var pj = Domain.Pessoas.PessoaJuridicaRepository.Load(idPessoaJuridica);
            var respFinanceiro = pj.ResponsavelFinanceiro;
            var nomeCompletoParaEnvioNoCorpoDoEmail = "";

            var assunto = string.Format("Trinks  – Retorno envio de Arquivo de LOTE RPS - {0}", pj.NomeFantasia);

            if (numeroLote > 0)
            {
                var emissao = emissoes.FirstOrDefault();

                if (emissao == null)
                    return;

                var solicitanteLote = emissao.PessoaQueEmitiu;
                nomeCompletoParaEnvioNoCorpoDoEmail = solicitanteLote.NomeCompleto;
                listaDestino.Add(new MailAddress(solicitanteLote.Email, solicitanteLote.NomeCompleto));
            }
            else
            {
                nomeCompletoParaEnvioNoCorpoDoEmail = respFinanceiro.NomeCompleto;
            }

            if (respFinanceiro.Email != null)
            {
                if (listaDestino.All(c => c.Address != respFinanceiro.Email))
                {
                    MailAddress responsavel = new MailAddress(respFinanceiro.Email, respFinanceiro.NomeCompleto);
                    listaDestino.Add(responsavel);
                }
                if (ConfiguracoesTrinks.Geral.HabilitaEnvioDeCopiaDeEmailParaFabrica)
                {
                    listaDestino.Add(ObterEmailTecnico());
                }
            }

            var caminhoTemplate = "RPSLoteOperacao.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            if (EhErro)
            {
                var textoTituloErro = numeroLote > 0 ? "Houve um problema no processamento do lote de RPS " + numeroLote : "Houve um problema no processamento do lote de RPS";
                var textoCorpoErroP1 = "Seu lote de RPS não foi processado por erro de validação na estrutura do arquivo ou por inconsistência de dados.";
                var textoCorpoErroP2 = "O lote foi desfeito e o RPS voltou ao status anterior para que seja enviado novamente.";
                var textoCorpoErroP3 = "Verifique se os dados cadastrados no Trinks.com estão de acordo com os dados cadastrados no site da Prefeitura do seu município e tente novamente.";

                var textoCorpoErroP4 = "";

                var textoCorpoErroP5 = "<h1 style='font-family:Arial,Helvetica,sans-serif; color:#f00; font-size:14px; text-align:center; font-weight:bold'>";
                var textoCorpoErroP6 = "</h1>";

                if (arquivosAnexo != null)
                {
                    try
                    {
                        var dados = arquivosAnexo.Select(f => new DetalhesXML(f.Value));

                        var status = new IntegracaoEnvioRpsUniNfe().StatusRetorno(dados.First());

                        if (status == StatusRetornoXMLEnum.Falha)
                        {
                            textoCorpoErroP4 += textoCorpoErroP5;
                            textoCorpoErroP4 += string.Join("\n==========\n", dados).Replace("\n", "<br />").Replace("\t", "&nbsp;&nbsp;");
                            textoCorpoErroP4 += textoCorpoErroP6;
                        }
                    }
                    catch
                    {
                    }
                }

                if (mensagem != null)
                {
                    textoCorpoErroP4 += textoCorpoErroP5 + mensagem + textoCorpoErroP6;
                }

                corpo = String.Format(corpo, nomeCompletoParaEnvioNoCorpoDoEmail, textoTituloErro, textoCorpoErroP1, textoCorpoErroP2, textoCorpoErroP3, textoCorpoErroP4);
            }
            else
            {
                var textoTituloSucesso = numeroLote > 0 ?
                    "O lote de RPS " + numeroLote + " foi processado com sucesso!" :
                    "O lote de RPS foi processado com sucesso!";
                var textoCorpoSucessoP1 = "Seu lote de RPS foi processado com sucesso no site da Prefeitrua do seu município e a nota fiscal foi gerada com sucesso e já pode ser consultada.";
                var textoCorpoSucessoP2 = "Em anexo segue o XML gerado no processamento para conferência.";
                var textoCorpoSucessoP3 = "Você tembém poderá realizar o download do XML das notas emitidas de forma direta para a Prefeitura pelo Trinks pelo botão “Exportar XML” em NFS-e > Lote de RPS.";

                if (dadosRpsRejeitados?.Any() == true)
                {
                    var textoListaRejeitados = "Os seguintes itens foram rejeitados e precisam ser revistos.<br/>";
                    var tabelaComRpsRejeitados = GerarTabelaRejeitados(dadosRpsRejeitados);

                    mensagem += $"<br/> {textoListaRejeitados} <br/> {tabelaComRpsRejeitados} ";
                }

                corpo = String.Format(corpo, nomeCompletoParaEnvioNoCorpoDoEmail, textoTituloSucesso, textoCorpoSucessoP1, textoCorpoSucessoP2, textoCorpoSucessoP3, mensagem);
            }

            if (!SimulationTool.Current.EhSimulacao)
                if (listaDestino.Any())
                    EmailManager.SendHtml(listaDestino.ToArray(), listaDestinoOculto.ToArray(), remetente, assunto, corpo, null, CriarArquivoAnexo(arquivosAnexo).ToArray());
        }

        private string GerarTabelaRejeitados(List<DadosParaGeracaoLoteRPSDTO> dadosRpsRejeitados)
        {
            if (dadosRpsRejeitados?.Any() != true) return string.Empty;

            var sb = new StringBuilder();

            sb.AppendLine("<table class='tabela-rejeitados' cellspacing='0' cellpadding='5' style='font-size: 12px; background:#f2f2f2; border:1px solid #e4e4e4;width: 100%;'>");
            sb.AppendLine("<tr><th>Data da Movimentação</th><th>Cliente</th><th>Valor do Serviço</th><th>No. RPS</th></tr>");

            foreach (var r in dadosRpsRejeitados)
            {
                var numeroRps = r.DadosRPS?.EmissoesRPS
                    ?.FirstOrDefault(e => e.PessoaJuridica?.IdPessoa == r.PessoaJuridica?.IdPessoa)
                    ?.Numero.ToString() ?? "N/A";

                sb.AppendLine("<tr>")
                  .AppendLine($"<td>{r.DataTransacao.Formatar()}</td>")
                  .AppendLine($"<td>{r.NomeCliente}</td>")
                  .AppendLine($"<td>{r.ValorTotal.ToString("C")}</td>")
                  .AppendLine($"<td>{numeroRps}</td>")
                  .AppendLine("</tr>");
            }

            sb.AppendLine("</table>");
            return sb.ToString();
        }

        private List<Attachment> CriarArquivoAnexo(IDictionary<string, string> arquivoAnexo)
        {
            MemoryStream memoryStream;
            List<Attachment> anexos = new List<Attachment>();

            if (arquivoAnexo != null)
                foreach (KeyValuePair<string, string> kvp in arquivoAnexo)
                {
                    if (kvp.Key == null || kvp.Value == null)
                        continue;

                    memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(kvp.Value.ToString()));
                    ContentType contentType = new ContentType("text/xml");
                    contentType.Name = kvp.Key;

                    anexos.Add(new Attachment(memoryStream, contentType));
                }

            return anexos;
        }

        #endregion Email Notificação LOTE RPS não validado pela Secretaria de Fazenda do Estado

        #region Importação realizada

        public void EnviarEmailImportacaoRealizada(string nomeImportador, int quantImportada, string nomeEstabelecimento, int totalRegistrosAntes, int totalRegistrosDepois, int totalRegistrosTabela, List<KeyValuePair<int, Exception>> listaExcecao, int? totalDeComboClientesDepois = null, int? totalDeComboClientesAntes = null)
        {
            var assunto = nomeImportador + ": Importação realizada - " + nomeEstabelecimento;

            var corpo = "Importação realizada com sucesso<br>" + nomeImportador + ": " + quantImportada + " registros estimados para importação " + nomeEstabelecimento;
            corpo += "<br><br>";
            corpo += "Total de registros na tabela antes da importação: " + totalRegistrosAntes + "<br>";
            corpo += "Total de registros na tabela depois da importação: " + totalRegistrosDepois + "<br>";
            corpo += "Total de registros adicionados pela importação: " + totalRegistrosTabela + "<br><br>";
            if (totalDeComboClientesAntes != null && totalDeComboClientesDepois != null)
            {
                corpo += "Total de itens da Combo Clientes antes da importação: " + totalDeComboClientesAntes + "<br>";
                corpo += "Total de itens da Combo Clientes depois da importação: " + totalDeComboClientesDepois + "<br>";
            }

            if (listaExcecao.Count > 0)
            {
                corpo += "<br><br>";
                corpo += MontaExcecaoEmailImportacao(listaExcecao);
            }

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress> { ObterEmailDeAtendimento(), ObterEmailFabrica() };

            if (!SimulationTool.Current.EhSimulacao)
                EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        public string MontaExcecaoEmailImportacao(List<KeyValuePair<int, Exception>> listaExcecao)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("<table width='500' border='0' bordercolor='#E69431' cellspacing='0' cellpadding='0'>");
            sb.Append("<tr><td align='left'><p style='font-family: Arial, Helvetica, sans-serif; color: #C1272D; text-align: left; font-weight: bold; font-size: 14px; margin: 5px 0;'>Exceções</p>");
            sb.Append("</td></tr>");
            foreach (KeyValuePair<int, Exception> item in listaExcecao)
            {
                sb.Append("<tr>");
                sb.Append("<td align='left'>");
                sb.AppendFormat("<ul><li>{0}", item.Value);
                sb.Append("</li>");
                sb.Append("</ul>");
                sb.Append("</td>");
                sb.Append("</tr>");
            }
            sb.Append("</table>");

            return sb.ToString();
        }

        #endregion Importação realizada

        #region E-mail Falta de crédito de marketing

        /// <summary>
        /// Rotina genérica para informar aos administradores do estabelecimento que não havia crédito no momento
        /// do disparo de SMS ou e-mails de uma campanha programada.
        /// </summary>
        /// <param name="marketingEnvio">Dados do envio da campanha.</param>
        /// <param name="nomeTipo">Texto referente ao tipo de campanha. Será apresentado no corpo dos e-mails enviados.</param>
        public void EnviarEmailMarketingEnvioSemCredito(MarketingEnvio marketingEnvio)
        {
            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(marketingEnvio.Estabelecimento, false).Distinct().ToList();

            // Ver uma forma de refatorar a obtenção do nome do tipo já que a instância recebida aqui é da classe base (e a propriedade "Tipo" não está acessível nela).
            // Pensei em obter o valor do Discriminator do tipo, mas o valor desse discrimator não coincide com
            // os valores que precisam ser apresentados no corpo dos e-mails enviados.
            var nomeTipo = marketingEnvio is MarketingEnvioWhatsApp ? "WhatsApp" : (marketingEnvio is MarketingEnvioSMS ? "SMS" : "e-mails");

            var assunto = string.Format(ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCampanhaMarketingPendenteDeCredito, nomeTipo, marketingEnvio.Estabelecimento.NomeDeExibicaoNoPortal);

            var emailsParaEnviar = ObterCorpoEmailMarketingEnvioSemCredito(marketingEnvio, nomeTipo, responsaveisEstabelecimento);
            var emailAtendimento = ObterEmailDeAtendimento();
            List<MailAddress> destinosOcultos = new List<MailAddress>();
            destinosOcultos.Add(emailAtendimento);

            var remetente = ObterRemetentePadrao();

            if (emailsParaEnviar.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var email in emailsParaEnviar)
                    {
                        EmailManager.SendHtml(
                            toAddresses: new MailAddress[] { email.Key },
                            bccAddresses: destinosOcultos.ToArray(),
                            fromAddress: remetente,
                            subject: assunto,
                            bodyHtml: email.Value
                        );
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarMarketingEnvioSemCredito - Envio Id " + marketingEnvio.IdMarketingEnvio.ToString());
        }

        public void EnviarEmailMarketingEstabelecimentoEmSituacaoIrregular(MarketingCampanha marketingCampanha)
        {
            if (marketingCampanha == null)
                return;

            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(marketingCampanha.Estabelecimento, false).Distinct().ToList();

            //var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCampanhaMarketingSituacaoIrregular;
            var assunto = string.Format(ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCampanhaMarketingSituacaoIrregular, marketingCampanha.Estabelecimento.NomeDeExibicaoNoPortal);

            var nomeTipo = marketingCampanha.GetRealType() == typeof(MarketingCampanhaWhatsApp) ? "WhatsApp" : (marketingCampanha.GetRealType() == typeof(MarketingCampanhaSMS) ? "SMS" : "e-mails");
            var emailsParaEnviar = ObterCorpoEmailMarketingEstabelecimentoEmSituacaoIrregular(marketingCampanha, nomeTipo, responsaveisEstabelecimento);
            var emailAtendimento = ObterEmailDeAtendimento();
            var destinosOcultos = new List<MailAddress> {
                emailAtendimento
            };
            var remetente = ObterRemetentePadrao();

            if (emailsParaEnviar.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var email in emailsParaEnviar)
                    {
                        EmailManager.SendHtml(
                            toAddresses: new MailAddress[] { email.Key },
                            bccAddresses: destinosOcultos.ToArray(),
                            fromAddress: remetente,
                            subject: assunto,
                            bodyHtml: email.Value
                        );
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailMarketingEstabelecimentoEmSituacaoIrregular - Id MarketingCampanha " + marketingCampanha.IdMarketingCampanha.ToString());
        }

        public void EnviarEmailMarketingConviteRetornoSemCredito(MarketingCampanha marketingCampanha, int quantidadeDeEnvios)
        {
            if (marketingCampanha == null)
                return;

            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(marketingCampanha.Estabelecimento, false).Distinct().ToList();
            var nomeTipo = marketingCampanha.GetRealType() == typeof(MarketingCampanhaWhatsApp) ? "WhatsApp" : (marketingCampanha.GetRealType() == typeof(MarketingCampanhaSMS) ? "SMS" : "e-mails");
            var assunto = string.Format("Trinks – Atualize seu plano de {0} no estabelecimento {1} para enviar os Convites de Retorno de hoje!", nomeTipo, marketingCampanha.Estabelecimento.NomeDeExibicaoNoPortal);
            var emailsParaEnviar = ObterCorpoEmailMarketingConviteRetornoSemCredito(marketingCampanha, responsaveisEstabelecimento, quantidadeDeEnvios);
            var emailAtendimento = ObterEmailDeAtendimento();
            var destinosOcultos = new List<MailAddress> {
                emailAtendimento
            };
            var remetente = ObterRemetentePadrao();

            if (emailsParaEnviar.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var email in emailsParaEnviar)
                    {
                        EmailManager.SendHtml(
                            toAddresses: new MailAddress[] { email.Key },
                            bccAddresses: destinosOcultos.ToArray(),
                            fromAddress: remetente,
                            subject: assunto,
                            bodyHtml: email.Value
                        );
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailMarketingConviteRetornoSemCredito - Id MarketingCampanha " + marketingCampanha.IdMarketingCampanha.ToString());
        }

        /// <summary>
        /// Responsável por gerar a lista de e-mails a serem enviados aos administradores de um estabelecimento informando
        /// da falta de crédito para disparo de uma campanha de marketing.
        /// </summary>
        /// <param name="marketingEnvio">Dados do envio da campanha.</param>
        /// <param name="tipoCampanhaParaExibicao">Nome de exibição do tipo da campanha (SMS/E-mails).</param>
        /// <param name="destinatarios">Lista de e-mails dos responsaveis pelo estabelecimento.</param>
        /// <returns>
        /// Lista com mensagens de e-mail específicas para cada responsável do estabelecimento informando da falta de crédito
        /// para disparo de uma campanha de marketing.
        /// </returns>
        private Dictionary<MailAddress, string> ObterCorpoEmailMarketingEnvioSemCredito(MarketingEnvio marketingEnvio, string tipoCampanhaParaExibicao, List<MailAddress> destinatarios)
        {
            var caminhoTemplate = marketingEnvio.MarketingCampanha.NomeCampanha != "Convite de Retorno" ?
                                    "MarketingCampanhaNaoEnviadaPorFaltaDeCredito.cshtml" :
                                    "MarketingConviteRetornoNaoEnviadaPorFaltaDeCredito.cshtml";

            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var retorno = new Dictionary<MailAddress, string>();
            var sufixoCompraDeCreditos = String.Format("{0}/BackOffice/{1}", ObterUrlBase(), ObterSufixoCompraCreditosCampanhasMarketing());
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(marketingEnvio.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            foreach (var responsavel in destinatarios)
            {
                var mensagem = string.Format(corpo,
                    responsavel.DisplayName,                                                // 0 -> Nome
                    tipoCampanhaParaExibicao,                                               // 1 -> Tipo da campanha (e-mail || SMS)
                    marketingEnvio.MarketingCampanha.NomeCampanha,                          // 2 -> Nome da campanha
                    marketingEnvio.DataHoraProgramada.ToBrazilianLongDateTimeString(),      // 3 -> Data/hora programação de envio da campanha
                    sufixoCompraDeCreditos,                                                 // 4 -> Url de compra de créditos
                    urlLogoEstabelecimento                                                  // 5 -> Caminho para a logo do estabelecimento
                );

                retorno.Add(responsavel, mensagem);
            }

            return retorno;
        }

        private Dictionary<MailAddress, string> ObterCorpoEmailMarketingConviteRetornoSemCredito(MarketingCampanha marketingCampanha, List<MailAddress> destinatarios, int quantidadeDeEnvios)
        {
            var retorno = new Dictionary<MailAddress, string>();
            var caminhoTemplate = "AlertaFaltaDeCreditoParaEnvioConvitedeRetorno.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var sufixoCompraDeCreditos = String.Format("{0}/BackOffice/{1}", ObterUrlBase(), ObterSufixoCompraCreditosCampanhasMarketing());
            var htmlDaLogoEstabelecimento = HtmlDaLogoDoEstabelecimento(marketingCampanha.Estabelecimento);
            int valorsaldo;
            string tipoCampanha;
            if (marketingCampanha is MarketingCampanhaEmail)
            {
                valorsaldo = Domain.Marketing.ConfiguracoesEstabelecimentoMarketingRepository.ObterSaldoTotalMarketingEmail(marketingCampanha.Estabelecimento.IdEstabelecimento);
                tipoCampanha = "e-mail";
            }
            else
            {
                valorsaldo = Domain.Marketing.ConfiguracoesEstabelecimentoMarketingRepository.ObterSaldoTotalMarketingSMS(marketingCampanha.Estabelecimento.IdEstabelecimento);
                tipoCampanha = "SMS";
            }

            foreach (var responsavel in destinatarios)
            {
                var mensagem = string.Format(corpo,
                    responsavel.DisplayName,                                                    // 0 -> Nome
                    tipoCampanha,                                                               // 1 -> Tipo da campanha (e-mail || SMS)
                    marketingCampanha.ProximoEnvio.Value.ToBrazilianLongDateTimeString(),       // 2 -> Data/hora programação de envio da campanha
                    quantidadeDeEnvios,                                                         // 3 -> Total de envios
                    valorsaldo,                                                                 // 4 -> Saldo total por tipo de campanha
                    sufixoCompraDeCreditos,                                                     // 5 -> Url de compra de crédito
                    htmlDaLogoEstabelecimento                                                   // 6 -> Url para a logo do estabelecimento
                    );

                retorno.Add(responsavel, mensagem);
            }
            return retorno;
        }

        private string HtmlDaLogoDoEstabelecimento(Estabelecimento estabelecimento)
        {
            var htmlDaLogo = string.Empty;

            var caminhoDaLogo = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim120x120);
            if (caminhoDaLogo != null)
                htmlDaLogo = $"<td align='left'><img src='{caminhoDaLogo}' height='120' width='120' alt='Logo do estabelecimento'></td>";

            return htmlDaLogo;
        }

        private Dictionary<MailAddress, string> ObterCorpoEmailMarketingEstabelecimentoEmSituacaoIrregular(MarketingCampanha marketingCampanha, string tipoCampanhaParaExibicao, List<MailAddress> destinatarios)
        {
            var caminhoTemplate = "MarketingCampanhaNaoEnviadaPorSituacaoFinanceiraIrregular.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var retorno = new Dictionary<MailAddress, string>();
            var sufixoCompraDeCreditos = String.Format("{0}/BackOffice/{1}", ObterUrlBase(), ObterSufixoCompraCreditosCampanhasMarketing());

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(marketingCampanha.Estabelecimento, DimemsoesFotosEnum.Dim243x92);

            foreach (var responsavel in destinatarios)
            {
                var mensagem = string.Format(corpo,
                    responsavel.DisplayName,                                                // 0 -> Nome
                    tipoCampanhaParaExibicao,                                               // 1 -> Tipo da campanha (e-mail || SMS)
                    marketingCampanha.NomeCampanha,                                         // 2 -> Nome da campanha
                    marketingCampanha.ProximoEnvio.Value.ToBrazilianLongDateTimeString(),   // 3 -> Data/hora programação de envio da campanha
                    sufixoCompraDeCreditos,                                                 // 4 -> Url de compra de créditos
                    urlLogoEstabelecimento                                                  // 5 -> Caminho para a logo do estabelecimento
                );

                retorno.Add(responsavel, mensagem);
            }

            return retorno;
        }

        #endregion E-mail Falta de crédito de marketing

        #region Boleto Gerado

        private bool EhPagamentoIUGU()
        {
            return new ParametrosTrinks<bool>(ParametrosTrinksEnum.utilizar_pagamento_boleto_iugu).ObterValor();
        }

        private Attachment CriarAnexoBoletoParaEmail(Fatura fatura)
        {
            if (!EhPagamentoIUGU())
                return null;
            else
            {
                Stream boleto = GerarBoletoDeCobranca(fatura);
                ContentType contentType = new ContentType("text/pdf");
                contentType.Name = "boleto.pdf";

                return new Attachment(boleto, contentType);
            }
        }

        private Stream GerarBoletoDeCobranca(Fatura fatura)
        {
            var faturaIntegracao = Domain.Cobranca.FaturaIntegracaoPagamentoExternoService.ObterFaturaExternaEGerarSeNecessarioSync(fatura);
            var url = faturaIntegracao.UrlPagamentoBoleto;
            var webRequest = WebRequest.Create(@url + ".pdf");
            var response = webRequest.GetResponse();
            var content = response.GetResponseStream();
            var boleto = new StreamReader(content).BaseStream;
            return boleto;
        }

        public void EnviarEmailFaturaGerada(Fatura fatura)
        {
            var pessoaJuridica = fatura.Assinatura.ContaFinanceira.Pessoa.PessoaJuridica;
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailBoletoGerado;
            var estabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoaJuridica.IdPessoa);

            var corpo = ObterCorpoEmailFaturaGerada(fatura);

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();

            var responsaveis =
                estabelecimento.VinculoUsuarios.Where(f => f.PerfilAcesso == AcessoBackoffice.Acesso_total && f.Ativo)
                    .Select(f => f.PessoaFisica).Where(f => f.Email != null);

            foreach (var r in responsaveis)
            {
                try
                {
                    destino.Add(new MailAddress(r.Email, r.NomeCompleto));
                }
                catch (Exception e)
                {
                    ErrorSignal.FromCurrentContext().Raise(e);
                }
            }

            try
            {
                List<Attachment> anexos = new List<Attachment>();
                if (EhPagamentoIUGU())
                {
                    // Retirou o Envio de Boleto por anexo. Devido a Tarefa abaixo:
                    // https://perlink.atlassian.net/secure/RapidBoard.jspa?rapidView=15&projectKey=TRINKSTREM&modal=detail&selectedIssue=TRINKSTREM-347
                    //anexos.Add(CriarAnexoBoletoParaEmail(fatura));
                }
                else
                {
                    LogService<EnvioEmailService>.Info(mensagemDeErroIntegracaoIUGU);
                    throw new InvalidOperationException(mensagemDeErroIntegracaoIUGU);
                }
                //caso seja falso?
                if (destino.Any())
                {
                    if (!SimulationTool.Current.EhSimulacao)
                    {
                        EmailManager.SendHtml(destino.ToArray(),
                            new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente,
                            assunto, corpo, null, anexos.ToArray());
                    }
                }

                LogService<EnvioEmailService>.Info("EnviarEmailBoletoGerado da fatura #" + fatura.IdFatura);
            }
            catch (Exception e)
            {
                LogService<EnvioEmailService>.Info("Erro ao enviar email com boleto para a conta financeira " + fatura.Assinatura.ContaFinanceira.IdContaFinanceira
                                                + " da Pessoa" + fatura.Assinatura.ContaFinanceira.Pessoa.IdPessoa);
            }
        }

        private string ObterCorpoEmailFaturaGerada(Fatura fatura)
        {
            var caminhoTemplate = "FaturaGerada.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var inicioPeriodoReferencia = fatura.InicioPeriodoReferencia;
            var fimPeriodoReferencia = fatura.FimPeriodoReferencia;
            var dataDeVencimento = fatura.DataVencimento;

            var valor = fatura.ValorCobrado;

            var pessoaJuridica = fatura.Assinatura.ContaFinanceira.Pessoa.PessoaJuridica;
            var responsavel = pessoaJuridica.ResponsavelFinanceiro;
            var linkRedirecionamentoPagamentoBoleto = string.Empty;

            if (fatura.FormaPagamento == FormaDePagamentoEnum.Boleto)
                if (EhPagamentoIUGU())
                {
                    linkRedirecionamentoPagamentoBoleto = "/BackOffice/Boleto/GerarBoletoIUGU?idFatura=" + fatura.IdFatura;
                }
                else
                {
                    linkRedirecionamentoPagamentoBoleto = "/BackOffice/Boleto/GerarBoleto?idFatura=" + fatura.IdFatura;
                }
            else if (fatura.FormaPagamento == FormaDePagamentoEnum.Pix)
                linkRedirecionamentoPagamentoBoleto = "/BackOffice/MeuPlano?tipoFormaPagamento=pix&idFatura=" + fatura.IdFatura;

            var linkCentralDeAjudaBoleto = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_central_ajuda_boleto).ObterValor();

            var estabelecimento = pessoaJuridica.Estabelecimento;
            corpo = string.Format(corpo,
                                  responsavel.NomeCompleto, //0
                                  estabelecimento.Hotsite().Url,  //1
                                  fatura.IdFatura, //2
                                  inicioPeriodoReferencia, //3
                                  fimPeriodoReferencia, //4
                                  valor, //5
                                  dataDeVencimento, //6
                                  pessoaJuridica.NomeFantasia, //7
                                  pessoaJuridica.EnderecoProprio.ToTextoFormatado(), //8
                                  pessoaJuridica.Telefones.ToTextoFormatadoLista(), //9
                                  linkRedirecionamentoPagamentoBoleto, //10
                                  linkCentralDeAjudaBoleto); //11

            return corpo;
        }

        #endregion Boleto Gerado

        #region E-mail Whatsapp

        public void EnviarEmailStatusSaldoWhatsapp(int idEstabelecimento, TipoSaldo tipo, bool zerado)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(estabelecimento, false).Distinct().ToList();
            var emailAtendimento = ObterEmailDeAtendimento();
            List<MailAddress> destinosOcultos = new List<MailAddress>() { emailAtendimento };

            var tipoSaldo = tipo == TipoSaldo.Automacoes ? "Rotina de Mensagens" : "Convite de Retorno";
            var alertaTipo = !zerado ? "Aviso:" : "Atenção:";
            var status = !zerado ? "está acabando" : "acabou";
            var assunto = $"{alertaTipo} Seu saldo da {tipoSaldo} {status}";
            var paginaCompra = tipo == TipoSaldo.Automacoes ? "CompraCredito" : "CompraCreditoConvite";
            var remetente = ObterRemetentePadrao();

            if (responsaveisEstabelecimento.Any() && !SimulationTool.Current.EhSimulacao)
            {
                foreach (var responsavel in responsaveisEstabelecimento)
                {
                    var corpo = ObterCorpoEmailAvisoSaldoWhatsapp(
                        estabelecimento.NomeDeExibicaoNoPortal,
                        responsavel.DisplayName,
                        tipoSaldo,
                        status,
                        paginaCompra
                    );

                    var destino = new List<MailAddress> {
                        new MailAddress(responsavel.Address)
                    };

                    EmailManager.SendHtml(
                        toAddresses: destino.ToArray(),
                        bccAddresses: destinosOcultos.ToArray(),
                        fromAddress: remetente,
                        subject: assunto,
                        bodyHtml: corpo
                    );
                }
            }
        }

        private string ObterCorpoEmailAvisoSaldoWhatsapp(string nomeEstabelecimento, string nomeResponsavel, string tipoSaldo, string status, string paginaCompra)
        {
            var nomeArquivo = "AvisoSaldo.cshtml";
            var urlBase = ObterUrlBase();
            var linkCompra = $"{urlBase}/BackOffice/Whatsapp/{paginaCompra}";
            var corpo = ResourceManager.ReadText(typeof(Textos).Assembly, "Perlink.Trinks.Resources.TemplatesEmail.Whatsapp." + nomeArquivo);

            corpo = corpo.Replace("{BASE_URL}", urlBase)
                .Replace("{tipoSaldo}", tipoSaldo)
                .Replace("{status}", status)
                .Replace("{estabelecimento}", nomeEstabelecimento)
                .Replace("{responsavel}", nomeResponsavel)
                .Replace("{linkCompra}", linkCompra);

            return corpo;
        }

        #endregion

        public void EnviarMarketingGanhouSMSPorAparecerNaBuscaDoPortal(int idEstabelecimento, int qtdBrindeSMS)
        {
            //TODO: VERIFICAR ASSUNTO CORRETO
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var assunto = estabelecimento.NomeDeExibicaoNoPortal + " no Trinks.com";

            string corpo = ObterCorpoEmailMarketingGanhouSMSPorAparecerNaBuscaDoPortal(estabelecimento, qtdBrindeSMS);

            MailAddress remetente = ObterRemetentePadrao();
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            var emailResponsavelEstabelecimento = estabelecimento.ObterResponsavel().Email;

            destino.Add(new MailAddress(emailResponsavelEstabelecimento));

            if (destino.Any())
            {
                destinosOcultos.Add(ObterEmailDeAtendimento());
                EmailManager.SendHtml(destino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        private string ObterCorpoEmailMarketingGanhouSMSPorAparecerNaBuscaDoPortal(Estabelecimento estabelecimento, int qtdBrindeSMS)
        {
            var responsavelEstabelecimento = estabelecimento.ObterResponsavel();
            var nomeResponsavelEstabelecimento = responsavelEstabelecimento.NomeCompleto;

            bool exibirPrecoServico = estabelecimento.Hotsite().PermiteExibicaoPrecoHotsite;
            bool algunsProfissionaisSemFoto = estabelecimento.ProfissionaisComServicosAtivos().Where(p => p.FotoPrincipal.Codigo == null).Any();

            var caminhoTemplate = "EmailBrindeSMSEmailMarketing.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            var telefonesEstabelecimento = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();
            var nomeFantasiaEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);
            var configuracaoHotsiteEstabelecimento =
                DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url)
                ? String.Empty
                : "/" + configuracaoHotsiteEstabelecimento.Url;
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);
            var formaDeTratamentoDependendoDoSexo = estabelecimento.ObterResponsavel().Genero.PronomeTratamento().ToLower();

            var parteTextoSugestoesTrinks = String.Empty;
            parteTextoSugestoesTrinks = TextoDeSugestoesTrinks(exibirPrecoServico, algunsProfissionaisSemFoto, parteTextoSugestoesTrinks);

            corpo = String.Format(corpo,
                 nomeResponsavelEstabelecimento,
                 nomeFantasiaEstabelecimento,
                 qtdBrindeSMS,
                 "",
                 "",
                 "",
                 urlHotsiteEstabelecimento,
                 enderecoEstabelecimento,
                 telefonesEstabelecimento,
                 "",
                 "",
                 "",
                 "",
                 "",
                 urlLogoEstabelecimento,
                 formaDeTratamentoDependendoDoSexo,
                 parteTextoSugestoesTrinks);

            return corpo;
        }

        private string TextoDeSugestoesTrinks(bool exibirPrecoServico, bool algunsProfissionaisSemFoto, string parteTextoSugestoesTrinks)
        {
            if (!exibirPrecoServico && algunsProfissionaisSemFoto)
            {
                parteTextoSugestoesTrinks = "Sugerimos que sejam incluídas as fotos de seus profissionais e também o valor de alguns dos serviços, pois isso dá maior credibilidade ao seu site e agrada bastante aos clientes.";
            }
            else if (exibirPrecoServico && algunsProfissionaisSemFoto)
            {
                parteTextoSugestoesTrinks = "Sugerimos que sejam incluídas as fotos de seus profissionais, pois isso dá maior credibilidade ao seu site e agrada bastante aos clientes.";
            }
            else if (!exibirPrecoServico && !algunsProfissionaisSemFoto)
            {
                parteTextoSugestoesTrinks = "Sugerimos que seja incluído o valor de alguns dos serviços, pois isso dá maior credibilidade ao seu site e agrada bastante aos clientes.";
            }
            return parteTextoSugestoesTrinks;
        }

        public void EnviarEmailMarketing(MarketingEnvioClienteEmail marketingEnvioClienteEmail)
        {
            var envio = marketingEnvioClienteEmail.MarketingEnvio;
            var marketingCampanha = (MarketingCampanhaEmail)envio.MarketingCampanha;

            string assunto = String.Empty;
            if (marketingCampanha != null)
            {
                string nomeCliente = marketingEnvioClienteEmail.ClienteEstabelecimento.Cliente.PessoaFisica.NomeReduzido();
                assunto = Regex.Replace(marketingCampanha.AssuntoCampanha, @"\[\s*cliente\s*\]", nomeCliente, RegexOptions.IgnoreCase);
            }
            else
                assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailMarketingAniversario;

            string corpo = ObterCorpoEmailMarketing(marketingEnvioClienteEmail);

            MailAddress remetente = ObterRemetenteNaoResponda(envio.Estabelecimento);
            List<MailAddress> destino = new List<MailAddress>();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            var emailCliente = marketingEnvioClienteEmail.EmailClienteEstabelecimento;

            if (emailCliente.EmailValido())
                destino.Add(new MailAddress(emailCliente));
            else
                LogService<Perlink.Trinks.Marketing.Services.MarketingCampanhaService>.Info("============ E-mail inválido para o cliente: " + marketingEnvioClienteEmail.ClienteEstabelecimento.Cliente.PessoaFisica.NomeReduzido() + " - " + emailCliente + " " + Calendario.Agora() + " ================");

            //destinosOcultos.Add(ObterEmailTecnico());

            if (destino.Any())
            {
                var disparador = ObterDisparadorEmailAwsSeHabilitado();
                disparador.SendHtml(destino.ToArray(), remetente, assunto, corpo, destinosOcultos.ToArray());
            }
        }

        private static IDisparadorEmail ObterDisparadorEmailAwsSeHabilitado()
        {
            IDisparadorEmail disparador;
            if (ConfiguracoesTrinks.Geral.HabilitaEnvioEmailAWS)
                disparador = new DisparadorEmailAWS();
            else
                disparador = new DisparadorEmailSMTP();
            return disparador;
        }

        public string ObterCorpoEmailMarketing(MarketingEnvioClienteEmail marketingEnvioClienteEmail)
        {
            var clienteEstabelecimento = marketingEnvioClienteEmail.ClienteEstabelecimento;
            var nomeClienteEstabelecimento = clienteEstabelecimento.Cliente.PessoaFisica.NomeReduzido();

            var conteudo = String.Empty;
            if (marketingEnvioClienteEmail.MarketingEnvio.MarketingCampanha != null)
            {
                conteudo = marketingEnvioClienteEmail.MarketingEnvio.MarketingCampanha.ConteudoCampanha.Conteudo;
            }
            else
            {
                var configuracoesMarketingEstabelecimento = Domain.Marketing.ConfiguracoesEstabelecimentoMarketingRepository.ObterPorEstabelecimento(clienteEstabelecimento.Estabelecimento.IdEstabelecimento);
                conteudo = configuracoesMarketingEstabelecimento.ConteudoEmailAniversariantes;
            }

            var estabelecimento = clienteEstabelecimento.Estabelecimento;

            return ObterCorpoEmailMarketing(estabelecimento, nomeClienteEstabelecimento, conteudo, clienteEstabelecimento.Codigo);
        }

        public string ObterCorpoEmailMarketing(Estabelecimento estabelecimento, string nomeCliente,
            string conteudo, int? idClienteEstabelecimento)
        {
            //{7}
            var enderecoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();
            //{8}
            var telefonesEstabelecimento = estabelecimento.PessoaJuridica.TelefonesProprios().Ativos().ToTextoFormatadoLista();
            //{1}
            var nomeFantasiaEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia;
            //{9} {10} {11}
            var informacoesDoAplicativo = new EnvioEmailInformacaoDoAplicativo();
            ObterTextoEUrlDoAplicativo(estabelecimento, informacoesDoAplicativo); //<texto e url do aplicativo>

            var configuracaoHotsiteEstabelecimento =
                DL.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var urlHotSiteExistente = String.IsNullOrEmpty(configuracaoHotsiteEstabelecimento.Url)
                ? String.Empty
                : "/" + configuracaoHotsiteEstabelecimento.Url;
            //{6}
            var urlHotsiteEstabelecimento = String.Format(ObterUrlBase() + "{0}", urlHotSiteExistente);

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92, false);

            string logoEstabelecimento = !string.IsNullOrWhiteSpace(urlLogoEstabelecimento)
                ? "<img src='" + urlLogoEstabelecimento + "' height='92' alt='Logo do estabelecimento' />"
                : String.Empty;

            var idCliente = idClienteEstabelecimento ?? 0;
            //{15}
            var linkUnsubscribe = "";
            if (idCliente != 0)
                linkUnsubscribe = "<a href = " + String.Format(ObterUrlBase() + "/Portal/Cliente/Unsubscribe?cdgCli=" + idCliente) + " ><i> Clique aqui </i></a> se não quiser mais receber e - mails de promoção de " + nomeFantasiaEstabelecimento + ".";

            string caminhoTemplate = "EmailMarketing.cshtml";

            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);

            corpo = String.Format(corpo,
                nomeCliente,// 0
                nomeFantasiaEstabelecimento,// 1
                conteudo,// 2
                "",// 3
                "",// 4
                "",// 5
                urlHotsiteEstabelecimento,// 6
                enderecoEstabelecimento,// 7
                telefonesEstabelecimento,// 8
                informacoesDoAplicativo.TextoAplicativo,// 9
                informacoesDoAplicativo.UrlAppStore,// 10
                informacoesDoAplicativo.UrlPlayStore,// 11
                "",// 12
                "",// 13
                logoEstabelecimento,// 14
                linkUnsubscribe// 15
                );

            corpo = Regex.Replace(corpo, @"\[\s*cliente\s*\]", nomeCliente, RegexOptions.IgnoreCase);

            return corpo;
        }

        public void EnviarEmailCartaoProximoAExpirar(Assinatura assinatura)
        {
            PessoaFisica resp;

            var pessoa = assinatura.ContaFinanceira.Pessoa;
            var estabelecimento = DL.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoa.IdPessoa);
            if (estabelecimento == null || (resp = estabelecimento.ObterResponsavel()) == null)
                return;

            var destino = new List<MailAddress>();
            var emailResponsavel = new MailAddress(resp.Email, resp.NomeCompleto);
            destino.Add(emailResponsavel);

            var remetente = ObterRemetentePadrao();

            var corpo = ObterCorpoEmailCartaoProximoAExpirar(pessoa.PessoaJuridica);
            var assunto = ConfiguracoesTrinks.EnvioEmail.AssuntoEmailCartaoProximoAExpirar;

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(destino.ToArray(),
                    new[] { new MailAddress(ConfiguracoesTrinks.EnvioEmail.EnderecoEmailFinanceiro) }, remetente,
                    assunto, corpo);
            }
        }

        private string ObterCorpoEmailCartaoProximoAExpirar(PessoaJuridica pessoaJuridica)
        {
            var caminhoTemplate = "EmailAvisoCartaoProximoAExpirar.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var nomeResponsavel = pessoaJuridica.ResponsavelFinanceiro.NomeCompleto;
            var nomeFantasiaDoEstabelecimento = pessoaJuridica.NomeFantasia;
            var endereco = pessoaJuridica.EnderecoProprio.ObterTextoEndereco();
            var telefones = pessoaJuridica.Telefones;

            corpo = string.Format(corpo, nomeResponsavel, nomeFantasiaDoEstabelecimento, endereco, telefones);

            return corpo;
        }

        public void EnviarEmailDeUsuarioInformouPagamentoDeBoleto(Fatura fatura, Stream comprovante, string nomeUsuarioQueSolicitou, DateTime dataSolicitacao, string extencao)
        {
            string assunto = "Informe de Pagamento De Boleto Pela Segunda Vez - " + fatura.Estabelecimento.PessoaJuridica.NomeFantasia;

            var caminhoTemplate = "InformePagamentoBoletoSegundaVezParaFinanceiro.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = String.Format(corpo, fatura.Estabelecimento.PessoaJuridica.NomeFantasia, fatura.IdFatura, dataSolicitacao.ToString(), nomeUsuarioQueSolicitou, fatura.DataDeTolerancia.Value.ToShortDateString());

            var remetente = ObterRemetentePadrao();

            var listaDestino = new List<MailAddress>();
            var destino = ObterEmailFinanceiro();
            listaDestino.Add(destino);

            List<Attachment> anexos = new List<Attachment>();
            anexos.Add(new Attachment(comprovante, "comprovante" + "." + extencao.Replace(".", "")));

            List<MailAddress> destinosOcultos = new List<MailAddress>();

            EmailManager.SendHtml(listaDestino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null, anexos.ToArray());
        }

        public void EnviarEmailDeRenovacaoDoPlanoProxima(IList<Assinatura> assinaturasParaAvisar)
        {
            //throw new NotImplementedException();
        }

        public void EnviarRelatorioGeradoPeloExtrator(MemoryStream arquivoCsv, List<string> destinatarios, string assunto, string corpo)
        {
            if (destinatarios == null || destinatarios.Count <= 0)
                return;

            // ======== Dados do e-mail ========
            MailAddress remetente = ObterRemetentePadrao();
            List<MailAddress> listaDestino = destinatarios.Select(destinatario => new MailAddress(destinatario)).ToList();
            List<MailAddress> destinosOcultos = new List<MailAddress>();

            // ======== Anexos ========

            ContentType contentType = new ContentType("text/csv");
            contentType.Name = "consolidado.csv";
            List<Attachment> anexos = new List<Attachment>();
            anexos.Add(new Attachment(arquivoCsv, contentType));

            // ======== Envio ========
            EmailManager.SendHtml(listaDestino.ToArray(), destinosOcultos.ToArray(), remetente, assunto, corpo, null, anexos.ToArray());
        }

        #region EnviarEmailParaOEstabelecimentoDoResumoDoFechamentoEAgendamentos

        public void EnviarEmailParaOEstabelecimentoDoResumoDoFechamentoEAgendamentos(EstabelecimentoComAgendamentosEFechamentosDiaAnteriorDTO dadosDoEmail, IEnumerable<Horario> horarios, DateTime dataDoEnvio)
        {
            var destino = dadosDoEmail.EmailsDosResponsaveis.Select(e => e.MailAddress()).ToList();
            if (destino.Any())
            {
                var textoEmails = string.Join(", ", dadosDoEmail.EmailsDosResponsaveis.Select(e => e.Email).ToArray());
                DateTime dataResumoFechamentos = dataDoEnvio.AddDays(-1);
                var NomeDoEstabelecimento = dadosDoEmail.EstabelecimentoDTO.NomeExibicaoPortal;
                var assunto = string.Format("Resumo do dia {0} e Agendamentos futuros - {1}", dataResumoFechamentos.ToString("dd/MM"), NomeDoEstabelecimento);

                var remetente = ObterRemetentePadrao();

                var horariosDeHoje = horarios.Where(h => h.DataInicio >= dataDoEnvio.Date && h.DataInicio < dataDoEnvio.Date.AddDays(1));

                var corpo = ObterCorpoDoEmailResumoDoFechamentoEhAgendamentos(dadosDoEmail, dataDoEnvio, horariosDeHoje);

                var bcc = new[] { new MailAddress("<EMAIL>") };
                var responderPara = new List<MailAddress> { ObterEmailDeAtendimento() };

                string csv = ObterCorpoCsvParaAnexarEmailDeResumoDoDiaDoEstabelecimento(horarios);

                Attachment[] anexos = {
                    Attachment.CreateAttachmentFromString(csv, "agendamentos.csv", Encoding.Default, "text/csv")
                };

                LogService<EnvioEmailService>.Info("EnviarEmailProximosAgendamentos do estabelecimento " + NomeDoEstabelecimento + " para os emails:" + textoEmails);
                if (!SimulationTool.Current.EhSimulacao)
                    EmailManager.SendHtml(destino.ToArray(), bcc, remetente, assunto, corpo, null, anexos, false, responderPara.ToArray());

                LogService<EnvioEmailService>.Info("EnviarEmailProximosAgendamentos do estabelecimento " + NomeDoEstabelecimento);
            }
        }

        private string ObterCorpoPdfParaAnexarNoEmailDePagamentoDeBoleto(IEnumerable<Horario> horarios)
        {
            var csvHeader = "Data;Hora;Profissional;Serviço;Duração;Cliente;Telefones;Valor;Fechamento Conta;Status;Cadastro;Observações Agendamento;Observações Cliente";

            var csvCorpo =
                horarios.Select(
                    f =>
                        f.DataInicio.ToShortDateString() + ";" + f.DataInicio.ToShortTimeString() + ";" +
                        (f.Profissional != null ? ObterApelidoOuNomeDoProfissional(f.Profissional.PessoaFisica.NomeCompleto, f.Profissional.PessoaFisica.Apelido) : "Fila de espera")
                       + ";" + f.ServicoEstabelecimento.Nome + ";" +
                        f.Duracao.PorExtenso() + ";" + f.Cliente.PessoaFisica.NomeCompleto + ";" +
                        f.Cliente.PessoaFisica.Telefones.ToTextoFormatadoLista() + ";" +
                        f.Valor.ObterValorEmReais() + ";" + (f.FoiPago ? "Fechada" : "Aberta") + ";" + f.Status.Nome +
                        ";" + (f.DataHoraCriacao.ToString()) + ";" +
                        (string.IsNullOrWhiteSpace(f.Observacao)
                            ? ""
                            : f.Observacao.Replace(Environment.NewLine, "").Replace(";", ",")) +
                        (string.IsNullOrWhiteSpace(f.ObservacaoCliente)
                            ? ""
                            : f.ObservacaoCliente.Replace(Environment.NewLine, "").Replace(";", ",")));

            var csv = csvHeader + Environment.NewLine + string.Join(Environment.NewLine, csvCorpo);
            return csv;
        }

        private string ObterCorpoCsvParaAnexarEmailDeResumoDoDiaDoEstabelecimento(IEnumerable<Horario> horarios)
        {
            var csvHeader = "Data;Hora;Profissional;Serviço;Duração;Cliente;Telefones;Valor;Fechamento Conta;Status;Cadastro;Observações Agendamento;Observações Cliente";

            var csvCorpo =
                horarios.Select(
                    f =>
                        f.DataInicio.ToShortDateString() + ";" + f.DataInicio.ToShortTimeString() + ";" +
                        (f.Profissional != null ? ObterApelidoOuNomeDoProfissional(f.Profissional.PessoaFisica.NomeCompleto, f.Profissional.PessoaFisica.Apelido) : "Fila de espera")
                       + ";" + f.ServicoEstabelecimento.Nome + ";" +
                        f.Duracao.PorExtenso() + ";" + f.Cliente.PessoaFisica.NomeCompleto + ";" +
                        f.Cliente.PessoaFisica.Telefones.ToTextoFormatadoLista() + ";" +
                        f.Valor.ObterValorEmReais() + ";" + (f.FoiPago ? "Fechada" : "Aberta") + ";" + f.Status.Nome +
                        ";" + (f.DataHoraCriacao.ToString()) + ";" +
                        (string.IsNullOrWhiteSpace(f.Observacao)
                            ? ""
                            : f.Observacao.Replace(Environment.NewLine, "").Replace(";", ",")) +
                        (string.IsNullOrWhiteSpace(f.ObservacaoCliente)
                            ? ""
                            : f.ObservacaoCliente.Replace(Environment.NewLine, "").Replace(";", ",")));

            var csv = csvHeader + Environment.NewLine + string.Join(Environment.NewLine, csvCorpo);
            return csv;
        }

        private static string ObterApelidoOuNomeDoProfissional(string nomeCompleto, string apelido)
        {
            return string.IsNullOrWhiteSpace(apelido) ? nomeCompleto : apelido;
        }

        #region Obtendo corpo do email de ResumoDoFechamentoEAgendamentos

        private string ObterCorpoDoEmailResumoDoFechamentoEhAgendamentos(EstabelecimentoComAgendamentosEFechamentosDiaAnteriorDTO dadosDoEmail, DateTime dataDoEnvio, IEnumerable<Horario> horarios)
        {
            //Tabela Resumo do Fechamento
            var corpoResumoFechamento = ObterTextoCompletoDoArquivo("ResumoFechamento.html");
            var tabelaResumoFechamento = FormatarCorpoResumoFechamento(dadosDoEmail.ResumoFechamentoDTO, corpoResumoFechamento);

            ////Tabela Resumo do Fechamento do Caixa ResumoDoFechamentoDeCaixaDTO
            var corpoResumoFechamentoDeCaixa = ObterTextoCompletoDoArquivo("ParteResumoFinanceiroDoCaixa.cshtml");
            var tabelaResumoFechamentoDeCaixa = FormatarCorpoResumoCaixa(dadosDoEmail.ResumoCaixa, corpoResumoFechamentoDeCaixa);

            //Tabela Totais do Resumo dos Agendamentos

            //Pôr um IF CASO não seja um estabelecimento com caixa e rever a lógica
            var corpoTotaisDosAgendamentos = ObterTextoCompletoDoArquivo("ResumoDosTotaisDosAgendamentos.html");
            var tabelaResumoDosAgendamentos = FormatarCorpoTotaisDosAgendamentos(dadosDoEmail.ResumoDosAgendamentosDTO, corpoTotaisDosAgendamentos);

            //Tabela Dos Agendamentos do Estabelecimento
            //var tabelaAgendamentos = FormatarCorpoAgendamentosDoEstabelecimento(dadosDoEmail.AgendamentosDoEstabelecimentoDTO, telefonesDosClientesDoEstabelecimento);

            var parteAgendamentosAmanha = ObterParteTabelaDeAgendamentosFuturos(horarios);

            var caminhoTemplate = "ResumoFechamentoAgendamentosFuturos.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = string.Format(corpo,
                    dadosDoEmail.NomeDoEstabelecimento, //{0} Nome do Estabelecimento
                    dadosDoEmail.DataDoFechamento,      //{1} Dia anterior ao Envio
                    tabelaResumoFechamento,             //{2} Tabela Resumo Fechamento
                    tabelaResumoFechamentoDeCaixa,      //{3} Tabela do fechamento de caixa do dia anterior
                    tabelaResumoDosAgendamentos,        //{4} Tabela Resumo dos Totais Resumo Fechamento
                    dadosDoEmail.DataDosAgendamentos,   //{5} Data dos agendamentos
                    parteAgendamentosAmanha             //{6} Tabela Agendamentos

            );

            return corpo;
        }

        private static string FormatarDetalheDescontoResumoFechamento(List<DetalheDosDescontosDTO> resumo)
        {
            string detalheDesconto = String.Empty;
            if (resumo.Count() > 0)
            {
                foreach (var desconto in resumo)
                {
                    detalheDesconto +=
                    "<tr style=\"font-size: 0.9em; font-weight: normal;\">" +
                        "<td style=\"padding-left: 20px;\">" + desconto.Descricao + "</td>" +
                        "<td style=\"text-align:center;\">" + desconto.Quantidade + "</td>" +
                        "<td style=\"text-align:right;\">" + desconto.Valor.ObterValorNegativoEmReais() + "</td>" +
                    "</tr>";
                }
            }
            return detalheDesconto;
        }

        private static string FormatarCorpoResumoCaixa(ResumoDoCaixaDTO resumoCaixa, string corpoResumoFechamento)
        {
            if (resumoCaixa == null)
                return string.Empty;

            var model = new
            {
                AberturaDeCaixa = resumoCaixa.AberturaDeCaixa.ObterValorEmReais(),
                RecebimentoEmDinheiro = resumoCaixa.RecebimentoEmDinheiro.ObterValorEmReais(),
                Troco = resumoCaixa.Troco.ObterValorEmReais(),
                ValorTotalDeDespesas = resumoCaixa.ValorTotalDeDespesas.ObterValorEmReais(),
                PossuiDespesaPorCategoria = resumoCaixa.PossuiDespesaPorCategoria,
                DespesasCaixa = resumoCaixa.Despesas,
                TotalEmDinheiro = resumoCaixa.TotalEmDinheiro.ObterValorEmReais(),
                Sangria = resumoCaixa.Sangria.ObterValorEmReais(),
                SaldoDoCaixaEmDinheiro = resumoCaixa.SaldoDoCaixaEmDinheiro.ObterValorEmReais()
            };

            return re.Razor.Parse(corpoResumoFechamento, model, "ResumoCaixa"); // Sempre preencher cacheName
        }

        private static string FormatarCorpoResumoFechamento(ResumoFechamentoDTO resumo, string corpoResumoFechamento)
        {
            var resumoFechamentos = String.Empty;
            if (resumo != null)
            {
                string trCreditosPgOnlineComprados = string.Empty;
                string trCreditosPgOnlineUtilizados = string.Empty;

                if (resumo.EstabelecimentoJaTrabalhouComPagamentoOnline)
                {
                    string trModelo = @"<tr>
                                            <td>{0}</td>
                                            <td align='center'>{1}</td>
                                            <td align='right'>{2}</td>
                                        </tr>";

                    trCreditosPgOnlineComprados = string.Format(trModelo, "Pagamento Online", resumo.QtdCreditoDePagamentoOnlineComprados, resumo.ValorCreditoDePagamentoOnlineComprados.ObterValorEmReais());
                    trCreditosPgOnlineUtilizados = string.Format(trModelo, "Crédito de Pagamento Online", resumo.QtdCreditoDePagamentoOnlineUtilizados, resumo.ValorCreditoDePagamentoOnlineUtilizados.ObterValorEmReais());
                }

                resumoFechamentos = string.Format(corpoResumoFechamento,
                    resumo.QuantidadeServicos,                                               //{0} QNTD  Serviço
                    resumo.ValorServicos.ObterValorEmReais(),                                      //{1} Valor Serviço
                    resumo.QuantidadeProdutos,                                               //{2} QNTD  Produto
                    resumo.ValorProdutos.ObterValorEmReais(),                                      //{3} Valor Produto
                    resumo.QuantidadePacotes,                                                //{4} QNTD  Pacotes
                    resumo.ValorPacotes.ObterValorEmReais(),                                       //{5} Valor Pacotes
                    resumo.QuantidadeValePresente,                                           //{6} QNTD  Vales - Presente
                    resumo.ValorValesPresente.ObterValorEmReais(),                                 //{7} Valor Vales - Presente
                    resumo.QuantidadeCreditoClienteComprados,                                //{8} QNTD  Crédito de Cliente
                    resumo.ValorCreditoClienteComprados.ObterValorEmReais(),                       //{9} Valor Crédito de Cliente
                    resumo.QuantidadeDescontos(),                                            //{10} QNTD  Descontos
                    resumo.ValorDescontos.ObterValorNegativoEmReais(),                                     //{11} Valor Descontos
                    FormatarDetalheDescontoResumoFechamento(resumo.DetalhesDosDescontosDTO), //{12} detalhesDescontosResumoFechamento
                    resumo.QuantidadeValePresenteUtilizados,                                 //{13} QNTD Vales - Presente Utilizados
                    resumo.ValorValesPresenteUtilizados.ObterValorEmReais(),                       //{14} Valor Vales - Presente Utilizados
                    resumo.QuantidadeCreditoClienteUtilizados,                               //{15} QNTD Crédito de Cliente Utilizados
                    resumo.ValorCreditoClienteUtilizados.ObterValorEmReais(),                      //{16} Valor Crédito de Cliente Utilizados
                    resumo.ValorTroco.ObterValorEmReais(),                                         //{17} Valor Troco
                    resumo.ValorTotal().ObterValorEmReais(),                                                     //{18} Valor Total
                    trCreditosPgOnlineComprados,                            // {19}
                    trCreditosPgOnlineUtilizados,                           // {20}
                    resumo.TituloCampoValorTotalMesAno,                     // {21}
                    resumo.ValorTotalMesAno.ObterValorEmReais()            // {22}
                );
            }
            return resumoFechamentos;
        }

        private static string FormatarCorpoTotaisDosAgendamentos(ResumoDosAgendamentosDTO resumo, string corpoTotaisDosAgendamentos)
        {
            var resumoDosAgendamentos = String.Empty;
            if (resumo != null)
            {
                var descricaoDaTabela = String.Empty;
                descricaoDaTabela += resumo.TotalAguardandoConfirmacao > 0 ? "<div>aguardando confirmação</div>" : string.Empty;
                descricaoDaTabela += resumo.TotalConfirmados > 0 ? "<div>confirmado</div>" : string.Empty;
                descricaoDaTabela += resumo.TotalEmAtendimento > 0 ? "<div>em atendimento</div>" : string.Empty;
                descricaoDaTabela += resumo.TotalFinalizados > 0 ? "<div>finalizado</div>" : string.Empty;
                descricaoDaTabela += resumo.TotalCancelados > 0 ? "<div>cancelado</div>" : string.Empty;
                descricaoDaTabela += resumo.TotalClienteFaltou > 0 ? "<div>cliente faltou</div>" : string.Empty;

                var conteudoStatusTabela = String.Empty;
                conteudoStatusTabela += resumo.TotalAguardandoConfirmacao > 0 ? "<div>" + resumo.TotalAguardandoConfirmacao + "</div>" : string.Empty;
                conteudoStatusTabela += resumo.TotalConfirmados > 0 ? "<div>" + resumo.TotalConfirmados + "</div>" : string.Empty;
                conteudoStatusTabela += resumo.TotalEmAtendimento > 0 ? "<div>" + resumo.TotalEmAtendimento + "</div>" : string.Empty;
                conteudoStatusTabela += resumo.TotalFinalizados > 0 ? "<div>" + resumo.TotalFinalizados + "</div>" : string.Empty;
                conteudoStatusTabela += resumo.TotalCancelados > 0 ? "<div>" + resumo.TotalCancelados + "</div>" : string.Empty;
                conteudoStatusTabela += resumo.TotalClienteFaltou > 0 ? "<div>" + resumo.TotalClienteFaltou + "</div>" : string.Empty;

                resumoDosAgendamentos = string.Format(corpoTotaisDosAgendamentos,
                                                      descricaoDaTabela,                       //{0} Descricao dos Agendamentos
                                                      resumo.TotalDeAgendamentos(),            //{1} Total de Agendamentos
                                                      conteudoStatusTabela,                    //{2} Conteudo dos Agendamentos
                                                      resumo.TotalDeClientesComPagamento,      //{3} Clientes Com Pagamento
                                                      resumo.TotalDeClientesEmDebito,          //{4} Clientes em Débito
                                                      resumo.ValorTotalDeServicosEmDebito.ObterValorEmReais(),     //{5} Valor Total Serviços em Débito
                                                      resumo.ValorTotalDeProdutosEmDebito.ObterValorEmReais(),     //{6} Valor Total Produtos em Débito
                                                      resumo.TotalDeProdutosServicosEmDebito().ObterValorEmReais() //{7} Valor Total de Produtos e Serviços em Débitos
                );
            }
            return resumoDosAgendamentos;
        }

        #endregion Obtendo corpo do email de ResumoDoFechamentoEAgendamentos

        #endregion EnviarEmailParaOEstabelecimentoDoResumoDoFechamentoEAgendamentos

        #region Obtendo E-mails dos Profissionais

        public List<EmailPorEstabelecimentoDTO> ObterEmailsResponsaveisDosEstabelecimentos(IQueryable<Estabelecimento> estabelecimentosQueryable, NotificacaoEnum notificacao)
        {
            var lista = new List<EmailPorEstabelecimentoDTO>();

            lista.AddRange(ListarEmailsDeProfissionaisQueMarcaramParaReceberANotificacao(estabelecimentosQueryable, notificacao));
            lista.AddRange(ListarEmailsDeAdministradoresQueNaoMarcaramParaNaoReceberANotificacao(estabelecimentosQueryable, notificacao));

            lista = RemoverEmailsInvalidosEDuplicados(lista);

            return lista;
        }

        public List<EmailPorEstabelecimentoDTO> ObterEmailsResponsaveisDosEstabelecimentosPorTipoNotificacaoEComContaFinanceiraPositiva(NotificacaoEnum notificacao)
        {
            var lista = new List<EmailPorEstabelecimentoDTO>();

            lista.AddRange(ListarEmailsDeProfissionaisQueMarcaramParaReceberANotificacaoPorTipoNotificaoComContaFinanceiraPositiva(notificacao));
            lista.AddRange(ListarEmailsDeAdministradoresQueNaoMarcaramParaNaoReceberANotificacaoPorTipoNotificaoComContaPositiva(notificacao));

            lista = RemoverEmailsInvalidosEDuplicados(lista);

            return lista;
        }

        private List<EmailPorEstabelecimentoDTO> RemoverEmailsInvalidosEDuplicados(List<EmailPorEstabelecimentoDTO> lista)
        {
            return lista
                .Where(e => e.Email.EmailValido())
                .Select(a => a.IdEstabelecimento + ";" + a.Email + ";" + a.NomePessoaDestinatario)
                .Distinct()
                .Select(b => new EmailPorEstabelecimentoDTO
                {
                    IdEstabelecimento = int.Parse(b.Split(';')[0]),
                    Email = b.Split(';')[1],
                    NomePessoaDestinatario = b.Split(';')[2]
                })
                .ToList();
        }

        private List<EmailPorEstabelecimentoDTO> ListarEmailsDeAdministradoresQueNaoMarcaramParaNaoReceberANotificacao(IQueryable<Estabelecimento> estabelecimentosQueryable, NotificacaoEnum notificacao)
        {
            var usuariosDosEstabelecimentos = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable();
            var inscricoesNaNotificacao = Domain.Notificacoes.InscricaoEmNotificacaoRepository.Queryable();

            return (from e in estabelecimentosQueryable
                    join ue in usuariosDosEstabelecimentos on e.IdEstabelecimento equals ue.Estabelecimento.IdEstabelecimento
                    where ue.Ativo
                       && ue.PerfilAcesso == AcessoBackoffice.Acesso_total
                       && !inscricoesNaNotificacao.Any(insc => insc.IdPessoaJuridica == e.PessoaJuridica.IdPessoa
                                                            && insc.IdPessoaDestinatario == ue.PessoaFisica.IdPessoa)
                    select new EmailPorEstabelecimentoDTO
                    {
                        Email = ue.PessoaFisica.Email,
                        IdEstabelecimento = e.IdEstabelecimento,
                        NomePessoaDestinatario = ue.PessoaFisica.NomeCompleto
                    }).ToList();
        }

        private List<EmailPorEstabelecimentoDTO> ListarEmailsDeAdministradoresQueNaoMarcaramParaNaoReceberANotificacaoPorTipoNotificaoComContaPositiva(NotificacaoEnum notificacao)
        {
            var usuariosDosEstabelecimentos = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable();
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Queryable();
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            var inscricoesNaNotificacao = Domain.Notificacoes.InscricaoEmNotificacaoRepository.Queryable();

            return (from ue in usuariosDosEstabelecimentos
                    join e in estabelecimento on ue.Estabelecimento.IdEstabelecimento equals e.IdEstabelecimento
                    join cf in contaFinanceira on e.PessoaJuridica.IdPessoa equals cf.Pessoa.IdPessoa
                    where ue.Ativo
                       && ue.PerfilAcesso == AcessoBackoffice.Acesso_total
                       && cf.Ativo
                       && (new[] { 1, 3, 4, 9 }).Contains(cf.Status.IdStatus)
                       && !inscricoesNaNotificacao.Any(insc => insc.IdPessoaJuridica == ue.Estabelecimento.PessoaJuridica.IdPessoa
                                                            && insc.IdPessoaDestinatario == ue.PessoaFisica.IdPessoa)
                    select new EmailPorEstabelecimentoDTO
                    {
                        Email = ue.PessoaFisica.Email,
                        IdEstabelecimento = ue.Estabelecimento.IdEstabelecimento,
                        NomePessoaDestinatario = ue.PessoaFisica.NomeCompleto
                    }).ToList();
        }

        private List<EmailPorEstabelecimentoDTO> ListarEmailsDeProfissionaisQueMarcaramParaReceberANotificacao(IQueryable<Estabelecimento> estabelecimentosQueryable, NotificacaoEnum notificacao)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
            var inscricoesNaNotificacao = Domain.Notificacoes.InscricaoEmNotificacaoRepository.Queryable();

            return (from e in estabelecimentosQueryable
                    join ep in estabelecimentoProfissional on e.IdEstabelecimento equals ep.Estabelecimento.IdEstabelecimento
                    join insc in inscricoesNaNotificacao on ep.Profissional.PessoaFisica.IdPessoa equals insc.IdPessoaDestinatario
                    where ep.Ativo
                       && ep.Ativo
                       && insc.Tipo == TipoNotificacaoEnum.Email
                       && insc.Notificacao == notificacao
                       && insc.IdPessoaJuridica == e.PessoaJuridica.IdPessoa
                       && insc.ReceberNotificacao
                    select new EmailPorEstabelecimentoDTO
                    {
                        Email = ep.Profissional.PessoaFisica.Email,
                        IdEstabelecimento = e.IdEstabelecimento,
                        NomePessoaDestinatario = ep.Profissional.PessoaFisica.NomeCompleto
                    }).ToList();
        }

        private List<EmailPorEstabelecimentoDTO> ListarEmailsDeProfissionaisQueMarcaramParaReceberANotificacaoPorTipoNotificaoComContaFinanceiraPositiva(NotificacaoEnum notificacao)
        {
            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Queryable();
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            var inscricoesNaNotificacao = Domain.Notificacoes.InscricaoEmNotificacaoRepository.Queryable();

            return (from ep in estabelecimentoProfissional
                    join e in estabelecimento on ep.Estabelecimento.IdEstabelecimento equals e.IdEstabelecimento
                    join cf in contaFinanceira on e.PessoaJuridica.IdPessoa equals cf.Pessoa.IdPessoa
                    join insc in inscricoesNaNotificacao on ep.Profissional.PessoaFisica.IdPessoa equals insc.IdPessoaDestinatario
                    where ep.Ativo
                       && ep.Ativo
                       && insc.Tipo == TipoNotificacaoEnum.Email
                       && insc.Notificacao == notificacao
                       && insc.IdPessoaJuridica == ep.Estabelecimento.PessoaJuridica.IdPessoa
                       && insc.ReceberNotificacao
                       && cf.Ativo
                       && (new[] { 1, 3, 4, 9 }).Contains(cf.Status.IdStatus)
                    select new EmailPorEstabelecimentoDTO
                    {
                        Email = ep.Profissional.PessoaFisica.Email,
                        IdEstabelecimento = ep.Estabelecimento.IdEstabelecimento,
                        NomePessoaDestinatario = ep.Profissional.PessoaFisica.NomeCompleto
                    }).ToList();
        }

        #endregion Obtendo E-mails dos Profissionais

        #region E-mail Problema no envio

        public void EnviarEmailMarketingProblemaNoEnvio(MarketingEnvio marketingEnvio)
        {
            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(marketingEnvio.Estabelecimento, false).Distinct().ToList();

            var historicoEnvio = Domain.Marketing.MarketingCampanhaHistoricoRepository.ListarRegistroAlteracaoHistoricoDTO(marketingEnvio.MarketingCampanha.IdMarketingCampanha).ToList();
            var motivo = historicoEnvio.LastOrDefault().DescricaoHistorico;
            var nomeTipo = marketingEnvio is MarketingEnvioSMS ? "SMS" : "e-mails";

            var assunto = string.Format(ConfiguracoesTrinks.EnvioEmail.CampanhaNaoEnviada, nomeTipo, marketingEnvio.Estabelecimento.NomeDeExibicaoNoPortal);

            var emailsParaEnviar = ObterCorpoEmailMarketingProblemaNoEnvio(marketingEnvio, nomeTipo, responsaveisEstabelecimento, motivo);
            var emailAtendimento = ObterEmailDeAtendimento();
            var emailFabrica = ObterEmailFabrica();
            List<MailAddress> destinosOcultos = new List<MailAddress>();
            destinosOcultos.Add(emailAtendimento);
            destinosOcultos.Add(emailFabrica);

            var remetente = ObterRemetentePadrao();

            if (emailsParaEnviar.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var email in emailsParaEnviar)
                    {
                        EmailManager.SendHtml(
                            toAddresses: new MailAddress[] { email.Key },
                            bccAddresses: destinosOcultos.ToArray(),
                            fromAddress: remetente,
                            subject: assunto,
                            bodyHtml: email.Value
                        );
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarMarketingProblemaNoEnvio - Envio Id " + marketingEnvio.IdMarketingEnvio.ToString());
        }

        private Dictionary<MailAddress, string> ObterCorpoEmailMarketingProblemaNoEnvio(MarketingEnvio marketingEnvio, string tipoCampanhaParaExibicao, List<MailAddress> destinatarios, string motivo)
        {
            var caminhoTemplate = "MarketingCampanhaNaoEnviadaPorProblemaNoEnvio.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var retorno = new Dictionary<MailAddress, string>();
            Estabelecimento estabelecimento = marketingEnvio.Estabelecimento;
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            foreach (var responsavel in destinatarios)
            {
                var mensagem = string.Format(corpo,
                    responsavel.DisplayName,                                                // 0 -> Nome
                    tipoCampanhaParaExibicao,                                               // 1 -> Tipo da campanha (e-mail || SMS)
                    marketingEnvio.MarketingCampanha.NomeCampanha,                          // 2 -> Nome da campanha
                    marketingEnvio.DataHoraProgramada.ToBrazilianLongDateTimeString(),      // 3 -> Data/hora programação de envio da campanha
                    urlLogoEstabelecimento,                                                 // 4 -> Caminho para a logo do estabelecimento
                    motivo                                                                  // 5 -> Motivo registrado no historico da campanha
                );

                retorno.Add(responsavel, mensagem);
            }

            return retorno;
        }

        #endregion E-mail Problema no envio

        #region E-mail de feedback do usuário sobre o aplicativo

        private string ObterCorpoDoEmailDeFeedbackDoUsuarioSobreAplicativo(Conta conta, string feedbackDoUsuario, string app)
        {
            StringBuilder sb = new StringBuilder();

            sb.AppendFormat("{0} ({1}) enviou o seguinte feedback sobre o aplicativo {2}:",
                conta.Pessoa.PessoaFisica.NomeOuApelido(),
                conta.Email,
                String.IsNullOrEmpty(app) ? "Trinks" : app);

            sb.Append("<br><br>");
            sb.Append(feedbackDoUsuario);

            return sb.ToString();
        }

        public void EnviarFeedbackDoUsuarioSobreAplicativo(Conta conta, string feedbackDoUsuario, string app = null)
        {
            string assunto = String.Format(ConfiguracoesTrinks.EnvioEmail.AssuntoEmailFeedbackDoAplicativoPeloUsuario, conta.Pessoa.PessoaFisica.NomeOuApelido());
            string corpoDoEmail = ObterCorpoDoEmailDeFeedbackDoUsuarioSobreAplicativo(conta, feedbackDoUsuario, app);

            MailAddress remetente = ObterEmailDeAtendimento();

            List<MailAddress> destino = new List<MailAddress>();
            destino.Add(remetente);

            EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpoDoEmail, null, null);
        }

        #endregion E-mail de feedback do usuário sobre o aplicativo

        #region E-mail de aviso de pedido de produto

        public void EnviarEmailDeAlteracaoDeStatusDePedido(PedidoDeCompra pedido, string pessoaFisicaQuemAlterou, bool ehReenvio, string statusAnteriorDoPedido, Attachment anexo = null)
        {
            var assunto = "Pedido de compra realizado";
            var titulo = "Pedido Enviado";
            var textoInicial = "Um novo pedido de produtos foi realizado.";
            var textoEnviadoOuReenviadoPor = "Pedido enviado por:";
            var textoDataEHoraDeEnvioOuReenvio = "Data e hora de envio:";
            var textoMotivoCancelamento = "";
            var linkDoArquivo = "";
            var data = pedido.DataEnvio;

            if (ehReenvio)
            {
                assunto = "Reenvio de pedido de compra realizado";
                titulo = "Reenvio de pedido realizado";
                textoInicial = "Um pedido de produtos foi reenviado.";
                data = DateTime.Now;
                textoEnviadoOuReenviadoPor = "Pedido reenviado por:";
                textoDataEHoraDeEnvioOuReenvio = "Data e hora do reenvio:";
            }
            if (pedido.Status == StatusPedidoDeCompraEnum.Cancelado)
            {
                titulo = "Pedido Cancelado";
                assunto = "Cancelamento de pedido de compra realizado";
                textoInicial = "Um pedido de produtos foi cancelado.";
                textoMotivoCancelamento = pedido.Cancelamento.MotivoDoCancelamento;
            }
            else
            {
                string url;
                SalvarArquivoAnexoNoS3(anexo, out url);
                linkDoArquivo = url;
            }
            var remetente = ObterEmailFaleConosco();

            var listaDestino = ObterEmailsResponsaveisEstabelecimento(pedido.Estabelecimento);

            if (pedido.Estabelecimento.EhUmEstabelecimentoFranqueadoAtivo())
            {
                var listaResponsaveisFranquia = ObterEmailsDosResponsaveisPelaFranquia(pedido.Estabelecimento.FranquiaEstabelecimento.Franquia.Id);
                foreach (var contato in listaResponsaveisFranquia)
                {
                    listaDestino.Add(contato);
                }
            }

            if (pedido.Status == StatusPedidoDeCompraEnum.Cancelado)
            {
                var corpo = ObterCorpoEmailPedidoCancelado(pedido, pessoaFisicaQuemAlterou, titulo, textoInicial, textoMotivoCancelamento, statusAnteriorDoPedido, textoEnviadoOuReenviadoPor, textoDataEHoraDeEnvioOuReenvio);

                if (listaDestino.Any())
                {
                    if (!SimulationTool.Current.EhSimulacao)
                        EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
                }
                else
                {
                    ErrorSignal.FromCurrentContext().Raise(new System.Exception("Erro na rotina de envio de email. Lista vazia."));
                }
            }
            else
            {
                var corpo = ObterCorpoEmailPedidoDeCompraRealizadoOuReenviado(pedido.Estabelecimento, pessoaFisicaQuemAlterou, pedido.NumeroIdentificadorDoPedido(), pedido.Status.ToString(), pedido.PessoaFisicaQuemCriou.NomeCompleto, data.ToString(), pedido.PessoaFisicaUltimaAlteracao.NomeCompleto, linkDoArquivo, titulo, textoInicial, textoEnviadoOuReenviadoPor, textoDataEHoraDeEnvioOuReenvio);
                if (listaDestino.Any())
                {
                    if (!SimulationTool.Current.EhSimulacao)
                        EmailManager.SendHtml(listaDestino.ToArray(), remetente, assunto, corpo, null, null);
                }
                else
                {
                    ErrorSignal.FromCurrentContext().Raise(new System.Exception("Erro na rotina de envio de email. Lista vazia."));
                }
            }
        }

        private string ObterCorpoEmailPedidoDeCompraRealizadoOuReenviado(Estabelecimento estabelecimento, string pessoaFisicaQuemAlterou, string numeroDoPedido, string statusDoPedido, string criadoPor, string dataEHora, string enviadoPor, string linkDoArquivo, string titulo, string textoInicial, string textoEnviadoOuReenviadoPor, string textoDataEHoraDeEnvioOuReenvio)
        {
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92, false);

            var caminhoTemplate = "EnvioDePedidoDeProdutoParaFranqueador.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = corpo.Replace("{BASE_URL}", ObterUrlBase());
            corpo = corpo.Replace("{URL_LOGO_ESTABELECIMENTO}", urlLogoEstabelecimento);
            corpo = corpo.Replace("{URL_DOWNLOAD_ANEXO}", linkDoArquivo);

            var model = new
            {
                Titulo = titulo,
                TextoInicial = textoInicial,
                NomeDoEstabelecimento = estabelecimento.PessoaJuridica.NomeFantasia,
                NumeroPedido = numeroDoPedido,
                StatusDoPedido = statusDoPedido,
                CriadoPor = criadoPor,
                EnviadoPor = pessoaFisicaQuemAlterou,
                DataEHora = dataEHora,
                TelefoneRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_telefones).ObterValor(),
                EmailAtendimentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_email_atendimento).ObterValor(),
                HorarioFuncionamentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_horario_funcionamento).ObterValor().ToLower(),
                PossuiLogoEstabelecimento = !string.IsNullOrWhiteSpace(urlLogoEstabelecimento),
                TextoEnviadoOuReenviadoPor = textoEnviadoOuReenviadoPor,
                TextoDataEHoraDeEnvioOuReenvio = textoDataEHoraDeEnvioOuReenvio
            };

            return re.Razor.Parse(corpo, model, caminhoTemplate); // Sempre preencher cacheName
        }

        private string ObterCorpoEmailPedidoCancelado(PedidoDeCompra pedido, string pessoaFisicaQuemAlterou, string titulo, string textoInicial, string textoMotivoCancelamento, string statusAnteriorDoPedido, string textoEnviadoOuReenviadoPor, string textoDataEHoraDeEnvioOuReenvio)
        {
            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(pedido.Estabelecimento, DimemsoesFotosEnum.Dim243x92, false);

            var caminhoTemplate = "CancelamentoDePedidoDeProdutoParaFranqueador.cshtml";
            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            corpo = corpo.Replace("{BASE_URL}", ObterUrlBase());
            corpo = corpo.Replace("{URL_LOGO_ESTABELECIMENTO}", urlLogoEstabelecimento);

            var model = new
            {
                Titulo = titulo,
                TextoInicial = textoInicial,
                NomeDoEstabelecimento = pedido.Estabelecimento.PessoaJuridica.NomeFantasia,
                NumeroPedido = pedido.NumeroIdentificadorDoPedido(),
                StatusAnteriorDoPedido = statusAnteriorDoPedido,
                StatusAtualDoPedido = pedido.Status,
                CriadoPor = pedido.PessoaFisicaQuemCriou,
                DataEHora = pedido.DataHoraUltimaAlteracaoDeStatus,
                CanceladoPor = pessoaFisicaQuemAlterou,
                TextoMotivoDoCancelamento = pedido.Cancelamento.MotivoDoCancelamento,
                TelefoneRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_telefones).ObterValor(),
                EmailAtendimentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_email_atendimento).ObterValor(),
                HorarioFuncionamentoRodape = new ParametrosTrinks<string>(ParametrosTrinksEnum.rodape_horario_funcionamento).ObterValor().ToLower(),
                PossuiLogoEstabelecimento = !string.IsNullOrWhiteSpace(urlLogoEstabelecimento),
                TextoEnviadoOuReenviadoPor = textoEnviadoOuReenviadoPor,
                TextoDataEHoraDeEnvioOuReenvio = textoDataEHoraDeEnvioOuReenvio
            };

            return re.Razor.Parse(corpo, model, caminhoTemplate); // Sempre preencher cacheName
        }

        public void EnviarEmailSeNaoTiverFiltro(Estabelecimento estabelecimentoAutenticado, Conta contaAutenticada)
        {
            var dataUltimoAgendamentoRealizado = Domain.Pessoas.HorarioRepository.ObterDataCriacaoDoUltimoHorarioBalcaoAgendado(estabelecimentoAutenticado.IdEstabelecimento);
            var quantidadeDeProfissionaisAtivos = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarAtivosEComAgendaPorEstabelecimento(estabelecimentoAutenticado.IdEstabelecimento).ToList().Count;

            var corpo = string.Format("O usuário {0} realizou a exportação da base completa de clientes em {1}. <br /> O link para o resumo de dados do estabelecimento: {2}.<br /> Numero de profissionais: {3}.<br /> Último agendamento: {4}.",
                                      contaAutenticada.Email,
                                      Calendario.Agora(),
                                      ObterUrlDaVitrineComResumoJaAberto(estabelecimentoAutenticado),
                                      quantidadeDeProfissionaisAtivos,
                                      dataUltimoAgendamentoRealizado != null ? dataUltimoAgendamentoRealizado.Value.ToBrazilianLongDateTimeString() : "N/A");
            var assunto = string.Format("{0} exportou todos os clientes", estabelecimentoAutenticado.NomeDeExibicaoNoPortal);
            var emailTo = "<EMAIL>";

            var remetente = ObterRemetentePadrao();
            var destino = new List<MailAddress>();
            destino.Add(new MailAddress(emailTo));
            EmailManager.SendHtml(destino.ToArray(), remetente, assunto, corpo, null, null);
        }

        private string ObterUrlDaVitrineComResumoJaAberto(Estabelecimento estabelecimentoAutenticado)
        {
            return ObterUrlBase() + "/Perlink/VitrineEstabelecimentos?idEstabelecimento=" + estabelecimentoAutenticado.IdEstabelecimento + "&abrirResumo=true";
        }

        #endregion E-mail de aviso de pedido de produto

        public DadosParaTemplateDoEmailDTO ObterDadosParaTemplateDoEmail(Estabelecimento estabelecimento)
        {
            var dados = new DadosParaTemplateDoEmailDTO();

            var hotSite = estabelecimento.Hotsite();

            dados.UrlHotSiteDoEstabelecimento = $"{ObterUrlBase()}/{hotSite.Url ?? ""}";

            var urlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92);

            dados.UrlLogoDoEstabelecimento = urlLogoEstabelecimento;
            dados.TelefonesDoEstabelecimento = estabelecimento.PessoaJuridica.Telefones.Ativos().ToTextoFormatadoLista();
            dados.EnderecoCompletoDoEstabelecimento = estabelecimento.PessoaJuridica.EnderecoProprio.ToTextoFormatado();

            if (estabelecimento.PossuiAplicativoProprio())
            {
                dados.UrlAppIos = estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppIOS;
                dados.UrlAppAndroid = estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppAndroid;
                dados.TextoBaixeAplicativo = ObterTextoAplicativo(hotSite.PermiteAgendamentoHotsite, hotSite.PermiteBuscaHotsite, estabelecimento.FranquiaEstabelecimento.Franquia.Nome);
            }
            else
            {
                dados.UrlAppIos = ObterLinkDownloadAppIos();
                dados.UrlAppAndroid = ObterLinkDownloadAppAndroid();
                dados.TextoBaixeAplicativo = ObterTextoAplicativo(hotSite.PermiteAgendamentoHotsite, hotSite.PermiteBuscaHotsite);
            }

            return dados;
        }

        private string ObterTextoAplicativo(bool permiteAgendarHotsite, bool permiteBucaHotSite, string nomePersonalizadoDoApp = null)
        {
            string texto = $"Baixe o aplicativo do <a style='color:#0d5ea9;text-decoration: none;' href='https://www.trinks.com'>Trinks.com</a> gratuitamente para marcar seus horários e acompanhar seus agendamentos pelo smartphone ou tablet, de onde estiver!";

            if (!string.IsNullOrWhiteSpace(nomePersonalizadoDoApp))
                texto = texto.Replace("do Trinks", nomePersonalizadoDoApp);

            if (!permiteAgendarHotsite || !permiteBucaHotSite)
                texto = texto.Replace("para marcar seus horários e acompanhar seus agendamentos", "para nos acompanhar");

            return texto;
        }

        public void EnviarEmailDePagamentoEfetuado(string assunto, List<MailAddress> destinatarios, NotificacaoPagamentoEfetuadoDTO dadosHtml)
        {
            string conteudo = ObterTextoCompletoDoArquivo(caminhoDoArquivo: "PagamentoAntecipado.PagamentoEfetuado.cshtml");
            string htmlBody = re.Razor.Parse(conteudo, dadosHtml, "PagamentoEfetuado"); // Sempre preencher cacheName

            MailAddress remetente = ObterRemetentePadrao();
            List<MailAddress> destinatariosOcultos = new List<MailAddress>();

            EmailManager.SendHtml(destinatarios.ToArray(), destinatariosOcultos.ToArray(), remetente, assunto, htmlBody);
        }

        public void EnviarEmailDeAvisoAoCrossSellSobreInteresseNoAdicional(string nomeEstabelecimento, string nomeAdicional, string nomePessoaQueSolicitou)
        {
            var assunto = $"Interesse no adicional {nomeAdicional} do estabelecimento {nomeEstabelecimento}";
            var corpo = $"O cliente {nomePessoaQueSolicitou} do estabelecimento {nomeEstabelecimento} solicitou informações sobre o adicional {nomeAdicional}.";
            var remetente = ObterRemetentePadrao();
            var destinatario = ObterEmailDoCrossSell();
            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(new MailAddress[] { destinatario }, remetente, assunto, corpo, null, null);
            }
        }

        public void EnviarEmailAoAtendimentoParaAdicionalContratadoPeloEstabelecimento(string nomeEstabelecimento, List<string> nomesDosAdicionais, string nomeTela, string nomePessoaQueContratou)
        {
            if (AtendimentoDeveSerNotificadoDaContratacaoDoAdicional(nomesDosAdicionais))
            {
                var assunto = $"Contratação de ADICIONAL pelo backoffice - {string.Join(", ", nomesDosAdicionais)} - {nomePessoaQueContratou} do estabelecimento {nomeEstabelecimento} contratou pela tela {nomeTela}";

                var corpo = assunto;
                var remetente = ObterRemetentePadrao();

                var listaDeDestinatarios = new List<MailAddress> {
                ObterEmailDeAtendimento(),
                ObterEmailDoCrossSell()
                };

                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(listaDeDestinatarios.ToArray(), remetente, assunto, corpo, null, null);
                }
            }
        }

        private static readonly HashSet<ServicoAdicionalEnum> ServicosNotificaveis = new HashSet<ServicoAdicionalEnum>
        {
            ServicoAdicionalEnum.NFSe,
            ServicoAdicionalEnum.NFCe,
            ServicoAdicionalEnum.BelezinhaComSplit,
            ServicoAdicionalEnum.BelezinhaSemSplit,
            ServicoAdicionalEnum.Autoatendimento
        };

        private bool AtendimentoDeveSerNotificadoDaContratacaoDoAdicional(List<string> nomesDosAdicionais)
        {
            return nomesDosAdicionais.Any(nome =>
                Enum.TryParse<ServicoAdicionalEnum>(nome, out var servico) &&
                ServicosNotificaveis.Contains(servico));
        }

        public void EnviarEmailAoAtendimentoQuandoPOSSiclosEstiverHabilitado(string nomeEstabelecimento, bool status, string msg)
        {
            var assunto = $"Status de habilitação do POS Siclos no estabelecimento {nomeEstabelecimento}";

            var corpo = $"Status de habilitação do POS Siclos no estabelecimento {nomeEstabelecimento} <br/>Status: {status}<br/>Mensagem: {msg}";

            var remetente = ObterRemetentePadrao();

            var listaDeDestinatarios = new List<MailAddress> {
                ObterEmailDeAtendimento()
            };

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(listaDeDestinatarios.ToArray(), remetente, assunto, corpo, null, null);
            }
        }

        public void EnviarEmailRelatorioComissao(List<MailAddress> destinatario, string assunto, string corpo, MemoryStream arquivoPDF, string nomeDoArquivo, Estabelecimento estabelecimento)
        {
            var remetente = ObterRemetenteNaoResponda(estabelecimento);
            Attachment[] anexo = new Attachment[1];
            anexo[0] = new Attachment(arquivoPDF, new ContentType("text/pdf"));
            anexo[0].Name = nomeDoArquivo;

            if (destinatario.Count() > 0)
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    EmailManager.SendHtml(destinatario.ToArray(), remetente, assunto, corpo, null, anexo);
                }
            }
        }

        public void EnviarEmailDeSolicitacaoDeBuscaNoPortal(Pessoas.Estabelecimento estabelecimento, Pessoas.DTO.RequisitosBuscaNoPortalDTO atualizar, PessoaFisica pessoaLogada)
        {
            var solicitacao = new EstabelecimentoSolicitacaoAparecerBusca();
            solicitacao.DataSolicitacao = Calendario.Agora();
            solicitacao.Estabelecimento = estabelecimento;
            solicitacao.PessoaFisicaQueSolicitou = pessoaLogada;

            if (atualizar.EnderecoDoEstabelecimentoPossuiCEP && atualizar.EstabelecimentoPossuiLogo && atualizar.EstabelecimentoPossuiServicoComPrecoVinculadoAhProfissionalAtivo && atualizar.ExistePeloMenosUmaFotoAssociadaAoEstabelecimento)
            {
                String url = estabelecimento.Hotsite() != null ? estabelecimento.Hotsite().Url : String.Empty;
                Domain.Pessoas.EnvioEmailService.EnviarEmailSolicitacaoEstabelecimentoAparecerNaBuscaPortal(url, estabelecimento.PessoaJuridica.NomeFantasia);
            }
        }

        public void EnviarEmailPrestacaoDeContaProfissionalParceiro()
        {
            var remetente = ObterRemetenteParceiro();

            var listaParceiro = Domain.Cobranca.FaturaRepository.ObterListaParceirosAtivosEQueTemValorAReceberNoMesAnterior();
            var ListaDeNomesParceiros = listaParceiro.GroupBy(l => l.NomeParceiro);

            foreach (var nomeParceiro in ListaDeNomesParceiros)
            {
                var assunto = nomeParceiro.Select(p => p.CupomParceiro).FirstOrDefault() + " -  Veja aqui seu resultado do mês";

                var corpo = ObterCorpoEmailPrestacaoContaParceiro(nomeParceiro.Select(p => p.NomeParceiro).FirstOrDefault());

                //Obter destinatário...
                var emailDestinatario = nomeParceiro.Select(p => p.Email).FirstOrDefault();
                if (emailDestinatario.EmailValido())
                {
                    var destinatarios = new List<MailAddress>();
                    destinatarios.Add(new MailAddress(emailDestinatario));

                    var dataMesAnterior = DateTime.Today.AddMonths(-1);

                    //Obter Anexo para enviar no e-mail...
                    var planilhasParceiros = nomeParceiro.Where(p => Convert.ToDateTime(p.DataPagamento).Month == dataMesAnterior.Month && Convert.ToDateTime(p.DataPagamento).Year == dataMesAnterior.Year && p.NumeroDaParcela < 13).OrderBy(p => p.NomeEstabelecimento).ToList();
                    var totalReceberPorMes = planilhasParceiros.Sum(pp => pp.ValorReceber);
                    if (totalReceberPorMes > 0)
                    {
                        var planilha = CriarAnexoParceiroPDF(new { Parceiros = planilhasParceiros, TotalReceberPorMes = totalReceberPorMes });
                        Attachment[] anexo = new Attachment[1];
                        anexo[0] = new Attachment(planilha, new ContentType("text/pdf"));
                        anexo[0].Name = "Programa de Agentes - Planilha de pagamentos.pdf";

                        var enderecoOculto = ObterEmailParceiroTrinks();
                        var destinatarioOculto = new List<MailAddress>();
                        destinatarioOculto.Add(enderecoOculto);

                        if (destinatarios != null)
                        {
                            if (!SimulationTool.Current.EhSimulacao)
                            {
                                EmailManager.SendHtml(destinatarios.ToArray(), destinatarioOculto.ToArray(), remetente, assunto, corpo, null, anexo);
                            }
                        }
                    }
                }
            }
        }

        private MemoryStream CriarAnexoParceiroPDF(object model)
        {
            var template = RazorEngine.Razor.Parse(
                 ResourceManager.ReadText(typeof(Textos).Assembly,
                    "Perlink.Trinks.Resources.TemplatesEmail.PlanilhaDePagamentoParceiro.cshtml")
                , model, "Planilha");

            return PDFUtils.GerarPDFPeloHtml(template);
        }

        public string ObterCorpoEmailPrestacaoContaParceiro(string nomeParceiro)
        {
            var corpo = "Oi, " + (nomeParceiro != null ? nomeParceiro : "Parceiro Trinks") + ", como vai? <br/><br/>" +
                "Chegou a hora de conferir seu resultado geral do mês anterior.<br/><br/>" +
                "Aviso: Certifique-se que seu contrato está devidamente assinado para efetuarmos o seu pagamento até o dia 15.<br/><br/>" +
                "A falta da assinatura do contrato até o dia 07, acarretará no pagamento no dia 15 do mês subsequente.<br/><br/>" +
                "Ah! E não se esqueça de salvar nosso número de Whatsapp (21) 99864-7551 e nosso e-mail <EMAIL> para falar com nosso time de Parcerias.<br/><br/>" +
                "Se precisar de ajuda, conte com a gente. Juntos somos mais!<br/><br/>" +
                "Abraços,<br/>" +
                "Equipe Trinks - Parcerias<br/>" +
                "www.trinks.com";

            return corpo;
        }

        public void DispararEmail(string assunto, string corpo, MailAddress remetente, MailAddress destinatario, MailAddress destinatarioOculto, Attachment[] anexos = null)
        {
            var destinatarios = new MailAddress[] { destinatario };

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(destinatarios, null, remetente, assunto, corpo, null, attachments: anexos);
            }
        }

        public void DispararEmail(EmailParaEnvioDTO email)
        {
            var anexos = email.Anexos.Count > 0 ? email.Anexos.ToArray() : null;

            DispararEmail(email.Assunto, email.Corpo, email.Remetente, email.Destinatario, email.DestinatarioOculto, anexos);
        }

        public string ObterCorpoDeEmailPeloNomeDoArquivo(string nomeDoArquivo, object model)
        {
            var template = ObterTextoCompletoDoArquivo(nomeDoArquivo);
            return re.Razor.Parse(template, model, nomeDoArquivo);
        }

        private string ObterTemplateDeEmailsEnviadosParaClientesDosEstabelecimentos(Estabelecimento estabelecimento)
        {
            var nomeArquivo = "Shared.LayoutPadraoParaClienteEstabelecimento.cshtml";

            var UrlAppIos = ObterLinkDownloadAppIos();
            var UrlAppAndroid = ObterLinkDownloadAppAndroid();
            var NomeAplicativo = "Trinks";

            if (estabelecimento.PossuiAplicativoProprio())
            {
                UrlAppIos = estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppIOS;
                UrlAppAndroid = estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppAndroid;
                NomeAplicativo = estabelecimento.FranquiaEstabelecimento.Franquia.Nome;
            }

            var UrlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92, false);

            var model = new
            {
                UrlLogoEstabelecimento,
                UrlAppIos,
                UrlAppAndroid,
                NomeAplicativo
            };

            return ObterCorpoDeEmailPeloNomeDoArquivo(nomeArquivo, model);
        }

        public string ObterCorpoDeEmailParaClienteEstabelecimentoPeloNomeDoArquivo(Estabelecimento estabelecimento, string nomeDoArquivo, object model)
        {
            var layoutDoEmail = ObterTemplateDeEmailsEnviadosParaClientesDosEstabelecimentos(estabelecimento);
            var conteudoEmail = ObterCorpoDeEmailPeloNomeDoArquivo(nomeDoArquivo, model);

            return layoutDoEmail.Replace("{{main}}", conteudoEmail);
        }

        public string ObterCorpoDeEmailParaUsuarioWebPeloNomeDoArquivo(string nomeDoArquivo, object model)
        {
            var nomeArquivo = "Shared.LayoutPadraoParaUsuarioWeb.cshtml";

            var UrlAppIos = ObterLinkDownloadAppIos();
            var UrlAppAndroid = ObterLinkDownloadAppAndroid();
            var NomeAplicativo = "Trinks";

            var modelLayoutDoEmail = new
            {
                UrlAppIos,
                UrlAppAndroid,
                NomeAplicativo
            };

            var layoutDoEmail = ObterCorpoDeEmailPeloNomeDoArquivo(nomeArquivo, modelLayoutDoEmail);
            var conteudoEmail = ObterCorpoDeEmailPeloNomeDoArquivo(nomeDoArquivo, model);

            return layoutDoEmail.Replace("{{main}}", conteudoEmail);
        }

        public string ObterCorpoDeEmailParaProfissionalEstabelecimento(Estabelecimento estabelecimento, string nomeDoArquivo, object model)
        {
            var layoutDoEmail = ObterTemplateDeEmailsEnviadosParaProfissionalDoEstabelecimento(estabelecimento);
            var conteudoEmail = ObterCorpoDeEmailPeloNomeDoArquivo(nomeDoArquivo, model);

            return layoutDoEmail.Replace("{{main}}", conteudoEmail);
        }

        private string ObterTemplateDeEmailsEnviadosParaProfissionalDoEstabelecimento(Estabelecimento estabelecimento)
        {
            var nomeArquivo = "Shared.LayoutPadraoParaProfissionalEstabelecimento.cshtml";

            var UrlAppIos = new ParametrosTrinks<String>(ParametrosTrinksEnum.link_download_app_pro_ios).ObterValor();
            var UrlAppAndroid = new ParametrosTrinks<String>(ParametrosTrinksEnum.link_download_app_pro_android).ObterValor();
            var UrlLogoEstabelecimento = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoService.ObterCaminhoWeb(estabelecimento, DimemsoesFotosEnum.Dim243x92, false);

            var model = new
            {
                UrlLogoEstabelecimento,
                UrlAppIos,
                UrlAppAndroid
            };

            return ObterCorpoDeEmailPeloNomeDoArquivo(nomeArquivo, model);
        }

        public void EnviarEmailRotinaMensagensCompraNaoAutorizada(Estabelecimento estabelecimento)
        {
            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(estabelecimento, false).Distinct().ToList();

            var assunto = "Compra não autorizada: Rotina de Mensagens pela Trinks";

            var emailsParaEnviar = ObterCorpoEmailRotinaMensagensCompra(estabelecimento, responsaveisEstabelecimento);
            var emailAtendimento = ObterEmailDeAtendimento();
            List<MailAddress> destinosOcultos = new List<MailAddress>() { emailAtendimento };

            var remetente = ObterRemetentePadrao();

            if (emailsParaEnviar.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var email in emailsParaEnviar)
                    {
                        EmailManager.SendHtml(
                            toAddresses: new MailAddress[] { email.Key },
                            bccAddresses: destinosOcultos.ToArray(),
                            fromAddress: remetente,
                            subject: assunto,
                            bodyHtml: email.Value
                        );
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailRotinaMensagensCompraNaoAutorizada - Estabelecimento Id " + estabelecimento.IdEstabelecimento.ToString());
        }

        public void EnviarEmailRotinaMensagensCompraAutorizada(Estabelecimento estabelecimento, string qtdPacote, string valorPacote)
        {
            var responsaveisEstabelecimento = ObterEmailsResponsaveisEstabelecimento(estabelecimento, false).Distinct().ToList();

            var assunto = "Compra efetuada: Pacote recorrente da Rotina de Mensagens";

            var emailsParaEnviar = ObterCorpoEmailRotinaMensagensCompraAutorizada(estabelecimento, responsaveisEstabelecimento, qtdPacote, valorPacote);
            var emailAtendimento = ObterEmailDeAtendimento();
            List<MailAddress> destinosOcultos = new List<MailAddress>() { emailAtendimento };

            var remetente = ObterRemetentePadrao();

            if (emailsParaEnviar.Any())
            {
                if (!SimulationTool.Current.EhSimulacao)
                {
                    foreach (var email in emailsParaEnviar)
                    {
                        EmailManager.SendHtml(
                            toAddresses: new MailAddress[] { email.Key },
                            bccAddresses: destinosOcultos.ToArray(),
                            fromAddress: remetente,
                            subject: assunto,
                            bodyHtml: email.Value
                        );
                    }
                }
            }

            LogService<EnvioEmailService>.Info("EnviarEmailRotinaMensagensCompraAutorizada - Estabelecimento Id " + estabelecimento.IdEstabelecimento.ToString());
        }

        private Dictionary<MailAddress, string> ObterCorpoEmailRotinaMensagensCompraAutorizada(Estabelecimento estabelecimento,
            List<MailAddress> destinatarios, string qtdPacote, string valorPacote)
        {
            var result = ObterCorpoEmailRotinaMensagensCompra(estabelecimento, destinatarios, true);
            var retorno = new Dictionary<MailAddress, string>();

            foreach (var r in result)
            {
                var valorModificado = r.Value.Replace("{QTD_PACOTE}", qtdPacote);
                valorModificado = valorModificado.Replace("{VALOR_PACOTE}", valorPacote);

                retorno.Add(r.Key, valorModificado);
            }

            return retorno;
        }

        private Dictionary<MailAddress, string> ObterCorpoEmailRotinaMensagensCompra(Estabelecimento estabelecimento,
            List<MailAddress> destinatarios, bool compraAutorizada = false)
        {
            var caminhoTemplate = compraAutorizada ? "RotinaMensagensWhatsApp.CompraAutorizada.cshtml" : "RotinaMensagensWhatsApp.CompraNaoAutorizada.cshtml";

            var corpo = ObterTextoCompletoDoArquivo(caminhoTemplate);
            var retorno = new Dictionary<MailAddress, string>();
            var sufixoCompraDeCreditos = string.Format("{0}/BackOffice/{1}", ObterUrlBase(), "WhatsApp");

            corpo = corpo.Replace("{NOME_ESTABELECIMENTO}", estabelecimento.NomeDeExibicaoNoPortal);
            corpo = corpo.Replace("{linkCompra}", sufixoCompraDeCreditos);

            foreach (var responsavel in destinatarios)
            {
                var corpoModificado = corpo;
                corpoModificado = corpoModificado.Replace("{NOME_RESPONSAVEL}", responsavel.DisplayName);

                retorno.Add(responsavel, corpoModificado);
            }

            return retorno;
        }

        public void EnviarEmailItensNaoSincronizadoBaixaAutomatica(List<string> itens, Estabelecimento estabelecimento, string estabelecimentoDestino)
        {
            var assunto = "Aviso da Sincronização de Produtos da baixa Associados aos Serviços - Estabelecimento: " + estabelecimentoDestino;
            var listaDestinos = ObterEmailsResponsaveisEstabelecimento(estabelecimento);
            var remetente = EnvioEmailService.ObterRemetenteNaoResponda(estabelecimento);
            var corpo = ObterCorpoEmailItensNaoSincronizadoBaixaAutomatica(estabelecimentoDestino, itens);

            if (!SimulationTool.Current.EhSimulacao)
            {
                EmailManager.SendHtml(listaDestinos.ToArray(),
                    remetente,
                    assunto,
                    corpo,
                    null,
                    null);
            }
        }

        private string ObterCorpoEmailItensNaoSincronizadoBaixaAutomatica(string estabelecimentoDestino, List<string> itens)
        {
            var corpo = "Não foi possível realizar a sincronização dos produtos de baixa automática associados aos seguintes serviços: ";
            corpo += string.Join("<br>", itens);
            corpo += $"<br>para o estabelecimento {estabelecimentoDestino}.<br>";
            corpo += "Será necessário agendar uma nova sincronia de serviços para que os produtos de baixa automática sejam sincronizados.";

            return corpo;
        }

    }
}

public class EnvioEmailInformacaoDoAplicativo
{
    public string TextoAplicativo { get; set; }
    public string UrlAppStore { get; set; }
    public string UrlPlayStore { get; set; }
}
