﻿using Castle.ActiveRecord;
using Elmah;
using NHibernate.Transform;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Compromissos.DTO;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.Enums;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Marcadores.DTO;
using Perlink.Trinks.Marcadores.Filtros;
using Perlink.Trinks.Notificacoes.Services;
using Perlink.Trinks.PagamentosAntecipados.DTO;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Repositories;
using Perlink.Trinks.Promocoes.Enums;
using Perlink.Trinks.PromocoesOnline;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using RESMensagens = Perlink.Trinks.Resources.Mensagens;

namespace Perlink.Trinks.Pessoas.Services
{

    public class AgendaService : BaseService, IAgendaService
    {

        #region Propriedades de Apoio

        private static IEnvioEmailService EnvioEmailService
        {
            get { return Domain.Pessoas.EnvioEmailService; }
        }

        private static IEnvioDeNotificacoesService EnvioDeNotificacoesService
        {
            get { return Domain.Notificacoes.EnvioDeNotificacoesService; }
        }

        private static IEstabelecimentoProfissionalServicoRepository EstabelecimentoProfissionalServicoRepository
        {
            get { return Domain.Pessoas.EstabelecimentoProfissionalServicoRepository; }
        }

        private static IEstabelecimentoRepository EstabelecimentoRepository
        {
            get { return Domain.Pessoas.EstabelecimentoRepository; }
        }

        private static IHorarioHistoricoRepository HorarioHistoricoRepository
        {
            get { return Domain.Pessoas.HorarioHistoricoRepository; }
        }

        private static IHorarioRepository HorarioRepository
        {
            get { return Domain.Pessoas.HorarioRepository; }
        }

        private static IHotsiteEstabelecimentoRepository HotsiteEstabelecimentoRepository
        {
            get { return Domain.Pessoas.HotsiteEstabelecimentoRepository; }
        }

        private static IPeriodoAusenciaRepository PeriodoAusenciaRepository
        {
            get { return Domain.Pessoas.PeriodoAusenciaRepository; }
        }

        private static IUsuarioEstabelecimentoRepository UsuarioEstabelecimentoRepository
        {
            get { return Domain.Pessoas.UsuarioEstabelecimentoRepository; }
        }

        #endregion Propriedades de Apoio

        #region Validações

        private void ValidarLimiteDiasMaximoAgendamento(ParametrosBusca parametrosBusca,
            HotsiteEstabelecimento configuracoes)
        {
            var fuso = 0;
            if (parametrosBusca.Estabelecimentos.Count > 0)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(parametrosBusca.Estabelecimentos[0]);
                fuso = estabelecimento != null ? estabelecimento.GetFuso() : 0;
            }

            var numeroDias = (int)configuracoes.NumeroDiasMaximoParaAgendarHorarios;
            var dataMinimaAgendamento = Calendario.Agora(fuso);
            var dataMaximaAgendamento = Calendario.Hoje().AddDays(numeroDias + 1);
            var mensagem = String.Format(RESMensagens.RestricaoAgendamento, numeroDias);

            dataMinimaAgendamento = dataMinimaAgendamento.Date;//TODO: Ajuste emergencial > TRINKS-9145

            if (dataMinimaAgendamento > parametrosBusca.DataInicial ||
                parametrosBusca.DataFinal >= dataMaximaAgendamento)
                ValidationHelper.Instance.AdicionarItemValidacao(mensagem);
        }

        private void ValidarParametrosBusca(ParametrosBusca parametrosBusca)
        {
            if (!parametrosBusca.ServicosParaBackoffice)
            {
                foreach (var idEstabelecimento in parametrosBusca.Estabelecimentos)
                {
                    var configuracoesHotsite =
                        HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento);
                    if (configuracoesHotsite.NumeroDiasMaximoParaAgendarHorarios != PeriodoDiasEnum.Vazio)
                        ValidarLimiteDiasMaximoAgendamento(parametrosBusca, configuracoesHotsite);
                }
            }
        }

        private static void ValidarNovosUsuarios(Horario entity)
        {
            if (entity.Codigo > 0 || entity.HorarioOrigem == HorarioOrigemEnum.Balcao || entity.HorarioOrigem == HorarioOrigemEnum.GoogleReserve)
                return;

            var agendamentosCriadosHoje =
                Domain.Pessoas.HorarioRepository.CountAgendamentosNaoBalcaoCriadosNoDia(
                    entity.Cliente.PessoaFisica.IdPessoa, Calendario.Hoje());

            var configuracao = new ParametrosTrinks<string>(ParametrosTrinksEnum.qtd_limite_agendamentos_diario_para_novos_usuarios).ObterValor();
            var quantidadeLimiteDeAgendamentos = int.TryParse(configuracao, out var tempVar) ? tempVar : default(int?);

            if (quantidadeLimiteDeAgendamentos.HasValue && agendamentosCriadosHoje >= quantidadeLimiteDeAgendamentos.Value)
            {
                // conta está associada ao facebook
                var contaCliente = Domain.Pessoas.ContaRepository.ObterContaPorPessoa(entity.Cliente.PessoaFisica);
                if (!string.IsNullOrWhiteSpace(contaCliente.CodigoFacebook))
                    return;

                // existe checkout realizado para este usuário em qualquer estabelecimento do sistema
                var possuiAgendamentoPago =
                    Domain.Pessoas.HorarioRepository.Queryable().Any(f => f.Cliente == entity.Cliente && f.FoiPago);
                if (possuiAgendamentoPago)
                    return;

                // existe um agendamento/horário cuja origem seja balcão em qualquer estabelecimento do sistema
                var possuiAgendamentoBalcao =
                    Domain.Pessoas.HorarioRepository.Queryable()
                        .Any(f => f.Cliente == entity.Cliente && f.HorarioOrigem == HorarioOrigemEnum.Balcao);
                if (possuiAgendamentoBalcao)
                    return;

                var mensagemValidacao = string.Format("Para usuários novos, existe um limite de {0} agendamentos por dia.\nVocê já atingiu seu limite hoje. Esse horário não pode ser marcado.", quantidadeLimiteDeAgendamentos);
                ValidationHelper.Instance.AdicionarItemValidacao(mensagemValidacao);
            }
        }

        #endregion Validações

        #region Métodos Públicos

        [TransactionInitRequired]
        public void AlterarExibicaoProfissionaisDeFolga(int idUsuarioEstabelecimento, bool exibir)
        {
            var usuarioEstabelecimento =
                UsuarioEstabelecimentoRepository.Load(idUsuarioEstabelecimento);
            usuarioEstabelecimento.ExibeProfissionaisEmDiaDeFolga = exibir;
            UsuarioEstabelecimentoRepository.Update(usuarioEstabelecimento);
        }

        [TransactionInitRequired]
        public void AlterarStatusAgendamento(int codigoAgendamento, int codigoNovoStatus, PessoaFisica pessoaAutenticada,
            PessoaFisica pessoaQueAlterou, bool modoPAT = false, bool alteradoPorAgenda = false)
        {
            var horario = HorarioRepository.Load(codigoAgendamento);

            if (horario.Status.Codigo == codigoNovoStatus)
            {
                //ValidationHelper.Instance.AdicionarItemValidacao("O status selecionado já é o status atual.");
                return;
            }

            bool horarioEhPagamentoOnlineEhFoiCancelado = Domain.PagamentosAntecipados.CancelamentoDePagamentoOnlineAntecipadoService.HorarioEhPagamentoOnlineEhFoiCancelado(horario);

            if (horarioEhPagamentoOnlineEhFoiCancelado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível alterar o status do agendamento com pagamento online, pois ele já encontra-se cancelado. Você poderá realizar um novo agendamento, se desejar.");
                return;
            }

            horario.Status = new StatusHorario(codigoNovoStatus);

            ManterAgendamentoNoTransaction(horario, null, true, true, false, modoPAT, alteradoPorAgenda: alteradoPorAgenda);
        }

        public void AlterarConfiguracaoDeLarguraDaAgenda(Estabelecimento estabelecimento, Conta contaAutenticada, int largura)
        {
            var usuarioEstabelecimento = contaAutenticada.UsuarioEstabelecimento(estabelecimento);
            if (usuarioEstabelecimento == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O usuário precisa estar cadastrado como profissional no estabelecimento para realizar alterações no tamanho da linha e coluna da agenda.");
            }
            else
            {
                usuarioEstabelecimento.LarguraColunaAgenda = largura;
                UsuarioEstabelecimentoRepository.Update(usuarioEstabelecimento);
            }
        }

        public void AlterarConfiguracaoDeAlturaDaAgenda(Estabelecimento estabelecimento, Conta contaAutenticada, int altura)
        {
            var usuarioEstabelecimento = contaAutenticada.UsuarioEstabelecimento(estabelecimento);
            if (usuarioEstabelecimento == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O usuário precisa estar cadastrado como profissional no estabelecimento para realizar alterações no tamanho da linha e coluna da agenda.");
            }
            else
            {
                usuarioEstabelecimento.AlturaHoraInteiraAgenda = altura;
                UsuarioEstabelecimentoRepository.Update(usuarioEstabelecimento);
            }
        }

        public ResultadoBusca BuscaHorariosDisponiveisPeloCliente(ParametrosBusca parametros)
        {
            var resultado = new ResultadoBusca();

            var fuso = 0;
            if (parametros.Estabelecimentos.Count > 0)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(parametros.Estabelecimentos[0]);
                fuso = estabelecimento != null ? estabelecimento.GetFuso() : 0;
            }
            parametros.DataAtual = (parametros.DataAtual ?? Calendario.Agora()).AddHours(fuso);

            if (parametros.DataInicial.HasValue && parametros.DataInicial.Value.Date == Calendario.Agora(fuso).Date)
            {
                var dataFiltro = parametros.DataInicial.Value;
                var dataHoraAjustada = parametros.DataAtual.Value.AddMinutes(15);//TODO: Colocar tempo parametrizavel
                parametros.DataInicial = new DateTime(dataFiltro.Year, dataFiltro.Month, dataFiltro.Day, dataHoraAjustada.Hour, dataHoraAjustada.Minute, 0);
            }
            else
            {
                parametros.DataInicial = new DateTime(parametros.DataInicial.Value.Ticks).ToZeroHora();
            }

            ValidarParametrosBusca(parametros);

            if (ValidationHelper.Instance.IsValid)
                resultado = ListarHorariosDisponiveis(parametros);

            return resultado;
        }

        public ResultadoBusca BuscaHorariosDisponiveisPeloClienteMobile(ParametrosBusca parametros)
        {
            var resultado = new ResultadoBusca();

            ValidarParametrosBusca(parametros);
            if (ValidationHelper.Instance.IsValid)
                resultado = ListarHorariosDisponiveis(parametros);

            return resultado;
        }

        public IntervaloDataList DividirEmMarcos(IntervaloDataList periodosParaDividir, int tamanhoIntervalos,
            int duracaoServico, DateTime dataInicio, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var inicioDivisaoMinutos = ObterHorarioInicialMarcosDeDisponibilidade(dataInicio,
                estabelecimentoProfissional);
            var tratarAderenciaInicioMarcos =
                estabelecimentoProfissional.Estabelecimento.Hotsite().ConfiguracaoHotsiteAderencia.Codigo ==
                (int)ConfiguracaoHotsiteAderenciaEnum.Inicio_Marco;
            var retorno = new IntervaloDataList();
            retorno.AddRange(periodosParaDividir);
            retorno.Split(inicioDivisaoMinutos, tamanhoIntervalos, duracaoServico, tratarAderenciaInicioMarcos);

            return retorno;
        }

        public ParametrosBuscaHorario FiltrarHorarios(ParametrosBuscaHorario parametros)
        {
            parametros.Ausencias = PeriodoAusenciaRepository.Filtrar(parametros);
            parametros.Horarios = HorarioRepository.FiltrarAgendaDTO(parametros, false);

            return parametros;
        }

        [TransactionInitRequired]
        public void FinalizarAgendamento(int codigoAgendamento, PessoaFisica pessoaAutenticada,
            PessoaFisica pessoaQueAlterou, bool modoPAT = false, bool alteradoPorAgenda = false)
        {
            AlterarStatusAgendamento(codigoAgendamento, (int)StatusHorarioEnum.Finalizado, pessoaAutenticada, pessoaQueAlterou, modoPAT, alteradoPorAgenda);
        }

        [TransactionInitRequired]
        public void FinalizarAgendamentosDoClienteNoDia(int idClienteEstabelecimento, DateTime dataReferencia, PessoaFisica pessoaAutenticada, PessoaFisica pessoaQueAlterou)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            var agendamentosDoDia = Domain.Pessoas.HorarioRepository.ObterAgendamentosNaoPagosDoDiaPorCliente(dataReferencia, clienteEstabelecimento.Estabelecimento, clienteEstabelecimento.Cliente, paraCancelamento: false);

            foreach (var agendamento in agendamentosDoDia)
            {
                FinalizarAgendamento(agendamento.Id, pessoaAutenticada, pessoaQueAlterou);
            }
        }

        public ResultadoBusca ListarHorariosDisponiveis(ParametrosBusca parametros)
        {
            var resultado = new ResultadoBusca { ParametrosBusca = parametros };

            if (parametros.Estabelecimentos.Count > 0)
            {
                foreach (var idEstabelecimento in parametros.Estabelecimentos)
                {
                    var estabelecimento = EstabelecimentoRepository.Load(idEstabelecimento);
                    var estabelecimentoDTO = new ResultadoBusca.Estabelecimento(resultado, estabelecimento);
                    resultado.Estabelecimentos.Add(estabelecimentoDTO);

                    TratarProfissionaisServicos(parametros, estabelecimentoDTO);
                }
            }

            TratarDisponibilidade(parametros, resultado);

            if (parametros.DefinirMarcos)
            {
                foreach (var estabelecimento in resultado.Estabelecimentos)
                {
                    var configuracoesEstabelecimento =
                        Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                            estabelecimento.IdEstabelecimento);
                    var tamanhoIntervalos = configuracoesEstabelecimento.ConfiguracaoHotsiteIntervalo.IntervaloEmMinutos;

                    foreach (var servico in estabelecimento.EstabelecimentoProfissionalServicos)
                    {
                        var dataInicio = servico.IntervaloDataList.Min(f => f.Inicio) ?? new DateTime();

                        servico.IntervaloDataList = DividirEmMarcos(servico.IntervaloDataList, tamanhoIntervalos,
                            servico.DuracaoServico, dataInicio, servico.Entidade.EstabelecimentoProfissional);
                    }
                }
            }

            return resultado;
        }

        public ResultadoPaginado<Horario> ListarHorariosPaginados(ParametrosFiltroHistoricoCliente parametros)
        {
            return HorarioRepository.ListarHorariosPaginados(parametros);
        }

        [Obsolete("Este método está com muita responsabilidade. Criar métodos específicos para cada caso de uso.")]
        public void ManterAgendamentoNoTransaction(Horario entity, Transacao transacao = null, bool enviaEmail = true, bool sobreescreverStatusInicial = true, bool realizarValidacoes = true, bool modoPAT = false, bool forcarGeracaoHistorico = false, bool trataSplitDeComanda = true, bool alteradoPorAgenda = false, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false)
        {
            ManterAgendamento(entity, transacao, enviaEmail, sobreescreverStatusInicial, realizarValidacoes, modoPAT, forcarGeracaoHistorico, trataSplitDeComanda, alteradoPorAgendaCliente: alteradoPorAgenda, jaPassouPeloControleDeUsoDeProdutoAutomatico: jaPassouPeloControleDeUsoDeProdutoAutomatico);
        }

        [Obsolete("Este método está com muita responsabilidade. Criar métodos específicos para cada caso de uso.")]
        public bool ManterAgendamento(Horario entity, Transacao transacao = null, bool enviaEmail = true, bool sobreescreverStatusInicial = true,
            bool realizarValidacoes = true, bool modoPAT = false, bool forcarGeracaoHistorico = false, bool trataSplitDeComanda = true, Comanda comanda = null, bool alteradoPorAgendaCliente = false, bool ehComandaRapida = false, bool jaPassouPeloControleDeUsoDeProdutoAutomatico = false)
        {
            var fuso = 0;
            if (entity != null && entity.Estabelecimento != null)
            {
                fuso = entity.Estabelecimento.GetFuso();
            }

            var ehAgendamentoFuturo = entity.DataInicio > Calendario.Agora(fuso);
            var ehNovoAgendamento = !entity.Codigo.HasValue;
            var ehRecorrente = entity.RecorrenciaHorario != null;
            var enviarEmailAlteracaoStatus = false;
            var enviarEmailAlteracaoDados = false;
            var clienteMarcouAgendamento = false;
            var agendamentoSalvo = false;
            var alterouStatus = false;
            var valido = true;
            var statusAnterior = new StatusHorario();
            HorarioHistorico historico = null;

            if (realizarValidacoes)
            {
                ValidarNovosUsuarios(entity);
                ValidarManterAgendamento(entity);
                valido = ValidationHelper.Instance.IsValid;
            }

            if (valido)
            {
                if (trataSplitDeComanda)
                    TratarComandaDuranteEdicaoDeHorario(entity);

                var configuracoesEstabelecimento =
                    Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(
                        entity.Estabelecimento.IdEstabelecimento);
                if (!entity.Codigo.HasValue)
                {
                    entity.AjustarDataFimPelaDuracao();
                    if (entity.Estabelecimento.DataPrimeiroAgendamento == null)
                    {
                        entity.Estabelecimento.DataPrimeiroAgendamento = Calendario.Agora();
                    }

                    var clienteEstabelecimento =
                        Domain.Pessoas.ClienteEstabelecimentoService.ObterClienteEstabelecimento(
                            entity.Cliente.IdCliente, entity.Estabelecimento.IdEstabelecimento);

                    if (clienteEstabelecimento == null)
                    {
                        clienteEstabelecimento = new ClienteEstabelecimento
                        {
                            Cliente = entity.Cliente,
                            Estabelecimento = entity.Estabelecimento,
                            Ativo = true,
                            PrimeiroAgendamento = entity
                        };

                        Domain.Pessoas.ClienteEstabelecimentoService.ManterCliente(clienteEstabelecimento);
                    }
                    else
                    {
                        clienteEstabelecimento.Ativo = true;
                        if (clienteEstabelecimento.PrimeiroAgendamento == null)
                        {
                            clienteEstabelecimento.PrimeiroAgendamento = entity;
                        }
                        else if (clienteEstabelecimento.PrimeiroAgendamento.DataInicio > entity.DataInicio)
                        {
                            clienteEstabelecimento.PrimeiroAgendamento = entity;
                        }

                        //foreach (var telefone in clienteEstabelecimento.TelefonesProprios)
                        //    telefone.Ativo = true;

                        clienteEstabelecimento.UpdateAndFlush();
                    }

                    //Status inicial
                    if (sobreescreverStatusInicial &&
                        (entity.Status != StatusHorarioEnum.Finalizado &&
                         entity.HorarioOrigem != HorarioOrigemEnum.Balcao &&
                         entity.HorarioOrigem != HorarioOrigemEnum.GoogleReserve))
                        entity.Status = configuracoesEstabelecimento.StatusPadraoAgendamentoWeb;

                    //Verifica se o cliente que criou o agendamento, para enviar o email com os dados
                    if (entity.Historicos.Count > 0 && entity.PessoaQuemMarcou != null)
                        clienteMarcouAgendamento = entity.HorarioOrigem != HorarioOrigemEnum.Balcao;
                }
                else
                {
                    var ultimoHistorico = Domain.Pessoas.HorarioHistoricoRepository.ObterUltimoHistoricoDoHorario(entity);

                    if (ultimoHistorico != null)
                    {
                        statusAnterior = ultimoHistorico.Status;

                        alterouStatus = statusAnterior != null && entity.Status.Codigo != statusAnterior.Codigo;
                        enviarEmailAlteracaoStatus = alterouStatus;
                        enviarEmailAlteracaoStatus &= (ehAgendamentoFuturo ||
                                                       entity.Status == StatusHorarioEnum.Cliente_Faltou);

                        if (ehAgendamentoFuturo)
                        {
                            enviarEmailAlteracaoDados = entity.ServicoEstabelecimento.IdServicoEstabelecimento !=
                                                        ultimoHistorico.ServicoEstabelecimento
                                                            .IdServicoEstabelecimento;

                            var exibirNomeProfissionalNosEmails = entity.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirNomeProfissionalEmEmailESms;
                            if (exibirNomeProfissionalNosEmails)
                            {
                                enviarEmailAlteracaoDados |= (
                                                                (entity.Profissional == null && ultimoHistorico.Profissional != entity.Profissional)
                                                                ||
                                                                (ultimoHistorico.Profissional == null && ultimoHistorico.Profissional != entity.Profissional)
                                                                ||
                                                                (entity.Profissional != null
                                                                && ultimoHistorico.Profissional != null
                                                                && entity.Profissional.IdProfissional != ultimoHistorico.Profissional.IdProfissional
                                                                )
                                                              );
                            }
                            enviarEmailAlteracaoDados |= entity.DataInicio != ultimoHistorico.DataInicio;

                            historico = enviarEmailAlteracaoDados ? ultimoHistorico : null;
                        }
                    }
                }

                DadosDoControleDeAlteracaoDoHorarioDTO dadosAnteriores = entity.Codigo > 0 ?
                    Domain.Pessoas.HorarioRepository.ObterDadosVerificadosNoControleDeAlteracaoDeUmHorario(entity.Codigo.Value) :
                    null;

                if (clienteMarcouAgendamento)
                {
                    agendamentoSalvo = ControleConcorrenciaAgendamento.TratarConcorrenciaEmPersistencia(entity);
                }
                else
                {
                    Domain.Pessoas.HorarioService.ManterHorario(entity, transacao: transacao, modoPAT: modoPAT, forcarGeracaoHistorico: forcarGeracaoHistorico, comanda: comanda, alteradoPorAgendaCliente: alteradoPorAgendaCliente, ehComandaRapida: ehComandaRapida, jaPassouPeloControleDeUsoDeProdutoAutomatico: jaPassouPeloControleDeUsoDeProdutoAutomatico);
                    agendamentoSalvo = true;
                }

                var preVendaServico = Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(entity.Id);
                if (preVendaServico != null && entity.Profissional != null)
                    preVendaServico.EstabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorProfissionalEEstabelecimento(entity.Profissional.IdProfissional, entity.Estabelecimento.IdEstabelecimento);

                if (alterouStatus &&
                    (entity.Status == StatusHorarioEnum.Cliente_Faltou || entity.Status == StatusHorarioEnum.Cancelado))
                {
                    if (preVendaServico != null && preVendaServico.Comanda != null)
                    {
                        var numeroComanda = preVendaServico.Comanda.Numero;
                        Domain.Vendas.PreVendaService.ExcluirPreVenda(preVendaServico);

                        Domain.Vendas.ComandaService.VerificarEFecharComanda(numeroComanda, entity.Estabelecimento.IdEstabelecimento);
                    }
                }

                if (agendamentoSalvo)
                {
                    if (enviaEmail && entity.Profissional != null)
                    {
                        EnviarEmailDeAgendamentos(entity, ehNovoAgendamento, ehRecorrente, enviarEmailAlteracaoStatus, enviarEmailAlteracaoDados, statusAnterior, historico);
                    }

                    try
                    {
                        ProgramarEnvioDeNotificacaoParaOsProfissionais(entity, ehNovoAgendamento);
                    }
                    catch (Exception e)
                    {
                    }

                    // O envio de e-mail para cancelamento é feito fora deste método. Ainda é necessário verificar
                    // se existe profissional para o agendamento, sinalizando que não se trata de um agendamento
                    // para fila de espera.
                }
                DefinirPrimeiroAgendamentoDoClienteSeNaoHouver(entity);
            }
            else
                HorarioRepository.Clear();

            return agendamentoSalvo;
        }

        private static void GravarErroElmah(Exception e)
        {
            var erro = e;
            if (e is AggregateException)
                erro = ((AggregateException)e).InnerException;

            ErrorSignal.FromContext(HttpContext.Current).Raise(erro, HttpContext.Current);
        }

        private void ProgramarEnvioDeNotificacaoParaOsProfissionais(Horario agendamento, bool ehNovoAgendamento)
        {
            if (ehNovoAgendamento == null) return;

            if (agendamento.Status.Codigo == (int)StatusHorarioEnum.Em_Andamento)
            {
                Domain.Pessoas.NotificacoesDoAgendamentoService.ProgramarEnvioDeNotificacaoDeClienteChegou(agendamento);
            }
            else if (agendamento.Status.Codigo == (int)StatusHorarioEnum.Aguardando_Confirmacao)
            {
                Domain.Pessoas.NotificacoesDoAgendamentoService.ProgramarEnvioDeNotificacaoDeNovoAgendamentoAguardandoConfirmacao(agendamento);
            }
            else if (agendamento.Status.Codigo == (int)StatusHorarioEnum.Cancelado)
            {
                var historico = Domain.Pessoas.HorarioHistoricoRepository.ObterUltimoHistoricoDoHorario(agendamento);
                if (historico != null && historico.HorarioQuemCancelou.Codigo != (int)HorarioQuemCancelouEnum.Profissional)
                {
                    Domain.Pessoas.NotificacoesDoAgendamentoService.ProgramarEnvioDeNotificacaoDeAgendamentoCanceladoQueNaoSejaPeloProprioProfissional(agendamento);
                }
            }
        }

        [TransactionInitRequired]
        public void ManterAgendamentoEmLote(List<Horario> agendamentos, bool enviarNotificacaoCliente)
        {
            var fuso = 0;
            if (agendamentos != null && agendamentos[0].Estabelecimento != null)
            {
                fuso = agendamentos[0].Estabelecimento.GetFuso();
            }

            if (enviarNotificacaoCliente)
            {
                var existemAgendamentosFuturos = agendamentos.Any(f => f.DataInicio >= Calendario.Agora(fuso));

                if (existemAgendamentosFuturos)
                    EnvioEmailService.EnviarEmailManterAgendamentoEmLote(agendamentos);
            }

            foreach (var agendamento in agendamentos)
                ManterAgendamentoNoTransaction(agendamento, null, false);
        }

        [TransactionInitRequired]
        public void ManterAgendamentosDoDia(List<Horario> agendamentos)
        {
            foreach (var agendamento in agendamentos)
                ManterAgendamentoNoTransaction(agendamento, null, false);

            EnvioDeNotificacoesService.EnviarNotificacaoDeAgendamentoCancelado(agendamentos, new ResultadoCancelamentoDePagamentoAntecipado());
        }

        [TransactionInitRequired]
        public void ManterCancelamentoDeAgendamentosEmBloco(List<Horario> lista)
        {
            foreach (var entity in lista)
            {
                Domain.Pessoas.HorarioService.ManterHorario(entity);
            }

            EnvioDeNotificacoesService.EnviarNotificacaoDeCancelamentoDeAgendamentoRecorrente(lista);
            //EnvioEmailService.EnviarEmailCancelamentoDeRecorrenteMarcado(lista);
        }

        [TransactionInitRequired]
        public void RemoverAgendamento(int codigoAgendamento, PessoaFisica pessoaAutenticada,
            PessoaFisica pessoaQueAlterou)
        {
            var horario = HorarioRepository.Load(codigoAgendamento);
            horario.Ativo = false;
            var horarioHistory = horario.ToHorarioHistorico(pessoaAutenticada, pessoaQueAlterou);
            HorarioHistoricoRepository.SaveNew(horarioHistory);
        }

        public bool VerificarDisponibilidadeDoHorario(ParametrosBuscaHorario parametros)
        {
            var duracaoServico = parametros.ServicoSelecionadas[0].Duracao;
            var dataFim = parametros.DataInicial.AddMinutes(duracaoServico);
            var intervaloDisponibilidade = new IntervaloData(parametros.DataInicial, dataFim);

            var codigoProfissional = parametros.ProfissionaisSelecionados[0];
            var estabelecimentoProfissional =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(codigoProfissional,
                    parametros.IdEstabelecimento);

            return estabelecimentoProfissional.VerificarDisponibilidade(intervaloDisponibilidade);
        }

        private bool HorarioEstaVago(Horario horario)
        {
            int idProfissional = horario.Profissional.IdProfissional;
            int idEstabelecimento = horario.Estabelecimento.IdEstabelecimento;
            DateTime dataInicio = horario.DataInicio;
            DateTime dataFim = horario.DataFim;

            var mensagensConflito = ObterMensagensDeConflitoDeAgendamentoComProfissional(idProfissional, idEstabelecimento, dataInicio, dataFim, horario.Codigo);

            return !mensagensConflito.Any();
        }

        public List<string> ObterMensagensDeConflitoDeAgendamentoComProfissional(int idProfissional, int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int? idHorarioSendoEditado)
        {
            var mensagens = new List<string>();

            var horarioDentroPeriodoTrabalho = true;
            var horarioDentroPeriodoTrabalhoProfissional = true;
            var horarioContidoPeriodoAusencia = Domain.Pessoas.PeriodoAusenciaService.EstaNoPeriodoDeAusenciaDoProfissional(idProfissional, idEstabelecimento, dataInicio, dataFim);
            var horarioComConflitoDeHorarios = Domain.Pessoas.AgendaService.ExisteConflitoDeHorario(idProfissional, idEstabelecimento, dataInicio, dataFim, idHorarioSendoEditado);

            var horariosEspeciais = Domain.Pessoas.EstabelecimentoHorarioEspecialFuncionamentoRepository.ObterHorariosEspeciaisPeriodo(idEstabelecimento, dataInicio, dataFim);

            if (horariosEspeciais.Count == 0)
            {
                horarioDentroPeriodoTrabalho = Domain.Pessoas.HorarioService.HorarioDentroPeriodoTrabalhoEstabelecimento(dataInicio, dataFim, idEstabelecimento);
                horarioDentroPeriodoTrabalhoProfissional = Domain.Pessoas.HorarioService.HorarioDentroPeriodoTrabalhoProfissional(dataInicio, dataFim, idProfissional, idEstabelecimento);
            }

            var horarioDentroPeriodoHorarioEspecial = horariosEspeciais.Any();

            if (!horarioDentroPeriodoTrabalho
                || horarioContidoPeriodoAusencia
                || !horarioDentroPeriodoTrabalhoProfissional
                || !horarioDentroPeriodoHorarioEspecial
                || horarioComConflitoDeHorarios)
            {
                if (horarioContidoPeriodoAusencia)
                {
                    mensagens.Add(RESMensagens.ConfirmarAgendamentoEmPeriodoDeAusencia);
                }

                if (!horarioDentroPeriodoTrabalhoProfissional)
                {
                    mensagens.Add(RESMensagens.ConfirmarAgendamentoForaHorarioTrabalhoProfissional);
                }

                if (!horarioDentroPeriodoTrabalho)
                {
                    mensagens.Add(RESMensagens.ConfirmarAgendamentoForaHorarioTrabalho);
                }

                if (horarioComConflitoDeHorarios)
                {
                    mensagens.Add(RESMensagens.ConfirmarAgendamentoComConflitoDeHorario);
                }

                if (horarioDentroPeriodoHorarioEspecial)
                {
                    mensagens.Add(RESMensagens.ConfirmarAgendamentoForaHorarioEspecial);
                }
            }

            return mensagens;
        }

        private static void TratarComandaDuranteEdicaoDeHorario(Horario entity)
        {
            var codigo = entity.Codigo;
            if ((codigo ?? 0) == 0)
                return;

            var idEstabelecimento = entity.Estabelecimento.IdEstabelecimento;
            var cliente = entity.Cliente;
            var idPessoaCliente = cliente.PessoaFisica.IdPessoa;

            var prevenda = Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(codigo ?? 0);
            if (prevenda != null && prevenda.Comanda != null)
            {
                var comanda = prevenda.Comanda;
                var precisaDesvincularComandaPorTrocaDeCliente = Domain.Vendas.ComandaService.PrecisaDesvincularHorarioDaComandaAposTrocaCliente(codigo ?? 0, cliente.IdCliente);
                var precisaDesvincularComandaPorReagendamento = Domain.Vendas.ComandaService.PrecisaDesvincularHorarioDaComandaPorReagendamento(codigo ?? 0, entity.DataInicio);
                if (precisaDesvincularComandaPorTrocaDeCliente || precisaDesvincularComandaPorReagendamento)
                {
                    Domain.Vendas.PreVendaService.ExcluirPreVenda(prevenda, foiReagendado: precisaDesvincularComandaPorReagendamento);
                    entity.PreVendas.Clear();
                }
                else
                {
                    Domain.Vendas.ComandaService.TransferirComanda(prevenda.Comanda.Id, idPessoaCliente);
                }

                Domain.Vendas.ComandaService.VerificarEFecharComanda(comanda.Numero, idEstabelecimento);
            }
        }

        private void AlterarAgendamentosDoDiaParaOClienteParaEmAtendimento(Horario horarioBase)
        {
            var horarios = Domain.Pessoas.HorarioRepository.Filtrar(new ParametrosBuscaHorario
            {
                DataInicial = horarioBase.DataInicio.Date,
                DataFinal =
                    new DateTime(horarioBase.DataFim.Year, horarioBase.DataFim.Month, horarioBase.DataFim.Day, 23, 59,
                        59),
                IdEstabelecimentoCliente = horarioBase.ClienteEstabelecimento.Codigo,
                StatusSelecionados = new List<StatusHorario> { new StatusHorario((int)StatusHorarioEnum.Confirmado) }
            });

            var statusEmAtendimento = new StatusHorario((int)StatusHorarioEnum.Em_Andamento);

            foreach (var horario in horarios)
            {
                horario.Status = statusEmAtendimento;
                ManterAgendamento(horario, null, false);
            }
        }

        private void EnviarEmailDeAgendamentos(Horario entity, bool ehNovoAgendamento, bool ehRecorrente, bool enviarEmailAlteracaoStatus, bool enviarEmailAlteracaoDados, StatusHorario statusAnterior, HorarioHistorico historico)
        {
            if (enviarEmailAlteracaoStatus && !enviarEmailAlteracaoDados)
            {
                // Em caso de cancelamento feito pelo próprio cliente, o método abaixo será executado.
                // Neste primeiro momento, o EnvioDeNotificacoesService não será chamado.
                // Desta forma, não é necessário verificar se o cancelamento foi feito pelo próprio usuário.
                // Para o envio de notificações de cancelamento com este outro serviço, apenas o método
                // chamado a partir do BackOffice foi modificado.

                EnvioEmailService.EnviarEmailAlteracaoStatusAgendamento(entity, statusAnterior);
            }
            else if (enviarEmailAlteracaoDados)
            {
                EnvioDeNotificacoesService.EnviarNotificacaoAlteracaoAgendamento(entity, historico);
                //EnvioEmailService.EnviarEmailAlteracaoDadosAgendamento(entity, historico);
            }

            var fuso = 0;
            if (entity != null && entity.Estabelecimento != null)
            {
                fuso = entity.Estabelecimento.GetFuso();
            }

            var ehAgendamentoPassado = entity.DataInicio < Calendario.Agora(fuso);

            if (ehNovoAgendamento && !ehRecorrente && !ehAgendamentoPassado)
                EnvioDeNotificacoesService.EnviarNotificacaoDeAgendamentoMarcado(entity);

            var codigoStatusEmAtendimento = (int)StatusHorarioEnum.Em_Andamento;
            if (statusAnterior != null && statusAnterior.Codigo != codigoStatusEmAtendimento &&
                entity.Status.Codigo == codigoStatusEmAtendimento)
            {
                AlterarAgendamentosDoDiaParaOClienteParaEmAtendimento(entity);
            }
        }

        public bool AlterarDuracaoDoAgendamento(Horario entity, DateTime novaDataHoraFim)
        {
            entity.DataFim = novaDataHoraFim;
            entity.Duracao = (int)(entity.DataFim - entity.DataInicio).TotalMinutes;
            return ManterAgendamento(entity);
        }

        #endregion Métodos Públicos

        #region Métodos Privados

        //private void LimitarDataFimPorConfiguracaoDoEstabelecimento(ParametrosBusca parametrosBusca) {
        //    if (!parametrosBusca.ServicosParaBackoffice) {
        //        foreach (var idEstabelecimento in parametrosBusca.Estabelecimentos) {
        //            var configuracoesHotsite =
        //                HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento);
        //            if (configuracoesHotsite.NumeroDiasMaximoParaAgendarHorarios != PeriodoDiasEnum.Vazio) {
        //                    var fuso = 0;
        //                    if (parametrosBusca.Estabelecimentos.Count > 0) {
        //                        var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(parametrosBusca.Estabelecimentos[0]);
        //                        fuso = estabelecimento != null ? estabelecimento.GetFuso() : 0;
        //                    }

        //                    var numeroDias = (int)configuracoes.NumeroDiasMaximoParaAgendarHorarios;
        //                    var dataMinimaAgendamento = Calendario.Agora(fuso);
        //                    var dataMaximaAgendamento = Calendario.Hoje().AddDays(numeroDias + 1);
        //                    var mensagem = String.Format(RESMensagens.RestricaoAgendamento, numeroDias);

        //                    dataMinimaAgendamento = dataMinimaAgendamento.Date;//TODO: Ajuste emergencial > TRINKS-9145

        //                    if (dataMinimaAgendamento > parametrosBusca.DataInicial ||
        //                        parametrosBusca.DataFinal >= dataMaximaAgendamento)
        //                        ValidationHelper.Instance.AdicionarItemValidacao(mensagem);

        //            }
        //        }
        //    }
        //}
        private DateTime ObterHorarioInicialMarcosDeDisponibilidade(DateTime dataInicio,
            EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var estabelecimento = estabelecimentoProfissional.Estabelecimento;
            var configInicioMarcos =
                (ConfiguracaoHotsiteInicioMarcosEnum)estabelecimento.Hotsite().ConfiguracaoHotsiteInicioMarcos.Codigo;

            switch (configInicioMarcos)
            {
                case ConfiguracaoHotsiteInicioMarcosEnum.Iniciar_Zero_Horas:
                    return new DateTime(dataInicio.Year, dataInicio.Month, dataInicio.Day);

                case ConfiguracaoHotsiteInicioMarcosEnum.Iniciar_Abertura_Estabelecimento:
                    var aberturaEstabelecimento = estabelecimento.ObterHorarioDeFuncionamentoReal(dataInicio)[0].Inicio;
                    if (aberturaEstabelecimento.HasValue)
                        dataInicio = aberturaEstabelecimento.Value;
                    return new DateTime(dataInicio.Year, dataInicio.Month, dataInicio.Day, dataInicio.Hour,
                        dataInicio.Minute, dataInicio.Second);

                case ConfiguracaoHotsiteInicioMarcosEnum.Iniciar_Horario_Trabalho_Profissional:
                    var horarioTrabalho = Domain.Pessoas.HorarioTrabalhoRepository.ObterPorDiaDeSemana(dataInicio.DayOfWeek, estabelecimentoProfissional);

                    var inicioExpediente = Domain.Pessoas.EstabelecimentoProfissionalService.ObterHorarioDeTrabalhoConsiderandoHorarioEspecial(estabelecimento, dataInicio, horarioTrabalho).Select(p => p.Inicio).FirstOrDefault();
                    var inicioLiberacaoAgenda = ObterHorarioInicialDoDiaComLiberacaoDeAgenda(dataInicio, estabelecimentoProfissional);

                    if (inicioLiberacaoAgenda.HasValue && inicioExpediente.HasValue && inicioLiberacaoAgenda < inicioExpediente)
                        dataInicio = inicioLiberacaoAgenda.Value;
                    else if (inicioLiberacaoAgenda.HasValue && !inicioExpediente.HasValue)
                        dataInicio = inicioLiberacaoAgenda.Value;
                    else if (inicioExpediente.HasValue)
                        dataInicio = inicioExpediente.Value;

                    return new DateTime(dataInicio.Year, dataInicio.Month, dataInicio.Day, dataInicio.Hour,
                        dataInicio.Minute, dataInicio.Second);
            }

            return new DateTime();
        }

        private DateTime? ObterHorarioInicialDoDiaComLiberacaoDeAgenda(DateTime dataInicio, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var intervalosLiberacoesAgenda = Domain.ProfissionalAgenda.LiberacaoDeHorarioNaAgendaRepository
                .ListarDisponibilidadesDasLiberacoesAtivasNoDiaPorProfissional(estabelecimentoProfissional.Codigo, dataInicio)
                .Select(p => p.ObterIntervaloConsiderandoData(dataInicio))
                .ToList();

            if (!intervalosLiberacoesAgenda.Any())
                return null;

            return intervalosLiberacoesAgenda.Min(i => i.Inicio.Value);
        }

        private void TratarDisponibilidade(ParametrosBusca parametros, ResultadoBusca resultado)
        {
            if (parametros.DataInicial == null || parametros.DataFinal == null)
                return;
            var intervalo = new IntervaloData(parametros.DataInicial.Value, parametros.DataFinal.Value);
            //loop estabelecimento
            foreach (var estabelecimento in resultado.Estabelecimentos)
            {
                //estabelecimento
                estabelecimento.IntervaloDataList = estabelecimento.Entidade.ListarDisponibilidade(intervalo);
                var listaRevisada = new List<ResultadoBusca.EstabelecimentoProfissionalServico>();
                foreach (
                    var estabelecimentoProfissionalServico in
                        estabelecimento.EstabelecimentoProfissionalServicos.Where(
                            estabelecimentoProfissionalServico =>
                                estabelecimentoProfissionalServico.Entidade.ListarDisponibilidade(intervalo).Count > 0))
                {
                    estabelecimentoProfissionalServico.IntervaloDataList =
                        estabelecimentoProfissionalServico.Entidade.ListarDisponibilidade(intervalo);
                    listaRevisada.Add(estabelecimentoProfissionalServico);
                }
                estabelecimento.EstabelecimentoProfissionalServicos = listaRevisada;
            }
        }

        private void TratarProfissionaisServicos(ParametrosBusca parametros,
            ResultadoBusca.Estabelecimento estabelecimentoDTO)
        {
            //if (parametros.Profissionais.Count <= 0 && parametros.ServicosEstabelecimento.Count <= 0)
            //    return;
            var estabelecimentoProfissionalServicos =
                parametros.ServicosParaBackoffice
                    ? EstabelecimentoProfissionalServicoRepository.ListarPorEstabelecimentoProfissionalServicoAtivos(
                        estabelecimentoDTO.IdEstabelecimento, parametros.Profissionais,
                        parametros.ServicosEstabelecimento)
                    : EstabelecimentoProfissionalServicoRepository.ListarPorEstabelecimentoProfissionalServicoHotsite(
                        estabelecimentoDTO.IdEstabelecimento, parametros.Profissionais,
                        parametros.ServicosEstabelecimento);

            foreach (
                var estabelecimentoProfissionalServicoDTO in
                    estabelecimentoProfissionalServicos.Select(
                        estabelecimentoProfissionalServico =>
                            new ResultadoBusca.EstabelecimentoProfissionalServico(estabelecimentoDTO,
                                estabelecimentoProfissionalServico)))
            {
                estabelecimentoDTO.EstabelecimentoProfissionalServicos.Add(estabelecimentoProfissionalServicoDTO);
            }
        }

        public bool ValidarSeHorarioFoiPagadoAntecipadamente(Horario horario)
        {
            var foiPagamentoAntecipado = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.HorarioFoiPagadoAntecipadamento(horario);
            return foiPagamentoAntecipado;
        }

        public bool ValidarSeHorarioPodeSerReagendado(Horario horario)
        {
            var foiPagamentoAntecipado = ValidarSeHorarioFoiPagadoAntecipadamente(horario);
            if (foiPagamentoAntecipado)
                ValidationHelper.Instance.AdicionarItemValidacao("Os reagendamentos de serviços que já foram pagos online são feitos por ligação para o seu salão de beleza! Entre em contato agora.");
            return foiPagamentoAntecipado;
        }

        public bool ValidarSeHorarioPodeSerCancelado(Horario horario)
        {
            var foiPagamentoAntecipado = ValidarSeHorarioFoiPagadoAntecipadamente(horario);
            if (foiPagamentoAntecipado)
                ValidationHelper.Instance.AdicionarItemValidacao("Os cancelamentos de serviços que já foram pagos online são feitos por ligação para o seu salão de beleza! Entre em contato agora.");
            return foiPagamentoAntecipado;
        }

        public void ValidarManterAgendamento(Horario entity)
        {
            //if (HttpContext.Current != null && !HttpContext.Current.User.IsInRole(Permissao.Ignora_Valor_Maximo_Servicos)) {
            //	var valorMaximoParaServicos = entity.Estabelecimento.EstabelecimentoConfiguracaoGeral.ValorMaximoParaServicos;
            //	if (valorMaximoParaServicos.HasValue && entity.Valor > valorMaximoParaServicos) {
            //		ValidationHelper.Instance.AdicionarItemValidacao("O valor dos serviços não podem ultrapassar " + valorMaximoParaServicos.Value.ObterValorEmReais());
            //	}
            //}
            bool ehNovoAgendamento = !entity.Codigo.HasValue;
            if (!ehNovoAgendamento && entity.FoiPagoAntecipadamente)
                ValidarSeHorarioPagoOnlineMudouADataParaUmDiaComValorDiferente(entity.Id, entity.DataInicio.Date);

            if (entity.ServicoEstabelecimento == null || entity.ServicoEstabelecimento.IdServicoEstabelecimento == 0)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    RESMensagens.OServicoEhObrigatorioParaSalvarOAgendamento);

            if (entity.FoiPago)
                ValidationHelper.Instance.AdicionarItemValidacao(
                    RESMensagens.NaoEPossivelRealizarAlteracoesEmUmAgendamentoQueJaFoiPago);

            if (ehNovoAgendamento && ContextHelper.Instance.IdConta.HasValue)
            {
                var contaAutenticada = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value);
                var ehProfissionalDoEstabelecimento =
                    Domain.Pessoas.EstabelecimentoProfissionalRepository.EhProfissionalDoEstabelecimento(contaAutenticada.Pessoa.IdPessoa, entity.Estabelecimento.IdEstabelecimento);

                if (!entity.ClienteEstabelecimento.PodeAgendarOnlineNoEstabelecimento && !ehProfissionalDoEstabelecimento)
                    ValidationHelper.Instance.AdicionarItemValidacao(
                    RESMensagens.AgendamentoNaoRealizadoVoceNaoEstaAutorizadoMarcarHoraNoEstabelecimento);
            }

            if (entity.HorarioOrigem != HorarioOrigemEnum.Balcao && (!entity.Codigo.HasValue || entity.Codigo == 0))
            {
                if (entity.Profissional != null)
                {
                    var estabelecimentoProfissionalServico =
                        Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.ObterPorEstabelecimentoProfissionalServico
                            (entity.Estabelecimento.IdEstabelecimento, entity.Profissional.IdProfissional, entity.ServicoEstabelecimento.IdServicoEstabelecimento);

                    if (estabelecimentoProfissionalServico != null && !estabelecimentoProfissionalServico.PermiteAgendamentoHotsite)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao(
                                            "Este serviço não premite agendamento online");
                    }
                }

                var estabelecimentoConfiguracaoHotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(entity.Estabelecimento.IdEstabelecimento);
                if (!estabelecimentoConfiguracaoHotsite.PermiteAgendamentoHotsite)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "Este estabelecimento não permite agendamento online");
                }

                var horarioEstaVago = HorarioEstaVago(entity);
                if (!horarioEstaVago)
                    ValidationHelper.Instance.AdicionarItemValidacao(RESMensagens.EsteHorarioNaoSeEncontraMaisDisponivel);
            }

            var exibirDadosAPartirDe = entity.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;
            if (exibirDadosAPartirDe.HasValue && exibirDadosAPartirDe.Value > entity.DataInicio)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    string.Format(RESMensagens.AgendamentosAPartirDe, exibirDadosAPartirDe.Value));
            }

            if (entity.DataInicio >= entity.DataFim || entity.Duracao <= 0)
                ValidationHelper.Instance.AdicionarItemValidacao("O horário deve ter duração de pelo menos 1 minuto.");
        }

        public void ValidarSeHorarioPagoOnlineMudouADataParaUmDiaComValorDiferente(int idHorario, DateTime novaDataInicio)
        {
            var horario = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(h => h.Id == idHorario)
                .Select(h => new
                {
                    FoiPagoAntecipadamente = h.FoiPagoAntecipadamente,
                    DataInicio = h.DataInicio,
                    Valor = h.Valor,
                    IdServicoEstabelecimento = h.ServicoEstabelecimento.IdServicoEstabelecimento
                })
                .FirstOrDefault();

            if (horario != null && horario.FoiPagoAntecipadamente)
            {
                bool reagendouParaOutroDia = horario.DataInicio.Date != novaDataInicio.Date;

                if (reagendouParaOutroDia)
                {
                    var dadosServicoParaOAgendamentoDTO = ObterDadosDoServicoParaUmAgendamento(horario.IdServicoEstabelecimento, novaDataInicio);
                    bool servicoTemPrecoDiferenciadoNoDia = horario.Valor != dadosServicoParaOAgendamentoDTO.Preco;

                    if (servicoTemPrecoDiferenciadoNoDia)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("O serviço não poderá ser reagendado para data selecionada, pois o valor do serviço será alterado.\n É necessário cancelar o agendamento atual e o valor revertido em crédito para o cliente poderá ser usado para o novo agendamento.");
                    }
                }
            }
        }

        public DadosServicoParaOAgendamentoDTO ObterDadosDoServicoParaUmAgendamento(int idServicoEstabelecimento, DateTime? dataParaAgendamento = null)
        {
            var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(idServicoEstabelecimento);
            Promocoes.PromocaoDiaDaSemana promocaoDia = null;
            var data = dataParaAgendamento.Value;

            if (servicoEstabelecimento.Promocao != null)
            {
                if (servicoEstabelecimento.Promocao.TipoVigencia == TipoPromocaoVigenciaEnum.Sempre.GetHashCode() ||
                    (servicoEstabelecimento.Promocao.TipoVigencia == TipoPromocaoVigenciaEnum.PorPeriodo.GetHashCode() &&
                            (!servicoEstabelecimento.Promocao.DataInicio.HasValue || servicoEstabelecimento.Promocao.DataInicio <= data.Date) &&
                                    (!servicoEstabelecimento.Promocao.DataFim.HasValue || servicoEstabelecimento.Promocao.DataFim >= data.Date)))
                {
                    var diasPromocao = Domain.Promocoes.PromocaoDiaDaSemanaRepository.ObterListaPromocaoDiaDaSemana(servicoEstabelecimento.Promocao.IdPromocao);
                    promocaoDia = diasPromocao.Where(a => a.Valor.HasValue && a.DiaDaSemana == data.DayOfWeek.GetHashCode()).FirstOrDefault();
                }
            }
            var promocao = promocaoDia != null ? promocaoDia.Valor.Value : servicoEstabelecimento.Preco;
            return new DadosServicoParaOAgendamentoDTO
            {
                IdServicoEstabelecimento = servicoEstabelecimento.IdServicoEstabelecimento,
                Nome = servicoEstabelecimento.Nome,
                Duracao = servicoEstabelecimento.Duracao,
                Preco = promocao,
                TipoPreco = servicoEstabelecimento.TipoPreco
            };
        }

        #endregion Métodos Privados

        public void CancelarHorario(int idHorario, string motivo, HorarioQuemCancelouEnum quemCancelou, PessoaFisica pessoaQueAlterou = null)
        {
            var entity = Domain.Pessoas.HorarioRepository.Load(idHorario);

            if (entity.Status == StatusHorarioEnum.Cancelado)
                return;

            if (quemCancelou == HorarioQuemCancelouEnum.Cliente && pessoaQueAlterou == null)
            {
                pessoaQueAlterou = entity.Cliente.PessoaFisica;
            }
            else if (pessoaQueAlterou == null)
            {
                throw new Exception("É necessário definir PessoaQueAlterou");
            }

            entity.Status = StatusHorarioEnum.Cancelado;

            var historico = new HorarioHistorico(entity, pessoaQueRealizou: pessoaQueAlterou)
            {
                Status = StatusHorarioEnum.Cancelado,
                HorarioQuemCancelou = quemCancelou,
                MotivoCancelamento = motivo
            };

            entity.Historicos.Add(historico);

            Domain.Pessoas.HorarioRepository.Update(entity);
            Domain.Pessoas.HorarioHistoricoRepository.Update(historico);
        }

        [TransactionInitRequired]
        public async Task CancelarHorarios(List<int> idsHorarios, string motivo, HorarioQuemCancelou quemCancelou, PessoaFisica pessoaQueAlterou, AreasTrinksEnum areaDoTrinks, bool ehUmEstornoDePagamentoOnline)
        {
            var horarios = Domain.Pessoas.HorarioRepository.ObterPorIds(idsHorarios);

            bool existeHorarioPagoOnline = Domain.PagamentosAntecipados.CancelamentoDePagamentoOnlineAntecipadoService.ExisteHorarioPagoOnline(idsHorarios);
            if (existeHorarioPagoOnline)
                ValidarCancelamentoDeAgendamentosPagosOnlineAntecipadamente(idsHorarios);

            if (areaDoTrinks == AreasTrinksEnum.Backoffice)
                VerificarSeAgendamentosCompoemComandaEConfirmarDesassociacaoComOUsuario(idsHorarios);

            var resultadoPagamentoAntecipado = new ResultadoCancelamentoDePagamentoAntecipado();

            if (!ValidationHelper.Instance.IsValid || ValidationHelper.Instance.TemPendenciaDeConfirmacao())
                return;

            foreach (var horario in horarios)
            {
                decimal? valorPagoAntecipadamente = ObterValorPagoAntecipadamenteCasoTenha(horario);
                horario.FicarComoCancelado(quemCancelou, motivo, valorPagoAntecipadamente);
                Domain.Pessoas.HorarioService.ManterHorario(horario, persistirHistorico: false);
            }

            foreach (var horario in horarios)
            {
                var preVendaServico = Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(horario.Id);

                if (preVendaServico != null && preVendaServico.Comanda != null)
                {
                    var numeroComanda = preVendaServico.Comanda.Numero;

                    preVendaServico.DefinirComoExcluida();
                    Domain.Vendas.PreVendaRepository.Update(preVendaServico);

                    Domain.Vendas.PreVendaService.GerarHistoricoDePrevenda(preVendaServico, pessoaQueAlterou, "Pré-venda excluída");

                    Domain.Vendas.ComandaService.VerificarEFecharComanda(numeroComanda, horario.Estabelecimento.IdEstabelecimento);
                }
            }

            if (existeHorarioPagoOnline)
                resultadoPagamentoAntecipado = await EstornarOuTransformarEmCreditoClienteOPagamentoOnlineDosHorarios(idsHorarios, motivo, pessoaQueAlterou, ehUmEstornoDePagamentoOnline);

            //Verificar como vamos isolar a notificação, para caso der erro não impacte no restante já estornado
            try
            {
                Domain.Notificacoes.EnvioDeNotificacoesService.EnviarNotificacaoDeAgendamentoCancelado(horarios, resultadoPagamentoAntecipado);
            }
            catch (Exception ex) { }
        }

        private static void DefinirPrimeiroAgendamentoDoClienteSeNaoHouver(Horario horario)
        {
            var clienteEstabelecimento = horario.ClienteEstabelecimento;
            if (clienteEstabelecimento.PrimeiroAgendamento == null)
                clienteEstabelecimento.PrimeiroAgendamento = horario;
        }

        private static decimal? ObterValorPagoAntecipadamenteCasoTenha(Horario horario)
        {
            var ultimoHistorico = horario.Historicos.LastOrDefault();
            decimal? valorPagoAntecipadamente = ultimoHistorico == null ? null : ultimoHistorico.ValorPagoAntecipadamente;
            return valorPagoAntecipadamente;
        }

        private async Task<ResultadoCancelamentoDePagamentoAntecipado> EstornarOuTransformarEmCreditoClienteOPagamentoOnlineDosHorarios(List<int> idsHorarios, string motivo, PessoaFisica quemEstornou, bool ehUmEstornoDePagamentoOnline)
        {
            return await Domain.PagamentosAntecipados.CancelamentoDePagamentoOnlineAntecipadoService
                .EstornarOuTransformarEmCreditoClienteOPagamentoOnlineDosHorarios(idsHorarios, motivo, quemEstornou, ehUmEstornoDePagamentoOnline);
        }

        private void ValidarCancelamentoDeAgendamentosPagosOnlineAntecipadamente(List<int> idsHorarios)
        {
            Domain.PagamentosAntecipados.CancelamentoDePagamentoOnlineAntecipadoService
                .ValidarCancelamentoDeAgendamentos(idsHorarios, ehUmEstornoDePagamentoOnline: false);
        }

        private static bool VerificarSeAgendamentosCompoemComandaEConfirmarDesassociacaoComOUsuario(List<int> idsHorarios)
        {
            var compoemComanda = false;
            if (!ValidationHelper.Instance.Confirmado)
            {
                foreach (var idHorario in idsHorarios)
                {
                    var prevenda = Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(idHorario);
                    if (prevenda != null && prevenda.Comanda != null)
                        compoemComanda = true;
                }
            }

            if (compoemComanda && !ValidationHelper.Instance.Confirmado)
            {
                ValidationHelper.Instance.DefinirMensagemConfirmacao("Este serviço pertence a uma comanda. Esta operação irá desassociá-lo da comanda. Deseja prosseguir?");
            }

            return compoemComanda;
        }

        public void AssociarAssistenteAoServico(int idEstabelecimentoProfissional, int idHorario, bool alteradoPorAgendaPorCliente)
        {
            int idServicoEstabelecimento = Domain.Pessoas.HorarioRepository.ObterIdServicoEstabelecimentoDoHorarioPorId(idHorario);
            var assistente = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorId(idEstabelecimentoProfissional);
            bool configuracaoDeAssociacaoEstaHabilitada = assistente.Estabelecimento.EstabelecimentoConfiguracaoGeral.AssistentePodeVerAgendamentosQuePodeParticipar;
            bool PodeSerAssistente = Domain.Pessoas.EstabelecimentoAssistenteServicoRepository.Queryable().Any(ea => ea.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento && ea.EstabelecimentoProfissional.Codigo == idEstabelecimentoProfissional && ea.Ativo);
            bool servicoContinuaDisponivel = Domain.Pessoas.HorarioRepository.HorarioEstaDisponivelParaAssociarAssistente(idHorario, idEstabelecimentoProfissional);

            if (servicoContinuaDisponivel && PodeSerAssistente && configuracaoDeAssociacaoEstaHabilitada)
            {
                var horario = Domain.Pessoas.HorarioRepository.Load(idHorario);

                horario.EstabelecimentoProfissionalAssistente = assistente;
                Domain.Pessoas.HorarioService.ManterHorario(horario, alteradoPorAgendaCliente: alteradoPorAgendaPorCliente);
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemNotificacao(
                    "Não foi possível realizar a associação.\n Atualize sua tela para verificar se este serviço ainda está disponível para associação.");
            }
        }

        public ConsultaDeEtiquetasAssociadasDTO ListarEtiquetasParaAgenda(List<FiltroObjetoEtiquetado> filtroObjetos)
        {
            var retorno = new ConsultaDeEtiquetasAssociadasDTO();
            var listaParaexibicao = new List<ObjetoEtiquetadoDTO>();
            var listaEtiquetasAgendamento = new List<ObjetoEtiquetadoDTO>();
            var listaEtiquetasClientesEstabelecimento = new List<ObjetoEtiquetadoDTO>();

            foreach (var item in filtroObjetos)
            {
                if (item.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento)
                {
                    listaEtiquetasAgendamento.AddRange(Domain.Marcadores.ConsultaDeEtiquetasService.ListarEtiquetasDoObjeto(item.IdDono, item.Tipo, item.IdObjeto.Value));
                }
                else
                {
                    listaEtiquetasClientesEstabelecimento.AddRange(Domain.Marcadores.ConsultaDeEtiquetasService.ListarEtiquetasDoObjeto(item.IdDono, item.Tipo, item.IdObjeto.Value));
                }
            }

            listaParaexibicao.AddRange(listaEtiquetasAgendamento.Take(2));
            listaParaexibicao.AddRange(listaEtiquetasClientesEstabelecimento.Take(2));
            listaParaexibicao.AddRange(listaEtiquetasAgendamento.Skip(2).Take(2));
            listaParaexibicao.AddRange(listaEtiquetasClientesEstabelecimento.Skip(2).Take(2));

            retorno.Lista = listaParaexibicao
                            .Select(e => new KeyValuePair<string, string>(e.Cor, e.Conteudo))
                            .Take(4)
                            .ToList();

            retorno.TotalEtiquetas = listaEtiquetasAgendamento.Count() + listaEtiquetasClientesEstabelecimento.Count();

            return retorno;
        }

        public StatusDTO ValidarERealizarAgendamentoPeloClienteWebERetornarStatus(DadosParaRealizarAgendamentoDTO dadosAgendamento)
        {

            if (!Domain.WebContext.IdClienteAutenticado.HasValue)
                throw new InvalidOperationException("Um usuário autenticado é necessário para a execução deste método.");

            int idCLiente = Domain.WebContext.IdClienteAutenticado.Value;
            var servico = Domain.Pessoas.ServicoEstabelecimentoRepository.ObterPorId(dadosAgendamento.IdServicoEstabelecimento);

            if (!Domain.Pessoas.ClienteEstabelecimentoService.ClientePossuiPermissaoParaAgendarViaAPI(idCLiente, servico.Estabelecimento.IdEstabelecimento))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível fazer o agendamento online. Entre em contato com o estabelecimento.");
                return new StatusDTO(StatusHorarioEnum.Cancelado, "#F00");
            }

            // Devido a problemas com duplicação de agendamentos, colocamos essa validação para evitar
            // que a agenda fique ocupada por um cliente só realizando o mesmo serviço
            var idEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(dadosAgendamento.IdServicoEstabelecimento).Estabelecimento.IdEstabelecimento;

            var trackingDTO = Domain.Google.GoogleReserveConversionTrackingService
                            .MontarConversionTrackingParaEnvio(dadosAgendamento.IdEstabelecimento.ToString());

            if (trackingDTO != null)
            {
                dadosAgendamento.Origem = HorarioOrigemEnum.GoogleReserve;
            }

            if (!Domain.Pessoas.HotsiteEstabelecimentoService.VerificarSePodeRepetirAgendamentosNoDia(idEstabelecimento) &&
                Domain.Compromissos.MeusCompromissosService.VerificaAgendamentosExistentes(dadosAgendamento))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível fazer o agendamento, você já tem esse serviço agendado com esse profissional no dia de hoje.");
                return new StatusDTO(StatusHorarioEnum.Cancelado, "#F00");
            }

            var statusDoHorario = Domain.Pessoas.AgendaService.RealizarAgendamentoPeloClienteWebERetornarStatus(dadosAgendamento, null);

            if (trackingDTO != null && statusDoHorario.Id == (int)StatusHorarioEnum.Confirmado)
            {
                Domain.Google.GoogleReserveConversionTrackingService.DeletarCookie(trackingDTO.CookieName);
                Domain.Google.GoogleReserveConversionTrackingService.EnviarDadosConversaoParaGoogleAPI(trackingDTO);
            }

            return statusDoHorario;
        }

        public StatusDTO RealizarAgendamentoPeloClienteWebERetornarStatus(DadosParaRealizarAgendamentoDTO dadosAgendamento, int? idHorario)
        {
            var horario = RealizarAgendamentoPorDTO(dadosAgendamento, idHorario);

            if (!string.IsNullOrEmpty(dadosAgendamento.Cupom) && horario != null)
            {
                Domain.Cupom.CupomPessoaFisicaService.AssociarCupomAPessoaFisica(horario, dadosAgendamento.Cupom);
            }

            if (horario == null)
            {
                return new StatusDTO(StatusHorarioEnum.Cancelado, "#F00");
            }

            StatusHorario statusDoHorario = horario.HorarioOrigem == HorarioOrigemEnum.GoogleReserve
                ? StatusHorarioEnum.Confirmado
                : StatusHorarioEnum.Aguardando_Confirmacao;

            if (ValidationHelper.Instance.IsValid && horario.Status != null)
            {
                statusDoHorario = horario.Status;
            }

            if (horario == null)
                return new StatusDTO((StatusHorarioEnum)statusDoHorario.Codigo, statusDoHorario.Cor);
            else
                return new StatusDTO((StatusHorarioEnum)statusDoHorario.Codigo, statusDoHorario.Cor, horario.Id, AgendamentoAtendeRequisitosParaPagamentoOnline(horario));
        }

        private InformacoesDoPagamentoOnline AgendamentoAtendeRequisitosParaPagamentoOnline(Horario horario)
        {
            var status = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.AgendamentoAtendeRequisitosParaPagamentoOnline(horario);
            return status;
        }

        private Horario RealizarAgendamentoPorDTO(DadosParaRealizarAgendamentoDTO dto, int? idHorario, bool ehProfissionalAgendando = false)
        {
            var conta = Domain.WebContext.IdContaAutenticada.HasValue
                ? Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value, false)
                : null;

            var agendamentoOnlineRequerContaConfirmada = new ParametrosTrinks<bool>(ParametrosTrinksEnum.agendamento_online_requer_conta_confirmada).ObterValor();

            if (conta != null && !conta.Confirmada && agendamentoOnlineRequerContaConfirmada)
                ValidationHelper.Instance.AdicionarItemValidacao("Cadastro não confirmado. Acesse www.trinks.com e entre com seu login e senha para confirmar sua conta.");

            var fuso = 0;
            var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.ObterPorId(dto.IdServicoEstabelecimento);

            if (servicoEstabelecimento != null)
                fuso = servicoEstabelecimento.Estabelecimento != null ? servicoEstabelecimento.Estabelecimento.GetFuso() : 0;

            if (dto.DataHora != null && dto.DataHora < Calendario.Agora(fuso) && !ehProfissionalAgendando)
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possivel agendar em horários passados. Por favor, selecione um horário válido.");

            if (servicoEstabelecimento == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Solicitação inválida.");
            }
            else if (!idHorario.HasValue && dto.IdEstabelecimentoProfissional == 0)
            {
                DefinirUmProfissionalParaNovoAgendamentoSemPreferenciaDeProfissional(dto, servicoEstabelecimento);
            }

            var profissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterProfissionalPeloIdEstabelecimentoProfissional(dto.IdEstabelecimentoProfissional);
            int? idCliente = dto.IdCliente ?? Domain.WebContext.IdClienteAutenticado;

            if (profissional == null)
                ValidationHelper.Instance.AdicionarItemValidacao("Horário indisponível");

            Cliente cliente;
            if (!idCliente.HasValue && dto.Origem != HorarioOrigemEnum.GoogleReserve)
            {
                var permitePreCadastro = EstabelecimentoPermiteAgendamentoComPreCadastro(servicoEstabelecimento.Estabelecimento.IdEstabelecimento, dto.Origem);
                if (!permitePreCadastro)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Um usuário autenticado é necessário para a execução deste método.");
                }

                cliente = new Cliente();
            }
            else
            {
                cliente = Domain.Pessoas.ClienteRepository.ObterPorIdCliente(idCliente.Value);
            }

            ValidarRegrasParaAgendamentoB2C(dto, servicoEstabelecimento);

            Horario horario = null;
            if (ValidationHelper.Instance.IsValid)
            {
                horario = GerarEntidadeHorario(idHorario, dto, servicoEstabelecimento, profissional, cliente, conta);
                if (horario.Status == null)
                    horario.Status = StatusHorarioEnum.Aguardando_Confirmacao;

                if (horario.HorarioOrigem == HorarioOrigemEnum.GoogleReserve || horario.HorarioOrigem == HorarioOrigemEnum.Balcao)
                    horario.Status = StatusHorarioEnum.Confirmado;

                if (dto.Duracao.HasValue)
                {
                    horario.Duracao = dto.Duracao.Value;
                    horario.DataFim = horario.DataInicio.AddMinutes(dto.Duracao.Value);
                }

                if (horario.HorarioOrigem == HorarioOrigemEnum.Balcao)
                {
                    if (dto.Valor != 0)
                        horario.Valor = dto.Valor;
                }

                ManterAgendamento(horario, comanda: dto.Comanda);

                if (dto.StatusHorario.HasValue)
                    horario.Status = dto.StatusHorario.Value;

                if (horario.Status == null)
                    horario.Status = StatusHorarioEnum.Aguardando_Confirmacao;
            }

            return horario;
        }

        private void ValidarRegrasParaAgendamentoB2C(DadosParaRealizarAgendamentoDTO dto, ServicoEstabelecimento servicoEstabelecimento)
        {
            //if (dto.Origem != HorarioOrigemEnum.GoogleReserve && servicoEstabelecimento.ObservacaoObrigatorioDuranteAgendamenteB2C() && string.IsNullOrWhiteSpace(dto.Comentarios)) {
            //    ValidationHelper.Instance.AdicionarItemValidacao("Preencha o campo de observação para continuar o agendamento.");
            //}

            if (!String.IsNullOrWhiteSpace(dto.Comentarios) && dto.Comentarios.Length > 400)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O comentário do agendamento deve ter até 400 caracteres.");
            }
        }

        private void DefinirUmProfissionalParaNovoAgendamentoSemPreferenciaDeProfissional(DadosParaRealizarAgendamentoDTO dto, ServicoEstabelecimento servicoEstabelecimento)
        {
            var intervalo = new IntervaloData(dto.DataHora, servicoEstabelecimento.Duracao);
            var idEstabelecimentoProfissional = Domain.Pessoas.DisponibilidadeService
                .ObterIdDoPrimeiroEstabelecimentoProfissionalDisponivelOrdenadoPorMenorQuantidadeDeAgendamentos(servicoEstabelecimento, intervalo);

            if (idEstabelecimentoProfissional == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Nenhum profissional possui disponibilidade para o dia e hora selecionados.");
                return;
            }

            dto.IdEstabelecimentoProfissional = idEstabelecimentoProfissional.Value;
        }

        public Horario RealizarAgendamentoPeloGoogleReserve(DadosParaRealizarAgendamentoComClienteDTO dto, int? idHorario = null)
        {
            var cliente = ObterClientePeloDadosParaRealizarAgendamentoComClienteProvisorioDTO(dto);

            dto.IdCliente = cliente.IdCliente;

            //if (dto.IdEstabelecimentoProfissional == 0) {
            //    var estabelecimentoProfissional = ObterEstabelecimentoProfissionalPeloDadosParaRealizarAgendamentoComClienteProvisorioDTO(dto);
            //    if (estabelecimentoProfissional != null)
            //        dto.IdEstabelecimentoProfissional = estabelecimentoProfissional.Codigo;
            //}

            return RealizarAgendamentoPorDTO(dto, idHorario);
        }

        private static EstabelecimentoProfissional ObterEstabelecimentoProfissionalPeloDadosParaRealizarAgendamentoComClienteProvisorioDTO(DadosParaRealizarAgendamentoComClienteDTO dto)
        {
            var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(dto.IdServicoEstabelecimento);
            var intervalo = new IntervaloData(dto.DataHora, servicoEstabelecimento.Duracao);
            return Domain.Pessoas.DisponibilidadeService.ObterPrimeiroEstabelecimentoProfissionalDisponivel(dto.IdServicoEstabelecimento, intervalo);
        }

        private static Cliente ObterClientePeloDadosParaRealizarAgendamentoComClienteProvisorioDTO(DadosParaRealizarAgendamentoComClienteDTO dto)
        {
            var cliente = Domain.Pessoas.ClienteService.ObterClienteWebOuClienteDoEstabelecimentoPorEmail(dto.IdEstabelecimento, dto.DadosCliente.Email);
            if (cliente == null)
            {
                var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoService.ManterCliente(dto.DadosCliente);
                cliente = clienteEstabelecimento.Cliente;
            }

            return cliente;
        }

        public StatusDTO RealizarReagendamentoPeloClienteWebERetornarStatus(int idAgendamento, DadosParaRealizarAgendamentoDTO dadosAgendamento)
        {
            StatusDTO statusDoHorario = null;
            var horario = Domain.Pessoas.HorarioRepository.Load(idAgendamento);

            var fuso = 0;
            var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(dadosAgendamento.IdServicoEstabelecimento);

            if (servicoEstabelecimento != null)
                fuso = servicoEstabelecimento.Estabelecimento != null ? servicoEstabelecimento.Estabelecimento.GetFuso() : 0;

            if (horario != null && horario.DataInicio < Calendario.Agora(fuso))
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível reagendar um serviço em que a data início seja inferior a data atual.");

            if (ValidationHelper.Instance.IsValid)
                statusDoHorario = RealizarAgendamentoPeloClienteWebERetornarStatus(dadosAgendamento, idAgendamento);

            return statusDoHorario;
        }

        public Horario RealizarReagendamentoPeloGoogleReserve(DadosParaRealizarAgendamentoDTO dto, int idHorario)
        {
            return RealizarAgendamentoPorDTO(dto, idHorario);
        }

        public Horario SalvarAgendamentoDTOPeloProfissional(DadosParaRealizarAgendamentoDTO dto, int idHorario)
        {
            var horario = RealizarAgendamentoPorDTO(dto, idHorario, true);

            return horario;
        }

        private bool EstabelecimentoPermiteAgendamentoComPreCadastro(int idEstabelecimento, HorarioOrigemEnum origemAgendamento)
        {
            bool franquiaPermitePreCadastro = Domain.Pessoas.FranquiaEstabelecimentoRepository.FranquiaPermiteAgendamentoComPreCadastro(idEstabelecimento);
            return franquiaPermitePreCadastro && origemAgendamento == HorarioOrigemEnum.SiteFranquia;
        }

        private Horario GerarEntidadeHorario(int? idHorario, DadosParaRealizarAgendamentoDTO dto, ServicoEstabelecimento servicoEstabelecimento, Profissional profissional, Cliente cliente, Conta contaAutenticada)
        {
            var clienteEstabelecimento = cliente != null
                ? Domain.Pessoas.ClienteEstabelecimentoService.ObterOuTransformarEmClienteEstabelecimento(cliente, servicoEstabelecimento.Estabelecimento)
                : null;
            var idEstabelecimento = servicoEstabelecimento.Estabelecimento.IdEstabelecimento;
            var status = dto.StatusHorario.HasValue
                ? dto.StatusHorario.Value
                : Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterStatusPadraoAgendamentoWeb(idEstabelecimento);
            var valorPromocaoDoDia = Domain.Promocoes.PromocaoService.ObterValorPromocional(servicoEstabelecimento.IdServicoEstabelecimento, dto.DataHora);

            var assistente = dto.IdEstabelecimentoProfissionalAssistente.HasValue
                ? Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorId(dto.IdEstabelecimentoProfissionalAssistente.Value)
                : null;

            var horario = Domain.Pessoas.HorarioRepository.Factory.CreateParaAgendamentoRealizado(
                    idHorario, servicoEstabelecimento, clienteEstabelecimento, profissional, assistente, contaAutenticada?.Pessoa.PessoaFisica, dto.DataHora, dto.Origem, status, dto.Comentarios, valorPromocaoDoDia);
            
            horario.FoiPagoAntecipadamente = dto.FoiPagoAntecipadamente;
            
            return horario;
        }

        public BuscaClientesFuturos ListarAgendamentosFuturoPorNomeDoCliente(Estabelecimento estabelecimento, string nomeDoCliente, int pagina)
        {
            if (string.IsNullOrWhiteSpace(nomeDoCliente))
                return null;

            int limiteDiasFuturo = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_dias_busca_clientes_futuros_agenda).ObterValor();

            var buscaDeClientes = new BuscaClientesFuturos();
            buscaDeClientes.RegistrosPorPagina = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_agendamentos_futuros_por_requisicao_agenda).ObterValor();
            ;
            buscaDeClientes.DataFimLimite = Calendario.Hoje().AddDays(limiteDiasFuturo);
            int registrosParaPular = pagina > 1 ? (pagina - 1) * buscaDeClientes.RegistrosPorPagina : 0;

            var horarios = HorarioRepository.Queryable();
            var dadosCliente = Domain.Pessoas.ItemComboClienteRepository.Queryable();

            var agendamentosFuturo = (from h in horarios
                                      join d in dadosCliente on h.ClienteEstabelecimento.Codigo equals d.IdClienteEstabelecimento
                                      let se = h.ServicoEstabelecimento
                                      where h.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento &&
                                            h.DataInicio >= Calendario.Hoje().AddDays(1).Date &&
                                            h.DataInicio < buscaDeClientes.DataFimLimite.AddDays(1).Date &&
                                            d.Nome.Contains(nomeDoCliente) &&
                                            h.Ativo
                                      select new BuscaHorarioDTO
                                      {
                                          IdHorario = h.Id,
                                          IdStatus = h.Status.Codigo,
                                          IdPessoaDoCliente = d.IdPessoa,
                                          IdClienteEstabelecimento = d.IdClienteEstabelecimento,
                                          NomeCompletoDoCliente = d.Nome,
                                          DataInicio = h.DataInicio,
                                          IdProfissional = h.Profissional.IdProfissional,
                                          NomeServicoEstabelecimento = se.Nome,
                                          TipoCliente = h.Cliente.TipoCliente
                                      })
                                      .OrderBy(h => h.DataInicio)
                                      .Skip(registrosParaPular)
                                      .Take(buscaDeClientes.RegistrosPorPagina)
                                      .ToList();

            if (agendamentosFuturo.Any())
            {
                var idsPessoasDeClientesWeb = agendamentosFuturo.Where(h => h.TipoCliente == TipoClienteEnum.Web).Select(hf => hf.IdPessoaDoCliente).Distinct().ToList();

                var urlsFotosClientes = new List<KeyValuePair<int, string>>();
                foreach (var idPessoa in idsPessoasDeClientesWeb)
                {
                    var fotoPrincipalDoCliente = Domain.Pessoas.FotoPessoaRepository.FotoDePerfilDoUsuario(idPessoa);

                    if (fotoPrincipalDoCliente != null)
                        urlsFotosClientes.Add(new KeyValuePair<int, string>(idPessoa, fotoPrincipalDoCliente.ObterCaminhoWebFotoDePerfil(Fotos.Enums.DimemsoesFotosEnum.Dim50x50)));
                }

                DateTime dataInicioPagina = agendamentosFuturo.Min(h => h.DataInicio);
                DateTime dataFimPagina = agendamentosFuturo.Max(h => h.DataInicio);

                var ultimasVisitasDosClientes = ListarUltimaVisitaDosClientes(estabelecimento.IdEstabelecimento, dataInicioPagina, dataFimPagina, nomeDoCliente: nomeDoCliente);

                foreach (var agendamentoFuturo in agendamentosFuturo)
                {
                    agendamentoFuturo.UrlFotoCliente = urlsFotosClientes.Where(u => u.Key == agendamentoFuturo.IdPessoaDoCliente).Select(u => u.Value).FirstOrDefault();
                    agendamentoFuturo.DataDaUltimaVisita = ultimasVisitasDosClientes.Where(v => v.IdClienteEstabelecimento == agendamentoFuturo.IdClienteEstabelecimento).Select(v => v.DataUltimaVisita).FirstOrDefault();
                    buscaDeClientes.HorariosFuturo.Add(agendamentoFuturo);
                }
            }

            return buscaDeClientes;
        }

        public DadosExtrasClienteDTO ObterDadosExtrasDaPesquisaDeclientesDeHoje(Estabelecimento estabelecimento, List<int> statusSelecionados)
        {
            var dados = new DadosExtrasClienteDTO();

            if (statusSelecionados != null && statusSelecionados.Any())
            {
                DateTime hoje = Calendario.Hoje();

                var clientesWebDeHoje = Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(h => h.Estabelecimento == estabelecimento &&
                                h.DataInicio >= hoje &&
                                h.DataInicio < hoje.AddDays(1) &&
                                statusSelecionados.Contains(h.Status.Codigo) &&
                                h.Cliente.TipoCliente == TipoClienteEnum.Web &&
                                h.Ativo)
                    .Select(h => new
                    {
                        IdPessoaDoCliente = h.Cliente.PessoaFisica.IdPessoa,
                        IdClienteEstabelecimento = h.ClienteEstabelecimento.Codigo
                    })
                    .ToList();

                foreach (var cliente in clientesWebDeHoje)
                {
                    // Essa foi a forma menos impactante em questão de desempenho que encontramos pois a quantidade de clientes web é pequena comparado a clientes balcão
                    var fotoPrincipalDoCliente = Domain.Pessoas.FotoPessoaRepository.FotoDePerfilDoUsuario(cliente.IdPessoaDoCliente);

                    if (fotoPrincipalDoCliente != null)
                    {
                        string urlFoto = fotoPrincipalDoCliente.ObterCaminhoWebFotoDePerfil(Fotos.Enums.DimemsoesFotosEnum.Dim50x50);
                        dados.FotosDosClientes.Add(new UrlFotoClienteDTO(cliente.IdClienteEstabelecimento, urlFoto));
                    }
                }

                dados.UltimasVisitasDosClientes = ListarUltimaVisitaDosClientes(estabelecimento.IdEstabelecimento, hoje, hoje, status: statusSelecionados);
            }

            return dados;
        }

        private IList<UltimaVisitaDoClienteDTO> ListarUltimaVisitaDosClientes(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, string nomeDoCliente = null, List<int> status = null)
        {
            // Este foi a query com melhor desempenho que encontramos.
            // Verificar impacto de desempenho antes de modificar.
            string query = ObterQueryEmStringDataUltimaVisitaDosClientes(nomeDoCliente, status);

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session1 = holder.CreateSession(typeof(ActiveRecordBase));

            var q =
                session1.CreateSQLQuery(query)
                    .SetParameter("idEstabelecimento", idEstabelecimento)
                    .SetParameter("dataInicio", dataInicio.Date)
                    .SetParameter("dataFim", dataFim.AddDays(1).Date)
                    .SetParameter("hoje", Calendario.Hoje())
                    .SetResultTransformer(Transformers.AliasToBean(typeof(UltimaVisitaDoClienteDTO)));

            var result = q.List<UltimaVisitaDoClienteDTO>();
            holder.ReleaseSession(session1);

            return result;
        }

        private string ObterQueryEmStringDataUltimaVisitaDosClientes(string nomeDoCliente, List<int> status)
        {
            string query = @"
							select
								id_estabelecimento_cliente as IdClienteEstabelecimento,
								max(data_hora_inicio) as DataUltimaVisita
							from
								(select
								h.id_estabelecimento_cliente,
								h.data_hora_inicio
								from
								(select
									h.id_estabelecimento_cliente
								from
									Horario h
										{joinsExtras}
								where
										h.id_estabelecimento = :idEstabelecimento
									and h.ativo = 1
									and h.data_hora_inicio >= :dataInicio
									and h.data_hora_inicio < :dataFim
										{parametrosExtras}                        ) cientesDeHoje
								join
								horario h on h.id_estabelecimento_cliente = cientesDeHoje.id_estabelecimento_cliente
                                            AND h.id_estabelecimento = :idEstabelecimento
                                ) a
							where data_hora_inicio < :hoje
							group by
								id_estabelecimento_cliente";

            string parametrosExtrasString = string.Empty, joinsExtrasString = string.Empty;

            if (!string.IsNullOrWhiteSpace(nomeDoCliente))
            {
                joinsExtrasString = "inner join Combo_Clientes_v2 cli on h.id_estabelecimento_cliente = cli.id_estabelecimento_cliente";
                parametrosExtrasString += string.Format(" and cli.Nome like '%{0}%' ", nomeDoCliente);
            }

            if (status != null && status.Any())
            {
                parametrosExtrasString += string.Format(" and h.id_horario_status in ({0}) ", string.Join(",", status));
            }

            query = query
                .Replace("{joinsExtras}", joinsExtrasString)
                .Replace("{parametrosExtras}", parametrosExtrasString);

            return query;
        }

        public List<DadosDeAgendamentosParaGoogleReserveDTO> ListarHorariosFurutosParaGoogleReserve(string idUserGoogleReserve, DateTime data)
        {
            if (string.IsNullOrEmpty(idUserGoogleReserve))
                throw new Exception("idUserGoogleReserve não informado.");

            var query = Domain.Pessoas.HorarioRepository.Queryable();
            query = query.Where(f => f.Status != StatusHorarioEnum.Cancelado && f.DataInicio > data && f.Cliente.PessoaFisica.IdGoogleReserveUser == idUserGoogleReserve);

            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();

            return (from f in query
                    join ep in estabelecimentoProfissional on new { f.Estabelecimento, f.Profissional } equals new { ep.Estabelecimento, ep.Profissional }
                    select new DadosDeAgendamentosParaGoogleReserveDTO
                    {
                        Duracao = f.Duracao,
                        IdEstabelecimento = f.Estabelecimento.IdEstabelecimento,
                        IdServicoEstabelecimento = f.ServicoEstabelecimento.IdServicoEstabelecimento,
                        DataHoraInicio = f.DataInicio,
                        IdHorario = f.Id,
                        Valor = f.Valor,
                        StatusHorario = (StatusHorarioEnum)f.Status.Codigo,
                        IdEstabelecimentoProfissional = ep.Codigo
                    }).ToList();
        }

        public async Task CancelarHorario(int idHorario, string motivo, HorarioQuemCancelou quemCancelou, PessoaFisica pessoaQueAlterou, AreasTrinksEnum areaDoTrinks)
        {
            var idsHorarios = new List<int> { idHorario };

            await CancelarHorarios(
                idsHorarios,
                motivo: motivo,
                quemCancelou: quemCancelou,
                areaDoTrinks: AreasTrinksEnum.Backoffice,
                pessoaQueAlterou: pessoaQueAlterou,
                ehUmEstornoDePagamentoOnline: false);
        }

        public bool ExisteConflitoDeHorario(int idProfissional, int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int? idHorario = null)
        {
            var horariosConflitantes = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(f => f.Status != StatusHorarioEnum.Cancelado
                    && f.DataInicio < dataFim
                    && f.DataFim > dataInicio
                    && f.Profissional.IdProfissional == idProfissional
                    && f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (idHorario.HasValue)
                horariosConflitantes = horariosConflitantes.Where(f => f.Id != idHorario);

            return horariosConflitantes.Select(h => h.Id).Any();
        }

        public List<int> ListarIdsDosProfissionaisQueTrabalhamNoDia(int idEstabelecimento, DateTime dia)
        {
            var idsProfissionais = new List<int>();

            var horariosTrabalho = Domain.Pessoas.HorarioTrabalhoRepository.ObterPorDiaDeSemana(dia.DayOfWeek, idEstabelecimento);
            var liberacoesDeAgendaNoDia = Domain.ProfissionalAgenda.LiberacaoDeHorarioNaAgendaRepository.ListarDisponibilidadesDasLiberacoesAtivasNoDiaPorEstabelecimento(idEstabelecimento, dia);

            foreach (var horarioTrabalhoProfissional in horariosTrabalho)
            {
                var liberouAgendaNoDia = liberacoesDeAgendaNoDia.Any(l => l.EstabelecimentoProfissional.Codigo == horarioTrabalhoProfissional.EstabelecimentoProfissional.Codigo);
                var trabalhaNoDia = (horarioTrabalhoProfissional.TrabalhaNoDia() || liberouAgendaNoDia);

                if (trabalhaNoDia)
                    idsProfissionais.Add(horarioTrabalhoProfissional.EstabelecimentoProfissional.Profissional.IdProfissional);
            }

            return idsProfissionais;
        }

        public ConsultaHorariosDisponiveisDTO ConsultarHorariosDisponiveis(int idServicoEstabelecimento, DateTime data, int? idProfissional)
        {
            var parametros = new ParametrosBusca()
            {
                ServicosParaBackoffice = true,
                DataInicial = data,
                DataFinal = data.AddDays(1).AddSeconds(-1),
                DefinirMarcos = true
            };

            var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Load(idServicoEstabelecimento);
            var idEstabelecimento = servicoEstabelecimento.Estabelecimento.IdEstabelecimento;

            parametros.Estabelecimentos.Add(idEstabelecimento);
            parametros.ServicosEstabelecimento.Add(idServicoEstabelecimento);

            if (idProfissional.HasValue)
                parametros.Profissionais.Add(idProfissional.Value);

            var consulta = BuscaHorariosDisponiveisPeloCliente(parametros);
            var retorno = new ConsultaHorariosDisponiveisDTO()
            {
                Horarios = consulta.Estabelecimentos.FirstOrDefault(f => f.IdEstabelecimento == idEstabelecimento)
                .EstabelecimentoProfissionalServicos.FirstOrDefault()?.IntervaloDataList.Select(f => f.HoraInicio.ToString()).ToList()
            };

            return retorno;
        }

        public Horario RealizarAgendamentoWebPelaPromocaoOnline(AgendamentoTemporario agendamentoTemp)
            => RealizarAgendamentoPelaPromocaoOnline(agendamentoTemp, HorarioOrigemEnum.Web);

        public Horario RealizarAgendamentoBalcaoPelaPromocaoOnline(AgendamentoTemporario agendamentoTemp)
            => RealizarAgendamentoPelaPromocaoOnline(agendamentoTemp, HorarioOrigemEnum.Balcao);

        public Horario RealizarAgendamentoPelaPromocaoOnline(AgendamentoTemporario agendamentoTemp, HorarioOrigem origem)
        {
            var dadosParaAgendamento = DadosParaRealizarAgendamentoDTO.CriarPelaPromocaoOnline(agendamentoTemp);
            dadosParaAgendamento.Origem = origem;
            return RealizarAgendamentoPorDTO(dadosParaAgendamento, idHorario: null);
        }
    }
}
