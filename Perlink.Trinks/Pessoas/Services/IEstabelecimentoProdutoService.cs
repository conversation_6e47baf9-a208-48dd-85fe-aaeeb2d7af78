﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.ProdutoEstoque;
using Perlink.Trinks.ProdutoEstoque.DTO;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Services
{

    public interface IEstabelecimentoProdutoService : IService
    {

        void AssociarProdutoAoEstabelecimento(Estabelecimento estabelecimento, ProdutoPadrao produtoPadrao);

        void InativarEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada, bool validarAcao = true);

        void InativarTodosProdutosEstabelecimento(int idEstabelecimento, PessoaFisica pessoaFisicaLogada);

        void InativarEstabelecimentoProduto(List<Int32> idsEstabelecimentoProduto, PessoaFisica pessoaFisicaLogada);

        void ManterEstabelecimentoProduto(List<EstabelecimentoProduto> entidades, PessoaFisica pessoaFisicaLogada);

        void ManterEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada, IEnumerable<PropriedadeDoProdutoDTO> propriedadesDto, IList<PropriedadeDeProduto> propriedadesDeProdutosDoEstabelecimento);

        void ReativarEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada);

        List<EstabelecimentoProduto> AssociarProdutosAoEstabelecimento(Estabelecimento estabelecimento, List<ProdutoPadrao> produtoPadrao);
        Boolean NecessarioPreencherInformacoesFiscais(EstabelecimentoProduto estabelecimentoProduto);
        Boolean HouveAlteracaoRelevanteDeEstabelecimentoProduto(EstabelecimentoProduto estabelecimentoProdutoEditado);
        Boolean HouveAlteracaoRelevanteDeEstabelecimentoProduto(List<EstabelecimentoProduto> estabelecimentoProdutoEditado);
        void VerificaCondicoesProdutoEstabelecimentoNFCe(EstabelecimentoProduto produtoEstabelecimento);

        void ReplicarNCMNBS(int idProdutoCategoria, string codigoNCM, PessoaFisica pessoaFisicaLogada);
        void ReplicarSituacaoTributaria(int idEstabelecimentoProdutoCategoria, int? idSituacaoTributaria, decimal aliquotaICMSDoProdutoReplicado, PessoaFisica pessoaFisicaLogada);
        void ReplicarAliquotaICMS(int idEstabelecimentoProdutoCategoria, decimal aliquotaICMS, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimento);
        void ReplicarAliquotaPIS(int idEstabelecimentoProdutoCategoria, decimal aliquotaPIS, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimento);
        void ReplicarAliquotaCOFINS(int idEstabelecimentoProdutoCategoria, decimal aliquotaCOFINS, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimento);

        void ReplicarOrigemProduto(int idEstabelecimentoProdutoCategoria, int origemProduto, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimento);

        void ReplicarEhNacional(int idEstabelecimentoProdutoCategoria, bool ehNacional, PessoaFisica pessoaFisicaLogada);
        void ReplicarCodigoDeBarras(int idEstabelecimentoProdutoCategoria, string codigoDeBarras, PessoaFisica pessoaFisicaLogada);
        void ReplicarEhFabricacaoPropria(int idEstabelecimentoProdutoCategoria, bool ehFabricacaoPropria, PessoaFisica pessoaFisicaLogada);
        void ReplicarComissaoPorCategoria(int idEstabelecimentoProdutoCategoria, decimal valorAReplicar, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimanto);

        int ObterQuantidadeTotalPorUnidadeDeMedida(int quantidade, int quantidadeFracionada, int medidasPorUnidade);
        int ObterQuantidadeFracionadaEmUnidades(int quantidade, int medidasPorUnidade);
        void ObterQuantidateTotalEmUnidadesMaisFracao(int quantidadePorUnidadeDeMedida, int medidasPorUnidade, out int quantidadeDeUnidades, out int quantidadeFracionada);

        string ObterTextoDeQuantidadeComUnidadeDeMedida(int quantidadeTotalEmMedidas, string unidadeDeMedida, int medidasPorUnidade, bool exibeTudoComoFracaoSemSepararUnidade, bool colocarUnidadePorExtenso = false, bool medidaDeFracaoEmCaixaBaixa = true, bool adicionaTotalEmFracaoEntreParenteses = false, bool ehAppPro = false);

        string ObterTextoDeQuantidadeComUnidadeDeMedida(int quantidadeEmUnidades, int quantidadeEmFracao, string unidadeDeMedida, int medidasPorUnidade, bool colocarUnidadePorExtenso = false, bool medidaDeFracaoEmCaixaBaixa = true, bool adicionaTotalEmFracaoEntreParenteses = false);

        string TratarTextoDeUnidadeDeMedida(string unidadeDeMedida, int quantidadeFracionada);

        bool PermiteAlteracaoDeFracaoEDosagem(int idEstabelecimentoProduto);

        int ObterQuantidateTotalEmUnidades(int quantidadeTotalEmFracao, int medidasPorUnidade);

        decimal ObterPrecoPorTipoDeQuantidade(decimal precoUnitário, decimal precoPorFracao, Enums.TipoDeQuantidadeDeProduto tipoDeQuantidadeDeProduto);

        decimal DefinirPreco(EstabelecimentoProduto estabelecimentoProduto, bool valorEhDiferenciadoParaProfissional, Enums.TipoDeQuantidadeDeProduto tipoDeQuantidade);

        void VerificarProdutosComMesmoCodigoDeIdentificacao(Estabelecimento estabelecimento, int idEstabelecimentoProduto, string codigoDeIdentificacao);
        PermissaoParaProdutosDTO CarregarPermissoesParaProduto(Estabelecimento estabelecimento, PermissaoParaProdutosDTO permissoes);
        string ObterTextoDeQuantidadeComUnidadeDeMedida(QuantidadeUnidadeMedidaDTO medidaDTO);
    }
}
