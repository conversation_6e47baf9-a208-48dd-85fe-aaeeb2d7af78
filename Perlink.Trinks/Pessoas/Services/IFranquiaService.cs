﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas.DTO;

namespace Perlink.Trinks.Pessoas.Services
{
    public interface IFranquiaService : IService
    {
        string ObterUrlParaDownloadAppTrinksPorFranquia(bool ehAndroid, bool ehIOS, string identificadorDownloadApp);
        //bool VerificaPermissaodeTransferenciaDeProdutos(int idFranquia);
        void SaveOrUpdate(FranquiaDTO FranquiaDTO);
        string ObterUrlTemaFrameBusca(int idFranquia, string urlAtual);
        bool DeveOcultarAgendamentosProfissional2();
        bool AlterarPermissaoParaContratarItensAdicionaisNoPlanoPorIdEstabelecimento(int idEstabelecimento);
        bool EstabelecimentoPodeContratarItensAdicionaisNoPlano(int idEstabelecimento);
    }
}