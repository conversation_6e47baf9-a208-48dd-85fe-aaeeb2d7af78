﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Pessoas.DTO;

namespace Perlink.Trinks.Pessoas.Services
{
    public class EstabelecimentoConfiguracaoPosService : BaseService, IEstabelecimentoConfiguracaoPosService
    {
        public TipoPosDto ObterTipoPosPorEstabelecimento(int idEstabelecimento)
        {
            var tipoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                .ObterTipoPosDoEstabelecimento(idEstabelecimento);
            return new TipoPosDto
            {
                IdTipoPos = tipoPos.Id,
                PossuiSplitDePagamentosHabilitado = tipoPos.HabilitaSplit
            };
        }

        public EstabelecimentoConfiguracaoPosDto ObterConfiguracaoDoPosPorEstabelecimento(int idEstabelecimento)
        {
            var estabelecimentoConfiguracaoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                .ObterConfiguracaoPorEstabelecimento(idEstabelecimento);

            return new EstabelecimentoConfiguracaoPosDto
            {
                ConcluiuConfiguracao = estabelecimentoConfiguracaoPos.ConcluiuConfiguracao,
                IdTipoPos = estabelecimentoConfiguracaoPos.TipoPOS.Id,
            };
        }

        public void AtivarSplitDePagamentoParaEstabelecimentoSiclos(int idEstabelecimento)
        {
            var estabelecimentoConfiguracaoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                .ObterConfiguracaoPorEstabelecimento(idEstabelecimento);

            if(estabelecimentoConfiguracaoPos == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Estabelecimento não encontrado.");
            }

            estabelecimentoConfiguracaoPos.AguardandoRetornoDaHabilitacaoDoSplit = false;
            estabelecimentoConfiguracaoPos.AguardandoRetornoDoCredenciamentoNaAdquirente = false;
            estabelecimentoConfiguracaoPos.HabilitaSplit = true;

            Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                .Update(estabelecimentoConfiguracaoPos);
        }
    }
}