﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.Pacotes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.ProdutoEstoque;
using Perlink.Trinks.ProdutoEstoque.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Services
{

    public class EstabelecimentoProdutoService : BaseService, IEstabelecimentoProdutoService
    {

        private List<int> ListarEstabelecimentoProdutoComAlteracaoNoEstoque(List<EstabelecimentoProduto> entidades)
        {
            var idsEstabelecimentoProdutoComAlteracao = new List<int>();

            foreach (EstabelecimentoProduto entidade in entidades)
            {
                var possuiMovimentacaoEntradaExcetoRegistroInicial = Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueRepository.ExisteMovimentacaoExcetoRegistroInicial(entidade.Id);
                var estoqueInicialFoiAlterado = Domain.Pessoas.EstabelecimentoProdutoRepository.EstoqueInicialDoEstabelecimentoProdutoFoiAlterado(entidade.Id, entidade.EstoqueInicial);

                if (!possuiMovimentacaoEntradaExcetoRegistroInicial && estoqueInicialFoiAlterado)
                    idsEstabelecimentoProdutoComAlteracao.Add(entidade.Id);
            }

            return idsEstabelecimentoProdutoComAlteracao;
        }

        [TransactionInitRequired]
        public void ManterEstabelecimentoProduto(List<EstabelecimentoProduto> entidades, PessoaFisica pessoaFisicaLogada)
        {
            foreach (var entidade in entidades)
                ValidarManter(entidade);

            if (entidades.Count > 0 && ValidationHelper.Instance.IsValid)
            {
                var estabelecimento = entidades.FirstOrDefault().Estabelecimento;
                var idsEstabelecimentoProdutoComAlteracaoNoEstoque = ListarEstabelecimentoProdutoComAlteracaoNoEstoque(entidades);

                foreach (EstabelecimentoProduto entidade in entidades)
                {
                    var estabelecimentoProdutoTeveAlteracaoNoEstoque = idsEstabelecimentoProdutoComAlteracaoNoEstoque.Exists(p => p == entidade.Id);
                    PersistirEstabelecimentoProduto(entidade, pessoaFisicaLogada, estabelecimentoProdutoTeveAlteracaoNoEstoque);
                }

                Domain.Pessoas.EstabelecimentoProdutoRepository.Update(entidades[0]);
                Domain.Pessoas.EstabelecimentoService.AtualizarVersaoDaGeracaoDeNFC(estabelecimento);

                Domain.Pessoas.EstabelecimentoConfiguracaoGeralService
                    .AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(estabelecimento);
            }
            else
                Domain.Pessoas.EstabelecimentoProdutoRepository.Clear();
        }

        [TransactionInitRequired]
        public void ManterEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada, IEnumerable<PropriedadeDoProdutoDTO> propriedadesDto, IList<PropriedadeDeProduto> propriedadesDeProdutosDoEstabelecimento)
        {
            ValidarManter(entidade);

            if (ValidationHelper.Instance.IsValid)
            {
                var idsEstabelecimentoProdutoComAlteracaoNoEstoque = ListarEstabelecimentoProdutoComAlteracaoNoEstoque(new List<EstabelecimentoProduto> { entidade });

                var estabelecimentoProdutoTeveAlteracaoNoEstoque = idsEstabelecimentoProdutoComAlteracaoNoEstoque.Exists(p => p == entidade.Id);
                PersistirEstabelecimentoProduto(entidade, pessoaFisicaLogada, estabelecimentoProdutoTeveAlteracaoNoEstoque);

                if (entidade.Estabelecimento.ExibeCamposDeClassificacao())
                    Domain.ProdutoEstoque.ManterPropriedadesDoProdutoService.CriarOuAtualizarPropriedadesDoProduto(entidade, propriedadesDto, propriedadesDeProdutosDoEstabelecimento);

                if (entidade.Id == 0)
                {
                    Domain.Pessoas.EstabelecimentoProdutoRepository.SaveNew(entidade);

                    if (entidade.EstoqueInicial > 0)
                        Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueService.RegistrarPrimeiraMovimentacaoProduto(
                            entidade, pessoaFisicaLogada, entidade.Estabelecimento);
                }
                else
                {
                    Domain.Pessoas.EstabelecimentoProdutoRepository.Update(entidade);
                }

                var produtoFracionado = entidade.UnidadeMedida != null && entidade.UnidadeMedida.Id != (int)TipoUnidadeMedidaEnum.Unidade;
                if (entidade.Estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo != produtoFracionado)
                    Domain.Pessoas.EstabelecimentoConfiguracaoGeralService.AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(entidade.Estabelecimento);

                Domain.Pessoas.EstabelecimentoService.AtualizarVersaoDaGeracaoDeNFC(entidade.Estabelecimento);
            }
            else
            {
                Domain.Pessoas.EstabelecimentoProdutoRepository.Clear();
            }
        }

        public bool HouveAlteracaoRelevanteDeEstabelecimentoProduto(List<EstabelecimentoProduto> listaEstabelecimentoProdutoEditado)
        {
            foreach (var item in listaEstabelecimentoProdutoEditado)
            {
                if (HouveAlteracaoRelevanteDeEstabelecimentoProduto(item))
                    return true;
            }
            return false;
        }

        public bool HouveAlteracaoRelevanteDeEstabelecimentoProduto(EstabelecimentoProduto estabelecimentoProdutoEditado)
        {
            var estabelecimentoProdutoOriginal =
                Domain.Pessoas.EstabelecimentoProdutoRepository
                .Queryable().Where(f => f.Id == estabelecimentoProdutoEditado.Id)
                .Select(f => new
                {
                    Descricao = f.Descricao,
                    PrecoRevendaCliente = f.PrecoRevendaCliente,
                    PrecoRevendaProfissional = f.PrecoRevendaProfissional,
                    CodigoNCM = f.CodigoNCM,
                    IdSituacaoTributaria = f.IdSituacaoTributaria,
                    AliquotaICMS = f.AliquotaICMS
                }).Single();

            return (!estabelecimentoProdutoOriginal.Descricao.Equals(estabelecimentoProdutoEditado.Descricao)
                || estabelecimentoProdutoOriginal.PrecoRevendaCliente != estabelecimentoProdutoEditado.PrecoRevendaCliente
                || estabelecimentoProdutoOriginal.PrecoRevendaProfissional != estabelecimentoProdutoEditado.PrecoRevendaProfissional
                || estabelecimentoProdutoOriginal.CodigoNCM != estabelecimentoProdutoEditado.CodigoNCM
                || estabelecimentoProdutoOriginal.IdSituacaoTributaria != estabelecimentoProdutoEditado.IdSituacaoTributaria
                || estabelecimentoProdutoOriginal.AliquotaICMS != estabelecimentoProdutoEditado.AliquotaICMS);
        }

        private void PersistirEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada, bool estoqueFoiAlterado)
        {
            bool ehCadastro = entidade.Id == 0;

            if (ehCadastro)
            {
                entidade.PessoaCriacao = pessoaFisicaLogada;
                entidade.DataCriacao = Calendario.Agora();
            }
            entidade = AdicionarNacionalidade(entidade, ehCadastro);
            entidade.PessoaUltimaAlteracao = pessoaFisicaLogada;
            entidade.DataUltimaAlteracao = Calendario.Agora();

            if (entidade.Id > 0)
            {
                if (estoqueFoiAlterado)
                    Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueService.RegistrarMovimentacaoProdutoAoAlterarEstoqueInicial(entidade, pessoaFisicaLogada, entidade.Estabelecimento);

                entidade.EstoqueAtual = Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueRepository.ObterPosicaoEstoque(entidade.Id);
            }
        }

        public void InativarEstabelecimentoProduto(List<Int32> idsEstabelecimentoProduto, PessoaFisica pessoaFisicaLogada)
        {
            ValidarInativar(idsEstabelecimentoProduto);

            if (ValidationHelper.Instance.IsValid)
            {
                foreach (var idEstabelecimentoProduto in idsEstabelecimentoProduto)
                {
                    var entidade = Domain.Pessoas.EstabelecimentoProdutoRepository.Load(idEstabelecimentoProduto);
                    InativarEstabelecimentoProduto(entidade, pessoaFisicaLogada, false);
                }

                var estabelecimento = Domain.Pessoas.EstabelecimentoProdutoRepository.Load(idsEstabelecimentoProduto.First()).Estabelecimento;

                Domain.Pessoas.EstabelecimentoConfiguracaoGeralService
                    .AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(estabelecimento);
            }
        }

        [TransactionInitRequired]
        public void InativarEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada, bool validarAcao = true)
        {
            if (validarAcao)
                ValidarInativar(entidade);

            if (ValidationHelper.Instance.IsValid)
            {
                entidade.Ativo = false;
                entidade.DataUltimaAlteracao = Calendario.Agora();
                entidade.PessoaUltimaAlteracao = pessoaFisicaLogada;

                Domain.Pessoas.EstabelecimentoConfiguracaoGeralService
                    .AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(entidade.Estabelecimento);
            }
        }

        [TransactionInitRequired]
        public void InativarTodosProdutosEstabelecimento(int idEstabelecimento, PessoaFisica pessoaFisicaLogada)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarAtivosPorEstabelecimento(idEstabelecimento);
            var qtdMaxParaInativar = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_para_inativacao_via_gerenciamento).ObterValor();

            if (produtos.Count() <= qtdMaxParaInativar)
            {
                foreach (EstabelecimentoProduto estabelecimentoProduto in produtos)
                {
                    estabelecimentoProduto.Ativo = false;
                    estabelecimentoProduto.DataUltimaAlteracao = Calendario.Agora();

                    Domain.Pessoas.EstabelecimentoProdutoRepository.UpdateNoFlush(estabelecimentoProduto);
                }

                Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaService.InativarTodasAsCategorias(idEstabelecimento);
                Domain.Pessoas.EstabelecimentoProdutoRepository.Flush();
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemValidacao("A quantidade de produtos está acima do limite para realizar a inativação. Por favor, envie uma solicitação de inativação para o time de Fábrica.");
            }



        }

        [TransactionInitRequired]
        public void ReativarEstabelecimentoProduto(EstabelecimentoProduto entidade, PessoaFisica pessoaFisicaLogada)
        {
            VerificarSeExisteCodigoDeBarrasEhProdutoEstaAtivoParaReativacao(entidade.Id, entidade.Estabelecimento.IdEstabelecimento, entidade.CodigoBarras);

            ValidarSeExisteEstabelecimentoProdutoAtivoComMesmoNomeEFabricante(entidade);

            if (ValidationHelper.Instance.IsValid)
            {
                entidade.Ativo = true;
                entidade.DataUltimaAlteracao = Calendario.Agora();
                entidade.PessoaUltimaAlteracao = pessoaFisicaLogada;

                if (entidade.EstabelecimentoProdutoCategoria != null && !entidade.EstabelecimentoProdutoCategoria.Ativo)
                    entidade.EstabelecimentoProdutoCategoria.Ativo = true;

                Domain.Pessoas.EstabelecimentoConfiguracaoGeralService
                    .AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(entidade.Estabelecimento);
            }
        }

        [TransactionInitRequired]
        public void AssociarProdutoAoEstabelecimento(Estabelecimento estabelecimento,
            ProdutoPadrao produtoPadrao)
        {
            AssociarProdutosAoEstabelecimento(estabelecimento, new List<ProdutoPadrao> { produtoPadrao });

            Domain.Pessoas.EstabelecimentoConfiguracaoGeralService
                    .AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(estabelecimento);
        }

        [TransactionInitRequired]
        public List<EstabelecimentoProduto> AssociarProdutosAoEstabelecimento(Estabelecimento estabelecimento, List<ProdutoPadrao> listaProdutoPadrao)
        {
            List<EstabelecimentoProduto> estabelecimentosProdutos = new List<EstabelecimentoProduto>();

            foreach (var produtoPadrao in listaProdutoPadrao)
            {
                var estabelecimentoProduto = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable().FirstOrDefault(f => f.Estabelecimento == estabelecimento && f.ProdutoPadrao == produtoPadrao);
                var fabricante = Domain.Pessoas.EstabelecimentoFabricanteProdutoService.Associar(estabelecimento, produtoPadrao.FabricanteProdutoPadrao);
                var categoria = Domain.Pessoas.ServicoCategoriaEstabelecimentoService.Associar(estabelecimento, produtoPadrao.ProdutoCategoriaPadrao);

                if (estabelecimentoProduto != null)
                {
                    estabelecimentoProduto.Ativo = true;
                    Domain.Pessoas.EstabelecimentoProdutoRepository.Update(estabelecimentoProduto);
                }
                else
                {
                    estabelecimentoProduto = new EstabelecimentoProduto
                    {
                        ProdutoPadrao = produtoPadrao,
                        Estabelecimento = estabelecimento,
                        EstabelecimentoFabricanteProduto = fabricante,
                        EstabelecimentoProdutoCategoria = categoria,
                        ProdutoEhNacional = true,
                        PermitirRevenda = true,
                        EhRevendaParaCliente = true,
                        EhRevendaParaProfissional = true
                    };

                    Domain.Pessoas.EstabelecimentoProdutoRepository.SaveNew(estabelecimentoProduto);
                }

                estabelecimentosProdutos.Add(estabelecimentoProduto);
            }

            Domain.Pessoas.EstabelecimentoService.AtualizarVersaoDaGeracaoDeNFC(estabelecimento);

            Domain.Pessoas.EstabelecimentoConfiguracaoGeralService
                    .AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionado(estabelecimento);

            return estabelecimentosProdutos;
        }

        public Boolean NecessarioPreencherInformacoesFiscais(EstabelecimentoProduto estabelecimentoProduto)
        {
            if (estabelecimentoProduto.Estabelecimento == null)
                return false;

            bool necessitaPreencherInformacoesFiscaisSAT = estabelecimentoProduto.Estabelecimento.EstabelecimentoPossuiNFC() && (
                estabelecimentoProduto.AliquotaICMS == null ||
                estabelecimentoProduto.IdSituacaoTributaria == null ||
                estabelecimentoProduto.AliquotaPIS == null ||
                estabelecimentoProduto.AliquotaCOFINS == null ||
                estabelecimentoProduto.OrigemProduto == null);

            bool necessitaPreencherInformacoesFiscaisNFCe = (estabelecimentoProduto.Estabelecimento.EstabelecimentoPossuiNFC() && (
                estabelecimentoProduto.AliquotaICMS == null ||
                estabelecimentoProduto.CodigoNCM == null ||
                estabelecimentoProduto.IdSituacaoTributaria == null ||
                estabelecimentoProduto.AliquotaPIS == null ||
                estabelecimentoProduto.AliquotaCOFINS == null ||
                estabelecimentoProduto.OrigemProduto == null));

            //if (estabelecimentoProduto.Estabelecimento.EstabelecimentoPossuiNFC() && estabelecimentoProduto.Estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Sat)
            //    return necessitaPreencherInformacoesFiscaisSAT;
            //else
            return necessitaPreencherInformacoesFiscaisNFCe;
        }

        public void VerificaCondicoesProdutoEstabelecimentoNFCe(EstabelecimentoProduto produtoEstabelecimento)
        {
            if (Domain.Pessoas.EstabelecimentoService.EstabelecimentoEstahConfiguradoParaNFCDoTipoEletronica(produtoEstabelecimento.Estabelecimento.IdEstabelecimento))
            {
                //if (produtoEstabelecimento.Estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Sat && (!produtoEstabelecimento.AliquotaICMS.HasValue ||
                //    !produtoEstabelecimento.AliquotaPIS.HasValue ||
                //    !produtoEstabelecimento.AliquotaCOFINS.HasValue ||
                //    !produtoEstabelecimento.OrigemProduto.HasValue ||
                //    !produtoEstabelecimento.IdSituacaoTributaria.HasValue ||
                //    string.IsNullOrEmpty(produtoEstabelecimento.CodigoBarras))) {
                //    ValidationHelper.Instance.AdicionarItemNotificacao("Para que o cupom fiscal possa ser emitida para vendas com este produto, " +
                //                        "é necessário que os dados fiscais sejam informados através do botão Edição Fiscal.");
                //}
                //else 
                if (string.IsNullOrEmpty(produtoEstabelecimento.CodigoNCM) || !produtoEstabelecimento.AliquotaICMS.HasValue ||
                    !produtoEstabelecimento.IdSituacaoTributaria.HasValue || string.IsNullOrEmpty(produtoEstabelecimento.CodigoBarras))
                {
                    ValidationHelper.Instance.AdicionarItemNotificacao("Para que a nota fiscal possa ser emitida para vendas com este produto, " +
                                        "é necessário que os dados fiscais sejam informados através do botão Edição Fiscal.");
                }
            }
        }

        private EstabelecimentoProduto AdicionarNacionalidade(EstabelecimentoProduto produto, bool ehCadastro)
        {
            if (produto.Estabelecimento.ConfiguracaoDeNFC != null)
            {
                //if (produto.Estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Sat && !ehCadastro) {
                //    produto.OrigemProduto = produto.OrigemProduto == null ? 0 : produto.OrigemProduto;

                //    switch (produto.OrigemProduto.Value) {
                //        case 0:
                //        case 3:
                //        case 4:
                //        case 5:
                //        case 8:
                //            produto.ProdutoEhNacional = true;
                //            break;

                //        case 1:
                //        case 2:
                //        case 6:
                //        case 7:
                //            produto.ProdutoEhNacional = false;
                //            break;
                //    }
                //}
                //else
                if (ehCadastro)
                {
                    produto.ProdutoEhNacional = true;
                }
            }

            return produto;
        }

        #region Validações

        private void ValidarManter(EstabelecimentoProduto entidade)
        {
            ValidarSeExisteEstabelecimentoProdutoAtivoComMesmoNomeEFabricante(entidade);

            if (entidade.EstabelecimentoProdutoCategoria != null &&
                string.IsNullOrWhiteSpace(entidade.EstabelecimentoProdutoCategoria.Nome))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("É obrigatório ter categoria");
            }

            if (entidade.EstabelecimentoFabricanteProduto != null &&
                string.IsNullOrWhiteSpace(entidade.EstabelecimentoFabricanteProduto.Nome))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("É obrigatório ter Fabricante");
            }

            if (ValidationHelper.Instance.IsValid && entidade.EstabelecimentoProdutoCategoria != null &&
                entidade.EstabelecimentoProdutoCategoria.IdEstabelecimentoProdutoCategoria == 0)
            {
                var existeComMesmoNome = Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository
                    .ExisteEstabelecimentoCategoriaProdutoComNomeIgual(entidade.EstabelecimentoProdutoCategoria.Nome,
                        entidade.Estabelecimento.IdEstabelecimento, 0);

                if (existeComMesmoNome)
                    ValidationHelper.Instance.AdicionarItemValidacao("Já existe uma categoria com este nome!");
            }

            if (!entidade.PermitirRevenda)
            {
                var lista = new List<int> { entidade.Id };
                if (ValidarSeTempacoteComSaldo(lista))
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "O produto está associado a pacotes ativos ou com saldo para utilização.\n Para indicar que ele não é para revenda, os pacotes associados deverão estar inativos e ter o consumo encerrado na tela de Venda > Venda e Consumo de Pacotes.");
            }

            if (!string.IsNullOrEmpty(entidade.CodigoBarras))
                VerificarSeExisteCodigoDeBarrasEhProdutoEstaAtivo(entidade.Id, entidade.Estabelecimento.IdEstabelecimento, entidade.CodigoBarras);

            if (!string.IsNullOrEmpty(entidade.CEST) && entidade.CEST.Length < 7)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                        "O valor do campo 'CEST' para o produto " + entidade.Descricao + " deve conter 7 digitos obrigatórios ou ser mantido vazio!");
            }

            if (!string.IsNullOrEmpty(entidade.CodigoPis) && entidade.CodigoPis.Length != 2)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                        "O valor do campo 'Código do PIS' para o produto " + entidade.Descricao + " deve conter 2 digitos obrigatórios ou ser mantido vazio!");
            }

            if (!string.IsNullOrEmpty(entidade.CodigoCofins) && entidade.CodigoCofins.Length != 2)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                        "O valor do campo 'Código do COFINS' para o produto " + entidade.Descricao + " deve conter 2 digitos obrigatórios ou ser mantido vazio!");
            }

            if (entidade.IcmsOrigem != null && entidade.IcmsOrigem.Value.ToString().Length > 15)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                        "O valor do campo 'Alíquota do ICMS de Origem' para o produto " + entidade.Descricao + " deve conter até 15 digitos ou ser mantido vazio!");
            }

            if (entidade.ReducaoIcmsOrigem != null && entidade.ReducaoIcmsOrigem.Value.ToString().Length > 15)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                        "O valor do campo 'Percentual de Redução do ICMS de Origem' para o produto " + entidade.Descricao + " deve conter até 15 digitos ou ser mantido vazio!");
            }

            if (EstabelecimentoPossuiNFC(entidade.Estabelecimento))
            {
                if (!string.IsNullOrEmpty(entidade.CodigoNCM) && Domain.NotaFiscalDoConsumidor.TabelaIBPTRepository.ObterAliquotasPorCodigoeUF(entidade.CodigoNCM, entidade.Estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF) == null)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Código NCM não encontrado. Para que a nota fiscal possa ser emitida para vendas com este produto, é necessário inserir um código válido contido na tabela de NCM do IBPT.");
                }
            }
        }

        private bool EstabelecimentoPossuiNFC(Estabelecimento estabelecimento)
        {
            var possuiNFCAtivo = Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Queryable().FirstOrDefault(c => c.IdEstabelecimento == estabelecimento.IdEstabelecimento && c.InterfaceNFC != TipoDeInterfaceNFC.Nenhuma) != null;
            return possuiNFCAtivo;
        }

        private static void ValidarSeExisteEstabelecimentoProdutoAtivoComMesmoNomeEFabricante(EstabelecimentoProduto entidade)
        {
            if (entidade.EstabelecimentoFabricanteProduto != null &&
                entidade.EstabelecimentoFabricanteProduto.IdEstabelecimentoFabricanteProduto > 0)
            {
                var existeProdutosComMesmaDescricao =
                    Domain.Pessoas.EstabelecimentoProdutoRepository
                        .ExisteEstabelecimentoProdutoAtivoDoMesmoFabricanteComMesmoNome(entidade);

                if (existeProdutosComMesmaDescricao)
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "Já existe um produto ativo com com o nome '" + entidade.Descricao + "' associado a esse fabricante!");
            }
        }

        private void ValidarInativar(List<Int32> idsEstabelecimentoProduto)
        {
            if (ValidarSeTempacoteComSaldo(idsEstabelecimentoProduto))
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Os produtos estão associados a pacotes ativos ou com saldo para utilização.\n Para inativá-los, os pacotes associados deverão estar inativos e ter o consumo encerrado na tela de Venda > Venda e Consumo de Pacotes.");

            var servicosAssociados = servicosComBaixaAutomaticaAssociados(idsEstabelecimentoProduto);
            if (servicosAssociados.Count > 0)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "Os produtos estão associados a serviços com baixa automática de estoque.\n Para inativá-los, os produtos devem ser desassociados ou substituídos acessando o menu “Meu estabelecimento > Serviços > Todos os serviços");
            }

            if (ValidarSeTemClubeDeAssinaturaAtivo(idsEstabelecimentoProduto))
                ValidationHelper.Instance.AdicionarItemValidacao("Algum dos produtos selecionados está associado a um clube de assinaturas.\n Para excluí-lo, o clube de assinaturas deve ser cancelado na tela de Clube de assinaturas > Venda e renovação de assinaturas.");
        }

        public void ValidarInativar(EstabelecimentoProduto entidade)
        {
            var lista = new List<int> { entidade.Id };
            if (ValidarSeTempacoteComSaldo(lista))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "O produto está associado a pacotes ativos ou com saldo para utilização.\n Para inativá-lo, os pacotes associados deverão estar inativos e ter o consumo encerrado na tela de Venda > Venda e Consumo de Pacotes.");
            }

            var servicosAssociados = servicosComBaixaAutomaticaAssociados(lista);
            if (servicosAssociados.Count > 0)
            {
                string nomeServicos = "";
                foreach (var s in servicosAssociados)
                    nomeServicos += s.Nome + ", ";

                ValidationHelper.Instance.AdicionarItemValidacao(
                    "O produto está associado a serviços com baixa automática de estoque.\n Para inativá-lo, o produto deve ser desassociado ou substituído dos seguintes serviços: " + nomeServicos + " acessando o menu “Meu estabelecimento > Serviços > Todos os serviços");
            }

            if (ValidarSeTemClubeDeAssinaturaAtivo(lista))
                ValidationHelper.Instance.AdicionarItemValidacao("O produto está associado a um clube de assinaturas.\n Para excluí-la, o clube de assinaturas deve ser cancelado na tela de Clube de assinaturas > Venda e renovação de assinaturas.");
        }

        private bool ValidarSeTemClubeDeAssinaturaAtivo(ICollection<int> idsEstabelecimentoProduto)
        {
            return Domain.ClubeDeAssinaturas.BeneficioDaAssinaturaRepository.PossuiBeneficioComProdutosSelecionados(idsEstabelecimentoProduto)
                || Domain.ClubeDeAssinaturas.BeneficioDoPlanoRepository.PossuiBeneficioComProdutosSelecionados(idsEstabelecimentoProduto);
        }

        private bool ValidarSeTempacoteComSaldo(ICollection<int> idsEstabelecimentoProduto)
        {
            var contemSaldo = Domain.Pacotes.ItemPacoteClienteProdutoRepository.Queryable()
                 .Any(f => idsEstabelecimentoProduto.Contains(f.EstabelecimentoProduto.Id)
                           && f.QuantidadeConsumida < f.Quantidade
                           && f.PacoteCliente.Ativo);

            var contemPacoteEstabelecimento = Domain.Pacotes.PacoteRepository.Queryable()
                .Any(
                    p =>
                        p.Ativo &&
                        p.ItensPacote.Any(
                            p2 =>
                                (p2 is ItemPacoteProduto) &&
                                idsEstabelecimentoProduto.Contains(((ItemPacoteProduto)p2).EstabelecimentoProduto.Id)));

            return contemSaldo || contemPacoteEstabelecimento;
        }

        private List<ServicoEstabelecimento> servicosComBaixaAutomaticaAssociados(ICollection<int> idsEstabelecimentoProduto)
        {
            var itensComBaixaAutomatica = Domain.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomaticaService.ListarAtivosPorEstabelecimentoProduto(idsEstabelecimentoProduto.ToList());

            var listaDeServicos = new List<ServicoEstabelecimento>();
            if (itensComBaixaAutomatica.Count() > 0)
            {
                foreach (var item in itensComBaixaAutomatica)
                {
                    listaDeServicos.Add(item.ServicoEstabelecimento);
                }
            }

            return listaDeServicos;
        }

        private void VerificarSeExisteCodigoDeBarrasEhProdutoEstaAtivo(int idEstabelecimentoProduto, int idEstabelecimento, string codigoBarras)
        {
            var listaEstabelecimentoProdutosAtivo = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Ativo);

            if (listaEstabelecimentoProdutosAtivo.Any(f => f.CodigoBarras != null && f.CodigoBarras == codigoBarras && f.Id != idEstabelecimentoProduto))
                ValidationHelper.Instance.AdicionarItemValidacao(string.Format("O código de barras " + codigoBarras + " informado já está sendo utilizado pelo produto {0}.", listaEstabelecimentoProdutosAtivo.FirstOrDefault(f => f.CodigoBarras != null && f.CodigoBarras == codigoBarras && f.Id != idEstabelecimentoProduto).Descricao));
        }

        private void VerificarSeExisteCodigoDeBarrasEhProdutoEstaAtivoParaReativacao(int idEstabelecimentoProduto, int idEstabelecimento, string codigoBarras)
        {
            List<EstabelecimentoProduto> listaEstabelecimentoProdutosAtivo = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarAtivosPorEstabelecimento(idEstabelecimento);

            if (listaEstabelecimentoProdutosAtivo.Any(f => f.CodigoBarras != null && f.CodigoBarras == codigoBarras && f.Id != idEstabelecimentoProduto))
                ValidationHelper.Instance.AdicionarItemValidacao(string.Format("O produto não pode ser reativado pois seu código de barras já está sendo utilizado pelo produto {0}. Para fazer a reativação, é necessário alterar o código de barras do produto {0} ou inativá-lo.", listaEstabelecimentoProdutosAtivo.FirstOrDefault(f => f.CodigoBarras != null && f.CodigoBarras == codigoBarras && f.Id != idEstabelecimentoProduto).Descricao));
        }

        #endregion Validações

        #region Calculo quantidade estoque com fracionamento

        public int ObterQuantidadeTotalPorUnidadeDeMedida(int quantidade, int quantidadeFracionada, int medidasPorUnidade)
        {
            return (quantidade * medidasPorUnidade) + quantidadeFracionada;
        }

        public int ObterQuantidadeFracionadaEmUnidades(int quantidade, int medidasPorUnidade)
        {
            return quantidade / (medidasPorUnidade > 0 ? medidasPorUnidade : 1);
        }

        public int ObterQuantidateTotalEmUnidades(int quantidadeTotalEmFracao, int medidasPorUnidade)
        {
            int totalEmUnidades;
            int restoEmFracao;
            ObterQuantidateTotalEmUnidadesMaisFracao(quantidadeTotalEmFracao, medidasPorUnidade, out totalEmUnidades, out restoEmFracao);
            return totalEmUnidades;
        }

        public void ObterQuantidateTotalEmUnidadesMaisFracao(int quantidadePorUnidadeDeMedida, int medidasPorUnidade, out int quantidadeDeUnidades, out int quantidadeFracionada)
        {
            quantidadeDeUnidades = quantidadePorUnidadeDeMedida / (medidasPorUnidade > 0 ? medidasPorUnidade : 1);
            quantidadeFracionada = quantidadePorUnidadeDeMedida % (medidasPorUnidade > 0 ? medidasPorUnidade : 1);
        }

        public string ObterTextoDeQuantidadeComUnidadeDeMedida(int quantidadeEmUnidades, int quantidadeEmFracao, string unidadeDeMedida, int medidasPorUnidade, bool colocarUnidadePorExtenso = false, bool medidaDeFracaoEmCaixaBaixa = true, bool adicionaTotalEmFracaoEntreParenteses = false)
        {
            int total = (quantidadeEmUnidades * medidasPorUnidade) + quantidadeEmFracao;
            return ObterTextoDeQuantidadeComUnidadeDeMedida(total, unidadeDeMedida, medidasPorUnidade, exibeTudoComoFracaoSemSepararUnidade: false, colocarUnidadePorExtenso: colocarUnidadePorExtenso, medidaDeFracaoEmCaixaBaixa: medidaDeFracaoEmCaixaBaixa, adicionaTotalEmFracaoEntreParenteses: adicionaTotalEmFracaoEntreParenteses, quantidadeEmUnidadeEspecificada: quantidadeEmUnidades, quantidadeEmFracaoEspecificada: quantidadeEmFracao);
        }

        private string ObterTextoDeQuantidadeComUnidadeDeMedida(int quantidadeTotalEmMedidas, string unidadeDeMedida, int medidasPorUnidade, bool exibeTudoComoFracaoSemSepararUnidade, bool colocarUnidadePorExtenso = false, bool medidaDeFracaoEmCaixaBaixa = true, bool adicionaTotalEmFracaoEntreParenteses = false, int? quantidadeEmUnidadeEspecificada = null, int? quantidadeEmFracaoEspecificada = null, bool ehAppPro = false)
        {
            string textoDeQuantidadeComUnidadeDeMedida = "";
            int quantidadeDeUnidades = 0;
            int quantidadeFracionada = 0;

            bool produtoEhFracionado = unidadeDeMedida != null && unidadeDeMedida.ToLower() != "unidade" && unidadeDeMedida != "UN";

            if (exibeTudoComoFracaoSemSepararUnidade)
            {
                quantidadeFracionada = quantidadeTotalEmMedidas;
                quantidadeDeUnidades = 0;
            }
            else if (quantidadeEmFracaoEspecificada != null || quantidadeEmUnidadeEspecificada != null)
            {
                quantidadeFracionada = quantidadeEmFracaoEspecificada ?? 0;
                quantidadeDeUnidades = quantidadeEmUnidadeEspecificada ?? 0;
            }
            else
                Domain.Pessoas.EstabelecimentoProdutoService.ObterQuantidateTotalEmUnidadesMaisFracao(quantidadeTotalEmMedidas, medidasPorUnidade, out quantidadeDeUnidades, out quantidadeFracionada);

            if (quantidadeTotalEmMedidas == 0)
                textoDeQuantidadeComUnidadeDeMedida = "0";
            else
            {
                string textoUnidadeDeMedidaParaTotalEmMedidas = unidadeDeMedida.PluralizarPalavra(quantidadeTotalEmMedidas);

                unidadeDeMedida = TratarTextoDeUnidadeDeMedida(unidadeDeMedida, quantidadeFracionada);

                string siglaOuNomeDaUnidadeAbreviada = ehAppPro ? "un." : "UN";
                string siglaOuNomeDaUnidade = colocarUnidadePorExtenso ? "unidade" : siglaOuNomeDaUnidadeAbreviada;
                siglaOuNomeDaUnidade = TratarTextoDeUnidadeDeMedida(siglaOuNomeDaUnidade, quantidadeDeUnidades);

                //Coloca UN se exibir Unidades
                if (quantidadeDeUnidades != 0)
                    textoDeQuantidadeComUnidadeDeMedida = quantidadeDeUnidades.ToString() + " " + siglaOuNomeDaUnidade;

                //Coloca " e " caso tenha Unidades e Frações
                if (quantidadeDeUnidades != 0 && quantidadeFracionada != 0)
                    textoDeQuantidadeComUnidadeDeMedida = textoDeQuantidadeComUnidadeDeMedida + " e ";

                //Coloca a quantidade em fração e concatena com a unidade de medida
                if (quantidadeFracionada != 0)
                {
                    string textoParteDaFracao = quantidadeFracionada.ToString() + " " + unidadeDeMedida;

                    if (medidaDeFracaoEmCaixaBaixa)
                        textoParteDaFracao = textoParteDaFracao.ToLower();

                    textoDeQuantidadeComUnidadeDeMedida = textoDeQuantidadeComUnidadeDeMedida + textoParteDaFracao;
                }

                if (quantidadeTotalEmMedidas != 0 && adicionaTotalEmFracaoEntreParenteses == true && produtoEhFracionado && medidasPorUnidade <= quantidadeTotalEmMedidas)
                {
                    textoDeQuantidadeComUnidadeDeMedida = textoDeQuantidadeComUnidadeDeMedida
                        + " (" + quantidadeTotalEmMedidas + " " + textoUnidadeDeMedidaParaTotalEmMedidas + ")";
                }
            }

            return textoDeQuantidadeComUnidadeDeMedida;
        }

        public string ObterTextoDeQuantidadeComUnidadeDeMedida(int quantidadeTotalEmMedidas, string unidadeDeMedida, int medidasPorUnidade, bool exibeTudoComoFracaoSemSepararUnidade, bool colocarUnidadePorExtenso = false, bool medidaDeFracaoEmCaixaBaixa = true, bool adicionaTotalEmFracaoEntreParenteses = false, bool ehAppPro = false)
        {
            return ObterTextoDeQuantidadeComUnidadeDeMedida(quantidadeTotalEmMedidas, unidadeDeMedida, medidasPorUnidade, exibeTudoComoFracaoSemSepararUnidade, colocarUnidadePorExtenso: colocarUnidadePorExtenso, medidaDeFracaoEmCaixaBaixa: medidaDeFracaoEmCaixaBaixa, adicionaTotalEmFracaoEntreParenteses: adicionaTotalEmFracaoEntreParenteses, quantidadeEmUnidadeEspecificada: null, quantidadeEmFracaoEspecificada: null, ehAppPro: ehAppPro);
        }

        public string ObterTextoDeQuantidadeComUnidadeDeMedida(QuantidadeUnidadeMedidaDTO medidaDTO)
        {
            return ObterTextoDeQuantidadeComUnidadeDeMedida(
                medidaDTO.QuantidadeTotalEmMedidas, 
                medidaDTO.UnidadeDeMedida, 
                medidaDTO.MedidasPorUnidade, 
                medidaDTO.ExibeTudoComoFracaoSemSepararUnidade, 
                colocarUnidadePorExtenso: medidaDTO.ColocarUnidadePorExtenso, 
                medidaDeFracaoEmCaixaBaixa: medidaDTO.MedidaDeFracaoEmCaixaBaixa, 
                adicionaTotalEmFracaoEntreParenteses: medidaDTO.AdicionaTotalEmFracaoEntreParenteses, 
                ehAppPro: medidaDTO.EhAppPro);
        }

        public string TratarTextoDeUnidadeDeMedida(string unidadeDeMedida, int quantidadeFracionada)
        {
            return unidadeDeMedida.PluralizarPalavra(quantidadeFracionada);
        }

        #endregion Calculo quantidade estoque com fracionamento

        #region Replicações de Valores

        public void ReplicarNCMNBS(int idEstabelecimentoProdutoCategoria, string CodigoNCM, PessoaFisica pessoaFisicaLogada)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.CodigoNCM = CodigoNCM;
            }

            Domain.Pessoas.EstabelecimentoProdutoService.ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarSituacaoTributaria(int idEstabelecimentoProdutoCategoria, int? idSituacaoTributaria, decimal aliquotaICMSDoProdutoReplicado, PessoaFisica pessoaFisicaLogada)
        {
            bool existeProdutoQueNaoFoiReplicado = false;
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                bool aliquotasEstaoComAMesmaTributacao = (aliquotaICMSDoProdutoReplicado > 0 && (produto.AliquotaICMS ?? 0) > 0) || (aliquotaICMSDoProdutoReplicado == 0 && (produto.AliquotaICMS ?? 0) == 0);
                if (aliquotasEstaoComAMesmaTributacao)
                    produto.IdSituacaoTributaria = idSituacaoTributaria;
                else
                    existeProdutoQueNaoFoiReplicado = true;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);

            if (existeProdutoQueNaoFoiReplicado)
                ValidationHelper.Instance.AdicionarItemNotificacao("Alguns itens não foram replicados pois possuem o valor de alíquota incompatível com o tipo de situação tributária.\nPara resolver o problema, antes de replicar a situação tributária, replique o valor de alíquota.");
        }

        public void ReplicarAliquotaICMS(int idEstabelecimentoProdutoCategoria, decimal aliquotaICMS, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimanto)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                RedefinirSituacaoTributariaPelaAliquota(aliquotaICMS, produto, estabelecimanto);

                produto.AliquotaICMS = aliquotaICMS;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarAliquotaPIS(int idEstabelecimentoProdutoCategoria, decimal aliquotaPIS, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimanto)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.AliquotaPIS = aliquotaPIS;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarAliquotaCOFINS(int idEstabelecimentoProdutoCategoria, decimal aliquotaCOFINS, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimanto)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.AliquotaCOFINS = aliquotaCOFINS;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarOrigemProduto(int idEstabelecimentoProdutoCategoria, int origemProduto, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimento)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.OrigemProduto = origemProduto;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        private static void RedefinirSituacaoTributariaPelaAliquota(decimal aliquotaICMS, EstabelecimentoProduto produto, Estabelecimento estabelecimento)
        {
            bool aliquotaDoProdutoTemTributacaoDiferenteDaAliquotaAReplicar = (aliquotaICMS > 0 && produto.AliquotaICMS == 0) || (aliquotaICMS == 0 && produto.AliquotaICMS > 0);

            if (aliquotaDoProdutoTemTributacaoDiferenteDaAliquotaAReplicar)
            {
                ApagarSituacaoTributariaOuSugerirCasoSoTenhaUmaNaLista(produto, aliquotaICMS, estabelecimento);
            }
        }

        private static void ApagarSituacaoTributariaOuSugerirCasoSoTenhaUmaNaLista(EstabelecimentoProduto produto, decimal aliquotaICMS, Estabelecimento estabelecimento)
        {
            var listaSituacaoTributaria = Domain.NotaFiscalDoConsumidor.NfcSituacaoTributariaRepository.ObterSituacoesTributariasParaProduto(estabelecimento);

            if (aliquotaICMS == 0)
                listaSituacaoTributaria = listaSituacaoTributaria.Where(f => !f.EhTributado).ToList();
            else
                listaSituacaoTributaria = listaSituacaoTributaria.Where(f => f.EhTributado).ToList();

            if (listaSituacaoTributaria.Count == 1)
                produto.IdSituacaoTributaria = listaSituacaoTributaria[0].Id;
            else
                produto.IdSituacaoTributaria = null;
        }

        public void ReplicarEhNacional(int idEstabelecimentoProdutoCategoria, bool ehNacional, PessoaFisica pessoaFisicaLogada)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.ProdutoEhNacional = ehNacional;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarCodigoDeBarras(int idEstabelecimentoProdutoCategoria, string codigoDeBarras, PessoaFisica pessoaFisicaLogada)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.CodigoBarras = codigoDeBarras;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarEhFabricacaoPropria(int idEstabelecimentoProdutoCategoria, bool ehFabricacaoPropria, PessoaFisica pessoaFisicaLogada)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).ToList();

            foreach (var produto in produtos)
            {
                produto.FabricacaoPropria = ehFabricacaoPropria;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        public void ReplicarComissaoPorCategoria(int idEstabelecimentoProdutoCategoria, decimal valorAReplicar, PessoaFisica pessoaFisicaLogada, Estabelecimento estabelecimanto)
        {
            var produtos = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarProdutosPorEstabelecimentoProdutoCategoria(idEstabelecimentoProdutoCategoria).Where(p => p.Ativo).ToList();

            foreach (var produto in produtos)
            {
                produto.ValorComissaoRevenda = valorAReplicar;
            }

            ManterEstabelecimentoProduto(produtos, pessoaFisicaLogada);
        }

        #endregion Replicações de Valores

        public bool PermiteAlteracaoDeFracaoEDosagem(int idProduto)
        {
            var estabelecimentoProduto = Domain.Pessoas.EstabelecimentoProdutoRepository.Load(idProduto);

            return !ExisteMovimentacaoDeEstoqueImpedindoAlteracaoDeFracaoEDosagemNoProduto(idProduto)
                ||
                estabelecimentoProduto.UnidadeMedida.Id == (int)Enums.TipoUnidadeMedidaEnum.Unidade;
        }

        private bool ExisteMovimentacaoDeEstoqueImpedindoAlteracaoDeFracaoEDosagemNoProduto(int idEstabelecimentoProduto)
        {
            return Domain.Pessoas.EstabelecimentoMovimentacaoEstoqueRepository.ExisteMovimentacaoExcetoRegistroInicial(idEstabelecimentoProduto);
        }

        public decimal ObterPrecoPorTipoDeQuantidade(decimal precoUnitário, decimal precoPorFracao, TipoDeQuantidadeDeProduto tipoDeQuantidadeDeProduto)
        {
            return tipoDeQuantidadeDeProduto == TipoDeQuantidadeDeProduto.PorUnidade ? precoUnitário : precoPorFracao;
        }

        public decimal DefinirPreco(EstabelecimentoProduto estabelecimentoProduto, bool valorEhDiferenciadoParaProfissional, TipoDeQuantidadeDeProduto tipoDeQuantidade)
        {
            decimal precoPorUnidade = valorEhDiferenciadoParaProfissional ? estabelecimentoProduto.PrecoRevendaProfissional : estabelecimentoProduto.PrecoRevendaCliente;
            var preco = estabelecimentoProduto.ValorPorMedida != 0 ? estabelecimentoProduto.ValorPorMedida : precoPorUnidade;
            return ObterPrecoPorTipoDeQuantidade(precoPorUnidade, preco, tipoDeQuantidade);
        }

        public PermissaoParaProdutosDTO CarregarPermissoesParaProduto(Estabelecimento estabelecimento, PermissaoParaProdutosDTO permissoes)
        {
            if (estabelecimento.EstabelecimentoEhBaseadoEmUmModelo())
            {
                var permissoesExistentes = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterPermissoesParaProdutos(estabelecimento.IdEstabelecimento, estabelecimento.EstabelecimentoModelo().IdEstabelecimento);

                if (permissoesExistentes != null)
                    permissoes = new PermissaoParaProdutosDTO(permissoesExistentes);
            }

            return permissoes;
        }

        public void VerificarProdutosComMesmoCodigoDeIdentificacao(Estabelecimento estabelecimento, int idEstabelecimentoProduto, string codigoDeIdentificacao)
        {
            var codigoDeIdentificacaoOriginal = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable().Where(p => p.Id == idEstabelecimentoProduto).Select(p => p.CodigoDeIdentificacao).FirstOrDefault();

            if (codigoDeIdentificacao != codigoDeIdentificacaoOriginal)
            {
                var estabelecimentosProdutosComMesmoCodigoDeIdentificacao = Domain.Pessoas.EstabelecimentoProdutoRepository.ListarPorCodigoDeIdentificacao(estabelecimento, codigoDeIdentificacao);
                if (estabelecimentosProdutosComMesmoCodigoDeIdentificacao.Count > 0)
                {
                    var textoAviso = "Já existem os seguintes produtos cadastrados com o código de identificação '" + codigoDeIdentificacao + "':\n";
                    foreach (var ep in estabelecimentosProdutosComMesmoCodigoDeIdentificacao)
                    {
                        textoAviso = textoAviso + "- " + ep.Descricao + "\n";
                    }

                    ValidationHelper.Instance.AdicionarItemValidacao(textoAviso);
                }
            }
        }
    }
}