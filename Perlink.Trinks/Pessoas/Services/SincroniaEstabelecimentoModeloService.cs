﻿using log4net;
using NHibernate.Linq;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.Enums;
using Perlink.Trinks.ClubeDeAssinaturas;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.EstoqueComBaixaAutomatica;
using Perlink.Trinks.Pacotes;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.ProdutoEstoque;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Services
{

    public class SincroniaEstabelecimentoModeloService : BaseService, ISincroniaEstabelecimentoModeloService
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(SincroniaEstabelecimentoModeloService));

        private const string MensagemExcessoProdutosBackOffice = "Este modelo possui mais <b>Produtos</b> cadastrados do que o estimado para sincronia imediata. Nós agendaremos internamente a sincronia de <b>Produtos</b>";
        private const string MensagemExcessoServicosBackOffice = "Este modelo possui mais <b>Serviços</b> cadastrados do que o estimado para sincronia imediata. Nós agendaremos internamente a sincronia de <b>Serviços</b>";
        private const string MensagemExcessoPacotesBackOffice = "Este modelo possui mais <b>Pacotes</b> cadastrados do que o estimado para sincronia imediata. Nós agendaremos internamente a sincronia de <b>Pacotes</b>";
        private const string MensagemExcessoProdutoAreaPerlink = "Este modelo possui mais <b>Produtos</b> cadastrados do que o estimado para sincronia imediata. Nós agendaremos internamente uma sincronia de Produtos que será realizada de madrugada.";
        private const string MensagemExcessoServicoAreaPerlink = "Este modelo possui mais <b>Serviços</b> cadastrados do que o estimado para sincronia imediata. Nós agendaremos internamente uma sincronia de serviços que será realizada de madrugada pela rotina.";
        private const string MensagemExcessoPacoteAreaPerlink = "Este modelo possui mais <b>Pacotes</b> cadastrados do que o estimado para sincronia imediata. Nós agendaremos internamente uma sincronia de pacotes que será realizada de madrugada pela rotina.";

        public void AlterarModelo(AlterarModeloDTO dto)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(dto.IdEstabelecimentoUnidade);
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(dto.IdEstabelecimentoModelo);
            var quantidadeDeProdutosEstaDentroDoLimiteParaSincronia = QuantidadeDeProdutosEstaDentroDoLimiteParaSincronia(dto.IdEstabelecimentoModelo);
            var quantidadeDeServicosEstaDentroDoLimiteParaSincronia = QuantidadeDeServicosEstaDentroDoLimiteParaSincronia(dto.IdEstabelecimentoModelo);
            var quantidadePacotesEstaDentroDoLimiteParaSincronia = QuantidadeDePacotesEstaDentroDoLimiteParaSincronia(dto.IdEstabelecimentoModelo);

            if (quantidadeDeProdutosEstaDentroDoLimiteParaSincronia)
            {
                AlterarModeloEstabelecimentoProduto(estabelecimento, dto.IdEstabelecimentoModelo);
                AlterarModeloConfiguracaoSaidaDoEstoqueNoServicoEstabelecimento(dto.IdEstabelecimentoUnidade, dto.IdEstabelecimentoModelo);
            }
            else
            {
                Domain.Pessoas.DesassociarUnidadeDoModeloService.DesvincularProdutosDoEstabelecimentoUnidade(dto.IdEstabelecimentoUnidade);
                ValidationHelper.Instance.AdicionarItemNotificacao(MensagemExcessoProdutoAreaPerlink);
            }

            VerificarLimiteESincronizar(dto.IdEstabelecimentoUnidade, dto.IdEstabelecimentoModelo, quantidadeDeServicosEstaDentroDoLimiteParaSincronia, MensagemExcessoServicoAreaPerlink, AlterarModeloServicoEstabelecimento);

            AlterarModeloPlanosDoClubeDeAssinaturas(dto.IdEstabelecimentoUnidade, dto.IdEstabelecimentoModelo);
            estabelecimento.FranquiaEstabelecimento.AlterarModelo(estabelecimentoModelo);

            if (estabelecimento.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo)
                estabelecimento.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento = TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo;

            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);

            SincronizarComEstabelecimentoModelo(dto.IdEstabelecimentoUnidade, OrigemSincroniaDeModelo.AssociacaoPelaAreaPerlink,
                sincronizarOuAgendarSincroniaDeProdutos: quantidadeDeProdutosEstaDentroDoLimiteParaSincronia,
                sincronizarOuAgendarSincroniaDeServicos: quantidadeDeServicosEstaDentroDoLimiteParaSincronia,
                sincronizarOuAgendarSincroniaDePacotes: quantidadePacotesEstaDentroDoLimiteParaSincronia);

            SalvarQuemFezAAlteracaoDeModelo(dto);
        }

        public string ObterMensagemValidacaoParaVinculoDasPermissoes(int idEstabelecimento)
        {
            var quantidadeDeProdutosEstaDentroDoLimiteParaSincronia = QuantidadeDeProdutosEstaDentroDoLimiteParaSincronia(idEstabelecimento);
            var quantidadeDeServicosEstaDentroDoLimiteParaSincronia = QuantidadeDeServicosEstaDentroDoLimiteParaSincronia(idEstabelecimento);
            var quantidadePacotesEstaDentroDoLimiteParaSincronia = QuantidadeDePacotesEstaDentroDoLimiteParaSincronia(idEstabelecimento);

            string mensagemRetorno = string.Empty;
            if (!quantidadeDeProdutosEstaDentroDoLimiteParaSincronia)
                mensagemRetorno = MensagemExcessoProdutosBackOffice;
            if (!quantidadeDeServicosEstaDentroDoLimiteParaSincronia)
                mensagemRetorno = mensagemRetorno != string.Empty ? mensagemRetorno + " / " + MensagemExcessoServicosBackOffice : MensagemExcessoServicosBackOffice;
            if (!quantidadePacotesEstaDentroDoLimiteParaSincronia)
                mensagemRetorno = mensagemRetorno != string.Empty ? mensagemRetorno + " / " + MensagemExcessoPacotesBackOffice : MensagemExcessoPacotesBackOffice;


            return mensagemRetorno;
        }

        public bool QuantidadeDeProdutosEstaDentroDoLimiteParaSincronia(int idEstabelecimento)
        {
            var quantidadeDeProdutos = Domain.Pessoas.EstabelecimentoProdutoRepository.TotalDeProdutosDoEstabelecimentoAtivos(idEstabelecimento);
            var quantidadeMaximoProdutoParaSincronizar = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_produtos_no_modelo_para_sincronia_automatica).ObterValor();
            return quantidadeDeProdutos <= quantidadeMaximoProdutoParaSincronizar;
        }

        public bool QuantidadeDeServicosEstaDentroDoLimiteParaSincronia(int idEstabelecimento)
        {
            var quantidadeServico = Domain.Pessoas.ServicoEstabelecimentoRepository.RetornarQuantidadeDeServicosAtivosPorEstabelecimento(idEstabelecimento);
            var quantidadeMaximoServicoParaSincronizar = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_servicos_no_modelo_para_sincronia_automatica).ObterValor();
            return quantidadeServico <= quantidadeMaximoServicoParaSincronizar;
        }

        public bool QuantidadeDePacotesEstaDentroDoLimiteParaSincronia(int idEstabelecimento)
        {
            var quantidadePacote = Domain.Pacotes.PacoteRepository.RetornarQuantidadePacotesAtivosPorEstabelecimento(idEstabelecimento);
            var quantidadeMaximoPacoteParaSincronizar = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_pacotes_no_modelo_para_sincronia_automatica).ObterValor();
            return quantidadePacote <= quantidadeMaximoPacoteParaSincronizar;
        }
        private void SalvarQuemFezAAlteracaoDeModelo(AlterarModeloDTO dto)
        {
            var historio = new HistoricoAcaoUnidadeAoModelo(dto);
            Domain.Pessoas.HistoricoAcaoUnidadeAoModeloRepository.SaveNew(historio);
        }

        public void SincronizarComEstabelecimentoModelo(int idEstabelecimentoDestino, OrigemSincroniaDeModelo origem, bool sincronizarOuAgendarSincroniaDeProdutos = true, bool sincronizarOuAgendarSincroniaDeServicos = true, bool sincronizarOuAgendarSincroniaDePacotes = true)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);

            if (!estabelecimento.EstabelecimentoEhBaseadoEmUmModelo())
                throw new Exception("Este estabelecimento não possui modelo.");

            var estabelecimentoModelo = estabelecimento.EstabelecimentoModelo();

            bool ehModeloBaseadoEmModelo = estabelecimento.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.Modelo;

            if (sincronizarOuAgendarSincroniaDeProdutos)
            {
                SincronizarEstabelecimentoProdutosComModelo(idEstabelecimentoDestino, origem);
            }
            else if (ehModeloBaseadoEmModelo)
            {
                Domain.Pessoas.SincronizacaoEntreEstabelecimentosModelosFilaService.AgendarSincronizacao(estabelecimento.IdEstabelecimento, TipoSincronizacaoEnum.Produtos, origem);
            }
            else
            {
                Domain.Pessoas.SincronizacaoEstabelecimentosFilaService.AgendarSincronizacao(estabelecimentoModelo.IdEstabelecimento, TipoSincronizacaoEnum.Produtos, origem);
            }

            if (sincronizarOuAgendarSincroniaDeServicos)
            {
                SincronizarServicosEstabelecimentoComModelo(idEstabelecimentoDestino);
            }
            else if (ehModeloBaseadoEmModelo)
            {
                Domain.Pessoas.SincronizacaoEntreEstabelecimentosModelosFilaService.AgendarSincronizacao(estabelecimento.IdEstabelecimento, TipoSincronizacaoEnum.Servicos, origem);
            }
            else
            {
                Domain.Pessoas.SincronizacaoEstabelecimentosFilaService.AgendarSincronizacao(estabelecimentoModelo.IdEstabelecimento, TipoSincronizacaoEnum.Servicos, origem);
            }

            if (sincronizarOuAgendarSincroniaDePacotes)
            {
                SincronizarPacotesComModelo(idEstabelecimentoDestino);
            }
            else if (ehModeloBaseadoEmModelo)
            {
                Domain.Pessoas.SincronizacaoEntreEstabelecimentosModelosFilaService.AgendarSincronizacao(estabelecimento.IdEstabelecimento, TipoSincronizacaoEnum.Pacotes, origem);
            }
            else
            {
                Domain.Pessoas.SincronizacaoEstabelecimentosFilaService.AgendarSincronizacao(estabelecimentoModelo.IdEstabelecimento, TipoSincronizacaoEnum.Pacotes, origem);
            }

            SincronizarAssinaturaRecorrenteComModelo(idEstabelecimentoDestino);
            SincronizarPlanosDoClubeDeAssinaturaComModelo(idEstabelecimentoDestino);
        }

        private (bool, Estabelecimento) ObterEstabelecimentoDoModelo(int idEstabelecimentoModelo, TipoSincronizacaoEnum tipo)
        {
            var estabelecimentoSemPermissao = ObterEstabelecimentoSemPermissao(idEstabelecimentoModelo);
            var verificaCamposFlexiveis = true;

            if (estabelecimentoSemPermissao != null)
                return (verificaCamposFlexiveis, estabelecimentoSemPermissao);

            var estabelecimentoComTodasAsPermissoesFalsasParaEsseTipo = ObterEstabelecimentoComTodasAsPermissoesFalsasParaEsseTipo(idEstabelecimentoModelo, tipo);

            if (estabelecimentoComTodasAsPermissoesFalsasParaEsseTipo != null)
                return (verificaCamposFlexiveis, estabelecimentoComTodasAsPermissoesFalsasParaEsseTipo);

            verificaCamposFlexiveis = false;
            return (verificaCamposFlexiveis, ObterPrimeiroEstabelecimentoDoModelo(idEstabelecimentoModelo));
        }

        private Estabelecimento ObterEstabelecimentoSemPermissao(int idEstabelecimentoModelo)
        {
            var queryPermissao = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.Queryable();
            var queryEstabelecimentoUnidade = Domain.Pessoas.EstabelecimentoRepository.Queryable();

            var idUnidadesComConfiguracao = (from perm in queryPermissao
                                             join estab in queryEstabelecimentoUnidade on perm.IdEstabelecimentoUnidade equals estab.IdEstabelecimento
                                             where estab.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo
                                             && estab.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo
                                             select estab.IdEstabelecimento
                            ).ToList();

            var idDasUnidadesDoModelo = queryEstabelecimentoUnidade
                .Where(u => u.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo
                            && u.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo)
                .Select(u => u.IdEstabelecimento)
                .ToList();

            var idEstabelecimentoSemPermissao = idDasUnidadesDoModelo.Except(idUnidadesComConfiguracao).FirstOrDefault();

            Estabelecimento estabelecimento = null;

            if (idEstabelecimentoSemPermissao > 0)
                estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoSemPermissao);

            return estabelecimento;
        }

        private Estabelecimento ObterEstabelecimentoComTodasAsPermissoesFalsasParaEsseTipo(int idEstabelecimentoModelo, TipoSincronizacaoEnum tipo)
        {
            var queryPermissao = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.Queryable();
            var queryEstabelecimentoUnidade = Domain.Pessoas.EstabelecimentoRepository.Queryable();

            Estabelecimento estabelecimento = null;

            if (tipo == TipoSincronizacaoEnum.Pacotes)
            {

                estabelecimento = (from perm in queryPermissao
                                   join estab in queryEstabelecimentoUnidade on perm.IdEstabelecimentoUnidade equals estab.IdEstabelecimento
                                   where estab.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo &&
                                         estab.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo &&
                                         perm.PermissoesParaPacotes.InativarDoModelo == false &&
                                         perm.PermissoesParaPacotes.ComissaoDoModelo == false &&
                                         perm.PermissoesParaPacotes.ValorVendaDoModelo == false
                                   select estab).FirstOrDefault();

            }
            else if (tipo == TipoSincronizacaoEnum.Produtos)
            {

                estabelecimento = (from perm in queryPermissao
                                   join estab in queryEstabelecimentoUnidade on perm.IdEstabelecimentoUnidade equals estab.IdEstabelecimento
                                   where estab.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo &&
                                         estab.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo &&
                                         perm.PermissoesParaProdutos.InativarDoModelo == false &&
                                         perm.PermissoesParaProdutos.VendaConsumoDoModelo == false &&
                                         perm.PermissoesParaProdutos.ControleEstoqueDoModelo == false &&
                                         perm.PermissoesParaProdutos.InformacoesFiscaisDoModelo == false
                                   select estab).FirstOrDefault();

            }
            else if (tipo == TipoSincronizacaoEnum.Servicos)
            {

                estabelecimento = (from perm in queryPermissao
                                   join estab in queryEstabelecimentoUnidade on perm.IdEstabelecimentoUnidade equals estab.IdEstabelecimento
                                   where estab.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo &&
                                         estab.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo &&
                                         perm.PermissoesParaServicos.InativarDoModelo == false &&
                                         perm.PermissoesParaServicos.PrecoDuracaoDoModelo == false &&
                                         perm.PermissoesParaServicos.PromocaoDoModelo == false &&
                                         perm.PermissoesParaServicos.SaidaAutomaticaCustoOperacionalDoModelo == false &&
                                         perm.PermissoesParaServicos.EdicaoFiscalDoModelo == false
                                   select estab).FirstOrDefault();

            }
            else if (tipo == TipoSincronizacaoEnum.ClubeDeAssinaturas)
            {

                estabelecimento = (from perm in queryPermissao
                                   join estab in queryEstabelecimentoUnidade on perm.IdEstabelecimentoUnidade equals estab.IdEstabelecimento
                                   where estab.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo &&
                                         estab.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo &&
                                         perm.PermissoesParaClubeDeAssinaturas.InativarDoModelo == false &&
                                         perm.PermissoesParaClubeDeAssinaturas.PrecoPeriodoEncerramentoDoModelo == false
                                   select estab).FirstOrDefault();

            }

            return estabelecimento;
        }

        private static Estabelecimento ObterPrimeiroEstabelecimentoDoModelo(int idEstabelecimentoModelo)
        {
            return Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    .FirstOrDefault(
                        f =>
                            f.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.FranqueadoBaseadaEmUmModelo &&
                            f.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo);
        }

        private void AlterarModeloEstabelecimentoProduto(Estabelecimento estabelecimento, int idEstabelecimentoModelo)
        {
            bool carregarValoresPropriedades = estabelecimento.ExibeCamposDeClassificacao();

            var estabelecimentoProdutos = Domain.Pessoas.EstabelecimentoProdutoRepository
                                        .ListarPorEstabelecimentoComFetch(estabelecimento.IdEstabelecimento, somenteAtivos: false, incluiValoresPropriedades: carregarValoresPropriedades);

            var estabelecimentoProdutosModelos = Domain.Pessoas.EstabelecimentoProdutoRepository
                                        .ListarPorEstabelecimentoComFetch(idEstabelecimentoModelo, somenteAtivos: true, incluiValoresPropriedades: carregarValoresPropriedades);

            foreach (var estabelecimentoProduto in estabelecimentoProdutos)
            {
                var modelo = estabelecimentoProdutosModelos.FirstOrDefault(f => f.Descricao == estabelecimentoProduto.Descricao); // Um possível problema, pode causar um bug caso tenham dois produtos com mesmo nome

                if (modelo == null && estabelecimentoProduto.EstabelecimentoProdutoModelo != null)
                    estabelecimentoProduto.RemoverModelo();
                else
                    estabelecimentoProduto.AssociarModelo(modelo);

                AlterarModeloDasPropriedadesDoProduto(estabelecimentoProduto, modelo);
            }
        }

        private void AlterarModeloDasPropriedadesDoProduto(EstabelecimentoProduto produto, EstabelecimentoProduto produtoModelo)
        {
            foreach (var vp in produto.ValoresDePropriedades)
            {
                if (produtoModelo == null)
                {
                    vp.ValorPropriedadeDoProdutoModelo = null;
                }
                else
                {
                    var vpDoProdutoModelo = produtoModelo.ValoresDePropriedades.FirstOrDefault(p => p.PropriedadePadrao == vp.PropriedadePadrao);
                    vp.ValorPropriedadeDoProdutoModelo = vpDoProdutoModelo;
                }
            }
        }

        private void AlterarModeloPacotes(int idEstabelecimento, int idEstabelecimentoModelo)
        {
            var pacotesEstabelecimento = Domain.Pacotes.PacoteRepository.ObterPorEstabelecimento(idEstabelecimento);
            var pacotesModelo = Domain.Pacotes.PacoteRepository.ObterAtivosPorEstabelecimento(new Estabelecimento
            {
                IdEstabelecimento = idEstabelecimentoModelo
            });

            foreach (var pacote in pacotesEstabelecimento)
            {
                var modelo = pacotesModelo.FirstOrDefault(f => String.Equals(f.Nome, pacote.Nome, StringComparison.CurrentCultureIgnoreCase));

                if (modelo == null && pacote.PacoteModelo != null)
                    pacote.DesassociarModelo();
                else
                    pacote.AssociarModelo(modelo);
            }

            InativarPacotesRepetidos(pacotesEstabelecimento);
        }

        private void InativarPacotesRepetidos(IList<Pacote> pacotesEstabelecimento)
        {
            var pacotesRepetidos = pacotesEstabelecimento.GroupBy(f => f.Descricao).Where(f => f.Count() > 1);

            foreach (var pacoteRepetido in pacotesRepetidos)
            {
                var pacotes = pacoteRepetido.Skip(1);
                foreach (var pacote in pacotes)
                    pacote.Inativar();
            }
        }

        private void AlterarModeloPlanosDoClubeDeAssinaturas(int idEstabelecimento, int idEstabelecimentoModelo)
        {
            var planosDaUnidade = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterPlanosAtivos(idEstabelecimento);
            var planosDoModelo = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterPlanosAtivos(idEstabelecimentoModelo);

            foreach (var planoCliente in planosDaUnidade)
            {
                var modelo = planosDoModelo.FirstOrDefault(f => String.Equals(f.DadosDoPlanoCliente.Nome, planoCliente.DadosDoPlanoCliente.Nome, StringComparison.CurrentCultureIgnoreCase));

                if (modelo == null && planoCliente.PlanoClienteModelo != null)
                    planoCliente.DesassociarModelo();
                else
                    planoCliente.AssociarModelo(modelo);
            }

            InativarPlanosDoClubeRepetidos(planosDaUnidade);
        }

        private void InativarPlanosDoClubeRepetidos(IList<PlanoCliente> planosEstabelecimento)
        {
            var planosRepetidos = planosEstabelecimento.GroupBy(f => f.DadosDoPlanoCliente.Nome).Where(f => f.Count() > 1);

            foreach (var planoRepetido in planosRepetidos)
            {
                var planos = planoRepetido.Skip(1);
                foreach (var plano in planos)
                    plano.InativarPlano();
            }
        }

        private void AlterarModeloServicoEstabelecimento(int idEstabelecimento, int idEstabelecimentoModelo)
        {
            var servicosEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository
                                            .ListarPorEstabelecimento(idEstabelecimento);

            var servicosEstabelecimentoModelo = Domain.Pessoas.ServicoEstabelecimentoRepository
                                                .ListarAtivosPorEstabelecimento(idEstabelecimentoModelo);

            foreach (var se in servicosEstabelecimento)
            {
                var modelo = servicosEstabelecimentoModelo.FirstOrDefault(f => f.Nome == se.Nome);

                if (modelo == null && se.ServicoEstabelecimentoModelo != null)
                    se.DesassociarModelo();
                else
                    se.AssociarModelo(modelo);
            }
        }

        private void AlterarModeloConfiguracaoSaidaDoEstoqueNoServicoEstabelecimento(int idEstabelecimentoDestino, int idEstabelecimentoModelo)
        {

            var itensEstabelecimentosDestino = Domain.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomaticaRepository.ListarAtivosPorEstabelecimento(idEstabelecimentoDestino);
            var itensEstabelecimentosModelo = Domain.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomaticaRepository.ListarAtivosPorEstabelecimento(idEstabelecimentoModelo);
            var idPessoaTrinksInterno = new ParametrosTrinks<int>(ParametrosTrinksEnum.id_pessoa_conta_trinks_interno).ObterValor();
            var pessoaTrinksInterno = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaTrinksInterno);

            foreach (var itemDestino in itensEstabelecimentosDestino)
            {

                var itemModelo = itensEstabelecimentosModelo
                        .FirstOrDefault(i => i.ServicoEstabelecimento.Nome == itemDestino.ServicoEstabelecimento.Nome
                        && i.EstabelecimentoProduto.Descricao == itemDestino.EstabelecimentoProduto.Descricao);


                if (itemModelo != null)
                {
                    itemDestino.IdItemConfiguradoParaBaixaAutomaticaModelo = itemModelo.Id;
                    itemDestino.DataHoraUltimaSincroniaModelo = null;
                }
                else
                {
                    itemDestino.MarcarComoExcluido(pessoaTrinksInterno);
                }

                Domain.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomaticaRepository.SalvarNovoOuAtualizarNoFlush(itemDestino);
            }

            Domain.EstoqueComBaixaAutomatica.ItemConfiguradoParaBaixaAutomaticaRepository.Flush();
        }

        private void VerificarLimiteESincronizar(int idEstabelecimentoUnidade, int idEstabelecimentoModelo, bool dentroDoLimite, string mensagemExcessao, Action<int, int> acaoSincronizacao)
        {
            if (dentroDoLimite)
            {
                acaoSincronizacao(idEstabelecimentoUnidade, idEstabelecimentoModelo);
            }
            else
            {
                ValidationHelper.Instance.AdicionarItemNotificacao(mensagemExcessao);
            }
        }

        #region Pacotes

        public List<Pacote> ListarPacotesNaoSincronizados(int idEstabelecimentoModelo, int idEstabelecimentoDestino = 0)
        {
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);

            Estabelecimento estabelecimento = null;
            bool verificaCamposFlexiveis = true;

            if (idEstabelecimentoDestino > 0)
            {
                estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            }
            else
            {
                (bool verificaItensFlexiveis, Estabelecimento unidade) dados = ObterEstabelecimentoDoModelo(idEstabelecimentoModelo, TipoSincronizacaoEnum.Pacotes);

                estabelecimento = dados.unidade;
                verificaCamposFlexiveis = dados.verificaItensFlexiveis;
            }

            if (idEstabelecimentoDestino == 0 && estabelecimento == null)
                return null;

            var pacotesModelo = Domain.Pacotes.PacoteRepository.ObterPacotesParaSincroniaPorEstabelecimento(estabelecimentoModelo);
            log.Info($"PacotesModelo: {pacotesModelo.Count}");

            IEnumerable<Pacote> retorno;
            if (estabelecimento != null)
            {
                var pacotes = Domain.Pacotes.PacoteRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento);

                retorno = ObterPacotesExistentes(pacotes, pacotesModelo, estabelecimentoModelo, estabelecimento, verificaCamposFlexiveis);
                log.Info($"Pacotes: {pacotes.Count}");
            }
            else
                retorno = pacotesModelo;

            return retorno.Where(f => f != null).ToList();
        }

        public List<Pacote> ListarAssinaturaRecorrenteNaoSincronizados(int idEstabelecimentoModelo, int idEstabelecimentoDestino = 0)
        {
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);

            Estabelecimento estabelecimento = null;
            bool verificaCamposFlexiveis = true;

            if (idEstabelecimentoDestino > 0)
            {
                estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            }
            else
            {
                (bool verificaItensFlexiveis, Estabelecimento unidade) dados = ObterEstabelecimentoDoModelo(idEstabelecimentoModelo, TipoSincronizacaoEnum.Pacotes);

                estabelecimento = dados.unidade;
                verificaCamposFlexiveis = dados.verificaItensFlexiveis;
            }

            if (idEstabelecimentoDestino == 0 && estabelecimento == null)
                return null;

            var pacotesModelo = Domain.Pacotes.PacoteRepository.ObterPacotesDeAssinaturaRecorrentePorEstabelecimento(estabelecimentoModelo);
            log.Info($"PacotesModelo: {pacotesModelo.Count}");

            IEnumerable<Pacote> retorno;
            if (estabelecimento != null)
            {
                IList<Pacote> pacotes = Domain.Pacotes.PacoteRepository.ObterQueryPacoteDeAssinaturaPorEstabelecimento(estabelecimento.IdEstabelecimento).ToList();

                retorno = ObterPacotesExistentes(pacotes, pacotesModelo, estabelecimentoModelo, estabelecimento, false);
            }
            else
                retorno = pacotesModelo;

            log.Info($"Pacotes: {retorno.Count()}");

            return retorno.Where(f => f != null).ToList();
        }

        private IEnumerable<Pacote> ObterPacotesExistentes(IList<Pacote> pacotes, IList<Pacote> pacotesModelo, Estabelecimento estabelecimentoModelo, Estabelecimento estabelecimentoUnidade, bool verificaCamposFlexiveis)
        {

            List<Pacote> pacotesExistentes = null;

            pacotesExistentes = pacotes
                .Where(f => f.PacoteModelo == null ||
                            (f.Ativo != f.PacoteModelo.Ativo ||
                             f.Descricao != f.PacoteModelo.Descricao ||
                             f.Nome != f.PacoteModelo.Nome ||
                             (verificaCamposFlexiveis && f.Valor != f.PacoteModelo.Valor) ||
                             f.ValidadeDeUsoEmMeses != f.PacoteModelo.ValidadeDeUsoEmMeses ||
                             f.ItensPacote.Count != f.PacoteModelo.ItensPacote.Count ||
                             f.ItensPacote.Sum(g => g.Quantidade) != f.PacoteModelo.ItensPacote.Sum(g => g.Quantidade) ||
                             f.ItensPacote.Sum(g => g.ValorUnitario) != f.PacoteModelo.ItensPacote.Sum(g => g.ValorUnitario) ||
                             f.PacoteModelo.Estabelecimento != estabelecimentoModelo ||
                             (verificaCamposFlexiveis && f.PercentualComissaoProfissional != f.PacoteModelo.PercentualComissaoProfissional)))
                .Select(f => f.PacoteModelo).ToList();



            var novosPacotes =
                pacotesModelo.Where(f => pacotes.All(g => g.PacoteModelo == null || g.PacoteModelo.Id != f.Id));

            var retorno = pacotesExistentes.Union(novosPacotes);

            return retorno;
        }

        public void SincronizarPacotesComModelo(int idEstabelecimentoDestino)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);

            var estabelecimentoModelo = estabelecimento.EstabelecimentoModelo();
            if (estabelecimentoModelo == null)
                throw new Exception("Este estabelecimento não possui estabelecimento modelo.");

            AlterarModeloPacotes(idEstabelecimentoDestino, estabelecimentoModelo.IdEstabelecimento);

            var pacotesNaoSincronizados =
                ListarPacotesNaoSincronizados(estabelecimentoModelo.IdEstabelecimento, idEstabelecimentoDestino);

            SincronizarPacotesComModelo(pacotesNaoSincronizados, idEstabelecimentoDestino);
        }

        public void SincronizarAssinaturaRecorrenteComModelo(int idEstabelecimentoDestino)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);

            var estabelecimentoModelo = estabelecimento.EstabelecimentoModelo();
            if (estabelecimentoModelo == null)
                throw new Exception("Este estabelecimento não possui estabelecimento modelo.");

            var pacotesNaoSincronizados =
                ListarAssinaturaRecorrenteNaoSincronizados(estabelecimentoModelo.IdEstabelecimento, idEstabelecimentoDestino);

            SincronizarPacotesComModelo(pacotesNaoSincronizados, idEstabelecimentoDestino);
        }

        private void SincronizarPacotesComModelo(IEnumerable<Pacote> lista, int idEstabelecimentoDestino)
        {
            var pacotes = Domain.Pacotes.PacoteRepository.ObterTodosPacotesPorEstabelecimento(idEstabelecimentoDestino);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            var idEstabelecimentoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdDoModeloDoEstabelecimento(idEstabelecimentoDestino);

            var permissaoPacote = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterPermissoesParaPacote(idEstabelecimentoDestino, idEstabelecimentoModelo);

            bool podeSincronizarValorDeVenda = permissaoPacote != null && permissaoPacote.ValorVendaDoModelo;
            bool podeSincronizarComissao = permissaoPacote != null && permissaoPacote.ComissaoDoModelo;
            bool podeCriarEditarInativarPacotesDaUnidade = permissaoPacote != null && permissaoPacote.CriarEditarInativarDaUnidade;
            bool podeSicronizarEdicaoFiscalPacote = permissaoPacote != null && permissaoPacote.EdicaoFiscalDoModelo;

            log.Info($"Iniciando alteração: {lista.Count()} pacotes");
            foreach (var pacoteModelo in lista)
            {
                var pacote = pacotes.FirstOrDefault(f => f.PacoteModelo == pacoteModelo) ??
                             new Pacote();

                pacote.Nome = pacoteModelo.Nome;
                pacote.Ativo = pacoteModelo.Ativo;
                pacote.PacoteModelo = pacoteModelo;
                pacote.Descricao = pacoteModelo.Descricao;
                pacote.ValidadeDeUsoEmMeses = pacoteModelo.ValidadeDeUsoEmMeses;
                pacote.Estabelecimento = estabelecimento;
                pacote.EhPacoteDeAssinatura = pacoteModelo.EhPacoteDeAssinatura;

                if (!podeSincronizarValorDeVenda)
                    pacote.Valor = pacoteModelo.Valor;

                if (!podeSincronizarComissao)
                    pacote.PercentualComissaoProfissional = pacoteModelo.PercentualComissaoProfissional;

                pacote.ItensPacote.Clear();

                var todosOsProdutosEServicosEstaoNoDestino = true;

                foreach (var item in pacoteModelo.ItensPacote)
                {
                    var itemPacoteProdutoModelo = item as ItemPacoteProduto;
                    var itemPacoteServicoModelo = item as ItemPacoteServico;

                    if (itemPacoteProdutoModelo != null)
                    {
                        var estabelecimentoProduto = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                                .FirstOrDefault(
                                    f =>
                                        f.Estabelecimento.IdEstabelecimento == idEstabelecimentoDestino &&
                                        f.Descricao == itemPacoteProdutoModelo.EstabelecimentoProduto.Descricao &&
                                        f.Ativo);

                        if (estabelecimentoProduto != null)
                        {
                            var itemNovo = new ItemPacoteProduto
                            {
                                EstabelecimentoProduto = estabelecimentoProduto,
                                Pacote = pacote,
                                Quantidade = itemPacoteProdutoModelo.Quantidade,
                                ValorUnitario = itemPacoteProdutoModelo.ValorUnitario
                            };

                            if (!podeSicronizarEdicaoFiscalPacote)
                            {
                                itemNovo.ValorUnitarioFiscal = itemPacoteProdutoModelo.ValorUnitarioFiscal;
                            }

                            pacote.ItensPacote.Add(itemNovo);
                        }
                        else
                        {
                            todosOsProdutosEServicosEstaoNoDestino = false;
                            break; // Não tendo pelo menos um produto no estab. destino, pula para o próximo pacote.
                        }
                    }
                    else if (itemPacoteServicoModelo != null)
                    {
                        var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Queryable()
                                    .FirstOrDefault(
                                        f =>
                                            f.Estabelecimento.IdEstabelecimento == idEstabelecimentoDestino &&
                                            f.Nome == itemPacoteServicoModelo.ServicoEstabelecimento.Nome &&
                                            f.Ativo);

                        if (servicoEstabelecimento != null)
                        {
                            var itemNovo = new ItemPacoteServico
                            {
                                Pacote = pacote,
                                Quantidade = itemPacoteServicoModelo.Quantidade,
                                ValorUnitario = itemPacoteServicoModelo.ValorUnitario,
                                ServicoEstabelecimento = servicoEstabelecimento
                            };

                            if (!podeSicronizarEdicaoFiscalPacote)
                            {
                                itemNovo.ValorUnitarioFiscal = itemPacoteServicoModelo.ValorUnitarioFiscal;
                            }

                            pacote.ItensPacote.Add(itemNovo);
                        }
                        else
                        {
                            todosOsProdutosEServicosEstaoNoDestino = false;
                            break; // Não tendo pelo menos um serviço no estab. destino, pula para o próximo pacote.
                        }
                    }
                }

                if (pacote.Id == 0 && pacote.Ativo && todosOsProdutosEServicosEstaoNoDestino)
                    Domain.Pacotes.PacoteRepository.SaveNewNoFlush(pacote);
            }
            Domain.Pacotes.PacoteRepository.Flush();

            log.Info($"Finalizando alteração: {lista.Count()} pacotes");

            if (!podeCriarEditarInativarPacotesDaUnidade)
            {
                var pacotesCriadosNaUnidade = pacotes.Where(pac => pac.PacoteModelo == null);
                foreach (var pacote in pacotesCriadosNaUnidade)
                {
                    pacote.Inativar();
                    Domain.Pacotes.PacoteRepository.UpdateNoFlush(pacote);
                }
            }

            Domain.Pacotes.PacoteRepository.Flush();
            log.Info("Pacotes sincronizados com sucesso");
        }

        #endregion Pacotes

        #region Produtos


        public bool VerificarSePodeVisualizarModalProdutosParaSincronizar(int idEstabelecimentoModelo)
        {
            var qtdMaxParaVisualizarModal = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_produtos_para_agendamento_sincronia_automatico).ObterValor();
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);
            return Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                    .Where(f => f.Estabelecimento == estabelecimentoModelo).Count() > qtdMaxParaVisualizarModal;
        }

        public List<EstabelecimentoProduto> ListarEstabelecimentoProdutosNaoSincronizados(int idEstabelecimentoModelo,
            int idEstabelecimentoDestino = 0)
        {
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);

            Estabelecimento estabelecimento = null;
            bool verificaCamposFlexiveis = true;

            if (idEstabelecimentoDestino > 0)
            {
                estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            }
            else
            {
                (bool verificaItensFlexiveis, Estabelecimento unidade) = ObterEstabelecimentoDoModelo(idEstabelecimentoModelo, TipoSincronizacaoEnum.Produtos);

                estabelecimento = unidade;
                verificaCamposFlexiveis = verificaItensFlexiveis;
            }

            if (idEstabelecimentoDestino == 0 && estabelecimento == null)
                return null;


            var retorno = new List<EstabelecimentoProduto>();
            if (estabelecimento != null)
            {

                //ESTES QUERYABLE COM TOLIST SERVER PARA CARREGAR AS INFORMAÇÕES EM MEMORIA PARA DIMINUIR A QUANTIDADE ACESSOS QUE O CÓDIGO FARÁ AO BANCO
                var estabelecimentoProdutoQueryable = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                    .Where(f => f.Estabelecimento == estabelecimento);
                estabelecimentoProdutoQueryable.Fetch(f => f.EstabelecimentoProdutoCategoria).ToFuture();
                estabelecimentoProdutoQueryable.Fetch(f => f.EstabelecimentoProdutoModelo).ToFuture();

                var estabelecimentoProdutos = estabelecimentoProdutoQueryable.ToList();

                log.Info($"[Sincronia] {estabelecimentoProdutos.Count()} produtos obtidos do estabelecimento {estabelecimento.NomeDeExibicaoNoPortal}");

                var estabelecimentoProdutosModelo = FiltrarProdutosDoEstabelecimentoModeloQueSeraoSincronizados(estabelecimentoModelo).Fetch(f => f.EstabelecimentoProdutoCategoria).ToFuture().ToList();
                log.Info($"[Sincronia] {estabelecimentoProdutosModelo.Count()} produtos obtidos do modelo {estabelecimentoModelo.NomeDeExibicaoNoPortal}");

                var produtosExistentes = ObterProdutosExistentes(estabelecimentoProdutos, estabelecimento, idEstabelecimentoDestino, estabelecimentoModelo.IdEstabelecimento, verificaCamposFlexiveis);
                log.Info($"[Sincronia] {produtosExistentes.Count()} produtos precisam de sincronia {estabelecimento.NomeDeExibicaoNoPortal}");


                // Cria um conjunto com os IDs dos produtos do modelo que já existem no estabelecimento
                var idsModelosExistentes = new HashSet<int>(
                    estabelecimentoProdutos
                        .Where(g => g.EstabelecimentoProdutoModelo != null)
                        .Select(g => g.EstabelecimentoProdutoModelo.Id)
                );

                // Filtra os produtos do modelo que não existem no estabelecimento usando o conjunto
                var novosProdutos = estabelecimentoProdutosModelo
                    .Where(f => !idsModelosExistentes.Contains(f.Id))
                    .ToList();

                //var novosProdutos =
                //    estabelecimentoProdutosModelo.Where(
                //        f => !estabelecimentoProdutos.Any(g => g.EstabelecimentoProdutoModelo != null && g.EstabelecimentoProdutoModelo.Id == f.Id)).ToList();
                log.Info($"[Sincronia] {novosProdutos.Count()} produtos ainda não existem no {estabelecimento.NomeDeExibicaoNoPortal}");

                retorno.AddRange(produtosExistentes);
                retorno.AddRange(novosProdutos);

                if (estabelecimento.ExibeCamposDeClassificacao())
                {
                    IncluirProdutosModelosComValoresPropriedadesNaoSincronizadas(retorno, estabelecimento, estabelecimentoModelo);
                }
            }
            else
            {
                var estabelecimentoProdutoModelo = FiltrarProdutosDoEstabelecimentoModeloQueSeraoSincronizados(estabelecimentoModelo);
                retorno = estabelecimentoProdutoModelo.ToList();
                log.Info($"[Sincronia] {retorno.Count()} ProdutosDoEstabelecimentoModeloQueSeraoSincronizados do modelo {estabelecimentoModelo.NomeDeExibicaoNoPortal}");
            }
            return retorno;
        }

        private List<EstabelecimentoProduto> ObterProdutosExistentes(List<EstabelecimentoProduto> estabelecimentoProduto, Estabelecimento estabelecimento, int idEstabelecimentoDestino, int idEstabelecimentoModelo, bool verificaCamposFlexiveis)
        {

            var permiteAlterarValorDeCompra = estabelecimento.FranquiaEstabelecimento.Franquia.PermiteAlterarValorDeCompra;
            var permiteAlterarDadosFiscais = !estabelecimento.FranquiaEstabelecimento.Franquia.PermiteAlterarDadosFiscais;

            var produtosExistentes = new List<EstabelecimentoProduto>();
            if (verificaCamposFlexiveis)
            {
                produtosExistentes = estabelecimentoProduto
                    .Where(f => (f.EstabelecimentoProdutoModelo != null && f.EstabelecimentoProdutoModelo.Estabelecimento.IdEstabelecimento == idEstabelecimentoModelo) &&
                                (f.Ativo != f.EstabelecimentoProdutoModelo.Ativo ||
                                 f.Descricao != f.EstabelecimentoProdutoModelo.Descricao ||
                                 f.DescricaoComplementar != f.EstabelecimentoProdutoModelo.DescricaoComplementar ||
                                 //f.DataUltimaAlteracao != f.EstabelecimentoProdutoModelo.DataUltimaAlteracao || Nâo incluir esta propriedade pois ela não fica igual
                                 f.Ativo != f.EstabelecimentoProdutoModelo.Ativo ||
                                 f.PermitirRevenda != f.EstabelecimentoProdutoModelo.PermitirRevenda ||
                                 f.PrecoRevendaCliente != f.EstabelecimentoProdutoModelo.PrecoRevendaCliente ||
                                 //f.PrecoRevendaProfissional != f.EstabelecimentoProdutoModelo.PrecoRevendaProfissional || Não incluir esta propriedade pois o estabelecimento pode alterar
                                 f.EstabelecimentoFabricanteProduto.Nome != f.EstabelecimentoProdutoModelo.EstabelecimentoFabricanteProduto.Nome ||
                                 f.MedidasPorUnidade != f.EstabelecimentoProdutoModelo.MedidasPorUnidade ||
                                 f.ProdutoPadrao != f.EstabelecimentoProdutoModelo.ProdutoPadrao ||
                                 f.TipoComissao != f.EstabelecimentoProdutoModelo.TipoComissao ||
                                 (f.CodigoBarras != f.EstabelecimentoProdutoModelo.CodigoBarras) ||
                                 (f.CodigoBarras == null && f.EstabelecimentoProdutoModelo.CodigoBarras != null) ||
                                 (f.CodigoBarras != null && f.EstabelecimentoProdutoModelo.CodigoBarras == null) ||
                                 ((f.CodigoDeIdentificacao == null && f.EstabelecimentoProdutoModelo.CodigoDeIdentificacao != null) ||
                                  (f.CodigoDeIdentificacao != f.EstabelecimentoProdutoModelo.CodigoDeIdentificacao)) ||
                                 f.EstabelecimentoProdutoCategoria.Nome != f.EstabelecimentoProdutoModelo.EstabelecimentoProdutoCategoria.Nome ||
                                 (f.CustoMedio != f.EstabelecimentoProdutoModelo.CustoMedio) ||
                                 f.CodigoNCM != f.EstabelecimentoProdutoModelo.CodigoNCM ||
                                 (f.CodigoNCM != null && f.EstabelecimentoProdutoModelo.CodigoNCM == null) ||
                                 (f.CodigoNCM == null && f.EstabelecimentoProdutoModelo.CodigoNCM != null) ||
                                 (!permiteAlterarValorDeCompra && (f.ValorDeCompra != f.EstabelecimentoProdutoModelo.ValorDeCompra)) ||
                                 //f.EstoqueMinimo != f.EstabelecimentoProdutoModelo.EstoqueMinimo ||
                                 f.CEST != f.EstabelecimentoProdutoModelo.CEST ||
                                 permiteAlterarDadosFiscais &&
                                 (f.IdSituacaoTributaria != f.EstabelecimentoProdutoModelo.IdSituacaoTributaria ||
                                 (f.IdSituacaoTributaria != null && f.EstabelecimentoProdutoModelo.IdSituacaoTributaria == null) ||
                                 (f.IdSituacaoTributaria == null && f.EstabelecimentoProdutoModelo.IdSituacaoTributaria != null) ||
                                 f.AliquotaICMS != f.EstabelecimentoProdutoModelo.AliquotaICMS ||
                                 f.OrigemProduto != f.EstabelecimentoProdutoModelo.OrigemProduto ||
                                 f.AliquotaPIS != f.EstabelecimentoProdutoModelo.AliquotaPIS ||
                                 f.AliquotaCOFINS != f.EstabelecimentoProdutoModelo.AliquotaCOFINS ||
                                 f.CodigoNCM != f.EstabelecimentoProdutoModelo.CodigoNCM ||
                                 (f.CodigoNCM != null && f.EstabelecimentoProdutoModelo.CodigoNCM == null) ||
                                 (f.CodigoNCM == null && f.EstabelecimentoProdutoModelo.CodigoNCM != null) ||
                                 f.FabricacaoPropria != f.EstabelecimentoProdutoModelo.FabricacaoPropria)))
                    .Select(f => f.EstabelecimentoProdutoModelo).ToList();
            }
            else
            {
                produtosExistentes = estabelecimentoProduto
                    .Where(f => (f.EstabelecimentoProdutoModelo != null && f.EstabelecimentoProdutoModelo.Estabelecimento.IdEstabelecimento == idEstabelecimentoModelo) &&
                                (f.Ativo != f.EstabelecimentoProdutoModelo.Ativo ||
                                 f.Descricao != f.EstabelecimentoProdutoModelo.Descricao ||
                                 f.DescricaoComplementar != f.EstabelecimentoProdutoModelo.DescricaoComplementar ||
                                 //f.DataUltimaAlteracao != f.EstabelecimentoProdutoModelo.DataUltimaAlteracao || Nâo incluir esta propriedade pois ela não fica igual
                                 //f.PrecoRevendaProfissional != f.EstabelecimentoProdutoModelo.PrecoRevendaProfissional || Não incluir esta propriedade pois o estabelecimento pode alterar
                                 f.EstabelecimentoFabricanteProduto.Nome != f.EstabelecimentoProdutoModelo.EstabelecimentoFabricanteProduto.Nome ||
                                 f.ProdutoPadrao != f.EstabelecimentoProdutoModelo.ProdutoPadrao ||
                                 (f.CodigoBarras != f.EstabelecimentoProdutoModelo.CodigoBarras) ||
                                 (f.CodigoBarras == null && f.EstabelecimentoProdutoModelo.CodigoBarras != null) ||
                                 (f.CodigoBarras != null && f.EstabelecimentoProdutoModelo.CodigoBarras == null) ||
                                 ((f.CodigoDeIdentificacao == null && f.EstabelecimentoProdutoModelo.CodigoDeIdentificacao != null) ||
                                  (f.CodigoDeIdentificacao != f.EstabelecimentoProdutoModelo.CodigoDeIdentificacao)) ||
                                 f.EstabelecimentoProdutoCategoria.Nome != f.EstabelecimentoProdutoModelo.EstabelecimentoProdutoCategoria.Nome
                                 //f.EstoqueMinimo != f.EstabelecimentoProdutoModelo.EstoqueMinimo ||
                                 ))
                    .Select(f => f.EstabelecimentoProdutoModelo).ToList();
            }


            return produtosExistentes;
        }

        private IQueryable<EstabelecimentoProduto> FiltrarProdutosDoEstabelecimentoModeloQueSeraoSincronizados(Estabelecimento estabelecimentoModelo)
        {
            return Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                    .Where(f => f.Estabelecimento.IdEstabelecimento == estabelecimentoModelo.IdEstabelecimento && f.Ativo);
        }

        private void IncluirProdutosModelosComValoresPropriedadesNaoSincronizadas(List<EstabelecimentoProduto> retorno, Estabelecimento estabelecimentoDestino, Estabelecimento estabelecimentoModelo)
        {
            var listaAdicional = new List<EstabelecimentoProduto>();

            List<EstabelecimentoProduto> comPropriedadeAlterada = ListarProdutosModelosComValoresPropriedadesAlterados(estabelecimentoDestino);
            log.Info($"[Sincronia] {comPropriedadeAlterada.Count()} ProdutosModelosComValoresPropriedadesAlterados no {estabelecimentoDestino.NomeDeExibicaoNoPortal}");
            listaAdicional.AddRange(comPropriedadeAlterada);

            IList<EstabelecimentoProduto> queNuncaTeveClassificacaoSincronizada = ListarProdutosModelosQueNuncaTiveramAClassificacaoSincronizadas(estabelecimentoDestino, estabelecimentoModelo);
            log.Info($"[Sincronia] {queNuncaTeveClassificacaoSincronizada.Count()} ProdutosModelosQueNuncaTiveramAClassificacaoSincronizadas no {estabelecimentoDestino.NomeDeExibicaoNoPortal}");
            listaAdicional.AddRange(queNuncaTeveClassificacaoSincronizada);

            IncluirListaSemRepetirProdutos(retorno, listaAdicional);
        }

        private void IncluirListaSemRepetirProdutos(List<EstabelecimentoProduto> listaAtual, List<EstabelecimentoProduto> listaParaAdicionar)
        {
            var idsExistentes = new HashSet<int>(listaAtual.Select(p => p.Id));
            listaAtual.AddRange(listaParaAdicionar.Where(ep => !idsExistentes.Contains(ep.Id)));
        }

        private List<EstabelecimentoProduto> ListarProdutosModelosComValoresPropriedadesAlterados(Estabelecimento estabelecimentoDestino)
        {
            var queryProdutos = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable();
            var queryPropriedadesParaSincronizar = Domain.ProdutoEstoque.ValorPropriedadeDoProdutoRepository.FiltrarPorValoresPropriedadesNaoSincronizados(estabelecimentoDestino.IdEstabelecimento);

            var produtosModelosComPropriedadesNaoSincronizadas = queryProdutos
                .Where(ep => ep.Estabelecimento == estabelecimentoDestino && queryPropriedadesParaSincronizar.Any(p => p.EstabelecimentoProduto == ep) && ep.EstabelecimentoProdutoModelo != null)
                .Select(ep => ep.EstabelecimentoProdutoModelo)
                .ToList();

            return produtosModelosComPropriedadesNaoSincronizadas;
        }

        private IList<EstabelecimentoProduto> ListarProdutosModelosQueNuncaTiveramAClassificacaoSincronizadas(Estabelecimento estabelecimentoDestino, Estabelecimento estabelecimentoModelo)
        {
            return FiltrarProdutosModelosQueNuncaTiveramAClassificacaoSincronizada(estabelecimentoDestino, estabelecimentoModelo).ToList();
        }

        private IQueryable<EstabelecimentoProduto> FiltrarProdutosModelosQueNuncaTiveramAClassificacaoSincronizada(Estabelecimento estabelecimentoDestino, Estabelecimento estabelecimentoModelo)
        {
            var queryProdutos = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable();
            var produtosDoEstabelecimentoModelo = FiltrarProdutosDoEstabelecimentoModeloQueSeraoSincronizados(estabelecimentoModelo);

            var query = from epModelo in produtosDoEstabelecimentoModelo
                        join ep in queryProdutos on epModelo.Id equals ep.EstabelecimentoProdutoModelo.Id
                        where epModelo.ValoresDePropriedades.Any() && !ep.ValoresDePropriedades.Any() && ep.Estabelecimento == estabelecimentoDestino
                        select epModelo;

            return query;
        }

        public void SincronizarEstabelecimentoProdutosComModelo(int idEstabelecimentoDestino, OrigemSincroniaDeModelo origem)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);

            var estabelecimentoModelo = estabelecimento.EstabelecimentoModelo();
            if (estabelecimentoModelo == null)
                throw new Exception("Este estabelecimento não possui estabelecimento modelo.");

            var estabelecimentoProdutosModeloParaSincronizar =
                ListarEstabelecimentoProdutosNaoSincronizados(estabelecimentoModelo.IdEstabelecimento,
                    idEstabelecimentoDestino);
            log.Info($"[Sincronia] {estabelecimentoProdutosModeloParaSincronizar.Count()} ListarEstabelecimentoProdutosNaoSincronizados do modelo {estabelecimentoModelo.NomeDeExibicaoNoPortal}");

            InativarProdutosDaUnidadeSeNecessario(idEstabelecimentoDestino, estabelecimentoModelo.IdEstabelecimento);

            if (estabelecimentoProdutosModeloParaSincronizar.Any())
            {
                var categoriasAtivasDoModelo = Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository.ListarAtivosDoEstabelecimento(estabelecimentoModelo.IdEstabelecimento);
                SincronizarCategoriasComModelo(categoriasAtivasDoModelo, idEstabelecimentoDestino);

                var fabricantesAtivosDoModelo = Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.ListarAtivosDoEstabelecimento(estabelecimentoModelo.IdEstabelecimento);
                SincronizarFabricantesComModelo(fabricantesAtivosDoModelo, idEstabelecimentoDestino);

                AtualizarConfiguracoesDeProdutosDoEstabelecimento(estabelecimento);

                SincronizarEstabelecimentoProdutosComModelo(estabelecimentoModelo, estabelecimentoProdutosModeloParaSincronizar, idEstabelecimentoDestino, origem);
            }
        }

        private void InativarProdutosDaUnidadeSeNecessario(int idEstabelecimentoDestino, int idEstabelecimento)
        {
            log.Info($"[Sincronia] Iniciando InativarProdutosDaUnidadeSeNecessario do destino {idEstabelecimentoDestino} e modelo {idEstabelecimento}");

            var permissoes = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterPermissoesParaProdutos(idEstabelecimentoDestino, idEstabelecimento);
            var permissaoCriarEditarInativarDaUnidade = permissoes != null ? permissoes.CriarEditarInativarDaUnidade : false;

            if (permissaoCriarEditarInativarDaUnidade)
            {
                return;
            }

            var produtosModelo = Domain.Pessoas.EstabelecimentoProdutoRepository.ObterIdsDosProdutosEstabelecimento(idEstabelecimento)
                .ToDictionary(id => id, id => true);

            var produtosDaUnidade = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                .Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimentoDestino)
                .ToList();

            var produtosDaUnidadeParaInativar = produtosDaUnidade
                .Where(p => p.EstabelecimentoProdutoModelo == null ||
                            !produtosModelo.ContainsKey(p.EstabelecimentoProdutoModelo.Id))
                .ToList();

            foreach (var prod in produtosDaUnidadeParaInativar)
            {
                prod.Ativo = false;

                Domain.Pessoas.EstabelecimentoProdutoRepository.UpdateNoFlush(prod);
            }

            Domain.Pessoas.EstabelecimentoProdutoRepository.Flush();
            log.Info($"[Sincronia] Finalizando InativarProdutosDaUnidadeSeNecessario {produtosDaUnidadeParaInativar.Count} do destino {idEstabelecimentoDestino} e modelo {idEstabelecimento}");
        }

        private void AtualizarConfiguracoesDeProdutosDoEstabelecimento(Estabelecimento estabelecimento)
        {
            if (!estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo)
                Domain.Pessoas.EstabelecimentoConfiguracaoGeralService.AtualizarConfiguracaoDoEstabelecimentoParaIndicarSeHaProdutoFracionadoSincronia(estabelecimento);
        }

        private EstabelecimentoProdutoCategoria ObterOuCriarCategoriaPeloModelo(Estabelecimento estabelecimento,
            EstabelecimentoProdutoCategoria categoriaModelo, List<EstabelecimentoProdutoCategoria> categoriasProdutosEstabelecimentoDestino)
        {
            var categoriaExistente = categoriasProdutosEstabelecimentoDestino.FirstOrDefault(f => f.Nome.ToLower() == categoriaModelo.Nome.ToLower());

            return categoriaExistente ?? new EstabelecimentoProdutoCategoria
            {
                Ativo = categoriaModelo.Ativo,
                Estabelecimento = estabelecimento,
                Nome = categoriaModelo.Nome,
                ProdutoCategoriaPadrao = categoriaModelo.ProdutoCategoriaPadrao
            };
        }

        private EstabelecimentoFabricanteProduto ObterOuCriarFabricantePeloModelo(
            Estabelecimento estabelecimento, EstabelecimentoFabricanteProduto fabricanteModelo, List<EstabelecimentoFabricanteProduto> fabricantesProdutosEstabelecimentoDestino)
        {
            var fabricanteExistente = fabricantesProdutosEstabelecimentoDestino.FirstOrDefault(f => f.Nome == fabricanteModelo.Nome);

            return fabricanteExistente ?? new EstabelecimentoFabricanteProduto
            {
                Ativo = fabricanteModelo.Ativo,
                Estabelecimento = estabelecimento,
                Nome = fabricanteModelo.Nome,
                FabricanteProdutoPadrao = fabricanteModelo.FabricanteProdutoPadrao
            };
        }

        private void SincronizarCategoriasComModelo(IList<EstabelecimentoProdutoCategoria> categoriasAtivasDoModelo,
            int idEstabelecimentoDestino)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            SincronizarCategoriasComModelo(categoriasAtivasDoModelo, estabelecimento);
        }

        private void SincronizarCategoriasComModelo(IList<EstabelecimentoProdutoCategoria> categoriasAtivasDoModelo, Estabelecimento estabelecimentoDestino)
        {
            log.Info($"[Sincronia] Iniciando SincronizarCategoriasComModelo {categoriasAtivasDoModelo.Count()} categorias");

            var produtoCategorias = Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository.Queryable()
                .Where(f => f.Estabelecimento == estabelecimentoDestino).ToList();

            foreach (var catModelo in categoriasAtivasDoModelo)
            {
                var cat = produtoCategorias.FirstOrDefault(f => f.Nome == catModelo.Nome) ??
                          new EstabelecimentoProdutoCategoria();

                cat.Ativo = true;// catModelo.Ativo;
                cat.Estabelecimento = estabelecimentoDestino;
                cat.Nome = catModelo.Nome;
                cat.ProdutoCategoriaPadrao = catModelo.ProdutoCategoriaPadrao;

                if (cat.IdEstabelecimentoProdutoCategoria == 0)
                    Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository.SaveNewNoFlush(cat);
            }

            foreach (var cat in produtoCategorias.Where(f => f.Ativo && categoriasAtivasDoModelo.All(g => g.Nome != f.Nome)))
            {
                cat.Ativo = false;
            }

            Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository.Flush();

            log.Info($"[Sincronia] Finalizando SincronizarCategoriasComModelo");

        }

        private void SincronizarEstabelecimentoProdutosComModelo(Estabelecimento estabelecimentoModelo, IEnumerable<EstabelecimentoProduto> produtosASincronizarDoModelo,
            int idEstabelecimentoDestino, OrigemSincroniaDeModelo origem)
        {

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            log.Info($"[Sincronia] Iniciando SincronizarEstabelecimentoProdutosComModelo {produtosASincronizarDoModelo.Count()} do destino {estabelecimento.NomeDeExibicaoNoPortal}");

            var permissoes = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterPermissoesParaProdutos(idEstabelecimentoDestino, estabelecimentoModelo.IdEstabelecimento);

            var fabricantesProdutosEstabelecimentoDestino = Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.Queryable().Where(p => p.Estabelecimento == estabelecimento).ToList();
            var categoriasProdutosEstabelecimentoDestino = Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository.Queryable().Where(p => p.Estabelecimento == estabelecimento).ToList();

            var estabelecimentoProdutos = ListarProdutosDoEstabelecimento(idEstabelecimentoDestino, buscaIncluiClassificacao: estabelecimento.ExibeCamposDeClassificacao());

            var valoresDePropriedadesDosProdutosModeloParaSincronizar = ListarValoresDePropriedadesDosProdutosModeloParaSincronizar(estabelecimento, estabelecimentoModelo);

            var permiteAlterarValorDeCompra = estabelecimento.FranquiaEstabelecimento.Franquia.PermiteAlterarValorDeCompra;
            var permiteAlterarEstoqueMinimo = estabelecimento.FranquiaEstabelecimento.Franquia.PermiteAlterarEstoqueMinimo;
            var permiteAlterarDadosFiscais = estabelecimento.FranquiaEstabelecimento.Franquia.PermiteAlterarDadosFiscais;

            var permissaoDeControleDeEstoque = permissoes != null ? permissoes.ControleEstoqueDoModelo : false;
            var permissaoDeInformacoesFiscaisDoModelo = permissoes != null ? permissoes.InformacoesFiscaisDoModelo : false;
            var permissaoDeVendaConsumoDoModelo = permissoes != null ? permissoes.VendaConsumoDoModelo : false;
            var permissaoCriarEditarInativarDaUnidade = permissoes != null ? permissoes.CriarEditarInativarDaUnidade : false;

            log.Info($"[Sincronia] Iniciando EstabelecimentoFabricanteProdutoRepository do {estabelecimento.NomeDeExibicaoNoPortal}");
            var fabricantesDestino = Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.ListarAtivosDoEstabelecimento(idEstabelecimentoDestino)
                .ToDictionary(f => f, f => f.Nome.ToLower().Trim());
            var fabricantesModelo = Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.ListarAtivosDoEstabelecimento(estabelecimentoModelo.IdEstabelecimento);
            log.Info($"[Sincronia] Finalizando EstabelecimentoFabricanteProdutoRepository do {estabelecimento.NomeDeExibicaoNoPortal}");

            foreach (var prodModelo in produtosASincronizarDoModelo)
            {
                var produtosDaUnidadePorIdModelo =
                    estabelecimentoProdutos.Where(
                        f =>
                            f.EstabelecimentoProdutoModelo == prodModelo).ToList();

                EstabelecimentoProduto prod;

                if (produtosDaUnidadePorIdModelo.Any())
                {
                    prod = produtosDaUnidadePorIdModelo.First();

                    if (produtosDaUnidadePorIdModelo.Count() > 1)
                    {
                        foreach (var produtoRepetido in produtosDaUnidadePorIdModelo.Skip(1))
                        {
                            produtoRepetido.Ativo = false;
                            Domain.Pessoas.EstabelecimentoProdutoRepository.UpdateNoFlush(produtoRepetido);
                        }
                        Domain.Pessoas.EstabelecimentoProdutoRepository.Flush();
                    }
                }
                else
                {
                    var produtosDaUnidadePorDescricaoEFabricante =
                        estabelecimentoProdutos.Where(
                            f =>
                                (f.Descricao.ToLower().Trim() == prodModelo.Descricao.ToLower().Trim()) &&
                                         fabricantesDestino.TryGetValue(f.EstabelecimentoFabricanteProduto, out var nomeDestino) &&
                                         fabricantesModelo.Any(fm => fm.Nome.ToLower().Trim() == nomeDestino)).ToList();

                    if (produtosDaUnidadePorDescricaoEFabricante.Any())
                    {
                        prod = produtosDaUnidadePorDescricaoEFabricante.First();
                    }
                    else
                    {
                        prod = new EstabelecimentoProduto();
                    }

                }

                prod.Ativo = prodModelo.Ativo;

                if (!prodModelo.Ativo)
                    continue;

                if ((!permiteAlterarValorDeCompra || origem != OrigemSincroniaDeModelo.Agendamento) && !permissaoDeControleDeEstoque)
                {
                    prod.ValorDeCompra = prodModelo.ValorDeCompra;
                }

                if ((!permiteAlterarEstoqueMinimo && origem == OrigemSincroniaDeModelo.CadastroDeUnidade) && !permissaoDeControleDeEstoque)
                {
                    prod.EstoqueMinimo = prodModelo.EstoqueMinimo;
                }

                var atualizaDadosFiscais = (!permiteAlterarDadosFiscais || origem == OrigemSincroniaDeModelo.CadastroDeUnidade) && !permissaoDeInformacoesFiscaisDoModelo;

                if (atualizaDadosFiscais || string.IsNullOrWhiteSpace(prod.CodigoNCM))
                    prod.CodigoNCM = prodModelo.CodigoNCM;
                if (atualizaDadosFiscais || !prod.IdSituacaoTributaria.HasValue)
                    prod.IdSituacaoTributaria = prodModelo.IdSituacaoTributaria;
                if (atualizaDadosFiscais || string.IsNullOrWhiteSpace(prod.CEST))
                    prod.CEST = prodModelo.CEST;
                if (atualizaDadosFiscais || !prod.AliquotaICMS.HasValue)
                    prod.AliquotaICMS = prodModelo.AliquotaICMS;
                if (atualizaDadosFiscais || !prod.OrigemProduto.HasValue)
                    prod.OrigemProduto = prodModelo.OrigemProduto;
                if (atualizaDadosFiscais || !prod.AliquotaPIS.HasValue)
                    prod.AliquotaPIS = prodModelo.AliquotaPIS;
                if (atualizaDadosFiscais || !prod.AliquotaCOFINS.HasValue)
                    prod.AliquotaCOFINS = prodModelo.AliquotaCOFINS;
                if (atualizaDadosFiscais)
                    prod.FabricacaoPropria = prodModelo.FabricacaoPropria;

                if (!permissaoDeInformacoesFiscaisDoModelo && permiteAlterarDadosFiscais &&
                    (origem == OrigemSincroniaDeModelo.Agendamento || origem == OrigemSincroniaDeModelo.AssociacaoPelaAreaPerlink))
                {
                    prod.CodigoNCM = prodModelo.CodigoNCM;
                }

                prod.Descricao = prodModelo.Descricao;
                prod.Estabelecimento = estabelecimento;
                prod.DataUltimaAlteracao = prodModelo.DataUltimaAlteracao;
                prod.DescricaoComplementar = prodModelo.DescricaoComplementar;
                prod.EstabelecimentoProdutoModelo = prodModelo;
                prod.PessoaCriacao = prodModelo.PessoaCriacao;

                prod.ProdutoPadrao = prodModelo.ProdutoPadrao;
                prod.TipoComissao = prodModelo.TipoComissao;

                prod.CodigoBarras = prodModelo.CodigoBarras;
                prod.CodigoDeIdentificacao = prodModelo.CodigoDeIdentificacao;

                if (!permissaoDeVendaConsumoDoModelo)
                {
                    prod.ValorPorMedida = prodModelo.ValorPorMedida;
                    prod.MedidasPorUnidade = prodModelo.MedidasPorUnidade;
                    prod.UnidadeMedida = prodModelo.UnidadeMedida;
                    prod.PrecoRevendaCliente = prodModelo.PrecoRevendaCliente;
                }

                if (!permissaoDeControleDeEstoque)
                {
                    prod.CustoMedio = prodModelo.CustoMedio;
                }


                if (prod.Id != 0)
                {
                    if (prodModelo.PermitirRevenda == true && prod.PermitirRevenda == false)
                    {
                        prod.EhRevendaParaCliente = prodModelo.EhRevendaParaCliente;
                        prod.EhRevendaParaProfissional = prodModelo.EhRevendaParaProfissional;
                    }
                    else if (prodModelo.PermitirRevenda == false && prod.PermitirRevenda == true)
                    {
                        prod.EhRevendaParaCliente = prodModelo.EhRevendaParaCliente;
                        prod.EhRevendaParaProfissional = prodModelo.EhRevendaParaProfissional;
                        if (!permissaoDeVendaConsumoDoModelo)
                        {
                            prod.ValorComissaoRevenda = prodModelo.ValorComissaoRevenda;
                            prod.PrecoRevendaProfissional = prodModelo.PrecoRevendaProfissional;
                        }

                    }
                }
                prod.PermitirRevenda = prodModelo.PermitirRevenda;

                if (prod.EstabelecimentoProdutoCategoria == null ||
                    prod.EstabelecimentoProdutoCategoria.Nome.ToLower() !=
                    prodModelo.EstabelecimentoProdutoCategoria.Nome.ToLower())
                {
                    prod.EstabelecimentoProdutoCategoria = ObterOuCriarCategoriaPeloModelo(estabelecimento,
                        prodModelo.EstabelecimentoProdutoCategoria, categoriasProdutosEstabelecimentoDestino);

                    if (prod.EstabelecimentoProdutoCategoria.IdEstabelecimentoProdutoCategoria == 0)
                        categoriasProdutosEstabelecimentoDestino.Add(prod.EstabelecimentoProdutoCategoria);
                }

                if (prodModelo.EstabelecimentoFabricanteProduto != null)
                {
                    if (prod.EstabelecimentoFabricanteProduto == null || prod.EstabelecimentoFabricanteProduto.Nome != prodModelo.EstabelecimentoFabricanteProduto.Nome)
                    {
                        prod.EstabelecimentoFabricanteProduto = ObterOuCriarFabricantePeloModelo(estabelecimento, prodModelo.EstabelecimentoFabricanteProduto, fabricantesProdutosEstabelecimentoDestino);

                        if (prod.EstabelecimentoFabricanteProduto.IdEstabelecimentoFabricanteProduto == 0)
                            fabricantesProdutosEstabelecimentoDestino.Add(prod.EstabelecimentoFabricanteProduto);
                    }
                }
                else
                    prod.EstabelecimentoFabricanteProduto = null;

                if (estabelecimento.ExibeCamposDeClassificacao())
                {
                    var valoresDePropriedadesDoProdutoModelo = valoresDePropriedadesDosProdutosModeloParaSincronizar.Where(ppm => ppm.EstabelecimentoProduto == prodModelo).ToList();

                    if (valoresDePropriedadesDoProdutoModelo.Any())
                    {
                        Domain.ProdutoEstoque.ManterPropriedadesDoProdutoService.SincronizarValoresDasPropriedadesDoProdutoComModelo(prod, valoresDePropriedadesDoProdutoModelo);
                    }
                }

                if (prod.Id == 0)
                {
                    prod.EhRevendaParaCliente = prodModelo.EhRevendaParaCliente;
                    prod.EhRevendaParaProfissional = prodModelo.EhRevendaParaProfissional;
                    if (!permissaoDeVendaConsumoDoModelo)
                    {
                        prod.ValorComissaoRevenda = prodModelo.ValorComissaoRevenda;
                        prod.PrecoRevendaProfissional = prodModelo.PrecoRevendaProfissional;
                    }
                    if ((!permiteAlterarEstoqueMinimo && origem == OrigemSincroniaDeModelo.CadastroDeUnidade) && !permissaoDeControleDeEstoque)
                    {
                        prod.EstoqueMinimo = prodModelo.EstoqueMinimo;
                    }
                    if (atualizaDadosFiscais)
                    {
                        prod.IdSituacaoTributaria = prodModelo.IdSituacaoTributaria;
                    }

                    Domain.Pessoas.EstabelecimentoProdutoRepository.SaveNewNoFlush(prod);
                }
            }

            log.Info($"[Sincronia] Finalizando SincronizarEstabelecimentoProdutosComModelo do destino {estabelecimento.NomeDeExibicaoNoPortal}");
        }

        private IList<EstabelecimentoProduto> ListarProdutosDoEstabelecimento(int idEstabelecimento, bool buscaIncluiClassificacao = false)
        {
            var query = Domain.Pessoas.EstabelecimentoProdutoRepository.Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (buscaIncluiClassificacao)
            {
                query.FetchMany(h => h.ValoresDePropriedades).ToFuture();
            }

            return query.ToList();
        }

        private List<ValorPropriedadeDoProduto> ListarValoresDePropriedadesDosProdutosModeloParaSincronizar(Estabelecimento estabelecimentoDestino, Estabelecimento estabelecimentoModelo)
        {
            var valoresParaSincronizar = new List<ValorPropriedadeDoProduto>();

            if (estabelecimentoDestino.ExibeCamposDeClassificacao())
            {
                List<ValorPropriedadeDoProduto> classificacaoAlterada = ListarValoresPropriedadesDeProdutosModelosQueTiveramClassificacaoAlterada(estabelecimentoDestino);
                log.Info($"[Sincronia] {classificacaoAlterada.Count()} ListarValoresPropriedadesDeProdutosModelosQueTiveramClassificacaoAlterada do {estabelecimentoDestino.NomeDeExibicaoNoPortal}");
                valoresParaSincronizar.AddRange(classificacaoAlterada);

                List<ValorPropriedadeDoProduto> classificacaoNuncaSincronizada = ListarValoresPropriedadesDeProdutosModelosQueNuncaTiveramClassificacaoSincronizada(estabelecimentoDestino, estabelecimentoModelo);
                log.Info($"[Sincronia] {classificacaoNuncaSincronizada.Count()} ListarValoresPropriedadesDeProdutosModelosQueNuncaTiveramClassificacaoSincronizada do {estabelecimentoDestino.NomeDeExibicaoNoPortal}");
                valoresParaSincronizar.AddRange(classificacaoNuncaSincronizada);
            }

            return valoresParaSincronizar;
        }

        private List<ValorPropriedadeDoProduto> ListarValoresPropriedadesDeProdutosModelosQueTiveramClassificacaoAlterada(Estabelecimento estabelecimentoDestino)
        {
            return Domain.ProdutoEstoque.ValorPropriedadeDoProdutoRepository
                .FiltrarPorValoresPropriedadesNaoSincronizados(estabelecimentoDestino.IdEstabelecimento)
                .Select(pp => pp.ValorPropriedadeDoProdutoModelo).ToList();
        }

        private List<ValorPropriedadeDoProduto> ListarValoresPropriedadesDeProdutosModelosQueNuncaTiveramClassificacaoSincronizada(Estabelecimento estabelecimentoDestino, Estabelecimento estabelecimentoModelo)
        {
            var produtosModelosNuncaSincronizados = FiltrarProdutosModelosQueNuncaTiveramAClassificacaoSincronizada(estabelecimentoDestino, estabelecimentoModelo);
            var valoresPropriedadesNuncaSincronizados = Domain.ProdutoEstoque.ValorPropriedadeDoProdutoRepository.Queryable()
                .Where(vpp => vpp.Estabelecimento == estabelecimentoModelo && produtosModelosNuncaSincronizados.Any(epm => vpp.EstabelecimentoProduto == epm))
                .ToList();

            return valoresPropriedadesNuncaSincronizados;
        }

        private void SincronizarFabricantesComModelo(IList<EstabelecimentoFabricanteProduto> fabricantesAtivosDoModelo,
            int idEstabelecimentoDestino)
        {
            log.Info($"[Sincronia] Iniciando SincronizarFabricantesComModelo");

            // Filtra fabricantes nulos e cria um HashSet para pesquisas mais eficientes
            var fabricantesModeloFiltrados = fabricantesAtivosDoModelo
                .Where(f => f != null)
                .ToDictionary(f => f.Nome.ToLower().Trim(), f => f);

            if (!fabricantesModeloFiltrados.Any())
            {
                log.Info($"[Sincronia] SincronizarFabricantesComModelo: Nenhum fabricante modelo para sincronizar");
                return;
            }

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);

            // Carrega todos os fabricantes do estabelecimento destino em uma única consulta
            var fabricantesDestino = Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimentoDestino)
                .ToList();

            // Cria um dicionário para busca rápida por nome
            var dicionarioPorNome = fabricantesDestino.ToDictionary(f => f.Nome.ToLower().Trim(), f => f);

            // Processa todos os fabricantes modelo
            foreach (var kvp in fabricantesModeloFiltrados)
            {
                var nomeModeloNormalizado = kvp.Key;
                var fabricanteModelo = kvp.Value;

                if (dicionarioPorNome.TryGetValue(nomeModeloNormalizado, out var fabricanteExistente))
                {
                    fabricanteExistente.Ativo = true;
                    fabricanteExistente.FabricanteProdutoPadrao = fabricanteModelo.FabricanteProdutoPadrao;
                    fabricanteExistente.Nome = fabricanteModelo.Nome;
                    Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.UpdateNoFlush(fabricanteExistente);
                }
                else
                {
                    var novoFabricante = new EstabelecimentoFabricanteProduto
                    {
                        Ativo = fabricanteModelo.Ativo,
                        Estabelecimento = estabelecimento,
                        FabricanteProdutoPadrao = fabricanteModelo.FabricanteProdutoPadrao,
                        Nome = fabricanteModelo.Nome
                    };
                    Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.SaveNewNoFlush(novoFabricante);
                }
            }

            // Inativa fabricantes que existem no destino mas não no modelo
            var fabricantesParaInativar = fabricantesDestino
                .Where(f => f.Ativo && !fabricantesModeloFiltrados.ContainsKey(f.Nome.ToLower().Trim()))
                .ToList();

            foreach (var fabricante in fabricantesParaInativar)
            {
                fabricante.Nome = fabricante.Nome + " #" + fabricante.IdEstabelecimentoFabricanteProduto;
                fabricante.Ativo = false;
                Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.UpdateNoFlush(fabricante);
            }

            Domain.Pessoas.EstabelecimentoFabricanteProdutoRepository.Flush();
            log.Info($"[Sincronia] Finalizando SincronizarFabricantesComModelo");
        }

        #endregion Produtos

        #region Servicos
        public bool VerificarSePodeVisualizarModalServicosParaSincronizar(int idEstabelecimentoModelo)
        {
            var qtdMaxParaVisualizarModal = new ParametrosTrinks<int>(ParametrosTrinksEnum.limite_servicos_para_agendamento_sincronia_automatico).ObterValor();
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);
            return Domain.Pessoas.ServicoEstabelecimentoRepository.Queryable()
                    .Where(f => f.Estabelecimento == estabelecimentoModelo).Count() > qtdMaxParaVisualizarModal;
        }

        public List<ServicoEstabelecimento> ListarServicosEstabelecimentoNaoSincronizados(int idEstabelecimentoModelo,
            int idEstabelecimentoDestino = 0)
        {
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);

            Estabelecimento estabelecimentoDestino = null;
            bool verificaCamposFlexiveis = true;

            if (idEstabelecimentoDestino > 0)
            {
                estabelecimentoDestino = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            }
            else
            {
                (bool verificaItensFlexiveis, Estabelecimento unidade) dados = ObterEstabelecimentoDoModelo(idEstabelecimentoModelo, TipoSincronizacaoEnum.Servicos);

                estabelecimentoDestino = dados.unidade;
                verificaCamposFlexiveis = dados.verificaItensFlexiveis;
            }

            if (idEstabelecimentoDestino == 0 && estabelecimentoDestino == null)
                return null;

            var servicosEstabelecimentoModelo = Domain.Pessoas.ServicoEstabelecimentoRepository.ListarPorEstabelecimento(estabelecimentoModelo.IdEstabelecimento);

            IEnumerable<ServicoEstabelecimento> retorno;

            if (estabelecimentoDestino != null)
            {
                var servicosEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.Queryable()
                    .Where(f => f.Estabelecimento == estabelecimentoDestino && f.ServicoEstabelecimentoModelo != null);

                List<ItemConfiguradoParaBaixaAutomatica> itensModelosDaBaixaAutomaticaPraSincronizar = null;
                if (verificaCamposFlexiveis)
                    itensModelosDaBaixaAutomaticaPraSincronizar = ListarItensModelosConfiguradosQuePrecisamSerSincronizados(estabelecimentoModelo, estabelecimentoDestino);

                if (!ValidationHelper.Instance.IsValid)
                    return null;


                List<int> idsServicosQuePrecisamSincronizarConfiguracoesBaixaAutomatica = null;
                if (verificaCamposFlexiveis)
                    idsServicosQuePrecisamSincronizarConfiguracoesBaixaAutomatica = Domain.EstoqueComBaixaAutomatica.SincroniaDaBaixaAutomaticaService.ListarIdsDosServicosModelosQuePrecisamSerSincronizados(estabelecimentoModelo, estabelecimentoDestino);

                var servicosExistentes = new List<ServicoEstabelecimento>();
                foreach (ServicoEstabelecimento se in servicosEstabelecimento)
                {
                    var servicoFoiAlterado = VerificarSeOServicoFoiAlterado(se, itensModelosDaBaixaAutomaticaPraSincronizar, idsServicosQuePrecisamSincronizarConfiguracoesBaixaAutomatica, verificaCamposFlexiveis);
                    if (servicoFoiAlterado)
                        servicosExistentes.Add(se);
                }

                var novosServicos =
                    servicosEstabelecimentoModelo.Where(
                        f => !servicosEstabelecimento.Any(g => g.ServicoEstabelecimentoModelo == f) && f.Ativo).ToList();

                retorno = servicosExistentes.Union(novosServicos);
            }
            else
            {
                retorno = servicosEstabelecimentoModelo;
            }

            return retorno.ToList();
        }

        private List<ItemConfiguradoParaBaixaAutomatica> ListarItensModelosConfiguradosQuePrecisamSerSincronizados(Estabelecimento estabelecimentoModelo, Estabelecimento estabelecimentoDestino)
        {
            if (!Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.BaixaEstoque_ConfigurarUsoNosServicos))
                return new List<ItemConfiguradoParaBaixaAutomatica>();

            var itensModelosDaBaixaAutomaticaPraSincronizar = Domain.EstoqueComBaixaAutomatica.SincroniaDaBaixaAutomaticaService
                .ListarItensModelosConfiguradosQuePrecisamSerSincronizados(estabelecimentoModelo, estabelecimentoDestino);

            if (itensModelosDaBaixaAutomaticaPraSincronizar.Any() && !ExisteAgendamentoPendenteDeSincronizacaoParaOModelo(estabelecimentoModelo))
            {

                var produtosModeloQuePrecisamSincronizar = Domain.Pessoas.SincroniaEstabelecimentoModeloService.ListarEstabelecimentoProdutosNaoSincronizados(estabelecimentoModelo.IdEstabelecimento);
                var idsEstProdutoItensDaBaixa = itensModelosDaBaixaAutomaticaPraSincronizar.Select(iDes => iDes.EstabelecimentoProduto.Id).Distinct().ToList();

                if (produtosModeloQuePrecisamSincronizar.Any(pMod => idsEstProdutoItensDaBaixa.Contains(pMod.Id)))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Alguns produtos configurados na saída de estoque nos serviços precisam ser sincronizados anteriormente.\nPara continuar, vá em Visualizar/Editar > Produtos e agende a sincronia dos produtos.");
                }
            }

            return itensModelosDaBaixaAutomaticaPraSincronizar;
        }

        private bool ExisteAgendamentoPendenteDeSincronizacaoParaOModelo(Estabelecimento estabelecimentoModelo)
        {
            return Domain.Pessoas.SincronizacaoEstabelecimentosFilaRepository
                .ExisteAgendamentoPendenteDeSincronizacaoParaOModelo(estabelecimentoModelo.IdEstabelecimento, TipoSincronizacaoEnum.Produtos);
        }

        public void SincronizarServicosEstabelecimentoComModelo(int idEstabelecimentoDestino)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            SincronizarServicosEstabelecimentoComModelo(estabelecimento);
        }

        public void SincronizarServicosEstabelecimentoComModelo(Estabelecimento estabelecimentoDestino)
        {
            var estabelecimentoModelo = estabelecimentoDestino.EstabelecimentoModelo()
                ?? throw new Exception("Este estabelecimento não possui estabelecimento modelo.");

            var servicosEstabelecimentoModelo =
                Domain.Pessoas.ServicoEstabelecimentoRepository.ListarTodosPorEstabelecimento(estabelecimentoModelo.IdEstabelecimento);
            log.Info($"[Sincronia] Iniciando SincronizarServicosEstabelecimentoComModelo {servicosEstabelecimentoModelo.Count()} servicos");

            var categorias = servicosEstabelecimentoModelo.Select(f => f.ServicoCategoriaEstabelecimento).Distinct().ToList();

            SincronizarCategoriasComModelo(categorias, estabelecimentoDestino);
            log.Info($"[Sincronia] SincronizarCategoriasComModelo {categorias.Count} categoias finalizado");
            SincronizarServicosEstabelecimentoComModelo(servicosEstabelecimentoModelo, estabelecimentoModelo, estabelecimentoDestino);

        }

        private ServicoCategoriaEstabelecimento ObterOuCriarCategoriaPeloModelo(Estabelecimento estabelecimento,
                    ServicoCategoriaEstabelecimento categoriaModelo, List<ServicoCategoriaEstabelecimento> categoriasServicosEstabelecimentoDestino)
        {
            var categoriaExistente = categoriasServicosEstabelecimentoDestino.FirstOrDefault(f => f.Nome == categoriaModelo.Nome);

            return categoriaExistente ?? new ServicoCategoriaEstabelecimento
            {
                Ativo = categoriaModelo.Ativo,
                Estabelecimento = estabelecimento,
                Nome = categoriaModelo.Nome,
                Descricao = categoriaModelo.Descricao,
                ServicoCategoria = categoriaModelo.ServicoCategoria
            };
        }

        private static void RedefinirValoresAgendamentosFuturos(Estabelecimento estabelecimento,
            IEnumerable<ServicoEstabelecimento> servicosEstabelecimentosPrecoAlterado)
        {
            foreach (var se in servicosEstabelecimentosPrecoAlterado)
            {
                Domain.Pessoas.HorarioService.AlterarPrecoAgendamentosMarcadosAPartirDe(Calendario.Hoje(),
                    estabelecimento.IdEstabelecimento, se.IdServicoEstabelecimento);
            }
        }

        private static void RemoverDaLista(ICollection<ServicoEstabelecimento> lista,
            IEnumerable<ServicoEstabelecimento> removidos)
        {
            foreach (var item in removidos)
            {
                lista.Remove(item);
            }
        }

        private static void SincronizarCategoriasComModelo(IList<ServicoCategoriaEstabelecimento> categoriasModelo, Estabelecimento estabelecimentoDestino)
        {
            var categorias = Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Queryable()
                            .Where(f => f.Estabelecimento == estabelecimentoDestino).ToList();

            foreach (var catModelo in categoriasModelo)
            {
                var cat = categorias.FirstOrDefault(f => f.Nome == catModelo.Nome) ??
                          new ServicoCategoriaEstabelecimento();

                cat.Ativo = catModelo.Ativo;
                cat.Estabelecimento = estabelecimentoDestino;
                cat.Nome = catModelo.Nome;
                cat.Descricao = catModelo.Descricao;
                cat.ServicoCategoria = catModelo.ServicoCategoria;

                if (cat.Codigo == 0)
                    Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.SaveNewNoFlush(cat);
            }

            Domain.ProdutoEstoque.EstabelecimentoProdutoCategoriaRepository.Flush();
        }

        private static bool VerificarSeOServicoFoiAlterado(ServicoEstabelecimento se, List<ItemConfiguradoParaBaixaAutomatica> itensModelosDaBaixaAutomaticaParaSincronizar, List<int> idsServicosQuePrecisamSincronizarConfiguracoesBaixaAutomatica, bool verificaCamposFlexiveis)
        {
            var seModelo = se.ServicoEstabelecimentoModelo;
            var servicoFoiAlterado = false;

            if (se.Ativo != seModelo.Ativo ||
                se.ExibePreco != seModelo.ExibePreco ||
                se.PrecoFixo != seModelo.PrecoFixo ||
                se.CodigoInterno != seModelo.CodigoInterno ||
                se.CustoDescartaveis != seModelo.CustoDescartaveis ||
                se.Descricao != seModelo.Descricao ||
                (verificaCamposFlexiveis && se.Duracao != seModelo.Duracao) ||
                se.Nome != seModelo.Nome ||
                (verificaCamposFlexiveis && se.Preco != seModelo.Preco) ||
                se.ServicoCategoriaEstabelecimento.Nome !=
                seModelo.ServicoCategoriaEstabelecimento.Nome ||
                se.TipoPreco != seModelo.TipoPreco ||
                (se.Promocao != null && seModelo.Promocao == null) ||
                (se.Promocao == null && seModelo.Promocao != null) ||
                se.ServicoIndiposnivelParaCliente != seModelo.ServicoIndiposnivelParaCliente)
                servicoFoiAlterado = true;

            var servicoTeveBaixaAutomaticaReconfigurada = false;
            var servicoTeveDescontoPersonalizadoAlterado = false;

            if (verificaCamposFlexiveis)
            {
                servicoTeveBaixaAutomaticaReconfigurada =
                itensModelosDaBaixaAutomaticaParaSincronizar.Any(iMod => iMod.ServicoEstabelecimento == seModelo)
                || idsServicosQuePrecisamSincronizarConfiguracoesBaixaAutomatica.Any(id => id == seModelo.IdServicoEstabelecimento);
            }

            var temPermissaoParaDescontosPersonalizados = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(se.Estabelecimento, Recurso.DescontosPersonalizados).EstaDisponivel;

            if (temPermissaoParaDescontosPersonalizados)
            {
                var descontoServicoUnidade = Domain.Financeiro.DescontoPersonalizadoServicoRepository
                    .Obter(se.IdServicoEstabelecimento, se.Estabelecimento.IdEstabelecimento);
                var descontoServicoModelo = Domain.Financeiro.DescontoPersonalizadoServicoRepository
                    .Obter(seModelo.IdServicoEstabelecimento, seModelo.Estabelecimento.IdEstabelecimento);

                if ((descontoServicoUnidade != null && descontoServicoModelo == null) || (descontoServicoUnidade == null && descontoServicoModelo != null))
                    servicoTeveDescontoPersonalizadoAlterado = true;

                if (descontoServicoUnidade != null && descontoServicoModelo != null)
                {
                    servicoTeveDescontoPersonalizadoAlterado = (descontoServicoUnidade.DescontoPersonalizado.Valor != descontoServicoModelo.DescontoPersonalizado.Valor) ||
                                                               (descontoServicoUnidade.DescontoPersonalizado.Tipo != descontoServicoModelo.DescontoPersonalizado.Tipo);
                }
            }

            if (servicoTeveBaixaAutomaticaReconfigurada || servicoTeveDescontoPersonalizadoAlterado)
            {
                return true;
            }

            if (se.Promocao != null && seModelo.Promocao != null && verificaCamposFlexiveis)
            {
                if (se.Promocao.TipoVigencia != seModelo.Promocao.TipoVigencia)
                    servicoFoiAlterado = true;
                if (se.Promocao.DataInicio != seModelo.Promocao.DataInicio)
                    servicoFoiAlterado = true;
                if (se.Promocao.DataFim != seModelo.Promocao.DataFim)
                    servicoFoiAlterado = true;

                var promocoesDaSemana = Domain.Promocoes.PromocaoDiaDaSemanaRepository.ObterListaPromocaoDiaDaSemana(se.Promocao.IdPromocao);
                var promocoesDaSemanaModelo = Domain.Promocoes.PromocaoDiaDaSemanaRepository.ObterListaPromocaoDiaDaSemana(seModelo.Promocao.IdPromocao);
                var diasDaSemana = EnumActions.GetEnumValues(typeof(Enums.DiaSemana));

                foreach (EnumActions.EnumItem diaDaSemana in diasDaSemana)
                {
                    var codigoDiaDaSemana = diaDaSemana.Value - 1;
                    var promocaoDoDia = promocoesDaSemana.FirstOrDefault(p => p.DiaDaSemana == codigoDiaDaSemana);
                    var promocaoDoDiaModelo = promocoesDaSemanaModelo.FirstOrDefault(p => p.DiaDaSemana == codigoDiaDaSemana);

                    if (promocaoDoDia == null && promocaoDoDiaModelo != null)
                        servicoFoiAlterado = true;
                    if (promocaoDoDia != null && promocaoDoDiaModelo == null)
                        servicoFoiAlterado = true;
                    if (promocaoDoDia != null && promocaoDoDiaModelo != null && promocaoDoDia.Valor != promocaoDoDiaModelo.Valor)
                        servicoFoiAlterado = true;
                }
            }

            return servicoFoiAlterado;
        }

        //[TransactionInitRequired]
        //private void SincronizarCategoriasComModelo(IList<ServicoCategoriaEstabelecimento> categoriasModelo,
        //    int idEstabelecimentoDestino) {
        //    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
        //    SincronizarCategoriasComModelo(categoriasModelo, estabelecimento);
        //}

        private void SincronizarServicosEstabelecimentoComModelo(IEnumerable<ServicoEstabelecimento> lista,
            Estabelecimento estabelecimentoModelo, Estabelecimento estabelecimentoDestino)
        {
            //var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            var servicosEstabelecimentosPrecoAlterado = new List<ServicoEstabelecimento>();
            var listaAuxEstabelecimento =
                Domain.Pessoas.ServicoEstabelecimentoRepository.ListarPorEstabelecimento(estabelecimentoDestino.IdEstabelecimento);
            var listaAuxModelo = lista.ToList();
            log.Info($"[Sincronia] Iniciando SincronizarServicosEstabelecimentoComModelo do destino {estabelecimentoDestino.NomeDeExibicaoNoPortal} {listaAuxModelo.Count}");

            var permissoes = Domain.Pessoas.PermissoesDaUnidadeBaseadaEmModeloRepository.ObterConfiguracoesParaServicos(estabelecimentoDestino.IdEstabelecimento, estabelecimentoModelo.IdEstabelecimento);

            var permissaoPrecoDuracaoDoModelo = permissoes != null ? permissoes.PrecoDuracaoDoModelo : false;
            var permissaoPromocaoDoModelo = permissoes != null ? permissoes.PromocaoDoModelo : false;
            var permissaoSaidaAutomaticaCustoOperacionalDoModelo = permissoes != null ? permissoes.SaidaAutomaticaCustoOperacionalDoModelo : false;
            var permissaoCriarEditarInativarDaUnidade = permissoes != null ? permissoes.CriarEditarInativarDaUnidade : false;

            var listaDeAtualizacao = new List<KeyValuePair<ServicoEstabelecimento, ServicoEstabelecimento>>();
            var listaDeDesativacao = new List<ServicoEstabelecimento>();

            var categoriasServicosEstabelecimentoDestino = Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Queryable().Where(p => p.Estabelecimento == estabelecimentoDestino).ToList();
            log.Info($"[Sincronia] SincronizarServicosEstabelecimentoComModelo {categoriasServicosEstabelecimentoDestino.Count} categorias");
            var itensParaSincronizarBaixa = Domain.EstoqueComBaixaAutomatica.SincroniaDaBaixaAutomaticaService.ListarItensModelosConfiguradosQuePrecisamSerSincronizados(estabelecimentoModelo, estabelecimentoDestino);
            log.Info($"[Sincronia] SincronizarServicosEstabelecimentoComModelo {itensParaSincronizarBaixa.Count} itens para sincronizar baixa");
            var idsDosServicosEstabelecimentoModeloQuePrecisamSerSincronizados = Domain.EstoqueComBaixaAutomatica.SincroniaDaBaixaAutomaticaService.ListarIdsDosServicosModelosQuePrecisamSerSincronizados(estabelecimentoModelo, estabelecimentoDestino);
            log.Info($"[Sincronia] SincronizarServicosEstabelecimentoComModelo {idsDosServicosEstabelecimentoModeloQuePrecisamSerSincronizados.Count} ids dos serviços que precisam ser sincronizados");
            var idPessoaTrinksInterno = new ParametrosTrinks<int>(ParametrosTrinksEnum.id_pessoa_conta_trinks_interno).ObterValor();
            var pessoaTrinksInterno = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaTrinksInterno);
            var dataHoraSincronia = Calendario.Agora();

            var temPermissaoParaDescontosPersonalizados = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                        .ObterDisponibilidadeDeRecurso(estabelecimentoDestino, Recurso.DescontosPersonalizados)
                        .EstaDisponivel;
            log.Info($"[Sincronia] temPermissaoParaDescontosPersonalizados = {temPermissaoParaDescontosPersonalizados}");

            // Já associados ao modelo
            foreach (var seModelo in listaAuxModelo)
            {
                var ses =
                    listaAuxEstabelecimento.Where(
                        f =>
                            f.ServicoEstabelecimentoModelo != null &&
                            f.ServicoEstabelecimentoModelo.IdServicoEstabelecimento == seModelo.IdServicoEstabelecimento)
                        .ToList();

                var seEstabelecimento = ses.FirstOrDefault();
                if (seEstabelecimento != null)
                {
                    listaDeAtualizacao.Add(new KeyValuePair<ServicoEstabelecimento, ServicoEstabelecimento>(seModelo,
                        seEstabelecimento));
                }

                // se houver mais de um associado ao mesmo modelo
                if (ses.Count > 1)
                {
                    listaDeDesativacao.AddRange(ses.Skip(1));
                }
            }

            RemoverDaLista(listaAuxEstabelecimento, listaDeDesativacao);
            RemoverDaLista(listaAuxEstabelecimento, listaDeAtualizacao.Select(f => f.Value));
            RemoverDaLista(listaAuxModelo, listaDeAtualizacao.Select(f => f.Key));


            // Com o mesmo nome
            foreach (var seModelo in listaAuxModelo)
            {
                var seEstabelecimento =
                    listaAuxEstabelecimento.FirstOrDefault(f => f.Nome.ToLower() == seModelo.Nome.ToLower());
                if (seEstabelecimento != null)
                    listaDeAtualizacao.Add(new KeyValuePair<ServicoEstabelecimento, ServicoEstabelecimento>(seModelo,
                        seEstabelecimento));
            }
            RemoverDaLista(listaAuxEstabelecimento, listaDeAtualizacao.Select(f => f.Value));
            RemoverDaLista(listaAuxModelo, listaDeAtualizacao.Select(f => f.Key));

            // Ainda não existentes
            listaDeAtualizacao.AddRange(
                listaAuxModelo.Select(
                    seModelo =>
                        new KeyValuePair<ServicoEstabelecimento, ServicoEstabelecimento>(seModelo,
                           listaAuxEstabelecimento.FirstOrDefault(f => f.Nome.ToLower() == seModelo.Nome.ToLower()) ?? new ServicoEstabelecimento())));

            RemoverDaLista(listaAuxEstabelecimento, listaDeAtualizacao.Select(f => f.Value));
            RemoverDaLista(listaDeDesativacao, listaDeAtualizacao.Select(f => f.Value));
            RemoverDaLista(listaAuxModelo, listaDeAtualizacao.Select(f => f.Key));

            log.Info($"[Sincronia] listaAuxEstabelecimento {listaAuxEstabelecimento.Count} itens");
            log.Info($"[Sincronia] listaDeDesativacao {listaDeDesativacao.Count} itens");
            log.Info($"[Sincronia] listaAuxModelo {listaAuxModelo.Count} itens");

            var listaItensNaoSincronizadosGeral = new List<string>();

            log.Info($"[Sincronia] Iniciando atualizacao {listaDeAtualizacao.Count} itens");
            // Atualizar e incluir
            foreach (var item in listaDeAtualizacao)
            {
                var se = item.Value;
                var seModelo = item.Key;

                var precoAlterado = se.Preco != seModelo.Preco;

                se.CodigoInterno = seModelo.CodigoInterno;
                se.Ativo = seModelo.Ativo;
                se.DataAtualizacao = seModelo.DataAtualizacao;
                se.Descricao = seModelo.Descricao;
                se.Estabelecimento = estabelecimentoDestino;
                se.Nome = seModelo.Nome;
                //removido para que não seja feita sincronia de dados fiscais de serviço.
                //se.CodigoNCM = !string.IsNullOrEmpty(seModelo.CodigoNCM) ? seModelo.CodigoNCM : se.CodigoNCM;

                if (!permissaoPrecoDuracaoDoModelo)
                {
                    se.Duracao = seModelo.Duracao;
                    se.Preco = seModelo.Preco;
                }

                se.PrecoFixo = seModelo.PrecoFixo;
                se.Servico = seModelo.Servico;
                se.ServicoCategoriaEstabelecimento = ObterOuCriarCategoriaPeloModelo(estabelecimentoDestino,
                    seModelo.ServicoCategoriaEstabelecimento, categoriasServicosEstabelecimentoDestino);

                //Alterando serviço indisponivel para cliente...
                se.ServicoIndiposnivelParaCliente = seModelo.ServicoIndiposnivelParaCliente;

                if (se.IdServicoEstabelecimento == 0)
                    se.ExibePreco = seModelo.ExibePreco;

                if (se.ServicoCategoriaEstabelecimento.Codigo == 0)
                    categoriasServicosEstabelecimentoDestino.Add(se.ServicoCategoriaEstabelecimento);

                se.ServicoCategoriaEstabelecimento.Ativo = seModelo.ServicoCategoriaEstabelecimento.Ativo;
                se.ServicoEstabelecimentoModelo = seModelo;
                se.TipoPreco = seModelo.TipoPreco;

                if (seModelo.Promocao != null && !permissaoPromocaoDoModelo)
                {
                    var promocao = Domain.Promocoes.PromocaoService.CopiarPromocaoParaServico(seModelo.Promocao, se);
                    se.Promocao = promocao;
                }
                else if (seModelo.Promocao == null && se.Promocao != null)
                {
                    Domain.Promocoes.PromocaoService.CancelarPromocao(se.Promocao.IdPromocao);
                }

                if (se.IdServicoEstabelecimento == 0)
                    Domain.Pessoas.ServicoEstabelecimentoRepository.SaveNew(se);
                else
                    Domain.Pessoas.ServicoEstabelecimentoRepository.Update(se);

                if (!permissaoSaidaAutomaticaCustoOperacionalDoModelo)
                {
                    Domain.EstoqueComBaixaAutomatica.SincroniaDaBaixaAutomaticaService.SincronizarConfiguracoesDoServico(seModelo, se, idsDosServicosEstabelecimentoModeloQuePrecisamSerSincronizados, dataHoraSincronia);
                    var listaItensNaoSincronizados = Domain.EstoqueComBaixaAutomatica.SincroniaDaBaixaAutomaticaService.SincronizarConfiguracaoDeSaidaDoEstoqueEntreServicos(seModelo, se, itensParaSincronizarBaixa, pessoaTrinksInterno, dataHoraSincronia);
                    listaItensNaoSincronizadosGeral.AddRange(listaItensNaoSincronizados);

                    SincronizarDescontoDeServico(seModelo, se, usaDescontosPersonalizados: temPermissaoParaDescontosPersonalizados);
                }

                if (precoAlterado)
                    servicosEstabelecimentosPrecoAlterado.Add(se);
            }
            log.Info($"[Sincronia] Atualizacao finalizada");

            if (listaItensNaoSincronizadosGeral.Any())
            {
                var nomeEstabelecimento = estabelecimentoDestino.NomeDeExibicaoNoPortal;
                Domain.Pessoas.EnvioEmailService.EnviarEmailItensNaoSincronizadoBaixaAutomatica(listaItensNaoSincronizadosGeral, estabelecimentoModelo, nomeEstabelecimento);
            }



            if (!permissaoCriarEditarInativarDaUnidade)
            {
                var listaDeServicosCriadosNaUnidade = listaAuxEstabelecimento.Where(p => p.ServicoEstabelecimentoModelo == null).ToList();
                listaDeDesativacao.AddRange(listaDeServicosCriadosNaUnidade);
            }
            log.Info($"[Sincronia] RemoverServicoEstabelecimento {listaDeDesativacao.Count} itens");
            // Desativar
            Domain.Pessoas.ServicoEstabelecimentoService.RemoverServicoEstabelecimento(
                listaDeDesativacao.Select(f => f.IdServicoEstabelecimento).ToList(), false);
            log.Info($"[Sincronia] Desativacao finalizada");

            // Definir valores de agendamentos futuros
            RedefinirValoresAgendamentosFuturos(estabelecimentoDestino, servicosEstabelecimentosPrecoAlterado);
            log.Info($"[Sincronia] Redefinir valores de agendamentos futuros finalizado");
        }

        private static void SincronizarDescontoDeServico(ServicoEstabelecimento seModelo, ServicoEstabelecimento se, bool usaDescontosPersonalizados)
        {
            se.CustoDescartaveis = seModelo.CustoDescartaveis;

            var servicoDescontoModelo = Domain.Financeiro.DescontoPersonalizadoServicoRepository
                .Obter(seModelo.IdServicoEstabelecimento, seModelo.Estabelecimento.IdEstabelecimento);

            if (servicoDescontoModelo == null || !servicoDescontoModelo.DescontoPersonalizado.Ativo)
            {
                Domain.Financeiro.ServicoDescontoPersonalizadoService.RemoverDescontoDeServicoSeHouver(se);

                return;
            }

            if (!usaDescontosPersonalizados)
            {
                return;
            }

            Domain.Financeiro.ServicoDescontoPersonalizadoService
                .ManterDescontoDeServico(
                    se,
                    servicoDescontoModelo.DescontoPersonalizado.Tipo,
                    servicoDescontoModelo.DescontoPersonalizado.Valor, false);
        }

        #endregion Servicos

        #region Clube de assinaturas
        public List<PlanoCliente> ListarPlanosNaoSincronizados(int idEstabelecimentoModelo, int idEstabelecimentoDestino = 0)
        {
            var estabelecimentoModelo = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoModelo);


            Estabelecimento estabelecimento = null;
            bool verificaCamposFlexiveis = true;

            if (idEstabelecimentoDestino > 0)
            {
                estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);
            }
            else
            {
                (bool verificaItensFlexiveis, Estabelecimento unidade) dados = ObterEstabelecimentoDoModelo(idEstabelecimentoModelo, TipoSincronizacaoEnum.Servicos);

                estabelecimento = dados.unidade;
                verificaCamposFlexiveis = dados.verificaItensFlexiveis;
            }

            if (idEstabelecimentoDestino == 0 && estabelecimento == null)
                return null;

            var planosClienteModelo = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterPlanosAtivos(estabelecimentoModelo.IdEstabelecimento);

            IEnumerable<PlanoCliente> retorno;
            if (estabelecimento != null)
            {
                var planosCliente = Domain.ClubeDeAssinaturas.PlanoClienteRepository.ObterPlanos(estabelecimento.IdEstabelecimento);
                retorno = ObterPlanosExistentesDoClubeDeAssinaturas(planosCliente, planosClienteModelo, estabelecimentoModelo, estabelecimento, verificaCamposFlexiveis);
            }
            else
                retorno = planosClienteModelo;

            return retorno.Where(f => f != null).ToList();
        }

        private IEnumerable<PlanoCliente> ObterPlanosExistentesDoClubeDeAssinaturas(IList<PlanoCliente> planosDoClubeDeAssinaturaDoEstabelecimentoDestino, IList<PlanoCliente> planosDoClubeDeAssinaturaDoModelo, Estabelecimento estabelecimentoModelo, Estabelecimento estabelecimentoUnidade, bool verificaCamposFlexiveis)
        {
            var planosDoClubeDeAssinaturaDiferentesEntreUnidadeEModelo = ObterPlanosDiferentesEntreUnidadeEModelo(planosDoClubeDeAssinaturaDoEstabelecimentoDestino, estabelecimentoModelo, estabelecimentoUnidade, verificaCamposFlexiveis);

            var planosASeremSincronizados =
                planosDoClubeDeAssinaturaDoModelo.Where(f => planosDoClubeDeAssinaturaDoEstabelecimentoDestino.All(g => g.PlanoClienteModelo == null || g.PlanoClienteModelo.Id != f.Id));

            var retorno = planosDoClubeDeAssinaturaDiferentesEntreUnidadeEModelo.Union(planosASeremSincronizados);

            return retorno;
        }

        private List<PlanoCliente> ObterPlanosDiferentesEntreUnidadeEModelo(IList<PlanoCliente> planosDoClubeDeAssinaturaDoEstabelecimentoDestino, Estabelecimento estabelecimentoModelo, Estabelecimento estabelecimentoUnidade, bool verificaCamposFlexiveis)
        {

            var planosDoClubeDeAssinaturaDiferentesEntreUnidadeEModelo = planosDoClubeDeAssinaturaDoEstabelecimentoDestino
                    .Where(f => f.PlanoClienteModelo == null ||
                            (f.Ativo != f.PlanoClienteModelo.Ativo ||
                             f.DadosDoPlanoCliente.Nome != f.PlanoClienteModelo.DadosDoPlanoCliente.Nome ||
                             f.DadosDoPlanoCliente.Descricao != f.PlanoClienteModelo.DadosDoPlanoCliente.Descricao ||
                             (verificaCamposFlexiveis && f.DadosDoPlanoCliente.Valor != f.PlanoClienteModelo.DadosDoPlanoCliente.Valor) ||
                             (verificaCamposFlexiveis && f.DadosDoPlanoCliente.CicloDeCobranca != f.PlanoClienteModelo.DadosDoPlanoCliente.CicloDeCobranca) ||
                             f.PlanoClienteModelo.DadosDoPlanoCliente.IdEstabelecimento != estabelecimentoModelo.IdEstabelecimento ||
                             (verificaCamposFlexiveis && f.DadosDoPlanoCliente.EncerraAposXCobrancas != f.PlanoClienteModelo.DadosDoPlanoCliente.EncerraAposXCobrancas) ||
                             f.BeneficiosDoPlano.Where(g => g.Ativo).Count() != f.PlanoClienteModelo.BeneficiosDoPlano.Where(g => g.Ativo).Count() ||
                             f.BeneficiosDoPlano.Where(g => g.Ativo).Sum(g => g.Beneficio.ValorUnitario) != f.PlanoClienteModelo.BeneficiosDoPlano.Where(g => g.Ativo).Sum(g => g.Beneficio.ValorUnitario) ||
                             f.BeneficiosDoPlano.Where(g => g.Ativo && g.Beneficio.ConsumoLimitado).Sum(g => g.Beneficio.QuantidadeMaximaConsumo) != f.PlanoClienteModelo.BeneficiosDoPlano.Where(g => g.Ativo && g.Beneficio.ConsumoLimitado).Sum(g => g.Beneficio.QuantidadeMaximaConsumo) ||
                             f.BeneficiosDoPlano.Where(g => g.Ativo && !g.Beneficio.ConsumoLimitado).Sum(a => 1) != f.PlanoClienteModelo.BeneficiosDoPlano.Where(g => g.Ativo && !g.Beneficio.ConsumoLimitado).Sum(a => 1) ||
                             f.BeneficiosDoPlano.Where(g => g.Ativo).Select(g => g.Beneficio.ObterDescricao()) != f.PlanoClienteModelo.BeneficiosDoPlano.Where(g => g.Ativo).Select(g => g.Beneficio.ObterDescricao())
                            )
                    )
                    .Select(f => f.PlanoClienteModelo).ToList();

            return planosDoClubeDeAssinaturaDiferentesEntreUnidadeEModelo;
        }

        public void SincronizarPlanosDoClubeDeAssinaturaComModelo(int idEstabelecimentoDestino)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimentoDestino);

            var estabelecimentoModelo = estabelecimento.EstabelecimentoModelo() ?? throw new Exception("Este estabelecimento não possui estabelecimento modelo.");

            var planosNaoSincronizados = ListarPlanosNaoSincronizados(estabelecimentoModelo.IdEstabelecimento, idEstabelecimentoDestino);
            log.Info($"[Sincronia] Iniciando SincronizarPlanosDoClubeDeAssinaturaComModelo {planosNaoSincronizados.Count()} planos");
            Domain.ClubeDeAssinaturas.SincroniaDePlanoService.SincronizarPlanosDoClubeDeAssinaturaComModelo(planosNaoSincronizados, idEstabelecimentoDestino);
            log.Info($"[Sincronia] SincronizarPlanosDoClubeDeAssinaturaComModelo finalizado");
        }

        #endregion
    }
}
