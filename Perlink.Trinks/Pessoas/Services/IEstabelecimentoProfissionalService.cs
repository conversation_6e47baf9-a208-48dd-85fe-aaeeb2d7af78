﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Services
{

    public interface IEstabelecimentoProfissionalService : IService
    {

        void AlterarCodigoPAT(int idEstabelecimentoProfissional, string novoCodigoPAT);

        EstabelecimentoProfissional AssociarPFaoEstabelecimentoComoProfissional(PessoaFisica pessoa, Estabelecimento estabelecimento);

        EstabelecimentoProfissional CadastrarNovo(Estabelecimento estabelecimento, Profissional profissional);

        bool EstabelecimentoProfissionalPodeTerAssistente(int? idEstabelecimentoProfissional, int? idServicoEstabelecimento);

        void Inativar(int idEstabelecimentoProfissional);

        IntervaloDataList ObterHorarioDeTrabalho(DateTime dia, HorarioTrabalho horarioTrabalho);

        IntervaloDataList ObterHorarioDeTrabalhoConsiderandoHorarioEspecial(Estabelecimento estabelecimento, DateTime dia, HorarioTrabalho horarioTrabalho);

        string ObterProximoCodigoInterno(int idEstabelecimento);

        bool ProfissionalPodeTerAssistente(int? idProfissional, int? idServicoEstabelecimento);

        void Reativar(int idEstabelecimentoProfissional);

        void ValidarCodigoInterno(int idEstabelecimentoProfissional, string codigoInterno, int idEstabelecimento);

        void ConfigurarComissaoDosProfissionaisNoEstabelecimento(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral);

        //void AlterarExcecaoDeDataRecebimentoComissao(EstabelecimentoProfissional estabelecimentoProfissional, bool ativarExcessaoDoProfissional, bool configuracaoNoEstabelecimento);

        //void AlterarExcecaoDeDescontoComissaoProfissional(EstabelecimentoProfissional estabelecimentoProfissional, bool descontarTaxaOperadora, bool configuracaoNoEstabelecimento);

        //void AlterarExcecaoDeDescontoComissaoAssistente(EstabelecimentoProfissional estabelecimentoProfissional, bool descontarTaxaOperadora, bool configuracaoNoEstabelecimento);
        List<EstabelecimentoProfissionalDTO> ObterProfissionaisEItensRelacionadosPorTransacao(int idTransacao);

        void ValidarEstabelecimentoProfissionalEstaAtivo(int idEstabelecimentoProfissional);
        void RealizarPushParaSNS(EstabelecimentoProfissional estabelecimentoProfissional);
        bool TemAcessoAoResumoAgenda(Estabelecimento estabelecimentoAutenticado, EstabelecimentoProfissional estabelecimentoProfissional, bool modoPAT, bool acessoTotalOuRecepcao);

        List<KeyValuePair<int, string>> ObterEstabelecimentoProfissionaisParaCampanha(int idEstabelecimento);

        void ReplicarHorariosParaProfissionaisDoEstabelecimento(int idEstabelecimentoProfissionalParaCopiar, List<HorarioTrabalho> horariosParaCopiar, int[] idsDeEstabelecimentoProfissionalParaIgnorar);

        DadosParaCadastrarRecebedorDto ObterProfissionalParaEdicaoDeSplit(int idEstabelecimentoProfissional);

        void EditarDadosDoRecebedor(DadosParaCadastrarRecebedorDto dto);

        bool ValidarSePjPertenceAoEstabelecimentoOuProfissional(int idPessoaJuridicaValidar, Estabelecimento estabelecimento);

        bool PossuiSplitHabilitado(int idPessoa, int idEstabelecimento);

        bool UtilizaMenuLateral(string email, int idConta, int idEstabelecimento);

        void AtivarSplitDePagamentoParaEstabelecimentoProfissional(int idEstabelecimento, string stoneCode, string affiliationCode);
    }
}