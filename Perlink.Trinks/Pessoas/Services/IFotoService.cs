﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Fotos.DTO;
using System;
using System.Web;

namespace Perlink.Trinks.Pessoas.Services
{

    public interface IFotoService : IService
    {

        void DefineFotoPrincipal(Pessoa pessoa, int idFotoPesssoa);

        bool EstourouLimiteFotos(Pessoa pessoa);

        void RotacionarImagem(Foto foto, bool sentidoHorario);

        void ExcluirFotoEstabelecimento(int codigoFoto);

        void ExcluirFotoPessoa(int codigoFoto);

        void ExcluirFoto(Foto foto);

        Foto SalvarFoto(Foto foto, HttpPostedFileBase arquivo);

        Foto SalvarFotoApi(Foto foto, HttpPostedFile arquivo);

        Foto SalvarFotoPorUrl(Foto foto, String urlExterna);

        void ValidarQuantidadeDeFotos(Pessoa pessoa);

        Foto SalvarUploadDeFoto(Foto foto, SolicitacaoDeUploadDeFoto arquivo);

        FotoEstabelecimento SalvarUploadDeFotoEstabelecimento(Foto foto, SolicitacaoDeUploadDeFoto arquivo, int? idEstabelecimento);

        Foto SalvarFotoLogo(Foto foto, HttpPostedFileBase arquivo);

        bool ExisteArquivo(Foto foto);
    }
}