﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using System;
using System.Linq;
using System.Web;

namespace Perlink.Trinks.Pessoas.Services
{

    public class DadosCadastraisDoUsuarioService : BaseService, IDadosCadastraisDoUsuarioService
    {

        public PerfilUsuarioDTO CarregarDadosDoPerfil(int idConta, string versaoApp = "")
        {
            var buscaConta = Domain.Pessoas.ContaRepository.Queryable();
            var buscaPessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var pessoa = (from c in buscaConta
                          join pf in buscaPessoaFisica on c.Pessoa.IdPessoa equals pf.IdPessoa
                          where c.IdConta == idConta
                          select new
                          {
                              IdPessoa = pf.IdPessoa,
                              Nome = pf.NomeCompleto,
                              Email = c.Email,
                              Sexo = pf.Genero,
                              Cpf = pf.Cpf,
                              Newsletter = c.ReceberNews,
                              DataNascimento = pf.DataNascimento,
                              CodigoFacebook = c.CodigoFacebook
                          })
                        .FirstOrDefault();

            if (pessoa == null)
                throw new ArgumentNullException("Foi realizado tentativa em obter dados de uma pessoa não existente.");

            var telefones = Domain.Pessoas.TelefoneRepository.StatelessQueryable()
                .Where(tel => tel.IdPessoa == pessoa.IdPessoa && tel.Dono.IdPessoa == pessoa.IdPessoa && tel.Ativo)
                .Select(tel => new TelefoneDTO
                {
                    Id = tel.IdTelefone,
                    Ddi = tel.Ddi,
                    DDD = tel.DDD,
                    Numero = tel.Numero
                })
                .ToList();

            telefones = Domain.Pessoas.TelefoneService.ValidarExibicaoTelefonesInternacionaisAppB2c(telefones, versaoApp);

            var fotoPrincipal = Domain.Pessoas.FotoPessoaRepository.FotoDePerfilDoUsuario(pessoa.IdPessoa);

            var perfil = new PerfilUsuarioDTO(pessoa.Nome, pessoa.Email, pessoa.DataNascimento, pessoa.Sexo, pessoa.Cpf, pessoa.Newsletter, telefones, fotoPrincipal, pessoa.CodigoFacebook);

            return perfil;
        }

        [TransactionInitRequired]
        public void AtualizarDadosDoPerfil(PerfilUsuarioParaAtualizacaoDTO dados, string versaoApp = "")
        {
            int? idContaAutenticada = Domain.WebContext.IdContaAutenticada;
            if (!idContaAutenticada.HasValue)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Um usuário autenticado é necessário para a execução deste método.");
                return;
            }

            int idPessoa = Domain.Pessoas.ContaRepository.ObterIdPessoaDaConta(idContaAutenticada.Value);

            dados.ValidarDadosDoPerfil(idPessoa, verificaSeContaEstaDesativada: true);
            if (!ValidationHelper.Instance.IsValid)
                return;

            string cpfFormatado = dados.Cpf.RemoverFormatacaoCPFeCPNJ();

            var pessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Factory
                .CreateParaAtualizarDadosDoPerfil(idPessoa, dados.Nome, dados.Sexo, cpfFormatado, dados.Email, dados.DataNascimento);

            var primeiraConta = pessoaFisica.PrimeiraConta;
            primeiraConta.Email = dados.Email;
            primeiraConta.ReceberNews = dados.Newsletter;

            Domain.Pessoas.PessoaService.ManterPessoaFisica(pessoaFisica);
            Domain.Pessoas.TelefoneService.ManterTelefonesNovosAtualizadosERemovidos(dados.Telefones, pessoaFisica, versaoApp);
        }

        public void RedefinirSenhaAtual(int idConta, string senhaAtual, string novaSenha)
        {
            Domain.Pessoas.ContaService.ValidarSenhaDaConta(idConta, senhaAtual);

            if (!ValidationHelper.Instance.IsValid)
                return;

            var conta = Domain.Pessoas.ContaRepository.Factory.CreateParaRedefinicaoDeSenha(idConta, novaSenha);

            Domain.Pessoas.ContaService.ManterConta(conta);
        }

        public FotoPessoa AlterarFotoDoPerfil(int idConta, HttpPostedFile arquivo)
        {
            int idPessoa = Domain.Pessoas.ContaRepository.ObterIdPessoaDaConta(idConta);

            FotoPessoa fotoPessoa = Domain.Pessoas.FotoPessoaRepository.FotoDePerfilDoUsuario(idPessoa);

            if (fotoPessoa == null)
            {
                Pessoa pessoa = Domain.Pessoas.PessoaRepository.Load(idPessoa);
                fotoPessoa = Domain.Pessoas.FotoPessoaRepository.Factory.CreateFotoPrincipal(pessoa);
            }
            else
            {
                fotoPessoa.Principal = true;
            }

            var foto = Domain.Pessoas.ProfissionalService.DefinirFotoPadraoApi(fotoPessoa, arquivo);

            return foto;
        }

        public FotoPessoa ObterFotoAPartirDeProvedorExterno(int idConta, string provedorExterno)
        {
            Conta conta = Domain.Pessoas.ContaRepository.Load(idConta);
            bool estaAssociadoAoFacebook = !String.IsNullOrEmpty(conta.CodigoFacebook);

            if (!estaAssociadoAoFacebook)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Sua conta não está associada ao Facebook.");
                return null;
            }

            int idPessoa = Domain.Pessoas.ContaRepository.ObterIdPessoaDaConta(idConta);
            var fotoPessoa = Domain.Pessoas.FotoPessoaRepository.FotoDePerfilDoUsuario(idPessoa);

            if (fotoPessoa == null)
            {
                Pessoa pessoa = Domain.Pessoas.PessoaRepository.Load(idPessoa);

                fotoPessoa = new FotoPessoa()
                {
                    Pessoa = pessoa,
                    Principal = true
                };
            }

            try
            {
                Domain.Pessoas.FotoService.SalvarFotoPorUrl(fotoPessoa, String.Format(ConfiguracoesTrinks.Facebook.LinkParaFotoPerfil, conta.CodigoFacebook));
                return fotoPessoa;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}