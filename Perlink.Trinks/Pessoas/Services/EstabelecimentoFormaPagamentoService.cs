using Newtonsoft.Json;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Trinks.Integracoes.Stone.Connect.Dtos;

namespace Perlink.Trinks.Pessoas.Services
{

    public class EstabelecimentoFormaPagamentoService : BaseService, IEstabelecimentoFormaPagamentoService
    {
        private const int IdFormaPagamentoDescontoProfissional = (int)FormaPagamentoEnum.DescontoDeProfissional;

        public EstabelecimentoFormaPagamento AssociarFormaDePagamento(FormaPagamento formaPagamento, Estabelecimento estabelecimento)
        {
            var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(estabelecimento.IdEstabelecimento, formaPagamento.Id)
                 ?? new EstabelecimentoFormaPagamento(formaPagamento)
                 {
                     Estabelecimento = estabelecimento
                 };

            if (estabelecimentoFormaPagamento.Id == 0)
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveNew(estabelecimentoFormaPagamento);
            else
            {
                estabelecimentoFormaPagamento.Ativo = true;
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Update(estabelecimentoFormaPagamento);
            }

            return estabelecimentoFormaPagamento;
        }

        public void AssociarFormaDePagamentoPorIdFormaDePagamento(int idFormaPagamento, int idEstabelecimento)
        {
            var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(idEstabelecimento, idFormaPagamento);
            estabelecimentoFormaPagamento.Ativo = true;
            Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveOrUpdate(estabelecimentoFormaPagamento);
        }

        public void Inativar(EstabelecimentoFormaPagamento entidade)
        {
            if (entidade.FormaPagamento == FormaPagamentoEnum.Pix)
            {
                var contaDigitalEstabelecimento =
                    Domain.ContaDigital.ContaEstabelecimentoRepository.ObterPorIdEstabelecimento(entidade.Estabelecimento
                        .IdEstabelecimento);

                if (contaDigitalEstabelecimento != null && contaDigitalEstabelecimento.Ativo)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Não é possível desativar a forma de pagamento PIX em estabelecimento que possua Conta Digital.");
                    return;
                }
            }

            entidade.Ativo = false;

            if (entidade.FormaPagamento == FormaPagamentoEnum.PagamentoOnline)
            {
                Domain.Pessoas.EstabelecimentoService.DesabilitarMotivoDeDescontoDoTrinks(entidade.Estabelecimento, MotivoDeDescontoDoTrinksEnum.PagamentoOnline);
            }

            if (entidade.FormaPagamento == FormaPagamentoEnum.PagamentoOnlineHotsite)
            {
                Domain.Pacotes.PacoteService.RemoverTodosPacotesDaVendaHotsite(entidade.Estabelecimento.IdEstabelecimento);
            }

            if (entidade.FormaPagamento == FormaPagamentoEnum.DescontoDeProfissional)
            {
                entidade.AceitaParcelamento = false;
            }

            Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveOrUpdate(entidade);
        }

        public void Inativar(int idFormaPagamento, int idEstabelecimento)
        {
            if (!EhValidaInativacaoDeDescontoProfissional(idFormaPagamento, idEstabelecimento))
                return;
            
            var estabelecimentoFormaPagamento =
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable()
                    .FirstOrDefault(
                        f =>
                            f.FormaPagamento.Id == idFormaPagamento &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            if (estabelecimentoFormaPagamento != null)
                Inativar(estabelecimentoFormaPagamento);
        }

        private bool EhValidaInativacaoDeDescontoProfissional(int idFormaPagamento, int idEstabelecimento)
        {
            if (idFormaPagamento != (int)FormaPagamentoEnum.DescontoDeProfissional)
                return true;

            var temPermissaoProdutoParcelado = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(idEstabelecimento, Recurso.VendaDeProdutoParceladoParaProfissional)
                .EstaDisponivel;

            if (temPermissaoProdutoParcelado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    "O desconto de profissional não pode ser desativado, pois o estabelecimento possui permissão para venda de produto parcelado.");

                return false;
            }

            return true;
        }

        public void Ativar(int idFormaPagamento, int idEstabelecimento)
        {
            var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable().FirstOrDefault(f => f.FormaPagamento.Id == idFormaPagamento && f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (estabelecimentoFormaPagamento != null)
            {
                Ativar(estabelecimentoFormaPagamento);
            }
        }

        private void Ativar(EstabelecimentoFormaPagamento entidade)
        {
            entidade.Ativo = true;

            if (entidade.FormaPagamento == FormaPagamentoEnum.PagamentoOnline)
            {
                Domain.Pessoas.EstabelecimentoService.CriarOuDesativarMotivoDeDescontoDePagamentoOnline(entidade.Estabelecimento, entidade);
            }
            Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveOrUpdate(entidade);
        }

        public void Manter(List<EstabelecimentoFormaPagamento> entidades)
        {
            ValidarManter(entidades);

            if (!ValidationHelper.Instance.IsValid) return;
            if (!entidades.Any()) return;
            var estabelecimento = entidades.FirstOrDefault().Estabelecimento;
            var formasPagamentoDoEstabelecimento =
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ListarPorEstabelecimento(
                    estabelecimento.IdEstabelecimento);

            foreach (var entidade in entidades)
            {
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveOrUpdate(entidade);
            }

            var entidadesParaInativar = formasPagamentoDoEstabelecimento.Except(entidades);
            foreach (var entidade in entidadesParaInativar)
            {
                Inativar(entidade);
            }
        }

        public decimal ObterPercentualDescontoOperadora(int idEstabelecimento,
            int idFormaPagamento,
            int numeroParcelas = 1)
        {
            var efp =
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(
                    idEstabelecimento, idFormaPagamento);

            return ObterPercentualDescontoOperadora(efp, numeroParcelas);
        }

        public decimal ObterPercentualDescontoOperadora(EstabelecimentoFormaPagamento efp, int numeroParcelas = 1)
        {
            var desconto = efp.PercentualDescontoNaComissao ?? 0;

            var primeiraParcela = efp.Parcelamento.FirstOrDefault(f => f.Ativo && f.NumeroParcela == 1);
            if (primeiraParcela != null)
                desconto = primeiraParcela.DescontoOperadora;

            if (numeroParcelas > 1)
            {
                var parcela = efp.Parcelamento.FirstOrDefault(f => f.Ativo && f.NumeroParcela == numeroParcelas);
                if (parcela != null)
                    desconto = parcela.DescontoOperadora;
            }

            return desconto;
        }

        private void ValidarManter(IEnumerable<EstabelecimentoFormaPagamento> entidades)
        {
            foreach (var entidade in entidades)
            {
                ValidarManter(entidade);
            }
        }

        private void ValidarManter(EstabelecimentoFormaPagamento entidade)
        {
            if (entidade.FormaPagamento.TemControleOperadora)
            {
                if (entidade.PercentualCobradoPelaOperadora < 0 || entidade.PercentualCobradoPelaOperadora > 100)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "O percentual pago pela operadora deve ser entre 0% e 100%");
                }

                if (entidade.DiasParaReceberDaOperadora < 0)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(
                        "O número de dia para recebimento deve ser maior ou igual a 0");
                }
            }
        }

        [TransactionInitRequired]
        public void ManterFormasDePagamentoBelezinha(Estabelecimento estabelecimento)
        {
            var formasPagamentoBelezinha = Domain.Financeiro.FormaPagamentoRepository.ListarTodasBelezinhaAtivos();

            if (estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaBelezinha)
            {
                formasPagamentoBelezinha.ToList().ForEach(a =>
                {
                    AssociarFormaDePagamento(a, estabelecimento);
                });
            }
            else
            {
                formasPagamentoBelezinha.ToList().ForEach(a =>
                {
                    Inativar(a.Id, estabelecimento.IdEstabelecimento);
                });
            }
        }

        public void AssociarFormaPagamentoParaVendaHotsite(Estabelecimento estabelecimento)
        {
            var formaPagamento = Domain.Financeiro.FormaPagamentoRepository.Queryable().Single(p => p.Id == (int)FormaPagamentoEnum.PagamentoOnlineHotsite);
            var estabFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(estabelecimento.IdEstabelecimento, formaPagamento.Id);

            if (estabFormaPagamento != null)
            {
                estabFormaPagamento.Ativo = true;
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Update(estabFormaPagamento);
                return;
            }

            var estabFormaPagamentoVendaHotsite = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Factory.CreateEstabelecimentoFormaPagamentoParaVendaHotsite(estabelecimento, formaPagamento);

            Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveNew(estabFormaPagamentoVendaHotsite);
        }

        public void SalvarAlteracoes(EstabelecimentoFormaPagamento estabelecimentoFormaPagamento)
        {
            Manter(estabelecimentoFormaPagamento);
        }

        private void Manter(EstabelecimentoFormaPagamento estabelecimentoFormaPagamento)
        {
            Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveOrUpdate(estabelecimentoFormaPagamento);

            if (estabelecimentoFormaPagamento.FormaPagamento == FormaPagamentoEnum.PagamentoOnline)
            {
                Domain.Pessoas.EstabelecimentoService.CriarOuDesativarMotivoDeDescontoDePagamentoOnline(estabelecimentoFormaPagamento.Estabelecimento, estabelecimentoFormaPagamento);
            }
        }

        public void AtualizarStatusDaFormaDePagamento(int idEstabelecimento, int idFormaDePagamento)
        {
            var formaDePagamentoDoEstabelecimento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamento(idEstabelecimento, idFormaDePagamento);

            if (formaDePagamentoDoEstabelecimento is null)
                return;

            if (formaDePagamentoDoEstabelecimento.Ativo)
            {
                formaDePagamentoDoEstabelecimento.Ativo = false;
            }
            else
            {
                formaDePagamentoDoEstabelecimento.Ativo = true;
            }
            Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.SaveOrUpdate(formaDePagamentoDoEstabelecimento);
        }

        public void SalvarFormaDePagamentoDoEstabelecimento(int idEstabelecimento, int idTipoForma, string nomeFormaDePagamento)
        {
            try
            {
                var session = Castle.ActiveRecord.ActiveRecordMediator.GetSessionFactoryHolder().CreateSession(typeof(Castle.ActiveRecord.ActiveRecordBase));
                NHibernate.IQuery query = session.CreateSQLQuery("exec AdicionarFormaPagamentoEstabelecimento @idEstabelecimento=:idEstabelecimento, @idTipoForma=:idTipoForma, @nome=:nome");
                query.SetInt32("idEstabelecimento", idEstabelecimento);
                query.SetInt32("idTipoForma", idTipoForma);
                query.SetString("nome", nomeFormaDePagamento);
                query.ExecuteUpdate();
            }
            catch (Exception ex)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Ocorreu um erro inesperado, entre em contato com a fabrica para ajudarmos !");
            }
        }

        [Obsolete("Este método não considera todas as regras. Usar ListarFormasDePagamentoParaFechamentoDeContas")]
        public IList<EstabelecimentoFormaPagamento> ListarOpcoesDeFormasDePagamentoParaFechamentoDeContas(Estabelecimento estabelecimento)
        {
            var formasPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ListarAtivasPorEstabelecimento(estabelecimento.IdEstabelecimento);

            RemoverPagamentoOnlineDasFormasDePagamento(formasPagamento);
            RemoverClubeDeAssinaturasDasFormasDePagamento(formasPagamento);
            RemoverPagamentoOnlineHotsiteDasFormasDePagamento(formasPagamento);
            RemoverDescontoProfissionalDasFormasDePagamento(formasPagamento);
            RemoverPagarmeDasFormasDePagamento(formasPagamento);

            bool pagamentoOnlinePorLinkEstaDisponivel = VerificarPagamentoOnlinePorLink(estabelecimento);
            if (!pagamentoOnlinePorLinkEstaDisponivel)
            {
                RemoverPagamentoOnlinePorLinkDasFormasDePagamento(formasPagamento);
            }

            formasPagamento = RemoverPixIntegradoDoConnectStone(formasPagamento);

            return formasPagamento;
        }

        public List<EstabelecimentoFormaPagamento> ListarFormasDePagamentoParaFechamentoDeContas(bool ehCompraCredito = false)
        {
            var formasPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .ListarAtivasPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            formasPagamento = RemoverPagamentoOnlineDasFormasDePagamento(formasPagamento);
            formasPagamento = RemoverPagamentoPOSSeEstabelecimentoNaoEstiverConfigurado(formasPagamento);
            formasPagamento = RemoverCreditoDeClienteCasoSejaCompraDeCredito(formasPagamento, ehCompraCredito);
            formasPagamento = RemoverFormasDePagamentoPeloMecanismoDeDisponibilidade(formasPagamento);
            formasPagamento = RemoverClubeDeAssinaturasDasFormasDePagamento(formasPagamento);
            formasPagamento = RemoverPagamentoOnlineHotsiteDasFormasDePagamento(formasPagamento);
            formasPagamento = RemoverDescontoProfissionalDasFormasDePagamento(formasPagamento);
            formasPagamento = RemoverPixIntegradoDoConnectStone(formasPagamento);
            formasPagamento = RemoverPagarmeDasFormasDePagamento(formasPagamento);

            return formasPagamento
                .OrderBy(f => f.FormaPagamento.Tipo.OrdemNoFechamentoDeConta)
                .ThenBy(f => f.FormaPagamento.Tipo.Id)
                .ThenBy(f => f.FormaPagamento.Nome).ToList();
        }

        private List<EstabelecimentoFormaPagamento> RemoverPixIntegradoDoConnectStone(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            var exibirOpcaoDePagamentoComPixIntegradoPelaBelezinhaNoFechamentoDeConta = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.ExibirOpcaoDePagamentoComPixIntegradoPelaBelezinha).EstaDisponivel;
            if (exibirOpcaoDePagamentoComPixIntegradoPelaBelezinhaNoFechamentoDeConta)
                return formasPagamento;

            var estabelecimentoFormaPagamento = formasPagamento.FirstOrDefault(fp => fp.FormaPagamento.Tipo.Id == (int)FormaPagamentoTipoEnum.Pix
                                                       && fp.FormaPagamento.TipoPOS != null
                                                       && fp.FormaPagamento.TipoPOS.EhConnectStone);

            if (estabelecimentoFormaPagamento != null)
                formasPagamento.Remove(estabelecimentoFormaPagamento);

            return formasPagamento;
        }

        private List<EstabelecimentoFormaPagamento> RemoverPagamentoOnlineHotsiteDasFormasDePagamento(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.PagamentoOnlineHotsite);
            return formasPagamento;
        }
        
        private List<EstabelecimentoFormaPagamento> RemoverPagarmeDasFormasDePagamento(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.PagarmeCredito);
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.PagarmePix);
            return formasPagamento;
        }

        private List<EstabelecimentoFormaPagamento> RemoverClubeDeAssinaturasDasFormasDePagamento(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            formasPagamento.RemoveAll(fp => fp.FormaPagamento.Tipo == FormaPagamentoTipoEnum.ClubeDeAssinaturas);
            return formasPagamento;
        }

        private List<EstabelecimentoFormaPagamento> RemoverFormasDePagamentoPeloMecanismoDeDisponibilidade(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            var tfpPagarDepois = formasPagamento.FirstOrDefault(f => f.FormaPagamento == FormaPagamentoEnum.DeixarFaltaComoDivida);

            if (tfpPagarDepois != null)
            {
                var debitoParcial = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, ControleDeFuncionalidades.ObjetosDeValor.Recurso.DebitoParcial);
                formasPagamento.Remove(tfpPagarDepois);
            }

            return formasPagamento;
        }

        private List<EstabelecimentoFormaPagamento> RemoverCreditoDeClienteCasoSejaCompraDeCredito(List<EstabelecimentoFormaPagamento> formasPagamento, bool ehCompraCredito)
        {
            if (ehCompraCredito)
                formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.CreditoCliente);
            return formasPagamento;
        }

        private static List<EstabelecimentoFormaPagamento> RemoverPagamentoPOSSeEstabelecimentoNaoEstiverConfigurado(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            var configPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(Domain.WebContext.IdEstabelecimentoAutenticado.Value);
            if (configPos == null || !configPos.ConcluiuConfiguracao)
            {
                formasPagamento.RemoveAll(a => a.FormaPagamento.TipoPOS != null);
                return formasPagamento;
            }

            var ehIntegracaoConnect = configPos.TipoPOS.Id == (int)SubadquirenteEnum.ConnectPagarme ||
                                      configPos.TipoPOS.Id == (int)SubadquirenteEnum.ConnectStone;

            if (!ehIntegracaoConnect) return formasPagamento;


            var terminaisBelezinha =
                Domain.Belezinha.EstabelecimentoTerminalPosService
                    .ObterTerminaisDoEstabelecimentoPorIdEstabelecimentoEIdTipoPos(
                        Domain.WebContext.IdEstabelecimentoAutenticado.Value, configPos.TipoPOS.Id);
            if (terminaisBelezinha.Count == 0)
                formasPagamento.RemoveAll(a => a.FormaPagamento.TipoPOS != null);

            return formasPagamento;
        }

        private List<EstabelecimentoFormaPagamento> RemoverPagamentoOnlineDasFormasDePagamento(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            // PagamentoOnline não deve aparecer; CreditoDePagamentoOnline deve ser adicionado manualmente caso exista um horário pago antecipadamente
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.PagamentoOnline || fp.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnline);
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.PagamentoOnlineHotsite || fp.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnlineHotsite);
            return formasPagamento;
        }

        private List<EstabelecimentoFormaPagamento> RemoverPagamentoOnlinePorLinkDasFormasDePagamento(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.PagamentoOnlinePorLink);
            return formasPagamento;
        }
        
        private List<EstabelecimentoFormaPagamento> RemoverDescontoProfissionalDasFormasDePagamento(List<EstabelecimentoFormaPagamento> formasPagamento)
        {
            formasPagamento.RemoveAll(fp => fp.FormaPagamento == FormaPagamentoEnum.DescontoDeProfissional);
            return formasPagamento;
        }

        private static bool VerificarPagamentoOnlinePorLink(Estabelecimento estabelecimento)
        {
            return Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.LinkDePagamento).EstaDisponivel;
        }

        public int ObterDiasParaReceberDaOperadora(EstabelecimentoFormaPagamento efp, Transacao transacao = null)
        {
            if (efp.FormaPagamento == FormaPagamentoEnum.CreditoDePagamentoOnline)
            {
                var taxas = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterTaxasConfiguradasParaEstabelecimento(efp.Estabelecimento.IdEstabelecimento);
                var diasIntegral = taxas.Parametros.DiasParaReceber ?? efp.DiasParaReceberDaOperadora;
                var horarioTransacao = transacao.HorariosTransacoes.FirstOrDefault(ht => ht.Horario.FoiPagoAntecipadamente);
                var dataPagamento = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ObterDataDoPagamento(horarioTransacao.Horario);
                if (dataPagamento == null)
                    throw new ArgumentException("Um horário sem data de pagamento está marcado comom pago antecipado. id: " + horarioTransacao.Horario.Id);

                return diasIntegral - (Calendario.Hoje() - dataPagamento.Value.Date).Days;
            }
            else
                return efp.DiasParaReceberDaOperadora;
        }

        public void AssociarFormasDePagamentoDoTipoPOS(Estabelecimento estabelecimento, TipoPOS tipoPOS)
        {
            var formasPagamentoPOS = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(p => p.TipoPOS.Id == tipoPOS.Id && p.Ativo);
            foreach (var formaPagamento in formasPagamentoPOS)
            {
                AssociarFormaDePagamento(formaPagamento, estabelecimento);
            }
        }

        public async Task<List<CardRate>> ObterCardsRateSiclos(string establishment_id)
        {
            return await Domain.ConciliacaoBelezinha.ConsultasNaApiDaSiclosService.ConsultarTaxasDeEstabelecimentoAsync(establishment_id);
        }

        public void AjustarTaxaDeOperadoraParaFormaDePagamento(EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPOS)
        {
            if (estabelecimentoConfiguracaoPOS.TipoPOS.Id == (int)SubadquirenteEnum.StoneSiclos)
            {

                var configuracao = new ParametrosTrinks<string>(ParametrosTrinksEnum.taxa_padrao_para_forma_pgmnto_siclos).ObterValor();
                var taxasSiclosPadrao = JsonConvert.DeserializeObject<TaxaPadraoSiclosDTO>(configuracao);

                var estabelecimentoFormasPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ListarFormasDePagamentoPorTipoPOS(estabelecimentoConfiguracaoPOS.Estabelecimento.IdEstabelecimento, (int)SubadquirenteEnum.StoneSiclos);
                var limiteParcelaSiclos = ObterQuantidadeParcelasParaConfigurarSiclos();

                foreach (var estabelecimentoFormaPagamento in estabelecimentoFormasPagamento)
                {

                    var formaPagamento = estabelecimentoFormaPagamento.FormaPagamento;
                    var idTipoFormaPagamento = formaPagamento.Tipo.Id;

                    estabelecimentoFormaPagamento.DiasParaReceberDaOperadora = ObterQuantidadeDiasParaReceberDaOperadoraSiclos();
                    estabelecimentoFormaPagamento.PercentualCobradoPelaOperadora = ObterTaxaPadraoFormaPagamentoSiclos(taxasSiclosPadrao, idTipoFormaPagamento: idTipoFormaPagamento);

                    if (idTipoFormaPagamento == (int)FormaPagamentoTipoEnum.Credito)
                    {
                        estabelecimentoFormaPagamento.AceitaParcelamento = true;

                        if (estabelecimentoFormaPagamento.Parcelamento.Count == 0)
                        {

                            for (int parcelaAtual = 1; parcelaAtual <= limiteParcelaSiclos; parcelaAtual++)
                            {
                                var taxaOperadora = ObterTaxaPadraoFormaPagamentoSiclos(taxasSiclosPadrao, idTipoFormaPagamento, parcelaAtual);
                                var parcela = GerarParcelas(estabelecimentoFormaPagamento, parcelaAtual, taxaOperadora);
                                estabelecimentoFormaPagamento.Parcelamento.Add(parcela);
                            }
                        }
                        else
                        {
                            foreach (var parcelaExistente in estabelecimentoFormaPagamento.Parcelamento)
                            {
                                var taxaOperadora = ObterTaxaPadraoFormaPagamentoSiclos(taxasSiclosPadrao, idTipoFormaPagamento, parcelaExistente.NumeroParcela);

                                if (taxaOperadora == 0m)
                                    break;

                                parcelaExistente.DescontoOperadora = taxaOperadora;
                                parcelaExistente.Ativo = true;
                            }
                        }
                    }
                }
            }
        }
        public void AtualizarTaxasDePagamentoDoEstabelecimento(
           int idTipoPosAntigo,
           EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPOS,
           int idTipoPosNovo)
        {
            if (idTipoPosNovo == (int)SubadquirenteEnum.StoneSiclos && idTipoPosAntigo == 0)
            {
                AjustarTaxaDeOperadoraParaFormaDePagamento(estabelecimentoConfiguracaoPOS);
                return;
            }
            if (idTipoPosAntigo == idTipoPosNovo || idTipoPosAntigo == 0) return;

            var estabelecimento = estabelecimentoConfiguracaoPOS.Estabelecimento;
            var formasPagamentoAntigas = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable()
             .Where(fp => fp.Estabelecimento == estabelecimento && fp.FormaPagamento.TipoPOS.Id == idTipoPosAntigo)
             .ToList();

            var formasPagamentoNovas = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable()
                .Where(fp => fp.Estabelecimento == estabelecimento && fp.FormaPagamento.TipoPOS.Id == idTipoPosNovo)
                .ToList();

            if (formasPagamentoAntigas.Count == 0 || formasPagamentoNovas.Count == 0) return;

            var formasPagamentoNovasPorNome = CriarDicionarioFormasPagamentoPorNome(formasPagamentoNovas);

            foreach (var formaPagamentoAntiga in formasPagamentoAntigas)
            {
                if (formasPagamentoNovasPorNome.TryGetValue(formaPagamentoAntiga.FormaPagamento.Nome, out var formaPagamentoNova))
                {
                    AtualizarFormaPagamento(formaPagamentoAntiga, formaPagamentoNova);
                    CriarOuAtualizarParcelas(formaPagamentoAntiga, formaPagamentoNova);
                }
            }
            AtualizarFormasDePagamento(formasPagamentoNovas);
        }

        private void AtualizarFormasDePagamento(IList<EstabelecimentoFormaPagamento> formasPagamento)
        {
            var repository = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository;
            foreach (var formaPagamento in formasPagamento)
            {
                repository.UpdateNoFlush(formaPagamento);
            }
            repository.Flush();
        }

        public bool FormaDePagamentoEstahAtivaParaEstabelecimento(int idEstabelecimento, FormaPagamentoEnum formaPagamento)
        {
            var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable()
                .FirstOrDefault(efp =>
                    efp.Estabelecimento.IdEstabelecimento == idEstabelecimento && efp.Ativo &&
                    efp.FormaPagamento == formaPagamento);

            return estabelecimentoFormaPagamento != null;
        }

        public EstabelecimentoFormaPagamento ObterFormaDePagamentoDescontoProfissionalCriarSeNecessario(Estabelecimento estabelecimento)
        {
            EstabelecimentoFormaPagamento estabelecimentoformaPagamentoDescontoProfissional =
                Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterEstabelecimentoFormaDePagamentoAtivos(
                    estabelecimento.IdEstabelecimento, IdFormaPagamentoDescontoProfissional);

            if (estabelecimentoformaPagamentoDescontoProfissional != null)
                return estabelecimentoformaPagamentoDescontoProfissional;

            FormaPagamento formaPagamentoDescontoProfissional = Domain.Financeiro.FormaPagamentoRepository.Load(IdFormaPagamentoDescontoProfissional);
                
            estabelecimentoformaPagamentoDescontoProfissional = Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaPagamentoDescontoProfissional, estabelecimento);

            return estabelecimentoformaPagamentoDescontoProfissional;
        }

        private Dictionary<string, EstabelecimentoFormaPagamento> CriarDicionarioFormasPagamentoPorNome(
            IList<EstabelecimentoFormaPagamento> formasPagamento)
        {
            return formasPagamento.ToDictionary(f => f.FormaPagamento.Nome);
        }

        private void AtualizarFormaPagamento(
            EstabelecimentoFormaPagamento formaPagamentoAntiga,
            EstabelecimentoFormaPagamento formaPagamentoNova)
        {
            formaPagamentoNova.AtualizarComOsValoresDeOutraFormaDePagamento(formaPagamentoAntiga);
        }

        private void CriarOuAtualizarParcelas(EstabelecimentoFormaPagamento formaPagamentoAntiga, EstabelecimentoFormaPagamento formaPagamentoNova)
        {
            formaPagamentoNova.AceitaParcelamento = formaPagamentoAntiga.AceitaParcelamento;

            if (formaPagamentoAntiga.AceitaParcelamento)
            {
                formaPagamentoNova.AceitaParcelamento = true;

                foreach (var parcelaAntiga in formaPagamentoAntiga.Parcelamento)
                {
                    
                    var parcelaExistente = formaPagamentoNova.Parcelamento
                        .FirstOrDefault(p => p.NumeroParcela == parcelaAntiga.NumeroParcela);

                    if (parcelaExistente != null)
                    {
                        parcelaExistente.DescontoOperadora = parcelaAntiga.DescontoOperadora;
                        parcelaExistente.Ativo = parcelaAntiga.Ativo;
                    }
                    else
                    {
                        formaPagamentoNova.AdicionarParcelas(parcelaAntiga.DescontoOperadora, parcelaAntiga.NumeroParcela, parcelaAntiga.Ativo);
                    }
                }
            }
        }

        private EstabelecimentoFormaPagamentoParcela GerarParcelas(
            EstabelecimentoFormaPagamento estabelecimentoFormaPagamento,
            int numeroParcela,
            decimal? taxaOperadora = 0m)
        {
            var retorno = new EstabelecimentoFormaPagamentoParcela
            {
                Ativo = true,
                DescontoOperadora = taxaOperadora ?? 0m,
                EstabelecimentoFormaPagamento = estabelecimentoFormaPagamento,
                NumeroParcela = numeroParcela
            };

            return retorno;
        }

        private int ObterQuantidadeDiasParaReceberDaOperadoraSiclos()
        {
            return new ParametrosTrinks<int>(ParametrosTrinksEnum.quantidade_de_dias_para_recebimento_da_operadora_siclos).ObterValor();
        }

        private int ObterQuantidadeParcelasParaConfigurarSiclos()
        {
            return new ParametrosTrinks<int>(ParametrosTrinksEnum.quantidade_de_parcelas_para_configurar_preenchimento_automatico_siclos).ObterValor();
        }

        private decimal ObterTaxaPadraoFormaPagamentoSiclos(TaxaPadraoSiclosDTO taxaPadraoSiclosDTO, int idTipoFormaPagamento, int parcelas = 1)
        {

            if (idTipoFormaPagamento == (int)FormaPagamentoTipoEnum.Debito && parcelas == 1)
                return taxaPadraoSiclosDTO.debito;

            else if (idTipoFormaPagamento == (int)FormaPagamentoTipoEnum.Credito && parcelas == 1)
                return taxaPadraoSiclosDTO.credito;

            else if (idTipoFormaPagamento == (int)FormaPagamentoTipoEnum.Credito && parcelas <= 6)
                return taxaPadraoSiclosDTO.parcelado_2_a_6_vezes;

            else if (idTipoFormaPagamento == (int)FormaPagamentoTipoEnum.Credito && parcelas <= 12)
                return taxaPadraoSiclosDTO.parcelado_6_a_12_vezes;

            return 0m;
        }
    }
}