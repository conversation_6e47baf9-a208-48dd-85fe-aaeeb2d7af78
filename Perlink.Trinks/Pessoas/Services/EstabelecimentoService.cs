﻿using Elmah;
using Newtonsoft.Json;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Enums;
using Perlink.Shared.Notificacao;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.Cobranca.Helpers;
using Perlink.Trinks.DTO.PushSNS;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor.GeradoresDeNF;
using Perlink.Trinks.Pessoas.Configuracoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.VO;
using Perlink.Trinks.PromotoresDoTrinks;
using Perlink.Trinks.Resources;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using RES = Perlink.Trinks.Resources.Pessoas.Services.EstabelecimentoService;
using RESMensagens = Perlink.Trinks.Resources.Mensagens;

namespace Perlink.Trinks.Pessoas.Services
{

    public class EstabelecimentoService : BaseService, IEstabelecimentoService
    {

        #region Propriedades de Apoio

        private IEnvioEmailService EnvioEmailService
        {
            get { return Domain.Pessoas.EnvioEmailService; }
        }

        #endregion Propriedades de Apoio

        [TransactionInitRequired]
        public void AlteraEmailEstabelecimento(string email, Int32 idPessoa)
        {
            var pessoa = Domain.Pessoas.PessoaJuridicaRepository.Load(idPessoa);
            pessoa.Email = email;
            Domain.Pessoas.PessoaJuridicaRepository.Update(pessoa);
        }

        [TransactionInitRequired]
        public void AlterarDadosGeraisEstabelecimento(EstabelecimentoDadosGerais entity)
        {
            Domain.Pessoas.EstabelecimentoDadosGeraisRepository.Update(entity);
        }

        [TransactionInitRequired]
        public void AlterarHorariosDeFuncionamentoEstabelecimento(EstabelecimentoHorarioFuncionamento entity)
        {
            Domain.Pessoas.EstabelecimentoHorarioFuncionamentoRepository.Update(entity);
        }

        [TransactionInitRequired]
        public void AssociarCategriaAEstabelecimento(Estabelecimento estabelecimento, Int32 codigoServicoCategoria,
            Boolean associarServicos)
        {
            var categoriaEstabelecimento =
                Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.ObterPorServicoCategoriaDoEstabelecimento(
                    codigoServicoCategoria, estabelecimento.IdEstabelecimento);

            if (categoriaEstabelecimento != null)
            {
                categoriaEstabelecimento.Ativo = true;

                if (associarServicos)
                    foreach (var servico in categoriaEstabelecimento.Servicos)
                        servico.Ativo = true;

                Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Update(categoriaEstabelecimento);
            }
            else
            {
                var categoria = Domain.Pessoas.ServicoCategoriaRepository.Load(codigoServicoCategoria);
                categoriaEstabelecimento = new ServicoCategoriaEstabelecimento
                {
                    Nome = categoria.Nome,
                    Descricao = categoria.Descricao,
                    Estabelecimento = estabelecimento,
                    ServicoCategoria = categoria,
                    Ativo = true
                };

                Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.SaveNew(categoriaEstabelecimento);

                if (associarServicos)
                    foreach (var servico in categoria.ServicosPadroes.Ativos())
                        AssociarServicoAoEstabelecimento(estabelecimento, servico);
            }
        }

        [TransactionInitRequired]
        public object AssociarServicosAoEstabelecimento(Estabelecimento estabelecimento, List<Servico> listaServicos, bool ehAdministrador, bool ehRecepcionista)
        {
            var totalDeServicos = listaServicos.Count();
            var totalDeServicosAssociados = 0;
            var codServicoEstabelecimento = 0;
            var estabelecimentoEhHabilitadoProgramaFidelidade = estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaProgramaDeFidelidade;
            var exibeMensagemAdm = estabelecimentoEhHabilitadoProgramaFidelidade && ehAdministrador;
            var exibeMensagemRecepcionista = !exibeMensagemAdm && estabelecimentoEhHabilitadoProgramaFidelidade && ehRecepcionista;

            foreach (var servico in listaServicos)
            {
                var categoriaJaAssociada =
                    estabelecimento.ObterCategoriaEstabelecimentoAtivaPorCategoriaPadrao(servico.ServicoCategoria.Codigo) !=
                    null;

                if (!categoriaJaAssociada)
                    AssociarCategriaAEstabelecimento(estabelecimento, servico.ServicoCategoria.Codigo, false);

                var servicoJaAssociado =
                    Domain.Pessoas.ServicoEstabelecimentoRepository.VerificarSeServicoJaEstaAssociadoAoEstabelecimento(
                        estabelecimento.IdEstabelecimento, servico.IdServico);

                var servicoEstabelecimentoJaAssociado =
                            Domain.Pessoas.ServicoEstabelecimentoService.ObterServicoDoEstabelecimentoPeloNome(
                                estabelecimento.IdEstabelecimento, servico.Nome);

                if (servicoEstabelecimentoJaAssociado != null && servicoEstabelecimentoJaAssociado.Ativo)
                {
                    servicoJaAssociado = true;
                }

                if (!servicoJaAssociado)
                {
                    var servicoEstabelecimento = AssociarServicoAoEstabelecimento(estabelecimento, servico);
                    codServicoEstabelecimento = servicoEstabelecimento.IdServicoEstabelecimento;
                    totalDeServicosAssociados++;
                }
            }

            if (totalDeServicosAssociados > 0)
            {
                AtualizarVersaoDaGeracaoDeNFC(estabelecimento);

                if (totalDeServicos == 1)
                {
                    return new
                    {
                        AbrirAssociarServicosAProfissional = true,
                        IdServicoEstabelecimento = codServicoEstabelecimento,
                        ExibeMensagemFidelidadeParaNovoCadastro = true
                    };
                }
                return new
                {
                    AbrirAssociarServicosAProfissional = false,
                    ExibeMensagemAdm = exibeMensagemAdm,
                    ExibeMensagemRecepcionista = exibeMensagemRecepcionista
                };
            }
            ValidationHelper.Instance.AdicionarItemValidacao(RESMensagens.ServicoJaAssociado);

            return null;
        }

        public void AtualizarVersaoDaGeracaoDeNFC(Estabelecimento estabelecimento)
        {
            if (estabelecimento.EstabelecimentoPossuiNFC() && estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Autocom)
            {
                IncrementarVersaoProdutoOuServico(estabelecimento);

                var geradorDeNotaFiscal = GeradorDeDadosParaNotaFiscalFactory.ConstruirGeradorDeNF(estabelecimento);

                geradorDeNotaFiscal.GerarDadosDeAtualizacaoDeIntegracao();
            }
        }

        //[TransactionInitRequired]
        //public void CompletarTutorialBackOffice(Estabelecimento estabelecimento, Conta conta) {
        //    Domain.Despesas.LancamentoGrupoService.CriarLancamentosGrupoPadroesParaEstabelecimento(estabelecimento);
        //    Domain.Despesas.LancamentoCategoriaService.CriarDespesasParaOEstabelecimento(estabelecimento);
        //    var vinculoEstabelecimento =
        //        conta.Pessoa.PessoaFisica.VinculoEstabelecimentos.FiltrarPorEstabelecimento(
        //            estabelecimento.IdEstabelecimento);
        //    if (vinculoEstabelecimento != null) {
        //        vinculoEstabelecimento.TutorialBackOfficeConcluido = true;
        //        Domain.Pessoas.ContaRepository.Update(conta);
        //    }
        //}

        [TransactionInitRequired]
        public void CriarMotivosDeDescontoInicial(Estabelecimento estabelecimento)
        {
            var possuiMotivos =
                Domain.Financeiro.MotivoDescontoRepository.Queryable().Any(f => f.Estabelecimento == estabelecimento);
            if (possuiMotivos)
                return;

            var motivos = new List<MotivoDesconto>{
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    Descricao = Textos.MotivoNaoInformado
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    DescontoRefleteNaComissao = false,
                    Descricao = Textos.Cortesia
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    Descricao = Textos.Aniversario
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    Descricao = Textos.DiaDaSemanaPromocao
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    DescontoRefleteNaComissao = false,
                    Descricao = Textos.DescontoAdicionalEmPacote
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    DescontoRefleteNaComissao = false,
                    Descricao = Textos.CartaoFidelidade
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    Descricao = Textos.Convenio
                },
                new MotivoDesconto{
                    Estabelecimento = estabelecimento,
                    Descricao = Textos.PagamentoAVista
                }
            };

            foreach (var m in motivos)
            {
                Domain.Financeiro.MotivoDescontoRepository.SaveNew(m);
            }
            List<MotivoDeDescontoDoTrinksEnum> foiPossivelCriarMotivoDeDesconto;
            CriarOuDesativarMotivosDeDescontoDoTrinksSeNecessario(estabelecimento, out foiPossivelCriarMotivoDeDesconto);
        }

        [TransactionInitRequired]
        public void CriarOuDesativarMotivosDeDescontoDoTrinksSeNecessario(Estabelecimento estabelecimento, out List<MotivoDeDescontoDoTrinksEnum> motivosDoTrinksAtivados, MotivoDeDescontoDoTrinksEnum motivoDeDesconto = 0)
        {
            motivosDoTrinksAtivados = new List<MotivoDeDescontoDoTrinksEnum>();

            bool foiPossivelCriarMotivoDeDesconto;
            if (motivoDeDesconto == 0)
            {
                CriarOuDesativarMotivoDeDescontoDoTrinksSeNecessario(MotivoDeDescontoDoTrinksEnum.ProgramaDeFidelidade,
                    motivoEhNecessario: estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaProgramaDeFidelidade,
                    nomeDoMotivo: Textos.ProgramaFidelidade,
                    nomeDaFuncionalidade: Textos.ProgramaFidelidade,
                    estabelecimento: estabelecimento,
                    foiPossivelCriarMotivoDeDesconto: out foiPossivelCriarMotivoDeDesconto);
                if (foiPossivelCriarMotivoDeDesconto)
                    motivosDoTrinksAtivados.Add(MotivoDeDescontoDoTrinksEnum.ProgramaDeFidelidade);

                foiPossivelCriarMotivoDeDesconto = false;
                CriarOuDesativarMotivoDeDescontoDoTrinksSeNecessario(MotivoDeDescontoDoTrinksEnum.PagamentoOnline,
                    motivoEhNecessario: Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstabelecimentoPossuiPagamentoOnlineAtivado(estabelecimento.IdEstabelecimento),
                    nomeDoMotivo: Textos.PagamentoOnline,
                    nomeDaFuncionalidade: Textos.PagamentoOnline,
                    estabelecimento: estabelecimento,
                    foiPossivelCriarMotivoDeDesconto: out foiPossivelCriarMotivoDeDesconto);
                if (foiPossivelCriarMotivoDeDesconto)
                    motivosDoTrinksAtivados.Add(MotivoDeDescontoDoTrinksEnum.PagamentoOnline);
            }

            if (motivoDeDesconto == MotivoDeDescontoDoTrinksEnum.CupomComDesconto)
            {
                CriarOuDesativarMotivoDeDescontoDoTrinksSeNecessario(MotivoDeDescontoDoTrinksEnum.CupomComDesconto,
                    motivoEhNecessario: Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, ControleDeFuncionalidades.ObjetosDeValor.Recurso.Cupom).EstaDisponivel,
                    nomeDoMotivo: Textos.CupomMotivoDeDescontoDoTrinksComDesconto,
                    nomeDaFuncionalidade: Textos.CupomMotivoDeDescontoDoTrinksComDesconto,
                    estabelecimento: estabelecimento,
                    foiPossivelCriarMotivoDeDesconto: out foiPossivelCriarMotivoDeDesconto,
                    descontoRefleteNaComissao: true);
                if (foiPossivelCriarMotivoDeDesconto)
                    motivosDoTrinksAtivados.Add(MotivoDeDescontoDoTrinksEnum.CupomComDesconto);
            }

            if (motivoDeDesconto == MotivoDeDescontoDoTrinksEnum.CupomSemDesconto)
            {
                CriarOuDesativarMotivoDeDescontoDoTrinksSeNecessario(MotivoDeDescontoDoTrinksEnum.CupomSemDesconto,
                    motivoEhNecessario: Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, ControleDeFuncionalidades.ObjetosDeValor.Recurso.Cupom).EstaDisponivel,
                    nomeDoMotivo: Textos.CupomMotivoDeDescontoDoTrinksSemDesconto,
                    nomeDaFuncionalidade: Textos.CupomMotivoDeDescontoDoTrinksSemDesconto,
                    estabelecimento: estabelecimento,
                    foiPossivelCriarMotivoDeDesconto: out foiPossivelCriarMotivoDeDesconto,
                    descontoRefleteNaComissao: false);
                if (foiPossivelCriarMotivoDeDesconto)
                    motivosDoTrinksAtivados.Add(MotivoDeDescontoDoTrinksEnum.CupomSemDesconto);
            }

            if (motivoDeDesconto == MotivoDeDescontoDoTrinksEnum.ClubeDeAssinaturas)
            {
                CriarOuDesativarMotivoDeDescontoDoTrinksSeNecessario(MotivoDeDescontoDoTrinksEnum.ClubeDeAssinaturas,
                    motivoEhNecessario: Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.VerificarSeEstabelecimentoUsaFormaPagamento(estabelecimento.IdEstabelecimento, FormaPagamentoEnum.ClubeDeAssinatura),
                    nomeDoMotivo: Textos.ClubeDeAssinaturas,
                    nomeDaFuncionalidade: Textos.ClubeDeAssinaturas,
                    estabelecimento: estabelecimento,
                    foiPossivelCriarMotivoDeDesconto: out foiPossivelCriarMotivoDeDesconto);
                if (foiPossivelCriarMotivoDeDesconto)
                    motivosDoTrinksAtivados.Add(MotivoDeDescontoDoTrinksEnum.ClubeDeAssinaturas);
            }
        }

        public void DesabilitarMotivoDeDescontoDoTrinks(Estabelecimento estabelecimento, MotivoDeDescontoDoTrinksEnum motivoDeDesconto)
        {
            var motivoDesconto = Domain.Financeiro.MotivoDescontoRepository.Queryable().FirstOrDefault(f => f.Estabelecimento == estabelecimento && f.MotivoDeDescontoDoTrinks == motivoDeDesconto);

            if (motivoDesconto.Ativo)
            {
                motivoDesconto.Ativo = false;
                Domain.Financeiro.MotivoDescontoRepository.Update(motivoDesconto);
            }
        }

        public void CriarOuAtivarMotivoDeDescontoDoTrinks(Estabelecimento estabelecimento, MotivoDeDescontoDoTrinksEnum motivo, string nomeDoMotivo, string nomeDaFuncionalidade, out bool foiPossivelCriarMotivoDeDesconto, bool descontoRefleteNaComissao = false)
        {
            foiPossivelCriarMotivoDeDesconto = true;
            var motivoDesconto = Domain.Financeiro.MotivoDescontoRepository.Queryable().FirstOrDefault(f => f.Estabelecimento == estabelecimento && f.MotivoDeDescontoDoTrinks == motivo);
            var possuiMotivoComOMesmoNome = Domain.Financeiro.MotivoDescontoRepository.Queryable().Any(md => md.Estabelecimento == estabelecimento && md.Descricao == nomeDoMotivo);

            if (motivoDesconto == null)
            {
                if (possuiMotivoComOMesmoNome)
                {
                    foiPossivelCriarMotivoDeDesconto = false;
                    ValidationHelper.Instance.AdicionarItemAlerta("Não foi possível habilitar " + nomeDaFuncionalidade + " pois já existe um Motivo de Desconto com o nome '" + nomeDoMotivo + "'. Por favor, renomear o Motivo de Desconto existente.");
                }
                else
                {
                    motivoDesconto = new MotivoDesconto
                    {
                        Estabelecimento = estabelecimento,
                        Descricao = nomeDoMotivo,
                        DescontoRefleteNaComissao = descontoRefleteNaComissao,
                        MotivoDeDescontoDoTrinks = motivo
                    };

                    Domain.Financeiro.MotivoDescontoRepository.SaveNew(motivoDesconto);
                }
            }
            else
            {
                if (!motivoDesconto.Ativo)
                {
                    motivoDesconto.Ativo = true;
                    Domain.Financeiro.MotivoDescontoRepository.Update(motivoDesconto);
                }
            }
        }

        private void CriarOuDesativarMotivoDeDescontoDoTrinksSeNecessario(MotivoDeDescontoDoTrinksEnum motivo, bool motivoEhNecessario, string nomeDoMotivo, string nomeDaFuncionalidade, Estabelecimento estabelecimento, out bool foiPossivelCriarMotivoDeDesconto, bool descontoRefleteNaComissao = false)
        {
            var possuiMotivo = Domain.Financeiro.MotivoDescontoRepository.Queryable().Any(md => md.Estabelecimento == estabelecimento && md.MotivoDeDescontoDoTrinks == motivo);

            var ehParaCriar = !possuiMotivo && motivoEhNecessario;
            foiPossivelCriarMotivoDeDesconto = true;

            if (motivoEhNecessario)
            {
                CriarOuAtivarMotivoDeDescontoDoTrinks(estabelecimento, motivo, nomeDoMotivo, nomeDaFuncionalidade, out foiPossivelCriarMotivoDeDesconto, descontoRefleteNaComissao);
            }
            else if (possuiMotivo)
            {
                DesabilitarMotivoDeDescontoDoTrinks(estabelecimento, motivo);
            }
        }

        [TransactionInitRequired]
        public void DefinirEstabelecimentoComoFranqueado(Estabelecimento estabelecimento,
            FranquiaEstabelecimento franquiaEstabelecimento, bool compoeConsolidadoFranqueador = true, bool modificadoPelaVitrine = false)
        {
            franquiaEstabelecimento.IdEstabelecimento = estabelecimento.IdEstabelecimento;
            franquiaEstabelecimento.NomeUnidade = estabelecimento.PessoaJuridica.NomeFantasia;
            franquiaEstabelecimento.CompoeConsolidadoFranqueador = compoeConsolidadoFranqueador;
            estabelecimento.EstabelecimentoConfiguracaoGeral.PermiteEnvioDeLembreteSmsGratuito = franquiaEstabelecimento.Franquia.PermiteEnvioDeLembreteSmsGratuito;
            estabelecimento.FranquiaEstabelecimento = franquiaEstabelecimento;
            Domain.Pessoas.FranquiaEstabelecimentoRepository.SaveNew(franquiaEstabelecimento);
            Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);

            if (estabelecimento.EstabelecimentoEhBaseadoEmUmModelo())
            {
                var idEstabelecimentoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdDoModeloDoEstabelecimento(estabelecimento.IdEstabelecimento);
                var quantidadeDeProdutosEstaDentroDoLimiteParaSincronia = Domain.Pessoas.SincroniaEstabelecimentoModeloService.QuantidadeDeProdutosEstaDentroDoLimiteParaSincronia(idEstabelecimentoModelo);
                var quantidadeDeServicosEstaDentroDoLimiteParaSincronia = Domain.Pessoas.SincroniaEstabelecimentoModeloService.QuantidadeDeServicosEstaDentroDoLimiteParaSincronia(idEstabelecimentoModelo);
                var quantidadePacotesEstaDentroDoLimiteParaSincronia = Domain.Pessoas.SincroniaEstabelecimentoModeloService.QuantidadeDePacotesEstaDentroDoLimiteParaSincronia(idEstabelecimentoModelo);
                Domain.Pessoas.SincroniaEstabelecimentoModeloService.SincronizarComEstabelecimentoModelo(estabelecimento.IdEstabelecimento, OrigemSincroniaDeModelo.CadastroDeUnidade,
                    sincronizarOuAgendarSincroniaDeProdutos: quantidadeDeProdutosEstaDentroDoLimiteParaSincronia,
                    sincronizarOuAgendarSincroniaDeServicos: quantidadeDeServicosEstaDentroDoLimiteParaSincronia,
                    sincronizarOuAgendarSincroniaDePacotes: quantidadePacotesEstaDentroDoLimiteParaSincronia);
            }


            if (EstabelecimentoDeveSerIncluidoComCobrancaManual(estabelecimento))
                AtualizarContaFinanceiraComoCobrancaManual(estabelecimento);

            if (EstabelecimentoDeveSeguirPlanoDeAssinaturaPadrao(estabelecimento, modificadoPelaVitrine))
                AtualizarPlanoAssinaturaComPlanoPadraoFranquia(estabelecimento);

            if (estabelecimento.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.Modelo)
                Domain.Pessoas.EstabelecimentoService.SalvaConfiguracaoNFCEstabelecimento(estabelecimento, TipoDeInterfaceNFC.NotaFiscalEletronica);

            Domain.Cobranca.PromocaoPraContaFinanceiraService.DesativarPromocoesDoEstabeleciemnto(estabelecimento);
        }

        public void DefinirPesoBuscaPortal(Estabelecimento estabelecimento, DateTime data)
        {
            // 0 = Não quer aparecer no Portal
            // 10 = Permite busca de horário no hotsite e agendamento online
            // 20 = Não permite busca de horário no hotsite ou agendamento online
            // 30 = Status conta negativo e responsável logou recentemente
            // 40 = Status conta negativo e responsável NÃO logou recentemente

            if (estabelecimento == null)
                return;

            var hotsite = estabelecimento.Hotsite();
            if (hotsite == null)
            {
                estabelecimento.PesoBuscaPortal = 40;
                return;
            }

            if (!hotsite.DesejaAparecerBuscaPortal)
            {
                estabelecimento.PesoBuscaPortal = 0;
                return;
            }

            var statusAdimplentes = new[]{
                StatusContaFinanceira.Adimplente, StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.PeriodoGratis, StatusContaFinanceira.CobrancaManual, StatusContaFinanceira.InadimplenteForaTolerancia
            };

            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);

            if (contaFinanceira == null)
            {
                estabelecimento.PesoBuscaPortal = 40;
                return;
            }

            if (statusAdimplentes.Contains<StatusContaFinanceira>(contaFinanceira.Status))
                estabelecimento.PesoBuscaPortal = hotsite.PermiteAgendamentoHotsite && hotsite.PermiteBuscaHotsite ? 10 : 20;
            else if (contaFinanceira.Status == StatusContaFinanceira.ContaCancelada)//TRINKS-7807
                estabelecimento.PesoBuscaPortal = 40;
            else
            {
                var toleranciaUltimoLogin =
                    data.AddDays(
                        -(new ParametrosTrinks<Int32>(
                            ParametrosTrinksEnum.qtd_dias_padrao_ultimo_login_para_manter_hotsite_no_ar).ObterValor()));
                var logadoRecentemente = estabelecimento.VinculoUsuarios.Any(
                    g => g.Ativo &&
                        g.PessoaFisica.Contas.Any(
                            p => p.DataUltimoLogin.HasValue && p.DataUltimoLogin.Value.Date > toleranciaUltimoLogin.Date));

                estabelecimento.PesoBuscaPortal = logadoRecentemente ? 30 : 40;
            }
        }

        public bool EstabelecimentoEstahConfiguradoParaNFCDoTipoEletronica(Int32 idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            return estabelecimento.ConfiguracaoDeNFC != null
                && (estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.NotaFiscalEletronica
                    //|| estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Sat
                    || estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.SysPDV);
        }

        //public bool EstabelecimentoEstahConfiguradoParaNFCDoTipoSAT(Int32 idEstabelecimento) {
        //    var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
        //    return estabelecimento.ConfiguracaoDeNFC != null
        //        && (estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Sat);
        //}

        public bool EstabelecimentoExigeAliquotaICMS(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            return estabelecimento.ConfiguracaoDeNFC != null
                && estabelecimento.ConfiguracaoDeNFC.InterfaceNFC != NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.SysPDV;
        }

        public bool EstabelecimentoExigeSituacaoTributaria(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            return estabelecimento.ConfiguracaoDeNFC != null
                && estabelecimento.ConfiguracaoDeNFC.InterfaceNFC != NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Nenhuma;
        }

        public bool ExisteDadosGerais(Int32 codigoEstabelecimento)
        {
            return
                Domain.Pessoas.EstabelecimentoDadosGeraisRepository.ExisteDadosGeraisEstabelecimento(
                    codigoEstabelecimento);
        }

        public bool ExisteHotsiteEstabelecimento(string url)
        {
            return Domain.Pessoas.HotsiteEstabelecimentoRepository.ExistePorUrl(url);
        }

        public void ManterConfiguracaoDeNFC(Estabelecimento estabelecimento)
        {
            if (!String.IsNullOrEmpty(estabelecimento.ConfiguracaoDeNFC.IdentificadorCSC) && estabelecimento.ConfiguracaoDeNFC.IdentificadorCSC.Length != 6)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O identificador CSC deve possuir exatamente 6 digitos");
            }

            if (ValidationHelper.Instance.IsValid)
            {
                if (!String.IsNullOrEmpty(estabelecimento.ConfiguracaoDeNFC.CSC) && estabelecimento.ConfiguracaoDeNFC.CSC.Contains("-"))
                    converteCSCParaUTF8(estabelecimento.ConfiguracaoDeNFC.CSC);

                EquipararTipoDeTributacaoDeNFCComOptanteSimplesNacionalDeNFS(estabelecimento);
                Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
            }
        }

        private void converteCSCParaUTF8(string csc)
        {
            byte[] bytes = Encoding.Default.GetBytes(csc);
            csc = Encoding.UTF8.GetString(bytes);
        }

        [TransactionInitRequired]
        public void ManterEstabelecimento(Estabelecimento estabelecimento, Conta conta = null, bool ehOrigemPortalProgramaDeFidelidade = false, ParceriaTrinks parceria = null, bool ehPreenchimentoManual = false)
        {
            var ehCadastro = estabelecimento.IdEstabelecimento == 0;

            if (conta == null)
                conta = estabelecimento.ObterResponsavel().PrimeiraConta;

            if (!string.IsNullOrEmpty(estabelecimento.PessoaJuridica.CNPJ))
            {
                if (
                    Domain.Pessoas.EstabelecimentoRepository.ValidarSeExisteOutroCnpjAtivo(
                        estabelecimento.PessoaJuridica.CNPJ.RemoverFormatacaoCPFeCPNJ(), estabelecimento.PessoaJuridica.IdPessoaJuridica))
                {
                    ValidationHelper.Instance.AdicionarItemValidacao(RESMensagens.OCnpjIndicadoJaEstaCadastradoNoSistema);
                }
            }

            if (!ValidationHelper.Instance.IsValid)
                return;

            if (ehCadastro)
            {
                Domain.Pessoas.EstabelecimentoRepository.SaveNewNoFlush(estabelecimento);
                Domain.Pessoas.EstabelecimentoRepository.Flush();

                if (ehPreenchimentoManual)
                {
                    Domain.Pessoas.EnderecoPreenchidoManualmenteRepository.SaveNew(new EnderecoPreenchidoManualmente(estabelecimento));
                }

                Domain.MarketingInterno.DadosMarketingService.RegistrarNoCadastroDePessoa(estabelecimento.PessoaJuridica);

                if (estabelecimento.FranquiaEstabelecimento != null && !Domain.Pessoas.FranquiaEstabelecimentoRepository.Exists(estabelecimento.IdEstabelecimento))
                {
                    estabelecimento.FranquiaEstabelecimento.IdEstabelecimento = estabelecimento.IdEstabelecimento;
                    Domain.Pessoas.FranquiaEstabelecimentoRepository.SaveNew(estabelecimento.FranquiaEstabelecimento);
                }
            }
            else
                Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);

            // Cria Conta Financeira se não houver
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);
            Assinatura assinatura = null;
            if (contaFinanceira == null)
            {
                contaFinanceira = new ContaFinanceira(estabelecimento, conta);
                PlanoAssinatura plano = null;

                if (parceria != null && parceria.PlanoAssinatura != null)
                {
                    plano = parceria.PlanoAssinatura;
                }
                else
                {
                    Domain.WebContext.DadosFixos(ConfiguracoesTrinks.Geral.IdDoEstabelecimentoCadastradoKey,
                        estabelecimento.IdEstabelecimento, expirationMinutes: 2);

                    Domain.WebContext.DadosFixos(ConfiguracoesTrinks.Geral.IdContaDoResponsavelFinanceiroDoEstabelecimentoCadastradoKey,
                        conta.IdConta, expirationMinutes: 2);

                    plano = !ehOrigemPortalProgramaDeFidelidade
                        ? Domain.Cobranca.PlanoAssinaturaRepository.ObterPlanoDeAssinaturaPadrao()
                        : Domain.Cobranca.PlanoAssinaturaRepository.ObterPlanoDeAssinaturaPadraoComProgramaFidelidade();
                }

                assinatura = new Assinatura(contaFinanceira, plano);

                contaFinanceira.Assinaturas.Add(assinatura);
                Domain.Cobranca.ContaFinanceiraRepository.SaveNew(contaFinanceira);
                Domain.Cobranca.ContaFinanceiraService.RealizarPushMudancaStatus(contaFinanceira);
            }

            if (estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade == null)
            {
                EnvioEmailService.EnviarEmailCEPSemBairro(estabelecimento);
            }

            if (!conta.Confirmada)
            {
                var ativarContaSemValidacao = new ParametrosTrinks<bool>(ParametrosTrinksEnum.ativa_conta_sem_verificacao_identidade_no_fluxo_cadastro_estabelecimento).ObterValor();

                if (ativarContaSemValidacao)
                {
                    conta.ConfirmarConta();
                    Domain.Pessoas.ContaRepository.Update(conta);
                }
                //else
                //    EnvioEmailService.EnviarEmailAtivacaoCadastro(conta);
            }

            Domain.Pessoas.EstabelecimentoRepository.Flush();

            Domain.Cobranca.PromocaoPraContaFinanceiraService.ApontarQueEstabelecimentoVeioDeParceria(estabelecimento, parceria);

            if (ehCadastro)
            {
                CriarConfiguracoesBasicas(estabelecimento, conta, ehOrigemPortalProgramaDeFidelidade);
                Domain.Cobranca.PromocaoTrinksService.AssociarPromocoesAOferecerProEstabelecimento(estabelecimento, parceria);

                Domain.Pessoas.EstabelecimentoRepository.Flush();
                estabelecimento.Refresh();

                if (parceria != null && parceria.PromotorDoTrinks != null)
                {
                    //Solução para erro de carregamento de proxy. Não quisemos colocar o 'Lazy = FetchWhen.OnInvoke' para não onerar outros lugares que utilizem a parceria.
                    var promotorDotrinks = Domain.PromotoresDoTrinks.PromotorDoTrinksRepository.Load(parceria.PromotorDoTrinks.Id);
                    Domain.PromotoresDoTrinks.NotificacoesDoAcompanhamentoService.NotificarAoPromotorSobreIndicadoQueSeCadastrouNoTrinks(promotorDotrinks, estabelecimento.NomeDeExibicaoNoPortal, parceria.Cupom);

                    var usoCupomParceria = new HistoricoUsoCuponsParceria()
                    {
                        PromotorDoTrinks = promotorDotrinks,
                        EstabelecimentoCadastrado = estabelecimento,
                        DataCadastro = Calendario.Agora()
                    };

                    Domain.PromotoresDoTrinks.HistoricoUsoCuponsParceriaRepository.SaveNew(usoCupomParceria);
                }

                //if (conta.Confirmada)
                //    Domain.Pessoas.EnvioEmailService.EnviarEmailConfirmacaoCadastro(conta, estabelecimento);
            }
            if (estabelecimento.IsNovo && ConfiguracoesTrinks.Geral.HabilitaSalesForce)
                RegistraLeadSalesForce(estabelecimento, assinatura);

            if (estabelecimento.IsNovo)
            {
                RealizarPushDosDadosDoEstabelecimento(estabelecimento, assinatura, conta, true);
            }
        }

        [TransactionInitRequired]
        public void ManterServicoCategoriaEstabelecimento(ServicoCategoriaEstabelecimento entity)
        {
            //if (entity.Codigo > 0) {
            //    Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Clear();
            //    var antigo = Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Load(entity.Codigo);
            //    Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Clear();
            //    //###Comentado devido ao item TRINKS-9074
            //    //if (antigo != null && antigo.Nome != entity.Nome)
            //    //    EnvioEmailService.EnviarEmailAlteracaoNomeServicoOuCategoriaPadrao(antigo, entity);
            //    //###
            //}
            //else
            if (entity.Codigo == 0)
                EnvioEmailService.EnviarEmailNovoServicoOuCategoriaNaoPadrao(entity);

            Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.SaveOrUpdate(entity);

            if (entity.Servicos != null)
            {
                foreach (var servicoEstabelecimento in entity.Servicos)
                {
                    var idServico = 0;
                    if (String.IsNullOrWhiteSpace(servicoEstabelecimento.CodigoInterno))
                    {
                        if (servicoEstabelecimento.Servico != null)
                            idServico = servicoEstabelecimento.Servico.IdServico;

                        servicoEstabelecimento.CodigoInterno =
                            Domain.Pessoas.ServicoEstabelecimentoService.ObterProximoCodigoInterno(
                                entity.Estabelecimento.IdEstabelecimento, idServico);
                    }
                    Domain.Pessoas.ServicoEstabelecimentoRepository.UpdateNoFlush(servicoEstabelecimento);
                }
                Domain.Pessoas.ServicoEstabelecimentoRepository.Flush();
            }
        }

        public Boolean NomeFantasiaEhValido(String nomeFantasia, int idEstabelecimento)
        {
            return
                !Domain.Pessoas.EstabelecimentoRepository.ExistemEstabelecimentosAtivosComMesmoNomeFantasia(nomeFantasia, idEstabelecimento);
        }

        public Estabelecimento ObterEstabelecimentoPorHotsite(string url)
        {
            Estabelecimento estabelecimento = null;
            var hotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorUrl(url);

            if (hotsite != null)
                estabelecimento = hotsite.Estabelecimento;

            return estabelecimento;
        }

        public HotsiteEstabelecimento ObterHotsiteEstabelecimento(string url)
        {
            return Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorUrl(url);
        }

        public List<Estabelecimento> ObterTodosOsEstabelecimentosPorHotsite(string url)
        {
            var hotsites = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterTodosPorUrl(url);
            var listaDeEstabelecimento = new List<Estabelecimento>();

            if (hotsites.Any())
            {
                listaDeEstabelecimento.AddRange(hotsites.Select(item => item.Estabelecimento));
            }

            return listaDeEstabelecimento;
        }

        [TransactionInitRequired]
        public void RemoverServicoCategoriaEstabelecimento(Int32 idServicoCategoriaEstabelecimento)
        {
            var categoria =
                Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Load(idServicoCategoriaEstabelecimento);
            foreach (var servico in categoria.Servicos)
            {
                ValidarRemocaoServicoAssociadoAProfissional(servico);
                ValidarSePossuiClubeDeAssinaturasAtivo(servico.IdServicoEstabelecimento);
                if (!ValidationHelper.Instance.IsValid)
                    return;
            }

            if (ValidationHelper.Instance.IsValid)
            {
                categoria.Ativo = false;
                Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.Update(categoria);

                foreach (var servico in categoria.Servicos)
                {
                    servico.Ativo = false;
                    servico.EstabelecimentoProfissionalServicoLista.ExcluirTodos();
                    Domain.Pessoas.ServicoEstabelecimentoRepository.Update(servico);
                }
            }
        }

        public void SalvaConfiguracaoNFCEstabelecimento(Estabelecimento estabelecimento, TipoDeInterfaceNFC tipoDeNFC = TipoDeInterfaceNFC.NotaFiscalEletronica)
        {
            int idUf = estabelecimento.PessoaJuridica.EnderecoProprio.UF.IdUF;
            if (!idUf.Equals(0) && Domain.NotaFiscalDoConsumidor.ConfiguracaoNFCEstadoRepository.Queryable().Any(f => f.UF.IdUF == idUf && f.NFCEImplementada))
            {
                var estabelecimentoDeNFCConfiguracao = new ConfiguracaoDeNFCDoEstabelecimento
                {
                    InterfaceNFC = tipoDeNFC,
                    IdEstabelecimento = estabelecimento.IdEstabelecimento,
                    TipoRegimeTributario = Domain.NotaFiscalDoConsumidor.TipoRegimeTributarioNFCRepository.Load(new ParametrosTrinks<int>(ParametrosTrinksEnum.tipo_regime_tributario_padrao).ObterValor()),
                    TipoIntegracaoPagamento = estabelecimento.ConfiguracaoDeNFC != null ? estabelecimento.ConfiguracaoDeNFC.TipoIntegracaoPagamento : (int)TipoIntegracaoPagamentoEnum.PagamentoNAOIntegradoComOSistemaDeAutomacao
                };
                estabelecimentoDeNFCConfiguracao.IncluirServicosNaNota = estabelecimentoDeNFCConfiguracao.IncluirServicosNaNotaConsiderandoLocalizacao();

                var envDev = ConfigurationManager.AppSettings["EnvDev"];
                estabelecimentoDeNFCConfiguracao.NFCProducao = envDev == null || !bool.Parse(envDev);

                if (!Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Queryable().Any(f => f.IdEstabelecimento.Equals(estabelecimento.IdEstabelecimento)))
                    Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.SaveNew(estabelecimentoDeNFCConfiguracao);
            }
        }

        [TransactionInitRequired]
        public void SalvarDadosGeraisEstabelecimento(EstabelecimentoDadosGerais entity)
        {
            Domain.Pessoas.EstabelecimentoDadosGeraisRepository.SaveNew(entity);
        }

        [TransactionInitRequired]
        public void SalvarEstatisticaAcessoTelefone(EstatisticaExibicaoTelefone entity)
        {
            Domain.Pessoas.EstatisticaExibicaoTelefoneRepository.SaveNew(entity);
        }

        [TransactionInitRequired]
        public void SalvarHorarioDeFuncionamentoEstabelecimento(EstabelecimentoHorarioFuncionamento entity)
        {
            Domain.Pessoas.EstabelecimentoHorarioFuncionamentoRepository.SaveNew(entity);
        }

        public bool VerificarSeEstabelecimentoPodeConfirmarSolicitacaoAparicaoNoPortal(Estabelecimento estabelecimento)
        {
            var estabelecimentoPossuiServicoComPrecoVinculadoAhProfissionalAtivo =
                Domain.Pessoas.ServicoEstabelecimentoRepository
                    .EstabelecimentoPossuiPeloMenosUmServicosComPrecoVinculadoAhProfissionalAtivo(estabelecimento.IdEstabelecimento);

            var estabelecimentoPossuiLogo = Domain.Conteudo.ConteudoImagemLogoEstabelecimentoRepository.ObterPorEstabelecimento(estabelecimento.IdEstabelecimento) != null;

            var existePeloMenosUmaFotoAssociadaAoEstabelecimento =
                Domain.Pessoas.EstabelecimentoRepository.ExistemFotosAssociadasAoEstabelecimento(
                    estabelecimento.IdEstabelecimento);

            var enderecoDoEstabelecimentoPossuiCEP =
                Domain.Pessoas.EstabelecimentoRepository.EstabelecimentoPossuiCEP(estabelecimento.IdEstabelecimento);

            if (!estabelecimentoPossuiServicoComPrecoVinculadoAhProfissionalAtivo ||
                !existePeloMenosUmaFotoAssociadaAoEstabelecimento ||
                !estabelecimentoPossuiLogo ||
                !enderecoDoEstabelecimentoPossuiCEP)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Para aparecer no Trinks seu cadastro deve estar completo e bem bonito! \nÉ necessário que sua" +
                                                                 " lista de serviços esteja atualizada com preços definidos, que seus profissionais estejam " +
                                                                 "cadastrados e vinculados aos serviços que realizam, que tenha o CEP do seu estabelecimento informado, que já tenha logo e pelo menos uma foto do seu estabelecimento! ");
            }

            return estabelecimentoPossuiServicoComPrecoVinculadoAhProfissionalAtivo && existePeloMenosUmaFotoAssociadaAoEstabelecimento && estabelecimentoPossuiLogo && enderecoDoEstabelecimentoPossuiCEP;
        }

        private static void AtualizarContaFinanceiraComoCobrancaManual(Estabelecimento entity)
        {
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterContaAtiva(entity.PessoaJuridica);
            contaFinanceira.Status = StatusContaFinanceira.CobrancaManual;
            Domain.Pessoas.EstabelecimentoRepository.Update(entity);
        }

        private static void AtualizarPlanoAssinaturaComPlanoPadraoFranquia(Estabelecimento entity)
        {
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterContaAtiva(entity.PessoaJuridica);
            var assinatura = contaFinanceira.AssinaturaAtiva();
            assinatura.PlanoAssinatura = entity.FranquiaEstabelecimento.Franquia.PlanoAssinaturaPadrao;
            assinatura.DiasGratis = entity.FranquiaEstabelecimento.Franquia.PlanoAssinaturaPadrao.DiasGratisInicial;
            Domain.Cobranca.AssinaturaRepository.Update(assinatura);
            Domain.Cobranca.AssinaturaService.ConfigurarEstabelecimentoComItensDisponiveisNoPlanoDeAssinatura(entity, entity.FranquiaEstabelecimento.Franquia.PlanoAssinaturaPadrao, assinatura);
        }

        private static bool EstabelecimentoDeveSeguirPlanoDeAssinaturaPadrao(Estabelecimento estabelecimento, bool modificadoPelaVitrine)
        {
            var ehEstabelecimentoModelo = estabelecimento.EhUmEstabelecimentoModelo();
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.ObterContaAtiva(estabelecimento.PessoaJuridica);
            var estabelecimentoPossuiAssinaturaAtiva = contaFinanceira.AssinaturaAtiva() != null;
            var franquiaPossuiPlanoPadrao = estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Franquia.PlanoAssinaturaPadrao != null;

            return !ehEstabelecimentoModelo && estabelecimentoPossuiAssinaturaAtiva && franquiaPossuiPlanoPadrao && !modificadoPelaVitrine;
        }

        private static bool EstabelecimentoDeveSerIncluidoComCobrancaManual(Estabelecimento estabelecimento)
        {
            return estabelecimento.EhUmEstabelecimentoModelo() || estabelecimento.FranquiaEstabelecimento.Franquia.CadastrarComCobrancaManual;
        }

        private static Task GetUrl(string url)
        {
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            ServicePointManager.DefaultConnectionLimit = 9999;

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.UserAgent =
                "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.69 Safari/537.36";
            request.Accept = "text/html";
            return Task
                .Factory
                .FromAsync<WebResponse>(request.BeginGetResponse, request.EndGetResponse, url)
                .ContinueWith(t =>
                {
                    if (t.IsCompleted)
                    {
                        using (var stream = t.Result.GetResponseStream())
                        {
                            using (var reader = new StreamReader(stream))
                            {
                                //Console.WriteLine("-- Successfully downloaded {0} --", t.AsyncState);
                                //Console.WriteLine(reader.ReadToEnd());
                            }
                        }
                    }
                    else if (t.IsFaulted)
                    {
                        throw new Exception(t.Exception.ToString());
                        //Console.WriteLine("There was an error downloading {0} - {1}", t.AsyncState, t.Exception);
                    }
                });
        }

        public void RegistraLeadSalesForcePorParametros(string nomeEstabelecimento, string emailContato, string telefoneSemFormatacao, string nomeresponsavelEstabelecimento, string referrer)
        {
            var url = @"https://webto.salesforce.com/servlet/servlet.WebToLead";

            string NomeTesteLinkIntegracao = ConfiguracoesTrinks.Geral.TESTETRINKSFABRICA;
            NomeTesteLinkIntegracao = NomeTesteLinkIntegracao ?? "";

            var parametros = new List<string>{
                "encoding=UTF-8",
                "oid=00DE0000000c7DT"
            };

            if (!string.IsNullOrWhiteSpace(referrer))
            {
                if (referrer.ToLower().Contains("google"))
                    parametros.Add("lead_source=Google");
                else if (referrer.ToLower().Contains("facebook"))
                    parametros.Add("lead_source=Facebook");
            }

            var partesNome = nomeresponsavelEstabelecimento.Split(' ');
            parametros.Add("first_name=" + NomeTesteLinkIntegracao + HttpUtility.UrlEncode(partesNome.FirstOrDefault()));
            parametros.Add("last_name=" + NomeTesteLinkIntegracao + HttpUtility.UrlEncode(partesNome.LastOrDefault()));

            parametros.Add("email=" + HttpUtility.UrlEncode(emailContato));
            parametros.Add("company=" + NomeTesteLinkIntegracao + HttpUtility.UrlEncode(nomeEstabelecimento));

            var dddTelefoneResponsavel = telefoneSemFormatacao.Substring(0, 2);
            var numeroTelefoneResponsavel = telefoneSemFormatacao.Substring(2);

            parametros.Add("00NE0000005CtF6=" + dddTelefoneResponsavel);
            parametros.Add("00NE0000005CtFB=" + numeroTelefoneResponsavel);

            //parametros.Add("00N0L000006BDst=" + HttpUtility.UrlEncode(urlHotSite));

            var dados = url + "?" + string.Join("&", parametros);

            try
            {
                GetUrl(dados);
            }
            catch (Exception e)
            {
                Domain.WebContext.GravarErro(e);
            }
        }

        private static void RegistraLeadSalesForce(Estabelecimento estabelecimento, Assinatura assinatura)
        {
            estabelecimento.Refresh();
            var url = @"https://webto.salesforce.com/servlet/servlet.WebToLead";
            ////https://www.salesforce.com/servlet/servlet.WebToLead

            var responsavel = estabelecimento.ObterResponsavel();

            string NomeTesteLinkIntegracao = ConfiguracoesTrinks.Geral.TESTETRINKSFABRICA;
            NomeTesteLinkIntegracao = NomeTesteLinkIntegracao ?? "";

            var genero = Genero.ObterPorCodigo(responsavel.Genero);

            var parametros = new List<string>{
                "encoding=UTF-8",
                "oid=00DE0000000c7DT",
                "salutation=" + $"Prezad{genero.PronomeTratamento().ToLower()}"
            };

            if (!string.IsNullOrWhiteSpace(estabelecimento.Referrer))
            {
                if (estabelecimento.Referrer.ToLower().Contains("google"))
                    parametros.Add("lead_source=Google");
                else if (estabelecimento.Referrer.ToLower().Contains("facebook"))
                    parametros.Add("lead_source=Facebook");
            }

            var partesNome = responsavel.NomeCompleto.Split(' ');
            parametros.Add("first_name=" + NomeTesteLinkIntegracao + HttpUtility.UrlEncode(partesNome.FirstOrDefault()));
            parametros.Add("last_name=" + NomeTesteLinkIntegracao + HttpUtility.UrlEncode(partesNome.LastOrDefault()));

            parametros.Add("email=" + HttpUtility.UrlEncode(estabelecimento.EmailEstabelecimento()));
            parametros.Add("company=" + NomeTesteLinkIntegracao + HttpUtility.UrlEncode(estabelecimento.NomeDeExibicaoNoPortal));

            var telefones = estabelecimento.PessoaJuridica.Telefones;
            var telefonesCount = telefones.Count();
            var telefonesResponsavel = estabelecimento.PessoaJuridica.ResponsavelFinanceiro.Telefones;
            var telefonesResponsavelCount = telefonesResponsavel.Count;
            var urlHotSite = estabelecimento.Hotsite() != null ? estabelecimento.Hotsite().ObterUrlCompleta() : "";

            if (telefonesCount > 0)
            {
                var telefone = telefones[0];
                parametros.Add("00NE0000005CtEm=" + telefone.DDD);
                parametros.Add("00NE0000005CtEr=" + telefone.Numero);
            }
            if (telefonesCount > 1)
            {
                var telefone = telefones[1];
                parametros.Add("00NE0000005CtEw=" + telefone.DDD);
                parametros.Add("00NE0000005CtF1=" + telefone.Numero);
            }
            if (telefonesResponsavelCount > 0)
            {
                var telefone = telefonesResponsavel[0];
                parametros.Add("00NE0000005CtF6=" + telefone.DDD);
                parametros.Add("00NE0000005CtFB=" + telefone.Numero);
            }
            if (telefonesResponsavelCount > 1)
            {
                var telefone = telefonesResponsavel[1];
                parametros.Add("00NE0000005CtFG=" + telefone.DDD);
                parametros.Add("00NE0000005CtFL=" + telefone.Numero);
            }

            if (estabelecimento.PessoaJuridica.DataCadastro != null)
                parametros.Add("00NE0000005CtFV=" +
                               HttpUtility.UrlEncode(
                                   estabelecimento.PessoaJuridica.DataCadastro.Value.ToShortDateString()));

            parametros.Add("00NE0000005CtFa=" +
                           HttpUtility.UrlEncode(assinatura.DataFimPeriodoPagoOuGratuito().ToShortDateString()));

            var enderecoProprio = estabelecimento.PessoaJuridica.EnderecoProprio;
            parametros.Add("00NE0000005CtMM=" + HttpUtility.UrlEncode(enderecoProprio.Bairro));
            parametros.Add("00NE0000005CtMR=" + HttpUtility.UrlEncode(enderecoProprio.Cidade));
            parametros.Add("00NE0000005CtMb=" + HttpUtility.UrlEncode(enderecoProprio.UF.Sigla));
            parametros.Add("00NE0000005CtMg=" +
                           HttpUtility.UrlEncode(enderecoProprio.Logradouro + ", " + enderecoProprio.Numero + ", " +
                                                 enderecoProprio.Complemento));
            parametros.Add("00NE0000005CtMl=" + HttpUtility.UrlEncode(enderecoProprio.Cep));

            parametros.Add("00NE0000005CtHg=" + HttpUtility.UrlEncode(estabelecimento.TipoEstabelecimento.Nome));
            parametros.Add("00NE0000005CtNe=" + responsavel.PessoaFisica.Cpf);
            parametros.Add("00NE0000005CwEp=" + responsavel.PessoaFisica.Email);
            parametros.Add("00NE0000005CtNj=" + HttpUtility.UrlEncode(estabelecimento.PessoaJuridica.CNPJ));
            parametros.Add("00NE0000005CtNo=" + responsavel.PessoaFisica.Genero);
            parametros.Add("00NE0000005CtOD=" + estabelecimento.IdEstabelecimento);
            parametros.Add("00N0L000006BDst=" + HttpUtility.UrlEncode(urlHotSite));

            if (responsavel.PessoaFisica.DataNascimento != null)
                parametros.Add("00NE0000005CtNy=" +
                               HttpUtility.UrlEncode(responsavel.PessoaFisica.DataNascimento.Value.ToShortDateString()));
            var dados = url + "?" + string.Join("&", parametros);

            try
            {
                GetUrl(dados);
            }
            catch (Exception e)
            {
                Domain.WebContext.GravarErro(e);
            }
        }

        private static void VincularUsuarioAoEstabelecimento(Estabelecimento estabelecimento, Conta conta)
        {
            var usuario = new UsuarioEstabelecimento
            {
                EhResponsavel = true,
                Estabelecimento = estabelecimento,
                PessoaFisica = conta.Pessoa.PessoaFisica
            };
            Domain.Pessoas.UsuarioEstabelecimentoRepository.SaveNew(usuario);
            Domain.Permissoes.UsuarioPerfilService.DefinirPerfilUsuario(usuario, 1);
        }

        [TransactionInitRequired]
        private ServicoEstabelecimento AssociarServicoAoEstabelecimento(Estabelecimento estabelecimento, Servico servico)
        {
            var servicoEstabelecimento =
                Domain.Pessoas.ServicoEstabelecimentoRepository.ObterPorServicoPorEstabelecimentoEServicoPadrao(
                    estabelecimento.IdEstabelecimento, servico.IdServico);

            if (servicoEstabelecimento != null)
            {
                servicoEstabelecimento.Ativo = true;
                servicoEstabelecimento.TipoPreco = TipoPrecoEnum.Fixo;
                servicoEstabelecimento.CodigoInterno =
                    Domain.Pessoas.ServicoEstabelecimentoService.ObterProximoCodigoInterno(
                        estabelecimento.IdEstabelecimento, servico.IdServico);
                Domain.Pessoas.ServicoEstabelecimentoRepository.Update(servicoEstabelecimento);
            }
            else
            {
                var categoriaServico = servico.ServicoCategoria;
                var categoriaServicoEstabelecimento =
                    Domain.Pessoas.ServicoCategoriaEstabelecimentoRepository.ObterPorServicoCategoriaDoEstabelecimento(
                        categoriaServico.Codigo, estabelecimento.IdEstabelecimento);

                servicoEstabelecimento = new ServicoEstabelecimento
                {
                    Nome = servico.Nome,
                    Descricao = servico.Descricao,
                    Duracao = servico.Duracao,
                    Preco = servico.Preco,
                    Estabelecimento = estabelecimento,
                    Servico = servico,
                    ServicoCategoriaEstabelecimento = categoriaServicoEstabelecimento,
                    Ativo = true,
                    ExibePreco = true,
                    TipoPreco = TipoPrecoEnum.Fixo,
                    CodigoInterno =
                        Domain.Pessoas.ServicoEstabelecimentoService.ObterProximoCodigoInterno(
                            estabelecimento.IdEstabelecimento, servico.IdServico)
                };

                Domain.Pessoas.ServicoEstabelecimentoRepository.SaveNew(servicoEstabelecimento);
            }

            return servicoEstabelecimento;
        }

        private void CriarConfiguracoesBasicas(Estabelecimento estabelecimento, Conta conta, bool ehOrigemPortalProgramaDeFidelidade = false)
        {
            var possuiModelo = estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.EstabelecimentoModelo != null;
            var ehModelo = estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.Modelo;

            CriarHorarioFuncionamentoInicial(estabelecimento);
            estabelecimento.PessoaJuridica.ResponsavelFinanceiro = conta.Pessoa.PessoaFisica;

            if (!estabelecimento.VinculoUsuarios.Any())
            {
                VincularUsuarioAoEstabelecimento(estabelecimento, conta);
            }

            var estabelecimentoProfissionalResponsavel = Domain.Pessoas.EstabelecimentoProfissionalService.AssociarPFaoEstabelecimentoComoProfissional(conta.Pessoa.PessoaFisica, estabelecimento);

            ServicoEstabelecimento servicoEstabelecimentoAAssociar;

            if (estabelecimento.FranquiaEstabelecimento != null)
                Domain.Pessoas.EstabelecimentoService.DefinirEstabelecimentoComoFranqueado(estabelecimento,
                        estabelecimento.FranquiaEstabelecimento);

            if (possuiModelo)
            {
                servicoEstabelecimentoAAssociar = Domain.Pessoas.ServicoEstabelecimentoRepository.Queryable().FirstOrDefault(f => f.Estabelecimento == estabelecimento && f.Ativo);
            }
            else
            {
                var servicosEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoService.AssociarServicosEstabelecimentoPadraoAoEstabelecimento(estabelecimento);
                servicoEstabelecimentoAAssociar = servicosEstabelecimento.FirstOrDefault();
            }

            if (servicoEstabelecimentoAAssociar != null && !ehModelo)
            {
                Domain.Pessoas.ServicoEstabelecimentoService.AssociarProfissionalAServicoEstabelecimento(estabelecimentoProfissionalResponsavel, servicoEstabelecimentoAAssociar);
            }

            AssociarFormasDePagamentoPadrao(estabelecimento);

            CriarMotivosDeDescontoInicial(estabelecimento);

            Domain.Marketing.MarketingService.CriarConfiguracaoMarketing(estabelecimento.IdEstabelecimento);
            Domain.Marketing.MarketingService.CreditarBrindesDeCadastroParaOEstabelecimento(estabelecimento.IdEstabelecimento);
            if (!ExisteDadosGerais(estabelecimento.IdEstabelecimento))
                SalvarInformacoesPadraoParaDadosGeraisEstabelecimentoAindaVazio(estabelecimento);

            CriarConfiguracaoHotsite(estabelecimento);

            Domain.Despesas.LancamentoGrupoService.CriarLancamentosGrupoPadroesParaEstabelecimento(estabelecimento);
            Domain.Despesas.LancamentoCategoriaService.CriarDespesasParaOEstabelecimento(estabelecimento);
            Domain.Pessoas.DadosDeRelacaoProfissionalService.CriarRelacoesEFuncoesDeProfissionais(estabelecimento);

            if (ehOrigemPortalProgramaDeFidelidade)
                estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaProgramaDeFidelidade = true;
            ManterSaldoLimiteSMSLembreteParaEstabelecimentoNovo(estabelecimento);
        }

        private void AssociarFormasDePagamentoPadrao(Estabelecimento estabelecimento)
        {
            var idsFormaDePagamentoPadrao = new Pessoas.ParametrosTrinks<List<int>>(Pessoas.Enums.ParametrosTrinksEnum.ids_formas_pagamento_padrao).ObterValor();
            var formasDePagamento = Domain.Financeiro.FormaPagamentoRepository.Queryable().Where(f => idsFormaDePagamentoPadrao.Contains(f.Id));
            foreach (var formaDePagamento in formasDePagamento)
            {
                Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamento, estabelecimento);
            }
        }

        private void CriarConfiguracaoHotsite(Estabelecimento estabelecimento)
        {
            var hotsite = new HotsiteEstabelecimento(estabelecimento);
            Domain.Pessoas.HotsiteEstabelecimentoRepository.SaveNew(hotsite);
            estabelecimento.Refresh();
            estabelecimento.HotsiteEstabelecimentoLista.Add(hotsite);
        }

        private void SalvarInformacoesPadraoParaDadosGeraisEstabelecimentoAindaVazio(Estabelecimento estabelecimento)
        {
            var dadosGerais = Domain.Pessoas.EstabelecimentoDadosGeraisRepository.Factory.CreateNewComValoresIniciais(estabelecimento);

            SalvarDadosGeraisEstabelecimento(dadosGerais);
        }

        private void CriarHorarioFuncionamentoInicial(Estabelecimento estabelecimento)
        {
            var diasSemana = Domain.Pessoas.DiaSemanaRepository.LoadAll();

            var horarioFuncionamento = diasSemana.Select(
                    dia =>
                        new EstabelecimentoHorarioFuncionamento
                        {
                            DiaSemana = dia,
                            Ativo = true,
                            Aberto = dia.IdDiaSemana != 1,
                            HoraAbertura = new TimeSpan(9, 0, 0),
                            HoraFechamento = new TimeSpan(20, 0, 0),
                            Estabelecimento = estabelecimento
                        });

            foreach (var h in horarioFuncionamento)
            {
                Domain.Pessoas.EstabelecimentoHorarioFuncionamentoRepository.SaveNewNoFlush(h);
            }

            Domain.Pessoas.EstabelecimentoHorarioFuncionamentoRepository.Flush();
            estabelecimento.Refresh();
        }

        #region Métodos Privados

        private void ValidarRemocaoServicoAssociadoAProfissional(ServicoEstabelecimento servicoEstabelecimento)
        {
            var profissionaisComServicosAtivos = servicoEstabelecimento.Estabelecimento.ProfissionaisComServicosAtivos();

            foreach (var profissional in profissionaisComServicosAtivos)
            {
                var estProf =
                    profissional.EstabelecimentoProfissionalLista.ToEstabelecimentoProfissional(
                        servicoEstabelecimento.Estabelecimento, profissional);
                var servicosAtivosAssociados = estProf.EstabelecimentoProfissionalServicoLista.Ativos();
                var ehServicoAssociado =
                    servicosAtivosAssociados.ToServicosEstabelecimento()
                        .FirstOrDefault(
                            p => p.IdServicoEstabelecimento == servicoEstabelecimento.IdServicoEstabelecimento) != null;

                if (!ehServicoAssociado)
                    continue;
                ValidationHelper.Instance.AdicionarItemValidacao(RES.MensagemValidacaoExclusaoCategoria);
                break;
            }
        }

        private void ValidarSePossuiClubeDeAssinaturasAtivo(Int32 idServicoEstabelecimento)
        {
            var existeAssinatura = Domain.ClubeDeAssinaturas.BeneficioDaAssinaturaRepository.PossuiBeneficioComOServico(idServicoEstabelecimento);
            var existePlano = Domain.ClubeDeAssinaturas.BeneficioDoPlanoRepository.PossuiBeneficioComOServico(idServicoEstabelecimento);

            if (existeAssinatura || existePlano)
                ValidationHelper.Instance.AdicionarItemValidacao("Algum dos serviços da categoria está associado a um clube de assinaturas.\n Para excluí-la, o clube de assinaturas deve ser cancelado na tela de Clube de assinaturas > Venda e renovação de assinaturas.");
        }

        #endregion Métodos Privados

        //TODO: Esta lógica está fixa para Nota Fiscal Eletrônica.
        //Caso venha a ter outras interfaces de nota fiscal que necessite equiparar o optante simples nacional, generalizar o código.
        private void EquipararTipoDeTributacaoDeNFCComOptanteSimplesNacionalDeNFS(Estabelecimento estabelecimento)
        {
            //TODO: rever código

            //if (estabelecimento.Est/abelecimentoConfiguracaoNFe != null
            //    && estabelecimento.ConfiguracaoDeNFC != null
            //    && estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.NotaFiscalEletronica) {
            //    if (estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.Codigo == "1")
            //        estabelecimento.Est/abelecimentoConfiguracaoNFe.OptanteSimplesNacionalCidade = Domain.RPS.OptanteSimplesNacionalCidadeRepository.ObterPelaDescricao("Sim", estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.Codigo);
            //    else
            //        estabelecimento.Est/abelecimentoConfiguracaoNFe.OptanteSimplesNacionalCidade = Domain.RPS.OptanteSimplesNacionalCidadeRepository.ObterPelaDescricao("Não", estabelecimento.PessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.Codigo);

            //    Domain.Pessoas.Est/abelecimentoConfiguracaoNFeService.SaveOrUpdate(estabelecimento.EstabelecimentoConfiguracaoNFe);
            //}
        }

        private void IncrementarVersaoProdutoOuServico(Estabelecimento estabelecimento)
        {
            if (estabelecimento.ConfiguracaoDeNFC != null)
            {
                estabelecimento.ConfiguracaoDeNFC.VersaoProdutoServico++;
                Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
            }
        }

        public bool EstabelecimentoTrabalhaComProdutoFracionado(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            bool estabelecimentoTrabalhaComProdutoFracionado = estabelecimento != null && estabelecimento.EstabelecimentoConfiguracaoGeral.ProdutoFracionadoEstaAtivo;
            return estabelecimentoTrabalhaComProdutoFracionado;
        }

        public bool ValidarEGravarQuePediuParaVerHotsiteEmOrientacoes(Estabelecimento estabelecimento, out string mensagemValidacao)
        {
            mensagemValidacao = "";

            if (!estabelecimento.HotsiteFoiAcessadoPelasOrientacoesIniciais && EstabelecimentoPodeAcessarHotsitePorOrientacoesIniciais(estabelecimento, out mensagemValidacao))
            {
                estabelecimento.HotsiteFoiAcessadoPelasOrientacoesIniciais = true;
                Domain.Pessoas.EstabelecimentoService.ManterEstabelecimento(estabelecimento);
            }

            return true;
        }

        private bool EstabelecimentoPodeAcessarHotsitePorOrientacoesIniciais(Estabelecimento estabelecimento, out string mensagemValidacao)
        {
            bool estabelecimentoPossuiPeloMenosDoisServicoComPreco = Domain.Pessoas.ServicoEstabelecimentoRepository
                    .EstabelecimentoPossuiPeloMenosDoisServicosComPreco(estabelecimento.IdEstabelecimento);

            bool estabelecimentoPossuiPeloMenosUmaFotoAssociada = Domain.Pessoas.EstabelecimentoRepository.ExistemFotosAssociadasAoEstabelecimento(
                    estabelecimento.IdEstabelecimento);

            mensagemValidacao = "";

            if (!estabelecimentoPossuiPeloMenosDoisServicoComPreco)
                mensagemValidacao += " Estabelecimento deve possuir pelo menos dois serviços com preço.";
            if (!estabelecimentoPossuiPeloMenosUmaFotoAssociada)
                mensagemValidacao += " Estabelecimento deve possuir pelo menos uma foto.";

            return estabelecimentoPossuiPeloMenosDoisServicoComPreco && estabelecimentoPossuiPeloMenosUmaFotoAssociada;
        }

        public string ObterUrlParaDownloadAppTrinks(bool ehAndroid, bool ehIOS, int idEstabelecimento = 0)
        {
            Estabelecimento estabelecimento = null;
            string urlDownloadApp = ConfiguracoesTrinks.EnvioEmail.UrlBase;

            if (idEstabelecimento > 0)
                estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            if (ehAndroid)
            {
                urlDownloadApp = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_download_app_android).ObterValor();
                if (estabelecimento != null && estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Ativo && estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppAndroid != null)
                    urlDownloadApp = estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppAndroid;
            }
            else if (ehIOS)
            {
                urlDownloadApp = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_download_app_ios).ObterValor();
                if (estabelecimento != null && estabelecimento.FranquiaEstabelecimento != null && estabelecimento.FranquiaEstabelecimento.Ativo && estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppIOS != null)
                    urlDownloadApp = estabelecimento.FranquiaEstabelecimento.Franquia.UrlDownloadAppIOS;
            }

            return urlDownloadApp;
        }

        public bool EstabelecimentoPodeCadastrarNovosProfissionaisNaFaixaAtual(int qtdProfissifionaisComServiçosAtivos, Assinatura assinatura)
        {
            if (!assinatura.PlanoAssinatura.PossuiFidelidade())
            {
                return true;
            }

            int quantoTem = qtdProfissifionaisComServiçosAtivos;
            int? quantoPodeTer = Domain.Cobranca.FaturaTrinksRepository.ObterMaiorLimiteDeFaixaNoPeriodo(assinatura.IdAssinatura, Calendario.Hoje());

            return quantoPodeTer == null || quantoTem <= quantoPodeTer;
        }

        public string ObterHotSiteUrlUnico(string urlHotSite)
        {
            if (!Domain.Pessoas.HotsiteEstabelecimentoRepository.ExistePorUrl(urlHotSite))
            {
                return urlHotSite;
            }

            var cont = 1;

            while (Domain.Pessoas.HotsiteEstabelecimentoRepository.ExistePorUrl(String.Format("{0}{1}", urlHotSite, cont)))
            {
                cont++;
            }

            return String.Format("{0}{1}", urlHotSite, cont);
        }

        public void OcultarHotsiteDoPortal(int idEstabelecimento, int idPessoaQueEstaOcultando)
        {
            var hotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento);

            hotsite.PessoaQueDesejaOcultarDoPortal = Domain.Pessoas.PessoaFisicaRepository.Load(idPessoaQueEstaOcultando);
            hotsite.DataDoPedidoDeOcultacaoDoPortal = Calendario.Agora();
            hotsite.DesejaOcultarDoPortal = true;
            hotsite.DesejaAparecerBuscaPortal = false;

            Domain.Pessoas.HotsiteEstabelecimentoRepository.Update(hotsite);
        }

        public bool EstabelecimentoExigeAliquotaICMSParaProduto(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            if (estabelecimento.ConfiguracaoDeNFC == null)
                return false;

            bool ehSimplesNacional = estabelecimento.ConfiguracaoDeNFC.TipoRegimeTributario.Codigo == "1";
            bool ehAutocom = estabelecimento.ConfiguracaoDeNFC.InterfaceNFC == TipoDeInterfaceNFC.Autocom;

            if (ehSimplesNacional && !ehAutocom)
                return false;

            return estabelecimento.ConfiguracaoDeNFC.InterfaceNFC != TipoDeInterfaceNFC.SysPDV;
        }

        public DadosDoEstabelecimentoParaOClienteDTO ObterDadosDoEstabelecimentoParaOClienteDTO(int idEstabelecimento)
        {
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
            var hotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento);
            DadosDoEstabelecimentoParaOClienteDTO estabelecimentoBasicoDTO = new DadosDoEstabelecimentoParaOClienteDTO(estabelecimento);
            estabelecimentoBasicoDTO.UrlHotSite = hotsite.ObterUrlCompleta() + "/m";

            estabelecimentoBasicoDTO.AppAndroid = ObterDadosDeAppAndroidParaDownload(estabelecimento);
            estabelecimentoBasicoDTO.AppIos = ObterDadosDeAppIosParaDownload(estabelecimento);

            return estabelecimentoBasicoDTO;
        }

        public string ObterIdAppTrinksAndroid(int? idEstabelecimento)
        {
            string idApp = "";

            idApp = new ParametrosTrinks<string>(ParametrosTrinksEnum.id_app_padrao_android).ObterValor();
            if (idEstabelecimento > 0)
            {
                string idAppFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdAppAndroidDaFranquiaDoEstabelecimento(idEstabelecimento);
                if (!string.IsNullOrWhiteSpace(idAppFranquia))
                    idApp = idAppFranquia;
            }

            return idApp;
        }

        public string ObterIdAppTrinksIos(int? idEstabelecimento)
        {
            string idApp = "";

            idApp = new ParametrosTrinks<string>(ParametrosTrinksEnum.id_app_padrao_ios).ObterValor();
            if (idEstabelecimento > 0)
            {
                string idAppFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterIdAppIosDaFranquiaDoEstabelecimento(idEstabelecimento);
                if (!string.IsNullOrWhiteSpace(idAppFranquia))
                    idApp = idAppFranquia;
            }

            return idApp;
        }

        public DownloadDeAppDTO ObterDadosDeAppIosParaDownload(Estabelecimento estabelecimento)
        {
            string urlApp = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_download_app_ios).ObterValor();
            bool temAppDeFranquia = false;

            if (estabelecimento.EhUmEstabelecimentoFranqueadoAtivo())
            {
                string urlAppFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterUrlAppIosDaFranquiaDoEstabelecimento(estabelecimento.IdEstabelecimento);
                if (!string.IsNullOrWhiteSpace(urlAppFranquia))
                {
                    urlApp = urlAppFranquia;
                    temAppDeFranquia = true;
                }
            }

            return new DownloadDeAppDTO(urlApp, appExclusivo: temAppDeFranquia);
        }

        public DownloadDeAppDTO ObterDadosDeAppAndroidParaDownload(Estabelecimento estabelecimento)
        {
            string urlApp = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_download_app_android).ObterValor();
            bool temAppDeFranquia = false;

            if (estabelecimento.EhUmEstabelecimentoFranqueadoAtivo())
            {
                string urlAppFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterUrlAppAndroidDaFranquiaDoEstabelecimento(estabelecimento.IdEstabelecimento);
                if (!string.IsNullOrWhiteSpace(urlAppFranquia))
                {
                    urlApp = urlAppFranquia;
                    temAppDeFranquia = true;
                }
            }

            return new DownloadDeAppDTO(urlApp, appExclusivo: temAppDeFranquia);
        }

        public string ObterUrlDoHotsiteDoEstabelecimento(int? idEstabelecimento, int? idFranquia = null, bool urlCompleta = true)
        {
            string UrlBase = ConfiguracoesTrinksWrapper.Instancia.UrlBase();

            if (idEstabelecimento.HasValue && idEstabelecimento > 0)
            {
                var queryHotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.Queryable()
                    .Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento.Value);

                if (idFranquia.HasValue)
                {
                    queryHotsite = queryHotsite.Where(h => h.Estabelecimento.FranquiaEstabelecimento.Franquia.Id == idFranquia.Value);
                }

                string urlHotsite = queryHotsite
                    .Select(h => h.Url)
                    .FirstOrDefault();

                if (!urlCompleta)
                    return urlHotsite;

                if (!string.IsNullOrWhiteSpace(urlHotsite))
                {
                    UrlBase += "/" + urlHotsite;
                }
            }

            return UrlBase;
        }

        public bool PessoaEhResponsavelDeAlgumEstabelecimento(Pessoa pessoa)
        {
            return Domain.Pessoas.EstabelecimentoRepository.Queryable().Any(e => e.PessoaJuridica.ResponsavelFinanceiro.IdPessoa == pessoa.IdPessoa);
        }

        public ResumoDeDadosDoEstabelecimentoDTO ObterResumoDeDadosDoEstabelecimento(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var pessoaJuridicaDoEstabelecimento = Domain.Pessoas.PessoaJuridicaRepository.Load(estabelecimento.PessoaJuridica.IdPessoaJuridica);
            var responsavelFinanceiro = pessoaJuridicaDoEstabelecimento.ResponsavelFinanceiro;
            var pessoa = Domain.Pessoas.PessoaRepository.Load(pessoaJuridicaDoEstabelecimento.IdPessoa);
            var quantidadeDeProfissionaisAtivos = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarAtivosPorEstabelecimento(idEstabelecimento).ToList().Count;
            var programaDeFidelidadeEstaConfigurado = Domain.Fidelidade.ProgramaDeFidelidadeRepository.ObterUltimaConfiguracoesDoEstabelecimento(idEstabelecimento) != null &&
                                                        Domain.Fidelidade.ProgramaDeFidelidadeRepository.EstabelecimentoPossuiProgramaDeFidelidadeConfigurado(idEstabelecimento);
            var estaNoPortal = Domain.Pessoas.EstabelecimentoRepository.ObterVisiveisNaBusca().Any(e => e.IdEstabelecimento == idEstabelecimento);

            var dataUltimoFechamentoDeContaRealizado = Domain.Financeiro.TransacaoRepository.ObterDataUltimoFechamentoDeContaRealizado(pessoaJuridicaDoEstabelecimento.IdPessoa);
            var dataUltimoAgendamentoRealizado = Domain.Pessoas.HorarioRepository.ObterDataCriacaoDoUltimoHorarioBalcaoAgendado(estabelecimento.IdEstabelecimento);
            var dataUltimoAgendamentoOnlineRealizado = Domain.Pessoas.HorarioRepository.ObterDataCriacaoDoUltimoHorarioOnlineAgendado(estabelecimento.IdEstabelecimento);

            var dataPontoGanho = Domain.Fidelidade.MovimentacaoDePontosRepository.Queryable().Where(p => p.IdPessoaDoEstabelecimento == pessoa.IdPessoa && !p.FoiEstornado && p.TipoDeOperacaoDeMovimentacao == Fidelidade.Enums.OperacaoDaMovimentacaoDePontosEnum.GanhoDePontos && p.TipoOrigem != Fidelidade.Enums.TipoOrigemMovimentacaoDePontosEnum.PontoAvulso).Max(p => (DateTime?)p.HoraMovimentacao);
            var dataPontoResgatados = Domain.Fidelidade.MovimentacaoDePontosRepository.Queryable().Where(p => p.IdPessoaDoEstabelecimento == pessoa.IdPessoa && !p.FoiEstornado && p.TipoDeOperacaoDeMovimentacao == Fidelidade.Enums.OperacaoDaMovimentacaoDePontosEnum.UsoDePontos).Max(p => (DateTime?)p.HoraMovimentacao);

            var contaFinanceiraAtiva = Domain.Cobranca.ContaFinanceiraRepository.ObterAtivaPorEstabelecimento(estabelecimento);

            var IdAssinaturaEstabelecimento = 0;
            var estabelecimentoConfiguracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);
            var dataUltimaFechamentoContaBelezinha = Domain.Financeiro.TransacaoPOSRepository.DataUltimaFechamentoContaBelezinha(estabelecimento.IdEstabelecimento);
            var assinaturaCorrente = contaFinanceiraAtiva.AssinaturaAtiva();
            if (contaFinanceiraAtiva != null)
            {
                var assinaturaAtiva = contaFinanceiraAtiva.AssinaturaAtiva();
                if (assinaturaAtiva != null)
                    IdAssinaturaEstabelecimento = assinaturaAtiva.IdAssinatura;
                else
                {
                    assinaturaCorrente = contaFinanceiraAtiva.Assinaturas.OrderByDescending(f => f.DataInicio).FirstOrDefault();
                    IdAssinaturaEstabelecimento = assinaturaCorrente.IdAssinatura;
                }
            }

            var relatorioFaturamentoDatas = Domain.Cobranca.RelatorioAssinaturaRepository.Queryable().FirstOrDefault(f => f.CodigoAssinatura == IdAssinaturaEstabelecimento)
        ?? Domain.Cobranca.RelatorioAssinaturaRepository.Queryable().OrderByDescending(f => f.CodigoAssinatura).First(f => f.PessoaJuridicaId == estabelecimento.PessoaJuridica.IdPessoaJuridica);

            var hotSiteEstabelecimento = Domain.Pessoas.HotsiteEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento);

            var solicitacaoAparecerNaBusca =
                    Domain.Pessoas.EstabelecimentoSolicitacaoAparecerBuscaRepository.ObterUltimaSolicitacaoDoEstabelecimento(estabelecimento.IdEstabelecimento);

            var codigosParceria = Domain.Cobranca.ParceriaTrinksService.ObterCuponsDoEstabelecimentoTratandoInativos(idEstabelecimento);
            var responsavel = Domain.Cobranca.ResponsavelAtendimentoService.ObterResponsavelAtendimentoPeloEstabelecimento(idEstabelecimento);
            var responsavelAtendimento = responsavel == " " ? "N/A" : responsavel;

            var resumoDeDadosDoEstabelecimento = new ResumoDeDadosDoEstabelecimentoDTO(estabelecimento: estabelecimento, responsavelFinanceiro: responsavelFinanceiro, quantidadeDeProfissionaisAtivos: quantidadeDeProfissionaisAtivos
                , programaDeFidelidadeEstaConfigurado: programaDeFidelidadeEstaConfigurado, hostSiteEstabelecimento: hotSiteEstabelecimento, estaNoPortal: estaNoPortal, dataUltimoAgendamentoOnlineRealizado: dataUltimoAgendamentoOnlineRealizado, dataUltimoFechamentoDeContaRealizado: dataUltimoFechamentoDeContaRealizado
            , dataUltimoAgendamentoRealizado: dataUltimoAgendamentoRealizado, ultimosPontosDados: dataPontoGanho, ultimosPontosResgatados: dataPontoResgatados, solicitacaoAparecerNaBusca: solicitacaoAparecerNaBusca, relatorioDatas: relatorioFaturamentoDatas
            , codigosParceria: codigosParceria, responsavelAtendimento: responsavelAtendimento, estabelecimentoConfiguracaoPOS: estabelecimentoConfiguracaoPOS, dataUltimaFechamentoContaBelezinha: dataUltimaFechamentoContaBelezinha);

            resumoDeDadosDoEstabelecimento.FaixaDeProfissionaisAssinada = Domain.Cobranca.AssinaturaService.ObterFaixaDeProfissionaisAssinada(IdAssinaturaEstabelecimento);
            resumoDeDadosDoEstabelecimento.ExisteAlgumaFaturaPaga = Domain.Cobranca.FaturaRepository.ExisteAlgumaFaturaPagaComValorMaiorQueZero(IdAssinaturaEstabelecimento);

            var ultimaFaturaaPagar = Domain.Cobranca.FaturaTrinksRepository.ObterTodasPelaAssinatura(assinaturaCorrente).Where(f => FormaPagamentoHelper.TemProcessoAssincrono(f.FormaPagamento) && f.FaturaPodeSerPaga).OrderByDescending(p => p.DataEmissao).FirstOrDefault();
            if (ultimaFaturaaPagar != null)
            {
                resumoDeDadosDoEstabelecimento.DataUltimaFatura = ultimaFaturaaPagar?.DataVencimento.ToBrazilianShortDateString();
                resumoDeDadosDoEstabelecimento.UrlUltimaFatura = "../BackOffice/Boleto/RedirecionarParaBoleto?pdf=true&idFatura=" + ultimaFaturaaPagar?.IdFatura + "&idPessoaJuridica=" + estabelecimento.PessoaJuridica.IdPessoa;
            }

            var dadosMarketing = Domain.MarketingInterno.DadosMarketingRepository.Queryable().FirstOrDefault(m => m.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa);

            if (dadosMarketing != null)
            {
                resumoDeDadosDoEstabelecimento.OrigemDoLead = dadosMarketing.UtmSource ?? "N/A";
                resumoDeDadosDoEstabelecimento.MidiaDePublicidade = dadosMarketing.UtmMedium ?? "N/A";
                resumoDeDadosDoEstabelecimento.CampanhaDeMarketing = dadosMarketing.UtmCampaign ?? "N/A";
                resumoDeDadosDoEstabelecimento.PalavrasChaveDaCampanha = dadosMarketing.UtmTerm ?? "N/A"; // [KANBANFDB-35]
                resumoDeDadosDoEstabelecimento.ResumoDaCampanhaDeMarketing = dadosMarketing.UtmContent ?? "N/A";
                resumoDeDadosDoEstabelecimento.Dispositivo = dadosMarketing.Dispositivo ?? "N/A";
            }
            else
            {
                resumoDeDadosDoEstabelecimento.OrigemDoLead = "N/A";
                resumoDeDadosDoEstabelecimento.MidiaDePublicidade = "N/A";
                resumoDeDadosDoEstabelecimento.CampanhaDeMarketing = "N/A";
                resumoDeDadosDoEstabelecimento.PalavrasChaveDaCampanha = "N/A";
                resumoDeDadosDoEstabelecimento.ResumoDaCampanhaDeMarketing = "N/A";
                resumoDeDadosDoEstabelecimento.Dispositivo = "N/A";
            }

            var dadosRecebedor =
                Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                    .ObterGatewayAtivoHabilitadoEIdRecebedorNoGatewayDoEstabelecimento(idEstabelecimento);

            if (dadosRecebedor.gateway?.IsPagarMe() == true)
            {
                resumoDeDadosDoEstabelecimento.PagarmeAtiva = dadosRecebedor.gateway.Value.IsPagarMe();
                resumoDeDadosDoEstabelecimento.IdRecebedorPagarme = dadosRecebedor.idRecebedorNoGateway;
                resumoDeDadosDoEstabelecimento.VersaoPagarme = dadosRecebedor.gateway.Value.ObterDescricao();
            }

            return resumoDeDadosDoEstabelecimento;
        }

        public string ObterLinkSistemaExterno(int idEstabelecimento)
        {
            return "https://na39.salesforce.com/_ui/search/ui/UnifiedSearchResults?asPhrase=1&searchType=2&str=" + idEstabelecimento;
        }

        public bool ApareceNaBuscaDoPortal(int idEstabelecimento)
        {
            return Domain.Pessoas.HotsiteEstabelecimentoRepository.EstabelecimentoPossuiHotSiteVisivelNaBusca(idEstabelecimento);
        }

        public void AlterarCNPJDoEstabelecimento(int idEstabelecimento, string cnpj)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var temEmissaoRPS = estabelecimento.PessoaJuridica.ConfiguracaoNFe != null && estabelecimento.PessoaJuridica.ConfiguracaoNFe.UltimoLoteGerado > 0;
            var temEmissaoNfcNota = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Queryable().FirstOrDefault(n => n.Estabelecimento == estabelecimento);

            if(temEmissaoRPS || temEmissaoNfcNota != null) {
                ValidationHelper.Instance.AdicionarItemValidacao("Esse estabelecimento já emitiu notas. A alteração do CNPJ deve ser feita enviando uma solicitação para o time de Fábrica. ");
            }

            if (cnpj.ToUpper() == "N/A")
                cnpj = null;

            if (!string.IsNullOrEmpty(cnpj))
            {
                bool cnpjJaEstaSendoUtilizado = Domain.Pessoas.PessoaJuridicaRepository.ExisteOutraPessoaJuridicaUtilizandoCnpj(cnpj, estabelecimento.PessoaJuridica.IdPessoa);
                if (cnpjJaEstaSendoUtilizado)
                {
                    ValidationHelper.Instance.AdicionarItemValidacao("Este CNPJ já está sendo usado e não poderá ser utilizado.");
                }
            }

            if (ValidationHelper.Instance.IsValid)
            {
                estabelecimento.PessoaJuridica.CNPJ = cnpj.RemoverFormatacaoCPFeCPNJ();

                Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.NotificarEventosOcorridosNoEsbelecimentoParaIntegracaoComOutrosSistemas(estabelecimento);

                //Domain.Pessoas.PessoaJuridicaRepository.Update(estabelecimento.PessoaJuridica);
                Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
            }
        }

        public void AlterarRazaoSocialDoEstabelecimento(int idEstabelecimento, string razaoSocial)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            if (razaoSocial.ToUpper() == "N/A")
                razaoSocial = null;

            if (ValidationHelper.Instance.IsValid)
            {
                estabelecimento.PessoaJuridica.RazaoSocial = razaoSocial;

                Domain.IntegracaoComOutrosSistemas.IntegracaoComTrinks.NotificacaoDeEventoParaIntegracaoApartirDoTrinksService.NotificarEventosOcorridosNoEsbelecimentoParaIntegracaoComOutrosSistemas(estabelecimento);

                //Domain.Pessoas.PessoaJuridicaRepository.Update(estabelecimento.PessoaJuridica);
                Domain.Pessoas.EstabelecimentoRepository.Update(estabelecimento);
            }
        }

        public void ValidarFinalizarCadastroDeEstabelecimentoNovo(string nomeFantasia, ref Conta conta, bool jaPossuiConta)
        {
            if (conta == null)
                throw new Exception("Para Finalizar Cadastro De Estabelecimento Novo é necessário que a conta não esteja nula");

            if (!NomeFantasiaEhValido(nomeFantasia.Trim(), 0))
            {
                ValidationHelper.Instance.AdicionarItemValidacao(
                    RESMensagens.JaExisteEstabelecimentoComNomeFantasiaInformado);
            }

            if (!jaPossuiConta && conta.Pessoa.TelefoneCelular(apenasComOperadoraIdentificada: false) == null)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Para concluir o cadastrado é necessário que você informe um celular válido para receber o SMS de confirmação de conta.");
            }
        }

        public void ManterSaldoLimiteSMSLembreteParaEstabelecimentoNovo(Estabelecimento estabelecimento)
        {
            Domain.Pessoas.ControleDeSaldoSMSLembreteService.ManterSaldoLimiteSMSLembreteParaEstabelecimentoNovo(estabelecimento);
        }

        private static void RealizarPushDosDadosDoEstabelecimento(Estabelecimento estabelecimento, Assinatura assinatura, Conta conta, bool ehCadastro)
        {
            try
            {
                var dados = new DadosPushDoEstabelecimentoDTO();

                if (!string.IsNullOrWhiteSpace(estabelecimento.Referrer))
                {
                    if (estabelecimento.Referrer.ToLower().Contains("google"))
                        dados.Origem = "Google";
                    else if (estabelecimento.Referrer.ToLower().Contains("facebook"))
                        dados.Origem = "Facebook";
                }

                var responsavel = estabelecimento.ObterResponsavel();
                var partesNome = responsavel.NomeCompleto.Split(' ');
                dados.PrimeiroNome = partesNome.FirstOrDefault();
                dados.UltimoNome = partesNome.LastOrDefault();

                dados.EmailEstabelecimento = estabelecimento.EmailEstabelecimento();
                dados.Estabelecimento = estabelecimento.NomeDeExibicaoNoPortal;

                if (estabelecimento.PessoaJuridica.DataCadastro != null)
                    dados.DataCadastro = estabelecimento.PessoaJuridica.DataCadastro.Value.ToShortDateString();

                dados.DataFimPeriodoPagoOuGratuito = assinatura.DataFimPeriodoPagoOuGratuito().ToShortDateString();

                var enderecoProprio = estabelecimento.PessoaJuridica.EnderecoProprio;
                dados.Bairro = enderecoProprio.Bairro;
                dados.Cidade = enderecoProprio.Cidade;
                dados.UF = enderecoProprio.UF?.Sigla;
                dados.Logradouro = enderecoProprio.Logradouro;
                dados.Numero = enderecoProprio.Numero;
                dados.Complemento = enderecoProprio.Complemento;
                dados.CEP = enderecoProprio.Cep;

                dados.TipoEstabelecimento = estabelecimento.TipoEstabelecimento.Nome;
                dados.CPF = responsavel.PessoaFisica.Cpf;
                dados.EmailResponsavel = responsavel.PessoaFisica.Email;
                dados.CNPJ = estabelecimento.PessoaJuridica.CNPJ;
                dados.Sexo = responsavel.PessoaFisica.Genero;
                dados.IdEstabelecimento = estabelecimento.IdEstabelecimento.ToString();
                dados.urlHotSite = estabelecimento.Hotsite() != null ? estabelecimento.Hotsite().ObterUrlCompleta() : "";

                if (responsavel.PessoaFisica.DataNascimento != null)
                    dados.DataNascimento = responsavel.PessoaFisica.DataNascimento.Value.ToShortDateString();

                dados.QtProfissionaisEsperada = assinatura.QuantidadeDeProfissionaisEsperada;

                var telefonesEstabelecimento = estabelecimento.PessoaJuridica.Telefones;
                var telefonesResponsavel = estabelecimento.PessoaJuridica.ResponsavelFinanceiro.Telefones;

                dados.TelefonesEstabelecimento = telefonesEstabelecimento
                    .Where(t => t.Ativo)
                    .Select(t => new TelefonePushDoEstabelecimentoDTO(t.DDD, t.Numero))
                    .ToList();

                dados.TelefonesResponsavel = telefonesResponsavel
                    .Where(t => t.Ativo)
                    .Select(t => new TelefonePushDoEstabelecimentoDTO(t.DDD, t.Numero))
                    .ToList();

                dados.ContaConfirmado = conta.Confirmada ? "Sim" : "Não";


                dados.FaixaProfissionais = EnumActions.GetEnumText(estabelecimento.FaixaProfissionais);
                dados.TipoFranquia = estabelecimento.TipoFranquia.Nome;

                dados.RDStationTrackingId = Domain.WebContext.DadosFixos("rdtrk") as string;
                dados.RDStationTrafficSource = Domain.WebContext.DadosFixos("__trf.src") as string;

                var parceria = Domain.Cobranca.EstabelecimentoParceriasTrinksRepository.ObterParceriaAtiva(estabelecimento.IdEstabelecimento);

                dados.CodigoParceria = parceria?.Cupom;
                dados.TipoParceria = parceria?.Tipo?.Nome;

                var dadosMarketing = Domain.MarketingInterno.DadosMarketingRepository.ObterPorIdPessoa(estabelecimento.PessoaJuridica.IdPessoa);

                if (dadosMarketing != null)
                {
                    dados.OrigemDoLead = dadosMarketing.UtmSource;
                    dados.CampanhaDeMarketing = dadosMarketing.UtmCampaign;
                    dados.Dispositivo = dadosMarketing.Dispositivo;
                    dados.PalavrasChaveDaCampanha = dadosMarketing.UtmTerm; // [KANBANFDB-35]
                    dados.UtmContent = dadosMarketing.UtmContent;
                    dados.UtmMedium = dadosMarketing.UtmMedium;
                }

                dados.UserTokenHubSpot = HttpContext.Current.Request.Cookies["hubspotutk"]?.Value;

                dados.MotivoDeCadastro = estabelecimento.MotivoCadastro;


                if (parceria != null && parceria.PromotorDoTrinks != null)
                {

                    var nomeCompletoDoPromotor = Domain.Pessoas.PessoaService.ObterPessoaFisicaPorEmail(parceria.PromotorDoTrinks.Email).NomeCompleto;
                    dados.IdPromotor = parceria.PromotorDoTrinks.IdPessoa;
                    dados.EmailPromotor = parceria.PromotorDoTrinks.Email;
                    dados.TipoDePromotor = parceria.PromotorDoTrinks.Tipo.ToString();
                    dados.NomeFantasiaPromotor = parceria.PromotorDoTrinks.Nome;
                    dados.TelefonePromotor = parceria.PromotorDoTrinks.Telefone;
                    dados.NomeDoPromotor = nomeCompletoDoPromotor;

                }






                var json = JsonConvert.SerializeObject(dados);

                var sns = ehCadastro ? ConfiguracoesTrinks.AWS.PushDadosEstabelecimentoCadastrado : ConfiguracoesTrinks.AWS.PushDadosEstabelecimentoAtualizado;

                ServicoDeNotificacaoSNS.PublicarNotificacaoSNS(sns, json);
            }
            catch (Exception e)
            {
                ErrorSignal.FromContext(HttpContext.Current).Raise(e, HttpContext.Current);
            }
        }

        public void CriarOuDesativarMotivoDeDescontoDePagamentoOnline(Estabelecimento estabelecimento, EstabelecimentoFormaPagamento estabelecimentoFormaPagamento)
        {
            bool foiPossivelCriarMotivoDeDesconto = true;
            if (estabelecimentoFormaPagamento.Ativo)
            {
                CriarOuAtivarMotivoDeDescontoDoTrinks(estabelecimento, MotivoDeDescontoDoTrinksEnum.PagamentoOnline, Textos.PagamentoOnline, Textos.PagamentoOnline, out foiPossivelCriarMotivoDeDesconto);
            }
            else
            {
                DesabilitarMotivoDeDescontoDoTrinks(estabelecimento, MotivoDeDescontoDoTrinksEnum.PagamentoOnline);
            }
        }

        public bool JaEnviouFotos(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            return Domain.Pessoas.FotoDePessoasService.PossuiFoto(estabelecimento.PessoaJuridica.IdPessoa);
        }

        public bool JaEnviouLogo(int idEstabelecimento)
        {
            return Domain.Conteudo.ConteudoImagemLogoEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento) != null;
        }
    }
}