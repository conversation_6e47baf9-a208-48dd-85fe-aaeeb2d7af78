﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas.DTO;
using System.Web;

namespace Perlink.Trinks.Pessoas.Services
{
    public interface IDadosCadastraisDoUsuarioService : IService
    {
        PerfilUsuarioDTO CarregarDadosDoPerfil(int idConta, string versaoApp = "");
        void AtualizarDadosDoPerfil(PerfilUsuarioParaAtualizacaoDTO dados, string versaoApp = "");
        void RedefinirSenhaAtual(int idConta, string senhaAtual, string novaSenha);
        FotoPessoa AlterarFotoDoPerfil(int idConta, HttpPostedFile arquivo);
        FotoPessoa ObterFotoAPartirDeProvedorExterno(int idConta, string provedorExterno);
    }
}
