﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.NotaFiscalDoConsumidor;
using Perlink.Trinks.RPS.Enums;
using Perlink.Trinks.RPS.GeradorDeArquivo;
using System;
using System.Linq;
using static Perlink.Trinks.PropertyNames.Pessoas;

namespace Perlink.Trinks.Pessoas.Services
{

    public class PessoaJuridicaConfiguracaoNFeService : BaseService, IPessoaJuridicaConfiguracaoNFeService
    {

        public void SaveOrUpdate(PessoaJuridicaConfiguracaoNFe entidade)
        {
            if (entidade.IdPessoaJuridica > 0)
                Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.Update(entidade);
            else
                Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.SaveNew(entidade);
        }

        public void Salvar(PessoaJuridica pessoaJuridica)
        {
            if (pessoaJuridica != null && pessoaJuridica.Ativo)
            {
                var configuracaoNF = pessoaJuridica.ConfiguracaoNFe;
                if (configuracaoNF.IdPessoaJuridica == 0)
                {
                    configuracaoNF.IdPessoaJuridica = pessoaJuridica.IdPessoa;
                    Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.SaveNew(configuracaoNF);
                }
                else
                {
                    Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.Update(configuracaoNF);
                }
            }
        }

        public void GerarConfirguracaoNFe(PessoaJuridica pessoaJuridica, bool persistirModificacoes = true)
        {
            if (pessoaJuridica.ConfiguracaoNFe == null)
                pessoaJuridica.ConfiguracaoNFe = new PessoaJuridicaConfiguracaoNFe();

            var enderecoProprio = pessoaJuridica.EnderecoProprio;
            if (enderecoProprio == null || enderecoProprio.BairroEntidade == null)
                return;

            var geradorArquivo = GeradorDeArquivoRPS.ParaCidade(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade.CodigoIBGE);
            if (geradorArquivo == null)
                return;

            var configPadrao =
                Domain.RPS.ConfiguracaoPadraoNFSRepository.ObterValoresDefaultPorCidade(
                    enderecoProprio.BairroEntidade.Cidade);

            if (configPadrao != null)
            {
                if (String.IsNullOrEmpty(pessoaJuridica.ConfiguracaoNFe.CodigoOptanteSimplesNacional))
                {
                    pessoaJuridica.ConfiguracaoNFe.CodigoOptanteSimplesNacional =
                                        configPadrao.OptanteSimplesNacional;
                }

                if (String.IsNullOrEmpty(pessoaJuridica.ConfiguracaoNFe.CodigoTipoRegimeEspecialTributacao))
                {
                    pessoaJuridica.ConfiguracaoNFe.CodigoTipoRegimeEspecialTributacao =
                        configPadrao.TipoRegimeEspecialTributacao;
                }

                if (String.IsNullOrEmpty(pessoaJuridica.ConfiguracaoNFe.CodigoTributacaoMunicipio))
                {
                    pessoaJuridica.ConfiguracaoNFe.CodigoTributacaoMunicipio =
                        configPadrao.TipoTributacaoServico;
                }

                if (pessoaJuridica.ConfiguracaoNFe.IntegracaoNFSe != IntegracaoNFSeEnum.Invoicy)
                    pessoaJuridica.ConfiguracaoNFe.IntegracaoNFSe = configPadrao.IntegracaoNFSe;

                //estabelecimento.EstabelecimentoConfiguracaoNFe.CodigoUtilizacaoIncentivoCultural =
                //    configPadrao.UtilizacaoIncentivoCultural;
            }

            //var tipoServicoPrestadoCidade =
            //    Domain.RPS.TipoServicoPrestadoCidadeRepository.ObterTipoServicoCidade(estabelecimento);

            //if (tipoServicoPrestadoCidade != null)
            //    estabelecimento.EstabelecimentoConfiguracaoNFe.CodigoTipoServicoPrestado =
            //        tipoServicoPrestadoCidade.TipoServicoPrestado.Codigo;

            pessoaJuridica.ConfiguracaoNFe.EnvioWebServiceHabilitado = geradorArquivo.EnviaPorWebService(pessoaJuridica.ConfiguracaoNFe);

            if (Domain.NotaFiscalDoConsumidor.IntegradorDeDadosDeNFService.CidadeEmiteServicoPorNFCe(pessoaJuridica))
            { // NFC-e Manaus
                //pessoaJuridica.ConfiguracaoNFe.EnvioWebServiceHabilitado = true;

                Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(pessoaJuridica.IdPessoa);
                if (estabelecimento != null)
                {
                    //Tem que criar a configuração de NFC para realizar o upload de certificado
                    var estabelecimentoDeNFCConfiguracao = new ConfiguracaoDeNFCDoEstabelecimento
                    {
                        InterfaceNFC = NotaFiscalDoConsumidor.Enums.TipoDeInterfaceNFC.Nenhuma,
                        IdEstabelecimento = estabelecimento.IdEstabelecimento,
                        TipoIntegracaoPagamento = estabelecimento.ConfiguracaoDeNFC != null ? estabelecimento.ConfiguracaoDeNFC.TipoIntegracaoPagamento : (int)TipoIntegracaoPagamentoEnum.PagamentoNAOIntegradoComOSistemaDeAutomacao
                    };
                    estabelecimentoDeNFCConfiguracao.IncluirServicosNaNota = estabelecimentoDeNFCConfiguracao.IncluirServicosNaNotaConsiderandoLocalizacao();

                    var existeConfiguracao = Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.Queryable().Any(f => f.IdEstabelecimento.Equals(estabelecimento.IdEstabelecimento));
                    if (!existeConfiguracao)
                        Domain.NotaFiscalDoConsumidor.ConfiguracaoDeNFCDoEstabelecimentoRepository.SaveNew(estabelecimentoDeNFCConfiguracao);
                }
            }

            if (persistirModificacoes)
                Salvar(pessoaJuridica);//Avaliar melhor momento para refatorar este método
        }

        public bool ProfissionalParceiroDeveCarregarOCertificado(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            bool profissionalDeveCarregarOCertificadoDigital = false;

            bool profissionalParceiroEstaHabilitado = estabelecimentoProfissional.Estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitarProfissionalParceiro;
            var tipoEmissaoNfeParceiro = estabelecimentoProfissional.Estabelecimento.EstabelecimentoConfiguracaoGeral.TipoDeEmissaoNfeComProfissionalParceiro;

            var habilitadoEmissaoParceiroUnificado = Domain.RPS.EmissaoRPSService.EstabelecimentoComToggleHabilitadoParaEmissaoDeParceiroUnificado(estabelecimentoProfissional.Estabelecimento);

            if (profissionalParceiroEstaHabilitado
                && estabelecimentoProfissional.PessoaJuridica != null
                && estabelecimentoProfissional.PessoaJuridica.Ativo
                && (tipoEmissaoNfeParceiro != Statics.TipoNfeProfissionalParceiro.NotaUnica.Id
                || (estabelecimentoProfissional.PessoaJuridica.EnderecoProprio != null && (estabelecimentoProfissional.PessoaJuridica.EnderecoProprio.Cidade == "Rio de Janeiro" || estabelecimentoProfissional.PessoaJuridica.EnderecoProprio.Cidade == "São Paulo" || estabelecimentoProfissional.PessoaJuridica.EnderecoProprio.Cidade == "Campinas" || habilitadoEmissaoParceiroUnificado))))
            {
                var bairro = estabelecimentoProfissional.PessoaJuridica.EnderecoProprio.BairroEntidade;
                var gerador = GeradorDeArquivoRPS.PorPessoaJuridica(estabelecimentoProfissional.PessoaJuridica);
                if (gerador != null)
                {
                    var camposExistentes = gerador.CamposExistentes();
                    var envioWebServiceEstaHabilitado = gerador.EnviaPorWebService(estabelecimentoProfissional.PessoaJuridica.ConfiguracaoNFe);

                    var certificadoDigital = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.ObterPorPessoaJuridica(estabelecimentoProfissional.PessoaJuridica);

                    profissionalDeveCarregarOCertificadoDigital = certificadoDigital == null && estabelecimentoProfissional.PessoaJuridica.ConfiguracaoNFe != null
                                                                    && (camposExistentes.CertificadoDigital || envioWebServiceEstaHabilitado);
                }
            }

            return profissionalDeveCarregarOCertificadoDigital;
        }

        public bool VerificarSeCidadeSolicitaCertificadoDigital(PessoaJuridica pessoaJuridica)
        {
            var gerador = GeradorDeArquivoRPS.PorPessoaJuridica(pessoaJuridica);
            var camposExistentes = gerador.CamposExistentes();
            return camposExistentes.CertificadoDigital;
        }

        public bool PossuiCertificadoDentroDaValidade(PessoaJuridica pessoaJuridica)
        {
            var certificadoDigitalPorPessoaJuridica = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.VerificarCertificadoDigitalPorPessoaJuridica(pessoaJuridica);

            if (certificadoDigitalPorPessoaJuridica)
            {
                var dataFimCertificado = Domain.NotaFiscalDoConsumidor.PessoaJuridicaCertificadoDigitalRepository.BuscardataFimcertificadoDigitalPorPessoaJuridica(pessoaJuridica);
                return DateTime.Now < dataFimCertificado;
            }
            else
            {
                return false;
            }
        }

        public bool ExisteProfissionalParceiroUtilizandoCnpj(int idEstabelecimento, string cnpj)
        {
            cnpj = cnpj.RemoverFormatacaoCPFeCPNJ();

            return Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .Any(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            ep.PessoaJuridica.CNPJ == cnpj);
        }

        public bool ExisteOutroProfissionalParceiroUtilizandoCnpj(int idEstabelecimento, string cnpj, int idPessoaJuridica)
        {
            cnpj = cnpj.RemoverFormatacaoCPFeCPNJ();

            return Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .Any(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            ep.PessoaJuridica.CNPJ == cnpj
                            && ((idPessoaJuridica > 0 && ep.PessoaJuridica.IdPessoa != idPessoaJuridica)
                            || (idPessoaJuridica == 0 && ep.PessoaJuridica.Ativo)));
        }

        public void DesassociarPessoaJuridicaDoProfissional(EstabelecimentoProfissional estabelecimentoProfisisional)
        {
            if (estabelecimentoProfisisional.PessoaJuridica == null)
                return;

            int profissionaisCompartilhandoMesmaPJ = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .Count(ep => ep.Estabelecimento == estabelecimentoProfisisional.Estabelecimento &&
                             ep.PessoaJuridica == ep.PessoaJuridica);

            if (profissionaisCompartilhandoMesmaPJ > 1)
                estabelecimentoProfisisional.PessoaJuridica = null;
            else
                estabelecimentoProfisisional.PessoaJuridica.Ativo = false;
        }

        public bool ExisteNotaEmitidaNaCidade(int idCidade)
        {

            // Por questões de perfomance, não é possível verificar status das emissões.
            // Logo, será validado por número do último Lote RPS

            var enderecoesQueryable = Domain.Pessoas.EnderecoRepository.Queryable();
            var configuracoesQueryable = Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.Queryable();

            var emissaoNaCidade = from c in configuracoesQueryable
                                  join e in enderecoesQueryable on c.IdPessoaJuridica equals e.Pessoa.IdPessoa
                                  where e.BairroEntidade.Cidade.Codigo == idCidade && c.UltimoRpsEmitido > 0
                                  select c.IdPessoaJuridica;

            return emissaoNaCidade.Any();
        }

        public void SalvarSenhaPrefeitura(PessoaJuridica pessoaJuridica, string senha)
        {
            if (pessoaJuridica != null && pessoaJuridica.Ativo)
            {
                var configuracaoNF = pessoaJuridica.ConfiguracaoNFe;
                if (configuracaoNF.IdPessoaJuridica != 0)
                {
                    configuracaoNF.SenhaUsuarioCadastrado = senha;
                    Domain.Pessoas.PessoaJuridicaConfiguracaoNFeRepository.Update(configuracaoNF);
                }
            }
        }
    }
}