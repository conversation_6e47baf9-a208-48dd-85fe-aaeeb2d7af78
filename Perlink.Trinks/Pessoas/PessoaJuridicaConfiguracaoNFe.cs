﻿using Castle.ActiveRecord;
using Perlink.Trinks.RPS.Enums;
using System;
using static Perlink.Trinks.PropertyNames.Pessoas;

namespace Perlink.Trinks.Pessoas
{

    [ActiveRecord("Pessoa_Juridica_Configuracao_NFSe", Lazy = true, DynamicUpdate = false, DynamicInsert = true)]
    [Serializable]
    public class PessoaJuridicaConfiguracaoNFe
    {

        public PessoaJuridicaConfiguracaoNFe()
        {
            IntegracaoNFSe = IntegracaoNFSeEnum.UniNFE;
            Producao = true;
            PossuiNfseDeducao = true;
        }

        [PrimaryKey(PrimaryKeyType.Assigned, "id_pessoa")]
        public virtual int IdPessoaJuridica { get; set; }

        [Property("Numero_Processo_Judicial")]
        public virtual string NumeroProcessoJudicial { get; set; }

        [Property("Cod_Optante_Simples_Nacional")]
        public virtual string CodigoOptanteSimplesNacional { get; set; }

        [Property("Cod_Tipo_Regime_Especial_Tributacao")]
        public virtual string CodigoTipoRegimeEspecialTributacao { get; set; }

        [Property("Cod_Tipo_Servico")]
        public virtual string ItemListaServico { get; set; }

        [Property("Cod_Tipo_Tributacao_Servico_Cidade")]
        public virtual string CodigoTributacaoMunicipio { get; set; }

        [Property("Cod_Cnae")]
        public virtual string CodigoCnae { get; set; }

        [Property("Habilitar_Enviar_Cnae_Null")]
        public virtual bool? HabilitaEnviarCnaeNull { get; set; }

        [Property("Numero_Ultimo_Lote")]
        public virtual int UltimoLoteGerado { get; set; }

        [Property("Numero_Ultimo_RPS")]
        public virtual int UltimoRpsEmitido { get; set; }

        [Property("Aliquota_Iss")]
        public virtual decimal AliquotaISS { get; set; }

        [Property("Aliquota_Iss_NaoFormatada")]
        public virtual decimal? Aliquota_Iss_NaoFormatada { get; set; }

        [Property("Cpf_Usuario_Cadastrado")]
        public virtual string CpfUsuarioCadastrado { get; set; }

        [Property("Senha_Usuario_Cadastrado")]
        public virtual string SenhaUsuarioCadastrado { get; set; }

        [Property("Aguardando_Envio")]
        public virtual bool AguardandoEnvio { get; set; }

        [Property("Data_Ultimo_Envio")]
        public virtual DateTime? DataUltimoEnvio { get; set; }

        [Property("Cmc_Prestador")]
        public virtual string CmcPrestador { get; set; }

        [Property("Envio_WebService_Habilitado")]
        public virtual bool EnvioWebServiceHabilitado { get; set; }

        [Property("id_externo")]
        public virtual string IdExterno { get; set; }

        [Property("id_externo_hmg")]
        public virtual string IdExternoHomologacao { get; set; }

        [Property("integracao_nfse")]
        public virtual IntegracaoNFSeEnum IntegracaoNFSe { get; set; }

        [Property("serie")]
        public virtual string Serie { get; set; }

        [Property("natureza_operacao")]
        public virtual string NaturezaDaOperacao { get; set; }

        [Property("nfse_unificada_deducao")]
        public virtual bool PossuiNfseDeducao { get; set; }

        [Property("nfse_unificada_baseCalculo")]
        public virtual bool PossuiNfseBaseCalculo { get; set; }

        [Property("nfse_unificada_baseCalculo_valorTotal")]
        public virtual bool PossuiNfseBaseCalculoValorToral { get; set; }

        [Property("permitir_dtEmissao_na_dtCompetencia")]
        public virtual bool PermitirEnvioDataEmissaoNaDataCompetencia { get; set; }

        [Property("permitir_dtCompetencia_na_dtEmissao")]
        public virtual bool PermitirEnvioDataCompetenciaNaDataEmissao { get; set; }

        [Property("UtilizarComissaoProfissionalComoCota")]
        public virtual bool? UtilizarComissaoProfissionalComoCota { get; set; }

        [Property("habilitar_nao_emitir_descontos")]
        public virtual bool? habilitarNaoEmitirDescontoOperadora { get; set; }

        [Property("utilizarPercentualCargaTributaria")]
        public virtual bool utilizarPercentualCargaTributaria { get; set; }

        [Property("iss_retido")]
        public virtual bool issRetido { get; set; }

        [Property("ExibirBaseCalculoDescricao")]
        public virtual bool ExibirBaseCalculoDescricao { get; set; }

        [Property("Producao")]
        public virtual bool Producao { get; set; }

        [Property("Emitir_Nfse_brasilia")]
        public virtual bool EmitirNfseBrasilia { get; set; }

        [Property("Cfps_no_municipio")]
        public virtual int? CfpsNoMunicipio { get; set; }

        [Property("Cfps_fora_municipio")]
        public virtual int? CfpsForaMunicipio { get; set; }

        [Property("Cfps_fora_estado")]
        public virtual int? CfpsForaEstado { get; set; }

        [Property("integracao_nfse_Anterior_Nacional_Mei")]
        public virtual IntegracaoNFSeEnum? IntegracaoNFSeAnteriorNacionalMei { get; set; }

        [Property("habilita_padrao_nacional_mei")]
        public virtual bool HabilitadoPadraoNacionalMei { get; set; }

        [Property("habilita_emissao_EI")]
        public virtual bool HabilitadoEmissaoEI { get; set; }

        [Property("habilita_padrao_nacional_estabelecimento")]
        public virtual bool HabilitadoPadraoNacionalEstabelecimento { get; set; }

        [Property("habilitar_ItemvUnit_duas_casas_decimais")]
        public virtual bool HabilitarItemvUnitDuasCasasDecimais { get; set; }

        [Property("aliquota_pis")]
        public virtual decimal AliquotaPis { get; set; }

        [Property("aliquota_cofins")]
        public virtual decimal AliquotaCofins { get; set; }

        public virtual bool EhProducaoConsiderandoAmbiente()
        {
            var pessoaJuridica = Domain.Pessoas.PessoaJuridicaRepository.Load(IdPessoaJuridica);
            var configDefault = Domain.RPS.ConfiguracaoPadraoNFSRepository.ObterValoresDefaultPorCidade(pessoaJuridica.EnderecoProprio.BairroEntidade.Cidade);
            if (configDefault == null)
                configDefault = Domain.RPS.ConfiguracaoPadraoNFSRepository.ObterValoresDefaultPorPadrao("Invoicy");
            return !configDefault.AmbienteHomologacao
            && Producao
            && Domain.WebContext.AmbienteProducao();
        }
        public virtual string IdExternoDoAmbiente()
        {
            return EhProducaoConsiderandoAmbiente() ?
            IdExterno : IdExternoHomologacao;
        }


    }
}