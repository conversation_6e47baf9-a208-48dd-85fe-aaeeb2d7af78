﻿using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Relatorios;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IEstabelecimentoRepository
    {

        ResultadoPaginado<Estabelecimento> Buscar(ParametrosBuscaProfissionalOuEstabelecimento parametros);

        int CountEstabelecimentoCadastrados();

        int CountEstabelecimentoCadastradosNoDia(DateTime dia);

        int CountEstabelecimentoParaEstatisticasDiarias();

        int CountEstabelecimentoQueAparecemNaBuscaDoPortal();

        int CountEstabelecimentoQueFecharamContaNoDia(DateTime dia);

        int CountEstabelecimentoQuePossuemHorariosNoDia(HorarioOrigemEnum origem, DateTime dia);

        bool EstabelecimentoEhValidoParaIntegracaoDeNotaFiscalDoConsumidor(int idEstabelecimento, string emailDaConta);

        bool EstabelecimentoPossuiCEP(Int32 idEstabelecimento);

        Boolean ExistemEstabelecimentosAtivosComMesmoNomeFantasia(String nomeFantasia, int idEstabelecimento);

        Boolean ExistemFotosAssociadasAoEstabelecimento(Int32 idEstabelecimento);

        IList<Estabelecimento> FiltrarBusca(ParametrosBuscaEstabelecimento parametros);

        List<KeyValuePair<int, string>> ListarEstabelecimentosKeyValuePorConta(Conta conta, bool pegarSomentePrimeiro = false);

        List<KeyValuePair<int, string>> ListarEstabelecimentosKeyValuePorContaAdministrador(Conta conta, bool pegarSomentePrimeiro = false);

        List<Estabelecimento> ListarEstabelecimentosModelos(Franquia franquia);

        IQueryable<Estabelecimento> ListarEstabelecimentosParaAreaAdministrativa();

        List<EstabelecimentoCarrosselDTO> ListarEstabelecimentosParaCarrocelDoPortal(int limite = 50);

        IQueryable<Estabelecimento> ListarOsDisponiveisNoGoogleReserve();

        IQueryable<Estabelecimento> ListarEstabelecimentosPeloModelo(IQueryable<Int32> idEstabelecimentoFranqueador);

        IQueryable<Estabelecimento> ListarEstabelecimentosPeloModelo(int idEstabelecimentoFranqueador);

        List<KeyValuePair<int, string>> ListarEstabelecimentosPorConta(Conta contaAutenticada, bool pegarSomentePrimeiro = false);

        IList<Estabelecimento> ListarEstabelecimentosValidosParaIntegracaoDeNotaFiscalDoConsumidor(string emailDaConta);

        IQueryable<Estabelecimento> ListarPorLocal(int? idEstado, int? idCidade, int? idBairro, string textoBusca = null,
            bool somenteVisivelAoPublico = true, int idFranquia = 0);

        IQueryable<Estabelecimento> ListarPorProfissional(Int32 idPessoa);

        IQueryable<Estabelecimento> ListarPorProfissionalComStatusPositivo(Int32 idPessoa);

        //Area Administrativa....
        DadosParaComboDeFiltrosDoRelatorioConsolidado ObterDadosParaFiltrosDaAreaAdministrativa(int? idPessoa);

        Estabelecimento ObterEstabelecimentoPrincipalDaConta(Conta contaAutenticada);

        IQueryable<Estabelecimento> ObterEstabelecimentosApenasComIdPreenchidoEComContaFinanceiraAtivaENaoCanceladas();

        /// <summary>
        ///     Metodo especifico para ser utilizado no trinks em numeros
        /// </summary>
        /// <returns></returns>
        IQueryable<Estabelecimento> ObterEstabelecimentosAtivosParaTrinksEmNumeros();

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtiva();

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual();

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaENaoInadimplenteOuCancelado();

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaENaoInadimplenteOuCanceladoOuNaoAssinado();

        String ObterNomeDeExibicaoNoPortalPorIdEstabelecimento(int idEstabelecimento);

        IQueryable<Estabelecimento> ObterPorInvisiveisNaBusca();

        Estabelecimento ObterPorPessoaJuridica(Int32 idPessoaJuridica);

        IQueryable<Estabelecimento> ObterVisiveisNaBusca();

        DateTime? TrataDataInicialComRestricaoDeDadosAPartirDeUmaData(Int32 idEstabelecimento,
            DateTime? dataInformadaPeloUsuario);

        DateTime TrataDataInicialComRestricaoDeDadosAPartirDeUmaData(Int32 idEstabelecimento,
            DateTime dataInformadaPeloUsuario);

        Boolean ValidarSeExisteOutroCnpjAtivo(String cnpj, int idPessoaJuridica = 0);

        IQueryable<Estabelecimento> ListarEstabelecimentosEmQueECliente(PessoaFisica pessoaFisica, bool somenteAtivos = false);

        bool ExistemEstabelecimentosComMesmoResponsavelFinanceiro(Estabelecimento estabelecimento);

        bool ObterConfiguracaoDeDataRecebimentoComissao(int idEstabelecimento);

        bool ConfiguracaoDeDataRecebimentoComissaoFoiAlterado(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral);

        bool ObterConfiguracaoDeDescontoComissaoProfissional(int idEstabelecimento);

        bool ConfiguracaoDeDescontoComissaoFoiAlterado(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral);

        bool ObterConfiguracaoDeDescontoComissaoAssistente(int idEstabelecimento);

        bool ConfiguracaoDeDescontoComissaoAssistenteFoiAlterado(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral);

        Estabelecimento ObterPorId(int idEstabelecimento);

        DadosEstabelecimentoIntegracaoDTO ProjecaoDeDadosEstabelecimentoIntegracaoDTOPorIdEstabelecimento(Estabelecimento estabelecimento);

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaAtiva();

        IQueryable<Estabelecimento> FiltrarEstabelecimentosComContaAtiva(List<int> idsEstabelecimento);

        IQueryable<Estabelecimento> ObterEstabelecimentosQuePossuaAgendamendos(IQueryable<Estabelecimento> estabelecimentos, DateTime data);

        bool EstabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento(int idEstabelecimento);

        bool EstabelecimentoEstaUtilizandoSplitDePagamento(int idEstabelecimento);

        int TotalDeCampanhasDoEstabelecimento(int idEstabelecimento);

        int ObterIdPessoaDoEstabelecimento(int idEstabelecimento);

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManualOuCancelada();

        IList<Estabelecimento> ListarHabilitadosEnvioEmailDespesas();

        //IList<Estabelecimento> ListarHabilitadosEnvioEmailDespesasLinq();

        IQueryable<Estabelecimento> ObterEstabelecimentosAtivosEmPeriodoGratis();

        IQueryable<Estabelecimento> Listar(ParametrosBusca parametros);

        IEnumerable<KeyValuePair<string, string>> ObterKeyValueUrlNomeEstabelecimento(string filtro);

        //
        IEnumerable<KeyValuePair<string, string>> ObterKeyValueUrlIdEstabelecimento(string filtro);

        IEnumerable<KeyValuePair<string, string>> ObterKeyValueNomeIdEstabelecimento(string filtro);

        Estabelecimento BuscarEstabelecimentoPeloNome(string nomeEstabelecimento);

        Estabelecimento BuscarEstabelecimentoPeloCNPJ(string cnpj);

        IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManualOuForaToleranciaOuCancelada();

        bool EhUmaUnidadeDaFranquia(int idEstabelecimento, int idFranquia);

        bool EhPessoaJuridicaFranqueadaAtiva(int idPessoa);

        DadosParaEnvioDeEmailVendaHotsiteDTO ObterDadosParaEnvioDeEmailVendaHotsitePorIdEstabelecimento(int idEstabelecimento);
        List<int> ListarIdsEstabelecimentoPorProfissionalComStatusPositivo(int idPessoa);

        int ObterIdPorPessoaJuridica(int idPessoaJuridica);
        Estabelecimento ObterEstabelecimentodoProfissionalParceiro(int idPessoaJuridica);

        List<KeyValuePair<int, int>> ObterPorListaPessoaJuridica(List<int> idsPessoaJuridica);

        List<Estabelecimento> ObterEstabelecimentosDaDataQueTenhamFilaDoRodizio(DateTime data);

        List<Estabelecimento> ObterEstabelecimentosPorIds(List<int> idsEstabelecimentos);
    }
}