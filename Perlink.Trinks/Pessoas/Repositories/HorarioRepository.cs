﻿using NHibernate;
using NHibernate.Criterion;
using NHibernate.Linq;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.DTO;
using Perlink.Trinks.Estabelecimentos.DTO;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Filtros;
using Perlink.Trinks.Pessoas.Helpers;
using Perlink.Trinks.Promocoes;
using Perlink.Trinks.Promocoes.Enums;
using Perlink.Trinks.Relatorios.Enums;
using Perlink.Trinks.RodizioDeProfissionais.DTO;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Vendas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class HorarioRepository : IHorarioRepository
    {

        #region Métodos Públicos

        #region Filtrar por Cliente

        public IQueryable<Horario> QueryableHorariosParaComboMovimentacaoEstoque(int idEstabelecimento, DateTime dataMovimentacao, List<StatusHorario> listaStatusExcecao)
        {
            var queryable = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                                   p.DataInicio.Date == dataMovimentacao.Date);

            queryable = FiltroExcetoStatus(listaStatusExcecao, queryable);
            return queryable;
        }

        public List<Horario> FiltrarPorClienteMobile(int idCliente, int idFranquia = 0)
        {
            var queryable = Queryable();
            queryable = queryable.Where(p => p.Cliente.IdCliente == idCliente);
            queryable = queryable.Where(p => p.DataInicio > Calendario.Hoje());

            if (idFranquia > 0)
                queryable = queryable.Where(p => p.Estabelecimento.FranquiaEstabelecimento != null
                    && p.Estabelecimento.FranquiaEstabelecimento.Ativo
                    && p.Estabelecimento.FranquiaEstabelecimento.Franquia.Id == idFranquia
                    && p.ServicoEstabelecimento.ServicoIndiposnivelParaCliente == false);

            return queryable.OrderBy(p => p.DataInicio).ToList();
        }

        #endregion Filtrar por Cliente

        public bool ClienteEstaEmDebito(int idEstabelecimento, int idCliente, bool incluiHoje = false)
        {
            return (ListarHorariosDoClienteEmDebito(idEstabelecimento, idCliente, incluiHoje).Any());
        }

        public int CountAgendamentosCriadosNoDia(List<HorarioOrigemEnum> listaHorarioOrigemEnum, DateTime dia)
        {
            var query = Queryable();

            if (listaHorarioOrigemEnum != null && listaHorarioOrigemEnum.Any())
                query = query.Where(h => listaHorarioOrigemEnum.Contains((HorarioOrigemEnum)h.HorarioOrigem.IdHorarioOrigem));

            return query.Count(h => h.Historicos.Any(j => j.DataHoraAlteracao.Date == dia.Date) &&
                                    h.DataHoraCriacao.Date == dia.Date);
        }

        public List<Horario> ListarAgendamentosFuturosNaoPagosAPartirDe(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32? idServicoEstabelecimento = null)
        {
            return
                ObterAgendamentosFuturosNaoPagosAPartirDe(aPartirDe, idEstabelecimento, idServicoEstabelecimento)
                    .ToList();
        }

        public bool ExisteAgendamentoFuturoNaoPagosAPartirDe(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32? idServicoEstabelecimento = null)
        {
            return
                ObterAgendamentosFuturosNaoPagosAPartirDe(aPartirDe, idEstabelecimento, idServicoEstabelecimento).Any();
        }

        public List<int> ExisteAgendamentoFuturoNaoPagosAPartirDeParaAlterarParaPrecoPromocional(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32 idServicoEstabelecimento, DateTime? dataInicioVigencia, DateTime? dataFimVigencia, int tipoVigencia, List<PromocaoDiaDaSemana> listaPromocaoDiaDaSemana, decimal precoPadrao, bool temPromocao = false)
        {
            var agentamentos = ObterAgendamentosFuturosNaoPagosAPartirDeParaAlterarParaPrecoPromocional(aPartirDe, idEstabelecimento, idServicoEstabelecimento, dataInicioVigencia, dataFimVigencia, tipoVigencia);

            var listaInt = new List<int>();

            var totalAgendamentosParaPromocao = QuantidadeAgendamentosParaPromocao(agentamentos, dataInicioVigencia, dataFimVigencia, (TipoPromocaoVigenciaEnum)tipoVigencia, listaPromocaoDiaDaSemana);
            listaInt.Add(totalAgendamentosParaPromocao);

            var totalAgendamentosParaPrecoPadrao = QuantidadeAgendamentosComPrecoPadrao(agentamentos, dataInicioVigencia, dataFimVigencia, (TipoPromocaoVigenciaEnum)tipoVigencia, listaPromocaoDiaDaSemana, precoPadrao);
            listaInt.Add(totalAgendamentosParaPrecoPadrao);
            return listaInt;
        }

        private int QuantidadeAgendamentosComPrecoPadrao(IQueryable<Horario> horarios, DateTime? dataInicioVigencia, DateTime? dataFimVigencia, TipoPromocaoVigenciaEnum tipoVigencia, List<PromocaoDiaDaSemana> listaPromocaoDiaDaSemana, decimal precoPadrao)
        {
            int totalAgendamentosParaPrecoPadrao = 0;
            bool temPromocaoParaODia;

            var horariosDTO = horarios.Select(f => new { f.DataInicio, f.Valor });

            foreach (var h in horariosDTO)
            {
                temPromocaoParaODia = listaPromocaoDiaDaSemana.Any(p => p.DiaDaSemana == (int)h.DataInicio.DayOfWeek && p.Valor != null);

                if (tipoVigencia == TipoPromocaoVigenciaEnum.PorPeriodo && HorarioEstaDentroDoPeriodoDaPromocao(dataInicioVigencia, dataFimVigencia, h.DataInicio))
                {
                }
                else if (h.Valor != precoPadrao && !temPromocaoParaODia)
                {
                    totalAgendamentosParaPrecoPadrao++;
                }
            }

            return totalAgendamentosParaPrecoPadrao;
        }

        private int QuantidadeAgendamentosParaPromocao(IQueryable<Horario> horarios, DateTime? dataInicioVigencia, DateTime? dataFimVigencia, TipoPromocaoVigenciaEnum tipoVigencia, List<PromocaoDiaDaSemana> listaPromocaoDiaDaSemana)
        {
            if (tipoVigencia == TipoPromocaoVigenciaEnum.Nenhum)
                return 0;

            int totalAgendamentosParaPromocao = 0;
            bool temPromocaoParaODia, temPromocaoParaODiaComValorDiferente;

            var horariosDTO = horarios.Select(f => new { f.DataInicio, f.Valor });

            foreach (var h in horariosDTO)
            {
                temPromocaoParaODia = listaPromocaoDiaDaSemana.Any(p => p.DiaDaSemana == (int)h.DataInicio.DayOfWeek && p.Valor != null);
                temPromocaoParaODiaComValorDiferente = listaPromocaoDiaDaSemana.Any(p => p.DiaDaSemana == (int)h.DataInicio.DayOfWeek && p.Valor != null && p.Valor != h.Valor);

                if (!temPromocaoParaODiaComValorDiferente || !temPromocaoParaODia)
                    continue;

                if (tipoVigencia == TipoPromocaoVigenciaEnum.PorPeriodo && HorarioEstaDentroDoPeriodoDaPromocao(dataInicioVigencia, dataFimVigencia, h.DataInicio))
                {
                    totalAgendamentosParaPromocao++;
                }
                else if (tipoVigencia == TipoPromocaoVigenciaEnum.Sempre)
                {
                    totalAgendamentosParaPromocao++;
                }
            }

            return totalAgendamentosParaPromocao;
        }

        public int ExisteAgendamentoFuturoNaoPagosAPartirDeParaAlterarPrecoNaExclusaoDePromocao(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32 idServicoEstabelecimento, DateTime? dataInicioVigencia, DateTime? dataFimVigencia, int tipoVigencia, List<PromocaoDiaDaSemana> listaPromocaoDiaDaSemana)
        {
            var agentamentos = ObterAgendamentosFuturosNaoPagosAPartirDeParaAlterarParaPrecoPromocional(aPartirDe, idEstabelecimento, idServicoEstabelecimento, dataInicioVigencia, dataFimVigencia, tipoVigencia);
            int totalAgendamentosParaPromocao = 0;

            foreach (Horario agendamento in agentamentos)
            {
                if (listaPromocaoDiaDaSemana.Any(p => p.DiaDaSemana == (int)agendamento.DataInicio.DayOfWeek && p.Valor != null && p.Valor == agendamento.Valor) &&
                    tipoVigencia == (int)TipoPromocaoVigenciaEnum.PorPeriodo && HorarioEstaDentroDoPeriodoDaPromocao(dataInicioVigencia, dataFimVigencia, agendamento.DataInicio))
                {
                    totalAgendamentosParaPromocao++;
                }
                else if (listaPromocaoDiaDaSemana.Any(p => p.DiaDaSemana == (int)agendamento.DataInicio.DayOfWeek && p.Valor != null && p.Valor == agendamento.Valor) && tipoVigencia == (int)TipoPromocaoVigenciaEnum.Sempre)
                {
                    totalAgendamentosParaPromocao++;
                }
            }

            return totalAgendamentosParaPromocao;
        }

        private bool HorarioEstaDentroDoPeriodoDaPromocao(DateTime? dataInicio, DateTime? dataFim, DateTime dataInicioHorario)
        {
            if (dataInicio.HasValue)
                return (dataInicioHorario.Date >= dataInicio.Value.Date && !dataFim.HasValue) || (dataInicioHorario.Date >= dataInicio.Value.Date && dataFim.HasValue && dataInicioHorario.Date <= dataFim.Value.Date);
            else
                return false;
        }

        public Int32 CountAgendamentosFuturos(Int32 codigoClienteEstabelecimento)
        {
            var retorno = Queryable();

            retorno = FiltroDataInicioMaiorQueAData(Calendario.Agora(), retorno);
            retorno = FiltroSemStatus(9, retorno);
            retorno = FiltroClienteEstabelecimento(codigoClienteEstabelecimento, retorno);
            retorno = FiltroAtivo(retorno);

            return retorno.Count();
        }

        public IList<Horario> ObterAgendamentosNaoPagosDoDiaPorCliente(DateTime data, Estabelecimento estabelecimento,
            Cliente cliente, bool paraCancelamento)
        {
            var query = Queryable().Where(f => f.DataInicio >= data.Date
                                                          && f.DataInicio < data.Date.AddDays(1)
                                                          && f.Status != StatusHorarioEnum.Cancelado
                                                          && !f.FoiPago
                                                          && f.Estabelecimento == estabelecimento
                                                          && f.Cliente == cliente);
            if (paraCancelamento)
                query = query.Where(h => !h.FoiPagoAntecipadamente);

            return query.ToList();
        }

        public IList<Horario> ObterAgendamentosDoDiaPorCliente(DateTime data, Estabelecimento estabelecimento,
            Cliente cliente, bool paraCancelamento)
        {
            var query = Queryable().Where(f => f.DataInicio >= data.Date
                                                          && f.DataInicio < data.Date.AddDays(1)
                                                          && f.Status != StatusHorarioEnum.Cancelado
                                                          && f.Estabelecimento == estabelecimento
                                                          && f.Cliente == cliente);
            if (paraCancelamento)
                query = query.Where(h => !h.FoiPagoAntecipadamente);

            return query.ToList();
        }

        public IList<Horario> ObterAgendamentosDoDiaPorClienteParaComanda(DateTime data, Estabelecimento estabelecimento,
            Cliente cliente)
        {
            var preVendaQuery = Domain.Vendas.PreVendaServicoRepository.Queryable();
            var query = Queryable().Where(f => f.DataInicio >= data.Date
                             && f.DataInicio < data.Date.AddDays(1)
                             && f.Status != StatusHorarioEnum.Cancelado
                             && f.Status != StatusHorarioEnum.Cliente_Faltou
                             && !f.FoiPago
                             && f.Estabelecimento == estabelecimento
                             && f.Cliente == cliente
                             && !preVendaQuery.Any(p => p.Comanda != null && p.Horario.Id == f.Id));

            return query.ToList();
        }

        public int CountAgendamentosNaoBalcaoCriadosNoDia(int idPessoa, DateTime dia)
        {
            var query = Queryable().Where(h => h.HorarioOrigem != HorarioOrigemEnum.Balcao);
            query = query.Where(f => f.Cliente.PessoaFisica.IdPessoa == idPessoa);
            return query.Count(h => h.DataHoraCriacao.Date == dia.Date);
        }

        public bool ExisteConflitoDeHorariosParaProfissional(Horario horario)
        {
            var dc = DetachedCriteria.For(typeof(Horario));
            AdicionarCriterioAtivo(dc);
            AdicionarCriterioNaoCancelado(dc);

            if (horario.Profissional != null)
                AdicionarCriterioIdProfissional(horario.Profissional.IdProfissional, dc);
            AdicionarCriterioDataInicioMenorQue(horario.DataFim, dc);
            AdicionarCriterioDataFimMaiorQue(horario.DataInicio, dc);
            AdicionarCriterioIdEstabelecimento(horario.Estabelecimento.IdEstabelecimento, dc);

            if (horario.Codigo.HasValue)
                AdicionarCriterioNaoPossuiCodigoHorario(horario.Codigo, dc);

            //Evita que o horário seja alterado ao realizar o Exists, retornando o valor ja selecionado.
            Evict(horario);

            return Exists(dc);
        }

        public Boolean ExistemHorarioDeOrigemBalcaoParaCliente(int idPessoa)
        {
            return
                Queryable()
                    .Any(p => p.HorarioOrigem == HorarioOrigemEnum.Balcao && p.Cliente.PessoaFisica.IdPessoa == idPessoa);
        }

        public List<Horario> Filtrar(ParametrosBuscaHorario parametros, bool ordenar = true, bool fetchEmPreVendas = false, bool fetchEmHorariosTransacao = false)
        {
            var retorno = FiltroDeHorarios(parametros, false);

            //Fetches
            var a = retorno.Select(f => f.ServicoEstabelecimento).Distinct().ToList();
            var b = retorno.Select(f => f.ClienteEstabelecimento).Distinct().ToList();
            var c = retorno.Select(f => f.ClienteEstabelecimento.Cliente).Distinct().ToList();
            var d = retorno.Select(f => f.ClienteEstabelecimento.Cliente.PessoaFisica).Distinct().ToList();
            var e = retorno.Select(f => f.Profissional).Distinct().ToList();
            var g = retorno.Select(f => f.Profissional.PessoaFisica).Distinct().ToList();

            if (fetchEmPreVendas)
                retorno.SelectMany(f => f.PreVendas).ToList();

            if (fetchEmHorariosTransacao)
                retorno.SelectMany(f => f.HorariosTransacoes).ToList();

            return retorno.ToList();
        }

        public List<HorarioAgendaDTO> FiltrarDTO(ParametrosBuscaHorario parametros, bool ordenar = true, bool fetchEmPreVendas = false, bool fetchEmHorariosTransacao = false)
        {
            var horarios = FiltroDeHorarios(parametros, false);

            var dadosCliente = Domain.Pessoas.ItemComboClienteRepository.Queryable();

            var tagSemMensagemEnviada = (from i in Domain.WhatsApp.HorarioTagRepository.Queryable() where i.Nome == "SemMensagemEnviada" select i).FirstOrDefault();

            var retorno = from h in horarios
                          let ce = h.ClienteEstabelecimento
                          let pfCliente = ce.Cliente.PessoaFisica
                          let se = h.ServicoEstabelecimento
                          let status = h.Status
                          let profissional = h.Profissional
                          let pessoaFisica = profissional.PessoaFisica
                          let temProfissional = profissional != null
                          select new HorarioAgendaDTO
                          {
                              Id = h.Id,
                              StatusCor = status.Cor,
                              StatusId = status.Codigo,
                              StatusNome = status.Nome,
                              Origem = (HorarioOrigemEnum)h.HorarioOrigem.IdHorarioOrigem,
                              ClienteAtivo = ce.Ativo,
                              ClienteDataNascimento = pfCliente.DataNascimento,
                              ClienteEstabelecimentoId = ce.Codigo,
                              IdPessoaDoCliente = pfCliente.IdPessoa,
                              ClienteNomeCompleto = pfCliente.NomeCompleto,
                              DataInicio = h.DataInicio,
                              DataFim = h.DataFim,
                              Duracao = h.Duracao,
                              ProfissionalApelido = temProfissional ? pessoaFisica.Apelido : "",
                              ProfissionalId = temProfissional ? profissional.IdProfissional : 0,
                              ProfissionalNome = temProfissional ? pessoaFisica.NomeCompleto : "",
                              ServicoEstabelecimentoId = se.IdServicoEstabelecimento,
                              ServicoEstabelecimentoNome = se.Nome,
                              Valor = h.Valor,
                              FoiPago = h.FoiPago,
                              TemRecorrencia = h.IdRecorrenciaHorario != null,
                              Observacao = h.Observacao,
                              ObservacaoDoCliente = h.ObservacaoCliente,
                              ObservacoesSobreCliente = ce.Observacoes,
                              ClienteIncompleto = pfCliente.Email == null && pfCliente.Cpf == null,
                              FoiPagoAntecipadamente = h.FoiPagoAntecipadamente
                          };

            return retorno.ToList();
        }

        public List<HorarioAgendaDTO> FiltrarAgendaDTO(ParametrosBuscaHorario parametros, bool ordenar = true)
        {
            var dataFinal = new DateTime(parametros.DataInicial.Year,
                                              parametros.DataInicial.Month,
                                              parametros.DataInicial.Day,
                                              23,
                                              59,
                                              59);
            parametros.DataFinal = dataFinal;
            return FiltrarDTO(parametros, ordenar);
        }

        public IList<Horario> FiltrarPorIntervaloData(int codigoProfissional, int codigoEstabelecimento,
            IntervaloData intervalo, bool incluiCancelados = false)
        {
            var horarios = Queryable();
            horarios = horarios.Where(f => f.Ativo);
            horarios = horarios.Where(f => f.DataInicio < intervalo.Fim && f.DataFim > intervalo.Inicio);
            horarios = horarios.Where(f => f.Profissional != null && f.Profissional.IdProfissional == codigoProfissional);
            horarios = horarios.Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);

            if (!incluiCancelados)
                horarios = horarios.Where(f => f.Status != StatusHorarioEnum.Cancelado);

            return horarios.ToList();
        }

        public IntervaloDataList ListarIntervalosOcupadosNoPeriodo(int codigoProfissional, int codigoEstabelecimento, IntervaloData intervalo, bool incluiCancelados = false)
        {
            var horarios = Queryable();
            horarios = horarios.Where(f => f.Ativo);
            horarios = horarios.Where(f => f.DataInicio < intervalo.Fim && f.DataFim > intervalo.Inicio);
            horarios = horarios.Where(f => f.Profissional != null && f.Profissional.IdProfissional == codigoProfissional);
            horarios = horarios.Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);

            if (!incluiCancelados)
                horarios = horarios.Where(f => f.Status != StatusHorarioEnum.Cancelado);

            var dados = horarios.Select(h => new { h.DataInicio, h.Duracao }).ToList();

            var retorno = new IntervaloDataList();
            retorno.AddRange(dados.Select(d => new IntervaloData(d.DataInicio, d.Duracao)));
            return retorno;
        }

        public IQueryable<Horario> FiltroDeHorarios(ParametrosBuscaHorario parametros, bool ordenar = true, bool stateless = false)
        {
            var possuiFiltrosMinimos = false;

            var retorno = stateless ? StatelessQueryable() : Queryable();

            if (parametros.DataInicial.Year > 1900)
                retorno = FiltroDataInicio(parametros.DataInicial, retorno);

            if (parametros.DataFinal.HasValue && parametros.DataFinal.Value.Year > 1900)
                retorno = FiltroDataFim(parametros.DataFinal.Value, retorno);

            if (parametros.IdEstabelecimento > 0)
            {
                retorno = FiltroEstabelecimento(parametros.IdEstabelecimento, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.IdPessoaLogada > 0)
            {
                retorno = FiltroPessoaLogado(parametros.IdPessoaLogada, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.TipoFiltroAgenda == TipoFiltroAgendaEnum.AgendamentosCategoria &&
                parametros.CategoriasServicoSelecionadas.Count > 0)
                retorno = FiltroCategorias(parametros.CategoriasServicoSelecionadas, retorno);

            if (parametros.FiltrarComAssistenteOuProfissional)
                if (parametros.ProfissionaisSelecionados.Count > 0)
                {
                    if (parametros.FiltrarComAssistenteOuProfissional)
                    {
                        if (parametros.EhProfissionalQuePodeSeAssociarComoAssistente && parametros.AssistentePodeVerAgendamentosQuePodeParticipar)
                        {
                            retorno = FiltroAssistenteOuProfissionalESemAssistenteParaHorario(parametros.ProfissionaisSelecionados, retorno);
                        }
                        else
                        {
                            retorno = FiltroAssistenteOuProfissional(parametros.ProfissionaisSelecionados, retorno);
                        }
                        possuiFiltrosMinimos = true;
                    }
                    else
                        retorno = FiltroProfissionais(parametros.ProfissionaisSelecionados, retorno);
                }

            if (parametros.StatusSelecionados.Count > 0)
                retorno = FiltroStatus(parametros.StatusSelecionados, retorno);

            if (parametros.ExcetoStatus.Count > 0)
                retorno = FiltroExcetoStatus(parametros.ExcetoStatus, retorno);

            if (parametros.IdCliente > 0)
            {
                retorno = FiltroCliente(parametros.IdCliente, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.IdEstabelecimentoCliente > 0)
            {
                retorno = FiltroEstabelecimentoCliente(parametros.IdEstabelecimentoCliente, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.IdRecorrencia.HasValue)
                retorno = FiltroRecorrencia(parametros.IdRecorrencia.Value, retorno);

            if (parametros.FechamentoContaSelecionado > 0)
            {
                retorno = parametros.FechamentoContaSelecionado == 2
                    ? FiltroPagas(retorno)
                    : FiltroNaoPagas(retorno);
            }

            if (parametros.SomenteComFoto)
            {
                var horarioDasFotos = Domain.Pessoas.FotoDeServicoRealizadoEmHorarioRepository.Queryable().Select(f => f.Horario);

                if (parametros.IdEstabelecimento > 0)
                    horarioDasFotos = horarioDasFotos.Where(f => f.Estabelecimento.IdEstabelecimento == parametros.IdEstabelecimento);

                retorno = retorno.Where(f => horarioDasFotos.Contains(f));
            }

            retorno = FiltroAtivo(retorno);

            if (ordenar)
                retorno = OrdenarPorData(retorno);

            if (parametros.TagWhatsAppSelecionada?.Any() ?? false)
            {
                var exibirTagWhatsApp = Domain.WhatsApp.SaldoEstabelecimentoRepository.Queryable(true).Where(i => i.IdEstabelecimento == parametros.IdEstabelecimento).Any();

                if (exibirTagWhatsApp)
                {
                    var tags = (
                        from i in Domain.WhatsApp.HorarioTagRepository.Queryable()
                        where parametros.TagWhatsAppSelecionada.Contains(i.Id)
                        select i
                    );
                    var tagSemMensagemEnviada = (
                            from i in Domain.WhatsApp.HorarioTagRepository.Queryable()
                            where i.Nome == "SemMensagemEnviada"
                            select i
                        ).FirstOrDefault();
                    retorno = (
                        from r in retorno
                        let tag = (
                            from w in Domain.WhatsApp.HistoricoHorarioTagRepository.Queryable()
                            where w.IdHorario == r.Id
                            orderby w.DataCriacao descending
                            select w.HorarioTag
                        ).FirstOrDefault()
                        where (tag == null ? tags.Contains(tagSemMensagemEnviada) : tags.Contains(tag))
                        select r
                    );
                }
            }

            if (!possuiFiltrosMinimos)
                throw new ArgumentException("Não possui filtros mínimos para a consulta");

            return retorno;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosRecentementeAgendadosPorUmCliente(Int32 idCliente)
        {
            var estabelecimentosQry = from h in Queryable()
                                      where h.ClienteEstabelecimento.Cliente.IdCliente == idCliente &&
                                            h.Estabelecimento.PessoaJuridica.Ativo
                                      orderby h.DataInicio descending
                                      select h.Estabelecimento;
            return estabelecimentosQry;
        }

        public IList<Horario> HorariosConflitandoParaProfissional(Horario horario)
        {
            var dc = DetachedCriteria.For(typeof(Horario));
            AdicionarCriterioAtivo(dc);
            AdicionarCriterioNaoCancelado(dc);

            if (horario.Profissional != null)
                AdicionarCriterioIdProfissional(horario.Profissional.IdProfissional, dc);

            AdicionarCriterioDataInicioMenorQue(horario.DataFim, dc);
            AdicionarCriterioDataFimMaiorQue(horario.DataInicio, dc);
            AdicionarCriterioIdEstabelecimento(horario.Estabelecimento.IdEstabelecimento, dc);

            if (horario.Codigo.HasValue)
                AdicionarCriterioNaoPossuiCodigoHorario(horario.Codigo, dc);

            //Evita que o horário seja alterado ao realizar o Exists, retornando o valor ja selecionado.
            try
            {
                //TODO: verificar por que o Evict dá erro quando passa por horário com checkout
                Evict(horario);
            }
            catch { }

            return LoadAll(dc);
        }

        public IList<Horario> HorariosConflitandoParaProfissionalSemEvict(Horario horario)
        {
            var dc = DetachedCriteria.For(typeof(Horario));
            AdicionarCriterioAtivo(dc);
            AdicionarCriterioNaoCancelado(dc);

            if (horario.Profissional != null)
                AdicionarCriterioIdProfissional(horario.Profissional.IdProfissional, dc);

            AdicionarCriterioDataInicioMenorQue(horario.DataFim, dc);
            AdicionarCriterioDataFimMaiorQue(horario.DataInicio, dc);
            AdicionarCriterioIdEstabelecimento(horario.Estabelecimento.IdEstabelecimento, dc);

            if (horario.Codigo.HasValue)
                AdicionarCriterioNaoPossuiCodigoHorario(horario.Codigo, dc);

            //Evita que o horário seja alterado ao realizar o Exists, retornando o valor ja selecionado.
            //try {
            //    //TODO: verificar por que o Evict dá erro quando passa por horário com checkout
            //    Evict(horario);
            //} catch { }

            return LoadAll(dc);
        }

        public IList<Horario> HorariosConflitandoParaProfissional(int idProfissional, DateTime DataInicio, DateTime DataFim, int idEstabelecimento, int? idHorario = null)
        {
            if (idProfissional == 0)
                return new List<Horario>();
            var dc = DetachedCriteria.For(typeof(Horario));
            AdicionarCriterioAtivo(dc);
            AdicionarCriterioNaoCancelado(dc);
            AdicionarCriterioIdProfissional(idProfissional, dc);
            AdicionarCriterioDataInicioMenorQue(DataFim, dc);
            AdicionarCriterioDataFimMaiorQue(DataInicio, dc);
            AdicionarCriterioIdEstabelecimento(idEstabelecimento, dc);

            if (idHorario.HasValue)
                AdicionarCriterioNaoPossuiCodigoHorario(idHorario, dc);

            return LoadAll(dc);
        }

        public IList<Horario> HorariosConflitandoParaProfissionalDeClientesDiferentes(Horario horario)
        {
            if (horario.Profissional == null)
                return new List<Horario>();
            var dc = DetachedCriteria.For(typeof(Horario));
            AdicionarCriterioAtivo(dc);
            AdicionarCriterioNaoCancelado(dc);

            if (horario.Profissional != null)
                AdicionarCriterioIdProfissional(horario.Profissional.IdProfissional, dc);

            AdicionarCriterioDataInicioMenorQue(horario.DataFim, dc);
            AdicionarCriterioDataFimMaiorQue(horario.DataInicio, dc);
            AdicionarCriterioIdEstabelecimento(horario.Estabelecimento.IdEstabelecimento, dc);
            AdicionarCriterioClienteDiferente(horario.Cliente.IdCliente, dc);

            if (horario.Codigo.HasValue)
                AdicionarCriterioNaoPossuiCodigoHorario(horario.Codigo, dc);

            //Evita que o horário seja alterado ao realizar o Exists, retornando o valor ja selecionado.
            Evict(horario);

            return LoadAll(dc);
        }

        public decimal ObterTotalDebitosDeClienteAnterioresAData(int idCliente, int idEstabelecimento, DateTime dataLimite)
        {
            var isDataLimiteTodayOrLater = dataLimite >= DateTime.Today;

            decimal? debito = null;
            if (isDataLimiteTodayOrLater)
                debito = ObterTotalDebitosDeCliente(idCliente, idEstabelecimento);

            if (debito.HasValue)
                return debito.Value;

            // Se não tiver saldo já desnormalizado, busca os horários em débito
            var horarios = QueryHorariosEmDebitoParaRelatorioClientesEmDebito(new ParametrosFiltroCliente { CodigoEstabelecimento = idEstabelecimento }, true);
            horarios = horarios.Where(h => h.Cliente.IdCliente == idCliente && h.DataInicio < dataLimite);
            debito = horarios.Sum(h => (decimal?)h.Valor) ?? 0;
            return debito.Value;
        }

        private decimal? ObterTotalDebitosDeCliente(int idCliente, int idEstabelecimento)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterClienteEstabelecimento(idCliente, idEstabelecimento);
            var saldo = Domain.Pessoas.ClienteEstabelecimentoSaldosRepository.Load(clienteEstabelecimento.Codigo, false);
            return saldo?.TotalDebitos;
        }

        public IList<ClientesEmDebito> ListarClientesEmDebito(ParametrosFiltroCliente filtro, bool incluiHoje = false)
        {
            var horarios = QueryHorariosEmDebitoParaRelatorioClientesEmDebito(filtro, incluiHoje);
            horarios = AdicionarFiltroPorCliente(horarios, filtro);

            if (filtro.ListarPor == TiposDeVizualizacoesClienteEmDebito.Cliente)
            {
                var horariosDoCliente = (from h in horarios
                                         group h by new { ClienteEstabelecimento = h.ClienteEstabelecimento } into g
                                         select g).ToList();

                return horariosDoCliente.Select(f => new ClientesEmDebito
                {
                    ClienteEstabelecimento = f.Key.ClienteEstabelecimento,
                    Valor = f.Sum(g => g.Valor),
                    TotalCredito = f.Key.ClienteEstabelecimento.ValorCredito,
                    SaldoDoCliente = (f.Key.ClienteEstabelecimento.ValorCredito - f.Sum(g => g.Valor))
                }).ToList();
            }

            var retorno = (from h in horarios
                           group h by new
                           {
                               ClienteEstabelecimento = h.ClienteEstabelecimento,
                               Comanda = Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(h.Id) != null
                                            ? Domain.Vendas.PreVendaServicoRepository.ObterPorHorario(h.Id).Comanda : null
                           } into g
                           select g).ToList();

            if (filtro.ApenasComanda.HasValue && filtro.ApenasComanda.Value)
            {
                return retorno.Where(f => f.Key.Comanda != null).Select(f => new ClientesEmDebito
                {
                    ClienteEstabelecimento = f.Key.ClienteEstabelecimento,
                    Valor = f.Sum(g => g.Valor),
                    Comanda = f.Key.Comanda
                }).ToList();
            }

            return retorno.Select(f => new ClientesEmDebito
            {
                ClienteEstabelecimento = f.Key.ClienteEstabelecimento,
                Valor = f.Sum(g => g.Valor),
                TotalCredito = f.Key.ClienteEstabelecimento.ValorCredito,
                SaldoDoCliente = (f.Key.ClienteEstabelecimento.ValorCredito - f.Sum(g => g.Valor)),
                Comanda = f.Key.Comanda
            }).ToList();
        }

        public IList<ClientesEmDebito> ListarClientesEmDebitoDias(int idClienteEstabelecimento, DateTime? dataInicio, DateTime? dataFim, bool incluiHoje = false, bool incluirComandas = true)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            var filtro = new ParametrosFiltroCliente()
            {
                CodigoEstabelecimento = clienteEstabelecimento.Estabelecimento.IdEstabelecimento,
                DataInicio = dataInicio,
                DataFim = dataFim
            };

            var horarios = QueryHorariosEmDebitoParaRelatorioClientesEmDebito(filtro, incluiHoje, incluirComandas);
            horarios = horarios.Where(f => f.ClienteEstabelecimento == clienteEstabelecimento);

            var retorno = (from h in horarios
                           group h by h.DataInicio.Date
                               into g
                           select g).ToList();

            return retorno.Select(f => new ClientesEmDebito
            {
                ClienteEstabelecimento = clienteEstabelecimento,
                Data = f.Key,
                Valor = f.Sum(g => g.Valor)
            }).ToList();
        }

        public IQueryable<Horario> ListarNoDiaComProfissionalComClienteComEmail(DateTime data)
        {
            return Queryable()
                .Where(f => f.Ativo
                            && f.Status.Codigo != (int)StatusHorarioEnum.Cancelado
                            && f.DataInicio >= data.Date
                            && f.DataInicio < data.Date.AddDays(1)
                            && f.Historicos.Min(p => p.DataHoraAlteracao) < Calendario.Hoje()
                            && f.Profissional != null
                            && f.Cliente.PessoaFisica.Email != null
                            && f.ClienteEstabelecimento.EnviarEmailAgendamentoCliente);
        }

        public IQueryable<Horario> ListarHorariosDoClienteEmDebito(int idEstabelecimento, int idCliente,
            bool incluiHoje = false)
        {
            var query = ListarHorariosEmDebito(idEstabelecimento, incluiHoje);
            return FiltroCliente(idCliente, query);
        }

        public IQueryable<Horario> QueryHorariosEmDebitoParaRelatorioClientesEmDebito(ParametrosFiltroCliente filtro, bool incluiHoje = false, bool incluirComandas = true)
        {
            var query = Queryable();
            query = query.Where(f => f.Ativo && !f.FoiPago);
            query = FiltroStatus((int)StatusHorarioEnum.Finalizado, query);
            query = FiltroEstabelecimento(filtro.CodigoEstabelecimento, query);
            query = FiltroSomenteGestaoAtual(filtro.CodigoEstabelecimento, query);

            if (filtro.DataInicio.HasValue)
                query = query.Where(f => f.DataInicio >= filtro.DataInicio.Value.Date);
            if (filtro.DataFim.HasValue)
                query = query.Where(f => f.DataFim <= filtro.DataFim.Value.Date.AddDays(1).AddSeconds(-1));

            if (!filtro.DataInicio.HasValue &&
                !filtro.DataFim.HasValue)
            {
                query = !incluiHoje
                           ? query.Where(f => f.DataInicio < Calendario.Hoje())
                           : query.Where(f => f.DataInicio < Calendario.Hoje().AddDays(1));
            }

            if (!incluirComandas)
            {
                var prevendas = Domain.Vendas.PreVendaServicoRepository.Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == filtro.CodigoEstabelecimento && f.PreVendaStatus != StatusPreVendaEnum.Excluido);
                query = query.Where(f => !prevendas.Any(g => g.Horario == f));
            }

            return query;
        }

        public IQueryable<Horario> ListarHorariosEmDebito(int idEstabelecimento, bool incluiHoje = false)
        {
            var query = Queryable();
            query = query.Where(f => f.Ativo && !f.FoiPago);

            query = !incluiHoje
                ? query.Where(f => f.DataInicio.Date < Calendario.Hoje())
                : query.Where(f => f.DataInicio.Date < Calendario.Hoje().AddDays(1));

            query = FiltroStatus((int)StatusHorarioEnum.Finalizado, query);
            query = FiltroEstabelecimento(idEstabelecimento, query);
            query = FiltroSomenteGestaoAtual(idEstabelecimento, query);
            return query;
        }

        public ResultadoPaginado<Horario> ListarHorariosPaginados(ParametrosFiltroHistoricoCliente parametros)
        {
            var retorno = ListarHorarios(parametros);
            return retorno.OrderBy(f => f.DataInicio)
                .ToResultadoPaginado(parametros.ParametrosPaginacao);
        }

        public ResultadoPaginado<Horario> ListarHorariosPaginadosPorDataDecrescente(
            ParametrosFiltroHistoricoCliente parametros)
        {
            return ListarHorarios(parametros)
                .OrderByDescending(l => l.DataInicio)
                .ToResultadoPaginado(parametros.ParametrosPaginacao);
        }

        public List<Horario> ListarHorariosPorFiltro(ParametrosFiltroConsultarAgendamentos parametros)
        {
            var possuiFiltrosMinimos = false;

            var retorno = Queryable();

            if (parametros.DataInicio.Year > 1900)
            {
                retorno = FiltroDataInicio(parametros.DataInicio, retorno);
            }

            if (parametros.DataFim.Year > 1900)
            {
                retorno = FiltroDataFim(parametros.DataFim, retorno);
            }

            if (parametros.CodigoEstabelecimento > 0)
            {
                retorno = FiltroEstabelecimento(parametros.CodigoEstabelecimento, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.ProfissionalSelecionado > 0)
            {
                retorno = FiltroProfissionais(parametros.ProfissionalSelecionado, retorno, incluiComoAssistente: parametros.IncluiComoAssistente);
                possuiFiltrosMinimos = true;
            }

            if (parametros.StatusAgendamentoSelecionado > 0)
                retorno = FiltroStatus(parametros.StatusAgendamentoSelecionado, retorno);

            if (parametros.IdCliente > 0)
            {
                retorno = FiltroCliente(parametros.IdCliente.Value, retorno);
                possuiFiltrosMinimos = true;
            }
            else if (!string.IsNullOrWhiteSpace(parametros.ValorCampoFiltroCliente))
            {
                retorno = FiltroCliente(parametros.ValorCampoFiltroCliente, parametros.TipoDeBuscaCampoCliente, retorno, parametros.CodigoEstabelecimento);
            }

            if (parametros.ServicoSelecionado > 0)
            {
                retorno = FiltroServico(parametros.ServicoSelecionado, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.FechamentoContaSelecionado > 0)
            {
                retorno = parametros.FechamentoContaSelecionado == 2
                    ? FiltroPagas(retorno)
                    : FiltroNaoPagas(retorno);
            }

            if (parametros.AgendadoPor != null && parametros.AgendadoPor.Count > 0)
            {
                retorno = FiltroAgendadoPor(parametros.AgendadoPor, retorno);
            }

            retorno = FiltroAtivo(retorno);
            retorno = OrdenarPorData(retorno);

            if (!possuiFiltrosMinimos)
                throw new ArgumentException("Não possui filtros mínimos para a consulta");

            return retorno.ToList();
        }

        public List<HorarioDTO> ListarHorariosPorFiltroDTO(ParametrosFiltroConsultarAgendamentos parametros)
        {
            var query = Queryable();

            if (parametros.DataInicio.Year > 1900)
            {
                query = FiltroDataInicio(parametros.DataInicio, query);
            }

            if (parametros.DataFim.Year > 1900)
            {
                query = FiltroDataFim(parametros.DataFim, query);
            }

            if (parametros.CodigoEstabelecimento > 0)
            {
                query = FiltroEstabelecimento(parametros.CodigoEstabelecimento, query);
            }

            if (parametros.CategoriaSelecionada > 0)
            {
                query = FiltroCategoria(parametros.CategoriaSelecionada, query);
            }

            if (parametros.ProfissionalSelecionado > 0)
                query = FiltroProfissionais(parametros.ProfissionalSelecionado, query);

            if (parametros.StatusAgendamentoSelecionado > 0)
                query = FiltroStatus(parametros.StatusAgendamentoSelecionado, query);

            if (!string.IsNullOrWhiteSpace(parametros.ValorCampoFiltroCliente))
            {
                query = FiltroCliente(parametros.ValorCampoFiltroCliente, parametros.TipoDeBuscaCampoCliente, query, parametros.CodigoEstabelecimento);
            }

            if (parametros.ServicoSelecionado > 0)
            {
                query = FiltroServico(parametros.ServicoSelecionado, query);
            }

            if (parametros.FechamentoContaSelecionado > 0)
            {
                query = parametros.FechamentoContaSelecionado == 2
                    ? FiltroPagas(query)
                    : FiltroNaoPagas(query);
            }

            if (parametros.AgendadoPor != null && parametros.AgendadoPor.Count > 0)
            {
                query = FiltroAgendadoPor(parametros.AgendadoPor, query);
            }

            if (parametros.SomenteAgendamentoRecorrente)
            {
                query = query.Where(r => r.RecorrenciaHorario != null);

                if (parametros.ApenasQueSejaOUltimoDaRecorrencia)
                {
                    query = FiltroQueSejaUltimoAgendamentoDaRecorrencia(query);
                }
            }

            if (parametros.EtiquetasSelecionadas.Count() > 0)
            {
                var idsClienteEstabelecimentoEtiquetados = Domain.Marcadores.ObjetoEtiquetadoRepository.Queryable()
                        .Where(e => e.Etiqueta.IdDono == parametros.CodigoEstabelecimento &&
                                  e.Etiqueta.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento &&
                                  e.Etiqueta.Ativo &&
                                  parametros.EtiquetasSelecionadas.Contains(e.Etiqueta.IdEtiqueta))
                        .Select(o => o.IdObjetoEtiquetado);

                query = query.Where(f => idsClienteEstabelecimentoEtiquetados.Any(c => c == f.Codigo));
            }

            query = FiltroAtivo(query);
            //query = OrdenarPorData(query);

            if (parametros.ApenasAgendamentosDaVez)
            {
                query = ApllicarFiltroAgendamentosComProfissionalDaVez(query);
            }

            var retorno = query.Select(f => new HorarioDTO
            {
                DataHora = f.DataInicio,
                Profissional = f.Profissional != null ? f.Profissional.PessoaFisica.NomeOuApelido() : null,
                NomeQuemRealizouAgendamento = f.PessoaQuemMarcou != null ? f.PessoaQuemMarcou.NomeOuApelido() : null,
                ApelidoOUNomeAssistente = f.EstabelecimentoProfissionalAssistente != null ? f.EstabelecimentoProfissionalAssistente.Profissional.PessoaFisica.NomeOuApelido() : null,
                Servico = f.ServicoEstabelecimento.Nome,
                ServicoCategoria = f.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Nome,
                Duracao = f.Duracao,
                Cliente = f.Cliente.PessoaFisica.NomeCompleto,
                Sexo = f.Cliente.PessoaFisica.Genero.Codigo(),
                DataCadastroCliente = f.ClienteEstabelecimento.DataCadastro,
                IdPessoa = f.Cliente.PessoaFisica.IdPessoa,
                Valor = f.Valor,
                FoiPago = f.FoiPago,
                Status = f.Status,
                DataCadastro = f.DataHoraCriacao,
                IdClienteEstabelecimento = f.ClienteEstabelecimento.Codigo,
                IdHorario = f.Codigo.Value,
                IdProfissional = f.Profissional != null ? f.Profissional.IdProfissional : 0,
                DataNascimento = f.Cliente.PessoaFisica.DataNascimento,
                Observacao = f.Observacao,
                ObservacaoRecorrencia = f.RecorrenciaHorario != null ? f.RecorrenciaHorario.Observacao : null,
                ObservacaoCliente = f.ObservacaoCliente,
                ObservacaoClienteEstabelecimento = f.ClienteEstabelecimento.Observacoes,
                EmailCliente = f.Cliente.PessoaFisica.Email,
                HorarioOrigem = new HorarioOrigemDTO
                {
                    IdHorarioOrigem = f.HorarioOrigem.IdHorarioOrigem,
                    Nome = f.HorarioOrigem.Nome,
                    Ativo = f.HorarioOrigem.Ativo
                }
            });

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(parametros.CodigoEstabelecimento);
            var telefonesCliente = Domain.Pessoas.TelefoneRepository.Queryable()
                .Where(
                    f =>
                        retorno.Select(g => g.IdPessoa).Contains(f.IdPessoa) &&
                        (f.Dono.IdPessoa == f.IdPessoa || f.Dono.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa) &&
                        f.Ativo)
                .ToList();

            var htsPagamento = ListarHorariosTransacaoDosHorariosFiltrados(query);
            var htsCancelamento = ListarHistoricosDeCancelamentosDosHorariosFiltrados(query);

            var agendamentosQueForamDaVez = new List<AgendamentoQueFoiDaVezDTO>();
            if (estabelecimento.JaTrabalhouComRodizioDeProfissionais() && !parametros.ApenasAgendamentosDaVez)
                agendamentosQueForamDaVez = ListarAgendamentosQueForamComProfissionalDaVez(query);

            var lista = retorno.OrderBy(f => f.DataHora).ToList();
            foreach (var h in lista)
            {
                var ht = htsPagamento.FirstOrDefault(f => f.Horario.Codigo == h.IdHorario);
                if (ht != null)
                {
                    if (ht.ItemPacoteCliente != null)
                    {
                        h.ValorPacote = ht.ItemPacoteCliente.ValorUnitario;
                        h.EhConsumoPacote = true;
                    }
                    h.ValorPago = (ht.Preco ?? 0) + (ht.Desconto ?? 0);
                }

                var tc = telefonesCliente.Where(f => f.IdPessoa == h.IdPessoa).Take(3);
                if (tc != null && tc.Any())
                {
                    var listaTelefones = tc.Select(f => f.ToString()).ToList();

                    h.Telefones = String.Join(" / ", listaTelefones);
                }

                if (h.Status == StatusHorarioEnum.Cancelado)
                {
                    var cancelamento = htsCancelamento.FindLast(f => f.Horario.Codigo == h.IdHorario);
                    if (cancelamento != null)
                        h.MotivoCancelamento = cancelamento.MotivoCancelamento;
                }

                h.FoiProfissionalDaVez = parametros.ApenasAgendamentosDaVez || agendamentosQueForamDaVez.Any(a => a.IdHorario == h.IdHorario && a.IdProfissionalQueFoiDavez == h.IdProfissional);
            }

            return lista;
        }

        private static List<HorarioTransacao> ListarHorariosTransacaoDosHorariosFiltrados(IQueryable<Horario> query)
        {
            //var pagos = query.Where(f => f.FoiPago);
            //var htsPagamento = Domain.Pessoas.HorarioTransacaoRepository.Queryable()
            //    .Where(
            //        f =>
            //            pagos.Contains(f.Horario) && f.Transacao.TipoTransacao.Id == 1 &&
            //            f.Transacao.TransacaoQueEstounouEsta == null).ToList();

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            return (from h in query
                    join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on h.Id equals ht.Horario.Id
                    where h.FoiPago
                       && ht.Transacao.TipoTransacao.Id == 1
                       && ht.Transacao.TransacaoQueEstounouEsta == null
                       && ht.Transacao.PessoaQueRecebeu == estabelecimento.PessoaJuridica
                    select ht).ToList();
        }

        private static List<HorarioHistorico> ListarHistoricosDeCancelamentosDosHorariosFiltrados(IQueryable<Horario> query)
        {
            //var cancelados = query.Where(f => f.Status == StatusHorarioEnum.Cancelado);
            //var htsCancelamento = Domain.Pessoas.HorarioHistoricoRepository.Queryable()
            //    .Where(f => cancelados.Contains(f.Horario) && f.MotivoCancelamento != null && f.MotivoCancelamento != "")
            //    .ToList();

            return (from h in query
                    join hh in Domain.Pessoas.HorarioHistoricoRepository.Queryable() on h.Id equals hh.IdHorario
                    where h.Status.Codigo == (int)StatusHorarioEnum.Cancelado
                       && hh.MotivoCancelamento != null
                       && hh.MotivoCancelamento != ""
                    select hh).ToList();
        }

        private List<AgendamentoQueFoiDaVezDTO> ListarAgendamentosQueForamComProfissionalDaVez(IQueryable<Horario> query)
        {
            var horariosNoRodizio = Domain.RodizioDeProfissionais.HorarioNoRodizioRepository.Queryable();

            return (from h in query
                    join hrod in horariosNoRodizio on h.Id equals hrod.IdHorario
                    select new AgendamentoQueFoiDaVezDTO(h.Id, hrod.IdProfissional)
                    ).ToList();
        }

        public ResultadoPaginado<Horario> ListarHorariosPorFiltroPaginado(
            ParametrosFiltroConsultarAgendamentos parametros, Profissional profissionalAutenticado = null, bool origemAreaProfissional = false)
        {
            var possuiFiltrosMinimos = false;

            var retorno = Queryable();
            var idProfissional = 0;
            retorno = retorno.Fetch(f => f.Profissional).ThenFetch(f => f.PessoaFisica);
            retorno = retorno.Fetch(f => f.ClienteEstabelecimento).Fetch(f => f.Cliente);

            if (profissionalAutenticado != null)
            {
                var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
                retorno =
                    retorno.Where(
                        f =>
                            estabelecimentoProfissional.Any(
                                g =>
                                    g.PodeAcessarMinhaAgenda && g.Profissional == profissionalAutenticado &&
                                    g.Estabelecimento == f.Estabelecimento));
                retorno = FiltroProfissionais(profissionalAutenticado.IdProfissional, retorno);
                idProfissional = profissionalAutenticado.IdProfissional;
                possuiFiltrosMinimos = true;
            }

            if (parametros.DataInicio.Year > 1900)
            {
                retorno = FiltroDataInicio(parametros.DataInicio, retorno);
            }

            if (parametros.DataFim.Year > 1900)
            {
                retorno = FiltroDataFim(parametros.DataFim, retorno);
            }

            if (parametros.CodigoEstabelecimento > 0)
            {
                retorno = FiltroEstabelecimento(parametros.CodigoEstabelecimento, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.ServicosComFoto > 0 && parametros.CodigoEstabelecimento > 0)
            {
                var horarioDasFotos = Domain.Pessoas.FotoDeServicoRealizadoEmHorarioRepository.Queryable().Select(f => f.Horario);

                if (parametros.ServicosComFoto == 1)
                {
                    retorno = retorno.Where(f => horarioDasFotos.Contains(f));
                }
                else if (parametros.ServicosComFoto == 2)
                {
                    retorno = retorno.Where(f => !horarioDasFotos.Contains(f));
                }
            }

            if (parametros.ProfissionalSelecionado > 0)
            {
                retorno = FiltroProfissionais(parametros.ProfissionalSelecionado, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.StatusAgendamentoSelecionado > 0)
                retorno = FiltroStatus(parametros.StatusAgendamentoSelecionado, retorno);

            if (!string.IsNullOrWhiteSpace(parametros.ValorCampoFiltroCliente))
            {
                retorno = FiltroCliente(parametros.ValorCampoFiltroCliente, parametros.TipoDeBuscaCampoCliente, retorno, parametros.CodigoEstabelecimento);
            }

            if (parametros.CategoriaSelecionada > 0)
            {
                retorno = FiltroCategoria(parametros.CategoriaSelecionada, retorno);
            }

            if (parametros.ServicoSelecionado > 0)
            {
                retorno = FiltroServico(parametros.ServicoSelecionado, retorno);
                possuiFiltrosMinimos = true;
            }

            if (parametros.FechamentoContaSelecionado > 0)
            {
                retorno = parametros.FechamentoContaSelecionado == 2
                    ? FiltroPagas(retorno)
                    : FiltroNaoPagas(retorno);
            }

            if (parametros.AgendadoPor != null && parametros.AgendadoPor.Count > 0)
            {
                retorno = FiltroAgendadoPor(parametros.AgendadoPor, retorno);
            }

            if (parametros.SomenteAgendamentoRecorrente)
            {
                retorno = retorno.Where(r => r.RecorrenciaHorario != null);

                if (parametros.ApenasQueSejaOUltimoDaRecorrencia)
                {
                    retorno = FiltroQueSejaUltimoAgendamentoDaRecorrencia(retorno);
                }
            }

            if (parametros.EtiquetasSelecionadas != null && parametros.EtiquetasSelecionadas.Count() > 0)
            {
                var idsClienteEstabelecimentoEtiquetados = Domain.Marcadores.ObjetoEtiquetadoRepository.Queryable()
                        .Where(e => e.Etiqueta.IdDono == parametros.CodigoEstabelecimento &&
                                  e.Etiqueta.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.Agendamento &&
                                  e.Etiqueta.Ativo &&
                                  parametros.EtiquetasSelecionadas.Contains(e.Etiqueta.IdEtiqueta))
                        .Select(o => o.IdObjetoEtiquetado);

                retorno = retorno.Where(f => idsClienteEstabelecimentoEtiquetados.Any(c => c == f.Codigo));
            }

            if (parametros.ApenasAgendamentosDaVez)
            {
                retorno = ApllicarFiltroAgendamentosComProfissionalDaVez(retorno);
            }

            retorno = FiltroAtivo(retorno);

            if (origemAreaProfissional && idProfissional > 0)
            {
                retorno = FiltroDeProfissionalQuePodeVerAreaProfissionalDeDiasAnterioresEPosteriores(retorno, idProfissional);
            }

            retorno = OrdenarPorData(retorno);

            var posicaoInicial = (parametros.ParametrosPaginacao.RegistrosPorPagina *
                                  parametros.ParametrosPaginacao.Pagina -
                                  parametros.ParametrosPaginacao.RegistrosPorPagina);

            if (!possuiFiltrosMinimos)
                throw new ArgumentException("Não possui filtros mínimos para a consulta");

            parametros.ParametrosPaginacao.TotalItens = retorno.Count();
            retorno = retorno.Skip(posicaoInicial).Take(parametros.ParametrosPaginacao.RegistrosPorPagina);

            return new ResultadoPaginado<Horario>(retorno.ToList(), parametros.ParametrosPaginacao);
        }

        private IQueryable<Horario> FiltroDeProfissionalQuePodeVerAreaProfissionalDeDiasAnterioresEPosteriores(IQueryable<Horario> retorno, int idProfissional)
        {
            var profissional = Domain.Pessoas.ProfissionalRepository.Load(idProfissional);

            var estabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                .Where(f => f.PessoaFisica.IdPessoa == profissional.PessoaFisica.IdPessoa
                    && f.Ativo && f.PerfilAcesso != AcessoBackoffice.Acesso_total && f.PerfilAcesso != AcessoBackoffice.Somente_Agenda
                    && f.Estabelecimento.EstabelecimentoConfiguracaoGeral.ProfissionalPodeVerAreaProfissionalDeDiasAnterioresEPosteriores != (int)ConfiguracaoProfissionalPodeVerAreaProfissional.TodosOsDias)
                 .Select(f => new KeyValuePair<int, int>(f.Estabelecimento.IdEstabelecimento, f.Estabelecimento.EstabelecimentoConfiguracaoGeral.ProfissionalPodeVerAreaProfissionalDeDiasAnterioresEPosteriores)).ToList();

            if (estabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm.Count > 0)
            {
                var idsEstabelecimentosDataAtual = estabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm.Where(kv => kv.Value == (int)ConfiguracaoProfissionalPodeVerAreaProfissional.SomenteDiaDeHoje).Select(kv => kv.Key).ToList();
                var idsEstabelecimentosAtualOuDataFutura = estabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm.Where(kv => kv.Value == (int)ConfiguracaoProfissionalPodeVerAreaProfissional.HojeEhDiasFuturos).Select(kv => kv.Key).ToList();
                var idsEstabelecimentosAtualOuPassada = estabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm.Where(kv => kv.Value == (int)ConfiguracaoProfissionalPodeVerAreaProfissional.HojeEhDiasPassados).Select(kv => kv.Key).ToList();
                var idsEstabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm = estabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm.Select(kv => kv.Key).ToList();

                retorno = retorno.Where(h =>
                    (!idsEstabelecimentosComConfiguracaoOndeNaoEhRecepcaoNemAdm.Contains(h.Estabelecimento.IdEstabelecimento))
                    || (idsEstabelecimentosDataAtual.Contains(h.Estabelecimento.IdEstabelecimento) && h.DataInicio >= Calendario.Hoje() && h.DataInicio < Calendario.Hoje().AddDays(1))
                    || (idsEstabelecimentosAtualOuDataFutura.Contains(h.Estabelecimento.IdEstabelecimento) && h.DataInicio >= Calendario.Hoje())
                    || (idsEstabelecimentosAtualOuPassada.Contains(h.Estabelecimento.IdEstabelecimento) && h.DataInicio < Calendario.Hoje().AddDays(1)));
            }
            return retorno;
        }

        private static IQueryable<Horario> ApllicarFiltroAgendamentosComProfissionalDaVez(IQueryable<Horario> retorno)
        {
            var horariosNoRodizio = Domain.RodizioDeProfissionais.HorarioNoRodizioRepository.Queryable();

            retorno = from h in retorno
                      join hr in horariosNoRodizio on h.Id equals hr.IdHorario
                      where hr.IdProfissional == h.Profissional.IdProfissional
                      select h;

            return retorno;
        }

        public List<Horario> ListarPendentesConfirmacao(FiltroConsultaNotificacao filtro)
        {
            var query = Queryable().Where(h => h.Estabelecimento.IdEstabelecimento.Equals(filtro.IdEstabelecimento)
                                            && h.Status == StatusHorarioEnum.Aguardando_Confirmacao
                                            && h.DataInicio >= filtro.DataInicio
                                            && h.Ativo)
                                    .Skip(filtro.IndexPaginaASerListada)
                                    .Take(filtro.QtdParaListar)
                                    .OrderBy(h => h.DataInicio);

            return query.ToList();
        }

        public int ObterNumeroPendentesConfirmacao(int codigoEstabelecimento, DateTime dataInicio)
        {
            var query = Queryable().Where(h => h.Estabelecimento.IdEstabelecimento.Equals(codigoEstabelecimento)
                                            && h.Status == StatusHorarioEnum.Aguardando_Confirmacao
                                            && h.DataInicio >= dataInicio
                                            && h.Ativo);

            return query.Count();
        }

        public List<Horario> ObterAgendamentosFuturosConfirmadosEhOndeClienteEstabelecimentoRecebaNotificacao(Estabelecimento estabelecimento)
        {
            return
                Queryable()
                    .Where(
                        f =>
                            f.Ativo &&
                            f.Profissional != null &&
                            f.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento &&
                            f.Status == StatusHorarioEnum.Confirmado &&
                            (!f.ClienteEstabelecimento.RecebeNotificacao.HasValue ||
                            f.ClienteEstabelecimento.RecebeNotificacao.Value))
                    .ToList();
        }

        public List<Horario> ObterAgendamentosFuturosConfirmadosEhOndeClienteEstabelecimentoRecebaNotificacao(ClienteEstabelecimento clienteEstabelecimento)
        {
            return
                Queryable()
                    .Where(
                        f =>
                            f.Ativo &&
                            f.Profissional != null &&
                            f.ClienteEstabelecimento.Codigo == clienteEstabelecimento.Codigo &&
                            f.Status == StatusHorarioEnum.Confirmado &&
                            (!f.ClienteEstabelecimento.RecebeNotificacao.HasValue ||
                            f.ClienteEstabelecimento.RecebeNotificacao.Value))
                    .ToList();
        }

        public List<Horario> ObterComNotificacoesProgramadas(Estabelecimento estabelecimento)
        {
            return
                Queryable()
                    .Where(
                        f =>
                            f.Profissional != null &&
                            f.DataHoraNotificacaoClienteProgramada != null &&
                            f.DataHoraNotificacaoClienteEnviada == null &&
                            f.Estabelecimento == estabelecimento)
                    .ToList();
        }

        private IQueryable<Horario> QueryableHorariosOnlineEstabelecimento(Int32 idEstabelecimento)
        {
            return Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                          p.HorarioOrigem != HorarioOrigemEnum.Balcao);
        }

        public Int32 ObterQuantidadeAgendamentosOnlineRealizadosNoEstabelecimento(Int32 idEstabelecimento)
        {
            return QueryableHorariosOnlineEstabelecimento(idEstabelecimento).Count();
        }

        public long QuantidadeDeAgendamentosAtivosPorEstabelecimento(int idEstabelecimento,
           DateTime? dataInicioMinima = null)
        {
            var query = Queryable(true);

            query = query.Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento && h.Ativo);

            if (dataInicioMinima.HasValue)
                query = query.Where(h => h.DataInicio >= dataInicioMinima.Value);

            return query.LongCount();
        }

        public long QuantidadeDeDiasDiferentesDeAgendamentosEstabelecimento(int idEstabelecimento,
             DateTime? dataInicioMinima = null)
        {
            var query = Queryable(true)
                .Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento && h.Ativo);

            if (dataInicioMinima.HasValue)
                query = query.Where(f => f.DataInicio >= dataInicioMinima.Value.Date);

            return query.Select(f => f.DataInicio).ToList().GroupBy(f => f.Date).LongCount();
        }

        public DateTime? ObterDataCriacaoDoUltimoHorarioOnlineAgendado(Int32 idEstabelecimento)
        {
            return QueryableHorariosOnlineEstabelecimento(idEstabelecimento).Select(p => (DateTime?)p.DataHoraCriacao).Max();
        }

        public DateTime? ObterDataCriacaoDoUltimoHorarioBalcaoAgendado(Int32 idEstabelecimento)
        {
            var query = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                    (p.HorarioOrigem == HorarioOrigemEnum.Balcao));

            return query.Select(p => (DateTime?)p.DataHoraCriacao).Max();
        }

        public DateTime? ObterDataPrimeiroAgendamentoClienteEstabelecimento(Int32 idClienteEstabelecimento,
            DateTime? aPartirDe)
        {
            var horario = ObterHorarioPrimeiroAgendamentoClienteEstabelecimento(idClienteEstabelecimento, aPartirDe);
            return horario == null ? (DateTime?)null : horario.DataInicio;
        }

        public DateTime? ObterDataDoUltimoServicoAgendadoAteDataAtual(Int32 idClienteEstabelecimento)
        {
            return Queryable()
                .Where(h => h.ClienteEstabelecimento.Codigo == idClienteEstabelecimento && h.DataInicio <= Calendario.Hoje())
                .Select(h => (DateTime?)h.DataInicio)
                .Max();
        }

        public IList<Horario> ObterHorariosParaRelatorio(ParametrosFiltrosRelatorio parametros)
        {
            var dc = DetachedCriteria.For(typeof(Horario));
            AdicionarCriterioPeriodo(parametros, dc);

            if (parametros.IdProfissional != -1)
                AdicionarCriterioProfissionais(parametros, dc);

            AdicionarCriterioEstabelecimento(parametros, dc);
            AdicionarOrdernacaoPorData(dc);
            return LoadAll(dc);
        }

        private IQueryable<Horario> FiltrarNotificacoesPendentesApartirDeHoje(ClienteEstabelecimento clienteEstabelecimento, IQueryable<Horario> queryable)
        {
            var retorno = queryable;
            retorno = retorno.Fetch(f => f.Profissional).ThenFetch(f => f.PessoaFisica);
            retorno = retorno.Fetch(f => f.ServicoEstabelecimento);

            retorno = FiltroClienteEstabelecimento(clienteEstabelecimento.Codigo, retorno);
            retorno = FiltroAtivo(retorno);
            retorno = FiltroDataInicioMaiorQueAData(Calendario.Agora(), retorno);
            retorno = FiltroDataInicio(Calendario.Hoje(), retorno);
            retorno = FiltroStatus((int)StatusHorarioEnum.Confirmado, retorno);

            return retorno;
        }

        public IQueryable<Horario> ObterNotificacoesPendentesDoDiaSeguinte(ClienteEstabelecimento clienteEstabelecimento)
        {
            var retorno = Queryable();
            retorno = FiltrarNotificacoesPendentesApartirDeHoje(clienteEstabelecimento, retorno);
            retorno = FiltroPendenteDeEnvioNotificacaoNaData(retorno, Calendario.Hoje(), Calendario.Hoje().AddDays(1));
            retorno = retorno.Where(f => f.DataDeEmissaoFilaParaEnvio == null);
            retorno = OrdenarPorData(retorno);

            return retorno;
        }

        public IQueryable<Horario> ObterNotificacoesPendentesDeHoje(ClienteEstabelecimento clienteEstabelecimento)
        {
            var retorno = Queryable();
            retorno = FiltrarNotificacoesPendentesApartirDeHoje(clienteEstabelecimento, retorno);
            retorno = FiltroPendenteDeEnvioNotificacaoNaData(retorno, Calendario.Hoje().AddDays(-1), Calendario.Hoje());
            retorno = retorno.Where(f => f.DataDeEmissaoFilaParaEnvio == null);
            retorno = OrdenarPorData(retorno);

            return retorno;
        }

        public IQueryable<Horario> ObterPendentesDeNotificacao(bool stateless = false)
        {
            var retorno = stateless ? StatelessQueryable() : Queryable();
            retorno = retorno.Fetch(f => f.ClienteEstabelecimento);
            retorno = FiltroAtivo(retorno);
            retorno = FiltroDataInicioMaiorQueAData(Calendario.Agora(), retorno);
            retorno = FiltroStatus((int)StatusHorarioEnum.Confirmado, retorno);
            retorno = FiltroClienteComTelefoneCelular(retorno);
            retorno = FiltroPendenteDeEnvioNotificacao(retorno);
            return retorno;
        }

        public void SaveOrUpdate(Horario entity)
        {
            //TAREFA  http://jira.perlink.net/browse/TRINKS-9690
            //CODIGO TEMPORÁRIO CRIADO PARA CONTINGENCIAR HORARIOS COM DURAÇÃO 0
            //FOI CRIADA UMA CONSTRAINT NO BANCO DE DADOS PARA QUE HORARIOS QUE JÁ POSSUEM DURAÇÃO 0 POSSAM SER MANIPULADOS PELO SISTEMA
            //A REGRA DESSA CONSTRAINT ESTÁ SENDO ADICIONADA NO CÓDIGO TEMPORÁRIAMENTE ATÉ TENHAMOS RESOLVIDO TODOS OS CASOS HORARIOS COM DURAÇÃO 0
            if (entity.Duracao == 0)
            {
                if (String.IsNullOrEmpty(entity.PromoCode) ||
                   (!String.IsNullOrEmpty(entity.PromoCode) && entity.PromoCode != " "))
                {
                    throw new Exception("Agendamento com duração 0.");
                }
            }

            if (entity.Codigo.HasValue)
                Update(entity);
            else
                SaveNew(entity);
        }

        public int ObterQuantidadeAgendamentosPorEstabelecimentoProfissionalAPartirDe(DateTime aPartirDe,
            Int32 idEstabelecimento, Int32 idProfissional)
        {
            var query = Queryable();

            query = ObterAgendamentosFuturosNaoPagosAPartirDe(aPartirDe, idEstabelecimento, incluiNaoPagasAntecipadamente: false);
            query = query.Where(f => f.Profissional != null && f.Profissional.IdProfissional == idProfissional);

            return query.Count();
        }

        private IQueryable<Horario> AdicionarFiltroAgendamentosPorClienteEstabelecimento(Int32 idClienteEstabelecimento,
            IQueryable<Horario> query)
        {
            return query.Where(h => h.ClienteEstabelecimento.Codigo == idClienteEstabelecimento);
        }

        private IQueryable<Horario> FiltroSomenteGestaoAtual(int idEstabelecimento, IQueryable<Horario> query)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var configuracoes = estabelecimento.EstabelecimentoConfiguracaoGeral;

            if (configuracoes.ExibirDadosAPartirDe.HasValue)
                query = query.Where(f => f.DataInicio > configuracoes.ExibirDadosAPartirDe.Value);

            return query;
        }

        public IQueryable<Horario> ListarHorarios(ParametrosFiltroHistoricoCliente parametros)
        {
            var retorno = Queryable();
            retorno = retorno.Where(f =>
                        f.Ativo &&
                        f.Estabelecimento.IdEstabelecimento == parametros.IdEstabelecimento &&
                        f.Cliente.IdCliente == parametros.IdCliente
                );

            if (parametros.DataInicio.HasValue)
                retorno = retorno.Where(f =>
                    f.DataInicio >= parametros.DataInicio.Value.Date);

            if (parametros.DataFim.HasValue)
            {
                var dataFim = new DateTime(parametros.DataFim.Value.Year, parametros.DataFim.Value.Month,
                    parametros.DataFim.Value.Day, 23, 59, 59);

                retorno = retorno.Where(f =>
                        f.DataFim <= dataFim);
            }

            if (parametros.Status.HasValue)
                retorno = retorno.Where(f => f.Status == parametros.Status.Value);

            if (parametros.FoiPago.HasValue)
                retorno = retorno.Where(f => f.FoiPago == parametros.FoiPago.Value);

            if (parametros.ApenasSemComanda)
            {
                var buscaPreVenda = Domain.Vendas.PreVendaServicoRepository.Queryable();

                retorno = from h in retorno
                          where !buscaPreVenda.Any(p => p.IdHorario == h.Id && p.PreVendaStatus == StatusPreVendaEnum.EmAberto)
                          select h;
            }

            return retorno;
        }

        private IQueryable<Horario> ObterAgendamentosFuturosNaoPagosAPartirDe(DateTime aPartirDe,
            Int32 idEstabelecimento, Int32? idServicoEstabelecimento = null, bool incluiNaoPagasAntecipadamente = true)
        {
            var statusDesejados = new List<StatusHorario>();

            var query = Queryable();

            query = FiltroDataInicioMaiorQueAData(aPartirDe, query);
            query = FiltroEstabelecimento(idEstabelecimento, query);
            if (idServicoEstabelecimento.HasValue)
                query = FiltroServico(idServicoEstabelecimento.Value, query);
            query = FiltroAtivo(query);
            query = FiltroNaoPagas(query);

            if (incluiNaoPagasAntecipadamente)
                query = FiltroNaoPagasAntecipadamente(query);

            return query;
        }

        private IQueryable<Horario> ObterAgendamentosFuturosNaoPagosAPartirDeParaAlterarParaPrecoPromocional(DateTime aPartirDe, Int32 idEstabelecimento, Int32 idServicoEstabelecimento,
            DateTime? dataInicioVigencia, DateTime? dataFimVigencia, int tipoVigencia)
        {
            var statusDesejados = new List<StatusHorario>();

            var query = Queryable();

            query = FiltroDataInicioMaiorQueAData(aPartirDe, query);
            query = FiltroEstabelecimento(idEstabelecimento, query);
            query = FiltroServico(idServicoEstabelecimento, query);
            query = FiltroAtivo(query);
            query = FiltroNaoPagas(query);
            query = FiltroNaoPagasAntecipadamente(query);

            return query;
        }

        private IQueryable<Horario> ObterAgendamentosDoClienteEstabelecimento(Int32 idClienteEstabelecimento)
        {
            var query = Queryable();
            query = AdicionarFiltroAgendamentosPorClienteEstabelecimento(idClienteEstabelecimento, query);

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            query = FiltroEstabelecimento(clienteEstabelecimento.Estabelecimento.IdEstabelecimento, query);
            return query;
        }

        private Horario ObterHorarioPrimeiroAgendamentoClienteEstabelecimento(Int32 idClienteEstabelecimento,
            DateTime? dataVenda)
        {
            var query = ObterAgendamentosDoClienteEstabelecimento(idClienteEstabelecimento);

            if (dataVenda.HasValue)
                query = query.Where(f => f.DataInicio >= dataVenda);

            return query.FirstOrDefault(f => f.DataInicio == query.Min(g => g.DataInicio));
        }

        public IQueryable<Horario> ListarHorariosDoClienteNoStatus(int idClienteEstabelecimento, DateTime dataAgendamento, int? idProfissional = null, bool somenteNaoPagos = false, params StatusHorario[] status)
        {
            var query = Queryable();
            query = query.Where(f => status.Contains(f.Status)
                && (f.DataInicio >= dataAgendamento.Date && f.DataInicio < dataAgendamento.Date.AddDays(1))
                && f.ClienteEstabelecimento.Codigo == idClienteEstabelecimento);

            if (idProfissional.HasValue)
                query = query.Where(f => f.Profissional != null && f.Profissional.IdProfissional == idProfissional);

            if (somenteNaoPagos)
                query = query.Where(f => !f.FoiPago);

            return query.OrderBy(f => f.DataInicio).ThenBy(f => f.Profissional.IdProfissional);
        }

        public int QuantidadeDeHorariosDoClienteNoStatus(int idClienteEstabelecimento, DateTime dataAgendamento, int? idProfissional = null, bool somenteNaoPagos = false, params StatusHorario[] status)
        {
            var query = ListarHorariosDoClienteNoStatus(idClienteEstabelecimento, dataAgendamento, idProfissional, somenteNaoPagos, status);
            return query.Count();
        }

        public void TransferirHorariosParaOutroProfissional(int idProfissionalOrigem, int idProfissionalDestino)
        {
            var querySQL = "UPDATE " + typeof(Horario).Name + " SET "
                + "id_profissional = :idDefinitivo "
                + "WHERE id_profissional = :idDescartado";

            IQuery query = Session().CreateQuery(querySQL);
            query.SetParameter("idDescartado", idProfissionalOrigem);
            query.SetParameter("idDefinitivo", idProfissionalDestino);
            var rows = query.ExecuteUpdate();
        }

        public DateTime? ObterUltimoHorarioDoClienteComProfissional(int idClienteEstabelecimento, int? idProfissional, DateTime dataHoraInicio)
        {
            var horarios = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(f => f.Status != StatusHorarioEnum.Cancelado
                        && f.ClienteEstabelecimento.Codigo == idClienteEstabelecimento
                        && f.Profissional.IdProfissional == idProfissional
                        && f.DataInicio >= dataHoraInicio.Date
                        && f.DataInicio < dataHoraInicio.Date.AddDays(1));

            return horarios.Max(f => (DateTime?)f.DataFim);
        }

        #endregion Métodos Públicos

        #region Trinks em Números

        public IList<KeyValuePair<StatusHorarioEnum, int>> CountAgendamentosAtivosPorStatus()
        {
            var retorno = from i in Queryable()
                          where i.Ativo
                          group i by i.Status.Codigo
                              into g
                          select new KeyValuePair<StatusHorarioEnum, int>((StatusHorarioEnum)g.Key, g.Count());
            return retorno.ToList();
        }

        public int CountAgendamentosInativos()
        {
            return Queryable().Count(f => !f.Ativo);
        }

        #endregion Trinks em Números

        #region Filtros Queryable

        private static IQueryable<Horario> AdicionarCriterioCpf(String cpf, IQueryable<Horario> query)
        {
            return query.Where(p => p.Cliente.PessoaFisica.Cpf == cpf.Replace("-", "").Replace(".", "").Trim());
        }

        private static IQueryable<Horario> AdicionarCriterioNome(String nome, IQueryable<Horario> query)
        {
            return
                query.Where(
                    p => p.Cliente.PessoaFisica.NomeCompleto.Contains(nome));
        }

        private static IQueryable<Horario> FiltroAtivo(IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.Ativo);
            return retorno;
        }

        public static IQueryable<Horario> FiltroCategoria(int idCategoriaEstabelecimento, IQueryable<Horario> retorno)
        {
            return retorno.Where(f => f.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Codigo == idCategoriaEstabelecimento);
        }

        private static IQueryable<Horario> FiltroCategorias(
            List<ServicoCategoriaEstabelecimento> categoriasServicoSelecionadas, IQueryable<Horario> retorno)
        {
            retorno = retorno
                .Where(f => categoriasServicoSelecionadas
                                .Contains(f.ServicoEstabelecimento.ServicoCategoriaEstabelecimento));

            var estabelecimentoProfissionalRepository = new EstabelecimentoProfissionalRepository();
            var profissionaisDasCategorias = estabelecimentoProfissionalRepository.Queryable()
                                                                                  .Where(f => f.EstabelecimentoProfissionalServicoLista
                                                                                               .Any(g => categoriasServicoSelecionadas
                                                                                                             .Contains(g.ServicoEstabelecimento.ServicoCategoriaEstabelecimento)))
                                                                                  .Select(f => f.Profissional);

            retorno = retorno.Where(f => profissionaisDasCategorias.Contains(f.Profissional));

            return retorno;
        }

        private static IQueryable<Horario> FiltroCliente(string valorCampoFiltroCliente, int tipoDeBuscaCampoCliente,
            IQueryable<Horario> retorno, int idEstabelecimento)
        {
            switch (tipoDeBuscaCampoCliente)
            {
                case (int)TipoBuscaClienteEnum.Nome:
                    return FiltroNomeCliente(valorCampoFiltroCliente, retorno);

                case (int)TipoBuscaClienteEnum.Cpf:
                    return FiltroCpfCliente(valorCampoFiltroCliente, retorno);

                case (int)TipoBuscaClienteEnum.Telefone:
                    return FiltroTelefoneCliente(valorCampoFiltroCliente, retorno, idEstabelecimento);

                case (int)TipoBuscaClienteEnum.Email:
                    return FiltroEmailCliente(valorCampoFiltroCliente, retorno);

                default:
                    return retorno;
            }
        }

        private static IQueryable<Horario> FiltroCliente(int idCliente, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.Cliente.IdCliente == idCliente);
            return retorno;
        }

        private static IQueryable<Horario> FiltroClienteComTelefoneCelular(IQueryable<Horario> query)
        {
            return query.Where(f => f.Cliente.PessoaFisica.Telefones.Any(g => g.Ativo &&
                                                                              (g.Numero.StartsWith("9") ||
                                                                               g.Numero.StartsWith("8") ||
                                                                               g.Numero.StartsWith("7"))
                ));
        }

        private static IQueryable<Horario> FiltroClienteEstabelecimento(int idClienteEstabelecimento,
            IQueryable<Horario> retorno)
        {
            return retorno.Where(f => f.ClienteEstabelecimento.Codigo == idClienteEstabelecimento);
        }

        private static IQueryable<Horario> FiltroCpfCliente(string valorCampoFiltroCliente, IQueryable<Horario> retorno)
        {
            return
                retorno.Where(
                    f => f.Cliente.PessoaFisica.Cpf == valorCampoFiltroCliente.Replace(".", "").Replace("-", ""));
        }

        private static IQueryable<Horario> FiltroDataFim(DateTime data, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.DataInicio < data.Date.AddDays(1));
            return retorno;
        }

        private static IQueryable<Horario> FiltroDataInicio(DateTime data, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.DataInicio >= data.Date);
            return retorno;
        }

        private static IQueryable<Horario> FiltroQueSejaUltimoAgendamentoDaRecorrencia(IQueryable<Horario> retorno)
        {
            var horarios = Domain.Pessoas.HorarioRepository.Queryable();
            retorno = retorno.Where(f => f.RecorrenciaHorario != null && horarios.Where(h => h.RecorrenciaHorario == f.RecorrenciaHorario && h.Estabelecimento == f.Estabelecimento).Max(h => h.DataFim) == f.DataFim);
            return retorno;
        }

        private static IQueryable<Horario> FiltroDataInicioMaiorQueAData(DateTime data, IQueryable<Horario> retorno)
        {
            return retorno.Where(f => f.DataInicio > data);
        }

        private static IQueryable<Horario> FiltroEmailCliente(string valorCampoFiltroCliente,
            IQueryable<Horario> retorno)
        {
            return
                retorno.Where(
                    f =>
                        (f.ClienteEstabelecimento.Cliente.PessoaFisica.Email == valorCampoFiltroCliente.ToLower()) ||
                        f.Cliente.PessoaFisica.Contas.Any(
                            g => g.Email == valorCampoFiltroCliente.ToLower()));
        }

        private static IQueryable<Horario> FiltroEstabelecimento(int idEstabelecimento, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            return retorno;
        }

        private static IQueryable<Horario> FiltroEstabelecimentoCliente(int idEstabelecimentoCliente,
            IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.ClienteEstabelecimento.Codigo == idEstabelecimentoCliente);
            return retorno;
        }

        private static IQueryable<Horario> FiltroExcetoStatus(List<StatusHorario> statusSelecionados,
            IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => !statusSelecionados.Contains(f.Status));
            return retorno;
        }

        private static IQueryable<Horario> FiltroNaoPagas(IQueryable<Horario> retorno)
        {
            return retorno.Where(f => !f.FoiPago);
        }

        private static IQueryable<Horario> FiltroNaoPagasAntecipadamente(IQueryable<Horario> retorno)
        {
            return retorno.Where(f => !f.FoiPagoAntecipadamente);
        }

        private static IQueryable<Horario> FiltroNomeCliente(string valorCampoFiltroCliente, IQueryable<Horario> retorno)
        {
            return
                retorno.Where(
                    f => f.Cliente.PessoaFisica.NomeCompleto.ToLower().Contains(valorCampoFiltroCliente.ToLower()));
        }

        private static IQueryable<Horario> FiltroPagas(IQueryable<Horario> retorno)
        {
            return retorno.Where(f => f.FoiPago);
        }

        private static IQueryable<Horario> FiltroPendenteDeEnvioNotificacao(IQueryable<Horario> query)
        {
            query = query.Where(f => f.DataHoraNotificacaoClienteProgramada != null);
            query = query.Where(f => f.Profissional != null);
            query = query.Where(f => f.DataHoraNotificacaoClienteProgramada <= Calendario.Agora());
            query = query.Where(f => f.DataHoraNotificacaoClienteEnviada == null);
            query = query.Where(f => f.DataDeEmissaoFilaParaEnvio == null);
            query = query.Where(f => f.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnvioDeNotificacaoHabilitado);
            query = query.Where(f => f.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarNotificacaoParaClientes);
            query =
                query.Where(
                    f =>
                        (f.ClienteEstabelecimento.RecebeNotificacao == null ||
                         f.ClienteEstabelecimento.RecebeNotificacao == true));
            return query;
        }

        private static IQueryable<Horario> FiltroPendenteDeEnvioNotificacaoNaData(IQueryable<Horario> query, DateTime dataNotificacao, DateTime dataInicio)
        {
            query = query.Where(f => f.DataHoraNotificacaoClienteProgramada != null);
            query = query.Where(f => f.Profissional != null);
            query = query.Where(f => f.DataHoraNotificacaoClienteProgramada >= dataNotificacao.Date &&
                                     f.DataInicio >= dataInicio.Date && f.DataInicio < dataInicio.Date.AddDays(1));
            query = query.Where(f => f.DataHoraNotificacaoClienteEnviada == null);
            query = query.Where(f => f.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnvioDeNotificacaoHabilitado);
            query = query.Where(f => f.Estabelecimento.EstabelecimentoConfiguracaoGeral.EnviarNotificacaoParaClientes);
            query =
                query.Where(
                    f =>
                        (f.ClienteEstabelecimento.RecebeNotificacao == null ||
                         f.ClienteEstabelecimento.RecebeNotificacao == true));
            return query;
        }

        private static IQueryable<Horario> FiltroProfissionais(List<int> profissionaisSelecionados,
            IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => profissionaisSelecionados.Contains(f.Profissional.IdProfissional));
            return retorno;
        }

        private static IQueryable<Horario> FiltroAssistenteOuProfissional(List<int> profissionaisSelecionados,
            IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => profissionaisSelecionados.Contains(f.Profissional.IdProfissional) ||
                                         profissionaisSelecionados.Contains(
                                             f.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional));
            return retorno;
        }

        private IQueryable<Horario> FiltroAssistenteOuProfissionalESemAssistenteParaHorario(List<int> profissionaisSelecionados
            , IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => profissionaisSelecionados.Contains(f.Profissional.IdProfissional) ||
                                         profissionaisSelecionados.Contains(
                                             f.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional) ||
            f.EstabelecimentoProfissionalAssistente == null);
            return retorno;
        }

        private static IQueryable<Horario> FiltroProfissionais(int idProfissional, IQueryable<Horario> retorno, bool incluiComoAssistente = true)
        {
            if (incluiComoAssistente)
            {
                var idsEstabelecimentosDoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                    .Where(f => f.Profissional.IdProfissional == idProfissional)
                    .Select(f => f.Estabelecimento.IdEstabelecimento);

                retorno = retorno.Where(f => idsEstabelecimentosDoProfissional.Contains(f.Estabelecimento.IdEstabelecimento) &&
                    (f.Profissional.IdProfissional == idProfissional
                    || f.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional));
            }
            else
                retorno = retorno.Where(f => f.Profissional.IdProfissional == idProfissional);
            return retorno;
        }

        private static IQueryable<Horario> FiltroRecorrencia(int idHorarioRecorrencia, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.RecorrenciaHorario.IdHorarioRecorrencia == idHorarioRecorrencia);
            return retorno;
        }

        private static IQueryable<Horario> FiltroSemStatus(int status, IQueryable<Horario> retorno)
        {
            return retorno.Where(f => f.Status.Codigo != status);
        }

        private static IQueryable<Horario> FiltroServico(int idServicoEstabelecimento, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento);
            return retorno;
        }

        private static IQueryable<Horario> FiltroStatus(List<StatusHorario> statusSelecionados,
            IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => statusSelecionados.Contains(f.Status));
            return retorno;
        }

        private static IQueryable<Horario> FiltroStatus(int statusSelecionado, IQueryable<Horario> retorno)
        {
            retorno = retorno.Where(f => f.Status.Codigo == statusSelecionado);
            return retorno;
        }

        private static IQueryable<Horario> FiltroTelefoneCliente(string valorCampoFiltroCliente,
            IQueryable<Horario> retorno, int idEstabelecimento)
        {
            var idsClientesEstabelecimento = Domain.Pessoas.ItemComboClienteRepository
                .ObterIdsClientesEstabelecimentoPorFiltroTelefone(idEstabelecimento, valorCampoFiltroCliente, true);

            return retorno.Where(f => idsClientesEstabelecimento.Contains(f.ClienteEstabelecimento.Codigo));
        }

        private static IQueryable<Horario> OrdenarPorData(IQueryable<Horario> retorno)
        {
            retorno = retorno.OrderBy(f => f.DataInicio);
            return retorno;
        }

        private IQueryable<Horario> AdicionarCiterioNomeTelefoneOuEmail(IQueryable<Horario> query, string conteudo, int idEstabelecimento)
        {
            if (!conteudo.Contains('@') && !conteudo.Contains('.'))
            {
                if (conteudo.SomenteNumeros().Any())
                {
                    var idsClienteEstabelecimento = Domain.Pessoas.ItemComboClienteRepository
                        .ObterIdsClientesEstabelecimentoPorFiltroTelefone(idEstabelecimento, conteudo);

                    query = query.Where(f => idsClienteEstabelecimento.Contains(f.ClienteEstabelecimento.Codigo));
                }
                else
                    query = query.Where(p => p.Cliente.PessoaFisica.NomeCompleto.Contains(conteudo));
            }

            query = query.Where(p => p.Cliente.PessoaFisica.Email == conteudo.ToLower());

            return query;
        }

        private IQueryable<Horario> AdicionarCriterioExisteEmailContendo(IQueryable<Horario> query, String email)
        {
            return query.Where(
                p => p.ClienteEstabelecimento.Cliente.PessoaFisica.Email == email.ToLower() ||
                     p.Cliente.PessoaFisica.Contas.Any(
                        c => c.Email == email.ToLower() &&
                             c.Pessoa.IdPessoa == p.Cliente.PessoaFisica.IdPessoa
                )
            );
        }

        private IQueryable<Horario> AdicionarCriterioTelefone(Int32 idEstabelecimento, String telefone,
            IQueryable<Horario> query)
        {
            var idsClientesEstabelecimento = Domain.Pessoas.ItemComboClienteRepository
                .ObterIdsClientesEstabelecimentoPorFiltroTelefone(idEstabelecimento, telefone);

            return query.Where(f => idsClientesEstabelecimento.Contains(f.ClienteEstabelecimento.Codigo));
        }

        private IQueryable<Horario> AdicionarFiltroPorCliente(IQueryable<Horario> query,
            ParametrosFiltroCliente parametros)
        {
            if (!string.IsNullOrEmpty(parametros.ConteudoFiltro))
            {
                if (parametros.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.Nome)
                    query = AdicionarCriterioNome(parametros.ConteudoFiltro, query);
                else if (parametros.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.CPF)
                    query = AdicionarCriterioCpf(parametros.ConteudoFiltro, query);
                else if (parametros.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.Telefone)
                    query = AdicionarCriterioTelefone(parametros.CodigoEstabelecimento, parametros.ConteudoFiltro, query);
                else if (parametros.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.Email)
                    query = AdicionarCriterioExisteEmailContendo(query, parametros.ConteudoFiltro);
                else if (parametros.FiltroPor == ParametrosFiltroCliente.FiltroPorEnum.NomeTelefoneOuEmail)
                    query = AdicionarCiterioNomeTelefoneOuEmail(query, parametros.ConteudoFiltro, parametros.CodigoEstabelecimento);
            }

            return query;
        }

        private IQueryable<Horario> FiltroAgendadoPor(List<int> agendadoPor, IQueryable<Horario> retorno)
        {
            if (agendadoPor == null || agendadoPor[0] <= 0)
                return retorno;


            return retorno.Where(f => agendadoPor.Contains(f.HorarioOrigem.IdHorarioOrigem));
        }

        private IQueryable<Horario> FiltroPessoaLogado(int idPessoaLogada, IQueryable<Horario> retorno)
        {
            var idsEstabelecimentos =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .Where(f => f.PessoaFisica.IdPessoa == idPessoaLogada && f.Ativo == true)
                    .Take(3).Select(f => f.Estabelecimento.IdEstabelecimento).ToList();

            retorno = retorno.Where(f => idsEstabelecimentos.Contains(f.Estabelecimento.IdEstabelecimento));
            return retorno;
        }

        #endregion Filtros Queryable

        #region Critérios

        private static void AdicionarCriterioAtivo(DetachedCriteria dc)
        {
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.Horario.Ativo, true));
        }

        private static void AdicionarCriterioClienteDiferente(int idCliente, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Not(
                    Restrictions.Eq(
                        PropertyNames.Pessoas.Horario.Cliente + "." + PropertyNames.Pessoas.Cliente.IdCliente, idCliente)));
        }

        private static void AdicionarCriterioEstabelecimento(ParametrosFiltrosRelatorio parametros, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.Horario.Estabelecimento + "." +
                    PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento, parametros.IdEstabelecimento));
        }

        private static void AdicionarCriterioNaoCancelado(DetachedCriteria dc)
        {
            var status = new StatusHorario((int)StatusHorarioEnum.Cancelado);
            dc.Add(Restrictions.Not(Restrictions.Eq(PropertyNames.Pessoas.Horario.Status, status)));
        }

        private static void AdicionarCriterioNaoPossuiCodigoHorario(int? codigoHorario, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Not(Restrictions.Eq(PropertyNames.Pessoas.Horario.Codigo, codigoHorario)));
        }

        private static void AdicionarCriterioPeriodo(ParametrosFiltrosRelatorio parametros, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Ge(PropertyNames.Pessoas.Horario.DataInicio, parametros.DataInicial));
            dc.Add(Restrictions.Le(PropertyNames.Pessoas.Horario.DataInicio, parametros.DataFinal));
        }

        private static void AdicionarCriterioProfissionais(ParametrosFiltrosRelatorio parametros, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.Horario.Profissional + "." + PropertyNames.Pessoas.Profissional.IdProfissional,
                    parametros.IdProfissional));
        }

        private static void AdicionarOrdernacaoPorData(DetachedCriteria dc)
        {
            dc.AddOrder(new Order(PropertyNames.Pessoas.Horario.DataInicio, true));
        }

        private void AdicionarCriterioDataFimMaiorQue(DateTime data, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Gt(PropertyNames.Pessoas.Horario.DataFim, data));
        }

        private void AdicionarCriterioDataInicioMenorQue(DateTime data, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Lt(PropertyNames.Pessoas.Horario.DataInicio, data));
        }

        private void AdicionarCriterioIdEstabelecimento(int idEstabelecimento, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.Horario.Estabelecimento + "." +
                    PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento, idEstabelecimento));
        }

        private void AdicionarCriterioIdProfissional(int idProfissional, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.Horario.Profissional + "." + PropertyNames.Pessoas.Profissional.IdProfissional,
                    idProfissional));
        }

        #endregion Critérios

        public IQueryable<Horario> FiltrarParaPATMobile(FiltroPATMobile filtro)
        {
            var queryableHorarios =
                        Domain.Pessoas.HorarioRepository.Queryable()
                            .Where(p => (p.DataInicio >= filtro.DataSelecionada.Date && p.DataInicio <= filtro.DataSelecionada.AddDays(1).Date) &&
                                        p.Estabelecimento.IdEstabelecimento == filtro.Estabelecimento.IdEstabelecimento);

            if (filtro.ListarApenasEmAtendimento)
                queryableHorarios = queryableHorarios.Where(p => p.Status == StatusHorarioEnum.Em_Andamento);

            return queryableHorarios;
        }

        //TRINKS-8591
        public int CountEstabelecimentoTotalDeHorariosCriadosAtravesDoSiteDaFranquiaNoDia(HorarioOrigemEnum horarioOrigemEnum, DateTime diaPesquisa)
        {
            return Queryable().Where(x => x.HorarioOrigem == horarioOrigemEnum && x.DataHoraCriacao >= diaPesquisa.Date && x.DataHoraCriacao < diaPesquisa.Date.AddDays(1)).Count();
        }

        public List<Horario> ListarTodosDoDia(int idEstabelecimento, DateTime data)
        {
            var filtrados = Queryable().Where(h => h.Estabelecimento.IdEstabelecimento.Equals(idEstabelecimento)
                                          && h.DataInicio >= data.Date
                                          && h.DataInicio < data.Date.AddDays(1));

            return filtrados.ToList();
        }

        public bool HorarioPodeSerAssociadoEmUmaComanda(int idHorario, int idEstabelecimento, out int? numeroComandaAssociar)
        {
            numeroComandaAssociar = null;

            var configuracaoGeral = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.ObterPorIdEstabelecimento(idEstabelecimento);
            if (!configuracaoGeral.ComandaEstaAtiva)
                return false;

            var horario = Load(idHorario, false);

            var hoje = Calendario.Hoje();
            if (horario == null || horario.Id != idHorario || horario.FoiPago || horario.DataInicio < hoje || horario.DataInicio >= hoje.AddDays(1)
                || horario.Status == StatusHorarioEnum.Cliente_Faltou || horario.Status == StatusHorarioEnum.Cancelado)
            {
                return false;
            }

            var idPessoaCliente = horario.Cliente.PessoaFisica.IdPessoa;

            if (!Domain.Vendas.PreVendaServicoRepository.VerificarSeHorarioPossuiComandaAssociada(idHorario))
            {
                numeroComandaAssociar = Domain.Vendas.ComandaRepository.ObterNumeroDeComandaAbertaPorClienteNaData(idEstabelecimento, idPessoaCliente, hoje);
                return true;
            }

            return false;
        }

        public DadosDoHorarioParaTelaDeLembreteDTO ObterDadosDoClientePeloHorario(int idHorario)
        {
            return Queryable()
                    .Where(h => h.Id == idHorario)
                    .Select(h => new DadosDoHorarioParaTelaDeLembreteDTO
                    {
                        IdCliente = h.Cliente.IdCliente,
                        IdEstabelecimento = h.Estabelecimento.IdEstabelecimento,
                        DataInicio = h.DataInicio
                    })
                    .FirstOrDefault();
        }

        public IQueryable<Horario> ObterAgendamentosDoDiaPorIdClienteEIdEstabelecimento(DateTime dataAtual, int idEstabelecimento, int idCliente)
        {
            return Queryable().Where(h => (h.DataInicio >= dataAtual.Date && h.DataInicio < dataAtual.AddDays(1).Date) && h.Estabelecimento.IdEstabelecimento == idEstabelecimento && h.Cliente.IdCliente == idCliente);
        }

        public TipoClienteEnum ObterTipoDeClienteDoHorario(int idAgendamento)
        {
            var query = Queryable().Where(h => h.Id == idAgendamento).Select(h => h.Cliente.TipoCliente).FirstOrDefault();
            return query;
        }

        public IQueryable<Horario> ObterHorariosDoDiaEmQuePodeSerAssistente(DateTime dataAtual, Estabelecimento estabelecimento)
        {
            var horario = Queryable();
            var estabProfiServico = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable();
            return from h in horario
                   join eps in estabProfiServico on new { h.Estabelecimento, h.Profissional, h.ServicoEstabelecimento } equals new { eps.EstabelecimentoProfissional.Estabelecimento, eps.EstabelecimentoProfissional.Profissional, eps.ServicoEstabelecimento }
                   where //h.Profissional == eps.EstabelecimentoProfissional.Profissional
                         //&& h.Estabelecimento == eps.EstabelecimentoProfissional.Estabelecimento
                   eps.ServicoExecutadoComAssistente
                   && h.EstabelecimentoProfissionalAssistente == null
                   && (h.DataInicio >= dataAtual.Date && h.DataFim < dataAtual.Date.AddDays(1))
                   && h.Ativo
                   && !h.FoiPago
                   && (h.Status != StatusHorarioEnum.Finalizado && h.Status != StatusHorarioEnum.Cancelado)
                   && h.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento
                   && eps.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento
                   select h;
        }

        public IQueryable<Horario> ObterQueryHorarioPorId(int idHorario)
        {
            return Queryable().Where(h => h.Id == idHorario);
        }

        public int ObterIdServicoEstabelecimentoDoHorarioPorId(int idHorario)
        {
            return Queryable().Where(h => h.Id == idHorario).Select(h => h.ServicoEstabelecimento.IdServicoEstabelecimento).FirstOrDefault();
        }

        public bool HorarioEstaDisponivelParaAssociarAssistente(int idHorario, int idEstabelecimentoProfissional)
        {
            var horario = Queryable();
            var estabProfiServico = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable();
            return (from h in horario
                    join eps in estabProfiServico on new { h.Estabelecimento, h.Profissional, h.ServicoEstabelecimento } equals new { eps.EstabelecimentoProfissional.Estabelecimento, eps.EstabelecimentoProfissional.Profissional, eps.ServicoEstabelecimento }
                    where
                    h.Id == idHorario
                    && eps.ServicoExecutadoComAssistente
                    && h.EstabelecimentoProfissionalAssistente == null
                    && h.Ativo
                    && !h.FoiPago
                    && (h.Status != StatusHorarioEnum.Finalizado && h.Status != StatusHorarioEnum.Cancelado)
                    select h.Id).Any();
        }

        public IQueryable<Horario> ObterFiltroDeHorariosQueAssistentePodeParticipar(EstabelecimentoProfissional assistente)
        {
            var horarios = Queryable();
            var estabelecimentoProfissionalServicos = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable();
            var estabelecimentoAssistenteServicos = Domain.Pessoas.EstabelecimentoAssistenteServicoRepository.Queryable();

            var res = from h in horarios
                      join eas in estabelecimentoAssistenteServicos on new { h.Estabelecimento, h.ServicoEstabelecimento }
                                                   equals new { eas.EstabelecimentoProfissional.Estabelecimento, eas.ServicoEstabelecimento }

                      where h.EstabelecimentoProfissionalAssistente == null
                          && h.Ativo
                          && !h.FoiPago
                          && h.Status != StatusHorarioEnum.Finalizado
                          && h.Status != StatusHorarioEnum.Cancelado
                          && h.Estabelecimento.IdEstabelecimento == assistente.Estabelecimento.IdEstabelecimento

                          && eas.EstabelecimentoProfissional.Codigo == assistente.Codigo
                          && eas.Ativo

                          && h.Estabelecimento.IdEstabelecimento == assistente.Estabelecimento.IdEstabelecimento
                          && eas.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == assistente.Estabelecimento.IdEstabelecimento

                          && estabelecimentoProfissionalServicos.Any(f => f.ServicoExecutadoComAssistente == true
                               && f.EstabelecimentoProfissional.Estabelecimento == h.Estabelecimento
                               && f.EstabelecimentoProfissional.Profissional == h.Profissional
                               && f.ServicoEstabelecimento == h.ServicoEstabelecimento

                               && f.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == assistente.Estabelecimento.IdEstabelecimento

                             )
                      select h;
            return res;
        }

        public IQueryable<Horario> ObterAgendamentosDaData(DateTime dataDoAgendamento)
        {
            return Queryable().Where(h => h.DataInicio >= dataDoAgendamento.Date
                                          && h.DataInicio < dataDoAgendamento.Date.AddDays(1));
        }

        public int TotalDeHorariosDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(x => x.Estabelecimento.IdEstabelecimento == idEstabelecimento).Count();
        }

        public Horario ObterHorarioParaAssociacaoRapidaDeComanda(DateTime data, ClienteEstabelecimento clienteEstabeleciemnto, Profissional profissional, ServicoEstabelecimento servicoEstabelecimento, int idEstabelecimento, Comanda comanda)
        {
            Horario horario = null;
            var horarioNaComanda = Domain.Vendas.PreVendaServicoRepository.Queryable()
                .Where(pvs => pvs.Comanda == comanda && pvs.Horario.Status != StatusHorarioEnum.Finalizado
                && pvs.PreVendaStatus == StatusPreVendaEnum.EmAberto && pvs.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Where(pvs => pvs.Horario.DataInicio >= data.Date
                             && pvs.Horario.DataInicio < data.Date.AddDays(1)
                             && pvs.Horario.Estabelecimento == clienteEstabeleciemnto.Estabelecimento
                             && pvs.Horario.ClienteEstabelecimento == clienteEstabeleciemnto
                             && pvs.Horario.Cliente == clienteEstabeleciemnto.Cliente
                             && pvs.Horario.Profissional == profissional
                             && pvs.Horario.ServicoEstabelecimento == servicoEstabelecimento
                             && !pvs.Horario.FoiPago
                             && (pvs.Horario.Status != StatusHorarioEnum.Cliente_Faltou
                                && pvs.Horario.Status != StatusHorarioEnum.Cancelado
                                && pvs.Horario.Status != StatusHorarioEnum.Finalizado
                                )
                             )
                .Select(pvs => pvs.Horario).FirstOrDefault();

            if (horarioNaComanda != null)
                horario = horarioNaComanda;
            else
            {
                var comandaFinalizadaDoHorario = Domain.Vendas.PreVendaServicoRepository.Queryable()
                .Where(pvs => pvs.Comanda != null && pvs.PreVendaStatus == StatusPreVendaEnum.EmAberto && pvs.Estabelecimento.IdEstabelecimento == idEstabelecimento);

                horario = Queryable().Where(h => h.DataInicio >= data.Date
                                 && h.DataInicio < data.Date.AddDays(1)
                                 && h.Estabelecimento == clienteEstabeleciemnto.Estabelecimento
                                 && h.ClienteEstabelecimento == clienteEstabeleciemnto
                                 && h.Cliente == clienteEstabeleciemnto.Cliente
                                 && h.Profissional == profissional
                                 && h.ServicoEstabelecimento == servicoEstabelecimento
                                 && !h.FoiPago
                                 && (h.Status != StatusHorarioEnum.Cliente_Faltou
                                    && h.Status != StatusHorarioEnum.Cancelado)
                                 && !comandaFinalizadaDoHorario.Where(c => c.IdHorario == h.Id).Any()).FirstOrDefault();
            }
            return horario;
        }

        public ServicoDoEstabelecimentoDTO ObterUltimoAgendamentoPagoDoCliente(int idCliente, int? idFranquia)
        {
            var query = ObterQueryDeAgendamentosPagosDoCliente(idCliente)
                .Where(h => h.Estabelecimento.PessoaJuridica.Ativo && h.Estabelecimento.PesoBuscaPortal <= 30);

            if (idFranquia.HasValue)
            {
                query = query.Where(h => h.Estabelecimento.FranquiaEstabelecimento.Franquia.Id == idFranquia.Value);
            }

            var ultimoAgendamento = query
                .Select(h => new
                {
                    IdServicoEstabelecimento = h.ServicoEstabelecimento.IdServicoEstabelecimento,
                    IdEstabelecimento = h.Estabelecimento.IdEstabelecimento,
                    NomeEstabelecimento = h.Estabelecimento.NomeDeExibicaoNoPortal,
                    NomeServico = h.ServicoEstabelecimento.Nome,
                    IdProfissional = h.Profissional.IdProfissional,
                    IdPessoa = h.Profissional.PessoaFisica.IdPessoa
                })
                .FirstOrDefault();

            if (ultimoAgendamento != null)
            {
                var pessoaFisicaProfissional = Domain.Pessoas.PessoaFisicaRepository
                    .StatelessQueryable()
                    .FirstOrDefault(x => x.IdPessoa == ultimoAgendamento.IdPessoa);

                var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository
                    .StatelessQueryable()
                    .FirstOrDefault(x =>
                        x.Estabelecimento.IdEstabelecimento == ultimoAgendamento.IdEstabelecimento &&
                        x.Profissional.IdProfissional == ultimoAgendamento.IdProfissional &&
                        x.Ativo
                    );

                if (pessoaFisicaProfissional == null || estabelecimentoProfissional == null)
                    return null;

                var dadosDoAgendamento = new ServicoDoEstabelecimentoDTO()
                {
                    IdServicoEstabelecimento = ultimoAgendamento.IdServicoEstabelecimento,
                    IdEstabelecimento = ultimoAgendamento.IdEstabelecimento,
                    NomeEstabelecimento = ultimoAgendamento.NomeEstabelecimento,
                    NomeServico = ultimoAgendamento.NomeServico,
                    IdProfissionalEstabelecimento = estabelecimentoProfissional.Codigo,
                    NomeProfissional = pessoaFisicaProfissional.NomeOuApelido()
                };

                if (dadosDoAgendamento != null)
                {
                    var queryableEstabelecimentos = Domain.Pessoas.EstabelecimentoRepository.StatelessQueryable();
                    var queryableEnderecos = Domain.Pessoas.EnderecoRepository.StatelessQueryable();
                    var queryableHotsite = Domain.Pessoas.HotsiteEstabelecimentoRepository.StatelessQueryable();

                    var hotSite = (from est in queryableEstabelecimentos
                                   join hot in queryableHotsite on est.IdEstabelecimento equals hot.Estabelecimento.IdEstabelecimento
                                   where est.IdEstabelecimento == dadosDoAgendamento.IdEstabelecimento
                                   select new
                                   {
                                       PermiteAgendamentoHotsite = hot.PermiteAgendamentoHotsite,
                                       PermiteBuscaHotsite = hot.PermiteBuscaHotsite
                                   })
                                   .FirstOrDefault();

                    if (hotSite != null)
                    {
                        dadosDoAgendamento.PermiteAgendamentoHotsite = hotSite.PermiteAgendamentoHotsite;
                        dadosDoAgendamento.PermiteBuscaHotsite = hotSite.PermiteBuscaHotsite;
                    }

                    var endereco = (from est in queryableEstabelecimentos
                                    join end in queryableEnderecos on est.PessoaJuridica.IdPessoa equals end.Pessoa.IdPessoa
                                    where est.IdEstabelecimento == dadosDoAgendamento.IdEstabelecimento && end.Ativo
                                    select new EnderecoEstabelecimentoDTO()
                                    {
                                        TipoLogradouro = end.TipoLogradouro.Nome,
                                        Logradouro = end.Logradouro,
                                        Numero = end.Numero,
                                        Complemento = end.Complemento,
                                        Bairro = end.Bairro,
                                        Cidade = end.Cidade,
                                        SiglaUF = end.UF.Sigla
                                    })
                                    .FirstOrDefault();

                    if (endereco != null)
                        dadosDoAgendamento.Endereco = endereco;
                }

                return dadosDoAgendamento;
            }
            else
            {
                return null;
            }
        }

        private IQueryable<Horario> ObterQueryDeAgendamentosPagosDoCliente(int idCliente)
        {
            return Queryable()
                .FiltrarAgendamentosFinalizadosOuConfirmados()
                .FiltrarPorIdCliente(idCliente)
                .Where(x => x.ServicoEstabelecimento.Ativo && x.Profissional != null && x.ServicoEstabelecimento.ServicoIndiposnivelParaCliente == false)
                .OrderByDescending(h => h.DataInicio);
        }

        public Horario ObterPorId(int idHorario)
        {
            return Queryable().FirstOrDefault(h => h.Id == idHorario);
        }

        public List<UltimoServicoRealizadoDTO> ListarUltimosServicosRealizadosNosEstabelecimentos(int idCliente, List<int> idsEstabelecimentos)
        {
            var queryableHorario = ObterQueryDeAgendamentosPagosDoCliente(idCliente)
                            .Where(est => est.Estabelecimento.PessoaJuridica.Ativo && est.Estabelecimento.PesoBuscaPortal <= 30);

            var listaParcial = queryableHorario
                .Select(h => new
                {
                    IdEstabelecimento = h.Estabelecimento.IdEstabelecimento,
                    Id = h.ServicoEstabelecimento.IdServicoEstabelecimento,
                    Nome = h.ServicoEstabelecimento.Nome,
                    IdProfissional = h.Profissional.IdProfissional,
                    NomeProfissional = h.Profissional.PessoaFisica.NomeOuApelido(),
                    DataHora = h.DataInicio,
                    DuracaoEmMinutos = h.ServicoEstabelecimento.Duracao
                })
                .ToList();

            var retorno = new List<UltimoServicoRealizadoDTO>();

            foreach (var agendamento in listaParcial)
            {
                var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository
                        .StatelessQueryable()
                        .FirstOrDefault(x =>
                            x.Estabelecimento.IdEstabelecimento == agendamento.IdEstabelecimento &&
                            x.Profissional.IdProfissional == agendamento.IdProfissional &&
                            x.Ativo
                        );

                if (estabelecimentoProfissional != null)
                {
                    retorno.Add(new UltimoServicoRealizadoDTO()
                    {
                        IdEstabelecimento = agendamento.IdEstabelecimento,
                        DataHora = agendamento.DataHora,
                        Id = agendamento.Id,
                        IdProfissionalEstabelecimento = estabelecimentoProfissional.Codigo,
                        Nome = agendamento.Nome,
                        NomeProfissional = agendamento.NomeProfissional,
                        DuracaoEmMinutos = agendamento.DuracaoEmMinutos
                    });
                }
            }

            return retorno;
        }

        public int? ObterIdClienteDoHorario(int idHorario)
        {
            var horario = Load(idHorario, false);
            if (horario != null && horario.Ativo)
            {
                return horario.Cliente?.IdCliente;
            }
            return null;
        }

        public HorarioOrigemEnum ObterOrigemDoHorarioAgendado(int idHorario)
        {
            var horario = Load(idHorario, false);
            if (horario != null && horario.Ativo)
            {
                return horario.HorarioOrigem;
            }
            return HorarioOrigemEnum.Balcao;
        }

        public IQueryable<Horario> Listar(ParametrosBusca parametros)
        {
            var retorno = Queryable();

            if (parametros.Estabelecimentos != null && parametros.Estabelecimentos.Any())
                retorno = retorno.Where(f => parametros.Estabelecimentos.Contains(f.Estabelecimento.IdEstabelecimento));
            else
                retorno = retorno.Where(f => f.Estabelecimento.PessoaJuridica.Ativo);

            if (parametros.Profissionais.Any())
                retorno = retorno.Where(f => parametros.Profissionais.Contains(f.Profissional.IdProfissional));

            if (parametros.DataInicial.HasValue)
                retorno = retorno.Where(f => f.DataInicio >= parametros.DataInicial);
            if (parametros.DataFinal.HasValue)
                retorno = retorno.Where(f => f.DataInicio <= parametros.DataFinal);

            return retorno;
        }

        public DadosDoControleDeAlteracaoDoHorarioDTO ObterDadosVerificadosNoControleDeAlteracaoDeUmHorario(int idHorario)
        {
            var dadosDoHorario = Queryable()
                .Where(x => x.Id == idHorario)
                .Select(x => new
                {
                    IdHorario = x.Id,
                    DataInicio = x.DataInicio,
                    Duracao = x.Duracao,
                    IdProfissional = x.Profissional != null ? x.Profissional.IdProfissional : 0,
                    IdEstabelecimento = x.Estabelecimento.IdEstabelecimento,
                    IdServicoEstabelecimento = x.ServicoEstabelecimento.IdServicoEstabelecimento,
                    Status = x.Status.Codigo
                }).Single();

            return new DadosDoControleDeAlteracaoDoHorarioDTO(
                idHorario: dadosDoHorario.IdHorario,
                dataHoraInicio: dadosDoHorario.DataInicio,
                duracao: dadosDoHorario.Duracao,
                idEstabelecimento: dadosDoHorario.IdEstabelecimento,
                idServicoEstabelecimento: dadosDoHorario.IdServicoEstabelecimento,
                status: (StatusHorarioEnum)dadosDoHorario.Status,
                idProfissional: dadosDoHorario.IdProfissional
            );
        }

        public IQueryable<HistoricoNaRedeItemDTO> ListarHistoricoNaRedePaginadosPorDataDecrescente(int idFranquia, string cpf, string email, DateTime dataInicio, DateTime dataFim)
        {
            if (string.IsNullOrWhiteSpace(cpf) && string.IsNullOrWhiteSpace(email))
                throw new Exception("Deve conter cpf ou e-mail no filtro");

            var horarios = Queryable();

            horarios = horarios.Where(f => f.Estabelecimento.FranquiaEstabelecimento.Franquia.Id == idFranquia);
            horarios = horarios.Where(f => f.Estabelecimento.FranquiaEstabelecimento.Ativo);

            if (!string.IsNullOrWhiteSpace(cpf))
                cpf = cpf.SomenteNumeros();

            var idPfComCpf = Domain.Pessoas.PessoaFisicaRepository.Queryable().Where(f => f.Cpf == cpf).Select(f => f.IdPessoa);
            var idPfComEmail = Domain.Pessoas.PessoaFisicaRepository.Queryable().Where(f => f.Email == email).Select(f => f.IdPessoa);

            if (!string.IsNullOrWhiteSpace(cpf) && !string.IsNullOrWhiteSpace(email))
                horarios = horarios.Where(f => idPfComCpf.Contains(f.Cliente.PessoaFisica.IdPessoa) || idPfComEmail.Contains(f.Cliente.PessoaFisica.IdPessoa));
            else if (!string.IsNullOrWhiteSpace(cpf))
                horarios = horarios.Where(f => idPfComCpf.Contains(f.Cliente.PessoaFisica.IdPessoa));
            else
                horarios = horarios.Where(f => idPfComEmail.Contains(f.Cliente.PessoaFisica.IdPessoa));

            horarios = horarios.Where(f => f.DataInicio >= dataInicio);
            horarios = horarios.Where(f => f.DataFim <= dataFim.AddDays(1));

            return horarios.Select(f => new HistoricoNaRedeItemDTO
            {
                Data = f.DataInicio,
                NomeEstabelecimento = f.Estabelecimento.FranquiaEstabelecimento.NomeUnidade,
                NomeServico = f.ServicoEstabelecimento.Nome,
                NomeStatus = f.Status.Nome,
                IdEstabelecimento = f.Estabelecimento.IdEstabelecimento
            });
        }

        public List<Horario> ObterPorIds(List<int> idsHorarios)
        {
            return Queryable().Where(h => idsHorarios.Contains(h.Id)).ToList();
        }

        public bool FoiPagoAntecipadamente(int idHorario)
        {
            bool? pagoAntecipadmente = Queryable().Where(h => h.Id == idHorario).Select(h => (bool?)h.FoiPagoAntecipadamente).FirstOrDefault();
            return pagoAntecipadmente ?? false;
        }

        public List<Horario> ObterApenasHorariosComServicosDisponiveisParaCliente(List<Horario> horarios)
        {
            var horariosDisponiveisParaCliente = horarios.Where(h => h.ServicoEstabelecimento.ServicoIndiposnivelParaCliente == false).ToList();

            return horariosDisponiveisParaCliente;
        }

        public List<Horario> ObterHorarioPorProfissionalEEstabelecimento(int idProfissional, int idEstabelecimento, bool ehServicoComProfissional)
        {
            IQueryable<Horario> horario;
            if (ehServicoComProfissional)
            {
                horario = Queryable().Where(h => h.Ativo && h.Estabelecimento.IdEstabelecimento == idEstabelecimento
                                            && h.Profissional.IdProfissional == idProfissional);
            }
            else
            {
                horario = Queryable().Where(h => h.Ativo && h.Estabelecimento.IdEstabelecimento == idEstabelecimento
                                            && h.Profissional.IdProfissional != idProfissional).OrderByDescending(h => h.DataHoraCriacao);
            }

            return horario.ToList();
        }

        public List<int> ListarIdsDosHorariosQueEstaoFinalizados(int idEstabelecimento, List<int> idsHorariosParaVerificar)
        {
            if (idsHorariosParaVerificar == null || !idsHorariosParaVerificar.Any())
                return new List<int>();

            return Queryable()
                .Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento
                        && idsHorariosParaVerificar.Contains(h.Id)
                        && h.Status.Codigo == (int)StatusHorarioEnum.Finalizado)
                .Select(h => h.Id)
                .ToList();
        }

        public List<Horario> ListarHorariosQueEstaoFinalizados(List<int> idsHorariosParaVerificar)
        {
            return idsHorariosParaVerificar.Select(i => Load(i)).ToList();
        }

        #region Cupom

        public List<int> ObterIdsDosHorariosDisponiveisDoClienteNoDia(int idPessoa, int idEstabelecimento, DateTime data)
        {
            return StatelessQueryable()
                .Where(h => h.PessoaQuemMarcou.IdPessoa == idPessoa
                        && h.Estabelecimento.IdEstabelecimento == idEstabelecimento
                        && h.DataInicio.Date == data.Date
                        && (h.Status.Codigo == (int)StatusHorarioEnum.Aguardando_Confirmacao
                            || h.Status.Codigo == (int)StatusHorarioEnum.Confirmado
                            || h.Status.Codigo == (int)StatusHorarioEnum.Em_Andamento))
                .Select(h => h.Id)
                .ToList();
        }

        #endregion Cupom

        public int ObterUltimoIdHorarioPorIdClienteEIdEstabelecimento(int idCliente, int idEstabelecimento)
        {
            return Queryable().
                Where(h =>
                      h.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                      h.Cliente.IdCliente == idCliente)
                .OrderByDescending(h => h.Id)
                .Select(h => h.Id)
                .FirstOrDefault();
        }

        private IQueryable<Horario> QueryableAgendamentosPendentesNaData(int idEstabelecimento, DateTime data, int idClienteEstabelecimento, bool considerarFinalizados = false)
        {
            var listaStatusConsiderados = new List<int>
            {
                (int)StatusHorarioEnum.Aguardando_Confirmacao,
                (int)StatusHorarioEnum.Confirmado,
                (int)StatusHorarioEnum.Em_Andamento
            };

            var retorno = Queryable()
                .Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Where(h => h.DataInicio >= data.Date && h.DataInicio < data.Date.AddDays(1))
                .Where(h => h.ClienteEstabelecimento.Codigo == idClienteEstabelecimento)
                .Where(h => !h.FoiPago);

            if (considerarFinalizados)
            {
                listaStatusConsiderados.Add((int)StatusHorarioEnum.Finalizado);
            }

            return retorno.Where(h => listaStatusConsiderados.Contains(h.Status.Codigo));
        }

        public bool ExisteAgendamentoPendenteNaoFinalizadoNaData(int idEstabelecimento, DateTime data, int idClienteEstabelecimento)
        {
            return QueryableAgendamentosPendentesNaData(idEstabelecimento, data, idClienteEstabelecimento).Any();
        }

        public List<Horario> AgendamentosAindaPendentesNaData(int idEstabelecimento, DateTime data, int idClienteEstabelecimento)
        {
            return QueryableAgendamentosPendentesNaData(idEstabelecimento, data, idClienteEstabelecimento, true).ToList();
        }
    }
}