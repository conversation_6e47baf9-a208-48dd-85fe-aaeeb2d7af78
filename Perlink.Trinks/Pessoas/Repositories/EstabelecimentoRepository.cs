﻿using Castle.ActiveRecord;
using NHibernate;
using NHibernate.Criterion;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.Despesas;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.NotaFiscalDoConsumidor.Enums;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Relatorios;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class EstabelecimentoRepository : IEstabelecimentoRepository
    {


        #region Métodos Públicos

        // @see http://jira.perlink.net/browse/TRINKS-4478

        public int CountEstabelecimentoCadastrados()
        {
            return Queryable().Count();
        }

        public int CountEstabelecimentoCadastradosNoDia(DateTime dia)
        {
            return Queryable().Count(
                e => e.PessoaJuridica.DataCadastro.HasValue && e.PessoaJuridica.DataCadastro.Value.Date == dia.Date);
        }

        public int CountEstabelecimentoParaEstatisticasDiarias()
        {
            return ObterVisiveisNasEstatisticasDiarias().Count();
        }

        public int CountEstabelecimentoQueAparecemNaBuscaDoPortal()
        {
            return ObterVisiveisNaBusca().Count();
        }

        public int CountEstabelecimentoQueFecharamContaNoDia(DateTime dia)
        {
            return Queryable().Count(
                e => e.Agendamentos.Any(p => p.HorariosTransacoes.Any(p2 => p2.Transacao.DataHora.Date == dia.Date)));
        }

        public int CountEstabelecimentoQuePossuemHorariosNoDia(HorarioOrigemEnum origem, DateTime dia)
        {
            return
                Queryable().Count(e =>
                    e.Agendamentos.Any(h =>
                        h.HorarioOrigem.IdHorarioOrigem == (int)origem
                        && h.Historicos.Min(j => j.DataHoraAlteracao.Date) == dia.Date)
                    );
        }

        public bool EstabelecimentoPossuiCEP(Int32 idEstabelecimento)
        {
            var retorno = Queryable();
            return
                retorno.Any(
                    p => p.IdEstabelecimento == idEstabelecimento && p.PessoaJuridica.Enderecos.Any(e => e.Cep != ""));
        }

        public Boolean ExistemEstabelecimentosAtivosComMesmoNomeFantasia(String nomeFantasia, int idEstabelecimento)
        {
            var retorno = Queryable();

            retorno = FiltroNomeFantasia(nomeFantasia, retorno);
            retorno = FiltroAtivo(retorno);
            retorno = FiltroEstabelecimentoDiferente(idEstabelecimento, retorno);

            return retorno.Any();
        }

        private static IQueryable<Estabelecimento> FiltroEstabelecimentoDiferente(int idEstabelecimento, IQueryable<Estabelecimento> retorno)
        {
            retorno = retorno.Where(p => p.IdEstabelecimento != idEstabelecimento);
            return retorno;
        }

        public Boolean ExistemFotosAssociadasAoEstabelecimento(Int32 idEstabelecimento)
        {
            var retorno = Queryable();
            return retorno.Any(p => p.IdEstabelecimento == idEstabelecimento && p.PessoaJuridica.Fotos.Any());
        }

        public IList<Estabelecimento> FiltrarBusca(ParametrosBuscaEstabelecimento parametros)
        {
            var dc = DetachedCriteria.For<Estabelecimento>("EstabRaiz");
            AdicionarCriterioTutorialConcluido(dc);
            AdicionarAliasPessoaJuridica(dc);
            AdicionarCriterioAtivo(dc);
            AdicionarOrdemRazaoSocial(dc);
            return LoadAll(dc);
        }

        //public IList<Estabelecimento> ListarHabilitadosEnvioEmailDespesasLinq() {
        //    //string sql = @"Select est.*
        //    //            FROM Estabelecimento est
        //    //            INNER JOIN Pessoa p ON p.id_pessoa = est.id_pessoa
        //    //            INNER JOIN COB_Conta_Financeira cf ON cf.id_pessoa = p.id_pessoa
        //    //            left JOIN FINC_Lancamento lanc ON (
        //    //            lanc.id_estabelecimento = est.id_estabelecimento
        //    //            AND dt_pagamento IS NOT null
        //    //            AND dt_vencimento >= GETDATE()
        //    //            AND dt_vencimento <= DATEADD(day, 7, GETDATE())
        //    //            and lanc.ativo = 1)
        //    //            WHERE
        //    //            p.ativo = 1
        //    //            AND cf.id_cob_conta_financeira_status in  (1, 3, 4, 9)
        //    //            AND cf.ativo = 1";
        //    var estabelecimentos = ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual();
        //    //var lancamentos = Domain.Despesas.LancamentoRepository.Queryable();
        //    //lancamentos = lancamentos.Where(l => l.Ativo)
        //    //.Where(l => l.DataPagamento != null
        //    //        && l.DataVencimento >= Calendario.Agora()
        //    //        && l.DataVencimento <= Calendario.Agora().AddDays(7)
        //    //        && l.Ativo);

        //    //var query = from est in estabelecimentos
        //    //            join lanc in lancamentos
        //    //             on est.IdEstabelecimento equals lanc.Estabelecimento.IdEstabelecimento into l
        //    //            from li in l.DefaultIfEmpty()
        //    //            select est;

        //    var query = estabelecimentos
        //        .Fetch(e => e.Lancamentos).ToFuture();

        //    query = query.Where(e => e.Lancamentos.All(l => l.DataPagamento != null
        //            && l.DataVencimento >= Calendario.Agora()
        //            && l.DataVencimento <= Calendario.Agora().AddDays(7)
        //            && l.Ativo));

        //    //query = Queryable()
        //    //    .Fetch(e => e.PessoaJuridica)
        //    //    .Fetch(e => e.EstabelecimentoConfiguracaoGeral)
        //    //    .Fetch(e => e.Lancamentos
        //    //        .Where(l => l.DataPagamento != null
        //    //            && l.DataVencimento >= Calendario.Agora()
        //    //            && l.DataVencimento <= Calendario.Agora().AddDays(7)
        //    //            && l.Ativo)
        //    //    ).Where(e => e.PessoaJuridica.Ativo && e.PessoaJuridica.ContasFinanceiras.Any(f => f.Ativo && (new[] { 1, 3, 4, 9 }).Contains(f.Status.IdStatus)));

        //    //return estabelecimentos.ToList();

        //    //var sql = @"select e.* from Estabelecimentos e";
        //    //var query = Session().CreateQuery(sql).List<Estabelecimento>();
        //    //return query.Take(10).ToList();

        //    return query.Take(5).ToList();
        //}

        public IList<Estabelecimento> ListarHabilitadosEnvioEmailDespesas()
        {
            var dc = DetachedCriteria.For<Estabelecimento>("EstabRaiz");
            //AdicionarCriterioTutorialConcluido(dc);
            AdicionarAliasPessoaJuridica(dc);
            AdicionarCriterioAtivo(dc);
            AdicionarOrdemRazaoSocial(dc);

            var dtEstabelecimentoConfiguracaoGeral = DetachedCriteria.For(typeof(EstabelecimentoConfiguracaoGeral));
            dtEstabelecimentoConfiguracaoGeral.SetProjection(
                Property.ForName(PropertyNames.Pessoas.EstabelecimentoConfiguracaoGeral.IdEstabelecimento));
            dtEstabelecimentoConfiguracaoGeral.Add(
                Property.ForName(PropertyNames.Pessoas.EstabelecimentoConfiguracaoGeral.IdEstabelecimento)
                    .EqProperty("EstabRaiz." + PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento)
                );

            dtEstabelecimentoConfiguracaoGeral.Add(
                Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoConfiguracaoGeral.EnviarEmailDespesasHabilitado, true));

            dc.Add(Subqueries.Exists(dtEstabelecimentoConfiguracaoGeral));

            var dtContaFinanceiral = DetachedCriteria.For(typeof(ContaFinanceira));
            dtContaFinanceiral.SetProjection(
                Property.ForName(PropertyNames.Cobranca.ContaFinanceira.Pessoa));
            dtContaFinanceiral.Add(
                Property.ForName(PropertyNames.Cobranca.ContaFinanceira.Pessoa)
                    .EqProperty("EstabRaiz." + PropertyNames.Pessoas.Estabelecimento.PessoaJuridica)
                );

            dtContaFinanceiral.Add(
                Restrictions.In(PropertyNames.Cobranca.ContaFinanceira.Status + "." + PropertyNames.Cobranca.StatusConta.IdStatus, new[] { 1, 3, 4, 9 }));
            dtContaFinanceiral.Add(
                Restrictions.Eq(PropertyNames.Cobranca.StatusConta.Ativo, true));

            dc.Add(Subqueries.Exists(dtContaFinanceiral));

            var dtEstabelecimentoLancamento = DetachedCriteria.For(typeof(Lancamento));
            dtEstabelecimentoLancamento.SetProjection(
                Property.ForName(PropertyNames.Despesas.Lancamento.Estabelecimento));
            dtEstabelecimentoLancamento.Add(
                Property.ForName(PropertyNames.Despesas.Lancamento.Estabelecimento)
                    .EqProperty("EstabRaiz." + PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento)
                );

            dtEstabelecimentoLancamento.Add(
                Restrictions.Eq(PropertyNames.Despesas.Lancamento.Ativo, true));
            dtEstabelecimentoLancamento.Add(
                Restrictions.Ge(PropertyNames.Despesas.Lancamento.DataVencimento, Calendario.Agora()));
            dtEstabelecimentoLancamento.Add(
                Restrictions.Le(PropertyNames.Despesas.Lancamento.DataVencimento, Calendario.Agora().AddDays(7)));

            dc.Add(Subqueries.Exists(dtEstabelecimentoLancamento));

            return LoadAll(dc);

            //var contas = Domain.Cobranca.ContaFinanceiraRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManual();
            //return retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica)).ToList();
        }

        public List<KeyValuePair<int, string>> ListarEstabelecimentosKeyValuePorConta(Conta conta, bool pegarSomentePrimeiro = false)
        {
            //MODIFICAÇOES PARA TAREFA TRINKS-6259
            var usuarioFranquiaComAcessoAdministrativoNosEstabelecimentos = false;
            if (ContextHelper.Instance.IdFranquia.HasValue && ContextHelper.Instance.IdFranquia > 0)
                usuarioFranquiaComAcessoAdministrativoNosEstabelecimentos = Domain.Pessoas.ContaFranquiaRepository.TemAcessoAoBackOfficeDoEstabelecimento(conta.IdConta, ContextHelper.Instance.IdFranquia.Value);

            //recupera os vinculos com os estabelecimentos

            var estabelecimentosDaConta = ListarEstabelecimentosPorConta(conta, pegarSomentePrimeiro);
            var estabelecimentosDeVinculo = estabelecimentosDaConta.ToList();

            if (usuarioFranquiaComAcessoAdministrativoNosEstabelecimentos)
            {
                var estabelecimentosDaFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository.ListarEstabelecimentosQuePermitemAcessoBackOfficePorOutrosFranqueadores(ContextHelper.Instance.IdFranquia.Value);
                estabelecimentosDeVinculo.AddRange(estabelecimentosDaFranquia);
                estabelecimentosDeVinculo = estabelecimentosDeVinculo.Distinct().ToList();
            }

            //pega o profississional para checar se ele é um profissional ativo dos estabelecimentos retornados
            var profissional = conta.Pessoa.PessoaFisica.Profissional();

            var listaEstabelecimentos = AlteraNomeDeExibicaoQuandoForModelo(estabelecimentosDeVinculo).ToList();

            if (profissional != null && !usuarioFranquiaComAcessoAdministrativoNosEstabelecimentos &&
                profissional.EstabelecimentoProfissionalLista != null && profissional.EstabelecimentoProfissionalLista.Any())
            {
                //pega uma lista de ids de estabelecimento cuja conta é um profissional ativo
                var estabelecimentosComProfisisonalAtivo = profissional.EstabelecimentoProfissionalLista.Where(p => p.Ativo).Select(p => p.Estabelecimento).Select(c => c.IdEstabelecimento).ToList();

                //checa entre as listas se existe estabelecimentos no vinculo onde o profissional esta ativo
                var estabelecimentosParaExibicao = listaEstabelecimentos.Where(e => estabelecimentosComProfisisonalAtivo.Contains(e.Key)).ToList();

                //retorna a lista de keyvalue dos vinculos
                return estabelecimentosParaExibicao;
            }

            if (pegarSomentePrimeiro)
                return listaEstabelecimentos.Take(1).ToList();

            return listaEstabelecimentos.ToList();
        }

        public List<KeyValuePair<int, string>> ListarEstabelecimentosKeyValuePorContaAdministrador(Conta conta, bool pegarSomentePrimeiro = false)
        {
            var estabelecimentosDaConta = ListarEstabelecimentosPorContaAdministrador(conta, pegarSomentePrimeiro);
            var estabelecimentosDeVinculo = estabelecimentosDaConta.ToList();

            var listaEstabelecimentos = AlteraNomeDeExibicaoQuandoForModelo(estabelecimentosDeVinculo).ToList();

            if (pegarSomentePrimeiro)
                return listaEstabelecimentos.Take(1).ToList();

            return listaEstabelecimentos.ToList();
        }

        private static List<KeyValuePair<int, string>> AlteraNomeDeExibicaoQuandoForModelo(List<KeyValuePair<int, string>> estabelecimentosDeVinculo)
        {
            var listaEstabelecimentos = new List<KeyValuePair<int, string>>();

            var listaFranquiaEstabelecimentoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterTodosOsIdsDeModelo();

            foreach (var estabelecimento in estabelecimentosDeVinculo)
            {
                if (listaFranquiaEstabelecimentoModelo.Contains(estabelecimento.Key))
                    listaEstabelecimentos.Add(new KeyValuePair<int, string>(estabelecimento.Key, estabelecimento.Value + " (modelo)"));
                else
                    listaEstabelecimentos.Add(new KeyValuePair<int, string>(estabelecimento.Key, estabelecimento.Value));
            }

            return listaEstabelecimentos;
        }

        public List<Estabelecimento> ListarEstabelecimentosModelos(Franquia franquia)
        {
            var busca = Queryable();
            var estabelecimentos =
                busca.Where(
                    e =>
                        e.FranquiaEstabelecimento.Franquia == franquia &&
                        e.FranquiaEstabelecimento.TipoDeCadastroDeEstabelecimento ==
                        TipoDeCadastroDeEstabelecimentoFranqueado.Modelo).ToList();

            return estabelecimentos;
        }

        public List<EstabelecimentoCarrosselDTO> ListarEstabelecimentosParaCarrocelDoPortal(int limite = 50)
        {
            var estabelecimentos = ObterVisiveisNoCarrossel().OrderBy(p => p.Random).Take(limite);

            var enderecos = Domain.Pessoas.EnderecoRepository.Queryable();
            var hotsites = Domain.Pessoas.HotsiteEstabelecimentoRepository.Queryable();

            var dto = (from f in estabelecimentos
                       join end in enderecos on f.PessoaJuridica.IdPessoa equals end.Pessoa.IdPessoa
                       join hot in hotsites on f equals hot.Estabelecimento
                       select new EstabelecimentoCarrosselDTO
                       {
                           IdEstabelecimento = f.IdEstabelecimento,
                           IdPessoa = f.PessoaJuridica.IdPessoa,
                           NomeFantasia = f.NomeDeExibicaoNoPortal,
                           Bairro = end.Bairro,
                           Cidade = end.Cidade,
                           UrlHotSite = hot.Url,
                           Random = f.Random,
                           //IdEstabelecimentoModelo = f.FranquiaEstabelecimento != null && f.FranquiaEstabelecimento.EstabelecimentoModelo != null ?
                           //     f.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento : 0
                       }).ToList();

            var idsEstabelecimento = dto.Select(g => g.IdEstabelecimento).ToList();
            var idModelos = Domain.Pessoas.FranquiaEstabelecimentoRepository.Queryable()
                .Where(f => idsEstabelecimento.Contains(f.IdEstabelecimento) && f.EstabelecimentoModelo != null)
                .Select(f => new { f.IdEstabelecimento, IdEstabelecimentoModelo = f.EstabelecimentoModelo.IdEstabelecimento })
                .ToList();

            foreach (var d in dto)
            {
                d.IdEstabelecimentoModelo = idModelos.Where(f => f.IdEstabelecimento == d.IdEstabelecimento).Select(f => f.IdEstabelecimentoModelo).FirstOrDefault();
            }

            return dto;
        }

        public IQueryable<Estabelecimento> ListarEstabelecimentosPeloModelo(
            IQueryable<Int32> idEstabelecimentoFranqueador)
        {
            return Queryable()
                .Where(p => p.FranquiaEstabelecimento != null &&
                            p.FranquiaEstabelecimento.EstabelecimentoModelo != null &&
                            idEstabelecimentoFranqueador.Contains(
                                p.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento));
        }

        public IQueryable<Estabelecimento> ListarEstabelecimentosPeloModelo(
            int idEstabelecimentoFranqueador)
        {
            return Queryable()
                .Where(p => p.FranquiaEstabelecimento != null &&
                            p.FranquiaEstabelecimento.EstabelecimentoModelo != null &&
                            p.FranquiaEstabelecimento.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoFranqueador);
        }

        public List<KeyValuePair<int, string>> ListarEstabelecimentosPorConta(Conta conta, bool pegarSomentePrimeiro = false)
        {
            var statusDesejaveisAdmin = (new[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.Adimplente,
                StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.CobrancaManual,
                StatusContaFinanceira.InadimplenteForaTolerancia
            }).Select(f => (int)f).ToList();
            var statusDesejaveisRecepcionista = (new[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.Adimplente,
                StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.CobrancaManual,
                StatusContaFinanceira.InadimplenteForaTolerancia
            }).Select(f => (int)f).ToList();
            var statusDesejaveisDemaisUsuarios = (new[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.Adimplente,
                StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.CobrancaManual
            }).Select(f => (int)f).ToList();

            var statusNegativosAdmin = (new[] {
                StatusContaFinanceira.NaoAssinado,
                StatusContaFinanceira.ContaCancelada,
                StatusContaFinanceira.ContaNaoConfirmada
            }).Select(f => (int)f).ToList();

            var contasFinanceiras = Domain.Cobranca.ContaFinanceiraRepository.Queryable();

            var estabelecimento = new List<KeyValuePair<int, string>>();

            var usuarios = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable();

            var vinculos = (from u in usuarios
                            join cf in contasFinanceiras on u.Estabelecimento.PessoaJuridica equals cf.Pessoa
                            where u.PessoaFisica.IdPessoa == conta.Pessoa.IdPessoa
                                 && u.Ativo
                                 && (u.PerfilAcesso == AcessoBackoffice.SomenteMinhaAgendaEPainelAtendimento || u.UsuarioPerfil != null && u.UsuarioPerfil.Perfil != UsuarioPerfilEnum.SemAcesso && u.UsuarioPerfil.Perfil != UsuarioPerfilEnum.Profissional1)
                            orderby u.Estabelecimento.IdEstabelecimento
                            select new { u.Estabelecimento.IdEstabelecimento, u.Estabelecimento.NomeDeExibicaoNoPortal, u.UsuarioPerfil.Perfil, cf.Status, idPessoaResponsavel = u.Estabelecimento.PessoaJuridica.ResponsavelFinanceiro.IdPessoa });

            var vinculoResponsavel = vinculos.Where(f => statusDesejaveisAdmin.Contains(f.Status.IdStatus) && f.idPessoaResponsavel == conta.Pessoa.IdPessoa);
            estabelecimento.AddRange(vinculoResponsavel.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoAdministrador = vinculos.Where(f => statusDesejaveisAdmin.Contains(f.Status.IdStatus) && f.idPessoaResponsavel != conta.Pessoa.IdPessoa && f.Perfil != null && f.Perfil == UsuarioPerfilEnum.Administrador);
                var listaVinculoAdministrador = vinculoAdministrador.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal));
                estabelecimento.AddRange(listaVinculoAdministrador);
            }

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoRecepcionista = vinculos.Where(f => statusDesejaveisRecepcionista.Contains(f.Status.IdStatus) && f.Perfil != null && f.Perfil == UsuarioPerfilEnum.Recepcionista);
                estabelecimento.AddRange(vinculoRecepcionista.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));
            }

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoProfissional = vinculos.Where(f => statusDesejaveisDemaisUsuarios.Contains(f.Status.IdStatus) && (f.Perfil == null || f.Perfil != UsuarioPerfilEnum.Recepcionista && f.Perfil != UsuarioPerfilEnum.Administrador));
                estabelecimento.AddRange(vinculoProfissional.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));
            }

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoAdministradorNegativo = vinculos.Where(f => statusNegativosAdmin.Contains(f.Status.IdStatus) && (f.Perfil != null && f.Perfil == UsuarioPerfilEnum.Administrador));
                estabelecimento.AddRange(vinculoAdministradorNegativo.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));
            }

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
                estabelecimento.AddRange(ObterEstabelecimentosFranquiados(conta));

            if (pegarSomentePrimeiro)
                return estabelecimento.Take(1).ToList();

            return estabelecimento;
        }

        public List<KeyValuePair<int, string>> ListarEstabelecimentosPorContaAdministrador(Conta conta, bool pegarSomentePrimeiro = false)
        {
            var statusDesejaveisAdmin = (new[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.Adimplente,
                StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.CobrancaManual,
                StatusContaFinanceira.InadimplenteForaTolerancia
            }).Select(f => (int)f).ToList();
            var statusDesejaveisRecepcionista = (new[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.Adimplente,
                StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.CobrancaManual,
                StatusContaFinanceira.InadimplenteForaTolerancia
            }).Select(f => (int)f).ToList();
            var statusDesejaveisDemaisUsuarios = (new[] {
                StatusContaFinanceira.PeriodoGratis,
                StatusContaFinanceira.Adimplente,
                StatusContaFinanceira.InadimplenteEmTolerancia,
                StatusContaFinanceira.CobrancaManual
            }).Select(f => (int)f).ToList();

            var statusNegativosAdmin = (new[] {
                StatusContaFinanceira.NaoAssinado,
                StatusContaFinanceira.ContaCancelada,
                StatusContaFinanceira.ContaNaoConfirmada
            }).Select(f => (int)f).ToList();

            var contasFinanceiras = Domain.Cobranca.ContaFinanceiraRepository.Queryable();

            var estabelecimento = new List<KeyValuePair<int, string>>();

            var usuarios = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable();

            var vinculos = (from u in usuarios
                            join cf in contasFinanceiras on u.Estabelecimento.PessoaJuridica equals cf.Pessoa
                            where u.PessoaFisica.IdPessoa == conta.Pessoa.IdPessoa
                                 && u.Ativo
                                 && u.EhResponsavel
                            orderby u.Estabelecimento.IdEstabelecimento
                            select new { u.Estabelecimento.IdEstabelecimento, u.Estabelecimento.NomeDeExibicaoNoPortal, u.UsuarioPerfil.Perfil, cf.Status, idPessoaResponsavel = u.Estabelecimento.PessoaJuridica.ResponsavelFinanceiro.IdPessoa });

            var vinculoResponsavel = vinculos.Where(f => statusDesejaveisAdmin.Contains(f.Status.IdStatus) && f.idPessoaResponsavel == conta.Pessoa.IdPessoa);
            estabelecimento.AddRange(vinculoResponsavel.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoAdministrador = vinculos.Where(f => statusDesejaveisAdmin.Contains(f.Status.IdStatus) && f.idPessoaResponsavel != conta.Pessoa.IdPessoa && f.Perfil != null && f.Perfil == UsuarioPerfilEnum.Administrador);
                var listaVinculoAdministrador = vinculoAdministrador.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal));
                estabelecimento.AddRange(listaVinculoAdministrador);
            }

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoProfissional = vinculos.Where(f => statusDesejaveisDemaisUsuarios.Contains(f.Status.IdStatus) && (f.Perfil == null || f.Perfil != UsuarioPerfilEnum.Recepcionista && f.Perfil != UsuarioPerfilEnum.Administrador));
                estabelecimento.AddRange(vinculoProfissional.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));
            }

            if (!pegarSomentePrimeiro || !estabelecimento.Any())
            {
                var vinculoAdministradorNegativo = vinculos.Where(f => statusNegativosAdmin.Contains(f.Status.IdStatus) && (f.Perfil != null && f.Perfil == UsuarioPerfilEnum.Administrador));
                estabelecimento.AddRange(vinculoAdministradorNegativo.Select(f => new { f.IdEstabelecimento, f.NomeDeExibicaoNoPortal }).ToList().Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal)));
            }

            if (pegarSomentePrimeiro)
                return estabelecimento.Take(1).ToList();

            return estabelecimento;
        }

        public IQueryable<Estabelecimento> ListarPorProfissional(Int32 idPessoa)
        {
            var estabelecimentosProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarPorPF(idPessoa);
            return estabelecimentosProfissionais.Select(f => f.Estabelecimento);
        }

        public IQueryable<Estabelecimento> ListarPorProfissionalComStatusPositivo(Int32 idPessoa)
        {
            var lista = ListarPorProfissional(idPessoa);

            var statusDesejados = new[]{
                    (int)StatusContaFinanceira.PeriodoGratis, (int)StatusContaFinanceira.Adimplente,
                    (int)StatusContaFinanceira.InadimplenteEmTolerancia, (int)StatusContaFinanceira.CobrancaManual
                };

            lista = lista.Where(
                    e =>
                        e.PessoaJuridica.ContasFinanceiras.Any(
                            cf => cf.Ativo && statusDesejados.Contains(cf.Status.IdStatus)));

            return lista.OrderBy(e => e.NomeDeExibicaoNoPortal);
        }

        // Novo método para listar apenas os IDs dos estabelecimentos por profissional com status positivo entra aqui
        public List<int> ListarIdsEstabelecimentoPorProfissionalComStatusPositivo(int idPessoa)
        {

            var estabelecimentosProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.ListarPorPF(idPessoa).Select(f => f.Estabelecimento);

            var statusDesejados = new[]
            {
                (int)StatusContaFinanceira.PeriodoGratis, (int)StatusContaFinanceira.Adimplente,
                (int)StatusContaFinanceira.InadimplenteEmTolerancia, (int)StatusContaFinanceira.CobrancaManual
            };

            var idsEstabelecimentosComStatusPositivo = estabelecimentosProfissionais
                .Where(e => e.PessoaJuridica.ContasFinanceiras.Any(cf => cf.Ativo && statusDesejados.Contains(cf.Status.IdStatus)))
                .Select(e => e.IdEstabelecimento)
                .ToList();

            return idsEstabelecimentosComStatusPositivo;
        }


        public Estabelecimento ObterEstabelecimentoPrincipalDaConta(Conta conta)
        {
            var idEstabelecimento = 0;

            var estabelecimentosKV = Domain.Pessoas.EstabelecimentoRepository.ListarEstabelecimentosKeyValuePorConta(conta, true).Select(f => f.Key).ToList();
            if (estabelecimentosKV.Any())
                idEstabelecimento = estabelecimentosKV.First();

            return idEstabelecimento == 0 ? null : Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosApenasComIdPreenchidoEComContaFinanceiraAtivaENaoCanceladas()
        {
            var contas =
               Domain.Cobranca.ContaFinanceiraRepository.Queryable()
                   .Where(f => f.Ativo && f.Status != StatusContaFinanceira.ContaCancelada);
            var retorno = Queryable();
            retorno = retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica)).Select(p => new Estabelecimento
            {
                IdEstabelecimento = p.IdEstabelecimento
            });
            return retorno;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosAtivosParaTrinksEmNumeros()
        {
            var retorno = Queryable();

            retorno = retorno.Where(f => f.PessoaJuridica.Ativo);
            retorno =
                retorno.Where(
                    f => f.HotsiteEstabelecimentoLista.Any(g => g.DesejaTerHotsite && !g.Url.Contains("testetrinks")));

            return retorno.OrderBy(f => f.PessoaJuridica.NomeFantasia);
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtiva()
        {
            var contas = Domain.Cobranca.ContaFinanceiraRepository.Queryable()
                .Where(f => f.Ativo && f.DataCancelamento == null && f.Assinaturas.Any(g => g.DataFim == null));
            var retorno = Queryable();
            return retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual()
        {
            var contas = Domain.Cobranca.ContaFinanceiraRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManual();
            var retorno = Queryable();
            retorno = retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));
            return retorno;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManualOuForaToleranciaOuCancelada()
        {
            var contas = Domain.Cobranca.ContaFinanceiraRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManualOuForaToleranciaOuCancelada();
            var retorno = Queryable();
            retorno = retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));
            return retorno;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManualOuCancelada()
        {
            var contas = Domain.Cobranca.ContaFinanceiraRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobrancaManualOuCancelada();
            var retorno = Queryable();
            retorno = retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));
            return retorno;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosComContaFinanceiraAtivaENaoInadimplenteOuCancelado()
        {
            var contas =
                Domain.Cobranca.ContaFinanceiraRepository.Queryable()
                    .Where(f => f.Ativo && f.Status != StatusContaFinanceira.InadimplenteForaTolerancia &&
                                f.Status != StatusContaFinanceira.ContaCancelada);
            var retorno = Queryable();
            retorno = retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));
            return retorno;
        }

        public IQueryable<Estabelecimento>
            ObterEstabelecimentosComContaFinanceiraAtivaENaoInadimplenteOuCanceladoOuNaoAssinado()
        {
            var contas =
                Domain.Cobranca.ContaFinanceiraRepository.Queryable()
                    .Where(f => f.Ativo && f.Status != StatusContaFinanceira.InadimplenteForaTolerancia &&
                                f.Status != StatusContaFinanceira.ContaCancelada
                                && f.Status != StatusContaFinanceira.NaoAssinado);
            var retorno = Queryable();
            retorno = retorno.Where(f => contas.Any(g => g.Pessoa == f.PessoaJuridica));
            return retorno;
        }

        public String ObterNomeDeExibicaoNoPortalPorIdEstabelecimento(int idEstabelecimento)
        {
            var retorno = Queryable();
            return retorno.Where(p => p.IdEstabelecimento == idEstabelecimento).Select(p => p.NomeDeExibicaoNoPortal).FirstOrDefault();
        }

        public IQueryable<Estabelecimento> ObterPorInvisiveisNaBusca()
        {
            var dados = Queryable();

            dados = dados.Where(f => f.PessoaJuridica.Ativo);
            dados =
                dados.Where(
                    f => f.HotsiteEstabelecimentoLista.Any(g => g.DesejaTerHotsite && !g.Url.Contains("testetrinks")));
            dados =
                dados.Where(f => !ObterVisiveisNaBusca().Select(g => g.IdEstabelecimento).Contains(f.IdEstabelecimento));

            //var toleranciaUltimoLogin = Calendario.Hoje().AddDays(-(new ParametrosTrinks<Int32>(ParametrosTrinksEnum.qtd_dias_padrao_ultimo_login_para_manter_hotsite_no_ar).ObterValor()));

            //dados = dados.Where(f => !f.HotsiteEstabelecimentoLista.Any(g => g.DesejaAparecerBuscaPortal)
            //    || !f.HotsiteEstabelecimentoLista.Any(g => g.DesejaTerHotsite)
            //    || !f.VinculoUsuarios.Any(g => g.TutorialBackOfficeConcluido && g.PessoaFisica.Contas.Any(p => p.DataUltimoLogin.HasValue && p.DataUltimoLogin > toleranciaUltimoLogin))
            //    || !f.PessoaJuridica.ContasFinanceiras.Any(cf => cf.Ativo && cf.Status != StatusContaFinanceira.ContaInativa));

            return dados;
        }

        public Estabelecimento ObterPorPessoaJuridica(Int32 idPessoaJuridica)
        {
            return Queryable(true).FirstOrDefault(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica);
        }

        public IQueryable<Estabelecimento> ObterVisiveisNaBusca()
        {
            var dados = Queryable();
            dados = ObterVisiveis(dados);
            return dados;
        }

        public IQueryable<Estabelecimento> ObterVisiveisNasEstatisticasDiarias()
        {
            var dados = Queryable();
            dados = ObterVisiveis(dados);
            return dados;
        }

        public IQueryable<Estabelecimento> ObterVisiveisNoCarrossel()
        {
            var dados = Queryable();
            dados = FiltrarVisiveisNoCarrossel(dados);
            return dados;
        }

        private IQueryable<Estabelecimento> FiltrarVisiveisNoCarrossel(IQueryable<Estabelecimento> dados)
        {
            dados = ObterVisiveis(dados);
            dados = dados.Where(f => f.HotsiteEstabelecimentoLista.Any(g => g.PermiteBuscaHotsite && g.PermiteAgendamentoHotsite));
            return dados;
        }

        // @see http://jira.perlink.net/browse/TRINKS-4478
        public DateTime? TrataDataInicialComRestricaoDeDadosAPartirDeUmaData(Int32 idEstabelecimento,
            DateTime? dataInformadaPeloUsuario)
        {
            if (idEstabelecimento <= 0)
                return dataInformadaPeloUsuario;

            var exibirDadosAPartirDe = ExibirDadosAPartirDe(idEstabelecimento);
            if (!exibirDadosAPartirDe.HasValue)
                return dataInformadaPeloUsuario;

            if (dataInformadaPeloUsuario.HasValue)
            {
                if (dataInformadaPeloUsuario.Value.CompareTo(exibirDadosAPartirDe) < 0)
                    dataInformadaPeloUsuario = exibirDadosAPartirDe;
            }
            else
            {
                dataInformadaPeloUsuario = exibirDadosAPartirDe;
            }

            return dataInformadaPeloUsuario;
        }

        // @see http://jira.perlink.net/browse/TRINKS-4478
        public DateTime TrataDataInicialComRestricaoDeDadosAPartirDeUmaData(Int32 idEstabelecimento,
            DateTime dataInformadaPeloUsuario)
        {
            DateTime? dataNull = null;

            if (dataInformadaPeloUsuario != DateTime.MinValue)
            {
                dataNull = dataInformadaPeloUsuario;
            }

            var trataDataInicialComRestricaoDeDadosAPartirDeUmaData =
                TrataDataInicialComRestricaoDeDadosAPartirDeUmaData(idEstabelecimento, dataNull);
            return trataDataInicialComRestricaoDeDadosAPartirDeUmaData != null
                ? trataDataInicialComRestricaoDeDadosAPartirDeUmaData.Value
                : dataInformadaPeloUsuario;
        }

        public Boolean ValidarSeExisteOutroCnpjAtivo(String cnpj, int idPessoaJuridica = 0)
        {
            var retorno = Queryable();

            return idPessoaJuridica == 0
                ? retorno.Any(p => p.PessoaJuridica.CNPJ == cnpj && p.PessoaJuridica.Ativo)
                : retorno.Any(
                    p =>
                        p.PessoaJuridica.CNPJ == cnpj && p.PessoaJuridica.Ativo &&
                        p.PessoaJuridica.IdPessoa != idPessoaJuridica);
        }

        private static List<KeyValuePair<int, string>> ObterEstabelecimentosFranquiados(Conta conta)
        {
            var retorno = new List<KeyValuePair<int, string>>();
            var ehFranqueador = Domain.Pessoas.ContaFranquiaRepository.EhContaVinculadaAUmaFranquia(conta.IdConta);
            if (ehFranqueador)
            {
                var franquiasAssociadas =
                    Domain.Pessoas.ContaFranquiaRepository.Queryable().Where(p => p.Conta.IdConta == conta.IdConta && p.PermiteAcessarBackOfficeDosEstabelecimentosDaFranquia);

                foreach (var contaFranquia in franquiasAssociadas)
                {
                    var franquia = contaFranquia;
                    var estabelecimentosDaFranquia =
                        Domain.Pessoas.FranquiaEstabelecimentoRepository.Queryable()
                            .Where(f => f.Franquia == franquia.Franquia && f.Franquia.Ativo && f.Ativo && f.PermiteAcessoBackOfficePorOutrosFranqueadores);

                    var estabelecimentosDaFranquiaComAcessoDeAdministrador =
                        Domain.Pessoas.FranquiaEstabelecimentoRepository.Queryable()
                            .Where(f => f.Franquia == franquia.Franquia && f.Ativo && f.Franquia.Ativo && f.PermiteAcessoBackOfficePorOutrosFranqueadores &&
                            (f.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo &&
                            f.CompoeConsolidadoFranqueador ||
                            f.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.Modelo));

                    var estabelecimentosUnificados = estabelecimentosDaFranquia.Where(f => estabelecimentosDaFranquiaComAcessoDeAdministrador.Contains(f))
                        .Select(f => f.IdEstabelecimento);

                    var estabelecimentos = Domain.Pessoas.EstabelecimentoRepository.Queryable()
                        .Where(f => estabelecimentosUnificados.Contains(f.IdEstabelecimento))
                        .Select(f => new KeyValuePair<int, string>(f.IdEstabelecimento, f.NomeDeExibicaoNoPortal));

                    retorno.AddRange(estabelecimentos);
                }
            }
            return retorno;
        }

        private DateTime? ExibirDadosAPartirDe(Int32 idEstabelecimento)
        {
            return
                Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.Load(idEstabelecimento).ExibirDadosAPartirDe;
        }

        private IQueryable<Estabelecimento> ObterVisiveis(IQueryable<Estabelecimento> dados)
        {
            dados = dados.Where(f => f.PessoaJuridica.Ativo);
            dados = dados.Where(f => f.PesoBuscaPortal <= 30);

            var hotsites = Domain.Pessoas.HotsiteEstabelecimentoRepository.Queryable();
            dados = from f in dados
                    join h in hotsites on f.IdEstabelecimento equals h.Estabelecimento.IdEstabelecimento
                    where h.DesejaAparecerBuscaPortal && h.DesejaTerHotsite
                    select f;
            return dados;
        }

        public int TotalDeCampanhasDoEstabelecimento(int idEstabelecimento)
        {
            var queryCount = "SELECT COUNT(*) as [CampanhaEstabelecimento] FROM Campanha_Estabelecimento";

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var sessionCount = holder.CreateSession(typeof(ActiveRecordBase));
            IQuery iSQLQuery = sessionCount.CreateSQLQuery(queryCount);

            var result = iSQLQuery.UniqueResult<int>();
            holder.ReleaseSession(sessionCount);

            return result;
        }


        #endregion Métodos Públicos

        #region Busca Portal

        public ResultadoPaginado<Estabelecimento> Buscar(ParametrosBuscaProfissionalOuEstabelecimento parametros)
        {
            var dados = ListarPorLocal(parametros.UF, parametros.Cidade, parametros.Bairro, null, true,
                parametros.IdFranquia ?? 0);

            dados = FiltroFuncionaNoDia(parametros.Data, dados);
            dados = FiltroOfereceServico(parametros.IdServico, dados);
            dados = FiltroRazaoSocial(parametros.TextoBusca, dados);
            dados = FiltroPorId(parametros.IdEstabelecimento, dados);
            dados = FiltroTipoEstabelecimento(parametros.TipoEstabelecimento, dados);
            dados = MontarOrdenacao(parametros.OrdemResultado, dados);

            return dados.ToList().ToResultadoPaginado(parametros.ParametrosPaginacao);
        }

        public IQueryable<Estabelecimento> ListarPorLocal(int? idEstado, int? idCidade, int? idBairro,
            string textoBusca = null, bool somenteVisivelAoPublico = true, int idFranquia = 0)
        {
            var dados = somenteVisivelAoPublico ? ObterVisiveisNaBusca() : Queryable();

            var enderecos = Domain.Pessoas.EnderecoRepository.Queryable();

            if (idBairro.HasValue && idBairro > 0)
                dados = from f in dados
                        join e in enderecos on f.PessoaJuridica equals e.Pessoa
                        where e.BairroEntidade.Codigo == idBairro.Value
                        select f;
            else if (idCidade.HasValue && idCidade > 0)
                dados = from f in dados
                        join e in enderecos on f.PessoaJuridica equals e.Pessoa
                        where e.BairroEntidade.Cidade.Codigo == idCidade.Value
                        select f;
            else if (idEstado.HasValue && idEstado > 0)
                dados = from f in dados
                        join e in enderecos on f.PessoaJuridica equals e.Pessoa
                        where e.UF.IdUF == idEstado.Value
                        select f;

            if (!string.IsNullOrWhiteSpace(textoBusca))
            {
                textoBusca = textoBusca.ToLower();
                dados = dados.Where(f => f.NomeDeExibicaoNoPortal.ToLower().Contains(textoBusca));
            }

            if (idFranquia > 0)
                dados =
                    dados.Where(
                        f =>
                            f.FranquiaEstabelecimento != null && f.FranquiaEstabelecimento.Ativo &&
                            f.FranquiaEstabelecimento.Franquia.Id == idFranquia);

            return dados;
        }

        #endregion Busca Portal

        #region Métodos Privados

        private IQueryable<Estabelecimento> MontarOrdenacao(
            ParametrosBuscaProfissionalOuEstabelecimento.OrdemResultadosBuscaPortalEnum ordenacaoEscolhida,
            IQueryable<Estabelecimento> dados)
        {
            switch (ordenacaoEscolhida)
            {
                case ParametrosBuscaProfissionalOuEstabelecimento.OrdemResultadosBuscaPortalEnum.Default:
                    dados = dados.OrderBy(f => f.PesoBuscaPortal)
                        .ThenBy(f => f.NomeDeExibicaoNoPortal);
                    break;

                case ParametrosBuscaProfissionalOuEstabelecimento.OrdemResultadosBuscaPortalEnum.NomeEstabelecimentoAZ:
                    dados = dados.OrderBy(
                        f =>
                            f.HotsiteEstabelecimentoLista.Any(g => g.PermiteAgendamentoHotsite && g.PermiteBuscaHotsite)
                                ? 0
                                : 1)
                        .ThenBy(f => f.NomeDeExibicaoNoPortal);
                    break;

                case ParametrosBuscaProfissionalOuEstabelecimento.OrdemResultadosBuscaPortalEnum.NomeEstabelecimentoZA:
                    dados = dados.OrderBy(
                        f =>
                            f.HotsiteEstabelecimentoLista.Any(g => g.PermiteAgendamentoHotsite && g.PermiteBuscaHotsite)
                                ? 0
                                : 1)
                        .ThenByDescending(f => f.NomeDeExibicaoNoPortal);
                    break;

                case ParametrosBuscaProfissionalOuEstabelecimento.OrdemResultadosBuscaPortalEnum.MaisNovos:
                    dados = dados.OrderBy(
                        f =>
                            f.HotsiteEstabelecimentoLista.Any(g => g.PermiteAgendamentoHotsite && g.PermiteBuscaHotsite)
                                ? 0
                                : 1)
                        .ThenByDescending(f => f.PessoaJuridica.DataCadastro);
                    break;

                case ParametrosBuscaProfissionalOuEstabelecimento.OrdemResultadosBuscaPortalEnum.MaisAntigos:
                    dados = dados.OrderBy(
                        f =>
                            f.HotsiteEstabelecimentoLista.Any(g => g.PermiteAgendamentoHotsite && g.PermiteBuscaHotsite)
                                ? 0
                                : 1)
                        .ThenBy(f => f.PessoaJuridica.DataCadastro);
                    break;
            }

            return dados;
        }

        #endregion Métodos Privados

        #region Filtros LINQ

        private static IQueryable<Estabelecimento> FiltroAtivo(IQueryable<Estabelecimento> retorno)
        {
            return retorno.Where(f => f.PessoaJuridica.Ativo);
        }

        private static IQueryable<Estabelecimento> FiltroFuncionaNoDia(DateTime? data, IQueryable<Estabelecimento> dados)
        {
            if (data.HasValue)
            {
                dados = dados.Where(f =>
                    f.HorariosDeFuncionamento.Any(g =>
                        g.Aberto && g.Ativo &&
                        g.DiaSemana.IdDiaSemana == (data.Value.DayOfWeek.GetHashCode() + 1) &&
                        g.HoraAbertura != null && g.HoraFechamento != null
                        ) &&
                    !f.HorariosEspeciaisDeFuncionamento.Any(g =>
                        g.DataInicio < data.Value.Date.AddDays(1) &&
                        g.DataFim >= data.Value.Date &&
                        g.EstabelecimentoHorarioEspecialFuncionamentoTipo.Codigo == 1));
            }
            return dados;
        }

        private static IQueryable<Estabelecimento> FiltroNomeFantasia(String nomeFantasia,
            IQueryable<Estabelecimento> retorno)
        {
            return retorno.Where(f => f.PessoaJuridica.NomeFantasia.ToLower() == nomeFantasia.ToLower());
        }

        private static IQueryable<Estabelecimento> FiltroOfereceServico(int? idServico,
            IQueryable<Estabelecimento> dados)
        {
            var sinonimoServQueryable = Domain.Pessoas.ServicoSinonimoRepository.Queryable();

            if (idServico != null && idServico > 0)
                dados = dados.Where(f => f.ServicosEstabelecimento.Any(g =>
                    g.Ativo &&
                    g.EstabelecimentoProfissionalServicoLista.Any(h => h.Ativo) &&
                    (g.Servico.IdServico == idServico ||
                     sinonimoServQueryable.Any(p => p.PrimeiroServico.IdServico == idServico &&
                                                    p.SegundoServico.IdServico == g.Servico.IdServico))
                    ));

            return dados;
        }

        private static IQueryable<Estabelecimento> FiltroPorId(int? idEstabelecimento, IQueryable<Estabelecimento> dados)
        {
            if (idEstabelecimento != null && idEstabelecimento > 0)
                dados = dados.Where(f => f.IdEstabelecimento == idEstabelecimento);
            return dados;
        }

        private static IQueryable<Estabelecimento> FiltroRazaoSocial(string texto, IQueryable<Estabelecimento> dados)
        {
            if (!string.IsNullOrWhiteSpace(texto))
                dados = dados.Where(f => f.PessoaJuridica.NomeFantasia.Contains(texto));
            return dados;
        }

        private static IQueryable<Estabelecimento> FiltroTipoEstabelecimento(string tipoEstabelecimento,
            IQueryable<Estabelecimento> dados)
        {
            if (!string.IsNullOrEmpty(tipoEstabelecimento))
                dados =
                    dados.Where(
                        f =>
                            f.TipoEstabelecimento.Ativo &&
                            f.TipoEstabelecimento.Nome.ToLower() == tipoEstabelecimento.Replace("-", " "));
            return dados;
        }

        #endregion Filtros LINQ

        #region Critérios

        private static void AdicionarCriterioPessoaJuridica(int idPessoaJuridica, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.Estabelecimento.PessoaJuridica + "." + PropertyNames.Pessoas.Pessoa.IdPessoa,
                    idPessoaJuridica));
        }

        private void AdicionarAliasPessoaJuridica(DetachedCriteria dc)
        {
            dc.CreateAlias(PropertyNames.Pessoas.Estabelecimento.PessoaJuridica,
                PropertyNames.Pessoas.Estabelecimento.PessoaJuridica);
        }

        private void AdicionarCriterioAtivo(DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.Estabelecimento.PessoaJuridica + "." + PropertyNames.Pessoas.Pessoa.Ativo,
                    true));
        }

        private void AdicionarCriterioTutorialConcluido(DetachedCriteria dc)
        {
            var dtTutorialRealizado = DetachedCriteria.For(typeof(UsuarioEstabelecimento));
            dtTutorialRealizado.SetProjection(
                Property.ForName(PropertyNames.Pessoas.UsuarioEstabelecimento.IdUsuarioEstabelecimento));
            dtTutorialRealizado.Add(
                Property.ForName(PropertyNames.Pessoas.UsuarioEstabelecimento.Estabelecimento + "." +
                                 PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento)
                    .EqProperty("EstabRaiz." + PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento)
                );
            //dtTutorialRealizado.Add(
            //    Restrictions.Eq(PropertyNames.Pessoas.UsuarioEstabelecimento.TutorialBackOfficeConcluido, true));

            dc.Add(Subqueries.Exists(dtTutorialRealizado));
        }

        private void AdicionarOrdemRazaoSocial(DetachedCriteria dc)
        {
            dc.AddOrder(
                new Order(
                    PropertyNames.Pessoas.Estabelecimento.PessoaJuridica + "." +
                    PropertyNames.Pessoas.PessoaJuridica.RazaoSocial, true));
        }

        #endregion Critérios

        public bool EstabelecimentoEhValidoParaIntegracaoDeNotaFiscalDoConsumidor(int idEstabelecimento,
            string emailDaConta)
        {
            var query = ObterQueryDeEstabelecimentosValidosParaIntegracaoDeNotaFiscalDoConsumidor(emailDaConta);
            return query.Any(f => f.IdEstabelecimento == idEstabelecimento);
        }

        public IQueryable<Estabelecimento> ListarEstabelecimentosEmQueECliente(PessoaFisica pessoaFisica, bool somenteAtivos = false)
        {
            var clientesEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Queryable();

            if (somenteAtivos)
                clientesEstabelecimento = clientesEstabelecimento.Where(f => f.Ativo);

            clientesEstabelecimento = clientesEstabelecimento.Where(f => f.Cliente.PessoaFisica.IdPessoa == pessoaFisica.IdPessoa);

            return clientesEstabelecimento.Select(f => f.Estabelecimento);
        }

        public IList<Estabelecimento> ListarEstabelecimentosValidosParaIntegracaoDeNotaFiscalDoConsumidor(
                    string emailDaConta)
        {
            var query = ObterQueryDeEstabelecimentosValidosParaIntegracaoDeNotaFiscalDoConsumidor(emailDaConta);
            return query.OrderBy(f => f.PessoaJuridica.NomeFantasia).ToList();
        }

        private IQueryable<Estabelecimento> ObterQueryDeEstabelecimentosValidosParaIntegracaoDeNotaFiscalDoConsumidor(
            string emailDaConta)
        {
            var vinculos =
                Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                    .Where(f => f.PessoaFisica.Email == emailDaConta.ToLower() && f.Ativo);

            var statusDeContasValidas = new List<int>{
                ((int) StatusContaFinanceira.PeriodoGratis),
                ((int) StatusContaFinanceira.Adimplente),
                ((int) StatusContaFinanceira.InadimplenteEmTolerancia),
                ((int) StatusContaFinanceira.CobrancaManual)
            };
            var idsDeInterfacesQuePrecisamDeIntegracao = new List<TipoDeInterfaceNFC> { TipoDeInterfaceNFC.Autocom };//, TipoDeInterfaceNFC.Sat };
            var query = vinculos
                .Where(
                    f =>
                        f.PerfilAcesso != AcessoBackoffice.Nao_Possui &&
                        f.PerfilAcesso != AcessoBackoffice.SomenteMinhaAgenda)
                .Where(
                    e =>
                        e.Estabelecimento.PessoaJuridica.ContasFinanceiras.Any(
                            cf => cf.Ativo && statusDeContasValidas.Contains(cf.Status.IdStatus)))
                .Where(
                    e =>
                        e.Estabelecimento.ConfiguracaoDeNFC != null &&
                        idsDeInterfacesQuePrecisamDeIntegracao.Contains(e.Estabelecimento.ConfiguracaoDeNFC.InterfaceNFC))
                .Select(f => f.Estabelecimento);

            return query;
        }

        #region ÁREA ADMINISTRATIVA

        public IQueryable<Estabelecimento> ListarEstabelecimentosParaAreaAdministrativa()
        {
            IQueryable<Estabelecimento> listaDeEstabelecimentos;

            if (ContextHelper.Instance.IdFranquia.HasValue && ContextHelper.Instance.IdFranquia.Value != 0)
            {
                listaDeEstabelecimentos = ListarEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivoOuForaToleranciaOuCancelados();
            }
            else
            {
                listaDeEstabelecimentos = ListarEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento();
            }
            return listaDeEstabelecimentos;
        }

        public DadosParaComboDeFiltrosDoRelatorioConsolidado ObterDadosParaFiltrosDaAreaAdministrativa(int? idPessoa)
        {
            var dadosParaCombo = new DadosParaComboDeFiltrosDoRelatorioConsolidado();
            var listaDeEstabelecimentos = ListarEstabelecimentosParaAreaAdministrativa();

            var listaUF = listaDeEstabelecimentos.Select(f => f.PessoaJuridica.EnderecoProprio.UF).Distinct().ToList();
            var listaMunicipios = listaDeEstabelecimentos.Select(f => new { IdUF = f.PessoaJuridica.EnderecoProprio.UF.IdUF, NomeCidade = f.PessoaJuridica.EnderecoProprio.Cidade, Codigo = f.PessoaJuridica.EnderecoProprio.BairroEntidade.Codigo }).Distinct();

            //dadosParaCombo.ListaEstabelecimentosDoConsolidado = listaDeEstabelecimentos.ToList();
            dadosParaCombo.ListaEstadosDoConsolidado = listaUF;

            foreach (var item in listaMunicipios)
            {
                var municipio = new MunicipioDoConsolidado(item.IdUF, item.NomeCidade);
                dadosParaCombo.ListaDeMunicipiosDoConsolidado.Add(municipio);
            }

            return dadosParaCombo;
        }

        private IQueryable<Estabelecimento> ListarEstabelecimentosCujoAdministradorEhAdministradoDeOutroEstabelecimento()
        {
            var estabelecimentosComContaFinanceiraEmStatusPositivo = ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManual().Select(f => f.IdEstabelecimento);
            var idPessoa = Domain.Pessoas.ContaRepository.Load(ContextHelper.Instance.IdConta.Value).Pessoa.IdPessoa;

            var listaIdsEstabelecimentoLigadosAFranquiaExcetoModelo = Domain.Pessoas.FranquiaEstabelecimentoRepository
                .Queryable().Where(p => p.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.Modelo)
                .Select(p => p.IdEstabelecimento);

            var listaIdsEstabelecimento = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable()
                .Where(p => p.Ativo && p.UsuarioPerfil.Perfil.Id == (int)UsuarioPerfilEnum.Administrador && p.PessoaFisica.IdPessoa == idPessoa &&
                estabelecimentosComContaFinanceiraEmStatusPositivo.Contains(p.Estabelecimento.IdEstabelecimento) &&
                !listaIdsEstabelecimentoLigadosAFranquiaExcetoModelo.Contains(p.Estabelecimento.IdEstabelecimento))
                .Select(e => e.Estabelecimento.IdEstabelecimento).ToList();

            return Domain.Pessoas.EstabelecimentoRepository.Queryable().Where(f => listaIdsEstabelecimento.Contains(f.IdEstabelecimento));
        }

        private IQueryable<Estabelecimento> ListarEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivoOuForaToleranciaOuCancelados()
        {
            var estabelecimentosComContaFinanceiraEmStatusPositivoOuForaToleranciaOuCancelados = Domain.Pessoas.EstabelecimentoRepository.ObterEstabelecimentosComContaFinanceiraAtivaEAssinadoOuGratisOuCobManualOuForaToleranciaOuCancelada();
            var listaIdsEstabelecimentoLigadosAFranquia = Domain.Pessoas.FranquiaEstabelecimentoRepository
                .Queryable().Where(p => p.CompoeConsolidadoFranqueador && p.Ativo && p.Franquia.Id == ContextHelper.Instance.IdFranquia && p.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo)
                .Select(p => p.IdEstabelecimento);

            var listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo = estabelecimentosComContaFinanceiraEmStatusPositivoOuForaToleranciaOuCancelados
                .Where(p => listaIdsEstabelecimentoLigadosAFranquia.Contains(p.IdEstabelecimento));

            return listaEstabelecimentosLigadosAFranquiaComContaFinanceiraEmStatusPositivo;
        }

        #endregion ÁREA ADMINISTRATIVA

        public bool ExistemEstabelecimentosComMesmoResponsavelFinanceiro(Estabelecimento estabelecimento)
        {
            return Queryable().Where(e => e.PessoaJuridica.ResponsavelFinanceiro.IdPessoa == estabelecimento.PessoaJuridica.ResponsavelFinanceiro.IdPessoa && estabelecimento.IdEstabelecimento != e.IdEstabelecimento).Any();
        }

        public bool ObterConfiguracaoDeDataRecebimentoComissao(int idEstabelecimento)
        {
            return Queryable().Where(e => e.EstabelecimentoConfiguracaoGeral.IdEstabelecimento == idEstabelecimento)
                            .Select(e => e.EstabelecimentoConfiguracaoGeral.PagarComissoesNaDataPrevistaDeRecebimento)
                            .FirstOrDefault();
        }

        public bool ConfiguracaoDeDataRecebimentoComissaoFoiAlterado(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral)
        {
            var atualConfiguracao = ObterConfiguracaoDeDataRecebimentoComissao(estabelecimentoConfiguracaoGeral.IdEstabelecimento);

            return atualConfiguracao != estabelecimentoConfiguracaoGeral.PagarComissoesNaDataPrevistaDeRecebimento;
        }

        public bool ObterConfiguracaoDeDescontoComissaoProfissional(int idEstabelecimento)
        {
            return Queryable().Where(e => e.EstabelecimentoConfiguracaoGeral.IdEstabelecimento == idEstabelecimento)
                            .Select(e => e.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao)
                            .FirstOrDefault();
        }

        public bool ConfiguracaoDeDescontoComissaoFoiAlterado(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral)
        {
            var atualConfiguracao = ObterConfiguracaoDeDescontoComissaoProfissional(estabelecimentoConfiguracaoGeral.IdEstabelecimento);

            return atualConfiguracao != estabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao;
        }

        public bool ObterConfiguracaoDeDescontoComissaoAssistente(int idEstabelecimento)
        {
            return Queryable().Where(e => e.EstabelecimentoConfiguracaoGeral.IdEstabelecimento == idEstabelecimento)
                            .Select(e => e.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissaoDeAssistentes)
                            .FirstOrDefault();
        }

        public bool ConfiguracaoDeDescontoComissaoAssistenteFoiAlterado(EstabelecimentoConfiguracaoGeral estabelecimentoConfiguracaoGeral)
        {
            var atualConfiguracao = ObterConfiguracaoDeDescontoComissaoAssistente(estabelecimentoConfiguracaoGeral.IdEstabelecimento);

            return atualConfiguracao != estabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissaoDeAssistentes;
        }

        public Estabelecimento ObterPorId(int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(e => e.IdEstabelecimento == idEstabelecimento);
        }

        public DadosEstabelecimentoIntegracaoDTO ProjecaoDeDadosEstabelecimentoIntegracaoDTOPorIdEstabelecimento(Estabelecimento estabelecimento)
        {
            var pessoaJuridicaDTO = Domain.Pessoas.PessoaJuridicaRepository.Queryable()
                                      .Where(pj => pj.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa)
                                      .Select(pj => new { pj.NomeFantasia, pj.CNPJ, pj.RazaoSocial, pj.Email }).FirstOrDefault();

            var responsavelEstabelecimentoDTO = Domain.Pessoas.PessoaJuridicaRepository.Queryable()
                .Where(pj => pj.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa && pj.Ativo)
                .Select(pj => new { pj.ResponsavelFinanceiro.IdPessoa, pj.ResponsavelFinanceiro.NomeCompleto, pj.ResponsavelFinanceiro.Email, pj.ResponsavelFinanceiro.Cpf }).FirstOrDefault();

            var telefonesDoEstabelecimento = Domain.Pessoas.TelefoneRepository.Queryable().Where(te => te.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa && te.Ativo)
               .Select(t => t.Numero).ToList();

            var telefoneDoResponsavel = Domain.Pessoas.TelefoneRepository.Queryable()
                .Where(te => te.Dono.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa
                        && te.IdPessoa == responsavelEstabelecimentoDTO.IdPessoa && te.Ativo)
                .Select(t => t.Numero).ToList().FirstOrDefault();

            var diasFuncionamento = Domain.Pessoas.EstabelecimentoHorarioFuncionamentoRepository.Queryable()
                .Where(h => h.Estabelecimento.IdEstabelecimento == estabelecimento.IdEstabelecimento && h.Aberto && h.Ativo)
                .Select(d => d.DiaSemana.Nome).ToList();

            var enderecoEstabelecimentoDTO = Domain.Pessoas.EnderecoRepository.Queryable().Where(e => e.IdEndereco == estabelecimento.PessoaJuridica.EnderecoProprio.IdEndereco && e.Ativo)
               .Select(e => new { e.Cep, e.Logradouro, e.Bairro, e.Cidade, e.Numero, e.Complemento, e.UF }).FirstOrDefault();

            var enderecoCompletoTexto = "";
            var endereco = Domain.Pessoas.EnderecoRepository.Queryable().Where(e => e.IdEndereco == estabelecimento.PessoaJuridica.EnderecoProprio.IdEndereco && e.Ativo)
                .Select(e => e).FirstOrDefault();
            if (endereco != null)
                enderecoCompletoTexto = endereco.ObterTextoEndereco();

            var enderecoEstabelecimento =
                enderecoCompletoTexto != null ?
                new IntegracaoComOutrosSistemas.Endereco()
                {
                    Logradouro = enderecoEstabelecimentoDTO.Logradouro ?? "",
                    Bairro = enderecoEstabelecimentoDTO.Bairro ?? "",
                    Cidade = enderecoEstabelecimentoDTO.Cidade ?? "",
                    Numero = enderecoEstabelecimentoDTO.Numero ?? "",
                    UF = enderecoEstabelecimentoDTO.UF == null ? String.Empty : enderecoEstabelecimentoDTO.UF.Sigla.ToString(),
                    Complemento = enderecoEstabelecimentoDTO.Complemento ?? "",
                    EnderecoCompleto = enderecoCompletoTexto ?? ""
                }
             : null;

            var dadosEstabelecimentoIntegracaoDTO = new DadosEstabelecimentoIntegracaoDTO()
            {
                CNPJ = pessoaJuridicaDTO.CNPJ,
                NomeFantasia = pessoaJuridicaDTO.NomeFantasia,
                RazaoSocial = pessoaJuridicaDTO.RazaoSocial,
                Email = pessoaJuridicaDTO.Email,
                Telefones = telefonesDoEstabelecimento,
                NomeDoResponsavel = responsavelEstabelecimentoDTO.NomeCompleto,
                CPFDoResponsavel = responsavelEstabelecimentoDTO.Cpf,
                EmailDoResponsavel = responsavelEstabelecimentoDTO.Email,
                TelefoneDoResponsavel = telefoneDoResponsavel,
                Cep = enderecoEstabelecimentoDTO.Cep,
                Endereco = enderecoEstabelecimento,
                DiasFuncionamento = diasFuncionamento
            };
            return dadosEstabelecimentoIntegracaoDTO;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosComContaAtiva()
        {
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            return from e in Queryable()
                   join c in contaFinanceira on e.PessoaJuridica.IdPessoa equals c.Pessoa.IdPessoa
                   where c.Ativo
                         && (new[] { 1, 3, 4, 9 }).Contains(c.Status.IdStatus)
                   select e;
        }

        public IQueryable<Estabelecimento> FiltrarEstabelecimentosComContaAtiva(List<int> idsEstabelecimento)
        {
            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            return from e in Queryable()
                   join c in contaFinanceira on e.PessoaJuridica.IdPessoa equals c.Pessoa.IdPessoa
                   where c.Ativo && idsEstabelecimento.Contains(e.IdEstabelecimento)
                         && (new[] { 1, 3, 4, 9 }).Contains(c.Status.IdStatus)
                   select e;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosQuePossuaAgendamendos(IQueryable<Estabelecimento> estabelecimentos, DateTime data)
        {
            var dataInicial = data.AddDays(-1);
            var dataFinal = data.AddDays(15);

            var agendamentos = Domain.Pessoas.HorarioRepository.Queryable().
                Where(a => a.DataInicio >= dataInicial
                     && a.DataInicio <= dataFinal);

            var estabelecimentosComAgendamentos = estabelecimentos.Where(e => agendamentos.Any(a => a.Estabelecimento.IdEstabelecimento == e.IdEstabelecimento));

            return estabelecimentosComAgendamentos;
        }

        public bool EstabelecimentoEstaUtilizandoSplitDePagamento(int idEstabelecimento)
        {
            var estabelecimentoEstaUtilizandoSplitDePagamento = false;
            var estabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento = EstabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento(idEstabelecimento);

            if (estabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento)
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
                estabelecimentoEstaUtilizandoSplitDePagamento = estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaSplitPagamento;
            }

            return estabelecimentoEstaUtilizandoSplitDePagamento;
        }

        public bool EstabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var estabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento = false;

            var estabelecimentoConfiguracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);
            if (estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaBelezinha &&
                estabelecimentoConfiguracaoPOS != null &&
                estabelecimentoConfiguracaoPOS.HabilitaSplit &&
                estabelecimentoConfiguracaoPOS.TipoPOS.HabilitaSplit &&
                !estabelecimentoConfiguracaoPOS.AguardandoRetornoDoCredenciamentoNaAdquirente &&
                estabelecimentoConfiguracaoPOS.ConcluiuConfiguracao)
                estabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento = true;
            else
                estabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento = false;

            return estabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento;
        }

        public int ObterIdPessoaDoEstabelecimento(int idEstabelecimento)
        {
            int? idPessoa = StatelessQueryable().Where(est => est.IdEstabelecimento == idEstabelecimento).Select(est => est.PessoaJuridica.IdPessoa).FirstOrDefault();
            return idPessoa ?? 0;
        }

        public IQueryable<Estabelecimento> ObterEstabelecimentosAtivosEmPeriodoGratis()
        {
            var query = Queryable();
            query = FiltroAtivo(query);

            var contaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            query = from e in query
                    join c in contaFinanceira on e.PessoaJuridica.IdPessoa equals c.Pessoa.IdPessoa
                    where c.Ativo
                          && c.Status.IdStatus == (int)StatusContaFinanceira.PeriodoGratis
                    select e;

            return query;
        }

        public IQueryable<Estabelecimento> ListarOsDisponiveisNoGoogleReserve()
        {
            var statusContaAceitaveis = new[] { 1, 3, 4, 9 };

            var contasFinanceiras = Domain.Cobranca.ContaFinanceiraRepository.Queryable().Where(f => statusContaAceitaveis.Contains(f.Status.IdStatus));

            var servicosComValor = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable()
                .Where(f => f.Ativo
                    && f.PermiteAgendamentoHotsite
                    && f.ServicoEstabelecimento.Ativo
                    && f.EstabelecimentoProfissional.Ativo
                    && f.ServicoEstabelecimento.TipoPreco == TipoPrecoEnum.Fixo
                    && f.ServicoEstabelecimento.Preco > 1
                    && f.ServicoEstabelecimento.Preco < 10000
                    && f.ServicoEstabelecimento.Duracao <= 600
                    && f.ServicoEstabelecimento.Duracao > 0
                    && !f.ServicoEstabelecimento.ServicoIndiposnivelParaCliente);

            var retorno = from e in Queryable()
                          where e.PessoaJuridica.Ativo
                            && e.EstabelecimentoConfiguracaoGeral.PermiteAgendarGoogleReserve
                            && contasFinanceiras.Any(f => f.Pessoa == e.PessoaJuridica)
                            && servicosComValor.Any(f => f.EstabelecimentoProfissional.Estabelecimento == e)
                            && e.PessoaJuridica.Enderecos.Any(f => f.Cep.Length == 8)
                          select e;

            //var estabelecimentosAtivosReserve = ConfiguracoesTrinks.Google.EstabelecimentosAtivosReserve;
            //if (ConfiguracoesTrinks.Google.EstabelecimentosAtivosReserve != null)
            //    retorno = retorno.Where(f => estabelecimentosAtivosReserve.Contains(f.IdEstabelecimento));

            return retorno;
        }

        public IQueryable<Estabelecimento> Listar(ParametrosBusca parametros)
        {
            var retorno = Queryable();

            if (parametros.Estabelecimentos != null && parametros.Estabelecimentos.Any())
                retorno = retorno.Where(f => parametros.Estabelecimentos.Contains(f.IdEstabelecimento));
            else
                retorno = retorno.Where(f => f.PessoaJuridica.Ativo);

            return retorno;
        }

        public IEnumerable<KeyValuePair<string, string>> ObterKeyValueUrlNomeEstabelecimento(string filtro)
        {
            var retorno = Queryable().Where(f => f.NomeDeExibicaoNoPortal.Contains(filtro) && f.PessoaJuridica.Ativo && f.PessoaJuridica.ConfiguracaoNFe != null && f.PessoaJuridica.ConfiguracaoNFe.UltimoLoteGerado > 0)
               .Select(f => new KeyValuePair<string, string>(f.PessoaJuridica.IdPessoa.ToString(), f.NomeDeExibicaoNoPortal)).OrderBy(f => f.Value)
               .AsEnumerable();

            return retorno;
        }

        //
        public IEnumerable<KeyValuePair<string, string>> ObterKeyValueUrlIdEstabelecimento(string filtro)
        {
            var retorno = Queryable().Where(f => f.NomeDeExibicaoNoPortal.Contains(filtro) && f.PessoaJuridica.Ativo && f.PessoaJuridica.ConfiguracaoNFe != null && f.PessoaJuridica.ConfiguracaoNFe.UltimoLoteGerado > 0)
               .Select(f => new KeyValuePair<string, string>(f.IdEstabelecimento.ToString(), f.NomeDeExibicaoNoPortal)).OrderBy(f => f.Value)
               .AsEnumerable();

            return retorno;
        }

        public IEnumerable<KeyValuePair<string, string>> ObterKeyValueNomeIdEstabelecimento(string filtro)
        {
            var retorno = Queryable().Where(f => f.NomeDeExibicaoNoPortal.Contains(filtro) && f.PessoaJuridica.Ativo)
               .Select(f => new KeyValuePair<string, string>(f.IdEstabelecimento.ToString(), f.NomeDeExibicaoNoPortal)).OrderBy(f => f.Value)
               .AsEnumerable();

            return retorno;
        }

        public Estabelecimento BuscarEstabelecimentoPeloNome(string nomeEstabelecimento)
        {
            return Queryable().FirstOrDefault(e => e.NomeDeExibicaoNoPortal == nomeEstabelecimento);
        }

        public Estabelecimento BuscarEstabelecimentoPeloCNPJ(string cnpj)
        {
            return Queryable().FirstOrDefault(e => e.PessoaJuridica.CNPJ == cnpj);
        }

        public bool EhUmaUnidadeDaFranquia(int idEstabelecimento, int idFranquia)
        {
            return Queryable()
                .Where(e => e.IdEstabelecimento == idEstabelecimento && e.FranquiaEstabelecimento.Franquia.Id == idFranquia)
                .Select(e => e.IdEstabelecimento)
                .Any();
        }

        public bool EhPessoaJuridicaFranqueadaAtiva(int idPessoa)
        {
            return Queryable()
                .Where(e => e.PessoaJuridica.IdPessoa == idPessoa && e.FranquiaEstabelecimento.Ativo)
                .Select(e => e.IdEstabelecimento)
                .Any();
        }

        public DadosParaEnvioDeEmailVendaHotsiteDTO ObterDadosParaEnvioDeEmailVendaHotsitePorIdEstabelecimento(int idEstabelecimento)
        {
            return Queryable()
                .Where(p => p.IdEstabelecimento == idEstabelecimento).Select(estab => new DadosParaEnvioDeEmailVendaHotsiteDTO(estab.IdEstabelecimento, estab.NomeDeExibicaoNoPortal, estab.PessoaJuridica.ResponsavelFinanceiro.NomeCompleto, estab.PessoaJuridica.ResponsavelFinanceiro.Email)).FirstOrDefault();
        }

        public int ObterIdPorPessoaJuridica(int idPessoaJuridica)
        {
            return Queryable().Where(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica).Select(e => e.IdEstabelecimento).FirstOrDefault();
        }

        public Estabelecimento ObterEstabelecimentodoProfissionalParceiro(int idPessoaJuridica)
        {
            return Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable(true)
                .Where(e => e.PessoaJuridica.IdPessoa == idPessoaJuridica)
                .Select(e => e.Estabelecimento).FirstOrDefault();

        }

        public List<KeyValuePair<int, int>> ObterPorListaPessoaJuridica(List<int> idsPessoaJuridica)
        {
            return Queryable(true).Where(e => idsPessoaJuridica.Contains(e.PessoaJuridica.IdPessoa))
                .Select(e => new KeyValuePair<int, int>(e.PessoaJuridica.IdPessoa, e.IdEstabelecimento)).ToList();
        }

        public List<Estabelecimento> ObterEstabelecimentosDaDataQueTenhamFilaDoRodizio(DateTime data)
        {
            return Queryable()
                .Where(estabelecimento => estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComRodizioDeProfissionais)
                .Where(estabelecimento => Domain.RodizioDeProfissionais.ColocacaoDoProfissionalRepository.Queryable()
                    .Any(colocacao => colocacao.DataReferenciaDaFila == data && colocacao.IdEstabelecimento == estabelecimento.IdEstabelecimento))
                .ToList();
        }

        public List<Estabelecimento> ObterEstabelecimentosPorIds(List<int> idsEstabelecimentos)
        {
            if (idsEstabelecimentos == null || !idsEstabelecimentos.Any())
                return new List<Estabelecimento>();

            return Queryable().Where(e => idsEstabelecimentos.Contains(e.IdEstabelecimento)).ToList();
        }
    }
}