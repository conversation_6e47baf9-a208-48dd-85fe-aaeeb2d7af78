﻿using NHibernate.Criterion;
using NHibernate.Linq;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Fidelidade.DTO;
using Perlink.Trinks.Fidelidade.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Promocoes.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class ServicoEstabelecimentoRepository : IServicoEstabelecimentoRepository
    {

        public bool ExisteOutroServicoEstabelecimentoNoEstabelecimentoExcetoOsServicos(List<int> idsServicoEstabelecimento, int idEstabelecimento)
        {
            return Queryable().Any(p => !idsServicoEstabelecimento.Contains(p.IdServicoEstabelecimento) && p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo);
        }

        public bool ExisteServicoEstabelecimentoAtivo(int idServicoEstabelecimento)
        {
            return Queryable().Any(p => p.IdServicoEstabelecimento == idServicoEstabelecimento && p.Ativo);
        }

        public bool ExisteServicoEstabelecimentoJaAssociadoAoServicoPadraoNoEstabelecimento(int idEstabelecimento, int idServicoPadrao, int idServicoEstabelecimento)
        {
            return Queryable().Any(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.IdServicoEstabelecimento != idServicoEstabelecimento && p.Servico.IdServico == idServicoPadrao);
        }

        public IQueryable<KeyValuePair<int, string>> KeyValueServicoEstabelecimentosAtivos(int codigoEstabelecimento)
        {
            var retorno = StatelessQueryable();

            retorno = FiltroEstabelecimento(codigoEstabelecimento, retorno);
            retorno = FiltroAtivo(retorno);
            retorno = OrdenacaoPorNomeServicoEstabelecimento(retorno);
            return retorno.Select(f => new KeyValuePair<int, string>(f.IdServicoEstabelecimento, f.Nome));
        }

        public IList<ServicoEstabelecimento> ListarAtivosAssociadosAoEstabelecimentoEExibemPreco(Int32 idEstabelecimento)
        {
            return Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.ExibePreco && f.Ativo).ToList();
        }

        public bool EstabelecimentoPossuiPeloMenosDoisServicosComPrecoVinculadoAhProfissionalAtivo(int idEstabelecimento)
        {
            return QueryableEstabelecimentoPossuiServicoComPreco(idEstabelecimento).Where(f => f.EstabelecimentoProfissionalServicoLista.Any(p => p.Ativo && p.EstabelecimentoProfissional.Ativo)).Count() > 1;
        }
        public bool EstabelecimentoPossuiPeloMenosUmServicosComPrecoVinculadoAhProfissionalAtivo(int idEstabelecimento)
        {
            return QueryableEstabelecimentoPossuiServicoComPreco(idEstabelecimento).Where(f => f.EstabelecimentoProfissionalServicoLista.Any(p => p.Ativo && p.EstabelecimentoProfissional.Ativo)).Count() >= 1;
        }
        public bool EstabelecimentoPossuiPeloMenosDoisServicosComPreco(int idEstabelecimento)
        {
            return QueryableEstabelecimentoPossuiServicoComPreco(idEstabelecimento).Count() > 1;
        }

        private IQueryable<ServicoEstabelecimento> QueryableEstabelecimentoPossuiServicoComPreco(int idEstabelecimento)
        {
            return Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            (f.Preco != 0 || f.TipoPreco == TipoPrecoEnum.Gratuito) &&
                            f.Ativo &&
                            f.ExibePreco);
        }

        public IQueryable<ServicoEstabelecimento> ListarAtivosAssociadosAoEstabelecimentoEExibemPrecoEComProfissionalAtivo(Int32 idEstabelecimento, bool? filtraComExibicaoDePrecos = null, int? idProfissionalAssociado = 0, bool servicoIndisponivelParaClientes = false)
        {
            var query = ObterQueryableDeServicosVisiveisNoHotSite(idEstabelecimento, idProfissionalAssociado: idProfissionalAssociado, servicoIndisponivelParaClientes: servicoIndisponivelParaClientes);

            if (filtraComExibicaoDePrecos.HasValue && filtraComExibicaoDePrecos.Value)
                query = query.Where(f => f.ExibePreco);

            return query.OrderBy(f => f.Nome);
        }

        public IList<ServicoEstabelecimento> ListarAtivosAssociadosAoEstabelecimentoENaoExibemPreco(Int32 idEstabelecimento)
        {
            return Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && !f.ExibePreco && f.Ativo).ToList();
        }

        public IList<ServicoEstabelecimento> ListarAtivosAssociadosAProfissionaisPorCategoriaEstabelecimento(Int32 codigoServicoCategoriaEstabelecimento)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(ServicoEstabelecimento), "Resultado");
            AdicionarCriterioProfissionaisAssociadosAoServico(dc);
            AdicionarCriterioIdServicoCategoriaEstabelecimento(codigoServicoCategoriaEstabelecimento, dc);
            AdicionarCriterioServicoEstabelecimentoAtivo(true, dc);
            AdicionarCriterioComValorOuGratis(dc);
            AdicionarOrdenacaoPorNomeServicoEstabelecimento(dc);
            return DetachedLoadAll(dc);
        }

        public List<ServicoEstabelecimento> ListarAtivosPorEstabelecimento(Int32 codigoEstabelecimento, bool trazerApenasOsComSituacaoTributaria = false)
        {
            var retorno = ObterServicoEstabelecimentosAtivos(codigoEstabelecimento);

            if (trazerApenasOsComSituacaoTributaria)
                retorno = retorno.Where(r => r.IdSituacaoTributaria != null && r.IdSituacaoTributaria > 0);

            return retorno.Fetch(f => f.Servico).ToList();
        }

        public int RetornarQuantidadeDeServicosAtivosPorEstabelecimento(Int32 codigoEstabelecimento, bool trazerApenasOsComSituacaoTributaria = false)
        {
            var retorno = ObterServicoEstabelecimentosAtivos(codigoEstabelecimento);

            if (trazerApenasOsComSituacaoTributaria)
            {
                retorno = retorno.Where(r => r.IdSituacaoTributaria != null && r.IdSituacaoTributaria > 0);
            }

            return retorno.Count();
        }

        public List<ServicoEstabelecimento> ListarTodosPorEstabelecimento(Int32 codigoEstabelecimento, bool trazerApenasOsComSituacaoTributaria = false)
        {
            var retorno = ObterTodosServicoEstabelecimentos(codigoEstabelecimento);

            if (trazerApenasOsComSituacaoTributaria)
                retorno = retorno.Where(r => r.IdSituacaoTributaria != null && r.IdSituacaoTributaria > 0);

            return retorno.Fetch(f => f.Servico).ToList();
        }

        public IQueryable<ServicoEstabelecimento> ListarAtivosPorEstabelecimentoAssociadosAProfissional(Int32 idEstabelecimento)
        {
            var query = Queryable();
            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            query = query.Where(f => f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo));
            query = query.Where(f => f.Ativo);

            return query.OrderBy(f => f.Nome);
        }

        public List<KeyValuePair<int, string>> ListarKeyValueAtivosPorEstabelecimentoAssociadosAProfissional(int idEstabelecimento)
        {
            var query = ListarAtivosPorEstabelecimentoAssociadosAProfissional(idEstabelecimento);

            return query.Select(f => new KeyValuePair<int, string>(f.IdServicoEstabelecimento, f.Nome)).ToList();
        }

        public IList<ServicoEstabelecimento> ListarAtivosPorPessoaProfissionalEstabelecimento(Int32 idPessoa, Int32 codigoEstabelecimento)
        {
            return Queryable()
                     .Where(
                        se => se.Ativo &&
                              se.EstabelecimentoProfissionalServicoLista.Any(
                                eps => eps.Ativo &&
                                       eps.EstabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa == idPessoa &&
                                       eps.EstabelecimentoProfissional.Estabelecimento.IdEstabelecimento == codigoEstabelecimento
                              )).ToList();
        }

        private IQueryable<ServicoEstabelecimento> ObterQueryableDeServicosAtivosPorProfissionalEstabelecimento(int? codigoProfissional, int codigoEstabelecimento)
        {
            var query = Queryable();

            query = query.Where(f => f.Ativo);

            if (codigoProfissional > 0)
                query = query.Where(f => f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo && g.EstabelecimentoProfissional.Profissional.IdProfissional == codigoProfissional));
            else if (codigoProfissional == 0)
                query = query.Where(f => f.EhUsadoNaFilaDeEspera);

            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);

            query = query.OrderBy(f => f.Nome);

            return query;
        }

        public List<DadosBasicosDeServicoEstabelecimentoDTO> ListarDadosBasicosDeServicosAtivosPorProfissionalEstabelecimento(int? codigoProfissional, int codigoEstabelecimento)
        {
            var query = ObterQueryableDeServicosAtivosPorProfissionalEstabelecimento(codigoProfissional, codigoEstabelecimento);

            return query.Select(s => new DadosBasicosDeServicoEstabelecimentoDTO()
            {
                Codigo = s.IdServicoEstabelecimento,
                Nome = s.Nome,
                Duracao = s.Duracao,
                Selecionado = false
            }).ToList();
        }

        public IList<ServicoEstabelecimento> ListarAtivosPorProfissionalEstabelecimento(int? codigoProfissional, Int32 codigoEstabelecimento)
        {
            var dados = ObterQueryableDeServicosAtivosPorProfissionalEstabelecimento(codigoProfissional, codigoEstabelecimento);

            return dados.ToList();
        }

        public List<ServicoEstabelecimento> ListarServicosEstabelecimento(ParametrosFiltroServicosEstabelecimento filtro)
        {
            var retorno = Queryable();

            if (!String.IsNullOrEmpty(filtro.ConteudoFiltro))
            {
                if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.NomeEstabelecimento)
                    retorno = AdicionarCriterioNomeEstabelecimento(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.CategoriaPadrao)
                    retorno = AdicionarCriterioNomeCategoriaPadrao(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.CategoriaEstabelecimento)
                    retorno = AdicionarCriterioNomeCategoriaEstabelecimento(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.ServicoPadrao)
                    retorno = AdicionarCriterioNomeServicoPadrao(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.ServicoEstabelecimento)
                    retorno = AdicionarCriterioNome(filtro.ConteudoFiltro, retorno);
            }

            retorno.OrderBy(f => f.ServicoCategoriaEstabelecimento.Nome).ThenBy(p => p.Nome);
            return retorno.ToList();
        }

        public ResultadoPaginado<ServicoEstabelecimento> ListarServicosEstabelecimentoPaginado(ParametrosFiltroServicosEstabelecimento filtro)
        {
            var retorno = Queryable();

            if (!String.IsNullOrEmpty(filtro.ConteudoFiltro))
            {
                if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.NomeEstabelecimento)
                    retorno = AdicionarCriterioNomeEstabelecimento(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.CategoriaPadrao)
                    retorno = AdicionarCriterioNomeCategoriaPadrao(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.CategoriaEstabelecimento)
                    retorno = AdicionarCriterioNomeCategoriaEstabelecimento(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.ServicoPadrao)
                    retorno = AdicionarCriterioNomeServicoPadrao(filtro.ConteudoFiltro, retorno);
                else if (filtro.FiltroPorServicoEstabelecimento == FiltroPorServicoEstabelecimentoEnum.ServicoEstabelecimento)
                    retorno = AdicionarCriterioNome(filtro.ConteudoFiltro, retorno);
            }

            retorno.OrderBy(f => f.ServicoCategoriaEstabelecimento.Nome).ThenBy(p => p.Nome);

            int posicaoInicial = (filtro.ParametrosPaginacao.RegistrosPorPagina * filtro.ParametrosPaginacao.Pagina - filtro.ParametrosPaginacao.RegistrosPorPagina);
            filtro.ParametrosPaginacao.TotalItens = retorno.Count();
            retorno = retorno.Skip(posicaoInicial).Take(filtro.ParametrosPaginacao.RegistrosPorPagina);

            var lista = retorno.ToList();
            return new ResultadoPaginado<ServicoEstabelecimento>(lista, filtro.ParametrosPaginacao);
        }

        public ServicoEstabelecimento ObterPorCodigoInterno(string codigoInterno, int idEstabelecimento)
        {
            var query = Queryable();
            return query.FirstOrDefault(p => p.CodigoInterno == codigoInterno && p.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public ServicoEstabelecimento ObterPorServicoPorEstabelecimentoEServicoPadrao(Int32 codigoEstabelecimento, Int32 codigoServicoPadrao)
        {
            var dc = DetachedCriteria.For(typeof(ServicoEstabelecimento));
            AdicionarCriterioIdServico(codigoServicoPadrao, dc);
            AdicionarCriterioIdEstabelecimentoServico(codigoEstabelecimento, dc);
            return Load(dc);
        }

        public Int32 ObterIdServicoPorCodigoInterno(String codigoInterno, Int32 idEstabelecimento)
        {
            return Queryable().Where(p => p.CodigoInterno == codigoInterno && p.Estabelecimento.IdEstabelecimento == idEstabelecimento).Select(p => p.IdServicoEstabelecimento).FirstOrDefault();
        }

        public ServicoEstabelecimento ObterServicoEstabelecimento(Int32 codigoServico, Int32 codigoEstabelecimento)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(ServicoEstabelecimento));
            AdicionarCriterioIdEstabelecimentoServico(codigoEstabelecimento, dc);
            AdicionarCriterioIdServicoEstabelecimento(codigoServico, dc);
            AdicionarCriterioServicoEstabelecimentoAtivo(true, dc);
            return Load(dc);
        }

        public ServicoEstabelecimento ObterServicoEstabelecimentoSemVerificarFlagAtivo(Int32 codigoServico, Int32 codigoEstabelecimento)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(ServicoEstabelecimento));
            AdicionarCriterioIdEstabelecimentoServico(codigoEstabelecimento, dc);
            AdicionarCriterioIdServicoEstabelecimento(codigoServico, dc);
            return Load(dc);
        }

        public IQueryable<ServicoEstabelecimento> ObterServicoEstabelecimentosAtivos(int codigoEstabelecimento)
        {
            var retorno = Queryable();

            retorno = FiltroEstabelecimento(codigoEstabelecimento, retorno);
            retorno = FiltroAtivo(retorno);
            retorno = OrdenacaoPorNomeCategoriaServicoEstabelecimento(retorno);
            retorno = OrdenacaoPorNomeServicoEstabelecimento(retorno);
            return retorno;
        }

        public IQueryable<ServicoEstabelecimento> ObterTodosServicoEstabelecimentos(int codigoEstabelecimento)
        {
            var retorno = Queryable();

            retorno = FiltroEstabelecimento(codigoEstabelecimento, retorno);
            retorno = OrdenacaoPorNomeCategoriaServicoEstabelecimento(retorno);
            retorno = OrdenacaoPorNomeServicoEstabelecimento(retorno);
            return retorno;
        }

        public bool PossuiProfissionalAssociado(int idServicoEstabelecimento)
        {
            return Queryable().Any(f => f.IdServicoEstabelecimento == idServicoEstabelecimento
                && f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo));
        }

        public Int32 QuantidadeDeServicosEstabelecimentoNoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Count(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo);
        }

        public void SaveOrUpdate(ServicoEstabelecimento entity)
        {
            if (entity.IdServicoEstabelecimento > 0)
                Update(entity);
            else
                SaveNew(entity);
        }

        public Boolean VerificarSeServicoJaEstaAssociadoAoEstabelecimento(Int32 codigoEstabelecimento, Int32 codigoServico)
        {
            var dc = DetachedCriteria.For(typeof(ServicoEstabelecimento));
            AdicionarCriterioIdServico(codigoServico, dc);
            AdicionarCriterioIdEstabelecimentoServico(codigoEstabelecimento, dc);
            AdicionarCriterioServicoEstabelecimentoAtivo(true, dc);
            return Exists(dc);
        }

        public ServicoEstabelecimento ObterServicoDoEstabelecimentoPeloNome(Int32 codigoEstabelecimento, String nomeServicoEstabelecimento, Int32 codigoServicoEstabelecimento = 0)
        {
            var query = Queryable();

            if (codigoServicoEstabelecimento == 0)
                query = FiltroEstabelecimento(codigoEstabelecimento, query);
            else
                query = query.Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento && f.IdServicoEstabelecimento != codigoServicoEstabelecimento);
            return FiltroNomeServicoEstabelecimento(nomeServicoEstabelecimento, query);
        }

        public IQueryable<ServicoEstabelecimento> ListarOsDisponiveisNoGoogleReserve()
        {
            var estabelecimentos = Domain.Pessoas.EstabelecimentoRepository.ListarOsDisponiveisNoGoogleReserve();

            var se = Domain.Pessoas.ServicoEstabelecimentoRepository.Queryable();
            var eps = Domain.Pessoas.EstabelecimentoProfissionalServicoRepository.Queryable().Where(f => f.Ativo && f.PermiteAgendamentoHotsite && f.EstabelecimentoProfissional.Ativo);

            return se.Where(f => f.PrecoFixo && f.TipoPreco == TipoPrecoEnum.Fixo && f.Preco >= 1 && f.Preco < 10000 && f.Duracao > 0 && f.Ativo && estabelecimentos.Contains(f.Estabelecimento) && eps.Any(g => g.ServicoEstabelecimento == f));
        }
        public List<ServicoEstabelecimento> ListarServicosEstabelecimentoMaisUsadosPeloProfissionalDesde(int idEstabelecimento, int idProfissional, DateTime dataDesde)
        {
            var horarios = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento
                    && f.Profissional.IdProfissional == idProfissional
                    && f.DataInicio >= dataDesde.Date
                    && f.DataInicio <= Calendario.Agora()
                    && f.Status != StatusHorarioEnum.Cancelado)
                .Select(f => f.ServicoEstabelecimento)
                .ToList();

            var retorno = horarios.GroupBy(f => f)
                .OrderByDescending(f => f.Count())
                .Select(f => f.Key);

            return retorno.ToList();
        }

        #region Critérios

        private static void AdicionarCriterioComValorOuGratis(DetachedCriteria dc)
        {
            dc.Add(Restrictions.Or(
                Restrictions.Gt(PropertyNames.Pessoas.ServicoEstabelecimento.Preco, (decimal)0),
                Restrictions.Eq(PropertyNames.Pessoas.ServicoEstabelecimento.TipoPreco, (TipoPreco)TipoPrecoEnum.Gratuito)
                ));
        }

        private static IQueryable<ServicoEstabelecimento> AdicionarCriterioNome(String nome, IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.Nome.Contains(nome));
        }

        private static IQueryable<ServicoEstabelecimento> AdicionarCriterioNomeCategoriaEstabelecimento(String nome, IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.ServicoCategoriaEstabelecimento.Nome.Contains(nome));
        }

        private static IQueryable<ServicoEstabelecimento> AdicionarCriterioNomeCategoriaPadrao(String nome, IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.Servico.ServicoCategoria.Nome.Contains(nome));
        }

        private static IQueryable<ServicoEstabelecimento> AdicionarCriterioNomeEstabelecimento(String nome, IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.Estabelecimento.PessoaJuridica.NomeFantasia.Contains(nome));
        }

        private static IQueryable<ServicoEstabelecimento> AdicionarCriterioNomeServicoPadrao(String nome, IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.Servico.Nome.Contains(nome));
        }

        private static IQueryable<ServicoEstabelecimento> FiltroAtivo(IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.Ativo);
        }

        private static IQueryable<ServicoEstabelecimento> FiltroEstabelecimento(Int32 codigoEstabelecimento, IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);
        }

        private static IQueryable<ServicoEstabelecimento> OrdenacaoPorNomeCategoriaServicoEstabelecimento(IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.OrderBy(f => f.ServicoCategoriaEstabelecimento.Nome);
        }

        private static IQueryable<ServicoEstabelecimento> OrdenacaoPorNomeServicoEstabelecimento(IQueryable<ServicoEstabelecimento> retorno)
        {
            return retorno.OrderBy(f => f.Nome);
        }

        private static ServicoEstabelecimento FiltroNomeServicoEstabelecimento(String nomeServicoEstabelecimento, IQueryable<ServicoEstabelecimento> query)
        {
            return query.FirstOrDefault(f => f.Nome.Equals(nomeServicoEstabelecimento));
        }

        private void AdicionarCriterioIdEstabelecimentoServico(Int32 idEstabelecimento, DetachedCriteria dc)
        {
            var propertyName = String.Format("{0}.{1}", typeof(Estabelecimento).Name, PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento);
            dc.Add(Restrictions.Eq(propertyName, idEstabelecimento));
        }

        private void AdicionarCriterioIdServico(Int32 idServico, DetachedCriteria dc)
        {
            var propertyName = String.Format("{0}.{1}", typeof(Servico).Name, PropertyNames.Pessoas.Servico.IdServico);
            dc.Add(Restrictions.Eq(propertyName, idServico));
        }

        private void AdicionarCriterioIdServicoCategoriaEstabelecimento(Int32 idServicoCategoriaEstabelecimento, DetachedCriteria dc)
        {
            var propertyName = String.Format("{0}.{1}", typeof(ServicoCategoriaEstabelecimento).Name,
                                                        PropertyNames.Pessoas.ServicoCategoriaEstabelecimento.Codigo);
            dc.Add(Restrictions.Eq(propertyName, idServicoCategoriaEstabelecimento));
        }

        private void AdicionarCriterioIdServicoEstabelecimento(Int32 idServicoEstabelecimento, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.ServicoEstabelecimento.IdServicoEstabelecimento, idServicoEstabelecimento));
        }

        private void AdicionarCriterioProfissionaisAssociadosAoServico(DetachedCriteria dc)
        {
            var sq = DetachedCriteria.For(typeof(EstabelecimentoProfissionalServico));
            sq.SetProjection(Projections.Id());
            sq.CreateAlias(PropertyNames.Pessoas.EstabelecimentoProfissionalServico.EstabelecimentoProfissional, PropertyNames.Pessoas.EstabelecimentoProfissionalServico.EstabelecimentoProfissional);

            var propertyNameEstabelecimentoProfissionalAtivo = String.Format("{0}.{1}", PropertyNames.Pessoas.EstabelecimentoProfissionalServico.EstabelecimentoProfissional, PropertyNames.Pessoas.EstabelecimentoProfissional.Ativo);
            sq.Add(Restrictions.Eq(propertyNameEstabelecimentoProfissionalAtivo, true));

            sq.Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissionalServico.Ativo, true));
            sq.Add(Restrictions.EqProperty("ServicoEstabelecimento.IdServicoEstabelecimento", "Resultado.IdServicoEstabelecimento"));
            dc.Add(Subqueries.Exists(sq));
        }

        private void AdicionarCriterioServicoEstabelecimentoAtivo(Boolean ativo, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.ServicoEstabelecimento.Ativo, ativo));
        }

        private void AdicionarOrdenacaoPorNomeServicoEstabelecimento(DetachedCriteria dc)
        {
            dc.AddOrder(new Order(PropertyNames.Pessoas.ServicoEstabelecimento.Nome, true));
        }

        #endregion Critérios

        public List<ServicoEstabelecimento> ListarServicosQuePossuamNomenclaturaParaNotaFiscalDoConsumidorDefinida(int idEstabelecimento)
        {
            var busca = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.CodigoNCM != null);

            return busca.ToList();
        }

        public List<ServicoEstabelecimento> ListarPorEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento).ToList();
        }

        public List<ServicoEstabelecimento> ListarServicosPorServicoCategoriaEstabelecimento(int idServicoCategoriaEstabelecimento)
        {
            var query = Queryable();
            query = query.Where(p => p.ServicoCategoriaEstabelecimento.Codigo == idServicoCategoriaEstabelecimento);
            return query.ToList();
        }

        public List<KeyValuePair<int, string>> ListarAtivosPorProfissionalEstabelecimentoKeyValue(Int32 idEstabelecimento, Int32 idProfissionalEstabelecimento)
        {
            var dados = Queryable();
            dados = dados.Where(f => f.Ativo);
            if (idProfissionalEstabelecimento > 0)
                dados = dados.Where(f => f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo && g.EstabelecimentoProfissional.Codigo == idProfissionalEstabelecimento));
            else
            {
                var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
                if (estabelecimento.EstabelecimentoConfiguracaoGeral.FilaDeEsperaEstaAtiva)
                {
                    dados = dados.Where(f => f.EhUsadoNaFilaDeEspera);
                }
            }
            dados = dados.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return dados.Select(f => new KeyValuePair<int, string>(f.IdServicoEstabelecimento, f.Nome)).OrderBy(f => f.Value).ToList();
        }

        public List<KeyValuePair<int, string>> ListarKeyValue(int idEstabelecimento, bool somenteAtivos = true, List<int> idsCategorias = null)
        {
            var query = Queryable();

            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (idsCategorias != null && idsCategorias.Any())
                query = query.Where(f => idsCategorias.Contains(f.ServicoCategoriaEstabelecimento.Codigo));

            if (somenteAtivos)
                query = query.Where(f => f.Ativo);

            var lista = query.Select(
                f =>
                    new
                    {
                        f.IdServicoEstabelecimento,
                        f.Nome
                    }).ToList();

            return
                lista.Select(
                    f =>
                        new KeyValuePair<int, string>(f.IdServicoEstabelecimento, f.Nome))
                            .OrderBy(f => f.Value)
                            .ToList();
        }

        public List<KeyValuePair<int, string>> ListarServicosAtivosComValorFixoMaiorQue1(int idEstabelecimento)
        {
            var query = Queryable();

            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            query = query.Where(f => f.TipoPreco == TipoPrecoEnum.Fixo);
            query = query.Where(f => f.Ativo);
            query = query.Where(f => f.Preco > 1);
            
            return query.Select(f => new KeyValuePair<int, string>(f.IdServicoEstabelecimento, f.Nome)).ToList();
        }

        public List<KeyValuePair<string, string>> ListarServicosPorProfissionalKeyValue(Estabelecimento estabelecimento, int idProfissional, string nomeFiltro, bool listarPorCodigoInterno = false)
        {
            var usaCodigoInterno = estabelecimento.EstabelecimentoConfiguracaoGeral.UtilizarCodigoInterno;

            var retorno = Queryable();
            retorno = FiltroAtivo(retorno);

            if (idProfissional > 0)
            {
                retorno = retorno.Where(f => f.EstabelecimentoProfissionalServicoLista
                    .Any(g => g.Ativo && g.EstabelecimentoProfissional.Profissional.IdProfissional == idProfissional));
            }
            else if (estabelecimento.EstabelecimentoConfiguracaoGeral.FilaDeEsperaEstaAtiva)
            {
                retorno = retorno.Where(f => f.EhUsadoNaFilaDeEspera);
            }

            retorno = FiltroEstabelecimento(estabelecimento.IdEstabelecimento, retorno);

            if (!string.IsNullOrEmpty(nomeFiltro))
                retorno = retorno
                    .Where(p => p.Nome.ToLower().Replace("'", "").Contains(nomeFiltro.ToLower().Replace("'", "")));

            var listaKeyValuePair = retorno
                .Select(f => new KeyValuePair<string, string>(
                    usaCodigoInterno && listarPorCodigoInterno ? f.CodigoInterno : f.IdServicoEstabelecimento.ToString(), 
                    f.Nome))
                .OrderBy(f => f.Value)
                .ToList();

            return listaKeyValuePair;
        }

        public Boolean TemServicoComPromocaoNoMomento(int idEstabelecimento)
        {
            var data = Calendario.Agora().Date;

            var query = Queryable();
            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            query = query.Where(f => f.Ativo);
            query = query.Where(f => f.Promocao.TipoVigencia == TipoPromocaoVigenciaEnum.Sempre.GetHashCode() || (f.Promocao.TipoVigencia == TipoPromocaoVigenciaEnum.PorPeriodo.GetHashCode() &&
                            (!f.Promocao.DataInicio.HasValue || f.Promocao.DataInicio <= data.Date) && (!f.Promocao.DataFim.HasValue || f.Promocao.DataFim >= data.Date)));

            return query.Any();
        }

        public List<KeyValuePair<int, string>> ListarKeyValueParaFiltroDeRankingDeServicos(int idEstabelecimento, List<int> idsCategorias)
        {
            var query = Queryable();

            var horariosExistentes = Domain.Pessoas.HorarioRepository.Queryable().Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            query = query.Where(f => horariosExistentes.Any(h => h.ServicoEstabelecimento.IdServicoEstabelecimento == f.IdServicoEstabelecimento));

            if (idsCategorias != null && idsCategorias.Any())
                query = query.Where(f => idsCategorias.Contains(f.ServicoCategoriaEstabelecimento.Codigo));

            var lista = query.OrderByDescending(s => s.Ativo).OrderBy(s => s.Nome).Select(
                f =>
                    new
                    {
                        f.IdServicoEstabelecimento,
                        f.Nome,
                        f.Ativo
                    }).ToList();

            return
                lista.Select(
                    f =>
                        new KeyValuePair<int, string>(f.IdServicoEstabelecimento, f.Nome + (f.Ativo ? "" : " [Inativo]")))
                            .ToList();
        }

        public List<ServicoEstabelecimento> ListarServicosComResgateDePontosAtivo(int idEstabelecimento)
        {
            var query = Queryable().Where(s => s.Estabelecimento.IdEstabelecimento == idEstabelecimento && s.ClientePodeResgatarPontos && s.Ativo);
            return query.ToList();
        }

        public List<ItemDoProgramaDTO> ListarItensDeServicoDoPrograma(int idEstabelecimento, string textoDoFiltroDeItens, TipoConsultaItensDoProgramaEnum tipoConsulta)
        {
            var query = Queryable().Where(s => s.Estabelecimento.IdEstabelecimento == idEstabelecimento && s.Ativo == true);
            List<ItemDoProgramaDTO> resultado = null;

            if (!String.IsNullOrEmpty(textoDoFiltroDeItens))
            {
                query = query.Where(s => s.Nome.Contains(textoDoFiltroDeItens) || s.ServicoCategoriaEstabelecimento.Nome.Contains(textoDoFiltroDeItens));
            }

            if (tipoConsulta == TipoConsultaItensDoProgramaEnum.DaoPontosNaCompra)
            {
                query = query.Where(s => s.ClienteGanhaPontos == true);
            }
            else if (tipoConsulta == TipoConsultaItensDoProgramaEnum.PodemSerResgatados)
            {
                query = query.Where(s => s.ClientePodeResgatarPontos == true);
            }
            else if (tipoConsulta == TipoConsultaItensDoProgramaEnum.FazemParte)
            {
                query = query.Where(s => s.ClientePodeResgatarPontos == true || s.ClienteGanhaPontos == true);
            }

            resultado = query.Select(s => new ItemDoProgramaDTO()
            {
                IdItemDoPrograma = s.IdServicoEstabelecimento,
                Categoria = s.ServicoCategoriaEstabelecimento.Nome,
                Nome = s.Nome,
                Preco = s.Preco,
                QuantidadeDePontosParaResgate = s.PontosNecessariosParaResgate,
                ClienteGanhaPonto = s.ClienteGanhaPontos,
                ClientePodeResgatar = s.ClientePodeResgatarPontos
            }).ToList();

            return resultado;
        }

        public List<ServicoEstabelecimento> ListarServicosComMenorPontoNecessarioParaResgaste(int idEstabelecimento)
        {
            List<ServicoEstabelecimento> resultado = new List<ServicoEstabelecimento>();

            var query = Queryable().Where(s => s.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                               s.ClientePodeResgatarPontos && s.Ativo);

            if (query.Any())
            {
                int menorPontoNecessarioParaResgaste = query.Select(p => p.PontosNecessariosParaResgate).Min();

                resultado = query.Where(p => p.PontosNecessariosParaResgate == menorPontoNecessarioParaResgaste).ToList();
            }

            return resultado;
        }

        public bool EstabelecimentoPossuiServicosComResgateNoProgramaDeFidelidade(int idEstabelecimento)
        {
            return Queryable()
                .Where(pf => pf.Estabelecimento.IdEstabelecimento == idEstabelecimento && pf.ClientePodeResgatarPontos)
                .Select(pf => (int?)pf.IdServicoEstabelecimento)
                .Any();
        }

        public IQueryable<ServicoEstabelecimento> ObterQueryableDeServicosVisiveisNoHotSite(int? idEstabelecimento = null, int? idProfissionalAssociado = null, int? idEstabelecimentoProfissionalAssociado = null, int? idCategoriaPortal = null, bool servicoIndisponivelParaClientes = false, int? idFranquia = null)
        {
            var queryable = Queryable();
            if (servicoIndisponivelParaClientes)
            {
                queryable = queryable.Where(se => se.Ativo && (se.Preco != 0 || se.TipoPreco == TipoPrecoEnum.Gratuito) && !se.ServicoIndiposnivelParaCliente);
            }
            else
            {
                queryable = queryable.Where(se => se.Ativo && (se.Preco != 0 || se.TipoPreco == TipoPrecoEnum.Gratuito));
            }


            if (idEstabelecimento.HasValue && idEstabelecimento.Value > 0)
                queryable = queryable.Where(se => se.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (idFranquia.HasValue && idFranquia.Value > 0)
                queryable = queryable.Where(se => se.Estabelecimento.FranquiaEstabelecimento.Franquia.Id == idFranquia.Value);

            if (idProfissionalAssociado.HasValue && idProfissionalAssociado.Value > 0)
                queryable = queryable.Where(f => f.EstabelecimentoProfissionalServicoLista.Any(h => h.Ativo && h.EstabelecimentoProfissional.Ativo && h.EstabelecimentoProfissional.Profissional.IdProfissional == idProfissionalAssociado));
            else if (idEstabelecimentoProfissionalAssociado.HasValue && idEstabelecimentoProfissionalAssociado.Value > 0)
                queryable = queryable.Where(se => se.EstabelecimentoProfissionalServicoLista.Any(eps => eps.Ativo && eps.EstabelecimentoProfissional.Ativo && eps.EstabelecimentoProfissional.Codigo == idEstabelecimentoProfissionalAssociado));
            else
                queryable = queryable.Where(se => se.EstabelecimentoProfissionalServicoLista.Any(eps => eps.Ativo && eps.EstabelecimentoProfissional.Ativo));

            if (idCategoriaPortal != null)
                queryable = queryable.Where(se => se.Servico.ServicoCategoria.CategoriaPortal.Id == idCategoriaPortal);

            return queryable;
        }

        public int ObterIdEstabelecimentoDoServico(int idServicoEstabelecimento)
        {
            int? idEstabelecimento = StatelessQueryable().Where(se => se.IdServicoEstabelecimento == idServicoEstabelecimento).Select(se => se.Estabelecimento.IdEstabelecimento).FirstOrDefault();
            return idEstabelecimento ?? 0;
        }

        public int ObterDuracaoDoServico(int idServicoEstabelecimento)
        {
            int? duracao = StatelessQueryable().Where(se => se.IdServicoEstabelecimento == idServicoEstabelecimento).Select(se => se.Duracao).FirstOrDefault();
            return duracao ?? 0;
        }

        public ServicoEstabelecimento ObterPorId(int idServicoEstabelecimento)
        {
            return Queryable().FirstOrDefault(se => se.IdServicoEstabelecimento == idServicoEstabelecimento);
        }

        public ServicoEstabelecimento ObterServicoEstabelecimentoAtivoPorCodigoInterno(string codigoInterno, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(s => s.CodigoInterno == codigoInterno && s.Estabelecimento.IdEstabelecimento == idEstabelecimento && s.Ativo);
        }

        public IList<PagamentosAntecipados.DTO.ServicoHabilitadoParaPagamentoDTO> ListarServicosPagamentoOnlineSegundoRegra(int idEstabelecimento, decimal valorMaximo)
        {
            return Queryable().Where(f =>
                                     f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                     f.ExibePreco &&
                                     f.Ativo &&
                                     f.TipoPreco == TipoPrecoEnum.Fixo &&
                                     f.Preco <= valorMaximo &&
                                     f.Preco > 0
                                     ).Select(s =>
                                     new PagamentosAntecipados.DTO.ServicoHabilitadoParaPagamentoDTO
                                     {
                                         IdServicoEstabelecimento = s.IdServicoEstabelecimento,
                                         IdEstabelecimento = s.Estabelecimento.IdEstabelecimento,
                                         Categoria = s.ServicoCategoriaEstabelecimento.Nome,
                                         Servico = s.Nome,
                                         Preco = String.Format("{0:c}", s.Preco),
                                         Ativo = false
                                     }).OrderBy(s => s.Categoria).ToList();
        }

        public int TotalDeServicosDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Count(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Ativo);
        }

        public DateTime? ObterDataPrimeiroServicoNaoPadraoCadastrado(int idEstabelecimento)
        {
            return Queryable().Where(se => se.Estabelecimento.IdEstabelecimento == idEstabelecimento && se.Servico == null)
                .OrderBy(se => se.DataCadastro)
                .Select(se => se.DataCadastro)
                .FirstOrDefault();
        }

        public List<KeyValuePair<int, string>> ListarKeyValueComIdENomeDosServicosAtivos(int idEstabelecimento)
        {
            return Queryable()
                .Where(se => se.Estabelecimento.IdEstabelecimento == idEstabelecimento && se.Ativo)
                .Select(se => new KeyValuePair<int, string>(se.IdServicoEstabelecimento, se.Nome))
                .ToList();
        }


        public List<KeyValuePair<int, string>> ListarKeyValue(int idEstabelecimento, bool ativo, string conteudoFiltro)
        {
            var query = Queryable().Where(e => e.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            if (ativo)
                query = query.Where(s => s.Ativo);

            if (!string.IsNullOrEmpty(conteudoFiltro))
                query = query.Where(s => s.Nome.Contains(conteudoFiltro));

            return query
                .Select(se => new KeyValuePair<int, string>(se.IdServicoEstabelecimento, se.Nome))
                .ToList();
        }

        public decimal ObterPrecoPorId(int idServico, int idEstabelecimento)
        {
            return Queryable()
                   .Where(f => f.IdServicoEstabelecimento == idServico && f.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                   .Select(f => f.Preco)
                   .FirstOrDefault();
        }

        public ServicoEstabelecimento ObterBaseadoNoModelo(int idEstabelecimento, int idServicoEstabelecimentoDoModelo)
        {
            return Queryable()
                   .FirstOrDefault(f => 
                       f.Estabelecimento.IdEstabelecimento == idEstabelecimento && 
                       f.ServicoEstabelecimentoModelo.IdServicoEstabelecimento == idServicoEstabelecimentoDoModelo);
        }

        public int ObterCodigoServicoCategoriaEstabelecimento(int idServicoEstabelecimento)
        {
            return Queryable().Where(c => c.IdServicoEstabelecimento == idServicoEstabelecimento).Select(c => c.ServicoCategoriaEstabelecimento.Codigo).FirstOrDefault();
        }

        public IQueryable<ServicoEstabelecimento> QueryableAtivosComPrecoFixo(int idEstabelecimento)
        {
            return Queryable(cacheable: true)
                .Where(s => s.Estabelecimento.IdEstabelecimento == idEstabelecimento && 
                            s.TipoPreco == TipoPrecoEnum.Fixo &&
                            s.EstabelecimentoProfissionalServicoLista.Any(eps => eps.Ativo) &&
                            s.Ativo);
        }
    }
}
