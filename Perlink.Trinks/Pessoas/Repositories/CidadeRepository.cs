﻿using Perlink.Trinks.RPS.GeradorDeArquivo;
using System;
using System.Collections.Generic;
using System.Linq;
using DL = Perlink.Trinks.Domain;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class CidadeRepository : ICidadeRepository
    {

        public List<Cidade> ListarCidadesContendoEstabelecimentos(int? codigoUF)
        {
            var bairros = DL.Pessoas.BairroRepository.ListarBairrosContendoEstabelecimentos(null);
            var dados = bairros.Select(f => f.Cidade).Distinct();

            if (codigoUF.HasValue && codigoUF > 0)
                dados = FiltroEstado(dados, codigoUF.Value);

            return dados.ToList();
        }

        private IEnumerable<Cidade> FiltroEstado(IEnumerable<Cidade> dados, int codigoUF)
        {
            return dados.Where(f => f.UF.IdUF == codigoUF).OrderBy(x => x.Nome);
        }

        public bool ExisteImplementacaoDeNFSeParaCidade(string codigoIBGE)
        {
            return GeradorDeArquivoRPS.ExisteImplementacaoParaCidade(codigoIBGE);
        }

        public long ObterCodigoIBGEDoClienteOuEstabelecimento(UF unidadeFederativaDoCliente, String nomeDaCidadeEmTexto,
                                                              Cidade cidadeDoEstabelecimento)
        {
            /*
                Recuperar o código IBGE através de um texto livre da cidade (atualmente o endereço do cliente
                no trinks, permite isso.
                O código IBGE é necessário para a emissão de cupom fiscal, p. ex. Caso não seja encontrado
                este código, será retornado o do estabelecimento.
            */
            var codigoDoIBGEDoEstabelecimento = cidadeDoEstabelecimento == null ? 0 : Convert.ToInt64(cidadeDoEstabelecimento.CodigoIBGE);
            if (codigoDoIBGEDoEstabelecimento <= 0 && string.IsNullOrEmpty(nomeDaCidadeEmTexto)) return 0;

            var buscaPorTextoDoNomeDaCidadeDoCliente = Queryable().Where(f => f.UF == unidadeFederativaDoCliente
                                                                           && f.Nome == nomeDaCidadeEmTexto).ToList();

            if (buscaPorTextoDoNomeDaCidadeDoCliente.Count == 0) return codigoDoIBGEDoEstabelecimento;
            if (buscaPorTextoDoNomeDaCidadeDoCliente.First().CodigoIBGE == null) return codigoDoIBGEDoEstabelecimento;

            return Convert.ToInt64(buscaPorTextoDoNomeDaCidadeDoCliente.First().CodigoIBGE);
        }

        public Cidade Obter(string nomeCidade, string uf)
        {
            return Queryable(true)
                    .FirstOrDefault(f => f.UF.Sigla == uf.ToUpper() && f.Nome.ToLower() == nomeCidade.ToLower());
        }

        public Cidade ObterPorCodigoIBGE(string codigoIBGE)
        {
            return Queryable(true).FirstOrDefault(f => f.CodigoIBGE == codigoIBGE);
        }

        public IEnumerable<KeyValuePair<string, string>> ObterKeyValueCidadeCodigIbge (string filtro)
        {
            return Queryable().Where(c => c.Nome.Contains(filtro) && c.CodigoIBGE != null && c.CodigoIBGE != "")
                .Select(c => new KeyValuePair<string, string>(c.CodigoIBGE.ToString(), $"{c.Nome} - {c.UF.Sigla}")).ToList()
                .OrderBy(c => c.Value);
                
        }
    }
}