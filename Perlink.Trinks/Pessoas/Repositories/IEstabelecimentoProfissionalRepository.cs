﻿using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.DTO.Filtros;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Filtros;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IEstabelecimentoProfissionalRepository
    {

        EstabelecimentoProfissional ObterProfissionalPelaPessoaJuridica(int idPessoaDaPessoaJuridica);

        bool VerificarPessoaJuridicaEhProfissionalParceiro(int idPessoaDaPessoaJuridica);

        bool VerificarSeProfissionalEhProfissionalParceiro(int idProfissional, int idEstabelecimento, bool verificarSeEstaAtivo);

        bool VerificarSeProfissionalEhProfissionalParceiro(PessoaJuridica pessoaJuridica, bool verificarSeEstaAtivo);

        bool VerificarPessoaJuridicaEhProfissionalParceiroDoEstabelecimento(int idEstabelecimento, int idPessoaDaPessoaJuridica);

        IQueryable<EstabelecimentoProfissional> QueryableProfissionaisParceirosDoEstabelecimento(int idEstabelecimento);

        IQueryable<EstabelecimentoProfissional> QueryableProfissionaisParceirosDoEstabelecimentoNotaUnificada(int idEstabelecimento);

        bool ComissaoPadraoFoiAlterada(EstabelecimentoProfissional entity);

        bool EhProfissionalDoEstabelecimento(Int32 idPessoa, Int32 idEstabelecimento);

        bool EhProfissionalPjDoEstabelecimento(Int32 idPessoaJuridica, Int32 idEstabelecimento);

        bool EstaAtivo(Int32 idPessoaFisica);

        bool ExisteEstabelecimentoProfissionalPorEmail(string email, int codigoEstabelecimento);

        bool ExisteProfissionalComAcessoAoPAT(Int32 idEstabelecimento);

        List<EstabelecimentoProfissional> ListarAssistente(int idProfissional, int idServico);

        IList<EstabelecimentoProfissional> ListarAssistentes(int? idProfissionalEstabelecimento, int? idServicoEstabelecimento);
        IList<EstabelecimentoProfissional> ListarAssistentes(int? idProfissionalEstabelecimento, int? idServicoEstabelecimento, Estabelecimento estabelecimentoAutenticado);

        List<KeyValuePair<string, string>> ListarAssistentesKeyValue(FiltroAssistenteProfissionalDTO filtroAssistenteProfissional);

        IList<EstabelecimentoProfissional> ListarAssociadosAoServicoDoEstabelecimento(Int32 idEstabelecimento, Int32 idServicoEstabelecimento);

        IQueryable<EstabelecimentoProfissional> ListarAtivosPorEstabelecimento(Int32 idEstabelecimento);

        IQueryable<EstabelecimentoProfissional> ObterQueryPorEstabelecimento(Int32 idEstabelecimento);

        //
        IEnumerable<KeyValuePair<string, string>> ListarIdProfissionaisPorEstabelecimento(Int32 idEstabelecimento);

        IQueryable<EstabelecimentoProfissional> ListarAtivosEComAgendaPorEstabelecimento(Int32 idEstabelecimento);
        bool EhAssistenteEmPeloMenosUmServico(int idPessoa);

        IList<EstabelecimentoProfissional> ListarComServicoAtivoPorEstabelecimento(int idEstabelecimento);

        IOrderedEnumerable<KeyValuePair<Int32, String>> ListarIdNomePessoaFisicaDosProfissionaisPorEstabelecimentoQueJaVenderamProdutos(int idEstabelecimento);

        IOrderedEnumerable<KeyValuePair<Int32, String>> ListarIdPessoaNomePessoaFisicaDosProfissionaisPorEstabelecimento(int idEstabelecimento, int idPessoaFisicaProfissional = 0);

        IOrderedEnumerable<KeyValuePair<Int32, String>> ListarIdPessoaNomePessoaFisicaDosProfissionaisPorEstabelecimentoAssociadosALancamento(int idEstabelecimento);

        List<KeyValuePair<String, String>> ListarIdProfissionalNomeOuApelidoComServicosAtivosECodigoPAT(int idEstabelecimento, bool exibirCodigoInterno);

        List<KeyValuePair<int, string>> ListarKeyValuePair(int idEstabelecimento);

        IList<EstabelecimentoProfissional> ListarPorEstabelecimento(Int32 idEstabelecimento, bool somenteAtivos = false);

        IList<EstabelecimentoProfissional> ListarProfissionaisComServicosAssociados(Int32 idEstabelecimento, bool somenteAtivos = false);

        List<KeyValuePair<int, string>> ListarProfissionaisKeyValue(Estabelecimento estabelecimento, string nomeFiltro, bool listarPorIdPessoa = false);

        IQueryable<ConsultaDeProfissionaisDTO> ListarPaginado(FiltroConsultaDeProfissionais filtro);

        IQueryable<ConsultaDeProfissionaisDTO> ListarPorNomeEmailOuCpf(FiltroConsultaDeProfissionais filtro);

        IQueryable<EstabelecimentoProfissional> ListarPorPF(int idPessoa);

        IList<EstabelecimentoProfissional> ListarPorServicoEstabelecimento(Int32 codigoServicoEstabelecimento);

        IQueryable<EstabelecimentoProfissional> ListarProfissionaisComPeloId(IEnumerable<int> idProfissionais, int idEstabelecimento);

        IList<EstabelecimentoProfissional> ListarProfissionaisComPeloMenosUmServicoAssociado(Int32 idEstabelecimento);

        IList<EstabelecimentoProfissional> ListarProfissionaisComPeloMenosUmServicoAssociadoComInativos(Int32 idEstabelecimento);

        IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(Int32 codigoCategoriaServico);

        IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(List<Int32> codigoCategoriaServico);

        EstabelecimentoProfissional Obter(string email, int codigoEstabelecimento);

        EstabelecimentoProfissional Obter(Int32 codigoProfissional, Int32 codigoEstabelecimento);

        EstabelecimentoProfissional ObterInativo(Int32 codigoProfissional, Int32 codigoEstabelecimento);

        IQueryable<EstabelecimentoProfissional> ObterParaFolhaDePagamento(ParametrosFiltrosRelatorio filtro);
        IQueryable<EstabelecimentoProfissional> ObterParaRelatorioComissoes(ParametrosFiltrosRelatorio filtro);

        EstabelecimentoProfissional ObterPorCodigoIdentificacao(String codigoIdentificacao, Int32 codigoEstabelecimento);

        EstabelecimentoProfissional ObterPorCodigoInterno(string codigoInterno, int idEstabelecimento);

        EstabelecimentoProfissional ObterPorPessoaFisica(Int32 codigoPessoaFisica, Int32 codigoEstabelecimento);

        int? ObterIdEstabelecimentoProfissionalPelaPessoaFisica(Int32 codigoPessoaFisica, Int32 codigoEstabelecimento);

        [Obsolete]
        EstabelecimentoProfissional ObterPorPessoaFisica(Int32 codigoPessoaFisica);

        EstabelecimentoProfissional ObterPorProfissionalEEstabelecimento(int idProfissional, int idEstabelecimento);

        IList<EstabelecimentoProfissional> ObterProfissionaisAtivosPorIdEstabelecimento(int idEstabelecimento);

        IList<EstabelecimentoProfissional> ListarFiltrandoPelaConfiguracaoDataPagamentoComissao(int idEstabelecimento, bool? ehexcecao, bool? ativo);

        IList<EstabelecimentoProfissional> ListarFiltrandoPelaConfiguracaoDescontoOperadoraProfissional(int idEstabelecimento, bool? ehexcecao, bool? ativo);

        IList<EstabelecimentoProfissional> ListarFiltrandoPelaConfiguracaoDescontoOperadoraAssistente(int idEstabelecimento, bool? ehexcecao, bool? ativo);

        bool PossuiAcessoMinhaAgenda(Int32 idPessoaFisica);

        bool PossuiServicoAtivo(int codigo);

        void SaveOrUpdate(EstabelecimentoProfissional entity);

        List<KeyValuePair<int, string>> ListarIdComNomeDosProfissionaisAtivosNoEstabelecimento(int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarIdProfissionalComNomeDosProfissionaisNoEstabelecimento(int idEstabelecimento);

        int? ObterIdPessoaPorEstabelecimentoProfissional(int idEstabelecimentoProfissional);

        List<EstabelecimentoProfissional> ObterEstabelecimentoProfissionaisEnvolvidosNaTransacao(int idTransacao);

        EstabelecimentoProfissional ObterPorId(int idEstabelecimentoProfissional);

        IQueryable<EstabelecimentoProfissional> ObterQueryPorId(int idEstabelecimentoProfissional, int idEstabelecimento);

        IList<EstabelecimentoProfissional> EstabelecimentoProfissionalcomSplitPagamentoPorEstabelecimento(int idEstabelecimento);
        bool EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(int idProfissional, int idEstabelecimento);

        bool EstabelecimentoProfissionalEstaUtilizandoSplitPagamentoPelaPessoa(int idPessoaProfissional, int idPessoaEstabelecimento);

        string ObterCodigoIdentificadorPOSDoProfissional(int idProfissional, int idEstabelecimento);

        int ObterQuantidadeDeProfissionaisAtivosComAgendaNoEstabelecimento(int idEstabelecimento);

        int? ObterIdEstabelecimentoProfissional(int idProfissional, int idEstabelecimento);

        bool ExisteOutroProfissionalParceiroUtilizandoCnpj(int idEstabelecimento, string cNPJ, EstabelecimentoProfissional estabelecimentoProfissional);

        IQueryable<EstabelecimentoProfissional> ObterAdministradoresERecepcionistasDoEstabelecimento(int idEstabelecimento);

        int ObterIdEstabelecimentoDoProfissional(int idEstabelecimentoProfissional);

        Profissional ObterProfissionalPeloIdEstabelecimentoProfissional(int idEstabelecimentoProfissional);

        IList<EstabelecimentoProfissional> ObterListaDeProfissionalAtivoNoEstabelecimentoPorPessoa(int idPessoa);

        bool ProfissionalEstaAtivoRealizaServicoEPossuiAgenda(Int32 idEstabelecimentoProfissional, int idEstabelecimento);

        IQueryable<EstabelecimentoProfissional> Filtrar(ParametrosBusca parametros);

        IQueryable<EstabelecimentoProfissional> ObterProfissionaisComAgendaDoEstabelecimento(int idEstabelecimento, bool ativo = true);

        IQueryable<EstabelecimentoProfissional> ObterProfissionaisComAgendaDoEstabelecimentoQueRealizamServico(int idEstabelecimento, int idServicoEstabelecimento, bool ativo = true);

        IOrderedEnumerable<KeyValuePair<Int32, String>> ListarIdNomePessoaFisicaDosProfissionaisPorEstabelecimento(int idEstabelecimento);

        IList<ApelidoENomeProfissionalDTO> ListarApelidoENomeDeTodosProfissionais(int idEstabelecimento);

        EstabelecimentoProfissional ObterProfissionalPorIdExternosDeBeneficiario(string idExternosDeBeneficiario);

        List<EstabelecimentoProfissional> ObterProfissionaisSemCodigoIdentificacaoPOS();

        List<EstabelecimentoProfissional> ObterEstabelecimentoProfissionaisParaCampanha(int idEstabelecimento);

        DateTime? ObterDataUltimoProfissionalCadastrado(int idEstabelecimento, int idPessoaResponsavelFinanceiro);

        PessoaFisica ObterPessoaFisica(int idEstabelecimentoProfissional);

        bool ProfissionalInformouSenhaDeAcessoAoPAT(int idEstabelecimentoProfissional);

        bool UtilizaMenuLateral(string email, int idConta, int idEstabelecimento);
        List<KeyValuePair<int, string>> ListarIdPessoaComNomeDosProfissionais(int idEstabelecimento);

        List<int> ListarIdPessoaPorIdProfissional(List<int> idsProfissional, int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarIdProfissionalComNomePorEstabelecimento(int idEstabelecimento);

        List<EstabelecimentoProfissionalDto> ObterProfissionaisPorEstabelecimento(
            FiltrosProfissionaisDoEstabelecimentoDto filtros);

        List<KeyValuePair<int, string>>
            ListarEstabelecimentosQueUtilizamConnectPagarmeEOProfissionalUtilizaSplitDePagamento(int idPessoa);

        DadosEstabelecimentoProfissionalCadastroContaDigitalDTO ObterDadosEstabelecimentoProfissionalParaCadastroContaDigital(int idPessoa, int idEstabelecimento);

        List<EstabelecimentoProfissional> ObterProfissionaisPelasComissoes(List<int> idPessoasComissionadas, int idEstabelecimento);

        ProfissionalDto ObterProfissionalPorContaEEstabelecimento(int idConta, int idEstabelecimento);

        EstabelecimentoProfissional ObterPorEstabelecimentoEStoneCode(int idEstabelecimento, string stoneCode);
    }
}