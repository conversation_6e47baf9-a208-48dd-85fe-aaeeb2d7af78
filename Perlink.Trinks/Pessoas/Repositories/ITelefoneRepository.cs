﻿using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface ITelefoneRepository
    {

        IList<Telefone> ObterTelefonesCelularesDaPessoa(ClienteEstabelecimento clienteEstabelecimento);

        TelefoneDaPessoaDTO ObterDoPrimeiroTelefoneCelularAtivoDoClientePorIdPessoa(int idPessoaCliente, int idPessoaEstabelecimento);

        IQueryable<Telefone> ObterTelefonesCelularesDaPessoaQueryable(ClienteEstabelecimento clienteEstabelecimento);

        IQueryable<Telefone> ObterTelefonesCelularesDaPessoaQueryable(int idPessoaCliente, int idPessoaEstabelecimento);

        IList<Telefone> ObterTelefonesDaPessoa(Int32 idPessoa);

        IQueryable<Telefone> ObterTelefonesVisiveisParaAPessoaJuridica(int idPessoaCliente, int idPessoaEstabelecimento);

        IQueryable<Telefone> ObterTelefonesCelularesNacionaisVisiveisParaAPessoaJuridica(int idPessoaCliente, int idPessoaEstabelecimento);

        IQueryable<Telefone> ObterTelefonesVisiveisParaAPessoaJuridica(ClienteEstabelecimento clienteEstabelecimento);

        List<KeyValuePair<string, bool>> ObterListaTelefonesVisiveisParaAPessoaJuridica(int idPessoaCliente, int idEstabelecimento);

        string ObterTelefonesDoEstabelecomento(int idEstabelecimento);
        List<KeyValuePair<int, string>> ListarTelefonesFormatadosQueContemOsIdsPessoas(List<int> idsPessoas);
        List<Telefone> ListarTelefonesPropriosDaPessoa(int idPessoa, bool somenteAtivos = true);
        List<TelefoneDaPessoaDTO> ListarTelefonesFormatadosDoProfissional(int idPessoaDoProfissional, int idPessoaDoEstabelecimento);
        TelefoneDaPessoaDTO ObterDoPrimeiroTelefoneCelularAtivoFormatadosDoProfissional(int idPessoaDoProfissional, int idPessoaEstabelecimento);
        IQueryable<Telefone> ObterTelefonesCelularesDaPessoaQueryable(List<int> idsPessoa);
    }
}