﻿using Castle.ActiveRecord;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class FranquiaEstabelecimentoRepository : IFranquiaEstabelecimentoRepository
    {

        public string ObterUrlRedirecionamentoFrameBusca(int idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo == true)
                .Select(fe => fe.Franquia.UrlRedirecionamentoFrameBusca).FirstOrDefault();
        }

        public string ObterIdAppIosDaFranquiaDoEstabelecimento(int? idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo).Select(fe => fe.Franquia.IdAppIos).FirstOrDefault();
        }
        public string ObterUrlAppIosDaFranquiaDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo).Select(fe => fe.Franquia.UrlDownloadAppIOS).FirstOrDefault();
        }
        public string ObterUrlAppAndroidDaFranquiaDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo).Select(fe => fe.Franquia.UrlDownloadAppAndroid).FirstOrDefault();
        }

        public string ObterIdAppAndroidDaFranquiaDoEstabelecimento(int? idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo).Select(fe => fe.Franquia.IdAppandroid).FirstOrDefault();
        }

        public bool ObterOpcaoQueDefineSeFranquiaCompartilhaEstabelecimento(int? idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo).Select(fe => fe.Franquia.CompartilhaClientes).FirstOrDefault();
        }

        public List<int> ObterTodosOsIdsDeModelo()
        {
            return Queryable().Where(fe => fe.Ativo && fe.TipoDeCadastroDeEstabelecimento == TipoDeCadastroDeEstabelecimentoFranqueado.Modelo).Select(fe => new { Id = fe.IdEstabelecimento }.Id).ToList();
        }

        public int ObterIdDoModeloDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimento && fe.Ativo).Select(fe => fe.EstabelecimentoModelo.IdEstabelecimento).FirstOrDefault();
        }

        public List<UnidadesDaFranquiaDTO> ListarEstabelecimentosDaFranquiaForaDoModelo(int idEstabelecimentoDoModelo, string conteudoFiltro)
        {

            var idFranquiaDoModelo = Queryable().Where(fe => fe.IdEstabelecimento == idEstabelecimentoDoModelo).Select(fe => fe.Franquia.Id).FirstOrDefault();

            if (idFranquiaDoModelo < 1)
            {
                return null;
            }

            var retorno = (from fe in Queryable()
                           join e in Domain.Pessoas.EstabelecimentoRepository.Queryable() on fe.IdEstabelecimento equals e.IdEstabelecimento
                           join cf in Domain.Cobranca.ContaFinanceiraRepository.Queryable() on e.PessoaJuridica.IdPessoa equals cf.Pessoa.IdPessoa
                           where (fe.EstabelecimentoModelo == null || fe.EstabelecimentoModelo.IdEstabelecimento != idEstabelecimentoDoModelo)
                               && fe.Ativo
                               && fe.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo
                               && cf.Ativo
                               && (new[] { 1, 3, 4, 5, 9 }).Contains(cf.Status.IdStatus)
                               && fe.NomeUnidade.Contains(conteudoFiltro)
                               && fe.Franquia.Id == idFranquiaDoModelo
                               && fe.IdEstabelecimento != idEstabelecimentoDoModelo
                           select new UnidadesDaFranquiaDTO
                           {
                               IdEstabelecimento = fe.IdEstabelecimento,
                               Nome = fe.NomeUnidade
                           }).ToList();

            return retorno;
        }

        public bool EstabelecimentoEhFranquiaBaseadoEmModelo(int idEstabelecimento)
        {
            return Queryable().Where(p => p.IdEstabelecimento == idEstabelecimento).Select(p => p.EstabelecimentoModelo != null).FirstOrDefault();
        }

        public IQueryable<UnidadesDaFranquiaDTO> ListarUnidadesDoModelo(int idEstabelecimentoDoModelo)
        {
            var retorno = (from fe in Queryable()
                           join e in Domain.Pessoas.EstabelecimentoRepository.Queryable() on fe.IdEstabelecimento equals e.IdEstabelecimento
                           join cf in Domain.Cobranca.ContaFinanceiraRepository.Queryable() on e.PessoaJuridica.IdPessoa equals cf.Pessoa.IdPessoa
                           where fe.EstabelecimentoModelo.IdEstabelecimento.Equals(idEstabelecimentoDoModelo)
                               && fe.Ativo
                               && fe.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo
                               && cf.Ativo
                               && (new[] { 1, 3, 4, 5, 9 }).Contains(cf.Status.IdStatus)
                           select new UnidadesDaFranquiaDTO
                           {
                               IdEstabelecimento = fe.IdEstabelecimento,
                               Nome = e.NomeDeExibicaoNoPortal
                           });

            return retorno;
        }

        public bool FranquiaPermiteAgendamentoComPreCadastro(int idEstabelecimento)
        {
            bool? permite = Queryable()
                .Where(f => f.IdEstabelecimento == idEstabelecimento)
                .Select(f => f.Franquia.AceitaPreCadastroClienteNoFrame)
                .FirstOrDefault();

            return permite ?? false;
        }

        public List<KeyValuePair<int, string>> ObterEstabelecimentosDaMesmaFranquiaParaTransferenciaDeProdutos(Estabelecimento estabelecimento)
        {
            var statusPermitidosDaContaFinanceira = new List<int>() { 1, 3, 4, 9 };
            var query = Queryable().Where(fe => fe.Franquia.Id == estabelecimento.FranquiaEstabelecimento.Franquia.Id);
            var queryEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.Queryable();
            var queryContaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();

            query = from fe in query
                    join e in queryEstabelecimento on fe.IdEstabelecimento equals e.IdEstabelecimento
                    join cf in queryContaFinanceira on e.PessoaJuridica.IdPessoa equals cf.Pessoa.IdPessoa
                    where statusPermitidosDaContaFinanceira.Contains(cf.Status.IdStatus) && fe.TipoDeCadastroDeEstabelecimento != TipoDeCadastroDeEstabelecimentoFranqueado.Modelo
                    select fe;

            var estabelecimentos = query.Select(p => new KeyValuePair<int, string>(p.IdEstabelecimento, p.NomeUnidade)).OrderBy(fe => fe.Value).ToList();

            estabelecimentos = RemoverOProprioEstabelecimentoDaConsulta(estabelecimento, estabelecimentos);

            return estabelecimentos;
        }

        public List<KeyValuePair<int, string>> ListarEstabelecimentosQuePermitemAcessoBackOfficePorOutrosFranqueadores(int idFranquia)
        {
            var estabelecimentos = Domain.Pessoas.EstabelecimentoRepository.Queryable();

            return (from fe in Queryable()
                    join e in estabelecimentos on fe.IdEstabelecimento equals e.IdEstabelecimento
                    where fe.Franquia.Id == idFranquia &&
                          fe.PermiteAcessoBackOfficePorOutrosFranqueadores &&
                          fe.Ativo
                    select new KeyValuePair<int, string>(e.IdEstabelecimento, e.NomeDeExibicaoNoPortal)
                    ).ToList();
        }

        public List<int> ObterIdsEstabelecimentoDasUnidadesAssociadasAoModelo(int idEstabelecimentoModelo)
        {
            return Queryable()
               .Where(f => f.EstabelecimentoModelo.IdEstabelecimento == idEstabelecimentoModelo)
               .Select(fe => fe.IdEstabelecimento)
               .ToList();
        }

        private List<KeyValuePair<int, string>> RemoverOProprioEstabelecimentoDaConsulta(Estabelecimento estabelecimento, List<KeyValuePair<int, string>> query)
        {
            return query.Where(fe => fe.Key != estabelecimento.IdEstabelecimento).ToList();
        }

        public List<KeyValuePair<int, string>> ListarIdENomePorFranquia(int idFranquia)
        {
            var queryFranquiaEstab = Queryable();

            var queryContaFinanceira = Domain.Cobranca.ContaFinanceiraRepository.Queryable();
            var queryEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.Queryable();

            var resultado = from fe in Queryable()
                            join e in queryEstabelecimento on fe.IdEstabelecimento equals e.IdEstabelecimento
                            join cf in queryContaFinanceira on e.PessoaJuridica.IdPessoa equals cf.Pessoa.IdPessoa
                            where fe.Franquia.Id == idFranquia && cf.Ativo && (new[] { 1, 3, 4, 9 }).Contains(cf.Status.IdStatus)
                            && fe.IdEstabelecimento != Domain.WebContext.IdEstabelecimentoAutenticado
                            select new KeyValuePair<int, string>(fe.IdEstabelecimento, fe.NomeUnidade);

            return resultado.ToList();
        }

        public TipoCompartilhaNaRedeEnum ObterTipoCompartilhaNaRede(int idEstabelecimento)
        {
            return Queryable()
                .Where(ef => ef.IdEstabelecimento == idEstabelecimento)
                .Select(ef => ef.TipoCompartilhamentoNaRede)
                .FirstOrDefault();
        }

        public void AtualizarSeExistePacoteDisponivelNaRedeParaConsumo(int idFranquia)
        {
            string query = ObterQueryAtualizarExistePacoteDisponivelNaRedeParaConsumo();

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session1 = holder.CreateSession(typeof(ActiveRecordBase));

            session1.CreateSQLQuery(query)
                    .SetParameter("idFranquia", idFranquia)
                    .ExecuteUpdate();

            holder.ReleaseSession(session1);
        }

        private string ObterQueryAtualizarExistePacoteDisponivelNaRedeParaConsumo()
        {
            return @"
                    update fe2
	                set fe2.existe_pacote_da_rede_disponivel_para_consumo =
		                CASE
			                WHEN EXISTS (
				                select 1 from Franquia_Estabelecimento fe
				                join Pacote pa on fe.id_estabelecimento = pa.id_estabelecimento
				                where fe.tipo_compartilhamento_na_rede = 1 and pa.compartilhado_na_rede = 1 and fe.id_franquia = fe2.id_franquia)
			                THEN 1
			                WHEN EXISTS (
				                select 1 from Compartilhamento_Na_Rede c
				                join Pacote p on c.id_estabelecimento_origem = p.id_estabelecimento
				                join Franquia_Estabelecimento feo on c.id_estabelecimento_origem = feo.id_estabelecimento
				                where p.compartilhado_na_rede = 1
				                and feo.tipo_compartilhamento_na_rede = 2
				                and c.id_estabelecimento_destino = fe2.id_estabelecimento)
			                THEN 1
			                ELSE 0
		                END
	                from dbo.Franquia_Estabelecimento fe2
	                where fe2.id_franquia = :idFranquia";
        }

        public int ObterIdFranquiaPorIdEstabelecimento(int idEstabelecimento)
        {
            return Queryable()
                 .Where(f => f.IdEstabelecimento == idEstabelecimento)
                 .Select(f => f.Franquia.Id)
                 .FirstOrDefault();
        }

        public bool EhUmEstabelecimentoFranquiado(int idEstabelecimento)
        {
            return Queryable()
                 .Where(f => f.IdEstabelecimento == idEstabelecimento)
                 .FirstOrDefault() != null;
        }

        public List<FranquiaEstabelecimento> ObterFranquiasEstabelecimentosPorIdsEstabelecimentos(List<int> idsEstabelecimentos)
        {
            if (idsEstabelecimentos == null || !idsEstabelecimentos.Any())
                return new List<FranquiaEstabelecimento>();

            return Queryable().Where(fe => idsEstabelecimentos.Contains(fe.IdEstabelecimento)).ToList();
        }
    }
}