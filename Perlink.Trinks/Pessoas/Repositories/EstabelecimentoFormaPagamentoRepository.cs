﻿using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class EstabelecimentoFormaPagamentoRepository : IEstabelecimentoFormaPagamentoRepository
    {

        public void SaveOrUpdate(EstabelecimentoFormaPagamento entidade)
        {
            if (entidade.Id > 0)
                Update(entidade);
            else
                SaveNew(entidade);
        }

        public bool VerificarSeEstabelecimentoPossuiOutraFormaDePagamentoAlemDeDinheiro(int idEstabelecimento)
        {
            return Queryable().Any(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo && p.FormaPagamento != FormaPagamentoEnum.Dinheiro);
        }

        public Boolean VerificarSeEstabelecimentoUsaFormaPagamento(Int32 idEstabelecimento, FormaPagamentoEnum formaPagamento)
        {
            var query = Queryable();

            return query.Any(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Ativo
                && f.FormaPagamento == formaPagamento);
        }

        public Boolean VerificarSeEstabelecimentoUsaFormaPagamentoComCache(Int32 idEstabelecimento, FormaPagamentoEnum formaPagamento)
        {
            var query = Queryable(true);

            return query.Any(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Ativo
                && f.FormaPagamento == formaPagamento);
        }

        public List<EstabelecimentoFormaPagamento> ListarPorIdEstabelecimentoFormaPagamento(List<Int32> listaIdEstabelecimentoFormaPagamento)
        {
            return Queryable().Where(p => listaIdEstabelecimentoFormaPagamento.Contains(p.Id)).ToList();
        }

        public List<EstabelecimentoFormaPagamento> ListarPorEstabelecimento(Int32 idEstabelecimento)
        {
            return Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento).ToList();
        }

        public List<EstabelecimentoFormaPagamento> ListarAtivasPorEstabelecimento(Int32 idEstabelecimento)
        {
            var lista = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo)
                .OrderBy(f => f.FormaPagamento.Tipo.OrdemNoFechamentoDeConta)
                .ThenBy(f => f.FormaPagamento.Tipo.Id)
                .ThenByDescending(f => f.FormaPagamento.TipoPOS.Id)
                .ThenBy(f => f.FormaPagamento.Nome).ToList();
            return lista;
        }
        public List<EstabelecimentoFormaPagamento> ListarParaRecebimentoPorEstabelecimento(Int32 idEstabelecimento)
        {
            var lista = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo && p.FormaPagamento.UtilizadoEmRecebimento).OrderBy(f => f.FormaPagamento.Tipo.Id).ThenByDescending(f => f.FormaPagamento.TipoPOS.Id).ThenBy(f => f.FormaPagamento.Id).ToList();
            return lista;
        }

        public List<EstabelecimentoFormaPagamento> ListarAtivasPorEstabelecimentoEhTipoPOS(int idEstabelecimento, int idTipoPOS)
        {
            return Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                               (p.FormaPagamento.TipoPOS.Id == idTipoPOS || p.FormaPagamento.TipoPOS == null) &&
                                               p.Ativo)
                                   .OrderBy(f => f.FormaPagamento.Tipo.Id)
                                   .ThenByDescending(f => f.FormaPagamento.TipoPOS.Id)
                                   .ThenBy(f => f.FormaPagamento.Id)
                                   .ToList();
        }

        public List<EstabelecimentoFormaPagamento> ListarFormasDePagamentoPorTipoPOS(int idEstabelecimento, int idTipoPOS)
        {
            return Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                               p.FormaPagamento.TipoPOS.Id == idTipoPOS &&
                                               p.Ativo)
                                   .OrderBy(f => f.FormaPagamento.Tipo.Id)
                                   .ThenByDescending(f => f.FormaPagamento.TipoPOS.Id)
                                   .ThenBy(f => f.FormaPagamento.Id)
                                   .ToList();
        }

        public EstabelecimentoFormaPagamento ObterEstabelecimentoFormaDePagamentoAtivos(Int32 idEstabelecimento, Int32 idFormaPagamento)
        {
            return Queryable().FirstOrDefault(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.FormaPagamento.Id == idFormaPagamento && p.Ativo && p.FormaPagamento.Ativo);
        }

        //[Obsolete("Não usar pq pesa muito no banco")]
        //public List<EstabelecimentoFormaPagamento> ListarAtivasOuComTransacoesNaoEstornadaPorEstabelecimento(Int32 idEstabelecimento) {
        //    var transacaoQueryable = Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable();
        //    return Queryable().Where(p =>
        //        p.Estabelecimento.IdEstabelecimento == idEstabelecimento
        //        && (p.Ativo
        //        || transacaoQueryable.Any(p2 =>
        //            p2.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento
        //            && p2.Transacao.TransacaoQueEstounouEsta == null
        //            && p2.FormaPagamento.Id == p.FormaPagamento.Id))
        //    ).ToList();
        //}

        public EstabelecimentoFormaPagamento ObterEstabelecimentoFormaDePagamento(Int32 idEstabelecimento, Int32 idFormaPagamento)
        {
            return Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.FormaPagamento.Id == idFormaPagamento).FirstOrDefault();
        }

        public IList<EstabelecimentoFormaPagamento> ListarFormasPagamentoRecebimentoAtivos()
        {
            var formasAtivas = Domain.Financeiro.FormaPagamentoRepository.ListarFormasPagamentoRecebimentoAtivos().ToList();
            var formasAssociadas = Queryable()
                   .Where(p => p.Ativo)
                   .ToList();
            var formasNaoAssociadas = formasAtivas
                .Where(f => !formasAssociadas.Select(g => g.FormaPagamento).Contains(f))
                .Select(f => new EstabelecimentoFormaPagamento(f));

            return formasAssociadas.Union(formasNaoAssociadas)
                   .OrderBy(p => p.FormaPagamento.Tipo.Id)
                   .ThenBy(p => p.FormaPagamento.Id)
                   .ToList();
        }

        public IList<string> ListarFormasDePagamentosVisiveisNoHotSite(int idEstabelecimento)
        {
            var concluiuConfiguracoesBelezinha = false;
            var configPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);
            if (configPos != null)
            {
                concluiuConfiguracoesBelezinha = configPos.ConcluiuConfiguracao;
            }
            var formasDePagamento = StatelessQueryable()
                .Where(efp => efp.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                              efp.FormaPagamento.ApareceHotSite &&
                              efp.Ativo)
                .OrderBy(efp => efp.FormaPagamento.Tipo.Id)
                .ThenBy(efp => efp.FormaPagamento.Id);

            var listaFinalFormasDePagamento = formasDePagamento.Select(efp => efp.FormaPagamento.Nome).ToList();

            if (!concluiuConfiguracoesBelezinha)
            {
                listaFinalFormasDePagamento = formasDePagamento.Where(efp => efp.FormaPagamento.TipoPOS == null).Select(efp => efp.FormaPagamento.Nome).ToList();
            }
            return listaFinalFormasDePagamento;
        }

        public bool EstabelecimentoAceitaPagamentoComCartaoDeCredito(int idEstabelecimento)
        {
            return StatelessQueryable()
                .Where(efp => efp.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                              efp.FormaPagamento.Tipo == FormaPagamentoTipoEnum.Credito &&
                              efp.Ativo)
                .Select(efp => (int?)efp.Id)
                .Any();
        }

        public EstabelecimentoFormaPagamento ObterEstabelecimentoFormaPagamentoAtivaPOS(int idEstabelecimento, int idFormaPagamento, int subaquirente)
        {
            return Queryable()
                .SingleOrDefault(efp => efp.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                        efp.Ativo &&
                                        efp.FormaPagamento.Id == idFormaPagamento &&
                                        efp.FormaPagamento.TipoPOS != null &&
                                        efp.FormaPagamento.TipoPOS.Id == subaquirente);
        }

        public bool EstabelecimentoAceitaParcelamento(int idEstabelecimento, int idFormaPagamento)
        {
            return Queryable()
                .Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.FormaPagamento.Id == idFormaPagamento)
                .Select(p => p.AceitaParcelamento)
                .FirstOrDefault();
        }

        public int ObterNumeroDeParcelaMaximo(int idEstabelecimento, int idFormaPagamento)
        {
            var queryParcela = Domain.Pessoas.EstabelecimentoFormaPagamentoParcelaRepository.Queryable();

            var NumerosParcelas = queryParcela
               .Where(p =>
                   p.EstabelecimentoFormaPagamento.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                   p.EstabelecimentoFormaPagamento.FormaPagamento.Id == idFormaPagamento &&
                   p.Ativo)
               .Select(p => p.NumeroParcela).ToList();

            var numeroMaximoParcela = 1;

            foreach (var numeroParcela in NumerosParcelas)
            {
                if (numeroParcela > numeroMaximoParcela)
                {
                    numeroMaximoParcela = numeroParcela;
                };
            }

            return numeroMaximoParcela;
        }

        public List<ParcelaFormaPagamentoDTO> ObterListaDeParcelas(int idEstabelecimento, int idFormaPagamento)
        {
            var queryParcela = Domain.Pessoas.EstabelecimentoFormaPagamentoParcelaRepository.Queryable();

            var parcelas = queryParcela
                .Where(p =>
                   p.EstabelecimentoFormaPagamento.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                   p.EstabelecimentoFormaPagamento.FormaPagamento.Id == idFormaPagamento &&
                   p.Ativo)
               .Select(p => new ParcelaFormaPagamentoDTO()
               {
                   NumeroParcela = p.NumeroParcela,
               }).ToList();

            return parcelas;
        }

        public List<KeyValuePair<int, string>> ListarKeyValueDeFormasDePagamentoAtivas(int idEstabelecimento)
        {
            return Queryable()
                .Where(p => p.Ativo &&
                       p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                       p.FormaPagamento.UtilizadoEmPagamento)
                .Select(p => new KeyValuePair<int, string>(p.FormaPagamento.Id, p.FormaPagamento.Nome))
                .OrderBy(p => p.Value)
                .ToList();
        }
    }
}