﻿using NHibernate.Criterion;
using NHibernate.Linq;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Cobranca;
using Perlink.Trinks.DataQuery;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Fotos.Enums;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.DTO.Filtros;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Filtros;
using Perlink.Trinks.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using static Perlink.Trinks.PropertyNames.Pessoas;
using DL = Perlink.Trinks.Domain;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class EstabelecimentoProfissionalRepository : IEstabelecimentoProfissionalRepository
    {

        #region Propriedades de Apoio

        private IProfissionalRepository ProfissionalRepository
        {
            get { return Domain.Pessoas.ProfissionalRepository; }
        }

        #endregion Propriedades de Apoio

        #region Métodos Públicos

        public EstabelecimentoProfissional ObterProfissionalPelaPessoaJuridica(int idPessoaDaPessoaJuridica)
        {
            return Queryable().FirstOrDefault(p => p.PessoaJuridica.IdPessoa == idPessoaDaPessoaJuridica);
        }

        public bool VerificarPessoaJuridicaEhProfissionalParceiroDoEstabelecimento(int idEstabelecimento, int idPessoaDaPessoaJuridica)
        {
            return Queryable().Any(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.PessoaJuridica != null && p.PessoaJuridica.IdPessoa == idPessoaDaPessoaJuridica);
        }

        public bool VerificarPessoaJuridicaEhProfissionalParceiro(int idPessoaDaPessoaJuridica)
        {
            return Queryable().Any(p => p.PessoaJuridica != null && p.PessoaJuridica.IdPessoa == idPessoaDaPessoaJuridica);
        }

        public bool VerificarSeProfissionalEhProfissionalParceiro(int idProfissional, int idEstabelecimento, bool verificarSeEstaAtivo)
        {
            if (idProfissional > 0 && idEstabelecimento > 0)
            {
                var pessoaJuridica = Queryable().FirstOrDefault(p => p.Profissional.IdProfissional == idProfissional &&
                                                                     p.Estabelecimento.IdEstabelecimento == idEstabelecimento).PessoaJuridica;
                return VerificarSeProfissionalEhProfissionalParceiro(pessoaJuridica, verificarSeEstaAtivo);
            }
            return false;
        }

        public bool VerificarSeProfissionalEhProfissionalParceiro(PessoaJuridica pessoaJuridica, bool verificarSeEstaAtivo)
        {
            if (pessoaJuridica != null && pessoaJuridica.IdPessoa > 0)
            {
                if (!verificarSeEstaAtivo)
                    return true;
                else if (pessoaJuridica.Ativo)
                    return true;
            }
            return false;
        }

        public IQueryable<EstabelecimentoProfissional> QueryableProfissionaisParceirosDoEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.PessoaJuridica != null);

            query = query.Where(p => (p.Estabelecimento.EstabelecimentoConfiguracaoGeral.TipoDeEmissaoNfeComProfissionalParceiro == Pessoas.Statics.TipoNfeProfissionalParceiro.NotaUnica.Id
                                        && p.PessoaJuridica.ConfiguracaoNFe != null
                                        && p.PessoaJuridica.ConfiguracaoNFe.UltimoRpsEmitido > 0)
                                     || (p.Estabelecimento.EstabelecimentoConfiguracaoGeral.TipoDeEmissaoNfeComProfissionalParceiro == Pessoas.Statics.TipoNfeProfissionalParceiro.NotaSeparada.Id
                                        && p.PessoaJuridica.ConfiguracaoNFe != null));

            return query;
        }

        public IQueryable<EstabelecimentoProfissional> QueryableProfissionaisParceirosDoEstabelecimentoNotaUnificada(int idEstabelecimento)
        {
            var query = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.PessoaJuridica != null);

            query = query.Where(p => (p.Estabelecimento.EstabelecimentoConfiguracaoGeral.TipoDeEmissaoNfeComProfissionalParceiro == Pessoas.Statics.TipoNfeProfissionalParceiro.NotaUnica.Id
                                        && p.PessoaJuridica.ConfiguracaoNFe != null));

            return query;
        }

        public bool ComissaoPadraoFoiAlterada(EstabelecimentoProfissional entity)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional));
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissional.Codigo, entity.Codigo));
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissional.ComissaoPadrao,
                entity.ComissaoPadrao));
            return !Exists(dc);
        }

        public bool EhProfissionalDoEstabelecimento(Int32 idPessoa, Int32 idEstabelecimento)
        {
            return
                Queryable().Any(p =>
                    p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                    p.Profissional.PessoaFisica.IdPessoa == idPessoa &&
                    p.Ativo
                    );
        }

        public bool EhProfissionalPjDoEstabelecimento(Int32 idPessoaJuridica, Int32 idEstabelecimento)
        {
            return
                Queryable().Any(p =>
                    p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                    p.PessoaJuridica.IdPessoa == idPessoaJuridica &&
                    p.Ativo
                    );
        }

        public bool EstaAtivo(Int32 idPessoaFisica)
        {
            return
                Queryable().Any(p =>
                    p.Profissional.PessoaFisica.IdPessoa == idPessoaFisica &&
                    p.Ativo
                    );
        }

        public bool ExisteEstabelecimentoProfissionalPorEmail(string email, int codigoEstabelecimento)
        {
            return Queryable()
                .Any(
                    ep => ep.Ativo &&
                          ep.Profissional.PessoaFisica.Contas.Any(c => c.Email == email) &&
                          ep.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);
        }

        public bool ExisteProfissionalComAcessoAoPAT(Int32 idEstabelecimento)
        {
            var retorno = Queryable().Any(
                            f => f.CodigoDeAcessoAoPainelAtendimento != null &&
                            f.CodigoDeAcessoAoPainelAtendimento.Length > 0 &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            return retorno;
        }

        public List<EstabelecimentoProfissional> ListarAssistente(int idProfissional, int idServico)
        {
            var query = Queryable().Where(p => p.Ativo && p.EstabelecimentoAssistenteServicoLista.Any(q => q.ServicoEstabelecimento.IdServicoEstabelecimento == idServico && q.Ativo) && p.Codigo != idProfissional);
            return query.ToList();
        }

        public IList<EstabelecimentoProfissional> ListarAssistentes(int? idProfissionalEstabelecimento, int? idServicoEstabelecimento)
        {
            var resultado = Queryable().Where(f => f.Ativo
                                              && f.Codigo != idProfissionalEstabelecimento
                                              && f.EstabelecimentoAssistenteServicoLista
                                                    .Any(s => s.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento
                                                         && s.Ativo && s.EstabelecimentoProfissional.Codigo != idProfissionalEstabelecimento))
                                       .ToList();

            return resultado.OrderBy(a => a.Profissional.PessoaFisica.NomeOuApelido()).ToList();
        }

        public IList<EstabelecimentoProfissional> ListarAssistentes(int? idProfissionalEstabelecimento, int? idServicoEstabelecimento, Estabelecimento estabelecimentoAutenticado)
        {
            var resultado = Queryable().Where(f => f.Ativo
                                              && f.Estabelecimento == estabelecimentoAutenticado
                                              && f.Codigo != idProfissionalEstabelecimento
                                              && f.EstabelecimentoAssistenteServicoLista
                                                    .Any(s => s.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento
                                                         && s.Ativo && s.EstabelecimentoProfissional.Codigo != idProfissionalEstabelecimento))
                                       .ToList();

            return resultado.OrderBy(a => a.Profissional.PessoaFisica.NomeOuApelido()).ToList();
        }

        public List<KeyValuePair<string, string>> ListarAssistentesKeyValue(FiltroAssistenteProfissionalDTO filtroAssistenteProfissional)
        {
            var resultado = Queryable()
                .Where(f => f.Ativo)
                .Where(f => f.Estabelecimento.IdEstabelecimento == filtroAssistenteProfissional.IdEstabelecimento)
                .Where(f => f.Profissional.IdProfissional != filtroAssistenteProfissional.IdProfissional)
                .Where(f => f.EstabelecimentoAssistenteServicoLista
                    .Any(s => s.ServicoEstabelecimento.IdServicoEstabelecimento == filtroAssistenteProfissional.IdServicoEstabelecimento
                        && s.Ativo && s.EstabelecimentoProfissional.Profissional.IdProfissional != filtroAssistenteProfissional.IdProfissional));                                        

            if (!string.IsNullOrEmpty(filtroAssistenteProfissional.ConteudoFiltro))
                resultado = resultado.Where(f => 
                    f.Profissional.PessoaFisica.NomeCompleto.ToLower().Contains(filtroAssistenteProfissional.ConteudoFiltro.ToLower()) 
                    || f.Profissional.PessoaFisica.Apelido.ToLower().Contains(filtroAssistenteProfissional.ConteudoFiltro.ToLower()));

            var listaKeyValuePair = resultado
                .Select(a => new KeyValuePair<string, string>(
                    filtroAssistenteProfissional.ListarPorCodigoInterno ? a.CodigoInterno : a.Profissional.IdProfissional.ToString(),
                    string.IsNullOrEmpty(a.Profissional.PessoaFisica.Apelido) ? a.Profissional.PessoaFisica.NomeCompleto : a.Profissional.PessoaFisica.Apelido))
                .ToList();

            return listaKeyValuePair
                .OrderBy(a => a.Value)
                .ToList();
        }

        public IList<EstabelecimentoProfissional> ListarAssociadosAoServicoDoEstabelecimento(Int32 idEstabelecimento,
            Int32 idServicoEstabelecimento)
        {
            IQueryable<EstabelecimentoProfissional> retorno = Queryable();

            retorno = FiltrarEstabelecimento(retorno, idEstabelecimento);
            retorno = FiltrarServicoEstabelecimento(retorno, idServicoEstabelecimento);
            retorno = FiltrarAtivo(retorno);

            return retorno.ToList();
        }

        public IQueryable<EstabelecimentoProfissional> ListarAtivosPorEstabelecimento(Int32 idEstabelecimento)
        {
            return Queryable().Where(f => f.Ativo && f.Estabelecimento.IdEstabelecimento == idEstabelecimento).OrderBy(f => f.Profissional.PessoaFisica.NomeCompleto);
        }

        public IQueryable<EstabelecimentoProfissional> ObterQueryPorEstabelecimento(Int32 idEstabelecimento)
        {
            return Queryable().Where(f => f.Ativo && f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }
        //
        public IEnumerable<KeyValuePair<string, string>> ListarIdProfissionaisPorEstabelecimento(Int32 idEstabelecimento)
        {
            var retorno = Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.PessoaJuridica != null)
               .Select(f => new KeyValuePair<string, string>(f.PessoaJuridica.IdPessoa.ToString(), f.Profissional.PessoaFisica.NomeCompleto))
               .OrderBy(f => f.Value)
               .AsEnumerable();

            return retorno;
        }

        public IQueryable<EstabelecimentoProfissional> ListarAtivosEComAgendaPorEstabelecimento(Int32 idEstabelecimento)
        {
            return Queryable().Where(f => f.Ativo && f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.PossuiAgenda).OrderBy(f => f.Profissional.PessoaFisica.NomeCompleto);
        }

        public IList<EstabelecimentoProfissional> ListarComServicoAtivoPorEstabelecimento(int idEstabelecimento)
        {
            if (idEstabelecimento == 0)
                return new List<EstabelecimentoProfissional>();
            return Queryable()
                .Where(f => f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo)
                            && f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Ativo).
                OrderBy(f => f.Profissional.PessoaFisica.NomeCompleto).ToList();
        }

        public IOrderedEnumerable<KeyValuePair<Int32, String>>
            ListarIdNomePessoaFisicaDosProfissionaisPorEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return query
                .Select(
                    f =>
                        new KeyValuePair<Int32, String>(f.Profissional.PessoaFisica.IdPessoa,
                            String.Format("{0}{1}", f.Profissional.PessoaFisica.ObterApelidoOuNome(),
                                f.Ativo ? String.Empty : " [Inativo]")))
                .ToList()
                .OrderBy(f => f.Value);
        }

        public IOrderedEnumerable<KeyValuePair<Int32, String>>
            ListarIdNomePessoaFisicaDosProfissionaisPorEstabelecimentoQueJaVenderamProdutos(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);

            var queryableItemVenda = Domain.Vendas.ItemVendaRepository.Queryable();
            var query = Queryable().Where(f =>
                f.Estabelecimento.IdEstabelecimento == idEstabelecimento
                && (f.Ativo
                    || !f.Ativo &&
                    queryableItemVenda.Any(p =>
                        p.Comissao.PessoaComissionada.IdPessoa == f.Profissional.PessoaFisica.IdPessoa
                        && p.Venda.Transacao.TransacaoQueEstounouEsta == null
                        && p.Venda.Transacao.TipoTransacao.Id != (int)TipoTransacaoEnum.Estorno
                        && p.Comissao.IdPessoaEstabelecimento == estabelecimento.PessoaJuridica.IdPessoa
                        && p.Venda.Transacao.PessoaQueRecebeu.IdPessoa == estabelecimento.PessoaJuridica.IdPessoa)));

            return query
                .ToList()
                .Select(
                    f =>
                        new KeyValuePair<Int32, String>(f.Profissional.PessoaFisica.IdPessoa,
                            String.Format("{0}{1}", f.Profissional.PessoaFisica.ObterApelidoOuNome(),
                                f.Ativo ? String.Empty : " [Inativo]")))
                .OrderBy(f => f.Value);
        }

        public IOrderedEnumerable<KeyValuePair<Int32, String>>
            ListarIdPessoaNomePessoaFisicaDosProfissionaisPorEstabelecimento(int idEstabelecimento,
                int idPessoaFisicaProfissional = 0)
        {
            IQueryable<EstabelecimentoProfissional> query =
                Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            query = idPessoaFisicaProfissional > 0
                ? query.Where(f => f.Ativo || f.Profissional.PessoaFisica.IdPessoa == idPessoaFisicaProfissional)
                : query.Where(f => f.Ativo);

            return query
                .ToList()
                .Select(
                    f =>
                        new KeyValuePair<Int32, String>(f.Profissional.PessoaFisica.IdPessoa,
                            String.Format("{0}{1}", f.Profissional.PessoaFisica.ObterApelidoOuNome(),
                                f.Ativo ? String.Empty : " [Inativo]")))
                .OrderBy(f => f.Value);
        }

        public IOrderedEnumerable<KeyValuePair<Int32, String>>
            ListarIdPessoaNomePessoaFisicaDosProfissionaisPorEstabelecimentoAssociadosALancamento(int idEstabelecimento)
        {
            var query =
                Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            var queryLancamento = Domain.Despesas.LancamentoRepository.Queryable();
            query =
                query.Where(
                    f =>
                        f.Ativo ||
                        (!f.Ativo && queryLancamento.Any(l => l.PessoaQueRecebeuOuPagou == f.Profissional.PessoaFisica)));

            return query
                .ToList()
                .Select(
                    f =>
                        new KeyValuePair<Int32, String>(f.Profissional.PessoaFisica.IdPessoa,
                            String.Format("{0}{1}", f.Profissional.PessoaFisica.ObterApelidoOuNome(),
                                f.Ativo ? String.Empty : " [Inativo]")))
                .OrderBy(f => f.Value);
        }

        public List<KeyValuePair<String, String>> ListarIdProfissionalNomeOuApelidoComServicosAtivosECodigoPAT(int idEstabelecimento, bool exibirCodigoInterno)
        {
            IQueryable<EstabelecimentoProfissional> retorno = Queryable();

            retorno = FiltrarEstabelecimento(retorno, idEstabelecimento);
            retorno = FiltrarAtivo(retorno);
            retorno = FiltrarPorProfissionaisComServicosAssociados(retorno);
            retorno = FiltrarPorProfissionaisComCodigoPAT(retorno);

            return retorno
                    .OrderBy(p => p.Profissional.PessoaFisica.Apelido ?? p.Profissional.PessoaFisica.NomeCompleto)
                    .Select(p => new KeyValuePair<string, string>(p.CodigoInterno,
                                 (p.Profissional.PessoaFisica.Apelido ?? p.Profissional.PessoaFisica.NomeCompleto)
                                   + (exibirCodigoInterno ? " (" + p.CodigoInterno + ")" : String.Empty)))
                    .ToList();
        }

        public IList<EstabelecimentoProfissional> ListarPorEstabelecimento(Int32 idEstabelecimento, bool somenteAtivos = false)
        {
            if (idEstabelecimento == 0)
                return new List<EstabelecimentoProfissional>();

            var retorno = Queryable();
            retorno = FiltrarEstabelecimento(retorno, idEstabelecimento);

            if (somenteAtivos)
                retorno = FiltrarAtivo(retorno);

            retorno = retorno.OrderBy(f => f.Profissional.PessoaFisica.NomeCompleto);

            return retorno.ToList();
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisComServicosAssociados(Int32 idEstabelecimento, bool somenteAtivos = false)
        {
            var profissionaisEstabelecimento = ListarPorEstabelecimento(idEstabelecimento, somenteAtivos);
            return profissionaisEstabelecimento.Where(p => p.ProfissionalRealizaServicoNoEstabelecimento()).ToList();
        }

        public List<KeyValuePair<int, string>> ListarProfissionaisKeyValue(Estabelecimento estabelecimento, string nomeFiltro, bool listarPorIdPessoa = false)
        {
            var retorno = Queryable();
            retorno = FiltrarEstabelecimento(retorno, estabelecimento.IdEstabelecimento);
            retorno = FiltrarAtivo(retorno);
            retorno = FiltrarPorProfissionaisComServicosAssociados(retorno);

            if (!string.IsNullOrEmpty(nomeFiltro))
                retorno = retorno.Where(p => 
                    p.Profissional.PessoaFisica.NomeCompleto.ToLower().Contains(nomeFiltro.ToLower()) 
                    || p.Profissional.PessoaFisica.Apelido.ToLower().Contains(nomeFiltro.ToLower()));

            var listaKeyValuePair = new List<KeyValuePair<int, string>>();

            listaKeyValuePair = retorno
                .Select(p => new KeyValuePair<int, string>(
                    listarPorIdPessoa ? p.Profissional.PessoaFisica.IdPessoa : p.Profissional.IdProfissional,
                    string.IsNullOrEmpty(p.Profissional.PessoaFisica.Apelido) ? p.Profissional.PessoaFisica.NomeCompleto : p.Profissional.PessoaFisica.Apelido))
                .ToList();

            if (estabelecimento.EstabelecimentoConfiguracaoGeral.FilaDeEsperaEstaAtiva)
                listaKeyValuePair.Add(new KeyValuePair<int, string>(0, "-- " + Textos.FilaDeEspera + " --"));

            return listaKeyValuePair
                .OrderBy(p => p.Value)
                .ToList();
        }

        public List<EstabelecimentoProfissionalDto> ObterProfissionaisPorEstabelecimento(FiltrosProfissionaisDoEstabelecimentoDto filtros)
        {
            var query = Queryable();
            query = FiltrarEstabelecimento(query, filtros.IdEstabelecimento);
            query = FiltrarAtivo(query);
            if (!string.IsNullOrEmpty(filtros.Nome))
                query = FiltrarPorNome(query, filtros.Nome);

            return query
                .ApplyPagination(filtros)
                .Select(estabelecimentoProfissional => new EstabelecimentoProfissionalDto
                {
                    Id = estabelecimentoProfissional.Codigo,
                    Nome = estabelecimentoProfissional.Profissional.PessoaFisica.NomeCompleto,
                    Foto = ObterUrlDaFoto(estabelecimentoProfissional),
                    PossuiSplit = estabelecimentoProfissional.HabilitaSplitPagamento &&
                                    estabelecimentoProfissional.EhRecebedorNoConnectPagarme()
                }).ToList();
        }

        public List<KeyValuePair<int, string>> ListarEstabelecimentosQueUtilizamConnectPagarmeEOProfissionalUtilizaSplitDePagamento(int idPessoa)
        {
            var configuracaoPosQueryable = DL.Pessoas.EstabelecimentoConfiguracaoPOSRepository.Queryable();

            return Queryable()
                .Where(f =>
                    configuracaoPosQueryable.Any(c =>
                        c.Estabelecimento.IdEstabelecimento == f.Estabelecimento.IdEstabelecimento &&
                        c.TipoPOS.Id == (int)SubadquirenteEnum.ConnectPagarme) &&
                    f.Profissional.PessoaFisica.IdPessoa == idPessoa &&
                    f.HabilitaSplitPagamento &&
                    f.IdExternosDeBeneficiario != null && f.IdExternosDeBeneficiario != "")
                .Select(f => new KeyValuePair<int, string>(f.Estabelecimento.IdEstabelecimento,
                    f.Estabelecimento.NomeDeExibicaoNoPortal))
                .ToList();
        }

        private string ObterUrlDaFoto(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            return estabelecimentoProfissional.FotoPrincipal.ObterCaminhoWeb(DimemsoesFotosEnum.Dim60x60);
        }

        public IQueryable<ConsultaDeProfissionaisDTO> ListarPaginado(FiltroConsultaDeProfissionais filtro)
        {
            var queryable = ListarPorNomeEmailOuCpf(filtro);
            if (filtro.Paginacao != null)
                queryable = queryable.ToResultadoPaginadoQueryable(filtro.Paginacao);

            return queryable;
        }

        public IQueryable<ConsultaDeProfissionaisDTO> ListarPorNomeEmailOuCpf(FiltroConsultaDeProfissionais filtro)
        {
            var query = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();

            if (filtro.IdEstabelecimento.HasValue && filtro.IdEstabelecimento.Value > 0)
            {
                query = query.Where(p => p.Estabelecimento.IdEstabelecimento == filtro.IdEstabelecimento.Value);
            }

            if (!String.IsNullOrEmpty(filtro.Conteudo))
            {
                switch (filtro.OpcaoFiltroSelecionado)
                {
                    case 0:
                        query = query.Where(p => p.Profissional.PessoaFisica.NomeCompleto.Contains(filtro.Conteudo));
                        break;

                    case 1:
                        query = query.Where(p => p.Profissional.PessoaFisica.Cpf == filtro.Conteudo.SomenteNumeros());
                        break;

                    case 2:
                        query = query.Where(p => p.Profissional.PessoaFisica.Email == filtro.Conteudo.ToLower());
                        break;
                }
            }

            return query.Select(p => new ConsultaDeProfissionaisDTO(p));
        }

        public IQueryable<EstabelecimentoProfissional> ListarPorPF(int idPessoa)
        {
            return Queryable().Where(f => f.Ativo
                && f.Profissional.PessoaFisica.IdPessoa == idPessoa);
        }

        public IList<EstabelecimentoProfissional> ListarPorServicoEstabelecimento(Int32 codigoServicoEstabelecimento)
        {
            if (codigoServicoEstabelecimento == 0)
                return new List<EstabelecimentoProfissional>();

            DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional), "resultado");
            AdicionarCriterioServicoEstabelecimento(codigoServicoEstabelecimento, dc);
            return LoadAll(dc);
        }

        public IQueryable<EstabelecimentoProfissional> ListarProfissionaisComPeloId(IEnumerable<int> idProfissionais,
            int idEstabelecimento)
        {
            IQueryable<EstabelecimentoProfissional> retorno =
                Queryable()
                    .Where(
                        f =>
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            idProfissionais.Contains(f.Profissional.IdProfissional));
            return retorno.Fetch(f => f.Profissional).ThenFetch(f => f.PessoaFisica);
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisComPeloMenosUmServicoAssociado(
            Int32 idEstabelecimento)
        {
            IQueryable<EstabelecimentoProfissional> retorno =
                Queryable()
                    .Where(
                        f =>
                            f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo) &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            return retorno.ToList();
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisComPeloMenosUmServicoAssociadoComInativos(
            Int32 idEstabelecimento)
        {
            IQueryable<EstabelecimentoProfissional> retorno =
                Queryable()
                    .Where(
                        f =>
                            f.EstabelecimentoProfissionalServicoLista.Any() &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            return retorno.ToList();
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(Int32 codigoCategoriaServico)
        {
            if (codigoCategoriaServico == 0)
                return new List<EstabelecimentoProfissional>();

            DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional), "resultado");
            AdicionarCriterioCategoriaServico(codigoCategoriaServico, dc);
            return LoadAll(dc);
        }

        public IList<EstabelecimentoProfissional> ListarProfissionaisPorCategoriaServico(
            List<Int32> codigosCategoriaServico)
        {
            if (!codigosCategoriaServico.Any())
                return new List<EstabelecimentoProfissional>();

            IQueryable<EstabelecimentoProfissional> retorno =
                Queryable()
                    .Where(
                        f =>
                            f.EstabelecimentoProfissionalServicoLista.Any(
                                g =>
                                    g.Ativo &&
                                    codigosCategoriaServico.Contains(
                                        g.ServicoEstabelecimento.ServicoCategoriaEstabelecimento.Codigo)));
            return retorno.ToList();
        }

        public EstabelecimentoProfissional Obter(string email, int codigoEstabelecimento)
        {
            var query = Queryable();
            query = query.Where(ep => ep.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);
            query = query.Where(ep => ep.Profissional.PessoaFisica.Email == email);

            EstabelecimentoProfissional retorno;
            try
            {
                retorno = query.First(t => t.Ativo) ?? query.First();
            }
            catch
            {
                retorno = null;
            }

            return retorno;
        }

        public EstabelecimentoProfissional Obter(Int32 codigoProfissional, Int32 codigoEstabelecimento)
        {
            return Queryable(true).FirstOrDefault(f =>
                f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento &&
                f.Profissional.IdProfissional == codigoProfissional);

            //DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional));
            //AdicionarCriterioEstabelecimento(codigoEstabelecimento, dc);
            //AdicionarCriterioProfissional(codigoProfissional, dc);
            //return Load(dc);
        }

        public EstabelecimentoProfissional ObterInativo(Int32 codigoProfissional, Int32 codigoEstabelecimento)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional));
            AdicionarCriterioEstabelecimento(codigoEstabelecimento, dc);
            AdicionarCriterioProfissional(codigoProfissional, dc);
            AdicionarCriterioAtivo(false, dc);
            return Load(dc);
        }

        public IQueryable<EstabelecimentoProfissional> ObterParaFolhaDePagamento(ParametrosFiltrosRelatorio filtro)
        {
            var retorno = Queryable();
            //retorno = retorno.Fetch(f => f.Profissional).ThenFetch(f => f.PessoaFisica);
            if (!string.IsNullOrWhiteSpace(filtro.Filtro))
                retorno = retorno.Where(f => f.Profissional.PessoaFisica.NomeCompleto.Contains(filtro.Filtro));

            retorno = retorno.Where(f => f.Ativo || (f.DataInativacao.HasValue && f.DataInativacao.Value >= filtro.DataInicial));
            retorno = retorno.Where(f => f.Estabelecimento == filtro.Estabelecimento);

            if (filtro.DataFinal.HasValue)
                retorno = retorno.Where(f => f.DataCadastro < filtro.DataFinal.Value.AddDays(1));

            if (filtro.IdEstabelecimentoProfissional > 0)
                retorno = retorno.Where(f => f.Codigo == filtro.IdEstabelecimentoProfissional);

            if (filtro.IdRelacaoProfissional > 0)
                retorno = retorno.Where(f => f.FormaRelacaoProfissional.Id == filtro.IdRelacaoProfissional);

            retorno = retorno.OrderBy(f => f.Profissional.PessoaFisica.NomeCompleto);

            if (filtro.ParametrosPaginacao != null)
                retorno =
                    retorno.Skip(filtro.ParametrosPaginacao.RegistroInicial - 1)
                        .Take(filtro.ParametrosPaginacao.RegistrosPorPagina);

            return retorno;
        }

        public IQueryable<EstabelecimentoProfissional> ObterParaRelatorioComissoes(ParametrosFiltrosRelatorio filtro)
        {
            var retorno = Queryable();
            //retorno = retorno.Fetch(f => f.Profissional).ThenFetch(f => f.PessoaFisica);
            if (!string.IsNullOrWhiteSpace(filtro.Filtro))
                retorno = retorno.Where(f => f.Profissional.PessoaFisica.NomeCompleto.Contains(filtro.Filtro));

            //TRINKSBUG-134
            //retorno = retorno.Where(f => f.Ativo || (f.DataInativacao.HasValue && f.DataInativacao.Value >= filtro.DataInicial));
            retorno = retorno.Where(f => f.Estabelecimento == filtro.Estabelecimento);

            //if (filtro.DataFinal.HasValue)
            //    retorno = retorno.Where(f => f.DataCadastro < filtro.DataFinal.Value.AddDays(1));

            if (filtro.IdEstabelecimentoProfissional > 0)
                retorno = retorno.Where(f => f.Codigo == filtro.IdEstabelecimentoProfissional);

            if (filtro.IdsProfissional.Any())
                retorno = retorno.Where(f => filtro.IdsProfissional.Contains(f.Profissional.IdProfissional));

            if (filtro.IdRelacaoProfissional > 0)
                retorno = retorno.Where(f => f.FormaRelacaoProfissional.Id == filtro.IdRelacaoProfissional);

            retorno = retorno.OrderBy(f => f.Profissional.PessoaFisica.NomeCompleto);

            if (filtro.ParametrosPaginacao != null)
                retorno =
                    retorno.Skip(filtro.ParametrosPaginacao.RegistroInicial - 1)
                        .Take(filtro.ParametrosPaginacao.RegistrosPorPagina);

            return retorno;
        }

        public EstabelecimentoProfissional ObterPorCodigoIdentificacao(String codigoIdentificacao, Int32 codigoEstabelecimento)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional));
            AdicionarCriterioEstabelecimento(codigoEstabelecimento, dc);
            AdicionarCriterioCodigoIdentificacao(codigoIdentificacao, dc);
            return Load(dc);
        }

        public EstabelecimentoProfissional ObterPorCodigoInterno(string codigoInterno, int idEstabelecimento)
        {
            return
                Queryable()
                    .FirstOrDefault(
                        ep =>
                            ep.CodigoInterno == codigoInterno &&
                            ep.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public EstabelecimentoProfissional ObterPorPessoaFisica(Int32 codigoPessoaFisica, Int32 codigoEstabelecimento)
        {
            var retorno = Queryable(true)
                .FirstOrDefault(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento
                    && f.Profissional.PessoaFisica.IdPessoa == codigoPessoaFisica
                    && f.Ativo);
            return retorno;
        }

        public int? ObterIdEstabelecimentoProfissionalPelaPessoaFisica(int codigoPessoaFisica, int codigoEstabelecimento)
        {
            var retorno = Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento
                    && f.Profissional.PessoaFisica.IdPessoa == codigoPessoaFisica)
                .Select(f => f.Codigo)
                .FirstOrDefault();

            return retorno;
        }


        [Obsolete("Esse método pode ocorrer erro. Use o método com os parâmetros codigoPessoaFisica e idEstabelecimento.")]
        public EstabelecimentoProfissional ObterPorPessoaFisica(Int32 codigoPessoaFisica)
        {
            DetachedCriteria dc = DetachedCriteria.For(typeof(EstabelecimentoProfissional));
            AdicionarCriterioPessoaFisica(codigoPessoaFisica, dc);
            return Load(dc);
        }

        public EstabelecimentoProfissional ObterPorProfissionalEEstabelecimento(int idProfissional, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(ep => ep.Profissional.IdProfissional == idProfissional &&
                                           ep.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public IList<EstabelecimentoProfissional> ListarFiltrandoPelaConfiguracaoDataPagamentoComissao(int idEstabelecimento, bool? ehexcecao, bool? ativo)
        {
            var query = Queryable().Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (ehexcecao.HasValue)
                query = query.Where(ep => ep.EhExcecaoComissaoNaDataPrevistaDeRecebimento == ehexcecao);

            if (ativo.HasValue)
                query = query.Where(ep => ep.Ativo);

            return query.ToList();
        }

        public IList<EstabelecimentoProfissional> ListarFiltrandoPelaConfiguracaoDescontoOperadoraProfissional(int idEstabelecimento, bool? ehexcecao, bool? ativo)
        {
            var query = Queryable().Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            if (ehexcecao.HasValue)
                query = query.Where(ep => ep.EhExcecaoDescontoTaxaOperadora == ehexcecao);

            if (ativo.HasValue)
                query = query.Where(ep => ep.Ativo);

            return query.ToList();
        }

        public IList<EstabelecimentoProfissional> ListarFiltrandoPelaConfiguracaoDescontoOperadoraAssistente(int idEstabelecimento, bool? ehexcecao, bool? ativo)
        {
            var query = Queryable().Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                                ep.EstabelecimentoAssistenteServicoLista.Any(s => s.Ativo));

            if (ehexcecao.HasValue)
                query = query.Where(ep => ep.EhExcecaoDescontoTaxaOperadoraAssistente == ehexcecao);

            if (ativo.HasValue)
                query = query.Where(ep => ep.Ativo);

            return query.ToList();
        }

        public bool PossuiAcessoMinhaAgenda(Int32 idPessoa)
        {
            var statusDesejados = new List<Int32> {
                (int)StatusContaFinanceira.NaoAssinado,
                (int)StatusContaFinanceira.ContaCancelada,
                (int)StatusContaFinanceira.InadimplenteForaTolerancia,
                (int)StatusContaFinanceira.ContaInativa
            };

            return Queryable().Any(ep =>
                    ep.Ativo &&
                    ep.Profissional.PessoaFisica.IdPessoa == idPessoa &&
                    (!ep.Estabelecimento.HabilitaCobranca ||
                     DL.Cobranca.ContaFinanceiraRepository.Queryable(false).Any(cf =>
                                !statusDesejados.Contains(cf.Status.IdStatus) &&
                                cf.Ativo &&
                                cf.Pessoa == ep.Estabelecimento.PessoaJuridica)
                    ) &&
                    (ep.PodeAcessarMinhaAgenda || (ep.EstabelecimentoProfissionalServicoLista.Any(eps => eps.Ativo) &&
                    ep.Profissional.PessoaFisica.VinculoEstabelecimentos.Any(ue => ue.Ativo)))
            );
        }

        public bool PossuiServicoAtivo(int codigo)
        {
            var retorno =
                Queryable().Any(f => f.Codigo == codigo && f.EstabelecimentoProfissionalServicoLista.Any(g => g.Ativo));
            return retorno;
        }

        public void SaveOrUpdate(EstabelecimentoProfissional entity)
        {
            ProfissionalRepository.SaveOrUpdate(entity.Profissional);

            if (entity.Codigo == 0)
                SaveNew(entity);
            else
                Update(entity);
        }

        public List<KeyValuePair<int, string>> ListarIdComNomeDosProfissionaisAtivosNoEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarEstabelecimento(query, idEstabelecimento);
            query = FiltrarAtivo(query);

            return query
                .Select(f => new KeyValuePair<int, string>(f.Codigo, f.Profissional.PessoaFisica.NomeCompleto))
                .OrderBy(kv => kv.Value)
                .ToList();
        }

        public List<KeyValuePair<int, string>> ListarIdProfissionalComNomeDosProfissionaisNoEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarEstabelecimento(query, idEstabelecimento);

            return query
                .OrderByDescending(f => f.Ativo)
                .ThenBy(f => f.Profissional.PessoaFisica.NomeCompleto)
                .Select(f => new KeyValuePair<int, string>(f.Profissional.IdProfissional, f.Ativo ? f.Profissional.PessoaFisica.NomeCompleto : f.Profissional.PessoaFisica.NomeCompleto + " [inativo]"))
                .ToList();
        }

        public List<KeyValuePair<int, string>> ListarKeyValuePair(int idEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarEstabelecimento(query, idEstabelecimento);

            return query.OrderBy(p => p.Profissional.PessoaFisica.NomeCompleto)
                .Select(f => new KeyValuePair<int, string>(f.Codigo, f.Ativo ? f.Profissional.PessoaFisica.NomeOuApelido() : f.Profissional.PessoaFisica.NomeOuApelido() + " [inativo]"))
                .ToList();
        }

        public int? ObterIdPessoaPorEstabelecimentoProfissional(int idEstabelecimentoProfissional)
        {
            var query = Queryable().Where(e => e.Codigo == idEstabelecimentoProfissional);
            return query.Select(e => e.Profissional.PessoaFisica.IdPessoa).FirstOrDefault();
        }

        public List<EstabelecimentoProfissional> ObterEstabelecimentoProfissionaisEnvolvidosNaTransacao(int idTransacao)
        {
            var estabelecimentoProfissionais = new List<EstabelecimentoProfissional>();

            var epsDosHorarios = EstabelecimentosProfissionaisDosHorariosDaTransacao(idTransacao).ToList();
            estabelecimentoProfissionais.AddRange(epsDosHorarios);

            var epasDosHorarios = EstabelecimentosProfissionaisAssistentesDosHorariosDaTransacao(idTransacao).ToList();
            estabelecimentoProfissionais.AddRange(epasDosHorarios);

            var epsDosItensVenda = EstabelecimentoProfissionaisDosItensVenda(idTransacao).ToList();
            estabelecimentoProfissionais.AddRange(epsDosItensVenda);

            return estabelecimentoProfissionais.Distinct().ToList();
        }

        private static IQueryable<EstabelecimentoProfissional> EstabelecimentoProfissionaisDosItensVenda(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);
            var todosEstabelecimentosProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
            var todosItensVendas = Domain.Vendas.ItemVendaRepository.Queryable();

            var epsDosItensVenda = from prof in Domain.Pessoas.ProfissionalRepository.Queryable()
                                   join ep in todosEstabelecimentosProfissionais on prof equals ep.Profissional
                                   join iv in todosItensVendas on prof.PessoaFisica equals iv.PessoaComissionada
                                   where ep.Estabelecimento == estabelecimento && iv.Venda.Transacao.Id == idTransacao
                                   select ep;

            return epsDosItensVenda;
        }

        private static IQueryable<EstabelecimentoProfissional> EstabelecimentosProfissionaisDosHorariosDaTransacao(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);

            var todosEstabelecimentosProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
            var todosHorariosTransacao = Domain.Pessoas.HorarioTransacaoRepository.Queryable();

            var epsDosHorarios = from ep in todosEstabelecimentosProfissionais
                                 join ht in todosHorariosTransacao on ep.Profissional equals ht.Horario.Profissional
                                 where ep.Estabelecimento == estabelecimento && ht.Transacao.Id == idTransacao
                                 select ep;
            return epsDosHorarios;
        }

        private static IQueryable<EstabelecimentoProfissional> EstabelecimentosProfissionaisAssistentesDosHorariosDaTransacao(int idTransacao)
        {
            var transacao = Domain.Financeiro.TransacaoRepository.Load(idTransacao);
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorPessoaJuridica(transacao.PessoaQueRecebeu.IdPessoa);

            var todosEstabelecimentosProfissionais = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
            var todosHorariosTransacao = Domain.Pessoas.HorarioTransacaoRepository.Queryable();

            var epsDosHorarios = from ep in todosEstabelecimentosProfissionais
                                 join ht in todosHorariosTransacao on ep equals ht.Horario.EstabelecimentoProfissionalAssistente
                                 where ep.Estabelecimento == estabelecimento && ht.Transacao.Id == idTransacao
                                 select ep;
            return epsDosHorarios;
        }

        #endregion Métodos Públicos

        #region Filtros LINQ

        private static IQueryable<EstabelecimentoProfissional> FiltrarAtivo(
            IQueryable<EstabelecimentoProfissional> retorno)
        {
            return retorno.Where(ep => ep.Ativo);
        }

        private IQueryable<EstabelecimentoProfissional> FiltrarPorNome(IQueryable<EstabelecimentoProfissional> retorno, string nome)
        {
            return retorno.Where(ep => ep.Profissional.PessoaFisica.NomeCompleto.Contains(nome));
        }

        private static IQueryable<EstabelecimentoProfissional> FiltrarEstabelecimento(
            IQueryable<EstabelecimentoProfissional> retorno, Int32 codigoDoEstabelecimento)
        {
            return retorno.Where(ep => ep.Estabelecimento.IdEstabelecimento == codigoDoEstabelecimento);
        }

        private static IQueryable<EstabelecimentoProfissional> FiltrarPorProfissionaisComCodigoPAT(
            IQueryable<EstabelecimentoProfissional> retorno)
        {
            return retorno.Where(ep => ep.CodigoDeAcessoAoPainelAtendimento != "");
        }

        private static IQueryable<EstabelecimentoProfissional> FiltrarPorProfissionaisComServicosAssociados(
            IQueryable<EstabelecimentoProfissional> retorno)
        {
            return retorno.Where(ep => ep.EstabelecimentoProfissionalServicoLista.Any(p2 => p2.Ativo)
                                       || ep.EstabelecimentoAssistenteServicoLista.Any(p3 => p3.Ativo));
        }

        private static IQueryable<EstabelecimentoProfissional> FiltrarServicoEstabelecimento(
            IQueryable<EstabelecimentoProfissional> retorno, Int32 codigoDoServicoEstabelecimento)
        {
            return
                retorno.Where(
                    ep =>
                        ep.EstabelecimentoProfissionalServicoLista.Any(
                            eps =>
                                eps.ServicoEstabelecimento.IdServicoEstabelecimento == codigoDoServicoEstabelecimento &&
                                eps.Ativo));
        }

        #endregion Filtros LINQ

        #region Critérios

        private static void AdicionarCriterioAtivo(bool ativo, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissional.Ativo, ativo));
        }

        private static void AdicionarCriterioCategoriaServico(Int32 codigoCategoriaServico, DetachedCriteria dc)
        {
            ServicoEstabelecimento[] servicosEstabelecimento =
                Domain.Pessoas.ServicoEstabelecimentoRepository
                    .ListarAtivosAssociadosAProfissionaisPorCategoriaEstabelecimento(codigoCategoriaServico).ToArray();
            string className = typeof(EstabelecimentoProfissionalServico).Name;

            DetachedCriteria subQuery = DetachedCriteria.For(typeof(EstabelecimentoProfissionalServico), className)
                .SetProjection(Projections.Id())
                .Add(Restrictions.In(PropertyNames.Pessoas.EstabelecimentoProfissionalServico.ServicoEstabelecimento,
                    servicosEstabelecimento))
                .Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissionalServico.Ativo, true))
                .Add(
                    Restrictions.EqProperty(
                        PropertyNames.Pessoas.EstabelecimentoProfissionalServico.EstabelecimentoProfissional,
                        "resultado.Codigo"));

            dc.Add(Subqueries.Exists(subQuery));
        }

        private static void AdicionarCriterioCodigoIdentificacao(String codigoIdentificacao, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissional.CodigoInterno, codigoIdentificacao));
        }

        private static void AdicionarCriterioEstabelecimento(Int32 codigoEstabelecimento, DetachedCriteria dc)
        {
            dc.Add(
                Restrictions.Eq(
                    PropertyNames.Pessoas.EstabelecimentoProfissional.Estabelecimento + "." +
                    PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento, codigoEstabelecimento));
        }

        private static void AdicionarCriterioPessoaFisica(Int32 codigoPessoaFisica, DetachedCriteria dc)
        {
            dc.CreateAlias(PropertyNames.Pessoas.EstabelecimentoProfissional.Profissional, "Profissional");
            dc.CreateAlias("Profissional.PessoaFisica", "PessoaFisica");
            dc.Add(Restrictions.Eq("PessoaFisica.IdPessoa", codigoPessoaFisica));
        }

        private static void AdicionarCriterioProfissional(Int32 codigoProfissional, DetachedCriteria dc)
        {
            dc.Add(Restrictions.Eq(
                typeof(Profissional).Name + "." + PropertyNames.Pessoas.Profissional.IdProfissional, codigoProfissional));
        }

        private static void AdicionarCriterioServicoEstabelecimento(Int32 codigoServicoEstabelecimento,
            DetachedCriteria dc)
        {
            ServicoEstabelecimento servicoEstabelecimento =
                Domain.Pessoas.ServicoEstabelecimentoRepository.Load(codigoServicoEstabelecimento);
            string className = typeof(EstabelecimentoProfissionalServico).Name;

            DetachedCriteria subQuery = DetachedCriteria.For(typeof(EstabelecimentoProfissionalServico), className)
                .SetProjection(Projections.Id())
                .Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissionalServico.ServicoEstabelecimento,
                    servicoEstabelecimento))
                .Add(Restrictions.Eq(PropertyNames.Pessoas.EstabelecimentoProfissionalServico.Ativo, true))
                .Add(
                    Restrictions.EqProperty(
                        PropertyNames.Pessoas.EstabelecimentoProfissionalServico.EstabelecimentoProfissional,
                        "resultado.Codigo"));

            dc.Add(Subqueries.Exists(subQuery));
        }

        #endregion Critérios

        public EstabelecimentoProfissional ObterPorId(int idEstabelecimentoProfissional)
        {
            return Queryable().FirstOrDefault(p => p.Codigo == idEstabelecimentoProfissional);
        }

        public IQueryable<EstabelecimentoProfissional> ObterQueryPorId(int idEstabelecimentoProfissional, int idEstabelecimento)
        {
            return Queryable().Where(ep => ep.Codigo == idEstabelecimentoProfissional && ep.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public IList<EstabelecimentoProfissional> ObterProfissionaisAtivosPorIdEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento && ep.Ativo).ToList();
        }

        public bool EhAssistenteEmPeloMenosUmServico(int idProfissional)
        {
            return Queryable().Any(p => p.Ativo && p.EstabelecimentoAssistenteServicoLista.Any(a => a.Ativo && a.EstabelecimentoProfissional.Profissional.IdProfissional == idProfissional));
        }

        public int ObterQuantidadeDeProfissionaisAtivosComAgendaNoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Ativo && p.PossuiAgenda).Count();
        }

        public bool EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(int idProfissional, int idEstabelecimento)
        {
            var estabelecimentoProfissional = Obter(idProfissional, idEstabelecimento);
            return EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(estabelecimentoProfissional);
        }

        public bool EstabelecimentoProfissionalEstaUtilizandoSplitPagamentoPelaPessoa(int idPessoaProfissional, int idPessoaEstabelecimento)
        {
            var estabelecimentoProfissional = Queryable().FirstOrDefault(f => f.Profissional.PessoaFisica.IdPessoa == idPessoaProfissional && f.Estabelecimento.PessoaJuridica.IdPessoa == idPessoaEstabelecimento);
            return EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(estabelecimentoProfissional);
        }

        public IList<EstabelecimentoProfissional> EstabelecimentoProfissionalcomSplitPagamentoPorEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(p => p.IdExternosDeBeneficiario != null && p.Estabelecimento.IdEstabelecimento == idEstabelecimento).ToList();
        }
        private static bool EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var estabelecimentoEstaUtilizandoSplitPagamento = Domain.Pessoas.EstabelecimentoRepository.EstabelecimentoEstaUtilizandoSplitDePagamento(estabelecimentoProfissional.Estabelecimento.IdEstabelecimento);
            var idRecebedor = ObterIdRecebedor(estabelecimentoProfissional);
            return estabelecimentoEstaUtilizandoSplitPagamento &&
                   estabelecimentoProfissional.HabilitaSplitPagamento &&
                   !estabelecimentoProfissional.AguardandoRetornoDaHabilitacaoDoSplit &&
                   !string.IsNullOrEmpty(idRecebedor);
        }
        private static string ObterIdRecebedor(EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var configuracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                                  .ObterConfiguracaoPorEstabelecimento(estabelecimentoProfissional.Estabelecimento.IdEstabelecimento);

            return configuracaoPOS != null && configuracaoPOS.TipoPOS != null &&
                configuracaoPOS.TipoPOS.Id == (int)SubadquirenteEnum.ConnectPagarme
                ? estabelecimentoProfissional.IdExternosDeBeneficiario
                : estabelecimentoProfissional.CodigoIdentificacaoPOS;
        }

        public string ObterCodigoIdentificadorPOSDoProfissional(int idProfissional, int idEstabelecimento)
        {
            var query = Queryable().Where(p => p.Profissional.IdProfissional == idProfissional && p.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            return query.Select(p => p.CodigoIdentificacaoPOS).FirstOrDefault();
        }

        public bool ExisteOutroProfissionalParceiroUtilizandoCnpj(int idEstabelecimento, string cnpj, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var query = Queryable();
            query = query.Where(f => f != estabelecimentoProfissional); // que não seja ele
            query = query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento); // Do mesmo estabelecimento
            query = query.Where(f => f.PessoaJuridica != null && f.PessoaJuridica.CNPJ == cnpj); // Com PJ com mesmo CNPJ

            if (estabelecimentoProfissional.PessoaJuridica != null)
                query = query.Where(f => f.PessoaJuridica != estabelecimentoProfissional.PessoaJuridica); // Que seja outra PJ

            return query.Any();
        }

        public IQueryable<EstabelecimentoProfissional> ObterAdministradoresERecepcionistasDoEstabelecimento(int idEstabelecimento)
        {
            var queryableUsuarioEstabelecimento = Domain.Pessoas.UsuarioEstabelecimentoRepository.Queryable();

            return from ep in Queryable()
                   join ue in queryableUsuarioEstabelecimento on ep.Profissional.PessoaFisica.IdPessoa equals ue.PessoaFisica.IdPessoa
                   where ep.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                         ue.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                         (ue.PerfilAcesso == Enums.AcessoBackoffice.Acesso_total
                         || ue.PerfilAcesso == Enums.AcessoBackoffice.Somente_Agenda
                         || ue.UsuarioPerfil.UsuarioPerfilPermissoes.Any(p => p.Permissao == Permissao.VendaDeProduto))
                         && ep.Ativo
                   select ep;
        }

        public int ObterIdEstabelecimentoDoProfissional(int idEstabelecimentoProfissional)
        {
            int? idEstabelecimento = Queryable().Where(ep => ep.Codigo == idEstabelecimentoProfissional).Select(ep => ep.Estabelecimento.IdEstabelecimento).FirstOrDefault();
            return idEstabelecimento ?? 0;
        }

        public Profissional ObterProfissionalPeloIdEstabelecimentoProfissional(int idEstabelecimentoProfissional)
        {
            return Queryable()
                .Where(ep => ep.Codigo == idEstabelecimentoProfissional)
                .Select(ep => ep.Profissional)
                .FirstOrDefault();
        }

        public int? ObterIdEstabelecimentoProfissional(int idProfissional, int idEstabelecimento)
        {
            var idEstabelecimentoProfissional = Queryable()
                .Where(ep => ep.Profissional.IdProfissional == idProfissional && ep.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(ep => ep.Codigo)
                .FirstOrDefault();

            return idEstabelecimentoProfissional;
        }

        public IList<EstabelecimentoProfissional> ObterListaDeProfissionalAtivoNoEstabelecimentoPorPessoa(int idPessoa)
        {
            return Queryable().Where(ce => ce.Profissional.PessoaFisica.IdPessoa == idPessoa).ToList();
        }

        public IQueryable<EstabelecimentoProfissional> Filtrar(ParametrosBusca parametros)
        {
            var retorno = Queryable().Where(f => f.Ativo);

            if (parametros.Estabelecimentos != null && parametros.Estabelecimentos.Any())
                retorno = retorno.Where(f => parametros.Estabelecimentos.Contains(f.Estabelecimento.IdEstabelecimento));

            if (parametros.Profissionais != null && parametros.Profissionais.Any())
                retorno = retorno.Where(f => parametros.Profissionais.Contains(f.Profissional.IdProfissional));

            return retorno;
        }

        public bool ProfissionalEstaAtivoRealizaServicoEPossuiAgenda(Int32 idEstabelecimentoProfissional, int idEstabelecimento)
        {
            return Queryable().Where(f => f.Profissional.EstabelecimentoProfissionalLista
                    .Any(g => g.Ativo && g.PossuiAgenda && g.EstabelecimentoProfissionalServicoLista.Any(h => h.Ativo)
                        && g.Estabelecimento.IdEstabelecimento == idEstabelecimento))
                     .Any(ep => ep.Codigo == idEstabelecimentoProfissional);
        }

        public IQueryable<EstabelecimentoProfissional> ObterProfissionaisComAgendaDoEstabelecimento(int idEstabelecimento, bool ativo = true)
        {
            return Queryable()
                .Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento
                          && ep.PossuiAgenda
                          && ep.Ativo == ativo);
        }

        public IQueryable<EstabelecimentoProfissional> ObterProfissionaisComAgendaDoEstabelecimentoQueRealizamServico(int idEstabelecimento, int idServicoEstabelecimento, bool ativo = true)
        {
            return Queryable()
                .Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento
                          && ep.PossuiAgenda
                          && ep.Ativo == ativo
                          && ep.EstabelecimentoProfissionalServicoLista.Any(
                                eps =>
                                    eps.ServicoEstabelecimento.IdServicoEstabelecimento == idServicoEstabelecimento &&
                                    eps.Ativo));
        }

        public IList<ApelidoENomeProfissionalDTO> ListarApelidoENomeDeTodosProfissionais(int idEstabelecimento)
        {
            return Queryable()
                .Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(ep => new ApelidoENomeProfissionalDTO
                {
                    IdPessoa = ep.Profissional.PessoaFisica.IdPessoa,
                    Apelido = ep.Profissional.PessoaFisica.Apelido,
                    NomeCompleto = ep.Profissional.PessoaFisica.NomeCompleto,
                }).ToList();
        }

        // Add param tipoPos ??
        public EstabelecimentoProfissional ObterProfissionalPorIdExternosDeBeneficiario(string idExternosDeBeneficiario)
        {
            return Queryable().FirstOrDefault(p => p.IdExternosDeBeneficiario == idExternosDeBeneficiario && p.AguardandoRetornoDaHabilitacaoDoSplit == true);
        }

        public List<EstabelecimentoProfissional> ObterProfissionaisSemCodigoIdentificacaoPOS()
        {
            return Queryable().Where(p => p.IdExternosDeBeneficiario != null && p.IdExternosDeBeneficiario != string.Empty && !p.DataFimDoContratoDoProfissional.HasValue && (p.AguardandoRetornoDaHabilitacaoDoSplit == true || p.CodigoIdentificacaoPOS == null)).ToList();
        }

        public List<EstabelecimentoProfissional> ObterEstabelecimentoProfissionaisParaCampanha(int idEstabelecimento)
        {
            var profissionais = Queryable();
            profissionais = profissionais.Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                                                 p.Ativo).OrderBy(p => p.Profissional.PessoaFisica.NomeCompleto);

            return profissionais.ToList();
        }

        public DateTime? ObterDataUltimoProfissionalCadastrado(int idEstabelecimento, int idPessoaResponsavelFinanceiro)
        {
            return Queryable().Where(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento && ep.Profissional.PessoaFisica.IdPessoa != idPessoaResponsavelFinanceiro)
                .OrderByDescending(ep => ep.DataCadastro)
                .Select(ep => ep.DataCadastro)
                .FirstOrDefault();
        }

        public PessoaFisica ObterPessoaFisica(int idEstabelecimentoProfissional)
        {
            return Queryable()
                .Where(ep => ep.Codigo == idEstabelecimentoProfissional)
                .Select(ep => ep.Profissional.PessoaFisica)
                .FirstOrDefault();
        }

        public bool ProfissionalInformouSenhaDeAcessoAoPAT(int idEstabelecimentoProfissional)
        {
            return Queryable().Any(ep => ep.Codigo == idEstabelecimentoProfissional && ep.CodigoDeAcessoAoPainelAtendimento != null);
        }

        public bool UtilizaMenuLateral(string email, int idConta, int idEstabelecimento)
        {
            var estabelecimentoProfissional = DL.Pessoas.EstabelecimentoProfissionalRepository.Obter(email, idEstabelecimento);
            var contaFranquia = DL.Pessoas.ContaFranquiaRepository.ObterContaVinculadaAoSalao(idConta, idEstabelecimento);
            return (estabelecimentoProfissional != null && estabelecimentoProfissional.UtilizaMenuLateral) || (contaFranquia != null && contaFranquia.UtilizaMenuLateral);
        }

        public List<KeyValuePair<int, string>> ListarIdPessoaComNomeDosProfissionais(int idEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarEstabelecimento(query, idEstabelecimento);

            return query
                .OrderByDescending(f => f.Ativo)
                .ThenBy(f => f.Profissional.PessoaFisica.NomeCompleto)
                .Select(f => new KeyValuePair<int, string>(f.Profissional.PessoaFisica.IdPessoa, f.Ativo ? f.Profissional.PessoaFisica.NomeCompleto : f.Profissional.PessoaFisica.NomeCompleto + " [inativo]"))
                .ToList();
        }

        public List<int> ListarIdPessoaPorIdProfissional(List<int> idsProfissional, int idEstabelecimento)
        {
            return Queryable()
                .Where(p => idsProfissional.Contains(p.Profissional.IdProfissional) &&
                            p.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(p => p.Profissional.PessoaFisica.IdPessoa)
                .ToList();
        }

        public List<KeyValuePair<int, string>> ListarIdProfissionalComNomePorEstabelecimento(int idEstabelecimento)
        {
            var query = Queryable();
            query = FiltrarEstabelecimento(query, idEstabelecimento);

            return query
                .OrderByDescending(p => p.Ativo)
                .Select(p => new KeyValuePair<int, string>(p.Profissional.IdProfissional,
                    (p.Profissional.PessoaFisica.Apelido ?? p.Profissional.PessoaFisica.NomeCompleto) +
                    (p.Ativo ? string.Empty : " [Inativo]")))
                .OrderBy(kv => kv.Value)
                .ToList();
        }

        public DadosEstabelecimentoProfissionalCadastroContaDigitalDTO ObterDadosEstabelecimentoProfissionalParaCadastroContaDigital(int idPessoa, int idEstabelecimento)
        {
            var query = Queryable();


            var dados = query
                        .Where(p => p.Profissional.PessoaFisica.IdPessoa == idPessoa)
                        .Select(p => new DadosEstabelecimentoProfissionalCadastroContaDigitalDTO
                        {
                            IdEstabelecimentoProfissional = p.Codigo,
                            IdPessoa = p.Profissional.PessoaFisica.IdPessoa,
                            Email = p.Profissional.PessoaFisica.Email,
                            Nome = p.Profissional.PessoaFisica.NomeCompleto,
                        }).FirstOrDefault();

            if (dados == null)
                return dados;

            var idPessoaEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterIdPessoaDoEstabelecimento(idEstabelecimento);

            var telefone = Domain.Pessoas.TelefoneRepository.ObterDoPrimeiroTelefoneCelularAtivoFormatadosDoProfissional(dados.IdPessoa, idPessoaEstabelecimento);

            dados.NumeroTelefone = telefone != null ? telefone.ToString() : string.Empty;


            return dados;
        }
        public List<EstabelecimentoProfissional> ObterProfissionaisPelasComissoes(List<int> idPessoasComissionadas, int idEstabelecimento)
        {
            return Queryable().Where(p => idPessoasComissionadas.Contains(p.Profissional.PessoaFisica.IdPessoa) && p.Estabelecimento.IdEstabelecimento == idEstabelecimento).ToList();
        }

        public ProfissionalDto ObterProfissionalPorContaEEstabelecimento(int idConta, int idEstabelecimento)
        {
            var conta = Domain.Pessoas.ContaRepository.Load(idConta);

            var profissionalDto = Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .Select(ep => new ProfissionalDto
                {
                    Id = ep.Profissional.IdProfissional,
                    IdPessoa = ep.Profissional.PessoaFisica.IdPessoa,
                    IdProfissionalEstabelecimento = ep.Codigo,
                    IdEstabelecimento = ep.Estabelecimento.IdEstabelecimento,
                    Ativo = ep.Ativo,
                    Nome = ep.Profissional.PessoaFisica.Apelido ?? ep.Profissional.PessoaFisica.NomeCompleto,
                    UtilizaSplitDePagamentos = ep.HabilitaSplitPagamento && !string.IsNullOrEmpty(ep.IdExternosDeBeneficiario)
                })
               .FirstOrDefault(f => f.IdPessoa == conta.Pessoa.IdPessoa
                                    && f.IdEstabelecimento == idEstabelecimento
                                    && f.Ativo);

            return profissionalDto;
        }

        public EstabelecimentoProfissional ObterPorEstabelecimentoEStoneCode(int idEstabelecimento, string stoneCode)
        { 
            return Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable()
                .FirstOrDefault(ep => ep.Estabelecimento.IdEstabelecimento == idEstabelecimento && 
                ep.CodigoIdentificacaoPOS == stoneCode);
        }
    }
}