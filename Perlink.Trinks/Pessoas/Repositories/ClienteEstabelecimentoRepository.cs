﻿using Castle.ActiveRecord;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.Linq;
using NHibernate.Linq.Functions;
using NHibernate.Transform;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.DTO;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class ClienteEstabelecimentoRepository : IClienteEstabelecimentoRepository
    {

        #region Métodos

        private IClienteRepository ClienteRepository
        {
            get { return Domain.Pessoas.ClienteRepository; }
        }
        public ClienteEstabelecimento ObterClienteEstabelecimentoPorCPFeEstabelecimento(int idEstabelecimento, string cpf)
        {
            return Queryable().FirstOrDefault(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento && p.Cliente.PessoaFisica.Cpf == cpf && p.Ativo);
        }

        public Boolean ExisteClienteEstabelecimentoComEmail(Int32 codigoCliente, String email)
        {
            return Queryable().Any(f => f.Codigo == codigoCliente && f.Cliente.PessoaFisica.Email == email);
        }

        public List<ClienteEstabelecimento> ListarClienteEstabelecimentoPorCpf(String cpf)
        {
            var retorno = Queryable().Where(f => f.Ativo && f.Cliente.PessoaFisica.Cpf == cpf);
            return retorno.ToList();
        }

        public List<ClienteEstabelecimento> ListarClientesEstabelecimentoPorIdCliente(Int32 idCliente)
        {
            return Queryable().Where(f => f.Cliente.IdCliente == idCliente).ToList();
        }

        public List<ClienteEstabelecimento> ListarClienteEstabelecimentoPorEmail(String email)
        {
            return Queryable().Where(f => f.Cliente.PessoaFisica.Email == email && f.Cliente.PessoaFisica.Ativo).ToList();
        }

        public IList<ClienteEstabelecimento> ListarPorEstabelecimento(Int32 codigoEstabelecimento)
        {
            var dc = DetachedCriteria.For(typeof(ClienteEstabelecimento));
            dc.Add(
                Restrictions.Eq(
                    String.Format("{0}.{1}", typeof(Estabelecimento).Name,
                        PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento), codigoEstabelecimento));
            dc.Add(Restrictions.Eq(PropertyNames.Pessoas.ClienteEstabelecimento.Ativo, true));
            dc.AddOrder(new Order(PropertyNames.Pessoas.ClienteEstabelecimento.NomeCompletoCliente, true));
            return LoadAll(dc);
        }

        public ResultadoPaginado<ClienteEstabelecimento> ListarPorEstabelecimentoPaginado(
            ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool filtrarPorTelefone)
        {
            var retorno = ObterClientesComFiltro(parametros, filtraPorCPF, filtraPorEmail, filtrarPorTelefone, false);

            var posicaoInicial = (parametros.ParametrosPaginacao.RegistrosPorPagina *
                                  parametros.ParametrosPaginacao.Pagina -
                                  parametros.ParametrosPaginacao.RegistrosPorPagina);
            parametros.ParametrosPaginacao.TotalItens = retorno.Count();
            retorno = retorno.Skip(posicaoInicial).Take(parametros.ParametrosPaginacao.RegistrosPorPagina);

            return new ResultadoPaginado<ClienteEstabelecimento>(retorno.ToList(), parametros.ParametrosPaginacao);
        }

        public IQueryable<ClienteEstabelecimento> ListarPorEstabelecimentoQueryable(int idEstabelecimento)
        {
            return Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Ativo);
        }

        public decimal ObterValorCreditoClienteEstabelecimento(int idCliente, int idEstabelecimento)
        {
            return Queryable().Where(c => c.Cliente.IdCliente == idCliente && c.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                              .Select(c => c.ValorCredito)
                              .FirstOrDefault();
        }

        public ClienteEstabelecimento ObterClienteEstabelecimento(Int32 idCliente, Int32 idEstabelecimento)
        {
            var dc = DetachedCriteria.For(typeof(ClienteEstabelecimento));
            AdicionarCriterioIdCliente(dc, idCliente);
            AdicionarCriterioEstabelecimento(dc, idEstabelecimento);
            AdicionarFetchEmClienteEEstabelecimento(dc);
            return Load(dc);
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorCpf(String cpf, Int32 codigoEstabelecimento)
        {
            if (!String.IsNullOrEmpty(cpf))
                cpf = cpf.RemoverFormatacaoCPFeCPNJ();

            return Queryable().FirstOrDefault(ce => ce.Cliente.PessoaFisica.Cpf == cpf && ce.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoIncluindoInativo(String cpf,
            Int32 codigoEstabelecimento)
        {
            if (!String.IsNullOrEmpty(cpf))
                cpf = cpf.RemoverFormatacaoCPFeCPNJ();

            var query = Queryable();
            var resultado = (from l in query
                             where l.Cliente.PessoaFisica.Cpf == cpf && l.Estabelecimento.IdEstabelecimento == codigoEstabelecimento && l.Cliente.PessoaFisica.Ativo
                             select l).FirstOrDefault();

            return resultado;
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorEmailAssociacao(String email,
            Int32 codigoEstabelecimento)
        {
            var query = Queryable().Where(f => f.Cliente.PessoaFisica.Email == email && f.Cliente.PessoaFisica.Ativo);

            if (codigoEstabelecimento > 0)
                query = query.Where(f => f.Estabelecimento.IdEstabelecimento == codigoEstabelecimento);

            return query.FirstOrDefault();
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorEmailContaOuAssociacao(String email,
            Int32 codigoEstabelecimento)
        {
            var clienteEstabelecimento = ObterClienteEstabelecimentoPorEmailAssociacao(email, codigoEstabelecimento);

            if (clienteEstabelecimento != null)
                return clienteEstabelecimento;

            var cliente = ClienteRepository.ObterPorEmail(email);
            return cliente != null ? new ClienteEstabelecimento { Cliente = cliente, Ativo = false, Estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(codigoEstabelecimento) } : null;
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorEmailAssociadoAoEstabelecimentoOuAUmClienteWeb(String email, Int32 codigoEstabelecimento)
        {
            var clienteEstabelecimento = ObterClienteEstabelecimentoPorEmailAssociacao(email, codigoEstabelecimento);

            if (clienteEstabelecimento != null)
                return clienteEstabelecimento;

            var clienteWeb = ClienteRepository.ObterPorEmail(email);

            if (clienteWeb != null && clienteWeb.TipoCliente == TipoClienteEnum.Web)
            {
                return new ClienteEstabelecimento { Cliente = clienteWeb, Ativo = false, Estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(codigoEstabelecimento) };
            }

            return null;
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorPF(Int32 idPessoa, Int32 idEstabelecimento)
        {
            //DetachedCriteria dc = DetachedCriteria.For<ClienteEstabelecimento>();
            //AdicionarAliasCliente(dc);
            //AdicionarAliasPessoaFisica(dc);
            //dc.Add(Restrictions.Eq(PropertyNames.Pessoas.Profissional.PessoaFisica + "." + PropertyNames.Pessoas.Pessoa.IdPessoa, idPessoa));
            //AdicionarCriterioEstabelecimento(dc, idEstabelecimento);
            //dc.SetCacheable(true);
            //return Load(dc);

            var retorno = Queryable(true);
            return
                retorno.FirstOrDefault(
                    f =>
                        f.Cliente.PessoaFisica.IdPessoa == idPessoa &&
                        f.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public Int32 ObterIdClienteEstabelecimentoPorPF(Int32 idPessoa, Int32 idEstabelecimento)
        {
            var retorno = StatelessQueryable();
            return
                retorno.Where(
                    f =>
                        f.Cliente.PessoaFisica.IdPessoa == idPessoa &&
                        f.Estabelecimento.IdEstabelecimento == idEstabelecimento).Select(p => p.Codigo).FirstOrDefault();
        }

        public List<ClienteEstabelecimento> ObterClientesEstabelecimentoComEnvioNotificacaoNaoDefinido(
            Int32 idEstabelecimento)
        {
            var query = Queryable();
            return
                query.Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.RecebeNotificacao == null)
                    .ToList();
        }

        public List<ClienteEstabelecimento> ObterPendentesDeNotificacao(int limiteRegistros = 1000, bool stateless = false, int idEstabelecimento = 0)
        {
            var busca = Domain.Pessoas.HorarioRepository.ObterPendentesDeNotificacao(stateless);

            if (idEstabelecimento > 0)
                busca = busca.Where(h => h.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            return busca.Take(limiteRegistros)
                .Select(f => f.ClienteEstabelecimento)
                .ToList()
                .Distinct()
                .ToList();
        }

        public void SaveOrUpdate(ClienteEstabelecimento entidade, int? idPessoaQueAlterou)
        {
            entidade.RegistrarEdicao(idPessoaQueAlterou);
            if (entidade.Codigo > 0)
                Update(entidade);
            else
                SaveNew(entidade);
        }

        public ClienteEstabelecimento ObterPorIdExterno(string idExterno, int idEstabelecimento)
        {
            return
                Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            f.IdExterno.ToLower() == idExterno.ToLower());
        }

        public ClienteEstabelecimento ObterPorCpf(string cpf, int idEstabelecimento)
        {
            return
                Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Ativo &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            f.Cliente.PessoaFisica.Cpf == cpf);
        }

        public ClienteEstabelecimento ObterPorEmail(string email, int idEstabelecimento)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;
            return
                Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Ativo &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            f.Cliente.PessoaFisica.Email == email.ToLower());
        }

        public ClienteEstabelecimento ObterPorNome(string nome, int idEstabelecimento)
        {
            if (string.IsNullOrWhiteSpace(nome))
                return null;
            return
                Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Ativo &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            f.Cliente.PessoaFisica.NomeCompleto.ToLower() == nome.ToLower());
        }

        public ClienteEstabelecimento ObterPorTelefone(string telefone, int idEstabelecimento)
        {
            if (string.IsNullOrWhiteSpace(telefone))
                return null;
            return
                Queryable()
                    .FirstOrDefault(
                        f =>
                            f.Ativo &&
                            f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                            f.Cliente.PessoaFisica.Telefones.Any(g => g.Numero == telefone && g.Ativo));
        }

        public List<ClienteEstabelecimento> ListarAniversariantes(int idEstabelecimento, DateTime dataComparacao)
        {
            var aniversariantes = Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento
                            && f.DataNascimentoCliente != null
                            && f.DataNascimentoCliente.Value.Day == dataComparacao.Day && f.DataNascimentoCliente.Value.Month == dataComparacao.Month);
            return aniversariantes.ToList();
        }

        public IQueryable<ClienteEstabelecimento> ListarClientesDoEstabelecimentoPorIds(List<int> idsClientes, int idEstabelecimento)
        {
            var queryable = Queryable().Where(f => idsClientes.Contains(f.Cliente.IdCliente));
            queryable = queryable.Where(p => p.Estabelecimento.IdEstabelecimento == idEstabelecimento);
            return queryable;
        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorEmail(String email, Int32 codigoEstabelecimento)
        {
            var cliente = ClienteRepository.ObterPorEmail(email);

            var clientes = this.ListarClienteEstabelecimentoPorEmail(email);

            if (clientes.Count <= 0)
            {
                if (cliente != null)
                    return new ClienteEstabelecimento { Cliente = cliente };
                return null;
            }

            var clienteWeb = clientes.Where(a => a.Cliente.TipoCliente == TipoClienteEnum.Web).FirstOrDefault();

            if (clienteWeb != null)
                return clienteWeb;

            if (cliente != null && cliente.TipoCliente == TipoClienteEnum.Web)
            {
                return new ClienteEstabelecimento { Cliente = cliente };
            }

            var clienteBalcao = clientes.Where(a => a.Estabelecimento.IdEstabelecimento == codigoEstabelecimento && String.IsNullOrEmpty(a.EmailAlternativo)).FirstOrDefault();

            if (clienteBalcao == null)
            {
                if (cliente != null)
                    return new ClienteEstabelecimento { Cliente = cliente };
            }

            return clienteBalcao;
        }

        #endregion Métodos

        #region Trinks em Números

        public IList<Estabelecimento> ObterEstabelecimentosRecentesComESemHorariosDeUmCliente(Int32 idCliente, int quantidadeLimite)
        {
            var estabelecimentosEmQueOClienteMarcouHoraRecentemente =
                Domain.Pessoas.HorarioRepository.ObterEstabelecimentosRecentementeAgendadosPorUmCliente(idCliente).Take(quantidadeLimite);

            var estabelecimentosOndeOClienteEstaCadastrado = (from h in Queryable()
                                                              where h.Cliente.IdCliente == idCliente &&
                                                                    h.Cliente.PessoaFisica.Ativo
                                                              orderby h.Estabelecimento.NomeDeExibicaoNoPortal
                                                              select h.Estabelecimento).Take(quantidadeLimite);

            var todosOsEstabelecimentos = estabelecimentosEmQueOClienteMarcouHoraRecentemente.ToList().
                Union(estabelecimentosOndeOClienteEstaCadastrado.ToList())
                .GroupBy(p => p.IdEstabelecimento)
                .Select(y => y.First())
                .Take(quantidadeLimite);

            return todosOsEstabelecimentos.ToList();
        }

        public int ClientesEstabelecimentosWebBalcaoTotal()
        {
            var dados = Queryable().Where(f => f.Ativo);
            return dados.Count(g => g.Cliente.TipoCliente == TipoClienteEnum.Balcao);
        }

        public IList<ClientesEstabelecimentosCountDTO> ClientesEstabelecimentosWebBalcaoCadastradosNoDia(
            DateTime dataCadastro)
        {
            var dados =
                Queryable()
                    .Where(
                        c =>
                            c.Ativo && c.Cliente.PessoaFisica.DataCadastro.HasValue &&
                            c.Cliente.PessoaFisica.DataCadastro.Value.Date == dataCadastro.Date);
            return dados.Select(g => new ClientesEstabelecimentosCountDTO
            {
                Sexo = g.Cliente.PessoaFisica.Genero,
                Tipo = g.Cliente.TipoCliente,
                Count = 1
            }).ToList();
        }

        #endregion Trinks em Números

        #region Critérios

        public IQueryable<ClienteEstabelecimento> ObterClientesComFiltro(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool filtrarPorTelefone,
             out bool possuieFiltro, bool comFetchParaUsoSemPaginacao = false)
        {
            // Se usar stateless queryable (duplica) devido a bug no NH ainda não corrigido.
            // https://github.com/nhibernate/nhibernate-core/pull/111
            var query = Queryable();
            possuieFiltro = false;
            // [JIRA-] Problema de desempenho (N+1 para Pessoa_Fisica, Telefones e Contas) na exportação
            if (comFetchParaUsoSemPaginacao)
            {
                query = query.Fetch(p => p.Cliente).ThenFetch(p => p.PessoaFisica).ThenFetchMany(p => p.Telefones);
                query = query.Fetch(p => p.Cliente).ThenFetch(p => p.PessoaFisica).ThenFetchMany(p => p.Contas);
            }

            query = query.Where(p => p.Estabelecimento.IdEstabelecimento == parametros.CodigoEstabelecimento);

            bool filtrarSomenteAtivos = parametros.FiltrarApenasAtivos.HasValue && parametros.FiltrarApenasAtivos.Value;
            if (filtrarSomenteAtivos)
                query = AdicionarCriterioRelacionamentoEPessoasAtivas(query);
            else
                query = query.Where(ce => ce.Cliente.PessoaFisica.IdPessoaUnificacao == null);

            if (!string.IsNullOrEmpty(parametros.ConteudoFiltro))
            {
                switch (parametros.FiltroPor)
                {
                    case ParametrosFiltroCliente.FiltroPorEnum.Nome:
                        query = AdicionarCriterioNome(parametros.ConteudoFiltro, query, parametros.CodigoEstabelecimento, filtrarSomenteAtivos);
                        break;

                    case ParametrosFiltroCliente.FiltroPorEnum.CPF:
                        if (filtraPorCPF)
                            query = AdicionarCriterioCpf(parametros.ConteudoFiltro, query);
                        break;

                    case ParametrosFiltroCliente.FiltroPorEnum.Telefone:
                        if (filtrarPorTelefone)
                            query = AdicionarCriterioTelefone(parametros.CodigoEstabelecimento, parametros.ConteudoFiltro, query);
                        break;

                    case ParametrosFiltroCliente.FiltroPorEnum.Email:
                        if (filtraPorEmail)
                            query = AdicionarCiterioExisteEmailContendo(query, parametros.ConteudoFiltro);
                        break;

                    case ParametrosFiltroCliente.FiltroPorEnum.NomeTelefoneOuEmail:
                        query = AdicionarCiterioNomeTelefoneOuEmail(query, parametros.ConteudoFiltro, parametros.CodigoEstabelecimento, filtraPorEmail, filtrarPorTelefone);
                        break;
                }
                possuieFiltro = true;
            }

            if (parametros.MesAniversario > 0)
            {
                query =
                    query.Where(
                        p =>
                            (p.DataNascimentoCliente != null &&
                             p.DataNascimentoCliente.Value.Month == parametros.MesAniversario) ||
                            (p.Cliente.PessoaFisica.DataNascimento != null &&
                             p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.MesAniversario));
                possuieFiltro = true;
            }

            if (parametros.DataInicioAniversariante != null && parametros.DataFimAniversariante != null)
            {
                query = ObterClientePorFiltroDePeriodoDeAniversariante(query, parametros);
            }

            if (parametros.ClientePodeAgendarNoEstabelecimento > 0)
            {
                query = query.Where(p => p.PodeAgendarOnlineNoEstabelecimento == (parametros.ClientePodeAgendarNoEstabelecimento == 1));
                possuieFiltro = true;
            }

            if (parametros.OrigemCliente == 3)
            {
                query = query.Where(p => p.Cliente.PessoaFisica.IdGoogleReserveUser != null);
                possuieFiltro = true;
            }
            else if (parametros.OrigemCliente > 0)
            {
                query = query.Where(p => p.Cliente.TipoCliente == (TipoClienteEnum)parametros.OrigemCliente);
                possuieFiltro = true;
            }

            if (parametros.ClientesQueRecebemSMS.HasValue)
            {
                query = parametros.ClientesQueRecebemSMS.Value
                    ? query.Where(f => f.RecebeNotificacao == true || f.RecebeNotificacao == null)
                    : query.Where(f => f.RecebeNotificacao != null && f.RecebeNotificacao == false);
                possuieFiltro = true;
            }

            if (parametros.EnviarEmailAgendamentoCliente.HasValue)
            {
                query = parametros.EnviarEmailAgendamentoCliente.Value
                    ? query.Where(f => f.EnviarEmailAgendamentoCliente)
                    : query.Where(
                        f => !f.EnviarEmailAgendamentoCliente);
                possuieFiltro = true;
            }

            if (parametros.FiltroDataInicioCadastroCliente.HasValue)
            {
                query = query.Where(f => f.DataCadastro.Value >= parametros.FiltroDataInicioCadastroCliente);
                possuieFiltro = true;
            }

            if (parametros.EtiquetasSelecionadas.Count() > 0)
            {
                var idsClienteEstabelecimentoEtiquetados = Domain.Marcadores.ObjetoEtiquetadoRepository.Queryable()
                        .Where(e => e.Etiqueta.IdDono == parametros.CodigoEstabelecimento &&
                                  e.Etiqueta.Tipo == Marcadores.Enums.TipoDeEtiquetaEnum.ClienteEstabelecimento &&
                                  e.Etiqueta.Ativo &&
                                  parametros.EtiquetasSelecionadas.Contains(e.Etiqueta.IdEtiqueta))
                        .Select(o => o.IdObjetoEtiquetado);

                query = query.Where(f => idsClienteEstabelecimentoEtiquetados.Any(c => c == f.Codigo));
                possuieFiltro = true;
            }

            if (parametros.FiltroDataFimCadastroCliente.HasValue)
                possuieFiltro = true;

            if (parametros.FiltroPersonalizado != null)
            {
                parametros.FiltroPersonalizado.IdEstabelecimento = parametros.CodigoEstabelecimento;
                query = Domain.Marketing.MarketingCampanhaRepository.AplicarFiltroPersonalizadoDeClientes(query, parametros.FiltroPersonalizado);
            }

            query = AdicionarCriterioPeriodoDeCadastroClienteEstabelecimento(parametros.FiltroDataInicioCadastroCliente, parametros.FiltroDataFimCadastroCliente, query);

            query = query.OrderBy(p => p.Cliente.PessoaFisica.NomeCompleto);
            return query;
        }

        public IQueryable<ClienteEstabelecimento> ObterClientePorFiltroDePeriodoDeAniversariante(IQueryable<ClienteEstabelecimento> query, ParametrosFiltroCliente parametros)
        {
            if (parametros.DataInicioAniversariante.Value.Month < parametros.DataFimAniversariante.Value.Month)
            {
                query =
                    query.Where(p => p.Cliente.PessoaFisica.DataNascimento != null &&
                    p.Cliente.PessoaFisica.DataNascimento.Value.Day >= parametros.DataInicioAniversariante.Value.Day && p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.DataInicioAniversariante.Value.Month ||
                    p.Cliente.PessoaFisica.DataNascimento.Value.Month > parametros.DataInicioAniversariante.Value.Month && p.Cliente.PessoaFisica.DataNascimento.Value.Month < parametros.DataFimAniversariante.Value.Month ||
                    p.Cliente.PessoaFisica.DataNascimento.Value.Day <= parametros.DataFimAniversariante.Value.Day && p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.DataFimAniversariante.Value.Month);
            }
            else if (parametros.DataInicioAniversariante.Value.Month > parametros.DataFimAniversariante.Value.Month)
            {
                query =
                    query.Where(p => p.Cliente.PessoaFisica.DataNascimento != null &&
                    p.Cliente.PessoaFisica.DataNascimento.Value.Day >= parametros.DataInicioAniversariante.Value.Day && p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.DataInicioAniversariante.Value.Month ||
                    p.Cliente.PessoaFisica.DataNascimento.Value.Month > parametros.DataInicioAniversariante.Value.Month || p.Cliente.PessoaFisica.DataNascimento.Value.Month < parametros.DataFimAniversariante.Value.Month ||
                    p.Cliente.PessoaFisica.DataNascimento.Value.Day <= parametros.DataFimAniversariante.Value.Day && p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.DataFimAniversariante.Value.Month);
            }
            else
            {
                query =
                     query.Where(p => p.Cliente.PessoaFisica.DataNascimento != null &&
                     (p.Cliente.PessoaFisica.DataNascimento.Value.Day >= parametros.DataInicioAniversariante.Value.Day && p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.DataInicioAniversariante.Value.Month) &&
                     (p.Cliente.PessoaFisica.DataNascimento.Value.Day <= parametros.DataFimAniversariante.Value.Day && p.Cliente.PessoaFisica.DataNascimento.Value.Month == parametros.DataFimAniversariante.Value.Month));
            }
            return query;
        }

        public IQueryable<ClienteEstabelecimento> ObterClientesComFiltro(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool filtrarPorTelefone, bool comFetchParaUsoSemPaginacao = true)
        {
            bool possuieFiltro = true;
            return ObterClientesComFiltro(parametros, filtraPorCPF, filtraPorEmail, filtrarPorTelefone, out possuieFiltro, comFetchParaUsoSemPaginacao);
        }

        public IList<EstatisticaCliente> ObterEstatisticasDeTodosOsClientesEmUmEstabelecimento(long idEstabelecimento)
        {
            // Utilizada query e AliasToBean para otimizar o desempenho para geração de arquivo CSV.
            // Verificar impacto de desempenho antes de modificar.
            var query = @"
							SELECT
								u.id_estabelecimento_cliente AS IdClienteEstabelecimento,
								ph.id_horario_status AS PrimeiroAgendamentoStatus,
								ph.data_hora_inicio AS PrimeiroAgendamentoData,
								uh.id_horario_status AS UltimoAgendamentoStatus,
								uh.data_hora_inicio AS UltimoAgendamentoData

							FROM
								(SELECT
									id_estabelecimento_cliente,
									MAX(id_horario) AS id_horario
								FROM
									Horario
								WHERE
									id_estabelecimento = :key
								GROUP BY
									id_estabelecimento_cliente) AS u INNER JOIN

								Horario AS uh ON u.id_horario = uh.id_horario INNER JOIN

								(SELECT
									id_estabelecimento_cliente,
									MIN(id_horario) AS id_horario
								FROM
									Horario
								WHERE
									id_estabelecimento = :key
								GROUP BY
									id_estabelecimento_cliente) AS p INNER JOIN

								Horario AS ph ON p.id_horario = ph.id_horario ON u.id_estabelecimento_cliente = p.id_estabelecimento_cliente
							";

            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session1 = holder.CreateSession(typeof(ActiveRecordBase));

            var q =
                session1.CreateSQLQuery(query)
                    .SetParameter("key", idEstabelecimento)
                    .SetResultTransformer(Transformers.AliasToBean(typeof(EstatisticaCliente)));

            var result = q.List<EstatisticaCliente>();
            holder.ReleaseSession(session1);

            return result;
        }

        private static IQueryable<ClienteEstabelecimento> AdicionarCriterioCpf(String cpf,
            IQueryable<ClienteEstabelecimento> query)
        {
            return query.Where(p => p.Cliente.PessoaFisica.Cpf == cpf.Replace("-", "").Replace(".", "").Trim());
        }

        private static IQueryable<ClienteEstabelecimento> AdicionarCriterioNome(String nome,
            IQueryable<ClienteEstabelecimento> query, int idEstabelecimento, bool somenteAtivos = true)
        {
            //var regexCaracteresNaoEspeciais = new Regex("^[a-zA-Z0-9 ]*$");
            //var stringSemAcentuacao = nome.RemoverAcentos();

            //if (!regexCaracteresNaoEspeciais.IsMatch(stringSemAcentuacao)) {
            //    return
            //          query.Where(p => p.Cliente.PessoaFisica.NomeCompleto.Contains(nome));
            //}
            //else {
            //    return
            //        query.Where(
            //            p => p.Cliente.PessoaFisica.NomeCompleto.ContainsLike(String.Format("\"*{0}*\"", nome)));
            //}

            var habilitarMelhoriaPesquisaClientes = new ParametrosTrinks<bool>(ParametrosTrinksEnum.habilitar_melhoria_pesquisa_clientes).ObterValor();

            var nomeCliente = habilitarMelhoriaPesquisaClientes
                ? Regex.Replace(nome, " ", "%", RegexOptions.IgnoreCase)
                : nome;

            if (somenteAtivos)
            {
                var itensCombo = Domain.Pessoas.ItemComboClienteRepository.Queryable();
                return from ce in query
                       join i in itensCombo on ce.Codigo equals i.IdClienteEstabelecimento
                       where i.Nome.Contains(nomeCliente) && i.IdEstabelecimento == idEstabelecimento
                       select ce;
            }
            else
            {
                return query.Where(p => p.Cliente.PessoaFisica.NomeCompleto.Contains(nomeCliente));
            }
        }

        private static void AdicionarFetchEmClienteEEstabelecimento(DetachedCriteria dc)
        {
            dc.SetFetchMode(PropertyNames.Pessoas.ClienteEstabelecimento.Cliente, FetchMode.Join);
            dc.SetFetchMode(PropertyNames.Pessoas.ClienteEstabelecimento.Estabelecimento, FetchMode.Join);
        }

        private IQueryable<ClienteEstabelecimento> AdicionarCiterioExisteEmailContendo(
            IQueryable<ClienteEstabelecimento> query,
            String email)
        {
            email = email.Trim().ToLower();

            return
                query.Where(
                    p =>
                        p.Cliente.PessoaFisica.Email == email ||
                        p.Cliente.PessoaFisica.Contas.Any(
                            c =>
                                c.Email == email &&
                                c.Pessoa.IdPessoa == p.Cliente.PessoaFisica.IdPessoa) ||
                        p.EmailAlternativo == email);
        }

        private IQueryable<ClienteEstabelecimento> AdicionarCiterioNomeTelefoneOuEmail(
            IQueryable<ClienteEstabelecimento> query, string conteudo, int idEstabelecimento, bool filtraPorEmail, bool filtrarPorTelefone)
        {
            if (!conteudo.Contains('@') && !conteudo.Contains('.'))
            {
                if (filtrarPorTelefone && conteudo.SomenteNumeros().Any())
                {
                    var idsClienteEstabelecimento = Domain.Pessoas.ItemComboClienteRepository
                        .ObterIdsClientesEstabelecimentoPorFiltroTelefone(idEstabelecimento, conteudo);

                    query = query.Where(f => idsClienteEstabelecimento.Contains(f.Codigo));
                }
                else
                    query = query.Where(p => p.Cliente.PessoaFisica.NomeCompleto.Contains(conteudo));
            }

            if (filtraPorEmail)
                query = query.Where(p => p.Cliente.PessoaFisica.Email == conteudo.ToLower());

            return query;
        }

        //private void AdicionarCriterioEmailCliente(DetachedCriteria dc, String email) {
        //    dc.Add(Restrictions.Eq(PropertyNames.Pessoas.ClienteEstabelecimento.EmailCliente, email));
        //}

        private void AdicionarCriterioEstabelecimento(DetachedCriteria dc, Int32 codigoEstabelecimento)
        {
            var propertyNameEstabelecimento = String.Format("{0}.{1}", typeof(Estabelecimento).Name,
                PropertyNames.Pessoas.Estabelecimento.IdEstabelecimento);
            dc.Add(Restrictions.Eq(propertyNameEstabelecimento, codigoEstabelecimento));
        }

        private void AdicionarCriterioIdCliente(DetachedCriteria dc, Int32 idCliente)
        {
            var propertyNameIdCliente = String.Format("{0}.{1}", typeof(Cliente).Name,
                PropertyNames.Pessoas.Cliente.IdCliente);
            dc.Add(Restrictions.Eq(propertyNameIdCliente, idCliente));
        }

        private IQueryable<ClienteEstabelecimento> AdicionarCriterioRelacionamentoEPessoasAtivas(
            IQueryable<ClienteEstabelecimento> query)
        {
            return query.Where(p => p.Cliente.PessoaFisica.Ativo && p.Ativo);
        }

        private IQueryable<ClienteEstabelecimento> AdicionarCriterioPeriodoDeCadastroClienteEstabelecimento(DateTime? dataInicial, DateTime? dataFim,
            IQueryable<ClienteEstabelecimento> query)
        {
            if (dataInicial.HasValue)
                query = FiltroDataDeCadastroInicial(dataInicial.Value, query);

            if (dataFim.HasValue)
                query = FiltroDataDeCadastroFim(dataFim.Value, query);

            return query;
        }

        private IQueryable<ClienteEstabelecimento> FiltroDataDeCadastroInicial(DateTime dataInicio, IQueryable<ClienteEstabelecimento> query)
        {
            return query.Where(f => f.DataCadastro >= dataInicio);
        }

        private IQueryable<ClienteEstabelecimento> FiltroDataDeCadastroFim(DateTime dataFim, IQueryable<ClienteEstabelecimento> query)
        {
            return query.Where(f => f.DataCadastro < dataFim.AddDays(1));
        }

        private IQueryable<ClienteEstabelecimento> AdicionarCriterioTelefone(Int32 idEstabelecimento, String telefone,
            IQueryable<ClienteEstabelecimento> query)
        {
            var idsClientesEstabelecimento = Domain.Pessoas.ItemComboClienteRepository
                .ObterIdsClientesEstabelecimentoPorFiltroTelefone(idEstabelecimento, telefone);

            return query.Where(f => idsClientesEstabelecimento.Contains(f.Codigo));
        }

        #endregion Critérios

        public IQueryable<ClienteEstabelecimento> ObterPendentesDeNotificacaoDeAgendePeloTrinks(DateTime? data = null)
        {
            data = data ?? Calendario.Hoje();

            int quantosDiasSemReceberANotificacao = new ParametrosTrinks<int>(ParametrosTrinksEnum.quantos_dias_sem_receber_notificacao_agende_pelo_trinks).ObterValor();

            var notificacoesConsolidadasNosUltimosXDias = Domain.Notificacoes.NotificacaoDoTrinksRepository.Queryable().Where(n => n.Origem == EnvioMensagem.Enums.OrigemMensagemEnum.AgendePeloTrinks && n.DataRegistro > Calendario.Hoje().AddDays(-quantosDiasSemReceberANotificacao));

            var query =
                Domain.Pessoas.HorarioRepository.Queryable()
                    .Where(h =>
                    h.DataInicio >= data.Value.Date
                    && h.DataInicio < data.Value.Date.AddDays(1)
                    && h.Status != StatusHorarioEnum.Cancelado
                    && h.Status != StatusHorarioEnum.Cliente_Faltou
                    && h.ClienteEstabelecimento.Cliente.TipoCliente == TipoClienteEnum.Balcao
                    && h.ClienteEstabelecimento.RecebeSMSMarketing);

            // Filtro
            var ufsFiltrados = new ParametrosTrinks<List<string>>(ParametrosTrinksEnum.ufs_para_sms_agende_pelo_trinks).ObterValor() ?? new List<string>();
            var idsEstabelecimentoIncluidos = new ParametrosTrinks<List<int>>(ParametrosTrinksEnum.id_estabelecimento_teste_para_sms_agende_pelo_trinks).ObterValor() ?? new List<int>();

            query = query.Where(f => idsEstabelecimentoIncluidos.Contains(f.Estabelecimento.IdEstabelecimento)
                || f.Estabelecimento.PessoaJuridica.Enderecos.Any(g => ufsFiltrados.Contains(g.UF.Sigla)));

            // Excluir
            var idsEstabelecimentoExcluidos = new ParametrosTrinks<List<int>>(ParametrosTrinksEnum.id_estabelecimento_excluido_para_sms_agende_pelo_trinks).ObterValor();
            if (idsEstabelecimentoExcluidos != null && idsEstabelecimentoExcluidos.Any())
            {
                query = query.Where(f => !idsEstabelecimentoExcluidos.Contains(f.Estabelecimento.IdEstabelecimento));
            }

            var idsFranquiasExcluidas = new ParametrosTrinks<List<int>>(ParametrosTrinksEnum.id_franquia_excluida_para_sms_agende_pelo_trinks).ObterValor();
            if (idsFranquiasExcluidas != null && idsFranquiasExcluidas.Any())
            {
                query = query.Where(f => f.Estabelecimento.FranquiaEstabelecimento.Franquia == null || !idsFranquiasExcluidas.Contains(f.Estabelecimento.FranquiaEstabelecimento.Franquia.Id));
            }

            query = query.Where(h => h.Estabelecimento.EstabelecimentoConfiguracaoGeral.PermiteEnviarNotificacaoDeAgendePeloTrinks)//que permita o sms
                .Where(h => h.Estabelecimento.HotsiteEstabelecimentoLista.Any(hs => hs.PermiteAgendamentoHotsite));//que permita agendamento online;

            //Que esteja aparecendo no portal
            query = query.Where(h => h.Estabelecimento.PessoaJuridica.Ativo)
                    .Where(h => h.Estabelecimento.PesoBuscaPortal > 0 && h.Estabelecimento.PesoBuscaPortal <= 30 && h.Estabelecimento.HotsiteEstabelecimentoLista.Any())
                    .Where(h => h.Estabelecimento.PessoaJuridica.Enderecos.Any(g => g.Ativo))
                    .Where(h => h.Estabelecimento.HotsiteEstabelecimentoLista.Any(g => g.DesejaAparecerBuscaPortal && g.DesejaTerHotsite));

            var clientesEstabelecimento = query.Select(f => f.ClienteEstabelecimento)
                .Where(ce => !notificacoesConsolidadasNosUltimosXDias.Any(n => n.ClienteEstabelecimento == ce));

            return clientesEstabelecimento;
        }

        public string ObterObservacaoSobreOCliente(int idPessoa)
        {
            return Queryable().Where(x => x.Cliente.PessoaFisica.IdPessoa == idPessoa).Select(y => y.Observacoes).FirstOrDefault();
        }

        public DadosDoClienteEstabelecimentoParaLembreteSmsDTO ObterDadosDoClienteEstabelecimentoParaLembreteSmsDto(int idCliente, int idEstabelecimento)
        {
            return Queryable()
                .Where(p => p.Cliente.IdCliente == idCliente && p.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(p => new DadosDoClienteEstabelecimentoParaLembreteSmsDTO
                {
                    IdClienteEstabelecimento = p.Codigo,
                    SaldoDePontosDeFidelidade = p.SaldoDePontosDeFidelidade,
                    TipoCliente = p.Cliente.TipoCliente,
                    NomeCompleto = p.Cliente.PessoaFisica.NomeCompleto
                })
                .FirstOrDefault();
        }

        public int TotalDeClientesDoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(x => x.Estabelecimento.IdEstabelecimento == idEstabelecimento).Count();
        }

        public int ObterIdClienteEstabelecimento(int idEstabelecimento, int idCliente)
        {
            int? id = Queryable()
                .Where(c => c.Estabelecimento.IdEstabelecimento == idEstabelecimento && c.Cliente.IdCliente == idCliente)
                .Select(c => c.Codigo)
                .FirstOrDefault();
            return id ?? 0;
        }

        public int? ObterIdClienteEstabelecimentoPeloIdPessoa(int idPessoa, int idEstabelecimento)
        {
            var codigo = Queryable()
                .Where(c => c.Cliente.PessoaFisica.IdPessoa == idPessoa && c.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(c => c.Codigo)
                .FirstOrDefault();

            return codigo;
        }

        public int ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(int idFranquia, string cpf, string email)
        {

            var mesmoClienteNaRede = ObterQueryComBuscaPeloMesmoClienteNaRede(idFranquia, cpf, email);

            var totalPontosNaRede = mesmoClienteNaRede
                .Where(ce => ce.Estabelecimento.FranquiaEstabelecimento.HabilitaTransferenciaDePontosDeFidelidade)
                .Sum(ce => (int?)ce.SaldoDePontosDeFidelidade);

            return totalPontosNaRede ?? 0;
        }

        public IQueryable<ClienteEstabelecimento> ObterQueryComBuscaPeloMesmoClienteNaRede(int idFranquia, string cpf, string email)
        {
            var query = Queryable().Where(ce => ce.Estabelecimento.FranquiaEstabelecimento.Franquia.Id == idFranquia && ce.Ativo);

            var possuiEmail = !string.IsNullOrWhiteSpace(email);
            var possuiCpf = !string.IsNullOrWhiteSpace(cpf);

            if (possuiCpf && possuiEmail)
            {
                query = query.Where(ce => ce.Cliente.PessoaFisica.Cpf == cpf.SomenteNumeros() || ce.Cliente.PessoaFisica.Email == email);
            }
            else if (possuiCpf)
            {
                query = query.Where(ce => ce.Cliente.PessoaFisica.Cpf == cpf.SomenteNumeros());
            }
            else if (possuiEmail)
            {
                query = query.Where(ce => ce.Cliente.PessoaFisica.Email == email);
            }
            else
            {
                throw new NotImplementedException("É necessário ter CPF ou E-mail para buscar o cliente na rede.");
            }

            return query;
        }

        public IList<ClienteEstabelecimento> ObterListaDeClienteAtivoNoEstabelecimentoPorPessoa(int idPessoa)
        {
            var ce = Queryable();
            return ce.Where(cliEstab => cliEstab.Cliente.PessoaFisica.IdPessoa == idPessoa && cliEstab.Ativo).ToList();
        }

        public bool ClienteRecebeSmsMarketing(int idPessoaDoCliente, int idEstabelecimento)
        {
            bool? recebeSmsMarketing = Queryable()
                .Where(ce => ce.Cliente.PessoaFisica.IdPessoa == idPessoaDoCliente
                          && ce.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(ce => ce.RecebeSMSMarketing)
                .FirstOrDefault();

            return recebeSmsMarketing ?? false;
        }

        public IQueryable<ClientesDaRedeDTO> ListarBuscaCPFNaRede(int idFranquia, string cpf, bool soEstabelecimentoQueCompartilhaPacote = false)
        {
            if (string.IsNullOrWhiteSpace(cpf) && !cpf.ValidarCPF())
                return null;
            IQueryable<ClientesDaRedeDTO> comboCliente = ListarBuscaNaRede(idFranquia, soEstabelecimentoQueCompartilhaPacote);

            if (!string.IsNullOrWhiteSpace(cpf))
                cpf = cpf.SomenteNumeros();

            return comboCliente.Where(f => f.Cpf == cpf);
        }


        public IQueryable<ClientesDaRedeDTO> ListarBuscaEmailNaRede(int idFranquia, string email, bool soEstabelecimentoQueCompartilhaPacote = false)
        {
            if (string.IsNullOrWhiteSpace(email) && !email.EmailValido())
                return null;
            IQueryable<ClientesDaRedeDTO> comboCliente = ListarBuscaNaRede(idFranquia, soEstabelecimentoQueCompartilhaPacote);

            return comboCliente.Where(f => f.Email == email);
        }

        private static IQueryable<ClientesDaRedeDTO> ListarBuscaNaRede(int idFranquia, bool soEstabelecimentoQueCompartilhaPacote)
        {
            var queryFranquiaEstabelecimento1 = Domain.Pessoas.FranquiaEstabelecimentoRepository.Queryable()
                            .Where(fe => fe.Franquia.Id == idFranquia && fe.Ativo);

            if (soEstabelecimentoQueCompartilhaPacote)
                queryFranquiaEstabelecimento1 = queryFranquiaEstabelecimento1.Where(fe => fe.TipoCompartilhamentoNaRede != TipoCompartilhaNaRedeEnum.NenhumaUnidade);

            var idEstabelecimentos = queryFranquiaEstabelecimento1.Select(fe => fe.IdEstabelecimento).ToList();

            var queryFranquiaEstabelecimento = Domain.Pessoas.FranquiaEstabelecimentoRepository.Queryable();
            var queryPessoaFisica = Domain.Pessoas.PessoaFisicaRepository.Queryable();

            var comboCliente = from cc in Domain.Pessoas.ItemComboClienteRepository.Queryable()
                               join fe in queryFranquiaEstabelecimento on cc.IdEstabelecimento equals fe.IdEstabelecimento
                               join pf in queryPessoaFisica on cc.IdPessoa equals pf.IdPessoa
                               where idEstabelecimentos.Contains(cc.IdEstabelecimento)
                               select new ClientesDaRedeDTO
                               {
                                   IdClienteEstabelecimento = cc.IdClienteEstabelecimento,
                                   NomeUnidade = fe.NomeUnidade,
                                   Nome = cc.Nome,
                                   Email = cc.Email,
                                   Telefones = cc.Telefones,
                                   Cpf = pf.Cpf,
                                   DataNascimento = pf.DataNascimento.HasValue ? pf.DataNascimento.Value.ToDataNascimentoString() : String.Empty,
                                   Sexo = pf.Genero.Codigo(),
                                   IdEstabelecimento = cc.IdEstabelecimento,
                                   IdCliente = cc.IdCliente
                               };
            return comboCliente;
        }

        public class EstatisticaCliente
        {
            public long IdClienteEstabelecimento { get; set; }

            public DateTime? PrimeiroAgendamentoData { get; set; }

            public StatusHorarioEnum PrimeiroAgendamentoStatus { get; set; }

            public DateTime? UltimoAgendamentoData { get; set; }

            public StatusHorarioEnum UltimoAgendamentoStatus { get; set; }
        }

        public KeyValuePair<int, string> ObterKeyValueClienteEstabelecimento(int idEstabelecimento, int idCliente)
        {
            return Queryable()
                    .Where(e => e.Estabelecimento.IdEstabelecimento == idEstabelecimento
                            && e.Cliente.IdCliente == idCliente)
                    .Select(a => new KeyValuePair<int, string>(a.Codigo, a.Cliente.PessoaFisica.NomeCompleto))
                    .FirstOrDefault();
        }

        public ClienteEstabelecimento ObterPorId(int idClienteEstabelecimento, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(ce => ce.Codigo == idClienteEstabelecimento && ce.Estabelecimento.IdEstabelecimento == idEstabelecimento);
        }

        public int ObterIdPessoaDoCliente(int idClienteEstabelecimento)
        {
            return Queryable()
                .Where(ce => ce.Codigo == idClienteEstabelecimento)
                .Select(ce => ce.Cliente.PessoaFisica.IdPessoa)
                .FirstOrDefault();
        }

        public PessoaFisica ObterPessoaFisicaDoCliente(int idClienteEstabelecimento)
        {
            return Queryable()
                .Where(ce => ce.Codigo == idClienteEstabelecimento)
                .Select(ce => ce.Cliente.PessoaFisica)
                .FirstOrDefault();
        }

        public List<ClienteEstabelecimento> ObterClienteEstabelecimentoPelaPessoa(List<int> idsPessoas, int idEstabelecimento)
        {
            return Queryable().Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && idsPessoas.Contains(f.Cliente.PessoaFisica.IdPessoa)).ToList();

        }

        public ClienteEstabelecimento ObterClienteEstabelecimentoPorIdPessoaEIdEstabelecimento(int idPessoaFisicaCliente, int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento && f.Cliente.PessoaFisica.IdPessoa == idPessoaFisicaCliente);

        }

        public List<int> IdsClientesEstabelecimentosNovos(int idEstabelecimento, DateTime data)
        {
            var IdsClientesEstabelecimentosNovosNoPeriodo = Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento
                            && f.PrimeiroAgendamento != null
                            && f.PrimeiroAgendamento.Estabelecimento.IdEstabelecimento == idEstabelecimento
                            && f.PrimeiroAgendamento.DataInicio >= data.Date
                            && f.PrimeiroAgendamento.DataInicio < data.Date.AddDays(1))
                .Select(f => f.Codigo).ToList();

            var IdsClientesEstabelecimentosNovosQueNaoTiveramPrimeiroAgendamento = Queryable()
                .Where(f => f.Estabelecimento.IdEstabelecimento == idEstabelecimento
                            && f.PrimeiroAgendamento == null)
                .Select(f => f.Codigo).ToList();

            return IdsClientesEstabelecimentosNovosNoPeriodo.Concat(IdsClientesEstabelecimentosNovosQueNaoTiveramPrimeiroAgendamento).Distinct().ToList();
        }

        public List<ClienteEstabelecimento> ObterClienteEstabelecimentoPeloId(List<int> listaIds)
        {
            var idsClientesEstabelecimento = Queryable().Where(f => listaIds.Contains(f.Codigo)).ToList();
            return idsClientesEstabelecimento;
        }
        public IList<ClienteEstabelecimento> ObterClienteEstabelecimentosPorTelefone(string ddd, string numero, int idEstabelecimento)
        {
            return Queryable()
                .Where(f =>
                    f.Ativo &&
                    f.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                    f.Cliente.PessoaFisica.Telefones
                .Any(t => t.DDD == ddd && t.Numero == numero && t.Ativo))
                .ToList();

        }
    }
}