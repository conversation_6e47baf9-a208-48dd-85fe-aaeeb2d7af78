﻿using NHibernate.Criterion;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class HorarioTransacaoRepository : IHorarioTransacaoRepository
    {

        private const String AliasTabelaCliente =
            PropertyNames.Pessoas.HorarioTransacao.Horario + "." + PropertyNames.Pessoas.Horario.Cliente;

        private const String AliasTabelaEstabelecimento =
            PropertyNames.Pessoas.HorarioTransacao.Horario + "." + PropertyNames.Pessoas.Horario.Estabelecimento;

        private const String AliasTabelaHorario = PropertyNames.Pessoas.HorarioTransacao.Horario;
        private const String AliasTabelaTipoTransacao = PropertyNames.Financeiro.Transacao.TipoTransacao;
        private const String AliasTabelaTransacao = PropertyNames.Pessoas.HorarioTransacao.Transacao;

        #region Métodos Públicos

        public Boolean ExisteTransacoesParaOCliente(Int32 idPessoa)
        {
            return Queryable().Any(p => p.Horario.Cliente.PessoaFisica.IdPessoa == idPessoa);
        }

        public Boolean ExisteTransacoesParaOHorario(Int32 codigoHorario)
        {
            var dc = DetachedCriteria.For<HorarioTransacao>();
            AdicionarCriterioHorario(codigoHorario, dc);
            return Exists(dc);
        }

        public IQueryable<HorarioTransacao> ListarComissaoServicoAssistente(IQueryable<ValorDeComissaoAReceber> valores)
        {
            return from i in valores
                   join j in Queryable() on i.Comissao equals j.ComissaoAssistente
                   select j;
        }

        public IQueryable<HorarioTransacao> ListarComissaoServicoAssistente(ParametrosFiltrosRelatorio filtro,
                    bool somenteNaoFechados)
        {
            var valores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, somenteNaoFechados);

            return ListarComissaoServicoAssistente(valores);
        }

        public List<FechamentoMesComissaoDeServicoDTO> ListarComissaoServicoAssistenteDTO(IQueryable<ValorDeComissaoAReceber> valores)
        {
            var dados = (from a in valores
                         join b in Queryable() on a.Comissao equals b.ComissaoAssistente
                         select new
                         {
                             IdPessoaComissionada = a.Comissao.PessoaComissionada.IdPessoa,
                             Nome = b.Horario.ServicoEstabelecimento.Nome,
                             a.Valor,
                             ValorServico = a.Comissao.ValorBase
                         }).ToList();

            var agrupados = dados.GroupBy(c => new { c.IdPessoaComissionada, c.Nome });

            var retorno = agrupados.Select(d => new FechamentoMesComissaoDeServicoDTO
            {
                Comissao = d.Sum(h => h.Valor),
                Nome = d.Key.Nome,
                Quantidade = d.Count(),
                ValorPago = d.Sum(h => h.ValorServico),
                IdPessoaComissionada = d.Key.IdPessoaComissionada
            });

            return retorno.ToList();
        }

        public List<FechamentoMesComissaoDeServicoDTO> ListarComissaoServicoProfissionalDTO(IQueryable<ValorDeComissaoAReceber> valores)
        {
            var dados = (from a in valores
                         join b in Queryable() on a.Comissao equals b.Comissao
                         select new
                         {
                             IdPessoaComissionada = b.Comissao.PessoaComissionada.IdPessoa,
                             Nome = b.Horario.ServicoEstabelecimento.Nome,
                             a.Valor,
                             ValorServico = a.Comissao.ValorBase
                         }).ToList();

            var agrupados = dados.GroupBy(c => new { c.IdPessoaComissionada, c.Nome });

            var retorno = agrupados.Select(d => new FechamentoMesComissaoDeServicoDTO
            {
                Comissao = d.Sum(h => h.Valor),
                Nome = d.Key.Nome,
                Quantidade = d.Count(),
                ValorPago = d.Sum(h => h.ValorServico),
                IdPessoaComissionada = d.Key.IdPessoaComissionada
            });

            return retorno.ToList();
        }

        public List<FechamentoMesComissaoDeServicoDTO> ListarComissaoServicoProfissionalDTOParaFechamento(ParametrosFiltrosRelatorio filtro)
        {
            var valores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, true);
            return ListarComissaoServicoProfissionalDTO(valores);
        }

        public IEnumerable<FechamentoMesComissaoDeServicoDTO> ObterComissoes(ParametrosFiltrosRelatorio filtro, bool stateless = false)
        {
            var estabelecimentoProfissional =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(filtro.IdEstabelecimentoProfissional);

            if (filtro.TipoData == TipoDataRelatorio.DataDaComissao)
                return ObterComissoesPorDataDaComissao(filtro, stateless, estabelecimentoProfissional);
            return ObterComissoesPorDataPagamento(filtro, stateless, estabelecimentoProfissional);
        }

        public List<FechamentoMesComissaoDeServicoDTO> ListarComissaoServicoAssistenteDTO(ParametrosFiltrosRelatorio filtro)
        {
            var valores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, true);
            return ListarComissaoServicoAssistenteDTO(valores);
        }

        public IQueryable<HorarioTransacao> ObterParaFechamentoComissaoProfissional(ParametrosFiltrosRelatorio filtro,
            bool somenteNaoFechados, bool stateless = false)
        {
            var buscarDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, somenteNaoFechados);

            var hts = from g in buscarDeValores
                      join h in Queryable() on g.Comissao equals h.Comissao
                      select h;

            return hts;
        }

        public List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatas(
            ParametrosFiltrosRelatorio parametros)
        {
            var dados = Listar(parametros);
            var idPessoa = Domain.Pessoas.ProfissionalRepository.Load(parametros.IdProfissional).PessoaFisica.IdPessoa;

            var retorno = (from f in dados
                           select new RelatorioHorarioTransacao
                           {
                               DataHoraInicioHorario = f.DataHoraInicioHorario,
                               DataHoraTransacao = f.Transacao.DataHora,
                               DescontarDescartaveisDoValorPago =
                                   f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                               ConsiderarDescontoOperadoraNaComissao =
                                   f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao,
                               NomeCliente =
                                   string.IsNullOrWhiteSpace(f.Horario.ClienteEstabelecimento.NomeCompletoCliente)
                                       ? f.Horario.Cliente.PessoaFisica.NomeCompleto
                                       : f.Horario.ClienteEstabelecimento.NomeCompletoCliente,
                               IdTransacao = f.Transacao.Id,
                               IdHorarioTransacao = f.Codigo,
                               ComissaoFoiAlteradaManualmente = f.Comissao.ComissaoAlteradaManualmente,
                               ValorComissaoParaPagarOriginal = f.Comissao.ValorComissaoParaPagarOriginal,
                               ResponsavelPelaUltimaAlteracaoDaComissao = f.Comissao.ResponsavelPelaUltimaAlteracaoDeComissao,
                               DataHoraUltimaAlteracaoManualComissao = f.Comissao.DataHoraUltimaAlteracaoManualComissao,
                               PermiteAlteracaoDaComissao =
                                   ((f.Transacao.TipoTransacao.Id != (int)TipoTransacaoEnum.Estorno) &&
                                    f.Transacao.TransacaoQueEstounouEsta == null),
                               ApelidoOuNomeProfissional =
                                   !String.IsNullOrEmpty(f.Horario.Profissional.PessoaFisica.Apelido)
                                       ? f.Horario.Profissional.PessoaFisica.Apelido
                                       : f.Horario.Profissional.PessoaFisica.NomeCompleto,
                               NomeServicoEstabelecimento = f.Horario.ServicoEstabelecimento.Nome,
                               TotalServicos = f.Preco * (f.Transacao.TipoTransacao.Id == 1 ? 1 : -1),
                               TotalDescontos = f.Desconto * (f.Transacao.TipoTransacao.Id == 1 ? 1 : -1),
                               TotalDescontoOperadoras =
                                   f.Transacao.TotalDescontoOperadoras * (f.Transacao.TipoTransacao.Id == 1 ? 1 : -1),
                               TotalDescartaveis = f.Descartaveis * (f.Transacao.TipoTransacao.Id == 1 ? 1 : -1),
                               TotalComissao = f.Comissao.ComissaoParaPagar * (f.Transacao.TipoTransacao.Id == 1 ? 1 : -1),
                               TotalPercentualComissao = f.Comissao.ValorComissao,
                               IdProfissional = parametros.IdProfissional,
                               IdPessoa = idPessoa
                           });

            retorno = parametros.TipoData == TipoDataRelatorio.DataTransacao
                ? retorno.OrderBy(p => p.DataHoraTransacao).ThenBy(p => p.DataHoraInicioHorario)
                : retorno.OrderBy(p => p.DataHoraInicioHorario).ThenBy(p => p.DataHoraTransacao);

            return retorno.ToList();
        }

        public List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatasAgrupadosPorDia(
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return ObterRelatorioHorarioTransacoesEntreDatasTransacaoAgrupadosPorDia(parametros);
            return ObterRelatorioHorarioTransacoesEntreDatasAtendimentoAgrupadosPorDia(parametros);
        }

        public List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatasAgrupadosPorMesAno(
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataAtendimento)
                return ObterRelatorioHorarioTransacoesEntreDatasAtendimentoAgrupadosPorMesAno(parametros);
            return ObterRelatorioHorarioTransacoesEntreDatasMovimentacaoAgrupadosPorMesAno(parametros);
        }

        public List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesPorProfissional(
            ParametrosFiltrosRelatorio parametros)
        {
            var dados = Listar(parametros);

            var grupo =
                from f in dados
                group f by new
                {
                    f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                    f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                    f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao,
                    f.Horario.Profissional.IdProfissional,
                    f.Horario.Profissional.PessoaFisica.IdPessoa,
                    Codigo = f.Transacao.TipoTransacao.Id,
                    f.Horario.Profissional.PessoaFisica.NomeCompleto,
                    f.Horario.Profissional.PessoaFisica.Apelido
                }
                    into g
                select new
                {
                    g.Key,
                    ExibeValoresDescartaveis = g.Key.TrabalhaComDescartaveis,
                    g.Key.DescontarDescartaveisDoValorPago,
                    g.Key.ConsiderarDescontoOperadoraNaComissao,
                    TotalServicos = g.Sum(f => f.Preco) * (g.Key.Codigo == 1 ? 1 : -1),
                    TotalDescontos = g.Sum(f => f.Desconto) * (g.Key.Codigo == 1 ? 1 : -1),
                    TotalDescontoOperadoras =
                        g.Sum(f => f.Transacao.TotalDescontoOperadoras) * (g.Key.Codigo == 1 ? 1 : -1),
                    TotalDescartaveis = g.Sum(f => f.Descartaveis) * (g.Key.Codigo == 1 ? 1 : -1),
                    TotalComissaoAGanhar = g.Sum(f => f.Comissao.ComissaoParaPagar) * (g.Key.Codigo == 1 ? 1 : -1),
                    TotalPercentualComissao = g.Sum(f => f.Comissao.ValorComissao),
                    g.Key.IdProfissional,
                    g.Key.IdPessoa
                };

            var queryEp =
                Domain.Pessoas.EstabelecimentoProfissionalRepository.Queryable();
            var retorno = (from t in grupo.ToList()
                           let profissionalAtivo =
                               queryEp.FirstOrDefault(q =>
                                   q.Estabelecimento.IdEstabelecimento == parametros.Estabelecimento.IdEstabelecimento &&
                                   q.Profissional.IdProfissional == t.IdProfissional)
                           group t by
                               new
                               {
                                   t.Key.NomeCompleto,
                                   t.Key.Apelido,
                                   t.IdProfissional,
                                   t.IdPessoa,
                                   ProfissionalAtivo = profissionalAtivo == null || profissionalAtivo.Ativo
                               }
                               into g
                           orderby g.Key.NomeCompleto
                           select new RelatorioHorarioTransacao
                           {
                               IdProfissional = g.Key.IdProfissional,
                               IdPessoa = g.Key.IdPessoa,
                               DescontarDescartaveisDoValorPago = g.FirstOrDefault().DescontarDescartaveisDoValorPago,
                               ConsiderarDescontoOperadoraNaComissao = g.FirstOrDefault().ConsiderarDescontoOperadoraNaComissao,
                               ProfissionalExcluido = !g.Key.ProfissionalAtivo,
                               Profissional = !String.IsNullOrEmpty(g.Key.Apelido) ? g.Key.Apelido : g.Key.NomeCompleto,
                               TotalServicos = g.Sum(f => f.TotalServicos),
                               TotalDescontos = g.Sum(f => f.TotalDescontos),
                               TotalDescontoOperadoras = g.Sum(f => f.TotalDescontoOperadoras),
                               TotalDescartaveis = g.Sum(f => f.TotalDescartaveis),
                               TotalComissao = g.Sum(f => f.TotalComissaoAGanhar),
                               TotalPercentualComissao = g.Sum(f => f.TotalPercentualComissao),
                               Dia = parametros.DataInicial.HasValue ? parametros.DataInicial.Value.Day : 0,
                               Mes = parametros.DataInicial.HasValue ? parametros.DataInicial.Value.Month : 0,
                               Ano = parametros.DataInicial.HasValue ? parametros.DataInicial.Value.Year : 0
                           });

            return retorno.ToList();
        }

        public decimal? ObterValorTotalServicosAcumuladosNoMes(Estabelecimento estabelecimento, int mes, int ano,
            int? idProfissional = null, bool aplicarPercentualDosProfissionais = false)
        {
            var retorno = Queryable(estabelecimento);

            var dataInicio = new DateTime(ano, mes, 1);
            var dataFim = dataInicio.AddMonths(1);

            retorno = retorno.Where(f => f.Horario.DataInicio >= dataInicio && f.Horario.DataInicio < dataFim);
            var valorTotal = ObterValorTotal(estabelecimento.IdEstabelecimento, idProfissional, retorno);

            if (aplicarPercentualDosProfissionais)
            {
                var estabelecimentoConfiguracaoGeral = estabelecimento.EstabelecimentoConfiguracaoGeral;
                Decimal? percentual = estabelecimentoConfiguracaoGeral.PercentualAplicadoTotaisServicosPAT.HasValue
                    ? estabelecimentoConfiguracaoGeral.PercentualAplicadoTotaisServicosPAT.Value
                    : 100;

                valorTotal = Math.Round((Decimal)(valorTotal * percentual / 100), 2);
            }

            return valorTotal;
        }

        public decimal? ObterValorTotalServicosFechados(Estabelecimento estabelecimento, DateTime data,
            int? idProfissional = null, bool aplicarPercentualDosProfissionais = false)
        {
            var retorno = Queryable(estabelecimento);
            retorno = retorno.Where(f => f.Horario.DataInicio.Date == data.Date);
            var valorTotal = ObterValorTotal(estabelecimento.IdEstabelecimento, idProfissional, retorno);

            if (aplicarPercentualDosProfissionais)
            {
                var estabelecimentoConfiguracaoGeral = estabelecimento.EstabelecimentoConfiguracaoGeral;
                var percentual = estabelecimentoConfiguracaoGeral.PercentualAplicadoTotaisServicosPAT.HasValue
                    ? estabelecimentoConfiguracaoGeral.PercentualAplicadoTotaisServicosPAT
                    : 100;

                valorTotal = Math.Round((Decimal)(valorTotal * percentual / 100), 2);
            }

            return valorTotal;
        }

        public IQueryable<HorarioTransacao> Queryable(Estabelecimento estabelecimento)
        {
            var exibirDadosAPartirDe = estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;

            if (exibirDadosAPartirDe.HasValue)
                return Queryable().Where(f => f.Transacao.DataHora >= exibirDadosAPartirDe.Value.Date);

            return Queryable();
        }

        public IQueryable<HorarioTransacao> StatelessQueryable(Estabelecimento estabelecimento)
        {
            var exibirDadosAPartirDe = estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;

            if (exibirDadosAPartirDe.HasValue)
                return StatelessQueryable().Where(f => f.Transacao.DataHora >= exibirDadosAPartirDe);

            return StatelessQueryable();
        }

        public decimal ValorComissaoServico(ParametrosFiltrosRelatorio filtro, bool somenteNaoFechados)
        {
            var buscarDeValores = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, somenteNaoFechados);
            return (buscarDeValores.Sum(f => f.Valor));
        }

        #endregion Métodos Públicos

        private static void AdicionarCriterioHorario(int codigoHorario, DetachedCriteria dc)
        {
            var propertyNameIdHorario = String.Format("{0}.{1}", PropertyNames.Pessoas.HorarioTransacao.Horario,
                PropertyNames.Pessoas.Horario.Codigo);
            dc.Add(Restrictions.Eq(propertyNameIdHorario, codigoHorario));
        }

        private static IQueryable<HorarioTransacao> FiltroAsisstenteParaComissoes(
            IQueryable<HorarioTransacao> ht, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var pessoaFisica = estabelecimentoProfissional.Profissional.PessoaFisica;
            return ht.Where(f => f.ComissaoAssistente.PessoaComissionada == pessoaFisica);
        }

        private static IQueryable<HorarioTransacao> FiltroEstabelecimento(IQueryable<HorarioTransacao> ht,
                    Estabelecimento estabelecimento)
        {
            return ht.Where(
                f =>
                    f.Transacao.PessoaQueRecebeu == estabelecimento.PessoaJuridica
                    && f.Transacao.PessoaQueRecebeu == estabelecimento.PessoaJuridica);
        }

        private static IQueryable<HorarioTransacao> FiltroNaoEstornada(IQueryable<HorarioTransacao> ht)
        {
            return ht.Where(f => f.Transacao.TransacaoQueEstounouEsta == null);
        }

        private static IQueryable<HorarioTransacao> FiltroPeriodoDataLiberacaoDaComissaoAssistente(
            ParametrosFiltrosRelatorio filtro, IQueryable<HorarioTransacao> ht, bool somenteNaoFechados)
        {
            var buscaDeValoresAReceber = Domain.Financeiro.ValorDeComissaoAReceberRepository.Queryable();
            return from f in ht
                   where
                   buscaDeValoresAReceber.Any(v => v.Ativo && (v.Comissao == f.ComissaoAssistente)
                       && (somenteNaoFechados == false || v.FechamentoProfisisonal == null)
                                    &&
                                    (
                                        ((somenteNaoFechados == false && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.Date)
                                            ||
                                        (somenteNaoFechados == true && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.AddMonths(-1).Date)
                                        )
                                     &&
                                     v.DataDaComissaoAReceber < filtro.DataFinal.Value.Date.AddDays(1)))

                   select f;
        }

        private static IQueryable<HorarioTransacao> FiltroPeriodoDataLiberacaoDaComissaoProfissional(
            ParametrosFiltrosRelatorio filtro, IQueryable<HorarioTransacao> ht, bool somenteNaoFechados)
        {
            var buscaDeValoresAReceber = Domain.Financeiro.ValorDeComissaoAReceberRepository.Queryable();
            return from f in ht
                   where
                   buscaDeValoresAReceber.Any(v => v.Ativo && (v.Comissao == f.Comissao)
                       && (somenteNaoFechados == false || v.FechamentoProfisisonal == null)
                                    &&
                                    (
                                        ((somenteNaoFechados == false && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.Date)
                                            ||
                                        (somenteNaoFechados == true && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.AddMonths(-1).Date)
                                        )
                                     &&
                                     v.DataDaComissaoAReceber < filtro.DataFinal.Value.Date.AddDays(1)))

                   select f;
        }

        private static IQueryable<HorarioTransacao> FiltroProfissionalOuAssistentePrincipalParaComissoes(
            IQueryable<HorarioTransacao> ht, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var pessoaFisica = estabelecimentoProfissional.Profissional.PessoaFisica;
            return ht.Where(
                f =>
                    (f.Comissao != null && f.Comissao.PessoaComissionada == pessoaFisica) ||
                    (f.ComissaoAssistente != null && f.ComissaoAssistente.PessoaComissionada == pessoaFisica));
        }

        private static IQueryable<HorarioTransacao> FiltroProfissionalPrincipalParaComissoes(
            IQueryable<HorarioTransacao> ht, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var pessoaFisica = estabelecimentoProfissional.Profissional.PessoaFisica;
            return ht.Where(f => f.Comissao.PessoaComissionada == pessoaFisica);
        }

        private static IQueryable<HorarioTransacao> FiltroTipoPagamento(IQueryable<HorarioTransacao> ht)
        {
            return ht.Where(f => f.Transacao.TipoTransacao.Id == 1);
        }

        private static decimal? ObterValorTotal(int idEstabelecimento, int? idProfissional,
                                                                                    IQueryable<HorarioTransacao> retorno)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(Domain.WebContext.IdEstabelecimentoAutenticado.Value);

            retorno = retorno.Where(f => f.Horario.Estabelecimento.IdEstabelecimento == idEstabelecimento);

            retorno = FiltroNaoEstornada(retorno);
            retorno = FiltroTipoPagamento(retorno);
            retorno = retorno.Where(ht => ht.Transacao.PessoaQueRecebeu == estabelecimento.PessoaJuridica);

            if (idProfissional.HasValue)
                retorno = retorno.Where(f => f.Horario.Profissional.IdProfissional == idProfissional ||
                                             (f.Horario.EstabelecimentoProfissionalAssistente != null &&
                                              f.Horario.EstabelecimentoProfissionalAssistente.Profissional
                                                  .IdProfissional == idProfissional));

            decimal valorComPacote = 0;
            if (retorno.Any(f => f.ItemPacoteCliente != null))
                valorComPacote =
                    retorno.Where(f => f.ItemPacoteCliente != null).Sum(f => f.ItemPacoteCliente.ValorUnitario);

            decimal valorSemPacote = 0;
            if (retorno.Any(f => f.ItemPacoteCliente == null))
                valorSemPacote =
                    retorno.Where(f => f.ItemPacoteCliente == null).Sum(f => f.Preco + f.Comissao.DescontoCliente) ?? 0;

            return valorComPacote + valorSemPacote;
        }

        private IQueryable<HorarioTransacao> Listar(ParametrosFiltrosRelatorio parametros)
        {
            var dados = Queryable(parametros.Estabelecimento).Where(f => f.Ativo
                                               &&
                                               f.Horario.Estabelecimento.IdEstabelecimento ==
                                               parametros.Estabelecimento.IdEstabelecimento);

            if (parametros.TipoData == TipoDataRelatorio.DataAtendimento)
            {
                if (parametros.DataInicial.HasValue)
                {
                    if (parametros.DataInicial.Value.Year > 2000)
                        dados = dados.Where(f => f.Horario.DataInicio >= parametros.DataInicial);
                }

                if (parametros.DataFinal.HasValue)
                {
                    if (parametros.DataFinal.Value.Year > 2000)
                        dados = dados.Where(f => f.Horario.DataFim <= parametros.DataFinal);
                }
            }
            else
            {
                if (parametros.DataInicial.HasValue)
                {
                    if (parametros.DataInicial.Value.Year > 2000)
                        dados = dados.Where(f => f.Transacao.DataHora >= parametros.DataInicial);
                }

                if (parametros.DataFinal.HasValue)
                {
                    if (parametros.DataFinal.Value.Year > 2000)
                        dados = dados.Where(f => f.Transacao.DataHora <= parametros.DataFinal);
                }
            }

            if (parametros.IdProfissional > 0)
                dados = dados.Where(f => f.Horario.Profissional.IdProfissional == parametros.IdProfissional);
            return dados;
        }

        private IEnumerable<FechamentoMesComissaoDeServicoDTO> ObterComissoesPorDataDaComissao(ParametrosFiltrosRelatorio filtro, bool stateless, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            var idProfissional = estabelecimentoProfissional.Profissional.IdProfissional;
            var idPessoaProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa;
            var idPessoaEstabelecimento = estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa;
            var valores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Queryable();

            var queryable = stateless ? StatelessQueryable(estabelecimentoProfissional.Estabelecimento) : Queryable(estabelecimentoProfissional.Estabelecimento);
            var hts = from ht in queryable
                      where ht.Horario.Estabelecimento.IdEstabelecimento == estabelecimentoProfissional.Estabelecimento.IdEstabelecimento
                        && ht.Transacao.TransacaoQueEstounouEsta == null
                        && ht.Transacao.TipoTransacao.Id == 1
                        && ht.Transacao.PessoaQueRecebeu.IdPessoa == idPessoaEstabelecimento
                      //&& ht.Horario.ServicoEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa == idPessoaEstabelecimento
                      select ht;

            var htsSemFechamentoMesProfissionais = from ht in hts
                                                   join v in valores on ht.Comissao.Id equals v.Comissao.Id
                                                   where v.Ativo
                                                    && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.Date
                                                    && v.DataDaComissaoAReceber < filtro.DataFinal.Value.Date.AddDays(1)
                                                    && ht.Horario.Profissional.IdProfissional == idProfissional
                                                    && v.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                    && ht.Comissao.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                    && ht.Comissao.PessoaComissionada.IdPessoa == idPessoaProfissional
                                                   select new { ht, v };

            var htDTOProfissionais = from f in htsSemFechamentoMesProfissionais
                                     let comissao = f.ht.Horario.Profissional.IdProfissional == idProfissional ? f.ht.Comissao : null
                                     select new
                                     {
                                         Comissao = comissao,
                                         ComissaoAssistente = (Comissao)null,
                                         Horario = new
                                         {
                                             Codigo = f.ht.Horario.Codigo,
                                             NomeServicoEstabelecimento = f.ht.Horario.ServicoEstabelecimento.Nome,
                                             Profissional = f.ht.Horario.Profissional,
                                             EstabelecimentoProfissionalAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente,
                                             Cliente = f.ht.Horario.Cliente,
                                             DataInicio = f.ht.Horario.DataInicio
                                         },
                                         MotivoDesconto = f.ht.MotivoDesconto,
                                         ItemPacoteCliente = f.ht.ItemPacoteCliente,
                                         Transacao = f.ht.Transacao,
                                         Desconto = comissao.DescontoCliente,
                                         Preco = comissao.ValorBruto,
                                         UsouPontosDeFidelidade = f.ht.UsouPontosDeFidelidade,
                                         DataLiberacaoPagamento = f.v.DataDaComissaoAReceber,
                                         ComissaoValor = f.v.Valor,
                                         ValorProporcional = f.v.ValorBaseProporcional,
                                         PossuiComissaoParcelada = f.ht.Comissao.ValorBruto != f.v.ValorBrutoProporcional,
                                         EhComissaoComSplit = f.ht.Comissao.EhComissaoComSplit,
                                         CustoDescartavel = f.ht.Descartaveis
                                     };

            var htsSemFechamentoMesAssistentes = from ht in hts
                                                 join v in valores on ht.ComissaoAssistente.Id equals v.Comissao.Id
                                                 where v.Ativo
                                                    && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.Date
                                                    && v.DataDaComissaoAReceber < filtro.DataFinal.Value.Date.AddDays(1)
                                                    && ht.Horario.EstabelecimentoProfissionalAssistente != null
                                                    && ht.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional
                                                    && ht.Transacao.PessoaQueRecebeu.IdPessoa == idPessoaEstabelecimento
                                                    && ht.ComissaoAssistente != null
                                                    && ht.ComissaoAssistente.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                    && ht.ComissaoAssistente.PessoaComissionada.IdPessoa == idPessoaProfissional
                                                    && v.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                    && ht.ComissaoAssistente.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                    && ht.ComissaoAssistente.PessoaComissionada.IdPessoa == idPessoaProfissional
                                                 select new { ht, v };

            var htDTOAssistentes = from f in htsSemFechamentoMesAssistentes
                                   let comissaoAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente != null &&
                                                            f.ht.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional
                                       ? f.ht.ComissaoAssistente
                                       : null
                                   let comissao = f.ht.Horario.Profissional.IdProfissional == idProfissional ? f.ht.Comissao : null
                                   select new
                                   {
                                       Comissao = comissao,// ?? new Comissao(idPessoaEstabelecimento),
                                       ComissaoAssistente = comissaoAssistente,// ?? new Comissao(idPessoaEstabelecimento),
                                       Horario = new
                                       {
                                           Codigo = f.ht.Horario.Codigo,
                                           NomeServicoEstabelecimento = f.ht.Horario.ServicoEstabelecimento.Nome,
                                           Profissional = f.ht.Horario.Profissional,
                                           EstabelecimentoProfissionalAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente,
                                           Cliente = f.ht.Horario.Cliente,
                                           DataInicio = f.ht.Horario.DataInicio
                                       },
                                       MotivoDesconto = f.ht.MotivoDesconto,
                                       ItemPacoteCliente = f.ht.ItemPacoteCliente,
                                       Transacao = f.ht.Transacao,
                                       Desconto = comissao != null ? comissao.DescontoCliente : (comissaoAssistente != null ? comissaoAssistente.DescontoCliente : 0),
                                       Preco = comissao != null ? comissao.ValorBruto : (comissaoAssistente != null ? comissaoAssistente.ValorBruto : 0),
                                       UsouPontosDeFidelidade = f.ht.UsouPontosDeFidelidade,
                                       DataLiberacaoPagamento = f.v.DataDaComissaoAReceber,
                                       ComissaoValor = f.v.Valor,
                                       ValorProporcional = f.v.ValorBrutoProporcional,
                                       PossuiComissaoParcelada = comissaoAssistente.ValorBruto != f.v.ValorBrutoProporcional,
                                       EhComissaoComSplit = comissaoAssistente.EhComissaoComSplit,
                                       CustoDescartavel = f.ht.Descartaveis
                                   };

            var htsDTO = htDTOProfissionais.ToList();
            htsDTO.AddRange(htDTOAssistentes);

            //tipo de impressão: agrupados
            if (filtro.TipoDeImpressao == TipoImpressaoComissaoEnum.Agrupados)
            {
                var buscaDeValoresALiberarListaProfissional = from ht in hts
                                                              let comissao = ht.Comissao
                                                              join v in valores on comissao equals v.Comissao
                                                              where v.Ativo
                                                                && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.Date
                                                                && v.DataDaComissaoAReceber < filtro.DataFinal.Value.Date.AddDays(1)
                                                                && v.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                                && comissao.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                                && comissao.PessoaComissionada.IdPessoa == idPessoaProfissional
                                                              select new { IdComissao = comissao.Id, v.Valor, v.TransacaoFormaPagamentoParcela, v.DataDaComissaoAReceber };

                var buscaDeValoresALiberarListaAssistente = from ht in hts
                                                            let comissaoAssistente = ht.ComissaoAssistente
                                                            join v in valores on comissaoAssistente equals v.Comissao
                                                            where v.Ativo
                                                                && v.DataDaComissaoAReceber >= filtro.DataInicial.Value.Date
                                                                && v.DataDaComissaoAReceber < filtro.DataFinal.Value.Date.AddDays(1)
                                                                && v.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                                && comissaoAssistente.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                                                && comissaoAssistente.PessoaComissionada.IdPessoa == idPessoaProfissional
                                                            select new { IdComissao = comissaoAssistente.Id, v.Valor, v.TransacaoFormaPagamentoParcela, v.DataDaComissaoAReceber };

                var buscaDeValoresALiberarLista = buscaDeValoresALiberarListaProfissional.ToList();
                buscaDeValoresALiberarLista.AddRange(buscaDeValoresALiberarListaAssistente);

                return (from i in htsDTO
                        group i by new { Nome = i.Horario.NomeServicoEstabelecimento } into g
                        //group i by new { Nome = i.Horario.ServicoEstabelecimento.Nome, ValorPago = (i.ItemPacoteCliente == null ? (i.Preco) + (i.Desconto) : i.ItemPacoteCliente.ValorUnitario) } into g
                        let horariosAssistente = g.Where(f => f.Horario.EstabelecimentoProfissionalAssistente != null && f.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == estabelecimentoProfissional.Profissional.IdProfissional && f.ComissaoAssistente != null)
                        let horariosProfissional = g.Where(f => f.Horario.Profissional.IdProfissional == estabelecimentoProfissional.Profissional.IdProfissional)
                        select new FechamentoMesComissaoDeServicoDTO
                        {
                            Comissao = buscaDeValoresALiberarLista.Where(v => g.Select(r => r.Comissao?.Id).Contains(v.IdComissao) || g.Where(f => f.ComissaoAssistente != null).Select(r => r.ComissaoAssistente?.Id).Contains(v?.IdComissao)).Sum(v => v.Valor),
                            Nome = g.Key.Nome,
                            Quantidade = g.GroupBy(p => p.Horario.Codigo).Count(),
                            ValorPago = g.GroupBy(f => new
                            {
                                Codigo = f.Horario.Codigo,
                                Valor = f.ItemPacoteCliente == null ? (f.Preco) + (f.MotivoDesconto != null ? (f.MotivoDesconto.DescontoRefleteNaComissao ? (f.Desconto) : 0) : (f.Desconto)) : f.ItemPacoteCliente.ValorUnitario
                            }).Sum(x => x.Key.Valor)
                        })
                        .OrderBy(f => f.Nome).ToList();
            }
            //tipo de impressão: detalhados
            else
            {
                return (from i in htsDTO
                        group i by new
                        {
                            DataAtendimento = i.Horario.DataInicio,
                            DataPagamento = i.Transacao.DataHora,
                            NomeCliente = i.Horario.Cliente.PessoaFisica.NomeOuApelido(),
                            Nome = i.Horario.NomeServicoEstabelecimento,
                            ValorPago = (i.ItemPacoteCliente == null ? (i.Preco) + (i.Desconto) : i.ItemPacoteCliente.ValorUnitario),
                            Transacao = i.Transacao,
                            EhItemPacoteCliente = i.ItemPacoteCliente != null,
                            EhPacoteDaRede = i.ItemPacoteCliente != null && i.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.PessoaJuridica.IdPessoa != i.Transacao.PessoaQueRecebeu.IdPessoa,
                            UsouPontosDeFidelidade = i.UsouPontosDeFidelidade,
                            ComoAssistente = i.Horario.EstabelecimentoProfissionalAssistente != null && i.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional && i.ComissaoAssistente != null,
                            TeveAssistente = i.Horario.EstabelecimentoProfissionalAssistente != null,
                            ApelidoOuNomeAssistente = i.Horario.EstabelecimentoProfissionalAssistente != null ? i.Horario.EstabelecimentoProfissionalAssistente.Profissional.PessoaFisica.PrimeiroNomeOuApelido() : "",//TODO:verificar se não terá impacto no
                            IdComissao = i.Comissao?.Id,
                            IdComissaoAssistente = i.ComissaoAssistente?.Id,
                            DataLiberacaoPagamento = i.DataLiberacaoPagamento,
                            PossuiComissaoParcelada = i.PossuiComissaoParcelada,
                            EhComissaoComSplit = i.EhComissaoComSplit,
                            CustoDescartavel = i.CustoDescartavel
                        }
    into g
                        select new FechamentoMesComissaoDeServicoDTO
                        {
                            DataAtendimento = g.Key.DataAtendimento,
                            NomeCliente = g.Key.NomeCliente,
                            Nome = g.Key.Nome,
                            ValorPago = g.Key.ValorPago,
                            FormasDePagamento = g.Key.Transacao.FormasPagamento.Where(p => p.ValorPago > 0).ToList(),
                            Comissao = g.Sum(p => p.ComissaoValor),
                            EhItemPacoteCliente = g.Key.EhItemPacoteCliente,
                            EhPacoteDaRede = g.Key.EhPacoteDaRede,
                            UsouPontosDeFidelidade = g.Key.UsouPontosDeFidelidade,
                            ComoAssistente = g.Key.ComoAssistente,
                            TeveAssistente = g.Key.TeveAssistente,
                            ApelidoOuNomeAssistente = g.Key.ApelidoOuNomeAssistente,
                            DataLiberacaoPagamento = g.Key.DataLiberacaoPagamento,
                            DataPagamento = g.Key.DataPagamento,
                            ValorProporcional = g.Sum(p => p.ValorProporcional),
                            PossuiComissaoParcelada = g.Key.PossuiComissaoParcelada,
                            EhComissaoComSplit = g.Key.EhComissaoComSplit,
                            CustoDescartavel = g.Key.CustoDescartavel
                        })
                        .OrderBy(i => i.DataAtendimento.Date).ThenBy(i => i.DataPagamento.Date).ThenBy(i => i.DataLiberacaoPagamento.Date).ThenBy(i => i.NomeCliente).ThenBy(i => i.Nome).ToList();
            }
        }

        private IEnumerable<FechamentoMesComissaoDeServicoDTO> ObterComissoesPorDataPagamentoAgrupado(ParametrosFiltrosRelatorio filtro, bool stateless, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            int idProfissional;
            var idPessoaEstabelecimento = estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa;
            IQueryable<HorarioTransacao> hts;
            ObterDadosCompartilhadosComissoesPorFiltro(filtro, stateless, estabelecimentoProfissional, out idProfissional, out hts);

            var htProfissionais = from ht in hts.Where(f => f.Horario.Profissional.IdProfissional == idProfissional)
                                  select new { ht };

            var htProfissionaisDTO = from f in htProfissionais
                                     let comissao = f.ht.Comissao
                                     select new
                                     {
                                         Comissao = comissao,// ?? new Comissao(idPessoaEstabelecimento),
                                         ComissaoAssistente = (Comissao)null,//new Comissao(idPessoaEstabelecimento),
                                         Horario = new Horario
                                         {
                                             ServicoEstabelecimento = f.ht.Horario.ServicoEstabelecimento,
                                             Profissional = f.ht.Horario.Profissional,
                                             EstabelecimentoProfissionalAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente,
                                             Cliente = f.ht.Horario.Cliente,
                                             DataInicio = f.ht.Horario.DataInicio
                                         },
                                         MotivoDesconto = f.ht.MotivoDesconto,
                                         ItemPacoteCliente = f.ht.ItemPacoteCliente,
                                         Transacao = f.ht.Transacao,
                                         Desconto = comissao != null ? comissao.DescontoCliente : 0,
                                         Preco = comissao != null ? comissao.ValorBruto : 0,
                                         UsouPontosDeFidelidade = f.ht.UsouPontosDeFidelidade,
                                         CustoDescartavel = f.ht.Descartaveis
                                     };

            var htAssistentes = from ht in hts.Where(f => f.Horario.EstabelecimentoProfissionalAssistente != null &&
                                                          f.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional)
                                select new { ht };

            var htAssistentesDTO = from f in htAssistentes
                                   let comissaoAssistente = f.ht.ComissaoAssistente
                                   select new
                                   {
                                       Comissao = (Comissao)null,// new Comissao(idPessoaEstabelecimento),
                                       ComissaoAssistente = comissaoAssistente,// ?? new Comissao(idPessoaEstabelecimento),
                                       Horario = new Horario
                                       {
                                           ServicoEstabelecimento = f.ht.Horario.ServicoEstabelecimento,
                                           Profissional = f.ht.Horario.Profissional,
                                           EstabelecimentoProfissionalAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente,
                                           Cliente = f.ht.Horario.Cliente,
                                           DataInicio = f.ht.Horario.DataInicio
                                       },
                                       MotivoDesconto = f.ht.MotivoDesconto,
                                       ItemPacoteCliente = f.ht.ItemPacoteCliente,
                                       Transacao = f.ht.Transacao,
                                       Desconto = comissaoAssistente != null ? comissaoAssistente.DescontoCliente : 0,
                                       Preco = comissaoAssistente != null ? comissaoAssistente.ValorBruto : 0,
                                       UsouPontosDeFidelidade = f.ht.UsouPontosDeFidelidade,
                                       CustoDescartavel = f.ht.Descartaveis
                                   };

            var htDTO = htProfissionaisDTO.ToList();
            htDTO.AddRange(htAssistentesDTO.ToList());

            return (from i in htDTO
                    group i by i.Horario.ServicoEstabelecimento.Nome into g
                    let horariosAssistente = g.Where(f => f.Horario.EstabelecimentoProfissionalAssistente != null && f.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == estabelecimentoProfissional.Profissional.IdProfissional && f.ComissaoAssistente != null)
                    let horariosProfissional = g.Where(f => f.Horario.Profissional.IdProfissional == estabelecimentoProfissional.Profissional.IdProfissional)
                    select new FechamentoMesComissaoDeServicoDTO
                    {
                        Comissao = (horariosProfissional.Sum(f => f.Comissao.ComissaoParaPagar) + horariosAssistente.Sum(f => f.ComissaoAssistente.ComissaoParaPagar)) ?? 0,
                        //Comissao = horariosProfissional.Sum(f => f.ComissaoValor) + horariosAssistente.Sum(f => f.ComissaoValor),
                        Nome = g.Key,
                        Quantidade = g.Count(),
                        ValorPago = g.Sum(f => f.ItemPacoteCliente == null ?
                            (f.Preco) + (f.MotivoDesconto != null ? (f.MotivoDesconto.DescontoRefleteNaComissao ? (f.Desconto) : 0) : (f.Desconto)) :
                            f.ItemPacoteCliente.ValorUnitario)
                    })
                .OrderBy(f => f.Nome).ToList();
        }

        private IEnumerable<FechamentoMesComissaoDeServicoDTO> ObterComissoesPorDataPagamentoDetalhado(ParametrosFiltrosRelatorio filtro, bool stateless, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            int idProfissional;
            var idPessoaProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa;
            var idPessoaEstabelecimento = estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa;
            IQueryable<HorarioTransacao> hts;
            ObterDadosCompartilhadosComissoesPorFiltro(filtro, stateless, estabelecimentoProfissional, out idProfissional, out hts);

            var valores = Domain.Financeiro.ValorDeComissaoAReceberRepository.Queryable();

            var htProfissionais = from ht in hts
                                  join v in valores on ht.Comissao equals v.Comissao
                                  where ht.Horario.Profissional.IdProfissional == idProfissional
                                      && v.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                      && v.Comissao.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                      && v.Comissao.PessoaComissionada.IdPessoa == idPessoaProfissional
                                  select new { ht, v };

            var formasPagamentoProfissional = htProfissionais.SelectMany(f => f.ht.Transacao.FormasPagamento);

            var htProfissionaisDTO = from f in htProfissionais
                                     let comissao = f.ht.Comissao
                                     select new
                                     {
                                         IdComissao = (int?)comissao.Id,// ?? new Comissao(idPessoaEstabelecimento),
                                         IdComissaoAssistente = (int?)null,// new Comissao(idPessoaEstabelecimento),
                                         NomeCliente = f.ht.Transacao.PessoaQuePagou.NomeCompleto,
                                         NomeServico = f.ht.Horario.ServicoEstabelecimento.Nome,
                                         DataHora = f.ht.Horario.DataInicio,
                                         EstabelecimentoProfissionalAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente,
                                         MotivoDesconto = f.ht.MotivoDesconto,
                                         ItemPacoteCliente = f.ht.ItemPacoteCliente,
                                         IdTransacao = f.ht.Transacao.Id,
                                         DataTransacao = f.ht.Transacao.DataHora,
                                         Desconto = comissao.DescontoCliente,
                                         Preco = comissao.ValorBruto,
                                         UsouPontosDeFidelidade = f.ht.UsouPontosDeFidelidade,
                                         DataLiberacaoPagamento = f.v.DataDaComissaoAReceber,
                                         ComissaoValor = f.v.Valor,
                                         ValorProporcional = f.v.ValorBaseProporcional,
                                         PossuiComissaoParcelada = f.v.Comissao.ValorBruto != f.v.ValorBrutoProporcional,
                                         EhComissaoComSplit = f.v.Comissao.EhComissaoComSplit,
                                         CustoDescartavel = f.ht.Descartaveis
                                     };

            var htAssistentes = from ht in hts.Where(f => f.Horario.EstabelecimentoProfissionalAssistente != null &&
                                                          f.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional)
                                join v in valores on ht.ComissaoAssistente equals v.Comissao
                                where v.Ativo
                                    && v.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                    && v.Comissao.IdPessoaEstabelecimento == idPessoaEstabelecimento
                                    && v.Comissao.PessoaComissionada.IdPessoa == idPessoaProfissional
                                select new { ht, v };

            var formasPagamentoAssistente = htAssistentes.SelectMany(f => f.ht.Transacao.FormasPagamento);

            var htAssistentesDTO = from f in htAssistentes
                                   let comissaoAssistente = f.ht.ComissaoAssistente
                                   select new
                                   {
                                       IdComissao = (int?)null,// new Comissao(idPessoaEstabelecimento),
                                       IdComissaoAssistente = (int?)comissaoAssistente.Id,// ?? new Comissao(idPessoaEstabelecimento),
                                       NomeCliente = f.ht.Transacao.PessoaQuePagou.NomeCompleto,
                                       NomeServico = f.ht.Horario.ServicoEstabelecimento.Nome,
                                       DataHora = f.ht.Horario.DataInicio,
                                       EstabelecimentoProfissionalAssistente = f.ht.Horario.EstabelecimentoProfissionalAssistente,
                                       MotivoDesconto = f.ht.MotivoDesconto,
                                       ItemPacoteCliente = f.ht.ItemPacoteCliente,
                                       IdTransacao = f.ht.Transacao.Id,
                                       DataTransacao = f.ht.Transacao.DataHora,
                                       Desconto = comissaoAssistente.DescontoCliente,
                                       Preco = comissaoAssistente.ValorBruto,
                                       UsouPontosDeFidelidade = f.ht.UsouPontosDeFidelidade,
                                       DataLiberacaoPagamento = f.v.DataDaComissaoAReceber,
                                       ComissaoValor = f.v.Valor,
                                       ValorProporcional = f.v.ValorBaseProporcional,
                                       PossuiComissaoParcelada = f.v.Comissao.ValorBruto != f.v.ValorBrutoProporcional,
                                       EhComissaoComSplit = f.v.Comissao.EhComissaoComSplit,
                                       CustoDescartavel = f.ht.Descartaveis
                                   };

            var htDTO = htProfissionaisDTO.ToList();
            htDTO.AddRange(htAssistentesDTO);

            var formasPagamento = formasPagamentoProfissional.ToHashSet();
            formasPagamentoAssistente.ToList().ForEach(f => formasPagamento.Add(f));

            return (from i in htDTO
                    group i by new
                    {
                        DataAtendimento = i.DataHora,
                        DataPagamento = i.DataTransacao,
                        NomeCliente = i.NomeCliente,
                        Nome = i.NomeServico,
                        ValorPago = (i.ItemPacoteCliente == null ? (i.Preco) + (i.MotivoDesconto != null ? (i.MotivoDesconto.DescontoRefleteNaComissao ? (i.Desconto) : 0) : (i.Desconto)) : i.ItemPacoteCliente.ValorUnitario),
                        IdTransacao = i.IdTransacao,
                        EhItemPacoteCliente = i.ItemPacoteCliente != null,
                        EhPacoteDaRede = i.ItemPacoteCliente != null && i.ItemPacoteCliente.PacoteCliente.PacoteOriginal.Estabelecimento.IdEstabelecimento != estabelecimentoProfissional.Estabelecimento.IdEstabelecimento,
                        UsouPontosDeFidelidade = i.UsouPontosDeFidelidade,
                        ComoAssistente = i.EstabelecimentoProfissionalAssistente != null && i.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional == idProfissional && i.IdComissaoAssistente != null,
                        TeveAssistente = i.EstabelecimentoProfissionalAssistente != null,
                        ApelidoOuNomeAssistente = i.EstabelecimentoProfissionalAssistente != null ? i.EstabelecimentoProfissionalAssistente.Profissional.PessoaFisica.PrimeiroNomeOuApelido() : "",//TODO:verificar se não terá impacto no
                        IdComissao = i.IdComissao,
                        IdComissaoAssistente = i.IdComissaoAssistente,
                        DataLiberacaoPagamento = i.DataLiberacaoPagamento,
                        PossuiComissaoParcelada = i.PossuiComissaoParcelada,
                        EhComissaoComSplit = i.EhComissaoComSplit,
                        CustoDescartavel = i.CustoDescartavel
                    } into g

                    //return (from i in htDTO
                    select new FechamentoMesComissaoDeServicoDTO
                    {
                        DataAtendimento = g.Key.DataAtendimento,
                        DataPagamento = g.Key.DataPagamento,
                        NomeCliente = g.Key.NomeCliente,
                        Nome = g.Key.Nome,
                        ValorPago = g.Key.ValorPago,
                        FormasDePagamento = formasPagamento.Where(p => p.Transacao.Id == g.Key.IdTransacao && p.ValorPago > 0).ToList(),
                        EhItemPacoteCliente = g.Key.EhItemPacoteCliente,
                        EhPacoteDaRede = g.Key.EhPacoteDaRede,
                        UsouPontosDeFidelidade = g.Key.UsouPontosDeFidelidade,
                        ComoAssistente = g.Key.ComoAssistente,
                        TeveAssistente = g.Key.TeveAssistente,
                        ApelidoOuNomeAssistente = g.Key.ApelidoOuNomeAssistente,
                        DataLiberacaoPagamento = g.Key.DataLiberacaoPagamento,
                        Comissao = g.Sum(p => p.ComissaoValor),
                        PossuiComissaoParcelada = g.Key.PossuiComissaoParcelada,
                        ValorProporcional = g.Sum(p => p.ValorProporcional),
                        EhComissaoComSplit = g.Key.EhComissaoComSplit,
                        CustoDescartavel = g.Key.CustoDescartavel
                    })
               .OrderBy(i => i.DataAtendimento.Date).ThenBy(i => i.DataPagamento.Date).ThenBy(i => i.DataLiberacaoPagamento.Date).ThenBy(i => i.NomeCliente).ThenBy(i => i.Nome).ToList();
        }

        private void ObterDadosCompartilhadosComissoesPorFiltro(ParametrosFiltrosRelatorio filtro, bool stateless, EstabelecimentoProfissional estabelecimentoProfissional, out int idProfissional, out IQueryable<HorarioTransacao> hts)
        {
            idProfissional = estabelecimentoProfissional.Profissional.IdProfissional;
            var idPessoaProfissional = estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa;
            var idPessoaEstabelecimento = estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa;

            hts = from f in stateless ? StatelessQueryable() : Queryable()
                  where f.Horario.Estabelecimento.IdEstabelecimento == estabelecimentoProfissional.Estabelecimento.IdEstabelecimento
                    && f.Transacao.TransacaoQueEstounouEsta == null
                    && f.Transacao.TipoTransacao.Id == 1
                    && f.Transacao.PessoaQueRecebeu.IdPessoa == idPessoaEstabelecimento
                    && f.Comissao.IdPessoaEstabelecimento == idPessoaEstabelecimento
                  select f;
            if (filtro != null && filtro.TipoData == TipoDataRelatorio.DataAtendimento)
                hts = hts.Where(f => f.Transacao.DataReferencia >= filtro.DataInicial && f.Transacao.DataReferencia <= filtro.DataFinal);
            else
                hts = hts.Where(f => f.Transacao.DataHora >= filtro.DataInicial && f.Transacao.DataHora <= filtro.DataFinal);
        }

        private IEnumerable<FechamentoMesComissaoDeServicoDTO> ObterComissoesPorDataPagamento(ParametrosFiltrosRelatorio filtro, bool stateless, EstabelecimentoProfissional estabelecimentoProfissional)
        {
            if (filtro.TipoDeImpressao == TipoImpressaoComissaoEnum.Agrupados)
                return ObterComissoesPorDataPagamentoAgrupado(filtro, stateless, estabelecimentoProfissional);
            else
                return ObterComissoesPorDataPagamentoDetalhado(filtro, stateless, estabelecimentoProfissional);
        }

        private List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatasAtendimentoAgrupadosPorDia(
            ParametrosFiltrosRelatorio parametros)
        {
            var dados = Listar(parametros);
            var idPessoa = Domain.Pessoas.ProfissionalRepository.Load(parametros.IdProfissional).PessoaFisica.IdPessoa;

            var queryRelatorioAgrupadosPorTipoEData =
                (from f in dados
                 group f by new
                 {
                     f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                     f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                     f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao,
                     f.DataHoraInicioHorario.Value.Date,
                     Codigo = f.Transacao.TipoTransacao.Id
                 }
                     into g
                 select new
                 {
                     g.Key,
                     ExibeValoresDescartaveis = g.Key.TrabalhaComDescartaveis,
                     g.Key.DescontarDescartaveisDoValorPago,
                     g.Key.ConsiderarDescontoOperadoraNaComissao,
                     TotalServicos = g.Sum(f => f.Preco) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalDescontos = g.Sum(f => f.Desconto) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalDescontoOperadoras =
                         g.Sum(f => f.Transacao.TotalDescontoOperadoras) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalDescartaveis = g.Sum(f => f.Descartaveis) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalComissaoAGanhar = g.Sum(f => f.Comissao.ComissaoParaPagar) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalPercentualComissao = g.Sum(f => f.Comissao.ValorComissao)
                 });

            var retorno = (from t in queryRelatorioAgrupadosPorTipoEData.ToList()
                           group t by
                               new
                               {
                                   t.ExibeValoresDescartaveis,
                                   t.DescontarDescartaveisDoValorPago,
                                   t.ConsiderarDescontoOperadoraNaComissao,
                                   t.Key.Date
                               }
                               into g
                           select new RelatorioHorarioTransacao
                           {
                               DataHoraInicioHorario = g.Key.Date,
                               DescontarDescartaveisDoValorPago = g.Key.DescontarDescartaveisDoValorPago,
                               ConsiderarDescontoOperadoraNaComissao = g.Key.ConsiderarDescontoOperadoraNaComissao,
                               TotalServicos = g.Sum(f => f.TotalServicos),
                               TotalDescontos = g.Sum(f => f.TotalDescontos),
                               TotalDescontoOperadoras = g.Sum(f => f.TotalDescontoOperadoras),
                               TotalDescartaveis = g.Sum(f => f.TotalDescartaveis),
                               TotalComissao = g.Sum(f => f.TotalComissaoAGanhar),
                               TotalPercentualComissao = g.Sum(f => f.TotalPercentualComissao),
                               IdProfissional = parametros.IdProfissional,
                               IdPessoa = idPessoa
                           });

            retorno = retorno.OrderBy(p => p.DataHoraInicioHorario).ThenBy(p => p.DataHoraTransacao);
            return retorno.ToList();
        }

        private List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatasAtendimentoAgrupadosPorMesAno(
            ParametrosFiltrosRelatorio parametros)
        {
            var dados = Listar(parametros);

            var queryRelatorioAgrupadosPorMesAno =
                from f in dados
                group f by
                    new
                    {
                        f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                        f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                        f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao,
                        f.DataHoraInicioHorario.Value.Month,
                        f.DataHoraInicioHorario.Value.Year,
                        IdTipoTransacao = f.Transacao.TipoTransacao.Id
                    }
                    into g
                select new
                {
                    g.Key,
                    ExibeValoresDescartaveis = g.Key.TrabalhaComDescartaveis,
                    g.Key.DescontarDescartaveisDoValorPago,
                    g.Key.ConsiderarDescontoOperadoraNaComissao,
                    TotalServicos = g.Sum(f => f.Preco) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalDescontos = g.Sum(f => f.Desconto) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalDescontoOperadoras =
                        g.Sum(f => f.Transacao.TotalDescontoOperadoras) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalDescartaveis = g.Sum(f => f.Descartaveis) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalComissaoAGanhar =
                        g.Sum(f => f.Comissao.ComissaoParaPagar) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalPercentualComissao = g.Sum(f => f.Comissao.ValorComissao)
                };

            var retorno = (from t in queryRelatorioAgrupadosPorMesAno.ToList()
                           group t by
                               new
                               {
                                   t.ExibeValoresDescartaveis,
                                   t.DescontarDescartaveisDoValorPago,
                                   t.ConsiderarDescontoOperadoraNaComissao,
                                   t.Key.Month,
                                   t.Key.Year
                               }
                               into g
                           select new RelatorioHorarioTransacao
                           {
                               DescontarDescartaveisDoValorPago = g.Key.DescontarDescartaveisDoValorPago,
                               ConsiderarDescontoOperadoraNaComissao = g.Key.ConsiderarDescontoOperadoraNaComissao,
                               DataHoraInicioHorario = new DateTime(g.Key.Year, g.Key.Month, 1),
                               TotalServicos = g.Sum(f => f.TotalServicos),
                               TotalDescontos = g.Sum(f => f.TotalDescontos),
                               TotalDescontoOperadoras = g.Sum(f => f.TotalDescontoOperadoras),
                               TotalDescartaveis = g.Sum(f => f.TotalDescartaveis),
                               TotalComissao = g.Sum(f => f.TotalComissaoAGanhar),
                               TotalPercentualComissao = g.Sum(f => f.TotalPercentualComissao),
                               IdProfissional = parametros.IdProfissional
                           });

            retorno = parametros.TipoData == TipoDataRelatorio.DataTransacao
                ? retorno.OrderBy(p => p.DataHoraTransacao).ThenBy(p => p.DataHoraInicioHorario)
                : retorno.OrderBy(p => p.DataHoraInicioHorario).ThenBy(p => p.DataHoraTransacao);
            return retorno.ToList();
        }

        private List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatasMovimentacaoAgrupadosPorMesAno
            (ParametrosFiltrosRelatorio parametros)
        {
            var dados = Listar(parametros);

            var queryRelatorioAgrupadosPorTipoEData =
                from f in dados
                group f by
                    new
                    {
                        f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                        f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                        f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao,
                        f.Transacao.DataHora.Month,
                        f.Transacao.DataHora.Year,
                        IdTipoTransacao = f.Transacao.TipoTransacao.Id
                    }
                    into g
                select new
                {
                    g.Key,
                    ExibeValoresDescartaveis = g.Key.TrabalhaComDescartaveis,
                    g.Key.DescontarDescartaveisDoValorPago,
                    g.Key.ConsiderarDescontoOperadoraNaComissao,
                    TotalServicos = g.Sum(f => f.Preco) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalDescontos = g.Sum(f => f.Desconto) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalDescontoOperadoras =
                        g.Sum(f => f.Transacao.TotalDescontoOperadoras) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalDescartaveis = g.Sum(f => f.Descartaveis) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalComissaoAGanhar =
                        g.Sum(f => f.Comissao.ComissaoParaPagar) * (g.Key.IdTipoTransacao == 1 ? 1 : -1),
                    TotalPercentualComissao = g.Sum(f => f.Comissao.ValorComissao)
                };

            var retorno = (from t in queryRelatorioAgrupadosPorTipoEData.ToList()
                           group t by
                               new
                               {
                                   t.ExibeValoresDescartaveis,
                                   t.DescontarDescartaveisDoValorPago,
                                   t.ConsiderarDescontoOperadoraNaComissao,
                                   t.Key.Month,
                                   t.Key.Year
                               }
                               into g
                           select new RelatorioHorarioTransacao
                           {
                               DescontarDescartaveisDoValorPago = g.Key.DescontarDescartaveisDoValorPago,
                               ConsiderarDescontoOperadoraNaComissao = g.Key.ConsiderarDescontoOperadoraNaComissao,
                               DataHoraInicioHorario = new DateTime(g.Key.Year, g.Key.Month, 1),
                               TotalServicos = g.Sum(f => f.TotalServicos),
                               TotalDescontos = g.Sum(f => f.TotalDescontos),
                               TotalDescontoOperadoras = g.Sum(f => f.TotalDescontoOperadoras),
                               TotalDescartaveis = g.Sum(f => f.TotalDescartaveis),
                               TotalComissao = g.Sum(f => f.TotalComissaoAGanhar),
                               TotalPercentualComissao = g.Sum(f => f.TotalPercentualComissao),
                               IdProfissional = parametros.IdProfissional
                           });

            retorno = parametros.TipoData == TipoDataRelatorio.DataTransacao
                ? retorno.OrderBy(p => p.DataHoraTransacao).ThenBy(p => p.DataHoraInicioHorario)
                : retorno.OrderBy(p => p.DataHoraInicioHorario).ThenBy(p => p.DataHoraTransacao);

            return retorno.ToList();
        }

        private List<RelatorioHorarioTransacao> ObterRelatorioHorarioTransacoesEntreDatasTransacaoAgrupadosPorDia(
            ParametrosFiltrosRelatorio parametros)
        {
            var dados = Listar(parametros);

            var queryRelatorioAgrupadosPorTipoEData =
                (from f in dados
                 group f by new
                 {
                     f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.TrabalhaComDescartaveis,
                     f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.DescontarDescartaveisDoValorPago,
                     f.Horario.Estabelecimento.EstabelecimentoConfiguracaoGeral.ConsiderarDescontoOperadoraNaComissao,
                     Codigo = f.Transacao.TipoTransacao.Id,
                     DataHora = f.Transacao.DataHora.Date,
                     f.Horario.Profissional.IdProfissional,
                     f.Horario.Profissional.PessoaFisica.IdPessoa
                 }
                     into g
                 select new
                 {
                     g.Key,
                     ExibeValoresDescartaveis = g.Key.TrabalhaComDescartaveis,
                     g.Key.DescontarDescartaveisDoValorPago,
                     g.Key.ConsiderarDescontoOperadoraNaComissao,
                     TotalServicos = g.Sum(f => f.Preco) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalDescontos = g.Sum(f => f.Desconto) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalDescontoOperadoras =
                         g.Sum(f => f.Transacao.TotalDescontoOperadoras) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalDescartaveis = g.Sum(f => f.Descartaveis) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalComissaoAGanhar = g.Sum(f => f.Comissao.ComissaoParaPagar) * (g.Key.Codigo == 1 ? 1 : -1),
                     TotalPercentualComissao = g.Sum(f => f.Comissao.ValorComissao)
                 });

            var retorno = (from t in queryRelatorioAgrupadosPorTipoEData.ToList()
                           group t by
                               new
                               {
                                   t.ExibeValoresDescartaveis,
                                   t.DescontarDescartaveisDoValorPago,
                                   t.ConsiderarDescontoOperadoraNaComissao,
                                   t.Key.DataHora,
                                   t.Key.IdPessoa,
                                   t.Key.IdProfissional
                               }
                               into g
                           select new RelatorioHorarioTransacao
                           {
                               DataHoraTransacao = g.Key.DataHora,
                               DescontarDescartaveisDoValorPago = g.Key.DescontarDescartaveisDoValorPago,
                               ConsiderarDescontoOperadoraNaComissao = g.Key.ConsiderarDescontoOperadoraNaComissao,
                               TotalServicos = g.Sum(f => f.TotalServicos),
                               TotalDescontos = g.Sum(f => f.TotalDescontos),
                               TotalDescontoOperadoras = g.Sum(f => f.TotalDescontoOperadoras),
                               TotalDescartaveis = g.Sum(f => f.TotalDescartaveis),
                               TotalComissao = g.Sum(f => f.TotalComissaoAGanhar),
                               TotalPercentualComissao = g.Sum(f => f.TotalPercentualComissao),
                               IdProfissional = g.Key.IdProfissional,
                               IdPessoa = g.Key.IdPessoa
                           });

            retorno = retorno.OrderBy(p => p.DataHoraTransacao).ThenBy(p => p.DataHoraInicioHorario);
            return retorno.ToList();
        }

        public IQueryable<HorarioTransacao> ObterHorariosTransacaoSemRegistroDeComissaoParaRecalculo(List<int> idsProfissional, int idEstabelecimento, DateTime? dataInicioRecalculo)
        {
            var query = Queryable().Where(p => p.Transacao.DataHora >= dataInicioRecalculo &&
               p.Horario.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
               (idsProfissional.Contains(p.Horario.Profissional.IdProfissional) ||
               (p.Horario.EstabelecimentoProfissionalAssistente != null && idsProfissional.Contains(p.Horario.EstabelecimentoProfissionalAssistente.Profissional.IdProfissional))) &&
               p.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento &&
               p.Transacao.TransacaoQueEstounouEsta == null &&
               p.Comissao == null &&
               p.ComissaoAssistente == null);

            return query;
        }

        public IQueryable<HorarioTransacao> ObterNaoEstornadosPorDataDaTransacao(DateTime dataDaTransacao)
        {
            return Queryable()
            .Where(ht => ht.Transacao.DataHora >= dataDaTransacao
                        && ht.Transacao.DataHora < dataDaTransacao.AddDays(1)
                        && ht.Transacao.TransacaoQueEstounouEsta == null
                        && ht.Transacao.TipoTransacao.Id == (int)TipoTransacaoEnum.Pagamento
                        && !(ht.Transacao.FormasPagamento.Any(tfp => tfp.FormaPagamento == FormaPagamentoEnum.DescontoDeProfissional))
                        && ht.Ativo);
        }

        public IQueryable<HorarioTransacao> ObterHorariosTransacaoPorIdTransacao(int idTransacao)
        {
            var query = Queryable().Where(p => p.Transacao.Id == idTransacao);

            return query;
        }

        public decimal ObterValorTotalConsumoDePacoteComConfigConsumo(int idTransacao)
        {
            return Queryable().Where(f => f.Transacao.Id == idTransacao && f.ItemPacoteCliente != null && f.ItemPacoteCliente.PacoteCliente.ConfiguracaoNF == Pacotes.Enums.TipoNFPacoteEnum.Consumo)
                .Select(f => f.ItemPacoteCliente.ValorUnitarioFiscal > 0 ? f.ItemPacoteCliente.ValorUnitarioFiscal : f.ItemPacoteCliente.ValorUnitario ).ToList()
                .Sum();
        }

        public int ObterQuantidadeHorariosTransacaoPorTransacao(int idTransacao)
        {
            return StatelessQueryable().Where(horarioTransacao => horarioTransacao.Transacao.Id == idTransacao)
                .Select(horarioTransacao => horarioTransacao.Codigo).Count();
        }
    }
}