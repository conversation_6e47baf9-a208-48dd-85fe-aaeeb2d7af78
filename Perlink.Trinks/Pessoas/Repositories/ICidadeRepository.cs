﻿using System;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface ICidadeRepository
    {

        List<Cidade> ListarCidadesContendoEstabelecimentos(int? codigoUF);

        bool ExisteImplementacaoDeNFSeParaCidade(string codigoIBGE);

        long ObterCodigoIBGEDoClienteOuEstabelecimento(UF unidadeFederativaDoCliente, String nomeDaCidadeEmTexto,
                                                       Cidade cidadeDoEstabelecimento);

        Cidade Obter(string nomeCidade, string uf);

        Cidade ObterPorCodigoIBGE(string codigoIBGE);
        IEnumerable<KeyValuePair<string, string>> ObterKeyValueCidadeCodigIbge(string filtro);

    }
}