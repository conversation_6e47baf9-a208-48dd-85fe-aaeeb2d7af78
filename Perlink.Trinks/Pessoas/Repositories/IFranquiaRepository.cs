﻿using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IFranquiaRepository
    {
        Franquia ObterFranquiaPorIdentificadorDownloadApp(string identificadorDownloadApp);
        IList<KeyValuePair<int, string>> ObterListaDeIdNomeDasFranquiasAtivas();

        bool ExisteFranquiaAtivaComMesmoNome(string nome);

        bool ExisteFranquiaComMesmoSlug(string slug);

        Franquia ObterPorSlug(string slug);

        bool ObterValorConfiguracaoOcultaAgendamentosPerfilProfissional2(int idFranquia);

        List<Franquia> ObterFranquiasPorIds(List<int> idsFranquias);
    }
}