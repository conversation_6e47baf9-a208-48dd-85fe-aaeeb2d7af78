﻿using Perlink.Trinks.Pessoas.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Repositories
{
    public partial interface IFotoDeClienteEstabelecimentoRepository
    {
        FotoDeClienteEstabelecimento Obter(int idFoto);
        IList<FotoDeClienteEstabelecimento> ListarFotosPeloClienteEstabelecimento(int idClienteEstabelecimento);
        List<FotoClienteEstabelecimentoDTO> ListarFotoDoClienteEstabelecimentoDTO(int idClienteEstabelecimento);
        FotoDeClienteEstabelecimento ObterFotoMaisRecenteDoCliente(int idClienteEstabelecimento);
    }
}
