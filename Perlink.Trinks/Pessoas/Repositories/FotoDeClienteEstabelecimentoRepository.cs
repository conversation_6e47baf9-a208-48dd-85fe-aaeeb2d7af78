﻿using Perlink.Trinks.Fotos.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class FotoDeClienteEstabelecimentoRepository : IFotoDeClienteEstabelecimentoRepository
    {
        public FotoDeClienteEstabelecimento Obter(int idFoto)
        {
            return Queryable().FirstOrDefault(f => f.Id == idFoto);
        }
        public IList<FotoDeClienteEstabelecimento> ListarFotosPeloClienteEstabelecimento(int idClienteEstabelecimento)
        {
            var retorno = Queryable().Where(fc => fc.ClienteEstabelecimento.Codigo == idClienteEstabelecimento).ToList();

            return retorno;
        }

        public FotoDeClienteEstabelecimento ObterFotoMaisRecenteDoCliente(int idClienteEstabelecimento)
        {
            var query = ListarFotosPeloClienteEstabelecimento(idClienteEstabelecimento).OrderByDescending(p => p.Foto.DataDoCadastro);
            return query.FirstOrDefault();
        }

        public List<FotoClienteEstabelecimentoDTO> ListarFotoDoClienteEstabelecimentoDTO(int idClienteEstabelecimento)
        {
            var listaFotoDoClienteEstabelecimentoDTO = new List<FotoClienteEstabelecimentoDTO>();
            var listaFotosCliestabelecimento = Queryable().Where(p => p.ClienteEstabelecimento.Codigo == idClienteEstabelecimento).OrderByDescending(p => p.Foto.DataDoCadastro);

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            if (Domain.Pessoas.FotoPessoaRepository.PessoaPossuiFoto(clienteEstabelecimento.Cliente.PessoaFisica))
            {
                listaFotoDoClienteEstabelecimentoDTO.Add(ObterFotoClienteEstabelecimentoDTO(clienteEstabelecimento));
            }

            foreach (var fotoClienteEstabelecimento in listaFotosCliestabelecimento)
            {
                listaFotoDoClienteEstabelecimentoDTO.Add(ObterFotoClienteEstabelecimentoDTO(fotoClienteEstabelecimento));
            }

            return listaFotoDoClienteEstabelecimentoDTO;
        }

        private FotoClienteEstabelecimentoDTO ObterFotoClienteEstabelecimentoDTO(ClienteEstabelecimento clienteEstabelecimento)
        {
            var fotoPrincipalPerfilClienteEstabelecimento = clienteEstabelecimento.Cliente.PessoaFisica.FotoPrincipal;

            var fotoClienteEstabelecimentoDTO = new FotoClienteEstabelecimentoDTO()
            {
                IdFotoPessoa = fotoPrincipalPerfilClienteEstabelecimento.Codigo.Value,
                IdClienteEstabelecimento = clienteEstabelecimento.Codigo,
                IdCliente = clienteEstabelecimento.Cliente.IdCliente,
                UrlFoto = fotoPrincipalPerfilClienteEstabelecimento.ObterCaminhoWeb(DimemsoesFotosEnum.Original),
                UrlFotoThumb = fotoPrincipalPerfilClienteEstabelecimento.ObterCaminhoWeb(DimemsoesFotosEnum.Dim163x122),
                EhFotoPrincipal = clienteEstabelecimento.FotoDoPerfil == null ? true : false,
                EhEditavel = false,
                EhFotoDePerfilDeUmClienteWeb = true,
                Legenda = fotoPrincipalPerfilClienteEstabelecimento.Legenda,
                PossuiLegenda = !string.IsNullOrEmpty(fotoPrincipalPerfilClienteEstabelecimento.Legenda)
            };

            return fotoClienteEstabelecimentoDTO;
        }

        private FotoClienteEstabelecimentoDTO ObterFotoClienteEstabelecimentoDTO(FotoDeClienteEstabelecimento fotoClienteEstabelecimento)
        {
            var fotoDoClienteEstabelecimentoDTO = new FotoClienteEstabelecimentoDTO()
            {
                IdFoto = fotoClienteEstabelecimento.Foto.Id,
                IdFotoClienteEstabelecimento = fotoClienteEstabelecimento.Id,
                IdClienteEstabelecimento = fotoClienteEstabelecimento.ClienteEstabelecimento.Codigo,
                IdCliente = fotoClienteEstabelecimento.ClienteEstabelecimento.Cliente.IdCliente,
                UrlFoto = Domain.Conteudo.ConteudoFotoClienteEstabelecimentoService.ObterCaminhoWeb(fotoClienteEstabelecimento, DimemsoesFotosEnum.Original),
                UrlFotoThumb = Domain.Conteudo.ConteudoFotoClienteEstabelecimentoService.ObterCaminhoWeb(fotoClienteEstabelecimento, DimemsoesFotosEnum.Dim163x122),
                Legenda = fotoClienteEstabelecimento.Foto.Legenda,
                DataCadastro = fotoClienteEstabelecimento.Foto.DataDoCadastro,
                EhFotoPrincipal = fotoClienteEstabelecimento.ClienteEstabelecimento.FotoDoPerfil != null && fotoClienteEstabelecimento.ClienteEstabelecimento.FotoDoPerfil.Id == fotoClienteEstabelecimento.Id,
                PossuiLegenda = !String.IsNullOrEmpty(fotoClienteEstabelecimento.Foto.Legenda),
                EhEditavel = true,
                EhFotoDePerfilDeUmClienteWeb = false
            };

            return fotoDoClienteEstabelecimentoDTO;
        }
    }
}