﻿using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Fidelidade.DTO;
using Perlink.Trinks.Fidelidade.Enums;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IServicoEstabelecimentoRepository
    {

        bool EstabelecimentoPossuiPeloMenosDoisServicosComPreco(int idEstabelecimento);

        bool EstabelecimentoPossuiPeloMenosDoisServicosComPrecoVinculadoAhProfissionalAtivo(int idEstabelecimento);
        bool EstabelecimentoPossuiPeloMenosUmServicosComPrecoVinculadoAhProfissionalAtivo(int idEstabelecimento);

        bool ExisteOutroServicoEstabelecimentoNoEstabelecimentoExcetoOsServicos(List<int> idsServicoEstabelecimento,
            int idEstabelecimento);

        bool ExisteServicoEstabelecimentoAtivo(int idServicoEstabelecimento);

        bool ExisteServicoEstabelecimentoJaAssociadoAoServicoPadraoNoEstabelecimento(int idEstabelecimento,
            int idServicoPadrao, int idServicoEstabelecimento);

        IQueryable<KeyValuePair<int, string>> KeyValueServicoEstabelecimentosAtivos(int codigoEstabelecimento);

        IList<ServicoEstabelecimento> ListarAtivosAssociadosAoEstabelecimentoEExibemPreco(Int32 idEstabelecimento);

        IQueryable<ServicoEstabelecimento> ListarAtivosAssociadosAoEstabelecimentoEExibemPrecoEComProfissionalAtivo(
            Int32 idEstabelecimento, bool? filtraComExibicaoDePrecos = null, int? idProfissionalAssociado = 0, bool servicoIndisponivelParaClientes = false);

        IList<ServicoEstabelecimento> ListarAtivosAssociadosAoEstabelecimentoENaoExibemPreco(Int32 idEstabelecimento);

        IList<ServicoEstabelecimento> ListarAtivosAssociadosAProfissionaisPorCategoriaEstabelecimento(
            Int32 codigoServicoCategoriaEstabelecimento);

        List<ServicoEstabelecimento> ListarAtivosPorEstabelecimento(Int32 codigoEstabelecimento,
            bool trazerApenasOsComSituacaoTributaria = false);

        int RetornarQuantidadeDeServicosAtivosPorEstabelecimento(Int32 codigoEstabelecimento,
            bool trazerApenasOsComSituacaoTributaria = false);

        List<ServicoEstabelecimento> ListarTodosPorEstabelecimento(Int32 codigoEstabelecimento,
            bool trazerApenasOsComSituacaoTributaria = false);

        IQueryable<ServicoEstabelecimento> ListarAtivosPorEstabelecimentoAssociadosAProfissional(Int32 idEstabelecimento);

        List<KeyValuePair<int, string>> ListarKeyValueAtivosPorEstabelecimentoAssociadosAProfissional(int idEstabelecimento);

        IList<ServicoEstabelecimento> ListarAtivosPorPessoaProfissionalEstabelecimento(Int32 idPessoa,
            Int32 codigoEstabelecimento);

        List<DadosBasicosDeServicoEstabelecimentoDTO> ListarDadosBasicosDeServicosAtivosPorProfissionalEstabelecimento(int? codigoProfissional, int codigoEstabelecimento);

        IList<ServicoEstabelecimento> ListarAtivosPorProfissionalEstabelecimento(int? codigoProfissional,
            Int32 codigoEstabelecimento);

        List<KeyValuePair<int, string>> ListarAtivosPorProfissionalEstabelecimentoKeyValue(Int32 idEstabelecimento,
            Int32 idProfissionalEstabelecimento);

        List<KeyValuePair<int, string>> ListarKeyValue(int idEstabelecimento, bool somenteAtivos = true,
            List<int> idsCategorias = null);

        List<KeyValuePair<int, string>> ListarServicosAtivosComValorFixoMaiorQue1(int idEstabelecimento);

        List<KeyValuePair<string, string>> ListarServicosPorProfissionalKeyValue(Estabelecimento estabelecimento, int idProfissional, string nomeFiltro, bool listarPorCodigoInterno = false);

        List<ServicoEstabelecimento> ListarPorEstabelecimento(int idEstabelecimento);

        List<ServicoEstabelecimento> ListarServicosEstabelecimento(ParametrosFiltroServicosEstabelecimento filtro);

        ResultadoPaginado<ServicoEstabelecimento> ListarServicosEstabelecimentoPaginado(
            ParametrosFiltroServicosEstabelecimento filtro);

        List<ServicoEstabelecimento> ListarServicosPorServicoCategoriaEstabelecimento(
            int idServicoCategoriaEstabelecimento);

        List<ServicoEstabelecimento> ListarServicosQuePossuamNomenclaturaParaNotaFiscalDoConsumidorDefinida(
            int idEstabelecimento);

        Int32 ObterIdServicoPorCodigoInterno(String codigoInterno, Int32 idEstabelecimento);

        ServicoEstabelecimento ObterPorCodigoInterno(String codigoInterno, int idEstabelecimento);

        ServicoEstabelecimento ObterPorServicoPorEstabelecimentoEServicoPadrao(Int32 codigoEstabelecimento,
            Int32 codigoServicoPadrao);

        ServicoEstabelecimento ObterServicoDoEstabelecimentoPeloNome(Int32 codigoEstabelecimento,
            String nomeServicoEstabelecimento, Int32 codigoServicoEstabelecimento = 0);

        ServicoEstabelecimento ObterServicoEstabelecimento(Int32 codigoServico, Int32 codigoEstabelecimento);

        IQueryable<ServicoEstabelecimento> ObterServicoEstabelecimentosAtivos(int codigoEstabelecimento);

        bool PossuiProfissionalAssociado(int idServicoEstabelecimento);

        Int32 QuantidadeDeServicosEstabelecimentoNoEstabelecimento(int idEstabelecimento);

        void SaveOrUpdate(ServicoEstabelecimento entity);

        Boolean VerificarSeServicoJaEstaAssociadoAoEstabelecimento(Int32 codigoEstabelecimento, Int32 codigoServico);

        Boolean TemServicoComPromocaoNoMomento(int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarKeyValueParaFiltroDeRankingDeServicos(int idEstabelecimento, List<int> idsCategorias);

        List<ServicoEstabelecimento> ListarServicosComResgateDePontosAtivo(int idEstabelecimento);

        List<ItemDoProgramaDTO> ListarItensDeServicoDoPrograma(int idEstabelecimento, string textoDoFiltroDeItens, TipoConsultaItensDoProgramaEnum tipoConsulta);

        List<ServicoEstabelecimento> ListarServicosComMenorPontoNecessarioParaResgaste(int idEstabelecimento);

        bool EstabelecimentoPossuiServicosComResgateNoProgramaDeFidelidade(int idEstabelecimento);

        IQueryable<ServicoEstabelecimento> ObterQueryableDeServicosVisiveisNoHotSite(int? idEstabelecimento = null, int? idProfissionalAssociado = null, int? idEstabelecimentoProfissionalAssociado = null, int? idCategoriaPortal = null, bool servicoIndisponivelParaClientes = false, int? idFranquia = null);

        IQueryable<ServicoEstabelecimento> ListarOsDisponiveisNoGoogleReserve();

        int ObterIdEstabelecimentoDoServico(int idServicoEstabelecimento);

        int ObterDuracaoDoServico(int idServicoEstabelecimento);

        ServicoEstabelecimento ObterPorId(int idServicoEstabelecimento);

        ServicoEstabelecimento ObterServicoEstabelecimentoAtivoPorCodigoInterno(string codigoInterno, int idEstabelecimento);

        IList<PagamentosAntecipados.DTO.ServicoHabilitadoParaPagamentoDTO> ListarServicosPagamentoOnlineSegundoRegra(int idEstabelecimento, decimal valorMaximo);

        List<ServicoEstabelecimento> ListarServicosEstabelecimentoMaisUsadosPeloProfissionalDesde(int idEstabelecimento, int idProfissional, DateTime dataDesde);

        int TotalDeServicosDoEstabelecimento(int idEstabelecimento);

        ServicoEstabelecimento ObterServicoEstabelecimentoSemVerificarFlagAtivo(Int32 codigoServico, Int32 codigoEstabelecimento);

        DateTime? ObterDataPrimeiroServicoNaoPadraoCadastrado(int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarKeyValueComIdENomeDosServicosAtivos(int idEstabelecimento);

        decimal ObterPrecoPorId(int idServico, int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarKeyValue(int idEstabelecimento, bool ativo, string conteudoFiltro);

        ServicoEstabelecimento ObterBaseadoNoModelo(int idEstabelecimento, int idServicoEstabelecimentoDoModelo);

        int ObterCodigoServicoCategoriaEstabelecimento(int idServicoEstabelecimento);
        
        IQueryable<ServicoEstabelecimento> QueryableAtivosComPrecoFixo(int idEstabelecimento);
    }
}
