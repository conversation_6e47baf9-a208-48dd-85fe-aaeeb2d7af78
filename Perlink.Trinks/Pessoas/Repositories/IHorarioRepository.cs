﻿using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.DTO;
using Perlink.Trinks.Estabelecimentos.DTO;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.Filtros;
using Perlink.Trinks.Promocoes;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IHorarioRepository
    {

        IQueryable<Horario> FiltrarParaPATMobile(FiltroPATMobile filtro);

        IQueryable<Horario> QueryableHorariosParaComboMovimentacaoEstoque(int idEstabelecimento, DateTime dataMovimentacao, List<StatusHorario> listaStatusExcecao);

        bool ClienteEstaEmDebito(int idEstabelecimento, int idCliente, bool incluiHoje = false);

        IList<KeyValuePair<StatusHorarioEnum, int>> CountAgendamentosAtivosPorStatus();

        DateTime? ObterUltimoHorarioDoClienteComProfissional(int idClienteEstabelecimento, int? idProfissional, DateTime data);

        //IList<KeyValuePair<int, int>> CountAgendamentosBalcaoPorEstabelecimento();

        Int32 ObterQuantidadeAgendamentosOnlineRealizadosNoEstabelecimento(Int32 idEstabelecimento);

        DateTime? ObterDataCriacaoDoUltimoHorarioOnlineAgendado(Int32 idEstabelecimento);

        DateTime? ObterDataCriacaoDoUltimoHorarioBalcaoAgendado(Int32 idEstabelecimento);

        int CountAgendamentosCriadosNoDia(List<HorarioOrigemEnum> listaHorarioOrigemEnum, DateTime dia);

        Int32 CountAgendamentosFuturos(Int32 codigoClienteEstabelecimento);

        void TransferirHorariosParaOutroProfissional(int idProfissionalOrigem, int idProfissionalDestino);

        int CountAgendamentosInativos();

        int CountAgendamentosNaoBalcaoCriadosNoDia(int idPessoa, DateTime dia);

        bool ExisteAgendamentoFuturoNaoPagosAPartirDe(DateTime aPartirDe, int idEstabelecimento,
            int? idServicoEstabelecimento = null);

        bool ExisteConflitoDeHorariosParaProfissional(Horario horario);

        Boolean ExistemHorarioDeOrigemBalcaoParaCliente(int idPessoa);

        List<Horario> Filtrar(ParametrosBuscaHorario parametros, bool ordenar = true, bool fetchEmPreVendas = false, bool fetchEmHorariosTransacao = false);

        List<HorarioAgendaDTO> FiltrarDTO(ParametrosBuscaHorario parametros, bool ordenar = true, bool fetchEmPreVendas = false, bool fetchEmHorariosTransacao = false);

        List<HorarioAgendaDTO> FiltrarAgendaDTO(ParametrosBuscaHorario parametros, bool ordenar = true);

        List<Horario> FiltrarPorClienteMobile(int idCliente, int idFranquia = 0);

        IList<Horario> FiltrarPorIntervaloData(int codigoProfissional, int codigoEstabelecimento, IntervaloData intervalo, bool incluiCancelados = false);

        IQueryable<Horario> FiltroDeHorarios(ParametrosBuscaHorario parametros, bool ordenar = true, bool stateless = false);

        IList<Horario> HorariosConflitandoParaProfissional(Horario horario);

        IList<Horario> HorariosConflitandoParaProfissionalSemEvict(Horario horario);

        IList<Horario> HorariosConflitandoParaProfissional(int idProfissional, DateTime DataInicio, DateTime DataFim,
            int idEstabelecimento, int? idHorario = null);

        IList<Horario> HorariosConflitandoParaProfissionalDeClientesDiferentes(Horario horario);

        List<Horario> ListarAgendamentosFuturosNaoPagosAPartirDe(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32? idServicoEstabelecimento = null);

        decimal ObterTotalDebitosDeClienteAnterioresAData(int idCliente, int idEstabelecimento, DateTime dataLimite);

        IList<ClientesEmDebito> ListarClientesEmDebito(ParametrosFiltroCliente filtro, bool incluiHoje = false);

        IList<ClientesEmDebito> ListarClientesEmDebitoDias(int idClienteEstabelecimento, DateTime? dataInicio, DateTime? dataFim, bool incluiHoje = false, bool incluirComandas = true);

        IQueryable<Horario> ListarNoDiaComProfissionalComClienteComEmail(DateTime Data);

        IQueryable<Horario> ListarHorarios(ParametrosFiltroHistoricoCliente parametros);

        IQueryable<Horario> ListarHorariosDoClienteEmDebito(int idEstabelecimento, int idCliente,
            bool incluiHoje = false);

        IQueryable<Horario> ListarHorariosDoClienteNoStatus(int idClienteEstabelecimento, DateTime dataAgendamento,
            int? idProfissional = null, bool somenteNaoPagos = false, params StatusHorario[] status);

        IQueryable<Horario> ListarHorariosEmDebito(int idEstabelecimento, bool incluiHoje = false);

        IQueryable<Horario> ObterHorariosDoDiaEmQuePodeSerAssistente(DateTime dateTime, Estabelecimento estabelecimento);

        IQueryable<Horario> QueryHorariosEmDebitoParaRelatorioClientesEmDebito(ParametrosFiltroCliente filtro, bool incluiHoje = false, bool incluirComandas = true);

        ResultadoPaginado<Horario> ListarHorariosPaginados(ParametrosFiltroHistoricoCliente parametros);

        ResultadoPaginado<Horario> ListarHorariosPaginadosPorDataDecrescente(ParametrosFiltroHistoricoCliente parametros);

        IQueryable<HistoricoNaRedeItemDTO> ListarHistoricoNaRedePaginadosPorDataDecrescente(int idFranquia, string cpf, string email, DateTime dataInicio, DateTime dataFim);

        List<Horario> ListarHorariosPorFiltro(ParametrosFiltroConsultarAgendamentos parametros);

        List<HorarioDTO> ListarHorariosPorFiltroDTO(ParametrosFiltroConsultarAgendamentos parametros);

        ResultadoPaginado<Horario> ListarHorariosPorFiltroPaginado(ParametrosFiltroConsultarAgendamentos parametros,
            Profissional profissionalAutenticado = null, bool origemAreaProfissional = false);

        List<Horario> ListarPendentesConfirmacao(FiltroConsultaNotificacao filtro);

        int ObterNumeroPendentesConfirmacao(int idEstabelecimento, DateTime dataInicio);

        IList<Horario> ObterAgendamentosNaoPagosDoDiaPorCliente(DateTime data, Estabelecimento estabelecimento, Cliente cliente, bool paraCancelamento);

        IList<Horario> ObterAgendamentosDoDiaPorCliente(DateTime data, Estabelecimento estabelecimento, Cliente cliente, bool paraCancelamento);

        IList<Horario> ObterAgendamentosDoDiaPorClienteParaComanda(DateTime data, Estabelecimento estabelecimento, Cliente cliente);

        List<Horario> ObterAgendamentosFuturosConfirmadosEhOndeClienteEstabelecimentoRecebaNotificacao(Estabelecimento estabelecimento);

        List<Horario> ObterAgendamentosFuturosConfirmadosEhOndeClienteEstabelecimentoRecebaNotificacao(ClienteEstabelecimento clienteEstabelecimento);

        List<Horario> ObterComNotificacoesProgramadas(Estabelecimento estabelecimento);

        DateTime? ObterDataPrimeiroAgendamentoClienteEstabelecimento(Int32 idClienteEstabelecimento, DateTime? aPartirDe);

        DateTime? ObterDataDoUltimoServicoAgendadoAteDataAtual(Int32 idClienteEstabelecimento);

        IQueryable<Estabelecimento> ObterEstabelecimentosRecentementeAgendadosPorUmCliente(Int32 idCliente);

        IList<Horario> ObterHorariosParaRelatorio(ParametrosFiltrosRelatorio parametros);

        IQueryable<Horario> ObterNotificacoesPendentesDeHoje(ClienteEstabelecimento clienteEstabelecimento);

        IQueryable<Horario> ObterNotificacoesPendentesDoDiaSeguinte(ClienteEstabelecimento clienteEstabelecimento);

        IQueryable<Horario> ObterPendentesDeNotificacao(bool stateless = false);

        int ObterQuantidadeAgendamentosPorEstabelecimentoProfissionalAPartirDe(DateTime aPartirDe,
            Int32 idEstabelecimento, Int32 idProfissional);

        int QuantidadeDeHorariosDoClienteNoStatus(int idClienteEstabelecimento, DateTime dataAgendamento,
            int? idProfissional = null, bool somenteNaoPagos = false, params StatusHorario[] status);

        void SaveOrUpdate(Horario entity);

        List<int> ExisteAgendamentoFuturoNaoPagosAPartirDeParaAlterarParaPrecoPromocional(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32 idServicoEstabelecimento, DateTime? dataInicioVigencia, DateTime? dataFimVigencia, int tipoVigencia, List<PromocaoDiaDaSemana> listaPromocaoDiaDaSemana, decimal precoPadrao, bool temPromocao = false);

        int ExisteAgendamentoFuturoNaoPagosAPartirDeParaAlterarPrecoNaExclusaoDePromocao(DateTime aPartirDe, Int32 idEstabelecimento,
            Int32 idServicoEstabelecimento, DateTime? dataInicioVigencia, DateTime? dataFimVigencia, int tipoVigencia, List<PromocaoDiaDaSemana> listaPromocaoDiaDaSemana);

        int CountEstabelecimentoTotalDeHorariosCriadosAtravesDoSiteDaFranquiaNoDia(HorarioOrigemEnum horarioOrigemEnum, DateTime diaPesquisa);

        List<Horario> ListarTodosDoDia(int idEstabelecimento, DateTime data);

        bool HorarioPodeSerAssociadoEmUmaComanda(int idHorario, int idEstabelecimento, out int? numeroComandaAssociar);

        DadosDoHorarioParaTelaDeLembreteDTO ObterDadosDoClientePeloHorario(int idHorario);

        IQueryable<Horario> ObterAgendamentosDoDiaPorIdClienteEIdEstabelecimento(DateTime dateTime, int idEstabelecimento, int idCliente);

        TipoClienteEnum ObterTipoDeClienteDoHorario(int idHorario);

        IQueryable<Horario> ObterQueryHorarioPorId(int idHorario);

        bool HorarioEstaDisponivelParaAssociarAssistente(int idHorario, int idEstabelecimentoProfissional);

        IQueryable<Horario> ObterFiltroDeHorariosQueAssistentePodeParticipar(EstabelecimentoProfissional estabelecimentoProfissional);

        int ObterIdServicoEstabelecimentoDoHorarioPorId(int idHorario);

        IQueryable<Horario> ObterAgendamentosDaData(DateTime dataDoAgendamento);

        int TotalDeHorariosDoEstabelecimento(int idEstabelecimento);

        Horario ObterHorarioParaAssociacaoRapidaDeComanda(DateTime data, ClienteEstabelecimento clienteEstabeleciemnto, Profissional profissional, ServicoEstabelecimento servicoEstabelecimento, int idEstabelecimento, Comanda comanda);

        ServicoDoEstabelecimentoDTO ObterUltimoAgendamentoPagoDoCliente(int idCliente, int? idFranquia);

        Horario ObterPorId(int idHorario);

        List<UltimoServicoRealizadoDTO> ListarUltimosServicosRealizadosNosEstabelecimentos(int idCliente, List<int> idsEstabelecimentos);

        int? ObterIdClienteDoHorario(int idHorario);

        HorarioOrigemEnum ObterOrigemDoHorarioAgendado(int idHorario);

        IQueryable<Horario> Listar(ParametrosBusca parametros);

        DadosDoControleDeAlteracaoDoHorarioDTO ObterDadosVerificadosNoControleDeAlteracaoDeUmHorario(int idHorario);

        List<Horario> ObterPorIds(List<int> idsHorarios);

        bool FoiPagoAntecipadamente(int idHorario);

        List<Horario> ObterApenasHorariosComServicosDisponiveisParaCliente(List<Horario> horarios);

        List<int> ListarIdsDosHorariosQueEstaoFinalizados(int idEstabelecimento, List<int> idsHorariosParaVerificar);
        List<Horario> ListarHorariosQueEstaoFinalizados(List<int> idsHorariosParaVerificar);

        List<Horario> ObterHorarioPorProfissionalEEstabelecimento(int idProfissional, int idEstabelecimento, bool ehServicoComProfissional);

        IntervaloDataList ListarIntervalosOcupadosNoPeriodo(int codigoProfissional, int codigoEstabelecimento, IntervaloData intervalo, bool incluiCancelados = false);

        long QuantidadeDeAgendamentosAtivosPorEstabelecimento(int idEstabelecimento,
           DateTime? dataInicioMinima = null);

        long QuantidadeDeDiasDiferentesDeAgendamentosEstabelecimento(int idEstabelecimento,
              DateTime? dataInicioMinima = null);

        List<int> ObterIdsDosHorariosDisponiveisDoClienteNoDia(int idPessoa, int idEstabelecimento, DateTime data);

        int ObterUltimoIdHorarioPorIdClienteEIdEstabelecimento(int idCliente, int idEstabelecimento);

        bool ExisteAgendamentoPendenteNaoFinalizadoNaData(int idEstabelecimento, DateTime data, int idClienteEstabelecimento);

        List<Horario> AgendamentosAindaPendentesNaData(int idEstabelecimento, DateTime data, int idClienteEstabelecimento);
    }
}