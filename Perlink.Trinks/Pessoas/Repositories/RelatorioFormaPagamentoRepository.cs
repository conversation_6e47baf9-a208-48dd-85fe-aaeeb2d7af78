﻿using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Resources;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class RelatorioFormaPagamentoRepository : IRelatorioFormaPagamentoRepository
    {

        public IList<ItemRelatorioFormaPagamentoNOVO> Filtrar(ParametrosFiltrosRelatorio parametros, bool formaPagamentoDiferenteCreditoCliente = false)
        {
            //var query = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros, formaPagamentoDiferenteCreditoCliente);
            var query = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.Queryable();

            query = AplicarFiltro(parametros, query);

            //var teste = query.ToList();

            var lancamentosQuery = Domain.Despesas.LancamentoRepository.Queryable().Where(l => l.Estabelecimento == parametros.Estabelecimento);
            var lancamentoAntecipacao = Domain.Financeiro.LancamentoDeAntecipacaoRepository.Queryable();

            var itens = from tfpp in query
                        let tfp = tfpp.TransacaoFormaPagamento
                        let t = tfp.Transacao
                        let ant = lancamentoAntecipacao.FirstOrDefault(a => a.TransacaoParcela == tfpp)
                        let fp = tfp.FormaPagamento
                        select
                            new ItemRelatorioFormaPagamentoNOVO
                            {
                                IdTransacaoParcela = tfpp.Id,
                                IdTransacao = t.Id,
                                IdTipoTransacao = t.TipoTransacao.Id,
                                NomeTipoTransacao = t.TipoTransacao.Nome,
                                PagamentoJaEstornado = t.TransacaoQueEstounouEsta != null,
                                DataAtendimento = t.DataReferencia,
                                DataMovimentacao = t.DataHora,
                                DataPrevistaRecebimento = tfpp.DataRecebimento,
                                NomeCliente = t.PessoaQuePagou != null ? t.PessoaQuePagou.NomeCompleto : Textos.ClienteComanda,
                                IdEstabelecimentoCliente = t.PessoaQuePagou != null ? Domain.Pessoas.ClienteEstabelecimentoRepository.ObterIdClienteEstabelecimentoPeloIdPessoa(t.PessoaQuePagou.IdPessoa, parametros.Estabelecimento.IdEstabelecimento) : 0,
                                IdPessoaRecebeu = t.PessoaQueRecebeu.IdPessoa,
                                ValorPago = tfpp.Valor,
                                NumeroParcelas = tfp.NumeroParcelas,
                                PagamentoPOS = fp.TipoPOS != null,
                                TotalLancamentos = lancamentosQuery.Where(l => l.Transacao == t).Sum(l => (decimal?)l.Valor) ?? 0,
                                PercentualDescontoOperadora = tfp.PercentualCobradoPelaOperadora ?? 0,
                                ValorFixoCobradoPelaOperadora = tfp.ValorFixoCobradoPelaOperadora ?? 0,
                                TaxaDescontoAntecipacao = lancamentoAntecipacao.Where(a => a.TransacaoParcela == tfpp).Sum(a => (decimal?)a.TaxaDesconto) ?? 0,
                                ValorBaseCalculoPercentualDesconto = tfp.ValorBaseCalculoPercentualDesconto ?? 0,
                                IdFormaPagamento = fp.Id,
                                NomeFormaPagamento = fp.Nome,
                                IdTipoPagamento = fp.Tipo.Id,
                                NomeTipoPagamento = fp.Tipo.Nome,
                                QuemFechouAConta = t.PessoaQueRealizou,
                                NumeroParcela = tfpp.NumeroParcela,
                                NumeroDoFechamento = t.NumeroDaPreVenda,
                                ComentarioFechamento = t.ComentarioFechamentoConta
                            };

            var itensList = itens.ToList();

            //if (parametros.Estabelecimento.EstabelecimentoPossuiNFC()) {
            //    PreencherInformacoesDeNFC(parametros, itensList);
            //}

            //if (parametros.Estabelecimento.EstabelecimentoConfiguracaoGeral.ComandaEstaAtiva) {
            //    PreencherInformacoesDeComanda(parametros, itensList);
            //}

            if (parametros.IncluirContaFinanceiraNoRelatorio)
            {
                IncluirNomeDaContaFinanceiraNoResultado(itensList, parametros);
            }

            if (itensList.Any())
            {
                PreencherDadosDeAntecipacaoDaParcela(query, itensList);
            }

            return itensList;
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> AplicarFiltro(ParametrosFiltrosRelatorio parametros, IQueryable<TransacaoFormaPagamentoParcela> query)
        {
            query = query.Where(tfpp => tfpp.TransacaoFormaPagamento.Transacao.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa &&
                            tfpp.TransacaoFormaPagamento.Transacao.TotalPago != 0);

            query = query.Where(tfpp => tfpp.TransacaoFormaPagamento.FormaPagamento.Id != (int)FormaPagamentoEnum.DeixarFaltaComoDivida);

            if (parametros.IdTipoFormaPagamento > 0)
                query = query.Where(tfpp => tfpp.TransacaoFormaPagamento.FormaPagamento.Tipo.Id == parametros.IdTipoFormaPagamento);

            if (parametros.IdFormasPagamentoSelecionadas != null && parametros.IdFormasPagamentoSelecionadas.Any())
                query = query.Where(tfpp => parametros.IdFormasPagamentoSelecionadas.Contains(tfpp.TransacaoFormaPagamento.FormaPagamento.Id));

            if (parametros.IdFormaPagamento > 0)
                query = query.Where(f => f.TransacaoFormaPagamento.FormaPagamento.Id == parametros.IdFormaPagamento);

            if (parametros.IdPessoasQueFecharam != null && parametros.IdPessoasQueFecharam.Any())
                query = query.Where(f => parametros.IdPessoasQueFecharam.Contains(f.TransacaoFormaPagamento.Transacao.PessoaQueRealizou.IdPessoa));

            if (!parametros.ExibirEstornos)
                query = query.Where(f => f.TransacaoFormaPagamento.Transacao.TransacaoQueEstounouEsta == null && f.TransacaoFormaPagamento.Transacao.TipoTransacao.Id == 1);

            if (parametros.IdContaFinanceira > 0)
            {
                var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable();

                query = from r in query
                        join efp in estabelecimentoFormaPagamento on r.TransacaoFormaPagamento.FormaPagamento.Id equals efp.FormaPagamento.Id
                        where efp.ContaFinanceira.Id == parametros.IdContaFinanceira && efp.Estabelecimento == parametros.Estabelecimento
                        select r;
            }

            if (parametros.ExibirFormaPagamentoNosRelatorios)
            {
                query = query.Where(t => t.TransacaoFormaPagamento.Transacao.FormasPagamento.Any(p2 => p2.FormaPagamento.ExibirTransacoesPagasNosRelatorios));
            }

            query = AdicionarWhereDatasAgrupamento(query, parametros);
            //query = AdicionarCriterioFormaPagamentoDiferenteDescontoFornecedor(query);

            if (!parametros.ExibirCreditoClienteExportacao)
                query = query.Where(f => f.TransacaoFormaPagamento.FormaPagamento.Tipo != FormaPagamentoTipoEnum.PrePago);
            //query = AdicionarCriterioFormaPagamentoDiferentePrePago(query);

            return query;
        }

        private IQueryable<RelatorioFormaPagamento> AdicionarWhereBaseTransacaoRelatorioFinanceiro(
            ParametrosFiltrosRelatorio parametros,
            Boolean formaPagamentoDiferentePrePago = false)
        {
            var query =
                Queryable()
                    .Where(
                        t =>
                            //t.Transacao.Ativo &&
                            t.PessoaQueRecebeu.IdPessoa == parametros.Estabelecimento.PessoaJuridica.IdPessoa &&
                            t.ValorPago != 0);

            query = query.Where(t => t.FormaPagamento.Id != (int)FormaPagamentoEnum.DeixarFaltaComoDivida);

            query = AdicionarWhereDatasAgrupamento(query, parametros);
            query = AdicionarCriterioFormaPagamentoDiferenteDescontoFornecedor(query);

            if (formaPagamentoDiferentePrePago)
                query = AdicionarCriterioFormaPagamentoDiferentePrePago(query);

            if (parametros.IdTipoFormaPagamento > 0)
                query = query.Where(f => f.FormaPagamento.Tipo.Id == parametros.IdTipoFormaPagamento);

            if (parametros.IdFormasPagamentoSelecionadas != null && parametros.IdFormasPagamentoSelecionadas.Any())
                query = query.Where(f => parametros.IdFormasPagamentoSelecionadas.Contains(f.FormaPagamento.Id));

            if (parametros.IdFormaPagamento > 0)
                query = query.Where(f => f.FormaPagamento.Id == parametros.IdFormaPagamento);

            if (parametros.IdPessoasQueFecharam != null && parametros.IdPessoasQueFecharam.Any())
                query = query.Where(f => parametros.IdPessoasQueFecharam.Contains(f.PessoaQueRealizou.IdPessoa));

            if (!parametros.ExibirEstornos)
                query = query.Where(f => !f.IdTransacaoQueEstounouEsta.HasValue && f.TipoTransacao.Id == 1);

            if (parametros.IdContaFinanceira > 0)
            {
                var estabelecimentoFormaPagamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable();

                query = from r in query
                        join efp in estabelecimentoFormaPagamento on r.FormaPagamento.Id equals efp.FormaPagamento.Id
                        where efp.ContaFinanceira.Id == parametros.IdContaFinanceira
                        select r;
            }

            return query;
        }

        private void PreencherDadosDeAntecipacaoDaParcela(IQueryable<TransacaoFormaPagamentoParcela> query, List<ItemRelatorioFormaPagamentoNOVO> itensList)
        {
            var lancamentos = Domain.Financeiro.LancamentoDeAntecipacaoRepository.Queryable();

            var parcelasAntecipadas = (from p in query
                                       join la in lancamentos on p.Id equals la.TransacaoParcela.Id
                                       select new AntecipacaoDaParcelaDTO
                                       {
                                           IdTransacaoParcela = la.TransacaoParcela.Id,
                                           NomePessoaQueRegistrou = la.PessoaQueRegistrou.NomeCompleto,
                                           TaxaDesconto = la.TaxaDesconto,
                                           DataHoraRegistro = la.DataHoraRegistro,
                                           DataRecebimentoOriginal = la.DataRecebimentoOriginal,
                                           ValorRecebimentoAnterior = p.Valor
                                       }).ToList();

            foreach (var parcela in itensList)
            {
                parcela.Antecipacao = parcelasAntecipadas.FirstOrDefault(p => p.IdTransacaoParcela == parcela.IdTransacaoParcela);
            }
        }

        private void PreencherInformacoesDeNFC(IQueryable<RelatorioFormaPagamento> query, List<ItemRelatorioFormaPagamento> itensList)
        {
            var queryNotaNFC = Domain.NotaFiscalDoConsumidor.NotaNFCRepository.Queryable();

            var dadosNFC = (from r in query
                            join nfc in queryNotaNFC on r.Transacao.Id equals nfc.Transacao.Id
                            select new
                            {
                                nfc.StatusNota.Status,
                                nfc.NumeroNota,
                                IdTransacao = nfc.Transacao.Id,
                                nfc.AutorizacaoSEFAZ
                            }).ToList();

            foreach (var item in itensList)
            {
                var notaNFC = dadosNFC.Where(f => f.IdTransacao == item.IdTransacao).FirstOrDefault();

                if (notaNFC != null)
                {
                    item.NumeroDaNFE = notaNFC.NumeroNota;
                    item.StatusDaNFE = notaNFC.Status;
                    if (notaNFC.AutorizacaoSEFAZ != null)
                        item.CodigoSEFAZ = notaNFC.AutorizacaoSEFAZ.Remove(0, 3);
                    else
                        item.CodigoSEFAZ = "---";
                }
            }
        }

        private void PreencherInformacoesDeComanda(ParametrosFiltrosRelatorio parametros, List<ItemRelatorioFormaPagamento> itensList)
        {
            var idsTransacao = itensList.Select(a => a.IdTransacao).ToList();

            var transacoesComanda = Domain.Vendas.ComandaRepository.ObterKeyValueTransacaoNumeroComandaPorTransacoes(idsTransacao);

            foreach (var item in itensList)
            {
                var numerosComanda = transacoesComanda.Where(a => a.Key == item.IdTransacao).Select(a => a.Value).Distinct().OrderBy(a => a).ToList();

                item.NumerosComanda = String.Join(", ", numerosComanda);
            }
        }

        private void IncluirNomeDaContaFinanceiraNoResultado(List<ItemRelatorioFormaPagamentoNOVO> itensList, ParametrosFiltrosRelatorio parametros)
        {
            var contasformasDePagamentoDoEstabelecimento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable().Where(fpe => fpe.Estabelecimento.IdEstabelecimento == parametros.Estabelecimento.IdEstabelecimento).Select(fpe => new { IdFormaPagamento = fpe.FormaPagamento.Id, NomeConta = fpe.ContaFinanceira.Nome }).ToList();

            foreach (var item in itensList)
            {
                item.NomeContaFinanceiraAssociada = contasformasDePagamentoDoEstabelecimento.Where(cf => cf.IdFormaPagamento == item.IdFormaPagamento).Select(cf => cf.NomeConta).FirstOrDefault();
            }
        }

        public IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoAgrupadoFormaPagamento(
            ParametrosFiltrosRelatorio parametros)
        {
            return ListarRelatorioFormaPagamentoAgrupadosPorFormaPagamento(parametros);
        }

        public IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoAgrupadoPorData(
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return ListarRelatorioFormaPagamentoPorDataTransacao(parametros);
            return parametros.TipoData == TipoDataRelatorio.DataAtendimento ? ListarRelatorioFormaPagamentoPorDataAtendimento(parametros) : ListarRelatorioFormaPagamentoPorDataRecebimento(parametros);
        }

        public IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoAgrupadoPorMesAno(
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return ListarMesAnoRelatorioFormaPagamentoPorDataTransacao(parametros);
            if (parametros.TipoData == TipoDataRelatorio.DataAtendimento)
                return ListarMesAnoRelatorioFormaPagamentoPorDataAtendimento(parametros);
            return ListarMesAnoRelatorioFormaPagamentoPorDataRecebimento(parametros);
        }

        public IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoAgrupadoTipoPagamento(
            ParametrosFiltrosRelatorio parametros)
        {
            return ListarRelatorioFormaPagamentoAgrupadosPorTipoPagamento(parametros);
        }

        #region Métodos Privados

        #region Linha Mês/Ano

        private IList<ItemRelatorioFormaPagamento> ListarMesAnoRelatorioFormaPagamentoPorDataAtendimento(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorio = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros, true);

            var queryRelatorioAgrupadosPorTipoEData = (from tfp in queryBaseRelatorio
                                                       group tfp by new { tfp.DataReferencia.Month, tfp.DataReferencia.Year }
                                                           into g
                                                       select
                                                           new ItemRelatorioFormaPagamento
                                                           {
                                                               DataHoraAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                                                               TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.MesAno,
                                                               ValorPago = g.Sum(f => f.ValorPago),
                                                               ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                                                               ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                                                               ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                                                               ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao)
                                                           });

            return queryRelatorioAgrupadosPorTipoEData.ToList();
        }

        private IList<ItemRelatorioFormaPagamento> ListarMesAnoRelatorioFormaPagamentoPorDataRecebimento(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorio = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros, true);

            var queryRelatorioAgrupadosPorTipoEData = (from tfp in queryBaseRelatorio
                                                       group tfp by new { tfp.DataHoraParaReceberOperadora.Month, tfp.DataHoraParaReceberOperadora.Year }
                                                           into g
                                                       select
                                                           new ItemRelatorioFormaPagamento
                                                           {
                                                               DataHoraAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                                                               TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.MesAno,
                                                               ValorPago = g.Sum(f => f.ValorPago),
                                                               ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                                                               ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                                                               ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                                                               ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao)
                                                           });

            return queryRelatorioAgrupadosPorTipoEData.ToList();
        }

        private IList<ItemRelatorioFormaPagamento> ListarMesAnoRelatorioFormaPagamentoPorDataTransacao(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorio = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros, true);

            var queryRelatorioAgrupadosPorTipoEData = (from tfp in queryBaseRelatorio
                                                       group tfp by new { tfp.DataHoraTransacao.Month, tfp.DataHoraTransacao.Year }
                                                           into g
                                                       select
                                                           new ItemRelatorioFormaPagamento
                                                           {
                                                               DataHoraAgrupamento = new DateTime(g.Key.Year, g.Key.Month, 1),
                                                               ValorPago = g.Sum(f => f.ValorPago),
                                                               ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                                                               ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                                                               ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                                                               ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao)
                                                           });

            return queryRelatorioAgrupadosPorTipoEData.ToList();
        }

        #endregion Linha Mês/Ano

        #region Linha Tipo de Forma de Pagamento

        private IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoAgrupadosPorTipoPagamento(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorioTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros, true);

            var queryAgrupadosPorTipoPagamento = from t in queryBaseRelatorioTransacao
                                                 group t by
                                                     new
                                                     {
                                                         IdTipoPagamento = t.FormaPagamento.Tipo.Id,
                                                         TipoPagamento = t.FormaPagamento.Tipo.Nome,
                                                         IdTipoTransacao = t.TipoTransacao.Id
                                                     }
                                                     into g
                                                 select
                                                     new ItemRelatorioFormaPagamento
                                                     {
                                                         IdTipoPagamento = g.Key.IdTipoPagamento,
                                                         NomeTipoPagamento = g.Key.TipoPagamento,
                                                         ValorPago = g.Sum(f => f.ValorPago),
                                                         ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                                                         ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                                                         ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                                                         ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao),
                                                     };

            return (from t in queryAgrupadosPorTipoPagamento.ToList()
                    group t by new { t.IdTipoPagamento, TipoPagamento = t.NomeTipoPagamento }
                        into g
                    select
                        new ItemRelatorioFormaPagamento
                        {
                            TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.TipoFormaPagamento,
                            DataHoraAgrupamento =
                                new DateTime(parametros.DataInicial.Value.Year, parametros.DataInicial.Value.Month, 1),
                            IdTipoPagamento = g.Key.IdTipoPagamento,
                            NomeTipoPagamento = g.Key.TipoPagamento,
                            ValorPago = g.Sum(f => f.ValorPago),
                            ValorDescontoOperadora = g.Sum(f => f.ValorDescontoOperadora),
                            ValorSplit = g.Sum(f => f.ValorSplit),
                            ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                            ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao)
                        }).ToList();
        }

        #endregion Linha Tipo de Forma de Pagamento

        #region Linha Forma de Pagamento

        private IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoAgrupadosPorFormaPagamento(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorioTransacao = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            var queryAgrupadosPorTipoPagamento = from t in queryBaseRelatorioTransacao
                                                 group t by
                                                     new
                                                     {
                                                         IdFormaPagamento = t.FormaPagamento.Id,
                                                         FormaPagamento = t.FormaPagamento.Nome,
                                                         IdTipoTransacao = t.TipoTransacao.Id
                                                     }
                                                     into g
                                                 select
                                                     new ItemRelatorioFormaPagamento
                                                     {
                                                         IdFormaPagamento = g.Key.IdFormaPagamento,
                                                         NomeFormaPagamento = g.Key.FormaPagamento,
                                                         ValorPago = g.Sum(f => f.ValorPago),
                                                         ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                                                         ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                                                         ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                                                         ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao),
                                                     };

            return (from t in queryAgrupadosPorTipoPagamento.ToList()
                    group t by new { t.IdFormaPagamento, t.NomeFormaPagamento }
                        into g
                    select
                        new ItemRelatorioFormaPagamento
                        {
                            TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.FormaPagamento,
                            DataHoraAgrupamento =
                                new DateTime(parametros.DataInicial.Value.Year, parametros.DataInicial.Value.Month, 1),
                            IdTipoPagamento = parametros.IdTipoFormaPagamento,
                            IdFormaPagamento = g.Key.IdFormaPagamento,
                            NomeFormaPagamento = g.Key.NomeFormaPagamento,
                            ValorPago = g.Sum(f => f.ValorPago),
                            ValorSplit = g.Sum(f => f.ValorSplit),
                            ValorDescontoOperadora = g.Sum(f => f.ValorDescontoOperadora),
                            ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                            ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao),
                        }).ToList();
        }

        #endregion Linha Forma de Pagamento

        #region Linha Data

        private IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoPorDataAtendimento(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorio = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            return (from tfp in queryBaseRelatorio
                    group tfp by new { DataReferencia = tfp.DataReferencia.Date }
                        into g
                    select
                        new ItemRelatorioFormaPagamento
                        {
                            TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.Data,
                            IdTipoPagamento = parametros.IdTipoFormaPagamento,
                            IdFormaPagamento = parametros.IdFormaPagamento,
                            DataHoraAgrupamento = g.Key.DataReferencia,
                            ValorPago = g.Sum(f => f.ValorPago),
                            ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                            ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                            ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                            ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao),
                        }).ToList();
        }

        private IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoPorDataRecebimento(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorio = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            return (from tfp in queryBaseRelatorio
                    group tfp by new { DataHoraParaReceberOperadora = tfp.DataHoraParaReceberOperadora.Date }
                        into g
                    select
                        new ItemRelatorioFormaPagamento
                        {
                            TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.Data,
                            IdTipoPagamento = parametros.IdTipoFormaPagamento,
                            IdFormaPagamento = parametros.IdFormaPagamento,
                            DataHoraAgrupamento = g.Key.DataHoraParaReceberOperadora,
                            ValorPago = g.Sum(f => f.ValorPago),
                            ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                            ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                            ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                            ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao),
                        }).ToList();
        }

        private IList<ItemRelatorioFormaPagamento> ListarRelatorioFormaPagamentoPorDataTransacao(
            ParametrosFiltrosRelatorio parametros)
        {
            var queryBaseRelatorio = AdicionarWhereBaseTransacaoRelatorioFinanceiro(parametros);

            return (from tfp in queryBaseRelatorio
                    group tfp by new { DataHoraTransacao = tfp.DataHoraTransacao.Date }
                        into g
                    select
                        new ItemRelatorioFormaPagamento
                        {
                            TipoAgrupamento = TipoAgrupamentoRelatorioPorFormaPagamento.Data,
                            IdTipoPagamento = parametros.IdTipoFormaPagamento,
                            IdFormaPagamento = parametros.IdFormaPagamento,
                            DataHoraAgrupamento = g.Key.DataHoraTransacao,
                            ValorPago = g.Sum(f => f.ValorPago),
                            ValorDescontoOperadora = g.Sum(f => f.ValorCobradoOperadora),
                            ValorSplit = g.Sum(f => f.ValorSplit / f.NumeroParcelas),
                            ValorASerRecebido = g.Sum(f => f.ValorASerRecebido),
                            ValorDescontoAntecipacao = g.Sum(f => f.ValorDescontoAntecipacao)
                        }).ToList();
        }

        #endregion Linha Data

        private IQueryable<RelatorioFormaPagamento> AdicionarCriterioFormaPagamentoDiferenteDescontoFornecedor(
            IQueryable<RelatorioFormaPagamento> query)
        {
            return query.Where(p => p.FormaPagamento.ExibirTransacoesPagasNosRelatorios);
        }

        private IQueryable<RelatorioFormaPagamento> AdicionarCriterioFormaPagamentoDiferentePrePago(
            IQueryable<RelatorioFormaPagamento> query)
        {
            return query.Where(p => p.FormaPagamento.Tipo != FormaPagamentoTipoEnum.PrePago);
        }

        private IQueryable<RelatorioFormaPagamento> AdicionarWhereDataPrevistaRecebimento(
            IQueryable<RelatorioFormaPagamento> query,
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.DataInicial.HasValue)
                query = query.Where(f => f.DataHoraParaReceberOperadora >= parametros.DataInicial.Value.Date);

            if (parametros.DataFinal.HasValue)
                query = query.Where(f => f.DataHoraParaReceberOperadora < parametros.DataFinal.Value.Date.AddDays(1));

            var exibirDadosAPartirDe = parametros.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;
            if (exibirDadosAPartirDe.HasValue)
                query = AdicionarWhereDataTransacao(query, exibirDadosAPartirDe);

            return query;
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> AdicionarWhereDatasAgrupamento(IQueryable<TransacaoFormaPagamentoParcela> query, ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return AdicionarWhereDataTransacao(query, parametros.DataInicial, parametros.DataFinal);

            if (parametros.TipoData == TipoDataRelatorio.DataAtendimento)
                return AdicionarWhereDataAtendimento(query, parametros);

            if (parametros.TipoData == TipoDataRelatorio.DataPrevistaPagamento)
                return AdicionarWhereDataPrevistaRecebimento(query, parametros);

            return query;
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> AdicionarWhereDataTransacao(IQueryable<TransacaoFormaPagamentoParcela> query, DateTime? dataInicio = null, DateTime? dataFim = null)
        {
            if (dataInicio.HasValue)
                query = query.Where(f => f.TransacaoFormaPagamento.Transacao.DataHora >= dataInicio.Value.Date);

            if (dataFim.HasValue)
                query = query.Where(f => f.TransacaoFormaPagamento.Transacao.DataHora < dataFim.Value.AddDays(1).Date);

            return query;
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> AdicionarWhereDataAtendimento(IQueryable<TransacaoFormaPagamentoParcela> query, ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.DataInicial.HasValue)
                query = query.Where(f => f.TransacaoFormaPagamento.Transacao.DataReferencia >= parametros.DataInicial.Value.Date);

            if (parametros.DataFinal.HasValue)
                query = query.Where(f => f.TransacaoFormaPagamento.Transacao.DataReferencia < parametros.DataFinal.Value.Date.AddDays(1));

            return query;
        }

        private IQueryable<RelatorioFormaPagamento> AdicionarWhereDataAtendimento(IQueryable<RelatorioFormaPagamento> query, ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.DataInicial.HasValue)
                query = query.Where(f => f.DataReferencia >= parametros.DataInicial.Value.Date);
            if (parametros.DataFinal.HasValue)
                query = query.Where(f => f.DataReferencia < parametros.DataFinal.Value.Date.AddDays(1));
            return query;
        }

        private static IQueryable<TransacaoFormaPagamentoParcela> AdicionarWhereDataPrevistaRecebimento(IQueryable<TransacaoFormaPagamentoParcela> query, ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.DataInicial.HasValue)
                query = query.Where(f => f.DataRecebimento >= parametros.DataInicial.Value.Date);

            if (parametros.DataFinal.HasValue)
                query = query.Where(f => f.DataRecebimento < parametros.DataFinal.Value.Date.AddDays(1));

            var exibirDadosAPartirDe = parametros.Estabelecimento.EstabelecimentoConfiguracaoGeral.ExibirDadosAPartirDe;
            if (exibirDadosAPartirDe.HasValue)
                query = AdicionarWhereDataTransacao(query, exibirDadosAPartirDe);

            return query;
        }

        private IQueryable<RelatorioFormaPagamento> AdicionarWhereDatasAgrupamento(
            IQueryable<RelatorioFormaPagamento> query,
            ParametrosFiltrosRelatorio parametros)
        {
            if (parametros.TipoData == TipoDataRelatorio.DataTransacao)
                return AdicionarWhereDataTransacao(query, parametros.DataInicial, parametros.DataFinal);

            if (parametros.TipoData == TipoDataRelatorio.DataAtendimento)
                return AdicionarWhereDataAtendimento(query, parametros);

            if (parametros.TipoData == TipoDataRelatorio.DataPrevistaPagamento)
                return AdicionarWhereDataPrevistaRecebimento(query, parametros);

            return query;
        }

        private IQueryable<RelatorioFormaPagamento> AdicionarWhereDataTransacao(
            IQueryable<RelatorioFormaPagamento> query,
            DateTime? dataInicio = null,
            DateTime? dataFim = null)
        {
            if (dataInicio.HasValue)
                query = query.Where(f => f.DataHoraTransacao >= dataInicio.Value.Date);

            if (dataFim.HasValue)
                query = query.Where(f => f.DataHoraTransacao < dataFim.Value.AddDays(1).Date);

            return query;
        }

        #endregion Métodos Privados
    }
}