﻿using Perlink.Trinks.LinksDePagamentoNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks;
using Perlink.Trinks.Pessoas.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.Pessoas.Repositories
{
    public partial interface ILinkDePagamentoDoHorarioRepository
    {
        int ObterIdHorarioPorIdLinkDePagamento(int idLinkDePagamento);
        int[] ObterIdsHorariosComLinkDePagamento(IEnumerable<int> idsHorarios);
        List<LinkPorIdHorarioDto> ObterLinksDePagamentoPagosAgrupadoPorIdHorario(int[] idsHorarios);
        List<PagamentoOnlineNoTrinks> ObterPagamentosOnlineNoTrinksPorIdsHorario(int[] idsHorario);
        bool TransacaoPossuiHorarioComLinkDePagamento(int idTransacao);
        bool IdHorarioPossuiPagamentoAntecipadoHotsite(int idHorario);
        LinkDePagamentoNoTrinks ObterLinkDePagamentoTrinksPorIdHorario(int idHorario);
        LinkDePagamentoDoHorario ObterAtivoPorIdHorario(int idHorario);
        List<LinkPorIdHorarioDto> ObterLinksDePagamentoPagosAgrupadoPorIdTransacao(int idTransacao);
        bool IdHorarioPossuiPagamentoAntecipadoHotsiteConcluido(int idHorario);
        bool IdHorarioPossuiLinkPagoSemProcessamento(int idHorario);
        decimal ObterValorPorIdHorario(int idHorario);
    }
}