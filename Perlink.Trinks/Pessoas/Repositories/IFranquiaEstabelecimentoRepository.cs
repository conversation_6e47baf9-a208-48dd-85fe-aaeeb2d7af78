﻿using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IFranquiaEstabelecimentoRepository
    {

        string ObterUrlRedirecionamentoFrameBusca(int idEstabelecimento);

        string ObterIdAppIosDaFranquiaDoEstabelecimento(int? idEstabelecimento);

        bool ObterOpcaoQueDefineSeFranquiaCompartilhaEstabelecimento(int? idEstabelecimento);

        string ObterIdAppAndroidDaFranquiaDoEstabelecimento(int? idEstabelecimento);

        List<int> ObterTodosOsIdsDeModelo();

        bool FranquiaPermiteAgendamentoComPreCadastro(int idEstabelecimento);

        string ObterUrlAppIosDaFranquiaDoEstabelecimento(int idEstabelecimento);

        string ObterUrlAppAndroidDaFranquiaDoEstabelecimento(int idEstabelecimento);

        int ObterIdDoModeloDoEstabelecimento(int idEstabelecimento);

        List<UnidadesDaFranquiaDTO> ListarEstabelecimentosDaFranquiaForaDoModelo(int idEstabelecimentoDoModelo, string conteudoFiltro);

        IQueryable<UnidadesDaFranquiaDTO> ListarUnidadesDoModelo(int idEstabelecimentoDoModelo);

        List<KeyValuePair<int, string>> ObterEstabelecimentosDaMesmaFranquiaParaTransferenciaDeProdutos(Estabelecimento estabelecimento);

        List<KeyValuePair<int, string>> ListarEstabelecimentosQuePermitemAcessoBackOfficePorOutrosFranqueadores(int idFranquia);

        List<int> ObterIdsEstabelecimentoDasUnidadesAssociadasAoModelo(int idEstabelecimentoModelo);

        bool EstabelecimentoEhFranquiaBaseadoEmModelo(int idEstabelecimento);

        List<KeyValuePair<int, string>> ListarIdENomePorFranquia(int idFranquia);

        TipoCompartilhaNaRedeEnum ObterTipoCompartilhaNaRede(int idEstabelecimento);

        void AtualizarSeExistePacoteDisponivelNaRedeParaConsumo(int idFranquia);

        int ObterIdFranquiaPorIdEstabelecimento(int idEstabelecimento);

        bool EhUmEstabelecimentoFranquiado(int idEstabelecimento);

        List<FranquiaEstabelecimento> ObterFranquiasEstabelecimentosPorIdsEstabelecimentos(List<int> idsEstabelecimentos);
    }
}