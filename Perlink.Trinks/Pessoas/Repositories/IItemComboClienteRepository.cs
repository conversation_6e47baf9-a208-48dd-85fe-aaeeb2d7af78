﻿using System;
using System.Collections.Generic;
using System.Linq;
namespace Perlink.Trinks.Pessoas.Repositories
{
    public partial interface IItemComboClienteRepository
    {
        List<ItemComboCliente> ObterPorFiltro(string ConteudoFiltro, int idEstabelecimento);
        List<ItemComboCliente> ObterPorFiltro(int ConteudoFiltro, int idEstabelecimento);
        IQueryable<ItemComboCliente> ObterPorFiltroComAgendamento(string conteudoFiltro, int idEstabelecimento, DateTime data);
        IQueryable<ItemComboCliente> ToItemComboCliente(IQueryable<ClienteEstabelecimento> clientesEstabelecimento);
        System.Collections.Generic.List<ItemComboCliente> ObterItensComboClientesQuePossuamAtendimentoNaDataNaoCanceladoOuClienteFaltou(string ConteudoFiltro, DateTime data, int idEstabelecimento);
        int TotalDeItensComboClientesNoEstabelecimento(int idEstabelecimento);
        List<int> ObterIdsClientesEstabelecimentoPorFiltroTelefone(int idEstabelecimento, string telefone, bool obterTodos = false);
    }
}
