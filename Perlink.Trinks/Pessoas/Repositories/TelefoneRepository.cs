﻿using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class TelefoneRepository : ITelefoneRepository
    {

        #region Métodos Públicos

        public IList<Telefone> ObterTelefonesDaPessoa(Int32 idPessoa)
        {
            var retorno = Queryable();
            retorno = AdicionarFiltroPessoa(retorno, idPessoa);
            retorno = AdicionarFiltroAtivo(retorno);

            return retorno.ToList();
        }

        public IList<Telefone> ObterTelefonesCelularesDaPessoa(ClienteEstabelecimento clienteEstabelecimento)
        {
            return ObterTelefonesCelularesDaPessoaQueryable(clienteEstabelecimento).ToList();
        }

        public TelefoneDaPessoaDTO ObterDoPrimeiroTelefoneCelularAtivoDoClientePorIdPessoa(int idPessoaCliente, int idPessoaEstabelecimento)
        {
            return ObterTelefonesCelularesDaPessoaQueryable(idPessoaCliente, idPessoaEstabelecimento)
                .Select(p => new TelefoneDaPessoaDTO(p.Ddi, p.DDD, p.Numero))
                .FirstOrDefault();
        }

        public IQueryable<Telefone> ObterTelefonesCelularesDaPessoaQueryable(ClienteEstabelecimento clienteEstabelecimento)
        {
            var retorno = Queryable();

            retorno = AdicionarFiltroTelefonesDoEstabalecimentoOuDoCliente(retorno, clienteEstabelecimento);
            retorno = AdicionarFiltroAtivo(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelular(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelularNacional(retorno);

            return retorno;
        }

        public IQueryable<Telefone> ObterTelefonesCelularesDaPessoaQueryable(int idPessoaCliente, int idPessoaEstabelecimento)
        {
            var retorno = Queryable();

            retorno = AdicionarFiltroTelefonesDoEstabalecimentoOuDoCliente(retorno, idPessoaCliente, idPessoaEstabelecimento);
            retorno = AdicionarFiltroAtivo(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelular(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelularNacional(retorno);

            return retorno;
        }

        public IQueryable<Telefone> ObterTelefonesCelularesDaPessoaQueryable(List<int> idsPessoa)
        {
            var retorno = Queryable().Where(t => idsPessoa.Contains(t.IdPessoa));

            retorno = AdicionarFiltroAtivo(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelular(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelularNacional(retorno);

            return retorno;
        }

        public IQueryable<Telefone> ObterTelefonesVisiveisParaAPessoaJuridica(ClienteEstabelecimento clienteEstabelecimento)
        {
            return ObterTelefonesVisiveisParaAPessoaJuridica(clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa, clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoa);
        }

        public IQueryable<Telefone> ObterTelefonesVisiveisParaAPessoaJuridica(int idPessoaCliente, int idPessoaEstabelecimento)
        {
            var retorno = Queryable(true);

            retorno = AdicionarFiltroAtivo(retorno);
            retorno = AdicionarFiltroPessoa(retorno, idPessoaCliente);
            retorno = AdicionarFiltroPorDonos(retorno, idPessoaCliente, idPessoaEstabelecimento);

            return retorno;
        }

        public IQueryable<Telefone> ObterTelefonesCelularesNacionaisVisiveisParaAPessoaJuridica(int idPessoaCliente, int idPessoaEstabelecimento)
        {
            var retorno = Queryable(true);

            retorno = AdicionarFiltroAtivo(retorno);
            retorno = AdicionarFiltroPessoa(retorno, idPessoaCliente);
            retorno = AdicionarFiltroPorDonos(retorno, idPessoaCliente, idPessoaEstabelecimento);
            retorno = AdicionarFiltroClienteComTelefoneCelular(retorno);
            retorno = AdicionarFiltroClienteComTelefoneCelularNacional(retorno);

            return retorno;
        }

        public List<KeyValuePair<string, bool>> ObterListaTelefonesVisiveisParaAPessoaJuridica(int idPessoaCliente, int idEstabelecimento)
        {
            var retorno = Domain.Pessoas.ItemComboClienteRepository.Queryable(true);

            retorno = retorno.Where(f => f.IdEstabelecimento == idEstabelecimento);
            retorno = retorno.Where(f => f.IdPessoa == idPessoaCliente);

            var telefonesString = retorno.Select(f => f.Telefones).FirstOrDefault();

            var telefones = telefonesString.ToTextoFormatadoLista();

            return telefones;
        }

        public string ObterTelefonesDoEstabelecomento(int idEstabelecimento)
        {
            var retorno = Domain.Pessoas.ItemComboClienteRepository.Queryable(true);

            retorno = retorno.Where(f => f.IdEstabelecimento == idEstabelecimento);

            return retorno.Select(f => f.Telefones).FirstOrDefault();
        }
        #endregion Métodos Públicos

        #region Métodos Privados

        private static IQueryable<Telefone> AdicionarFiltroPessoa(IQueryable<Telefone> retorno, Int32 idPessoa)
        {
            return retorno.Where(t => t.IdPessoa == idPessoa);
        }

        private static IQueryable<Telefone> AdicionarFiltroPorDonos(IQueryable<Telefone> retorno, params int[] idPessoas)
        {
            return retorno.Where(t => idPessoas.Contains(t.Dono.IdPessoa));
        }


        private static IQueryable<Telefone> AdicionarFiltroAtivo(IQueryable<Telefone> retorno)
        {
            return retorno.Where(t => t.Ativo);
        }

        private static IQueryable<Telefone> AdicionarFiltroClienteComTelefoneCelular(IQueryable<Telefone> query)
        {
            return query.Where(f => f.Numero.StartsWith("6") || f.Numero.StartsWith("7") || f.Numero.StartsWith("8") || f.Numero.StartsWith("9") || f.Ddi != DdiConstants.Brasil);
        }

        private static IQueryable<Telefone> AdicionarFiltroClienteComTelefoneCelularNacional(IQueryable<Telefone> query)
        {
            return query.Where(f => f.Ddi == DdiConstants.Brasil && f.DDD.Length == 2 && (f.Numero.Length == 8 || f.Numero.Length == 9));
        }

        private static IQueryable<Telefone> AdicionarFiltroTelefonesDoEstabalecimentoOuDoCliente(IQueryable<Telefone> retorno, ClienteEstabelecimento clienteEstabelecimento)
        {
            return retorno.Where(t => t.IdPessoa == clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa).Where(d => d.Dono.IdPessoa == clienteEstabelecimento.Cliente.PessoaFisica.IdPessoaFisica || d.Dono.IdPessoa == clienteEstabelecimento.Estabelecimento.PessoaJuridica.IdPessoaJuridica);
        }

        private static IQueryable<Telefone> AdicionarFiltroTelefonesDoEstabalecimentoOuDoCliente(IQueryable<Telefone> retorno, int idPessoaCliente, int idPessoaEstabelecimento)
        {
            return retorno.Where(t => t.IdPessoa == idPessoaCliente).Where(d => d.Dono.IdPessoa == idPessoaCliente || d.Dono.IdPessoa == idPessoaEstabelecimento);
        }

        #endregion Métodos Privados

        public List<KeyValuePair<int, string>> ListarTelefonesFormatadosQueContemOsIdsPessoas(List<int> idsPessoas)
        {
            return Queryable()
                .Where(tel => idsPessoas.Contains(tel.Pessoa.IdPessoa) && tel.Ativo && tel.Ddi == DdiConstants.Brasil)
                .Select(tel => new KeyValuePair<int, string>(tel.Pessoa.IdPessoa, new TelefoneDaPessoaDTO(tel.Ddi, tel.DDD, tel.Numero).NumeroFormatado()))
                .ToList();
        }

        public List<Telefone> ListarTelefonesPropriosDaPessoa(int idPessoa, bool somenteAtivos = true)
        {
            var query = Queryable(true).Where(t => t.IdPessoa == idPessoa && t.Dono.IdPessoa == idPessoa);

            if (somenteAtivos)
                query = query.Where(t => t.Ativo);

            return query.ToList();
        }

        public List<TelefoneDaPessoaDTO> ListarTelefonesFormatadosDoProfissional(int idPessoaDoProfissional, int idPessoaDoEstabelecimento)
        {
            return Queryable()
                .Where(tel => tel.IdPessoa == idPessoaDoProfissional && tel.Ativo)
                .Where(tel => tel.Dono.IdPessoa == idPessoaDoProfissional || tel.Dono.IdPessoa == idPessoaDoEstabelecimento)
                .Select(tel => new TelefoneDaPessoaDTO(tel.Ddi, tel.DDD, tel.Numero))
                .ToList();
        }

        public TelefoneDaPessoaDTO ObterDoPrimeiroTelefoneCelularAtivoFormatadosDoProfissional(int idPessoaDoProfissional, int idPessoaEstabelecimento)
        {
            var query = Queryable();

            query = AdicionarFiltroAtivo(query);
            query = AdicionarFiltroClienteComTelefoneCelular(query);
            query = AdicionarFiltroClienteComTelefoneCelularNacional(query);

            var retorno = query.Where(tel => tel.IdPessoa == idPessoaDoProfissional && tel.Ativo)
                .Where(tel => tel.Dono.IdPessoa == idPessoaDoProfissional || tel.Dono.IdPessoa == idPessoaEstabelecimento)
                .Select(tel => new TelefoneDaPessoaDTO(tel.Ddi, tel.DDD, tel.Numero))
                .FirstOrDefault();

            return retorno;
        }
    }
}