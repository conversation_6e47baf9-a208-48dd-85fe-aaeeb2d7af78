﻿using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class ItemComboClienteRepository : IItemComboClienteRepository
    {

        public List<ItemComboCliente> ObterPorFiltro(string conteudoFiltro, int idEstabelecimento)
        {
            return ObterPorFiltroConsiderandoNomeEmailETelefone(conteudoFiltro, idEstabelecimento);
        }

        private List<ItemComboCliente> ObterPorFiltroConsiderandoNomeEmailETelefone(string conteudoFiltro, int idEstabelecimento)
        {
            var foiFiltrado = false;

            var habilitarMelhoriaPesquisaClientes = new ParametrosTrinks<bool>(ParametrosTrinksEnum.habilitar_melhoria_pesquisa_clientes).ObterValor();

            var nomeCliente = habilitarMelhoriaPesquisaClientes
                ? Regex.Replace(conteudoFiltro, " ", "%", RegexOptions.IgnoreCase)
                : conteudoFiltro;

            var query = Queryable().Where(f => f.IdEstabelecimento == idEstabelecimento);

            var expressaoDeConsulta = PredicateBuilder.False<ItemComboCliente>();

            var ehEmail = conteudoFiltro.EmailValido();
            var somenteNumeros = conteudoFiltro.SomenteNumeros();
            var podeSerFiltroTelefone = somenteNumeros.Length >= 3;

            if (!ehEmail && conteudoFiltro.Length >= 3)
            {
                expressaoDeConsulta = expressaoDeConsulta.Or(ic => ic.Nome.Contains(nomeCliente));
                foiFiltrado = true;
            }

            if (ehEmail && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Email))
            {
                expressaoDeConsulta = expressaoDeConsulta.Or(ic => ic.Email == conteudoFiltro.ToLower());
                foiFiltrado = true;
            }

            if (podeSerFiltroTelefone && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Telefone))
            {
                expressaoDeConsulta = expressaoDeConsulta.Or(ic => ic.TelefonesInexado.Contains(somenteNumeros));
                foiFiltrado = true;
            }

            query = query.Where(expressaoDeConsulta);

            if (!foiFiltrado)
                return new List<ItemComboCliente>();

            var list = query.ToList();
            var formatador = new FormatadorDeTelefone();

            list.ForEach(item =>
            {
                if (item.Telefones != null)
                {
                    item.Telefones = item.Telefones.ToTextoFormatadoListaConcatenada();
                }
            });

            return list;
        }

        public List<ItemComboCliente> ObterPorFiltro(int conteudoFiltro, int idEstabelecimento)
        {
            var dados = Queryable();
            dados = dados.Where(f =>
                f.IdEstabelecimento == idEstabelecimento &&
                f.IdCliente == conteudoFiltro);

            return dados.ToList();
        }

        public IQueryable<ItemComboCliente> ObterPorFiltroComAgendamento(string conteudoFiltro, int idEstabelecimento, DateTime data)
        {
            var clientesDoDia = Domain.Pessoas.HorarioRepository.Queryable()
                .Where(f => f.DataInicio.Date == data && f.Estabelecimento.IdEstabelecimento == idEstabelecimento)
                .Select(f => f.Cliente.IdCliente)
                .Distinct();

            var query = Queryable().Where(f => clientesDoDia.Contains(f.IdCliente) && f.IdEstabelecimento == idEstabelecimento);

            var expressaoDeConsulta = PredicateBuilder.False<ItemComboCliente>();

            var ehEmail = conteudoFiltro.EmailValido();
            var somenteNumeros = conteudoFiltro.SomenteNumeros();
            var podeSerFiltroTelefone = somenteNumeros.Length >= 3;

            if (ehEmail && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Email))
                expressaoDeConsulta = expressaoDeConsulta.Or(ic => ic.Email == conteudoFiltro.ToLower());

            if (podeSerFiltroTelefone && Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.LGPD.Ver_Telefone))
                expressaoDeConsulta = expressaoDeConsulta.Or(ic => ic.TelefonesInexado.Contains(somenteNumeros));

            if (!ehEmail)
                expressaoDeConsulta = expressaoDeConsulta.Or(ic => ic.Nome.Contains(conteudoFiltro));

            query = query.Where(expressaoDeConsulta);

            return query;
        }

        public IQueryable<ItemComboCliente> ToItemComboCliente(IQueryable<ClienteEstabelecimento> clientesEstabelecimento)
        {
            return from c in clientesEstabelecimento
                   join i in Queryable() on c.Codigo equals i.IdClienteEstabelecimento
                   select i;
        }

        public int TotalDeItensComboClientesNoEstabelecimento(int idEstabelecimento)
        {
            return Queryable().Where(x => x.IdEstabelecimento == idEstabelecimento).Count();
        }

        public List<ItemComboCliente> ObterItensComboClientesQuePossuamAtendimentoNaDataNaoCanceladoOuClienteFaltou(string ConteudoFiltro, DateTime data, int idEstabelecimento)
        {
            var listaStatusExcecao = new List<StatusHorario>() { StatusHorarioEnum.Cancelado, StatusHorarioEnum.Cliente_Faltou };

            var listaIdsClienteEstabelecimentoDosHorariosDoDiaDaMovimentacao = Domain.Pessoas.HorarioRepository.QueryableHorariosParaComboMovimentacaoEstoque(idEstabelecimento, data, listaStatusExcecao).Select(p => p.ClienteEstabelecimento.Codigo).ToList();
            var listaItemCombo = Domain.Pessoas.ItemComboClienteRepository.ObterPorFiltro(ConteudoFiltro, idEstabelecimento);
            var listaItemComboComHorarios = new List<ItemComboCliente>();

            if (listaIdsClienteEstabelecimentoDosHorariosDoDiaDaMovimentacao.Any())
            {
                listaItemComboComHorarios = listaItemCombo.Where(p => listaIdsClienteEstabelecimentoDosHorariosDoDiaDaMovimentacao.Contains(p.IdClienteEstabelecimento)).ToList();
            }

            return listaItemComboComHorarios;
        }

        public List<int> ObterIdsClientesEstabelecimentoPorFiltroTelefone(int idEstabelecimento, string telefone, bool obterTodos = false)
        {
            var telefonesFiltrados = Queryable()
                .Where(f => f.IdEstabelecimento == idEstabelecimento)
                .ComTelefoneQueContenha(telefone);

            if (!obterTodos)
            {
                telefonesFiltrados = telefonesFiltrados.Take(100);
            }

            return telefonesFiltrados.Select(f => f.IdClienteEstabelecimento).ToList();
        }
    }
}