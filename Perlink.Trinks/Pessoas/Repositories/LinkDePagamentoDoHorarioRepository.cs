﻿using Perlink.Trinks.LinksDePagamento.Enums;
using Perlink.Trinks.LinksDePagamentoNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks.Enums;
using Perlink.Trinks.Pessoas.DTO;
using Perlink.Trinks.Pessoas.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{
    public partial class LinkDePagamentoDoHorarioRepository : ILinkDePagamentoDoHorarioRepository
    {
        public int ObterIdHorarioPorIdLinkDePagamento(int idLinkDePagamento)
        {
            return Queryable()
                .Where(lph => lph.IdLinkDePagamento == idLinkDePagamento)
                .Select(lph => lph.IdHorario)
                .FirstOrDefault();
        }

        public int[] ObterIdsHorariosComLinkDePagamento(IEnumerable<int> idsHorarios)
        {
            // Permite a reutilização do plano de execução caso só haja um ID na lista
            if (idsHorarios.Count() == 1)
            {
                var idHorario = idsHorarios.Single();

                return Queryable()
                    .Where(lph => lph.IdHorario == idHorario)
                    .Select(lph => lph.IdHorario)
                    .ToArray();
            }

            return Queryable()
                .Where(lph => idsHorarios.Contains(lph.IdHorario))
                .Select(lph => lph.IdHorario).ToArray();
        }

        public List<LinkPorIdHorarioDto> ObterLinksDePagamentoPagosAgrupadoPorIdHorario(int[] idsHorarios)
        {
            var linkQueryable = Domain.LinksDePagamento.LinkDePagamentoRepository.Queryable();
            var pagamentosQueryable = Domain.Pagamentos.PagamentoRepository.Queryable();

            return (from linkDoHorario in Queryable()
                    join linkDePagamento in linkQueryable on linkDoHorario.IdLinkDePagamento equals linkDePagamento
                        .IdLinkDePagamento
                    join pagamento in pagamentosQueryable on linkDePagamento.IdPagamento equals pagamento.IdPagamento
                    where idsHorarios.Contains(linkDoHorario.IdHorario) &&
                          linkDoHorario.Ativo
                    select new LinkPorIdHorarioDto(linkDoHorario.IdHorario, linkDePagamento,
                        pagamento.QuantidadeParcelas))
                .ToList();
        }

        public List<PagamentoOnlineNoTrinks> ObterPagamentosOnlineNoTrinksPorIdsHorario(int[] idsHorario)
        {
            var pagamentoOnlineNoTrinksQueryable =
                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Queryable();
            var linkDePagamentoTrinksQueryable =
                Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable();

            return (from linkDoHorario in Queryable()
                join linkTrinks in linkDePagamentoTrinksQueryable on linkDoHorario.IdLinkDePagamento equals linkTrinks
                    .IdLinkDePagamento
                join pagamentoOnlineTrinks in pagamentoOnlineNoTrinksQueryable on linkTrinks.PagamentoOnlineNoTrinks.Id
                    equals pagamentoOnlineTrinks.Id
                where idsHorario.Contains(linkDoHorario.IdHorario) && linkDoHorario.Ativo
                select pagamentoOnlineTrinks).ToList();
        }

        public bool TransacaoPossuiHorarioComLinkDePagamento(int idTransacao)
        {
            var pagamentoOnlineTrinksQueryable =
                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Queryable(true);
            var linkDePagamentoTrinksQueryable =
                Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable(true);

            return (from pagamentoOnlineTrinks in pagamentoOnlineTrinksQueryable
                join linkDePagamentoTrinks in linkDePagamentoTrinksQueryable on pagamentoOnlineTrinks.Id equals
                    linkDePagamentoTrinks.PagamentoOnlineNoTrinks.Id
                join linkDoHorario in Queryable(true) on linkDePagamentoTrinks.IdLinkDePagamento equals linkDoHorario
                    .IdLinkDePagamento
                where pagamentoOnlineTrinks.Transacao.Id == idTransacao &&
                      pagamentoOnlineTrinks.Transacao.TipoTransacao.Id == 1 &&
                      pagamentoOnlineTrinks.Transacao.TransacaoQueEstounouEsta == null
                select linkDoHorario).Any();
        }

        public LinkDePagamentoNoTrinks ObterLinkDePagamentoTrinksPorIdHorario(int idHorario)
        {
            var linkDePagamentoTrinksQuery =
                Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable();

            return (from linkDoHorario in Queryable()
                join linkDePagamentoTrinks in linkDePagamentoTrinksQuery on linkDoHorario.IdLinkDePagamento equals
                    linkDePagamentoTrinks.IdLinkDePagamento
                where linkDoHorario.IdHorario == idHorario
                select linkDePagamentoTrinks).FirstOrDefault();
        }

        public List<LinkPorIdHorarioDto> ObterLinksDePagamentoPagosAgrupadoPorIdTransacao(int idTransacao)
        {
            var linkNoTrinksQueryable = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable();
            var pagamentosQueryable = Domain.Pagamentos.PagamentoRepository.Queryable();
            var linkDePagamentoQueryable = Domain.LinksDePagamento.LinkDePagamentoRepository.Queryable();

            return (from pagamento in pagamentosQueryable
                    join linkNoTrinks in linkNoTrinksQueryable on pagamento.IdPagamento equals linkNoTrinks
                        .PagamentoOnlineNoTrinks.IdPagamentoOnline
                    join linkDoHorario in Queryable() on linkNoTrinks.IdLinkDePagamento equals linkDoHorario
                        .IdLinkDePagamento
                    join linkDePagamento in linkDePagamentoQueryable on linkDoHorario.IdLinkDePagamento equals
                        linkDePagamento.IdLinkDePagamento
                    where linkNoTrinks.PagamentoOnlineNoTrinks.Transacao.Id == idTransacao &&
                          linkDoHorario.Ativo
                    select new LinkPorIdHorarioDto(linkDoHorario.IdHorario, linkDePagamento,
                        pagamento.QuantidadeParcelas))
                .ToList();
        }

        public bool IdHorarioPossuiPagamentoAntecipadoHotsite(int idHorario)
            => Queryable(cacheable: true).Any(lh => lh.IdHorario == idHorario && lh.Ativo);

        public bool IdHorarioPossuiPagamentoAntecipadoHotsiteConcluido(int idHorario)
        {
            var linkNoTrinksQuery = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable();
            return (from linkNoTrinks in linkNoTrinksQuery
                join linkDoHorario in Queryable(cacheable: true) on linkNoTrinks.IdLinkDePagamento equals linkDoHorario
                    .IdLinkDePagamento
                where linkDoHorario.Ativo && linkDoHorario.IdHorario == idHorario &&
                      linkNoTrinks.PagamentoOnlineNoTrinks.Status == StatusPagamentoNoTrinksEnum.Pago
                select linkDoHorario).Any();
        }

        public bool IdHorarioPossuiLinkPagoSemProcessamento(int idHorario)
        {
            var linkDePagamentoQuery = Domain.LinksDePagamento.LinkDePagamentoRepository.Queryable();
            var linkNoTrinksQuery = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable();
            return (from linkDePagamento in linkDePagamentoQuery
                join linkDoHorario in Queryable(cacheable: true) on linkDePagamento.IdLinkDePagamento equals
                    linkDoHorario.IdLinkDePagamento
                join linkNoTrinks in linkNoTrinksQuery on linkDePagamento.IdLinkDePagamento equals linkNoTrinks
                    .IdLinkDePagamento
                where linkDoHorario.Ativo &&
                      linkDoHorario.IdHorario == idHorario &&
                      linkNoTrinks.PagamentoOnlineNoTrinks.Status ==
                      StatusPagamentoNoTrinksEnum.AguardandoConfirmacaoDePagamento &&
                      linkDePagamento.StatusPagamento == StatusPagamentoEnum.Pago &&
                      linkDePagamento.Ativo
                select linkDoHorario).Any();
        }

        public LinkDePagamentoDoHorario ObterAtivoPorIdHorario(int idHorario)
            => Queryable().FirstOrDefault(lh => lh.IdHorario == idHorario && lh.Ativo);

        public decimal ObterValorPorIdHorario(int idHorario)
        {
            var linkDePagamentoQuery = Domain.LinksDePagamento.LinkDePagamentoRepository.Queryable();
            return (from linkDoHorario in Queryable()
                    join linkDePagamento in linkDePagamentoQuery
                        on linkDoHorario.IdLinkDePagamento equals linkDePagamento.IdLinkDePagamento
                    where linkDoHorario.IdHorario == idHorario
                    select linkDePagamento.Valor)
                .FirstOrDefault();
        }
    }
}