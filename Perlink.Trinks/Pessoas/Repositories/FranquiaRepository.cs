﻿using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial class FranquiaRepository : IFranquiaRepository
    {

        public Franquia ObterFranquiaPorIdentificadorDownloadApp(string identificadorDownloadApp)
        {
            return Queryable().Where(f => f.IdentificadorDownloadAppLista.Contains("," + identificadorDownloadApp + ",")).FirstOrDefault();
        }
        public IList<KeyValuePair<int, string>> ObterListaDeIdNomeDasFranquiasAtivas()
        {
            IList<KeyValuePair<int, string>> franquias = new List<KeyValuePair<int, string>>();

            var franquiasAtivasQuery = Queryable().Where(f => f.Ativo).OrderBy(f => f.Nome).ToList();

            franquias = franquiasAtivasQuery.Select(f => new KeyValuePair<int, string>(f.Id, f.Nome)).ToList();

            return franquias;
        }

        public bool ExisteFranquiaAtivaComMesmoNome(string nome)
        {
            var queryPromocoes = Queryable().Where(p => p.Nome == nome);
            return queryPromocoes.Any();
        }

        public bool ExisteFranquiaComMesmoSlug(string slug)
        {
            return Queryable().Any(p => p.Slug == slug.ToLower());
        }

        public Franquia ObterPorSlug(string slug)
        {
            return Queryable().FirstOrDefault(p => p.Slug == slug.ToLower());
        }

        public bool ObterValorConfiguracaoOcultaAgendamentosPerfilProfissional2(int idFranquia)
        {
            return Domain.Pessoas.FranquiaRepository.Queryable()
                .Where(f => f.Id == idFranquia)
                .Select(f => f.OcultaAgendamentosParaPerfilProfissional2).FirstOrDefault();
        }

        public List<Franquia> ObterFranquiasPorIds(List<int> idsFranquias)
        {
            if (idsFranquias == null || !idsFranquias.Any())
                return new List<Franquia>();

            return Queryable().Where(f => idsFranquias.Contains(f.Id)).ToList();
        }
    }
}