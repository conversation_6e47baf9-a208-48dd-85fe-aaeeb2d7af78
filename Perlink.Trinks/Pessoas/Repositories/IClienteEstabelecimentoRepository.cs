using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.DTO;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas.Repositories
{

    public partial interface IClienteEstabelecimentoRepository
    {

        ClienteEstabelecimento ObterClienteEstabelecimentoPorCPFeEstabelecimento(int idEstabelecimento, string cpf);
        IList<ClientesEstabelecimentosCountDTO> ClientesEstabelecimentosWebBalcaoCadastradosNoDia(DateTime dataCadastro);

        int ClientesEstabelecimentosWebBalcaoTotal();

        Boolean ExisteClienteEstabelecimentoComEmail(Int32 codigoCliente, String email);

        List<ClienteEstabelecimento> ListarAniversariantes(int idEstabelecimento, DateTime dataComparacao);

        List<ClienteEstabelecimento> ListarClienteEstabelecimentoPorCpf(String cpf);

        List<ClienteEstabelecimento> ListarClienteEstabelecimentoPorEmail(String email);

        IQueryable<ClienteEstabelecimento> ListarClientesDoEstabelecimentoPorIds(List<int> idsClientes, int idEstabelecimento);

        List<ClienteEstabelecimento> ListarClientesEstabelecimentoPorIdCliente(Int32 idCliente);

        IList<ClienteEstabelecimento> ListarPorEstabelecimento(Int32 codigoEstabelecimento);

        ResultadoPaginado<ClienteEstabelecimento> ListarPorEstabelecimentoPaginado(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool veTelefone);

        IQueryable<ClienteEstabelecimento> ListarPorEstabelecimentoQueryable(int idEstabelecimento);

        Decimal ObterValorCreditoClienteEstabelecimento(int idCliente, int idEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimento(Int32 idCliente, Int32 idEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimentoPorCpf(String cpf, Int32 codigoEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimentoIncluindoInativo(string cpfCliente, int codigoEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimentoPorEmailAssociacao(String email, Int32 codigoEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimentoPorEmailContaOuAssociacao(String email, Int32 codigoEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimentoPorPF(Int32 idPessoa, Int32 idEstabelecimento);

        IQueryable<ClienteEstabelecimento> ObterClientesComFiltro(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool filtrarPorTelefone, bool comFetchParaUsoSemPaginacao = true);

        IQueryable<ClienteEstabelecimento> ObterClientesComFiltro(ParametrosFiltroCliente parametros, bool filtraPorCPF, bool filtraPorEmail, bool filtrarPorTelefone, out bool PossuieFiltro, bool comFetchParaUsoSemPaginacao = false);

        List<ClienteEstabelecimento> ObterClientesEstabelecimentoComEnvioNotificacaoNaoDefinido(Int32 idEstabelecimento);

        IList<Estabelecimento> ObterEstabelecimentosRecentesComESemHorariosDeUmCliente(Int32 idCliente, int quantidadeLimite);

        IList<ClienteEstabelecimentoRepository.EstatisticaCliente> ObterEstatisticasDeTodosOsClientesEmUmEstabelecimento(long idEstabelecimento);

        Int32 ObterIdClienteEstabelecimentoPorPF(Int32 idPessoa, Int32 idEstabelecimento);


        List<ClienteEstabelecimento> ObterPendentesDeNotificacao(int limiteRegistros = 1000, bool stateless = false, int idEstabelecimento = 0);
        //DateTime? ObterDataNascimentoCliente(int idClienteEstabelecimento);
        ClienteEstabelecimento ObterPorCpf(string cpf, int idEstabelecimento);

        ClienteEstabelecimento ObterPorEmail(string email, int idEstabelecimento);

        ClienteEstabelecimento ObterPorTelefone(string telefone, int idEstabelecimento);

        ClienteEstabelecimento ObterPorNome(string nome, int idEstabelecimento);

        ClienteEstabelecimento ObterPorIdExterno(string idExterno, int idEstabelecimento);

        void SaveOrUpdate(ClienteEstabelecimento entidade, int? idPessoaQueAlterou);

        ClienteEstabelecimento ObterClienteEstabelecimentoPorEmail(String email, Int32 codigoEstabelecimento);

        ClienteEstabelecimento ObterClienteEstabelecimentoPorEmailAssociadoAoEstabelecimentoOuAUmClienteWeb(String email, Int32 codigoEstabelecimento);

        IQueryable<ClienteEstabelecimento> ObterPendentesDeNotificacaoDeAgendePeloTrinks(DateTime? data = null);

        string ObterObservacaoSobreOCliente(int idPessoa);

        DadosDoClienteEstabelecimentoParaLembreteSmsDTO ObterDadosDoClienteEstabelecimentoParaLembreteSmsDto(int idCliente, int idEstabelecimento);

        int TotalDeClientesDoEstabelecimento(int idEstabelecimento);

        int? ObterIdClienteEstabelecimentoPeloIdPessoa(int idPessoa, int idEstabelecimento);

        int ObterIdClienteEstabelecimento(int idEstabelecimento, int idCliente);

        int ObterTotalDePontosDeFidelidadeDeUmClienteEmUmaRedeDeFranquiaPeloCpfOuEmail(int idFranquia, string cpf, string email);

        IQueryable<ClienteEstabelecimento> ObterQueryComBuscaPeloMesmoClienteNaRede(int idFranquia, string cpf, string email);

        IList<ClienteEstabelecimento> ObterListaDeClienteAtivoNoEstabelecimentoPorPessoa(int idPessoa);

        bool ClienteRecebeSmsMarketing(int idPessoaDoCliente, int idEstabelecimento);

        IQueryable<ClientesDaRedeDTO> ListarBuscaCPFNaRede(int idFranquia, string cpf, bool soEstabelecimentoQueCompartilhaPacote = false);
        IQueryable<ClientesDaRedeDTO> ListarBuscaEmailNaRede(int idFranquia, string email, bool soEstabelecimentoQueCompartilhaPacote = false);

        ClienteEstabelecimento ObterPorId(int idClienteEstabelecimento, int idEstabelecimento);

        int ObterIdPessoaDoCliente(int idClienteEstabelecimento);

        PessoaFisica ObterPessoaFisicaDoCliente(int idClienteEstabelecimento);

        List<int> IdsClientesEstabelecimentosNovos(int idEstabelecimento, DateTime data);
        KeyValuePair<int, string> ObterKeyValueClienteEstabelecimento(int idEstabelecimento, int idCliente);

        List<ClienteEstabelecimento> ObterClienteEstabelecimentoPelaPessoa(List<int> idsPessoas, int idEstabelecimento);
        List<ClienteEstabelecimento> ObterClienteEstabelecimentoPeloId(List<int> listaIds);
        ClienteEstabelecimento ObterClienteEstabelecimentoPorIdPessoaEIdEstabelecimento(int idPessoaFisicaCliente, int idEstabelecimento);
        IList<ClienteEstabelecimento> ObterClienteEstabelecimentosPorTelefone(string ddd, string numero, int idEstabelecimento);
    }
}