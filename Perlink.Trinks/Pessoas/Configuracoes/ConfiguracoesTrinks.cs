﻿using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Notificacoes.Enums;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.Web;

namespace Perlink.Trinks.Pessoas.Configuracoes
{

    public static class ConfiguracoesTrinks
    {

        public static class AWS
        {

            public static string AccessKeyId
            {
                get
                {
                    var accessKeyId = Configuracoes["AWSAccessKeyId"];

                    if (string.IsNullOrWhiteSpace(accessKeyId))
                        throw new Exception("Access Key Id do AWS não configurado.");

                    return accessKeyId;
                }
            }

            public static string NomeFilaSQSMensagensSMS
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensSMS"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensSMSImediato
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensSMSImediato"].ToString();
                }
            }

            public static string NomeFilaSQSRetornoMensagensSMS
            {
                get
                {
                    return Configuracoes["NomeFilaSQSRetornoMensagensSMS"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensPush
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensPush"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensPushProgramada
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensPushProgramada"].ToString();
                }
            }

            public static string NomeFilaSQSDisparoPushB2CFirebase
            {
                get
                {
                    return Configuracoes["NomeFilaSQSDisparoPushB2CFirebase"].ToString();
                }
            }

            public static string NomeFilaSQSDisparoPushB2CApple
            {
                get
                {
                    return Configuracoes["NomeFilaSQSDisparoPushB2CApple"].ToString();
                }
            }

            public static string NomeFilaSQSRetornoConsolidacaoPushB2C
            {
                get
                {
                    return Configuracoes["NomeFilaSQSRetornoConsolidacaoPushB2C"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensB2B
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensB2B"].ToString();
                }
            }

            public static string NomeFilaSQSWppCompraRecorrente
            {
                get
                {
                    return Configuracoes["NomeFilaSQSWppCompraRecorrente"].ToString();
                }
            }

            public static string NomeFilaSQSEnvioAssincronoCrmBonus
            {
                get
                {
                    return Configuracoes["NomeFilaSQSEnvioAssincronoCrmBonus"].ToString();
                }
            }

            public static string NomeFilaSQSDlqEnvioAssincronoCrmBonus
            {
                get
                {
                    return Configuracoes["NomeFilaSQSDlqEnvioAssincronoCrmBonus"].ToString();
                }
            }

            public static string RegionLocal
            {
                get
                {
                    var region = Configuracoes["AWSRegion"];

                    if (string.IsNullOrWhiteSpace(region))
                        throw new Exception("Region do AWS não configurado.");

                    return region;
                }
            }

            public static string RegionSQS
            {
                get
                {
                    var region = Configuracoes["AWSRegionSQS"];

                    if (string.IsNullOrWhiteSpace(region))
                        throw new Exception("Region do AWS não configurado.");

                    return region;
                }
            }

            public static string RegionPrincipal
            {
                get
                {
                    var region = Configuracoes["AWSRegionSES"];

                    if (string.IsNullOrWhiteSpace(region))
                        throw new Exception("Region do AWS não configurado.");

                    return region;
                }
            }

            public static string SecretAccessKey
            {
                get
                {
                    var secretAccessKey = Configuracoes["AWSSecretAccessKey"];

                    if (string.IsNullOrWhiteSpace(secretAccessKey))
                        throw new Exception("Secret Access Key do AWS não configurado.");

                    return secretAccessKey;
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return ConfigurationManager.AppSettings;
                }
            }

            #region Referências de Push SNS
            // As referências abaixo foram mantidas intencionalmente INCORRETAS
            // para evitar que o código gere notificações reais durante a execução dos testes.
            // Isso é necessário pois algumas integrações, como o Hubspot, não possuem um ambiente de testes (sandbox).
            // Caso queira testar gerando eventos reais, por favor, altere os valores abaixo conforme indicado
            // Além disso, alguns valores não possuem correspondente na conta de dev na AWS

            // Valor correto: "arn:aws:sns:us-east-1:749532251542:dev_eventos_trinks_cadastro_estabelecimento"
            public static string PushDadosEstabelecimentoCadastrado
                => ConfigurationManager.AppSettings["push_dados_estabelecimento_cadastrado"] ?? "arn:aws:sns:sa-east-1:441135549897:dev_eventos_trinks_cadastro_estabelecimento";

            // O valor correto dessa configuração já está disponível via appSettings.config
            public static string PushDadosPreCadastroEstabelecimentoCadastrado
                => Configuracoes["push_dados_pre_cadastro_estabelecimento"].ToString();

            // Valor correto: não possui correspondente na conta de dev na AWS!
            public static string PushDadosEstabelecimentoAtualizado
                => ConfigurationManager.AppSettings["push_dados_estabelecimento_atualizado"] ?? "arn:aws:sns:sa-east-1:441135549897:dev_eventos_trinks_atualiza_estabelecimento";

            // Valor correto: "arn:aws:sns:us-east-1:749532251542:dev_eventos_trinks_cadastro_profissional"
            public static string PushQtdAtualProfissionaisComAgenda
                => ConfigurationManager.AppSettings["push_qtd_atual_profissionais_com_agenda"] ?? "arn:aws:sns:sa-east-1:441135549897:dev_eventos_trinks_cadastro_profissional";

            // Valor correto: não possui correspondente na conta de dev na AWS!
            public static string PushExpectativaTutorial
                => ConfigurationManager.AppSettings["push_expectativa_tutorial"] ?? "arn:aws:sns:sa-east-1:441135549897:dev_eventos_trinks_expectativa_tutorial";

            // Valor correto: "arn:aws:sns:us-east-1:749532251542:dev_eventos_trinks_alteracao_status_conta"
            public static string PushMudancaStatusConta
                => ConfigurationManager.AppSettings["push_mudanca_status_conta"] ?? "arn:aws:sns:sa-east-1:441135549897:dev_eventos_trinks_alteracao_status_conta";

            // Valor correto: "arn:aws:sns:us-east-1:749532251542:dev_eventos_trinks_pagamento_fatura"
            public static string PushDadosPagamentoFaturaTrinks
                => ConfigurationManager.AppSettings["push_dados_pagamento_fatura_trinks"] ?? "arn:aws:sns:sa-east-1:441135549897:dev_eventos_trinks_pagamento_fatura";
            #endregion

            public static string NomeFilaSQSRequisicaoVouverBelezaAmiga
            {
                get
                {
                    return Configuracoes["NomeFilaSQSRequisicaoVouverBelezaAmiga"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensLinkDePagamento
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensLinkDePagamento"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensRegistroConsumoLimitePagamento
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensRegistroConsumoLimitePagamento"].ToString();
                }
            }

            public static string NomeFilaSQSMensagensContaDigital
            {
                get
                {
                    return Configuracoes["NomeFilaSQSMensagensContaDigital"].ToString();
                }
            }

            public static string DiafaanSNSEnvioDeMsgs
            {
                get
                {
                    return Configuracoes["DiafaanSNSEnvioDeMsgs"].ToString();
                }
            }

            public static string GoogleReserveConversionTracking
            {
                get
                {
                    return Configuracoes["push_google_reserve_conversion_tracking"].ToString();
                }
            }

            public static string ComissaoLogGroupName => Configuracoes[nameof(ComissaoLogGroupName)]?.ToString() ?? "Comissao";

            public static string DescontoPersonalizadoLogGroupName => Configuracoes["DescontoPersonalizadoLogGroupName"]?.ToString() ?? "DescontoPersonalizado";

            public static string PacotePersonalizadoLogGroupName => Configuracoes[nameof(PacotePersonalizadoLogGroupName)]?.ToString() ?? "PacotePersonalizado";
        }

        public static class Cobranca
        {

            public static bool HabilitaFase2
            {
                get
                {
                    var config = Perlink.Trinks.Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("habilita_fase_2_cobranca");
                    return config != null && config.Valor == "1";
                }
            }

            public static bool HabilitaHomologacaoCobrebem
            {
                get
                {
                    var config = Perlink.Trinks.Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("habilita_homologacao_cobrebem");
                    return config == null || config.Valor == "1";
                }
            }

            public static bool HabilitarReprocessamentoFaturasPagas
            {
                get
                {
                    return new ParametrosTrinks<bool>(ParametrosTrinksEnum.habilitar_reprocessamento_faturas_pagas).ObterValor();
                }
            }

            public static string LoginCobrebem
            {
                get
                {
                    var config = Perlink.Trinks.Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("login_cobrebem");
                    return config == null ? "" : config.Valor;
                }
            }

            public static int IdPlanoPadraoProgramaFidelidade
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("id_plano_padrao_programa_fidelidade");
                    return config == null ? 0 : int.Parse(config.Valor);
                }
            }

            public static string PagarMeApiKey
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("PagarMe_ApiKey");
                    return config == null ? "ak_test_e5RbpRpObNezaySFiOecGOA2v3wYPj" : config.Valor;
                }
            }

            public static string PagarMeEncryptionKey
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("PagarMe_EncryptionKey");
                    return config == null ? "ek_test_yVBlg5nbcBHfDCpXP4vIyLkGddxeAk" : config.Valor;
                }
            }
        }

        public static class EnvioEmail
        {

            public static int TemplateSendInBlueBoasVindasBelezinha
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("template_sendinblue_boasvindas_belezinha");
                    return config == null ? 886 : int.Parse(config.Valor);
                }
            }

            public static int TemplateSendInBlueAppBelezinhaLiberado
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("template_sendinblue_app_belezinha_liberado");
                    return config == null ? 887 : int.Parse(config.Valor);
                }
            }

            public static string AssuntoEmailConviteAvaliacaoEstabelecimento
            {
                get
                {
                    return Configuracoes["AssuntoEmailConviteAvaliacaoEstabelecimento"].ToString();
                }
            }

            public static string AssuntoEmailErroEmissaoCFeSAT
            {
                get
                {
                    return Configuracoes["AssuntoEmailErroEmissaoCFeSAT"].ToString();
                }
            }

            public static string AssuntoEmailAgendamentoCancelado
            {
                get
                {
                    return Configuracoes["AssuntoEmailAgendamentoCancelado"].ToString();
                }
            }

            public static string AssuntoEmailAgendamentoFinalizado
            {
                get
                {
                    return Configuracoes["AssuntoEmailAgendamentoFinalizado"].ToString();
                }
            }

            public static string AssuntoEmailAgendamentoMarcado
            {
                get
                {
                    return Configuracoes["AssuntoEmailAgendamentoMarcado"].ToString();
                }
            }

            public static string AssuntoEmailComprovantePagamentoBelezinha
            {
                get
                {
                    return Configuracoes["AssuntoEmailComprovantePagamentoBelezinha"].ToString();
                }
            }

            public static string AssuntoEmailAlteracaoDadosAgendamento
            {
                get
                {
                    return Configuracoes["AssuntoEmailAlteracaoDadosAgendamento"].ToString();
                }
            }

            public static string AssuntoEmailAlteracaoStatusAgendamento
            {
                get
                {
                    return Configuracoes["AssuntoEmailAlteracaoStatusAgendamento"].ToString();
                }
            }

            public static string AssuntoEmailAssinaturaCancelada
            {
                get
                {
                    return Configuracoes["AssuntoEmailAssinaturaCancelada"].ToString();
                }
            }

            public static string AssuntoEmailAtivacaoCadastro
            {
                get
                {
                    return Configuracoes["AssuntoEmailAtivacaoCadastro"].ToString();
                }
            }

            public static string AssuntoEmailBoasVindasClienteWeb
            {
                get
                {
                    return Configuracoes["AssuntoEmailBoasVindasClienteWeb"].ToString();
                }
            }

            public static string AssuntoEmailBoletoGerado
            {
                get
                {
                    return Configuracoes["AssuntoEmailBoletoGerado"].ToString();
                }
            }

            public static string AssuntoEmailCadastroProfissionalRealizado
            {
                get
                {
                    return Configuracoes["AssuntoEmailCadastroProfissionalRealizado"].ToString();
                }
            }

            public static string AssuntoEmailCampanhaMarketingPendenteDeCredito
            {
                get
                {
                    return Configuracoes["AssuntoEmailCampanhaMarketingPendenteDeCredito"].ToString();
                }
            }

            public static string AssuntoEmailCampanhaMarketingSituacaoIrregular
            {
                get
                {
                    return Configuracoes["AssuntoEmailCampanhaMarketingSituacaoIrregular"].ToString();
                }
            }

            public static string AssuntoEmailCancelamentoNFC
            {
                get
                {
                    return Configuracoes["AssuntoEmailCancelamentoNFC"].ToString();
                }
            }

            public static string AssuntoEmailCancelamentoRecorrencia
            {
                get
                {
                    return Configuracoes["AssuntoEmailCancelamentoRecorrencia"].ToString();
                }
            }

            public static string AssuntoEmailCartaoProximoAExpirar
            {
                get
                {
                    return Configuracoes["AssuntoEmailCartaoProximoAExpirar"].ToString();
                }
            }

            public static string AssuntoEmailClienteBalcaoCadastrado
            {
                get
                {
                    return Configuracoes["AssuntoEmailClienteBalcaoCadastrado"].ToString();
                }
            }

            public static string AssuntoEmailClienteFaltou
            {
                get
                {
                    return Configuracoes["AssuntoEmailClienteFaltou"].ToString();
                }
            }

            public static string AssuntoEmailConfirmacaoCadastro
            {
                get
                {
                    return Configuracoes["AssuntoEmailConfirmacaoCadastro"].ToString();
                }
            }

            public static string AssuntoEmailContaBloqueada
            {
                get
                {
                    return Configuracoes["AssuntoEmailContaBloqueada"].ToString();
                }
            }

            public static string AssuntoEmailContaFinanceiraAExpirar1
            {
                get
                {
                    return Configuracoes["AssuntoEmailContaFinanceiraAExpirar1"].ToString();
                }
            }

            public static string AssuntoEmailContaFinanceiraAExpirar10
            {
                get
                {
                    return Configuracoes["AssuntoEmailContaFinanceiraAExpirar10"].ToString();
                }
            }

            public static string AssuntoEmailContaFinanceiraAExpirar15
            {
                get
                {
                    return Configuracoes["AssuntoEmailContaFinanceiraAExpirar15"].ToString();
                }
            }

            public static string AssuntoEmailContaFinanceiraAExpirar3
            {
                get
                {
                    return Configuracoes["AssuntoEmailContaFinanceiraAExpirar3"].ToString();
                }
            }

            public static string AssuntoEmailContaFinanceiraCancelada
            {
                get
                {
                    return Configuracoes["AssuntoEmailContaFinanceiraCancelada"].ToString();
                }
            }

            public static string AssuntoEmailEmissaoNFC
            {
                get
                {
                    return Configuracoes["AssuntoEmailEmissaoNFC"].ToString();
                }
            }

            public static string AssuntoEmailProgramaDeFidelidadePontosGanho
            {
                get
                {
                    return Configuracoes["AssuntoEmailProgramaDeFidelidadePontosGanho"].ToString();
                }
            }

            public static string AssuntoEmailProgramaDeFidelidadePontosExpirandoNoPrazo
            {
                get
                {
                    return Configuracoes["AssuntoEmailProgramaDeFidelidadePontosExpirandoNoPrazo"].ToString();
                }
            }

            public static string AssuntoEmailProgramaDeFidelidadePontosExpirandoNoDia
            {
                get
                {
                    return Configuracoes["AssuntoEmailProgramaDeFidelidadePontosExpirandoNoDia"].ToString();
                }
            }

            public static string AssuntoEmailEstabelecimentoCadastrado
            {
                get
                {
                    return Configuracoes["AssuntoEmailEstabelecimentoCadastrado"].ToString();
                }
            }

            public static string AssuntoEmailEstabelecimentoIndicado
            {
                get
                {
                    return Configuracoes["AssuntoEmailEstabelecimentoIndicado"].ToString();
                }
            }

            public static string AssuntoEmailFaleConosco
            {
                get
                {
                    return Configuracoes["AssuntoEmailFaleConosco"].ToString();
                }
            }

            public static string AssuntoEmailInformativoLembreteAgendamentosAmanha
            {
                get
                {
                    return Configuracoes["AssuntoEmailInformativoLembreteAgendamentosAmanha"].ToString();
                }
            }

            public static string AssuntoEmailManterAgendamentoEmLote
            {
                get
                {
                    return Configuracoes["AssuntoEmailManterAgendamentoEmLote"].ToString();
                }
            }

            public static string AssuntoEmailMarketingAniversario
            {
                get
                {
                    return Configuracoes["AssuntoEmailMarketingAniversario"].ToString();
                }
            }

            public static string AssuntoEmailMudancaNomeServicoOuCategoriaPadrao
            {
                get
                {
                    return Configuracoes["AssuntoEmailMudancaNomeServicoOuCategoriaPadrao"].ToString();
                }
            }

            public static string AssuntoEmailNovoServicoOuCategoriaNaoPadrao
            {
                get
                {
                    return Configuracoes["AssuntoEmailNovoServicoOuCategoriaNaoPadrao"].ToString();
                }
            }

            public static string AssuntoEmailPagamentoFaturaAprovado
            {
                get
                {
                    return Configuracoes["AssuntoEmailPagamentoFaturaAprovado"].ToString();
                }
            }

            public static string AssuntoEmailPagamentoFaturaNaoAutorizado
            {
                get
                {
                    return Configuracoes["AssuntoEmailPagamentoFaturaNaoAutorizado"].ToString();
                }
            }

            public static string AssuntoEmailPreCadastro
            {
                get
                {
                    return Configuracoes["AssuntoEmailPreCadastro"].ToString();
                }
            }

            public static string AssuntoEmailProximosAgendamentos
            {
                get
                {
                    return Configuracoes["AssuntoEmailProximosAgendamentos"].ToString();
                }
            }

            public static string AssuntoEmailReativacaoConta
            {
                get
                {
                    return Configuracoes["AssuntoEmailReativacaoConta"].ToString();
                }
            }

            public static string AssuntoEmailRecuperacaoSenha
            {
                get
                {
                    return Configuracoes["AssuntoEmailRecuperacaoSenha"].ToString();
                }
            }

            public static string AssuntoEmailSenhaAlterada
            {
                get
                {
                    return Configuracoes["AssuntoEmailSenhaAlterada"].ToString();
                }
            }

            public static string AssuntoEmailSolicitacaoEnviadaPeloCliente
            {
                get
                {
                    return Configuracoes["AssuntoEmailSolicitacaoEnviadaPeloCliente"].ToString();
                }
            }

            public static string AssuntoEnviarEmailEmissaoNFCe
            {
                get
                {
                    return Configuracoes["AssuntoEnviarEmailEmissaoNFCe"].ToString();
                }
            }

            public static string AssuntoEstatisticasSobreUsoDoSistema
            {
                get
                {
                    return Configuracoes["AssuntoEstatisticasSobreUsoDoSistema"].ToString();
                }
            }

            public static string AssuntoLembreteAgendamentosAmanha
            {
                get
                {
                    return Configuracoes["AssuntoLembreteAgendamentosAmanha"].ToString();
                }
            }

            public static string AssuntoEmailFeedbackDoAplicativoPeloUsuario
            {
                get
                {
                    return Configuracoes["AssuntoEmailFeedbackDoAplicativoPeloUsuario"].ToString();
                }
            }

            public static string AssuntoEmailComprovantePagamentoPorLinkDePagamento
            {
                get
                {
                    return Configuracoes["AssuntoEmailComprovantePagamentoPorLinkDePagamento"].ToString();
                }
            }

            public static string AssuntoEmailComprovantePagamentoDeClubeDeAssinatura
            {
                get
                {
                    return Configuracoes["AssuntoEmailComprovantePagamentoDeClubeDeAssinatura"].ToString();
                }
            }

            public static string ContaFinanceiraNaoAssinanteExpiradaA10Dias
            {
                get
                {
                    return Configuracoes["ContaFinanceiraNaoAssinanteExpiradaA10Dias"].ToString();
                }
            }

            public static string EnderecoEmailAtendimento
            {
                get
                {
                    return Configuracoes["EnderecoEmailAtendimento"].ToString();
                }
            }

            public static string EnderecoEmailVendas
            {
                get
                {
                    return Configuracoes["EnderecoEmailVendas"].ToString();
                }
            }

            public static string EnderecoEmailCopiaInterna
            {
                get
                {
                    return Configuracoes["EnderecoEmailCopiaInterna"].ToString();
                }
            }

            public static string EnderecoEmailAdicionais
            {
                get
                {
                    return Configuracoes["EnderecoEmailAdicionais"].ToString();
                }
            }

            public static string EnderecoEmailContato
            {
                get
                {
                    return Configuracoes["EnderecoEmailContato"].ToString();
                }
            }

            public static string EnderecoEmailFabrica
            {
                get
                {
                    return Configuracoes["EnderecoEmailFabrica"].ToString();
                }
            }

            public static string EnderecoEmailFaleConosco
            {
                get
                {
                    return Configuracoes["EnderecoEmailFaleConosco"].ToString();
                }
            }

            public static string EnderecoEmailSuporte
            {
                get
                {
                    return Configuracoes["EnderecoEmailSuporte"].ToString();
                }
            }

            public static string EnderecoEmailCrossSell
            {
                get
                {
                    return Configuracoes["EnderecoEmailCrossSell"].ToString();
                }
            }

            public static string EnderecoEmailErrosPagarme
            {
                get
                {
                    return Configuracoes["EnderecoEmailErrosPagarme"].ToString();
                }
            }

            public static string EnderecoEmailLimitePagamentosRecebedor
            {
                get
                {
                    return Configuracoes["EnderecoEmailLimitePagamentosRecebedor"].ToString();
                }
            }

            public static string EnderecoEmailFinanceiro
            {
                get
                {
                    return Configuracoes["EnderecoEmailFinanceiro"].ToString();
                }
            }

            public static string EnderecoEmailMudancasPlano
            {
                get
                {
                    return Configuracoes["EnderecoEmailMudancasPlano"].ToString();
                }
            }

            public static string EnderecoEmailNaoResponda
            {
                get
                {
                    return Configuracoes["EnderecoEmailNaoResponda"].ToString();
                }
            }

            public static string EnderecoEmailPrincipal
            {
                get
                {
                    return Configuracoes["EnderecoEmailPrincipal"].ToString();
                }
            }

            public static string EnderecoEmailParceiro
            {
                get
                {
                    return Configuracoes["EnderecoEmailParceiro"].ToString();
                }
            }

            public static string EnderecoEmailParceiroTrinks
            {
                get
                {
                    return Configuracoes["EnderecoEmailParceiroTrinks"].ToString();
                }
            }

            public static string EnderecoEmailTecnico
            {
                get
                {
                    return Configuracoes["EnderecoEmailTecnico"].ToString();
                }
            }

            public static string EnderecoEmailErrosConsumoPacote
                => Configuracoes["EnderecoEmailErrosConsumoPacote"].ToString();

            public static string[] EnderecosRemotosPermitidos
            {
                get
                {
                    string parameter = Configuracoes["EnderecosRemotosPermitidos"];
                    string[] listaEnderecosPermitidos = null;

                    if (parameter != null)
                        listaEnderecosPermitidos = parameter.Split(';');

                    return listaEnderecosPermitidos;
                }
            }

            public static string NomeEmailAtendimento
            {
                get
                {
                    return Configuracoes["NomeEmailAtendimento"].ToString();
                }
            }

            public static string NomeEmailVendas
            {
                get
                {
                    return Configuracoes["NomeEmailVendas"].ToString();
                }
            }

            public static string NomeEmailParceiro
            {
                get
                {
                    return Configuracoes["NomeEmailParceiro"].ToString();
                }
            }

            public static string NomeEmailFinanceiro
            {
                get
                {
                    return Configuracoes["NomeEmailFinanceiro"].ToString();
                }
            }

            public static string NomeEmailSuporte
            {
                get
                {
                    return Configuracoes["NomeEmailSuporte"].ToString();
                }
            }

            public static string NomeEmailCrossSell
            {
                get
                {
                    return Configuracoes["NomeEmailCrossSell"].ToString();
                }
            }

            public static string NomeEmailErrosPagarme
            {
                get
                {
                    return Configuracoes["NomeEmailErrosPagarme"].ToString();
                }
            }

            public static string NomeEmailLimitePagamentosRecebedor
            {
                get
                {
                    return Configuracoes["NomeEmailLimitePagamentosRecebedor"].ToString();
                }
            }

            public static string NomeEmailAdicionais
            {
                get
                {
                    return Configuracoes["NomeEmailAdicionais"].ToString();
                }
            }

            public static string NomeEmailContato
            {
                get
                {
                    return Configuracoes["NomeEmailContato"].ToString();
                }
            }

            public static string NomeEmailPrincipal
            {
                get
                {
                    return Configuracoes["NomeEmailPrincipal"].ToString();
                }
            }

            public static string NomeEmailNaoResponda
            {
                get
                {
                    return Configuracoes["NomeEmailNaoResponda"].ToString();
                }
            }

            public static string NomeEmailErrosConsumoPacote
                => Configuracoes["NomeEmailErrosConsumoPacote"].ToString();

            public static string CampanhaNaoEnviada
            {
                get
                {
                    return Configuracoes["CampanhaNaoEnviada"].ToString();
                }
            }

            public static string NomeFilaSQSEmailsRejeitados
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.nome_fila_sqs_emails_rejeitados);
                    return config != null ? config.Valor : null;
                }
            }

            public static string NomeFilaSQSMensagensEmail
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterParametro(ParametrosTrinksEnum.nome_fila_sqs_mensagens_email);
                    return config != null ? config.Valor : null;
                }
            }

            public static string UrlBase
            {
                get
                {
                    var valor = Configuracoes["UrlBase"];

                    if (string.IsNullOrWhiteSpace(valor) && HttpContext.Current != null)
                    {
                        var request = HttpContext.Current.Request;
                        valor = request.Url.Scheme + "://" + request.Url.Authority + HttpContext.Current.Request.ApplicationPath;
                    }

                    if (string.IsNullOrWhiteSpace(valor))
                        return null;

                    return valor;
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("envioEmail");
                }
            }

            public static string NomeEmailErrosContaDigital
            {
                get
                {
                    return Configuracoes["NomeEmailErrosContaDigital"].ToString();
                }
            }

            public static string EnderecoEmailErrosContaDigital
            {
                get
                {
                    return Configuracoes["EnderecoEmailErrosContaDigital"].ToString();
                }
            }
        }

        public static class Facebook
        {

            public static string ApplicationId
            {
                get
                {
                    return Configuracoes["ApplicationId"].ToString();
                }
            }

            public static string ApplicationSecret
            {
                get
                {
                    return Configuracoes["ApplicationSecret"].ToString();
                }
            }

            public static string ApplicationSecretAccountKit
            {
                get
                {
                    return Configuracoes["ApplicationSecretAccountKit"].ToString();
                }
            }

            public static string LinkParaFotoPerfil
            {
                get
                {
                    return Configuracoes["LinkParaFotoPerfil"].ToString();
                }
            }

            public static string[] ImagensCompartilhamento
            {
                get
                {
                    var range = new ParametrosTrinks<List<int>>(ParametrosTrinksEnum.range_de_imagens_compartilhamento_facebook).ObterValor();
                    var retorno = new List<string>();
                    var urlBase = ConfiguracoesTrinks.EnvioEmail.UrlBase + "/Content/Face/";
                    if (range != null && range.Count == 2)
                    {
                        var inicio = range[0];
                        var fim = range[1];

                        for (int i = inicio; i <= fim; i++)
                        {
                            retorno.Add(urlBase + i + ".png");
                        }
                    }

                    return retorno.ToArray();
                }
            }

            public static bool ImagensCompartilhamentoHabilitadas
            {
                get
                {
                    var range = new ParametrosTrinks<string>(ParametrosTrinksEnum.range_de_imagens_compartilhamento_facebook).ObterValor();

                    return !string.IsNullOrWhiteSpace(range);
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("facebook");
                }
            }
        }

        public static class Fotos
        {

            public static string CaminhoWebFotos
            {
                get
                {
                    return Configuracoes["CaminhoWebFotos"];
                }
            }

            public static string ExtensoesUploadFoto
            {
                get
                {
                    return Configuracoes["ExtensoesUploadFoto"].ToString();
                }
            }

            public static int QuantidadeMaximaFotos
            {
                get
                {
                    int quantidadeMaximaFotos = 0;
                    string quantidadeMaximaFotosString = Configuracoes["QuantidadeMaximaFotos"].ToString();
                    int.TryParse(quantidadeMaximaFotosString, out quantidadeMaximaFotos);

                    return quantidadeMaximaFotos;
                }
            }

            public static int TamanhoMaximoUploadFoto
            {
                get
                {
                    return Int32.Parse(Configuracoes["TamanhoMaximoUploadFoto"]);
                }
            }

            public static int TamanhoMinimoUploadFoto
            {
                get
                {
                    return Int32.Parse(Configuracoes["TamanhoMinimoUploadFoto"]);
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("fotos");
                }
            }
        }

        public static class FTP_Portabilidade
        {

            public static string CaminhoLocalParaDownload
            {
                get
                {
                    return (Configuracoes["CaminhoLocalParaDownload"]).ToString();
                }
            }

            public static string NomeArquivoDownload
            {
                get
                {
                    return (Configuracoes["NomeArquivoDownload"]).ToString();
                }
            }

            public static string NomeArquivoFinal
            {
                get
                {
                    return (Configuracoes["NomeArquivoFinal"]).ToString();
                }
            }

            public static string Password
            {
                get
                {
                    return (Configuracoes["Password"]).ToString();
                }
            }

            public static string PathFTP
            {
                get
                {
                    return (Configuracoes["PathFTP"]).ToString();
                }
            }

            public static string Username
            {
                get
                {
                    return (Configuracoes["Username"]).ToString();
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.AppSettings;
                }
            }
        }

        public static class Geral
        {

            public static bool AtivarContaSemValidacao
            {
                get
                {
                    string parameter = Configuracoes["AtivarContaSemValidacao"];
                    return (parameter != null && "true".Equals(parameter)) ? true : false;
                }
            }

            public static int DiasGratisDaTabelaPadrao
            {
                get
                {
                    var planoPadrao = Domain.Cobranca.PlanoAssinaturaRepository.Load(IdPlanoPadrao);
                    return planoPadrao.DiasGratisInicial;
                }
            }

            public static int IdPlanoPadrao
            {
                get
                {
                    return IdPlanoCupomParceria()
                        ?? IdPlanoTesteAB()
                        ?? new ParametrosTrinks<int>(ParametrosTrinksEnum.id_plano_assinatura_padrao).ObterValor();
                }
            }

            public static string IdDoEstabelecimentoCadastradoKey
            {
                get
                {
                    return idDoEstabelecimentoCadastradoKey;
                }
            }

            public static string IdContaDoResponsavelFinanceiroDoEstabelecimentoCadastradoKey
            {
                get
                {
                    return idContaDoResponsavelFinanceiroDoEstabelecimentoCadastradoKey;
                }
            }

            public static string TesteABPlanoAssinaturaKey
            {
                get
                {
                    return testeABPlanoAssinaturaKey;
                }
            }

            private static int? IdPlanoCupomParceria()
            {
                if (HttpContext.Current == null)
                    return null;

                var cupom = Domain.WebContext.DadosFixos("cupom_parceria") as string;

                if (cupom != null && !string.IsNullOrEmpty(cupom))
                {
                    var parceria = Domain.Cobranca.ParceriaTrinksRepository.ObterParceriaAtivaPorCupom(cupom);

                    if (parceria?.PlanoAssinatura != null)
                        return parceria.PlanoAssinatura.IdPlano;
                }

                return null;
            }

            private const string testeABPlanoAssinaturaKey = "teste_ab_plano_assinatura";
            private const string idDoEstabelecimentoCadastradoKey = "id_estabelecimento_cadastrado";
            private const string idContaDoResponsavelFinanceiroDoEstabelecimentoCadastradoKey = "id_conta_do_responsavel_financeiro_do_estabelecimento_cadastrado";

            private static int? IdPlanoTesteAB()
            {
                if (HttpContext.Current == null)
                    return null;

                if (!Domain.TestesAB.TesteABPlanoAssinaturaService.TesteEstaHabilitado())
                    return null;

                var testeABPlanoAssinatura = Domain.WebContext.DadosFixos(testeABPlanoAssinaturaKey) as string;
                var isValid = int.TryParse(testeABPlanoAssinatura?.DecodeBase64StringToNormalString(), out int idPlano);

                if (isValid && Domain.TestesAB.TesteABPlanoAssinaturaService.ValidarIdPlano(idPlano))
                {
                    try
                    {
                        var isValidEstabelecimentoId = int.TryParse(Domain.WebContext.DadosFixos(IdDoEstabelecimentoCadastradoKey) as string, out int idEstabelecimento);

                        if (isValidEstabelecimentoId && !Domain.TestesAB.TesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository.Queryable().Any(t => t.Estabelecimento.IdEstabelecimento == idEstabelecimento))
                        {
                            var testeAbAssinaturaPlano = Domain.TestesAB.TesteABAssinaturaPlanoAssinaturaRepository.Load(idPlano);

                            Domain.TestesAB.TesteABAssinaturaPlanoAssinaturaEstabelecimentoRepository.SaveNew(
                                new TestesAB.TesteABAssinaturaPlanoAssinaturaEstabelecimento
                                {
                                    DataQueEntrouNoTeste = DateTime.Now,
                                    Plano = testeAbAssinaturaPlano.Plano,
                                    Estabelecimento = Estabelecimento.Find(idEstabelecimento),
                                    TesteABAssinaturaPlanoAssinatura = testeAbAssinaturaPlano,
                                });
                        }
                    }
                    catch (Exception) { }

                    return idPlano;
                }

                var idProximoPlano = Domain.TestesAB.TesteABPlanoAssinaturaService.ObterProximoPlanoPadraoATestar();

                if (!idProximoPlano.HasValue)
                    return null;

                var expiracaoEmDias = (int)(Domain.TestesAB.TesteABPlanoAssinaturaService.DataLimiteParaOTeste() - DateTime.Today).TotalDays;

                Domain.WebContext.DadosFixos(testeABPlanoAssinaturaKey, idProximoPlano.ToString().EncodeToBase64String(), expirationDays: expiracaoEmDias);

                return idProximoPlano;
            }

            public static int DiasGratisDaTabelaPadraoComProgramaDeFidelidade
            {
                get
                {
                    var planoPadraoComProgramaDeFidelidade = Domain.Cobranca.PlanoAssinaturaRepository.ObterPlanoDeAssinaturaPadraoComProgramaFidelidade();
                    return planoPadraoComProgramaDeFidelidade != null ? planoPadraoComProgramaDeFidelidade.DiasGratisInicial : 0;
                }
            }

            public static string LinkDeApresentacao
            {
                get
                {
                    var link = new ParametrosTrinks<string>(ParametrosTrinksEnum.link_apresentacao_trinks).ObterValor();
                    return link;
                }
            }

            public static bool DisableHomeTemporaria
            {
                get
                {
                    string parameter = Configuracoes["DisableHomeTemporaria"];
                    return (parameter != null && "true".Equals(parameter)) ? true : false;
                }
            }

            public static bool HabilitaEnvioEmailAWS
            {
                get
                {
                    var parameter = Configuracoes["HabilitaEnvioEmailAWS"];
                    return (parameter == null || String.Equals(parameter, "true", StringComparison.CurrentCultureIgnoreCase));
                }
            }

            public static bool HabilitaMarketingAniversariantes
            { // Temporário
                get
                {
                    var parameter = Configuracoes["HabilitaMarketingAniversariantes"];
                    return (parameter == null || String.Equals(parameter, "true", StringComparison.CurrentCultureIgnoreCase));
                }
            }

            public static bool HabilitaMonitoracaoHttp
            {
                get
                {
                    var parameter = Configuracoes["HabilitaMonitoracaoHttp"];
                    return (parameter == null || String.Equals(parameter, "true", StringComparison.CurrentCultureIgnoreCase));
                }
            }

            public static bool HabilitaSalesForce
            {
                get
                {
                    var parameter = Configuracoes["HabilitaSalesForce"];
                    return (parameter != null && "true".Equals(parameter)) ? true : false;
                }
            }

            public static string TESTETRINKSFABRICA
            {
                get
                {
                    var parameter = Configuracoes["TESTETRINKSFABRICA"];
                    return parameter;
                }
            }

            public static bool PermiteDefinicaoDeDataAtual
            {
                get
                {
                    var parameter = Configuracoes["PermiteDefinicaoDeDataAtual"];
                    return (parameter != null && "true".Equals(parameter)) ? true : false;
                }
            }

            /// <summary>
            /// Quando esta configuração tem o valor "false":
            ///   1. Novos eventos não terão as notificações encaminhadas para fila de disparo no SQS.
            ///      Observar que as notificações continuarão sendo registradas no banco de dados para exibição na tela de notificações do aplicativo.
            ///   2. A fila de disparo do SQS não será consumida. As mensagens existentes na fila não serão processadas.
            /// Isso é válido tanto para o aplicativo B2B como para o B2C (incluindo aplicativos de franquias).
            /// </summary>
            public static bool EnviarNotificacoesPush
            {
                get
                {
                    return new ParametrosTrinks<bool>(ParametrosTrinksEnum.push_envio_ativo).ObterValor();
                }
            }

            public static string CaminhoCertificadoNotificacoesPushiOS
            {
                get
                {
                    var parameter = Configuracoes["CaminhoCertificadoNotificacoesPushiOS"];
                    return parameter;
                }
            }

            public static int IntervaloMaximoDoRelatorioDeAgendamentos
            {
                get
                {
                    int IntervaloMaximoDoRelatoriosDeAgendamentos = 60;
                    Int32.TryParse(Configuracoes["IntervaloMaximoDoRelatorioDeAgendamentos"], out IntervaloMaximoDoRelatoriosDeAgendamentos);
                    return IntervaloMaximoDoRelatoriosDeAgendamentos;
                }
            }

            public static int QuantidadeMaximaPaginasExibirNoPaginador
            {
                get
                {
                    int quantidadeMaximaPaginasExibir = 10;
                    Int32.TryParse(Configuracoes["QuantidadeMaximaPaginasExibirNoPaginador"], out quantidadeMaximaPaginasExibir);
                    return quantidadeMaximaPaginasExibir;
                }
            }

            public static bool ShowJSError
            {
                get
                {
                    bool showJSError = false;

                    bool.TryParse(Configuracoes["ShowJSError"], out showJSError);

                    return showJSError;
                }
            }

            public static int TamanhoMinimoSenha
            {
                get
                {
                    int tamanhoMinimoSenha = 0;
                    Int32.TryParse(Configuracoes["TamanhoMinimoSenha"], out tamanhoMinimoSenha);
                    return tamanhoMinimoSenha;
                }
            }

            public static string UniNFEPath
            {
                get
                {
                    var uniNfePath = Configuracoes["UniNFE_Path"];

                    //if(string.IsNullOrWhiteSpace(uniNfePath))
                    //    throw new Exception("Falta configuração do caminho no UniNFE");

                    return uniNfePath;
                }
            }

            public static string UniNFEPathBaseMunicipios
            {
                get
                {
                    var uniNfePathBaseMunicipios = Configuracoes["UniNFE_Path_Base_Municipios"];

                    if (string.IsNullOrWhiteSpace(uniNfePathBaseMunicipios))
                        throw new Exception("Falta configuração do caminho da base de municípios no UniNFE");

                    return uniNfePathBaseMunicipios;
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return ConfigurationManager.AppSettings;
                }
            }

            internal static bool HabilitaEnvioDeCopiaDeEmailParaFabrica
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("habilita_envio_copia_email_para_fabrica");
                    return config == null ? false : bool.Parse(config.Valor);
                }
            }

            public static bool PriorizaEstabelecimentosComMaisAgendamentosOnline
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("prioriza_estabelecimentos_com_mais_agendamentos_online");
                    return config == null ? true : bool.Parse(config.Valor);
                }
            }

            public static string ApiB2BBaseUrl
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("ApiB2BBaseUrl");
                    var retorno = config?.Valor;

                    if (retorno == null)
                    {
                        retorno = HttpContext.Current.Request.Url.GetLeftPart(System.UriPartial.Authority) + HttpContext.Current.Request.ApplicationPath.ToLower().Replace("/trinks", "").TrimEnd('/') + "/ProApi";
                    }

                    return retorno;
                }
            }

            public static bool SinalizaSeLembreteEhShortOuLongNaMensagem
            {
                get
                {
                    var config = ConfigurationManager.AppSettings["SinalizaSeLembreteEhShortOuLongNaMensagem"];
                    if (config == "true")
                        return true;
                    return false;
                }
            }

            private const string ChaveDeVersaoDoFluxoDeCadastroNaQueryString = "v";
            private const string VersaoDoNovoFluxoDeCadastro = "2";

            public static bool NovoFluxoCadastroAtivo
            {
                get
                {
                    bool isWebRequest = HttpContext.Current != null;

                    if (isWebRequest && HttpContext.Current.Request.QueryString[ChaveDeVersaoDoFluxoDeCadastroNaQueryString] == VersaoDoNovoFluxoDeCadastro)
                    {
                        return true;
                    }

                    // "0101"
                    var localhost = 0;      // 0 - localhost
                    var trinksH = 1;        // 1 - H
                    var trinksBeta = 2;     // 2 - beta
                    var trinksProd = 3;     // 3 - producao

                    var novoFluxoCadastroAtivo = new ParametrosTrinks<string>(ParametrosTrinksEnum.novo_fluxo_cadastro_ativo).ObterValor();
                    if (novoFluxoCadastroAtivo.Length != 4)
                        return false;

                    if (HttpContext.Current != null)
                    {
                        var request = HttpContext.Current.Request;
                        var url = request.Url.Scheme + "://" + request.Url.Authority;
                        //var url = request.Url.AbsoluteUri;

                        if (url.IndexOf("beta.trinks") >= 0)
                            return novoFluxoCadastroAtivo[trinksBeta] == '1';

                        if (url.IndexOf("trinks.com") >= 0)
                            return novoFluxoCadastroAtivo[trinksProd] == '1';

                        if (url.IndexOf("trinks-") >= 0 || url.IndexOf("trinks.io") > 0)
                            return novoFluxoCadastroAtivo[trinksH] == '1';

                        if (url.IndexOf("localhost") >= 0)
                            return novoFluxoCadastroAtivo[localhost] == '1';
                    }

                    return false;
                }
            }

            public static string TokenWebService
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("token_webservice");
                    return config?.Valor ?? throw new ArgumentNullException("O parâmetro 'token_webservice' não foi configurado na tabela de configurações");
                }
            }

            public static bool IdentificacaoNoHubspotAtivada
                => bool.TryParse(ConfigurationManager.AppSettings["IdentificacaoNoHubspotAtivada"], out var ativada) ?
                    ativada : false;
        }

        public static class HotSite
        {

            public static string AssemblyHotsite
            {
                get
                {
                    return Configuracoes["AssemblyHotsite"];
                }
            }

            public static List<string> BlackListUrlsHotsite
            {
                get
                {
                    List<String> lista = new List<string>();
                    var blackLista = Configuracoes["BlackListUrlsHotsite"];

                    foreach (String item in blackLista.Split(';'))
                        if (!String.IsNullOrEmpty(item))
                            lista.Add(item);

                    return lista;
                }
            }

            public static int IdConfiguracaoHotsiteAderenciaDefault
            {
                get
                {
                    return Int32.Parse(Configuracoes["IdConfiguracaoHotsiteAderenciaDefault"]);
                }
            }

            public static int IdConfiguracaoHotsiteInicioMarcosDefault
            {
                get
                {
                    return Int32.Parse(Configuracoes["IdConfiguracaoHotsiteInicioMarcosDefault"]);
                }
            }

            public static int IdConfiguracaoHotsiteIntervaloComponenteFotos
            {
                get
                {
                    return Int32.Parse(Configuracoes["IdConfiguracaoHotsiteIntervaloComponenteFotos"]);
                }
            }

            public static int IdConfiguracaoHotsiteIntervaloDefault
            {
                get
                {
                    return Int32.Parse(Configuracoes["IdConfiguracaoHotsiteIntervaloDefault"]);
                }
            }

            public static int IdConfiguracaoHotsiteUniversoDefault
            {
                get
                {
                    return Int32.Parse(Configuracoes["IdConfiguracaoHotsiteUniversoDefault"]);
                }
            }

            public static int IdHotsiteTemaDefault
            {
                get
                {
                    return Int32.Parse(Configuracoes["IdHotsiteTemaDefault"]);
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("hotsite");
                }
            }

            public static bool TermoPertenceABlackListUrlHotsite(String termo)
            {
                return BlackListUrlsHotsite.Any(p => p == termo);
            }
        }

        public static class Marketing
        {

            public static int MinutosToleranciaEnvioCampanha
            {
                get
                {
                    var config =
                        Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro(
                            "minutos_tolerancia_envio_campanha");
                    return config == null ? 60 : int.Parse(config.Valor);
                }
            }

            public static int MinutosToleranciaEnvioCampanhaAniversariantes
            {
                get
                {
                    var config =
                        Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro(
                            "minutos_tolerancia_envio_campanha_aniversariantes");
                    return config == null ? 240 : int.Parse(config.Valor);
                }
            }

            public static int QuantidadeRegistrosPaginacaoMarketing
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("qtd_registros_pagina_marketing");
                    return config == null ? 10 : int.Parse(config.Valor);
                }
            }
        }

        public static class Navegacao
        {

            public static string UrlIndex
            {
                get { return (ConfigurationManager.AppSettings["UrlIndex"]).ToString(); }
            }
        }

        public static class ReCaptcha
        {

            public static string PrivateKey
            {
                get { return (ConfigurationManager.AppSettings["reCaptchaPrivateKey"]).ToString(); }
            }

            public static string PublicKey
            {
                get { return (ConfigurationManager.AppSettings["reCaptchaPublicKey"]).ToString(); }
            }
        }

        public static class SMSGateway
        {

            public static string HttpRequestSendMessage
            {
                get
                {
                    return Configuracoes["HttpRequestSendMessage"].ToString();
                }
            }

            public static string Password
            {
                get
                {
                    return Configuracoes["Password"].ToString();
                }
            }

            public static string Server
            {
                get
                {
                    return Configuracoes["Server"].ToString();
                }
            }

            public static SMSGatewayTipo Tipo
            {
                get
                {
                    int smsGatewayTipo = 0;
                    Int32.TryParse(Configuracoes["Tipo"].ToString(), out smsGatewayTipo);
                    return (SMSGatewayTipo)smsGatewayTipo;
                }
            }

            public static string Username
            {
                get
                {
                    return Configuracoes["Username"].ToString();
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("smsGateway");
                }
            }
        }

        public static class TratamentoErro
        {

            public static string AssuntoEmailErro
            {
                get
                {
                    return Configuracoes["AssuntoEmailErro"].ToString();
                }
            }

            public static string DestinatarioEmailErro
            {
                get
                {
                    return Configuracoes["DestinatarioEmailErro"].ToString();
                }
            }

            public static bool DeveEnviarEmailErro
            {
                get
                {
                    string deveEnviar = Configuracoes["DeveEnviarEmailErro"];

                    if (deveEnviar != null && deveEnviar.ToLower() == "true")
                        return true;
                    else
                        return false;
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("tratamentoErro");
                }
            }
        }

        public static class NFCe
        {

            public static bool HabilitarUsoDoZeus
            {
                get
                {
                    bool result;
                    var habilita = bool.TryParse(Configuracoes["UtilizarZeus"], out result);
                    return result ? habilita : false;
                }
            }

            public static bool AmbienteProducao
            {
                get
                {
                    bool result;
                    var producao = bool.TryParse(Configuracoes["NFCeAmbienteProducao"], out result);
                    return result ? producao : true;
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return ConfigurationManager.AppSettings;
                }
            }
        }

        public static class NFSe
        {

            public static List<string> CodigosDeErroNaEmissao
            {
                get
                {
                    var codigos = new ParametrosTrinks<List<string>>(ParametrosTrinksEnum.codigos_erro_retorno_nfse).ObterValor() ?? new List<string>();
                    return codigos;
                }
            }
        }

        public static class oAuth
        {

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return ConfigurationManager.GetSection("oAuth") as NameValueCollection;
                }
            }

            public static bool PermiteAcessoSemSSL
            {
                get
                {
                    if (Configuracoes == null || Configuracoes["permiteAcessoSemSSL"] == null)
                        return true;

                    return bool.Parse(Configuracoes["permiteAcessoSemSSL"]);
                }
            }

            public static int QuantidadeDeMinutosParaExpiracaoDoAccessToken
            {
                get
                {
                    if (Configuracoes == null || Configuracoes["permiteAcessoSemSSL"] == null)
                        return 600;
                    return Int32.Parse(Configuracoes["quantidadeDeMinutosParaExpiracaoDoAccessToken"]);
                }
            }
        }

        public static class Geolocation
        {

            public static string GoogleMapsGeolocationApiKey
            {
                get { return (ConfigurationManager.AppSettings["GoogleMapsGeolocationApiKey"]).ToString(); }
            }
        }

        public static class GoogleMaps
        {

            public static string GoogleMapsStaticApiFQDN
            {
                get { return (ConfigurationManager.AppSettings["GoogleMapsStaticApiFQDN"]).ToString(); }
            }
        }

        public static class Google
        {

            public static string GoogleReserveMerchantSFTPUsername
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_merchant_sftp_username");
                    return config == null ? "" : config.Valor;
                }
            }

            public static string GoogleReserveServicesSFTPUsername
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_services_sftp_username");
                    return config == null ? "" : config.Valor;
                }
            }

            public static string GoogleReserveAvailabilitySFTPUsername
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_availability_sftp_username");
                    return config == null ? "" : config.Valor;
                }
            }
            public static string GoogleReserveGenericSFTPUsername
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_generic_sftp_username");
                    return config == null ? "" : config.Valor;
                }
            }

            public static string GoogleReserveSFTPSenha
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_sftp_senha");
                    return config == null ? "" : config.Valor;
                }
            }

            public static string GoogleReserveSFTPHost
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_sftp_host");
                    return config == null ? "" : config.Valor;
                }
            }

            public static int GoogleReserveSFTPPorta
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_sftp_porta");
                    return config == null ? 0 : int.Parse(config.Valor);
                }
            }

            public static string GoogleReserveSFTPArquivoCertificado
            {
                get
                {
                    var config = Domain.Pessoas.ParametrizacaoTrinksRepository.ObterPorNomeParametro("google_reserve_sftp_arquivo_certificado");
                    return config == null ? "" : config.Valor;
                }
            }

            public static int GoogleReservegRPCPort
            {
                get
                {
                    int retorno;
                    int.TryParse(ConfigurationManager.AppSettings["google_reserve_grpc_port"], out retorno);
                    return retorno;
                }
            }

            public static List<int> EstabelecimentosAtivosReserve
            {
                get
                {
                    var lista = new ParametrosTrinks<List<int>>(ParametrosTrinksEnum.google_reserve_estabelecimentos_ativos).ObterValor();

                    return lista;
                }
            }

            public static string ArquivosFeedPath
            {
                get
                {
                    return ConfigurationManager.AppSettings["google_reserve_feed_files_path"];
                }
            }

            public static string GoogleMapsBookingAPICredentialsJson
            {
                get
                {
                    return ConfigurationManager.AppSettings["google_mapsbooking_api_credential_json"]
                        ?? "~/app_data/google_mapsbooking_api_credential.json";
                }
            }

            public static string PartnerId
            {
                get
                {
                    return ConfigurationManager.AppSettings["google_reserve_partner_id"] ?? "10000070";
                }
            }

            public static string GoogleMapsBookingAPI_SNSBooking
            {
                get
                {
                    return ConfigurationManager.AppSettings["google_reserve_booking_sns"] ?? "arn:aws:sns:us-east-1:749532251542:google_reserve_booking";
                }
            }

            public static string GoogleMapsBookingAPI_SNSAvailability
            {
                get
                {
                    return ConfigurationManager.AppSettings["google_reserve_availability_sns"] ?? "arn:aws:sns:us-east-1:749532251542:dev_google_reserve_availability";
                }
            }

            public static string BookingAPILogin
            {
                get
                {
                    var valor = new ParametrosTrinks<string>(ParametrosTrinksEnum.google_reserve_bookingapi_login).ObterValor();
                    return valor ?? "google";
                }
            }

            public static string BookingAPISenha
            {
                get
                {
                    var valor = new ParametrosTrinks<string>(ParametrosTrinksEnum.google_reserve_bookingapi_senha).ObterValor();
                    return valor ?? "123456";
                }
            }
        }

        public static class ElasticSearch
        {

            public static string UriBusca
            {
                get { return (ConfigurationManager.AppSettings["elasticSearchBusca:Uri"])?.ToString(); }
            }

            public static string UriLog
            {
                get { return (ConfigurationManager.AppSettings["elasticSearchLog:Uri"])?.ToString(); }
            }
        }

        public static class Logs
        {
            public static string Uri
            {
                get { return (ConfigurationManager.ConnectionStrings["LogsSistema"].ConnectionString); }
            }

            public static bool AtivarSelflogDoSerilog
            {
                get
                {
                    return new ParametrosTrinks<bool>(ParametrosTrinksEnum.serilog_selflog_logs_ativado).ObterValor();
                }
            }

            public static string CaminhoDoTxtParaSalvarSelflogDoSerilog
            {
                get
                {
                    return new ParametrosTrinks<string>(ParametrosTrinksEnum.serilog_selflog_logs_caminhotxt).ObterValor();
                }
            }
        }

        public static class LogsV2
        {
            public static string Uri => (ConfigurationManager.ConnectionStrings["LogsSistema"].ConnectionString);

            public static bool AtivarSelflogDoSerilog => new ParametrosTrinks<bool>(ParametrosTrinksEnum.serilog_selflog_logs_ativado_v2).ObterValor();

            public static string CaminhoDoTxtParaSalvarSelflogDoSerilog => new ParametrosTrinks<string>(ParametrosTrinksEnum.serilog_selflog_logs_caminhotxt_v2).ObterValor();

            public static bool AtivarLogsPostgresql => new ParametrosTrinks<bool>(ParametrosTrinksEnum.ativar_logs_postgresql_v2).ObterValor();

            public static int TempoEmSegundosParaProcessamentoDeBatches => new ParametrosTrinks<int>(ParametrosTrinksEnum.serilog_tempo_em_segundos_para_processamento_de_batches_v2).ObterValor();

            public static int TamanhoMaximoDoBatch => new ParametrosTrinks<int>(ParametrosTrinksEnum.serilog_tamanho_maximo_do_batch_v2).ObterValor();
        }

        public static class Metrics
        {

            public static string Uri
            {
                get { return (ConfigurationManager.ConnectionStrings["Metrics"].ConnectionString); }
            }

            public static bool RegistroDeMetricasAtivo
            {
                get
                {
                    return new ParametrosTrinks<bool>(ParametrosTrinksEnum.metricas_registro_ativo).ObterValor();
                }
            }

            public static bool RegistroDeMetricasAppProAtivo
            {
                get
                {
                    return new ParametrosTrinks<bool>(ParametrosTrinksEnum.metricas_registro_apppro_ativo).ObterValor();
                }
            }

            public static int TempoEmSegundosParaProcessamentoDeBatches
            {
                get
                {
                    return new ParametrosTrinks<int>(ParametrosTrinksEnum.metricas_periodo_batch_em_segundos).ObterValor();
                }
            }

            public static int TamanhoMaximoDoBatch
            {
                get
                {
                    return new ParametrosTrinks<int>(ParametrosTrinksEnum.metricas_tamanho_maximo_batch).ObterValor();
                }
            }

            public static bool AtivarSelflogDoSerilog
            {
                get
                {
                    return new ParametrosTrinks<bool>(ParametrosTrinksEnum.metricas_serilog_selflog_ativado).ObterValor();
                }
            }

            public static string CaminhoDoTxtParaSalvarSelflogDoSerilog
            {
                get
                {
                    return new ParametrosTrinks<string>(ParametrosTrinksEnum.metricas_serilog_selflog_caminhotxt).ObterValor();
                }
            }

            public static string TituloPromocaoMarketing
            {
                get
                {
                    return new ParametrosTrinks<string>(ParametrosTrinksEnum.titulo_promocao_marketing).ObterValor();
                }
            }
        }

        public static class TrinksLog
        {
            public static string IndicesPreffix => ConfigurationManager.AppSettings["trinkslog:indicesPreffix"];
        }

        public static class Redis
        {

            public static bool HabilitarIdempotencia
            {
                get
                {
                    var setting = ConfigurationManager.AppSettings["idempotencia:Habilitar"];
                    return (setting != null ? setting.ToString() : "false") == "true";
                }
            }

            public static string Host
            {
                get
                {
                    var setting = ConfigurationManager.AppSettings["redis:Server"];
                    return setting != null ? setting.ToString() : "localhost";
                }
            }
        }

        public static class LinkDePagamento
        {
            public static Uri UrlBase => new Uri(Configuracoes["UrlLinkDePagamento"]);
            private static NameValueCollection Configuracoes => (NameValueCollection)ConfigurationManager.GetSection("servicosInternos");
        }

        public static class WhatsApp
        {

            public static string UrlBase
            {
                get
                {
                    return Configuracoes["urlBase"];
                }
            }

            private static NameValueCollection Configuracoes
            {
                get
                {
                    return (NameValueCollection)ConfigurationManager.GetSection("whatsapp");
                }
            }
        }

        public static class GoogleAnalytics
        {
            public static string GoogleAnalyticsId
            {
                get
                {
                    return (ConfigurationManager.AppSettings["GoogleAnalyticsId"]).ToString();
                }
            }
        }
        
        public static class PromocaoOnline
        {
            public static string UrlBase => Configuracoes["UrlPromocaoOnline"];
            private static NameValueCollection Configuracoes => (NameValueCollection)ConfigurationManager.GetSection("servicosInternos");
        }
    }
}