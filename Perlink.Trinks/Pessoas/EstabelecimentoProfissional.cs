﻿using Castle.ActiveRecord;
using Perlink.Trinks.Disponibilidade;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Pessoas.ExtensionMethods;
using Perlink.Trinks.Pessoas.VO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Security;

namespace Perlink.Trinks.Pessoas
{

    [ActiveRecord("Estabelecimento_Profissional", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true,
        Cache = CacheEnum.ReadWrite, Mutable = true)]
    [Serializable]
    public class EstabelecimentoProfissional : ActiveRecordBase<EstabelecimentoProfissional>
    {
        private bool _ativo;

        private Dictionary<IntervaloData, DisponibilidadeEstabelecimentoProfissional> _disponibilidades =
            new Dictionary<IntervaloData, DisponibilidadeEstabelecimentoProfissional>();

        private UsuarioEstabelecimento _usuarioEstabelecimento;
        private string _codigoInterno;

        public EstabelecimentoProfissional()
        {
            HorarioTrabalhoLista = new List<HorarioTrabalho>();
            EstabelecimentoProfissionalServicoLista = new List<EstabelecimentoProfissionalServico>();
            EstabelecimentoAssistenteServicoLista = new List<EstabelecimentoAssistenteServico>();
            Estabelecimento = new Estabelecimento();
            Profissional = new Profissional();
            Ativo = true;
            PodeAddFotosServicoPATPainel = true;
            UtilizaMenuLateral = Estabelecimento.EstabelecimentoConfiguracaoGeral.ProfissionaisCadastradosVeraoMenuLateralEHeader;
        }

        /// <summary>
        ///     Construtor utilizado nos testes
        /// </summary>
        /// <param name="estabelecimento"></param>
        /// <param name="profissional"></param>
        public EstabelecimentoProfissional(Estabelecimento estabelecimento, Profissional profissional)
            : this()
        {
            Estabelecimento = estabelecimento;
            Profissional = profissional;
        }

        [Property("ativo")]
        public virtual bool Ativo
        {
            get { return _ativo; }
            set
            {
                _ativo = value;
                if (!value && !DataInativacao.HasValue)
                    DataInativacao = Calendario.Agora();
                else if (value && DataInativacao.HasValue)
                    DataInativacao = null;
            }
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_estabelecimento_profissional", ColumnType = "Int32")]
        public virtual Int32 Codigo { get; set; }

        [Property("codigo_acesso_painel", ColumnType = "String")]
        public virtual string CodigoDeAcessoAoPainelAtendimento { get; set; }

        [Property("cod_integracao", ColumnType = "String")]
        public virtual string CodigoIntegracao { get; set; }

        [Property("codigo_interno")]
        public virtual string CodigoInterno
        {
            get { return _codigoInterno; }
            set { _codigoInterno = value; }
        }

        [Property("valor_comissao_padrao", ColumnType = "Decimal", NotNull = false)]
        public virtual Decimal ComissaoPadrao { get; set; }

        [Property("ctps", ColumnType = "String")]
        public virtual string Ctps { get; set; }

        [Property("ctps_serie", ColumnType = "String")]
        public virtual string CtpsSerie { get; set; }

        [Property("data_cadastro")]
        public virtual DateTime? DataCadastro { get; set; }

        [Property("rg_data_expedicao", ColumnType = "DateTime")]
        public virtual DateTime? DataExpedicaoRG { get; set; }

        [Property("data_inativacao")]
        public virtual DateTime? DataInativacao { get; set; }

        [Property("dt_nascimento", ColumnType = "DateTime")]
        public virtual DateTime? DataNascimento { get; set; }

        /// <summary>
        ///     Indica se o Profissional é Responsável pelo Estabelecimento
        /// </summary>
        public virtual bool EhResponsavelEstabelecimento
        {
            get { return (UsuarioEstabelecimento != null && UsuarioEstabelecimento.EhResponsavel); }
            set { throw new NotImplementedException(); }
        }

        /// <summary>
        ///     Indica se o Profissional é também Usuário Administrativo do Estabelecimento
        /// </summary>
        public virtual bool EhUsuarioAdministrativoEstabelecimento
        {
            get { return (UsuarioEstabelecimento != null); }
            set { throw new NotImplementedException(); }
        }

        public virtual Endereco Endereco
        {
            get { return Profissional.PessoaFisica.Enderecos.FiltrarPorDono(Estabelecimento.PessoaJuridica.IdPessoa); }

            set {
                AtualizarOuAdicionarEndereco(value);
            }
        }

        [BelongsTo("id_estabelecimento", Lazy = FetchWhen.OnInvoke)]
        public virtual Estabelecimento Estabelecimento { get; set; }

        [Obsolete("Não utilizar. Utilizar o EstabelecimentoProfissionalServicoRepository")]
        [HasMany(typeof(EstabelecimentoProfissionalServico), ColumnKey = "id_estabelecimento_profissional",
            Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO",
            Table = "Estabelecimento_Profissional_Servico", Inverse = true, Lazy = true)]
        public virtual IList<EstabelecimentoProfissionalServico> EstabelecimentoProfissionalServicoLista { get; set; }

        [HasMany(typeof(EstabelecimentoAssistenteServico), ColumnKey = "id_estabelecimento_profissional",
            Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO",
            Table = "Estabelecimento_Assistente_Servico", Inverse = true, Lazy = true)]
        public virtual IList<EstabelecimentoAssistenteServico> EstabelecimentoAssistenteServicoLista { get; set; }

        [BelongsTo("id_estado_civil", Lazy = FetchWhen.OnInvoke)]
        public virtual EstadoCivil EstadoCivil { get; set; }

        public virtual FotoPessoa FotoPrincipal
        {
            get
            {
                var foto = Profissional.FotoPrincipal;

                if (!foto.Codigo.HasValue)
                {
                    foto = new FotoPessoa { Pessoa = Profissional.PessoaFisica };
                }

                return foto;
            }

            set { throw new NotImplementedException(); }
        }

        [Obsolete("Não utilizar. Utilizar o HorarioTrabalhoRepository")]
        public virtual List<HorarioTrabalho> HorariosTrabalhoProfissionalAtivos
        {
            get { return HorarioTrabalhoLista.Ativos(); }

            set { throw new NotImplementedException(); }
        }

        [Obsolete("Não utilizar. Utilizar o HorarioTrabalhoRepository")]
        [HasMany(typeof(HorarioTrabalho), ColumnKey = "id_estabelecimento_profissional",
            Cascade = ManyRelationCascadeEnum.AllDeleteOrphan, Schema = "DBO", Table = "Horario_Trabalho",
            Inverse = true, Lazy = true)]
        public virtual IList<HorarioTrabalho> HorarioTrabalhoLista { get; set; }

        [Property("obs_do_estabelecimento_sobre_profissional", ColumnType = "String")]
        public virtual string ObservacaoSobreProfissional { get; set; }

        [Property("rg_orgao_expedidor", ColumnType = "String")]
        public virtual string OrgaoExpedidorRG { get; set; }

        [Property("permite_envio_email_para_profissional", ColumnType = "Boolean")]
        public virtual Boolean PermiteEnvioEmail { get; set; }

        [Property("pis", ColumnType = "String")]
        public virtual string PIS { get; set; }

        [Property("pode_visualizar_horarios_area_profissional", ColumnType = "Boolean")]
        public virtual Boolean PodeAcessarMinhaAgenda { get; set; }

        [BelongsTo("id_profissional", Cascade = CascadeEnum.SaveUpdate, Lazy = FetchWhen.OnInvoke)]
        public virtual Profissional Profissional { get; set; }

        [BelongsTo("id_pessoa_juridica", Cascade = CascadeEnum.SaveUpdate, Lazy = FetchWhen.OnInvoke)]
        public virtual PessoaJuridica PessoaJuridica { get; set; }

        [Property("rg", ColumnType = "String")]
        public virtual string RG { get; set; }

        [Property("sexo", ColumnType = "Perlink.Trinks.Pessoas.UserTypes.GeneroUserType, Perlink.Trinks")]
        public virtual Genero Genero { get; set; }

        [Property("recebe_comissao_na_data_prevista_de_recebimento", ColumnType = "Boolean")]
        public virtual Boolean RecebeComissaoNaDataPrevistaDeRecebimento { get; set; }

        [Property("eh_excecao_comissao_na_data_prevista_de_recebimento", ColumnType = "Boolean")]
        public virtual Boolean EhExcecaoComissaoNaDataPrevistaDeRecebimento { get; set; }

        [Property("desconta_taxa_operadora_na_comissao", ColumnType = "Boolean")]
        public virtual Boolean DescontaTaxaOperadoraNaComissao { get; set; }

        [Property("eh_excecao_desconto_taxa_operadora", ColumnType = "Boolean")]
        public virtual Boolean EhExcecaoDescontoTaxaOperadora { get; set; }

        [Property("desconta_taxa_operadora_na_comissao_assistente", ColumnType = "Boolean")]
        public virtual Boolean DescontaTaxaOperadoraNaComissaoAssistente { get; set; }

        [Property("eh_excecao_desconto_taxa_operadora_assistente", ColumnType = "Boolean")]
        public virtual Boolean EhExcecaoDescontoTaxaOperadoraAssistente { get; set; }

        public virtual List<Telefone> Telefones
        {
            get { return Profissional.PessoaFisica.Telefones.Ativos().FiltrarPorDono(Estabelecimento.PessoaJuridica.IdPessoa); }

            set { throw new NotImplementedException(); }
        }

        [Property("data_inicio")]
        public virtual DateTime? DataInicio { get; set; }

        [BelongsTo("id_comissao_tipo", Lazy = FetchWhen.OnInvoke)]
        public virtual TipoComissao TipoComissao { get; set; }

        [BelongsTo("id_forma_relacao_profissional", Cascade = CascadeEnum.All, Lazy = FetchWhen.OnInvoke)]
        public virtual FormaRelacaoProfissional FormaRelacaoProfissional { get; set; }

        [BelongsTo("id_funcao_profissional", Cascade = CascadeEnum.All, Lazy = FetchWhen.OnInvoke)]
        public virtual FuncaoDoProfissional FuncaoProfissional { get; set; }

        public virtual UsuarioEstabelecimento UsuarioEstabelecimento
        {
            get
            {
                return Domain.Pessoas.UsuarioEstabelecimentoRepository.ObterPorPessoaFisica(Profissional.PessoaFisica.IdPessoaFisica, Estabelecimento.IdEstabelecimento);
            }
            set { throw new NotImplementedException(); }
        }

        public string GerarCodigoPadraoDeAcessoAoPainelAtendimento()
        {
            if (string.IsNullOrEmpty(Profissional.PessoaFisica.Cpf))
                throw new Exception("O profissional precisa possuir um cpf para poder gerar o acesso ao PAT");

            string codigoCPF = Profissional.PessoaFisica.Cpf.Substring(0, 4);
            return FormsAuthentication.HashPasswordForStoringInConfigFile(codigoCPF, "SHA1");
        }

        [Property("possui_agenda")]
        public bool PossuiAgenda { get; set; }

        [Property("pode_adicionar_fotos_servico_pat_painel")]
        public bool? PodeAddFotosServicoPATPainel { get; set; }

        [Property("habilita_split_pagamento")]
        public virtual bool HabilitaSplitPagamento { get; set; }

        [Property("codigo_identificacao_pos")]
        public virtual string CodigoIdentificacaoPOS { get; set; } // Stone: Affiliation_Key

        [Property("affiliation_key")]
        public virtual string IdExternosDeBeneficiario { get; set; } // Stone: recipient_key

        [Property("aguardando_retorno_habilitacao_split")]
        public virtual bool AguardandoRetornoDaHabilitacaoDoSplit { get; set; }

        [Property("data_fim_contrato_profissional")]
        public virtual DateTime? DataFimDoContratoDoProfissional { get; set; }

        [Property("utiliza_menu_lateral")]
        public virtual bool UtilizaMenuLateral { get; set; }

        [BelongsTo("tipo_conta_bancaria", Lazy = FetchWhen.OnInvoke, NotNull = false)]
        public virtual TipoContaBancaria TipoDeContaBancaria { get; set; }

        [Property("codigo_banco", NotNull = false)]
        public virtual int? CodigoDoBanco { get; set; }

        [Property("agencia", Length = 20, NotNull = false)]
        public virtual string Agencia { get; set; }

        [Property("agencia_dv", Length = 10, NotNull = false)]
        public virtual string AgenciaDV { get; set; }

        [Property("numero_conta", Length = 20, NotNull = false)]
        public virtual string NumeroConta { get; set; }

        [Property("numero_conta_dv", Length = 10, NotNull = false)]
        public virtual string NumeroContaDV { get; set; }

        [Property("conta_bancaria_mesma_titularidade", NotNull = false)]
        public virtual int? ContaMesmaTitularidade { get; set; }

        [Property("documento_conta_bancaria", NotNull = false)]
        public virtual string DocumentoDaContaBancaria { get; set; }

        [Property("nome_conta_bancaria", NotNull = false)]
        public virtual string NomeDoTitularDaContaBancaria { get; set; }

        public virtual bool FazLancamentoNoPAT()
        {
            return !String.IsNullOrEmpty(CodigoDeAcessoAoPainelAtendimento);
        }

        public bool UsandoSplit
        {
            get
            {
                return HabilitaSplitPagamento &&
                   (!string.IsNullOrEmpty(CodigoIdentificacaoPOS) || EhRecebedorNoConnectPagarme());
            }
            set { }
        }

        public virtual bool VerificarDisponibilidade(IntervaloData intervalo)
        {
            IntervaloDataList resultadoDisponibilidade =
                DisponibilidadeEstabelecimentoProfissional(intervalo).ListarDisponibilidade();

            return resultadoDisponibilidade.Any(f => f.Inicio <= intervalo.Inicio && f.Fim >= intervalo.Fim);
        }

        private DisponibilidadeEstabelecimentoProfissional DisponibilidadeEstabelecimentoProfissional(
            IntervaloData intervalo)
        {
            if (!_disponibilidades.ContainsKey(intervalo))
                _disponibilidades.Add(intervalo, new DisponibilidadeEstabelecimentoProfissional(this, intervalo));

            return _disponibilidades[intervalo];
        }
        private void AtualizarOuAdicionarEndereco(Endereco value)
        {
            if (Profissional == null || Profissional.PessoaFisica == null || Estabelecimento == null || Estabelecimento.PessoaJuridica == null)
            {
                throw new InvalidOperationException("Dados insuficientes para definir o endereço.");
            }

            var enderecos = Profissional.PessoaFisica.Enderecos;
            var enderecoExistente = enderecos?.FiltrarPorDono(Estabelecimento.PessoaJuridica.IdPessoa);

            if (enderecoExistente != null)
            {
                enderecoExistente.Logradouro = value.Logradouro ?? "";
                enderecoExistente.Numero = value.Numero ?? "";
                enderecoExistente.Complemento = value.Complemento ?? "";
                enderecoExistente.Bairro = value.Bairro ?? "";
                enderecoExistente.Cidade = value.Cidade ?? "";
                enderecoExistente.UF = value.UF;
                enderecoExistente.Cep = !string.IsNullOrEmpty(value.Cep) ? value.Cep.Replace("-", "") : "";
                enderecoExistente.Logradouro = value.Logradouro;
                enderecoExistente.TipoLogradouro = value.TipoLogradouro;
                enderecoExistente.Ativo = true;
                enderecoExistente.Pessoa = Profissional.PessoaFisica;
            }
            else
            {
                value.Ativo = true;
                value.Pessoa = Profissional.PessoaFisica;
                enderecos.Add(value);
            }
        }
        public virtual bool ProfissionalRealizaServicoNoEstabelecimento()
        {
            return EstabelecimentoProfissionalServicoLista.Ativos().Count > 0 || EstabelecimentoAssistenteServicoLista.Ativos().Count > 0;
        }

        public virtual void ConfigurarComissoes()
        {
            var configuracao = Estabelecimento.EstabelecimentoConfiguracaoGeral;

            if (!configuracao.PagarComissoesNaDataPrevistaDeRecebimento)
                RecebeComissaoNaDataPrevistaDeRecebimento = configuracao.PagarComissoesNaDataPrevistaDeRecebimento;
            else if (!EhExcecaoComissaoNaDataPrevistaDeRecebimento)
                RecebeComissaoNaDataPrevistaDeRecebimento = configuracao.PagarComissoesNaDataPrevistaDeRecebimento;
            else
                RecebeComissaoNaDataPrevistaDeRecebimento = !configuracao.PagarComissoesNaDataPrevistaDeRecebimento;

            if (!configuracao.ConsiderarDescontoOperadoraNaComissao)
                DescontaTaxaOperadoraNaComissao = configuracao.ConsiderarDescontoOperadoraNaComissao;
            else if (!EhExcecaoDescontoTaxaOperadora)
                DescontaTaxaOperadoraNaComissao = configuracao.ConsiderarDescontoOperadoraNaComissao;
            else
                DescontaTaxaOperadoraNaComissao = !configuracao.ConsiderarDescontoOperadoraNaComissao;

            if (!configuracao.ConsiderarDescontoOperadoraNaComissaoDeAssistentes)
                DescontaTaxaOperadoraNaComissaoAssistente = configuracao.ConsiderarDescontoOperadoraNaComissaoDeAssistentes;
            else if (!EhExcecaoDescontoTaxaOperadoraAssistente)
                DescontaTaxaOperadoraNaComissaoAssistente = configuracao.ConsiderarDescontoOperadoraNaComissaoDeAssistentes;
            else
                DescontaTaxaOperadoraNaComissaoAssistente = !configuracao.ConsiderarDescontoOperadoraNaComissaoDeAssistentes;
        }

        public virtual bool EhRecebedorNoConnectPagarme()
        {
            var configuracaoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterIdTipoPOSDoEstabelecimento(Estabelecimento.IdEstabelecimento);
            return configuracaoPos.HasValue && configuracaoPos.Value == (int)SubadquirenteEnum.ConnectPagarme && !string.IsNullOrEmpty(IdExternosDeBeneficiario) &&
                IdExternosDeBeneficiario.StartsWith("re_");
        }

        private string ObterSenhaUtilizadaParaAutorizacao()
        {
            if (!string.IsNullOrWhiteSpace(CodigoDeAcessoAoPainelAtendimento))
                return CodigoDeAcessoAoPainelAtendimento;

            var digitosDoCpf = Profissional.PessoaFisica.Cpf.Substring(0, 4);
            return FormsAuthentication.HashPasswordForStoringInConfigFile(digitosDoCpf, "SHA1");
        }

        public virtual bool SenhaDeAutorizacaoEstaValida(string senhaDescriptografada)
        {
            var senhaCriptografadaParaValidar = FormsAuthentication.HashPasswordForStoringInConfigFile(senhaDescriptografada, "SHA1");
            var senhaDeAutorizacaoCriptografada = ObterSenhaUtilizadaParaAutorizacao();
            return senhaCriptografadaParaValidar == senhaDeAutorizacaoCriptografada;
        }

        public override string ToString()
        {
            return Profissional.PessoaFisica.NomeOuApelido();
        }
    }
}