﻿using Castle.ActiveRecord;
using Perlink.Trinks.Pacotes.Enums;
using Perlink.Trinks.Pessoas.Enums;
using System;

namespace Perlink.Trinks.Pessoas
{

    [ActiveRecord("Estabelecimento_Configuracao_Geral", DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class EstabelecimentoConfiguracaoGeral
    {
        private int? _antecedenciaDeNotificacaoEmMinutos;

        private bool? _enviarNotificacaoParaClientes;
        private bool? _envioEmailDespesasHabilitado;
        private bool? _envioEmailEstoqueHabilitado;

        public EstabelecimentoConfiguracaoGeral()
        {
            HabilitarPesquisaClientesDeOutrosProfissionais = false;
            HabilitaAdicaoRapidaDeProduto = true;
            TrabalhaComDescartaveis = false;
            DescontarDescartaveisDoValorPago = true;
            ProfissionalPodeVerTotaisServicos = true;
            PercentualAplicadoTotaisServicosPAT = 100;
            ProfissionalPodeVerTotaisProdutos = true;
            EnviarEmailDespesasHabilitado = false;
            EnviarEmailEstoqueHabilitado = false;
            ExibirNomeProfissionalEmEmailESms = false;
            ExibirAlertasCobrancaParaRecepcionista = true;
            ProfissionaisPodemIncluirObservacoesNosAgendamentos = true;
            EnvioDeNotificacaoHabilitado = true;
            EnviarEmailsAgendamentoParaClientes = true;
            ExibeVideosTreinamento = true;
            PermiteEnviarNotificacaoDeAgendePeloTrinks = true;
            PermiteClientesAvaliemOEstabelecimento = true;
            PermiteClientesAvaliemOsProfissionais = true;
            VerSomenteHorarioDeFuncionamentoNaAgenda = true;
            DiaInicioFechamentoMensal = 1;
            AssistentePodeVerAgendamentosQuePodeParticipar = true;
            ExibirSugestaoDeProdutoQuando = ConfiguracaoSugestaoDeProdutoEnum.EstiverAbaixoDoEstoqueMinimo;
            PercentualEstoqueMinimoSugestaoDeProduto = null;
            HabilitaTerminalDeConsultaDePreco = false;
            AbrirInventarioComContagemIgualAoEstoqueAtual = true;
            RecepcaoPodeIncluirEExcluirProfissionaisNoRodizio = false;
            IncrementoDeHorarioClicado = 30;
            PermiteEnvioDeLembreteSmsGratuito = false;
            HabilitaBaixaAutomaticaDeEstoque = true;
            HabilitarPesquisaDeSatisfacao = true;
            ProfissionalPodeVerResumoAgenda = true;
            ProfissionalPodeVerAreaProfissionalDeDiasAnterioresEPosteriores = (int)ConfiguracaoProfissionalPodeVerAreaProfissional.TodosOsDias;
            AlertaEstoqueNegativo = true;
            ProfissionaisCadastradosVeraoMenuLateralEHeader = true;
            StatusDeAgendamentosFeitoPeloEstabelecimento = StatusHorarioEnum.Confirmado;
            ReceberEmailsAutomaticosDeAgendamento = true;
            ConfiguracaoNFPacote = TipoNFPacoteEnum.Venda;
        }

        [Property("antecedencia_notificacao_em_minutos")]
        public virtual int AntecedenciaDeNotificacaoEmMinutos
        {
            get { return _antecedenciaDeNotificacaoEmMinutos ?? 1440; }
            set
            {
                if (_antecedenciaDeNotificacaoEmMinutos != null && _antecedenciaDeNotificacaoEmMinutos != value)
                    AntecedenciaDeNotificacaoEmMinutosAlterado = true;
                _antecedenciaDeNotificacaoEmMinutos = value;
            }
        }

        public virtual bool AntecedenciaDeNotificacaoEmMinutosAlterado { get; set; }

        [BelongsTo("id_template_lembrete_selecionado")]
        public virtual Template TemplateLembreteSelecionado { get; set; }

        [Property("id_tipo_impressao")]
        public virtual Int32 CodigoTipoDeImpressao { get; set; }

        [Property("comanda_ativo", ColumnType = "Boolean")]
        public virtual bool ComandaEstaAtiva { get; set; }

        [Property("considerar_desconto_operadora_na_comissao")]
        public virtual bool ConsiderarDescontoOperadoraNaComissao { get; set; }

        [Property("considerar_desconto_operadora_na_comissao_assistente")]
        public virtual bool ConsiderarDescontoOperadoraNaComissaoDeAssistentes { get; set; }

        [Property("data_solicitacao_deseja_ser_lembrado_de_processo_para_aparecer_na_busca_do_portal")]
        public virtual DateTime? DataSolicitacaoDesejaSerLembradoSobreExibicaoNoPortal { get; set; }

        [Property("descartaveis_descontar_do_valor_pago")]
        public virtual bool DescontarDescartaveisDoValorPago { get; set; }

        [Property("deseja_ser_lembrado_de_processo_para_aparecer_na_busca_do_portal")]
        public virtual bool DesejaSerLembradoSobreExibicaoNoPortal { get; set; }

        [Property("enviar_email_despesas")]
        public virtual bool EnviarEmailDespesasHabilitado
        {
            get { return _envioEmailDespesasHabilitado ?? false; }
            set { _envioEmailDespesasHabilitado = value; }
        }

        [Property("enviar_email_estoque")]
        public virtual bool EnviarEmailEstoqueHabilitado
        {
            get { return _envioEmailEstoqueHabilitado ?? false; }
            set { _envioEmailEstoqueHabilitado = value; }
        }

        [Property("enviar_emails_agendamentos_para_cliente", ColumnType = "Boolean")]
        public virtual bool EnviarEmailsAgendamentoParaClientes { get; set; }

        [Property("enviar_notificacao_clientes")]
        public virtual bool EnviarNotificacaoParaClientes
        {
            get { return _enviarNotificacaoParaClientes ?? false; }
            set
            {
                if (_enviarNotificacaoParaClientes != null && _enviarNotificacaoParaClientes != value)
                    EnviarNotificacaoParaClientesAlterado = true;
                _enviarNotificacaoParaClientes = value;
            }
        }

        public virtual bool EnviarNotificacaoParaClientesAlterado { get; set; }

        [Property("envio_notificacao_habilitado")]
        public virtual bool EnvioDeNotificacaoHabilitado { get; set; }

        [Property("permite_lembrete_sms_gratuito")]
        public virtual bool PermiteEnvioDeLembreteSmsGratuito { get; set; }

        [Property("exibe_numero_fechamento")]
        public virtual bool ExibeNumeroDeFechamento { get; set; }

        [Property("exibe_numero_comanda_para_impressao")]
        public virtual bool ExibeNumeroDaComandaParaImpressao { get; set; }

        [Property("exibir_valor_produto_usado_na_impressao_comanda")]
        public virtual bool ExibirValorProdutoUsadoNaImpressaoComanda { get; set; }

        [Property("exibe_videos_treinamento")]
        public virtual bool ExibeVideosTreinamento { get; set; }

        [Property("exibir_alertas_cobranca_para_recepcionista")]
        public virtual bool ExibirAlertasCobrancaParaRecepcionista { get; set; }

        [Property("exibir_dados_a_partir")]
        public virtual DateTime? ExibirDadosAPartirDe { get; set; }

        [Property("exibir_nome_profissional_em_email_e_sms")]
        public virtual bool ExibirNomeProfissionalEmEmailESms { get; set; }

        [Property("fila_espera_ativo", ColumnType = "Boolean")]
        public virtual bool FilaDeEsperaEstaAtiva { get; set; }

        [Property("habilita_adicao_rapida_produto")]
        public virtual bool HabilitaAdicaoRapidaDeProduto { get; set; }

        [Property("habilita_belezinha")]
        public virtual bool HabilitaBelezinha { get; set; }

        [Property("habilitar_pesquisa_clientes_de_outros_profissionais")]
        public virtual bool HabilitarPesquisaClientesDeOutrosProfissionais { get; set; }

        [Property("habilitar_profissional_parceiro")]
        public virtual bool HabilitarProfissionalParceiro { get; set; }

        [PrimaryKey(PrimaryKeyType.Assigned, "id_estabelecimento")]
        public virtual int IdEstabelecimento { get; set; }

        [Property("imprime_fechamento_ao_fechar_conta", ColumnType = "Boolean")]
        public virtual bool ImprimirFechamentoAoFecharConta { get; set; }

        [Property("imprime_qrcode_fechamento", ColumnType = "Boolean")]
        public virtual bool ImprimirQRCodeFechamento { get; set; }

        [Property("numero_ultimo_fechamento")]
        public virtual Int32? NumeroDoUltimoFechamento { get; set; }

        [Property("comanda_numero_max")]
        public virtual Int32 NumeroMaximoComanda { get; set; }

        [Property("comanda_numero_min")]
        public virtual Int32 NumeroMinimoComanda { get; set; }

        [Property("pagar_comissoes_na_data_prevista_de_recebimento")]
        public virtual bool PagarComissoesNaDataPrevistaDeRecebimento { get; set; }

        [Property("percentual_aplicado_totais_servicos_pat")]
        public virtual decimal? PercentualAplicadoTotaisServicosPAT { get; set; }

        [Property("percentual_desconto_maximo")]
        public virtual decimal? PercentualDeDescontoMaximo { get; set; }

        //[Property("valor_maximo_para_servicos ")]
        //public virtual decimal? ValorMaximoParaServicos { get; set; }

        [Property("permite_notificacao_agende_pelo_trinks")]
        public virtual bool PermiteEnviarNotificacaoDeAgendePeloTrinks { get; set; }

        [Property("permite_finalizar_apenas_clientes_em_atendimento")]
        public virtual bool PermiteFinalizarApenasClientesEmAtendimento { get; set; }

        [BelongsTo("id_pessoa_solicitacao_deseja_ser_lembrado_de_processo_para_aparecer_na_busca_do_portal", Lazy = FetchWhen.OnInvoke)]
        public virtual PessoaFisica PessoaSolicitouDesejaSerLembradoSobreExibicaoNoPortal { get; set; }

        [Property("produto_fracionado_ativo")]
        public virtual bool ProdutoFracionadoEstaAtivo { get; set; }

        [Property("profissional_pode_alterar_valor_servico", ColumnType = "Boolean")]
        public virtual bool ProfissionaisPodemAlterarPrecosDosServicos { get; set; }

        [Property("profissional_pode_incluir_observacao_nos_agendamentos")]
        public virtual bool ProfissionaisPodemIncluirObservacoesNosAgendamentos { get; set; }

        [Property("profissional_pode_ver_valor_servico", ColumnType = "Boolean")]
        public virtual bool ProfissionaisPodemVerPrecosDosServicos { get; set; }

        [Property("profissional_so_acessa_seus_agendamentos")]
        public virtual bool ProfissionaisVeemSeusPropriosAgendamentosApenas { get; set; }

        [Property("profissional_pode_ver_agendamentos_de_dias_anteriores_e_posteriores")]
        public virtual int ProfissionalPodeVerAgendaDeDiasAnterioresEPosteriores { get; set; }

        [Property("profissional_pode_ver_valor_comissao", ColumnType = "Boolean")]
        public virtual bool ProfissionalPodeVerTotaisComissoes { get; set; }

        [Property("profissional_pode_ver_totais_servicos")]
        public virtual bool ProfissionalPodeVerTotaisServicos { get; set; }

        [Property("profissional_pode_ver_totais_produtos")]
        public virtual bool ProfissionalPodeVerTotaisProdutos { get; set; }

        [Property("deseja_trabalhar_com_descartaveis")]
        public virtual bool TrabalhaComDescartaveis { get; set; }

        [Property("ultima_execucao_rotina_consolidado")]
        public virtual DateTime? UltimaExecucaoDaRotinaDoConsolidado { get; set; }

        [Property("utilizar_codigo_interno")]
        public virtual bool UtilizarCodigoInterno { get; set; }

        [Property("utilizar_nota_fiscal_eletronica_servico")]
        public virtual bool UtilizarNFeServico { get; set; }

        [Property("ver_somente_horario_funcionamento_na_agenda")]
        public virtual bool VerSomenteHorarioDeFuncionamentoNaAgenda { get; set; }

        [Property("exibir_campo_assinatura_em_impressao_relatorio_comissoes")]
        public virtual bool ExibirCampoAssinaturaEmImpressaoRelatorioComissoes { get; set; }

        [Property("habilita_envio_copia_email_resumo_comissao_profissional_para_responsavel_financeiro")]
        public virtual bool HabilitaEnvioCopiaEmailResumoComissaoProfissionalParaResponsavelFinanceiro { get; set; }

        [Property("mensagem_impressao_relatorio_comissoes")]
        public virtual String MensagemImpressaoRelatorioComissoes { get; set; }

        [Property("dia_inicio_fechamento_mensal")]
        public virtual int DiaInicioFechamentoMensal { get; set; }

        [Property("profissionais_podem_ver_comissoes")]
        public virtual bool ProfissionaisPodemVerAColunaComissoes { get; set; }

        private bool habilitaProgramaDeFidelidade;

        [Property("habilita_programa_fidelidade")]
        public virtual bool HabilitaProgramaDeFidelidade
        {
            get
            {
                return habilitaProgramaDeFidelidade;
            }

            set
            {
                habilitaProgramaDeFidelidade = value;
                if (value && !HabilitaProgramaDeFidelidadeDataHora.HasValue)
                    HabilitaProgramaDeFidelidadeDataHora = Calendario.Agora();
            }
        }

        [Property("habilita_programa_fidelidade_data")]
        public virtual DateTime? HabilitaProgramaDeFidelidadeDataHora { get; set; }

        [Property("assistente_pode_ver_agendamentos_que_pode_participar")]
        public virtual bool AssistentePodeVerAgendamentosQuePodeParticipar { get; set; }

        [Property("habilita_split_pagamento")]
        public virtual bool HabilitaSplitPagamento { get; set; }

        [Property("habilitar_controle_de_caixa_por_profissional")]
        public virtual bool HabilitaControleDeCaixaPorProfissional { get; set; }

        [Property("habilita_motivo_sangria_obrigatorio")]
        public virtual bool HabilitaMotivoSangriaObrigatorio { get; set; }

        [Property("ja_teve_caixa_por_profissional_aberto")]
        public virtual bool JaTeveCaixaPorProfissionalAberto { get; set; }

        [Property("permite_clientes_avaliem_estabelecimento")]
        public virtual bool PermiteClientesAvaliemOEstabelecimento { get; set; }

        [Property("permite_clientes_avaliem_profissionais")]
        public virtual bool PermiteClientesAvaliemOsProfissionais { get; set; }

        [Property("exibir_sugestao_produto_quando")]
        public virtual ConfiguracaoSugestaoDeProdutoEnum ExibirSugestaoDeProdutoQuando { get; set; }

        [Property("percentual_estoque_minimo_sugestao_produto")]
        public virtual int? PercentualEstoqueMinimoSugestaoDeProduto { get; set; }

        [Property("Tipo_Emissao_Nfe_Profissional_Parceiro")]
        public virtual int? TipoDeEmissaoNfeComProfissionalParceiro { get; set; }

        [Property("habilita_pedidos_compra")]
        public virtual bool HabilitaPedidosDeCompraCasoNaoSejaDeFranquia { get; set; }

        [Property("permite_agendar_google_reserve")]
        public virtual bool PermiteAgendarGoogleReserve { get; set; }

        [Property("habilitar_pesquisa_de_satisfacao")]
        public virtual bool HabilitarPesquisaDeSatisfacao { get; set; }

        [Property("habilitar_terminal_de_consulta_de_preco")]
        public virtual bool HabilitaTerminalDeConsultaDePreco { get; set; }

        [Property("abrir_inventário_com_contagem_igual_ao_estoque_atual")]
        public virtual bool AbrirInventarioComContagemIgualAoEstoqueAtual { get; set; }

        private bool _trabalhaComRodizioDeProfissionais;

        [Property("trabalha_com_rodizio_profissionais")]
        public virtual bool TrabalhaComRodizioDeProfissionais
        {
            get
            {
                return _trabalhaComRodizioDeProfissionais;
            }
            set
            {
                _trabalhaComRodizioDeProfissionais = value;
                if (_trabalhaComRodizioDeProfissionais && !JaTrabalhouComRodizioDeProfissionais)
                    JaTrabalhouComRodizioDeProfissionais = true;
            }
        }

        [Property("ja_trabalhou_com_rodizio_profissionais")]
        public virtual bool JaTrabalhouComRodizioDeProfissionais { get; set; }

        [Property("recepcao_pode_incluir_e_excluir_profissionais_rodizio")]
        public virtual bool RecepcaoPodeIncluirEExcluirProfissionaisNoRodizio { get; set; }

        [Property("pode_ver_area_profissional_de_dias_anteriores_e_posteriores")]
        public virtual int ProfissionalPodeVerAreaProfissionalDeDiasAnterioresEPosteriores { get; set; }

        [Property("incremento_horario_clicado")]
        public virtual int IncrementoDeHorarioClicado { get; set; }

        [Property("valor_em_porcentagem_do_custo_operacional")]
        public virtual int ValorEmPorcentagemDoCustoOperacional { get; set; }

        [Property("possue_custo_operacional")]
        public virtual bool PossuiCustoOperacionalHabilitado { get; set; }

        [Property("possui_custo_operacional_produto")]
        public virtual bool PossuiCustoOperacionalProdutoHabilitado { get; set; }

        [Property("assistente_pode_se_associar_a_servicos")]
        public virtual bool AssistentePodeSeAssociarAServicos { get; set; }

        [Property("habilita_baixa_automatica_estoque")]
        public virtual bool HabilitaBaixaAutomaticaDeEstoque { get; set; }

        [Property("profissional_pode_ver_resumo_agenda")]
        public virtual bool ProfissionalPodeVerResumoAgenda { get; set; }

        [Property("permite_mgm_profissional")]
        public virtual bool PermiteMGMProfissional { get; set; }

        [Property("quantidade_de_dias_para_consulta_rps")]
        public virtual int? QuantidadeDeDiasParaConsultaRps { get; set; }

        [Property("alerta_estoque_negativo")]
        public virtual bool AlertaEstoqueNegativo { get; set; }

        [Property("profissionais_cadastrados_verao_menu_lateral_e_header")]
        public virtual bool ProfissionaisCadastradosVeraoMenuLateralEHeader { get; set; }

        [Property("configuracao_automatica_do_programa_de_fidelidade_esta_ativa")]
        public virtual bool ConfiguracaoAutomaticaDoProgramaDeFidelidadeEstaAtiva { get; set; }

        [Property("exibir_somente_os_primeiros_profissionais_do_rodizio_na_agenda")]
        public virtual bool ExibirSomenteOsPrimeirosProfissionaisDoRodizioNaAgenda { get; set; }

        [Property("quantidade_profissionais_exibir_durante_rodizio")]
        public virtual int QtdDeProfissionaisExibirDuranteRodizio { get; set; }

        [Property("gerencia_notificacoes_cliente_agendamento")]
        public virtual bool GerenciaNotificacoesDoClienteNoAgendamento { get; set; }

        [Property("status_de_agendamentos_feito_pelo_estabelecimento")]
        public virtual StatusHorarioEnum StatusDeAgendamentosFeitoPeloEstabelecimento { get; set; }

        [Property("permite_utilizar_link_de_pagamento")]
        public virtual bool PermiteUtilizarLinkDePagamento { get; set; }

        [Property("comanda_lancamento_rapido_permite_buscar_servico_pelo_nome")]
        public virtual bool LancamentoRapidoComandaPermiteBuscarServicoPeloNome { get; set; }

        [Property("receber_email_automatico_de_agendamento")]
        public virtual bool ReceberEmailsAutomaticosDeAgendamento { get; set; }

        [Property("imprimir_comanda_apos_abrir")]
        public virtual bool ImprimirComandaAposAbrir { get; set; }

        [Property("associar_servicos_e_produtos_a_comanda_automaticamente")]
        public virtual bool AssociarServicosEProdutosAComandaAbertaAutomaticamente { get; set; }

        [Property("permite_abrir_comanda_vazia")]
        public virtual bool PermiteAbrirComandaVazia { get; set; }

        [Property("ver_somente_clientes_em_atendimento")]
        public virtual bool VerSomenteClientesEmAtendimento { get; set; }

        [Property("trabalha_com_rodizio_profissionais_por_categoria")]
        public virtual bool TrabalhaComRodizioDeProfissionaisPorCategoria { get; set; }

        [Property("pode_ver_posicao_no_rodizio")]
        public virtual bool PodeVerPosicaoNoRodizio { get; set; }

        [Property("momento_retorno_ao_rodizio")]
        public virtual bool MomentoRetornoAoRodizio { get; set; }


        [Property("configuracao_NF_pacote")]
        public virtual TipoNFPacoteEnum ConfiguracaoNFPacote { get; set; }

        [Property("cancelar_envio_email_cadastro_cliente_balcao")]
        public virtual bool CancelarEnvioEmailCadastroClienteBalcao { get; set; }

        [Property("avisar_sobre_servicos_nao_finalizados")]
        public virtual bool AvisarSobreServicosNaoFinalizados { get; set; }

        [Property("avisar_sobre_servicos_ainda_pendentes")]
        public virtual bool AvisarSobreServicosAindaPendentes { get; set; }

        [Property("habilita_visualizacao_valor_produto_baixa_automatica")]
        public virtual bool HabilitaVisualizacaoDoValorDoProdutoNaBaixaAutomatica { get; set; }
    }
}
