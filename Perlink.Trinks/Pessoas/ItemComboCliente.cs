﻿using Castle.ActiveRecord;

namespace Perlink.Trinks.Pessoas
{
    [ActiveRecord("Combo_Clientes_v2", Mutable = false)]
    public class ItemComboCliente : ActiveRecordBase<ItemComboCliente>
    {

        [PrimaryKey(PrimaryKeyType.Native, "ID_ESTABELECIMENTO_CLIENTE", ColumnType = "Int32")]
        public int IdClienteEstabelecimento { get; set; }

        [Property("ID_CLIENTE")]
        public int IdCliente { get; set; }

        [Property("id_pessoa")]
        public int IdPessoa { get; set; }

        [Property("ID_ESTABELECIMENTO")]
        public int IdEstabelecimento { get; set; }

        [Property("Nome", ColumnType = "AnsiString")]
        public string Nome { get; set; }

        [Property("Email", ColumnType = "AnsiString")]
        public string Email { get; set; }

        [Property("Telefones")]
        public string Telefones { get; set; }

        [Property("Telefones_index")]
        public string TelefonesInexado { get; set; }
    }
}
