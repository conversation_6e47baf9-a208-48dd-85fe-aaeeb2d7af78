namespace Perlink.Trinks.Pessoas.Enums
{
    public enum ParametrosTrinksEnum
    {
        id_cob_formas_pagamento_ativas,
        qtd_dias_padrao_pos_fim_degustacao_e_nao_assinatura,
        qtd_dias_padrao_pos_inadimplencia_fora_tolerancia,
        qtd_dias_padrao_ultimo_login_para_manter_hotsite_no_ar,
        qtd_dias_tolerancia_padrao_nao_pagamento,
        qtd_dias_antecedencia_verificacao_proximo_boleto,
        qtd_dias_antecedencia_verificacao_boleto_pendente_pagamento,
        qtd_dias_max_padrao_para_recebimento_operadora,
        id_plano_assinatura_padrao,
        habilita_produto_estoque,
        qtd_dias_antes_assinatura,
        qtd_dias_maximo_periodo_consulta_rps,
        link_download_app_android,
        link_download_app_ios,
        ultima_atualizacao_rancking_cliente,
        habilitar_reprocessamento_faturas_pagas,
        nome_do_bucketS3_para_cupom_fiscal,
        nome_do_bucketS3_para_importacao,
        nome_do_bucketS3_para_fotos,
        nome_do_bucketS3_para_imagens,
        nome_do_bucketS3_email_marketing,
        boleto_dias_vencimento,
        boleto_cnpj_cedente,
        boleto_nome_cedente,
        boleto_agencia_cedente,
        boleto_conta_corrente_cedente,
        boleto_codigo_cedente,
        boleto_codigo_banco_cedente,
        boleto_carteira_cedente,
        boleto_codigo_especie_documento,
        boleto_instrucoes_banco,
        boleto_local_pagamento,
        boleto_multa_pagamento_vencido,
        zopin_exibir_no_backoffice,
        habilita_operadora_telefone,
        qtd_maxima_fotos_galeria,
        qtd_dias_exibicao_servicos_anteriores_fotos_clientes,
        qtd_itens_servicos_fotos,
        nome_fila_sqs_processamento_nfse,
        tempo_processamento_mensagens_nfse,
        qtd_registros_pagina_marketing,
        hora_inicio_disparo_sms_aniversariantes,
        qtd_caracteres_texto_sms,
        qtd_caracteres_texto_email,
        sufixo_url_compra_credito_campanha_marketing,
        hora_inicio_disparo_email_aniversariantes,
        plano_assinatura_padrao,
        plano_assinatura_padrao_app,
        franquia_spa_sobrancelhas,
        plano_especial_esmalteria,
        plano_especial_esmalteria_com_nota_produto,
        tipo_regime_tributario_padrao,
        ultima_atualizacao_ranking_profissionais,
        ultima_atualizacao_ranking_produtos,
        ultima_atualizacao_ranking_servicos,
        antecedencia_minima_para_envio_notificacao_fora_horario,
        cpfe_cnpj_cedente,
        nome_fila_sqs_emails_rejeitados,
        buscar_gateway_pela_operadora_celular,
        certificado_perlink_arquivo,
        certificado_perlink_senha,
        fraquia_esmalteria_nacional,
        habilita_reCAPTCHA,
        nome_fila_sqs_mensagens_email,
        nome_fila_sqs_mensagens_sms,
        nome_fila_sqs_retorno_mensagens_sms,
        qtd_horas_limite_para_envio_sms_lembrete,
        rotina_criptografia_executada,
        habilita_inutilizar_numero_nota,
        valor_maximo_emissao_nota,
        //nota_tecnica_2015_implantada,
        horario_limite_para_envio_notificacao_cliente,
        ultima_atualizacao_demonstrativo_resultado,
        quantidade_em_minutos_para_atualizacao,
        programar_data_envio_mensagens_em_massa,
        habilitar_cest_para_estabelecimentos,
        quantos_dias_promocao_trinks_fica_fisivel_apos_cadastro,
        id_status_nfce_aguardando,
        quantos_dias_sem_receber_notificacao_agende_pelo_trinks,
        id_servicosms_fixo_para_agende_pelo_trinks,
        id_servicosms_fixo_para_marketing,
        link_para_baixar_app,
        id_estabelecimento_teste_para_sms_agende_pelo_trinks,
        cnpj_trinks,
        inscricao_municipal_trinks,
        codigo_regime_especial_tributacao_trinks,
        codigo_optante_simples_nacional_trinks,
        codigo_tipo_servico_trinks,
        aliquota_iss_trinks,
        emails_padrao_envio_sendindblue,
        id_estabelecimento_excluido_para_sms_agende_pelo_trinks,
        dias_uteis_a_adicionar_tolerancia_ao_informar_pagamento_boleto,
        dias_antecedencia_maxima_para_troca_cartao,
        exibir_link_blog_trinks,
        id_franquia_excluida_para_sms_agende_pelo_trinks,
        ufs_para_sms_agende_pelo_trinks,
        id_servicosms_fixo_para_aniversariantes,
        dias_antecedencia_para_cancelar_assinatura,
        qt_notificacoes_a_listar_por_requisicao,
        url_stone,
        url_download_drive_belezinha,
        url_download_instalador_stone,
        qtd_dias_verificacao_validade_certificado_digital,
        dias_antecedencia_aviso_backoffice_de_pontos_fidelidade_expirando,
        fidelidade_habilitada_todos,
        qtd_dias_lembrar_promocao_Programa_de_fidelidade,
        data_final_Promocao_Programa_Fidelidade,
        minutos_diferenca_email_marketing_fidelidade,
        assunto_email_marketing_fidelidade,
        nome_campanha_email_marketing_fidelidade,
        precos_com_varios_planos_pra_fidelidade,
        qtd_caracteres_reservados_sms_aniversariante,
        url_de_fotos,
        url_de_imagens_email_marketing,
        url_de_imagens_novidades,
        permite_cliente_balcao_cancelar_agendamento,
        id_app_padrao_android,
        id_app_padrao_ios,
        id_estabelecimento_teste_beta,
        rodape_telefones,
        rodape_email_atendimento,
        rodape_horario_funcionamento,
        iugu_custom_api_token,
        utilizar_pagamento_boleto_iugu,
        dias_para_subtrair_data_de_referencia_do_expurgo_do_importador,
        dt_fim_promocao_dia_maes,
        url_download_instalador_pago,
        utilizar_mock_para_pagementos_com_maquina_belezinha,
        realizar_estorno_automatico_apos_transacoes,
        url_base_transferencia_interna,
        url_base_transferencia_interna_mock,
        habilita_envio_confirmacao_conta_cliente_web,
        habilita_envio_confirmacao_conta_responsavel_financeiro,
        id_servicosms_fixo_para_sms_imediato,
        texto_sms_codigo_confirmacao,
        range_de_imagens_compartilhamento_facebook,
        nome_campanha_sms_convite_retorno,
        assunto_email_convite_retorno,
        nome_campanha_email_convite_retorno,
        sugestao_texto_campanha_sms_convite_retorno,
        sugestao_texto_campanha_email_convite_retorno,
        qtd_caracteres_texto_sms_convite_retorno,
        lista_de_assunto_fale_conosco_usuario_logado,
        lista_de_assunto_fale_conosco_usuario_deslogado,
        nome_do_bucketS3_para_anexo,
        url_de_anexos,
        limite_tamanho_anexo_em_megas,
        lista_tipos_de_arquivos_aceitos,
        cores_permitidas_etiquetas,
        validade_dias_cookie_cupomPromocional,
        limite_maximo_de_registros_por_pagina,
        dias_subtrair_busca_estabelecimento,
        limite_servicos_por_pagina,
        minutos_antecedencia_minina_agendamentos_cliente,
        meses_vigencia_avaliacoes_estabelecimento,
        qtd_minima_avaliacoes_estabelecimento_para_exibicao,
        numero_meses_pesquisa_meus_compromissos_antigos_app_mobile,
        qtd_maxima_itens_meus_compromissos_passados_app,
        link_apresentacao_trinks,
        max_dias_passados_para_profissionais_consultar_comissao,
        qtdProdutos_email_agendaDeDespesasPosicaoAtualDeEstoque,
        url_team_viewer,
        qtd_limite_sms_lembrete_durante_periodo_gratis,
        limite_dias_busca_clientes_futuros_agenda,
        limite_agendamentos_futuros_por_requisicao_agenda,
        numero_maximo_dias_pesquisa_relatorio_venda_produto,
        numero_maximo_notas_para_inutilizar,
        codigos_erro_retorno_nfse,
        limite_itens_por_pedido_de_compra,
        qtd_faixas_plano_exibir_why_trinks,
        dias_antecedencia_pesquisa_pontos_fidelidade_expirando_cliente,
        lista_de_atendimento_por_regiao,
        qtd_dias_de_intervalo_para_solicitar_avaliacao,
        id_servicosms_fixo_para_sms_com_resposta,
        texto_pergunta_avaliacao_servico_sms,
        minutos_apos_fechamento_para_envio_avaliacao_servico,
        qtd_horas_limite_para_envio_de_pedido_de_avaliacao_sms,
        limite_dias_para_cliente_responder_avaliacao_satisfacao,
        google_reserve_estabelecimentos_ativos,
        google_reserve_bookingapi_login,
        google_reserve_bookingapi_senha,
        qtd_dias_passados_para_enviar_avaliacoes_satisfacao,
        url_banner_popup_promocao_marketing,
        url_banner_promocao_marketing,
        dt_fim_exibicao_popup_promocao_marketing,
        dt_fim_exibicao_banner_promocao_marketing,
        qtd_itens_pesquisa_estabelecimentos_servicos_portal,
        pgtoOnline_numero_minimo_meses_assinatura_paga_para_ativar_pagamento_online,
        pgtoOnline_valor_maximo_para_pagamento_online,
        pgtoOnline_horario_envio_notificacoes_incentivo,
        pgtoOnline_percentual_trinks_default,
        pgtoOnline_percentual_trinks_primeiro_pagamento_default,
        pgtoOnline_valor_fixo_trinks_default,
        pgtoOnline_valor_fixo_trinks_primeiro_pagamento_default,
        pgtoOnline_percentual_operadora_default,
        pgtoOnline_valor_fixo_operadora_default,
        pgtoOnline_valor_fixo_por_transferencia_default,
        numero_maximo_dias_pesquisa_relatorio_consumo_pacote,
        franquias_que_importam_vale,
        minutos_antecedencia_envio_sms_lembrete_plano_basico,
        dias_para_cancelamento_apos_contratar_item_adicional,
        id_servicosms_fixo_para_sms_lembrete_com_garantia,
        limite_envios_sms_lembrete_por_estabelecimento,
        dias_renovar_limite_envios_sms_lembrete_por_estabelecimento,
        pgtoOnline_gateway,
        pgtoOnline_dias_para_recebimento_eh_aproximado,
        pgtoOnline_dias_para_receber_default,
        pgtoOnline_rate_limit_cartao_ativado,
        pgtoOnline_rate_limit_cartao_qtd,
        pgtoOnline_rate_limit_cartao_limite_horario,
        qtd_dias_abranje_periodo_para_lancar_vales_adiantamentos,
        franquias_com_dashboard,
        nome_do_bucketS3_para_log_de_boleto_IUGU,
        nome_do_bucketS3_para_controle_de_fotos,
        url_cdn_cloudfront_para_controle_de_fotos,
        ignorar_avaliacoes_ja_respondidas,
        estabelecimentos_busca_cliente_CPF,
        personalizacao_frame_busca,
        link_s3_imagem_principal_adicional,
        pasta_conteudo_saiba_mais_meu_plano,
        nome_do_bucketS3_para_conteudos,
        necessario_confirmacao_antes_atualizar_assinatura_meu_plano,
        origens_cadastro_confirma,
        id_plano_assinatura_padrao_estrutura_antiga,
        parcelas_plano_com_selo_mais_popular_fluxo_assinatura,
        api_key_stone_siclos,
        partner_id_stone_siclos,
        url_callback_pretransacao_stone_siclos,
        url_callback_transacao_stone_siclos,
        url_callback_split_transacao_stone_siclos,
        url_callback_split_habilitado_stone_siclos,
        url_callback_pos_habilitado_stone_siclos,
        taxa_antecipacao_automatica_stone_siclos,
        taxa_antecipacao_pontual_stone_siclos,
        cadastros_vao_para_producao_stone_siclos,
        limite_cadastro_etiquetas_cliente,
        limite_cadastro_etiquetas_agendamento,
        limite_associar_etiquetas_no_cliente,
        limite_associar_etiquetas_no_agendamento,
        limite_cadastro_etiquetas_com_mesma_cor,
        id_pessoa_conta_trinks_interno,
        promocao_padrao_na_assinatura_com_cartao,
        qtd_profissionais_no_rodizio,
        numero_maximo_de_parcelas_de_despesas,
        qtd_dias_maximo_periodo_consulta_rps_para_profissional_parceiro,
        baixa_automatica_no_estoque_pode_editar_na_edicao_do_agendamento_default,
        baixa_automatica_no_estoque_pode_editar_no_fechamento_de_conta_default,
        baixa_automatica_no_estoque_pode_editar_no_pat_default,
        baixa_automatica_no_estoque_pode_editar_na_agenda_por_cliente_default,
        nome_do_bucketS3_para_anexo_do_cliente,
        beleza_amiga_pagarme_api_endpoint,
        beleza_amiga_pagarme_api_key,
        beleza_amiga_pagarme_password_key,
        beleza_amiga_pagarme_habilita_transferencia_automatica,
        beleza_amiga_pagarme_intervalo_transferencia,
        beleza_amiga_pagarme_dia_transferencia,
        beleza_amiga_pagarme_percentual_split_trinks,
        beleza_amiga_pagarme_id_trinks,
        beleza_amiga_limite_compra_voucher_por_cpf,
        beleza_amiga_limite_venda_voucher_por_estabelecimento,
        beleza_amiga_eh_modo_desenvolvimento,
        beleza_amiga_origens_validas,
        raiz_do_site_belezaamiga,
        qtd_profissionais_visiveis_agenda,
        novo_fluxo_cadastro_ativo,
        qtd_estabelecimentos_usam_trinks,
        pgto_gateway_pagarme_api_key,
        pgto_gateway_pagarme_id_recebedor_trinks,
        pgto_gateway_novas_assinaturas,
        habilita_enviar_notificacao_push_marketing,
        lista_teste_para_enviar_notificacao_push_marketing,
        limite_diario_de_envio_de_notificacoes_push_marketing,
        minutos_cooldown_envio_de_notificacoes_push_marketing,
        limite_dias_considerar_dispositivo_ativo,
        habilita_enviar_notificacao_push_marketing_app_trinks,
        limite_diario_de_envio_de_notificacoes_push_marketing_app_trinks,
        mgm_profissional_id_plano_assinatura_padrao,
        mgm_profissional_id_promocao_padrao,
        mgm_profissional_banner_padrao,
        clientes_anexos_extensoes_permitidas,
        clientes_anexos_tamanho_max_arquivo_em_megabytes,
        qtd_limite_agendamentos_diario_para_novos_usuarios,
        limite_registros_por_pagina_area_perlink,
        id_estabelecimento_para_ocultar_coluna_rateio,
        limite_dias_futuro_agendar_pelo_hotsite,
        lista_de_atendimento_por_regiao_comercial,
        qtd_dias_para_obter_estabelecimentos_integracao_cx,
        whytrinks_partialview_cabecalho,
        whytrinks_blackfriday_cabecalho_background_desktop,
        whytrinks_blackfriday_cabecalho_background_mobile,
        precadastro_banner_padrao,
        parceria_automatica_ao_se_cadastrar,
        data_inicio_parceria_automatica_ao_se_cadastrar,
        data_fim_parceria_automatica_ao_se_cadastrar,
        palavras_que_nao_devem_ser_utilizadas_como_cupom,
        nome_do_bucketS3_para_assinatura_digital,
        minutos_expirar_acesso_url_publica_arquivos_s3,
        periodo_limite_para_envio_de_email_assinatura_digital_tela_publica,
        quantidade_de_dias_para_recebimento_da_operadora_siclos,
        quantidade_de_parcelas_para_configurar_preenchimento_automatico_siclos,
        lista_de_assunto_fale_conosco_app_trinks_profissional,
        ultima_atualizacao_ranking_pacotes,
        habilitar_melhoria_pesquisa_clientes,
        quantidadede_maxima_de_dias_do_filtro_listar_fechamentos_trinsk_pro,
        config_survey_app_trinks_profissional,
        ativar_log_elasticsearch,
        ativar_log_postgresql,
        serilog_selflog_logs_ativado,
        serilog_selflog_logs_caminhotxt,
        taxa_padrao_para_forma_pgmnto_siclos,
        movidesk_associar_organizacoes_tickets_por_telefone,
        limite_dias_filtrar_relatorio_saldo_pacotes,
        teste_ab_planos_padrao,
        qtd_dias_para_exibir_card_de_oferta_novamente,
        novo_menu_quantidade_de_dias_botao_assinar_fica_visivel,
        configuracao_automatica_do_programa_de_fidelidade,
        reprocessamento_faturas_vencidas_ha_x_dias_atras,
        reprocessamento_faturas_vencidas_com_ultima_tentativa_ha_x_dias_atras,
        config_survey_trinks_mobile,
        requisicao_idempotente_web_habilitada,
        requisicao_idempotente_web_segundos_no_cache,
        push_envio_ativo,
        push_b2c_quantidade_maxima_disparos_por_execucao,
        push_b2c_quantidade_maxima_dispositivos_obtidos_por_usuario,
        push_b2c_quantidade_maxima_dias_para_consulta_de_dispositivos,
        push_b2c_grau_maximo_de_paralelismo,
        push_b2b_firebase_project_id,
        push_b2b_firebase_client_email,
        push_b2b_firebase_private_key,
        push_b2b_grau_maximo_de_paralelismo,
        push_b2b_quantidade_maxima_disparos_por_execucao,
        numero_de_contato_da_retencao,
        numero_de_contato_do_cross_sell,
        mensagem_padrao_de_contato_com_o_cross_sell_no_popup_de_experimentacao_NFS,
        periodo_curto_que_o_CTA_de_integracao_com_a_belezinha_ficara_oculto_se_o_cliente_desejar,
        periodo_longo_que_o_CTA_de_integracao_com_a_belezinha_ficara_oculto_se_o_cliente_desejar,
        id_do_questionario_do_porque_nao_quer_integrar_com_a_belezinha,
        valor_teste_emissao_belezinha,
        mapa_calor_filtro_periodo_limite_de_meses_atras,
        mapa_calor_filtro_periodo_limite_de_meses_a_frente,
        dt_conciliacao_dia_especifico,
        rotina_importacao_backoffice_esta_habilitada,
        retorno_clientes_ultimos_x_meses_do_filtro,
        importacao_dados_link_tutorial_aprenda_formatar_corretamente,
        desabilitar_intercom_no_backoffice,
        pgtoOnline_zoop_id_recebedor_trinks,
        pgtoOnline_zoop_marketplace_id,
        pgtoOnline_zoop_username,
        pgtoOnline_zoop_default_api_endpoint,
        pgtoOnline_zoop_vendedores_transferencia_habilitada,
        pgtoOnline_zoop_vendedores_transferencia_intervalo,
        pgtoOnline_zoop_vendedores_transferencia_dia,
        pgtoOnline_pagarme_id_recebedor_trinks,
        pgtoOnline_pagarme_api_key,
        pgtoOnline_pagarme_vendedores_transferencia_habilitada,
        pgtoOnline_pagarme_vendedores_transferencia_intervalo,
        pgtoOnline_pagarme_vendedores_transferencia_dia,
        pgtoOnline_pagarme_id_taxas_padrao,
        linkDePagamento_url_base_frontend,
        linkDePagamento_minutos_de_validade,
        como_converter_relatorios_do_trinks_para_outros_formatos,
        mgm_franquia_com_restricao,
        link_download_app_pro_ios,
        link_download_app_pro_android,
        portal_qtd_fotos_carrossel,
        rodape_whatsapp,
        storyly_api_token,
        url_de_imagens_comunidade_trinks,
        nome_do_bucketS3_para_imagens_dos_artigos_novidade,
        ids_formas_pagamento_padrao,
        back_to_salon_limite_cupom_por_cpf,
        comunidade_trinks_topicos_em_alta_x_dias,
        comunidade_trinks_sugestoes_implementadas_x_dias,
        qtd_limite_selecao_profissionais_relatorio_comissoes,
        duracao_lembrar_depois_popup_experimentacao_nfs_em_dias,
        numero_de_contato_do_suporte,
        back_to_salon_limite_cupom_validade,
        id_banco_temporario,
        numero_maximo_dias_pesquisa_relatorio_venda_pacote,
        questionarios_de_onboarding_por_faixa,
        dias_que_sao_permitidos_editar_disponibilidades,
        urls_para_ocultar_barra_aceite_cookies,
        plataforma_movidesk_contato_cross_sell,
        numero_de_contato_integracao_belezinha_cross_sell,
        id_categoria_padrao_para_cadastro_simplificado_de_servico,
        id_forma_de_pagamento_lancamento_de_despesa,
        texto_pergunta_recomendar_trilha,
        texto_resposta_trilha_agenda_organizada,
        texto_resposta_trilha_controle_financeiro,
        texto_resposta_trilha_divulgacao,
        segundos_para_reenvio_codigo_de_autenticacao,
        segundos_para_expiracao_do_codigo_de_autenticacao,
        id_servico_sms_fixo_para_sms_com_resposta_para_validacao,
        ligar_ou_desligar_etapa_nao_obrigatoria_fluxo_de_cadastro,
        ligar_ou_desligar_redirecionamentos_de_contas_nao_confirmadas,
        agendamento_online_requer_conta_confirmada,
        id_grupo_despesas_pessoais_antigo,
        id_grupo_despesas_pessoais_novo,
        artigo_ajuda_atualizar_certificado_digital,
        valor_proximo_resgate_fidelidade_em_porcertagem,
        texto_explicacao_aporte,
        mensagem_padrao_que_eh_enviada_a_retencao_em_caso_de_solicitacao_de_cancelamento,
        conteudo_da_popup_solicitacao_de_cancelamento_da_assinatura_recebida,
        conteudo_da_popup_solicitacao_de_cancelamento_da_assinatura_em_andamento,
        url_acessada_pela_webview_do_trinkspro_para_solicitar_o_cancelamento_da_assinatura,
        conteudo_da_popup_final_do_fluxo_de_cancelamento_da_assinatura,
        habilita_reCAPTCHA_v2,
        movidesk_api_key,
        id_plano_padrao_uso_trinks_api,
        dias_para_suspender_assinaturas_do_cliente_do_clube_de_assinaturas,
        id_do_conteudo_exibido_no_modal_de_integracao_com_FBE,
        codigo_beneficio_fiscal_iss_trinks,
        cache_method_call,
        url_para_artigo_de_permissoes_da_unidade,
        video_como_emitir_notas,
        video_como_baixar_lote_rps,
        artigo_como_baixar_lote_rps,
        artigo_como_emitir_notas,
        mensagem_fale_conosco_emissao_nota,
        rotina_lista_siclos,
        pgtoPorLink_gateway,
        form_interesse_whatsapp,
        valor_por_indicacao_parceria,
        email_de_resgate_do_indica_trinks,
        link_central_ajuda,
        postback_url_mudanca_status_recebedor_pagarme,
        representacao_string_status_recebedor_ativo_pagarme,
        representacao_string_status_recebedor_ativo_zoop,
        whytrinks_promocao_cabecalho_banner_desktop,
        whytrinks_promocao_cabecalho_banner_mobile,
        whytrinks_promocao_cabecalho_texto_desktop,
        whytrinks_promocao_cabecalho_texto_mobile,
        ativa_conta_sem_verificacao_identidade_no_fluxo_cadastro_estabelecimento,
        contato_whatsapp_comercial,
        fluxodecadastro_url_imagem_banner_cadastro,
        fluxodecadastro_texto_banner_cadastro,
        background_urls_tipo_estabelecimento_terminal_de_chegada,
        metricas_registro_ativo,
        metricas_registro_apppro_ativo,
        metricas_periodo_batch_em_segundos,
        metricas_tamanho_maximo_batch,
        metricas_tempo_de_cache_metricas_desativadas_client_side_em_minutos,
        metricas_serilog_selflog_ativado,
        metricas_serilog_selflog_caminhotxt,
        titulo_promocao_marketing,
        ultima_atualizacao_Ranking_Profissionais_Estendido,
        percentual_saldo_recompra_whatsapp,
        id_cob_formas_pagamento_ativas_whatsapp,
        quantidade_recompras_automaticas_mes_whatsapp,
        video_tutorial_cadastrar_profissionais,
        video_tutorial_como_vender_pacotes,
        video_tutorial_como_cadastrar_pacotes,
        video_tutorial_como_comissionar_profissional_na_venda_de_pacote,
        video_tutorial_personalizar_permissoes_de_acesso,
        ultima_atualizacao_ranking_produtos_estendido,
        ultima_atualizacao_ranking_clientes_estendido,
        ultima_atualizacao_ranking_servicos_estendido,
        ultima_atualizacao_ranking_pacotes_estendido,
        contaDigital_trampolin_api_key,
        contaDigital_trampolin_default_api_endpoint,
        contaDigital_trampolin_nome_do_bucketS3,
        contaDigital_gateway,
        codigo_nbs_clube_assinatura,
        forca_rowlock_na_transferencia_de_historicos_da_unificacao,
        limite_produtos_para_agendamento_sincronia_automatico,
        limite_servicos_para_agendamento_sincronia_automatico,
        meses_atras_busca_alcance,
        id_limite_plano_vigente_conta_digital,
        url_programa_de_parceria,
        limite_para_inativacao_via_gerenciamento,
        quantidade_maxima_estabelecimentos_ranking_estendido,
        link_guia_autoatendimento,
        limite_produtos_no_modelo_para_sincronia_automatica,
        limite_servicos_no_modelo_para_sincronia_automatica,
        limite_pacotes_no_modelo_para_sincronia_automatica,
        link_termo_de_uso_da_trinks,
        link_politica_de_privacidade_da_trinks,
        link_programa_de_parceria_da_trinks,
        pgtoOnline_pagarme_url_faq,
        taxa_antecipacao_pagarme,
        pgtoOnline_pagarme_habilitar_kyc,
        emails_aviso_contratacao_belezinha,
        ConciliacaoConnectStoneV2BearerToken,
        ConciliacaoConnectStoneV2EncryptedData,
        ConciliacaoConnectStoneV2RawData,
        dias_antecedencia_renovacao_assinatura,
        contaDigital_validade_em_segundos_token_sms,
        contaDigital_tempo_expiracao_em_segundos_token_sms,
        pagamentos_id_limite_padrao,
        pagamentos_porcentagem_em_decimal_para_valor_perto_limite_maximo_pagamento,
        link_sugestoes_comunidade,
        link_central_ajuda_autoatendimento,
        url_autoatendimento,
        pgtoOnline_numero_minimo_meses_credenciado_para_antecipar_pagamentos,
        nfse_max_rps_por_lote,
        tempo_seguranca_para_desfazer_lote,
        contaDigital_maximo_tentativas_autenticacao,
        contaDigital_quantidade_minutos_bloqueio_autenticacao,
        link_central_ajuda_boleto,
        link_central_ajuda_videos_treinamento,
        link_central_ajuda_duplicidade_nf,
        link_central_ajuda_codigo_numero_formato_invalido,
        link_central_ajuda_emissao_nf_consumidor,
        link_central_ajuda_erro_cadastro_codigo_de_barras_produto,
        link_central_ajuda_nfse_goiania,
        link_central_ajuda_nfse_rj,
        link_central_ajuda_nfse_bh,
        link_central_ajuda_nfse_pa,
        link_central_ajuda_nfse_sp,
        link_central_ajuda_nfse,
        link_central_ajuda_split,
        link_central_ajuda_belezinha_siclos,
        link_central_ajuda_belezinha_stone,
        link_central_ajuda_conciliacao_trinks_belezinha,
        link_central_ajuda_clube_de_assinaturas,
        link_central_ajuda_agendamento_google,
        link_central_ajuda_importacao_vale_profissional,
        link_central_ajuda_nf_pacote,
        link_central_ajuda_faq,
        link_central_ajuda_pacote,
        link_central_ajuda_certificado_digital,
        pgtoPorLink_gateways,
        contaDigital_como_funciona_permissoes,
        contaDigital_como_funciona_app_stone_ios,
        contaDigital_como_funciona_app_stone_andoid,
        contaDigital_como_funciona_ajuda_transferencias_stone,
        contaDigital_como_funciona_pagar_profissional,
        contaDigital_como_funciona_chave_pix,
        contaDigital_link_faq,
        habilita_venda_online_de_assinatura,
        quantidade_Reenvio_Rejeicao_nfse,
        limite_mensagens_processadas_envio_assincrono_crm_bonus,
        ativa_o_envio_de_eventos_para_o_rabbitmq,
        nome_do_bucketS3_logs,
        qtd_max_para_executar_sincronia,
        tempo_para_cancelamento_pedido_connect_pagarme_em_milissegundos,
        link_central_ajuda_pagamento_autoatendimento_siclos,
        link_central_ajuda_pagamento_autoatendimento_stone,
        maximo_de_linha_por_arquivo_importador_historico_de_cliente,
        contaDigital_quantidade_de_horas_limite_para_cancelar_transacao,
        link_redirecionamento_comunidade,
        login_web_log_no_elmah_erro_token_api_b2c,
        login_web_metodo_obtencao_token_api_b2c,
        login_web_log_mensagem_da_excecao,
        numero_telefone_msg_indica_trinks,
        tempo_expiracao_cache_disponibilidade_recursos_minutos,
        ativar_logs_postgresql_v2,
        serilog_selflog_logs_ativado_v2,
        serilog_selflog_logs_caminhotxt_v2,
        serilog_tempo_em_segundos_para_processamento_de_batches_v2,
        serilog_tamanho_maximo_do_batch_v2,
        tempo_de_validade_do_codigo_pix_no_link_de_pagamento,
        linkDePagamento_intervalo_polling_pix_milissegundos,
        quantidade_ids_por_busca_pagina_para_dados_geracao_RPS,
        quantidade_itens_pagina_para_dados_geracao_RPS,
        versao_minima_app_b2b_telefone_internacional,
        versao_minima_app_b2c_telefone_internacional,
        pgtoOnline_pagarme_v3_version_header,
        pgto_gateway_fatura_pagarmev5_api_key,
        pgto_gateway_fatura_pagarmev5_id_recebedor_trinks,
        pgto_gateway_fatura_novas_assinaturas,
        pgto_gateway_fatura_pix,
        pgto_gateway_fatura_webhook_username,
        pgto_gateway_fatura_webhook_password,
    }
}
