﻿using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Pessoas
{

    [Serializable]
    public class ParametrosFiltrosRelatorio
    {

        public ParametrosFiltrosRelatorio()
        {
            TipoData = TipoDataRelatorio.DataAtendimento;
        }

        private int _idEstabelecimentoProfissional;
        private Estabelecimento _estabelecimento;
        private DateTime? _dataInicial;

        private int _idPessoaJuridica;

        public int IdProfissional { get; set; }
        //public int IdProfissionalAssistente { get; set; }

        public int IdEstabelecimentoProfissional
        {
            get
            {
                if (_idEstabelecimentoProfissional == 0 && IdProfissional > 0 && Estabelecimento != null)
                {
                    EstabelecimentoProfissional =
                        Domain.Pessoas.EstabelecimentoProfissionalRepository.Obter(IdProfissional,
                            Estabelecimento.IdEstabelecimento);
                }
                return _idEstabelecimentoProfissional;
            }
            set { _idEstabelecimentoProfissional = value; }
        }

        public int IdPessoaJuridica
        {
            get
            {
                if (_idPessoaJuridica > 0)
                    return _idPessoaJuridica;
                if (IdEstabelecimento == 0)
                    return 0;

                _idPessoaJuridica = Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    .Where(f => f.IdEstabelecimento == IdEstabelecimento)
                    .Select(f => f.PessoaJuridica.IdPessoa)
                    .First();

                return _idPessoaJuridica;
            }
        }

        public int IdEstabelecimento { get; set; }
        public int IdFormaPagamento { get; set; }
        public int IdTipoFormaPagamento { get; set; }
        public int IdMotivoDesconto { get; set; }
        public bool DesconsiderarRestricaoDeDadosAPartirDeUmaData { get; set; }

        // @see http://jira.perlink.net/browse/TRINKS-4478
        public DateTime? DataInicial
        {
            get
            {
                if (DesconsiderarRestricaoDeDadosAPartirDeUmaData)
                    return _dataInicial;
                return Domain.Pessoas.EstabelecimentoRepository.TrataDataInicialComRestricaoDeDadosAPartirDeUmaData(IdEstabelecimento, _dataInicial);
            }
            set { _dataInicial = value; }
        }

        public String Filtro { get; set; }

        public DateTime? DataFinal { get; set; }

        public Estabelecimento Estabelecimento
        {
            get { return _estabelecimento; }
            set
            {
                _estabelecimento = value;
                IdEstabelecimento = value.IdEstabelecimento;
            }
        }

        public TipoDataRelatorio TipoData { get; set; }
        public TipoTransacaoRelatorio TipoTransacao { get; set; }
        public TipoFiltroTransacaoProduto TipoFiltroTransacaoProduto { get; set; }
        public AcessoBackoffice AcessoBackoffice { get; set; }
        public OrigemRelatorio OrigemRelatorio { get; set; }
        public bool ExibirEstornos { get; set; }
        public bool ExibirCreditoClienteExportacao { get; set; }
        public bool ExibirApenasComDescontos { get; set; }
        public bool ExibirApenasTransacoesComDescontoCashback { get; set; }
        public List<int> IdPessoasQueFecharam { get; set; }
        public List<int> IdFormasPagamentoSelecionadas { get; set; }

        public EstabelecimentoProfissional EstabelecimentoProfissional
        {
            get
            {
                return IdEstabelecimentoProfissional == 0
                    ? null
                    : Domain.Pessoas.EstabelecimentoProfissionalRepository.Load(IdEstabelecimentoProfissional);
            }
            set { IdEstabelecimentoProfissional = value.Codigo; }
        }

        public ParametrosPaginacao ParametrosPaginacao { get; set; }

        public int? NumeroComanda { get; set; }

        public TipoImpressaoComissaoEnum TipoDeImpressao { get; set; }

        public Conta ContaAutenticada { get; set; }
        public int IdRelacaoProfissional { get; set; }
        public int IdContaFinanceira { get; set; }
        public bool IncluirContaFinanceiraNoRelatorio { get; set; }
        public List<int> IdsProfissional { get; set; } = new List<int>();
        public bool ExibirFormaPagamentoNosRelatorios { get; set; }

        public static ParametrosFiltrosRelatorio ObterParaFiltrarComissoes(int idEstabelecimento, DateTime? dataInicio, DateTime? dataFim)
        {
            if (!Domain.WebContext.IdContaAutenticada.HasValue)
                throw new Exception("Conta autenticada não especificada.");

            var idPessoaDaContaAutenticada = Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value)?.Pessoa?.IdPessoa;
            if (!idPessoaDaContaAutenticada.HasValue)
                throw new Exception($"Não foi possível carregar Pessoa da conta #{Domain.WebContext.IdContaAutenticada.Value}.");

            var estabelecimentoProfissional = Domain.Pessoas.EstabelecimentoProfissionalRepository.ObterPorPessoaFisica(idPessoaDaContaAutenticada.Value, idEstabelecimento)
                ?? throw new Exception($"Não foi possível carregar EstabelecimentoProfissional da Pessoa #{idPessoaDaContaAutenticada.Value} no Estabelecimento #{idEstabelecimento}.");
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento)
                ?? throw new Exception($"Não foi possível carregar o estabelecimento #{idEstabelecimento}");

            var filtro = new ParametrosFiltrosRelatorio()
            {
                Estabelecimento = estabelecimento,
                IdEstabelecimentoProfissional = estabelecimentoProfissional.Codigo,
                DataInicial = dataInicio,
                DataFinal = dataFim,
            };

            return filtro;
        }
    }
}