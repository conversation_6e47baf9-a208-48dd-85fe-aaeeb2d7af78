﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ClubeDeAssinaturas;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.Pessoas;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Services
{

    public interface ILinkDePagamentoNoTrinksService : IService
    {
        LinkDePagamentoNoTrinks ObterPorIdLinkDoPagamento(int idLinkDePagamento);
        LinkDePagamentoNoTrinks GerarLinkDePagamentoParaCompraDeCredito(int idClienteEstabelecimento, decimal valor);
        bool LinkDePagamentoEstaDisponivel(int idEstabelecimento);
        bool EstabelcimentoJaAceitouOsTermosDeUso(int idEstabelecimento);
        Task SalvarQueOEstabelecimentoPermiteUtilizarLinkDePagamento(int idEstabelecimento, bool status, OrigemDeCadastroEnum origem);
        LinkDePagamentoNoTrinks GerarLinkDePagamentoRecorrenteParaOClube(int idClienteEstabelecimento, AssinaturaDoCliente pagamento);
        LinkDePagamentoNoTrinks GerarLinkDePagamentoDaMultaDeCancelamentoDoClubeDeAssinatura(int idClienteEstabelecimento, AssinaturaDoCliente pagamento);
        OrigemDoLinkDePagamentoEnum ObterOrigemPorIdLinkDoPagamento(int idLinkDePagamento);
        string ObterTipoDeFormaDePagamento(LinkDePagamento link);
        void AssociarEstabelecimentoAFormaDePagamentoLinkDePagamento(int idEstabelecimento);
        void InativarEstabelecimentoAFormaDePagamentoLinkDePagamento(int idEstabelecimento);
        LinkDePagamentoNoTrinks GerarLinkDePagamentoParaVendaDePacoteHotsite(int idClienteEstabelecimento, decimal valor, string nomeItem);
        LinkDePagamentoNoTrinks ObterPorIdLinkDoPagamentoCasoLinkSejaPago(int idLinkDePagamento);
        LinkDePagamento GerarLinkDePagamentoParaPagamentoAntecipadoHotsite(int idClienteEstabelecimento, decimal valor, string nomeItem);
        LinkDePagamento GerarLinkDePagamentoParaPromocaoOnline(int idClienteEstabelecimento, decimal valor, string nomeItem);
    }
}