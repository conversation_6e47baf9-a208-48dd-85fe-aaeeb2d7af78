﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Text;
using Perlink.Trinks.ClubeDeAssinaturas;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.PagamentosOnlineNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Services
{

    public class LinkDePagamentoNoTrinksService : BaseService, ILinkDePagamentoNoTrinksService
    {

        public LinkDePagamentoNoTrinks GerarLinkDePagamentoParaCompraDeCredito(int idClienteEstabelecimento, decimal valor)
        {
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarPagamentoOnlineNoTrinks(idClienteEstabelecimento, 0, valor);

            var linkDePagamento = GerarLinkDePagamento(new DadosParaCadastrarLinkDePagamentoNoTrinksDTO()
            {
                IdClienteEstabelecimento = idClienteEstabelecimento,
                ValorTotal = valor,
                Itens = new List<KeyValuePair<string, decimal>>() {
                    new KeyValuePair<string, decimal>("Crédito de cliente", valor)
                }
            });

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            return GerarLinkDePagamentoNoTrinks(
                idLinkDePagamento: linkDePagamento.IdLinkDePagamento,
                clienteEstabelecimento,
                pagamentoOnlineNoTrinks,
                OrigemDoLinkDePagamentoEnum.CreditoDeCliente
            );
        }

        public LinkDePagamentoNoTrinks GerarLinkDePagamentoParaVendaDePacoteHotsite(int idClienteEstabelecimento, decimal valor, string nomeItem)
        {
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarPagamentoOnlineNoTrinks(idClienteEstabelecimento, 0, valor);

            var linkDePagamento = GerarLinkDePagamento(new DadosParaCadastrarLinkDePagamentoNoTrinksDTO()
            {
                IdClienteEstabelecimento = idClienteEstabelecimento,
                ValorTotal = valor,
                Itens = new List<KeyValuePair<string, decimal>>() {
                    new KeyValuePair<string, decimal>(nomeItem, valor)
                }
            });

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            return GerarLinkDePagamentoNoTrinks(
                idLinkDePagamento: linkDePagamento.IdLinkDePagamento,
                clienteEstabelecimento,
                pagamentoOnlineNoTrinks,
                OrigemDoLinkDePagamentoEnum.VendaPacoteHotsite
            );
        }
        
        public LinkDePagamento GerarLinkDePagamentoParaPagamentoAntecipadoHotsite(int idClienteEstabelecimento, decimal valor, string nomeItem)
        {
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarPagamentoOnlineNoTrinks(idClienteEstabelecimento, 0, valor);

            var linkDePagamento = GerarLinkDePagamento(new DadosParaCadastrarLinkDePagamentoNoTrinksDTO()
            {
                IdClienteEstabelecimento = idClienteEstabelecimento,
                ValorTotal = valor,
                Itens = new List<KeyValuePair<string, decimal>>() {
                    new KeyValuePair<string, decimal>(nomeItem, valor)
                }
            });

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            GerarLinkDePagamentoNoTrinks(
                idLinkDePagamento: linkDePagamento.IdLinkDePagamento,
                clienteEstabelecimento,
                pagamentoOnlineNoTrinks,
                OrigemDoLinkDePagamentoEnum.Hotsite
            );

            return linkDePagamento;
        }

        public LinkDePagamentoNoTrinks GerarLinkDePagamentoRecorrenteParaOClube(int idClienteEstabelecimento, AssinaturaDoCliente assinatura)
        {
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarPagamentoOnlineNoTrinks(idClienteEstabelecimento, 0, assinatura.ValorAPagar);

            var linkDePagamento = GerarLinkDePagamento(new DadosParaCadastrarLinkDePagamentoNoTrinksDTO()
            {
                IdClienteEstabelecimento = idClienteEstabelecimento,
                ValorTotal = assinatura.ValorAPagar,
                Itens = new List<KeyValuePair<string, decimal>>() {
                    new KeyValuePair<string, decimal>(assinatura.Nome, assinatura.ValorAPagar)
                }
            });

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            var linkDePagamentoNoTrinks = GerarLinkDePagamentoNoTrinks(linkDePagamento.IdLinkDePagamento, clienteEstabelecimento, pagamentoOnlineNoTrinks, OrigemDoLinkDePagamentoEnum.ClubeDeAssinaturas);

            return linkDePagamentoNoTrinks;
        }

        public LinkDePagamentoNoTrinks GerarLinkDePagamentoDaMultaDeCancelamentoDoClubeDeAssinatura(int idClienteEstabelecimento, AssinaturaDoCliente assinatura)
        {
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarPagamentoOnlineNoTrinks(idClienteEstabelecimento, 0, assinatura.ValorAPagar);

            var linkDePagamento = GerarLinkDePagamento(new DadosParaCadastrarLinkDePagamentoNoTrinksDTO()
            {
                IdClienteEstabelecimento = idClienteEstabelecimento,
                ValorTotal = assinatura.MultaDeCancelamentoDaAssinatura.Valor,
                Itens = new List<KeyValuePair<string, decimal>>() {
                    new KeyValuePair<string, decimal>(assinatura.Nome, assinatura.MultaDeCancelamentoDaAssinatura.Valor)
                }
            });

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);
            var linkDePagamentoNoTrinks = GerarLinkDePagamentoNoTrinks(linkDePagamento.IdLinkDePagamento, clienteEstabelecimento, pagamentoOnlineNoTrinks, OrigemDoLinkDePagamentoEnum.MultaCancelamentoDeClubeDeAssinaturas);

            return linkDePagamentoNoTrinks;
        }
        
        public LinkDePagamento GerarLinkDePagamentoParaPromocaoOnline(int idClienteEstabelecimento, decimal valor, string nomeItem)
        {
            var pagamentoOnlineNoTrinks = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.GerarPagamentoOnlineNoTrinks(idClienteEstabelecimento, 0, valor);

            var linkDePagamento = GerarLinkDePagamento(new DadosParaCadastrarLinkDePagamentoNoTrinksDTO()
            {
                IdClienteEstabelecimento = idClienteEstabelecimento,
                ValorTotal = valor,
                Itens = new List<KeyValuePair<string, decimal>>() {
                    new KeyValuePair<string, decimal>(nomeItem, valor)
                },
                UrlValidacao = "/promocaoOnline/horario/disponibilidade",
            });

            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(idClienteEstabelecimento);

            GerarLinkDePagamentoNoTrinks(
                idLinkDePagamento: linkDePagamento.IdLinkDePagamento,
                clienteEstabelecimento,
                pagamentoOnlineNoTrinks,
                OrigemDoLinkDePagamentoEnum.PromocaoOnline
            );

            return linkDePagamento;
        }

        public LinkDePagamentoNoTrinks ObterPorIdLinkDoPagamento(int idLinkDePagamento)
        {
            return Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.Queryable().FirstOrDefault(f => f.IdLinkDePagamento == idLinkDePagamento);
        }

        public LinkDePagamentoNoTrinks ObterPorIdLinkDoPagamentoCasoLinkSejaPago(int idLinkDePagamento)
        {
            var ehLinkDePagamentoValido = Domain.LinksDePagamento.LinkDePagamentoRepository.LinkDoPagamentoFoiPago(idLinkDePagamento);

            return ehLinkDePagamentoValido ?
                   Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.ObterPorIdLinkDePagamento(idLinkDePagamento)
                   : null;
        }

        public bool LinkDePagamentoEstaDisponivel(int idEstabelecimento)
        {
            bool recebedorConfigurado = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository
                .ExisteEstabelecimentoAtivoEHabilitadoNosGateways(idEstabelecimento, GatewayEnum.PagarMe, GatewayEnum.PagarMeV5);

            if (!recebedorConfigurado)
                return false;

            var estabelecimento = new Estabelecimento(idEstabelecimento);

            bool linkDePagamentoDisponivel =
                Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                    estabelecimento, Recurso.LinkDePagamento).EstaDisponivel;

            if (!linkDePagamentoDisponivel)
                return false;

            bool pagamentoAntecipadoDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                estabelecimento, Recurso.PagamentoOnlineAntecipado).EstaDisponivel;

            if (!pagamentoAntecipadoDisponivel)
                return false;

            bool realizarPagamentoAntecipadoDisponivel =
                Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(
                    estabelecimento, Recurso.RealizarPagamentoOnlineAntecipado).EstaDisponivel;

            if (!realizarPagamentoAntecipadoDisponivel)
                return false;

            return true;
        }

        public bool EstabelcimentoJaAceitouOsTermosDeUso(int idEstabelecimento)
        {
            return Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.ObterPorIdEstabelecimento(idEstabelecimento).PermiteUtilizarLinkDePagamento;
        }

        public async Task SalvarQueOEstabelecimentoPermiteUtilizarLinkDePagamento(int idEstabelecimento, bool status, OrigemDeCadastroEnum origem)
        {

            var config = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.ObterPorIdEstabelecimento(idEstabelecimento);
            config.PermiteUtilizarLinkDePagamento = status;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            bool estabelecimentoEstaRegistradoComoRecebedor = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstabelecimentoEstaRegistradoComoRecebedor(estabelecimento.IdEstabelecimento, origem);

            if (!estabelecimentoEstaRegistradoComoRecebedor)
            {
                var dtoEstabelecimentoRecebedor = new ConfigurarEstabelecimentoRecebedorDTO
                {
                    IdEstabelecimento = estabelecimento.IdEstabelecimento,
                    CNPJ = estabelecimento.PessoaJuridica.CNPJ.SomenteLetrasENumeros(),
                    DataAberturaEmpresa = Calendario.Agora(),
                    Banco = new ParametrosTrinks<int>(ParametrosTrinksEnum.id_banco_temporario).ObterValor(),
                    AgenciaBancaria = "0000",
                    AgenciaBancariaDV = null,
                    NomeTitular = "CONTA TEMPORÁRIA",
                    NumeroConta = "00000",
                    NumeroContaDV = "0",
                    TipoDaConta = 1,
                    Origem = origem
                };

                await Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService
                    .ConfigurarEstabelecimentoComoRecebedor(dtoEstabelecimentoRecebedor);
            }

            AssociarEstabelecimentoAFormaDePagamentoLinkDePagamento(idEstabelecimento);
        }

        public OrigemDoLinkDePagamentoEnum ObterOrigemPorIdLinkDoPagamento(int idLinkDePagamento)
        {
            var origem = Domain.LinksDePagamento.LinkDePagamentoRepository.ObterOrigem(idLinkDePagamento);

            return origem;
        }

        public string ObterTipoDeFormaDePagamento(LinkDePagamento link)
        {
            var origem = Domain.LinksDePagamento.LinkDePagamentoRepository.ObterOrigem(link.IdLinkDePagamento);

            var strategy = LinkNoTrinksFactory.ObterPorOrigem(origem);

            return strategy.GerarTextoDeFormaDePagamento(link);
        }
        
        public void AssociarEstabelecimentoAFormaDePagamentoLinkDePagamento(int idEstabelecimento)
        {
            Estabelecimento estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
            
            AssociarPagamentoOnlinePorLink(estabelecimento);
            AssociarCreditoDeCliente(estabelecimento);
            AssociarPagarmeCredito(estabelecimento);

            if (!EstaHabilitadoParaPix(estabelecimento)) return;
            
            AssociarPagarmePix(estabelecimento);
        }

        private static void AssociarPagarmePix(Estabelecimento estabelecimento)
        {
            var formaDePagamentoPagarmePix = Domain.Financeiro.FormaPagamentoRepository.Queryable().Single(p => p.Id == (int)FormaPagamentoEnum.PagarmePix);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoPagarmePix, estabelecimento);
        }

        private static void AssociarPagarmeCredito(Estabelecimento estabelecimento)
        {
            var formaDePagamentoPagarmeCredito = Domain.Financeiro.FormaPagamentoRepository.Queryable().Single(p => p.Id == (int)FormaPagamentoEnum.PagarmeCredito);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoPagarmeCredito, estabelecimento);
        }

        private static void AssociarCreditoDeCliente(Estabelecimento estabelecimento)
        {
            var formaDePagamentoDeCreditoCliente = Domain.Financeiro.FormaPagamentoRepository.Queryable().Single(p => p.Id == (int)FormaPagamentoEnum.CreditoCliente);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoDeCreditoCliente, estabelecimento);
        }

        private static void AssociarPagamentoOnlinePorLink(Estabelecimento estabelecimento)
        {
            var formaDePagamentoPagamentoOnline = Domain.Financeiro.FormaPagamentoRepository.Queryable().Single(p => p.Id == (int)FormaPagamentoEnum.PagamentoOnlinePorLink);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaDePagamentoPagamentoOnline, estabelecimento);
        }

        public void InativarEstabelecimentoAFormaDePagamentoLinkDePagamento(int idEstabelecimento)
        {
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.Inativar((int)FormaPagamentoEnum.PagamentoOnlinePorLink, idEstabelecimento);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.Inativar((int)FormaPagamentoEnum.PagamentoOnlineHotsite, idEstabelecimento);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.Inativar((int)FormaPagamentoEnum.PagarmeCredito, idEstabelecimento);
            Domain.Pessoas.EstabelecimentoFormaPagamentoService.Inativar((int)FormaPagamentoEnum.PagarmePix, idEstabelecimento);
        }

        #region Métodos Privados
        private LinkDePagamento GerarLinkDePagamento(DadosParaCadastrarLinkDePagamentoNoTrinksDTO dadosParaCadastro)
        {
            var clienteEstabelecimento = Domain.Pessoas.ClienteEstabelecimentoRepository.Load(dadosParaCadastro.IdClienteEstabelecimento);
            var cliente = clienteEstabelecimento.Cliente;
            var estabelecimento = clienteEstabelecimento.Estabelecimento;

            return Domain.LinksDePagamento.LinkDePagamentoService.GerarNovoLinkDePagamento(
                new NovoLinkDePagamentoDTO
                {
                    IdRecebedor = estabelecimento.IdEstabelecimento,
                    IdComprador = clienteEstabelecimento.Codigo,
                    Valor = dadosParaCadastro.ValorTotal,
                    EmailComprador = cliente.PessoaFisica.Email,
                    NomeComprador = cliente.PessoaFisica.NomeCompleto,
                    NomeRecebedor = estabelecimento.NomeDeExibicaoNoPortal,
                    Itens = dadosParaCadastro.Itens,
                    UrlValidacao = dadosParaCadastro.UrlValidacao,
                });
        }

        private LinkDePagamentoNoTrinks GerarLinkDePagamentoNoTrinks(
            int idLinkDePagamento,
            ClienteEstabelecimento clienteEstabelecimento,
            PagamentoOnlineNoTrinks pagamentoOnline,
            OrigemDoLinkDePagamentoEnum origem)
        {

            var linkDePagamentoNoTrinks = new LinkDePagamentoNoTrinks(idLinkDePagamento, clienteEstabelecimento, pagamentoOnline, origem);

            Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksRepository.SaveNew(linkDePagamentoNoTrinks);

            return linkDePagamentoNoTrinks;
        }

        private bool EstaHabilitadoParaPix(Estabelecimento estabelecimento)
            => Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.HabilitarPixNoLinkDePagamento).EstaDisponivel;

        #endregion
    }
}