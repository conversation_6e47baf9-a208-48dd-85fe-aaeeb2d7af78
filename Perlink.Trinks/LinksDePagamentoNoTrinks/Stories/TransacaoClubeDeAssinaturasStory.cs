﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Auditing;
using Perlink.Trinks.ClubeDeAssinaturas;
using Perlink.Trinks.ClubeDeAssinaturas.DTO;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.LinksDePagamento;
using System;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories
{
    public class TransacaoClubeDeAssinaturasStory : BaseStory, ITransacaoClubeDeAssinaturasStory
    {
        public async Task Registrar(int idLinkDoPagamento, int idPagamento)
        {
            try
            {
                LinkDePagamentoNoTrinks linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterPorIdLinkDoPagamento(idLinkDoPagamento);

                LinkDePagamento linkDePagamento = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idLinkDoPagamento);

                if (!EhUmLinkValido(linkDePagamento))
                    return;

                await RealizarTransacaoDeClubePorLinkDePagamento(linkNoTrinks, idPagamento);
            }
            catch (Exception ex)
            {
                Elmah.ErrorSignal.FromCurrentContext().Raise(new BusinessException(ex.Message));
            }
        }

        [TransactionInitRequired]
        private async Task RealizarTransacaoDeClubePorLinkDePagamento(LinkDePagamentoNoTrinks link, int idPagamento)
        {
            LogService<TransacaoDeCreditoDeClienteStory>.Info("[TransacaoDeCreditoDeClienteStory] Realizar transação de clube por link de pagamento");
            var idAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterIdDaAssinaturaPeloLinkDePagamento(link.IdLinkDePagamento);
            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Load(idAssinatura);
            var pessoaDoClienteQuePagou = Domain.Pessoas.PessoaFisicaRepository.ObterPorId(link.ClienteEstabelecimento.Cliente.PessoaFisica.IdPessoa);
            var naoPossuiVigencia = Domain.ClubeDeAssinaturas.VigenciaDeAssinaturaRepository.ObterIdVigenciaMaisAntigaNaoPaga(assinatura.Id) == 0;

            try
            {
                if (AssinaturaValidaParaPagamento(assinatura))
                {
                    var pagamentoDaAssinatura = Domain.ClubeDeAssinaturas.PagamentosService.RegistrarPagamento(assinatura, Calendario.Agora(), naoPossuiVigencia, false, !naoPossuiVigencia);
                    var pagamento = link.PagamentoOnlineNoTrinks;

                    var dto = new GerarTransacaoDTO(assinatura, pagamentoDaAssinatura.Id, pessoaDoClienteQuePagou, pagamento.Taxas.Id);
                    var resultadoCheckOut = await Domain.ClubeDeAssinaturas.PagamentosService.GerarTransacaoFinanceiraComConfirmacaoDoPagamento(dto);

                    var transacao = Domain.Financeiro.TransacaoRepository.Load(resultadoCheckOut);

                    pagamento.Transacao = transacao;
                    pagamento.IdPagamentoOnline = idPagamento;
                    pagamento.IndicarQueFoiPagoComSucesso(assinatura.ValorAPagar);

                    Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamento);

                    LogService<TransacaoDeCreditoDeClienteStory>.Info("[TransacaoDeCreditoDeClienteStory] Transação de clube por link de pagamento finalizada");
                }
                else
                {
                    throw new Exception("O pagamento desta assinatura não pode ser concluído.");
                }
            }
            catch (Exception ex)
            {
                LogService<TransacaoDeCreditoDeClienteStory>.Error("Erro ao realizar pagamento " + ex.Message);
            }
        }

        private bool EhUmLinkValido(LinkDePagamento link)
        {
            return link.Ativo && link.StatusPagamento == LinksDePagamento.Enums.StatusPagamentoEnum.Pago;
        }

        private bool AssinaturaValidaParaPagamento(AssinaturaDoCliente assinatura)
        {
            if (!assinatura.PodeConfirmar())
                return false;

            if (assinatura.VigenciaAtual != null && assinatura.VigenciaAtual.FoiPago)
            {
                return false;
            }

            var aindaNaoEncerrouCobranca = assinatura.CobrancaDaAssinaturaEstaAtiva();

            if (!aindaNaoEncerrouCobranca)
            {
                return false;
            }

            return true;
        }
    }
}