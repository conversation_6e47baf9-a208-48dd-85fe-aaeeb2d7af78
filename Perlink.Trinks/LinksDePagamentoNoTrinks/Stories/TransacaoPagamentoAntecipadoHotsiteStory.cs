﻿using Castle.ActiveRecord;
using Perlink.DomainInfrastructure.Stories;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories
{

    public class TransacaoPagamentoAntecipadoHotsiteStory : BaseStory, ITransacaoPagamentoAntecipadoHotsiteStory
    {
        public async Task RegistrarPagamento(int idLinkDePagamento, int idPagamento)
        {
            try
            {
                LinkDePagamentoNoTrinks linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterPorIdLinkDoPagamento(idLinkDePagamento);
                LinkDePagamento linkDePagamento = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idLinkDePagamento);

                if (!EhUmLinkValido(linkDePagamento))
                    throw new BusinessException("Link de pagamento já foi pago ou está inativo.");

                await RegistrarPagamentoDoHorario(linkNoTrinks, idPagamento);
            }
            catch (Exception ex)
            {
                var exception = new BusinessException($"Erro ao realizar pagamento do link de id {idLinkDePagamento}.", ex);
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(exception));
                throw;
            }
        }

        private async Task RegistrarPagamentoDoHorario(LinkDePagamentoNoTrinks linkNoTrinks, int idPagamento)
        {
            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session = holder.CreateSession(typeof(ActiveRecordBase));

            using (var tx = session.BeginTransaction())
            {
                try
                {
                    await RegistarPagamentoDoHorario(linkNoTrinks, idPagamento);
                    tx.Commit();
                }
                catch
                {
                    tx.Rollback();
                    throw;
                }
            }
        }

        private async Task RegistarPagamentoDoHorario(LinkDePagamentoNoTrinks linkNoTrinks, int idPagamento)
        {
            LogInfo("Realizar pagamento antecipado no hotsite pelo link de pagamento");

            var idHorario = Domain.Pessoas.LinkDePagamentoDoHorarioRepository.ObterIdHorarioPorIdLinkDePagamento(linkNoTrinks.IdLinkDePagamento);
            var horario = Domain.Pessoas.HorarioRepository.ObterPorId(idHorario);
            
            var pagamentoOnlineTrinks = linkNoTrinks.PagamentoOnlineNoTrinks;
            pagamentoOnlineTrinks.IdPagamentoOnline = idPagamento;

            var pagamentoAntecipadoHotsiteDto = ObterPagamentoAntecipadoHotsiteDto(linkNoTrinks, idPagamento, idHorario);
            var resultadoCheckout = await Domain.Financeiro.TransacaoService.RealizarTransacaoPagamentoAntecipadoHotsite(pagamentoAntecipadoHotsiteDto);
            
            pagamentoOnlineTrinks.IndicarQueFoiPagoComSucesso(horario.Valor);
            pagamentoOnlineTrinks.Transacao = resultadoCheckout.Transacao;
            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamentoOnlineTrinks);
            
            horario.ConfirmarPagamentoAntecipado();
            Domain.Pessoas.HorarioService.ManterHorario(horario);
            
            LogInfo("Pagamento antecipado no hotsite pelo link de pagamento finalizado");
        }

        private void LogInfo(string message)
            => LogService<TransacaoPagamentoAntecipadoHotsiteStory>.Info($"[TransacaoPagamentoAntecipadoHotsiteStory] {message}");

        private PagamentoAntecipadoHotsiteDto ObterPagamentoAntecipadoHotsiteDto(LinkDePagamentoNoTrinks linkNoTrinks, int idPagamento, int idHorario)
        {
            var pagamento = Domain.Pagamentos.PagamentoRepository.Load(idPagamento);
            var numeroDeParcelas = pagamento.QuantidadeParcelas;
            var idEstabelecimento = linkNoTrinks.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento;
            var nomeItem = pagamento.Itens.Select(p => p.Nome).Single();

            var pagamentoAntecipadoHotsiteDto = new PagamentoAntecipadoHotsiteDto
            {
                Pagamento = pagamento,
                IdEstabelecimento = idEstabelecimento,
                NumeroParcelas = numeroDeParcelas,
                PessoaQuePagou = linkNoTrinks.ClienteEstabelecimento.Cliente.PessoaFisica,
                PessoaQueRecebeu = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento).PessoaJuridica,
                ValorPago = linkNoTrinks.PagamentoOnlineNoTrinks.ValorTotal,
                IdReferencia = idHorario,
                Nome = nomeItem,
            };

            return pagamentoAntecipadoHotsiteDto;
        }

        private static bool EhUmLinkValido(LinkDePagamento link)
            => link.Ativo && link.StatusPagamento == LinksDePagamento.Enums.StatusPagamentoEnum.Pago;
    }
}