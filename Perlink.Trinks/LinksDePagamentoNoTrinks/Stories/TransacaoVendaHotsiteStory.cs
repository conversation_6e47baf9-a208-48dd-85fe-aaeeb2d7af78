using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories
{
    public class TransacaoVendaHotsiteStory : BaseStory, ITransacaoVendaHotsiteStory
    {
        public async Task Registrar(int idLinkDoPagamento, int idPagamento)
        {
            try
            {
                LinkDePagamentoNoTrinks linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterPorIdLinkDoPagamento(idLinkDoPagamento);

                LinkDePagamento linkDePagamento = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idLinkDoPagamento);

                if (!EhUmLinkValido(linkDePagamento))
                    return;

                await RealizarTransacaoVendaHotsite(linkNoTrinks, idPagamento);
            }
            catch (Exception ex)
            {
                LogService<TransacaoVendaHotsiteStory>.Error("Erro ao realizar pagamento " + ex.Message);
            }
        }

        [TransactionInitRequired]
        private async Task RealizarTransacaoVendaHotsite(LinkDePagamentoNoTrinks link, int idPagamento)
        {
            LogService<TransacaoVendaHotsiteStory>.Info("[TransacaoVendaHotsiteStory] Realizar transação de clube por link de pagamento");

            try
            {

                var clienteQuePagou = Domain.Pessoas.ClienteEstabelecimentoRepository.ObterPessoaFisicaDoCliente(link.ClienteEstabelecimento.Codigo);

                var pagamento = link.PagamentoOnlineNoTrinks;
                pagamento.IdPagamentoOnline = idPagamento;

                var vendaHotsiteDTO = ObterVendaHotsiteDTO(link, idPagamento);

                var resultadoCheckOut = await Domain.Financeiro.TransacaoService.GerarTransacaoFinanceiraVendaPacoteNoHotsite(vendaHotsiteDTO, clienteQuePagou);

                pagamento.Transacao = resultadoCheckOut.Transacao;

                pagamento.IndicarQueFoiPagoComSucesso(resultadoCheckOut.Transacao.TotalPago.Value);

                Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamento);

                LogService<TransacaoVendaHotsiteStory>.Info("[TransacaoVendaHotsiteStory] Transação de clube por link de pagamento finalizada");

            }
            catch (Exception ex)
            {
                LogService<TransacaoVendaHotsiteStory>.Error("Erro ao realizar pagamento " + ex.Message);
            }
        }

        private bool EhUmLinkValido(LinkDePagamento link)
        {
            return link.Ativo && link.StatusPagamento == LinksDePagamento.Enums.StatusPagamentoEnum.Pago;
        }

        private VendaHotsiteDTO ObterVendaHotsiteDTO(LinkDePagamentoNoTrinks link, int idPagamento)
        {

            var idPacote = Domain.Pacotes.LinkDePagamentoDoPacoteRepository.ObterIdPacoteClientePeloIdDoLink(link.IdLinkDePagamento);
            var pacote = Domain.Pacotes.PacoteRepository.Load(idPacote);
            var pagamento = Domain.Pagamentos.PagamentoRepository.Load(idPagamento);
            var numeroDeParcelas = pagamento.QuantidadeParcelas;

            var vendaHotsiteDTO = new VendaHotsiteDTO
            {
                Pagamento = pagamento,
                IdEstabelecimento = pacote.Estabelecimento.IdEstabelecimento,
                NumeroParcelas = numeroDeParcelas,
                Nome = pacote.Nome,
                Itens = new List<ItemParaVendaDTO> {
                    new ItemParaVendaDTO {
                        Quantidade = 1,
                        ValorUnitario = pacote.Valor,
                        TipoItem = TipoItemEnum.pacote,
                        IdReferenciaItem = pacote.Id
                    }
                }
            };

            return vendaHotsiteDTO;
        }
    }
}
