﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Adapters;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.Vendas;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories
{
    public class TransacaoDeCreditoDeClienteStory : BaseStory, ITransacaoDeCreditoDeClienteStory
    {
        public async Task Registrar(int idLinkDoPagamento, int idPagamento)
        {
            try
            {
                LinkDePagamentoNoTrinks linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterPorIdLinkDoPagamento(idLinkDoPagamento);

                LinkDePagamento linkDePagamento = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idLinkDoPagamento);

                if (!EhUmLinkValido(linkDePagamento))
                    return;

                await RealizarTransacaoDeCreditoPorLinkDePagamento(linkNoTrinks, idPagamento);
            }
            catch (Exception ex)
            {
                Elmah.ErrorSignal.FromCurrentContext().Raise(new BusinessException(ex.Message));
            }
        }

        [TransactionInitRequired]
        private async Task RealizarTransacaoDeCreditoPorLinkDePagamento(LinkDePagamentoNoTrinks link, int idPagamento)
        {
            LogService<TransacaoDeCreditoDeClienteStory>.Info("[TransacaoDeCreditoDeClienteStory] Realizar transação de credito por link de pagamento");
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(link.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento);
            var pessoaDoClienteQuePagou = Domain.Pessoas.PessoaFisicaRepository.ObterPorId(link.ClienteEstabelecimento.Cliente.PessoaFisica.IdPessoa);
            Venda venda = Domain.Vendas.VendaRepository.Factory.CreateParaRealizarTransacao(
                estabelecimento: estabelecimento,
                pessoaQuePagou: pessoaDoClienteQuePagou,
                dataHoraDaTransacao: Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterDataHoraTransacaoCorreta(link.PagamentoOnlineNoTrinks.DataHoraPagamento),
                dataReferencia: Calendario.Agora(),
                pessoaQueRealizou: pessoaDoClienteQuePagou,
                tipoTransacao: TipoTransacaoEnum.Pagamento,
                comentarioFechamentoConta: "",
                comentarioEstorno: "");

            var transacao = venda.Transacao;
            var pagamento = link.PagamentoOnlineNoTrinks;
            var metodo = Domain.Pagamentos.PagamentoRepository.ObterMetodoPorIdPagamentos(idPagamento);

            Domain.Financeiro.TransacaoService.CriarOuAssociarFormaPagamentoSeNecessario(estabelecimento.IdEstabelecimento);
            transacao.FormasPagamento.Add(GerarFormaPagamentoParaCreditoPorLinkDePagamento(metodo ?? MetodoDePagamentoNoGatewayEnum.CartaoDeCredito,
                                                                                           estabelecimento.IdEstabelecimento,
                                                                                           link.PagamentoOnlineNoTrinks.ValorTotal,
                                                                                           venda.Transacao));
            Domain.Financeiro.TransacaoService
                .ColocarCompraDeCreditoNaTransacao(transacao: transacao,
                                                   formaPagamentoPrePago: FormaPagamentoPrePagoEnum.CreditoCliente, //FormaPagamentoPrePagoEnum.CreditoDePagamentoOnlinePorLink,
                                                   valorDoCredito: link.PagamentoOnlineNoTrinks.ValorTotal);
            
            var idTransacaoFormaPagamentoCreditoDeCliente = transacao.FormasPagamento.Single(tfp => tfp.FormaPagamento == FormaPagamentoEnum.CreditoCliente).IdTransacaoFormaPagamento;
            transacao.AdicionarTransacaoItem("Crédito de Cliente", TransacaoItemTipo.CreditoDeCliente, idTransacaoFormaPagamentoCreditoDeCliente);
            
            var resultadoCheckOut = await Domain.Financeiro.TransacaoService.RealizarCheckOut(new Financeiro.DTO.CheckoutDTO()
            {
                Transacao = transacao,
                QuemMarcou = venda.Transacao.PessoaQuePagou,
                Venda = venda,
                EmitirNFC = false,
                IdTransacaoPOS = null,
                ControlarCaixaSeNecessario = false,
                ControlarPagamentoPOS = false
            });

            pagamento.Transacao = resultadoCheckOut.Transacao;
            pagamento.IdPagamentoOnline = idPagamento;
            pagamento.IndicarQueFoiPagoComSucesso(pagamento.ValorTotal);
            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamento);
            LogService<TransacaoDeCreditoDeClienteStory>.Info("[TransacaoDeCreditoDeClienteStory] Transação de credito por link de pagamento finalizada");
        }

        private TransacaoFormaPagamento GerarFormaPagamentoParaCreditoPorLinkDePagamento(MetodoDePagamentoNoGatewayEnum metodo, int idEstabelecimento, decimal ValorTotal, Transacao transacao)
        {
            var formaPagamento = metodo.ToFormaPagamentoEnum();
            var transacaoFormaPagamentoDePagamentoOnline = Domain.Financeiro.TransacaoService
                .MontarTransacaoFormaPagamento(transacao, (int)formaPagamento, idEstabelecimento, ValorTotal);

            if (metodo == MetodoDePagamentoNoGatewayEnum.Pix)
            {
                var taxas = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService
                    .ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(idEstabelecimento, transacaoFormaPagamentoDePagamentoOnline.FormaPagamento.ToMetodoDePagamentoNoGatewayEnum());
                var taxasDTO = new TaxasDTO(taxas.ObterPercentualTotalPorTransacao(), taxas.ObterValorFixoTotalPorTransacao());
                
                transacaoFormaPagamentoDePagamentoOnline.PercentualCobradoPelaOperadora = taxasDTO.PercentualCobradoPelaOperadora;
                transacaoFormaPagamentoDePagamentoOnline.ValorFixoCobradoPelaOperadora = taxasDTO.ValorFixoCobradoPelaOperadora;
            }

            return transacaoFormaPagamentoDePagamentoOnline;
        }

        private bool EhUmLinkValido(LinkDePagamento link)
        {
            return link.Ativo && link.StatusPagamento == LinksDePagamento.Enums.StatusPagamentoEnum.Pago;
            //TODO:Duvida - Terá tempo maximo para efetuar um pagamento? Se sim será necessario adicionar validação da data de pagamento
        }
    }
}