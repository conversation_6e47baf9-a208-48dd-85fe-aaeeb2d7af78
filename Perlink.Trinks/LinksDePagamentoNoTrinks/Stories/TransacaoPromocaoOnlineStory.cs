﻿using Castle.ActiveRecord;
using Elmah;
using Perlink.DomainInfrastructure.Stories;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.PagamentosOnlineNoTrinks;
using Perlink.Trinks.PagamentosOnlineNoTrinks.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.PromocoesOnline;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ApplicationException = System.ApplicationException;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Stories
{
    public class TransacaoPromocaoOnlineStory : BaseStory, ITransacaoPromocaoOnlineStory
    {
        public async Task RegistrarPagamento(int idLinkDePagamento, int idPagamento)
        {
            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session = holder.CreateSession(typeof(ActiveRecordBase));

            using (var tx = session.BeginTransaction())
            {
                try
                {
                    LinkDePagamentoNoTrinks linkNoTrinks = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterPorIdLinkDoPagamento(idLinkDePagamento);
                    LinkDePagamento linkDePagamento = Domain.LinksDePagamento.LinkDePagamentoRepository.Load(idLinkDePagamento);

                    if (!EhUmLinkValido(linkDePagamento))
                        throw new BusinessException("Link de pagamento já foi pago ou está inativo.");
                    
                    await CriarERegistarPagamentoDoHorario(linkNoTrinks, idPagamento);
                    tx.Commit();
                }
                catch (Exception ex)
                {
                    tx.Rollback();
                    LogService<TransacaoPromocaoOnlineStory>.Error($"Erro ao realizar pagamento do link de id {idLinkDePagamento}: " + ex.Message);
                    ErrorLog.GetDefault(null).Log($"Erro ao realizar pagamento do link de id {idLinkDePagamento}", ex);
                    throw;
                }
            }
        }

        [TransactionInitRequired]
        private async Task CriarERegistarPagamentoDoHorario(LinkDePagamentoNoTrinks linkNoTrinks, int idPagamento)
        {
            LogService<TransacaoPromocaoOnlineStory>.Info("[PagamentoOnlineAntecipadoPeloHotsiteStory] Realizar pagamento antecipado no hosite pelo link de pagamento");

            var agendamentoTemp = Domain.PromocoesOnline.AgendamentoTemporarioRepository.Queryable()
                .FirstOrDefault(a => a.LinkDePagamento.IdLinkDePagamento == linkNoTrinks.IdLinkDePagamento);

            if (agendamentoTemp is null)
                throw new ApplicationException("Agendamento temporário não encontrado.");

            var pagamentoOnlineTrinks = linkNoTrinks.PagamentoOnlineNoTrinks;
            pagamentoOnlineTrinks.IdPagamentoOnline = idPagamento;
            
            var pagamentoAntecipadoHotsiteDto = ObterPagamentoAntecipadoHotsiteDto(linkNoTrinks, idPagamento, agendamentoTemp.Id);
            var resultadoCheckout = await Domain.Financeiro.TransacaoService.RealizarTransacaoPagamentoAntecipadoHotsite(pagamentoAntecipadoHotsiteDto);
            
            var idHorario = CriarAgendamento(linkNoTrinks, agendamentoTemp);

            ConcluirPagamentoOnlineTrinks(pagamentoOnlineTrinks, agendamentoTemp, resultadoCheckout);
            AtualizarTransacaoItem(resultadoCheckout.Transacao, idHorario);

            Domain.PromocoesOnline.PromocaoOnlineService.AtualizarEntidadesComHorario(agendamentoTemp.PromocaoOnline, linkNoTrinks.IdLinkDePagamento, idHorario);
            
            LogService<TransacaoPromocaoOnlineStory>.Info("[TransacaoVendaHotsiteStory] Pagamento antecipado no hosite pelo link de pagamento finalizado");
        }

        private PagamentoAntecipadoHotsiteDto ObterPagamentoAntecipadoHotsiteDto(LinkDePagamentoNoTrinks linkNoTrinks, int idPagamento, int idAgendamentoTemporario)
        {
            var pagamento = Domain.Pagamentos.PagamentoRepository.Load(idPagamento);
            var numeroDeParcelas = pagamento.QuantidadeParcelas;
            var idEstabelecimento = linkNoTrinks.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento;
            var nomeItem = pagamento.Itens.Select(p => p.Nome).Single();

            var pagamentoAntecipadoHotsiteDto = new PagamentoAntecipadoHotsiteDto
            {
                Pagamento = pagamento,
                IdEstabelecimento = idEstabelecimento,
                NumeroParcelas = numeroDeParcelas,
                PessoaQuePagou = linkNoTrinks.ClienteEstabelecimento.Cliente.PessoaFisica,
                PessoaQueRecebeu = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento).PessoaJuridica,
                ValorPago = linkNoTrinks.PagamentoOnlineNoTrinks.ValorTotal,
                IdReferencia = idAgendamentoTemporario,
                Nome = nomeItem,
            };

            return pagamentoAntecipadoHotsiteDto;
        }
        
        private static int CriarAgendamento(LinkDePagamentoNoTrinks linkNoTrinks, AgendamentoTemporario agendamentoTemp)
        {
            var guid = Domain.LinksDePagamento.LinkDePagamentoRepository.ObterIdentificador(linkNoTrinks.IdLinkDePagamento);
            var horarioDisponivel = Domain.PromocoesOnline.PromocaoOnlineService.VerificarSeHorarioEstaDisponivel(guid);
            
            var horario = horarioDisponivel
                ? Domain.Pessoas.AgendaService.RealizarAgendamentoWebPelaPromocaoOnline(agendamentoTemp)
                : Domain.Pessoas.AgendaService.RealizarAgendamentoBalcaoPelaPromocaoOnline(agendamentoTemp); // para casos de concorrência no horário (exceções)
            
            return horario.Id;
        }
        
        private static void ConcluirPagamentoOnlineTrinks(PagamentoOnlineNoTrinks pagamentoOnlineTrinks,
            AgendamentoTemporario agendamentoTemp, ResultadoCheckOutDTO resultadoCheckout)
        {
            pagamentoOnlineTrinks.IndicarQueFoiPagoComSucesso(agendamentoTemp.PromocaoOnline.CalcularValorComDesconto());
            pagamentoOnlineTrinks.Transacao = resultadoCheckout.Transacao;
            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamentoOnlineTrinks);
        }

        private static void AtualizarTransacaoItem(Transacao transacao, int idHorario)
        {
            var transacaoItem = transacao.TransacaoItens.First();
            transacaoItem.IdObjetoReferencia = idHorario;
            Domain.Financeiro.TransacaoItemRepository.Update(transacaoItem);
        }

        private static bool EhUmLinkValido(LinkDePagamento link)
            => link.Ativo && link.StatusPagamento == LinksDePagamento.Enums.StatusPagamentoEnum.Pago;
    }
}