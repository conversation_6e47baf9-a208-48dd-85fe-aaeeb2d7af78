﻿using Perlink.Trinks.LinksDePagamento.DTOs;
using System.Linq;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Repositories
{
    public partial class LinkDePagamentoNoTrinksRepository : ILinkDePagamentoNoTrinksRepository
    {
        public LinkDePagamentoNoTrinks ObterPorIdLinkDePagamento(int idLinkDePagamento)
        {
            return Queryable().FirstOrDefault(lpnt => lpnt.IdLinkDePagamento == idLinkDePagamento);
        }

        public ClienteDTO ObterDadosDoClientePorLinkDePagamento(int idLinkDePagamento)
        {
            var informacoesDoCliente = Queryable()
                .Where(lpnt => lpnt.IdLinkDePagamento == idLinkDePagamento)
                .Select(l => new {
                    Nome = l.ClienteEstabelecimento.Cliente.PessoaFisica.NomeCompleto,
                    Telefone = l.ClienteEstabelecimento.Cliente.PessoaFisica.Telefones.FirstOrDefault(),
                    Email = l.ClienteEstabelecimento.Cliente.PessoaFisica.Email,
                })
                .FirstOrDefault();

            return new ClienteDTO(
                nome: informacoesDoCliente.Nome,
                telefone: informacoesDoCliente.Telefone.ToString(),
                email: informacoesDoCliente.Email
            );
        }
    }
}
