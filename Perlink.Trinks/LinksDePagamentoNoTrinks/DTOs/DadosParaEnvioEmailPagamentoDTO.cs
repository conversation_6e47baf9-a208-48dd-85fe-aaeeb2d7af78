﻿using Perlink.Trinks.LinksDePagamento;
using System;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs
{
    public class DadosParaEnvioEmailPagamentoDTO
    {
        public LinkDePagamento LinkDePagamento { get; private set; }
        public int IdLinkPagamento { get; private set; }
        public DateTime DataPagamento { get; private set; }
        public decimal Valor { get; private set; }

        public int NumeroParcela { get; set; }
        public DadosCompradorParaEnvioEmailPagamentoDTO Comprador { get; set; }
        public DadosRecebedorParaEnvioEmailPagamentoDTO Recebedor { get; set; }

        public DadosParaEnvioEmailPagamentoDTO(LinkDePagamento linkDePagamento, int numeroParcela, DadosCompradorParaEnvioEmailPagamentoDTO comprador, DadosRecebedorParaEnvioEmailPagamentoDTO recebedor)
        {
            LinkDePagamento = linkDePagamento;
            IdLinkPagamento = linkDePagamento.IdLinkDePagamento;
            Valor = linkDePagamento.Valor;
            DataPagamento = linkDePagamento.DataPagamento ?? throw new Exception("Data invalida");
            NumeroParcela = numeroParcela;
            NumeroParcela = numeroParcela;
            Comprador = comprador;
            Recebedor = recebedor;
        }
    }

    public class DadosRecebedorParaEnvioEmailPagamentoDTO
    {
        public int IdEstabelecimento { get; set; }
        public string NomeEstabelecimento { get; set; }
        public string EmailResponsavelFinanceiro { get; set; }
        public string NomeResponsavelFinanceiro { get; set; }

        public DadosRecebedorParaEnvioEmailPagamentoDTO(int idEstabelecimento, string nome, string emailResponsavelFinanceiro, string nomeResponsavelFinanceiro)
        {
            IdEstabelecimento = idEstabelecimento;
            NomeEstabelecimento = nome;
            EmailResponsavelFinanceiro = emailResponsavelFinanceiro;
            NomeResponsavelFinanceiro = nomeResponsavelFinanceiro;
        }
    }

    public class DadosCompradorParaEnvioEmailPagamentoDTO
    {
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public string Email { get; set; }

        public DadosCompradorParaEnvioEmailPagamentoDTO(string nome, string cpfCnpj, string email)
        {
            Nome = nome;
            CpfCnpj = cpfCnpj;
            Email = email;
        }
    }
}
