﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public interface ILinkDePagamentoStrategy
    {
        Task RealizarTransacao(int idLinkDePagamento, int? idPagamento);
        DadosDoLinkParaPagamentoDTO ObterDadosDeUmLink(LinkDePagamento link);
        List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoAceitos(LinkDePagamento linkDePagamento);
        void EnviarEmailComprovantePagamento(DadosParaEnvioEmailPagamentoDTO dto);
        string GerarTextoDeFormaDePagamento(LinkDePagamento link);
    }
}
