﻿using Elmah;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using System.Collections.Generic;
using Perlink.Trinks.Pessoas.DTO;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public class PagamentoOnlinePeloHotsiteStrategy : ILinkDePagamentoStrategy
    {
        public async Task RealizarTransacao(int idLinkDePagamento, int? idPagamento)
        {
            if (idPagamento != null)
            {
                await Domain.LinksDePagamentoNoTrinks.TransacaoPagamentoAntecipadoHotsiteStory.RegistrarPagamento(
                    idLinkDePagamento, idPagamento.Value);
                return;
            }

            ErrorLog.GetDefault(null).Log($"[PagamentoOnlinePeloHotsiteStrategy] Link de pagamento ({idLinkDePagamento}) não possui id_pagamento");
            throw new ArgumentNullException();
        }

        public DadosDoLinkParaPagamentoDTO ObterDadosDeUmLink(LinkDePagamento link)
        {
            var aceitaParcelamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.EstabelecimentoAceitaParcelamento(link.IdRecebedor, (int)FormaPagamentoEnum.PagarmeCredito);
            
            var dto = new DadosDoLinkParaPagamentoDTO(link, ObterMetodosDePagamentoAceitos(link))
            {
                AceitaParcelamento = aceitaParcelamento,
                TipoDeFormaDePagamento = GerarTextoDeFormaDePagamento(link),
                MensagemDeEnvioParaWhatsApp = "Realize o pagamento acessando o link:",
                Itens = link.Itens.Select(i => new ItensDoLinkDTO { Nome = i.Nome, Valor = i.Valor }).ToList(),
            };
            
            if (aceitaParcelamento)
            {
                var parcelas = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterListaDeParcelas(link.IdRecebedor, (int)FormaPagamentoEnum.PagarmeCredito);
                dto.Parcelas = ObterParcelas(parcelas, link.Valor);
            }

            return dto;
        }

        public List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoAceitos(LinkDePagamento linkDePagamento)
            => Domain.LinksDePagamento.LinkDePagamentoService
                .ListarTodosMetodosDePagamentoParaEstabelecimento(linkDePagamento.IdRecebedor);

        public void EnviarEmailComprovantePagamento(DadosParaEnvioEmailPagamentoDTO dto)
        {
            Domain.LinksDePagamento.EnvioEmailService.EnviarEmailComprovantePagamentoPadrao(new EmailComprovantePagamentoDTO
            {
                NomeComprador = dto.Comprador.Nome,
                CpfComprador = dto.Comprador.CpfCnpj,
                EmailComprador = dto.Comprador.Email,
                NomeRecebedor = dto.Recebedor.NomeEstabelecimento,
                ValorTotal = dto.Valor,
                TipoPagamento = GerarTextoDeFormaDePagamento(dto.LinkDePagamento),
                DataHora = dto.DataPagamento,
            });

        }
        
        private List<ParcelasDTO> ObterParcelas(List<ParcelaFormaPagamentoDTO> parcelas, decimal valor)
            => parcelas.Select(parcela => ObterParcela(valor, parcela.NumeroParcela)).ToList();
        
        private ParcelasDTO ObterParcela(decimal valor, int numeroParcela)
        {
            var valorParcela = decimal.Round(valor / numeroParcela, 2);

            return new ParcelasDTO
            {
                NumeroParcela = numeroParcela,
                TextoParcela = numeroParcela == 1 ? $"À vista, por R$ {valorParcela}" : $"{numeroParcela}x de R$ {valorParcela}"
            };
        }

        public string GerarTextoDeFormaDePagamento(LinkDePagamento link)
        {
            var metodosDePagamento = ObterMetodosDePagamentoAceitos(link);
            return string.Join(", ", metodosDePagamento.ConvertAll(m => m.ObterDescricao()));
        }
    }
}
