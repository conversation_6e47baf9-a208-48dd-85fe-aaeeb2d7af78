﻿using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;
using System.Collections.Generic;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public class LinkNoTrinksFactory
    {

        public static ILinkDePagamentoStrategy ObterPorOrigem(OrigemDoLinkDePagamentoEnum origem)
        {
            var strategies = new Dictionary<OrigemDoLinkDePagamentoEnum, ILinkDePagamentoStrategy>
            {
                { OrigemDoLinkDePagamentoEnum.CreditoDeCliente, new CreditoDeClienteStrategy() },
                { OrigemDoLinkDePagamentoEnum.Hotsite, new PagamentoOnlinePeloHotsiteStrategy() },
                { OrigemDoLinkDePagamentoEnum.ClubeDeAssinaturas, new ClubeDeAssinaturasStrategy() },
                { OrigemDoLinkDePagamentoEnum.MultaCancelamentoDeClubeDeAssinaturas, new MultaCancelamentoClubeDeAssinaturasStrategy() },
                { OrigemDoLinkDePagamentoEnum.VendaPacoteHotsite, new VendaPacoteHotsiteStrategy() },
                { OrigemDoLinkDePagamentoEnum.PromocaoOnline, new PromocaoOnlineStrategy() }
            };

            return strategies[origem];
        }
    }
}
