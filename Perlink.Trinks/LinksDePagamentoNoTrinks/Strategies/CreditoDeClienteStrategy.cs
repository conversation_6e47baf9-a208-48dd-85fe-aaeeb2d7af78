﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public class CreditoDeClienteStrategy : ILinkDePagamentoStrategy
    {
        public async Task RealizarTransacao(int idLinkDePagamento, int? idPagamento)
        {
            await Domain.LinksDePagamentoNoTrinks.TransacaoDeCreditoDeClienteStory.Registrar(idLinkDePagamento, idPagamento.Value);
        }

        public DadosDoLinkParaPagamentoDTO ObterDadosDeUmLink(LinkDePagamento link)
        {
            var tipoFormaDePagamento = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterOrigemPorIdLinkDoPagamento(link.IdLinkDePagamento);

            var dto = new DadosDoLinkParaPagamentoDTO(link, ObterMetodosDePagamentoAceitos(link))
            {
                AceitaParcelamento = false,
                TipoDeFormaDePagamento = GerarTextoDeFormaDePagamento(link),
                MensagemDeEnvioParaWhatsApp = "Realize o pagamento acessando o link:",
                Itens = link.Itens.Select(i => new ItensDoLinkDTO { Nome = i.Nome, Valor = i.Valor }).ToList(),
            };

            return dto;
        }

        public List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoAceitos(LinkDePagamento linkDePagamento)
            => Domain.LinksDePagamento.LinkDePagamentoService
                .ListarTodosMetodosDePagamentoParaEstabelecimento(linkDePagamento.IdRecebedor);

        public void EnviarEmailComprovantePagamento(DadosParaEnvioEmailPagamentoDTO dto)
        {
            Domain.LinksDePagamento.EnvioEmailService.EnviarEmailComprovantePagamentoPadrao(new EmailComprovantePagamentoDTO
            {
                NomeComprador = dto.Comprador.Nome,
                CpfComprador = dto.Comprador.CpfCnpj,
                EmailComprador = dto.Comprador.Email,
                NomeRecebedor = dto.Recebedor.NomeEstabelecimento,
                ValorTotal = dto.Valor,
                TipoPagamento = GerarTextoDeFormaDePagamento(dto.LinkDePagamento),
                DataHora = dto.DataPagamento
            });
        }

        public string GerarTextoDeFormaDePagamento(LinkDePagamento link)
        {
            var metodosAceitos = ObterMetodosDePagamentoAceitos(link);
            return string.Join(", ", metodosAceitos.Select(m => m.ObterDescricao()));
        }
    }
}
