﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.ClubeDeAssinaturas;
using Perlink.Trinks.ClubeDeAssinaturas.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public class ClubeDeAssinaturasStrategy : ILinkDePagamentoStrategy
    {
        public async Task RealizarTransacao(int idLinkDePagamento, int? idPagamento)
        {
            await Domain.LinksDePagamentoNoTrinks.TransacaoClubeDeAssinaturasStory.Registrar(idLinkDePagamento, idPagamento.Value);
        }

        public DadosDoLinkParaPagamentoDTO ObterDadosDeUmLink(LinkDePagamento link)
        {
            var idAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterIdDaAssinaturaPeloLinkDePagamento(link.IdLinkDePagamento);
            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPorIdAssinatura(idAssinatura);

            var dto = new DadosDoLinkParaPagamentoDTO(link, ObterMetodosDePagamentoAceitos(link))
            {
                TipoDeFormaDePagamento = GerarTextoDeFormaDePagamento(link),
                MensagemDeEnvioParaWhatsApp = "Pague sua assinatura clicando no link abaixo:",
                Itens = link.Itens.Select(i => new ItensDoLinkDTO { Nome = i.Nome, Valor = i.Valor }).ToList(),
                NomeDaCompra = assinatura.Nome,
                AceitaParcelamento = false,
                SubItens = assinatura.BeneficiosDaAssinatura.Select(p => new SubItensDoLinkDTO()
                {
                    IdReferencia = p.Id,
                    Nome = p.NomeDoItem,
                    TextoQuantidade = p.Beneficio.ConsumoLimitado ?
                        $"{p.Beneficio.QuantidadeMaximaConsumo} {(p.Beneficio.CicloDeConsumo == CicloDeConsumoEnum.Especifico ? "por " + p.Beneficio.PrazoParaConsumo + " meses" : "por mês")}"
                    : "Ilimitado"
                }).ToList(),
            };
            return dto;
        }

        public List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoAceitos(LinkDePagamento linkDePagamento)
        {
            // No momento, o Clube de Assinaturas só poderá ser pago com cartão de crédito
            // Como todos os gateways atuais (Zoop, PagarMe e PagarMeV5) aceitam cartão de crédito,
            // não é necessário verificar qual gateway está sendo utilizado pelo recebedor
            return new List<MetodoDePagamentoNoGatewayEnum> { MetodoDePagamentoNoGatewayEnum.CartaoDeCredito };
        }

        public void EnviarEmailComprovantePagamento(DadosParaEnvioEmailPagamentoDTO dto)
        {
            var idAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDaAssinaturaRepository.ObterIdDaAssinaturaPeloLinkDePagamento(dto.IdLinkPagamento);
            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.Queryable().Where(a => a.Id.Equals(idAssinatura)).FirstOrDefault();
            var beneficiosDaAssinatura = assinatura.BeneficiosDaAssinatura;
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(assinatura.IdEstabelecimento);

            Domain.LinksDePagamento.EnvioEmailService.EnviarEmailComprovantePagamentoDeClubeDeAssinatura(new EmailComprovantePagamentoDeClubeDeAssinaturaDTO
            {
                NomeComprador = dto.Comprador.Nome,
                EmailComprador = dto.Comprador.Email,
                Estabelecimento = estabelecimento,
                FormaPagamento = GerarTextoDeFormaDePagamento(dto.LinkDePagamento),
                NomeAssinatura = assinatura.Nome,
                ValorAssinatura = assinatura.ValorAPagar,
                BeneficiosDaAssinatura = new List<BeneficioDaAssinatura>(beneficiosDaAssinatura),
                DataHora = dto.DataPagamento,
                TextoTituloPagamentoAprovado = "Primeiro pagamento",
                TextoCorpoPagamentoAprovado = $"O primeiro pagamento da assinatura {assinatura.Nome} foi aprovado e você já pode aproveitar todos os benefícios.",
            });
        }

        public string GerarTextoDeFormaDePagamento(LinkDePagamento link)
        {
            var metodosAceitos = ObterMetodosDePagamentoAceitos(link);

            return string.Join(", ", metodosAceitos.ConvertAll(MetodoDePagamentoToString));
        }

        public string MetodoDePagamentoToString(MetodoDePagamentoNoGatewayEnum metodo)
            => metodo == MetodoDePagamentoNoGatewayEnum.CartaoDeCredito ? "Cartão de crédito - pagamento recorrente" : metodo.ObterDescricao();
    }
}