﻿using NHibernate.Linq;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using Perlink.Trinks.Pessoas.DTO;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public class VendaPacoteHotsiteStrategy : ILinkDePagamentoStrategy
    {
        public async Task RealizarTransacao(int idLinkDePagamento, int? idPagamento)
        {
            await Domain.LinksDePagamentoNoTrinks.TransacaoVendaHotsiteStory.Registrar(idLinkDePagamento, idPagamento.Value);
        }

        public DadosDoLinkParaPagamentoDTO ObterDadosDeUmLink(LinkDePagamento link)
        {
            var tipoFormaDePagamento = Domain.LinksDePagamentoNoTrinks.LinkDePagamentoNoTrinksService.ObterOrigemPorIdLinkDoPagamento(link.IdLinkDePagamento);
            var aceitaParcelamento = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.EstabelecimentoAceitaParcelamento(link.IdRecebedor, (int)FormaPagamentoEnum.PagarmeCredito);

            var dto = new DadosDoLinkParaPagamentoDTO(link, ObterMetodosDePagamentoAceitos(link))
            {
                AceitaParcelamento = aceitaParcelamento,
                TipoDeFormaDePagamento = GerarTextoDeFormaDePagamento(link),
                MensagemDeEnvioParaWhatsApp = "Realize o pagamento acessando o link:",
                Itens = link.Itens.Select(i => new ItensDoLinkDTO { Nome = i.Nome, Valor = i.Valor }).ToList(),
            };

            if (aceitaParcelamento)
            {
                var parcelas = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.ObterListaDeParcelas(link.IdRecebedor, (int)FormaPagamentoEnum.PagarmeCredito);
                dto.Parcelas = ObterParcelas(parcelas, link.Valor);
            }

            return dto;
        }

        public List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoAceitos(LinkDePagamento linkDePagamento)
            => Domain.LinksDePagamento.LinkDePagamentoService
                .ListarTodosMetodosDePagamentoParaEstabelecimento(linkDePagamento.IdRecebedor);

        public void EnviarEmailComprovantePagamento(DadosParaEnvioEmailPagamentoDTO dto)
        {
            var tipoFormaPagamento = GerarTextoDeFormaDePagamento(dto.LinkDePagamento);

            var idPacote = Domain.Pacotes.LinkDePagamentoDoPacoteRepository.ObterIdPacoteClientePeloIdDoLink(dto.IdLinkPagamento);
            var nomePacote = Domain.Pacotes.PacoteRepository.ObterNomePacotePorIdPacote(idPacote);

            var valorPagoParcelado = ObterParcela(dto.Valor, dto.NumeroParcela);

            var itensPacote = Domain.Pacotes.PacoteRepository.ObterItensDoPacote(idPacote);


            var listaItensPacoteString = new List<string>();

            itensPacote.ForEach(itemPacote => listaItensPacoteString.Add($"{itemPacote.Quantidade}x {itemPacote.Nome}"));

            EnviarEmailComprovanteParaComprador(dto, tipoFormaPagamento, nomePacote, valorPagoParcelado, listaItensPacoteString);

            EnviarEmailNoficacoVendaParaRecebedor(dto, tipoFormaPagamento, nomePacote, valorPagoParcelado, listaItensPacoteString);
        }

        private void EnviarEmailComprovanteParaComprador(DadosParaEnvioEmailPagamentoDTO dto, string tipoFormaPagamento, string nomePacote, ParcelasDTO valorPagoParcelado, List<string> listaItensPacoteString)
        {
            var dadosComprovante = new EmailComprovantePagamentoVendaPacoteHotsiteCompradorDTO()
            {
                NomeComprador = dto.Comprador.Nome,
                EmailComprador = dto.Comprador.Email,
                IdEstabelecimento = dto.Recebedor.IdEstabelecimento,
                NomeEstabelecimento = dto.Recebedor.NomeEstabelecimento,
                NomePacote = nomePacote,
                DataHora = dto.DataPagamento,
                FormaPagamento = tipoFormaPagamento,
                ValorTotalPacote = dto.Valor,
                TextoValorCompraPacote = valorPagoParcelado.TextoParcela,
                ItensPacote = listaItensPacoteString,
            };

            Domain.LinksDePagamento.EnvioEmailService.EnviarEmailComprovantePagamentoVendaPacoteHotsiteComprador(dadosComprovante);
        }

        private void EnviarEmailNoficacoVendaParaRecebedor(DadosParaEnvioEmailPagamentoDTO dto, string tipoFormaPagamento, string nomePacote, ParcelasDTO valorPagoParcelado, List<string> listaItensPacoteString)
        {
            var dadosComprovante = new EmailComprovantePagamentoVendaPacoteHotsiteRecebedorDTO()
            {
                NomeRecebedor = dto.Recebedor.NomeEstabelecimento,
                EmailResponsavel = dto.Recebedor.EmailResponsavelFinanceiro,
                NomeResponsavel = dto.Recebedor.NomeResponsavelFinanceiro,
                IdEstabelecimento = dto.Recebedor.IdEstabelecimento,
                NomeComprador = dto.Comprador.Nome,
                NomePacote = nomePacote,
                DataHora = dto.DataPagamento,
                FormaPagamento = tipoFormaPagamento,
                ValorTotalPacote = dto.Valor,
                TextoValorCompraPacote = valorPagoParcelado.TextoParcela,
                ItensPacote = listaItensPacoteString,
            };

            Domain.LinksDePagamento.EnvioEmailService.EnviarEmailNoficacoVendaPacoteHotsiteParaRecebedor(dadosComprovante);
        }

        private List<ParcelasDTO> ObterParcelas(List<ParcelaFormaPagamentoDTO> parcelas, decimal valor)
            => parcelas.Select(parcela => ObterParcela(valor, parcela.NumeroParcela)).ToList();

        private ParcelasDTO ObterParcela(decimal valor, int numeroParcela)
        {
            var valorParcela = decimal.Round(valor / numeroParcela, 2);

            return new ParcelasDTO
            {
                NumeroParcela = numeroParcela,
                TextoParcela = numeroParcela == 1 ? $"À vista, por R$ {valorParcela}" : $"{numeroParcela}x de R$ {valorParcela}"
            };
        }

        public string GerarTextoDeFormaDePagamento(LinkDePagamento link)
        {
            var metodosAceitos = ObterMetodosDePagamentoAceitos(link);
            return string.Join(", ", metodosAceitos.ConvertAll(m => m.ObterDescricao()));
        }
    }
}
