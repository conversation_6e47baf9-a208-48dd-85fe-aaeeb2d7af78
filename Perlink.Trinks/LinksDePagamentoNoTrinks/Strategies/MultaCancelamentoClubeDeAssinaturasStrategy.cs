﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.LinksDePagamento;
using Perlink.Trinks.LinksDePagamento.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.DTOs;
using Perlink.Trinks.LinksDePagamentoNoTrinks.Enums;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.LinksDePagamentoNoTrinks.Strategies
{
    public class MultaCancelamentoClubeDeAssinaturasStrategy : ILinkDePagamentoStrategy
    {
        public async Task RealizarTransacao(int idLinkDePagamento, int? idPagamento)
        {
            await Domain.LinksDePagamentoNoTrinks.MultaClubeDeAssinaturasStory.Registrar(idLinkDePagamento, idPagamento.Value);
        }

        public DadosDoLinkParaPagamentoDTO ObterDadosDeUmLink(LinkDePagamento link)
        {
            if (link == null)
                return null;

            var idAssinatura = Domain.ClubeDeAssinaturas.LinkDePagamentoDoCancelamentoDaAssinaturaRepository.ObterIdDaAssinaturaPeloLinkDePagamento(link.IdLinkDePagamento);
            var assinatura = Domain.ClubeDeAssinaturas.AssinaturaDoClienteRepository.ObterPorIdAssinatura(idAssinatura);

            var dto = new DadosDoLinkParaPagamentoDTO(link, ObterMetodosDePagamentoAceitos(link))
            {
                TipoDeFormaDePagamento = GerarTextoDeFormaDePagamento(link),
                MensagemDeEnvioParaWhatsApp = "Pague a multa de cancelamento clicando no link abaixo:",
                Itens = link.Itens.Select(i => new ItensDoLinkDTO { Nome = "Multa de Cancelamento - " + i.Nome, Valor = i.Valor }).ToList(),
                NomeDaCompra = "Multa de Cancelamento - " + assinatura.Nome,
                AceitaParcelamento = false,
                SubItens = new List<SubItensDoLinkDTO>(),
            };

            return dto;
        }

        //TODO: Esse fluxo do envio de comprovante será realizado na próxima sprint
        public void EnviarEmailComprovantePagamento(DadosParaEnvioEmailPagamentoDTO dto)
        {
            return;
        }

        public List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoAceitos(LinkDePagamento linkDePagamento)
        {
            // No momento, o Clube de Assinaturas só poderá ser pago com cartão de crédito
            // Como todos os gateways atuais (Zoop, PagarMe e PagarMeV5) aceitam cartão de crédito,
            // não é necessário verificar qual gateway está sendo utilizado pelo recebedor
            return new List<MetodoDePagamentoNoGatewayEnum> { MetodoDePagamentoNoGatewayEnum.CartaoDeCredito };
        }

        public string GerarTextoDeFormaDePagamento(LinkDePagamento link)
        {
            var metodosAceitos = ObterMetodosDePagamentoAceitos(link);
            return string.Join(", ", metodosAceitos.Select(m => m.ObterDescricao()));
        }
    }
}