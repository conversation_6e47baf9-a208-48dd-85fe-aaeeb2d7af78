﻿
using Castle.ActiveRecord;
using NHibernate.Criterion;
using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.Pessoas;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories
{
    public partial class FranquiaComChaveDeIntegracaoRepository : IFranquiaComChaveDeIntegracaoRepository
    {
        public string ObterChaveDeIntegracaoComOutrosSistemas(int idFranquia)
        {
            return Load(idFranquia, false);
        }

        public bool PermiteEnvioDadosAgendamento(int idFranquia)
        {
            var franquiaComChaveDeIntegracao = Load(idFranquia, false);
            return franquiaComChaveDeIntegracao != null && franquiaComChaveDeIntegracao.PermiteEnvioDadosAgendamento;
        }

        public ListagemFranquiaWebHookDto ListarFranquiasComChaveDeIntegracao(FiltroFranquiaWebHookDto filtro)
        {
            var query = DetachedCriteria.For<FranquiaComChaveDeIntegracao>();

            // Extrair propriedades do objeto de filtro tipado
            int? idFranquia = filtro.IdFranquia;
            bool? permiteEnvioDadosAgendamento = filtro.PermiteEnvioDadosAgendamento;
            bool aplicarPaginacao = filtro.AplicarPaginacao;
            int paginaAtual = filtro.PaginaAtual;
            int registrosPorPagina = filtro.RegistrosPorPagina;

            if (idFranquia.HasValue)
            {
                query.Add(Restrictions.Eq("IdFranquia", idFranquia.Value));
            }

            if (permiteEnvioDadosAgendamento.HasValue)
            {
                query.Add(Restrictions.Eq("PermiteEnvioDadosAgendamento", permiteEnvioDadosAgendamento.Value));
            }

            var totalRegistros = ActiveRecordMediator<FranquiaComChaveDeIntegracao>.Count(query);
            var resultado = new ListagemFranquiaWebHookDto
            {
                Registros = new List<FranquiaWebHookItemDto>(),
                Paginacao = new PaginacaoDto
                {
                    TotalDeRegistros = totalRegistros,
                    PaginaAtual = paginaAtual,
                    RegistrosPorPagina = registrosPorPagina,
                    TotalDePaginas = aplicarPaginacao ? (int)System.Math.Ceiling((double)totalRegistros / registrosPorPagina) : 1
                }
            };

            if (totalRegistros > 0)
            {
                if (aplicarPaginacao)
                {
                    query.SetFirstResult((paginaAtual - 1) * registrosPorPagina)
                         .SetMaxResults(registrosPorPagina);
                }

                var franquias = ActiveRecordMediator<FranquiaComChaveDeIntegracao>.FindAll(query);

                var idsFranquias = franquias.Select(f => f.IdFranquia).ToList();
                var franquiasInfo = Domain.Pessoas.FranquiaRepository.ObterFranquiasPorIds(idsFranquias);

                resultado.Registros = franquias.Select(f => new FranquiaWebHookItemDto
                {
                    IdFranquia = f.IdFranquia,
                    NomeFranquia = franquiasInfo.FirstOrDefault(fi => fi.Id == f.IdFranquia)?.Nome ?? "Não encontrada",
                    ChaveIntegracaoOutrosSistemas = f.ChaveIntegracaoOutrosSistemas,
                    PermiteEnvioDadosAgendamento = f.PermiteEnvioDadosAgendamento
                }).ToList();
            }

            return resultado;
        }

        public FranquiaComChaveDeIntegracao SalvarFranquiaComChaveDeIntegracao(FranquiaComChaveDeIntegracao franquiaWebHook)
        {
            var existente = Load(franquiaWebHook.IdFranquia, false);

            if (existente != null)
            {
                existente.ChaveIntegracaoOutrosSistemas = franquiaWebHook.ChaveIntegracaoOutrosSistemas;
                existente.PermiteEnvioDadosAgendamento = franquiaWebHook.PermiteEnvioDadosAgendamento;
                existente.IdFranquia = franquiaWebHook.IdFranquia;
                Update(existente);
                return existente;
            }
            else
            {
                ActiveRecordMediator<FranquiaComChaveDeIntegracao>.Create(franquiaWebHook);
                return franquiaWebHook;
            }
        }

        private static FranquiaComChaveDeIntegracao Load(int idFranquia, bool throwOnNotFound)
        {
            return ActiveRecordMediator<FranquiaComChaveDeIntegracao>.FindByPrimaryKey(idFranquia, throwOnNotFound);
        }
    }
}
