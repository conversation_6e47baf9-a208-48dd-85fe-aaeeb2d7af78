﻿using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories
{
    public partial interface IFranquiaComChaveDeIntegracaoRepository
    {
        string ObterChaveDeIntegracaoComOutrosSistemas(int idFranquia);
        bool PermiteEnvioDadosAgendamento(int idFranquia);
        ListagemFranquiaWebHookDto ListarFranquiasComChaveDeIntegracao(FiltroFranquiaWebHookDto filtro);
        FranquiaComChaveDeIntegracao SalvarFranquiaComChaveDeIntegracao(FranquiaComChaveDeIntegracao franquiaWebHook);
    }
}
