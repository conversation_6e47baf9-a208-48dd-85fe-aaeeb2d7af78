﻿﻿using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using System.Collections.Generic;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories
{
    public partial interface IFranquiaEstabelecimentoComChaveDeIntegracaoRepository
    {
        string ObterChaveDeIntegracaoComOutrosSistemas(int idEstabelecimento);
        bool PermiteEnvioDadosAgendamento(int idEstabelecimento);
        ListagemEstabelecimentoWebHookDto ListarEstabelecimentosComChaveDeIntegracao(FiltroEstabelecimentoWebHookDto filtro);
        FranquiaEstabelecimentoComChaveDeIntegracao SalvarEstabelecimentoComChaveDeIntegracao(FranquiaEstabelecimentoComChaveDeIntegracao estabelecimentoWebHook);
    }
}
