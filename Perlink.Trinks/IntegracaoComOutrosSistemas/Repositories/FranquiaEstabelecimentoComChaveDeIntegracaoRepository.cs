﻿
using Castle.ActiveRecord;
using NHibernate.Criterion;
using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.Pessoas;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas.Repositories
{
    public partial class FranquiaEstabelecimentoComChaveDeIntegracaoRepository : IFranquiaEstabelecimentoComChaveDeIntegracaoRepository
    {
        public string ObterChaveDeIntegracaoComOutrosSistemas(int idEstabelecimento)
        {
            return Load(idEstabelecimento, false);
        }

        public bool PermiteEnvioDadosAgendamento(int idEstabelecimento)
        {
            var estabelecimentoChaveIntegracao = Load(idEstabelecimento, false);
            return estabelecimentoChaveIntegracao != null && estabelecimentoChaveIntegracao.PermiteEnvioDadosAgendamento;
        }

        public ListagemEstabelecimentoWebHookDto ListarEstabelecimentosComChaveDeIntegracao(FiltroEstabelecimentoWebHookDto filtro)
        {
            var query = DetachedCriteria.For<FranquiaEstabelecimentoComChaveDeIntegracao>();

            // Extrair propriedades do objeto de filtro tipado
            int? idEstabelecimento = filtro.IdEstabelecimento;
            int? idFranquia = filtro.IdFranquia;
            bool? permiteEnvioDadosAgendamento = filtro.PermiteEnvioDadosAgendamento;
            bool aplicarPaginacao = filtro.AplicarPaginacao;
            int paginaAtual = filtro.PaginaAtual;
            int registrosPorPagina = filtro.RegistrosPorPagina;

            if (idEstabelecimento.HasValue)
            {
                query.Add(Restrictions.Eq("IdEstabelecimento", idEstabelecimento.Value));
            }

            if (permiteEnvioDadosAgendamento.HasValue)
            {
                query.Add(Restrictions.Eq("PermiteEnvioDadosAgendamento", permiteEnvioDadosAgendamento.Value));
            }

            var totalRegistros = ActiveRecordMediator<FranquiaEstabelecimentoComChaveDeIntegracao>.Count(query);
            var resultado = new ListagemEstabelecimentoWebHookDto
            {
                Registros = new List<EstabelecimentoWebHookItemDto>(),
                Paginacao = new PaginacaoDto
                {
                    TotalDeRegistros = totalRegistros,
                    PaginaAtual = paginaAtual,
                    RegistrosPorPagina = registrosPorPagina,
                    TotalDePaginas = aplicarPaginacao ? (int)System.Math.Ceiling((double)totalRegistros / registrosPorPagina) : 1
                }
            };

            if (totalRegistros > 0)
            {
                if (aplicarPaginacao)
                {
                    query.SetFirstResult((paginaAtual - 1) * registrosPorPagina)
                         .SetMaxResults(registrosPorPagina);
                }

                var estabelecimentos = ActiveRecordMediator<FranquiaEstabelecimentoComChaveDeIntegracao>.FindAll(query);

                var idsEstabelecimentos = estabelecimentos.Select(e => e.IdEstabelecimento).ToList();
                var estabelecimentosInfo = Domain.Pessoas.EstabelecimentoRepository.ObterEstabelecimentosPorIds(idsEstabelecimentos);

                // Obter informações das franquias
                var franquiasEstabelecimentos = Domain.Pessoas.FranquiaEstabelecimentoRepository.ObterFranquiasEstabelecimentosPorIdsEstabelecimentos(idsEstabelecimentos);
                var idsFranquias = franquiasEstabelecimentos.Select(fe => fe.Franquia.Id).Distinct().ToList();
                var franquiasInfo = Domain.Pessoas.FranquiaRepository.ObterFranquiasPorIds(idsFranquias);

                resultado.Registros = estabelecimentos.Select(e =>
                {
                    var estabelecimento = estabelecimentosInfo.FirstOrDefault(ei => ei.IdEstabelecimento == e.IdEstabelecimento);
                    var franquiaEstabelecimento = franquiasEstabelecimentos.FirstOrDefault(fe => fe.IdEstabelecimento == e.IdEstabelecimento);
                    var franquia = franquiaEstabelecimento != null ? franquiasInfo.FirstOrDefault(f => f.Id == franquiaEstabelecimento.Franquia.Id) : null;

                    return new EstabelecimentoWebHookItemDto
                    {
                        IdEstabelecimento = e.IdEstabelecimento,
                        NomeEstabelecimento = estabelecimento?.NomeDeExibicaoNoPortal ?? "Não encontrado",
                        IdFranquia = franquia?.Id,
                        NomeFranquia = franquia?.Nome ?? "Não encontrada",
                        ChaveIntegracaoOutrosSistemas = e.ChaveIntegracaoOutrosSistemas,
                        PermiteEnvioDadosAgendamento = e.PermiteEnvioDadosAgendamento
                    };
                }).ToList();
            }

            return resultado;
        }

        public FranquiaEstabelecimentoComChaveDeIntegracao SalvarEstabelecimentoComChaveDeIntegracao(FranquiaEstabelecimentoComChaveDeIntegracao estabelecimentoWebHook)
        {
            var existente = Load(estabelecimentoWebHook.IdEstabelecimento, false);

            if (existente != null)
            {
                existente.ChaveIntegracaoOutrosSistemas = estabelecimentoWebHook.ChaveIntegracaoOutrosSistemas;
                existente.PermiteEnvioDadosAgendamento = estabelecimentoWebHook.PermiteEnvioDadosAgendamento;
                ActiveRecordMediator<FranquiaEstabelecimentoComChaveDeIntegracao>.Update(existente);
                return existente;
            }
            else
            {
                ActiveRecordMediator<FranquiaEstabelecimentoComChaveDeIntegracao>.Create(estabelecimentoWebHook);
                return estabelecimentoWebHook;
            }
        }

        private static FranquiaEstabelecimentoComChaveDeIntegracao Load(int idEstabelecimento, bool throwOnNotFound)
        {
            return ActiveRecordMediator<FranquiaEstabelecimentoComChaveDeIntegracao>.FindByPrimaryKey(idEstabelecimento, throwOnNotFound);
        }
    }
}
