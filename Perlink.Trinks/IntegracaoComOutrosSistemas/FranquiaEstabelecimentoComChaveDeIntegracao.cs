﻿using Castle.ActiveRecord;
using System;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas
{
    [ActiveRecord("Franquia_Estabelecimento_Chave_Integracao")]
    [Serializable]
    public partial class FranquiaEstabelecimentoComChaveDeIntegracao
    {

        public static implicit operator string(FranquiaEstabelecimentoComChaveDeIntegracao franquiaEstabelecimentoComChaveDeIntegracao)
        {
            return franquiaEstabelecimentoComChaveDeIntegracao == null ? null : franquiaEstabelecimentoComChaveDeIntegracao.ChaveIntegracaoOutrosSistemas;
        }

        [PrimaryKey(PrimaryKeyType.Assigned, "id_estabelecimento", ColumnType = "Int32")]
        public virtual int IdEstabelecimento { get; set; }

        [Property("chave_integracao_outros_sistemas", ColumnType = "String")]
        public virtual string ChaveIntegracaoOutrosSistemas { get; set; }

        [Property("permite_envio_dados_agendamento")]
        public virtual bool PermiteEnvioDadosAgendamento { get; set; }
    }
}
