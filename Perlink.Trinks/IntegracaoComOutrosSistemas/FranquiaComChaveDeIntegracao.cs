﻿using Castle.ActiveRecord;
using System;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas
{
    [ActiveRecord("Franquia_Chave_Integracao")]
    [Serializable]
    public partial class FranquiaComChaveDeIntegracao
    {

        public static implicit operator string(FranquiaComChaveDeIntegracao franquiaComChaveDeIntegracao)
        {
            return franquiaComChaveDeIntegracao == null ? null : franquiaComChaveDeIntegracao.ChaveIntegracaoOutrosSistemas;
        }

        [PrimaryKey(PrimaryKeyType.Assigned, "id_franquia", ColumnType = "Int32")]
        public virtual int IdFranquia { get; set; }

        [Property("chave_integracao_outros_sistemas", ColumnType = "String")]
        public virtual string ChaveIntegracaoOutrosSistemas { get; set; }

        [Property("permite_envio_dados_agendamento")]
        public virtual bool PermiteEnvioDadosAgendamento { get; set; }
    }
}
