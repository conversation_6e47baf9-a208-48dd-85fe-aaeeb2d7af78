﻿using Perlink.Trinks.IntegracaoComOutrosSistemas.DTO;
using Perlink.Trinks.IntegracaoComOutrosSistemas.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Web.Script.Serialization;

namespace Perlink.Trinks.IntegracaoComOutrosSistemas
{

    public class EventoDeFecharConta : IEventoIntegracaoComOutrosSistemas
    {
        public TipoDeEventoEnum Tipo { get; set; }

        public DadosEventoDeFecharConta Dados { get; set; }

        private string MontarJsonDosDados()
        {
            return new JavaScriptSerializer().Serialize(Dados);
        }

        public string ObterJsonDosDados()
        {
            return MontarJsonDosDados();
        }

        public TipoDeEventoEnum ObterTipo()
        {
            return Tipo;
        }
    }

    public class DadosEventoDeFecharConta
    {

        public DadosEventoDeFecharConta()
        {
            Itens = new List<DadosEventoDeFecharContaItem>();
        }

        public int IdDoEstabelecimento { get; set; }
        public int IdDoCliente { get; set; }
        public int IdDoClienteNoEstabelecimento { get; set; }
        public int IdPessoaDoProfissional { get; set; }
        public int IdDoProfissional { get; set; }
        public int IdDoProfissionalNoEstabelecimento { get; set; }
        public int IdDaTransacao { get; set; }
        public List<EstabelecimentoProfissionalDTO> IdsDosProfissionaisEnvolvidos { get; set; }
        public String ValorDaCompra { get; set; }
        public List<FormaPagamentoItem> FormasPagamento { get; set; }
        public String DataDoFechamento { get; set; }
        public int IdDoProfissionalQueFechouConta { get; set; }
        public int IdDoProfissionalNoEstabelecimentoQueFechouConta { get; set; }
        public string NomeDoProfissionalQueFechouConta { get; set; }
        public String NomeDoCliente { get; set; }
        public String CPFDoCliente { get; set; }
        public String EmailDoCliente { get; set; }
        public IList<Telefone> TelefoneDoCliente { get; set; }
        public String DataDeNascimentoDoCliente { get; set; }
        public String SexoDoCliente { get; set; }
        public Endereco EnderecoDoCliente { get; set; }
        public String DataDeInclusaoDoCliente { get; set; }
        public TipoDeAcaoEnum Action { get; set; }
        public TipoDeEventoEnum TipoDeEvento { get; set; }

        public List<DadosEventoDeFecharContaItem> Itens { get; set; }
    }

    public class FormaPagamentoItem
    {
        public string FormaPagamento { get; set; }
        public decimal Valor { get; set; }
    }

    public class DadosEventoDeFecharContaItem
    {

        public DadosEventoDeFecharContaItem()
        {
            Quantidade = 1;
        }

        public DadosEventoDeFecharContaItem(HorarioTransacao ht) : this()
        {
            var se = ht.Horario.ServicoEstabelecimento;
            Id = se.IdServicoEstabelecimento;
            Nome = se.Nome;
            ValorUnitario = ht.Preco ?? 0;
            DescontoTotal = ht.Desconto ?? 0;
            MotivoDesconto = ht.MotivoDesconto?.Descricao;
            Total = ht.SubTotal() ?? 0;
            TipoItem = TipoDoItemEnum.Servico;
            IdDoAgendamento = ht.Horario.Id;
        }

        public DadosEventoDeFecharContaItem(ItemVenda iv) : this()
        {
            Quantidade = iv.Quantidade;
            DescontoTotal = iv.Desconto;
            MotivoDesconto = iv.MotivoDesconto?.Descricao;
            Total = iv.ValorFinal();
            ValorUnitario = iv.ValorUnitario;

            if (iv is ItemVendaProduto)
            {
                var ivp = iv as ItemVendaProduto;
                Nome = ivp.EstabelecimentoProduto.Descricao;
                EAN = ivp.EstabelecimentoProduto.CodigoBarras;
                Id = ivp.EstabelecimentoProduto.Id;
                TipoItem = TipoDoItemEnum.Produto;
            }
            else if (iv is ItemVendaPacote)
            {
                var ivp = iv as ItemVendaPacote;
                Nome = ivp.PacoteCliente.Nome;
                Id = ivp.PacoteCliente.PacoteOriginal.Id;
                TipoItem = TipoDoItemEnum.Pacote;
            }
            else if (iv is ItemVendaValePresente)
            {
                var ivp = iv as ItemVendaValePresente;
                Nome = "Vale Presente #" + ivp.ValePresente.Numero;
                TipoItem = TipoDoItemEnum.ValePresente;
            }
            else
            {
                Nome = iv.ToString();
                TipoItem = TipoDoItemEnum.Outros;
            }
        }

        public int Id { get; set; }
        public string Nome { get; set; }
        public int Quantidade { get; set; }
        public decimal ValorUnitario { get; set; }
        public decimal DescontoTotal { get; set; }
        public string MotivoDesconto { get; set; }
        public decimal Total { get; set; }
        public string EAN { get; set; }
        public TipoDoItemEnum TipoItem { get; set; }
        public int IdDoAgendamento { get; set; }
    }
}