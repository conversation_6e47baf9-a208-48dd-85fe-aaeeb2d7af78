﻿﻿namespace Perlink.Trinks.IntegracaoComOutrosSistemas.DTO
{
    /// <summary>
    /// DTO para filtro de estabelecimentos com chave de integração
    /// </summary>
    public class FiltroEstabelecimentoWebHookDto
    {
        /// <summary>
        /// ID do estabelecimento para filtrar
        /// </summary>
        public int? IdEstabelecimento { get; set; }

        /// <summary>
        /// ID da franquia para filtrar
        /// </summary>
        public int? IdFranquia { get; set; }

        /// <summary>
        /// Filtro para estabelecimentos que permitem ou não o envio de dados de agendamento
        /// </summary>
        public bool? PermiteEnvioDadosAgendamento { get; set; }

        /// <summary>
        /// Página atual para paginação
        /// </summary>
        public int PaginaAtual { get; set; } = 1;

        /// <summary>
        /// Quantidade de registros por página
        /// </summary>
        public int RegistrosPorPagina { get; set; } = 10;

        /// <summary>
        /// Indica se deve aplicar paginação
        /// </summary>
        public bool AplicarPaginacao { get; set; } = true;
    }
}
