﻿﻿namespace Perlink.Trinks.IntegracaoComOutrosSistemas.DTO
{
    /// <summary>
    /// DTO para item de estabelecimento com chave de integração
    /// </summary>
    public class EstabelecimentoWebHookItemDto
    {
        /// <summary>
        /// ID do estabelecimento
        /// </summary>
        public int IdEstabelecimento { get; set; }

        /// <summary>
        /// Nome do estabelecimento
        /// </summary>
        public string NomeEstabelecimento { get; set; }

        /// <summary>
        /// ID da franquia
        /// </summary>
        public int? IdFranquia { get; set; }

        /// <summary>
        /// Nome da franquia
        /// </summary>
        public string NomeFranquia { get; set; }

        /// <summary>
        /// Chave de integração com outros sistemas
        /// </summary>
        public string ChaveIntegracaoOutrosSistemas { get; set; }

        /// <summary>
        /// Indica se permite envio de dados de agendamento
        /// </summary>
        public bool PermiteEnvioDadosAgendamento { get; set; }
    }
}
