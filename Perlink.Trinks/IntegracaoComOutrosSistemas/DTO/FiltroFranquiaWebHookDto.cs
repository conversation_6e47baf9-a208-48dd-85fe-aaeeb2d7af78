﻿﻿namespace Perlink.Trinks.IntegracaoComOutrosSistemas.DTO
{
    /// <summary>
    /// DTO para filtro de franquias com chave de integração
    /// </summary>
    public class FiltroFranquiaWebHookDto
    {
        /// <summary>
        /// ID da franquia para filtrar
        /// </summary>
        public int? IdFranquia { get; set; }

        /// <summary>
        /// Filtro para franquias que permitem ou não o envio de dados de agendamento
        /// </summary>
        public bool? PermiteEnvioDadosAgendamento { get; set; }

        /// <summary>
        /// Página atual para paginação
        /// </summary>
        public int PaginaAtual { get; set; } = 1;

        /// <summary>
        /// Quantidade de registros por página
        /// </summary>
        public int RegistrosPorPagina { get; set; } = 10;

        /// <summary>
        /// Indica se deve aplicar paginação
        /// </summary>
        public bool AplicarPaginacao { get; set; } = true;
    }
}
