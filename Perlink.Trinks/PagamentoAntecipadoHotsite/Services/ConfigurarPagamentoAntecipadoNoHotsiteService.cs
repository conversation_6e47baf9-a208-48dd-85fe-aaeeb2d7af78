﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.ControleDeFuncionalidades.Services;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.Repositories;
using Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories;
using Perlink.Trinks.PagamentosOnlineNoTrinks.Repositories;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Services;
using System;

namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Services
{
    public class ConfigurarPagamentoAntecipadoNoHotsiteService : BaseService, IConfigurarPagamentoAntecipadoNoHotsiteService
    {
        #region ctor
        private readonly IEstabelecimentoRecebedorRepository _estabelecimentoRecebedorRepository;
        private readonly IPagamentoAntecipadoHotsiteConfiguracoesRepository _pagamentoAntecipadoHotsiteConfiguracoesRepository;
        private readonly IHotsiteEstabelecimentoService _hotsiteEstabelecimentoService;
        private readonly IEstabelecimentoFormaPagamentoService _estabelecimentoFormaPagamentoService;
        private readonly IDisponibilidadeDeRecursosService _disponibilidadeDeRecursosService;
        private readonly IFormaPagamentoRepository _formaPagamentoRepository;

        public ConfigurarPagamentoAntecipadoNoHotsiteService()
        {
            _estabelecimentoRecebedorRepository = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository;
            _pagamentoAntecipadoHotsiteConfiguracoesRepository = Domain.PagamentoAntecipadoHotsite.PagamentoAntecipadoHotsiteConfiguracoesRepository;
            _hotsiteEstabelecimentoService = Domain.Pessoas.HotsiteEstabelecimentoService;
            _estabelecimentoFormaPagamentoService = Domain.Pessoas.EstabelecimentoFormaPagamentoService;
            _disponibilidadeDeRecursosService = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService;
            _formaPagamentoRepository = Domain.Financeiro.FormaPagamentoRepository;
        }
        
        private static int IdFormaPagamentoPagarmeCredito => (int)FormaPagamentoEnum.PagarmeCredito;
        private static int IdFormaPagamentoPagarmePix => (int)FormaPagamentoEnum.PagarmePix;
        private static int IdFormaPagamentoCreditoDePagamentoOnlineHotsite => (int)FormaPagamentoPrePagoEnum.CreditoDePagamentoOnlineHotsite;
        #endregion
            
        public void ConfigurarPagamentoAntecipadoDeServicoNoHotsite(Estabelecimento estabelecimento)
        {
            if (!PagamentoServicosHotsiteDisponivel(estabelecimento))
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Estabelecimento não possui permissão para o pagamento antecipado de serviço no hotsite.");
                return;
            }
                
            var idRecebedor = _estabelecimentoRecebedorRepository.ObterIdRecebedorPorIdEstabelecimento(estabelecimento.IdEstabelecimento);
            
            var configuracao = _pagamentoAntecipadoHotsiteConfiguracoesRepository.ObterPorIdRecebedor(idRecebedor) ??
                               CriarConfiguracaoParaPagamentoAntecipadoDeServicoNoHotsite(idRecebedor);

            AtualizarConfiguracaoDePagamentoAntecipadoDeServicoNoHotsite(configuracao, estabelecimento);
        }

        public bool EstabelecimentoPossuiConfiguracaoPagamentoAntecipadoHotsite(Estabelecimento estabelecimento)
            => PagamentoServicosHotsiteDisponivel(estabelecimento) && ConfiguracaoAtiva(estabelecimento.IdEstabelecimento);

        private PagamentoAntecipadoHotsiteConfiguracoes CriarConfiguracaoParaPagamentoAntecipadoDeServicoNoHotsite(int idRecebedor)
        {
            var novaConfiguracao = new PagamentoAntecipadoHotsiteConfiguracoes(idRecebedor);
            _pagamentoAntecipadoHotsiteConfiguracoesRepository.SaveNew(novaConfiguracao);
            return novaConfiguracao;
        }
        
        private void AtualizarConfiguracaoDePagamentoAntecipadoDeServicoNoHotsite(PagamentoAntecipadoHotsiteConfiguracoes configuracao, Estabelecimento estabelecimento)
        {
            var hotsite = estabelecimento.Hotsite();
            
            if (!configuracao.HabilitaPagamentoAntecipadoServicoHotsite)
            {
                AtualizarFormaPagamento(estabelecimento, IdFormaPagamentoPagarmeCredito);
                AtualizarFormaPagamento(estabelecimento, IdFormaPagamentoPagarmePix);
                AtualizarFormaPagamento(estabelecimento, IdFormaPagamentoCreditoDePagamentoOnlineHotsite);
                AtualizarPermitirAgendamentoHotsiteSeNecessario(hotsite);
                AtualizarPermiteExibirPrecoHotsiteSeNecessario(hotsite);
            }
            
            configuracao.Atualizar();
            _pagamentoAntecipadoHotsiteConfiguracoesRepository.Update(configuracao);
        }

        private void AtualizarPermiteExibirPrecoHotsiteSeNecessario(HotsiteEstabelecimento hotsite)
        {
            if (!hotsite.PermiteExibicaoPrecoHotsite)
            {
                _hotsiteEstabelecimentoService.PermitirExibirPrecos();
                ValidationHelper.Instance.AdicionarItemNotificacao(
                    "A configuração de 'Exibição de Informações do Site - Exibir Preços' foi alterada para habilitar o pagamento antecipado no hotsite.");
            }
        }

        private void AtualizarPermitirAgendamentoHotsiteSeNecessario(HotsiteEstabelecimento hotsite)
        {
            if (!hotsite.PermiteAgendamentoHotsite)
            {
                _hotsiteEstabelecimentoService.AlteraOStatusDoAgendamentoOnlinePara(status: true);
                ValidationHelper.Instance.AdicionarItemNotificacao(
                    "A configuração de Agendamento Online foi alterada para habiltiar o pagamento antecipado no hotsite.");
            }
        }

        private void AtualizarFormaPagamento(Estabelecimento estabelecimento, int idFormaPagamento)
        {
            var formaPagamentoOnlineHotsite = _formaPagamentoRepository.ObterPorIdFormaPagamento(idFormaPagamento);
            _estabelecimentoFormaPagamentoService.AssociarFormaDePagamento(formaPagamentoOnlineHotsite, estabelecimento);
        }
        
        private bool PagamentoServicosHotsiteDisponivel(Estabelecimento estabelecimento)
            => _disponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.PagamentoAntecipadoDeServicosNoHotsite).EstaDisponivel;  

        private bool ConfiguracaoAtiva(int idEstabelecimento)
            => _pagamentoAntecipadoHotsiteConfiguracoesRepository.EstabelecimentoPossuiPagamentoAntecipadoAtivo(idEstabelecimento);
    }
}