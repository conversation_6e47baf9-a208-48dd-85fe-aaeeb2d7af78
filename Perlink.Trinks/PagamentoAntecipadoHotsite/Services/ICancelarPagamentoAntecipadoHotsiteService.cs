﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.PagamentoAntecipadoHotsite.Enums;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Services
{
    public interface ICancelarPagamentoAntecipadoHotsiteService : IService
    {
        void ValidarEstornoPagamentoAntecipadoHotsiteServico(int idTransacao);
        Task CancelarPagamentoAntecipadoHotsiteAsync(int idHorario, FormaDeReembolsoEnum formaDeReembolso, string motivo = null);
    }
}