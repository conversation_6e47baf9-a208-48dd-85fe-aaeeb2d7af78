using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.Financeiro.Adapters;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Financeiro.Repositories;
using Perlink.Trinks.Financeiro.Services;
using Perlink.Trinks.PagamentoAntecipadoHotsite.Enums;
using Perlink.Trinks.PagamentosOnlineNoTrinks.Services;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Repositories;
using Perlink.Trinks.PromocoesOnline.Repositories;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Vendas.Repositories;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Services
{
    public class CancelarPagamentoAntecipadoHotsiteService : BaseService, ICancelarPagamentoAntecipadoHotsiteService
    {
        #region ctor
        
        private readonly IConfigurarPagamentoAntecipadoNoHotsiteService _configurarPagamentoAntecipadoNoHotsiteService;
        private readonly ITransacaoRepository _transacaoRepository;
        private readonly ILinkDePagamentoDoHorarioRepository _linkDePagamentoDoHorarioRepository;
        private readonly IPagamentoOnlineNoTrinksService _pagamentoOnlineNoTrinksService;
        private readonly IEstabelecimentoRepository _estabelecimentoRepository;
        private readonly IPessoaFisicaRepository _pessoaFisicaRepository;
        private readonly IVendaRepository _vendaRepository;
        private readonly ITransacaoService _transacaoService;
        private readonly IHorariosDaPromocaoRepository _horariosDaPromocaoRepository;
        private readonly IPromocaoOnlineRepository _promocaoOnlineRepository;

        public CancelarPagamentoAntecipadoHotsiteService()
        {
            _configurarPagamentoAntecipadoNoHotsiteService = Domain.PagamentoAntecipadoHotsite.ConfigurarPagamentoAntecipadoNoHotsiteService;
            _transacaoRepository = Domain.Financeiro.TransacaoRepository;
            _linkDePagamentoDoHorarioRepository = Domain.Pessoas.LinkDePagamentoDoHorarioRepository;
            _pagamentoOnlineNoTrinksService = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService;
            _estabelecimentoRepository = Domain.Pessoas.EstabelecimentoRepository;
            _pessoaFisicaRepository = Domain.Pessoas.PessoaFisicaRepository;
            _vendaRepository = Domain.Vendas.VendaRepository;
            _transacaoService = Domain.Financeiro.TransacaoService;
            _horariosDaPromocaoRepository = Domain.PromocoesOnline.HorariosDaPromocaoRepository;
            _promocaoOnlineRepository = Domain.PromocoesOnline.PromocaoOnlineRepository;
        }
        
        #endregion

        public void ValidarEstornoPagamentoAntecipadoHotsiteServico(int idTransacao)
        {
            var transacao = _transacaoRepository.Load(idTransacao);
            var estabelecimento = _transacaoRepository.ObterEstabelecimentoDaTransacao(transacao);
            var configuracaoAtiva = _configurarPagamentoAntecipadoNoHotsiteService.EstabelecimentoPossuiConfiguracaoPagamentoAntecipadoHotsite(estabelecimento);

            if (!configuracaoAtiva) return;

            var transacaoPossuiHorarioComLink = _linkDePagamentoDoHorarioRepository.TransacaoPossuiHorarioComLinkDePagamento(idTransacao);

            if (!transacaoPossuiHorarioComLink) return;
            
            ValidationHelper.Instance.AdicionarItemValidacao("Não é possível realizar estorno de pagamento antecipado pelo hotsite");
        }

        public async Task CancelarPagamentoAntecipadoHotsiteAsync(int idHorario, FormaDeReembolsoEnum formaDeReembolso, string motivo = null)
        {
            try
            {
                ValidarHorario(idHorario);
                
                switch (formaDeReembolso)
                {
                    case FormaDeReembolsoEnum.Credito:
                        await GerarCreditoDeClienteDoPagamentoAntecipadoHotsiteAsync(idHorario);
                        break;
                    case FormaDeReembolsoEnum.Estorno:
                        await EstornarPagamentoAntecipadoHotsiteAsync(idHorario);
                        break;
                    case FormaDeReembolsoEnum.Nenhum:
                    default:
                        break;
                }

                CriarTransacaoDeEstorno(idHorario, motivo);
                DesativarLinkDoHorario(idHorario);
                DescontarVendaDaPromocaoSeNecessario(idHorario);
            }
            catch (Exception ex)
            {
                Elmah.ErrorLog.GetDefault(null).Log("[Link de Pagamento - Pagamento Antecipado Hotsite]", ex);
                ValidationHelper.Instance.AdicionarItemValidacao("Erro ao cancelar horário do pagamento antecipado.");
            }
        }

        private async Task EstornarPagamentoAntecipadoHotsiteAsync(int idHorario)
        {
            var pagamentoOnline = _linkDePagamentoDoHorarioRepository.ObterPagamentosOnlineNoTrinksPorIdsHorario(new[] { idHorario }).FirstOrDefault();
            var estornadoComSucesso = await _pagamentoOnlineNoTrinksService.EstornarPagamentoOnline(pagamentoOnline);
            
            if (!estornadoComSucesso) 
                throw new BusinessException("Erro ao realizar estorno do pagamento online");
        }

        [TransactionInitRequired]
        private async Task GerarCreditoDeClienteDoPagamentoAntecipadoHotsiteAsync(int idHorario)
        {
            var linkDePagamentoTrinks = _linkDePagamentoDoHorarioRepository.ObterLinkDePagamentoTrinksPorIdHorario(idHorario);
            var metodo = Domain.Pagamentos.PagamentoRepository.ObterMetodoPorIdPagamentos(linkDePagamentoTrinks.IdLinkDePagamento);
            var totalPago = linkDePagamentoTrinks.PagamentoOnlineNoTrinks.ValorTotal;
            
            var estabelecimento = _estabelecimentoRepository.ObterPorId(linkDePagamentoTrinks.ClienteEstabelecimento.Estabelecimento.IdEstabelecimento);
            var pessoaDoClienteQuePagou = _pessoaFisicaRepository.ObterPorId(linkDePagamentoTrinks.ClienteEstabelecimento.Cliente.PessoaFisica.IdPessoa);
            Venda venda = _vendaRepository.Factory.CreateParaRealizarTransacao(
                estabelecimento: estabelecimento,
                pessoaQuePagou: pessoaDoClienteQuePagou,
                dataHoraDaTransacao: Calendario.Agora(),
                dataReferencia: Calendario.Agora(),
                pessoaQueRealizou: pessoaDoClienteQuePagou,
                tipoTransacao: TipoTransacaoEnum.Pagamento,
                comentarioFechamentoConta: "",
                comentarioEstorno: "");
            
            venda.Transacao.FormasPagamento.Add(GerarFormaPagamentoParaCreditoPorLinkDePagamento(
                metodo ?? MetodoDePagamentoNoGatewayEnum.CartaoDeCredito, estabelecimento.IdEstabelecimento, totalPago, venda.Transacao));;
            
            _transacaoService
                .ColocarCompraDeCreditoNaTransacao(transacao: venda.Transacao,
                    formaPagamentoPrePago: FormaPagamentoPrePagoEnum.CreditoCliente,
                    valorDoCredito: totalPago);
            
            await _transacaoService.RealizarCheckOut(new CheckoutDTO()
            {
                Transacao = venda.Transacao,
                QuemMarcou = venda.Transacao.PessoaQuePagou,
                Venda = venda,
                EmitirNFC = false,
                IdTransacaoPOS = null,
                ControlarCaixaSeNecessario = false,
                ControlarPagamentoPOS = false
            });
        }

        private void DesativarLinkDoHorario(int idHorario)
        {
            LinkDePagamentoDoHorario linkDoHorario = _linkDePagamentoDoHorarioRepository.ObterAtivoPorIdHorario(idHorario);
            linkDoHorario.Desativar();
            Domain.LinksDePagamento.CancelamentoLinkPagamentoService.CancelarLinkPagamento(linkDoHorario.IdLinkDePagamento);
            _linkDePagamentoDoHorarioRepository.Update(linkDoHorario);
        }

        private void ValidarHorario(int idHorario)
        {
            var horarioJaTeveLinkPago = _linkDePagamentoDoHorarioRepository.IdHorarioPossuiLinkPagoSemProcessamento(idHorario);
            if (horarioJaTeveLinkPago)
                throw new ApplicationException("Pagamento do link já foi realizado. Aguardar o processamento do link para cancelar o agendamento da forma correta.");
        }

        private void CriarTransacaoDeEstorno(int idHorario, string motivo = null)
        {
            var pagamentoOnline = _linkDePagamentoDoHorarioRepository.ObterLinkDePagamentoTrinksPorIdHorario(idHorario);
            
            _transacaoService.RealizarEstorno(
                pagamentoOnline.PagamentoOnlineNoTrinks.Transacao.Id,
                Calendario.Agora(),
                motivo,
                pagamentoOnline.PagamentoOnlineNoTrinks.Transacao.PessoaQueRealizou,
                idTransacaoPOS: null);
        }

        private void DescontarVendaDaPromocaoSeNecessario(int idHorario)
        {
            var promocaoOnline = _horariosDaPromocaoRepository.ObterPromocaoPorIdHorario(idHorario);

            if (promocaoOnline is null) return;
            
            promocaoOnline.DescontarVendaCancelada();
            _promocaoOnlineRepository.Update(promocaoOnline);
        }
        
        private TransacaoFormaPagamento GerarFormaPagamentoParaCreditoPorLinkDePagamento(MetodoDePagamentoNoGatewayEnum metodo, int idEstabelecimento, decimal ValorTotal, Transacao transacao)
        {
            var formaPagamento = metodo.ToFormaPagamentoEnum();
            var transacaoFormaPagamentoDePagamentoOnline = Domain.Financeiro.TransacaoService
                .MontarTransacaoFormaPagamento(transacao, (int)formaPagamento, idEstabelecimento, ValorTotal);

            if (metodo == MetodoDePagamentoNoGatewayEnum.Pix)
            {
                var taxas = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService
                    .ObterTaxasConfiguradasParaEstabelecimentoEMetodoDePagamento(idEstabelecimento, transacaoFormaPagamentoDePagamentoOnline.FormaPagamento.ToMetodoDePagamentoNoGatewayEnum());
                var taxasDTO = new TaxasDTO(taxas.ObterPercentualTotalPorTransacao(), taxas.ObterValorFixoTotalPorTransacao());
                
                transacaoFormaPagamentoDePagamentoOnline.PercentualCobradoPelaOperadora = taxasDTO.PercentualCobradoPelaOperadora;
                transacaoFormaPagamentoDePagamentoOnline.ValorFixoCobradoPelaOperadora = taxasDTO.ValorFixoCobradoPelaOperadora;
            }

            return transacaoFormaPagamentoDePagamentoOnline;
        }
    }
}