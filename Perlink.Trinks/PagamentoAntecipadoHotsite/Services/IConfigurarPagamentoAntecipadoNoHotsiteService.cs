﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Pessoas;

namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Services
{
    public interface IConfigurarPagamentoAntecipadoNoHotsiteService : IService
    {
        void ConfigurarPagamentoAntecipadoDeServicoNoHotsite(Estabelecimento estabelecimento);
        bool EstabelecimentoPossuiConfiguracaoPagamentoAntecipadoHotsite(Estabelecimento estabelecimento);
    }
}