﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Shared.NHibernate.Paginacao;
using Perlink.Trinks.PagamentoAntecipadoHotsite.DTO;
using Perlink.Trinks.PagamentoAntecipadoHotsite.Repositories;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.PagamentoAntecipadoHotsite.Services
{
    public class GerenciarServicosComPagamentoAntecipadoHotsiteService : BaseService, IGerenciarServicosComPagamentoAntecipadoHotsiteService
    {
        public const decimal ValorMinimoParaPagamentoNaPagarMe = 1;
        #region ctor
        private readonly IServicoEstabelecimentoRepository _servicoEstabelecimentoRepository;
        private readonly IPagamentoAntecipadoHotsiteServicosRepository _pagamentoAntecipadoHotsiteServicosRepository;
        private readonly IConfigurarPagamentoAntecipadoNoHotsiteService _configurarPagamentoAntecipadoNoHotsiteService;

        public GerenciarServicosComPagamentoAntecipadoHotsiteService()
        {
            _servicoEstabelecimentoRepository = Domain.Pessoas.ServicoEstabelecimentoRepository;
            _pagamentoAntecipadoHotsiteServicosRepository = Domain.PagamentoAntecipadoHotsite.PagamentoAntecipadoHotsiteServicosRepository;
            _configurarPagamentoAntecipadoNoHotsiteService = Domain.PagamentoAntecipadoHotsite.ConfigurarPagamentoAntecipadoNoHotsiteService;
        }
        #endregion

        public ResultadoServicosPagamentoAntecipadoDto ListarServicosDisponiveisParaPagamentoAntecipadoNoHotsite(int idEstabelecimento, FiltroServicosDisponiveisPagamentoAntecipadoDto filtro)
        {
            var servicosDoEstabelecimento = _servicoEstabelecimentoRepository
                .QueryableAtivosComPrecoFixo(idEstabelecimento).Where(q => q.Preco >= ValorMinimoParaPagamentoNaPagarMe);
            var servicosComPagamentoAntecipado = _pagamentoAntecipadoHotsiteServicosRepository
                .ObterQueryDeServicosComPagamentoAntecipadoNoEstabelecimento(idEstabelecimento);
            
            var servicosDisponiveisParaPagamentoAntecipado = servicosDoEstabelecimento.Select(servicoEstabelecimento => 
                new ServicosDisponiveisParaPagamentoAntecipadoDto
                {
                    IdServicoEstabelecimento = servicoEstabelecimento.IdServicoEstabelecimento,
                    Servico = servicoEstabelecimento.Nome,
                    Categoria = servicoEstabelecimento.Servico.ServicoCategoria.Nome,
                    PagamentoAntecipadoHabilitado = servicosComPagamentoAntecipado.Any(sa => sa.IdServicoEstabelecimento == servicoEstabelecimento.IdServicoEstabelecimento && sa.Ativo)
                }
            );

            servicosDisponiveisParaPagamentoAntecipado = AplicarBusca(servicosDisponiveisParaPagamentoAntecipado, filtro.TextoPesquisa);
            filtro.Paginacao.TotalItens = servicosDisponiveisParaPagamentoAntecipado.Count();
            var servicos = AplicarPaginacao(servicosDisponiveisParaPagamentoAntecipado, filtro.Paginacao);
            return new ResultadoServicosPagamentoAntecipadoDto(servicos, filtro.Paginacao);
        }

        public void ConfigurarServicosComPagamentoAntecipado(ConfigurarServicosPagamentoAntecipadoDto configuracao)
        {
            try
            {
                configuracao.IdRecebedor = Domain.PagamentosOnlineNoTrinks.EstabelecimentoRecebedorRepository.ObterIdRecebedorPorIdEstabelecimento(configuracao.IdEstabelecimento);
                configuracao.IdsServicoEstabelecimento = configuracao.IdsServicoEstabelecimento.Distinct().ToList();
                var queryServicosComPagamentoAntecipado = _pagamentoAntecipadoHotsiteServicosRepository
                    .ObterQueryDeServicosComPagamentoAntecipadoNoEstabelecimento(configuracao.IdEstabelecimento)
                    .ToList();
                
                ConfigurarServicosParaPagamentoAntecipado(configuracao, queryServicosComPagamentoAntecipado);
            }
            catch (Exception ex)
            { 
                Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
                ValidationHelper.Instance.AdicionarItemValidacao("Erro ao tentar salvar serviços com pagamento antecipado.");
                throw;
            }
            
            _pagamentoAntecipadoHotsiteServicosRepository.Flush();
        }

        public bool ServicoExigePagamentoAntecipado(ServicoEstabelecimento servicoEstabelecimento)
        {
            var estabelecimentoPossuiPagamentoAntecipadoHabilitado = _configurarPagamentoAntecipadoNoHotsiteService
                .EstabelecimentoPossuiConfiguracaoPagamentoAntecipadoHotsite(servicoEstabelecimento.Estabelecimento);

            if (!estabelecimentoPossuiPagamentoAntecipadoHabilitado) 
                return false;
            
            var servicoExigePagamentoAntecipado = _pagamentoAntecipadoHotsiteServicosRepository
                .ServicoExigePagamentoAntecipado(servicoEstabelecimento.IdServicoEstabelecimento);
            
            return servicoExigePagamentoAntecipado;
        }

        private void ConfigurarServicosParaPagamentoAntecipado(ConfigurarServicosPagamentoAntecipadoDto configuracao,  
            List<PagamentoAntecipadoHotsiteServicos> servicosComPagamentoAntecipado)
        {
            var idsServicosDoEstabelecimento = _servicoEstabelecimentoRepository
                .QueryableAtivosComPrecoFixo(configuracao.IdEstabelecimento)
                .Where(q => q.Preco >= 1) // valor mínimo autorizado para pagamento na Pagarme
                .Select(s => s.IdServicoEstabelecimento)
                .ToList();

            foreach (var idServicoDoEstabelecimento in idsServicosDoEstabelecimento)
            {
                var deveAtivar = configuracao.SelecionarTodos || configuracao.IdsServicoEstabelecimento.Contains(idServicoDoEstabelecimento);

                if (!deveAtivar)
                {
                    continue;
                }

                AtivarOuRegistrarServicoParaPagamentoAntecipado(idServicoDoEstabelecimento, servicosComPagamentoAntecipado, configuracao.IdRecebedor);
            }
        }

        private void AtivarOuRegistrarServicoParaPagamentoAntecipado(int idServicoDoEstabelecimento, List<PagamentoAntecipadoHotsiteServicos> servicosComPagamentoAntecipado, int idRecebedor)
        {
            var servicoExistente = servicosComPagamentoAntecipado.FirstOrDefault(spa => spa.IdServicoEstabelecimento == idServicoDoEstabelecimento);

            if (servicoExistente == null)
            {
                var novoServico = new PagamentoAntecipadoHotsiteServicos(idServicoDoEstabelecimento, idRecebedor);
                _pagamentoAntecipadoHotsiteServicosRepository.SaveNewNoFlush(novoServico);
                return;
            }

            servicoExistente.Ativar();
            _pagamentoAntecipadoHotsiteServicosRepository.UpdateNoFlush(servicoExistente);
        }

        private static IQueryable<ServicosDisponiveisParaPagamentoAntecipadoDto> AplicarBusca(IQueryable<ServicosDisponiveisParaPagamentoAntecipadoDto> query, string busca)
        {
            if (!string.IsNullOrEmpty(busca))
                query = query.Where(q => q.Servico.Contains(busca) || q.Categoria.Contains(busca));
            
            return query;
        }
        
        private static List<ServicosDisponiveisParaPagamentoAntecipadoDto> AplicarPaginacao(IQueryable<ServicosDisponiveisParaPagamentoAntecipadoDto> query, ParametrosPaginacao paginacao)
            => query.Skip(paginacao.RegistroInicial - 1).Take(paginacao.RegistrosPorPagina).ToList();
    }
}