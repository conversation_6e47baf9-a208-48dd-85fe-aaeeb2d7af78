﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Cashback.DTO;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Vendas;

namespace Perlink.Trinks.Cashback.Services
{
    public interface ICashbackService : IService
    {
        DadosSalvarCashbackDto GerarDadosHorarioTransacaoEITemVendaCashback(Transacao transacao, Venda venda);
        DadosSalvarCashbackComissaoDto GerarDadosCashbackComissao(Transacao transacao, Venda venda);
        void PersistirDadosCashbackVinculadosAoFechamento(DadosSalvarCashbackDto dadosCashback);
        void PersistirDadosComissaoCashbackVinculadosAoFechamento(DadosSalvarCashbackComissaoDto dadosCashbackComissao);
        bool EstabelecimentoNecessitaBucarCashback(Estabelecimento estabelecimento);
    }
}
