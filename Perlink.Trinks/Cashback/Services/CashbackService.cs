﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Cashback.DTO;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Vendas;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cashback.Services
{
    public class CashbackService : BaseService, ICashbackService
    {
        public DadosSalvarCashbackDto GerarDadosHorarioTransacaoEITemVendaCashback(Transacao transacao, Venda venda)
        {
            DadosSalvarCashbackDto resultado = new DadosSalvarCashbackDto();

            if (transacao.PossuiCashbackNaoPersistido())
            {

                resultado.CashbackTransacao = transacao.ObterCashbackNaoPersistido();
                resultado.CashbackTransacao.Transacao = transacao;

                // O mesmo tratamento da atribuição de NULL no Cashback
                // será aplicado nas funções utilizadas abaixo durante montagem DTO
                LidarComHorariosTransacao(transacao, resultado);

                LidarComItensVenda(venda, resultado);
            }

            return resultado;
        }

        public DadosSalvarCashbackComissaoDto GerarDadosCashbackComissao(Transacao transacao, Venda venda)
        {
            DadosSalvarCashbackComissaoDto resultado = new DadosSalvarCashbackComissaoDto();

            LidarComComissaoHorariosTransacao(transacao, resultado);

            LidarComComissaoItensVenda(venda, resultado);

            return resultado;
        }

        public void PersistirDadosCashbackVinculadosAoFechamento(DadosSalvarCashbackDto dadosCashback)
        {
            if (dadosCashback.CashbackTransacao == null)
            {
                return;
            }

            dadosCashback.CashbackTransacao.VincularTransacao(dadosCashback.CashbackTransacao.Transacao.Id);
            dadosCashback.CashbackTransacao.Transacao = null;
            Domain.Cashback.CashbackTransacaoRepository.SaveNewNoFlush(dadosCashback.CashbackTransacao);

            foreach (var cashbackHorarioTransacao in dadosCashback.CashbackHorariosTransacao)
            {
                cashbackHorarioTransacao.VincularHorarioTransacao(cashbackHorarioTransacao.HorarioTransacao.Codigo);
                cashbackHorarioTransacao.HorarioTransacao = null;
                Domain.Cashback.CashbackHorarioTransacaoRepository.SaveNewNoFlush(cashbackHorarioTransacao);
            }

            foreach (var cashbackItemVenda in dadosCashback.CashbackItensVenda)
            {
                cashbackItemVenda.VincularItemVenda(cashbackItemVenda.ItemVenda.Id);
                cashbackItemVenda.ItemVenda = null;
                Domain.Cashback.CashbackItemVendaRepository.SaveNewNoFlush(cashbackItemVenda);
            }

            foreach (var cashbackComissao in dadosCashback.CashbackComissoes)
            {
                cashbackComissao.VincularComissao(cashbackComissao.Comissao.Id);
                cashbackComissao.Comissao = null;
                Domain.Cashback.CashbackComissaoRepository.SaveNewNoFlush(cashbackComissao);
            }

            foreach (var cashbackComissaoValorReceber in dadosCashback.CashbackComissaoValoresAReceber)
            {
                cashbackComissaoValorReceber.VincularComissaoValorReceber(cashbackComissaoValorReceber.ComissaoValorAReceber.Id);
                cashbackComissaoValorReceber.ComissaoValorAReceber = null;
                Domain.Cashback.CashbackComissaoValorAReceberRepository.SaveNewNoFlush(cashbackComissaoValorReceber);
            }

            Domain.Cashback.CashbackTransacaoRepository.Flush();
        }

        public void PersistirDadosComissaoCashbackVinculadosAoFechamento(DadosSalvarCashbackComissaoDto dadosCashbackComissao)
        {
            if (!dadosCashbackComissao.PossuiDadosParaSalvar())
            {
                return;
            }

            // Necessário flush para garantir que entidades de Comissao e ComissaoValorAReceber
            // possuem já ID nesse momento
            Domain.Financeiro.ComissaoRepository.Flush();

            foreach (var cashbackComissao in dadosCashbackComissao.CashbackComissoes)
            {
                cashbackComissao.VincularComissao(cashbackComissao.Comissao.Id);
                cashbackComissao.Comissao = null;
                Domain.Cashback.CashbackComissaoRepository.SaveNewNoFlush(cashbackComissao);
            }

            foreach (var cashbackComissaoValorReceber in dadosCashbackComissao.CashbackComissaoValoresAReceber)
            {
                cashbackComissaoValorReceber.VincularComissaoValorReceber(cashbackComissaoValorReceber.ComissaoValorAReceber.Id);
                cashbackComissaoValorReceber.ComissaoValorAReceber = null;
                Domain.Cashback.CashbackComissaoValorAReceberRepository.SaveNewNoFlush(cashbackComissaoValorReceber);
            }

            Domain.Cashback.CashbackComissaoRepository.Flush();
        }

        public bool EstabelecimentoNecessitaBucarCashback(Estabelecimento estabelecimento)
        {
            var possuiCashback = Domain.Cashback.BonusTransacaoRepository
                .EstabelecimentoJaPossuiTransacoesQueConsumiramBonus(estabelecimento.IdEstabelecimento);

            possuiCashback = possuiCashback || Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService
                .ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.CashbackCrmBonus).EstaDisponivel;

            return possuiCashback;
        }

        private static void LidarComHorariosTransacao(Transacao transacao, DadosSalvarCashbackDto resultado)
        {
            foreach (var horarioTransacao in transacao.HorariosTransacoes)
            {
                if (horarioTransacao.PossuiCashbackNaoPersistido())
                {
                    var cashbackHorarioTransacao = horarioTransacao.ObterCashbackNaoPersistido();
                    cashbackHorarioTransacao.HorarioTransacao = horarioTransacao;
                    resultado.CashbackHorariosTransacao.Add(cashbackHorarioTransacao);
                }
            }
        }

        private static void LidarComItensVenda(Venda venda, DadosSalvarCashbackDto resultado)
        {
            if (venda != null)
            {
                foreach (var itemVenda in venda.ItensVenda)
                {
                    if (itemVenda.PossuiCashbackNaoPersistido())
                    {
                        var cashbackItemVenda = itemVenda.ObterCashbackNaoPersistido();
                        cashbackItemVenda.ItemVenda = itemVenda;
                        resultado.CashbackItensVenda.Add(cashbackItemVenda);
                    }
                }
            }
        }

        private static void LidarComComissaoHorariosTransacao(Transacao transacao, DadosSalvarCashbackComissaoDto resultado)
        {
            foreach (var comissao in transacao.HorariosTransacoes.Select(ht => ht.Comissao))
            {
                if (comissao?.ObterCashbackNaoPersistido()?.IdComissao == 0)
                {
                    LidarComComissao(comissao, resultado);
                }
            }
        }

        private static void LidarComComissaoItensVenda(Venda venda, DadosSalvarCashbackComissaoDto resultado)
        {
            if (venda != null)
            {
                foreach (var comissao in venda.ItensVenda.Select(it => it.Comissao))
                {
                    if (comissao?.ObterCashbackNaoPersistido()?.IdComissao == 0)
                    {
                        LidarComComissao(comissao, resultado);
                    }
                }
            }
        }

        private static void LidarComComissao(Comissao comissao, DadosSalvarCashbackComissaoDto resultado)
        {
            var cashbackComissao = comissao.ObterCashbackNaoPersistido();
            cashbackComissao.Comissao = comissao;

            resultado.CashbackComissoes.Add(cashbackComissao);

            if(comissao.ValoresAReceber.Count > 0)
            {
                LidarComComissaoValorReceber(comissao.ValoresAReceber, resultado);
            }
        }

        private static void LidarComComissaoValorReceber(IList<ValorDeComissaoAReceber> comissaoValoresReceber, DadosSalvarCashbackComissaoDto resultado)
        {
            foreach (var comissaoValorReceber in comissaoValoresReceber)
            {
                if (comissaoValorReceber.ObterCashbackNaoPersistido()?.IdComissaoValorReceber == 0)
                {
                    var cashbackComissaoValorReceber = comissaoValorReceber.ObterCashbackNaoPersistido();
                    cashbackComissaoValorReceber.ComissaoValorAReceber = comissaoValorReceber;

                    resultado.CashbackComissaoValoresAReceber.Add(cashbackComissaoValorReceber);
                }
            }
        }
    }
}
