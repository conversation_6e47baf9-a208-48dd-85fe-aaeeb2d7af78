﻿using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cashback.Repositories
{
    public partial class CashbackHorarioTransacaoRepository : ICashbackHorarioTransacaoRepository
    {
        public decimal ObterValorCashbackDoHorarioTransacao(int idHorarioTransacao)
        {
            return Queryable(true).Where(cht => cht.IdHorarioTransacao == idHorarioTransacao).Select(cht => cht.Valor)
                .FirstOrDefault();
        }

        public List<CashbackHorarioTransacao> ObterCashbacksDosHorariosTransacaoPeloHorario(int idHorario)
        {
            var queryableHorario = Domain.Pessoas.HorarioRepository.Queryable();

            return (from horario in queryableHorario
                    from horarioTransacao in horario.HorariosTransacoes
                    join cashback in Domain.Cashback.CashbackHorarioTransacaoRepository.Queryable() on
                    horarioTransacao.Codigo equals cashback.IdHorarioTransacao
                    where horario.Id == idHorario
                    select cashback).ToList();
        }
    }
}
