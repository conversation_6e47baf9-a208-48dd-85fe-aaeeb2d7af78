﻿using NHibernate.Linq;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Cashback.Repositories
{
    public partial class BonusTransacaoRepository : IBonusTransacaoRepository
    {
        public bool EstabelecimentoJaPossuiTransacoesQueConsumiramBonus(int idEstabelecimento)
        {
            return (from bonus in Domain.Cashback.BonusTransacaoRepository.Queryable()
                    join estabelecimento in Domain.Pessoas.EstabelecimentoRepository.Queryable()
                    on bonus.Transacao.PessoaQueRecebeu.IdPessoa equals estabelecimento.PessoaJuridica.IdPessoa
                    where bonus.ValorBonusConsumido > 0 &&
                          estabelecimento.IdEstabelecimento == idEstabelecimento
                    select bonus.IdTransacao).Any();
        }

        public BonusTransacao ObterPorIdTransacao(int idTransacao)
        {
            return Domain.Cashback.BonusTransacaoRepository.Queryable()
                    .FirstOrDefault(bonus => bonus.IdTransacao == idTransacao);
        }

        public List<BonusTransacao> ObterParaEnvioAssincrono()
        {
            var queryableBonusTransacao = Queryable().Fetch(bonus => bonus.Transacao).ThenFetch(transacao => transacao.PessoaQueRecebeu);
            return queryableBonusTransacao
                .Where(bonus => !bonus.Enviado && bonus.ObjetoEnvioAssincrono != null && bonus.ObjetoEnvioAssincrono != "")
                .OrderBy(bonus => bonus.Transacao.PessoaQueRecebeu.IdPessoa)
                .Take(30)
                .Select(bonus => new BonusTransacao()
                {
                    Cancelado = bonus.Cancelado,
                    CelularCliente = bonus.CelularCliente,
                    DataCriacao = bonus.DataCriacao,
                    Enviado = bonus.Enviado,
                    IdBonusCrmBonus = bonus.IdBonusCrmBonus,
                    IdClienteCrmBonus = bonus.IdClienteCrmBonus,
                    IdPedidoCrmBonus = bonus.IdPedidoCrmBonus,
                    IdTransacao = bonus.IdTransacao,
                    ObjetoEnvioAssincrono = bonus.ObjetoEnvioAssincrono,
                    ValorBonusConsumido = bonus.ValorBonusConsumido,
                    ValorBonusGerado = bonus.ValorBonusGerado,
                    Transacao = new Financeiro.Transacao()
                    {
                        Id = bonus.IdTransacao,
                        PessoaQueRecebeu = new Pessoas.PessoaJuridica()
                        {
                            IdPessoa = bonus.Transacao.PessoaQueRecebeu.IdPessoa,
                        }
                    }
                }).ToList();
        }
    }
}
