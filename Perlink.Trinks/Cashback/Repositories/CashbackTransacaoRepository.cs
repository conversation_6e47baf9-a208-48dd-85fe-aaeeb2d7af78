﻿using System.Linq;

namespace Perlink.Trinks.Cashback.Repositories
{
    public partial class CashbackTransacaoRepository : ICashbackTransacaoRepository
    {
        public decimal ObterValorCashbackPelaTransacao(int idTransacao)
        {
            return Queryable().Where(cashback => cashback.IdTransacao == idTransacao)
                .Select(cashback => cashback.Valor).FirstOrDefault();
        }

        public decimal ObterSomaDeValorCashbackPelaTransacao(int idTransacao)
        {
            return Queryable()
                .Where(cashback => cashback.IdTransacao == idTransacao)
                .Sum(cashb => (decimal?)cashb.Valor) ?? 0;
        }

        public CashbackTransacao ObterCashbackPelaTransacao(int idTransacao)
        {
            return Queryable().FirstOrDefault(cashback => cashback.IdTransacao == idTransacao);
        }
    }
}
