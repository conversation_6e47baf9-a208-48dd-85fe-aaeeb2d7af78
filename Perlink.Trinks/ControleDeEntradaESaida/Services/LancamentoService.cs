﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.ControleDeEntradaESaida.DTO;
using Perlink.Trinks.ControleDeEntradaESaida.Enum;
using Perlink.Trinks.ControleDeEntradaESaida.Filtros;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Despesas.DTO;
using Perlink.Trinks.Despesas.Enums;
using Perlink.Trinks.DTO;
using Perlink.Trinks.Enums;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Perlink.Trinks.ControleDeEntradaESaida.Services
{
    public class FiltroLancamentos
    {
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }
        public Estabelecimento Estabelecimento { get; set; }
        public bool ApenasAtivos { get; set; }
    }

    public class LancamentoService : BaseService, ILancamentoService
    {

        public List<LancamentoCategoriaDTO> ObterCategoriasDisponiveis(Estabelecimento estabelecimento, AreasTrinksEnum area)
        {
            var categorias = Domain.ControleDeEntradaESaida.LancamentoDeReceitaCategoriaRepository.Queryable()
                .Where(f => f.Area == area && f.Ativo)
                .Select(f => new LancamentoCategoriaDTO()
                {
                    IdLancamentoCategoria = f.IdLancamentoCategoria,
                    Nome = f.Nome,
                    UrlDeLancamentoDeReceita = f.UrlDeLancamentoDeReceita,
                    Ativo = f.Ativo
                });
            categorias = RetirarAporteCasoNaoEstejaDisponivel(estabelecimento, categorias);
            categorias = RetirarProdutoSeNaoHouverComoFazerUmaVenda(estabelecimento, categorias);
            categorias = RetirarPacoteSeNaoHouverComoFazerUmaVenda(estabelecimento, categorias);
            categorias = RetirarValePresenteSeEstabelecimentoNaoUtilizar(estabelecimento, categorias);
            categorias = RetirarCreditoDeClienteSeEstabelecimentoNaoUtilizar(estabelecimento, categorias);

            return categorias.ToList();
        }

        public Dictionary<DateTime, List<LancamentoDTO>> ObterLancamentosAgrupadosPorDia(Estabelecimento estabelecimento, AcessoBackoffice acessoBackoffice, DateTime dataInicio, DateTime dataFim, bool apenasAtivos, LancamentoStatusPagamentoEnum statusPagamento, int[] idsCategorias = null)
        {
            var listaDeLancamentos = ObterListaDeDespesas(estabelecimento, acessoBackoffice, dataInicio, dataFim, apenasAtivos, statusPagamento, idsCategorias);

            if (idsCategorias == null)
            {
                var listaDeReceitas = ObterListaDeReceitas(estabelecimento, dataInicio, dataFim, apenasAtivos);
                listaDeLancamentos.AddRange(listaDeReceitas);
            }

            DateTime defaultData = default;

            var retorno = listaDeLancamentos
                .GroupBy(l => new DateTime(l.Data?.Year ?? defaultData.Year, l.Data?.Month ?? defaultData.Month,
                        l.Data?.Day ?? defaultData.Month))
                .OrderByDescending(x => x.Key)
                .ToDictionary(x => x.Key, x => x.OrderByDescending(l => l.Data).ToList());

            return retorno;
        }

        private List<LancamentoDTO> ObterListaDeReceitas(Estabelecimento estabelecimento, DateTime dataInicio, DateTime dataFim, bool apenasAtivos)
        {
            dataInicio = (DateTime)Domain.Estabelecimentos.FuncionamentoDeEstabelecimentoService.ObterExibicaoDadosAPartirDe(dataInicio, estabelecimento.IdEstabelecimento);
            var filtro = new FiltroLancamentos
            {
                DataInicio = dataInicio,
                DataFim = dataFim,
                Estabelecimento = estabelecimento,
                ApenasAtivos = apenasAtivos
            };

            var aportes = ObterLancamentosDeAportes(filtro);
            var creditoDeCliente = ObterLancamentosDeCreditoDeCliente(filtro);

            var pagamentosDeClientes = ObterLancamentosDePagamentosDeClientes(filtro);
            var pagamentosDeProfissional = ObterLancamentosDeProfissionais(filtro);

            var lancamentos = new List<LancamentoDTO>();
            lancamentos.AddRange(aportes);
            lancamentos.AddRange(creditoDeCliente);
            lancamentos.AddRange(pagamentosDeClientes);
            lancamentos.AddRange(pagamentosDeProfissional);

            return lancamentos;
        }

        private Expression<Func<ItemVenda, bool>> ObterFiltroItemVenda(FiltroLancamentos filtro, Expression<Func<ItemVenda, bool>> filtroAdicional = null)
        {
            Expression<Func<ItemVenda, bool>> filtroBase = itemVenda =>
                itemVenda.Venda.Transacao.Ativo
                && itemVenda.Venda.Transacao.DataHora >= filtro.DataInicio.Date
                && itemVenda.Venda.Transacao.DataHora < filtro.DataFim.Date.AddDays(1)
                && itemVenda.Venda.Transacao.PessoaQueRecebeu.IdPessoa == filtro.Estabelecimento.PessoaJuridica.IdPessoa
                && itemVenda.Venda.Transacao.TransacaoQueEstounouEsta == null
                && itemVenda.Venda.Transacao.TipoTransacao == TipoTransacaoEnum.Pagamento
                && itemVenda.Venda.Transacao.TotalPago != null;

            if (filtroAdicional != null)
            {
                filtroBase = filtroBase.And(filtroAdicional);
            }

            return filtroBase;
        }

        private Expression<Func<ItemVenda, bool>> ObterFiltroItemVendaDeClientes(FiltroLancamentos filtro)
        {
            Expression<Func<ItemVenda, bool>> filtroVendaParaCliente = itemVenda => 
                itemVenda.Venda.Transacao.FoiVendaParaProfissional == false;

            return ObterFiltroItemVenda(filtro, filtroVendaParaCliente);
        }

        private Expression<Func<ItemVenda, bool>> ObterFiltroItemVendaDeProfissionais(FiltroLancamentos filtro)
        {
            Expression<Func<ItemVenda, bool>> filtroProfissional = itemVenda =>
                itemVenda.Venda.Transacao.TotalPago > 0
                && !Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable()
                       .Any(g => g.Transacao.Id == itemVenda.Venda.Transacao.Id && g.FormaPagamento.Id == (int)FormaPagamentoEnum.DescontoDeProfissional)
                && itemVenda.Venda.Transacao.FoiVendaParaProfissional == true;

            return ObterFiltroItemVenda(filtro, filtroProfissional);
        }

        private Expression<Func<HorarioTransacao, bool>> ObterFiltroHorarioTransacao(FiltroLancamentos filtro)
        {
            Expression<Func<HorarioTransacao, bool>> deleg = horarioTransacao =>
                horarioTransacao.Transacao.Ativo
                && horarioTransacao.Transacao.DataHora >= filtro.DataInicio.Date
                && horarioTransacao.Transacao.DataHora < filtro.DataFim.Date.AddDays(1)
                && horarioTransacao.Transacao.PessoaQueRecebeu.IdPessoa == filtro.Estabelecimento.PessoaJuridica.IdPessoa
                && horarioTransacao.Transacao.TransacaoQueEstounouEsta == null
                && horarioTransacao.Transacao.TipoTransacao == TipoTransacaoEnum.Pagamento
                && horarioTransacao.Transacao.TotalPago > 0;

            return deleg;
        }

        private Expression<Func<TransacaoItem, bool>> ObterFiltroTransacaoClube(FiltroLancamentos filtro)
        {
            Expression<Func<TransacaoItem, bool>> deleg = transacaoItem =>
                transacaoItem.Transacao.Ativo
                && transacaoItem.Transacao.DataHora >= filtro.DataInicio.Date
                && transacaoItem.Transacao.DataHora < filtro.DataFim.Date.AddDays(1)
                && transacaoItem.Transacao.PessoaQueRecebeu.IdPessoa == filtro.Estabelecimento.PessoaJuridica.IdPessoa
                && transacaoItem.Transacao.TransacaoQueEstounouEsta == null
                && transacaoItem.Transacao.TipoTransacao == TipoTransacaoEnum.Pagamento
                && transacaoItem.Valor > 0
                && (transacaoItem.Tipo == TransacaoItemTipo.PagamentoDeAssinatura || transacaoItem.Tipo == TransacaoItemTipo.PagamentoDeMultaDeCancelamento);

            return deleg;
        }

        private List<LancamentoDTO> ObterLancamentos(Expression<Func<ItemVenda, bool>> filtroTransacoes, string descricao, bool incluirDescricaoPrePago = false, Expression<Func<HorarioTransacao, bool>> filtroHorarioTransacao = null, Expression<Func<TransacaoItem, bool>> filtroTransacaoClube = null)
        {
            var produtos = Domain.Vendas.ItemVendaProdutoRepository.Queryable(true)
                .Where(filtroTransacoes)
                .Select(p => p.Venda.Transacao.Id)
                .ToList();

            var pacotes = Domain.Vendas.ItemVendaPacoteRepository.Queryable(true)
                .Where(filtroTransacoes)
                .Select(p => p.Venda.Transacao.Id)
                .ToList();

            var valePresentes = Domain.Vendas.ItemVendaValePresenteRepository.Queryable(true)
                .Where(filtroTransacoes)
                .Select(p => p.Venda.Transacao.Id)
                .ToList();

            var servicos = filtroHorarioTransacao != null
                ? Domain.Pessoas.HorarioTransacaoRepository.Queryable(true).Where(filtroHorarioTransacao).Select(ht => ht.Transacao.Id).ToList()
                : new List<int>();

            var clubes = filtroTransacaoClube != null
                ? Domain.Financeiro.TransacaoItemRepository.Queryable(true).Where(filtroTransacaoClube).Select(ti => ti.Transacao.Id).ToList()
                : new List<int>();

            var idsTransacoes = produtos
                .Union(pacotes)
                .Union(valePresentes)
                .Union(servicos)
                .Union(clubes)
                .Distinct()
                .ToList();

            int TamanhoLote = 2000;
            var transacoes = new List<LancamentoDTO>();

            for (int i = 0; i < Math.Ceiling((double)idsTransacoes.Count / TamanhoLote); i++)
            {
                var idsDoLote = idsTransacoes.Skip(i * TamanhoLote).Take(TamanhoLote).ToList();

                var loteTransacoes = Domain.Financeiro.TransacaoRepository.Queryable(true)
                    .Where(t => idsDoLote.Contains(t.Id))
                    .Select(t => new LancamentoDTO
                    {
                        Id = t.Id,
                        Data = t.DataHora,
                        NomeExibido = t.PessoaQuePagou.NomeCompleto,
                        Valor = t.TotalPago.Value,
                        Descricao = incluirDescricaoPrePago && t.TotalPagoEmPrePago < 0 ? $"{descricao} - Crédito utilizado" : descricao,
                        EhRecorrente = false,
                        TipoDeLancamento = TipoDeLancamentoEnum.FechamentoDeConta
                    })
                    .ToList();

                transacoes.AddRange(loteTransacoes);
            }

            return transacoes;
        }

        private List<LancamentoDTO> ObterLancamentosDeProfissionais(FiltroLancamentos filtro)
        {
            var filtroTransacoes = ObterFiltroItemVendaDeProfissionais(filtro);

            return ObterLancamentos(filtroTransacoes, "Pagamento de profissional");
        }

        private List<LancamentoDTO> ObterLancamentosDePagamentosDeClientes(FiltroLancamentos filtro)
        {
            var filtroTransacoes = ObterFiltroItemVendaDeClientes(filtro);
            var filtroHorarioTransacao = ObterFiltroHorarioTransacao(filtro);
            var filtroTransacaoClube = ObterFiltroTransacaoClube(filtro);

            return ObterLancamentos(filtroTransacoes, "Pagamento de cliente", true, filtroHorarioTransacao, filtroTransacaoClube);
        }

        private List<LancamentoDTO> ObterLancamentosDeCreditoDeCliente(FiltroLancamentos filtro)
        {
            var historicoDeCreditoDeClienteQueryable = Domain.Financeiro.TransacaoFormaPagamentoRepository
                .FiltrarHistoricoDeCreditoCliente(filtro.Estabelecimento.IdEstabelecimento, filtro.DataInicio.Date,
                    filtro.DataFim.Date.AddHours(23).AddMinutes(59).AddSeconds(59), true, true)
                .Where(x =>
                    x.Transacao.Ativo &&
                    x.ValorPago < 0 &&
                    x.Transacao.TipoTransacao == TipoTransacaoEnum.Pagamento);

            var creditoDeCliente = historicoDeCreditoDeClienteQueryable
                .Select(x => new LancamentoDTO
                {
                    Id = x.Transacao.Id,
                    Data = x.Transacao.DataHora,
                    Descricao = "Crédito de Cliente",
                    NomeExibido = "Compra de Crédito " + x.Transacao.PessoaQuePagou.NomeCompleto,
                    Valor = x.ValorPago * -1,
                    EhRecorrente = false,
                    TipoDeLancamento = TipoDeLancamentoEnum.CreditoDeCliente
                })
                .ToList();

            return creditoDeCliente;
        }

        private List<LancamentoDTO> ObterLancamentosDeAportes(FiltroLancamentos filtro)
        {
            var aportes = Domain.ControleDeEntradaESaida.LancamentoAporteService.ObterAportesDoEstabelecimento(new FiltroAporte
            {
                DataPagamentoInicio = filtro.DataInicio,
                DataPagamentoFim = filtro.DataFim,
                SomenteNaoEstornados = filtro.ApenasAtivos,
                IdEstabelecimento = filtro.Estabelecimento.IdEstabelecimento,
                AplicarPaginacao = false
            }).Registros.Select(f => new LancamentoDTO()
            {
                Id = f.IdAporte,
                Data = f.DataDePagamento,
                NomeExibido = string.IsNullOrEmpty(f.Descricao) ? "Investimento no negócio" : f.Descricao,
                Descricao = "Aporte",
                Valor = f.Valor,
                TipoDeLancamento = TipoDeLancamentoEnum.Aporte
            }).ToList();

            return aportes;
        }

        private List<LancamentoDTO> ObterListaDeDespesas(Estabelecimento estabelecimento, AcessoBackoffice acessoBackoffice, DateTime dataInicio, DateTime dataFim, bool apenasAtivos, LancamentoStatusPagamentoEnum statusPagamento, int[] idsCategorias = null)
        {
            var dataInicioAPartirde = Domain.Estabelecimentos.FuncionamentoDeEstabelecimentoService.ObterExibicaoDadosAPartirDe(dataInicio, estabelecimento.IdEstabelecimento);

            if (dataInicioAPartirde != null)
                dataInicio = dataInicioAPartirde.Value;



            var filtro = new FiltroBuscaLancamentos(estabelecimento.IdEstabelecimento, acessoBackoffice)
            {
                ApenasAtivos = apenasAtivos,
                StatusPagamento = statusPagamento,
                TipoBuscaPeriodoContaAPagar = TipoBuscaPeriodoLancamento.DataPagamento,
                InicioPeriodo = dataInicio,
                FinalPeriodo = dataFim,
                IdsCategorias = idsCategorias
            };

            var despesas = Domain.Despesas.LancamentoRepository.Filtrar(filtro);
            var listaDeLancamentos = despesas.Select(f => new LancamentoDTO()
            {
                Id = f.IdLancamento,
                NomeExibido = f.LancamentoCategoria.Nome,
                Descricao = f.LancamentoCategoria.LancamentoTipo.Nome,
                Valor = f.Valor,
                Data = f.DataPagamento,
                EhRecorrente = f.LancamentoRecorrencia != null,
                TipoDeLancamento = TipoDeLancamentoEnum.Despesa,
                IdCategoriaPadro = f.LancamentoCategoria.LancamentoCategoriaPadrao?.IdLancamentoCategoria
            }).ToList();

            return listaDeLancamentos;
        }

        #region Metodos Privados
        private IQueryable<LancamentoCategoriaDTO> RetirarProdutoSeNaoHouverComoFazerUmaVenda(Estabelecimento estabelecimento, IQueryable<LancamentoCategoriaDTO> categorias)
        {
            var existemProdutosDisponiveis = Domain.Pessoas.EstabelecimentoProdutoRepository.ExistemRegistros(
                    new FiltroBuscaEstabelecimentoProduto
                    {
                        ApenasRevenda = true,
                        ApenasAtivos = true,
                        IdEstabelecimento = estabelecimento.IdEstabelecimento
                    });

            if (!existemProdutosDisponiveis)
                categorias = categorias.Where(f => f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.ProdutoBackoffice &&
                                                   f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.ProdutoTrinksPro);
            return categorias;
        }

        private IQueryable<LancamentoCategoriaDTO> RetirarValePresenteSeEstabelecimentoNaoUtilizar(Estabelecimento estabelecimento, IQueryable<LancamentoCategoriaDTO> categorias)
        {
            var estabelecimentoUtilizaValePresente = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .VerificarSeEstabelecimentoUsaFormaPagamento(estabelecimento.IdEstabelecimento, FormaPagamentoEnum.ValePresente);

            if (!estabelecimentoUtilizaValePresente)
                categorias = categorias.Where(f => f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.ValePresenteBackoffice);
            return categorias;
        }

        private IQueryable<LancamentoCategoriaDTO> RetirarCreditoDeClienteSeEstabelecimentoNaoUtilizar(Estabelecimento estabelecimento, IQueryable<LancamentoCategoriaDTO> categorias)
        {
            var estabelecimentoUtilizaCreditoDeCliente = Domain.Pessoas.EstabelecimentoFormaPagamentoRepository
                .VerificarSeEstabelecimentoUsaFormaPagamento(estabelecimento.IdEstabelecimento, FormaPagamentoEnum.CreditoCliente);

            if (!estabelecimentoUtilizaCreditoDeCliente)
                categorias = categorias.Where(f => f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.CreditoDeClienteBackoffice);
            return categorias;
        }

        private IQueryable<LancamentoCategoriaDTO> RetirarPacoteSeNaoHouverComoFazerUmaVenda(Estabelecimento estabelecimento, IQueryable<LancamentoCategoriaDTO> categorias)
        {
            var existemPacotesDisponiveis = Domain.Pacotes.PacoteRepository.ExistePacotesParaEstabelecimento(estabelecimento);

            if (!existemPacotesDisponiveis)
                categorias = categorias.Where(f => f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.PacoteBackoffice);
            return categorias;
        }

        private IQueryable<LancamentoCategoriaDTO> RetirarAporteCasoNaoEstejaDisponivel(Estabelecimento estabelecimento, IQueryable<LancamentoCategoriaDTO> categorias)
        {
            var aporteEstaDisponivel = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.Aporte).EstaDisponivel;

            if (!aporteEstaDisponivel)
                categorias = categorias.Where(f => f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.AporteBackoffice &&
                                                   f.IdLancamentoCategoria != (int)LancamentoDeReceitaCategoriaEnum.AporteTrinksPro);
            return categorias;
        }
        #endregion
    }
}