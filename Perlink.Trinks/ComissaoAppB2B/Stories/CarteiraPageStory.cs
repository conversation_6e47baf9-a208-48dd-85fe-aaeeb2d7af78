﻿using Perlink.DomainInfrastructure.Stories;
using Perlink.Trinks.ComissaoAppB2B.DTO;
using Perlink.Trinks.ComissaoAppB2B.Enum;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.ComissaoAppB2B.Stories
{

    public class CarteiraPageStory : BaseStory, ICarteiraPageStory
    {

        public List<ComissaoRecebidaDTO> UltimasComissoesRecebidas(int idEstabelecimento, int quantidade, DateTime dataInicio, DateTime dataFim, bool considerarDadosDePacote)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            var comissoesValor = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);

            comissoesValor = comissoesValor
                .Where(f => f.Valor != 0)
                .OrderByDescending(f => f.DataDaComissaoAReceber).Take(quantidade);

            var estabelecimentoTrabalhaComProdutoFracionado = Domain.Pessoas.EstabelecimentoService.EstabelecimentoTrabalhaComProdutoFracionado(idEstabelecimento);

            var comissoesProduto = from cv in comissoesValor
                                   join ivp in Domain.Vendas.ItemVendaProdutoRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                   select new ComissaoRecebidaDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       Data = cv.DataDaComissaoAReceber,
                                       IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                       Nome = Domain.ComissaoAppB2B.ComissaoService.AdicionarQuantidadeEMedidasNoNome(
                                           estabelecimentoTrabalhaComProdutoFracionado,
                                           ivp.EstabelecimentoProduto.Descricao,
                                           ivp.Quantidade,
                                           ivp.EstabelecimentoProduto.UnidadeMedida.Simbolo,
                                           ivp.EstabelecimentoProduto.MedidasPorUnidade,
                                           ivp.TipoDeQuantidade)
                                   };

            var comissoesServico = from cv in comissoesValor
                                   join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.Comissao.Id
                                   select new ComissaoRecebidaDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                       Nome = ht.Horario.ServicoEstabelecimento.Nome,
                                       Valor = cv.Valor,
                                       Data = cv.DataDaComissaoAReceber
                                   };

            var comissoesServicoAssistente = from cv in comissoesValor
                                             join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.ComissaoAssistente.Id
                                             select new ComissaoRecebidaDTO
                                             {
                                                 Id = cv.Id,
                                                 IdTransacao = cv.Comissao.Transacao.Id,
                                                 IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                                 Nome = ht.Horario.ServicoEstabelecimento.Nome,
                                                 Valor = cv.Valor,
                                                 Data = cv.DataDaComissaoAReceber
                                             };

            var lista = comissoesProduto.ToList();
            lista.AddRange(comissoesServico);
            lista.AddRange(comissoesServicoAssistente);

            if (considerarDadosDePacote)
            {
                var comissoesPacote = from cv in comissoesValor
                                      join ivp in Domain.Vendas.ItemVendaPacoteRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                      select new ComissaoRecebidaDTO
                                      {
                                          Id = cv.Id,
                                          IdTransacao = cv.Comissao.Transacao.Id,
                                          Valor = cv.Valor,
                                          Data = cv.DataDaComissaoAReceber,
                                          IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                          Nome = ivp.PacoteCliente.Nome
                                      };

                lista.AddRange(comissoesPacote);
            }

            lista = lista.OrderBy(f => f.Data).ToList();

            return lista;
        }

        private List<ComissaoRecebidaDTO> UltimasComissoesRecebidas(int idEstabelecimento, int quantidade, ParametrosFiltrosRelatorio filtro)
        {
            var comissoesValor = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);

            comissoesValor = comissoesValor
                .Where(f => f.Valor != 0)
                .OrderByDescending(f => f.DataDaComissaoAReceber).Take(quantidade);

            var estabelecimentoTrabalhaComProdutoFracionado = Domain.Pessoas.EstabelecimentoService.EstabelecimentoTrabalhaComProdutoFracionado(idEstabelecimento);

            var comissoesProduto = from cv in comissoesValor
                                   join ivp in Domain.Vendas.ItemVendaProdutoRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                   select new ComissaoRecebidaDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       Data = cv.DataDaComissaoAReceber,
                                       IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                       Nome = Domain.ComissaoAppB2B.ComissaoService.AdicionarQuantidadeEMedidasNoNome(
                                           estabelecimentoTrabalhaComProdutoFracionado,
                                           ivp.EstabelecimentoProduto.Descricao,
                                           ivp.Quantidade,
                                           ivp.EstabelecimentoProduto.UnidadeMedida.Simbolo,
                                           ivp.EstabelecimentoProduto.MedidasPorUnidade,
                                           ivp.TipoDeQuantidade)
                                   };

            var comissoesServico = from cv in comissoesValor
                                   join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.Comissao.Id
                                   select new ComissaoRecebidaDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                       Nome = ht.Horario.ServicoEstabelecimento.Nome,
                                       Valor = cv.Valor,
                                       Data = cv.DataDaComissaoAReceber
                                   };

            var comissoesServicoAssistente = from cv in comissoesValor
                                             join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.ComissaoAssistente.Id
                                             select new ComissaoRecebidaDTO
                                             {
                                                 Id = cv.Id,
                                                 IdTransacao = cv.Comissao.Transacao.Id,
                                                 IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                                 Nome = ht.Horario.ServicoEstabelecimento.Nome,
                                                 Valor = cv.Valor,
                                                 Data = cv.DataDaComissaoAReceber
                                             };

            var comissoesPacote = from cv in comissoesValor
                                  join ivp in Domain.Vendas.ItemVendaPacoteRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                  select new ComissaoRecebidaDTO
                                  {
                                      Id = cv.Id,
                                      IdTransacao = cv.Comissao.Transacao.Id,
                                      Valor = cv.Valor,
                                      Data = cv.DataDaComissaoAReceber,
                                      IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                      Nome = ivp.PacoteCliente.Nome
                                  };

            var lista = comissoesProduto.ToList();
            lista.AddRange(comissoesServico);
            lista.AddRange(comissoesServicoAssistente);
            lista.AddRange(comissoesPacote);

            lista = lista.OrderBy(f => f.Data).ToList();

            return lista;
        }

        public ComissaoValoresParaCalculoDTO ComissaoTotalQuantidadeEValorTotalEmVales(EstabelecimentoProfissional estabelecimentoProfissional, DateTime dataInicio, DateTime dataFim, bool considerarDadosDePacote)
        {
            var filtro = new ParametrosFiltrosRelatorio()
            {
                Estabelecimento = estabelecimentoProfissional.Estabelecimento,
                IdEstabelecimentoProfissional = estabelecimentoProfissional.Codigo,
                DataInicial = dataInicio,
                DataFinal = dataFim,
            };

            var comissoesServico = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, false);
            var valoresComissoesServico = comissoesServico.Select(c => c.Valor).ToArray();
            var comissoesServicoTotal = valoresComissoesServico.Sum(g => (decimal?)g) ?? 0;
            var quantidadeServico = valoresComissoesServico.Count();

            var comissoesProduto = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeProduto(filtro, false);
            var valoresComissoesProduto = comissoesProduto.Select(c => c.Valor).ToArray();
            var comissoesProdutoTotal = valoresComissoesProduto.Sum(g => (decimal?)g) ?? 0;
            var quantidadeProduto = valoresComissoesProduto.Count();

            var totalDeVales = Domain.Despesas.LancamentoRepository.ListarVales(filtro);
            var valorTotalDeVales = totalDeVales.Sum(g => (decimal?)g.Valor) ?? 0;

            var totalDescontoProfissional = Domain.Financeiro.RelatorioComissaoService.ObterTotalDeDescontosProfissionalNoPeriodo(dataInicio.Date, dataFim.Date.AddDays(1), estabelecimentoProfissional.Profissional.PessoaFisica.IdPessoa, estabelecimentoProfissional.Estabelecimento.PessoaJuridica.IdPessoa) ?? 0;

            var res = new ComissaoValoresParaCalculoDTO
            {
                ServicoTotal = comissoesServicoTotal,
                ServicoQuantidade = quantidadeServico,
                ProdutoTotal = comissoesProdutoTotal,
                ProdutoQuantidade = quantidadeProduto,
                ValorTotalDeVales = valorTotalDeVales,
                ValorDescontoProfissional = totalDescontoProfissional
            };

            if (considerarDadosDePacote)
            {
                var comissoesPacote = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDePacote(filtro, false);
                var valoresComissoesPacote = comissoesPacote.Select(c => c.Valor).ToArray();
                var comissoesPacoteTotal = valoresComissoesPacote.Sum(g => (decimal?)g) ?? 0;
                var quantidadePacote = valoresComissoesPacote.Count();

                res.PacoteTotal = comissoesPacoteTotal;
                res.PacoteQuantidade = quantidadePacote;
            }

            return res;
        }

        public ExtratoPagamentoComissaoDiaDTO ObterDetalhesDaComissao(int idComissaoAReceber, int idPessoa, bool considerarDadosDePacote)
        {
            var comissaoAReceber = Domain.Financeiro.ValorDeComissaoAReceberRepository.Queryable()
                .FirstOrDefault(v => v.Id == idComissaoAReceber && v.Comissao.PessoaComissionada.IdPessoa == idPessoa);

            if (comissaoAReceber == null)
                return null;

            var comissaoRetorno = new ExtratoPagamentoComissaoDiaDTO()
            {
                NomeCliente = comissaoAReceber.Comissao.Transacao.PessoaQuePagou?.NomeCompleto,
                DataPagamentoCliente = comissaoAReceber.Comissao.Transacao.DataHora,
                DataPagamentoProfissional = comissaoAReceber.DataDaComissaoAReceber,
                ValorPagar = comissaoAReceber.Valor,
                ValorDescontoCliente = comissaoAReceber.DescontoClienteProporcional,
                ValorDescontoOperadora = comissaoAReceber.DescontoOperadoraProporcional,
                ValorDescontoCustoOperacional = comissaoAReceber.DescontoExtraProporcional,
                ValorBase = comissaoAReceber.ValorBaseProporcional,
                ValorBruto = comissaoAReceber.ValorBrutoProporcional,
                ValorFoiAlteradoManualmente = comissaoAReceber.ValorAlteradoManualmente,
                ValorFoiParcelado = comissaoAReceber.Comissao.ValorBruto != comissaoAReceber.ValorBrutoProporcional,
                PercentualComissao = comissaoAReceber.ValorAlteradoManualmente ? (decimal?)null : comissaoAReceber.Comissao.ValorComissao
            };

            if ((int)comissaoAReceber.Comissao.TipoOrigemComissao == (int)TipoOrigemComissaoEnum.Produto)
            { // Produto
                var ivp = Domain.Vendas.ItemVendaProdutoRepository.Queryable()
                    .Where(f => f.Comissao.Id == comissaoAReceber.Comissao.Id)
                    .Select(f => new
                    {
                        Descricao = f.Quantidade + "x " + f.EstabelecimentoProduto.Descricao,
                        f.SubTotal,
                    })
                    .FirstOrDefault();

                comissaoRetorno.NomeItem = ivp.Descricao;
                comissaoRetorno.ValorItem = ivp.SubTotal;
            }
            else if ((int)comissaoAReceber.Comissao.TipoOrigemComissao == (int)TipoOrigemComissaoEnum.Servico)
            { // Serviço
                var ht = Domain.Pessoas.HorarioTransacaoRepository.Queryable()
                    .Where(f => f.Transacao.Id == comissaoAReceber.Comissao.Transacao.Id && f.Comissao.Id == comissaoAReceber.Comissao.Id)
                    .Select(f => new
                    {
                        f.Horario.ServicoEstabelecimento.Nome,
                        f.Preco
                    })
                    .FirstOrDefault();

                comissaoRetorno.NomeItem = ht.Nome;
                comissaoRetorno.ValorItem = ht.Preco ?? 0;
            }
            else if (considerarDadosDePacote)
            { //pacote
                var ivp = Domain.Vendas.ItemVendaPacoteRepository.Queryable()
                    .Where(f => f.Comissao.Id == comissaoAReceber.Comissao.Id)
                    .Select(f => new
                    {
                        f.SubTotal,
                        f.PacoteCliente.Nome
                    })
                    .FirstOrDefault();

                comissaoRetorno.NomeItem = ivp.Nome;
                comissaoRetorno.ValorItem = ivp.SubTotal;
            }

            return comissaoRetorno;
        }

        public decimal? ObterTotalDeValeParaPagar(int idEstabelecimento,
            EstabelecimentoProfissional estabelecimentoProfissional, DateTime dataInicio, DateTime dataFim)
        {
            var filtro = new ParametrosFiltrosRelatorio()
            {
                IdEstabelecimento = idEstabelecimento,
                EstabelecimentoProfissional = estabelecimentoProfissional,
                DataInicial = dataInicio,
                DataFinal = dataFim,
            };

            var vales = Domain.Despesas.LancamentoRepository.ListarVales(filtro);
            if (!vales.Any())
                return null;

            return vales.Sum(f => f.Valor);
        }

        public DadosCarteiraPageDTO CarregarTodosOsDados(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, int quantidade)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            int idProfissional = ObterIdProfissional();

            var profissionalTrabalhaComSplit = Domain.Pessoas.EstabelecimentoProfissionalRepository.EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(idProfissional, idEstabelecimento);
            var permiteVerDescontosDetalhado = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.TrinksPro.VisualizarDescontosDetalhados);

            var totaisDosRecebiveis = ObterTotaisDosRecebiveis(filtro, profissionalTrabalhaComSplit, permiteVerDescontosDetalhado, idProfissional);
            var resumos = ObterResumos(idEstabelecimento, profissionalTrabalhaComSplit, permiteVerDescontosDetalhado, totaisDosRecebiveis);

            var listaDeComissoes = UltimasComissoesRecebidas(idEstabelecimento, quantidade, filtro);

            var retorno = new DadosCarteiraPageDTO
            {
                Resumos = resumos,
                ComissoesRecebidas = listaDeComissoes,
                ComissaoPacote = totaisDosRecebiveis.PacoteTotal,
                ComissaoProduto = totaisDosRecebiveis.ProdutoTotal,
                ComissaoServico = totaisDosRecebiveis.ServicoTotal
            };

            return retorno;
        }

        public ResumosDTO ObterResumosTotais(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            int idProfissional = ObterIdProfissional();

            var profissionalTrabalhaComSplit = Domain.Pessoas.EstabelecimentoProfissionalRepository.EstabelecimentoProfissionalEstaUtilizandoSplitPagamento(idProfissional, idEstabelecimento);
            var permiteVerDescontosDetalhado = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissoes.Permissao.TrinksPro.VisualizarDescontosDetalhados);

            var totaisDosRecebiveis = ObterTotaisDosRecebiveis(filtro, profissionalTrabalhaComSplit, permiteVerDescontosDetalhado, idProfissional);
            return ObterResumos(idEstabelecimento, profissionalTrabalhaComSplit, permiteVerDescontosDetalhado, totaisDosRecebiveis);
        }

        private ResumosDTO ObterResumos(int idEstabelecimento, bool profissionalTrabalhaComSplit, bool permiteVerDescontosDetalhado, TotalFaltaReceberDTO totaisDosRecebiveis)
        {
            var usoDeTermos = new TrinksApps.UsoDeTermosNoApp(idEstabelecimento);
            var detalhes = GerarDetalhesDoResumo(profissionalTrabalhaComSplit, permiteVerDescontosDetalhado, usoDeTermos, totaisDosRecebiveis);

            var resumos = new ResumosDTO();
            resumos.Detalhes = detalhes;
            resumos.CalcularTotal(resumos.Detalhes);

            return resumos;
        }

        private List<ResumoDetalhesFaltaReceberDTO> GerarDetalhesDoResumo(bool profissionalTrabalhaComSplit, bool permiteVerDescontosDetalhado, TrinksApps.UsoDeTermosNoApp usoDeTermos, TotalFaltaReceberDTO totaisDosRecebiveis)
        {
            var detalhes = new List<ResumoDetalhesFaltaReceberDTO>();

            if (permiteVerDescontosDetalhado)
            {
                detalhes.Add(new ResumoDetalhesFaltaReceberDTO(usoDeTermos.ObterTermoUsado(TrinksApps.UsoDeTermosNoApp.ChaveEnum.totalEmRecebiveis), totaisDosRecebiveis.TotalComissao, TipoDespesaEnum.Recebivel, TipoDeValorEnum.TotalComissao));
                detalhes.Add(new ResumoDetalhesFaltaReceberDTO("Total em bonificações", totaisDosRecebiveis.BonificacoesTotal, TipoDespesaEnum.Recebivel, TipoDeValorEnum.TotalBonificacao));
                detalhes.Add(new ResumoDetalhesFaltaReceberDTO("Total em gorjetas", totaisDosRecebiveis.GorjetasReceberTotal, TipoDespesaEnum.Recebivel, TipoDeValorEnum.GorjetaAReceber));

                detalhes.Add(new ResumoDetalhesFaltaReceberDTO("Pago em vale/adiantamento", totaisDosRecebiveis.ValesTotal, TipoDespesaEnum.Desconto, TipoDeValorEnum.DescontoValeEAdiantamento));

                if (profissionalTrabalhaComSplit)
                    detalhes.Add(new ResumoDetalhesFaltaReceberDTO("Já foi pago por split", totaisDosRecebiveis.SplitTotal, TipoDespesaEnum.Desconto, TipoDeValorEnum.DescontoSplit));

                detalhes.Add(new ResumoDetalhesFaltaReceberDTO("Produtos que você comprou", totaisDosRecebiveis.DescontoProfissionalTotal, TipoDespesaEnum.Desconto, TipoDeValorEnum.DescontoProdutosComprados));
            }
            else
            {
                detalhes.Add(new ResumoDetalhesFaltaReceberDTO(usoDeTermos.ObterTermoUsado(TrinksApps.UsoDeTermosNoApp.ChaveEnum.totalEmRecebiveis), totaisDosRecebiveis.TotalComissao, TipoDespesaEnum.Recebivel, TipoDeValorEnum.TotalComissao));
                detalhes.Add(new ResumoDetalhesFaltaReceberDTO("Total em descontos", totaisDosRecebiveis.ValesTotal + totaisDosRecebiveis.DescontoProfissionalTotal, TipoDespesaEnum.Desconto, TipoDeValorEnum.DescontoGenerico));
            }

            return detalhes;
        }

        private TotalFaltaReceberDTO ObterTotaisDosRecebiveis(ParametrosFiltrosRelatorio filtro, bool profissionalTrabalhaComSplit, bool permiteVerDescontosDetalhado, int idProfissional)
        {
            var comissoesServico = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeServico(filtro, false);
            var comissoesProduto = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDeProduto(filtro, false);
            var comissoesPacote = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoesDePacote(filtro, false);
            var novoFiltro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(filtro.IdEstabelecimento, filtro.DataInicial, filtro.DataFinal);
            novoFiltro.IdProfissional = idProfissional;

            var descontoVendaProduto = Domain.Financeiro.TransacaoFormaPagamentoParcelaRepository.ListarComDescontoProfissional(novoFiltro);

            var totalDeVales = Domain.Despesas.LancamentoRepository.ListarVales(filtro);
            var totalGorjetas = Domain.Financeiro.GorjetaRepository.ObterTotalNaoPago(filtro);

            var comissoesServicoTotal = comissoesServico.Sum(g => (decimal?)g.Valor) ?? 0;
            var comissoesProdutoTotal = comissoesProduto.Sum(g => (decimal?)g.Valor) ?? 0;
            var comissoesPacoteTotal = comissoesPacote.Sum(g => (decimal?)g.Valor) ?? 0;
            var valorTotalDeVales = totalDeVales.Sum(g => (decimal?)g.Valor) ?? 0;
            var totalDescontoProfissional = descontoVendaProduto.Sum(g => (decimal?)g.Valor) ?? 0;

            var res = new TotalFaltaReceberDTO
            {
                ServicoTotal = comissoesServicoTotal,
                ProdutoTotal = comissoesProdutoTotal,
                DescontoProfissionalTotal = totalDescontoProfissional,
                ValesTotal = valorTotalDeVales,
                PacoteTotal = comissoesPacoteTotal,
                GorjetasReceberTotal = totalGorjetas
            };

            if (permiteVerDescontosDetalhado)
            {
                var totalBonificacoes = Domain.Despesas.LancamentoRepository.ListarBonificacoes(filtro)
                    .Where(f => f.PessoaQueRecebeuOuPagou != null);
                res.BonificacoesTotal = totalBonificacoes.Sum(g => (decimal?)g.Valor) ?? 0;

                if (profissionalTrabalhaComSplit)
                {
                    var totalSplit = Domain.Despesas.LancamentoRepository.ListarSplitsDePagamento(filtro);
                    res.SplitTotal = totalSplit.Sum(g => (decimal?)g.Valor) ?? 0;
                }
            }

            return res;
        }

        private int ObterIdProfissional()
        {
            var conta = Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value);
            var idProfissional = Domain.Pessoas.ProfissionalRepository.Queryable().Where(p => p.PessoaFisica.IdPessoa == conta.Pessoa.IdPessoa).Select(i => i.IdProfissional).FirstOrDefault();
            return idProfissional;
        }
    }
}