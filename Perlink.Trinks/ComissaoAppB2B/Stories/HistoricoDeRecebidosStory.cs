﻿using Castle.ActiveRecord;
using NHibernate.Transform;
using Perlink.DomainInfrastructure.Stories;
using Perlink.Trinks.ComissaoAppB2B.DTO;
using Perlink.Trinks.ExtensionMethods;
using Perlink.Trinks.Permissoes;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.ValoresRecebiveisAppB2B.DTO;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Perlink.Trinks.ComissaoAppB2B.Stories
{

    public class HistoricoDeRecebidosStory : BaseStory, IHistoricoDeRecebidosStory
    {

        public List<ComissoesTotaisDTO> TotalDeComissoesAgrupadasPorDia(int idEstabelecimento, DateTime dataInicio,
            DateTime dataFim)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            var comissoes = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);

            var retorno = comissoes
                .GroupBy(g => g.DataDaComissaoAReceber.Date)
                .Select(g => new ComissoesTotaisDTO()
                {
                    Data = g.Key,
                    Total = g.Sum(c => c.Valor),
                })
                .OrderByDescending(c => c.Data)
                .ToList();

            if (!retorno.Any())
                return null;

            return retorno;
        }

        public List<DataETotalDaComissaoDTO> TotalEDataDeComissoesAgrupadasPorDia(int idEstabelecimento, DateTime dataInicio,
            DateTime dataFim)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            var comissoes = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);

            var retorno = comissoes
                .GroupBy(g => g.DataDaComissaoAReceber.Date)
                .Select(g => new DataETotalDaComissaoDTO()
                {
                    Data = g.Key,
                    Total = g.Sum(c => c.Valor),
                    DiaDaSemanaAbreviado = "(" + g.Key.ToString("ddd", new CultureInfo("pt-BR")) + ")"
                })
                .OrderByDescending(c => c.Data)
                .ToList();

            if (!retorno.Any())
                return null;

            return retorno;
        }

        public List<ComissoesTotaisDTO> TotalDeComissoesAgrupadasPorMes(int idEstabelecimento, DateTime dataInicio, DateTime dataFim)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            var comissoes = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);

            var comissoesAgrupadas = comissoes
                .GroupBy(g => new { Mes = g.DataDaComissaoAReceber.Month, Ano = g.DataDaComissaoAReceber.Year })
                .Select(g => new
                {
                    Ano = g.Key.Ano,
                    Mes = g.Key.Mes,
                    Total = g.Sum(c => c.Valor)
                })
                .ToList();

            if (!comissoesAgrupadas.Any())
                return null;

            return comissoesAgrupadas
                .Select(g => new ComissoesTotaisDTO
                {
                    Data = new DateTime(g.Ano, g.Mes, 1),
                    Total = g.Total
                })
                .OrderByDescending(c => c.Data)
                .ToList();
        }

        public List<ComissaoDTO> ListarComissoesDoDia(int idEstabelecimento, DateTime data, bool considerarDadosDePacote)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, data, data);
            var comissoesValor = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);


            var estabelecimentoTrabalhaComProdutoFracionado = Domain.Pessoas.EstabelecimentoService.EstabelecimentoTrabalhaComProdutoFracionado(idEstabelecimento);

            var comissoesProduto = from cv in comissoesValor
                                   join ivp in Domain.Vendas.ItemVendaProdutoRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                   select new ComissaoDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       IdComissao = cv.Comissao.Id,
                                       DataHoraPagamento = cv.Comissao.Transacao.DataHora,
                                       NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                       Tipo = cv.Comissao.TipoOrigemComissao,
                                       NomeItem = Domain.ComissaoAppB2B.ComissaoService.AdicionarQuantidadeEMedidasNoNome(
                                           estabelecimentoTrabalhaComProdutoFracionado,
                                           ivp.EstabelecimentoProduto.Descricao,
                                           ivp.Quantidade,
                                           ivp.EstabelecimentoProduto.UnidadeMedida.Simbolo,
                                           ivp.EstabelecimentoProduto.MedidasPorUnidade,
                                           ivp.TipoDeQuantidade)
                                   };

            var comissoesServico = from cv in comissoesValor
                                   join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.Comissao.Id
                                   select new ComissaoDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       IdComissao = cv.Comissao.Id,
                                       DataHoraPagamento = cv.Comissao.Transacao.DataHora,
                                       NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                       Tipo = cv.Comissao.TipoOrigemComissao,
                                       NomeItem = ht.Horario.ServicoEstabelecimento.Nome
                                   };

            var comissoesServicoAssistente = from cv in comissoesValor
                                             join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.ComissaoAssistente.Id
                                             select new ComissaoDTO
                                             {
                                                 Id = cv.Id,
                                                 IdTransacao = cv.Comissao.Transacao.Id,
                                                 Valor = cv.Valor,
                                                 IdComissao = cv.Comissao.Id,
                                                 DataHoraPagamento = cv.Comissao.Transacao.DataHora,
                                                 NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                                 Tipo = cv.Comissao.TipoOrigemComissao,
                                                 NomeItem = ht.Horario.ServicoEstabelecimento.Nome
                                             };

            var lista = comissoesProduto.ToList();
            lista.AddRange(comissoesServico);
            lista.AddRange(comissoesServicoAssistente);

            if (considerarDadosDePacote)
            {
                var comissoesPacote = from cv in comissoesValor
                                      join ivp in Domain.Vendas.ItemVendaPacoteRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                      select new ComissaoDTO
                                      {
                                          Id = cv.Id,
                                          IdTransacao = cv.Comissao.Transacao.Id,
                                          Valor = cv.Valor,
                                          IdComissao = cv.Comissao.Id,
                                          DataHoraPagamento = cv.Comissao.Transacao.DataHora,
                                          NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                          Tipo = cv.Comissao.TipoOrigemComissao,
                                          NomeItem = ivp.PacoteCliente.Nome
                                      };

                lista.AddRange(comissoesPacote);
            }

            lista = lista.OrderBy(f => f.DataHoraPagamento).ToList();

            return lista;
        }

        public List<ComissaoComParcelasDTO> ListarComissoesComParcelasDoDia(int idEstabelecimento, DateTime data)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, data, data);
            var comissoesValor = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);

            var estabelecimentoTrabalhaComProdutoFracionado = Domain.Pessoas.EstabelecimentoService.EstabelecimentoTrabalhaComProdutoFracionado(idEstabelecimento);

            var comissoesProduto = from cv in comissoesValor
                                   join ivp in Domain.Vendas.ItemVendaProdutoRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                   let par = cv.TransacaoFormaPagamentoParcela
                                   select new ComissaoComParcelasDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       NumeroDaParcela = par != null ? par.NumeroParcela : 1,
                                       TotalDeParcelas = par != null ? par.TransacaoFormaPagamento.NumeroParcelas : 1,
                                       IdComissao = cv.Comissao.Id,
                                       DataHoraAtendimentoVenda = cv.Comissao.Transacao.DataReferencia,
                                       NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                       Tipo = cv.Comissao.TipoOrigemComissao,
                                       NomeItem = Domain.ComissaoAppB2B.ComissaoService.AdicionarQuantidadeEMedidasNoNome(
                                           estabelecimentoTrabalhaComProdutoFracionado,
                                           ivp.EstabelecimentoProduto.Descricao,
                                           ivp.Quantidade,
                                           ivp.EstabelecimentoProduto.UnidadeMedida.Simbolo,
                                           ivp.EstabelecimentoProduto.MedidasPorUnidade,
                                           ivp.TipoDeQuantidade)
                                   };

            var comissoesServico = from cv in comissoesValor
                                   join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.Comissao.Id
                                   let par = cv.TransacaoFormaPagamentoParcela
                                   select new ComissaoComParcelasDTO
                                   {
                                       Id = cv.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       NumeroDaParcela = par != null ? par.NumeroParcela : 1,
                                       TotalDeParcelas = par != null ? par.TransacaoFormaPagamento.NumeroParcelas : 1,
                                       IdComissao = cv.Comissao.Id,
                                       DataHoraAtendimentoVenda = cv.Comissao.Transacao.DataReferencia,
                                       NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                       Tipo = cv.Comissao.TipoOrigemComissao,
                                       NomeItem = ht.Horario.ServicoEstabelecimento.Nome,
                                   };

            var comissoesServicoAssistente = from cv in comissoesValor
                                             join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.ComissaoAssistente.Id
                                             let par = cv.TransacaoFormaPagamentoParcela
                                             select new ComissaoComParcelasDTO
                                             {
                                                 Id = cv.Id,
                                                 IdTransacao = cv.Comissao.Transacao.Id,
                                                 Valor = cv.Valor,
                                                 NumeroDaParcela = par != null ? par.NumeroParcela : 1,
                                                 TotalDeParcelas = par != null ? par.TransacaoFormaPagamento.NumeroParcelas : 1,
                                                 IdComissao = cv.Comissao.Id,
                                                 DataHoraAtendimentoVenda = cv.Comissao.Transacao.DataReferencia,
                                                 NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                                 Tipo = cv.Comissao.TipoOrigemComissao,
                                                 NomeItem = ht.Horario.ServicoEstabelecimento.Nome
                                             };

            var comissoesPacote = from cv in comissoesValor
                                  join ivp in Domain.Vendas.ItemVendaPacoteRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                  let par = cv.TransacaoFormaPagamentoParcela
                                  select new ComissaoComParcelasDTO
                                  {
                                      Id = cv.Id,
                                      IdTransacao = cv.Comissao.Transacao.Id,
                                      Valor = cv.Valor,
                                      NumeroDaParcela = par != null ? par.NumeroParcela : 1,
                                      TotalDeParcelas = par != null ? par.TransacaoFormaPagamento.NumeroParcelas : 1,
                                      IdComissao = cv.Comissao.Id,
                                      DataHoraAtendimentoVenda = cv.Comissao.Transacao.DataReferencia,
                                      NomeCliente = cv.Comissao.Transacao.PessoaQuePagou.NomeCompleto,
                                      Tipo = cv.Comissao.TipoOrigemComissao,
                                      NomeItem = ivp.PacoteCliente.Nome
                                  };

            var lista = comissoesProduto.ToList();
            lista.AddRange(comissoesServico);
            lista.AddRange(comissoesServicoAssistente);
            lista.AddRange(comissoesPacote);

            lista = lista.OrderBy(f => f.DataHoraAtendimentoVenda).ToList();

            return lista;
        }

        public List<ComissaoRecebidaDTO> ComissoesRecebidasNoPeriodo(int idEstabelecimento, DateTime dataInicio, DateTime dataFim, bool considerarDadosDePacote)
        {
            var filtro = ParametrosFiltrosRelatorio.ObterParaFiltrarComissoes(idEstabelecimento, dataInicio, dataFim);
            var comissoesValor = Domain.Financeiro.ValorDeComissaoAReceberRepository.ListarComissoes(filtro, false);


            var estabelecimentoTrabalhaComProdutoFracionado = Domain.Pessoas.EstabelecimentoService.EstabelecimentoTrabalhaComProdutoFracionado(idEstabelecimento);

            var comissoesProduto = from cv in comissoesValor
                                   join ivp in Domain.Vendas.ItemVendaProdutoRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                   select new ComissaoRecebidaDTO
                                   {
                                       Id = cv.Comissao.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       Data = cv.DataDaComissaoAReceber,
                                       IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                       Nome = Domain.ComissaoAppB2B.ComissaoService.AdicionarQuantidadeEMedidasNoNome(
                                           estabelecimentoTrabalhaComProdutoFracionado,
                                           ivp.EstabelecimentoProduto.Descricao,
                                           ivp.Quantidade,
                                           ivp.EstabelecimentoProduto.UnidadeMedida.Simbolo,
                                           ivp.EstabelecimentoProduto.MedidasPorUnidade,
                                           ivp.TipoDeQuantidade)
                                   };

            var comissoesServico = from cv in comissoesValor
                                   join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.Comissao.Id
                                   select new ComissaoRecebidaDTO
                                   {
                                       Id = cv.Comissao.Id,
                                       IdTransacao = cv.Comissao.Transacao.Id,
                                       Valor = cv.Valor,
                                       Data = cv.DataDaComissaoAReceber,
                                       IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                       Nome = ht.Horario.ServicoEstabelecimento.Nome
                                   };

            var comissoesServicoAssistente = from cv in comissoesValor
                                             join ht in Domain.Pessoas.HorarioTransacaoRepository.Queryable() on cv.Comissao.Id equals ht.ComissaoAssistente.Id
                                             select new ComissaoRecebidaDTO
                                             {
                                                 Id = cv.Comissao.Id,
                                                 IdTransacao = cv.Comissao.Transacao.Id,
                                                 Valor = cv.Valor,
                                                 Data = cv.DataDaComissaoAReceber,
                                                 IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                                 Nome = ht.Horario.ServicoEstabelecimento.Nome
                                             };



            var lista = comissoesProduto.ToList();
            lista.AddRange(comissoesServico);
            lista.AddRange(comissoesServicoAssistente);

            if (considerarDadosDePacote)
            {

                var comissoesPacote = from cv in comissoesValor
                                      join ivp in Domain.Vendas.ItemVendaPacoteRepository.Queryable() on cv.Comissao.Id equals ivp.Comissao.Id
                                      select new ComissaoRecebidaDTO
                                      {
                                          Id = cv.Comissao.Id,
                                          IdTransacao = cv.Comissao.Transacao.Id,
                                          Valor = cv.Valor,
                                          Data = cv.DataDaComissaoAReceber,
                                          IdTipoOrigemComissao = (int)cv.Comissao.TipoOrigemComissao,
                                          Nome = ivp.PacoteCliente.Nome
                                      };
                lista.AddRange(comissoesPacote);
            }

            lista = lista.OrderBy(f => f.Data).ToList();

            return lista;
        }

        public List<LancamentoDTO> LancamentosNoPeriodo(FiltroLancamentosNoPeriodoDTO dto)
        {
            var idPessoaDaContaAutenticada = Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value).Pessoa.IdPessoa;
            var idPessoaEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterIdPessoaDoEstabelecimento(dto.IdEstabelecimento);
            var permiteVerDescontosDetalhado = Domain.WebContext.UsuarioLogadoPossuiPermissao(Permissao.TrinksPro.VisualizarDescontosDetalhados);

            var query = ObterQueryLancamentosFormatada(permiteVerDescontosDetalhado);
            var lancamentos = ObterLancamentosNoPeriodo(dto, idPessoaDaContaAutenticada, idPessoaEstabelecimento, permiteVerDescontosDetalhado, query);

            return lancamentos;
        }

        public List<ProdutosCompradosPorProfissionalDTO> ProdutosCompradosPorProfissional(DateTime data, int idEstabelecimento)
        {
            var idPessoaDaContaAutenticada = Domain.Pessoas.ContaRepository.Load(Domain.WebContext.IdContaAutenticada.Value).Pessoa.IdPessoa;
            var produtosComprados = Domain.Vendas.ItemVendaProdutoRepository.ObterProdutosCompradosPorProfissional(idEstabelecimento, idPessoaDaContaAutenticada, data);
            return produtosComprados;
        }

        private List<LancamentoDTO> ObterLancamentosNoPeriodo(FiltroLancamentosNoPeriodoDTO dto, int idPessoaDaContaAutenticada, int idPessoaEstabelecimento, bool permiteVerDescontosDetalhado, string query)
        {
            var holder = ActiveRecordMediator.GetSessionFactoryHolder();
            var session = holder.CreateSession(typeof(ActiveRecordBase));

            var q = session.CreateSQLQuery(query)
                    .SetParameter("id_pessoa_estabelecimento", idPessoaEstabelecimento)
                    .SetParameter("id_pessoa_recebeu", idPessoaEstabelecimento)
                    .SetParameter("id_pessoa_estabelecimento", idPessoaEstabelecimento)
                    .SetParameter("dt_inicio", dto.DataInicio.Date)
                    .SetParameter("dt_fim", dto.DataFim.AddDays(1).Date)
                    .SetParameter("id_pessoa_comissionada", idPessoaDaContaAutenticada)
                    .SetParameter("registrosCarregados", (dto.Pagina - 1) * dto.RegistrosPorPagina)
                    .SetParameter("registrosPorPagina", dto.RegistrosPorPagina);

            if (permiteVerDescontosDetalhado)
                q.SetParameter("idEstabelecimento", dto.IdEstabelecimento);

            q.SetResultTransformer(Transformers.AliasToBean(typeof(LancamentoDTO)));

            var result = q.List<LancamentoDTO>();
            holder.ReleaseSession(session);

            var lancamentos = new List<LancamentoDTO>();

            if (result.Any())
            {
                TratarNomeDosProdutos(dto, result);
                lancamentos = result.ToList();
            }

            return lancamentos;
        }

        private void TratarNomeDosProdutos(FiltroLancamentosNoPeriodoDTO dto, IList<LancamentoDTO> result)
        {
            if (result.Where(p => p.IdTipoDado == TipoDadoEnum.Produto).Any())
            {
                var estabelecimentoTrabalhaComProdutoFracionado = Domain.Pessoas.EstabelecimentoService.EstabelecimentoTrabalhaComProdutoFracionado(dto.IdEstabelecimento);

                foreach (var item in result.Where(p => p.IdTipoDado == TipoDadoEnum.Produto))
                {
                    item.Nome = Domain.ComissaoAppB2B.ComissaoService.AdicionarQuantidadeEMedidasNoNome(estabelecimentoTrabalhaComProdutoFracionado, item.Descricao, item.Quantidade, item.Simbolo, item.MedidasPorUnidade, item.TipoDeQuantidade);
                    item.Descricao = null;
                }
            }
        }

        private string ObterQueryLancamentosFormatada(bool permiteVerDescontosDetalhado)
        {
            var queryProduto = @"
                           select 1 as IdTipoDado,
                           cvar.valor_comissao_a_pagar as Valor,
                           cvar.dt_comissao_receber    as Data,
                           null                          as Nome,
                           CAST(c.eh_comissao_split as bit) as EhComissaoSplit,
                    	   ep.descricao				   as Descricao,
                           vi.quantidade               as Quantidade,
                           um.simbolo                  as Simbolo,
                           CAST(ep.medidas_por_unidade as INTEGER)      as MedidasPorUnidade,
						   vi.tipo_quantidade          as TipoDeQuantidade,
                           1                           as Tipo
                        from   DBO.Comissao_Valor_A_Receber cvar
                               inner join DBO.Comissao c
                                 on cvar.id_comissao = c.id_comissao
                               inner join DBO.Transacao t
                                 on c.id_transacao = t.id_transacao,
                               Venda_Item vi
                               left outer join Estabelecimento_Produto ep
                                 on vi.id_estabelecimento_produto = ep.id_estabelecimento_produto
                               left outer join Unidades_Medida um
                                 on ep.id_unidade_medida = um.id
                        where  vi.type = 'PROD'
                               and cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and cvar.ativo = 1
                               and t.id_pessoa_recebeu = :id_pessoa_recebeu
                               and cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and c.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and t.id_transacao_tipo = 1
                               and not (t.id_transacao_que_estornou_esta is not null)
                               and cvar.dt_comissao_receber >= :dt_inicio
                               and cvar.dt_comissao_receber <= :dt_fim
                               and c.id_pessoa_comissionada = :id_pessoa_comissionada
                               and vi.id_comissao = cvar.id_comissao";

            var queryServico = @" select 2 as IdTipoDado,
                           cvar.valor_comissao_a_pagar    as Valor,
                           cvar.dt_comissao_receber       as Data,
                           se.nm_servico_estabelecimento  as Nome,
                          CAST(c.eh_comissao_split as bit) as EhComissaoSplit,
                    	   null			                  as Descricao,
                           0                           as Quantidade,
                           null                           as Simbolo,
                           0                           as MedidasPorUnidade,
						   0                           as TipoDeQuantidade ,
                            1                           as Tipo
                    from   DBO.Comissao_Valor_A_Receber cvar
                               inner join DBO.Comissao c
                                 on cvar.id_comissao = c.id_comissao
                               inner join DBO.Transacao t
                                 on c.id_transacao = t.id_transacao,
                               DBO.Horario_Transacao ht
                               left outer join Horario h
                                 on ht.id_horario = h.id_horario
                               left outer join DBO.Servico_Estabelecimento se
                                 on h.id_servico_estabelecimento = se.id_servico_estabelecimento
                        where  cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and cvar.ativo = 1
                               and t.id_pessoa_recebeu = :id_pessoa_recebeu
                               and cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and c.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and t.id_transacao_tipo = 1
                               and not (t.id_transacao_que_estornou_esta is not null)
                               and cvar.dt_comissao_receber >= :dt_inicio
                               and cvar.dt_comissao_receber <= :dt_fim
                               and c.id_pessoa_comissionada = :id_pessoa_comissionada
                               and ht.id_comissao = cvar.id_comissao";

            var queryServicoAssistente = @"select 2 as IdTipoDado,
                           cvar.valor_comissao_a_pagar     as Valor,
                           cvar.dt_comissao_receber        as Data,
                           se.nm_servico_estabelecimento   as Nome,
                           CAST(c.eh_comissao_split as bit) as EhComissaoSplit,
                    	   null			                  as Descricao,
                           0                           as Quantidade,
                           null                           as Simbolo,
                           0                           as MedidasPorUnidade,
						   0                           as TipoDeQuantidade,  
                           1                           as Tipo
                   from   DBO.Comissao_Valor_A_Receber cvar
                               inner join DBO.Comissao c
                                 on cvar.id_comissao = c.id_comissao
                               inner join DBO.Transacao t
                                 on c.id_transacao = t.id_transacao,
                               DBO.Horario_Transacao ht
                               left outer join Horario h
                                 on ht.id_horario = h.id_horario
                               left outer join DBO.Servico_Estabelecimento se
                                 on h.id_servico_estabelecimento = se.id_servico_estabelecimento
                        where  cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and cvar.ativo = 1
                               and t.id_pessoa_recebeu = :id_pessoa_recebeu
                               and cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and c.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and t.id_transacao_tipo = 1
                               and not (t.id_transacao_que_estornou_esta is not null)
                               and cvar.dt_comissao_receber >= :dt_inicio
                               and cvar.dt_comissao_receber <= :dt_fim
                               and c.id_pessoa_comissionada = :id_pessoa_comissionada
                               and ht.id_comissao_assistente = cvar.id_comissao";

            var queryPacote = @"select 3 as IdTipoDado,
                           cvar.valor_comissao_a_pagar as Valor,
                           cvar.dt_comissao_receber    as Data,
                           CAST(pc.nome COLLATE SQL_Latin1_General_CP1_CI_AS as varchar(200)) as Nome,
                           CAST(c.eh_comissao_split as bit) as EhComissaoSplit,
                    	   null			                  as Descricao,
                           0                           as Quantidade,
                           null                           as Simbolo,
                           0                           as MedidasPorUnidade,
						   0                           as TipoDeQuantidade,
                           1                           as Tipo
                    from   DBO.Comissao_Valor_A_Receber cvar
                               inner join DBO.Comissao c
                                 on cvar.id_comissao = c.id_comissao
                               inner join DBO.Transacao t
                                 on c.id_transacao = t.id_transacao,
                               Venda_Item vi
                               left outer join Pacote_Cliente pc
                                 on vi.id_pacote_cliente = pc.id_pacote_cliente
                        where  vi.type = 'PAC'
                               and cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and cvar.ativo = 1
                               and t.id_pessoa_recebeu = :id_pessoa_recebeu
                               and cvar.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and c.id_pessoa_estabelecimento = :id_pessoa_estabelecimento
                               and t.id_transacao_tipo = 1
                               and not (t.id_transacao_que_estornou_esta is not null)
                               and cvar.dt_comissao_receber >= :dt_inicio
                               and cvar.dt_comissao_receber <= :dt_fim
                               and c.id_pessoa_comissionada = :id_pessoa_comissionada
                               and vi.id_comissao = cvar.id_comissao";


            var query = $"{queryProduto} UNION ALL {queryServico} UNION ALL {queryServicoAssistente} UNION ALL {queryPacote}";


            if (permiteVerDescontosDetalhado)
            {

                query = query + @"
                    UNION ALL

                    select 
	                    7 as IdTipoDado,
                        g.valor as Valor,
                        t.data_hora_transacao as Data,
                        concat('Gorjeta deixada por ', pf.nome_completo) COLLATE SQL_Latin1_General_CP1_CI_AS as Nome,
                        cast(0 as bit) as EhComissaoSplit,
                        null as Descricao,
                        1 as Quantidade,
	                    null as Simbolo,
	                    0 as MedidasPorUnidade,
	                    0 as TipoDeQuantidade,
                        5 as Tipo
                    from
	                    Gorjeta g
	                    inner join Transacao t on g.id_transacao = t.id_transacao
	                    inner join Pessoa_Fisica pf on t.id_pessoa_pagou = pf.id_pessoa
	                    inner join Profissional pro on g.id_profissional = pro.id_profissional
                    where 
	                    g.ativo = 1
	                    and g.id_estabelecimento = :idEstabelecimento
	                    and t.data_hora_transacao >= :dt_inicio
	                    and t.data_hora_transacao <= :dt_fim
	                    and t.id_transacao_tipo = 1
	                    and t.id_transacao_que_estornou_esta is null
                        and pro.id_pessoa = :id_pessoa_comissionada

                    UNION ALL

                    select 4 as IdTipoDado,
                           fl.valor_lancamento                as Valor,
                           dateadd(dd, 0, datediff(dd, 0, fl.dt_pagamento)) as Data,
                    	   'Bonificação' COLLATE SQL_Latin1_General_CP1_CI_AS as Nome,																																		
                    	   CAST(0 as bit)									as EhComissaoSplit,
                    	   null								   as Descricao,
                           0                                as Quantidade,
                           null                  as Simbolo,
                           0                        as MedidasPorUnidade,
						   0                        as TipoDeQuantidade,
                           2                           as Tipo
                    from   FINC_Lancamento fl
                           inner join FINC_LANCAMENTO_CATEGORIA flc
                             on fl.id_finc_lancamento_categoria = flc.id_finc_lancamento_categoria
                    where  fl.ativo = 1
                           and flc.id_finc_lancamento_categoria_padrao = 21
                           and flc.id_estabelecimento = :idEstabelecimento
                           and fl.id_estabelecimento = :idEstabelecimento
                           and fl.id_finc_lancamento_status_pagamento = 1
                           and fl.id_pessoa_de_quem_recebeu_ou_pagou = :id_pessoa_comissionada
                           and (fl.dt_pagamento is not null)
                           and fl.dt_pagamento >= :dt_inicio
                           and fl.dt_pagamento <= :dt_fim
                           and (fl.id_pessoa_de_quem_recebeu_ou_pagou is not null)
                    
                    UNION ALL
                    
                    select 6 as IdTipoDado,
                           SUM(tp.valor) as Valor,
                           tp.data_pagamento as Data,
                    	   'Produtos que você comprou' COLLATE SQL_Latin1_General_CP1_CI_AS as Nome,
                    	   CAST(0 as bit) as EhComissaoSplit,
						   null	as Descricao,
                    	   0 as Quantidade,
                           null as Simbolo,
                           0 as MedidasPorUnidade,
						   0 as TipoDeQuantidade,
                           4 as Tipo
                    from   Transacao_Parcela tp
                           inner join Transacao__Forma_Pagamento transacaof0_
                             on tp.id_transacao__forma_pagamento = transacaof0_.id_transacao__forma_pagamento
                           inner join Forma_Pagamento formapagam1_
                             on transacaof0_.id_forma_pagamento = formapagam1_.id_forma_pagamento
                           inner join DBO.Transacao transacao2_
                             on transacaof0_.id_transacao = transacao2_.id_transacao
                           inner join Venda v
                             on transacao2_.id_transacao = v.id_transacao
                    where  not (formapagam1_.exibir_transacao_paga_nos_relatorios = 1)
                           and transacao2_.id_pessoa_recebeu = :id_pessoa_estabelecimento
                           and transacao2_.id_transacao_tipo = 1
                           and transacao2_.id_pessoa_pagou = :id_pessoa_comissionada
                           and (transacao2_.id_transacao_que_estornou_esta is null)
                           and tp.data_pagamento >= :dt_inicio
                           and tp.data_pagamento < :dt_fim
                    group by tp.data_pagamento
                    
                    UNION ALL
                    
                    select 5 as IdTipoDado,
                           fl.valor_lancamento as Valor,
                           fl.dt_pagamento     as Data,
                    	   'Retirada de vale'			 as Nome,
                    	   CAST(0 as bit)							 as EhComissaoSplit,
                          fl.ds_lancamento COLLATE SQL_Latin1_General_CP1_CI_AS  as Descricao,
                           0                            as Quantidade,
                           null                         as Simbolo,
                           0                            as MedidasPorUnidade,
						   0                            as TipoDeQuantidade,
                            3                           as Tipo
                    from   FINC_Lancamento fl
                           inner join FINC_LANCAMENTO_CATEGORIA flc
                             on fl.id_finc_lancamento_categoria = flc.id_finc_lancamento_categoria
                    where  fl.ativo = 1
                           and flc.id_finc_lancamento_categoria_padrao = 1
                           and flc.id_estabelecimento = :idEstabelecimento
                           and fl.id_estabelecimento = :idEstabelecimento
                           and fl.id_finc_lancamento_status_pagamento = 1
                           and fl.id_pessoa_de_quem_recebeu_ou_pagou = :id_pessoa_comissionada
                           and (fl.dt_pagamento is not null)
                           and fl.dt_pagamento >= :dt_inicio
                           and fl.dt_pagamento <= :dt_fim";
            }

            var queryBase = @"select * 
                              from ({0}) as Lancamentos 
                              order by Lancamentos.Data
                              offset :registrosCarregados rows
                              fetch next :registrosPorPagina row only";

            var queryFinal = string.Format(queryBase, query);
            return queryFinal;
        }
    }
}