using Elmah;
using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Shared.Auditing;
using Perlink.Trinks.Compromissos.DTO;
using Perlink.Trinks.ControleDeFuncionalidades.ObjetosDeValor;
using Perlink.Trinks.Exceptions;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.Pagamentos.DTO;
using Perlink.Trinks.Pagamentos.Enums;
using Perlink.Trinks.PagamentosAntecipados.DTO;
using Perlink.Trinks.PagamentosAntecipados.Enums;
using Perlink.Trinks.PagamentosAntecipados.Utils;
using Perlink.Trinks.PagamentosOnlineNoTrinks.Exceptions;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Specifications;
using Perlink.Trinks.Vendas;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentosAntecipados.Services
{

    public class PagamentoOnlineAntecipadoService : BaseService, IPagamentoOnlineAntecipadoService
    {

        private Conta ObterContaDoUsuarioLogado()
        {
            if (string.IsNullOrEmpty(Domain.WebContext.EmailDoUsuarioAutenticado))
                return null;

            return Domain.Pessoas.ContaRepository.ObterContaPorEmail(Domain.WebContext.EmailDoUsuarioAutenticado);
        }

        private Conta ObterContaObrigatoriaDoUsuarioLogado()
            => ObterContaDoUsuarioLogado() ?? throw new UnauthorizedAccessException();

        [TransactionInitRequired]
        public async Task<Resultado<DTO.DadosDoPagamentoRealizadoDTO>> RealizarPagamentoOnlineAntecipado(NovoPagamentoOnlineAntecipadoDTO dadosDoPagamento)
        {
            var conta = ObterContaObrigatoriaDoUsuarioLogado();

            var resultadoValidacao = ValidarConsistenciaDosDadosDoPagamentoComOBanco(dadosDoPagamento);
            if (resultadoValidacao.Falha)
                return Resultado.Falhar<DTO.DadosDoPagamentoRealizadoDTO>(resultadoValidacao.Erro);

            var dadosAtualizadosDoPagamento = resultadoValidacao.Valor;
            var pagamento = CriarESalvarPagamentoAntecipadoPendente(conta, dadosDoPagamento, dadosAtualizadosDoPagamento);

            var dtoDePagamentoOnline = GerarDtoDePagamentoOnline(conta, dadosDoPagamento, dadosAtualizadosDoPagamento);
            var resultadoDoPagamento = await Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService
                .RealizarPagamentoOnlineNoEstabelecimento(dadosAtualizadosDoPagamento.IdEstabelecimento, dtoDePagamentoOnline);

            if (resultadoDoPagamento.PagamentoFoiRealizado)
            {
                pagamento.IndicarQueFoiPagoComSucesso(resultadoDoPagamento.Pagamento);
            }
            else
            {
                pagamento.IndicarQueTeveProblemasNoPagamento(resultadoDoPagamento.Pagamento);
            }

            Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.Update(pagamento);

            if (!resultadoDoPagamento.PagamentoFoiRealizado)
                return Resultado.Ok(new DTO.DadosDoPagamentoRealizadoDTO() { PagamentoRealizado = false });

            TentarAtualizarStatusDosHorariosAssociados(pagamento);
            await TentarRealizarTransacaoDePagamentoOnlineAntecipadoAsync(pagamento);
            TentarNotificarClienteQuePagamentoFoiRecebebido(pagamento);

            return Resultado.Ok(new DTO.DadosDoPagamentoRealizadoDTO()
            {
                PagamentoRealizado = resultadoDoPagamento.PagamentoFoiRealizado,
                DataPagamento = resultadoDoPagamento.PagamentoFoiRealizado ? resultadoDoPagamento.DataHoraPagamento : null,
            });
        }

        private PagamentoAntecipado CriarESalvarPagamentoAntecipadoPendente(Conta conta, NovoPagamentoOnlineAntecipadoDTO dadosDoPagamento, DadosParaPagamentoDeServicosDTO dadosAtualizadosDoPagamento)
        {
            var pagamento = new PagamentoAntecipado()
            {
                IdEstabelecimento = dadosAtualizadosDoPagamento.IdEstabelecimento,
                IdPessoaDoCliente = conta.Pessoa.IdPessoa,
                ValorTotal = dadosAtualizadosDoPagamento.ValorTotal,
                DadosPesquisados = new DadosPesquisadosParaPagamento(
                    idHorario: dadosDoPagamento.FiltroPagamento.IdAgendamento,
                    data: dadosDoPagamento.FiltroPagamento.DataAgendamento,
                    idEstabelecimento: dadosDoPagamento.FiltroPagamento.IdEstabelecimento
                )
            };

            pagamento.Itens = new List<ItemPagamentoAntecipado>(dadosAtualizadosDoPagamento.Servicos.Select(x => new ItemPagamentoHorario()
            {
                Horario = Domain.Pessoas.HorarioRepository.ObterPorId(x.IdAgendamento),
                NomeItem = x.NomeServico,
                DataDeExpiracaoDosBeneficios = x.DataExpiracaoBeneficios,
                PercentualDeDesconto = x.PercentualDeDesconto,
                ValorComDesconto = x.ValorComDesconto,
                ValorOriginal = x.ValorOriginal,
                PagamentoAntecipado = pagamento
            }));

            pagamento.Beneficios = new BeneficiosPagamento()
            {
                DisponivelAoCliente = dadosAtualizadosDoPagamento.BeneficiosDisponiveis,
                PercentualDeDesconto = dadosAtualizadosDoPagamento.Beneficios?.PercentualDescontoPorServico,
                QuantasHorasAntesPodeAgendarComBeneficios = dadosAtualizadosDoPagamento.Beneficios.QuantasHorasAntesPodeAgendarComBeneficios,
                ProgramaDeFidelidade = new BeneficiosDoProgramaDeFidelidade(
                    tipoDeBeneficio: dadosAtualizadosDoPagamento.Beneficios.TipoDeBeneficioDoProgramaDeFidelidade,
                    quantidadePontos: dadosAtualizadosDoPagamento.Beneficios.QuantidadePontosDeFidelidade,
                    valorPorPontos: dadosAtualizadosDoPagamento.Beneficios.ValorPorPontosDeFidelidade
                ),
                PagamentoAntecipado = pagamento
            };

            Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.SaveNew(pagamento);

            return pagamento;
        }

        public DadosParaPagamentoDeServicosDTO ObterDadosParaPagamentoDeServicos(FiltroParaObterDadosDePagamentoDTO filtro, int? idFranquia, bool ehLinkDePagamento = false)
        {
            //if (filtro.IdEstabelecimento.HasValue || filtro.DataAgendamento.HasValue)
            //    throw new BusinessException("Não é permitido pagamento por estabelecimento/data.");

            if (!((filtro.IdAgendamento.HasValue && !filtro.IdEstabelecimento.HasValue && !filtro.DataAgendamento.HasValue) ||
                 (!filtro.IdAgendamento.HasValue && filtro.IdEstabelecimento.HasValue && filtro.DataAgendamento.HasValue)))
                throw new InvalidOperationException("Os parâmetros informados não são suficientes para esta operação.");

            var contaDoUsuarioLogado = ObterContaObrigatoriaDoUsuarioLogado();

            var idCliente = Domain.Pessoas.ClienteRepository.ObterIdPorPessoaFisica(contaDoUsuarioLogado.Pessoa.IdPessoa);
            if (!idCliente.HasValue)
            {
                LogService<PagamentoOnlineAntecipadoService>.Error(String.Format("Cliente não localizado a partir da pessoa física logada (Id: {0}).", contaDoUsuarioLogado.Pessoa.IdPessoa));
                throw new BusinessException("Cliente não localizado.");
            }

            var dadosServicos = Domain.Compromissos.MeusCompromissosService
                .ListarDadosDeServicosParaPagamento(idCliente.Value, filtro, idFranquia);
            var idEstabelecimento = dadosServicos.Select(c => c.IdEstabelecimento).FirstOrDefault();

            bool permitePagarAgendamento = GrupoDeAgendamentosDeEstabelecimentoPermitePagamentoOnline(
                idEstabelecimento: idEstabelecimento,
                agendamentos: dadosServicos,
                cacheDeServicosComPagamentoOnline: null,
                ehLinkDePagamento: ehLinkDePagamento
            );

            if (!permitePagarAgendamento)
                throw new AgendamentoNaoPermitePagamentoOnlineException();

            var valorTotalDoPagamento = dadosServicos.Sum(x => x.Valor);
            var beneficiosOferecidosPeloEstabelecimento = ObterBeneficiosOferecidosPeloEstabelecimento(idEstabelecimento);
            var dataFuturaSpecification = new EhDataFuturaSpecification();

            var servicosParaPagamento = dadosServicos.Select(s =>
            {
                DateTime dataDeExpiracaoDosBeneficios = CalcularDataDeExpiracaoDeBeneficiosDoPagamentoOnline(idEstabelecimento, s.DataInicio);
                bool beneficioEstaDisponivel = dataFuturaSpecification.IsSatisfiedBy(dataDeExpiracaoDosBeneficios);

                return new ServicoParaPagamentoDTO()
                {
                    Data = s.DataInicio,
                    IdAgendamento = s.IdHorario,
                    IdServicoEstabelecimento = s.IdServicoEstabelecimento,
                    NomeEstabelecimento = s.NomeExibicaoEstabelecimento,
                    NomeServico = s.NomeServico,
                    PercentualDeDesconto = beneficioEstaDisponivel ? beneficiosOferecidosPeloEstabelecimento.PercentualDescontoPorServico : null,
                    ValorOriginal = s.Valor,
                    ValorComDesconto = beneficioEstaDisponivel && beneficiosOferecidosPeloEstabelecimento.PercentualDescontoPorServico.HasValue ?
                            s.Valor - (s.Valor * (beneficiosOferecidosPeloEstabelecimento.PercentualDescontoPorServico.Value / 100.0m)) :
                            (decimal?)null,
                    DataExpiracaoBeneficios = dataDeExpiracaoDosBeneficios
                };
            }).ToList();

            var dadosParaPagamento = new DadosParaPagamentoDeServicosDTO(
                idEstabelecimento: idEstabelecimento,
                servicos: servicosParaPagamento,
                cartoesDeCredito: Domain.Pagamentos.CartaoApplicationService.ObterCartoesDoUsuarioAutenticado(),
                beneficios: new BeneficiosPagamentoOnlineDTO()
                {
                    PercentualDescontoPorServico = beneficiosOferecidosPeloEstabelecimento.PercentualDescontoPorServico,
                    QuantasHorasAntesPodeAgendarComBeneficios = beneficiosOferecidosPeloEstabelecimento.QuantasHorasAntesPodeAgendarComBeneficios,
                    QuantidadePontosDeFidelidade = beneficiosOferecidosPeloEstabelecimento.ProgramaDeFidelidade.QuantidadePontosDeFidelidade,
                    TipoDeBeneficioDoProgramaDeFidelidade = beneficiosOferecidosPeloEstabelecimento.ProgramaDeFidelidade.TipoDeBeneficio,
                    ValorPorPontosDeFidelidade = beneficiosOferecidosPeloEstabelecimento.ProgramaDeFidelidade.ValorPorPontosDeFidelidade
                },
                ultimaTransacaoRealizada: Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterDadosDaUltimaTransacaoRealizadaPorConta(contaDoUsuarioLogado.IdConta)
            );

            return dadosParaPagamento;
        }

        private Resultado<DadosParaPagamentoDeServicosDTO> ValidarConsistenciaDosDadosDoPagamentoComOBanco(NovoPagamentoOnlineAntecipadoDTO dadosDoPagamento)
        {
            var cartoes = Domain.Pagamentos.CartaoApplicationService.ObterCartoesDoUsuarioAutenticado();
            if (!cartoes.Any(c => c.IdCartao == dadosDoPagamento.IdCartao))
                return GerarResultadoDeInconsistenciaNoPagamentoOnline("O cartão selecionado não foi encontrado.");

            var dadosAtualizadosDoPagamento = ObterDadosParaPagamentoDeServicos(dadosDoPagamento.FiltroPagamento, idFranquia: null);

            if (!dadosAtualizadosDoPagamento.Servicos.Any())
                return GerarResultadoDeInconsistenciaNoPagamentoOnline("Não há serviço a ser pago.");

            if (dadosDoPagamento.BeneficiosDisponiveis != dadosAtualizadosDoPagamento.BeneficiosDisponiveis)
                return GerarResultadoDeInconsistenciaNoPagamentoOnline("Disponibilidade de benefícios modificada.");

            if (dadosDoPagamento.ValorTotal != dadosAtualizadosDoPagamento.ValorTotal)
                return GerarResultadoDeInconsistenciaNoPagamentoOnline("Valor a pagar calculado difere do valor apresentado ao usuário.");

            var idsDosHorariosAtualizados = dadosAtualizadosDoPagamento.Servicos.Select(x => x.IdAgendamento).ToList();
            bool agendamentosPagosCorrespondemAosDadosAtualizados =
                dadosDoPagamento.AgendamentosSendoPago.All(x => idsDosHorariosAtualizados.Contains(x)) &&
                idsDosHorariosAtualizados.All(x => dadosDoPagamento.AgendamentosSendoPago.Contains(x));

            if (!agendamentosPagosCorrespondemAosDadosAtualizados)
                return GerarResultadoDeInconsistenciaNoPagamentoOnline("Serviços a pagar diferem da lista de serviços apresentada ao usuário.");

            return Resultado.Ok(dadosAtualizadosDoPagamento);
        }

        private BeneficiosEstabelecimento ObterBeneficiosOferecidosPeloEstabelecimento(int idEstabelecimento)
        {
            return Domain.PagamentosAntecipados.BeneficiosEstabelecimentoRepository
                .StatelessQueryable()
                .Where(x => x.IdEstabelecimento == idEstabelecimento)
                .SingleOrDefault();
        }

        private Resultado<DadosParaPagamentoDeServicosDTO> GerarResultadoDeInconsistenciaNoPagamentoOnline(string mensagem)
        {
            return Resultado.Falhar<DadosParaPagamentoDeServicosDTO>("Inconsistência no Pagamento Online. " + mensagem);
        }

        private NovoPagamentoOnlineDTO GerarDtoDePagamentoOnline(Conta conta, NovoPagamentoOnlineAntecipadoDTO dadosDoPagamento, DadosParaPagamentoDeServicosDTO dadosAtualizadosDoPagamento)
        {
            var dadosAdicionais = new Dictionary<string, string>() {
                    { "idAgendamento", dadosDoPagamento.FiltroPagamento.IdAgendamento.ToString() },
                    { "idEstabelecimento", dadosDoPagamento.FiltroPagamento.IdEstabelecimento.ToString() },
                    { "dataAgendamento", dadosDoPagamento.FiltroPagamento.DataAgendamento.ToString() },
                    { "idConta", conta.IdConta.ToString() } };

            var itensPagos = new List<ItemPagamentoDTO>(dadosAtualizadosDoPagamento.Servicos.Select(x => new ItemPagamentoDTO()
            {
                IdItemPagamento = x.IdServicoEstabelecimento,
                NomeItem = x.NomeServico,
                DataDeExpiracaoDosBeneficios = x.DataExpiracaoBeneficios,
                PercentualDeDesconto = x.PercentualDeDesconto,
                ValorComDesconto = x.ValorComDesconto,
                ValorOriginal = x.ValorOriginal
            }));

            return new NovoPagamentoOnlineDTO()
            {
                IdCartao = dadosDoPagamento.IdCartao,
                ValorTotal = dadosAtualizadosDoPagamento.ValorTotal,
                NomeDoRecebedor = dadosAtualizadosDoPagamento.Servicos.First().NomeEstabelecimento,
                ItensPagos = itensPagos,
                DadosAdicionais = dadosAdicionais,
            };
        }

        private void TentarAtualizarStatusDosHorariosAssociados(PagamentoAntecipado pagamento)
        {
            try
            {
                foreach (var item in pagamento.Itens.OfType<ItemPagamentoHorario>())
                {
                    var horario = item.Horario;
                    horario.FoiPagoAntecipadamente = true;
                    Domain.Pessoas.HorarioRepository.Update(horario);
                }
            }
            catch
            {
                ErrorSignal.FromCurrentContext().Raise(new BusinessException("Falha ao marcar agendamentos como pago antecipadamente."));
            }
        }

        private async Task TentarRealizarTransacaoDePagamentoOnlineAntecipadoAsync(PagamentoAntecipado pagamento)
        {
            try
            {
                await Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.RealizarTransacaoDePagamentoOnlineAntecipado(pagamento);
            }
            catch
            {
                ErrorSignal.FromCurrentContext().Raise(new BusinessException("Falha ao criar transação de crédito do pagamento online."));
            }
        }

        private void TentarNotificarClienteQuePagamentoFoiRecebebido(PagamentoAntecipado pagamento)
        {
            try
            {
                Domain.PagamentosAntecipados.NotificacaoDePagamentoAntecipadoOnlineService.NotificarClienteQuePagamentoFoiRecebebido(pagamento);
            }
            catch (Exception e)
            {
                ErrorSignal.FromCurrentContext().Raise(e);
            }
        }

        #region RealizarTransacaoDePagamentoOnlineAntecipado

        [TransactionInitRequired]
        public async Task RealizarTransacaoDePagamentoOnlineAntecipado(PagamentoAntecipado pagamento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(pagamento.IdEstabelecimento);
            var pessoaDoClienteQuePagou = Domain.Pessoas.PessoaFisicaRepository.ObterPorId(pagamento.IdPessoaDoCliente);
            Venda venda = Domain.Vendas.VendaRepository.Factory.CreateParaRealizarTransacao(
                estabelecimento: estabelecimento,
                pessoaQuePagou: pessoaDoClienteQuePagou,
                dataHoraDaTransacao: Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterDataHoraTransacaoCorreta(pagamento.DataHoraPagamento),
                dataReferencia: Calendario.Agora(),
                pessoaQueRealizou: pessoaDoClienteQuePagou,
                tipoTransacao: TipoTransacaoEnum.Pagamento,
                comentarioFechamentoConta: "",
                comentarioEstorno: "");


            // TODO:duvida Colocar a opção de ter pagamento online ou pagamento online por link para reutilização do código
            venda.Transacao.FormasPagamento.Add(GerarFormaPagamentoParaTransacaoDePagamentoOnlineAntecipado(pagamento, venda.Transacao));

            Domain.Financeiro.TransacaoService
                .ColocarCompraDeCreditoNaTransacao(transacao: venda.Transacao, /* TODO:duvida pro Paulo */ formaPagamentoPrePago: FormaPagamentoPrePagoEnum.CreditoDePagamentoOnline, valorDoCredito: pagamento.ValorTotal);

            var checkoutDTO = new Financeiro.DTO.CheckoutDTO(venda.Transacao, venda.Transacao.PessoaQuePagou)
            {
                Venda = venda,
                EmitirNFC = false,
                IdTransacaoPOS = null,
                ControlarCaixaSeNecessario = false,
                ControlarPagamentoPOS = false,
                PagamentoAntecipado = pagamento
            };

            var resultadoCheckOut = await Domain.Financeiro.TransacaoService.RealizarCheckOut(checkoutDTO);

            pagamento.Transacao = resultadoCheckOut.Transacao;
            Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.Update(pagamento);

            pagamento.PagamentoOnline.Transacao = resultadoCheckOut.Transacao;
            Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Update(pagamento.PagamentoOnline);

            MarcarHorariosComoConfirmadosEGerarHistoricoDePagamentoAntecipado(pagamento);
        }

        private void MarcarHorariosComoConfirmadosEGerarHistoricoDePagamentoAntecipado(PagamentoAntecipado pagamentoAntecipado)
        {
            foreach (var itemHorario in pagamentoAntecipado.Itens.Where(i => i is ItemPagamentoHorario))
            {
                var horario = ((ItemPagamentoHorario)itemHorario).Horario;

                if (horario.Status != StatusHorarioEnum.Confirmado)
                    Domain.Pessoas.AgendaService.AlterarStatusAgendamento(((ItemPagamentoHorario)itemHorario).Horario.Id, (int)StatusHorarioEnum.Confirmado, null, null);
                else
                {
                    var horarioComLoad = Domain.Pessoas.HorarioRepository.Load(horario.Id); // o horário está sendo carregado novamente aqui porque a sessão está sendo fechada pelo ManterAgendamento. Isso gera erro ao obter entidades como o estabelecimento do horário.
                    Domain.Pessoas.AgendaService.ManterAgendamentoNoTransaction(horarioComLoad, null, true, true, false, modoPAT: false, alteradoPorAgenda: false);
                }
            }
        }

        private TransacaoFormaPagamento GerarFormaPagamentoParaTransacaoDePagamentoOnlineAntecipado(PagamentoAntecipado pagamento, Transacao transacao)
        {
            var transacaoFormaPagamentoDePagamentoOnline = Domain.Financeiro.TransacaoService.MontarTransacaoFormaPagamento(transacao, (int)FormaPagamentoEnum.PagamentoOnline, pagamento.IdEstabelecimento, pagamento.ValorTotal);

            return transacaoFormaPagamentoDePagamentoOnline;
        }

        #endregion RealizarTransacaoDePagamentoOnlineAntecipado

        public bool TransacaoEhDePagamentoOnline(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnline);
        }

        public bool TransacaoEhDePagamentoOnlinePorLink(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnlinePorLink);
        }

        public bool TransacaoEhClubeDeAssinaturasPorLink(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.ClubeDeAssinaturaPorLink);
        }
        public bool TransacaoEhPagamentoOnlineHotsite(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable(true).Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnlineHotsite);
        }

        public bool TransacaoEhDeClubeDeAssinatura(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.ClubeDeAssinatura);
        }

        public bool TransacaoEhConsumoDePagamentoOnline(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnline && tf.ValorPago > 0);//TODO:duvida - tem que verificar se esses sinais estão certos
        }

        public bool TransacaoEhDeCreditoPagamentoOnline(int idTransacao)
        {
            return Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable().Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.CreditoDePagamentoOnline);
        }

        public bool TransacaoEhConsumoDePagamentoOnlineHotsite(int idTransacao)
            => Domain.Financeiro.TransacaoFormaPagamentoRepository.TransacaoEhConsumoDePagamentoOnlineHotsite(idTransacao);

        public bool TransacaoEhPagarmeCredito(int idTransacao)
            => Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable(true).Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.PagarmeCredito);

        public bool TransacaoEhPagarmePix(int idTransacao)
            => Domain.Financeiro.TransacaoFormaPagamentoRepository.Queryable(true).Any(tf => tf.Transacao.Id == idTransacao && tf.FormaPagamento.Id == (int)FormaPagamentoEnum.PagarmePix);

        public bool HorarioFoiPagadoAntecipadamento(Horario horario)
        {
            return horario != null && horario.FoiPagoAntecipadamente;
        }

        public List<int> ObterIdHorariosPorTransacaoDePagamentoOnline(int idTransacao)
        {
            var pagamentos = Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.Queryable().Where(p => p.Transacao.Id == idTransacao).ToList();

            if (pagamentos != null && pagamentos.Count > 0)
            {
                List<ItemPagamentoHorario> itensPagamento = new List<ItemPagamentoHorario>();
                foreach (PagamentoAntecipado pagamento in pagamentos)
                {
                    var itensPorPagamento = Domain
                                        .PagamentosAntecipados
                                        .ItemPagamentoHorarioRepository
                                        .Queryable()
                                        .Where(f => f.PagamentoAntecipado == pagamento &&
                                                    f.PagamentoAntecipado.IdEstabelecimento == ContextHelper.Instance.IdEstabelecimento.Value).ToList();
                    itensPagamento.AddRange(itensPorPagamento);
                }

                return itensPagamento.Select(h => h.Horario.Id).Distinct().ToList();
            }

            return new List<int>();
        }

        public decimal? ObterValorPagoAntecipadamenteParaOServico(int idHorario)
        {
            return Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.ObterValorPagoPeloHorario(idHorario);
        }

        private DateTime CalcularDataDeExpiracaoDeBeneficiosDoPagamentoOnline(int idEstabelecimento, DateTime dataServico)
        {
            var minutosAntecedencia = MinutosDeAntecedenciaParaPagamentoComBeneficios(idEstabelecimento);
            return dataServico.AddMinutes(minutosAntecedencia * -1);
        }

        public int MinutosDeAntecedenciaParaPagamentoComBeneficios(int idEstabelecimento)
        {
            int horasDeAntecedencia = Domain.PagamentosAntecipados.BeneficiosEstabelecimentoRepository
                .StatelessQueryable()
                .Where(x => x.IdEstabelecimento == idEstabelecimento)
                .Select(x => x.QuantasHorasAntesPodeAgendarComBeneficios)
                .FirstOrDefault();

            return horasDeAntecedencia * 60;
        }

        public InformacoesDoPagamentoOnline AgendamentoAtendeRequisitosParaPagamentoOnline(Horario horario)
        {
            var compromissos = Domain.Compromissos.MeusCompromissosService.GerarListaDeCompromissos(new List<Horario>() { horario });

            bool permitePagamentoOnline = GrupoDeAgendamentosDeEstabelecimentoPermitePagamentoOnline(
                idEstabelecimento: horario.Estabelecimento.IdEstabelecimento,
                agendamentos: compromissos,
                cacheDeServicosComPagamentoOnline: null
            );

            if (!permitePagamentoOnline)
                return new InformacoesDoPagamentoOnline() { PermitePagarOnline = false };

            DateTime dataDeExpiracaoDosBeneficios = CalcularDataDeExpiracaoDeBeneficiosDoPagamentoOnline(horario.Estabelecimento.IdEstabelecimento, horario.DataInicio);
            bool permitePagarComBeneficios = new EhDataFuturaSpecification().IsSatisfiedBy(dataDeExpiracaoDosBeneficios);

            return new InformacoesDoPagamentoOnline
            {
                PermitePagarOnline = permitePagamentoOnline,
                MensagemPagamentoOnline = permitePagarComBeneficios ? ConstruirMensagemDeIncentivoAoClientePagarOnlineAproveitandoBeneficios(horario) : String.Empty,
                DataExpiracaoBeneficios = dataDeExpiracaoDosBeneficios
            };
        }

        private string ConstruirMensagemDeIncentivoAoClientePagarOnlineAproveitandoBeneficios(Horario horario)
        {
            var beneficios = ObterConfiguracaoBeneficio(horario.Estabelecimento.IdEstabelecimento);
            return ConstruirMensagemDeIncentivoAoClientePagarOnlineAproveitandoBeneficios(beneficios, horario.Valor, quantidadeDeItens: 1);
        }

        public string ConstruirMensagemDeIncentivoAoClientePagarOnlineAproveitandoBeneficios(BeneficiosEstabelecimento beneficios, decimal totalAPagarEmItens, int quantidadeDeItens)
        {
            decimal? desconto = beneficios.PercentualDescontoPorServico;

            if (desconto > 0)
                totalAPagarEmItens = ObterValorComDesconto(totalAPagarEmItens, desconto.Value);

            int pontosParaGanhar = Domain.Fidelidade.CalculoDePontosService.ObterTotalDePontosCalculadosParaMovimentar(beneficios.ProgramaDeFidelidade, totalAPagarEmItens, quantidadeDeItens);

            if (desconto.HasValue && desconto.Value <= 0 && pontosParaGanhar <= 0)
                return null;

            string mensagem = "Pague agora";

            if (desconto > 0 || pontosParaGanhar > 0)
            {
                mensagem = "Pague agora e ganhe ";

                var vantagens = new List<string>();

                if (desconto > 0)
                    vantagens.Add($"{FormatarDecimal(desconto.Value)}% de desconto");

                if (pontosParaGanhar == 1)
                    vantagens.Add("1 ponto de fidelidade");
                else if (pontosParaGanhar > 1)
                    vantagens.Add($"{pontosParaGanhar} pontos de fidelidade");

                mensagem += string.Join(" e ", vantagens);
            }

            return mensagem;
        }

        private static decimal ObterValorComDesconto(decimal valor, decimal desconto)
        {
            return valor - (valor * desconto / 100m);
        }

        private string FormatarDecimal(decimal valor)
        {
            CultureInfo currentCulture = CultureInfo.CurrentCulture;
            string valorString = valor > 0 ? (Math.Truncate(valor * 100) / 100).ToString("#,0.##", CultureInfo.CurrentCulture) : "0";
            int indiceSeparador = valorString.IndexOf(currentCulture.NumberFormat.NumberDecimalSeparator);
            string parcial = valorString.Substring(indiceSeparador + 1, valorString.Length - indiceSeparador - 1);

            return String.Format("{0}{1}", valorString, (parcial.Length == 1 && valor > 0 && indiceSeparador > 0 ? "0" : ""));
        }

        public bool GrupoDeAgendamentosDeEstabelecimentoPermitePagamentoOnline(int idEstabelecimento, List<DadosDoCompromissoDTO> agendamentos, Dictionary<int, List<int>> cacheDeServicosComPagamentoOnline = null, bool ehLinkDePagamento = false)
        {

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(idEstabelecimento);
            var disponibilidadeParaRealizarPagamentoOnline = Domain.ControleDeFuncionalidades.DisponibilidadeDeRecursosService.ObterDisponibilidadeDeRecurso(estabelecimento, Recurso.RealizarPagamentoOnlineAntecipado);
            if (!disponibilidadeParaRealizarPagamentoOnline.EstaDisponivel)
                return false;

            if (agendamentos.Count > 1)
                return false; // Temporariamente não é permitido pagamento de mais de um agendamento

            bool cadastroDeRecebedorPermitePagamentoAntecipado = EstabelecimentoPodeRealizarPagamentoAntecipado(idEstabelecimento, ehLinkDePagamento);

            // Retorna `false` antecipadamente se o cadastro de recebedor do estabelecimento não permite pagamento antecipado.
            // Caso contrário verifica as demais regras.
            if (!cadastroDeRecebedorPermitePagamentoAntecipado)
                return false;

            if (cacheDeServicosComPagamentoOnline == null)
                cacheDeServicosComPagamentoOnline = new Dictionary<int, List<int>>();

            int valorMaximoAceitoParaPagamento = new ParametrosTrinks<int>(ParametrosTrinksEnum.pgtoOnline_valor_maximo_para_pagamento_online).ObterValor();

            if (!cacheDeServicosComPagamentoOnline.ContainsKey(idEstabelecimento))
                cacheDeServicosComPagamentoOnline.Add(idEstabelecimento, ObterIdsDeServicosHabilitadosNoPagamentoOnline(idEstabelecimento));

            var idsServicoEstabelecimentoComPagamentoOnline = cacheDeServicosComPagamentoOnline[idEstabelecimento];

            return agendamentos.All(x =>
            {
                var valorTotalDoServicos = agendamentos.Sum(s => s.Valor);

                return
                    // Nenhum serviço pode ter sido pago
                    x.FoiPago == false &&
                    // Nenhum serviço pode ter sido pago antecipadamente
                    x.FoiPagoAntecipadamente == false &&
                    // Nenhum serviço pode ter iniciado
                    x.DataInicio > Calendario.Agora() &&
                    // Todos os serviços estão ativados no pagamento online
                    idsServicoEstabelecimentoComPagamentoOnline.Contains(x.IdServicoEstabelecimento) &&
                    // Todos os serviços estão com status que permitem o pagamento online
                    Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.StatusPermitePagamentoOnline(x.Status) &&
                    // Todos os serviços estão configurados com preço fixo
                    x.CodigoTipoPreco == (int)TipoPrecoEnum.Fixo &&
                    // O valor não ultrapassa o limite para pagamento online
                    valorTotalDoServicos <= valorMaximoAceitoParaPagamento &&
                    // Não pode ser um agendamento feito pelo Google Reserve
                    x.HorarioOrigem != (int)HorarioOrigemEnum.GoogleReserve &&
                    // Não pode ser um agendamento recorrente
                    x.EhHorarioRecorrencia == false;
            });
        }

        private bool EstabelecimentoPodeRealizarPagamentoAntecipado(int idEstabelecimento, bool ehLinkDePagamento = false)
        {
            if (ehLinkDePagamento)
                return Domain.Pagamentos.PagamentosService.GatewayPermiteAntiFraude(idEstabelecimento, OrigemDeCadastroEnum.PagarMeV5);

            var recebedor = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.ObterEstabelecimentoRecebedorAtivo(idEstabelecimento);

            if (recebedor == null)
                return false;

            var recebedorCredenciado = Domain.Pagamentos.RecebedorCredenciadoRepository.Queryable().SingleOrDefault(e => e.Recebedor.IdRecebedor == recebedor.IdRecebedor);

            return recebedorCredenciado?.Gateway.IdGateway == (int)GatewayEnum.Zoop;
        }

        public bool SalvarOuAtualizarConfiguracaoBeneficioParaOCliente(BeneficioPagamentoClienteDTO beneficioPagamentoCliente)
        {
            if (!ValidarDadosVantagemCliente(beneficioPagamentoCliente))
                return false;

            try
            {
                var beneficioEntity = ObterConfiguracaoBeneficio(beneficioPagamentoCliente.IdEstabelecimento);

                if (beneficioEntity != null)
                {
                    beneficioEntity.PercentualDescontoPorServico = beneficioPagamentoCliente.DescontoServico.HasValue ? beneficioPagamentoCliente.DescontoServico.Value : 0;

                    beneficioEntity.ProgramaDeFidelidade = new BeneficiosDoProgramaDeFidelidade(
                        tipoDeBeneficio: (TipoDeBeneficioDoProgramaDeFidelidadeEnum)beneficioPagamentoCliente.FidelidadeSelecionada,
                        quantidadePontos: beneficioPagamentoCliente.QuantidadePontos,
                        valorPorPontos: beneficioPagamentoCliente.ValorParaPontos
                    );

                    int tempoDeExpiracaoBeneficios = RetornaTempoParaExpiracaoEmHoras((UnidadeTempoParaLimiteDeReceberDescontosEnum)beneficioPagamentoCliente.UnidadeTempoLimite, beneficioPagamentoCliente.TempoLimite);

                    beneficioEntity.QuantasHorasAntesPodeAgendarComBeneficios = tempoDeExpiracaoBeneficios;
                    beneficioEntity.UnidadeTempoParaLimiteDeReceberDescontos = (UnidadeTempoParaLimiteDeReceberDescontosEnum)beneficioPagamentoCliente.UnidadeTempoLimite;
                    beneficioEntity.DataUltimaAtualizacao = DateTime.Now;
                }
                else
                {
                    beneficioEntity = new BeneficiosEstabelecimento
                    {
                        IdEstabelecimento = beneficioPagamentoCliente.IdEstabelecimento,
                        PercentualDescontoPorServico = beneficioPagamentoCliente.DescontoServico.HasValue ? beneficioPagamentoCliente.DescontoServico.Value : 0,
                        QuantasHorasAntesPodeAgendarComBeneficios = beneficioPagamentoCliente.TempoLimite.HasValue ? beneficioPagamentoCliente.TempoLimite.Value : 0,
                        UnidadeTempoParaLimiteDeReceberDescontos = (UnidadeTempoParaLimiteDeReceberDescontosEnum)beneficioPagamentoCliente.UnidadeTempoLimite,
                        DataHoraCriacao = DateTime.Now,
                        DataUltimaAtualizacao = DateTime.Now,
                        ProgramaDeFidelidade = new BeneficiosDoProgramaDeFidelidade(
                            tipoDeBeneficio: (TipoDeBeneficioDoProgramaDeFidelidadeEnum)beneficioPagamentoCliente.FidelidadeSelecionada,
                            quantidadePontos: beneficioPagamentoCliente.QuantidadePontos,
                            valorPorPontos: beneficioPagamentoCliente.ValorParaPontos
                        )
                    };
                }
                if (beneficioEntity.Id != 0)
                    Domain.PagamentosAntecipados.BeneficiosEstabelecimentoRepository.Update(beneficioEntity);
                else
                    Domain.PagamentosAntecipados.BeneficiosEstabelecimentoRepository.SaveNew(beneficioEntity);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private bool ValidarDadosVantagemCliente(BeneficioPagamentoClienteDTO beneficioPagamentoCliente)
        {
            if (beneficioPagamentoCliente.TempoLimite > 999)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("O período limite é inválido.");
                return false;
            }

            var tipoDeBeneficio = (TipoDeBeneficioDoProgramaDeFidelidadeEnum)beneficioPagamentoCliente.FidelidadeSelecionada;
            var valorPorPontos = beneficioPagamentoCliente.ValorParaPontos;
            var quantidadePontos = beneficioPagamentoCliente.QuantidadePontos;

            switch (tipoDeBeneficio)
            {
                case TipoDeBeneficioDoProgramaDeFidelidadeEnum.PontosPorReaisGastos:
                    if (!valorPorPontos.HasValue || !quantidadePontos.HasValue)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("O valor e quantidade de pontos devem ser informados.");
                        return false;
                    }
                    break;

                case TipoDeBeneficioDoProgramaDeFidelidadeEnum.PontosPorAgendamento:
                    if (valorPorPontos.HasValue || !quantidadePontos.HasValue)
                    {
                        ValidationHelper.Instance.AdicionarItemValidacao("A quantidade de pontos deve ser informada.");
                        return false;
                    }
                    break;
            }

            return true;
        }

        private int RetornaTempoParaExpiracaoEmHoras(UnidadeTempoParaLimiteDeReceberDescontosEnum unidadeMedidaHorarioLimite, int? tempoLimite)
        {
            //UnidadeMedidaHorarioLimite = 1 unidade em dias e será convertido
            //UnidadeMedidaHorarioLimite = 0 unidade em horas e não será convertido
            int horarioConvertido;
            if (tempoLimite.HasValue)
            {
                if (unidadeMedidaHorarioLimite == UnidadeTempoParaLimiteDeReceberDescontosEnum.Dias)
                {
                    horarioConvertido = (int)tempoLimite * 24;
                    return horarioConvertido;
                }
                else
                {
                    return (int)tempoLimite;
                }
            }
            else
            {
                return 0;
            }
        }

        private BeneficiosEstabelecimento ObterConfiguracaoBeneficio(int idEstabelecimento)
        {
            return Domain.PagamentosAntecipados.BeneficiosEstabelecimentoRepository.ObterPorEstabelecimento(idEstabelecimento);
        }

        public BeneficioPagamentoClienteDTO ObterConfiguracaoBeneficioParaOCliente(int idEstabelecimento)
        {
            // idEstabelecimento = 814;
            var beneficioEntity = ObterConfiguracaoBeneficio(idEstabelecimento);
            var poseeFidelidade = Domain.Fidelidade.ProgramaDeFidelidadeService.EstabelecimentoPossuiProgramaDeFidelidadeHabilitadoEConfigurado(idEstabelecimento);
            if (beneficioEntity == null)
                return new BeneficioPagamentoClienteDTO(poseeFidelidade);
            int tempoLimiteParaExpiracaoDosBeneficios = RetornaTempoParaExpiracaoDeAcordoComConfiguracaoDoUsuario(beneficioEntity.UnidadeTempoParaLimiteDeReceberDescontos, beneficioEntity.QuantasHorasAntesPodeAgendarComBeneficios);

            return new BeneficioPagamentoClienteDTO
            {
                IdEstabelecimento = beneficioEntity.IdEstabelecimento,
                DescontoServico = beneficioEntity.PercentualDescontoPorServico,
                FidelidadeSelecionada = (int)beneficioEntity.ProgramaDeFidelidade.TipoDeBeneficio,
                ProgramaFidelidadeHabilitado = poseeFidelidade,
                QuantidadePontos = beneficioEntity.ProgramaDeFidelidade.QuantidadePontosDeFidelidade,
                TempoLimite = tempoLimiteParaExpiracaoDosBeneficios,
                UnidadeTempoLimite = (int)beneficioEntity.UnidadeTempoParaLimiteDeReceberDescontos,
                ValorParaPontos = beneficioEntity.ProgramaDeFidelidade.ValorPorPontosDeFidelidade
            };
        }

        private int RetornaTempoParaExpiracaoDeAcordoComConfiguracaoDoUsuario(UnidadeTempoParaLimiteDeReceberDescontosEnum unidadeTempoParaLimiteDeReceberDescontos, int quantasHorasAntesPodeAgendarComBeneficios)
        {
            //UnidadeMedidaHorarioLimite = 1 unidade em dias e será convertido
            //UnidadeMedidaHorarioLimite = 0 unidade em horas e não será convertido
            int horarioConvertido;
            if (quantasHorasAntesPodeAgendarComBeneficios != 0)
            {
                if (unidadeTempoParaLimiteDeReceberDescontos == UnidadeTempoParaLimiteDeReceberDescontosEnum.Dias)
                {
                    horarioConvertido = quantasHorasAntesPodeAgendarComBeneficios / 24;
                    return horarioConvertido;
                }
                else
                {
                    return quantasHorasAntesPodeAgendarComBeneficios;
                }
            }
            else
            {
                return 0;
            }
        }

        public List<int> ObterIdsDeServicosHabilitadosNoPagamentoOnline(int idEstabelecimento)
        {
            if (!Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstabelecimentoPossuiPagamentoOnlineAtivado(idEstabelecimento))
                return new List<int>();

            return Domain.PagamentosAntecipados.ServicoHabilitadoRepository.ObterListaDeIdsServicosHabilitadoEstabelecimento(idEstabelecimento).Select(s => s.IdServicoEstabelecimento).ToList();
        }

        public bool ServicoPodeSerPagoOnline(int idEstabelecimeto, int idServico)
        {
            var servicosHabilitados = ObterIdsDeServicosHabilitadosNoPagamentoOnline(idEstabelecimeto);

            var servicoEstabelecimento = Domain.Pessoas.ServicoEstabelecimentoRepository.ObterServicoEstabelecimento(idServico, idEstabelecimeto);

            if (servicoEstabelecimento == null)
                return false;

            return servicosHabilitados.Contains(idServico) && servicoEstabelecimento.TipoPreco == TipoPrecoEnum.Fixo;
        }

        public decimal ValorMaximoParaUmUnicoPagamentoOnline
        {
            get
            {
                return new ParametrosTrinks<decimal>(ParametrosTrinksEnum.pgtoOnline_valor_maximo_para_pagamento_online).ObterValor();
            }
        }

        public List<ServicoHabilitadoParaPagamentoDTO> ObterListaServicosDisponiveisNoPagamentoOnline(int idEstabelecimento)
        {
            var idsServicosHabilitados = Domain.PagamentosAntecipados.ServicoHabilitadoRepository.ObterListaDeIdsServicosHabilitadoEstabelecimento(idEstabelecimento)
                                        .Select(s => new { s.IdServicoEstabelecimento, s.Ativo })
                                        .ToDictionary(s => s.IdServicoEstabelecimento, s => s.Ativo);

            var listaServicosEstabelecimentoSegundoRegra = Domain.Pessoas.ServicoEstabelecimentoRepository
                                        .ListarServicosPagamentoOnlineSegundoRegra(idEstabelecimento, ValorMaximoParaUmUnicoPagamentoOnline)
                                        .ToList();

            if (idsServicosHabilitados.Any())
                listaServicosEstabelecimentoSegundoRegra
                     .Where(w => idsServicosHabilitados.ContainsKey(w.IdServicoEstabelecimento)).ToList()
                     .ForEach(f => f.Ativo = idsServicosHabilitados[f.IdServicoEstabelecimento]);
            else
                listaServicosEstabelecimentoSegundoRegra
                     .ForEach(f => f.Ativo = true);

            return listaServicosEstabelecimentoSegundoRegra;
        }

        public List<ServicoHabilitadoParaPagamentoDTO> ObterListaServicosHabilitados(int idEstabelecimento)
        {
            var idsServicosHabilitados = Domain.PagamentosAntecipados.ServicoHabilitadoRepository.ObterListaDeIdsServicosHabilitadoEstabelecimento(idEstabelecimento).Select(s => s.IdServicoEstabelecimento).ToList();

            if (!idsServicosHabilitados.Any())
                return new List<ServicoHabilitadoParaPagamentoDTO>();

            var listaServicosEstabelecimentoSegundoRegra = Domain.Pessoas.ServicoEstabelecimentoRepository
                                        .ListarServicosPagamentoOnlineSegundoRegra(idEstabelecimento, ValorMaximoParaUmUnicoPagamentoOnline)
                                        .ToList();

            return listaServicosEstabelecimentoSegundoRegra.Where(w => idsServicosHabilitados.Contains(w.IdServicoEstabelecimento)).ToList();
        }

        public bool ValidarSeEstabelecimentoTemServicosHabilitados(int idEstabelecimento)
        {
            return Domain.PagamentosAntecipados.ServicoHabilitadoRepository.ObterListaDeIdsServicosHabilitadoEstabelecimento(idEstabelecimento)
                                        .Select(s => new { s.IdServicoEstabelecimento, s.Ativo })
                                        .ToDictionary(s => s.IdServicoEstabelecimento, s => s.Ativo).Any();
        }

        [TransactionInitRequired]
        public bool SalvarEAtualizarListaServicosSelecionado(List<int> listaServicosHabilitadosNova, int idEstabelecimento)
        {
            var listaServicosHabilitadosOriginal = Domain.PagamentosAntecipados.ServicoHabilitadoRepository.ObterListaDeIdsServicosHabilitadoEstabelecimento(idEstabelecimento).ToList();

            try
            {
                if (!listaServicosHabilitadosOriginal.Any() && listaServicosHabilitadosNova.Any())
                {
                    listaServicosHabilitadosNova
                            .ForEach(f => Domain.PagamentosAntecipados.ServicoHabilitadoRepository.SaveNew(
                                new ServicoHabilitado
                                {
                                    IdEstabelecimento = idEstabelecimento,
                                    IdServicoEstabelecimento = f,
                                    DataCriacao = DateTime.Now,
                                    DataUltimaAtualizacao = DateTime.Now,
                                    Ativo = true
                                }));
                    return true;
                }

                //deshabilitar tudos os sevicos
                listaServicosHabilitadosOriginal
                    .ForEach(f =>
                    {
                        f.Ativo = false;
                        Domain.PagamentosAntecipados.ServicoHabilitadoRepository.Update(f);
                    });

                //habilitar os sevicos que continuam selecionados
                listaServicosHabilitadosOriginal
                    .Where(w => listaServicosHabilitadosNova.Contains(w.IdServicoEstabelecimento)).ToList()
                    .ForEach(f => { f.Ativo = true; Domain.PagamentosAntecipados.ServicoHabilitadoRepository.Update(f); });

                //salvar os sevicos novos selecionados
                listaServicosHabilitadosNova
                    .Where(w => !listaServicosHabilitadosOriginal.Any(id => id.IdServicoEstabelecimento == w)).ToList()
                    .ForEach(f => Domain.PagamentosAntecipados.ServicoHabilitadoRepository.SaveNew(
                        new ServicoHabilitado
                        {
                            IdEstabelecimento = idEstabelecimento,
                            IdServicoEstabelecimento = f,
                            DataCriacao = DateTime.Now,
                            DataUltimaAtualizacao = DateTime.Now,
                            Ativo = true
                        }));
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        public decimal ObterValorCobradoPelaOperadora(int idHorario)
        {
            var pagamentoAntecipado = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.ObterPagamentoAntecipadoDoHorario(idHorario);

            return pagamentoAntecipado.PagamentoOnline.ObterValorTotalDescontado();
        }

        public bool PermitePagamentoComBeneficios(int idEstabelecimento, List<DateTime> datasDeRealizacaoDosServicos)
        {
            DateTime dataDoServicoMaisAntigo = ObterDataDeExpiracaoDeBeneficios(idEstabelecimento, datasDeRealizacaoDosServicos);
            return new EhDataFuturaSpecification().IsSatisfiedBy(dataDoServicoMaisAntigo);
        }

        public DateTime ObterDataDeExpiracaoDeBeneficios(int idEstabelecimento, List<DateTime> datasDosServicos)
        {
            return datasDosServicos.Select(x => CalcularDataDeExpiracaoDeBeneficiosDoPagamentoOnline(idEstabelecimento, x)).Min();
        }

        public bool EstabelecimentoJaPossuiBeneficiosConfigurados(int idEstabelecimento)
        {
            return Domain.PagamentosAntecipados.BeneficiosEstabelecimentoRepository.Queryable().Where(p => p.IdEstabelecimento == idEstabelecimento).Select(p => p.Id).Any();
        }

        public bool EstabelecimentoJaTrabalhouComPagamentoOnline(Estabelecimento estabelecimento)
        {
            return Domain.Pessoas.EstabelecimentoFormaPagamentoRepository.Queryable().Any(ef => ef.Estabelecimento == estabelecimento && ef.FormaPagamento.Id == (int)FormaPagamentoEnum.PagamentoOnline);
        }

        public DateTime? ObterDataDoPagamento(Horario horario)
        {
            var dataPagamento = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable().Where(i => i.Horario == horario && i.PagamentoAntecipado.StatusPagamento == StatusPagamentoAntecipadoEnum.Pago).Select(i => i.PagamentoAntecipado.DataHoraPagamento).FirstOrDefault();
            return dataPagamento;
        }

        public PagamentoAntecipado ObterPorPagamentoOnline(int idPagamentoOnline)
        {
            return Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.Queryable().FirstOrDefault(f => f.PagamentoOnline.IdPagamentoOnline == idPagamentoOnline);
        }

        public PagamentoAntecipado GerarPagamentoAntecipadoParaAgendamentoHotsite(Horario horario)
        {
            var estabelecimento = horario.Estabelecimento;
            var filtroParaPagamento = new FiltroParaObterDadosDePagamentoDTO() { IdAgendamento = horario.Id };

            var dadosParaPagamento = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ObterDadosParaPagamentoDeServicos(
                filtro: filtroParaPagamento,
                idFranquia: estabelecimento.FranquiaEstabelecimento?.Franquia.Id,
                ehLinkDePagamento: true
            );

            var clienteEstabelecimento = horario.ClienteEstabelecimento;

            var pagamentoAntecipado = new PagamentoAntecipado
            {
                IdEstabelecimento = clienteEstabelecimento.Estabelecimento.IdEstabelecimento,
                IdPessoaDoCliente = clienteEstabelecimento.Cliente.PessoaFisica.IdPessoa,
                ValorTotal = dadosParaPagamento.ValorTotal,
                DadosPesquisados = new DadosPesquisadosParaPagamento(horario.Id, null, null)
            };

            pagamentoAntecipado.Itens = new List<ItemPagamentoAntecipado>(dadosParaPagamento.Servicos.Select(x => new ItemPagamentoHorario
            {
                Horario = Domain.Pessoas.HorarioRepository.ObterPorId(x.IdAgendamento),
                NomeItem = x.NomeServico,
                DataDeExpiracaoDosBeneficios = x.DataExpiracaoBeneficios,
                PercentualDeDesconto = x.PercentualDeDesconto,
                ValorComDesconto = x.ValorComDesconto,
                ValorOriginal = x.ValorOriginal,
                PagamentoAntecipado = pagamentoAntecipado
            }));

            pagamentoAntecipado.Beneficios = new BeneficiosPagamento
            {
                DisponivelAoCliente = dadosParaPagamento.BeneficiosDisponiveis,
                PercentualDeDesconto = dadosParaPagamento.Beneficios?.PercentualDescontoPorServico,
                QuantasHorasAntesPodeAgendarComBeneficios = dadosParaPagamento.Beneficios.QuantasHorasAntesPodeAgendarComBeneficios,
                ProgramaDeFidelidade = new BeneficiosDoProgramaDeFidelidade(
                    tipoDeBeneficio: dadosParaPagamento.Beneficios.TipoDeBeneficioDoProgramaDeFidelidade,
                    quantidadePontos: dadosParaPagamento.Beneficios.QuantidadePontosDeFidelidade,
                    valorPorPontos: dadosParaPagamento.Beneficios.ValorPorPontosDeFidelidade
                ),
                PagamentoAntecipado = pagamentoAntecipado
            };

            Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.SaveNew(pagamentoAntecipado);

            return pagamentoAntecipado;
        }
    }
}