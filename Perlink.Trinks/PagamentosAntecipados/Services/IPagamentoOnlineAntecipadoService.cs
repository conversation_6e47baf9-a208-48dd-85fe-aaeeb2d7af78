﻿﻿using Perlink.DomainInfrastructure.Services;
using Perlink.Trinks.Compromissos.DTO;
using Perlink.Trinks.PagamentosAntecipados.DTO;
using Perlink.Trinks.PagamentosAntecipados.Utils;
using Perlink.Trinks.Pessoas;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentosAntecipados.Services
{

    public interface IPagamentoOnlineAntecipadoService : IService
    {

        Task RealizarTransacaoDePagamentoOnlineAntecipado(PagamentoAntecipado pagamento);

        bool TransacaoEhDePagamentoOnline(int id);

        bool TransacaoEhDePagamentoOnlinePorLink(int idTransacao);

        bool TransacaoEhClubeDeAssinaturasPorLink(int idTransacao);
        bool TransacaoEhPagamentoOnlineHotsite(int idTransacao);

        bool TransacaoEhDeClubeDeAssinatura(int idTransacao);

        bool TransacaoEhConsumoDePagamentoOnline(int idTransacao);

        bool TransacaoEhDeCreditoPagamentoOnline(int idTransacao);

        bool HorarioFoiPagadoAntecipadamento(Horario horario);

        List<int> ObterIdHorariosPorTransacaoDePagamentoOnline(int idTransacao);

        decimal? ObterValorPagoAntecipadamenteParaOServico(int idHorario);

        Task<Resultado<DTO.DadosDoPagamentoRealizadoDTO>> RealizarPagamentoOnlineAntecipado(NovoPagamentoOnlineAntecipadoDTO dadosDoPagamento);

        /// <summary>
        /// Retorna as informações pertinentes ao pagamento de um agendamento específico ou de todos
        /// os serviços agendados em uma data específica para um único estabelecimento.
        /// </summary>
        DadosParaPagamentoDeServicosDTO ObterDadosParaPagamentoDeServicos(FiltroParaObterDadosDePagamentoDTO filtro, int? idFranquia, bool ehLinkDePagamento = false);

        /// <summary>
        /// Minutos de antecedência a realização do serviço para utilização dos benefícios do pagamento online.
        /// </summary>
        int MinutosDeAntecedenciaParaPagamentoComBeneficios(int idEstabelecimento);

        bool GrupoDeAgendamentosDeEstabelecimentoPermitePagamentoOnline(int idEstabelecimento, List<DadosDoCompromissoDTO> agendamentos, Dictionary<int, List<int>> cacheDeServicosComPagamentoOnline, bool ehLinkDePagamento = false);

        bool SalvarOuAtualizarConfiguracaoBeneficioParaOCliente(BeneficioPagamentoClienteDTO beneficioPagamentoCliente);
        PagamentoAntecipado ObterPorPagamentoOnline(int idPagamentoOnline);
        BeneficioPagamentoClienteDTO ObterConfiguracaoBeneficioParaOCliente(int idEstabelecimento);

        InformacoesDoPagamentoOnline AgendamentoAtendeRequisitosParaPagamentoOnline(Horario horario);

        List<int> ObterIdsDeServicosHabilitadosNoPagamentoOnline(int idEstabelecimento);

        List<ServicoHabilitadoParaPagamentoDTO> ObterListaServicosDisponiveisNoPagamentoOnline(int idEstabelecimento);

        List<ServicoHabilitadoParaPagamentoDTO> ObterListaServicosHabilitados(int idEstabelecimento);

        bool ValidarSeEstabelecimentoTemServicosHabilitados(int idEstabelecimento);

        bool SalvarEAtualizarListaServicosSelecionado(List<int> idsServicos, int idEstabelecimento);

        /// <summary>
        /// Valor máximo a ser pago por um cliente para um pagamento online (se o somatório dos serviços agendados em um dia ultrapassar este valor não será dada ao cliente a opção de fazer o pagamento antecipado).
        /// </summary>
        decimal ValorMaximoParaUmUnicoPagamentoOnline { get; }

        decimal ObterValorCobradoPelaOperadora(int idHorario);
        string ConstruirMensagemDeIncentivoAoClientePagarOnlineAproveitandoBeneficios(BeneficiosEstabelecimento beneficios, decimal totalGastoEmItens, int quantidadeDeItens);
        DateTime ObterDataDeExpiracaoDeBeneficios(int idEstabelecimento, List<DateTime> datasDosServicos);
        bool PermitePagamentoComBeneficios(int idEstabelecimento, List<DateTime> datasDeRealizacaoDosServicos);
        bool EstabelecimentoJaPossuiBeneficiosConfigurados(int idEstabelecimento);
        bool EstabelecimentoJaTrabalhouComPagamentoOnline(Estabelecimento estabelecimento);
        DateTime? ObterDataDoPagamento(Horario horario);
        bool ServicoPodeSerPagoOnline(int idEstabelecimeto, int idServico);
        PagamentoAntecipado GerarPagamentoAntecipadoParaAgendamentoHotsite(Horario horario);
        bool TransacaoEhConsumoDePagamentoOnlineHotsite(int idTransacao);
        bool TransacaoEhPagarmeCredito(int idTransacao);
        bool TransacaoEhPagarmePix(int idTransacao);
    }
}