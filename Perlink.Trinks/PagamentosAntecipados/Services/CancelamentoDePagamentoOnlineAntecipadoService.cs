﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Enums;
using Perlink.Trinks.Financeiro;
using Perlink.Trinks.Financeiro.Enums;
using Perlink.Trinks.PagamentosAntecipados.DTO;
using Perlink.Trinks.PagamentosAntecipados.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Vendas;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Perlink.Trinks.PagamentosAntecipados.Services
{
    public class CancelamentoDePagamentoOnlineAntecipadoService : BaseService, ICancelamentoDePagamentoOnlineAntecipadoService
    {

        public async Task<ResultadoCancelamentoDePagamentoAntecipado> EstornarOuTransformarEmCreditoClienteOPagamentoOnlineDosHorarios(List<int> idsHorarios, string motivo, PessoaFisica quemEstornou, bool ehUmEstornoDePagamentoOnline)
        {
            var resultado = new ResultadoCancelamentoDePagamentoAntecipado();

            var pagamento = ObterPagamentoAntecipadoDosHorarios(idsHorarios);
            var idsDeTodosOsHorariosDoPagamento = ObterTodosIdsHorariosDoPagamento(pagamento.Id);

            bool algumHorarioDoPagamentoJaEstaFechado = ExisteAlgumHorarioJaEmFechamentoDeConta(idsDeTodosOsHorariosDoPagamento);
            bool pagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora = PagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora(pagamento);//TODO:duvida - ver se é uma regra do antecipado ou do online
            bool serahFeitoExtornoPelaOperadora = pagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora && !algumHorarioDoPagamentoJaEstaFechado;
            bool serahTransformadoEmCreditoCliente = !serahFeitoExtornoPelaOperadora;

            if (ehUmEstornoDePagamentoOnline && !serahFeitoExtornoPelaOperadora)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Só é possível estornar um pagamento online caso seja no mesmo dia do pagamento e nenhum agendamento esteja em uma conta fechada. Caso necessário, vá nos agendamentos e selecione a opção de cancelar.");
                return resultado;
            }

            if (serahFeitoExtornoPelaOperadora || ehUmEstornoDePagamentoOnline)
            {
                await EstornarOPagamentoOnlineDosHorarios(idsHorarios, motivo, quemEstornou);
                resultado = new ResultadoCancelamentoDePagamentoAntecipado(AcaoExecutadaAoCancelarPagamentoAntecipadoEnum.EstornadoDiretamenteAoCliente, pagamento.ValorTotal);
            }
            else if (serahTransformadoEmCreditoCliente)
            {
                await TransformarEmCreditoClienteOPagamentoOnlineDosHorarios(idsHorarios);
                resultado = new ResultadoCancelamentoDePagamentoAntecipado(AcaoExecutadaAoCancelarPagamentoAntecipadoEnum.TransformadoEmCreditoNoEstabelecimento, pagamento.ValorTotal);
            }

            return resultado;
        }

        public async Task EstornarPagamentoOnlineDosHorariosPorIdTransacao(int idTransacao, string motivo, PessoaFisica quemEstornou)
        {

            var idsHorarios = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ObterIdHorariosPorTransacaoDePagamentoOnline(idTransacao);
            ValidarCancelamentoDeAgendamentos(idsHorarios, ehUmEstornoDePagamentoOnline: true);

            if (ValidationHelper.Instance.TemPendenciaDeConfirmacao() && !ValidationHelper.Instance.Confirmado)
                return;

            await Domain.Pessoas.AgendaService
                .CancelarHorarios(idsHorarios, motivo, new HorarioQuemCancelou((int)HorarioQuemCancelouEnum.Estabelecimento), pessoaQueAlterou: quemEstornou, areaDoTrinks: AreasTrinksEnum.Backoffice, ehUmEstornoDePagamentoOnline: true);
        }

        private async Task TransformarEmCreditoClienteOPagamentoOnlineDosHorarios(List<int> idsHorarios)
        {
            var pagamento = ObterPagamentoAntecipadoDosHorarios(idsHorarios);
            var itensPagamentoATransfromarEmCredito = pagamento.Itens
                .Where(ip => ip is ItemPagamentoHorario && idsHorarios.Contains(((ItemPagamentoHorario)ip).Horario.Id)).ToList().Cast<ItemPagamentoHorario>().ToList();

            await RealizarTransacaoDeCompraDeCreditoUsandoCreditoDePagamentoOnline(itensPagamentoATransfromarEmCredito, pagamento);
        }

        private async Task RealizarTransacaoDeCompraDeCreditoUsandoCreditoDePagamentoOnline(List<ItemPagamentoHorario> itensPagamentoATransfromarEmCredito, PagamentoAntecipado pagamentoAntecipado)
        {
            var totalPago = itensPagamentoATransfromarEmCredito.Sum(i => i.ValorComDesconto).Value;

            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterPorId(pagamentoAntecipado.IdEstabelecimento);
            var pessoaDoClienteQuePagou = Domain.Pessoas.PessoaFisicaRepository.Load(pagamentoAntecipado.IdPessoaDoCliente);
            Venda venda = Domain.Vendas.VendaRepository.Factory.CreateParaRealizarTransacao(
                estabelecimento: estabelecimento,
                pessoaQuePagou: pessoaDoClienteQuePagou,
                dataHoraDaTransacao: Calendario.Agora(),
                dataReferencia: Calendario.Agora(),
                pessoaQueRealizou: pessoaDoClienteQuePagou,
                tipoTransacao: TipoTransacaoEnum.Pagamento,
                comentarioFechamentoConta: "",
                comentarioEstorno: "");

            venda.Transacao.FormasPagamento.Add(GerarFormaPagamentoDeCreditoPagamentoOnline(totalPago, venda.Transacao, pagamentoAntecipado.IdEstabelecimento));
            Domain.Financeiro.TransacaoService
                .ColocarCompraDeCreditoNaTransacao(transacao: venda.Transacao, formaPagamentoPrePago: FormaPagamentoPrePagoEnum.CreditoCliente, valorDoCredito: pagamentoAntecipado.ValorTotal);

            var checkoutDTO = new Financeiro.DTO.CheckoutDTO(venda.Transacao, venda.Transacao.PessoaQuePagou)
            {
                Venda = venda,
                EmitirNFC = false,
                IdTransacaoPOS = null,
                ControlarCaixaSeNecessario = false,
                ControlarPagamentoPOS = false
            };

            var resultadoCheckOut = await Domain.Financeiro.TransacaoService.RealizarCheckOut(checkoutDTO);

            foreach (var item in itensPagamentoATransfromarEmCredito)
            {
                item.TransacaoDeCancelamento = resultadoCheckOut.Transacao;
                Domain.PagamentosAntecipados.ItemPagamentoAntecipadoRepository.UpdateNoFlush(item);
            }

            Domain.PagamentosAntecipados.ItemPagamentoAntecipadoRepository.Flush();
        }

        private TransacaoFormaPagamento GerarFormaPagamentoDeCreditoPagamentoOnline(decimal totalPago, Transacao transacao, int idEstabelecimento)
        {
            var transacaoFormaPagamentoDeCreditoPagamentoOnline = Domain.Financeiro.TransacaoService.MontarTransacaoFormaPagamento(transacao, (int)FormaPagamentoEnum.CreditoDePagamentoOnline, idEstabelecimento, totalPago);
            return transacaoFormaPagamentoDeCreditoPagamentoOnline;
        }

        private async Task EstornarOPagamentoOnlineDosHorarios(List<int> idsHorarios, string comentarioEstorno, PessoaFisica pessoaQueEstornou)
        {
            var pagamentoAntecipado = ObterPagamentoAntecipadoDosHorarios(idsHorarios);

            VerificarSePacoteJaFoiConsumido(pagamentoAntecipado.PagamentoOnline.Transacao);

            if (!ValidationHelper.Instance.IsValid) return;

            var estornadoComSucesso = await Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstornarPagamentoOnline(pagamentoAntecipado.PagamentoOnline);

            if (!estornadoComSucesso) return;

            var transacaoDeEstorno = await Domain.Financeiro.TransacaoService.RealizarEstorno(pagamentoAntecipado.Transacao.Id, Calendario.Agora(), comentarioEstorno, pessoaQueEstornou, null);

            if (transacaoDeEstorno == null)//Avaliar como colocar as validações do estorno no fluxo de estorno do pagamento online
                throw new ArgumentException("Ocorreu um erro durante o estorno do pagamento online.", new Exception(string.Join(";", ValidationHelper.Instance.ListaItensValidacao.Select(i => i.Mensagem).ToArray())));

            pagamentoAntecipado.IndicarQueFoiCancelado();

            foreach (var item in pagamentoAntecipado.Itens)
            {
                item.TransacaoDeCancelamento = transacaoDeEstorno;

                if (item is ItemPagamentoHorario)
                {
                    var itemPagamentoHorario = item as ItemPagamentoHorario;
                    itemPagamentoHorario.Horario.FoiPagoAntecipadamente = false;
                    Domain.Pessoas.HorarioRepository.UpdateNoFlush(itemPagamentoHorario.Horario);
                }
                //Domain.PagamentosAntecipados.ItemPagamentoRepository.UpdateNoFlush(item);
            }

            Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.Update(pagamentoAntecipado);
        }

        public async Task EstornarOPagamentoOnline(int idTransacao, string comentarioEstorno, PessoaFisica pessoaQueEstornou)
        {
            bool ehCreditoPagamentoOnlineHotsite = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhConsumoDePagamentoOnlineHotsite(idTransacao);

            if (ehCreditoPagamentoOnlineHotsite)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Não é possível realizar estorno de pagamento antecipado pelo hotsite");
                return;
            }
            
            bool ehPagamentoOnlineHotsite = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhPagamentoOnlineHotsite(idTransacao) ||
                                            Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhPagarmeCredito(idTransacao) ||
                                            Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.TransacaoEhPagarmePix(idTransacao);
            
            if (ehPagamentoOnlineHotsite)
                Domain.PagamentoAntecipadoHotsite.CancelarPagamentoAntecipadoHotsiteService.ValidarEstornoPagamentoAntecipadoHotsiteServico(idTransacao);
            
            if (!ValidationHelper.Instance.IsValid) return;
            
            var idsHorarios = Domain.PagamentosAntecipados.PagamentoOnlineAntecipadoService.ObterIdHorariosPorTransacaoDePagamentoOnline(idTransacao);
            if (idsHorarios.Count > 0)
                await EstornarPagamentoOnlineDosHorariosPorIdTransacao(idTransacao, comentarioEstorno, pessoaQueEstornou);
            else
            {
                await EstornarPagamentoOnlineQueNaoContemHorariosPorIdTransacao(idTransacao, comentarioEstorno, pessoaQueEstornou);
            }
        }

        private async Task EstornarPagamentoOnlineQueNaoContemHorariosPorIdTransacao(int idTransacao, string comentarioEstorno, PessoaFisica pessoaQueEstornou)
        {
            var pagamentoOnline = Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksRepository.Queryable().FirstOrDefault(f => f.Transacao.Id == idTransacao);

            VerificarSePacoteJaFoiConsumido(pagamentoOnline.Transacao);

            if (!ValidationHelper.Instance.IsValid) return;

            var estornadoComSucesso = await Domain.PagamentosOnlineNoTrinks.PagamentoOnlineNoTrinksService.EstornarPagamentoOnline(pagamentoOnline);

            if (!estornadoComSucesso) return;

            var transacaoDeEstorno = await Domain.Financeiro.TransacaoService.RealizarEstorno(pagamentoOnline.Transacao.Id, Calendario.Agora(), comentarioEstorno, pessoaQueEstornou, null);

            if (transacaoDeEstorno == null)//Avaliar como colocar as validações do estorno no fluxo de estorno do pagamento online
                throw new ArgumentException("Ocorreu um erro durante o estorno do pagamento online.", new Exception(string.Join(";", ValidationHelper.Instance.ListaItensValidacao.Select(i => i.Mensagem).ToArray())));
        }

        private void VerificarSePacoteJaFoiConsumido(Transacao transacao)
        {
            var venda = Domain.Vendas.VendaRepository.ObterPorTransacao(transacao);
            Domain.Financeiro.TransacaoService.VerificarSePacoteJaFoiConsumido(venda, transacao);
        }

        public void ValidarCancelamentoDeAgendamentos(List<int> idsHorariosParaCancelar, bool ehUmEstornoDePagamentoOnline)
        {
            var pagamento = ObterPagamentoAntecipadoDosHorarios(idsHorariosParaCancelar);
            var idsDeTodosOsHorariosDoPagamento = ObterTodosIdsHorariosDoPagamento(pagamento.Id);

            bool algumHorarioEstaFechado = ExisteAlgumHorarioJaEmFechamentoDeConta(idsHorariosParaCancelar);
            bool algumHorarioDoPagamentoJaEstaFechado = ExisteAlgumHorarioJaEmFechamentoDeConta(idsDeTodosOsHorariosDoPagamento);
            bool pagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora = PagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora(pagamento);
            bool podeCancelarApenasAlgunsHorariosDoPagamento = algumHorarioDoPagamentoJaEstaFechado || !pagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora;
            bool todosOsHorariosDoPagamentoEstaoNaListaParaCancelar = idsDeTodosOsHorariosDoPagamento.All(hp => idsHorariosParaCancelar.Any(hc => hc == hp));
            bool serahFeitoExtornoPelaOperadora = pagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora && !algumHorarioDoPagamentoJaEstaFechado;
            bool serahTransformadoEmCreditoCliente = !serahFeitoExtornoPelaOperadora;
            bool valido = true;

            if (algumHorarioEstaFechado)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Já existe um agendamento em um fechamento de conta.");
                valido = false;
            }
            else if (!podeCancelarApenasAlgunsHorariosDoPagamento && !todosOsHorariosDoPagamentoEstaoNaListaParaCancelar)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Todos os agendamentos de um mesmo pagamento precisam ser cancelados juntos, enquanto o cancelamento gerar estorno no cartão.");
                valido = false;
            }

            if (serahFeitoExtornoPelaOperadora)
            {
                ValidarEstornoDaTransacaonoTrinks(pagamento.Transacao);
            }

            if (ehUmEstornoDePagamentoOnline && !serahFeitoExtornoPelaOperadora)
            {
                ValidationHelper.Instance.AdicionarItemValidacao("Só é possível estornar um pagamento online caso seja no mesmo dia do pagamento e nenhum agendamento esteja em uma conta fechada. Caso necessário, vá nos agendamentos e selecione a opção de cancelar.");//TODO:duvida - confirmar com a Laura
                valido = false;
            }

            if (valido && ValidationHelper.Instance.IsValid && !ValidationHelper.Instance.Confirmado)
            {
                if (serahFeitoExtornoPelaOperadora)
                    ValidationHelper.Instance.AdicionarItemConfirmacao("Este cancelamento causará um estorno junto a operadora do cartão. Deseja Continuar?");
                else if (serahTransformadoEmCreditoCliente)
                    ValidationHelper.Instance.AdicionarItemConfirmacao("Este cancelamento transformará o valor pago pelo cliente em crédito cliente. Deseja Continuar?");
            }
        }

        private void ValidarEstornoDaTransacaonoTrinks(Transacao transacao)
        {
            Domain.Financeiro.TransacaoService.ValidarEstorno(transacao);
        }

        private bool AlgumHorarioDoPagamentoJaEstaFechado(int idPagamento)
        {
            var idsHorariosDoPagamento = ObterTodosIdsHorariosDoPagamento(idPagamento);
            return ExisteAlgumHorarioJaEmFechamentoDeConta(idsHorariosDoPagamento);
        }

        public bool ExisteHorarioPagoOnline(List<int> idsHorarios)
        {
            var queryItemPagamentoHorario = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable();

            return queryItemPagamentoHorario
                .Where(itp => itp.PagamentoAntecipado.DataHoraPagamento != null
                && idsHorarios.Contains(itp.Horario.Id)).Any();
        }

        public bool HorarioEhPagamentoOnlineEhFoiCancelado(Horario Horario)
        {
            var query = Domain.Pessoas.HorarioRepository.Queryable().Where(f => Horario.Id == f.Id
                                                        && f.Status == StatusHorarioEnum.Cancelado
                                                        && f.FoiPagoAntecipadamente);
            return query.Any();
        }


        private bool ExisteAlgumHorarioJaEmFechamentoDeConta(List<int> idsHorarios)
        {//TODO:duvida.PO
            var query = Domain.Pessoas.HorarioRepository.Queryable().Where(f => idsHorarios.Contains(f.Id)
                                                          && f.Status != StatusHorarioEnum.Cancelado
                                                          && f.FoiPago);
            return query.Any();
        }

        public IList<Horario> ObterHorariosDoMesmoPagamentoAntecipadoParaCancelar(int idHorario)
        {//TODO:duvida.PO

            var queryItemPagamentoHorario = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable();
            var horarios = queryItemPagamentoHorario.Where(itp => itp.PagamentoAntecipado.StatusPagamento != StatusPagamentoAntecipadoEnum.Negado
                                && idHorario == itp.Horario.Id
                                && itp.Horario.Status != StatusHorarioEnum.Cancelado
                                && itp.Horario.FoiPago == false).Select(itp => itp.Horario).ToList();

            return horarios.ToList();
        }

        private static List<int> ObterTodosIdsHorariosDoPagamento(int idPagamento)
        {
            return Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable()
                .Where(i => i.PagamentoAntecipado.Id == idPagamento).Select(i => ((ItemPagamentoHorario)i).Horario.Id).ToList();
        }

        private static List<Horario> ObterTodosHorariosDoPagamento(int idPagamento)
        {
            var queryHorario = Domain.Pessoas.HorarioRepository.Queryable();
            var queryItemPagamento = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable();

            var horarios = from h in queryHorario
                           join i in queryItemPagamento on h.Id equals i.Horario.Id
                           where i.PagamentoAntecipado.Id == idPagamento
                           select h;

            return horarios.ToList();
        }

        private bool PagamentoEstahNoTempoLimitePermitidoParaSerExtornadoPelaOperadora(PagamentoAntecipado pagamento)
        {
            return pagamento.DataHoraPagamento.Value.Date == Calendario.Hoje();
        }

        private PagamentoAntecipado ObterPagamentoAntecipadoDosHorarios(List<int> idsHorarios)
        {
            var queryItemPagamentoHorario = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable();
            var queryPagamento = Domain.PagamentosAntecipados.PagamentoAntecipadoRepository.Queryable();
            var pagamento = (from p in queryPagamento
                             join itp in queryItemPagamentoHorario on p.Id equals itp.PagamentoAntecipado.Id
                             where p.StatusPagamento != StatusPagamentoAntecipadoEnum.Negado
                                && idsHorarios.Contains(itp.Horario.Id)
                             select p).FirstOrDefault();

            //var pagamentos = 
            //    queryItemPagamentoHorario.Where(itp => itp.Pagamento.StatusPagamento != PagamentoOnline.Enums.StatusPagamentoEnum.Negado 
            //    && idsHorarios.Contains(itp.IdHorario)).GroupBy(itp => itp.Pagamento).Select(g => g.Key).ToList();

            return pagamento;
        }

        private int ObterIdPagamentoOnlineDosHorarios(List<int> idsHorarios)
        {
            var queryItemPagamentoHorario = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable();
            var pagamentos =
                queryItemPagamentoHorario.Where(itp => itp.PagamentoAntecipado.StatusPagamento != StatusPagamentoAntecipadoEnum.Negado
                && idsHorarios.Contains(itp.Horario.Id)).GroupBy(itp => itp.PagamentoAntecipado.Id).Select(g => g.Key).ToList();

            if (pagamentos.Count > 1)
                throw new Exception("Foi feita uma tentativa de cancelar horários de mais de um pagamento online ao mesmo tempo.");

            return pagamentos.FirstOrDefault();
        }

        //private Pagamento ObterPagamentoOnlineDoHorario(int idHorario) {
        //    var queryItemPagamentoHorario = Domain.PagamentosAntecipados.ItemPagamentoHorarioRepository.Queryable();
        //    //var queryPagamento = Domain.PagamentoOnline.PagamentoRepository.Queryable();
        //    //var pagamentos = (from p in queryPagamento
        //    //                 join itp in queryItemPagamentoHorario on p.IdPagamento equals itp.Pagamento.IdPagamento
        //    //                 where p.StatusPagamento != StatusPagamentoEnum.Negado
        //    //                    && idHorario == itp.IdHorario
        //    //                 group p by p into g 
        //    //                 select g.Key).ToList();

        //    var pagamentos = queryItemPagamentoHorario.Where(itp => itp.PagamentoAntecipado.StatusPagamento != StatusPagamentoEnum.Negado
        //                        && idHorario == itp.Horario.Id).Select(itp=> itp.PagamentoAntecipado).ToList();

        //    if (pagamentos.Count > 1)
        //        throw new Exception("Existe um horário com mais de um pagamento online válido ao mesmo tempo.");

        //    return pagamentos.FirstOrDefault();
        //}

    }
}