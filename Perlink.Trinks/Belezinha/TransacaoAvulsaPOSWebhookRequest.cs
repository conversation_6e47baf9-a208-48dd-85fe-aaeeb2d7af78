﻿using Castle.ActiveRecord;
using Perlink.Trinks.Belezinha.Enums;
using System;

namespace Perlink.Trinks.Belezinha
{

    [ActiveRecord("Transacao_Avulsa_Pos_Webhook_Request", Schema = "DBO", DynamicInsert = true, DynamicUpdate = true, Lazy = true)]
    [Serializable]
    public class TransacaoAvulsaPOSWebhookRequest : ActiveRecordBase<TransacaoAvulsaPOSWebhookRequest>
    {
        public TransacaoAvulsaPOSWebhookRequest()
        {
            StatusDaCaptura = StatusDaCapturaEnum.Nenhum;
            DataCriacao = Calendario.Agora();
        }

        [PrimaryKey(PrimaryKeyType.Native, "id_transacao_avulsa_pos_request", ColumnType = "Int32")]
        public virtual Int32 Id { get; set; }

        [Property("order_id")]
        public virtual string OrderId { get; set; }

        [Property("charge_id")]
        public virtual string ChargeId { get; set; }

        [BelongsTo("id_estabelecimento_terminal_pos", <PERSON><PERSON> = FetchWhen.OnInvoke)]
        public virtual EstabelecimentoTerminalPos EstabelecimentoTerminalPos { get; set; }     
        
        [Property("valor")]
        public virtual decimal Valor { get; set; }

        [Property("status_captura")]
        public virtual StatusDaCapturaEnum StatusDaCaptura { get; set; }

        [Property("data_criacao", NotNull = false)]
        public virtual DateTime DataCriacao { get; set; }
    }
}