﻿using Perlink.DomainInfrastructure.Services;
using System.Collections.Generic;

namespace Perlink.Trinks.Belezinha.Services
{
    public interface IMigracaoDaSiclosParaOConnectService : IService
    {
        bool MigrarEstabelecimentoParaOConnectStone(int idEstabelecimento, string secretKey);
        bool MigrarEstabelecimentoParaOConnectPagarme(int idEstabelecimento, string secretKey, string stoneCode);
        List<int> ObterIdsEstabelecimentosPorStoneCodeEDocumento(string stoneCode, string document);
    }
}
