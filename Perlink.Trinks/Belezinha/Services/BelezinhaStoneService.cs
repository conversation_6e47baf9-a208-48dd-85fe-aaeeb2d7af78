﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Transactions;
using Perlink.Shared.Auditing;
using Perlink.Shared.Exceptions;
using Perlink.Trinks.Cobranca.ContratacaoDosAdicionais;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.Financeiro.DTO;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using Perlink.Trinks.Wrapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Web;

namespace Perlink.Trinks.Belezinha.Services
{
    class BelezinhaStoneService : BaseService, IBelezinhaStoneService
    {

        public bool TemTransacaoIntegrada(int idEstabelecimento)
        {
            var temTransacaoIntegrada = Domain.Financeiro.TransacaoPOSRepository.Queryable()
                .Where(t => t.Estabelecimento.IdEstabelecimento == idEstabelecimento && t.InitiatorTransactionKey != null)
                .FirstOrDefault();

            return temTransacaoIntegrada != null;
        }
        public string NumeroContato(int idEstabelecimento)
        {
            var jaIntegrou = TemTransacaoIntegrada(idEstabelecimento);

            if (jaIntegrou)
            {
                var relacionamentoClienteCIntegracaoBelezinha = new ParametrosTrinks<string>(Pessoas.Enums.ParametrosTrinksEnum.numero_de_contato_do_suporte).ObterValor();
                return "https://api.whatsapp.com/send?phone=" + relacionamentoClienteCIntegracaoBelezinha + "&text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20a%20solução%20de%20pagamento%20integrado!%20Fiquei%20com%20algumas%20dúvidas.%20Você%20pode%20me%20ajudar?";
            }
            else
            {
                var crossSellIntegracaoBelezinha = new ParametrosTrinks<string>(Pessoas.Enums.ParametrosTrinksEnum.numero_de_contato_integracao_belezinha_cross_sell).ObterValor();
                return "https://api.whatsapp.com/send?phone=" + crossSellIntegracaoBelezinha + "&text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20a%20solução%20de%20pagamento%20integrado!%20Fiquei%20com%20algumas%20dúvidas.%20Você%20pode%20me%20ajudar?";
            }
        }

        public string FormaDeContatoCrossSell()
        {
            return new ParametrosTrinks<string>(Pessoas.Enums.ParametrosTrinksEnum.plataforma_movidesk_contato_cross_sell).ObterValor();
        }

        public bool ConcluiuIntegracao()
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            return Domain.Pessoas.EstabelecimentoRepository
                .EstabelecimentoPodeIndicarUtilizacaoDeSplitDePagamento(idEstabelecimento);
        }

        public bool ObterToggleSplit()
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var estabelecimentoConfiguracaoGeral = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.Queryable()
                .Where(ecg => ecg.IdEstabelecimento == idEstabelecimento)
                .FirstOrDefault();
            if (estabelecimentoConfiguracaoGeral != null)
            {
                return estabelecimentoConfiguracaoGeral.HabilitaSplitPagamento;
            }
            else
            {
                return false;
            }
        }

        public bool ToggleSplit(bool splitHabilitado)
        {
            int idEstabelecimento = ContextHelper.Instance.IdEstabelecimento.Value;

            var estabelecimentoConfiguracaoGeral = Domain.Pessoas.EstabelecimentoConfiguracaoGeralRepository.Queryable()
                .Where(ecg => ecg.IdEstabelecimento == idEstabelecimento)
                .FirstOrDefault();
            if (estabelecimentoConfiguracaoGeral != null)
            {
                estabelecimentoConfiguracaoGeral.HabilitaSplitPagamento = splitHabilitado;
                return true;
            }
            else
            {
                return false;
            }
        }

        public (decimal?, decimal?, decimal?) ObterValorContratacao()
        {
            int idEstabelecimento = (int)ContextHelper.Instance.IdEstabelecimento;

            int idPessoaJuridica = Domain.Pessoas.EstabelecimentoRepository.Queryable()
                .Where(e => e.IdEstabelecimento == idEstabelecimento)
                .Select(e => e.PessoaJuridica.IdPessoa)
                .FirstOrDefault();

            int idPlanoAssinatura = Domain.Cobranca.AssinaturaRepository
                .ObterAssinaturaAtivaDoEstabelecimento(idPessoaJuridica).PlanoAssinatura.IdPlano;

            decimal valor = ObterValorAdicionalBelezinha(idEstabelecimento, idPlanoAssinatura);

            var valoresAssinatura = Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory
                .ObterValoresDeContratacaoDoPlano(idEstabelecimento, idPlanoAssinatura);
            var dadosContratacaoAdicionais = Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory
                .ObterDisponibilidadesDeContratacoesDosAdicionais(idEstabelecimento);
            var valoresAdicionaisContratados = from va in valoresAssinatura.ValoresDosAdicionais
                                               join dca in dadosContratacaoAdicionais
                                               on va.IdServico equals (int)dca.ServicoAdicional
                                               where dca.JaEstaContratado == true
                                               select va.ValorMensalSemDesconto;


            decimal? valorPlanoPorMes = valoresAssinatura.ValorPorMes;
            decimal? valorAdicionaisPorMes = 0;
            foreach (var valoreAdicionalContratado in valoresAdicionaisContratados)
            {
                valorAdicionaisPorMes += valoreAdicionalContratado;
            }

            decimal? valorTotalPorMes = valorPlanoPorMes.HasValue && valorAdicionaisPorMes.HasValue
                ? valorPlanoPorMes + valorAdicionaisPorMes : null;

            decimal? valorTotalPorMesComBelezinha = valorPlanoPorMes.HasValue && valorAdicionaisPorMes.HasValue
                ? valor + valorPlanoPorMes + valorAdicionaisPorMes : null;

            return (Math.Round(valor, 2), ArredondarSeNaoForNulo(valorTotalPorMes), ArredondarSeNaoForNulo(valorTotalPorMesComBelezinha));
        }

        public bool TemAdicionalBelezinha(Estabelecimento EstabelecimentoAutenticado)
        {
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository
                  .ObterAssinaturaVigenciaEOuAtivaDaPJ(EstabelecimentoAutenticado.PessoaJuridica.IdPessoa);
            var recursoBelezinha = ServicoAdicionalEnum.BelezinhaComSplit;

            return Domain.Cobranca.AdicionalNaAssinaturaService
                .ServicoJahEstahContratadoNessaAssinatura(assinaturaAtiva.IdAssinatura, recursoBelezinha);
        }

        [TransactionInitRequired]
        public RetornoCadastroDeEstabelecimentoDTO ContratarBelezinha(CredenciamentoComStoneCodeDTO dadosCredenciamento, Estabelecimento estabelecimento)
        {

            var disponibilidadeContratacao = ObterDisponibilidadeDeContratacao(estabelecimento);

            if (!disponibilidadeContratacao.PodeContratar)
                return new RetornoCadastroDeEstabelecimentoDTO() { MensagemDeErro = disponibilidadeContratacao.MotivoIndisponibilidade };

            var configuracaoPOS = ConfiguraPOSBelezinha(dadosCredenciamento, estabelecimento);
            Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.SalvarNovoOuAtualizar(configuracaoPOS);

            var dados = new NovoAdicionalContratadoDTO()
            {
                IdEstabelecimento = estabelecimento.IdEstabelecimento,
                ServicoAdicional = ServicoAdicionalEnum.BelezinhaComSplit,
                IdFormaDeContratacao = (int)FormaDeContratacaoDoAdicionalEnum.SomenteIntegracaoStone2_0,
            };

            if (dadosCredenciamento.ContratandoAutoatendimentoCompleto)
            {
                dados.DescontoAtribuido = new DescontoNoAdicionalContratadoDTO()
                {
                    Percentual = 100,
                    QuantidadeDeDescontosProgramados = 9999,
                };

                dados.IdentificacaoMotivo = "Cliente contratação Nova Jornada + Autoatendimento completo";
                dados.ComentarioMotivo = "Cliente contratação Nova Jornada + Autoatendimento completo";
            }
            else
            {
                dados.IdentificacaoMotivo = "Cliente contratação Nova Jornada";
                dados.ComentarioMotivo = "Cliente contratação Nova Jornada";
            }

            Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory
                .SalvarNovaContratacaoDeAdicional(dados);

            var credenciamentoComStone = new CredenciamentoComStoneCode(dadosCredenciamento, estabelecimento.IdEstabelecimento);
            Domain.Belezinha.CredenciamentoComStoneCodeRepository.SaveNew(credenciamentoComStone);

            if (dadosCredenciamento.ContratandoAutoatendimentoCompleto)
            {
                var motivo = "Cliente contratação Nova Jornada + Autoatendimento completo";
                var idAssinatura = Domain.Cobranca.AssinaturaRepository.ObterCodigoAssinaturaAtivaEmVigenciaDoEstabelecimento(estabelecimento.PessoaJuridica.IdPessoa);
                var autoatendimentoJaContratadoComoCheckIn = Domain.Cobranca.AdicionalNaAssinaturaRepository
                    .ExisteAtivoNaAssinaturaPorServicoAdicionalEFormaContratacao(idAssinatura, ServicoAdicionalEnum.Autoatendimento, FormaDeContratacaoDoAdicionalEnum.CheckIn);

                if (!autoatendimentoJaContratadoComoCheckIn)
                {
                    var dadosAutoatendimentoCompleto = new NovoAdicionalContratadoDTO()
                    {
                        IdEstabelecimento = estabelecimento.IdEstabelecimento,
                        ServicoAdicional = ServicoAdicionalEnum.Autoatendimento,
                        IdentificacaoMotivo = motivo,
                        ComentarioMotivo = motivo,
                        IdFormaDeContratacao = (int)FormaDeContratacaoDoAdicionalEnum.CheckInECheckout,
                    };

                    Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory.SalvarNovaContratacaoDeAdicional(dadosAutoatendimentoCompleto);
                }
                else
                {
                    var dadosAutoatendimentoCompleto = new EdicaoFormaDeContratacaoAdicionalDTO()
                    {
                        IdEstabelecimento = estabelecimento.IdEstabelecimento,
                        ServicoAdicional = ServicoAdicionalEnum.Autoatendimento,
                        IdentificacaoMotivo = motivo,
                        ComentarioMotivo = motivo,
                        IdFormaDeContratacao = (int)FormaDeContratacaoDoAdicionalEnum.CheckInECheckout,
                    };

                    Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory.EditarFormaDeContratacaoDoAdicional(dadosAutoatendimentoCompleto);
                }
            }

            EnviarEmailSobreContratacaoParaCrossSell(estabelecimento, dadosCredenciamento.ContratandoAutoatendimentoCompleto);

            return new RetornoCadastroDeEstabelecimentoDTO { Sucesso = true };
        }

        private void EnviarEmailSobreContratacaoParaCrossSell(Estabelecimento estabelecimento, bool contratandoAutoatendimentoCompleto = false)
        {
            try
            {
                var textoTipoContratacao = contratandoAutoatendimentoCompleto ? "[Contratacao-Belezinha] + [Contratacao-Autoatendimento-Completo]" : "[Contratacao-Belezinha]";
                var assunto = $"{textoTipoContratacao} - Nova contratação de adicional no estabelecimento {estabelecimento.NomeDeExibicaoNoPortal}";
                var remetente = Pessoas.Services.EnvioEmailService.ObterRemetentePadrao();
                var destinatarios = new ParametrosTrinks<string>(ParametrosTrinksEnum.emails_aviso_contratacao_belezinha).ObterValor();
                var pathQuery = HttpContext.Current.Request.Url.PathAndQuery;
                var url = HttpContext.Current.Request.Url.AbsoluteUri.Replace(pathQuery, "/");
                var paragrafos = new List<string>
                {
                    $"Nova contratação de adicional no estabelecimento {estabelecimento.NomeDeExibicaoNoPortal}",
                    $"<b>Link da vitrine: <a href={url}/Perlink/VitrineEstabelecimentos?idEstabelecimento={estabelecimento.IdEstabelecimento}>Clique aqui</a> </b>"
                };

                var corpoEmail = string.Join("<br /><br />", paragrafos);

                foreach (var destinatario in destinatarios.Split(';'))
                    Domain.Pessoas.EnvioEmailService.DispararEmail(assunto, corpoEmail, remetente, new MailAddress(destinatario, "CrossSellTrinks"), null);

            }
            catch (Exception ex)
            {
                LogService<BelezinhaStoneService>.Error("[Contratacao-Belezinha] - Não foi possível enviar o e-mail de erro. Mensagem de erro: " + ex.Formatada());
                return;
            }
        }

        private EstabelecimentoConfiguracaoPOS ConfiguraPOSBelezinha(CredenciamentoComStoneCodeDTO dadosCredenciamento, Estabelecimento estabelecimento)
        {
            var configuracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository
                .ObterConfiguracaoPorEstabelecimento(estabelecimento.IdEstabelecimento);

            if (configuracaoPOS == null)
                configuracaoPOS = new EstabelecimentoConfiguracaoPOS();

            configuracaoPOS.AutomaticRate = dadosCredenciamento.TaxaAutomatica;
            configuracaoPOS.SpotRate = dadosCredenciamento.TaxaPontual;
            configuracaoPOS.AffiliationKey = dadosCredenciamento.StoneCode;
            configuracaoPOS.CNPJConfiguracao = dadosCredenciamento.DocumentoCpfOuCnpj;
            configuracaoPOS.DataAlteracao = DateTime.Now;

            configuracaoPOS.Estabelecimento = estabelecimento;

            configuracaoPOS.TipoPOS = Domain.Pessoas.TipoPOSRepository
                .Load((int)SubadquirenteEnum.ConnectStone);
            // A partir desse ponto fica claro que eu já entreguei a toalha
            var IdPessoa = estabelecimento.PessoaJuridica.IdPessoa;
            var PessoaFisicaQueAlterou = Domain.Pessoas.PessoaFisicaRepository.Queryable()
                .Where(p => p.IdPessoa == IdPessoa)
                .FirstOrDefault();
            if (PessoaFisicaQueAlterou == null)
            {
                PessoaFisicaQueAlterou = new PessoaFisica()
                {
                    IdPessoa = IdPessoa
                };
                Domain.Pessoas.PessoaFisicaRepository.SaveNew(PessoaFisicaQueAlterou);
            }

            configuracaoPOS.PessoaFisicaQueAlterou = PessoaFisicaQueAlterou;

            return configuracaoPOS;
        }

        public decimal ObterValorAdicionalBelezinha(int idEstabelecimento, int idPlanoAssinatura)
        {
            int somenteIntegraçãoStoneSiclos = 3;

            return Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory
                .ObterValorDeCobrancaDoAdicional(idEstabelecimento,
                                                 idPlanoAssinatura,
                                                 ServicoAdicionalEnum.BelezinhaComSplit,
                                                 somenteIntegraçãoStoneSiclos).Valor;
        }

        public DisponibilidadeDeContratacaoDoAdicionalDTO ObterDisponibilidadeDeContratacao(Estabelecimento estabelecimento)
        {
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository
                .ObterAssinaturaVigenciaEOuAtivaDaPJ(estabelecimento.PessoaJuridica.IdPessoa);

            var servicoAdicional = Domain.Cobranca.ServicoTrinksRepository
                .Obter(ServicoAdicionalEnum.BelezinhaComSplit);

            var contratacao = new DisponibilidadeDeContratacao(estabelecimento, assinaturaAtiva);

            var disponibilidadeContratacao = contratacao
                .ObterDisponibilidade(servicoAdicional, assinaturaAtiva.PlanoAssinatura, false);

            return disponibilidadeContratacao;
        }

        public bool EhApenasIntegracao(Estabelecimento estabelecimento)
        {
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository
            .ObterAssinaturaVigenciaEOuAtivaDaPJ(estabelecimento.PessoaJuridica.IdPessoa);

            var adicionaisContratados = Domain.Cobranca.AdicionalNaAssinaturaService.ObterAdicionaisAtivosNaAssinatura(assinaturaAtiva);

            var ehApenasIntegracao = adicionaisContratados.FirstOrDefault(a => a.Servico.IdServico == 8 && a.Ativo);

            return ehApenasIntegracao?.FormaDeContratacao.Id == (int)FormaDeContratacaoDoAdicionalEnum.SomenteIntegracaoStoneSiclos ||
                   ehApenasIntegracao?.FormaDeContratacao.Id == (int)FormaDeContratacaoDoAdicionalEnum.SomenteIntegracaoStone2_0;
        }
        private decimal? ArredondarSeNaoForNulo(decimal? value) => value.HasValue ? Math.Round(value.Value, 2) : (decimal?)null;

    }
}
