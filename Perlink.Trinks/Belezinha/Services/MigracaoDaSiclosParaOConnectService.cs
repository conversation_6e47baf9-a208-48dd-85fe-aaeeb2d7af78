﻿using Perlink.DomainInfrastructure.Services;
using Perlink.DomainInfrastructure.Validation;
using Perlink.Trinks.Cobranca.DTO;
using Perlink.Trinks.Cobranca.Enums;
using Perlink.Trinks.ControleDeFuncionalidades.Enums;
using Perlink.Trinks.Pessoas;
using Perlink.Trinks.Pessoas.Enums;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Belezinha.Services
{
    public class MigracaoDaSiclosParaOConnectService : BaseService, IMigracaoDaSiclosParaOConnectService
    {
        public IReadOnlyDictionary<FormaDeContratacaoDoAdicionalEnum, FormaDeContratacaoDoAdicionalEnum> DeParaDosIdsDasFormasDePagamentoSiclosComConnectStone { get; }
        public IReadOnlyDictionary<FormaDeContratacaoDoAdicionalEnum, FormaDeContratacaoDoAdicionalEnum> DeParaDosIdsDasFormasDePagamentoSiclosComConnectPagarme { get; }

        public MigracaoDaSiclosParaOConnectService()
        {
            DeParaDosIdsDasFormasDePagamentoSiclosComConnectStone = new Dictionary<FormaDeContratacaoDoAdicionalEnum, FormaDeContratacaoDoAdicionalEnum>
            {
                { FormaDeContratacaoDoAdicionalEnum.AluguelMaquinaPOSIntegracaoStoneSiclos, FormaDeContratacaoDoAdicionalEnum.AluguelMaquinaPOSIntegracaoStone2_0 },
                { FormaDeContratacaoDoAdicionalEnum.SomenteIntegracaoStoneSiclos, FormaDeContratacaoDoAdicionalEnum.SomenteIntegracaoStone2_0 },
            };

            DeParaDosIdsDasFormasDePagamentoSiclosComConnectPagarme = new Dictionary<FormaDeContratacaoDoAdicionalEnum, FormaDeContratacaoDoAdicionalEnum>
            {
                { FormaDeContratacaoDoAdicionalEnum.AluguelMaquinaPOSIntegracaoStoneSiclos, FormaDeContratacaoDoAdicionalEnum.AluguelMaquinaPOSIntegracaoPagarMe2_0 },
                { FormaDeContratacaoDoAdicionalEnum.SomenteIntegracaoStoneSiclos, FormaDeContratacaoDoAdicionalEnum.AluguelMaquinaPOSIntegracaoPagarMe2_0 },
            };
        }

        public bool MigrarEstabelecimentoParaOConnectPagarme(int idEstabelecimento, string secretKey, string stoneCode)
        {
            if (!EstabelecimentoUtilizaSiclos(idEstabelecimento))
            {
                AdicionarValidacao("Estabelecimento informado não utiliza Stone Siclos!");
                return false;
            }

            var idPessoaDoEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterIdPessoaDoEstabelecimento(idEstabelecimento);
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaDoEstabelecimento(idPessoaDoEstabelecimento);
            if (assinaturaAtiva == null)
            {
                AdicionarValidacao("Estabelecimento não possui assinatura ativa!");
                return false;
            }
            var ultimaAssinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(idPessoaDoEstabelecimento);                     

            var idPlanoAssinatura = ultimaAssinatura.PlanoAssinatura.IdPlano;
            var valoresParaContratacaoDoPlanoDto = Domain.Cobranca.AssinaturaService.ObterValoresParaContratacaoDoPlano(assinaturaAtiva, idPlanoAssinatura);
            var idFormaContratacaoBelezinha = ObterIdFormaContratacaoBelezinha(valoresParaContratacaoDoPlanoDto);

            if (!idFormaContratacaoBelezinha.HasValue)
            {
                AdicionarValidacao("Falha ao obter a forma de contratação atual!");
                return false;
            }
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var estabelecimentoConfiguracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);

            var idFormaContratacaoConnectPagarme = DeParaDosIdsDasFormasDePagamentoSiclosComConnectPagarme[(FormaDeContratacaoDoAdicionalEnum)idFormaContratacaoBelezinha.Value];

            EditarFormaDeContratacao(estabelecimento, estabelecimentoConfiguracaoPOS, idEstabelecimento, (int)idFormaContratacaoConnectPagarme, secretKey);

            return true;
        }

        public bool MigrarEstabelecimentoParaOConnectStone(int idEstabelecimento, string secretKey)
        {
            if (!EstabelecimentoUtilizaSiclos(idEstabelecimento))
            {
                AdicionarValidacao("Estabelecimento informado não utiliza Stone Siclos!");
                return false;
            }

            var idPessoaDoEstabelecimento = Domain.Pessoas.EstabelecimentoRepository.ObterIdPessoaDoEstabelecimento(idEstabelecimento);
            var assinaturaAtiva = Domain.Cobranca.AssinaturaRepository.ObterAssinaturaAtivaDoEstabelecimento(idPessoaDoEstabelecimento);
            if (assinaturaAtiva == null)
            {
                AdicionarValidacao("Estabelecimento não possui assinatura ativa!");
                return false;
            }
            var ultimaAssinatura = Domain.Cobranca.AssinaturaRepository.ObterUltimaAssinatura(idPessoaDoEstabelecimento);


            if (!ValidarHabilitacaoSplitPagamento(idEstabelecimento))
            {
                AdicionarValidacao("Estabelecimento utiliza split de pagamento!");
                return false;
            }

            var idPlanoAssinatura = ultimaAssinatura.PlanoAssinatura.IdPlano;
            var valoresParaContratacaoDoPlanoDto = Domain.Cobranca.AssinaturaService.ObterValoresParaContratacaoDoPlano(assinaturaAtiva, idPlanoAssinatura);
            var idFormaContratacaoBelezinha = ObterIdFormaContratacaoBelezinha(valoresParaContratacaoDoPlanoDto);

            if (!idFormaContratacaoBelezinha.HasValue)
            {
                AdicionarValidacao("Falha ao obter a forma de contratação atual!");
                return false;
            }
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            var estabelecimentoConfiguracaoPOS = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);



            var idFormaContratacaoConnectStone = DeParaDosIdsDasFormasDePagamentoSiclosComConnectStone[(FormaDeContratacaoDoAdicionalEnum)idFormaContratacaoBelezinha.Value];

            EditarFormaDeContratacao(estabelecimento, estabelecimentoConfiguracaoPOS, idEstabelecimento, (int)idFormaContratacaoConnectStone, secretKey);

            return true;
        }

        public List<int> ObterIdsEstabelecimentosPorStoneCodeEDocumento(string stoneCode, string documento)
        {
            return Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterIdsDosEstabelecimentosPorStoneCodeEDocumento(stoneCode, documento);
        }

        #region Metodos privados

        private bool EstabelecimentoUtilizaSiclos(int idEstabelecimento)
        {
            var estabelecimentoConfiguracaoPos = Domain.Pessoas.EstabelecimentoConfiguracaoPOSRepository.ObterConfiguracaoPorEstabelecimento(idEstabelecimento);
            if (estabelecimentoConfiguracaoPos == null)
                AdicionarValidacao("Estabelecimento não utiliza Belezinha!");

            return estabelecimentoConfiguracaoPos.TipoPOS.Id == (int)SubadquirenteEnum.StoneSiclos;
        }

        private bool ValidarHabilitacaoSplitPagamento(int idEstabelecimento)
        {
            var estabelecimento = Domain.Pessoas.EstabelecimentoRepository.Load(idEstabelecimento);
            return !estabelecimento.EstabelecimentoConfiguracaoGeral.HabilitaSplitPagamento;
        }

        private void AdicionarValidacao(string mensagem)
        {
            ValidationHelper.Instance.AdicionarItemValidacao(mensagem);
        }

        private int? ObterIdFormaContratacaoBelezinha(ValoresDaAssinaturaDoPlanoDTO valoresParaContratacaoDoPlanoDto)
        {
            return valoresParaContratacaoDoPlanoDto.ValoresDosAdicionais
                .FirstOrDefault(f => f.IdServico == (int)ServicoAdicionalEnum.BelezinhaComSplit)?.IdFormaDeContratacao;
        }

        private void EditarFormaDeContratacao(Estabelecimento estabelecimento, EstabelecimentoConfiguracaoPOS estabelecimentoConfiguracaoPOS, int idEstabelecimento, int idFormaContratacaoConnectStone, string secretKey, string stoneCode = null)
        {
            Domain.Cobranca.AlteracaoNaAssinaturaAreaPerlinkStory.EditarFormaDeContratacaoDoAdicional(new EdicaoFormaDeContratacaoAdicionalDTO()
            {
                ComentarioMotivo = $"[MIGRAÇÃO] - Migrando estabelecimento {estabelecimento.NomeDeExibicaoNoPortal} da Siclos para o Connect Stone",
                ConfiguracoesPos = new ConfiguracoesPosDTO()
                {
                    AffiliationKey = stoneCode == null ? estabelecimentoConfiguracaoPOS.AffiliationKey : stoneCode,
                    ConcluiuConfiguracao = estabelecimentoConfiguracaoPOS.ConcluiuConfiguracao,
                    DocumentoCpfCnpj = estabelecimentoConfiguracaoPOS.CNPJConfiguracao,
                    EhParaConfigurarTrinksPay = true,
                    IdEstabelecimento = idEstabelecimento,
                    SecretKey = secretKey,
                    MerchantKey = estabelecimentoConfiguracaoPOS.MerchantKey,
                    QueroImprimirMeusComprovantes = false,
                    TaxaAutomatica = estabelecimentoConfiguracaoPOS.AutomaticRate,
                    TaxaPontual = estabelecimentoConfiguracaoPOS.SpotRate
                },
                IdEstabelecimento = idEstabelecimento,
                IdFormaDeContratacao = idFormaContratacaoConnectStone,
                IdentificacaoMotivo = "Outros",
                ServicoAdicional = ServicoAdicionalEnum.BelezinhaComSplit
            });
        }

        #endregion
    }
}
