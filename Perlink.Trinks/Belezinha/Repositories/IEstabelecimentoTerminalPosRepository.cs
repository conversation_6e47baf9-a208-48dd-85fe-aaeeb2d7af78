﻿using Perlink.Trinks.Belezinha.Filters;
using System.Collections.Generic;

namespace Perlink.Trinks.Belezinha.Repositories
{
    public partial interface IEstabelecimentoTerminalPosRepository
    {
        List<EstabelecimentoTerminalPos> ObterEstabelecimentoTerminalPos(FiltrosEstabelecimentoTerminalPosDTO filtros);

        EstabelecimentoTerminalPos ObterPorId(int idEstabelecimentoTerminalPos);

        bool PossuiEstabelecimentoTerminalPosVinculadaAoAutoatendimento(int idEstabelecimento);

        EstabelecimentoTerminalPos ObterEstabelecimentoTerminalPosVinculadaAoAutoatendimento(int idEstabelecimento);

        EstabelecimentoTerminalPos ObterEstabelecimentoTerminalPosPorSerialNumber(string serialNumber);
    }
}
