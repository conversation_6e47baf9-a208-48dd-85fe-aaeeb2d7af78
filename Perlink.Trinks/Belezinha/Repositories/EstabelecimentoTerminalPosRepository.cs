﻿using Perlink.Trinks.Belezinha.Filters;
using Perlink.Trinks.DataQuery;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.Trinks.Belezinha.Repositories
{
    public partial class EstabelecimentoTerminalPosRepository : IEstabelecimentoTerminalPosRepository
    {

        public List<EstabelecimentoTerminalPos> ObterEstabelecimentoTerminalPos(FiltrosEstabelecimentoTerminalPosDTO filtros)
        {
            var estabelecimentoTerminalPos = Domain.Belezinha.EstabelecimentoTerminalPosRepository.Queryable()
                .Where(eb =>
                    eb.EstabelecimentoConfigPos.Id == filtros.IdEstabelecimentoConfigPos &&
                    eb.EstabelecimentoConfigPos.ConcluiuConfiguracao);

            if (filtros.IdTipoPos != 0)
                estabelecimentoTerminalPos = estabelecimentoTerminalPos.Where(eb => eb.TipoPos.Id == filtros.IdTipoPos);

            return estabelecimentoTerminalPos.ApplyPagination(filtros).ToList();
        }


        public EstabelecimentoTerminalPos ObterPorId(int idEstabelecimentoTerminalPos) =>
            Domain.Belezinha.EstabelecimentoTerminalPosRepository.Queryable()
                .FirstOrDefault(eb =>
                    eb.Id == idEstabelecimentoTerminalPos &&
                    eb.EstabelecimentoConfigPos.ConcluiuConfiguracao);

        public bool PossuiEstabelecimentoTerminalPosVinculadaAoAutoatendimento(int idEstabelecimento)
        {
            return Queryable().Any(eb =>
                    eb.EstabelecimentoConfigPos.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                    eb.VinculadaAoAutoatendimento);
        }

        public EstabelecimentoTerminalPos ObterEstabelecimentoTerminalPosVinculadaAoAutoatendimento(int idEstabelecimento)
        {
            return Queryable().FirstOrDefault(eb =>
                    eb.EstabelecimentoConfigPos.Estabelecimento.IdEstabelecimento == idEstabelecimento &&
                    eb.VinculadaAoAutoatendimento);
        }

        public EstabelecimentoTerminalPos ObterEstabelecimentoTerminalPosPorSerialNumber(string serialNumber)
        {
            return Queryable().FirstOrDefault(eb =>
                    eb.SerialNumber == serialNumber);
        }
    }
}
