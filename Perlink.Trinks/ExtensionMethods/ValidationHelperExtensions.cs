﻿using Perlink.DomainInfrastructure.Validation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Perlink.Trinks.ExtensionMethods
{
    public static class ValidationHelperExtensions
    {
        public static ValidationHelper AddError(this ValidationHelper vh, string message)
        {
            if (vh == null) return vh;

            vh.AdicionarItemValidacao(message);
            return vh;
        }

        public static ValidationHelper MustBeTrue(this ValidationHelper vh, bool condition, string message)
            => condition? vh : vh.AddError(message);

        public static ValidationHelper MustBeNotNull(this ValidationHelper vh, object obj, string message)
            => MustBeTrue(vh, obj != null, message);

        #region String Validations
        public static ValidationHelper MustBeNotNullOrWhitespace(this ValidationHelper vh, string str, string message)
            => MustBeTrue(vh, !string.IsNullOrWhiteSpace(str), message);
        #endregion

        #region Comparison Validations
        public static ValidationHelper MustEqual<T>(this ValidationHelper vh, T e1, T e2, string message) where T : IEquatable<T>
            => MustBeTrue(vh, e1.Equals(e2), message);

        public static ValidationHelper MustBeGreaterThan<T>(this ValidationHelper vh, T value, T minValue, string message) where T : IComparable<T>
            => MustBeTrue(vh, value.CompareTo(minValue) > 0, message);

        public static ValidationHelper MustBeGreaterThanOrEqualTo<T>(this ValidationHelper vh, T value, T minValue, string message) where T : IComparable<T>
            => MustBeTrue(vh, value.CompareTo(minValue) >= 0, message);

        public static ValidationHelper MustBeGreaterThanZero<T>(this ValidationHelper vh, T value, string message) where T : IComparable<T>
            => MustBeGreaterThan(vh, value, default, message);
        #endregion
    }
}
