using Perlink.Shared.Text;
using Perlink.Shared.Validation;
using Perlink.Trinks.Pessoas.VO;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Security;
using RES = Perlink.Trinks.Resources.ExtensionMethods;

namespace Perlink.Trinks.ExtensionMethods
{

    public static class StringExtension
    {

        #region Depois tem que colocar este código em Perlink.Shared.Text.Formatter

        public static string PluralizarPalavra(this string palavra, int? quantidade = null)
        {
            if (quantidade > 1 || quantidade < -1)
                palavra = Pluralizar(palavra);
            return palavra;
        }

        private static string Pluralizar(string palavra)
        {
            if (PalavraPluraliza(palavra))
                palavra = palavra + "s";//+"s" provisório, até haver a necessidade de encontrarmos um framework de pluralização ou fazer os tratamentos de irregulares
            return palavra;
        }

        private static List<string> palavrasQueNaoPluralizam { get { return new List<string> { "UN", "cm", "mL", "g", "mm", "ml" }; } }

        private static bool PalavraPluraliza(string palavra)
        {
            return !palavrasQueNaoPluralizam.Contains(palavra);
        }

        #endregion Depois tem que colocar este código em Perlink.Shared.Text.Formatter

        public static String MensagemPrecoEmReais(this string preco, bool permiteExibirPreco, Int32 precoFixo)
        {
            if (permiteExibirPreco)
            {
                return precoFixo == 1 ? preco : String.Format(RES.StringExtension.APartirDe, preco);
            }
            return RES.StringExtension.Consultar;
        }

        public static string NomeReduzido(this string nomeCompleto)
        {
            nomeCompleto = nomeCompleto ?? "";
            var splitNomeCompleto = nomeCompleto.Split(' ');
            var nomeReduzido = string.Empty;

            if (splitNomeCompleto.Length >= 3 && splitNomeCompleto[1].Length <= 3 && splitNomeCompleto[2].Length > 0)
                nomeReduzido = string.Join(" ", splitNomeCompleto.Take(3));
            else if (splitNomeCompleto.Length > 1 && splitNomeCompleto[1].Length > 0)
                nomeReduzido = string.Join(" ", splitNomeCompleto.Take(2));
            else
                nomeReduzido = splitNomeCompleto.FirstOrDefault();

            return nomeReduzido;
        }

        public static string ObterPrimeiroNome(this string nomeCompleto)
        {
            if (string.IsNullOrWhiteSpace(nomeCompleto))
                return nomeCompleto;
            return nomeCompleto.Trim().Split(' ').FirstOrDefault();
        }


        public static string ObterUltimoNome(this string nomeCompleto)
        {
            if (string.IsNullOrWhiteSpace(nomeCompleto))
                return nomeCompleto;
            return nomeCompleto.Trim().Split(' ').LastOrDefault();
        }

        public static string FormatarCNPJ(this string valor)
        {
            return valor.Formatar("99.999.999/9999-99");
        }

        public static string FormatarCPF(this string valor)
        {
            return valor.Formatar("999.999.999-99");
        }

        public static string MascararNumeroCartao(this string numero)
        {
            if (numero == null) return "";

            return $"**** **** **** {numero.Substring(12)}";
        }

        public static String Formatar(this String valor, String mascara)
        {
            if (valor == null)
                return null;
            if (mascara == null)
                return valor;
            var dado = new StringBuilder();
            // remove caracteres nao numericos
            foreach (var c in valor.Where(Char.IsNumber))
            {
                dado.Append(c);
            }

            var indMascara = mascara.Length;
            var indCampo = dado.Length;

            for (; indCampo > 0 && indMascara > 0;)
            {
                if (mascara[--indMascara] == '9')
                    indCampo--;
            }

            var saida = new StringBuilder();
            for (; indMascara < mascara.Length; indMascara++)
                saida.Append((mascara[indMascara] == '9') ? dado[indCampo++] : mascara[indMascara]);

            return saida.ToString();
        }

        public static String FormatarTelefone(String ddd, String numero)
        {
            return String.Format("({0}) {1}-{2}", ddd, numero.Replace("-", String.Empty).Substring(0, numero.Length - 4), numero.Replace("-", String.Empty).Substring(numero.Length - 4));
        }

        public static DateTime? ToDateTime(this String data)
        {
            if (String.IsNullOrEmpty(data))
                return null;
            DateTime dataConvertida;
            if (DateTime.TryParse(data, out dataConvertida))
                return dataConvertida;

            return null;
        }

        public static String ToGeneroExtenso(this String valor)
        {
            return Genero.ObterPorCodigo(valor).Descricao();
        }

        public static string SomenteNumeros(this string texto)
        {
            if (texto == null)
                return null;
            return new string(texto.Where(Char.IsDigit).ToArray());
        }

        public static string SomenteNumerosELetras(this string texto)
        {
            if (texto == null)
                return null;
            return new string(texto.Where(Char.IsLetterOrDigit).ToArray());
        }

        public static string SomenteLetras(this string texto)
        {
            if (texto == null)
                return null;
            return new string(texto.Where(Char.IsLetter).ToArray());
        }

        /// <summary>
        /// Transforma uma lista de strings em uma unica string separada por ',' e com o ' e ' separando o ultimo item da lista
        /// </summary>
        /// <param name="lista">a lista para ser transformada</param>
        /// <returns>A string no formato: barba, cabelo e unhas</returns>
        public static String ListaFormatadaComSeparador(this IEnumerable<String> lista)
        {
            return lista.ToArray().ListaFormatadaComSeparador();
        }

        /// <summary>
        /// Remove de uma lista os itens que foram inseridos como Null ou Empty
        /// </summary>
        /// <param name="minhaLista">lista com os itens</param>
        public static void RemoverItensEmBrancoDeListas(this List<string> minhaLista)
        {
            minhaLista.RemoveAll(String.IsNullOrEmpty);
        }

        public static String ListaFormatadaComSeparador(this String[] lista)
        {
            var listaFinal = String.Empty;

            for (int i = 0; i < lista.Length; i++)
            {
                var valor = lista[i];
                string separador;

                if (i == (lista.Length - 2))
                    separador = " e ";
                else if (i == (lista.Length - 1))
                    separador = String.Empty;
                else
                    separador = ", ";

                listaFinal += valor + separador;
            }

            return listaFinal;
        }

        public static String RemoverAcentos(this string valor)
        {
            if (valor == null)
                return null;
            var strsemAcentos = valor;
            strsemAcentos = Regex.Replace(strsemAcentos, "[áàâãª]", "a");
            strsemAcentos = Regex.Replace(strsemAcentos, "[ÁÀÂÃ]", "A");
            strsemAcentos = Regex.Replace(strsemAcentos, "[éèê]", "e");
            strsemAcentos = Regex.Replace(strsemAcentos, "[ÉÈÊ]", "e");
            strsemAcentos = Regex.Replace(strsemAcentos, "[íìî]", "i");
            strsemAcentos = Regex.Replace(strsemAcentos, "[ÍÌÎ]", "I");
            strsemAcentos = Regex.Replace(strsemAcentos, "[óòôõº]", "o");
            strsemAcentos = Regex.Replace(strsemAcentos, "[ÓÒÔÕ]", "O");
            strsemAcentos = Regex.Replace(strsemAcentos, "[úùû]", "u");
            strsemAcentos = Regex.Replace(strsemAcentos, "[ÚÙÛ]", "U");
            strsemAcentos = Regex.Replace(strsemAcentos, "[ç]", "c");
            strsemAcentos = Regex.Replace(strsemAcentos, "[Ç]", "C");
            return strsemAcentos;
        }

        public static String RemoverFormatacaoCPFeCPNJ(this string valor)
        {
            if (String.IsNullOrWhiteSpace(valor))
                return String.Empty;
            return valor.Replace(".", String.Empty).Replace("-", String.Empty).Replace("/", String.Empty).Trim();
        }

        public static String RemoverFormatacaoTelefone(this string valor)
        {
            return valor.Replace("-", String.Empty).Replace("(", String.Empty).Replace(")", String.Empty).Replace("R.", String.Empty).Trim();
        }

        public static String RemoverQuebrasDeLinha(this string valor)
        {
            return Formatter.RemoverQuebrasDeLinha(valor);
        }

        public static string RemoverPontoeVirgula(this string texto)
        {
            return Formatter.RemoverPontoeVirgula(texto);
        }

        public static String FormatarParaCodigoFoto(this string valor)
        {
            return valor.Replace(" ", String.Empty).RemoverAcentos().ToLower();
        }

        public static String FormatarParaHtml(this string valor)
        {
            return valor.Replace("\r\n", "<br>")
                  .Replace("\r", "<br>")
                  .Replace("\n", "<br>");
        }

        public static bool EmailValido(this string email)
        {
            if (String.IsNullOrEmpty(email))
                return false;
            return Validator.ValidateEmail(email);
        }

        public static bool ValidarCPF(this string cpf)
        {
            if (cpf == null)
                return false;

            cpf = cpf.RemoverFormatacaoCPFeCPNJ();

            if (cpf.Length != 11)
                return false;

            switch (cpf)
            {
                case "00000000000":
                case "11111111111":
                case "22222222222":
                case "33333333333":
                case "44444444444":
                case "55555555555":
                case "66666666666":
                case "77777777777":
                case "88888888888":
                case "99999999999":
                    return false;
            }

            int soma = 0;
            for (int i = 0, j = 10; i < 9; i++, j--)
            {
                int d;
                if (!Int32.TryParse(cpf[i].ToString(), out d))
                    return false;
                soma += d * j;
            }

            var resto = soma % 11;

            var digito = (resto < 2 ? 0 : 11 - resto).ToString();
            var prefixo = cpf.Substring(0, 9) + digito;

            soma = 0;
            for (int i = 0, j = 11; i < 10; i++, j--)
                soma += Int32.Parse(prefixo[i].ToString()) * j;

            resto = soma % 11;
            digito += (resto < 2 ? 0 : 11 - resto).ToString();

            return cpf.EndsWith(digito);
        }

        public static bool ValidarCnpj(this string cnpj)
        {
            int[] multiplicador1 = new int[12] { 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            int[] multiplicador2 = new int[13] { 6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };
            int soma;
            int resto;
            string digito;
            string tempCnpj;

            cnpj = cnpj.Trim();
            cnpj = cnpj.RemoverFormatacaoCPFeCPNJ();

            if (cnpj.Length != 14)
                return false;
            else if (cnpj == "00000000000000")
                return false;

            tempCnpj = cnpj.Substring(0, 12);

            soma = 0;
            for (int i = 0; i < 12; i++)
                soma += int.Parse(tempCnpj[i].ToString()) * multiplicador1[i];

            resto = (soma % 11);
            if (resto < 2)
                resto = 0;
            else
                resto = 11 - resto;

            digito = resto.ToString();

            tempCnpj = tempCnpj + digito;
            soma = 0;
            for (int i = 0; i < 13; i++)
                soma += int.Parse(tempCnpj[i].ToString()) * multiplicador2[i];

            resto = (soma % 11);
            if (resto < 2)
                resto = 0;
            else
                resto = 11 - resto;

            digito = digito + resto.ToString();

            return cnpj.EndsWith(digito);
        }

        public static String Left(this string texto, Int32 tamanho)
        {
            int tamanhoAtual = texto.Length;
            int tamanhoReduzir = tamanhoAtual < tamanho ? tamanhoAtual : tamanho;

            return texto.Substring(0, tamanhoReduzir);
        }

        public static string Right(this string texto, int tamanho)
        {
            if (string.IsNullOrEmpty(texto))
            {
                texto = string.Empty;
            }
            else if (texto.Length > tamanho)
            {
                texto = texto.Substring(texto.Length - tamanho, tamanho);
            }

            //Return the string
            return texto;
        }

        public static String LarguraFixa(this string texto, Int32 tamanho, char preenchimento = ' ')
        {
            if (String.IsNullOrEmpty(texto))
                texto = String.Empty;
            return texto.PadLeft(tamanho, preenchimento).Substring(0, tamanho);
        }

        public static String LarguraFixaComEspacamentoADireita(this string texto, Int32 tamanho, char preenchimento = ' ')
        {
            if (String.IsNullOrEmpty(texto))
                texto = String.Empty;
            return texto.PadRight(tamanho, preenchimento).Substring(0, tamanho);
        }

        public static List<String> SplitEmBlocos(this string texto, int tamanhoBloco, bool incluirSobras = true)
        {
            var partes = new List<String>();
            var posicao = 0;
            var total = texto.Length;
            while (total >= posicao + tamanhoBloco)
            {
                partes.Add(texto.Substring(posicao, tamanhoBloco));
                posicao += tamanhoBloco;
            }
            if (incluirSobras && (total > posicao))
                partes.Add(texto.Substring(posicao));

            return partes;
        }

        /// <summary>
        /// Metodo utilizado apenas para embaralhar uma string
        /// </summary>
        /// <param name="stringAtual"></param>
        /// <returns>uma nova string baseada na base64 bits</returns>
        public static string EncodeToBase64String(this string stringAtual)
        {
            var bytesDaString = Encoding.ASCII.GetBytes(stringAtual);
            return Convert.ToBase64String(bytesDaString);
        }

        /// <summary>
        /// Metodo utilizado para transformar de uma string na base64 para uma string comum
        /// </summary>
        /// <param name="stringAtualNaBase64"></param>
        /// <returns></returns>
        public static string DecodeBase64StringToNormalString(this string stringAtualNaBase64)
        {
            var bytesDaString = Convert.FromBase64String(stringAtualNaBase64);
            return Encoding.ASCII.GetString(bytesDaString);
        }

        /// <summary>
        /// Cria uma Url mais amigavel, combinando os parametros da busca e adicionando sua representacao em texto
        /// http://www.deliveron.com/blog/post/SEO-Friendly-Routes-with-ASPnet-MVC.aspx
        /// Urls amigaveis para SEO
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string ToSeoUrl(this string url)
        {
            string retorno =
                string.IsNullOrEmpty(url) ? string.Empty : url.ToLower()
                .Replace(" ", "-")
                .Replace("/", "-")
                .Replace("+", "-")
                .Replace(@"\", "-")
                .Replace(".", "-")
                .Replace("(", "-")
                .Replace(")", "-")
                .Replace("'", "-")
                .Replace("’", "-")
                .Replace("&", "-")
                .Replace("%", "-")
                .RemoverAcentos();

            retorno = Regex.Replace(retorno, "[^0-9a-zA-Z]+", "-");

            return retorno;
        }

        /// <summary>
        /// Usado para remver espaços em branco e colocar em minusculo, para buscas por texto para comparacoes
        /// </summary>
        /// <param name="urlText">texto da url não formatado</param>
        /// <returns></returns>
        public static string NormalizeUrlTextToComparing(this string urlText)
        {
            return urlText.Trim().ToLower();
        }

        /// <summary>
        /// Agrupar um texto que contenha os seguintes caracteres(" ", /, +, \, . ) por "-", geralmente urilizado em url
        /// </summary>
        /// <param name="urlText"></param>
        /// <returns></returns>
        public static string GroupTextPerTraceToComparing(this string urlText)
        {
            return urlText.Trim().ToLower()
                                 .Replace(" ", "-")
                                 .Replace("/", "-")
                                 .Replace("+", "-")
                                 .Replace(@"\", "-")
                                 .Replace(".", "-")
                                 .Replace("(", "-")
                                 .Replace(")", "-");
        }

        public static string ToSHA1(this string text)
        {
            return FormsAuthentication.HashPasswordForStoringInConfigFile(text, "SHA1");
        }

        public static string truncarTextoColocandoReticenciasNoFinalSeLimiteExcedido(this string texto, int limite)
        {
            if (string.IsNullOrEmpty(texto))
            {
                return "";
            }

            var textoTruncado = texto;
            if (texto.Count() > limite)
            {
                textoTruncado = texto.Substring(0, limite - 3);
                textoTruncado = textoTruncado + "...";
            }

            return textoTruncado;
        }

        public static bool NumeroDoTelefoneEstaValido(this string telefone)
        {
            var numero = telefone.SomenteNumeros();
            return numero.Length == 8 || numero.Length == 9;
        }

        public static bool NumeroDeCelularComDDDEstaValido(this string celular)
        {
            if (string.IsNullOrWhiteSpace(celular))
            {
                return false;
            }

            string numeroTratado = celular.SomenteNumeros();

            // Verifica se o número tem 11 dígitos (código de área + número) e se começa com 9
            return numeroTratado.Length == 11 && numeroTratado[2] == '9';
        }

        private const string palavrasSempreEmMinusculasSeparadoPorVirgula = "e,ou,de,a,só,com,das,da,do,dos,sem,por,em,no,na,nas,nos,para,o,os,as";
        private const string regexEspaco = "\\s";
        private const string espaco = " ";
        private static List<string> palavrasSempreEmMinusculas;

        static StringExtension()
        {
            palavrasSempreEmMinusculas = new List<string>();
            palavrasSempreEmMinusculas.AddRange(palavrasSempreEmMinusculasSeparadoPorVirgula.Split(','));
        }

        public static string ToCapitalizacaoDeTitulos(this string source)
        {
            CultureInfo culture = new CultureInfo("pt-br"); // mudar para configuração
            TextInfo textInfo = culture.TextInfo;
            string resultado = textInfo.ToTitleCase(source);

            foreach (var palavra in palavrasSempreEmMinusculas)
            {
                string pattern = String.Format(@"\s{0}\s|\s{0}$", palavra);
                string replacement = espaco + palavra.ToLower() + espaco;

                resultado = Regex.Replace(resultado, pattern, replacement, RegexOptions.IgnoreCase);
            }

            return resultado.Trim();
        }

        public static string CapitalizarPrimeiraPalavra(this string texto)
        {
            if (!string.IsNullOrEmpty(texto))
            {
                texto = char.ToUpper(texto[0]) + texto.Substring(1);
            }
            return texto;

        }

        public static string TextoNoSingularOuPlural(this int valor, string textoNoSingular, string textoNoPlural)
        {
            if (valor > 1)
                return string.Format(textoNoPlural, valor);
            else
                return string.Format(textoNoSingular, valor);
        }

        public static string ObterPeriodoPorExtenso(this int valorEmMeses)
        {
            switch (valorEmMeses)
            {
                case 1: return "Mês";
                case 2: return "Bimestre";
                case 3: return "Trimestre";
                case 6: return "Semestre";
                case 12: return "Ano";
                default: return "";
            }
        }

        public static string RemoverTodosOsEspacosEmBranco(this string texto)
        {
            return texto.Replace(" ", "");
        }

        /// <summary>
        /// Separar o telefone entre DDD e Número
        /// </summary>
        /// <param name="telefone">telefone completo(DDD + numero)</param>
        /// <returns>tupla sendo o DDD e o número de telefone</returns>
        public static (string DDD, string Numero) SepararDDDeNumero(this string telefone)
        {
            const int dddLength = 2;
            
            if (string.IsNullOrWhiteSpace(telefone) || !NumeroDeCelularComDDDEstaValido(telefone))
                return (string.Empty, string.Empty);

            telefone = telefone.RemoverFormatacaoTelefone().Trim();

            if (telefone.Length < 11)
                return (string.Empty, telefone);

            var ddd = telefone.Substring(0, dddLength);
            var numero = telefone.Substring(dddLength);

            return (ddd, numero);
        }

        /// <summary>
        /// Macarar o nome para a segurança de dados
        /// Mostrar o primeiro nome e três letras do segundo seguido por *
        /// </summary>
        /// <param name="nomeCompleto">nome a ser mascarado</param>
        /// <returns>o nome mascardo:Maria Rebecca Lopes -> Maria Reb**** </returns>
        public static string MascararNome(this string nomeCompleto)
        {
            if (string.IsNullOrWhiteSpace(nomeCompleto))
                return string.Empty;

            var nomes = nomeCompleto.Trim().Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            if (nomes.Length == 0)
                return string.Empty;

            if (nomes.Length == 1)
                return nomes[0];

            var primeiroNome = nomes[0];
            var segundoNome = nomes[1];

            int letrasVisiveis = Math.Min(3, segundoNome.Length);
            var visivel = segundoNome.Substring(0, letrasVisiveis);
            var mascarado = visivel.PadRight(segundoNome.Length, '*');

            return $"{primeiroNome} {mascarado}";
        }


        /// <summary>
        /// Macarar email para segurança de dados
        /// Mostrar até primeiro e ultimo caractere, antes do @ Usar máscara para o restante "*******" 
        /// Sempre mostrar o domínio(@gmail.com etc)
        /// </summary>
        /// <param name="email">email a ser mascarado</param>
        /// <returns>o Email de forma mascarada: m*******<EMAIL></returns>
        public static string MascararEmail(this string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return string.Empty;

            var atIndex = email.IndexOf('@');
            if (atIndex <= 0)
                return email;

            var usuario = email.Substring(0, atIndex);
            var dominio = email.Substring(atIndex);

            if (usuario.Length <= 2)
                return usuario + dominio;

            if (usuario.Length <= 5)
            {
                return usuario.Substring(0, 1) + new string('*', usuario.Length - 2) + usuario.Substring(usuario.Length - 1) + dominio;
            }

            var primeiros = usuario.Substring(0, 3);
            var ultimos = usuario.Substring(usuario.Length - 2, 2);
            var mascarados = new string('*', usuario.Length - 5);

            return primeiros + mascarados + ultimos + dominio;
        }
    }

    public class StringIgnoreAccenstsAndCaseComparer : EqualityComparer<string>
    {

        public override bool Equals(string stringA, string stringB)
        {
            return string.Compare(stringA.ToLower(), stringB.ToLower(), CultureInfo.CurrentCulture, CompareOptions.IgnoreNonSpace) == 0;
        }

        public override int GetHashCode(string obj)
        {
            return obj != null ? obj.GetHashCode() : 0;
        }
    }

    public class CharIgnoreAccenstsAndCaseComparer : EqualityComparer<char>
    {

        public override bool Equals(char charA, char charB)
        {
            return string.Compare(charA.ToString().ToLower(), charB.ToString().ToLower(), CultureInfo.CurrentCulture, CompareOptions.IgnoreNonSpace) == 0;
        }

        public override int GetHashCode(char obj)
        {
            return obj.GetHashCode();
        }
    }
}