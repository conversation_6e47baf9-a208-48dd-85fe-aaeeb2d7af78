﻿using System;
using System.Collections.Generic;

namespace Perlink.GatewayPagamento
{
    public class DadosCadastroCartao : DadosPagamentoGateway
    {
        public string Numero { get; set; }
        public string PortadorNome { get; set; }
        public int ValidadeMes { get; set; }
        public int ValidadeAno { get; set; }
        public string CodigoSeguranca { get; set; }
        public string IdComprador { get; set; }

        /// <summary>
        /// Lista de erros baseado nas validações feitas em @see EhValido.
        /// </summary>
        /// <returns>Lista de erros, vazio se não tem erros</returns>
        public IList<string> Validar()
        {

            IList<string> mensagens = new List<string>();

            if (string.IsNullOrWhiteSpace(Numero))
                mensagens.Add("Número do Cartão nulo ou vazio");
            if (string.IsNullOrWhiteSpace(PortadorNome))
                mensagens.Add("Nome do Portador do Cartão nulo ou vazio");
            if (string.IsNullOrWhiteSpace(CodigoSeguranca))
                mensagens.Add("Código de Seg do Cartão nulo ou vazio");
            if (ValidadeMes <= 0 || ValidadeMes > 12)
                mensagens.Add(string.Format("Mês de validade do Cartão inválido. {0}", ValidadeMes));
            if (ValidadeAno > 100 || ValidadeAno < (DateTime.Today.Year - 2000))
                mensagens.Add(string.Format("Ano de validade do Cartão inválido. {0}", ValidadeAno));
            if (new DateTime(ValidadeAno + 2000, ValidadeMes, 1).AddMonths(1) <= DateTime.Today)
                mensagens.Add(string.Format("Ano de validade do Cartão não é válido. {0}", ValidadeAno));

            return mensagens;
        }

        public bool EhValido
        {
            get
            {
                return Validar().Count == 0;
            }
        }
    }
}
