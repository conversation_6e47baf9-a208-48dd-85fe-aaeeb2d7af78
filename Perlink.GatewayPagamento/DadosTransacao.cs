﻿using Perlink.GatewayPagamento.Enum;
using System.Collections.Generic;

namespace Perlink.GatewayPagamento
{
    public class DadosTransacao : DadosPagamentoGateway
    {
        public DadosTransacao()
        {
            Operadora = CartaoCreditoOperadoraEnum.CIELO;
            Moeda = MoedaEnum.BRL;
            CartaoCredito = new CartaoCredito();
            Parcelas = 1;
        }
        
        public int Parcelas { get; set; }
        public string IPComprador { get; set; }
        public MoedaEnum Moeda { get; set; }
        public CartaoCreditoOperadoraEnum Operadora { get; set; }
        public CartaoCredito CartaoCredito { get; set; }

        /// <summary>
        /// Lista de erros baseado nas validações feitas em @see EhValido.
        /// </summary>
        /// <returns>Lista de erros</returns>
        public IList<string> Validar()
        {

            List<string> mensagens = new List<string>();

            if (Valor <= 0)
                mensagens.Add("Valor menor ou igual a zero");
            if (Parcelas <= 0)
                mensagens.Add("Parcelas menor ou igual a zero");
            if (CartaoCredito == null)
                mensagens.Add("Cartão de crédito nulo");
            if (!CartaoCredito.EhValido)
                mensagens.AddRange(CartaoCredito.Validar());
            if (string.IsNullOrWhiteSpace(NumeroDocumento))
                mensagens.Add("Número Documento nulo ou vazio");
            if (string.IsNullOrWhiteSpace(IPComprador))
                mensagens.Add("IP do comprador nulo ou vazio");

            return mensagens;
        }

        public bool EhValido
        {
            get
            {
                return Validar().Count == 0;
            }
        }
    }
}
