﻿using System;
using Trinks.Integracoes.PagarMe.Enums;

namespace Perlink.GatewayPagamento
{
    public class ResultadoCadastroCartao
    {
        public string Id { get; protected set; }
        public string Impressao { get; protected set; }
        public string Numero { get; protected set; }
        public string PortadorNome { get; protected set; }
        public int ValidadeMes { get; protected set; }
        public int ValidadeAno { get; protected set; }
        public CreditCardBrand Bandeira { get; protected set; }
        public bool Valido { get; protected set; }
        public bool CadastroConcluido { get; protected set; }
        public string ResultadoSolicitacaoCadastro { get; protected set; }
        public Exception Exception { get; protected set; }
        public string IdComprador { get; set; }
    }
}
