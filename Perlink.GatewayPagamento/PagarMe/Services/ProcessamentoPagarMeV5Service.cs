﻿using Perlink.GatewayPagamento.Enum;
using Perlink.GatewayPagamento.Helpers;
using Perlink.GatewayPagamento.Services;
using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Converters;
using System;
using System.Linq;
using Trinks.Integracoes.PagarMe.V5.Abstractions;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Configuration;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Models;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Sections.Orders.Commands;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Utils;
using Trinks.Integracoes.PagarMe.V5.Client;

namespace Perlink.GatewayPagamento.PagarMe.Services
{
    public class ProcessamentoPagarMeV5Service : ProcessamentoService
    {
        private readonly string _idRecebedorTrinks;
        private readonly IPagarMeV5Client _pagarMeV5Client;
        
        public ProcessamentoPagarMeV5Service(ConfiguracaoGatewayFatura configuracao)
        {
            _idRecebedorTrinks = configuracao.IdRecebedorTrinks;
            _pagarMeV5Client = new PagarMeV5Client(GerarConfiguracaoV5(configuracao));
        }
        
        #region Configuração

        private static PagarMeV5ClientConfiguration GerarConfiguracaoV5(ConfiguracaoGatewayFatura configuracao) 
            => new PagarMeV5ClientConfiguration() {
                SecretKey = configuracao.ApiKey,
                WebhookUsername = configuracao.WebhookUsername,
                WebhookPassword = configuracao.WebhookPassword,
            };
        #endregion
        
        public override ResultadoTransacao SolicitarTransacao(DadosTransacao dadosTransacao)
        {
            if (!dadosTransacao.EhValido)
                throw new ArgumentException($"Dados não válidos para transação com a PagarMe - {string.Join("/", dadosTransacao.Validar())}");
            
            ValidarParametrosAdicionaisDaPagarMe(dadosTransacao.Parcelas, dadosTransacao.Valor);
            
            var novoPagamento = dadosTransacao.ConverterParaNovoPagamentoNoGatewayDto(_idRecebedorTrinks);
            var customerData = PagarMeV5ModelGenerator.GenerateCustomerData(novoPagamento);
            
            var order = new CreateOrderWithNewCustomerCommand(customerData)
            {
                Items = PagarMeV5ModelGenerator.GenerateOrderItems(novoPagamento),
                PaymentMethods = PagarMeV5ModelGenerator.PreparePaymentWithNewCreditCard(novoPagamento)
            };

            var orderResponse = _pagarMeV5Client.Orders.CreateOrder(order);

            return new ResultadoTransacaoPagarMe(dadosTransacao.NumeroDocumento, orderResponse);
        }

        public override ResultadoTransacao SolicitarRecorrenciaDeTransacao(DadosRecorrenciaTransacao dadosTransacao)
        {
            if (!dadosTransacao.EhValido)
                throw new ArgumentException("Dados não válidos para transação com a PagarMe");
            
            ValidarParametrosAdicionaisDaPagarMe(dadosTransacao.Parcelas, dadosTransacao.Valor);
            
            var novoPagamento = dadosTransacao.ConverterParaNovoPagamentoNoGatewayDto(_idRecebedorTrinks);
            var customerData = PagarMeV5ModelGenerator.GenerateCustomerData(novoPagamento);
            var order = new CreateOrderWithNewCustomerCommand(customerData)
            {
                Items = PagarMeV5ModelGenerator.GenerateOrderItems(novoPagamento),
                PaymentMethods = PagarMeV5ModelGenerator.PreparePaymentWithExistingCreditCard(novoPagamento)
            };

            var orderResponse = _pagarMeV5Client.Orders.CreateOrder(order);

            return new ResultadoTransacaoPagarMe(dadosTransacao.NumeroDocumento, orderResponse, ehRecorrencia: true);
        }

        public override string SolicitarTransacaoPix(DadosTransacaoPix dados)
        {
            var novoPagamento = dados.ConverterParaNovoPagamentoNoGatewayDto(_idRecebedorTrinks);
            var customerData = PagarMeV5ModelGenerator.GenerateCustomerData(novoPagamento);
            var order = new CreateOrderWithNewCustomerCommand(customerData)
            {
                Items = PagarMeV5ModelGenerator.GenerateOrderItems(novoPagamento),
                PaymentMethods = PagarMeV5ModelGenerator.PreparePaymentWithPix(novoPagamento, dados.TempoExpiracaoEmSegundos)
            };

            var orderResponse = _pagarMeV5Client.Orders.CreateOrder(order);

            if (!orderResponse.IsSuccess || orderResponse.Success.Status == "failed")
                LogAndThrowException(orderResponse);

            return RetornarQrCodeSeValido(orderResponse);
        }
        
        public override ResultadoCadastroCartao CadastrarCartaoDeCredito(DadosCadastroCartao dadosCartao)
        {
            string customerId = dadosCartao.IdComprador;
            var novoPagamento = dadosCartao.ConverterParaNovoPagamentoNoGatewayDto();

            if (string.IsNullOrEmpty(customerId))
                customerId = CriarCustomer(novoPagamento);
            
            var cardData = PagarMeV5ModelGenerator.GenerateNewCreditCardData(novoPagamento);
            var createNewCardResponse = _pagarMeV5Client.Customers.CreateCard(cardData, customerId);
            
            return new ResultadoCadastroCartaoPagarMe(createNewCardResponse, customerId);
        }

        private string CriarCustomer(NovoPagamentoNoGatewayDTO novoPagamento)
        {
            var customerData = PagarMeV5ModelGenerator.GenerateCustomerData(novoPagamento);
            var response = _pagarMeV5Client.Customers.CreateCustomer(customerData);

            if (!response.IsSuccess || string.IsNullOrEmpty(response.Success.Id))
                LogAndThrowException(response);

            return response.Success.Id;
        }

        public override ResultadoCaptura CapturarPeloIdTransacao(string transacao)
        {
            throw new NotImplementedException();
        }

        public override ResultadoCaptura CapturarPeloNumeroDocumento(string numeroDocumento)
        {
            throw new NotImplementedException();
        }

        public override GatewayEnum Gateway { get; }
        
        private static void ValidarParametrosAdicionaisDaPagarMe(int parcelas, decimal valor)
        {
            if (parcelas > 1)
                throw new ArgumentException("Dados não válidos para transação com a PagarMe - Não é suportado pagamento com mais de 1 parcela");
            
            if (valor % 0.01m > 0)
                throw new ArgumentException("Dados não válidos para transação com a PagarMe - Valor não pode ter precisão acima de duas casas decimais");
        }
        
        private static string RetornarQrCodeSeValido(Result<OrderModel, PagarMeFailureModel> orderResponse)
        {
            var qrCode = orderResponse.Success.Charges[0].LastTransaction.QrCode;

            if (string.IsNullOrEmpty(qrCode)) 
                LogAndThrowException(orderResponse); 
                    
            return qrCode;
        }
        
        private static void LogAndThrowException<TRequest>(Result<TRequest, PagarMeFailureModel> response)
        {
            Exception ex = new Exception(response.Failure.Message) { Source = CreateErrorMessage(response) };
            Elmah.ErrorLog.GetDefault(null).Log(new Elmah.Error(ex));
            throw ex;
        }
        
        private static string CreateErrorMessage<TRequest>(Result<TRequest, PagarMeFailureModel> result)
        {
            var message = CreateErrorMessage(result.Failure, result.StatusCode);

            if (string.IsNullOrEmpty(result.RawResponseBody))
                return message;

            return $"{message} - Response: {result.RawResponseBody}";
        }

        private static string CreateErrorMessage(PagarMeFailureModel failure, int statusCode)
        {
            var errorMessage = $"Status Code: {statusCode}.";

            if (!string.IsNullOrWhiteSpace(failure?.Message))
            {
                errorMessage += $" Message: {failure.Message}.";
            }

            var error = failure?.Errors != null
                ? failure?.Errors.FirstOrDefault(d => d.Value != null && d.Value.Any()).Value?.FirstOrDefault()
                : "Request failed";

            errorMessage += $" Error: {error}";
            return errorMessage;
        }
    }
}