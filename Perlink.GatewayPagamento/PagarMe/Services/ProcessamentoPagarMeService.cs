﻿using Perlink.GatewayPagamento.Enum;
using Perlink.GatewayPagamento.Helpers;
using Perlink.GatewayPagamento.Services;
using System;
using System.Collections.Generic;
using Trinks.Integracoes.PagarMe;
using Trinks.Integracoes.PagarMe.Dto;
using Trinks.Shared.Dto;

namespace Perlink.GatewayPagamento.PagarMe.Services
{
    public class ProcessamentoPagarMeService : ProcessamentoService
    {
        private readonly string _idRecebedorTrinks;
        private readonly PagarMeApi _pagarMeApi;

        public ProcessamentoPagarMeService(ConfiguracaoGatewayFatura configuracao)
        {
            _idRecebedorTrinks = configuracao.IdRecebedorTrinks;
            _pagarMeApi = new PagarMeApi(configuracao.ApiKey, configuracao.VersionHeader);
        }

        public override GatewayEnum Gateway => GatewayEnum.CobreBem;

        public override ResultadoTransacao SolicitarRecorrenciaDeTransacao(DadosRecorrenciaTransacao dadosTransacao)
        {
            if (!dadosTransacao.EhValido)
                throw new ArgumentException("Dados não válidos para transação com a PagarMe");
            ValidarParametrosAdicionaisDaPagarMe(dadosTransacao.Parcelas, dadosTransacao.Valor);

            int valorDaTransacaoEmCentavos = ObterValorEmCentavos(dadosTransacao.Valor);

            var resultadoTransacaoPagarMe = AsyncHelper.RunSync<Result<RegisteredTransactionDTO>>(() =>
                _pagarMeApi.Transactions.CreateNewCreditCardTransaction(new CreditCardTransaction()
                {
                    Amount = valorDaTransacaoEmCentavos,
                    CardId = dadosTransacao.TokenDeRecorrencia,
                    SplitRules = new List<SplitRule>() {
                        new SplitRule() {
                            Amount = valorDaTransacaoEmCentavos,
                            ChargeProcessingFee = true,
                            Liable = true,
                            RecipientId = _idRecebedorTrinks
                        }
                    }
                }));

            return new ResultadoTransacaoPagarMe(dadosTransacao.NumeroDocumento, resultadoTransacaoPagarMe, true);
        }

        public override ResultadoTransacao SolicitarTransacao(DadosTransacao dadosTransacao)
        {
            if (!dadosTransacao.EhValido)
                throw new ArgumentException(string.Format("{0} - {1}", "Dados não válidos para transação com a PagarMe", String.Join("/", dadosTransacao.Validar())));
            ValidarParametrosAdicionaisDaPagarMe(dadosTransacao.Parcelas, dadosTransacao.Valor);

            int valorDaTransacaoEmCentavos = ObterValorEmCentavos(dadosTransacao.Valor);

            DateTime expiracao = new DateTime(dadosTransacao.CartaoCredito.ValidadeAno, dadosTransacao.CartaoCredito.ValidadeMes, 1, 0, 0, 0, DateTimeKind.Utc);

            var resultadoTransacaoPagarMe = AsyncHelper.RunSync<Result<RegisteredTransactionDTO>>(() =>
                _pagarMeApi.Transactions.CreateNewCreditCardTransaction(new CreditCardTransaction()
                {
                    Amount = valorDaTransacaoEmCentavos,
                    CreditCardCVV = dadosTransacao.CartaoCredito.CodigoSeguranca,
                    CreditCardExpiration = expiracao.ToString("MMyy"),
                    CreditCardHolderName = dadosTransacao.CartaoCredito.PortadorNome,
                    CreditCardNumber = dadosTransacao.CartaoCredito.Numero,
                    SplitRules = new List<SplitRule>() {
                        new SplitRule() {
                            Amount = valorDaTransacaoEmCentavos,
                            ChargeProcessingFee = true,
                            Liable = true,
                            RecipientId = _idRecebedorTrinks
                        }
                    }
                }));

            return new ResultadoTransacaoPagarMe(dadosTransacao.NumeroDocumento, resultadoTransacaoPagarMe);
        }

        private void ValidarParametrosAdicionaisDaPagarMe(int parcelas, decimal valor)
        {
            if (parcelas > 1)
                throw new ArgumentException(string.Format("{0} - {1}", "Dados não válidos para transação com a PagarMe", "Não é suportado pagamento com mais de 1 parcela"));
            if (valor % 0.01m > 0)
                throw new ArgumentException(string.Format("{0} - {1}", "Dados não válidos para transação com a PagarMe", "Valor não pode ter precisão acima de duas casas decimais"));
        }

        private int ObterValorEmCentavos(decimal valor)
        {
            return Convert.ToInt32(valor * 100);
        }

        public override ResultadoCaptura CapturarPeloIdTransacao(string transacao)
        {
            throw new NotImplementedException();
        }

        public override ResultadoCaptura CapturarPeloNumeroDocumento(string numeroDocumento)
        {
            throw new NotImplementedException();
        }

        public override ResultadoCadastroCartao CadastrarCartaoDeCredito(DadosCadastroCartao dadosCartao)
        {
            if (!dadosCartao.EhValido)
                throw new ArgumentException("Dados não válidos para cadastrar cartão na PagarMe");

            var resultadoCadastroPagarMe = AsyncHelper.RunSync<Result<RegisteredCreditCard>>(() =>
                _pagarMeApi.Cards.CreateNewCreditCard(new NewCreditCardForRegistration()
                {
                    ExpirationMonth = dadosCartao.ValidadeMes,
                    CVV = dadosCartao.CodigoSeguranca,
                    ExpirationYear = 2000 + dadosCartao.ValidadeAno,
                    HolderName = dadosCartao.PortadorNome,
                    Number = dadosCartao.Numero,
                }));

            return new ResultadoCadastroCartaoPagarMe(resultadoCadastroPagarMe);
        }
        
        public override string SolicitarTransacaoPix(DadosTransacaoPix dados)
            => throw new NotImplementedException();
    }
}
