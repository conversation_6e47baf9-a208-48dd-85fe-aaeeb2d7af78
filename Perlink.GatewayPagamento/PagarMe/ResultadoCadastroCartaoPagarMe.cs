﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Trinks.Integracoes.PagarMe.Dto;
using Trinks.Integracoes.PagarMe.Enums;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Models;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Utils;
using Trinks.Integracoes.PagarMe.V5.Abstractions.ValueObjects;
using Trinks.Shared.Dto;

namespace Perlink.GatewayPagamento.PagarMe
{
    internal class ResultadoCadastroCartaoPagarMe : ResultadoCadastroCartao
    {

        public ResultadoCadastroCartaoPagarMe(Result<RegisteredCreditCard> resultadoCadastro)
        {
            if (resultadoCadastro.Success)
            {
                CadastroConcluido = true;
                Id = resultadoCadastro.Content.Id;
                Impressao = resultadoCadastro.Content.Fingerprint;
                Numero = $"{resultadoCadastro.Content.FirstDigits.PadRight(12, '*')}{resultadoCadastro.Content.LastDigits}";
                PortadorNome = resultadoCadastro.Content.HolderName;
                ValidadeMes = Convert.ToInt32(resultadoCadastro.Content.ExpirationDate.Substring(0, 2));
                ValidadeAno = Convert.ToInt32(resultadoCadastro.Content.ExpirationDate.Substring(2, 2));
                Valido = resultadoCadastro.Content.Valid;
                Bandeira = resultadoCadastro.Content.Brand;
            }
            else
            {
                CadastroConcluido = false;
                ResultadoSolicitacaoCadastro = $"Falha ao solicitar cadastro. Retorno: '{resultadoCadastro.ErrorMessage}'";
                Exception = new Exception(resultadoCadastro.ErrorMessage) { Source = resultadoCadastro.ErrorDetails };
                Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(Exception));
            }
        }
        
        public ResultadoCadastroCartaoPagarMe(Result<CardSummary, PagarMeFailureModel> response, string customerId)
        {
            if (response.IsSuccess)
            {
                CardSummary result = response.Success;
                
                CadastroConcluido = true;
                Id = result.Id;
                Numero = $"{result.FirstSixDigits.PadRight(12, '*')}{result.LastFourDigits}";
                PortadorNome = result.HolderName;
                ValidadeMes = result.ExpMonth;
                ValidadeAno = result.ExpYear;
                Valido = result.Status.StartsWith("active");
                Bandeira = ConvertToCreditCardBrand(result.Brand);
                IdComprador = customerId;
            }
            else
            {
                PagarMeFailureModel result = response.Failure;
                
                CadastroConcluido = false;
                ResultadoSolicitacaoCadastro = "Falha ao solicitar cadastro.";
                Exception = new Exception(result.Message) { Source = CreateErrorMessage(response) };
                Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(Exception));
            }
        }
        
        private static string CreateErrorMessage<TRequest>(Result<TRequest, PagarMeFailureModel> result)
        {
            var message = CreateErrorMessage(result.Failure, result.StatusCode);

            if (string.IsNullOrEmpty(result.RawResponseBody))
                return message;

            return $"{message} - Response: {result.RawResponseBody}";
        }

        private static string CreateErrorMessage(PagarMeFailureModel failure, int statusCode)
        {
            var errorMessage = $"Status Code: {statusCode}.";

            if (!string.IsNullOrWhiteSpace(failure?.Message))
            {
                errorMessage += $" Message: {failure.Message}.";
            }

            var error = failure?.Errors != null
                ? failure?.Errors.FirstOrDefault(d => d.Value != null && d.Value.Any()).Value?.FirstOrDefault()
                : "Request failed";

            errorMessage += $" Error: {error}";
            return errorMessage;
        }
        
        private static CreditCardBrand ConvertToCreditCardBrand(PagarMeV5CreditCardBrand brand)
        {
            if (BrandMappings.TryGetValue(brand, out CreditCardBrand result))
                return result;
    
            throw new ArgumentOutOfRangeException(nameof(brand), brand, null);
        }
        
        private static readonly Dictionary<PagarMeV5CreditCardBrand, CreditCardBrand> BrandMappings = 
            new Dictionary<PagarMeV5CreditCardBrand, CreditCardBrand>
            {
                { PagarMeV5CreditCardBrand.Elo, CreditCardBrand.Elo },
                { PagarMeV5CreditCardBrand.Mastercard, CreditCardBrand.MasterCard },
                { PagarMeV5CreditCardBrand.Visa, CreditCardBrand.Visa },
                { PagarMeV5CreditCardBrand.Amex, CreditCardBrand.AmericanExpress },
                { PagarMeV5CreditCardBrand.Hipercard, CreditCardBrand.Hipercard }
            };
    }
}
