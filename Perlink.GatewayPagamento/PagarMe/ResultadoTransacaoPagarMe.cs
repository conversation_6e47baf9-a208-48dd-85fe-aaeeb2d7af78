﻿using System;
using System.Web;
using Trinks.Integracoes.PagarMe.Dto;
using Trinks.Integracoes.PagarMe.Enums;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Models;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Utils;
using Trinks.Shared.Dto;

namespace Perlink.GatewayPagamento.PagarMe
{
    internal class ResultadoTransacaoPagarMe : ResultadoTransacao
    {
        public ResultadoTransacaoPagarMe(string numeroDocumento, Result<RegisteredTransactionDTO> resultadoTransacao, bool ehRecorrencia = false)
        {
            Gateway = Enum.GatewayEnum.PagarMe;

            // A Transação na Pagar.me retorna como falha em casos de erros na requisição
            // Se a requisição concluir, mas o pagamento não for aprovado retornará como sucesso, com o status apropriado
            if (resultadoTransacao.Success)
            {
                var transacao = resultadoTransacao.Content;

                switch (transacao.Status)
                {
                    case TransactionStatus.Paid:
                    case TransactionStatus.Refused:

                        TransacaoAprovada = transacao.Status == TransactionStatus.Paid;
                        ResultadoSolicitacaoAprovacao = ObterTextoDeResultadoDaAprovacao(transacao);

                        CodigoAutorizacao = transacao.Id;
                        Transacao = transacao.Id;
                        NumeroDocumento = numeroDocumento;
                        ComprovanteAdministradora = transacao.AuthorizationCode;
                        CartaoMascarado = $"{transacao.CardFirstDigits}******{transacao.CardLastDigits}";
                        TokenDeRecorrencia = transacao.Card?.Id;

                        //if (!TransacaoAprovada) {
                        //    var exceptionTransacaoNaoAprovada = new Exception($"Transação Negada na Pagar.Me. {ResultadoSolicitacaoAprovacao}");
                        //    Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(exceptionTransacaoNaoAprovada));
                        //}

                        break;

                    default:
                        // Como a transação é feita de forma síncrona e com captura, qualquer status
                        // diferente de "paid" ou "refused" é um retorno inesperado.
                        TransacaoAprovada = false;
                        ResultadoSolicitacaoAprovacao = $"Retorno inesperado da Pagar.me. {ObterTextoDeResultadoDaAprovacao(transacao)}";

                        // Exception é preenchido porque caso contrário será considerado como transação negada e haverá envio de email ao cliente
                        TratarLogEhRetornoExcessao(ehRecorrencia);

                        break;
                }

            }
            else
            {
                TransacaoAprovada = false;
                ResultadoSolicitacaoAprovacao = $"Falha ao solicitar transacação. Retorno: '{resultadoTransacao.ErrorMessage}'";
                TratarLogEhRetornoExcessao(ehRecorrencia);
            }
        }

        public ResultadoTransacaoPagarMe(string numeroDocumento, Result<OrderModel, PagarMeFailureModel> resultado, bool ehRecorrencia = false)
        {
            Gateway = Enum.GatewayEnum.PagarMeV5;

            if (!resultado.IsSuccess)
            {
                TransacaoAprovada = false;
                ResultadoSolicitacaoAprovacao = $"Falha ao solicitar transacação. Retorno: '{resultado.Failure.Message}'";
                TratarLogEhRetornoExcessao(ehRecorrencia);
                return;
            }
            
            var pedido = resultado.Success;
            var transacao = pedido.Charges[0].LastTransaction;
            var cartao = transacao.Card;

            switch (pedido.Status)
            {
                case "paid":
                case "refused":
                    TransacaoAprovada = pedido.Status == "paid";
                    ResultadoSolicitacaoAprovacao = ObterTextoDeResultadoDaAprovacao(pedido);
                    CodigoAutorizacao = transacao.Id;
                    Transacao = "1111"; // https://github.com/trinks-com/documentacao-tecnica/blob/main/%C3%89picos/Migra%C3%A7%C3%A3o%20Assinaturas%20V5/ADRs/2025-06-07-resultado-transacao-cartao.md
                    NumeroDocumento = numeroDocumento;
                    ComprovanteAdministradora = transacao.AcquirerAuthCode;
                    CartaoMascarado = $"{cartao.FirstSixDigits}******{cartao.LastFourDigits}";
                    TokenDeRecorrencia = cartao.Id;
                    break;
                default:
                    TransacaoAprovada = false;
                    ResultadoSolicitacaoAprovacao = $"Retorno inesperado da Pagar.me. {ObterTextoDeResultadoDaAprovacao(pedido)}";
                    TratarLogEhRetornoExcessao(ehRecorrencia);
                    break;
            }
        }

        private void TratarLogEhRetornoExcessao(bool ehRecorrencia)
        {
            // Exception é preenchido porque caso contrário será considerado como transação negada e haverá envio de email ao cliente
            var exception = new Exception(ResultadoSolicitacaoAprovacao);
            Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(exception));

            if (!ehRecorrencia)
                Exception = exception;
        }

        private string ObterTextoDeResultadoDaAprovacao(RegisteredTransactionDTO transacao)
        {
            return $"Transação com Id '{transacao.Id}' retornou com status '{ObterTextoDoStatus(transacao.Status)}'";
        }
        
        private string ObterTextoDeResultadoDaAprovacao(OrderModel pedido)
        {
            return $"Transação com Id '{pedido.Charges[0].LastTransaction.Id}' retornou com status '{pedido.Status}'";
        }

        private string ObterTextoDoStatus(TransactionStatus status)
        {
            return System.Enum.GetName(typeof(TransactionStatus), status);
        }
    }
}
