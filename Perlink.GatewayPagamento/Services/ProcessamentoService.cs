﻿using Perlink.GatewayPagamento.Enum;

namespace Perlink.GatewayPagamento.Services
{
    public abstract class ProcessamentoService
    {
        public abstract ResultadoTransacao SolicitarTransacao(DadosTransacao dadosTransacao);
        public abstract ResultadoTransacao SolicitarRecorrenciaDeTransacao(DadosRecorrenciaTransacao dadosTransacao);
        public abstract string SolicitarTransacaoPix(DadosTransacaoPix dados);
        public abstract ResultadoCaptura CapturarPeloIdTransacao(string transacao);
        public abstract ResultadoCaptura CapturarPeloNumeroDocumento(string numeroDocumento);

        public abstract ResultadoCadastroCartao CadastrarCartaoDeCredito(DadosCadastroCartao dadosCartao);

        public abstract GatewayEnum Gateway { get; }
    }
}