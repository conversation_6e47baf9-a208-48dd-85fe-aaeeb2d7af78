﻿using Perlink.DomainInfrastructure;
using Perlink.GatewayPagamento.Enum;
using Perlink.GatewayPagamento.Services;
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Web;
using System.Xml;

namespace Perlink.GatewayPagamento.CobreBem.Services
{

    public class ProcessamentoCobreBemService : ProcessamentoService
    {
        private bool _ehTeste;
        private string _urlBase;

        private ProcessamentoCobreBemService()
            : base() { }

        public ProcessamentoCobreBemService(string login, bool ehTeste = false)
            : this()
        {
            var urlBaseTeste = "https://teste.aprovafacil.com/cgi-bin/APFW/{0}";
            var urlBase = "https://www.aprovafacil.com/cgi-bin/APFW/{0}";

            if (SimulationTool.Current.EhSimulacao)
                ehTeste = true;

            _urlBase = string.Format(ehTeste ? urlBaseTeste : urlBase, login);
            _ehTeste = ehTeste;
        }

        public override GatewayEnum Gateway => GatewayEnum.CobreBem;

        public override ResultadoTransacao SolicitarTransacao(DadosTransacao dadosTransacao)
        {
            if (!dadosTransacao.EhValido)
                throw new ArgumentException(string.Format("{0} - {1}", "Dados não válidos para transação com o CobreBem", String.Join("/", dadosTransacao.Validar())));

            var dados = "NumeroDocumento=" + dadosTransacao.NumeroDocumento + "&ValorDocumento=" +
                        dadosTransacao.Valor.ToString("F2").Replace(",", ".") + "&QuantidadeParcelas=" +
                        dadosTransacao.Parcelas + "&NumeroCartao=" + dadosTransacao.CartaoCredito.Numero + "&Bandeira=" +
                        dadosTransacao.CartaoCredito.Bandeira + "&MesValidade=" +
                        dadosTransacao.CartaoCredito.ValidadeMes + "&AnoValidade=" +
                        (dadosTransacao.CartaoCredito.ValidadeAno + 2000) + "&CodigoSeguranca=" +
                        dadosTransacao.CartaoCredito.CodigoSeguranca + "&NomePortadorCartao=" +
                        dadosTransacao.CartaoCredito.PortadorNome;
            //+ "&EnderecoIPComprador=" + dadosTransacao.IPComprador;

            ResultadoSolicitacaoAPC retorno;

            try
            {
                var retornoWS = AcessaWebService(_urlBase + "/APC", dados, _ehTeste);
                var xmlNodes = ToXML(retornoWS);
                retorno = new ResultadoSolicitacaoAPC(xmlNodes);

                if (_ehTeste && retorno.ResultadoSolicitacaoAprovacao.Contains("PDV"))
                {
                    // Se der erro no CobreBemX, forjar
                    retorno = new ResultadoSolicitacaoAPC(dadosTransacao.NumeroDocumento, dadosTransacao.CartaoCredito.CodigoSeguranca == "123");
                }
            }
            catch (Exception e)
            {
                Elmah.ErrorLog.GetDefault(HttpContext.Current).Log(new Elmah.Error(e));
                retorno = new ResultadoSolicitacaoAPC(e);
            }

            return retorno;
        }

        public override ResultadoTransacao SolicitarRecorrenciaDeTransacao(DadosRecorrenciaTransacao dadosTransacao)
        {
            if (!_ehTeste && !dadosTransacao.EhValido)
                throw new ArgumentException("Dados não válidos para transação com o CobreBem");

            var dados = "NumeroDocumento=" + dadosTransacao.NumeroDocumento + "&ValorDocumento=" +
                        dadosTransacao.Valor.ToString("F2").Replace(",", ".") + "&QuantidadeParcelas=" +
                        dadosTransacao.Parcelas + "&TransacaoAnterior=" + dadosTransacao.TransacaoAnterior;

            if (_ehTeste)
            {
                return new ResultadoSolicitacaoAPC(dadosTransacao.NumeroDocumento, true);
            }
            else
            {
                var retornoWS = AcessaWebService(_urlBase + "/APC", dados, _ehTeste);
                var xmlNodes = ToXML(retornoWS);
                return new ResultadoSolicitacaoAPC(xmlNodes);
            }
        }

        public override ResultadoCaptura CapturarPeloIdTransacao(string transacao)
        {
            if (string.IsNullOrWhiteSpace(transacao))
                throw new ArgumentException("Dados não válidos para transação com o CobreBem");

            var retornoWS = AcessaWebService(_urlBase + "/CAP?Transacao=" + transacao, null, _ehTeste);
            var xmlNodes = ToXML(retornoWS);

            var retorno = new ResultadoSolicitacaoCAP(xmlNodes);
            return retorno;
        }

        public override ResultadoCaptura CapturarPeloNumeroDocumento(string numeroDocumento)
        {
            if (string.IsNullOrWhiteSpace(numeroDocumento))
                throw new ArgumentException("Dados não válidos para transação com o CobreBem");

            var retornoWS = AcessaWebService(_urlBase + "/CAP?NumeroDocumento=" + numeroDocumento, null, _ehTeste);
            var xmlNodes = ToXML(retornoWS);

            var retorno = new ResultadoSolicitacaoCAP(xmlNodes);
            return retorno;
        }

        private static XmlDocument ToXML(string stringRetorno)
        {
            if (stringRetorno == null)
                return null;
            var xml = new XmlDocument();

            try
            {
                xml.LoadXml(stringRetorno);
            }
            catch (Exception)
            {
                throw new Exception("Retorno inválido do WebService") { Source = stringRetorno };
            }
            return xml;
        }

        private static string AcessaWebService(string url, string post, bool ehTeste)
        {
            string stringRetorno;

            try
            {
                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12
                       | SecurityProtocolType.Tls11
                       | SecurityProtocolType.Tls
                       | SecurityProtocolType.Ssl3;

                var req = (HttpWebRequest)WebRequest.Create(url);

                req.Method = "POST";
                req.ContentType = "application/x-www-form-urlencoded";

                var stOut = new StreamWriter(req.GetRequestStream(), Encoding.GetEncoding("ISO-8859-1"));
                stOut.Write(post);
                stOut.Close();

                var stIn = new StreamReader(req.GetResponse().GetResponseStream(), Encoding.GetEncoding("ISO-8859-1"));

                stringRetorno = stIn.ReadToEnd().Replace("\n", "");
                stIn.Close();
            }
            catch (Exception)
            {
                if (!ehTeste)
                    throw;

                Random rnd = new Random();
                var sorteio = rnd.Next(1, 4);

                if (sorteio < 3)
                    stringRetorno = "<ResultadoAPC>" +
                                    "<TransacaoAprovada>True</TransacaoAprovada>" +
                                    "<ResultadoSolicitacaoAprovacao>Aprovada</ResultadoSolicitacaoAprovacao>" +
                                    "<CodigoAutorizacao>1234</CodigoAutorizacao>" +
                                    "<Transacao>1234</Transacao>" +
                                    "<CartaoMascarado>11111111111111</CartaoMascarado>" +
                                    "<NumeroDocumento>22222222222222</NumeroDocumento>" +
                                    "<ComprovanteAdministradora>1234</ComprovanteAdministradora>" +
                                    "</ResultadoAPC>";
                else
                    stringRetorno = "<ResultadoAPC>" +
                                "<TransacaoAprovada>False</TransacaoAprovada>" +
                                "<ResultadoSolicitacaoAprovacao>Recusado por sorteio</ResultadoSolicitacaoAprovacao>" +
                                "<CodigoAutorizacao>0</CodigoAutorizacao>" +
                                "<Transacao>1234</Transacao>" +
                                "<CartaoMascarado>11111111111111</CartaoMascarado>" +
                                "<NumeroDocumento>22222222222222</NumeroDocumento>" +
                                "<ComprovanteAdministradora>0</ComprovanteAdministradora>" +
                                "</ResultadoAPC>";
            }
            return stringRetorno;
        }

        public override ResultadoCadastroCartao CadastrarCartaoDeCredito(DadosCadastroCartao dadosCartao)
        => throw new NotImplementedException();
        
        public override string SolicitarTransacaoPix(DadosTransacaoPix dados)
            => throw new NotImplementedException();
    }
}