﻿namespace Perlink.GatewayPagamento
{
    public class DadosRecorrenciaTransacao : DadosPagamentoGateway
    {
        public DadosRecorrenciaTransacao()
        {
            Parcelas = 1;
        }
        
        public int Parcelas { get; set; }
        public string TransacaoAnterior { get; set; }
        public string IPComprador { get; set; }
        public string TokenDeRecorrencia { get; set; }

        public bool EhValido
        {
            get
            {
                return Valor > 0
                    && Parcelas > 0
                    && !string.IsNullOrWhiteSpace(NumeroDocumento)
                    && !string.IsNullOrWhiteSpace(TransacaoAnterior);
            }
        }

    }
}
