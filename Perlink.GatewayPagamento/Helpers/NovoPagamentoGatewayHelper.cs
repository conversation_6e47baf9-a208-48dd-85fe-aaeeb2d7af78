﻿using Perlink.Pagamentos.Gateways.DTO;
using System.Collections.Generic;

namespace Perlink.GatewayPagamento.Helpers
{
    public static class NovoPagamentoGatewayHelper
    {
        public static NovoPagamentoNoGatewayDTO ConverterParaNovoPagamentoNoGatewayDto(this DadosTransacao dados, string idRecebedorTrinks)
            => new NovoPagamentoNoGatewayDTO
            {
                Itens = new List<ItemPagamentoNoGatewayDTO> { dados.ConverterParaItemPagamentoNoGatewayDto() },
                Cartao = dados.CartaoCredito.ConverterParaDadosDoCartaoDto(),
                Valor = dados.Valor,
                QuantidadeParcelas = 1,
                NomeDeExibicaoNaFatura = DadosPagamentoGateway.NomeItem,
                EnderecoDeCobranca = dados.Endereco.ConverterParaEnderecoParaCadastrarDto(),
                Comprador = dados.ConverterParaCompradorDto(),
                RegrasDeSplit = new List<SplitPagamentoNoGatewayDTO>
                {
                    NovoSplitPagamento(dados.Valor, idRecebedorTrinks)
                }
            };
        
        public static NovoPagamentoNoGatewayDTO ConverterParaNovoPagamentoNoGatewayDto(this DadosRecorrenciaTransacao dados, string idRecebedorTrinks)
            => new NovoPagamentoNoGatewayDTO
            {
                Itens = new List<ItemPagamentoNoGatewayDTO> { dados.ConverterParaItemPagamentoNoGatewayDto() },
                IdCartaoNoGateway = dados.TokenDeRecorrencia,
                Valor = dados.Valor,
                QuantidadeParcelas = 1,
                NomeDeExibicaoNaFatura = DadosPagamentoGateway.NomeItem,
                EnderecoDeCobranca = dados.Endereco.ConverterParaEnderecoParaCadastrarDto(),
                Comprador = dados.ConverterParaCompradorDto(),
                RegrasDeSplit = new List<SplitPagamentoNoGatewayDTO>
                {
                    NovoSplitPagamento(dados.Valor, idRecebedorTrinks)
                }
            };
        
        public static NovoPagamentoNoGatewayDTO ConverterParaNovoPagamentoNoGatewayDto(this DadosTransacaoPix dados, string idRecebedorTrinks)
            => new NovoPagamentoNoGatewayDTO
            {
                Itens = new List<ItemPagamentoNoGatewayDTO> { dados.ConverterParaItemPagamentoNoGatewayDto() },
                Valor = dados.Valor,
                QuantidadeParcelas = 1,
                NomeDeExibicaoNaFatura = DadosPagamentoGateway.NomeItem,
                EnderecoDeCobranca = dados.Endereco.ConverterParaEnderecoParaCadastrarDto(),
                Comprador = dados.ConverterParaCompradorDto(),
                RegrasDeSplit = new List<SplitPagamentoNoGatewayDTO>
                {
                    NovoSplitPagamento(dados.Valor, idRecebedorTrinks)
                }
            };
        
        public static NovoPagamentoNoGatewayDTO ConverterParaNovoPagamentoNoGatewayDto(this DadosCadastroCartao dados)
            => new NovoPagamentoNoGatewayDTO
            {
                EnderecoDeCobranca = dados.Endereco.ConverterParaEnderecoParaCadastrarDto(),
                Comprador = dados.ConverterParaCompradorDto(),
                Cartao = dados.ConverterParaDadosDoCartaoDto(),
            };

        private static CompradorDTO ConverterParaCompradorDto(this DadosPagamentoGateway dados)
            => new CompradorDTO
            {
                Nome = dados.NomeComprador,
                Email = dados.Email,
                Telefone = dados.Telefone,
                CpfCnpj = dados.DocumentoComprador
            };

        private static EnderecoParaCadastrarDTO ConverterParaEnderecoParaCadastrarDto(this EnderecoDeCobranca endereco)
            => new EnderecoParaCadastrarDTO
            {
                Logradouro = endereco.Logradouro,
                Numero = endereco.Numero,
                Bairro = endereco.Bairro,
                Complemento = endereco.Complemento,
                Cidade = endereco.Cidade,
                Estado = endereco.Estado,
                CEP = endereco.CEP,
                PontoDeReferencia = endereco.PontoDeReferencia
            };

        private static SplitPagamentoNoGatewayDTO NovoSplitPagamento(decimal valor, string idRecebedorTrinks)
        {
            return new SplitPagamentoNoGatewayDTO
            {
                IdRecebedorNoGateway = idRecebedorTrinks,
                ResponsavelPelasTaxasDaOperacao = true,
                ResponsavelPeloChargeback = true,
                ResponsavelPeloRestoDaDivisaoDeTaxas = true,
                Valor = valor,
            };
        }

        private static DadosDoCartaoDTO ConverterParaDadosDoCartaoDto(this CartaoCredito cartao)
            => new DadosDoCartaoDTO
            {
                Nome = cartao.PortadorNome,
                Numero = cartao.Numero,
                CVV = cartao.CodigoSeguranca,
                Validade = $"{cartao.ValidadeMes:D2}/{cartao.ValidadeAno}"
            };
        
        private static DadosDoCartaoDTO ConverterParaDadosDoCartaoDto(this DadosCadastroCartao cartao)
            => new DadosDoCartaoDTO
            {
                Nome = cartao.PortadorNome,
                Numero = cartao.Numero,
                CVV = cartao.CodigoSeguranca,
                Validade = $"{cartao.ValidadeMes:D2}/{cartao.ValidadeAno}"
            };

        private static ItemPagamentoNoGatewayDTO ConverterParaItemPagamentoNoGatewayDto(this DadosPagamentoGateway dados)
            => new ItemPagamentoNoGatewayDTO
            {
                IdItemPagamento = int.Parse(dados.NumeroDocumento),
                NomeItem = DadosPagamentoGateway.NomeItem,
                Quantidade = DadosPagamentoGateway.Quantidade,
                ValorOriginal = dados.Valor
            };
    }
}