﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AWSSDK.Core" version="3.7.105.2" targetFramework="net472" />
  <package id="AWSSDK.SimpleNotificationService" version="3.7.101.9" targetFramework="net472" />
  <package id="AWSSDK.SQS" version="3.7.2.57" targetFramework="net472" />
  <package id="Castle.Activerecord" version="3.0.0.1" targetFramework="net472" />
  <package id="Castle.Components.Validator" version="2.5.0" targetFramework="net472" />
  <package id="Castle.Core" version="2.5.2" targetFramework="net472" />
  <package id="Castle.Windsor" version="2.5.3" targetFramework="net472" />
  <package id="Elasticsearch.Net" version="5.6.5" targetFramework="net472" />
  <package id="elmah.corelibrary" version="1.2.2" targetFramework="net472" />
  <package id="Flurl" version="2.6.0" targetFramework="net472" />
  <package id="Flurl.Http" version="2.1.1" targetFramework="net472" />
  <package id="Iesi.Collections" version="3.2.0.4000" targetFramework="net472" />
  <package id="log4net" version="1.2.10" targetFramework="net472" />
  <package id="MassTransit" version="8.1.3" targetFramework="net472" />
  <package id="MassTransit.Abstractions" version="8.1.3" targetFramework="net472" />
  <package id="MassTransit.RabbitMQ" version="8.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="4.0.20710.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="2.0.20710.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="2.0.20710.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Caching.Memory" version="6.0.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Diagnostics.HealthChecks" version="6.0.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" version="6.0.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net472" />
  <package id="NEST" version="5.6.5" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net472" />
  <package id="NHibernate" version="3.4.0.4000" targetFramework="net472" />
  <package id="NuGet.Build.Tasks.Pack" version="6.8.0" targetFramework="net472" developmentDependency="true" />
  <package id="Perlink.DomainInfrastructure" version="*******" targetFramework="net472" />
  <package id="Perlink.DomainInfrastructure.Dependencies" version="1.0.1" targetFramework="net472" />
  <package id="Pipelines.Sockets.Unofficial" version="2.2.0" targetFramework="net472" />
  <package id="RabbitMQ.Client" version="6.8.1" targetFramework="net472" />
  <package id="StackExchange.Redis" version="2.2.50" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.1" targetFramework="net472" />
  <package id="System.Diagnostics.PerformanceCounter" version="5.0.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="5.0.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="6.0.8" targetFramework="net472" />
  <package id="System.Threading.Channels" version="7.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="Trinks.Shared" version="1.0.0" targetFramework="net472" />
</packages>