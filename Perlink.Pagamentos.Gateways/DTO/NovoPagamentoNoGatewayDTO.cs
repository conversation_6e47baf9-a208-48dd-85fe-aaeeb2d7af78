﻿using System.Collections.Generic;

namespace Perlink.Pagamentos.Gateways.DTO
{
    public class NovoPagamentoNoGatewayDTO
    {
        public NovoPagamentoNoGatewayDTO()
        {
            RegrasDeSplit = new List<SplitPagamentoNoGatewayDTO>();
        }

        public string IdRecebedorNoGateway { get; set; }
        public string IdCompradorNoGateway { get; set; }
        public string IdCartaoNoGateway { get; set; }
        public int QuantidadeParcelas { get; set; }
        public string Descricao { get; set; }
        public string NomeDeExibicaoNaFatura { get; set; }
        public decimal Valor { get; set; }
        public List<SplitPagamentoNoGatewayDTO> RegrasDeSplit { get; set; }
        public List<ItemPagamentoNoGatewayDTO> Itens { get; set; }
        public DadosDoCartaoDTO Cartao { get; set; }
        public CompradorDTO Comprador { get; set; }
        public EnderecoParaCadastrarDTO EnderecoDeCobranca { get; set; }
    }

    public sealed class SplitPagamentoNoGatewayDTO
    {
        public string IdRecebedorNoGateway { get; set; }
        public bool ResponsavelPelasTaxasDaOperacao { get; set; }
        public bool ResponsavelPeloChargeback { get; set; }
        public bool ResponsavelPeloRestoDaDivisaoDeTaxas { get; set; }
        public decimal Valor { get; set; }
    }

    public class ItemPagamentoNoGatewayDTO
    {
        public int IdItemPagamento { get; set; }
        public string NomeItem { get; set; }
        public decimal ValorOriginal { get; set; }
        public decimal? ValorComDesconto { get; set; }
        public decimal? PercentualDeDesconto { get; set; }
        public int Quantidade { get; set; }
        public bool EhTangivel { get; set; }
    }
}