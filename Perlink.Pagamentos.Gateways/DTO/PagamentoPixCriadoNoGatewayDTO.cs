﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Perlink.Pagamentos.Gateways.DTO
{
    public class PagamentoPixCriadoNoGatewayDTO
    {
        public decimal CustoTransacao { get; set; }
        public string IdCobrancaNoGateway { get; set; }
        public string IdTransacaoNoGateway { get; set; }
        public string NomeDeExibicaoNaFatura { get; set; }
        public bool PagamentoFoiRealizado { get; set; }
        public string PixProviderTid { get; set; }
        public string QrCode { get; set; }
        public string QrCodeUrl { get; set; }
        public DateTime DataDeExpiracao { get; set; }
        public string TipoDeTransacao { get; set; }
        public string Status { get; set; }
        public bool Sucesso { get; set; }
        public string DataDeCriacao { get; set; }
        public string DataDeAtualizacao { get; set; }
        public CompradorCadastradoNoGatewayDTO Comprador { get; set; }
        public DateTime? DataEsperadaDeRecebimento { get; set; }
    }
}
