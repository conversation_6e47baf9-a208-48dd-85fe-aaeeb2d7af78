﻿using System.ComponentModel;

namespace Perlink.Pagamentos.Gateways.Enums
{
    public enum MetodoDePagamentoNoGatewayEnum
    {
        [Description("Pix")]
        Pix = 1,

        [Description("Cartão de crédito")]
        CartaoDeCredito = 2,
    }

    public static class MetodoDePagamentoNoGatewayEnumExtensions
    {
        public static string ObterDescricao(this MetodoDePagamentoNoGatewayEnum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = (DescriptionAttribute)field.GetCustomAttributes(typeof(DescriptionAttribute), false)[0];
            return attribute.Description;
        }
    }
}
