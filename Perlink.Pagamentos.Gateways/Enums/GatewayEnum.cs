﻿using System;

namespace Perlink.Pagamentos.Gateways.Enums
{
    // Os valores correspondem a tabela "Pagamentos.Gateway"
    public enum GatewayEnum
    {
        PagarMe = 1,
        Zoop = 2,
        PagarMeV5 = 3,
    }

    public static class GatewayEnumExtensions
    {
        public static string ObterDescricao(this GatewayEnum value)
        {
            switch (value)
            {
                case GatewayEnum.PagarMe:
                    return "V3";
                case GatewayEnum.PagarMeV5:
                    return "V5";
                case GatewayEnum.Zoop:
                    return "Zoop";
                default:
                    throw new ArgumentOutOfRangeException(nameof(value), value, null);
            }
        }

        public static bool IsPagarMe(this GatewayEnum value)
        {
            return value == GatewayEnum.PagarMe || value == GatewayEnum.PagarMeV5;
        }
    }
}
