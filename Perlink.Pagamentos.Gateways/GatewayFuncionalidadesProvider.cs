﻿using Perlink.Pagamentos.Gateways.Enums;
using System;
using System.Collections.Generic;

namespace Perlink.Pagamentos.Gateways
{
    public static class GatewayFuncionalidadesProvider
    {
        public static List<MetodoDePagamentoNoGatewayEnum> ObterMetodosDePagamentoSuportadosPeloGateway(GatewayEnum gateway)
        {
            switch (gateway) {
                case GatewayEnum.PagarMe:
                case GatewayEnum.Zoop:
                    return ToList(MetodoDePagamentoNoGatewayEnum.CartaoDeCredito);
                case GatewayEnum.PagarMeV5:
                    return ToList(MetodoDePagamentoNoGatewayEnum.CartaoDeCredito, MetodoDePagamentoNoGatewayEnum.Pix);
                default:
                    throw new NotImplementedException();
            }
        }

        private static List<MetodoDePagamentoNoGatewayEnum> ToList(params MetodoDePagamentoNoGatewayEnum[] metodosDePagamento)
            => new List<MetodoDePagamentoNoGatewayEnum>(metodosDePagamento);
    }
}
