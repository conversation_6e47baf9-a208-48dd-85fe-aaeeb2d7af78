﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{47A4C4DB-EB2A-410B-9020-6F1C6DEF3FB4}</ProjectGuid>
    <RuntimeIdentifiers>win</RuntimeIdentifiers>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Perlink.Pagamentos.Gateways</RootNamespace>
    <AssemblyName>Perlink.Pagamentos.Gateways</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <RuntimeIdentifiers>win</RuntimeIdentifiers>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <RuntimeIdentifiers>win</RuntimeIdentifiers>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Flurl.Http">
      <Version>2.1.1</Version>
    </PackageReference>
    <PackageReference Include="Trinks.Shared">
      <Version>1.0.0</Version>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config\IConfigurationProvider.cs" />
    <Compile Include="Config\PagamentoOnlineConfiguration.cs" />
    <Compile Include="Config\PagamentoOnlineGatewayConfiguration.cs" />
    <Compile Include="DTO\AntecipacaoCanceladaDTO.cs" />
    <Compile Include="DTO\AntecipacaoDTO.cs" />
    <Compile Include="DTO\ArquivoCadastradoNoGatewayDTO.cs" />
    <Compile Include="DTO\CartaoCadastradoNoGatewayDTO.cs" />
    <Compile Include="DTO\CartaoParaCadastrarDTO.cs" />
    <Compile Include="DTO\CompradorCadastradoNoGatewayDTO.cs" />
    <Compile Include="DTO\CompradorDTO.cs" />
    <Compile Include="DTO\CompradorParaCadastrarDTO.cs" />
    <Compile Include="DTO\ContaBancariaCadastradaNoGatewayDTO.cs" />
    <Compile Include="DTO\ContaBancariaParaAssociarAoRecebedorNoGatewayDTO.cs" />
    <Compile Include="DTO\DadosContratoRecebedorDTO.cs" />
    <Compile Include="DTO\DadosDoCartaoDTO.cs" />
    <Compile Include="DTO\DadosParaCriarAntecipacaoDTO.cs" />
    <Compile Include="DTO\DadosParaLimiteDeAntecipacaoDTO.cs" />
    <Compile Include="DTO\DadosParaObterRecebiveisDTO.cs" />
    <Compile Include="DTO\DadosUnidadeRecebiveisDTO.cs" />
    <Compile Include="DTO\DadosQrCodeDTO.cs" />
    <Compile Include="DTO\DocumentoCadastradoNoGatewayDTO.cs" />
    <Compile Include="DTO\DocumentoParaAssociarARecebedorDTO.cs" />
    <Compile Include="DTO\EnderecoParaCadastrarDTO.cs" />
    <Compile Include="DTO\EstornarPagamentoDTO.cs" />
    <Compile Include="DTO\FiltroContratosRecebedorGatewayDTO.cs" />
    <Compile Include="DTO\FiltroPesquisaUnidadeRecebiveisDTO.cs" />
    <Compile Include="DTO\LimitesDeAntecipacaoDTO.cs" />
    <Compile Include="DTO\NovoPagamentoNoGatewayDTO.cs" />
    <Compile Include="DTO\PagamentoPixCriadoNoGatewayDTO.cs" />
    <Compile Include="DTO\PagamentoRealizadoNoGatewayDTO.cs" />
    <Compile Include="DTO\RecebedorCadastradoNoGatewayDTO.cs" />
    <Compile Include="DTO\RecebedorParaCadastrarNaPagarmeDTO.cs" />
    <Compile Include="DTO\RecebedorParaCadastrarNoGatewayDTO.cs" />
    <Compile Include="DTO\RecebivelDTO.cs" />
    <Compile Include="Enums\BandeiraDoCartaoEnum.cs" />
    <Compile Include="Enums\GatewayEnum.cs" />
    <Compile Include="Enums\IntervaloTransferenciaEnum.cs" />
    <Compile Include="Enums\MetodoDePagamentoNoGatewayEnum.cs" />
    <Compile Include="Enums\TipoDeContaBancariaEnum.cs" />
    <Compile Include="Exceptions\PagamentoOnlineGatewayException.cs" />
    <Compile Include="GatewayAntecipacaoFactory.cs" />
    <Compile Include="GatewayPagamentoOnlineFactory.cs" />
    <Compile Include="Gateways\PagarMe\Config\PagarMeConfiguration.cs" />
    <Compile Include="Gateways\PagarMe\Converters\PagarMeV5ModelGenerator.cs" />
    <Compile Include="Gateways\PagarMe\Services\PagarMeAntecipacaoService.cs" />
    <Compile Include="GatewayFuncionalidadesProvider.cs" />
    <Compile Include="IGatewayPix.cs" />
    <Compile Include="Gateways\PagarMe\Services\PagarMeV5Service.cs" />
    <Compile Include="Gateways\PagarMe\Services\PagarMeService.cs" />
    <Compile Include="Gateways\Zoop\Config\ZoopConfiguration.cs" />
    <Compile Include="Gateways\Zoop\DTO\DocumentoCadastradoNaZoopDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\EstornoRealizadoNaZoopDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\PagamentoRealizadoNaZoopDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\HistoricoDeTransacaoComFalhaDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\PoliticaDeRecebimentoDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\RecebedorCadastradoNaZoopDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\TokenDeContaCadastradoNaZoopDTO.cs" />
    <Compile Include="IGatewayAntecipacao.cs" />
    <Compile Include="IGatewayPagamentoOnline.cs" />
    <Compile Include="GatewayPixFactory.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utilities\UrlPath.cs" />
    <Compile Include="Utils\Resultado.cs" />
    <Compile Include="Gateways\Zoop\Config\VendedorConfigurationElement.cs" />
    <Compile Include="Gateways\Zoop\Config\ZoopConfigurationElement.cs" />
    <Compile Include="Gateways\Zoop\Conversores\CardBrandToBandeiraDoCartaoEnum.cs" />
    <Compile Include="Gateways\Zoop\DTO\CompradorCadastradoNaZoopDTO.cs" />
    <Compile Include="Gateways\Zoop\DTO\TokenDeCartaoCadastradoNaZoopDTO.cs" />
    <Compile Include="Gateways\Zoop\Service\ZoopService.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="..\Trinks.Integracoes.Pagarme.V5.Abstractions\Trinks.Integracoes.PagarMe.V5.Abstractions.csproj">
      <Project>{4959ff33-2b0f-457f-9573-8294e7b46e08}</Project>
      <Name>Trinks.Integracoes.PagarMe.V5.Abstractions</Name>
    </ProjectReference>
    <ProjectReference Include="..\Trinks.Integracoes.PagarMe.V5.Client\Trinks.Integracoes.PagarMe.V5.Client.csproj">
      <Project>{55B24B0C-0731-4DB2-9078-F003C02A81A4}</Project>
      <Name>Trinks.Integracoes.PagarMe.V5.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\Trinks.Integracoes.PagarMe\Trinks.Integracoes.PagarMe.csproj">
      <Project>{0524605A-FC11-4A4A-98D8-68436158F26D}</Project>
      <Name>Trinks.Integracoes.PagarMe</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>