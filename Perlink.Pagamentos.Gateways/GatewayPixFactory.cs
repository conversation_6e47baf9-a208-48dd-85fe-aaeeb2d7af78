﻿using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Pagamentos.Gateways.Config;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Services;
using System;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Interface;

namespace Perlink.Trinks.Pagamentos.Factories
{
    public static class GatewayPixFactory
    {
        public static IGatewayPix ObterGatewayParaPagamentoPix(GatewayEnum gateway, IConfigurationProvider configurationProvider)
        {
            switch (gateway)
            {
                case GatewayEnum.PagarMeV5:
                    return new PagarMeV5Service(configurationProvider);

                default:
                    throw new NotImplementedException();
            }
        }
    }
}
