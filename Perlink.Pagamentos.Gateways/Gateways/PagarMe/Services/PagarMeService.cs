﻿using Newtonsoft.Json;
using Perlink.Pagamentos.Gateways.Config;
using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Config;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Trinks.Integracoes.PagarMe;
using Trinks.Integracoes.PagarMe.Dto;
using Trinks.Integracoes.PagarMe.Enums;

namespace Perlink.Pagamentos.Gateways.Gateways.PagarMe.Services
{
    public class PagarMeService : IGatewayPagamentoOnline, IGatewayAntecipacao
    {
        private readonly PagarMeConfiguration _configuration;
        private readonly PagarMeApi _pagarMeApi;
        private readonly PagarMeAntecipacaoService antecipacaoService;

        public object PagamentoOnlineConfiguration { get; set; }

        public PagarMeService(IConfigurationProvider configurationProvider)
        {
            _configuration = new PagarMeConfiguration(configurationProvider);
            _pagarMeApi = new PagarMeApi(_configuration.ApiKey, _configuration.VersionHeader);
            antecipacaoService = new PagarMeAntecipacaoService(configurationProvider);
        }

        public Task<Resultado<CartaoCadastradoNoGatewayDTO>> CadastrarCartaoDeCredito(string idDoCompradorNoGateway, CartaoParaCadastrarDTO cartaoParaCadastro)
        {
            throw new NotImplementedException();
        }

        public Task<Resultado<string>> CadastrarComprador(CompradorParaCadastrarDTO compradorParaCadastro)
        {
            throw new NotImplementedException();
        }

        public async Task<Resultado<ContaBancariaCadastradaNoGatewayDTO>> CadastrarOuObterContaBancaria(ContaBancariaParaAssociarAoRecebedorNoGatewayDTO conta)
        {
            var result = await _pagarMeApi.Recipients.GetRecipientById(conta.IdDoRecebedorNoGateway);

            if (!result.Success)
            {
                return Resultado.Falhar<ContaBancariaCadastradaNoGatewayDTO>(result.ErrorDetails);
            }

            string idDaConta = result.Content.BankAccount.Id.ToString();

            return await Task.FromResult(Resultado.Ok(new ContaBancariaCadastradaNoGatewayDTO()
            {
                id = idDaConta,
                fingerprint = idDaConta
            }));
        }


        public Task<Resultado<ArquivoCadastradoNoGatewayDTO>> CadastrarDocumentoParaRecebedor(DocumentoParaAssociarARecebedorDTO documento)
        {
            throw new NotImplementedException();
        }

        public async Task<Resultado<RecebedorCadastradoNoGatewayDTO>> CadastrarRecebedor(RecebedorParaCadastrarNoGatewayDTO recebedorParaCadastrar,
            ContaBancariaParaAssociarAoRecebedorNoGatewayDTO conta)
        {

            var recebedorDTO = recebedorParaCadastrar as RecebedorParaCadastrarNaPagarmeDTO;

            NewRecipientForRegistrationDTO dto = new NewRecipientForRegistrationDTO()
            {
                TransferInterval = _configuration.IntervaloPadraoDaTransferenciaAutomatica,
                TransferDay = _configuration.DiaPadraoDaTransferenciaAutomatica,
                TransferEnabled = conta.CodigoBanco != "000" && _configuration.TransferenciaAutomaticaAtivadaPorPadrao,
                AnticipatableVolumePercentage = _configuration.PorcentagemDeVolumeDaAntecipacaoParaNovosCadastros,
                AutomaticAnticipationEnabled = _configuration.HabilitaAntecipacaoAutomaticaParaNovosCadastros,
                PostBackURL = recebedorDTO.PostBackURL,
                BankAccount = new NewBankAccountForRegistrationDTO
                {
                    BankCode = conta.CodigoBanco,
                    Agencia = conta.Agencia,
                    AgenciaDV = conta.AgenciaDV,
                    Conta = conta.NumeroConta,
                    ContaDV = conta.NumeroContaDV,
                    Type = ObterTipoDaContaParaCadastro(conta.TipoDaConta),
                    DocumentNumber = recebedorDTO.CNPJ,
                    LegalName = conta.Titular,
                },
                Mcc = recebedorDTO.Mcc,
                RegisterInformation = new NewRegisterInformationDTO
                {
                    CompanyName = recebedorDTO.Nome,
                    Type = recebedorDTO.TipoDeRegistro,
                    DocumentNumber = recebedorDTO.CNPJ,
                    AnnualRevenue = recebedorDTO.FaturamentoAnualEmpresa,
                    PhoneNumbers = new List<PhoneNumber> {
                        new PhoneNumber {
                            Ddd = recebedorDTO.TelefoneEmpresa.Substring(0, 2),
                            Number = recebedorDTO.TelefoneEmpresa.Substring(2),
                            Type = "mobile"
                        }
                    },
                    Email = recebedorDTO.EmailEmpresa,
                    SiteUrl = recebedorDTO.WebsiteEmpresa,
                    MainAddress = new Address
                    {
                        City = recebedorDTO.Endereco.Cidade,
                        Complementary = recebedorDTO.Endereco.Complemento,
                        Neighborhood = recebedorDTO.Endereco.Bairro,
                        ReferencePoint = recebedorDTO.Endereco.PontoDeReferencia,
                        State = recebedorDTO.Endereco.Estado,
                        Street = recebedorDTO.Endereco.Logradouro,
                        StreetNumber = recebedorDTO.Endereco.Numero,
                        Zipcode = recebedorDTO.Endereco.CEP
                    },
                    ManagingPartners = new List<ManagingPartners> {
                        new ManagingPartners {
                            Birthdate = recebedorDTO.Responsavel.DataNascimento,
                            DocumentNumber = recebedorDTO.Responsavel.NumeroDocumento,
                            Email = recebedorDTO.Responsavel.Email,
                            Name = recebedorDTO.Responsavel.Nome,
                            PhoneNumbers = new List<PhoneNumber> {
                                new PhoneNumber {
                                    Ddd = recebedorDTO.Responsavel.Telefone.Substring(0, 2),
                                    Number = recebedorDTO.Responsavel.Telefone.Substring(2),
                                    Type = "primary"
                                }
                            },
                            Type = recebedorDTO.Responsavel.Tipo,
                            MonthlyIncome = recebedorDTO.Responsavel.RendaMensalDeclaradaCentavos.ToString(),
                            ProfessionalOccupation = recebedorDTO.Responsavel.ProfissaoDoResponsavel,
                            SelfDeclaredLegalRepresentative = recebedorDTO.Responsavel.EhRepresentanteLegalDaEmpresa,
                            ManagingPartnerAddress = new Address {
                                City = recebedorDTO.Responsavel.Endereco.Cidade,
                                Complementary = recebedorDTO.Responsavel.Endereco.Complemento,
                                Neighborhood = recebedorDTO.Responsavel.Endereco.Bairro,
                                ReferencePoint = recebedorDTO.Responsavel.Endereco.PontoDeReferencia,
                                State = recebedorDTO.Responsavel.Endereco.Estado,
                                Street = recebedorDTO.Responsavel.Endereco.Logradouro,
                                StreetNumber = recebedorDTO.Responsavel.Endereco.Numero,
                                Zipcode = recebedorDTO.Responsavel.Endereco.CEP
                            }
                        }
                    }
                }
            };

            var result = (await _pagarMeApi.Recipients.CreateNewRecipient(dto));
            if (result.Success)
            {
                var response = new RecebedorCadastradoNoGatewayDTO()
                {
                    Id = result.Content.Id,
                    Habilitado = result.Content.Status.Equals("active"),
                    StatusNoGateway = result.Content.Status,
                };

                return Resultado.Ok(response);
            }
            else
                return Resultado.Falhar<RecebedorCadastradoNoGatewayDTO>(result.ErrorDetails);
        }

        public void Dispose()
        {
        }

        public async Task<Resultado<string>> EstornarTransacao(EstornarPagamentoDTO pagamentoParaEstorno)
        {
            var transacao = await _pagarMeApi.Transactions.GetAsync(pagamentoParaEstorno.IdTransacaoNoGateway);

            if (!transacao.Success)
                return Resultado.Falhar<string>(transacao.ErrorDetails);

            if (TransacaoEstaEstornada(transacao.Content))
                return Resultado.Ok(transacao.Content.Status.ToString());

            var estorno = await _pagarMeApi.Transactions.Refund(new NewRefoundForRegistrationDTO() { IdTransaction = pagamentoParaEstorno.IdTransacaoNoGateway });

            if (!estorno.Success)
                return Resultado.Falhar<string>(estorno.ErrorDetails);

            return Resultado.Ok(estorno.Content.Status.ToString());
        }

        public Task<Resultado<bool>> ExcluirCartaoDeCredito(string idDoCartaoNoGateway)
        {
            throw new NotImplementedException();
        }

        public async Task<Resultado<PagamentoRealizadoNoGatewayDTO>> RealizarPagamentoOnline(NovoPagamentoNoGatewayDTO novoPagamento)
        {

            var transacao = new CreditCardTransaction()
            {
                Amount = ConverterValorParaCentavos(novoPagamento.Valor),
                Installments = novoPagamento.QuantidadeParcelas.ToString(),
                SplitRules = novoPagamento.RegrasDeSplit.Select(f => new SplitRule
                {
                    Amount = ConverterValorParaCentavos(f.Valor),
                    ChargeProcessingFee = f.ResponsavelPelasTaxasDaOperacao,
                    RecipientId = f.IdRecebedorNoGateway,
                    Liable = !f.ResponsavelPelasTaxasDaOperacao
                }).ToList()
            };

            var jaPossuiCartaoNoGateway = !string.IsNullOrEmpty(novoPagamento.IdCartaoNoGateway);

            if (jaPossuiCartaoNoGateway)
            {
                transacao.CardId = novoPagamento.IdCartaoNoGateway;
            }
            else
            {
                transacao.CreditCardHolderName = novoPagamento.Cartao.Nome;
                transacao.CreditCardNumber = novoPagamento.Cartao.Numero;
                transacao.CreditCardExpiration = novoPagamento.Cartao.Validade.Replace("/", "");
                transacao.CreditCardCVV = novoPagamento.Cartao.CVV;
            }

            var jaPossuiCompradorNoGateway = !string.IsNullOrEmpty(novoPagamento.IdCompradorNoGateway);

            if (jaPossuiCompradorNoGateway)
            {
                var dadosDoComprador = await _pagarMeApi.Customers.GetCustomerById(novoPagamento.IdCompradorNoGateway);
                var primeiroDocumento = dadosDoComprador.Content.Documents[0];
                var documentoSerializado = JsonConvert.SerializeObject(primeiroDocumento);
                var documentoDesserializado = JsonConvert.DeserializeObject<DocumentoCadastradoNoGatewayDTO>(documentoSerializado);
                var cpf = documentoDesserializado.Number;
                novoPagamento.Comprador.CpfCnpj = cpf;
            }

            if (novoPagamento.Comprador != null)
            {
                transacao.Customer = GerarComprador(novoPagamento.Comprador);
                transacao.Billing = GerarEnderecoDeCobranca(novoPagamento.Comprador.Nome, novoPagamento.EnderecoDeCobranca);
            }

            if (novoPagamento.Itens?.Count > 0)
            {
                transacao.Items = novoPagamento.Itens.Select(f => new Item
                {
                    Title = f.NomeItem,
                    Id = f.IdItemPagamento.ToString(),
                    UnitPrice = ConverterValorParaCentavos(f.ValorOriginal),
                    Quantity = f.Quantidade,
                    Tangible = f.EhTangivel
                }).ToList();
            }

            var response = await _pagarMeApi.Transactions.CreateNewCreditCardTransaction(transacao);

            if (response.Success)
            {
                var dadosDoPagamento = response.Content;

                Enum.TryParse(dadosDoPagamento.Card.Bandeira, out BandeiraDoCartaoEnum bandeira);

                return Resultado.Ok(new PagamentoRealizadoNoGatewayDTO()
                {
                    CustoTransacao = dadosDoPagamento.Cost,
                    IdTransacaoNoGateway = dadosDoPagamento.Id,
                    PagamentoFoiRealizado = dadosDoPagamento.Status == TransactionStatus.Paid,
                    Status = dadosDoPagamento.Status.ToString(),
                    InformacoesDeErro = dadosDoPagamento.RefuseReason,
                    UltimosDigitos = dadosDoPagamento.CardLastDigits,
                    PrimeirosDigitosDoCartao = dadosDoPagamento.CardFirstDigits,
                    Comprador = new CompradorCadastradoNoGatewayDTO()
                    {
                        IdNoGateway = dadosDoPagamento.Customer.Id.ToString(),
                    },
                    Cartao = new CartaoCadastradoNoGatewayDTO()
                    {
                        Bandeira = bandeira,
                        IdDoCartaoNoGateway = dadosDoPagamento.Card.Id,
                        Fingerprint = dadosDoPagamento.Card.Fingerprint,
                        Valido = dadosDoPagamento.Card.Valido
                    }
                });
            }
            else
            {
                return Resultado.Falhar<PagamentoRealizadoNoGatewayDTO>(response.ErrorDetails);
            }
        }

        public async Task<Resultado<decimal>> ObterSaldoDisponivelDoRecebedorNoGateway(string idDoRecebedorNoGateway)
            => await antecipacaoService.ObterSaldoDisponivelDoRecebedorNoGateway(idDoRecebedorNoGateway);

        public Dictionary<string, string> ObterDictionaryDoPayloadPostBack(string stringPayload)
        {

            Dictionary<string, string> obj = new Dictionary<string, string>();

            foreach (string pair in stringPayload.Split('&'))
            {
                string[] keyValue = pair.Split('=');
                obj.Add(keyValue[0], keyValue[1]);
            }

            return obj;
        }

        public bool ValidarOrigemPostback(string signature, string jsonBody)
        {

            var _apiKey = _configuration.ApiKey;
            var requestBody = jsonBody;
            var isValid = false;

            byte[] apiKeyBytes = Encoding.ASCII.GetBytes(_apiKey);
            byte[] rawBodyBytes = Encoding.ASCII.GetBytes(requestBody);
            string cleanedSignature = signature.Split('=')[1];
            using (HMACSHA1 hmac = new HMACSHA1(apiKeyBytes))
            {
                byte[] rawBodyHashBytes = hmac.ComputeHash(rawBodyBytes);
                String rawBodyHash = BitConverter.ToString(rawBodyHashBytes).Replace("-", String.Empty).ToLower();
                isValid = rawBodyHash.Equals(cleanedSignature);
            }

            return isValid;
        }

        public async Task<Resultado<List<RecebivelDTO>>> ObterRecebiveis(DadosParaObterRecebiveisDTO dados)
            => await antecipacaoService.ObterRecebiveis(dados);

        public async Task<Resultado<List<AntecipacaoDTO>>> ObterAntecipacoesPorIdRecebedor(string idRecebedorNoGateway, string status)
            => await antecipacaoService.ObterAntecipacoesPorIdRecebedor(idRecebedorNoGateway, status);

        public async Task<Resultado<AntecipacaoCanceladaDTO>> CancelarAntecipacao(string idRecebedorNoGateway, string idAntecipacao)
            => await antecipacaoService.CancelarAntecipacao(idRecebedorNoGateway, idAntecipacao);

        public async Task<Resultado<AntecipacaoDTO>> RealizarAntecipacao(DadosParaCriarAntecipacaoDTO dados)
            => await antecipacaoService.RealizarAntecipacao(dados);

        public async Task<Resultado<LimitesDeAntecipacaoDTO>> ObterLimitesDeAntecipacao(DadosParaLimiteDeAntecipacaoDTO dados)
            => await antecipacaoService.ObterLimitesDeAntecipacao(dados);

        public async Task<Resultado<List<DadosUnidadeRecebiveisDTO>>> ObterUnidadeRecebiveis(FiltroPesquisaUnidadeRecebiveisDTO dados)
            => await antecipacaoService.ObterUnidadeRecebiveis(dados);

        public async Task<Resultado<RecebedorCadastradoNoGatewayDTO>> AtualizarRecebedorParaAntecipacoes(string idRecebedorNoGateway)
            => await antecipacaoService.AtualizarRecebedorParaAntecipacoes(idRecebedorNoGateway);

        public async Task<Resultado<DadosContratoRecebedorDTO>> ObterContratos(FiltroContratosRecebedorGatewayDTO filtro)
        {

            var dto = new GetSettlementObligationDTO()
            {
                StartDate = filtro.DataInicio,
                EndDate = filtro.DataFim,
                RecipientId = filtro.IdRecebedorGateway,
                Pagination = new PaginationDTO()
                {
                    Page = filtro.Pagina,
                    PageSize = filtro.RegistrosPorPagina
                }
            };

            var response = await _pagarMeApi.Register.GetSettlementObligation(dto);

            if (!response.Success)
                return Resultado.Falhar<DadosContratoRecebedorDTO>(response.ErrorDetails);

            var dadosContrato = response.Content.SettlementData.Select(c => new DadosContratoDTO()
            {
                DataDeContratoPrevista = c.ExpectedSettlementDate,
                DetentorDeAtivoOriginal = c.OriginalAssetHolder,
                ObrigacoesDeContrato = c.SettlementObligations.Select(o => new ObrigacaoContratoDTO()
                {
                    ChaveContrato = o.ContractKey,
                    DataDeContratoPrevista = o.ExpectedSettlementDate,
                    DetentorContrato = o.ContractHolder,
                    EsquemaDePagamento = o.PaymentScheme,
                    MetodoDivisao = o.DivisionMethod,
                    PrioridadeEfeito = o.EffectPriority,
                    TipoContrato = o.ContractType,
                    ValorEfeito = o.EffectAmount,
                    ValorEfeitoComprometido = o.CommittedEffectAmount,
                    ValorNaoComprometido = o.UncommittedAmount,
                    ValorTotal = o.TotalAmount
                })
            });

            var dadosContratoRecebedor = new DadosContratoRecebedorDTO()
            {
                DadosContrato = dadosContrato,
                ResumoPagina = new ResumoPaginaDTO()
                {
                    PaginaAtual = response.Content.PageSummary.CurrentPage,
                    RegistrosPorPagina = response.Content.PageSummary.PerPage,
                    TotalPaginas = response.Content.PageSummary.TotalPages
                }
            };

            return Resultado.Ok(dadosContratoRecebedor);
        }

        public async Task<Resultado<DadosQrCodeDTO>> GerarQrCode(string idRecebedorNoGateway)
        {
            var response = await _pagarMeApi.Recipients.GenerateQrCode(idRecebedorNoGateway);

            if (!response.Success)
                return Resultado.Falhar<DadosQrCodeDTO>(response.ErrorDetails);

            var retorno = response.Content;

            var qrCode = new DadosQrCodeDTO(retorno.QrCodeBase64, retorno.QrCodeUrl, retorno.DataExpiracao);

            return Resultado.Ok(qrCode);
        }

        private BillingInformation GerarEnderecoDeCobranca(string nomeCobranca, EnderecoParaCadastrarDTO enderecoDeCobranca)
        {
            var billing = new BillingInformation()
            {
                Name = nomeCobranca
            };

            if (enderecoDeCobranca != null)
            {
                var endereco = enderecoDeCobranca;

                billing.Address = new BillingAddress()
                {
                    City = endereco.Cidade,
                    Country = "br",
                    Complementary = endereco.Complemento,
                    Neighborhood = endereco.Bairro,
                    State = endereco.Estado,
                    Street = endereco.Logradouro,
                    StreetNumber = endereco.Numero,
                    ZipCode = endereco.CEP
                };
            }

            return billing;
        }

        private Customer GerarComprador(CompradorDTO comprador)
        {
            var customer = new Customer
            {
                ExternalId = $"{comprador.IdComprador}",
                Name = comprador.Nome,
                Email = comprador.Email,
                Type = "individual",
                Country = "br"
            };

            if (!string.IsNullOrWhiteSpace(comprador.CpfCnpj))
            {
                customer.Documents = new List<CustomerDocument> {
                    new CustomerDocument {
                        Number = comprador.CpfCnpj,
                        Type = "cpf"
                    }
                };
            }

            if (!string.IsNullOrWhiteSpace(comprador.Telefone))
            {
                customer.PhoneNumbers = new List<string> {
                    "+55"+ comprador.Telefone
                };
            }

            if (comprador.IdComprador > 0)
            {
                customer.ExternalId = comprador.IdComprador.ToString();
            }

            return customer;
        }

        private int ConverterValorParaCentavos(decimal valor)
        {
            var parteInteiraEmCentavos = Decimal.Truncate(valor) * 100;
            var parteDecimal = (int)(((decimal)valor % 1) * 100);

            return Convert.ToInt32(parteInteiraEmCentavos + parteDecimal);
        }

        private string ObterTipoDaContaParaCadastro(TipoDeContaBancariaEnum tipo)
        {
            switch (tipo)
            {
                case TipoDeContaBancariaEnum.Corrente:
                    return "conta_corrente";
                case TipoDeContaBancariaEnum.CorrenteConjunta:
                    return "conta_corrente_conjunta";
                case TipoDeContaBancariaEnum.Poupanca:
                    return "conta_poupanca";
                case TipoDeContaBancariaEnum.PoupancaConjunta:
                    return "conta_poupanca_conjunta";
                default:
                    return String.Empty;
            }
        }

        private static bool TransacaoEstaEstornada(RegisteredTransactionDTO transaction)
            => transaction.Status == TransactionStatus.Refunded;
    }
}
