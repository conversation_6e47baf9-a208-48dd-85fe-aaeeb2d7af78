﻿using Perlink.Pagamentos.Gateways.Config;
using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Config;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Trinks.Integracoes.PagarMe;
using Trinks.Integracoes.PagarMe.Dto;

namespace Perlink.Pagamentos.Gateways.Gateways.PagarMe.Services
{
    public class PagarMeAntecipacaoService : IGatewayAntecipacao
    {
        private readonly PagarMeApi _pagarMeApi;
        private readonly PagarMeConfiguration _configuration;

        public PagarMeAntecipacaoService(IConfigurationProvider configurationProvider)
        {
            _configuration = new PagarMeConfiguration(configurationProvider);
            _pagarMeApi = new PagarMeApi(_configuration.ApiKey, _configuration.VersionHeader);
        }

        public async Task<Resultado<List<RecebivelDTO>>> ObterRecebiveis(DadosParaObterRecebiveisDTO dados)
        {
            var filtro = new GetPayablesDTO(dados.IdRecebedorNoGateway, dados.DataInicio, dados.DataFinal, dados.Status);

            var response = await _pagarMeApi.Payables.GetPayablesByRecipientIdAndDate(filtro);

            if (!response.Success)
                return Resultado.Falhar<List<RecebivelDTO>>(response.ErrorDetails);

            var dadosDosRecebiveis = response.Content;
            var recebiveis = ProcessarRecebiveisEstornados(dadosDosRecebiveis);

            return Resultado.Ok(recebiveis);
        }

        public async Task<Resultado<List<AntecipacaoDTO>>> ObterAntecipacoesPorIdRecebedor(string idRecebedorNoGateway, string status)
        {
            var response = await _pagarMeApi.Anticipations.GetAnticipationsById(idRecebedorNoGateway, status);

            if (!response.Success)
                return Resultado.Falhar<List<AntecipacaoDTO>>(response.ErrorDetails);

            var dadosDeAntecipacoes = response.Content;
            var antecipacoes = dadosDeAntecipacoes.Select(a => new AntecipacaoDTO()
            {
                Id = a.Id,
                Status = a.Status,
                TaxaDeAntecipacao = a.AnticipationFee,
                Valor = a.Amount,
                DataDePagamento = a.PaymentDate
            })
            .ToList();

            return Resultado.Ok(antecipacoes);
        }

        public async Task<Resultado<AntecipacaoCanceladaDTO>> CancelarAntecipacao(string idRecebedorNoGateway, string idAntecipacao)
        {
            var response = await _pagarMeApi.Anticipations.CancelAnticipation(idRecebedorNoGateway, idAntecipacao);

            if (!response.Success)
                return Resultado.Falhar<AntecipacaoCanceladaDTO>(response.ErrorDetails);

            var retorno = response.Content;

            var antecipacaoCancelada = new AntecipacaoCanceladaDTO
            {
                Id = retorno.Id,
                Status = retorno.Status,
                DataDePagamento = retorno.PaymentDate
            };
            return Resultado.Ok(antecipacaoCancelada);
        }

        public async Task<Resultado<AntecipacaoDTO>> RealizarAntecipacao(DadosParaCriarAntecipacaoDTO dados)
        {
            var newAnticipation = new NewAnticipationForRegistrationDTO
            {
                IdRecipient = dados.IdRecebedorNoGateway,
                Build = false,
                AutomaticTransfer = true,
                PaymentDate = dados.DataDePagamento,
                Timeframe = dados.Periodo,
                RequestedAmount = dados.Valor
            };

            var response = await _pagarMeApi.Anticipations.CreateNewAnticipation(newAnticipation);

            if (!response.Success)
                return Resultado.Falhar<AntecipacaoDTO>(response.ErrorDetails);

            var retorno = response.Content;
            var antecipacao = new AntecipacaoDTO
            {
                TaxaDeAntecipacao = retorno.AnticipationFee,
                Valor = retorno.Amount,
                DataDePagamento = retorno.PaymentDate,
                Id = retorno.Id,
                Status = retorno.Status
            };

            return Resultado.Ok(antecipacao);
        }

        private List<RecebivelDTO> ProcessarRecebiveisEstornados(List<RegisteredPayablesDTO> dadosDosRecebiveis)
        {
            var recebiveisDict = dadosDosRecebiveis
                .Select(r => new
                {
                    Valor = (decimal)r.Amount / 100,
                    DataDePagamento = DateTime.Parse(r.PaymentDate),
                    IdTransacaoNoGateway = r.TransactionId,
                    IdAntecipacao = r.AnticipationId,
                    Status = r.Status
                })
                .OrderBy(r => r.DataDePagamento)
                .ThenBy(r => r.Valor < 0)
                .GroupBy(r => r.IdTransacaoNoGateway)
                .ToDictionary(
                    g => g.Key,
                    g =>
                    {
                        var item = g.First();
                        return new RecebivelDTO
                        {
                            Valor = item.Valor,
                            DataDePagamento = item.DataDePagamento,
                            IdTransacaoNoGateway = item.IdTransacaoNoGateway,
                            IdAntecipacao = item.IdAntecipacao,
                            Status = g.Any(x => x.Valor < 0) ? "reversed" : item.Status
                        };
                    });

            return recebiveisDict.Values.OrderBy(r => r.DataDePagamento).ToList();
        }

        public async Task<Resultado<List<DadosUnidadeRecebiveisDTO>>> ObterUnidadeRecebiveis(FiltroPesquisaUnidadeRecebiveisDTO dados)
        {

            var dto = new GetReceivablesUnitDTO(dados.IdRecebedorGateway, dados.DataInicio, dados.DataFim);

            var response = await _pagarMeApi.ReceivablesUnit.GetReceivablesUnit(dto);

            if (!response.Success)
                return Resultado.Falhar<List<DadosUnidadeRecebiveisDTO>>(response.ErrorDetails);

            var listaUnidadeRecebiveis = response.Content.Select(ru => new DadosUnidadeRecebiveisDTO()
            {
                DataPagamento = ru.PaymentDate,
                MarcaCartao = ru.CardBrand,
                MetodoPagamento = ru.PaymentMethod,
                ValorLiquido = ConverterValorCentavosParaReais(ru.Amount),
                ValorBruto = ConverterValorCentavosParaReais(ru.CreditAmount),
                Deducoes = new DeducaoUnidadeRecebiveisDTO()
                {
                    ValorChargebackAReceber = ConverterValorCentavosParaReais(ru.ChargebackAmount),
                    ValorChargebackRecebido = ConverterValorCentavosParaReais(ru.ChargebackRefundAmount),
                    ValorEstorno = ConverterValorCentavosParaReais(ru.RefundAmount),
                    ValorLiquidacoes = ConverterValorCentavosParaReais(ru.LiquidationAmount),
                    ValorTaxa = ConverterValorCentavosParaReais(ru.FeeAmount)
                },
            }).ToList();

            return Resultado.Ok(listaUnidadeRecebiveis);
        }

        private decimal ConverterValorCentavosParaReais(int valorCentavos)
        {
            var valor = (decimal)valorCentavos / 100;

            return valor;
        }

        public async Task<Resultado<decimal>> ObterSaldoDisponivelDoRecebedorNoGateway(string idDoRecebedorNoGateway)
        {
            var result = await _pagarMeApi.Recipients.GetRecipientBalance(idDoRecebedorNoGateway);

            if (result.Failure)
                return Resultado.Falhar<decimal>("Recebedor não encontrado");

            return Resultado.Ok(result.Content);
        }

        public async Task<Resultado<LimitesDeAntecipacaoDTO>> ObterLimitesDeAntecipacao(DadosParaLimiteDeAntecipacaoDTO dados)
        {
            var filtro = new GetAnticipationLimitsDTO
            {
                PaymentDate = dados.DataDePagamento,
                RecipientId = dados.IdRecebedorNoGateway,
                TimeFrame = dados.Periodo
            };

            var response = await _pagarMeApi.Anticipations.GetAnticipationLimits(filtro);

            if (!response.Success)
                return Resultado.Falhar<LimitesDeAntecipacaoDTO>(response.ErrorDetails);

            var retorno = response.Content;
            var limites = new LimitesDeAntecipacaoDTO
            {
                Minimo = new Minimo
                {
                    Taxa = retorno.Minimum.Fee,
                    TaxaDeAntecipacao = retorno.Minimum.AnticipationFee,
                    Valor = retorno.Minimum.Amount,
                    TaxaAntiCorrupcao = retorno.Minimum.FraudCoverageFee
                },
                Maximo = new Maximo
                {
                    Taxa = retorno.Maximum.Fee,
                    TaxaDeAntecipacao = retorno.Maximum.AnticipationFee,
                    Valor = retorno.Maximum.Amount,
                    TaxaAntiCorrupcao = retorno.Maximum.FraudCoverageFee
                }
            };

            return Resultado.Ok(limites);
        }

        public async Task<Resultado<RecebedorCadastradoNoGatewayDTO>> AtualizarRecebedorParaAntecipacoes(string idRecebedorNoGateway)
        {
            var dados = new UpdateRecipientDTO
            {
                IdRecipient = idRecebedorNoGateway,
                AnticipatableVolumePercentage = _configuration.PorcentagemDeVolumeDaAntecipacao,
                AutomaticAnticipationEnabled = _configuration.HabilitaAntecipacaoAutomatica,
            };

            var response = await _pagarMeApi.Recipients.UpdateRecipient(dados);

            if (!response.Success)
                return Resultado.Falhar<RecebedorCadastradoNoGatewayDTO>(response.ErrorDetails);

            var retorno = response.Content;
            var recebedor = new RecebedorCadastradoNoGatewayDTO
            {
                Id = retorno.Id,
                Habilitado = retorno.Status.Equals("active"),
                StatusNoGateway = retorno.Status,
            };

            return Resultado.Ok(recebedor);
        }
    }
}
