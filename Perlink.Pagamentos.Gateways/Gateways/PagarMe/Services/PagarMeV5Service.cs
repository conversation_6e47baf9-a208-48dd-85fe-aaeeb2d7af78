﻿using Perlink.Pagamentos.Gateways.Config;
using Perlink.Pagamentos.Gateways.DTO;
using System.Threading.Tasks;
using Trinks.Integracoes.PagarMe.V5.Client;
using Trinks.Integracoes.PagarMe.V5.Abstractions;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Configuration;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Sections.Orders.Commands;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Converters;
using Perlink.Pagamentos.Gateways.Enums;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Config;
using System;
using System.Linq;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Models;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData.Address;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData.Phone;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData.RecipientDetails;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Sections.Recipients.Commands;
using Trinks.Integracoes.PagarMe.V5.Abstractions.ValueObjects;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Sections.Settlements_Obligations.Commands;
using System.Collections.Generic;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Utils;
using Perlink.Pagamentos.Gateways.Gateways.PagarMe.Interface;

namespace Perlink.Pagamentos.Gateways.Gateways.PagarMe.Services
{
    public class PagarMeV5Service : IGatewayPagamentoOnline, IGatewayAntecipacao, IGatewayPix
    {
        #region Configuração
        public const string PagarMeV5SecretKeyConfigurationName = "pagarme_v5_secret_key";
        public const string PagarMeV5WebhookUsernameConfigurationName = "pagarme_v5_webhook_username";
        public const string PagarMeV5WebhookPasswordConfigurationName = "pagarme_v5_webhook_password";

        public static PagarMeV5ClientConfiguration GerarConfiguracaoV5(IConfigurationProvider configurationProvider)
            => new PagarMeV5ClientConfiguration() {
                SecretKey = configurationProvider.GetConfiguration(PagarMeV5SecretKeyConfigurationName),
                WebhookUsername = configurationProvider.GetConfiguration(PagarMeV5WebhookUsernameConfigurationName),
                WebhookPassword = configurationProvider.GetConfiguration(PagarMeV5WebhookPasswordConfigurationName),
            };
        #endregion

        private readonly IPagarMeV5Client pagarMeV5Client;
        private readonly PagarMeConfiguration _configuration;
        private readonly PagarMeAntecipacaoService antecipacaoService;
        private bool _disposed = false;

        public PagarMeV5Service(IConfigurationProvider cp)
        {
            pagarMeV5Client = new PagarMeV5Client(GerarConfiguracaoV5(cp));

            _configuration = new PagarMeConfiguration(cp);
            antecipacaoService = new PagarMeAntecipacaoService(cp);
        }

        public async Task<Resultado<PagamentoRealizadoNoGatewayDTO>> RealizarPagamentoOnline(NovoPagamentoNoGatewayDTO novoPagamento)
            => !string.IsNullOrEmpty(novoPagamento.IdCartaoNoGateway)?
                await RealizarTransacaoComCompradorExistente(novoPagamento) :
                await RealizarTransacaoComNovoComprador(novoPagamento);

        public async Task<Resultado<RecebedorCadastradoNoGatewayDTO>> CadastrarRecebedor(
            RecebedorParaCadastrarNoGatewayDTO recebedorParaCadastrar,
            ContaBancariaParaAssociarAoRecebedorNoGatewayDTO conta)
        {
            var recipient = CreateRecipientCommand(recebedorParaCadastrar, conta);

            var recipientResponse = await pagarMeV5Client.Recipients.CreateRecipientAsync(recipient);

            if (!recipientResponse.IsSuccess)
                return Resultado.Falhar<RecebedorCadastradoNoGatewayDTO>(CreateErrorMessage(recipientResponse));

            var result = new RecebedorCadastradoNoGatewayDTO()
            {
                Id = recipientResponse.Success.Id,
                Habilitado = recipientResponse.Success.Status.Equals("active"),
                StatusNoGateway = recipientResponse.Success.Status,
            };

            return Resultado.Ok(result);
        }

        public async Task<Resultado<DadosQrCodeDTO>> GerarQrCode(string idRecebedorNoGateway)
        {
            var response = await pagarMeV5Client.Recipients.CreateKycLinkAsync(idRecebedorNoGateway);

            if (!response.IsSuccess)
                return Resultado.Falhar<DadosQrCodeDTO>(CreateErrorMessage(response));

            var resultContent = response.Success;

            var result = new DadosQrCodeDTO(resultContent.Base64Qrcode, resultContent.Url, resultContent.ExpiresAt);

            return Resultado.Ok(result);
        }

        public async Task<Resultado<PagamentoPixCriadoNoGatewayDTO>> CriarPedidoPix(NovoPagamentoNoGatewayDTO novoPagamento, int expiracaoDoPedidoSegs)
        {
            var customerData = PagarMeV5ModelGenerator.GenerateCustomerData(novoPagamento);
            var order = new CreateOrderWithNewCustomerCommand(customerData);
            order.Items = PagarMeV5ModelGenerator.GenerateOrderItems(novoPagamento);
            order.PaymentMethods = PagarMeV5ModelGenerator.PreparePaymentWithPix(novoPagamento, expiracaoDoPedidoSegs);

            var orderResponse = await pagarMeV5Client.Orders.CreateOrderAsync(order);
            if (!orderResponse.IsSuccess)
                return Resultado.Falhar<PagamentoPixCriadoNoGatewayDTO>(CreateErrorMessage(orderResponse.Failure, orderResponse.StatusCode));

            var result = PagarMeV5ModelGenerator.ToPagamentoPixCriadoNoGatewayDTO(orderResponse.Success);

            return Resultado.Ok(result);

        }

        #region Estorno
        public async Task<Resultado<string>> EstornarTransacao(EstornarPagamentoDTO pagamentoParaEstorno)
        {
            var cobranca = await pagarMeV5Client.Charges.GetChargeAsync(pagamentoParaEstorno.IdCobrancaNoGateway);

            if (!cobranca.IsSuccess)
                return Resultado.Falhar<string>(CreateErrorMessage(cobranca));

            if (CobrancaEstaCancelada(cobranca.Success))
                return Resultado.Ok(cobranca.Success.Status);

            var estorno = await pagarMeV5Client.Charges.DeleteChargeAsync(pagamentoParaEstorno.IdCobrancaNoGateway);

            if (!estorno.IsSuccess)
                return Resultado.Falhar<string>(CreateErrorMessage(estorno));

            return Resultado.Ok(estorno.Success.Status);
        }

        public async Task<Resultado<ContaBancariaCadastradaNoGatewayDTO>> CadastrarOuObterContaBancaria(ContaBancariaParaAssociarAoRecebedorNoGatewayDTO conta)
        {
            var result = await pagarMeV5Client.Recipients.GetRecipientAsync(conta.IdDoRecebedorNoGateway);

            if (!result.IsSuccess)
            {
                return Resultado.Falhar<ContaBancariaCadastradaNoGatewayDTO>(result.Failure.Message);
            }

            string idDaConta = result.Success.DefaultBankAccount.Id;

            return await Task.FromResult(Resultado.Ok(new ContaBancariaCadastradaNoGatewayDTO()
            {
                id = idDaConta,
                fingerprint = idDaConta
            }));
        }
        #endregion

        public async Task<Resultado<DadosContratoRecebedorDTO>> ObterContratos(FiltroContratosRecebedorGatewayDTO filtro)
        {

            var dto = new GetSettlementObligationCommand(filtro.IdRecebedorGateway)
            {
                ExpectedSettlementDateSince = filtro.DataInicio,
                ExpectedSettlementDateUntil = filtro.DataFim,
                Page = filtro.Pagina,
                PageSize = filtro.RegistrosPorPagina,
            };

            var response = await pagarMeV5Client.SettlementObligations.GetSettlementObligationsAsync(dto);

            if (!response.IsSuccess)
                return Resultado.Falhar<DadosContratoRecebedorDTO>(response.Failure.Message);

            var dadosContrato = response.Success?.Items.Select(c => new DadosContratoDTO()
            {
                DataDeContratoPrevista = DateTime.Parse(c.ExpectedSettlementDate),
                DetentorDeAtivoOriginal = c.OriginalAssetHolder,
                ObrigacoesDeContrato = c.SettlementObligations.Select(o => new ObrigacaoContratoDTO()
                {
                    ChaveContrato = o.ContractKey,
                    DataDeContratoPrevista = DateTime.Parse(c.ExpectedSettlementDate),
                    DetentorContrato = o.ContractHolder,
                    EsquemaDePagamento = o.PaymentScheme,
                    MetodoDivisao = o.DivisionMethod,
                    PrioridadeEfeito = o.EffectPriority,
                    TipoContrato = o.ContractType,
                    ValorEfeito = o.EffectAmount,
                    ValorEfeitoComprometido = o.CommittedEffectAmount,
                    ValorNaoComprometido = o.UncommittedAmount,
                    ValorTotal = o.TotalAmount
                })
            }) ?? new List<DadosContratoDTO>();

            var dadosContratoRecebedor = new DadosContratoRecebedorDTO()
            {
                DadosContrato = dadosContrato,
                ResumoPagina = new ResumoPaginaDTO()
                {
                    PaginaAtual = filtro.Pagina,
                    RegistrosPorPagina = filtro.RegistrosPorPagina,
                    TotalPaginas = response.Success?.Paging.Total ?? 0,
                }
            };

            return Resultado.Ok(dadosContratoRecebedor);
        }

        #region RealizarPagamentoOnline
        private async Task<Resultado<PagamentoRealizadoNoGatewayDTO>> RealizarTransacaoComNovoComprador(NovoPagamentoNoGatewayDTO novoPagamento)
        {
            var customerData = PagarMeV5ModelGenerator.GenerateCustomerData(novoPagamento);
            var order = new CreateOrderWithNewCustomerCommand(customerData);

            order.Items = PagarMeV5ModelGenerator.GenerateOrderItems(novoPagamento);
            order.PaymentMethods = PagarMeV5ModelGenerator.PreparePaymentWithNewCreditCard(novoPagamento);

            var orderResponse = await pagarMeV5Client.Orders.CreateOrderAsync(order);

            if (!orderResponse.IsSuccess)
                return Resultado.Falhar<PagamentoRealizadoNoGatewayDTO>(CreateErrorMessage(orderResponse));

            var result = PagarMeV5ModelGenerator.ToPagamentoNoGateway(orderResponse.Success);

            return Resultado.Ok(result);
        }

        private async Task<Resultado<PagamentoRealizadoNoGatewayDTO>> RealizarTransacaoComCompradorExistente(NovoPagamentoNoGatewayDTO novoPagamento)
        {
            var order = new CreateOrderWithExistingCustomerCommand(novoPagamento.IdCompradorNoGateway);

            order.Items = PagarMeV5ModelGenerator.GenerateOrderItems(novoPagamento);
            order.PaymentMethods = PagarMeV5ModelGenerator.PreparePaymentWithExistingCreditCard(novoPagamento);

            var orderResponse = await pagarMeV5Client.Orders.CreateOrderAsync(order);

            if (!orderResponse.IsSuccess)
                return Resultado.Falhar<PagamentoRealizadoNoGatewayDTO>(CreateErrorMessage(orderResponse));

            var result = PagarMeV5ModelGenerator.ToPagamentoNoGateway(orderResponse.Success);

            return Resultado.Ok(result);
        }

        private static string CreateErrorMessage<TRequest>(Result<TRequest, PagarMeFailureModel> result)
        {
            var message = CreateErrorMessage(result.Failure, result.StatusCode);

            if (string.IsNullOrEmpty(result.RawResponseBody))
                return message;

            return $"{message} - Response: {result.RawResponseBody}";
        }

        private static string CreateErrorMessage(PagarMeFailureModel failure, int statusCode)
        {
            var errorMessage = $"Status Code: {statusCode}.";

            if (!string.IsNullOrWhiteSpace(failure?.Message))
            {
                errorMessage += $" Message: {failure.Message}.";
            }

            var error = failure?.Errors != null
                ? failure?.Errors.FirstOrDefault(d => d.Value != null && d.Value.Any()).Value?.FirstOrDefault()
                : "Request failed";

            errorMessage += $" Error: {error}";
            return errorMessage;
        }

        private static bool CobrancaEstaCancelada(ChargeSummary charge) => string.Equals(charge.Status, "canceled", StringComparison.OrdinalIgnoreCase);
        #endregion

        private static string ObterTipoDaContaParaCadastro(TipoDeContaBancariaEnum tipo)
        {
            switch (tipo)
            {
                case TipoDeContaBancariaEnum.Corrente:
                    return "checking";
                case TipoDeContaBancariaEnum.Poupanca:
                    return "savings";
                default:
                    throw new NotImplementedException();
            }
        }

        private static int ConverterValorParaCentavos(string valor) => decimal.TryParse(valor, out var valorDecimal) ? (int)Math.Round(valorDecimal * 100) : 0;

        private CreateRecipientCommand CreateRecipientCommand(RecebedorParaCadastrarNoGatewayDTO recebedorParaCadastrar,
            ContaBancariaParaAssociarAoRecebedorNoGatewayDTO conta)
        {
            var recebedor = recebedorParaCadastrar as RecebedorParaCadastrarNaPagarmeDTO;

            var transferSettings = new TransferSettingsData(conta.CodigoBanco != "000" && _configuration.TransferenciaAutomaticaAtivadaPorPadrao,
                _configuration.IntervaloPadraoDaTransferenciaAutomatica, _configuration.DiaPadraoDaTransferenciaAutomatica);

            var automaticAnticipationSettings = new AutomaticAnticipationSettingsData()
            {
                Enabled = Convert.ToBoolean(_configuration.HabilitaAntecipacaoAutomaticaParaNovosCadastros),
                VolumePercentage = _configuration.PorcentagemDeVolumeDaAntecipacaoParaNovosCadastros
            };

            BankAccountData bankAccount = CreateBankAccountData(conta, recebedor);

            ManagingPartnersData managingPartner = CreateManagingPartnersData(recebedor);

            RegisterInformationData registerInformation = CreateRegisterInformationData(recebedor, managingPartner);

            var recipient = new CreateRecipientCommand(
                registerInformation,
                bankAccount,
                transferSettings,
                automaticAnticipationSettings);
            return recipient;
        }

        private RegisterInformationData CreateRegisterInformationData(RecebedorParaCadastrarNaPagarmeDTO recebedor,
            ManagingPartnersData managingPartner)
        {
            var registerInformation = new RegisterInformationData(
                new PagarMeEmail(recebedor.EmailEmpresa),
                new PagarMeCnpj(recebedor.CNPJ),
                recebedor.WebsiteEmpresa,
                new RecipientPhoneData(recebedor.TelefoneEmpresa.Substring(0, 2), recebedor.TelefoneEmpresa.Substring(2)),
                recebedor.Nome,
                recebedor.RazaoSocial,
                ConverterValorParaCentavos(recebedor.FaturamentoAnualEmpresa),
                new RecipientMainAddress(
                    recebedor.Endereco.Logradouro,
                    recebedor.Endereco.Numero,
                    recebedor.Endereco.Complemento,
                    recebedor.Endereco.Bairro,
                    recebedor.Endereco.PontoDeReferencia,
                    recebedor.Endereco.Cidade,
                    recebedor.Endereco.Estado,
                    recebedor.Endereco.CEP),
                managingPartner);
            return registerInformation;
        }

        private static ManagingPartnersData CreateManagingPartnersData(RecebedorParaCadastrarNaPagarmeDTO recebedor)
        {
            var managingPartner = new ManagingPartnersData(
                recebedor.Responsavel.Nome,
                new PagarMeEmail(recebedor.Responsavel.Email),
                new PagarMeCpf(recebedor.Responsavel.NumeroDocumento),
                recebedor.Responsavel.DataNascimento,
                recebedor.Responsavel.RendaMensalDeclaradaCentavos,
                recebedor.Responsavel.ProfissaoDoResponsavel,
                new RecipientMainAddress(
                    recebedor.Responsavel.Endereco.Logradouro,
                    recebedor.Responsavel.Endereco.Numero,
                    recebedor.Responsavel.Endereco.Complemento,
                    recebedor.Responsavel.Endereco.Bairro,
                    recebedor.Responsavel.Endereco.PontoDeReferencia,
                    recebedor.Responsavel.Endereco.Cidade,
                    recebedor.Responsavel.Endereco.Estado,
                    recebedor.Responsavel.Endereco.CEP),
                new RecipientPhoneData(recebedor.Responsavel.Telefone.Substring(0, 2), recebedor.Responsavel.Telefone.Substring(2)));
            return managingPartner;
        }

        private BankAccountData CreateBankAccountData(ContaBancariaParaAssociarAoRecebedorNoGatewayDTO conta,
            RecebedorParaCadastrarNaPagarmeDTO recebedor)
        {
            var bankAccount = new BankAccountData(
                conta.Titular,
                new PagarMeCnpj(recebedor.CNPJ),
                conta.CodigoBanco,
                conta.Agencia,
                conta.AgenciaDV,
                conta.NumeroConta,
                conta.NumeroContaDV,
                ObterTipoDaContaParaCadastro(conta.TipoDaConta));
            return bankAccount;
        }

        #region Antecipações
        // OBS.: a Pagar.Me V5 usa a V4 para antecipações. Por isso, a classe PagarMeAntecipacaoService é usada aqui.
        public async Task<Resultado<List<RecebivelDTO>>> ObterRecebiveis(DadosParaObterRecebiveisDTO dados)
            => await antecipacaoService.ObterRecebiveis(dados);

        public async Task<Resultado<List<AntecipacaoDTO>>> ObterAntecipacoesPorIdRecebedor(string idRecebedorNoGateway, string status)
            => await antecipacaoService.ObterAntecipacoesPorIdRecebedor(idRecebedorNoGateway, status);

        public async Task<Resultado<AntecipacaoCanceladaDTO>> CancelarAntecipacao(string idRecebedorNoGateway, string idAntecipacao)
            => await antecipacaoService.CancelarAntecipacao(idRecebedorNoGateway, idAntecipacao);

        public async Task<Resultado<AntecipacaoDTO>> RealizarAntecipacao(DadosParaCriarAntecipacaoDTO dados)
            => await antecipacaoService.RealizarAntecipacao(dados);

        public async Task<Resultado<LimitesDeAntecipacaoDTO>> ObterLimitesDeAntecipacao(DadosParaLimiteDeAntecipacaoDTO dados)
            => await antecipacaoService.ObterLimitesDeAntecipacao(dados);

        public async Task<Resultado<List<DadosUnidadeRecebiveisDTO>>> ObterUnidadeRecebiveis(FiltroPesquisaUnidadeRecebiveisDTO dados)
            => await antecipacaoService.ObterUnidadeRecebiveis(dados);

        public async Task<Resultado<decimal>> ObterSaldoDisponivelDoRecebedorNoGateway(string idDoRecebedorNoGateway)
            => await antecipacaoService.ObterSaldoDisponivelDoRecebedorNoGateway(idDoRecebedorNoGateway);

        public async Task<Resultado<RecebedorCadastradoNoGatewayDTO>> AtualizarRecebedorParaAntecipacoes(string idRecebedorNoGateway)
            => await antecipacaoService.AtualizarRecebedorParaAntecipacoes(idRecebedorNoGateway);
        #endregion

        #region Implementados somente pela Zoop
        public Task<Resultado<string>> CadastrarComprador(CompradorParaCadastrarDTO compradorParaCadastro)
            => throw new NotImplementedException();

        public Task<Resultado<CartaoCadastradoNoGatewayDTO>> CadastrarCartaoDeCredito(string idDoCompradorNoGateway, CartaoParaCadastrarDTO cartaoParaCadastro)
            => throw new NotImplementedException();

        public Task<Resultado<bool>> ExcluirCartaoDeCredito(string idDoCartaoNoGateway)
            => throw new NotImplementedException();

        public Task<Resultado<ArquivoCadastradoNoGatewayDTO>> CadastrarDocumentoParaRecebedor(DocumentoParaAssociarARecebedorDTO documento)
            => throw new NotImplementedException();
        #endregion

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (_disposed)
            {
                return;
            }

            if (disposing)
            {
                pagarMeV5Client.Dispose();
                _disposed = true;
            }
        }
    }
}
