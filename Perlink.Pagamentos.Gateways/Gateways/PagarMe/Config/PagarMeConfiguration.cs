﻿using Perlink.Pagamentos.Gateways.Config;
using Perlink.Pagamentos.Gateways.Enums;

namespace Perlink.Pagamentos.Gateways.Gateways.PagarMe.Config
{
    internal class PagarMeConfiguration : PagamentoOnlineGatewayConfiguration
    {
        public PagarMeConfiguration(IConfigurationProvider configurationProvider) : base(configurationProvider)
        {
        }

        public override string GetGatewayNameForConfigurations()
            => GatewayEnum.PagarMe.ToString();

        public string ApiKey => GetConfiguration<string>("api_key");
        public bool TransferenciaAutomaticaAtivadaPorPadrao => GetConfiguration<bool>("vendedores_transferencia_habilitada");
        public string IntervaloPadraoDaTransferenciaAutomatica => GetConfiguration<string>("vendedores_transferencia_intervalo");
        public int DiaPadraoDaTransferenciaAutomatica => GetConfiguration<int>("vendedores_transferencia_dia");
        public string PorcentagemDeVolumeDaAntecipacaoParaNovosCadastros => GetConfiguration<string>("porcentagem_volume_antecipacao_novo_cadastro");
        public string HabilitaAntecipacaoAutomaticaParaNovosCadastros => GetConfiguration<string>("habilitar_antecipacao_automatica_novo_cadastro");
        public string PorcentagemDeVolumeDaAntecipacao => GetConfiguration<string>("porcentagem_volume_antecipacao");
        public string HabilitaAntecipacaoAutomatica => GetConfiguration<string>("habilitar_antecipacao_automatica");
        public string VersionHeader => GetConfiguration<string>("v3_version_header");
    }
}
