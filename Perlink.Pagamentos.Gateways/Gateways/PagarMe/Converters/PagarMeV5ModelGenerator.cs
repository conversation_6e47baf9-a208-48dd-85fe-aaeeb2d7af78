﻿using Perlink.Pagamentos.Gateways.DTO;
using Perlink.Pagamentos.Gateways.Enums;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Trinks.Integracoes.PagarMe.V5.Abstractions.Models;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData.Address;
using Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData.PaymentMethods;
using Trinks.Integracoes.PagarMe.V5.Abstractions.ValueObjects;
using SplitRule = Trinks.Integracoes.PagarMe.V5.Abstractions.RequestData.SplitRule;

namespace Perlink.Pagamentos.Gateways.Gateways.PagarMe.Converters
{
    public static class PagarMeV5ModelGenerator
    {
        private const int defaultExpirationTime = 300;
        public static PagamentoRealizadoNoGatewayDTO ToPagamentoNoGateway(OrderModel orderResponse)
            => new PagamentoRealizadoNoGatewayDTO()
            {
                CustoTransacao = ToReais(orderResponse.Charges[0].LastTransaction.Amount),
                PrimeirosDigitosDoCartao = orderResponse.Charges[0].LastTransaction.Card.FirstSixDigits,
                UltimosDigitos = orderResponse.Charges[0].LastTransaction.Card.LastFourDigits,
                IdTransacaoNoGateway = orderResponse.Charges[0].GatewayId, // o gateway_id é o mesmo que o transaction_id que era utilizado na V4
                NomeDeExibicaoNaFatura = orderResponse.Charges[0].LastTransaction.StatementDescriptor,
                PagamentoFoiRealizado = orderResponse.Charges[0].LastTransaction.IsPaid,
                Status = orderResponse.Status,
                IdCobrancaNoGateway = orderResponse.Charges[0].Id,
                Comprador = new CompradorCadastradoNoGatewayDTO()
                {
                    IdNoGateway = orderResponse.Customer.Id,
                },
                Cartao = new CartaoCadastradoNoGatewayDTO()
                {
                    // Esse Fingerprint só é usado na Zoop
                    // Mas estamos preenchendo com o Id para não quebrar a interface
                    Fingerprint = orderResponse.Charges[0].LastTransaction.Card.Id,
                    IdDoCartaoNoGateway = orderResponse.Charges[0].LastTransaction.Card.Id,
                    Bandeira = orderResponse.Charges[0].LastTransaction.Card.Brand.ToBandeiraDoCartao(),
                    Valido = true,
                },

                // Campos não fornecidos pela Pagar.me
                DataEsperadaDeRecebimento = null,
            };

        #region Order customer and items initialization
      
        public static PagamentoPixCriadoNoGatewayDTO ToPagamentoPixCriadoNoGatewayDTO(OrderModel orderResponse)
            => new PagamentoPixCriadoNoGatewayDTO()
            {
                CustoTransacao = ToReais(orderResponse.Charges[0].LastTransaction.Amount),
                IdTransacaoNoGateway = orderResponse.Charges[0].GatewayId, // o gateway_id é o mesmo que o transaction_id que era utilizado na V4
                PagamentoFoiRealizado = orderResponse.Charges[0].LastTransaction.IsPaid,
                NomeDeExibicaoNaFatura = orderResponse.Charges[0].LastTransaction.StatementDescriptor,
                IdCobrancaNoGateway = orderResponse.Charges[0].Id,
                PixProviderTid = orderResponse.Charges[0].LastTransaction.PixProviderTid,
                QrCode = orderResponse.Charges[0].LastTransaction.QrCode,
                QrCodeUrl = orderResponse.Charges[0].LastTransaction.QrCodeUrl,
                DataDeExpiracao = orderResponse.Charges[0].LastTransaction.ExpiresAt,
                TipoDeTransacao = orderResponse.Charges[0].LastTransaction.TransactionType,
                Status = orderResponse.Charges[0].LastTransaction.Status,
                Sucesso = orderResponse.Charges[0].LastTransaction.Success,
                DataDeCriacao = orderResponse.Charges[0].LastTransaction.CreatedAt,
                DataDeAtualizacao = orderResponse.Charges[0].LastTransaction.UpdatedAt,

                DataEsperadaDeRecebimento = null,
            };

        public static CustomerData GenerateCustomerData(NovoPagamentoNoGatewayDTO np)
        {
            IPagarMeDocument document = GetDocument(np.Comprador.CpfCnpj);

            return new CustomerData(
                np.Comprador.Nome,
                document,
                new PagarMeEmail(np.Comprador.Email),
                new PagarMeMobile(np.Comprador.Telefone));
        }

        public static List<OrderItemData> GenerateOrderItems(NovoPagamentoNoGatewayDTO np)
            => np.Itens.ConvertAll(GenerateOrderItem);

        public static OrderItemData GenerateOrderItem(ItemPagamentoNoGatewayDTO item)
            => new OrderItemData(item.IdItemPagamento.ToString())
            {
                Amount = ToCentavos(item.ValorOriginal),
                Quantity = item.Quantidade,
                Description = item.NomeItem,
            };
        #endregion
        
        public static List<PaymentMethodData> PreparePaymentWithPix(NovoPagamentoNoGatewayDTO np, int expirationSecs)
            => new List<PaymentMethodData>() { 
                new PaymentMethodData(ToCentavos(np.Valor), GenerateNewPixPaymentData(expirationSecs), GenerateSplitRules(np)) 
            };
        
        public static List<PaymentMethodData> PreparePaymentWithNewCreditCard(NovoPagamentoNoGatewayDTO np)
            => new List<PaymentMethodData>() { 
                new PaymentMethodData(ToCentavos(np.Valor), GenerateNewCreditCardPaymentData(np), GenerateSplitRules(np)) 
            };

        public static CreditCardPaymentData GenerateNewCreditCardPaymentData(NovoPagamentoNoGatewayDTO np)
            => new CreditCardPaymentData()
            {
                Card = new CardBillingData(GenerateCreditCard(np), GenerateBillingAddress(np)),
                Installments = np.QuantidadeParcelas,
            };
        
        public static CardBillingData GenerateNewCreditCardData(NovoPagamentoNoGatewayDTO np)
            => new CardBillingData(GenerateCreditCard(np), GenerateBillingAddress(np));
        
        public static PixPaymentData GenerateNewPixPaymentData(int? expirationTime)
            => new PixPaymentData(expirationTime ?? defaultExpirationTime);
           
        public static AddressData GenerateBillingAddress(NovoPagamentoNoGatewayDTO np)
            => new AddressData()
            {
                Country = "BR",
                City = np.EnderecoDeCobranca.Cidade,
                ZipCode = np.EnderecoDeCobranca.CEP,
                MainLine = new ParseableAddressLine(np.EnderecoDeCobranca.Numero, np.EnderecoDeCobranca.Logradouro, np.EnderecoDeCobranca.Bairro),
                State = np.EnderecoDeCobranca.Estado,
                Complement = np.EnderecoDeCobranca.Complemento,
            };

        public static PagarMeCreditCard GenerateCreditCard(NovoPagamentoNoGatewayDTO np)
            => new PagarMeCreditCard(
                    number: np.Cartao.Numero,
                    holderName: np.Cartao.Nome,
                    expMonth: ParseValidade(np.Cartao.Validade).Month,
                    expYear: ParseValidade(np.Cartao.Validade).Year,
                    cvv: np.Cartao.CVV
                );

        private static int ToCentavos(this decimal value)
        {
            var parteInteiraEmCentavos = decimal.Truncate(value) * 100;
            var parteDecimal = (int)(value % 1 * 100);

            return Convert.ToInt32(parteInteiraEmCentavos + parteDecimal);
        }
        
        private static decimal ToReais(this int cents)
        {
            return cents / 100m;
        }


        private static (int Month, int Year) ParseValidade(string validade)
        {
            if (TryParseDateWithFormat(validade, "MM/yy", out var result))
                return (result.Month, EnsureYearWithinRange(result.Year));

            if (TryParseDateWithFormat(validade, "MM/yyyy", out result))
                return (result.Month, result.Year);

            throw new ArgumentException($"Não foi possível processar a validade {validade}. Formato desconhecido.");
        }

        private static bool TryParseDateWithFormat(string date, string format, out DateTime result)
            => DateTime.TryParseExact(date, format, DateInterpretationCulture.Value, DateTimeStyles.None, out result);

        private static Lazy<CultureInfo> DateInterpretationCulture = new Lazy<CultureInfo>(() =>
        {
            var culture = CultureInfo.InvariantCulture.Clone() as CultureInfo;

            // Força a interpretação de 04/22 como 04/2022 e 04/31 como 04/2031
            // Sem isso, mesmo usando InvariantCulture, há diferenças de interpretação entre versões do Windows
            culture.DateTimeFormat.Calendar.TwoDigitYearMax = DateTime.UtcNow.Year + 50;

            // Usando readonly para thread safety
            return CultureInfo.ReadOnly(culture);
        });

        private static int EnsureYearWithinRange(int year)
        {
            var yearDiff = DateTime.UtcNow.Year - year;

            if (Math.Abs(yearDiff) < 50)
            {
                return year;
            }

            return year + (yearDiff > 0 ? 100 : -100);
        }

        public static List<PaymentMethodData> PreparePaymentWithExistingCreditCard(NovoPagamentoNoGatewayDTO np)
            => new List<PaymentMethodData>() { 
                new PaymentMethodData(ToCentavos(np.Valor), GenerateExistingCardPaymentData(np), GenerateSplitRules(np)) 
            };

        public static CreditCardPaymentData GenerateExistingCardPaymentData(NovoPagamentoNoGatewayDTO np)
            => new CreditCardPaymentData()
            {
                CardId = np.IdCartaoNoGateway,
                Card = new CardBillingData(GenerateBillingAddress(np)),
                Installments = np.QuantidadeParcelas,
            };

        private static List<SplitRule> GenerateSplitRules(NovoPagamentoNoGatewayDTO np)
            => np.RegrasDeSplit.ConvertAll(
                rs => new SplitRule()
                {
                    Amount = ToCentavos(rs.Valor),
                    AmountType = SplitAmountTypes.Flat,
                    RecipientId = rs.IdRecebedorNoGateway,
                    Options = new SplitRecipientOptions()
                    {
                        ChargeProcessingFee = rs.ResponsavelPelasTaxasDaOperacao,
                        ChargeRemainderFee = rs.ResponsavelPeloRestoDaDivisaoDeTaxas,
                        Liable = rs.ResponsavelPeloChargeback,
                    },
                }
            );
        
        private static IPagarMeDocument GetDocument(string cpfCnpj)
        {
            string cleanDocument = new string(cpfCnpj.Where(char.IsDigit).ToArray());
                
            if (cleanDocument.Length <= 11)
            {
                return new PagarMeCpf(cpfCnpj);
            }
            
            return new PagarMeCnpj(cpfCnpj);
        }
    }
}
