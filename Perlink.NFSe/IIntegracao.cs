﻿using Perlink.NFSe.Models;
using System.Collections.Generic;

namespace Perlink.NFSe
{

    public interface IIntegracaoNFSe
    {

        EmpresaRetornoModel CadastrarEmpresa(EmpresaModel empresa, bool ambienteProducao);

        RetornoConsultaDocumentoModel EmitirNota(LoteRPSModel lote, bool ambienteProducao, string chaveAcesso);

        (RetornoConsultaDocumentoModel RetornoDocumentoModel, List<RPSModel> RetornoRpsNovaNumeracao) EmitirNotaRpsSequencial(LoteRPSModel lote, bool ambienteProducao, string chaveAcesso, int numeroUltimoRpsEmitido);

        RetornoConsultaDocumentoModel ConsultarNotas(string cnpj, int numeroInicial, int numeroFinal, string serie, bool ambienteProducao, string chaveAcesso, bool habilitarNumeracaoIsaneto = false);

        SituacaoDocumentoModel ConsultarEspelhoNota(string cnpj, int numero, string serie, bool ambienteProducao, string chaveAcesso, bool habilitarNumeracaoIsaneto = false);
    }
}