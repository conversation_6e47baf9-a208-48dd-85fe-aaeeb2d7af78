﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Perlink.NFSe.Models
{

    public class LoteRPSModel
    {

        public LoteRPSModel()
        {
            RPSs = new List<RPSModel>();
        }

        public List<RPSModel> RPSs { get; set; }
    }

    public class RPSModel
    {

        public RPSModel()
        {
            Prestador = new EmpresaModel();
            StatusRPS = StatusRPSEnum.Normal;
            DataEmissao = DateTime.Now;
            DataCompetencia = DateTime.Today;
        }

        public bool AmbienteProducao { get; set; }
        public DateTime DataCompetencia { get; set; }
        public DateTime DataEmissao { get; set; }
        public List<ItemRPSModel> Itens { get; set; }
        public int Numero { get; set; }
        public int IdEmissao { get; set; }
        public bool OptanteSimples { get; set; }
        public EmpresaModel Prestador { get; set; }
        public string RegimeEspecialTributacao { get; set; }
        public string Serie { get; set; }
        public StatusRPSEnum StatusRPS { get; set; }
        public int SubstituidoNumero { get; set; }
        public int SubstituidoSerie { get; set; }

        public decimal ValorTotal
        {
            get { return Itens.Sum(f => f.ValorTotal); }
        }

        public decimal ValorDeducao { get; set; }
        public decimal ValorBaseDeCalculo { get; set; }
        public decimal AliquotaPis { get; set; }
        public decimal AliquotaCofins { get; set; }

        public decimal AliquotaISS
        {
            get { return Itens.Average(f => f.AliquotaISS); }
        }

        public string CMC { get; set; }
        public ClienteModel Tomador { get; set; }
        public string NaturezaDaOperacao { get; set; }
        public string CodigoTributacaoMunicipio { get; set; }
        public string TextoInformandoValorDosImpostos { get; set; }
        public string TextoProcon { get; set; }
        public bool HabilitadoDeducaoItemNaoTributavel { get; set; }
        public bool EmiteTagDeducao { get; set; }
        public bool EmiteTagBaseDeCalculo { get; set; }
        public bool HabilitadoPadraoNacionalMei { get; set; }
        public bool HabilitadoPadraoNacionalEstabelecimento { get; set; }
        public bool HabilitarDeducaoDuasCasasDecimais { get; set; }
        public bool HabilitarBasedeCalculoDuasCasasDecimais { get; set; }
        public bool HabilitarItemvUnitDuasCasasDecimais { get; set; }
        public bool HabilitarEnvioTagItemVlrLiquido { get; set; }
        public bool HabilitaEnviarCnaeNull { get; set; }

        public int? MaxQtdcaracteresDiscriminacao { get; set; }
        public bool HabilitarNumeracaoIsaneto { get; set; }
        public bool NaoEnviarDadosTomadorSemCpf { get; set; }
        public bool HabilitarTagItemSeqComecarUm { get; set; }

        public bool HabilitarDeducaoPorServicoPadraoIPM { get; set; }

        public string NumeroFormatado { get => HabilitarNumeracaoIsaneto ? DateTime.Now.Year + $"{Numero:D11}" : Numero.ToString(); }

        public bool UtilizarCnpjPrestadorNoTomador { get; set; }
        public bool UtilizarEmailPrestadorNoTomador { get; set; }
        public bool ArredondarBaseDeCalculoAntesCalcularIss { get; set; }
        public bool HabilitarTruncarValissDuasCasasDecimais { get; set; }

        public class ItemRPSModel
        {

            public ItemRPSModel()
            {
                Quantidade = 1;
            }

            public string CNAE { get; set; }
            public string Codigo { get; set; }
            //public string CodigoTributacaoMunicipio { get; set; }
            public decimal Desconto { get; set; }
            public string Descricao { get; set; }
            public decimal AliquotaISS { get; set; }

            public decimal ValorISS
            {
                get
                {
                    return AliquotaISS / 100 * ValorTotal;
                }
            }

            public decimal Quantidade { get; set; }

            public decimal ValorTotal
            {
                get
                {
                    return ValorUnitario * Quantidade - Desconto;
                }
            }

            public decimal ValorUnitario { get; set; }
            public string ItemListaServico { get; set; }
            public decimal? ValorCota { get; set; }
        }

        public enum StatusRPSEnum
        {
            Normal = 1,
            Cancelada = 2
        }
    }

    public class LoteRPSRetornoModel
    {
        public string Codigo { get; set; }
        public string Mensagem { get; set; }
    }
}