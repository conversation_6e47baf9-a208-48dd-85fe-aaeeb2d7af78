﻿//using Perlink.NFSe.br.com.invoicy.homolog;
using Perlink.NFSe.br.com.invoicy.web;
using Perlink.NFSe.Invoicy;
using Perlink.NFSe.Models;
using Perlink.Shared.Auditing;
using Perlink.Shared.Serialization;
using Perlink.Shared.Streams;
using Perlink.Shared.Text;
using Perlink.Shared.Xml;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;

namespace Perlink.NFSe
{

    public class IntegracaoInvoicy : IIntegracaoNFSe
    {
        private const int TimeoutParaEnvioDeDados = 90;
        private const int TimeoutParaConsulta = 120;
        private const int TamanhoAgrupamentoXml = 10;

        #region Requisição Invoicy

        public Invoicyretorno EnviarDados(string[] listaDados, bool ambienteProducao, string chaveAcesso = null, string parametros = null, int timeoutSegundos = 60)
        {
            var dados = listaDados.Select(f => f
                .Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", "")
                .Replace(" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"", "")
                .Replace(" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"", "")
                ).ToArray();

            var chaveParceiro = ConfigurationManager.AppSettings["ChaveParceiroInvoicy"];

            if (string.IsNullOrWhiteSpace(chaveParceiro))
            {
                chaveParceiro = "eJvehYiIR3n1KfPFJoPnPg==";
            }

            chaveAcesso = chaveAcesso ?? chaveParceiro;

            InvoiCy dadosRequisicao = GerarInvoicy(dados, chaveParceiro, chaveAcesso, parametros);

            var recepcao = new recepcao
            {
                Timeout = timeoutSegundos * 1000,
                Url = ambienteProducao ? "https://nfse.invoicy.com.br/arecepcao.aspx" : "https://homolog.invoicy.com.br/arecepcao.aspx"
            };

            var invoicyretorno = new Invoicyretorno();

            try
            {
                invoicyretorno = recepcao.Execute(dadosRequisicao);
                return invoicyretorno;
            }
            catch (Exception e)
            {
                LogService<IntegracaoInvoicy>.Info(String.Format("XML de consulta [consulta]: {0} ", listaDados.FirstOrDefault()));
                LogService<IntegracaoInvoicy>.Info(String.Format("XML de consulta [parametros] {0}: ", parametros));
                LogService<IntegracaoInvoicy>.Info(String.Format("XML de consulta [Envio invoicy]: {0} ", Serializer.Serialize(dadosRequisicao)));
                LogService<IntegracaoInvoicy>.Info(String.Format("XML de consulta [retorno invoicy]: {0} ", Serializer.Serialize(invoicyretorno)));
                LogService<IntegracaoInvoicy>.Info("Erro: " + e.Message);
                throw new Exception("Erro " + e.Message + " ao enviar o seguinte XML ao Invoicy: " + Serializer.Serialize(dadosRequisicao));
            }
        }

        private static InvoiCyRecepcaoCabecalho GerarCabecalho(string[] listaDados, string chaveParceiro, string chaveAcesso)
        {
            return new InvoiCyRecepcaoCabecalho
            {
                EmpCK = GeraHashMD5(chaveAcesso + listaDados[0]),
                EmpPK = chaveParceiro
            };
        }

        private static InvoiCyRecepcaoDadosItem GerarDadosItem(string dados, string parametros = null)
        {
            return new InvoiCyRecepcaoDadosItem
            {
                Documento = dados,
                Parametros = parametros ?? ""
            };
        }

        private static InvoiCyRecepcaoDadosItem[] GerarDadosItens(string[] listaDados, string parametros = null)
        {
            var itens = new List<InvoiCyRecepcaoDadosItem>();

            itens.AddRange(listaDados.Select(f => GerarDadosItem(f, parametros)));

            return itens.ToArray();
        }

        private static InvoiCyRecepcaoInformacoes GerarInformacoes()
        {
            return new InvoiCyRecepcaoInformacoes
            {
                Texto = ""
            };
        }

        private static InvoiCy GerarInvoicy(string[] listaDados, string chaveParceiro, string chaveAcesso, string parametros = null)
        {
            return new InvoiCy
            {
                Cabecalho = GerarCabecalho(listaDados, chaveParceiro, chaveAcesso),
                Dados = GerarDadosItens(listaDados, parametros),
                Informacoes = GerarInformacoes()
            };
        }

        private static string GeraHashMD5(string texto)
        {
            using (System.Security.Cryptography.MD5 md5Hash = System.Security.Cryptography.MD5.Create())
            {
                byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(texto.Trim()));

                var sBuilder = new StringBuilder();

                for (int i = 0; i < data.Length; i++)
                {
                    sBuilder.Append(data[i].ToString("x2"));
                }
                return sBuilder.ToString();
            }
        }

        #endregion Requisição Invoicy

        #region EmissaoNota

        #region EmissaoSequencial
        public (RetornoConsultaDocumentoModel RetornoDocumentoModel, List<RPSModel> RetornoRpsNovaNumeracao) EmitirNotaRpsSequencial(LoteRPSModel lote, bool ambienteProducao, string chaveAcesso, int numeroUltimoRpsEmitido)
        {
            var documentosProcessados = new List<SituacaoDocumentoModel>();
            var documentosRejeitados = new List<SituacaoDocumentoModel>();
            var rpsComNovaNumeracao = new List<RPSModel>();
            var rpsRejeitados = new List<RPSModel>();

            var listaDeRps = lote.RPSs.OrderBy(x => x.Numero).ToList();
            var numeroAtualEmissao = numeroUltimoRpsEmitido + 1; // Inicia a numeração a partir do último RPS emitido

            for (int i = 0; i < listaDeRps.Count; i++)
            {
                var rps = listaDeRps[i];
                rps.Numero = numeroAtualEmissao;

                var dadoEnvioInvoicy = GerarEnvio(rps);
                var xml = Serializer.Serialize(dadoEnvioInvoicy).RemoveDiacritics();
                var invoicyretorno = EnviarDados(new string[] { xml }, ambienteProducao, chaveAcesso, timeoutSegundos: TimeoutParaEnvioDeDados);

                var invoicyRetornoModel = GerarRetornoConsultaDocumentoModel(invoicyretorno, lote.RPSs.FirstOrDefault()?.HabilitarNumeracaoIsaneto ?? false);

                if (invoicyRetornoModel.Documentos.Any())
                {
                    var retornoSituacaoRps = invoicyRetornoModel.Documentos.FirstOrDefault();
                    if (retornoSituacaoRps.Rejeitado)
                    {
                        if (retornoSituacaoRps.StatusEnum == StatusDocumentoEnum.Duplicado || retornoSituacaoRps.StatusEnum == StatusDocumentoEnum.EmConflito)
                            break; // Para o processamento se o RPS estiver duplicado ou em conflito

                        documentosRejeitados.Add(retornoSituacaoRps);
                        rpsRejeitados.Add(rps);
                    }
                    else
                    {
                        documentosProcessados.Add(retornoSituacaoRps); // RPS processado com sucesso
                        rpsComNovaNumeracao.Add(rps); // Adiciona à lista de numeração nova    
                        numeroAtualEmissao++; // Incrementa para o próximo número disponível
                    }
                }
                else
                {
                    rpsRejeitados.Add(rps);
                    documentosRejeitados.Add(new SituacaoDocumentoModel
                    {
                        CodigoStatus = (int)StatusDocumentoEnum.Rejeitada,
                        Numero = rps.Numero,
                        Serie = rps.Serie,
                        Status = "RPS não encontrado ou erro no processamento"
                    });
                }
            }

            // Processa RPS rejeitados com nova numeração
            if (rpsRejeitados.Any())
            {
                documentosRejeitados.ForEach(x => x.Numero = 0);
                rpsRejeitados.ForEach(x => x.Numero = 0);
                rpsComNovaNumeracao.AddRange(rpsRejeitados);
            }

            var retornoFinal = new RetornoConsultaDocumentoModel
            {
                CodigoStatus = documentosProcessados.Any() ? documentosProcessados.FirstOrDefault()?.CodigoStatus ?? 0 : documentosRejeitados.FirstOrDefault()?.CodigoStatus ?? 0,
                Status = documentosProcessados.Any() ? documentosProcessados.FirstOrDefault()?.Status : documentosRejeitados.FirstOrDefault()?.Status,
                Documentos = documentosProcessados.Concat(documentosRejeitados).ToList()
            };
            return (retornoFinal, rpsComNovaNumeracao);
        }
        #endregion

        public RetornoConsultaDocumentoModel EmitirNota(LoteRPSModel lote, bool ambienteProducao, string chaveAcesso)
        {
            var dados = lote.RPSs.Select(f => GerarEnvio(f));
            var xmls = dados.Select(f => Serializer.Serialize(f).RemoveDiacritics()).ToList();

            if (!ambienteProducao)
                xmls.ForEach(x => LogService<IntegracaoInvoicy>.Info($"XML de envio:\r\n{x}"));

            // Processa os xmls em grupos de 10
            var retornos = ProcessarListaXmlAgrupados(xmls, ambienteProducao, chaveAcesso);

            // Consolida resultados
            return ConsolidarRetornos(retornos, lote.RPSs.FirstOrDefault()?.HabilitarNumeracaoIsaneto ?? false);
        }

        private List<Invoicyretorno> ProcessarListaXmlAgrupados(List<string> xmls, bool ambienteProducao, string chaveAcesso)
        {
            var retornos = new List<Invoicyretorno>();

            // Processa em lotes usando LINQ para agrupar
            var agrupamentos = xmls.Select((xml, index) => new { Xml = xml, Index = index })
                            .GroupBy(x => x.Index / TamanhoAgrupamentoXml)
                            .Select(g => g.Select(x => x.Xml).ToList());

            foreach (var grupo in agrupamentos)
            {
                var retorno = EnviarDados(grupo.ToArray(), ambienteProducao, chaveAcesso, timeoutSegundos: TimeoutParaEnvioDeDados);
                retornos.Add(retorno);
            }

            return retornos;
        }

        private RetornoConsultaDocumentoModel ConsolidarRetornos(List<Invoicyretorno> retornos, bool habilitaNumeracaoIsaneto)
        {
            if (!retornos.Any())
                return new RetornoConsultaDocumentoModel();

            var documentosConsolidados = retornos
                .SelectMany(r => r.Mensagem[0].Documentos ?? Array.Empty<InvoiCyRetornoMensagemItemDocumentosItem>())
                .ToList();

            var primeiroRetorno = retornos.First();
            primeiroRetorno.Mensagem[0].Documentos = documentosConsolidados.ToArray();

            return GerarRetornoConsultaDocumentoModel(primeiroRetorno, habilitaNumeracaoIsaneto);
        }

        private static EnvioInvoicy GerarEnvio(RPSModel rps)
        {
            if (rps.StatusRPS == RPSModel.StatusRPSEnum.Normal)
                return new Envio
                {
                    ModeloDocumento = "NFSe",
                    Versao = 1,
                    RPS = GerarRPS(rps)
                };

            return new EnvioEvento
            {
                ModeloDocumento = "NFSe",
                Versao = 1,
                Evento = GerarEventoCancelamento(rps),
            };
        }

        private static Evento GerarEventoCancelamento(RPSModel rps)
        {
            return new Evento
            {
                CNPJ = rps.Prestador.CNPJ,
                EveTp = "110111", // Código de cancelamento
                RPSNumero = rps.Numero.ToString(),
                RPSSerie = rps.Serie,
                tpAmb = rps.AmbienteProducao ? "1" : "2"
            };
        }

        private static RPS GerarRPS(RPSModel rps)
        {
            var cidadesSemRegEspTrib = new string[] { "5103403", "5201108", "3543402" };
            var natOp = ObterNaturezaDaOperacaoPeloCodigoTributacao(rps.CodigoTributacaoMunicipio);

            if (rps.HabilitarDeducaoPorServicoPadraoIPM && rps.Itens.Any() && rps.Itens.LastOrDefault().ValorCota != null && rps.Itens.LastOrDefault().ValorCota > 0)
            {
                natOp = "21";
            }

            var codigoIbgeMunicipio = rps.Prestador.Endereco.CodigoIbgeMunicipio;

            var dataCompetencia = GetDataCompetencia(rps, codigoIbgeMunicipio);
            var dataEmissao = GetDataEmissao(rps, codigoIbgeMunicipio);

            var retorno = new RPS
            {
                RPSNumero = rps.NumeroFormatado,
                RPSSerie = rps.Serie,
                RPSTipo = "1",
                dEmis = dataEmissao,
                dCompetencia = dataCompetencia,
                dCompetenciaSpecified = true,
                LocalPrestServ = "1",
                natOp = rps.NaturezaDaOperacao == null ? natOp : rps.NaturezaDaOperacao,
                Operacao = rps.ValorDeducao > 0 ? "A" : "B",
                RegEspTrib = !cidadesSemRegEspTrib.Contains(codigoIbgeMunicipio) ? rps.RegimeEspecialTributacao : "0",
                OptSN = rps.OptanteSimples ? "1" : "2",
                IncCult = "2",
                Status = ((int)rps.StatusRPS).ToString(),
                tpAmb = rps.AmbienteProducao ? "1" : "2",
                Prestador = GerarPrestador(rps),
                ListaItens = codigoIbgeMunicipio != "3513009" ? (rps.HabilitadoDeducaoItemNaoTributavel && rps.ValorDeducao > 0 ? GerarListaItensSemTributacao(rps) : GerarListaItens(rps)) : null,
                Servico = GerarServico(rps),
                Tomador = GeraTomador(rps)
            };

            if (rps.HabilitadoPadraoNacionalEstabelecimento && (rps.RegimeEspecialTributacao == "5" || rps.RegimeEspecialTributacao == "6"))
            {
                retorno.regApTribSN = "1";
            }

            if (rps.StatusRPS == RPSModel.StatusRPSEnum.Cancelada)
                retorno.RPSSubs = new RPSSubs
                {
                    SubsNumero = rps.SubstituidoNumero.ToString(),
                    SubsSerie = rps.SubstituidoSerie.ToString(),
                    SubsTipo = "1"
                };

            return retorno;
        }

        private static DateTime GetDataEmissao(RPSModel rps, string codigoIbgeMunicipio)
        {
            if (codigoIbgeMunicipio == "1302603")
                return DateTime.Today;
            return rps.DataEmissao;
        }

        private static DateTime GetDataCompetencia(RPSModel rps, string codigoIbgeMunicipio)
        {
            if (codigoIbgeMunicipio == "3520509")
                return DateTime.Today;
            return rps.DataCompetencia;
        }

        private static string ObterNaturezaDaOperacaoPeloCodigoTributacao(string codigoTributacaoMunicipio)
        {
            var codigosTributacaoConversiveisParaNatOp = new[] { "T", "E", "C", "F", "K", "G", "H", "M", "3", "5", "6", "7", "1", "2", "4", "5.1", "5.2", "5.8", "5.9" };
            var codigosNatOp = new[] { "1", "2", "3", "4", "5", "10", "12", "17", "3", "4", "5", "6", "7", "8", "9", "5.1", "5.2", "5.8", "5.9" };
            var indexCod = Array.IndexOf(codigosTributacaoConversiveisParaNatOp, codigoTributacaoMunicipio);
            string natOp = "1";
            if (indexCod >= 0)
                natOp = codigosNatOp[indexCod];
            return natOp;
        }

        private static Tomador GeraTomador(RPSModel rps)
        {
            var endereco = rps.Tomador.Endereco;
            string codigoIbgeMunicipio = rps.Prestador.Endereco.CodigoIbgeMunicipio;

            if ((codigoIbgeMunicipio == "4309100" || codigoIbgeMunicipio == "1100049") && string.IsNullOrWhiteSpace(rps.Tomador.Cpf))
                rps.Tomador.Cpf = "12345678909";

            if (codigoIbgeMunicipio == "4321451" && string.IsNullOrWhiteSpace(rps.Tomador.Cpf))
                rps.Tomador.Cpf = "23454287000125";

            var tomador = new Tomador
            {
                TomaCPF = string.IsNullOrWhiteSpace(rps.Tomador.Cpf) && (codigoIbgeMunicipio == "3509502" || codigoIbgeMunicipio == "3205200") ? "77777777777" : rps.Tomador.Cpf.RemoveSpecialCharacters(),
                TomaEmail = rps.Tomador.Email,
                TomaRazaoSocial = rps.Tomador.Nome,
                TomaTelefone = rps.Tomador.Telefone
            };

            if (rps.UtilizarEmailPrestadorNoTomador && string.IsNullOrEmpty(tomador.TomaEmail))
            {
                tomador.TomaEmail = rps.Prestador.Email;
            }

            if (rps.UtilizarCnpjPrestadorNoTomador && string.IsNullOrEmpty(tomador.TomaCPF))
            {
                tomador.TomaCNPJ = rps.Prestador.CNPJ;
            }

            if (!string.IsNullOrEmpty(rps.Tomador.Cnpj))
            {
                tomador.TomaCPF = null;
                tomador.TomaCNPJ = rps.Tomador.Cnpj;
                tomador.TomaIM = rps.Tomador.InscricaoMunicipal;
            }

            if (codigoIbgeMunicipio == "5211909" && !string.IsNullOrWhiteSpace(tomador.TomaTelefone))
                tomador.TomaTipoTelefone = "CE";

            if (endereco != null)
            {
                tomador.TomatpLgr = endereco.TipoLogradouro;
                tomador.TomaBairro = endereco.Bairro;
                tomador.TomaCEP = endereco.CEP.RemoveSpecialCharacters();
                tomador.TomacMun = endereco.CodigoIbgeMunicipio;
                tomador.TomaComplemento = endereco.Complemento;
                tomador.TomaEndereco = endereco.Logradouro;
                tomador.TomaNumero = endereco.Numero;
                tomador.TomaUF = endereco.UF;
                tomador.TomaxMun = endereco.Cidade;
                tomador.TomaPais = "BR";
            }

            if ((rps.NaoEnviarDadosTomadorSemCpf || rps.HabilitadoPadraoNacionalMei || rps.HabilitadoPadraoNacionalEstabelecimento)
                && string.IsNullOrWhiteSpace(rps.Tomador.Cpf) && string.IsNullOrWhiteSpace(rps.Tomador.Cnpj))
                tomador = new Tomador();

            return tomador;
        }

        private static Servico GerarServico(RPSModel rps)
        {
            var primeiroItem = rps.Itens[0];
            var descricao = string.Join("\n", rps.Itens.Select(f => f.Descricao));
            if (descricao.Length > 1300)
            {
                descricao = descricao.Substring(0, 1300) + "...";
            }

            string codigoIbgeMunicipio = rps.Prestador.Endereco.CodigoIbgeMunicipio;

            if (codigoIbgeMunicipio == "4205407" && descricao.Length > 400)
            {
                descricao = descricao.Substring(0, 400) + "...";
            }

            var discriminacaoServicos = descricao + (string.IsNullOrEmpty(rps.TextoInformandoValorDosImpostos) ? "" : "\n\n" + rps.TextoInformandoValorDosImpostos) + ". " + rps.TextoProcon;

            if (rps.MaxQtdcaracteresDiscriminacao != null && discriminacaoServicos.Length > rps.MaxQtdcaracteresDiscriminacao.Value)
            {
                var caracteresSobrando = rps.MaxQtdcaracteresDiscriminacao.Value - discriminacaoServicos.Length;
                var qtdCaracteresPermitidosParaEssaDescricao = descricao.Length - Math.Abs(caracteresSobrando - 4);

                if (caracteresSobrando < 0 && descricao.Length > qtdCaracteresPermitidosParaEssaDescricao)
                    descricao = descricao.Substring(0, qtdCaracteresPermitidosParaEssaDescricao) + "...";
            }

            var cnae = primeiroItem.CNAE ?? primeiroItem.ItemListaServico;

            if (rps.HabilitaEnviarCnaeNull)
            {
                cnae = null;
            }

            var servico = new Servico
            {
                Valores = GerarValores(rps),
                IteListServico = primeiroItem.ItemListaServico,
                Cnae = cnae,
                TributMunicipio = rps.CodigoTributacaoMunicipio,
                Discriminacao = descricao + (string.IsNullOrEmpty(rps.TextoInformandoValorDosImpostos) ? "" : "\n" + rps.TextoInformandoValorDosImpostos) + ". " + rps.TextoProcon,
                cMun = codigoIbgeMunicipio,
                cMunIncidencia = rps.AmbienteProducao || codigoIbgeMunicipio != "3303302" ? codigoIbgeMunicipio : "9999999"
            };

            if (rps.HabilitadoPadraoNacionalMei || rps.HabilitadoPadraoNacionalEstabelecimento)
            {
                if (servico.LocalPrestacao == null)
                {
                    servico.LocalPrestacao = new LocalPrestacao
                    {
                        SerEndcMun = codigoIbgeMunicipio
                    };
                }
                else
                {
                    servico.LocalPrestacao.SerEndcMun = codigoIbgeMunicipio;
                }
            }

            if (rps.OptanteSimples)
                servico.Discriminacao += "\n\n DOCUMENTO EMITIDO POR ME OU EPP OPTANTE PELO SIMPLES NACIONAL, CFE LC 123/2006";

            return servico;
        }

        private static Valores GerarValores(RPSModel rps)
        {
            string codigoIbgeMunicipio = rps.Prestador.Endereco.CodigoIbgeMunicipio;
            var basedecalculo = rps.ValorBaseDeCalculo == 0 ? (codigoIbgeMunicipio == "4205407" ? 0 : rps.ValorTotal) : rps.ValorBaseDeCalculo;
            var basedecalculoDuasCasasDecimais = Math.Round(basedecalculo, 2, MidpointRounding.AwayFromZero);

            var valIss = (rps.ArredondarBaseDeCalculoAntesCalcularIss ? basedecalculoDuasCasasDecimais : basedecalculo) * rps.AliquotaISS / 100;

            if (rps.HabilitarTruncarValissDuasCasasDecimais)
            {
                valIss = Math.Truncate(valIss * 100) / 100;
            }
            else
            {
                valIss = Math.Round(valIss, 2, MidpointRounding.AwayFromZero);
            }

            var valores = new Valores
            {
                ValServicos = rps.ValorTotal,
                ValAliqISS = rps.AliquotaISS / 100,
                ValLiquido = rps.ValorTotal,
                ISSRetido = "2",
                Tributavel = "S",
                ValDescCond = 0,
                ValISS = valIss,
               ValDeducoes = rps.HabilitarDeducaoDuasCasasDecimais ? Math.Round(rps.ValorDeducao, 2, MidpointRounding.AwayFromZero) : rps.ValorDeducao,
                ValDeducoesSpecified = rps.ValorDeducao > 0 ? true : false
            };

            if (rps.AliquotaPis > 0)
            {
                valores.ValPIS = Math.Round(rps.ValorBaseDeCalculo * rps.AliquotaPis / 100, 2, MidpointRounding.AwayFromZero);
                valores.ValPISSpecified = true;
            }

            if (rps.AliquotaCofins > 0)
            {
                valores.ValCOFINS = Math.Round(rps.ValorBaseDeCalculo * rps.AliquotaCofins / 100, 2, MidpointRounding.AwayFromZero);
                valores.ValCOFINSSpecified = true;
            }

            if (codigoIbgeMunicipio != "3144805" || valores.ValDeducoes > 0)
                valores.ValBaseCalculo = rps.HabilitarBasedeCalculoDuasCasasDecimais ? basedecalculoDuasCasasDecimais : basedecalculo;

            if (!rps.EmiteTagDeducao)
            {
                valores.ValDeducoes = 0;
                valores.ValDeducoesSpecified = false;
            }

            var cidadesRespRetencao = new string[] { "4211900", "5213806" };

            if (cidadesRespRetencao.Contains(codigoIbgeMunicipio))
            {
                valores.RespRetencao = "3";
                if (codigoIbgeMunicipio == "5213806")
                    valores.RespRetencao = "2";
            }

            if (rps.HabilitadoPadraoNacionalEstabelecimento)
            {
                valores.ValAliqRetFederais = 0;
                valores.ValAliqRetEstaduais = 0;
                valores.ValAliqRetMunicipais = 0;
            }

            return valores;
        }

        private static ListaDedDed GerarDeducao(RPSModel rps)
        {
            var listaDedDed = new ListaDedDed
            {
                DedValPer = "1",
                DedTipo = "Despesas com Materiais",
                DedCNPJRef = "12982258765",
                DedvlTotRef = 10,
                DedValor = rps.ValorDeducao
            };

            return listaDedDed;
        }

        private static ListaItensItem[] GerarListaItens(RPSModel rps)
        {
            string codigoIbgeMunicipio = rps.Prestador.Endereco.CodigoIbgeMunicipio;
            var lista = new List<ListaItensItem>();
            var cidadesRespRetencao = new string[] { "4211900", "5213806" };

            var index = 0;
            if (rps.HabilitarTagItemSeqComecarUm)
                index = 1;

            lista.AddRange(rps.Itens.Select(f => new ListaItensItem
            {
                ItemSeq = (index++).ToString(),
                ItemCod = f.Codigo,
                ItemDesc = f.Descricao,
                ItemQtde = f.Quantidade,
                ItemuMed = "UN",
                ItemvUnit = rps.HabilitarItemvUnitDuasCasasDecimais ? Math.Round(f.ValorUnitario, 2) : f.ValorUnitario,
                ItemTributavel = "S",
                ItemTributMunicipio = codigoIbgeMunicipio == "4205407" ? (rps.Itens[0].CNAE == "9602502" ? "9251" : "9248") : rps.CodigoTributacaoMunicipio,
                ItemcCnae = (codigoIbgeMunicipio == "5107925" || codigoIbgeMunicipio == "4205407" || codigoIbgeMunicipio == "3541000" || codigoIbgeMunicipio == "4106902" || codigoIbgeMunicipio == "4119905" || codigoIbgeMunicipio == "3154606") ? rps.Itens[0].CNAE : null,
                ItemvIss = Math.Round(f.ValorISS, 2),
                ItemvDesconto = f.Desconto,
                ItemvDescontoSpecified = f.Desconto > 0,
                ItemAliquota = f.AliquotaISS,
                ItemBaseCalculo = codigoIbgeMunicipio == "4205407" ? 0 : f.ValorTotal,
                ItemIssRetido = "2",
                ItemIteListServico = f.ItemListaServico,
                ItemExigibilidadeISS = codigoIbgeMunicipio == "4205407" ? "12" : "1",
                ItemVlrLiquido = f.ValorUnitario,
                ItemVlrLiquidoSpecified = rps.HabilitarEnvioTagItemVlrLiquido,
                ItemVlrTotal = f.ValorUnitario,
                ItemVlrTotalSpecified = codigoIbgeMunicipio == "4119905",
                ItemRespRetencao = cidadesRespRetencao.Contains(codigoIbgeMunicipio) ? (codigoIbgeMunicipio == "5213806" ? "2" : "3") : null,
                ItemcMunIncidencia = codigoIbgeMunicipio == "4305108" ? codigoIbgeMunicipio : null,
                ItemvlDed = rps.HabilitarDeducaoPorServicoPadraoIPM ? (f.ValorCota != null && f.ValorCota > 0 ? f.ValorCota.Value : 0) : 0,
                ItemvlDedSpecified = rps.HabilitarDeducaoPorServicoPadraoIPM && (f.ValorCota != null && f.ValorCota > 0)
            }));

            return lista.ToArray();
        }

        private static ListaItensItem[] GerarListaItensSemTributacao(RPSModel rps)
        {
            string codigoIbgeMunicipio = rps.Prestador.Endereco.CodigoIbgeMunicipio;
            var lista = new List<ListaItensItem>();
            var cidadesRespRetencao = new string[] { "4211900", "5213806" };

            var index = 0;
            if (rps.HabilitarTagItemSeqComecarUm)
                index = 1;

            var f = rps.Itens[0];

            var nomeServicos = "";
            foreach (var item in rps.Itens)
                nomeServicos += item.Descricao.Split('-')[0] + "/ ";

            nomeServicos = nomeServicos.Substring(0, nomeServicos.Length - 2);

            lista.Add(new ListaItensItem
            {
                ItemSeq = (index++).ToString(),
                ItemCod = f.Codigo,
                ItemDesc = nomeServicos,
                ItemQtde = f.Quantidade,
                ItemuMed = "UN",
                ItemvUnit = rps.HabilitarItemvUnitDuasCasasDecimais ? Decimal.Truncate(rps.ValorBaseDeCalculo * 100) / 100 : rps.ValorBaseDeCalculo,
                ItemTributavel = "S",
                ItemTributMunicipio = codigoIbgeMunicipio == "4205407" ? (rps.Itens[0].CNAE == "9602502" ? "9251" : "9248") : rps.CodigoTributacaoMunicipio,
                ItemcCnae = (codigoIbgeMunicipio == "5107925" || codigoIbgeMunicipio == "4205407" || codigoIbgeMunicipio == "3541000" || codigoIbgeMunicipio == "4106902" || codigoIbgeMunicipio == "4119905" || codigoIbgeMunicipio == "3154606") ? rps.Itens[0].CNAE : null,
                ItemvDesconto = f.Desconto,
                ItemvDescontoSpecified = f.Desconto > 0,
                ItemAliquota = f.AliquotaISS,
                ItemBaseCalculo = codigoIbgeMunicipio == "4205407" ? 0 : rps.ValorBaseDeCalculo,
                ItemIssRetido = "2",
                ItemIteListServico = f.ItemListaServico,
                ItemExigibilidadeISS = codigoIbgeMunicipio == "4205407" ? "12" : "1",
                ItemVlrLiquido = rps.ValorBaseDeCalculo,
                ItemVlrLiquidoSpecified = rps.HabilitarEnvioTagItemVlrLiquido,
                ItemVlrTotal = rps.ValorBaseDeCalculo,
                ItemVlrTotalSpecified = codigoIbgeMunicipio == "4119905",
                ItemRespRetencao = cidadesRespRetencao.Contains(codigoIbgeMunicipio) ? (codigoIbgeMunicipio == "5213806" ? "2" : "3") : null,
                ItemcMunIncidencia = codigoIbgeMunicipio == "4305108" ? codigoIbgeMunicipio : null
            });

            lista.Add(new ListaItensItem
            {
                ItemSeq = (index++).ToString(),
                ItemCod = f.Codigo,
                ItemDesc = nomeServicos,
                ItemQtde = f.Quantidade,
                ItemuMed = "UN",
                ItemvUnit = rps.HabilitarItemvUnitDuasCasasDecimais ? Math.Round(rps.ValorDeducao, 2, MidpointRounding.AwayFromZero) : rps.ValorDeducao,
                ItemTributavel = "N",
                ItemTributMunicipio = codigoIbgeMunicipio == "4205407" ? (rps.Itens[0].CNAE == "9602502" ? "9251" : "9248") : rps.CodigoTributacaoMunicipio,
                ItemcCnae = (codigoIbgeMunicipio == "5107925" || codigoIbgeMunicipio == "4205407" || codigoIbgeMunicipio == "3541000" || codigoIbgeMunicipio == "4106902" || codigoIbgeMunicipio == "4119905" || codigoIbgeMunicipio == "3154606") ? rps.Itens[0].CNAE : null,
                ItemvDesconto = f.Desconto,
                ItemvDescontoSpecified = f.Desconto > 0,
                ItemAliquota = f.AliquotaISS,
                ItemBaseCalculo = codigoIbgeMunicipio == "4205407" ? 0 : rps.ValorDeducao,
                ItemIssRetido = "2",
                ItemIteListServico = f.ItemListaServico,
                ItemExigibilidadeISS = codigoIbgeMunicipio == "4205407" ? "12" : "1",
                ItemVlrLiquido = rps.ValorDeducao,
                ItemVlrLiquidoSpecified = rps.HabilitarEnvioTagItemVlrLiquido,
                ItemVlrTotal = rps.ValorDeducao,
                ItemVlrTotalSpecified = codigoIbgeMunicipio == "4119905",
                ItemRespRetencao = cidadesRespRetencao.Contains(codigoIbgeMunicipio) ? (codigoIbgeMunicipio == "5213806" ? "2" : "3") : null,
                ItemcMunIncidencia = codigoIbgeMunicipio == "4305108" ? codigoIbgeMunicipio : null
            });

            return lista.ToArray();
        }

        private static Prestador GerarPrestador(RPSModel rps)
        {
            var prest = new Prestador
            {
                CNPJ_prest = rps.Prestador.CNPJ,
                xNome = rps.Prestador.RazaoSocial,
                xFant = rps.Prestador.NomeFantasia,
                IE = rps.Prestador.InscricaoEstadual,
                IM = rps.Prestador.InscricaoMunicipal,
                enderPrest = GerarEndereco(rps.Prestador),
                CMC = rps.CMC
            };

            return prest;
        }

        private static enderPrest GerarEndereco(EmpresaModel emp)
        {
            var end = emp.Endereco;
            return new enderPrest
            {
                CEP = end.CEP,
                cMun = end.CodigoIbgeMunicipio,
                Email = emp.Email,
                fone = emp.TelefoneDDD + emp.Telefone,
                TPEnd = end.TipoLogradouro,
                xLgr = end.Logradouro,
                nro = end.Numero,
                xCpl = end.Complemento,
                xBairro = end.Bairro,
                UF = end.UF
            };
        }

        #endregion EmissaoNota

        #region Cadastrar empresa

        public EmpresaRetornoModel CadastrarEmpresa(EmpresaModel empresa, bool ambienteProducao)
        {
            var dados = GerarCadastroEmpresa(empresa);

            var xml = Serializer.Serialize(dados).RemoveDiacritics();

            var retornoInvoicy = EnviarDados(new[] { xml }, ambienteProducao, timeoutSegundos: TimeoutParaEnvioDeDados);

            string idExterno = null;
            string msg = null;

            var mensagem = retornoInvoicy.Mensagem.FirstOrDefault();
            if (mensagem != null && mensagem.Codigo == 100)
            {
                msg = mensagem.Descricao;
                var documento = mensagem.Documentos.FirstOrDefault();
                if (documento != null)
                {
                    var retornoDetalhe = new DetalhesXML(documento.Documento);
                    var elemento = retornoDetalhe.ObterRecursivoPorLabel("EmpAK").FirstOrDefault();
                    if (elemento != null)
                        idExterno = elemento.Valor;
                }
            }

            return new EmpresaRetornoModel
            {
                IdExterno = idExterno,
                Mensagem = msg
            };
        }

        private static CadastroEmpresa GerarCadastroEmpresa(EmpresaModel empresa)
        {
            var end = empresa.Endereco;
            return new CadastroEmpresa
            {
                EmpRazSocial = empresa.RazaoSocial,
                EmpApelido = empresa.NomeFantasia,
                EmpNomFantasia = empresa.NomeFantasia,
                EmpCNPJ = empresa.CNPJ,
                EmpIE = empresa.InscricaoEstadual,
                EmpTelefone = empresa.TelefoneDDD + empresa.Telefone,
                EmpTpoEndereco = end.TipoLogradouro,
                EmpEndereco = end.Logradouro,
                EmpBairro = end.Bairro,
                EmpCEP = end.CEP,
                EmpComplemento = end.Complemento,
                EmpIM = empresa.InscricaoMunicipal,
                MunCodigo = end.CodigoIbgeMunicipio,
                EmpCNAE = empresa.CNAE,
                EmpCRT = ((int)empresa.CRT).ToString(),
                Certificado = GeraCertificado(empresa),
                Parametros = GerarParametros(empresa),
                EmpNumero = end.Numero,
                EmpEmail = empresa.Email
            };
        }

        private static Certificado GeraCertificado(EmpresaModel empresa)
        {
            if (empresa.Autenticacao == null || empresa.Autenticacao.ArquivoCertificado == null)
                return null;

            var bytes = empresa.Autenticacao.ArquivoCertificado.ReadToEnd();

            return new Certificado
            {
                ArquivoPFX = Convert.ToBase64String(bytes),
                Senha = empresa.Autenticacao.SenhaCertificado
            };
        }

        private static Parametros GerarParametros(EmpresaModel empresa)
        {
            return new Parametros
            {
                NFSe = GerarParametrosNFSe(empresa)
            };
        }

        private static Invoicy.NFSe GerarParametrosNFSe(EmpresaModel empresa)
        {
            string senha = null;
            var autenticacao = empresa.Autenticacao;

            if (autenticacao != null)
            {
                if (!string.IsNullOrWhiteSpace(autenticacao.Senha))
                    senha = autenticacao.Senha;
                if (!string.IsNullOrWhiteSpace(autenticacao.SenhaCertificado))
                    senha = autenticacao.SenhaCertificado;
            }

            var retorno = new Invoicy.NFSe
            {
                AmbienteEmissao = empresa.AmbienteProducao ? "1" : "2",
                UsuarioAutent = autenticacao != null ? autenticacao.Login : null,
                SenhaAutent = senha,
                EnviarNFSeTomador = "E",
                TipoProcessamento = "A",
                ImprimeNFSeEfetivada = "N",
                MaxRPSLote = "1",
                EmpEspelhoNovo = "S",
                CodCMC = "0",
                EmpImprimeCanhoto = "4",
                EmpConcatOutrasInfo = "N",
                EmpRetImpPrefeitura = "S",
                EmpEnviaCPFPrestador = "N",
                FormaRetornoPDFIntegracao = "3",
                FormaRetornoXMLIntegracao = "3",
                EmpEnviaEmailCompactado = "S",
                MEIAmbienteNacional = empresa.HabilitadoPadraoNacionalMei ? "S" : "N",
                ReenvioAutomatico = "N",
                CorrigirDadosRPS = "N"
            };
            return retorno;
        }

        #endregion Cadastrar empresa

        #region Consulta Nota

        public RetornoConsultaDocumentoModel ConsultarNotas(string cnpj, int numeroInicial, int numeroFinal, string serie, bool ambienteProducao, string chaveAcesso, bool habilitarNumeracaoIsaneto = false)
        {
            LogService<IntegracaoInvoicy>.Info($"ConsultarNotas CNPJ {cnpj} - De {numeroInicial} até {numeroFinal}");

            var quantidadePorConsulta = 5;

            RetornoConsultaDocumentoModel retorno = new RetornoConsultaDocumentoModel();

            for (int inicio = numeroInicial; inicio <= numeroFinal; inicio += quantidadePorConsulta)
            {
                var fim = inicio + quantidadePorConsulta - 1;
                if (fim > numeroFinal)
                    fim = numeroFinal;

                var consulta = new Consulta
                {
                    ModeloDocumento = "NFSe",
                    Versao = 1,
                    tpAmb = ambienteProducao ? "1" : "2",
                    CnpjEmissor = cnpj,
                    NumeroInicial = FormatarNumero(inicio, habilitarNumeracaoIsaneto),
                    NumeroFinal = FormatarNumero(fim, habilitarNumeracaoIsaneto),
                    Serie = serie
                };
                var parametros = new ParametrosConsulta
                {
                    Situacao = "N",
                    XMLCompleto = "S",
                    PDFLink = "S"
                };

                var xmlConsulta = Serializer.Serialize(consulta).RemoveDiacritics();
                var xmlParametros = Serializer.Serialize(parametros).RemoveDiacritics();

                var invoicyretorno = EnviarDados(new[] { xmlConsulta }, ambienteProducao, chaveAcesso, xmlParametros, timeoutSegundos: TimeoutParaConsulta);
                var invoicyRetornoModel = GerarRetornoConsultaDocumentoModel(invoicyretorno, habilitarNumeracaoIsaneto);

                if (retorno.CodigoStatus == 0 || invoicyRetornoModel.CodigoStatus == 100)
                {
                    retorno.CodigoStatus = invoicyRetornoModel.CodigoStatus;
                    retorno.Status = invoicyRetornoModel.Status;
                }

                retorno.Documentos.AddRange(invoicyRetornoModel.Documentos);
            }

            return retorno;
        }

        private string FormatarNumero(int numeroRPS, bool habilitarNumeracaoIsaneto = false)
        {
            return habilitarNumeracaoIsaneto ? DateTime.Now.Year + $"{numeroRPS:D11}" : numeroRPS.ToString();
        }

        public SituacaoDocumentoModel ConsultarEspelhoNota(string cnpj, int numero, string serie, bool ambienteProducao, string chaveAcesso, bool habilitarNumeracaoIsaneto = false)
        {
            var consulta = new Consulta
            {
                ModeloDocumento = "NFSe",
                Versao = 1,
                tpAmb = ambienteProducao ? "1" : "2",
                CnpjEmissor = cnpj,
                NumeroInicial = FormatarNumero(numero, habilitarNumeracaoIsaneto),
                NumeroFinal = FormatarNumero(numero, habilitarNumeracaoIsaneto),
                Serie = serie
            };
            var parametros = new ParametrosConsulta
            {
                Situacao = "S",
                XMLCompleto = "S",
                PDFLink = "S",
                PDFBase64 = "S",
                XMLLink = "S"
            };

            var xmlConsulta = Serializer.Serialize(consulta).RemoveDiacritics();
            var xmlParametros = Serializer.Serialize(parametros).RemoveDiacritics();

            var invoicyretorno = EnviarDados(new[] { xmlConsulta }, ambienteProducao, chaveAcesso, xmlParametros, timeoutSegundos: TimeoutParaConsulta);
            var invoicyRetornoModel = GerarRetornoConsultaDocumentoModel(invoicyretorno, habilitarNumeracaoIsaneto);

            if (invoicyRetornoModel.Documentos.Any())
            {
                var retornoConsulta = new SituacaoDocumentoModel()
                {
                    LinkPDF = invoicyRetornoModel.Documentos.FirstOrDefault().LinkPDF,
                    LinkDocPrefeitura = invoicyRetornoModel.Documentos.FirstOrDefault().LinkDocPrefeitura,
                    ChaveAcesso = invoicyRetornoModel.Documentos.FirstOrDefault().ChaveAcesso
                };

                return retornoConsulta;
            }
            else
            {
                return null;
            }
        }

        private static RetornoConsultaDocumentoModel GerarRetornoConsultaDocumentoModel(Invoicyretorno invoicyretorno, bool habilitaNumeracaoIsaneto = false)
        {
            var mensagem = invoicyretorno.Mensagem.FirstOrDefault()
                ?? new InvoiCyRetornoMensagemItem();
            var documentos = mensagem != null ? mensagem.Documentos : null;

            documentos = documentos ?? new InvoiCyRetornoMensagemItemDocumentosItem[0];

            return new RetornoConsultaDocumentoModel
            {
                Documentos = documentos.Select(f => GerarSituacaoDocumento(f, habilitaNumeracaoIsaneto)).ToList(),
                CodigoStatus = mensagem.Codigo,
                Status = mensagem.Descricao
            };
        }

        private static SituacaoDocumentoModel GerarSituacaoDocumento(InvoiCyRetornoMensagemItemDocumentosItem f, bool habilitaNumeracaoIsaneto = false)
        {
            var detalhesDocumento = new DetalhesXML(f.Documento);
            var numero = detalhesDocumento.ObterRecursivoPorLabel("DocNumero").FirstOrDefault();
            var serie = detalhesDocumento.ObterRecursivoPorLabel("DocSerie").FirstOrDefault();
            var codigoSituacao = detalhesDocumento.ObterRecursivoPorLabel("DocSitCodigo").FirstOrDefault()
                ?? detalhesDocumento.ObterRecursivoPorLabel("SitCodigo").FirstOrDefault();
            var situacao = detalhesDocumento.ObterRecursivoPorLabel("DocSitDescricao").FirstOrDefault()
                ?? detalhesDocumento.ObterRecursivoPorLabel("SitDescricao").FirstOrDefault();
            var linkPDF = detalhesDocumento.ObterRecursivoPorLabel("DocPDFDownload").FirstOrDefault() != null ? detalhesDocumento.ObterRecursivoPorLabel("DocPDFDownload").FirstOrDefault() : detalhesDocumento.ObterRecursivoPorLabel("DocPDFLink").FirstOrDefault();
            var numeroNfse = detalhesDocumento.ObterRecursivoPorLabel("NFSeNumero").FirstOrDefault();
            var linkDocPrefeitura = detalhesDocumento.ObterRecursivoPorLabel("DocImpPrefeitura").FirstOrDefault();

            string xml = null;

            var xmlElemento = detalhesDocumento.ObterRecursivoPorLabel("DocXML").FirstOrDefault();
            if (xmlElemento != null)
            {
                var xmlBase = xmlElemento.Valor;
                xml = Encoding.UTF8.GetString(Convert.FromBase64String(xmlBase));
            }

            if (string.IsNullOrWhiteSpace(xml))
                xml = f.Documento;

            var chaveAcesso = "";
            if (!string.IsNullOrWhiteSpace(xml) && EhXmlValido(xml))
            {
                var xmlDetalhes = new DetalhesXML(xml);
                var chaveAcessoXml = xmlDetalhes.ObterRecursivoPorLabel("OutrasInformacoes").FirstOrDefault();
                if (!string.IsNullOrWhiteSpace(chaveAcessoXml?.Valor) && chaveAcessoXml.Valor.Contains("Chave de acesso"))
                {
                    chaveAcesso = Regex.Replace(chaveAcessoXml?.Valor, @"\D", "");
                }
                else
                {
                    chaveAcessoXml = xmlDetalhes.ObterRecursivoPorLabel("chaveAcesso").FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(chaveAcessoXml?.Valor))
                        chaveAcesso = chaveAcessoXml?.Valor;
                }
            }

            return new SituacaoDocumentoModel
            {
                CodigoStatus = codigoSituacao != null ? int.Parse(codigoSituacao.Valor) : 0,
                Numero = numero != null ? ConverterNumeracao(numero.Valor, habilitaNumeracaoIsaneto) : 0,
                Serie = serie?.Valor,
                Status = FormatarMensagemErro(situacao),
                LinkPDF = linkPDF?.Valor,
                LinkDocPrefeitura = linkDocPrefeitura?.Valor,
                NumeroNfse = numeroNfse?.Valor,
                Xml = xml,
                ChaveAcesso = chaveAcesso
            };
        }

        private static bool EhXmlValido(string xml)
        {
            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(xml);
                return true;
            }
            catch (XmlException)
            {
                return false;
            }
        }

        private static int ConverterNumeracao(string numeroRPS, bool habilitaNumeracaoIsaneto = false)
        {
            return int.Parse(habilitaNumeracaoIsaneto ? numeroRPS.Substring(4) : numeroRPS);
        }

        private static string FormatarMensagemErro(DetalhesXML mensagem)
        {
            string status;
            try
            {
                status = mensagem.Valor.ToString().Split('\n')[0].Split('|')[0].Split('|')[0].Replace('|', '\n');
                var statusArr = status.Split(new string[] { "error:", "Erro:" }, StringSplitOptions.None);

                if (statusArr.Count() > 1)
                {
                    status = "Erro:" + statusArr[1].Substring(statusArr[1].LastIndexOf(":") + 1);
                }

                var partes = status.Split('\'');

                if (partes.Count() > 2)
                {
                    var campo = partes[3];
                    var valor = partes[1];
                    if (!string.IsNullOrEmpty(campo))
                        status = ObterMensagem(status, campo, valor);
                }
            }
            catch (Exception)
            {
                status = mensagem.Valor;
            }

            return status;
        }

        private static string ObterMensagem(string mensagem, string campo, string valor = null)
        {
            var nomeCampos = new Dictionary<string, string> {
                { "cListServ", "Código Item Lista Serviço" },
                { "fone", "Telefone" }
            };
            if (!string.IsNullOrWhiteSpace(campo) && nomeCampos.ContainsKey(campo)) campo = nomeCampos[campo];

            var retorno = "Erro: Campo '" + campo + "' com valor inválido";

            if (valor != null)
                retorno += " '" + valor + "'.";

            if (mensagem.Contains("MinLength"))
                retorno += ". Valor está menor que o tamanho mínimo.";

            if (mensagem.Contains("MaxLength"))
                retorno += ". Valor está maior que o tamanho mínimo.";

            return retorno;
        }

        #endregion Consulta Nota
    }
}